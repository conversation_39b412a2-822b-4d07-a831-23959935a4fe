/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef GRD_SQL_API_H
#define GRD_SQL_API_H

#include <stdint.h>
#include "grd_resultset_api.h"
#include "grd_type_export.h"
#include "grd_db_api.h"
#ifdef __cplusplus
extern "C" {
#endif

typedef struct GRD_Stmt GRD_StmtT;

/**
 * @brief prepare a query stmt
 * @param[in] db Indicates the db abstract pointer
 * @param[in] str input SQL string
 * @param[in] strLen length of the SQL string
 * @param[out] stmt return the prepared stmt
 * @param[out] unusedStr return the unused string part or NULL
 * @return int32_t Return code indicating success or failure
 */
GRD_API int32_t GRD_SqlPrepare(GRD_DB *db, const char *str, uint32_t strLen, GRD_StmtT **stmt, const char **unusedStr);

/**
 * @brief reset a query stmt
 * @param[in] stmt the prepared stmt
 * @return int32_t Return code indicating success or failure
 */
GRD_API int32_t GRD_SqlReset(GRD_StmtT *stmt);

/**
 * @brief release a query stmt
 * @param[in] stmt the prepared stmt
 * @return int32_t Return code indicating success or failure
 */
GRD_API int32_t GRD_SqlFinalize(GRD_StmtT *stmt);

/**
 * @brief bind an object value to the parameter of specified index
 * @param[in] stmt the prepared stmt
 * @param[in] idx parameter's index
 * @param[in] val value to bind
 * @param[in] len value's length, 0~2147483647 MAX_INT32
 * @param[in] freeFunc Release function, nullable.
 * @return int32_t Return code indicating success or failure
 **/
GRD_API int32_t GRD_SqlBindBlob(GRD_StmtT *stmt, uint32_t idx, const void *val, int32_t len, void (*freeFunc)(void *));

/**
 * @brief bind an string value to the parameter of specified index
 * @param[in] stmt the prepared stmt
 * @param[in] idx parameter's index
 * @param[in] val value to bind
 * @param[in] len value's length without '\0', strlen(), 0~1024*1024
 * @param[in] freeFunc Release function, nullable.
 * @return int32_t Return code indicating success or failure
 */
GRD_API int32_t GRD_SqlBindText(GRD_StmtT *stmt, uint32_t idx, const char *val, int32_t len, void (*freeFunc)(void *));

/**
 * @brief bind an float vector to the parameter of specified index
 * @param[in] stmt the prepared stmt
 * @param[in] idx parameter's index
 * @param[in] val float vector to bind
 * @param[in] dim dimension of vector, 1 ~ 1024
 * @param[in] freeFunc Release function, nullable.
 * @return int32_t Return code indicating success or failure
 **/
GRD_API int32_t GRD_SqlBindFloatVector(
    GRD_StmtT *stmt, uint32_t idx, float *val, uint32_t dim, void (*freeFunc)(void *));

/**
 * @brief bind an int32 value to the parameter of specified index
 * @param[in] stmt the prepared stmt
 * @param[in] idx parameter's index
 * @param[in] val value to bind
 * @return int32_t Return code indicating success or failure
 */
GRD_API int32_t GRD_SqlBindInt(GRD_StmtT *stmt, uint32_t idx, int32_t val);

/**
 * @brief bind an int64 value to the parameter of specified index
 * @param[in] stmt the prepared stmt
 * @param[in] idx parameter's index
 * @param[in] val value to bind
 * @return int32_t Return code indicating success or failure
 */
GRD_API int32_t GRD_SqlBindInt64(GRD_StmtT *stmt, uint32_t idx, int64_t val);

/**
 * @brief bind an null value to the parameter of specified index, i.e. remove a bound parameter
 * @param[in] stmt the prepared stmt
 * @param[in] idx parameter's index
 * @return int32_t Return code indicating success or failure
 */
GRD_API int32_t GRD_SqlBindNull(GRD_StmtT *stmt, uint32_t idx);

/**
 * @brief bind an double value to the parameter of specified index
 * @param[in] stmt the prepared stmt
 * @param[in] idx parameter's index
 * @param[in] val value to bind
 * @return int32_t Return code indicating success or failure
 */
GRD_API int32_t GRD_SqlBindDouble(GRD_StmtT *stmt, uint32_t idx, double val);

/**
 * @brief step a query stmt
 * @param[in] stmt the prepared stmt
 * @return int32_t Return code indicating success or failure
 */
GRD_API int32_t GRD_SqlStep(GRD_StmtT *stmt);

/**
 * @brief get the column num of current select stmt
 * @param[in] stmt the prepared and stepped select stmt
 * @return uint32_t column num
 */
GRD_API uint32_t GRD_SqlColumnCount(GRD_StmtT *stmt);

/**
 * @brief get the column value type of current row
 * @param[in] stmt the prepared and stepped select stmt
 * @param[in] idx the index of column
 * @return DbDataTypeE the value type
 */
GRD_API GRD_DbDataTypeE GRD_SqlColumnType(GRD_StmtT *stmt, uint32_t idx);

/**
 * @brief get the column value bytes of current row
 * @param[in] stmt the prepared and stepped select stmt
 * @param[in] idx the index of column
 * @return uint32_t the byte size of the column
 */
GRD_API uint32_t GRD_SqlColumnBytes(GRD_StmtT *stmt, uint32_t idx);

/**
 * @brief get the column name of current row
 * @param[in] stmt the prepared and stepped select stmt
 * @param[in] idx the index of column
 * @return uint32_t the byte size of the column
 */
GRD_API char *GRD_SqlColumnName(GRD_StmtT *stmt, uint32_t idx);

/**
 * @brief get the column value of current row of select stmt
 * @param[in] stmt the prepared and stepped select stmt
 * @param[in] idx the index of column
 * @return DbValueT the dynamically typed value object or NULL if value not exist
 */
GRD_API GRD_DbValueT GRD_SqlColumnValue(GRD_StmtT *stmt, uint32_t idx);

/**
 * @brief get the column value of current row of select stmt and covert it to int64
 * @param[in] stmt the prepared and stepped select stmt
 * @param[in] idx the index of column
 * @return int64_t the value converted to int64
                   or 0 if value not exist or the value cannot be converted to int
 */
GRD_API int64_t GRD_SqlColumnInt64(GRD_StmtT *stmt, uint32_t idx);

/**
 * @brief get the column value of current row of select stmt and covert it to int32
 * @param[in] stmt the prepared and stepped select stmt
 * @param[in] idx the index of column
 * @return int32_t the value converted to int32
                   or 0 if value not exist or the value cannot be converted to int
 */
GRD_API int32_t GRD_SqlColumnInt(GRD_StmtT *stmt, uint32_t idx);

/**
 * @brief get the column value of current row of select stmt and covert it to double
 * @param[in] stmt the prepared and stepped select stmt
 * @param[in] idx the index of column
 * @return double the value converted to double
                   or 0 if value not exist or the value cannot be converted to double
 */
GRD_API double GRD_SqlColumnDouble(GRD_StmtT *stmt, uint32_t idx);

/**
 * @brief get the column value of current row of select stmt and covert it to text
 * @param[in] stmt the prepared and stepped select stmt
 * @param[in] idx the index of column
 * @return char * the value converted to text or NULL if value not exist.
                  returned result's memory will be freed when calling GRD_SqlFinalize.
 */
GRD_API const char *GRD_SqlColumnText(GRD_StmtT *stmt, uint32_t idx);

/**
 * @brief get the column value of current row of select stmt and covert it to blob
 * @param[in] stmt the prepared and stepped select stmt
 * @param[in] idx the index of column
 * @return void * the value converted to text or NULL if value not exist.
                  returned result's memory will be freed when calling GRD_SqlFinalize.
 */
GRD_API const void *GRD_SqlColumnBlob(GRD_StmtT *stmt, uint32_t idx);

/**
** @brief get the column value of current row of select stmt and covert it to floatvector
** @param [in] sqlStmt the prepared and stepped select stmt
** @param [in] index the index of column
** @param [out param] dim dimension of the returned float vector. 0 if value does not exist.
** @return float * the value converted to floatvector or NULL if value does not exist.
                  returned result's memory will be freed when calling GRD_SqlFinalize.
**/
GRD_API const float *GRD_SqlColumnFloatVector(GRD_StmtT *stmt, uint32_t idx, uint32_t *dim);

/**
 * @brief Interface for Applying for Memory.
 * @param[in] size Size of the memory to be applied for
 * @return void *
 */
GRD_API void *GRD_Malloc(uint32_t size);

/**
 * @brief Interface for actively releasing memory.
 * @param[in] ptr Pointer to the memory to be released
 * @return void
 */
GRD_API void GRD_Free(void *ptr);

/**
 * @brief get config value by config type.
 * @param[in] db Indicates the db abstract pointer
 * @param[in] type Configuration items whose parameters need to be obtained
 * @return DbValueT Return
 */
GRD_API GRD_DbValueT GRD_GetConfig(GRD_DB *db, GRD_ConfigTypeE type);

/**
 * @brief set config value by config type.
 * @param[in] db Indicates the db abstract pointer
 * @param[in] type Configuration items that need to be set
 * @param[in] value Value set for the configuration item
 * @return Return code indicating success or failure
 */
GRD_API int32_t GRD_SetConfig(GRD_DB *db, GRD_ConfigTypeE type, GRD_DbValueT value);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // GRD_SQL_API_H
