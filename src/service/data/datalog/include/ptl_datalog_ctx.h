/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: ptl_datalog_ctx.h
 * Description: interface of datalog ddl context
 * Author: optimizer-team
 * Create: 2023-12-01
 */

#ifndef DTL_DATALOG_CTX_H
#define DTL_DATALOG_CTX_H

#include "gmc_errno.h"
#include "db_mem_context.h"
#include "cpl_dtl_online_compiler.h"
#include "cpl_planner.h"
#include "ee_plan_node_ddl.h"
#include "ee_subject.h"
#include "gm_udf.h"
#include "db_list.h"
#include "adpt_spinlock.h"

#ifdef __cplusplus
extern "C" {
#endif

#define PATCH_SO_NAME_KEY_WORD_MAX_LEN (10)

/*
 * ByteCode for parse so
 */
typedef struct {
    uint8_t *byteCodeBuf;
    uint32_t bufLen;
} ByteCodeT;

/*
 * @brief init ByteCode
 */
inline static void ByteCodeInit(ByteCodeT *byteC)
{
    *byteC = (ByteCodeT){};
    byteC->byteCodeBuf = NULL;
    byteC->bufLen = 0;
}

/*
 * @brief uninit ByteCode
 */
inline static void ByteCodeUnInit(ByteCodeT *byteC)
{
    ByteCodeInit(byteC);
}

/*
 * LoadCtxT for load datalog so
 */
typedef struct LoadCtx {
    DbMemCtxT *memCtx;
    DbMemCtxT *escapeMemCtx;     // escape memory for rollback
    DtlImportParamT *loadParam;  // load datalog parameters
    ByteCodeT tableByteCode;     // table byte code
    ByteCodeT planByteCode;      // plan byte code
    ByteCodeT udfByteCode;       // udf byte code
    ByteCodeT resPoolByteCode;   // respool byte code
    void *programInfo;           // type is DatalogProgramInfoT
    uint32_t soId;
    uint32_t namespaceId;             // the namespace where datalog program is to be loaded/unloaded
    LibFuncT getByteCode;             // function to get bytecode of serialized infos, plans, tables, udfs, respools
    void *handle;                     // handle of get bytecode of serialized infos function
    VersionFuncT getCompilerVersion;  // function to get gmprecompiler version
    VersionFuncT getDtlVersion;       // function to get datalog version
    VersionFuncT getUpgDtlVersion;    // function to get upgrade datalog version
    HashFuncT getLowDtlHash;          // function to get low datalog hash(generated by SHA256)
    HashFuncT getHighDtlHash;         // function to get high datalog hash(generated by SHA256)
    HashFuncT getPatchDtlHash;        // function to get patch datalog hash(generated by SHA256)
    DtlInitT initFunc;                // init function
    DtlUninitT uninitFunc;            // uninit function
    DbListT *tableList;               // table list
    DbListT *udfList;                 // udf list , including aggregate cmp udf
    DbListT *resPoolList;             // respool list
    uint32_t preInvokeUdfId;          // pre_invoke func udf id
    uint32_t postInvokeUdfId;         // post_invoke func udf id
    uint32_t transFailUdfId;          // trans fail function
    bool normalUdfHasAccessKv : 1;    // 非init/uninit的udf是否带access_kv选项
    DbListT *planList;                // plan list<PlannedStmtT>
} LoadCtxT;

/*
 * @brief init LoadContext
 */
Status LoadContextInit(LoadCtxT *loadCtx);

/*
 * @brief uninit LoadContext
 */
void LoadContextUninit(LoadCtxT *loadCtx);

/*
 * udf / respool / table executed context
 */
typedef struct ObjExecuted {
    bool executed;              // true if some obj executed
    int32_t lastExecutedIndex;  // last success executed index
} ObjExecutedT;

/*
 * @brief init object executed , default not executed
 */
inline static void ObjExecutedInit(ObjExecutedT *executed)
{
    executed->executed = false;
    executed->lastExecutedIndex = -1;
}

/*
 * @brief set index to obj executed
 */
inline static void ObjExecutedSetIndex(ObjExecutedT *executed, int32_t index)
{
    executed->lastExecutedIndex = index;
    executed->executed = (executed->lastExecutedIndex >= 0);
}

/*
 * @brief return object executed or not
 */
inline static bool ObjExecutedDone(ObjExecutedT *executed)
{
    return executed->executed;
}

/*
 * @brief return object executed's last executed index
 */
inline static int32_t ObjExecutedLastIndex(ObjExecutedT *executed)
{
    return executed->lastExecutedIndex;
}

/*
 * @brief reset obj executed to unexecuted
 */
inline static void ObjExecutedReset(ObjExecutedT *executed)
{
    ObjExecutedInit(executed);
}

/*
 * rollback load datalog ctx
 */
typedef struct RollbackLoadCtx {
    bool handlerLoaded;           // so handler loaded or not
    bool soIdInited;              // soId increased or not
    ObjExecutedT udfCreated;      // udf created ctx
    bool inited;                  // dtl init func invoked or not
    ObjExecutedT resPoolCreated;  // respool created ctx
    ObjExecutedT tableCreated;    // table created ctx
    bool programInfoSaved;        // programInfo saved
    bool planSaved;               // plan saved or not
    bool lockMgrCreated;          // lockMgr created or not
} RollbackLoadCtxT;

/*
 * @brief init rollback ctx , default not done
 */
inline static void RollbackLoadCtxInit(RollbackLoadCtxT *ctx)
{
    *ctx = (RollbackLoadCtxT){};
    ObjExecutedInit(&ctx->udfCreated);
    ObjExecutedInit(&ctx->resPoolCreated);
    ObjExecutedInit(&ctx->tableCreated);
}

/*
 * UnloadCtxT for unload datalog so
 */
typedef struct {
    DbMemCtxT *memCtx;
    uint32_t soId;
    uint32_t nspId;       // the so to be unloaded is in this namespace
    DbListT udfList;      // udfs to be unloaded
    DbListT respoolList;  // respools to be unloaded
    DbListT tableList;    // tables to be unloaded
    bool hasKvAccess;
    Status lastErrorCode;            // the last error occurred in ClearDatalogResource
    DtlUnImportParamT *unloadParam;  // unload datalog parameters
} UnloadCtxT;

/*
 * @brief init unloadCtx as default values
 */
Status UnloadCtxInit(UnloadCtxT *unloadCtx);

/*
 * @brief uninit unloadCtx
 */
void UnloadCtxUninit(UnloadCtxT *unloadCtx);

/*
 * UpgradeCtxT for upgrade/degrade context
 */
typedef struct UpgradeCtx {
    LoadCtxT baseCtx;
    ByteCodeT alterTableByteCode;     // alter table byte code
    ByteCodeT dropTableByteCode;      // drop table byte code
    ByteCodeT redoTableByteCode;      // redo table byte code
    ByteCodeT alterPlanByteCode;      // alter plan byte code
    ByteCodeT dropPlanByteCode;       // drop plan byte code
    ByteCodeT alterUdfByteCode;       // alter udf byte code
    ByteCodeT dropUdfByteCode;        // drop udf byte code
    ByteCodeT topoSortTblByteCode;    // topo sorted table byte code
    ByteCodeT redoOutputTblByteCode;  // no heap tables need redo during upgrade byte code
    SubjectT *subject;
    DbMemCtxT *backgroundMemCtx;  // 后台重做线程使用的memCtx，由后台重做线程执行完毕后一把释放
    DbListT *alterTableList;      // alter table list [DmVertexLabelT*]
    DbListT *dropTableList;       // drop table list [char*]
    DbListT *redoTableList;  // 要升级的规则，及其通过中间表串联起来的规则，涉及的所有输入输出表名称
    DbListT *redoTableIdList;  // 要升级的规则，及其通过中间表串联起来的规则，涉及的所有输入输出表Id
    DbListT *alterUdfList;        // alter udf list [DmUdfBaseT *]
    DbListT *dropUdfList;         // drop udf list [char*]
    DbListT *alterPlanList;       // alter plan list<IRPlanT>
    DbListT *dropPlanList;        // drop plan list<IRPlanT>
    DbListT *newTopoSortTblList;  // new topo table list<char *>, 升级后so中所有表名，经过topo排序(升级)
    DbListT *oldTopoSortTblList;  // old topo table list<uint32_t>, 升级前so中所有表的labelId，经过topo排序(回滚)
    DbListT *redoOutputTblList;   // no heap tables need redo during upgrade process<char *>
    DbListT triggerLabelIdList;   // 要重做的规则的触发表
    DbListT triggerRuleNameList;  // 要重做的规则名称
    uint32_t hungTimeUs;          // 一级hung时间
    bool isNeedRedo;              // only upgrade patch with device reboot set false
    bool isRedoOff;               // true: should not start redo thread
    char soNameKeyWord[PATCH_SO_NAME_KEY_WORD_MAX_LEN];
} UpgradeCtxT;

/*
 * @brief init Upgrade/Degrade Context
 */
Status UpgradeContextInit(UpgradeCtxT *upgCtx);

/*
 * @brief uninit Upgrade/Degrade Context
 */
void UpgradeContextUninit(UpgradeCtxT *upgCtx);

/*
 * @brief Get SoPath In Upgrade/Degrade Context
 */
inline static char *UpgradeCtxGetSoPath(UpgradeCtxT *upgCtx)
{
    DB_POINTER(upgCtx);
    return upgCtx->baseCtx.loadParam != NULL ? upgCtx->baseCtx.loadParam->soPath.str : (char *)"nonHotPatchLoad";
}

/*
 * Hold vertexLabel->commonInfo->datalogLabelInfo->upgradeInfo ,
 * reside in RollbackUpgradeCtxT.backupUpgradeVersionCache
 */
typedef struct BackupUpgradeVersion {
    uint32_t vertexLabelId;      // key of RollbackUpgradeCtxT.backupUpgradeVersionCache ,
                                 // value is BackupUpgradeVersionT itself
    int32_t prevUpgradeVersion;  // copy of datalogLabelInfo.prevUpgradeVersion
    int32_t upgradeVersion;      // copy of datalogLabelInfo.upgradeVersion
} BackupUpgradeVersionT;

/*
 * rollback upgrade/degrade datalog ctx
 */
typedef struct RollbackUpgradeCtx {
    RollbackLoadCtxT baseCtx;
    bool backgroundMemCtxCreated;
    bool soHandlerAppended;
    /*
     * hold programInfo.preVersion and currentVersion for rollback
     */
    char preVersion[DB_MAX_PATH];
    char currentVersion[DB_MAX_PATH];
    bool versionChanged;

    /*
     * hold programInfo.hash for rollback
     */
    char hash[DTL_SHA256_LENGTH + 1];
    bool hashChanged;
    /*
     * hold programInfo.redoMode for rollback
     */
    LibFuncT getByteCode;
    bool byteCodeFuncSaved;
    bool oldBlockMode;
    bool flagChanged;
    ObjExecutedT tableAltered;
    ObjExecutedT udfAltered;
    bool planCachePartialCopied;
    bool planDropped;
    bool planSaved;
    bool planAltered;
    bool topoSortIdAltered;
    bool planCacheSwitched;
    bool upgradeVersionChanged;
    /*
     * <VertexLabelId , BackupUpgradeVersionT>
     */
    DbOamapT *backupUpgradeVersionCache;
    bool subjectCreated;
    bool subjectSaved;
    bool lockMgrUpdated;
} RollbackUpgradeCtxT;

/*
 * @brief init rollback upgrade/degrade ctx , default not done
 */
inline static void RollbackUpgradeCtxInit(RollbackUpgradeCtxT *ctx)
{
    *ctx = (RollbackUpgradeCtxT){};
    RollbackLoadCtxInit(&ctx->baseCtx);
    ObjExecutedInit(&ctx->tableAltered);
    ObjExecutedInit(&ctx->udfAltered);
    ctx->backupUpgradeVersionCache = NULL;
}

/*
 * @brief uninit rollback upgrade/degrade ctx , default not done
 */
inline static void RollbackUpgradeCtxUninit(RollbackUpgradeCtxT *ctx)
{
    RollbackUpgradeCtxInit(ctx);
}

#ifdef __cplusplus
}
#endif

#endif  // DTL_DATALOG_CTX_H
