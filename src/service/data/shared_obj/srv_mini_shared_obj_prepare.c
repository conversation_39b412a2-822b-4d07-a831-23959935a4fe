/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: embedded mini shared obj prepare
 * Author: wangxiangdong
 * Create: 2024-03-06
 */

#include "srv_mini_shared_obj_prepare.h"
#include "ee_mini_shared_obj_context.h"
#include "adpt_define.h"
#include "db_rpc_msg_op.h"
#include "db_instance.h"
#include "de_instance.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SHARED_OBJ_PRE_HANDLER_CNT (sizeof(g_gmdbSharedObjPrepareHandler) / sizeof(SharedObjPrepareHandlerT))

static SharedObjPrepareHandlerT g_gmdbSharedObjPrepareHandler[] = {
    {MSG_OP_RPC_CREATE_SHARED_OBJ, MINI_QRY_TYPE_CREATE_SHARED_OBJ, EmbeddedParseCreateSharedObj, WRITE_OPT},
    {MSG_OP_RPC_DROP_SHARED_OBJ, MINI_QRY_TYPE_DROP_SHARED_OBJ, EmbeddedParseDropSharedObj, WRITE_OPT},
    {MSG_OP_RPC_SEQUENCE_INSERT, MINI_QRY_TYPE_SEQUENCE_INSERT, EmbeddedParseSequenceInsert, WRITE_OPT},
    {MSG_OP_RPC_SEQUENCE_ASSIGN_ATTRIBUTES, MINI_QRY_TYPE_SEQUENCE_ASSIGN_ATTRIBUTES,
        EmbeddedParseSequenceAssignAttributes, WRITE_OPT},
    {MSG_OP_RPC_SEQUENCE_DELETE, MINI_QRY_TYPE_SEQUENCE_DELETE, EmbeddedParseSequenceDelete, WRITE_OPT},
    {MSG_OP_RPC_SEQUENCE_EMBED_INSERT, MINI_QRY_TYPE_SEQUENCE_EMBED_INSERT, EmbeddedParseSequenceEmbedInsert,
        WRITE_OPT},
    {MSG_OP_RPC_SEQUENCE_READ, MINI_QRY_TYPE_SEQUENCE_READ, EmbeddedParseSequenceRead, WRITE_OPT},
    {MSG_OP_RPC_SEQUENCE_READ_DELTA_FORMAT, MINI_QRY_TYPE_SEQUENCE_READ_DELTA_FORMAT,
        EmbeddedParseSequenceReadDeltaFormat, WRITE_OPT},
    {MSG_OP_RPC_SEQUENCE_CREATE_SNAP, MINI_QRY_TYPE_SEQUENCE_CREATE_SNAP, EmbeddedParseSequenceGetSnap, WRITE_OPT},
    {MSG_OP_RPC_SHARED_OBJ_OP_HISTORY_ENABLE, MINI_QRY_TYPE_SHARED_OBJ_OP_HISTORY_ENABLE, EmbeddedParseSharedObjHistory,
        WRITE_OPT},
    {MSG_OP_RPC_SHARED_OBJ_OP_HISTORY_DISABLE, MINI_QRY_TYPE_SHARED_OBJ_OP_HISTORY_DISABLE,
        EmbeddedParseSharedObjHistory, WRITE_OPT},
    {MSG_OP_RPC_SHARED_OBJ_OP_HISTORY_UNDO, MINI_QRY_TYPE_SHARED_OBJ_OP_HISTORY_UNDO, EmbeddedParseSharedObjHistory,
        WRITE_OPT},
    {MSG_OP_RPC_SHARED_OBJ_OP_HISTORY_REDO, MINI_QRY_TYPE_SHARED_OBJ_OP_HISTORY_REDO, EmbeddedParseSharedObjHistory,
        WRITE_OPT},
    {MSG_OP_RPC_EVENT_REGISTER, MINI_QRY_TYPE_EVENT_REGISTER, MiniParseEventRegister, WRITE_OPT},
    {MSG_OP_RPC_DOC_GET_SHARED_OBJ_BY_TYPE, MINI_QRY_TYPE_DOC_GET_SHARED_OBJ_BY_TYPE, MiniParseTypeGet, WRITE_OPT},
    {MSG_OP_RPC_MAP_SET, MINI_QRY_TYPE_MAP_SET, MiniParseMapSet, WRITE_OPT},
    {MSG_OP_RPC_MAP_DELETE, MINI_QRY_TYPE_MAP_DELETE, MiniParseMapDelete, WRITE_OPT},
    {MSG_OP_RPC_MAP_READ, MINI_QRY_TYPE_MAP_READ, MiniParseMapRead, WRITE_OPT},
    {MSG_OP_RPC_XML_ELEMENT_SET_ATTR, MINI_QRY_TYPE_XML_ELEMENT_SET_ATTR, MiniParseXmlElementSetAttr, WRITE_OPT},
    {MSG_OP_RPC_XML_ELEMENT_REMOVE_ATTR, MINI_QRY_TYPE_XML_ELEMENT_REMOVE_ATTR, MiniParseXmlElementRemoveAttr,
        WRITE_OPT},
    {MSG_OP_RPC_XML_ELEMENT_GET_ATTRS, MINI_QRY_TYPE_XML_ELEMENT_GET_ATTRS, MiniParseXmlElementGetAttrs, WRITE_OPT},
    {MSG_OP_RPC_XML_FRAGMENT_INSERT, MINI_QRY_TYPE_XML_NODE_INSERT, MiniParseXmlNodeInsert, WRITE_OPT},
    {MSG_OP_RPC_XML_FRAGMENT_DELETE, MINI_QRY_TYPE_XML_NODE_DELETE, MiniParseXmlNodeDelete, WRITE_OPT},
    {MSG_OP_RPC_XML_FRAGMENT_GET, MINI_QRY_TYPE_XML_FRAGMENT_GET, MiniParseXmlFragmentGet, READ_OPT},
    {MSG_OP_RPC_XML_FRAGMENT_TO_STRING, MINI_QRY_TYPE_XML_FRAGMENT_TO_STRING, MiniParseXmlFragmentToString, READ_OPT},
    {MSG_OP_RPC_XML_TEXT_INSERT, MINI_QRY_TYPE_XML_TEXT_INSERT, MiniParseXmlTextInsert, WRITE_OPT},
    {MSG_OP_RPC_XML_TEXT_DELETE, MINI_QRY_TYPE_XML_TEXT_DELETE, MiniParseXmlTextDelete, WRITE_OPT},
    {MSG_OP_RPC_XML_TEXT_READ, MINI_QRY_TYPE_XML_TEXT_READ, MiniParseXmlTextRead, READ_OPT},
    {MSG_OP_RPC_XML_TEXT_ASSIGN_ATTR, MINI_QRY_TYPE_XML_TEXT_ASSIGN_ATTR, MiniParseXmlTextAssignAttribute, WRITE_OPT},
};

static SharedObjPrepareHandlerT *SharedObjGetPrepareHandler(MsgOpcodeRpcE opCode)
{
    for (uint8_t i = 0; i < SHARED_OBJ_PRE_HANDLER_CNT; ++i) {
        if (g_gmdbSharedObjPrepareHandler[i].opCode == opCode) {
            return &g_gmdbSharedObjPrepareHandler[i];
        }
    }
    return NULL;
}

Status SharedObjPrepare(MiniRunCtxT *runCtx, MsgOpcodeRpcE opCode)
{
    Status ret = GMERR_OK;
    SharedObjPrepareHandlerT *handle = SharedObjGetPrepareHandler(opCode);
    if (handle == NULL) {
        return GMERR_INTERNAL_ERROR;
    }

    DbInstanceT *dbIns = (DbInstanceT *)runCtx->session->sessionPool->dbInstance;
    uint8_t permission = dbIns->sharedModeInfo.sharedReadOnly ? READ_OPT : WRITE_OPT;
    if (permission < handle->permission) {
        DB_LOG_ERROR(GMERR_PERMISSION_DENIED, "gme shared obj permission denied");
        return GMERR_PERMISSION_DENIED;
    }

    runCtx->type = (MiniQryTypeE)handle->qryType;

    if (handle->parse != NULL) {
        ret = handle->parse(runCtx);
    }
    if (opCode > MSG_OP_RPC_SHARED_OBJ_BEGIN && opCode < MSG_OP_RPC_SHARED_OBJ_END) {
        PrepareDeInstance(runCtx);
    }
    return ret;
}

#ifdef __cplusplus
}
#endif
