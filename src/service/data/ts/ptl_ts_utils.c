/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: utils for ts service
 * Author: lihainuo
 * Create: 2024-01-09
 */

#include "srv_data_base_proc.h"
#include "dm_data_ts.h"
#include "se_space.h"
#include "ee_background_schedule.h"
#include "ee_plan.h"
#include "ee_time_partition_util.h"
#include "ee_session_interface.h"
#include "ee_plan_state.h"
#include "ptl_ts_utils.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

Status PtlTsInitSessionResource(DrtProcCtxT *procCtx, SessionT *session)
{
    DB_POINTER2(procCtx, session);
    session->procCtx = procCtx;
    FixBufferT *request = &procCtx->msg;
    MsgHeaderT *msgHeader = procCtx->msgHeader;
    QrySessionSetReq(session, request);
    session->reqStartTime = msgHeader->reqStartTime;
    session->reqTimeOut = msgHeader->reqTimeOut;
    session->cltTimeoutMs = QrySessionGetServerTimeoutWithInterval(session);

    SrvDataInitResponse(session, session->rsp, msgHeader->flags);
    if (procCtx->msgHeader == NULL) {
        (void)DbAtomicInc64(&procCtx->conn->connMgr->connProbeData.invalidMsgNum);
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "No msg header when init TS session resource.");  // LCOV_EXCL_LINE
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    Status ret = QryGetStmtById(session, procCtx->msgHeader->stmtId, &session->currentStmt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get stmt %" PRIu16 " when init TS session resource.", procCtx->msgHeader->stmtId);
        return ret;
    }

    QrySetStmtExecInfo(session->currentStmt, msgHeader->flags);

    return GMERR_OK;
}

Status PtlTsVerifyRequest(DrtProcCtxT *procCtx, SessionT *session, uint32_t opCode)
{
    DB_POINTER2(procCtx, session);
    if (session->req == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Request.");  // LCOV_EXCL_LINE
        return GMERR_DATA_EXCEPTION;
    }

    if (FixBufGetPos(session->req) < MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "request format.");  // LCOV_EXCL_LINE
        return GMERR_DATA_EXCEPTION;
    }
    MsgHeaderT *msgHeader = procCtx->msgHeader;
    // verify the received msg if it is ddl , dcl , dml service, has right flag, opNum and opCode
    if ((msgHeader->serviceId != (uint16_t)DRT_SERVICE_STMT) || (msgHeader->flags != CS_FLAG_NONE) ||
        (msgHeader->opNum != 1) || (opCode < MSG_OP_RPC_TS_BEGIN || opCode > MSG_OP_RPC_TS_CEIL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
            "service id is %" PRIu16 ", flags is %" PRIu32 ", op num is %" PRIu16 ", op code is %" PRIu32,
            msgHeader->serviceId, msgHeader->flags, msgHeader->opNum, opCode);
        return GMERR_DATA_EXCEPTION;
    }

    return GMERR_OK;
}

Status StartTransaction(SessionT *session)
{
    DB_POINTER2(session, session->seInstance);
    TrxCfgT cfg = (TrxCfgT){
        .readOnly = false,
        .isLiteTrx = false,
        .isBackGround = false,
        .isInteractive = false,
        .connId = DB_INVALID_UINT16,
        .trxType = PESSIMISTIC_TRX,
        .isolationLevel = READ_COMMITTED,
    };
    return SeTransBegin(session->seInstance, &cfg);
}

void RollBackTransaction(SessionT *session)
{
    DB_POINTER2(session, session->seInstance);
    if (session->isInteractiveTrx) {
        return;
    }
    Status ret = SeTransRollback(session->seInstance, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "rollback transaction");
    }
    // there is no trxMemCtx in session
}

Status CommitTransaction(SessionT *session)
{
    DB_POINTER2(session, session->seInstance);
    Status ret = SeTransCommit(session->seInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "commit transaction after execute ts");
    }
    // there is no trxMemCtx in session
    return ret;
}

void ReleaseLabelRLatchFromList(DbListT *latchList)
{
    uint32_t latchNum = DbListGetItemCnt(latchList);
    LabelRWLatchT *labelLatch = NULL;
    for (uint32_t i = latchNum; i > 0; i--) {
        labelLatch = *(LabelRWLatchT **)DbListItem(latchList, i - 1);
        LabelRLatchRelease(labelLatch);
    }
}

Status AcquireLabelRLatch(DmVertexLabelT *vertexLabel, LabelRWLatchT **latch)
{
    Status ret = GMERR_OK;
    LabelRWLatchT *labelLatch = (LabelRWLatchT *)GetLabelRWLatchPtrById(
        vertexLabel->commonInfo->vertexLabelLatchId, DbGetInstanceByMemCtx(vertexLabel->memCtx));
    if (labelLatch == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_UNEXPECTED_NULL_VALUE, "get latchLatch, table name: %s", vertexLabel->metaCommon.metaName);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t hungTimeUs = 0;
    if ((ret = DbGetHungTime(WORKER_HUNG_LEVEL_ONE, &hungTimeUs)) != GMERR_OK) {
        return ret;
    }
    // use latch protect dql concurrency with ddl drop
    if (!LabelRLatchTimedAcquire(labelLatch, hungTimeUs)) {
        ret = GMERR_LOCK_NOT_AVAILABLE;
        DB_LOG_ERROR(ret, "get latch read lock timeout");  // LCOV_EXCL_LINE
        return ret;
    }
    // 校验表锁的有效性，防止并发下表被删除
    ret = LabelLatchCheckVersion(labelLatch, vertexLabel->commonInfo->vertexLabelLatchVersionId);
    if (ret != GMERR_OK) {
        LabelRLatchRelease(labelLatch);
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "table: %s already dropped", vertexLabel->metaCommon.metaName);  // LCOV_EXCL_LINE
        return ret;
    }
    *latch = labelLatch;
    return ret;
}

/* be aware to avoid deadlock. current label latch situation:
 * 1. Select/CopyTo. One label read latch;
 * 2. Insert_into. Two label read latch;
 * 3. Drop Table. One label write latch;
 * 4. TTL. One label write latch at a time;
 * In no case shall two write latches be acquired at a time. No deadlock possible.
 */
Status AcquireLabelRLatchFromList(DbListT *labelList, DbListT *latchList)
{
    uint32_t labelNum = DbListGetItemCnt(labelList);
    DmVertexLabelT *label = NULL;
    LabelRWLatchT *labelLatch = NULL;
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < labelNum; i++) {
        label = *(DmVertexLabelT **)DbListItem(labelList, i);
        ret = AcquireLabelRLatch(label, &labelLatch);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "acquire logic table read latch before query.");  // LCOV_EXCL_LINE
            goto ERROR;
        }
        ret = DbAppendListItem(latchList, &labelLatch);
        if (ret != GMERR_OK) {
            LabelRLatchRelease(labelLatch);
            goto ERROR;
        }
    }
    return ret;
ERROR:
    ReleaseLabelRLatchFromList(latchList);
    return ret;
}

void ReleaseLabelFromList(DbListT *labelList)
{
    uint32_t labelNum = DbListGetItemCnt(labelList);
    DmVertexLabelT *label = NULL;
    for (uint32_t i = labelNum; i > 0; i--) {
        label = *(DmVertexLabelT **)DbListItem(labelList, i - 1);
        (void)CataReleaseVertexLabel(label);
        label = NULL;
    }
}

static Status TsBgAddNotifyForDiskClean(SessionT *session, DmVertexLabelT *vertexLabel, Status result)
{
    bool isOverLimit = false;
    Status ret = TsCheckLabelOverLimit(session, vertexLabel, &isOverLimit);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "check label over limit or not.");  // LCOV_EXCL_LINE
        return (result == GMERR_OK) ? ret : result;           // executor err is prior to this
    } else if (!isOverLimit) {
        return (result == GMERR_OK) ? ret : result;
    }

    DB_LOG_INFO("label %d, %s exceed limit.", vertexLabel->metaCommon.metaId, vertexLabel->metaCommon.metaName);
    ret = TsLcmBgCleanDiskAddNotify(vertexLabel->metaCommon.metaId, 0);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "add notify to ts disk clean background.");  // LCOV_EXCL_LINE
        return (result == GMERR_OK) ? ret : result;
    }
    return (result == GMERR_OK) ? ret : result;
}

Status TsBgAddNotifyByType(SessionT *session, DmVertexLabelT *logicalLabel, NodeTagT tagType, Status result)
{
    Status ret = GMERR_OK;
    uint32_t labelId = logicalLabel->metaCommon.metaId;
    DmVertexLabelT *tmpLabel = NULL;
    switch (tagType) {
        case T_DROP_TABLE_STMT:
            // if drop fails, or it's not logical table dropped, don't add notification.
            if (result != GMERR_OK || logicalLabel->metaVertexLabel->vertexLabelType != VERTEX_TYPE_TS_LOGICAL) {
                return result;
            }
            return TsLcmBgDropTblAddNotify(labelId, logicalLabel);
        case T_ALTER_TABLE_STMT:
        case T_TIME_PARTITION_INSERT:
            // get latest label anyway
            ret = CataGetVertexLabelById(DbGetInstanceByMemCtx(session->memCtx), labelId, &tmpLabel);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {  // unlikely to happen after alter success
                DB_LOG_ERROR(ret, "get latest label when adding notify, labelId: %" PRIu32, labelId);  // LCOV_EXCL_LINE
                return (result == GMERR_OK) ? ret : result;
            }
            ret = TsBgAddNotifyForDiskClean(session, tmpLabel, result);
            if (ret != GMERR_OK) {
                (void)CataReleaseVertexLabel(tmpLabel);
                return (result == GMERR_OK) ? ret : result;
            }
            break;
        default:
            // 前置逻辑保证这里不会出现其他类型
            return (result == GMERR_OK) ? GMERR_INTERNAL_ERROR : result;
    }
    if (tmpLabel != NULL) {  // release latest label got in this func
        (void)CataReleaseVertexLabel(tmpLabel);
    }
    return (result == GMERR_OK) ? ret : result;
}

Status TsExecAnalyze(FusionModelQryStmtT *qryStmt)
{
    if (qryStmt->planTree == NULL) {
        return GMERR_OK;  // ddl、dcl不打印执行计划
    }
    StringBuilderT sb = {0};
    sb.memCtx = qryStmt->memCtx;
    Status ret = ExecAnalyze(qryStmt->memCtx, qryStmt->planState, &sb);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unable to get ts analyze result.");
        return ret;
    }

    EEResultT result = {0};
    result.sqlType = ANALYZE_SQL;
    result.explainResult = DbDynMemCtxAlloc(qryStmt->memCtx, sizeof(DmValueT));
    if (result.explainResult == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "unable to alloc memory for analyze result.");
        return GMERR_OUT_OF_MEMORY;
    }
    memset_s(result.explainResult, sizeof(DmValueT), 0, sizeof(DmValueT));

    result.explainResult->type = DB_DATATYPE_STRING;
    result.explainResult->value.strAddr = DmSbDump(&sb);
    if (result.explainResult->value.strAddr == NULL) {
        DbDynMemCtxFree(qryStmt->memCtx, result.explainResult);
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "unable to alloc memory for analyze result.");
        return GMERR_OUT_OF_MEMORY;
    }
    result.explainResult->value.length = DM_STR_LEN((char *)result.explainResult->value.strAddr);

    // 执行receive， ReceiveSerTable
    DestReceiverT *recv = qryStmt->destReceiver;
    ret = recv->receive(recv, &result, NULL);
    DbDynMemCtxFree(sb.memCtx, result.explainResult->value.strAddr);
    DmSbReset(&sb);
    DbDynMemCtxFree(qryStmt->memCtx, result.explainResult);
    qryStmt->eof = true;
    qryStmt->execStatus = EXEC_DONE;
    return ret;
}

Status TsExecPlan(FusionModelQryStmtT *qryStmt)
{
    DB_POINTER(qryStmt);
    Status ret = ExecutorStart(qryStmt);
    if (ret == GMERR_OK) {
        ret = ExecutorRun(qryStmt);
    }
    if (qryStmt->result.sqlType == ANALYZE_SQL) {
        ret = TsExecAnalyze(qryStmt);
    }
    ExecutorFinish(qryStmt);
    return ret;
}

Status ExecutePlanWithTrx(TsQryStmtT *stmt)
{
    Status ret = StartTransaction(stmt->base.session);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = TsExecPlan((FusionModelQryStmtT *)stmt);
    if (ret != GMERR_OK) {
        RollBackTransaction(stmt->base.session);
    } else {
        ret = CommitTransaction(stmt->base.session);
    }
    return ret;
}

Status ExecuteMemInsert(TsQryStmtT *stmt)
{
    DB_POINTER(stmt);
    return ExecutePlanWithTrx(stmt);
}

Status TsExecExplain(FusionModelQryStmtT *qryStmt)
{
    if (qryStmt->planTree == NULL) {
        return GMERR_OK;  // ddl、dcl不打印执行计划
    }
    StringBuilderT sb = {0};
    sb.memCtx = qryStmt->memCtx;
    Status ret = ExecExplain(qryStmt->memCtx, qryStmt->planTree, &sb, 0);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get ts explain result.");  // LCOV_EXCL_LINE
        return ret;
    }

    EEResultT result = {0};
    result.sqlType = EXPLAIN_SQL;
    result.explainResult = DbDynMemCtxAlloc(qryStmt->memCtx, sizeof(DmValueT));
    if (result.explainResult == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc for explain result.");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    memset_s(result.explainResult, sizeof(DmValueT), 0, sizeof(DmValueT));

    result.explainResult->type = DB_DATATYPE_STRING;
    result.explainResult->value.strAddr = DmSbDump(&sb);
    result.explainResult->value.length = DM_STR_LEN((char *)result.explainResult->value.strAddr);

    // 执行receive， ReceiveSerTable
    DestReceiverT *recv = qryStmt->destReceiver;
    ret = recv->receive(recv, &result, NULL);
    DbDynMemCtxFree(sb.memCtx, result.explainResult->value.strAddr);
    DmSbReset(&sb);
    DbDynMemCtxFree(qryStmt->memCtx, result.explainResult);
    qryStmt->eof = true;
    qryStmt->execStatus = EXEC_DONE;
    return ret;
}

Status RemoveTempFileLargeResult(DrtConnectionT *conn)
{
    uint64_t id = (conn->id == 0) ? DbThreadGetSelfId() : conn->id;
    char filepath[PATH_MAX];
    Status ret = GetTempFileDirForLargeResult(filepath);
    if (ret != GMERR_OK) {
        return ret;
    }
    char idString[PATH_MAX] = "";
    sprintf_s(idString, sizeof(idString), "%" PRIu64, id);
    if (strcat_s(filepath, PATH_MAX, idString) != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "construct file path when entering ts service.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (!DbFileExist(filepath)) {
        return GMERR_OK;
    }
    (void)DbRemoveFile(filepath);  // 不应该影响正常的service入口流程
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
