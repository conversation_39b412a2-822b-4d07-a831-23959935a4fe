/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 * Description:
 * Author:
 * Create:
 */
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
#include "db_config.h"
#include "drt_worker_manager.h"
#include "ee_merge_write_cache.h"
#include "srv_data_fastpath.h"

#ifdef __cplusplus
extern "C" {
#endif

void *MergeAllWriteCache(void *ctx, uint16_t workerId)
{
    WorkerMgrT *workerMgr = (WorkerMgrT *)ctx;
    DbCfgValueT interval;
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    Status ret = DbCfgGet(cfgHandle, DB_CFG_MERGE_WRITE_CACHE_INTERVAL, &interval);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get config DB_CFG_MERGE_WRITE_CACHE_INTERVAL.");
        return NULL;
    }
    uint32_t sleepTime = (uint32_t)interval.int32Val * 1000;  // 必须先转换成uint32_t再计算，否则可能溢出
    DB_ASSERT(sleepTime != 0);
    SessionT *session = NULL;
    ret = QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, NULL, &session);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "alloc session.");
        return NULL;
    }
    QrySessionInitSubsOverLoad(session);
    while (!DrtWorkerIsStopping(workerMgr, workerId)) {
        DrtWorkerUpdateStat(workerMgr, workerId, true);
        bool isDone = QryMergeAllWriteCache(session, FastPathProcessCommand);
        if (isDone) {
            DrtWorkerUpdateStat(workerMgr, workerId, false);
            DbUsleep(sleepTime);
        }
        QryClearInvalidCache(session);
        QryClearInvalidSubsCache(session);
    }
    if (session != NULL) {
        QrySessionRelease(session);
    }
    return NULL;
}

Status DbStartWriteCacheMergeThread(void)
{
    DrtInstanceT *drtInstance = DrtGetInstance(NULL);
    if (drtInstance == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "get instance.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    WorkerParaT workerPara = {.name = "Merge_WC",
        .type = DRT_WORKER_BGTASK,
        .entry = MergeAllWriteCache,
        .ctx = (void *)&drtInstance->workerMgr,
        .isBindCpu = 1,
        .cpuNO = 1,
        .priority = WORKER_PRIORITY_HIGH,
        .nameWithId = false,
        .scheduleTime = WRITE_CACHE_THREAD_SCHEDULE_TIME * SCHEDULE_TIMES};
    Status ret = DrtStartNewWorker(&drtInstance->workerMgr, (void *)(&workerPara), NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "start write cache merge thread.");
        return ret;
    }
    return ret;
}

Status DbWriteCacheInitImpl(void)
{
    uint32_t maxWriteCacheSize = (uint32_t)DbCfgGetInt32Lite(DB_CFG_MAX_WRITE_CACHE_SIZE, NULL);
    if (maxWriteCacheSize != 0) {
        Status ret = QryInitWriteCacheMem(maxWriteCacheSize);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DbStartWriteCacheMergeThread();
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
#endif
