/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Implementations for interface of PathHistoryTaskQueue
 * Author: GQL Team
 * Create: 2023-08-27
 */
#include "srv_path_history_task_queue.h"

#include "db_sysapp_context.h"
#include "drt_instance.h"
#include "dm_meta_subscription.h"
#include "ee_gql_search.h"
#include "cpl_gql_ast.h"
#include "srv_sr_map.h"
#include "srv_pvc_diff.h"
#include "srv_path_search_task.h"
#include "srv_path_trigger_action.h"
#include "srv_path_smooth.h"
#include "srv_path_recon.h"
#include "srv_data_gql_path_sub.h"

#define ACTION_LIST_ACTION_LIST 20
#define PATH_TASK_CNT_MAX (uint32_t)4

void SetHistoryQueueWorkerUnscheduled(PathHistoryQueueT *queue)
{
    DbSpinLock(&queue->lock);
    DB_ASSERT(queue->historyQueueWorkerScheduled);
    queue->historyQueueWorkerScheduled = false;
    DbSpinUnlock(&queue->lock);
}

void RescheduleHistoryTask(PathHistoryQueueT *historyQueue)
{
    // schedule a history task if the history queue is not empty (the current summary in flight has just finished)
    DbSpinLock(&historyQueue->lock);
    DB_ASSERT(historyQueue->historyQueueWorkerScheduled);
    if (!DbQueueIsEmpty(&historyQueue->summaryTasks)) {
        DrtWorkerScheduleTask(historyQueue->workerMgr, historyQueue->workerTaskId, TASK_PRIORITY_NORMAL, false);
    } else {
        historyQueue->historyQueueWorkerScheduled = false;
    }
    DbSpinUnlock(&historyQueue->lock);
}

PathSearchPreAllocMemT *GetPreAllocMem(PathHistoryQueueT *queue)
{
    DbSpinLock(&queue->preAllocMemMgr.lock);
    for (uint32_t i = 0; i < queue->preAllocMemMgr.preAllocMemCnt; i++) {
        if (!queue->preAllocMemMgr.preAllocMems[i].isUsed) {
            queue->preAllocMemMgr.preAllocMems[i].isUsed = true;
            DbSpinUnlock(&queue->preAllocMemMgr.lock);
            return queue->preAllocMemMgr.preAllocMems + i;
        }
    }
    DbSpinUnlock(&queue->preAllocMemMgr.lock);
    return NULL;
}

void ReleasePreAllocMem(PathHistoryQueueT *queue, uint32_t id)
{
    DbSpinLock(&queue->preAllocMemMgr.lock);
    PathSearchPreAllocMemMgrT *mgr = &queue->preAllocMemMgr;
    for (uint32_t i = 0; i < mgr->preAllocMemCnt; i++) {
        if (mgr->preAllocMems[i].id == id) {
            mgr->preAllocMems[i].isUsed = false;
            break;
        }
    }
    DbSpinUnlock(&queue->preAllocMemMgr.lock);
}

static void HistoryQueueProcessEntry(void *ctx, void *data)
{
    DB_UNUSED(data);
    Status ret;
    PathHistoryQueueT *queue = ctx;
    // pop summary from history queue
    PathSummaryT *summary = PathHistoryQueuePop(queue);
    DB_POINTER(summary);  // this should be always ok: we don't want to execute a history task and find an empty queue

    // set executing flag of task
    DrtWorkerPoolPendTask(queue->workerMgr, queue->workerTaskId);

    /* check summary type */
    bool canFreeSummary = false;
    switch (summary->summaryType) {
        case NORTH: {
            ret = ProcessNorthSummary(summary, queue);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_ERROR(ret, "Unable to process a north summary.");
            }
            canFreeSummary = false;
            goto EXIT;
        }
        case SOUTH_SMOOTH: {
            ret = ProcessSouthSmoothSummary(summary, queue, &canFreeSummary);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_ERROR(ret, "Unable to process a south smooth summary.");
            }
            goto EXIT;
        }
        case SOUTH_RECON: {
            ret = ProcessSouthReconSummary(summary, queue);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_ERROR(ret, "Unable to process a south reconciliation summary.");
            }
            canFreeSummary = false;
            goto EXIT;
        }
        case INIT_LOAD: {
            ret = ProcessInitLoadSummary(summary, queue);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_ERROR(ret, "Unable to process a south reconciliation summary.");
            }
            canFreeSummary = false;
            goto EXIT;
        }
        default: {
            DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Can't recocnize summary type.");
            goto EXIT;
        }
    }
EXIT:
    if (canFreeSummary) {
        DestroyPathSummary(summary->memCtx);
    }
}

#define LIST_INIT_SIZE 5
#define PRE_ALLOC_BUF_NUM 2

typedef struct {
    uint32_t srcSlotIdx;
    uint32_t edgeLabelId;
    AASlotT *slot;
    TextT *variableName;
} PathSearchHelperT;  // 参考PushEdgeElementT

#define PRE_ALLOC_SIZE 128

Status PathPreparePreAllocMemForSearch(PathHistoryQueueT *queue, uint32_t workerNumMax)
{
    queue->preAllocMemMgr.preAllocMems = DbDynMemCtxAlloc(queue->memCtx, sizeof(PathSearchPreAllocMemT) * workerNumMax);
    if (queue->preAllocMemMgr.preAllocMems == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc pre mems when init PathSearchTaskQueue.");
        return GMERR_OUT_OF_MEMORY;
    }
    DbSpinInit(&queue->preAllocMemMgr.lock);
    uint32_t listCnt = ((GQL_MAX_BATCH_OP_NUM - 1) / workerNumMax + 1) * (uint32_t)workerNumMax;
    uint32_t pathSearchTasksAllocSize = (uint32_t)(sizeof(PathSearchTaskNewT) * workerNumMax);
    uint32_t preAllocSingleBufferAllocSize = sizeof(PreAllocBufferT);
    uint32_t preAllocBufferAllocSize = preAllocSingleBufferAllocSize * workerNumMax;
    uint32_t preAllocBufferInnerBufAllocSize = PRE_ALLOC_SIZE * DB_KIBI * workerNumMax * PRE_ALLOC_BUF_NUM;
    uint32_t allocSize = (uint32_t)(pathSearchTasksAllocSize + preAllocBufferAllocSize +
                                    preAllocBufferInnerBufAllocSize + sizeof(DbListT) * listCnt);
    for (uint32_t i = 0; i < workerNumMax; i++) {
        void *preAllocMem = (void *)DbDynMemCtxAlloc(queue->memCtx, allocSize);
        if (preAllocMem == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc pre mem when init PathSearchTaskQueue.");
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s(preAllocMem, allocSize, 0, allocSize);
        queue->preAllocMemMgr.preAllocMems[i].isUsed = false;
        queue->preAllocMemMgr.preAllocMems[i].id = i;
        // path service uninit时销毁
        queue->preAllocMemMgr.preAllocMems[i].preAllocForPathSearchTask = preAllocMem;
        queue->preAllocMemMgr.preAllocMems[i].preAllocForAASlot = preAllocMem + pathSearchTasksAllocSize;
        for (uint32_t j = 0; j < workerNumMax; j++) {
            // 在一个buffer上预分配PRE_ALLOC_BUF_NUM个内存给aa->preBuffer用
            // buffersize也是PRE_ALLOC_SIZE * DB_KIBI的PRE_ALLOC_BUF_NUM倍
            queue->preAllocMemMgr.preAllocMems[i].preAllocForAASlot[j].buffer =
                preAllocMem + (pathSearchTasksAllocSize + preAllocBufferAllocSize + j * preAllocSingleBufferAllocSize);
            queue->preAllocMemMgr.preAllocMems[i].preAllocForAASlot[j].bufferSize = preAllocSingleBufferAllocSize;
            queue->preAllocMemMgr.preAllocMems[i].preAllocForAASlot[j].bufferCursor =
                queue->preAllocMemMgr.preAllocMems[i].preAllocForAASlot[j].buffer;
        }
        queue->preAllocMemMgr.preAllocMems[i].preAllocForList =
            (DbListT *)(preAllocMem + pathSearchTasksAllocSize + preAllocBufferAllocSize +
                        preAllocBufferInnerBufAllocSize);
        for (uint32_t j = 0; j < listCnt; j++) {
            DbCreateListWithExtendSize(&(queue->preAllocMemMgr.preAllocMems[i].preAllocForList[j]),
                sizeof(PathSearchHelperT), LIST_INIT_SIZE, queue->memCtx);
            Status ret = DbListReserve(&(queue->preAllocMemMgr.preAllocMems[i].preAllocForList[j]), LIST_INIT_SIZE);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
        }
    }
    queue->preAllocMemMgr.preAllocListCnt = listCnt / workerNumMax;
    queue->preAllocMemMgr.preAllocMemCnt = workerNumMax;
    return GMERR_OK;
}

Status PathHistoryInitActionList(PathHistoryQueueT *queue)
{
    // path service uninit时销毁
    queue->actionLists = DbDynMemCtxAlloc(queue->memCtx, sizeof(DbListT));
    DbCreateList(queue->actionLists, sizeof(DbListT *), queue->memCtx);
    Status ret = DbListReserve(queue->actionLists, PATH_TASK_CNT_MAX);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to pre-alloc items for actionList init PathSearchTaskQueue.");
        return ret;
    }
    for (uint32_t i = 0; i < PATH_TASK_CNT_MAX; i++) {
        DbListT *tmpList = DbNewListItem(queue->actionLists);
        DbCreateListWithExtendSize(tmpList, sizeof(uint32_t), ACTION_LIST_ACTION_LIST, queue->memCtx);
        ret = DbListReserve(tmpList, ACTION_LIST_ACTION_LIST);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status InitPathHistoryMgr(DbMemCtxT *memCtx, DrtInstanceT *instance, PathHistoryQueueT *queue)
{
    DbSpinInit(&queue->lock);
    queue->workerMgr = &instance->workerMgr;
    queue->memCtx = memCtx;
    Status ret = DbQueueCreate(memCtx, &queue->summaryTasks, sizeof(PathSummaryT *));
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = PathPreparePreAllocMemForSearch(queue, PATH_TASK_CNT_MAX);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbQueueDestroy(&queue->summaryTasks);
        return ret;
    }
    queue->pathSearchTaskCnt = PATH_TASK_CNT_MAX;
    ret = PathHistoryInitActionList(queue);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbQueueDestroy(&queue->summaryTasks);
        return ret;
    }
    uint16_t workerTaskId =
        DrtWorkerCreateTask(&instance->workerMgr, queue, &queue->summaryTasks, HistoryQueueProcessEntry);
    if (workerTaskId == DB_INVALID_ID16) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_MEMORY_OPERATE_FAILED, "Unable to create worker task for PathSearchTaskQueue.");
        DbQueueDestroy(&queue->summaryTasks);
        return ret;
    }
    queue->workerTaskId = workerTaskId;
    queue->historyQueueWorkerScheduled = false;
    return ret;
}

Status PathHistoryMgrInit(PathHistoryQueueT **queue)
{
    DB_POINTER(queue);
    DbMemCtxT *appDynCtx = DbSrvGetAppDynCtx(DbGetProcGlobalId());
    if (appDynCtx == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Unable to get app dyn memctx.");
        return GMERR_INTERNAL_ERROR;
    }
    DrtInstanceT *instance = DrtGetInstance(NULL);
    if (instance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "cannot get instance when init PathHistoryQueue.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (instance->scheMgr.scheMode != SCHEDULE_THREAD_POOL) {
        // Currently, pathHistoryTaskQueue cannot be created in non-thread pool mode.
        return GMERR_OK;
    }
    // memCtx用途：path history memory
    // 生命周期：长进程级别
    // 释放方式：兜底清空
    // 兜底清空措施：1.下方初始化流程失败时销毁；2.pathService销毁时销毁
    DbMemCtxArgsT args = {};
    args.dynCtxNeedRecycle = true;
    args.collectAllocSizeOnThisTree = true;
    DbMemCtxT *pathHistoryMemCtx = DbCreateDynMemCtx(appDynCtx, true, "PATH_HISTORY", &args);
    if (pathHistoryMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to create dynamic memctx when init path service.");
        return GMERR_OUT_OF_MEMORY;
    }
    // 内存释放点:本函数异常分支/PathHistoryMgrUnInit；生命周期:长进程级别
    PathHistoryQueueT *tmpQueue = DbDynMemCtxAlloc(pathHistoryMemCtx, sizeof(PathHistoryQueueT));
    if (tmpQueue == NULL) {
        DbDeleteDynMemCtx(pathHistoryMemCtx);
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc when init PathSearchTaskQueue.");
        return GMERR_OUT_OF_MEMORY;
    }
    Status ret = InitPathHistoryMgr(pathHistoryMemCtx, instance, tmpQueue);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto RELEASE_QUEUE;
    }
    *queue = tmpQueue;
    return GMERR_OK;
RELEASE_QUEUE:
    DbDynMemCtxFree(pathHistoryMemCtx, tmpQueue);
    *queue = NULL;
    DbDeleteDynMemCtx(pathHistoryMemCtx);
    return ret;
}

void PathHistoryMgrUnInit(PathHistoryQueueT *queue)
{
    DB_POINTER(queue);
    if (queue->workerTaskId == DB_INVALID_ID16) {
        return;
    }
    DrtInstanceT *instance = DrtGetInstance(NULL);
    if (instance == NULL) {
        return;
    }
    DbQueueDestroy(&queue->summaryTasks);
    DrtWorkerDestroyTask(&instance->workerMgr, queue->workerTaskId);
    queue->workerTaskId = DB_INVALID_ID16;
    DbDeleteDynMemCtx(queue->memCtx);
    queue->memCtx = NULL;
}

PathSummaryT *PathHistoryQueuePop(PathHistoryQueueT *queue)
{
    if (queue == NULL) {
        return NULL;
    }
    PathSummaryT *summary = NULL;
    DbSpinLock(&queue->lock);
    DB_ASSERT(queue->historyQueueWorkerScheduled);
    Status ret = DbQueuePop(&queue->summaryTasks, &summary);
    DbSpinUnlock(&queue->lock);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return NULL;
    }
    return summary;
}

Status PathHistoryQueuePush(PathHistoryQueueT *queue, PathSummaryT *summary)
{
    DB_POINTER(summary);
    if (queue == NULL) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED,
            "Unable to push the history task into history queue, please check whether the schedule is 2");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    DbSpinLock(&queue->lock);
    Status ret = DbQueuePush(&queue->summaryTasks, &summary);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbSpinUnlock(&queue->lock);
        DB_LOG_ERROR(ret, "Unable to push the history task into history queue");
        return ret;
    }
    // If there is a summary being executed, do not schedule another history task
    if (queue->historyQueueWorkerScheduled == false) {
        // The queue is trivially non-empty because we have just pushed a summary
        DB_ASSERT((queue->summaryTasks.rear - queue->summaryTasks.front) > 0);
        queue->historyQueueWorkerScheduled = true;
        DrtWorkerScheduleTask(queue->workerMgr, queue->workerTaskId, TASK_PRIORITY_NORMAL, false);
    }
    DbSpinUnlock(&queue->lock);
    return ret;
}
