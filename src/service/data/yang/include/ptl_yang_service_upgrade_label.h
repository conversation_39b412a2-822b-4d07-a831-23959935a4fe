/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: header of yang service for upgrade labels
 * Author: zengzebin
 * Create: 2025-02-24
 */

#ifndef PTL_YANG_SERVICE_UPGRADE_LABEL_H
#define PTL_YANG_SERVICE_UPGRADE_LABEL_H

#include "db_mem_context.h"
#include "db_hashmap.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

Status RecoverUpgradeLabelsFuncImpl(DbInstanceHdT dbInstance, DbOamapT *metaDiffMap);

Status RecoverUpdateLeafListLabelsFuncImpl(DbOamapT *metaDiffMap);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* PTL_YANG_SERVICE_UPGRADE_LABEL_H */
