/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: stream data service
 * Author: Stream Team
 * Create: 2024-09-05
 */

#ifndef SRV_DATA_STREAM_H
#define SRV_DATA_STREAM_H

#include "ee_stmt_fusion.h"
#include "ee_session_interface.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

SO_EXPORT Status StreamServiceInit(DbInstanceHdT dbInstance);

SO_EXPORT void StreamServiceUnInit(DbInstanceHdT dbInstance);

typedef Status (*ForwardHook)(SessionT *session, DmVertexLabelT *streamVertexLabel, AAT *aa);

typedef struct TagStreamSrvAmT {
    ForwardHook forwardHook;
} StreamSrvAmT;

SO_EXPORT_FOR_TS void StreamSrvAmInit(StreamSrvAmT *am);
SO_EXPORT Status StreamForwardNextByAA(SessionT *session, DmVertexLabelT *streamVertexLabel, AAT *aa);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // SRV_DATA_STREAM_H
