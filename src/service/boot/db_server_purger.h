/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author:
 * Create: 2024-04-16
 */
#ifndef DB_SERVER_PURGER_H
#define DB_SERVER_PURGER_H

#include "db_instance.h"
#include "se_instance.h"
#include "se_index.h"
#include "se_fixed_heap.h"
#include "db_instance.h"
#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define PURGER_DEFAULT_SPILT_TIME 1000  // 1秒，1级挂死最小为3秒，同时参考其余后台任务分片时间设置为1s
#ifdef FEATURE_SIMPLEREL
#define PURGER_DEFAULT_SLEEP_US 5000  // 5 ms
#define PURGER_SLEEP_US 1000          // 如果有资源回收过，则间隔1 ms触发下一次资源回收。
#else
#define PURGER_DEFAULT_SLEEP_US 500000  // 500 ms
#define PURGER_SLEEP_US 100000          // 如果有资源回收过，则间隔100 ms触发下一次资源回收。
#endif
Status PurgerGetLabelInfo(SeRunCtxHdT seRunCtx, LabelInfoT *labelInfo);

Status PurgerReleaseLabelInfo(LabelInfoT *labelInfo);

Status PurgerGetHeapHandle(
    uint32_t labelId, DbMemCtxT *memCtx, SeRunCtxHdT seRunCtx, DmLabelTypeE labelType, HpRunHdlT *heapRunCtx);

Status PurgerGetFixedHeapHandle(
    uint32_t labelId, DbMemCtxT *memCtx, SeRunCtxHdT seRunCtx, FixedHeapRunHdlT *edgeTopoCtxOut);

Status PurgerReleaseLabelAndLatch(DmLabelTypeE labelType, void *label);

Status PurgerSetIdxKeyCmpCallback(SeRunCtxHdT seRunCtx, uint32_t labelId, IndexOpenCfgT *idxOpenCfg);

#ifdef __cplusplus
}
#endif  /* __cplusplus */
#endif  // DB_SERVER_PURGER_H
