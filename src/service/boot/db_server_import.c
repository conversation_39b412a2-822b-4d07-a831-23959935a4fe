/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: db_server_import.c
 * Description: Import rules and objects when DbStart
 * Create: 2023-5-22
 */

#include <dirent.h>
#include <regex.h>
#include "adpt_string.h"
#include "db_utils.h"
#include "db_sysapp_context.h"
#include "db_json.h"
#include "cpl_public_parser_ddl_utils.h"
#include "cpl_public_parser_ddl.h"
#include "cpl_public_parser_dcl.h"
#include "cpl_public_verifier_ddl.h"
#include "cpl_public_verifier_dcl.h"
#include "cpl_base_pub.h"
#include "ee_ddl_desc.h"
#include "ee_dcl_desc.h"
#include "ee_ddl_user.h"
#include "ee_dcl_priv.h"
#include "ee_ddl_vertex_label.h"
#include "ee_ddl_kv.h"
#include "ee_cmd_namespace.h"
#include "ee_init.h"
#include "ee_dcl_ctrl.h"
#include "dm_meta_role.h"
#include "dm_meta_key_oper.h"
#include "dm_data_prop_json.h"
#include "srv_data_fastpath.h"
#include "db_secure_msg_buffer.h"
#include "srv_log.h"
#include "ptl_service_utils.h"
#ifdef FEATURE_DATALOG
#include "ptl_datalog_service.h"
#include "ptl_datalog_service_common.h"
#include "cpl_dtl_online_compiler.h"
#include "ptl_datalog_hot_patch_upgrader.h"
#endif
#include "adpt_startup_time.h"
#include "db_dyn_load.h"
#include "srv_data_ts.h"
#include "srv_log.h"
#include "db_server_import.h"

#define MAX_FOLDER_LAYER_LIMIT 10
#define FIRST_FOLDER_LAYER 1
#define IMPORT_VERTEX_NUM 1
#define CFG_SCHEMA_PATH_NUM 4
#define CFG_SCHEMA_PATH_NUM_FOR_SHMEM 2
#define CFG_OBJ_SCHEMA_PATH_INDEX 2u
#define CFG_OBJ_POLICY_PATH_INDEX 3u
#define CFG_SCHEMA_DATA_PATH_NUM 2
#define WAIT_DATA_TIME (1000 * 100)  // 每次等待100ms

#ifdef FEATURE_DATALOG
#define CFG_DATALOG_SO_PATH_NUM 2
#define DATALOG_PATH_LEN_TWO 2
#endif

typedef enum {
    IMPORT_ALLOW_LIST = 0,
    IMPORT_SYS_PRIVIS,
    IMPORT_OBJECTS,
    IMPORT_OBJ_PRIVIS,

    IMPORT_BASE_DATA,
    IMPORT_TAG_DATA,
#ifdef FEATURE_DATALOG
    IMPORT_DATALOG_SO,
    IMPORT_DATALOG_PATCH,
#endif
} DbServerImportStageE;

typedef struct {
    DbServerImportStageE stag;
    uint32_t successNum;
    uint32_t unsuccessNum;
} LoaderInfoT;

typedef struct ImportMethod {
    Status (*import)(char *filePath, DbMemCtxT *memCtx);
} ImportMethodT;

SrvFpExportFuncsT g_gmdbSrvFpExportFuncs = {NULL};

void SetSrvFpExportFuncMap(const SrvFpExportFuncsT *exportFuncs)
{
    g_gmdbSrvFpExportFuncs = *exportFuncs;
}

SrvFpExportFuncsT *GetSrvFpExportFuncMap(void)
{
    return &g_gmdbSrvFpExportFuncs;
}

static inline Status FastPathProcessCommandFunc(QryStmtT *stmt, uint32_t opCode, LongOperationInfoT *longInfo)
{
    SrvFpExportFuncsT *exportFuncs = GetSrvFpExportFuncMap();
    if (exportFuncs->fastPathProcessCommandFunc == NULL) {
        DB_LOG_WARN(GMERR_FEATURE_NOT_SUPPORTED, "FastPath service not registered, found NULL");  // LCOV_EXCL_LINE
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    return exportFuncs->fastPathProcessCommandFunc(stmt, opCode, longInfo);
}

Status DbWriteCacheInit(void)
{
    SrvFpExportFuncsT *exportFuncs = GetSrvFpExportFuncMap();
    if (exportFuncs->dbWriteCacheInitFunc == NULL) {
        DB_LOG_WARN(
            GMERR_FEATURE_NOT_SUPPORTED, "FastPath write cache service not registered, found NULL");  // LCOV_EXCL_LINE
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    return exportFuncs->dbWriteCacheInitFunc();
}

typedef struct {
    void *key;          // key的指针
    uint32_t keyLen;    // key的长度
    void *value;        // value的指针
    uint32_t valueLen;  // value的长度
} KvTupleT;

typedef struct {
    DmVertexLabelT *vertexLabel;
    DmVertexT *vertex;
    DbJsonT *json;      // 存多条vertex信息的json结构
    uint32_t totalNum;  // vertex 的数量
} VertexDataInfoT;

static bool IsFileNameValid(const char *prefix)
{
    DB_POINTER(prefix);
    const char *cursor = prefix;
    if (!IsAlpha(*cursor)) {
        return false;
    }
    while (*(++cursor) != '\0') {
        bool ret = IsAlpha(*cursor) || IsNumChar(*cursor) || (*cursor == '_');
        if (!ret) {
            return false;
        }
    }
    return true;
}

static bool IsPolicyRuleStag(DbServerImportStageE stag)
{
    return stag == IMPORT_ALLOW_LIST || stag == IMPORT_SYS_PRIVIS || stag == IMPORT_OBJ_PRIVIS;
}

Status CheckFileNameValidity(const char *filePath, DbServerImportStageE stag)
{
    if (!IsPolicyRuleStag(stag)) {
        return GMERR_OK;  // 非白名单及权限相关的文件，不对文件名限制
    }
    char tempPath[DB_MAX_FILE_PATH_LEN] = {0};
    errno_t err = strcpy_s(tempPath, sizeof(tempPath), filePath);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW,
            "strcpy_s unsucc, too long filePath length: %zu, limited length: %" PRIu32 ".", strlen(filePath),
            (uint32_t)(DB_MAX_FILE_PATH_LEN - 1));  // LCOV_EXCL_LINE
        return GMERR_FIELD_OVERFLOW;
    }
    const char *prefix = DbGetFileNameInfo(tempPath, DB_MAX_FILE_PATH_LEN, DB_FILENAME_PREFIX);
    if (prefix == NULL) {
        DB_LOG_ERROR(GMERR_FILE_OPERATE_FAILED, "Get fileName prefix from %s unsucc.", tempPath);  // LCOV_EXCL_LINE
        return GMERR_FILE_OPERATE_FAILED;
    }
    // 文件名首字母必须是字母开头，并且除首字母外的其他字符只支持大小写字母、数字、_等字符
    if (!IsFileNameValid(prefix)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_NAME,
            "novalid file name: %s, File name must start with a letter and contain only upper or lower case "
            "letters and \"_\" ",
            prefix);  // LCOV_EXCL_LINE
        return GMERR_INVALID_NAME;
    }
    return GMERR_OK;
}

static void DbServerImportSingleFile(char *filePath, DbMemCtxT *memCtx, LoaderInfoT *loaderInfo)
{
#ifndef NDEBUG
    uint64_t beginTime = DbRdtsc();
#endif
    Status ret = CheckFileNameValidity(filePath, loaderInfo->stag);
    if (ret != GMERR_OK) {
        return;
    }
#ifdef FEATURE_DATALOG
    ImportMethodT serverImportMethods[CFG_SCHEMA_PATH_NUM + CFG_SCHEMA_DATA_PATH_NUM + CFG_DATALOG_SO_PATH_NUM] = {
        [IMPORT_ALLOW_LIST] = {DbServerImportAllowList},
        [IMPORT_SYS_PRIVIS] = {DbServerImportSysPri},
        [IMPORT_OBJECTS] = {DbServerImportObjects},
        [IMPORT_OBJ_PRIVIS] = {DbServerImportObjPri},
        [IMPORT_BASE_DATA] = {DbServerImportData},
        [IMPORT_TAG_DATA] = {DbServerImportData},
        [IMPORT_DATALOG_SO] = {DbServerImportDatalogData},
        [IMPORT_DATALOG_PATCH] = {DbServerImportDatalogPatchData},
    };
#else
    ImportMethodT serverImportMethods[CFG_SCHEMA_PATH_NUM + CFG_SCHEMA_DATA_PATH_NUM] = {
        [IMPORT_ALLOW_LIST] = {DbServerImportAllowList},
        [IMPORT_SYS_PRIVIS] = {DbServerImportSysPri},
        [IMPORT_OBJECTS] = {DbServerImportObjects},
        [IMPORT_OBJ_PRIVIS] = {DbServerImportObjPri},
        [IMPORT_BASE_DATA] = {DbServerImportData},
        [IMPORT_TAG_DATA] = {DbServerImportData},
    };
#endif
    ret = serverImportMethods[(uint32_t)loaderInfo->stag].import(filePath, memCtx);
    if (ret == GMERR_OK) {
        loaderInfo->successNum++;
    } else if (ret != GMERR_NO_DATA) {
        loaderInfo->unsuccessNum++;
        DB_LOG_ERROR(ret, "unsucc to import file: %s when DB Start", filePath);
    }
#ifndef NDEBUG
    uint64_t endTime = DbRdtsc();
    DB_BOOT_LOG_WARN(
        GMERR_OK, "INFO: import file: %s, delay %" PRIu64 "ms.", filePath, DbToMseconds(endTime - beginTime));
#endif
}

static void DbServerImportMutipleFile(char *filePath, DbMemCtxT *memCtx, LoaderInfoT *loaderInfo, uint32_t folderLayer)
{
    if (folderLayer > FIRST_FOLDER_LAYER + MAX_FOLDER_LAYER_LIMIT) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DIRECTORY_OPERATE_FAILED, "The directory is too deep to load, path: %s.",
            filePath);  // LCOV_EXCL_LINE
        return;
    }
    if (DbFileExist(filePath)) {
        DbServerImportSingleFile(filePath, memCtx, loaderInfo);
        return;
    }
    DbDIRT *dir = NULL;
    Status ret = DbOpenDir(filePath, &dir);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DIRECTORY_OPERATE_FAILED, "Open directory: %s unsucc.", filePath);
        return;
    }

    char subDirPath[DB_MAX_FILE_PATH_LEN] = {0};
    if (sprintf_s(subDirPath, DB_MAX_FILE_PATH_LEN, "%s/", filePath) < 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "cpy subDirPath unsucc.");  // LCOV_EXCL_LINE
        DbCloseDir(dir);
        return;
    }

    const uint32_t len = (uint32_t)strlen(subDirPath);
    while ((ret = DbReadDir(dir, subDirPath + len, DB_MAX_FILE_PATH_LEN - len)) == GMERR_OK) {
        if (strcmp(subDirPath + len, ".") == 0 || strcmp(subDirPath + len, "..") == 0) {
            continue;
        }
        DbServerImportMutipleFile(subDirPath, memCtx, loaderInfo, folderLayer + 1);
    }
    if (ret != GMERR_NO_DATA) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Read file path unsucc, dirPath = %s.", filePath);  // LCOV_EXCL_LINE
    }
    DbCloseDir(dir);
}

Status DbServerImportAllowList(char *filePath, DbMemCtxT *memCtx)
{
    if (!QryCheckFileSuffix(filePath, "gmuser")) {
        return GMERR_NO_DATA;  // 忽略，不处理
    }

    QryCreateUserAndRoleDescT desc = {0};
    Status ret = QryParseAllowListForDbStart(filePath, &desc, memCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unsucc to parse allow list when DbStart.");
        return ret;
    }
    ret = QryVerifyAllowListForDbStart(&desc);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unsucc to verify allow list when DbStart.");
        return ret;
    }
    ret = QryExecuteAllowListForDbStart(DbGetInstanceByMemCtx(memCtx), &desc);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unsucc to execute allow list when DbStart.");
    }
    return ret;
}

Status DbServerImportSysPri(char *filePath, DbMemCtxT *memCtx)
{
    if (!QryCheckFileSuffix(filePath, "gmpolicy")) {
        return GMERR_NO_DATA;  // 忽略，不处理
    }
    // MemCtx:统一释放
    DbListT *sysPriCombins = DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
    if (sysPriCombins == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Alloc memctx for sysPriCombins unsucc");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateList(sysPriCombins, sizeof(QryGrantOrRevokeSysPrivsDescT), memCtx);

    Status ret = QryParseGrantSysPriForDbStart(filePath, sysPriCombins, memCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unsucc to parse sysPri when DbStart.");  // LCOV_EXCL_LINE
        goto RELEASE;
    }

    uint32_t cnt = DbListGetItemCnt(sysPriCombins);
    for (uint32_t i = 0; i < cnt; i++) {
        QryGrantOrRevokeSysPrivsDescT *desc = DbListItem(sysPriCombins, i);
        ret = QryVerifyGrantSysPrivsDesc(desc);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "unsucc to verify sys priv when DbStart.");  // LCOV_EXCL_LINE
            break;
        }
        ret = QryExecuteGrantSysPrivsForDbStart(DbGetInstanceByMemCtx(memCtx), desc);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "unsucc to execute grant sys priv when DbStart.");  // LCOV_EXCL_LINE
            break;
        }
    }
RELEASE:
    DbDestroyList(sysPriCombins);
    return ret;
}

static Status ExecuteCreateNamespaceForDbStart(QryStmtT *stmt)
{
    Status ret = QryExecuteBeginTrans(stmt);  // 适配持久化系统表，开启事务
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Cannot begin transaction when create vertexLabel for DbStart.");  // LCOV_EXCL_LINE
        return ret;
    }

    ret = QryExecuteCreateNamespace(stmt);
    if (ret == GMERR_OK) {
        ret = QryExecuteCommitTrans(stmt);
    } else {
        (void)QryExecuteRollbackTrans(stmt);
    }
    return ret;
}

Status DbServerImportNamespace(char *filePath, DbMemCtxT *memCtx)
{
    // MemCtx:统一释放
    DbListT *createNspDescList = DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
    if (createNspDescList == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_OUT_OF_MEMORY, "Alloc memctx for createNspDescList unsucc");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateList(createNspDescList, sizeof(QryCreateNamespaceDescT), memCtx);

    SessionT *session = NULL;
    QryStmtT *stmt = NULL;
    Status ret =
        QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, DbGetInstanceByMemCtx(memCtx), &session);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to alloc session when import namespaces for DbStart.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = InitStmtForSchemaLoader(session, &stmt);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }
    session->currentStmt = stmt;

    ret = QryParseCreateNspForDbStart(filePath, createNspDescList, memCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unsucc to parse namespace to create when DbStart.");  // LCOV_EXCL_LINE
        goto RELEASE;
    }

    uint32_t cnt = DbListGetItemCnt(createNspDescList);
    for (uint32_t i = 0; i < cnt; i++) {
        QryCreateNamespaceDescT *desc = DbListItem(createNspDescList, i);
        ret = QryVerifyCreateNamespaceDesc(desc);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "unsucc to verify namespace to create when DbStart.");  // LCOV_EXCL_LINE
            break;
        }
        stmt->context->entry = (void *)desc;
        ret = ExecuteCreateNamespaceForDbStart(stmt);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "unsucc to execute create namespace when DbStart.");  // LCOV_EXCL_LINE
            break;
        }
    }
RELEASE:
    QrySessionRelease(session);
    DbDestroyList(createNspDescList);
    return ret;
}

Status DbServerImportObjects(char *filePath, DbMemCtxT *memCtx)
{
    char tempPath[PATH_MAX] = {0};
    errno_t err = strcpy_s(tempPath, sizeof(tempPath), filePath);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_FIELD_OVERFLOW, "Strcpy_s filePath: %s unsucc.", filePath);  // LCOV_EXCL_LINE
        return GMERR_FIELD_OVERFLOW;
    }
    char *suffix = DbGetFileNameInfo(tempPath, sizeof(tempPath), DB_FILENAME_SUFFIX);
    if (suffix == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_UNEXPECTED_NULL_VALUE, "Get file suffix unsucc, filePath: %s.", filePath);  // LCOV_EXCL_LINE
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (strcmp(suffix, "gmnsp") == 0) {
        return DbServerImportNamespace(filePath, memCtx);
    } else if (strcmp(suffix, "gmjson") == 0) {
        uint32_t publicNspId = 0;
        DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(memCtx);
        Status ret = CataGetNamespaceIdByName(dbInstance, PUBLIC_NAMESPACE_NAME, &publicNspId);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to get public namespace id.");  // LCOV_EXCL_LINE
            return ret;
        }
        DbImportTableParaT impTablePara = {
            .filePath = filePath, .labelJsonStr = NULL, .configJsonStr = NULL, .labelName = NULL};
        return DbServerImportTables(&impTablePara, memCtx, publicNspId);
    } else if (strcmp(suffix, "gmsql") == 0) {
        return ServerImportTsTables(filePath, memCtx);
    } else {
        return GMERR_NO_DATA;  // 忽略，不处理
    }
}

Status DbServerImportObjPri(char *filePath, DbMemCtxT *memCtx)
{
    if (!QryCheckFileSuffix(filePath, "gmpolicy")) {
        return GMERR_NO_DATA;  // 忽略，不处理
    }
    // MemCtx:统一释放
    DbListT *objPriCombins = DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
    if (objPriCombins == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Alloc memctx for objPriCombins unsucc");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateList(objPriCombins, sizeof(QryGrantOrRevokeObjPrivsDescT), memCtx);
    uint32_t publicNspId = 0;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(memCtx);
    Status ret = CataGetNamespaceIdByName(dbInstance, "public", &publicNspId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unsucc to get pubilc nspId when DbStart.");  // LCOV_EXCL_LINE
        goto RELEASE;
    }
    ret = QryParseGrantObjPriForDbStart(filePath, NULL, objPriCombins, memCtx, publicNspId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unsucc to parse objPriv when DbStart.");  // LCOV_EXCL_LINE
        goto RELEASE;
    }

    uint32_t cnt = DbListGetItemCnt(objPriCombins);
    for (uint32_t i = 0; i < cnt; i++) {
        QryGrantOrRevokeObjPrivsDescT *desc = DbListItem(objPriCombins, i);
        ret = QryVerifyGrantObjPrivsDesc(desc);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "unsucc to verify objPriv when DbStart.");  // LCOV_EXCL_LINE
            break;
        }
        ret = QryExecuteGrantObjPrivsForDbStart(desc, DbGetInstanceByMemCtx(memCtx));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "unsucc to execute grant objPriv when DbStart.");  // LCOV_EXCL_LINE
            break;
        }
    }
RELEASE:
    DbDestroyList(objPriCombins);
    return ret;
}

static Status FormatFilePath(char *filePath, uint32_t maxFilePathSize)
{
    DB_POINTER(filePath);
    if (strcmp(filePath, "/") == 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DIRECTORY_OPERATE_FAILED, "Schema path is not supported");  // LCOV_EXCL_LINE
        return GMERR_DIRECTORY_OPERATE_FAILED;
    }
    char realFilePath[PATH_MAX] = {0};
    Status ret = DbAdptRealPath(filePath, realFilePath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get realpath from %s unsucc", filePath);
        return ret;
    }
    if (DbAdptAccess(realFilePath, R_OK) != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FILE_OPERATE_FAILED, "Can not access file: %s", filePath);  // LCOV_EXCL_LINE
        return GMERR_FILE_OPERATE_FAILED;
    }
    (void)memset_s(filePath, maxFilePathSize, 0, maxFilePathSize);
    if (strcpy_s(filePath, maxFilePathSize, realFilePath) != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Strcpy_s unsucc, the absolute path length is %" PRId32,
            (int32_t)strlen(realFilePath));  // LCOV_EXCL_LINE
        return GMERR_FIELD_OVERFLOW;
    }
    if (filePath[strlen(realFilePath) - 1] == '/') {  // 去掉文件路径结尾的"/"
        filePath[strlen(realFilePath) - 1] = 0;
    }
    return GMERR_OK;
}

static Status GetPathList(char *filePathStr, char (*filePathList)[DB_MAX_FILE_PATH_LEN], uint32_t num)
{
    // 分别解析各个资源的路径，多个路径以';'隔开
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < num; i++) {
        char *tailStr = NULL;
        char *path = strtok_s(filePathStr, ";", &tailStr);
        uint32_t pathLen = path == NULL ? 0 : strlen(path);
        // 按需建表场景可以不配置policy文件路径
        if (i == CFG_OBJ_POLICY_PATH_INDEX && pathLen == 0) {
            continue;
        }
        if (pathLen == 0 || pathLen >= DB_MAX_FILE_PATH_LEN) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_CONFIG_ERROR, "Unable to get schema path conf, i = %" PRIu32 ".", i);  // LCOV_EXCL_LINE
            return GMERR_CONFIG_ERROR;
        }
        if (strcpy_s(filePathList[i], DB_MAX_FILE_PATH_LEN, path) != EOK) {
            return GMERR_FIELD_OVERFLOW;
        }
        ret = FormatFilePath(filePathList[i], DB_MAX_FILE_PATH_LEN);
        if (ret != GMERR_OK) {
            return ret;
        }
        filePathStr = tailStr;
    }

    // 开启按需建表场景，提前记录好obj schema文件路径和obj policy文件路径。
    if (DbGetSchemaLoaderMod() == SCHEMA_LOADER_ON_DEMAND) {
        // 初始化按需建表控制并发的锁
        InitCreateTableOnDemandLock();
        // 1) 记录按需建表的obj对象文件夹路径
        errno_t err = strcpy_s(DbGetSchemaFilePath(), DB_MAX_FILE_PATH_LEN, filePathList[CFG_OBJ_SCHEMA_PATH_INDEX]);
        if (err != EOK) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW,
                "Create tables on demand. Unable to copy schema file path, ret: %" PRId32 ".", err);  // LCOV_EXCL_LINE
            return GMERR_FIELD_OVERFLOW;
        }
        // 2) 记录按需建表obj policy文件夹路径。（加载map失败不影响服务启动。）
        uint32_t policyPathLen =
            filePathList[CFG_OBJ_POLICY_PATH_INDEX] == NULL ? 0 : strlen(filePathList[CFG_OBJ_POLICY_PATH_INDEX]);
        if (policyPathLen != 0) {
            ret = DbInitPolicyFilePath(policyPathLen + 1);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "Create tables on demand. Unable to init obj policy file path.");  // LCOV_EXCL_LINE
                return GMERR_OK;
            }
            err = strcpy_s(DbGetPolicyFilePath(), DB_MAX_FILE_PATH_LEN, filePathList[CFG_OBJ_POLICY_PATH_INDEX]);
            if (err != EOK) {
                DB_LOG_ERROR(GMERR_FIELD_OVERFLOW,
                    "Create tables on demand. Unable to copy obj policy file path, ret: %" PRId32 ".",
                    err);  // LCOV_EXCL_LINE
                return GMERR_OK;
            }
        }
    }
    return GMERR_OK;
}

static Status GetSchemaPathList(char (*filePathList)[DB_MAX_FILE_PATH_LEN], uint32_t pathNum)
{
    // 获取DB_CFG_SCHEMA_PATH配置文件的路径
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    DbCfgValueT filePath;
    Status ret = DbCfgGet(cfgHandle, DB_CFG_SCHEMA_PATH, &filePath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unsucc to get schema path config.");  // LCOV_EXCL_LINE
        return ret;
    }
    return GetPathList(filePath.str, filePathList, pathNum);
}

static Status GetSchemaDataPathList(char (*filePathList)[DB_MAX_FILE_PATH_LEN])
{
    // 获取DB_CFG_SCHEMA_DATA_PATH配置文件的路径
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    DbCfgValueT filePath;
    Status ret = DbCfgGet(cfgHandle, DB_CFG_SCHEMA_DATA_PATH, &filePath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unsucc to get schema data path config.");  // LCOV_EXCL_LINE
        return ret;
    }
    if (DbStrCmp(filePath.str, "", false) == 0) {
        return GMERR_NO_DATA;
    }
    return GetPathList(filePath.str, filePathList, CFG_SCHEMA_DATA_PATH_NUM);
}

static Status SetKeyForLabelByJsonFileName(char *filePath, DbMemCtxT *memCtx, CataKeyT *cataKey)
{
    char tempPath[PATH_MAX] = {0};
    errno_t err = strcpy_s(tempPath, PATH_MAX, filePath);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "cpy filePath: %s unsucc.", filePath);  // LCOV_EXCL_LINE
        return GMERR_FIELD_OVERFLOW;
    }
    char *tempName = DbGetFileNameInfo(tempPath, sizeof(tempPath), DB_FILENAME_PREFIX);
    if (tempName == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_UNEXPECTED_NULL_VALUE, "Get file prefix unsucc, filePath: %s.", filePath);  // LCOV_EXCL_LINE
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    // 通过memCtx统一释放
    uint32_t labelNameLen = DM_STR_LEN(tempName);
    char *labelName = DbDynMemCtxAlloc(memCtx, labelNameLen);
    if (labelName == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(labelName, labelNameLen, 0, labelNameLen);
    err = strcpy_s(labelName, labelNameLen, tempName);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Cannot to cpy labelName.");  // LCOV_EXCL_LINE
        return GMERR_FIELD_OVERFLOW;
    }

    uint32_t publicNspId = 0;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(memCtx);
    Status ret = CataGetNamespaceIdByName(dbInstance, PUBLIC_NAMESPACE_NAME, &publicNspId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Cannot to get namespace id by name: %s.", PUBLIC_NAMESPACE_NAME);  // LCOV_EXCL_LINE
        return ret;
    }
    CataSetKeyForLabel(cataKey, DEFAULT_DATABASE_ID, publicNspId, labelName);
    return GMERR_OK;
}

static void FillAndSkipMsgHeader(FixBufferT *req, uint32_t opCode, uint16_t stmtId)
{
    FixBufResetMem(req);
    // 预留 MsgHeaderT + OpHeaderT
    FixBufInitPut(req, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE);
    MsgHeaderT *msgHdr = RpcPeekMsgHeader(req);

    MsgHeaderT msg = {0};
    msg.serviceId = DRT_SERVICE_STMT;
    msg.modelType = MODEL_FASTPATH;
    msg.flags = CS_FLAG_NONE;
    msg.stmtId = stmtId;
    msg.msgMagicNum = MSG_VERIFY_NUMBER;
    msg.protocolVersion = MSG_PROTOCOL_VERSION_PRIVATE;
    msg.reqTimeOut = CS_NEVER_TIMEOUT;
    msg.priority = MSG_PRIO_NORMAL;
    *msgHdr = msg;

    OpHeaderT *opHdr = ProtocolPeekFirstOpHeader(req);
    OpHeaderT op = {0};
    op.opCode = opCode;
    *opHdr = op;
    FixBufSeek(req, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE);
}

static Status FillSingleVertex(FixBufferT *req, DbJsonT *vertexJson, DmVertexT *vertex, DmVertexLabelT *vertexLabel)
{
    Status ret = DmSetVertexByJson(vertex, vertexLabel, vertexJson);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Cannot to set vertex by json");
        return ret;
    }
    uint32_t vertexBufLen = 0;
    uint8_t *buf = NULL;
    ret = DmSerializeVertex(vertex, &buf, &vertexBufLen);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Cannot to serialize vertex");  // LCOV_EXCL_LINE
        return ret;
    }
    TextT vertexBufText = {.len = vertexBufLen, .str = (char *)buf};
    ret = FixBufPutText(req, &vertexBufText);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FixBufPutUint32(req, 0);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (vertexLabel->commonInfo->dlrInfo.isDataSyncLabel) {
        // fill isReplay, 常规操作，不支持DLR
        return FixBufPutUint32(req, 0);
    }
    return GMERR_OK;
}

static Status FillRepVertexMsg(FixBufferT *req, VertexDataInfoT *vertexDataInfo, uint32_t index)
{
    DmVertexLabelT *vertexLabel = vertexDataInfo->vertexLabel;
    Status ret = FixBufPutUint32(req, vertexLabel->metaCommon.metaId);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(req, vertexLabel->metaCommon.version);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(req, vertexLabel->metaVertexLabel->uuid);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FixBufPutUint32(req, IMPORT_VERTEX_NUM);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmVertexT *vertex = vertexDataInfo->vertex;
    DbJsonT *vertexJson = DbJsonArrayGet(vertexDataInfo->json, index);
    if (vertexJson == NULL) {
        ret = GMERR_INVALID_JSON_CONTENT;
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "Cannot to get vertex from json, idx: %" PRIu32 ".", index);  // LCOV_EXCL_LINE
        return ret;
    }

    ret = FillSingleVertex(req, vertexJson, vertex, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Cannot to fill req with vertex, idx: %" PRIu32 ", vertexlabel = %s.", index,
            vertexLabel->metaCommon.metaName);
        return ret;
    }

    ret = DmResetVertex(vertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status GetVertexLabelToImportData(char *filePath, DbMemCtxT *memCtx, DmVertexLabelT **vertexLabel)
{
    // Get label by file name
    CataKeyT cataKey = {0};
    Status ret = SetKeyForLabelByJsonFileName(filePath, memCtx, &cataKey);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CataGetVertexLabelByName(DbGetInstanceByMemCtx(memCtx), &cataKey, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Cannot to get vertexlabel by name: %s.", cataKey.labelName);
        return ret;
    }
    if (DmVertexLabelIsDatalogLabel(*vertexLabel) || DmIsYangVertexLabel(*vertexLabel)) {
        (void)CataReleaseVertexLabel(*vertexLabel);
        *vertexLabel = NULL;
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED,
            "Unsupported to import data to datalog or yang label when Db start, label name: %s.",
            cataKey.labelName);  // LCOV_EXCL_LINE
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

static void ReleaseVertexDataInfo(VertexDataInfoT *vertexDataInfo, DbInstanceHdT dbInstance)
{
    if (vertexDataInfo->json != NULL) {
        DbJsonDelete(vertexDataInfo->json);
        vertexDataInfo->json = NULL;
    }

    if (vertexDataInfo->vertex != NULL) {
        DmDestroyVertex(vertexDataInfo->vertex);
        vertexDataInfo->vertex = NULL;
    }

    if (vertexDataInfo->vertexLabel != NULL) {
        (void)CataReleaseVertexLabel(vertexDataInfo->vertexLabel);
        vertexDataInfo->vertexLabel = NULL;
    }
}

static Status ImportVertexDataFromFile(SessionT *session, char *filePath, DbMemCtxT *memCtx)
{
    VertexDataInfoT vertexDataInfo = {0};
    Status ret = GetVertexLabelToImportData(filePath, memCtx, &vertexDataInfo.vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DmCreateEmptyVertexWithMemCtx(memCtx, vertexDataInfo.vertexLabel, &vertexDataInfo.vertex);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }

    ret = QryLoadJsonFile(filePath, &vertexDataInfo.json);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }
    if (!DbJsonIsArray(vertexDataInfo.json)) {
        ret = GMERR_DATATYPE_MISMATCH;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "novalid json type when import vertex data.");  // LCOV_EXCL_LINE
        goto RELEASE;
    }
    vertexDataInfo.totalNum = (uint32_t)DbJsonGetArraySize(vertexDataInfo.json);
    FixBufferT *req = session->req;
    for (uint32_t index = 0; index < vertexDataInfo.totalNum; index++) {
        FillAndSkipMsgHeader(req, MSG_OP_RPC_REPLACE_VERTEX, session->currentStmt->id);
        ret = FillRepVertexMsg(req, &vertexDataInfo, index);
        if (ret != GMERR_OK) {
            goto RELEASE;
        }
        ret = FastPathProcessCommandFunc(session->currentStmt, MSG_OP_RPC_REPLACE_VERTEX, NULL);
        if (ret != GMERR_OK) {
            goto RELEASE;
        }
        FixBufResetMem(req);
    }

RELEASE:
    ReleaseVertexDataInfo(&vertexDataInfo, DbGetInstanceByMemCtx(session->memCtx));
    return ret;
}

static Status DbServerImportVertexData(char *filePath, DbMemCtxT *memCtx)
{
    SessionT *session = NULL;
    Status ret =
        QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, DbGetInstanceByMemCtx(memCtx), &session);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to allocate session for import vertex data.");  // LCOV_EXCL_LINE
        return ret;
    }
    session->isDBA = true;

    ret = QryGetStmtById(session, 0, &session->currentStmt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to get stmt to import vertex data when DB start.");  // LCOV_EXCL_LINE
        goto RELEASE;
    }

    FixBufferT req = {0};
    // 通过memCtx统一释放
    ret = FixBufCreate(&req, memCtx, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }
    QrySessionSetReq(session, &req);

    ret = ImportVertexDataFromFile(session, filePath, memCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to import vertex data from json file: %s.", filePath);
    }
    FixBufRelease(&req);

RELEASE:
    QrySessionRelease(session);
    return ret;
}

#ifdef FEATURE_KV
static Status ImportParseKvInfo(char *buffer, uint64_t bufferSize, uint64_t *offset, KvTupleT *kvTuple)
{
    DB_POINTER2(buffer, offset);
    uint64_t keyLenOffset = *offset;
    if (keyLenOffset + sizeof(uint32_t) > bufferSize) {
        goto EXIT;
    }
    kvTuple->keyLen = *(uint32_t *)(buffer + keyLenOffset);
    uint64_t keyOffset = keyLenOffset + (uint64_t)sizeof(uint32_t);
    if (keyOffset + kvTuple->keyLen > bufferSize) {
        goto EXIT;
    }
    kvTuple->key = (uint8_t *)(buffer + keyOffset);
    uint64_t valueLenOffset = keyOffset + kvTuple->keyLen;
    if (valueLenOffset + sizeof(uint32_t) > bufferSize) {
        goto EXIT;
    }
    kvTuple->valueLen = *(uint32_t *)(buffer + valueLenOffset);
    uint64_t valueOffset = valueLenOffset + (uint64_t)sizeof(uint32_t);
    if (valueOffset + kvTuple->valueLen > bufferSize) {
        goto EXIT;
    }
    kvTuple->value = (uint8_t *)(buffer + valueOffset);
    *offset = *offset + kvTuple->keyLen + kvTuple->valueLen + sizeof(uint32_t) + sizeof(uint32_t);
    return GMERR_OK;

EXIT:
    DB_LOG_ERROR_AND_SET_LASTERR(
        GMERR_DATA_EXCEPTION, "Parse kv data unsucc at %" PRIu64 ", buffer size is %" PRIu64 ".", *offset, bufferSize);
    return GMERR_DATA_EXCEPTION;
}

static Status FillSetKvMsg(char *kvBuffer, uint64_t fileSize, DmKvLabelT *label, FixBufferT *req, uint64_t *offset)
{
    // fill label id
    Status ret = FixBufPutUint32(req, label->metaCommon.metaId);
    if (ret != GMERR_OK) {
        return ret;
    }

    KvTupleT kvInfo = {0};
    ret = ImportParseKvInfo(kvBuffer, fileSize, offset, &kvInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill key with len
    ret = SecureFixBufPutRawText(req, kvInfo.keyLen, kvInfo.key);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill value with len
    ret = SecureFixBufPutRawText(req, kvInfo.valueLen, kvInfo.value);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status ImportKvDataFromFile(SessionT *session, char *filePath, DbMemCtxT *memCtx)
{
    // Get label by file name
    CataKeyT cataKey = {0};
    Status ret = SetKeyForLabelByJsonFileName(filePath, memCtx, &cataKey);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmKvLabelT *label = NULL;
    ret = CataGetLabelByName(&cataKey, &label, DbGetInstanceByMemCtx(session->memCtx));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Cannot to get Kv label by name: %s.", cataKey.labelName);
        return ret;
    }

    // load file
    size_t fileSize = 0;
    char *kvBuffer = QryImportReadDataFromFile(memCtx, filePath, &fileSize);
    if (kvBuffer == NULL) {
        (void)CataReleaseLabel(label);
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to read kv data, file: %s", filePath);  // LCOV_EXCL_LINE
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    uint64_t offset = 0;
    FixBufferT *req = session->req;
    while (offset < fileSize) {
        FillAndSkipMsgHeader(req, MSG_OP_RPC_SET_KV, session->currentStmt->id);
        ret = FillSetKvMsg(kvBuffer, fileSize, label, req, &offset);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to fill set Kv massage");
            break;
        }
        ret = FastPathProcessCommandFunc(session->currentStmt, MSG_OP_RPC_SET_KV, NULL);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to call FastPathProcessCommand to set Kv data");  // LCOV_EXCL_LINE
            break;
        }
        FixBufResetMem(req);
    }

    DbDynMemCtxFree(memCtx, kvBuffer);
    (void)CataReleaseLabel(label);
    return ret;
}

static Status DbServerImportKvData(char *filePath, DbMemCtxT *memCtx)
{
    SessionT *session = NULL;
    Status ret =
        QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, DbGetInstanceByMemCtx(memCtx), &session);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to allocate session for import kv data.");  // LCOV_EXCL_LINE
        return ret;
    }
    session->isDBA = true;

    ret = QryGetStmtById(session, 0, &session->currentStmt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to get stmt to import kv data when DB start.");  // LCOV_EXCL_LINE
        goto RELEASE;
    }

    FixBufferT req = {0};
    // 通过memCtx统一释放
    ret = FixBufCreate(&req, memCtx, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }
    QrySessionSetReq(session, &req);

    ret = ImportKvDataFromFile(session, filePath, memCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to import Kv data from file: %s.", filePath);
    }
    FixBufRelease(&req);

RELEASE:
    QrySessionRelease(session);
    return ret;
}
#endif

Status DbServerImportData(char *filePath, DbMemCtxT *memCtx)
{
    char tempPath[PATH_MAX] = {0};
    errno_t err = strcpy_s(tempPath, sizeof(tempPath), filePath);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "cpy file path: %s unsucc.", filePath);  // LCOV_EXCL_LINE
        return GMERR_FIELD_OVERFLOW;
    }
    char *suffix = DbGetFileNameInfo(tempPath, sizeof(tempPath), DB_FILENAME_SUFFIX);
    if (suffix == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_UNEXPECTED_NULL_VALUE, "Get file suffix unsucc, file path: %s.", filePath);  // LCOV_EXCL_LINE
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    if (strcmp(suffix, "gmdata") == 0) {
        return DbServerImportVertexData(filePath, memCtx);
#ifdef FEATURE_KV
    } else if (strcmp(suffix, "gmkv") == 0) {
        return DbServerImportKvData(filePath, memCtx);
#endif
    } else {
        return GMERR_NO_DATA;  // 忽略，不处理
    }
}

#ifdef FEATURE_DATALOG
static Status DbServerImportDatalogSoInner(char *filePath, DbMemCtxT *memCtx)
{
    SessionT *session = NULL;
    Status ret =
        QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, DbGetInstanceByMemCtx(memCtx), &session);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to allocate session for import datalog so when DB start.");  // LCOV_EXCL_LINE
        return ret;
    }
    QryStmtT *stmt = NULL;

    ret = InitStmtForSchemaLoader(session, &stmt);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }
    session->currentStmt = stmt;
    typedef Status (*ImportDatalogSoFromFileFuncT)(SessionT * session, char *filePath, DbMemCtxT *memCtx);
    ImportDatalogSoFromFileFuncT importDatalogDataFunc =
        (ImportDatalogSoFromFileFuncT)DbDynLoadGetFunc("datalog", "import-data");
    if (importDatalogDataFunc == NULL) {
        DB_LOG_INFO("can not find datalog services module");  // LCOV_EXCL_LINE
        return GMERR_OK;
    }
    ret = importDatalogDataFunc(session, filePath, memCtx);  // ImportDatalogSoFromFile
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to import datalog so file: %s.", filePath);  // LCOV_EXCL_LINE
    }

RELEASE:
    QrySessionRelease(session);
    return ret;
}

Status DbServerImportDatalogData(char *filePath, DbMemCtxT *memCtx)
{
    char tempPath[PATH_MAX] = {0};
    errno_t err = strcpy_s(tempPath, sizeof(tempPath), filePath);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_FIELD_OVERFLOW, "Strcpy_s datalog so filePath: %s unsucc.", filePath);  // LCOV_EXCL_LINE
        return GMERR_FIELD_OVERFLOW;
    }
    char *suffix = DbGetFileNameInfo(tempPath, sizeof(tempPath), DB_FILENAME_SUFFIX);
    if (suffix == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "Get datalog so file suffix unsucc, filePath: %s.",
            filePath);  // LCOV_EXCL_LINE
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    if (strcmp(suffix, "so") == 0) {
        return DbServerImportDatalogSoInner(filePath, memCtx);
    } else {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_UNEXPECTED_NULL_VALUE, "Datalog so file suffix nvalid, filePath: %s.", filePath);  // LCOV_EXCL_LINE
        return GMERR_INVALID_PARAMETER_VALUE;  // 文件类型不合法
    }
}

static Status DbServerImportDatalogPatchInner(char *filePath, DbMemCtxT *memCtx)
{
    SessionT *session = NULL;
    Status ret =
        QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, DbGetInstanceByMemCtx(memCtx), &session);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to allocate session for import datalog patch when DB start.file: %s.",
            filePath);  // LCOV_EXCL_LINE
        return ret;
    }

    typedef Status (*ImportDatalogPatchFromFileFuncT)(SessionT * session, char *filePath, DbMemCtxT *memCtx);
    ImportDatalogPatchFromFileFuncT importDatalogPatchDataFunc =
        (ImportDatalogPatchFromFileFuncT)DbDynLoadGetFunc("datalog", "import-dataPatch");
    if (importDatalogPatchDataFunc == NULL) {
        DB_LOG_INFO("can not find datalog patch services module");  // LCOV_EXCL_LINE
        return GMERR_OK;
    }
    ret = importDatalogPatchDataFunc(session, filePath, memCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to import datalog patch file: %s.", filePath);  // LCOV_EXCL_LINE
    }

    QrySessionRelease(session);
    return ret;
}

static bool CheckPatchPattern(DbMemCtxT *memCtx, char *patchName)
{
    // 正则匹配ylog_patch_数字.so格式
    char *pattern = "^ylog_patch_[0-9]+.so$";
    regex_t regex;
    int32_t ret = regcomp(&regex, pattern, REG_EXTENDED);
    if (SECUREC_UNLIKELY(ret != 0)) {
        DB_LOG_ERROR(ret, "unable to call regcomp");  // LCOV_EXCL_LINE
        return false;
    }

    ret = regexec(&regex, patchName, 0, NULL, 0);
    if (ret != 0) {
        DB_LOG_ERROR(ret, "unable to match %s with %s", patchName, pattern);
    }
    regfree(&regex);
    return (ret == 0);
}

static int32_t CompareStrAsc(const void *a, const void *b)
{
    const char *strA = *(const char *const *)a;
    const char *strB = *(const char *const *)b;
    return DbStrCmp(strA, strB, false);
}

static Status ConcatPatchPath(DbMemCtxT *memCtx, char *dirPath, char *patchName, char **absolutePatchPath)
{
    Status ret = GMERR_OK;
    // 保存patchName, \0 + '/'
    size_t size = strlen(dirPath) + strlen(patchName) + DATALOG_PATH_LEN_TWO;
    char *tmpPatchPath = DbDynMemCtxAlloc(memCtx, size);
    if (tmpPatchPath == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY,
            "unable to malloc for patchAllPath when load datalog patch, dir:%s, patchName:%s.", dirPath,
            patchName);  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }

    if (sprintf_s(tmpPatchPath, size, "%s/%s", dirPath, patchName) < 0) {
        ret = GMERR_FIELD_OVERFLOW;
        DB_LOG_ERROR_AND_SET_LASTERR(ret,
            "sprintf dirPath and patchName unsucc when concat patchPath,dirPath:%s,patchName:%s", dirPath,
            patchName);  // LCOV_EXCL_LINE
        goto ERROR;
    }

    *absolutePatchPath = tmpPatchPath;
    return GMERR_OK;
ERROR:
    DbDynMemCtxFree(memCtx, tmpPatchPath);
    return ret;
}

static Status DtlReadAllPatchFile(char *dirPath, DbMemCtxT *memCtx, DbListT *patchFiles)
{
    DbDIRT *dir = NULL;
    Status ret = DbOpenDir(dirPath, &dir);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DIRECTORY_OPERATE_FAILED, "open directory: %s unsucc.", dirPath);  // LCOV_EXCL_LINE
        return ret;
    }

    // 读取目录下文件
    char subDirPath[DB_MAX_FILE_PATH_LEN] = {0};
    if (sprintf_s(subDirPath, DB_MAX_FILE_PATH_LEN, "%s/", dirPath) < 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_FIELD_OVERFLOW, "cpy subDirPath unsucc, path: %s", dirPath);  // LCOV_EXCL_LINE
        goto RELEASE;
    }

    const uint32_t len = (uint32_t)strlen(subDirPath);
    while ((ret = DbReadDir(dir, subDirPath + len, DB_MAX_FILE_PATH_LEN - len)) == GMERR_OK) {
        if (strcmp(subDirPath + len, ".") == 0 || strcmp(subDirPath + len, "..") == 0) {
            continue;
        }

        // 校验补丁类型以及名称
        if (!CheckPatchPattern(memCtx, subDirPath + len)) {
            continue;
        }

        char *patchPath = NULL;
        if ((ret = ConcatPatchPath(memCtx, dirPath, subDirPath + len, &patchPath)) != GMERR_OK) {
            break;
        }

        if ((ret = DbAppendListItem(patchFiles, &patchPath)) != GMERR_OK) {
            break;
        }
    }

    if (ret == GMERR_NO_DATA) {
        ret = GMERR_OK;
    }

RELEASE:
    DbCloseDir(dir);
    return ret;
}

// 读取patch目录下补丁文件，解决带补丁重启问题，按照patch命名字典序加载
Status DbServerImportDatalogPatchData(char *filePath, DbMemCtxT *memCtx)
{
    // 获取patch目录下所有补丁名
    DbListT patchFiles = {};
    DbCreateList(&patchFiles, sizeof(char *), memCtx);
    Status ret = DtlReadAllPatchFile(filePath, memCtx, &patchFiles);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }

    // patch排序
    uint32_t patchCnt = DbListGetItemCnt(&patchFiles);
    if (patchCnt == 0) {
        goto RELEASE;
    }
    qsort(patchFiles.items, patchCnt, patchFiles.itemSize, CompareStrAsc);

    // 逐个加载patch
    for (uint32_t i = 0; i < patchCnt; i++) {
        char *patchPath = *(char **)DbListItem(&patchFiles, i);
        ret = DbServerImportDatalogPatchInner(patchPath, memCtx);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Cannot to load patch: %s.", patchPath);  // LCOV_EXCL_LINE
            goto RELEASE;
        }
    }

RELEASE:
    DbDestroyList(&patchFiles);
    return ret;
}

#endif

static Status FindPoliciesFileInner(char *curDirPath, char *outPoliciesFile)
{
    DB_POINTER(curDirPath);
    if (QryCheckFileSuffix(curDirPath, "gmpolicies") && strlen(outPoliciesFile) == 0) {
        if (sprintf_s(outPoliciesFile, DB_MAX_FILE_PATH_LEN, "%s", curDirPath) < 0) {
            DB_LOG_ERROR(
                GMERR_INTERNAL_ERROR, "Create tables on demand, unable to find gmpolicies.");  // LCOV_EXCL_LINE
            return GMERR_INTERNAL_ERROR;
        }
        return GMERR_OK;
    }
    return GMERR_NO_DATA;
}

static Status FindPoliciesFile(char *curDirPath, char *outPoliciesFile, uint32_t folderLayer)
{
    DB_POINTER2(curDirPath, outPoliciesFile);
    // 递归文件夹深度不超过FIRST_FOLDER_LAYER + MAX_FOLDER_LAYER_LIMIT
    if (folderLayer > FIRST_FOLDER_LAYER + MAX_FOLDER_LAYER_LIMIT) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Create tables on demand. The directory is too deep to load, path: %s.",
            curDirPath);  // LCOV_EXCL_LINE
        return GMERR_INTERNAL_ERROR;
    }

    if (DbFileExist(curDirPath)) {
        return FindPoliciesFileInner(curDirPath, outPoliciesFile);
    }

    DbDIRT *curDir = NULL;
    Status ret = DbOpenDir(curDirPath, &curDir);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Create tables on demand, unable to open dir:(%s).", curDirPath);  // LCOV_EXCL_LINE
        return ret;
    }
    char curSubDirPath[DB_MAX_FILE_PATH_LEN] = {0};
    int32_t err = snprintf_s(curSubDirPath, sizeof(curSubDirPath), sizeof(curSubDirPath) - 1, "%s/", curDirPath);
    if (err < 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Create tables on demand, unable to get subDirPath, ret: %" PRId32 ".",
            err);  // LCOV_EXCL_LINE
        DbCloseDir(curDir);
        return GMERR_INTERNAL_ERROR;
    }
    const uint32_t len = (uint32_t)strlen(curSubDirPath);
    while ((ret = DbReadDir(curDir, curSubDirPath + len, DB_MAX_FILE_PATH_LEN - len)) == GMERR_OK) {
        if (strcmp(curSubDirPath + len, ".") == 0 || strcmp(curSubDirPath + len, "..") == 0) {
            continue;
        }
        if (FindPoliciesFile(curSubDirPath, outPoliciesFile, folderLayer + 1) == GMERR_OK) {
            break;
        }
    }
    DbCloseDir(curDir);
    return GMERR_OK;
}

#define PRIVI_JSON_ARR_ITEM_NUM 1u
static const DbJsonT *CheckAndGetFirstPolicyConfigJson(const DbJsonT *objJson)
{
    DbJsonT *policyJsonConfigObj = DbJsonObjectGet(objJson, "object_privilege_config");
    if (policyJsonConfigObj == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT, "Create tables on demand. The object_privilege_config is null.");
        return NULL;
    }
    // object_privilege_config为数组且只能包含一个元素
    if (!DbJsonIsArray(policyJsonConfigObj)) {
        DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT,
            "Create tables on demand. The object_privilege_config should be array.");  // LCOV_EXCL_LINE
        return NULL;
    }
    uint32_t objCnt = (uint32_t)DbJsonGetArraySize(policyJsonConfigObj);
    if (objCnt != PRIVI_JSON_ARR_ITEM_NUM) {
        DB_LOG_ERROR(
            GMERR_INVALID_JSON_CONTENT, "Create tables on demand. ObjCnt is %" PRIu32 ".", objCnt);  // LCOV_EXCL_LINE
        return NULL;
    }
    const DbJsonT *configJson = DbJsonArrayGet(policyJsonConfigObj, 0);
    if (configJson == NULL || !DbJsonIsObject(configJson)) {
        DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT,
            "Create tables on demand. The object_privilege_config should be obj");  // LCOV_EXCL_LINE
        return NULL;
    }
    return configJson;
}

// obj_type只能是KV_TABLE或者VERTEX_LABEL
static bool IsMatchObjType(const DbJsonT *json)
{
    if (json == NULL) {
        return false;
    }
    const DbJsonT *objTypeJson = DbJsonObjectGet(json, "obj_type");
    if (objTypeJson == NULL || !DbJsonIsString(objTypeJson)) {
        return false;
    }
    const char *objType = DbJsonStringValue(objTypeJson);
    if (strcmp(objType, "KV_TABLE") == 0 || strcmp(objType, "VERTEX_LABEL") == 0) {
        return true;
    }
    return false;
}

static Status CatLabelNameAndNspName(char *catStr, uint32_t catStrLen, char *labelName, char *nspName)
{
    errno_t err = strcat_s(catStr, catStrLen, labelName);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "Create tables on demand. Unable to cat labelName:%s, ret: %" PRId32 ".",
            labelName, err);  // LCOV_EXCL_LINE
        return GMERR_FIELD_OVERFLOW;
    }
    err = strcat_s(catStr, catStrLen - strlen(labelName), nspName);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "Create tables on demand. Unable to cat nspName:%s, ret: %" PRId32 ".",
            nspName, err);  // LCOV_EXCL_LINE
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

static Status InsertItemIntoMapInner(DbMemCtxT *memCtx, const DbJsonT *objJson, char *keyInfo, DbOamapT *map)
{
    char *valueInfo = DbJsonDumps(objJson, 0);
    if (valueInfo == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT, "Create tables on demand. The obj json is null.");  // LCOV_EXCL_LINE
        return GMERR_INVALID_JSON_CONTENT;
    }
    // key, value长生命周期，不释放。key: labelName + nspName, value: objJson
    Status ret = GMERR_OK;
    uint32_t keyLen = (uint32_t)strlen(keyInfo) + 1;
    char *key = DbDynMemCtxAlloc(memCtx, keyLen);
    if (key == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Create tables on demand. Size: %" PRIu32 ".", keyLen);  // LCOV_EXCL_LINE
        goto EXIT3;
    }
    uint32_t valueLen = (uint32_t)strlen(valueInfo) + 1;
    char *value = DbDynMemCtxAlloc(memCtx, valueLen);
    if (value == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_ERROR(ret, "Create tables on demand. Alloc size: %" PRIu32 ".", valueLen);  // LCOV_EXCL_LINE
        goto EXIT2;
    }
    errno_t err = strcpy_s(key, keyLen, keyInfo);
    if (err != EOK) {
        ret = GMERR_FIELD_OVERFLOW;
        DB_LOG_ERROR(ret, "Create tables on demand. Key info: %s, ret:%" PRId32 ".", keyInfo, err);  // LCOV_EXCL_LINE
        goto EXIT1;
    }
    err = strcpy_s(value, valueLen, valueInfo);
    if (err != EOK) {
        ret = GMERR_FIELD_OVERFLOW;
        DB_LOG_ERROR(ret, "Create tables on demand. Info: %s, ret:%" PRId32 ".", valueInfo, err);  // LCOV_EXCL_LINE
        goto EXIT1;
    }

    uint32_t hash = DbStrToHash32(key);
    ret = DbOamapInsert(map, hash, (void *)key, (void *)value, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Create tables on demand. Unable to insert policy info into map.");  // LCOV_EXCL_LINE
        goto EXIT1;
    }
    DbJsonFree(valueInfo);
    return GMERR_OK;
EXIT1:
    DbDynMemCtxFree(memCtx, value);
EXIT2:
    DbDynMemCtxFree(memCtx, key);
EXIT3:
    DbJsonFree(valueInfo);
    return ret;
}

static Status SetAndInsertItemIntoPoliciesMap(char *labelName, char *nspName, const DbJsonT *objJson, DbOamapT *map)
{
    char keyInfo[MAX_POLICY_MAP_KEY_LEN] = {0};
    Status ret = GMERR_OK;
    if ((ret = CatLabelNameAndNspName(keyInfo, MAX_POLICY_MAP_KEY_LEN, labelName, nspName)) != GMERR_OK) {
        return ret;
    }
    DbMemCtxT *memCtx = DbSrvGetSysDynCtx(DbGetProcGlobalId());
    if (memCtx == NULL) {
        DB_LOG_ERROR(
            GMERR_MEMORY_OPERATE_FAILED, "Create tables on demand. Unable to get sys dyn ctx.");  // LCOV_EXCL_LINE
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return InsertItemIntoMapInner(memCtx, objJson, keyInfo, map);
}

static Status InsertSinglePolicyInfoIntoMapInner(const DbJsonT *objJson, DbOamapT *map, uint32_t index)
{
    const DbJsonT *policyJson = CheckAndGetFirstPolicyConfigJson(objJson);
    // 检查obj_type只能是KV_TABLE或者VERTEX_LABEL.
    if (!IsMatchObjType(policyJson)) {
        DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT, "Create tables on demand. The %" PRIu32 " obj is inv.", index);
        return GMERR_INVALID_JSON_CONTENT;
    }
    const DbJsonT *objNspJson = DbJsonObjectGet(policyJson, "namespace");
    if (objNspJson == NULL || !DbJsonIsString(objNspJson)) {
        DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT, "Create tables on demand. Inv %" PRIu32 ".", index);  // LCOV_EXCL_LINE
        return GMERR_INVALID_JSON_CONTENT;
    }
    char nspName[MAX_NAMESPACE_LENGTH] = {0};
    const char *nspStr = DbJsonStringValue(objNspJson);
    if (nspStr == NULL || strlen(nspStr) == 0) {
        DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT, "Create tables on demand. Inv %" PRIu32 ".", index);  // LCOV_EXCL_LINE
        return GMERR_INVALID_JSON_CONTENT;
    }
    errno_t err = strcat_s(nspName, MAX_NAMESPACE_LENGTH, nspStr);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "Create tables on demand. Item %" PRIu32 " nspName: %s, ret: %" PRId32 ".",
            index, nspStr, err);  // LCOV_EXCL_LINE
        return GMERR_FIELD_OVERFLOW;
    }

    const DbJsonT *objNameJson = DbJsonObjectGet(policyJson, "obj_name");
    if (objNameJson == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT, "Create tables on demand. The %" PRIu32 " obj name is null.",
            index);  // LCOV_EXCL_LINE
        return GMERR_INVALID_JSON_CONTENT;
    }
    char labelName[MAX_TABLE_NAME_LEN] = {0};
    if (DbJsonIsString(objNameJson)) {
        const char *objName = DbJsonStringValue(objNameJson);
        if (objName == NULL || strlen(objName) == 0) {
            DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT, "Create tables on demand, the %" PRIu32 " obj name is inv.",
                index);  // LCOV_EXCL_LINE
            return GMERR_INVALID_JSON_CONTENT;
        }
        err = strcat_s(labelName, MAX_TABLE_NAME_LEN, objName);
        if (err != GMERR_OK) {
            DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "Create tables on demand. Item:%" PRIu32 ", name: %s, ret:%" PRId32 ".",
                index, objName, err);  // LCOV_EXCL_LINE
            return GMERR_FIELD_OVERFLOW;
        }
    } else {
        DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT, "Create tables on demand. Obj name should be string.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    return SetAndInsertItemIntoPoliciesMap(labelName, nspName, objJson, map);
}

static Status GetObjPolicyConfigByIndex(DbJsonT *json, uint32_t index, DbOamapT *map)
{
    const DbJsonT *objPolicyJson = DbJsonArrayGet(json, (size_t)index);
    if (objPolicyJson == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT,
            "Create tables on demand. Unable to get %" PRIu32 " from policyJsonArr.", index);  // LCOV_EXCL_LINE
        return GMERR_INVALID_JSON_CONTENT;
    }

    return InsertSinglePolicyInfoIntoMapInner(objPolicyJson, map, index);
}

static Status InsertPoliciesIntoMap(char *filePath, DbOamapT *map)
{
    DB_POINTER2(filePath, map);
    if (!DbFileExist(filePath)) {
        DB_LOG_WARN(GMERR_OK, "Create tables on demand. The policies file:%s is not exist.", filePath);
        return GMERR_OK;
    }
    DbJsonT *root = DbJsonLoadsFile(filePath, 0);
    if (root == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_JSON_CONTENT, "Create tables on demand. Unable to load policy json: %s.", filePath);
        return GMERR_INVALID_JSON_CONTENT;
    }
    Status ret = GMERR_OK;
    // 获取object_privilege_configs
    DbJsonT *policyJsonObj = DbJsonObjectGet(root, "object_privilege_configs");
    if (policyJsonObj == NULL) {
        ret = GMERR_INVALID_JSON_CONTENT;
        DB_LOG_ERROR(ret, "Create tables on demand. Object_privilege_configs is null.");
        goto EXIT;
    }
    if (DbJsonIsObject(policyJsonObj)) {
        if ((ret = InsertSinglePolicyInfoIntoMapInner(policyJsonObj, map, 0) != GMERR_OK)) {
            goto EXIT;
        }
    } else if (DbJsonIsArray(policyJsonObj)) {
        uint32_t count = (uint32_t)DbJsonGetArraySize(policyJsonObj);
        for (uint32_t i = 0; i < count; i++) {
            ret = GetObjPolicyConfigByIndex(policyJsonObj, i, map);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "Create tables on demand. Unable to get %" PRIu32 " obj policy.", i);
                continue;  // 不合法的json，不中断正常流程，只打印日志信息。
            }
        }
    } else {
        ret = GMERR_INVALID_JSON_CONTENT;
        DB_LOG_ERROR(ret, "Create tables on demand. The object_privilege_configs type is inv.");
        goto EXIT;
    }

EXIT:
    DbJsonDelete(root);
    return ret;
}

#define POLICY_MAP_COUNT 512
static Status CreatePoliciesMap(void)
{
    DB_ASSERT(g_gmdbAllPoliciesMap == NULL);
    DbMemCtxT *memCtx = DbSrvGetSysDynCtx(DbGetProcGlobalId());
    if (memCtx == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
            "Create tables on demand. Unable to get memctx for create policies map.");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    g_gmdbAllPoliciesMap = (DbOamapT *)DbDynMemCtxAlloc(memCtx, sizeof(DbOamapT));
    if (g_gmdbAllPoliciesMap == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
            "Create tables on demand. Unable to alloc mem for create policies map.");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    Status ret = DbOamapInit(g_gmdbAllPoliciesMap, POLICY_MAP_COUNT, DbOamapStringCompare, memCtx, true);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(memCtx, g_gmdbAllPoliciesMap);
        g_gmdbAllPoliciesMap = NULL;
        DB_LOG_ERROR(ret, "Create tables on demand. Unable to init policies map.");  // LCOV_EXCL_LINE
        return ret;
    }
    return GMERR_OK;
}

static void InitPoliciesMap(char *filePath)
{
    if (filePath == NULL || strlen(filePath) == 0) {
        return;
    }
    // 查找XX.gmpolicies文件
    char policyFilePath[DB_MAX_FILE_PATH_LEN] = "\0";
    Status ret = FindPoliciesFile(filePath, policyFilePath, FIRST_FOLDER_LAYER);
    // 找到XXX.policies后，释放g_gmdbPolicyFilePath内存
    DbDestoryPolicyFilePath();
    if (ret != GMERR_OK) {
        return;
    }
    if (strlen(policyFilePath) == 0) {
        return;
    }
    ret = CreatePoliciesMap();
    if (ret != GMERR_OK) {
        return;
    }
    (void)InsertPoliciesIntoMap(policyFilePath, g_gmdbAllPoliciesMap);
}

void ImportSourcesCommon(DbMemCtxT *memCtx, char (*filePathList)[DB_MAX_FILE_PATH_LEN], uint32_t pathNum)
{
    for (uint32_t i = 0; i < pathNum; i++) {
        uint64_t beginTime = DbRdtsc();
        DbAdptPrintCpuRunTime(true, "begin", "DbServerImportSchema-%" PRIu32 "", i);
        LoaderInfoT loaderInfo = {0};
        loaderInfo.stag = (DbServerImportStageE)i;
        // 单一资源导入失败不中断执行，故不处理返回值
        DbServerImportMutipleFile(filePathList[i], memCtx, &loaderInfo, FIRST_FOLDER_LAYER);
        DbAdptPrintCpuRunTime(true, "end", "DbServerImportSchema-%" PRIu32 "", i);
        uint64_t endTime = DbRdtsc();
        DB_BOOT_LOG_WARN(GMERR_OK,
            "INFO: import sources (step: %" PRIu32 ") when DbStart, successFileNum: %" PRIu32
            ", unsuccFileNum: %" PRIu32 ", stage cost time: %" PRIu64 "ms.",
            (uint32_t)loaderInfo.stag, loaderInfo.successNum, loaderInfo.unsuccessNum,
            DbToMseconds(endTime - beginTime));
    }
}

static Status DbServerImportSchemaFromShMem(DbMemCtxT *memCtx)
{
    // 从共享内存导入业务数据，需要等待业务准备好数据后，继续执行
    // 由业务配置等待时间[0, 1800]秒，如果业务配置0秒，则无限等待；默认配置60秒
    uint32_t waitTimeInSeconds = (uint32_t)DbCfgGetInt32Lite(DB_CFG_SCHEMA_LOADER_WAIT_TIME, NULL);
    uint32_t waitCount = waitTimeInSeconds == 0 ? DB_INVALID_UINT32 : waitTimeInSeconds * 10;  // 1秒扫描频率10次
    uint32_t waitTime = 0;                                                                     // 已等待时间
    uint64_t beginTime = DbRdtsc();
    for (uint32_t i = 1; i <= waitCount; i++) {
        if (DbFileExist("/run/verona/schema_ready")) {
            break;
        }
        // 每隔 (600 * WAIT_DATA_TIME) ms打印一行warning日志
        if (i % 600 == 0) {
            waitTime += 60;  // 已等待时间增加60s
            DB_LOG_WARN(GMERR_NO_DATA,
                "WARN: schema_ready still doesn't exist, DB has been waiting for %" PRIu32
                "s, remaining wait time: %" PRIu32 "s.",
                waitTime, waitCount / 10 - waitTime);  // LCOV_EXCL_LINE
        }
        if (i == waitCount) {
            DB_LOG_ERROR(GMERR_NO_DATA, "Wait time exceeds the limit: %" PRIu32 "s, schema_ready doesn't exist.",
                waitTimeInSeconds);  // LCOV_EXCL_LINE
            return GMERR_NO_DATA;
        }
        DbUsleep(WAIT_DATA_TIME);
    }
    uint64_t endTime = DbRdtsc();
    DB_LOG_WARN(GMERR_OK, "INFO: Wait prepare shmem data, delay %" PRIu64 "ms.", DbToMseconds(endTime - beginTime));
    // 从共享内存中导入gmjson和gomconfig（不包含nsp对象）、policy信息
    // 导入失败不影响db正常启动
    (void)ImportSchemaAndPolicyFromShmem(memCtx);
    return GMERR_OK;
}

Status DbServerImportSchema(DbMemCtxT *memCtx)
{
    SchemaLoaderModE loaderMod = DbGetSchemaLoaderMod();
    uint32_t needImporPathNum = CFG_SCHEMA_PATH_NUM;  // 起服务过程中需要导入的文件路径数量
    uint32_t needParsePathNum = CFG_SCHEMA_PATH_NUM;  // 需要解析的文件路径数量
    char filePathList[CFG_SCHEMA_PATH_NUM][DB_MAX_FILE_PATH_LEN] = {0};
    Status ret = GMERR_OK;
    if (loaderMod == SCHEMA_LOADER_ON_DEMAND) {
        // SCHEMA_LOADER_ON_DEMAND：只导入系统权限和白名单。
        needImporPathNum = CFG_SCHEMA_PATH_NUM - 2;  // 2：按需建表的时候，这里不导入json和policy。
    }
    if (loaderMod == SCHEMA_LOADER_FULL_FROM_SHMEM) {
        // SCHEMA_LOADER_FROM_SHMEM： 系统权限和白名单从文件中导入，obj、policy从共享内存中导入。
        needImporPathNum = CFG_SCHEMA_PATH_NUM_FOR_SHMEM;
        needParsePathNum = CFG_SCHEMA_PATH_NUM_FOR_SHMEM;
    }
    ret = GetSchemaPathList(filePathList, needParsePathNum);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Cannot to get file path lists when load sources.");
        return GMERR_OK;  // 路径解析失败，不影响起服务。
    }
    if (loaderMod == SCHEMA_LOADER_ON_DEMAND) {
        // SCHEMA_LOADER_ON_DEMAND: 服务启动过程中，提前加载合并后的文件（加载失败不中断服务，打印日志信息）
        InitPoliciesMap(DbGetPolicyFilePath());
    }
    // 依次导入白名单、系统权限、表及nsp对象(可选)、对象权限（可选）
    ImportSourcesCommon(memCtx, filePathList, needImporPathNum);
    if (loaderMod == SCHEMA_LOADER_FULL_FROM_SHMEM) {
        return DbServerImportSchemaFromShMem(memCtx);
    }
    return GMERR_OK;
}

Status DbServerImportSchemaData(DbMemCtxT *memCtx)
{
    if (DbCommonGetWarmReboot()) {
        DB_LOG_WARN(GMERR_INSUFFICIENT_RESOURCES, "Unable to import data when warm reboot recovering.");
        return GMERR_INSUFFICIENT_RESOURCES;
    }
    // 依次导入数据
    char filePathList[CFG_SCHEMA_DATA_PATH_NUM][DB_MAX_FILE_PATH_LEN] = {0};
    Status ret = GetSchemaDataPathList(filePathList);
    if (ret == GMERR_NO_DATA) {
        return ret;
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Cannot to get file path lists when load data.");  // LCOV_EXCL_LINE
        return ret;
    }

    for (uint32_t i = 0; i < CFG_SCHEMA_DATA_PATH_NUM; i++) {
        LoaderInfoT loaderInfo = {0};
        loaderInfo.stag = (DbServerImportStageE)(IMPORT_BASE_DATA + i);
        DbServerImportMutipleFile(filePathList[i], memCtx, &loaderInfo, FIRST_FOLDER_LAYER);
        DB_BOOT_LOG_WARN(GMERR_OK,
            "INFO: import data (step: %" PRIu32 ") when DbStart, successFileNum: %" PRIu32 ", unsuccFileNum: %" PRIu32
            ".",
            (uint32_t)loaderInfo.stag, loaderInfo.successNum, loaderInfo.unsuccessNum);
    }
    return GMERR_OK;
}

#ifdef FEATURE_DATALOG
static Status GetDatalogSoPath(char *soFilePath)
{
    // 获取DB_CFG_DATALOG_SO_PATH配置文件的路径
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    DbCfgValueT filePath;
    Status ret = DbCfgGet(cfgHandle, DB_CFG_DATALOG_SO_PATH, &filePath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unsucc to get datalog so path config.");  // LCOV_EXCL_LINE
        return ret;
    }
    if (DbStrCmp(filePath.str, "", false) == 0) {
        return GMERR_NO_DATA;
    }

    errno_t err = strcpy_s(soFilePath, DB_MAX_FILE_PATH_LEN, filePath.str);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW,
            "Cannot to get datalog so path when load datalog so. retno:%" PRId32 ", filePath is:%s.", (int32_t)err,
            filePath.str);  // LCOV_EXCL_LINE
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

static Status GetPatchFileDir(char *soPath, char *patchDir)
{
    char *fileName = strrchr(soPath, '/');
    if (fileName == NULL) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "strrchr unsucc when get patch path, path: %s", soPath);  // LCOV_EXCL_LINE
        return GMERR_DATA_EXCEPTION;
    }
    if (strncpy_s(patchDir, DB_MAX_FILE_PATH_LEN, soPath, strlen(soPath) - strlen(fileName)) != EOK) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Str copy unsucc when get patch path, fileName %s, path: %s", fileName,
            soPath);  // LCOV_EXCL_LINE
        return GMERR_DATA_EXCEPTION;
    }

    char *patchSubDir = "/patch";
    if (strcat_s(patchDir, DB_MAX_FILE_PATH_LEN, patchSubDir) != EOK) {
        DB_LOG_ERROR(
            GMERR_DATA_EXCEPTION, "Concat str unsucc when get patch path, path: %s", soPath);  // LCOV_EXCL_LINE
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status DbServerImportDatalogSo(DbMemCtxT *memCtx)
{
    char filePath[DB_MAX_FILE_PATH_LEN] = {0};
    Status ret = GetDatalogSoPath((char *)filePath);
    if (ret != GMERR_OK) {
        return ret;
    }
    LoaderInfoT loaderInfo = {0};
    loaderInfo.stag = IMPORT_DATALOG_SO;
    if (DbFileExist(filePath)) {
        DbServerImportSingleFile(filePath, memCtx, &loaderInfo);
    }
    DB_BOOT_LOG_INFO("Import datalog so (step: %" PRIu32 ") when DbStart, successFileNum: %" PRIu32
                     ", unsuccFileNum: %" PRIu32 ".",
        (uint32_t)loaderInfo.stag, loaderInfo.successNum, loaderInfo.unsuccessNum);

    // 加上/patch路径
    char patchDir[DB_MAX_FILE_PATH_LEN] = {0};
    ret = GetPatchFileDir(filePath, patchDir);
    if (ret != GMERR_OK) {
        return ret;
    }
    LoaderInfoT patchLoaderInfo = {0};
    patchLoaderInfo.stag = IMPORT_DATALOG_PATCH;
    if (DbDirExist(patchDir)) {
        DbServerImportSingleFile(patchDir, memCtx, &patchLoaderInfo);
    }
    return GMERR_OK;
}
#endif

Status DbServerImportRulesAndObjects(void)
{
    uint8_t schemaLoaderMod = (uint8_t)DbCfgGetInt32Lite(DB_CFG_NEED_SCHEMA_LOADER, NULL);
    DbSetSchemaLoaderMod(schemaLoaderMod);
    if (schemaLoaderMod == (uint8_t)SCHEMA_LOADER_DISABLED) {
        return GMERR_OK;
    }

#ifndef FEATURE_TS
    if (SeGetPersistMode() != PERSIST_OFF && (schemaLoaderMod != (uint8_t)SCHEMA_LOADER_DISABLED)) {
        // 持久化场景下关闭服务端schema loader，也不导入白名单和系统权限。
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_FEATURE_NOT_SUPPORTED, "unable to start schema loader in persistence mode.");  // LCOV_EXCL_LINE
        return GMERR_OK;  // 不影响正常启动服务。
    }
#endif

    DbMemCtxT *sysDynCtx = DbSrvGetSysDynCtx(DbGetProcGlobalId());
    if (sysDynCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_UNEXPECTED_NULL_VALUE, "unsucc to get sys dyn memctx to load sources.");  // LCOV_EXCL_LINE
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbMemCtxArgsT args = {0};
    DbMemCtxT *memCtx = DbCreateDynMemCtx(sysDynCtx, false, "Load rules and objects memCtx", &args);  // app动态内存
    if (memCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_MEMORY_OPERATE_FAILED, "Unable to create dynamic memctx when load sources.");  // LCOV_EXCL_LINE
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    Status ret = DbServerImportSchema(memCtx);
    if (ret == GMERR_OK) {
        DbAdptPrintCpuRunTime(true, "begin", "DbServerImportSchemaData");
        (void)DbServerImportSchemaData(memCtx);
        DbAdptPrintCpuRunTime(true, "end", "DbServerImportSchemaData");
    } else {
        DbDeleteDynMemCtx(memCtx);
        return ret;
    }
// 导入datalog so
#ifdef FEATURE_DATALOG
#ifndef NDEBUG
    uint64_t beginTime = DbRdtsc();
#endif
    DbAdptPrintCpuRunTime(true, "begin", "DbServerImportDatalogSo");
    (void)DbServerImportDatalogSo(memCtx);
    DbAdptPrintCpuRunTime(true, "end", "DbServerImportDatalogSo");
#ifndef NDEBUG
    uint64_t endTime = DbRdtsc();
    DB_BOOT_LOG_WARN(GMERR_OK, "INFO: import datalogso end, delay %" PRIu64 "ms.", DbToMseconds(endTime - beginTime));
#endif
#endif
    DbDeleteDynMemCtx(memCtx);
    return GMERR_OK;
}
