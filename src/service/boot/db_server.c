/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: db_server.c
 * Description: Provides the function StartDb() for the HPE and gmserver.
 * Author:
 * Create: 2020-8-14
 */

#include "db_server.h"
#include <errno.h>
#include <dlfcn.h>
#include "adpt_define.h"
#include "db_common_init.h"
#include "db_sysapp_context.h"
#include "db_config.h"
#include "db_server_dfgmt_task.h"
#include "db_server_undo_purger.h"
#include "db_server_purger.h"
#include "adpt_process_lock.h"
#include "srv_view.h"
#include "db_signal.h"
#include "adpt_sleep.h"
#include "adpt_spinlock.h"
#include "adpt_process_name.h"
#include "db_label_latch_mgr.h"
#include "db_utils.h"
#include "drt_base_def.h"
#include "adpt_init.h"
#include "ee_cltcache.h"
#include "sysview_instance.h"
#include "srv_conn_service.h"
#include "cpl_base_def.h"
#include "cpl_base_pub.h"
#include "srv_data_service.h"
#include "srv_data_ha.h"
#include "adpt_log.h"
#include "db_config_file.h"
#include "db_config.h"
#include "srv_log.h"
#include "ee_init.h"
#include "ee_rsm_recovery_public.h"
#include "db_server_import.h"
#include "adpt_startup_time.h"
#include "ee_persist.h"
#include "ee_feature_import.h"
#include "se_space.h"
#include "db_dyn_load.h"
#include "dm_base_def.h"
#include "adpt_string.h"
#include "db_server_periodic_persistence.h"
#include "de_init.h"
#include "de_instance.h"
#include "de_op_history_base.h"
#include "se_persist.h"
#include "db_instance.h"
#include "ee_feature_import.h"
#include "srv_data_fastpath.h"
#ifdef FEATURE_YANG
#include "srv_data_yang.h"
#endif
#ifdef FEATURE_DAF
// RR事务才需要
#include "db_server_daf_purger.h"
#endif
#ifdef HPE_SIMULATION
#include "adpt_define.h"
#endif
#include "se_ckpt.h"
#include "ee_key_cmp.h"
#include "ee_systbl.h"
#include "se_recovery.h"
#include "se_page_mgr.h"
#ifdef ART_CONTAINER
#include "se_art.h"
#endif
#ifdef FEATURE_REPLICATION
#include "srv_data_slave_replay.h"
#endif
#include "cpl_public_parser_ddl.h"
#include "srv_data_alarm.h"
#include "adpt_thread.h"
#include "db_dead_loop.h"

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
static void RegisterIdxRecoveryCallback(void)
{
#ifndef FEATURE_SIMPLEREL
    IdxRecoveryCallBackFuncRegister(QryVertexHashCompare);
#endif
}

#if defined(FEATURE_YANG)
Status DbYangRecovery(RecoveryCtxT *recoveryCtx, SeInstanceHdT seIns, bool isFirstBoot)
{
    DbCfgValueT recoveryUpgDir = {0};
    Status ret = DbCfgGet(DbGetCfgHandle(seIns->dbInstance), DB_CFG_YANG_UPGRADE_DIR_PATH, &recoveryUpgDir);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get cfg DB_CFG_YANG_UPGRADE_DIR_PATH.");
        return DbGetStatusInterErrno(ret);
    }
    if (recoveryUpgDir.str[0] == '\0') {
        // 如果未配置表升级目录，则不创建 metaDiffMap
        return DbRecovery(recoveryCtx, seIns, isFirstBoot);
    }

    DbMemCtxT *rootMemCtx = seIns->seServerMemCtx;
    DbMemCtxArgsT args = {0};
    DbMemCtxT *newMemCtx = (DbMemCtxT *)DbCreateDynMemCtx(rootMemCtx, false, "YangRecoveryCtx", &args);
    if (newMemCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Create YangRecoveryCtx.");
        return GMERR_OUT_OF_MEMORY;
    }
    recoveryCtx->memCtx = newMemCtx;
    DbOamapT *metaDiffMap = DbDynMemCtxAlloc(newMemCtx, sizeof(DbOamapT));
    if (metaDiffMap == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Alloc map");
        goto EXIT;
    }
    ret = DbOamapInit(metaDiffMap, 1, DbOamapUint32Compare, newMemCtx, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "init metaDiffMap.");
        goto EXIT;
    }
    recoveryCtx->metaDiffMap = metaDiffMap;
    ret = DbRecovery(recoveryCtx, seIns, isFirstBoot);

EXIT:
    DbDeleteDynMemCtx(newMemCtx);
    recoveryCtx->memCtx = NULL;
    recoveryCtx->metaDiffMap = NULL;
    return ret;
}
#endif

Status DoRecovery(SeInstanceHdT seIns)
{
    RegisterIdxRecoveryCallback();
    RecoveryCtxT recoveryCtx = {0};
    recoveryCtx.idxRebuildFunc = QryRebuildVertexLabelIndex;
    recoveryCtx.cataSetUuidFunc = CataSetUuid;
#if defined(FEATURE_YANG)
    recoveryCtx.planCacheRebuildFunc = YangServiceRebuildPlanCache;
    recoveryCtx.recoverUpgradeLabels = YangServiceRecoverUpgradeLabels;
    recoveryCtx.recoverUpdateLeafListLabels = YangServiceRecoverUpdateLeafListLabels;
    recoveryCtx.autoIncIdRecycleFunc = QryRecycleYangAutoIncId;
    return DbYangRecovery(&recoveryCtx, seIns, true);
#else
    return DbRecovery(&recoveryCtx, seIns, true);
#endif
}

static Status DbPersisInitByMode(DbInstanceHdT dbInstance)
{
    Status ret = GMERR_OK;
    SeInstanceHdT seIns = SeGetInstance(DbGetInstanceId(dbInstance));
    if (seIns == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (SeGetStorageStatus(seIns) == SE_ON_DISK_CREATE) {
        ret = SysTableStart(dbInstance);
        if (ret != GMERR_OK) {
            DbServerCleanAfterInitFail("systable", ret, true, dbInstance);
            return ret;
        }

        if (SeGetPersistMode() == PERSIST_INCREMENT) {
            // 元数据及时落盘， 以保证第二次启动正常加载
            DB_LOG_INFO("begin trigger ckpt on startup");
            ret = CkptTrigger(seIns, CKPT_MODE_BOOT_FULL, true, WAIT_MSECONDS_FOR_CHECKPOINT);
            if (ret != GMERR_OK) {
                DbServerCleanAfterInitFail("ckpt trigger", ret, true, dbInstance);
                return ret;
            }
            DB_LOG_INFO("end trigger ckpt");
        }
    } else {
        ret = DoRecovery(seIns);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    ret = SeProcedureAfterRecovery(dbInstance, seIns);
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("procedure after recov", ret, true, dbInstance);
        return ret;
    }

    return SeSetStorageStatus(seIns, SE_ON_DISK_ONLINE);
}
#else
static Status DbPersisInitByMode(DbInstanceHdT dbInstance)
{
    DB_ASSERT(false);
    return GMERR_OK;
}
#endif /* FEATURE_SIMPLEREL */

#ifdef DB_BOOT_FUZZ
// 在DB启动阶段初始化各模块后，加入随机时延，使启动变慢，用于检查模块依赖
// 如果启动时间超过20S，设备会拉起不成功，则此时序测试失效。需保证加入时延后总启动时间小于20S
#define FUZZ_DELAY(MOD_NAME)                                                                                      \
    do {                                                                                                          \
        uint32_t delayTime = rand() % 1000;                                                                       \
        DbUsleep(delayTime * 1000);                                                                               \
        DB_BOOT_LOG_WARN(GMERR_OK, "gmserver boot fuzz test, mod:%s, delay:%" PRIu32 " ms", MOD_NAME, delayTime); \
    } while (0)
#else
#define FUZZ_DELAY(MOD_NAME) (void)0
#endif

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#ifdef WARM_REBOOT
#define PC_PERSISTENCE_THREAD_SCHEDULE_TIME 1000000u
#endif
#define DB_PROCESS_SLEEP_MSTIME 2000

typedef enum DbServerStatus {
    DB_SERVER_STATUS_STOPPED = 0,
    DB_SERVER_STATUS_RUNNING,
} DbServerStatusE;

typedef enum DbServerProcType {
    DB_SERVER_PROC_TYPE_LINUX,
#ifdef HPE
    DB_SERVER_PROC_TYPE_HPE,
#endif
} DbServerProcTypeE;

static struct TagDbServerInstance {
    DbServerStatusE status;
    DbServerProcTypeE type;
    uint64_t dbStartTime;
    uint64_t dbStartTimeVal;
    uint64_t initiateDelay;
    ConnServiceT connSvc;
    uint32_t ignoreSigCount;
    int32_t exitSignal;
} g_gmdbSvrInst = {0};

// 记录DB启动各阶段时间点
typedef struct {
    uint64_t startTime;
    uint64_t prepare;
    uint64_t initModules;
    uint64_t createPath;
    uint64_t importRulesAndObjects;
} GmserverStartStageTimePointT;

uint64_t DbGetServerStartTime(uint32_t instId)
{
    DB_UNUSED(instId);
    return g_gmdbSvrInst.dbStartTime;
}

uint64_t DbGetServerStartTimeVal(uint32_t instId)
{
    DB_UNUSED(instId);
    return g_gmdbSvrInst.dbStartTimeVal;
}

uint64_t DbGetServerInitiateDelay(uint32_t instId)
{
    DB_UNUSED(instId);
    return g_gmdbSvrInst.initiateDelay;
}

static void DbServerPrintToStdout(const char *moduleName, Status ret, const char *desc)
{
    DB_POINTER(moduleName);
    if (desc == NULL) {
        if (ret == GMERR_OK) {
            DbPrintToStdout("(GMDB SERVER) %s init successfully!", moduleName);
        } else {
            DbPrintToStdout("(GMDB SERVER) (GMERR-%" PRId32 ") %s init unsucc!", (int32_t)ret, moduleName);
        }
    } else {
        if (ret == GMERR_OK) {
            DbPrintToStdout("(GMDB SERVER) %s init successfully! %s.", moduleName, desc);
        } else {
            DbPrintToStdout("(GMDB SERVER) (GMERR-%" PRId32 ") %s init unsucc! %s.", (int32_t)ret, moduleName, desc);
        }
    }
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
static void DbServerPrintInitFailedWithSysError(const char *module, Status ret)
{
#define ERR_BUFF_LEN 512
    char errLogBuff[ERR_BUFF_LEN] = {0};
    const char *errLogBufStr = NULL;
    if (DbAptStrError(errLogBuff, ERR_BUFF_LEN)) {
        errLogBufStr = errLogBuff;
    }
    DbServerPrintToStdout(module, ret, errLogBufStr);
#undef ERR_BUFF_LEN
}
#endif /* FEATURE_SIMPLEREL */

bool DbProcessIsStopped(void)
{
    return (g_gmdbSvrInst.status == DB_SERVER_STATUS_STOPPED);
}

void DbServerInit(DbServerProcTypeE type)
{
    g_gmdbSvrInst.status = DB_SERVER_STATUS_RUNNING;
    g_gmdbSvrInst.type = type;
#ifdef HPE_SIMULATION
    DbSetIsServerFlag();
#endif
}

inline static void DbInitStorageConfigSetPurger(SeConfigT *config)
{
    config->undoPurgerCreate = ServerCreateUndoPurger;  // 注册启动undo purger的函数
#ifdef FEATURE_DAF
    // RR事务才需要
    config->dafPurgerCreate = ServerCreateDafPurger;  // 注册启动daf purger的函数
#endif
}

static void DbInitStorageConfigLog(SeConfigT config)
{
    DB_LOG_INFO("(SE)Configuration item configured successfully. (deviceSize:%" PRIu16 ", pageSize:%" PRIu16
                ", maxSeMem:%" PRIu16 ", instanceId:%" PRIu16 ", heapTupleAddrMode:%" PRIu32 ")",
        config.deviceSize, config.pageSize, config.maxSeMem, config.instanceId, (uint32_t)config.heapTupleAddrMode);
}
// init storage module
Status DbInitStorage(DbServerOptionT *dbServerOption, DbInstanceHdT dbInstance)
{
#ifdef STREAM_EMB
    return GMERR_OK;
#endif
    // 当前 maxLockShareCnt 位数为11位，2^11为 2048
    _Static_assert((int32_t)MAX_CONN_NUM <= 2048, "max connections and maxLockShareCnt needs to be adjusted together");
    // use defalut config without config file
    const char *recoveryPath = NULL;
    Status ret = GMERR_OK;
    if (DbDynLoadHasFeature(COMPONENT_PERSISTENCE)) {
        recoveryPath = dbServerOption->recoveryPath;
        TrxRecoveryLabelInfoHandleRegister(PurgerGetLabelInfo, PurgerReleaseLabelInfo);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    SeConfigT config = {0};
    ret = StorageConfigGet(dbInstance, &config, recoveryPath);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 配置成功后打印配置日志
    DbInitStorageConfigLog(config);
    DbInitStorageConfigSetPurger(&config);
    config.seKeepThreadAlive = DrtKeepThisWorkerAlive;  // 注册喂狗接口
    // 持久化开启的情况下，默认为非只读实例；
    // mini KV场景下，若开启一写多读，则后续初始化流程可能会更改此配置，如：持久化开启,但为只读进程
    config.isReadOnlyInstance = SeGetPersistMode() != PERSIST_OFF;

    DbMemCtxT *topShmMemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, DbGetInstanceId(dbInstance));
    if (topShmMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Unable to find top shmemCtx when init storage module.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    SeInstanceHdT se = NULL;
    ret = SeCreateInstance(dbInstance, topShmMemCtx, &config, &se);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbSetSeInstance(dbInstance, (void *)se);
    DrtInstanceT *drtIns = DrtGetInstance(dbInstance);
    if (drtIns == NULL) {
        return GMERR_DATA_EXCEPTION;
    }
    DrtRegisterFlowCtrlShmMemUsage(drtIns, SeGetUsedPageMemSize, se);
#ifdef ART_CONTAINER
    ret = StartEpochMgr();
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Art container init epoch mgr");
        return ret;
    }
#endif
    return GMERR_OK;
}

// 单独的
bool IsServerSingleProcess(void)
{
#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
    uint32_t pid = DbAdptGetpid();
    char pName[DB_MAX_PROC_NAME_LEN] = {0};
    (void)DbGetProcessNameByPid(pid, pName, DB_MAX_PROC_NAME_LEN);
    if (strstr(pName, "gmserver") != NULL) {
        return true;
    } else {
        return false;
    }
#else
    return true;
#endif
}

Status DbServerPrepareStart(const DbServerOptionT *dbServerOption, int32_t instanceId)
{
    DB_POINTER(dbServerOption);
    DbSetInstanceId(instanceId);
    if (DbProcessInstanceIsExist()) {
        DbServerPrintToStdout("Server process already exists, ", GMERR_PROGRAM_LIMIT_EXCEEDED, NULL);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    if (dbServerOption->isBackground) {
        if (IsServerSingleProcess()) {
            Status ret = DbMakeProcessBackground();
            if (ret != GMERR_OK) {
                DbServerPrintToStdout("Process background, ", ret, NULL);
                return ret;
            }
        }
    }
    return GMERR_OK;
}

Status DbDfxRegist(SvInstanceT *viewInst)
{
    DB_POINTER(viewInst);
    Status ret = SvServiceViewRegist(viewInst);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "register mem compact dfx unsucc!");
        return ret;
    }

    return GMERR_OK;
}

// 该函数退出清理函数相较于DbServerShutDown缺少部分模块，如果判断是否可以清理干净
void DbServerCleanAfterInitFail(const char *moduleName, Status ret, bool isDrtInstanceInit, DbInstanceHdT dbInstance)
{
    DB_POINTER(moduleName);
    DbServerPrintToStdout(moduleName, ret, NULL);
    DB_LOG_ERROR(ret, "(%s) init unsucc!", moduleName);
    CataLabelCacheUnRegTimer(dbInstance);
    if (DbCfgGetBoolLite(DB_CFG_TRX_MONITOR_ENABLE, dbInstance)) {
        DbDeadLoopUnRegist(DEAD_LOOP_TRX);
    }
    if (isDrtInstanceInit) {
        DrtInstanceDestroy(dbInstance);
    }
    SeDestroyInstance(DbGetInstanceId(dbInstance));
    DbCommonFinalize(dbInstance);
}

inline static AdptEnvironmentE GetAdptEnvType(void)
{
#ifdef HPE
    return ADPT_HPE;
#else
    return ADPT_RTOS_SERVER;
#endif
}

Status DbServiceInit(DbInstanceHdT dbInstance)
{
    Status ret = GMERR_OK;
    if (!DbIsMultiInstanceEnabled()) {
        ret = SrvInitServiceMgr(dbInstance);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Server manager init unsucc!");
            return ret;
        }
    }

    ret = DataServiceInit(dbInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Data service init unsucc!");
        return ret;
    }

    DrtInstanceT *drtInstance = DrtGetInstance(dbInstance);
    if (drtInstance == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return ConnInitService(&g_gmdbSvrInst.connSvc, drtInstance);
}

#ifdef WARM_REBOOT
void *PeriodicPersistence(void *ctx, uint16_t workerId)
{
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    DbCfgValueT periodicPersistenceDirPath;
    Status ret = DbCfgGet(cfgHandle, DB_PERIODIC_PERSISTENCE_DIRPATH, &periodicPersistenceDirPath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get config option for DB_PERIODIC_PERSISTENCE_DIRPATH worthless.");
        return NULL;
    }
    DbCfgValueT periodicPersistence;
    ret = DbCfgGet(cfgHandle, DB_PERIODIC_PERSISTENCE, &periodicPersistence);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get config option for DB_PERIODIC_PERSISTENCE worthless.");
        return NULL;
    }

    DrtInstanceT *drtInstance = DrtGetInstance(NULL);
    if (drtInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Get instance worthless when start periodic persistence thread.");
        return NULL;
    }
    uint8_t instanceId = drtInstance->instanceId;

    WorkerMgrT *workerMgr = (WorkerMgrT *)ctx;
    uint64_t lastTimeStamp = 0;
    while (!DrtWorkerIsStopping(workerMgr, workerId)) {
        DbSleep((uint32_t)periodicPersistence.int32Val * MSECONDS_IN_SECOND);
        ret =
            MakeNowTimeDirAndExport(&periodicPersistenceDirPath, instanceId, &lastTimeStamp, g_gmdbSvrInst.dbStartTime);
        if (ret != GMERR_OK) {
            continue;
        }
    }
    return NULL;
}

Status DbStartPeriodicPersistenceThread(DrtInstanceT *drtInstance)
{
    WorkerParaT workerPara = {.name = "Pc_Persist",
        .type = DRT_WORKER_BGTASK,
        .entry = PeriodicPersistence,
        .ctx = (void *)&drtInstance->workerMgr,
        .isBindCpu = 1,
        .cpuNO = 1,
        .priority = WORKER_PRIORITY_HIGH,
        .nameWithId = false,
        .scheduleTime = PC_PERSISTENCE_THREAD_SCHEDULE_TIME * SCHEDULE_TIMES};
    Status ret = DrtStartNewWorker(&drtInstance->workerMgr, (void *)(&workerPara), NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Start periodic persistence thread worthless.");
        return ret;
    }
    return GMERR_OK;
}

Status DbPeriocPersistenceInit(void)
{
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    DbCfgValueT periodicPersistence;
    Status ret = DbCfgGet(cfgHandle, DB_PERIODIC_PERSISTENCE, &periodicPersistence);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get config option for DB_PERIODIC_PERSISTENCE worthless.");
        return ret;
    }
    if (periodicPersistence.int32Val == 0) {
        DB_LOG_INFO("No need for periodic persistence.");
        return GMERR_OK;
    }
    DbCfgValueT periodicPersistenceDirPath;
    ret = DbCfgGet(cfgHandle, DB_PERIODIC_PERSISTENCE_DIRPATH, &periodicPersistenceDirPath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get config option for DB_PERIODIC_PERSISTENCE_DIRPATH worthless.");
        return ret;
    }
    if (DbStrCmp(periodicPersistenceDirPath.str, "", false) == 0) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Periodic persistence dir path is empty.");
        return GMERR_OK;
    }
    DrtInstanceT *drtInstance = DrtGetInstance(NULL);
    if (drtInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Get instance worthless when start periodc persistence thread.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint8_t instanceId = drtInstance->instanceId;

    ret = MakePeriocPersisInstanDirectory(&periodicPersistenceDirPath, instanceId);
    if (ret != GMERR_OK) {
        return ret;
    }

    return DbStartPeriodicPersistenceThread(drtInstance);
}
#endif

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
void DbServiceUninit(DbInstanceHdT dbInstance)
{
    if (!DbIsMultiInstanceEnabled()) {
        ConnDestroyService(&g_gmdbSvrInst.connSvc);
    }

    DataServiceUnInit(dbInstance);
    SrvDestroyServiceMgr();
}

Status DbServerInitModules(DbServerOptionT *dbServerOption, DbInstanceHdT dbInstance)
{
#ifndef FEATURE_SIMPLEREL
    DB_POINTER(dbServerOption);
    DbCommonInitCfgT cfg = {
        .env = GetAdptEnvType(),
        .configFileName = dbServerOption->cfgFilePath,
        .isBackGround = dbServerOption->isBackground,
        .openMode = dbServerOption->openMode,
    };
    Status ret = DbCommonInit(&cfg, dbInstance, DbServerPrintToStdout);
    if (ret != GMERR_OK) {
        DbServerPrintToStdout("Common", ret, NULL);
        return ret;
    }
#ifdef DB_BOOT_FUZZ
    srand(time(NULL));
#endif
    FUZZ_DELAY("Common");
    if ((ret = DbServerInitCltStat()) != GMERR_OK) {
        DbServerCleanAfterInitFail("Client statistic", ret, false, dbInstance);
        return ret;
    }
    FUZZ_DELAY("CltStat");
    ret = RuntimeModuleInit(NULL);
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Runtime instance", ret, false, dbInstance);
        return ret;
    }
    FUZZ_DELAY("Runtime");
    ret = DbInitStorage(dbServerOption, NULL);
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Storage", ret, true, dbInstance);
        return ret;
    }
    FUZZ_DELAY("Storage");
    ret = DataModelInit(dbInstance);
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Data model", ret, true, dbInstance);
        return ret;
    }
    FUZZ_DELAY("DataModel");
    ret = CompilerInit();
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Compiler", ret, true, dbInstance);
        return ret;
    }
    FUZZ_DELAY("Compiler");
    ret = DbDfxRegist(DbSvGetSvInstance(dbInstance));
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Regist dfx", ret, true, dbInstance);
        return ret;
    }
    FUZZ_DELAY("DfxRegist");
    ret = DbServiceInit(dbInstance);
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Server", ret, true, dbInstance);
        return ret;
    }
    FUZZ_DELAY("Service");
    ret = DbSrvDfgmtTaskMgrInit(NULL);
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Memory compact task mgr", ret, true, dbInstance);
        return ret;
    }
    FUZZ_DELAY("DfgmtTaskMgr");
    ret = ExecutorInit(dbInstance);
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Executor", ret, true, dbInstance);
        return ret;
    }
    FUZZ_DELAY("Executor");
#ifdef WARM_REBOOT
    ret = DbPeriocPersistenceInit();
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Periodc persistence", ret, true, dbInstance);
        return ret;
    }
    FUZZ_DELAY("PeriocPersistence");
#endif
    if (SeGetPersistMode() != PERSIST_OFF) {
        SysTableInit(dbInstance);
        ret = DbPersisInitByMode(dbInstance);
        if (ret != GMERR_OK) {
            DbServerCleanAfterInitFail("Recovery", ret, true, dbInstance);
            return ret;
        }
        FUZZ_DELAY("Persistence");
    }
    bool isWarmReboot = DbCommonGetIsWarmReboot();
    if (isWarmReboot) {
        ShmemPtrT rsmGrobalConfigPtr = DbRsmMgrGetMetaInfo(DB_RSM_META_TYPE_GLOBAL_CONFIG);
        RsmemGlobalConfigT *globalConfig = DbShmPtrToAddr(rsmGrobalConfigPtr);
        if (globalConfig == NULL) {
            DbServerCleanAfterInitFail("Write Cache", ret, true, dbInstance);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        CataSetUuid(globalConfig->uuid, dbInstance);
    }
    DrtInitComplete(NULL);
    bool isUseRsm = DbCfgGetInt32Lite(DB_CFG_IS_USE_RSM, NULL) == 0 ? false : true;
    if (!isWarmReboot && isUseRsm) {
        // 表示初始化阶段结束，后续出现服务端出现异常，DB可用warmReboot重启恢复
        DbRsmKernelLeave(DB_RSM_KERNEL_INIT);
    }
#endif
    return GMERR_OK;
}

void DbSigCoredumpHandler(int32_t signo)
{
#if !defined(HPE) || defined(HPE_SIMULATION)
#if !defined(FEATURE_RSMEM)
    DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "gmserver receive signal %" PRId32 ".", signo);
    // HPE有打印core堆栈，stacktrace内部有不可重入函数，因此HPE下暂不调，不影响DFX
    DbLogCurrentStackTrace();
#endif
    (void)DbRaise(signo);
#else
    // 仅HPEVM下支持，ehlibc.so中接口，将panic信息持久化到panic日志中。
    // 提取命令：hpecli kbox dump panic，保存路径：/opt/vrpv8/home/<USER>/hpe/panic.log
    DbReportUserException(signo);
    // 1. HPE不支持SA_ONESHOT，所以只能直接退出，不能在内部raise或者构造空指针访问异常，会导致嵌套触发信号。
    exit(1);
#endif
}

void DbSigCoredumpHandlerRsmKernel(int32_t signo)
{
    DbRsmKernelEntry(DB_RSM_KERNEL_COREDUMP_SIG);
    DbSigCoredumpHandler(signo);
}

void DbSigTerm(int32_t signo)
{
    // 信号处理函数, 不要调用复杂函数. 需要分析信号重入
    g_gmdbSvrInst.exitSignal = signo;
    g_gmdbSvrInst.status = DB_SERVER_STATUS_STOPPED;
}

void DbSigIgnoreWithLog(int32_t signo)
{
    // 信号处理函数, 不要调用复杂函数. 需要分析信号重入
    (void)DbAtomicInc(&g_gmdbSvrInst.ignoreSigCount);
}

Status DbSignalPrepareWithRsm(void)
{
    bool isUseRsm = DbCfgGetInt32Lite(DB_CFG_IS_USE_RSM, NULL) == 0 ? false : true;
    if (isUseRsm) {
        // 开启保留内存时，进入内核态，阻止再次恢复
        int32_t rsmCoreDumpSignalArray[] = {DB_SIGILL, DB_SIGTRAP, DB_SIGBUS, DB_SIGFPE, DB_SIGSEGV};
        return DbSignalArray(rsmCoreDumpSignalArray, ELEMENT_COUNT(rsmCoreDumpSignalArray),
            DbSigCoredumpHandlerRsmKernel, DB_SIG_FLAG_ONESHOT);
    }
    return GMERR_OK;
}

Status DbSignalPrepare(void)
{
    // 需要先加载HPE的相关信号接口
    Status ret = DbAdapterInit();
    if (ret != GMERR_OK) {
        DbServerPrintInitFailedWithSysError("Adapter", ret);
        return ret;
    }
    DB_BOOT_LOG_DBG_DEBUG("Aadpter initialize successfully.");

    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    DbCfgValueT cfgValue = {0};
    ret = DbCfgGet(cfgHandle, DB_CFG_ENABLE_SIGNAL_REGISTER, &cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_INVALID_PARAMETER_VALUE, "Unable to get config enableSignalRegister from config file!");
        return ret;
    }
    // 没有开启 enableSignalRegister 就不用注册信号量
    if (!(bool)cfgValue.int32Val) {
        return GMERR_OK;
    }

    /* Other non-listed signals keep system default action */
    int32_t coreDumpSignalArray[] = {DB_SIGABRT};
    int32_t termSignalArray[] = {DB_SIGINT, DB_SIGQUIT, DB_SIGTERM};
    int32_t ignoreSignalArray[] = {DB_SIGPIPE};
#ifndef LIBFUZZER_RUN
    // 开启这些信号处理打core会影响libfuzzer测试的输出，开启需要分析
    if (!DbIsHpeUm()) {  // HPEUM模式已经默认注册了，会自动打印exception信息，不能重复注册
        ret = DbSignalArray(
            coreDumpSignalArray, ELEMENT_COUNT(coreDumpSignalArray), DbSigCoredumpHandler, DB_SIG_FLAG_ONESHOT);
        if (ret != GMERR_OK) {
            DbServerPrintInitFailedWithSysError("Coredump signal", ret);
            return ret;
        }
    }
    ret = DbSignalPrepareWithRsm();
    if (ret != GMERR_OK) {
        DbServerPrintInitFailedWithSysError("rsm-Coredump signal", ret);
        return ret;
    }
#endif

    ret = DbSignalArray(termSignalArray, ELEMENT_COUNT(termSignalArray), DbSigTerm, DB_SIG_FLAG_NODEFER);
    if (ret != GMERR_OK) {
        DbServerPrintInitFailedWithSysError("Terminal signal", ret);
        return ret;
    }

    ret = DbSignalArray(ignoreSignalArray, ELEMENT_COUNT(ignoreSignalArray), DbSigIgnoreWithLog, DB_SIG_FLAG_NODEFER);
    if (ret != GMERR_OK) {
        DbServerPrintInitFailedWithSysError("Ignore signal", ret);
        return ret;
    }
    return GMERR_OK;
}

void DbServerInitFinish(GmserverStartStageTimePointT stageTime)
{
    g_gmdbSvrInst.dbStartTime = DbRdtsc();
    struct timeval timestamp;
    (void)DB_GET_TIME_OF_DAY(&timestamp, NULL);
    g_gmdbSvrInst.dbStartTimeVal = (uint64_t)timestamp.tv_sec * DEVIL_TIMELINUX_MSEC_TIME + (uint64_t)timestamp.tv_usec;
    uint64_t initiateDelay = DbToMseconds(g_gmdbSvrInst.dbStartTime - stageTime.startTime);
    g_gmdbSvrInst.initiateDelay = initiateDelay;

    uint64_t prepareTime = DbToMseconds(stageTime.prepare - stageTime.startTime);
    uint64_t initModuleTime = DbToMseconds(stageTime.initModules - stageTime.prepare);
    uint64_t importTime = DbToMseconds(stageTime.importRulesAndObjects - stageTime.initModules);
    uint64_t createPathTime = DbToMseconds(stageTime.createPath - stageTime.importRulesAndObjects);

    DB_BOOT_LOG_WARN(GMERR_OK,
        "INFO: Db server start successfully! Initiate delay: %" PRIu64 " ms. stage time (%" PRIu64 ", %" PRIu64
        ", %" PRIu64 ", %" PRIu64 ")",
        initiateDelay, prepareTime, initModuleTime, importTime, createPathTime);
    // 启动日志在DB完成启动后即失效，若扩展该函数，请保持 DbBootLogUninit 在最后一行
    DbBootLogUninit();
}

inline static void DbServerTrimMemory(uint32_t *loopCountCur)
{
    const uint32_t loopCountMax = 600;
    const uint32_t trimInterval = 30;
    uint32_t currentCount = *loopCountCur;
    if (currentCount < loopCountMax) {
        // server起来10min内每30s调用一次malloc_trim，10min后不再调用
        ++currentCount;
        if ((currentCount % trimInterval) == 0) {
            (void)DbAdptMallocTrim(0);
        }
        *loopCountCur = currentCount;
    }
}

inline static void DbServerStartLoop(void)
{
    uint32_t lastIgnoreSigCount = 0;
    uint32_t loopCountCur = 0;
    while (!DbProcessIsStopped()) {
        CliOpInfoT cliOpInfo = {.pid = DB_INVALID_UINT32, .tid = DB_INVALID_UINT32, .opCode = DB_INVALID_UINT32};
        DbPrintServiceEntryThreadRunTime("begin", "DbServerStartLoop", &cliOpInfo, NULL);
        // 信号处理可能并发, 但是原子读性能开销大, 这里只需感知变化然后打印即可, 不追求精度.
        uint32_t ignoreSigCount = g_gmdbSvrInst.ignoreSigCount;
        if (lastIgnoreSigCount != ignoreSigCount) {
            lastIgnoreSigCount = ignoreSigCount;
            DB_LOG_ERROR_SIGNAL_UNFOLD(GMERR_INTERNAL_ERROR,
                "Received ignore signal(sig=%" PRId32 ")! current count %" PRIu32 "", DB_SIGPIPE, lastIgnoreSigCount);
        }
        DbUsleep(DB_PROCESS_SLEEP_MSTIME * USECONDS_IN_MSECOND);
        DbServerTrimMemory(&loopCountCur);
        DbPrintCltKernelLog();
        DbPrintServiceEntryThreadRunTime("end", "DbServerStartLoop", &cliOpInfo, NULL);
    }
    if (g_gmdbSvrInst.exitSignal != 0) {
        DB_LOG_ERROR_SIGNAL_UNFOLD(GMERR_INTERNAL_ERROR,
            "Received termination signal (sig=%" PRId32 "), system will exit!", g_gmdbSvrInst.exitSignal);
        DbCfgValueT cfgValue;
        Status ret = DbCfgGet(DbGetCfgHandle(NULL), DB_CFG_START_UP_SHM_CLEAR, &cfgValue);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Get startup clear shm config.");
            return;
        }
        // StartUpShmClear设为2时在服务接收到异常信号退出前清理共享内存
        if (cfgValue.int32Val == 2) {
            (void)DbDestroyTopShmemCtx(DbGetProcGlobalId());
            DB_LOG_WARN(GMERR_OK, "Clear shmem finish");
        }
    } else {
        DB_LOG_WARN_UNFOLD(GMERR_INTERNAL_ERROR, "Received shutdown command, system will exit!");
        DbServerShutDown(NULL);
    }
}

void DbServerFreeFilePathMem(DbServerOptionT *dbServerOption)
{
    DB_POINTER(dbServerOption);
    if (dbServerOption->cfgFilePath != NULL) {
        DB_FREE(dbServerOption->cfgFilePath);
        dbServerOption->cfgFilePath = NULL;
    }
    if (dbServerOption->backupFilePath != NULL) {
        DB_FREE(dbServerOption->backupFilePath);
        dbServerOption->backupFilePath = NULL;
    }
    if (dbServerOption->recoveryPath != NULL) {
        DB_FREE(dbServerOption->recoveryPath);
        dbServerOption->recoveryPath = NULL;
    }
}

static Status InitConfig(const char *configFileName)
{
    DbCfgMgrT *gmdbCfgMgr = DB_MALLOC(sizeof(DbCfgMgrT));
    if (gmdbCfgMgr == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    DbCfgMgrHandleT cfgHandle = gmdbCfgMgr;
    Status ret = DbInitConfigFile(configFileName, &cfgHandle);
    if (ret != GMERR_OK) {
        DB_FREE(gmdbCfgMgr);
        return ret;
    }
    DbSetCfgHandle(cfgHandle);
    if (gmdbCfgMgr != NULL) {
        DB_FREE(gmdbCfgMgr);
    }
    return GMERR_OK;
}

void CreateGmserverPath(void)
{
#ifdef HPE
    // 为了优化server拉起流程新增方式1，待各个组件适配完成后，删除方式2
    // 方式1：server启动成功后，向notify机制通知app启动完成
    DbAdptNotifyAppReady();
    // 方式2：在hpe下server启动成功后需要在/run/verona/下创建gmserver0.pid
    // 兼容V3，启动文件命名为gmserver0.pid，0代表状态面DB。
    // instanceId>=1，默认为1。此处减1确保默认场景和多实例场景命名符合要求。
    uint16_t gmserverId = DbGetProcGlobalId() - 1u;
    char gmserverPath[DB_MAX_FILE_PATH_LEN] = {0};
    int32_t len = 0;
    if (DbCfgIsTsInstance()) {
        len = sprintf_s(gmserverPath, DB_MAX_FILE_PATH_LEN, "/run/verona/gmserver_ts%" PRIu16 ".pid", gmserverId);
    } else {
        len = sprintf_s(gmserverPath, DB_MAX_FILE_PATH_LEN, "/run/verona/gmserver%" PRIu16 ".pid", gmserverId);
    }

    if (len < 0) {
        DB_BOOT_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Unable to get file name when create gmserver path.");
        return;
    }
    int32_t fd;
    Status ret = DbOpenFile(gmserverPath, CREATE_FILE | READ_ONLY, PERM_GRPR, &fd);
    if (ret != GMERR_OK) {
        DB_BOOT_LOG_ERROR(ret, "Open gmserver path file %s unsucc, os ret no %" PRId32 ", os info %s", gmserverPath,
            DbAptGetErrno(), dlerror());
        return;
    }
    DbCloseFile(fd);

#ifdef FEATURE_RSMEM
    if (DbCfgGetBoolLite(DB_CFG_IS_USE_RSM, NULL) && !DbCommonGetIsWarmReboot()) {
        // 保留内存首次启动，创建保留内存启动文件
        const char *rsmNormalStart = "/run/verona/gmserver_normal_start.path";
        ret = DbOpenFile(rsmNormalStart, CREATE_FILE | READ_ONLY, PERM_GRPR, &fd);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Open gmserver path file %s, os ret no %" PRId32 ", os info %s", rsmNormalStart,
                DbAptGetErrno(), dlerror());
            return;
        }
        DbCloseFile(fd);
    }
#endif  // FEATURE_RSMEM
#endif  // HPE
}

static Status DbServerCheckConfictWithLocalLocator(void)
{
    DbCfgValueT locatorCfg;
    Status ret = DbCfgGet(DbGetCfgHandle(NULL), DB_CFG_LOCAL_LOCATOR, &locatorCfg);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Unable to get config for local locator.");
        return GMERR_DATA_EXCEPTION;
    }
    if (strstr(locatorCfg.str, "tcp") == NULL) {
        return GMERR_OK;
    }
    DbCfgValueT msgPoolCfg;
    ret = DbCfgGet(DbGetCfgHandle(NULL), DB_CFG_ENABLE_SHARE_MSG_POOL, &msgPoolCfg);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Unable to get config for shared msg pool.");
        return GMERR_DATA_EXCEPTION;
    }
    if (msgPoolCfg.int32Val != 0) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Unable to open shared msg pool for tcp mode.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    int32_t dwCfg = DbCfgGetDirectWriteLite();
    if (dwCfg != 0) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Unable to open direct write for tcp mode.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    return GMERR_OK;
}

static Status DbServerStartConfigInitAndCheck(DbServerOptionT *dbServerOption)
{
    // 初始化配置项
    Status ret = InitConfig(dbServerOption->cfgFilePath);
    if (ret != GMERR_OK) {
        DB_BOOT_LOG_ERROR(ret, "Configure initializes unsucc, file path %s.",
            dbServerOption->cfgFilePath == NULL ? DB_DEFAULT_CFG_PATH : dbServerOption->cfgFilePath);
        return ret;
    }
#if defined(FEATURE_TS) && !defined(FEATURE_STREAM)
    DbAdptMallocTrim(0);
#endif

    // 校验warmReboot
    bool isUseRsm = DbCfgGetInt32Lite(DB_CFG_IS_USE_RSM, NULL) == 0 ? false : true;
    if (dbServerOption->isWarmReboot) {
        if (!isUseRsm) {
            ret = GMERR_INVALID_PARAMETER_VALUE;
            DB_BOOT_LOG_ERROR(ret, "Warmreboot startup configuration file initializes unsucc.");
            return ret;
        }
        DbCommonSetWarmReboot(true);
        DbCommonSetIsWarmReboot();
    }

    // 校验保留内存大小配置是否合理
    if (isUseRsm) {
        int32_t rsmBlockMaxSize = DbCfgGetInt32Lite(DB_CFG_RSM_BLOCK_MAX_SIZE, NULL);
        int32_t deviceSize = DbCfgGetInt32Lite(DB_CFG_SE_DEV_SIZE, NULL);
        if (rsmBlockMaxSize <= 0 || rsmBlockMaxSize % deviceSize != 0) {
            ret = GMERR_INVALID_PARAMETER_VALUE;
            DB_BOOT_LOG_ERROR(ret,
                "rsm block max size initializes unsucc, rsmBlockMaxSize:%" PRId32 ", deviceSize:%" PRId32,
                rsmBlockMaxSize, deviceSize);
            return ret;
        }
    }
    return DbServerCheckConfictWithLocalLocator();
}

static Status DbServerPrepare(DbServerOptionT *dbServerOption, DbCfgValueT *locatorCfg)
{
    Status ret = DbServerStartConfigInitAndCheck(dbServerOption);
    if (ret != GMERR_OK) {
        DB_BOOT_LOG_ERROR(ret, "The configuration item is not properly set.");
        return ret;
    }
    ret = DbSignalPrepare();
    if (ret != GMERR_OK) {
        DB_BOOT_LOG_ERROR(ret, "Signal Prepare unsucc.");
        return ret;
    }
#if defined(FEATURE_TS) && !defined(FEATURE_STREAM)
    DbAdptMallocTrim(0);
#endif

#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
    if (DbCfgGet(DbGetCfgHandle(NULL), DB_CFG_FILE_LOCK_PATH, locatorCfg) == GMERR_OK) {
        if (strlen(locatorCfg->str) == 0) {
            DB_BOOT_LOG_ERROR(GMERR_CONFIG_ERROR, "config lockFilePath is empty.");
            return GMERR_CONFIG_ERROR;
        }
        DbSetFilePathLock(locatorCfg->str);
    }
#endif
    int32_t instanceId = DbCfgGetInt32Lite(DB_CFG_SE_GET_INSTANCE_ID, NULL);
    if ((ret = DbServerPrepareStart(dbServerOption, instanceId)) != GMERR_OK) {
        DB_BOOT_LOG_ERROR(ret, "Server prepares unsucc, instance id is %" PRId32, instanceId);
        return ret;
    }
    DbOpenPrintRuntime();  // 获取打印runtime开关。
    return GMERR_OK;
}

void DbServerLoop(DbServerOptionT *dbServerOption)
{
    DB_POINTER(dbServerOption);
    if (dbServerOption->isLoopInside) {
#ifndef LIBFUZZER_RUN
#ifdef FEATURE_SERVER_FUNC_REG
        DbDisableRegAdaptFuncs();
#endif
        // 因为运行libfuzzer的程序会直接调用DbServerStart，需要函数返回，所有不能有死循环
        DbServerStartLoop();
#endif
    }
}

static Status DbServerStartCompete(void)
{
    Status ret = GMERR_OK;
#ifdef FEATURE_RSMEM
    // 导入所有元数据和权限后,恢复保留内存元数据
    ret = RsmLabelRecoveryForDbStart();
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Rsm label recovery", ret, true, NULL);
        return ret;
    }
#endif

#ifdef FEATURE_REPLICATION
    DbHaNotifyInitComplete();
    if (DbHaGetRole() == DB_HA_ROLE_SLAVE) {
        ret = QryStartReplay();
    } else {
        ret = QryStartReplication();
    }
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Replication", ret, true, NULL);
        return ret;
    }
#endif

    return ret;
}

Status DbServerStartEx(DbServerOptionT *dbServerOption)
{
    DB_POINTER(dbServerOption);
    GmserverStartStageTimePointT stageTime = {0};
    stageTime.startTime = DbRdtsc();
    DbCfgValueT locatorCfg;
    Status ret = DbServerPrepare(dbServerOption, &locatorCfg);
    if (ret != GMERR_OK) {
        return ret;
    }
    stageTime.prepare = DbRdtsc();
    DbAdptPrintCpuRunTime(true, "begin", "DbServerInitModules");
    if ((ret = DbServerInitModules(dbServerOption, NULL)) != GMERR_OK) {
        DB_BOOT_LOG_ERROR(ret, "Db modules initialize unsucc.");
        return ret;
    }
    DbAdptPrintCpuRunTime(true, "end", "DbServerInitModules");
    stageTime.initModules = DbRdtsc();
    // 启动耗时主要在导表，设备上INFO级别日志被过滤，此处记录WARN日志
    DB_BOOT_LOG_WARN(GMERR_OK, "Ini mod:%" PRIu64 "ms.", DbToMseconds(stageTime.initModules - stageTime.startTime));
    if ((ret = DbServerImportRulesAndObjects()) != GMERR_OK) {
        DbServerCleanAfterInitFail("Server import rules and objs.", ret, true, NULL);
        return ret;
    }

    ret = DbServerStartCompete();
    if (ret != GMERR_OK) {
        return ret;
    }

    if ((ret = DbProcessInstanceLock()) != GMERR_OK) {
        DbServerCleanAfterInitFail("Process instance lock", ret, true, NULL);
        DbProcessInstanceUnLock();
        return ret;
    }
    stageTime.importRulesAndObjects = DbRdtsc();
    DB_BOOT_LOG_WARN(GMERR_OK, "INFO: Import rules and objects end, delay %" PRIu64 "ms.",
        DbToMseconds(stageTime.importRulesAndObjects - stageTime.startTime));
    CreateGmserverPath();
    stageTime.createPath = DbRdtsc();
    DbServerInitFinish(stageTime);
    DbServerLoop(dbServerOption);
    // 共进程场景下面非阻塞方式启动，启动成功后不解文件锁
    if (IsServerSingleProcess()) {
        DbProcessInstanceUnLock();
    }
    return GMERR_OK;
}

Status DbServerStart(DbServerOptionT *dbServerOption)
{
    DB_POINTER(dbServerOption);
    // 启动日志在DB开始启动时即生效
    Status ret = DbLogInitBootLog();
    if (ret != GMERR_OK) {
        DbServerFreeFilePathMem(dbServerOption);
        return ret;
    }
    DB_BOOT_LOG_WARN(GMERR_OK, "INFO: Db server start.");
#if defined(HPE) && !defined(HPE_SIMULATION)
    DbServerInit(DB_SERVER_PROC_TYPE_HPE);
    dbServerOption->isBackground = false;
#else
    DbServerInit(DB_SERVER_PROC_TYPE_LINUX);
#endif
    ret = DbServerStartEx(dbServerOption);
    if (ret != GMERR_OK) {
        DbServerFreeFilePathMem(dbServerOption);
        DbServerPrintToStdout("DbServer", ret, NULL);
    }
    DbServerFreeFilePathMem(dbServerOption);
    return ret;
}
#endif /* FEATURE_SIMPLEREL */

// 与MiniKv不同，由于嵌入式SQL是走的通用流程，因此需要注册DrtInstance和SessionPool.
static Status DbEmbDrtInstanceInit(bool isEmbedded, DbInstanceHdT instance)
{
#if defined(FEATURE_SQL) || defined(FEATURE_SIMPLEREL) || (defined(STREAM_EMB))
    if (isEmbedded || DbDynLoadHasFeature(COMPONENT_SQL)) {
        Status ret = RuntimeModuleInit(instance);
        if (ret != GMERR_OK) {
            return ret;
        }
        return QryInitSessionPool(instance);
    }
#endif
    return GMERR_OK;
}

static Status DbInitModules(DbServerOptionT *dbServerOption, DbInstanceHdT dbInstance)
{
    Status ret = GMERR_OK;

    ret = DbEmbDrtInstanceInit(dbServerOption->isEmbedded, dbInstance);
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Runtime instance", ret, false, dbInstance);
        return ret;
    }
    ret = DbInitStorage(dbServerOption, dbInstance);
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Storage", ret, true, dbInstance);
        return ret;
    }
#ifndef FEATURE_SIMPLEREL
    ret = CompilerInit();
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Compiler", ret, true, dbInstance);
        return ret;
    }
#endif
    ret = DataModelInit(dbInstance);
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Data model", ret, true, dbInstance);
        return ret;
    }
#ifndef FEATURE_SIMPLEREL
    ret = DbDfxRegist(DbSvGetSvInstance(dbInstance));
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Regist dfx", ret, true, dbInstance);
        return ret;
    }
#endif
    ret = DbServiceInit(dbInstance);
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Service", ret, true, dbInstance);
        return ret;
    }
#ifndef FEATURE_SIMPLEREL
    ret = ExecutorInit(dbInstance);
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Executor", ret, true, dbInstance);
        return ret;
    }
    ret = DistributionAmInit(dbInstance);
    if (ret != GMERR_OK) {
        DbServerCleanAfterInitFail("Distribution", ret, true, dbInstance);
        return ret;
    }
#endif
    return GMERR_OK;
}

static Status DbEmbeddedServerPrepare(DbServerOptionT *dbServerOption, DbInstanceHdT dbInstance)
{
    DbCommonInitCfgT cfg = {
        .env = GetAdptEnvType(),
        .configFileName = dbServerOption->cfgFilePath,
        .isBackGround = dbServerOption->isBackground,
        .openMode = dbServerOption->openMode,
    };
    Status ret = GMERR_OK;

    if (DbIsMultiInstanceEnabled()) {
        ret = DbCommonInstInit(&cfg, dbInstance, DbServerPrintToStdout);
        if (ret != GMERR_OK) {
            DbServerPrintToStdout("Common", ret, NULL);
        }
        return ret;
    }

    // 保证DbCommonInit 里的日志能打印出来
    ret = DbAdapterInit();
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DbCommonInit(&cfg, dbInstance, DbServerPrintToStdout);
    if (ret != GMERR_OK) {
        DbServerPrintToStdout("Common", ret, NULL);
        return ret;
    }

    return ret;
}

Status DbEmbeddedServerStart(DbServerOptionT *dbServerOption, DbInstanceHdT dbInstance)
{
    Status ret = DbEmbeddedServerPrepare(dbServerOption, dbInstance);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbInitModules(dbServerOption, dbInstance);
    if (ret != GMERR_OK) {
        DB_BOOT_LOG_ERROR(ret, "Db init modules unsucc.");
        return ret;
    }
    if (SeGetPersistMode() != PERSIST_OFF) {
        SysTableInit(dbInstance);
        ret = DbPersisInitByMode(dbInstance);
        if (ret != GMERR_OK) {
            DbServerCleanAfterInitFail("Recovery", ret, true, dbInstance);
            return ret;
        }
    }
    DrtInitComplete(dbInstance);
    return ret;
}
#ifdef SHUTDOWN
void DbProcessStop(void)
{
    g_gmdbSvrInst.status = DB_SERVER_STATUS_STOPPED;
}
#endif
void DbServerShutDown(DbInstanceHdT dbInstance)
{
#ifdef SHUTDOWN
    (void)DrtSetDbServerStatus((uint32_t)g_gmdbSvrInst.status);
    DrtSetAllConnUnAccess();
#endif
    CataLabelCacheUnRegTimer(dbInstance);
    DeDestroyInstance(dbInstance);
    OpHistoryMgrDestroy(dbInstance);
    (void)QryDestroyTableLoadTask(dbInstance);
    DrtInstanceDestroy(dbInstance);
    DataServiceUnInit(dbInstance);
    if (!DbIsMultiInstanceEnabled()) {
        SrvDestroyServiceMgr();
    }
    DbCloseDfgmtMgr();
    Status ret = GMERR_OK;
    uint16_t instanceId = DbGetInstanceId(dbInstance);
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(instanceId);
    if (seIns != NULL) {
        if (seIns->seConfig.redoFileDropOnClose) {
            SeSetNormalShutDown(seIns, true);
        }
        if (seIns->seConfig.shaCheckEnable) {
            CkptEnableDigestUpdate(seIns);
        }
        // 此处忽略返回值 不成功走非优雅关闭
        ret = CkptTrigger(seIns, CKPT_MODE_BOOT_FULL, true, WAIT_MSECONDS_FOR_FLUSH_IN_DESTROY);
        if (ret == GMERR_OK) {
            (void)SeSetStorageStatus(seIns, SE_ON_DISK_OFFLINE);
        }
        SeDestroyInstance(seIns->instanceId);
    }
    DbCommonFinalize(dbInstance);
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
void DbGlobalServerShutDown(DbInstanceHdT dbInstance)
{
    DrtInstanceDestroy(dbInstance);
    QryUnInitUserConnCache();
#ifndef FEATURE_SIMPLEREL
    QryUninitUserThreatStat();
    QryDestroySubsEventConfigMap();
    (void)PublicUnregisterAlarmTask();
#endif /* FEATURE_SIMPLEREL */
    SrvUnRegister(SrvGetServiceMgr(), DRT_SERVICE_STMT);
    SrvDestroyServiceMgr();
    SeDestroyGlobalResource();
    DbCommonFinalize(dbInstance);
    DbThreadResetCpuSet();
}
#endif /* FEATURE_SIMPLEREL */

#ifdef __cplusplus
}
#endif /* __cplusplus */
