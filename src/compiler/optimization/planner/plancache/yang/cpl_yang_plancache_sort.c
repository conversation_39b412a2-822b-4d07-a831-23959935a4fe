/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * @Description: Implementation for yang planner sorting
 * @Author: z<PERSON><PERSON>bin
 * @Create: 2023-11-14
 */

#include <xxh3.h>
#include "db_arena.h"
#include "db_hashmap.h"
#include "db_sysapp_context.h"
#include "dm_yang_vertex.h"
#include "cpl_yang_common.h"
#include "cpl_yang_planner.h"

#ifdef __cplusplus
extern "C" {
#endif

#define NODE_LIST_SIZE 8
#define YANG_SORT_ARENA_BLOCK_SIZE 4096

typedef struct {
    DbArenaT *arena;
    DmYangValidateTypeE type;
    DbOamapT dependency;  // planKey依赖关系，key:原node<YangNodeItemT *>, value:nodeList<DbListT <YangNodeItemT>*>
    DbOamapT inCount;     // 记录各个节点被依赖的次数 key node<YangNodeItemT *>, value:count<uint32_t>
    DbOamapT *vertexMap;
    DbOamapT *edgeMap;
} YangPlanSortCtxT;

YANG_MODEL_VALIDATE_SECTION
int32_t CompareYangNodeItem(const void *item1, const void *item2)
{
    const YangNodeItemT *nodeKey1 = item1;
    const YangNodeItemT *nodeKey2 = item2;
    int32_t result = 0;
    if (nodeKey1->vertexLabelId != nodeKey2->vertexLabelId) {
        result = nodeKey1->vertexLabelId > nodeKey2->vertexLabelId ? 1 : -1;
    } else if (nodeKey1->uniqueNodeId != nodeKey2->uniqueNodeId) {
        result = nodeKey1->uniqueNodeId > nodeKey2->uniqueNodeId ? 1 : -1;
    } else if (nodeKey1->propeId != nodeKey2->propeId) {
        result = nodeKey1->propeId > nodeKey2->propeId ? 1 : -1;
    }
    return result;
}

YANG_MODEL_VALIDATE_SECTION
Status InitPlanSortCtx(DmYangValidateTypeE type, DbOamapT *vertexMap, DbOamapT *edgeMap, YangPlanSortCtxT *ctx)
{
    Status ret;
    // memCtx用途：plan 排序保存中间结果
    // 生命周期：请求级别
    // 释放方式：兜底清空
    // 兜底清空措施：排序结束时调用ReleasePlanSortCtx释放
    DbMemCtxT *sysDynCtx = DbSrvGetSysDynCtx(DbGetProcGlobalId());
    if (sysDynCtx == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Unable to get sys dyn memctx.");  // LCOV_EXCL_LINE
        return GMERR_INTERNAL_ERROR;
    }

    ret = DbArenaCreate(sysDynCtx, "yang plan sort memCtx", YANG_SORT_ARENA_BLOCK_SIZE, &ctx->arena);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to create memctx when init yang plan sort context.");  // LCOV_EXCL_LINE
        return ret;
    }

    ret = DbOamapInit(&ctx->dependency, 1, YangNodeItemCompare, ctx->arena->memCtx, true);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to init dependency map.");  // LCOV_EXCL_LINE
        return ret;
    }

    ret = DbOamapInit(&ctx->inCount, 1, YangNodeItemCompare, ctx->arena->memCtx, true);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to init dependency map.");  // LCOV_EXCL_LINE
        return ret;
    }

    ctx->type = type;
    ctx->vertexMap = vertexMap;
    ctx->edgeMap = edgeMap;
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
void ReleasePlanSortCtx(YangPlanSortCtxT *ctx)
{
    if (ctx->arena != NULL) {
        DbArenaDelete(ctx->arena);
        ctx->arena = NULL;
    }
}

YANG_MODEL_VALIDATE_SECTION
static Status PutNewNodeListToDependency(YangPlanSortCtxT *ctx, YangNodeItemT key, DbListT **list)
{
    // 释放原则：兜底释放，排序结束后 ReleasePlanSortCtx 释放。
    YangNodeItemT *nodeKey = DbArenaAlloc(ctx->arena, sizeof(YangNodeItemT));
    if (nodeKey == NULL) {
        DB_LOG_EMRG_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to alloc node key.");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    *nodeKey = key;

    // 释放原则：兜底释放，排序结束后 ReleasePlanSortCtx 释放。
    DbListT *nodeList = DbArenaAlloc(ctx->arena, sizeof(DbListT));
    if (nodeList == NULL) {
        DB_LOG_EMRG_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to alloc node list.");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateListWithExtendSize(nodeList, sizeof(YangNodeItemT), NODE_LIST_SIZE, ctx->arena->memCtx);
    *list = nodeList;

    Status ret = DbOamapInsert(&ctx->dependency, YangNodeItemToHash32(nodeKey), nodeKey, nodeList, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_EMRG_AND_SET_LASTERR(ret, "Unable to insert dependency map.");  // LCOV_EXCL_LINE
        return ret;
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static void DebugInsertInCount(DbMemCtxT *memCtx, YangNodeItemT *itemKey, uint32_t inCount)
{
    DB_POINTER2(memCtx, itemKey);
    if (DbYangTraceLogEnable(YANG_TRACE_PLANECACHE)) {
        YANG_TRACE_LOG("Insert|update in count - count: %" PRIu32 ".", inCount);
        YangPlanKeyT passiveKey = {.labelId = itemKey->vertexLabelId,
            .uniqueNodeId = itemKey->uniqueNodeId,
            .propeId = itemKey->propeId,
            .type = (uint16_t)DM_YANG_VALIDATE_BOTTOM};
        YangTracePlanKey(memCtx, &passiveKey);
    }
}

YANG_MODEL_VALIDATE_SECTION
static Status InitInCountInfo(YangPlanSortCtxT *ctx, YangNodeItemT item)
{
    uint32_t *count = DbOamapLookup(&ctx->inCount, YangNodeItemToHash32(&item), &item, NULL);
    if (count != NULL) {
        return GMERR_OK;
    }
    // 释放原则：兜底释放，排序结束后 ReleasePlanSortCtx 释放。
    YangNodeItemT *itemKey = DbArenaAlloc(ctx->arena, sizeof(YangNodeItemT));
    if (itemKey == NULL) {
        DB_LOG_EMRG_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to alloc item key.");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    *itemKey = item;
    // 释放原则：兜底释放，排序结束后 ReleasePlanSortCtx 释放。
    uint32_t *itemCount = DbArenaAlloc(ctx->arena, sizeof(uint32_t));
    if (itemCount == NULL) {
        DB_LOG_EMRG_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to alloc item count.");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    *itemCount = 0;
    DebugInsertInCount(ctx->arena->memCtx, itemKey, *itemCount);
    Status ret = DbOamapInsert(&ctx->inCount, YangNodeItemToHash32(itemKey), itemKey, itemCount, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_EMRG_AND_SET_LASTERR(ret, "Unable to insert to in count map.");  // LCOV_EXCL_LINE
        return ret;
    }

    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status UpdateInCount(YangPlanSortCtxT *ctx, YangNodeItemT pre, YangNodeItemT cur)
{
    Status ret = InitInCountInfo(ctx, pre);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = InitInCountInfo(ctx, cur);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t *inCount = DbOamapLookup(&ctx->inCount, YangNodeItemToHash32(&cur), &cur, NULL);
    if (inCount == NULL) {
        DB_LOG_EMRG_AND_SET_LASTERR(ret, "Unable to find node item in count map.");  // LCOV_EXCL_LINE
        DB_ASSERT(inCount != NULL);
        return ret;
    }
    *inCount += 1;
    DebugInsertInCount(ctx->arena->memCtx, &cur, *inCount);
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status AddAncestralDependency2PlanNodeCache(YangPlanSortCtxT *ctx, YangNodeItemT parent, YangNodeItemT cur)
{
    DbListT *nodeList = NULL;
    YangPlanKeyT key = {
        .labelId = cur.vertexLabelId,
        .uniqueNodeId = cur.uniqueNodeId,
        .propeId = cur.propeId,
        .type = (uint16_t)ctx->type,
    };
    Status ret = YangFetchNodeListOnPlan(&key, &nodeList);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_AND_SET_LASERR(
            ret, "Add ancestral dependency: unable to fetch node list for (" PLAN_KEY_FMT ").", PLAN_KEY_VALUE(&key));
        // LCOV_EXCL_STOP
        return ret;
    }
    uint32_t cnt = DbListGetItemCnt(nodeList);
    for (uint32_t i = 0; i < cnt; i++) {
        YangPassiveInfoT *info = DbListItem(nodeList, i);
        if (YangNodeItemCompare(&info->node, &parent)) {
            return GMERR_OK;  // 确保添加到nodeList中的元素不会重复
        }
    }
    ret = DbAppendListItem(nodeList, &parent);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_AND_SET_LASERR(
            ret, "Add ancestral dependency: unable to append list for (" PLAN_KEY_FMT ").", PLAN_KEY_VALUE(&key));
        // LCOV_EXCL_STOP
        return ret;
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status AddDependency(YangPlanSortCtxT *ctx, YangNodeItemT pre, YangNodeItemT cur)
{
    if (YangNodeItemCompare(&pre, &cur) != 0) {
        return GMERR_OK;
    }
    DbOamapT *dependency = &ctx->dependency;
    Status ret = GMERR_OK;

    DbListT *nodeList = (DbListT *)DbOamapLookup(dependency, YangNodeItemToHash32(&pre), &pre, NULL);
    if (nodeList == NULL) {
        ret = PutNewNodeListToDependency(ctx, pre, &nodeList);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    ret = DbAppendListItem(nodeList, &cur);
    if (ret != GMERR_OK) {
        DB_LOG_EMRG_AND_SET_LASTERR(ret, "Unable to append list item when add dependency.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = UpdateInCount(ctx, pre, cur);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static inline void SetPropeParentNodeItem(YangNodeItemT item, YangNodeItemT *parentItem)
{
    *parentItem = (YangNodeItemT){
        .vertexLabelId = item.vertexLabelId,
        .uniqueNodeId = item.uniqueNodeId,
        .propeId = UINT32_MAX,
    };
}

YANG_MODEL_VALIDATE_SECTION
static Status SetNodeParentNodeItem(YangPlanSortCtxT *ctx, YangNodeItemT item, YangNodeItemT *parentItem)
{
    uint16_t parentId;
    DmVertexLabelT *vertexLabel = NULL;
    Status ret = GetVertexLabelFromVerifyRuleCtx(ctx->vertexMap, item.vertexLabelId, &vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Cannot get vertex label by id :  %" PRIu32 ".", item.vertexLabelId);  // LCOV_EXCL_LINE
        return ret;
    }
    const DmSchemaT *nodeSchema =
        DmGetSchemaAndParentIdByUniqueId(vertexLabel->metaVertexLabel->schema, item.uniqueNodeId, &parentId);
    if (nodeSchema == NULL) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Cannot fetch schema, curNodeId :  %" PRIu32 ".", item.uniqueNodeId);
        // LCOV_EXCL_STOP
        return GMERR_INTERNAL_ERROR;
    }
    parentItem->uniqueNodeId = (uint16_t)parentId;
    parentItem->vertexLabelId = item.vertexLabelId;
    parentItem->propeId = item.propeId;
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status SetVertexParentNodeItem(YangPlanSortCtxT *ctx, YangNodeItemT item, YangNodeItemT *parentItem)
{
    DmVertexLabelT *vertexLabel = NULL;
    Status ret = GetVertexLabelFromVerifyRuleCtx(ctx->vertexMap, item.vertexLabelId, &vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Cannot get vertex label by id :  %" PRIu32 ".", item.vertexLabelId);  // LCOV_EXCL_LINE
        return ret;
    }
    for (uint32_t i = 0; i < vertexLabel->commonInfo->edgeLabelNum; i++) {
        uint32_t edgeLabelId = vertexLabel->commonInfo->relatedEdgeLabels[i].edgeLabelId;
        DmEdgeLabelT *edgeLabel = NULL;
        ret = GetEdgeLabelFromVerifyRuleCtx(ctx->edgeMap, edgeLabelId, &edgeLabel);
        if (ret != GMERR_OK) {
            // LCOV_EXCL_START
            DB_LOG_AND_SET_LASERR(ret, "Cannot get edge label by id, id :  %" PRIu32 ".", edgeLabelId);
            // LCOV_EXCL_STOP
            return ret;
        }
        if (edgeLabel->destVertexLabelId == item.vertexLabelId) {
            parentItem->vertexLabelId = edgeLabel->sourceVertexLabelId;
            parentItem->uniqueNodeId = edgeLabel->sourceUniqueNodeId;
            parentItem->propeId = UINT32_MAX;
            return GMERR_OK;
        }
    }
    *parentItem = (YangNodeItemT){};
    return GMERR_OK;
}

static inline bool IsPropeItem(YangNodeItemT item)
{
    return item.propeId != UINT32_MAX;
}

static inline bool IsNodeItem(YangNodeItemT item)
{
    return item.propeId == UINT32_MAX && item.uniqueNodeId != DM_YANG_TREE_NODE_IS_VERTEX;
}

static inline bool IsVertexItem(YangNodeItemT item)
{
    return item.propeId == UINT32_MAX && item.uniqueNodeId == DM_YANG_TREE_NODE_IS_VERTEX;
}

YANG_MODEL_VALIDATE_SECTION
static Status SetParentNodeItem(YangPlanSortCtxT *ctx, YangNodeItemT item, YangNodeItemT *parentItem)
{
    Status ret = GMERR_OK;
    if (IsPropeItem(item)) {
        SetPropeParentNodeItem(item, parentItem);
    } else if (IsNodeItem(item)) {
        ret = SetNodeParentNodeItem(ctx, item, parentItem);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else if (IsVertexItem(item)) {
        ret = SetVertexParentNodeItem(ctx, item, parentItem);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        // LCOV_EXCL_START
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR,
            "Get unexpected yang node item, vertexLabelId = %" PRIu32 ",  uniqueNodeId = %" PRIu32
            ", propId = %" PRIu32,
            item.vertexLabelId, item.uniqueNodeId, item.propeId);
        // LCOV_EXCL_STOP
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static void DebugBuildDependency(DbMemCtxT *memCtx, YangNodeItemT cur, YangNodeItemT parent)
{
    if (!DbYangTraceLogEnable(YANG_TRACE_PLANECACHE)) {
        return;
    }
    char *curName = NULL;
    YangPlanKeyT curKey = {.labelId = cur.vertexLabelId,
        .uniqueNodeId = cur.uniqueNodeId,
        .propeId = cur.propeId,
        .type = (uint16_t)DM_YANG_VALIDATE_BOTTOM};
    Status ret = YangGetPlanName(memCtx, &curKey, &curName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to get yang passive node name for Ancestral Dependency.");  // LCOV_EXCL_LINE
        return;
    }
    YangPlanKeyT parentKey = {.labelId = parent.vertexLabelId,
        .uniqueNodeId = parent.uniqueNodeId,
        .propeId = parent.propeId,
        .type = (uint16_t)DM_YANG_VALIDATE_BOTTOM};
    char *parentName = NULL;
    ret = YangGetPlanName(memCtx, &parentKey, &parentName);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(memCtx, curName);
        DB_LOG_ERROR(ret, "Unable to get yang plan name for trace delta set.");  // LCOV_EXCL_LINE
        return;
    }
    YANG_TRACE_LOG("    (%s depend %s)", curName, parentName);
    DbDynMemCtxFree(memCtx, curName);
    DbDynMemCtxFree(memCtx, parentName);
}

YANG_MODEL_VALIDATE_SECTION
static Status BuildAncestralDependency(YangPlanSortCtxT *ctx, YangNodeItemT pre, YangNodeItemT cur)
{
    Status ret = GMERR_OK;
    YangNodeItemT parentKey = pre;
    while (true) {
        ret = SetParentNodeItem(ctx, parentKey, &parentKey);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 无父亲节点
        if (parentKey.vertexLabelId == 0) {
            break;
        }
        YANG_TRACE_MASK_LOG(YANG_TRACE_PLANECACHE, "AncestralDependency: ");
        DebugBuildDependency(ctx->arena->memCtx, cur, parentKey);
        ret = AddDependency(ctx, parentKey, cur);
        if (ret != GMERR_OK) {
            return ret;
        }
        // planCache中的planNodeCache也更新一下，方便后续视图查询
        ret = AddAncestralDependency2PlanNodeCache(ctx, parentKey, cur);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status BuildPlanDependency(YangPlanSortCtxT *ctx, YangPlanKeyT *key, YangNodeItemT cur)
{
    Status ret = GMERR_OK;

    // 对于 A节点上的 when not(B')，获取 B'
    DbListT *nodeList = NULL;
    ret = YangFetchNodeListOnPlan(key, &nodeList);
    if (ret != GMERR_OK) {
        return ret;
    }

    const uint32_t count = DbListGetItemCnt(nodeList);
    for (uint32_t i = 0; i < count; i++) {
        YangPassiveInfoT *info = DbListItem(nodeList, i);  // 循环内不可能为NULL
        YANG_TRACE_MASK_LOG(YANG_TRACE_PLANECACHE, "PlanDependency: ");
        DebugBuildDependency(ctx->arena->memCtx, cur, info->node);

        ret = AddDependency(ctx, info->node, cur);
        if (ret != GMERR_OK) {
            return ret;
        }

        ret = BuildAncestralDependency(ctx, info->node, cur);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status BuildDependency(YangPlanSortCtxT *ctx)
{
    Status ret = GMERR_OK;
    DbOamapIteratorT iter = 0;
    YangPlanKeyT *key = NULL;
    void *plan = NULL;
    // 扫描plan，并构建plan与其他plan，父亲节点的依赖关系
    while (YangPlanFetch(ctx->type, &iter, &key, &plan) == GMERR_OK) {
        YangNodeItemT keyNode = (YangNodeItemT){
            .vertexLabelId = key->labelId,
            .propeId = key->propeId,
            .uniqueNodeId = key->uniqueNodeId,
        };
        ret = InitInCountInfo(ctx, keyNode);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = BuildPlanDependency(ctx, key, keyNode);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static bool NodeHasClause(YangPlanSortCtxT *ctx, YangNodeItemT *node)
{
    DmVertexLabelT *vertexLabel = NULL;
    Status ret = GetVertexLabelFromVerifyRuleCtx(ctx->vertexMap, node->vertexLabelId, &vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to get vertex label by id when get node clause");  // LCOV_EXCL_LINE
        return false;
    }
    uint16_t parentId;
    const DmSchemaT *nodeSchema =
        DmGetSchemaAndParentIdByUniqueId(vertexLabel->metaVertexLabel->schema, node->uniqueNodeId, &parentId);
    if (nodeSchema == NULL) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Cannot fetch schema for label:%s by node, curNodeId: %" PRIu16,
            vertexLabel->metaCommon.metaName, node->uniqueNodeId);
        // LCOV_EXCL_STOP
        return false;
    }
    DmYangClauseInfoT *clauseInfo = (node->propeId == UINT32_MAX) ? nodeSchema->yangInfo->clauseInfo :
                                                                    nodeSchema->properties[node->propeId].clauseInfo;
    if (clauseInfo == NULL) {
        return false;
    }
    uint16_t count = clauseInfo->count;
    DmYangClauseT *clauses = clauseInfo->clauses;
    for (uint16_t i = 0; i < count; i++) {
        DmYangClauseT clause = clauses[i];
        if ((uint32_t)clause.type != (uint32_t)ctx->type) {
            continue;
        }
        return true;
    }
    return false;
}

YANG_MODEL_VALIDATE_SECTION
static Status AddPlanKeyToPlanListByNode(YangPlanSortCtxT *ctx, YangNodeItemT *node, DbListT *planList)
{
    if (NodeHasClause(ctx, node)) {
        YangPlanKeyPtrT item = {
            .key =
                {
                    .labelId = node->vertexLabelId,
                    .uniqueNodeId = node->uniqueNodeId,
                    .propeId = node->propeId,
                    .type = (uint16_t)ctx->type,
                },
            .plan = NULL,
        };

        Status ret = YangGetPlan(ctx->type, &item.key, &item.plan);
        if (ret == GMERR_NO_DATA) {
            // 如果获取不到 plan 说明 plan 可能被删除（成环场景），正常返回
            return GMERR_OK;
        } else if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to append get plan when add plan list.");  // LCOV_EXCL_LINE
            return ret;
        }
        YANG_TRACE_MASK_LOG(YANG_TRACE_PLANECACHE, "add to plan list");
        YangTracePlanKey(ctx->arena->memCtx, &item.key);
        ret = DbAppendListItem(planList, &item);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to append planKey to planList.");  // LCOV_EXCL_LINE
            return ret;
        }
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status PushIndependentNode(YangPlanSortCtxT *ctx, DbListT *queue, DbListT *nodeList, uint32_t *rear)
{
    Status ret = GMERR_OK;
    YangNodeItemT *nodes = DbListGetItems(nodeList);
    uint32_t count = nodeList->count;
    for (uint32_t id = 0; id < count; ++id) {
        YangNodeItemT *item = &nodes[id];
        uint32_t *in = DbOamapLookup(&ctx->inCount, YangNodeItemToHash32(item), item, NULL);
        DB_ASSERT(in != NULL && *in != 0);
        *in -= 1;
        if (*in != 0) {
            continue;
        }
        ret = DbAppendListItem(queue, item);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to yang node to queue.");  // LCOV_EXCL_LINE
            return ret;
        }
        *rear += 1;
    }
    return ret;
}

YANG_MODEL_VALIDATE_SECTION
static Status ProcessQueueLoop(YangPlanSortCtxT *ctx, DbListT *queue, DbListT *planList)
{
    YANG_TRACE_MASK_LOG(YANG_TRACE_PLANECACHE, "Process Queue Loop.");
    Status ret = GMERR_OK;
    uint32_t front = 0, rear = queue->count, levelFront = 0, levelRear = queue->count;
    while (rear - front > 0) {
        YangNodeItemT *node = DbListItem(queue, front);
        front++;

        DbListT *nodeList = DbOamapLookup(&ctx->dependency, YangNodeItemToHash32(node), node, NULL);
        if (nodeList != NULL) {
            // 对于有依赖的情况，更新目的点入边数量，如果入边为0则加入队列
            ret = PushIndependentNode(ctx, queue, nodeList, &rear);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "Unable to push QueueNode.");  // LCOV_EXCL_LINE
                return ret;
            }
        }

        if (front < levelRear) {
            continue;
        }
        ret = DbListRangeSort(queue, levelFront, levelRear - 1, CompareYangNodeItem);
        if (ret != GMERR_OK) {
            return ret;
        }
        for (uint32_t i = levelFront; i < levelRear; ++i) {
            YangNodeItemT *currNode = (YangNodeItemT *)DbListItem(queue, i);
            // 判断节点上是否有对应类型的条件，如果有则加入list
            ret = AddPlanKeyToPlanListByNode(ctx, currNode, planList);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        levelFront = front;
        levelRear = rear;
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status ProcessRemainNode(YangPlanSortCtxT *ctx, DbListT *planList)
{
    YANG_TRACE_MASK_LOG(YANG_TRACE_PLANECACHE, "Process Remain.");
    DbOamapIteratorT iter = {0};
    uint32_t *value = NULL;
    YangNodeItemT *node = NULL;
    Status ret = GMERR_OK;
    uint32_t oldCnt = DbListGetItemCnt(planList);
    while (DbOamapFetch(&ctx->inCount, &iter, (void **)&node, (void **)&value) == GMERR_OK) {
        if (*value == 0) {
            continue;
        }
        ret = AddPlanKeyToPlanListByNode(ctx, node, planList);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "Unable to append planList when ProcessRemainNode.");  // LCOV_EXCL_LINE
            return ret;
        }
    }

    uint32_t curCnt = DbListGetItemCnt(planList);
    if (curCnt > oldCnt) {
        DbListRangeSort(planList, oldCnt, curCnt - 1, CompareYangNodeItem);
    }
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to sort remainNodeList.");  // LCOV_EXCL_LINE
        return ret;
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status InitQueue(YangPlanSortCtxT *ctx, DbListT *queue)
{
    DbCreateList(queue, sizeof(YangNodeItemT), ctx->arena->memCtx);

    DbOamapIteratorT iter = {0};
    uint32_t *value = NULL;
    YangNodeItemT *key = NULL;
    Status ret = GMERR_OK;
    while (DbOamapFetch(&ctx->inCount, &iter, (void **)&key, (void **)&value) == GMERR_OK) {
        if (*value == 0) {
            ret = DbAppendListItem(queue, key);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "Unable to append node item to queue.");  // LCOV_EXCL_LINE
                return ret;
            }
        }
    }

    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status DependencyTopoSort(YangPlanSortCtxT *ctx, DbListT *planList)
{
    // 获取入边为0的节点存在队列中
    DbListT queue = {0};

    Status ret = InitQueue(ctx, &queue);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = ProcessQueueLoop(ctx, &queue, planList);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 遍历剩余的入边不为0的节点，如果有对应类型的条件，则加入list
    ret = ProcessRemainNode(ctx, planList);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status AddPlanToList(DmYangValidateTypeE type, DbListT *list)
{
    Status ret = GMERR_OK;
    uint32_t iter = 0;
    YangPlanKeyT *key = NULL;
    void *plan = NULL;
    while (YangPlanFetch(type, &iter, &key, &plan) == GMERR_OK) {
        YANG_TRACE_MASK_LOG(YANG_TRACE_PLANECACHE, "add to plan list without sort");
        YangTracePlanKey(list->memCtx, key);
        YangPlanKeyPtrT item = {.key = *key, .plan = plan};
        ret = DbAppendListItem(list, &item);
        if (ret != GMERR_OK) {
            // LCOV_EXCL_START
            DB_LOG_ERROR(ret, "Unable to append plan key to list: " PLAN_KEY_FMT, PLAN_KEY_VALUE(key));
            // LCOV_EXCL_STOP
            return ret;
        }
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static uint32_t YangGetPlanNum(DmYangValidateTypeE type)
{
    switch (type) {
        case DM_YANG_VALIDATE_WHEN:
            return YangGetWhenPlanNum();
        case DM_YANG_VALIDATE_LEAFREF:
            return YangGetLeafRefPlanNum();
        case DM_YANG_VALIDATE_NO_REQUIRED_LEAFREF:
            return YangGetNoRequiredLeafRefPlanNum();
        case DM_YANG_VALIDATE_MUST:
            return YangGetMustPlanNum();
        default:
            return 0;
    }
}

YANG_MODEL_VALIDATE_SECTION
static Status CheckSortedList(DmYangValidateTypeE type, DbListT *list)
{
    uint32_t listSize = DbListGetItemCnt(list);
    uint32_t mapSize = YangGetPlanNum(type);
    if (listSize != mapSize) {
        // LCOV_EXCL_START
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR,
            "Sorted list count does not match the plan num, type = %" PRIu32 "list count = %" PRIu32
            ", plan num = %" PRIu32 ".",
            (uint32_t)type, listSize, mapSize);
        // LCOV_EXCL_STOP
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
int32_t YangClauseCmpFunc(const void *item1, const void *item2)
{
    const YangPlanKeyT key1 = ((const YangVerifyClauseT *)item1)->key;
    const YangPlanKeyT key2 = ((const YangVerifyClauseT *)item2)->key;
    if (key1.labelId != key2.labelId) {
        return key1.labelId > key2.labelId ? 1 : -1;
    }
    if (key1.uniqueNodeId != key2.uniqueNodeId) {
        return key1.uniqueNodeId > key2.uniqueNodeId ? 1 : -1;
    }
    if (key1.propeId != key2.propeId) {
        return key1.propeId > key2.propeId ? 1 : -1;
    }
    if (key1.type != key2.type) {
        return key1.type > key2.type ? 1 : -1;
    }

    return 0;
}

YANG_MODEL_VALIDATE_SECTION
static inline Status SortMustPlan(DmYangValidateTypeE type, DbListT *list)
{
    Status ret = AddPlanToList(type, list);
    if (ret != GMERR_OK) {
        return ret;
    }
    // must语句其实不必考虑依赖关系，任意一个校验失败都会报错，因此plan的执行顺序其实不会影响校验结果的正确性
    // 这里做排序只是为了结果的稳定性，即：确保存在多个不符合条件的must语句的场景，每次校验都能在同样的位置报错
    DbListSort(list, YangClauseCmpFunc);
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
Status SortYangPlan(DmYangValidateTypeE type, DbListT *list, DbOamapT *vertexMap, DbOamapT *edgeMap)
{
    DB_POINTER(list);
    if (type == DM_YANG_VALIDATE_MUST) {
        return SortMustPlan(type, list);
    }
    YangPlanSortCtxT ctx = {0};
    Status ret = InitPlanSortCtx(type, vertexMap, edgeMap, &ctx);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = BuildDependency(&ctx);
    if (ret != GMERR_OK) {
        ReleasePlanSortCtx(&ctx);
        return ret;
    }

    ret = DependencyTopoSort(&ctx, list);
    if (ret != GMERR_OK) {
        ReleasePlanSortCtx(&ctx);
        return ret;
    }

    ret = CheckSortedList(type, list);
    if (ret != GMERR_OK) {
        ReleasePlanSortCtx(&ctx);
        return ret;
    }

    ReleasePlanSortCtx(&ctx);
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
