/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * @Description: 刷新PlanCache中的元数据
 * @Note1:
 * @Author:
 * @Create: 2024-07-19
 */
#include "db_arena.h"
#include "db_sysapp_context.h"
#include "dm_data_print.h"
#include "dm_yang_interface.h"
#include "cpl_yang_plangenerator_interface.h"
#include "cpl_yang_planner.h"
#include "cpl_yang_serialize_common.h"
#include "se_space.h"
#include "ee_plan_node.h"
#include "cpl_yang_plancache.h"

#ifdef __cplusplus
extern "C" {
#endif

YANG_MODEL_VALIDATE_SECTION
inline static Status YangRefreshPlan(YangPlanT *plan)
{
    DB_POINTER(plan);
    Status ret = GMERR_OK;
    ret = ExecRefreshPlanTreeMeta(plan->plan.planTree);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status YangRefreshPlanCacheMap(DbOamapT *map)
{
    DB_POINTER(map);
    YangPlanKeyT *key = NULL;
    YangPlanT *value = NULL;
    DbOamapIteratorT iter = 0;
    while (DbOamapFetch(map, &iter, (void **)&key, (void **)&value) == GMERR_OK) {
        Status ret = YangRefreshPlan(value);
        if (ret != GMERR_OK) {
            // LCOV_EXCL_START
            DB_LOG_ERROR(ret, "Unable to refresh yang plan, labelId: %" PRIu32 ".", key->labelId);
            // LCOV_EXCL_STOP
            return ret;
        }
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status YangRefreshMandatoryPlanCacheMap(DbOamapT *map)
{
    DB_POINTER(map);
    YangPlanKeyT *key = NULL;
    YangMandatoryCacheT *value = NULL;
    DbOamapIteratorT iter = 0;
    while (DbOamapFetch(map, &iter, (void **)&key, (void **)&value) == GMERR_OK) {
        Status ret = YangRefreshPlan(value->plan);
        if (ret != GMERR_OK) {
            // LCOV_EXCL_START
            DB_LOG_ERROR(ret, "Unable to refresh yang plan, labelId: %" PRIu32 ".", key->labelId);
            // LCOV_EXCL_STOP
            return ret;
        }
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status YangRefreshMinMaxPlanCacheMap(DbOamapT *map)
{
    DB_POINTER(map);
    YangPlanKeyT *key = NULL;
    YangMinMaxCacheT *value = NULL;
    DbOamapIteratorT iter = 0;
    while (DbOamapFetch(map, &iter, (void **)&key, (void **)&value) == GMERR_OK) {
        Status ret = YangRefreshPlan(value->plan);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status YangRefreshPassiveClauseList(DbListT *clauseList)
{
    Status ret = GMERR_OK;
    const uint32_t count = DbListGetItemCnt(clauseList);
    for (uint32_t i = 0; i < count; i++) {
        YangPassiveClauseInfoT *clauseInfo = DbListItem(clauseList, i);  // 保证i正确不判空
        if (clauseInfo->passivePlan == NULL) {
            continue;
        }
        ret = YangRefreshPlan(clauseInfo->passivePlan);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status YangRefreshPassiveClause(YangPassiveClauseT *clause)
{
    DB_POINTER(clause);
    Status ret = GMERR_OK;

    ret = YangRefreshPassiveClauseList(&clause->whenList);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = YangRefreshPassiveClauseList(&clause->leafRefList);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = YangRefreshPassiveClauseList(&clause->noRequiredLeafRefList);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = YangRefreshPassiveClauseList(&clause->mustList);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = YangRefreshPassiveClauseList(&clause->mandatoryList);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = YangRefreshPassiveClauseList(&clause->minMaxList);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status YangRefreshPassiveNodeMap(DbOamapT *map)
{
    DB_POINTER(map);
    YangPlanKeyT *key = NULL;
    YangPassiveClauseT *value = NULL;
    DbOamapIteratorT iter = 0;
    while (DbOamapFetch(map, &iter, (void **)&key, (void **)&value) == GMERR_OK) {
        Status ret = YangRefreshPassiveClause(value);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
Status RefreshYangPlanCache(YangPlanCacheT *yangPlanCache)
{
    Status ret = GMERR_OK;
    ret = YangRefreshPlanCacheMap(&yangPlanCache->whenCache);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = YangRefreshPlanCacheMap(&yangPlanCache->mustCache);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = YangRefreshPlanCacheMap(&yangPlanCache->leafRefCache);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = YangRefreshPlanCacheMap(&yangPlanCache->noRequiredLeafRefCache);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = YangRefreshMandatoryPlanCacheMap(&yangPlanCache->mandatoryCache);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = YangRefreshPlanCacheMap(&yangPlanCache->mandatoryWhenCache);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = YangRefreshMinMaxPlanCacheMap(&yangPlanCache->minMaxCache);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = YangRefreshPlanCacheMap(&yangPlanCache->minMaxWhenCache);
    if (ret != GMERR_OK) {
        return ret;
    }

    return YangRefreshPassiveNodeMap(&yangPlanCache->passiveCache);
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
