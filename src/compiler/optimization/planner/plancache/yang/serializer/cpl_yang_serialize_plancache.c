/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: serialize yang plan cache.
 * 异常分支内存统一在 ClearYangPlanCacheByNspId -> ClearYangPlanCacheByNspIdImpl 释放
 * Author:
 * Create: 2024-3-12
 */

#include "cpl_yang_serialize_common.h"

#ifdef __cplusplus
extern "C" {
#endif

// =================================================
//          获取 yangPlanCache 序列化长度
// =================================================

// 获取单条 YangPlanT 的长度
Status GetOneYangPlanSeriLen(const YangPlanT *yangPlan, uint32_t *outLen)
{
    if (yangPlan == NULL) {
        return GMERR_OK;
    }
    *outLen += (uint32_t)sizeof(uint16_t);  // totalParamNum
    *outLen += (uint32_t)sizeof(uint16_t);  // pathLen
    *outLen += (uint32_t)sizeof(uint16_t);  // currentPathId
    *outLen += (uint32_t)sizeof(uint32_t);  // PlanT树 序列化总长度
    *outLen += GetPlanTreeSeriLen(yangPlan->plan.planTree);
    return GetYangPlanKeyListLength(yangPlan->mergedList, outLen);
}

Status GetStrSeriLenByLabelId(const uint32_t *labelId, uint32_t *outLen)
{
    char *labelName = NULL;
    Status ret = GetMetaNameById(labelId, &labelName);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(
            ret, "Unable to get labelName by ID when GetStrSeriLenByLabelId, labelId = %" PRIu32 ".", *labelId);
        // LCOV_EXCL_STOP
        return ret;
    }
    uint32_t len = GetStrSeriLen(labelName);
    *outLen += len;
    return GMERR_OK;
}

Status GetYangPlanKeySeriLen(const YangPlanKeyT *planKey, uint32_t *outLen)
{
    char *labelName = NULL;
    Status ret = GetMetaNameById(&planKey->labelId, &labelName);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(
            ret, "Unable to get labelName by ID when GetYangPlanKeySeriLen, labelId = %" PRIu32 ".", planKey->labelId);
        // LCOV_EXCL_STOP
        return ret;
    }
    uint32_t len = GetStrSeriLen(labelName);
    len += (uint32_t)sizeof(uint16_t);  // uniqueNodeId
    len += (uint32_t)sizeof(uint16_t);  // DmYangValidateTypeE
    len += (uint32_t)sizeof(uint32_t);  // propeId
    *outLen += len;
    return GMERR_OK;
}

Status GetYangPlanMapLength(const DbOamapT *cacheMap, uint32_t *outLen)
{
    uint32_t len = sizeof(uint32_t);  // mapCnt
    YangPlanKeyT *planKey = NULL;
    YangPlanT *plan = NULL;
    DbOamapIteratorT iter = 0;

    while (DbOamapFetch(cacheMap, &iter, (void **)&planKey, (void **)&plan) == GMERR_OK) {
        Status ret = GetYangPlanKeySeriLen(planKey, &len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get yangPlanKey seri len when GetYangPlanMapLength.");  // LCOV_EXCL_LINE
            return ret;
        }
        len += (uint32_t)sizeof(uint32_t);  // PlanT buf 长度
        ret = GetOneYangPlanSeriLen(plan, &len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get yangPlan seri len when GetYangPlanMapLength.");  // LCOV_EXCL_LINE
            return ret;
        }
    }
    *outLen += len;
    return GMERR_OK;
}

Status GetYangMinMaxCacheLength(const DbOamapT *cacheMap, uint32_t *outLen)
{
    uint32_t len = sizeof(uint32_t);  // mapCnt
    YangPlanKeyT *planKey = NULL;
    YangMinMaxCacheT *minMaxCache = NULL;
    DbOamapIteratorT iter = 0;

    while (DbOamapFetch(cacheMap, &iter, (void **)&planKey, (void **)&minMaxCache) == GMERR_OK) {
        Status ret = GetYangPlanKeySeriLen(planKey, &len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get yangPlanKey seri len when Get minMaxCacheLength.");  // LCOV_EXCL_LINE
            return ret;
        }
        len += (uint32_t)sizeof(uint32_t);  // itemListCnt 长度
        len += (uint32_t)sizeof(uint32_t);  // PlanT buf 长度
        ret = GetOneYangPlanSeriLen(minMaxCache->plan, &len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get yangPlan seri len when Get minMaxCacheLength.");  // LCOV_EXCL_LINE
            return ret;
        }
        uint32_t itemCnt = DbListGetItemCnt(&minMaxCache->itemList);
        for (uint32_t i = 0; i < itemCnt; ++i) {
            YangMinMaxItemT *item = (YangMinMaxItemT *)DbListItem(&minMaxCache->itemList, i);
            ret = GetVLIdSeriLen(&item->vertexLabelId, &len);
            if (ret != GMERR_OK) {
                return ret;
            }
            ret = GetELIdSeriLen(&item->edgeLabelId, &len);
            if (ret != GMERR_OK) {
                return ret;
            }
            len += sizeof(uint16_t);  // parentNodeId
        }
    }
    *outLen += len;
    return GMERR_OK;
}

Status GetMandatoryCacheLength(const DbOamapT *cacheMap, uint32_t *outLen)
{
    uint32_t len = sizeof(uint32_t);  // mapCnt
    YangPlanKeyT *planKey = NULL;
    YangMandatoryCacheT *mandatoryCache = NULL;
    DbOamapIteratorT iter = 0;

    while (DbOamapFetch(cacheMap, &iter, (void **)&planKey, (void **)&mandatoryCache) == GMERR_OK) {
        Status ret = GetYangPlanKeySeriLen(planKey, &len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get yangPlanKey seri len when GetMandatoryCacheLength.");  // LCOV_EXCL_LINE
            return ret;
        }
        len += (uint32_t)sizeof(uint32_t);  // itemListCnt 长度
        len += (uint32_t)sizeof(uint32_t);  // PlanT buf 长度
        ret = GetOneYangPlanSeriLen(mandatoryCache->plan, &len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get yangPlan seri len when GetMandatoryCacheLength.");  // LCOV_EXCL_LINE
            return ret;
        }
        uint32_t itemCnt = DbListGetItemCnt(&mandatoryCache->itemList);
        len += itemCnt * (sizeof(uint16_t) + sizeof(uint32_t));
    }
    *outLen += len;
    return GMERR_OK;
}

Status GetYangNodeItemLength(const YangNodeItemT *nodeItem, uint32_t *outLen)
{
    uint32_t len = 0;
    Status ret = GetStrSeriLenByLabelId(&nodeItem->vertexLabelId, &len);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to get metaname by ID when GetYangNodeItemLength.");  // LCOV_EXCL_LINE
        return ret;
    }
    len += (uint32_t)sizeof(nodeItem->uniqueNodeId);
    len += (uint32_t)sizeof(nodeItem->propeId);
    *outLen += len;
    return GMERR_OK;
}

Status GetYangPassiveListLength(const DbListT *list, uint32_t *outLen)
{
    if (list == NULL) {
        *outLen += (uint32_t)sizeof(uint32_t);  // listCnt = 0
        return GMERR_OK;
    }
    uint32_t listSize = DbListGetItemCnt(list);
    uint32_t len = sizeof(uint32_t);  // listCnt
    for (uint32_t i = 0; i < listSize; ++i) {
        YangPassiveClauseInfoT *ypClauseInfo = (YangPassiveClauseInfoT *)DbListItem(list, i);
        Status ret = GetYangPlanKeySeriLen(&ypClauseInfo->planKey, &len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get yangPlanKey seri len when GetYangPassiveListLength.");  // LCOV_EXCL_LINE
            return ret;
        }
        len += (uint32_t)sizeof(ypClauseInfo->type);
        len += (uint32_t)sizeof(ypClauseInfo->parentLevel);
        len += (uint32_t)sizeof(ypClauseInfo->filterPropeId);
        len += (uint32_t)sizeof(ypClauseInfo->filterNodeId);
        len += (uint32_t)sizeof(uint32_t);  // 0 表示yangPlan为空
        ret = GetOneYangPlanSeriLen(ypClauseInfo->passivePlan, &len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get yangPlan seri len when GetYangPassiveListLength.");  // LCOV_EXCL_LINE
            return ret;
        }
    }
    *outLen += len;
    return GMERR_OK;
}

Status GetYangPassiveCacheLength(const DbOamapT *passiveCache, uint32_t *outLen)
{
    Status ret = GMERR_OK;
    uint32_t len = sizeof(uint32_t);  // mapCnt
    YangNodeItemT *nodeItem = NULL;
    YangPassiveClauseT *passiveClause = NULL;
    DbOamapIteratorT iter = 0;

    while (DbOamapFetch(passiveCache, &iter, (void **)&nodeItem, (void **)&passiveClause) == GMERR_OK) {
        ret = GetYangNodeItemLength(nodeItem, &len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get yangNodeItem seri len when GetYangPassiveCacheLength.");
            return ret;
        }
        ret = GetYangPassiveListLength(&passiveClause->whenList, &len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get whenList seri len when GetYangPassiveCacheLength.");  // LCOV_EXCL_LINE
            return ret;
        }
        ret = GetYangPassiveListLength(&passiveClause->mustList, &len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get mustList seri len when GetYangPassiveCacheLength.");  // LCOV_EXCL_LINE
            return ret;
        }
        ret = GetYangPassiveListLength(&passiveClause->leafRefList, &len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get leafrefList seri len when GetYangPassiveCacheLength.");
            return ret;
        }
        ret = GetYangPassiveListLength(&passiveClause->noRequiredLeafRefList, &len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get noRequiredLeafRefList seri len when GetYangPassiveCacheLength.");
            return ret;
        }
        ret = GetYangPassiveListLength(&passiveClause->mandatoryList, &len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get mandatoryList seri len when GetYangPassiveCacheLength.");
            return ret;
        }
        ret = GetYangPassiveListLength(&passiveClause->minMaxList, &len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get minMaxList seri len when GetYangPassiveCacheLength.");  // LCOV_EXCL_LINE
            return ret;
        }
    }
    *outLen += len;
    return GMERR_OK;
}

Status GetYangPlanNodeCacheLength(const DbOamapT *planNodeCache, uint32_t *outLen)
{
    Status ret = GMERR_OK;
    uint32_t len = (uint32_t)sizeof(uint32_t);  // mapCnt

    YangPlanKeyT *planKey = NULL;
    DbListT *nodeList = NULL;
    DbOamapIteratorT iter = 0;
    while (DbOamapFetch(planNodeCache, &iter, (void **)&planKey, (void **)&nodeList) == GMERR_OK) {
        ret = GetYangPlanKeySeriLen(planKey, &len);
        if (ret != GMERR_OK) {
            // LCOV_EXCL_START
            DB_LOG_ERROR(ret, "Unable to get yangPlanKey seri len when GetYangPlanNodeCacheLength.");
            // LCOV_EXCL_STOP
            return ret;
        }
        len += (uint32_t)sizeof(uint32_t);  // nodeListCnt
        for (uint32_t i = 0; i < DbListGetItemCnt(nodeList); ++i) {
            YangPassiveInfoT *passiveInfo = (YangPassiveInfoT *)DbListItem(nodeList, i);
            ret = GetYangNodeItemLength(&passiveInfo->node, &len);
            if (ret != GMERR_OK) {
                // LCOV_EXCL_STARTs
                DB_LOG_ERROR(ret, "Unable to get yangNodeItem seri len when GetYangPlanNodeCacheLength.");
                // LCOV_EXCL_STOP
                return ret;
            }
            len += (uint32_t)sizeof(uint32_t);  // commonFatherLevel
        }
    }
    *outLen += len;
    return GMERR_OK;
}

Status GetYangPlanKeyListLength(const DbListT *list, uint32_t *outLen)
{
    uint32_t len = sizeof(uint32_t);
    if (list == NULL) {
        *outLen += len;
        return GMERR_OK;
    }
    uint32_t listSize = DbListGetItemCnt(list);
    for (uint32_t i = 0; i < listSize; ++i) {
        YangPlanKeyT *planKey = (YangPlanKeyT *)DbListItem(list, i);
        Status ret = GetYangPlanKeySeriLen(planKey, &len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get yangPlanKey seri len when GetYangPlanKeyListLength.");  // LCOV_EXCL_LINE
            return ret;
        }
    }
    *outLen += len;
    return GMERR_OK;
}

uint32_t GetYangVariablesListLength(const YangVariablesListT *variables)
{
    uint32_t listLen = variables->num;
    uint32_t len = sizeof(uint32_t);
    for (uint32_t i = 0; i < listLen; ++i) {
        len += GetStrSeriLen(variables->variableNames[i]);
    }
    return len;
}

// =================================================
//               序列化 yangPlanCache
// =================================================

Status SeriYangPassiveList(const DbListT *list, uint8_t **bufCursor)
{
    uint32_t listSize = 0;
    if (list == NULL) {
        SeriUint32(&listSize, bufCursor);
        return GMERR_OK;
    }

    listSize = DbListGetItemCnt(list);
    SeriUint32(&listSize, bufCursor);
    for (uint32_t i = 0; i < listSize; ++i) {
        YangPassiveClauseInfoT *pcInfo = (YangPassiveClauseInfoT *)DbListItem(list, i);
        Status ret = SeriYangPlanKey(&pcInfo->planKey, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri yangPlanKey when SeriYangPassiveList.");  // LCOV_EXCL_LINE
            return ret;
        }
        **bufCursor = (uint8_t)pcInfo->type;
        *bufCursor += sizeof(DmYangValidateTypeE);
        SeriUint16(&pcInfo->parentLevel, bufCursor);
        SeriUint32(&pcInfo->filterPropeId, bufCursor);
        SeriUint16(&pcInfo->filterNodeId, bufCursor);
        uint32_t passivePlanLen = 0;
        ret = GetOneYangPlanSeriLen(pcInfo->passivePlan, &passivePlanLen);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get yangPlan len when SeriYangPassiveList.");  // LCOV_EXCL_LINE
            return ret;
        }
        SeriUint32(&passivePlanLen, bufCursor);
        if (passivePlanLen > 0) {
            ret = SeriYangPlan(pcInfo->passivePlan, bufCursor);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "Unable to seri yang plan when SeriYangPassiveList.");  // LCOV_EXCL_LINE
                return ret;
            }
        }
    }
    return GMERR_OK;
}

Status SeriYangPlanNodeList(const DbListT *list, uint8_t **bufCursor)
{
    uint32_t listSize = DbListGetItemCnt(list);
    SeriUint32(&listSize, bufCursor);
    for (uint32_t i = 0; i < listSize; ++i) {
        YangPassiveInfoT *passiveInfo = (YangPassiveInfoT *)DbListItem(list, i);
        Status ret = SeriYangNodeItem(&passiveInfo->node, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri yangNodeItem when SeriYangPlanNodeList.");  // LCOV_EXCL_LINE
            return ret;
        }
        SeriUint32(&passiveInfo->commonFatherLevel, bufCursor);
    }
    return GMERR_OK;
}

Status SeriPlanKeyList(const DbListT *list, uint8_t **bufCursor)
{
    uint32_t listSize = list == NULL ? 0 : DbListGetItemCnt(list);
    SeriUint32(&listSize, bufCursor);
    if (listSize == 0) {
        return GMERR_OK;
    }
    for (uint32_t i = 0; i < listSize; ++i) {
        YangPlanKeyT *planKey = (YangPlanKeyT *)DbListItem(list, i);
        Status ret = SeriYangPlanKey(planKey, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri yangPlanKey when SeriPlanKeyList.");  // LCOV_EXCL_LINE
            return ret;
        }
    }
    return GMERR_OK;
}

Status SeriYangVariablesList(const YangVariablesListT *varList, uint8_t **bufCursor)
{
    // | num | len | char* | ...
    uint32_t listLen = varList->num;
    SeriUint32(&listLen, bufCursor);
    for (uint32_t i = 0; i < listLen; ++i) {
        SeriStrToBuf(varList->variableNames[i], bufCursor);
    }
    return GMERR_OK;
}

// 函数序列化的 map: when must leafref mandatorywhen
// 序列化格式:| mapCnt | YangPlanKeyT( nameLen | metaname | uniqueNodeId | propeId) | YangPlanT ( yangPlanBufLen |
// yangPlan)
Status SeriYangPlanMap(const DbOamapT *planMap, uint8_t **bufCursor)
{
    Status ret = GMERR_OK;
    YangPlanKeyT *planKey = NULL;
    YangPlanT *plan = NULL;
    DbOamapIteratorT iter = 0;

    uint32_t planSize = DbOamapUsedSize(planMap);
    SeriUint32(&planSize, bufCursor);
    while (DbOamapFetch(planMap, &iter, (void **)&planKey, (void **)&plan) == GMERR_OK) {
        ret = SeriYangPlanKey(planKey, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri yangPlanKey when SeriYangPlanMap.");  // LCOV_EXCL_LINE
            return ret;
        }
        // 序列化单条 yangPlan
        uint32_t yangPlanLen = 0;
        ret = GetOneYangPlanSeriLen(plan, &yangPlanLen);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get yangPlan len when SeriYangPlanMap.");  // LCOV_EXCL_LINE
            return ret;
        }
        SeriUint32(&yangPlanLen, bufCursor);

        ret = SeriYangPlan(plan, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri yang plan when SeriYangPlanMap.");  // LCOV_EXCL_LINE
            return ret;
        }
    }
    return GMERR_OK;
}

// 序列化格式: | mapCnt | plankey | YangMinMaxCacheT ( listLen | YangMinMaxItemT(vertexLabelId
// | parentNodeId | min | max ) | yangPlanBufLen | yangPlan)
Status SeriYangMinMaxCache(const DbOamapT *planMap, uint8_t **bufCursor)
{
    Status ret = GMERR_OK;
    YangPlanKeyT *planKey = NULL;
    YangMinMaxCacheT *minMaxCache = NULL;
    DbOamapIteratorT iter = 0;
    uint32_t planSize = DbOamapUsedSize(planMap);
    SeriUint32(&planSize, bufCursor);
    while (DbOamapFetch(planMap, &iter, (void **)&planKey, (void **)&minMaxCache) == GMERR_OK) {
        // 序列化 planKey
        ret = SeriYangPlanKey(planKey, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri yangPlanKey when SeriminMaxCache.");  // LCOV_EXCL_LINE
            return ret;
        }

        uint32_t itemListCnt = DbListGetItemCnt(&minMaxCache->itemList);
        SeriUint32(&itemListCnt, bufCursor);
        for (uint32_t i = 0; i < itemListCnt; ++i) {
            YangMinMaxItemT *item = DbListItem(&minMaxCache->itemList, i);
            ret = SeriLabelId(&item->vertexLabelId, bufCursor);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "Seri Label id when SeriminMaxCache.");  // LCOV_EXCL_LINE
                return ret;
            }
            ret = SeriEdgeLabelId(&item->edgeLabelId, bufCursor);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "Seri edge id when SeriminMaxCache.");  // LCOV_EXCL_LINE
                return ret;
            }
            SeriUint16(&item->parentNodeId, bufCursor);
        }

        uint32_t yangPlanLen = 0;
        ret = GetOneYangPlanSeriLen(minMaxCache->plan, &yangPlanLen);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get yang plan len when SeriminMaxCache.");  // LCOV_EXCL_LINE
            return ret;
        }
        SeriUint32(&yangPlanLen, bufCursor);
        ret = SeriYangPlan(minMaxCache->plan, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri yang plan when SeriminMaxCache.");  // LCOV_EXCL_LINE
            return ret;
        }
    }
    return GMERR_OK;
}

// 序列化格式: | mapCnt | plankey | YangMandatoryCacheT ( listLen | YangMandatoryItemT(uniqueNodeId
// | propeId ) | yangPlanBufLen | yangPlan)
Status SeriMandatoryCache(const DbOamapT *planMap, uint8_t **bufCursor)
{
    Status ret = GMERR_OK;
    YangPlanKeyT *planKey = NULL;
    YangMandatoryCacheT *mandatoryCache = NULL;
    DbOamapIteratorT iter = 0;
    uint32_t planSize = DbOamapUsedSize(planMap);
    SeriUint32(&planSize, bufCursor);
    while (DbOamapFetch(planMap, &iter, (void **)&planKey, (void **)&mandatoryCache) == GMERR_OK) {
        // 序列化 planKey
        ret = SeriYangPlanKey(planKey, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri yangPlanKey when SeriMandatoryCache.");  // LCOV_EXCL_LINE
            return ret;
        }

        uint32_t itemListCnt = DbListGetItemCnt(&mandatoryCache->itemList);
        SeriUint32(&itemListCnt, bufCursor);
        for (uint32_t i = 0; i < itemListCnt; ++i) {
            YangMandatoryItemT *item = DbListItem(&mandatoryCache->itemList, i);
            SeriUint16(&item->uniqueNodeId, bufCursor);
            SeriUint32(&item->propeId, bufCursor);
        }

        uint32_t yangPlanLen = 0;
        ret = GetOneYangPlanSeriLen(mandatoryCache->plan, &yangPlanLen);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get yang plan len when SeriMandatoryCache.");  // LCOV_EXCL_LINE
            return ret;
        }
        SeriUint32(&yangPlanLen, bufCursor);
        ret = SeriYangPlan(mandatoryCache->plan, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri yang plan when SeriMandatoryCache.");  // LCOV_EXCL_LINE
            return ret;
        }
    }
    return GMERR_OK;
}

// 序列化格式: | mapCnt | YangNodeItemT( nameLen | metaname | uniqueNodeId | propeId) | YangPassiveClauseT ( whenList
// Len | plankey( nameLen | metaname | uniqueNodeId | propeId) | type | parentLevel) | mustList Len|...
Status SeriYangPassiveCache(const DbOamapT *passiveCache, uint8_t **bufCursor)
{
    Status ret = GMERR_OK;
    YangNodeItemT *nodeItem = NULL;
    YangPassiveClauseT *passiveClause = NULL;
    DbOamapIteratorT iter = 0;
    uint32_t planSize = DbOamapUsedSize(passiveCache);
    SeriUint32(&planSize, bufCursor);
    while (DbOamapFetch(passiveCache, &iter, (void **)&nodeItem, (void **)&passiveClause) == GMERR_OK) {
        ret = SeriYangNodeItem(nodeItem, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri yangNodeItem when SeriYangPassiveCache.");  // LCOV_EXCL_LINE
            return ret;
        }
        // YangPassiveClauseT
        ret = SeriYangPassiveList(&passiveClause->whenList, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri passive whenList when SeriYangPassiveCache.");  // LCOV_EXCL_LINE
            return ret;
        }
        ret = SeriYangPassiveList(&passiveClause->mustList, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri passive mustList when SeriYangPassiveCache.");  // LCOV_EXCL_LINE
            return ret;
        }
        ret = SeriYangPassiveList(&passiveClause->leafRefList, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri passive leafrefList when SeriYangPassiveCache.");  // LCOV_EXCL_LINE
            return ret;
        }
        ret = SeriYangPassiveList(&passiveClause->noRequiredLeafRefList, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri passive leafrefList when SeriYangPassiveCache.");  // LCOV_EXCL_LINE
            return ret;
        }
        ret = SeriYangPassiveList(&passiveClause->mandatoryList, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri passive mandatoryList when SeriYangPassiveCache.");  // LCOV_EXCL_LINE
            return ret;
        }
        ret = SeriYangPassiveList(&passiveClause->minMaxList, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri passive minMaxList when SeriYangPassiveCache.");  // LCOV_EXCL_LINE
            return ret;
        }
    }
    return GMERR_OK;
}

// 序列化格式: | mapCnt | YangPlanKeyT( nameLen | metaname | uniqueNodeId | propeId ) | YangPassiveInfoT (
// YangNodeItemT( nameLen | metaname | uniqueNodeId | propeId) | commonFatherLevel) | ...
Status SeriYangPlanNodeCache(const DbOamapT *planNodeCache, uint8_t **bufCursor)
{
    Status ret = GMERR_OK;
    YangPlanKeyT *planKey = NULL;
    DbListT *nodeList = NULL;
    DbOamapIteratorT iter = 0;
    uint32_t planSize = DbOamapUsedSize(planNodeCache);
    SeriUint32(&planSize, bufCursor);
    while (DbOamapFetch(planNodeCache, &iter, (void **)&planKey, (void **)&nodeList) == GMERR_OK) {
        ret = SeriYangPlanKey(planKey, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri yangPlanKey when SeriYangPlanNodeCache.");  // LCOV_EXCL_LINE
            return ret;
        }
        ret = SeriYangPlanNodeList(nodeList, bufCursor);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to seri plan node list when SeriYangPlanNodeCache.");  // LCOV_EXCL_LINE
            return ret;
        }
    }
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
