/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: deserialize yangplan tree.
 * 异常分支内存统一在 ClearYangPlanCacheByNspId -> ClearYangPlanCacheByNspIdImpl 释放
 * Author:
 * Create: 2024-3-12
 */

#include "cpl_yang_serialize_common.h"

#ifdef __cplusplus
extern "C" {
#endif

// ============================================
//             yangplan buf check
// ============================================
Status DeseriYangPlanTreeCheck(uint8_t **bufCursor, uint8_t *endCursor)
{
    Status ret = DeseriUint32Check(bufCursor, endCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Deserialize check yangPlan tree buf length unsucc.");  // LCOV_EXCL_LINE
        return ret;
    }
    uint32_t planNodeLen = 0;
    DeseriUint32(&planNodeLen, bufCursor);
    if (planNodeLen == 0) {
        return GMERR_OK;
    }
    ret = DeseriCheckWithLength(bufCursor, endCursor, sizeof(NodeTagT));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Deserialize check yangPlan tree node tag unsucc.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = DeseriYangPlanNodeCheck(bufCursor, endCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Deserialize check yangPlan node unsucc.");  // LCOV_EXCL_LINE
        return ret;
    }
    // 左右子节点
    ret = DeseriYangPlanTreeCheck(bufCursor, endCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Deserialize check yangPlan tree left child unsucc.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = DeseriYangPlanTreeCheck(bufCursor, endCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Deserialize check yangPlan tree right child unsucc.");  // LCOV_EXCL_LINE
        return ret;
    }
    return GMERR_OK;
}

Status DeseriOneYangPlanCheck(uint8_t **bufCursor, uint8_t *endCursor)
{
    Status ret = DeseriUint16MoveCheck(bufCursor, endCursor);  // totalParamNum
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Deserialize check YangPlan totalParamNum unsucc.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = DeseriUint16MoveCheck(bufCursor, endCursor);  // pathLen
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Deserialize check YangPlan pathLen unsucc.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = DeseriUint16MoveCheck(bufCursor, endCursor);  // currentPathId
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Deserialize check YangPlan currentPathId unsucc.");  // LCOV_EXCL_LINE
        return ret;
    }

    // 获取单个 planTree 总长度
    ret = DeseriUint32Check(bufCursor, endCursor);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t planTreeBufLen = 0;
    DeseriUint32(&planTreeBufLen, bufCursor);
    ret = DeseriCheckWithLength(bufCursor, endCursor, planTreeBufLen);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(
            ret, "Deserialize check yangplan TreeBufLen unsucc, planTreeBufLen is %" PRIu32 ".", planTreeBufLen);
        // LCOV_EXCL_STOP
        return ret;
    }
    ret = DeseriYangPlanTreeCheck(bufCursor, endCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Deserialize check yangplan tree unsucc.");  // LCOV_EXCL_LINE
        return ret;
    }
    // mergeList
    return DeseriPlanKeyListCheck(bufCursor, endCursor);
}

Status DeseriYangPlanCheck(uint8_t **bufCursor, uint8_t *endCursor)
{
    // 获取单条 yang plan 总长度
    Status ret = DeseriUint32Check(bufCursor, endCursor);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "Deserialize check yangplan BufLen length(uint32_t) unsucc when DeseriYangPlanCheck.");
        // LCOV_EXCL_STOP
        return ret;
    }
    uint32_t yangPlanBufLen = 0;
    DeseriUint32(&yangPlanBufLen, bufCursor);
    if (yangPlanBufLen == 0) {
        return GMERR_OK;
    }
    ret = DeseriCheckWithLength(bufCursor, endCursor, yangPlanBufLen);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "Deserialize check yangPlanBufLen unsucc,len = %" PRIu32 ".", yangPlanBufLen);
        // LCOV_EXCL_STOP
        return ret;
    }
    ret = DeseriOneYangPlanCheck(bufCursor, endCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Deserialize check YangPlan unsucc when DeseriYangPlanCheck.");  // LCOV_EXCL_LINE
        return ret;
    }

    return GMERR_OK;
}

// ===============================================
//               反序列化yangplan
// ===============================================
Status DeseriYangPlanKeyList(PlanCacheDeseriCtxT *deseriCtx, DbListT **planKeyList, uint8_t **bufCursor)
{
    uint32_t listCnt = 0;
    DeseriUint32(&listCnt, bufCursor);
    if (listCnt == 0) {
        return GMERR_OK;
    }

    // 内部分配内存
    DbListT *keyList = *planKeyList;
    if (keyList == NULL) {
        keyList = DbDynMemCtxAlloc(deseriCtx->memCtx, sizeof(DbListT));
        if (keyList == NULL) {
            // LCOV_EXCL_START
            DB_LOG_AND_SET_LASERR(
                GMERR_OUT_OF_MEMORY, "Unable to allocate memory for planKeyList when deseri yangPlanKeyList.");
            // LCOV_EXCL_STOP
            return GMERR_OUT_OF_MEMORY;
        }
        DbCreateList(keyList, sizeof(YangPlanKeyT), deseriCtx->memCtx);
    }

    for (uint32_t i = 0; i < listCnt; ++i) {
        YangPlanKeyT yangPlanKey = (YangPlanKeyT){};
        Status ret = DeseriYangPlanKey(deseriCtx, &yangPlanKey, bufCursor);
        if (ret != GMERR_OK) {
            // LCOV_EXCL_START
            DB_LOG_ERROR(ret,
                "Unable to deserialize planKeyList's YangPlanKey when deseri yangPlanKeyList, list index is "
                "(%" PRIu32 ", %" PRIu32 ").",
                i, listCnt);
            // LCOV_EXCL_STOP
            return ret;
        }
        ret = DbAppendListItem(keyList, &yangPlanKey);
        if (ret != GMERR_OK) {
            // LCOV_EXCL_START
            DB_LOG_AND_SET_LASERR(ret,
                "Unable to append yangPlanKey to planKeyList when deseri yangPlanKeyList, list index is "
                "(%" PRIu32 ", %" PRIu32 ").",
                i, listCnt);
            // LCOV_EXCL_STOP
            return ret;
        }
    }
    *planKeyList = keyList;
    return GMERR_OK;
}

Status DeseriOnePlan(PlanCacheDeseriCtxT *deseriCtx, PlanT **plan, uint8_t **bufCursor)
{
    PlanT *planNode = NULL;
    uint32_t planNodeLen = 0;
    DeseriUint32(&planNodeLen, bufCursor);
    if (planNodeLen == 0) {
        *plan = planNode;
        return GMERR_OK;
    }
    Status ret = DeseriOnePlanNode(deseriCtx, &planNode, bufCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Deserialize YangPlan node unsucc when DeseriOnePlan.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = DeseriOnePlan(deseriCtx, &planNode->leftTree, bufCursor);  // 递归左孩子
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Deserialize YangPlan left child node unsucc when DeseriOnePlan.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = DeseriOnePlan(deseriCtx, &planNode->rightTree, bufCursor);  // 递归反序列化右孩子
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Deserialize YangPlan right child node unsucc when DeseriOnePlan.");  // LCOV_EXCL_LINE
        return ret;
    }
    *plan = planNode;
    return GMERR_OK;
}

Status DeseriYangPlan(PlanCacheDeseriCtxT *deseriCtx, YangPlanT *yangPlan, uint8_t **bufCursor)
{
    DeseriUint16(&yangPlan->plan.totalParamNum, bufCursor);
    DeseriUint16(&yangPlan->plan.pathLen, bufCursor);
    DeseriUint16(&yangPlan->plan.currentPathId, bufCursor);

    uint32_t planBufferLen = 0;
    DeseriUint32(&planBufferLen, bufCursor);
    if (planBufferLen == 0) {
        return GMERR_OK;
    }
    Status ret = DeseriOnePlan(deseriCtx, &yangPlan->plan.planTree, bufCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Deserialize YangPlan plantree unsucc.");  // LCOV_EXCL_LINE
        return ret;
    }

    // 反序列化 mergeList
    return DeseriYangPlanKeyList(deseriCtx, &yangPlan->mergedList, bufCursor);
}

#ifdef __cplusplus
}
#endif
