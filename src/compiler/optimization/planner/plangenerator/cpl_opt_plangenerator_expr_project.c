/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:  Implementation for converting physical operator ExprProject into exec operator
 * Author: zhuangyifeng
 * Create: 2024-03-04
 */

#if defined(FEATURE_TS) || defined(FEATURE_SQL) || defined(FEATURE_STREAM)

#include "cpl_opt_plangenerator.h"
#include "cpl_opt_plangenerator_common.h"
#ifdef FEATURE_STREAM
#include "ee_plan_node_stream.h"
#endif

static ExprT **GetProjectExprFromPlan(PlanT *plan)
{
    switch (plan->tagType) {
        case T_SEQ_SCAN:
        case T_INDEX_SCAN:
            return &((ScanT *)(void *)plan)->projectionExpr;
#ifdef FEATURE_SQL
        case T_VECTOR_INDEX_SCAN:
            return &((ScanT *)(void *)plan)->projectionExpr;
        case T_NEST_LOOP:
            return &((NestLoopT *)(void *)plan)->join.projectionExpr;
        case T_LIMIT:
            return &((LimitT *)(void *)plan)->projectionExpr;
#ifndef IDS_HAOTIAN
        case T_UNION:
            return &((UnionT *)(void *)plan)->base.projectionExpr;
        case T_SUBQUERY_SCAN:
            return &((SubQueryScanT *)(void *)plan)->projectionExpr;
#endif
#endif
        case T_EXTEND_SORT:
            return &((SortT *)(void *)plan)->projectionExpr;
#if defined(FEATURE_TS) || defined(FEATURE_SQL)
        case T_BUILTIN:
            return &((BuiltInT *)(void *)plan)->projectExpr;
        case T_HASH_AGGREGATE:
            return &((HashAggT *)(void *)plan)->projectionExpr;
#endif
#ifdef FEATURE_TS
        case T_TIME_PARTITION_SCAN:
            return &((TimePartitionScanT *)(void *)plan)->scan.projectionExpr;
#endif
#ifdef FEATURE_STREAM
        case T_STREAMSCAN:
            return &((StreamScanT *)(void *)plan)->scan.projectionExpr;
        case T_SEQ_DISTINCT_COUNT:
            return &((SeqDistctCntT *)(void *)plan)->projectExpr;
        case T_STREAM_WINDOW_TABLE:
            return &((StreamWindowTableT *)(void *)plan)->projectionExpr;
        case T_STREAM_OVER_AGG:
            return &((StreamAggOverT *)(void *)plan)->projectionExpr;
        case T_STREAM_AGG_OVER_WINDOW:
            return &((StreamAggOverWindowT *)(void *)plan)->projectionExpr;
        case T_STREAM_WINDOW_AGG:
            return &((StreamWindowAggT *)(void *)plan)->projectionExpr;
#endif
        default:
            return NULL;
    }
}

#ifdef FEATURE_SQL
#ifndef IDS_HAOTIAN
static Status UpdateProjExpr4Join(ExprT *newProjExpr, ExprT *oldProjExpr);

// 处理复合表达式类型 (Binary/Unary/Func 等)
static Status UpdateComplexProjExpr4Join(ExprT *newProjExpr, ExprT *oldProjExpr)
{
    Status ret;
    if (ExprIsBinary(newProjExpr->opType)) {
        ret = UpdateProjExpr4Join(CastToBinary(newProjExpr)->left, oldProjExpr);
        if (ret != GMERR_OK) {
            return ret;
        }
        return UpdateProjExpr4Join(CastToBinary(newProjExpr)->right, oldProjExpr);
    }
    if (ExprIsUnary(newProjExpr->opType)) {
        return UpdateProjExpr4Join(CastToUnary(newProjExpr)->child, oldProjExpr);
    }
    if (ExprIsFunc(newProjExpr->opType)) {
        ExprArrayT *arr = ExprGetExprArray(newProjExpr);
        for (uint32_t i = 0; i < arr->num; ++i) {
            UpdateProjExpr4Join(arr->expr[i], oldProjExpr);
        }
    }
    return GMERR_FEATURE_NOT_SUPPORTED;
}

static Status UpdateProjExpr4Join(ExprT *newProjExpr, ExprT *oldProjExpr)
{
    DB_POINTER2(newProjExpr, oldProjExpr);
    DB_ASSERT(oldProjExpr->opType == EXPR_OP_TUPLE);
    switch (newProjExpr->opType) {
        case EXPR_OP_LVAR: {
            ExprVarT *exprVar = CastToVar(newProjExpr);
            ExprArrayT *oldProjExprArr = ExprGetExprArray(oldProjExpr);
            DB_ASSERT(exprVar->propId < oldProjExprArr->num);
            ExprT *refExpr = oldProjExprArr->expr[exprVar->propId];
            DB_ASSERT(refExpr->opType == EXPR_OP_LVAR || refExpr->opType == EXPR_OP_RVAR);
            *exprVar = *CastToVar(refExpr);
            break;
        }
        case EXPR_OP_TUPLE: {
            ExprArrayT *exprArr = ExprGetExprArray(newProjExpr);
            for (uint32_t i = 0; i < exprArr->num; ++i) {
                ExprT *exprInArr = exprArr->expr[i];
                UpdateProjExpr4Join(exprInArr, oldProjExpr);
            }
            break;
        }
        default:
            return UpdateComplexProjExpr4Join(newProjExpr, oldProjExpr);
    }
    return GMERR_OK;
}
#endif
#endif

static Status CreateProjExpr(DbMemCtxT *memCtx, const IRExprT *irExpr, PlanT **plan)
{
    ExprT **projExpr = GetProjectExprFromPlan(*plan);
    if (projExpr == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_FEATURE_NOT_SUPPORTED, "get project expression, tagType: %" PRIu32, (uint32_t)(*plan)->tagType);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    OpPhyExprProjectT *phyOp = (OpPhyExprProjectT *)(void *)irExpr->op;
#ifdef FEATURE_SQL
#ifndef IDS_HAOTIAN
    if ((*plan)->tagType == T_NEST_LOOP) {
        // 投影的下方是 JOIN 时, 需要修正投影表达式中的 ExprVar.
        Status ret = UpdateProjExpr4Join(phyOp->projExpr, *projExpr);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
#endif
#endif
    *projExpr = phyOp->projExpr;
    return GMERR_OK;
}

static Status UpdatePlanSchemaByProjExpr(DbMemCtxT *memCtx, const AASchemaT *projSchema, PlanPropDescT *planPropDesc)
{
    // 1. 修正 propNum
    planPropDesc->propNum = projSchema->propAA.propNum;
    // 2. 删除旧的 Schema
    DbDynMemCtxFree(memCtx, planPropDesc->propSchema);
    planPropDesc->propSchema = NULL;
    // 3. 生成新的schema
    size_t size = (size_t)projSchema->propAA.propNum * sizeof(DmPropertySchemaT *);
    // memCtx：统一释放，详见文件注释Note1；并发：不涉及，详见文件注释Note2
    planPropDesc->propSchema = (DmPropertySchemaT **)DbDynMemCtxAlloc(memCtx, size);
    if (planPropDesc->propSchema == NULL) {
        // LCOV_EXCL_START
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "malloc projected schema, size: %" PRIu32, (uint32_t)size);
        // LCOV_EXCL_STOP
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(planPropDesc->propSchema, size, 0, size);
    for (uint32_t i = 0; i < projSchema->propAA.propNum; i++) {
        planPropDesc->propSchema[i] = projSchema->propAA.properties[i];
    }

    return GMERR_OK;
}

Status Phy2PlanExprProjectRule(PlannerInfoT *info, const IRExprT *phyExp, PlanT **plan)
{
    Status ret;
    // 先递归处理子节点
    if ((ret = TransPhyExpr2Plan(info, phyExp->children[0], plan)) != GMERR_OK) {
        return ret;
    }
    // 为子节点构造投影表达式
    if ((ret = CreateProjExpr(info->memCtx, phyExp, plan)) != GMERR_OK) {
        return ret;
    }
    // 修改子节点的 schema (plan prop desc) 为投影后的 schema
    const AASchemaT *projSchema = IRGetOpAASchemaConst(phyExp->op);
    if ((ret = UpdatePlanSchemaByProjExpr(info->memCtx, projSchema, &(*plan)->aaSlotDesc)) != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

#endif
