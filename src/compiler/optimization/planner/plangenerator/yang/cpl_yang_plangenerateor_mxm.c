/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * @Description: Implementation for expand plan generator rule of yang planner
 * Note1:memctx生命周期为长进程级别，在创建dml计划时从g_yangPlanCache.memCtx中申请，异常分支统一释放
 * Note2:模块不涉及并发，由外围开启串行化事务保证
 * @Author: tianyabo
 * @Create: 2023-08-30
 */

#include "dm_yang_interface.h"
#include "cpl_ir_item_op.h"
#include "cpl_yang_plangenerator_common.h"
#include "cpl_yang_plangenerator.h"

#ifdef __cplusplus
extern "C" {
#endif

// key的类型可能和value的不一致，需要转换
YANG_MODEL_VALIDATE_SECTION
static Status YangLog2PlanIndexScanCastType(IRExprT *eqList[], DmVlIndexLabelT *pkIndex)
{
    Status ret = GMERR_OK;
    uint32_t propeNum = pkIndex->propeNum;
    for (uint32_t i = 0; i < propeNum; i++) {
        if (eqList[i] == NULL) {
            continue;
        }

        IRExprT *eq = eqList[i];
        DB_ASSERT(eq->op->type == IR_ITEMOP_BINARY);
        DB_ASSERT(((OpItemBinaryT *)eq->op)->code == COMP_OP_EQ);
        IRExprT *key = eq->children[0];
        IRExprT *value = eq->children[1];

        // 类型提升可以了类型，需要改回去，否则索引不能使用
        ((OpItemPropertyT *)key->op)->valueType = pkIndex->properties[pkIndex->propIds[i]].dataType;
        if (value->op->type == IR_ITEMOP_CONSTVALUE) {
            ret =
                DmValueConvert(&((OpItemConstT *)value->op)->value, pkIndex->properties[pkIndex->propIds[i]].dataType);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to cast const value, property=%s, type=%" PRIi32 ".",
                    pkIndex->properties[pkIndex->propIds[i]].name, (int32_t)((OpItemConstT *)value->op)->value.type);
                return ret;
            }
        }

        if (value->op->type == IR_ITEMOP_PROPERTY) {
            ((OpItemPropertyT *)value->op)->valueType = pkIndex->properties[pkIndex->propIds[i]].dataType;
        }

        if (value->op->type == IR_ITEMOP_PARA) {
            ((OpItemParaT *)value->op)->valueType = pkIndex->properties[pkIndex->propIds[i]].dataType;
        }
    }
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static void YangLog2PlanIndexScanSeparateKey(IRExprT *eqList[], uint32_t propeNum)
{
    DB_POINTER(eqList);
    // 将主键过滤条件分离
    for (uint32_t i = 0; i < propeNum; i++) {
        if (eqList[i] == NULL) {
            continue;
        }

        IRExprT *eq = eqList[i];
        IRExprT *father = eq->father;
        if (father->op->type == IR_LOGOP_SELECT) {
            // 左边弟节点替换父节点
            IRReplace(father, father->children[0]);
        } else {
            DB_ASSERT(father->op->type == IR_ITEMOP_BINARY);
            DB_ASSERT(((OpItemBinaryT *)father->op)->code == LOGIC_OP_AND);
            IRExprT *sibling = IRGetSibling(eq);
            // 兄弟节点替换父节点
            IRReplace(sibling->father, sibling);
        }
    }
}

// 如果没有找到返回 pkIndex->propeNum
YANG_MODEL_VALIDATE_SECTION
static uint32_t YangFindIndexPropId(DmVlIndexLabelT *pkIndex, uint32_t propertyId)
{
    for (uint32_t i = 0; i < pkIndex->propeNum; i++) {
        if (pkIndex->propIds[i] == propertyId) {
            return i;
        }
    }
    return pkIndex->propeNum;
}

YANG_MODEL_VALIDATE_SECTION
static bool YangLog2PlanIndexScanFindEQ(IRExprT **eqList, DmVlIndexLabelT *pkIndex, IRExprT *op, uint32_t pathId)
{
    DB_POINTER3(eqList, pkIndex, op);
    uint32_t i = 0;
    if (op->children[0]->op->type == IR_ITEMOP_PROPERTY) {  // 左边
        OpItemPropertyT *p = (OpItemPropertyT *)op->children[0]->op;
        if (pathId == p->pathId && p->nodeInfo.uniqNodeId == 1 &&
            pkIndex->idxLabelBase.srcLabelId == p->nodeInfo.vertexLabelId &&
            ((i = YangFindIndexPropId(pkIndex, p->nodeInfo.propertyId)) < pkIndex->propeNum)) {
            if (eqList[i] != NULL) {
                return false;  // 主键中的属性只能出现一次
            }
            eqList[i] = op;
            return true;
        }
    }

    if (op->children[1]->op->type == IR_ITEMOP_PROPERTY) {  // 右边
        OpItemPropertyT *p = (OpItemPropertyT *)op->children[1]->op;
        if (pathId == p->pathId && p->nodeInfo.uniqNodeId == 1 &&
            pkIndex->idxLabelBase.srcLabelId == p->nodeInfo.vertexLabelId &&
            ((i = YangFindIndexPropId(pkIndex, p->nodeInfo.propertyId)) < pkIndex->propeNum)) {
            if (eqList[i] != NULL) {
                return false;  // 主键中的属性只能出现一次
            }
            eqList[i] = op;
            IRSwapChildren(op);  // 左右交换，索引放在左边
            return true;
        }
    }

    return true;
}

YANG_MODEL_VALIDATE_SECTION
static bool YangLog2PlanIndexScanFindKey(IRExprT **eqList, DmVlIndexLabelT *pkIndex, IRExprT *op, uint32_t pathId)
{
    if (op->op->type == IR_ITEMOP_BINARY) {
        OpItemBinaryT *binary = (OpItemBinaryT *)op->op;
        if (binary->code == LOGIC_OP_AND) {
            if (!YangLog2PlanIndexScanFindKey(eqList, pkIndex, op->children[0], pathId)) {
                return false;  // 不能继续处理
            }

            if (!YangLog2PlanIndexScanFindKey(eqList, pkIndex, op->children[1], pathId)) {
                return false;  // 不能继续处理
            }
        }

        if (binary->code == COMP_OP_EQ) {  // 目前只考虑顶层属性
            return YangLog2PlanIndexScanFindEQ(eqList, pkIndex, op, pathId);
        }
    }

    return true;  // 不满足继续处理
}

YANG_MODEL_VALIDATE_SECTION
static bool YangLog2PlanIndexScanGetKey(IRExprT **eqList, DmVlIndexLabelT *pkIndex, IRExprT *op, uint32_t pathId)
{
    DB_POINTER3(eqList, pkIndex, op);
    // 条件里必须都是AND和等值判断
    bool continueCheck = YangLog2PlanIndexScanFindKey(eqList, pkIndex, op, pathId);
    if (!continueCheck) {
        return false;
    }

    for (uint32_t i = 0; i < pkIndex->propeNum; i++) {
        if (eqList[i] == NULL && pkIndex->propIds[i] != DM_YANG_PID_PROPE_SUBSCRIPT) {
            return false;  // 主键不能完全匹配，放弃
        }
    }
    return true;
}

YANG_MODEL_VALIDATE_SECTION
static uint32_t Log2PhyGetMxMFatherPathId(IRExprT *mxm)
{
    DB_POINTER(mxm);
    if (mxm->children[0]->op->type == IR_LOGOP_MXM) {
        return ((OpLogicalAAScanT *)mxm->children[0]->children[1]->op)->pathId;
    }
    if (mxm->children[0]->op->type == IR_LOGOP_SCAN) {
        return ((OpLogicalAAScanT *)(void *)mxm->children[0]->op)->pathId;
    }
    if (mxm->children[0]->op->type == IR_LOGOP_ARGUMENT) {
        return ((OpLogicalArgumentT *)(void *)mxm->children[0]->op)->pathId;
    }
    if (mxm->children[0]->op->type == IR_LOGOP_SELECT) {
        return Log2PhyGetMxMFatherPathId(mxm->children[0]);
    }
    DB_ASSERT(false);
    return DB_MAX_UINT32;
}

YANG_MODEL_VALIDATE_SECTION
Status Log2MxMIndexScanFillPidKey(YangPlannerInfoT *info, ExprT **left, ExprT **right, IRExprT *oriExpr)
{
    const uint32_t fatherPathId = Log2PhyGetMxMFatherPathId(oriExpr);
    const uint32_t pathId = ((OpLogicalAAScanT *)(void *)oriExpr->children[1]->op)->pathId;

    // pid一定在MxM对应的节点上，可以直接用MxM对应点的pathId
    *left = ExprMakePropertyWithArena(info->planArena, 1, DM_YANG_PID_PROPE_SUBSCRIPT, pathId, DB_DATATYPE_NULL);
    if (*left == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to alloc property expression.");
        return GMERR_OUT_OF_MEMORY;
    }

    *right = ExprMakePropertyWithArena(info->planArena, 1, DM_YANG_ID_PROPE_SUBSCRIPT, fatherPathId, DB_DATATYPE_NULL);
    if (*right == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to alloc property expression.");
        return GMERR_OUT_OF_MEMORY;
    }

    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
Status Log2MxMIndexScanCombineKey(
    YangPlannerInfoT *info, IRExprT *eqList[], IRExprT *oriExpr, ExprT **expr, PlanT *plan)
{
    Status ret = GMERR_OK;
    DmVertexLabelT *dstVertexLabel = ((OpLogicalAAScanT *)oriExpr->children[1]->op)->vertexLabel;
    DmVlIndexLabelT *pkIndex = dstVertexLabel->metaVertexLabel->pkIndex;
    uint32_t propeNum = pkIndex->propeNum;

    ExprT *left = ExprMakeTupleWithArena(info->planArena, propeNum);
    if (left == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to alloc tuple expression.");
        return GMERR_OUT_OF_MEMORY;
    }

    ExprT *right = ExprMakeTupleWithArena(info->planArena, propeNum);
    if (right == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to alloc tuple expression.");
        return GMERR_OUT_OF_MEMORY;
    }

    ExprT *current = ExprMakeBinaryWithArena(info->planArena, left, right, EXPR_OP_EQ);
    if (current == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to alloc binary expression.");
        return GMERR_OUT_OF_MEMORY;
    }

    ExprArrayT *leftArray = ExprGetExprArray(left);
    ExprArrayT *rightArray = ExprGetExprArray(right);
    for (uint32_t i = 0; i < propeNum; i++) {
        IRExprT *eq = eqList[i];
        if (eq == NULL) {  // :pid = :id
            ret = Log2MxMIndexScanFillPidKey(info, &leftArray->expr[i], &rightArray->expr[i], oriExpr);
            if (ret != GMERR_OK) {
                return ret;
            }

            continue;
        }

        ret = YangGetExprByItem(info, eq->children[0], plan, &leftArray->expr[i]);
        if (ret != GMERR_OK) {
            return ret;
        }

        ret = YangGetExprByItem(info, eq->children[1], plan, &rightArray->expr[i]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    *expr = current;
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
static Status YangLog2PlanIndexScanBuild(YangPlannerInfoT *info, IRExprT *logExp, IRExprT **eqList, PlanT **plan)
{
    Status ret = GMERR_OK;
    DmVertexLabelT *dstVertexLabel = ((OpLogicalAAScanT *)logExp->children[1]->op)->vertexLabel;

    // 生成嵌套循环
    NestLoopT *nestLoop = DbArenaAlloc(info->planArena, sizeof(NestLoopT));
    if (nestLoop == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to alloc NestLoopT.");
        return GMERR_OUT_OF_MEMORY;
    }

    *nestLoop = (NestLoopT){};
    nestLoop->join.plan.tagType = T_NEST_LOOP;
    nestLoop->join.joinType = JOIN_INNER;

    IndexScanT *indexScan = DbArenaAlloc(info->planArena, sizeof(IndexScanT));
    if (indexScan == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to alloc IndexScanT.");
        return GMERR_OUT_OF_MEMORY;
    }
    bool notFirst = info->notFist;
    info->notFist = true;
    nestLoop->join.plan.rightTree = &indexScan->scan.plan;
    *indexScan = (IndexScanT){};
    indexScan->scan.plan.tagType = T_INDEX_SCAN;
    // indexScan的pathId直接使用mxm的pathId
    indexScan->scan.pathId = ((OpLogicalAAScanT *)logExp->children[1]->op)->pathId;
    indexScan->scan.label = CataCloneVertexLabel(dstVertexLabel);
    indexScan->indexSeqNum = 0;
    ret = Log2MxMIndexScanCombineKey(info, eqList, logExp, &indexScan->indexQualExpr, &indexScan->scan.plan);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = TransYangLogExpr2Plan(info, logExp->children[0], &nestLoop->join.plan.leftTree);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (!notFirst) {
        info->currentPathId = indexScan->scan.pathId;
    }
    info->pathLen = (info->pathLen < indexScan->scan.pathId) ? indexScan->scan.pathId : info->pathLen;
    *plan = &nestLoop->join.plan;
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
Status YangLog2PlanIndexScan(YangPlannerInfoT *info, IRExprT *logExp, PlanT **plan)
{
    Status ret = GMERR_OK;
    IRExprT **eqList = NULL;  // 必须放在最前面
    *plan = NULL;

    if (logExp->father == NULL || logExp->father->op->type != IR_LOGOP_SELECT) {
        return GMERR_OK;
    }

    DmVertexLabelT *dstVertexLabel = ((OpLogicalAAScanT *)logExp->children[1]->op)->vertexLabel;
    uint32_t pathId = ((OpLogicalAAScanT *)logExp->children[1]->op)->pathId;
    DmVlIndexLabelT *pkIndex = dstVertexLabel->metaVertexLabel->pkIndex;
    if (pkIndex == NULL) {
        goto EXIT;
    }

    eqList = DbDynMemCtxAlloc(info->planArena->memCtx, sizeof(IRExprT *) * pkIndex->propeNum);
    if (eqList == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to alloc array, propeNum=%" PRIu32 ".", pkIndex->propeNum);
        goto EXIT;
    }

    (void)memset_s(eqList, sizeof(IRExprT *) * pkIndex->propeNum, 0, sizeof(IRExprT *) * pkIndex->propeNum);

    bool match = YangLog2PlanIndexScanGetKey(eqList, pkIndex, logExp->father->children[1], pathId);
    if (!match) {
        ret = GMERR_OK;
        goto EXIT;
    }

    ret = YangLog2PlanIndexScanCastType(eqList, pkIndex);
    if (ret != GMERR_OK) {
        return ret;
    }

    YangLog2PlanIndexScanSeparateKey(eqList, pkIndex->propeNum);
    ret = YangLog2PlanIndexScanBuild(info, logExp, eqList, plan);

EXIT:
    if (eqList != NULL) {  // 这个内存成功需要释放，其他内存成功不释放，失败统一释放
        DbDynMemCtxFree(info->planArena->memCtx, eqList);
    }
    return ret;
}

YANG_MODEL_VALIDATE_SECTION
Status YangLog2PlanExpand(YangPlannerInfoT *info, IRExprT *logExp, PlanT **plan)
{
    DB_POINTER3(info, logExp, plan);
    Status ret;

    // memCtx：统一释放，详见文件注释cpl_yang_plancache.c Note1；并发：不涉及
    ExpandT *expand = DbArenaAlloc(info->planArena, sizeof(ExpandT));
    if (expand == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY,
            "Unable to apply YangPhy2PlanExpandRule, malloc mistake, size is %" PRIu32, (uint32_t)sizeof(ExpandT));
        return GMERR_OUT_OF_MEMORY;
    }
    *expand = (ExpandT){};
    expand->plan.tagType = T_EXPAND;
    OpLogicalAAScanT *op = (OpLogicalAAScanT *)(void *)(logExp->children[1]->op);

    expand->edgeLabel = CataCloneEdgeLabel(op->edgeLabel);
    expand->vertexLabel = CataCloneVertexLabel(op->vertexLabel);

    if (op->vertexLabel->metaCommon.metaId == expand->edgeLabel->sourceVertexLabelId) {
        expand->direction = EDGE_IN;
    } else if (op->vertexLabel->metaCommon.metaId == expand->edgeLabel->destVertexLabelId) {
        expand->direction = EDGE_OUT;
    } else {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Expand is not valid, vertexLabel=%s.",
            op->vertexLabel->metaCommon.metaName);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    expand->pathId = op->pathId;

    info->pathLen = (info->pathLen < expand->pathId) ? expand->pathId : info->pathLen;

    bool notFirst = info->notFist;
    info->notFist = true;

    ret = TransYangLogExpr2Plan(info, logExp->children[0], &(expand->plan.leftTree));
    if (ret != GMERR_OK) {
        return ret;
    }

    if (!notFirst) {
        info->currentPathId = expand->pathId;
    }
    *plan = (PlanT *)expand;
    return GMERR_OK;
}

// 目前扫描方向固定:src->dst
YANG_MODEL_VALIDATE_SECTION
Status YangLog2PlanMxMRule(YangPlannerInfoT *info, IRExprT *logExp, PlanT **plan)
{
    Status ret = GMERR_OK;
    // MxM右边节点是Scan(EdgeLabel) 转换成下一跳算子
    if (logExp->children[1]->op->type == IR_LOGOP_SCAN) {
        ret = YangLog2PlanIndexScan(info, logExp, plan);
        if (ret != GMERR_OK) {
            return ret;
        }

        if (*plan != NULL) {
            return GMERR_OK;
        }

        return YangLog2PlanExpand(info, logExp, plan);
    }

    DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Feature not supported.");
    return GMERR_FEATURE_NOT_SUPPORTED;
}

#ifdef __cplusplus
}
#endif
