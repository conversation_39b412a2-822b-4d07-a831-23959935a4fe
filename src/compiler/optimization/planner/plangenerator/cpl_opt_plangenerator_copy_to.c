/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: CopyTo phy2exec rule of planner
 * Author: gao<PERSON>hao
 * Create: 2023-11-15
 */
#ifdef FEATURE_TS

#include "cpl_opt_plangenerator.h"
#include "cpl_opt_plangenerator_common.h"

Status Phy2PlanCopyToRule(PlannerInfoT *info, const IRExprT *phyExp, PlanT **plan)
{
    CopyToT *planCopyTo = NULL;
    Status ret = InitExecPlanByType(info, (PlanT **)&planCopyTo, NULL, T_COPY_TO, sizeof(CopyToT));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "apply Phy2PlanCopyToRule.");  // LCOV_EXCL_LINE
        return ret;
    }
    OpPhyCopyToT *op = (OpPhyCopyToT *)(void *)(phyExp->op);
    planCopyTo->fileNameSize = op->pathLen;
    ret = DmCopyBufWithMemCtx(
        info->memCtx, (uint8_t *)(void *)op->targetPath, op->pathLen, (uint8_t **)(void **)&planCopyTo->fileName);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "assign target file name.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = TransPhyExpr2Plan(info, phyExp->children[0], &(planCopyTo->plan.leftTree));
    if (ret != GMERR_OK) {
        // 下层应当完备打印过日志
        return ret;
    }
    // copyTo 的 planPropDesc 直接指向子节点
    planCopyTo->propDesc = &(planCopyTo->plan.leftTree->aaSlotDesc);
    *plan = (PlanT *)planCopyTo;
    return GMERR_OK;
}
#endif
