/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: func plan generator rule of planner
 * Author: SQL team
 * Create: 2024-3-20
 */

#include "cpl_ir_func_op.h"
#include "cpl_opt_plangenerator.h"
#include "cpl_opt_plancache.h"
#include "cpl_opt_plangenerator_common.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SCAN_EXPR_TYPE_NUM 2

#if defined(FEATURE_TS)
static Status TsGetScanOpMetaId(DbMemCtxT *memCtx, IRExprT *rootExpr, BuiltInT *planAggFunc)
{
    DbListT scanExprList = {0};
    DbCreateList(&scanExprList, sizeof(IRExprT *), memCtx);
    Status ret = IRGetLeafExprListByTypes(
        rootExpr, &scanExprList, SCAN_EXPR_TYPE_NUM, IR_PHYOP_TIMEPARTITION_SCAN, IR_PHYOP_SEQSCAN);
    if (ret != GMERR_OK || scanExprList.count == 0) {
        DB_LOG_ERROR(ret, "get leaf label of build.");  // LCOV_EXCL_LINE
        DbDestroyList(&scanExprList);
        return ret;
    }

    if (scanExprList.count == 0) {
        DB_LOG_ERROR(ret, "find seqScan and timePartitionScan.");  // LCOV_EXCL_LINE
        DbDestroyList(&scanExprList);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    // 时序只有一个scan算子
    IRExprT *scanExpr = *(IRExprT **)DbListItem(&scanExprList, 0);
    OpPhySeqScanT *phyOp = (OpPhySeqScanT *)(void *)scanExpr->op;
    DbDestroyList(&scanExprList);
    planAggFunc->labelId = phyOp->label->metaCommon.metaId;
    return ret;
}
#endif

#if defined(FEATURE_TS) || defined(FEATURE_SQL) || defined(FEATURE_STREAM)
Status Phy2PlanSysAggFunc(PlannerInfoT *info, const IRExprT *phyExp, PlanT **plan)
{
    OpSysAggFuncT *aggFuncOp = (OpSysAggFuncT *)phyExp->op;
#if defined(FEATURE_STREAM)
    if (aggFuncOp->funcNum == 1) {
        ExprFuncT *exprFunc = CastToFunc((ExprT *)aggFuncOp->funcTrees);
        ExprSetFieldT *setExpr = CastToSetField(exprFunc->array.expr[0]);
        ExprBinaryT *binaryExpr = CastToBinary(setExpr->func);
        if (binaryExpr->expr.opType == EXTD_EXPR_OP_SEQDISTINCTCOUNT) {
            return Phy2PlanStreamSeqDistctCnt(info, phyExp, plan);
        }
    }
#endif
    BuiltInT *planAggFunc = NULL;
    Status ret = InitExecPlanByType(info, (PlanT **)&planAggFunc, phyExp->op, T_BUILTIN, sizeof(BuiltInT));
    if (ret != GMERR_OK) {
        return ret;
    }

    planAggFunc->argExpr = (ExprT *)aggFuncOp->funcTrees;

    ret = TransPhyExpr2Plan(info, phyExp->children[0], &(planAggFunc->plan.leftTree));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "transform child expr to exec plan.");  // LCOV_EXCL_LINE
        return ret;
    }
    // memCtx：由外层传入，并由外层管理释放；并发：不支持，外部模块保证线程安全
    ret = GenPlanPropDescFromAASchema(info->memCtx, &aggFuncOp->schema, &planAggFunc->builtInSlotDesc);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (aggFuncOp->having != NULL) {
        // memCtx：由外层传入，并由外层管理释放；并发：不支持，外部模块保证线程安全
        ret = CreateFilterExpressionAsTree(info->memCtx, (const IRExprT *)aggFuncOp->having, &planAggFunc->havingExpr);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

#if defined(FEATURE_TS)
    // 时序添加build in的metaId给explain使用
    if (DbCfgIsTsInstance()) {
        ret = TsGetScanOpMetaId(info->memCtx, phyExp->children[0], planAggFunc);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
#endif

    *plan = (PlanT *)planAggFunc;
    return GMERR_OK;
}
#endif

#ifdef __cplusplus
}
#endif
