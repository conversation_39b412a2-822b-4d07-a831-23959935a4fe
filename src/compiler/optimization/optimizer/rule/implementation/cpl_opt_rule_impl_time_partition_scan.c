/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 * Description: Implementation for implementation rule of TimePartitionScan
 * Author: gaominghao
 * Create: 2024-01-12
 * Notes:
 */
#ifdef FEATURE_TS
#include "cpl_opt_rule_implementation.h"

Status CreateRuleImplTimePartitionScan(DbMemCtxT *memCtx, OptRuleT **rule)
{
    return CreateImplRule(memCtx, IR_LOGOP_SCAN, RULE_IMPL_TIME_PARTITIONSCAN, rule);
}

uint32_t ImplTimePartitionScanRulePromise(OptRuleT *rule, MExprT *mExpr, OptCtxT *ctx)
{
    if (mExpr->op->type != IR_LOGOP_SCAN) {
        return REJECT_RULE_PROMISE;
    }
    OpLogicalAAScanT *scan = (OpLogicalAAScanT *)(void *)mExpr->op;
    if (!DmVertexLabelIsTsLabel(scan->vertexLabel)) {
        return REJECT_RULE_PROMISE;
    }
    return PHY_RULE_DEFAULT_PROMISE;
}

Status ApplyRuleImplTimePartitionScan(MemoT *memo, IRExprT **expr, PhyPropT *reqProp, bool *applied)
{
    Status ret;
    OpLogicalAAScanT *logOp = (OpLogicalAAScanT *)(void *)(*expr)->op;
    OpPhyScanT *phyOp = NULL;
    ret = IRCreateOp(memo->memCtx, IR_PHYOP_TIMEPARTITION_SCAN, (OpBaseT **)&phyOp);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    phyOp->schema = logOp->schema;
    phyOp->label = logOp->vertexLabel;
    (*expr)->op = (OpBaseT *)phyOp;
    *applied = true;
    return GMERR_OK;
}
#endif
