/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Implementation for finding the best index for a where clause
 * Author: zhuangyifeng
 * Create: 2023-08-30
 * Note: 请从入口函数 SearchIndex4ItemExpr 开始阅读
 */

#include "cpl_ir_item_op.h"
#include "cpl_ir_logical_op.h"
#include "cpl_opt_rule_utils.h"

#define INVALID_INDEXED_PROP_NUM DB_MAX_UINT32

#if defined(FEATURE_SQL) || defined(FEATURE_TS)
#define MAX_INDEX_CNT_IN_MULTI_INDEX_OR 3  // 约束: MULTI-INDEX-OR 场景下最多能够使用的索引扫描的个数
#define ARITY_OF_BINARY_OP 2
#endif

typedef enum IndexCondType { INDEX_EQ_COND, INDEX_COMP_COND } IndexCondTypeE;

// 记录where中查询条件，适用于等值查询、非等值查询
typedef struct {
    DbListT *condIRExprs;  // 查询条件的IRExprT*型列表
    bool *isCondUsed;      // 用于记录查询条件是否使用过
} IndexMatchCtxT;

// 返回允许使用索引的二元运算符的类型
static bool IsIndexableCompOp(OpBinaryCodeE binaryCode)
{
    if (binaryCode == COMP_OP_EQ ||  // =
        binaryCode == COMP_OP_LT ||  // <
        binaryCode == COMP_OP_LE ||  // <=
        binaryCode == COMP_OP_GT ||  // >
        binaryCode == COMP_OP_GE     // >=
    ) {
        return true;
    }
    return false;
}

Status InitIndexSearchCtx(DbMemCtxT *memCtx, IndexSearchCtxT *ctx)
{
    ctx->isIndexFound = false;

    DbCreateList(&ctx->eqCondList, sizeof(IRExprT *), memCtx);
    DbCreateList(&ctx->compCondList, sizeof(IRExprT *), memCtx);
    DbCreateList(&ctx->otherCondList, sizeof(IRExprT *), memCtx);

    DbCreateList(&ctx->indexList, sizeof(DmVlIndexLabelT *), memCtx);
    DbCreateList(&ctx->indexedCondTreeList, sizeof(IRExprT *), memCtx);
    DbCreateList(&ctx->nonIndexedCondTreeList, sizeof(IRExprT *), memCtx);

    // 初始化的时候先给 *不能使用索引的树* 的数组中放入一个空树, 保证这个数组一定不为空
    // 如果存在无法使用索引的条件, 那么这棵 [0] 树会在搜索索引的过程中被更新
    // 如果所有条件都可以使用索引, 那么这棵 [0] 树会一直保持为 NULL
    IRExprT *nullTree = NULL;
    return DbAppendListItem(&ctx->nonIndexedCondTreeList, &nullTree);
}

void DestroyIndexSearchCtx(IndexSearchCtxT *ctx)
{
    if (ctx == NULL) {
        return;
    }
    DbDestroyList(&ctx->indexList);
    DbDestroyList(&ctx->eqCondList);
    DbDestroyList(&ctx->compCondList);
    DbDestroyList(&ctx->indexedCondTreeList);
    DbDestroyList(&ctx->nonIndexedCondTreeList);
    DbDestroyList(&ctx->otherCondList);
}

/**
 * @brief 创建用于索引匹配的上下文
 * @param[in] memo               优化器的搜索空间实例
 * @param[in] indexLabel         待匹配的索引信息
 * @param[in] indexSearchCtx     查询条件信息
 * @param[in] condType           查询类型（等值，非等值），据此从indexSearchCtx获取查询条件信息到indexMatchCtx
 * @param[out] indexMatchCtx     用于匹配的上下文
 * @return                       结果状态
 */
static Status CreateIndexMatchCtx(MemoT *memo, const DmVlIndexLabelT *indexLabel, IndexSearchCtxT *indexSearchCtx,
    IndexCondTypeE condType, IndexMatchCtxT *indexMatchCtx)
{
    DB_POINTER4(memo, indexLabel, indexSearchCtx, indexMatchCtx);
    if (condType == INDEX_EQ_COND) {
        indexMatchCtx->condIRExprs = &indexSearchCtx->eqCondList;
    } else {
        indexMatchCtx->condIRExprs = &indexSearchCtx->compCondList;
    }

    bool *isCondUsed = NULL;
    uint32_t condsCnt = DbListGetItemCnt(indexMatchCtx->condIRExprs);
    size_t size = (size_t)condsCnt * sizeof(bool);
    Status ret = CreateObjectWithSize(memo->memCtx, (void **)&isCondUsed, size, "IndexMatchCtx.isCondUsed");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "create condUsed object.");
        return ret;
    }
    indexMatchCtx->isCondUsed = isCondUsed;
    return GMERR_OK;
}

/**
 * @brief 销毁CreateIndexMatchCtx创建的对象，未创建condIRExprs所以不销毁
 * @param[in] memCtx            内存上下文信息
 * @param[in] indexMatchCtx     用于匹配的上下文
 */
inline static void DestroyIndexMatchCtx(DbMemCtxT *memCtx, IndexMatchCtxT *indexMatchCtx)
{
    DB_POINTER2(memCtx, indexMatchCtx);
    DbDynMemCtxFree(memCtx, indexMatchCtx->isCondUsed);
    indexMatchCtx->isCondUsed = NULL;
}

/**
 * @brief 对于指定索引列, 获取已经添加的索引条件,是否已经设置 等于条件, 下限条件, 或者上限条件
 * @param[in] itemExpr    查找条件对应的表达式.
 * @param[in] hasEqCond     等于条件标志位.
 * @param[in] hasLowerCond  下限条件标志位.
 * @param[in] hasUpperCond   上限条件标志位.
 * @return 当前条件对应标志是否已设置.
 */
static bool GetIndexConditionBoundFlag(const IRExprT *itemExpr, bool hasEqCond, bool hasLowerCond, bool hasUpperCond)
{
    if ((IROpTypeE)(itemExpr->op->type) == IR_ITEMOP_BINARY) {
        OpBinaryCodeE binaryCode = ((OpItemBinaryT *)(void *)itemExpr->op)->code;
        switch (binaryCode) {
            case COMP_OP_EQ:
#ifdef FEATURE_SQL
            case LOGIC_OP_IS:
#endif
                return hasEqCond;
            case COMP_OP_LE:
            case COMP_OP_LT:
                return hasUpperCond;
            case COMP_OP_GE:
            case COMP_OP_GT:
                return hasLowerCond;
            default:
                return hasEqCond;
        }
    }
    return hasEqCond;
}

/**
 * @brief 对于指定索引列, 根据当前条件表达式,设置对应标志位
 * @param[in] itemExpr    查找条件对应的表达式.
 * @param[out] hasEqCond     等于条件标志位.
 * @param[out] hasLowerCond  下限条件标志位.
 * @param[out] hasUpperCond   上限条件标志位.
 */
static void SetIndexConditionBoundFlag(const IRExprT *itemExpr, bool *hasEqCond, bool *hasLowerCond, bool *hasUpperCond)
{
    if ((IROpTypeE)(itemExpr->op->type) == IR_ITEMOP_BINARY) {
        OpBinaryCodeE binaryCode = ((OpItemBinaryT *)(void *)itemExpr->op)->code;
        switch (binaryCode) {
            case COMP_OP_EQ:
#ifdef FEATURE_SQL
            case LOGIC_OP_IS:
#endif
                *hasEqCond = true;
                break;
            case COMP_OP_LE:
            case COMP_OP_LT:
                *hasUpperCond = true;
                break;
            case COMP_OP_GE:
            case COMP_OP_GT:
                *hasLowerCond = true;
                break;
            default:
                // where col1 + col2 (col1 + col2 上建立了表达式索引.)
                *hasEqCond = true;
                break;
        }
    } else {
        // where col1 (col1 上建立了索引.)
        *hasEqCond = true;
    }
}

/**
 * 检查索引列是否在给定的查询条件列表中出现. 这个函数目前只支持处理单列索引.
 * @param[in] memo      优化器搜索空间实例
 * @param[in] uniquePropId 要检查的索引属性的唯一id，有propId和metaId构成
 * @param[in] conds     list<IRExprT *> 查询条件的列表
 * @param[in] isCondUsed    bool 数组, 长度和 conds 相同. true 表示这个条件已经使用过, 不能用于匹配索引列.
 * @param[in] indexedCondTree   能够使用索引的条件组成的 IRExprT 树.
 *                              如果传入的索引列匹配到了 conds 中的某个搜索条件, 会将这个条件添加到 indexedCondTree 中
 * @return 如果 indexProp 在 conds 中出现过, 返回 true. 否则返回 false.
 */
static bool IsIndexPropMatchConds(
    MemoT *memo, const UniquePropIdT *uniquePropId, DbListT *conds, bool *isCondUsed, IRExprT **indexedCondTree)
{
    uint32_t condsCnt = DbListGetItemCnt(conds);
    bool hasEqCond = false;
    bool hasLowerCond = false;
    bool hasUpperCond = false;
    // 遍历查询条件
    for (uint32_t i = 0; i < condsCnt; ++i) {
        if (isCondUsed != NULL && isCondUsed[i]) {
            continue;
        }
        // 2. 取出查询条件中的属性列
        IRExprT *itemExpr = *(IRExprT **)DbListItem(conds, i);
        OpItemAttrT *itemAttr = NULL;
        if (itemExpr->op->type == IR_ITEMOP_BINARY) {
            if (itemExpr->children[0]->op->type != IR_ITEMOP_ATTR) {
                continue;
            }
            itemAttr = (OpItemAttrT *)(void *)itemExpr->children[0]->op;
        } else {
            DB_ASSERT(itemExpr->op->type == IR_ITEMOP_ATTR);
            itemAttr = (OpItemAttrT *)(void *)itemExpr->op;
        }
        uint32_t propId = DecodeGPropIdToPropId(itemAttr->propId);
        uint32_t tableId = DecodeGPropIdToTableId(itemAttr->propId);
        uint32_t metaId = *(uint32_t *)DbListItem(&memo->tableIdMap, tableId);
        if (uniquePropId->propId == propId && uniquePropId->tableId == metaId) {
            if (GetIndexConditionBoundFlag(itemExpr, hasEqCond, hasLowerCond, hasUpperCond)) {
                continue;
            }
            // 将这个查询条件添加到过滤表达式树中
            Status ret = AddItemExprToItemTree(memo->memCtx, itemExpr, indexedCondTree);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return false;
            }
            // 标记这个等值条件已使用, 并返回匹配成功
            if (isCondUsed != NULL) {
                isCondUsed[i] = true;
            }
            SetIndexConditionBoundFlag(itemExpr, &hasEqCond, &hasLowerCond, &hasUpperCond);
        }
    }
    return hasEqCond || hasLowerCond || hasUpperCond;
}

#ifdef FEATURE_SQL
/**
 * @brief 是否能在上下文中匹配表达式索引的单列，仅用于表达式索引
 * @param[in] memo               优化器的搜索空间实例
 * @param[in] exprCol            表达式索引的单个列
 * @param[in] condMatchCtx      用于匹配的上下文
 * @param[out] indexedCondTree   由已匹配条件构建的IRTree
 * @return 上下文中存在能匹配当前列的表达式则返回true
 */
static bool IsIndexExprColMatchConds(
    MemoT *memo, IRExprT *exprCol, IndexMatchCtxT *condMatchCtx, IRExprT **indexedCondTree)
{
    DB_POINTER4(memo, exprCol, condMatchCtx, indexedCondTree);
    DbListT *condIRExprs = condMatchCtx->condIRExprs;
    bool *isCondExprUsed = condMatchCtx->isCondUsed;
    uint32_t exprCnt = DbListGetItemCnt(condIRExprs);
    bool hasEqCond = false;
    bool hasLowerCond = false;
    bool hasUpperCond = false;
    for (uint32_t i = 0; i < exprCnt; ++i) {
        if (isCondExprUsed[i]) {
            continue;
        }
        IRExprT *exprCond = *(IRExprT **)DbListItem(condIRExprs, i);
        // 条件与索引列的匹配即是条件左子树与索引列的匹配，因为右子树为常量
        if (IRExprTreeEqual(exprCond->children[0], exprCol)) {
            if (GetIndexConditionBoundFlag(exprCond, hasEqCond, hasLowerCond, hasUpperCond)) {
                continue;
            }
            Status ret = AddItemExprToItemTree(memo->memCtx, exprCond, indexedCondTree);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_ERROR(ret, "add item expr to tree.");
                return false;
            }
            isCondExprUsed[i] = true;
            SetIndexConditionBoundFlag(exprCond, &hasEqCond, &hasLowerCond, &hasUpperCond);
        }
    }
    return hasEqCond || hasLowerCond || hasUpperCond;
}
#endif

/**
 * @brief 是否能在上下文中匹配索引的单列
 * @param[in] memo               优化器的搜索空间实例
 * @param[in] indexLabel         索引的信息
 * @param[in] idxColId           索引的列id
 * @param[in] condMatchCtx       用于匹配的上下文
 * @param[out] indexedCondTree   由已匹配条件构建的IRTree
 * @return 上下文中存在能匹配当前列的条件则返回true
 */
inline static bool IsIndexColMatchConds(MemoT *memo, const DmVlIndexLabelT *indexLabel, uint32_t idxColId,
    IndexMatchCtxT *condMatchCtx, IRExprT **indexedCondTree)
{
    DB_POINTER4(memo, indexLabel, condMatchCtx, indexedCondTree);
#ifdef FEATURE_SQL
    if (indexLabel->isExprIdx) {
        IRExprT *indexExprCol = (IRExprT *)DbListItem(&indexLabel->exprIdxInfo->irExprs, idxColId);
        return IsIndexExprColMatchConds(memo, indexExprCol, condMatchCtx, indexedCondTree);
    }
#endif
    DbListT *conds = condMatchCtx->condIRExprs;
    bool *isCondUsed = condMatchCtx->isCondUsed;
    UniquePropIdT uniquePropId = {
        .tableId = indexLabel->idxLabelBase.srcLabelId, .propId = indexLabel->propIds[idxColId]};
    return IsIndexPropMatchConds(memo, &uniquePropId, conds, isCondUsed, indexedCondTree);
}

/**
 * 将 conds 中没有使用到的条件添加到给定的 *不能使用索引的条件的树* nonIndexedCondTree 中
 * @param[in] memCtx        内存上下文
 * @param[in] condList      list<IRExprT *> 每个元素是一个要处理的条件
 * @param[in] isCondUsed    bool 数组, 长度和 conds 相同. true 表示这个条件不添加到树中.
 *                          可以传 NULL, 表示 conds 中所有条件都需要处理.
 * @param[in/out] nonIndexedCondTree 不能使用索引的条件构成的 IRExpr 树
 * @return 执行成功返回 GMERR_OK, 否则返回相应的错误码
 */
static Status AddCondsToNonIndexedCondTree(
    DbMemCtxT *memCtx, DbListT *condList, const bool *isCondUsed, IRExprT **nonIndexedCondTree)
{
    uint32_t condsCnt = DbListGetItemCnt(condList);
    for (uint32_t i = 0; i < condsCnt; ++i) {
        if (isCondUsed == NULL || !isCondUsed[i]) {
            IRExprT *cond = *(IRExprT **)DbListItem(condList, i);
            Status ret = AddItemExprToItemTree(memCtx, cond, nonIndexedCondTree);
            if (ret != GMERR_OK) {
                // add condition to non-Indexed condition tree
                DB_LOG_ERROR(ret, "add condition.");
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static uint32_t CheckHashIndexAgainstConds(MemoT *memo, DmVlIndexLabelT *index, IndexSearchCtxT *indexSearchCtx,
    IRExprT **indexedCondTree, IRExprT **nonIndexedCondTree)
{
    uint32_t indexedPropNum = index->propeNum;  // 如果哈希索引可用, 那么一定是所有的索引列都命中.
    DbListT *eqConds = &indexSearchCtx->eqCondList;
    uint32_t eqCondsCnt = DbListGetItemCnt(eqConds);
    // 哈希索引只能用等值条件, 并且需要完全匹配
    if (eqCondsCnt == 0 || index->propeNum > eqCondsCnt) {
        return 0;
    }

    IndexMatchCtxT eqCondMatchCtx;
    Status ret = CreateIndexMatchCtx(memo, index, indexSearchCtx, INDEX_EQ_COND, &eqCondMatchCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return INVALID_INDEXED_PROP_NUM;
    }
    uint32_t size = eqCondsCnt * sizeof(bool);

    // 依次检查每个索引属性是否有等值条件. 当每个索引属性都有一个等值条件时, 才能使用哈希索引.
    for (uint32_t i = 0; i < index->propeNum; ++i) {
        // 1. 遍历等值条件, 检查这个索引列是否和这个等值条件的属性匹配 (在同一个等价类中).
        //    只要有一个索引列不存在等值条件, 就不能使用哈希索引
        if (!IsIndexColMatchConds(memo, index, i, &eqCondMatchCtx, indexedCondTree)) {
            indexedPropNum = 0;
            IRDestroyExprTree(memo->memCtx, indexedCondTree);
            *indexedCondTree = NULL;
            (void)memset_s(eqCondMatchCtx.isCondUsed, size, 0, size);
            break;
        }
    }

    // 2. 将没有使用的等值条件挂到 nonIndexedCondTree 上
    ret = AddCondsToNonIndexedCondTree(memo->memCtx, eqConds, eqCondMatchCtx.isCondUsed, nonIndexedCondTree);
    if (ret != GMERR_OK) {
        indexedPropNum = INVALID_INDEXED_PROP_NUM;
        goto EXIT;
    }
    // 3. 将没有使用的非等值条件挂到 nonIndexedCondTree 上
    ret = AddCondsToNonIndexedCondTree(memo->memCtx, &indexSearchCtx->compCondList, NULL, nonIndexedCondTree);
    if (ret != GMERR_OK) {
        indexedPropNum = INVALID_INDEXED_PROP_NUM;
        goto EXIT;
    }
EXIT:
    DestroyIndexMatchCtx(memo->memCtx, &eqCondMatchCtx);
    return indexedPropNum;
}

#if defined(FEATURE_SQL) || defined(FEATURE_TS)
#ifdef FEATURE_SQL
/**
 * @brief 获取索引的列数
 * @param[in] indexLabel         索引的信息
 * @return 索引的列数
 */
inline static uint32_t GetIndexColumnCnt(DmVlIndexLabelT *indexLabel)
{
    if (indexLabel->isExprIdx) {
        return indexLabel->exprIdxInfo->num;
        ;
    } else {
        return indexLabel->propeNum;
    }
}
#else
inline static uint32_t GetIndexColumnCnt(DmVlIndexLabelT *indexLabel)
{
    return indexLabel->propeNum;
}
#endif

static uint32_t CheckBtreeIndexAgainstConds(MemoT *memo, DmVlIndexLabelT *index, IndexSearchCtxT *indexSearchCtx,
    IRExprT **indexedCondTree, IRExprT **nonIndexedCondTree)
{
    Status ret;
    uint32_t indexedPropNum = 0;  // 记录可以使用索引的属性个数
    uint32_t indexColCnt = GetIndexColumnCnt(index);

    IndexMatchCtxT eqCondMatchCtx;
    IndexMatchCtxT compCondMatchCtx = {0};

    ret = CreateIndexMatchCtx(memo, index, indexSearchCtx, INDEX_EQ_COND, &eqCondMatchCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        OPT_ERROR_LOG_PRINT(ret, "Unable to create index match context.");
        return INVALID_INDEXED_PROP_NUM;
    }

    ret = CreateIndexMatchCtx(memo, index, indexSearchCtx, INDEX_COMP_COND, &compCondMatchCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        OPT_ERROR_LOG_PRINT(ret, "Unable to create index match context.");
        goto EXIT;
    }

    // BTree 索引需要索引列和查询条件中的属性列达成最左匹配. 且非等值条件只能使用一个 (同一个属性的非等值条件视为一个)
    for (uint32_t i = 0; i < indexColCnt; ++i) {
        // 1. 遍历等值条件, 检查这个索引列是否和这个等值条件的属性匹配 (在同一个等价类中).
        if (IsIndexColMatchConds(memo, index, i, &eqCondMatchCtx, indexedCondTree)) {
            // 找到一个等值条件可以用这个索引, 记录可用索引的属性 +1, 检查下一个索引列
            ++indexedPropNum;
            continue;
        }
        // 2. 如果没有等值条件命中这个索引列, 检查非等值条件. 最多只能使用一个属性上的一个或多个非等值条件
        if (IsIndexColMatchConds(memo, index, i, &compCondMatchCtx, indexedCondTree)) {
            ++indexedPropNum;
        }
        break;
    }
    // 3. 把没有使用到的等值条件拼接到 nonIndexedCondTree 上
    ret = AddCondsToNonIndexedCondTree(
        memo->memCtx, eqCondMatchCtx.condIRExprs, eqCondMatchCtx.isCondUsed, nonIndexedCondTree);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    // 4. 把没有使用到的非等值条件拼接到 nonIndexedCondTree 上
    ret = AddCondsToNonIndexedCondTree(
        memo->memCtx, compCondMatchCtx.condIRExprs, compCondMatchCtx.isCondUsed, nonIndexedCondTree);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
EXIT:
    DestroyIndexMatchCtx(memo->memCtx, &eqCondMatchCtx);
    DestroyIndexMatchCtx(memo->memCtx, &compCondMatchCtx);
    return (ret == GMERR_OK) ? indexedPropNum : INVALID_INDEXED_PROP_NUM;
}
#else
// 非 SQL 场景下暂不支持选中 BTree 索引
static uint32_t CheckBtreeIndexAgainstConds(MemoT *memo, DmVlIndexLabelT *index, IndexSearchCtxT *indexSearchCtx,
    IRExprT **indexedCondTree, IRExprT **nonIndexedCondTree)
{
    return 0;
}
#endif

/**
 * 检查 index 在 indexSearchCtx 下是否可用, 并构造对应的两个 IRExprT 树:
 * 1. 可以用索引的条件 indexedCondTree
 * 2. 无法使用索引的条件 nonIndexedCondTree
 * 最后返回可用的索引列的个数
 * @param[in] memo  优化器的搜索空间实例
 * @param[in] index 要检查的索引
 * @param[in] indexSearchCtx 索引搜索的上下文
 * @param[out] indexedCondTree      可以使用索引的条件构成的 IR 表达式树
 * @param[out] nonIndexedCondTree   不能使用索引的条件构成的 IR 表达式树
 * @return 可用的索引列的个数. 如果在查找过程中发生错误, 返回值会是 INVALID_PROP_NUM
 */
static uint32_t CheckIndexAgainstConds(MemoT *memo, DmVlIndexLabelT *index, IndexSearchCtxT *indexSearchCtx,
    IRExprT **indexedCondTree, IRExprT **nonIndexedCondTree)
{
    switch (index->idxLabelBase.indexType) {
        case HASH_INDEX:
        case CHAINED_HASH_INDEX:
        case ART_INDEX_HASHCLUSTER:
        case ART_INDEX_LOCAL:
#ifdef FEATURE_TS
        case HASH_LINKLIST_INDEX:
#endif
#ifdef FEATURE_HAC
        case HAC_HASH_INDEX:
        case MULTI_HASH_INDEX:
#endif
            return CheckHashIndexAgainstConds(memo, index, indexSearchCtx, indexedCondTree, nonIndexedCondTree);
        case BTREE_INDEX:
            return CheckBtreeIndexAgainstConds(memo, index, indexSearchCtx, indexedCondTree, nonIndexedCondTree);
        default:
            break;
    }

    // 对于不支持的索引类型, 所有的条件都只能是非索引条件
    // 将所有等值条件挂到 nonIndexedCondTree 上
    Status ret = AddCondsToNonIndexedCondTree(memo->memCtx, &indexSearchCtx->eqCondList, NULL, nonIndexedCondTree);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return INVALID_INDEXED_PROP_NUM;
    }
    // 将所有非等值条件挂到 nonIndexedCondTree 上
    ret = AddCondsToNonIndexedCondTree(memo->memCtx, &indexSearchCtx->compCondList, NULL, nonIndexedCondTree);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return INVALID_INDEXED_PROP_NUM;
    }

    return 0;
}

static Status UpdateCtxAfterIndexSearch(DbMemCtxT *memCtx, IndexSearchCtxT *indexSearchCtx, DmVlIndexLabelT *bestIndex,
    IRExprT *indexedCondTree, IRExprT *nonIndexedCondTree)
{
    // 不管有没有找到索引, 要把 otherConds 拼到不可用索引的表达式树中
    Status ret;
    ret = AddCondsToNonIndexedCondTree(memCtx, &indexSearchCtx->otherCondList, NULL, &nonIndexedCondTree);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 有最优索引, 将 *这个索引* 以及 *索引上的过滤条件树* 记录到索引搜索上下文中
    if (bestIndex != NULL) {
        ret = DbAppendListItem(&indexSearchCtx->indexList, &bestIndex);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DbAppendListItem(&indexSearchCtx->indexedCondTreeList, &indexedCondTree);
        if (ret != GMERR_OK) {
            return ret;
        }
        return DbSetListItem(&indexSearchCtx->nonIndexedCondTreeList, &nonIndexedCondTree, 0);
    }
    // 没有找到可用的索引, 此时所有的条件都是不可用索引的条件
    ret = AddCondsToNonIndexedCondTree(memCtx, &indexSearchCtx->eqCondList, NULL, &nonIndexedCondTree);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = AddCondsToNonIndexedCondTree(memCtx, &indexSearchCtx->compCondList, NULL, &nonIndexedCondTree);
    if (ret != GMERR_OK) {
        return ret;
    }
    return DbSetListItem(&indexSearchCtx->nonIndexedCondTreeList, &nonIndexedCondTree, 0);
}

/**
 * 检查在给定的索引搜索上下文下, vertexLabel 中是否有可用的索引.
 * 依次检查 vertexLabel 的主键索引和二级索引. 选择匹配属性最多的那个索引. 如果匹配属性数量相同, 选取排序在前的索引.
 * 如果有可用的索引, 那么：
 * 1. 索引会被放在&indexSearchCtx->indexList[0]
 * 2. 索引上的条件会被放在&indexSearchCtx->indexedCondTreeList[0]
 * 3. 非索引上的条件会被放在&indexSearchCtx->nonIndexedCondTreeList[0]
 * 如果没有可用的索引:
 * 1. 所有的查询条件都会被放在&indexSearchCtx->nonIndexedCondTreeList[0]
 * 2. indexList 和 indexedCondTreeList 长度为 0 (注意不是为空)
 * @param[in] memo          优化器搜索空间实例
 * @param[in] scanOp        AA_scan算子
 * @param[in] indexSearchCtx 索引搜索的上下文
 * @return 执行成功返回 GMERR_OK, 否则返回相应的错误码
 */
static Status SearchIndexWithCtxInner(MemoT *memo, OpLogicalAAScanT *scanOp, IndexSearchCtxT *indexSearchCtx)
{
    DmVlIndexLabelT *bestIndex = NULL;
    uint32_t indexedPropNum = 0;
    DmVertexLabelT *vertexLabel = scanOp->vertexLabel;
    // [内存上下文] 外部传入
    // [释放周期] 如果这两棵树对应的索引最终被选中, 那么它会和优化器的内存上下文一起被销毁. 否则它会在本函数内被释放
    // [多线程] 不涉及
    IRExprT *indexedCondTree = NULL;
    IRExprT *nonIndexedCondTree = NULL;
    if (vertexLabel->metaVertexLabel->pkIndex != NULL) {
        indexedPropNum = CheckIndexAgainstConds(
            memo, vertexLabel->metaVertexLabel->pkIndex, indexSearchCtx, &indexedCondTree, &nonIndexedCondTree);
        if (indexedPropNum == INVALID_INDEXED_PROP_NUM) {  // 目前错误退出只会是 OUT_OF_MEMORY
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "check primary index.");
            return GMERR_OUT_OF_MEMORY;
        }
        if (indexedPropNum != 0) {
            bestIndex = vertexLabel->metaVertexLabel->pkIndex;
        }
    }
    for (uint32_t i = 0; i < vertexLabel->metaVertexLabel->secIndexNum; ++i) {
        // [内存上下文] 优化器内存上下文
        // [释放周期] 如果这两棵树对应的索引最终被选中, 那么它会和优化器的内存上下文一起被销毁. 否则它会在本函数内被释放
        // [多线程] 不涉及
        IRExprT *indexedCondTreeOfSecIndex = NULL;
        IRExprT *nonIndexedCondTreeOfSecIndex = NULL;
        DmVlIndexLabelT *idxLabel = &vertexLabel->metaVertexLabel->secIndexes[i];
        uint32_t indexedPropNumOfSecIndex = CheckIndexAgainstConds(
            memo, idxLabel, indexSearchCtx, &indexedCondTreeOfSecIndex, &nonIndexedCondTreeOfSecIndex);
        if (indexedPropNumOfSecIndex == INVALID_INDEXED_PROP_NUM) {  // 目前错误退出只会是 OUT_OF_MEMORY
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "check second index No.%" PRIu32 "", i);
            return GMERR_OUT_OF_MEMORY;
        }
        if (indexedPropNumOfSecIndex > indexedPropNum) {
            indexedPropNum = indexedPropNumOfSecIndex;
            bestIndex = &vertexLabel->metaVertexLabel->secIndexes[i];  // 更新最优索引
            IRDestroyExprTree(memo->memCtx, &indexedCondTree);         // 释放旧的, 索引上的过滤条件树
            IRDestroyExprTree(memo->memCtx, &nonIndexedCondTree);      // 释放旧的, 非索引上的过滤条件树
            indexedCondTree = indexedCondTreeOfSecIndex;               // 更新索引上的过滤条件树
            nonIndexedCondTree = nonIndexedCondTreeOfSecIndex;         // 更新非索引上的过滤条件树
        }
    }
    return UpdateCtxAfterIndexSearch(memo->memCtx, indexSearchCtx, bestIndex, indexedCondTree, nonIndexedCondTree);
}

#if defined(FEATURE_SQL) || defined(FEATURE_GQL)
static Status UpdateCtxBySpecifiedIndex(MemoT *memo, DmVlIndexLabelT *indexLabel, IndexSearchCtxT *indexSearchCtx)
{
    // 当前仅SQL使用indexBy功能，指定索引仅可能是Btree索引
    // gql复用算子，使用hachash和multihash索引
    DB_ASSERT(indexLabel->idxLabelBase.indexType == BTREE_INDEX
#ifdef FEATURE_HAC
              || indexLabel->idxLabelBase.indexType == HAC_HASH_INDEX ||
              indexLabel->idxLabelBase.indexType == MULTI_HASH_INDEX
#endif
    );

    IRExprT *indexedCondTree = NULL;
    IRExprT *nonIndexedCondTree = NULL;
    uint32_t indexedPropNum =
        CheckIndexAgainstConds(memo, indexLabel, indexSearchCtx, &indexedCondTree, &nonIndexedCondTree);
    if (indexedPropNum == INVALID_INDEXED_PROP_NUM) {  // 目前错误退出只会是 OUT_OF_MEMORY
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "check index: %s against conditions.", indexLabel->indexName);
        return GMERR_OUT_OF_MEMORY;
    }
    return UpdateCtxAfterIndexSearch(memo->memCtx, indexSearchCtx, indexLabel, indexedCondTree, nonIndexedCondTree);
}
#endif

static Status SearchIndexWithCtxExtend(MemoT *memo, const IRExprT *scanExpr, IndexSearchCtxT *indexSearchCtx)
{
    OpLogicalAAScanT *scanOp = (OpLogicalAAScanT *)(void *)scanExpr->op;
#if defined(FEATURE_SQL) || defined(FEATURE_GQL)
    if (scanOp->index != NULL) {
        return UpdateCtxBySpecifiedIndex(memo, scanOp->index, indexSearchCtx);
    } else {
        return SearchIndexWithCtxInner(memo, scanOp, indexSearchCtx);
    }
#else
    return SearchIndexWithCtxInner(memo, scanOp, indexSearchCtx);
#endif
}

static Status SearchIndexWithCtx(MemoT *memo, const IRExprT *scanExpr, IndexSearchCtxT *indexSearchCtx)
{
    // 3. 根据收集到的条件搜索可用索引
    Status ret = SearchIndexWithCtxExtend(memo, scanExpr, indexSearchCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // 3.1. 找到了索引
    indexSearchCtx->isIndexFound = DbListGetItemCnt(&indexSearchCtx->indexList) != 0;
    if (indexSearchCtx->isIndexFound) {
        // 还要把未处理的 OR 子树 (如果有) 拼接到不能使用索引的条件的树上
        IRExprT *nonIndexedCondTree = *(IRExprT **)DbListItem(&indexSearchCtx->nonIndexedCondTreeList, 0);
        ret = AddItemExprToItemTree(memo->memCtx, indexSearchCtx->orCondTree, &nonIndexedCondTree);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        return DbSetListItem(&indexSearchCtx->nonIndexedCondTreeList, &nonIndexedCondTree, 0);
    }
    // 3.2. 没有找到索引, 检查有没有未检查的 OR 条件, 如果有, 对 OR 条件触发索引搜索, 否则直接返回
    if (indexSearchCtx->orCondTree == NULL) {
        // 3.2.2. 没有未检查的 OR 条件
        return GMERR_OK;
    }
    ret = SearchIndex4ItemExpr(memo, scanExpr, indexSearchCtx->orCondTree, true, indexSearchCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    indexSearchCtx->isIndexFound = DbListGetItemCnt(&indexSearchCtx->indexList) != 0;
    if (!indexSearchCtx->isIndexFound) {
        // 3.2.2. OR 条件找不到索引
        return GMERR_OK;
    }
    // 3.2.1. OR 条件找到了索引, 更新 nonIndexedCondTreeList
    IRExprT *globalNonIndexedCond = *(IRExprT **)DbListItem(&indexSearchCtx->nonIndexedCondTreeList, 0);
    uint32_t nonIndexedCondTreeCnt = DbListGetItemCnt(&indexSearchCtx->nonIndexedCondTreeList);
    for (uint32_t i = 1; i < nonIndexedCondTreeCnt; ++i) {
        IRExprT *nonIndexedCondTree = *(IRExprT **)DbListItem(&indexSearchCtx->nonIndexedCondTreeList, i);
        ret = AddItemExprToItemTree(memo->memCtx, globalNonIndexedCond, &nonIndexedCondTree);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        ret = DbSetListItem(&indexSearchCtx->nonIndexedCondTreeList, &nonIndexedCondTree, i);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    DbDelListItem(&indexSearchCtx->nonIndexedCondTreeList, 0);
    return GMERR_OK;
}

/**
 * 根据过滤表达式的类型, 将它放入索引搜索上下文的不同数组中. 规则如下:
 * 1. 根节点是不支持c使用索引的运算符: 放入 otherCondList.
 * 2. 根节点是支持使用索引的运算符 (见 IsIndexableCompOp 函数):
 *      2.1 表达式不是 attr op const 的形式: 放入 otherCondList
 *      2.2 表达式是 attr eq const 的形式: 放入 eqCondList
 *      2.3 表达式是 attr comp const 的形式: 放入 compCondList
 * @param[in] itemExpr        过滤表达式
 * @param[out] indexSearchCtx 要更新的索引搜索上下文
 * @return GMERR_OK 或相应的错误码
 */
static Status UpdateIndexSearchCtxByItemExpr(const IRExprT *itemExpr, IndexSearchCtxT *indexSearchCtx)
{
    Status ret = GMERR_OK;
    OpItemBinaryT *itemBinary = (OpItemBinaryT *)(void *)itemExpr->op;
    if (!IsIndexableCompOp(itemBinary->code)) {
        // 1. 根节点是不支持使用索引的二元运算符: 放入 otherCondList
        return DbAppendListItem(&indexSearchCtx->otherCondList, &itemExpr);
    }
    // 2. 根节点是支持使用索引的二元运算符:  条件必须写成右边是常量的形式 (目前不支持其他的形式)
    IRExprT *rightExpr = itemExpr->children[1];
    if (rightExpr->op->type != IR_ITEMOP_CONSTVALUE) {
        // 2.1 不支持使用索引的形式, 放入 otherCondList
        ret = DbAppendListItem(&indexSearchCtx->otherCondList, &itemExpr);
    } else {  // 支持使用索引的形式, 根据根节点的运算符类型, 放入 eqCondProps 或 compCondProps 中
        if (itemBinary->code == COMP_OP_EQ) {
            // 2.2 根节点是等值比较运算符
            ret = DbAppendListItem(&indexSearchCtx->eqCondList, &itemExpr);
        } else {
            // 2.3 根节点是非等值比较运算符
            ret = DbAppendListItem(&indexSearchCtx->compCondList, &itemExpr);
        }
    }
    return ret;
}

#if defined(FEATURE_SQL) || defined(FEATURE_TS)
typedef struct IndexSearchParam {
    MemoT *memo;
    const IRExprT *scanExpr;
    const IRExprT *itemExpr;
    uint32_t arrCnt;
    IndexSearchCtxT *subIdxCtxArr;
} IndexSearchParamT;

// 对 OR 的左右子树搜索可用索引
static Status SearchIndex4OrExprInner(IndexSearchParamT *para, bool *needExit)
{
    Status ret = GMERR_OK;
    uint32_t indexCntInSubCtx[ARITY_OF_BINARY_OP] = {0, 0};  // arrCnt 等于 ARITY_OF_BINARY_OP
    for (uint32_t i = 0; i < para->arrCnt; ++i) {
        // [内存上下文] 外部传入
        // [释放时点] 本函数内
        // [多线程] 不涉及
        ret = InitIndexSearchCtx(para->memo->memCtx, &para->subIdxCtxArr[i]);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            break;
        }
        const IRExprT *itemExpr = (const IRExprT *)(const void *)para->itemExpr->children[i];
        ret = SearchIndex4ItemExpr(para->memo, para->scanExpr, itemExpr, true, &para->subIdxCtxArr[i]);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            break;
        }
        // 2.2.2.1. 有子树找不到索引, 那么整个 OR 语句都找不到索引, 退出
        indexCntInSubCtx[i] = DbListGetItemCnt(&para->subIdxCtxArr[i].indexList);
        if (indexCntInSubCtx[i] == 0) {
            *needExit = true;
            break;
        }
    }
    // 2.2.2.1. 使用的索引总数之和超出阈值, 认为索引扫描的收益低于顺序扫描, 放弃使用索引扫描
    if (indexCntInSubCtx[0] + indexCntInSubCtx[1] > MAX_INDEX_CNT_IN_MULTI_INDEX_OR) {
        *needExit = true;
    }
    return ret;
}

static Status UpdateIdxSearchCtx(uint32_t arrCnt, IndexSearchCtxT *subIdxSearchCtx, IndexSearchCtxT *idxSearchCtx)
{
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < arrCnt; ++i) {
        ret = DbAppendListAll(&idxSearchCtx->indexList, &subIdxSearchCtx[i].indexList);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            break;
        }
        ret = DbAppendListAll(&idxSearchCtx->indexedCondTreeList, &subIdxSearchCtx[i].indexedCondTreeList);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            break;
        }
        ret = DbAppendListAll(&idxSearchCtx->nonIndexedCondTreeList, &subIdxSearchCtx[i].nonIndexedCondTreeList);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            break;
        }
    }
    return ret;
}

static Status SearchIndex4OrExpr(
    MemoT *memo, const IRExprT *scanExpr, const IRExprT *itemExpr, bool doIndexSearch, IndexSearchCtxT *idxSearchCtx)
{
    // 2.1.1. 不查询索引, 保存 OR 条件语句到 orCondTree, 直接返回
    if (!doIndexSearch) {
        idxSearchCtx->orCondTree = itemExpr;
        return GMERR_OK;
    }
    // 2.2.2. 要查询索引
    // 对 OR 的左右子树搜索可用索引
    bool needExit = false;
    IndexSearchCtxT subIdxSearchCtx[ARITY_OF_BINARY_OP] = {0};
    IndexSearchParamT para = {.memo = memo,
        .scanExpr = scanExpr,
        .itemExpr = itemExpr,
        .arrCnt = (uint32_t)ARITY_OF_BINARY_OP,
        .subIdxCtxArr = subIdxSearchCtx};
    Status ret = SearchIndex4OrExprInner(&para, &needExit);
    if (SECUREC_UNLIKELY(ret != GMERR_OK || needExit)) {
        goto EXIT_OR;
    }

    // 2.2.2.2. 左右子树都找得到索引, 更新 OR 节点的信息
    // 将左右子节点的 indexList / indexedCondTreeList / nonIndexedCondTreeList 依次 append 到 OR 节点
    if (*(IRExprT **)(DbListGetItems(&idxSearchCtx->nonIndexedCondTreeList)) == NULL) {
        DbDelListItem(&idxSearchCtx->nonIndexedCondTreeList, 0);
    }

    ret = UpdateIdxSearchCtx((uint32_t)ARITY_OF_BINARY_OP, subIdxSearchCtx, idxSearchCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT_OR;
    }

EXIT_OR:
    for (uint32_t i = 0; i < ARITY_OF_BINARY_OP; ++i) {
        DestroyIndexSearchCtx(&subIdxSearchCtx[i]);
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    idxSearchCtx->isIndexFound = DbListGetItemCnt(&idxSearchCtx->indexList) != 0;
    // 2.2.2.2. 找到索引, 所有信息都保存在搜索上下文中, 直接返回
    // 2.2.2.1. 找不到索引, 那么这个以 OR 为根节点的树整体是不可使用索引的查询条件, 应该放到 nonIndexedCondTreeList[0]
    return idxSearchCtx->isIndexFound ? GMERR_OK : DbSetListItem(&idxSearchCtx->nonIndexedCondTreeList, &itemExpr, 0);
}
#else
static Status SearchIndex4OrExpr(
    MemoT *memo, const IRExprT *scanExpr, const IRExprT *itemExpr, bool doIndexSearch, IndexSearchCtxT *idxSearchCtx)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}
#endif

Status SearchIndex4ItemExpr(
    MemoT *memo, const IRExprT *scanExpr, const IRExprT *itemExpr, bool doIndexSearch, IndexSearchCtxT *indexSearchCtx)
{
    DB_POINTER4(memo, scanExpr, itemExpr, indexSearchCtx);
    Status ret;
    // clang-format off
    /**
     * orCondTree / indexList / indexedCondTreeList / nonIndexedCondTree / eqCondList / compCondList / otherCondList
     * 都是 IndexSearchCtxT 中的成员、
     * 检查过滤条件表达式 itemExpr 的根节点算子的类型, 进行以下处理:
     * 1. 根节点不是二元运算符: 不能使用索引. 这个 itemExpr 整体放到 otherCondList 中.
     * 2. 根节点是二元运算符:
     *      2.1. 根节点是 AND: 递归处理左右孩子. 并且处理左右孩子时不查找索引, 只收集过滤条件 (doIndexSearch 为 false)
     *           然后根据当前递归层次是否要查找索引, 进行以下处理:
     *          2.1.1. 不查询索引: 说明当前是在 AND 的子节点的处理流程中, 不用进一步动作, 直接返回
     *          2.1.2. 要查询索引: 见 SearchIndexWithCtx 函数
     *      2.2. 根节点是 OR: (见 SearchIndex4OrExpr 函数)
     *           根据当前递归层次是否要查找索引, 进行以下处理:
     *          2.2.1. 不查询索引: 说明当前是在 AND 的子节点的处理流程中
     *                            仅把以 OR 为根节点的这个 itemExpr 记录到 orCondTree 中, 不执行其他动作
     *          2.2.2. 要查询索引: 递归处理左右孩子, 并且处理左右孩子时要查找索引 (doIndexSearch 为 true)
     *              2.2.2.1. 左右孩子至少有一个不可用索引或违反其他约束: 这个 OR 语句不能使用索引
     *              2.2.2.2. 左右孩子都有索引: 这个 OR 语句可以使用索引. 合并左右孩子的查询结果. 此时:
     *                  indexList[0~K-1] 中存的是 K 个 OR 的子句各自使用的索引;
     *                  indexedCondTreeList[0~K-1] 中存的是 K 个索引上的查询条件
     *                  nonIndexedCondTree 中保存的是 K 个非索引上的查询条件. 有两种情况:
     *                  如果这个以 OR 为根节点的条件的索引查询是被:
     *                      - 外部触发: 非索引上的条件存在 nonIndexedCondTree[0~K-1]
     *                      - 上层的 AND 条件找不到索引时才触发: 非索引上的条件存在 nonIndexedCondTree[1~K].
     *                                                       [0] 存的是除了这棵 OR 子树以外的其他所有查询条件.
     *      2.3. 根节点是除了 AND 和 OR 以外的其他二元运算符:
     *           更新 eqCondList / compCondList / otherCondList (见 UpdateIndexSearchCtxByItemExpr)
     *           然后根据当前递归层次是否要查找索引, 进行以下处理:
     *           2.3.1. 不查询索引: (同 2.1.1) 说明当前是在 AND 的子节点的处理流程中, 不用进一步动作, 直接返回
     *           2.3.2. 要查询索引: (同 2.1.2) 见 SearchIndexWithCtx 函数
     */
    // clang-format on
    if (itemExpr->op->type != IR_ITEMOP_BINARY) {
        // 1. 根节点不是二元运算符
        ret = DbAppendListItem(&indexSearchCtx->otherCondList, &itemExpr);
    } else {
        // 2. 根节点是二元运算符
        OpItemBinaryT *itemBinary = (OpItemBinaryT *)(void *)itemExpr->op;
        if (itemBinary->code == LOGIC_OP_AND) {
            // 2.1 根节点是 AND: 递归处理左右孩子
            ret = SearchIndex4ItemExpr(memo, scanExpr, itemExpr->children[0], false, indexSearchCtx);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
            ret = SearchIndex4ItemExpr(memo, scanExpr, itemExpr->children[1], false, indexSearchCtx);
        } else if (itemBinary->code == LOGIC_OP_OR) {
            // 2.2 根节点是 OR: 在以下函数中处理, 处理完毕直接返回
            return SearchIndex4OrExpr(memo, scanExpr, itemExpr, doIndexSearch, indexSearchCtx);
        } else {
            // 2.3 其他情况, 更新 indexSearchCtx 中的 eqCondList / compCondList / otherCondList
            ret = UpdateIndexSearchCtxByItemExpr(itemExpr, indexSearchCtx);
        }
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (!doIndexSearch) {  // 2.1.1. 或 2.3.1. 说明是在收集条件的流程中, 不用在这个递归层次查找索引. 直接返回
        return ret;
    }
    /**
     * 3. 根据收集到的条件搜索最优索引. 如果:
     *     3.1. 找到了索引:
     *          indexList[0] 存放的 DmVlIndexLabelT * 是这个表达式要使用的索引
     *          indexedCondTreeList[0] 存放的 IRExprT * 是能使用索引的条件的树
     *          nonIndexedCondTreeList[0] 存放的 IRExprT * 是不能使用索引的条件的树
     *     3.2. 找不到索引:
     *          indexList / indexedCondTreeList 为空
     *          nonIndexedCondTreeList[0] 存放的 IRExprT * 是不能使用索引的条件的树 (不包含 OR 条件)
     *          如果搜索上下文中有未检查的 OR 条件, 利用这个 OR 条件查找索引. 如果:
     *          3.2.1. OR 条件找到了索引:
     *              indexList[0~K-1] 中存放的就是每个 OR 子句需要使用的索引
     *              indexedCondTreeList[0~K-1] 中存放的是每个子句能使用索引的条件
     *              nonIndexedCondTreeList[1~K] 中存放的是每个子句中不能使用索引的条件
     *              此时还要把 nonIndexedCondTreeList[0] 拼接到 [1~K] 的树上, 然后删除 [0]
     *              最终返回的 nonIndexedCondTreeList[0~K] 中存放的是每个子句中不能使用索引的条件
     *          3.2.2. 没有未检查的 OR 条件或 OR 条件找不到索引:
     *              说明整棵树都无法使用索引, 要把入参 itemExpr 放到 nonIndexedCondTreeList[0]
     *              此时 indexList / indexedCondTreeList 为空
     *              nonIndexedCondTreeList[0] 是整个 itemExpr
     */
    ret = SearchIndexWithCtx(memo, scanExpr, indexSearchCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (!indexSearchCtx->isIndexFound) {
        // 3.2.2. 最终没有找到索引
        return DbSetListItem(&indexSearchCtx->nonIndexedCondTreeList, &itemExpr, 0);
    }
    return GMERR_OK;
}
