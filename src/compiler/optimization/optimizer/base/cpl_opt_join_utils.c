/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Implementation for JOIN 需要的一些辅助函数
 * Author: zhuangyifeng
 * Create: 2023-05-10
 */

#include "xxh3.h"

#include "cpl_ir_logical_op.h"
#include "cpl_opt_optimizer_utils.h"
#include "cpl_opt_eq_class.h"
#include "cpl_ir_func_op.h"
#include "cpl_opt_join_utils.h"

/**
 * @brief 获取 JOIN 的左右两个孩子中的公共字段个数. 对应 IR 中的原函数为 IRGetCommonPropsCnt
 * @param[in] leftChildOp  JOIN 的左孩子的 op
 * @param[in] rightChildOp JOIN 的右孩子的 op
 * @param[in] idMap id -> prop 的映射表
 * @param[in] eqClassMap 属性等价类表
 * @return 返回两个 op 的 propAA 中的公共字段个数
 */
uint32_t OptGetCommonKeyNumForJoin(OpBaseT *leftChildOp, OpBaseT *rightChildOp, EqClassT *eqClass)
{
    DB_POINTER3(leftChildOp, rightChildOp, eqClass);
    AAVertexDescT *leftPropAA = &IRGetOpAASchema(leftChildOp)->propAA;
    AAVertexDescT *rightPropAA = &IRGetOpAASchema(rightChildOp)->propAA;
    uint32_t commonPropsCnt = 0;
    for (uint32_t i = 0; i < leftPropAA->propNum; ++i) {
        for (uint32_t j = 0; j < rightPropAA->propNum; ++j) {
            if (EqClassArePropsEqual(
                    eqClass, leftPropAA->properties[i]->propeId, rightPropAA->properties[j]->propeId)) {
                ++commonPropsCnt;
                break;
            }
        }
    }
    return commonPropsCnt;
}

typedef struct JoinParam {
    uint32_t commonPropNum;        // 左右孩子共同字段数量
    uint32_t unionPropNum;         // 左右孩子合并且去重后的字段数量
    uint32_t *leftCommonPropIds;   // 左孩子中共同字段的全局 id 列表
    uint32_t *rightCommonPropIds;  // 右孩子中共同字段的全局 id 列表
    uint32_t *unionPropIds;  // 左右孩子合并且去重以后的全局 id 列表. 去重指的是在相同等价类中的两个 id 只留一个.
} JoinParamT;

static Status CreateJoinParamPropIds(JoinParamT *joinParam, DbMemCtxT *memCtx)
{
    size_t size = (size_t)joinParam->commonPropNum * sizeof(uint32_t);
    // joinParam 的数组成员初始化
    // [内存上下文] 优化器实例的内存上下文 optimizer memCtx
    // [释放时期] OptQryOptimize 函数结束时随 optimizer memCtx 一起销毁
    // [并发支持] 不涉及
    Status ret = CreateObjectWithSize(memCtx, (void **)&joinParam->leftCommonPropIds, size, "leftCommonPropIds");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // [内存上下文] 优化器实例的内存上下文 optimizer memCtx
    // [释放时期] OptQryOptimize 函数结束时随 optimizer memCtx 一起销毁
    // [并发支持] 不涉及
    ret = CreateObjectWithSize(memCtx, (void **)&joinParam->rightCommonPropIds, size, "rightCommonPropIds");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    size = (size_t)joinParam->unionPropNum * sizeof(uint32_t);
    // [内存上下文] 优化器实例的内存上下文 optimizer memCtx
    // [释放时期] OptQryOptimize 函数结束时随 optimizer memCtx 一起销毁
    // [并发支持] 不涉及
    return CreateObjectWithSize(memCtx, (void **)&joinParam->unionPropIds, size, "[CreateJoinParam]union prop ids");
}

typedef struct GetAndUpdateJoinParamArgs {
    DbMemCtxT *memCtx;
    EqClassT *eqClass;
    OpBaseT *leftChildOp;
    OpBaseT *rightChildOp;
} GetAndUpdateJoinParamArgsT;

static void UpdateJoinParamPropIds(EqClassT *eqClass, AAVertexDescT *leftPropAA, AAVertexDescT *rightPropAA,
    uint32_t commonPropNum, JoinParamT *joinParam)
{
    uint32_t unionPropCnt = 0;
    uint32_t commonPropCnt = 0;
    DmPropertySchemaT **leftProps = leftPropAA->properties;
    DmPropertySchemaT **rightProps = rightPropAA->properties;
    for (uint32_t i = 0; i < leftPropAA->propNum; ++i) {
        joinParam->unionPropIds[unionPropCnt++] = leftProps[i]->propeId;
    }
    for (uint32_t i = 0; i < rightPropAA->propNum; ++i) {
        uint32_t j;
        for (j = 0; j < leftPropAA->propNum; ++j) {
            if ((EqClassArePropsEqual(eqClass, rightProps[i]->propeId, leftProps[j]->propeId))) {
                break;
            }
        }
        if (j != leftPropAA->propNum) {  // 找到一个 join 字段
            joinParam->leftCommonPropIds[commonPropCnt] = leftProps[j]->propeId;
            joinParam->rightCommonPropIds[commonPropCnt] = rightProps[i]->propeId;
            ++commonPropCnt;
        } else {  // 找到右孩子的一个非 join 字段
            joinParam->unionPropIds[unionPropCnt++] = rightProps[i]->propeId;
        }
    }
    DB_ASSERT(commonPropNum == commonPropCnt && joinParam->unionPropNum == unionPropCnt);
}

static Status GetJoinParam(GetAndUpdateJoinParamArgsT *funcArgs, JoinParamT *joinParam)
{
    DbMemCtxT *memCtx = funcArgs->memCtx;
    EqClassT *eqClass = funcArgs->eqClass;
    OpBaseT *leftChildOp = funcArgs->leftChildOp;
    OpBaseT *rightChildOp = funcArgs->rightChildOp;

    DB_POINTER4(eqClass, leftChildOp, rightChildOp, joinParam);
    AAVertexDescT *leftPropAA = &IRGetOpAASchema(leftChildOp)->propAA;
    AAVertexDescT *rightPropAA = &IRGetOpAASchema(rightChildOp)->propAA;
    DB_ASSERT(leftPropAA->propNum > 0 && leftPropAA->propNum <= IR_AASCHEMA_MAX_PROP_NUM);
    DB_ASSERT(rightPropAA->propNum > 0 && rightPropAA->propNum <= IR_AASCHEMA_MAX_PROP_NUM);
    // 计算 JOIN 字段数量和 JOIN 后的总字段数量
    uint32_t commonPropNum = OptGetCommonKeyNumForJoin(leftChildOp, rightChildOp, eqClass);
    joinParam->commonPropNum = commonPropNum;
    joinParam->unionPropNum = leftPropAA->propNum + rightPropAA->propNum - commonPropNum;

    Status ret = CreateJoinParamPropIds(joinParam, memCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 更新 joinParam 的 left/rightCommonPropIds 和 unionPropIds
    UpdateJoinParamPropIds(eqClass, leftPropAA, rightPropAA, commonPropNum, joinParam);
    return GMERR_OK;
}

static Status UpdateJoinOpByJoinParam(DbMemCtxT *memCtx, DbOamapT *idMap, OpBaseT *joinOp, JoinParamT *joinParam)
{
    DB_POINTER3(idMap, joinOp, joinParam);
    OpLogicalAAJoinT *join = (OpLogicalAAJoinT *)(void *)joinOp;
    // 填充 join 算子的属性
    join->joinKeyNum = joinParam->commonPropNum;
    join->leftKeyIds = joinParam->leftCommonPropIds;
    join->rightKeyIds = joinParam->rightCommonPropIds;
    // 更新joinType
    if (join->joinType == IR_JOIN_INNER && join->joinKeyNum == 0) {
        join->joinType = IR_JOIN_FULL;
    } else if (join->joinType == IR_JOIN_FULL && join->joinKeyNum > 0) {
        join->joinType = IR_JOIN_INNER;
    }
    // 填充 join 的 propAA
    AASchemaT *joinSchema = IRGetOpAASchema(joinOp);
    Status ret = IRInitAASchema(memCtx, joinParam->unionPropNum, joinSchema);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // unable to update join op by join param
        DB_LOG_ERROR(ret, "init AASchema.");
        return ret;
    }
    // 填充 properties
    for (uint32_t i = 0; i < joinParam->unionPropNum; ++i) {
        uint32_t propId = joinParam->unionPropIds[i];
        joinSchema->propAA.properties[i] = *(DmPropertySchemaT **)DbOamapLookup(idMap, propId, &propId, NULL);
        DB_ASSERT(joinSchema->propAA.properties[i] != NULL);
    }
    return GMERR_OK;
}

Status OptUpdateJoin(DbMemCtxT *memCtx, MemoT *memo, OpBaseT *joinOp, OpBaseT *leftChildOp, OpBaseT *rightChildOp)
{
    DB_POINTER4(memo, leftChildOp, rightChildOp, joinOp);
    DB_POINTER2(&memo->idMap, &memo->eqClass);

    JoinParamT joinParam;
    GetAndUpdateJoinParamArgsT funcArgs = {0};
    funcArgs.memCtx = memCtx;
    funcArgs.eqClass = &memo->eqClass;
    funcArgs.leftChildOp = leftChildOp;
    funcArgs.rightChildOp = rightChildOp;
    Status ret = GetJoinParam(&funcArgs, &joinParam);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "get join param.");
        return ret;
    }
    // 更新 JOIN 算子的属性
    return UpdateJoinOpByJoinParam(memCtx, &memo->idMap, joinOp, &joinParam);
}

static void SwapTwoExprs(IRExprT *left, IRExprT *right)
{
    if (left->op == right->op) {
        return;
    }

    IRExprT *leftJoin = left->father;
    IRExprT *rightJoin = right->father;

    leftJoin->children[1] = right;
    right->father = leftJoin;
    rightJoin->children[1] = left;
    left->father = rightJoin;
}

uint32_t FindMostCommonFiledLabelIdx(DbListT *scanExprList, IRExprT *leftJoin, bool *visited, EqClassT *eqClass)
{
    uint32_t leafLabelCnt = DbListGetItemCnt(scanExprList);
    uint32_t outIndex = 0;
    int32_t maxCommonCnt = -1;
    for (uint32_t i = 0; i < leafLabelCnt; i++) {
        if (visited[i]) {
            continue;
        }
        IRExprT *rightLabel = *(IRExprT **)DbListItem(scanExprList, i);
        uint32_t commonCnt = OptGetCommonKeyNumForJoin(leftJoin->children[0]->op, rightLabel->op, eqClass);
        if ((int32_t)commonCnt > maxCommonCnt) {
            maxCommonCnt = (int32_t)commonCnt;
            outIndex = i;
        }
    }
    DB_ASSERT(maxCommonCnt != -1);  // 公共字段最少为0，因此maxCommonCnt不可能等于-1

    visited[outIndex] = true;
    return outIndex;
}

Status JoinReorder(EqClassT *eqClass, DbListT *scanExprList, IRExprT *leftJoin, bool *visited)
{
    // 每次找到跟当前join的左表 有最多join条件的表，将其作为当前join的右表
    uint32_t rightLabelIdx = FindMostCommonFiledLabelIdx(scanExprList, leftJoin, visited, eqClass);

    IRExprT *rightLabel = *(IRExprT **)DbListItem(scanExprList, rightLabelIdx);
    IRExprT *rightJoin = IRGet1stAncestorExprByTypes(rightLabel, 1, IR_LOGOP_JOIN);  // 逻辑计划join算子只有1种
    DB_ASSERT(rightJoin != NULL);

    SwapTwoExprs(leftJoin->children[1], rightJoin->children[1]);

    // 优化结束条件1：当前join的父join的右孩子是udf
    if (leftJoin->father->op->type == IR_LOGOP_JOIN) {
        if (leftJoin->father->children[1]->op->type == IR_FUNCOP_UDF ||
            leftJoin->father->children[1]->op->type == IR_FUNCOP_STATE_TRANSFER_UDF) {
            return GMERR_OK;
        }
    }
    // 优化结束条件2：当前join的父亲不是join
    if (leftJoin->father->op->type != IR_LOGOP_JOIN) {
        return GMERR_OK;
    }
    // 否则继续优化
    return JoinReorder(eqClass, scanExprList, leftJoin->father, visited);
}

Status UpdateOpAfterJoinOrderOpt(MemoT *memo, IRExprT *expr)
{
    if (expr->op->type == IR_LOGOP_JOIN) {
        Status ret = OptUpdateJoin(memo->memCtx, memo, expr->op, expr->children[0]->op, expr->children[1]->op);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "update join.");
            return ret;
        }
        return UpdateOpAfterJoinOrderOpt(memo, expr->father);
    }
    return GMERR_OK;
}

/**
 * @brief join顺序优化
 * @param[in] memo memo.
 * @param[in/out] plan 待处理的计划
 * @return 优化成功返回 GMERR_OK, 否则返回相应的错误码
 */
Status OptimizeJoinOrder(MemoT *memo, IRPlanT *plan)
{
    DB_POINTER2(memo, plan);
    if (plan->noReorder) {
        return GMERR_OK;
    }
    DbMemCtxT *memCtx = memo->memCtx;
    DbListT scanExprList;
    DbCreateList(&scanExprList, sizeof(IRExprT *), memCtx);
    Status ret = IRGetLeafExprListByTypes(
        plan->root, &scanExprList, 2, IR_LOGOP_SCANWORKLABEL, IR_LOGOP_SCAN);  // 目前逻辑scan只有2种
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get leaf labels of plan.");
        return ret;
    }

    // 不包含连接算子时，此时只会出现一张输入表或为Merge plan，或者包含聚合算子，不用做优化
    // 全为Func算子参与连接时，此时没有输入表，不用做优化
    uint32_t leafLabelCnt = DbListGetItemCnt(&scanExprList);
    if (leafLabelCnt == 0 || leafLabelCnt == 1) {
        return GMERR_OK;
    }

    // 包含特殊连接，不用优化
    if (IRIsPlanContainsAntiJoin(plan->root)) {
        return GMERR_OK;
    }

    // memCtx：由外层传入，并由外层管理释放；并发：不支持，外部模块保证线程安全
    bool *visited = DbDynMemCtxAlloc(memCtx, sizeof(bool) * leafLabelCnt);
    if (visited == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_OUT_OF_MEMORY, "malloc, leaf labels array, size:%" PRIu32, (uint32_t)sizeof(bool) * leafLabelCnt);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(visited, leafLabelCnt * sizeof(bool), 0x00, leafLabelCnt * sizeof(bool));
    visited[0] = true;

    IRExprT *deltaLabel = *(IRExprT **)DbListItem(&scanExprList, 0);
    IRExprT *firstJoin = IRGet1stAncestorExprByTypes(deltaLabel, 1, IR_LOGOP_JOIN);  // 逻辑计划join算子只有1种

    // 多表之间连接，必定会出现Join算子
    DB_ASSERT(firstJoin != NULL);
    ret = JoinReorder(&memo->eqClass, &scanExprList, firstJoin, visited);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = UpdateOpAfterJoinOrderOpt(memo, firstJoin);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}
