/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Header for prop id converter.
 * Author: zhuangyifeng
 * Create: 2023-04-27
 * Notes: id converter 的作用是在优化器执行前后做一次属性的局部 id <-> 全局 id 的转换.
 */

#ifndef CPL_OPT_ID_CONVERTER_H
#define CPL_OPT_ID_CONVERTER_H

#include "db_hashmap.h"
#include "cpl_ir_plan.h"
#include "cpl_opt_eq_class.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 优化器的预处理操作:
 * 1. 修改以 root 为根节点的 IRExpr tree. 为树中出现过的属性赋予全局唯一的 ID, 并修改局部 id 为全局 id.
 * 2. 将初始计划中所有自然连接的 JOIN KEY 加入到对应的等价类中. 所有等价的属性的全局 ID 会映射到同一个等价类 ID 上.
 *    等价类的 ID 也是一个全局属性 ID, 即第一个加入这个等价类的属性的全局 ID.
 * 注 1. 目前只对 SCAN 和 UDF 中出现的属性赋予全局唯一的 ID. 并构造一张 id->property 的映射表 idMap.
 *    目前不考虑上层算子中出现的计算属性.
 * 注 2. 全局唯一的 ID 会覆盖原先的propId
 * @param[in] root              待修改的 IRExpr 树的根节点
 * @param[in/out] idMap         属性的全局ID -> <DmPropertySchema *> 的映射表
 * @param[in/out] eqClass    属性的全局ID -> eqClassId 的等价类表 (equivalence class)
 * @param[in] tableIdMap        优化器中的表ID -> vertexLabel中的metaId
 * @return 执行成功返回 GMERR_OK, 否则返回对应的错误码.
 */
Status CvtIdsLocal2Global(IRExprT *root, DbOamapT *idMap, EqClassT *eqClass, DbListT *tableIdMap);

/**
 * @brief 修改以 root 为根节点的 IRExpr tree. 将树中所有的全局 id 修改为局部 id
 * @param[in] root  待修改的 IRExpr 树的根节点
 * @param[in] idMap propId -> DmPropertySchema 的映射表
 * @param[in] eqClassMap 属性的等价类
 */
void CvtIdsGlobal2Local(IRExprT *root, EqClassT *eqClass);

#ifdef __cplusplus
}
#endif
#endif
