/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Header for JOIN 需要的一些辅助函数
 * Author: zhuangyifeng
 * Create: 2023-05-10
 */

#ifndef CPL_OPT_JOIN_UTILS_H
#define CPL_OPT_JOIN_UTILS_H

#include "cpl_ir_aa_schema.h"
#include "cpl_opt_memo.h"
#include "cpl_opt_id_converter.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 更新 JOIN 算子的 schema. 对应 IR 中的原函数为 IRUpdateJoin
 * @param[in] memo 优化器的搜索空间实例. 其实需要的是 memCtx, id -> prop 的映射表和属性等价类表
 * @param[in] joinOp 要更新的 join 算子
 * @param[in] leftChildOp JOIN 的左孩子的 op
 * @param[in] rightChildOp JOIN 的右孩子的 op
 * @return 更新成功返回 GMERR_OK, 否则返回相应的错误码
 */
Status OptUpdateJoin(DbMemCtxT *memCtx, MemoT *memo, OpBaseT *joinOp, OpBaseT *leftChildOp, OpBaseT *rightChildOp);

/**
 * @brief 获取 JOIN 的左右两个孩子中的公共字段个数. 对应 IR 中的原函数为 IRGetCommonPropsCnt
 * @param[in] leftChildOp  JOIN 的左孩子的 op
 * @param[in] rightChildOp JOIN 的右孩子的 op
 * @param[in] idMap id -> prop 的映射表
 * @param[in] eqClassMap 属性等价类表
 * @return 返回两个 op 的 propAA 中的公共字段个数
 */
uint32_t OptGetCommonKeyNumForJoin(OpBaseT *leftChildOp, OpBaseT *rightChildOp, EqClassT *eqClass);

Status OptimizeJoinOrder(MemoT *memo, IRPlanT *plan);

Status UpdateOpAfterJoinOrderOpt(MemoT *memo, IRExprT *expr);

#ifdef __cplusplus
}
#endif
#endif
