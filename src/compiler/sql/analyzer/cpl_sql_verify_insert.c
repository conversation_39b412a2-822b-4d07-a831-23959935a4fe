/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: the impl for SQL verify insert analyzer
 * Author: SQL
 * Create: 2024-01-30
 */

#include "dm_meta_basic_in.h"
#include "cpl_sql_analyzer_common.h"
#include "cpl_sql_verify_select.h"
#include "cpl_sql_verify_insert.h"
#include "cpl_sql_analyzer_hidden_column.h"

static Status SqlCheckColumnNameValid(
    const char *column, uint32_t propeNum, const DmPropertySchemaT *properties, bool *propeHitArray)
{
    Status ret = GMERR_OK;
    uint32_t i = 0;
    for (; i < propeNum; ++i) {
        if (DbStrCmp(properties[i].name, column, true) != 0) {
            continue;
        }
        if (propeHitArray[i]) {
            ret = GMERR_SYNTAX_ERROR;
        } else {
            propeHitArray[i] = true;
        }
        break;
    }
    if (i == propeNum) {
        ret = GMERR_UNDEFINE_COLUMN;
    }
    return ret;
}

static Status SqlVerifyColumnList(DbListT *columnList, DmVertexLabelT *vertexLabel)
{
    if (columnList == NULL) {
        return GMERR_OK;
    }
    DmPropertySchemaT *properties = vertexLabel->metaVertexLabel->schema->properties;
    uint32_t propeNum = vertexLabel->metaVertexLabel->schema->propeNum;
    uint32_t allocSize = propeNum * sizeof(bool);
    bool *propeHitArray = (bool *)DbDynMemCtxAlloc(vertexLabel->memCtx, allocSize);
    if (propeHitArray == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(propeHitArray, allocSize, 0, allocSize);
    Status ret = GMERR_OK;
    char *column = NULL;
    uint32_t columnCnt = DbListGetItemCnt(columnList);
    for (uint32_t i = 0; i < columnCnt; ++i) {
        column = *(char **)DbListItem(columnList, i);
        ret = SqlCheckColumnNameValid(column, propeNum, properties, propeHitArray);
        if (ret != GMERR_OK) {
            break;
        }
    }
    DbDynMemCtxFree(vertexLabel->memCtx, propeHitArray);
    if (ret == GMERR_SYNTAX_ERROR) {
        SQL_ERROR_LOG(ret, "Sql analyzer: duplicated column|%s| for insert.", column);
    } else if (ret == GMERR_UNDEFINE_COLUMN) {
        SQL_ERROR_LOG(ret, "Sql analyzer: undefined column|%s| for insert.", column);
    }
    return ret;
}

Status SqlVerifyColumnCnt(DbListT *valuesList, DbListT *columnList, DmVertexLabelT *vertexLabel)
{
    uint32_t valueRowsCnt = (valuesList == NULL) ? 0 : DbListGetItemCnt(valuesList);
    if (valueRowsCnt == 0) {
        SQL_ERROR_LOG(GMERR_SYNTAX_ERROR, "Sql analyzer: the empty value list is not allowed when insert by values.");
        return GMERR_SYNTAX_ERROR;
    }

    uint32_t propNum = vertexLabel->metaVertexLabel->schema->propeNum;
    uint32_t hiddenNum = 0;
    for (uint32_t i = 0; i < propNum; ++i) {
        if (IsSqlHiddenColumn(vertexLabel->metaVertexLabel->schema->properties[i].name)) {
            hiddenNum++;
        }
    }
    propNum -= hiddenNum;
    uint32_t columnNum = (columnList == NULL) ? propNum : DbListGetItemCnt(columnList);
    for (uint32_t i = 0; i < valueRowsCnt; ++i) {
        DbListT *valueRow = *(DbListT **)DbListItem(valuesList, i);
        uint32_t valueNum = DbListGetItemCnt(valueRow);
        if (valueNum != columnNum) {
            SQL_ERROR_LOG(GMERR_SYNTAX_ERROR, "Sql analyzer: the values count does not match the column count.");
            return GMERR_SYNTAX_ERROR;
        }
    }
    return GMERR_OK;
}

static Status SqlVerifyValues(
    SessionT *session, DbMemCtxT *memCtx, SqlInsertValueT *value, DbListT *columnList, DmVertexLabelT *vertexLabel)
{
    switch (value->type) {
        case VALUES_LIST:
            return SqlVerifyColumnCnt(value->valuesList, columnList, vertexLabel);
        case DEFAULT_VALUES:
            if (value->valuesList != NULL) {
                SQL_ERROR_LOG(GMERR_SYNTAX_ERROR, "Sql analyzer: value list is not allowed when insert defaults.");
                return GMERR_SYNTAX_ERROR;
            }
            break;
#ifndef IDS_HAOTIAN
        case SELECT_SUBQUERY:
            // verify subquery
            {
                SqlSelectStmtT *select = (SqlSelectStmtT *)(void *)(value->selectSubquery);
                return SqlVerifySelectStmt(session, memCtx, select);
            }
#endif
        default:
            SQL_ERROR_LOG(GMERR_SYNTAX_ERROR, "Sql analyzer: value type is not expected in the insert statement.");
            return GMERR_SYNTAX_ERROR;
    }
    return GMERR_OK;
}
#ifdef IDS_HAOTIAN
static Status SqlVerifyInsertStmtHaoTian(SqlInsertStmtT *insertStmt)
{
    if (insertStmt->conflictStrategy != RESOLVE_NONE) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupported conflict strategy.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    if (insertStmt->value->type == SELECT_SUBQUERY) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupported subquery in insert.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    if (insertStmt->returningClause != NULL) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupported return clause in insert.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}
#endif

Status SqlVerifyInsertStmt(
    SessionT *session, DbMemCtxT *memCtx, SqlInsertStmtT *insertStmt, DmVertexLabelT **vertexLabel)
{
    DB_POINTER4(session, memCtx, insertStmt, vertexLabel);
    Status ret = GMERR_OK;
#ifdef IDS_HAOTIAN
    ret = SqlVerifyInsertStmtHaoTian(insertStmt);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif
    uint32_t dbId = session->dbId;
    char *nspName = insertStmt->table->database;
    char *tableName = insertStmt->table->tableName;
    uint32_t nspId = session->namespaceId;
    if (SqlCheckIsSysTableName(tableName)) {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer the name(%s) is same as system table when inserting.",
            tableName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(session->memCtx);
    ret = SqlGetNspIdAndVertexLabelByName(dbInstance, dbId, nspName, tableName, &nspId, vertexLabel);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Sql analyzer: unable to get the id of namespace|%s| for insert.", nspName);
        return ret;
    }
    if (*vertexLabel == NULL) {
        SQL_ERROR_LOG(GMERR_UNDEFINED_TABLE, "Sql analyzer: undefined table|%s| for insert.", tableName);
        return GMERR_UNDEFINED_TABLE;
    }
    // 如果操作的对象是视图，则报错，视图不允许做DML操作
    if (IsSqlViewScenario(*vertexLabel)) {
        SQL_ERROR_LOG(GMERR_SEMANTIC_ERROR, "Sql analyzer: insert operation is not allowed on view(%s)", tableName);
        return GMERR_SEMANTIC_ERROR;
    }
    ret = SqlVerifyColumnList(insertStmt->columnList, *vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    // upsert statement column list also need check
    if (insertStmt->upsertClause != NULL) {
        ret = SqlVerifyColumnList(insertStmt->upsertClause->columnList, *vertexLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return SqlVerifyValues(session, memCtx, insertStmt->value, insertStmt->columnList, *vertexLabel);
}
