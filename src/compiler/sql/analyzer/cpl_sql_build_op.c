/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: the impl for build IR logical op
 * Author: SQL
 * Create: 2024-01-30
 */

#include <ctype.h>
#include "adpt_types.h"
#include "cpl_ir_func_op.h"
#include "cpl_sql_analyzer_common.h"
#include "cpl_sql_build_expr.h"
#include "cpl_sql_build_op.h"
#include "cpl_sql_build_ir_tree.h"
#include "cpl_sql_analyzer_hidden_column.h"

static Status SqlCheckColumn4SetClause(
    DbMemCtxT *memCtx, DmPropertySchemaT *properties, uint32_t propId, SqlExprT *sqlExpr);

Status SqlInitSchemaForScanNullable(DbMemCtxT *memCtx, DmVertexLabelT *label, AASchemaT *schema)
{
    DB_POINTER3(memCtx, label, schema);
    Status ret = IRInitAASchemaWithLabel(memCtx, label, schema);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = 0; i < schema->propAA.propNum; ++i) {
        schema->propAA.properties[i]->isNullable = true;
    }
    return GMERR_OK;
}

Status SqlCreateScanExprWithLabel(DbMemCtxT *memCtx, DmVertexLabelT *label, IRExprT **retExpr)
{
    DB_POINTER3(memCtx, label, retExpr);
    IRExprT *scanExpr = NULL;
    // this function will also assign type and arity
    Status ret = IRCreateExprWithOp(memCtx, IR_LOGOP_SCAN, &scanExpr);
    if (ret != GMERR_OK) {
        return ret;
    }
    OpLogicalAAScanT *scanOp = (OpLogicalAAScanT *)(void *)(scanExpr->op);
    // assign label
    scanOp->vertexLabel = label;
    // create and assign schema
    ret = SqlInitSchemaForScanNullable(memCtx, label, &(scanOp->schema));
    if (ret != GMERR_OK) {
        return ret;
    }

    *retExpr = scanExpr;
    return GMERR_OK;
}

Status SqlCreateIndexScanExprWithLabel(
    DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, DmVlIndexLabelT *indexLabel, IRExprT **retExpr)
{
    DB_POINTER4(memCtx, vertexLabel, indexLabel, retExpr);
    IRExprT *indexScanExpr = NULL;
    // this function will also assign type and arity
    Status ret = IRCreateExprWithOp(memCtx, IR_LOGOP_INDEXFILTERSCAN, &indexScanExpr);
    if (ret != GMERR_OK) {
        return ret;
    }
    OpLogicalAAIndexFilterScanT *indexScanOp = (OpLogicalAAIndexFilterScanT *)(void *)(indexScanExpr->op);
    // assign label
    indexScanOp->vertexLabel = vertexLabel;
    indexScanOp->index = indexLabel;
    // create and assign schema
    ret = SqlInitSchemaForScanNullable(memCtx, vertexLabel, &(indexScanOp->schema));
    if (ret != GMERR_OK) {
        return ret;
    }

    *retExpr = indexScanExpr;
    return GMERR_OK;
}

#ifndef IDS_HAOTIAN
static inline IRJoinTypeE SqlCvtJoinTypeToIR(uint8_t sqlJoinType)
{
    return (sqlJoinType & (SQL_LEFT_JOIN | SQL_OUTER_JOIN)) != 0 ? IR_JOIN_LEFT : IR_JOIN_INNER;
}

static Status SqlVerifyAndFillUsingProp(
    char *usingProp, uint32_t propNum, DmPropertySchemaT **props, uint32_t *keyIds, uint32_t keyIndex)
{
    bool isExist = false;
    for (uint32_t j = 0; j < propNum; j++) {
        if (strcmp(props[j]->name, usingProp) == 0) {
            keyIds[keyIndex] = j;
            isExist = true;
            break;
        }
    }
    if (!isExist) {
        SQL_ERROR_LOG(GMERR_UNDEFINE_COLUMN, "Sql analyzer:table undefined prop|%s| for usingList.", usingProp);
        return GMERR_UNDEFINE_COLUMN;
    }
    return GMERR_OK;
}

static Status SqlVerifyAndUpdateNaturalJoinWithUsingList(
    DbMemCtxT *memCtx, AASchemaT *leftSchema, AASchemaT *rightSchema, DbListT *usingList, OpLogicalAAJoinT *naturalJoin)
{
    DmPropertySchemaT **leftProps = leftSchema->propAA.properties;
    DmPropertySchemaT **rightProps = rightSchema->propAA.properties;
    uint32_t leftPropsNum = leftSchema->propAA.propNum;
    uint32_t rightPropsNum = rightSchema->propAA.propNum;

    naturalJoin->joinKeyNum = DbListGetItemCnt(usingList);
    naturalJoin->leftKeyIds = DbDynMemCtxAlloc(memCtx, sizeof(uint32_t) * (naturalJoin->joinKeyNum));
    naturalJoin->rightKeyIds = DbDynMemCtxAlloc(memCtx, sizeof(uint32_t) * (naturalJoin->joinKeyNum));
    for (uint32_t i = 0; i < naturalJoin->joinKeyNum; i++) {
        char *usingProp = *(char **)DbListItem(usingList, i);
        Status ret = SqlVerifyAndFillUsingProp(usingProp, leftPropsNum, leftProps, naturalJoin->leftKeyIds, i);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = SqlVerifyAndFillUsingProp(usingProp, rightPropsNum, rightProps, naturalJoin->rightKeyIds, i);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    AACommonPropIdsParaT commonPropIds = {0};
    commonPropIds.commonPropsNum = naturalJoin->joinKeyNum;
    commonPropIds.leftCommonPropIds = naturalJoin->leftKeyIds;
    commonPropIds.rightCommonPropIds = naturalJoin->rightKeyIds;
    return IRJoinAASchemaByCommonPropIdx(memCtx, leftSchema, rightSchema, &commonPropIds, &naturalJoin->schema);
}

static Status SqlCreateNaturalJoinExpr(
    DbMemCtxT *memCtx, SqlSrcItemT *src, const IRExprT *left, const IRExprT *right, IRExprT **joinExpr)
{
    // build natural join expr
    Status ret = IRCreateExprWithOp(memCtx, IR_LOGOP_JOIN, joinExpr);
    if (ret != GMERR_OK) {
        return ret;
    }
    OpLogicalAAJoinT *naturalJoin = (OpLogicalAAJoinT *)(void *)(*joinExpr)->op;
    naturalJoin->joinType = SqlCvtJoinTypeToIR(src->joinType);
    AASchemaT *leftSchema = IRGetOpAASchema(left->op);
    AASchemaT *rightSchema = IRGetOpAASchema(right->op);
    // this function will assign the fields (i.e., joinKeyNum, leftIds, rightIds and Schema)
    if (src->usingList != NULL) {
        return SqlVerifyAndUpdateNaturalJoinWithUsingList(memCtx, leftSchema, rightSchema, src->usingList, naturalJoin);
    } else {
        return IRUpdateJoin(memCtx, leftSchema, rightSchema, naturalJoin);
    }
}

static Status SqlCreateCartesianProductExpr(
    SqlBuildQueryPlanOptT *opt, SqlSrcItemT *src, const IRExprT *left, const IRExprT *right, IRExprT **joinExpr)
{
    Status ret = IRCreateExprWithOp(opt->memCtx, IR_LOGOP_JOIN, joinExpr);
    if (ret != GMERR_OK) {
        return ret;
    }
    OpLogicalAAJoinT *op = (OpLogicalAAJoinT *)(void *)(*joinExpr)->op;
    op->joinType = SqlCvtJoinTypeToIR(src->joinType);
    AASchemaT *leftSchema = IRGetOpAASchema(left->op);
    AASchemaT *rightSchema = IRGetOpAASchema(right->op);
    ret = IRUnionAASchema(opt->memCtx, leftSchema, rightSchema, &(op->schema));
    if (ret != GMERR_OK) {
        return ret;
    }
    if (src->onExpr == NULL) {
        return GMERR_OK;
    }
    DmSchemaT dmSchema = {0};
    ret = SqlCvtAASchema2DmSchema(opt->memCtx, &op->schema, &dmSchema);
    if (ret != GMERR_OK) {
        return ret;
    }
    SqlExprCvtParaT cvtPara = {0};
    cvtPara.schema = &dmSchema;
    cvtPara.lvarNum = leftSchema->propAA.propNum;
    cvtPara.rightTableName = src->type == SRC_TABLE_REF ? src->label->metaCommon.metaName : NULL;
    cvtPara.rightTableAliasName = src->aliasName;
    cvtPara.labelNames = &opt->symbolTable->labelNames;
    ret = SqlCvtSqlExpr2Expr(src->onExpr, opt->memCtx, &cvtPara, (ExprT **)&op->onExpr);
    DbDynMemCtxFree(opt->memCtx, dmSchema.properties);
    return ret;
}

static Status SqlGetNaturalJoinCommonPropsCnt(AASchemaT *leftSchema, AASchemaT *rightSchema, uint32_t *commonPropsCnt)
{
    DB_POINTER2(leftSchema, rightSchema);

    uint32_t leftPropsNum = leftSchema->propAA.propNum;
    uint32_t rightPropsNum = rightSchema->propAA.propNum;
    DmPropertySchemaT **leftProps = leftSchema->propAA.properties;
    DmPropertySchemaT **rightProps = rightSchema->propAA.properties;

    *commonPropsCnt = 0;
    bool hasDiffType = false;
    for (uint32_t i = 0; i < leftPropsNum; i++) {
        for (uint32_t j = 0; j < rightPropsNum; j++) {
            const char *name = leftProps[i]->name;
            if (strcmp(name, rightProps[j]->name) == 0 && strcmp(name, FUNC_CONST_ALIAS) != 0) {
                hasDiffType = leftProps[i]->dataType != rightProps[j]->dataType;
                (*commonPropsCnt)++;
            }
            if (hasDiffType) {
                break;
            }
        }
    }
    if (hasDiffType) {
        SQL_ERROR_LOG(
            GMERR_FEATURE_NOT_SUPPORTED, "NATURAL JOIN not support columns of the same name of different types.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

/*
 * Build Join expr according to left and right Expr, and the srcItem corresponding to right Expr
 * Support natural join and cross join only
 */
Status SqlCreateJoinExpr(
    SqlBuildQueryPlanOptT *opt, SqlSrcItemT *src, const IRExprT *left, const IRExprT *right, IRExprT **retExpr)
{
    DB_POINTER2(retExpr, src);
    Status ret;
    IRExprT *joinExpr = NULL;
    AASchemaT *leftSchema = IRGetOpAASchema(left->op);
    AASchemaT *rightSchema = IRGetOpAASchema(right->op);
    uint32_t commonPropsCnt = 0;
    if (src->joinType & SQL_NATURAL_JOIN) {
        ret = SqlGetNaturalJoinCommonPropsCnt(leftSchema, rightSchema, &commonPropsCnt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (src->usingList != NULL || commonPropsCnt > 0) {
        if (src->onExpr != NULL) {
            SQL_ERROR_LOG(GMERR_SEMANTIC_ERROR, "NATURAL JOIN or USING operator cannot be used with ON expr.");
            return GMERR_SEMANTIC_ERROR;
        }
        ret = SqlCreateNaturalJoinExpr(opt->memCtx, src, left, right, &joinExpr);
        if (ret != GMERR_OK) {
            return ret;
        }
        src->joinType = SQL_NATURAL_JOIN;
    } else {
        ret = SqlCreateCartesianProductExpr(opt, src, left, right, &joinExpr);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    *retExpr = joinExpr;
    return GMERR_OK;
}

// 根据targetList中的列，和原有childSchema中的列，生成一个新的childSchema, 给后续的算子使用
static Status SqlAllocExtractIndex(DbMemCtxT *memCtx, const DbListT *targetList, SqlExtractInfoT *info)
{
    uint32_t indexSize = (uint32_t)(sizeof(uint32_t) * DbListGetItemCnt(targetList));
    uint8_t *indexArray = DbDynMemCtxAlloc(memCtx, indexSize);
    if (indexArray == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer unable to alloc memory for index array.");
        return GMERR_OUT_OF_MEMORY;
    }
    info->extractIndexNum = 0;
    info->extractIndexArray = (uint32_t *)(void *)indexArray;
    (void)memset_s(indexArray, indexSize, 0, indexSize);
    return GMERR_OK;
}

static inline bool SqlIsColumnSrcTableMatch(SqlExprColumnT *col, AliasOrgNamePairT *srcTableName)
{
    return  // 非 view 场景，比较 `col->originalTableName` 与 `srcTableName->orgName` 为表原名，区分大小写
        (col->originalTableName != NULL && DbStrCmp(col->originalTableName, srcTableName->orgName, true) == 0) ||
        // view 场景且 view 未设置别名，`col->tableName` 与 `srcTableName->orgName` 为 view 名，区分大小写
        (srcTableName->orgName != NULL && DbStrCmp(col->tableName, srcTableName->orgName, true) == 0) ||
        (srcTableName->alias != NULL && DbStrCmp(col->tableName, srcTableName->alias, true) == 0) ||
        // view 设置别名时，`col->tableName` 与 `srcTableName->alias` 为 view 别名，不区分大小写
        (srcTableName->alias != NULL && DbStrCmp(col->tableName, srcTableName->alias, true) == 0);
}

static bool SqlIsColumnMatch(
    DmPropertySchemaT *prop, SqlExprColumnT *col, char *name, SymbolTableT *symbolTable, uint32_t idx)
{
    DB_ASSERT((col != NULL) || (name != NULL));
    char *colName = col ? col->columnName : name;
    if (DbStrCmp(colName, prop->name, true) != 0) {
        // 非同名
        return false;
    }
    if (symbolTable == NULL || col == NULL || col->tableName == NULL) {
        // 未指定表名或符号表
        return true;
    }
    DbListT *srcTableNames = DbListItem(&symbolTable->labelNames, idx);
    uint32_t cnt = DbListGetItemCnt(srcTableNames);
    for (uint32_t i = 0; i < cnt; i++) {
        AliasOrgNamePairT *srcTableName = DbListItem(srcTableNames, i);
        if (srcTableName == NULL) {
            continue;
        }
        if (SqlIsColumnSrcTableMatch(col, srcTableName)) {
            return true;
        }
    }
    return false;
}

// 当表中所有的列都需要被提取，且不包含别名信息时，设置isAllExtract标记，后续查询出的数据可以直接反序列化到目标vertex
static bool SqlExtractIsAllProjection(SqlExtractParaT *para, SqlExtractInfoT *info)
{
    uint32_t targetCnt = DbListGetItemCnt(para->targetList);
    const AASchemaT *schema = para->schema;
    if (info->extractIndexNum != schema->propAA.propNum || info->extractIndexNum != targetCnt) {
        return false;
    }
    for (uint32_t i = 0; i < info->extractIndexNum; i++) {
        SqlTargetT *target = *(SqlTargetT **)DbListItem(para->targetList, i);
        DmPropertySchemaT *prop = schema->propAA.properties[i];
        if (info->extractIndexArray[i] != prop->propeId || target->aliasName != NULL) {
            info->isAllProjection = false;
            return false;
        }
    }
    return true;
}

static inline void SqlDestoryExtractArray(DbMemCtxT *memCtx, SqlExtractInfoT *info)
{
    info->extractIndexNum = 0;
    DbDynMemCtxFree(memCtx, info->extractIndexArray);
    info->extractIndexArray = NULL;
}

Status SqlGetExtractExprInfo(SqlExtractParaT *para, SqlExtractInfoT *info)
{
    const DbListT *targetList = para->targetList;
    Status ret = SqlAllocExtractIndex(para->memCtx, targetList, info);
    if (ret != GMERR_OK) {
        return ret;
    }
    const AASchemaT *schema = para->schema;
    uint32_t propNum = schema->propAA.propNum;
    uint32_t targetCnt = DbListGetItemCnt(para->targetList);
    for (uint32_t i = 0; i < targetCnt; i++) {
        SqlTargetT *target = *(SqlTargetT **)DbListItem(targetList, i);
        SqlExprOpTypeE type = (SqlExprOpTypeE)target->value->op->type;
        if (type != SQL_EXPR_ITEM_COLUMN) {
            SQL_ERROR_LOG(GMERR_SEMANTIC_ERROR, "Sql analyzer get an invalidate op type(%u).", target->value->op->type);
            goto RELEASE;
        }
        SqlExprColumnT *col = (SqlExprColumnT *)(void *)target->value->op;
        uint32_t foundIndex = DB_MAX_UINT32;
        for (uint32_t j = 0; j < propNum; j++) {
            DmPropertySchemaT *prop = schema->propAA.properties[j];
            if (SqlIsColumnMatch(prop, col, NULL, para->symbolTable, j)) {
                foundIndex = j;
                break;
            }
        }
        if (foundIndex == DB_MAX_UINT32) {
            SQL_ERROR_LOG(GMERR_UNDEFINE_COLUMN, "Sql analyzer unable to find column name (%s).", col->columnName);
            goto RELEASE;
        }
        // 对于无效的字段，不进行投影操作。(暂不支持搜索系统表)
        DmPropertySchemaT *foundProp = schema->propAA.properties[foundIndex];
        if (!foundProp->isValid || foundProp->isSysPrope) {
            continue;
        }
        info->extractIndexArray[info->extractIndexNum++] = foundIndex;
    }
    // 因为会过滤一些无效字段，当查询的都是无效字段时，进行报错
    if (info->extractIndexNum == 0) {
        SQL_ERROR_LOG(GMERR_SEMANTIC_ERROR, "Sql analyzer unable to find a valid column.");
        goto RELEASE;
    }
    info->isAllProjection = SqlExtractIsAllProjection(para, info);
    return GMERR_OK;

RELEASE:
    SqlDestoryExtractArray(para->memCtx, info);
    return GMERR_SEMANTIC_ERROR;
}
#else
Status SqlCreateJoinExpr(
    SqlBuildQueryPlanOptT *opt, SqlSrcItemT *src, const IRExprT *left, const IRExprT *right, IRExprT **retExpr)
{
    DB_POINTER5(retExpr, src, left, right, retExpr);
    // 走不到此处,SqlAnalyzeSelectStmtVerify已经拦截了
    return GMERR_FEATURE_NOT_SUPPORTED;
}
#endif

Status SqlCreateItemOpMinus(DbMemCtxT *memCtx, SqlExprT *expr, SqlBuildItemOptT *opt, IRExprT **pItemExpr)
{
    DB_POINTER3(memCtx, opt, pItemExpr);
    IRExprT *singleExpr = NULL;
    Status ret = IRCreateExprWithOp(memCtx, IR_ITEMOP_FUNC_EX, &singleExpr);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Sql analyzer unable to create itemOp funcEx.");
        return ret;
    }

    SqlExprCvtParaT para = {0};
    // 创建表的 default 表达式, aaSchema 为空, 且表达式内不允许有column.
    DmSchemaT schema = {0};
    if (opt->aaSchema != NULL) {
        ret = SqlCvtAASchema2DmSchema(memCtx, opt->aaSchema, &schema);
        if (ret != GMERR_OK) {
            IRDestroyExprWithOp(memCtx, &singleExpr);
            return ret;
        }
        para.schema = &schema;
        para.lvarNum = schema.propeNum;
    }

    OpItemFuncExT *funcOp = (OpItemFuncExT *)(void *)singleExpr->op;
    ExprT *funcExpr = NULL;
    ret = SqlCvtSqlExpr2Expr(expr, memCtx, &para, &funcExpr);
    DbDynMemCtxFree(memCtx, schema.properties);
    if (ret != GMERR_OK) {
        IRDestroyExprWithOp(memCtx, &singleExpr);
        return ret;
    }
    funcOp->func = funcExpr;
    *pItemExpr = singleExpr;
    return GMERR_OK;
}

Status SqlCreateItemOpBinary(DbMemCtxT *memCtx, SqlExprT *expr, SqlBuildItemOptT *opt, IRExprT **pItemExpr)
{
    DB_POINTER4(memCtx, expr, opt, pItemExpr);
    SqlExprOpTypeE type = (SqlExprOpTypeE)expr->op->type;
    if (!(type <= SQL_EXPR_OP_LIKE && type >= SQL_EXPR_OP_MULTI)) {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupported binary op type(%u).", expr->op->type);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    IRExprT *singleExpr = NULL;
    Status ret = IRCreateExprWithOp(memCtx, IR_ITEMOP_BINARY, &singleExpr);
    if (ret != GMERR_OK) {
        return ret;
    }
    const int operatorMap[] = {
        [SQL_EXPR_OP_EQ] = COMP_OP_EQ,
        [SQL_EXPR_OP_GT] = COMP_OP_GT,
        [SQL_EXPR_OP_LT] = COMP_OP_LT,
        [SQL_EXPR_OP_GE] = COMP_OP_GE,
        [SQL_EXPR_OP_LE] = COMP_OP_LE,
        [SQL_EXPR_OP_NE] = COMP_OP_NE,
        [SQL_EXPR_OP_IS] = LOGIC_OP_IS,
        [SQL_EXPR_OP_IS_NOT] = LOGIC_OP_ISNOT,
        [SQL_EXPR_OP_IN] = COMP_OP_IN,
        [SQL_EXPR_OP_NOT_IN] = COMP_OP_NOTIN,
        [SQL_EXPR_OP_AND] = LOGIC_OP_AND,
        [SQL_EXPR_OP_OR] = LOGIC_OP_OR,
        [SQL_EXPR_OP_MULTI] = ARITH_OP_MULTI,
        [SQL_EXPR_OP_DIV] = ARITH_OP_DIV,
        [SQL_EXPR_OP_MOD] = ARITH_OP_MOD,
        [SQL_EXPR_OP_ADD] = ARITH_OP_ADD,
        [SQL_EXPR_OP_SUB] = ARITH_OP_SUB,
        [SQL_EXPR_OP_BITOR] = BIT_OP_OR,
        [SQL_EXPR_OP_BITAND] = BIT_OP_AND,
        [SQL_EXPR_OP_LSHIFT] = BIT_OP_LSHIFT,
        [SQL_EXPR_OP_RSHIFT] = BIT_OP_RSHIFT,
        [SQL_EXPR_OP_CONCAT] = STR_OP_CONCAT,
        [SQL_EXPR_OP_VEC_L2_DISTANCE] = VECTOR_DIST_L2,
        [SQL_EXPR_OP_VEC_COSINE_DISTANCE] = VECTOR_DIST_COSINE,
    };

    OpItemBinaryT *binary = (OpItemBinaryT *)(void *)singleExpr->op;
    binary->code = operatorMap[expr->op->type];
    *pItemExpr = singleExpr;
    return GMERR_OK;
}

static Status SqlBuildMatchChildren(
    DbMemCtxT *memCtx, SqlExprT *expr, SqlBuildItemOptT *opt, IRExprT *irExpr, uint32_t childId)
{
    DB_POINTER4(memCtx, expr, opt, irExpr);
    SqlExprOpTypeE type = (SqlExprOpTypeE)expr->op->type;
    if (type != SQL_EXPR_OP_LIKE && type != SQL_EXPR_OP_GLOB) {
        SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE, "Sql analyzer: unsupport expr op|%u|.", (uint32_t)expr->op->type);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    IRExprT *tmpResultExpr = NULL;
    Status ret = GMERR_OK;
    SqlExprLikeT *exprLike = (SqlExprLikeT *)(void *)expr->op;
    SqlExprT *sqlExpr = (childId == 0) ? exprLike->src : exprLike->dst;
    SqlBuildItemOptT childOpt = *opt;
    switch (sqlExpr->op->type) {
        case SQL_EXPR_ITEM_CONST: {
            if (childId == 0) {
                childOpt.tableList = NULL;
                ret = SqlCreateItemOpConst(memCtx, sqlExpr, &childOpt, &tmpResultExpr);
            } else {
                ret = SqlCreateMatchOpConst(memCtx, sqlExpr, &tmpResultExpr, expr->op->type);
            }
            break;
        }
        case SQL_EXPR_ITEM_COLUMN: {
            ret = SqlCreateItemOpAttr(memCtx, sqlExpr, &childOpt, &tmpResultExpr);
            break;
        }
        case SQL_EXPR_ITEM_PARA: {
            ret = SqlCreateItemOpBindPara(memCtx, sqlExpr, &childOpt, &tmpResultExpr);
            break;
        }
        default:
            ret = GMERR_INVALID_PARAMETER_VALUE;
            SQL_ERROR_LOG(ret, "Sql analyzer: unable to handle expr op|%u|.", (uint32_t)sqlExpr->op->type);
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    irExpr->children[childId] = tmpResultExpr;
    tmpResultExpr->father = irExpr;
    return GMERR_OK;
}

Status SqlCreateItemOpMatch(DbMemCtxT *memCtx, SqlExprT *expr, SqlBuildItemOptT *opt, IRExprT **pItemExpr)
{
    DB_POINTER4(memCtx, expr, opt, pItemExpr);
    SqlExprOpTypeE type = (SqlExprOpTypeE)expr->op->type;
    if (type != SQL_EXPR_OP_LIKE && type != SQL_EXPR_OP_GLOB) {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupported type|%u|", (uint32_t)expr->op->type);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    SqlExprLikeT *exprLike = (SqlExprLikeT *)(void *)expr->op;
    if (exprLike->src == NULL || exprLike->dst == NULL) {
        SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE, "Sql analyzer: empty child node of exprLike.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    Status ret = SqlFillItemOpMatchForEscape(exprLike);
    if (ret != GMERR_OK) {
        return ret;
    }
    IRExprT *singleExpr = NULL;
    ret = IRCreateExprWithOp(memCtx, IR_ITEMOP_BINARY, &singleExpr);
    if (ret != GMERR_OK) {
        return ret;
    }
    OpItemBinaryT *binary = (OpItemBinaryT *)(void *)singleExpr->op;

    if (exprLike->isGlobMatch) {
        binary->code = (exprLike->isNot) ? COMP_OP_NOT_GLOB : COMP_OP_GLOB;
    } else {
        binary->code = (exprLike->isNot) ? COMP_OP_NOT_LIKE : COMP_OP_LIKE;
    }

    ret = SqlBuildMatchChildren(memCtx, expr, opt, singleExpr, 0);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SqlBuildMatchChildren(memCtx, expr, opt, singleExpr, 1);
    if (ret != GMERR_OK) {
        return ret;
    }

    *pItemExpr = singleExpr;
    return GMERR_OK;
}

Status SqlCreateItemOpListExpr(DbMemCtxT *memCtx, IRExprT **pItemExpr)
{
    DB_POINTER2(memCtx, pItemExpr);
    IRExprT *outExpr = NULL;
    Status ret = IRCreateExprWithOp(memCtx, IR_ITEMOP_LIST, &outExpr);
    if (ret != GMERR_OK) {
        return ret;
    }
    OpItemListT *exprListOp = (OpItemListT *)(void *)outExpr->op;
    DbCreateList(&exprListOp->itemList, sizeof(IRExprT *), memCtx);
    *pItemExpr = outExpr;
    return GMERR_OK;
}

#ifndef IDS_HAOTIAN
static bool SqlIsColumAlias(char *columnName, SqlBuildItemOptT *opt, uint32_t *colIdx)
{
    if (opt->symbolTable == NULL) {
        return false;
    }
    AAVertexDescT *propAA = &opt->symbolTable->schema->propAA;
    DbListT *alias = &opt->symbolTable->columnAlias;
    uint32_t aliasCount = DbListGetItemCnt(alias);
    for (uint32_t i = 0; i < aliasCount; ++i) {
        const AliasOrgNamePairT *curPair = (const AliasOrgNamePairT *)DbListItem(alias, i);
        if (curPair->alias == NULL || DbStrCmp(columnName, curPair->alias, true) != 0) {
            continue;
        }
        // 根据alias对应的orgName，去AASchema中找orgName对应的列propeId
        for (uint32_t j = 0; j < propAA->propNum; j++) {
            DmPropertySchemaT *property = propAA->properties[j];
            if (DbStrCmp(property->name, curPair->orgName, true) != 0) {
                continue;
            }
            *colIdx = property->propeId;
            return true;
        }
    }
    return false;
}
#else
static bool SqlIsColumAlias(char *columnName, SqlBuildItemOptT *opt, uint32_t *colIdx)
{
    return false;
}
#endif

static uint32_t SqlGetPropIdByAADesc(const AAVertexDescT *aaDesc, SqlBuildItemOptT *opt, SqlExprColumnT *col)
{
    uint32_t propNum = aaDesc->propNum;
    // 优先按照原名查找
    for (uint32_t i = 0; i < propNum; i++) {
        if (DbStrCmp(col->columnName, aaDesc->properties[i]->name, true) == 0) {
            // 如果是触发器的列，不涉及符号表
            if (opt->symbolTable == NULL) {
                return i;
            }
            DbListT *labelNames = &opt->symbolTable->labelNames;
            DbListT *list = (DbListT *)DbListItem(labelNames, i);
            if (SqlFindColumnWithSymbolTable(list, i, col, true, opt) != 0) {
                return i;
            }
        }
    }
    // 其次按照别名查找
    uint32_t colIdx;
    if (SqlIsColumAlias(col->columnName, opt, &colIdx)) {
        return colIdx;
    }

    return DB_INVALID_UINT32;
}

Status SqlCreateItemOpAttr(DbMemCtxT *memCtx, SqlExprT *expr, SqlBuildItemOptT *opt, IRExprT **pItemExpr)
{
    DB_POINTER4(memCtx, expr, opt, pItemExpr);
    SqlExprColumnT *col = (SqlExprColumnT *)(void *)expr->op;

    if (col->location.isCorrelateColumn) {  // 属性来自外查询的符号表
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupported correlated column.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    IRExprT *singleExpr = NULL;
    Status ret = IRCreateExprWithOp(memCtx, IR_ITEMOP_ATTR, &singleExpr);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 触发器场景: new old 优先按照表名解析, 若未匹配再按照关键字解析
    uint32_t propId = DB_INVALID_UINT32;
    if (opt->scene == SQL_EXPR_INDEX) {
#ifndef IDS_HAOTIAN
        propId = SqlCvtGetPropIdByDmSchema(col, opt->cvtPara);
#endif
    } else {
        if (opt->aaSchema == NULL) {
            SQL_ERROR_LOG(GMERR_UNEXPECTED_NULL_VALUE, "Sql analyzer: null aaSchema for column|%s|.", col->columnName);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        propId = SqlGetPropIdByAADesc(&opt->aaSchema->propAA, opt, col);
    }

#ifndef IDS_HAOTIAN
    bool isTrigKeyWord = false;
    if (propId == DB_INVALID_UINT32 && col->tableName != NULL && opt->trigSrcSchema != NULL) {
        isTrigKeyWord = true;
        propId = SqlGetPropIdByAADesc(&opt->trigSrcSchema->propAA, opt, col);
    }
#endif
    if (propId == DB_INVALID_UINT32) {
        SQL_ERROR_LOG(GMERR_UNDEFINE_COLUMN, "Sql analyzer: undefined column|%s|.", col->columnName);
        return GMERR_UNDEFINE_COLUMN;
    }

    OpItemAttrT *attrOp = (OpItemAttrT *)(void *)singleExpr->op;
    attrOp->propId = propId;
#ifndef IDS_HAOTIAN
    if (isTrigKeyWord) {
        bool isTableNameNew = DbStrCmp(col->tableName, "new", true) == 0;
        bool isTableNameOld = DbStrCmp(col->tableName, "old", true) == 0;
        if (isTableNameNew || isTableNameOld) {
            attrOp->isTrigRelated = true;
            attrOp->isNewSlot = isTableNameNew;
        }
    }
#endif

    *pItemExpr = singleExpr;
    return GMERR_OK;
}

Status SqlCreateItemOpConst(DbMemCtxT *memCtx, SqlExprT *expr, SqlBuildItemOptT *opt, IRExprT **pItemExpr)
{
    DB_POINTER4(memCtx, expr, opt, pItemExpr);
    IRExprT *singleExpr = NULL;
    Status ret = IRCreateExprWithOp(memCtx, IR_ITEMOP_CONSTVALUE, &singleExpr);
    if (ret != GMERR_OK) {
        return ret;
    }
    OpItemConstT *constOp = (OpItemConstT *)(void *)singleExpr->op;
    SqlExprConstT *exprConstT = (SqlExprConstT *)(void *)expr->op;
    constOp->value = exprConstT->arg;
    // 待适配 constOp->bindedPara = NULL;
    if (DM_TYPE_NEED_MALLOC(exprConstT->arg.type)) {
        const uint8_t *srcBuf = (const uint8_t *)exprConstT->arg.value.strAddr;
        uint32_t bufLen = exprConstT->arg.value.length;
        ret = DmCopyBufWithMemCtx(memCtx, srcBuf, bufLen, (uint8_t **)&(constOp->value.value.strAddr));
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    *pItemExpr = singleExpr;
    return GMERR_OK;
}

Status SqlCreateItemOpBindPara(DbMemCtxT *memCtx, SqlExprT *expr, SqlBuildItemOptT *opt, IRExprT **pItemExpr)
{
    DB_POINTER4(memCtx, expr, opt, pItemExpr);
    DB_UNUSED(opt);
    SqlBuildItemOptT childOpt = {0};
    Status ret = SqlCreateItemOpConst(memCtx, expr, &childOpt, pItemExpr);
    if (ret != GMERR_OK) {
        return ret;
    }
    OpItemConstT *op = (OpItemConstT *)(void *)(*pItemExpr)->op;
    ((SqlExprParaT *)(void *)expr->op)->planVal = NULL;
    op->bindedPara = expr->op;
    return GMERR_OK;
}

#ifndef IDS_HAOTIAN
uint32_t SqlGetItemOpOutputColumn(OpBaseT *op, IRPlanT *subIrPlan)
{
    DB_POINTER(op);
    switch (op->type) {
        case IR_ITEMOP_LIST: {  // 暂时用OpItemListT的itemList的长度表示，不考虑itemList中嵌套list的情况(1, (1, 2))
            OpItemListT *listOp = (OpItemListT *)(void *)op;
            return DbListGetItemCnt(&listOp->itemList);
        }
        case IR_ITEMOP_SUB_QUERY:  // todo in子查询需要构造输出列
        case IR_ITEMOP_ATTR:
        case IR_ITEMOP_CONSTVALUE:
        case IR_ITEMOP_UNARY:
        case IR_ITEMOP_BINARY:
        case IR_ITEMOP_NULL:
            return 1;
        default:
            return DB_INVALID_UINT32;
    }
}

static Status GetColumnOfSubQuery(IRExprT *root, uint32_t *columnNum)
{
    if (root->op->type == (uint8_t)IR_LOGOP_EXPR_PROJECT) {
        *columnNum = ((OpLogicalAAExprProjectT *)root->op)->schema.propAA.propNum;
        return GMERR_OK;
    } else if (root->op->type == (uint8_t)IR_LOGOP_LIMIT) {
        *columnNum = ((OpLogicalAALimitT *)root->op)->schema.propAA.propNum;
        return GMERR_OK;
    } else {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupported operator of subquery.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
}

static IRPlanT *GetLeftSubIrPlan(SqlBuildItemOptT *opt)
{
    if (opt->leftBrother == NULL || opt->leftBrother->op->type != (uint8_t)IR_ITEMOP_SUB_QUERY) {
        return NULL;
    }
    OpItemSubQueryT *leftSubQueryOp = (OpItemSubQueryT *)(void *)opt->leftBrother->op;
    uint32_t subQueryId = leftSubQueryOp->subQueryId;
    return *(IRPlanT **)DbListItem(opt->subQueries, subQueryId);
}

static Status SqlCreateSubParam4SubQuery(
    DbMemCtxT *memCtx, IRExprT *leftExpr, IRPlanT *leftIrPlan, ExprT **subParamExpr, SqlIrStmtT *newIrStmt)
{
    DB_POINTER2(memCtx, newIrStmt);
    uint32_t subQueryColCnt;
    Status ret = GetColumnOfSubQuery(newIrStmt->irPlan->root, &subQueryColCnt);  // 子查询输出列个数
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t leftChildColCnt = leftExpr != NULL ? SqlGetItemOpOutputColumn(leftExpr->op, leftIrPlan) : subQueryColCnt;
    if (leftChildColCnt == DB_INVALID_UINT32) {
        SQL_ERROR_LOG(
            GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupported operator of left children of the IN operator.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    if (subQueryColCnt != leftChildColCnt) {  // 检查子查询的输出列数量是否和inExpr的左孩子的输出列数量相同
        SQL_ERROR_LOG(GMERR_SEMANTIC_ERROR,
            "Sql analyzer: the number of left(%u) and right(%u) children of the IN operator must be the same.",
            leftChildColCnt, subQueryColCnt);
        return GMERR_SEMANTIC_ERROR;
    }

    return SqlMakeSetPara4SubQuery(memCtx, subQueryColCnt, subParamExpr, newIrStmt);
}

// 构造完子查询的ir plan后, 更新全局subQueries.
Status SqlUpdateIrPlan4SubQueries(IRPlanT *subIrPlan, DbListT *subQueries, OpItemSubQueryT *subQueryOp)
{
    DB_POINTER3(subIrPlan, subQueries, subQueryOp);
    subQueryOp->subQueryId = DbListGetItemCnt(subQueries);  // 重设子查询OpItemSubQueryT的sub-query id
    subIrPlan->subQueryId = subQueryOp->subQueryId;         // 重设子查询IRPlanT的sub-query id
    // 将子查询自己的ir plan也append给父查询
    Status ret = DbAppendListItem(subQueries, &subIrPlan);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Sql analyzer unable to append current sub-query when updating parent ir plan.");
        return ret;
    }
    DbClearList(&subIrPlan->subQueries);  // 将子查询的subQueries长度清空为0
    return GMERR_OK;
}

Status SqlCreateItemOpSubQuery(DbMemCtxT *memCtx, SqlExprT *expr, SqlBuildItemOptT *opt, IRExprT **pItemExpr)
{
    DB_POINTER4(memCtx, expr, opt, pItemExpr);

    IRExprT *singleExpr = NULL;
    Status ret = IRCreateExprWithOp(memCtx, IR_ITEMOP_SUB_QUERY, &singleExpr);
    if (ret != GMERR_OK) {
        return ret;
    }
    OpItemSubQueryT *subQuery = (OpItemSubQueryT *)(void *)singleExpr->op;

    // 为子查询构造一个新的SqlIrStmtT newIrStmt
    SqlIrStmtT newIrStmt = {0};
    ret = SqlCreateIrStmt4SubQuery(opt->irStmt, &newIrStmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 设置symbol table中的current sub query
    IRPlanT *newIrPlan = newIrStmt.irPlan;
    if (opt->symbolTable != NULL) {
        SymbolTableSetSubQuery(opt->symbolTable, subQuery);
    }

    // 递归调用SqlAnalyzeSelectWithSymbolTable
    SqlSelectStmtT *parsedSubQuery = (SqlSelectStmtT *)(void *)((SqlExprSubQueryT *)(void *)expr->op)->select;
    ret = opt->analyzeSelectWST(opt->session, parsedSubQuery, opt->symbolTable, opt->subQueries, &newIrStmt);
    if (ret != GMERR_OK) {
        goto ERROR;
    }

    // 暂时不支持相关列
    if (opt->symbolTable != NULL && opt->symbolTable->corColsPropIds.count != 0) {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer unable to handle correrative columns.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    // 检查子查询的输出列数并为其构造subParam
    IRPlanT *leftIrPlan = GetLeftSubIrPlan(opt);
    ret = SqlCreateSubParam4SubQuery(memCtx, opt->leftBrother, leftIrPlan, &subQuery->subParam, &newIrStmt);
    if (ret != GMERR_OK) {
        goto ERROR;
    }

    subQuery->testExpr = ExprMakePara(memCtx, 0);
    if (subQuery->testExpr == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        goto ERROR;
    }

    ret = SqlUpdateIrPlan4SubQueries(newIrPlan, opt->subQueries, subQuery);
    if (ret != GMERR_OK) {
        goto ERROR;
    }

    *pItemExpr = singleExpr;
    return GMERR_OK;
ERROR:
    IRDestroyPlan(newIrStmt.memCtx, &newIrPlan);
    return ret;
}
#endif

Status SqlCreateSelectExprWithLeftChild(DbMemCtxT *memCtx, IRExprT *left, IRExprT **retExpr)
{
    DB_POINTER3(memCtx, left, retExpr);
    IRExprT *selectExpr = NULL;
    Status ret = IRCreateExprWithOp(memCtx, IR_LOGOP_SELECT, &selectExpr);
    if (ret != GMERR_OK) {
        return ret;
    }
    const AASchemaT *childSchema = IRGetOpAASchemaConst(left->op);
    ret = IRGenAASchema4LogicalOp(memCtx, &childSchema, selectExpr->op);
    if (ret != GMERR_OK) {
        return ret;
    }
    selectExpr->children[0] = left;
    left->father = selectExpr;

    *retExpr = selectExpr;
    return GMERR_OK;
}

Status SqlCreateSelectExprWithChildren(DbMemCtxT *memCtx, IRExprT *left, IRExprT *right, IRExprT **retExpr)
{
    DB_POINTER4(memCtx, left, right, retExpr);
    IRExprT *selectExpr = NULL;
    Status ret = IRCreateExprWithOp(memCtx, IR_LOGOP_SELECT, &selectExpr);
    if (ret != GMERR_OK) {
        return ret;
    }
    const AASchemaT *childSchema = IRGetOpAASchemaConst(left->op);
    ret = IRGenAASchema4LogicalOp(memCtx, &childSchema, selectExpr->op);
    if (ret != GMERR_OK) {
        return ret;
    }
    selectExpr->children[0] = left;
    selectExpr->children[1] = right;
    left->father = selectExpr;
    right->father = selectExpr;

    *retExpr = selectExpr;
    return GMERR_OK;
}

static inline void SqlDestroyOrderByOp(DbMemCtxT *memCtx, IRExprT *irExpr)
{
    IRDestroyExprWithOp(memCtx, &irExpr);
}

static IROrderByNULLPosE SqlGetNullPositionForOrderByOp(SqlSortTypeE orderType, SqlNullsPosTypeE posType)
{
    if (posType == SQL_SORT_NULLS_FIRST) {
        return ORDERBY_NULL_FIRST;
    }
    if (posType == SQL_SORT_NULLS_LAST) {
        return ORDERBY_NULL_LAST;
    }
    // 未指定时 需根据升降序确认位置
    if (orderType == SQL_SORT_ASC) {
        return ORDERBY_NULL_FIRST;
    }
    return ORDERBY_NULL_LAST;
}

static Status SqlVerifyOneProp4OrderBy(
    SqlExprColumnT *sortColumn, DmPropertySchemaT **properties, uint32_t propNum, SqlOrderByT *curItem, bool isVecType)
{
    for (uint32_t j = 0; j < propNum; ++j) {
        if (DbStrCmp(sortColumn->columnName, properties[j]->name, true) == 0) {
            if (isVecType && !DmDataTypeIsVector(properties[j]->dataType)) {
                SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED,
                    "Sql analyzer: column %s  must be of vector type in the order by.", sortColumn->columnName);
                return GMERR_FEATURE_NOT_SUPPORTED;
            } else if (!isVecType && DmDataTypeIsVector(properties[j]->dataType)) {
                SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED,
                    "Sql analyzer: column %s  must be of scalar type in the order by.", sortColumn->columnName);
                return GMERR_FEATURE_NOT_SUPPORTED;
            }
            curItem->propId = j;
            sortColumn->location.property = properties[j];
            return GMERR_OK;
        }
    }
    // 属性列必须存在
    SQL_ERROR_LOG(
        GMERR_UNDEFINE_COLUMN, "Sql analyzer unable to find the column %s in the order by.", sortColumn->columnName);
    return GMERR_UNDEFINE_COLUMN;
}

static Status SqlVerifySortKey4Vector(DbMemCtxT *memCtx, SqlOrderByInfoT *orderByInfo)
{
    // 表达式一定是 向量属性列 <=> 向量字面量/绑定参数 或 向量字面量/绑定参数 <=> 向量属性列
    SqlOrderByT *curItem = orderByInfo->item;
    SqlExprT *leftExpr = curItem->sortExpr->children[0];
    SqlExprT *rightExpr = curItem->sortExpr->children[1];
    SqlExprT *constOrPara = SqlExprTypeIsConstOrPara(leftExpr->op->type) ? leftExpr : rightExpr;
    SqlExprT *sortColumn = SqlExprTypeIsConstOrPara(leftExpr->op->type) ? rightExpr : leftExpr;
    // 属性列必须在表中出现
    uint32_t propNum = orderByInfo->schema.propAA.propNum;
    DmPropertySchemaT **properties = orderByInfo->schema.propAA.properties;
    Status ret = SqlVerifyOneProp4OrderBy((SqlExprColumnT *)(void *)sortColumn->op, properties, propNum, curItem, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    curItem->constOrPara = constOrPara;
    if (leftExpr->op->type == SQL_EXPR_ITEM_CONST || rightExpr->op->type == SQL_EXPR_ITEM_CONST) {
        uint32_t byteLength = ((SqlExprColumnT *)(void *)sortColumn->op)->location.property->size;
        DbDataTypeE type = ((SqlExprColumnT *)(void *)sortColumn->op)->location.property->dataType;
        return SqlLiteralVector2Vector(memCtx, byteLength, type, &((SqlExprConstT *)(void *)constOrPara->op)->arg);
    }
    return ret;
}

static Status SqlAddSortKey2OrderBySchema(DbMemCtxT *memCtx, IRExprT *child, SqlOrderByInfoT *orderByInfo)
{
    // 如果第一个就是列名称，如order by a，那么a一定存在于OrderBy Schema中，因此no need添加了。
    SqlOrderByT *curItem = orderByInfo->item;
    if (curItem->sortExpr->op->type == SQL_EXPR_ITEM_COLUMN) {
        return GMERR_OK;
    }
    DmPropertySchemaT *newProp = DbDynMemCtxAlloc(memCtx, sizeof(DmPropertySchemaT));
    if (newProp == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql Analyzer: Unable to alloc property schema for order by.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(newProp, sizeof(DmPropertySchemaT), 0, sizeof(DmPropertySchemaT));
    // 因为目前暂时没法确定order by列的类型，需要在转换Expr后才能确定，因此先设置为UNDEFINED。
    uint32_t propId = orderByInfo->schema.propAA.propNum;
    SqlExprPropTypeT dataInfo = {.isFixed = false, .type = DB_DATATYPE_UNDEFINED, .size = 0};
    SqlSetNewPropInfo(EXPR_COLUMN_NAME, propId, &dataInfo, newProp);
    orderByInfo->schema.propAA.properties[propId] = newProp;
    orderByInfo->schema.propAA.propNum++;
    return GMERR_OK;
}

// OrderBy中出现的列信息，必须是存在于原Schema的列，比如order by a+1 中的a必须要存在
static Status SqlVerifySortKeyTraverse(SqlExprT *expr, void *procPara)
{
    SqlExprOpTypeE type = expr->op->type;
    // 如果order by的是聚合函数，如order by sum(a)类的操作，无法计算，因此需要报错处理
    if (type == SQL_EXPR_OP_FUN && SqlExprGetFuncType(expr) < FUNC_TYPE_AGG_END) {
        SQL_ERROR_LOG(GMERR_SYNTAX_ERROR, "Sql Analyzer: the aggregate function is misused in order by statement.");
        return GMERR_SYNTAX_ERROR;
    }
    if (type != SQL_EXPR_ITEM_COLUMN) {
        return GMERR_OK;
    }
    SqlOrderByInfoT *orderByInfo = (SqlOrderByInfoT *)procPara;
    DmPropertySchemaT **properties = orderByInfo->schema.propAA.properties;
    uint32_t propNum = orderByInfo->schema.propAA.propNum;
    SqlExprColumnT *sortColumn = (SqlExprColumnT *)(void *)expr->op;
    return SqlVerifyOneProp4OrderBy(sortColumn, properties, propNum, orderByInfo->item, false);
}

static Status SqlVerifySortKey4Scalar(DbMemCtxT *memCtx, SqlOrderByInfoT *orderByInfo)
{
    SqlExprT *sortExpr = orderByInfo->item->sortExpr;
    return SqlExprTraverse(sortExpr, SqlVerifySortKeyTraverse, orderByInfo);
}

static Status SqlCreateOrderByExpr4Vector(DbMemCtxT *memCtx, SqlOrderByT *curItem, ExprT **expr)
{
    Status ret = GMERR_OK;
    ExprT *left = ExprMakeLVar(memCtx, curItem->propId);
    if (left == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "|SQL Analyzer| Unable to alloc left var expr for order by.");
        return GMERR_OUT_OF_MEMORY;
    }

    ExprT *right = NULL;
    if (curItem->constOrPara->op->type == SQL_EXPR_ITEM_CONST) {
        right = ExprMakeConst(memCtx, ((SqlExprConstT *)(void *)curItem->constOrPara->op)->arg);
    } else {
        SqlExprCvtParaT cvtPara = {0};
        ret = SqlCvtSqlExpr2Expr(curItem->constOrPara, memCtx, &cvtPara, &right);
    }
    if (right == NULL || ret != GMERR_OK) {
        ret = (ret == GMERR_OK) ? GMERR_OUT_OF_MEMORY : ret;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|SQL Analyzer| Unable to cvt expr for order by.");
        goto RELEASE_LVAR;
    }
    const ExprOpTypeE g_vecDistOp[] = {
        [SQL_EXPR_OP_VEC_L2_DISTANCE] = EXPR_OP_VECTOR_DIST_L2,
        [SQL_EXPR_OP_VEC_COSINE_DISTANCE] = EXPR_OP_VECTOR_DIST_COSINE,
    };
    ExprT *binary = ExprMakeBinary(memCtx, left, right, g_vecDistOp[curItem->sortExpr->op->type]);
    if (binary == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "|SQL Analyzer| Unable to alloc binary expr for order by.");
        goto RELEASE_ALL;
    }
    *expr = binary;
    return GMERR_OK;
RELEASE_ALL:
    ExprDestroy(memCtx, right);
RELEASE_LVAR:
    ExprDestroy(memCtx, left);
    return ret;
}

static Status SqlCvtOrderByInt2TargetExpr(DbMemCtxT *memCtx, IRExprT *child, SqlOrderByInfoT *orderByInfo)
{
    DbListT *orderBys = orderByInfo->orderBy;
    DbListT *targets = orderByInfo->target;
    uint32_t orderByCnt = DbListGetItemCnt(orderBys);
    uint32_t targetCnt = DbListGetItemCnt(targets);
    for (uint32_t i = 0; i < orderByCnt; i++) {
        SqlOrderByT *curItem = *(SqlOrderByT **)DbListItem(orderBys, i);
        SqlOpBaseT *op = curItem->sortExpr->op;
        if (op->type != SQL_EXPR_ITEM_CONST) {
            continue;
        }
        DmValueT *constValue = &((SqlExprConstT *)(void *)(op))->arg;
        if (constValue->type != DB_DATATYPE_INT64) {
            continue;
        }
        if (constValue->value.longValue < 1 || constValue->value.longValue > targetCnt) {
            SQL_ERROR_LOG(GMERR_SYNTAX_ERROR, "|SQL Analyzer| Unable to convert order by item, const value: %u.",
                constValue->value.uintValue);
            return GMERR_SYNTAX_ERROR;
        }
        SqlTargetT *curTarget = *(SqlTargetT **)DbListItem(targets, (uint32_t)(constValue->value.longValue - 1));
        curItem->sortExpr = curTarget->value;
    }
    return GMERR_OK;
}

static Status SqlCreateOrderByExpr4Scalar(DbMemCtxT *memCtx, SqlOrderByInfoT *orderByInfo, ExprT **expr)
{
    SqlExprCvtParaT cvtPara = {0};
    SqlOrderByT *curItem = orderByInfo->item;
    cvtPara.needCfgPropDataType = true;
    cvtPara.schema = &orderByInfo->dmSchema;
    cvtPara.dataInfo.type = DB_DATATYPE_CHAR;
    cvtPara.lvarNum = orderByInfo->dmSchema.propeNum;
    Status ret = SqlCvtSqlExpr2Expr(curItem->sortExpr, memCtx, &cvtPara, expr);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Sql Analyzer: Unable to convert sql expr to expr for order by.");
        return ret;
    }
    // 说明在SqlAddSortKey2OrderBySchema没有添加到OrderBy Schema中，no need修改。
    if (curItem->sortExpr->op->type == SQL_EXPR_ITEM_COLUMN) {
        return GMERR_OK;
    }
    DmPropertySchemaT *prop = orderByInfo->schema.propAA.properties[orderByInfo->orderByPropId];
    prop->isFixed = cvtPara.dataInfo.isFixed;
    prop->size = cvtPara.dataInfo.size;
    prop->dataType = cvtPara.dataInfo.type;
    orderByInfo->orderByPropId++;
    return GMERR_OK;
}

static Status SqlVerifyAndGenLeftSchema4OrderBy(DbMemCtxT *memCtx, IRExprT *child, SqlOrderByInfoT *orderByInfo)
{
    Status ret;
    uint32_t orderByCnt = DbListGetItemCnt(orderByInfo->orderBy);
    for (uint32_t i = 0; i < orderByCnt; i++) {
        SqlOrderByT *curItem = *(SqlOrderByT **)DbListItem(orderByInfo->orderBy, i);
        orderByInfo->item = curItem;
        if (SqlIsVectorDistOperator(curItem->sortExpr->op->type)) {
            ret = SqlVerifySortKey4Vector(memCtx, orderByInfo);
            if (ret != GMERR_OK) {
                return ret;
            }
        } else {
            // 标量场景，需要将OrderBy Item中的列添加到OrderBy Schema中，同样的，如果已经存在了则no need添加了。
            ret = SqlVerifySortKey4Scalar(memCtx, orderByInfo);
            if (ret != GMERR_OK) {
                return ret;
            }
            ret = SqlAddSortKey2OrderBySchema(memCtx, child, orderByInfo);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static Status SqlCreateExpr4OrderBy(DbMemCtxT *memCtx, SqlOrderByInfoT *orderByInfo, IRExprT *orderByExpr)
{
    Status ret;
    OpLogicalAAExtendOrderByT *orderByOp = (OpLogicalAAExtendOrderByT *)(void *)orderByExpr->op;
    ExprArrayT *tuple = &CastToFunc(orderByOp->orderByList)->array;
    uint32_t orderByCnt = DbListGetItemCnt(orderByInfo->orderBy);
    ret = SqlCvtAASchema2DmSchema(memCtx, &orderByInfo->schema, &orderByInfo->dmSchema);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Sql analyzer unable to convert schema when create order by expr for scalar.");
        return ret;
    }
    orderByInfo->orderByPropId = orderByInfo->basePropNum;
    for (uint32_t i = 0; i < orderByCnt; ++i) {
        SqlOrderByT *curItem = *(SqlOrderByT **)DbListItem(orderByInfo->orderBy, i);
        orderByInfo->item = curItem;
        orderByOp->directions[i] = curItem->order == SQL_SORT_ASC ? ORDERBY_ASC : ORDERBY_DESC;
        orderByOp->nullPoses[i] = SqlGetNullPositionForOrderByOp(curItem->order, curItem->nullsPos);
        bool isVector = SqlIsVectorDistOperator(curItem->sortExpr->op->type);
        if (isVector) {
            ret = SqlCreateOrderByExpr4Vector(memCtx, curItem, &tuple->expr[i]);
        } else {
            ret = SqlCreateOrderByExpr4Scalar(memCtx, orderByInfo, &tuple->expr[i]);
        }
        if (ret != GMERR_OK) {
            SQL_ERROR_LOG(ret, "Sql analyzer unable to create order by expr, is vector(%u).", isVector);
            break;
        }
    }
    DbDynMemCtxFree(memCtx, orderByInfo->dmSchema.properties);
    return ret;
}

Status SqlCreateOrderByExpr(DbMemCtxT *memCtx, IRExprT *child, SqlOrderByInfoT *orderByInfo, IRExprT **retExpr)
{
    DB_POINTER3(child, orderByInfo, retExpr);
    Status ret;
    IRExprT *orderByExpr = NULL;
    ret = SqlCreateOrderByOp(memCtx, orderByInfo, &orderByExpr);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SqlGenBaseSchema4OrderBy(memCtx, child, orderByInfo);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }

    ret = SqlVerifyAndGenLeftSchema4OrderBy(memCtx, child, orderByInfo);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }

    ret = SqlCvtOrderByInt2TargetExpr(memCtx, child, orderByInfo);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }

    ret = SqlCreateExpr4OrderBy(memCtx, orderByInfo, orderByExpr);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }

    const AASchemaT *orderBySchema = &orderByInfo->schema;
    ret = IRGenAASchema4LogicalOp(memCtx, &orderBySchema, orderByExpr->op);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }

    SqlDestroySchema4OrderBy(memCtx, orderByInfo);
    // 先排序后投影，因此需要在expr project <-> scan 中间插入order by，变成expr project <-> order by <-> scan计划
    IRExprT *scanExpr = child->children[0];
    child->children[0] = orderByExpr;
    scanExpr->father = orderByExpr;
    orderByExpr->father = child;
    orderByExpr->children[0] = scanExpr;
    *retExpr = child;
    return GMERR_OK;

RELEASE:
    SqlDestroyOrderByOp(memCtx, orderByExpr);
    SqlDestroySchema4OrderBy(memCtx, orderByInfo);
    return ret;
}

static Status SqlGetLimitAndOffsetInteger(const SqlLimitT *limitClause, uint32_t *limitNum, uint32_t *offset)
{
    Status ret = GMERR_OK;
    DmValueT limitValue;
    if (SqlExprIsNegativeInteger(limitClause->num)) {
        ret = SqlExprGetPositiveInteger(limitClause->num->children[0], &limitValue);
        if (ret != GMERR_OK) {
            return ret;
        }
        *limitNum = (limitValue.value.uintValue == 0) ? 0 : DB_MAX_UINT32;  // limit 分页负值 相当于无分页
    } else if (SqlExprIsPositiveInteger(limitClause->num)) {
        ret = SqlExprGetPositiveInteger(limitClause->num, &limitValue);
        if (ret != GMERR_OK) {
            return ret;
        }
        *limitNum = limitValue.value.uintValue;
    }

    DmValueT offsetValue;
    if (limitClause->offset == NULL || SqlExprIsNegativeInteger(limitClause->offset)) {
        *offset = 0;  // offset 偏移负值 相当于无偏移
        return GMERR_OK;
    }
    if (SqlExprIsPositiveInteger(limitClause->offset)) {
        ret = SqlExprGetPositiveInteger(limitClause->offset, &offsetValue);
        if (ret != GMERR_OK) {
            return ret;
        }
        *offset = offsetValue.value.uintValue;
    }
    if (*limitNum == DB_MAX_UINT32) {
        *limitNum -= *offset;
    }

    return GMERR_OK;
}

Status SqlCreateLimitExpr(DbMemCtxT *memCtx, IRExprT *child, const SqlLimitT *limitClause, IRExprT **retExpr)
{
    DB_POINTER3(child, limitClause, retExpr);
    uint32_t limitNum = 0;
    uint32_t offset = 0;
    Status ret = SqlGetLimitAndOffsetInteger(limitClause, &limitNum, &offset);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Sql analyzer unable to get integer for limit clause.");
        return ret;
    }
    // 若无分页 偏移即可省略 limit算子
    if (limitNum == DB_MAX_UINT32 && offset == 0) {
        *retExpr = child;
        return GMERR_OK;
    }

    IRExprT *limitExpr = NULL;
    ret = IRCreateExprWithOp(memCtx, IR_LOGOP_LIMIT, &limitExpr);
    if (ret != GMERR_OK) {
        return ret;
    }

    OpLogicalAALimitT *limitOp = (OpLogicalAALimitT *)(void *)limitExpr->op;
    limitOp->limitNum = limitNum;
    limitOp->offset = offset;

    const AASchemaT *childSchema = IRGetOpAASchemaConst(child->op);
    ret = IRGenAASchema4LogicalOp(memCtx, &childSchema, limitExpr->op);
    if (ret != GMERR_OK) {
        return ret;
    }

    limitExpr->children[0] = child;
    child->father = limitExpr;
    *retExpr = limitExpr;

    return GMERR_OK;
}

Status SqlCreateDistinctExpr(DbMemCtxT *memCtx, IRExprT *child, IRExprT **retExpr)
{
    DB_POINTER3(memCtx, child, retExpr);
    IRExprT *distinctExpr = NULL;
    Status ret = IRCreateExprWithOp(memCtx, IR_LOGOP_DISTINCT, &distinctExpr);
    if (ret != GMERR_OK) {
        return ret;
    }

    const AASchemaT *childSchema = IRGetOpAASchemaConst(child->op);
    ret = IRGenAASchema4LogicalOp(memCtx, &childSchema, distinctExpr->op);
    if (ret != GMERR_OK) {
        IRDestroyExprWithOp(memCtx, &distinctExpr);
        return ret;
    }

    distinctExpr->children[0] = child;
    child->father = distinctExpr;
    *retExpr = distinctExpr;

    return GMERR_OK;
}

Status SqlCreateDmlExprWithOneArity(
    const IROpTypeE type, DbMemCtxT *memCtx, DmVertexLabelT *label, IRExprT *child, IRExprT **expr)
{
    DB_POINTER4(memCtx, label, child, expr);
    if (type != IR_LOGOP_INSERT && type != IR_LOGOP_DELETE) {
        SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE, "Sql analyzer: unexpected operator type: %u.", type);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    IRExprT *dmlExpr = NULL;
    Status ret = IRCreateExprWithOp(memCtx, type, &dmlExpr);
    if (ret != GMERR_OK) {
        return ret;
    }
    OpLogicalAACudT *logicalAACud = (OpLogicalAACudT *)(void *)dmlExpr->op;
    logicalAACud->label = label;
    AASchemaT *childSchema = IRGetOpAASchema(child->op);
    ret = IRCopyAASchema(memCtx, childSchema, &logicalAACud->schema);
    if (ret != GMERR_OK) {
        return ret;
    }

    child->father = dmlExpr;
    dmlExpr->children[0] = child;
    *expr = dmlExpr;
    return GMERR_OK;
}

Status SqlCreateDmlExprWithTwoArity(
    DbMemCtxT *memCtx, DmVertexLabelT *label, IRExprT *left, IRExprT *right, IRExprT **retExpr)
{
    DB_POINTER4(memCtx, label, left, right);
    IRExprT *updateExpr = NULL;
    Status ret = IRCreateExprWithOp(memCtx, IR_LOGOP_UPDATE, &updateExpr);
    if (ret != GMERR_OK) {
        return ret;
    }

    OpLogicalAACudT *logicalAA = (OpLogicalAACudT *)(void *)updateExpr->op;
    logicalAA->label = label;
    AASchemaT *childSchema = IRGetOpAASchema(left->op);
    ret = IRCopyAASchema(memCtx, childSchema, &logicalAA->schema);
    if (ret != GMERR_OK) {
        return ret;
    }

    updateExpr->children[0] = left;
    updateExpr->children[1] = right;
    left->father = updateExpr;
    right->father = updateExpr;

    *retExpr = updateExpr;
    return GMERR_OK;
}

static Status SqlGetIndexForProperty(DmPropertySchemaT *property, DbListT *columnList, uint32_t *idx)
{
    uint32_t columnCnt = DbListGetItemCnt(columnList);
    for (uint32_t i = 0; i < columnCnt; ++i) {
        char *colName = *(char **)DbListItem(columnList, i);
        if (DbStrCmp(property->name, colName, true) == 0) {
            *idx = i;
            return GMERR_OK;
        }
    }
    return GMERR_NO_DATA;
}

// DEFAULT VALUES 场景 获取默认值
static Status SqlGetDefaultValue4Expr(const DmValueT *defaultValue, DmValueT *retValue)
{
    if (defaultValue != NULL) {
        *retValue = *defaultValue;
        return GMERR_OK;
    }
    *retValue = (DmValueT){0};
    retValue->type = DB_DATATYPE_NULL;
    return GMERR_OK;
}

// DEFAULT VALUES 场景 自增列无视默认值 标记为 UNDEFINED即可，exec层处理
static inline Status SqlGetDefaultValueForAutoInc(DmValueT *retValue)
{
    *retValue = (DmValueT){0};
    retValue->type = DB_DATATYPE_UNDEFINED;
    return GMERR_OK;
}

// 用于 INSERT ... (COLUMN LIST) VALUES (VALUE LIST) 场景
static inline Status SqlMakeConstExprForAutoInc(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, ExprT **valExpr)
{
    DmValueT propValue;
    propValue.type = DB_DATATYPE_UNDEFINED;  // INSERT时没有给主键赋值，analyzer层做此标记，exec层处理
    *valExpr = ExprMakeConst(memCtx, propValue);
    if (*valExpr == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to make autoincrement value for insert tuple.");
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

static Status SqlGetDefault(DmPropertySchemaT *property, DmValueT *defaultValue)
{
    if (property->isAutoIncProp) {
        return SqlGetDefaultValueForAutoInc(defaultValue);  // 自增列无视默认值
    }

    if (property->defaultType == DM_DEFAULT_TYPE_VALUE || property->defaultType == DM_DEFAULT_TYPE_EXPR) {
        // 包括常量值和表达式计算值.
        return SqlGetDefaultValue4Expr(property->defaultValue, defaultValue);
    }

#ifndef IDS_HAOTIAN
    if (SqlDefaultValueIsTimeType(property->defaultType)) {
        return SqlGetDefaultValue4Time(property->defaultType, defaultValue);
    }
#endif
    return GMERR_OK;
}

static Status SqlFillValueForUnsetProp(
    DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, DmPropertySchemaT *property, ExprT **valExpr)
{
    if (property->isAutoIncProp) {
        return SqlMakeConstExprForAutoInc(memCtx, vertexLabel, valExpr);
    }
    DmValueT defaultValue = {0};
    Status ret = SqlGetDefault(property, &defaultValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (DmDataTypeIsVector(property->dataType) && defaultValue.type == DB_DATATYPE_NULL) {
        SQL_ERROR_LOG(GMERR_NULL_VALUE_NOT_ALLOWED, "|SQL Analyzer| Insert null is not allowed for vector property %s.",
            property->name);
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    *valExpr = ExprMakeConst(memCtx, defaultValue);
    if (*valExpr == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to make ExprConstT for default value.");
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

static Status SqlConstExprConvertVector(DbMemCtxT *memCtx, DmPropertySchemaT *prop, SqlExprT *expr)
{
    if (expr->op->type == SQL_EXPR_ITEM_PARA) {
        return GMERR_OK;
    }
    SqlExprConstT *constExpr = (SqlExprConstT *)(void *)expr->op;
    // 向量字面量在parser之后数据类型仍是字符串，需要转换为vector
    if (DmDataTypeIsVector(prop->dataType)) {
        if (constExpr->arg.type == DB_DATATYPE_NULL) {
            SQL_ERROR_LOG(GMERR_NULL_VALUE_NOT_ALLOWED,
                "Sql Analyzer: inserting null is not allowed for vector property %s", prop->name);
            return GMERR_NULL_VALUE_NOT_ALLOWED;
        }
        return SqlLiteralVector2Vector(memCtx, prop->size, prop->dataType, &constExpr->arg);
    }
    return GMERR_OK;
}

// Note1: 简化逻辑: 暂时交由执行层处理默认值转换
static Status SqlFillOneColValueWithoutColumnList(
    DbMemCtxT *memCtx, DmVertexLabelT *label, SqlInsertStmtT *insertStmt, DbListT *valExprList, ExprArrayT *exprArray)
{
    SqlExprCvtParaT cvtPara = {0};
    DmSchemaT trigSrcSchema = {0};
    cvtPara.trigSrcSchema = &trigSrcSchema;
    Status ret = SqlInitExprCvtPara(memCtx, label, insertStmt->trigSrcSchema, &cvtPara);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Unable to init expr convert para for insert.");
        return ret;
    }

    uint32_t propeNum = cvtPara.schema->propeNum;
    SqlExprT *sqlValExpr;
    SqlExprT valExprEmpty = {0};
    SqlExprParaT valExprParEmpty = {0};
    SqlExprConstT sqlExprConst = {0};
    uint32_t vari = 0;
    for (uint32_t i = 0; i < propeNum; ++i) {
        if (IsSqlHiddenColumn(cvtPara.schema->properties[i].name)) {
            if (IsSqlHiddenAutoIdColumn(cvtPara.schema->properties[i].name)) {
                sqlExprConst.expr.type = SQL_EXPR_ITEM_CONST;
                sqlExprConst.arg.type = DB_DATATYPE_UNDEFINED;
                valExprEmpty.op = &sqlExprConst.expr;
                sqlValExpr = &valExprEmpty;
            } else {
                // 若为隐藏字段，创建空的valExpr用于插入占位
                valExprParEmpty.expr.type = SQL_EXPR_ITEM_PARA;
                valExprEmpty.op = &valExprParEmpty.expr;
                sqlValExpr = &valExprEmpty;
            }
        } else {
            sqlValExpr = *(SqlExprT **)DbListItem(valExprList, vari);
            vari++;
        }
        ret = SqlConstExprConvertVector(memCtx, &label->metaVertexLabel->schema->properties[i], sqlValExpr);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(memCtx, trigSrcSchema.properties);
            return ret;
        }

        // sqlExpr convert to expr
        ExprT **valExpr = &exprArray->expr[i];
        ret = SqlCvtSqlExpr2Expr(sqlValExpr, memCtx, &cvtPara, valExpr);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(memCtx, trigSrcSchema.properties);
            SQL_ERROR_LOG(ret, "Sql analyzer: unable to convert value|%u| for insert tuple.", i);
            return ret;
        }
    }
    DbDynMemCtxFree(memCtx, trigSrcSchema.properties);
    return GMERR_OK;
}

// Note1: 简化逻辑: 暂时交由执行层处理默认值转换
static Status SqlFillOneColValue(
    DbMemCtxT *memCtx, DmVertexLabelT *label, SqlInsertStmtT *insertStmt, DbListT *valExprList, ExprArrayT *exprArray)
{
    SqlExprCvtParaT cvtPara = {0};
    DmSchemaT trigSrcSchema = {0};
    cvtPara.trigSrcSchema = &trigSrcSchema;
    Status ret = SqlInitExprCvtPara(memCtx, label, insertStmt->trigSrcSchema, &cvtPara);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Unable to init expr convert para for insert.");
        return ret;
    }

    DmPropertySchemaT *properties = cvtPara.schema->properties;
    uint32_t propeNum = cvtPara.schema->propeNum;
    for (uint32_t i = 0; i < propeNum; ++i) {
        uint32_t idx = 0;
        ExprT **valExpr = &exprArray->expr[i];
        ret = SqlGetIndexForProperty(&properties[i], insertStmt->columnList, &idx);
        if (ret != GMERR_OK) {
            ret = SqlFillValueForUnsetProp(memCtx, label, &properties[i], valExpr);
            if (ret != GMERR_OK) {
                DbDynMemCtxFree(memCtx, trigSrcSchema.properties);
                return ret;
            }
        } else {
            // sqlExpr convert to expr
            SqlExprT *sqlValExpr = *(SqlExprT **)DbListItem(valExprList, idx);
            ret = SqlConstExprConvertVector(memCtx, &label->metaVertexLabel->schema->properties[i], sqlValExpr);
            if (ret != GMERR_OK) {
                DbDynMemCtxFree(memCtx, trigSrcSchema.properties);
                return ret;
            }
            ret = SqlCvtSqlExpr2Expr(sqlValExpr, memCtx, &cvtPara, valExpr);
            if (ret != GMERR_OK) {
                DbDynMemCtxFree(memCtx, trigSrcSchema.properties);
                SQL_ERROR_LOG(ret, "Sql analyzer: unable to convert value|%u| for insert tuple.", idx);
                return ret;
            }
        }
    }
    DbDynMemCtxFree(memCtx, trigSrcSchema.properties);
    return GMERR_OK;
}

static Status SqlFillValuesList(
    DbMemCtxT *memCtx, DmVertexLabelT *label, SqlInsertStmtT *insertStmt, DbListT **exprList)
{
    DbListT *valuesList = insertStmt->value->valuesList;
    uint32_t valueCnt = DbListGetItemCnt(valuesList);
    DbListT *listValues = (DbListT *)DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
    if (listValues == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to malloc tuple list for insert.");
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateListWithExtendSize(listValues, sizeof(ExprT *), valueCnt, memCtx);

    Status ret = GMERR_OK;
    uint32_t propeNum = label->metaVertexLabel->schema->propeNum;
    for (uint32_t i = 0; i < valueCnt; ++i) {
        ExprT *exprTuple = ExprMakeTuple(memCtx, propeNum);
        if (exprTuple == NULL) {
            ret = GMERR_OUT_OF_MEMORY;
            SQL_ERROR_LOG(ret, "Sql analyzer: unable to make tuple for insert.");
            goto EXIT;
        }

        ExprArrayT *exprArray = &(CastToFunc(exprTuple)->array);
        DbListT *valExprList = *(DbListT **)DbListItem(valuesList, i);
        if (insertStmt->columnList == NULL) {
            ret = SqlFillOneColValueWithoutColumnList(memCtx, label, insertStmt, valExprList, exprArray);
        } else {
            ret = SqlFillOneColValue(memCtx, label, insertStmt, valExprList, exprArray);
        }
        if (ret != GMERR_OK) {
            ExprDestroy(memCtx, exprTuple);
            goto EXIT;
        }

        ret = DbAppendListItem(listValues, &exprTuple);
        if (ret != GMERR_OK) {
            ExprDestroy(memCtx, exprTuple);
            goto EXIT;
        }
    }
    *exprList = listValues;
    return GMERR_OK;

EXIT:
    DbDestroyList(listValues);
    DbDynMemCtxFree(memCtx, listValues);
    return ret;
}

static Status SqlFillDefaultValues(DbMemCtxT *memCtx, DmVertexLabelT *label, DbListT **exprList)
{
    Status ret = GMERR_OK;
    DmPropertySchemaT *properties = label->metaVertexLabel->schema->properties;
    uint32_t propeNum = label->metaVertexLabel->schema->propeNum;
    ExprT *exprTuple = ExprMakeTuple(memCtx, propeNum);
    if (exprTuple == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to make tuple with default values for insert.");
        return GMERR_OUT_OF_MEMORY;
    }

    ExprArrayT *exprArray = &(CastToFunc(exprTuple)->array);
    for (uint32_t i = 0; i < propeNum; ++i) {
        DmValueT defaultValue = {0};
        ret = SqlGetDefault(&properties[i], &defaultValue);
        if (ret != GMERR_OK) {
            ExprDestroy(memCtx, exprTuple);
            SQL_ERROR_LOG(ret, "Sql analyzer: unable to get property|%u|'s default value in tuple for insert.", i);
            return ret;
        }
        exprArray->expr[i] = ExprMakeConst(memCtx, defaultValue);
        if (exprArray->expr[i] == NULL) {
            ExprDestroy(memCtx, exprTuple);
            SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to make default ExprConstT in tuple for insert.");
            return GMERR_OUT_OF_MEMORY;
        }
    }

    // create list for 1 new tuple with default values intended to insert
    DbListT *listValues = (DbListT *)DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
    if (listValues == NULL) {
        ExprDestroy(memCtx, exprTuple);
        SQL_ERROR_LOG(ret, "Sql analyzer: unable to malloc tuple list for insert default values.");
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateListWithExtendSize(listValues, sizeof(ExprT *), 1, memCtx);
    ret = DbAppendListItem(listValues, &exprTuple);
    if (ret != GMERR_OK) {
        ExprDestroy(memCtx, exprTuple);
        DbDestroyList(listValues);
        DbDynMemCtxFree(memCtx, listValues);
        return ret;
    }
    *exprList = listValues;
    return GMERR_OK;
}

Status SqlFillInsertExprListForBuildOp(
    DbMemCtxT *memCtx, SqlInsertStmtT *insertStmt, DmVertexLabelT *label, DbListT **exprList)
{
    DB_POINTER4(memCtx, insertStmt, label, exprList);
    if (insertStmt->value->type == VALUES_LIST) {
        return SqlFillValuesList(memCtx, label, insertStmt, exprList);
    }
    return SqlFillDefaultValues(memCtx, label, exprList);
}

Status SqlGetPropIdByColumnName(DmPropertySchemaT *properties, uint32_t propeNum, char *colName, uint32_t *idx)
{
    DB_POINTER2(properties, colName);
    for (uint32_t i = 0; i < propeNum; i++) {
        if (DbStrCmp(properties[i].name, colName, true) == 0) {
            *idx = i;
            return GMERR_OK;
        }
    }
    return GMERR_UNDEFINE_COLUMN;
}

// Note1: 简化逻辑: 暂时交由执行层处理默认值转换
static Status SqlBuildUpdateTupleItemsForSetList(DbMemCtxT *memCtx, DmVertexLabelT *label, SqlSetExprRefT *setClause,
    SqlUpdateExprParamT *exprParam, ExprArrayT *exprArray)
{
    SqlExprCvtParaT cvtPara = {0};
    DmSchemaT trigSrcSchema = {0};
    cvtPara.trigSrcSchema = &trigSrcSchema;
    Status ret = SqlInitExprCvtPara(memCtx, label, exprParam->trigSrcSchema, &cvtPara);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Unable to init expr convert para for update.");
        return ret;
    }
    DmPropertySchemaT *properties = cvtPara.schema->properties;
    uint32_t propeNum = cvtPara.schema->propeNum;

    uint32_t propId = 0;
    uint32_t colCnt = DbListGetItemCnt(setClause->columnList);
    for (uint32_t i = 0; i < colCnt; ++i) {
        char *colName = *((char **)DbListItem(setClause->columnList, i));
        ret = SqlGetPropIdByColumnName(properties, propeNum, colName, &propId);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(memCtx, trigSrcSchema.properties);
            SQL_ERROR_LOG(ret, "Sql analyzer: undefined column|%s| for update.", colName);
            return ret;
        }
        SqlExprT *val = *((SqlExprT **)DbListItem(((SqlExprListT *)(void *)setClause->expr->op)->exprList, i));
        ret = SqlCheckColumn4SetClause(memCtx, properties, propId, val);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(memCtx, trigSrcSchema.properties);
            return ret;
        }
        // sqlExpr convert to expr
        ExprT **valExpr = &exprArray->expr[propId];
        ret = SqlCvtSqlExpr2Expr(val, memCtx, &cvtPara, valExpr);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(memCtx, trigSrcSchema.properties);
            SQL_ERROR_LOG(ret, "Sql analyzer: unable to convert setValue for column|%s| when update.", colName);
            return ret;
        }
    }
    DbDynMemCtxFree(memCtx, trigSrcSchema.properties);
    return GMERR_OK;
}

static Status SqlCheckColumn4SetClause(
    DbMemCtxT *memCtx, DmPropertySchemaT *properties, uint32_t propId, SqlExprT *sqlExpr)
{
    // Case 1: 非向量列直接返回
    // Case 2: set子句中，对于SQL_EXPR_ITEM_PARA类型直接返回不转换
    if (!DmDataTypeIsVector(properties[propId].dataType) || sqlExpr->op->type == SQL_EXPR_ITEM_PARA) {
        return GMERR_OK;
    }
    // Case3: set子句中，对于其他非SQL_EXPR_ITEM_CONST类型均不支持
    if (sqlExpr->op->type != SQL_EXPR_ITEM_CONST) {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED,
            "Sql Analyzer: only const vector literals are supported in the set clause, %" PRIu32 ".",
            (uint32_t)sqlExpr->op->type);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    // Case 4: set子句中，对于SQL_EXPR_ITEM_CONST类型不允许其type为DB_DATATYPE_NULL
    SqlExprConstT *constExpr = (SqlExprConstT *)(void *)sqlExpr->op;
    if (constExpr->arg.type == DB_DATATYPE_NULL) {
        SQL_ERROR_LOG(GMERR_NULL_VALUE_NOT_ALLOWED,
            "Sql Analyzer: null is not supported for vector column %s in the set clause", properties[propId].name);
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    // Case 5: set子句中，对于SQL_EXPR_ITEM_CONST类型需要将向量字符串转换为float类型的数组
    return SqlLiteralVector2Vector(memCtx, properties[propId].size, properties[propId].dataType, &constExpr->arg);
}

// Note1: 简化逻辑: 暂时交由执行层处理默认值转换
static Status SqlBuildTupleItemsBySetClause(DbMemCtxT *memCtx, DbListT *setClauseList, DmVertexLabelT *label,
    SqlUpdateExprParamT *exprParam, ExprArrayT *exprArray)
{
    SqlExprCvtParaT cvtPara = {0};
    DmSchemaT trigSrcSchema = {0};
    cvtPara.trigSrcSchema = &trigSrcSchema;
    Status ret = SqlInitExprCvtPara(memCtx, label, exprParam->trigSrcSchema, &cvtPara);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Unable to init expr convert para for update.");
        return ret;
    }

    uint32_t propId = 0;
    uint32_t exprCnt = DbListGetItemCnt(setClauseList);
    uint32_t propeNum = cvtPara.schema->propeNum;
    DmPropertySchemaT *properties = cvtPara.schema->properties;
    for (uint32_t i = 0; i < exprCnt; ++i) {
        SqlSetExprRefT *setClause = *((SqlSetExprRefT **)DbListItem(setClauseList, i));
        if (setClause->isSetList) {
            ret = SqlBuildUpdateTupleItemsForSetList(memCtx, label, setClause, exprParam, exprArray);
            if (ret != GMERR_OK) {
                DbDynMemCtxFree(memCtx, trigSrcSchema.properties);
                return ret;
            }
        } else {
            char *colName = setClause->columnName;
            ret = SqlGetPropIdByColumnName(properties, propeNum, colName, &propId);
            if (ret != GMERR_OK) {
                DbDynMemCtxFree(memCtx, trigSrcSchema.properties);
                SQL_ERROR_LOG(ret, "Sql analyzer: undefined column|%s| for update.", colName);
                return ret;
            }
            // sqlExpr convert to expr
            ret = SqlCheckColumn4SetClause(memCtx, properties, propId, setClause->expr);
            if (ret != GMERR_OK) {
                DbDynMemCtxFree(memCtx, trigSrcSchema.properties);
                return ret;
            }
            ExprT **valExpr = &exprArray->expr[propId];
            ret = SqlCvtSqlExpr2Expr(setClause->expr, memCtx, &cvtPara, valExpr);
            if (ret != GMERR_OK) {
                DbDynMemCtxFree(memCtx, trigSrcSchema.properties);
                SQL_ERROR_LOG(ret, "Sql analyzer: unable to convert setValue for update tuple column|%s|.", colName);
                return ret;
            }
        }
    }
    DbDynMemCtxFree(memCtx, trigSrcSchema.properties);
    return GMERR_OK;
}

Status SqlFillUpdateExprListForBuildOp(DbMemCtxT *memCtx, DbListT *setClauseList, DmVertexLabelT *label,
    SqlUpdateExprParamT *exprParam, DbListT **exprList)
{
    DB_POINTER5(memCtx, setClauseList, label, exprParam, exprList);
    Status ret = GMERR_OK;
    uint32_t propeNum = label->metaVertexLabel->schema->propeNum;
    ExprT *exprTuple = ExprMakeTuple(memCtx, propeNum);
    if (exprTuple == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to make tuple for update.");
        return GMERR_OUT_OF_MEMORY;
    }

    DmValueT undefValue = {0};
    undefValue.type = DB_DATATYPE_UNDEFINED;
    ExprArrayT *exprArray = &(CastToFunc(exprTuple)->array);
    for (uint32_t i = 0; i < propeNum; ++i) {
        exprArray->expr[i] = ExprMakeConst(memCtx, undefValue);
        if (exprArray->expr[i] == NULL) {
            ExprDestroy(memCtx, exprTuple);
            ret = GMERR_OUT_OF_MEMORY;
            SQL_ERROR_LOG(ret, "Sql analyzer: unable to make ExprConstT in tuple for update.");
            return ret;
        }
    }

    ret = SqlBuildTupleItemsBySetClause(memCtx, setClauseList, label, exprParam, exprArray);
    if (ret != GMERR_OK) {
        ExprDestroy(memCtx, exprTuple);
        return ret;
    }

    // create list for 1 new tuple intended to update
    DbListT *listValues = (DbListT *)DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
    if (listValues == NULL) {
        ExprDestroy(memCtx, exprTuple);
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to malloc value list for update tuple.");
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateListWithExtendSize(listValues, sizeof(ExprT *), 1, memCtx);

    ret = DbAppendListItem(listValues, &exprTuple);
    if (ret != GMERR_OK) {
        ExprDestroy(memCtx, exprTuple);
        DbDestroyList(listValues);
        DbDynMemCtxFree(memCtx, listValues);
        return ret;
    }
    *exprList = listValues;
    return GMERR_OK;
}

Status SqlCreateItemOpFuncEx(DbMemCtxT *memCtx, SqlExprT *expr, SqlBuildItemOptT *opt, IRExprT **pItemExpr)
{
    DB_POINTER4(memCtx, expr, opt, pItemExpr);
    IRExprT *singleExpr = NULL;
    Status ret = IRCreateExprWithOp(memCtx, IR_ITEMOP_FUNC_EX, &singleExpr);
    if (ret != GMERR_OK) {
        return ret;
    }

    OpItemFuncExT *funcOp = (OpItemFuncExT *)(void *)singleExpr->op;
    SqlExprFuncT *func = (SqlExprFuncT *)(void *)expr->op;
    // where中不支持聚合函数
    if (opt->scene == SQL_WHERE && func->funcType < FUNC_TYPE_AGG_END) {
        IRDestroyExprWithOp(memCtx, &singleExpr);
        SQL_ERROR_LOG(GMERR_SEMANTIC_ERROR, "Aggregate functions are not allowed in where clause.");
        return GMERR_SEMANTIC_ERROR;
    }

    // 校验函数类型
    ExprOpTypeE type = SqlCvtFuncType2ExprType(func->funcType);
    if (type == EXPR_OP_FUNC_END) {
        IRDestroyExprWithOp(memCtx, &singleExpr);
        SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE, "Function type is not valid for itemOpFunc.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    SqlExprCvtParaT cvtPara = {0};
    DmSchemaT schema = {0};
    ExprT *funcExpr = NULL;
    if (opt->scene != SQL_EXPR_INDEX) {
        ret = SqlCvtAASchema2DmSchema(memCtx, opt->aaSchema, &schema);
        if (ret != GMERR_OK) {
            IRDestroyExprWithOp(memCtx, &singleExpr);
            return ret;
        }
        cvtPara.schema = &schema;
        cvtPara.lvarNum = schema.propeNum;
    }
    SqlExprCvtParaT *para = opt->scene == SQL_EXPR_INDEX ? opt->cvtPara : &cvtPara;

    // 聚合函数
    if (func->funcType < FUNC_TYPE_AGG_END) {
        ret = SqlCvtAggFuncExpr2ExprVar(memCtx, para, func, &funcExpr);
        // 非聚合函数
    } else {
        ret = SqlCvtNonAggFuncExpr2Expr(memCtx, para, func, &funcExpr);
    }

    DbDynMemCtxFree(memCtx, schema.properties);

    if (ret != GMERR_OK) {
        IRDestroyExprWithOp(memCtx, &singleExpr);
        return ret;
    }
    funcOp->func = funcExpr;
    *pItemExpr = singleExpr;

    return GMERR_OK;
}

#ifndef IDS_HAOTIAN
Status SqlCreateCompoundExpr(DbMemCtxT *memCtx, SqlSelectTypeE multiSelectOp, IRExprT **retExpr)
{
    DB_POINTER2(memCtx, retExpr);
    IRExprT *compoundExpr = NULL;
    IROpTypeE opType = IR_OPTYPE_N;
    Status ret = GMERR_OK;
    switch (multiSelectOp) {
        case SELECT_UNION:
        case SELECT_UNIONALL: {
            opType = IR_LOGOP_UNION;
            ret = IRCreateExprWithOp(memCtx, opType, &compoundExpr);
            if (ret != GMERR_OK) {
                break;
            }
            OpLogicalAAUnionT *unionOp = (OpLogicalAAUnionT *)(void *)compoundExpr->op;
            unionOp->isUnionAll = (multiSelectOp == SELECT_UNIONALL);
            unionOp->isDistinctByProps = true;
            break;
        }
        default: {
            SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE, "Sql analyzer: confused compound select type|%" PRIu32 "|",
                (uint32_t)multiSelectOp);
            return GMERR_INVALID_PARAMETER_VALUE;
        }
    }
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Sql analyzer: unable to create compound logical op|%s|.", GetOpName(opType));
        return ret;
    }
    *retExpr = compoundExpr;
    return GMERR_OK;
}

Status SqlCreateCompoundExprWithSchema(
    DbMemCtxT *memCtx, SqlSelectTypeE multiSelectOp, const AASchemaT *schema, IRExprT **retExpr)
{
    Status ret = SqlCreateCompoundExpr(memCtx, multiSelectOp, retExpr);
    if (ret != GMERR_OK) {
        return ret;
    }
    AASchemaT *dstSchema = IRGetOpAASchema((*retExpr)->op);
    ret = IRCopyAASchema(memCtx, schema, dstSchema);
    if (ret != GMERR_OK) {
        IRDestroyExprWithOp(memCtx, retExpr);
        return ret;
    }
    return ret;
}

Status SqlCreateSubQueryScanExpr(DbMemCtxT *memCtx, uint32_t subQueryId, IRExprT **retExpr)
{
    IRExprT *subQueryScanExpr = NULL;
    Status ret = IRCreateExprWithOp(memCtx, IR_LOGOP_SUB_QUERY_SCAN, &subQueryScanExpr);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Sql analyzer: unable to create logical op|%s|.", GetOpName(IR_LOGOP_SUB_QUERY_SCAN));
        return ret;
    }
    OpLogicalAASubQueryScanT *subQueryOp = (OpLogicalAASubQueryScanT *)(void *)subQueryScanExpr->op;
    subQueryOp->subQueryId = subQueryId;

    *retExpr = subQueryScanExpr;
    return GMERR_OK;
}

Status SqlCreateSubQueryScanWithAASchema(
    DbMemCtxT *memCtx, uint32_t subQueryId, const AASchemaT *schema, IRExprT **retExpr)
{
    Status ret = SqlCreateSubQueryScanExpr(memCtx, subQueryId, retExpr);
    if (ret != GMERR_OK) {
        return ret;
    }
    AASchemaT *dstSchema = IRGetOpAASchema((*retExpr)->op);
    // 深拷贝, 不同irplan的schema内的properties的addr相互独立.
    // 优化器列id转换的时候,子树id转换以后,不影响父树的id转换.
    ret = IRDeepCopyAASchema(memCtx, schema, dstSchema);
    if (ret != GMERR_OK) {
        IRDestroyExprWithOp(memCtx, retExpr);
        return ret;
    }
    return GMERR_OK;
}

Status SqlCreateIrStmt4SubQuery(SqlIrStmtT *oldIrStmt, SqlIrStmtT *newIrStmt)
{
    DB_POINTER2(oldIrStmt, newIrStmt);
    *newIrStmt = *oldIrStmt;  // 拷贝SqlIrStmtT中信息，除irPlan外均和父查询相同

    // SqlIrStmtT中的irPlan需要重新create
    // 内存释放点：SqlCreateItemOpAndIRPlan4InSubQuery中释放，或service的SqlDoExecuteReq中释放
    newIrStmt->irPlan = IRCreatePlan(oldIrStmt->memCtx);
    if (newIrStmt->irPlan == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer unable to alloc memory for sub-plan of ir plan.");
        return GMERR_OUT_OF_MEMORY;
    }
    IRPlanT *oldIrPlan = oldIrStmt->irPlan;
    IRPlanT *newIrPlan = newIrStmt->irPlan;
    newIrPlan->paramNum = oldIrPlan->paramNum;
    newIrPlan->parentPlan = oldIrPlan;  // 指向父ir plan
    return GMERR_OK;
}
#endif
