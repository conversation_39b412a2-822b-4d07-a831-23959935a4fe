/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:  the impl for SQL verify select analyzer
 * Author: SQL
 * Create: 2024-01-30
 */

#include "db_utils.h"
#include "ee_systbl.h"
#include "cpl_ir_aa_schema.h"
#include "cpl_sql_analyzer_common.h"
#include "cpl_sql_verify_expr.h"
#include "cpl_sql_verify_select.h"
#include "cpl_sql_analyzer_hidden_column.h"
#include "qry_sysview.h"

// 支持select查询的视图
static const char *g_selectSupportView[] = {
    // 特定条件下支持的视图
    "V$QRY_DML_INFO",
#ifdef FEATURE_MEMDATA
    "V$COM_MEM_SUMMARY", "V$CATA_TABLESPACE_INFO", "V$SERVER_MEMORY_OVERHEAD", "V$STORAGE_MEMDATA_STAT",
    "V$STORAGE_SHMEM_INFO",
#endif
#ifdef FEATURE_DURABLE_MEMDATA
    "V$STORAGE_DURABLE_MEMDATA_STAT", "V$DB_SERVER_KEY_RESOURCE",
#endif
#ifdef DB_MEM_TRACE
    "V$DYN_MEM_TRACE_INFO", "V$SHMEM_TRACE_INFO",
#endif
    "V$CATA_GENERAL_INFO", "V$CATA_LABEL_SUBS_INFO", "V$CATA_NAMESPACE_INFO", "V$CATA_VERTEX_LABEL_CHECK_INFO",
    "V$CATA_VERTEX_LABEL_INFO", "V$COM_DYN_CTX", "V$COM_SHMEM_CTX", "V$COM_SHMEM_GROUP", "V$COM_SHMEM_USAGE_STAT",
    "V$COM_TABLE_MEM_SUMMARY", "V$CONFIG_PARAMETERS", "V$CST_SHMEM_INFO", "V$DB_PROBE_DATA", "V$DB_PROBE_DATA",
    "V$DB_SERVER", "V$DRT_COM_STAT", "V$DRT_CONN_STAT", "V$DRT_CONN_SUBS_STAT", "V$DRT_CONN_THREAT_STAT",
    "V$DRT_DATA_PLANE_CHANNEL_STAT", "V$DRT_LONG_OPERATION_STAT", "V$DRT_PIPE_STAT", "V$DRT_SCHEDULE_STAT",
    "V$DRT_WORKER_POOL_STAT", "V$DRT_WORKER_STAT", "V$MEM_COMPACT_TASKS_STAT", "V$PRIVILEGE_ROLE_STAT",
    "V$PRIVILEGE_USER_STAT", "V$QRY_DML_OPER_STATIS", "V$QRY_DYNMEM", "V$QRY_SESSION", "V$QRY_TRX_MONITOR_STAT",
    "V$STORAGE_ART_INDEX_STAT", "V$STORAGE_BTREE_INDEX_STAT", "V$STORAGE_BUFFERPOOL_STAT",
    "V$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "V$STORAGE_FSM_STAT", "V$STORAGE_HASH_CLUSTER_INDEX_STAT",
    "V$STORAGE_HASH_COLLISION_STAT", "V$STORAGE_HASH_INDEX_STAT", "V$STORAGE_HASH_LINKLIST_INDEX_STAT",
    "V$STORAGE_HEAP_STAT", "V$STORAGE_HEAP_VERTEX_LABEL_STAT", "V$STORAGE_INDEX_GLOBAL_STAT", "V$STORAGE_LOCK_OVERVIEW",
    "V$STORAGE_PERSISTENT_STAT", "V$STORAGE_SPACE_INFO", "V$STORAGE_TABLE_SHM_INFO", "V$STORAGE_TRX_DETAIL",
    "V$STORAGE_TRX_STAT", "V$STORAGE_UNDO_PURGER_INFO", "V$STORAGE_UNDO_STAT", "V$STORAGE_VERTEX_COUNT",
    "V$SYS_MODULE_MEM_INFO", "V$STORAGE_BUFFERPOOL_LOAD_TABLE_STAT", "V$STORAGE_REDO_INFO"};

Status SqlStrAllocAndCopy(DbMemCtxT *memCtx, const char *srcStr, char **destStr)
{
    if (srcStr != NULL) {
        uint32_t strLen = (uint32_t)DM_STR_LEN(srcStr);
        *destStr = (char *)DbDynMemCtxAlloc(memCtx, strLen);
        if (*destStr == NULL) {
            return GMERR_OUT_OF_MEMORY;
        }
        errno_t err = strcpy_s(*destStr, strLen, srcStr);
        if (err != EOK) {
            SQL_ERROR_LOG(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy sql string, ret=%" PRId32 ".", err);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }
    return GMERR_OK;
}

Status SqlColStrAllocAndCopy(DbMemCtxT *memCtx, SqlDotNameT *srcTable, char *columnName, SqlExprColumnT *colExpr)
{
    Status ret = SqlStrAllocAndCopy(memCtx, srcTable->database, &(colExpr->dbName));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SqlStrAllocAndCopy(memCtx, srcTable->name, &(colExpr->tableName));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SqlStrAllocAndCopy(memCtx, srcTable->originalName, &(colExpr->originalTableName));
    if (ret != GMERR_OK) {
        return ret;
    }
    return SqlStrAllocAndCopy(memCtx, columnName, &(colExpr->columnName));
}

Status SqlAddColumnToTargetList(DbMemCtxT *memCtx, SqlDotNameT *srcTable, DbListT *newTargetList, char *srcColName)
{
    Status ret = GMERR_OK;
    SqlExprT *newExpr = SqlExprMakeColumn(memCtx, NULL, NULL, NULL);
    if (newExpr == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to malloc for column expr|%s|.", srcColName);
        return GMERR_OUT_OF_MEMORY;
    }
    SqlExprColumnT *newColExpr = (SqlExprColumnT *)(void *)newExpr->op;
    SqlTargetT *newTarget = (SqlTargetT *)DbDynMemCtxAlloc(memCtx, sizeof(SqlTargetT));
    if (newTarget == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to malloc for target column|%s|.", srcColName);
        goto ERROR;
    }
    (void)memset_s(newTarget, sizeof(SqlTargetT), 0, sizeof(SqlTargetT));
    ret = SqlColStrAllocAndCopy(memCtx, srcTable, srcColName, newColExpr);
    if (ret != GMERR_OK) {
        goto ERROR;
    }
    newTarget->value = newExpr;

    ret = DbAppendListItem(newTargetList, &newTarget);
    if (ret != GMERR_OK) {
        goto ERROR;
    }
    return GMERR_OK;
ERROR:
    DbDynMemCtxFree(memCtx, newExpr);
    DbDynMemCtxFree(memCtx, newTarget);
    return ret;
}

#define FUNCTION_WITH_NON_ARG (0)
#define FUNCTION_WITH_ONE_ARG (1)
#define FUNCTION_WITH_THREE_ARG (3)
#define FUNCTION_WITH_TEN_ARG (10)

typedef enum SqlFuncArgsType {
    ARGS_TYPE_NUMERIC,
    ARGS_TYPE_STRING,
} SqlFuncArgsTypeE;

typedef Status (*SqlVerifyArgsFunc)(SqlExprFuncT *func, const SqlBuildItemOptT *opt);

static inline bool SqlVerifyFuncArgsDataTypeIsMatch(SqlFuncArgsTypeE target, DbDataTypeE var)
{
    if (target == ARGS_TYPE_NUMERIC) {
        return (var >= DB_DATATYPE_INT8 && var <= DB_DATATYPE_DOUBLE && var != DB_DATATYPE_BOOL);
    }
    return var == DB_DATATYPE_STRING;
}

static Status SqlVerifyFuncColumnArgs(SqlExprColumnT *argInfo, const SqlBuildItemOptT *opt, SqlFuncArgsTypeE argsType)
{
    DB_POINTER2(argInfo, opt);
    bool isIndexScene = (opt->scene == SQL_EXPR_INDEX);
    uint32_t propeNum = isIndexScene ? opt->tableSchema->propeNum : opt->symbolTable->schema->propAA.propNum;
    for (uint32_t i = 0; i < propeNum; i++) {
        DmPropertySchemaT *prope =
            isIndexScene ? &opt->tableSchema->properties[i] : opt->symbolTable->schema->propAA.properties[i];
        if (DbStrCmp(prope->name, argInfo->columnName, true) != 0) {
            continue;
        }
        if (!SqlVerifyFuncArgsDataTypeIsMatch(argsType, prope->dataType)) {
            SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE,
                "Sql analyzer function pass args with unexpect type, args type: %u, schema type: %u.",
                (uint32_t)argsType, (uint32_t)prope->dataType);
            return GMERR_INVALID_PARAMETER_VALUE;
        }
    }

    return GMERR_OK;
}

static Status SqlVerifyFuncConstArgs(SqlExprConstT *argInfo, SqlFuncArgsTypeE argsType)
{
    DB_POINTER(argInfo);
    // 入参为NULL时，直接返回OK
    if (argInfo->arg.type == DB_DATATYPE_NULL) {
        return GMERR_OK;
    }
    if (!SqlVerifyFuncArgsDataTypeIsMatch(argsType, argInfo->arg.type)) {
        SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE, "Sql analyzer function const args with unexpect type.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

static inline bool SqlVerifyFuncArgsCnt(SqlExprFuncT *func, uint32_t argCntMin, uint32_t argCntMax)
{
    uint32_t argCnt = (func->args == NULL) ? 0 : DbListGetItemCnt(func->args);
    return (argCnt >= argCntMin && argCnt <= argCntMax);
}

static Status SqlVerifyFuncArgsDataType(SqlExprFuncT *func, const SqlBuildItemOptT *opt, SqlFuncArgsTypeE targetType)
{
    if (func->args == NULL) {
        return GMERR_OK;
    }

    Status ret = GMERR_OK;
    uint32_t argsCnt = DbListGetItemCnt(func->args);
    for (uint32_t i = 0; i < argsCnt; i++) {
        SqlExprT *argExpr = *(SqlExprT **)DbListItem(func->args, i);
        SqlExprOpTypeE type = (SqlExprOpTypeE)argExpr->op->type;
        if (type == SQL_EXPR_ITEM_COLUMN) {  // e.g.: select sum(id) from tbl;
            ret = SqlVerifyFuncColumnArgs((SqlExprColumnT *)(void *)argExpr->op, opt, targetType);
        } else if (type == SQL_EXPR_ITEM_CONST) {  // e.g.: select sum(100);
            ret = SqlVerifyFuncConstArgs((SqlExprConstT *)(void *)argExpr->op, targetType);
        } else if (type != SQL_EXPR_OP_UMINUS && type != SQL_EXPR_OP_UPLUS) {
            ret = GMERR_FEATURE_NOT_SUPPORTED;
            SQL_ERROR_LOG(ret, "Only support column and const.");
        }
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static bool SqlFuncArgsIsVectorDistOperator(SqlExprFuncT *func)
{
    if (func->args == NULL) {
        return false;
    }

    SqlExprT *argExpr = *(SqlExprT **)DbListItem(func->args, 0);
    return SqlIsVectorDistOperator((SqlExprOpTypeE)argExpr->op->type);
}

static Status SqlVerifyCountAggFunc(SqlExprFuncT *func, const SqlBuildItemOptT *opt)
{
    if (!SqlVerifyFuncArgsCnt(func, FUNCTION_WITH_NON_ARG, FUNCTION_WITH_ONE_ARG)) {
        SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE, "Function count with unexpect parameter cnt.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (SqlFuncArgsIsVectorDistOperator(func)) {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Function with unexpect parameter type");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

static Status SqlVerifyCommonAggFunc(SqlExprFuncT *func, const SqlBuildItemOptT *opt)
{
    if (!SqlVerifyFuncArgsCnt(func, FUNCTION_WITH_ONE_ARG, FUNCTION_WITH_ONE_ARG)) {
        SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE, "Function with unexpect parameter count.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    return SqlVerifyFuncArgsDataType(func, opt, ARGS_TYPE_NUMERIC);
}

static Status SqlVerifyMaxAndMinAggFunc(SqlExprFuncT *func, const SqlBuildItemOptT *opt)
{
    if (!SqlVerifyFuncArgsCnt(func, FUNCTION_WITH_ONE_ARG, FUNCTION_WITH_THREE_ARG)) {
        SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE, "Function with unexpect parameter count.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    return SqlVerifyFuncArgsDataType(func, opt, ARGS_TYPE_NUMERIC);
}

static inline Status SqlVerifyNonArgsFunc(SqlExprFuncT *func, const SqlBuildItemOptT *opt)
{
#ifdef IDS_HAOTIAN
    DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Sql verify: unsupported random and sql version func.");
    return GMERR_FEATURE_NOT_SUPPORTED;
#endif
    if (func->args != NULL && func->args->count != 0) {
        SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE, "Function should not have args.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

static Status SqlVerifyCommonTimeFunc(SqlExprFuncT *func, const SqlBuildItemOptT *opt)
{
#ifdef IDS_HAOTIAN
    DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Sql verify: unsupported time func.");
    return GMERR_FEATURE_NOT_SUPPORTED;
#endif
    uint32_t argCntMax = FUNCTION_WITH_TEN_ARG;  // 当前最大允许传入10个入参，后续按需修改
    uint32_t argCntMin = (func->funcType == FUNC_TYPE_STRFTIME) ? FUNCTION_WITH_ONE_ARG : FUNCTION_WITH_NON_ARG;
    if (!SqlVerifyFuncArgsCnt(func, argCntMin, argCntMax)) {
        SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE, "Time function with unexpect parameter count.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    return GMERR_OK;
}

static inline Status SqlVerifyStrCommonFunc(SqlExprFuncT *func, const SqlBuildItemOptT *opt)
{
    if (!SqlVerifyFuncArgsCnt(func, FUNCTION_WITH_ONE_ARG, FUNCTION_WITH_ONE_ARG)) {
        SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE, "Common function with unexpect parameter count.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    return SqlVerifyFuncArgsDataType(func, opt, ARGS_TYPE_STRING);
}

static inline Status SqlVerifyFuncArgsCntOnly(SqlExprFuncT *func, const SqlBuildItemOptT *opt)
{
    DB_UNUSED(opt);
    if (!SqlVerifyFuncArgsCnt(func, FUNCTION_WITH_ONE_ARG, FUNCTION_WITH_ONE_ARG)) {
        SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE, "Function only check args count, with unexpect parameter count.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return SqlVerifyFuncArgsDataType(func, opt, ARGS_TYPE_STRING);
}

static inline Status SqlVerifyStrMultiParaFunc(SqlExprFuncT *func, const SqlBuildItemOptT *opt)
{
    DB_UNUSED(opt);
    if (!SqlVerifyFuncArgsCnt(func, FUNCTION_WITH_THREE_ARG, FUNCTION_WITH_THREE_ARG)) {
        SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE, "Function only check args count, with unexpect parameter count.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status SqlVerifyFuncExpr(SqlExprT *expr, SqlBuildItemOptT *opt)
{
    DB_POINTER(expr);
    SqlExprFuncT *func = (SqlExprFuncT *)(void *)expr->op;
    const SqlVerifyArgsFunc sqlVerifyFuncArgsTbl[] = {
        [FUNC_TYPE_COUNT] = SqlVerifyCountAggFunc,
        [FUNC_TYPE_MIN] = SqlVerifyMaxAndMinAggFunc,
        [FUNC_TYPE_MAX] = SqlVerifyMaxAndMinAggFunc,
        [FUNC_TYPE_SUM] = SqlVerifyCommonAggFunc,
        [FUNC_TYPE_AVG] = SqlVerifyCommonAggFunc,
        [FUNC_TYPE_DATE] = SqlVerifyCommonTimeFunc,
        [FUNC_TYPE_TIME] = SqlVerifyCommonTimeFunc,
        [FUNC_TYPE_DATETIME] = SqlVerifyCommonTimeFunc,
        [FUNC_TYPE_JULIANDAY] = SqlVerifyCommonTimeFunc,
        [FUNC_TYPE_STRFTIME] = SqlVerifyCommonTimeFunc,
        [FUNC_TYPE_RANDOM] = SqlVerifyNonArgsFunc,
        [FUNC_TYPE_ABS] = SqlVerifyCommonAggFunc,
        [FUNC_TYPE_UPPER] = SqlVerifyStrCommonFunc,
        [FUNC_TYPE_LOWER] = SqlVerifyStrCommonFunc,
        [FUNC_TYPE_LENGTH] = SqlVerifyFuncArgsCntOnly,
        [FUNC_TYPE_SQLVERSION] = SqlVerifyNonArgsFunc,
        [FUNC_TYPE_SUBSTR] = SqlVerifyStrMultiParaFunc,
    };
    if (func->funcType < FUNC_TYPE_END && sqlVerifyFuncArgsTbl[func->funcType] != NULL) {
        return sqlVerifyFuncArgsTbl[func->funcType](func, opt);
    }
    SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE, "Undefined function type: %" PRIu32 ".", func->funcType);
    return GMERR_INVALID_PARAMETER_VALUE;
}

static Status SqlVerifyTargetColumnExpr(
    DbMemCtxT *memCtx, const DbListT *fromClause, SqlExprT *expr, SqlBuildItemOptT *opt)
{
    if (opt->symbolTable == NULL) {
        // 视图暂不支持列别名
        return SqlVerifyColumnExprWithoutSymbolTable(fromClause, expr);
    }
    Status ret = SqlVerifyColumnExprForTargetList(memCtx, expr, opt);
    if (ret != GMERR_OK) {
        return ret;
    }
    return SqlVerifyVectorColumnExpr(expr);
}

Status SqlVerifyInExpr(SqlExprT *expr)
{
    DB_POINTER(expr);
    SqlExprT *exprLeft = expr->children[0];
    SqlExprT *exprRight = expr->children[1];
    SqlExprOpTypeE lType = exprLeft->op->type;
    SqlExprOpTypeE rType = exprRight->op->type;
    // 目前只能是 X In List，且左不能是list，右只能是list
    if (lType == SQL_EXPR_ITEM_LIST || rType != SQL_EXPR_ITEM_LIST) {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql Analyzer: unable to build in expr, lType: %u, rType: %u.",
            (uint32_t)lType, (uint32_t)rType);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    // IN List中的每个元素目前不能是List
    DbListT *exprList = ((SqlExprListT *)exprRight->op)->exprList;
    uint32_t cnt = DbListGetItemCnt(exprList);
    for (uint32_t i = 0; i < cnt; ++i) {
        SqlExprT *exprInList = *(SqlExprT **)DbListItem(exprList, i);
        if (exprInList->op->type == SQL_EXPR_ITEM_LIST) {
            SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql Analyzer: unable to build in right expr operator: %u.",
                (uint32_t)exprInList->op->type);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
    }

    return GMERR_OK;
}
#ifndef IDS_HAOTIAN
Status SqlVerifySubQuery(DbMemCtxT *memCtx, SqlExprT *expr)
{
    SqlSelectStmtT *select = (SqlSelectStmtT *)(((SqlExprSubQueryT *)(void *)expr->op)->select);
    if (select->fromClause == NULL) {
        return GMERR_OK;
    }

    // 2. target list里只允许子查询目标列为一列，否则语法错误
    if (DbListGetItemCnt(select->targetList) > 1) {
        SQL_ERROR_LOG(GMERR_SEMANTIC_ERROR, "Only single-column sub-select is allowed in target list.");
        return GMERR_SEMANTIC_ERROR;
    }

    // 3. 如果子查询里面有多个表链接，语法错误
    SqlTargetT *target = *(SqlTargetT **)DbListItem(select->targetList, 0);
    if (target->isStar) {
        SqlSrcItemT *src = (*(SqlSrcItemT **)DbListItem(select->fromClause, 0));
        if (src->type == SRC_JOIN_LIST) {
            SQL_ERROR_LOG(GMERR_SEMANTIC_ERROR, "Only single-table subquery is allowed in from list.");
            return GMERR_SEMANTIC_ERROR;
        }
    }

    return GMERR_OK;
}
#endif

static Status SqlVerifyAllLiteralVec4TargetList(
    DbMemCtxT *memCtx, SqlExprT *leftChild, SqlExprT *rightChild, SqlExprOpTypeE type)
{
    SqlExprConstT *leftChildConst = (SqlExprConstT *)(void *)leftChild->op;
    SqlExprConstT *rightChildConst = (SqlExprConstT *)(void *)rightChild->op;
    if (leftChildConst->arg.type == DB_DATATYPE_STRING && rightChildConst->arg.type == DB_DATATYPE_STRING) {
        Status ret = SqlLiteralVector2Vector(memCtx, DB_INVALID_UINT32, DB_DATATYPE_FLOATVECTOR, &leftChildConst->arg);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 左孩子的向量字节数是右孩子期望的向量字节数，即向量维度相同
        return SqlLiteralVector2Vector(
            memCtx, leftChildConst->arg.value.length, DB_DATATYPE_FLOATVECTOR, &rightChildConst->arg);
    }
    SQL_ERROR_LOG(
        GMERR_FEATURE_NOT_SUPPORTED, "Sql Analyzer: expected const operand of the operator: %u.", (uint32_t)type);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

static Status SqlExprVerifyLiteralVecAndCol4TargetList(
    DbMemCtxT *memCtx, SqlExprT *leftChild, SqlExprT *rightChild, SqlExprOpTypeE type)
{
    // 左右孩子有一个是 SQL_EXPR_ITEM_CONST ，另一个是 SQL_EXPR_ITEM_COLUMN
    SqlColumnLocationT *columnLocation = NULL;
    SqlOpBaseT *otherOp = NULL;

    if (leftChild->op->type == SQL_EXPR_ITEM_COLUMN) {
        columnLocation = &((SqlExprColumnT *)(void *)leftChild->op)->location;
        otherOp = rightChild->op;
    } else {
        columnLocation = &((SqlExprColumnT *)(void *)rightChild->op)->location;
        otherOp = leftChild->op;
    }

    // SQL_EXPR_ITEM_COLUMN 类型的 SqlExprT，其属性的数据类型必须是向量类型
    if (!DmDataTypeIsVector(columnLocation->property->dataType)) {
        SQL_ERROR_LOG(GMERR_DATATYPE_MISMATCH, "Sql Analyzer: unexpected const or column operand of the operator: %u.",
            (uint32_t)type);
        return GMERR_DATATYPE_MISMATCH;
    }
    // 运算符另一边要么是 floatvector 类型的常量, 要么是绑定值
    if (otherOp->type == SQL_EXPR_ITEM_CONST) {
        SqlExprConstT *constOp = (SqlExprConstT *)(void *)otherOp;
        if (constOp->arg.type == DB_DATATYPE_NULL) {
            SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql Analyzer: unsupported null operand of the operator: %u.",
                (uint32_t)type);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
        // SQL_EXPR_ITEM_CONST 类型，则其 DmValueT 数据类型必须均为 DB_DATATYPE_STRING，且必须可以转为向量类型，
        // 并且 SQL_EXPR_ITEM_CONST 的 expr 的向量字节数是 SQL_EXPR_ITEM_COLUMN 期望的向量字节数，即向量维度相同
        return SqlLiteralVector2Vector(
            memCtx, columnLocation->property->size, columnLocation->property->dataType, &constOp->arg);
    } else if (otherOp->type != SQL_EXPR_ITEM_PARA) {
        SQL_ERROR_LOG(
            GMERR_FEATURE_NOT_SUPPORTED, "Sql Analyzer: unsupported operand in targetList, type: %u.", (uint32_t)type);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

static Status SqlVerifyVectorExpr4TargetList(DbMemCtxT *memCtx, SqlExprT *expr)
{
    SqlExprOpTypeE type = expr->op->type;
    SqlExprT *leftChild = expr->children[0];
    SqlExprT *rightChild = expr->children[1];

    if (leftChild->op->type == SQL_EXPR_ITEM_CONST && rightChild->op->type == SQL_EXPR_ITEM_CONST) {
        // Case 1: expr的左右孩子均为 SQL_EXPR_ITEM_CONST 类型，则其 DmValueT 数据类型必须均为
        //         DB_DATATYPE_STRING，且STRING必须可以转为向量类型，且左右孩子的向量维度必须匹配
        return SqlVerifyAllLiteralVec4TargetList(memCtx, leftChild, rightChild, type);
    } else if (leftChild->op->type == SQL_EXPR_ITEM_COLUMN && rightChild->op->type == SQL_EXPR_ITEM_COLUMN) {
        // Case 2: expr的左右孩子均为 SQL_EXPR_ITEM_COLUMN 类型，则其属性的数据类型必须是向量类型
        return SqlVerifyAllColumn4VectorDistExpr(leftChild, rightChild, type);
    } else if (SqlExprTypeIsConstOrColumn(leftChild->op->type) && SqlExprTypeIsConstOrColumn(rightChild->op->type)) {
        // Case 3: expr的左右孩子有一个是 SQL_EXPR_ITEM_CONST，另一个是 SQL_EXPR_ITEM_CONST，校验同 Case 3 和 Case 4
        return SqlExprVerifyLiteralVecAndCol4TargetList(memCtx, leftChild, rightChild, type);
    } else if (leftChild->op->type == SQL_EXPR_ITEM_PARA || rightChild->op->type == SQL_EXPR_ITEM_PARA) {
        return GMERR_OK;
    }
    // Case 4: 其他情况均报错
    SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql Analyzer: unexpected operand of the operator: %u.", (uint32_t)type);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

static Status SqlVerifyExpr4TargetList(
    DbMemCtxT *memCtx, const DbListT *fromClause, SqlExprT *expr, SqlBuildItemOptT *targetOpt)
{
    Status ret = GMERR_OK;
    SqlExprOpTypeE type = (SqlExprOpTypeE)expr->op->type;
    if (type == SQL_EXPR_ITEM_CONST) {
        // Case 1: 常量直接返回
        return GMERR_OK;
    } else if (type == SQL_EXPR_ITEM_PARA) {
        // Case 2: target list中支持绑定参数
        return GMERR_OK;
    } else if (type == SQL_EXPR_ITEM_COLUMN) {
        // Case 3: 校验属性列
        return SqlVerifyTargetColumnExpr(memCtx, fromClause, expr, targetOpt);
    } else if (type == SQL_EXPR_OP_FUN) {
        // Case 4: 校验函数类型
        return SqlVerifyFuncExpr(expr, targetOpt);
    } else if (type == SQL_EXPR_OP_LIKE) {
        // Case 5: 校验like类运算符（无孩子节点）
        SqlExprLikeT *likeExpr = (SqlExprLikeT *)(void *)expr->op;
        ret = SqlVerifyExpr4TargetList(memCtx, fromClause, likeExpr->src, targetOpt);
        if (ret != GMERR_OK) {
            return ret;
        }
        return SqlVerifyExpr4TargetList(memCtx, fromClause, likeExpr->dst, targetOpt);
    } else if (type < SQL_EXPR_OP_BINARY_END) {
        // Case 6: 校验有孩子节点的二元运算符和一元运算符
        if (type >= SQL_EXPR_OP_BINARY_BEGIN) {  // binary operators
            ret = SqlVerifyExpr4TargetList(memCtx, fromClause, expr->children[1], targetOpt);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        ret = SqlVerifyExpr4TargetList(memCtx, fromClause, expr->children[0], targetOpt);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 校验表达式树中的向量距离运算符
        if (SqlIsVectorDistOperator(type)) {
            return SqlVerifyVectorExpr4TargetList(memCtx, expr);
        }
        return GMERR_OK;
    } else if (type == SQL_EXPR_OP_SUBQUERY) {
#ifdef IDS_HAOTIAN
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Unsupported type %d in targetList.", (uint32_t)type);
        return GMERR_FEATURE_NOT_SUPPORTED;
#else
        return SqlVerifySubQuery(memCtx, expr);
#endif
    }
    SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupported type %d in targetList.", (uint32_t)type);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

#ifndef IDS_HAOTIAN
static Status SqlAddColumn4Star(
    DbMemCtxT *memCtx, SqlExprColumnT *colExpr, char *tableName, char *propName, DbListT *newTargetList)
{
    Status ret = GMERR_OK;
    // table.* 添加对应列
    if (DbStrCmp(colExpr->tableName, tableName, true) == 0) {
        SqlDotNameT srcTable = {.database = colExpr->dbName, .name = tableName, .originalName = NULL};
        ret = SqlAddColumnToTargetList(memCtx, &srcTable, newTargetList, propName);
        if (ret != GMERR_OK) {
            SQL_ERROR_LOG(ret, "Sql analyzer: unable to append column|%s.%s| to targetList.", tableName, propName);
            return ret;
        }
    }
    return GMERR_OK;
}

static Status SqlVerifyTargetAndExpandStar(
    DbMemCtxT *memCtx, SqlTargetT *target, const DbListT *fromClause, SymbolTableT *symbolTable, DbListT *newTargetList)
{
    Status ret = GMERR_OK;
    SqlExprColumnT *colExpr = (SqlExprColumnT *)(void *)target->value->op;
    uint32_t cnt = symbolTable->schema->propAA.propNum;
    uint32_t targetNum = DbListGetItemCnt(newTargetList);

    for (uint32_t i = 0; i < cnt; i++) {
        // 若支持扩展投影列,此处需要适配.
        DmPropertySchemaT *prop = symbolTable->schema->propAA.properties[i];
        DbListT *srcTableNames = DbListItem(&symbolTable->labelNames, i);
        char *propName = prop->name;
        uint32_t labelCnt = DbListGetItemCnt(srcTableNames);
        for (uint32_t j = 0; j < labelCnt; j++) {
            AliasOrgNamePairT *srcTableName = DbListItem(srcTableNames, j);
            char *tableName = (srcTableName->alias != NULL) ? srcTableName->alias : srcTableName->orgName;
            ret = SqlAddColumn4Star(memCtx, colExpr, tableName, propName, newTargetList);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    // table.* 找不到对应的table.
    if (colExpr->tableName != NULL && targetNum == DbListGetItemCnt(newTargetList)) {
        SQL_ERROR_LOG(GMERR_UNDEFINED_TABLE, "Sql analyzer: table|%s| is not exist.", colExpr->tableName);
        return GMERR_UNDEFINED_TABLE;
    }
    return GMERR_OK;
}
#else
static Status SqlVerifyTargetAndExpandStar(
    DbMemCtxT *memCtx, SqlTargetT *target, const DbListT *fromClause, SymbolTableT *symbolTable, DbListT *newTargetList)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}
#endif

static Status SqlExpandStarBySymbolTable(
    DbMemCtxT *memCtx, SqlTargetT *target, SymbolTableT *symbolTable, DbListT *newTargetList)
{
    Status ret = GMERR_OK;
    SqlExprColumnT *colExpr = (SqlExprColumnT *)(void *)target->value->op;
    uint32_t cnt = symbolTable->schema->propAA.propNum;
    uint32_t tag = 0;
    for (uint32_t i = 0; i < cnt; i++) {
        // 若子查询内支持扩展投影列,此处需要适配.
        DmPropertySchemaT *prop = symbolTable->schema->propAA.properties[i];
        if (IsSqlHiddenColumn(prop->name)) {
            tag++;
            continue;
        }
        DbListT *srcTableNames = DbListItem(&symbolTable->labelNames, i - tag);
        AliasOrgNamePairT *srcTableName = DbListGetItems(srcTableNames);
        char *tableName = srcTableName->alias != NULL ? srcTableName->alias : srcTableName->orgName;

        SqlDotNameT table = {.database = colExpr->dbName, .name = tableName, .originalName = NULL};
        ret = SqlAddColumnToTargetList(memCtx, &table, newTargetList, prop->name);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

#ifndef IDS_HAOTIAN
Status SqlGetStarTargetListByTable(
    DbMemCtxT *memCtx, DmVertexLabelT *label, SqlSrcTableT *srcTable, DbListT **starTargetList)
{
    Status ret = GMERR_OK;
    // 1. 构建新的 newTargetList.
    DbListT *newTargetList = (DbListT *)DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
    if (newTargetList == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to malloc for starTargetList.");
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateListWithExtendSize(newTargetList, sizeof(SqlTargetT *), SQL_LIST_EXTEND_SIZE, memCtx);

    DmSchemaT *schema = label->metaVertexLabel->schema;
    for (uint32_t i = 0; i < schema->propeNum; i++) {
        DmPropertySchemaT prop = schema->properties[i];
        SqlDotNameT table = {.database = srcTable->database, .name = srcTable->tableName, .originalName = NULL};
        ret = SqlAddColumnToTargetList(memCtx, &table, newTargetList, prop.name);
        if (ret != GMERR_OK) {
            DbDestroyList(newTargetList);
            DbDynMemCtxFree(memCtx, newTargetList);
            return ret;
        }
    }
    *starTargetList = newTargetList;
    return GMERR_OK;
}
#endif

static Status SqlVerifyOneTarget(
    SqlTargetT *target, OptVerifyTargetListT *opt, DbListT **newTargetList, AASchemaT *schema)
{
    Status ret = GMERR_OK;
    DbMemCtxT *memCtx = opt->memCtx;
    const DbListT *fromClause = opt->fromClause;
    SqlExprT *expr = target->value;
    SqlExprOpTypeE type = (SqlExprOpTypeE)expr->op->type;
    SymbolTableT *symbolTable = opt->symbolTable;
    SqlExprColumnT *colExpr = (SqlExprColumnT *)(void *)expr->op;
    // 不带*号, 校验成功后, 直接将 target追加到 newTargetList.
    if (!target->isStar) {
        if (type == SQL_EXPR_ITEM_COLUMN && symbolTable != NULL) {  // 视图暂不支持列别名
            AliasOrgNamePairT aliasPair = {.alias = target->aliasName, .orgName = colExpr->columnName};
            ret = DbAppendListItem(&symbolTable->columnAlias, &aliasPair);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        SqlBuildItemOptT targetOpt = {0};
        targetOpt.symbolTable = symbolTable;
        targetOpt.aaSchema = schema;
        targetOpt.tableList = fromClause;
        targetOpt.irStmt = opt->irStmt;
        // 递归地遍历表达式树校验target是否合法
        ret = SqlVerifyExpr4TargetList(memCtx, fromClause, expr, &targetOpt);
        if (ret != GMERR_OK) {
            return ret;
        }
        return DbAppendListItem(*newTargetList, &target);
    }
    // 带*号校验扩展.
    if (colExpr->tableName == NULL) {
        // 单独的* 扩展.
        if (opt->starTargetList == NULL) {
            // select * 扩展.
            return SqlExpandStarBySymbolTable(memCtx, target, opt->symbolTable, *newTargetList);
        } else {
            //  create view as select * , returing * 扩展.(暂未回合)
            return DbAppendListAll(*newTargetList, opt->starTargetList);
        }
    } else {
        // table.* 校验扩展
        return SqlVerifyTargetAndExpandStar(memCtx, target, fromClause, opt->symbolTable, *newTargetList);
    }
}

Status SqlVerifyTargetList(OptVerifyTargetListT *opt, DbListT **retList, AASchemaT *schema)
{
    DB_POINTER2(opt, retList);  // 非select语句的target list校验 symbolTable 可以为NULL

    Status ret = GMERR_OK;
    DbMemCtxT *memCtx = opt->memCtx;
    DbListT *targetList = opt->targetList;

    // 1. 构建新的 newTargetList.
    DbListT *newTargetList = (DbListT *)DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
    if (newTargetList == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to malloc for targetList.");
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateListWithExtendSize(newTargetList, sizeof(SqlTargetT *), SQL_LIST_EXTEND_SIZE, memCtx);

    // 2. 遍历处理每一个 target.
    for (uint32_t i = 0; i < targetList->count; i++) {
        SqlTargetT *target = *(SqlTargetT **)DbListItem(targetList, i);
        ret = SqlVerifyOneTarget(target, opt, &newTargetList, schema);
        if (ret != GMERR_OK) {
            goto ERROR;
        }
    }
    uint32_t targetCnt = DbListGetItemCnt(newTargetList);
    if (targetCnt > SQL_TARGET_LIST_LIMIT) {
        ret = GMERR_PROGRAM_LIMIT_EXCEEDED;
        SQL_ERROR_LOG(ret, "Sql analyzer: target list count|%u| exceeds limit|%u|.", targetCnt, SQL_TARGET_LIST_LIMIT);
        goto ERROR;
    }
    *retList = newTargetList;
    return GMERR_OK;
ERROR:
    DbDestroyList(newTargetList);
    DbDynMemCtxFree(memCtx, newTargetList);
    return ret;
}

#ifndef IDS_HAOTIAN
Status SqlVerifyColumnExpr4OrderBy(const DbListT *targetList, SqlOrderByT *orderByItem)
{
    uint32_t targetCnt = DbListGetItemCnt(targetList);
    bool isFound = false;
    SqlExprColumnT *sortExpr = (SqlExprColumnT *)(void *)orderByItem->sortExpr->op;
    for (uint32_t j = 0; j < targetCnt; ++j) {
        SqlTargetT *targetItem = *(SqlTargetT **)DbListItem(targetList, j);
        if ((SqlExprOpTypeE)targetItem->value->op->type != SQL_EXPR_ITEM_COLUMN) {
            continue;
        }
        SqlExprColumnT *targetColumn = (SqlExprColumnT *)(void *)targetItem->value->op;
        if (DbStrCmp(sortExpr->columnName, targetColumn->columnName, true) == 0) {
            isFound = true;
            break;
        }
    }
    // 当前 sortExpr 仅支持 target list 中的表属性列
    if (!isFound) {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: sorted property %s is not in target list.",
            sortExpr->columnName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}
#endif

static Status SqlVerifyVecDistExpr4OrderBy(SqlOrderByT *orderByItem)
{
    SqlExprOpTypeE leftExprType = orderByItem->sortExpr->children[0]->op->type;
    SqlExprOpTypeE rightExprType = orderByItem->sortExpr->children[1]->op->type;
    // 当前 order by 的表达式只允许 向量属性列 <=> 向量字面量/绑定参数 或 向量字面量/绑定参数 <=> 向量属性列
    if ((SqlExprTypeIsConstOrPara(leftExprType) && rightExprType == SQL_EXPR_ITEM_COLUMN) ||
        (leftExprType == SQL_EXPR_ITEM_COLUMN && SqlExprTypeIsConstOrPara(rightExprType))) {
        return GMERR_OK;
    }
    SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupported expression for orderBy.");
    return GMERR_FEATURE_NOT_SUPPORTED;
}

static Status SqlVerifyOrderByClause(const DbListT *targetList, const DbListT *orderByList)
{
    Status ret = GMERR_OK;
    uint32_t orderByCnt = DbListGetItemCnt(orderByList);
    for (uint32_t i = 0; i < orderByCnt; ++i) {
        SqlOrderByT *orderByItem = *(SqlOrderByT **)DbListItem(orderByList, i);
        // 当前不支持指定字符串比较规则;
        if (orderByItem->collate != NULL) {
            SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupported specified string cmp for orderBy.");
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
        // 当前不支持orderby后跟子查询
        if (SqlExprHasType(orderByItem->sortExpr, SQL_EXPR_OP_SUBQUERY)) {
            SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: sub-query is not supported for orderBy.");
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
        if (SqlIsVectorDistOperator(orderByItem->sortExpr->op->type)) {
            // Case 1: order by item是向量距离函数运算符类型
            ret = SqlVerifyVecDistExpr4OrderBy(orderByItem);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

// 当前仅支持正负数形式 不支持表达式形式
static Status SqlVerifyLimitClause(const SqlLimitT *limitInfo)
{
    if (limitInfo->num == NULL) {
        SQL_ERROR_LOG(GMERR_SEMANTIC_ERROR, "Sql analyzer: without specified tuples number for limit clause.");
        return GMERR_SEMANTIC_ERROR;
    }
    if ((limitInfo->offset != NULL && !SqlExprIsInteger(limitInfo->offset)) || !SqlExprIsInteger(limitInfo->num)) {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupport expr format para for limit clause.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

static inline bool SqlVerifySrcItemIsUnsupport(const SqlSrcItemT *src)
{
    if ((src->joinType & SQL_OUTER_JOIN) != 0 && (src->joinType & SQL_LEFT_JOIN) == 0) {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer only support left outer join for outer join.");
        return true;
    }
    return false;
}

static Status SqlVerifySrcItemValid(SessionT *session, SqlSrcItemT *src)
{
    Status ret = GMERR_OK;
    // Unsupport tmp namespace yet
    uint32_t nspId = session->namespaceId;
    char *tableName = src->srcTable->tableName;
    if (SqlCheckIsSysTableName(tableName)) {
        src->label = SysTableGetSqlVertexLabelByName(tableName, DbGetInstanceByMemCtx(session->memCtx));
    } else {
        src->label = SqlGetVertexLabelByName(DbGetInstanceByMemCtx(session->memCtx), session->dbId, nspId, tableName);
    }
    if (src->label == NULL) {
        SQL_ERROR_LOG(GMERR_UNDEFINED_TABLE, "Sql analyzer: table|%s| is not exist.", tableName);
        return GMERR_UNDEFINED_TABLE;
    }
    if (src->indexBy != NULL) {
        ret = SqlGetIndexLabelForIndexBy(src->label, src->indexBy);
        if (ret != GMERR_OK) {
            SQL_ERROR_LOG(ret, "Sql Analyzer unable to get indexlabel for indexby while processing select.");
        }
    }
    return ret;
}

static bool IsSupportView(char *viewName)
{
    DbStrToUpper(viewName);
    for (uint32_t i = 0; i < ELEMENT_COUNT(g_selectSupportView); i++) {
        if (strcmp(viewName, g_selectSupportView[i]) == 0) {
            return true;
        }
    }
    return false;
}

static Status SqlGetViewByName(QryStmtT *stmt, char *viewName)
{
    if (!IsSupportView(viewName)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "select do not support view %s.", viewName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return SvGetVertexLabelByViewName(stmt, viewName);
}

static Status SqlVerifyFromClause(SessionT *session, DbMemCtxT *memCtx, SqlSelectStmtT *select)
{
    DbListT *fromClause = select->fromClause;
    Status ret = GMERR_OK;
    uint32_t srcCnt = DbListGetItemCnt(fromClause);
    if (srcCnt > SQL_JOINED_TABLE_LIMIT) {
        SQL_ERROR_LOG(GMERR_INVALID_PARAMETER_VALUE, "Sql analyzer: the joined tables count(%u) exceeds limit(%u).",
            srcCnt, SQL_JOINED_TABLE_LIMIT);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    for (uint32_t i = 0; i < srcCnt; i++) {
        SqlSrcItemT *src = *(SqlSrcItemT **)DbListItem(fromClause, i);
        if (SqlVerifySrcItemIsUnsupport(src)) {
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
        if (select->isSvLabel) {
            QryStmtT *stmt = session->sqlCtx->currentFusionStmt;
            stmt->session = session;
            ret = SqlGetViewByName(stmt, src->srcTable->tableName);
            if (ret != GMERR_OK) {
                SQL_ERROR_LOG(ret, "can not get view vertix label:%s", src->srcTable->tableName);
                return ret;
            }
        }
        if (src->type == SRC_TABLE_REF) {
            ret = SqlVerifySrcItemValid(session, src);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

Status SqlVerifySelectExtendClause(const SqlSelectStmtT *select)
{
    DB_POINTER(select);
    Status ret = GMERR_OK;
    // step 1. verify orderBy clause
    if (select->orderBy != NULL) {
        ret = SqlVerifyOrderByClause(select->targetList, select->orderBy);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // step 2. verify limit clause
    if (select->limitClause != NULL) {
        return SqlVerifyLimitClause(select->limitClause);
    }
    return ret;
}

static Status SqlVerifyTargetListWithoutFromClause(DbMemCtxT *memCtx, DbListT *targetList)
{
    for (uint32_t i = 0; i < targetList->count; i++) {
        SqlTargetT *curTarget = *(SqlTargetT **)DbListItem(targetList, i);
        SqlExprOpTypeE type = (SqlExprOpTypeE)curTarget->value->op->type;
        if (curTarget->isStar || type == SQL_EXPR_ITEM_COLUMN) {
            SQL_ERROR_LOG(GMERR_SEMANTIC_ERROR, "Sql analyzer unable to select * or prop if table is not specified.");
            return GMERR_SEMANTIC_ERROR;
        }

        if (type == SQL_EXPR_OP_FUN) {
            Status ret = SqlVerifyFuncExpr(curTarget->value, NULL);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static uint32_t GetAggFuncNum(DbListT *targetList, DbListT *orderByList)
{
    uint32_t aggFuncNum = 0;
    uint32_t itemCnt = DbListGetItemCnt(targetList);
    for (uint32_t i = 0; i < itemCnt; i++) {
        SqlTargetT *target = *(SqlTargetT **)DbListItem(targetList, i);
        if ((SqlExprOpTypeE)target->value->op->type == SQL_EXPR_OP_FUN &&
            ((SqlExprFuncT *)(void *)target->value->op)->funcType < FUNC_TYPE_AGG_END) {
            aggFuncNum++;
        }
    }

    if (orderByList == NULL) {
        return aggFuncNum;
    }

    itemCnt = DbListGetItemCnt(orderByList);
    for (uint32_t i = 0; i < itemCnt; i++) {
        SqlOrderByT *orderBy = *(SqlOrderByT **)DbListItem(orderByList, i);
        if ((SqlExprOpTypeE)orderBy->sortExpr->op->type == SQL_EXPR_OP_FUN &&
            ((SqlExprFuncT *)orderBy->sortExpr->op)->funcType < FUNC_TYPE_AGG_END) {
            aggFuncNum++;
        }
    }
    return aggFuncNum;
}

static bool HasAggInSrvLabel(SqlSelectStmtT *select)
{
    if (select->groupBy != NULL) {
        return true;
    }
    uint32_t aggFuncNum = GetAggFuncNum(select->targetList, select->orderBy);
    return aggFuncNum > 0;
}

static Status SqlVerifyGroupByAndOrderBy(DbMemCtxT *memCtx, SqlSelectStmtT *select)
{
    DB_POINTER2(memCtx, select);
    // 系统视图不支持聚合函数查询，不支持投影
    if (select->isSvLabel && HasAggInSrvLabel(select)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "system view do not support groupBy query.");
        return GMERR_SEMANTIC_ERROR;
    }
    // 系统视图不支持排序
    if (select->isSvLabel && select->orderBy != NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "system view do not support orderBy query.");
        return GMERR_SEMANTIC_ERROR;
    }
    return GMERR_OK;
}

Status SqlVerifySelectForSingleSelect(SessionT *session, DbMemCtxT *memCtx, SqlSelectStmtT *select)
{
    DB_POINTER3(session, memCtx, select);
    // step 1. verify target list if there is none from clause
    if (select->fromClause == NULL) {
        return SqlVerifyTargetListWithoutFromClause(memCtx, select->targetList);
    }

    // step 2. verify from clause and fill vertexLabel, indexLabel info in SqlSrcItemT
    Status ret = SqlVerifyFromClause(session, memCtx, select);
    if (ret != GMERR_OK) {
        return ret;
    }

    // step3. verify group by and order by
    ret = SqlVerifyGroupByAndOrderBy(memCtx, select);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

#ifndef IDS_HAOTIAN
Status SqlVerifySelectForCompoundSelect(SessionT *session, DbMemCtxT *memCtx, SqlSelectStmtT *select)
{
    DB_POINTER3(session, memCtx, select);
    Status ret = SqlVerifySelectForSingleSelect(session, memCtx, select);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (select->pPrior != NULL) {
        return SqlVerifySelectForCompoundSelect(session, memCtx, select->pPrior);
    }
    return GMERR_OK;
}
#else
Status SqlVerifySelectForCompoundSelect(SessionT *session, DbMemCtxT *memCtx, SqlSelectStmtT *select)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}
#endif
