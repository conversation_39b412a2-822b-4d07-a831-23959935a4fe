/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: the impl for SQL create Index analyzer
 * Author: SQL
 * Create: 2024-01-30
 */

#include "dm_cache_basic.h"
#include "dm_data_index_sql.h"
#include "cpl_sql_common.h"
#include "cpl_public_sql_parser_common.h"
#include "cpl_sql_analyzer_common.h"
#include "cpl_sql_analyzer_hidden_column.h"
#include "cpl_sql_verify_expr.h"
#include "cpl_sql_build_expr.h"
#include "cpl_sql_build_ir_tree.h"
#include "cpl_sql_analyzer_create_index.h"

typedef struct SqlVectorIndexType {
    const char *indexTypeName;  // 索引类型名称字符串
    DmIndexTypeE indexType;     // 索引类型枚举
} SqlVectorIndexTypeT;

static SqlVectorIndexTypeT g_vectorIndexType[] = {
    {"GSDISKANN", DISKANN_INDEX},
    {"GSLPASMEM", LPASMEM_INDEX},
};

typedef struct SqlVectorDistFunc {
    const char *distFuncName;  // 距离函数名称字符串
    DmVecDistTypeE distType;   // 距离函数枚举
} SqlVectorDistFuncT;

static SqlVectorDistFuncT g_vectorDistFunc[] = {{"L2", DIST_TYPE_L2}, {"COSINE", DIST_TYPE_COSINE}};

#define ANN_INDEX_PARAMETER_EXTEND_SIZE 3u

// 在IVF索引中，最大簇内方差的上下界，用于判断簇内各向量之间的离散程度
#define MAX_CLUSTER_VARIANCE_LOWER_LIMIT 0.0
#define MAX_CLUSTER_VARIANCE_UPPER_LIMIT DB_MAX_DOUBLE

// 在IVF索引中，最小簇间距离的上下界，用于判断两个簇的中心点间距离
#define CLUSTERS_MIN_DIST_LOWER_LIMIT 0.0
#define CLUSTERS_MIN_DIST_UPPER_LIMIT 2.0

#define QUEUE_SIZE_LOWER_LIMIT 10u
#define QUEUE_SIZE_UPPER_LIMIT 1000u

#define OUT_DEGREE_LOWER_LIMIT 1u
#define OUT_DEGREE_UPPER_LIMIT 1200u

#define LPAS_OUT_DEGREE_LOWER_LIMIT 32u
#define LPAS_OUT_DEGREE_UPPER_LIMIT 256u

#define LPAS_BUILD_SEARCH_LIST_SIZE_LOWER_LIMIT 16u
#define LPAS_BUILD_SEARCH_LIST_SIZE_UPPER_LIMIT 128u

#define BATCHCOUNT_LOWER_LIMIT 50u
#define BATCHCOUNT_UPPER_LIMIT 500000u

static SqlAnnParameterInfoT g_annWithParameterInfo[] = {
    {
        .parameterName = "QUEUE_SIZE",
        .lowerLimit = {.value = {.type = DB_DATATYPE_INT64, .value.ulongValue = QUEUE_SIZE_LOWER_LIMIT},
            .isOpenInterval = false,
            .valueStr = "10"},
        .upperLimit = {.value = {.type = DB_DATATYPE_INT64, .value.ulongValue = QUEUE_SIZE_UPPER_LIMIT},
            .isOpenInterval = false,
            .valueStr = "1000"},
        .parameterType = DISKANN_QUEUE_SIZE,
        .valueTargetDataType = DB_DATATYPE_UINT16,
        .indexType = DISKANN_INDEX,
    },
    {
        .parameterName = "OUT_DEGREE",
        .lowerLimit = {.value = {.type = DB_DATATYPE_INT64, .value.ulongValue = OUT_DEGREE_LOWER_LIMIT},
            .isOpenInterval = false,
            .valueStr = "1"},
        .upperLimit = {.value = {.type = DB_DATATYPE_INT64, .value.ulongValue = OUT_DEGREE_UPPER_LIMIT},
            .isOpenInterval = false,
            .valueStr = "1200"},
        .parameterType = DISKANN_OUT_DEGREE,
        .valueTargetDataType = DB_DATATYPE_UINT16,
        .indexType = DISKANN_INDEX,
    },
    {
        .parameterName = "LPAS_BUILD_SEARCH_LIST_SIZE",
        .lowerLimit = {.value = {.type = DB_DATATYPE_INT64,
                           .value.ulongValue = LPAS_BUILD_SEARCH_LIST_SIZE_LOWER_LIMIT},
            .isOpenInterval = false,
            .valueStr = "16"},
        .upperLimit = {.value = {.type = DB_DATATYPE_INT64,
                           .value.ulongValue = LPAS_BUILD_SEARCH_LIST_SIZE_UPPER_LIMIT},
            .isOpenInterval = false,
            .valueStr = "128"},
        .parameterType = LPAS_BUILD_SEARCH_LIST_SIZE,
        .valueTargetDataType = DB_DATATYPE_UINT16,
        .indexType = LPASMEM_INDEX,
    },
    {
        .parameterName = "LPAS_OUT_DEGREE",
        .lowerLimit = {.value = {.type = DB_DATATYPE_INT64, .value.ulongValue = LPAS_OUT_DEGREE_LOWER_LIMIT},
            .isOpenInterval = false,
            .valueStr = "32"},
        .upperLimit = {.value = {.type = DB_DATATYPE_INT64, .value.ulongValue = LPAS_OUT_DEGREE_UPPER_LIMIT},
            .isOpenInterval = false,
            .valueStr = "256"},
        .parameterType = LPAS_OUT_DEGREE,
        .valueTargetDataType = DB_DATATYPE_UINT16,
        .indexType = LPASMEM_INDEX,
    },
    {
        .parameterName = "PARALLEL_BUILD_BATCH_SIZE",
        .lowerLimit = {.value = {.type = DB_DATATYPE_INT64, .value.ulongValue = BATCHCOUNT_LOWER_LIMIT},
            .isOpenInterval = false,
            .valueStr = "50"},
        .upperLimit = {.value = {.type = DB_DATATYPE_INT64, .value.ulongValue = BATCHCOUNT_UPPER_LIMIT},
            .isOpenInterval = false,
            .valueStr = "500000"},
        .parameterType = PARALLEL_BUILD_BATCH_SIZE,
        .valueTargetDataType = DB_DATATYPE_UINT32,
        .indexType = LPASMEM_INDEX,
    },
    {
        .parameterName = "ENABLE_LPAS_PQ",
        .lowerLimit = {.value.type = DB_DATATYPE_BOOL},
        .parameterType = LPAS_ENABLE_PQ,
        .valueTargetDataType = DB_DATATYPE_BOOL,
        .indexType = LPASMEM_INDEX,
    },
};

static Status SqlVerifyName4WithParameter(DmIndexTypeE indexType, const char *parameterName, uint32_t *index)
{
    for (uint32_t i = 0; i < ELEMENT_COUNT(g_annWithParameterInfo); ++i) {
        if (DbStrCmp(parameterName, g_annWithParameterInfo[i].parameterName, true) == 0) {
            if (indexType != g_annWithParameterInfo[i].indexType) {
                SQL_ERROR_LOG(GMERR_SEMANTIC_ERROR,
                    "Sql analyzer: parameter name: %s can only be used to set the index type %d.", parameterName,
                    indexType);
                return GMERR_SEMANTIC_ERROR;
            }
            *index = i;
            return GMERR_OK;
        }
    }
    SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupported parameter name: %s when setting parameter.",
        parameterName);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status SqlVerifyIndexName(uint32_t dbId, SqlCreateIndexStmtT *parsedStmt, uint32_t nspId, DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(parsedStmt, vertexLabel);
    char *indexName = parsedStmt->indexInfo->indexName;
    if (SqlCheckNameLength(indexName)) {
        SQL_ERROR_LOG(GMERR_NAME_TOO_LONG, "Sql analyzer verify a invalidate index name(%s).", indexName);
        return GMERR_NAME_TOO_LONG;
    }
    char *upperName = NULL;
    Status ret = SqlAllocUpperStr(indexName, &upperName);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Sql analyzer unable to upper the index name(%s).", indexName);
        return ret;
    }

    DmVertexLabelT *label = SqlGetVertexLabelByName(DbGetInstanceByMemCtx(vertexLabel->memCtx), dbId, nspId, upperName);
    SqlFreeUpperStr(upperName);
    if (label != NULL) {
        SQL_ERROR_LOG(GMERR_DUPLICATE_TABLE, "Sql analyzer the index name(%s) is duplicate with table.", indexName);
        return GMERR_DUPLICATE_TABLE;
    }
    DmVlIndexLabelT *indexLabel = DmGetIndexLabelByNameIfExist(vertexLabel, indexName);
    if (indexLabel != NULL) {
        SQL_ERROR_LOG(GMERR_DUPLICATE_OBJECT, "Sql analyzer: create a duplicate index name(%s).", indexName);
        return GMERR_DUPLICATE_OBJECT;
    }
    return GMERR_OK;
}

// Generate a tableList from parsedStmt and vertexLabel.
Status SqlGenTableList(DbMemCtxT *memCtx, SqlCreateIndexStmtT *parsedStmt, DmVertexLabelT *vertexLabel, DbListT **list)
{
    DB_POINTER4(memCtx, parsedStmt, vertexLabel, list);
    SqlTableRefT *table = (SqlTableRefT *)DbDynMemCtxAlloc(memCtx, sizeof(SqlTableRefT));
    if (table == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer alloc table ref memory not successfully.");
        return GMERR_OUT_OF_MEMORY;
    }
    table->tableName = parsedStmt->indexInfo->tableName;
    table->database = parsedStmt->indexInfo->database;
    table->aliasName = NULL;
    Status ret = SqlCreateSrcItemByTable(memCtx, table, NULL, vertexLabel, list);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Sql analyzer create src item by table is not successfully.");
        return ret;
    }
    return GMERR_OK;
}

// 索引列中出现的列信息，必须是存在于原Schema的列，比如a+1 中的a必须要存在
static Status SqlVerifyIndexColumnTraverse(SqlExprT *expr, void *procPara)
{
    SqlIndexColumnInfoT *idxColInfo = (SqlIndexColumnInfoT *)procPara;
    SqlExprOpTypeE type = expr->op->type;
    // 表达式索引支持的函数只能是标量函数，并输出值可以由输入决定的
    if (type == SQL_EXPR_OP_FUN) {
        SqlExprFuncTypeE funType = SqlExprGetFuncType(expr);
        if (funType < FUNC_TYPE_ABS || funType == FUNC_TYPE_SQLVERSION) {
            SQL_ERROR_LOG(GMERR_SYNTAX_ERROR, "Sql Analyzer: the function is misused in index.");
            return GMERR_SYNTAX_ERROR;
        }
    }
    if (type != SQL_EXPR_ITEM_COLUMN) {
        return GMERR_OK;
    }
    return SqlVerifyColumnExprWithoutSymbolTable(idxColInfo->tableList, expr);
}

static Status SqlVerifyScalarIndexColumn(DbMemCtxT *memCtx, DbListT *tableList, SqlIndexColumnT *indexColumn)
{
    // 非向量索引不可指定距离函数
    if (indexColumn->distanceFuncName != NULL) {
        SQL_ERROR_LOG(GMERR_SYNTAX_ERROR, "Sql analyzer set distance func %s for scalar index is undefined.",
            indexColumn->distanceFuncName);
        return GMERR_SYNTAX_ERROR;
    }
    SqlIndexColumnInfoT idxColInfo = {0};
    idxColInfo.tableList = tableList;
    Status ret = SqlExprTraverse(indexColumn->columnExpr, SqlVerifyIndexColumnTraverse, &idxColInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 向量字段上不允许建标量索引
    SqlExprColumnT *colExpr = (SqlExprColumnT *)(void *)indexColumn->columnExpr->op;
    if (DmDataTypeIsVector(colExpr->arg.type)) {
        SQL_ERROR_LOG(
            GMERR_FEATURE_NOT_SUPPORTED, "Scalar index cannot be created on vector column %s.", colExpr->columnName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

static Status SqlVerifyVectorIndexColumn(DbMemCtxT *memCtx, DbListT *tableList, SqlIndexColumnT *indexColumn)
{
    // 向量索引必须指定距离函数
    SqlExprColumnT *colExpr = (SqlExprColumnT *)(void *)indexColumn->columnExpr->op;
    if (indexColumn->distanceFuncName == NULL) {
        SQL_ERROR_LOG(GMERR_SYNTAX_ERROR, "Sql analyzer distance func %s must be set for vector index %s.",
            indexColumn->distanceFuncName, colExpr->columnName);
        return GMERR_SYNTAX_ERROR;
    }
    SqlIndexColumnInfoT idxColInfo = {0};
    idxColInfo.tableList = tableList;
    Status ret = SqlExprTraverse(indexColumn->columnExpr, SqlVerifyIndexColumnTraverse, &idxColInfo);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 标量字段上不允许建向量索引
    if (!DmDataTypeIsVector(colExpr->arg.type)) {
        SQL_ERROR_LOG(
            GMERR_FEATURE_NOT_SUPPORTED, "Vector index cannot be created on scalar column %s.", colExpr->columnName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    // 必须是已支持的向量距离函数
    for (uint32_t i = 0; i < ELEMENT_COUNT(g_vectorDistFunc); ++i) {
        if (DbStrCmp(indexColumn->distanceFuncName, g_vectorDistFunc[i].distFuncName, true) == 0) {
            return GMERR_OK;
        }
    }
    SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED,
        "Sql analyzer: unsupported distance function name: %s when creating vector index.",
        indexColumn->distanceFuncName);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

static Status SqlVerifyCols4Index(
    DbMemCtxT *memCtx, SqlCreateIndexStmtT *parsedStmt, DmVertexLabelT *vertexLabel, bool isVectorIndex)
{
    DB_POINTER3(memCtx, parsedStmt, vertexLabel);
    // 向量索引不支持复合索引
    uint32_t colNum = DbListGetItemCnt(parsedStmt->indexColList);
    if (isVectorIndex && colNum != 1u) {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED,
            "Sql analyzer: multi-column (%u) indexes does not support when creating vector index %s.", colNum,
            parsedStmt->indexInfo->indexName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    // 索引中属性的最大数目，与系统限制保持一致
    if (colNum > DM_MAX_KEY_PROPE_NUM) {
        SQL_ERROR_LOG(GMERR_PROGRAM_LIMIT_EXCEEDED, "Sql analyzer there are too many columns(%u) in index %s.", colNum,
            parsedStmt->indexInfo->indexName);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    DbListT *tableList = NULL;
    Status ret = SqlGenTableList(memCtx, parsedStmt, vertexLabel, &tableList);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Sql analyzer get table list for verify index column not successfully.");
        return ret;
    }
    if (isVectorIndex) {
        // 向量索引不支持复合索引
        SqlIndexColumnT *indexColumn = *(SqlIndexColumnT **)DbListItem(parsedStmt->indexColList, 0);
        return SqlVerifyVectorIndexColumn(memCtx, tableList, indexColumn);
    }
    for (uint32_t i = 0; i < colNum; i++) {
        SqlIndexColumnT *indexColumn = *(SqlIndexColumnT **)DbListItem(parsedStmt->indexColList, i);
        ret = SqlVerifyScalarIndexColumn(memCtx, tableList, indexColumn);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status SqlVerifyIndexUnsupportedFeature(SqlCreateIndexStmtT *parsedStmt)
{
    if (parsedStmt->whereClause != NULL) {
        SQL_ERROR_LOG(
            GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: where clause not support when creating vector index.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    // 如果未设置索引类型名，默认为b-tree索引，即标量索引
    if (parsedStmt->indexTypeName == NULL) {
        parsedStmt->indexType = BTREE_INDEX;
        return GMERR_OK;
    }

    // 向量索引不支持unique
    if (parsedStmt->isUnique) {
        SQL_ERROR_LOG(
            GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unique constrain is not supported when creating vector index.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    // 向量索引的向量类型名称必须是已支持的
    for (uint32_t i = 0; i < ELEMENT_COUNT(g_vectorIndexType); ++i) {
        if (DbStrCmp(parsedStmt->indexTypeName, g_vectorIndexType[i].indexTypeName, true) == 0) {
            parsedStmt->indexType = g_vectorIndexType[i].indexType;
            return GMERR_OK;
        }
    }
    SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupported index type: %s when creating vector index.",
        parsedStmt->indexTypeName);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

static Status SqlVerifyWithParameterClause(DbMemCtxT *memCtx, SqlCreateIndexStmtT *parsedStmt)
{
    DbOamapT map = {0};  // key: char *, parameter name
    Status ret = DbOamapInit(&map, 0, DbOamapStringCompare, memCtx, true);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Sql analyzer: unable to init hash map when verifying with parameter clause.");
        return ret;
    }
    uint32_t count = DbListGetItemCnt(parsedStmt->withParameterList);
    for (uint32_t i = 0; i < count; ++i) {
        SqlAnnParameterPairT *pair = *(SqlAnnParameterPairT **)DbListItem(parsedStmt->withParameterList, i);

        // 检查参数名称是否合法
        uint32_t index = 0;
        ret = SqlVerifyName4WithParameter(parsedStmt->indexType, pair->parameterName, &index);
        if (ret != GMERR_OK) {
            break;
        }

        // 检查参数名是否重复
        char *name = pair->parameterName;
        ret = DbOamapInsert(&map, DbStrToHash32(name), name, NULL, NULL);
        if (ret == GMERR_DUPLICATE_OBJECT) {
            SQL_ERROR_LOG(ret, "Sql analyzer: unable to insert into hash map with duplicate parameter name %s.", name);
            break;
        }
        if (ret != GMERR_OK) {
            SQL_ERROR_LOG(ret, "Sql analyzer: unable to insert into hash map with parameter name %s.", name);
            break;
        }

        // 检查参数值是否合法
        ret = SqlVerifyParameterValue(&g_annWithParameterInfo[index], pair);
        if (ret != GMERR_OK) {
            break;
        }
        if (pair->parameterType == LPAS_ENABLE_PQ) {
            parsedStmt->isPq = ((SqlExprConstT *)(void *)pair->constValueExpr->op)->arg.value.boolValue;
        }
    }

    DbOamapDestroy(&map);
    return ret;
}

static Status SqlVerifyLpasPQIndex(SqlCreateIndexStmtT *parsedStmt, DmVertexLabelT *verLabel)
{
    DB_POINTER2(parsedStmt, verLabel);
    if (parsedStmt->indexTypeName == NULL || (!parsedStmt->isPq)) {
        return GMERR_OK;
    }
    bool isExistPqVec = false;
    for (uint32_t i = 0; i < verLabel->metaVertexLabel->schema->propeNum; i++) {
        DmPropertySchemaT *property = &verLabel->metaVertexLabel->schema->properties[i];
        if (IsSqlHiddenAutoIdColumn(property->name)) {
            isExistPqVec = true;
            break;
        }
    }
    if (isExistPqVec == false) {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer does not find the hidden autoincrement column.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    uint32_t curPqIndexNum = 0;
    for (uint32_t i = 0; i < verLabel->metaVertexLabel->secIndexNum; i++) {
        DmVlIndexLabelT *index = &verLabel->metaVertexLabel->secIndexes[i];
        if (DmIndexLabelIsPqIndex(index)) {
            curPqIndexNum++;
        }
    }
    if (curPqIndexNum >= verLabel->vertexLabelConstraint->pqIndexNum) {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "The curPqIndexNum(%u) are more than existing number(%u).",
            curPqIndexNum, verLabel->vertexLabelConstraint->pqIndexNum);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

static Status SqlVerifyTableName(
    SessionT *session, DbMemCtxT *memCtx, SqlCreateIndexStmtT *parsedStmt, DmVertexLabelT **label)
{
    uint32_t nspId = session->namespaceId;
    char *nspName = parsedStmt->indexInfo->database;
    char *tableName = parsedStmt->indexInfo->tableName;

    if (SqlCheckIsSysTableName(tableName)) {
        SQL_ERROR_LOG(
            GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer the name(%s) is same as system table when deleting.", tableName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
#if defined(IDS_HAOTIAN)
    if (SqlExprsExistNotColumn(parsedStmt->indexColList)) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupported expr index.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
#endif  // IDS_HAOTIAN
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(memCtx);
    Status ret = SqlGetNspIdAndVertexLabelByName(dbInstance, session->dbId, nspName, tableName, &nspId, label);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Sql analyzer get nsp id by name(%s) is not successfully when create index.", nspName);
        return ret;
    }
    if (*label == NULL) {
        SQL_ERROR_LOG(GMERR_UNDEFINED_TABLE, "Sql analyzer table(%s) is not exist when create index.", tableName);
        return GMERR_UNDEFINED_TABLE;
    }
    return ret;
}

Status SqlVerifyCreateIndex(
    SessionT *session, DbMemCtxT *memCtx, SqlCreateIndexStmtT *parsedStmt, DmVertexLabelT **vertexLabel)
{
    DB_POINTER4(session, memCtx, parsedStmt, vertexLabel);

    DmVertexLabelT *label = NULL;
    Status ret = SqlVerifyTableName(session, memCtx, parsedStmt, &label);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SqlVerifyIndexName(session->dbId, parsedStmt, session->namespaceId, label);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SqlVerifyIndexUnsupportedFeature(parsedStmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SqlVerifyCols4Index(memCtx, parsedStmt, label, parsedStmt->indexTypeName != NULL);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (parsedStmt->withParameterList != NULL) {
        ret = SqlVerifyWithParameterClause(memCtx, parsedStmt);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = SqlVerifyLpasPQIndex(parsedStmt, label);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    *vertexLabel = label;
    return GMERR_OK;
}

static Status SqlGetIndexDistType(const char *distanceFuncName, DmVecDistTypeE *distType)
{
    if (distanceFuncName == NULL) {
        // 标量索引中距离函数无意义
        *distType = DIST_TYPE_BUTT;
        return GMERR_OK;
    }
    for (uint32_t i = 0; i < ELEMENT_COUNT(g_vectorDistFunc); ++i) {
        if (DbStrCmp(distanceFuncName, g_vectorDistFunc[i].distFuncName, true) == 0) {
            *distType = g_vectorDistFunc[i].distType;
            return GMERR_OK;
        }
    }
    SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Index type %s not yet supported.", distanceFuncName);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

#ifndef IDS_HAOTIAN
Status SqlFillExprIndexDetail(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, DbListT *colList, DmSortTypeE *sortType,
    DmExprIndexInfoT *exprIdxInfo)
{
    uint32_t exprCnt = DbListGetItemCnt(colList);
    DbDataTypeE *dataTypes = exprIdxInfo->resDataTypes;
    ExprArrayT *exprArray = &(CastToFunc(exprIdxInfo->exprs)->array);
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < exprCnt; ++i) {
        SqlIndexColumnT *indexCol = *(SqlIndexColumnT **)DbListItem(colList, i);
        SqlExprT *sqlExpr = indexCol->columnExpr;
        ExprT *expr = NULL;
        SqlExprCvtParaT cvtPara = {0};
        cvtPara.schema = vertexLabel->metaVertexLabel->schema;
        cvtPara.lvarNum = vertexLabel->metaVertexLabel->schema->propeNum;
        cvtPara.needCfgPropDataType = true;
        ret = SqlCvtSqlExpr2Expr(sqlExpr, memCtx, &cvtPara, &expr);
        if (ret != GMERR_OK) {
            SQL_ERROR_LOG(ret, "Sql analyzer: unable to cvt sqlExpr to expr.");
            return ret;
        }
        IRExprT *irExpr = NULL;
        SqlBuildItemOptT opt = {};
        opt.scene = SQL_EXPR_INDEX;
        opt.cvtPara = &cvtPara;
        ret = SqlBuildItemOpTree(memCtx, sqlExpr, &opt, 0, &irExpr);
        if (ret != GMERR_OK) {
            SQL_ERROR_LOG(ret, "Sql analyzer: unable to cvt sqlExpr to ir expr.");
            return ret;
        }
        ret = DbAppendListItem(&exprIdxInfo->irExprs, irExpr);
        if (ret != GMERR_OK) {
            return ret;
        }
        exprArray->expr[i] = expr;
        dataTypes[i] = cvtPara.dataInfo.type;
        static_assert((uint32_t)DM_SORT_END == (uint32_t)SQL_SORT_END, "DM_SORT_END and SQL_SORT_END should be equal.");
        sortType[i] = (DmSortTypeE)indexCol->sortType;
    }
    exprArray->num = exprCnt;
    return GMERR_OK;
}

/**
 * @brief 填充表达式索引的信息
 * @param[in] memCtx         内存上下文
 * @param[in] vertexLabel    被创建索引的表信息
 * @param[in] colList        表达式列表
 * @param[out] indexLabel    待创建的索引信息
 * @return GMERR_OK或错误码
 */
Status SqlFillExprIndexInfo(
    DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, DbListT *colList, DmVlIndexLabelT *indexLabel)
{
    indexLabel->isExprIdx = true;
    uint32_t exprCnt = DbListGetItemCnt(colList);
    Status ret = GMERR_OK;
    DmExprIndexInfoT *exprIdxInfo = (DmExprIndexInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(DmExprIndexInfoT));
    if (exprIdxInfo == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to allocate expr index information.");
        return GMERR_OUT_OF_MEMORY;
    }

    DbDataTypeE *dataTypes = (DbDataTypeE *)DbDynMemCtxAlloc(memCtx, exprCnt * sizeof(DbDataTypeE));
    if (dataTypes == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to allocate data types.");
        ret = GMERR_OUT_OF_MEMORY;
        goto ERROR;
    }

    DbCreateList(&exprIdxInfo->irExprs, sizeof(IRExprT), memCtx);

    exprIdxInfo->exprs = (void *)ExprMakeTuple(memCtx, exprCnt);
    if (exprIdxInfo->exprs == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to make tuple.");
        ret = GMERR_OUT_OF_MEMORY;
        goto ERROR;
    }
    exprIdxInfo->num = CastToFunc(exprIdxInfo->exprs)->array.num;
    exprIdxInfo->resDataTypes = dataTypes;
    ret = SqlFillExprIndexDetail(memCtx, vertexLabel, colList, indexLabel->sortType, exprIdxInfo);
    if (ret != GMERR_OK) {
        goto ERROR;
    }
    indexLabel->exprIdxInfo = exprIdxInfo;
    indexLabel->propeNum = exprCnt;
    indexLabel->properties = NULL;
    return GMERR_OK;

ERROR:
    DbDynMemCtxFree(memCtx, exprIdxInfo);
    DbDynMemCtxFree(memCtx, dataTypes);
    return ret;
}
#else
Status SqlFillExprIndexInfo(
    DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, DbListT *colList, DmVlIndexLabelT *indexLabel)
{
    DB_POINTER4(memCtx, vertexLabel, colList, indexLabel);
    // SqlVerifyTableName中已拦截，此处必然走不到
    return GMERR_FEATURE_NOT_SUPPORTED;
}
#endif
/**
 * @brief 填充索引的列信息，列索引需填充properties，表达式索引需填充indexExprs等
 * @param[in] memCtx         内存上下文
 * @param[in] vertexLabel    被创建索引的表信息
 * @param[in] colList        表达式列表
 * @param[out] indexLabel    待创建的索引信息
 * @return GMERR_OK或错误码
 */
Status SqlFillIndexColInfo(
    DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, DbListT *colList, DmVlIndexLabelT *indexLabel)
{
    // 暂不支持指定升降序, 表达式索引
    DB_POINTER4(memCtx, vertexLabel, colList, indexLabel);
    uint32_t propeCount = DbListGetItemCnt(colList);
    indexLabel->propeNum = propeCount;

    DmSortTypeE *sortType = SqlInitSortTypeArray(memCtx, propeCount);
    if (sortType == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to alloc sortTypeArray memory.");
        return GMERR_OUT_OF_MEMORY;
    }

    bool isExprIdx = SqlExprsExistNotColumn(colList);
    if (isExprIdx) {
        indexLabel->sortType = sortType;  // 先把申请的内存空间赋值给indexLabel
        // 直接用cataCache的内存创建，避免后续深拷贝
        Status ret = SqlFillExprIndexInfo(memCtx, vertexLabel, colList, indexLabel);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(memCtx, sortType);
            return ret;
        }
    } else {
        indexLabel->propIds = (uint32_t *)DbDynMemCtxAlloc(memCtx, sizeof(uint32_t) * propeCount);
        if (indexLabel->propIds == NULL) {
            DbDynMemCtxFree(memCtx, sortType);
            SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer cannot alloc memory for index propIds.");
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s(indexLabel->propIds, sizeof(uint32_t) * propeCount, 0, sizeof(uint32_t) * propeCount);
        for (uint32_t i = 0; i < propeCount; ++i) {
            SqlIndexColumnT *indexCol = *(SqlIndexColumnT **)DbListItem(colList, i);
            SqlExprColumnT *column = (SqlExprColumnT *)(void *)indexCol->columnExpr->op;
            // indexLabel->propIds指定这个索引用了哪些属性
            indexLabel->propIds[i] = column->location.propId;
            Status ret = SqlGetIndexDistType(indexCol->distanceFuncName, &indexLabel->indexDistType);
            if (ret != GMERR_OK) {
                return ret;
            }
            sortType[i] = (DmSortTypeE)indexCol->sortType;
        }
        // indexLabel->properties 直接引用 vertexLabel->metaVertexLabel->schema->properties
        indexLabel->properties = vertexLabel->metaVertexLabel->schema->properties;
        indexLabel->sortType = sortType;
        indexLabel->isExprIdx = false;
        SqlSetFixedPropeNum(indexLabel);
    }
    return GMERR_OK;
}

Status SqlCreateIndexLabel(SessionT *session, DbMemCtxT *memCtx, SqlCreateIndexStmtT *idxStmt,
    DmVertexLabelT *vertexLabel, DmVlIndexLabelT **indexLabel)
{
    DB_POINTER5(session, memCtx, vertexLabel, idxStmt, indexLabel);
    DmVlIndexLabelT *index = (DmVlIndexLabelT *)DbDynMemCtxAlloc(memCtx, sizeof(DmVlIndexLabelT));
    if (index == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(index, sizeof(DmVlIndexLabelT), 0, sizeof(DmVlIndexLabelT));
    Status ret = SqlFillIndexColInfo(memCtx, vertexLabel, idxStmt->indexColList, index);
    if (ret != GMERR_OK) {
        return ret;
    }
    char *idxName = idxStmt->indexInfo->indexName;
    index->indexNameLen = (uint32_t)(DM_STR_LEN(idxName));
    index->indexName = (char *)DbDynMemCtxAlloc(memCtx, index->indexNameLen);
    if (index->indexName == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memcpy_s(index->indexName, index->indexNameLen, idxName, index->indexNameLen);
    index->idxLabelBase.indexConstraint = idxStmt->isUnique ? UNIQUE : NON_UNIQUE;
    index->idxLabelBase.isLabelLatchMode = DmIsLabelLatchMode(vertexLabel->commonInfo->heapInfo.ccType);
    index->idxLabelBase.srcLabelId = vertexLabel->metaCommon.metaId;
    index->idxLabelBase.indexType = idxStmt->indexType;
    // 向量索引不允许插入NULL值
    index->isNullable = DmIndexLabelIsVectorIndex(index->idxLabelBase.indexType) ? false : true;
    index->nullInfoBytes = DmGetNullInfoBytes(index->propeNum);
    index->maxKeyLen = DmGetMaxKeyBufLen4QE(index);
    index->idxLabelBase.srcLabelNameLen = DM_STR_LEN(vertexLabel->metaCommon.metaName);
    index->idxLabelBase.srcLabelName = vertexLabel->metaCommon.metaName;
    index->posOfPqAddr = DB_INVALID_ID32;
    index->isLpasPq = idxStmt->isPq;

    *indexLabel = index;
    return GMERR_OK;
}

// 表的恢复见SqlFillVertexLabelByRebuildInfo函数
static void SqlFillIndexLabelByRebuildInfo(DmVlIndexLabelT *indexLabel, DmRebuildInfoT *rebInfo)
{
    if (!rebInfo->rebAssist.isRebuild) {
        return;
    }
    indexLabel->idxLabelBase.indexId = rebInfo->rebData.nameId;
    indexLabel->idxLabelBase.shmAddr = *(ShmemPtrT *)(void *)&rebInfo->rebData.pageId;
}

static Status SqlFillAnnIndexParameters(DbListT *withParameterList, DbMemCtxT *memCtx, DbListT *annIndexParameters)
{
    if (withParameterList == NULL) {
        return GMERR_OK;
    }
    uint32_t count = DbListGetItemCnt(withParameterList);
    for (uint32_t i = 0; i < count; ++i) {
        SqlAnnParameterPairT *pair = *(SqlAnnParameterPairT **)DbListItem(withParameterList, i);
        // 生命周期：和GmeSqlStmtT同级别
        // 释放方式：异常分支在 SqlCreateIndexLabelIrStmt 内释放，否则在GmeSqlFinalize中释放
        // 兜底清空措施：GmeSqlFinalize中释放
        AnnIndexParameterT *item = DbNewListItem(annIndexParameters);
        if (item == NULL) {
            SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Unable to get new ann index parameter item.");
            return GMERR_OUT_OF_MEMORY;
        }
        item->parameterType = pair->parameterType;
        SqlExprConstT *constExpr = (SqlExprConstT *)(void *)pair->constValueExpr->op;
        item->parameterValue = constExpr->arg;
    }
    return GMERR_OK;
}

Status SqlCreateIndexLabelIrStmt(
    DbMemCtxT *memCtx, SqlCreateIndexStmtT *idxStmt, DmVlIndexLabelT *indexLabel, SqlIrStmtT *irStmt)
{
    DB_POINTER3(memCtx, indexLabel, irStmt);
    // 生命周期：和GmeSqlStmtT同级别
    // 释放方式：异常分支在本函数内释放，否则在GmeSqlFinalize中释放
    // 兜底清空措施：GmeSqlFinalize中释放
    CreateIndexStmtT *node = (CreateIndexStmtT *)DbDynMemCtxAlloc(memCtx, sizeof(CreateIndexStmtT));
    if (node == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer alloc index stmt memory for index label is not successfully.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(node, sizeof(CreateIndexStmtT), 0, sizeof(CreateIndexStmtT));
    DbCreateListWithExtendSize(
        &node->annIndexParameters, sizeof(AnnIndexParameterT), ANN_INDEX_PARAMETER_EXTEND_SIZE, memCtx);
    Status ret = SqlFillAnnIndexParameters(idxStmt->withParameterList, memCtx, &node->annIndexParameters);
    if (ret != GMERR_OK) {
        DbDestroyList(&node->annIndexParameters);
        DbDynMemCtxFree(memCtx, node);
        return ret;
    }
    SqlFillIndexLabelByRebuildInfo(indexLabel, &irStmt->rebInfo);
    node->indexLabel = indexLabel;
    node->node.tag = T_CREATE_INDEX_STMT;
    irStmt->utilityStmt = (NodeT *)node;
    irStmt->irPlan = NULL;
    return GMERR_OK;
}

static void SqlCreateIndexGenPosOfPqAddr(DmVertexLabelT *vertexLabel, DmVlIndexLabelT *indexLabel)
{
    DB_POINTER2(vertexLabel, indexLabel);
    if (!DmIndexLabelIsPqIndex(indexLabel)) {
        return;
    }
    if (vertexLabel->rebInfo.rebAssist.isRebuild) {
        // 恢复流程下，从系统表字段STB_SQL_PROP_ID获取值
        indexLabel->posOfPqAddr = vertexLabel->rebInfo.rebData.propId;
        return;
    }

    // 对应的位置为true表示该位置已经被PQ索引使用了，新建的需要从为false的位置去选
    bool flags[DM_MAX_KEY_COUNT] = {0};
    for (uint32_t i = 0; i < vertexLabel->metaVertexLabel->secIndexNum; i++) {
        DmVlIndexLabelT *index = &vertexLabel->metaVertexLabel->secIndexes[i];
        if (DmIndexLabelIsPqIndex(index)) {
            if (index->posOfPqAddr != DB_INVALID_ID32 && index->posOfPqAddr < DM_MAX_KEY_COUNT &&
                index->posOfPqAddr < vertexLabel->vertexLabelConstraint->pqIndexNum) {
                flags[index->posOfPqAddr] = true;
                break;
            }
        }
    }

    uint32_t pos = 0;
    for (uint32_t i = 0; i < vertexLabel->vertexLabelConstraint->pqIndexNum && i < DM_MAX_KEY_COUNT; i++) {
        if (flags[i] == false) {
            break;
        }
        pos++;
    }
    indexLabel->posOfPqAddr = pos;
}

Status SqlBuildIrStmtForCreateIdx(
    SessionT *session, DbMemCtxT *memCtx, SqlCreateIndexStmtT *idxStmt, DmVertexLabelT *vertexLabel, SqlIrStmtT *irStmt)
{
    DB_POINTER5(session, memCtx, idxStmt, vertexLabel, irStmt);
    DmVlIndexLabelT *indexLabel = NULL;
    vertexLabel->rebInfo = irStmt->rebInfo;
    Status ret = SqlCreateIndexLabel(session, memCtx, idxStmt, vertexLabel, &indexLabel);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Sql analyzer create index label not successfully.");
        return ret;
    }
    SqlCreateIndexGenPosOfPqAddr(vertexLabel, indexLabel);
    return SqlCreateIndexLabelIrStmt(memCtx, idxStmt, indexLabel, irStmt);
}

Status SqlAnalyzeCreateIndex(SessionT *session, NodeT *parsedStmt, SqlIrStmtT *irStmt)
{
    DB_POINTER3(session, parsedStmt, irStmt);
    DmVertexLabelT *vertexLabel = NULL;
    SqlCreateIndexStmtT *createIndexStmt = (SqlCreateIndexStmtT *)(void *)parsedStmt;
    Status ret = SqlVerifyCreateIndex(session, irStmt->memCtx, createIndexStmt, &vertexLabel);
    if (ret != GMERR_OK) {
        if (vertexLabel) {
            (void)CataReleaseVertexLabel(vertexLabel);
        }
        return SqlCheckIfNotExists4Create(createIndexStmt->ifNotExists, ret);
    }

    ret = SqlBuildIrStmtForCreateIdx(session, irStmt->memCtx, createIndexStmt, vertexLabel, irStmt);
    (void)CataReleaseVertexLabel(vertexLabel);
    return ret;
}
