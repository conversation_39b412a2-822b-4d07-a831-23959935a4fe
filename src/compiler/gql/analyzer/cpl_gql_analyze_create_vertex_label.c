/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementation for Gql analyzer about CREATE VERTEXLABEL
 * Author: GQL
 * Create: 2022-10-08
 */

#include "adpt_string.h"
#include "adpt_types.h"
#include "dm_data_basic.h"
#include "dm_data_define.h"
#include "dm_data_index.h"
#include "dm_meta_prop_strudefs.h"
#include "cpl_public_parser.h"
#include "cpl_gql_analyzer_utils.h"
#include "cpl_gql_analyze_create_vertex_label.h"

static Status VerifyVertexLabelIndexType(ParseCreateVertexLabelStmtT *stmt)
{
    for (uint32_t i = 0; i < stmt->indexes.count; i++) {
        IndexT *index = (IndexT *)DbListItem(&stmt->indexes, i);
        if (!IsIndexTypeOrNot(index->type, &index->standardType)) {
            DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED,
                "Unable to analyze path, index %s type hasn't been supported in vertex label %s.", index->name,
                stmt->label);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
    }
    return GMERR_OK;
}

static Status VerifyVertexLabelIndexName(ParseCreateVertexLabelStmtT *stmt, DbMemCtxT *memCtx)
{
    // 表内index name不能重复
    DbOamapT indexNameMap;  // key: index name, value: count
    Status ret = DbOamapInit(&indexNameMap, 0, DbOamapStringCompare, memCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (stmt->primaryIndex != NULL) {
        ret = CheckLengthOfName(stmt->primaryIndex->name);
        if (ret != GMERR_OK) {
            return ret;
        }
        uint32_t indexNameHash = DbStrToHash32(stmt->primaryIndex->name);
        ret = DbOamapIncreaseCnt(&indexNameMap, indexNameHash, stmt->primaryIndex->name, NULL, memCtx);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    for (uint32_t i = 0; i < stmt->indexes.count; i++) {
        IndexT *index = (IndexT *)DbListItem(&stmt->indexes, i);
        ret = CheckLengthOfName(index->name);
        if (ret != GMERR_OK) {
            return ret;
        }
        uint32_t indexNameHash = DbStrToHash32(index->name);
        ret = DbOamapFindAndCheck(&indexNameMap, indexNameHash, index->name, NULL, memCtx);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_INVALID_TABLE_DEFINITION,
                "Unable to analyze path, index name %s should be unique in one vertex label.", index->name);
            DbOamapDestroy(&indexNameMap);
            return GMERR_INVALID_TABLE_DEFINITION;
        }
    }
    DbOamapDestroy(&indexNameMap);
    return GMERR_OK;
}

static uint32_t GetPropSize(PropertyT *property)
{
    switch (property->standardType) {
        case DB_DATATYPE_INT8:
        case DB_DATATYPE_UINT8:
            return sizeof(int8_t);
        case DB_DATATYPE_INT16:
        case DB_DATATYPE_UINT16:
            return sizeof(int16_t);
        case DB_DATATYPE_INT32:
        case DB_DATATYPE_UINT32:
            return sizeof(int32_t);
        case DB_DATATYPE_INT64:
        case DB_DATATYPE_UINT64:
            return sizeof(int64_t);
        default:  // DB_DATATYPE_FIXED
            return property->varSize;
    }
}

static Status VerifyPrimaryIndexProperties(
    ParseCreateVertexLabelStmtT *stmt, DbMemCtxT *memCtx, DbOamapT *propertyMapCnt)
{
    IndexT *primaryIndex = stmt->primaryIndex;
    uint32_t propertiesCnt = DbListGetItemCnt(&primaryIndex->properties);
    if (propertiesCnt > DM_MAX_KEY_PROPE_NUM) {
        DB_LOG_ERROR(GMERR_INVALID_TABLE_DEFINITION,
            "Gql analyzer: index associated properties shouldn't exceed %u, index: %s, vertex label: "
            "%s.",
            DM_MAX_KEY_PROPE_NUM, primaryIndex->name, stmt->label);
        return GMERR_INVALID_TABLE_DEFINITION;
    }
    uint32_t keySize = 0;
    for (uint32_t i = 0; i < propertiesCnt; i++) {
        PropertyT *property1 = (PropertyT *)DbListItem(&primaryIndex->properties, i);
        uint32_t indexNameHash = DbStrToHash32(property1->name);
        PropertyT *originProperty =
            (PropertyT *)DbOamapLookup(propertyMapCnt, indexNameHash, (void *)property1->name, NULL);
        // 索引成员必须在属性列表中
        if (originProperty == NULL) {
            DB_LOG_ERROR(GMERR_INVALID_TABLE_DEFINITION,
                "Unable to analyze path, indexProperty name should appear in vertex properties.");
            return GMERR_INVALID_TABLE_DEFINITION;
        }
        for (uint32_t j = i + 1; j < propertiesCnt; j++) {
            PropertyT *property2 = (PropertyT *)DbListItem(&primaryIndex->properties, j);
            if (strcmp(property1->name, property2->name) == 0) {
                DB_LOG_ERROR(GMERR_INVALID_TABLE_DEFINITION,
                    "Unable to analyze path, indexProperty name should be unique in one index.");
                return GMERR_INVALID_TABLE_DEFINITION;
            }
        }
        keySize += GetPropSize(originProperty);
    }

    if (keySize > KEY_SIZE_MAX) {
        DB_LOG_ERROR(GMERR_INVALID_TABLE_DEFINITION,
            "Unable to analyze gql, the primary key size in vertex label %s exceeds the upper limit, which is "
            "%u.",
            stmt->label, KEY_SIZE_MAX);
        return GMERR_INVALID_TABLE_DEFINITION;
    }

    return GMERR_OK;
}

static Status VerifySecondIndexPropertiesInner(ParseCreateVertexLabelStmtT *stmt, DbMemCtxT *memCtx,
    DbOamapT *propertyMapCnt, DbOamapT *indexPropertyNameMap, IndexT *index)
{
    Status ret = GMERR_OK;
    uint32_t propertiesCnt = DbListGetItemCnt(&index->properties);
    if (propertiesCnt > DM_MAX_KEY_PROPE_NUM) {
        DB_LOG_ERROR(GMERR_INVALID_TABLE_DEFINITION,
            "Gql analyzer: index associated properties shouldn't exceed %u, index: %s, vertex "
            "label: %s.",
            DM_MAX_KEY_PROPE_NUM, index->name, stmt->label);
        return GMERR_INVALID_TABLE_DEFINITION;
    }
    uint32_t keySize = 0;
    for (uint32_t j = 0; j < propertiesCnt; j++) {
        PropertyT *indexProperty = (PropertyT *)DbListItem(&index->properties, j);
        uint32_t indexNameHash = DbStrToHash32(indexProperty->name);
        ret = DbOamapFindAndCheck(indexPropertyNameMap, indexNameHash, indexProperty->name, NULL, memCtx);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_INVALID_TABLE_DEFINITION,
                "Unable to analyze path, indexProperty name should be unique in one index.");
            return GMERR_INVALID_TABLE_DEFINITION;
        }
        PropertyT *originProperty =
            (PropertyT *)DbOamapLookup(propertyMapCnt, indexNameHash, (void *)indexProperty->name, NULL);
        // 索引成员必须在属性列表中
        if (originProperty == NULL) {
            DB_LOG_ERROR(GMERR_INVALID_TABLE_DEFINITION,
                "Unable to analyze path, indexProperty name should appear in vertex properties.");
            return GMERR_INVALID_TABLE_DEFINITION;
        }
        // 方便之后填充vertex
        indexProperty->type = originProperty->type;
        keySize += GetPropSize(originProperty);
    }
    if (keySize > KEY_SIZE_MAX) {
        DB_LOG_ERROR(GMERR_INVALID_TABLE_DEFINITION,
            "Unable to analyze gql, the key: %s size in vertex label %s exceeds the upper limit, which is "
            "%u.",
            index->name, stmt->label, KEY_SIZE_MAX);
        return GMERR_INVALID_TABLE_DEFINITION;
    }
    return ret;
}

static Status VerifySecondIndexProperties(
    ParseCreateVertexLabelStmtT *stmt, DbMemCtxT *memCtx, DbOamapT *propertyMapCnt)
{
    // 二级索引
    DbOamapT indexPropertyNameMap;  // key: index name, value: count
    Status ret = DbOamapInit(&indexPropertyNameMap, 0, DbOamapStringCompare, memCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = 0; i < stmt->indexes.count; i++) {
        IndexT *index = (IndexT *)DbListItem(&stmt->indexes, i);
        ret = VerifySecondIndexPropertiesInner(stmt, memCtx, propertyMapCnt, &indexPropertyNameMap, index);
        if (ret != GMERR_OK) {
            DbOamapDestroy(&indexPropertyNameMap);
            return ret;
        }
        DbOamapClear(&indexPropertyNameMap);
    }
    DbOamapDestroy(&indexPropertyNameMap);
    return ret;
}

static Status VerifyVertexLabelIndexProperties(
    ParseCreateVertexLabelStmtT *stmt, DbMemCtxT *memCtx, DbOamapT propertyMapCnt)
{
    // 主键索引
    if (stmt->primaryIndex != NULL) {
        Status ret = VerifyPrimaryIndexProperties(stmt, memCtx, &propertyMapCnt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // 二级索引
    return VerifySecondIndexProperties(stmt, memCtx, &propertyMapCnt);
}

Status PathInitPropSchemaArray(DbMemCtxT *memCtx, uint32_t arrayLen, DmPropertySchemaT **properties)
{
    size_t propArraySize = arrayLen * sizeof(DmPropertySchemaT);
    *properties =
        (DmPropertySchemaT *)DbDynMemCtxAlloc(memCtx, propArraySize);  // 在path service PathServiceEntry中统一释放
    if (*properties == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Unable to analyze path, malloc properties meets mistake.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(*properties, propArraySize, 0, propArraySize);
    return GMERR_OK;
}

// 填充字段（表字段/索引字段/函数字段等），将fields中的元素（偏移offset个元素）依次填充到properties数组中
void PathFillPropSchema(DbListT *indexProperties, uint32_t propNum, DmPropertySchemaT *properties, uint32_t *propIds,
    DmVertexLabelT *vertexLabel)
{
    uint32_t vertexLabelPropertyCnt = vertexLabel->metaVertexLabel->schema->propeNum;
    for (uint32_t i = 0; i < propNum; i++) {
        PropertyT *property = (PropertyT *)DbListItem(indexProperties, i);
        for (uint32_t j = 0; j < vertexLabelPropertyCnt; j++) {
            if (DbStrCmp(vertexLabel->metaVertexLabel->schema->properties[j].name, property->name, false) == 0) {
                properties[i] = vertexLabel->metaVertexLabel->schema->properties[j];
                propIds[i] = j;
                break;
            }
        }
        properties[i].isValid = true;
    }
}

Status PathInitPropPropIds(DbMemCtxT *memCtx, uint32_t arrayLen, uint32_t **propIds)
{
    size_t propArraySize = arrayLen * sizeof(uint32_t);
    *propIds = (uint32_t *)DbDynMemCtxAlloc(memCtx, propArraySize);  // 在path service PathServiceEntry中统一释放
    if (*propIds == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Unable to analyze path, malloc properties meets mistake.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(*propIds, propArraySize, 0, propArraySize);
    return GMERR_OK;
}

// rule中填充vertexLabel主键索引
Status PathAddPkIndex(DmVertexLabelT *vertexLabel, IndexT *index, DbMemCtxT *memCtx)
{
    // 为索引字段数组分配内存，并初始化为全零
    DmPropertySchemaT *properties;
    Status ret = PathInitPropSchemaArray(memCtx, DbListGetItemCnt(&index->properties), &properties);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t *propIds;
    ret = PathInitPropPropIds(memCtx, DbListGetItemCnt(&index->properties), &propIds);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmVlIndexLabelT *pkIndex =
        DbDynMemCtxAlloc(memCtx, sizeof(DmVlIndexLabelT));  // 在path service PathServiceEntry中统一释放
    if (pkIndex == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Unable to analyze path, malloc pkIndex meets mistake.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(pkIndex, sizeof(DmVlIndexLabelT), 0, sizeof(DmVlIndexLabelT));
    vertexLabel->metaVertexLabel->pkIndex = pkIndex;

    /* 填充主键索引property schema */
    pkIndex->propeNum = DbListGetItemCnt(&index->properties);
    PathFillPropSchema(&index->properties, pkIndex->propeNum, properties, propIds, vertexLabel);
    pkIndex->properties = vertexLabel->metaVertexLabel->schema->properties;
    pkIndex->propIds = propIds;

    // 填充主键索引
    pkIndex->indexName = (char *)index->name;
    pkIndex->indexNameLen = (uint32_t)(strlen(index->name) + 1);
    pkIndex->indexConstraint = PRIMARY;
    pkIndex->indexType = HAC_HASH_INDEX;
    pkIndex->isNullable = false;
    pkIndex->isLabelLatchMode = false;
    pkIndex->nullInfoBytes = DmGetNullInfoBytes(pkIndex->propeNum);
    pkIndex->fixedPropeNum = (uint16_t)pkIndex->propeNum;
    pkIndex->idxLabelBase.indexLabelCfg.initHashCapacity = index->initHashCapacity;
    pkIndex->idxLabelBase.srcLabelId = vertexLabel->metaCommon.metaId;
    pkIndex->sourceLabelName = vertexLabel->metaCommon.metaName;
    pkIndex->sourceLabelNameLen = DM_STR_LEN(vertexLabel->metaCommon.metaName);
    pkIndex->maxKeyLen = DmGetMaxKeyBufLen(pkIndex);  // 注意此属性需在properties被赋值后才填充

    return GMERR_OK;
}

static Status PathAddSecIndex(DmVertexLabelT *vertexLabel, IndexT *index, DbMemCtxT *memCtx, uint32_t idx)
{
    (void)memset_s(&vertexLabel->metaVertexLabel->secIndexes[idx], sizeof(DmVlIndexLabelT), 0, sizeof(DmVlIndexLabelT));

    // 为索引字段数组分配内存，并初始化为全零
    DmPropertySchemaT *properties;
    Status ret = PathInitPropSchemaArray(memCtx, index->properties.count, &properties);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t *propIds;
    ret = PathInitPropPropIds(memCtx, DbListGetItemCnt(&index->properties), &propIds);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 填充二级索引的property schema
    PathFillPropSchema(&index->properties, index->properties.count, properties, propIds, vertexLabel);
    vertexLabel->metaVertexLabel->secIndexes[idx].properties = vertexLabel->metaVertexLabel->schema->properties;
    vertexLabel->metaVertexLabel->secIndexes[idx].propIds = propIds;

    vertexLabel->metaVertexLabel->secIndexes[idx].indexName = (char *)index->name;
    vertexLabel->metaVertexLabel->secIndexes[idx].indexNameLen = (uint32_t)(strlen(index->name) + 1);
    vertexLabel->metaVertexLabel->secIndexes[idx].propeNum = index->properties.count;
    vertexLabel->metaVertexLabel->secIndexes[idx].isLabelLatchMode = false;
    vertexLabel->metaVertexLabel->secIndexes[idx].isNullable = false;
    vertexLabel->metaVertexLabel->secIndexes[idx].indexType = index->standardType;
    vertexLabel->metaVertexLabel->secIndexes[idx].indexConstraint = index->constraint;
    vertexLabel->metaVertexLabel->secIndexes[idx].fixedPropeNum = (uint16_t)index->properties.count;
    vertexLabel->metaVertexLabel->secIndexes[idx].nullInfoBytes =
        DmGetNullInfoBytes(vertexLabel->metaVertexLabel->secIndexes[idx].propeNum);
    vertexLabel->metaVertexLabel->secIndexes[idx].idxLabelBase.indexLabelCfg.initHashCapacity = index->initHashCapacity;
    vertexLabel->metaVertexLabel->secIndexes[idx].idxLabelBase.srcLabelId = vertexLabel->metaCommon.metaId;
    vertexLabel->metaVertexLabel->secIndexes[idx].idxLabelBase.srcLabelName = vertexLabel->metaCommon.metaName;
    vertexLabel->metaVertexLabel->secIndexes[idx].idxLabelBase.srcLabelNameLen =
        DM_STR_LEN(vertexLabel->metaCommon.metaName);
    vertexLabel->metaVertexLabel->secIndexes[idx].maxKeyLen =
        DmGetMaxKeyBufLen(&vertexLabel->metaVertexLabel->secIndexes[idx]);

    return GMERR_OK;
}

static Status FulFillTableOptions(DbMemCtxT *memCtx, ParseCreateVertexLabelStmtT *stmt, DmVertexLabelT *vertexLabel)
{
    vertexLabel->pathAttributes = (ComplexPathAttributeT *)DbDynMemCtxAlloc(
        memCtx, sizeof(ComplexPathAttributeT));  // 在path service PathServiceEntry中统一释放
    if (vertexLabel->pathAttributes == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Unable to analyze path, malloc path attributes meets mistake.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(vertexLabel->pathAttributes, sizeof(ComplexPathAttributeT), 0, sizeof(ComplexPathAttributeT));
    vertexLabel->pathAttributes->isTrigsAllTrue = true;
    vertexLabel->pathAttributes->isChanged = false;
    vertexLabel->pathAttributes->vertexLabelMergeViewType = stmt->mergeViewType;
    return GMERR_OK;
}

static Status FulFillIndexToVertex(DbMemCtxT *memCtx, ParseCreateVertexLabelStmtT *stmt, DmVertexLabelT *vertexLabel)
{
    /* 主键索引 */
    Status ret = GMERR_OK;
    if (stmt->primaryIndex != NULL) {
        ret = PathAddPkIndex(vertexLabel, stmt->primaryIndex, memCtx);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    /* 二级索引 */
    vertexLabel->metaVertexLabel->secIndexNum = DbListGetItemCnt(&stmt->indexes);
    vertexLabel->metaVertexLabel->secIndexes = (DmVlIndexLabelT *)DbDynMemCtxAlloc(
        memCtx, sizeof(DmVlIndexLabelT) *
                    vertexLabel->metaVertexLabel->secIndexNum);  // 在path service PathServiceEntry中统一释放
    if (vertexLabel->metaVertexLabel->secIndexes == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc second index meets mistake.");
        return GMERR_OUT_OF_MEMORY;
    }

    for (uint32_t i = 0; i < vertexLabel->metaVertexLabel->secIndexNum; i++) {
        IndexT *index = (IndexT *)DbListItem(&stmt->indexes, i);
        ret = PathAddSecIndex(vertexLabel, index, memCtx, i);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    vertexLabel->metaVertexLabel->hcIndexNum = 0;
    return GMERR_OK;
}

static uint32_t GetUniqueConstraint(DmIndexTypeE type)
{
    switch (type) {
        case HASH_INDEX:
        case HAC_HASH_INDEX:
            return UNIQUE;
        default:
            return NON_UNIQUE;
    }
}

static Status CheckIndexUniqueness(IndexT *index)
{
    uint32_t constraint = GetUniqueConstraint(index->standardType);
    if ((uint32_t)index->constraint == constraint) {
        return GMERR_OK;
    } else {
        DB_LOG_ERROR(GMERR_INVALID_TABLE_DEFINITION,
            "Unable to analyze path, set uniqueness of index %s meets mistake.", index->name);
        return GMERR_INVALID_TABLE_DEFINITION;
    }
}

static Status VerifyVertexLabelIndexHashCap(ParseCreateVertexLabelStmtT *stmt)
{
    if (stmt->primaryIndex != NULL) {
        if (stmt->primaryIndex->initHashCapacity > HASH_CAP_MAX) {
            DB_LOG_ERROR(GMERR_PROGRAM_LIMIT_EXCEEDED,
                "Unable to analyze path, index %s hash capacity exceeds limit %d in vertex label %s.",
                stmt->primaryIndex->name, HASH_CAP_MAX, stmt->label);
            return GMERR_PROGRAM_LIMIT_EXCEEDED;
        }
    }
    for (uint32_t i = 0; i < stmt->indexes.count; i++) {
        IndexT *index = (IndexT *)DbListItem(&stmt->indexes, i);
        if (index->initHashCapacity > HASH_CAP_MAX) {
            DB_LOG_ERROR(GMERR_PROGRAM_LIMIT_EXCEEDED,
                "Unable to analyze path, index %s hash capacity exceeds limit %d in vertex label %s.", index->name,
                HASH_CAP_MAX, stmt->label);
            return GMERR_PROGRAM_LIMIT_EXCEEDED;
        }
    }
    return GMERR_OK;
}

static Status VerifyVertexLabelIndex(DbOamapT propertyMapCnt, ParseCreateVertexLabelStmtT *stmt, DbMemCtxT *memCtx)
{
    // 校验二级索引个数
    uint32_t sIndexCnt = DbListGetItemCnt(&stmt->indexes);
    if (sIndexCnt > SECOND_INDEX_MAX) {
        DB_LOG_ERROR(GMERR_INVALID_TABLE_DEFINITION, "Unable to analyze path, The number of second index exceeds %d.",
            SECOND_INDEX_MAX);
        return GMERR_INVALID_TABLE_DEFINITION;
    }

    // 校验索引命名不能重复
    Status ret = VerifyVertexLabelIndexName(stmt, memCtx);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = VerifyVertexLabelIndexType(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = VerifyVertexLabelIndexHashCap(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 校验二级索引类型于其unique约束是否匹配
    for (uint32_t i = 0; i < sIndexCnt; i++) {
        IndexT *index = (IndexT *)DbListItem(&stmt->indexes, i);
        ret = CheckIndexUniqueness(index);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return VerifyVertexLabelIndexProperties(stmt, memCtx, propertyMapCnt);
}

static Status VerifyTupleSize(ParseCreateVertexLabelStmtT *stmt, uint32_t propertiesCnt)
{
    uint32_t tupleSize = 0;
    for (uint32_t i = 0; i < propertiesCnt; i++) {
        PropertyT *property = (PropertyT *)DbListItem(&stmt->properties, i);
        tupleSize += GetPropSize(property);
    }
    if (tupleSize > TUPLE_SIZE_MAX) {
        DB_LOG_ERROR(GMERR_INVALID_TABLE_DEFINITION,
            "Unable to analyze gql, the tuple size in vertex label %s exceeds the upper limit, which is "
            "%u.",
            stmt->label, TUPLE_SIZE_MAX);
        return GMERR_INVALID_TABLE_DEFINITION;
    }
    return GMERR_OK;
}

static Status VerifyCreateVertexLabelStmt(ParseCreateVertexLabelStmtT *stmt, IrPlannedStmtT *outputStmt)
{
    if (stmt->properties.count > DM_MAX_FIELD_PROPE_NUM) {
        DB_LOG_ERROR(GMERR_PROGRAM_LIMIT_EXCEEDED,
            "Unable to analyze gql, the number of property in vertex label %s exceeds the upper limit, which is "
            "%u.",
            stmt->label, DM_MAX_FIELD_PROPE_NUM);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }

    Status ret = CheckLengthOfName(stmt->label);
    if (ret != GMERR_OK) {
        return ret;
    }

    /* verify property */
    uint32_t propertiesCnt = stmt->properties.count;
    DbOamapT propertyMapCnt;  // key: property name, value: count
    ret = DbOamapInit(&propertyMapCnt, 0, DbOamapStringCompare, outputStmt->memCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = 0; i < propertiesCnt; i++) {
        PropertyT *property = (PropertyT *)DbListItem(&stmt->properties, i);
        if (!IsTypeOrNot(property)) {
            DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED,
                "Unable to analyze path, field %s type hasn't been supported in vertex label %s.", property->name,
                stmt->label);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
        uint32_t propertyNameHash = DbStrToHash32(property->name);
        ret = DbOamapInsert(&propertyMapCnt, propertyNameHash, property->name, property, NULL);
        if (ret != GMERR_OK) {
            DbOamapDestroy(&propertyMapCnt);
            DB_LOG_ERROR(
                GMERR_DUPLICATE_COLUMN, "Unable to analyze path, duplicate field in vertex label %s.", stmt->label);
            return GMERR_DUPLICATE_COLUMN;
        }
    }

    ret = VerifyTupleSize(stmt, propertiesCnt);
    if (ret != GMERR_OK) {
        DbOamapDestroy(&propertyMapCnt);
        return ret;
    }

    /* verify index will be added later */
    ret = VerifyVertexLabelIndex(propertyMapCnt, stmt, outputStmt->memCtx);
    if (ret != GMERR_OK) {
        DbOamapDestroy(&propertyMapCnt);
        return ret;
    }

    DbOamapDestroy(&propertyMapCnt);
    return GMERR_OK;
}

static Status InitVertexLabelSchemaProperties(DmVertexLabelT *vertexLabel)
{
    DmPropertySchemaT *properties;
    size_t propArraySize = vertexLabel->metaVertexLabel->schema->propeNum * sizeof(DmPropertySchemaT);
    properties = (DmPropertySchemaT *)DbDynMemCtxAlloc(
        vertexLabel->memCtx, propArraySize);  // 在path service PathServiceEntry中统一释放
    if (properties == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Unable to analyze path, malloc properties meets mistake.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(properties, propArraySize, 0, propArraySize);
    vertexLabel->metaVertexLabel->schema->properties = properties;
    vertexLabel->metaVertexLabel->schema->isFlat = true;
    vertexLabel->metaVertexLabel->vertexLabelType = VERTEX_TYPE_GQL;
    return GMERR_OK;
}

static Status FulfillVertexLabelSchemaProperties(
    DbMemCtxT *memCtx, ParseCreateVertexLabelStmtT *stmt, DmSchemaT *schema)
{
    uint32_t propertiesCnt = stmt->properties.count;
    schema->propeNum = propertiesCnt;
    PropertyT *stmtProperties = (PropertyT *)stmt->properties.items;
    DmPropertySchemaT *properties = schema->properties;
    for (uint32_t i = 0; i < propertiesCnt; i++) {
        (void)memset_s(&properties[i], sizeof(DmPropertySchemaT), 0, sizeof(DmPropertySchemaT));
        PropertyT property = stmtProperties[i];
        Status ret = CheckLengthOfName(property.name);
        if (ret != GMERR_OK) {
            return ret;
        }
        properties[i].nameLen = (uint16_t)strnlen(property.name, NAME_LEN_MAX) + 1;
        properties[i].name = property.name;
        properties[i].propeId = i;  // begin with 0
        properties[i].isValid = true;
        // Only DB_DATATYPE_STRING is not a fixed-length attribute now
        properties[i].isFixed = (property.standardType != DB_DATATYPE_STRING);
        properties[i].dataType = property.standardType;
        if (properties[i].dataType == DB_DATATYPE_FIXED) {
            if (property.varSize > FIXED_SIZE_MAX) {
                DB_LOG_ERROR(GMERR_PROGRAM_LIMIT_EXCEEDED,
                    "Gql analyzer: size of DB_DATATYPE_FIXED can't exceed %u, property name is:%s, "
                    "vertex label name:%s.",
                    FIXED_SIZE_MAX, property.name, stmt->label);
                return GMERR_PROGRAM_LIMIT_EXCEEDED;
            }
            properties[i].size = (uint32_t)property.varSize;
        } else if (properties[i].dataType == DB_DATATYPE_STRING) {
            properties[i].size = PROP_LEN_MAX;  // Maximum length of a variable-length type
        } else {
            properties[i].size = DmGetBasicDataTypeLength(property.standardType);
        }
    }
    return GMERR_OK;
}

static Status PathFillVertexLabelMetaCommon(
    SessionT *session, DbMemCtxT *memCtx, ParseCreateVertexLabelStmtT *stmt, DmVertexLabelT *vertexLabel)
{
    vertexLabel->metaCommon.metaName = stmt->label;
    vertexLabel->metaCommon.dbId = session->dbId;
    vertexLabel->metaCommon.namespaceId = session->namespaceId;
    return CataGetNspDefaultTspId(
        session->namespaceId, &vertexLabel->metaCommon.tablespaceId, DbGetInstanceByMemCtx(memCtx));
}

static Status PathFillVertexLabelCommonInfo(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel)
{
    /* 为commonInfo分配内存，并初始化为全零，就是多版本情况下，一个id可以对应多个vertexlabel，必须初始化 */
    VertexLabelCommonInfoT *commonInfo =
        DbDynMemCtxAlloc(memCtx, sizeof(VertexLabelCommonInfoT));  // 在path service PathServiceEntry中统一释放
    if (commonInfo == NULL) {
        DB_LOG_ERROR(
            GMERR_MEMORY_OPERATE_FAILED, "Unable to analyze path, malloc vertexLabelCommonInfo meets mistake.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(commonInfo, sizeof(VertexLabelCommonInfoT), 0, sizeof(VertexLabelCommonInfoT));
    commonInfo->heapInfo.maxVertexNum = DB_MAX_UINT64;
    commonInfo->heapInfo.maxVertexNumCheck = false;
    commonInfo->heapInfo.ccType = CONCURRENCY_CONTROL_NORMAL;
    commonInfo->statusMergeList = DB_INVALID_SHMPTR;
    DmTrxInfoT trxInfo = {0};
    Status ret = CataGetNspTrxInfoById(vertexLabel->metaCommon.namespaceId, &trxInfo, DbGetInstanceByMemCtx(memCtx));
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Cannot get nsp trxInfo by id (%" PRIu32 ") when create logical table: %s.",
            vertexLabel->metaCommon.namespaceId, vertexLabel->metaCommon.metaName);
        return ret;
    }
    // 用namespace级别进行覆盖
    commonInfo->heapInfo.trxType = trxInfo.trxType;
    commonInfo->heapInfo.isolationLevel = trxInfo.isolationLevel;
    commonInfo->heapInfo.skipRowLockPessimisticRR = true;
    vertexLabel->commonInfo = commonInfo;

    return GMERR_OK;
}

static Status InitEmptyVertexLabel(DmVertexLabelT **vertexLabel, IrPlannedStmtT *outputStmt)
{
    Status ret = DmCreateEmptyVertexLabelWithMemCtx(outputStmt->memCtx, vertexLabel);  // 在service统一释放
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to analyze path, create empty vertex label meets mistake.");
        return ret;
    }

    (*vertexLabel)->metaVertexLabel = DbDynMemCtxAlloc((*vertexLabel)->memCtx, sizeof(MetaVertexLabelT));
    if ((*vertexLabel)->metaVertexLabel == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc meta of plan stmt meets mistake.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s((*vertexLabel)->metaVertexLabel, sizeof(MetaVertexLabelT), 0x00, sizeof(MetaVertexLabelT));

    (*vertexLabel)->metaVertexLabel->schema =
        DbDynMemCtxAlloc((*vertexLabel)->memCtx, sizeof(DmSchemaT));  // 在service统一释放
    if ((*vertexLabel)->metaVertexLabel->schema == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc property schema of plan stmt meets mistake.");
        return GMERR_OUT_OF_MEMORY;
    }
    return ret;
}

static Status FulfillCreateVertexLabelStmt(
    SessionT *session, ParseCreateVertexLabelStmtT *stmt, IrPlannedStmtT *outputStmt)
{
    DmVertexLabelT *vertexLabel;
    Status ret = InitEmptyVertexLabel(&vertexLabel, outputStmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    /* fulfill common info: label */
    ret = PathFillVertexLabelMetaCommon(session, vertexLabel->memCtx, stmt, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    /* fulfill schema: properties */
    (void)memset_s(vertexLabel->metaVertexLabel->schema, sizeof(DmSchemaT), 0, sizeof(DmSchemaT));
    vertexLabel->metaVertexLabel->schema->propeNum = stmt->properties.count;

    /* init properties in schema */
    ret = InitVertexLabelSchemaProperties(vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    /* fulfill CommonInfo */
    ret = PathFillVertexLabelCommonInfo(vertexLabel->memCtx, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    /* fulfill properties in schema */
    ret = FulfillVertexLabelSchemaProperties(vertexLabel->memCtx, stmt, vertexLabel->metaVertexLabel->schema);
    if (ret != GMERR_OK) {
        return ret;
    }
    /* verify index will be added later */
    ret = FulFillIndexToVertex(vertexLabel->memCtx, stmt, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FulFillTableOptions(vertexLabel->memCtx, stmt, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    /* fulfill IrPlannedStmtT */
    CreateTableStmtT *node = (CreateTableStmtT *)DbDynMemCtxAlloc(outputStmt->memCtx, sizeof(CreateTableStmtT));
    if (node == NULL) {
        DB_LOG_ERROR(
            GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc create vertex label plan stmt meets mistake.");
        return GMERR_OUT_OF_MEMORY;
    }
    node->vertexLabel = vertexLabel;
    node->node.tag = T_CREATE_TABLE_STMT;
    NodeT *uStmt = (NodeT *)(void *)node;
    outputStmt->utilityStmt = uStmt;
    outputStmt->irPlan = NULL;
    return GMERR_OK;
}

// Analyzer for CreateVertexLabelStmtT
Status AnalyzeCreateVertexLabelStmt(SessionT *session, GqlStatementT *inputStmt, IrPlannedStmtT *outputStmt)
{
    ParseCreateVertexLabelStmtT *stmt = (ParseCreateVertexLabelStmtT *)((void *)inputStmt);
    /* verify */
    Status ret = VerifyCreateVertexLabelStmt(stmt, outputStmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    /* fulfill catalog */
    return FulfillCreateVertexLabelStmt(session, stmt, outputStmt);
}
