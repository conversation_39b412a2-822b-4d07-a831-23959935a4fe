/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Implementation for Path analyzer about DROP PATH
 * Author: GQL
 * Create: 2023-2-16
 */

#include "dm_meta_complex_path.h"
#include "cpl_gql_analyzer_utils.h"
#include "cpl_gql_analyze_create_path.h"

static Status VerifyDropPathStmt(SessionT *session, ParseDropPathStmtT *inputStmt, uint32_t *pathId)
{
    uint32_t number;
    Status ret = CheckLengthOfPathName(inputStmt->pathName, &number);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 校验path是否已创建
    DmComplexPathInfoT *pathInfo = NULL;
    ret = QryGetPathInfoByName(session, inputStmt->pathName, &pathInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_UNDEFINED_OBJECT, "Unable to analyze path, dm path label hasn't been created, path: %s.",
            inputStmt->pathName);
        return GMERR_UNDEFINED_OBJECT;
    }
    *pathId = pathInfo->metaCommon.metaId;
    return CataReleaseComplexPathInfo(pathInfo);
}

static Status FulfillDropPathStmt(
    SessionT *session, ParseDropPathStmtT *inputStmt, IrPlannedStmtT *outputStmt, uint32_t pathId)
{
    DropPathPatternStmtT *node =
        (DropPathPatternStmtT *)DbDynMemCtxAlloc(outputStmt->memCtx, sizeof(DropPathPatternStmtT));
    if (node == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc drop path meets mistake.");
        return GMERR_OUT_OF_MEMORY;
    }
    node->pathPatternId = pathId;
    node->node.tag = T_GQL_DROP_PATH_PATTERN_STMT;
    NodeT *uStmt = (NodeT *)(void *)node;
    outputStmt->utilityStmt = uStmt;
    outputStmt->irPlan = NULL;
    return GMERR_OK;
}

Status AnalyzeDropPathStmt(SessionT *session, GqlStatementT *inputStmt, IrPlannedStmtT *outputStmt)
{
    ParseDropPathStmtT *stmt = (ParseDropPathStmtT *)(void *)inputStmt;
    uint32_t pathId;
    /* verify */
    Status ret = VerifyDropPathStmt(session, stmt, &pathId);
    if (ret != GMERR_OK) {
        return ret;
    }
    /* fulfill DropTableStmtT */
    return FulfillDropPathStmt(session, stmt, outputStmt, pathId);
}
