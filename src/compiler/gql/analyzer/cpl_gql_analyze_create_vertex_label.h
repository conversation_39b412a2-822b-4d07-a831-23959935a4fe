/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Definition for analyze create vertex label of Gql
 * Author: GQL
 * Create: 2022-10-08
 */

#ifndef CPL_GQL_ANALYZE_CREATE_VERTEX_LABEL_H
#define CPL_GQL_ANALYZE_CREATE_VERTEX_LABEL_H

#include "cpl_gql_ast.h"
#include "cpl_gql_compiler.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Gql           analyze create vertex label of Gql
 * @param session       session connection
 * @param inputStmt     ast of create vertex label
 * @param outputStmt    structure store all statement after analyzing
 * @return Status       status code
 */
Status AnalyzeCreateVertexLabelStmt(SessionT *session, GqlStatementT *inputStmt, IrPlannedStmtT *outputStmt);

#ifdef __cplusplus
}
#endif

#endif
