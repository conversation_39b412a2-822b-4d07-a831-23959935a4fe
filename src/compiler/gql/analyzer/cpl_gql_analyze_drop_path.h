/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Path analyzer for DROP PATH
 * Author: GQL
 * Create: 2023-2-16
 */

#ifndef CPL_GQL_ANALYZE_DROP_PATH_H
#define CPL_GQL_ANALYZE_DROP_PATH_H

#include "cpl_gql_ast.h"
#include "cpl_gql_compiler.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Path          analyze drop path
 * @param session       session connection
 * @param inputStmt     ast of drop path
 * @param outputStmt    structure store all statement after analyzing
 * @return Status       status code
 */
Status AnalyzeDropPathStmt(SessionT *session, GqlStatementT *inputStmt, IrPlannedStmtT *outputStmt);

#ifdef __cplusplus
}
#endif

#endif
