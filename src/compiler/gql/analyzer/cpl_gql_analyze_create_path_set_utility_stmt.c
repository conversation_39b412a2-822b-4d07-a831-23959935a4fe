/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementation for path analyzer about CREATE PATH
 * Author: GQL
 * Create: 2022-10-08
 */

#include "adpt_string.h"
#include "dm_cache_basic.h"
#include "dm_meta_complex_path.h"
#include "cpl_gql_analyzer_utils.h"
#include "cpl_gql_analyze_create_path.h"

#define MERGE_PATH_VERTEX_COUNT 2
#define MERGE_PATH_EDGE_COUNT 2

static Maps4MergeReplaceT g_maps4MergeReplace = {0};
static DbOamapT g_label2DmEdge;

static void PathInitList4Vertex(DmPathInfoVertexT *dmVertex, IrPlannedStmtT *outputStmt)
{
    DbCreateListWithExtendSize(dmVertex->inEdgeList, sizeof(DmPathInfoEdgeT *), LIST_EXTEND_SIZE, outputStmt->memCtx);
    DbCreateListWithExtendSize(dmVertex->outEdgeList, sizeof(DmPathInfoEdgeT *), LIST_EXTEND_SIZE, outputStmt->memCtx);
    DbCreateListWithExtendSize(
        dmVertex->inOrGroupList, sizeof(DmPathInfoOrGroupT *), LIST_EXTEND_SIZE, outputStmt->memCtx);
    DbCreateListWithExtendSize(
        dmVertex->outOrGroupList, sizeof(DmPathInfoOrGroupT *), LIST_EXTEND_SIZE, outputStmt->memCtx);
}

// put basic ParsePathInfoVertexT information into DmPathInfoVertexT
static Status SetVertexBasic(ParsePathInfoVertexT *vertex, DmPathInfoVertexT *dmVertex, IrPlannedStmtT *outputStmt)
{
    /* vertexId wont' fulfill here */
    dmVertex->vertexLabelId = vertex->id;
    dmVertex->pathInfoVertexName.len = DM_STR_LEN(vertex->variableName);
    dmVertex->pathInfoVertexName.str = vertex->variableName;
    // 在path service PathServiceEntry中统一释放
    dmVertex->inEdgeList = (DbListT *)DbDynMemCtxAlloc(outputStmt->memCtx, sizeof(DbListT));
    if (dmVertex->inEdgeList == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc meets mistake.");
        return GMERR_OUT_OF_MEMORY;
    }
    // 在path service PathServiceEntry中统一释放
    dmVertex->outEdgeList = (DbListT *)DbDynMemCtxAlloc(outputStmt->memCtx, sizeof(DbListT));
    if (dmVertex->outEdgeList == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc meets mistake.");
        return GMERR_OUT_OF_MEMORY;
    }
    // 在path service PathServiceEntry中统一释放
    dmVertex->inOrGroupList = (DbListT *)DbDynMemCtxAlloc(outputStmt->memCtx, sizeof(DbListT));
    if (dmVertex->inOrGroupList == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc meets mistake.");
        return GMERR_OUT_OF_MEMORY;
    }
    // 在path service PathServiceEntry中统一释放
    dmVertex->outOrGroupList = (DbListT *)DbDynMemCtxAlloc(outputStmt->memCtx, sizeof(DbListT));
    if (dmVertex->outOrGroupList == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc meets mistake.");
        return GMERR_OUT_OF_MEMORY;
    }
    PathInitList4Vertex(dmVertex, outputStmt);
    uint32_t groupCnt = DbListGetItemCnt(&vertex->outOrGroupList);
    for (uint32_t i = 0; i < groupCnt; i++) {
        DmPathInfoOrGroupT *dmGroup = (DmPathInfoOrGroupT *)DbDynMemCtxAlloc(
            outputStmt->memCtx, sizeof(DmPathInfoOrGroupT));  // 在path service PathServiceEntry中统一释放
        if (dmGroup == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc meets mistake.");
            return GMERR_OUT_OF_MEMORY;
        }
        dmGroup->edges = (DbListT *)DbDynMemCtxAlloc(
            outputStmt->memCtx, sizeof(DbListT));  // 在path service PathServiceEntry中统一释放
        if (dmGroup->edges == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc meets mistake.");
            return GMERR_OUT_OF_MEMORY;
        }
        DbCreateListWithExtendSize(dmGroup->edges, sizeof(DmPathInfoEdgeT *), LIST_EXTEND_SIZE, outputStmt->memCtx);
        Status ret = DbAppendListItem(dmVertex->outOrGroupList, (void *)&dmGroup);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

// 由于是给edgeLabel填充属性，因此输入的memCtx得是edgeLabel所属的memCtx
static Status SetPathInfoVertexAndVarName(
    DbMemCtxT *memCtx, DbListT *list, char *parsedVarName, DmPathInfoVertexT **pathInfoVertex, char **varName)
{
    uint32_t cnt = DbListGetItemCnt(list);
    for (uint32_t i = 0; i < cnt; i++) {
        DmPathInfoVertexT *dmVertex = *(DmPathInfoVertexT **)DbListItem(list, i);
        if (DbStrCmp(parsedVarName, dmVertex->pathInfoVertexName.str, false) == 0) {
            (*pathInfoVertex) = dmVertex;
            // 因为是path ddl时填充edgeLabel的源点与目的点的variable name,因此需要使用edgeLabel的memCtx申请内存
            uint32_t size = DM_STR_LEN(dmVertex->pathInfoVertexName.str);
            *varName = (char *)DbDynMemCtxAlloc(memCtx, size);
            if (*varName == NULL) {
                DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
                    "Unable to alloc the variable name of vertex in SetPathInfoVertexAndVarName. the variable name is "
                    "%s and the size is %" PRIu32 ".",
                    dmVertex->pathInfoVertexName.str, size);
                return GMERR_OUT_OF_MEMORY;
            }
            errno_t err = strcpy_s(*varName, size, dmVertex->pathInfoVertexName.str);
            if (err != EOK) {
                DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy path info vertex name.");
                return GMERR_MEMORY_OPERATE_FAILED;
            }
        }
    }
    return GMERR_OK;
}

static Status SetSrcDstVarNames(char *pathName, DmEdgePatternT *edgePattern, DmSrcDstVertexVariableNameT *namePair)
{
    char *key = (char *)DbDynMemCtxAlloc(edgePattern->memCtx, DM_STR_LEN(pathName));
    if (key == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc key when insert SrcDstVertexVariableNameT.");
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = memcpy_s(key, DM_STR_LEN(pathName), pathName, DM_STR_LEN(pathName));
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy path name for source and destination.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    uint32_t hashCode = DbStrToHash32(pathName);
    DmSrcDstVertexVariableNameT *namePairExist = (DmSrcDstVertexVariableNameT *)DbOamapLookup(
        &edgePattern->pathRelatedEdgeAttrs->pathName2SrcDstVertexVarName, hashCode, key, false);
    if (namePairExist == NULL) {
        Status ret = DbOamapInsert(
            &edgePattern->pathRelatedEdgeAttrs->pathName2SrcDstVertexVarName, hashCode, key, namePair, false);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to insert SrcDstVertexVariableNameT.");
            DbDynMemCtxFree(edgePattern->memCtx, key);
            return ret;
        }
    }
    return GMERR_OK;
}

static Status SetEdgeVertexLabel(
    char *pathName, DmPathInfoEdgeT *dmEdge, ParsePathInfoEdgeT *edge, DbOamapT *vertexLabelId2PathInfoVertexMap)
{
    dmEdge->edgeLabelId = edge->id;
    dmEdge->type = edge->type;
    DmEdgePatternT *edgePattern = NULL;
    Status ret = QryGetEdgeById(dmEdge->edgeLabelId, &edgePattern);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmSrcDstVertexVariableNameT *namePair =
        (DmSrcDstVertexVariableNameT *)DbDynMemCtxAlloc(edgePattern->memCtx, sizeof(DmSrcDstVertexVariableNameT));
    if (namePair == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc SrcDstVertexVariableNameT.");
        ret = GMERR_OUT_OF_MEMORY;
        goto RELEASE_EDGE_LABEL;
    }
    DbListT *list = (DbListT *)DbOamapLookup(
        vertexLabelId2PathInfoVertexMap, edge->srcPathInfoVertex->id, &edge->srcPathInfoVertex->id, false);
    ret = SetPathInfoVertexAndVarName(edgePattern->memCtx, list, edge->srcPathInfoVertex->variableName,
        &dmEdge->srcPathInfoVertex, &namePair->srcVariableName);
    if (ret != GMERR_OK) {
        goto RELEASE_VARIBALE;
    }

    if (edge->dstPathInfoVertex != NULL) {
        list = (DbListT *)DbOamapLookup(
            vertexLabelId2PathInfoVertexMap, edge->dstPathInfoVertex->id, &edge->dstPathInfoVertex->id, false);
        ret = SetPathInfoVertexAndVarName(edgePattern->memCtx, list, edge->dstPathInfoVertex->variableName,
            &dmEdge->dstPathInfoVertex, &namePair->dstVariableName);
        if (ret != GMERR_OK) {
            goto RELEASE_VARIBALE;
        }
    }
    ret = SetSrcDstVarNames(pathName, edgePattern, namePair);
    if (ret != GMERR_OK) {
        goto RELEASE_VARIBALE;
    }
    (void)CataReleaseEdgePattern(edgePattern);
    return ret;
RELEASE_VARIBALE:
    DbDynMemCtxFree(edgePattern->memCtx, namePair);
RELEASE_EDGE_LABEL:
    (void)CataReleaseEdgePattern(edgePattern);
    return ret;
}

static Status SetInOutList(char *pathName, DbListT edgeList, DbListT *dmEdgeList,
    DbOamapT *vertexLabelId2PathInfoVertexMap, IrPlannedStmtT *outputStmt)
{
    uint32_t cnt = DbListGetItemCnt(&edgeList);
    for (uint32_t i = 0; i < cnt; i++) {
        ParsePathInfoEdgeT *edge = *(ParsePathInfoEdgeT **)DbListItem(&edgeList, i);
        uint32_t key = DbStrToHash32(edge->labelName);
        DmPathInfoEdgeT *dmEdge = (DmPathInfoEdgeT *)DbOamapLookup(&g_label2DmEdge, key, edge->labelName, false);
        if (dmEdge == NULL) {
            Status ret = DmCreateEmptyPathInfoEdgeWithMemCtx(outputStmt->memCtx, &dmEdge);
            if (ret != GMERR_OK) {
                return ret;
            }
            ret = DbOamapInsert(&g_label2DmEdge, key, edge->labelName, dmEdge, false);
            if (ret != GMERR_OK) {
                DbDynMemCtxFree(outputStmt->memCtx, dmEdge);
                dmEdge = NULL;
                return ret;
            }
        }
        Status ret = SetEdgeVertexLabel(pathName, dmEdge, edge, vertexLabelId2PathInfoVertexMap);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DbAppendListItem(dmEdgeList, &dmEdge);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status SetInOutGroupList(char *pathName, DbListT groupList, DbListT *dmGroupList,
    DbOamapT *vertexLabelId2PathInfoVertexMap, IrPlannedStmtT *outputStmt)
{
    uint32_t cnt = DbListGetItemCnt(&groupList);
    for (uint32_t i = 0; i < cnt; i++) {
        ParsePathInfoOrGroupT group = ((ParsePathInfoOrGroupT *)groupList.items)[i];
        DmPathInfoOrGroupT *dmGroup = ((DmPathInfoOrGroupT **)dmGroupList->items)[i];
        Status ret = SetInOutList(pathName, group.edges, dmGroup->edges, vertexLabelId2PathInfoVertexMap, outputStmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

// put all ParsePathInfoVertexT information into DmPathInfoVertexT
static Status SetVertexAll(char *pathName, ParsePathInfoVertexT *vertex, DmPathInfoVertexT *dmVertex,
    DbOamapT *vertexLabelId2PathInfoVertexMap, IrPlannedStmtT *outputStmt)
{
    dmVertex->vertexLabelId = vertex->id;
    dmVertex->pathInfoVertexName.len = DM_STR_LEN(vertex->variableName);
    dmVertex->pathInfoVertexName.str = vertex->variableName;
    /* set inEdgeList */
    if (DbListGetItemCnt(&vertex->inEdgeList) != 0) {
        Status ret = SetInOutList(
            pathName, vertex->inEdgeList, dmVertex->inEdgeList, vertexLabelId2PathInfoVertexMap, outputStmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    /* set outEdgeList */
    if (DbListGetItemCnt(&vertex->outEdgeList) != 0) {
        Status ret = SetInOutList(
            pathName, vertex->outEdgeList, dmVertex->outEdgeList, vertexLabelId2PathInfoVertexMap, outputStmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    /* don't support inOrGroupList now */

    /* set outOrGroupList */
    if (DbListGetItemCnt(&vertex->outOrGroupList) != 0) {
        Status ret = SetInOutGroupList(
            pathName, vertex->outOrGroupList, dmVertex->outOrGroupList, vertexLabelId2PathInfoVertexMap, outputStmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return GMERR_OK;
}

static Status SetDmPathInfoVertex(
    ParseCreatePathStmtT *stmt, DbOamapT *vertexLabelId2PathInfoVertexMap, IrPlannedStmtT *outputStmt)
{
    DbOamapIteratorT iterator = 0;
    void *key = NULL;
    ParsePathInfoVertexT *vertex = NULL;
    while (DbOamapFetch(stmt->base.variable2PathInfoVertex, &iterator, &key, (void **)&vertex) == GMERR_OK) {
        /* put basic ParsePathInfoVertexT information into DmPathInfoVertexT */
        DbListT *list = (DbListT *)DbOamapLookup(vertexLabelId2PathInfoVertexMap, vertex->id, &vertex->id, false);
        if (list == NULL) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION,
                "Unable to find PathInfoVertexs when setting PathInfoVertex. the vertex id is %u.", vertex->id);
            return GMERR_DATA_EXCEPTION;
        }
        uint32_t cnt = DbListGetItemCnt(list);
        for (uint32_t i = 0; i < cnt; i++) {
            DmPathInfoVertexT *dmVertex = *(DmPathInfoVertexT **)DbListItem(list, i);
            if (DbStrCmp(vertex->variableName, dmVertex->pathInfoVertexName.str, false) != 0) {
                continue;
            }
            Status ret = SetVertexAll(stmt->pathName, vertex, dmVertex, vertexLabelId2PathInfoVertexMap, outputStmt);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static Status BuildEmptyDmPathInfoVertex(
    ParseCreatePathStmtT *stmt, DbOamapT *vertexLabelId2PathInfoVertexMap, IrPlannedStmtT *outputStmt)
{
    DbOamapIteratorT iterator = 0;
    void *key = NULL;
    ParsePathInfoVertexT *vertex = NULL;
    while (DbOamapFetch(stmt->base.variable2PathInfoVertex, &iterator, &key, (void **)&vertex) == GMERR_OK) {
        /* put basic ParsePathInfoVertexT information into DmPathInfoVertexT */
        DmPathInfoVertexT *dmVertex = NULL;
        Status ret = DmCreateEmptyPathInfoVertexWithMemCtx(outputStmt->memCtx, &dmVertex);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc meets mistake.");
            return GMERR_OUT_OF_MEMORY;
        }
        ret = SetVertexBasic(vertex, dmVertex, outputStmt);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(outputStmt->memCtx, dmVertex);
            dmVertex = NULL;
            return ret;
        }
        ret = DmAddPathInfoVertexToVertexLabelId2PathInfoVertexMap(
            outputStmt->memCtx, vertexLabelId2PathInfoVertexMap, &dmVertex);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(outputStmt->memCtx, dmVertex);
            dmVertex = NULL;
            return ret;
        }
    }
    return GMERR_OK;
}

static void InitSubFields(DmComplexPathInfoT *pathInfo, IrPlannedStmtT *outputStmt)
{
    for (uint32_t i = 0; i < DM_SUBS_COMPLEXPATH_CEIL; i++) {
        pathInfo->vertexLabelId2SubFieldsMap[i] = NULL;
    }
    pathInfo->subscriptionNum = 0;
    (void)memset_s(pathInfo->subEventNums, DM_SUBS_COMPLEXPATH_CEIL * sizeof(uint16_t), 0,
        DM_SUBS_COMPLEXPATH_CEIL * sizeof(uint16_t));
}

static Status SetEventSubFields(
    IrPlannedStmtT *outputStmt, SessionT *session, DbOamapT *subFields, DbOamapT **vertexId2SubFields)
{
    /* init map */
    DbOamapT *map = (DbOamapT *)DbDynMemCtxAlloc(outputStmt->memCtx, sizeof(DbOamapT));
    if (map == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc meets mistake.");
        return GMERR_OUT_OF_MEMORY;
    }
    Status ret = DbOamapInit(map, 0, DbOamapUint32Compare, outputStmt->memCtx, true);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(outputStmt->memCtx, map);
        return ret;
    }

    DbOamapIteratorT iterator = 0;
    void *key = NULL;
    DmVertexSubFieldsT *field = NULL;
    while (DbOamapFetch(subFields, &iterator, (void **)(&key), (void **)(&field)) == GMERR_OK) {
        DmVertexLabelT *realVertex;  // real vertex label in data model
        ret = QryGetVertexlabelByName(session, field->vertexLabelName->str, &realVertex);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_UNDEFINED_TABLE,
                "Unable to analyze path, dm vertex label hasn't been created, vertex label: %s.",
                field->vertexLabelName->str);
            DbDynMemCtxFree(outputStmt->memCtx, map);
            return ret;
        }
        field->vertexLabelId = realVertex->metaCommon.metaId;
        (void)CataReleaseVertexLabel(realVertex);
        ret = DbOamapInsert(map, field->vertexLabelId, (void *)(&field->vertexLabelId), (void *)field, false);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(outputStmt->memCtx, map);
            return ret;
        }
    }
    *vertexId2SubFields = map;
    return GMERR_OK;
}

static Status SetSubFields4Replace(DmComplexPathInfoT *pathInfo, SessionT *session, IrPlannedStmtT *outputStmt)
{
    uint32_t replaceMapSize = DbOamapUsedSize(g_maps4MergeReplace.label2SubFieldsOfVertexReplace);
    if (replaceMapSize != 0) {
        Status ret = SetEventSubFields(outputStmt, session, g_maps4MergeReplace.label2SubFieldsOfVertexReplace,
            &pathInfo->vertexLabelId2SubFieldsMap[DM_SUBS_COMPLEXPATH_REPLACE]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return GMERR_OK;
}

static Status SetSubFields4Delete(DmComplexPathInfoT *pathInfo, SessionT *session, IrPlannedStmtT *outputStmt)
{
    uint32_t deleteMapSize = DbOamapUsedSize(g_maps4MergeReplace.label2SubFieldsOfVertexDelete);
    if (deleteMapSize != 0) {
        Status ret = SetEventSubFields(outputStmt, session, g_maps4MergeReplace.label2SubFieldsOfVertexDelete,
            &pathInfo->vertexLabelId2SubFieldsMap[DM_SUBS_COMPLEXPATH_DELETE]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return GMERR_OK;
}

static Status SetSubFields(DmComplexPathInfoT *pathInfo, SessionT *session, IrPlannedStmtT *outputStmt)
{
    Status ret = SetSubFields4Replace(pathInfo, session, outputStmt);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(outputStmt->memCtx, pathInfo->pattern);
        pathInfo->pattern = NULL;
        return ret;
    }
    ret = SetSubFields4Delete(pathInfo, session, outputStmt);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(outputStmt->memCtx, pathInfo->pattern);
        pathInfo->pattern = NULL;
        return ret;
    }
    return GMERR_OK;
}

static Status SetPathInfoHelper(DmComplexPathInfoT *pathInfo, SessionT *session, ParseCreatePathStmtT *stmt,
    DbOamapT *vertexLabelId2PathInfoVertexMap, IrPlannedStmtT *outputStmt)
{
    pathInfo->metaCommon.dbId = session->dbId;
    pathInfo->metaCommon.namespaceId = session->namespaceId;
    pathInfo->metaCommon.metaName = stmt->pathName;
    pathInfo->vertexLabelId2PathInfoVertexMap = vertexLabelId2PathInfoVertexMap;
    pathInfo->pathIdInApp = stmt->pathIdInApp;
    pathInfo->pattern = (DmPathPatternT *)DbDynMemCtxAlloc(outputStmt->memCtx, sizeof(DmPathPatternT));
    if (pathInfo->pattern == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc meets mistake.");
        return GMERR_OUT_OF_MEMORY;
    }
    pathInfo->pattern->pathInfoVertexCount = stmt->base.vertexCount;
    pathInfo->pattern->pathInfoEdgeCount = stmt->base.edgeCount;
    pathInfo->deletionMode = stmt->deletionMode;
    pathInfo->pathTlv = stmt->pathTlv;
    return GMERR_OK;
}

static Status SetVertexLabelAndFieldTuple(char *vertexLabelName, char *propertyName, SessionT *session,
    IrPlannedStmtT *outputStmt, DmVertexLabelAndFieldTupleT **outputTuple)
{
    DmVertexLabelAndFieldTupleT *tmpField =
        (DmVertexLabelAndFieldTupleT *)DbDynMemCtxAlloc(outputStmt->memCtx, sizeof(DmVertexLabelAndFieldTupleT));
    if (tmpField == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc memory for pidField when set pidField.");
        return GMERR_OUT_OF_MEMORY;
    }
    Status ret = DbCopyStrToText(
        outputStmt->memCtx, &tmpField->vertexLabelName, vertexLabelName, (uint32_t)strlen(vertexLabelName));
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(outputStmt->memCtx, tmpField);
        DB_LOG_ERROR(ret, "Unable to copy vtxLabelName when set pidField. the name is %s.", vertexLabelName);
        return ret;
    }
    DmVertexLabelT *vertexLabel = NULL;
    ret = QryGetVertexlabelByName(session, vertexLabelName, &vertexLabel);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(outputStmt->memCtx, tmpField);
        DB_LOG_ERROR(ret, "Unable to get vtxLabelName when set pathInfo. the name is %s.", vertexLabelName);
        return ret;
    }
    (void)CataReleaseVertexLabel(vertexLabel);
    tmpField->vertexLabelId = vertexLabel->metaCommon.metaId;
    ret = DbCopyStrToText(outputStmt->memCtx, &tmpField->fieldName, propertyName, (uint32_t)strlen(propertyName));
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(outputStmt->memCtx, tmpField);
        DB_LOG_ERROR(ret, "Unable to copy fieldName when set pidField. the name is %s.", propertyName);
        return ret;
    }
    uint32_t fieldCnt = vertexLabel->metaVertexLabel->schema->propeNum;
    for (uint32_t i = 0; i < fieldCnt; ++i) {
        DmPropertySchemaT *property = &vertexLabel->metaVertexLabel->schema->properties[i];
        if (DbStrCmp(property->name, propertyName, false) == 0) {
            tmpField->fieldId = property->propeId;
        }
    }
    *outputTuple = tmpField;
    return GMERR_OK;
}

static Status SetPathInfoPathType(DmComplexPathInfoT *pathInfo, ComplexPathTypeE *pathType)
{
    pathInfo->hasReplaceRootOrMergeView = false;
    if (*pathType == COMPLEX_PATH_NORMAL) {
        pathInfo->pathType = DM_COMPLEX_PATH_NORMAL;
    } else if (*pathType == COMPLEX_PATH_MERGE) {
        pathInfo->pathType = DM_COMPLEX_PATH_MERGE;
    } else if (*pathType == COMPLEX_PATH_REPLACE) {
        pathInfo->pathType = DM_COMPLEX_PATH_REPLACE;
    } else {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Set merge or replace info unsuccesful, path type unsupported: %u",
            (uint32_t)*pathType);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

static Status SetProperty(SessionT *session, char *propName, char *vertexLabelName, DmVertexLabelPropertyT **dmVlProp,
    IrPlannedStmtT *outputStmt)
{
    DmVertexLabelPropertyT *dmPropTemp = NULL;
    Status ret = SetVertexLabelAndFieldTuple(vertexLabelName, propName, session, outputStmt, &dmPropTemp);
    if (ret != GMERR_OK) {
        return ret;
    }
    *dmVlProp = dmPropTemp;
    return GMERR_OK;
}

static Status SetClauseTag4MergePath(
    DmSetClauseT *dmSetClause, SetClauseT *setClause, SessionT *session, IrPlannedStmtT *outputStmt)
{
    dmSetClause->tag = DM_SET_CLAUSE_COALESCE_FUNC;
    dmSetClause->coalesceClause = (DmCoalesceClauseT *)DbDynMemCtxAlloc(outputStmt->memCtx, sizeof(DmCoalesceClauseT));
    if (dmSetClause->coalesceClause == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to set replace path info, unable to allocate memory for coalesce");
        return GMERR_OUT_OF_MEMORY;
    }
    // left coalesce
    Status ret = SetProperty(session, setClause->coalesceClause->coalesceLeftProp->propertyName,
        setClause->coalesceClause->coalesceLeftProp->vertexLabelName, &dmSetClause->coalesceClause->coalesceLeftProp,
        outputStmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    // right coalesce
    return SetProperty(session, setClause->coalesceClause->coalesceRightProp->propertyName,
        setClause->coalesceClause->coalesceRightProp->vertexLabelName, &dmSetClause->coalesceClause->coalesceRightProp,
        outputStmt);
}

static Status SetClauseTagAndPropertiesForMergePath(
    DmComplexPathInfoT *pathInfo, MergeReplacePathAttrT *mpPathAttr, SessionT *session, IrPlannedStmtT *outputStmt)
{
    uint32_t setClauseCnt = DbListGetItemCnt(&mpPathAttr->setClauses);
    pathInfo->generatePathInfo->setClauses = (DbListT){};
    DbCreateList(&pathInfo->generatePathInfo->setClauses, sizeof(DmSetClauseT), outputStmt->memCtx);
    for (uint32_t i = 0; i < setClauseCnt; i++) {
        SetClauseT *setClause = (SetClauseT *)DbListItem(&mpPathAttr->setClauses, i);
        DmSetClauseT *dmSetClause = (DmSetClauseT *)DbDynMemCtxAlloc(outputStmt->memCtx, sizeof(DmSetClauseT));
        if (dmSetClause == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to set merge path, unable to allocate memory");
            return GMERR_OUT_OF_MEMORY;
        }
        if (setClause->tag == SET_CLAUSE_PROPERTY) {
            dmSetClause->tag = DM_SET_CLAUSE_PROPERTY;
            // right property
            Status ret = SetProperty(session, setClause->rightProp->propertyName, setClause->rightProp->vertexLabelName,
                &dmSetClause->rightProp, outputStmt);
            if (ret != GMERR_OK) {
                return ret;
            }
        } else {
            // SET_CLAUSE_COALESCE_FUNC
            Status ret = SetClauseTag4MergePath(dmSetClause, setClause, session, outputStmt);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        // set left property
        Status ret = SetProperty(
            session, setClause->leftPropertyName, mpPathAttr->viewTableName, &dmSetClause->leftProp, outputStmt);
        if (ret != GMERR_OK) {
            return ret;
        }
        // append to set clause list
        ret = DbAppendListItem(&pathInfo->generatePathInfo->setClauses, dmSetClause);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status SetClauseTagAndPropertiesForReplacePath(
    DmComplexPathInfoT *pathInfo, ParseCreatePathStmtT *stmt, SessionT *session, IrPlannedStmtT *outputStmt)
{
    // set clause doesn't have the right property vertex label name for replace paths
    // need to propagate the tail vertex label name to the set clause attributes
    ParsePathInfoVertexT *root = *(ParsePathInfoVertexT **)DbListItem(stmt->pattern->roots, 0);
    ParsePathInfoVertexT *tmpVertex = root;
    while (&(tmpVertex->outEdgeList) != NULL && DbListGetItemCnt(&(tmpVertex->outEdgeList)) > 0) {
        ParsePathInfoEdgeT *outEdge =
            *(ParsePathInfoEdgeT **)DbListItem(&(tmpVertex->outEdgeList), 0);  // path is linear, only one out edge
        tmpVertex = outEdge->dstPathInfoVertex;
    }
    ParsePathInfoVertexT *tail = tmpVertex;

    MergeReplacePathAttrT *mpPathAttr = stmt->mpPathAttr;
    uint32_t setClauseCnt = DbListGetItemCnt(&mpPathAttr->setClauses);
    pathInfo->generatePathInfo->setClauses = (DbListT){};
    DbCreateList(&pathInfo->generatePathInfo->setClauses, sizeof(DmSetClauseT), outputStmt->memCtx);
    for (uint32_t i = 0; i < setClauseCnt; i++) {
        SetClauseT *setClause = (SetClauseT *)DbListItem(&mpPathAttr->setClauses, i);
        DmSetClauseT *dmSetClause = (DmSetClauseT *)DbDynMemCtxAlloc(outputStmt->memCtx, sizeof(DmSetClauseT));
        if (dmSetClause == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to set replace path info, unable to allocate memory for catalog");
            return GMERR_OUT_OF_MEMORY;
        }
        if (setClause->tag == SET_CLAUSE_PROPERTY) {
            dmSetClause->tag = DM_SET_CLAUSE_PROPERTY;
            // right property (use the tail label name)
            DmVertexLabelPropertyT *dmProp = NULL;
            Status ret = SetVertexLabelAndFieldTuple(
                tail->labelName, setClause->rightProp->propertyName, session, outputStmt, &dmProp);
            if (ret != GMERR_OK) {
                return ret;
            }
            dmSetClause->rightProp = dmProp;
        } else {
            DB_LOG_ERROR(GMERR_INVALID_PROPERTY,
                "Set replace path info unsuccesful, unabled to analyze set clause tag: %u.", (uint32_t)setClause->tag);
            return GMERR_INVALID_PROPERTY;
        }
        // set left property
        DmVertexLabelPropertyT *dmProp = NULL;
        Status ret = SetVertexLabelAndFieldTuple(
            mpPathAttr->viewTableName, setClause->leftPropertyName, session, outputStmt, &dmProp);
        if (ret != GMERR_OK) {
            return ret;
        }
        dmSetClause->leftProp = dmProp;
        ret = DbAppendListItem(&pathInfo->generatePathInfo->setClauses, dmSetClause);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status SetMergeOrReplaceInfo(
    DmComplexPathInfoT *pathInfo, ParseCreatePathStmtT *stmt, SessionT *session, IrPlannedStmtT *outputStmt)
{
    if (pathInfo->pathType == DM_COMPLEX_PATH_NORMAL) {
        return GMERR_OK;
    }
    MergeReplacePathAttrT *mpPathAttr = stmt->mpPathAttr;
    pathInfo->generatePathInfo =
        (DmGeneratePathInfoT *)DbDynMemCtxAlloc(outputStmt->memCtx, sizeof(DmGeneratePathInfoT));
    if (pathInfo->generatePathInfo == NULL) {
        DB_LOG_ERROR(
            GMERR_OUT_OF_MEMORY, "Set merge or replace info unsuccesful in merge path info memory allocation.");
        return GMERR_OUT_OF_MEMORY;
    }
    DmVertexLabelT *vertexLabel = NULL;
    Status ret = QryGetVertexlabelByName(session, mpPathAttr->viewTableName, &vertexLabel);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(outputStmt->memCtx, pathInfo->generatePathInfo);
        DB_LOG_ERROR(GMERR_UNDEFINED_TABLE, "Set merge or replace info unsuccesful, undefined table %s.",
            mpPathAttr->viewTableName);
        return GMERR_UNDEFINED_TABLE;
    }
    pathInfo->generatePathInfo->viewTableId = vertexLabel->metaCommon.metaId;
    (void)CataReleaseVertexLabel(vertexLabel);
    if (pathInfo->pathType == DM_COMPLEX_PATH_REPLACE) {
        // set clause for replace path
        ret = SetClauseTagAndPropertiesForReplacePath(pathInfo, stmt, session, outputStmt);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(outputStmt->memCtx, pathInfo->generatePathInfo);
            return ret;
        }
    } else {
        // set clause for merge path
        ret = SetClauseTagAndPropertiesForMergePath(pathInfo, mpPathAttr, session, outputStmt);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(outputStmt->memCtx, pathInfo->generatePathInfo);
            return ret;
        }
    }
    return GMERR_OK;
}

static Status SetPIDField(
    DmComplexPathInfoT *pathInfo, ParseCreatePathStmtT *stmt, SessionT *session, IrPlannedStmtT *outputStmt)
{
    if (stmt->pidField == NULL) {
        pathInfo->pidField = NULL;
        return GMERR_OK;
    }
    DmPIDFieldT *outField = NULL;
    Status ret = SetVertexLabelAndFieldTuple(
        stmt->pidField->vertexLabelName, stmt->pidField->propertyName, session, outputStmt, &outField);
    if (ret != GMERR_OK) {
        return ret;
    }
    pathInfo->pidField = outField;
    return GMERR_OK;
}

static Status SetPathInfo(DmComplexPathInfoT *pathInfo, SessionT *session, ParseCreatePathStmtT *stmt,
    DbOamapT *vertexLabelId2PathInfoVertexMap, IrPlannedStmtT *outputStmt)
{
    Status ret = SetPathInfoHelper(pathInfo, session, stmt, vertexLabelId2PathInfoVertexMap, outputStmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    ParsePathInfoVertexT *root = *(ParsePathInfoVertexT **)DbListItem(stmt->pattern->roots, 0);
    uint32_t vertexId = root->id;
    DbListT *list = (DbListT *)DbOamapLookup(vertexLabelId2PathInfoVertexMap, vertexId, (void *)&vertexId, false);
    if (list == NULL) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Unable to find PathInfoVertexs when setting PathInfo. the vertex id is %u.",
            vertexId);
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t cnt = DbListGetItemCnt(list);
    DmPathInfoVertexT *dmVertex = NULL;
    for (uint32_t i = 0; i < cnt; i++) {
        dmVertex = *(DmPathInfoVertexT **)DbListItem(list, i);
        if (DbStrCmp(root->variableName, dmVertex->pathInfoVertexName.str, false) == 0) {
            pathInfo->pattern->root = dmVertex;
        }
    }
    if (dmVertex == NULL) {
        DB_LOG_ERROR(
            GMERR_OUT_OF_MEMORY, "Unable to analyze path, can't get DmPathInfoVertexT %s.", root->variableName);
        DbDynMemCtxFree(outputStmt->memCtx, pathInfo->pattern);
        pathInfo->pattern = NULL;
        return GMERR_OUT_OF_MEMORY;
    }

    ret = SetPIDField(pathInfo, stmt, session, outputStmt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to set pidField when fill pathinfo.");
        return ret;
    }

    ret = SetPathInfoPathType(pathInfo, &stmt->mpPathAttr->pathType);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SetMergeOrReplaceInfo(pathInfo, stmt, session, outputStmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    /* set subFields */
    uint32_t replaceMapSize = DbOamapUsedSize(g_maps4MergeReplace.label2SubFieldsOfVertexReplace);
    uint32_t deleteMapSize = DbOamapUsedSize(g_maps4MergeReplace.label2SubFieldsOfVertexDelete);
    if (replaceMapSize != 0 || deleteMapSize != 0) {
        InitSubFields(pathInfo, outputStmt);
    } else {
        return GMERR_OK;
    }

    return SetSubFields(pathInfo, session, outputStmt);
}

static Status SetCondition(
    DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, ParsePathConditionT *tmpParseCond, DmPathConditionT **condition)
{
    DB_POINTER4(memCtx, vertexLabel, tmpParseCond, condition);
    DmPropertySchemaT *schema = NULL;
    Status ret = DmSchemaGetPropeByName(vertexLabel->metaVertexLabel->schema, tmpParseCond->fieldName, &schema, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unable to fulfill constraint of path. the field %s is undefined in table %s.",
            tmpParseCond->fieldName, tmpParseCond->vertexLabelName);
        return ret;
    }
    DmPathConditionT *tmpCond = (DmPathConditionT *)DbDynMemCtxAlloc(memCtx, sizeof(DmPathConditionT));
    if (tmpCond == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to malloc memory for pathcondition. the size is %u.",
            (uint32_t)sizeof(DmPathConditionT));
        return GMERR_OUT_OF_MEMORY;
    }
    tmpCond->propId = schema->propeId;
    DmValueT *dmValue = NULL;
    ret = ConvertStr2DmValue(memCtx, schema->dataType, schema->size, tmpParseCond->value, &dmValue);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(memCtx, tmpCond);
        return ret;
    }
    tmpCond->value = *dmValue;
    *condition = tmpCond;
    return GMERR_OK;
}

static Status SetConstraint(
    SessionT *session, DmComplexPathInfoT *pathInfo, ParseCreatePathStmtT *stmt, IrPlannedStmtT *outputStmt)
{
    DB_POINTER4(session, pathInfo, stmt, outputStmt);
    if (stmt->constraint == NULL) {
        return GMERR_OK;
    }
    DmPathConstraintT *constraint = NULL;
    Status ret = DmCreateEmptyPathConstraintWithMemCtx(outputStmt->memCtx, &constraint);
    if (ret != GMERR_OK) {
        return ret;
    }
    ParsePathConstraintT *parseConstraint = stmt->constraint;
    DbListT *parseConditions = &parseConstraint->conditions;
    ParsePathConditionT *parseCondition = (ParsePathConditionT *)DbListItem(parseConditions, 0);
    DmVertexLabelT *vertexLabel = NULL;
    ret = QryGetVertexlabelByName(session, parseCondition->vertexLabelName, &vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    constraint->vertexLabelId = vertexLabel->metaCommon.metaId;
    uint32_t condNum = DbListGetItemCnt(parseConditions);
    for (uint32_t i = 0; i < condNum; ++i) {
        ParsePathConditionT *tmpParseCond = DbListItem(parseConditions, i);
        DmPathConditionT *condition = NULL;

        ret = SetCondition(outputStmt->memCtx, vertexLabel, tmpParseCond, &condition);
        if (ret != GMERR_OK) {
            goto EXIT;
        }

        ret = DbAppendListItem(&constraint->conditions, condition);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
    }
    pathInfo->constraint = constraint;

    (void)CataReleaseVertexLabel(vertexLabel);
    return GMERR_OK;

EXIT:
    (void)CataReleaseVertexLabel(vertexLabel);
    return ret;
}

static Status SetCreatePathPatternStmt(DbMemCtxT *memCtx, DmComplexPathInfoT **pathInfo, IrPlannedStmtT *outputStmt)
{
    CreatePathPatternStmtT *node = (CreatePathPatternStmtT *)DbDynMemCtxAlloc(memCtx, sizeof(CreatePathPatternStmtT));
    if (node == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc meets mistake.");
        return GMERR_OUT_OF_MEMORY;
    }
    node->pathInfo = *pathInfo;
    node->node.tag = T_GQL_CREATE_PATH_PATTERN_STMT;
    NodeT *uStmt = (NodeT *)(void *)node;
    outputStmt->utilityStmt = uStmt;
    outputStmt->irPlan = NULL;
    return GMERR_OK;
}

static Status InitPathInfoVertexMap(DbOamapT **map, IrPlannedStmtT *outputStmt)
{
    Status ret = GMERR_OK;
    // release in path service
    DbOamapT *vertexLabelId2PathInfoVertexMap = (DbOamapT *)DbDynMemCtxAlloc(outputStmt->memCtx, sizeof(DbOamapT));
    if (vertexLabelId2PathInfoVertexMap == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to analyze path, malloc meets mistake.");
        return GMERR_OUT_OF_MEMORY;
    }
    ret = DbOamapInit(vertexLabelId2PathInfoVertexMap, 0, DbOamapStringCompare, outputStmt->memCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    *map = vertexLabelId2PathInfoVertexMap;
    return ret;
}

Status FulfillCreatePathStmt(
    SessionT *session, ParseCreatePathStmtT *stmt, Maps4MergeReplaceT *maps, IrPlannedStmtT *outputStmt)
{
    g_maps4MergeReplace = *maps;
    /* initialize DmComplexPathInfoT */
    DmComplexPathInfoT *pathInfo;
    Status ret = DmCreateEmptyComplexPathInfoWithMemCtx(outputStmt->memCtx, &pathInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "PathAnalyzerFillPathInfo: Unable to create empty path info.");
        return ret;
    }

    /* build vertexLabelId2PathInfoVertexMap in DmComplexPathInfoT */
    DbOamapT *vertexLabelId2PathInfoVertexMap = NULL;
    ret = InitPathInfoVertexMap(&vertexLabelId2PathInfoVertexMap, outputStmt);
    if (ret != GMERR_OK) {
        goto RELEASE_INFO;
    }

    ret = DbOamapInit(&g_label2DmEdge, 1, DbOamapStringCompare, outputStmt->memCtx, true);
    if (ret != GMERR_OK) {
        goto RELEASE_INFO;
    }

    /* put all ParsePathInfoVertexT information into DmPathInfoVertexT->vertexLabelId2PathInfoVertexMap */
    ret = BuildEmptyDmPathInfoVertex(stmt, vertexLabelId2PathInfoVertexMap, outputStmt);
    if (ret != GMERR_OK) {
        goto RELEASE_INFO;
    }

    ret = SetDmPathInfoVertex(stmt, vertexLabelId2PathInfoVertexMap, outputStmt);
    if (ret != GMERR_OK) {
        goto RELEASE_INFO;
    }

    ret = SetPathInfo(pathInfo, session, stmt, vertexLabelId2PathInfoVertexMap, outputStmt);
    if (ret != GMERR_OK) {
        goto RELEASE_INFO;
    }

    /* fulfill constraint */
    ret = SetConstraint(session, pathInfo, stmt, outputStmt);
    if (ret != GMERR_OK) {
        goto RELEASE_INFO;
    }

    /* fulfill IrPlannedStmtT */
    ret = SetCreatePathPatternStmt(outputStmt->memCtx, &pathInfo, outputStmt);
    if (ret != GMERR_OK) {
        goto RELEASE_INFO;
    }

    return GMERR_OK;
RELEASE_INFO:
    DbDynMemCtxFree(outputStmt->memCtx, pathInfo);
    pathInfo = NULL;
    return ret;
}
