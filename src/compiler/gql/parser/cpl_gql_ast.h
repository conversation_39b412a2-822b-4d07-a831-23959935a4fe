/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: data structures for gql parser AST
 * Author: GQL
 * Create: 2022-08-27
 */

#ifndef CPL_GQL_AST_H
#define CPL_GQL_AST_H

#include "adpt_types.h"
#include "db_list.h"
#include "db_hashmap.h"
#include "dm_meta_basic.h"
#include "dm_meta_topo_label.h"
#include "dm_meta_index_label.h"
#include "dm_meta_pathtrigger_info.h"
#include "dm_meta_complex_path.h"
#include "cpl_gql_compiler.h"

#define STR_LEN_MAX 1023
#define PATH_NAME_LEN_MAX 41  // path name前缀英文最多31个字符 + 后缀数字类型为uint32_t
#define INT_LEN_MAX 20
#define EVENT_CNT 1
#define EDGE_LABEL_COUNT_MAX 200
#define OR_GROUP_EDGE_LABEL_COUNT_MAX 160
#define VERTEX_LABEL_COUNT_MAX 201
#define PATH_DEPTH_MAX 15
#define FIRST_IR_CHILD 0
#define SECOND_IR_CHILD 1
#define ANON_VARIABLE_NAME_LEN 16
#define GQL_MAX_BATCH_OP_NUM 2000

#ifdef __cplusplus
extern "C" {
#endif

/* data structure */

// expression operator
typedef enum {
    EQUAL = 0,   // ==
    IN_LIST,     // in (...)
    NOT_IN_LIST  // not in (...)
} ExprOperatorE;

typedef enum {
    CREATE_VERTEXLABEL = 0,
    DROP_VERTEXLABEL,
    CREATE_EDGE,
    DROP_EDGE,
    CREATE_PATH,
    DROP_PATH,
    CREATE_SUBSCRIPTION,
    DROP_SUBSCRIPTION,
    CREATE_TRIGGER,
    DROP_TRIGGER,
    SET_SUBSCRIPTION_SLOWSPEED
} StatementTagE;

typedef enum {
    PROPERTY = 0,  // property of vertexlabel
    BIT_EXP,       // property & integer
    INTEGER_LIST   // integer collection
} OperationTagE;

typedef enum { FULL_SEARCH = 0 } SearchModeE;

typedef enum { SUBS_EVENT_REPLACE, SUBS_EVENT_FLOOR } SubsEventE;

// base class for statement
typedef struct {
    StatementTagE tag;
    uint32_t vertexCount;               // 总节点数
    uint32_t edgeCount;                 // 总边数
    DbOamapT *variable2PathInfoVertex;  // <variable name : ParsePathInfoVertexT>
    DbOamapT *labels;                   // <vertex label name : occupy>
    DbListT *orGroups;                  // item<ParsePathInfoOrGroupT *>
} GqlStatementT;

// property in vertex label
typedef struct {
    char *type;                // property type defined by user
    char *name;                // name of property
    DbDataTypeE standardType;  // real property type
    uint64_t varSize;          // size of fixed type property
} PropertyT;

typedef struct {
    char *name;                     // index name
    char *type;                     // index type defined by user
    DmIndexTypeE standardType;      // real index type
    uint32_t sourceLabelId;         // vertex label id belongs to
    DbListT properties;             // associated properties, item<PropertyT>
    DmIndexConstraintE constraint;  // uniqueness of index
    uint32_t initHashCapacity;      // hash初始表的大小
} IndexT;

typedef struct {
    IndexT *primaryIndex;  // primary key index
    DbListT indexes;       // IndexT
} IndexClauseT;

typedef enum {
    VERTEX_LABEL_MERGE_NONE = 0,
    VERTEX_LABEL_MERGE,
    VERTEX_LABEL_MERGE_UPDATE,
    VERTEX_LABEL_MERGE_BOTT
} VertexLabelMergeViewTypeE;

// CREATE VERTEXLABEL
typedef struct {
    GqlStatementT base;
    char *label;           // vertex label
    DbListT properties;    // PropertyT
    IndexT *primaryIndex;  // primary key index
    DbListT indexes;       // IndexT
    VertexLabelMergeViewTypeE mergeViewType;
} ParseCreateVertexLabelStmtT;

// base class for operation
typedef struct {
    OperationTagE tag;
} OperationT;

typedef struct {
    OperationT type;
    DbListT numbers;  // item<uint64_t>
} IntegerListOperationT;

typedef struct {
    OperationT type;
    char *vertexLabelName;
    char *propertyName;
    uint32_t vertexPropeId;  // 字段id
    uint64_t number;
} BitOperationT;

typedef struct {
    OperationT type;
    char *vertexLabelName;
    char *propertyName;
    uint32_t vertexPropeId;  // 字段id
} PropertyOperationT;

// conditions of edge label
typedef struct {
    ExprOperatorE expOp;
    OperationT *left;   // left operation in condition
    OperationT *right;  // right operation in condition
} ConditionT;

// CREATE EDGE
typedef struct {
    GqlStatementT base;
    const char *label;  // edge label
    const char *srcVertexLabelName;
    uint32_t srcVertexLabelNameLen;
    const char *desVertexLabelName;
    uint32_t desVertexLabelNameLen;
    DmEdgeTypeE edgeType;
    DbListT conditions;  // ConditionT
    bool oneWayFlag;
    bool isEdgePattern;  // edge label or edge pattern
} ParseCreateEdgeLabelStmtT;

// forward property when event occurs
typedef struct {
    char *propertyName;     // name of property
    char *vertexlabelName;  // name of vertex label belongs to
    uint32_t encapType;     // 下发TLV类型
    char *specifyPathName;  // name of specifypath
} ForwardPropertyT;

typedef struct Subs {
    DmSubsEventE eventType;
    DbListT *subFields;
} SubsT;

// subscription information about forward property of path
typedef struct {
    DbListT *insertForward;   // ForwardPropertyT *
    DbListT *deleteForward;   // ForwardPropertyT *
    DbListT *replaceForward;  // ForwardPropertyT *
    DbListT *updateForward;   // ForwardPropertyT *
} SubscribeFieldT;

/*
 * 树状path所需的vertex
 */

typedef struct {
    DbListT vertexs;  // item<ParsePathInfoVertexT *>
} ParseVertexGroupT;

typedef struct {
    DbListT edges;  // item<ParsePathInfoEdgeT *>
} ParsePathInfoOrGroupT;

typedef struct {
    char *labelName;
    char *variableName;
    uint32_t id;
    DbListT inEdgeList;      // 入边集合（包括或组边/一般边）, item<ParsePathInfoEdgeT *>
    DbListT inOrGroupList;   // 入边或组集合, item<ParsePathInfoOrGroupT>
    DbListT outEdgeList;     // 出边集合（包括或组边/一般边/空边）
    DbListT outOrGroupList;  // 出边或组集合, item<ParsePathInfoOrGroupT>
    uint32_t inDegree;
    uint32_t outDegree;
    uint32_t inDegreeHelp;   // 检测是否有环时会被修改
    uint32_t outDegreeHelp;  // 检测是否有环时会被修改
    uint32_t idx;            // indicates the created order in path
} ParsePathInfoVertexT;

/*
 * 树状path所需的edge
 */
typedef struct {
    char *variable;
    DmEdgeTypeE type;
    uint32_t id;
    char *labelName;
    ParsePathInfoVertexT *srcPathInfoVertex;  // 源点，一般边/空边有效
    ParsePathInfoVertexT *dstPathInfoVertex;  // 目的节点，一般边有效
} ParsePathInfoEdgeT;

/*
 * 树状path
 */
typedef struct {
    DbListT *roots;  // list of ParsePathInfoVertexT*
} ParsePathPatternT;

typedef struct {
    char *vertexLabelName;
    char *propertyName;
} PidFieldT;

// CREATE PATH
typedef PidFieldT VertexLabelPropertyT;

typedef enum {
    SET_CLAUSE_PROPERTY,
    SET_CLAUSE_COALESCE_FUNC,
} SetClauseTagE;

typedef struct {
    VertexLabelPropertyT *coalesceLeftProp;
    VertexLabelPropertyT *coalesceRightProp;
} CoalesceClauseT;

typedef struct {
    SetClauseTagE tag;
    char *leftPropertyName;
    union {
        VertexLabelPropertyT *rightProp;
        CoalesceClauseT *coalesceClause;
    };
} SetClauseT;

typedef enum {
    COMPLEX_PATH_NORMAL,
    COMPLEX_PATH_MERGE,
    COMPLEX_PATH_REPLACE,
} ComplexPathTypeE;

typedef struct ParsePathCondition {
    char *vertexLabelName;
    char *fieldName;
    char *value;
} ParsePathConditionT;

typedef struct ParsePathConstraint {
    DbListT conditions;  // item<ParsePathConditionT>
} ParsePathConstraintT;

typedef struct {
    char *viewTableName;
    DbListT setClauses;  //  item<SetClauseT*>
    ComplexPathTypeE pathType;
} MergeReplacePathAttrT;

typedef struct {
    GqlStatementT base;
    char *pathName;
    uint32_t pathIdInApp;        // 保留，app定义的pathId
    ParsePathPatternT *pattern;  // 树状path，由PathInfoVertex构成
    SubscribeFieldT *subFields;  // info about send-forward data
    PidFieldT *pidField;         // info about pid
    uint32_t deletionMode;       // deletion mode for the path
    DmPathTlvE pathTlv;
    MergeReplacePathAttrT *mpPathAttr;  // merge path && replace path 涉及到的属性
    ParsePathConstraintT *constraint;
} ParseCreatePathStmtT;

// CREATE SUBSCRIPTION
typedef enum {
    SINGLE_PATH_SUB = 0,  // Single path subscription
    SR_ONESHOT_FAST,      // Smoothing
    SR_ONESHOT_SLOW,      // Reconciliation
} SubscriptionEventTypeE;

typedef struct {
    const char *tableName;  // table/vertex label name
    DbListT pathNames;      // item<PathNameT>
    DbListT conditions;     // item<ConditionT>
} SRTableSubscriptionT;

typedef struct {
    DmVertexDescT *vertexDesc;    // A pointer to the vertex label descriptor.
    DmPropertySchemaT *property;  // A pointer to the property.
    DmValueT value;               // A pointer to the conditioned value.
} SRTableSubCondAnalyzedT;

typedef struct SRTableSubAnalyzed {
    uint32_t tableId;     // table/vertex label id
    DbListT *pathIds;     // item<uint32_t>
    DbListT *conditions;  // item<SRTableSubCondAnalyzedT>
} SRTableSubAnalyzedT;

typedef struct {
    GqlStatementT base;
    char *subName;
    char *channelName;
    char *pathName;
    DbListT tableSubs;  // item<SRTableSubscriptionT>
    SubscriptionEventTypeE eventType;
    DbListT *events;  // item<DmSubsEventE>
} ParseCreateSubscriptionStmtT;

// DROP SUBSCRIPTION
typedef struct {
    GqlStatementT base;
    char *subName;
} ParseDropSubscriptionStmtT;

// DROP VERTEXLABEL
typedef struct {
    GqlStatementT base;
    const char *labelName;  // vertex label name
} ParseDropVertexLabelStmtT;

// DROP EDGE
typedef struct {
    GqlStatementT base;
    const char *labelName;  // edge label name
    bool isEdgePattern;     // edge label or edge pattern
} ParseDropEdgeLabelStmtT;

// DROP PATH
typedef struct {
    GqlStatementT base;
    const char *pathName;  // path name
} ParseDropPathStmtT;

// DROP PATH TRIGGER
typedef struct {
    GqlStatementT base;
    const char *triggerName;  // path trigger name
} ParseDropTriggerStmtT;

// CREATE TRIGGER
typedef enum { PATH_TRIG_OP_TRUE = 0, PATH_TRIG_OP_UPDATED_ANY } PathTrigCondOperationE;

typedef struct {
    const char *name;
} PathNameT;

typedef struct {
    const char *name;
} PathTrigPropertyNameT;

// base class
typedef struct {
    PathTrigCondOperationE tag;
} PathTrigCondT;

// Unconditional operation
typedef struct PathTrigCondTrue {
    PathTrigCondT op;  // base class
} PathTrigCondTrueT;

// Conditional operation
typedef struct {
    PathTrigCondT op;
    DbListT columns;  // item<PathTrigPropertyNameT>
} PathTrigCondUpdAnyT;

typedef struct {
    DbListT pathNames;  // item<PathNameT>
    PathTrigCondT *condition;
} TriggerActionT;

typedef struct {
    GqlStatementT base;
    const char *triggerName;  // trigger name
    DmPathTrigDmlE dmlType;
    const char *vertexLabelName;  // table name
    DbListT actions;              // item<TriggerActionT>
    bool isSendBeginEndTlv;       // whether to send begin/end tlv
    DbOamapT pathId2TlvMap;       // <pathId,tlvFormat>
    uint32_t noDataType;
} ParseCreateTriggerStmtT;

// SET SUBSCRIPTION_SLOWSPEED
typedef struct {
    GqlStatementT base;
    uint32_t slowRecordPerFetch;
    uint32_t slowTimeInterval;
} ParseSetSubSlowspeedStmtT;

#ifdef __cplusplus
}
#endif

#endif
