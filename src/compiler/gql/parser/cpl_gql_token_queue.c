/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementations for token queue of Gql
 * Author: GQL
 * Create: 2022-09-19
 */

#include "adpt_define.h"
#include "cpl_gql_parser.h"
#include "gql.tab.h"
#include "cpl_gql_token_queue.h"

extern int core_yylex(void);

static DbListT g_tokenQueue = (DbListT){};
static uint32_t g_head = 0;

Status GetNextToken(DbMemCtxT *memCtx, DbListT *queue, int *result)
{
    uint32_t cnt = DbListGetItemCnt(queue);
    if (cnt == g_head) {
        *result = core_yylex();
        Status ret = DbAppendListItem(queue, (void *)result);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        *result = *(int *)DbListItem(queue, g_head);
        g_head++;
    }
    return GMERR_OK;
}

Status IsBeforeIn(DbMemCtxT *memCtx, DbListT *queue, bool *flag)
{
    int result;
    Status ret = GetNextToken(memCtx, queue, &result);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (result == IN) {
        *flag = true;
    } else {
        *flag = false;
    }
    return GMERR_OK;
}

Status IsBeforeIdent(DbMemCtxT *memCtx, DbListT *queue, bool *flag)
{
    int result;
    Status ret = GetNextToken(memCtx, queue, &result);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (result == '(') {
        *flag = false;
    } else {
        *flag = true;
    }
    return GMERR_OK;
}

Status GetFromQueue(DbMemCtxT *memCtx, int *token)
{
    DB_POINTER2(memCtx, token);
    uint32_t cnt = DbListGetItemCnt(&g_tokenQueue);
    if (cnt == g_head) {
        *token = core_yylex();
    } else {
        *token = *(int *)DbListItem(&g_tokenQueue, g_head);
        g_head++;
    }

    bool flag;

    switch (*token) {
        case '(': {
            Status ret = IsBeforeIdent(memCtx, &g_tokenQueue, &flag);
            if (ret != GMERR_OK) {
                return ret;
            }
            if (flag == false) {
                *token = LP_BEFORE_GROUP;
                if (ret != GMERR_OK) {
                    return ret;
                }
            }
            break;
        }
        case NOT: {
            Status ret = IsBeforeIn(memCtx, &g_tokenQueue, &flag);
            if (ret != GMERR_OK) {
                return ret;
            }
            if (flag == true) {
                *token = NOT_IN;
            }
            g_head++;  // pop token IN in token queue
            break;
        }
        default:
            break;
    }
    return GMERR_OK;
}

void BuildQueue(DbMemCtxT *memCtx)
{
    DB_POINTER(memCtx);
    ParseCreateList(memCtx, &g_tokenQueue, sizeof(int));
    g_head = 0;
}
