/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Definition for token queue of Gql
 * Author: GQL
 * Create: 2022-09-19
 */

#ifndef CPL_GQL_TOKEN_QUEUE_H
#define CPL_GQL_TOKEN_QUEUE_H

#include "db_list.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Gql           get a token from token queue
 * @param memCtx        memory controller
 * @param token         store the result token
 * @return Status       status code
 */
Status GetFromQueue(DbMemCtxT *memCtx, int *token);

/**
 * @brief Gql           initialize token queue
 * @param memCtx        memory controller
 */
void BuildQueue(DbMemCtxT *memCtx);

#ifdef __cplusplus
}
#endif

#endif
