#其他依赖模块的头文件目录按需添加
include_directories(${GMDB_CJSON_INC_PATH})
include_directories(${GMDB_JANSSON_INC_PATH})
include_directories(${GMDB_OPENSSL_INC_PATH})
include_sub_directories_recursively(${CMAKE_CURRENT_SOURCE_DIR})
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/base SRC_CPL_BASE_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/public/parser SRC_PUBLIC_PARSER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/public/parser SRC_PUBLIC_SQL_PARSER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/public/verifier SRC_PUBLIC_VERIFIER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/public/analyzer SRC_PUBLIC_ANALYZER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fastpath SRC_FASTPATH_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fastpath/base SRC_FASTPATH_BASE_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fastpath/parser SRC_FASTPATH_PARSER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fastpath/planner SRC_FASTPATH_PLANNER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fastpath/utils SRC_FASTPATH_UTILS_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fastpath/verifier SRC_FASTPATH_VERIFIER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/ir SRC_IR_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/ir/explain SRC_IR_EXPLAIN_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/kv/parser SRC_KV_PARSER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/kv/verifier SRC_KV_VERIFIER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/yang/base SRC_YANG_BASE_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/yang/parser/fastpath SRC_YANG_FASTPATH_PARSER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/yang/parser/fusion SRC_YANG_FUSION_PARSER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/yang/verifier/fastpath SRC_YANG_FASTPATH_VERIFIER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/yang/verifier/fusion SRC_YANG_FUSION_VERIFIER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/yang/analyzer SRC_YANG_ANALYZER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/yang/common SRC_YANG_COMMON_LIST)
# cpl_public_sql前缀的文件为时序和sql共用的文件，放在public目录下，仅在需要的时候才进行编译
list(FILTER SRC_PUBLIC_PARSER_LIST EXCLUDE REGEX ".*cpl_public_sql/*")
list(FILTER SRC_PUBLIC_SQL_PARSER_LIST INCLUDE REGEX ".*cpl_public_sql/*")
if (TS_MULTI_INST)
    list(APPEND SRC_PUBLIC_PARSER_LIST ${CMAKE_CURRENT_SOURCE_DIR}/public/parser/cpl_public_sql_parser_common.c)
    list(APPEND SRC_PUBLIC_PARSER_LIST ${CMAKE_CURRENT_SOURCE_DIR}/public/parser/cpl_public_sql_expr.c)
endif()

if (NOT FEATURE_YANG)
    list(REMOVE_ITEM SRC_PUBLIC_PARSER_LIST "${CMAKE_CURRENT_SOURCE_DIR}/public/parser/cpl_public_parser_ddl_constraint.c")
    list(REMOVE_ITEM SRC_PUBLIC_PARSER_LIST "${CMAKE_CURRENT_SOURCE_DIR}/public/parser/cpl_public_w3c2posix_pattern.c")
    list(REMOVE_ITEM SRC_PUBLIC_SQL_PARSER_LIST "${CMAKE_CURRENT_SOURCE_DIR}/public/parser/cpl_public_parser_ddl_constraint.c")
    list(REMOVE_ITEM SRC_PUBLIC_SQL_PARSER_LIST "${CMAKE_CURRENT_SOURCE_DIR}/public/parser/cpl_public_w3c2posix_pattern.c")
    list(REMOVE_ITEM SRC_YANG_FASTPATH_PARSER_LIST "${CMAKE_CURRENT_SOURCE_DIR}/yang/parser/fastpath/cpl_yang_parser_subtree.c")
    list(REMOVE_ITEM SRC_YANG_FASTPATH_PARSER_LIST "${CMAKE_CURRENT_SOURCE_DIR}/yang/parser/fastpath/cpl_yang_parser_dml.c")
    list(REMOVE_ITEM SRC_YANG_FASTPATH_PARSER_LIST "${CMAKE_CURRENT_SOURCE_DIR}/yang/parser/fastpath/cpl_yang_parser_diff.c")
    list(REMOVE_ITEM SRC_YANG_FASTPATH_VERIFIER_LIST "${CMAKE_CURRENT_SOURCE_DIR}/yang/verifier/fastpath/cpl_yang_verifier_ddl.c")
    list(REMOVE_ITEM SRC_PUBLIC_VERIFIER_LIST "${CMAKE_CURRENT_SOURCE_DIR}/public/verifier/cpl_public_verifier_ddl_yang.c")
endif()

# add SQL source code
if (FEATURE_SQL)
    # 暂时忽略时序和sql共用的public部分，后续进行公共部分维护
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/sql/parser SRC_SQL_PARSER_LIST)
    list(APPEND SRC_SQL_PARSER_LIST ${SRC_PUBLIC_SQL_PARSER_LIST})
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/sql/analyzer SRC_SQL_ANALYZER_LIST)
    list(APPEND SRC_SQL_PARSER_LIST ${SRC_PUBLIC_ANALYZER_LIST})
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/sql/rewriter SRC_SQL_REWRITER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/sql/optimizer SRC_SQL_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer SRC_SQL_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/base SRC_SQL_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/fusion SRC_SQL_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/cost_model SRC_SQL_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/rule SRC_SQL_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/search_algo SRC_SQL_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/search_space SRC_SQL_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/rule/transformation SRC_SQL_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/rule/implementation SRC_SQL_OPTIMIZER_LIST)
endif()

if (NOT FEATURE_RESPOOL_SRV)
    list(REMOVE_ITEM SRC_PUBLIC_SQL_PARSER_LIST "${CMAKE_CURRENT_SOURCE_DIR}/public/parser/cpl_public_parser_ddl_res_pool.c")
    list(REMOVE_ITEM SRC_PUBLIC_PARSER_LIST "${CMAKE_CURRENT_SOURCE_DIR}/public/parser/cpl_public_parser_ddl_res_pool.c")
endif()

if (NOT FEATURE_EDGELABEL)
    list(REMOVE_ITEM SRC_PUBLIC_SQL_PARSER_LIST "${CMAKE_CURRENT_SOURCE_DIR}/public/parser/cpl_public_parser_ddl_graph.c")
    list(REMOVE_ITEM SRC_PUBLIC_PARSER_LIST "${CMAKE_CURRENT_SOURCE_DIR}/public/parser/cpl_public_parser_ddl_graph.c")
endif()

if (FEATURE_TS)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/ts/parser SRC_TS_PARSER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/ts/analyzer SRC_TS_ANALYZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/ts/analyzer/visitor SRC_TS_VISITOR_LIST)
    list(APPEND SRC_TS_ANALYZER_LIST ${SRC_PUBLIC_ANALYZER_LIST})
    list(APPEND SRC_TS_ANALYZER_LIST ${SRC_TS_VISITOR_LIST})
    if (NOT SINGLE_SO OR NOT FEATURE_SQL)
        list(APPEND SRC_TS_PARSER_LIST ${SRC_PUBLIC_SQL_PARSER_LIST})
    endif()
endif()

if (FEATURE_STREAM)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/stream/parser SRC_STREAM_PARSER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/stream/analyzer SRC_STREAM_ANALYZER_LIST)
    list(APPEND SRC_STREAM_ANALYZER_LIST ${SRC_PUBLIC_ANALYZER_LIST})
    if (NOT SINGLE_SO OR NOT FEATURE_SQL)
        list(APPEND SRC_STREAM_PARSER_LIST ${SRC_PUBLIC_SQL_PARSER_LIST})
    endif()
endif()

if (FEATURE_GQL)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/gql/parser SRC_GQL_PARSER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/gql/analyzer SRC_GQL_ANALYZER_LIST)
endif()

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer SRC_DTL_OPTIMIZER_LIST)
list(REMOVE_ITEM SRC_DTL_OPTIMIZER_LIST "${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/cpl_opt_optimizer_cascades.c")
if (FEATURE_TS OR FEATURE_SQL OR FEATURE_GQL OR FEATURE_STREAM)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer SRC_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/base SRC_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/fusion SRC_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/cost_model SRC_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/rule SRC_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/search_algo SRC_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/search_space SRC_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/rule/transformation SRC_OPTIMIZER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/rule/implementation SRC_OPTIMIZER_LIST)
if(NOT STREAM_MULTI_INST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/planner SRC_PLANNER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/planner/plangenerator SRC_PLANNER_LIST)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/planner/plancache SRC_PLANNER_LIST)
endif()
endif()
if (TS_MULTI_INST AND NOT FEATURE_STREAM)
    file(GLOB_RECURSE SRC_IMPLEMENTATION_STREAM_LIST "${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/rule/implementation/cpl_opt_rule_impl_stream*.*")
    file(GLOB_RECURSE SRC_PLANGENERATOR_STREAM_LIST "${CMAKE_CURRENT_SOURCE_DIR}/optimization/planner/plangenerator/cpl_opt_plangenerator_stream*.*")
    list(REMOVE_ITEM SRC_OPTIMIZER_LIST ${SRC_IMPLEMENTATION_STREAM_LIST})
    list(REMOVE_ITEM SRC_PLANNER_LIST ${SRC_PLANGENERATOR_STREAM_LIST})
endif()
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/yang SRC_YANG_OPTIMIZER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/optimizer/fusion SRC_FUSION_OPTIMIZER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/planner SRC_DTL_PLANNER_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/planner/plangenerator/fusion SRC_FUSION_PLANNER_PLANGENERATOR_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/planner/plangenerator SRC_DTL_PLANNER_PLANGENERATOR_LIST)
if (FEATURE_DATALOG)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/planner/plangenerator/datalog SRC_DTL_PLANNER_PLANGENERATOR_LIST)
endif()
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/planner/plangenerator/yang SRC_YANG_PLANNER_PLANGENERATOR_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/planner/plancache SRC_DTL_PLANNER_PLANCACHE_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/planner/plancache/yang SRC_YANG_PLANNER_PLANCACHE_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/optimization/planner/plancache/yang/serializer SRC_YANG_PLANNER_PLANCACHE_LIST)

# 添加datalog online源文件
set(SRC_ONLINE_DTL_PARSER ${CMAKE_CURRENT_SOURCE_DIR}/datalog/parser/cpl_dtl_online_parser.c)
set(SRC_ONLINE_DTL_VERIFY ${CMAKE_CURRENT_SOURCE_DIR}/datalog/verifier/cpl_dtl_online_verify.c)
set(SRC_CPL_DTL_INIT ${CMAKE_CURRENT_SOURCE_DIR}/datalog/base/cpl_dtl_init.c)

if(FEATURE_SIMPLEREL)
    set(SRC_COMPILER_LIST "")
else()
    # persistence文件夹下所有源文件编译进持久化特性
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/persistence SRC_PERSISTENCE_CPL_LIST)
    list(APPEND PERSISTENCE_SRCS ${SRC_PERSISTENCE_CPL_LIST})
    set(PERSISTENCE_SRCS ${PERSISTENCE_SRCS} PARENT_SCOPE)

    set(SRC_COMPILER_LIST
        ${SRC_CPL_BASE_LIST}
        ${SRC_PUBLIC_PARSER_LIST}
        ${SRC_PUBLIC_VERIFIER_LIST}
    )
endif()

set(SRC_INCLUDE_DIRECTORIES_LIST
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/include/datalog
    ${CMAKE_CURRENT_SOURCE_DIR}/include/fastpath
    ${CMAKE_CURRENT_SOURCE_DIR}/include/ir
    ${CMAKE_CURRENT_SOURCE_DIR}/include/kv
    ${CMAKE_CURRENT_SOURCE_DIR}/include/optimization
    ${CMAKE_CURRENT_SOURCE_DIR}/include/public
    ${CMAKE_CURRENT_SOURCE_DIR}/include/yang)

if (FEATURE_TS)
    list(APPEND SRC_INCLUDE_DIRECTORIES_LIST ${CMAKE_CURRENT_SOURCE_DIR}/include/ts)
endif()

if (FEATURE_STREAM)
    list(APPEND SRC_INCLUDE_DIRECTORIES_LIST ${CMAKE_CURRENT_SOURCE_DIR}/include/stream)
endif()

if (FEATURE_SQL)
    list(APPEND SRC_INCLUDE_DIRECTORIES_LIST ${CMAKE_CURRENT_SOURCE_DIR}/include/sql)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/persistence SRC_PERSISTENCE_CPL_LIST)
    list(APPEND PERSISTENCE_SRCS ${SRC_PERSISTENCE_CPL_LIST})
    set(PERSISTENCE_SRCS ${PERSISTENCE_SRCS} PARENT_SCOPE)
 
    list(FILTER SRC_PUBLIC_PARSER_LIST EXCLUDE REGEX ".*cpl_public_parser_d[cq]l*")  # 系统表，需要ddl解析
 
    list(APPEND SRC_COMPILER_LIST
        ${SRC_CPL_BASE_LIST}
        ${SRC_PUBLIC_PARSER_LIST}  # 系统表
    )
endif()

if (FEATURE_GQL)
    list(APPEND SRC_INCLUDE_DIRECTORIES_LIST ${CMAKE_CURRENT_SOURCE_DIR}/include/gql)
endif()

# 将fastpath compiler加到fastpath特性组件
set(FASTPATH_SRCS ${FASTPATH_SRCS}
    ${SRC_FASTPATH_LIST}
    ${SRC_FASTPATH_BASE_LIST}
    ${SRC_FASTPATH_PARSER_LIST}
    ${SRC_FASTPATH_PLANNER_LIST}
    ${SRC_FASTPATH_UTILS_LIST}
    ${SRC_FASTPATH_VERIFIER_LIST}
    ${SRC_KV_PARSER_LIST}
    ${SRC_KV_VERIFIER_LIST}
    ${SRC_YANG_FASTPATH_PARSER_LIST}
    ${SRC_YANG_FASTPATH_VERIFIER_LIST}
PARENT_SCOPE)

# 将yang compiler加到YANG特性组件
set(YANG_SRCS ${YANG_SRCS}
    ${SRC_YANG_BASE_LIST}
    ${SRC_YANG_FUSION_PARSER_LIST}
    ${SRC_YANG_FUSION_VERIFIER_LIST}
    ${SRC_YANG_ANALYZER_LIST}
    ${SRC_YANG_COMMON_LIST}
    ${SRC_YANG_PLANNER_PLANGENERATOR_LIST}
    ${SRC_YANG_PLANNER_PLANCACHE_LIST}
    ${SRC_YANG_OPTIMIZER_LIST}
PARENT_SCOPE)

set(FUSION_SRCS ${FUSION_SRCS}
    ${SRC_IR_LIST}
    ${SRC_IR_EXPLAIN_LIST}
    ${SRC_FUSION_PLANNER_PLANGENERATOR_LIST}
    ${SRC_FUSION_OPTIMIZER_LIST}
PARENT_SCOPE)

# 用于datalog离线工具编译静态库依赖 不发布 RTOSV2
if(NOT RTOSV2 AND (FEATURE_DATALOG OR PNFHOSTTOOLS))
    ADD_SUBDIRECTORY(datalog)
endif()

# 将datalog compiler加到datalog特性组件
set(DATALOG_SRCS ${DATALOG_SRCS}
    ${SRC_ONLINE_DTL_PARSER}
    ${SRC_ONLINE_DTL_VERIFY}
    ${SRC_CPL_DTL_INIT}
    ${SRC_CPL_DTL_COMPILER_UTILS}
    ${SRC_DTL_OPTIMIZER_LIST}
    ${SRC_DTL_PLANNER_LIST}
    ${SRC_DTL_PLANNER_PLANGENERATOR_LIST}
    ${SRC_DTL_PLANNER_PLANCACHE_LIST}
PARENT_SCOPE)

# 将ts compiler加到ts特性组件
if (FEATURE_TS)
    set(TS_SRCS ${TS_SRCS}
        ${SRC_TS_PARSER_LIST}
        ${SRC_TS_ANALYZER_LIST}
        ${SRC_OPTIMIZER_LIST}
        ${SRC_PLANNER_LIST}
    PARENT_SCOPE)
endif()

# 将stream compiler加到stream特性组件
if (FEATURE_STREAM)
    set(STREAM_SRCS ${STREAM_SRCS}
        ${SRC_STREAM_PARSER_LIST}
        ${SRC_STREAM_ANALYZER_LIST}
        ${SRC_OPTIMIZER_LIST}
        ${SRC_PLANNER_LIST}
    PARENT_SCOPE)
endif()

# 将gql compiler加到gql特性组件
if (FEATURE_GQL)
    set(GQL_SRCS ${GQL_SRCS}
        ${SRC_GQL_PARSER_LIST}
        ${SRC_GQL_ANALYZER_LIST}
        ${SRC_OPTIMIZER_LIST}
        ${SRC_PLANNER_LIST}
    PARENT_SCOPE)
endif()

# 将sql compiler加到sql特性组件
if (FEATURE_SQL)
    set(SQL_SRCS ${SQL_SRCS}
        ${SRC_SQL_PARSER_LIST}
        ${SRC_SQL_ANALYZER_LIST}
        ${SRC_SQL_REWRITER_LIST}
        ${SRC_SQL_OPTIMIZER_LIST}
        ${SRC_OPTIMIZER_LIST}
        ${SRC_PLANNER_LIST}
    PARENT_SCOPE)
endif()

if(MODULARBUILD)
    set(MODULE_NAME gmcompiler)
    add_library(${MODULE_NAME} SHARED ${SRC_COMPILER_LIST})
    target_include_directories(${MODULE_NAME} INTERFACE ${SRC_INCLUDE_DIRECTORIES_LIST})
    set_target_properties(${MODULE_NAME} PROPERTIES VERSION ${GMDB_PROJECT_VERSION} SOVERSION ${GMDB_MAJOR_VERSION})
    target_link_libraries(${MODULE_NAME} gmcommon gmruntime gmdatamodel gmexecutor)
    if(STRIP)
        separate_debug_info(${MODULE_NAME})
    endif()
    install(TARGETS ${MODULE_NAME} LIBRARY DESTINATION lib)
else()
    list(APPEND LIBRARIES_LIST ${SRC_COMPILER_LIST})
    list(APPEND INCLUDE_DIRECTORIES_LIST ${SRC_INCLUDE_DIRECTORIES_LIST})
    set(LIBRARIES_LIST ${LIBRARIES_LIST} PARENT_SCOPE)
    set(INCLUDE_DIRECTORIES_LIST ${INCLUDE_DIRECTORIES_LIST} PARENT_SCOPE)
endif()
