/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Internal specific implementation of time-series parser
 * Author: hujing lihainuo
 * Create: 2023-02-27
 */

#include "db_json.h"
#include "db_utils.h"
#include "adpt_string.h"
#include "cpl_public_sql_parser_common.h"
#include "cpl_ts_compiler.h"
#include "cpl_ts_build_op.h"
#include "cpl_public_verifier_utils.h"
#include "ee_systbl.h"

extern int ts_parse(void);
extern void *ts__scan_string(const char *);
extern int ts_lex_destroy(void);

#define TS_NONAGG_FUNC_NAME_MAX_LEN 256

typedef struct TsSupportFunc {
    bool isSupport;
    char funcName[TS_NONAGG_FUNC_NAME_MAX_LEN];
} TsSupportFuncT;

static TsSupportFuncT GetTsSupportFunc(SqlExprFuncTypeE funcType)
{
    DB_ASSERT(funcType != FUNC_TYPE_END);
    TsSupportFuncT supportedType[] = {
        [FUNC_TYPE_COUNT] = {true, "count"},
        [FUNC_TYPE_MIN] = {true, "min"},
        [FUNC_TYPE_MAX] = {true, "max"},
        [FUNC_TYPE_SUM] = {true, "sum"},
        [FUNC_TYPE_FIRST] = {true, "first"},
        [FUNC_TYPE_LAST] = {true, "last"},
        [FUNC_TYPE_COLSET] = {true, "colset"},
        [FUNC_TYPE_AVG] = {true, "avg"},
        [FUNC_TYPE_AGING] = {false, "tsdb_aging"},
        [FUNC_TYPE_DATE] = {false, "date"},
        [FUNC_TYPE_TIME] = {false, "time"},
        [FUNC_TYPE_DATETIME] = {false, "datetime"},
        [FUNC_TYPE_JULIANDAY] = {false, "julianday"},
        [FUNC_TYPE_STRFTIME] = {false, "strftime"},
        [FUNC_TYPE_RANDOM] = {false, "random"},
        [FUNC_TYPE_ABS] = {false, "abs"},
        [FUNC_TYPE_UPPER] = {false, "upper"},
        [FUNC_TYPE_LOWER] = {false, "lower"},
        [FUNC_TYPE_LENGTH] = {true, "length"},
        [FUNC_TYPE_SQLVERSION] = {false, "sqlversion"},
        [FUNC_TYPE_SUBSTR] = {false, "substr"},
    };

    return supportedType[funcType];
}

static void ExtractContextInParentheses(char *curName, char **parenthesesCon)
{
    uint32_t leftIndex = 0;
    uint32_t rightIndex = (uint32_t)strlen(curName) - 1;
    uint32_t prnCnt = 0;
    bool isFirstPrn = true;
    // find () pairs
    for (uint32_t i = 0; i <= rightIndex; i++) {
        if (curName[i] == '(' && isFirstPrn) {
            leftIndex = i;
            prnCnt++;
            isFirstPrn = false;
            continue;
        }
        if (curName[i] == '(') {
            prnCnt++;
            continue;
        }
        if (curName[i] == ')') {
            prnCnt--;
            if (prnCnt == 0) {
                rightIndex = i;
                break;
            }
        }
    }
    curName[rightIndex + 1] = '\0';
    *parenthesesCon = &curName[leftIndex];
    return;
}

static Status TsCheckIsExecFuncStmt(DbMemCtxT *memCtx, SqlSelectStmtT *selectStmt)
{
    uint32_t tgtCnt = DbListGetItemCnt(selectStmt->targetList);
    for (uint32_t i = 0; i < tgtCnt; i++) {
        SqlTargetT *tgt = *(SqlTargetT **)DbListItem(selectStmt->targetList, 0);
        if (tgt->value->op->type != SQL_EXPR_OP_FUN) {
            continue;
        }
        SqlExprFuncT *func = CastToSqlExprFunc(tgt->value->op);
        if (func->funcType == FUNC_TYPE_AGING) {
            // 防止出现在insert into/coppy to等语句中
            if (selectStmt->tag != T_TS_SELECT_STMT) {
                DB_LOG_ERROR(GMERR_SEMANTIC_ERROR, "aging not in simple select");
                return GMERR_SEMANTIC_ERROR;
            }
            selectStmt->tag = T_TS_EXEC_INTERNAL_FUNC_STMT;
            break;
        }
    }
    return GMERR_OK;
}

static Status MakeupFuncName4FuncExpr(
    DbMemCtxT *memCtx, TsSupportFuncT *supportedFunc, char *parenthesesCon, SqlExprFuncT *funcExpr)
{
    uint32_t funcNameLen = (uint32_t)strlen(supportedFunc->funcName);
    uint32_t funcNameTotalLen = DM_STR_LEN(parenthesesCon) + funcNameLen;

    // 资源统一释放
    funcExpr->aggFuncName = (char *)DbDynMemCtxAlloc(memCtx, sizeof(char) * funcNameTotalLen);
    if (funcExpr->aggFuncName == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "alloc agg func name from target");
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t errTgt = strcpy_s(funcExpr->aggFuncName, funcNameLen + 1, supportedFunc->funcName);
    if (errTgt != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "agg func name len");
        return GMERR_FIELD_OVERFLOW;
    }
    errTgt = strcpy_s(&funcExpr->aggFuncName[funcNameLen], DM_STR_LEN(parenthesesCon), parenthesesCon);
    if (errTgt != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "extracted target agg func name len");
        return GMERR_FIELD_OVERFLOW;
    }

    return GMERR_OK;
}

static Status ExtractFuncNameFromTargetList(DbMemCtxT *memCtx, char *curName, SqlTargetT *tgt)
{
    char *parenthesesCon = NULL;
    ExtractContextInParentheses(curName, &parenthesesCon);
    SqlExprFuncT *func = CastToSqlExprFunc(tgt->value->op);
    TsSupportFuncT supportedFunc = GetTsSupportFunc(func->funcType);
    if (!supportedFunc.isSupport) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "funcType:%" PRIu32 ", func name:%s in target.",
            (uint32_t)func->funcType, supportedFunc.funcName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    Status ret = MakeupFuncName4FuncExpr(memCtx, &supportedFunc, parenthesesCon, func);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Makeup func name.");
        return ret;
    }
    return ret;
}

static Status ExtractAddNameFromTargetList(DbMemCtxT *memCtx, char *curName, SqlTargetT *tgt)
{
    uint32_t size = DM_STR_LEN(curName);
    // 资源统一释放
    tgt->outputStr = (char *)DbDynMemCtxAlloc(memCtx, size);
    if (tgt->outputStr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "alloc add name from target");
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t errTgt = strcpy_s(tgt->outputStr, size, curName);
    if (errTgt != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "add name len");
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

static Status ExtractNameFromTargetList(DbMemCtxT *memCtx, char *tempCmd, char **result, SqlSelectStmtT *selectStmt)
{
    if (selectStmt->fromClause == NULL || DbListGetItemCnt(selectStmt->fromClause) == 0) {
        // 如果 select语句不带from,则看看是否是调用自定义函数
        return TsCheckIsExecFuncStmt(memCtx, selectStmt);
    }
    char *nextToken = NULL;
    char *targetEnd = strstr(tempCmd, " from ");
    if (targetEnd == NULL) {
        // extract func names from target list with problem because of illegal from clause
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SYNTAX_ERROR, "Find no blank spaces near \" from \" when extract name.");
        return GMERR_SYNTAX_ERROR;
    }
    targetEnd[0] = '\0';  // end for target list
    char *targetStart = strstr(tempCmd, "select ");
    char *token = strtok_s(targetStart, " ", &nextToken);  // begin for target list, skip "select"
    uint32_t tgtCnt = DbListGetItemCnt(selectStmt->targetList);
    if (token == NULL || DbStrSplit(nextToken, ",", SQL_COL_NUM_MAX, result) != tgtCnt) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SYNTAX_ERROR, "Extract func names from target list");
        return GMERR_SYNTAX_ERROR;
    }
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < tgtCnt; i++) {  // extract funcs' original names one by one
        SqlTargetT *tgt = *(SqlTargetT **)DbListItem(selectStmt->targetList, i);
        if (tgt->value->op->type == SQL_EXPR_OP_FUN) {
            char *curName = result[i];
            ret = ExtractFuncNameFromTargetList(memCtx, curName, tgt);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "Extract func name");
                return ret;
            }
        }
        if (tgt->value->op->type == SQL_EXPR_OP_ADD) {
            char *curName = result[i];
            ret = ExtractAddNameFromTargetList(memCtx, curName, tgt);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "Extract add name");
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static Status ExtractNameFromOrderByList(DbMemCtxT *memCtx, char *tempCmd, char **result, SqlSelectStmtT *selectStmt)
{
    char *nextToken = NULL;
    char *orderPos = strstr(tempCmd, " order ");
    if (orderPos == NULL) {
        goto ERROR;
    }
    char *byPos = strstr(orderPos, " by ");
    if (byPos == NULL) {
        goto ERROR;
    }
    char *limitPos = strstr(orderPos, " limit ");
    if (limitPos != NULL) {
        limitPos[0] = '\0';
    }
    char *token = strtok_s(byPos, " ", &nextToken);  // begin for order by list, skip "by"
    uint32_t orderByCnt = DbListGetItemCnt(selectStmt->orderBy);
    uint32_t nextTokenSplitCnt = DbStrSplit(nextToken, ",", SQL_COL_NUM_MAX, result);
    if (token == NULL || nextTokenSplitCnt != orderByCnt) {
        goto ERROR;
    }
    for (uint32_t i = 0; i < orderByCnt; i++) {  // extract funcs' original names one by one
        SqlOrderByT *orderByItem = *(SqlOrderByT **)DbListItem(selectStmt->orderBy, i);
        if (orderByItem->sortExpr->op->type == SQL_EXPR_OP_FUN) {
            char *curName = result[i];
            char *parenthesesCon = NULL;
            ExtractContextInParentheses(curName, &parenthesesCon);
            SqlExprFuncT *func = CastToSqlExprFunc(orderByItem->sortExpr->op);
            TsSupportFuncT supportedFunc = GetTsSupportFunc(func->funcType);
            if (!supportedFunc.isSupport) {
                DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED,
                    "funcType:%" PRIu32 ", func name:%s in order by.", (uint32_t)func->funcType,
                    supportedFunc.funcName);
                return GMERR_FEATURE_NOT_SUPPORTED;
            }

            SqlExprFuncT *funcExpr = CastToSqlExprFunc(orderByItem->sortExpr->op);
            Status ret = MakeupFuncName4FuncExpr(memCtx, &supportedFunc, parenthesesCon, funcExpr);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;

ERROR:
    DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SYNTAX_ERROR, "extract agg func names from order by");
    return GMERR_SYNTAX_ERROR;
}

static Status CopyOriginalCmd(char *tempCmd, const char *cmd)
{
    errno_t err = strcpy_s(tempCmd, TS_SQL_CMD_LEN_MAX, cmd);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "extracted agg func name len");
        return GMERR_FIELD_OVERFLOW;
    }
    DbStrToLower(tempCmd);
    return GMERR_OK;
}

static Status TsAggFuncNameExtract(DbMemCtxT *memCtx, const char *cmd, SqlSelectStmtT *selectStmt)
{
    DB_POINTER3(memCtx, cmd, selectStmt);
    char *result[SQL_COL_NUM_MAX] = {0};
    char *tempCmd = (char *)DbDynMemCtxAlloc(memCtx, TS_SQL_CMD_LEN_MAX);
    if (tempCmd == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc tempCmd");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(tempCmd, TS_SQL_CMD_LEN_MAX, 0, TS_SQL_CMD_LEN_MAX);
    // target list
    Status ret = CopyOriginalCmd(tempCmd, cmd);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = ExtractNameFromTargetList(memCtx, tempCmd, result, selectStmt);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    // order by list
    if (selectStmt->orderBy == NULL) {
        ret = GMERR_OK;
        goto EXIT;
    }
    (void)memset_s(&result[0], SQL_COL_NUM_MAX * sizeof(char *), 0, SQL_COL_NUM_MAX * sizeof(char *));
    (void)memset_s(&tempCmd[0], TS_SQL_CMD_LEN_MAX, 0, TS_SQL_CMD_LEN_MAX);
    ret = CopyOriginalCmd(tempCmd, cmd);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = ExtractNameFromOrderByList(memCtx, tempCmd, result, selectStmt);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
EXIT:
    DbDynMemCtxFree(memCtx, tempCmd);
    return ret;
}

static void TsParseSpecialLabel(SqlSelectStmtT *selectStmt)
{
    DB_POINTER(selectStmt);
    DbListT *fromClause = selectStmt->fromClause;
    if (fromClause == NULL) {
        return;
    }
    SqlSrcItemT *src = *(SqlSrcItemT **)DbListItem(fromClause, 0);
    selectStmt->isSvLabel = QryCheckLabelIsSysview(src->srcTable->tableName, strlen(src->srcTable->tableName));
    selectStmt->isSysTable = QryCheckLabelIsSysTable(src->srcTable->tableName, strlen(src->srcTable->tableName));
}

static Status InputSqlCmdSpecialProcess(DbMemCtxT *memCtx, NodeT *parsedStmt, const char *input)
{
    Status ret = GMERR_OK;
    switch (parsedStmt->tag) {
        case T_TS_SELECT_STMT: {
            SqlSelectStmtT *selectStmt = (SqlSelectStmtT *)parsedStmt;
            TsParseSpecialLabel(selectStmt);
            ret = TsAggFuncNameExtract(memCtx, input, selectStmt);  // when execute DQL, extract func's original name
            break;
        }
        case T_TS_COPY_TO_STMT: {
            SqlCopyToStmtT *copyToStmt = (SqlCopyToStmtT *)parsedStmt;
            SqlSelectStmtT *selectStmt = (SqlSelectStmtT *)copyToStmt->queryStmt;
            ret = TsAggFuncNameExtract(memCtx, input, selectStmt);  // when execute DQL, extract func's original name
            break;
        }
        case T_SQL_INSERT_STMT: {
            SqlInsertStmtT *insertStmt = (SqlInsertStmtT *)parsedStmt;
            SqlSelectStmtT *selectStmt = (SqlSelectStmtT *)insertStmt->value->selectSubquery;
            ret = TsAggFuncNameExtract(memCtx, input, selectStmt);  // when execute DQL, extract func's original name
            break;
        }
        default:
            return ret;
    }
    return ret;
}

Status TsSqlParse(DbMemCtxT *memCtx, const char *input, SqlParsedListT *parsedList)
{
    DB_POINTER3(memCtx, input, parsedList);

    SqlParseLock();
    SqlParsedListT *pList = SqlGetParseList();
    (void)memset_s(pList, sizeof(SqlParsedListT), 0, sizeof(SqlParsedListT));
    pList->memCtx = memCtx;
    pList->indexMap = parsedList->indexMap;
    pList->nameMap = parsedList->nameMap;
    pList->maxIndex = parsedList->maxIndex;
    pList->paraList = parsedList->paraList;

    Status ret = setjmp(pList->sqlBuf);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    /* parse */
    (void)ts__scan_string(input);  // get input
    (void)ts_parse();              // use flex and bison to parse

    if (SqlGetParseErrorCnt() != 0 || DbListGetItemCnt(&pList->cmdList) == 0) {
        ret = GMERR_SYNTAX_ERROR;
        goto EXIT;
    } else {
        // no long jump happened; parser success
        *parsedList = *pList;
    }

    // ts compiler only support parsing single cmd, the number of command will be verified in service, not here
    NodeT *parsedStmt = *(NodeT **)DbListItem(&pList->cmdList, 0);
    ret = InputSqlCmdSpecialProcess(memCtx, parsedStmt, input);

EXIT:
    (void)ts_lex_destroy();  // release memory of flex
    SqlParseClear();
    SqlParseUnlock();
    DbAdptMallocTrim(0);
    return ret;
}
