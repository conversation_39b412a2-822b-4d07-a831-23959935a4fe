/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: for TS drop analyzer
 * Author: lihainuo
 * Create: 2023-07-19
 */

#ifndef CPL_TS_ANALYZER_DROP_H
#define CPL_TS_ANALYZER_DROP_H

#include "ee_plan_node_ddl.h"
#include "cpl_ts_analyzer_common.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 实现删除语句的analyze功能,构建IRTREE
 * @param session 会话对象
 * @param parsedStmt parse传入analyze的结构体
 * @param irStmt IR plan tree.
 * @return Status 成功或错误码
 */
Status TsSqlAnalyzeDropTable(SessionT *session, NodeT *parsedStmt, SqlIrStmtT *irStmt);

#ifdef __cplusplus
}
#endif

#endif
