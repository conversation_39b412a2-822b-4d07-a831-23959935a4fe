/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: for TS alter table analyzer
 * Author: lihainuo
 * Create: 2024-04-03
 */

#include "adpt_string.h"
#include "cpl_ts_analyzer_create_table.h"
#include "cpl_ts_build_op.h"
#include "dm_data_record.h"
#include "cpl_ts_analyzer_alter_table.h"

Status TsSqlVerifyAddColumn(SqlAlterTableStmtT *parsedStmt);

Status TsSqlVerifySetFeature(SqlAlterTableStmtT *parsedStmt);

typedef struct TsAlterTableMethod {
    Status (*tsVerifyMethod)(SqlAlterTableStmtT *parsedStmt);
} TsAlterTableMethodT;

static TsAlterTableMethodT g_alterTableMethod[] = {[T_RENAME_TABLE] = {NULL},
    [T_RENAME_COLUMN] = {NULL},
    [T_ADD_COLUMN] = {TsSqlVerifyAddColumn},
    [T_DDL_DROP_COLUMN] = {NULL},
    [T_SET_FEATURE] = {TsSqlVerifySetFeature}};

typedef struct TsAlterSetItemMember {
    char *name;
    Status (*tsSetLabelMethod)(const char *value, DmVertexLabelT *label);
} TsAlterSetItemMemberT;

static Status TsSetLabelByDiskLimit(const char *value, DmVertexLabelT *label);
static Status TsSetLabelByTTL(const char *value, DmVertexLabelT *label);

static TsAlterSetItemMemberT g_tsAlterSetItemMember[] = {
    {"disk_limit", TsSetLabelByDiskLimit},
    {"ttl", TsSetLabelByTTL},
};

static Status TsSetLabelByDiskLimit(const char *value, DmVertexLabelT *label)
{
    DmTsInfoT *tsInfo = (DmTsInfoT *)label->metaVertexLabel->extraInfo.data;
    if (DbStrCmp(value, "0", false) == 0) {  // 设置为0时，特殊处理，表示不设限制
        // logical label maxSize must be 0, mem table diskLimit should be overwritten by maxSize
        tsInfo->diskLimit = (int64_t)tsInfo->maxSize;
        tsInfo->useDiskLimit = false;
        return GMERR_OK;
    }
    Status ret = TsParseToken2DiskLimit(value, &((DmTsInfoT *)label->metaVertexLabel->extraInfo.data)->diskLimit);
    if (ret != GMERR_OK || ((DmTsInfoT *)label->metaVertexLabel->extraInfo.data)->labelType == LOGIC_LABEL) {
        return ret;
    }

    ret = TsCalDiskLimitForMemTable(label);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

static Status TsSetLabelByTTL(const char *value, DmVertexLabelT *label)
{
    // 清空原数据
    ((DmTsInfoT *)label->metaVertexLabel->extraInfo.data)->ttl.microsecond = 0;
    ((DmTsInfoT *)label->metaVertexLabel->extraInfo.data)->ttl.day = 0;
    ((DmTsInfoT *)label->metaVertexLabel->extraInfo.data)->ttl.month = 0;

    if (DbStrCmp(value, "0", false) == 0) {  // 设置为0时，特殊处理，表示不设限制
        return GMERR_OK;
    }

    Status ret = TsParseToken2Interval(value, &((DmTsInfoT *)label->metaVertexLabel->extraInfo.data)->ttl);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to parse ttl from token during changing ttl, token: %s", value);
        return ret;
    }
    ret = TsCheckTTLConfig((DmTsInfoT *)label->metaVertexLabel->extraInfo.data);
    if (ret == GMERR_INVALID_JSON_CONTENT) {
        ret = GMERR_INVALID_PARAMETER_VALUE;
        DB_LOG_ERROR(ret, "new ttl violates rule, token: %s", value);
    }
    return ret;
}

static Status AlterVertexLabelBySetItem(DmVertexLabelT *label, SqlSetItemT *setItem, bool *hitArray, uint32_t memberNum)
{
    char *key = setItem->key;
    DbStrToLower(key);
    for (uint32_t i = 0; i < memberNum; i++) {
        if (DbStrCmp(key, g_tsAlterSetItemMember[i].name, false) == 0) {
            if (hitArray[i] == true) {
                DB_LOG_ERROR(GMERR_DUPLICATE_OBJECT, "set item: %s", key);
                return GMERR_DUPLICATE_OBJECT;
            }
            hitArray[i] = true;
            return g_tsAlterSetItemMember[i].tsSetLabelMethod(setItem->value, label);
        }
    }
    DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "set item feature: %s", key);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status TsSqlVerifySetFeature(SqlAlterTableStmtT *parsedStmt)
{
    // set feature 实际的 verify 在tsSetLabelMethod中实现
    DB_POINTER(parsedStmt);
    return GMERR_OK;
}

Status TsSqlVerifyAddColumn(SqlAlterTableStmtT *parsedStmt)
{
    Status ret = GMERR_OK;
    // 仅支持逻辑表
    if (parsedStmt->vertexLabel->metaVertexLabel->vertexLabelType != VERTEX_TYPE_TS_LOGICAL) {
        ret = GMERR_FEATURE_NOT_SUPPORTED;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "add column for mem ts label.");
        return ret;
    }
    // 检查列名是否超长
    char *colName = parsedStmt->column->colName;
    if (TsSqlCheckNameLength(colName)) {
        DB_LOG_ERROR(GMERR_NAME_TOO_LONG, "Adding col: %s", colName);
        return GMERR_NAME_TOO_LONG;
    }

    // 检查新增列类型是否合法
    ret = TsSqlVerifyOneColType(parsedStmt->column, parsedStmt->tableName->tableName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Adding col:%s type.", colName);
        return ret;
    }
    // 新增列不能为索引或者主键列
    DbListT *constraints = parsedStmt->column->constraints;
    if (constraints != NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "%s:support cons.", parsedStmt->tableName->tableName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    return GMERR_OK;
}

static Status TsSqlGetAndVerifyAlterTable(SessionT *session, SqlAlterTableStmtT *parsedStmt, SqlIrStmtT *irStmt)
{
    char *tableName = parsedStmt->tableName->tableName;
    if (TsSqlCheckNameLength(tableName)) {
        DB_LOG_ERROR(GMERR_NAME_TOO_LONG, "Altering ts table: %s.", tableName);
        return GMERR_NAME_TOO_LONG;
    }
    char *nspName = parsedStmt->tableName->database;
    DbStrToLower(tableName);
    Status ret = TsSqlVerifyDBAndGetLabel(session, nspName, tableName, &parsedStmt->vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get nsp id by name(%s) when verify.", nspName);
        return ret;
    }
    if (parsedStmt->vertexLabel == NULL) {
        if (parsedStmt->ifExists) {
            DB_LOG_INFO("Ts tbl-%s not exist when verify", tableName);
            return GMERR_UNDEFINED_TABLE;
        }
        DB_LOG_ERROR(GMERR_UNDEFINED_TABLE, "Ts tbl-%s when verify", tableName);
        return GMERR_UNDEFINED_TABLE;
    }
    ret = DbAppendListItem(irStmt->labelList, &parsedStmt->vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Append tbl to labelList in ts analyzer.");
        goto ERROR;
    }
    return ret;
ERROR:
    (void)CataReleaseVertexLabel(parsedStmt->vertexLabel);
    return ret;
}

Status TsSqlVerifyAlterTable(SessionT *session, DbMemCtxT *memCtx, SqlAlterTableStmtT *parsedStmt, SqlIrStmtT *irStmt)
{
    Status ret = TsSqlGetAndVerifyAlterTable(session, parsedStmt, irStmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 异常流程在TsExecSql执行完统一释放，正常流程在ProcessDdlCmd执行完释放
    ret = AcquireLabelWLatchFromList(irStmt->labelList, irStmt->latchList);
    if (ret != GMERR_OK) {
        return ret;
    }
    parsedStmt->vertexLabel = *(DmVertexLabelT **)DbListItem(irStmt->labelList, 0);  // 获取最新的label
    TsAlterTableMethodT *handle = &g_alterTableMethod[parsedStmt->type];
    if (SECUREC_UNLIKELY(handle == NULL || handle->tsVerifyMethod == NULL)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "unregister alter : %u", parsedStmt->type);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    ret = handle->tsVerifyMethod(parsedStmt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Verify altering method.");
        return ret;
    }
    return ret;
}

static Status TsInitOneProp(DbMemCtxT *memCtx, DmPropertySchemaT *properties, SqlColumnDefT *column, uint32_t propeId)
{
    properties[propeId].nameLen = (uint16_t)DM_STR_LEN(column->colName);
    properties[propeId].name = (char *)DbDynMemCtxAlloc(memCtx, properties[propeId].nameLen);
    if (properties[propeId].name == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memcpy_s(properties[propeId].name, properties[propeId].nameLen, column->colName, properties[propeId].nameLen);
    properties[propeId].propeId = propeId;  // begin with 0
    properties[propeId].isValid = true;
    properties[propeId].isNullable = true;
    properties[propeId].isResource = false;
    properties[propeId].isSysPrope = false;
    properties[propeId].dataType = column->standardType;
    properties[propeId].isFixed = DmIsFixedType(column->standardType);
    if (properties[propeId].dataType == DB_DATATYPE_FIXED) {
        properties[propeId].size = column->charLen;
    } else if (properties[propeId].isFixed) {
        properties[propeId].size = DmGetBasicDataTypeLength(column->standardType);
    } else if (properties[propeId].dataType == DB_DATATYPE_STRING && column->typeArg > 0) {
        properties[propeId].size = column->typeArg;
    } else {
        if (properties[propeId].dataType == DB_DATATYPE_STRING || column->charLen == 0) {
            properties[propeId].size = TS_CHAR_MAX_LEN;
        } else {
            properties[propeId].size = column->charLen;
        }
    }
    if (column->columnCompressMode == COMPRESS_NONE) {
        DmSchemaSetTsNoCompress(&properties[propeId]);
    }
    if (DbStrCmp(column->typeName, TS_IP_TYPE, true) == 0) {  // 标记是否为ip类型字段
        properties[propeId].isInet = true;
    }
    return GMERR_OK;
}

static Status AssignPropAndCopiedVertexLabels(
    DbMemCtxT *memCtx, DmVertexLabelT *oldVertexLabel, DmPropertySchemaT *tmpProperties, AlterTableStmtT *node)
{
    DmVertexLabelT *oldCopiedVertexLabel = NULL;
    // 对原始lable进行拷贝，避免在ee层访问其成员的时候因原始label被释放而导致不可预期的情况出现
    Status ret = DmCreateEmptyVertexLabelWithMemCtx(memCtx, &oldCopiedVertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Create old vertexLabel for alter.");
        return ret;
    }
    ret = DmCopyVertexLabel(oldVertexLabel, oldCopiedVertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Copy vertexLabel when altering.");
        return ret;
    }
    node->column = tmpProperties;
    node->oldVertexLabel = oldCopiedVertexLabel;
    return GMERR_OK;
}

static Status AlterAddOp(
    DbMemCtxT *memCtx, SqlAlterTableStmtT *parsedStmt, DmVertexLabelT *oldVertexLabel, AlterTableStmtT *node)
{
    DB_POINTER2(oldVertexLabel, node);
    if (oldVertexLabel == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Vertex label in alter anaylzer.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    // 目前只支持新增一列，所以乘以“1”
    uint32_t arrayLen = 1;
    DmPropertySchemaT *tmpProperties = TsSqlInitPropSchemaArray(memCtx, arrayLen);
    if (tmpProperties == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc size %zu in alter analyzer, ", arrayLen * sizeof(DmPropertySchemaT));
        return GMERR_OUT_OF_MEMORY;
    }
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < arrayLen; i++) {
        ret = TsInitOneProp(memCtx, tmpProperties, parsedStmt->column, i);
        if (ret != GMERR_OK) {
            break;
        }
    }
    return AssignPropAndCopiedVertexLabels(memCtx, oldVertexLabel, tmpProperties, node);
}

static Status AlterSetOp(DbMemCtxT *memCtx, SqlAlterTableStmtT *parsedStmt, AlterTableStmtT *node)
{
    Status ret = DmCreateEmptyVertexLabelWithMemCtx(memCtx, &node->vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Create alter vertex label for set op.");
        return ret;
    }
    ret = DmCopyVertexLabel(node->oldVertexLabel, node->vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Copy vertex label for set op.");
        return ret;
    }
    uint32_t memberNum = (uint32_t)(sizeof(g_tsAlterSetItemMember) / sizeof(TsAlterSetItemMemberT));
    bool *hitArray = DbDynMemCtxAlloc(memCtx, sizeof(bool) * memberNum);
    if (hitArray == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc hit array for set op.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(hitArray, sizeof(bool) * memberNum, 0, sizeof(bool) * memberNum);
    uint32_t setItemNum = DbListGetItemCnt(parsedStmt->setList);
    SqlSetItemT *setItem = NULL;
    for (uint32_t i = 0; i < setItemNum; i++) {
        setItem = *(SqlSetItemT **)DbListItem(parsedStmt->setList, i);
        ret = AlterVertexLabelBySetItem(node->vertexLabel, setItem, hitArray, memberNum);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Alter by setItem feature name:%s", setItem->key);
            return ret;
        }
    }
    node->type = T_SET_FEATURE;
    return ret;
}

static Status TsSqlBuildIrStmtForAlterTable(
    DmVertexLabelT *vertexLabel, SqlAlterTableStmtT *parsedStmt, SqlIrStmtT *irStmt)
{
    DB_POINTER3(vertexLabel, parsedStmt, irStmt);
    AlterTableStmtT *node = (AlterTableStmtT *)DbDynMemCtxAlloc(irStmt->memCtx, sizeof(AlterTableStmtT));
    Status ret = GMERR_OK;
    if (node == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc node for tbl-alter.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(node, sizeof(AlterTableStmtT), 0, sizeof(AlterTableStmtT));
    node->oldVertexLabel = vertexLabel;
    node->node.tag = T_ALTER_TABLE_STMT;
    irStmt->utilityStmt = (NodeT *)(void *)node;
    irStmt->irPlan = NULL;
    switch (parsedStmt->type) {
        case T_ADD_COLUMN:
            ret = AlterAddOp(irStmt->memCtx, parsedStmt, vertexLabel, node);
            if (ret != GMERR_OK) {
                return ret;
            }
            break;
        case T_SET_FEATURE:
            ret = AlterSetOp(irStmt->memCtx, parsedStmt, node);
            if (ret != GMERR_OK) {
                return ret;
            }
            break;
        default:
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Tbl-alter type.");
            return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

Status TsSqlAnalyzeAlterTable(SessionT *session, NodeT *parsedStmt, SqlIrStmtT *irStmt)
{
    DB_POINTER3(session, parsedStmt, irStmt);
    SqlAlterTableStmtT *alterTableStmt = (SqlAlterTableStmtT *)(void *)parsedStmt;
    Status ret = TsSqlVerifyAlterTable(session, irStmt->memCtx, alterTableStmt, irStmt);
    if (ret == GMERR_UNDEFINED_TABLE && alterTableStmt->ifExists) {
        return GMERR_UNDEFINED_TABLE;
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Verify tbl-alter");
        return ret;
    }

    ret = TsSqlBuildIrStmtForAlterTable(alterTableStmt->vertexLabel, alterTableStmt, irStmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}
