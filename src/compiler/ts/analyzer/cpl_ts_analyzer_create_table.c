/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Create table for TS analyzer
 * Author: lihainuo
 * Create: 2023-05-09
 */

#include <stdlib.h>
#include <string.h>
#include "db_json.h"
#include "adpt_string.h"
#include "srv_data_ts.h"
#include "cpl_public_sql_parser_common.h"
#include "cpl_public_verifier_utils.h"
#include "clt_ts.h"
#include "cpl_ts_analyzer_create_table.h"

#define MEMCTX_DESC_SIZE 50

#define TS_TIME_COLUMN "time_col"
#define TS_INTERVAL "interval"
#define TS_IS_VOLARILE_LABEL "is_volatile_label"
#define TS_COMPRESSION "compression"
#define TS_DATA_CACHE "cache_size"
#define TS_TTL "ttl"
#define TS_MAX_SIZE "max_size"
#define TS_DISK_LIMIT "disk_limit"
#define TS_DELAY_TIME "delay_time"
#define TS_TABLE_PATH "table_path"
#define TS_ENGINE "engine"
#define TS_MEM_TABLE "memory"
#define TS_COMP_MODE_NUM TS_COMP_BOTTOM

#define TS_MIN_TTL_VAL_MICSEC 60000000
#define TS_MAX_TTL_VAL_MONTH 1200
#define TS_MAX_TTL_VAL_DAY 36500
#define TS_MAX_TTL_VAL_MICSEC ((int64_t)(365 * 24 * 3600000000 * 100))

#define TS_MAX_DELAY_TIME ((int64_t)(60 * 1000000))
#define TS_INIT_SIZE_ON_SERVER 25000
#define TS_MAX_CACHE_CAPACITY 50000
#define TS_SENSITIVE_COL "sensitive_col"
#define TS_TABLE_PATH_MIN_LENGTH 2
#define TS_TABLE_PATH_ZERO 1
#define TS_MEM_TABLE_CFG_NUM 2
#define TS_MAX_IDX_NUM 64

// 时序场景下需要为列存的子目录预留长度，用户指定长度应该小于DB_MAX_PATH
#define TS_USER_TABLE_PATH_LEN 200

// for binary search, keep sorted
const char *g_cfgKeywords[] = {
    "cache_size",
    "compression",
    "delay_time",
    "disk_limit",
    "engine",
    "interval",
    "is_volatile_label",
    "max_size",
    "sensitive_col",
    "table_path",
    "time_col",
    "ttl",
};

// see struct CreateStmtContext of PostgresSQL
// maybe reuse for ALTER TABLE
typedef struct {
    uint32_t dbId;
    uint32_t namespaceId;
    char *sensitiveCols;
} SqlVerifyCreateTableCtxT;

// 表级压缩策略，和TsCompressionPolicyE一一对应
const char *g_compressMode[] = {
    [TS_COMP_NONE] = "no",                     // 不压缩
    [TS_COMP_FAST] = "fast",                   // 性能优先
    [TS_COMP_FAST_RAPIDLZ] = "fast(rapidlz)",  // 性能优先(lz4)
    [TS_COMP_FAST_ZSTAR] = "fast(zstar)",      // 性能优先(zstar)
    [TS_COMP_BOTTOM] = NULL,
};

static Status VerifyNormalMemVertexLabel(DbJsonT *jsonRootConfig, DmVertexLabelT *vertexLabel)
{
    DbJsonT *engine = DbJsonObjectGet(jsonRootConfig, TS_ENGINE);
    const char *value = DbJsonStringValue(engine);   // engine在开始检查过，不可能为NULL
    if (DbStrCmp(value, TS_MEM_TABLE, true) != 0) {  // 内存表行数最大限制为int64_t最大值
        // Ts mem table max size should range from 1 to 2^63 - 1.
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "engine support %s only.", TS_MEM_TABLE);
        return GMERR_INVALID_JSON_CONTENT;
    }
    vertexLabel->metaVertexLabel->vertexLabelType = VERTEX_TYPE_NORMAL;
    vertexLabel->metaVertexLabel->containerType = CONTAINER_HEAP;
    vertexLabel->commonInfo->heapInfo.ccType = CONCURRENCY_CONTROL_NORMAL;
    vertexLabel->commonInfo->heapInfo.heapShmAddr = DB_INVALID_SHMPTR;
    vertexLabel->commonInfo->heapInfo.maxVertexNumCheck = false;
    vertexLabel->commonInfo->heapInfo.isolationLevel = READ_COMMITTED;
    vertexLabel->metaCommon.isPersistent = false;
    return GMERR_OK;
}

static Status TsConfirmLabelType(DbJsonT *jsonRootConfig, DmVertexLabelT *vertexLabel)
{
    DbJsonT *timeColumn = DbJsonObjectGet(jsonRootConfig, TS_TIME_COLUMN);
    if (timeColumn == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            // Time-series time column must be configured with 'time_col' option.
            GMERR_INVALID_JSON_CONTENT, "ts time col.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    DbJsonT *engineMemory = DbJsonObjectGet(jsonRootConfig, TS_ENGINE);
    if (engineMemory != NULL) {
        return VerifyNormalMemVertexLabel(jsonRootConfig, vertexLabel);
    }
    return GMERR_OK;
}

/**
 * @brief extra table config time column from time-series logic table's configJson
 * @param jsonRootConfig configJson
 * @param vertexLabel parsed vertex label
 * @return Status Return code.
 */
static Status TsParseExtraTableConfigTimeColumn(DbJsonT *jsonRootConfig, DmVertexLabelT *vertexLabel)
{
    DbJsonT *timeColumn = DbJsonObjectGet(jsonRootConfig, TS_TIME_COLUMN);
    // timeColumn == NULL has been checked previously in TsConfirmLabelType
    DmPropertySchemaT *nodeSchema;
    const char *timeColValue = DbJsonStringValue(timeColumn);
    Status ret = DmSchemaGetPropeByName(vertexLabel->metaVertexLabel->schema, timeColValue, &nodeSchema, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (nodeSchema->dataType != DB_DATATYPE_INT64) {
        // time col data type must be int64
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_INVALID_PARAMETER_VALUE, "Ts timeCol dataType for tbl:%s.", vertexLabel->metaCommon.metaName);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DmTsInfoT *tsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;
    tsInfo->timeColId = nodeSchema->propeId;
    return GMERR_OK;
}

/**
 * @brief extra interval from time-series logic table's configJson
 * @param jsonRootConfig configJson
 * @param tsInfo ts logic table's unique info
 * @return Status Return code.
 */
static Status TsParseExtraTableConfigTimeInterval(DbJsonT *jsonRootConfig, DmTsInfoT *tsInfo)
{
    DbJsonT *config = DbJsonObjectGet(jsonRootConfig, TS_INTERVAL);
    if (config == NULL) {
        // Time-series time interval should be configured with 'interval' option, and minimum unit is 1 hour
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "Ts time interval.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    Status ret = TsParseToken2Interval(DbJsonStringValue(config), &tsInfo->interval);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parse Ts interval val.");
        return ret;
    }
    if (IsIntervalMinuteValid(tsInfo->interval) || IsIntervalOneHour(tsInfo->interval) ||
        IsIntervalOneDay(tsInfo->interval) || IsIntervalOneMonth(tsInfo->interval) ||
        IsIntervalOneYear(tsInfo->interval)) {
        return GMERR_OK;
    }

    DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "Ts time interval option.");
    return GMERR_INVALID_JSON_CONTENT;
}

/**
 * @brief extra table compress mode from time-series logic table's configJson
 * @param jsonRootConfig configJson
 * @param tsInfo ts logic table's unique info
 * @return Status Return code.
 */
static Status TsParseExtraTableCompressMode(DbJsonT *jsonRootConfig, DmTsInfoT *tsInfo)
{
    DbJsonT *comp = DbJsonObjectGet(jsonRootConfig, TS_COMPRESSION);
    if (comp == NULL) {
        return GMERR_OK;
    }
    const char *compName = DbJsonStringValue(comp);
    bool isValidCompConf = false;
    for (uint32_t i = 0; i < TS_COMP_MODE_NUM; i++) {
        if (DbStrCmp(g_compressMode[i], compName, true) == 0) {
            tsInfo->compressionMode = i;
            isValidCompConf = true;
            break;
        }
    }
    if (!isValidCompConf) {
        // Time-series compression should be configured with: fast, fast(rapidlz), fast(zstar), no
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "Ts compression.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    if (tsInfo->compressionMode == TS_COMP_FAST) {
        tsInfo->compressionMode = TS_COMP_FAST_RAPIDLZ;
    }
    return GMERR_OK;
}

static Status TsCheckTTLMinMax(DmTsInfoT *tsInfo)
{
    // less than 1 min
    if (tsInfo->ttl.microsecond < TS_MIN_TTL_VAL_MICSEC && tsInfo->ttl.day == 0 && tsInfo->ttl.month == 0) {
        // Time-series ttl should be bigger than 1 min
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "Ts min ttl.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    // more than threshold of each time limit
    if (tsInfo->ttl.microsecond > TS_MAX_TTL_VAL_MICSEC || tsInfo->ttl.day > TS_MAX_TTL_VAL_DAY ||
        tsInfo->ttl.month > TS_MAX_TTL_VAL_MONTH) {
        // Time-series ttl should less than 100 years.
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "Ts ttl.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    return GMERR_OK;
}

Status TsCheckTTLConfig(DmTsInfoT *tsInfo)
{
    Status ret = TsCheckTTLMinMax(tsInfo);
    if (ret != GMERR_OK) {
        return ret;
    }

    // less than interval and divisible by interval
    if (IsIntervalMinuteValid(tsInfo->interval)) {
        if ((tsInfo->ttl.microsecond > 0 && tsInfo->ttl.microsecond % tsInfo->interval.microsecond == 0) ||
            tsInfo->ttl.day != 0) {
            return GMERR_OK;
        }
        // Time-series cur ttl option should be integral multiple of interval when interval unit is minute.
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "Ts cur ttl min interval.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    if (IsIntervalOneHour(tsInfo->interval)) {
        if ((tsInfo->ttl.microsecond > 0 && tsInfo->ttl.microsecond % tsInfo->interval.microsecond == 0) ||
            tsInfo->ttl.day != 0) {
            return GMERR_OK;
        }
        // Time-series cur ttl option should be integral multiple of interval when interval is 1 hour.
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "Ts cur ttl interval.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    if (IsIntervalOneDay(tsInfo->interval)) {
        if (tsInfo->ttl.microsecond != 0 || tsInfo->ttl.month != 0) {
            // Time-series cur ttl option is not supported when interval is 1 day.
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "Ts cur ttl interval is 1 day.");
            return GMERR_INVALID_JSON_CONTENT;
        }
        return GMERR_OK;
    }
    if (IsIntervalOneMonth(tsInfo->interval)) {
        if (tsInfo->ttl.microsecond != 0) {
            // // Ts cur ttl option is not supported when interval is 1 month.
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "Ts cur ttl interval is 1 month.");
            return GMERR_INVALID_JSON_CONTENT;
        }
        return GMERR_OK;
    }
    if (IsIntervalOneYear(tsInfo->interval)) {
        if (tsInfo->ttl.month > 0 && tsInfo->ttl.month % tsInfo->interval.month == 0) {
            return GMERR_OK;
        }
        // Ts cur ttl option is not supported when interval is 1 year.
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "Ts cur ttl interval is 1 year.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    DB_LOG_ERROR_AND_SET_LASTERR(
        // Ts cur ttl option is unsupported format
        GMERR_INVALID_JSON_CONTENT, "Ts cur ttl option format.");
    return GMERR_INVALID_JSON_CONTENT;
}

/**
 * @brief extra ttl from time-series logic table's configJson
 * @param jsonRootConfig configJson
 * @param tsInfo ts logic table's unique info
 * @return Status Return code.
 */
static Status TsParseExtraTableConfigTTL(DbJsonT *jsonRootConfig, DmTsInfoT *tsInfo)
{
    DbJsonT *config = DbJsonObjectGet(jsonRootConfig, TS_TTL);
    if (config == NULL) {
        tsInfo->ttl.microsecond = 0;
        tsInfo->ttl.day = 0;
        tsInfo->ttl.month = 0;
        return GMERR_OK;
    }
    Status ret = TsParseToken2Interval(DbJsonStringValue(config), &tsInfo->ttl);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parse Ts time.");
        return ret;
    }

    ret = TsCheckTTLConfig(tsInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

typedef struct TsDiskLimitUint {
    const char *uintStr;
    uint64_t size;
} TsDiskLimitUintT;

Status TsParseConvertSizeUnit2Num(const char *unit, uint64_t *size)
{
    const TsDiskLimitUintT diskLimitUnit[] = {
        {"B", 1ULL}, {"KB", (uint64_t)SIZE_K(1)}, {"MB", SIZE_M(1)}, {"GB", SIZE_G(1)}, {"TB", SIZE_T(1)}};
    int32_t cnt = (int32_t)ELEMENT_COUNT(diskLimitUnit);
    for (int32_t i = 0; i < cnt; i++) {
        if (strcmp(unit, diskLimitUnit[i].uintStr) == 0) {
            *size = diskLimitUnit[i].size;
            return GMERR_OK;
        }
    }
    *size = 0;
    return GMERR_INVALID_PARAMETER_VALUE;
}

Status TsParseToken2DiskLimit(const char *limitStr, int64_t *diskLimit)
{
    DB_POINTER2(limitStr, diskLimit);
    char str[TS_DISK_LIMIT_CONFIG_LEN];
    errno_t err = strcpy_s(str, TS_DISK_LIMIT_CONFIG_LEN, limitStr);
    if (SECUREC_UNLIKELY(err != EOK)) {
        // over max num
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "DiskLimit token length.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    char *token[TS_DISK_LIMIT_TOKEN_NUM] = {NULL};
    uint32_t tokenNum = DbStrSplit(str, " ", TS_DISK_LIMIT_TOKEN_NUM, token);
    if (tokenNum != TS_DISK_LIMIT_TOKEN_NUM) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Parse DiskLimit token");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    uint64_t value = 0;
    Status ret = DbStrToUint64(token[0], &value);
    if (ret != GMERR_OK || value == 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Get DiskLimit token value %s.", limitStr);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DbStrToUpper(token[1]);
    uint64_t unit = 0;
    ret = TsParseConvertSizeUnit2Num(token[1], &unit);
    if (ret != GMERR_OK || unit == 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Get DiskLimit unit '%s'.", token[1]);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (TS_DISK_LIMIT_MAX / unit < value || value * unit < TS_DISK_LIMIT_MIN) {
        // DiskLimit should in range from 4KB to 1TB
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "DiskLimit range");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    *diskLimit = (int64_t)(value * unit);
    return GMERR_OK;
}

static Status TsParseExtraTableConfigDiskLimit(DbJsonT *jsonRootConfig, DmTsInfoT *tsInfo)
{
    DbJsonT *config = DbJsonObjectGet(jsonRootConfig, TS_DISK_LIMIT);
    if (config == NULL) {
        tsInfo->diskLimit = 0;
        return GMERR_OK;
    }
    Status ret = TsParseToken2DiskLimit(DbJsonStringValue(config), &tsInfo->diskLimit);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parse DiskLimit value.");
        return ret;
    }
    return GMERR_OK;
}

static Status TsParseExtraTableConfigCacheSize(DbJsonT *jsonRootConfig, DmTsInfoT *tsInfo)
{
    DbJsonT *cacheSize = DbJsonObjectGet(jsonRootConfig, TS_DATA_CACHE);
    if (cacheSize == NULL) {
        tsInfo->cacheCapacity = 0;
        return GMERR_OK;
    }
    uint32_t size = 0;
    Status ret = DbStrToUint32(DbJsonStringValue(cacheSize), &size);
    if (ret != GMERR_OK || size > TS_MAX_CACHE_CAPACITY) {
        // exceeds limit 50000
        DB_LOG_ERROR(GMERR_CONFIGURATION_LIMIT_EXCEEDED, "Data cache size: %s.", DbJsonStringValue(cacheSize));
        return GMERR_CONFIGURATION_LIMIT_EXCEEDED;
    }
    tsInfo->cacheCapacity = size;
    return GMERR_OK;
}

static Status TsVerifyTablePathLetter(char *subPath, size_t len)
{
    for (uint32_t i = 0; i < len; i++) {
        if ((subPath[i] >= *(char *)"a" && subPath[i] <= *(char *)"z") ||
            (subPath[i] >= *(char *)"A" && subPath[i] <= *(char *)"Z") ||
            (subPath[i] >= *(char *)"0" && subPath[i] <= *(char *)"9") || subPath[i] == *(char *)"_") {
            continue;
        } else {
            // subPath name is not compliant
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SYNTAX_ERROR, "subPath:%s", subPath);
            return GMERR_SYNTAX_ERROR;
        }
    }
    return GMERR_OK;
}

static Status TsVerifyTablePath(const char *tablePathStr, size_t len)
{
    Status ret = GMERR_OK;
    char slashes = '/';
    if (tablePathStr[0] != slashes) {
        // The first character of the table_path is not compliant
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SYNTAX_ERROR, "Table_path's first character");
        return GMERR_SYNTAX_ERROR;
    }
    size_t realityLen = len - TS_TABLE_PATH_ZERO;
    if (realityLen <= TS_TABLE_PATH_MIN_LENGTH ||
        (realityLen > TS_TABLE_PATH_MIN_LENGTH && tablePathStr[realityLen - TS_TABLE_PATH_MIN_LENGTH] == slashes)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SYNTAX_ERROR, "Table_path address");
        return GMERR_SYNTAX_ERROR;
    }
    if (realityLen > TS_TABLE_PATH_MIN_LENGTH && tablePathStr[realityLen - 1] != slashes) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SYNTAX_ERROR, "Table_path's last character");
        return GMERR_SYNTAX_ERROR;
    }
    char copyPath[DB_MAX_PATH] = {0};
    errno_t err = memcpy_s(copyPath, DB_MAX_PATH, tablePathStr, len);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Copy table_path");
        return GMERR_FIELD_OVERFLOW;
    }
    char *subPathSplit = strtok(copyPath, "/");
    while (subPathSplit) {
        ret = TsVerifyTablePathLetter(subPathSplit, strlen(subPathSplit));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Verify table_path letter");
            return ret;
        }
        subPathSplit = strtok(NULL, "/");
    }
    return ret;
}

static Status TsParseExtraTableConfigTablePath(DbJsonT *jsonRootConfig, DmTsInfoT *tsInfo, DbMemCtxT *memCtx)
{
    DbJsonT *tablePath = DbJsonObjectGet(jsonRootConfig, TS_TABLE_PATH);
    if (tablePath == NULL) {
        return GMERR_OK;
    }
    const char *tablePathStr = DbJsonStringValue(tablePath);
    size_t len = DM_STR_LEN(tablePathStr);
    // 传入路径为空时使用默认路径
    if (len == 1) {
        return GMERR_OK;
    }
    if (len > TS_USER_TABLE_PATH_LEN) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Custom table_path length:%" PRIu32 " exceed %" PRIu32,
            (uint32_t)len, TS_USER_TABLE_PATH_LEN);
        return GMERR_DATA_EXCEPTION;
    }
    Status ret = TsVerifyTablePath(tablePathStr, len);
    if (ret != GMERR_OK) {
        // table_path name is not compliant
        DB_LOG_ERROR(ret, "table_path:%s", tablePathStr);
        return ret;
    }
    tsInfo->tablePath = (char *)tsInfo + sizeof(DmTsInfoT);
    errno_t err = memcpy_s(tsInfo->tablePath, len, tablePathStr, len);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_MEMORY_OPERATE_FAILED, "Copy table_path:%s, length:%" PRIu32, tablePathStr, (uint32_t)len);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    ret = DbMakeDirWithGRPRXPermission(tsInfo->tablePath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Make directory, table_path:%s", tablePathStr);
        return ret;
    }
    tsInfo->tablePathLen = len;
    return ret;
}

static Status TsParseExtraTableConfigIsVolatileLabel(DbJsonT *jsonRootConfig, DmTsInfoT *tsInfo, DbMemCtxT *memCtx)
{
    DbJsonT *isVolatileJs = DbJsonObjectGet(jsonRootConfig, TS_IS_VOLARILE_LABEL);
    if (isVolatileJs == NULL) {
        tsInfo->isVolatile = false;
        return GMERR_OK;
    }
    const char *isVolatileStr = DbJsonStringValue(isVolatileJs);
    Status ret = DbStrToBool(isVolatileStr, &tsInfo->isVolatile);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "is_volatile_label:%s set bool", isVolatileStr);
        return ret;
    }
    return GMERR_OK;
}

inline static SqlVerifyCreateTableCtxT *TsCreateVerifyCreateTableCtx(DbMemCtxT *memCtx)
{
    DB_POINTER(memCtx);
    SqlVerifyCreateTableCtxT *ctx =
        (SqlVerifyCreateTableCtxT *)DbDynMemCtxAlloc(memCtx, sizeof(SqlVerifyCreateTableCtxT));
    if (ctx == NULL) {
        return NULL;
    }
    (void)memset_s(ctx, sizeof(SqlVerifyCreateTableCtxT), 0, sizeof(SqlVerifyCreateTableCtxT));
    return ctx;
}

static Status TsSqlVerifyDbAndTableName(
    SessionT *session, SqlCreateTableStmtT *parsedStmt, SqlVerifyCreateTableCtxT *ctx)
{
    DB_POINTER2(session, parsedStmt);

    // to consider, temp flag
    SqlTableRefT *name = parsedStmt->name;
    DmVertexLabelT *vertexLabel = NULL;
    if (TsSqlCheckNameLength(name->tableName)) {
        // exceeds limit 128 (including \\0)
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NAME_TOO_LONG, "%s", name->tableName);
        return GMERR_NAME_TOO_LONG;
    }
    DbStrToLower(name->tableName);  // convert to lower case
    Status ret = TsSqlVerifyDBAndGetLabel(session, name->database, name->tableName, &vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_INFO("table: %s verify db:%s and get vertexLabel with problem", name->tableName, name->database);
        return ret;
    }
    if (vertexLabel != NULL) {
        (void)CataReleaseVertexLabel(vertexLabel);
        if (!parsedStmt->ifNotExists) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DUPLICATE_TABLE, "%s", name->tableName);
        } else {
            DB_LOG_INFO("tbl %s already exists in %s", name->tableName, name->database);
        }
        return GMERR_DUPLICATE_TABLE;
    }
    ctx->dbId = session->dbId;
    ctx->namespaceId = session->namespaceId;
    return GMERR_OK;
}

static Status TsSqlVerifyOneColName(
    SqlCreateTableStmtT *parsedStmt, SqlColumnDefT *col, uint32_t curColIdx, char *tableName)
{
    DB_POINTER2(parsedStmt, col);
    if (TsSqlCheckNameLength(col->colName)) {
        // exceeds limit 128(including \\0)
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NAME_TOO_LONG, "%s of tbl: %s", col->colName, tableName);
        return GMERR_NAME_TOO_LONG;
    }
    for (uint32_t colIdx = 0; colIdx < curColIdx; colIdx++) {
        SqlColumnDefT *temCol = *(SqlColumnDefT **)DbListItem(parsedStmt->columns, colIdx);
        if (DbStrCmp(col->colName, temCol->colName, true) == 0) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DUPLICATE_COLUMN, " %s of tbl %s", tableName, col->colName);
            return GMERR_DUPLICATE_COLUMN;
        }
    }
    return GMERR_OK;
}

static Status TsSqlVerifyOneColCons(SqlCreateTableStmtT *parsedStmt, SqlColumnDefT *col, SqlVerifyCreateTableCtxT *ctx)
{
    DB_POINTER3(parsedStmt, col, ctx);
    // TS do not support cons now
    return GMERR_OK;
}

inline static Status TsSqlVerifyOneColCompressConfig(SqlColumnDefT *col, char *tableName)
{
    if (col->columnCompressMode < COMPRESS_END) {
        return GMERR_OK;
    }
    // has Unknown compression mode defined by codec
    DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_COLUMN_DEFINITION, "In tbl %s.", tableName);
    return GMERR_INVALID_COLUMN_DEFINITION;
}

static Status TsSqlVerifyOneCol(DbMemCtxT *memCtx, SqlCreateTableStmtT *parsedStmt, SqlVerifyCreateTableCtxT *ctx,
    uint32_t curColIdx, char *tableName)
{
    DB_POINTER3(ctx, memCtx, parsedStmt);
    SqlColumnDefT *col = *(SqlColumnDefT **)DbListItem(parsedStmt->columns, curColIdx);
    if (col == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "tbl %s has unknown col.", tableName);
        return GMERR_INTERNAL_ERROR;
    }

    Status ret = TsSqlVerifyOneColName(parsedStmt, col, curColIdx, tableName);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = TsSqlVerifyOneColType(col, tableName);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (col->constraints) {
        ret = TsSqlVerifyOneColCons(parsedStmt, col, ctx);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return TsSqlVerifyOneColCompressConfig(col, tableName);
}

static Status TsSqlVerifyCols(DbMemCtxT *memCtx, SqlCreateTableStmtT *parsedStmt, SqlVerifyCreateTableCtxT *ctx)
{
    DB_POINTER3(ctx, memCtx, parsedStmt);
    char *tableName = parsedStmt->name->tableName;
    uint32_t colNum = DbListGetItemCnt(parsedStmt->columns);
    if (colNum > SQL_COL_NUM_MAX) {
        // max:256
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_CONFIGURATION_LIMIT_EXCEEDED, "tbl %s's col num: %" PRIu32, tableName, colNum);
        return GMERR_CONFIGURATION_LIMIT_EXCEEDED;
    }

    for (uint32_t curColIdx = 0; curColIdx < colNum; curColIdx++) {
        Status ret = TsSqlVerifyOneCol(memCtx, parsedStmt, ctx, curColIdx, tableName);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status TsSqlVerifyOneCfg(SqlCreateWithT *createWith, char *tableName)
{
    DB_POINTER(createWith);

    bool isFound = false;
    int32_t middle = 0;
    int32_t low = 0;
    int32_t high = (int32_t)ELEMENT_COUNT(g_cfgKeywords) - 1;
    while (low <= high) {
        middle = low + DB_HALF_OF(high - low);
        int32_t compareResult = strcmp(g_cfgKeywords[middle], createWith->key);
        if (compareResult == 0) {
            isFound = true;
            break;
        } else if (compareResult > 0) {
            high = middle - 1;
        } else {
            low = middle + 1;
        }
    }
    if (!isFound) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_INVALID_JSON_CONTENT, "tbl %s 's config keywords: %s.", tableName, createWith->key);
        return GMERR_INVALID_JSON_CONTENT;
    }
    return GMERR_OK;
}

static bool IsColDefined(char *colStr, DbListT *columns)
{
    for (uint32_t i = 0; i < columns->count; i++) {
        SqlColumnDefT *col = *(SqlColumnDefT **)DbListItem(columns, i);
        if (strcmp(col->colName, colStr) == 0) {
            return true;
        }
    }
    return false;
}

static Status VerifySensitiveColUnique(
    DbOamapT *colNameMap, DbListT *columns, char *curStr, char *colStr, char *tableName)
{
    Status ret = GMERR_OK;
    char *trimStr = TrimInvisibleChar(curStr);
    if (trimStr == NULL) {
        ret = GMERR_SEMANTIC_ERROR;
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "tbl %s sensitive col %s empty.", tableName, colStr);
        return ret;
    }

    if (!IsColDefined(trimStr, columns)) {
        ret = GMERR_SEMANTIC_ERROR;
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "tbl %s sensitive col: %s not defined.", tableName, trimStr);
        return ret;
    }
    uint32_t hash = DbStrToHash32(trimStr);
    ret = DbOamapInsert(colNameMap, hash, trimStr, NULL, NULL);
    if (ret == GMERR_DUPLICATE_OBJECT) {
        ret = GMERR_SEMANTIC_ERROR;
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_SEMANTIC_ERROR, "tbl %s sensitive col name duplicate: %s.", tableName, trimStr);
        return ret;
    }
    return GMERR_OK;
}

static Status TsSqlVerifySensitiveCol(
    DbMemCtxT *memCtx, SqlVerifyCreateTableCtxT *ctx, SqlCreateTableStmtT *parsedStmt, char *tableName)
{
    if (ctx->sensitiveCols == NULL) {
        return GMERR_OK;
    }

    uint32_t strLen = DM_STR_LEN(ctx->sensitiveCols);
    char *colStr = (char *)DbDynMemCtxAlloc(memCtx, strLen);
    if (colStr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "tbl %s alloc sensitive col str.", tableName);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memcpy_s(colStr, strLen, ctx->sensitiveCols, strLen);

    // 列不能重复， 列必须存在
    DbOamapT colNameMap = {0};
    Status ret = DbOamapInit(&colNameMap, 0, DbOamapStringCompare, memCtx, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "tbl %s create sensitive col check map.", tableName);
        DbDynMemCtxFree(memCtx, colStr);
        return ret;
    }

    char *nextStr = NULL;
    char *curStr = strtok_s(colStr, ",", &nextStr);
    if (curStr == NULL) {
        ret = GMERR_SEMANTIC_ERROR;
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "tbl %s sensitive col %s empty.", tableName, colStr);
        goto END;
    }
    while (curStr != NULL) {
        ret = VerifySensitiveColUnique(&colNameMap, parsedStmt->columns, curStr, colStr, tableName);
        if (ret != GMERR_OK) {
            goto END;
        }
        curStr = strtok_s(NULL, ",", &nextStr);
    }
END:
    DbDynMemCtxFree(memCtx, colStr);
    DbOamapDestroy(&colNameMap);
    return ret;
}

static Status TsSqlVerifyOneIndex(
    SessionT *session, SqlCreateTableStmtT *parsedStmt, SqlCreateIdxInTblT *idxInTbl, char *timeColName)
{
    char *tableName = parsedStmt->name->tableName;
    if (idxInTbl == NULL || idxInTbl->indexInfo == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "Get tbl %s's idx.", tableName);
        return GMERR_SEMANTIC_ERROR;
    }
    char *indexName = idxInTbl->indexInfo->indexName;
    if (TsSqlCheckNameLength(indexName)) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            // too long, exceeds limit 128 (including \\0)
            GMERR_NAME_TOO_LONG, "Index:%s", indexName);
        return GMERR_NAME_TOO_LONG;
    }
    if (DbStrCmp(indexName, "time_col", true) == 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            // too long, exceeds limit 128 (including \\0)
            GMERR_SEMANTIC_ERROR, "Ts index cannot name time_col");
        return GMERR_SEMANTIC_ERROR;
    }
    // 确认索引只能建立在一列上
    uint32_t idxColNum = DbListGetItemCnt(idxInTbl->indexColList);
    if (idxColNum != 1) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Create idx on multi-cols in tbl %s.", tableName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    SqlIndexColumnT *idxCol = *(SqlIndexColumnT **)DbListItem(idxInTbl->indexColList, 0);
    SqlExprColumnT *idxColDef = (SqlExprColumnT *)(void *)idxCol->columnExpr->op;
    if (DbStrCmp(timeColName, idxColDef->columnName, true) == 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "Ts custom index cannot on time_col");
        return GMERR_SEMANTIC_ERROR;
    }

    bool isColFound = false;
    SqlColumnDefT *col = NULL;
    // 确认建立索引的列在表中
    uint32_t colNum = DbListGetItemCnt(parsedStmt->columns);
    for (uint32_t i = 0; i < colNum; i++) {
        col = *(SqlColumnDefT **)DbListItem(parsedStmt->columns, i);
        if (DbStrCmp(col->colName, idxColDef->columnName, true) == 0) {
            isColFound = true;
            break;
        }
    }
    if (isColFound == false) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_SEMANTIC_ERROR, "Find col:%s for idx when create tbl %s.", idxColDef->columnName, tableName);
        return GMERR_SEMANTIC_ERROR;
    }
    return GMERR_OK;
}

static Status TsSqlVerifyIndex(SessionT *session, DbMemCtxT *memCtx, SqlCreateTableStmtT *parsedStmt, char *timeColName)
{
    DB_POINTER3(session, memCtx, parsedStmt);
    if (parsedStmt->idxList == NULL) {
        return GMERR_OK;
    }
    Status ret = GMERR_OK;
    SqlCreateIdxInTblT *idxInTbl = NULL;
    SqlCreateIdxInTblT *prevIdxInTbl = NULL;
    uint32_t idxNum = DbListGetItemCnt(parsedStmt->idxList);
    if (idxNum > TS_MAX_IDX_NUM - 1) {
        // Idx num of one ts label should not larger than 64, one is used to build time_col index
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "ts idxNum should less than 64");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    for (uint32_t i = 0; i < idxNum; i++) {
        idxInTbl = *(SqlCreateIdxInTblT **)DbListItem(parsedStmt->idxList, i);
        ret = TsSqlVerifyOneIndex(session, parsedStmt, idxInTbl, timeColName);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 确认没有同名的索引
        for (uint32_t j = 0; j < i; j++) {
            prevIdxInTbl = *(SqlCreateIdxInTblT **)DbListItem(parsedStmt->idxList, j);
            if (DbStrCmp(idxInTbl->indexInfo->indexName, prevIdxInTbl->indexInfo->indexName, true) == 0) {
                DB_LOG_ERROR(GMERR_SEMANTIC_ERROR, "idx: %s duplicate", prevIdxInTbl->indexInfo->indexName);
                return GMERR_SEMANTIC_ERROR;
            }
        }
    }
    return GMERR_OK;
}

static Status TsSqlVerifyConfigs(
    DbMemCtxT *memCtx, SqlCreateTableStmtT *parsedStmt, SqlVerifyCreateTableCtxT *ctx, char **timeColName)
{
    DB_POINTER4(ctx, memCtx, parsedStmt, timeColName);
    DB_ASSERT(*timeColName == NULL);
    char *tableName = parsedStmt->name->tableName;
    if (parsedStmt->createWith == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "tbl %s lack of config name.", tableName);
        return GMERR_INTERNAL_ERROR;
    }
    uint32_t cfgNum = DbListGetItemCnt(parsedStmt->createWith);
    SqlCreateWithT *createWith = NULL;
    SqlCreateWithT *preCreWith = NULL;

    Status ret = GMERR_OK;
    for (uint32_t cfgIdx = 0; cfgIdx < cfgNum; cfgIdx++) {
        createWith = *(SqlCreateWithT **)DbListItem(parsedStmt->createWith, cfgIdx);
        DbStrToLower(createWith->key);
        for (uint32_t preIdx = 0; preIdx < cfgIdx; preIdx++) {
            preCreWith = *(SqlCreateWithT **)DbListItem(parsedStmt->createWith, preIdx);
            DbStrToLower(preCreWith->key);
            if (strcmp(createWith->key, preCreWith->key) == 0) {
                DB_LOG_ERROR_AND_SET_LASTERR(
                    GMERR_INVALID_PARAMETER_VALUE, "tbl %s's config name: %s duplicate.", tableName, createWith->key);
                return GMERR_INVALID_PARAMETER_VALUE;
            }
        }
        ret = TsSqlVerifyOneCfg(createWith, tableName);
        if (ret != GMERR_OK) {
            return ret;
        }

        if (strcmp(createWith->key, TS_SENSITIVE_COL) == 0) {
            ctx->sensitiveCols = createWith->value;
        }

        if (strcmp(createWith->key, TS_TIME_COLUMN) == 0) {
            *timeColName = createWith->value;
        }
    }

    if (*timeColName == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "time col not exists");
        return GMERR_SEMANTIC_ERROR;
    }

    return TsSqlVerifySensitiveCol(memCtx, ctx, parsedStmt, tableName);
}

static Status TsSqlVerifyCreateTable(
    SessionT *session, DbMemCtxT *memCtx, SqlCreateTableStmtT *parsedStmt, SqlVerifyCreateTableCtxT *ctx)
{
    DB_POINTER4(session, memCtx, parsedStmt, ctx);
    Status ret = TsSqlVerifyDbAndTableName(session, parsedStmt, ctx);
    if (ret != GMERR_OK) {
        return ret;
    }
    // verify col nums, name, type
    ret = TsSqlVerifyCols(memCtx, parsedStmt, ctx);
    if (ret != GMERR_OK) {
        return ret;
    }

    // verify configs
    char *timeColName = NULL;
    ret = TsSqlVerifyConfigs(memCtx, parsedStmt, ctx, &timeColName);
    if (ret != GMERR_OK) {
        return ret;
    }
    // verify index
    ret = TsSqlVerifyIndex(session, memCtx, parsedStmt, timeColName);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

static Status TsSqlFillVertexLabelMetaCommon(
    SqlCreateTableStmtT *parsedStmt, SqlVerifyCreateTableCtxT *ctx, DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel)
{
    DB_POINTER4(parsedStmt, ctx, memCtx, vertexLabel);
    uint32_t metaNameLen = DM_STR_LEN(parsedStmt->name->tableName);
    vertexLabel->metaCommon.metaName = (char *)DbDynMemCtxAlloc(memCtx, metaNameLen);
    if (vertexLabel->metaCommon.metaName == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Alloc meta name.");
        return GMERR_OUT_OF_MEMORY;
    }
    // table name has already convert to lower case
    (void)memcpy_s(vertexLabel->metaCommon.metaName, metaNameLen, parsedStmt->name->tableName, metaNameLen);
    vertexLabel->metaVertexLabel->topRecordName = vertexLabel->metaCommon.metaName;
    vertexLabel->metaCommon.dbId = ctx->dbId;
    vertexLabel->metaCommon.namespaceId = ctx->namespaceId;
    // if current vertexLabel is other table, following values will be changed while verifying the config values
    vertexLabel->metaCommon.isPersistent = true;
    vertexLabel->metaVertexLabel->vertexLabelType = VERTEX_TYPE_TS_LOGICAL;
    vertexLabel->metaVertexLabel->labelLevel = VERTEX_LEVEL_SIMPLE;
    return GMERR_OK;
}

static Status TsSqlFillPropertieWithCons(DbMemCtxT *memCtx, DmPropertySchemaT *propertie, SqlColumnDefT *col)
{
    DB_POINTER3(propertie, memCtx, col);
    if (col->constraints == NULL) {
        return GMERR_OK;
    }
    // TS do not support cons now
    DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "Cons in ts");
    return GMERR_INTERNAL_ERROR;
}

static Status InitVertexLabelSchema(SqlCreateTableStmtT *parsedStmt, DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel)
{
    vertexLabel->metaVertexLabel->schema = DbDynMemCtxAlloc(vertexLabel->memCtx, sizeof(DmSchemaT));
    if (vertexLabel->metaVertexLabel->schema == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Alloc schema.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(vertexLabel->metaVertexLabel->schema, sizeof(DmSchemaT), 0, sizeof(DmSchemaT));
    vertexLabel->metaVertexLabel->schema->propeNum = DbListGetItemCnt(parsedStmt->columns);
    vertexLabel->metaVertexLabel->schema->properties =
        TsSqlInitPropSchemaArray(memCtx, vertexLabel->metaVertexLabel->schema->propeNum);
    if (vertexLabel->metaVertexLabel->schema->properties == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Alloc properties.");
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

static Status TsSqlFillSingleProperty(SqlCreateTableStmtT *parsedStmt, DbMemCtxT *memCtx, SqlVerifyCreateTableCtxT *ctx,
    DmPropertySchemaT *properties, uint32_t index)
{
    SqlColumnDefT *col = *(SqlColumnDefT **)DbListItem(parsedStmt->columns, index);
    if (col == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "Get column definition.");
        return GMERR_INTERNAL_ERROR;
    }
    properties[index].nameLen = (uint16_t)strlen(col->colName) + 1;
    properties[index].name = (char *)DbDynMemCtxAlloc(memCtx, properties[index].nameLen);
    if (properties[index].name == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Alloc property name.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memcpy_s(properties[index].name, properties[index].nameLen, col->colName, properties[index].nameLen);
    properties[index].propeId = index;  // begin with 0
    properties[index].isValid = true;
    properties[index].isNullable = true;
    properties[index].dataType = col->standardType;
    properties[index].isFixed = DmIsFixedType(col->standardType);

    if ((ctx->sensitiveCols != NULL) && strstr(ctx->sensitiveCols, properties[index].name) != NULL) {
        properties[index].isSensitive = true;
    }

    if (col->columnCompressMode == COMPRESS_NONE) {
        DmSchemaSetTsNoCompress(&properties[index]);
    }
    if (properties[index].isFixed) {
        if (properties[index].dataType == DB_DATATYPE_FIXED) {
            properties[index].size = col->charLen;
        } else {
            properties[index].size = DmGetBasicDataTypeLength(col->standardType);
        }
        if (DbStrCmp(col->typeName, TS_IP_TYPE, true) == 0) {  // 标记是否为ip类型字段
            properties[index].isInet = true;
        }
    } else {
        if (properties[index].dataType == DB_DATATYPE_STRING || col->charLen == 0) {
            properties[index].size = TS_CHAR_MAX_LEN;
        } else {
            properties[index].size = col->charLen;
        }
    }
    return TsSqlFillPropertieWithCons(memCtx, &properties[index], col);
}

Status TsSqlFillProperties(
    SqlCreateTableStmtT *parsedStmt, DbMemCtxT *memCtx, SqlVerifyCreateTableCtxT *ctx, DmVertexLabelT *vertexLabel)
{
    DB_POINTER3(parsedStmt, memCtx, vertexLabel);
    Status ret = InitVertexLabelSchema(parsedStmt, memCtx, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmPropertySchemaT *properties = vertexLabel->metaVertexLabel->schema->properties;
    for (uint32_t i = 0; i < vertexLabel->metaVertexLabel->schema->propeNum; i++) {
        ret = TsSqlFillSingleProperty(parsedStmt, memCtx, ctx, properties, i);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (!properties[i].isFixed) {
            vertexLabel->metaVertexLabel->labelLevel = VERTEX_LEVEL_GENERAL;
        }
    }
    return GMERR_OK;
}

static Status HandleTsLogicLabelConfig(DbJsonT *jsonRootConfig, DmVertexLabelT *vertexLabel, DbMemCtxT *memCtx)
{
    DmTsInfoT *tsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;
    tsInfo->compressionMode = TS_COMP_FAST_RAPIDLZ;  // 默认压缩为fast模式
    // time_col
    Status ret = TsParseExtraTableConfigTimeColumn(jsonRootConfig, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    // interval
    ret = TsParseExtraTableConfigTimeInterval(jsonRootConfig, tsInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    // compression
    ret = TsParseExtraTableCompressMode(jsonRootConfig, tsInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    // ttl
    ret = TsParseExtraTableConfigTTL(jsonRootConfig, tsInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    // disk_limit
    ret = TsParseExtraTableConfigDiskLimit(jsonRootConfig, tsInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    // cache size
    ret = TsParseExtraTableConfigCacheSize(jsonRootConfig, tsInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    // table_path
    ret = TsParseExtraTableConfigTablePath(jsonRootConfig, tsInfo, memCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    // is_volatile_label
    ret = TsParseExtraTableConfigIsVolatileLabel(jsonRootConfig, tsInfo, memCtx);
    if (ret != GMERR_OK) {
        return ret;
    }

    tsInfo->isInsertHighConcurrent = DbCfgGetBoolLite(DB_CFG_IS_HIGH_CONCURRENCT, NULL);
    // 1 minute tolerance for ts interval
    tsInfo->tolerance.microsecond = TS_MICROSECONDS_PER_MIN;
    return GMERR_OK;
}

static Status TsParseExtraMemTableConfigMaxSize(DbJsonT *jsonRootConfig, DmTsInfoT *tsInfo)
{
    DbJsonT *maxSize = DbJsonObjectGet(jsonRootConfig, TS_MAX_SIZE);
    if (maxSize == NULL) {
        tsInfo->maxSize = DB_INVALID_UINT64;
        return GMERR_OK;
    }
    const char *value = DbJsonStringValue(maxSize);  // max_size在开始检查过, 不可能为NULL
    size_t len = strlen(value);
    for (uint32_t i = 0; i < len; i++) {
        if (value[i] < '0' || value[i] > '9') {
            // Ts mem table max size should be integer
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "mem tbl maxSize type.");
            return GMERR_INVALID_JSON_CONTENT;
        }
    }
    tsInfo->maxSize = strtoull(value, NULL, TS_STR_TO_NUM_BASE);
    if (tsInfo->maxSize <= 0 || tsInfo->maxSize > DB_MAX_INT64) {  // 内存表行数最大限制为int64_t最大值
        // range from 1 to 2^63 - 1
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "mem tbl maxSize range.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    return GMERR_OK;
}

static Status HandleTsMemLabelConfig(DbJsonT *jsonRootConfig, DmVertexLabelT *vertexLabel)
{
    DmTsInfoT *tsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;
    (void)memset_s(tsInfo, sizeof(DmTsInfoT), 0, sizeof(DmTsInfoT));

    tsInfo->labelType = MEM_LABEL;
    // time_col
    Status ret = TsParseExtraTableConfigTimeColumn(jsonRootConfig, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    // interval
    ret = TsParseExtraTableConfigTimeInterval(jsonRootConfig, tsInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    // ttl
    ret = TsParseExtraTableConfigTTL(jsonRootConfig, tsInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    // max_size
    ret = TsParseExtraMemTableConfigMaxSize(jsonRootConfig, tsInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    // disk_limit
    ret = TsParseExtraTableConfigDiskLimit(jsonRootConfig, tsInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = TsCalDiskLimitForMemTable(vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

static Status InitCommonInfoDefaultValue(SessionT *session, DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel)
{
    vertexLabel->commonInfo = DbDynMemCtxAlloc(memCtx, sizeof(VertexLabelCommonInfoT));
    if (vertexLabel->commonInfo == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Alloc common info.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(vertexLabel->commonInfo, sizeof(VertexLabelCommonInfoT), 0, sizeof(VertexLabelCommonInfoT));
    vertexLabel->commonInfo->heapInfo.maxVertexNum = DB_MAX_UINT64;
    vertexLabel->commonInfo->heapInfo.maxVertexNumCheck = false;
    vertexLabel->commonInfo->heapInfo.ccType = CONCURRENCY_CONTROL_NORMAL;
    vertexLabel->commonInfo->statusMergeList = DB_INVALID_SHMPTR;

    Status ret = TsSqlFillLabelCreatorInfo(session, memCtx, vertexLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return GMERR_OK;
}

static Status FillTrxInfo(DmVertexLabelT *vertexLabel)
{
    DmTrxInfoT trxInfo = {0};
    Status ret = CataGetNspTrxInfoById(
        vertexLabel->metaCommon.namespaceId, &trxInfo, DbGetInstanceByMemCtx(vertexLabel->memCtx));
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Get nsp trxInfo by id %" PRIu32 " when create tbl %s.", vertexLabel->metaCommon.namespaceId,
            vertexLabel->metaCommon.metaName);
        return ret;
    }
    // 用namespace级别进行覆盖
    vertexLabel->commonInfo->heapInfo.trxType = trxInfo.trxType;
    vertexLabel->commonInfo->heapInfo.isolationLevel = trxInfo.isolationLevel;
#ifdef FEATURE_GQL
    vertexLabel->commonInfo->heapInfo.skipRowLockPessimisticRR = false;
#endif
    if (trxInfo.isolationLevel != READ_COMMITTED) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "isolation level RC for ts logical tbl.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

// 需要为保存tablePath分配内存，所以提前计算长度
// DmTsInfoT和tablePath的内存一次连续分配方便管理
static uint32_t TsParseConfigGetTablePathLen(DbJsonT *jsonRootConfig)
{
    DbJsonT *tablePath = DbJsonObjectGet(jsonRootConfig, TS_TABLE_PATH);
    if (tablePath == NULL) {
        return 0;
    }
    const char *tablePathStr = DbJsonStringValue(tablePath);
    size_t len = strlen(tablePathStr);
    // 不为空字符串时加上'\0'的长度
    return len == 0 ? len : len + 1;
}

static Status TsSqlFillVertexLabelCommonInfo(SessionT *session, DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel)
{
    DB_POINTER3(session, memCtx, vertexLabel);
    Status ret = InitCommonInfoDefaultValue(session, memCtx, vertexLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = FillTrxInfo(vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbJsonT *jsonRootConfig = DbJsonLoads(vertexLabel->metaVertexLabel->configJson, DB_JSON_REJECT_DUPLICATES);
    if (jsonRootConfig == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "Load root json config.");
        return GMERR_INTERNAL_ERROR;
    }
    ret = DmCreateVertexLabelExtraInfo(vertexLabel, sizeof(DmTsInfoT) + TsParseConfigGetTablePathLen(jsonRootConfig));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Alloc DmTsInfoT.");
        goto EXIT;
    }

    if ((ret = TsConfirmLabelType(jsonRootConfig, vertexLabel)) != GMERR_OK) {
        goto EXIT;
    }

    // 内部校验config json
    if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_TS_LOGICAL) {
        ret = HandleTsLogicLabelConfig(jsonRootConfig, vertexLabel, memCtx);
    } else if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_NORMAL) {
        ret = HandleTsMemLabelConfig(jsonRootConfig, vertexLabel);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
    }
EXIT:
    DbJsonDelete(jsonRootConfig);
    return ret;
}

static Status TsSqlFillCreateVertexOutput(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, SqlIrStmtT *irStmt)
{
    DB_POINTER3(memCtx, vertexLabel, irStmt);
    CreateTableStmtT *node = (CreateTableStmtT *)DbDynMemCtxAlloc(memCtx, sizeof(CreateTableStmtT));
    if (node == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Alloc create table stmt node");
        return GMERR_OUT_OF_MEMORY;
    }
    node->vertexLabel = vertexLabel;
    node->node.tag = T_CREATE_TABLE_STMT;
    irStmt->utilityStmt = (NodeT *)node;
    irStmt->irPlan = NULL;
    return GMERR_OK;
}

static Status TsSqlFillCreateVertexLabelStmt(SessionT *session, SqlCreateTableStmtT *parsedStmt,
    SqlVerifyCreateTableCtxT *ctx, DbMemCtxT *memCtx, SqlIrStmtT *irStmt)
{
    DB_POINTER5(session, parsedStmt, ctx, memCtx, irStmt);
    DmVertexLabelT *vertexLabel;
    Status ret = DmCreateEmptyVertexLabelWithMemCtx(memCtx, &vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = TsSqlAllocSpaceForMetaLabel(memCtx, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = TsSqlFillVertexLabelMetaCommon(parsedStmt, ctx, memCtx, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill properties in schema
    ret = TsSqlFillProperties(parsedStmt, memCtx, ctx, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill configJson
    ret = TsSqlFillConfigJson(parsedStmt, memCtx, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill and verify tsInfo in the commonInfo
    ret = TsSqlFillVertexLabelCommonInfo(session, memCtx, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    // do not support primary key and unique cons
    ret = TsSqlFillIndexToVertex(memCtx, vertexLabel, parsedStmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill irStmt result
    return TsSqlFillCreateVertexOutput(memCtx, vertexLabel, irStmt);
}

Status TsSqlAnalyzeCreateTable(SessionT *session, DbMemCtxT *memCtx, NodeT *parsedStmt, SqlIrStmtT *irStmt)
{
    DB_POINTER4(session, memCtx, parsedStmt, irStmt);
    SqlVerifyCreateTableCtxT *ctx = TsCreateVerifyCreateTableCtx(memCtx);
    if (ctx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Alloc VerifyCreateTableCtx");
        return GMERR_OUT_OF_MEMORY;
    }

    // 校验表，列，config是否重复，敏感字段
    Status ret = TsSqlVerifyCreateTable(session, memCtx, (SqlCreateTableStmtT *)parsedStmt, ctx);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 填充元数据，校验tbl config内容见HandleTsLogicLabelConfig
    return TsSqlFillCreateVertexLabelStmt(session, (SqlCreateTableStmtT *)parsedStmt, ctx, memCtx, irStmt);
}
