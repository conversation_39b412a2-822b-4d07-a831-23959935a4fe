/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Common for TS analyzer
 * Author: lihainuo
 * Create: 2023-05-09
 */

#include "ptl_service_utils.h"
#include "dm_meta_key_oper.h"
#include "dm_meta_role.h"
#include "dm_meta_priv.h"
#include "dm_meta_schema.h"
#include "cpl_ts_analyzer_common.h"

#define TS_COL_STD_TYPE_NUM 5  // Number of standard types
#define TS_CHAR_MAX_LEN 65535
#define TS_IPV6_STR_LEN 33
#define TS_CONFIG_SIZE 5
#define TS_IP_TYPE "INET"
#define TS_TEXT_TYPE "TEXT"

typedef struct SqlStdType {
    const char *typeStr;
    DbDataTypeE type;
} SqlStdTypeT;

const SqlStdTypeT TS_STD_TYPE[] = {
    // only suppory int and text now
    {"CHAR", DB_DATATYPE_FIXED},
    {"INTEGER", DB_DATATYPE_INT64},
    {"INET", DB_DATATYPE_FIXED},
    {"BLOB", DB_DATATYPE_BYTES},
    {"TEXT", DB_DATATYPE_STRING},
};

DmVertexLabelT *TsSqlGetVertexLabelByName(uint32_t dbId, uint32_t namespaceId, char *tableName)
{
    DB_POINTER(tableName);
    DmVertexLabelT *vertexLabel = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, dbId, namespaceId, tableName);
    Status ret = CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    if (ret != GMERR_OK) {
        return NULL;
    }
    return vertexLabel;
}

DmPropertySchemaT *TsSqlInitPropSchemaArray(DbMemCtxT *memCtx, uint32_t arrayLen)
{
    size_t propArraySize = arrayLen * sizeof(DmPropertySchemaT);
    DmPropertySchemaT *properties = (DmPropertySchemaT *)DbDynMemCtxAlloc(memCtx, propArraySize);
    if (properties == NULL) {
        return NULL;
    }
    (void)memset_s(properties, propArraySize, 0, propArraySize);
    return properties;
}

Status TsSqlVerifyDBAndGetLabel(SessionT *session, char *database, char *tableName, DmVertexLabelT **retLabel)
{
    DB_POINTER3(session, tableName, retLabel);
    if (database) {
        DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(session->memCtx);
        Status ret = CataGetNamespaceIdByName(dbInstance, database, &session->namespaceId);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Get tbl %s's nspId in database: %s.", tableName, database);
            return ret;
        }
    }
    DmVertexLabelT *vl = NULL;
    DbSetPrintLogWithExpectedValue(false);  // 按需建表CataGet时不打印日志
    vl = TsSqlGetVertexLabelByName(session->dbId, session->namespaceId, tableName);
    DbSetPrintLogWithExpectedValue(true);
    *retLabel = vl;
    return GMERR_OK;
}

Status TsVerifyPropInfo(SqlSrcItemT *src, SqlExprColumnT *colExpr, int *cnt)
{
    DB_POINTER4(src, src->label, colExpr, cnt);
    DmSchemaT *schema = src->label->metaVertexLabel->schema;
    for (uint32_t colIdx = 0; colIdx < schema->propeNum; colIdx++) {
        char *srcColName = schema->properties[colIdx].name;
        if (schema->properties[colIdx].propeId != colIdx) {
            DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "propId incorrect");
            return GMERR_INTERNAL_ERROR;
        }
        if (DbStrCmp(srcColName, colExpr->columnName, true) == 0) {
            (*cnt)++;
            colExpr->location.label = src->label;
            colExpr->location.propId = colIdx;
            colExpr->location.property = schema->properties + colIdx;
            break;
        }
    }
    return GMERR_OK;
}

Status TsSqlVerifyOneColType(SqlColumnDefT *col, char *tableName)
{
    DB_POINTER(col);
    for (uint32_t typeIdx = 0; typeIdx < TS_COL_STD_TYPE_NUM; typeIdx++) {
        if (!col->typeName) {
            DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "In %s ", tableName);
            return GMERR_DATATYPE_MISMATCH;
        }
        if (DbStrCmp(col->typeName, TS_STD_TYPE[typeIdx].typeStr, true) == 0) {
            col->standardType = TS_STD_TYPE[typeIdx].type;
            if ((DbStrCmp(col->typeName, "char", true) == 0 || DbStrCmp(col->typeName, "blob", true) == 0) &&
                col->charLen > TS_CHAR_MAX_LEN) {
                DB_LOG_AND_SET_LASERR(
                    // max char length is 65535
                    GMERR_FEATURE_NOT_SUPPORTED, "%s's colName exceeds max char len", tableName);
                return GMERR_FEATURE_NOT_SUPPORTED;
            }
            if (DbStrCmp(col->typeName, TS_IP_TYPE, true) == 0) {
                col->charLen = TS_IPV6_STR_LEN;
            }
            if (DbStrCmp(col->typeName, TS_TEXT_TYPE, true) == 0 && col->charLen > 0) {
                DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "%s's text type has assign length", tableName);
                return GMERR_FEATURE_NOT_SUPPORTED;
            }
            return GMERR_OK;
        }
    }
    DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "Type unknown in %s.", tableName);
    return GMERR_DATATYPE_MISMATCH;
}

Status AcquireLabelWLatch(DmVertexLabelT *vertexLabel, LabelRWLatchT **latch)
{
    LabelRWLatchT *labelLatch = (LabelRWLatchT *)GetLabelRWLatchPtrById(
        vertexLabel->commonInfo->vertexLabelLatchId, DbGetInstanceByMemCtx(vertexLabel->memCtx));
    if (labelLatch == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_UNEXPECTED_NULL_VALUE, "tbl %s get latchLatch when ddl", vertexLabel->metaCommon.metaName);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t hungTimeUs = 0;
    Status ret = GMERR_OK;
    if ((ret = DbGetHungTime(WORKER_HUNG_LEVEL_ONE, &hungTimeUs)) != GMERR_OK) {
        return ret;
    }
    // use latch protect dml concurrency with ddl drop
    if (!LabelWLatchTimedAcquire(labelLatch, hungTimeUs)) {
        ret = GMERR_LOCK_NOT_AVAILABLE;
        DB_LOG_ERROR(ret, "Get latch wlatch timeout");
        return ret;
    }

    // 校验表锁的有效性，防止并发下表被删除
    ret = LabelLatchCheckVersion(labelLatch, vertexLabel->commonInfo->vertexLabelLatchVersionId);
    if (ret != GMERR_OK) {
        LabelWLatchRelease(labelLatch);
        DB_LOG_AND_SET_LASERR(ret, "tbl %s already dropped", vertexLabel->metaCommon.metaName);
        return ret;
    }
    *latch = labelLatch;
    return ret;
}

Status AcquireLabelWLatchFromList(DbListT *labelList, DbListT *latchList)
{
    uint32_t labelNum = DbListGetItemCnt(labelList);
    DB_ASSERT(labelNum <= 1);  // 时序当前逻辑保证一次操作仅有一张表被持加写锁
    if (labelNum == 0) {
        return GMERR_OK;
    }

    Status ret = GMERR_OK;
    // 不使用for循环，防止在死锁、资源释放考虑不周的情形下滥用labelList
    DmVertexLabelT *label = *(DmVertexLabelT **)DbListItem(labelList, 0);

    // latch释放在TsExecSql进行
    LabelRWLatchT *labelLatch = NULL;
    ret = AcquireLabelWLatch(label, &labelLatch);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Acquire tbl %s wlatch before query.", label->metaCommon.metaName);
        return ret;
    }
    ret = DbAppendListItem(latchList, &labelLatch);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 二次获取保证获取最新label， alter table会产生新的label
    DmVertexLabelT *vl =
        TsSqlGetVertexLabelByName(label->metaCommon.dbId, label->metaCommon.namespaceId, label->metaCommon.metaName);
    if (vl == NULL) {
        (void)CataReleaseVertexLabel(label);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    if (vl != label) {  // 表已经不是最新的，需要在list中设置为最新的
        ret = DbSetListItem(labelList, &vl, 0);
    }
    (void)CataReleaseVertexLabel(label);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {  // 这里出错已经不能保证list中数据的完整性了
        (void)CataReleaseVertexLabel(vl);
        DbClearList(labelList);  // 保证不在兜底时重复释放这里的表
        return ret;
    }
    return ret;
}

static uint32_t TsSqlGetCfgJsonLength(SqlCreateTableStmtT *parsedStmt)
{
    uint32_t configJsonSize = 0;
    // 按实际需要的长度申请内存
    uint32_t configCnt = DbListGetItemCnt(parsedStmt->createWith);
    configJsonSize += 1;  //  '{'
    for (uint32_t i = 0; i < configCnt; i++) {
        SqlCreateWithT *cwith = *(SqlCreateWithT **)DbListItem(parsedStmt->createWith, i);
        int32_t fieldLength =
            (int32_t)strlen(cwith->key) + (int32_t)strlen(cwith->value) + TS_CONFIG_SIZE;  //  "key":"value"
        configJsonSize += (uint32_t)fieldLength;
        configJsonSize += 1;  //  ',' 或 '}'
    }
    return configJsonSize + 1;  // \n
}

Status TsSqlFillConfigJson(SqlCreateTableStmtT *parsedStmt, DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel)
{
    if (parsedStmt->createWith == NULL) {
        vertexLabel->metaVertexLabel->configJson = NULL;
        return GMERR_OK;
    }
    // allocate space for configJson
    uint32_t configJsonSize = TsSqlGetCfgJsonLength(parsedStmt);
    char *configJson = (char *)DbDynMemCtxAlloc(memCtx, configJsonSize);
    if (configJson == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc config json");
        return GMERR_OUT_OF_MEMORY;
    }
    // record the pointer position
    char *currentPointer = configJson;
    currentPointer[0] = '{';
    currentPointer++;
    uint32_t configCnt = DbListGetItemCnt(parsedStmt->createWith);
    for (uint32_t i = 0; i < configCnt; i++) {
        SqlCreateWithT *cwith = *(SqlCreateWithT **)DbListItem(parsedStmt->createWith, i);
        int32_t fieldLength = sprintf_s(currentPointer, MAX_FIELD_LENGTH, "\"%s\":\"%s\"", cwith->key, cwith->value);
        if (fieldLength < 0) {
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Fill json field.");
            DbDynMemCtxFree(memCtx, configJson);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        currentPointer += fieldLength;
        // different suffix
        if (i != configCnt - 1) {
            currentPointer[0] = ',';
        } else {
            currentPointer[0] = '}';
        }
        currentPointer++;
        if (currentPointer - configJson > MAX_JSON_LENGTH) {
            // json length <= 65535
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "json len too long.");
            DbDynMemCtxFree(memCtx, configJson);
            return GMERR_OUT_OF_MEMORY;
        }
    }
    currentPointer[0] = '\0';
    vertexLabel->metaVertexLabel->configJson = configJson;
    return GMERR_OK;
}

static Status TsSqlFillIndexColInfo4CreateTable(
    DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, SqlCreateIdxInTblT *indexInfo, DmVlIndexLabelT *index)
{
    DmSchemaT *schema = vertexLabel->metaVertexLabel->schema;
    DmPropertySchemaT *properties = schema->properties;
    uint32_t propeNum = DbListGetItemCnt(indexInfo->indexColList);
    index->propeNum = (uint16_t)propeNum;

    index->propIds = (uint32_t *)DbDynMemCtxAlloc(memCtx, sizeof(uint32_t) * propeNum);
    if (index->propIds == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc idx propIds.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(index->propIds, sizeof(uint32_t) * propeNum, 0, sizeof(uint32_t) * propeNum);

    for (uint32_t i = 0; i < index->propeNum; i++) {
        SqlIndexColumnT *idxCol = *(SqlIndexColumnT **)DbListItem(indexInfo->indexColList, i);
        SqlExprColumnT *col = (SqlExprColumnT *)idxCol->columnExpr->op;
        if (col == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Empty column(%u) for idx", i);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        for (uint32_t j = 0; j < schema->propeNum; j++) {
            if (DbStrCmp(col->columnName, properties[j].name, true) == 0) {
                // index->propIds指定这个索引用了哪些属性
                index->propIds[i] = properties[j].propeId;
            }
        }
    }
    // index->properties 直接引用 vertexLabel->metaVertexLabel->schema->properties
    index->properties = properties;
    return GMERR_OK;
}

static Status TsSqlFillOneIndex(
    DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, DmVlIndexLabelT *index, SqlCreateIdxInTblT *indexInfo)
{
    Status ret = TsSqlFillIndexColInfo4CreateTable(memCtx, vertexLabel, indexInfo, index);
    if (ret != GMERR_OK) {
        return ret;
    }

    const char *indexName = indexInfo->indexInfo->indexName;
    index->indexNameLen = (uint32_t)(DM_STR_LEN(indexName));
    index->indexName = (char *)DbDynMemCtxAlloc(memCtx, index->indexNameLen);
    if (index->indexName == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, " Alloc idx name(%s).", indexName);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memcpy_s(index->indexName, index->indexNameLen, indexName, index->indexNameLen);

    index->idxLabelBase.indexConstraint = NON_UNIQUE;
    index->isNullable = false;
    index->idxLabelBase.isLabelLatchMode = false;
    index->fixedPropeNum = 0;
    for (uint32_t i = 0; i < index->propeNum; i++) {
        if (index->properties[index->propIds[i]].isFixed) {
            index->fixedPropeNum++;
        }
    }
    index->nullInfoBytes = DmGetNullInfoBytes(index->propeNum);
    index->maxKeyLen = DmGetMaxKeyBufLen4QE(index);
    index->idxLabelBase.indexType = HASH_LINKLIST_INDEX;
    index->idxLabelBase.srcLabelNameLen = GetStrLen(vertexLabel->metaCommon.metaName);
    index->idxLabelBase.srcLabelName = vertexLabel->metaCommon.metaName;
    return GMERR_OK;
}

static Status TsSqlFillTimeIndex(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, DmVlIndexLabelT *index)
{
    DmSchemaT *schema = vertexLabel->metaVertexLabel->schema;
    index->propeNum = 1;
    index->propIds = (uint32_t *)DbDynMemCtxAlloc(memCtx, sizeof(uint32_t));
    if (index->propIds == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc idx propIds.");
        return GMERR_OUT_OF_MEMORY;
    }
    DmTsInfoT *tsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;
    index->propIds[0] = tsInfo->timeColId;
    index->properties = schema->properties;

    const char *indexName = "time_col";
    index->indexNameLen = (uint32_t)(DM_STR_LEN(indexName));
    index->indexName = (char *)DbDynMemCtxAlloc(memCtx, index->indexNameLen);
    if (index->indexName == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, " Alloc idx name(%s).", indexName);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memcpy_s(index->indexName, index->indexNameLen, indexName, index->indexNameLen);

    index->idxLabelBase.indexConstraint = NON_UNIQUE;
    index->isNullable = false;
    index->idxLabelBase.isLabelLatchMode = false;
    index->fixedPropeNum = 0;
    for (uint32_t i = 0; i < index->propeNum; i++) {
        // 时间列应该必须为定长，可以考虑省略
        if (index->properties[index->propIds[i]].isFixed) {
            index->fixedPropeNum++;
        }
    }
    index->nullInfoBytes = DmGetNullInfoBytes(index->propeNum);
    index->maxKeyLen = DmGetMaxKeyBufLen4QE(index);
    index->idxLabelBase.indexType = ART_INDEX_LOCAL;
    index->idxLabelBase.srcLabelNameLen = GetStrLen(vertexLabel->metaCommon.metaName);
    index->idxLabelBase.srcLabelName = vertexLabel->metaCommon.metaName;
    return GMERR_OK;
}

// do not support primary key and unique cons
// time_col must be index column, and fixed to be the first index
// logical Label does not use index
Status TsSqlFillIndexToVertex(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, SqlCreateTableStmtT *parsedStmt)
{
    DB_POINTER2(memCtx, vertexLabel);
    if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_TS_LOGICAL) {
        return GMERR_OK;
    }

    if (parsedStmt->idxList == NULL) {
        vertexLabel->metaVertexLabel->secIndexNum = 1;
    } else {
        vertexLabel->metaVertexLabel->secIndexNum = DbListGetItemCnt(parsedStmt->idxList) + 1;
    }

    uint32_t secIndexSize = (uint32_t)sizeof(DmVlIndexLabelT) * vertexLabel->metaVertexLabel->secIndexNum;
    vertexLabel->metaVertexLabel->secIndexes = (DmVlIndexLabelT *)DbDynMemCtxAlloc(memCtx, secIndexSize);
    if (vertexLabel->metaVertexLabel->secIndexes == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Aalloc indexlabel for second index.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(vertexLabel->metaVertexLabel->secIndexes, secIndexSize, 0, secIndexSize);
    // build time_col index
    Status ret = TsSqlFillTimeIndex(memCtx, vertexLabel, vertexLabel->metaVertexLabel->secIndexes);
    if (ret != GMERR_OK) {
        return ret;
    }

    // build other index
    if (vertexLabel->metaVertexLabel->secIndexNum == 1) {
        return GMERR_OK;
    }
    SqlCreateIdxInTblT *indexInfo = NULL;
    for (uint32_t i = 1; i < vertexLabel->metaVertexLabel->secIndexNum; i++) {
        indexInfo = *(SqlCreateIdxInTblT **)DbListItem(parsedStmt->idxList, i - 1);
        ret = TsSqlFillOneIndex(memCtx, vertexLabel, vertexLabel->metaVertexLabel->secIndexes + i, indexInfo);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

typedef struct TsPrivToCheckPara {
    DmVertexLabelT *vertexLabel;  // for create stmt could be null
    DmPrivilegeE objPriv;
    CataSysPrivTypeE sysPriv;
} TsObjPrivToCheckT;

Status TsGetCheckPrivParaByType(NodeTagT stmtTag, TsObjPrivToCheckT *privToCheck)
{
    DB_POINTER(privToCheck);
    switch (stmtTag) {
        case T_TS_CREATE_TABLE_STMT:
            privToCheck->sysPriv = CREATE_PRIV;
            break;
        case T_TS_DROP_TABLE_STMT:
            privToCheck->sysPriv = DROP_PRIV;
            break;
        case T_TS_ALTER_TABLE_STMT:
            privToCheck->sysPriv = ALTER_PRIV;
            break;
        case T_TS_GRANT_REVOKE_STMT:
            privToCheck->sysPriv = GRANT_PRIV;
            break;
        case T_TS_SELECT_STMT:
        case T_TS_COPY_TO_STMT:
#if !defined FEATURE_STREAM || defined TS_MULTI_INST
            privToCheck->sysPriv = SELECTANY_PRIV;
            privToCheck->objPriv = DM_OBJ_SELECT_PRIV;
            break;
#endif
        case T_TS_BULK_INSERT_STMT:
            privToCheck->sysPriv = INSERTANY_PRIV;
            privToCheck->objPriv = DM_OBJ_INSERT_PRIV;
            break;
        case T_SQL_INSERT_STMT:
            privToCheck->sysPriv = INSERTANY_PRIV;
            privToCheck->objPriv = DM_OBJ_INSERT_PRIV;
            break;
        case T_TS_EXEC_INTERNAL_FUNC_STMT:
            privToCheck->sysPriv = SELECTANY_PRIV;
            privToCheck->objPriv = DM_OBJ_SELECT_PRIV;
            break;
        default:
            return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

Status TsAnalyzerCheckSingleObjPriv(CataRoleT *role, TsObjPrivToCheckT *privToCheck)
{
    DmVertexLabelT *vertexLabel = privToCheck->vertexLabel;
    // only check logical table and stream table privilege now
    if (vertexLabel->metaVertexLabel->vertexLabelType != VERTEX_TYPE_TS_LOGICAL) {
        return GMERR_OK;
    }

    // if is table owner
    if (DbStrCmp(role->metaCommon.metaName, vertexLabel->commonInfo->creator, true) == 0) {
        return GMERR_OK;
    }
    bool hasPriv =
        CataHasObjPriv(role, &(vertexLabel->commonInfo->metaInfoAddr->objPrivilege), (uint16_t)privToCheck->objPriv);
    return hasPriv ? GMERR_OK : GMERR_INSUFFICIENT_PRIVILEGE;
}

Status TsAnalyzerGetRoleBySession(SessionT *session, CataRoleT **role)
{
    CataRoleT *roleCache = NULL;
    Status ret = GetRoleBySession(session, &roleCache);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Get role by session");
        return ret;
    }
    ret = CataGetRoleById(roleCache->metaCommon.metaId, role, DbGetInstanceByMemCtx(session->memCtx));
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Get role by session");
        return ret;
    }
    return GMERR_OK;
}

Status TsAnalyzerCheckPrivForObj(SessionT *session, DmVertexLabelT *label, NodeTagT stmtTag)
{
    DB_POINTER(session);
    if (session->isDBA) {
        return GMERR_OK;
    }
    int32_t policyMode = DbCfgGetInt32Lite(DB_CFG_USER_POLICY_MODE, NULL);
    if (policyMode == 0) {
        return GMERR_OK;
    }

    CataRoleT *role = NULL;
    Status ret = TsAnalyzerGetRoleBySession(session, &role);
    if (ret != GMERR_OK) {
        // userPolicyMode为1时，可能未导入白名单。未获取到role也需要判断userPolicyMode，不直接返回
        DB_LOG_WARN(ret, "Get role by session");
        goto EXIT;
    }

    TsObjPrivToCheckT checkPara = {0};
    ret = TsGetCheckPrivParaByType(stmtTag, &checkPara);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "stmtTag %" PRIu32, (uint32_t)stmtTag);
        goto EXIT;
    }
    bool hasPriv = CataHasSysPriv(role, CATA_VERTEX_LABEL, (uint32_t)checkPara.sysPriv);
    if (hasPriv || label == NULL) {  // create stmt label is null, do not need check obj priv
        ret = hasPriv ? GMERR_OK : GMERR_INSUFFICIENT_PRIVILEGE;
        goto EXIT;
    }
    checkPara.vertexLabel = label;
    ret = TsAnalyzerCheckSingleObjPriv(role, &checkPara);

EXIT:
    if (role != NULL) {
        CataReleaseRole(role);
    }
    if (ret != GMERR_OK) {
        if (policyMode == 1) {
            DB_LOG_WARN(ret, "stmtTag %" PRIu32, (uint32_t)stmtTag);
            ret = GMERR_OK;
        } else {
            DB_LOG_ERROR(ret, "stmtTag %" PRIu32, (uint32_t)stmtTag);
        }
    }
    return ret;
}

#define TS_INSERT_INTO_TABLE_NUM 2U
Status TsAnalyzerCheckPrivForInsertInto(SessionT *session, DbListT *labelList)
{
    DB_POINTER2(session, labelList);
    if (DbListGetItemCnt(labelList) != TS_INSERT_INTO_TABLE_NUM) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "insert into label != 2");
        return GMERR_INTERNAL_ERROR;
    }
    Status ret = GMERR_OK;
    NodeTagT tagArr[TS_INSERT_INTO_TABLE_NUM] = {T_TS_SELECT_STMT, T_SQL_INSERT_STMT};
    for (uint32_t i = 0; i < TS_INSERT_INTO_TABLE_NUM; i++) {
        DmVertexLabelT *label = *(DmVertexLabelT **)DbListItem(labelList, i);
        ret = TsAnalyzerCheckPrivForObj(session, label, tagArr[i]);
        if (ret != GMERR_OK) {
            break;
        }
    }
    return ret;
}

Status TsAnalyzerCheckBulkInsertPriv(SessionT *session, DmVertexLabelT *logicalLabel)
{
    return TsAnalyzerCheckPrivForObj(session, logicalLabel, T_TS_BULK_INSERT_STMT);
}

Status TsSqlAnalyzerCheckPriv(SessionT *session, DbListT *labelList, NodeTagT stmtTag)
{
    // insert into 特殊处理
    if (stmtTag == T_SQL_INSERT_STMT) {
        return TsAnalyzerCheckPrivForInsertInto(session, labelList);
    }
    DmVertexLabelT *label = NULL;
    if (stmtTag != T_TS_CREATE_TABLE_STMT) {
        label = *(DmVertexLabelT **)DbListItem(labelList, 0);
    }
    Status ret = TsAnalyzerCheckPrivForObj(session, label, stmtTag);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Authenticate label");
        return ret;
    }
    // 避免重复释放，TsAnalyzerCheckPrivForObj完后从labelList去掉表
    if (stmtTag == T_TS_TRUNCATE_TABLE_STMT) {
        DbDelListItem(labelList, 0);
    }
    return ret;
}

void ReleaseLabelWLatchFromList(DbListT *latchList)
{
    uint32_t latchNum = DbListGetItemCnt(latchList);
    DB_ASSERT(latchNum <= 1);  // 时序当前逻辑保证一次操作仅有一张表被持加写锁
    if (latchNum == 0) {
        return;
    }
    LabelRWLatchT *labelLatch = NULL;
    labelLatch = *(LabelRWLatchT **)DbListItem(latchList, 0);
    LabelWLatchRelease(labelLatch);
}

Status TsSqlFillLabelCreatorInfo(SessionT *session, DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel)
{
    uint32_t creatorStrLen = 1;  // 1 byte for ':'
    creatorStrLen +=
        (uint32_t)strlen(session->externalUser.dbUserName) + (uint32_t)strlen(session->externalUser.dbProcessName) + 1;
    vertexLabel->commonInfo->creator = DbDynMemCtxAlloc(memCtx, creatorStrLen);
    if (vertexLabel->commonInfo->creator == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc creator info.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(vertexLabel->commonInfo->creator, creatorStrLen, 0, creatorStrLen);
    // creator format same as role metaName userName:processName
    int32_t length = sprintf_s(vertexLabel->commonInfo->creator, creatorStrLen, "%s:%s",
        session->externalUser.dbUserName, session->externalUser.dbProcessName);
    if (length < 0) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Format creator info.");
        DbDynMemCtxFree(memCtx, vertexLabel->commonInfo->creator);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

static void TsDmGetSchemaLength(const DmSchemaT *schema, uint32_t *schemaMaxLen, uint32_t *schemaMinLen)
{
    if (schema == NULL) {
        return;
    }
    DB_POINTER2(schemaMaxLen, schemaMinLen);

    for (uint32_t i = 0; i < schema->propeNum; ++i) {
        DmPropertySchemaT *prop = &(schema->properties[i]);
        if (!prop->isValid) {
            continue;
        }
        if (prop->isFixed) {
            *schemaMaxLen += prop->size;
            *schemaMinLen += prop->size;
        } else {
            // 变长字段序列化时需要记录长度，最大需要4字节
            *schemaMaxLen += prop->size + (uint32_t)sizeof(uint32_t);
            // 变长字段实际长度为0时，序列化时需占用1字节
            *schemaMinLen += 1;
        }
    }
}

Status TsCalDiskLimitForMemTable(DmVertexLabelT *vertexLabel)
{
    DmTsInfoT *tsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;

    if (tsInfo->diskLimit == 0) {
        tsInfo->diskLimit = (int64_t)tsInfo->maxSize;  // 没有设置disk_limit时, 改用max_size。
        tsInfo->useDiskLimit = false;
        return GMERR_OK;
    }

    uint32_t schemaMaxLen = 0;
    uint32_t schemaMinLen = 0;
    TsDmGetSchemaLength(vertexLabel->metaVertexLabel->schema, &schemaMaxLen, &schemaMinLen);
    if (schemaMaxLen == 0 || schemaMinLen == 0) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "schemaLen is zero, maxLen is %" PRIu32 ", minLen is %" PRIu32 "",
            schemaMaxLen, schemaMinLen);
        return GMERR_CONFIG_ERROR;
    }
    DB_LOG_INFO("Schame maxLen is %" PRIu32 ", minLen is %" PRIu32 "", schemaMaxLen, schemaMinLen);
    if (tsInfo->diskLimit < (int64_t)schemaMaxLen) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "diskLimit too small, disk_limit: %" PRId64 " B, row size: %" PRIu32 " B",
            tsInfo->diskLimit, schemaMaxLen);
        return GMERR_CONFIG_ERROR;
    }
    int64_t maxRowAllowByDiskLimit = tsInfo->diskLimit / (int64_t)schemaMaxLen;
    DB_ASSERT(maxRowAllowByDiskLimit > 0);
    DB_ASSERT(tsInfo->maxSize <= DB_MAX_INT64);  // max_size最大限制为int64_t最大值, 修改此约束需要修改比较时的unit转化
    if (maxRowAllowByDiskLimit > (int64_t)tsInfo->maxSize) {
        tsInfo->diskLimit = (int64_t)tsInfo->maxSize;  // 取消, 改用max_size
        tsInfo->useDiskLimit = false;
        DB_LOG_INFO(
            "Set diskLimit of table %s to be its maxSize %" PRIu64, vertexLabel->metaCommon.metaName, tsInfo->maxSize);
    } else {
        DB_LOG_INFO("Set diskLimit of table %s to be %" PRId64 " byte, which is %" PRId64 " row.",
            vertexLabel->metaCommon.metaName, tsInfo->diskLimit, maxRowAllowByDiskLimit);
        tsInfo->diskLimit = maxRowAllowByDiskLimit;
        tsInfo->useDiskLimit = true;
    }
    return GMERR_OK;
}
