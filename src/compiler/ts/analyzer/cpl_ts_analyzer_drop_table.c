/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: for TS Drop analyzer
 * Author: lihainuo
 * Create: 2023-07-19
 */

#include "adpt_string.h"
#include "cpl_public_sql_parser_common.h"
#include "cpl_ts_analyzer_drop_table.h"

Status TsSqlVerifyDropTable(
    SessionT *session, SqlDropStmtT *parsedStmt, uint32_t *vertexLabelId, DmVertexLabelT **curLabel)
{
    DB_POINTER3(session, parsedStmt, vertexLabelId);
    SqlFullNameT *fullName = parsedStmt->name;
    DmVertexLabelT *vertexLabel = NULL;
    if (TsSqlCheckNameLength(fullName->name)) {
        // too long exceeds 128(including \\0)
        DB_LOG_AND_SET_LASERR(GMERR_NAME_TOO_LONG, "tbl %s", fullName->name);
        return GMERR_NAME_TOO_LONG;
    }
    DbStrToLower(fullName->name);
    Status ret = TsSqlVerifyDBAndGetLabel(session, fullName->database, fullName->name, &vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Verify db and get label");
        return ret;
    }
    if (vertexLabel == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_TABLE, "tbl %s in verify drop", fullName->name);
        return GMERR_UNDEFINED_TABLE;
    }

    if (!IsTsTable(vertexLabel) && vertexLabel->metaVertexLabel->vertexLabelType != VERTEX_TYPE_NORMAL) {
        ret = GMERR_FEATURE_NOT_SUPPORTED;
        DB_LOG_AND_SET_LASERR(ret, "Drop non ts tbl %s.", fullName->name);
        goto EXIT;
    }

    *vertexLabelId = vertexLabel->metaCommon.metaId;
    *curLabel = vertexLabel;
    return GMERR_OK;
EXIT:
    (void)CataReleaseVertexLabel(vertexLabel);
    return ret;
}

Status TsSqlFillDropTableOutput(DbMemCtxT *memCtx, SqlIrStmtT *irStmt, uint32_t vertexLabelId)
{
    DB_POINTER2(memCtx, irStmt);

    DropTableStmtT *node = (DropTableStmtT *)DbDynMemCtxAlloc(memCtx, sizeof(DropTableStmtT));
    if (node == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc drop table stmt node");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(node, sizeof(DropTableStmtT), 0, sizeof(DropTableStmtT));
    node->dropSysTable = true;
    node->vertexLabelId = vertexLabelId;
    node->node.tag = T_DROP_TABLE_STMT;
    irStmt->utilityStmt = (NodeT *)node;
    irStmt->irPlan = NULL;
    return GMERR_OK;
}

Status TsAnalyzeDropTable(SessionT *session, NodeT *parsedStmt, SqlIrStmtT *irStmt, DmVertexLabelT **curLabel)
{
    DB_POINTER3(session, parsedStmt, irStmt);
    SqlDropStmtT *dropStmt = (SqlDropStmtT *)parsedStmt;
    uint32_t vertexLabelId = 0;
    Status ret = TsSqlVerifyDropTable(session, dropStmt, &vertexLabelId, curLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    return TsSqlFillDropTableOutput(irStmt->memCtx, irStmt, vertexLabelId);
}

Status TsSqlAnalyzeDropTable(SessionT *session, NodeT *parsedStmt, SqlIrStmtT *irStmt)
{
    DB_POINTER3(session, parsedStmt, irStmt);
    SqlDropStmtT *dropStmt = (SqlDropStmtT *)parsedStmt;
    Status ret = GMERR_OK;
    DmVertexLabelT *curLabel = NULL;
    if (dropStmt->dropElemType == DROP_INDEX) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Drop index");
        ret = GMERR_FEATURE_NOT_SUPPORTED;
    } else if (dropStmt->dropElemType == DROP_VIEW || dropStmt->dropElemType == DROP_TABLE) {
        ret = TsAnalyzeDropTable(session, parsedStmt, irStmt, &curLabel);
    }
    if (ret != GMERR_OK) {
        goto ERROR;
    }
    ret = DbAppendListItem(irStmt->labelList, &curLabel);
    if (ret != GMERR_OK) {
        goto ERROR;
    }
    return ret;
ERROR:
    if (curLabel != NULL) {
        (void)CataReleaseVertexLabel(curLabel);
        curLabel = NULL;
    }
    return ret;
}
