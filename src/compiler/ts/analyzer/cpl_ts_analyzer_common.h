/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Common for TS analyzer
 * Author: lihainuo
 * Create: 2023-05-09
 */

#ifndef CPL_TS_ANALYZER_COMMON_H
#define CPL_TS_ANALYZER_COMMON_H

#include "adpt_string.h"
#include "db_label_latch_mgr.h"
#include "db_utils.h"
#include "dm_data_ts.h"
#include "ee_plan_node_ddl.h"
#include "ee_session_interface.h"
#include "ee_session.h"
#include "cpl_ir_plan.h"
#include "cpl_public_sql_parser_common.h"
#include "cpl_log.h"

#define TS_STR_TO_NUM_BASE 10

// see SQLITE_MAX_LENGTH
#define TS_MAX_DATA_LENGTH (1024 * 1024)

#define MAX_JSON_LENGTH 65535
#define MAX_FIELD_LENGTH 34000
#define TS_IP_TYPE "INET"

#ifdef __cplusplus
extern "C" {
#endif

static inline bool TsSqlCheckNameLength(char *name)
{
    return DM_STR_LEN(name) > SQL_NAME_LEN_MAX;
}

inline static bool IsTsTable(DmVertexLabelT *vl)
{
    return vl->metaVertexLabel->vertexLabelType == VERTEX_TYPE_TS_LOGICAL ||
           vl->metaVertexLabel->vertexLabelType == VERTEX_TYPE_SYSVIEW;
}

inline static Status TsSqlAllocSpaceForMetaLabel(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(memCtx, vertexLabel);
    vertexLabel->metaVertexLabel = (MetaVertexLabelT *)DbDynMemCtxAlloc(memCtx, sizeof(MetaVertexLabelT));
    if (vertexLabel->metaVertexLabel == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Alloc metaVertexLabel.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(vertexLabel->metaVertexLabel, sizeof(MetaVertexLabelT), 0x00, sizeof(MetaVertexLabelT));
    return GMERR_OK;
}

static inline void TsReleaseLabel(DmVertexLabelT *label)
{
    if (label != NULL) {
        (void)CataReleaseVertexLabel(label);
        label = NULL;
    }
}

// parse methods
Status TsParseToken2DiskLimit(const char *limitStr, int64_t *diskLimit);
Status TsParseToken2Interval(const char *intervalStr, TsIntervalT *interval);

// analyze methods
Status TsSqlFillIndexToVertex(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, SqlCreateTableStmtT *parsedStmt);
Status TsSqlAnalyzerCheckPriv(SessionT *session, DbListT *labelList, NodeTagT stmtTag);
Status TsAnalyzerCheckBulkInsertPriv(SessionT *session, DmVertexLabelT *logicalLabel);
Status TsSqlFillLabelCreatorInfo(SessionT *session, DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel);
DmVertexLabelT *TsSqlGetVertexLabelByName(uint32_t dbId, uint32_t namespaceId, char *tableName);
DmPropertySchemaT *TsSqlInitPropSchemaArray(DbMemCtxT *memCtx, uint32_t arrayLen);
Status TsSqlFillConfigJson(SqlCreateTableStmtT *parsedStmt, DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel);
Status TsCalDiskLimitForMemTable(DmVertexLabelT *vertexLabel);

// verify methods
Status TsSqlVerifyDBAndGetLabel(SessionT *session, char *database, char *tableName, DmVertexLabelT **retLabel);
Status TsVerifyPropInfo(SqlSrcItemT *src, SqlExprColumnT *colExpr, int *cnt);
Status TsSqlVerifyOneColType(SqlColumnDefT *col, char *tableName);
Status TsCheckTTLConfig(DmTsInfoT *tsInfo);

// latch methods
Status AcquireLabelWLatchFromList(DbListT *labelList, DbListT *latchList);
Status AcquireLabelWLatch(DmVertexLabelT *vertexLabel, LabelRWLatchT **latch);
void ReleaseLabelWLatchFromList(DbListT *latchList);

#ifdef __cplusplus
}
#endif

#endif
