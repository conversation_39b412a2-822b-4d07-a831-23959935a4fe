/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: for verify expr in ts module
 * Author: lihainuo
 * Create: 2023-06-01
 */

#include "db_utils.h"
#include "cpl_ir_aa_schema.h"
#include "cpl_ts_analyzer_common.h"
#include "cpl_ts_build_op.h"
#include "cpl_ts_verify_select.h"
#include "cpl_ts_verify_expr.h"

#define TS_COLUMN_COUNT_ZERO 0
#define TS_COLUMN_COUNT_ONE 1
#define TS_WHERE_MAX_DEPTH 128

Status TsVerifyColTblName(const DbListT *tableList, SqlExprColumnT *columnExpr)
{
    DB_POINTER(tableList);
    // 时序只有1个表
    SqlSrcItemT *src = *(SqlSrcItemT **)DbListItem(tableList, 0);
    if (src->type != SRC_TABLE_REF) {
        return GMERR_SEMANTIC_ERROR;
    }

    // 表名或表别名
    if (columnExpr->tableName != NULL) {
        char *srcTableName = src->aliasName ? src->aliasName : src->srcTable->tableName;
        if (DbStrCmp(srcTableName, columnExpr->tableName, true) != 0 &&
            DbStrCmp(src->srcTable->tableName, columnExpr->tableName, true) != 0) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "tbl: %s is not find.", columnExpr->tableName);
            return GMERR_SEMANTIC_ERROR;
        }
    }
    return GMERR_OK;
}

Status TsSqlGetColumnCnt(const DbListT *tableList, SqlExprColumnT *colExpr, int *cnt)
{
    DB_POINTER2(tableList, colExpr);
    *cnt = TS_COLUMN_COUNT_ZERO;
    Status ret = GMERR_OK;
    for (uint32_t j = 0; j < tableList->count; j++) {
        SqlSrcItemT *src = *(SqlSrcItemT **)DbListItem(tableList, j);
        if (src->type != SRC_TABLE_REF) {
            return GMERR_SEMANTIC_ERROR;
        }
        if (colExpr->tableName != NULL) {
            char *srcTableName = src->aliasName ? src->aliasName : src->srcTable->tableName;
            if (DbStrCmp(srcTableName, colExpr->tableName, true) != 0) {
                continue;
            }
        }
        ret = TsVerifyPropInfo(src, colExpr, cnt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

static Status TsCheckWhereColType(SqlExprT *expr)
{
    DB_POINTER(expr);
    SqlExprColumnT *colExpr = CastToSqlExprColumn(expr->op);
    if (!colExpr->location.property->isValid) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_SEMANTIC_ERROR, "where unsupport node, name:%s", colExpr->location.property->name);
        return GMERR_SEMANTIC_ERROR;
    }

    // where不支持blob类型
    if (colExpr->location.property->dataType == DB_DATATYPE_BYTES) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_SEMANTIC_ERROR, "where unsupport blob, name:%s", colExpr->location.property->name);
        return GMERR_SEMANTIC_ERROR;
    }
    return GMERR_OK;
}

static bool IsConstExprType(SqlExprT *expr)
{
    DB_POINTER(expr);
    return expr->op->type == SQL_EXPR_ITEM_CONST || expr->op->type == SQL_EXPR_OP_UPLUS ||
           expr->op->type == SQL_EXPR_OP_UMINUS || expr->op->type == SQL_EXPR_ITEM_PARA;
}

static SqlExprT *TsGetBroExpr(SqlExprT *expr)
{
    DB_POINTER(expr);
    if (expr->father == NULL) {
        return NULL;
    }

    if (expr->father->op->arity == 1) {
        return expr;
    }

    if (expr == expr->father->children[1]) {
        return expr->father->children[0];
    }
    return expr->father->children[1];
}

static Status TsSqlVerifyColBroExpr(SqlExprT *expr, bool isSvLabel)
{
    DB_POINTER(expr);
    // where id
    if (expr->father == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "col filter without val unsupport ");
        return GMERR_SEMANTIC_ERROR;
    }

    // (id+id)>1
    if (IsArithExprType(expr->father)) {
        return GMERR_OK;
    }

    // where id > id 或者 length(id) > id 或者 length(id) > length(id) 或者 id > length(id)
    if (!IsConstExprType(expr)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "col filter with none const val");
        return GMERR_SEMANTIC_ERROR;
    }

    SqlExprConstT *sqlConst = (SqlExprConstT *)expr->op;
    if (!isSvLabel && sqlConst->arg.type == DB_DATATYPE_DOUBLE) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "col filter in ts with double val");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

Status TsSqlVerifyColumnBase(const DbListT *tableList, SqlExprT *expr)
{
    DB_POINTER2(tableList, expr);
    SqlExprColumnT *colExpr = CastToSqlExprColumn(expr->op);
    if (colExpr->dbName != NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "dbName not null");
        return GMERR_SEMANTIC_ERROR;
    }

    // 过滤列必须在表中定义
    int cnt = TS_COLUMN_COUNT_ZERO;
    Status ret = TsSqlGetColumnCnt(tableList, colExpr, &cnt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (cnt != TS_COLUMN_COUNT_ONE) {
        char *errorInfo = cnt == TS_COLUMN_COUNT_ZERO ? "no such col, using '' to wrap val for filter cols with it" :
                                                        "same field in multi tbls unsupport now.";
        if (colExpr->dbName != NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "%s: %s.%s.%s", errorInfo, colExpr->dbName,
                colExpr->tableName, colExpr->columnName);
        } else if (colExpr->tableName != NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_SEMANTIC_ERROR, "%s: %s.%s", errorInfo, colExpr->tableName, colExpr->columnName);
        } else {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "%s: %s", errorInfo, colExpr->columnName);
        }
        return GMERR_SEMANTIC_ERROR;
    }

    // 过滤列不能是node类型和blob类型
    return TsCheckWhereColType(expr);
}

Status TsSqlVerifyColumnExpr(const DbListT *tableList, SqlExprT *expr, bool isSvLabel)
{
    DB_POINTER2(tableList, expr);
    Status ret = TsSqlVerifyColumnBase(tableList, expr);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 过滤列必须有过滤条件且只能是常量
    SqlExprT *broExpr = TsGetBroExpr(expr);
    if (broExpr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "unsupported col filter format");
        return GMERR_SEMANTIC_ERROR;
    }

    ret = TsSqlVerifyColBroExpr(broExpr, isSvLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

static Status TsSqlVerifyFuncExpr(const DbListT *tableList, SqlExprT *expr, bool isSvLabel)
{
    DB_POINTER2(tableList, expr);
    SqlExprFuncT *funcExpr = CastToSqlExprFunc(expr->op);
    // 目前where条件中函数只支持length
    if (funcExpr->funcType != FUNC_TYPE_LENGTH) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Where clause's func type not length.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    uint32_t argCnt = funcExpr->args == NULL ? 0 : funcExpr->args->count;
    if (argCnt != 1) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "len's param num not 1.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    SqlExprT *sqlExpr = *(SqlExprT **)DbListItem(funcExpr->args, 0);
    if (sqlExpr->op->type != SQL_EXPR_ITEM_COLUMN) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_FEATURE_NOT_SUPPORTED, "length 's param type %" PRIu32 "not col.", sqlExpr->op->type);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    Status ret = TsSqlVerifyColumnBase(tableList, sqlExpr);
    if (ret != GMERR_OK) {
        return ret;
    }

    // length函数过滤条件只能是常量
    SqlExprT *broExpr = TsGetBroExpr(expr);
    if (broExpr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "unsupported col filter format");
        return GMERR_SEMANTIC_ERROR;
    }
    ret = TsSqlVerifyColBroExpr(broExpr, isSvLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    // length只能和整型比较（常量情况）
    if (broExpr->op->type == SQL_EXPR_ITEM_CONST && CastToSqlExprConst(broExpr->op)->arg.type != DB_DATATYPE_INT64) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "length should compare to int or where's para.");
        return GMERR_SEMANTIC_ERROR;
    }

    // ip类型不支持length
    SqlExprColumnT *colExpr = CastToSqlExprColumn(sqlExpr->op);
    if (colExpr->location.property->isInet) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "inet in length in where.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    // blob类型不支持length
    if (colExpr->location.property->dataType == DB_DATATYPE_BYTES) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "blob in length in where.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

static bool IsInt64ColumnExpr(const AASchemaT *schema, SqlExprT *expr)
{
    DB_POINTER2(schema, expr);
    if (expr->op->type != SQL_EXPR_ITEM_COLUMN) {
        return false;
    }
    SqlExprColumnT *columnExpr = CastToSqlExprColumn(expr->op);
    for (uint32_t i = 0; i < schema->propAA.propNum; i++) {
        char *srcColName = schema->propAA.properties[i]->name;
        if (DbStrCmp(srcColName, columnExpr->columnName, true) == 0) {
            return (schema->propAA.properties[i]->dataType == DB_DATATYPE_INT64);
        }
    }
    return false;
}

Status TsSqlVerifyArithExpr(DbMemCtxT *memCtx, const AASchemaT *schema, SqlExprT *expr)
{
    DB_POINTER2(schema, expr);
    // where a + b 场景
    if (expr->father == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "arith expr not have one right cosnt");
        return GMERR_SEMANTIC_ERROR;
    }

    // 父节点左右子节点都要存在
    if (expr->father->children[0] == NULL || expr->father->children[1] == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "arith expr not have brother expr.");
        return GMERR_SEMANTIC_ERROR;
    }

    // 算术表达式必须在左边, 且必须是变量
    if (!(expr == expr->father->children[0]) && IsColumnExpr(expr->children[0]) && IsColumnExpr(expr->children[1])) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "cols in the arith expr not in the left side.");
        return GMERR_SEMANTIC_ERROR;
    }

    // 常量/参数只能有一个，在表达式右边
    if (!IsConstExpr(GetBroExpr(expr)) && GetBroExpr(expr)->op->type != SQL_EXPR_ITEM_PARA) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "arith expr have various const on right side.");
        return GMERR_SEMANTIC_ERROR;
    }

    // 只支持int64类型
    if (!((IsInt64ConstExpr(GetBroExpr(expr)) || GetBroExpr(expr)->op->type == SQL_EXPR_ITEM_PARA) &&
            IsInt64ColumnExpr(schema, expr->children[0]) && IsInt64ColumnExpr(schema, expr->children[1]))) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "arith expr has none int64.");
        return GMERR_SEMANTIC_ERROR;
    }

    return GMERR_OK;
}

static Status TsSqlVerifyLikeStringExpr(SqlExprLikeT *exprLike, SqlExprColumnT *column)
{
    DB_POINTER2(exprLike, column);
    // verify LIKE string
    if (exprLike->dst->op->type != SQL_EXPR_ITEM_CONST) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "like expr right val not const.");
        return GMERR_SEMANTIC_ERROR;
    }
    DmValueT likeValue = ((SqlExprConstT *)(void *)exprLike->dst->op)->arg;
    if (likeValue.type != DB_DATATYPE_FIXED && likeValue.type != DB_DATATYPE_STRING) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATATYPE_MISMATCH, "like expr.");
        return GMERR_SEMANTIC_ERROR;
    }
    if (!DbStrCmp(likeValue.value.strAddr, "", false)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "empty str in like expr right val.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (likeValue.value.length - 1 >
        column->location.property->size * 2) {  // 2: like右值长度不超过定长字段长度的两倍,+1 用于空字符占位
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE,
            "Curr like expr len %" PRIu32 "exceed double field len.", likeValue.value.length - 1);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

static Status TsSqlVerifyLikeExpr(const DbListT *fromClause, SqlExprT *expr)
{
    DB_POINTER2(fromClause, expr);
    SqlExprLikeT *exprLike = (SqlExprLikeT *)(void *)expr->op;
    if (exprLike->src == NULL || exprLike->dst == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Empty child of like expr.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (SECUREC_UNLIKELY(exprLike->isNot)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "Unsupported not like.");
        return GMERR_SEMANTIC_ERROR;
    }
    // verify LIKE column
    if (exprLike->src->op->type != SQL_EXPR_ITEM_COLUMN) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "like expr left val not col.");
        return GMERR_SEMANTIC_ERROR;
    }
    Status ret = TsSqlVerifyColumnBase(fromClause, exprLike->src);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 校验LIKE左值为定长字段
    SqlExprColumnT *column = CastToSqlExprColumn(exprLike->src->op);
    if (column->location.property->dataType != DB_DATATYPE_FIXED &&
        column->location.property->dataType != DB_DATATYPE_STRING) {
        // Unable to put a field that is not fixed or text type into a like expr.
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "like expr only supports fixed or str type, tbl:%s, col:%s",
            column->tableName, column->columnName);
        return GMERR_SEMANTIC_ERROR;
    }

    // like 不支持ip类型
    if (column->location.property->isInet) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "like expr not support ip type");
        return GMERR_SEMANTIC_ERROR;
    }

    return TsSqlVerifyLikeStringExpr(exprLike, column);
}

Status TsSqlVerifyWhereExpr(ItemOpTreeCtxT *itemCtx, SqlExprT *whereClause)
{
    DB_POINTER2(itemCtx, whereClause);
    Status ret = GMERR_OK;
    if (whereClause->op->type == SQL_EXPR_OP_AND || whereClause->op->type == SQL_EXPR_OP_OR) {
        itemCtx->whereExprDepth++;
    }
    if (itemCtx->whereExprDepth >= TS_WHERE_MAX_DEPTH) {
        // exceeds the max limit: 128
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "where expr depth exceeds limit.");
        return GMERR_SEMANTIC_ERROR;
    }
    if (whereClause->op->type == SQL_EXPR_ITEM_COLUMN) {
        ret = TsSqlVerifyColumnExpr(itemCtx->fromClause, whereClause, itemCtx->isSvLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // verify like expr
    if (whereClause->op->type == SQL_EXPR_OP_LIKE) {
        ret = TsSqlVerifyLikeExpr(itemCtx->fromClause, whereClause);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (whereClause->op->type == SQL_EXPR_OP_FUN) {
        ret = TsSqlVerifyFuncExpr(itemCtx->fromClause, whereClause, itemCtx->isSvLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if ((SqlExprOpTypeE)whereClause->op->type == SQL_EXPR_ITEM_CONST && whereClause->father == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "Const used individually in where expr.");
        return GMERR_SEMANTIC_ERROR;
    }

    if (IsArithExprType(whereClause)) {
        // 校验算术表达式，只支持 a + b > 3格式
        return TsSqlVerifyArithExpr(itemCtx->memCtx, itemCtx->schema, whereClause);
    }
    return ret;
}

Status TsSqlGetChildrenExpr(SqlExprT *expr, SqlExprT **leftChildren, SqlExprT **rightChildren)
{
    DB_POINTER3(expr, leftChildren, rightChildren);
    SqlExprT *left = NULL;
    SqlExprT *right = NULL;
    if ((SqlExprOpTypeE)expr->op->type == SQL_EXPR_OP_AND || (SqlExprOpTypeE)expr->op->type == SQL_EXPR_OP_OR ||
        IsArithExprType(expr) ||
        ((SqlExprOpTypeE)expr->op->type >= SQL_EXPR_OP_LT && (SqlExprOpTypeE)expr->op->type <= SQL_EXPR_OP_NE)) {
        left = expr->children[0];
        right = expr->children[1];
    }
    if (((SqlExprOpTypeE)expr->op->type == SQL_EXPR_OP_AND || (SqlExprOpTypeE)expr->op->type == SQL_EXPR_OP_OR) &&
        ((SqlExprOpTypeE)left->op->type == SQL_EXPR_ITEM_CONST ||
            (SqlExprOpTypeE)right->op->type == SQL_EXPR_ITEM_CONST)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SEMANTIC_ERROR, "AND,OR have const on both sides.");
        return GMERR_SEMANTIC_ERROR;
    }
    *leftChildren = left;
    *rightChildren = right;

    return GMERR_OK;
}
