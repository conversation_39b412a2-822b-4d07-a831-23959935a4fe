/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Parse mini kv dml
 * Author:
 * Create: 2023-12-17
 */

#include "cpl_minikv_parser.h"

#include "adpt_string.h"
#include "ee_mini_dml_desc.h"
#include "ee_mini_session_interface.h"
#include "dm_data_minikv.h"
#include "ee_mini_cursor.h"
#include "ee_mini_dml_desc.h"
#include "srv_mini_proto.h"
#include "gme_kv.h"

static Status MiniGetAndChkLabelName(KvLabelProtoT *proto, TextT *labelName)
{
    labelName->str = (char *)(uintptr_t)proto->labelName;
    labelName->len = DM_STR_LEN(proto->labelName);
    if (DbStrCmp(labelName->str, SYS_NAME, false) == 0) {
        DB_LOG_ERROR(GMERR_RESTRICT_VIOLATION, "labelName is sysTableName, that is forbidden");
        return GMERR_RESTRICT_VIOLATION;
    }
    return GMERR_OK;
}

static Status MiniPrepareDmlKv(MiniRunCtxT *runCtx, bool keyNullable)
{
    KvLabelProtoT *proto = (KvLabelProtoT *)runCtx->session->req;
    runCtx->execParams = DbDynMemCtxAlloc(runCtx->session->memCtx, sizeof(MiniQryKVParamT));
    if (runCtx->execParams == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "read kv params: wrong malloc");
        return GMERR_OUT_OF_MEMORY;
    }
    MiniQryKVParamT *param = (MiniQryKVParamT *)runCtx->execParams;
    (void)memset_s(param, sizeof(MiniQryKVParamT), 0, sizeof(MiniQryKVParamT));
    Status ret = MiniGetAndChkLabelName(proto, &param->labelName);
    if (ret != GMERR_OK) {
        return ret;
    }
    MiniCursorCreateParamT dmlCursorCreateParam = {.labelName = param->labelName.str,
        .memCtx = runCtx->memCtx,
        .dbId = runCtx->session->dbId,
        .seRunCtx = runCtx->session->seRunCtx,
        .dbInstance = runCtx->session->sessionPool->dbInstance};
    ret = MiniCreateKvTableCursor(&dmlCursorCreateParam, &runCtx->labelCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parser create kv table cursor wrong");
        return ret;
    }
    param->key.str = (char *)(uintptr_t)proto->key;
    param->key.len = proto->keyLen;
    if (param->key.len > MAX_MINI_KV_KEY_LEN) {
        DB_LOG_ERROR(GMERR_PROGRAM_LIMIT_EXCEEDED,
            "Para too long "
            "%" PRIu32 " B is larger than %" PRIu32 " B",
            param->key.len, MAX_MINI_KV_KEY_LEN);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    return GMERR_OK;
}

Status MiniParseLoadIdx(MiniRunCtxT *runCtx)
{
    KvLabelProtoT *proto = (KvLabelProtoT *)runCtx->session->req;
    runCtx->execParams = DbDynMemCtxAlloc(runCtx->session->memCtx, sizeof(MiniIndexPreloadParamT));
    if (runCtx->execParams == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "read kv params: wrong malloc");
        return GMERR_OUT_OF_MEMORY;
    }
    MiniIndexPreloadParamT *param = (MiniIndexPreloadParamT *)runCtx->execParams;
    (void)memset_s(param, sizeof(MiniIndexPreloadParamT), 0, sizeof(MiniIndexPreloadParamT));
    Status ret = MiniGetAndChkLabelName(proto, &param->labelName);
    if (ret != GMERR_OK) {
        return ret;
    }
    // read ignoreLeaves
    param->ignoreLeaves = proto->ignoreLeaves;
    MiniCursorCreateParamT cursorCreateParam = {.labelName = param->labelName.str,
        .memCtx = runCtx->memCtx,
        .dbId = runCtx->session->dbId,
        .seRunCtx = runCtx->session->seRunCtx,
        .dbInstance = runCtx->session->sessionPool->dbInstance};
    return MiniCreateKvTableCursor(&cursorCreateParam, &runCtx->labelCursor);
}

static void MiniParseBatchValuesParams(MiniRunCtxT *runCtx, MiniBatchSetParamT *param)
{
    KvBatchProtoT *batchProto = (KvBatchProtoT *)runCtx->session->req;
    param->batchNum = batchProto->batchNum;
    for (uint32_t i = 0; i < param->batchNum; ++i) {
        param->batchValue[i].len = batchProto->values[i].dataLen;
        param->batchValue[i].str = (char *)(uintptr_t)batchProto->values[i].data;
        param->batchKey[i].len = batchProto->keys[i].dataLen;
        param->batchKey[i].str = (char *)(uintptr_t)batchProto->keys[i].data;
    }
}

Status MiniParseBatchSetKv(MiniRunCtxT *runCtx)
{
    KvLabelProtoT *proto = (KvLabelProtoT *)runCtx->session->req;
    MiniBatchSetParamT *param =
        (MiniBatchSetParamT *)DbDynMemCtxAlloc(runCtx->session->memCtx, sizeof(MiniBatchSetParamT));
    if (param == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "exec params malloc unsucc");
        return GMERR_OUT_OF_MEMORY;
    }
    runCtx->execParams = param;
    (void)memset_s(param, sizeof(MiniBatchSetParamT), 0, sizeof(MiniBatchSetParamT));
    Status ret = MiniGetAndChkLabelName(proto, &param->labelName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parser or check labelName unsucc");
        return ret;
    }
    MiniParseBatchValuesParams(runCtx, param);
    MiniCursorCreateParamT batchSetCursorCreateParam = {.labelName = param->labelName.str,
        .memCtx = runCtx->memCtx,
        .dbId = runCtx->session->dbId,
        .seRunCtx = runCtx->session->seRunCtx,
        .dbInstance = runCtx->session->sessionPool->dbInstance};
    ret = MiniCreateKvTableCursor(&batchSetCursorCreateParam, &runCtx->labelCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parser create kv table cursor wrong");
    }
    return ret;
}

Status MiniParseSetKv(MiniRunCtxT *runCtx)
{
    Status ret = MiniPrepareDmlKv(runCtx, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parser prepare set op wrong");
        return ret;
    }
    MiniQryKVParamT *param = (MiniQryKVParamT *)runCtx->execParams;
    KvLabelProtoT *proto = (KvLabelProtoT *)runCtx->session->req;
    param->value.str = (char *)(uintptr_t)proto->value;
    param->value.len = proto->valueLen;
    if (param->value.len > MAX_MINI_KV_VALUE_LEN_32M) {
        DB_LOG_ERROR(GMERR_PROGRAM_LIMIT_EXCEEDED,
            "Para too long "
            "%" PRIu32 " B is larger than %" PRIu32 " B",
            param->value.len, MAX_MINI_KV_VALUE_LEN_32M);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }

    // read insertMode
    param->insertMode = proto->insertMode;
    return GMERR_OK;
}

Status MiniParseGetKv(MiniRunCtxT *runCtx)
{
    return MiniPrepareDmlKv(runCtx, false);
}

static Status MiniParseBatchDeleteParams(MiniRunCtxT *runCtx, MiniBatchDeleteParamT *param)
{
    KvBatchProtoT *batchProto = (KvBatchProtoT *)runCtx->session->req;
    param->batchNum = batchProto->batchNum;
    for (uint32_t i = 0; i < param->batchNum; ++i) {
        param->batchKey[i].len = batchProto->keys[i].dataLen;
        param->batchKey[i].str = (char *)(uintptr_t)batchProto->keys[i].data;
    }
    return GMERR_OK;
}

Status MiniParseBatchDelete(MiniRunCtxT *runCtx)
{
    KvLabelProtoT *proto = (KvLabelProtoT *)runCtx->session->req;
    runCtx->execParams =
        (MiniBatchDeleteParamT *)DbDynMemCtxAlloc(runCtx->session->memCtx, sizeof(MiniBatchDeleteParamT));
    if (runCtx->execParams == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "exec params malloc unsucc");
        return GMERR_OUT_OF_MEMORY;
    }
    MiniBatchDeleteParamT *param = runCtx->execParams;
    (void)memset_s(param, sizeof(MiniBatchDeleteParamT), 0, sizeof(MiniBatchDeleteParamT));
    Status ret = MiniGetAndChkLabelName(proto, &param->labelName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = MiniParseBatchDeleteParams(runCtx, param);
    if (ret != GMERR_OK) {
        return ret;
    }
    MiniCursorCreateParamT batchDelCursorCreateParam = {.labelName = param->labelName.str,
        .memCtx = runCtx->memCtx,
        .dbId = runCtx->session->dbId,
        .seRunCtx = runCtx->session->seRunCtx,
        .dbInstance = runCtx->session->sessionPool->dbInstance};
    ret = MiniCreateKvTableCursor(&batchDelCursorCreateParam, &runCtx->labelCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parser create kv table cursor wrong");
    }
    return ret;
}

Status MiniParseScanKv(MiniRunCtxT *runCtx)
{
    Status ret = MiniPrepareDmlKv(runCtx, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parser prepare scan op wrong");
        return ret;
    }
    MiniQryKVParamT *param = (MiniQryKVParamT *)runCtx->execParams;
    KvLabelProtoT *proto = (KvLabelProtoT *)runCtx->session->req;
    param->scanType = proto->scanMode;
    if (((param->scanType < GME_SCAN_FIRST || param->scanType == GME_SCAN_PREFIX) && param->key.len == 0) ||
        (param->scanType >= GME_SCAN_FIRST && param->scanType <= GME_SCAN_LAST && param->key.len != 0)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "scan type not valid");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status MiniParseMoveKv(MiniRunCtxT *runCtx)
{
    runCtx->labelCursor = runCtx->session->cacheCursor;
    runCtx->execParams = DbDynMemCtxAlloc(runCtx->session->memCtx, sizeof(MiniQryKVParamT));
    if (runCtx->execParams == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "read kv params: wrong malloc");
        return GMERR_OUT_OF_MEMORY;
    }
    MiniQryKVParamT *param = (MiniQryKVParamT *)runCtx->execParams;
    KvLabelProtoT *proto = (KvLabelProtoT *)runCtx->session->req;
    param->moveDirection = proto->moveDirection;
    if (param->moveDirection > GME_MOVE_NEXT) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "prepare: not valid move direction");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status MiniParseGetCollProp(MiniRunCtxT *runCtx)
{
    runCtx->execParams = DbDynMemCtxAlloc(runCtx->session->memCtx, sizeof(MiniCollectionPropertyT));
    if (runCtx->execParams == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "read kv params: wrong malloc");
        return GMERR_OUT_OF_MEMORY;
    }
    KvLabelProtoT *proto = (KvLabelProtoT *)runCtx->session->req;
    MiniCollectionPropertyT *param = (MiniCollectionPropertyT *)runCtx->execParams;
    Status ret = MiniGetAndChkLabelName(proto, &param->labelName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Exec param get collection name fo wrong");
        return ret;
    }
    MiniCursorCreateParamT cursorCreateParam = {.labelName = param->labelName.str,
        .memCtx = runCtx->memCtx,
        .dbId = runCtx->session->dbId,
        .seRunCtx = runCtx->session->seRunCtx,
        .dbInstance = runCtx->session->sessionPool->dbInstance};
    ret = MiniCreateKvTableCursor(&cursorCreateParam, &runCtx->labelCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parser create kv table cursor wrong");
    }
    return ret;
}

Status MiniParseFilterKv(MiniRunCtxT *runCtx)
{
    MiniFilterParamT *param = (MiniFilterParamT *)DbDynMemCtxAlloc(runCtx->session->memCtx, sizeof(MiniFilterParamT));
    if (param == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Filter parse: wrong malloc");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(param, sizeof(MiniFilterParamT), 0, sizeof(MiniFilterParamT));
    runCtx->execParams = param;
    KvFilterProtoT *proto = (KvFilterProtoT *)runCtx->session->req;
    DB_POINTER(proto);
    param->labelName.str = (char *)(uintptr_t)proto->labelName;
    param->labelName.len = DM_STR_LEN(proto->labelName);
    if (DbStrCmp(param->labelName.str, SYS_NAME, false) == 0) {
        DB_LOG_ERROR(GMERR_RESTRICT_VIOLATION, "labelName should not be sys table name");
        return GMERR_RESTRICT_VIOLATION;
    }
    TextT beginT = {proto->beginLen, (char *)(uintptr_t)proto->begin};
    param->begin = beginT;
    TextT endT = {proto->endLen, (char *)(uintptr_t)proto->end};
    param->end = endT;
    param->scanType = proto->scanMode;
    MiniCursorCreateParamT filterCursorCreateParam = {.labelName = param->labelName.str,
        .memCtx = runCtx->memCtx,
        .dbId = runCtx->session->dbId,
        .seRunCtx = runCtx->session->seRunCtx,
        .dbInstance = runCtx->session->sessionPool->dbInstance};
    Status ret = MiniCreateKvTableCursor(&filterCursorCreateParam, &runCtx->labelCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parser create kv table cursor wrong");
    }
    return ret;
}
