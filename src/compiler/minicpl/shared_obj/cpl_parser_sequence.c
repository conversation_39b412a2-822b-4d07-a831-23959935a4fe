/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: complier for mini kv parser
 * Author: wangxiangdong
 * Create: 2024-02-06
 */

#include "cpl_sequence_parser.h"

#include "db_log.h"
#include "dm_meta_shared_obj.h"
#include "ee_session_interface.h"
#include "ee_mini_cursor.h"
#include "gme_shared_obj.h"
#include "srv_mini_proto.h"

#define ATTRIBUTE_TRUE_LEN 5

static Status EmbeddedParseSequenceOperation(MiniRunCtxT *runCtx)
{
    runCtx->execParams = DbDynMemCtxAlloc(runCtx->session->memCtx, sizeof(SequenceOperationCfgT));
    if (SECUREC_UNLIKELY(runCtx->execParams == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Sequence operation params: wrong malloc");
        return GMERR_OUT_OF_MEMORY;
    }

    SequenceOperationCfgT *seqParam = (SequenceOperationCfgT *)runCtx->execParams;
    (void)memset_s(seqParam, sizeof(SequenceOperationCfgT), 0, sizeof(SequenceOperationCfgT));

    GmeSharedObjOpProtoT *proto = (GmeSharedObjOpProtoT *)runCtx->session->req;
    seqParam->labelName.str = proto->labelName;
    seqParam->labelName.len = DM_STR_LEN(proto->labelName);
    seqParam->sequenceName.len = DM_STR_LEN(proto->sharedObjName);
    seqParam->sequenceName.str = (char *)(uintptr_t)proto->sharedObjName;
    return GMERR_OK;
}

static void EmbeddedParseSequenceGetAttrsFromReq(
    MiniRunCtxT *runCtx, uint16_t attrsItemSize, AttributePairT *attrValuesArray)
{
    GmeSharedObjOpProtoT *proto = (GmeSharedObjOpProtoT *)runCtx->session->req;
    AttributeValuePairT *protoAttrValues = proto->attrValuesArray;
    for (uint16_t i = 0; i < attrsItemSize; ++i) {
        AttributePairT *tempAttr = attrValuesArray + i;
        AttributeValuePairT *protoTempAttr = protoAttrValues + i;
        tempAttr->name.str = protoTempAttr->name;
        tempAttr->name.len = protoTempAttr->nameLen;
        tempAttr->valueType = protoTempAttr->valueType;
        if (tempAttr->valueType == DM_ATTRIBUTE_VALUE_STRING_START) {
            tempAttr->value.str = protoTempAttr->value;
            tempAttr->value.len = protoTempAttr->valueLen;
            tempAttr->valueLen = tempAttr->value.len;
        } else if (tempAttr->valueType == DM_ATTRIBUTE_VALUE_BOOL_START) {
            tempAttr->valueLen = (protoTempAttr->valueLen == ATTRIBUTE_TRUE_LEN);
        } else if (tempAttr->valueType == DM_ATTRIBUTE_VALUE_NULL_START) {
            tempAttr->valueLen = 0u;
        } else {
            DB_ASSERT(false);
        }
    }
}

static Status EmbeddedParseSequenceAttrsOperation(
    MiniRunCtxT *runCtx, uint16_t attrsItemSize, AttributePairT **attrsArray)
{
    uint32_t arrayMemLength = attrsItemSize * sizeof(AttributePairT);
    AttributePairT *attrValuesArray = (AttributePairT *)DbDynMemCtxAlloc(runCtx->session->memCtx, arrayMemLength);
    if (SECUREC_UNLIKELY(attrValuesArray == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Sequence attributes array: wrong malloc");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(attrValuesArray, arrayMemLength, 0, arrayMemLength);
    EmbeddedParseSequenceGetAttrsFromReq(runCtx, attrsItemSize, attrValuesArray);
    *attrsArray = attrValuesArray;
    return GMERR_OK;
}

Status EmbeddedParseSequenceInsertImpl(MiniRunCtxT *runCtx)
{
    DB_POINTER(runCtx);
    Status ret = EmbeddedParseSequenceOperation(runCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Parse sequence insert name go wrong");
        return ret;
    }

    SequenceOperationCfgT *seqParam = (SequenceOperationCfgT *)runCtx->execParams;
    GmeSharedObjOpProtoT *proto = (GmeSharedObjOpProtoT *)runCtx->session->req;
    seqParam->index = proto->index;
    seqParam->content.len = DM_STR_LEN(proto->content);
    seqParam->content.str = (char *)(uintptr_t)proto->content;
    seqParam->attrsItemSize = proto->attrsItemSize;
    if (seqParam->attrsItemSize == 0 || seqParam->attrsItemSize == INHERITED_ATTRIBUTE_INDICATOR) {
        seqParam->attrValuesArray = NULL;
        return GMERR_OK;
    }
    return EmbeddedParseSequenceAttrsOperation(runCtx, seqParam->attrsItemSize, &seqParam->attrValuesArray);
}

Status EmbeddedParseSequenceDeleteImpl(MiniRunCtxT *runCtx)
{
    DB_POINTER(runCtx);
    Status ret = EmbeddedParseSequenceOperation(runCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Parse sequence delete name go wrong");
        return ret;
    }

    SequenceOperationCfgT *param = (SequenceOperationCfgT *)runCtx->execParams;
    GmeSharedObjOpProtoT *proto = (GmeSharedObjOpProtoT *)runCtx->session->req;
    param->index = proto->index;
    param->length = proto->length;
    return GMERR_OK;
}

Status EmbeddedParseSequenceReadImpl(MiniRunCtxT *runCtx)
{
    DB_POINTER(runCtx);
    return EmbeddedParseSequenceOperation(runCtx);
}

Status EmbeddedParseSequenceReadDeltaFormatImpl(MiniRunCtxT *runCtx)
{
    DB_POINTER(runCtx);
    Status ret = EmbeddedParseSequenceOperation(runCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Parse sequence read in delta format name go wrong");
        return ret;
    }
    SequenceOperationCfgT *param = (SequenceOperationCfgT *)runCtx->execParams;
    GmeSharedObjOpProtoT *proto = (GmeSharedObjOpProtoT *)runCtx->session->req;
    param->snapshotPair.snapshotLast = proto->snapshotLast;
    param->snapshotPair.snapshotPerv = proto->snapshotPerv;
    return GMERR_OK;
}

Status EmbeddedParseSequenceGetSnapImpl(MiniRunCtxT *runCtx)
{
    DB_POINTER(runCtx);
    Status ret = EmbeddedParseSequenceOperation(runCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Parse sequence get snap name go wrong");
        return ret;
    }
    return GMERR_OK;
}

Status EmbeddedParseSequenceAssignAttributesImpl(MiniRunCtxT *runCtx)
{
    DB_POINTER(runCtx);
    Status ret = EmbeddedParseSequenceOperation(runCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Parse sequence assign attributes name go wrong");
        return ret;
    }

    SequenceOperationCfgT *param = (SequenceOperationCfgT *)runCtx->execParams;
    GmeSharedObjOpProtoT *proto = (GmeSharedObjOpProtoT *)runCtx->session->req;
    param->index = proto->index;
    param->length = proto->length;
    param->attrsItemSize = proto->attrsItemSize;
    if (param->attrsItemSize == 0) {
        param->attrValuesArray = NULL;
        return GMERR_OK;
    } else if (param->attrsItemSize == INHERITED_ATTRIBUTE_INDICATOR) {  // assign : null attribute
        param->attrsItemSize = 0;
        param->attrValuesArray = NULL;
        return GMERR_OK;
    }
    return EmbeddedParseSequenceAttrsOperation(runCtx, param->attrsItemSize, &param->attrValuesArray);
}

Status EmbeddedParseSharedObjHistoryImpl(MiniRunCtxT *runCtx)
{
    DB_POINTER(runCtx);
    Status ret = EmbeddedParseSequenceOperation(runCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Parse shared obj history go wrong");
        return ret;
    }

    SequenceOperationCfgT *param = (SequenceOperationCfgT *)runCtx->execParams;
    GmeSharedObjOpProtoT *proto = (GmeSharedObjOpProtoT *)runCtx->session->req;
    param->opHistoryEnable = proto->opHistoryEnable;
    return ret;
}

Status EmbeddedParseSequenceEmbedInsertImpl(MiniRunCtxT *runCtx)
{
    DB_POINTER(runCtx);
    Status ret = EmbeddedParseSequenceOperation(runCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Sequence embed insert parse sequence name go wrong");
        return ret;
    }

    SequenceOperationCfgT *seqParam = (SequenceOperationCfgT *)runCtx->execParams;
    GmeSharedObjOpProtoT *proto = (GmeSharedObjOpProtoT *)runCtx->session->req;
    seqParam->index = proto->index;
    seqParam->embed.typeLen = proto->embed->typeLen;
    if (SECUREC_UNLIKELY(seqParam->embed.typeLen <= 1)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Sequence embed insert parse embed type go wrong");
        return GMERR_INTERNAL_ERROR;
    }
    seqParam->embed.type = proto->embed->type;
    seqParam->embed.pathLen = proto->embed->pathLen;
    if (SECUREC_UNLIKELY(seqParam->embed.pathLen <= 1)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Sequence embed insert parse embed path go wrong");
        return GMERR_INTERNAL_ERROR;
    }
    seqParam->embed.path = proto->embed->path;
    return GMERR_OK;
}

void SequenceCompileAmFuncInit(void)
{
    SequenceCompileAmFuncT sequenceCompileAmFunc = {.embeddedParseSequenceInsertFunc = EmbeddedParseSequenceInsertImpl,
        .embeddedParseSequenceDeleteFunc = EmbeddedParseSequenceDeleteImpl,
        .embeddedParseSequenceReadFunc = EmbeddedParseSequenceReadImpl,
        .embeddedParseSequenceAssignAttributesFunc = EmbeddedParseSequenceAssignAttributesImpl,
        .embeddedParseSharedObjHistoryFunc = EmbeddedParseSharedObjHistoryImpl,
        .embeddedParseSequenceEmbedInsertFunc = EmbeddedParseSequenceEmbedInsertImpl,
        .embeddedParseSequenceReadDeltaFormatFunc = EmbeddedParseSequenceReadDeltaFormatImpl,
        .embeddedParseSequenceGetSnapFunc = EmbeddedParseSequenceGetSnapImpl};
    SetSequenceCompileAmFunc(&sequenceCompileAmFunc);
}
