/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: query parser subscription
 * Note1：memCtx生命周期为请求级别，请求结束后在函数FastpathPrepareCommand中统一释放
 * Note2: 模块不涉及并发，也不支持并发
 * Author: QE TEAM
 * Create: 2020-10-19
 */

#include "ee_ddl_desc.h"
#include "dm_data_print.h"
#include "adpt_string.h"
#include "adpt_locator.h"
#include "db_sysapp_context.h"
#include "cpl_base_pub.h"
#include "cpl_public_parser_ddl_utils.h"
#include "cpl_log.h"

#ifdef __cplusplus
extern "C" {
#endif

#define STR_BASE 26
#define SUBS_EVENT_SIZE 17
#define SUBS_HASH_SIZE 17
#define SUBS_PRIORITY 2

uint32_t HashStrSimple(const char *word, size_t len)
{
    uint32_t hashV = 0;
    for (size_t i = 0; i < len; i += 1) {
        hashV = hashV * STR_BASE + (uint32_t)((int32_t)word[i] - (int32_t)'a');
    }
    return hashV;
}

uint32_t DbOamapStrComp(const void *key1, const void *key2)
{
    return strcmp(key1, key2) == 0;
}

static DmSubsEventE g_gmdbSubsEvents[SUBS_EVENT_SIZE] = {DM_SUBS_EVENT_INSERT, DM_SUBS_EVENT_UPDATE,
    DM_SUBS_EVENT_DELETE, DM_SUBS_EVENT_REPLACE, DM_SUBS_EVENT_REPLACE_INSERT, DM_SUBS_EVENT_REPLACE_UPDATE,
    DM_SUBS_EVENT_MERGE, DM_SUBS_EVENT_MERGE_INSERT, DM_SUBS_EVENT_MERGE_UPDATE, DM_SUBS_EVENT_KV_SET,
    DM_SUBS_EVENT_AGED, DM_SUBS_EVENT_INIT, DM_SUBS_EVENT_INIT_SCAN, DM_SUBS_EVENT_MODIFY, DM_SUBS_EVENT_INIT_EOF,
    DM_SUBS_EVENT_DIFF, DM_SUBS_EVENT_DIFF_EXPLICIT};

DbOamapT *g_gmdbSubsEventsMap = NULL;
DbMemCtxT *g_gmdbSubStrEventsMemCtx = NULL;

// Call In QryInit to init the func
Status QryCreateSubsEventConfigMap(void)
{
    Status ret = GMERR_OK;
    g_gmdbSubStrEventsMemCtx = DbSrvGetSysDynCtx(DbGetProcGlobalId());
    if (g_gmdbSubStrEventsMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "get sys dyn memctx.");
        return GMERR_INTERNAL_ERROR;
    }
    /* memCtx用途：全局配置表生成
     * 生命周期：长进程级别
     * 释放方式：g_gmdbTopDynamicMemCtx申请的动态内存memCtx 生命周期与其保持一致 依赖上层memCtx销毁
     * 兜底清空措施：g_gmdbTopDynamicMemCtx申请的动态内存memCtx, 进程结束前由QryDestorySubsEventConfigMap释放
     * 并发：请求解析过程不涉及并发
     */
    g_gmdbSubsEventsMap = (DbOamapT *)DbDynMemCtxAlloc(g_gmdbSubStrEventsMemCtx, sizeof(*g_gmdbSubsEventsMap));
    if (g_gmdbSubsEventsMap == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    ret = DbOamapInit(g_gmdbSubsEventsMap, SUBS_HASH_SIZE, DbOamapStrComp, g_gmdbSubStrEventsMemCtx, false);
    if (ret != GMERR_OK) {
        return ret;
    }

    char *subStrEvent[SUBS_EVENT_SIZE] = {"insert", "update", "delete", "replace", "replace insert", "replace update",
        "merge", "merge insert", "merge update", "set", "age", "initial_load", "initial_scan", "modify",
        "initial_load_eof", "diff", "diff_explicit"};

    for (uint32_t i = 0; i < SUBS_EVENT_SIZE; i += 1) {
        size_t len = strlen(subStrEvent[i]);
        ret = DbOamapInsert(
            g_gmdbSubsEventsMap, HashStrSimple(subStrEvent[i], len), subStrEvent[i], &g_gmdbSubsEvents[i], NULL);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

void QryDestroySubsEventConfigMap(void)
{
    DbOamapDestroy(g_gmdbSubsEventsMap);
    DbDynMemCtxFree(g_gmdbSubStrEventsMemCtx, g_gmdbSubsEventsMap);
    g_gmdbSubsEventsMap = NULL;
}

static Status QryParseSubsEvent(const DbJsonT *jsonSubsEvent, DmSubsEventE *subsEvent, DmSubsTypeE subsType)
{
    char eventType[DM_MAX_NAME_LENGTH] = {0};
    Status ret = QryParseStringJson(jsonSubsEvent, "type", eventType, false, DM_MAX_NAME_LENGTH);
    if (ret != GMERR_OK) {
        return ret;
    }

    size_t len = strlen(eventType);
    DmSubsEventE *retriveEvent = DbOamapLookup(g_gmdbSubsEventsMap, HashStrSimple(eventType, len), eventType, NULL);
    if (retriveEvent == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "inv subs event type %s.", eventType);
        return GMERR_DATATYPE_MISMATCH;
    }
    *subsEvent = *retriveEvent;

    if (subsType == STATUS_MERGE &&
        (*subsEvent != DM_SUBS_EVENT_MODIFY && *subsEvent != DM_SUBS_EVENT_DELETE && *subsEvent != DM_SUBS_EVENT_INIT &&
            *subsEvent != DM_SUBS_EVENT_AGED && *subsEvent != DM_SUBS_EVENT_INIT_EOF)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATATYPE_MISMATCH, "event type:%s EXPECT modify, delete age and init in status merge.", eventType);
        return GMERR_DATATYPE_MISMATCH;
    }

    if (subsType == MESSAGE_QUEUE && (*subsEvent == DM_SUBS_EVENT_MODIFY || *subsEvent == DM_SUBS_EVENT_INIT_EOF)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "inv event type:%s in message queue subtype", eventType);
        return GMERR_DATATYPE_MISMATCH;
    }

    return GMERR_OK;
}

static void QryFillDefaultMsgType(uint16_t *subsMsgType, DmSubsEventE subsEvent, DmSubsTypeE subsType)
{
    if (subsEvent == DM_SUBS_EVENT_DIFF || subsEvent == DM_SUBS_EVENT_DIFF_EXPLICIT) {
        *subsMsgType = (uint16_t)(DM_SUBS_MSG_OLD_DATA | DM_SUBS_MSG_NEW_DATA);
        return;
    }
    if (subsType == STATUS_MERGE) {
        if (subsEvent == DM_SUBS_EVENT_DELETE || subsEvent == DM_SUBS_EVENT_AGED) {
            *subsMsgType = (uint16_t)(DM_SUBS_MSG_OLD_DATA);
        } else {
            *subsMsgType = (uint16_t)(DM_SUBS_MSG_NEW_DATA);
        }
        return;
    }
    if (subsEvent == DM_SUBS_EVENT_INIT || subsEvent == DM_SUBS_EVENT_INIT_SCAN) {
        *subsMsgType = (uint16_t)(DM_SUBS_MSG_KEY_DATA | DM_SUBS_MSG_NEW_DATA);
    } else {
        *subsMsgType = (uint16_t)DM_SUBS_MSG_KEY_DATA;
    }
}

#ifdef FEATURE_STREAM
inline static bool QrySubIsStreamLabelSub(QryLabelT *label, DmSubscriptionT *subs)
{
    return subs->labelType == VERTEX_LABEL &&
           label->def.vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_STREAM;
}
#endif

static Status QryParseSubsMsgType(
    const DbJsonT *jsonMsgTypes, uint16_t *subsMsgType, DmSubsEventE subsEvent, QryLabelT *label, DmSubscriptionT *subs)
{
    *subsMsgType = 0;

    // If msgTypes is not specified, the msg is pushed by default.
    if (jsonMsgTypes == NULL) {
        QryFillDefaultMsgType(subsMsgType, subsEvent, subs->subsType);
        return GMERR_OK;
    }
    DbJsonT *jsonMsgType = NULL;
    size_t msgTypeCnt = DbJsonGetArraySize(jsonMsgTypes);
#ifdef FEATURE_STREAM
    if (QrySubIsStreamLabelSub(label, subs) && msgTypeCnt != 1) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "inv msgTypeCnt:%" PRIu32 "", msgTypeCnt);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
#endif
    bool isTcp = DbIsTcp();
    for (uint32_t i = 0; i < msgTypeCnt; i++) {
        jsonMsgType = DbJsonArrayGet(jsonMsgTypes, i);
        const char *msgType = DbJsonStringValue(jsonMsgType);
        if (msgType == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "msgtype should be string.");
            return GMERR_DATATYPE_MISMATCH;
        }
#ifdef FEATURE_STREAM
        if (QrySubIsStreamLabelSub(label, subs) && (strcmp(msgType, "new object") != 0)) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "subscribe event is not new object in stream.");
            return GMERR_INVALID_PARAMETER_VALUE;
        }
#endif
        if (strcmp(msgType, "key") == 0 && !isTcp) {
            *subsMsgType |= DM_SUBS_MSG_KEY_DATA;
        } else if (strcmp(msgType, "old object") == 0) {
            *subsMsgType |= DM_SUBS_MSG_OLD_DATA;
        } else if (strcmp(msgType, "new object") == 0) {
            *subsMsgType |= DM_SUBS_MSG_NEW_DATA;
        } else if (strcmp(msgType, "delta object") == 0) {
            *subsMsgType |= DM_SUBS_MSG_DELTA_DATA;
        } else {
            DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "inv sub message type:%s in %s communication.", msgType,
                isTcp ? "tcp" : "local");
            return GMERR_DATATYPE_MISMATCH;
        }
    }
    return GMERR_OK;
}

static Status QryCheckSubsMsgType(uint16_t *subsMsgType, DmSubsEventE subsEvent)
{
    if ((subsEvent == DM_SUBS_EVENT_INSERT) || (subsEvent == DM_SUBS_EVENT_INIT) ||
        (subsEvent == DM_SUBS_EVENT_INIT_SCAN) || (subsEvent == DM_SUBS_EVENT_MODIFY)) {
        *subsMsgType &= (uint16_t)(~DM_SUBS_MSG_OLD_DATA);
    }
    if ((subsEvent == DM_SUBS_EVENT_DELETE) || (subsEvent == DM_SUBS_EVENT_AGED)) {
        *subsMsgType &= (uint16_t)(~DM_SUBS_MSG_NEW_DATA);
    }
    if ((subsEvent != DM_SUBS_EVENT_UPDATE) && (subsEvent != DM_SUBS_EVENT_MERGE) &&
        (subsEvent != DM_SUBS_EVENT_MERGE_UPDATE)) {
        *subsMsgType &= (uint16_t)(~DM_SUBS_MSG_DELTA_DATA);
    }
    if (*subsMsgType == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "inv sub message type.");
        return GMERR_DATATYPE_MISMATCH;
    }
    return GMERR_OK;
}

static Status QryParseSubsEvents(const DbJsonT *jsonRootSubs, QryLabelT *label, DmSubscriptionT *subs)
{
    DbJsonT *jsonSubsEvents = NULL;
    DbJsonT *jsonSubsEvent = NULL;
    DbJsonT *jsonMsgTypes = NULL;
    size_t eventNum = 0;
    DmSubsEventE subsEvent;
    uint16_t subsMsgType;
    Status ret = QryParseArrayJson(jsonRootSubs, "events", &jsonSubsEvents, DB_MAX_UINT32, &eventNum);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "parse events when parse subs event.");
        return ret;
    }
#ifdef FEATURE_STREAM
    if (QrySubIsStreamLabelSub(label, subs) && eventNum != 1) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Multiple events cannot be sub.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
#endif
    // 此处eventNum为array的实际size，i小于size时json_array_get返回值不可能为NULL，无空指针解引用风险
    for (uint32_t i = 0; i < eventNum; i++) {
        jsonSubsEvent = DbJsonArrayGet(jsonSubsEvents, i);
        ret = QryParseSubsEvent(jsonSubsEvent, &subsEvent, subs->subsType);
        if (ret != GMERR_OK) {
            return ret;
        }
#ifdef FEATURE_STREAM
        if (QrySubIsStreamLabelSub(label, subs) && subsEvent != DM_SUBS_EVENT_INSERT) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Can only subscribe insert event.");
            return GMERR_INVALID_PARAMETER_VALUE;
        }
#endif
        // jsonMsgTypes的判空在QryParseSubsMsgType中进行了，无空指针解引用风险
        jsonMsgTypes = DbJsonObjectGet(jsonSubsEvent, "msgTypes");
        ret = QryParseSubsMsgType(jsonMsgTypes, &subsMsgType, subsEvent, label, subs);
        if (ret != GMERR_OK) {
            return ret;
        }

        ret = QryCheckSubsMsgType(&subsMsgType, subsEvent);
        if (ret != GMERR_OK) {
            return ret;
        }

        subs->subMsgTypes[subsEvent] = subsMsgType;
    }

    // Compatible with V3: When only the delete event is subscribed, the delete and age messages need to be pushed.
    if (subs->subsType == MESSAGE_QUEUE && subs->subMsgTypes[DM_SUBS_EVENT_DELETE] != 0 &&
        subs->subMsgTypes[DM_SUBS_EVENT_AGED] == 0 && subs->labelType == VERTEX_LABEL) {
        subs->subMsgTypes[DM_SUBS_EVENT_AGED] = subs->subMsgTypes[DM_SUBS_EVENT_DELETE];
    }
    return GMERR_OK;
}

inline static bool IsCondSubsSupportedDataType(DmSubsConditionT *conditions)
{
    if (conditions->compareType == DM_COMPARE_OP_EQ) {
        return true;
    }
    DmPropertySchemaT *property = conditions->property;
    return DmIsNumericDataType(property->dataType) || (property->dataType == DB_DATATYPE_STRING);
}

static Status QryParseBoundedIntervalCmpValue(
    QryContextT *qryContext, const DbJsonT *jsonCondition, DmSubsConditionT *conditions)
{
    DbJsonT *jsonLeftValue = DbJsonObjectGet(jsonCondition, "left_value");
    if (jsonLeftValue == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Cond subs value not match compare type.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DmPropertySchemaT *property = conditions->property;
    Status ret = QryParseExtractValue(qryContext, jsonLeftValue, property, &conditions->interval.leftValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbJsonT *jsonRightValue = DbJsonObjectGet(jsonCondition, "right_value");
    if (jsonRightValue == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Cond subs right value not match compare type.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    ret = QryParseExtractValue(qryContext, jsonRightValue, property, &conditions->interval.rightValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 确认区间是否有效
    bool isEmpty = true;
    ret = DmIsEmptyInterval(&conditions->interval, conditions->compareType, &isEmpty);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Cond subs interval check.");
        return ret;
    }
    if (isEmpty) {
        ret = GMERR_INVALID_PARAMETER_VALUE;
        DB_LOG_AND_SET_LASERR(ret, "Condition sub interval is empty.");
        return ret;
    }
    return GMERR_OK;
}

static Status QryParseSubsCompareValue(
    QryStmtT *stmt, const DbJsonT *jsonCondition, DmSubsTypeE subsType, DmSubsConditionT *conditions)
{
    bool isSupported = IsCondSubsSupportedDataType(conditions);
    if (!isSupported) {
        DB_LOG_EMRG_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Cond sub not support dataType: %" PRIu32 ".",
            (uint32_t)conditions->property->dataType);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    QryContextT *qryContext = stmt->context;
    // 解析单边界比较，老订阅equal比较value可以不填，默认为null全匹配
    if (DmIsUnboundedCompareType(conditions->compareType)) {
        DmValueT *subsValue = &conditions->subsValue;
        DbJsonT *jsonSubsValue = DbJsonObjectGet(jsonCondition, "value");
        if (jsonSubsValue == NULL) {
            if (subsType == STATUS_MERGE) {
                DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Status merge subs json condition value is null");
                return GMERR_FEATURE_NOT_SUPPORTED;
            }
            if (conditions->compareType == DM_COMPARE_OP_EQ) {
                subsValue->type = DB_DATATYPE_NULL;
                return GMERR_OK;
            }
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        return QryParseExtractValue(qryContext, jsonSubsValue, conditions->property, subsValue);
    }
    if (!DmIsBoundedCompareType(conditions->compareType)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Unrecognized compare type: %" PRIu32 ".",
            (uint32_t)conditions->compareType);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    // 解析左右边界
    return QryParseBoundedIntervalCmpValue(qryContext, jsonCondition, conditions);
}

static Status QryParseSubsCompareCondition(
    QryStmtT *stmt, const DbJsonT *jsonCondition, DmSubsTypeE subsType, DmSubsConditionT *conditions)
{
    Status ret = GMERR_OK;
    // 解析compare type，默认为equal
    DbJsonT *jsonCmpType = DbJsonObjectGet(jsonCondition, "cmp_type");
    if (jsonCmpType == NULL) {
        conditions->compareType = DM_COMPARE_OP_EQ;
    } else {
        if (!DbJsonIsInteger(jsonCmpType)) {
            ret = GMERR_DATATYPE_MISMATCH;
            DB_LOG_AND_SET_LASERR(ret, "Sub cond compare type should be number.");
            return ret;
        }
        int64_t cmpType = (int64_t)DbJsonIntegerValue(jsonCmpType);
        if (cmpType <= (int64_t)DM_COMPARE_OP_EMPTY || cmpType >= (int64_t)DM_COMPARE_OP_INVALID) {
            ret = GMERR_INVALID_PARAMETER_VALUE;
            DB_LOG_AND_SET_LASERR(ret, "Sub cond compare input type: %" PRId64 "", cmpType);
            return ret;
        }
        conditions->compareType = (DmCompareOpTypeE)cmpType;
    }
    return QryParseSubsCompareValue(stmt, jsonCondition, subsType, conditions);
}

static Status QryParseVertexSubsConditions(QryStmtT *stmt, const DbJsonT *jsonConditions, DmSubsConstraintT *constraint,
    const DmVertexLabelT *vertexLabel, DmSubsTypeE subsType)
{
    Status ret = GMERR_OK;
    DbJsonT *jsonCondition = NULL;
    // 上层调用函数保证conditionNum为实际array的size，i小于size时json_array_get返回值不可能为NULL，无空指针解引用风险
    for (uint32_t i = 0; i < constraint->conditionNum; i++) {
        jsonCondition = DbJsonArrayGet(jsonConditions, i);
        TextT propeName = {};
        DmPathInfoT *pathInfo = &constraint->conditions[i].pathInfo;
        QryParseKeyT parseKey = {"property", &propeName};
        ret = QryParseVarLenString(stmt->context, jsonCondition, &parseKey, false, DM_MAX_NAME_LENGTH);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DbAllocNamePath(stmt->context->memCtx, propeName.str, pathInfo->namePath, &pathInfo->namePathDepth);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "name path is null in parse vertex subs cond.");
            return ret;
        }

        ret = DmLabelGetPropeIdPathByNamePath4QE(
            vertexLabel, pathInfo->namePath, pathInfo->namePathDepth, &pathInfo->idPathDepth, pathInfo->idPath);
        if (ret != GMERR_OK) {
            return ret;
        }

        ret = DmSchemaGetPropertyByIdPath(vertexLabel->metaVertexLabel->schema, (const uint32_t *)pathInfo->idPath,
            pathInfo->idPathDepth, (DmPropertySchemaT **)&constraint->conditions[i].property);
        if (ret != GMERR_OK) {
            return ret;
        }

        if (constraint->conditions[i].property->isSysPrope) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_OBJECT_DEFINITION, "Sub is not allowed for system fields.");
            return GMERR_INVALID_OBJECT_DEFINITION;
        }

        ret = QryParseSubsCompareCondition(stmt, jsonCondition, subsType, &constraint->conditions[i]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

// 条件订阅只对增量事件生效
static Status QryParseVertexSubsConstraint(
    QryStmtT *stmt, const DbJsonT *jsonRootSubs, DmSubscriptionT *subs, const DmVertexLabelT *vertexLabel)
{
    DmSubsConstraintT *constraint = &subs->subsConstraint;
    DbJsonT *jsonConstraint = DbJsonObjectGet(jsonRootSubs, "constraint");
    if (jsonConstraint == NULL) {
        return GMERR_OK;
    }
#ifdef FEATURE_STREAM
    if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_STREAM) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "stream sub not support constraint.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
#endif
    Status ret = QryParseOperatorType(jsonConstraint, &constraint->operatorType);
    if (ret == GMERR_UNDEFINE_COLUMN) {
        ret = GMERR_OK;
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    DbJsonT *jsonConditions = NULL;
    size_t conNum = 0;
    ret = QryParseArrayJson(jsonConstraint, "conditions", &jsonConditions, DM_MAX_SUBS_COND_NUM, &conNum);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "parse cond in parse vertex subs constraints.");
        return ret;
    }
    constraint->conditionNum = (uint32_t)conNum;
    size_t conditionSize = conNum * sizeof(DmSubsConditionT);
    // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
    ret = QryCtxAllocMem(stmt->context, conditionSize, (char **)&constraint->conditions);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "cond is null in parse vertex subs constraints.");
        return ret;
    }
    return QryParseVertexSubsConditions(stmt, jsonConditions, constraint, vertexLabel, subs->subsType);
}

static Status QryParseVertexSubsInfoJson(
    QryStmtT *stmt, const DbJsonT *jsonRootSubs, DmSubscriptionT *subs, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER4(stmt, jsonRootSubs, subs, vertexLabel);
    if (DmIsYangVertexLabel(vertexLabel)) {
        subs->labelVersion = DM_SCHEMA_MIN_VERSION;
        return GMERR_OK;
    }
    Status ret = QryParseVertexSubsConstraint(stmt, jsonRootSubs, subs, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "parse subs constraint when parse subs info json.");
    }
    return ret;
}

#ifdef MERGE_QUEUE
// 增量订阅events中，且msgType不仅仅key
// 全量订阅不合并
static bool IsFilterSubEventType(DmSubscriptionT *subs, DmSubsEventE subEvent)
{
    if (subs->subMsgTypes[subEvent] == 0) {
        return true;
    }
    // insert事件不包含new object在events解析逻辑中校验
    if (subEvent == DM_SUBS_EVENT_UPDATE || subEvent == DM_SUBS_EVENT_REPLACE || subEvent == DM_SUBS_EVENT_MERGE ||
        subEvent == DM_SUBS_EVENT_REPLACE_UPDATE || subEvent == DM_SUBS_EVENT_MERGE_UPDATE) {
        if ((subs->subMsgTypes[subEvent] & DM_SUBS_MSG_NEW_DATA) == 0) {
            DB_LOG_EMRG_AND_SET_LASTERR(GMERR_DATATYPE_MISMATCH, "merge event no subscribe new object");
            return true;
        }
    }
    uint16_t checkMsgType = DM_SUBS_MSG_NEW_DATA | DM_SUBS_MSG_OLD_DATA;
    if ((subs->subMsgTypes[subEvent] & checkMsgType) == 0) {
        DB_LOG_EMRG_AND_SET_LASTERR(GMERR_DATATYPE_MISMATCH, "Merge event msgType no new data and old data");
        return true;
    }
    return subEvent == DM_SUBS_EVENT_INIT || subEvent == DM_SUBS_EVENT_INIT_SCAN ||
           subEvent == DM_SUBS_EVENT_INIT_EOF || subEvent == DM_SUBS_EVENT_TRIGGER_SCAN_BEGIN ||
           subEvent == DM_SUBS_EVENT_TRIGGER_SCAN || subEvent == DM_SUBS_EVENT_TRIGGER_SCAN_END;
}

static Status QryParseSubMergeEvent(const DbJsonT *jsonMergeCond, DmSubscriptionT *subs)
{
    DbJsonT *mergeEvent = NULL;
    size_t mergeEventNum = 0;
    Status ret = QryParseArrayJson(jsonMergeCond, "merge_events", &mergeEvent, DM_SUBS_EVENT_CEIL, &mergeEventNum);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parse mq sub merge event");
        return ret;
    }
    if (mergeEventNum > DM_SUB_MERGE_EVENT_MAX) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "merge event num:%" PRIu32 "", mergeEventNum);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DmSubsEventE *mergeEventArray = subs->mergeCond->mergeEventArray;
    for (size_t i = 0; i < mergeEventNum; ++i) {
        DbJsonT *jsonEvent = DbJsonArrayGet(mergeEvent, i);
        if (jsonEvent == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_JSON_CONTENT, "merge event index: %" PRIu32 "", i);
            return GMERR_INVALID_JSON_CONTENT;
        }
        const char *eventName = DbJsonStringValue(jsonEvent);
        if (eventName == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "mq sub merge event null");
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        size_t len = strlen(eventName);
        DmSubsEventE *event = DbOamapLookup(g_gmdbSubsEventsMap, HashStrSimple(eventName, len), eventName, NULL);
        if (event == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "inv subs event type %s.", eventName);
            return GMERR_DATATYPE_MISMATCH;
        }
        if (IsFilterSubEventType(subs, *event)) {
            DB_LOG_EMRG_AND_SET_LASTERR(GMERR_DATATYPE_MISMATCH, "inv merge sub event type %s", eventName);
            return GMERR_DATATYPE_MISMATCH;
        }
        mergeEventArray[i] = *event;
    }
    subs->mergeCond->mergeEventNum = mergeEventNum;  // mergeEventSum为0则不合并
    return GMERR_OK;
}

static Status QryParseSubMergeKey(
    const QryStmtT *stmt, QryLabelT *label, const DbJsonT *jsonMergeCond, DmMergeConditionT *mergeCond)
{
    DB_POINTER4(stmt, label, jsonMergeCond, mergeCond);
    DbJsonT *jsonMergeKey = NULL;
    size_t mergeKeyNum = 0;
    Status ret = QryParseArrayJson(jsonMergeCond, "merge_keys", &jsonMergeKey, DB_MAX_UINT32, &mergeKeyNum);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parse mq sub merge key");
        return ret;
    }
    if (mergeKeyNum == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "mq merge key field num zero");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    mergeCond->mergeKeyNum = mergeKeyNum;
    // memCtx：parser层统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
    char *buf = NULL;
    ret = QryCtxAllocMem(stmt->context, mergeKeyNum * sizeof(uint32_t), &buf);
    if (ret != GMERR_OK) {
        return ret;
    }
    mergeCond->mergeKeyArray = (uint32_t *)(void *)buf;
    DmVertexLabelT *vertexLabel = label->def.vertexLabel;
    DmSchemaT *schema = vertexLabel->metaVertexLabel->schema;
    for (size_t i = 0; i < mergeKeyNum; ++i) {
        DbJsonT *mergeKey = DbJsonArrayGet(jsonMergeKey, i);
        if (mergeKey == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_JSON_CONTENT, "merge key field index: %" PRIu32 "", i);
            return GMERR_INVALID_JSON_CONTENT;
        }
        const char *mergeKeyName = DbJsonStringValue(mergeKey);
        if (mergeKeyName == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "mq merge key field name null");
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        DmPropertySchemaT *property = NULL;
        ret = DmSchemaGetPropeByName4QE(schema, mergeKeyName, &property);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "merge key name: %s", mergeKeyName);
            return ret;
        }
        if (SECUREC_UNLIKELY(property->isSysPrope)) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "not user defined prope: %s", mergeKeyName);
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        mergeCond->isFixed &= property->isFixed;
        mergeCond->mergeKeyBufLen += property->size;
        mergeCond->mergeKeyArray[i] = property->propeId;
    }
    return GMERR_OK;
}

Status QryParseSubMergeCondition(
    const QryStmtT *stmt, const DbJsonT *jsonField, QryLabelT *label, DmSubscriptionT *subs)
{
    subs->mergeCond = NULL;
    const DbJsonT *jsonMergeCond = DbJsonObjectGet(jsonField, "merge_condition");
    if (SECUREC_LIKELY(jsonMergeCond == NULL)) {
        return GMERR_OK;
    }
    if (subs->subsType != MESSAGE_QUEUE) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Only msg queue sub support merge conditions");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    if (label->def.labelType != VERTEX_LABEL) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "msg queue merge only support vertex label");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    // memCtx：parser层统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
    char *buf = NULL;
    Status ret = QryCtxAllocMem(stmt->context, sizeof(DmMergeConditionT), &buf);
    if (ret != GMERR_OK) {
        return ret;
    }
    subs->mergeCond = (DmMergeConditionT *)buf;
    subs->mergeCond->isFixed = true;
    subs->mergeCond->mergeKeyBufLen = 0;
    for (size_t i = 0; i < DM_SUB_MERGE_EVENT_MAX; ++i) {
        subs->mergeCond->mergeEventArray[i] = DM_SUBS_EVENT_CEIL;
    }
    ret = QryParseSubMergeKey(stmt, label, jsonMergeCond, subs->mergeCond);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return QryParseSubMergeEvent(jsonMergeCond, subs);
}
#endif

static Status QryParseJsonToDmSubs(QryStmtT *stmt, const DbJsonT *jsonRootSubs, QryLabelT *label, DmSubscriptionT *subs)
{
    bool compatibleV3 = QryIsCompatibleV3();
    Status ret;
    if (!compatibleV3) {
        ret = QryParseComment(stmt, jsonRootSubs, &subs->comment, &subs->commentLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    ret = QryParseBoolJson(jsonRootSubs, "is_reliable", &subs->isReliable);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryParseSubType(stmt, jsonRootSubs, &subs->subsType);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t priorityFromJson = 0;
    ret = QryParseUint32Json(jsonRootSubs, "priority", &priorityFromJson, true, true);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (priorityFromJson <= SUBS_PRIORITY) {
        subs->priority = (uint8_t)priorityFromJson;
    } else {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "subs priority should be 0, 1 or 2");
        return GMERR_INVALID_PROPERTY;
    }
    ret = QryParseSubsEvents(jsonRootSubs, label, subs);
    if (ret != GMERR_OK) {
        return ret;
    }
#ifdef MERGE_QUEUE
    return QryParseSubMergeCondition(stmt, jsonRootSubs, label, subs);
#else
    return GMERR_OK;
#endif
}

static Status QrySubsBaseInfoJsonParse(QryStmtT *stmt, const DbJsonT *jsonRootSubs, DmSubscriptionT *subs)
{
    TextT labelName;
    QryParseKeyT parseKey = {"label_name", &labelName};
    Status ret = QryParseVarLenString(stmt->context, jsonRootSubs, &parseKey, false, MAX_TABLE_NAME_LEN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "parse label_name when parse subs.");
        return ret;
    }
    DbText2Str(&labelName, &subs->labelName, &subs->labelNameLen);

    // 用户未指定labelVersion时，设值为DM_SCHEMA_MIN_VERSION，catalog返回最低版本的元数据
    subs->labelVersion = DM_SCHEMA_MIN_VERSION;
    ret = QryParseUint32Json(jsonRootSubs, "schema_version", &(subs->labelVersion), true, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "parse schema_version when parse subs.");
    }
    return ret;
}

static Status QrySubInfoJsonOfLabelParse(
    QryStmtT *stmt, const DbJsonT *jsonRootSubs, QryLabelT *label, DmSubscriptionT *subs)
{
    if (label->def.labelType == KV_TABLE) {
        if (subs->subsType == STATUS_MERGE) {
            DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "status merge sub cannot sub kv table");
            return GMERR_DATATYPE_MISMATCH;
        }
        return QryParseKvSubsInfoJson(stmt, jsonRootSubs, subs);
    } else {
        DmVertexLabelT *vertexLabel = label->def.vertexLabel;
        // subs == MESSAGE QUEUE no matter label's subsType all two option is ok
        if (subs->subsType == STATUS_MERGE && !DmIsLabelSupportStatusMerge(vertexLabel)) {
            DB_LOG_AND_SET_LASERR(
                GMERR_DATA_EXCEPTION, "Status merge sub cannot sub label:%s", vertexLabel->metaCommon.metaName);
            return GMERR_DATA_EXCEPTION;
        }
        if (subs->subsType == MESSAGE_QUEUE && !DmIsLabelSupportMessageQueue(vertexLabel)) {
            DB_LOG_AND_SET_LASERR(
                GMERR_DATA_EXCEPTION, "Message queue sub cannot sub label:%s", vertexLabel->metaCommon.metaName);
            return GMERR_DATA_EXCEPTION;
        }
        return QryParseVertexSubsInfoJson(stmt, jsonRootSubs, subs, vertexLabel);
    }
}
#ifdef FEATURE_STREAM
Status QryVerifyJsonOfSubsFromStream(QryLabelT *label, DmSubscriptionT *subs)
{
    if (!QrySubIsStreamLabelSub(label, subs)) {
        return GMERR_OK;
    }
    if (subs->subsType != MESSAGE_QUEUE) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "stream subs type should be message_queue");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (subs->isReliable) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "stream subs should be unreliable");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}
#endif

inline static bool QrySubNotSupportedTsLabel(DmVertexLabelTypeE labelType)
{
    return labelType == VERTEX_TYPE_TS_LOGICAL;
}

static Status QrySubInfoSetDefalutValueByType(QryLabelT *label, DmSubscriptionT *subs, bool *disableSubBackPressure)
{
    if (label->def.labelType == KV_TABLE) {
        subs->labelId = label->def.kvLabel->metaCommon.metaId;
        *disableSubBackPressure = label->def.kvLabel->disableSubBackPressure;
    } else {
        subs->labelId = label->def.vertexLabel->metaCommon.metaId;
        *disableSubBackPressure = label->def.vertexLabel->metaVertexLabel->disableSubBackPressure;
        if (subs->labelVersion == DM_SCHEMA_MIN_VERSION) {
            subs->labelVersion = label->def.vertexLabel->metaCommon.version;
        }
        if (QrySubNotSupportedTsLabel(label->def.vertexLabel->metaVertexLabel->vertexLabelType)) {
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Logical and stream tables cannot be sub in ts.");
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
#ifdef FEATURE_STREAM
        if (QrySubIsStreamLabelSub(label, subs)) {
            DmStreamVertexLabelT *streamLabel = (DmStreamVertexLabelT *)label->def.vertexLabel;
            if (streamLabel->streamType != STREAM_LABEL_TYPE_SINK ||
                streamLabel->streamMeta.sinkMeta.dispenser.sinkType != STREAM_SINK_PUBSUB) {
                DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Non-sink tables cannot be sub in stream.");
                return GMERR_FEATURE_NOT_SUPPORTED;
            }
        }
#endif
    }
    return GMERR_OK;
}

static Status QrySubsInfoJsonParse(QryStmtT *stmt, const DbJsonT *jsonRootSubs, DmSubscriptionT *subs)
{
    Status ret = QrySubsBaseInfoJsonParse(stmt, jsonRootSubs, subs);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "parse subs base info %s.", subs->metaCommon.metaName);
        return ret;
    }
    QryLabelT *label = NULL;
    ret = QryAddLabel((QryStmtT *)stmt, QRY_LABEL_TYPE_BOTH, subs->labelName, subs->labelVersion, &label);
    if (ret != GMERR_OK) {
        return ret;
    }
    subs->priority = 0;
    DbSetLabelNameForStmt(stmt, subs->labelName);
    subs->labelType = label->def.labelType;
    bool disableSubBackPressure = false;
    ret = QrySubInfoSetDefalutValueByType(label, subs, &disableSubBackPressure);
    if (ret != GMERR_OK) {
        return ret;
    }
    if ((ret = QryParseJsonToDmSubs(stmt, jsonRootSubs, label, subs)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "parse subs info %s.", subs->metaCommon.metaName);
        return ret;
    }
#ifdef FEATURE_STREAM
    ret = QryVerifyJsonOfSubsFromStream(label, subs);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif
    if (subs->isReliable && disableSubBackPressure) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED,
            "label: %s with disableSubBackPressure do not support reliable sub.", subs->labelName);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    return QrySubInfoJsonOfLabelParse(stmt, jsonRootSubs, label, subs);
}

inline static bool InvalidUserCbFuncName(const TextT *funcName)
{
    // funcName->len 包括字符串结束符
    return funcName->len == 0 || funcName->len > DB_USER_CALLBACK_FUNC_NAME_LEN_MAX || funcName->str == NULL;
}

static Status QrySubscriptionParse(QryStmtT *stmt, const TextT *subsName, const TextT *subsInfoJson,
    const TextT *subsUserCbFuncName, DmSubscriptionT **retSubscription)
{
    DmSubscriptionT *subscription = NULL;
    if (subsName->len > DM_MAX_NAME_LENGTH || subsName->len == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY,
            "subs name len should between: (%" PRIu32 ", %" PRIu32 "), usr len:%" PRIu32 ".", 0, DM_MAX_NAME_LENGTH,
            subsName->len);
        return GMERR_INVALID_PROPERTY;
    }
    static_assert(DB_USER_CALLBACK_FUNC_NAME_LEN_MAX >= DM_MAX_NAME_LENGTH, "func name array is too small");
    if (InvalidUserCbFuncName(subsUserCbFuncName)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "Subs user callback func name:%s, len %" PRIu32,
            (subsUserCbFuncName->str == NULL ? "NULL" : subsUserCbFuncName->str), subsUserCbFuncName->len);
        return GMERR_INVALID_PROPERTY;
    }
    // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
    Status ret = QryCtxAllocMem(stmt->context, sizeof(DmSubscriptionT), (char **)&subscription);
    if (ret != GMERR_OK) {
        return ret;
    }
    // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
    ret = QryCtxAllocMem(stmt->context, subsName->len, &(subscription->metaCommon.metaName));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "sub meta name is null");
        return ret;
    }
    errno_t err = strcpy_s(subscription->metaCommon.metaName, subsName->len, subsName->str);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "copy subs name.");
        return GMERR_FIELD_OVERFLOW;
    }
    subscription->subsUserCbFuncName = subsUserCbFuncName->str;
    subscription->subsJson.len = subsInfoJson->len;
    subscription->subsJson.str = subsInfoJson->str;
    subscription->isGroupCreate = stmt->session->externalUser.isGroupLogin;

    ret = QryParseUserName(stmt, &subscription->creator);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "parse creator when parse sub.");
        return ret;
    }

    DbJsonT *jsonRootSubs = DbLoadJsonSrv(subsInfoJson->str, DB_JSON_REJECT_DUPLICATES);
    if (jsonRootSubs == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_JSON_CONTENT, "inv sub json.");
        return GMERR_INVALID_JSON_CONTENT;
    }

    ret = QrySubsInfoJsonParse(stmt, jsonRootSubs, subscription);
    DbJsonDelete(jsonRootSubs);
    *retSubscription = subscription;
    return ret;
}

static Status QryParseCreateSubscriptionInner(
    QryStmtT *stmt, TextT *subsInfoJson, TextT *channelKeys, DmSubscriptionT **subscription)
{
    TextT subsName;
    FixBufferT *req = stmt->session->req;
    Status ret = FixBufGetTextExNullable(req, &subsName);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "fixbuf: get subs name");
        return ret;
    }
    ret = FixBufGetTextExNullable(req, subsInfoJson);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "fixbuf: get subs json");
        return ret;
    }
    ret = FixBufGetTextExNullable(req, channelKeys);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "fixbuf: get channel keys");
        return ret;
    }
    TextT subsUserCbFuncName;  // 订阅用户回调函数名
    ret = FixBufGetTextExNullable(req, &subsUserCbFuncName);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "fixbuf: get sub user callback func name");
        return ret;
    }
    if (channelKeys->len == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "ChannelKeys len is 0.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (subsInfoJson->len == 0 || subsInfoJson->len > DB_MEBI) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "subs json len should is 0 or more than 1MB.");
        return GMERR_DATA_EXCEPTION;
    }
    ret = QrySubscriptionParse(stmt, &subsName, subsInfoJson, &subsUserCbFuncName, subscription);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status QryParseCreateSubscription(QryStmtT *stmt)
{
    DmSubscriptionT *subscription = NULL;
    TextT subsInfoJson, channelKeys = {};
    QryCreateSubscriptionDescT *desc = NULL;

    // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
    Status ret = QryCtxAllocMem(stmt->context, sizeof(QryCreateSubscriptionDescT), (char **)&desc);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "parse create subscription.");
        return ret;
    }
    /* get req from FixBuff */
    ret = QryParseCreateSubscriptionInner(stmt, &subsInfoJson, &channelKeys, &subscription);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "parse create subscription.");
        return ret;
    }

    DbLinkedListInit(&subscription->channelList);
    char *buf = NULL;
    uint32_t size = (uint32_t)sizeof(DmChannelNodeT);
    // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
    ret = QryCtxAllocMem(stmt->context, size, &buf);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmChannelNodeT *chanNode = (DmChannelNodeT *)(void *)buf;
    DbRWSpinInit(&chanNode->rwlock);
    chanNode->isUnBind = false;
    chanNode->channelName.len = channelKeys.len;
    chanNode->channelName.str = channelKeys.str;
    subscription->channelCount++;
    DbLinkedListAppend(&subscription->channelList, &chanNode->linkedNode);
    subscription->metaCommon.namespaceId = stmt->session->namespaceId;
    desc->subscription = subscription;
    stmt->context->entry = (void *)desc;
    return GMERR_OK;
}

Status QryParseDropSubscription(QryStmtT *stmt)
{
    QryDropSubscriptionDescT *dropSubscriptionDesc = NULL;
    // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
    Status ret =
        QryCtxAllocMem(stmt->context, (uint32_t)sizeof(QryDropSubscriptionDescT), (char **)&dropSubscriptionDesc);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "parse DropSubscription");
        return ret;
    }

    // get req from FixBuff
    ret = FixBufGetTextEx(stmt->session->req, &dropSubscriptionDesc->subsName);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get subs name when parse drop subs.");
        return ret;
    }
    ret = FixBufGetUint32(stmt->session->req, &dropSubscriptionDesc->subId);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get subId when parse drop sub, subName: %s", dropSubscriptionDesc->subsName.str);
        return ret;
    }
    dropSubscriptionDesc->namespaceId = stmt->session->namespaceId;
    stmt->context->entry = (void *)dropSubscriptionDesc;
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
