/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: utils for keywords and property type
 * Author: lihainuo
 * Create: 2023-05-10
 */

#ifndef CPL_PUBLIC_SQL_KEYWORDS_H
#define CPL_PUBLIC_SQL_KEYWORDS_H

#include "adpt_string.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct SqlKeyWordInfo {
    const char *name;  // in lower case
    uint32_t token;    // grammar's token code
} SqlKeyWordInfoT;

const SqlKeyWordInfoT *GetSqlKeyWordInfo(const char *str, const SqlKeyWordInfoT *keyWordInfo, int32_t numSqlKeyword);

#ifdef __cplusplus
}
#endif

#endif
