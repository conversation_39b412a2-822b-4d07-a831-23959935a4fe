/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: data structures for sql parser
 * Author: COMPILER TEAM
 * Create: 2025-01-07
 */

#ifndef CPL_PUBLIC_SQL_PARSER_COMMON_BASE_H
#define CPL_PUBLIC_SQL_PARSER_COMMON_BASE_H

#include <setjmp.h>
#include "adpt_types.h"
#include "db_list.h"
#include "db_privileges.h"
#include "ee_plan_node_ddl.h"
#include "cpl_ir_plan.h"
#include "cpl_public_sql_expr.h"
#define SQL_LIST_EXTEND_SIZE 2

#define SQL_CMD_LEN_MAX (1024 * 1024)  // max sql cmd length
#define TS_SQL_CMD_LEN_MAX 65535       // max ts cmd length

/* max error msg size */
#define MAX_ERROR_MSG_SIZE 128

#define SQL_NAME_LEN_MAX 128                 // max col name length
#define SQL_COL_NUM_MAX 256                  // max number of column
#define SQL_WITH_NUM_MAX 256                 // max number of with column
#define INT64_MIN_NUM "9223372036854775808"  // int64最小值去掉负号后的数值

/*
    join type values allowed in SrcItem.jointype
    Remark: SQlite also defined a nother type of join "JT_LTORJ" when right join appears
        It is is used in query optimization. We do not include this type currently.
*/
#define SQL_INNER_JOIN 0x01    // INNER OR CROSS JOIN
#define SQL_CROSS_JOIN 0x02    // when "CROSS" appears
#define SQL_NATURAL_JOIN 0x04  // NATURAL JOIN
#define SQL_LEFT_JOIN 0x08     // LEFT OUTER JOIN
#define SQL_RIGHT_JOIN 0x10    // RIGHT OUTER JOIN
#define SQL_OUTER_JOIN 0x20    // When "OUTER" appears
#define SQL_ERROR_JOIN 0x80    // ERROR

#ifdef __cplusplus
extern "C" {
#endif

typedef enum SqlExplainType {
    EXPLAIN_EXECUTE_PLAN,
    EXPLAIN_LOGICAL_PLAN,
    EXPLAIN_PHYSICAL_PLAN,
} SqlExplainTypeE;

/* structures for drop stmt */
typedef enum SqlDropType {
    DROP_TABLE,
    DROP_INDEX,
    DROP_TRIGGER,
    DROP_VIEW,
#ifdef FEATURE_STREAM
    DROP_STREAM_SOURCE,
    DROP_STREAM_VIEW,
    DROP_STREAM_SINK,
#endif
} SqlDropTypeE;

#ifdef FEATURE_TS
typedef enum SqlTruncateType {
    TRUNCATE_TABLE,
} SqlTruncateTypeE;
#endif

typedef enum {
    COMPRESS_DEFAULT = 0,
    COMPRESS_NONE,
    COMPRESS_END,
} ColumnCompressModeE;

typedef enum {
    SQL_SORT_UNDEFINED,
    SQL_SORT_ASC,
    SQL_SORT_DESC,
    SQL_SORT_END,
} SqlSortTypeE;  // 与 DmSortTypeE 一致

/* structures for select stmt */
/*
    In a SELECT statement, 'name' refers to the corresponding alias name in the AS clause
    'value' refers to the expression value before the AS clause
*/
typedef enum SqlSelectType {
    SELECT_SIMPLE,
    SELECT_UNION,
    SELECT_UNIONALL,
    SELECT_EXCEPT,
    SELECT_INTERSECT,
} SqlSelectTypeE;

typedef enum {
    SRC_TABLE_REF,
    SRC_SELECT_SUB,
    SRC_JOIN_LIST,
    SRC_CTE,
    SRC_STREAM_WINDOW,
    SRC_STREAM_DISPATCH
} SqlSrcItemTypeE;

typedef enum SqlNullsPosType {
    SQL_SORT_NULLS_UNDEFINE,  // default, nulls_first for ascending, nulls_last for descending
    SQL_SORT_NULLS_FIRST,
    SQL_SORT_NULLS_LAST,
} SqlNullsPosTypeE;

/* resolve type when conflict */
typedef enum SqlResolveType {
    RESOLVE_NONE,     /* There is no constraint to check */
    RESOLVE_ABORT,    /* Back out changes but do no rollback transaction */
    RESOLVE_ROLLBACK, /* Fail the operation and rollback the transaction */
    RESOLVE_FAIL,     /* Stop the operation but leave all prior changes */
    RESOLVE_IGNORE,   /* Ignore the error. Do not do the INSERT or UPDATE */
    RESOLVE_REPLACE,  /* Delete existing record, then do INSERT or UPDATE */
    RESOLVE_END,
} SqlResolveTypeE;

typedef enum SqlCTEMaterializedType {
    CTE_MATERIALIZE_ANY = 0,  // any
    CTE_MATERIALIZE,          // MATERIALIZED
    CTE_DEMATERIALIZE         // NOT MATERIALIZED
} SqlCTEMaterializedTypeE;

typedef enum SqlTrxAction {
    TRX_ACTION_DEFERRED,
    TRX_ACTION_IMMEDIATE,
    TRX_ACTION_EXCLUSIVE,
} SqlTrxActionT;

#ifdef __cplusplus
}
#endif

#endif  // CPL_PUBLIC_SQL_PARSER_COMMON_BASE_H
