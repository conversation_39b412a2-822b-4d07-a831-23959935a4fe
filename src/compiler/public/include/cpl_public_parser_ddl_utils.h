/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: query parse ddl
 * Author: xuxinxin
 * Create: 2020-09-14
 */

#ifndef CPL_PUBLIC_PARSER_DDL_UTILS_H
#define CPL_PUBLIC_PARSER_DDL_UTILS_H

#include "db_json.h"
#include "ee_stmt.h"
#include "ee_ddl_desc.h"

#ifdef __cplusplus
extern "C" {
#endif

#define QRY_GET_BITS_MAX_VALUE(bitsSize) ((1ULL << (bitsSize)) - 1)

typedef Status (*ExtractValueFuncT)(const DmPropertySchemaT *property, const DbJsonT *jsonValue, DmValueT *value);

typedef struct QryParseKey {
    const char *key;
    TextT *value;
} QryParseKeyT;

typedef struct QryParseCfg {
    bool isNull;
    bool isZero;
    bool mustBeNull;
} QryParseCfgT;

Status QryParseVarLenString(
    QryContextT *context, const DbJsonT *jsonField, const QryParseKeyT *parseKey, bool isNull, const uint32_t maxLen);
Status QryParseBoolJsonCheckNull(const DbJsonT *jsonRoot, const char *key, bool *value, bool *isNull);
Status QryParseBoolJsonWithDefault(const DbJsonT *jsonRoot, const char *key, bool *value, bool defaultVal);
Status QryParseBoolJson(const DbJsonT *jsonRoot, const char *key, bool *value);
// 解析整形或bool，输出为整形
Status QryParseInt64JsonAndBool(const DbJsonT *jsonRoot, const char *key, int64_t *value);
Status QryParseInt32Json(const DbJsonT *jsonRoot, const char *key, int32_t *value, const bool isNull);
Status QryParseUint32Json(
    const DbJsonT *jsonRoot, const char *key, uint32_t *value, const bool isNull, const bool isZero);
Status QryParseUint32JsonWithRange(
    const DbJsonT *jsonRoot, const char *key, uint32_t *value, const uint32_t leftValue, const uint32_t rightValue);
Status QryParseUint64Json(const DbJsonT *jsonRoot, const char *key, uint64_t *value, const QryParseCfgT parseCfg);
Status QryParseArrayJson(
    const DbJsonT *jsonRoot, const char *key, DbJsonT **value, const uint32_t maxLen, size_t *sizeNum);
Status QryParseOperatorType(const DbJsonT *jsonConstraint, DmLogicalOperatorE *operatorType);
Status QryParseSetPropertyBitDataType(DbMemCtxT *memCtx, const char *type, DmPropertySchemaT *property);
Status QryParseSetPropertyDataType(
    const char *type, DmSchemaT *schema, DmPropertySchemaT *property, const DbJsonT *jsonField);
Status QryParseExtractValue(
    QryContextT *context, const DbJsonT *jsonValue, const DmPropertySchemaT *property, DmValueT *value);
Status QryParseComment(QryStmtT *stmt, const DbJsonT *jsonField, char **comment, uint32_t *commentLen);
Status QryParseExtractCfgLite(const DbJsonT *jsonRoot, ConcurrencyControlE *ccType, bool defaultVal);
Status QryParseClusterHashWithCfg(const DbJsonT *jsonRoot, bool *enableClusterHash, bool defaultVal);
Status QryParseConditionCompareType(const DbJsonT *jsonCondition, DmCondCompareTypeE *compareType);
Status QryParseFlowCtrlConfig(const DbJsonT *jsonRoot, uint64_t *subFlowControlSleepTime);

Status QryParseUserName(QryStmtT *stmt, char **buf);
Status QryExtractBoolJsonInner(const DbJsonT *jsonValue, bool *value);

Status QryParseSubType(const QryStmtT *stmt, const DbJsonT *jsonField, DmSubsTypeE *subsType);
Status QryParseSubFlag(const DbJsonT *jsonField, DmLabelSubsTypeE *labelSubsType);

Status QryParseStringJson(
    const DbJsonT *jsonRoot, const char *key, char *value, const bool isNull, const uint32_t maxLen);

Status QryParseExtractValueCheckLength(const DmPropertySchemaT *property, uint32_t length);

bool QryCheckFileSuffix(const char *filePath, const char *matchStr);
Status QryLoadJsonFile(char *filePath, DbJsonT **json);
char *QryImportReadDataFromFile(DbMemCtxT *memCtx, const char *filePath, size_t *fileSize);
Status QryGetConfigJson(char *filePath, TextT *configJson, DbMemCtxT *memCtx);
Status QryParseUserOrGrpInfoFromJson(
    const DbJsonT *json, TextT *process, TextT *userOrGrp, bool isUser, DbMemCtxT *memCtx);
Status QryParseTspInfoFromConfig(const DbJsonT *json, QryTspConfT *tspConf, DbMemCtxT *memCtx);
Status QryParseTspInfoFromConfigJson(const TextT *configJson, QryTspConfT *tspConf, DbMemCtxT *memCtx);
Status QryParseIndexLabelCfg(
    const DbJsonT *jsonRoot, DmIndexLabelCfgT *indexLabelCfg, bool *hashTypeSet, bool *initHashCapacitySet);
void AccessIndexCfg(DmVlIndexLabelT *index, ContainerTypeE containerType);

#ifdef __cplusplus
}
#endif
#endif /* CPL_PUBLIC_PARSER_DDL_UTILS_H */
