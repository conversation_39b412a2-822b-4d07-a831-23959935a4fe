/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: this file contains useful funcs will be used in datalog runtime parser.
 * Note1：memCtx生命周期为短进程级别，由service在dml操作执行完成或异常退出前后在DatalogEntry中调用DbDeleteDynMemCtx统一释放
 * Note2: 请求解析过程不涉及并发
 * Author:
 * Create: 2022-08-01
 */

#include "cpl_dtl_online_compiler.h"
#include "cpl_public_verifier_ddl.h"
#include "cpl_public_verifier.h"
#include "db_last_error.h"
#include "db_list.h"
#include "db_rpc_msg_op.h"
#include "db_text.h"
#include "ee_delta_table_info_access_method.h"
#include "adpt_string.h"
#include "dm_meta_prop_label.h"
#include "dm_data_math.h"
#include "cpl_log.h"
#include "ee_reserved_names.h"

#define DATALOG_PROGRAM_NAME_LEN_MIN 4

static Status DtlParseSoPath(DbMemCtxT *memCtx, FixBufferT *req, TextT *soPath);
static Status DtlParseDistribute(FixBufferT *req, uint32_t *isDistribute);

/*
 * description: 解析datalog加载所需参数，参数解析接口调用顺序与报文填充顺序一致，不得随意调整
 * param {DbMemCtxT} *memCtx：初始化参数内存
 * param {DtlImportParamT} *loadParam：加载参数
 * param {FixBufferT} *req：datalog加载报文
 * return {*} 参数解析结果
 */
Status ParseDatalogImportParam(DbMemCtxT *memCtx, DtlImportParamT *loadParam, FixBufferT *req)
{
    DB_POINTER3(loadParam, memCtx, req);
    RpcSeekFirstOpMsg(req);

    Status ret = DtlParseSoPath(memCtx, req, &loadParam->soPath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Datalog so path.");
        return ret;
    }
    ret = DtlParseSoName(memCtx, loadParam->soPath.str, &loadParam->soName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "So name.");
        return ret;
    }
    ret = DtlParseNspName(memCtx, req, &loadParam->namespaceName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Namespace name.");
        return ret;
    }

    // 校验加载的datalog namespace名
    char *nspName = loadParam->namespaceName.str;
    ret =
        QryCheckObjectNameConstraint(nspName, loadParam->namespaceName.len, DM_MAX_DATALOG_NAMESPACENAME_LENGTH, false);
    if (ret != GMERR_OK) {
        // violates constraint
        DB_LOG_ERROR(ret, "Namespace %s.", nspName);
        return ret;
    }
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(memCtx);
    ret = CataGetNamespaceIdByName(dbInstance, nspName, &loadParam->nspId);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get nspId, soName=%s, nspName=%s.", loadParam->soName.str, nspName);
        return ret;
    }

    ret = DtlParseDistribute(req, (uint32_t *)(void *)&loadParam->isDistribute);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Distribute switch.");
        return ret;
    }
    return GMERR_OK;
}

// 获取req content
inline static void RpcSeekContent(FixBufferT *msg)
{
    uint32_t offset = MSG_HEADER_ALIGN_SIZE;
    FixBufSeek(msg, offset);
}

// 获取MsgHeaderT
inline static bool DtlBatchDmlSupportOP(uint32_t opcode)
{
    return opcode == MSG_OP_RPC_INSERT_VERTEX || opcode == MSG_OP_RPC_DELETE_VERTEX;
}

static Status DtlBatchPrepare(FixBufferT *req, uint32_t *totalNum)
{
    const BatchHeaderT *batchHeader = FixBufGetData(req, sizeof(BatchHeaderT));
    if (batchHeader == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "datalog request message.");
        return GMERR_DATA_EXCEPTION;
    }
    if (batchHeader->batchType == GMC_BATCH_YANG) {
        // should be GMC_BATCH_NORMAL
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "datalog batchType.");
        return GMERR_DATATYPE_MISMATCH;
    }
    uint32_t batchCmdMaxNum = DB_MAX_BATCH_OP_NUM_CONFIG_MAX * DB_MAX_BATCH_OP_NUM_CONFIG_UNIT;
    if (batchHeader->totalOpNum == 0 || batchHeader->totalOpNum > batchCmdMaxNum) {
        DB_LOG_AND_SET_LASERR(
            GMERR_PROGRAM_LIMIT_EXCEEDED, "The number of datalog batch op not in range(1, %u).", batchCmdMaxNum);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    *totalNum = batchHeader->totalOpNum;
    return GMERR_OK;
}

static inline Status VerifyDtlVertexLabelById(DmVertexLabelT *vertexLabel)
{
    if (!DmVertexLabelIsDatalogLabel(vertexLabel)) {
        // datalog delete or update request only support datalog table
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "table %s not dtl tbl", vertexLabel->metaCommon.metaName);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

static Status DtlFillNamedTableByIndexKey(DbMemCtxT *memCtx, const DmVertexLabelT *vertexLabel,
    DmVlIndexLabelT *indexLabel, DmIndexKeyT *indexKey, DtlNamedTableT *namedTable)
{
    // 解析IndexKeyValue
    DmValueT propValues[DM_MAX_KEY_PROPE_NUM];
    Status ret = DmGetPropeValuesFromKeyBuf(indexKey->keyBuf, indexLabel, propValues);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Get prop vals buf: index = %s.vertex name %s", indexLabel->indexName,
            vertexLabel->metaCommon.metaName);
        return ret;
    }

    // 填充常量字段
    for (uint32_t i = 0; i < indexLabel->propeNum; i++) {
        // 元数据中的propeId包含了首位的count，namedTable->params不包含count
        uint32_t propId = indexLabel->propIds[i];
        DtlFieldT *dtlField = DbListItem(&namedTable->params, indexLabel->properties[propId].propeId - COUNT_OFFSET);
        dtlField->value = propValues[i];
        dtlField->isFilterField = true;
        dtlField->isUsed = true;
    }

    namedTable->isFilter = true;
    namedTable->isProject = true;
    namedTable->indexKey = indexKey;
    namedTable->labelId = vertexLabel->metaCommon.metaId;
    return ret;
}

static bool DtlIsUserDefinedField(DmPropertySchemaT *properties)
{
    if (properties->isSysPrope) {
        return false;
    }
    if (DbStrCmp(properties->name, DTL_RSVD_FIELD_NAME_COUNT, false) == 0) {
        return false;
    }
    return true;
}

static Status DtlFillNamedTableByVertexLabel(
    DbMemCtxT *memCtx, const DmVertexLabelT *vertexLabel, DtlNamedTableT *namedTable)
{
    // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
    char *labelName = vertexLabel->metaCommon.metaName;
    namedTable->name = DbDynMemCtxAlloc(memCtx, strlen(labelName) + 1);
    if (namedTable->name == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc namedTable name (%s).", labelName);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(namedTable->name, strlen(labelName) + 1, labelName);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "copy namedTable name (%s).", labelName);
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t propeNum = vertexLabel->metaVertexLabel->schema->propeNum;
    DbCreateListWithExtendSize(&namedTable->params, sizeof(DtlFieldT), propeNum, memCtx);
    DmPropertySchemaT *properties = vertexLabel->metaVertexLabel->schema->properties;
    for (uint32_t i = 0; i < propeNum; i++) {
        if (!DtlIsUserDefinedField(&properties[i])) {
            continue;
        }
        // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
        DtlFieldT field = {.name = DbDynMemCtxAlloc(memCtx, strlen(properties[i].name) + 1),
            .index = i - COUNT_OFFSET,  // 元数据中的propeId包含了首位的count，namedTable->params不包含count
            .size = (uint16_t)properties[i].size,
            .type = properties[i].dataType,
            .isUsed = false,
            .isFilterField = false};
        if (field.name == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "allocfield name (%s).", properties[i].name);
            return GMERR_OUT_OF_MEMORY;
        }
        err = strcpy_s((void *)field.name, strlen(properties[i].name) + 1, properties[i].name);
        if (err != EOK) {
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "copy field name (%s).", properties[i].name);
            return GMERR_DATA_EXCEPTION;
        }
        Status ret = DbAppendListItem(&namedTable->params, &field);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_AND_SET_LASERR(ret, "append field(%s) of vertex(%s) to field list.", field.name, labelName);
            return ret;
        }
    }
    return GMERR_OK;
}

static Status DtlFillVertexLabel2Ast(const uint32_t labelId, DmVertexLabelT **outVertexLabel, DtlRawAstT *dtlRawAst)
{
    DmVertexLabelT *vertexLabel = NULL;
    // 放入dtlRawAst->vertexLabels的vertex元数据引用信息由service执行完dml操作后调用DtlReleaseVertexFromAst释放
    // 否则手动释放
    Status ret = CataGetVertexLabelById(NULL, labelId, &vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get vertex label by id(%" PRIu32 ").", labelId);
        return ret;
    }
    // 校验是否是datalog表
    ret = VerifyDtlVertexLabelById(vertexLabel);
    if (ret != GMERR_OK) {
        goto RELEASE;
    }
    // 填入dtlRawAst->relationMap的relation由service在dml操作执行完成或异常退出前后调用DbDeleteDynMemCtx统一释放
    DtlRelationT *relation = DbDynMemCtxAlloc(dtlRawAst->memCtx, sizeof(DtlRelationT));
    if (relation == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc relation (%s).", vertexLabel->metaCommon.metaName);
        ret = GMERR_OUT_OF_MEMORY;
        goto RELEASE;
    }
    (void)memset_s(relation, sizeof(DtlRelationT), 0, sizeof(DtlRelationT));
    uint32_t hash = DbStrToHash32(vertexLabel->metaCommon.metaName);
    ret = DbOamapInsert(&dtlRawAst->relationMap, hash, vertexLabel->metaCommon.metaName, relation, NULL);
    if (ret == GMERR_DUPLICATE_OBJECT) {
        // relation此处free后不再使用，不会发生UAF漏洞
        DbDynMemCtxFree(dtlRawAst->memCtx, relation);
        *outVertexLabel = vertexLabel;
        ret = GMERR_OK;
        goto RELEASE;
    }
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "insert (%s) into relationMap.", vertexLabel->metaCommon.metaName);
        goto RELEASE;
    }
    // 填充vertexLabel数据
    ret = DbAppendListItem(&dtlRawAst->vertexLabels, &vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "fill label (%s) into ast.", vertexLabel->metaCommon.metaName);
        goto RELEASE;
    }
    relation->vertexLabel = vertexLabel;
    *outVertexLabel = vertexLabel;
    return GMERR_OK;
RELEASE:
    (void)CataReleaseVertexLabel(vertexLabel);
    return ret;
}

static Status DtlFillInputTables(DbMemCtxT *memCtx, const DmVertexLabelT *vertexLabel, DmVlIndexLabelT *indexLabel,
    DmIndexKeyT *indexKey, DbListT *tables)
{
    // 根据表的元数据填充输入表,删除场景输入表只有一张，即想要操作删除数据的表
    DbCreateListWithExtendSize(tables, sizeof(DtlNamedTableT), DTL_RULE_INPUT_TABLE_NUM, memCtx);
    DtlNamedTableT table = {0};
    Status ret = DtlFillNamedTableByVertexLabel(memCtx, vertexLabel, &table);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "fill dtl rule right tbl (%s) info.", vertexLabel->metaCommon.metaName);
        return ret;
    }
    // 填充输入表的常量字段，即索引属性常量入参
    ret = DtlFillNamedTableByIndexKey(memCtx, vertexLabel, indexLabel, indexKey, &table);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "fill dtl rule right tbl (%s) consts field info.", vertexLabel->metaCommon.metaName);
        return ret;
    }
    ret = DbAppendListItem(tables, &table);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, " append table(%s) to nameTable list .", table.name);
        return ret;
    }
    return ret;
}

static Status DtlFillIndexKey2Ast(
    DmVertexLabelT *vertexLabel, DmVlIndexLabelT *indexLabel, DmIndexKeyT *indexKey, DtlRawAstT *dtlRawAst)
{
    DtlRuleT dtlRule = {0};
    dtlRule.dmlOpType = IR_DML_DELETE;

    // 根据表的元数据填充DtlRule输出表，删除场景下该输出表即输入表
    Status ret = DtlFillNamedTableByVertexLabel(dtlRawAst->memCtx, vertexLabel, &(dtlRule.left));
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "fill dtl rule left tbl (%s) info.", vertexLabel->metaCommon.metaName);
        return ret;
    }
    // 根据表的元数据和indexkey value 填充DtlRule输入表信息
    ret = DtlFillInputTables(dtlRawAst->memCtx, vertexLabel, indexLabel, indexKey, &(dtlRule.tables));
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "fill dtl rule right tbls (%s) info.", vertexLabel->metaCommon.metaName);
        return ret;
    }
    // 填充DtlRule
    ret = DbAppendListItem(&dtlRawAst->rules, &dtlRule);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "fill dtl rule into list. vertex name is %s", vertexLabel->metaCommon.metaName);
        return ret;
    }
    return ret;
}

static Status DtlParseIndexKey(
    DbMemCtxT *memCtx, FixBufferT *req, const DmVlIndexLabelT *indexLabel, DmIndexKeyT **indexKey)
{
    // 此处内存由service在dml操作执行完成或异常退出前后调用DbDeleteDynMemCtx统一释放
    size_t size = sizeof(DmIndexKeyT) + indexLabel->maxKeyLen;
    DmIndexKeyT *parseIndexKey = DbDynMemCtxAlloc(memCtx, size);
    if (parseIndexKey == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc indexKey.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(parseIndexKey, size, 0, size);
    TextT indexBuf = {};
    Status ret = FixBufGetObjectNullable(req, &indexBuf);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "get left indexBuf.");
        return ret;
    }
    if (indexBuf.len == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "get index key.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    parseIndexKey->keyBuf = (uint8_t *)parseIndexKey + sizeof(DmIndexKeyT);
    ret = DmDeSeri2ExistsIndexKey((uint8_t *)indexBuf.str, indexBuf.len, parseIndexKey);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "deserialize idx key(vertex label id:%" PRIu32 ", idx key id:%" PRIu32 ").",
            parseIndexKey->labelId, indexLabel->idxLabelBase.indexId);
        return ret;
    }
    *indexKey = parseIndexKey;
    return ret;
}

#define DTL_DELETE_STR "Delete"
#define DTL_UPDATE_STR "Update"

static Status DtlGetIndexLabelByIndexId(
    DmVertexLabelT *vertexLabel, uint32_t labelId, uint32_t indexKeyId, DmVlIndexLabelT **indexLabel, bool isDelete)
{
    DmVlIndexLabelT *tmpIndexLabel = NULL;
    Status ret = DmGetIndexLabelByIndex(vertexLabel, indexKeyId, &tmpIndexLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret,
            "get idx label by label(id:%" PRIu32 ") and idx key id(%" PRIu32 ") when dtl %s parser Blunder.", labelId,
            indexKeyId, isDelete ? DTL_DELETE_STR : DTL_UPDATE_STR);
        return ret;
    }
    if (tmpIndexLabel->idxLabelBase.indexType == LPM4_INDEX || tmpIndexLabel->idxLabelBase.indexType == LPM6_INDEX) {
        // Datalog LPM索引不支持在线更新和删除
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "%s. Label is %s.",
            isDelete ? DTL_DELETE_STR : DTL_UPDATE_STR, vertexLabel->metaCommon.metaName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    *indexLabel = tmpIndexLabel;
    return ret;
}

static Status DtlDataSyncLabelBodyParser(FixBufferT *req, uint32_t *isReplay, uint64_t *oldVersion, bool isFillVersion)
{
    Status ret = FixBufGetUint32(req, isReplay);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get isDataSyncLabel.");
        return ret;
    }

    if (*isReplay && isFillVersion) {
        ret = FixBufGetUint64(req, oldVersion);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get oldVersion.");
            return ret;
        }
    }
    return ret;
}

static Status DtlParseRightIndexKey(FixBufferT *req, TextT *indexBuf)
{
    Status ret = FixBufGetObjectNullable(req, indexBuf);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "get right indexBuf.");
        return ret;
    }
    if (indexBuf->len != 0) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "delete by range.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return ret;
}

static Status DtlDeleteBodyParser(FixBufferT *req, uint32_t labelId, uint32_t indexKeyId, DtlRawAstT *deleteAst)
{
    // dml detele body
    // rangeScanflag | leftIndexKey | rightIndexKey | struct filter(include gql feature) | operateEdgeFlag | [isReplay]
    // | [oldVersion]
    uint32_t rangeScanflag, operateEdgeFlag, isReplay;
    uint64_t oldVersion = 0;
    // 获取rangeScanflag
    Status ret = FixBufGetUint32(req, &rangeScanflag);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get rangeScanflag.");
        return ret;
    }
    DmVertexLabelT *vertexLabel = NULL;
    // 获取vertexLabel元数据,填充vertexLabel元数据到relationMap和vertexLabels
    ret = DtlFillVertexLabel2Ast(labelId, &vertexLabel, deleteAst);
    if (ret != GMERR_OK || vertexLabel == NULL) {
        return ret;
    }

    // 获取indexLable元数据
    DmVlIndexLabelT *indexLabel = NULL;
    ret = DtlGetIndexLabelByIndexId(vertexLabel, labelId, indexKeyId, &indexLabel, true);
    if (ret != GMERR_OK) {
        // log inner
        return ret;
    }
    // 解析IndexKey
    DmIndexKeyT *indexKey = NULL;
    ret = DtlParseIndexKey(deleteAst->memCtx, req, indexLabel, &indexKey);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "parse indexkey.");
        return ret;
    }
    // 解析rightindexkey，当前不支持范围删除，返回错误
    TextT indexBuf = {0};
    ret = DtlParseRightIndexKey(req, &indexBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
#ifdef FEATURE_GQL
    // 空取structFilter, datalog delete 当前未使用该字段
    uint32_t structFilterNum;
    ret = FixBufGetUint32(req, &structFilterNum);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
#endif  // FEATURE_GQL
    // 空取operateEdgeFlag, datalog delete 当前未使用该字段
    ret = FixBufGetUint32(req, &operateEdgeFlag);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get operateEdgeFlag.");
        return ret;
    }
    if (vertexLabel->commonInfo->dlrInfo.isDataSyncLabel) {
        ret = DtlDataSyncLabelBodyParser(req, &isReplay, &oldVersion, true);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "parse SyncLabelBody.");
            return ret;
        }
    }
    // 填充Ast
    return DtlFillIndexKey2Ast(vertexLabel, indexLabel, indexKey, deleteAst);
}

static Status DtldmlAstInit(DbMemCtxT *memCtx, DtlRawAstT **dmlAst)
{
    if (*dmlAst != NULL) {
        return GMERR_OK;
    }
    // 此处内存由service在dml操作执行完成或异常退出前后调用DbDeleteDynMemCtx统一释放
    *dmlAst = DbDynMemCtxAlloc(memCtx, sizeof(DtlRawAstT));
    if (*dmlAst == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc delete params.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(*dmlAst, sizeof(DtlRawAstT), 0, sizeof(DtlRawAstT));
    Status ret = DbOamapInit(&(*dmlAst)->relationMap, 1, DbOamapStringCompare, memCtx, true);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Init relationMap.");
        return ret;
    }
    DbCreateListWithExtendSize(&(*dmlAst)->vertexLabels, sizeof(DmVertexLabelT *), DTL_LIST_DEFAULT_NUM, memCtx);
    DbCreateList(&(*dmlAst)->rules, sizeof(DtlRuleT), memCtx);
    (*dmlAst)->memCtx = memCtx;
    return ret;
}

/**
 * update or delete vertex dml header
 *      vertexLabelId | schemaVersion | indexKeyId | condStr | num
 */
static Status DtlDeleteOrUpdateVertexHeaderParser(
    FixBufferT *req, uint32_t *labelId, uint32_t *indexKeyId, uint32_t *num)
{
    uint32_t schemaVersion, condStr;
    // 解析vertexLabel
    Status ret = FixBufGetUint32(req, labelId);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get labelId.");
        return ret;
    }
    // 获取schemaVersion
    ret = FixBufGetUint32(req, &schemaVersion);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get schemaVersion.");
        return ret;
    }
    uint32_t uuid;
    ret = FixBufGetUint32(req, &uuid);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get uuid.");
        return ret;
    }
    // 获取indexKeyId
    ret = FixBufGetUint32(req, indexKeyId);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get indexKeyId.");
        return ret;
    }
    // 获取condStr
    ret = FixBufGetUint32(req, &condStr);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get condStr.");
        return ret;
    }
    if (condStr != 0) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "condStr.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    // 获取num
    ret = FixBufGetUint32(req, num);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get deleteNum.");
        return ret;
    }
    uint32_t maxNum = DB_MAX_BATCH_OP_NUM_CONFIG_MAX * DB_MAX_BATCH_OP_NUM_CONFIG_UNIT;
    if (*num == 0 || *num > maxNum) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED,
            "The num of delete or update %" PRIu32 " not in 0 to %" PRIu32 "(exclude 0).", *num, maxNum);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    return ret;
}
/**
 * delete vertex msg
 *      vertexLabelId | schemaVersion | indexKeyId | condStr |
 *      deleteNum = 1 | rangeScanflag | leftIndexKey | rightIndexKey | operateEdgeFlag
 */
static Status DtlDeleteVertexParser(SessionT *session, DbMemCtxT *memCtx, DtlRawAstT **dmlAst, uint32_t *deleteNum)
{
    Status ret = DtldmlAstInit(memCtx, dmlAst);
    if (ret != GMERR_OK) {
        return ret;
    }
    session->opCode = MSG_OP_RPC_DELETE_VERTEX;
    FixBufferT *req = session->req;
    uint32_t labelId, indexKeyId;
    ret = DtlDeleteOrUpdateVertexHeaderParser(req, &labelId, &indexKeyId, deleteNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DtlSessAppendInputId(session->datalogCtx, labelId);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Append labelId labelId %" PRIu32 " to input table list", labelId);
        return ret;
    }
    for (uint32_t i = 0; i < *deleteNum; i++) {
        // 获取dml delete Body信息
        ret = DtlDeleteBodyParser(req, labelId, indexKeyId, *dmlAst);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "parser fill dml delete body.");
            return ret;
        }
    }
    return ret;
}

static Status DtlParseVertex(DbMemCtxT *memCtx, FixBufferT *req, DmVertexLabelT *vertexLabel, DmVertexT **vertex)
{
    // 此处内存由service在dml操作执行完成或异常退出前后调用DbDeleteDynMemCtx统一释放
    Status ret = DmCreateEmptyVertexWithMemCtx(memCtx, vertexLabel, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "create empty vertex when get dtlParams.");
        return ret;
    }
    TextT vertexBuf = {};
    ret = FixBufGetObjectNullable(req, &vertexBuf);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "get vertex when get dtlParams.");
        return ret;
    }
    if (vertexBuf.len == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "vertex buf len when get dtlParams.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    ret = DmDeSerialize2ExistsVertex((uint8_t *)vertexBuf.str, vertexBuf.len, *vertex, DmGetCheckMode());
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "deserialize vertex (vertex label id:%" PRIu32 ") when get dtlParams.",
            vertexLabel->metaCommon.metaId);
    }
    return ret;
}

static Status DtlUpdateTimeoutField(DmValueT propValue, DmValueT *totalValue)
{
    DB_POINTER(totalValue);
    DmValueT currentTime = {0};
    currentTime.type = DB_DATATYPE_INT64;
    currentTime.value.timeValue = (int64_t)DbGettimeMonotonicMsec();
    Status ret = DmValueAdd(&propValue, &currentTime, totalValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "add; timeoutValue=%" PRIi64 ", currentTime=%" PRIi64, propValue.value.timeValue,
            currentTime.value.timeValue);
        return ret;
    }
    return GMERR_OK;
}
static Status DtlFillNamedTableByVertexInner(
    const DmVertexLabelT *vertexLabel, const DmVertexT *vertex, DtlNamedTableT *namedTable, DmValueT *propValue)
{
    Status ret = GMERR_OK;
    DmPropertySchemaT *properties = vertexLabel->metaVertexLabel->schema->properties;
    // 填充常量字段
    for (uint32_t i = 0; i < namedTable->params.count; i++) {
        ret = DmVertexGetPropeByIdNoCopy(vertex, i + COUNT_OFFSET, propValue);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(
                ret, "get field value by id %d in tbl %s when get dtlParams.", i, vertexLabel->metaCommon.metaName);
            return ret;
        }
        if (propValue->type == DB_DATATYPE_NULL) {
            continue;
        }
        if (properties[i + COUNT_OFFSET].isInLpmKey) {
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "update Lpm Index, labelName %s, propName: %s.",
                vertexLabel->metaCommon.metaName, properties[i + COUNT_OFFSET].name);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
        // 不允许用户更新upgradeVersion字段
        if (i == UPGRADE_VERSION_PROPID) {
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Updating \"upgradeVersion\" field. labelName=%s",
                vertexLabel->metaCommon.metaName);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }

        if (vertexLabel->commonInfo->datalogLabelInfo->withTimeout &&
            vertexLabel->commonInfo->datalogLabelInfo->timeoutPropId == i + COUNT_OFFSET) {
            ret = DtlUpdateTimeoutField(*propValue, propValue);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        DtlFieldT *dtlField = DbListItem(&namedTable->params, i);
        dtlField->value = *propValue;
        dtlField->isFilterField = true;
        dtlField->isUsed = true;
    }
    return ret;
}

static Status DtlFillNamedTableByVertex(
    DbMemCtxT *memCtx, const DmVertexLabelT *vertexLabel, const DmVertexT *vertex, DtlNamedTableT *namedTable)
{
    // 解析vertexValue
    DmValueT propValue = {0};
    Status ret = DmVertexGetPropeByName(vertex, DTL_RSVD_FIELD_NAME_COUNT, &propValue);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(
            ret, "get prope by name (%s) from label %s", DTL_RSVD_FIELD_NAME_COUNT, vertexLabel->metaCommon.metaName);
        return ret;
    }
    // 只允许更新自定义字段，不允许更新count字段
    if (propValue.type != DB_DATATYPE_NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Update non-usr defined field %s in table %s.",
            DTL_RSVD_FIELD_NAME_COUNT, vertexLabel->metaCommon.metaName);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    ret = DtlFillNamedTableByVertexInner(vertexLabel, vertex, namedTable, &propValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    namedTable->isFilter = true;
    namedTable->isProject = true;
    return ret;
}

static Status DtlFillOutputTable(
    DbMemCtxT *memCtx, const DmVertexLabelT *vertexLabel, const DmVertexT *vertex, DtlNamedTableT *table)
{
    // 根据表的元数据填充输出表,更新场景跟新字段即输出表常量字段
    Status ret = DtlFillNamedTableByVertexLabel(memCtx, vertexLabel, table);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(
            ret, "fill dtl rule left tbl (%s) info when get dtlParams.", vertexLabel->metaCommon.metaName);
        return ret;
    }
    // 填充输出表的常量字段，即目标更新字段值
    ret = DtlFillNamedTableByVertex(memCtx, vertexLabel, vertex, table);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(
            ret, "fill dtl rule right tbl (%s) const field info when get dtlParams.", vertexLabel->metaCommon.metaName);
        return ret;
    }
    return ret;
}

static Status DtlFillIndexKeyAndVertex2Ast(DmVertexLabelT *vertexLabel, DmVlIndexLabelT *indexLabel,
    DmIndexKeyT *indexKey, const DmVertexT *vertex, DtlRawAstT *dtlRawAst)
{
    DB_POINTER5(vertexLabel, indexLabel, indexKey, vertex, dtlRawAst);
    DtlRuleT dtlRule = {0};
    dtlRule.dmlOpType = IR_DML_UPDATE;
    // 根据表的元数据和vertex value填充DtlRule输出表，update场景下该输出表即输入表
    Status ret = DtlFillOutputTable(dtlRawAst->memCtx, vertexLabel, vertex, &(dtlRule.left));
    if (ret != GMERR_OK) {
        return ret;
    }
    // 根据表的元数据和indexkey value 填充DtlRule输入表信息
    ret = DtlFillInputTables(dtlRawAst->memCtx, vertexLabel, indexLabel, indexKey, &(dtlRule.tables));
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(
            ret, "fill dtl rule right tbls (%s) info when get dtlParams.", vertexLabel->metaCommon.metaName);
        return ret;
    }
    // 一个dtlRule对应一个删除条件或一个更新条件，添加到dtlRawAst->rules中
    ret = DbAppendListItem(&dtlRawAst->rules, &dtlRule);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(
            ret, "fill dtl rule into list when get dtlParams, vertex: %s", vertexLabel->metaCommon.metaName);
        return ret;
    }
    return ret;
}

static Status DtlUpdateBodyParser(FixBufferT *req, uint32_t labelId, uint32_t indexKeyId, DtlRawAstT *updateAst)
{
    // dml update body
    // uniqueIndexKey | vertex | operateEdgeFlag | [isReplay] | [oldVersion]
    uint32_t isReplay;
    uint64_t oldVersion = 0;
    DmVertexLabelT *vertexLabel = NULL;
    // 获取vertexLabel元数据,填充vertexLabel元数据到relationMap和vertexLabels
    Status ret = DtlFillVertexLabel2Ast(labelId, &vertexLabel, updateAst);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 获取indexLable元数据
    DmVlIndexLabelT *indexLabel = NULL;
    ret = DtlGetIndexLabelByIndexId(vertexLabel, labelId, indexKeyId, &indexLabel, false);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 解析IndexKey
    DmIndexKeyT *indexKey = NULL;
    ret = DtlParseIndexKey(updateAst->memCtx, req, indexLabel, &indexKey);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "parse indexkey when get dtlUpdateParams.");
        return ret;
    }
    // IndexKeyValue数量需要等于index中字段数量
    if (indexLabel->propeNum != indexKey->propertyNum) {
        // prope vals num should equals index prope num in datalog update
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "prope vals num in datalog update.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    // 解析vertex
    DmVertexT *vertex = NULL;
    ret = DtlParseVertex(updateAst->memCtx, req, vertexLabel, &vertex);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "parse vertex when get dtlUpdateParams.");
        return ret;
    }
    // 空取operateEdgeFlag, datalog update 当前未使用该字段
    uint32_t operateEdgeFlag;
    ret = FixBufGetUint32(req, &operateEdgeFlag);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get operateEdgeFlag when get dtlUpdateParams.");
        return ret;
    }

    if (vertexLabel->commonInfo->dlrInfo.isDataSyncLabel) {
        ret = DtlDataSyncLabelBodyParser(req, &isReplay, &oldVersion, true);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "parse SyncLabelBody when get dtlUpdateParams.");
            return ret;
        }
    }

    // 填充Ast
    return DtlFillIndexKeyAndVertex2Ast(vertexLabel, indexLabel, indexKey, vertex, updateAst);
}

/**
 * update vertex msg
 *      RpcMsgHeader | vertexLabelId | schemaVersion | indexKeyId | condStr |
 *      updateNum = 1 |  uniqueIndexKey | vertex | operateEdgeFlag | [isReplay] | [oldVersion]
 */
static Status DtlUpdateVertexParser(SessionT *session, DbMemCtxT *memCtx, DtlRawAstT **dmlAst, uint32_t *updateNum)
{
    Status ret = DtldmlAstInit(memCtx, dmlAst);
    if (ret != GMERR_OK) {
        return ret;
    }
    session->opCode = MSG_OP_RPC_UPDATE_VERTEX;
    FixBufferT *req = session->req;
    uint32_t labelId, indexKeyId;
    ret = DtlDeleteOrUpdateVertexHeaderParser(req, &labelId, &indexKeyId, updateNum);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "datalog parse update header.");
        return ret;
    }
    ret = DtlSessAppendInputId(session->datalogCtx, labelId);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "append labelId %" PRIu32 "to input list when update vertex", labelId, labelId);
        return ret;
    }
    // 当前只支持单条的更新操作
    if (*updateNum != 1) {
        // datalog update num should be 1
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "datalog update num.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    // 获取dml update Body信息，并填充到AST中
    ret = DtlUpdateBodyParser(req, labelId, indexKeyId, *dmlAst);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "datalog parser fill update body.");
        return ret;
    }
    return ret;
}

static Status DtlUpdateTimeoutFieldInVertex(DmVertexLabelT *vertexLabel, DmVertexT *dmVertex)
{
    uint32_t timeoutPropId = vertexLabel->commonInfo->datalogLabelInfo->timeoutPropId;
    DmValueT timeoutValue = {0};
    DmValueT currentTime = {0};
    DmValueT totalTimeoutValue = {0};
    Status ret = DmVertexGetPropeByIdNoCopy(dmVertex, timeoutPropId, &timeoutValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get delta timeout field;labelName=%s", vertexLabel->metaCommon.metaName);
        return ret;
    }
    currentTime.type = DB_DATATYPE_INT64;
    currentTime.value.timeValue = (int64_t)DbGettimeMonotonicMsec();

    if ((ret = DmValueAdd(&timeoutValue, &currentTime, &totalTimeoutValue)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "add timeout field, labelName=%s; timeoutValue=%" PRIi64 ", currentTime=%" PRIi64,
            vertexLabel->metaCommon.metaName, timeoutValue.value.longValue, currentTime.value.longValue);
        return ret;
    }

    if ((ret = DmVertexSetPropeById(timeoutPropId, totalTimeoutValue, dmVertex)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "set delta timeout field;labelName=%s", vertexLabel->metaCommon.metaName);
        return ret;
    }
    return GMERR_OK;
}

Status DtlFillVersion4Insert(DtlDmlInfoT *dmlParamData)
{
    Status ret = GMERR_OK;
    DmValueT upgradeVersion = {0};
    uint32_t insertCnt = DbListGetItemCnt(&dmlParamData->insertVertexList);
    upgradeVersion.type = DB_DATATYPE_INT32;
    for (uint32_t i = 0; i < insertCnt; i++) {
        ReqDataT *req = (ReqDataT *)DbListItem(&dmlParamData->insertVertexList, i);
        uint32_t labelId = req->labelId;
        DmVertexT *dmVertex = req->vertex;
        DmVertexLabelT *vertexLabel = NULL;
        if ((ret = CataGetVertexLabelById(NULL, labelId, &vertexLabel)) != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get vertexLabel by id %" PRIu32, labelId);
            return ret;
        }

        upgradeVersion.value.intValue = vertexLabel->commonInfo->datalogLabelInfo->upgradeInfo.upgradeVersion;
        // fill upgradeVersion equal to VertexLabel
        if ((ret = DmVertexSetPropeById(UPGRADE_VERSION_INDEX, upgradeVersion, dmVertex)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "set upgradeVersion field;labelName=%s", vertexLabel->metaCommon.metaName);
            (void)CataReleaseVertexLabel(vertexLabel);
            return ret;
        }
        (void)CataReleaseVertexLabel(vertexLabel);
    }
    return ret;
}

static Status DtlFillVersion4DeleteAndUpdate(DtlRuleT *rule)
{
    Status ret = GMERR_OK;
    uint32_t rightCount = DbListGetItemCnt(&rule->tables);
    for (uint32_t i = 0; i < rightCount; i++) {
        DtlNamedTableT *table = (DtlNamedTableT *)DbListItem(&rule->tables, i);
        uint32_t labelId = table->labelId;
        DmVertexLabelT *vertexLabel = NULL;
        if ((ret = CataGetVertexLabelById(NULL, labelId, &vertexLabel)) != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get vertexLabel by id %" PRIu32, labelId);
            return ret;
        }
        uint32_t fieldCount = DbListGetItemCnt(&table->params);
        for (uint32_t j = 0; j < fieldCount; j++) {
            DtlFieldT *filed = (DtlFieldT *)DbListItem(&table->params, j);
            if (strcmp(filed->name, DTL_RSVD_FIELD_NAME_UPGRADE_VERSION) == 0) {
                filed->value.value.intValue = vertexLabel->commonInfo->datalogLabelInfo->upgradeInfo.upgradeVersion;
                filed->type = DB_DATATYPE_INT32;
                break;
            }
        }
        CataReleaseVertexLabel(vertexLabel);
    }

    return GMERR_OK;
}

Status DtlFillUpgradeVersion(SessionT *session, DtlDmlInfoT *dmlParamData)
{
    Status ret = GMERR_OK;
    DtlSessCtxT *dtlCtx = session->datalogCtx;
    uint32_t insertCnt = DbListGetItemCnt(&dmlParamData->insertVertexList);
    if (insertCnt != 0 && DtlSessGetUpgradeVersionFill(dtlCtx)) {
        ret = DtlFillVersion4Insert(dmlParamData);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "set upgradeVersion field for insert");
            return ret;
        }
    }

    if (dmlParamData->dmlAst != NULL) {
        uint32_t ruleCount = DbListGetItemCnt(&dmlParamData->dmlAst->rules);
        for (uint32_t i = 0; i < ruleCount; i++) {
            DtlRuleT *rule = (DtlRuleT *)DbListItem(&dmlParamData->dmlAst->rules, i);

            ret = DtlFillVersion4DeleteAndUpdate(rule);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "set upgradeVersion field for delete or upgrade");
                return ret;
            }
        }
    }

    return GMERR_OK;
}

static Status DtlDeSerializeInsertTuple(
    DtlSessCtxT *dtlCtx, DbListT *dtlVertexDatas, DmVertexLabelT *vertexLabel, TextT *vertexBuf)
{
    Status ret = GMERR_OK;
    DmVertexT *dmVertex = NULL;
    DbMemCtxT *memCtx = dtlVertexDatas->memCtx;
    // create empty DmVertex to hold tuple
    ret = DmCreateEmptyVertexWithMemCtx(memCtx, vertexLabel, &dmVertex);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "create empty vertex;LabelName:%s", vertexLabel->metaCommon.metaName);
        return ret;
    }

    ret = DmDeSerialize2ExistsVertex((uint8_t *)vertexBuf->str, vertexBuf->len, dmVertex, DmGetCheckMode());
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(
            ret, "deserialize vertexBuf to empty vertex;LabelName:%s", vertexLabel->metaCommon.metaName);
        return ret;
    }

    // verify if not null property is all set
    if ((ret = DmVertexNotNullPropeIsAllSet(dmVertex)) != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "non-nullable property is null;LabelName:%s", vertexLabel->metaCommon.metaName);
        return ret;
    }
    if (vertexLabel->commonInfo->datalogLabelInfo->withTimeout) {
        if ((ret = DtlUpdateTimeoutFieldInVertex(vertexLabel, dmVertex)) != GMERR_OK) {
            return ret;
        }
    }

    ReqDataT reqData = {
        .labelId = vertexLabel->metaCommon.metaId, .vertex = dmVertex, .opCode = MSG_OP_RPC_INSERT_VERTEX};
    ret = DbAppendListItem(dtlVertexDatas, &reqData);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(
            ret, "append to reqData list when deserialize insert tuple;LabelName:%s", vertexLabel->metaCommon.metaName);
        return ret;
    }
    return ret;
}

// dmlBody： vertexlen | vertex | opreateEdgeFlag | [isReplay]
static Status DtlFillTableList(
    DtlSessCtxT *dtlCtx, FixBufferT *req, DbListT *insertVertexList, DmVertexLabelT *vertexLabel)
{
    uint32_t bufLen, opreateEdgeFlag, isReplay;
    uint64_t oldVersion = 0;
    Status ret = FixBufGetUint32(req, &bufLen);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get bufLen when get tupleList, labelId.");
        return ret;
    }
    char *bufstr = FixBufGetData(req, bufLen);
    if (bufstr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "get vertex when insert body");
        return GMERR_DATA_EXCEPTION;
    }
    ret = FixBufGetUint32(req, &opreateEdgeFlag);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get opreateEdgeFlag when get tupleList.");
        return ret;
    }
    if (vertexLabel->commonInfo->dlrInfo.isDataSyncLabel) {
        ret = DtlDataSyncLabelBodyParser(req, &isReplay, &oldVersion, false);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get SyncLabelBody when get tupleList.");
            return ret;
        }
    }

    TextT tmpText = {.len = bufLen, .str = bufstr};
    return DtlDeSerializeInsertTuple(dtlCtx, insertVertexList, vertexLabel, &tmpText);
}

static Status DtlInsertGetParams(FixBufferT *req, uint32_t *labelId, uint32_t *vertexNum)
{
    // 获取lableId
    Status ret = FixBufGetUint32(req, labelId);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get labelId when insert vertex.");
        return ret;
    }

    // 为保证后续解析报文与fast path的一致性，此处需要空取一下schema version
    uint32_t schemaVersion;
    if ((ret = FixBufGetUint32(req, &schemaVersion)) != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get schemaVersion when insert vertex.");
        return ret;
    }
    uint32_t uuid;
    if ((ret = FixBufGetUint32(req, &uuid)) != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get uuid when insert vertex.");
        return ret;
    }
    // 读取vertexNum
    if ((ret = FixBufGetUint32(req, vertexNum)) != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get vertexNum when insert vertex.");
        return ret;
    }

    return ret;
}

static Status DtlInsertVerifyTableType(
    DmDatalogLabelInfoT *dtlLabelInfo, DtlSessCtxT *datalogCtx, DmVertexLabelT *vertexLabel)
{
    if (dtlLabelInfo == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "datalogLabelInfo, labelId=%" PRIu32 ", labelName=%s",
            vertexLabel->metaCommon.metaId, vertexLabel->metaCommon.metaName);
        return GMERR_DATA_EXCEPTION;
    }
    if (DtlSessGetTableTypeVerify(datalogCtx)) {
        // 中间表和输出表不能用作当前的Insert
        if (dtlLabelInfo->inoutType != DM_DTL_INPUT_LABEL) {
            DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR,
                "intermediate tables and output tables don't allow insert in dtl dml, labelId=%" PRIu32
                ", labelName=%s",
                vertexLabel->metaCommon.metaId, vertexLabel->metaCommon.metaName);
            return GMERR_SEMANTIC_ERROR;
        }
    }
    return GMERR_OK;
}

static Status DtlInsertVertexParser(
    SessionT *session, DtlDmlInfoT *dmlParamData, uint32_t *vertexNum, DtlDmlResInfoT *dmlResp)
{
    // 重写session->opCode，有删除就填删除，没删除就根据插入的opcode填Insert,ee层实现需要通过session->opCode判断事务开启情况
    if (session->opCode != MSG_OP_RPC_DELETE_VERTEX) {
        session->opCode = MSG_OP_RPC_INSERT_VERTEX;
    }
    FixBufferT *req = session->req;
    // insertBuff数据格式
    // vertexLabelId | vertexNum | schemaVersion | vertexLen | vertex | operateEdgeFlag
    // | [isReplay]
    uint32_t labelId;
    Status ret = DtlInsertGetParams(req, &labelId, vertexNum);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t maxNum = DB_MAX_BATCH_OP_NUM_CONFIG_MAX * DB_MAX_BATCH_OP_NUM_CONFIG_UNIT;
    if (*vertexNum == 0 || *vertexNum > maxNum) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED,
            "The number of vertex %" PRIu32 " not in 0 to %" PRIu32 "(exclude 0).", *vertexNum, maxNum);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }

    DtlSessCtxT *datalogCtx = session->datalogCtx;
    ret = DtlSessAppendInputId(datalogCtx, labelId);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "append labelId to input table list, labelId %" PRIu32, labelId);
        return ret;
    }

    DmVertexLabelT *vertexLabel = NULL;
    if ((ret = CataGetVertexLabelById(NULL, labelId, &vertexLabel)) != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get vertexLabel by id %" PRIu32, labelId);
        return ret;
    }

    DmDatalogLabelInfoT *dtlLabelInfo = vertexLabel->commonInfo->datalogLabelInfo;
    if ((ret = DtlInsertVerifyTableType(dtlLabelInfo, datalogCtx, vertexLabel)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "label %s doesn't pass verification", vertexLabel->metaCommon.metaName);
        goto FINISH;
    }

    if ((ret = DtlVerifyBatchSoId(dmlResp->soId, dtlLabelInfo->soId, vertexLabel)) != GMERR_OK) {
        goto FINISH;
    }
    dmlResp->soId = dtlLabelInfo->soId;

    for (uint32_t i = 0; i < *vertexNum; i++) {
        // 获取dml insert Body信息
        ret = DtlFillTableList(datalogCtx, req, &dmlParamData->insertVertexList, vertexLabel);
        if (ret != GMERR_OK) {
            // datalog parser fill dmlBody unablely
            DB_LOG_AND_SET_LASERR(
                ret, "fill dmlBody, labelId=%" PRIu32 ", labelName=%s", labelId, vertexLabel->metaCommon.metaName);
            goto FINISH;
        }
    }

FINISH:
    (void)CataReleaseVertexLabel(vertexLabel);
    return ret;
}

static Status DtlParserDmlParam(
    SessionT *session, const uint32_t opCode, DtlDmlInfoT *dmlParamData, uint32_t *finishedNum, DtlDmlResInfoT *dmlResp)
{
    switch (opCode) {
        case MSG_OP_RPC_INSERT_VERTEX:
            return DtlInsertVertexParser(session, dmlParamData, finishedNum, dmlResp);
        case MSG_OP_RPC_DELETE_VERTEX:
            return DtlDeleteVertexParser(session, dmlParamData->dataMemCtx, &dmlParamData->dmlAst, finishedNum);
        case MSG_OP_RPC_UPDATE_VERTEX:
            return DtlUpdateVertexParser(session, dmlParamData->dataMemCtx, &dmlParamData->dmlAst, finishedNum);
        default:
            // 当前不支持除MSG_OP_RPC_INSERT_VERTEX、MSG_OP_RPC_DELETE_VERTEX、MSG_OP_RPC_UPDATE_VERTEX以外的操作
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "opcode: %" PRIu32 " when parse datalog dml.", opCode);
            return GMERR_FEATURE_NOT_SUPPORTED;
    }
}

static Status DtlParserBatchDmlParam(SessionT *session, DtlDmlInfoT *dmlParamData, DtlDmlResInfoT *dmlResp)
{
    FixBufferT *req = session->req;
    uint32_t batchNum = 0, finishedNum = 0;
    Status ret = DtlBatchPrepare(req, &batchNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t opCode;
    while (batchNum > 0) {
        ret = FixBufGetUint32(req, &opCode);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get opCode while parse No.%" PRIu32 " dml op.", batchNum);
            return ret;
        }
        if (!DtlBatchDmlSupportOP(opCode)) {
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "opcode: %" PRIu32 " when parse dml.", opCode);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
        ret = DtlParserDmlParam(session, opCode, dmlParamData, &finishedNum, dmlResp);
        if (ret != GMERR_OK) {
            return ret;
        }
        batchNum -= finishedNum;
    }
    return ret;
}

Status DtlDmlParser(SessionT *session, DtlDmlInfoT *dmlParamData, DtlDmlResInfoT *dmlResp)
{
    DB_POINTER2(session, dmlParamData);
    uint8_t notDistribute = false;
    static MsgExtendT msgExtend = {.tag = MSG_EXTENTD_TAG_NOT_DISTRIBUTE, .size = sizeof(uint8_t)};
    MsgGetExtend(session->req, &msgExtend, &notDistribute);
    DtlSessSetNotDistribute(session->datalogCtx, notDistribute);
    RpcSeekContent(session->req);
    OpHeaderT *opHeader = FixBufGetData(session->req, MSG_OP_HEADER_ALIGN_SIZE);
    if (opHeader == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "get opHeader when parser dml");
        return GMERR_DATA_EXCEPTION;
    }

    // 该list占用内存由外部统一释放，不调用DbDestroyList进行销毁
    DbCreateList(&session->datalogCtx->inputTblList, sizeof(uint32_t), dmlParamData->dataMemCtx);

    // 该map占用内存由外部统一释放，不调用DbOamapDestroy进行销毁
    Status ret =
        DbOamapInit(&session->datalogCtx->inputTblMap, 1, DbOamapUint32Compare, dmlParamData->dataMemCtx, true);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "init input tbl map when parser dml");
        return ret;
    }

    // 该list占用内存由外部统一释放，不调用DbDestroyList进行销毁
    DbCreateList(&dmlParamData->insertVertexList, sizeof(ReqDataT), dmlParamData->dataMemCtx);

    // datalog sub batch rsp
    if (opHeader->opCode == MSG_OP_RPC_BATCH || opHeader->opCode == MSG_OP_RPC_DATALOG_SUB_BATCH_RESPONSE) {
        // 解析Batch dml请求
        return DtlParserBatchDmlParam(session, dmlParamData, dmlResp);
    } else {
        // 解析dml请求参数
        uint32_t dmlNum;
        return DtlParserDmlParam(session, opHeader->opCode, dmlParamData, &dmlNum, dmlResp);
    }
}

static Status GetDatalogParamFromRequest(DbMemCtxT *memCtx, FixBufferT *req, TextT *para)
{
    DB_POINTER3(memCtx, req, para);
    TextT paraTemp = {0};
    Status ret = FixBufGetTextExNullable(req, &paraTemp);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get dtl parameter.");
        return ret;
    }

    if (paraTemp.len == 0) {  // 参数为空提前返回
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_NAME, "param len in req.");
        return GMERR_INVALID_NAME;
    }

    // 内存由加载结束统一释放
    para->str = (char *)DbDynMemCtxAlloc(memCtx, paraTemp.len);
    if (para->str == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc mem to init dtl param.");
        return GMERR_OUT_OF_MEMORY;
    }

    para->len = paraTemp.len;  // 包含末尾\0

    errno_t err = memcpy_s(para->str, paraTemp.len, paraTemp.str, paraTemp.len);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "cpy dtl param %s.", paraTemp.str);
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    return GMERR_OK;
}

static Status DtlParseSoPath(DbMemCtxT *memCtx, FixBufferT *req, TextT *soPath)
{
    DB_POINTER3(memCtx, req, soPath);
    Status ret = GetDatalogParamFromRequest(memCtx, req, soPath);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (soPath->len == 0 || soPath->len > DM_MAX_DATALOG_PATH_LENGTH) {  // soPath->len包含\0
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_NAME,
            "The len of path %" PRIu32 " is illegal when import dtl,so path is %s.", soPath->len,
            soPath->str == NULL ? "null" : soPath->str);
        return GMERR_INVALID_NAME;
    }
    return ret;
}

static Status DtlParseUnimportSoName(DbMemCtxT *memCtx, FixBufferT *req, TextT *para)
{
    Status ret = GetDatalogParamFromRequest(memCtx, req, para);
    if (ret != GMERR_OK) {
        return ret;
    }

    return ret;
}

static Status DtlParseDistribute(FixBufferT *req, uint32_t *isDistribute)
{
    Status ret = FixBufGetUint32(req, isDistribute);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

void DtlReleaseVertexFromAst(DtlRawAstT *dmlAst)
{
    if (dmlAst == NULL) {
        return;
    }
    uint32_t lableCount = DbListGetItemCnt(&dmlAst->vertexLabels);
    for (uint32_t i = 0; i < lableCount; i++) {
        DmVertexLabelT *vertexLabel = *(DmVertexLabelT **)DbListItem(&dmlAst->vertexLabels, i);
        (void)CataReleaseVertexLabel(vertexLabel);
    }
}

static inline Status DtlParseIgnorePubsub(FixBufferT *req, bool *ignorePubsub)
{
    uint32_t ignore = 0;
    Status ret = FixBufGetUint32(req, &ignore);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "fix buf get ignore pubsub");
        return ret;
    }
    *ignorePubsub = ignore;
    return GMERR_OK;
}

/*
 * description: 解析datalog卸载所需参数，参数解析接口调用顺序与报文填充顺序一致，不得随意调整
 * param {DbMemCtxT} *memCtx：初始化参数内存
 * param {DtlUnImportParamT} *unloadParam：卸载参数
 * param {FixBufferT} *req：datalog卸载报文
 * return {*} 参数解析结果
 */
Status ParseDatalogUnImportParam(DbMemCtxT *memCtx, DtlUnImportParamT *unloadParam, FixBufferT *req)
{
    DB_POINTER3(unloadParam, memCtx, req);
    RpcSeekFirstOpMsg(req);

    Status ret = DtlParseUnimportSoName(memCtx, req, &unloadParam->soName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DtlParseNspName(memCtx, req, &unloadParam->namespaceName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DtlParseIgnorePubsub(req, &unloadParam->ignorePubsub);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

Status VerifyDatalogUnImportParam(SessionT *session, DtlUnImportParamT *unloadParam)
{
    uint32_t soNameLen = unloadParam->soName.len;
    uint32_t nspNameLen = unloadParam->namespaceName.len;
    if (soNameLen == 0 || nspNameLen == 0) {
        // Verify Unload datalog param, soName or namespaceName is NULL
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_NAME, "soName or namespaceName. soNameLen=%" PRIu32 ", nspNameLen=%" PRIu32,
            soNameLen, nspNameLen);
        return GMERR_INVALID_NAME;
    }

    // 校验卸载的datalog so名
    Status ret =
        QryCheckObjectNameConstraint(unloadParam->soName.str, soNameLen, DM_MAX_DATALOG_FILENAME_LENGTH, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong unload so param.");
        return ret;
    }
    // 校验卸载的datalog namespace名
    ret = QryCheckObjectNameConstraint(
        unloadParam->namespaceName.str, nspNameLen, DM_MAX_DATALOG_NAMESPACENAME_LENGTH, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong unload namespace param.");
        return ret;
    }

    // 校验 namespace
    uint32_t targetNspId = 0;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(session->memCtx);
    ret = CataGetNamespaceIdByName(dbInstance, unloadParam->namespaceName.str, &targetNspId);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get unload dtl nspId, soName=%s, nspName=%s.", unloadParam->soName.str,
            unloadParam->namespaceName.str);
        return ret;
    }
    if (targetNspId != session->namespaceId) {
        // Incorrect namespace id
        DB_LOG_AND_SET_LASERR(GMERR_RESTRICT_VIOLATION,
            "namespace:%s, namespace id:%" PRIu32 " in unload dtl param, namespace id:%" PRIu32 " in session.",
            unloadParam->namespaceName.str, targetNspId, session->namespaceId);
        return GMERR_RESTRICT_VIOLATION;
    }

    unloadParam->nspId = targetNspId;

    return GMERR_OK;
}

//  example: /root/a.so ----> a.so ----> a
Status DtlParseSoName(DbMemCtxT *memCtx, char *srcPath, TextT *dstName)
{
    if (memCtx == NULL || srcPath == NULL || dstName == NULL) {
        DB_LOG_ERROR(GMERR_NULL_VALUE_NOT_ALLOWED, "Input param.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    char *soName = strrchr(srcPath, '/');
    soName = (soName == NULL ? srcPath : (soName + 1));
    size_t soNameLen = strlen(soName);
    if (soNameLen < DATALOG_PROGRAM_NAME_LEN_MIN) {
        // too small
        DB_LOG_ERROR(GMERR_INVALID_NAME, "dtl so name len, %s.", srcPath);
        return GMERR_INVALID_NAME;
    }
    size_t namePrefix = soNameLen - (DATALOG_PROGRAM_NAME_LEN_MIN - 1);
    /* memCtx用途：存放表加载上下文相关信息
     * 生命周期：请求级别
     * 释放方式：一次表加载请求完成在LoadDatalogEntry进行释放
     * 兜底清空措施：一次表加载请求完成在LoadDatalogEntry进行释放
     * 并发：不涉及并发
     */
    dstName->str = (char *)DbDynMemCtxAlloc(memCtx, namePrefix + 1);
    if (dstName->str == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc dtl so name prefix.");
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t errNo = memcpy_s(dstName->str, (namePrefix + 1), soName, namePrefix);
    if (errNo != EOK) {
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(memCtx, dstName->str);
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy dtl so name prefix.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    dstName->str[namePrefix] = 0;
    dstName->len = (uint32_t)(namePrefix + 1);
    return GMERR_OK;
}

Status DtlParseNspName(DbMemCtxT *memCtx, FixBufferT *req, TextT *dstName)
{
    TextT paraTemp = {0};
    Status ret = FixBufGetTextExNullable(req, &paraTemp);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get dtl nsp param.");
        return ret;
    }

    if (paraTemp.len == 0) {  // 客户端请求不指定namespace的话就使用默认的namespace
        paraTemp.str = PUBLIC_NAMESPACE_NAME;
        paraTemp.len = DM_STR_LEN(paraTemp.str);
    }

    // 内存由加载结束统一释放
    dstName->str = (char *)DbDynMemCtxAlloc(memCtx, paraTemp.len);
    if (dstName->str == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc to init dtl nsp param.");
        return GMERR_OUT_OF_MEMORY;
    }

    dstName->len = paraTemp.len;  // 包含末尾\0

    errno_t err = memcpy_s(dstName->str, paraTemp.len, paraTemp.str, paraTemp.len);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "cpy dtl nsp param %s.", paraTemp.str);
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    return GMERR_OK;
}
