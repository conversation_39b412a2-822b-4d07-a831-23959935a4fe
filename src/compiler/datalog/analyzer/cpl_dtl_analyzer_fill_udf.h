/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: datalog离线编译填充UDF元数据
 * Note1：memCtx生命周期为短进程级别，由gmprecompiler工具在编译开始的时候GmPreCompilerMain中申请，编译结束后在该函数中统一释放
 * Note2: 模块不涉及并发，也不支持并发
 * Author: wangsi
 * Create: 2022-09-07
 */

#ifndef CPL_DTL_ANALYZER_FILL_UDF_H
#define CPL_DTL_ANALYZER_FILL_UDF_H

#include "cpl_dtl_offline_compiler.h"
#include "cpl_dtl_analyzer_fill_vertex_label.h"

#ifdef __cplusplus
extern "C" {
#endif

Status DtlAnalyzerFillUdf(DbMemCtxT *memCtx, DtlRawAstT *program);

#ifdef __cplusplus
}
#endif

#endif
