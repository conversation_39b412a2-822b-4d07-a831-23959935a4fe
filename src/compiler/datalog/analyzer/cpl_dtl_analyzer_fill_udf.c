/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: fill in datalog udf meta data
 * Author: wangsi
 * Create: 2022-09-07
 */

#include "cpl_dtl_analyzer_fill_udf.h"
#include "cpl_dtl_verifier_basic.h"
#include "cpl_log.h"

#ifdef __cplusplus
extern "C" {
#endif

#define DTL_UDF_MAX_NUM 1024
#define DTL_UDF_MAX_NUM_IN_ONE_RELATION 2
#define COUNT_PROP_ID 0

typedef struct {
    DmUdfBaseT *baseUdf;
    DmNormalUdfT *normalUdf;
    DmAggUdfT *aggUdf;
    DbListT *fields;
    uint32_t inputNum;
    DmUdfTypeE udfType[DTL_UDF_MAX_NUM_IN_ONE_RELATION];
    DtlAccessTablesT *accessTable[DTL_UDF_MAX_NUM_IN_ONE_RELATION];
} DtlFillUdfCtxT;

static Status DtlFillUdf(DbMemCtxT *memCtx, DtlRawAstT *program, const char *relationName);
static Status DtlFillAndAppendAggCmpUdf(DbMemCtxT *memCtx, DtlRawAstT *rawAst, DtlRelationT *relation);
static Status DtlCreateUdfBase(DbMemCtxT *memCtx, DtlRelationT *relation, DtlFillUdfCtxT *ctx);
static Status DtlFillNormalAndAggUdf(DbMemCtxT *memCtx, DtlFillUdfCtxT *ctx, DtlRelationT *relation);
static Status DtlFillProps(DbMemCtxT *memCtx, DtlFillUdfCtxT *ctx);
static Status DtlFillProps4Agg(DbMemCtxT *memCtx, DtlFillUdfCtxT *ctx);
static Status DtlFillUdfBase(
    DbMemCtxT *memCtx, const char *relationName, DtlRelationT *relation, uint32_t idx, DtlFillUdfCtxT *udfCtx);
static Status DtlFillUdfAccessTables(DbMemCtxT *memCtx, DmUdfBaseT *baseUdf, DtlAccessTablesT *accessTable);
static inline bool DtlHasTimeoutUdf(const DtlTableT *table);
static inline bool DtlHasCmpUdf(const DtlTableT *table);
static inline bool DtlHasTbmUdf(const DtlTableT *table);
static inline bool DtlHasMsgNotifyUdf(const DtlTableT *table);
static inline bool DtlHasMsgNotifyCmpUdf(const DtlTableT *table);

Status DtlAnalyzerFillUdf(DbMemCtxT *memCtx, DtlRawAstT *program)
{
    DB_POINTER2(memCtx, program);
    Status ret = GMERR_OK;
    DtlTableT *table;
    DtlFunctionT *function;
    DtlAggregateT *aggregate;

    // UDF元数据以DbList的形式传递给后续模块，list元素为DmUdfBaseT *
    DbCreateList(&program->udfs, sizeof(DmUdfBaseT *), memCtx);

    for (uint32_t i = 0; i < program->tables.count; i++) {
        table = *(DtlTableT **)DbListItem(&program->tables, i);
        if (table->isOldDefined) {
            continue;
        }
        ret = DtlFillUdf(memCtx, program, table->name);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    for (uint32_t i = 0; i < program->functions.count; i++) {
        function = *(DtlFunctionT **)DbListItem(&program->functions, i);
        if (function->isOldDefined) {
            continue;
        }
        ret = DtlFillUdf(memCtx, program, function->name);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    for (uint32_t i = 0; i < program->aggregates.count; i++) {
        aggregate = *(DtlAggregateT **)DbListItem(&program->aggregates, i);
        if (aggregate->isOldDefined) {
            continue;
        }
        ret = DtlFillUdf(memCtx, program, aggregate->name);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // UDF的总数不能超限
    if (program->udfs.count > DTL_UDF_MAX_NUM) {
        DTL_LAST_ERROR_CONSOLE_PRINT(GMERR_SEMANTIC_ERROR,
            "Error: there are %" PRIu32 " UDFs which exceeds the maximum value %" PRIu32 ".", program->udfs.count,
            DTL_UDF_MAX_NUM);
        return GMERR_SEMANTIC_ERROR;
    }
    return ret;
}

static Status DtlFillUdf(DbMemCtxT *memCtx, DtlRawAstT *program, const char *relationName)
{
    Status ret = GMERR_OK;

    uint32_t hash = DbStrToHash32(relationName);
    DtlRelationT *relation = (DtlRelationT *)DbOamapLookup(&program->relationMap, hash, relationName, NULL);
    if (relation == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Not find \"%s\".", relationName);
        return GMERR_INTERNAL_ERROR;
    }

    DtlFillUdfCtxT ctx = {0};
    if (DtlIsTable(relation->relationType)) {
        ret = DtlCreateUdfBase(memCtx, relation, &ctx);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else if (relation->relationType == DTL_FUNCTION || relation->relationType == DTL_AGGREGATE) {
        ret = DtlFillNormalAndAggUdf(memCtx, &ctx, relation);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        return GMERR_OK;  // 不属于以上类型的不用填充UDF元数据
    }

    // 1个relation关联的udf不超过2个，绝大多数场景只关联1个udf。关联2个的场景包括：
    // 1、table中同时带比较udf和超时udf；2、aggregate带ordered选项
    DB_ASSERT(relation->udfNum <= DTL_UDF_MAX_NUM_IN_ONE_RELATION);

    for (uint32_t i = 0; i < relation->udfNum; i++) {
        DmUdfBaseT *pUdf = &relation->udf[i];
        ret = DbAppendListItem(&program->udfs, &pUdf);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "Udfs append list.");
            return ret;
        }
    }

    if (ctx.normalUdf != NULL && ctx.fields != NULL) {  // 普通UDF/聚合UDF等
        ret = relation->relationType == DTL_AGGREGATE ? DtlFillProps4Agg(memCtx, &ctx) : DtlFillProps(memCtx, &ctx);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    for (uint32_t i = 0; i < relation->udfNum; i++) {
        DB_ASSERT(ctx.baseUdf != NULL);
        ret = DtlFillUdfBase(memCtx, relationName, relation, i, &ctx);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return DtlFillAndAppendAggCmpUdf(memCtx, program, relation);
}

// 聚合函数带ordered选项的场景单独处理，需要填充agg比较函数并挂到list上
static Status DtlFillAndAppendAggCmpUdf(DbMemCtxT *memCtx, DtlRawAstT *rawAst, DtlRelationT *relation)
{
    Status ret = GMERR_OK;
    if (relation->relationType == DTL_AGGREGATE && relation->aggregate->aggregateOption.withOrdered) {
        // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
        DmUdfBaseT *pUdf = DbDynMemCtxAlloc(memCtx, sizeof(DmUdfBaseT));
        if (pUdf == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc aggCmpUdf.");
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s(pUdf, sizeof(DmUdfBaseT), 0, sizeof(DmUdfBaseT));
        ret = DbAppendListItem(&rawAst->udfs, &pUdf);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "append agg cmp udf to list.");
            return ret;
        }
        relation->udfNum++;

        DtlFillUdfCtxT udfCtx = (DtlFillUdfCtxT){0};
        udfCtx.baseUdf = pUdf;
        udfCtx.udfType[0] = DM_UDF_AGG_CMP;
        udfCtx.accessTable[0] = NULL;

        ret = DtlFillUdfBase(memCtx, relation->aggregate->name, relation, 0, &udfCtx);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

static Status DtlCreateUdfBase(DbMemCtxT *memCtx, DtlRelationT *relation, DtlFillUdfCtxT *ctx)
{
    if (!DtlHasTimeoutUdf(relation->table) && !DtlHasCmpUdf(relation->table) && !DtlHasTbmUdf(relation->table) &&
        !DtlHasMsgNotifyUdf(relation->table)) {
        return GMERR_OK;
    }
    if (DtlHasTimeoutUdf(relation->table)) {
        relation->udfNum++;
        ctx->udfType[relation->udfNum - 1] = DM_UDF_TIMEOUT;
        ctx->accessTable[relation->udfNum - 1] = &relation->table->parsedOption.timeout->accessTable;
    }
    if (DtlHasCmpUdf(relation->table)) {
        relation->udfNum++;
        ctx->udfType[relation->udfNum - 1] = DM_UDF_CMP;
        ctx->accessTable[relation->udfNum - 1] = NULL;  // 不支持比较UDF操作其它表
    }
    if (DtlHasTbmUdf(relation->table)) {
        relation->udfNum = 1;
        ctx->udfType[relation->udfNum - 1] = DM_UDF_TBM;
        ctx->accessTable[relation->udfNum - 1] = NULL;  // TBM表的UDF不支持操作其他表
    }

    // msgNotify
    if (DtlHasMsgNotifyUdf(relation->table)) {
        relation->udfNum = 1;
        ctx->udfType[relation->udfNum - 1] = DM_UDF_MSG_NOTIFY;
        ctx->accessTable[relation->udfNum - 1] = NULL;  // 消息通知表的UDF不支持操作其他表
        if (DtlHasMsgNotifyCmpUdf(relation->table)) {
            relation->udfNum++;
            ctx->udfType[relation->udfNum - 1] = DM_UDF_MSG_NOTIFY_CMP;
            ctx->accessTable[relation->udfNum - 1] = NULL;  // 消息通知表的排序UDF不支持操作其他表
        }
    }

    // 为baseUdf数组分配内存，并初始化为全零
    // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
    size_t udfArraySize = sizeof(DmUdfBaseT) * relation->udfNum;
    ctx->baseUdf = DbDynMemCtxAlloc(memCtx, udfArraySize);
    if (ctx->baseUdf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc baseUdf.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(ctx->baseUdf, udfArraySize, 0, udfArraySize);
    relation->udf = ctx->baseUdf;
    return GMERR_OK;
}

// table是否带timeout回调函数
static inline bool DtlHasTimeoutUdf(const DtlTableT *table)
{
    return table->parsedOption.withTimeout && table->parsedOption.timeout->withStateFunction;
}

// 可更新表是否带比较函数
static inline bool DtlHasCmpUdf(const DtlTableT *table)
{
    return table->parsedOption.withUpdateByRank;
}

// table 是否带TBM的udf（每个TBM表都要有1个对应的udf与之关联）
static inline bool DtlHasTbmUdf(const DtlTableT *table)
{
    return table->parsedOption.withTbmTable;
}

static inline bool DtlHasMsgNotifyUdf(const DtlTableT *table)
{
    return table->parsedOption.withMsgNotify;
}

static inline bool DtlHasMsgNotifyCmpUdf(const DtlTableT *table)
{
    return table->parsedOption.withMsgNotifyCmp;
}

static Status DtlFillNormalAndAggUdf(DbMemCtxT *memCtx, DtlFillUdfCtxT *ctx, DtlRelationT *relation)
{
    Status ret = GMERR_OK;
    if (relation->relationType == DTL_FUNCTION) {
        // 为normalUdf分配内存，并初始化为全零
        // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
        ctx->normalUdf = DbDynMemCtxAlloc(memCtx, sizeof(DmNormalUdfT));
        if (ctx->normalUdf == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc normalUdf.");
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s(ctx->normalUdf, sizeof(DmNormalUdfT), 0, sizeof(DmNormalUdfT));
        ctx->baseUdf = &ctx->normalUdf->base;
        relation->udf = ctx->baseUdf;
        relation->udfNum = 1;

        ctx->fields = &relation->function->fields;
        ctx->inputNum = relation->function->inputNum;
        ctx->udfType[0] = relation->function->isState ? DM_UDF_STATE_TRANSFER : DM_UDF_NORMAL;
        ctx->accessTable[0] = &relation->function->accessTable;
    } else if (relation->relationType == DTL_AGGREGATE) {
        // 为aggUdf分配内存，并初始化为全零
        // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
        ctx->aggUdf = DbDynMemCtxAlloc(memCtx, sizeof(DmAggUdfT));
        if (ctx->aggUdf == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc aggUdf.");
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s(ctx->aggUdf, sizeof(DmAggUdfT), 0, sizeof(DmAggUdfT));
        ctx->normalUdf = &ctx->aggUdf->normal;
        ctx->baseUdf = &ctx->normalUdf->base;  // 基础聚合udf
        relation->udf = ctx->baseUdf;
        relation->udfNum = 1;

        ctx->fields = &relation->aggregate->fields;
        ctx->inputNum = relation->aggregate->inputNum;
        ctx->udfType[0] = DM_UDF_AGGREGATION;
        ctx->accessTable[0] = &relation->aggregate->aggregateOption.accessTable;

        // 填充DmAggUdfT
        ctx->aggUdf->isOrdered = relation->aggregate->aggregateOption.withOrdered;
        if (ctx->aggUdf->isOrdered) {
            // 带ordered选项的聚合函数，需要有对应的比较UDF，这里填充比较函数的名字
            ret =
                DtlGenerateUdfName(&ctx->aggUdf->cmpUdfName, DTL_UDF_AGG_CMP_PREFIX, relation->aggregate->name, memCtx);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        ctx->aggUdf->isManyToMany = relation->aggregate->aggregateOption.withManyToMany;
    } else {
        // bad Relation type when filling normal|agg udf
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "Relation type.");
        return GMERR_DATATYPE_MISMATCH;
    }
    return GMERR_OK;
}

// 填充函数字段
static Status DtlFillProps(DbMemCtxT *memCtx, DtlFillUdfCtxT *ctx)
{
    DB_POINTER2(memCtx, ctx);
    Status ret = GMERR_OK;
    DmNormalUdfT *normalUdf = ctx->normalUdf;
    DbListT *fields = ctx->fields;

    normalUdf->inputPropNum = ctx->inputNum;
    // 为函数输入字段数组分配内存，并初始化为全零
    ret = DtlInitPropSchemaArray(memCtx, normalUdf->inputPropNum, &normalUdf->inputProps);
    if (ret != GMERR_OK) {
        return ret;
    }

    normalUdf->outputPropNum = ctx->fields->count - ctx->inputNum;
    // 为函数输出字段数组分配内存，并初始化为全零
    ret = DtlInitPropSchemaArray(memCtx, normalUdf->outputPropNum, &normalUdf->outputProps);
    if (ret != GMERR_OK) {
        return ret;
    }

    FillPropOffsetT offset = {0};
    ret = DtlFillPropSchema(memCtx, fields, offset, normalUdf->inputPropNum, normalUdf->inputProps);
    if (ret != GMERR_OK) {
        return ret;
    }

    offset.fieldOffset = normalUdf->inputPropNum;
    ret = DtlFillPropSchema(memCtx, fields, offset, normalUdf->outputPropNum, normalUdf->outputProps);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

inline static void DtlFillPatchType(DtlRelationT *relation, DmUdfBaseT *baseUdf)
{
    // fill patchOp for udf
    if (DtlIsTable(relation->relationType)) {
        baseUdf->patchOp = (uint8_t)relation->table->patchOp;
        baseUdf->isNotUpgrade = relation->table->isOldDefined;
    } else if (relation->relationType == DTL_FUNCTION) {
        baseUdf->patchOp = (uint8_t)relation->function->patchOp;
        baseUdf->isNotUpgrade = relation->function->isOldDefined;
    } else if (relation->relationType == DTL_AGGREGATE) {
        // 不支持AGG升级，所以AGG patchOp只能是DM_PATCH_CREATE
        baseUdf->patchOp = DM_PATCH_CREATE;
        baseUdf->isNotUpgrade = relation->aggregate->isOldDefined;
    }
}

inline static void DtlFillReservedCount4Agg(DmPropertySchemaT *inputProps)
{
    inputProps[COUNT_PROP_ID].name = (char *)DTL_RESERVED_FIELD_NAME;
    inputProps[COUNT_PROP_ID].nameLen = (uint16_t)(strlen(DTL_RESERVED_FIELD_NAME) + 1);
    inputProps[COUNT_PROP_ID].propeId = COUNT_PROP_ID;
    inputProps[COUNT_PROP_ID].dataType = DB_DATATYPE_INT32;
    // datalog只有byte和str是变长类型，int1/2/4/8和byte1~byte512都是定长的
    inputProps[COUNT_PROP_ID].isFixed = DmIsFixedType(DB_DATATYPE_INT32);
    inputProps[COUNT_PROP_ID].size = (uint32_t)sizeof(int32_t);
    inputProps[COUNT_PROP_ID].isValid = true;
}

static Status DtlFillProps4Agg(DbMemCtxT *memCtx, DtlFillUdfCtxT *ctx)
{
    DB_POINTER2(memCtx, ctx);
    Status ret = GMERR_OK;
    DmNormalUdfT *normalUdf = ctx->normalUdf;
    DbListT *fields = ctx->fields;

    uint32_t aggInputNum = ctx->inputNum;
    uint32_t aggOutputNum = ctx->fields->count - ctx->inputNum;
    normalUdf->inputPropNum = aggInputNum + 1;

    // 为函数输入字段数组分配内存，并初始化为全零
    ret = DtlInitPropSchemaArray(memCtx, normalUdf->inputPropNum, &normalUdf->inputProps);
    if (ret != GMERR_OK) {
        return ret;
    }

    normalUdf->outputPropNum = aggOutputNum;
    // 为函数输出字段数组分配内存，并初始化为全零
    ret = DtlInitPropSchemaArray(memCtx, normalUdf->outputPropNum, &normalUdf->outputProps);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 将DtlReservedCount填充到Agg参数的第一个
    DtlFillReservedCount4Agg(normalUdf->inputProps);

    FillPropOffsetT offset = {.fieldOffset = 0, .indexOffset = 0, .propOffset = 1};
    ret = DtlFillPropSchema(memCtx, fields, offset, aggInputNum, normalUdf->inputProps);
    if (ret != GMERR_OK) {
        return ret;
    }

    offset.fieldOffset = ctx->inputNum;
    offset.propOffset = 0;
    ret = DtlFillPropSchema(memCtx, fields, offset, aggOutputNum, normalUdf->outputProps);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status DtlFillUdfBase(
    DbMemCtxT *memCtx, const char *relationName, DtlRelationT *relation, uint32_t idx, DtlFillUdfCtxT *udfCtx)
{
    DB_POINTER3(memCtx, relationName, udfCtx);

    DmUdfTypeE udfType = udfCtx->udfType[idx];
    DmUdfBaseT *baseUdf = &udfCtx->baseUdf[idx];

    Status ret = GMERR_OK;
    char *prefixedUdfName;  // 添加前缀之后的UDF名（function/aggregate/超时UDF/比较UDF等 ，名字要加前缀）
    char *prefixArray[] = {
        [DM_UDF_NORMAL] = DTL_UDF_FUNC_PREFIX,
        [DM_UDF_CMP] = DTL_UDF_CMP_PREFIX,
        [DM_UDF_TIMEOUT] = DTL_UDF_TIMEOUT_PREFIX,
        [DM_UDF_AGGREGATION] = DTL_UDF_AGG_FUNC_PREFIX,
        [DM_UDF_AGG_CMP] = DTL_UDF_AGG_CMP_PREFIX,
        [DM_UDF_TBM] = DTL_UDF_TBM_PREFIX,
        [DM_UDF_MSG_NOTIFY] = DTL_UDF_MSG_NOTIFY_PREFIX,
        [DM_UDF_MSG_NOTIFY_CMP] = DTL_UDF_MSG_NOTIFY_CMP_PREFIX,
        [DM_UDF_STATE_TRANSFER] = DTL_UDF_FUNC_PREFIX,
    };
    DB_ASSERT(udfType < DM_UDF_BUTT);
    ret = DtlGenerateUdfName(&prefixedUdfName, prefixArray[udfType], relationName, memCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    DtlFillMetaCommon(prefixedUdfName, &baseUdf->metaCommon);
    baseUdf->udfType = (uint8_t)udfType;
    baseUdf->multiVersionShare = DbDynMemCtxAlloc(memCtx, sizeof(UdfMultiVersionShareT));
    if (baseUdf->multiVersionShare == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc udf commonInfo.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(baseUdf->multiVersionShare, sizeof(UdfMultiVersionShareT), 0x00, sizeof(UdfMultiVersionShareT));

    DtlFillPatchType(relation, baseUdf);
    if (udfCtx->accessTable[idx] != NULL) {
        return DtlFillUdfAccessTables(memCtx, baseUdf, udfCtx->accessTable[idx]);
    }
    return ret;
}

static Status DtlFillUdfAccessTables(DbMemCtxT *memCtx, DmUdfBaseT *baseUdf, DtlAccessTablesT *accessTable)
{
    Status ret = GMERR_OK;
    // 填充access_delta列出的表名
    baseUdf->accessDeltaNum = (uint16_t)accessTable->deltaTables.count;
    if (baseUdf->accessDeltaNum > 0) {  // 有access_delta选项
        ret = DtlCreateEmptyArray(memCtx, baseUdf->accessDeltaNum, sizeof(char *), (void **)&baseUdf->accessDeltaNames);
        if (ret != GMERR_OK) {
            return ret;
        }
        for (uint32_t i = 0; i < baseUdf->accessDeltaNum; i++) {
            baseUdf->accessDeltaNames[i] = *(char **)DbListItem(&accessTable->deltaTables, i);
        }
    }

    // 填充access_current列出的表名
    baseUdf->accessOrgNum = (uint16_t)accessTable->originTables.count;
    if (baseUdf->accessOrgNum > 0) {  // 有access_current选项
        ret = DtlCreateEmptyArray(memCtx, baseUdf->accessOrgNum, sizeof(char *), (void **)&baseUdf->accessOrgNames);
        if (ret != GMERR_OK) {
            return ret;
        }
        for (uint32_t i = 0; i < baseUdf->accessOrgNum; i++) {
            baseUdf->accessOrgNames[i] = *(char **)DbListItem(&accessTable->originTables, i);
        }
    }
    // 填充access_kv列出的表名
    baseUdf->accessKvName = accessTable->kvTable;
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
