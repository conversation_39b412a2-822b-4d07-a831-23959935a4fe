%{
#include "cpl_public_sql_parser_common.h"
#include "cpl_stream_parser.h"

// function defined in lexer file
extern int stream_lex(void);
extern int core_yylex(void);
extern void stream_error(const char *msg);

%}

%union {
    int64_t ival64;
    double fval;
    uint32_t uival32;
    uint8_t uival8;
    char *str;
    bool boolean;
    struct {
        uint32_t index;
        char *name;
        bool isIndexed;
    } para;
    DbListT list;
    DbListT *plist;
    NodeT *node;
    SqlColumnDefT *column;
    SqlTableRefT *qname;
    SqlExprT *expr;
    ColumnCompressModeE columnCompressMode;
    SqlTargetT *target;
    SqlGroupByT *groupby;
    SqlOrderByT *orderby;
    SqlLimitT *limit;
    SqlNullsPosTypeE nullspos;
    SqlFullNameT  *fname;
    SqlCreateWithT *cwith;
    SqlCopyToStmtT *copyto;
    SqlSetItemT *setItem;
    DmPrivilegeE privilegeVal;
    StreamSinkTypeStmtT sinkType;
    SqlInsertValueT *insertValues;
    StreamWatermarkT *watermark;
    StreamWindowExprT *window;
    StreamDispatchExprT *dispatch;
    SqlPartitionByT *partitionby;
    SqlDispatchByT *dispatchby;
    FromGroupT *fromGroupList;
}

%define api.prefix {stream_}
%file-prefix "stream"

%left       OR
%left       AND
%right      NOT
%left       IS IS_NOT ISNULL NOTNULL BETWEEN S_IN LIKE NOT_EQUALS EQUALS
%right      ESCAPE
%left       '<' '>' LESS_EQUALS GREATER_EQUALS
%left       '&' '|' LSHIFT RSHIFT
%left       '+' '-'
%left       '*' '/' '%'
%left       CONCAT
%right      UMINUS   // operation priority for unary operator '+' '-'
%right      '~'
%right      OVER
%left       '(' ')'

/* stream unused token */
%token S_CHECK ESCAPE GLOB
%token <para> PARAMETER
%token L2_DISTANCE COSINE_DISTANCE SAVEPOINT RELEASE CURRENT_DATE CURRENT_TIME CURRENT_TIMESTAMP DO NOTHING HAVING
%token EXPLAIN QUERY PLAN LOGICAL PHYSICAL
%token UNION EXCEPT INTERSECT
%token TRIGGER BEFORE AFTER INSTEAD OF FOR EACH ROW WHEN RETURNING
%token S_BEGIN COMMIT END TRANSACTION VIEW
%token FOREIGN RESTRICT CASCADE NOACTION DEFERRABLE INITIALLY REFERENCES DEFERRED IMMEDIATE COLLATE NOCASE BINARY

/* token for common */
%token <ival64> SEMICOLON
%token <boolean> S_TRUE S_FALSE
%token <str> SQL_ID SQL_STRING INTEGER_STRING DOUBLE_STRING
%token FAIL IGNORE REPLACE ROLLBACK ABORT ON ON_CONFLICT ASC DESC OR INDEX INDEXED BY DEFAULT USING
%token JOIN INNER OUTER CROSS NATURAL FULL LEFT RIGHT

/* token for alter table */
%token COLUMN ADD RENAME ALTER

/* token for create table */
%token CREATE TABLE S_PRIMARY S_UNIQUE TEMPORARY S_IF EXISTS NOT S_NULL WITH AUTOINCREMENT CONSTRAINT KEY
%token CODEC NO

/* token for copy to statement */
%token COPY TO

/* token for insert statement */
%token INSERT INTO VALUES UPSERT

/* token for delete statement */
%token DELETE

/* token for update statement */
%token UPDATE SET

/* token for select statement */
%token SELECT FROM WHERE AS
%token GROUP ORDER NULLS LIMIT OFFSET PARTITION DISPATCH
%token HOP TUMBLE INTERVAL
%type <uival32> window_slide window_size window_offset window_unit

/* token for drop statement */
%token DROP

/* token for truncate statement */
%token TRUNCATE

/* token for alter union table */
%token FROM_UNION

/* token for aggregate function */
%token SUM MAX MIN AVG COUNT

/* token for distinct (only support aggregate function now) */
%token ALL DISTINCT

/* token for grant revoke */
%token GRANT REVOKE PUBLIC PRIVILEGES

/* token for stream */
%token STREAM SINK REFERENCE REF SEQ_DISTINCT_COUNT STREAMREF FORMAT
%token WATERMARK
%token current_time_second get_hostname

/* common non-terminals */
%type <list> cmdlist
%type <node> cmd

%type <node> create_stream_sink create_stream_table create_stream_view create_stream_ref
%type <sinkType> outputType
%type <str> name typeName type_name opt_dot_name opt_as_name output_type_params group_name sinkDestinationName
%type <plist> create_with_clause create_with_list from_clause from_list groupby_list table_list from_union_list alter_table_name
%type <cwith> create_with_single

/* type for create stream table */
%type <plist> column_list from_group_list
%type <column> column_def file_path
%type <uival32> opt_type_char_len
%type <expr> expr expr_base literal_value column_ref char_len_value ref_arg ref_fun distinct_count_func time_expr format_func current_time_second_func get_hostname_func agg_func default_opt expr_in
%type <qname> stream_table_name qualified_name table_name string_name
%type <watermark> stream_table_watermark_def
%type <boolean> incremental_policy opt_not_exists in_or_not
%type <window> window_clause
%type <dispatch> dispatch_clause dispatch_in_tvf
%type <fromGroupList> from_group_clause

/* non-terminals for drop stream table */
%type <node> drop_stream_table drop_stream_ref
%type <fname> drop_name 
%type <ival64> drop_type

/* non-terminals for alter stream sink or view */
%type <node> alter_stream
%type <ival64> node_type alter_type

/* non-terminals for alter union type of view/sink */
%type <node> alter_union_stream

/* non-terminals for select statement */
%type <node> select_stmt
%type <plist> target_list opt_groupby opt_partitionby partitionby_list opt_dispatchby dispatchby_list dispatchfrom_list
%type <groupby> groupby_single
%type <target> target_single
%type <plist> expr_list
%type <partitionby> partitionby_single
%type <dispatchby> dispatchby_single  dispatchfrom_single

%type <expr> opt_where

/* non-terminals for insert into ref statement */
%type <node> upsert_into_ref
%type <insertValues> insert_values_clause
%type <plist> values_list func_arg_list
%type <expr> values

%start cmdlist

%%
cmdlist: cmdlist SEMICOLON cmd
    {
        //append to SqlPList
        SqlParsedListT *sqlPList = SqlGetParseList();
        SqlListAppend(&sqlPList->cmdList, &($3));
    }
    | cmd
    {
        //alloc SqlPList and append
        SqlParsedListT *sqlPList = SqlGetParseList();
        DbCreateListWithExtendSize(&sqlPList->cmdList, sizeof(NodeT *), SQL_LIST_EXTEND_SIZE, sqlPList->memCtx);
        SqlListAppend(&sqlPList->cmdList, &($1));
    }
    ;

cmd:
    create_stream_sink
    | create_stream_table
    | drop_stream_table
    | create_stream_view
    | create_stream_ref
    | drop_stream_ref
    | upsert_into_ref
    | alter_stream
    | alter_union_stream
;

in_or_not:
    S_IN
        {
            $$ = true;
        }
    | NOT S_IN %prec S_IN
        {
            $$ = false;
        }
    ;

expr_in:
    expr in_or_not '(' expr_list ')'
        {
            SqlExprT *right = SqlParserExprMakeList($4);
            if ($2) {
                $$ = SqlParserExprMakeBinary($1, right, SQL_EXPR_OP_IN);
            } else {
                $$ = SqlParserExprMakeBinary($1, right, SQL_EXPR_OP_NOT_IN);
            }
        }
    ;

opt_not_exists:
    S_IF NOT EXISTS { $$ = true; }
    | /*EMPTY*/     { $$ = false; }
    ;

create_stream_table :
    CREATE STREAM TABLE opt_not_exists stream_table_name '(' column_list stream_table_watermark_def')' from_group_clause opt_dispatchby
        {
            SqlCreateStreamStmtT *createStreamStmt = (SqlCreateStreamStmtT *)SqlCreateStruct(sizeof(SqlCreateStreamStmtT));
            createStreamStmt->tag = T_STREAM_CREATE_TABLE_STMT;
            createStreamStmt->ifNotExists = $4;
            createStreamStmt->name = $5;
            createStreamStmt->columns = $7;
            createStreamStmt->watermark = $8;
            createStreamStmt->fromFileList = $10;
            createStreamStmt->dispatchbyList = $11;
            $$ = (NodeT *)createStreamStmt;
        }
    ;

create_stream_sink:
    CREATE STREAM SINK opt_not_exists stream_table_name AS select_stmt INTO outputType create_with_clause
        {
            SqlCreateSinkStmtT *create_streamSink = (SqlCreateSinkStmtT *)SqlCreateStruct(sizeof(SqlCreateSinkStmtT));
            create_streamSink->tag = T_STREAM_CREATE_SINK_STMT;
            create_streamSink->ifNotExists = $4;
            create_streamSink->sinkName = $5;
            create_streamSink->value = (SqlInsertValueT *)SqlCreateStruct(sizeof(SqlInsertValueT));
	        create_streamSink->value->type = SELECT_SUBQUERY;
	        create_streamSink->value->selectSubquery = (NodeT *)$7;
            create_streamSink->streamSinkTypeStmt = $9;
            create_streamSink->createSinkWith = $10;
            $$ = (NodeT *)create_streamSink;
        }
    ;

create_stream_view:
    CREATE STREAM VIEW opt_not_exists stream_table_name AS select_stmt opt_dispatchby create_with_clause
        {
            SqlCreateViewStmtT *create_streamView = (SqlCreateViewStmtT *)SqlCreateStruct(sizeof(SqlCreateViewStmtT));
            create_streamView->tag = T_STREAM_CREATE_VIEW_STMT;
            create_streamView->ifNotExists = $4;
            create_streamView->viewName = $5;
            create_streamView->value = (SqlInsertValueT *)SqlCreateStruct(sizeof(SqlInsertValueT));
	        create_streamView->value->type = SELECT_SUBQUERY;
	        create_streamView->value->selectSubquery = (NodeT *)$7;
            create_streamView->dispatchbyList = $8;
            create_streamView->createViewWith = $9;
            $$ = (NodeT *)create_streamView;
        }
    ;

create_stream_ref:
    CREATE STREAM REFERENCE opt_not_exists stream_table_name '(' type_name',' type_name ')' create_with_clause
        {
            SqlCreateStreamRefStmtT *create_streamRef = (SqlCreateStreamRefStmtT *)SqlCreateStruct(sizeof(SqlCreateStreamRefStmtT));
            create_streamRef->tag = T_STREAM_CREATE_REF_STMT;
            create_streamRef->ifNotExists = $4;
            create_streamRef->streamRefName = $5;
            create_streamRef->keyTypeName = $7;
	        create_streamRef->valueTypeName = $9;
            create_streamRef->createRefWith = $11;
            $$ = (NodeT *)create_streamRef;
        }
    ;

alter_stream:
    ALTER STREAM node_type stream_table_name ALTER WHERE AS expr
        {
            SqlAlterStreamStmtT *alterStream = (SqlAlterStreamStmtT *)SqlCreateStruct(sizeof(SqlAlterStreamStmtT));
            alterStream->tag = T_STREAM_ALTER_STMT;
            alterStream->name = $4;
            alterStream->newWhereCond = $8;
            $$ = (NodeT *)alterStream;
        }
    ;

alter_union_stream:
    ALTER STREAM node_type stream_table_name ALTER FROM_UNION alter_type alter_table_name
        {
            SqlAlterUnionStmtT *alterUnion = (SqlAlterUnionStmtT *)SqlCreateStruct(sizeof(SqlAlterUnionStmtT));
            alterUnion->tag = T_STREAM_ALTER_UNION_STMT;
            alterUnion->nodeType = $3;
            alterUnion->alterType = $7;
            alterUnion->name = $4;
            alterUnion->fromList = $8;
            $$ = (NodeT *)alterUnion;
        }
    ;

alter_table_name:
    table_name
        {
            $$ = SqlCreateList(sizeof(SqlSrcItemT *));
            SqlSrcAddTable($$, $1, NULL, NULL);
        }
    | dispatch_clause
        {
            $$ = SqlCreateList(sizeof(SqlSrcItemT *));
            SqlSrcAddDispatch($$, $1, NULL, NULL);
        }
    ;

alter_type:
    ADD { $$ = STREAM_ALTER_ADD; }
    | DROP { $$ = STREAM_ALTER_DROP; }
    ;

node_type:
    SINK { $$ = STREAM_SINK; }
    | VIEW { $$ = STREAM_VIEW; }
    ;

/*
 *  Drop Statements
*/
drop_stream_table:
    DROP STREAM drop_type drop_name
        {
            SqlDropStmtT *dropStmt = (SqlDropStmtT *)SqlCreateStruct(sizeof(SqlDropStmtT));
            dropStmt->tag = T_STREAM_DROP_TABLE_STMT;
            dropStmt->name = $4;
            dropStmt->dropElemType = $3;
            $$ = (NodeT *)dropStmt;
        }
    ;

drop_stream_ref:
    DROP STREAM REFERENCE drop_name
        {
            SqlDropStreamRefStmtT *dropStmt = (SqlDropStreamRefStmtT *)SqlCreateStruct(sizeof(SqlDropStreamRefStmtT));
            dropStmt->tag = T_STREAM_DROP_REF_STMT;
            dropStmt->name = $4;
            $$ = (NodeT *)dropStmt;
        }
    ;

drop_type:
    TABLE { $$ = DROP_STREAM_SOURCE; }
    | SINK { $$ = DROP_STREAM_SINK; }
    | VIEW { $$ = DROP_STREAM_VIEW; }
    ;

outputType:
    typeName output_type_params
        {
            StreamSinkTypeStmtT streamSinkTypeStmt = {};
            if ($1 == NULL) {
                SqlParserConditionAbort(true, "Unable to parse sink type.", GMERR_SYNTAX_ERROR);
            } else if (DbStrCmp($1, "tsdb", true) == 0) {
                streamSinkTypeStmt.sinkType = STREAM_SINK_TABLE;
            } else if (DbStrCmp($1, "server_socket", true) == 0) {
                streamSinkTypeStmt.sinkType = STREAM_SINK_SOCKET;
            } else if (DbStrCmp($1, "file", true) == 0) {
                streamSinkTypeStmt.sinkType = STREAM_SINK_FILE;
            } else if (DbStrCmp($1, "pubsub_channel", true) == 0) {
                streamSinkTypeStmt.sinkType = STREAM_SINK_PUBSUB;
            } else {
                SqlParserConditionAbort(true, "Unable to parse sink type.", GMERR_SYNTAX_ERROR);
            }
            streamSinkTypeStmt.streamSinkName = $2;
            $$ = streamSinkTypeStmt;
        }
    ;

output_type_params:
    '(' sinkDestinationName ')' { $$ = $2; }
    |   { $$ = NULL; }
    ;

sinkDestinationName:
    name
        {
            $$ = $1;
        }
    | SQL_STRING
        {
            $$ = $1;
        }
    ;
 
drop_name:
    name opt_dot_name
        {
            SqlFullNameT *fullName = (SqlFullNameT *)SqlCreateStruct(sizeof(SqlFullNameT));
            if ($2 == NULL) {
                fullName->database = NULL;
                fullName->name = $1;
                fullName->indexName = NULL;
            } else {
                fullName->database = $1;
                fullName->name = $2;
                fullName->indexName = NULL;
            }
            $$ = fullName;
        }
    ;

upsert_into_ref:
    UPSERT INTO STREAMREF stream_table_name VALUES insert_values_clause
    	{
	        SqlInsertStreamRefStmtT *insert = (SqlInsertStreamRefStmtT *)SqlCreateStruct(sizeof(SqlInsertStreamRefStmtT));
	        insert->tag = T_STREAM_UPSERT_INTO_REF;
	        insert->refName = $4;
	        insert->value = $6;
	        $$ = (NodeT *)insert;
    	}
    ;

insert_values_clause:
    '(' values_list ')'
        {
            SqlInsertValueT *insertValues = (SqlInsertValueT *)SqlCreateStruct(sizeof(SqlInsertValueT));
            insertValues->type = VALUES_LIST;
            insertValues->valuesList = $2;
            $$ = insertValues;
        }
    ;

values_list:
    values
        {
            DbListT *valuesList = SqlCreateList(sizeof(DbListT *));
            SqlListAppend(valuesList, &($1));
            $$ = valuesList;
        }
    | values_list ',' values
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;

values:
    literal_value
	    {
	        $$ = $1;
	    }
    ;


column_list:
    column_list ',' column_def
        {
            ($3)->colIdx = DbListGetItemCnt($1);
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    | column_def
        {
            DbListT *columnList = SqlCreateList(sizeof(SqlColumnDefT *));
            ($1)->colIdx = 0;
            SqlListAppend(columnList, &($1));
            $$ = columnList;
        }
    ;

column_def:
    name type_name opt_type_char_len default_opt
        {
            SqlColumnDefT *columnDef = (SqlColumnDefT *)SqlCreateStruct(sizeof(SqlColumnDefT));
            DbStrToLower($1);
            columnDef->hName = DbStrToHash32($1);
            columnDef->colName = $1;
            columnDef->typeName = $2;
            columnDef->charLen = $3;
            columnDef->constraints = NULL;
            columnDef->defaultExpr = (SqlExprT *) $4;
            $$ = columnDef;
        }
    ;

type_name:
    name        { $$ = $1; }
    | /*EMPTY*/ { $$ = NULL; }
    ;

opt_type_char_len:
    '(' char_len_value ')'
    {
        SqlExprConstT *constVal =  (SqlExprConstT *)($2->op);
        SqlParserConditionAbort(constVal->arg.value.longValue < 0,
            "Char length should be larger than or equal to 0.", GMERR_INVALID_PARAMETER_VALUE);
        $$ = constVal->arg.value.longValue;
    }
    | /*EMPTY*/ { $$ = 0; }
    ;

stream_table_watermark_def:
     ',' WATERMARK FOR name AS expr incremental_policy
        {
            StreamWatermarkT *watermark = (StreamWatermarkT *)SqlCreateStruct(sizeof(StreamWatermarkT));
            watermark->colName = $4;
            watermark->expr = $6;
            watermark->isTolerant = $7;
            $$ = watermark;
        }
    |/* EMPTY */
        { $$ = NULL; }
    ;
incremental_policy:
    type_name
    {
        if($1==NULL){
            $$ = false;
        }else{
            DbStrToLower($1);
            if (DbStrCmp($1, "tolerant", true) == 0) {
                $$ = true;
            }else if(DbStrCmp($1, "strict", true) == 0){
                $$ = false;
            }else{
               stream_error("Failed to parse sql, only tolerant or strict is supported");
               SqlParserAbort(GMERR_SYNTAX_ERROR);
            }
        }
    }
    ;
create_with_clause:
    WITH '(' create_with_list ')'
        {
            $$ = $3;
        }
    | /*EMPTY*/
        {
            $$ = NULL;
            // SqlParseAddError();
        }
    ;

create_with_list:
    create_with_single
        {
            DbListT *cwithList = (DbListT *)SqlCreateList(sizeof(SqlCreateWithT));
            SqlListAppend(cwithList, &($1));
            $$ = cwithList;
        }
    | create_with_list ',' create_with_single
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;

create_with_single:
    name EQUALS SQL_STRING
        {
            SqlCreateWithT *cwith = (SqlCreateWithT *)SqlCreateStruct(sizeof(SqlCreateWithT));
            cwith->key = $1;
            cwith->value = $3;
            $$ = cwith;
        }
    | name EQUALS INTEGER_STRING
        {
            SqlCreateWithT *cwith = (SqlCreateWithT *)SqlCreateStruct(sizeof(SqlCreateWithT));
            cwith->key = $1;
            cwith->value = $3;
            $$ = cwith;
        }
    | name EQUALS S_TRUE
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            SqlCreateWithT *cwith = (SqlCreateWithT *)SqlCreateStruct(sizeof(SqlCreateWithT));
            TextT dstText;
            Status ret = DbCopyStrToText(sqlPList->memCtx, &dstText, "true", strlen("true"));
            SqlParserConditionAbort(ret != GMERR_OK, "Unable to copy const string", ret);
            cwith->key = $1;
            cwith->value = dstText.str;
            $$ = cwith;
        }
    | name EQUALS S_FALSE
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            SqlCreateWithT *cwith = (SqlCreateWithT *)SqlCreateStruct(sizeof(SqlCreateWithT));
            TextT dstText;
            Status ret = DbCopyStrToText(sqlPList->memCtx, &dstText, "false", strlen("false"));
            SqlParserConditionAbort(ret != GMERR_OK, "Unable to copy const string", ret);
            cwith->key = $1;
            cwith->value = dstText.str;
            $$ = cwith;
        }
    ;

stream_table_name:
    name opt_dot_name
        {
            SqlTableRefT *tableRef = (SqlTableRefT *)SqlCreateStruct(sizeof(SqlTableRefT));
            if ($2 == NULL) {
                tableRef->database = NULL;
                tableRef->tableName = $1;
            } else {
                char *tableName = SqlCatTableName($1, $2);
                tableRef->database = NULL;
                tableRef->tableName = tableName;
            }
            tableRef->aliasName = NULL;
            $$ = tableRef;
        }
    ;


typeName:
    name { $$ = $1; }
    |   { $$ = NULL; }
    ;

opt_dot_name:
    '.' name { $$ = $2; }
    |   { $$ = NULL; }
    ;

name:
    SQL_ID { $$ = $1; }
    ;

select_stmt:
    SELECT target_list from_clause opt_where opt_groupby
        {
            SqlSelectStmtT *select = (SqlSelectStmtT *)SqlCreateStruct(sizeof(SqlSelectStmtT));
            select->tag = T_SQL_SELECT_STMT;
            select->targetList = $2;
            select->fromClause = $3;
            select->whereClause = $4;
            select->groupBy = $5;
            select->multiSelectOp = SELECT_SIMPLE;
            $$ = (NodeT *)select;
        }
    ;

opt_groupby:
    GROUP BY groupby_list
        {
            $$ = $3;
        }
    | /* EMPTY */
        { $$ = NULL; }
    ;

opt_where:
    WHERE expr
        {
            $$ = $2;
        }
    | /* EMPTY */
        { $$ = NULL; }
    ;

default_opt:
    DEFAULT expr
        {
            $$ = $2;
        }
    | /* EMPTY */
        {
            $$ = NULL;
        }
    ;

groupby_list:
    groupby_single
        {
            DbListT *groupByList = SqlCreateList(sizeof(SqlGroupByT *));
            SqlListAppend(groupByList, &($1));
            $$ = groupByList;
        }
    | groupby_list ',' groupby_single
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;
groupby_single:
    column_ref
        {
            SqlGroupByT *groupBy = (SqlGroupByT *)SqlCreateStruct(sizeof(SqlGroupByT));
            groupBy->key = $1;
            $$ = groupBy;
        }
    ;
qualified_name:
    name opt_dot_name
        {
            SqlTableRefT *tableRef = (SqlTableRefT *)SqlCreateStruct(sizeof(SqlTableRefT));
            if ($2 == NULL) {
                tableRef->database = NULL;
                tableRef->tableName = $1;
            } else {
                char *tableName = SqlCatTableName($1, $2);
                tableRef->database = NULL;
                tableRef->tableName = tableName;
            }
            tableRef->aliasName = NULL;
            $$ = tableRef;
        }
    ;

string_name:
    SQL_STRING
        {
            SqlTableRefT *tableRef = (SqlTableRefT *)SqlCreateStruct(sizeof(SqlTableRefT));
            tableRef->database = NULL;
            tableRef->tableName = $1;
            tableRef->aliasName = NULL;
            $$ = tableRef;
        }
    ;

table_name:
    qualified_name opt_as_name
        {
	        ($1)->aliasName = $2;
	        $$ = $1;
        }
    | string_name opt_as_name
        {
	        ($1)->aliasName = $2;
	        $$ = $1;
        }
    ;

from_clause:
    FROM from_list
        {
            $$ = $2;
        }
    | /* EMPTY */ { $$ = NULL; }
    ;

from_union_list:
    TABLE '(' UNION '(' table_list ')' ')'
        {
            $$ = $5;
        }
    ;
table_list:
    TABLE table_name
        {
            $$ = SqlCreateList(sizeof(SqlSrcItemT *));
            SqlSrcAddTable($$, $2, NULL, NULL);
        }
    | table_list ',' TABLE table_name
        {
            $$ = $1;
            SqlSrcAddTable($1, $4, NULL, NULL);
        }
    | dispatch_clause
        {
            $$ = SqlCreateList(sizeof(SqlSrcItemT *));
            SqlSrcAddDispatch($$, $1, NULL, NULL);
        }
    | table_list ',' dispatch_clause
        {
            $$ = $1;
            SqlSrcAddDispatch($1, $3, NULL, NULL);
        }
    ;

from_list:
    table_name
        {
            $$ = SqlCreateList(sizeof(SqlSrcItemT *));
            SqlSrcAddTable($$, $1, NULL, NULL);
        }
    | window_clause
        {
            $$ = SqlCreateList(sizeof(SqlSrcItemT *));
            SqlSrcAddWindow($$, $1, NULL, NULL);
        }
    | dispatch_clause
        {
            $$ = SqlCreateList(sizeof(SqlSrcItemT *));
            SqlSrcAddDispatch($$, $1, NULL, NULL);
        }
    | from_union_list
    /* DO NOT SUPPORT MULTI TABLES NOW*/
    ;

from_group_clause:
    FROM group_name '(' from_group_list')'
        {
            FromGroupT *groupDef = (FromGroupT *)SqlCreateStruct(sizeof(FromGroupT));
            groupDef->groupName = $2;
            groupDef->memberNameConfigs = $4;
            $$ = groupDef;
 
        }
    | /* EMPTY */ { $$ = NULL; }
    ;
 
group_name:
    name
        {
            $$ = $1;
        }
    ;
 
from_group_list:
    from_group_list ',' file_path
        {
            ($3)->colIdx = DbListGetItemCnt($1);
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    | file_path
        {
            DbListT *columnList = SqlCreateList(sizeof(SqlColumnDefT *));
            ($1)->colIdx = 0;
            SqlListAppend(columnList, &($1));
            $$ = columnList;
        }
    ;
 
file_path:
    SQL_STRING
        {
            SqlColumnDefT *columnDef = (SqlColumnDefT *)SqlCreateStruct(sizeof(SqlColumnDefT));
            DbStrToLower($1);
            columnDef->hName = DbStrToHash32($1);
            columnDef->colName = $1;
            columnDef->constraints = NULL;
            $$ = columnDef;
        }
    ;

window_clause:
    TABLE '(' HOP '(' TABLE name ',' name ',' window_slide ',' window_size window_offset ')' ')'
        {
            StreamWindowExprT *window = (StreamWindowExprT *)SqlCreateStruct(sizeof(StreamWindowExprT));
            window->tableName = $6;
            window->timeColName = $8;
            window->slide = $10;
            window->size = $12;
            window->offset = $13;
            $$ = window;
        }
    | TABLE '(' TUMBLE '('TABLE name ',' name ',' window_size window_offset ')' ')'
        {
            StreamWindowExprT *window = (StreamWindowExprT *)SqlCreateStruct(sizeof(StreamWindowExprT));
            window->tableName = $6;
            window->timeColName = $8;
            window->slide = $10;
            window->size = $10;
            window->offset = $11;
            $$ = window;
        }
    | TABLE '(' HOP '(' TABLE dispatch_in_tvf ',' name ',' window_slide ',' window_size window_offset ')' ')'
        {
            StreamWindowExprT *window = (StreamWindowExprT *)SqlCreateStruct(sizeof(StreamWindowExprT));
            window->dispatch = $6;
            window->tableName = window->dispatch->tableName;
            window->timeColName = $8;
            window->slide = $10;
            window->size = $12;
            window->offset = $13;
            $$ = window;
        }
    | TABLE '(' TUMBLE '('TABLE dispatch_in_tvf ',' name ',' window_size window_offset ')' ')'
        {
            StreamWindowExprT *window = (StreamWindowExprT *)SqlCreateStruct(sizeof(StreamWindowExprT));
            window->dispatch = $6;
            window->tableName = window->dispatch->tableName;
            window->timeColName = $8;
            window->slide = $10;
            window->size = $10;
            window->offset = $11;
            $$ = window;
        }
    ;

dispatch_clause:
    TABLE '(' DISPATCH '(' TABLE name ',' dispatchfrom_list ')' ')'
        {
            StreamDispatchExprT *dispatch = (StreamDispatchExprT *)SqlCreateStruct(sizeof(StreamDispatchExprT));
            dispatch->tableName = $6;
            dispatch->dispatchFromList = $8;
            $$ = dispatch;
        }
    ;

dispatch_in_tvf:
    DISPATCH '(' TABLE name ',' dispatchfrom_list ')'
        {
            StreamDispatchExprT *dispatch = (StreamDispatchExprT *)SqlCreateStruct(sizeof(StreamDispatchExprT));
            dispatch->tableName = $4;
            dispatch->dispatchFromList = $6;
            $$ = dispatch;
        }
    ;

window_slide:
   INTERVAL SQL_STRING window_unit
        {
            if (!DbCheckDigit($2)) {
                if(IsValidNumber($2)){
                    stream_error("Failed to parse sql, window slide is invalid");
                    SqlParserAbort(GMERR_PROGRAM_LIMIT_EXCEEDED);
                }
                stream_error("Failed to parse sql, window slide is invalid");
                SqlParserAbort(GMERR_SYNTAX_ERROR);
            }
            $$ = strtoll($2, NULL, 10);
        }
    ;

window_size:
    INTERVAL SQL_STRING window_unit
        {
            if (!DbCheckDigit($2)) {
                if(IsValidNumber($2)){
                    stream_error("Failed to parse sql, window size is invalid");
                    SqlParserAbort(GMERR_PROGRAM_LIMIT_EXCEEDED);
                }
                stream_error("Failed to parse sql, window size is invalid");
                SqlParserAbort(GMERR_SYNTAX_ERROR);
            }
            $$ = strtoll($2, NULL, 10);
        }
    ;

window_unit:
    SQL_ID
    {
        if ($1 == NULL){
            stream_error("Failed to parse sql, window id is invalid");
            SqlParserAbort(GMERR_SYNTAX_ERROR);
        }else if (DbStrCmp($1, "SECONDS", true) != 0) {
            stream_error("Failed to parse sql, only SECONDS is supported");
            SqlParserAbort(GMERR_SYNTAX_ERROR);
        }
        $$ = 0;
    }
    //Currently, only SECONDS is supported.

window_offset:
    ',' INTEGER_STRING
        {
            $$ = strtoll($2, NULL, 10);
        }
     | /*EMPTY*/ { $$ = 0; }
     ;

target_list:
    target_single
        {
            DbListT *newList = SqlCreateList(sizeof(SqlTargetT *));
            SqlListAppend(newList, &($1));
            $$ = newList;
        }
    | target_list ',' target_single
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;

target_single:
    expr opt_as_name
        {
            SqlTargetT *target = (SqlTargetT *)SqlCreateStruct(sizeof(SqlTargetT));
            target->isStar = false;
            target->value = $1;
            target->aliasName = $2;
            $$ = target;
        }
    | name '.' '*'
        {
            SqlTargetT *target = (SqlTargetT *)SqlCreateStruct(sizeof(SqlTargetT));
            target->isStar = true;
            target->aliasName = NULL;
            SqlParsedListT *sqlPList = SqlGetParseList();
            target->value = SqlExprMakeColumn(sqlPList->memCtx, NULL, $1, NULL);
            SqlParserConditionAbort(target->value == NULL, "Failed to make expr", GMERR_OUT_OF_MEMORY);
            $$ = target;
        }
    | '*'
        {
            SqlTargetT *target = (SqlTargetT *)SqlCreateStruct(sizeof(SqlTargetT));
            target->isStar = true;
            target->aliasName = NULL;
            // to make test easy
            SqlParsedListT *sqlPList = SqlGetParseList();
            target->value = SqlExprMakeColumn(sqlPList->memCtx, NULL, NULL, NULL);
            SqlParserConditionAbort(target->value == NULL, "Failed to make expr", GMERR_OUT_OF_MEMORY);
            $$ = target;
        }
    ;

opt_as_name:
    AS name { $$ = $2; }
    | SQL_ID { $$ = $1; }
    | { $$ = NULL; }
    ;

opt_dispatchby:
    DISPATCH BY dispatchby_list
        {
            $$ = $3;
        }
    | /* EMPTY */
        { $$ = NULL; }
    ;

dispatchby_list:
    dispatchby_single
        {
            DbListT *dispatchByList = SqlCreateList(sizeof(SqlDispatchByT *));
            SqlListAppend(dispatchByList, &($1));
            $$ = dispatchByList;
        }
    | dispatchby_list ',' dispatchby_single
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;

dispatchfrom_list:
    dispatchfrom_single
        {
            DbListT *dispatchFromList = SqlCreateList(sizeof(SqlDispatchByT *));
            SqlListAppend(dispatchFromList, &($1));
            $$ = dispatchFromList;
        }
    | dispatchfrom_list ',' dispatchfrom_single
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;

dispatchfrom_single:
    literal_value
        {
            SqlDispatchByT *dispatchBy = (SqlDispatchByT *)SqlCreateStruct(sizeof(SqlDispatchByT));
            dispatchBy->key = $1;
            $$ = dispatchBy;
        }
    ;

dispatchby_single:
    column_ref
        {
            SqlDispatchByT *dispatchBy = (SqlDispatchByT *)SqlCreateStruct(sizeof(SqlDispatchByT));
            dispatchBy->key = $1;
            $$ = dispatchBy;
        }
    | ref_fun
        {
            SqlDispatchByT *dispatchBy = (SqlDispatchByT *)SqlCreateStruct(sizeof(SqlDispatchByT));
            dispatchBy->key = $1;
            $$ = dispatchBy;
        }
    ;

opt_partitionby:
    PARTITION BY partitionby_list
        {
            $$ = $3;
        }
    | /* EMPTY */
        { $$ = NULL; }
    ;

partitionby_list:
    partitionby_single
        {
            DbListT *partitionByList = SqlCreateList(sizeof(SqlPartitionByT *));
            SqlListAppend(partitionByList, &($1));
            $$ = partitionByList;
        }
    | partitionby_list ',' partitionby_single
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;

partitionby_single:
    column_ref
        {
            SqlPartitionByT *partitionBy = (SqlPartitionByT *)SqlCreateStruct(sizeof(SqlPartitionByT));
            partitionBy->key = $1;
            $$ = partitionBy;
        }
    ;

/* for expr operator and item */
expr:
    expr_base
        {
            $$ = $1;
        }
    // UMINUS for precedence declarations
    | expr_in
        {
            $$ = $1;
        }
    | '+' expr    %prec UMINUS
        {
            $$ = SqlParserExprMakeUnary($2, SQL_EXPR_OP_UPLUS);
        }
    | '-' expr    %prec UMINUS
        {
            $$ = SqlParserExprMakeUnary($2, SQL_EXPR_OP_UMINUS);
        }
    | NOT expr
        {
            $$ = SqlParserExprMakeUnary($2, SQL_EXPR_OP_NOT);
        }
    | expr ISNULL
        {
            $$ = SqlParserExprMakeUnary($1, SQL_EXPR_OP_ISNULL);
        }
    | expr NOTNULL
        {
            $$ = SqlParserExprMakeUnary($1, SQL_EXPR_OP_NOTNULL);
        }
    | expr NOT S_NULL %prec NOTNULL
        {
            $$ = SqlParserExprMakeUnary($1, SQL_EXPR_OP_NOTNULL);
        }
    | '~' expr
        {
            $$ = SqlParserExprMakeUnary($2, SQL_EXPR_OP_BITNOT);
        }
    | expr '+' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_ADD);
        }
    | expr '-' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_SUB);
        }
    | expr '*' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_MULTI);
        }
    | expr '/' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_DIV);
        }
    | expr '%' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_MOD);
        }
    | expr AND expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_AND);
        }
    | expr OR expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_OR);
        }
    | expr '<' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_LT);
        }
    | expr '>' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_GT);
        }
    | expr LESS_EQUALS expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_LE);
        }
    | expr GREATER_EQUALS expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_GE);
        }
    | expr EQUALS expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_EQ);
        }
    | expr NOT_EQUALS expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_NE);
        }
    | expr '|' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_BITOR);
        }
    | expr '&' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_BITAND);
        }
    | expr CONCAT expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_CONCAT);
        }
    | expr IS expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_IS);
        }
    | expr LSHIFT expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_LSHIFT);
        }
    | expr RSHIFT expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_RSHIFT);
        }
    | expr LIKE expr
         {
            SqlExprT *expr = (SqlExprT *)SqlCreateStruct(sizeof(SqlExprT) + sizeof(SqlExprLikeT));
            SqlExprLikeT *exprLike = (SqlExprLikeT *)(void *)((uint8_t *)expr + sizeof(SqlExprT));
            exprLike->expr.type = SQL_EXPR_OP_LIKE;
            exprLike->isNot = false;
            exprLike->isGlobMatch = false;
            exprLike->src = $1;
            exprLike->dst = $3;
            SqlParsedListT *sqlPList = SqlGetParseList();
            DmValueT value = {};
            value.type = DB_DATATYPE_FIXED;
            value.value.strAddr = DbDynMemCtxAlloc(sqlPList->memCtx, 2);
            SqlParserConditionAbort(value.value.strAddr == NULL, "Unable to alloc string.", GMERR_OUT_OF_MEMORY);
            memcpy_s((char *)value.value.strAddr, 2, "\\", 2);
            value.value.length = 2;
            $$ = SqlExprMakeConst(sqlPList->memCtx, &value, NULL);
            SqlParserConditionAbort($$ == NULL, "Unable to make expr.", GMERR_OUT_OF_MEMORY);
            exprLike->escape = $$;
            expr->op = (SqlOpBaseT *)(void *)exprLike;
            $$ = expr;
         }
    | expr LIKE expr ESCAPE expr
         {
             SqlExprT *expr = (SqlExprT *)SqlCreateStruct(sizeof(SqlExprT) + sizeof(SqlExprLikeT));
             SqlExprLikeT *exprLike = (SqlExprLikeT *)(void *)((uint8_t *)expr + sizeof(SqlExprT));
             exprLike->expr.type = SQL_EXPR_OP_LIKE;
             exprLike->isNot = false;
             exprLike->isGlobMatch = false;
             exprLike->src = $1;
             exprLike->dst = $3;
             exprLike->escape = $5;
             expr->op = (SqlOpBaseT *)(void *)exprLike;
             $$ = expr;
         }
    | '(' expr ')'
        {
            $$ = $2;
        }
    | '(' expr_list ',' expr ')'
        {
            SqlListAppend($2, &($4));
            SqlExprT *expr = (SqlExprT *)SqlCreateStruct(sizeof(SqlExprT) + sizeof(SqlExprListT));
            SqlExprListT *exprList = (SqlExprListT *)(void *)((uint8_t *)expr + sizeof(SqlExprT));
            exprList->expr.type = SQL_EXPR_ITEM_LIST;
            exprList->exprList = $2;
            expr->op = (SqlOpBaseT *)(void *)exprList;
            $$ = expr;
        }
    | expr OVER '(' opt_partitionby ')'
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            SqlExprT *expr = SqlExprMakeOverFunc(sqlPList->memCtx, $1, $4);
            SqlParserConditionAbort(expr == NULL, "Failed to make expr", GMERR_OUT_OF_MEMORY);
            $$ = expr;
        }
    ;

expr_list:
    expr
        {
            DbListT *exprList = SqlCreateList(sizeof(SqlExprT *));
            SqlListAppend(exprList, &($1));
            $$ = exprList;
        }
    | expr_list ',' expr
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;

/* for expr item */
expr_base:
    literal_value
        {
            $$ = $1;
        }
    | column_ref
        {
            $$ = $1;
        }
    | ref_fun
        {
            $$ = $1;
        }
    | distinct_count_func
        {
            $$ = $1;
        }
    | time_expr
        {
            $$ = $1;
        }
    | format_func
        {
            $$ = $1;
        }
    | agg_func
        {
            $$ = $1;
        }
    | current_time_second_func
        {
            $$ = $1;
        }
    | get_hostname_func
        {
            $$ = $1;
        }
    ;

ref_arg:
    expr
        {
            $$ = $1;
        }
    ;


ref_fun:
    REF '[' ref_arg ']' '[' ref_arg ']'
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            SqlAggFuncArgsT aggArgs = {
                .aggStar = false,
                .aggDistinct = false
            };
            DbListT *newList = SqlCreateList(sizeof(SqlExprT *));
            SqlListAppend(newList, &($3));
            SqlListAppend(newList, &($6));
            SqlExprFuncTypeE funcType = FUNC_TYPE_REF;
            $$ = SqlExprMakeFunc(sqlPList->memCtx, funcType, newList, aggArgs);
            SqlParserConditionAbort($$ == NULL, "Make expr with problem.", GMERR_OUT_OF_MEMORY);
        }

func_arg_list:
    expr
        {
            DbListT *newList = SqlCreateList(sizeof(SqlExprT *));
            SqlListAppend(newList, &($1));
            $$ = newList;
        }
    | func_arg_list ',' expr
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;

distinct_count_func:
    SEQ_DISTINCT_COUNT '(' '*' ')'
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            SqlAggFuncArgsT aggArgs = {
                .aggStar = true,
                .aggDistinct = false,
            };
            DbListT *newList = SqlCreateList(sizeof(SqlExprT *));
            SqlExprFuncTypeE funcType = FUNC_TYPE_SEQDISTINCTCOUNT;
            $$ = SqlExprMakeFunc(sqlPList->memCtx, funcType, newList, aggArgs);
            SqlParserConditionAbort($$ == NULL, "Make SeqDistctCnt with problem.", GMERR_OUT_OF_MEMORY);
        }
    | SEQ_DISTINCT_COUNT '(' func_arg_list ')'
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            SqlAggFuncArgsT aggArgs = {
                .aggStar = false,
                .aggDistinct = false
            };
            SqlExprFuncTypeE funcType = FUNC_TYPE_SEQDISTINCTCOUNT;
            $$ = SqlExprMakeFunc(sqlPList->memCtx, funcType, $3, aggArgs);
            SqlParserConditionAbort($$ == NULL, "Make expr with problem.", GMERR_OUT_OF_MEMORY);
        }
    ;

agg_func:
    name '(' func_arg_list ')'
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            SqlAggFuncArgsT aggArgs = {
                .aggStar = false,
                .aggDistinct = false
            };
            SqlExprFuncTypeE funcType = FUNC_TYPE_AGG_END;
            Status ret = SqlExprGetFuncTypeByName($1, &funcType);
            SqlParserConditionAbort(ret != GMERR_OK, "Function not support", ret);
            $$ = SqlExprMakeFunc(sqlPList->memCtx, funcType, $3, aggArgs);
            SqlParserConditionAbort($$ == NULL, "Make expr with problem.", GMERR_OUT_OF_MEMORY);
        }
    | name '(' ')'
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            SqlAggFuncArgsT aggArgs = {
                .aggStar = false,
                .aggDistinct = false
            };
            SqlExprFuncTypeE funcType = FUNC_TYPE_AGG_END;
            Status ret = SqlExprGetFuncTypeByName($1, &funcType);
            SqlParserConditionAbort(ret != GMERR_OK, "Function not support", ret);
            SqlExprT *expr = SqlExprMakeFunc(sqlPList->memCtx, funcType, NULL, aggArgs);
            SqlParserConditionAbort(expr == NULL, "Failed to make expr", GMERR_OUT_OF_MEMORY);
            $$ = expr;
        }
    ;

time_expr:
    INTERVAL SQL_STRING window_unit
        {
            if (!DbCheckDigit($2)) {
                if(IsValidNumber($2)){
                    stream_error("Failed to parse sql, window size is invalid");
                    SqlParserAbort(GMERR_PROGRAM_LIMIT_EXCEEDED);
                }
                stream_error("Failed to parse sql, window size is invalid");
                SqlParserAbort(GMERR_SYNTAX_ERROR);
            }
            SqlParsedListT *sqlPList = SqlGetParseList();
            DmValueT value = {};
            value.type = DB_DATATYPE_FIXED;
            value.value.strAddr = $2;
            value.value.length = strlen($2) + 1;
            $$ = SqlExprMakeConst(sqlPList->memCtx, &value, $2);
            SqlParserConditionAbort($$ == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
        }
    ;

format_func:
    FORMAT '(' func_arg_list ')'
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            SqlAggFuncArgsT aggArgs = {
                .aggStar = false,
                .aggDistinct = false,
            };
            SqlExprFuncTypeE funcType = FUNC_TYPE_FORMAT;
            $$ = SqlExprMakeFunc(sqlPList->memCtx, funcType, $3, aggArgs);
            SqlParserConditionAbort($$ == NULL, "Make Format with problem.", GMERR_OUT_OF_MEMORY);
        }
    ;

current_time_second_func:
    current_time_second '(' ')'
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            SqlAggFuncArgsT aggArgs = {
                .aggStar = false,
                .aggDistinct = false,
            };
            SqlExprFuncTypeE funcType = FUNC_TYPE_CURRENT_TIME_SECOND;
            $$ = SqlExprMakeFunc(sqlPList->memCtx, funcType, NULL, aggArgs);
            SqlParserConditionAbort($$ == NULL, "Error while creating current_time_second.", GMERR_OUT_OF_MEMORY);
        }

get_hostname_func:
    get_hostname '(' ')'
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            SqlAggFuncArgsT aggArgs = {
                .aggStar = false,
                .aggDistinct = false,
            };
            SqlExprFuncTypeE funcType = FUNC_TYPE_HOSTNAME;
            $$ = SqlExprMakeFunc(sqlPList->memCtx, funcType, NULL, aggArgs);
            SqlParserConditionAbort($$ == NULL, "Error while creating get_hostname.", GMERR_OUT_OF_MEMORY);
        }

char_len_value:
    INTEGER_STRING
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            DmValueT value = {};
            value.type = DB_DATATYPE_INT64;
            errno = 0;
            value.value.longValue = strtoll($1, NULL, 10);
            TextT dstText;
            Status ret = DbCopyStrToText(sqlPList->memCtx, &dstText, $1, strlen($1));
            SqlParserConditionAbort(ret != GMERR_OK, "Unable to copy const string", ret);
            $$ = SqlExprMakeConst(sqlPList->memCtx, &value, dstText.str);
            SqlParserConditionAbort($$ == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
        }
    ;

literal_value:
    INTEGER_STRING
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            DmValueT value = {};
            value.type = DB_DATATYPE_INT64;
            errno = 0;
            value.value.longValue = strtoll($1, NULL, 10);
            TextT dstText;
            Status ret = DbCopyStrToText(sqlPList->memCtx, &dstText, $1, strlen($1));
            SqlParserConditionAbort(ret != GMERR_OK, "Unable to copy const string", ret);
            $$ = SqlExprMakeConst(sqlPList->memCtx, &value, dstText.str);
            SqlParserConditionAbort($$ == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
        }
    | SQL_STRING
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            DmValueT value = {};
            value.type = DB_DATATYPE_STRING;
            value.value.strAddr = $1;
            value.value.length = strlen($1) + 1;
            $$ = SqlExprMakeConst(sqlPList->memCtx, &value, $1);
            SqlParserConditionAbort($$ == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
        }
    | DOUBLE_STRING
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            DmValueT value = {};
            value.type = DB_DATATYPE_DOUBLE;
            value.value.doubleValue = strtod($1, NULL);
            TextT dstText;
            Status ret = DbCopyStrToText(sqlPList->memCtx, &dstText, $1, strlen($1));
            SqlParserConditionAbort(ret != GMERR_OK, "Unable to copy const string", ret);
            $$ = SqlExprMakeConst(sqlPList->memCtx, &value, dstText.str);
            SqlParserConditionAbort($$ == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
        }
    ;

column_ref:
    /* one SQL_STRING cannot be a column*/
    SQL_ID
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            $$ = SqlExprMakeColumn(sqlPList->memCtx, NULL, NULL, $1);
            SqlParserConditionAbort($$ == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
        }
    | name '.' name
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            $$ = SqlExprMakeColumn(sqlPList->memCtx, NULL, $1, $3);
            SqlParserConditionAbort($$ == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
        }
    | name '.' name '.' name
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            char *tableName = SqlCatTableName($1, $3);
            $$ = SqlExprMakeColumn(sqlPList->memCtx, NULL, tableName, $5);
            SqlParserConditionAbort($$ == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
        }
    ;

%%
