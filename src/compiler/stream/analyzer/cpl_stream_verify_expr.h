/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: for verify expr in ts module
 * Author: yangchonghuan
 * Create: 2024-10-17
 */

#ifndef CPL_STREAM_VERIFY_EXPR_H
#define CPL_STREAM_VERIFY_EXPR_H

#include "cpl_ir_plan.h"
#include "cpl_public_sql_parser_common.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct StreamItemOpTreeCtx {
    uint32_t whereExprDepth;
    DbMemCtxT *memCtx;
    const AASchemaT *schema;
    const DbListT *fromClause;
} StreamItemOpTreeCtxT;

static inline bool StreamIsConstExpr(SqlExprT *expr)
{
    if (expr == NULL) {
        return false;
    }
    return expr->op->type == SQL_EXPR_ITEM_CONST || expr->op->type == SQL_EXPR_OP_UMINUS ||
           expr->op->type == SQL_EXPR_OP_UPLUS;
}

static inline bool StreamIsColumnExpr(SqlExprT *expr)
{
    if (expr == NULL) {
        return false;
    }
    return expr->op->type == SQL_EXPR_ITEM_COLUMN;
}

// 目前只支持加减法
static inline bool StreamIsArithExprType(SqlExprT *expr)
{
    if (expr == NULL) {
        return false;
    }
    return (expr->op->type >= SQL_EXPR_OP_MULTI) && (expr->op->type <= SQL_EXPR_OP_SUB);
}

static inline bool StreamIsInt64ConstExpr(SqlExprT *expr)
{
    if (expr->op->type == SQL_EXPR_OP_UPLUS || expr->op->type == SQL_EXPR_OP_UMINUS) {
        SqlExprConstT *constExpr = (SqlExprConstT *)expr->children[0]->op;
        return constExpr->arg.type == DB_DATATYPE_INT64;
    }

    if (expr->op->type != SQL_EXPR_ITEM_CONST) {
        return false;
    }
    SqlExprConstT *constExpr = (SqlExprConstT *)expr->op;
    return constExpr->arg.type == DB_DATATYPE_INT64;
}

// 获取兄弟节点
static inline SqlExprT *StreamGetBroExpr(SqlExprT *expr)
{
    DB_ASSERT(expr->father->children[0] != NULL && expr->father->children[1] != NULL);
    if (expr->father->children[0] == expr) {
        return expr->father->children[1];
    }
    return expr->father->children[0];
}

Status StreamSqlGetChildrenExpr(SqlExprT *expr, const AASchemaT *schema, SqlExprT **leftChildren,
    SqlExprT **rightChildren, DmStreamVertexLabelT *vertexLabel);

Status StreamSqlVerifyWhereExpr(
    StreamItemOpTreeCtxT *itemCtx, SqlExprT *whereClause, DmStreamVertexLabelT *vertexLabel);
Status StreamSqlVerifyColumnExpr(const DbListT *tableList, SqlExprT *expr);

#ifdef __cplusplus
}
#endif

#endif
