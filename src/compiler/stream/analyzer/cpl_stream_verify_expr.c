/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: for verify expr in stream module
 * Author: yangchognhuan
 * Create: 2024-11-01
 */

#include "db_utils.h"
#include "cpl_ir_aa_schema.h"
#include "cpl_stream_analyzer_common.h"
#include "cpl_stream_build_op.h"
#include "cpl_stream_verify_expr.h"

#define STREAM_COLUMN_COUNT_ZERO 0
#define STREAM_COLUMN_COUNT_ONE 1
#define STREAM_WHERE_MAX_DEPTH 128
#define STREAM_MAX_IN_LIST_LEN 64

Status StreamSqlGetColumnCnt(const DbListT *tableList, SqlExprColumnT *colExpr, int *cnt)
{
    DB_POINTER2(tableList, colExpr);
    *cnt = STREAM_COLUMN_COUNT_ZERO;
    if ((DbStrCmp(STREAM_WINDOW_START_PROP_NAME, colExpr->columnName, true) == 0) ||
        (DbStrCmp(STREAM_WINDOW_END_PROP_NAME, colExpr->columnName, true) == 0)) {
        *cnt = STREAM_COLUMN_COUNT_ONE;
        return GMERR_OK;
    }

    Status ret = GMERR_OK;
    // 此处存在两种情况：1.tablelist中只有一张表。2.tablelist中有多张表但是由union连接到。
    // 两种情况都可以只使用list中的首张表校验。
    SqlSrcItemT *src = *(SqlSrcItemT **)DbListItem(tableList, 0);
    if (src->type != SRC_TABLE_REF && src->type != SRC_STREAM_WINDOW && src->type != SRC_STREAM_DISPATCH) {
        return GMERR_SEMANTIC_ERROR;
    }
    if (colExpr->tableName != NULL) {
        char *srcTableName = src->aliasName                 ? src->aliasName :
                             src->type == SRC_TABLE_REF     ? src->srcTable->tableName :
                             src->type == SRC_STREAM_WINDOW ? src->window->tableName :
                                                              src->dispatch->tableName;
        ;
        if (DbStrCmp(srcTableName, colExpr->tableName, true) != 0) {
            return ret;
        }
    }
    ret = StreamVerifyPropInfo(src, colExpr, cnt);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

static Status StreamCheckWhereType(SqlExprT *expr)
{
    SqlExprColumnT *colExpr = (SqlExprColumnT *)(void *)expr->op;
    if (colExpr->location.label != NULL && !colExpr->location.property->isValid) {
        // window_start和window_start不校验
        DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR, "stream where clause unsupported node type, name:%s",
            colExpr->location.property->name);
        return GMERR_SEMANTIC_ERROR;
    }
    return GMERR_OK;
}

static Status StreamSqlVerifyConstExpr(SqlExprT *expr)
{
    SqlExprColumnT *colExpr = (SqlExprColumnT *)(void *)expr->op;
    if (colExpr->arg.type == DB_DATATYPE_FIXED || colExpr->arg.type == DB_DATATYPE_STRING) {
        DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR, "Const is be used individually in where expr.");
        return GMERR_SEMANTIC_ERROR;
    }
    return GMERR_OK;
}

static Status StreamSqlVerifyColumnInSchema(const AASchemaT *schema, SqlExprT *expr, DbDataTypeE *type)
{
    SqlExprColumnT *columnExpr = (SqlExprColumnT *)expr->op;
    for (uint32_t i = 0; i < schema->propAA.propNum; i++) {
        char *srcColName = schema->propAA.properties[i]->name;
        if (DbStrCmp(srcColName, columnExpr->columnName, true) == 0) {
            *type = schema->propAA.properties[i]->dataType;
            return GMERR_OK;
        }
    }
    DB_LOG_AND_SET_LASERR(GMERR_UNDEFINE_COLUMN, "column is undefined");
    return GMERR_UNDEFINE_COLUMN;
}

Status StreamSqlVerifyColumnExpr(const DbListT *tableList, SqlExprT *expr)
{
    DB_POINTER2(tableList, expr);
    SqlExprColumnT *colExpr = (SqlExprColumnT *)(void *)expr->op;
    if (colExpr->dbName != NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR, "unsupported dbName");
        return GMERR_SEMANTIC_ERROR;
    }
    int cnt = STREAM_COLUMN_COUNT_ZERO;
    Status ret = StreamSqlGetColumnCnt(tableList, colExpr, &cnt);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR, "get column cnt");
        return ret;
    }
    if (cnt != STREAM_COLUMN_COUNT_ONE) {
        char *errorInfo = cnt == STREAM_COLUMN_COUNT_ZERO ?
                              "no such column, if you want to filter columns with some values, "
                              "please using '' to wrap the value" :
                              " multiple tables have the same field, which is not supported currently.";
        if (colExpr->dbName != NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR, "stream analyzer problem: %s: %s.%s.%s", errorInfo,
                colExpr->dbName, colExpr->tableName, colExpr->columnName);
        } else if (colExpr->tableName != NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR, "stream analyzer problem: %s: %s.%s", errorInfo,
                colExpr->tableName, colExpr->columnName);
        } else {
            DB_LOG_AND_SET_LASERR(
                GMERR_SEMANTIC_ERROR, "stream analyzer problem: %s: %s", errorInfo, colExpr->columnName);
        }
        return GMERR_UNDEFINE_COLUMN;
    }
    return StreamCheckWhereType(expr);
}

static bool StreamIsInt64ColumnExpr(const AASchemaT *schema, SqlExprT *expr)
{
    if (StreamIsArithMeticOp(expr->op->type)) {
        return StreamIsInt64ColumnExpr(schema, expr->children[0]) && StreamIsInt64ColumnExpr(schema, expr->children[1]);
    }
    if (expr->op->type == SQL_EXPR_ITEM_CONST) {
        return true;
    }
    if (expr->op->type != SQL_EXPR_ITEM_COLUMN) {
        return false;
    }
    SqlExprColumnT *columnExpr = (SqlExprColumnT *)expr->op;
    for (uint32_t i = 0; i < schema->propAA.propNum; i++) {
        char *srcColName = schema->propAA.properties[i]->name;
        if (DbStrCmp(srcColName, columnExpr->columnName, true) == 0) {
            return (schema->propAA.properties[i]->dataType == DB_DATATYPE_INT64);
        }
    }
    return false;
}

Status StreamSqlVerifyArithExpr(DbMemCtxT *memCtx, const AASchemaT *schema, SqlExprT *expr)
{
    // where a + b 场景
    if (expr->father == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR,
            "illegal arithmetic expression format, there should have one constant on the right "
            "of the arithmetic expression.");
        return GMERR_SEMANTIC_ERROR;
    }

    // 父节点左右子节点都要存在
    if (expr->father->children[0] == NULL || expr->father->children[1] == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR, "arith expr in stream should has brother expr.");
        return GMERR_SEMANTIC_ERROR;
    }

    // 算术表达式必须在左边, 且必须是变量
    if (!(expr == expr->father->children[0]) && StreamIsColumnExpr(expr->children[0]) &&
        StreamIsColumnExpr(expr->children[1])) {
        DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR,
            "illegal arithmetic expression format, all columns in the arithmetic expression can "
            "only appear on the left of the expression.");
        return GMERR_SEMANTIC_ERROR;
    }

    // 常量只能有一个，在表达式右边
    if (!StreamIsConstExpr(StreamGetBroExpr(expr))) {
        DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR,
            "illegal arithmetic expression format, there can be only one constant on the right "
            "of the arithmetic expression.");
        return GMERR_SEMANTIC_ERROR;
    }

    // 只支持int64类型
    if (!(StreamIsInt64ConstExpr(StreamGetBroExpr(expr)) && StreamIsInt64ColumnExpr(schema, expr->children[0]) &&
            StreamIsInt64ColumnExpr(schema, expr->children[1]))) {
        DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR, "arithmetic expression only support int64 type.");
        return GMERR_SEMANTIC_ERROR;
    }
    return GMERR_OK;
}

static Status StreamSqlVerifyLikeStringExpr(SqlExprLikeT *exprLike, SqlExprColumnT *column)
{
    // verify LIKE string
    if (exprLike->dst->op->type != SQL_EXPR_ITEM_CONST) {
        DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR, "wrong datatype");
        return GMERR_SEMANTIC_ERROR;
    }
    DmValueT likeValue = ((SqlExprConstT *)(void *)exprLike->dst->op)->arg;
    if (likeValue.type != DB_DATATYPE_FIXED && likeValue.type != DB_DATATYPE_STRING) {
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "wrong datatype");
        return GMERR_DATATYPE_MISMATCH;
    }
    if (!DbStrCmp(likeValue.value.strAddr, "", false)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "wrong datatype");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (likeValue.value.length - 1 >
        column->location.property->size * 2) {  // 2: like右值长度不超过定长字段长度的两倍,+1 用于空字符占位
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE,
            "Like expr can not exceed two times of field length, current like expr length: %" PRIu32 ".",
            likeValue.value.length - 1);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

static Status StreamSqlVerifyLikeExpr(SqlExprLikeT *exprLike, const DbListT *fromClause)
{
    DB_POINTER2(exprLike, fromClause);
    if (exprLike->src == NULL || exprLike->dst == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Empty child node of Stream like expr.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (SECUREC_UNLIKELY(exprLike->isNot)) {
        DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR, "Unsupported not like for Stream.");
        return GMERR_SEMANTIC_ERROR;
    }
    // verify LIKE column
    if (exprLike->src->op->type != SQL_EXPR_ITEM_COLUMN) {
        DB_LOG_AND_SET_LASERR(GMERR_SYNTAX_ERROR, "Stream like expr left value has to be column.");
        return GMERR_SYNTAX_ERROR;
    }
    // like两边不能都是变量
    if (exprLike->src->op->type == SQL_EXPR_ITEM_COLUMN && exprLike->dst->op->type == SQL_EXPR_ITEM_COLUMN) {
        DB_LOG_AND_SET_LASERR(GMERR_SYNTAX_ERROR, "column value for right value of like expr");
        return GMERR_SYNTAX_ERROR;
    }
    Status ret = StreamSqlVerifyColumnExpr(fromClause, exprLike->src);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 校验LIKE左值为定长字段
    SqlExprColumnT *column = (SqlExprColumnT *)(void *)exprLike->src->op;
    if (column->location.property->dataType != DB_DATATYPE_FIXED &&
        column->location.property->dataType != DB_DATATYPE_STRING) {
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "put a field that is not fixed or text type into a like expr.");
        return GMERR_DATATYPE_MISMATCH;
    }

    return StreamSqlVerifyLikeStringExpr(exprLike, column);
}

static bool StreamSqlIsCharType(DbDataTypeE type)
{
    return (type == DB_DATATYPE_FIXED || type == DB_DATATYPE_STRING || type == DB_DATATYPE_CHAR);
}

Status StreamSqlVerifyConstAndRef(SqlExprT *whereClause, DmStreamVertexLabelT *vertexLabel)
{
    Status ret = GMERR_OK;
    if ((SqlExprOpTypeE)whereClause->op->type == SQL_EXPR_ITEM_CONST && whereClause->father == NULL) {
        ret = StreamSqlVerifyConstExpr(whereClause);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if ((SqlExprOpTypeE)whereClause->op->type == SQL_EXPR_OP_FUN && whereClause->father == NULL) {
        SqlExprFuncT *exprFunc = (SqlExprFuncT *)(void *)whereClause->op;
        ret = StreamSqlRegisterRefName(exprFunc, vertexLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

Status StreamSqlVerifyWhereExpr(StreamItemOpTreeCtxT *itemCtx, SqlExprT *whereClause, DmStreamVertexLabelT *vertexLabel)
{
    Status ret = GMERR_OK;
    if (whereClause->op->type == SQL_EXPR_OP_AND || whereClause->op->type == SQL_EXPR_OP_OR) {
        itemCtx->whereExprDepth++;
    }
    if (itemCtx->whereExprDepth >= STREAM_WHERE_MAX_DEPTH) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "where expr depth exceeds the max limit: 128.");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }

    // verify like expr
    if (whereClause->op->type == SQL_EXPR_OP_LIKE) {
        SqlExprLikeT *exprLike = (SqlExprLikeT *)(void *)whereClause->op;
        ret = StreamSqlVerifyLikeExpr(exprLike, itemCtx->fromClause);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR, "verify like expr");
            return ret;
        }
    }

    // verify column expr
    if ((SqlExprOpTypeE)whereClause->op->type == SQL_EXPR_ITEM_COLUMN) {
        ret = StreamSqlVerifyColumnExpr(itemCtx->fromClause, whereClause);
        if (ret != GMERR_OK) {
            return ret;
        }
        DbDataTypeE type = DB_DATATYPE_NULL;
        ret = StreamSqlVerifyColumnInSchema(itemCtx->schema, whereClause, &type);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 不支持where 字符
        if (whereClause->father == NULL && StreamSqlIsCharType(type)) {
            DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR, "unsupport where + char or string type");
            return GMERR_SEMANTIC_ERROR;
        }
    }

    // verify const && func
    ret = StreamSqlVerifyConstAndRef(whereClause, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (StreamIsArithExprType(whereClause)) {
        // 校验算术表达式，只支持 a + b > 3格式
        return StreamSqlVerifyArithExpr(itemCtx->memCtx, itemCtx->schema, whereClause);
    }
    return ret;
}

static bool StreamCheckIsColumnConst(SqlExprT *expr)
{
    return (SqlExprOpTypeE)expr->op->type == SQL_EXPR_ITEM_CONST ||
           (SqlExprOpTypeE)expr->op->type == SQL_EXPR_ITEM_COLUMN;
}

static Status StreamCheckListTypeConstrains(const AASchemaT *schema, SqlExprT *leftChildren, SqlExprT *rightChildren)
{
    if ((SqlExprOpTypeE)leftChildren->op->type == SQL_EXPR_OP_FUN) {
        SqlExprFuncT *exprFunc = (SqlExprFuncT *)(void *)leftChildren->op;
        if (exprFunc->funcType != FUNC_TYPE_REF) {
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "only support ref func when eval in");
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
        return GMERR_OK;
    }
    if ((SqlExprOpTypeE)leftChildren->op->type != SQL_EXPR_ITEM_COLUMN) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "only support column when eval in");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    SqlExprColumnT *leftExpr = (SqlExprColumnT *)(void *)leftChildren->op;
    DbDataTypeE leftDataType = leftExpr->arg.type;
    bool isFound = false;
    for (uint32_t i = 0; i < schema->propAA.propNum; i++) {
        char *srcColName = schema->propAA.properties[i]->name;
        if ((DbStrCmp(srcColName, leftExpr->columnName, true) == 0)) {
            leftDataType = schema->propAA.properties[i]->dataType;
            isFound = true;
        }
    }

    if (!isFound) {
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINE_COLUMN, "undefined column in left list");
        return GMERR_UNDEFINE_COLUMN;
    }
    DbListT *exprList = ((SqlExprListT *)(void *)rightChildren->op)->exprList;
    uint32_t listSize = DbListGetItemCnt(exprList);
    if (listSize > STREAM_MAX_IN_LIST_LEN) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "number of values in list exceeds limit");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    for (uint32_t i = 0; i < listSize; i++) {
        SqlExprT *item = *(SqlExprT **)DbListItem(exprList, i);
        if (item->op->type != SQL_EXPR_ITEM_CONST) {
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "only support const in list");
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
        SqlExprConstT *exprConstT = (SqlExprConstT *)(void *)item->op;
        DbDataTypeE rightDataType = exprConstT->arg.type;
        if ((!StreamSqlIsCharType(leftDataType) || !StreamSqlIsCharType(rightDataType)) &&
            leftDataType != rightDataType) {
            DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "Data type is mismatch in list.");
            return GMERR_DATATYPE_MISMATCH;
        }
        (void)item;
    }
    return GMERR_OK;
}

static Status StreamExprTypeConsist(const AASchemaT *schema, SqlExprT *leftChildren, SqlExprT *rightChildren)
{
    if ((SqlExprOpTypeE)rightChildren->op->type == SQL_EXPR_ITEM_LIST) {
        return StreamCheckListTypeConstrains(schema, leftChildren, rightChildren);
    }
    if (!StreamCheckIsColumnConst(leftChildren) || !StreamCheckIsColumnConst(rightChildren)) {
        return GMERR_OK;
    }
    SqlExprColumnT *leftExpr = (SqlExprColumnT *)(void *)leftChildren->op;
    SqlExprColumnT *rightExpr = (SqlExprColumnT *)(void *)rightChildren->op;
    DbDataTypeE leftDataType = leftExpr->arg.type;
    DbDataTypeE rightDataType = rightExpr->arg.type;
    bool flagLeft = false;
    bool flagRight = false;
    for (uint32_t i = 0; i < schema->propAA.propNum; i++) {
        char *srcColName = schema->propAA.properties[i]->name;
        if ((SqlExprOpTypeE)leftChildren->op->type != SQL_EXPR_ITEM_CONST &&
            DbStrCmp(srcColName, leftExpr->columnName, true) == 0) {
            leftDataType = schema->propAA.properties[i]->dataType;
            flagLeft = true;
        }
        if ((SqlExprOpTypeE)rightChildren->op->type != SQL_EXPR_ITEM_CONST &&
            DbStrCmp(srcColName, rightExpr->columnName, true) == 0) {
            rightDataType = schema->propAA.properties[i]->dataType;
            flagRight = true;
        }
    }
    if ((SqlExprOpTypeE)leftChildren->op->type == SQL_EXPR_ITEM_COLUMN && !flagLeft) {
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINE_COLUMN, "");
        return GMERR_UNDEFINE_COLUMN;
    }
    if ((SqlExprOpTypeE)rightChildren->op->type == SQL_EXPR_ITEM_COLUMN && !flagRight) {
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINE_COLUMN, "");
        return GMERR_UNDEFINE_COLUMN;
    }
    if ((!StreamSqlIsCharType(leftDataType) || !StreamSqlIsCharType(rightDataType)) && leftDataType != rightDataType) {
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "datatype mismatch");
        return GMERR_DATATYPE_MISMATCH;
    }
    return GMERR_OK;
}

Status StreamSqlCheckWhereRef(SqlExprT *left, SqlExprT *right, DmStreamVertexLabelT *vertexLabel)
{
    Status ret = GMERR_OK;
    if (left != NULL && (SqlExprOpTypeE)left->op->type == SQL_EXPR_OP_FUN) {
        SqlExprFuncT *leftExprFunc = (SqlExprFuncT *)(void *)left->op;
        ret = StreamSqlRegisterRefName(leftExprFunc, vertexLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (right != NULL && (SqlExprOpTypeE)right->op->type == SQL_EXPR_OP_FUN) {
        SqlExprFuncT *rightExprFunc = (SqlExprFuncT *)(void *)right->op;
        ret = StreamSqlRegisterRefName(rightExprFunc, vertexLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

bool StreamIsBooleanExprType(SqlExprT *expr)
{
    if (expr == NULL) {
        return false;
    }
    return (SqlExprOpTypeE)expr->op->type == SQL_EXPR_OP_AND || (SqlExprOpTypeE)expr->op->type == SQL_EXPR_OP_OR ||
           (SqlExprOpTypeE)expr->op->type == SQL_EXPR_OP_IN || (SqlExprOpTypeE)expr->op->type == SQL_EXPR_OP_NOT_IN ||
           ((SqlExprOpTypeE)expr->op->type >= SQL_EXPR_BOOL_COM_OP_BEGIN &&
               (SqlExprOpTypeE)expr->op->type < SQL_EXPR_BOOL_COM_OP_END);
}

Status StreamSqlCheckAndGetChildren4BinaryExpr(SqlExprT *expr, const AASchemaT *schema, SqlExprT *left, SqlExprT *right)
{
    if (left == NULL || right == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_SYNTAX_ERROR, "Missing argument for binary expression.");
        return GMERR_SYNTAX_ERROR;
    }
    enum SqlExprOpType fatherOpType = (SqlExprOpTypeE)expr->op->type;
    enum SqlExprOpType leftOpType = (SqlExprOpTypeE)left->op->type;
    enum SqlExprOpType rightOpType = (SqlExprOpTypeE)right->op->type;

    // 该约束考虑去掉，可以和TS一起讨论
    if (leftOpType == SQL_EXPR_ITEM_CONST && rightOpType != SQL_EXPR_ITEM_CONST) {
        DB_LOG_AND_SET_LASERR(
            GMERR_FEATURE_NOT_SUPPORTED, "Const data type as left-side-value is unsupported in where.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    if ((fatherOpType != SQL_EXPR_OP_AND && fatherOpType != SQL_EXPR_OP_OR) &&
        (StreamIsBooleanExprType(left) || StreamIsBooleanExprType(right))) {
        DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR, "The boolean expressions can only be combined with AND or OR.");
        return GMERR_SEMANTIC_ERROR;
    }

    // 该约束考虑const和列名统一处理
    if ((fatherOpType == SQL_EXPR_OP_AND || fatherOpType == SQL_EXPR_OP_OR) &&
        (leftOpType == SQL_EXPR_ITEM_CONST || rightOpType == SQL_EXPR_ITEM_CONST)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_SEMANTIC_ERROR, "Binary logical ops AND and OR do not support const on either sides.");
        return GMERR_SEMANTIC_ERROR;
    }
    return StreamExprTypeConsist(schema, left, right);
}

Status StreamSqlGetChildrenExpr(SqlExprT *expr, const AASchemaT *schema, SqlExprT **leftChildren,
    SqlExprT **rightChildren, DmStreamVertexLabelT *vertexLabel)
{
    DB_POINTER3(expr, leftChildren, rightChildren);
    Status ret = GMERR_OK;
    SqlExprT *left = NULL;
    SqlExprT *right = NULL;
    if (StreamIsArithExprType(expr) || StreamIsBooleanExprType(expr)) {
        left = expr->children[0];
        right = expr->children[1];
        ret = StreamSqlCheckAndGetChildren4BinaryExpr(expr, schema, left, right);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    // 检验是否索引ref
    ret = StreamSqlCheckWhereRef(left, right, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    *leftChildren = left;
    *rightChildren = right;

    return GMERR_OK;
}
