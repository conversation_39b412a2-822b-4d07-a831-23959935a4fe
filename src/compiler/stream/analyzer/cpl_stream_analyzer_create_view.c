/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Create view for Stream analyzer
 * Author: Stream Team
 * Create: 2024-09-18
 */
#include "cpl_stream_analyzer_create_view.h"

#include "cpl_stream_analyzer_common.h"
#include "cpl_stream_analyzer_select.h"

static Status StreamSqlFillCreateViewOutput(DbMemCtxT *memCtx, DmStreamVertexLabelT *vertexLabel, SqlIrStmtT *irStmt)
{
    DB_POINTER3(memCtx, vertexLabel, irStmt);
    CreateStreamViewStmtT *node = (CreateStreamViewStmtT *)DbDynMemCtxAlloc(memCtx, sizeof(CreateStreamViewStmtT));
    if (node == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, " ");
        return GMERR_OUT_OF_MEMORY;
    }
    node->vertexLabel = vertexLabel;
    node->node.tag = T_CREATE_TABLE_STMT;
    irStmt->utilityStmt = (NodeT *)node;
    irStmt->irPlan = NULL;
    return GMERR_OK;
}

Status StreamCreateVertexLabelWhenAnalyzeCreateView(
    SessionT *session, NodeT *parsedStmt, SqlIrStmtT *irStmt, DmStreamVertexLabelT **vertexLabel)
{
    SqlCreateViewStmtT *viewCreateStmt = (SqlCreateViewStmtT *)parsedStmt;

    SqlVerifyCreateTableCtxT *ctx = StreamCreateVerifyCreateTableCtx(irStmt->memCtx);
    if (ctx == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    SqlCreateTableStmtT tempCreateStmt = {0};
    tempCreateStmt.isTemp = false;
    tempCreateStmt.ifNotExists = viewCreateStmt->ifNotExists;
    tempCreateStmt.name = viewCreateStmt->viewName;
    tempCreateStmt.columns = NULL;
    tempCreateStmt.constraints = NULL;
    tempCreateStmt.createWith = viewCreateStmt->createViewWith;
    tempCreateStmt.query = viewCreateStmt->value->selectSubquery;

    Status ret = StreamSqlVerifyDbAndTableName(session, &tempCreateStmt, ctx);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = StreamSqlVerifyConfigs(irStmt->memCtx, viewCreateStmt->createViewWith);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = StreamCreateEmptyVertexLabelWithMemCtx(irStmt->memCtx, vertexLabel, STREAM_LABEL_TYPE_VIEW);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = StreamSqlFillVertexLabelStmtCommon(*vertexLabel, &tempCreateStmt, ctx, irStmt->memCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbLinkedListInit(&(*vertexLabel)->streamMeta.viewMeta.next);
    DbLinkedListInit(&(*vertexLabel)->streamMeta.viewMeta.prev);

    ret = StreamAnalyzeProcessJson(*vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status StreamSqlAnalyzeCreateView(SessionT *session, NodeT *parsedStmt, SqlIrStmtT *irStmt)
{
    DmStreamVertexLabelT *vertexLabel = NULL;
    SqlCreateViewStmtT *createViewStmt = (SqlCreateViewStmtT *)parsedStmt;
    SqlSelectStmtT *select = (SqlSelectStmtT *)(void *)createViewStmt->value->selectSubquery;

    Status ret = StreamCreateVertexLabelWhenAnalyzeCreateView(session, parsedStmt, irStmt, &vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = StreamSqlCreateSubSelectPlan(session, parsedStmt, irStmt, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 创建view的prenode
    ret = CreateStreamLinkNodePreList(session, select, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (vertexLabel->depth > STREAM_DAG_VIEW_DEPTH_MAX) {
        DB_LOG_AND_SET_LASERR(
            GMERR_PROGRAM_LIMIT_EXCEEDED, "Maximum depth of stream view exceeded: %u", STREAM_DAG_VIEW_DEPTH_MAX);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    // 分发规则
    ret = StreamSqlFillDispatch(createViewStmt->dispatchbyList, irStmt->memCtx, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 填充输出
    ret = StreamSqlFillCreateViewOutput(irStmt->memCtx, vertexLabel, irStmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}
