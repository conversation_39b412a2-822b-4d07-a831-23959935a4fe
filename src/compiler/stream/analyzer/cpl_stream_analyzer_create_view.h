/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: the interface for STREAM create view analyzer
 * Author: Stream Team
 * Create: 2024-09-18
 */

#ifndef CPL_STREAM_ANALYZER_CREATE_VIEW_H
#define CPL_STREAM_ANALYZER_CREATE_VIEW_H

#include "ee_session_interface.h"
#include "ee_plan_node_ddl_type.h"
#include "cpl_public_sql_parser_common.h"

Status StreamSqlAnalyzeCreateView(SessionT *session, NodeT *parsedStmt, SqlIrStmtT *irStmt);

#endif /* CPL_STREAM_ANALYZER_CREATE_VIEW_H */
