/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Implementation for printing IR Logical Expr
 * Author: zhuangyifeng
 * Create: 2023-08-01
 */

#include "cpl_ir_logical_op.h"
#include "cpl_ir_explain.h"
#include "cpl_ir_explain_common.h"
#include "ee_expression_dump.h"

// ================ 算子内部属性的打印函数 ================

static void PrintOpProp4Join(ExplainCtxT *explainCtx, const IRExprT *expr, StringBuilderT *sb)
{
    // [ExplainFormat] 打印 AASchema
    IRPrintAASchema(IRGetOpAASchemaConst(expr->op), sb);
    OpLogicalAAJoinT *join = (OpLogicalAAJoinT *)(void *)expr->op;
    // [ExplainFormat] 打印 joinType
    PrintPropIndent(explainCtx, expr->op, sb);
    DmSbAppendWithFormat(sb, "joinType: %s", GetJoinTypeName(join->joinType));
    // [ExplainFormat] 打印 joinKey
    PrintPropIndent(explainCtx, expr->op, sb);
    PrintJoinKeys(join->joinKeyNum, join->leftKeyIds, join->rightKeyIds, sb);
}

static void PrintOpProp4Mxm(ExplainCtxT *explainCtx, const IRExprT *expr, StringBuilderT *sb)
{
    OpLogicalAAMxmT *mxm = (OpLogicalAAMxmT *)(void *)expr->op;
    // [ExplainFormat] 打印 AASchema
    IRPrintAASchema(IRGetOpAASchemaConst(expr->op), sb);
    // [ExplainFormat] 打印 aaOpAdd 和 aaOpMulti 的信息
    DmSbAppendWithFormat(
        sb, " aaOpAdd = %s, aaOpMulti = %s", GetAAOpTypeName(mxm->aaOpAdd), GetAAOpTypeName(mxm->aaOpMulti));
}

static void PrintOpProp4Scan(ExplainCtxT *explainCtx, const IRExprT *expr, StringBuilderT *sb)
{
    OpLogicalAAScanT *scan = (OpLogicalAAScanT *)(void *)expr->op;
    // [ExplainFormat] 打印 on <vertexLabelName>
    DmSbAppendWithFormat(sb, FORMAT_ON_VERTEX_LABEL_NAME, scan->vertexLabel->metaCommon.metaName);
    if (scan->edgeLabel != NULL) {
        // [ExplainFormat] 打印 by <edgeLabelName>
        DmSbAppendWithFormat(sb, FORMAT_BY_EDGE_LABEL_NAME, scan->edgeLabel->metaCommon.metaName);
    }
    DmSbAppendWithFormat(sb, " pathId %" PRIu32 "", scan->pathId);
    // [ExplainFormat] 打印 AASchema
    IRPrintAASchema(IRGetOpAASchema(expr->op), sb);
}

void PrintLogicalOpProps(ExplainCtxT *explainCtx, const IRExprT *expr, StringBuilderT *sb)
{
    // 维护打印函数的函数表
    PrintOpPropFuncT printLogicalOpPropFunc[(uint32_t)IR_LOGOP_END - (uint32_t)IR_LOGOP_BEGIN] = {
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_EXTRACTTUPLES)] = NULL,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_TRANSPOSE)] = NULL,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_MXM)] = PrintOpProp4Mxm,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_EWISEADD)] = NULL,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_EWISEMULT)] = NULL,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_APPLY)] = NULL,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_EXTRACT)] = PrintOpProp4Project,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_EXTEND_EXTRACT)] = PrintOpProp4ExtendProject,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_ASSIGN)] = NULL,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_SELECT)] = NULL,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_SET_FIELD)] = NULL,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_KRONECKERPRODUCT)] = NULL,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_JOIN)] = PrintOpProp4Join,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_SCAN)] = PrintOpProp4Scan,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_SCANWORKLABEL)] = PrintOpProp4Scan,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_REPLACE)] = PrintOpProp4Dml,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_MERGE)] = PrintOpProp4Dml,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_MODIFYWORKLABEL)] = PrintOpProp4ModifyWorkLabel,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_ARGUMENT)] = PrintOpProp4Argument,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_AGG)] = NULL,
#ifdef FEATURE_SQL
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_UPDATE)] = PrintOpProp4Cud,
#endif
#if defined(FEATURE_SQL) || defined(FEATURE_GQL)
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_INDEXFILTERSCAN)] = PrintOpProp4Scan,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_DELETE)] = PrintOpProp4Cud,
#endif
#if defined(FEATURE_TS) || defined(FEATURE_SQL)
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_EXTEND_ORDER_BY)] = PrintOpProp4ExtendOrderBy,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_EXPR_PROJECT)] = PrintOpProp4ExprProject,
#endif
#ifdef FEATURE_SQL
#ifndef IDS_HAOTIAN
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_UNION)] = PrintOpProp4Union,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_SUB_QUERY_SCAN)] = PrintOpProp4SubQueryScan,
#endif
#endif
#if defined(FEATURE_TS) || defined(FEATURE_SQL)
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_ORDER_BY)] = PrintOpProp4OrderBy,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_LIMIT)] = PrintOpProp4Limit,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_INSERT)] = PrintOpProp4Cud,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_BUILD)] = NULL,
#endif
#ifdef FEATURE_TS
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_GROUP_BY)] = PrintOpProp4GroupBy,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_BULK_INSERT)] = NULL,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_COPY_TO)] = PrintOpProp4CopyTo,
#endif
#ifdef FEATURE_GQL
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_PATHTRAVERSAL)] = PrintOpProp4PathTraversal,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_FILTERPATHTRAVERSAL)] = PrintOpProp4PathTraversal,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_TOPOSCAN)] = PrintOpProp4TopoScan,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_PATHSEQFILTER)] = PrintOpProp4PathSeqFilter,
#endif  // FEATURE_GQL
#ifdef FEATURE_STREAM
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_UPSERT_INTO_REF)] = NULL,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_STREAM_WINDOW_TABLE)] = PrintOpProp4WindowTable,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_STREAM_WATERMARK_ASSIGNER)] = PrintOpProp4WatermarkAssigner,
        [INDEX_OF_LOGICAL_OP_TYPE(IR_LOGOP_STREAM_OVER_AGG)] = PrintOpProp4OverAgg,
#endif
    };
    if (printLogicalOpPropFunc[INDEX_OF_LOGICAL_OP_TYPE(expr->op->type)] != NULL) {
        printLogicalOpPropFunc[INDEX_OF_LOGICAL_OP_TYPE(expr->op->type)](explainCtx, expr, sb);
    }
}
