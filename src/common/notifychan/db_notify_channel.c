/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: db_notify_channel.c
 * Description: source file for notify channel
 * Author: chen<PERSON><PERSON>han
 * Create: 2023-2-13
 */
#include "db_notify_channel.h"

Status DbNotifyMsgQueueInit(DbMemCtxT *shmemCtx, DbNotifyMsgQueueT *msgQue)
{
    DB_POINTER2(shmemCtx, msgQue);
    DbRWLatchInit(&msgQue->lock);
    msgQue->capacity = NOTIFY_MSG_QUEUE_INIT_SIZE;
    uint32_t allocSize = NotifyMsgQueueOffset(msgQue->capacity, (uint32_t)NOTIFY_PRIORITY_BUTT);
    msgQue->queueBuf = DbShmemCtxAlloc(shmemCtx, allocSize);
    if (!DbIsShmPtrValid(msgQue->queueBuf)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "alloc notify chan shmem worthless! alloc size %" PRIu32, allocSize);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    for (uint32_t i = 0; i < (uint32_t)NOTIFY_PRIORITY_BUTT; i++) {
        msgQue->queue[i].count = 0;
        msgQue->queue[i].queue.segId = msgQue->queueBuf.segId;
        msgQue->queue[i].queue.offset = msgQue->queueBuf.offset + NotifyMsgQueueOffset(msgQue->capacity, i);
    }
    return GMERR_OK;
}

static Status DbNotifyMsgQueueExpand(DbMemCtxT *shmemCtx, DbNotifyMsgQueueT *msgQue, uint32_t newCap)
{
    DB_POINTER2(shmemCtx, msgQue);
    DB_ASSERT(newCap <= NOTIFY_MSG_QUEUE_MAX_SIZE);

    DbNotifyDataT *oldQueBuf = DbShmPtrToAddr(msgQue->queueBuf);
    if (oldQueBuf == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "notify msg que buf ptr unsound!");
        return GMERR_INTERNAL_ERROR;
    }

    uint32_t allocSize = NotifyMsgQueueOffset(newCap, (uint32_t)NOTIFY_PRIORITY_BUTT);
    ShmemPtrT newQueBufPtr = DbShmemCtxAlloc(shmemCtx, allocSize);
    DbNotifyDataT *newQueBuf = DbShmPtrToAddr(newQueBufPtr);
    if (newQueBuf == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "notify chan double size worthless! alloc size %" PRIu32, allocSize);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    for (uint32_t i = 0; i < (uint32_t)NOTIFY_PRIORITY_BUTT; i++) {
        msgQue->queue[i].queue.segId = newQueBufPtr.segId;
        msgQue->queue[i].queue.offset = newQueBufPtr.offset + NotifyMsgQueueOffset(newCap, i);
        if (msgQue->queue[i].count == 0) {
            continue;
        }
        uint32_t cpySize = (uint32_t)(msgQue->queue[i].count * sizeof(DbNotifyDataT));
        errno_t err = memcpy_s(newQueBuf + (uint64_t)newCap * i, allocSize - (uint64_t)newCap * i,
            oldQueBuf + (uint64_t)msgQue->capacity * i, cpySize);
        if (err != EOK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
                "notify msg que expand worthless! no:%" PRId32 ", newCap:%" PRIu32 ", oldCap:%" PRIu32 ", i:%" PRIu32
                "",
                (int32_t)err, newCap, msgQue->capacity, i);
            DbShmemCtxFree(shmemCtx, newQueBufPtr);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }
    DbShmemCtxFree(shmemCtx, msgQue->queueBuf);
    msgQue->queueBuf = newQueBufPtr;
    msgQue->capacity = newCap;
    return GMERR_OK;
}

Status DbNotifyMsgQueueWrite(DbNotifyMsgQueueT *msgQue, const DbNotifyDataT *data, DbNotifyPriorityE prior)
{
    DB_POINTER2(msgQue, data);
    DbRWLatchW(&msgQue->lock);
    DbNotifyQueueT *nfQue = &msgQue->queue[(uint32_t)prior];
    DbNotifyDataT *queue = DbShmPtrToAddr(nfQue->queue);
    if (queue == NULL) {
        DbRWUnlatchW(&msgQue->lock);
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "notify msg que ptr unsound!");
        return GMERR_INTERNAL_ERROR;
    }
    // 相同data不重复通知
    for (uint32_t i = 0; i < nfQue->count; i++) {
        if (queue[i].data == data->data) {
            DbRWUnlatchW(&msgQue->lock);
            return GMERR_OK;
        }
    }
    if (nfQue->count >= msgQue->capacity) {
        DbRWUnlatchW(&msgQue->lock);
        DB_LOG_ERROR(GMERR_PROGRAM_LIMIT_EXCEEDED, "notify queue write, count: %" PRIu32 ", capacity: %" PRIu32 "",
            nfQue->count, msgQue->capacity);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    queue[nfQue->count] = *data;
    nfQue->count++;
    DbRWUnlatchW(&msgQue->lock);
    return GMERR_OK;
}

Status DbNotifyMsgQueueRemove(DbNotifyChannelT *chan, DbNotifyDataT *data, DbNotifyPriorityE prior)
{
    DB_POINTER2(chan, data);
    DbNotifyMsgQueueT *msgQue = DbShmPtrToAddr(chan->msgQue);
    if (msgQue == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "notify msg que ptr unsound!");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    DbRWLatchW(&msgQue->lock);
    DbNotifyQueueT *nfQue = &msgQue->queue[(uint32_t)prior];
    DbNotifyDataT *queue = DbShmPtrToAddr(nfQue->queue);
    if (SECUREC_UNLIKELY(queue == NULL)) {
        DbRWUnlatchW(&msgQue->lock);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (nfQue->count == 0) {  // 检查确保nfQue->count - 1 >= 0;
        DbRWUnlatchW(&msgQue->lock);
        return GMERR_OK;
    }
    uint32_t findIdx = nfQue->count;
    for (uint32_t i = 0; i < nfQue->count; ++i) {
        if (queue[i].data == data->data) {
            findIdx = i;
            break;
        }
    }
    if (SECUREC_UNLIKELY(findIdx < nfQue->count)) {
        for (uint32_t i = findIdx; i < nfQue->count - 1; ++i) {
            queue[i].data = queue[i + 1].data;
        }
        --nfQue->count;
    }
    DbRWUnlatchW(&msgQue->lock);
    return GMERR_OK;
}

uint32_t DbNotifyMsgQueueMsgSize(DbNotifyMsgQueueT *msgQue)
{
    DB_POINTER(msgQue);
    uint32_t size = 0;
    for (uint32_t i = 0; i < (uint32_t)NOTIFY_PRIORITY_BUTT; i++) {
        size += msgQue->queue[i].count;
    }
    return size;
}

Status DbNotifyMsgQueueRead(DbNotifyMsgQueueT *msgQue, DbNotifyMsgBufferT *notifyMsgBuffer)
{
    DB_POINTER2(msgQue, notifyMsgBuffer);
    DbNotifyDataT *queue = DbShmPtrToAddr(msgQue->queueBuf);
    if (queue == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "notify msg que buf ptr unsound!");
        return GMERR_INTERNAL_ERROR;
    }

    notifyMsgBuffer->readPos = 0;
    uint32_t offset = 0;
    for (uint32_t i = 0; i < (uint32_t)NOTIFY_PRIORITY_BUTT; i++) {
        notifyMsgBuffer->priorPos[i] = offset;
        if (msgQue->queue[i].count == 0) {
            continue;
        }
        errno_t err =
            memcpy_s(notifyMsgBuffer->dataBuf + offset, notifyMsgBuffer->size * sizeof(DbNotifyDataT) - offset,
                queue + (uint64_t)msgQue->capacity * i, msgQue->queue[i].count * sizeof(DbNotifyDataT));
        if (err != EOK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
                "notify msg que read worthless! no:%" PRId32 ", i:%" PRIu32 ", cap:%" PRIu32, (int32_t)err, i,
                msgQue->capacity);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        offset += msgQue->queue[i].count;
        msgQue->queue[i].count = 0;
    }
    return GMERR_OK;
}

static inline uint32_t NotifyChanGetSuspendThreshold(void)
{
    uint32_t hungTimeUs = 0u;
    if (DbGetHungTime(WORKER_HUNG_LEVEL_THREE, &hungTimeUs) != GMERR_OK) {
        return DEFAULT_CHAN_SUSPEND_THRESHOLD;
    }
    return hungTimeUs / USECONDS_IN_MSECOND;
}

Status DbNotifyChannelCreate(DbMemCtxT *shmemCtx, uint32_t id, DbNotifyChannelT *chan)
{
    DB_POINTER2(shmemCtx, chan);
    DB_ASSERT(shmemCtx->memType == DB_SHARED_MEMORY);
    chan->shmemCtx = shmemCtx;
    Status ret = DbNotifyFdCreate(id, &chan->nfd);
    if (ret != GMERR_OK) {
        return ret;
    }
    // memCtx用途：订阅channel通道
    // 生命周期：连接级别
    // 释放方式：DbNotifyChannelRelease时释放
    // 兜底清空措施：断连时，销毁该memctx
    ShmemPtrT msgQuePtr = DbShmemCtxAlloc(shmemCtx, sizeof(DbNotifyMsgQueueT));
    DbNotifyMsgQueueT *msgQue = DbShmPtrToAddr(msgQuePtr);
    if (msgQue == NULL) {
        (void)DbNotifyFdClose(chan->nfd);
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc notify chan msg que worthless!");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    ret = DbNotifyMsgQueueInit(shmemCtx, msgQue);
    if (ret != GMERR_OK) {
        DbShmemCtxFree(shmemCtx, msgQuePtr);
        (void)DbNotifyFdClose(chan->nfd);
        return ret;
    }

    ShmemPtrT ctrlInfoPtr = DbShmemCtxAlloc(shmemCtx, sizeof(DbNotifyChanCtrlInfoT));
    DbNotifyChanCtrlInfoT *ctrlInfo = DbShmPtrToAddr(ctrlInfoPtr);
    if (ctrlInfo == NULL) {
        (void)DbNotifyFdClose(chan->nfd);
        DbShmemCtxFree(chan->shmemCtx, msgQue->queueBuf);
        DbShmemCtxFree(chan->shmemCtx, chan->msgQue);
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc chan ctrl info worthless");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    ctrlInfo->selfPtr = ctrlInfoPtr;
    ctrlInfo->isSuspend = false;
    ctrlInfo->suspendThreshold = NotifyChanGetSuspendThreshold();
    ctrlInfo->suspendTimeStamp = 0u;  // default greenwich 1970.01.01
    ctrlInfo->lastLogTimeStamp = 0u;

    chan->msgQue = msgQuePtr;
    chan->ctrlInfo = ctrlInfo;
    return GMERR_OK;
}

Status DbNotifyChannelRelease(DbNotifyChannelT *chan)
{
    DB_POINTER(chan);
    DbNotifyMsgQueueT *msgQue = DbShmPtrToAddr(chan->msgQue);
    if (msgQue == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "notify msg que ptr unsound!");
        return GMERR_INTERNAL_ERROR;
    }
    DbShmemCtxFree(chan->shmemCtx, msgQue->queueBuf);
    DbShmemCtxFree(chan->shmemCtx, chan->msgQue);
    DbShmemCtxFree(chan->shmemCtx, chan->ctrlInfo->selfPtr);
    Status ret = DbNotifyFdClose(chan->nfd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Release notify chan fd worthless!");
    }
    DbNotifyChannelClear(chan);
    return ret;
}

Status DbNotifyChannelCheckAndExpandCapacity(DbNotifyChannelT *chan, uint32_t neededSize)
{
    DB_POINTER(chan);
    DbNotifyMsgQueueT *msgQue = DbShmPtrToAddr(chan->msgQue);
    if (msgQue == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "notify msg que ptr unsound!");
        return GMERR_INTERNAL_ERROR;
    }
    DbRWLatchW(&msgQue->lock);
    if (neededSize <= msgQue->capacity) {
        DbRWUnlatchW(&msgQue->lock);
        return GMERR_OK;
    }
    uint32_t newCap = msgQue->capacity;
    while (newCap < neededSize) {
        newCap = newCap * NF_CHAN_DOUBLE_SIZE;
    }
    if (newCap > NOTIFY_MSG_QUEUE_MAX_SIZE) {
        DbRWUnlatchW(&msgQue->lock);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    Status ret = DbNotifyMsgQueueExpand(chan->shmemCtx, msgQue, newCap);
    DbRWUnlatchW(&msgQue->lock);
    return ret;
}

Status DbNotifyChannelSend(const DbNotifyChannelT *chan, DbNotifyDataT *data, DbNotifyPriorityE prior)
{
    DB_POINTER2(chan, data);
    DbNotifyMsgQueueT *msgQue = DbShmPtrToAddr(chan->msgQue);
    if (msgQue == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "notify msg que ptr unsound!");
        return GMERR_INTERNAL_ERROR;
    }
    Status ret = DbNotifyMsgQueueWrite(msgQue, data, prior);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (chan->ctrlInfo->isSuspend) {
        return GMERR_OK;
    }
    return DbNotifyFdWrite(chan->nfd);
}

Status DbNotifyChannelRecvOpen(DbMemCtxT *memCtx, DbNotifyMsgQueueT *msgQueue, DbNotifyMsgBufferT **notifyMsgBuffer)
{
    DB_POINTER3(memCtx, msgQueue, notifyMsgBuffer);
    if (msgQueue == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "notify msg que ptr unsound!");
        return GMERR_INTERNAL_ERROR;
    }
    DbRWLatchW(&msgQueue->lock);
    uint32_t dataSize = DbNotifyMsgQueueMsgSize(msgQueue);
    DbNotifyMsgBufferT *msgBuf =
        DbDynMemCtxAlloc(memCtx, sizeof(DbNotifyMsgBufferT) + dataSize * sizeof(DbNotifyDataT));
    if (msgBuf == NULL) {
        DbRWUnlatchW(&msgQueue->lock);
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "alloc notify msg buffer worthless!");
        return GMERR_INTERNAL_ERROR;
    }
    msgBuf->memCtx = memCtx;
    msgBuf->size = dataSize;
    Status ret = DbNotifyMsgQueueRead(msgQueue, msgBuf);
    DbRWUnlatchW(&msgQueue->lock);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(memCtx, msgBuf);
    }
    *notifyMsgBuffer = msgBuf;
    return ret;
}

void DbNotifyChannelRecvClose(DbNotifyMsgBufferT *notifyMsgBuffer)
{
    DB_POINTER(notifyMsgBuffer);
    DbDynMemCtxFree(notifyMsgBuffer->memCtx, notifyMsgBuffer);
}

Status DbNotifyChannelFetch(DbNotifyMsgBufferT *notifyMsgBuffer, DbNotifyDataT *data)
{
    DB_POINTER2(notifyMsgBuffer, data);
    if (notifyMsgBuffer->readPos >= notifyMsgBuffer->size) {
        return GMERR_NO_DATA;
    }
    *data = notifyMsgBuffer->dataBuf[notifyMsgBuffer->readPos];
    notifyMsgBuffer->readPos++;
    return GMERR_OK;
}
