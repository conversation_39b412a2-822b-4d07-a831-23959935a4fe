/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: mini_simulate_shmem.c
 * Description: source file for dynamic memory simulate share memory
 * Author: sudetong
 * Create: 2023-12-15
 */

#include "mini_simulate_shmem.h"

static const DbShmemCtxMethodsT g_gmdbSimulateShmemAlgoMethods = {SimulateShmemAlgoAlloc, SimulateShmemAlgoFree,
    SimulateShmemAlgoReset, SimulateShmemAlgoClear, SimulateShmemAlgoDestroy, SimulateShmemAlgoDestroyForce,
    SimulateShmemIsEmpty, NULL, NULL, NULL, NULL, SimulateMemCtxShmPtrToAddr};

const DbShmemCtxMethodsT *GetSimulateShmemAlgoMethods(void)
{
    return &g_gmdbSimulateShmemAlgoMethods;
}

void DbMiniShmemAlgoClear(DbSimulateShmemAlgoT *simulateShmemAlgo)
{
    for (uint32_t slotid = 0; slotid < simulateShmemAlgo->capacity; slotid++) {
        void *simulateShmemAddr = simulateShmemAlgo->simulateShmemArray[slotid].simulateShmemAddr;
        if (simulateShmemAddr != NULL) {
            DB_FREE(simulateShmemAddr);
            simulateShmemAlgo->simulateShmemArray[slotid].simulateShmemAddr = NULL;
        }
    }
    DB_FREE(simulateShmemAlgo->simulateShmemArray);
}

static Status SimulateShmemAlgoInitOneInstance(uint32_t instanceId)
{
    DbTopShmemCtxT *topShmemCtx = GetMiniTopShmemCtxAddr(instanceId);
    if (SECUREC_UNLIKELY(topShmemCtx == NULL)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE,
            "Unable to init shmemAlgo for instance: %" PRIu32 " , topShmemCtx is not valid.", instanceId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    DbSimulateShmemAlgoT *simulateShmemAlgo = (DbSimulateShmemAlgoT *)DB_MALLOC(sizeof(DbSimulateShmemAlgoT));
    if (SECUREC_UNLIKELY(simulateShmemAlgo == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
            "Unable to allocate memory during initialization of simulate share memory algo,"
            " size is: %zu.",
            sizeof(DbSimulateShmemAlgoT));
        return GMERR_OUT_OF_MEMORY;
    }

    uint32_t simulateShmemArrayAllocSize = DB_DEFAULT_SIMULATE_SHMEM_ARRAY_SIZE * sizeof(DbSimulateShmemMgrT);
    DbSimulateShmemMgrT *simulateShmemArray = (DbSimulateShmemMgrT *)DB_MALLOC(simulateShmemArrayAllocSize);
    if (SECUREC_UNLIKELY(simulateShmemArray == NULL)) {
        DB_FREE(simulateShmemAlgo);
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
            "Unable to allocate memory during initialization of simulate share memory algo,"
            " size is: %zu.",
            sizeof(DbSimulateShmemAlgoT));
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(simulateShmemArray, simulateShmemArrayAllocSize, 0x00, simulateShmemArrayAllocSize);
    simulateShmemAlgo->simulateShmemArray = simulateShmemArray;
    simulateShmemAlgo->instanceId = instanceId;
    simulateShmemAlgo->highWaterMark = 0;
    simulateShmemAlgo->firstFreeSlot = INVALID_SLOT_ID;
    simulateShmemAlgo->size = 0;
    simulateShmemAlgo->capacity = DB_DEFAULT_SIMULATE_SHMEM_ARRAY_SIZE;

    topShmemCtx->shmemAlgo = simulateShmemAlgo;
    return GMERR_OK;
}

static Status SimulateShmemAlgoExtendOneInstance(DbSimulateShmemAlgoT *simulateShmemAlgo)
{
    DbSimulateShmemMgrT *oldSimulateShmemArray = simulateShmemAlgo->simulateShmemArray;
    if (SECUREC_UNLIKELY(oldSimulateShmemArray == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Simulate shmem array null, instanceId : %" PRIu32 ".",
            simulateShmemAlgo->instanceId);
        return GMERR_INTERNAL_ERROR;
    }

    uint32_t curCapacity = simulateShmemAlgo->capacity;
    uint32_t extendCapacity = curCapacity << 1;

    uint32_t oldArraySize = curCapacity * sizeof(DbSimulateShmemMgrT);
    uint32_t extendArraySize = oldArraySize << 1;
    DbSimulateShmemMgrT *extendSimulateShmemArray = (DbSimulateShmemMgrT *)DB_MALLOC(extendArraySize);
    if (SECUREC_UNLIKELY(extendSimulateShmemArray == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
            "Unable to allocate memory during extend simulate share memory algo,"
            " cur capacity is: %" PRIu32 ".",
            curCapacity);
        return GMERR_OUT_OF_MEMORY;
    }

    (void)memmove_s(extendSimulateShmemArray, extendArraySize, oldSimulateShmemArray, oldArraySize);
    (void)memset_s(extendSimulateShmemArray + curCapacity, oldArraySize, 0x00, oldArraySize);
    DB_FREE(oldSimulateShmemArray);

    simulateShmemAlgo->capacity = extendCapacity;
    simulateShmemAlgo->highWaterMark = curCapacity;
    simulateShmemAlgo->simulateShmemArray = extendSimulateShmemArray;
    return GMERR_OK;
}

static Status SimulateShmemArrayFindFreeSlot(DbSimulateShmemAlgoT *simulateShmemAlgo, uint32_t *outFreeSlotId)
{
    DbSimulateShmemMgrT *simulateShmemArray = simulateShmemAlgo->simulateShmemArray;
    if (SECUREC_UNLIKELY(simulateShmemArray == NULL)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Simulate shmem array null, instanceId : %" PRIu32 ".",
            simulateShmemAlgo->instanceId);
        return GMERR_INTERNAL_ERROR;
    }

    uint32_t firstFreeSlotId = simulateShmemAlgo->firstFreeSlot;
    if (firstFreeSlotId != INVALID_SLOT_ID) {
        *outFreeSlotId = firstFreeSlotId;
        uint32_t nextFreeSlotId = simulateShmemArray[firstFreeSlotId].nextFreeSlot;
        simulateShmemAlgo->firstFreeSlot = nextFreeSlotId;
        return GMERR_OK;
    }

    if (simulateShmemAlgo->highWaterMark < simulateShmemAlgo->capacity) {
        goto RETURN;
    }

    Status ret = SimulateShmemAlgoExtendOneInstance(simulateShmemAlgo);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(
            ret, "Unable to extend shmem algo for one instance, instance: %" PRIu32 ".", simulateShmemAlgo->instanceId);
        return ret;
    }
RETURN:
    *outFreeSlotId = simulateShmemAlgo->highWaterMark;
    simulateShmemAlgo->highWaterMark++;
    return GMERR_OK;
}

bool IsShmemPtrExist(ShmemPtrT shmPtr)
{
    uint32_t instanceId = shmPtr.segId;
    DbTopShmemCtxT *topShmemCtx = GetMiniTopShmemCtxAddr(instanceId);
    if (SECUREC_UNLIKELY(topShmemCtx == NULL)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE,
            "TopShmemCtx is not valid when checking existence of ShmemPtr, instanceId: %" PRIu32 ".", instanceId);
        return false;
    }
    DbSimulateShmemAlgoT *simulateShmemAlgo = topShmemCtx->shmemAlgo;
    if (simulateShmemAlgo == NULL) {
        return false;
    }
    uint32_t simulateShmemID = shmPtr.offset;
    uint32_t curSimulateShmemArraySize = simulateShmemAlgo->size;
    DbSimulateShmemMgrT *SimulateShmemArray = simulateShmemAlgo->simulateShmemArray;
    if (simulateShmemID < curSimulateShmemArraySize && SimulateShmemArray[simulateShmemID].simulateShmemAddr != NULL) {
        return true;
    }
    return false;
}

ShmemPtrT SimulateShmemAlgoAlloc(DbMemCtxT *curCtx, size_t usrAllocSize)
{
    DB_POINTER(curCtx);
    Status ret;
    uint32_t instanceId = curCtx->instanceId;
    DbTopShmemCtxT *topShmemCtx = GetMiniTopShmemCtxAddr(instanceId);
    if (SECUREC_UNLIKELY(topShmemCtx == NULL)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "TopShmemCtx is not valid when allocating, instanceId: %" PRIu32 ".",
            instanceId);
        return DB_INVALID_SHMPTR;
    }

    DbSimulateShmemAlgoT *simulateShmemAlgo = topShmemCtx->shmemAlgo;
    if (simulateShmemAlgo == NULL) {
        ret = SimulateShmemAlgoInitOneInstance(instanceId);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Unable to init simulate shmem algo, instance:%" PRIu32 ".", instanceId);
            goto EXIT;
        }
        simulateShmemAlgo = topShmemCtx->shmemAlgo;
    }

    uint32_t freeSlotId;
    ret = SimulateShmemArrayFindFreeSlot(simulateShmemAlgo, &freeSlotId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Unable to find free slot for one instance, instance: %" PRIu32 ".", instanceId);
        goto EXIT;
    }

    void *simulateShmemAddr = DB_MALLOC((uint32_t)usrAllocSize);
    if (SECUREC_UNLIKELY(simulateShmemAddr == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
            "Unable to allocate memory during simulate shmem algo alloc,"
            " size is: %" PRIu32 ".",
            (uint32_t)usrAllocSize);
        goto EXIT;
    }
    simulateShmemAlgo->simulateShmemArray[freeSlotId].simulateShmemAddr = simulateShmemAddr;
    simulateShmemAlgo->simulateShmemArray[freeSlotId].simulateShmemSize = usrAllocSize;
    simulateShmemAlgo->size++;

    ShmemPtrT newShmemPtr = {.segId = instanceId, .offset = freeSlotId};
    return newShmemPtr;
EXIT:
    return DB_INVALID_SHMPTR;
}

Status SimulateShmemAlgoFree(DbMemCtxT *curCtx, ShmemPtrT ptr)
{
    DB_UNUSED(curCtx);
    uint32_t instanceId = ptr.segId;
    DbTopShmemCtxT *topShmemCtx = GetMiniTopShmemCtxAddr(instanceId);
    if (SECUREC_UNLIKELY(topShmemCtx == NULL)) {
        DB_LOG_ERROR(
            GMERR_UNEXPECTED_NULL_VALUE, "TopShmemCtx is not valid when freeing, instanceId: %" PRIu32 ".", instanceId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    DbSimulateShmemAlgoT *simulateShmemAlgo = topShmemCtx->shmemAlgo;
    if (SECUREC_UNLIKELY(simulateShmemAlgo == NULL)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Unable to free simulate shmem, instance:%" PRIu32 ".", instanceId);
        goto EXIT;
    }

    uint32_t simulateShmemSlotId = ptr.offset;
    uint32_t curCapacity = simulateShmemAlgo->capacity;
    if (SECUREC_UNLIKELY(simulateShmemSlotId >= curCapacity)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Simulate shmem slotid not valid: %" PRIu32 ", cur capacity: %" PRIu32 ".",
            simulateShmemSlotId, curCapacity);
        goto EXIT;
    }

    void *simulateShmemAddr = simulateShmemAlgo->simulateShmemArray[simulateShmemSlotId].simulateShmemAddr;
    if (SECUREC_UNLIKELY(simulateShmemAddr == NULL)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Unable to free simulate shmem addr, pointer is null.");
        goto EXIT;
    }

    simulateShmemAlgo->simulateShmemArray[simulateShmemSlotId].simulateShmemAddr = NULL;
    simulateShmemAlgo->simulateShmemArray[simulateShmemSlotId].nextFreeSlot = simulateShmemAlgo->firstFreeSlot;
    simulateShmemAlgo->firstFreeSlot = simulateShmemSlotId;

    simulateShmemAlgo->size--;

    DB_FREE(simulateShmemAddr);

    return GMERR_OK;
EXIT:
    return GMERR_INTERNAL_ERROR;
}

void SimulateShmemAlgoReset(DbMemCtxT *curCtx)
{
    DB_UNUSED(curCtx);
    return;
}

Status SimulateShmemAlgoClear(DbMemCtxT *curCtx)
{
    DB_POINTER(curCtx);
    DbTopShmemCtxT *topShmemCtx = (DbTopShmemCtxT *)(void *)curCtx;
    if (SECUREC_UNLIKELY(topShmemCtx == NULL)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "TopShmemCtx is not valid when clearing.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbSimulateShmemAlgoT *simulateShmemAlgo = topShmemCtx->shmemAlgo;
    if (simulateShmemAlgo != NULL) {
        DbMiniShmemAlgoClear(simulateShmemAlgo);
        topShmemCtx->shmemAlgo = NULL;
        DB_FREE(simulateShmemAlgo);
    }
    return GMERR_OK;
}

Status SimulateShmemAlgoDestroy(DbMemCtxT *curCtx)
{
    return SimulateShmemAlgoClear(curCtx);
}

void SimulateShmemAlgoDestroyForce(DbMemCtxT *curCtx)
{
    DB_UNUSED(curCtx);
    return;
}

Status SimulateShmemIsEmpty(DbMemCtxT *curCtx, bool *isEmpty)
{
    DB_UNUSED(curCtx);
    DB_UNUSED(isEmpty);
    return GMERR_OK;
}

void *SimulateMemCtxShmPtrToAddr(const DbMemCtxT *curCtx, ShmemPtrT shmPtr)
{
    if (SECUREC_UNLIKELY(curCtx == NULL)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "TopShmemCtx is not valid when converting ShmPtr to addr");
        DB_ASSERT(false);
        return NULL;
    }
    const DbTopShmemCtxT *topShmemCtx = (const DbTopShmemCtxT *)curCtx;
    DbSimulateShmemAlgoT *simulateShmemAlgo = topShmemCtx->shmemAlgo;
    if (SECUREC_UNLIKELY(simulateShmemAlgo == NULL)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Unable to get simulate shmem algo");
        goto EXIT;
    }

    uint32_t simulateShmemSlotId = shmPtr.offset;
    uint32_t simulateShmemArrayCapacity = simulateShmemAlgo->capacity;
    if (SECUREC_UNLIKELY(simulateShmemSlotId >= simulateShmemArrayCapacity)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Simulate shmem slotid not valid: %" PRIu32 ", array capacity: %" PRIu32 ".",
            simulateShmemSlotId, simulateShmemArrayCapacity);
        goto EXIT;
    }

    void *simulateShmemAddr = simulateShmemAlgo->simulateShmemArray[simulateShmemSlotId].simulateShmemAddr;
    return simulateShmemAddr;

EXIT:
    return NULL;
}

void *SimulateShmPtrToAddr(ShmemPtrT shmPtr)
{
    uint32_t instanceId = shmPtr.segId;
    DbTopShmemCtxT *topShmemCtx = GetMiniTopShmemCtxAddr(instanceId);
    return SimulateMemCtxShmPtrToAddr((DbMemCtxT *)topShmemCtx, shmPtr);
}

uint32_t SimulateShmemGetUserSize(const DbMemCtxT *ctx, ShmemPtrT shmPtr)
{
    DB_UNUSED(ctx);
    uint32_t instanceId = shmPtr.segId;
    const DbTopShmemCtxT *topShmemCtx = GetMiniTopShmemCtxAddr(instanceId);
    if (SECUREC_UNLIKELY(topShmemCtx == NULL)) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE,
            "TopShmemCtx is not valid when getting user size, instanceId: %" PRIu32 ".", instanceId);
        return 0;
    }
    DbSimulateShmemAlgoT *simulateShmemAlgo = topShmemCtx->shmemAlgo;
    if (simulateShmemAlgo == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Unable to get simulate shmem algo, instance:%" PRIu32 ".", instanceId);
        return 0;
    }

    uint32_t simulateShmemSlotId = shmPtr.offset;
    uint32_t simulateShmemAllocSize = simulateShmemAlgo->simulateShmemArray[simulateShmemSlotId].simulateShmemSize;

    return simulateShmemAllocSize;
}
