/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: mini_simulate_shmem.h
 * Description: head file for dynamic memory simulate share memory
 * Author: sudetong
 * Create: 2023-12-15
 */

#ifndef MINI_SIMULATE_SHARE_MEMORY_H
#define MINI_SIMULATE_SHARE_MEMORY_H

#include "db_mini_top_shmem_ctx.h"
#include "db_mini_mem_context_internal.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct DbSimulateShmemMgr {
    uint32_t simulateShmemSize;
    uint32_t nextFreeSlot;
    Handle simulateShmemAddr;
} DbSimulateShmemMgrT;

typedef struct DbSimulateShmemAlgo {
    uint32_t instanceId;
    uint32_t reserve;
    uint32_t capacity;
    uint32_t size;
    uint32_t highWaterMark;
    uint32_t firstFreeSlot;
    DbSimulateShmemMgrT *simulateShmemArray;
} DbSimulateShmemAlgoT;

// 实例对应模拟共享内存块默认16块
#define DB_DEFAULT_SIMULATE_SHMEM_ARRAY_SIZE 16
#define INVALID_SLOT_ID 0xFFFFFFFF

const DbShmemCtxMethodsT *GetSimulateShmemAlgoMethods(void);
ShmemPtrT SimulateShmemAlgoAlloc(DbMemCtxT *curCtx, size_t size);
Status SimulateShmemAlgoFree(DbMemCtxT *curCtx, ShmemPtrT ptr);
uint32_t SimulateShmemGetUserSize(const DbMemCtxT *ctx, ShmemPtrT ptr);
void SimulateShmemAlgoReset(DbMemCtxT *curCtx);
Status SimulateShmemAlgoClear(DbMemCtxT *curCtx);
Status SimulateShmemAlgoDestroy(DbMemCtxT *curCtx);
void SimulateShmemAlgoDestroyForce(DbMemCtxT *curCtx);
Status SimulateShmemIsEmpty(DbMemCtxT *curCtx, bool *isEmpty);
uint64_t ShmemBlockPhySizePeak(DbMemCtxT *curCtx);
void ShmemBlockResetPhySizePeak(DbMemCtxT *curCtx);
uint64_t ShmemBlockGetAndResetPeak(DbMemCtxT *curCtx);
void *SimulateShmPtrToAddr(ShmemPtrT shmPtr);
void *SimulateMemCtxShmPtrToAddr(const DbMemCtxT *curCtx, ShmemPtrT shmPtr);

#ifdef __cplusplus
}
#endif

#endif  // MINI_SIMULATE_SHARE_MEMORY_H
