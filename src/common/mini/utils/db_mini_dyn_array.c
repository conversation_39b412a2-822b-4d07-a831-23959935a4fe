/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 动态内存数组
 * Author: minikv
 * Create: 2024-01-20
 * Notes:
 */

#include "db_dyn_array.h"

typedef struct DbDynArrTupleHdr {
    uint32_t next;
    bool used;
    uint8_t reserve[3];  // 预留3字节对齐
} DbDynArrTupleHdrT;

Status DbDynArrayInit(DbDynArrayT *array, DbMemCtxT *memCtx, uint32_t itemSize, uint32_t initSize, uint32_t maxSize)
{
    DB_POINTER2(array, memCtx);
    if (initSize > maxSize || initSize == 0) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    if (maxSize > DB_MAX_DYN_ARRAY_SIZE) {
        return GMERR_INSUFFICIENT_RESOURCES;
    }
    uint32_t allocSize = (uint32_t)sizeof(void *);
    // 内存释放点:DbDynArrayDestroy
    void **items = DbDynMemCtxAlloc(memCtx, allocSize);
    if (items == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(items, allocSize, 0x00, allocSize);
    uint32_t actualSize = (uint32_t)(sizeof(DbDynArrTupleHdrT) + itemSize);
    allocSize = actualSize * initSize;
    // 内存释放点:DbDynArrayDestroy
    items[0] = DbDynMemCtxAlloc(memCtx, allocSize);
    if (items[0] == NULL) {
        DbDynMemCtxFree(memCtx, items);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(items[0], allocSize, 0x00, allocSize);
    array->maxNum = maxSize;
    array->allocNum = initSize;
    array->useNum = 0;
    array->curPos = 0;
    array->freeIdx = DB_INVALID_ID32;
    array->itemSize = actualSize;
    array->caps = initSize;
    array->extCount = 1;
    array->extUsed = 1;
    array->exts = items;
    array->memCtx = memCtx;
    return GMERR_OK;
}

void DbDynArrayDestroy(DbDynArrayT *array)
{
    DB_POINTER(array);
    if (array->memCtx == NULL || array->exts == NULL) {
        return;
    }
    for (uint32_t i = 0; i < array->extCount; i++) {
        DbDynMemCtxFree(array->memCtx, array->exts[i]);
        array->exts[i] = NULL;
    }
    DbDynMemCtxFree(array->memCtx, array->exts);
    array->memCtx = NULL;
    array->exts = NULL;
}

static Status DbDynArrayResizeExt(DbDynArrayT *array)
{
    DB_POINTER(array);
    uint32_t newExtCnt = array->extCount << 1;
    if (newExtCnt > DB_MAX_DYN_ARRAY_EXT_SIZE) {
        return GMERR_INSUFFICIENT_RESOURCES;
    }

    uint32_t newSize = (uint32_t)sizeof(void *) * newExtCnt;
    // 内存释放点:DbDynArrayDestroy
    void **newExts = DbDynMemCtxAlloc(array->memCtx, newSize);
    if (newExts == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(newExts, newSize, 0, newSize);

    uint32_t oldSize = (uint32_t)sizeof(void *) * array->extCount;
    int32_t ret = memcpy_s(newExts, newSize, array->exts, oldSize);
    if (ret != EOK) {
        DbDynMemCtxFree(array->memCtx, newExts);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    DbDynMemCtxFree(array->memCtx, array->exts);
    array->extCount = newExtCnt;
    array->exts = newExts;
    return GMERR_OK;
}

static Status DbDynArrayAllocNewExt(DbDynArrayT *array)
{
    uint32_t deltaCaps = DB_MIN(array->caps, array->maxNum - array->allocNum);
    uint32_t allocSize = array->itemSize * deltaCaps;

    // 内存释放点:DbDynArrayDestroy
    void *newExt = DbDynMemCtxAlloc(array->memCtx, allocSize);
    if (newExt == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(newExt, allocSize, 0x00, allocSize);
    array->exts[array->extUsed++] = newExt;
    array->allocNum += deltaCaps;
    return GMERR_OK;
}

static Status DbDynArrayResize(DbDynArrayT *array)
{
    if (array->extUsed == array->extCount) {
        Status ret = DbDynArrayResizeExt(array);
        if (ret != GMERR_OK || array->extUsed == array->extCount) {
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }
    return DbDynArrayAllocNewExt(array);
}

static DbDynArrTupleHdrT *DbDynArrayGetItemAddrById(const DbDynArrayT *array, uint32_t itemId)
{
    DB_POINTER(array);
    if (itemId >= array->allocNum) {
        return NULL;
    }

    if (array->caps == 0) {
        return NULL;
    }
    uint32_t extId = itemId / array->caps;
    if (extId >= array->extCount) {
        return NULL;
    }

    if (array->exts[extId] == NULL) {
        return NULL;
    }

    uint32_t offset = (itemId % array->caps) * array->itemSize;
    return array->exts[extId] + offset;
}

void *DbDynArrayAllocItem(DbDynArrayT *array, uint32_t *itemId)
{
    DB_POINTER3(array, array->exts, itemId);
    if (array->freeIdx != DB_INVALID_ID32) {
        DbDynArrTupleHdrT *hdr = DbDynArrayGetItemAddrById(array, array->freeIdx);
        if (hdr == NULL) {
            return NULL;
        }
        *itemId = array->freeIdx;
        hdr->used = true;
        array->freeIdx = hdr->next;
        array->useNum++;
        return (uint8_t *)hdr + sizeof(DbDynArrTupleHdrT);
    }
    if (array->useNum >= array->maxNum) {
        return NULL;
    }
    if (array->curPos >= array->allocNum) {
        Status ret = DbDynArrayResize(array);
        if (ret != GMERR_OK) {
            return NULL;
        }
    }
    DbDynArrTupleHdrT *hdr = DbDynArrayGetItemAddrById(array, array->curPos);
    if (hdr == NULL) {
        return NULL;
    }

    hdr->used = true;
    *itemId = array->curPos++;
    array->useNum++;
    return (uint8_t *)hdr + sizeof(DbDynArrTupleHdrT);
}
