/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2021. All rights reserved.
 * Description: gmserver's config file.
 * Author: xu yingyu
 * Create: 2020-08-18
 */

#include <securec.h>
#include "db_utils.h"
#include "adpt_process_id.h"
#include "adpt_string.h"
#include "db_log_fold.h"
#include "db_log_ctrl.h"
#include "db_sysapp_context.h"
#include "db_config_define.h"
#include "db_mem_context.h"
#include "db_instance.h"
#include "db_config.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/* ****************************************************************************
Description  : Validates a value for a CI whose data type is int with a value
                range.
Input        : cfgItemDesc: the description of CI.
Input        : value: the value to validates.
Output       : None
Return Value : return GMERR_OK when success.
History      : None
1.Author       : guoyuzhou
    Modification : Create function
**************************************************************************** */
static char *CfgRetrim(char *input)
{
    DB_POINTER(input);
    char *textRetrim = input;

    while (*textRetrim == ' ') {
        textRetrim++;
    }

    return textRetrim;
}

Status CfgParamValidateCheckTrxTypeAndIsoLevel(const DbCfgItemDescT *cfgItemDesc, const DbCfgValueT *value)
{
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    DbCfgMgrT *mgr = (DbCfgMgrT *)cfgHandle;
    // 第一次用默认值配置直接返回
    if (mgr->cfgItemNum == 0) {
        return CfgParamValidateIrange(cfgItemDesc, value);
    }
    DbCfgValueT trxType;
    Status ret = GMERR_OK;
    ret = DbCfgGet(DbGetCfgHandle(NULL), DB_CFG_DEFAULT_TRANSACTION_TYPE, &trxType);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_CONFIG_ERROR, "Get config transaction type.");
        return ret;
    }

    ret = CfgCheckTrxTypeAndIsolationLevel(trxType.int32Val, value->int32Val);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_CONFIG_ERROR,
            "isolation level and transaction type not support, trxType=%" PRIi32 ", isolationLevel=%" PRIi32 ".",
            trxType.int32Val, value->int32Val);
        return ret;
    }

    return CfgParamValidateIrange(cfgItemDesc, value);
}

Status CfgThresholdGetValue(char *value, uint32_t *valueGet, uint32_t valueGetLen)
{
    DB_POINTER2(value, valueGet);

    char *nextContext = NULL;
    uint32_t i = 0;

    // value:cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;
    // shareMemory:70,80,80,85,85,90;
    // subscribeQueue:70,80,80,85,85,90
    char *pCurrent = CfgRetrim(value);
    pCurrent = strtok_s(pCurrent, ":", &nextContext);
    if (pCurrent == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "|COM| pCurrent is null");
        return GMERR_DATA_EXCEPTION;
    }

    if (i >= valueGetLen) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATA_EXCEPTION, "Get config, Index exceeds the limit, index=%" PRIu32 ".", i);
        return GMERR_DATA_EXCEPTION;
    }

    if (DbStrNCmp(pCurrent, "cpu", sizeof("cpu"), false) == 0) {
        // cpu type
        valueGet[i++] = RESOURCE_TYPE_CPU;
    } else if (DbStrNCmp(pCurrent, "dynamicMemory", sizeof("dynamicMemory"), false) == 0) {
        // memory type
        valueGet[i++] = RESOURCE_TYPE_DYN_MEMORY;
    } else if (DbStrNCmp(pCurrent, "shareMemory", sizeof("shareMemory"), false) == 0) {
        // memory type
        valueGet[i++] = RESOURCE_TYPE_SHM_MEMORY;
    } else if (DbStrNCmp(pCurrent, "subscribeQueue", sizeof("subscribeQueue"), false) == 0) {
        // subscribe queue type
        valueGet[i++] = RESOURCE_TYPE_SUBS_QUEUE;
    } else {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Get config, Unexpected value type");
        return GMERR_DATA_EXCEPTION;
    }

    while (pCurrent != NULL) {
        pCurrent = strtok_s(NULL, ",", &nextContext);
        if (pCurrent == NULL) {
            break;
        }
        if (!CfgNumberCheck(pCurrent)) {
            return GMERR_DATA_EXCEPTION;
        }

        if (i >= valueGetLen) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_DATA_EXCEPTION, "Get config, index exceeds the limit, index=%" PRIu32 ".", i);
            return GMERR_DATA_EXCEPTION;
        }

        Status ret = DbStrToUint32(pCurrent, &valueGet[i++]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status CfgThresholdValue(char *value, uint32_t *array, uint32_t arrayLen)
{
    DB_POINTER(value);

    uint32_t valueGet[THRESHOLD_VALUE_NUM_MAX] = {0};
    Status ret = CfgThresholdGetValue(value, valueGet, THRESHOLD_VALUE_NUM_MAX);
    if (ret != GMERR_OK) {
        return ret;
    }

    for (uint32_t j = 1; j < (THRESHOLD_VALUE_NUM_MAX - 1); ++j) {
        if (valueGet[j] > valueGet[j + 1]) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Set cfg's threshold:sequence check");
            return GMERR_DATA_EXCEPTION;
        }
    }
    if (valueGet[THRESHOLD_VALUE_NUM_MAX - 1] > DB_PERCENTAGE_BASE) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Set cfg's threshold:full load");
        return GMERR_DATA_EXCEPTION;
    }

    if (array != NULL && arrayLen == THRESHOLD_VALUE_NUM_MAX) {
        ret = memcpy_s(array, sizeof(valueGet), valueGet, sizeof(valueGet));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Set cfg's threshold:copy unanticipated");
            return GMERR_DATA_EXCEPTION;
        }
    }

    return GMERR_OK;
}

Status CfgParamValidateLoop(const DbCfgValueT *value, uint32_t len, int32_t *splitNum, int32_t *seperateNum)
{
    int32_t splitNumInner = *splitNum;
    int32_t seperateNumInner = *seperateNum;
    if (len > DB_CFG_PARAM_MAX_STRING) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "cfg str len unsound");
        return GMERR_DATA_EXCEPTION;
    }
    for (uint32_t i = 0u; i < len; i++) {
        if (!CfgThresholdCharCheck(value->str[i])) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "cfg thresh char unsound");
            return GMERR_DATA_EXCEPTION;
        }
        if (value->str[i] == ':') {
            splitNumInner++;
        }
        if (value->str[i] == ',') {
            seperateNumInner++;
        }
    }
    *splitNum = splitNumInner;
    *seperateNum = seperateNumInner;
    return GMERR_OK;
}

Status CfgParamFlowCtrl(const DbCfgItemDescT *cfgItemDesc, const DbCfgValueT *value)
{
    DB_POINTER2(cfgItemDesc, value);
    if (value->type != cfgItemDesc->type || value->type != DB_DATATYPE_STRING) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_CONFIG_ERROR, "Config flow control, type=%" PRIi32 ".", (int32_t)value->type);
        return GMERR_CONFIG_ERROR;
    }

    // 校验数值
    char paraStr[DB_CFG_PARAM_MAX_STRING] = {0};
    errno_t ret = strncpy_s(paraStr, DB_CFG_PARAM_MAX_STRING, value->str, strlen(value->str));
    if (ret != EOK) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "copy cfg's string: '%s'", value->str);
        return GMERR_CONFIG_ERROR;
    }

    if (strcmp(paraStr, "0") == 0 || strcmp(paraStr, "1") == 0) {
        // 兼容原来的值，即输入0/1也可以起服务
        return GMERR_OK;
    }
    uint32_t count = 0;  // 校验配置项个数
    char *strNext = NULL;
    char *str = strtok_s(paraStr, ";", &strNext);
    while (str != NULL) {
        if (!(strcmp(str, "0") == 0) && !(strcmp(str, "1") == 0)) {
            DB_LOG_ERROR(GMERR_CONFIG_ERROR, "flowCtrl value: %s", str);
            return GMERR_CONFIG_ERROR;
        }
        count++;
        str = strtok_s(NULL, ";", &strNext);
    }

    if (count != FLOW_CONTROL_CONFIG_NUM) {  // 当前仅允许输入1个或4个值
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "flowCtrl value num, num %" PRIu32 ".", count);
        return GMERR_CONFIG_ERROR;
    }

    return GMERR_OK;
}

Status CfgParamValidateThreshold(const DbCfgItemDescT *cfgItemDesc, const DbCfgValueT *value)
{
    DB_POINTER2(cfgItemDesc, value);
    if (value->type != cfgItemDesc->type || value->type != DB_DATATYPE_STRING) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Config Threshold, type=%" PRIi32 ".", (int32_t)value->type);
        return GMERR_DATA_EXCEPTION;
    }

    uint32_t len = (uint32_t)strlen(value->str);
    if (len == 0u) {
        return GMERR_OK;
    }
    int32_t splitNum = 0;
    int32_t seperateNum = 0;
    char paraStr[DB_CFG_PARAM_MAX_STRING] = {0};

    errno_t ret = strncpy_s(paraStr, DB_CFG_PARAM_MAX_STRING, value->str, len);
    if (ret != EOK) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "copy cfg's string: '%s'", value->str);
        return GMERR_DATA_EXCEPTION;
    }

    Status ret1 = CfgParamValidateLoop(value, len, &splitNum, &seperateNum);
    if (ret1 != GMERR_OK) {
        return ret1;
    }

    if (splitNum > (int32_t)RESOURCE_TYPE_MAX || seperateNum > ((int32_t)RESOURCE_TYPE_MAX * THRESHOLD_SEPERATE_NUM)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
            "cfg parametres, spiltNum: %" PRId32 ", seperateNum: %" PRId32, splitNum, seperateNum);
        return GMERR_DATA_EXCEPTION;
    }

    char *nextContext = NULL;
    char *pCurrent = paraStr;
    for (int32_t j = 0; j < splitNum; j++) {
        pCurrent = strtok_s(pCurrent, ";", &nextContext);
        if (pCurrent == NULL) {
            break;
        }
        Status result = CfgThresholdValue(pCurrent, NULL, 0u);
        if (result != GMERR_OK) {
            return result;
        }

        pCurrent = nextContext;
    }

    return GMERR_OK;
}

Status CfgParamThresholdSplitString(
    const DbCfgItemDescT *cfgItemDesc, const DbCfgValueT *value, char *valueString, uint32_t strLen, const char *info)
{
    DB_POINTER2(valueString, info);
    if (value->type != cfgItemDesc->type || value->type != DB_DATATYPE_STRING) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Setting %s config string.", info);
        return GMERR_DATA_EXCEPTION;
    }

    uint32_t len = (uint32_t)strlen(value->str);
    if (len == 0u) {
        return GMERR_OK;
    }

    for (uint32_t i = 0; i < len; ++i) {
        bool isValid = ((value->str[i] >= '0' && value->str[i] <= '9') || value->str[i] == ' ' || value->str[i] == ',');
        if (!isValid) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Setting %s config character.", info);
            return GMERR_DATA_EXCEPTION;
        }
    }

    errno_t err = strcpy_s(valueString, strLen, value->str);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "copy %s config string.", info);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status CfgParamThresholdGetValue(
    char *valueString, uint32_t strLen, uint32_t *value, uint32_t valueNum, const char *info)
{
    DB_POINTER3(valueString, value, info);
    char *nextContext = NULL;

    if (strLen > DB_CFG_PARAM_MAX_STRING) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "%s strlen is unsound.", info);
        return GMERR_DATA_EXCEPTION;
    }
    char *pCurrent = CfgRetrim(valueString);
    uint32_t i = 0;

    while (pCurrent != NULL) {
        pCurrent = strtok_s(pCurrent, ",", &nextContext);
        if (pCurrent == NULL) {
            break;
        }
        if (!CfgNumberCheck(pCurrent)) {
            return GMERR_DATA_EXCEPTION;
        }
        if (i >= valueNum) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "%s's len is unsound.", info);
            return GMERR_DATA_EXCEPTION;
        }
        Status ret = DbStrToUint32(pCurrent, &value[i++]);
        if (ret != GMERR_OK) {
            return ret;
        }
        pCurrent = nextContext;
    }
    if (i != valueNum) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "%s's number is unsound.", info);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status CfgParamThresholdCheckValue(
    const DbCfgItemDescT *cfgItemDesc, const uint32_t *value, uint32_t valueNum, const char *info)
{
    DB_POINTER(info);
    for (uint32_t i = 0; i < (valueNum - 1); ++i) {
        if ((value[i] < cfgItemDesc->min) || (value[i] > cfgItemDesc->max) || (value[i] >= value[i + 1])) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "The value of %s is unsound.", info);
            return GMERR_DATA_EXCEPTION;
        }
    }
    if ((value[valueNum - 1] < cfgItemDesc->min) || (value[valueNum - 1] > cfgItemDesc->max)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "The value of %s is unsound.", info);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

bool CfgCheckFlowCtrlValue(const uint32_t *value, uint32_t valueNum)
{
    for (uint32_t i = 0; i < valueNum; i++) {
        if (value[i] != 0) {
            return false;
        }
    }
    return true;
}

Status CfgParamFlowCtrlTimeCheckValue(
    const DbCfgItemDescT *cfgItemDesc, const uint32_t *value, uint32_t valueNum, const char *info)
{
    DB_POINTER(info);
    if (CfgCheckFlowCtrlValue(value, valueNum)) {  // 允许输入0,0,0；其他数值需要递增
        return GMERR_OK;
    }

    for (uint32_t i = 0; i < (valueNum - 1); ++i) {
        if ((value[i] < cfgItemDesc->min) || (value[i] > cfgItemDesc->max) || (value[i] > value[i + 1])) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "The value of %s is unsound.", info);
            return GMERR_DATA_EXCEPTION;
        }
    }

    if ((value[valueNum - 1] < cfgItemDesc->min) || (value[valueNum - 1] > cfgItemDesc->max)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "The value of %s is unsound.", info);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status CfgParamFlowCtrlSleepTime(const DbCfgItemDescT *cfgItemDesc, const DbCfgValueT *value)
{
    DB_POINTER2(cfgItemDesc, value);

    char paraStr[DB_CFG_PARAM_MAX_STRING] = {0};
    // 校验格式
    Status ret = CfgParamThresholdSplitString(
        cfgItemDesc, value, paraStr, DB_CFG_PARAM_MAX_STRING, FLOW_CONTROL_TIME_INTERVAL_INFO);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 获取数值
    uint32_t valueGet[FLOW_CONTROL_TIME_INTERVAL_NUM] = {0};
    ret = CfgParamThresholdGetValue(
        paraStr, DB_CFG_PARAM_MAX_STRING, valueGet, FLOW_CONTROL_TIME_INTERVAL_NUM, FLOW_CONTROL_TIME_INTERVAL_INFO);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 校验数值
    return CfgParamFlowCtrlTimeCheckValue(
        cfgItemDesc, valueGet, FLOW_CONTROL_TIME_INTERVAL_NUM, FLOW_CONTROL_TIME_INTERVAL_INFO);
}

Status CfgParamMemtraceCtxIdsGetValue(char *valueString, uint32_t strLen, uint32_t *value, uint32_t valueNum)
{
    DB_POINTER2(valueString, value);
    char *nextContext = NULL;

    if (strLen > DB_CFG_PARAM_MAX_STRING) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Memtrace ctxIds strlen is unsound.");
        return GMERR_DATA_EXCEPTION;
    }
    char *pCurrent = CfgRetrim(valueString);
    if (pCurrent != NULL && (*pCurrent > '9' || *pCurrent < '0')) {
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t i = 0;

    while (pCurrent != NULL) {
        pCurrent = strtok_s(pCurrent, ",", &nextContext);
        if (pCurrent == NULL) {
            break;
        }
        if (!CfgNumberCheck(pCurrent)) {
            return GMERR_DATA_EXCEPTION;
        }
        if (i >= valueNum) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Memtrace ctxIds elemCnt is unsound.");
            return GMERR_DATA_EXCEPTION;
        }
        Status ret = DbStrToUint32(pCurrent, &value[i++]);
        if (ret != GMERR_OK) {
            return ret;
        }
        pCurrent = nextContext;
    }
    if ((i - 1) < value[0]) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Memtrace ctxIds total cnt is unsound.");
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status CfgParamMemtraceCtxIdsCheckValue(const DbCfgItemDescT *cfgItemDesc, const uint32_t *value, uint32_t valueNum)
{
    DB_POINTER2(cfgItemDesc, value);
    // 第一个数代表配置memtrace的内存上下文数量。
    if (value[0] > MEMTRACE_CTX_ID_NUM || value[0] == DB_INVALID_UINT32) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Memtrace num max value exceeded.");
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t ctxIdCnt = 0;
    for (uint32_t i = 1; i <= valueNum; i++) {
        // 校验ctxId取值范围。
        if (i <= value[0] && value[i] >= cfgItemDesc->max) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Memtrace ctxIds max value exceeded.");
            return GMERR_DATA_EXCEPTION;
        }
        if (value[i] != DB_INVALID_UINT32) {
            ctxIdCnt++;
        }
    }
    // 校验ctxId设置数量。
    if (ctxIdCnt != value[0]) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Memtrace ctxIds num mismatch.");
        return GMERR_DATA_EXCEPTION;
    }
    // 检验重复项
    for (uint32_t i = 1; i <= value[0]; i++) {
        for (uint32_t j = i + 1; j <= value[0]; j++) {
            if (value[i] == value[j]) {
                DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Memtrace dup ctxId.");
                return GMERR_DATA_EXCEPTION;
            }
        }
    }
    return GMERR_OK;
}

Status CfgParamMemtraceCtxIds(const DbCfgItemDescT *cfgItemDesc, const DbCfgValueT *value)
{
    DB_POINTER2(cfgItemDesc, value);

    char paraStr[DB_CFG_PARAM_MAX_STRING] = {0};
    // 校验格式
    Status ret = CfgParamThresholdSplitString(cfgItemDesc, value, paraStr, DB_CFG_PARAM_MAX_STRING, MEMTRACE_CTX_IDS);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 获取数值
    uint32_t valueGet[MEMTRACE_CTX_ID_NUM + 1] = {0};
    (void)memset_s(
        valueGet, (MEMTRACE_CTX_ID_NUM + 1) * sizeof(uint32_t), 0xFF, (MEMTRACE_CTX_ID_NUM + 1) * sizeof(uint32_t));
    ret = CfgParamMemtraceCtxIdsGetValue(paraStr, DB_CFG_PARAM_MAX_STRING, valueGet, (MEMTRACE_CTX_ID_NUM + 1));
    if (ret != GMERR_OK) {
        return ret;
    }
    // 校验数值
    return CfgParamMemtraceCtxIdsCheckValue(cfgItemDesc, valueGet, MEMTRACE_CTX_ID_NUM);
}

Status CfgParamRsmRangeCheckValue(
    const DbCfgItemDescT *cfgItemDesc, const uint32_t *value, uint32_t valueNum, const char *info)
{
    DB_POINTER(info);
    if (value[0] != 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "The first num of %s must be zero.", info);
        return GMERR_DATA_EXCEPTION;
    }

    for (uint32_t i = 0; i < (valueNum - 1); ++i) {
        if (value[i] >= value[i + 1]) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "The value of %s is unsound.", info);
            return GMERR_DATA_EXCEPTION;
        }
        if ((value[i + 1] - value[i]) >= RSM_MAX_BLOCK_NUM) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "The value of %s is out of limit.", info);
            return GMERR_DATA_EXCEPTION;
        }
    }

    return GMERR_OK;
}

Status CfgParamRsmRange(const DbCfgItemDescT *cfgItemDesc, const DbCfgValueT *value)
{
    DB_POINTER2(cfgItemDesc, value);

    char paraStr[DB_CFG_PARAM_MAX_STRING] = {0};
    // 校验格式
    Status ret = CfgParamThresholdSplitString(cfgItemDesc, value, paraStr, DB_CFG_PARAM_MAX_STRING, RSM_KEY_RANGE_INFO);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 获取数值
    uint32_t valueGet[RSM_KEY_RANGE_NUM] = {0};
    ret = CfgParamThresholdGetValue(paraStr, DB_CFG_PARAM_MAX_STRING, valueGet, RSM_KEY_RANGE_NUM, RSM_KEY_RANGE_INFO);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 校验数值
    return CfgParamRsmRangeCheckValue(cfgItemDesc, valueGet, RSM_KEY_RANGE_NUM, RSM_KEY_RANGE_INFO);
}

Status CfgParamValidateWorkerHungThreshold(const DbCfgItemDescT *cfgItemDesc, const DbCfgValueT *value)
{
    DB_POINTER2(cfgItemDesc, value);

    char paraStr[DB_CFG_PARAM_MAX_STRING] = {0};
    Status ret =
        CfgParamThresholdSplitString(cfgItemDesc, value, paraStr, DB_CFG_PARAM_MAX_STRING, WORKER_HUNG_THRESHOLD_INFO);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t valueGet[WORKER_HUNG_THRESHOLD_NUM] = {0};
    ret = CfgParamThresholdGetValue(
        paraStr, DB_CFG_PARAM_MAX_STRING, valueGet, WORKER_HUNG_THRESHOLD_NUM, WORKER_HUNG_THRESHOLD_INFO);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CfgParamThresholdCheckValue(cfgItemDesc, valueGet, WORKER_HUNG_THRESHOLD_NUM, WORKER_HUNG_THRESHOLD_INFO);
}

Status CfgParamValidateTrxMonitorThreshold(const DbCfgItemDescT *cfgItemDesc, const DbCfgValueT *value)
{
    DB_POINTER2(cfgItemDesc, value);

    char paraStr[DB_CFG_PARAM_MAX_STRING] = {0};
    Status ret =
        CfgParamThresholdSplitString(cfgItemDesc, value, paraStr, DB_CFG_PARAM_MAX_STRING, TRX_MONITOR_THRESHOLD_INFO);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t valueGet[TRX_MONITOR_THRESHOLD_NUM] = {0};
    ret = CfgParamThresholdGetValue(
        paraStr, DB_CFG_PARAM_MAX_STRING, valueGet, TRX_MONITOR_THRESHOLD_NUM, TRX_MONITOR_THRESHOLD_INFO);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CfgParamThresholdCheckValue(cfgItemDesc, valueGet, TRX_MONITOR_THRESHOLD_NUM, TRX_MONITOR_THRESHOLD_INFO);
}

static Status CheckShmemKeyThresholdInner(
    const DbCfgItemDescT *cfgItemDesc, const uint32_t *value, uint32_t valueNum, const char *info)
{
    DB_POINTER(info);
    for (uint32_t i = 0; i < valueNum; ++i) {
        if ((value[i] < cfgItemDesc->min) || (value[i] > cfgItemDesc->max)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "The value of %s is unsound.", info);
            return GMERR_DATA_EXCEPTION;
        }
    }
    return GMERR_OK;
}

Status CfgParamValidateSchemaInfoShmemKeyThreshold(const DbCfgItemDescT *cfgItemDesc, const DbCfgValueT *value)
{
    DB_POINTER2(cfgItemDesc, value);

    char paraStr[DB_CFG_PARAM_MAX_STRING] = {0};
    Status ret =
        CfgParamThresholdSplitString(cfgItemDesc, value, paraStr, DB_CFG_PARAM_MAX_STRING, SCHEMA_INFO_SHMEMKET_INFO);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t valueGet[SCHEMA_INFO_SHMEMKEY_NUM] = {0};
    ret = CfgParamThresholdGetValue(
        paraStr, DB_CFG_PARAM_MAX_STRING, valueGet, SCHEMA_INFO_SHMEMKEY_NUM, SCHEMA_INFO_SHMEMKET_INFO);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CheckShmemKeyThresholdInner(cfgItemDesc, valueGet, TRX_MONITOR_THRESHOLD_NUM, TRX_MONITOR_THRESHOLD_INFO);
    if (ret != GMERR_OK) {
        return ret;
    }
    // gmjson(gmconfig) key与gmpolicy key不能相同。
    if (valueGet[0] == valueGet[1]) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATA_EXCEPTION, "Value1:%" PRIu32 ", value2:%" PRIu32 ".", valueGet[0], valueGet[1]);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

#if defined(FEATURE_TS) || defined(TS_MULTI_INST) || defined(FEATURE_SQL)
static Status ParseConvertSizeUnit2Num(const char *unit, uint64_t *size)
{
    const struct {
        const char *uintStr;
        uint64_t size;
    } tempFileMaxSizeUnit[] = {
        {"B", 1ULL}, {"KB", (uint64_t)SIZE_K(1)}, {"MB", SIZE_M(1)}, {"GB", SIZE_G(1)}, {"TB", SIZE_T(1)}};
    int32_t count = ELEMENT_COUNT(tempFileMaxSizeUnit);
    for (int32_t i = 0; i < count; i++) {
        if (strcmp(unit, tempFileMaxSizeUnit[i].uintStr) == 0) {
            *size = tempFileMaxSizeUnit[i].size;
            return GMERR_OK;
        }
    }
    *size = 0;
    return GMERR_INVALID_PARAMETER_VALUE;
}

static Status SplitTempFileMaxSizeTokens(const char *limitStr, char *str, char **token)
{
    uint32_t len = (uint32_t)strlen(limitStr);
    token[0] = str;
    uint32_t tokenNum = 0;
    tokenNum++;
    for (uint32_t i = 0; i < len; i++) {
        if (!IsNumChar(limitStr[i])) {
            errno_t err = memcpy_s(str, i, limitStr, i);
            if (err != EOK) {
                DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Take the first token.");
                return GMERR_MEMORY_OPERATE_FAILED;
            }
            str[i] = '\0';
            err = strcpy_s(str + i + 1, TEMP_FILE_MAX_SIZE_CONFIG_LEN - i - 1, limitStr + i);
            if (err != EOK) {
                DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Take the second token");
                return GMERR_MEMORY_OPERATE_FAILED;
            }
            token[tokenNum] = str + i + 1;
            tokenNum++;
            break;
        }
    }
    if (tokenNum != TEMP_FILE_MAX_SIZE_TOKEN_NUM) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Parse temp file max size token.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status GetTempFileMaxSize(const DbCfgItemDescT *cfgItemDesc, const DbCfgValueT *value, uint64_t *size)
{
    DB_POINTER3(cfgItemDesc, value, size);
    const char *limitStr = value->str;
    char str[TEMP_FILE_MAX_SIZE_CONFIG_LEN];

    char *token[TEMP_FILE_MAX_SIZE_TOKEN_NUM] = {NULL};
    Status ret = SplitTempFileMaxSizeTokens(limitStr, str, token);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint64_t longValue = 0;
    ret = DbStrToUint64(token[0], &longValue);
    if (ret != GMERR_OK || longValue == 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Get token value, value=%s.", limitStr);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DbStrToUpper(token[1]);
    uint64_t unit = 0;
    ret = ParseConvertSizeUnit2Num(token[1], &unit);
    if (ret != GMERR_OK || unit == 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Get unit, unit='%s'.", token[1]);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (TEMP_FILE_LIMIT_MAX / unit < longValue || longValue * unit < TEMP_FILE_LIMIT_MIN) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_INVALID_PARAMETER_VALUE, "temp file max size should in range from 32MB to 1TB");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    *size = (int64_t)longValue * unit;
    return GMERR_OK;
}

Status CfgParamValidateTempFileMaxSize(const DbCfgItemDescT *cfgItemDesc, const DbCfgValueT *value)
{
    uint64_t size = 0;
    return GetTempFileMaxSize(cfgItemDesc, value, &size);
}
#endif

static void DbCfgChangeAuditLogNotifyFunc(
    const char *configName, const DbCfgValueT *oldValue, DbCfgValueT *newValue, DbInstanceHdT dbInstance);
static void DbCfgUpdMemPeakDfxThreshold(
    const char *configName, const DbCfgValueT *oldValue, DbCfgValueT *newValue, DbInstanceHdT dbInstance);
static void DbCfgUpdMemtraceDynCtxIds(
    const char *configName, const DbCfgValueT *oldValue, DbCfgValueT *newValue, DbInstanceHdT dbInstance);

#if !defined(NDEBUG)
static void DbCfgChangeTraceLogNotifyFunc(
    const char *configName, const DbCfgValueT *oldValue, DbCfgValueT *newValue, DbInstanceHdT dbInstance)
{
    DB_UNUSED(dbInstance);
    DbSetYangTraceLogMask((uint32_t)newValue->int32Val);
}
#endif

// 并发方案：不涉及并发，资源只读
static DB_CFG_DESC g_dbCfgItemDescs[(int32_t)DB_CFG_EM_ITEM_BUTT] = {
    // { Name,                    Description info,                             Min value,           Max value,
    // Default,
    // Validating function,       ID,                                         ChangeMode,               Data type }
    {"lpm4VrIdMax", DB_CFG_LPM4_VRID_MAX_DESC, 16, 4096, "16", CfgParamValidateIrange, DB_CFG_LPM4_VRID_MAX,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"lpm4VrfIdMax", DB_CFG_LPM4_VRFID_MAX_DESC, 1024, 16384, "1024", CfgParamValidateIrange, DB_CFG_LPM4_VRFID_MAX,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"lpm6VrIdMax", DB_CFG_LPM6_VRID_MAX_DESC, 16, 4096, "16", CfgParamValidateIrange, DB_CFG_LPM6_VRID_MAX,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"lpm6VrfIdMax", DB_CFG_LPM6_VRFID_MAX_DESC, 1024, 16384, "1024", CfgParamValidateIrange, DB_CFG_LPM6_VRFID_MAX,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"defaultHashType", DB_CFG_DEFAULT_HASH_TYPE_DESC, 0, 0, DB_DEFAULT_HASH_TYPE_CFG_HASH_INDEX, NULL,
        DB_CFG_DEFAULT_HASH_TYPE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    {"auditLogEnableDCL", DB_CFG_AUDIT_LOG_DCL_ENABLE_DESC, 0, 1, "1", CfgParamValidateIrange,
        DB_CFG_AUDIT_LOG_DCL_ENABLE, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, DbCfgChangeAuditLogNotifyFunc},
    {"auditLogEnableDDL", DB_CFG_AUDIT_LOG_DDL_ENABLE_DESC, 0, 1, "1", CfgParamValidateIrange,
        DB_CFG_AUDIT_LOG_DDL_ENABLE, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, DbCfgChangeAuditLogNotifyFunc},
    {"auditLogEnableDML", DB_CFG_AUDIT_LOG_DML_ENABLE_DESC, 0, 1, "1", CfgParamValidateIrange,
        DB_CFG_AUDIT_LOG_DML_ENABLE, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, DbCfgChangeAuditLogNotifyFunc},
    {"auditLogEnableDQL", DB_CFG_AUDIT_LOG_DQL_ENABLE_DESC, 0, 1, "1", CfgParamValidateIrange,
        DB_CFG_AUDIT_LOG_DQL_ENABLE, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, DbCfgChangeAuditLogNotifyFunc},
    {"longProcTimeThreshold", DB_CFG_LONG_PROC_TIME_THRESHOLD_DESC, -1, 1000, "100", CfgParamValidateIrange,
        DB_CFG_LONG_PROC_TIME_THRESHOLD, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#ifdef FEATURE_SIMPLEREL
    {"localLocatorListened", DB_CFG_LOCAL_LOCATOR_DESC, 0, 0, "usocket:/run/verona/unix_emserver",
        CfgParamValidateLocalLocator, DB_CFG_LOCAL_LOCATOR, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
#else
    {"localLocatorListened", DB_CFG_LOCAL_LOCATOR_DESC, 0, 0, "channel:ctl_channel", CfgParamValidateLocalLocator,
        DB_CFG_LOCAL_LOCATOR, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
#endif
    {"logLengthMax", DB_CFG_LOG_LENGTH_MAX_DESC, 128, DB_DEFAULT_LOG_SIZE, "512", CfgParamValidateIrange,
        DB_CFG_LOG_LENGTH_MAX, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"logFileNumMax", DB_CFG_LOG_FILE_NUM_MAX_DESC, 1, 1024, "16", CfgParamValidateIrange, DB_CFG_LOG_FILE_NUM_MAX,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"logFileSizeMax", DB_CFG_LOG_FILE_SIZE_MAX_DESC, 128, 67108864, "2097152", CfgParamValidateIrange,
        DB_CFG_LOG_FILE_SIZE_MAX, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"enableLogFold", DB_CFG_ENABLE_LOG_FOLD_DESC, 0, 1, "1", CfgParamValidateIrange, DB_CFG_ENABLE_LOG_FOLD,
        DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, DbLogUpdateFoldCfg},
    {"logFoldRule", DB_CFG_LOG_FOLD_RULE_DESC, 0, 0, "1:1,3600:50", DfgParamInvalidLogFoldRule, DB_CFG_LOG_FOLD_RULE,
        DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_STRING, DbLogUpdateFoldCfg},
#ifndef FEATURE_PERSISTENCE
#ifdef FEATURE_SIMPLEREL
    {"deviceSize", DB_CFG_SE_DEV_SIZE_DESC, 1, 1024, "2", CfgParamValidateIrange, DB_CFG_SE_DEV_SIZE,
#else
    {"deviceSize", DB_CFG_SE_DEV_SIZE_DESC, 1, 1024, "4", CfgParamValidateIrange, DB_CFG_SE_DEV_SIZE,
#endif
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"extendSize", DB_CFG_SE_EXTEND_SIZE_DESC, 8, 1048576, "4096", CfgParamValidateIrange, DB_CFG_SE_EXTEND_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
#ifdef ARM32
#ifdef FEATURE_PERSISTENCE
    {"deviceSize", DB_CFG_SE_DEV_SIZE_DESC, 1, 762, "4", CfgParamValidateIrange, DB_CFG_SE_DEV_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"extendSize", DB_CFG_SE_EXTEND_SIZE_DESC, 8, 780288, "4096", CfgParamValidateIrange, DB_CFG_SE_EXTEND_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
    {"defaultTablespaceMaxSize", DB_CFG_DEFAULT_TABLE_SPACE_MAX_SIZE_DESC, 1, 3047, "32", CfgParamValidateIrange,
        DB_CFG_DEFAULT_TABLE_SPACE_MAX_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#elif defined(HPE)
#ifdef FEATURE_PERSISTENCE
    {"deviceSize", DB_CFG_SE_DEV_SIZE_DESC, 1, 768, "4", CfgParamValidateIrange, DB_CFG_SE_DEV_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"extendSize", DB_CFG_SE_EXTEND_SIZE_DESC, 8, 786432, "4096", CfgParamValidateIrange, DB_CFG_SE_EXTEND_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
    {"defaultTablespaceMaxSize", DB_CFG_DEFAULT_TABLE_SPACE_MAX_SIZE_DESC, 1, 3071, "32", CfgParamValidateIrange,
        DB_CFG_DEFAULT_TABLE_SPACE_MAX_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#else
#ifdef FEATURE_PERSISTENCE
    {"deviceSize", DB_CFG_SE_DEV_SIZE_DESC, 1, 1024, "4", CfgParamValidateIrange, DB_CFG_SE_DEV_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"extendSize", DB_CFG_SE_EXTEND_SIZE_DESC, 8, 1048576, "4096", CfgParamValidateIrange, DB_CFG_SE_EXTEND_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
    {"defaultTablespaceMaxSize", DB_CFG_DEFAULT_TABLE_SPACE_MAX_SIZE_DESC, 1, 16359, "32", CfgParamValidateIrange,
        DB_CFG_DEFAULT_TABLE_SPACE_MAX_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
#ifdef FEATURE_SIMPLEREL
    {"pageSize", DB_CFG_SE_PAGE_SIZE_DESC, 4, 64, "16", CfgParamValidatePageSize, DB_CFG_SE_PAGE_SIZE,
#else
    {"pageSize", DB_CFG_SE_PAGE_SIZE_DESC, 4, 64, "32", CfgParamValidatePageSize, DB_CFG_SE_PAGE_SIZE,
#endif
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#ifdef FEATURE_PERSISTENCE
    {"ctrlPageSize", DB_CFG_SE_CTRL_PAGE_SIZE_DESC, 4, 64, "32", CfgParamValidatePageSize, DB_CFG_SE_CTRL_PAGE_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
#ifdef ARM32
    {"maxSeMem", DB_CFG_SE_MAX_MEM_DESC, 8, 3048, "1024", CfgParamValidateIrange, DB_CFG_SE_MAX_MEM,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#elif defined(HPE)
    {"maxSeMem", DB_CFG_SE_MAX_MEM_DESC, 8, 3072, "1024", CfgParamValidateIrange, DB_CFG_SE_MAX_MEM,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#else
    {"maxSeMem", DB_CFG_SE_MAX_MEM_DESC, 8, 1048552, "1024", CfgParamValidateIrange, DB_CFG_SE_MAX_MEM,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
    {"instanceId", DB_CFG_SE_GET_INSTANCE_ID_DESC, FIRST_INSTANCE_ID, MAX_INSTANCE_ID, "1", CfgParamValidateIrange,
        DB_CFG_SE_GET_INSTANCE_ID, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"isUseHugePage", "Use huge page for storage or not.", 0, 1, "0", CfgParamValidateIrange, DB_CFG_HUGE_PAGE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#ifdef ARM32
    {"maxTotalShmSize", "Total size of share memory alloced by server, unit: MB.", 32, 3072, "2048",
        CfgParamValidateIrange, DB_CFG_SERVER_TOTAL_SHM_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"maxResPoolShmSize", "Total size of share memory used for resource pool, unit: MB.", 16, 3072, "16",
        CfgParamValidateIrange, DB_CFG_RESOURCE_POOL_SHM_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#if defined(FEATURE_SIMPLEREL) && !(defined(X86_64) || defined(ARM64))
    {"maxTotalDynSize", "Total size of Dynamic memory alloced by server, unit: MB.", 24, 3072, "1024",
#else
    {"maxTotalDynSize", "Total size of Dynamic memory alloced by server, unit: MB.", 24, 3072, "2048",
#endif
        CfgParamValidateIrange, DB_CFG_SERVER_TOTAL_DYN_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"maxSysShmSize", "system memory size of share memory alloced by server, unit: MB", 12, 3052, "256",
        CfgParamValidateIrange, DB_CFG_MAX_SYS_SHM_MEM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#if defined(FEATURE_SIMPLEREL) && !(defined(X86_64) || defined(ARM64))
    {"maxSysDynSize", "system memory size of Dynamic memory alloced by server, unit: MB", 12, 3039, "384",
#else
    {"maxSysDynSize", "system memory size of Dynamic memory alloced by server, unit: MB", 12, 3039, "512",
#endif
        CfgParamValidateIrange, DB_CFG_MAX_SYS_DYN_MEM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"memPeakDfxThreshold", "Threshold to trigger memory peak alarm log, unit: MB.", -1, 3072, "-1",
        CfgParamValidateIrange, DB_CFG_MEM_PEAK_DFX_THREASHOLD, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32,
        DbCfgUpdMemPeakDfxThreshold},
#else
    {"maxTotalShmSize", "Total size of share memory alloced by server, unit: MB.", 32, 1048576, "2048",
        CfgParamValidateIrange, DB_CFG_SERVER_TOTAL_SHM_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"maxResPoolShmSize", "Total size of share memory used for resource pool, unit: MB.", 16, 16384, "16",
        CfgParamValidateIrange, DB_CFG_RESOURCE_POOL_SHM_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#if defined(FEATURE_SIMPLEREL) && !(defined(X86_64) || defined(ARM64))
    {"maxTotalDynSize", "Total size of Dynamic memory alloced by server, unit: MB.", 24, 1572864, "1024",
#else
    {"maxTotalDynSize", "Total size of Dynamic memory alloced by server, unit: MB.", 24, 1572864, "2048",
#endif
        CfgParamValidateIrange, DB_CFG_SERVER_TOTAL_DYN_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"maxSysShmSize", "system memory size of share memory alloced by server, unit: MB", 12, 16364, "256",
        CfgParamValidateIrange, DB_CFG_MAX_SYS_SHM_MEM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#if defined(FEATURE_SIMPLEREL) && !(defined(X86_64) || defined(ARM64))
    {"maxSysDynSize", "system memory size of Dynamic memory alloced by server, unit: MB", 12, 1572831, "384",
#else
    {"maxSysDynSize", "system memory size of Dynamic memory alloced by server, unit: MB", 12, 1572831, "512",
#endif
        CfgParamValidateIrange, DB_CFG_MAX_SYS_DYN_MEM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"memPeakDfxThreshold", "Threshold to trigger memory peak alarm log, unit: MB.", -1, 16384, "-1",
        CfgParamValidateIrange, DB_CFG_MEM_PEAK_DFX_THREASHOLD, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32,
        DbCfgUpdMemPeakDfxThreshold},
#endif
    {"liteDynMemMod", "When enabled, dyn mem middleware is closed. Memory directly allocated from OS.", 0, 1, "0",
        CfgParamValidateIrange, DB_CFG_LITE_DYN_MEM_MOD, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"memRunLinkLog", "Memory Run Link Log Level.", 0, 1, "0", CfgParamValidateIrange, DB_CFG_MEM_RUN_LINK_LOG,
        DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"planCacheSize", DB_CFG_PLAN_CACHE_SIZE_DESC, 8, 1048576, "32", CfgParamValidateIrange, DB_CFG_PLAN_CACHE_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    // for flow control
    {"clientServerFlowControl", DB_CFG_FLOW_CONTROL_DESC, 0, 0, "0;0;0;0", CfgParamFlowCtrl, DB_CFG_FLOW_CONTROL,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    {"flowControlSleepTime", DB_CFG_FLOW_CONTROL_SLEEP_TIME_DESC, 0, 1000000, "0,0,0", CfgParamFlowCtrlSleepTime,
        DB_CFG_FLOW_CONTROL_SLEEP_TIME, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    {"overloadThreshold", DB_CFG_OVER_LOAD_THRESHOLD_DESC, 0, 0, THRESHOLD_DEFAULT, CfgParamValidateThreshold,
        DB_CFG_OVER_LOAD_THRESHOLD, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    {"minFragmentationRateThreshold", "min fragmentation rate threshold, unit: percentage", 0, 100, "50",
        CfgParamValidateIrange, DB_CFG_DEFRAGMENTATION_RATE_THRESHOLD, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32,
        NULL},
#ifdef ARM32
    {"minFragmentationMemThreshold", "min fragmentation Memory threshold, unit: MB", 0, 3072, "64",
        CfgParamValidateIrange, DB_CFG_DEFRAGMENTATION_MEM_THRESHOLD, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32,
        NULL},
#else
    {"minFragmentationMemThreshold", "min fragmentation Memory threshold, unit: MB", 0, 16384, "64",
        CfgParamValidateIrange, DB_CFG_DEFRAGMENTATION_MEM_THRESHOLD, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32,
        NULL},
#endif
    {"memCompactEnable", DB_CFG_MEM_COMPACT_ENABLE_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_MEM_COMPACT_ENABLE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"maxUndoSpaceSize", DB_CFG_MAX_UNDO_SPACE_SIZE_DESC, 1, 1024, "300", CfgParamValidateIrange,
        DB_CFG_MAX_UNDO_SPACE_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"trxLockWakeupPeriod", DB_CFG_TRX_LOCK_WAKEUP_PERIOD_DESC, 1, 1000000, "100", CfgParamValidateIrange,
        DB_CFG_TRX_LOCK_WAKEUP_PERIOD, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"trxDeadlockCheckPeriod", DB_CFG_TRX_LOCK_DEADLOCK_CHECK_PERIOD_DESC, 1, 1000000, "300", CfgParamValidateIrange,
        DB_CFG_TRX_LOCK_DEADLOCK_CHECK_PERIOD, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"latchDeadlockDebugTimeout", DB_CFG_LATCH_DEADLOCK_DEBUG_TIMEOUT_DESC, 1, 60000000, "10000000",
        CfgParamValidateIrange, DB_CFG_LATCH_DEADLOCK_DEBUG_TIMEOUT, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32,
        NULL},
    {"trxLockJumpQueuePeriod", DB_CFG_TRX_LOCK_JUMP_QUEUE_PERIOD_DESC, 1, 1000000, "500", CfgParamValidateIrange,
        DB_CFG_TRX_LOCK_JUMP_QUEUE_PERIOD, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"trxLockTimeOut", DB_CFG_TRX_LOCK_TIME_OUT_DESC, 1, 1000000, "1000", CfgParamValidateIrange,
        DB_CFG_TRX_LOCK_TIME_OUT, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"trxMonitorEnable", DB_CFG_TRX_MONITOR_ENABLE_DESC, 0, 1, "1", CfgParamValidateIrange, DB_CFG_TRX_MONITOR_ENABLE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"trxMonitorThreshold", DB_CFG_TRX_MONITOR_THRESHOLD_DESC, 1, MAX_TRX_MONITOR_THRESHOLD, "60,120",
        CfgParamValidateTrxMonitorThreshold, DB_CFG_TRX_MONITOR_THRESHOLD, DB_CFG_CHANGE_NOT_ALLOWED,
        DB_DATATYPE_STRING, NULL},
    {"enableConSubsStatis", DB_CFG_ENABLE_CONN_SUBS_STATIS_DESC, 0, 1, "1", CfgParamValidateIrange,
        DB_CFG_ENABLE_CONN_SUBS_STATIS, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"enableSchedulePerfStat", DB_CFG_SCHEDULE_PERF_STAT_IS_ENABLED_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_SCHEDULE_PERF_STAT_IS_ENABLED, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"maxSortBufferSize", DB_CFG_MAX_SORT_BUFFER_SIZE_DESC, 1, 256, "64", CfgParamValidateIrange,
        DB_CFG_MAX_SORT_BUFFER_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"monitorWorkerSchedulePeriod", DB_CFG_MONITOR_WORKER_SCHEDULE_PERIOD_DESC, 0, 5000, "5000", CfgParamValidateIrange,
        DB_CFG_MONITOR_WORKER_SCHEDULE_PERIOD, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"workerHungThreshold", DB_CFG_WORKER_HUNG_THRESHOLD_DESC, 3, 1000, "20,299,300",
        CfgParamValidateWorkerHungThreshold, DB_CFG_WORKER_HUNG_THRESHOLD, DB_CFG_CHANGE_NOT_ALLOWED,
        DB_DATATYPE_STRING, NULL},
    {"enableDmlOperStat", DB_CFG_DML_OPER_STAT_IS_ENABLED_DESC, 0, 1, "1", CfgParamValidateIrange,
        DB_CFG_DML_OPER_STAT_IS_ENABLED, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"enableDmlPerfStat", DB_CFG_DML_PERF_STAT_IS_ENABLED_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_DML_PERF_STAT_IS_ENABLED, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"DBA", DB_CFG_DBA_DESC, 0, 0, DB_DBA_NAME ":" DB_DBA_PROCESS_NAME, NULL, DB_CFG_DBA_INFO,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    {"agePushSubsBatch", DB_CFG_AGE_PUSH_SUBS_BATCH_DESC, 1, 200, "10", CfgParamValidateIrange,
        DB_CFG_AGE_PUSH_SUBS_BATCH, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#ifdef FEATURE_SIMPLEREL
    {"userPolicyMode", DB_CFG_USER_POLICY_MODE_DESC, 0, 2, "0", CfgParamValidateIrange, DB_CFG_USER_POLICY_MODE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#else
    {"userPolicyMode", DB_CFG_USER_POLICY_MODE_DESC, 0, 2, "2", CfgParamValidateIrange, DB_CFG_USER_POLICY_MODE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
    {"enablePrintAgedInfo", DB_CFG_PRINT_AGED_INFO_ENABLED_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_PRINT_AGED_INFO_ENABLED, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
#ifdef FEATURE_SIMPLEREL
    {"maxConnNum", DB_CFG_CONN_MAX_DESC, MIN_CONN_NUM, MAX_CONN_NUM, "256", CfgParamValidateIrange, DB_CFG_CONN_MAX,
#else
    {"maxConnNum", DB_CFG_CONN_MAX_DESC, MIN_CONN_NUM, MAX_CONN_NUM, "100", CfgParamValidateIrange, DB_CFG_CONN_MAX,
#endif
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"maxStmtCnt", DB_CFG_MAX_STMT_CNT_DESC, 1, 65535, "10240", CfgParamValidateIrange, DB_CFG_MAX_STMT_CNT,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"scheduleMode", DB_CFG_RT_SCHEDULE_MODE_DESC, 0, 3, "0", CfgParamValidateIrange, DB_CFG_SCHEDULE_MODE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"permanentWorkerNum", DB_CFG_PERMANENT_WORKER_NUM_DESC, 1, 1536, "4", CfgParamValidateIrange,
        DB_CFG_PERMANENT_WORKER_NUM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#ifdef FEATURE_SIMPLEREL
    {"subsChannelGlobalShareMemSizeMax", DB_CFG_SUBS_CHANNEL_SHARE_MEM_MAX_DESC, 1, 2048, "1", CfgParamValidateIrange,
        DB_CFG_SUBS_CHANNEL_SHARE_MEM_MAX, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"subsChannelGlobalDynamicMemSizeMax", DB_CFG_SUBS_CHANNEL_DYNAMIC_MEM_MAX_DESC, 1, 2048, "1",
        CfgParamValidateIrange, DB_CFG_SUBS_CHANNEL_DYNAMIC_MEM_MAX, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32,
        NULL},
#else
    {"subsChannelGlobalShareMemSizeMax", DB_CFG_SUBS_CHANNEL_SHARE_MEM_MAX_DESC, 1, 2048, "512", CfgParamValidateIrange,
        DB_CFG_SUBS_CHANNEL_SHARE_MEM_MAX, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"subsChannelGlobalDynamicMemSizeMax", DB_CFG_SUBS_CHANNEL_DYNAMIC_MEM_MAX_DESC, 1, 2048, "512",
        CfgParamValidateIrange, DB_CFG_SUBS_CHANNEL_DYNAMIC_MEM_MAX, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32,
        NULL},
#endif
    {"compatibleV3", DB_CFG_COMPATIBLE_V3_DESC, 0, 1, "1", CfgParamValidateIrange, DB_CFG_COMPATIBLE_V3,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"enableTableLock", DB_CFG_TABLE_LOCK_IS_ENABLED_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_TABLE_LOCK_IS_ENABLED, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"openmpCount", DB_CFG_OPENMP_COUNT_DESC, 0, 32, "0", CfgParamValidateIrange, DB_CFG_OPENMP_COUNT,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"isFastReadUncommitted", DB_CFG_IS_FAST_READ_UNCOMMITTED_DESC, 0, 1, "1", CfgParamValidateIrange,
        DB_CFG_IS_FAST_READ_UNCOMMITTED, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"enableClusterHash", DB_CFG_CLUSTER_HASH_IS_ENABLED_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_CLUSTER_HASH_IS_ENABLED, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"isCltStatisEnable", DB_CFG_IS_CLT_STATIS_ENABLE_DESC, 0, 1, "1", CfgParamValidateIrange,
        DB_CFG_IS_CLT_STATIS_ENABLE, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"messageSecurityCheck", DB_CFG_MESSAGE_SECURITY_CHECK_DESC, 0, 1, "1", CfgParamValidateIrange,
        DB_CFG_MESSAGE_SECURITY_CHECK, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"shmemPermission", DB_CFG_SHMEM_PERMISSION_DESC, 0, 0, "0600", CfgParamValidateShmemPermission,
        DB_CFG_SHMEM_PERMISSION, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    {"devShmemPermission", DB_CFG_DEV_SHMEM_PERMISSION_DESC, 0, 0, "", CfgParamValidateDevShmemPermission,
        DB_CFG_DEV_SHMEM_PERMISSION, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    {"StartUpShmClear", DB_CFG_START_UP_SHM_CLEAR_DESC, 0, 2, "0", CfgParamValidateIrange, DB_CFG_START_UP_SHM_CLEAR,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"memtraceDynCtxIds", DB_CFG_MEMTRACE_DYN_CTXIDS_DESC, 0, 10240, "0,", CfgParamMemtraceCtxIds,
        DB_CFG_MEMTRACE_DYN_CTXIDS, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_STRING, DbCfgUpdMemtraceDynCtxIds},
    {"defaultTransactionType", DB_CFG_DEFAULT_TRANSACTION_TYPE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_DEFAULT_TRANSACTION_TYPE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"defaultIsolationLevel", DB_CFG_DEFAULT_ISOLATION_LEVEL_DESC, 0, 3, "1", CfgParamValidateCheckTrxTypeAndIsoLevel,
        DB_CFG_DEFAULT_ISOLATION_LEVEL, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"hashClusterBucketsCount", DB_CFG_HASH_CLUSTER_BUCKET_CNT_DESC, 100, 10000000, "16384", CfgParamValidateIrange,
        DB_CFG_HASH_CLUSTER_BUCKET_CNT, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"yangBigObjectSize", DB_CFG_YANG_BIG_OBJECT_SIZE_DESC, 1, 65536, "512", CfgParamValidateIrange,
        DB_CFG_YANG_BIG_OBJECT_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"maxConnMsgShmMem", DB_CFG_MAX_CONN_MSG_SHM_MEM_DESC, 8, 16384, "2048", CfgParamValidateIrange,
        DB_CFG_MAX_CONN_MSG_SHM_MEM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"shareSubMsgTimeout", DB_CFG_SHM_SUB_MSG_TIMEOUT_DESC, 1, 65536, "1440", CfgParamValidateIrange,
        DB_CFG_SHM_SUB_MSG_TIMEOUT, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"datalogTimeoutScheduleInterval", DB_CFG_DATALOG_TIMEOUT_SCHEDULE_INTERVAL_DESC, 100, 5000, "3000",
        CfgParamValidateIrange, DB_CFG_DATALOG_TIMEOUT_SCHEDULE_INTERVAL, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32,
        NULL},
    {"datalogCallBackTimeoutThreshold", DB_CFG_DATALOG_CALLBACK_TIMEOUT_THRESHOLD_DESC, 1, 30, "2",
        CfgParamValidateIrange, DB_CFG_DATALOG_CALLBACK_TIMEOUT_THRESHOLD, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32,
        NULL},
    {"udfTimeOut", DB_CFG_DTL_UDF_TIME_OUT_DESC, 0, 1000, "1000", CfgParamValidateIrange, DB_CFG_DTL_UDF_TIME_OUT,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"connectTimeout", DB_CFG_CONNECT_TIMEOUT_DESC, 0, 1000000, "0", CfgParamValidateIrange, DB_CFG_CONNECT_TIMEOUT,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#ifdef EXPERIMENTAL_NERGC
    {"tcpConnTimeout", DB_CFG_TCP_CONNECT_TIMEOUT_DESC, 0, 100, "10", CfgParamValidateIrange,
        DB_CFG_TCP_CONNECT_TIMEOUT, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"tcpRetryTimeout", DB_CFG_TCP_FAIL_RETRY_TIMEOUT_DESC, 0, 100, "5", CfgParamValidateIrange,
        DB_CFG_TCP_FAIL_RETRY_TIMEOUT, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
    {"udfMemSizeMax", DB_CFG_UDF_MAX_MEM_DESC, 1, 2048, "2048", CfgParamValidateIrange, DB_CFG_UDF_MAX_MEM,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"udfMemAllocAlarmSize", DB_CFG_UDF_MEM_ALLOC_ALARM_DESC, 0, 2048, "2048", CfgParamValidateIrange,
        DB_CFG_UDF_MEM_ALLOC_ALARM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"udfEnable", DB_CFG_UDF_ENABLE_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_UDF_ENABLE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"udfEscapeMemSize", DB_CFG_UDF_ESCAPE_MEM_SIZE_DESC, 1, 1024, "10", CfgParamValidateIrange,
        DB_CFG_UDF_ESCAPE_MEM_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#if !defined(NDEBUG)
    {"yangTraceLog", DB_CFG_YANG_TRACE_LOG_DESC, 0, 2147483647, "0", CfgParamValidateIrange, DB_CFG_YANG_TRACE_LOG,
        DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, DbCfgChangeTraceLogNotifyFunc},
#endif
    {"forceCommitEnable", DB_CFG_FORCE_COMMIT_ENABLE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_FORCE_COMMIT_ENABLE, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"datalogRunLinkLog", DB_CFG_DATALOG_LOG_RUN_LINK_DESC, 0, 2147483647, "0", CfgParamValidateIrange,
        DB_CFG_DATALOG_RUN_LINK_LOG, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"enableDatalogDmlWhenUpgrading", DB_CFG_ENABLE_DATALOG_DML_WHEN_UPG_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_ENABLE_DATALOG_DML_WHEN_UPG, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"datalogUpgradeFetchSize", DB_CFG_DATALOG_UPG_FETCH_SIZE_DESC, 1, 2147483647, "1", CfgParamValidateIrange,
        DB_CFG_DATALOG_UPG_FETCH_SIZE, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"upgradeMemActualAndEstimatedPercentage", DB_CFG_DATALOG_UPGRADE_MEM_ACTUAL_ESTIMATE_PERCENTAGE_DESC, 0, 1000,
        "300", CfgParamValidateIrange, DB_CFG_DATALOG_UPGRADE_MEM_ACTUAL_ESTIMATE_PERCENTAGE, DB_CFG_CHANGE_NO_LIMIT,
        DB_DATATYPE_INT32, NULL},
    {"datalogResetWhenUpgradeRollbackFail", DB_CFG_DATALOG_RESE_WHEN_UPGRADE_ROLLBACK_FAIL_DESC, 0, 1, "1",
        CfgParamValidateIrange, DB_CFG_DATALOG_RESE_WHEN_UPGRADE_ROLLBACK_FAIL, DB_CFG_CHANGE_NO_LIMIT,
        DB_DATATYPE_INT32, NULL},
    {"maxNormalTableNum", DB_CFG_MAX_NORMAL_TABLE_NUM_DESC, DB_MAX_NORMAL_TABLE_NUM_MIN, DB_MAX_NORMAL_TABLE_NUM_MAX,
#ifdef FEATURE_SIMPLEREL
        "196605", CfgParamValidateIrange, DB_CFG_MAX_NORMAL_TABLE_NUM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32,
#else
        "2000", CfgParamValidateIrange, DB_CFG_MAX_NORMAL_TABLE_NUM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32,
#endif
        NULL},
    {"maxYangTableNum", DB_CFG_MAX_YANG_TABLE_NUM_DESC, DB_MAX_YANG_TABLE_NUM_MIN, DB_MAX_YANG_TABLE_NUM_MAX, "2000",
        CfgParamValidateIrange, DB_CFG_MAX_YANG_TABLE_NUM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"maxSubscriptionNum", DB_CFG_MAX_SUBSCRIPTION_NUM_DESC, DB_MAX_SUBSCRIPTION_NUM_MIN, DB_MAX_SUBSCRIPTION_NUM_MAX,
        "1024", CfgParamValidateIrange, DB_CFG_MAX_SUBSCRIPTION_NUM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32,
        NULL},
    {"featureLibPath", DB_CFG_FEATURE_LIB_PATH_DESC, 0, 0, "", NULL, DB_CFG_FEATURE_LIB_PATH, DB_CFG_CHANGE_NOT_ALLOWED,
        DB_DATATYPE_STRING, NULL},
    {"featureNames", DB_CFG_FEATURE_NAMES_DESC, 0, 0, "", NULL, DB_CFG_FEATURE_NAMES, DB_CFG_CHANGE_NOT_ALLOWED,
        DB_DATATYPE_STRING, NULL},
    {"schemaLoader", DB_CFG_NEED_SCHEMA_LOADER_DESC, 0, 3, "0", CfgParamValidateIrange, DB_CFG_NEED_SCHEMA_LOADER,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"schemaPath", DB_CFG_SCHEMA_PATH_DESC, 0, 0, "/etc/gmdb;/etc/gmdb_sys_priv;/etc/gmjson;/etc/gmpolicy;", NULL,
        DB_CFG_SCHEMA_PATH, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    {"schemaInfoShmemKey", DB_CFG_SCHEMA_LOADER_SHMEM_DESC, 0, 2147483647, "0,1",
        CfgParamValidateSchemaInfoShmemKeyThreshold, DB_CFG_SCHEMA_INFO_SHMEM_KEY, DB_CFG_CHANGE_NOT_ALLOWED,
        DB_DATATYPE_STRING, NULL},
    {"importSchemaFromShmWaitTime", DB_CFG_SCHEMA_LOADER_WAIT_TIME_DESC, 0, 1800, "60", CfgParamValidateIrange,
        DB_CFG_SCHEMA_LOADER_WAIT_TIME, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"schemaDataPath", DB_CFG_SCHEMA_DATA_PATH_DESC, 0, 0, "", NULL, DB_CFG_SCHEMA_DATA_PATH, DB_CFG_CHANGE_NOT_ALLOWED,
        DB_DATATYPE_STRING, NULL},
    {"datalogSoPath", DB_CFG_DATALOG_SO_PATH_DESC, 0, 0, "", NULL, DB_CFG_DATALOG_SO_PATH, DB_CFG_CHANGE_NOT_ALLOWED,
        DB_DATATYPE_STRING, NULL},
#ifdef FEATURE_SIMPLEREL
    {"lockTableBucketNum", DB_CFG_LOCK_TABLE_BUCKET_NUM_DESC, 13, 1021, "257", CfgParamValidateIrange,
        DB_CFG_LOCK_TABLE_BUCKET_NUM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#else
    {"lockTableBucketNum", DB_CFG_LOCK_TABLE_BUCKET_NUM_DESC, 13, 1021, "1021", CfgParamValidateIrange,
        DB_CFG_LOCK_TABLE_BUCKET_NUM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
    {"enableVerticalIsolation", DB_CFG_VERTICAL_ISOLATION_ENABLE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_VERTICAL_ISOLATION_ENABLE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"maxWriteCacheSize", DB_CFG_MAX_WRITE_CACHE_SIZE_DESC, 0, 512, "0", NULL, DB_CFG_MAX_WRITE_CACHE_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"mergeWriteCacheInterval", DB_CFG_MERGE_WRITE_CACHE_INTERVAL_DESC, 1, 3600000, "10", NULL,
        DB_CFG_MERGE_WRITE_CACHE_INTERVAL, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"instanceType", DB_CFG_INSTANCE_TYPE_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_INSTANCE_TYPE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#if defined(FEATURE_TS) || defined(TS_MULTI_INST) || defined(FEATURE_SQL)
    {"tempFileDir", DB_CFG_TEMP_FILE_DIR_DESC, 0, 0, "/data/gmdb/temp", CfgParamValidateLogPath, DB_CFG_TEMP_FILE_DIR,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    {"forceUseTempFileForQueryResult", DB_CFG_FORCE_USE_TEMP_FILE_QUERY_RESULT_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_FORCE_USE_TEMP_FILE_QUERY_RESULT, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"operatorMemory", DB_CFG_OP_MEMORY_DESC, 1, 1024, "100", CfgParamValidateIrange, DB_CFG_OP_MEMORY,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"cStoreDir", DB_CFG_CSTORE_DIR_DESC, 0, 0, "/data/gmdb/cstore", CfgParamValidateLogPath, DB_CFG_CSTORE_DIR,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    {"tsLcmCheckPeriod", DB_CFG_TS_TCM_CHECK_PERIOD_DESC, 3, 3600, "3600", CfgParamValidateIrange,
        DB_CFG_TS_TCM_CHECK_PERIOD, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"isFileTapeCheckSum", DB_CFG_IS_FILE_TAPE_CHECK_SUM_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_IS_FILE_TAPE_CHECK_SUM, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"resultTempFileMaxSize", DB_CFG_RESULT_TEMP_FILE_MAX_SIZE_DESC, 0, 0, "1GB", CfgParamValidateTempFileMaxSize,
        DB_CFG_RESULT_TEMP_FILE_MAX_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    {"tsAllowDiskClean", DB_CFG_TS_ALLOW_DISK_CLEAN_DESC, 0, 1, "1", CfgParamValidateIrange, DB_CFG_TS_ALLOW_DISK_CLEAN,
        DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"cuCompactEnable", DB_CFG_TS_ALLOW_DISK_CLEAN_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_CU_COMPACT_ENABLE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
    {"persistentMode", DB_CFG_PERSISTENT_MODE_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_PERSISTENT_MODE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"fixPersistEnable", DB_CFG_FIX_PERSIST_ENABLE_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_FIX_PERSIST_ENABLE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    // redo
    {"redoPubBufSize", DB_CFG_REDO_PUB_BUF_SIZE_DESC, 256, 16384, "4096", CfgParamValidateIrange,
        DB_CFG_REDO_PUB_BUF_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"redoBufParts", DB_CFG_REDO_BUF_PARTS_DESC, 1, 16, "1", CfgParamValidateIrange, DB_CFG_REDO_BUF_PARTS,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"redoFlushByTrx", DB_CFG_REDO_FLUSH_BY_TRX_DESC, 0, 1, "1", CfgParamValidateIrange, DB_CFG_REDO_FLUSH_BY_TRX,
        DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"redoFlushBySize", DB_CFG_REDO_FLUSH_BY_SIZE_DESC, 0, 16384, "0", CfgParamValidateIrange,
        DB_CFG_REDO_FLUSH_BY_SIZE, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"redoFlushByTime", DB_CFG_REDO_FLUSH_BY_TIME_DESC, 0, 600000, "1000", CfgParamValidateIrange,
        DB_CFG_REDO_FLUSH_BY_TIME, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"redoFlushCheckPeriod", DB_CFG_REDO_FLUSH_CHECK_PERIOD_DESC, 10, 300000, "500", CfgParamValidateIrange,
        DB_CFG_REDO_FLUSH_CHECK_PERIOD, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"redoFileSize", DB_CFG_REDO_FILE_SIZE_DESC, 1, 1024, "128", CfgParamValidateIrange, DB_CFG_REDO_FILE_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"redoFileCount", DB_CFG_REDO_FILE_COUNT_DESC, 1, 64, "32", CfgParamValidateIrange, DB_CFG_REDO_FILE_COUNT,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"redoFileDropOnClose", DB_CFG_REDO_FILE_DROP_ON_CLOSE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_REDO_FILE_DROP_ON_CLOSE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    // space
    {"dataFileDirPath", DB_CFG_DATA_FILE_DIR_PATH_DESC, 0, 0, "", CfgParamValidateLogPath, DB_CFG_DATA_FILE_DIR_PATH,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
#ifdef FEATURE_PERSISTENCE
    {"dbFilesMaxCnt", DB_CFG_DB_FILES_MAX_COUNT_DESC, 3, 1024, "3", CfgParamValidateIrange, DB_CFG_DB_FILES_MAX_COUNT,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"dbFileSize", DB_CFG_DB_FILE_SIZE_DESC, 4096, 33554432, "1048576", CfgParamValidateIrange, DB_CFG_DB_FILE_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"spaceMaxNum", DB_CFG_SPACE_MAX_NUM_DESC, 3, 1024, "3", CfgParamValidateIrange, DB_CFG_SPACE_MAX_NUM,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"diskLessBoot", DB_CFG_DISKLESS_BOOT_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_DISKLESS_BOOT,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"fileSyncWriteEnable", DB_CFG_FILE_SYNC_WRITE_ENABLE_DESC, 0, 1, "1", CfgParamValidateIrange,
        DB_CFG_FILE_SYNC_WRITE_ENABLE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"compressSpaceEnable", DB_CFG_SPACE_COMPRESS_AREA_ENABLE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_SPACE_COMPRESS_AREA_ENABLE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"compressSpaceSize", DB_CFG_SPACE_COMPRESS_AREA_SIZE_DESC, 4096, DB_MAX_INT32, "1048576", CfgParamValidateIrange,
        DB_CFG_SPACE_COMPRESS_AREA_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"bufferpoolMemType", DB_CFG_BUFFERPOOL_MEM_TYPE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_BUFFERPOOL_MEM_TYPE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    // checkpoint
    {"ckptPeriod", DB_CFG_CKPT_PERIOD_DESC, 1, 65535, "60", CfgParamValidateIrange, DB_CFG_CKPT_PERIOD,
        DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    // bufferPoolSize: [128, 1073741824] default 4096, pageSize: {4, 8, 16, 32, 64} default 32
    // bufferPoolSize_min_value/pageSize_max_value=2, bufferPoolSize_max_value/pageSize_min_value=268435456
    // bufferPoolSize_default_value/pageSize_default_value=128, 128/2=64
    {"ckptThreshold", DB_CFG_CKPT_THLD_DESC, 2, 268435456, "64", CfgParamValidateIrange, DB_CFG_CKPT_THLD,
        DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    // double write
    {"dwrEnable", DB_CFG_DOUBLE_WRITE_ENABLE_DESC, 0, 1, "1", CfgParamValidateIrange, DB_CFG_DWR_ENABLE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"dwrBlockNum", DB_CFG_DOUBLE_WRITE_BLOCK_NUM_DESC, 1, 128, "32", CfgParamValidateIrange, DB_CFG_DWR_BLOCK_NUM,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    // crc
    {"crcCheckEnable", DB_CFG_CRC_CHECK_ENABLE_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_CRC_CHECK_ENABLE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    // sha256
    {"shaCheckEnable", DB_CFG_SHA_CHECK_ENABLE_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_SHA_CHECK_ENABLE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    // 用户自定义算法和密钥
    {"tamperProofEnable", DB_CFG_TAMPER_PROOF_ENABLE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_TAMPER_PROOF_ENABLE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    // 用户将自定义摘要计算方法放到此路径
    {"tamperProofSoPath", DB_CFG_TAMPER_PROOF_SO_PATH_DESC, 0, 0, "", CfgParamValidateLogPath,
        DB_CFG_TAMPER_PROOF_SO_PATH, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    // 多区持久化
    {"multizonePersistNum", DB_CFG_MULTIZONE_NUM_DESC, 1, 2, "1", CfgParamValidateIrange, DB_CFG_MULTIZONE_NUM,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"recoveryZoneId", DB_CFG_RECOVERY_ZONE_ID_DESC, 1, 2, "1", CfgParamValidateIrange, DB_CFG_RECOVERY_ZONE_ID,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"persistentCompressMode", DB_CFG_PERSISTENT_COMPRESS_MODE_DESC, 0, 2, "0", CfgParamValidateIrange,
        DB_CFG_PERSISTENT_COMPRESS_MODE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"bufferPoolChunkSize", DB_CFG_SE_BUFFER_POOL_CHUNK_SIZE_DESC, 0, 4194304, "0", CfgParamValidateIrange,
        DB_CFG_SE_BUFFER_POOL_CHUNK_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
// Bufferpool
#if defined(IDS_HAOTIAN)
    // 数存 bufferPoolSize max size : 1T
    {"bufferPoolSize", DB_CFG_SE_BUFFERPOOL_SIZE_DESC, 128, 1073741824, "4096", CfgParamValidateIrange,
        DB_CFG_SE_BUFFERPOOL_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#else
    // 非数存 bufferPoolSize max size : 4GB - pageSize
    {"bufferPoolSize", DB_CFG_SE_BUFFERPOOL_SIZE_DESC, 128, 4194304, "4096", CfgParamValidateIrange,
        DB_CFG_SE_BUFFERPOOL_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
    {"bufferPoolPolicy", DB_CFG_SE_BUFFERPOOL_POLICY_DESC, BUF_RECYCLE_NORMAL, BUF_RECYCLE_VECTOR_INDEX, "0",
        CfgParamValidateIrange, DB_CFG_SE_BUFFERPOOL_POLICY, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#if defined(IDS_HAOTIAN)
    {"bufferPoolNum", DB_CFG_SE_BUFFERPOOL_NUM_DESC, 1, 32, "1", CfgParamValidateIrange, DB_CFG_SE_BUFFERPOOL_NUM,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"preFetchPagesEnable", DB_CFG_PRE_FETCH_PAGES_ENABLE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_PRE_FETCH_PAGES_ENABLE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"maxPreFetchThreNum", DB_CFG_MAX_PRE_FETCH_THRE_NUM_DESC, 1, 256, "8", CfgParamValidateIrange,
        DB_CFG_MAX_PRE_FETCH_THRE_NUM, DB_CFG_CHANGE_NO_LIMIT, DB_DATATYPE_INT32, NULL},
    {"loadTablePriorityRatio", DB_CFG_SE_BUFFERPOOL_HIGH_PRIORITY_RATIO_DESC, 0, 70, "30", CfgParamValidateIrange,
        DB_CFG_SE_BUFFERPOOL_HIGH_PRIORITY_RATIO, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"bufferPoolPriorityRatio", DB_CFG_SE_BUFFERPOOL_PRIORITY_RATIO_DESC, 0, 80, "40", CfgParamValidateIrange,
        DB_CFG_SE_BUFFERPOOL_PRIORITY_RATIO, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"enableConcurrentFetchPage", DB_CFG_ENABLE_CONCURRENT_FETCH_PAGE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_ENABLE_CONCURRENT_FETCH_PAGE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
    {"lockFilePath", DB_CFG_FILE_LOCK_PATH_DESC, 0, 0, "/run/verona", CfgParamValidateLogPath, DB_CFG_FILE_LOCK_PATH,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    {"directWrite", DB_CFG_DIRECT_WRITER_DESC, 0, 2, "0", CfgParamValidateIrange, DB_CFG_DIRECT_WRITE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"shareMemoryMapMode", DB_CFG_SHM_MAP_MODE_DESC, 0, 2, "0", CfgParamValidateIrange, DB_CFG_SHM_MAP_MODE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"shareMemorySpaceSize", DB_CFG_SHM_SPACE_SIZE_DESC, 40, 16384, "8192", CfgParamValidateIrange,
        DB_CFG_SHM_SPACE_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"enableShareMsgPool", DB_CFG_ENABLE_SHARE_MSG_POOL_DESC, 0, 1, "1", CfgParamValidateIrange,
        DB_CFG_ENABLE_SHARE_MSG_POOL, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"enableResilienceStat", DB_CFG_ENABLE_RESILIENCE_STAT_DESC, 0, 1, "1", CfgParamValidateIrange,
        DB_CFG_ENABLE_RESILIENCE_STAT, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#ifdef FEATURE_TS
    {"enableVectorizedPushDown", DB_CFG_ENABLE_VECTORIZED_PUSHDOWN_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_ENABLE_VECTORIZED_PUSHDOWN, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"isHighConcurrent", DB_CFG_IS_HIGH_CONCURRENCT_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_IS_HIGH_CONCURRENCT,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
    {"enableSignalRegister", DB_CFG_ENABLE_SIGNAL_REGISTER_DESC, 0, 1, "1", CfgParamValidateIrange,
        DB_CFG_ENABLE_SIGNAL_REGISTER, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"enableReleaseDevice", DB_CFG_ENABLE_RELEASE_DEVICE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_ENABLE_RELEASE_DEVICE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"isUseRsm", DB_CFG_IS_USE_RSM_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_IS_USE_RSM,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"RsmBlockSize", DB_CFG_RSM_BLOCK_MAX_SIZE_DESC, 1, 128, "128", CfgParamValidateIrange, DB_CFG_RSM_BLOCK_MAX_SIZE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"RsmKeyRange", DB_CFG_RSM_KEY_RANGE_DESC, 0, 0, "0,9", CfgParamRsmRange, DB_CFG_RSM_KEY_RANGE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    {"RsmExtendBlockSize", DB_CFG_RSM_EXTEND_BLOCK_SIZE_DESC, 0, 512, "0", CfgParamValidateIrange,
        DB_CFG_RSM_EXTEND_BLOCK_SIZE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#ifdef WARM_REBOOT
    {"periodicPersistence", DB_PERIODIC_PERSISTENCE_DESC, 0, 3600, "0", NULL, DB_PERIODIC_PERSISTENCE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"periodicPersistenceDirPath", DB_PERIODIC_PERSISTENCE_DIRPATH_DESC, 0, 0, "PcPersistenceDir/", NULL,
        DB_PERIODIC_PERSISTENCE_DIRPATH, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
#endif
    {"clientHeartbeatInterval", DB_CFG_CLIENT_HEARTBEAT_INTERVAL_DESC, 5, 60, "5", CfgParamValidateIrange,
        DB_CFG_CLIENT_HEARTBEAT_INTERVAL, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    // 非mini 场景下ctrl page不使用紧密排布
    {"condensedCtrlPages", DB_CFG_CONDENSED_CTRL_PAGES_DESC, 0, 0, "0", CfgParamValidateIrange,
        DB_CFG_CONDENSED_CTRL_PAGES, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"multiHashBucketCount", DB_CFG_MULTI_HASH_BUCKET_CNT_DESC, 1, 10000000, "16384", CfgParamValidateIrange,
        DB_CFG_MULTI_HASH_BUCKET_CNT, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"hacMode", DB_CFG_HAC_MODE_DESC, 0, 2, "0", CfgParamValidateIrange, DB_CFG_HAC_MODE, DB_CFG_CHANGE_NOT_ALLOWED,
        DB_DATATYPE_INT32, NULL},
#ifdef FEATURE_GQL
    {"fastRecordPerFetchNum", DB_CFG_FAST_RECORD_PER_FETCH_DESC, 1, 65535, "100", CfgParamValidateIrange,
        DB_CFG_FAST_RECORD_PER_FETCH, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"slowRecordPerFetchNum", DB_CFG_SLOW_RECORD_PER_FETCH_DESC, 1, 65535, "100", CfgParamValidateIrange,
        DB_CFG_SLOW_RECORD_PER_FETCH, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"slowTimeInterval", DB_CFG_SLOW_TIME_INTERVAL_DESC, 200, INT32_MAX, "1000", CfgParamValidateIrange,
        DB_CFG_SLOW_TIME_INTERVAL, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
    {"yangAutoIndex", DB_CFG_YANG_AUTO_INDEX_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_YANG_AUTO_INDEX,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"yangMergeXPath", DB_CFG_YANG_MERGE_XPATH_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_YANG_MERGE_XPATH,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"yangUpgradeDirPath", DB_CFG_YANG_UPGRADE_DIR_PATH_DESC, 0, 0, "/opt/vrpv8/home/<USER>/grade",
        CfgParamValidateLogPath, DB_CFG_YANG_UPGRADE_DIR_PATH, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
    {"enableChannelReuse", DB_CFG_ENABLE_CHANNEL_REUSE_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_ENABLE_CHANNEL_REUSE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"enableUploadHungResult", DB_CFG_ENABLE_UPLOAD_HUNG_RESULT_DESC, 0, 1, "0", CfgParamValidateIrange,
        DB_CFG_ENABLE_UPLOAD_HUNG_RESULT, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"chSlotPerBucket", DB_CFG_CH_SLOT_PER_BUCKET_DESC, 2, 255, "2", CfgParamValidateIrange, DB_CFG_CH_SLOT_PER_BUCKET,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
    {"chSlotPerConflictNode", DB_CFG_CH_SLOT_PER_CONFLICT_NODE_DESC, 2, 65535, "8", CfgParamValidateIrange,
        DB_CFG_CH_SLOT_PER_CONFLICT_NODE, DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#ifdef HPE
    {"hpeAddShmemAcl", DB_CFG_HPE_ADD_SHMEM_ACL_DESC, 0, 0, "", NULL, DB_CFG_HPE_ADD_SHMEM_ACL,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_STRING, NULL},
#endif
#ifdef FEATURE_REPLICATION
    {"enableReplication", DB_CFG_ENABLE_REPLICATION_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_ENABLE_REPLICATION,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
#ifdef EXPERIMENTAL_GUANGQI
    {"enableTrxClone", DB_CFG_ENABLE_TRX_CLONE_DESC, 0, 1, "0", CfgParamValidateIrange, DB_CFG_ENABLE_TRX_CLONE,
        DB_CFG_CHANGE_NOT_ALLOWED, DB_DATATYPE_INT32, NULL},
#endif
}; /* for coding safe */

static void DbCfgChangeAuditLogNotifyFunc(
    const char *configName, const DbCfgValueT *oldValue, DbCfgValueT *newValue, DbInstanceHdT dbInstance)
{
    DB_UNUSED(dbInstance);
    if (strcmp(configName, "auditLogEnableDCL") == 0) {
        (void)DbAuditSetEvtSwitch(DB_AUDIT_DCL, (bool)newValue->int32Val);
    } else if (strcmp(configName, "auditLogEnableDDL") == 0) {
        (void)DbAuditSetEvtSwitch(DB_AUDIT_DDL, (bool)newValue->int32Val);
    } else if (strcmp(configName, "auditLogEnableDML") == 0) {
        (void)DbAuditSetEvtSwitch(DB_AUDIT_DML, (bool)newValue->int32Val);
    } else if (strcmp(configName, "auditLogEnableDQL") == 0) {
        (void)DbAuditSetEvtSwitch(DB_AUDIT_DQL, (bool)newValue->int32Val);
    }
}

static void DbCfgUpdMemPeakDfxThreshold(
    const char *configName, const DbCfgValueT *oldValue, DbCfgValueT *newValue, DbInstanceHdT dbInstance)
{
    DB_UNUSED(dbInstance);
    DbCfgEmItemIdE id = DbCfgGetIdByName(configName);
    DB_ASSERT(id == DB_CFG_MEM_PEAK_DFX_THREASHOLD);
    DbUpdMemPeakDfxThresholdByCfg(newValue->int32Val);
}

static void DbCfgUpdMemtraceDynCtxIds(
    const char *configName, const DbCfgValueT *oldValue, DbCfgValueT *newValue, DbInstanceHdT dbInstance)
{
    DB_UNUSED(dbInstance);
    DB_POINTER3(configName, oldValue, newValue);
    DbCfgEmItemIdE id = DbCfgGetIdByName(configName);
    DB_ASSERT(id == DB_CFG_MEMTRACE_DYN_CTXIDS);
    DbDynMemEnableMemtraceByCtxIds(newValue->str);
}

static void DbCfgValueSetInt32(DbCfgValueT *value, int32_t intVal)
{
    DB_POINTER(value);

    value->type = DB_DATATYPE_INT32;
    value->int32Val = intVal;
}

static void DbCfgValueSetString(DbCfgValueT *value, const char *str, uint32_t size)
{
    DB_POINTER2(value, str);

    value->type = DB_DATATYPE_STRING;
    errno_t err = strncpy_s(value->str, sizeof(value->str), str, size);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Copy db cfg value");
    }
}

Status DbCfgValueCpy(const DbCfgValueT *src, DbCfgValueT *tgt)
{
    DB_POINTER2(src, tgt);
    tgt->type = src->type;

    if (src->type == DB_DATATYPE_INT32) {
        tgt->int32Val = src->int32Val;
        return GMERR_OK;
    }

    errno_t ret = strcpy_s(tgt->str, sizeof(tgt->str), src->str);
    if (ret != EOK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status DbCfgItemGetDefaultValue(DbCfgValueT *value, const DbCfgItemDescT *desc)
{
    DB_POINTER2(value, desc);
    Status ret = GMERR_OK;
    if (desc->type == DB_DATATYPE_INT32) {
        int32_t defaultValue = 0;
        ret = DbStrToInt32(desc->defaultValue, &defaultValue);
        if (ret != GMERR_OK) {
            return ret;
        }
        DbCfgValueSetInt32(value, defaultValue);
    } else {
        DbCfgValueSetString(value, desc->defaultValue, (uint32_t)strlen(desc->defaultValue));
    }
    return ret;
}

Status DbCfgRegisterNofityFunc(DbCfgEmItemIdE id, DbCfgChangeNotifyFuncT func, DbInstanceHdT dbInstance)
{
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(dbInstance);
    DbCfgMgrT *mgr = (DbCfgMgrT *)cfgHandle;
    if ((uint32_t)id >= mgr->cfgItemNum) {
        return GMERR_DATA_EXCEPTION;
    }

    DbRWSpinWLock(&mgr->cfgModifyLock);
    DbCfgItemDescT *desc = (DbCfgItemDescT *)&g_dbCfgItemDescs[id];
    // 限制如果func为空，或者已经配置notify接口，则不予注册
    if (desc == NULL || desc->notifyFunc != NULL) {
        DB_LOG_DEBUG_WARN_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
            "|COM| config |%d| notify already register, desc is %s null.", (uint32_t)id, desc ? "not" : "");
        DbRWSpinWUnlock(&mgr->cfgModifyLock);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    desc->notifyFunc = func;
    DbRWSpinWUnlock(&mgr->cfgModifyLock);
    return GMERR_OK;
}

Status DbCfgCreateMgr(DbCfgMgrHandleT *cfgHandle)
{
    DB_POINTER(cfgHandle);
    Status ret = GMERR_OK;

    if (!DbIsMultiInstanceEnabled()) {
        (void)memset_s(*cfgHandle, sizeof(DbCfgMgrT), 0, sizeof(DbCfgMgrT));
    }

    DbRWSpinInit(&(*cfgHandle)->cfgModifyLock);
    for (uint32_t n = 0; n < (uint32_t)DB_CFG_EM_ITEM_BUTT; n++) {
        DbCfgItemT *cfgItem = &(*cfgHandle)->cfgItems[n];
        cfgItem->id = (uint8_t)n;
        cfgItem->isSet = false;
        ret = DbCfgItemGetDefaultValue(&cfgItem->value, &g_dbCfgItemDescs[n]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    (*cfgHandle)->cfgItemNum = DB_CFG_EM_ITEM_BUTT;
    return ret;
}

bool DbCfgItemEquals(DbCfgItemT *item1, DbCfgItemT *item2)
{
    DB_POINTER2(item1, item2);
    if (!(item1->isSet == item2->isSet && item1->id == item2->id && item1->value.type == item2->value.type)) {
        goto EXIT;
    }

    if (item1->value.type == DB_DATATYPE_STRING) {
        if (DbStrCmp((const char *)item1->value.str, (const char *)item2->value.str, false) != 0) {
            goto EXIT;
        }
        return true;
    }

    if (item1->value.uint64Val != item2->value.uint64Val) {
        goto EXIT;
    }
    return true;
EXIT:
    DB_LOG_ERROR(
        GMERR_CONFIG_ERROR, "new cfg (cfgName: %s) not equals with the old one.", g_dbCfgItemDescs[item1->id].name);
    return false;
}

bool DbCfgEquals(DbCfgMgrT *oldMgr, DbCfgMgrT *newMgr)
{
    if (oldMgr == NULL || newMgr == NULL) {
        DB_LOG_WARN(GMERR_INVALID_PARAMETER_VALUE, "mgr1 or mgr2 NULL");
        return oldMgr == newMgr;
    }
    if (oldMgr->cfgItemNum != newMgr->cfgItemNum) {
        return false;
    }
    for (uint32_t i = 0; i < oldMgr->cfgItemNum; i++) {
        if (!DbCfgItemEquals(&oldMgr->cfgItems[i], &newMgr->cfgItems[i])) {
            return false;
        }
    }
    return true;
}

Status DbCfgCheckOneItemIsValue(DbCfgEmItemIdE id, const DbCfgValueT *oldValue, const DbCfgValueT *newValue)
{
    DB_POINTER2(oldValue, newValue);
    if (g_dbCfgItemDescs[id].name == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NO_DATA, "Null g_dbCfgItemDescs name");
        return GMERR_NO_DATA;
    }
    switch (g_dbCfgItemDescs[id].changeMode) {
        case DB_CFG_CHANGE_NOT_ALLOWED:
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "db cfg is unchangeable.");
            return GMERR_DATA_EXCEPTION;
        case DB_CFG_CHANGE_MORE:
            if (oldValue->type == DB_DATATYPE_INT32 && oldValue->int32Val > newValue->int32Val) {
                DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
                    "Set new value which less litters, oldValue=%" PRIi32 ", newValue=%" PRIi32 ".", oldValue->int32Val,
                    newValue->int32Val);
                return GMERR_DATA_EXCEPTION;
            }
            break;
        case DB_CFG_CHANGE_LESS:
            if (oldValue->type == DB_DATATYPE_INT32 && oldValue->int32Val < newValue->int32Val) {
                DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
                    "Set bigger value, oldValue=%" PRIi32 ", newValue=%" PRIi32 ".", oldValue->int32Val,
                    newValue->int32Val);
                return GMERR_DATA_EXCEPTION;
            }
            break;
        case DB_CFG_CHANGE_NO_LIMIT:
            /* Do nothing. */
            break;
        default:
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
                "Old int32val doesn't match, id=%" PRIi32 ", mode=%" PRIi32 ".", (int32_t)id,
                (int32_t)g_dbCfgItemDescs[id].changeMode);
            return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status DbCfgSetInner(
    DbCfgMgrT *mgr, DbCfgEmItemIdE id, const DbCfgValueT *newValue, bool checkExist, DbCfgValueT *oldValue)
{
    const DbCfgItemDescT *desc = &g_dbCfgItemDescs[id];
    if (newValue->type != desc->type) {
        DbPrintToStdout("DbCfgSetInner type not matched,  valueType: %" PRId32 ", descType: %" PRId32 "\n",
            (int32_t)newValue->type, (int32_t)desc->type);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
            "db cfg type doesn't match, valueType: %" PRId32 ", descType: %" PRId32 ".", (int32_t)newValue->type,
            (int32_t)desc->type);
        return GMERR_DATA_EXCEPTION;
    }
    if (desc->validate != NULL && desc->validate(desc, newValue) != GMERR_OK) {
        return GMERR_DATA_EXCEPTION;
    }
    DbCfgValueT *tgt = &mgr->cfgItems[id].value;
    Status ret;
    if (checkExist) {
        ret = DbCfgCheckOneItemIsValue(id, tgt, newValue);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    ret = DbCfgValueCpy(tgt, oldValue);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DbCfgValueCpy(newValue, tgt);
    if (ret != GMERR_OK) {
        return ret;
    }
    mgr->cfgItems[id].isSet = true;

    return GMERR_OK;
}

Status DbGetCltCfgIdByDescId(uint32_t descId, TagCltCfgEmItemIdE *cltCfgId, const DbCfgMgrT *mgr)
{
    for (uint32_t i = 0; i < (uint32_t)CLT_CFG_EM_ITEM_BUTT; i++) {
        if (mgr->cfgForClient[i].id == descId) {
            *cltCfgId = i;
            return GMERR_OK;
        }
    }
    DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "get inv config id, type: %" PRIu32 ".", (uint32_t)descId);
    return GMERR_DATA_EXCEPTION;
}

Status DbSetClientCfg(DbCfgEmItemIdE id, DbCfgMgrT *mgr, DbCfgValueT *value)
{
    if (!IsCfgForClient(id)) {
        return GMERR_OK;
    }

    TagCltCfgEmItemIdE cltCfgId = CLT_CFG_EM_ITEM_BUTT;
    Status ret = DbGetCltCfgIdByDescId(id, &cltCfgId, mgr);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbCfgValueCpy(value, &mgr->cfgForClient[cltCfgId].value);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "update client config, type: %" PRIu32 ".", (uint32_t)id);
        return ret;
    }
    mgr->cfgForClient[cltCfgId].isSet = true;
    return GMERR_OK;
}

typedef struct CfgSetParam {
    bool checkExist;
    bool needNotify;
} CfgSetParamT;

static Status DbCfgSetAndNotify(
    DbCfgMgrT *mgr, DbCfgEmItemIdE id, DbCfgValueT *value, CfgSetParamT *cfgSetParam, DbInstanceHdT dbInstance)
{
    DbCfgValueT oldValue;
    DbRWSpinWLock(&mgr->cfgModifyLock);
#ifndef DIRECT_WRITE
    // 直连写配置项防呆
    switch (id) {
        case DB_CFG_DIRECT_WRITE:
        case DB_CFG_SHM_MAP_MODE:
            value->int32Val = 0;
            break;
        default:
            break;
    }
#endif
    Status ret = DbCfgSetInner(mgr, id, value, cfgSetParam->checkExist, &oldValue);
    if ((Status)ret != GMERR_OK) {
        DbRWSpinWUnlock(&mgr->cfgModifyLock);
        return ret;
    }

    if (cfgSetParam->needNotify) {
        ret = DbSetClientCfg(id, mgr, value);
        if ((Status)ret != GMERR_OK) {
            DbPrintToStdout("set client cfg unsucc, id:%" PRIu32 "\n", (uint32_t)id);
            DbRWSpinWUnlock(&mgr->cfgModifyLock);
            return ret;
        }
    }

    const DbCfgItemDescT *desc = &g_dbCfgItemDescs[id];
    if (cfgSetParam->needNotify && desc->notifyFunc != NULL) {
        desc->notifyFunc(desc->name, &oldValue, value, dbInstance);
    }
    DbRWSpinWUnlock(&mgr->cfgModifyLock);
    return GMERR_OK;
}

Status DbCfgSet(DbCfgMgrHandleT cfgHandle, DbCfgEmItemIdE id, DbCfgValueT *value, bool checkExist, bool needNotify,
    DbInstanceHdT dbInstance)
{
    if (cfgHandle == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NO_DATA, "db cfg handle is null");
        return GMERR_NO_DATA;
    }
    if (value == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NO_DATA, "db cfg value is null");
        return GMERR_NO_DATA;
    }
    DbCfgMgrT *mgr = (DbCfgMgrT *)cfgHandle;
    if ((uint32_t)id >= mgr->cfgItemNum) {
        DbPrintToStdout("id:%" PRIu32 " too big, cfgItemNum:%" PRIu32 "\n", (uint32_t)id, mgr->cfgItemNum);
        return GMERR_DATA_EXCEPTION;
    }

#ifdef FEATURE_SIMPLEREL  // v1此两项配置均仅使用默认配置
    if (id == DB_CFG_USER_POLICY_MODE || id == DB_CFG_LOCAL_LOCATOR) {
        return GMERR_OK;
    }
#endif
    CfgSetParamT cfgSetParam = {.checkExist = checkExist, .needNotify = needNotify};
    return DbCfgSetAndNotify(mgr, id, value, &cfgSetParam, dbInstance);
}

const DbCfgItemDescT *DbCfgDescById(DbCfgEmItemIdE id)
{
    if (id >= DB_CFG_EM_ITEM_BUTT) {
        return NULL;
    }
    return &g_dbCfgItemDescs[id];
}

DbCfgEmItemIdE DbCfgGetIdByName(const char *configName)
{
    DB_POINTER(configName);
    uint32_t id;
    for (id = (uint32_t)DB_CFG_LPM4_VRID_MAX; id < (uint32_t)DB_CFG_EM_ITEM_BUTT; id++) {
        if (DbStrCmp(g_dbCfgItemDescs[id].name, configName, false) == 0) {
            return (DbCfgEmItemIdE)id;
        }
    }
    return DB_CFG_EM_ITEM_BUTT;
}

Status DbCfgGetByName(const char *configName, const DbCfgItemDescT **desc, DbCfgValueT *value, DbInstanceHdT dbInstance)
{
    DB_POINTER3(configName, desc, value);
    DbCfgEmItemIdE id = DbCfgGetIdByName(configName);
    if (id == DB_CFG_EM_ITEM_BUTT) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SYNTAX_ERROR, "config name %s.", configName);
        return GMERR_SYNTAX_ERROR;
    }

    *desc = &g_dbCfgItemDescs[id];
    DbCfgMgrHandleT handle = DbGetCfgHandle(dbInstance);
    return DbCfgGet(handle, id, value);
}

Status DbCfgSetByNameInner(
    DbCfgMgrHandleT handle, const char *configName, const char *configValue, bool checkExist, bool needNotify)
{
    DB_POINTER3(handle, configName, configValue);

    DbCfgEmItemIdE id = DbCfgGetIdByName(configName);
    if (id == DB_CFG_EM_ITEM_BUTT) {
        DbPrintToStdout("unable to get config name %s\n", configName);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SYNTAX_ERROR, "config name %s.", configName);
        return GMERR_SYNTAX_ERROR;
    }
    const DbCfgItemDescT *desc = &g_dbCfgItemDescs[id];
    DbCfgValueT tmpValue;
    tmpValue.type = desc->type;

    if (desc->type == DB_DATATYPE_STRING) {
        errno_t err = strcpy_s(tmpValue.str, DB_CFG_PARAM_MAX_STRING, configValue);
        if (err != EOK) {
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    } else {
        if (!DbCheckDigit(configValue)) {
            DbPrintToStdout("Config %s check digit %s unsucc\n", configName, configValue);
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Config %s digit, value=%s.", configName, configValue);
            return GMERR_DATA_EXCEPTION;
        }

        int64_t cfgValueTmp;
        Status ret = DbStrToInt64(configValue, &cfgValueTmp);
        if (ret != GMERR_OK) {
            DbPrintToStdout("Config %s str to int64 %s unsucc\n", configName, configValue);
            return ret;
        }
        if (cfgValueTmp > desc->max || cfgValueTmp < desc->min) {
            DbPrintToStdout("Config %s is not in proper range, value=%" PRIi64 ", min=%" PRIi64 ", max=%" PRIi64 "\n",
                configName, cfgValueTmp, desc->min, desc->max);
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
                "Config %s int32 value is not in the proper range, value=%" PRIi64 ", min=%" PRIi64 ", max=%" PRIi64,
                configName, cfgValueTmp, desc->min, desc->max);
            return GMERR_DATA_EXCEPTION;
        }

        ret = DbStrToInt32(configValue, &tmpValue.int32Val);
        if (ret != GMERR_OK) {
            DbPrintToStdout("Config %s str to int32 %s unsucc\n", configName, configValue);
            return ret;
        }
    }

    return DbCfgSet(handle, id, &tmpValue, checkExist, needNotify, NULL);
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
Status DbCfgSetDbCfgValue(const DbValueT *dbValue, DbCfgValueT *configValue)
{
    DB_POINTER2(dbValue, configValue);
    if (!DbCfgIsSupportType(dbValue->type)) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_FEATURE_NOT_SUPPORTED, "Config value type, type=%" PRIi32 ".", (int32_t)dbValue->type);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    configValue->type = dbValue->type;
    if (dbValue->type == DB_DATATYPE_STRING) {
        errno_t ret =
            strncpy_s(configValue->str, DB_CFG_PARAM_MAX_STRING, dbValue->value.strAddr, dbValue->value.length);
        if (ret != EOK) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_MEMORY_OPERATE_FAILED, "Copy config string, length=%" PRIu32 ".", dbValue->value.length);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    } else {
        configValue->int32Val = dbValue->value.intValue;
    }

    return GMERR_OK;
}

Status DbCfgSetDbValue(DbMemCtxT *memCtx, const DbCfgValueT *configValue, DbValueT *dbValue)
{
    DB_POINTER4(memCtx, dbValue, configValue, dbValue);
    dbValue->type = configValue->type;
    if (dbValue->type == DB_DATATYPE_STRING) {
        uint32_t srcLen = (uint32_t)strlen(configValue->str);
        dbValue->value.length = srcLen + 1u;
        // 内存释放点:调用者释放
        void *strAddr = DbDynMemCtxAlloc(memCtx,
            dbValue->value.length);  // 目前只有QryExecutePutDbCfgValue调用，通过stmt内存上下文统一释放。
        if (strAddr == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Alloc memctx.");
            return GMERR_OUT_OF_MEMORY;
        }
        errno_t ret = strncpy_s((char *)strAddr, dbValue->value.length, configValue->str, srcLen);
        if (ret != EOK) {
            DbDynMemCtxFree(memCtx, strAddr);
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "Copy config string.");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        dbValue->value.strAddr = strAddr;
    } else {
        dbValue->value.intValue = configValue->int32Val;
    }
    return GMERR_OK;
}

Status DbCfgSetByName(const char *configName, DbCfgValueT *configValue, bool needNotify, DbInstanceHdT dbInstance)
{
    DB_POINTER2(configName, configValue);
    DbCfgEmItemIdE id = DbCfgGetIdByName(configName);
    if (id == DB_CFG_EM_ITEM_BUTT) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SYNTAX_ERROR, "config name %s.", configName);
        return GMERR_SYNTAX_ERROR;
    }
    DbCfgMgrHandleT handle = DbGetCfgHandle(dbInstance);
    return DbCfgSet(handle, id, configValue, (bool)true, needNotify, dbInstance);
}

bool DbCfgValueIsEqual(const DbCfgValueT *oldValue, const DbCfgValueT *newValue)
{
    DB_POINTER2(oldValue, newValue);
    if (oldValue->type != newValue->type) {
        return false;
    }
    if (oldValue->type == DB_DATATYPE_INT32) {
        return oldValue->int32Val == newValue->int32Val;
    } else {
        return (DbStrCmp(oldValue->str, newValue->str, false) == 0);
    }
}

// 客户端配置项
#define MIN_CATALOG_LABEL_NUM 5
#define MAX_CATALOG_LABEL_NUM 50

// 并发方案：不涉及并发，资源只读
static DbCltCfgItemDescT g_dbCltCfgItemDescs[(int32_t)DB_CLT_CFG_ITEM_BUTT] = {
    // { Name,                    Description info,   MinValue, MaxValue, type, DefaultValue, CurrentValue,
    // ID,                        ChangeMode,         isSet}
    {"clientSecurityCheckMode", 0, 1, DB_DATATYPE_INT32, 1, 1, DB_CLT_CFG_SECURITY_CHECK_MODE, DB_CFG_CHANGE_NO_LIMIT,
        false},
    {"clientQryStatisticEnable", 0, 1, DB_DATATYPE_INT32, 0, 0, DB_CLT_CFG_QRY_STATISTIC_ENABLE, DB_CFG_CHANGE_NO_LIMIT,
        false},
    {"clientCataLimitEnable", MIN_CATALOG_LABEL_NUM, MAX_CATALOG_LABEL_NUM, DB_DATATYPE_INT32, 0, 0,
        DB_CLT_CFG_CLT_LIMIT_ENABLE, DB_CFG_CHANGE_NO_LIMIT, false},
    {"clientLiteDynMemModeEnable", 0, 1, DB_DATATYPE_INT32, 0, 0, DB_CLT_CFG_LITE_DYNMEM_MOD_ENABLE,
        DB_CFG_CHANGE_NO_LIMIT, false}};

DbCltCfgEmItemIdE DbCltCfgGetIdByName(const char *configName)
{
    DB_POINTER(configName);
    uint32_t id;
    for (id = (uint32_t)DB_CLT_CFG_SECURITY_CHECK_MODE; id < (uint32_t)DB_CLT_CFG_ITEM_BUTT; id++) {
        if (DbStrCmp(g_dbCltCfgItemDescs[id].name, configName, false) == 0) {
            return (DbCltCfgEmItemIdE)id;
        }
    }
    return DB_CLT_CFG_ITEM_BUTT;
}

DbDataTypeE DbCltCfgGetTypeById(DbCltCfgEmItemIdE id)
{
    if (id >= DB_CLT_CFG_ITEM_BUTT) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Config id is unsound, id=%" PRIi32 ".", (int32_t)id);
        return DB_DATATYPE_NULL;
    }
    DbCltCfgItemDescT *desc = &g_dbCltCfgItemDescs[id];
    return desc->type;
}

Status DbCltCfgValueIsValid(DbCltCfgEmItemIdE id, const DbCfgValueT *newValue, const DbCfgValueT *oldValue)
{
    DbDataTypeE valueType = newValue->type;
    if (valueType != DB_DATATYPE_INT32) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED,
            "clt config value type is unsound, valueType=%" PRIi32 ".", (int32_t)valueType);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    DbCfgChangeModeE changeMode = g_dbCltCfgItemDescs[id].changeMode;
    int64_t min = g_dbCltCfgItemDescs[id].min;
    int64_t max = g_dbCltCfgItemDescs[id].max;
    if (newValue->int32Val < min || newValue->int32Val > max) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
            "clt cfg is out of range, value=%" PRIi32 ", min=%" PRIi64 ", max=%" PRIi64 ".", newValue->int32Val, min,
            max);
        return GMERR_DATA_EXCEPTION;
    }
    switch (changeMode) {
        case DB_CFG_CHANGE_NOT_ALLOWED:
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "not allowed to set the clt cfg.");
            return GMERR_DATA_EXCEPTION;
        case DB_CFG_CHANGE_MORE:
            if (oldValue->int32Val > newValue->int32Val) {
                DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
                    "Not allowed to set new clt cfgValue which litter than the old, oldValue=%" PRIi32
                    ", newValue=%" PRIi32 ".",
                    oldValue->int32Val, newValue->int32Val);
                return GMERR_DATA_EXCEPTION;
            }
            break;
        case DB_CFG_CHANGE_LESS:
            if (oldValue->int32Val < newValue->int32Val) {
                DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
                    "not allowed to set new clt cfgValue which bigger than the old, oldValue=%" PRIi32
                    ", newValue=%" PRIi32 ".",
                    oldValue->int32Val, newValue->int32Val);
                return GMERR_DATA_EXCEPTION;
            }
            break;
        case DB_CFG_CHANGE_NO_LIMIT:
            break;
        default:
            break;
    }
    return GMERR_OK;
}

Status DbCltCfgSetByName(const char *configName, DbCfgValueT *configValue)
{
    DB_POINTER2(configName, configValue);
    DbCltCfgEmItemIdE id = DbCltCfgGetIdByName(configName);
    if (id == DB_CLT_CFG_ITEM_BUTT) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SYNTAX_ERROR, "clt config name |%s| unsound.", configName);
        return GMERR_SYNTAX_ERROR;
    }

    DbCltCfgItemDescT *desc = &g_dbCltCfgItemDescs[id];
    DbCfgValueT oldValue = {};
    oldValue.type = desc->type;
    if (desc->isSet) {
        oldValue.int64Val = desc->currentValue;
    } else {
        oldValue.int64Val = desc->defaultValue;
    }

    Status ret = DbCltCfgValueIsValid(id, configValue, &oldValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    desc->currentValue = configValue->int32Val;
    desc->isSet = true;
    return ret;
}

void DbCltCfgSetDefaultByName(const char *configName)
{
    DB_POINTER(configName);
    DbCltCfgEmItemIdE id = DbCltCfgGetIdByName(configName);
    if (id == DB_CLT_CFG_ITEM_BUTT) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_SYNTAX_ERROR, "clt config name |%s| unsound.", configName);
        return;
    }

    DbCltCfgItemDescT *desc = &g_dbCltCfgItemDescs[id];

    desc->currentValue = desc->defaultValue;
    desc->isSet = false;
    return;
}

Status DbCltCfgGetById(DbCltCfgEmItemIdE id, DbCfgValueT *configValue)
{
    DB_POINTER(configValue);
    if (id >= DB_CLT_CFG_ITEM_BUTT) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Config id is unsound, id=%" PRIi32 ".", (int32_t)id);
        return GMERR_DATA_EXCEPTION;
    }
    DbCltCfgItemDescT *desc = &g_dbCltCfgItemDescs[id];
    configValue->type = desc->type;
    if (desc->isSet) {
        configValue->int64Val = desc->currentValue;
    } else {
        configValue->int64Val = desc->defaultValue;
    }
    // 性能优化快速路径
    if (SECUREC_LIKELY(desc->type == DB_DATATYPE_INT32)) {
        return GMERR_OK;
    }
    DbCfgValueT tgt = *configValue;
    return DbCfgValueCpy(&tgt, configValue);
}

Status DbCfgVerifyUserPolicyStrongMode(void)
{
    int32_t userPolicyMode = DbCfgGetInt32Lite(DB_CFG_USER_POLICY_MODE, NULL);
    // 2表示强authentication模式
    if (userPolicyMode != 2) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "unsound authentication mode: %" PRId32, userPolicyMode);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

void DbCfgGetFlowCtrlTimeInterval(uint32_t *array, uint32_t arrayLen)
{
    char *nextContext = NULL;

    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    DbCfgValueT cfgValue;
    Status ret = DbCfgGet(cfgHandle, DB_CFG_FLOW_CONTROL_SLEEP_TIME, &cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "config getting for flow control sleep string unsucc.");
        return;
    }

    char *pCurrent = cfgValue.str;
    uint32_t i = 0;

    while (pCurrent != NULL && i < arrayLen) {
        pCurrent = strtok_s(pCurrent, ",", &nextContext);
        if (pCurrent == NULL) {
            break;
        }

        ret = DbStrToUint32(pCurrent, &array[i++]);
        if (ret != GMERR_OK) {
            return;
        }
        pCurrent = nextContext;
    }
}
#endif /* FEATURE_SIMPLEREL */

#if defined(FEATURE_TS) || defined(TS_MULTI_INST) || defined(FEATURE_SQL)
Status DbCfgGetTempFileMaxSize(uint64_t *maxSize, DbInstanceHdT dbInstance)
{
    DbCfgMgrHandleT handle = DbGetCfgHandle(dbInstance);
    DbCfgValueT cfgValue;
    Status ret = DbCfgGet(handle, DB_CFG_RESULT_TEMP_FILE_MAX_SIZE, &cfgValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GetTempFileMaxSize(g_dbCfgItemDescs, &cfgValue, maxSize);
}
#endif

#ifdef __cplusplus
}
#endif /* __cplusplus */
