/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementation of validate for yang
 * Author: yuxinxin
 * Create: 2022-06-15
 */

#include "db_file.h"
#include "db_timer.h"
#include "db_utils.h"
#include "db_endian_trans.h"
#include "db_file_util.h"
#include "db_v1_file_util.h"

#ifdef __cplusplus
extern "C" {
#endif

#define V1IMP_NODE_USED 0xfffffffe
#define V1IMP_TABLE_SYSTEM_FIELD_SIZE 4u
#define V1DBKNL_NULL_OFFSET 0xFFFFFFFF
#define V1DBKNL_BYTE 8
#define V1DBKNL_CRC16_ENTRYVALUE 0xaaaa
#define V1DBKNL_SHIFT_ONEBITS 1
#define V1DB_CRC16_TBL_SIZE 256
#define V1DB_CRC_POLY_16 0x1021
#define V1DB_CRC_16 16
#define V1DB_BITMASK(X) (1L << (X))
#define V1DB_WIDTHMASK(X) (((uint32_t)(((uint32_t)(1L) << ((X)-1)) - 1L) << 1) | 1L)
#define V1DB_LITTLE_ENDIAN 1

/* Enum defining optional Managers type */
typedef enum TagDbDirType {
    DB_DIR_VARSEG_REL = 0,   /* Rel Specific Dir Entry */
    DB_DIR_VARSEG_DB = 1,    /* DB Specific Dir Entry */
    DB_DIR_REL_CHECKSUM = 2, /* Opt Manager type: Rel Checksum */
    DB_DIR_TYPE_BUTT = 0xFF
} DbDirTypeE;

/* Structure format in which the variable field length is interpretated.      */
typedef struct TagDbVariableDataType {
    uint32_t ulPageID;    /* ID of the page containing the data */
    uint32_t ulRowDirIdx; /* Index of the Row directory within the page. */
} DbVariableDataTypeT;

static inline ImpV1DbPageMgrT *DbPgMgrGet(const V5DbddlReldestblStru *pstRelDesc)
{
    return ((ImpV1DbTblFeatureMgrT *)(pstRelDesc->pTblRegFeatures.pPtr))->apRegFeatures[DB_PG_MGR_PTR];
}

/* Sets the Page Manager from Relation Descriptor feature list */
static inline void DbPgMgrSet(V5DbddlReldestblStru *pstRelDesc, ImpV1DbPageMgrT *pstPgMgr)
{
    ((ImpV1DbTblFeatureMgrT *)(pstRelDesc->pTblRegFeatures.pPtr))->apRegFeatures[DB_PG_MGR_PTR] = pstPgMgr;
}

/* Gets the Optional Manager from Relation Descriptor feature list */
static inline ImpV1DbRelDirT *DbOptMgrGet(const V5DbddlReldestblStru *pstRelDesc)
{
    return ((ImpV1DbTblFeatureMgrT *)(pstRelDesc->pTblRegFeatures.pPtr))->apRegFeatures[DB_OPT_MGR_PTR];
}

static inline void DbOptMgrSet(const V5DbddlReldestblStru *pstRelDesc, ImpV1DbRelDirT *relOptMgr)
{
    ((ImpV1DbTblFeatureMgrT *)(pstRelDesc->pTblRegFeatures.pPtr))->apRegFeatures[DB_OPT_MGR_PTR] = relOptMgr;
}

void DbVarSegFreeMapList(DbMemCtxT *memCtx, ImpV1DbVarSegMapListT **ppstVarMapList)
{
    ImpV1DbVarSegMapListT *pstTmpVarMapList = NULL;
    ImpV1DbVarSegMapListT *pstVarMapList = NULL;

    pstVarMapList = *ppstVarMapList;
    while (pstVarMapList != NULL) {
        pstTmpVarMapList = pstVarMapList;
        pstVarMapList = pstVarMapList->pstNext;
        DbDynMemCtxFree(memCtx, pstTmpVarMapList);
    }
    *ppstVarMapList = NULL;
}

ImpV1DbVarSegMapListT *DbVarSegAllocMapList(DbMemCtxT *memCtx, const uint32_t ulPageCnt)
{
    ImpV1DbVarSegMapListT *pstVarMapList = NULL;
    ImpV1DbVarSegMapListT *pstTmpVarMapList = NULL;
    // 每个map list结构管理256个page
    DB_ASSERT(ulPageCnt % DBMM_VARSEGLIST_EXTEND_CNT == 0);
    uint32_t ulCounter = (ulPageCnt / DBMM_VARSEGLIST_EXTEND_CNT);
    for (uint32_t ulIdx = 0; ulIdx < ulCounter; ulIdx++) {
        pstTmpVarMapList = (ImpV1DbVarSegMapListT *)DbDynMemCtxAlloc(memCtx, sizeof(ImpV1DbVarSegMapListT));
        if (pstTmpVarMapList == NULL) {
            DbVarSegFreeMapList(memCtx, &pstVarMapList);
            return NULL;
        }
        (void)memset_s(pstTmpVarMapList, sizeof(ImpV1DbVarSegMapListT), 0, sizeof(ImpV1DbVarSegMapListT));

        pstTmpVarMapList->pstNext = pstVarMapList;
        pstVarMapList = pstTmpVarMapList;
    }
    return pstVarMapList;
}

void DbTblPageMgrAssignPageMapEntry(ImpV1DbPageMgrT *pstPgMgr, uint32_t ulMaxPgCnt)
{
    DB_POINTER(pstPgMgr);
    ImpV1DbVarSegMapListT *pstTmpVarMapList = NULL;

    pstTmpVarMapList = pstPgMgr->pstVarMapList;
    uint32_t ulMapId = 0;
    for (uint32_t ulItr = 0; ulItr < ulMaxPgCnt; ulItr++) {
        /* Assign the Page Map entry in Destination Table local Buffer */
        pstPgMgr->ppstPgMapArr[ulItr] = &pstTmpVarMapList->astVarPage[ulMapId];
        ulMapId++;

        /* Move to Next Entry in list */
        if (ulMapId == DBMM_VARSEGLIST_EXTEND_CNT) {
            pstTmpVarMapList = pstTmpVarMapList->pstNext;
            ulMapId = 0;
        }
    }
}

static Status DBRegRelOptMgr(int32_t fd, bool isNeedSwapEndian, DbMemCtxT *memCtx, const FileLocationT *tableLocDesc,
    V5DbddlReldestblStru *pstRelDes)
{
    ImpV1DbRelDirT pstRelOptMgr = {0};
    // table desc | field | index | opt mgr | dir entry
    uint32_t optMgrOffset = tableLocDesc->offset + (uint32_t)sizeof(V5DbddlReldestblStru) +
                            (uint32_t)pstRelDes->ucFldNum * (uint32_t)sizeof(V5DbddlRflddestblStru) +
                            (uint32_t)pstRelDes->ucIdxNum * (uint32_t)sizeof(V5DbddlIdxdestblStru);
    Status ret = DbFileBufRead(fd, optMgrOffset, sizeof(ImpV1DbRelDirT), (uint8_t *)&pstRelOptMgr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Unable to read read rel opt mgr, table name: %s, offset: %" PRIu32 ".",
            pstRelDes->aucRelName, optMgrOffset);
        return ret;
    }
    pstRelOptMgr.ulNumOfDirEntry = DbTryTransEndian32(pstRelOptMgr.ulNumOfDirEntry, isNeedSwapEndian);
    pstRelOptMgr.ulDirEntrySize = DbTryTransEndian32(pstRelOptMgr.ulDirEntrySize, isNeedSwapEndian);
    uint32_t entrySize = pstRelOptMgr.ulNumOfDirEntry * pstRelOptMgr.ulDirEntrySize;
    uint32_t allocSize = (uint32_t)sizeof(ImpV1DbRelDirT) + entrySize;
    // 异常分支就近释放，解析完成后通过删memCtx统一回收
    ImpV1DbRelDirT *optMgrWithEntry = (ImpV1DbRelDirT *)DbDynMemCtxAlloc(memCtx, allocSize);
    if (SECUREC_UNLIKELY(optMgrWithEntry == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
            "Unable to alloc opt mgr with entry, table name: %s, size:%" PRIu32 ".", pstRelDes->aucRelName, allocSize);
        return GMERR_OUT_OF_MEMORY;
    }
    ImpV1DbDirEntryT *entry = (ImpV1DbDirEntryT *)((uint8_t *)optMgrWithEntry + (uint32_t)sizeof(ImpV1DbRelDirT));
    ret = DbFileBufRead(fd, optMgrOffset + (uint32_t)sizeof(ImpV1DbRelDirT), entrySize, (uint8_t *)entry);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbDynMemCtxFree(memCtx, optMgrWithEntry);
        DB_LOG_ERROR(ret, "Unable to read read rel opt entry, table name: %s, size: %" PRIu32 ".",
            pstRelDes->aucRelName, entrySize);
        return ret;
    }
    for (uint32_t i = 0; i < pstRelOptMgr.ulNumOfDirEntry; i++) {
        entry[i].ulOffset = DbTryTransEndian32(entry[i].ulOffset, isNeedSwapEndian);
        entry[i].ulSize = DbTryTransEndian32(entry[i].ulSize, isNeedSwapEndian);
    }
    optMgrWithEntry->ulDirEntrySize = pstRelOptMgr.ulDirEntrySize;
    optMgrWithEntry->ulNumOfDirEntry = pstRelOptMgr.ulNumOfDirEntry;

    DbOptMgrSet(pstRelDes, optMgrWithEntry);
    return GMERR_OK;
}

Status DbAllocTblRegMgr(
    int32_t fd, bool isNeedSwapEndian, DbMemCtxT *memCtx, FileLocationT *tableLocDesc, V5DbddlReldestblStru *pstRelDes)
{
    // 异常分支就近释放，解析完成后通过删memCtx统一回收
    ImpV1DbTblFeatureMgrT *pstTblRegMgr =
        (ImpV1DbTblFeatureMgrT *)DbDynMemCtxAlloc(memCtx, sizeof(ImpV1DbTblFeatureMgrT));
    if (SECUREC_UNLIKELY(pstTblRegMgr == NULL)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_OUT_OF_MEMORY, "Unable to alloc tbl reg mgr, table name: %s.", pstRelDes->aucRelName);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(pstTblRegMgr, sizeof(ImpV1DbTblFeatureMgrT), 0, sizeof(ImpV1DbTblFeatureMgrT));
    pstRelDes->pTblRegFeatures.pPtr = pstTblRegMgr;

    Status ret = DBRegRelOptMgr(fd, isNeedSwapEndian, memCtx, tableLocDesc, pstRelDes);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(memCtx, pstTblRegMgr);
        pstRelDes->pTblRegFeatures.pPtr = NULL;
        return ret;
    }
    return GMERR_OK;
}

static inline void PagePersAttrTransEndian(ImpV1DbPageMgrPersT *pstPgPersAttr, bool isNeedSwapEndian)
{
    pstPgPersAttr->ulMaxPgCnt = DbTryTransEndian32(pstPgPersAttr->ulMaxPgCnt, isNeedSwapEndian);
    pstPgPersAttr->ulCurPgCnt = DbTryTransEndian32(pstPgPersAttr->ulCurPgCnt, isNeedSwapEndian);
    pstPgPersAttr->ulPgSize = DbTryTransEndian32(pstPgPersAttr->ulPgSize, isNeedSwapEndian);
    for (uint32_t i = 0; i < pstPgPersAttr->ulMaxPgCnt; i++) {
        pstPgPersAttr->aulPgOffsets[i] = DbTryTransEndian32(pstPgPersAttr->aulPgOffsets[i], isNeedSwapEndian);
    }
}

static Status AllocPageMgrPersAttr(
    int32_t fd, bool isNeedSwapEndian, DbMemCtxT *memCtx, ImpV1DbDirEntryT *pstOptMgr, ImpV1DbPageMgrT *pstPgMgr)
{
    ImpV1DbPageMgrPersT *pstPgPersAttr = (ImpV1DbPageMgrPersT *)DbDynMemCtxAlloc(memCtx, pstOptMgr->ulSize);
    if (SECUREC_UNLIKELY(pstPgPersAttr == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc page persistent attributes, blk size: %" PRIu32 ".",
            pstOptMgr->ulSize);
        return GMERR_OUT_OF_MEMORY;
    }
    /* Seek and read the Persistent Var Seg Mgr from the file */
    Status ret = DbFileBufRead(fd, pstOptMgr->ulOffset, pstOptMgr->ulSize, (uint8_t *)pstPgPersAttr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Unable to read page persistent attributes, offset: %" PRIu32 ", size: %" PRIu32 ".",
            pstOptMgr->ulOffset, pstOptMgr->ulSize);
        DbDynMemCtxFree(memCtx, pstPgPersAttr);
        return ret;
    }
    PagePersAttrTransEndian(pstPgPersAttr, isNeedSwapEndian);
    pstPgMgr->pstPgPersAttr = pstPgPersAttr;
    return GMERR_OK;
}

Status DbTblPageMgrLoad(
    int32_t fd, bool isNeedSwapEndian, DbMemCtxT *memCtx, V5DbddlReldestblStru *pstRelDes, ImpV1DbDirEntryT *pstOptMgr)
{
    ImpV1DbPageMgrT *pstPgMgr = (ImpV1DbPageMgrT *)DbDynMemCtxAlloc(memCtx, sizeof(ImpV1DbPageMgrT));
    if (SECUREC_UNLIKELY(pstPgMgr == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc page mgr, table name: %s.", pstRelDes->aucRelName);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(pstPgMgr, sizeof(ImpV1DbPageMgrT), 0, sizeof(ImpV1DbPageMgrT));
    Status ret = AllocPageMgrPersAttr(fd, isNeedSwapEndian, memCtx, pstOptMgr, pstPgMgr);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(memCtx, pstPgMgr);
        return ret;
    }
    uint32_t ulMaxPgCnt = pstPgMgr->pstPgPersAttr->ulMaxPgCnt;
    /* Allocate memory for Var Seg - Var Data Pages Map */
    pstPgMgr->ppstPgMapArr = (ImpV1DbVarSegPageT **)DbDynMemCtxAlloc(memCtx, ulMaxPgCnt * sizeof(char *));
    if (pstPgMgr->ppstPgMapArr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc page map arr, page cnt: %" PRIu32 ".", ulMaxPgCnt);
        ret = GMERR_OUT_OF_MEMORY;
        goto ERR1;
    }
    (void)memset_s(pstPgMgr->ppstPgMapArr, ulMaxPgCnt * sizeof(char *), 0, ulMaxPgCnt * sizeof(char *));

    /* Allocate memory VarSeg Page Map List */
    pstPgMgr->pstVarMapList = DbVarSegAllocMapList(memCtx, ulMaxPgCnt);
    if (pstPgMgr->pstVarMapList == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_OUT_OF_MEMORY, "Unable to alloc var page map list, page cnt: %" PRIu32 ".", ulMaxPgCnt);
        ret = GMERR_OUT_OF_MEMORY;
        goto ERR2;
    }
    DbTblPageMgrAssignPageMapEntry(pstPgMgr, ulMaxPgCnt);

    DbPgMgrSet(pstRelDes, pstPgMgr);
    return GMERR_OK;
ERR2:
    DbDynMemCtxFree(memCtx, pstPgMgr->ppstPgMapArr);
ERR1:
    DbDynMemCtxFree(memCtx, pstPgMgr->pstPgPersAttr);
    DbDynMemCtxFree(memCtx, pstPgMgr);
    return ret;
}

Status DbTblOptMgrLoad(int32_t fd, bool isNeedSwapEndian, DbMemCtxT *memCtx, V5DbddlReldestblStru *pstRelDes)
{
    ImpV1DbRelDirT *pstRelOptMgr = DbOptMgrGet(pstRelDes);
    ImpV1DbDirEntryT *pstOptMgrs = (ImpV1DbDirEntryT *)(pstRelOptMgr + 1);
    uint32_t ulOptMgrCnt = pstRelOptMgr->ulNumOfDirEntry;
    uint32_t ulOptMgrSize = pstRelOptMgr->ulDirEntrySize;
    for (uint32_t i = 0; i < ulOptMgrCnt; i++) {
        if (pstOptMgrs->ucType == DB_DIR_VARSEG_REL) {
            Status ret = DbTblPageMgrLoad(fd, isNeedSwapEndian, memCtx, pstRelDes, pstOptMgrs);
            if (ret != GMERR_OK) {
                return ret;
            }
        } else {
            /* Forward Compatibility Code. Make all other optional managers Invalid */
            pstOptMgrs->ucType = DB_DIR_TYPE_BUTT;
            pstOptMgrs->ulOffset = V1DBKNL_NULL_OFFSET;
            pstOptMgrs->ulSize = 0;
        }
        // next entry
        pstOptMgrs = (ImpV1DbDirEntryT *)((uint8_t *)pstOptMgrs + ulOptMgrSize);
    }
    return GMERR_OK;
}

/* Page management lists */
typedef enum TagPageListType {
    PAGE_ACTIVE = 0, /* Indicates the page belongs to active list. */
    PAGE_FULL,       /* Indicates the page belongs to full list. */
    PAGE_BUTT        /* Invalid page type. */
} PageListTypeE;

void DbPageListAppendPg(ImpV1DbPageMgrT *pstPgMgr, ImpV1DbVarSegPageT *pstVarPgMap)
{
    DB_POINTER3(pstPgMgr, pstVarPgMap, pstVarPgMap->pstPage);
    ImpV1DbVarSegPageT **ppstPgList = NULL;
    /* Add page to active list. */
    if (pstVarPgMap->pstPage->ucPageType == PAGE_ACTIVE) {
        ppstPgList = &pstPgMgr->pstActivePgList;
    } else { /* Add page to full list. */
        ppstPgList = &pstPgMgr->pstFullPgList;
    }
    pstVarPgMap->pPrevPage = NULL;
    /* Add the page at the beginning of list */
    /* If list is NULL, then automatically first page next point will become NULL. */
    pstVarPgMap->pNextPage = *ppstPgList;
    /* If the list has pages, add new page at the beginning of the list. */
    if (*ppstPgList != NULL) {
        (*ppstPgList)->pPrevPage = pstVarPgMap;
    }
    *ppstPgList = pstVarPgMap;
    return;
}

void DbTblFreePagesAllocated(ImpV1DbPageMgrT *pstPgMgr, DbMemCtxT *memCtx, uint32_t currIdx)
{
    /* Free the Pages already Allocated and Read. */
    for (uint32_t i = 0; i < currIdx; i++) {
        if (pstPgMgr->ppstPgMapArr[i]->pstPage != NULL) {
            DbDynMemCtxFree(memCtx, pstPgMgr->ppstPgMapArr[i]->pstPage);
        }
    }
}

Status DbTblPagesLoad(int32_t fd, DbMemCtxT *memCtx, V5DbddlReldestblStru *pstRelDes)
{
    ImpV1DbPageMgrT *pstPgMgr = DbPgMgrGet(pstRelDes);
    ImpV1DbPageMgrPersT *pstPgPersAttr = pstPgMgr->pstPgPersAttr;
    uint32_t ulPgSize = pstPgPersAttr->ulPgSize;      // todo 大小端转换
    uint32_t ulMaxPgCnt = pstPgPersAttr->ulMaxPgCnt;  //

    for (uint32_t i = 0; i < ulMaxPgCnt; i++) {
        uint32_t readOffset = pstPgPersAttr->aulPgOffsets[i];  // todo 大小端
        if (readOffset == V1DBKNL_NULL_OFFSET) {
            continue;
        }
        // 异常分支就近释放，解析完成后通过删memCtx统一回收
        ImpV1DbPageHeaderT *page = (ImpV1DbPageHeaderT *)DbDynMemCtxAlloc(memCtx, ulPgSize);
        if (SECUREC_UNLIKELY(page == NULL)) {
            DbTblFreePagesAllocated(pstPgMgr, memCtx, i);
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc page, size: %" PRIu32 ".", ulPgSize);
            return GMERR_OUT_OF_MEMORY;
        }
        Status ret = DbFileBufRead(fd, readOffset, ulPgSize, (uint8_t *)page);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DbTblFreePagesAllocated(pstPgMgr, memCtx, i);
            DB_LOG_ERROR(ret,
                "Unable to read page, idx: %" PRIu32 ", page cnt: %" PRIu32 ", size: %" PRIu32 ", offset: %" PRIu32 ".",
                i, ulMaxPgCnt, ulPgSize, readOffset);
            DbDynMemCtxFree(memCtx, page);
            return ret;
        }
        ImpV1DbVarSegPageT *pstVarSegPage = pstPgMgr->ppstPgMapArr[i];
        pstVarSegPage->pstPage = page;
        pstVarSegPage->pPrevPage = NULL;
        pstVarSegPage->pNextPage = NULL;
        DbPageListAppendPg(pstPgMgr, pstVarSegPage);
    }
    return GMERR_OK;
}

static Status AllocMemForResult(ImportV1AnalyzeResultCtxT *analyzeResult)
{
    // 内存释放由外部对memctx进行统一释放，SimpRelReleaseImportDataCtx
    // 为表描述信息申请内存 和 表数据信息申请内存
    uint32_t allocSize = analyzeResult->tableNum * sizeof(FileLocationT);
    allocSize += analyzeResult->tableNum * sizeof(V1DataInfoT);
    uint8_t *ptr = (uint8_t *)DbDynMemCtxAlloc(analyzeResult->memCtx, allocSize);
    if (SECUREC_UNLIKELY(ptr == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc table desc ptr, size:%" PRIu32 ".", allocSize);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(ptr, allocSize, 0, allocSize);
    analyzeResult->tableDesc = (FileLocationT *)ptr;
    analyzeResult->v1DataInfo = (V1DataInfoT *)(ptr + analyzeResult->tableNum * sizeof(FileLocationT));
    return GMERR_OK;
}

static Status AnalyzeBlocks4V1(V5DbDbmsStru *pstNewDB, V5DbddlReldestblStru *pstRelDes,
    V5DbfiDatablkaddrStru *pstDataBlksAddr, uint32_t index, ImportV1AnalyzeResultCtxT *analyzeResult)
{
    bool isNeedSwapEndian = DbNeedSwapEndian(pstNewDB->ucFormat);
    uint16_t blkNum = DbTryTransEndian16(pstRelDes->usNumDIBlks, isNeedSwapEndian);
    analyzeResult->v1DataInfo[index].blkNum = blkNum;
    // 函数中的内存申请统一在SimpRelReleaseImportDataCtx中释放
    uint8_t *recordNumAddr = (uint8_t *)DbDynMemCtxAlloc(analyzeResult->memCtx, blkNum * sizeof(uint32_t));
    if (recordNumAddr == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_OUT_OF_MEMORY, "Unable to alloc record num ptr, size:%" PRIu32 ".", blkNum * sizeof(uint32_t));
        return GMERR_OUT_OF_MEMORY;
    }
    // 函数中的内存申请统一在SimpRelReleaseImportDataCtx中释放
    uint8_t *blkAddr = (uint8_t *)DbDynMemCtxAlloc(analyzeResult->memCtx, blkNum * sizeof(FileLocationT));
    if (blkAddr == NULL) {
        DbDynMemCtxFree(analyzeResult->memCtx, recordNumAddr);
        DB_LOG_AND_SET_LASERR(
            GMERR_OUT_OF_MEMORY, "Unable to alloc block addr, size:%" PRIu32 ".", blkNum * sizeof(FileLocationT));
        return GMERR_OUT_OF_MEMORY;
    }
    analyzeResult->v1DataInfo[index].blkRecordNum = (uint32_t *)recordNumAddr;
    analyzeResult->v1DataInfo[index].blkInfo = (FileLocationT *)blkAddr;
    for (uint32_t j = 0; j < blkNum; j++) {
        analyzeResult->v1DataInfo[index].blkRecordNum[j] =
            DbTryTransEndian32(pstDataBlksAddr[j].nRecCnt, isNeedSwapEndian);
        analyzeResult->v1DataInfo[index].blkInfo[j].offset =
            DbTryTransEndian32(pstDataBlksAddr[j].stBlock.ulFOffset, isNeedSwapEndian);
        analyzeResult->v1DataInfo[index].blkInfo[j].size =
            DbTryTransEndian32(pstDataBlksAddr[j].stBlock.ulBlkSize, isNeedSwapEndian);
    }
    return GMERR_OK;
}

static Status AnalyzeTableCheckRelDes(V5DbddlReldestblStru *pstRelDes, bool isNeedSwapEndian)
{
    pstRelDes->stDataSegHead.ulUsedElemCnt =
        DbTryTransEndian32(pstRelDes->stDataSegHead.ulUsedElemCnt, isNeedSwapEndian);
    pstRelDes->nTotalMaxRec = DbTryTransEndian32(pstRelDes->nTotalMaxRec, isNeedSwapEndian);
    if (pstRelDes->stDataSegHead.ulUsedElemCnt > pstRelDes->nTotalMaxRec) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_CORRUPTED,
            "Check rel des unsucc, used element cnt %" PRIu32 ", max cnt %" PRIu32 ".",
            pstRelDes->stDataSegHead.ulUsedElemCnt, pstRelDes->nTotalMaxRec);
        return GMERR_DATA_CORRUPTED;
    }
    return GMERR_OK;
}

// 函数中的内存申请统一在SimpRelReleaseImportDataCtx中释放
static Status AnalyzeTableDescAndDataInfo(
    int32_t fd, ImportV1AnalyzeResultCtxT *analyzeResult, V5DbDbmsStru *pstNewDB, V5DbDTablemapStru *tableMap)
{
    bool isNeedSwapEndian = DbNeedSwapEndian(analyzeResult->ucByteOrder);
    for (uint32_t i = 0; i < analyzeResult->tableNum; i++) {
        if (tableMap[i].stTblSpace.ulFOffset == V1DBKNL_NULL_OFFSET) {
            continue;
        }
        // 记录每个表的描述信息偏移和大小
        analyzeResult->tableDesc[i].offset = DbTryTransEndian32(tableMap[i].stTblSpace.ulFOffset, isNeedSwapEndian);
        analyzeResult->tableDesc[i].size = DbTryTransEndian32(tableMap[i].stTblSpace.ulBlkSize, isNeedSwapEndian);
        // 记录每个表的数据信息偏移和大小
        V5DbddlReldestblStru pstRelDes = {0};
        Status ret =
            DbFileBufRead(fd, analyzeResult->tableDesc[i].offset, sizeof(V5DbddlReldestblStru), (uint8_t *)&pstRelDes);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Unable to read table desc, index:%" PRIu32 ", offset:%" PRIu32 ".", i,
                analyzeResult->tableDesc[i].offset);
            return ret;
        }
        ret = AnalyzeTableCheckRelDes(&pstRelDes, isNeedSwapEndian);
        if (ret != GMERR_OK) {
            return ret;
        }
        uint32_t tmpBlkSize = DbTryTransEndian32(pstRelDes.stDataIdxMng.ulBlkSize, isNeedSwapEndian);
        if (tmpBlkSize == 0) {
            continue;
        }

        // 函数中的内存申请统一在SimpRelReleaseImportDataCtx中释放
        uint8_t *blkAddrPtr = (uint8_t *)DbDynMemCtxAlloc(analyzeResult->memCtx, tmpBlkSize);
        if (SECUREC_UNLIKELY(blkAddrPtr == NULL)) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc block addr ptr, size:%" PRIu32 ".", tmpBlkSize);
            return GMERR_OUT_OF_MEMORY;
        }
        uint32_t tmpOffset = DbTryTransEndian32(pstRelDes.stDataIdxMng.ulFOffset, isNeedSwapEndian);
        ret = DbFileBufRead(fd, tmpOffset, tmpBlkSize, blkAddrPtr);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(analyzeResult->memCtx, blkAddrPtr);
            return ret;
        }
        ret = AnalyzeBlocks4V1(pstNewDB, &pstRelDes, (V5DbfiDatablkaddrStru *)blkAddrPtr, i, analyzeResult);
        DbDynMemCtxFree(analyzeResult->memCtx, blkAddrPtr);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status DbCheckV1FileHeaderSize(int32_t fd)
{
    size_t totalSize;
    Status ret = DbFileSizeByFd(fd, &totalSize);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (totalSize < V5_DBFILE_HEADER_SIZE) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "The size of the file is too small, size:%" PRIu64 ".", totalSize);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

static void DbFileAnalyzeInitCrcTable(uint16_t *crcTable, uint32_t tblSize)
{
    uint32_t ulIndex;
    uint32_t ulTemp;
    uint32_t ulValue;
    uint32_t ulTopbit = V1DB_BITMASK(V1DB_CRC_16 - 1);
    for (ulIndex = 0; ulIndex < tblSize; ulIndex++) {
        ulValue = (ulIndex << (V1DB_CRC_16 - V1DBKNL_BYTE));
        for (ulTemp = 0; ulTemp < V1DBKNL_BYTE; ulTemp++) {
            if ((ulValue & ulTopbit) != 0) {
                ulValue = (ulValue << V1DBKNL_SHIFT_ONEBITS) ^ V1DB_CRC_POLY_16;
            } else {
                ulValue <<= V1DBKNL_SHIFT_ONEBITS;
            }
        }

        /* Mask higher order 16 bit */
        crcTable[ulIndex] = (uint16_t)(ulValue & V1DB_WIDTHMASK(V1DB_CRC_16));
    }
    return;
}

inline static void DbFileAnalyzeCaclCrc16Bit(uint8_t *buf, uint32_t len, uint16_t *checkSum)
{
    DB_POINTER2(buf, checkSum);
    uint16_t currReg = *checkSum;
    uint16_t crcTable[V1DB_CRC16_TBL_SIZE];
    DbFileAnalyzeInitCrcTable(crcTable, V1DB_CRC16_TBL_SIZE);
    for (uint32_t i = 0; i < len; ++i) {
        currReg = (uint16_t)((currReg << V1DBKNL_BYTE) ^ crcTable[(currReg >> V1DBKNL_BYTE) ^ buf[i]]);
    }
    *checkSum = currReg;
}

static Status DbFileAnalyzeCheckFileHeader(V5DbDbmsStru *pstNewDB, bool isAutoRestore, bool isNeedSwapEndian)
{
    if (DbGetCurrEndian() != pstNewDB->ucFormat && isAutoRestore) {
        DB_LOG_AND_SET_LASERR(GMERR_BYTES_ORDER_MISMATCH, "Byte order mismatch.");
        return GMERR_BYTES_ORDER_MISMATCH;
    } else if (pstNewDB->ucFormat > V1DB_LITTLE_ENDIAN) {
        DB_LOG_AND_SET_LASERR(GMERR_BYTES_ORDER_MISMATCH, "Byte order is not valid.");
        return GMERR_BYTES_ORDER_MISMATCH;
    }

    pstNewDB->usCheckSum = DbTryTransEndian16(pstNewDB->usCheckSum, isNeedSwapEndian);
    uint16_t crcValue = V1DBKNL_CRC16_ENTRYVALUE;
    DbFileAnalyzeCaclCrc16Bit((uint8_t *)pstNewDB, sizeof(V5DbDbmsStru) - sizeof(uint16_t), &crcValue);
    if (pstNewDB->usCheckSum != crcValue) {
        DB_LOG_AND_SET_LASERR(GMERR_CRC_CHECK_FAILED, "Unabel to check file header, file is corrupted.");
        return GMERR_CRC_CHECK_FAILED;
    }

    uint32_t version = DbTryTransEndian32(pstNewDB->ulVersion, isNeedSwapEndian);
    if (version > V5_DB_VERSION_NUMBER) {
        DB_LOG_AND_SET_LASERR(
            GMERR_MODEL_NOT_SUPPORT, "DB forward compatibity is not supported, version:%" PRIu32 ".", version);
        return GMERR_MODEL_NOT_SUPPORT;
    }
    return GMERR_OK;
}

Status DbFileAnalyze4V1(int32_t fd, ImportV1AnalyzeResultCtxT *analyzeResult)
{
    Status ret = DbCheckV1FileHeaderSize(fd);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 读取newDB
    V5DbDbmsStru pstNewDB = {0};
    ret = DbFileBufRead(fd, 0, sizeof(V5DbDbmsStru), (uint8_t *)&pstNewDB);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    analyzeResult->ucByteOrder = pstNewDB.ucFormat;
    bool isNeedSwapEndian = DbNeedSwapEndian(pstNewDB.ucFormat);
    if ((ret = DbFileAnalyzeCheckFileHeader(&pstNewDB, analyzeResult->isAutoRestore, isNeedSwapEndian)) != GMERR_OK) {
        return ret;
    }
    // DBdesc偏移和大小
    analyzeResult->dbDesc.offset = V5_DBFILE_HEADER_SIZE;
    analyzeResult->dbDesc.size = DbTryTransEndian32(pstNewDB.ulActDesLen, isNeedSwapEndian);
    analyzeResult->tableNum = DbTryTransEndian16(pstNewDB.usNumofTab, isNeedSwapEndian);
    uint16_t tblMapSize = DbTryTransEndian16(pstNewDB.usTblMapSize, isNeedSwapEndian);
    uint32_t ulNewTblMapSize = tblMapSize * (uint32_t)sizeof(V5DbDTablemapStru);
    V5DbDTablemapStru *tableMap = (V5DbDTablemapStru *)DbDynMemCtxAlloc(analyzeResult->memCtx, ulNewTblMapSize);
    if (SECUREC_UNLIKELY(tableMap == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc table map, size:%" PRIu32 ".", ulNewTblMapSize);
        return GMERR_OUT_OF_MEMORY;
    }
    uint32_t tableMapOffset = DbTryTransEndian32(pstNewDB.stTblMap.ulFOffset, isNeedSwapEndian);
    uint32_t blkSize = DbTryTransEndian32(pstNewDB.stTblMap.ulBlkSize, isNeedSwapEndian);
    // 读取tableMap信息
    if ((ret = DbFileBufRead(fd, tableMapOffset, blkSize, (uint8_t *)tableMap)) != GMERR_OK) {
        DbDynMemCtxFree(analyzeResult->memCtx, tableMap);
        return ret;
    }
    if ((AllocMemForResult(analyzeResult)) != GMERR_OK) {
        DbDynMemCtxFree(analyzeResult->memCtx, tableMap);
        return ret;
    }
    if ((ret = AnalyzeTableDescAndDataInfo(fd, analyzeResult, &pstNewDB, tableMap)) != GMERR_OK) {
        DbDynMemCtxFree(analyzeResult->memCtx, tableMap);
        return ret;
    }
    analyzeResult->tableMap = tableMap;
    return GMERR_OK;
}

static Status AnalyzeField4V1(
    int32_t fd, bool isNeedSwapEndian, FileLocationT *tableLocDesc, ImportV1TableInfoT *v1TabelInfo)
{
    uint32_t fieldNum = v1TabelInfo->ulNCols;
    uint32_t allocSize = fieldNum * sizeof(V5DbddlRflddestblStru);
    V5DbddlRflddestblStru *fieldAddr = (V5DbddlRflddestblStru *)DbDynMemCtxAlloc(v1TabelInfo->memCtx, allocSize);
    if (fieldAddr == NULL || allocSize == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc field addr, size:%" PRIu32 ".", allocSize);
        return GMERR_OUT_OF_MEMORY;
    }
    Status ret =
        DbFileBufRead(fd, tableLocDesc->offset + sizeof(V5DbddlReldestblStru), allocSize, (uint8_t *)fieldAddr);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(v1TabelInfo->memCtx, fieldAddr);
        return ret;
    }
    for (uint32_t i = 0; i < fieldNum; i++) {
        if (strcmp((const char *)fieldAddr[i].aucFldName, V5_DBTPC_CDB_FLD) == 0 ||
            strcmp((const char *)fieldAddr[i].aucFldName, V5_DBTPC_RDB_VALIDFLD) == 0) {
            v1TabelInfo->ulNCols--;
            v1TabelInfo->hasDefaultField = true;
        }
        if (fieldAddr[i].ucIsVarField) {
            // 统计变长字段存储长度
            v1TabelInfo->varFieldSize += (uint32_t)DbTryTransEndian16(fieldAddr[i].usStoredLen, isNeedSwapEndian);
            v1TabelInfo->isVarTable = true;
        }
    }
    fieldNum = v1TabelInfo->ulNCols;
    // 内存统一在SimpRelReleaseImportDataCtx中释放
    ImportV1FieldInfoT *pstFldLst =
        (ImportV1FieldInfoT *)DbDynMemCtxAlloc(v1TabelInfo->memCtx, fieldNum * sizeof(ImportV1FieldInfoT));
    if (pstFldLst == NULL) {
        DbDynMemCtxFree(v1TabelInfo->memCtx, fieldAddr);
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc field list, size:%" PRIu32 ".",
            fieldNum * sizeof(ImportV1FieldInfoT));
        return GMERR_OUT_OF_MEMORY;
    }

    for (uint32_t i = 0; i < fieldNum; i++) {
        (void)memcpy_s(pstFldLst[i].fieldName, V5_DB_NAME_LEN, fieldAddr[i].aucFldName, V5_DB_NAME_LEN);
        pstFldLst[i].ulDefVal = DbTryTransEndian32(fieldAddr[i].ulDefaultValue, isNeedSwapEndian);
        pstFldLst[i].dataType = fieldAddr[i].ucDataType;
        pstFldLst[i].isVarField = fieldAddr[i].ucIsVarField;
        pstFldLst[i].usSize = DbTryTransEndian16(fieldAddr[i].usDefLen, isNeedSwapEndian);
        pstFldLst[i].storedLen = DbTryTransEndian16(fieldAddr[i].usStoredLen, isNeedSwapEndian);
    }
    DbDynMemCtxFree(v1TabelInfo->memCtx, fieldAddr);
    v1TabelInfo->pstFldLst = pstFldLst;
    return GMERR_OK;
}

static void AnalyzeIndexFields4V1(uint32_t fieldNum, V5DbddlIdxdestblStru *indexAddr)
{
    uint32_t indexFieldNum = indexAddr->ucFldNum;
    for (uint32_t i = 0; i < indexAddr->ucFldNum; i++) {
        if (indexAddr->afIdxFldAry[i] >= fieldNum) {
            indexAddr->afIdxFldAry[i] = 0xFF;
            indexFieldNum--;
        }
    }
    indexAddr->ucFldNum = (uint8_t)indexFieldNum;
}

static Status AnalyzeIndex4V1(int32_t fd, const V5DbddlReldestblStru *pstRelDes, const FileLocationT *tableLocDesc,
    ImportV1TableInfoT *v1TabelInfo)
{
    if (pstRelDes->ucIdxNum == 0) {
        return GMERR_OK;
    }
    uint32_t allocSize = pstRelDes->ucIdxNum * sizeof(V5DbddlIdxdestblStru);
    V5DbddlIdxdestblStru *indexAddr = (V5DbddlIdxdestblStru *)DbDynMemCtxAlloc(v1TabelInfo->memCtx, allocSize);
    if (indexAddr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc index addr, size:%" PRIu32 ".", allocSize);
        return GMERR_OUT_OF_MEMORY;
    }
    uint32_t indexOffset =
        tableLocDesc->offset + sizeof(V5DbddlReldestblStru) + pstRelDes->ucFldNum * sizeof(V5DbddlRflddestblStru);
    Status ret =
        DbFileBufRead(fd, indexOffset, pstRelDes->ucIdxNum * sizeof(V5DbddlIdxdestblStru), (uint8_t *)indexAddr);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(v1TabelInfo->memCtx, indexAddr);
        return ret;
    }
    allocSize = pstRelDes->ucIdxNum * sizeof(ImportV1IndexInfoT);
    // 内存统一在SimpRelReleaseImportDataCtx中释放
    ImportV1IndexInfoT *pstIdxLst = (ImportV1IndexInfoT *)DbDynMemCtxAlloc(v1TabelInfo->memCtx, allocSize);
    if (pstIdxLst == NULL) {
        DbDynMemCtxFree(v1TabelInfo->memCtx, indexAddr);
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc index list, size:%" PRIu32 ".", allocSize);
        return GMERR_OUT_OF_MEMORY;
    }
    for (uint32_t i = 0; i < pstRelDes->ucIdxNum; i++) {
        (void)memcpy_s(pstIdxLst[i].indexName, V5_DB_NAME_LEN, indexAddr[i].aucIndexName, V5_DB_NAME_LEN);
        pstIdxLst[i].indexType = indexAddr[i].ucType;
        pstIdxLst[i].ucUniqueFlag = indexAddr[i].ucUnique;
        AnalyzeIndexFields4V1(v1TabelInfo->ulNCols, &indexAddr[i]);
        pstIdxLst[i].ucIdxFldNum = indexAddr[i].ucFldNum;
        (void)memcpy_s(pstIdxLst[i].aucFieldID, V5_DB_IDX_FLD_MAX, indexAddr[i].afIdxFldAry, V5_DB_IDX_FLD_MAX);
    }
    DbDynMemCtxFree(v1TabelInfo->memCtx, indexAddr);
    v1TabelInfo->pstIdxLst = pstIdxLst;
    return GMERR_OK;
}

Status DbVarTableRegMgrAndLoadPage(int32_t fd, bool isNeedSwapEndian, FileLocationT *tableLocDesc,
    ImportV1TableInfoT *v1TabelInfo, V5DbddlReldestblStru *pstRelDes)
{
    Status ret = DbAllocTblRegMgr(fd, isNeedSwapEndian, v1TabelInfo->memCtx, tableLocDesc, pstRelDes);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbTblOptMgrLoad(fd, isNeedSwapEndian, v1TabelInfo->memCtx, pstRelDes);
    if (ret != GMERR_OK) {
        return ret;
    }
    return DbTblPagesLoad(fd, v1TabelInfo->memCtx, pstRelDes);
}

Status DbFileAnalyzeTable4V1(
    int32_t fd, bool isNeedSwapEndian, FileLocationT *tableLocDesc, ImportV1TableInfoT *v1TabelInfo)
{
    // 读取表描述信息
    V5DbddlReldestblStru *pstRelDes =
        (V5DbddlReldestblStru *)DbDynMemCtxAlloc(v1TabelInfo->memCtx, sizeof(V5DbddlReldestblStru));
    if (pstRelDes == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_OUT_OF_MEMORY, "Unable to alloc rel des, size:%" PRIu32 ".", (uint32_t)sizeof(V5DbddlReldestblStru));
        return GMERR_OUT_OF_MEMORY;
    }
    Status ret = DbFileBufRead(fd, tableLocDesc->offset, sizeof(V5DbddlReldestblStru), (uint8_t *)pstRelDes);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(v1TabelInfo->memCtx, pstRelDes);
        return ret;
    }
    memcpy_s(v1TabelInfo->tableName, V5_DB_NAME_LEN, pstRelDes->aucRelName, V5_DB_NAME_LEN);
    v1TabelInfo->ulIntialSize = DbTryTransEndian32(pstRelDes->nCurRecMax, isNeedSwapEndian);
    v1TabelInfo->ulMaxSize = DbTryTransEndian32(pstRelDes->nTotalMaxRec, isNeedSwapEndian);
    v1TabelInfo->ulNCols = pstRelDes->ucFldNum;
    v1TabelInfo->ulNIdxs = pstRelDes->ucIdxNum;
    v1TabelInfo->freeSlot = DbTryTransEndian32(pstRelDes->stDataSegHead.ulFreeList, isNeedSwapEndian);
    v1TabelInfo->realRecordNum = DbTryTransEndian32(pstRelDes->stDataSegHead.ulUsedElemCnt, isNeedSwapEndian);
    v1TabelInfo->colSize = DbTryTransEndian32(pstRelDes->stDataSegHead.ulElemSize, isNeedSwapEndian);
    v1TabelInfo->varFieldSize = 0;
    v1TabelInfo->isVarTable = false;

    ret = AnalyzeField4V1(fd, isNeedSwapEndian, tableLocDesc, v1TabelInfo);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(v1TabelInfo->memCtx, pstRelDes);
        return ret;
    }
    if (v1TabelInfo->isVarTable) {
        ret = DbVarTableRegMgrAndLoadPage(fd, isNeedSwapEndian, tableLocDesc, v1TabelInfo, pstRelDes);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(v1TabelInfo->memCtx, pstRelDes);
            return ret;
        }
    }
    ret = AnalyzeIndex4V1(fd, pstRelDes, tableLocDesc, v1TabelInfo);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(v1TabelInfo->memCtx, pstRelDes);
        return ret;
    }
    v1TabelInfo->pstRelDes = pstRelDes;
    return GMERR_OK;
}

void DbFileFreeTableInfo4V1(ImportV1TableInfoT *v1TabelInfo)
{
    if (v1TabelInfo->memCtx == NULL) {
        return;
    }
    if (v1TabelInfo->pstFldLst != NULL) {
        DbDynMemCtxFree(v1TabelInfo->memCtx, v1TabelInfo->pstFldLst);
        v1TabelInfo->pstFldLst = NULL;
    }
    if (v1TabelInfo->pstIdxLst != NULL) {
        DbDynMemCtxFree(v1TabelInfo->memCtx, v1TabelInfo->pstIdxLst);
        v1TabelInfo->pstIdxLst = NULL;
    }
}

void *GetVarRecordPtr(
    V5DbddlReldestblStru *pstRelDes, DbVariableDataTypeT *varSegPage, uint32_t varPos, bool isNeedSwapEndian)
{
    ImpV1DbPageMgrT *pstPgMgr = DbPgMgrGet(pstRelDes);
    // 解析变长字段时，pgMgr需初始化
    DB_ASSERT(pstPgMgr != NULL);
    // 根据pageId获取page
    uint32_t pageId = DbTryTransEndian32(varSegPage->ulPageID, isNeedSwapEndian);
    ImpV1DbPageHeaderT *pstPgAddr = pstPgMgr->ppstPgMapArr[pageId]->pstPage;
    // page后为目录行
    uint32_t rowIdx = DbTryTransEndian32(varSegPage->ulRowDirIdx, isNeedSwapEndian);
    ImpV1DbRowDirT *pstRowDir = ((ImpV1DbRowDirT *)(pstPgAddr + 1)) + rowIdx;
    // 根据目录行获取变长数据的偏移
    uint32_t rowDirOffset = DbTryTransEndian32(pstRowDir->ulOffset, isNeedSwapEndian);
    uint8_t *pucVarRec = ((uint8_t *)pstPgAddr) + rowDirOffset;
    // 变长数据起始为：变长字段个数*4字节，记录每个变长字段buffer所在位置的相对偏移
    uint32_t pos = *(uint32_t *)(pucVarRec + varPos * sizeof(uint32_t));
    pos = DbTryTransEndian32(pos, isNeedSwapEndian);
    uint8_t *pucSrc = pucVarRec + pos;
    return pucSrc;
}

static Status AnalyzeBlockOneRecord(
    ImportV1AnalyzeBlockParamT *blockParam, uint8_t *blockRecordAddr, uint32_t recordSize, uint8_t *outRecordAddr)
{
    errno_t errNo = EOK;
    if (!blockParam->isVarTable) {
        // 定长表，block上即为所需buffer
        errNo = memcpy_s(outRecordAddr, recordSize, blockRecordAddr, recordSize);
        if (SECUREC_UNLIKELY(errNo != EOK)) {
            DB_LOG_ERROR(
                GMERR_MEMORY_OPERATE_FAILED, "Unable to memcpy record buffer, record size: %" PRIu32 ".", recordSize);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        return GMERR_OK;
    }

    // 变长表，逐个字段解析
    ImportV1FieldInfoT *pstFldLst = blockParam->pstFldLst;
    // data block前8个字节记录页信息
    DbVariableDataTypeT *varSegPage = (DbVariableDataTypeT *)blockRecordAddr;
    uint8_t *currDataBlockAddr = blockRecordAddr + sizeof(DbVariableDataTypeT);
    uint8_t *currOutRecordAddr = outRecordAddr;
    uint32_t totalSize = 0;
    uint32_t varPos = 0;
    for (uint32_t i = 0; i < blockParam->fldNum; i++) {
        if (!pstFldLst[i].isVarField) {
            errNo = memcpy_s(currOutRecordAddr, pstFldLst[i].storedLen, currDataBlockAddr, pstFldLst[i].storedLen);
            if (SECUREC_UNLIKELY(errNo != EOK)) {
                DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
                    "Unable to memcpy fixed fld, size: %" PRIu32 ", os ret %" PRId32 ".", pstFldLst[i].storedLen,
                    (int32_t)errNo);
                return GMERR_MEMORY_OPERATE_FAILED;
            }
            currDataBlockAddr += pstFldLst[i].storedLen;
        } else {
            uint8_t *varRecord =
                GetVarRecordPtr(blockParam->pstRelDes, varSegPage, varPos, blockParam->isNeedSwapEndian);
            // 前两个字节为变长字段长度

            uint16_t varLen =
                DbTryTransEndian16(*(uint16_t *)varRecord, blockParam->isNeedSwapEndian) + (uint16_t)sizeof(uint16_t);
            errNo = memcpy_s(currOutRecordAddr, varLen, varRecord, varLen);
            if (SECUREC_UNLIKELY(errNo != EOK)) {
                DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
                    "Unable to memcpy var fld, size: %" PRIu32 ", os ret %" PRId32 ".", varLen, (int32_t)errNo);
                return GMERR_MEMORY_OPERATE_FAILED;
            }
            varPos++;
        }
        currOutRecordAddr += pstFldLst[i].storedLen;
        totalSize += pstFldLst[i].storedLen;
    }
    // 字段存储长度总和，应与每条记录长度相等
    DB_ASSERT(totalSize == recordSize);
    return GMERR_OK;
}

static Status AnalyzeBlockData4V1(
    int32_t fd, ImportV1AnalyzeBlockParamT *blockParam, uint8_t *currAddr, uint32_t *outRecordNum)
{
    if (blockParam->blkRecordNum == 0) {
        return GMERR_OK;
    }
    uint32_t mapSize = blockParam->blkRecordNum * sizeof(uint32_t);
    uint32_t dataSize = blockParam->blkRecordNum * blockParam->originalColSize;
    uint32_t readSize = mapSize + dataSize;
    uint8_t *readBuffer = (uint8_t *)DbDynMemCtxAlloc(blockParam->memCtx, readSize);
    if (readBuffer == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
            "Unable to alloc read buffer, mapSize:%" PRIu32 ", dataSize:%" PRIu32, mapSize, dataSize);
        return GMERR_OUT_OF_MEMORY;
    }
    Status ret = DbFileBufRead(fd, blockParam->blkInfo->offset, readSize, readBuffer);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(blockParam->memCtx, readBuffer);
        return ret;
    }
    // 起始位置为map
    uint32_t *usedMap = (uint32_t *)readBuffer;
    // map后为record
    uint8_t *recordStartAddr = readBuffer + mapSize;
    uint32_t recordSize = blockParam->colSize;

    uint32_t offset = 0;
    uint32_t recordNum = 0;
    for (uint32_t i = 0; i < blockParam->blkRecordNum; i++) {
        if (DbTryTransEndian32(usedMap[i], blockParam->isNeedSwapEndian) != V1IMP_NODE_USED) {
            continue;
        }
        uint8_t *recordAddr = recordStartAddr + (i * blockParam->originalColSize);
        uint8_t *outRecordAddr = currAddr + offset;
        ret = AnalyzeBlockOneRecord(blockParam, recordAddr, recordSize, outRecordAddr);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(blockParam->memCtx, readBuffer);
            return ret;
        }
        offset += recordSize;
        recordNum++;
    }
    *outRecordNum = recordNum;
    DbDynMemCtxFree(blockParam->memCtx, readBuffer);
    return GMERR_OK;
}

static uint32_t GetRecordSize(ImportV1TableInfoT *tableInfo)
{
    uint32_t recordSize = tableInfo->colSize;
    if (tableInfo->hasDefaultField) {
        // 翻转校验。带默认字段时colSize必大于4
        DB_ASSERT(recordSize >= V1IMP_TABLE_SYSTEM_FIELD_SIZE);
        recordSize -= V1IMP_TABLE_SYSTEM_FIELD_SIZE;
    }
    if (tableInfo->isVarTable) {
        // 翻转校验
        DB_ASSERT(recordSize >= sizeof(DbVariableDataTypeT));
        // 前8个字节为存储变长数据的pageId和idx，后面为定长字段总大小
        recordSize -= (uint32_t)sizeof(DbVariableDataTypeT);
        // 一条记录的大小：定长+变长
        recordSize += tableInfo->varFieldSize;
    }
    return recordSize;
}

Status DbAnalyzeRecord4V1(
    int32_t fd, bool isNeedSwapEndian, ImportV1AnalyzeRecordParamT *recordParam, ImportV1RecordInfoT *outRecord)
{
    DB_POINTER2(recordParam, outRecord);

    ImportV1TableInfoT *tableInfo = recordParam->tableInfo;
    uint32_t recordSize = GetRecordSize(tableInfo);
    uint32_t bufferSize = tableInfo->realRecordNum * recordSize;
    // 异常分支就近释放，解析完成后通过删memCtx释放
    uint8_t *recordBuffer = (uint8_t *)DbDynMemCtxAlloc(recordParam->memCtx, bufferSize);
    if (recordBuffer == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc record buffer, buffer size:%" PRIu32, bufferSize);
        return GMERR_OUT_OF_MEMORY;
    }

    uint32_t totalRecordNum = 0;
    uint8_t *currAddr = recordBuffer;
    for (uint32_t i = 0; i < recordParam->dataLocDesc->blkNum; i++) {
        ImportV1AnalyzeBlockParamT blockParam = {.memCtx = recordParam->memCtx,
            .blkInfo = &recordParam->dataLocDesc->blkInfo[i],
            .pstRelDes = tableInfo->pstRelDes,
            .pstFldLst = tableInfo->pstFldLst,
            .fldNum = tableInfo->ulNCols,
            .blkRecordNum = recordParam->dataLocDesc->blkRecordNum[i],
            .colSize = recordSize,
            .originalColSize = tableInfo->colSize,
            .varFieldSize = tableInfo->varFieldSize,
            .isNeedSwapEndian = isNeedSwapEndian,
            .isVarTable = tableInfo->isVarTable};
        uint32_t outRecordNum = 0;
        Status ret = AnalyzeBlockData4V1(fd, &blockParam, currAddr, &outRecordNum);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(recordParam->memCtx, recordBuffer);
            return ret;
        }
        totalRecordNum += outRecordNum;
        uint32_t offset = (totalRecordNum * recordSize);
        currAddr = recordBuffer + offset;
    }
    // 实际解析出来的record数量，应与记录的数量匹配
    if (totalRecordNum != tableInfo->realRecordNum) {
        DbDynMemCtxFree(recordParam->memCtx, recordBuffer);
        DB_LOG_AND_SET_LASERR(GMERR_CRC_CHECK_FAILED, "Unable to check tbl recCnt, file is corrupted.");
        return GMERR_CRC_CHECK_FAILED;
    }
    outRecord->recordNum = totalRecordNum;
    outRecord->recordSize = recordSize;
    outRecord->recordBuffer = recordBuffer;
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
