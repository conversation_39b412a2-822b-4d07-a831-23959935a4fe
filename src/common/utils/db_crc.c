/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: db_crc.c
 * Description: implement of db crc
 * Author:
 * Create: 2024-1-31
 */

#include "db_crc.h"
#include "db_utils.h"

void DbAddCheckSum(char *data, uint32_t dataSize, uint32_t *checkSum)
{
    DB_POINTER2(data, checkSum);
    *checkSum = 0;                                       // checkSum point to data->checkSum,
    *checkSum = (uint32_t)DBTableCRC32(data, dataSize);  // calculate crc
}

bool DbCheckCrc(char *data, uint32_t dataSize, uint32_t *checkSum)
{
    DB_POINTER2(data, checkSum);
    uint32_t cacheCheckSum = *checkSum;
    *checkSum = 0;
    uint32_t calcCheckSum = (uint32_t)DBTableCRC32(data, dataSize);  // calculate crc
    *checkSum = cacheCheckSum;
    return cacheCheckSum == calcCheckSum;
}
