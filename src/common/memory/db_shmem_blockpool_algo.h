/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: db_shmem_blockpool_algo.h
 * Description: internal header file for common shared memory block algorithm
 * Author: jinfanglin
 * Create: 2020-8-19
 */

#ifndef DB_SHMEM_BLOCKPOOL_ALGO_H
#define DB_SHMEM_BLOCKPOOL_ALGO_H

#include "db_mem_context_internal.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SHM_BLOCK_POOL_BASE_SIZE (4 * 1024 * 1024)               // init 4M
#define SHM_BLOCK_POOL_STEP_SIZE (5 * SHM_BLOCK_POOL_BASE_SIZE)  // step size 20M
#define SHM_BLOCK_POOL_MAX_SIZE (1024 * 1024 * 1024)             // max 1G

typedef struct DbShmemBlockAlgo {
    uint32_t permission;      // permission setting of memory context
    uint32_t baseSize;        // initial size of memory that pool contains
    uint32_t maxSize;         // the max size of memory that pool could extend to
    uint32_t stepSize;        // the size of memory expanded(added to the pool) each time
    uint32_t key;             // made by server id + instance id + ctx id
    ShmemPtrT blkPoolShmPtr;  // shmem pointer for block pool
    uint32_t allocCount;      // all times allocated by this context
    uint32_t freeCount;       // all times freed by this context
    uint32_t reserve;
#ifdef DB_MEM_TRACE
    ShmemPtrT allocChunks;  // record head of chunk list for memory trace
#endif
} DbShmemBlockAlgoT;
DB_CHECK_ALIGN_ATOMIC32_MEMBER(DbShmemBlockAlgoT, allocCount);
DB_CHECK_ALIGN_ATOMIC32_MEMBER(DbShmemBlockAlgoT, freeCount);

typedef struct DbBlockShmemCtx {
    DbMemCtxT header;
    DbShmemBlockAlgoT shmemBlockAlgo;
} DbBlockShmemCtxT;

const DbShmemCtxMethodsT *GetShmemBlockAlgoMethods(void);
Status ShmemBlockAlgoInit(DbMemCtxT *curCtx, const AlgoParamT *algoParam);
ShmemPtrT ShmemBlockAlgoAlloc(DbMemCtxT *curCtx, size_t size);
Status ShmemBlockAlgoFree(DbMemCtxT *curCtx, ShmemPtrT ptr);
void ShmemBlockAlgoReset(DbMemCtxT *curCtx);
Status ShmemBlockAlgoClear(DbMemCtxT *curCtx);
Status ShmemBlockAlgoDestroy(DbMemCtxT *curCtx);
void ShmemBlockAlgoDestroyForce(DbMemCtxT *curCtx);
Status ShmemBlockIsEmpty(DbMemCtxT *curCtx, bool *isEmpty);
uint64_t ShmemBlockPhySizePeak(DbMemCtxT *ctx);
void ShmemBlockResetPhySizePeak(DbMemCtxT *ctx);
uint64_t ShmemBlockGetAndResetPeak(DbMemCtxT *ctx);

/* utlis */
Status InitDefaultBlockAlgoParam(DbMemCtxArgsT *ctxArgs);
void DestoryBlockAlgoParam(DbMemCtxArgsT *ctxArgs, bool isUserConfig);

#ifdef __cplusplus
}
#endif

#endif /* DB_SHMEM_BLOCKPOOL_ALGO_H */
