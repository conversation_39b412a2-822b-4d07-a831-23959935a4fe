/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: db_shmem_block_algo.c
 * Description: source file for common shared memory block algorithm
 * Author: jinfanglin
 * Create: 2020-8-19
 */

#include "db_shmem_blockpool_algo.h"
#include <securec.h>
#include <execinfo.h>
#include "db_top_shmem_ctx.h"
#include "db_mem_log.h"

static const DbShmemCtxMethodsT g_gmdbShmemBlockAlgoMethods = {ShmemBlockAlgoAlloc, ShmemBlockAlgoFree,
    ShmemBlockAlgoReset, ShmemBlockAlgoClear, ShmemBlockAlgoDestroy, ShmemBlockAlgoDestroyForce, ShmemBlockIsEmpty,
    ShmemBlockPhySizePeak, ShmemBlockResetPhySizePeak, ShmemBlockGetAndResetPeak};

const DbShmemCtxMethodsT *GetShmemBlockAlgoMethods(void)
{
    return &g_gmdbShmemBlockAlgoMethods;
}

static void ShmemBlockAlgoInitAttr(DbMemCtxT *curCtx, BlkPoolAttributeT *attr, const AlgoParamT *algoParam)
{
    DB_POINTER3(curCtx, attr, algoParam);
    attr->baseSize = algoParam->blockParam->baseSize;
    attr->stepSize = algoParam->blockParam->stepSize;
    attr->maxSize = algoParam->blockParam->maxSize;
    attr->isReused = algoParam->blockParam->isReused;
    attr->blkPoolType = algoParam->blockParam->blkPoolType;
    attr->isHugePage = algoParam->blockParam->isHugePage;
    attr->allowBigChunk = algoParam->blockParam->allowBigChunk;
    attr->groupId = curCtx->groupId;
    attr->maxTotalPhySize = curCtx->maxTotalPhySize;
    attr->permission = DbGetShmPermission();  // Passing the permission in HPE scenario
    attr->instanceId = DbGetProcGlobalId();
    attr->alignSize = BLK_ALIGN_SIZE;
    attr->staticCtx = curCtx->ctxId < DB_START_SPECIAL_CTX_ID;
}

static void InitBlockAlgoByParams(DbShmemBlockAlgoT *pShmemBlockAlgo, const AlgoParamT *algoParam)
{
    pShmemBlockAlgo->baseSize = algoParam->blockParam->baseSize;
    pShmemBlockAlgo->stepSize = algoParam->blockParam->stepSize;
    pShmemBlockAlgo->maxSize = (uint32_t)algoParam->blockParam->maxSize;
    pShmemBlockAlgo->permission = algoParam->blockParam->permission;
    pShmemBlockAlgo->allocCount = 0;
    pShmemBlockAlgo->freeCount = 0;
    pShmemBlockAlgo->blkPoolShmPtr = DB_INVALID_SHMPTR;
}

Status ShmemBlockAlgoInit(DbMemCtxT *curCtx, const AlgoParamT *algoParam)
{
    DB_POINTER3(curCtx, algoParam, algoParam->blockParam);
    Status ret = GMERR_OK;
    curCtx->methodType = ALGO_BLOCK;
    DbShmemBlockAlgoT *pShmemBlockAlgo = &((DbBlockShmemCtxT *)curCtx)->shmemBlockAlgo;
    pShmemBlockAlgo->key = ConstructPoolKeyByCtxId(curCtx->instanceId, curCtx->ctxId);
    uint32_t adptBlkParamSize = (uint32_t)(sizeof(BlkPoolAttributeT));
    BlkPoolAttributeT *attr = (BlkPoolAttributeT *)DB_MALLOC(adptBlkParamSize);
    if (attr == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Init block algo, size: %" PRIu32 ".", adptBlkParamSize);
        return GMERR_OUT_OF_MEMORY;
    }
    /* init block pool parameters */
    InitBlockAlgoByParams(pShmemBlockAlgo, algoParam);

#ifdef DB_MEM_TRACE
    // init list header to null
    pShmemBlockAlgo->allocChunks = DB_INVALID_SHMPTR;
#endif
    ShmemBlockAlgoInitAttr(curCtx, attr, algoParam);

    pShmemBlockAlgo->blkPoolShmPtr = DbAdptBlockPoolCreate(pShmemBlockAlgo->key, attr);
    DB_FREE(attr);
    if (SECUREC_UNLIKELY(!DbIsShmPtrValid(pShmemBlockAlgo->blkPoolShmPtr))) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Init block pool, inv shmPtr.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if ((ret = InsertShmemPtMap(curCtx)) != GMERR_OK) {
        goto EXIT;
    }
    /* Add ctx to shmem group */
    if ((ret = DbAdptAddMember2ShmemGroup(pShmemBlockAlgo->blkPoolShmPtr, curCtx->groupId)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Add ctx %s to shmem group %" PRIu64 ".", curCtx->ctxName, curCtx->groupId);
        goto EXIT;
    }
    return GMERR_OK;
EXIT:
    // ctx创建失败释放资源，此时没有客户端attach过pool，不会失败。
    (void)DbAdptBlockPoolDestroy(pShmemBlockAlgo->blkPoolShmPtr);
    return ret;
}

// ShmemBlockAlgoInit成功之后，后续异常分支需要调用该函数释放资源:释放blkPoolMgr。
void ShmemBlockAlgoUnInit(DbMemCtxT *curCtx)
{
    DbShmemBlockAlgoT *pShmemBlockAlgo = &((DbBlockShmemCtxT *)curCtx)->shmemBlockAlgo;
    // ctx创建失败释放资源，此时没有客户端attach过pool，不会失败。
    (void)DbAdptBlockPoolDestroy(pShmemBlockAlgo->blkPoolShmPtr);
}

Status InitDefaultBlockAlgoParam(DbMemCtxArgsT *ctxArgs)
{
    if (ctxArgs->algoParam != NULL) {
        return GMERR_OK;
    }
    ctxArgs->algoParam = (AlgoParamT *)DB_MALLOC(sizeof(AlgoParamT));
    if (ctxArgs->algoParam == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "init shmem algo param.");
        return GMERR_OUT_OF_MEMORY;
    }

    uint32_t blkParamSize = (uint32_t)(sizeof(DbBlockMemParamT));
    DbBlockMemParamT *blockParam = ctxArgs->algoParam->blockParam = (DbBlockMemParamT *)DB_MALLOC(blkParamSize);
    if (blockParam == NULL) {
        DB_FREE(ctxArgs->algoParam);
        ctxArgs->algoParam = NULL;
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "init shmem block algo param.");
        return GMERR_OUT_OF_MEMORY;
    }
    blockParam->isHugePage = false;
    blockParam->baseSize = SHM_BLOCK_POOL_BASE_SIZE;
    blockParam->stepSize = SHM_BLOCK_POOL_STEP_SIZE;
    blockParam->maxSize = SHM_BLOCK_POOL_MAX_SIZE;
    blockParam->isReused = false;
    blockParam->allowBigChunk = false;
    blockParam->blkPoolType = BLK_NORMAL;

    return GMERR_OK;
}

void DestoryBlockAlgoParam(DbMemCtxArgsT *ctxArgs, bool isUserConfig)
{
    if (!isUserConfig) {
        if (ctxArgs->algoParam != NULL) {
            if (ctxArgs->algoParam->blockParam != NULL) {
                DB_FREE(ctxArgs->algoParam->blockParam);
                ctxArgs->algoParam->blockParam = NULL;
            }
            DB_FREE(ctxArgs->algoParam);
            ctxArgs->algoParam = NULL;
        }
    }
}

void *DbCreateBlockPoolShmemCtx(DbMemCtxT *parent, const char *ctxName, DbMemCtxArgsT *ctxArgs)
{
    // Check the ctx deleteMgr first and delete what is ready to delete.
    // This would release ctx pool and recycle ctxId.
    // DbShmemCtxGlobalLock also performed inside.
    DbInstanceHdT dbInstance = parent->dbIns;
    DbShmemCtxDetachMgrScanSrv(dbInstance);

    if (SECUREC_UNLIKELY(ctxArgs == NULL)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Args is null-create blkPoolCtx.");
        return NULL;
    }
    DbShmemCtxGlobalLock();
    Status ret = CheckShmemCtxValid(parent);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create blockPool memctx.");
        goto EXIT;
    }

    bool isUserConfig = (ctxArgs->algoParam != NULL);
    if (!IsShmemCtxIdValid(ctxArgs->ctxId, parent->instanceId)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "create blockPool memctx.");
        goto EXIT;
    }

    ctxArgs->init = ShmemBlockAlgoInit;
    ctxArgs->uninit = ShmemBlockAlgoUnInit;
    ctxArgs->ctxSize = (uint32_t)sizeof(DbBlockShmemCtxT);
    ctxArgs->algoType = ALGO_BLOCK;
    ctxArgs->memType = DB_SHARED_MEMORY;  // must be shared memory type
    ctxArgs->instanceId = parent->instanceId;

    if (InitDefaultBlockAlgoParam(ctxArgs) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "create blockPool memctx.");
        goto EXIT;
    }

    DbMemCtxT *blockShmemCtx = (DbMemCtxT *)CreateMemCtx(parent, ctxName, true, ctxArgs);
    // 销毁用于创建memctx的临时内存
    DestoryBlockAlgoParam(ctxArgs, isUserConfig);
    DbShmemCtxGlobalUnlock();
    if (blockShmemCtx == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "create blockPool memctx.");
        return NULL;
    }

    // register ctxid and shmem pointer to top memory context
    ret = RegCtxIdToTopShmCtx(blockShmemCtx->ctxId, blockShmemCtx->shmPtrSelf, ctxArgs->instanceId);
    if (ret != GMERR_OK) {
        DbDeleteShmemCtx(blockShmemCtx);
        return NULL;
    }
    MEM_RUN_LINK_LOG(MEM_RUN_LINK_LOG_SHMEMCTX,
        "Create shmem ctxId %" PRIu32 ", ctxName: %s, ptNo %" PRIu32 ", key %" PRIu32 ". DB attachCnt %" PRIu32 ".",
        blockShmemCtx->ctxId, blockShmemCtx->ctxName, ShmemCtxGetPtNo(blockShmemCtx),
        ConstructPoolKeyByCtxId(parent->instanceId, blockShmemCtx->ctxId), blockShmemCtx->attachCount);
    return (void *)blockShmemCtx;
EXIT:
    DbShmemCtxGlobalUnlock();
    return NULL;
}

#ifdef DB_MEM_TRACE
// Put reused chunks into head of allocChunks list.
void LinkShmemChunkTraceInfo(
    DbMemCtxT *ctx, DbShmemBlockAlgoT *pShmemBlockAlgo, ShmemPtrT shmPtrMemTraceInfo, uint32_t allocSize)
{
    if (!ctx->enableMemTrace) {
        return;
    }
    ShmChunkTraceInfoT *pChunkTraceInfo = (ShmChunkTraceInfoT *)DbShmPtrToAddr(shmPtrMemTraceInfo);
    if (SECUREC_UNLIKELY(pChunkTraceInfo == NULL)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Link shmem trace info. Current chunk ptr inv.");
        return;
    }
    pChunkTraceInfo->pid = DbAdptGetpid();
    pChunkTraceInfo->stackSize = backtrace(pChunkTraceInfo->callStack, MEM_ALLOC_CALL_STACK_MAX_SIZE);
    pChunkTraceInfo->time = time(NULL);
    pChunkTraceInfo->size = allocSize;
    pChunkTraceInfo->traceReserve = 0;

    if (!DbIsShmPtrValid(pShmemBlockAlgo->allocChunks)) {
        pShmemBlockAlgo->allocChunks = shmPtrMemTraceInfo;
        pChunkTraceInfo->prevChunk = DB_INVALID_SHMPTR;
        pChunkTraceInfo->nextChunk = DB_INVALID_SHMPTR;
    } else {
        pChunkTraceInfo->nextChunk = pShmemBlockAlgo->allocChunks;
        ShmChunkTraceInfoT *pNextChunkTraceInfo = (ShmChunkTraceInfoT *)DbShmPtrToAddr(pChunkTraceInfo->nextChunk);
        if (SECUREC_UNLIKELY(pNextChunkTraceInfo == NULL)) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Link shmem trace info. Next chunk ptr inv.");
            return;
        }
        pNextChunkTraceInfo->prevChunk = shmPtrMemTraceInfo;
        pShmemBlockAlgo->allocChunks = shmPtrMemTraceInfo;
        pChunkTraceInfo->prevChunk = DB_INVALID_SHMPTR;
    }
}

void DelinkShmemChunkTraceInfo(DbMemCtxT *ctx, DbShmemBlockAlgoT *pShmemBlockAlgo, ShmemPtrT shmPtrMemTraceInfo)
{
    if (!ctx->enableMemTrace) {
        return;
    }
    ShmChunkTraceInfoT *pChunkTraceInfo = (ShmChunkTraceInfoT *)DbShmPtrToAddr(shmPtrMemTraceInfo);
    if (SECUREC_UNLIKELY(pChunkTraceInfo == NULL)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "delink shmem trace info. Current chunk ptr inv.");
        return;
    }
    if (IsShmemPtrEqual(pShmemBlockAlgo->allocChunks, shmPtrMemTraceInfo)) {
        pShmemBlockAlgo->allocChunks = pChunkTraceInfo->nextChunk;
        if (DbIsShmPtrValid(pChunkTraceInfo->nextChunk)) {
            ShmChunkTraceInfoT *pNextChunkTraceInfo = (ShmChunkTraceInfoT *)DbShmPtrToAddr(pChunkTraceInfo->nextChunk);
            if (SECUREC_UNLIKELY(pNextChunkTraceInfo == NULL)) {
                DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "delink shmem trace info. Next chunk ptr inv.");
                return;
            }
            pNextChunkTraceInfo->prevChunk = DB_INVALID_SHMPTR;
        }
    } else {
        if (DbIsShmPtrValid(pChunkTraceInfo->nextChunk)) {
            ShmChunkTraceInfoT *pNextChunkTraceInfo = (ShmChunkTraceInfoT *)DbShmPtrToAddr(pChunkTraceInfo->nextChunk);
            if (SECUREC_UNLIKELY(pNextChunkTraceInfo == NULL)) {
                DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "delink shmem trace info. Next chunk ptr inv.");
                return;
            }
            pNextChunkTraceInfo->prevChunk = pChunkTraceInfo->prevChunk;
        }
        ShmChunkTraceInfoT *pPrevChunkTraceInfo = (ShmChunkTraceInfoT *)DbShmPtrToAddr(pChunkTraceInfo->prevChunk);
        if (SECUREC_UNLIKELY(pPrevChunkTraceInfo == NULL)) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "delink shmem trace info. Prev chunk ptr inv.");
            return;
        }
        pPrevChunkTraceInfo->nextChunk = pChunkTraceInfo->nextChunk;
    }
}
#endif

ShmemPtrT ShmemBlockAlgoAlloc(DbMemCtxT *curCtx, size_t size)
{
    DB_POINTER(curCtx);
    DbShmemBlockAlgoT *pShmemBlockAlgo = &((DbBlockShmemCtxT *)curCtx)->shmemBlockAlgo;
    DB_PREFETCH(&(pShmemBlockAlgo->allocCount), (int32_t)DB_PREFECH_WRITE, (int32_t)DB_PREFECH_LOCALITY_LOW);
    uint32_t allocSize = (uint32_t)size;
#ifdef DB_MEM_TRACE
    // alter size, add the space for memtrace info struct
    allocSize += sizeof(ShmChunkTraceInfoT);
#endif
    ShmemPtrT newShmemPtr = DbAdptBlockPoolAlloc(pShmemBlockAlgo->blkPoolShmPtr, allocSize);
    if (DbIsShmPtrValid(newShmemPtr)) {
        (void)DbAtomicInc(&(pShmemBlockAlgo->allocCount));
        curCtx->isReset = false;
#ifdef DB_MEM_TRACE
        // initialize memtrace info struct and add into list
        LinkShmemChunkTraceInfo(curCtx, pShmemBlockAlgo, newShmemPtr, size);
        // alter newShmemPtr, plus sizeof memtrace info struct
        newShmemPtr.offset += sizeof(ShmChunkTraceInfoT);
#endif
    } else {
        uint64_t totalPhySize = DbAdptBlockPoolGetTotalPhySize(pShmemBlockAlgo->blkPoolShmPtr);
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
            "alloc from blockPool %s. CtxId: %" PRIu32 ", totalPhySize: %" PRIu64 ", allocSize: "
            "%" PRIu32 "; baseSize: %" PRIu32 ", stepSize: %" PRIu32 ", maxSize: %" PRIu32 ".",
            curCtx->ctxName, curCtx->ctxId, totalPhySize, (uint32_t)size, pShmemBlockAlgo->baseSize,
            pShmemBlockAlgo->stepSize, pShmemBlockAlgo->maxSize);
    }
    return newShmemPtr;
}

Status ShmemBlockAlgoFree(DbMemCtxT *curCtx, ShmemPtrT ptr)
{
    DB_POINTER(curCtx);
    DbShmemBlockAlgoT *pShmemBlockAlgo = &((DbBlockShmemCtxT *)curCtx)->shmemBlockAlgo;
    DB_PREFETCH(&(pShmemBlockAlgo->freeCount), (int32_t)DB_PREFECH_WRITE, (int32_t)DB_PREFECH_LOCALITY_LOW);
    ShmemPtrT chunkHeadPtr = {.offset = ptr.offset, .segId = ptr.segId};
#ifdef DB_MEM_TRACE
    // alter ptr, minus sizeof stack info struct
    chunkHeadPtr.offset -= sizeof(ShmChunkTraceInfoT);
    // remove this chunk from list
    DelinkShmemChunkTraceInfo(curCtx, pShmemBlockAlgo, chunkHeadPtr);
#endif
    Status ret = DbAdptBlockPoolFree(pShmemBlockAlgo->blkPoolShmPtr, chunkHeadPtr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "free chunk, ctxId: %" PRIu32 ", ctxName: %s, segId: %" PRIu32 ", offset: %" PRIu32 ".",
            curCtx->ctxId, curCtx->ctxName, ptr.segId, ptr.offset);
        return ret;
    }

    (void)DbAtomicInc(&(pShmemBlockAlgo->freeCount));
    return GMERR_OK;
}

// 调用钩子函数前对ctx加锁。
void ShmemBlockAlgoReset(DbMemCtxT *curCtx)
{
    DB_POINTER(curCtx);
    DbShmemBlockAlgoT *pShmemBlockAlgo = &((DbBlockShmemCtxT *)curCtx)->shmemBlockAlgo;
#ifdef DB_MEM_TRACE
    // set list header to null
    pShmemBlockAlgo->allocChunks = DB_INVALID_SHMPTR;
#endif
    DbAdptBlockPoolReset(pShmemBlockAlgo->blkPoolShmPtr);

    pShmemBlockAlgo->allocCount = 0;
    pShmemBlockAlgo->freeCount = 0;
    ((DbMemCtxT *)curCtx)->totalAllocSize = 0;
}

// 调用钩子函数前对ctx加锁。
Status ShmemBlockAlgoClear(DbMemCtxT *curCtx)
{
    DB_POINTER(curCtx);
    DbShmemBlockAlgoT *pShmemBlockAlgo = &((DbBlockShmemCtxT *)curCtx)->shmemBlockAlgo;
#ifdef DB_MEM_TRACE
    // set list header to null
    pShmemBlockAlgo->allocChunks = DB_INVALID_SHMPTR;
#endif
    Status ret = DbAdptBlockPoolClear(pShmemBlockAlgo->blkPoolShmPtr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "clear shmemCtx, ctxId %" PRIu32 ", ctxName %s.", curCtx->ctxId, curCtx->ctxName);
        return ret;
    }

    pShmemBlockAlgo->allocCount = 0;
    pShmemBlockAlgo->freeCount = 0;
    ((DbMemCtxT *)curCtx)->totalAllocSize = 0;
    return GMERR_OK;
}

Status ShmemBlockAlgoDestroy(DbMemCtxT *curCtx)
{
    DB_POINTER(curCtx);
    DbShmemBlockAlgoT *pShmemBlockAlgo = &((DbBlockShmemCtxT *)curCtx)->shmemBlockAlgo;
#ifdef DB_MEM_TRACE
    // set list header to null
    pShmemBlockAlgo->allocChunks = DB_INVALID_SHMPTR;
#endif
    Status ret = DbAdptBlockPoolDestroy(pShmemBlockAlgo->blkPoolShmPtr);
    if (ret != GMERR_OK) {
        return ret;
    }
    ((DbMemCtxT *)curCtx)->totalAllocSize = 0;
    return GMERR_OK;
}

void ShmemBlockAlgoDestroyForce(DbMemCtxT *curCtx)
{
    DB_POINTER(curCtx);  // Checked by caller.
    DbShmemBlockAlgoT *pShmemBlockAlgo = &((DbBlockShmemCtxT *)curCtx)->shmemBlockAlgo;
    DbAdptBlockPoolDestroyForce(pShmemBlockAlgo->blkPoolShmPtr);
#ifdef DB_MEM_TRACE
    // set list header to null
    pShmemBlockAlgo->allocChunks = DB_INVALID_SHMPTR;
#endif
    ((DbMemCtxT *)curCtx)->totalAllocSize = 0;
}

Status ShmemBlockIsEmpty(DbMemCtxT *curCtx, bool *isEmpty)
{
    DB_POINTER2(curCtx, isEmpty);
    DbShmemBlockAlgoT *pShmemBlockAlgo = &((DbBlockShmemCtxT *)curCtx)->shmemBlockAlgo;
    *isEmpty = (pShmemBlockAlgo->allocCount == pShmemBlockAlgo->freeCount) ? true : false;
    return GMERR_OK;
}

// 上层调用者加锁
uint64_t ShmemBlockPhySizePeak(DbMemCtxT *ctx)
{
    DbBlockShmemCtxT *pCtx = (DbBlockShmemCtxT *)ctx;
    DbShmemBlockAlgoT *pShmemBlockAlgo = &pCtx->shmemBlockAlgo;
    return DbAdptGetBlockPoolPhySizePeak(pShmemBlockAlgo->blkPoolShmPtr);
}

// 上层调用者加锁
void ShmemBlockResetPhySizePeak(DbMemCtxT *ctx)
{
    DbBlockShmemCtxT *pCtx = (DbBlockShmemCtxT *)ctx;
    DbShmemBlockAlgoT *pShmemBlockAlgo = &pCtx->shmemBlockAlgo;
    DbAdptBlockPoolResetPhySizePeak(pShmemBlockAlgo->blkPoolShmPtr);
}

uint64_t ShmemBlockGetAndResetPeak(DbMemCtxT *ctx)
{
    DbBlockShmemCtxT *pCtx = (DbBlockShmemCtxT *)ctx;
    DbShmemBlockAlgoT *pShmemBlockAlgo = &pCtx->shmemBlockAlgo;
    return DbAdptBlockPoolGetAndResetPeak(pShmemBlockAlgo->blkPoolShmPtr);
}

DbBlockMemParamT DbShmCtxGetBlockPoolParams(const DbMemCtxT *ctx)
{
    DB_POINTER(ctx);
    DB_ASSERT(ctx->methodType == ALGO_BLOCK);
    const DbShmemBlockAlgoT *pShmemBlockAlgo = &((const DbBlockShmemCtxT *)(const void *)ctx)->shmemBlockAlgo;
    DbBlockMemParamT params = {0};
    params.baseSize = pShmemBlockAlgo->baseSize;
    params.stepSize = pShmemBlockAlgo->stepSize;
    params.maxSize = pShmemBlockAlgo->maxSize;
    return params;
}

#ifdef DB_MEM_TRACE
ShmemPtrT DbShmemCtxGetBlockPoolChunks(const DbMemCtxT *ctx)
{
    DB_POINTER(ctx);
    DB_ASSERT(ctx->methodType == ALGO_BLOCK);
    const DbShmemBlockAlgoT *pShmemBlockAlgo = &((const DbBlockShmemCtxT *)(const void *)ctx)->shmemBlockAlgo;
    return pShmemBlockAlgo->allocChunks;
}
#endif
