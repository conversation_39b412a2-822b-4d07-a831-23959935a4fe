/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: db_rwlatch.c
 * Description: 提供读写闩锁能力
 * Author: <PERSON><PERSON><PERSON><PERSON>jian
 * Create: 2021/4/25
 */

#include "db_rwlatch.h"
#include "db_utils.h"
#include "db_config.h"
#include "adpt_sleep.h"
#include "db_crash_debug.h"

#ifdef __cplusplus
extern "C" {
#endif

#define DB_RWLATCH_SLEEP_DELAY 100u
#define DB_TRY_LATCH_COUNT 100u
#define DB_WAIT_FOREVER DB_MAX_UINT32                // 永久等待
#define DB_RWLATCH_TIME_TO_LOG_WARN_INFO 10000000    // 10S
#define DB_RWLATCH_TIME_TO_LOG_STACK_INFO 100000000  // 100S
#define DB_RWLATCH_TIME_TO_LOG_ERROR_INFO 100000000  // 100s

static uint32_t g_gmdbRwlatchPid = 0;

typedef enum DbRWLatchStatus {
    RWLATCH_STATUS_W_ACQUIRE_IX = 0,
    RWLATCH_STATUS_R_ACQUIRE_W_FINISH,
    RWLATCH_STATUS_W_ACQUIRE_R_FINISH,
} DbRWLatchStatusE;

typedef struct DbRWLatchCheckInterval {
    bool isTriggerTimeoutWarn;
    uint8_t reserved1;
    uint16_t reserved2;
    uint32_t warnLogTime;
    uint32_t errLogTime;
    uint32_t checkPidTime;
    uint32_t stackTime;
} DbRWLatchCheckIntervalT;

inline static bool DbRWLatchAcquireIX(DbLatchT *latch)
{
    return DbAtomicExchange32WithAcquireBarrier(&latch->pid, 0, g_gmdbRwlatchPid);
}

inline static bool DbIsTimeout(uint32_t timeout, uint32_t sleepTimes)
{
    if (timeout == DB_WAIT_FOREVER) {
        return false;
    }
    return sleepTimes > timeout;
}

static bool DbRWLatchIsStatusReady(DbLatchT *latch, DbRWLatchStatusE stat)
{
    bool isReady = false;
    switch (stat) {
        // 写写冲突，当前写者继续请求获取意向排他锁
        case RWLATCH_STATUS_W_ACQUIRE_IX:
        // 读写冲突，当前读者等待写者完成
        case RWLATCH_STATUS_R_ACQUIRE_W_FINISH:
            isReady = !DbRWLatchIsWriterFlag(latch);
            break;
        // 读写冲突，当前写者已获取意向排他锁，等待已拿读锁的读者读完
        case RWLATCH_STATUS_W_ACQUIRE_R_FINISH:
            isReady = (latch->latchMode == (uint32_t)DB_LATCH_IDLE);
            break;
        default:
            // 不应该走到这个分支
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "unknown latch status %" PRIu32, (uint32_t)stat);
            break;
    }
    return isReady;
}

inline static uint32_t DbRWLatchOwner(const DbLatchT *latch)
{
    return (uint32_t)latch->spinlock.lock;
}

inline static void DbRWLatchRecoverMode(DbLatchT *latch, uint32_t owner)
{
    // 服务端为默认owner，可能在加写锁过程中，加spinlock后解spinlock前挂死
    if (DbRWLatchOwner(latch) != DB_SPINLOCK_OWNER) {
        DbSpinLockOwner(owner, &latch->spinlock);
    }
    if (DbRWLatchIsWriterFlag(latch)) {
        latch->latchMode = (uint32_t)DB_LATCH_IDLE;
        latch->pid = 0;
    }
    DbSpinUnlock(&latch->spinlock);
}

static void DbRWLatchInitCheckInterval(DbRWLatchCheckIntervalT *checkInterval)
{
    checkInterval->isTriggerTimeoutWarn = false;
    checkInterval->warnLogTime = DB_RWLATCH_TIME_TO_LOG_WARN_INFO;
    checkInterval->errLogTime = DB_RWLATCH_TIME_TO_LOG_ERROR_INFO;
    checkInterval->checkPidTime = PID_RWLOCK_CHECK_INTERVAL;
    checkInterval->stackTime = DB_RWLATCH_TIME_TO_LOG_STACK_INFO;
}

static void TimeToLogLockInfo(DbLatchT *latch, uint32_t totalWaitTimeUs, DbRWLatchCheckIntervalT *checkInterval,
    uint32_t timeoutUs, DbRWLatchStatusE stat)
{
    if (totalWaitTimeUs > timeoutUs) {
        // 用户设置超时时间，超时
        DB_LOG_WARN(GMERR_LOCK_NOT_AVAILABLE,
            "acquire lock timeout, timeout %" PRIu32 " us, waittime %" PRIu32 " us, lock %" PRIu32 " pid %" PRIu32
            " mode %" PRIu32 " cnt %" PRIu32 " %" PRIu32 " stat %" PRIu32 "",
            timeoutUs, totalWaitTimeUs, latch->spinlock.lock, latch->pid, latch->latchMode, latch->serverCnt,
            latch->clientCnt, (uint32_t)stat);
        return;
    }

    if (totalWaitTimeUs > checkInterval->warnLogTime) {
        // 每10s打印一次warn日志
        DB_LOG_WARN(GMERR_LOCK_NOT_AVAILABLE,
            "acquire lock wait too long, waittime %" PRIu32 " us, lock %" PRIu32 " pid %" PRIu32 " mode %" PRIu32
            " cnt %" PRIu32 " %" PRIu32 " stat %" PRIu32 "",
            totalWaitTimeUs, latch->spinlock.lock, latch->pid, latch->latchMode, latch->serverCnt, latch->clientCnt,
            (uint32_t)stat);
        checkInterval->warnLogTime += DB_RWLATCH_TIME_TO_LOG_WARN_INFO;
    }

    if (totalWaitTimeUs > checkInterval->errLogTime) {
        // 每100s打印一次ER日志
        DB_LOG_ERROR_UNFOLD(GMERR_LOCK_NOT_AVAILABLE,
            "potential deadlock: acquire lock wait too long, waittime %" PRIu32 " us, lock %" PRIu32 " pid %" PRIu32
            " mode %" PRIu32 " cnt %" PRIu32 " %" PRIu32 " stat %" PRIu32 "",
            totalWaitTimeUs, latch->spinlock.lock, latch->pid, latch->latchMode, latch->serverCnt, latch->clientCnt,
            (uint32_t)stat);
        checkInterval->errLogTime += DB_RWLATCH_TIME_TO_LOG_ERROR_INFO;
        checkInterval->isTriggerTimeoutWarn = true;
    }

    if (totalWaitTimeUs > checkInterval->stackTime) {
        // 每100s打印一次堆栈
        DbLogCurrentStackTrace();
        checkInterval->stackTime += DB_RWLATCH_TIME_TO_LOG_STACK_INFO;
        checkInterval->isTriggerTimeoutWarn = true;
    }
}

static bool DbRWLatchWait(DbLatchT *latch, uint32_t owner, uint32_t *timeoutUs, DbRWLatchStatusE stat)
{
    uint64_t startTime = DbRdtsc();
    DbRWLatchCheckIntervalT checkInterval;
    DbRWLatchInitCheckInterval(&checkInterval);
    uint32_t totalWaitTimeUs = 0;
    uint32_t tryTimes = 0;
    while (!DbRWLatchIsStatusReady(latch, stat)) {
        DbIdleLoop(1);
        tryTimes++;
        CRASHPOINT(DB_CRASH_EVENT_WLOCK, DB_CRASH_STATE_LATCH_WAIT);
        CRASHPOINT(DB_CRASH_EVENT_RLOCK, DB_CRASH_STATE_LATCH_WAIT);
        totalWaitTimeUs = (uint32_t)DbToUseconds(DbRdtsc() - startTime);
        TimeToLogLockInfo(latch, totalWaitTimeUs, &checkInterval, *timeoutUs, stat);
        if (DbIsTimeout(*timeoutUs, totalWaitTimeUs)) {
            return false;
        }
        if (stat == RWLATCH_STATUS_R_ACQUIRE_W_FINISH && totalWaitTimeUs > checkInterval.checkPidTime) {
            if (latch->pid != 0 && !DbAdptProcessIsExist(latch->pid)) {
                DB_LOG_WARN(GMERR_INTERNAL_ERROR,
                    "COM: read latch: pid %" PRIu32 " not exist, lock %" PRIu32 " mode %" PRIu32 " cnt %" PRIu32
                    " %" PRIu32 "",
                    latch->pid, latch->spinlock.lock, latch->latchMode, latch->serverCnt, latch->clientCnt);
                DbRWLatchRecoverMode(latch, owner);
                return false;
            }
            checkInterval.checkPidTime += PID_RWLOCK_CHECK_INTERVAL;
        }
        if (tryTimes >= DB_TRY_LATCH_COUNT) {
            tryTimes = 0;
            DbUsleep(DB_RWLATCH_SLEEP_DELAY);
        }
    }
    if (checkInterval.isTriggerTimeoutWarn) {
        // warn消除，此处打印warn级别日志
        DB_LOG_WARN_UNFOLD(GMERR_OK,
            "INFO: Ending the RWLatch wait after timeout alarm is triggered, waittime %" PRIu32 " us, lock %" PRIu32
            " pid %" PRIu32 " mode %" PRIu32 " cnt %" PRIu32 " %" PRIu32 " stat %" PRIu32 "",
            totalWaitTimeUs, latch->spinlock.lock, latch->pid, latch->latchMode, latch->serverCnt, latch->clientCnt,
            (uint32_t)stat);
    }
    if (*timeoutUs == DB_WAIT_FOREVER) {
        return true;
    }
    if (totalWaitTimeUs <= *timeoutUs) {
        *timeoutUs -= totalWaitTimeUs;
    } else {
        *timeoutUs = 0;
    }
    return true;
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
// 为了避免上层调用者取址栈变量作为函数参数触发的栈保护对性能的影响，对函数封装一个按值传递的接口
NO_INLINE static void DbRWLatchWaitForever(DbLatchT *latch, uint32_t owner)
{
    uint32_t timeoutUs = DB_WAIT_FOREVER;
    (void)DbRWLatchWait(latch, owner, &timeoutUs, RWLATCH_STATUS_R_ACQUIRE_W_FINISH);
}
#endif /* FEATURE_SIMPLEREL */

bool DbRWLatchTimedREnter(DbLatchT *latch, uint32_t owner, uint32_t timeoutUs)
{
    do {
        if (DbRWLatchIsWriterFlag(latch)) {
            if (!DbRWLatchWait(latch, owner, &timeoutUs, RWLATCH_STATUS_R_ACQUIRE_W_FINISH)) {
                break;
            }
        }
        if (!DbSpinTimedLockOwner(owner, &latch->spinlock, &timeoutUs)) {
            break;
        }

        // double check writer
        if (SECUREC_UNLIKELY(DbRWLatchIsWriterFlag(latch))) {
            DbSpinUnlock(&latch->spinlock);
            continue;
        }

        DB_ASSERT(latch->latchMode == (uint32_t)DB_LATCH_IDLE || latch->latchMode == (uint32_t)DB_LATCH_S);
        latch->latchMode = (uint32_t)DB_LATCH_S;
        latch->serverCnt++;
        return true;
    } while (true);
    return false;
}

// 配对函数:DbRWLatchLeave
void DbRWLatchREnter(DbLatchT *latch, uint32_t owner)
{
    do {
        uint32_t timeoutUs = DB_WAIT_FOREVER;
        if (DbRWLatchIsWriterFlag(latch)) {
            (void)DbRWLatchWait(latch, owner, &timeoutUs, RWLATCH_STATUS_R_ACQUIRE_W_FINISH);
        }
        DbSpinLockOwner(owner, &latch->spinlock);
        // double check writer
        if (SECUREC_UNLIKELY(DbRWLatchIsWriterFlag(latch))) {
            DbSpinUnlock(&latch->spinlock);
            continue;
        }
        DB_ASSERT(latch->latchMode == (uint32_t)DB_LATCH_IDLE || latch->latchMode == (uint32_t)DB_LATCH_S);
        latch->latchMode = (uint32_t)DB_LATCH_S;
        latch->serverCnt++;
        return;
    } while (true);
}

bool DbRWLatchTryREnter(DbLatchT *latch, uint32_t owner)
{
    if (DbRWLatchIsWriterFlag(latch)) {
        return false;
    }
    if (!DbSpinTryLockOwner(owner, &latch->spinlock)) {
        return false;
    }
    if (SECUREC_UNLIKELY(DbRWLatchIsWriterFlag(latch))) {
        DbSpinUnlock(&latch->spinlock);
        return false;
    }
    DB_ASSERT(latch->latchMode == (uint32_t)DB_LATCH_IDLE || latch->latchMode == (uint32_t)DB_LATCH_S);
    latch->latchMode = (uint32_t)DB_LATCH_S;
    latch->serverCnt++;
    return true;
}

// 配对函数:DbRWLatchLeave
ALWAYS_INLINE_C void DbRWUnlatchREnter(DbLatchT *latch, uint32_t owner)
{
    DbSpinLockOwner(owner, &latch->spinlock);
    // 此时必须是DB_LATCH_S状态
    DB_ASSERT(latch->latchMode == (uint32_t)DB_LATCH_S);
    DB_ASSERT(latch->serverCnt > 0);
    latch->serverCnt--;
    if (latch->clientCnt == 0 && latch->serverCnt == 0) {
        latch->latchMode = (uint32_t)DB_LATCH_IDLE;
    }
}

inline static void DbRWlatchCheckPid(void)
{
#ifndef NDEBUG
    // debug模式下, 采用fork方式创建的子进程会继承父进程的全局变量, 会导致与实际pid不一致, 因此需要每次都获取进程pid
    // 可靠锁已经支持32位pid
    g_gmdbRwlatchPid = DbAdptGetpid();
#else
    if (SECUREC_UNLIKELY(g_gmdbRwlatchPid == 0)) {
        g_gmdbRwlatchPid = DbAdptGetpid();
    }
#endif
}

uint32_t DbRWlatchGetPid(void)
{
    if (SECUREC_UNLIKELY(g_gmdbRwlatchPid == 0)) {
        g_gmdbRwlatchPid = DbAdptGetpid();
    }

    return g_gmdbRwlatchPid;
}

bool DbRWLatchTimedWEnter(DbLatchT *latch, uint32_t owner, uint32_t timeoutUs)
{
    DbRWlatchCheckPid();
    while (!DbRWLatchAcquireIX(latch)) {
        if (!DbRWLatchWait(latch, owner, &timeoutUs, RWLATCH_STATUS_W_ACQUIRE_IX)) {
            return false;
        }
    }
    do {
        // 等待读者计数为0时加锁
        if (latch->latchMode != (uint32_t)DB_LATCH_IDLE) {
            if (!DbRWLatchWait(latch, owner, &timeoutUs, RWLATCH_STATUS_W_ACQUIRE_R_FINISH)) {
                DbAtomicSet(&latch->pid, 0);
                return false;
            }
        }
        if (!DbSpinTimedLockOwner(owner, &latch->spinlock, &timeoutUs)) {
            // 超时退出，重置writer标记位
            DbAtomicSet(&latch->pid, 0);
            break;
        }
        if (latch->latchMode == (uint32_t)DB_LATCH_IDLE) {
            // 加写锁成功，锁状态转换必为DB_LATCH_IDLE -> DB_LATCH_SERVER_X
            latch->latchMode = (uint32_t)DB_LATCH_SERVER_X;
            return true;
        }
        DbSpinUnlock(&latch->spinlock);
    } while (true);
    return false;
}

// 配对函数:DbRWLatchLeave
void DbRWLatchWEnter(DbLatchT *latch, uint32_t owner)
{
    DbRWlatchCheckPid();
    uint32_t timeoutUs = DB_WAIT_FOREVER;
    while (!DbRWLatchAcquireIX(latch)) {
        (void)DbRWLatchWait(latch, owner, &timeoutUs, RWLATCH_STATUS_W_ACQUIRE_IX);
    }
    do {
        // 等待读者计数为0时加锁
        if (latch->latchMode != (uint32_t)DB_LATCH_IDLE) {
            (void)DbRWLatchWait(latch, owner, &timeoutUs, RWLATCH_STATUS_W_ACQUIRE_R_FINISH);
        }
        DbSpinLockOwner(owner, &latch->spinlock);
        if (latch->latchMode == (uint32_t)DB_LATCH_IDLE) {
            // 加写锁成功，锁状态转换必为DB_LATCH_IDLE -> DB_LATCH_SERVER_X
            latch->latchMode = (uint32_t)DB_LATCH_SERVER_X;
            return;
        }
        DbSpinUnlock(&latch->spinlock);
    } while (true);
}

bool DbRWLatchTryWEnter(DbLatchT *latch, uint32_t owner)
{
    if (latch->latchMode != (uint32_t)DB_LATCH_IDLE) {
        return false;
    }
    DbRWlatchCheckPid();
    if (!DbAtomicExchange32WithAcquireBarrier(&latch->pid, 0, g_gmdbRwlatchPid)) {
        return false;
    }
    if (!DbSpinTryLockOwner(owner, &latch->spinlock)) {
        if (!DbAtomicExchange32WithAcquireBarrier(&latch->pid, g_gmdbRwlatchPid, 0)) {
            DB_ASSERT(false);
        }
        return false;
    }
    if (latch->latchMode != (uint32_t)DB_LATCH_IDLE) {
        latch->pid = 0;
        DbSpinUnlock(&latch->spinlock);
        return false;
    }
    // 加写锁成功，锁状态转换必为DB_LATCH_IDLE -> DB_LATCH_SERVER_X
    latch->latchMode = (uint32_t)DB_LATCH_SERVER_X;
    return true;
}

// 配对函数:DbRWLatchLeave
inline void DbRWUnlatchWEnter(DbLatchT *latch, uint32_t owner)
{
    DbSpinLockOwner(owner, &latch->spinlock);
    // 此时必须是DB_LATCH_SERVER_X模式, 防止重复解锁造成解锁混乱
    DB_ASSERT(latch->latchMode == (uint32_t)DB_LATCH_SERVER_X);
    latch->latchMode = (uint32_t)DB_LATCH_IDLE;
    latch->pid = 0;
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
/*
    锁模式和计数的恢复流程分为以下几种情况讨论:
    (1) 锁的模式为DB_LATCH_S：
        此时可能有0个或者多个客户端/服务器的线程持有这把锁的读锁，此时均需恢复客户端侧读锁的引用计数
    (2) 锁的模式为DB_LATCH_CLIENT_X：
        根据当前其他客户端线程持有这把锁的个数判断是否需要恢复锁模式
    (3) 锁的模式为DB_LATCH_SERVER_X：
        此时任何客户端线程都不应该持有这把锁，加断言验证
    (4) 锁的模式为DB_LATCH_IDLE:
        此时客户端/服务器都不应持有这把锁
*/
void DbLatchRecover(DbLatchT *latch, uint32_t clientHolderCnt, uint32_t latchAddrType, ShmemPtrT shmAddr)
{
    bool isZero = false;
    switch (latch->latchMode) {
        case DB_LATCH_S:
            latch->clientCnt = clientHolderCnt;
            break;
        case DB_LATCH_CLIENT_X:
            DB_LOG_WARN_UNFOLD(GMERR_LOCK_NOT_AVAILABLE,
                "Recover latch client x: %" PRIu32 ", pid: %" PRIu32 ", latchMode: %" PRIu32 ", cltCnt: %" PRIu32
                ", serverCnt: %" PRIu32 ", latchType: %" PRIu32 ", shm:(%" PRIu32 ", %" PRIu32 ")",
                latch->spinlock.lock, latch->pid, latch->latchMode, latch->clientCnt, latch->serverCnt, latchAddrType,
                shmAddr.segId, shmAddr.offset);
            DB_ASSERT(clientHolderCnt == 0);
            // 这把锁由当前这个异常客户端线程持有,需要恢复
            latch->latchMode = (uint32_t)DB_LATCH_IDLE;
            latch->pid = 0;
            isZero = DbRWLatchCountIsZero(latch);
            DB_ASSERT(isZero);
            break;
        case DB_LATCH_SERVER_X:
            DB_ASSERT(clientHolderCnt == 0);
            isZero = DbRWLatchCountIsZero(latch);
            DB_ASSERT(isZero);
            break;
        case DB_LATCH_IDLE:
            DB_ASSERT(clientHolderCnt == 0);
            isZero = DbRWLatchCountIsZero(latch);
            DB_ASSERT(isZero);
            break;
        default:
            break;
    }

    if (DbRWLatchCountIsZero(latch) && (latch->latchMode == (uint32_t)DB_LATCH_S)) {
        latch->latchMode = (uint32_t)DB_LATCH_IDLE;
    }
    DB_LOG_WARN_UNFOLD(GMERR_LOCK_NOT_AVAILABLE,
        "recover this lock info is owner: %" PRIu32 ", pid: %" PRIu32 ", latchMode: %" PRIu32 ", cltCnt: %" PRIu32
        ", serverCnt: %" PRIu32 ", latchType: %" PRIu32 ", shm:(%" PRIu32 ", %" PRIu32 ")",
        latch->spinlock.lock, latch->pid, latch->latchMode, latch->clientCnt, latch->serverCnt, latchAddrType,
        shmAddr.segId, shmAddr.offset);
}

bool DbRWLatchTimedREnterForClient(DbLatchT *latch, uint32_t owner, uint32_t timeoutUs)
{
    do {
        if (DbRWLatchIsWriterFlag(latch)) {
            if (!DbRWLatchWait(latch, owner, &timeoutUs, RWLATCH_STATUS_R_ACQUIRE_W_FINISH)) {
                break;
            }
        }
        if (!DbSpinTimedLockOwner(owner, &latch->spinlock, &timeoutUs)) {
            break;
        }
        CRASHPOINT(DB_CRASH_EVENT_RLOCK, DB_CRASH_STATE_LATCH_ADD_SPINLOCK);
        // double check writer
        if (SECUREC_UNLIKELY(DbRWLatchIsWriterFlag(latch))) {
            DbSpinUnlock(&latch->spinlock);
            continue;
        }
        DB_ASSERT(latch->latchMode == (uint32_t)DB_LATCH_IDLE || latch->latchMode == (uint32_t)DB_LATCH_S);
        latch->latchMode = (uint32_t)DB_LATCH_S;
        CRASHPOINT(DB_CRASH_EVENT_RLOCK, DB_CRASH_STATE_LATCH_UPDATE_S_MODE);
        latch->clientCnt++;
        CRASHPOINT(DB_CRASH_EVENT_RLOCK, DB_CRASH_STATE_LATCH_ADD_READER_COUNT);
        return true;
    } while (true);
    return false;
}

ALWAYS_INLINE_C void DbRWLatchREnterForClient(DbLatchT *latch, uint32_t owner)
{
    do {
        if (DbRWLatchIsWriterFlag(latch)) {
            DbRWLatchWaitForever(latch, owner);
        }
        DbSpinLockOwner(owner, &latch->spinlock);
        CRASHPOINT(DB_CRASH_EVENT_RLOCK, DB_CRASH_STATE_LATCH_ADD_SPINLOCK);
        // double check writer
        if (SECUREC_UNLIKELY(DbRWLatchIsWriterFlag(latch))) {
            DbSpinUnlock(&latch->spinlock);
            continue;
        }
        DB_ASSERT(latch->latchMode == (uint32_t)DB_LATCH_IDLE || latch->latchMode == (uint32_t)DB_LATCH_S);
        latch->latchMode = (uint32_t)DB_LATCH_S;
        CRASHPOINT(DB_CRASH_EVENT_RLOCK, DB_CRASH_STATE_LATCH_UPDATE_S_MODE);
        latch->clientCnt++;
        CRASHPOINT(DB_CRASH_EVENT_RLOCK, DB_CRASH_STATE_LATCH_ADD_READER_COUNT);
        return;
    } while (true);
}

ALWAYS_INLINE_C void DbRWUnlatchREnterForClient(DbLatchT *latch, uint32_t owner)
{
    DbSpinLockOwner(owner, &latch->spinlock);
    CRASHPOINT(DB_CRASH_EVENT_UN_RLOCK, DB_CRASH_STATE_LATCH_ADD_SPINLOCK);
    // 此时必须是DB_LATCH_S状态
    DB_ASSERT(latch->latchMode == (uint32_t)DB_LATCH_S);
    DB_ASSERT(latch->clientCnt > 0);
    latch->clientCnt--;
    CRASHPOINT(DB_CRASH_EVENT_UN_RLOCK, DB_CRASH_STATE_LATCH_DEC_READER_COUNT);
    if (latch->clientCnt == 0 && latch->serverCnt == 0) {
        latch->latchMode = (uint32_t)DB_LATCH_IDLE;
        CRASHPOINT(DB_CRASH_EVENT_UN_RLOCK, DB_CRASH_STATE_LATCH_UPDATE_IDLE_MODE);
    }
}

bool DbRWLatchTimedWEnterForClient(DbLatchT *latch, uint32_t owner, uint32_t timeoutUs)
{
    DbRWlatchCheckPid();
    while (!DbRWLatchAcquireIX(latch)) {
        if (!DbRWLatchWait(latch, owner, &timeoutUs, RWLATCH_STATUS_W_ACQUIRE_IX)) {
            return false;
        }
    }
    CRASHPOINT(DB_CRASH_EVENT_WLOCK, DB_CRASH_STATE_LATCH_SET_WRITER_FLAG);
    do {
        // 等待读者计数为0时加锁
        if (latch->latchMode != (uint32_t)DB_LATCH_IDLE) {
            if (!DbRWLatchWait(latch, owner, &timeoutUs, RWLATCH_STATUS_W_ACQUIRE_R_FINISH)) {
                DbAtomicSet(&latch->pid, 0);
                return false;
            }
        }
        if (!DbSpinTimedLockOwner(owner, &latch->spinlock, &timeoutUs)) {
            // 超时退出，重置writer标记位
            DbAtomicSet(&latch->pid, 0);
            break;
        }
        CRASHPOINT(DB_CRASH_EVENT_WLOCK, DB_CRASH_STATE_LATCH_ADD_SPINLOCK);
        if (latch->latchMode == (uint32_t)DB_LATCH_IDLE) {
            latch->latchMode = (uint32_t)DB_LATCH_CLIENT_X;
            CRASHPOINT(DB_CRASH_EVENT_WLOCK, DB_CRASH_STATE_LATCH_UPDATE_X_MODE);
            return true;
        }
        DbSpinUnlock(&latch->spinlock);
    } while (true);
    return false;
}

// 配对函数:DbRWLatchLeave
void DbRWLatchWEnterForClient(DbLatchT *latch, uint32_t owner)
{
    DbRWlatchCheckPid();
    uint32_t timeoutUs = DB_WAIT_FOREVER;
    while (!DbRWLatchAcquireIX(latch)) {
        (void)DbRWLatchWait(latch, owner, &timeoutUs, RWLATCH_STATUS_W_ACQUIRE_IX);
    }
    CRASHPOINT(DB_CRASH_EVENT_WLOCK, DB_CRASH_STATE_LATCH_SET_WRITER_FLAG);
    do {
        // 等待读者计数为0时加锁
        if (latch->latchMode != (uint32_t)DB_LATCH_IDLE) {
            (void)DbRWLatchWait(latch, owner, &timeoutUs, RWLATCH_STATUS_W_ACQUIRE_R_FINISH);
        }
        DbSpinLockOwner(owner, &latch->spinlock);
        CRASHPOINT(DB_CRASH_EVENT_WLOCK, DB_CRASH_STATE_LATCH_ADD_SPINLOCK);
        if (latch->latchMode == (uint32_t)DB_LATCH_IDLE) {
            latch->latchMode = (uint32_t)DB_LATCH_CLIENT_X;
            CRASHPOINT(DB_CRASH_EVENT_WLOCK, DB_CRASH_STATE_LATCH_UPDATE_X_MODE);
            return;
        }
        DbSpinUnlock(&latch->spinlock);
    } while (true);
}

bool DbRWLatchTryWEnterForClient(DbLatchT *latch, uint32_t owner)
{
    if (latch->latchMode != (uint32_t)DB_LATCH_IDLE) {
        return false;
    }
    DbRWlatchCheckPid();
    if (!DbRWLatchAcquireIX(latch)) {
        return false;
    }
    CRASHPOINT(DB_CRASH_EVENT_TRY_WLOCK, DB_CRASH_STATE_LATCH_SET_WRITER_FLAG);
    if (!DbSpinTryLockOwner(owner, &latch->spinlock)) {
        if (!DbAtomicExchange32WithAcquireBarrier(&latch->pid, g_gmdbRwlatchPid, 0)) {
            // 获取spinlock失败必然还原pid状态
            DB_ASSERT(false);
        }
        return false;
    }
    CRASHPOINT(DB_CRASH_EVENT_TRY_WLOCK, DB_CRASH_STATE_LATCH_ADD_SPINLOCK);
    if (latch->latchMode != (uint32_t)DB_LATCH_IDLE) {
        latch->pid = 0;
        DbSpinUnlock(&latch->spinlock);
        return false;
    }
    // 加写锁成功，锁状态转换必为DB_LATCH_IDLE -> DB_LATCH_SERVER_X
    latch->latchMode = (uint32_t)DB_LATCH_CLIENT_X;
    CRASHPOINT(DB_CRASH_EVENT_TRY_WLOCK, DB_CRASH_STATE_LATCH_UPDATE_X_MODE);
    return true;
}

// 配对函数:DbRWLatchLeave
inline void DbRWUnlatchWEnterForClient(DbLatchT *latch, uint32_t owner)
{
    DbSpinLockOwner(owner, &latch->spinlock);
    CRASHPOINT(DB_CRASH_EVENT_UN_WLOCK, DB_CRASH_STATE_LATCH_ADD_SPINLOCK);
    DB_ASSERT(latch->latchMode == (uint32_t)DB_LATCH_CLIENT_X);
    latch->latchMode = (uint32_t)DB_LATCH_IDLE;
    CRASHPOINT(DB_CRASH_EVENT_UN_WLOCK, DB_CRASH_STATE_LATCH_UPDATE_IDLE_MODE);
    latch->pid = 0;
    CRASHPOINT(DB_CRASH_EVENT_UN_WLOCK, DB_CRASH_STATE_LATCH_CLEAR_WRITER_FLAG);
}
#endif /* FEATURE_SIMPLEREL */

#ifdef __cplusplus
}
#endif
