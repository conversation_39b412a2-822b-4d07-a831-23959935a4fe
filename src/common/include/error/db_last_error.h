/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: file content
 * Author: gaohaiyang
 * Create: 2021-09-16
 */

#ifndef DB_LAST_ERROR_H
#define DB_LAST_ERROR_H

#include "db_last_error_base.h"
#include "db_last_error_haotian.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/*
 * description: 清空当前last error上的错误信息
 */
GMDB_EXPORT void DbResetLastErrorInfo(void);

/*
 * description: 获取当前线程/协程的last error
 * return: 如果返回值非NULL，那么也保证内容非NULL
 */
GMDB_EXPORT TextT *DbGetLastErrorInfo(void);

/*
 * description: 在当前last error上追加错误信息
 */
GMDB_EXPORT void DbAppendLastErrorInfo(const char *formatStr, ...);

#define DB_RESET_LAST_ERROR DbResetLastErrorInfo

#ifdef LOG_MODULE
#undef LOG_MODULE
#endif
#define LOG_MODULE "COMMON"

// 记录并打印日志last error相关日志宏
#define DB_LOG_INFO_AND_SET_LASTERR(errCode, errDesc, ...)                                                     \
    DbLogWriteAndSetLastError(true, DB_LOG_TYPE_RUN, LOG_MODULE, DB_LOG_LVL_INFO, errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, errDesc, ##__VA_ARGS__)
#define DB_LOG_WARN_AND_SET_LASTERR(errCode, errDesc, ...)                                                     \
    DbLogWriteAndSetLastError(true, DB_LOG_TYPE_RUN, LOG_MODULE, DB_LOG_LVL_WARN, errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, errDesc, ##__VA_ARGS__)
#define DB_LOG_ERROR_AND_SET_LASTERR(errCode, errDesc, ...)                                                   \
    DbLogWriteAndSetLastError(true, DB_LOG_TYPE_RUN, LOG_MODULE, DB_LOG_LVL_ERR, errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, errDesc, ##__VA_ARGS__)
#define DB_LOG_EMRG_AND_SET_LASTERR(errCode, errDesc, ...)                                                     \
    DbLogWriteAndSetLastError(true, DB_LOG_TYPE_RUN, LOG_MODULE, DB_LOG_LVL_EMRG, errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, errDesc, ##__VA_ARGS__)

#define DB_LOG_DEBUG_WARN_AND_SET_LASTERR(errCode, errDesc, ...)                                                 \
    DbLogWriteAndSetLastError(true, DB_LOG_TYPE_DEBUG, LOG_MODULE, DB_LOG_LVL_WARN, errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, errDesc, ##__VA_ARGS__)
#define DB_LOG_DEBUG_ERROR_AND_SET_LASTERR(errCode, errDesc, ...)                                               \
    DbLogWriteAndSetLastError(true, DB_LOG_TYPE_DEBUG, LOG_MODULE, DB_LOG_LVL_ERR, errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, errDesc, ##__VA_ARGS__)

// 只记录last error相关日志宏
#define DB_SET_LASTERR(errCode, errDesc, ...) DbWriteLastErrorCustom(errCode, errDesc, ##__VA_ARGS__)

#define DB_LOG_ERROR_AND_LASTERR_ON_DEMAND(errCode, errDesc, ...)   \
    do {                                                            \
        if (g_gmdbIsPrintLog) {                                     \
            DB_LOG_AND_SET_LASERR(errCode, errDesc, ##__VA_ARGS__); \
        }                                                           \
    } while (0)

// 定义SQL模块错误日志接口
#define SQL_ERROR_LOG(errCode, ...) DB_LOG_AND_SET_LASERR(errCode, ##__VA_ARGS__)
#define SQL_ERROR_LOG_UNFOLD(errCode, ...) \
    LOG_ERROR_AND_SET_LAST_ERROR_CUSTOM_UNFOLD(DB_LOG_TYPE_RUN, errCode, ##__VA_ARGS__)
#define EMB_ERROR_LOG(errCode, ...) DB_LOG_AND_SET_LASERR(errCode, ##__VA_ARGS__)

#define DB_LOG_AND_SET_LASERR(errCode, errDesc, ...)                                                          \
    DbLogWriteAndSetLastError(true, DB_LOG_TYPE_RUN, LOG_MODULE, DB_LOG_LVL_ERR, errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, errDesc, ##__VA_ARGS__)

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_LAST_ERROR_H */
