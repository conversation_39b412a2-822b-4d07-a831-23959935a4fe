/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: log base func
 * Author: jin<PERSON>glin
 * Create: 2025-04-07
 */

#ifndef DB_LOG_BASE_H
#define DB_LOG_BASE_H

#include "adpt_spinlock.h"
#include "adpt_rdtsc.h"
#include "adpt_thread.h"
#include "adpt_types.h"
#include "db_error.h"
#include "db_log_utils.h"
#include "adpt_log.h"
#include "adpt_log_macro_undefine.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/*
 * description: 日志写的统一接口
 * param {type} type：日志类型；其余参数：日志写入项
 */
GMDB_EXPORT void DbLogWrite(bool needFold, DbLogTypeE type, const char *module, uint32_t level, int32_t errCode,
    const char *fileName, uint32_t lineNum, const char *funcName, const char *formatStr, ...)
    __attribute__((format(printf, 9, 10), nonnull(9)));

GMDB_EXPORT void DbRunFoldErrLogWrite(const char *module, int32_t errCode, const char *fileName, uint32_t lineNum,
    const char *funcName, const char *formatStr, ...);
/*
 * description: 无格式日志写的统一接口
 * param {type} type：日志类型；其余参数：日志写入项
 */
GMDB_EXPORT void DbLogWriteNoFmt(bool needFold, DbLogTypeE type, const char *module, uint32_t level, int32_t errCode,
    const char *fileName, uint32_t lineNum, const char *funcName, ...);

/*
 * description: 审计日志写接口
 * param {*}：均为入参
 */
GMDB_EXPORT void DbAuditWrite(const char *userName, const char *resource, DbAuditEvtTypeE evtType, int32_t evtResult,
    const char *evtDesc, ...) __attribute__((format(printf, 5, 6)));

#define LOG_WRITE_CUSTOM(needFold, type, level, errCode, fmt, ...) \
    DbLogWrite(                                                    \
        needFold, type, LOG_MODULE, level, (int32_t)errCode, __FILE__, __LINE__, __DB_FUNC_NAME__, fmt, ##__VA_ARGS__)

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_LOG_BASE_H */
