/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: file content
 * Author: gaohaiyang
 * Create: 2021-09-01
 */

#ifndef DB_LOG_UTILS_H
#define DB_LOG_UTILS_H

#include "adpt_types.h"
#include "adpt_spinlock.h"
#include "adpt_log.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define DB_AUDIT_RESULT_DESC_LEN 16  // 审计结果描述串长度
#define LOG_FOLD_RULE_NUM_MAX 5      // 日志折叠规则最大数
#define CLT_LOG_SHMEM_BUFFER_NUM 10  // 客户端日志信息缓存，用于异常退出进入内核态时作为信号安全的日志手段

// 审计日志控制项
typedef struct DbAuditCtrlItem {
    bool auditTypeSwitch[(uint32_t)DB_AUDIT_BUTT];  // 4种类型控制开关
} DbAuditCtrlItemT;

typedef enum DbLogFoldMode { DISABLE_ALL_LOG_FOLD = 0, ENABLE_ALL_LOG_FOLD } DbLogFoldModeE;

typedef struct DbLogFoldCfg {
    uint8_t recordMode;                                // LogFoldRecordModeE
    DbLogFoldModeE enableLogFold;                      // 日志折叠开关，可配
    uint32_t logFoldPeriod[LOG_FOLD_RULE_NUM_MAX];     // 日志折叠周期数组，可配；
    uint32_t logFoldThreshold[LOG_FOLD_RULE_NUM_MAX];  // 日志折叠阈值数组，可配
    DbSpinLockT cfgModifyLock;                         // 多线程修改配置项需要加锁
} DbLogFoldCfgT;

#define DB_LOG_CYCLE_INIT 0                     // 0，初始值
#define DB_LOG_ENDTIME_INIT 0                   // 0，初始值
#define DB_LOG_TIME_OUT_DEF SECOND_OF_ONE_HOUR  // 默认结束时间1小时（3600秒）

typedef struct {
    uint32_t itemNum;
    DbSpinLockT listLock;
    uint32_t offset;  // 共享内存中，控制项数组的偏移
} DbLogCtrlHeaderT;

#define DB_LOG_TIMESTAMP_LEN 20  // 时间戳长度
// 文件大小参数宏定义
#define LOG_MAX_SIZE_OF_LOG_MSG 1024  // 每条日志消息最多包含1024个字符

// shm中结构体排列顺序：DbLogComFileCfgT | DbAuditCtrlItemT | DbLogCtrlHeaderT | DbLogCtrlItemT Array |
// 客户端内核态日志buffer。 通过DbLogCtrlHeaderT中的offset即可获得DbLogCtrlItemT Array的首addr
typedef enum DbLogShmContent {
    LOG_FILE_CFG = 0,
    LOG_AUDIT_CTRL_ITEM,
    LOG_WRITE_CTRL_HEADER,
    LOG_CLT_KERNEL_LOG_BUF,
} DbLogShmContentE;

typedef struct LogInfo {
    DbLogHeaderT header;
    DbLogLocationT place;
    const char *logDesc;
} DbLogInfoT;

typedef struct DbAuditLogInfo {
    DbAuditEvtTypeE evtType;
    int32_t evtResult;
    const char *userName;
    const char *resource;
    const char *evtDesc;
} DbAuditLogInfoT;

typedef struct DbCltKernelLog {
    uint32_t isSet;
    uint32_t pid;
    uint32_t sigNo;
    uint32_t policy;
    uint64_t timeStamp;
} DbCltKernelLogT;

// 创建共享内存，若之前存在该共享内存，则先删除旧的再创建
GMDB_EXPORT Status DbLogCreateShareMemSrv(void);

// attach共享内存
GMDB_EXPORT void *DbLogAttachShmem(uint32_t instanceId);

// detach共享内存
GMDB_EXPORT void DbLogDetachShmem(void);

// 预留接口，销毁共享内存，当前暂时不清理，只将指针置空
GMDB_EXPORT void DbLogDestroyShmem(void);

// 日志初始化判断用
GMDB_EXPORT bool DbLogCheckShmemNotNull(void);

/*
 * description: 从绝对路径中获取文件名
 * param {type} 传入的路径（__FILE__得到是绝对路径）
 * return: 文件名首addr
 */
GMDB_EXPORT const char *DbLogGetFileName(const char *filePath);
#ifdef EXPERIMENTAL_NERGC
GMDB_EXPORT void CltInitLogCtrl(void);
GMDB_EXPORT void CltUpdateLogCtrl(DbLogCtrlItemT *logCtl);
GMDB_EXPORT void CltGetLogCtrl(DbLogCtrlItemT *logCtl);
#endif
GMDB_EXPORT DbLogCtrlItemT *DbLogCreateCtrlItemByStr(const char *str);
GMDB_EXPORT void DbLogCreateCtrlItem(void);
GMDB_EXPORT void DbLogDestroyCtrlItem(DbLogCtrlItemT *ctrlItem);

GMDB_EXPORT DbLogCtrlItemT *DbLogGetCurCtrlItem(void);
GMDB_EXPORT DbLogCtrlItemT *DbLogGetCtrlItemByName(const char *procName);
GMDB_EXPORT void DbLogDestroyCurCtrlItem(void);

GMDB_EXPORT Status DbLogInitCtrlHeader(void);
GMDB_EXPORT DbLogCtrlHeaderT *DbLogGetCtrlHeader(void);

// 获取日志文件配置项
GMDB_EXPORT DbLogFileCfgT *DbLogGetFileCfg(void);

// 获取审计日志控制项
DbAuditCtrlItemT *DbLogGetAuditCtrlItem(void);

// 获取调测日志控制项数组首addr，上层调用需保证入参非NULL
GMDB_EXPORT DbLogCtrlItemT *DbLogGetCtrlArray(DbLogCtrlHeaderT *ctrlHeader);

// 判断是否可以写指定事件类型的审计日志
GMDB_EXPORT bool DbAuditEnable(DbAuditEvtTypeE auditEvtType);

// 获取gmlog当前attach的instanceId
GMDB_EXPORT uint32_t DbLogGetInstanceId(void);

// 获取客户端内核态共享内存Buffer
GMDB_EXPORT void *DbLogGetCltKernelLog(void);

// 打印对端异常信号处理框架内DFX日志。
GMDB_EXPORT void DbPrintCltKernelLog(void);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_LOG_UTILS_H */
