/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2021. All rights reserved.
 * File Name: db_hashmap.h
 * Description: header file for hashmap-related functions
 * Author:
 * Create: 2020-8-21
 */
#ifndef DB_HASHMAP_H
#define DB_HASHMAP_H

#include "db_hash.h"
#include "adpt_types.h"
#include "db_mem_context.h"
#include "db_hashmap_common.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

typedef struct DbOamapBucketT DbOamapBucketT;
typedef struct DbOamapT DbOamapT;

typedef uint32_t (*DbOamapCompareT)(const void *key1, const void *key2);
typedef uint32_t DbOamapIteratorT;

struct DbOamapBucketT {
    uint32_t state;
    uint32_t hash;
    void *key;
    void *value;
};

// DbOamapT提供加锁版本和非加锁版本，使用方根据需求选用，加锁版本接口名包含WithLock后缀。
struct DbOamapT {
    DbRWSpinLockT rwLock;
    bool extendable;
    DbMemCtxT *memCtx;
    uint32_t size;
    uint32_t capacity;
    DbOamapBucketT *buckets;
    DbOamapCompareT compareFunc;
};
// 原子操作的变量，需要4字节对齐
static_assert((sizeof(struct DbOamapT) % sizeof(uint32_t)) == 0, "DbOamapT must be aligned by 4 bytes");

GMDB_EXPORT Status DbOamapInit(
    DbOamapT *map, uint32_t capacity, DbOamapCompareT comparator, DbMemCtxT *memCtx, bool extendable);
GMDB_EXPORT void DbOamapDestroy(DbOamapT *map);
GMDB_EXPORT void DbOamapClear(DbOamapT *map);

/*
 * 向map插入key-value键值对
 * 使用规范：
 *    Key和Value是两个指针，我们需要注意Key和Value的生命周期是否一样，
 *    如果一样的话Key可以是Value内的部分字段，Key指向的内存可以是Value的一部分；
 *    如果不一样，典型如Value是集合or容器类型，Key可以是Value中某个元素的字段，
 *    因为Value内部元素可以动态添加或移除，所以Value中元素与key的生命周期是不一样的，
 *    key的内存必须得是独立生存，即Key和Value中元素的内存必须各自单独申请。
 * 注意：此接口并发不安全，并发场景需要使用DbOamapInsertWithLock
 */
GMDB_EXPORT Status DbOamapInsert(DbOamapT *map, uint32_t hash, void *key, void *value, uint32_t *mapIdx);

/*
 * 无重复key场景可使用，探测加速
 * 使用规范：调用者确保无重复key
 */
GMDB_EXPORT Status DbOamapInsertNoDupKeyWithLock(
    DbOamapT *map, uint32_t hash, void *key, void *value, uint32_t *mapIdx);

/*
 * @brief 根据 key 查询map 内存储的value指针，查询过程加读锁
 * @param[in] map : map本体
 * @return : GMERR if ok
 */
GMDB_EXPORT Status DbOamapExtend(DbOamapT *map);

/*
 * @brief 插入 key, value 对到map结构体中，插入过程加写锁
 * @param[in] map : map本体
 * @param[in] hash : key的哈希值，通常使用 DbHash32 生成
 * @param[in] key : key指针，要求key为长生命周期内存
 * @param[in] value : value指针，要求value为长生命周期内存
 * @param[out] mapIdx : 返回key对应的bucket偏移，用于遍历map；仅当插入成功时赋值
 * @return : GMERR if ok
 */
GMDB_EXPORT Status DbOamapInsertWithLock(DbOamapT *map, uint32_t hash, void *key, void *value, uint32_t *mapIdx);

/*
 * @brief 根据 key 查询map 内存储的key指针
 * @param[in] map : map本体
 * @param[in] hash : key的哈希值，通常使用 DbHash32 生成
 * @param[in] key : key指针，作为map->compareFunc的入参进行比较
 * @return : key指针
 * 注意：此接口并发不安全，并发场景需要自行新增接口
 */
GMDB_EXPORT void *DbOamapLookupKey(const DbOamapT *map, uint32_t hash, const void *key);

/*
 * @brief 根据 key 查询map 内存储的value指针
 * @param[in] map : map本体
 * @param[in] hash : key的哈希值，通常使用 DbHash32 生成
 * @param[in] key : key指针，作为map->compareFunc的入参进行比较
 * @param[out] mapIdx : 返回key对应的bucket偏移，用于遍历map；仅当查询成功时赋值
 * @return : value指针
 * 注意：此接口并发不安全，并发场景需要使用DbOamapLookupWithLock
 */
GMDB_EXPORT void *DbOamapLookup(const DbOamapT *map, uint32_t hash, const void *key, uint32_t *mapIdx);
#ifdef FEATURE_GQL
typedef uint32_t (*GetNextHashFuncT)(void *handle, uint32_t index);
GMDB_EXPORT void DbOamapBatchLookup(
    const DbOamapT *map, void *handle, GetNextHashFuncT getNextHash, uint32_t batchNum, void **slot);
#endif

/*
 * @brief 根据 key 查询map 内存储的value指针，查询过程加读锁
 * @param[in] map : map本体
 * @param[in] hash : key的哈希值，通常使用 DbHash32 生成
 * @param[in] key : key指针，作为map->compareFunc的入参进行比较
 * @param[out] mapIdx : 返回key对应的bucket偏移，用于遍历map；仅当查询成功时赋值
 * @return : value指针
 */
GMDB_EXPORT void *DbOamapLookupWithLock(DbOamapT *map, uint32_t hash, const void *key, uint32_t *mapIdx);

/*
 * @brief 根据 key 删除map 内存储的key,value对
 * @param[in] map : map本体
 * @param[in] hash : key的哈希值，通常使用 DbHash32 生成
 * @param[in] key : key指针，作为map->compareFunc的入参进行比较
 * @return : value指针
 * 注意：此接口并发不安全，并发场景需要DbOamapRemoveWithLock
 */
GMDB_EXPORT void *DbOamapRemove(DbOamapT *map, uint32_t hash, const void *key);

/*
 * @brief 根据 key 删除map 内存储的key,value对，移除过程中加写锁
 * @param[in] map : map本体
 * @param[in] hash : key的哈希值，通常使用 DbHash32 生成
 * @param[in] key : key指针，作为map->compareFunc的入参进行比较
 * @return : value指针
 */
GMDB_EXPORT void *DbOamapRemoveWithLock(DbOamapT *map, uint32_t hash, const void *key);

/*
 * @brief 根据 key 删除map 内存储的key,value对，删除成功后返回原key,value指针
 * @param[in] map : map本体
 * @param[in] hash : key的哈希值，通常使用 DbHash32 生成
 * @param[in] key : key指针，作为map->compareFunc的入参进行比较
 * @param[out] mapKey : 返回原key指针
 * @param[out] value :  返回原value指针
 * @return : GMERR if ok
 * 注意：此接口并发不安全，并发场景需要DbOamapRemoveKvWithLock
 */
GMDB_EXPORT Status DbOamapRemoveKv(DbOamapT *map, uint32_t hash, const void *key, void **mapKey, void **value);

/*
 * @brief 根据 key 删除map 内存储的key,value对，删除成功后返回原key,value指针;删除过程中加写锁
 * @param[in] map : map本体
 * @param[in] hash : key的哈希值，通常使用 DbHash32 生成
 * @param[in] key : key指针，作为map->compareFunc的入参进行比较
 * @param[out] mapKey : 返回原key指针
 * @param[out] value :  返回原value指针
 * @return : GMERR if ok
 */
GMDB_EXPORT Status DbOamapRemoveKvWithLock(DbOamapT *map, uint32_t hash, const void *key, void **mapKey, void **value);

/*
 * @brief 将迭代器置为0，从头开始遍历map
 * @param[in] iter : map迭代器
 */
GMDB_EXPORT void DbOamapResetIterator(DbOamapIteratorT *iter);

/*
 * @brief 根据迭代器获取value指针，此接口并发不安全
 * @param[in] map : map本体
 * @param[in] iter : map迭代器
 * @return : value指针
 */
GMDB_EXPORT void *DbOamapLookupByIdx(const DbOamapT *map, uint32_t mapIdx);

/*
 * @brief 根据迭代器删除key,value对，此接口并发不安全
 * @param[in] map : map本体
 * @param[in] iter : map迭代器
 * @return : value指针
 */
GMDB_EXPORT void *DbOamapRemoveByIdx(DbOamapT *map, uint32_t mapIdx);

/*
 * @brief 根据迭代器查询key,value对，此接口并发不安全
 * @param[in] map : map本体
 * @param[in] iter : map迭代器
 * @param[out] mapKey : 返回原key指针
 * @param[out] value :  返回原value指针
 * @return : GMERR if ok
 * 注意：此接口并发不安全，并发场景需要DbOamapFetchWithLock
 */
GMDB_EXPORT Status DbOamapFetch(const DbOamapT *map, DbOamapIteratorT *iter, void **key, void **value);

/*
 * @brief 根据迭代器查询key,value对，查询时加读锁
 * @param[in] map : map本体
 * @param[in] iter : map迭代器
 * @param[out] mapKey : 返回原key指针
 * @param[out] value :  返回原value指针
 * @return : GMERR if ok
 */
GMDB_EXPORT Status DbOamapFetchWithLock(DbOamapT *map, DbOamapIteratorT *iter, void **key, void **value);

inline static uint32_t DbOamapUsedSize(const DbOamapT *map)
{
    // function will be inlined
    return map->size;
}

GMDB_EXPORT uint32_t DbOamapPtrCompare(const void *key1, const void *key2);
GMDB_EXPORT uint32_t DbOamapUint64Compare(const void *key1, const void *key2);
GMDB_EXPORT uint32_t DbOamapUint32Compare(const void *key1, const void *key2);
GMDB_EXPORT uint32_t DbOamapUint16Compare(const void *key1, const void *key2);
GMDB_EXPORT uint32_t DbOamapStringCompare(const void *key1, const void *key2);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_HASHMAP_H */
