/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: db_sha.h
 * Description: header file of db sha
 * Author:
 * Create: 2024-07-17
 */

#ifndef DB_SHA_H
#define DB_SHA_H

#include "db_utils.h"
#include "common_log.h"
#if (!defined(HPE) && !defined(RTOSV2X))
#include <openssl/evp.h>
#endif

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#if (defined(HPE) || defined(RTOSV2X))
typedef struct DbDigestCtx DbDigestCtxT;
#else
typedef EVP_MD_CTX DbDigestCtxT;
#endif

SO_EXPORT_FOR_TS Status DbDigestCtxInit(DbDigestCtxT **digestCtx);
SO_EXPORT_FOR_TS Status DbDigestUpdate(DbDigestCtxT *digestCtx, uint8_t *data, uint32_t dataLen);
SO_EXPORT_FOR_TS Status DbDigestFinal(DbDigestCtxT *digestCtx, uint8_t *digest);
SO_EXPORT_FOR_TS void DbDigestCtxFree(DbDigestCtxT *digestCtx);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // DB_SHA_H
