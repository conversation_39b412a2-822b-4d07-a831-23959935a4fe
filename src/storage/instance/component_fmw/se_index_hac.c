/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 * File Name: se_index_hac.c
 * Description: storage index init functions for hac
 * Author: lijianchuan
 * Create: 2023/7/11
 */

#ifdef FEATURE_HAC
#include "adpt_function_loader.h"
#include "db_dyn_load.h"
#include "se_index_inner.h"

typedef StatusInter (*IdxHacInitFuncT)(SeInstanceT *seIns, DbMemCtxT *memCtx, uint32_t hacMode);
typedef HacStatisticsT *(*IdxHacGetStatsFuncT)(void);
typedef bool (*IsHacInitializedFuncT)(void);
typedef Status (*CltHacMgrCtxInitFuncT)(uint16_t instanceId);

StatusInter IdxHacInit(SeInstanceT *seInsPtr, DbMemCtxT *memCtx)
{
    int32_t hacModeCfg;
    Status statusRet = DbCfgGetInt32((DbCfgMgrT *)DbGetCfgHandle(NULL), DB_CFG_HAC_MODE, true, &hacModeCfg);
    if (statusRet != GMERR_OK) {
        DB_LOG_ERROR(statusRet, "get cfg hacmode.");
        return DbGetStatusInterErrno(statusRet);
    }
    if (hacModeCfg == 0) {  // 硬件卸载不开启, 不报错
        DB_LOG_INFO("unable init hacMgr, config is off");
        return STATUS_OK_INTER;
    }
    IdxHacInitFuncT idxHacInitFunc = DbDynLoadGetFunc(COMPONENT_HAC, "hac_mgr_init");
    if (idxHacInitFunc == NULL) {  // 找不到函数
        DB_LOG_INFO("hac feature off");
        return STATUS_OK_INTER;
    }
    StatusInter ret = idxHacInitFunc(seInsPtr, memCtx, hacModeCfg);
    if (ret == FEATURE_NOT_SUPPORTED_INNER) {  // 特性不支持，但不需报错
        DB_LOG_INFO("hac feature off");
        return STATUS_OK_INTER;
    }
    if (ret != STATUS_OK_INTER) {  // 函数加载失败
        DB_LOG_INFO("hac feature off");
        return ret;
    }
    DB_LOG_INFO("hac feature on");
    return STATUS_OK_INTER;
}

HacStatisticsT *HacGetStats(void)
{
    IdxHacGetStatsFuncT func = DbDynLoadGetFunc(COMPONENT_HAC, "hac_get_stat");
    if (func == NULL) {
        return NULL;
    }
    return func();
}

bool IsHacInitialized(void)
{
    IsHacInitializedFuncT func = DbDynLoadGetFunc(COMPONENT_HAC, "is_hac_inital");
    if (func == NULL) {
        return false;
    }
    return func();
}

Status CltHacMgrCtxInit(uint16_t instanceId)
{
    CltHacMgrCtxInitFuncT func = DbDynLoadGetFunc(COMPONENT_HAC, "clt_init");
    if (func == NULL) {
        return GMERR_OK;
    }
    return func(instanceId);
}

#endif
