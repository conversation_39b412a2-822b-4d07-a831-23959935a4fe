/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author:
 * Create: 2024-04-15
 */
#include "se_daf_inner.h"
#include "se_trx_inner.h"
#include "db_sysapp_context.h"
#include "se_resource_session_pub.h"

#if !defined(FEATURE_HAC) && !defined(FEATURE_DAF)
#error "unsupported feature daf"
#endif

DafFuncT g_dafAm = {0};

void DafSetAmFunc(const DafFuncT *const am)
{
    g_dafAm = *am;
}
typedef StatusInter (*DafInitFuncT)(DbMemCtxT *memCtx);
typedef void (*DafUnInitFuncT)(void);

StatusInter DafInit(DbMemCtxT *memCtx)  // 动态加载对应函数
{
    DafInitFuncT dafInit = DbDynLoadGetFunc("trm", "daf_init");
    if (dafInit == NULL) {
        DB_LOG_INFO("daf feature off");
        return STATUS_OK_INTER;
    }
    return dafInit(memCtx);
}

void DafUnInit(void)
{
    DafUnInitFuncT dafUnInit = DbDynLoadGetFunc("trm", "daf_uninit");
    if (dafUnInit == NULL) {
        DB_LOG_INFO("daf feature off");
        return;
    }
    return dafUnInit();
}

bool IsDafMgrInited(void)
{
    if (g_dafAm.isDafMgrInited == NULL) {
        return false;
    }
    return g_dafAm.isDafMgrInited();
}

// 启用daf条件：Daf已初始化+非轻量化事务
inline bool TrxIsDafEnable(const TrxT *trx)
{
    return IsDafMgrInited() && !TrxIsLiteTrx(trx);
}

inline bool TrxIsDafEnableForIndex(const TrxT *trx, const DmIndexTypeE indexType, const bool isUnique)
{
#ifndef FEATURE_HAC
    return TrxIsDafEnable(trx) && (indexType != HASHCLUSTER_INDEX && (!isUnique || indexType == BTREE_INDEX));
#else
    // 硬件卸载场景，daf仅限hachash、multihash
    return (indexType == HAC_HASH_INDEX || indexType == MULTI_HASH_INDEX) && TrxIsDafEnable(trx);
#endif
}

inline bool SeIsDafEnableForIndex(
    const SeRunCtxHdT seRunCtx, const HpRunHdlT hpHandle, const DmIndexTypeE indexType, const bool isUnique)
{
    if (hpHandle == NULL) {  // 没有heap上下文,过滤chlabel
        return false;
    }
    SeRunCtxT *seRunCtxPtr = (SeRunCtxT *)seRunCtx;
    TrxT *trx = (TrxT *)seRunCtxPtr->trx;
    return TrxIsDafEnableForIndex(trx, indexType, isUnique);
}

StatusInter DafCreateActionCollection(TrxT *trx)
{
    if (g_dafAm.dafCreateActionCollection == NULL) {
        return false;
    }
    return g_dafAm.dafCreateActionCollection(trx);
}

Status IdxDelayDelete(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr)
{
    if (g_dafAm.idxDelayDelete == NULL) {
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return g_dafAm.idxDelayDelete(idxCtx, idxKey, addr);
}

Status IdxDelayBatchDelete(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum)
{
    if (g_dafAm.idxDelayBatchDelete == NULL) {
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return g_dafAm.idxDelayBatchDelete(idxCtx, idxKey, addr, batchNum);
}

void DafCommitActionCollection(TrxT *trx)
{
    if (g_dafAm.dafCommitActionCollection == NULL) {
        return;
    }
    g_dafAm.dafCommitActionCollection(trx);
}

void DafCommit(TrxT *trx)
{
    if (g_dafAm.dafCommit == NULL) {
        return;
    }
    g_dafAm.dafCommit(trx);
}
void DafRollbackActions(TrxT *trx)
{
    if (g_dafAm.dafRollbackActions == NULL) {
        return;
    }
    g_dafAm.dafRollbackActions(trx);
}

void DafPurgerDestroyList(DafPurgerRunCtxT *purgerCtx)
{
    DbDestroyList(&purgerCtx->vertexLabelList);
    DbDestroyList(&purgerCtx->kvLabelList);
    DbDestroyList(&purgerCtx->edgeLabelList);
    DbDestroyList(&purgerCtx->vertexList);
}

void DafPurgerCtxDestroy(DafPurgerRunCtxHdlT purgerCtx)
{
    DB_POINTER(purgerCtx);
    DafPurgerDestroyList(purgerCtx);
    (void)SeClose(purgerCtx->seCtx);
    DbDeleteDynMemCtx((DbMemCtxT *)purgerCtx->memCtx);
}

void DafPurgerCtxRegister(DafPurgerRunCtxHdlT purgerCtx)
{
    DB_POINTER3(purgerCtx, purgerCtx->seCtx, purgerCtx->seCtx->seIns);
    SeInstanceT *seIns = purgerCtx->seCtx->seIns;
    seIns->undoPurgerCtx = purgerCtx;
}

void DafPurgerInitList(DafPurgerRunCtxT *purgerCtx, DbMemCtxT *memCtx)
{
    DbCreateList(&purgerCtx->vertexLabelList, sizeof(void *), memCtx);
    DbCreateList(&purgerCtx->kvLabelList, sizeof(void *), memCtx);
    DbCreateList(&purgerCtx->edgeLabelList, sizeof(void *), memCtx);
    DbCreateList(&purgerCtx->vertexList, sizeof(void *), memCtx);
}

Status DafPurgerCtxInit(DafPurgerRunCtxT **purgerCtx, const UndoPurgerCfgT *purgerCfg)
{
    DB_POINTER2(purgerCtx, purgerCfg);
    // create a dynamic memctx
    // create a dynamic memctx
    DbMemCtxT *sysDynCtx = DbSrvGetSysDynCtx(purgerCfg->instanceId);
    if (sysDynCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get sys dyn ctx");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    /* UndoPurgerCtx 说明
        用    途: 创建daf purger上下文结构, 用作daf purger线程的连接级别的sessionMemCtx
        生命周期: 长进程级别
        释放策略: 通过delete memctx删除释放
        兜底清空措施: 依赖上层sysDynCtx
    */
    DbMemCtxArgsT args = {0};
    args.liteModOn = true;
    DbMemCtxT *memCtx = DbCreateDynMemCtx(sysDynCtx, false, "DafPurgerCtx", &args);
    if (memCtx == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "Create dyn-memctx");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    // 申请purger线程运行上下文, 正常情况下, purger线程启动之后就不会退出, 退出后通过 删除memCtx统一释放
    *purgerCtx = DbDynMemCtxAlloc(memCtx, sizeof(DafPurgerRunCtxT));
    if (*purgerCtx == NULL) {
        DbDeleteDynMemCtx(memCtx);
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc purger ctx");
        return GMERR_OUT_OF_MEMORY;
    }

    (**purgerCtx) = (DafPurgerRunCtxT){0};
    (*purgerCtx)->cfg = *purgerCfg;

    // open a storage instance
    SeRunCtxHdT seHdl = NULL;
    Status ret = SeOpen(purgerCfg->instanceId, memCtx, NULL, &seHdl);
    if (ret != GMERR_OK) {
        DbDeleteDynMemCtx(memCtx);
        return ret;
    }

    ret = SeOpenResSession(seHdl);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "open se session.");
        (void)SeClose(seHdl);
        seHdl = NULL;
        DbDeleteDynMemCtx(memCtx);
        return ret;
    }

    DafPurgerInitList(*purgerCtx, memCtx);

    (*purgerCtx)->seCtx = seHdl;
    (*purgerCtx)->memCtx = memCtx;
    return GMERR_OK;
}

bool DafPurgerMain(DafPurgerRunCtxT *purgerRunCtx, uint64_t currTime, uint16_t workerId)
{
    DB_UNUSED(currTime);
    DB_UNUSED(workerId);
    if (g_dafAm.dafPurgerMain == NULL) {
        return true;
    }
    return g_dafAm.dafPurgerMain(purgerRunCtx);
}
