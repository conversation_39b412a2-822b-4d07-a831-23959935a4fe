/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: se_index_base.h
 * Description: storage index base public head file
 * Author: lijun
 * Create: 2025-03-13
 */
#ifndef SE_INDEX_BASE_H
#define SE_INDEX_BASE_H

#include "se_common.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_NODE_TYPE_NUM 10u
#define ART_NODE_TYPE_NUM 4u
#define INDEX_VALID_CODE 0x7a4bdf23a9b5bcc6u
#define SE_IDX_MAX_KEY_LENGTH DM_MAX_INDEX_KEY_SIZE
#define DM_INDEX_MAX_FIXED_PROPERTY_NUM 8
#define HAC_BATCH_LIMIT 0xFF

// 主要用来区分当前key类型是DmValueT封装的类型还是二进制类型。
// 在后文key compare时候，根据索引data类型选择不同的cmp函数
typedef enum {
    SE_INDEX_DATATYPE_DM_VALUE,
    SE_INDEX_DATATYPE_BLOB,
    SE_INDEX_DATATYPE_BUTT,  // Invalid data type
} SeIndexDataTypeE;

typedef struct KeyCmpRes {
    int32_t cmpRet;
    bool isMatch;
    bool isSelfDelete;  // 是否是本事务删除的buff
} KeyCmpResT;

typedef struct HacKeyInfo {
    uint16_t keySegmentNum;  // 用于解析tuple主键的参数
    uint16_t keySegOffset[DM_INDEX_MAX_FIXED_PROPERTY_NUM];
    uint16_t keySegLength[DM_INDEX_MAX_FIXED_PROPERTY_NUM];
} HacKeyInfoT;

typedef struct {
    uint32_t pageSize;            // the size of each page
    uint32_t totalNodeCount;      // the total number of nodes; totalNodeCount <= pageCount
    uint32_t leafCount;           // the number of leaves
    uint32_t internalNodeCount;   // the number of internal nodes
    uint64_t totalMemorySize;     // totalMemorySize = pageSize * pageCount;
    uint64_t occupiedMemorySize;  // the size of occupied memory
    uint32_t bigKeyPageCount;     // the count of big key page
} BTreeMemStatT;

typedef struct {
    uint64_t insertCount;    // the number of insertions thus far
    uint64_t deleteCount;    // the number of deletions thus far
    uint64_t splitCount;     // the number of splits thus far
    uint64_t freeCount;      // the number of empty node collections thus far
    uint64_t coalesceCount;  // the number of merge sibling nodes thus far, include insertion and deletion
} BTreePerfStatT;

typedef struct ArtMemPerfStat {
    uint32_t pageCount;
    uint32_t pageReleasedCount;
    uint32_t scaleInCount;
    uint16_t pageSize;

    uint16_t shmUtilization;  // eg. 90%, node used shm / total shm (except freeNode used shm)
    uint64_t usedShm;

    uint32_t nodeCounts[MAX_NODE_TYPE_NUM];
    uint32_t nodeCount;
    uint32_t freeNodeCounts[MAX_NODE_TYPE_NUM];
    uint32_t freeNodeCount;
} ArtMemPerfStatT;

typedef struct IndexHashPerfStat {
    uint32_t pageCount;         // 申请的page总个数
    uint32_t perPageSize;       // 每个page的大小
    uint64_t pageSize;          // 实际申请的page总大小
    uint64_t usedMemSize;       // 实际使用的内存空间
    uint64_t hashInsertCnt;     // hash索引插入次数
    uint64_t hashCollisionCnt;  // hash索引冲突次数
    uint8_t hashCollisionRate;  // hash冲突率
} IndexHashPerfStatT;

typedef struct BTreeIndexStatistics {
    uint64_t recordCount;     // number of records in the tree
    uint32_t treeHeight;      // the height of the tree
    BTreeMemStatT memStat;    // memory statistics
    BTreePerfStatT perfStat;  // performance statistics
} BTreeIndexStatisticsT;

typedef struct LpasPqAddrInfo {
    PageIdT slotId;
    uint32_t offset;
} LpasPqAddrInfoT;

typedef struct LpasPqAddrInfoOnIndex {
    uint32_t serPageId;
    uint32_t offset;
} LpasPqAddrInfoOnIndexT;

typedef struct DataIdAndPqAddr {
    HpTupleAddr dataId;
    LpasPqAddrInfoT pqInfo;
} DataIdAndPqAddrT;

#define LPAS_PQ_INFO_SIZE sizeof(LpasPqAddrInfoOnIndexT)
#define AUTO_ID_SIZE sizeof(uint64_t)
#define AUTO_ID_NULL_BYTES_SIZE 1
#define AUTO_ID_NULL_BYTES_VALUE 1
#define AUTO_ID_PREFIX_NUM 1

#ifdef FEATURE_HAC
typedef struct HacStatistics {
    uint32_t hacHashIdxCnt;        // hachash索引数目
    uint32_t multiHashIdxCnt;      // multihash索引数目
    uint64_t batchSoftwareCnt;     // 软件处理批量次数
    uint64_t batchHacCnt;          // 硬件处理批量次数
    uint64_t batchHacSyncCnt;      // 硬件同步批请求次数
    uint64_t batchHacAsyncCnt;     // 硬件异步批请求次数
    uint64_t batchExcapedCnt;      // 回调逃生次数
    uint64_t hacRqstCnt;           // 向加速器请求的总操作数
    uint64_t hacRspWarnCnt;        // 向加速器请求的提示性错误次数
    uint64_t hacRspNormalErrCnt;   // 向加速器请求的可回滚性错误次数
    uint64_t hacRspSpecialErrCnt;  // 向加速器请求的定位级错误次数
    uint8_t allocBlockCnt;         // 挂链占用的总block块数 [0,63]
    uint8_t allocHacBlockCnt;      // 挂链分配给硬件的的block块数 [0,31]
} HacStatisticsT;

HacStatisticsT *HacGetStats(void);
bool IsHacInitialized(void);
Status CltHacMgrCtxInit(uint16_t instanceId);
#endif

#ifdef __cplusplus
}
#endif

#endif
