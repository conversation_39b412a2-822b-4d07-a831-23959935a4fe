/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: 存储引擎DFX接口 对外提供的接口和结构
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * Create: 2020-12-15
 */
#ifndef SE_DFX_H
#define SE_DFX_H

#include "se_capacity_def_pub.h"
#include "se_heap.h"
#include "se_trx.h"
#include "se_trx_lock.h"
#include "dm_meta_res_col_pool.h"
#include "db_list.h"
#include "se_dfx_trx.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifndef NDEBUG
#define SE_HEAP_PERF
#endif

#define MAX_VECTOR_SIZE 1024
#define STAT_RES_POOL_MAX_NAME_LEN 64
#define STAT_RES_POOL_USER_NAME_LEN 128
#define STAT_RES_POOL_TABLE_NAME_LEN 128
#define STAT_MAX_FILE_CNT_PER_SPACE 32

typedef struct DeviceDescStat {
    uint8_t isUsed;
    uint8_t status;
    uint16_t reserve;
    uint32_t freeChunkCountInDev;
    uint32_t deviceId;
    uint32_t tableSpaceId;  // 记录device归属于哪个tableSpace
    ShmemPtrT devPtr;
} DeviceDescStatT;

typedef struct TagMemDataInfo {
    uint16_t instanceId;  //  存储引擎实例ID
    uint16_t reserve;
    uint32_t deviceCount;            //  当前存储引擎分配的device的个数
    uint32_t deviceSize;             //  当前配置的deviceSize大小, 大小 B
    uint32_t pageSize;               //  当前配置的page的大小, 大小 B
    uint32_t totalChunkCount;        //  当前存储引擎拥有的总的内存页的个数
    uint32_t totalFreeChunkCount;    //  当前存储引擎拥有的空闲内存页的个数
    uint32_t chunkUtilizationRatio;  //  当前存储引擎内存页利用率
    uint32_t totalPageDescCount;     //  当前存储引擎拥有的总的页描述符个数
    uint32_t pageDescCountUsed;      //  当前存储引擎的页描述符已经使用的个数
    uint32_t extentArrayCount;       //  当前页的缓存动态数组扩容的总个数
    uint32_t extentArrayCountUsed;   //  当前页的缓存动态数组已经使用的个数
    uint32_t eachExtPageDescCount;   //  每次扩容的页缓存描述符的个数
    uint64_t maxMemSize;             //  存储引擎占用的最大存储空间, 大小 B
    uint32_t ownId;
    uint32_t devDescSize;
    uint32_t idleDevCount;
    uint32_t devMaxCount;
    uint32_t devMemCtxId;
    uint32_t chunkCntPerDev;
    DeviceDescStatT devDesc[];
} MemDataInfoT;

#define SUB_BLOCK_NOT_USED 0  // 未分配的状态
#define SUB_BLOCK_NOT_BIND 1  // 分配出来了，但未绑定tableSpace
#define SUB_BLOCK_FREE 2      // 分配出来了，绑定了tableSpace，但未使用的状态
#define SUB_BLOCK_USED 3      // 正在使用的状态
#define SUB_BLOCK_INVALID_ID 0x3fffffff
#define SUB_BLOCK_INVALID_OFFSET 0xffffffff

#define RSM_DEV_NOT_USED 0
#define RSM_DEV_NOT_BIND 1
#define RSM_DEV_USED 2
#define RSM_DEV_RECOVERY 3  // 重建tsp时临时将RSM_DEV_USED设置为RSM_DEV_RECOVERY，重建结束后进行恢复

#define BLOCK_NOT_USED 0
#define BLOCK_USED 1

#define RSM_INVALID_KEY_ID 0x1fffffff
#define RSM_INVALID_DEVICE_ID 0x7fffffff

typedef struct TagRsmDevInfo {
    uint32_t status : 3;
    uint32_t devIdInRsmBlock : 29;  // 自己在device数组里的下标，方便核对使用
    uint32_t tableSpaceId;
    uint32_t freeRsmSubBlkCnt;  // 空闲的subBlk的数量
} RsmDevInfoT;

typedef struct TagRsmBlockInfo {
    uint16_t rsmBlockDescId;  //  保留内存在数组中的下标
    uint32_t status : 2;
    uint32_t isRsmExtendBlock : 1;
    uint32_t keyId : 29;
    uint32_t subRsmBlkCntIndev;          // 在一块dev中有多少subRsmBlock
    uint32_t unBindDeviceCntInRsmBlock;  // 分配出来但未绑定tableSpace的device的数量
    uint32_t curDeviceCntInRsmBlock;     // 当前使用到的device个数(包含未绑定的)
    RsmDevInfoT devDesc[];
} RsmBlockInfoT;

typedef struct TagSeLockOverviewStatT {
    uint32_t lockCnt;
    uint32_t usedLockCnt;
    uint32_t freeLockCnt;
    uint32_t bucketLockCnt;
    uint32_t usedBucketLockCnt;
    uint32_t freeBucketLockCnt;
    uint32_t poolLockCnt;
    uint32_t usedPoolLockCnt;
    uint32_t usedPoolLockCntMax;
    uint32_t freePoolLockCnt;
    uint32_t reserveLockCnt;
    uint32_t usedReserveLockCnt;
    uint32_t freeReserveLockCnt;
} SeLockOverviewStatT;

typedef struct TagResoucePoolStat {
    char name[STAT_RES_POOL_MAX_NAME_LEN];  // res pool的名称，全局唯一
    uint32_t poolId;                        // res pool的id，全局唯一
    uint32_t labelRefCnt;                   // 该res pool和多少张表(label)绑定
    uint32_t poolRefCnt;                    // 该res pool作为扩展池和多少res pool绑定
    uint32_t startId;                       // res pool中res id的起始id
    uint32_t capacity;                      // res pool的总容量，startId + capacity < max capacity
    uint32_t used;                          // 当前已使用的res数量
    ResColPoolAllocOrderE allocOrder;       // 分配的顺序
    ResColPoolAllocTypeE allocType;         // 申请资源模式
    uint32_t extendedPoolId;
    uint32_t shareMemSize;
    uint32_t dynamicMemSize;
    uint32_t memSize;
} ResoucePoolStatT;

typedef struct TagResoucePoolAndTableStat {
    ResoucePoolStatT resPoolStat;
    uint32_t tableCount;
} ResoucePoolAndTableStatT;

typedef struct TagResoucePoolBitmapStat {
    ResoucePoolAndTableStatT resPoolAndTable;
    uint32_t bitmapSize;
    uint32_t *bitmap;
} ResoucePoolBitmapStatT;

typedef struct TagResoucePoolStartIndexStatT {
    char name[STAT_RES_POOL_MAX_NAME_LEN];  // res pool的名称，全局唯一
    uint32_t poolId;                        // res pool的id，全局唯一
    uint32_t startIndex;                    // 资源索引的起始位置
    uint32_t count;                         // 查看资源个数
    bool isFree;                            // 是否被使用
    uint8_t reserved1;                      // 字节对齐, 预留字段
    uint16_t reserved2;                     // 字节对齐, 预留字段
    uint64_t resId;                         // 仅对根据ResId查询资源索引视图有效
} ResoucePoolStartIndexStatT;

typedef struct TagResoucePoolResIdStatT {
    ResoucePoolStatT resPoolStat;               // 资源池的所有信息
    ResoucePoolStartIndexStatT startIndexStat;  // 资源池中指定ResID的位置的资源使用情况
} ResoucePoolResIdStatT;

typedef struct TagResoucePoolBindLabelStatT {
    ResoucePoolStatT labelResPoolStat;  // 该资源表绑定的资源池的所有信息
} ResoucePoolBindLabelStatT;

typedef enum EnumHeapPerfOperationType {
    HEAP_OP_INSERT = 0,
    HEAP_OP_DELETE,
    HEAP_OP_UPDATE,
    HEAP_OP_FETCH,
    HEAP_OP_FETCHNEXT,
    HEAP_OP_REPLACE_INSERT,
    HEAP_OP_REPLACE_UPDATE,
    HEAP_OP_MAX_TYPE
} HpPerfOpTypeE;

typedef struct TagHeapPerfOperationStaticsT {
    uint64_t cnt;
    uint64_t failCnt;
    uint64_t totalCost;
    uint64_t minCost;
    uint64_t maxCost;
} HpPerfOpStatT;

typedef struct LockConflictStat {
    uint64_t trxLockAcquireCount;  // 加锁总数
    uint64_t conflictCount;        // 锁冲突数
    uint64_t lockEscalationCount;  // 锁升级总数 X锁不涉及此项
    uint64_t lockEscalationFail;   // 锁升级失败数 X锁不涉及此项
    uint64_t conflictAcqFail;      // 锁冲突后加锁失败
    uint64_t timeout;              // 锁冲突后因超时未拿到锁数
    uint64_t deadlockRollback;     // 锁冲突后因死锁而回滚的事务数
} LockConflictStatT;

typedef struct LockStat {
    DbSpinLockT lock;
    uint64_t tupleLockEscalateToLabelLock;
    LockConflictStatT lockConflictStat[SE_LOCK_TYPE_NUM][SE_LOCK_ACQ_TYPE_NUM];
} LockStatT;

typedef struct TagHeapPerfStatT {
    DbSpinLockT lock;  // 用于保护HeapPerfStatT结构体中成员变量的读写并发
    uint32_t metaPageCnt;
    uint64_t phyItemNum;       // heap物理存储的item计数，包含未提交的数据
    uint64_t realItemNum;      // 乐观事务已提交的数据计数
    uint64_t writeBytes;       // 写入的总字节数，包含未提交的字节
    uint64_t deleteBytes;      // 删除的总字节数，包含未提交的字节
    uint64_t phyItemSize;      // 物理所占空间大小
    uint64_t historyLinkRows;  // 累计出现过的跳转行的数量，只增不减，truncate可置0
#ifdef SE_HEAP_PERF
    HpPerfOpStatT opStat[(int32_t)HEAP_OP_MAX_TYPE];
#endif
    uint64_t lastDefragTaskTime;  // 上一次触发整理任务的时间
    uint64_t defragmentationCnt;  // 统计压缩的次数
    uint64_t lastDefragTime;      // 上一次真正进行整理的时间
    double fragmentationRate;     // 碎片率
    uint32_t heapFileId;
    uint32_t pageCnt;
} HeapPerfStatT;

typedef struct TagHeapDefragStatT {
    uint32_t totalCursorNum;
    uint32_t allocCursorFailTimes;
    bool cursorOpenerInit[HEAP_SCAN_CURSOR_OPENER_TOP_3];
    ScanCursorOpenerT cursorOpener[HEAP_SCAN_CURSOR_OPENER_TOP_3];
} HeapDefragStatT;

typedef struct TagHeapConfigStatT {
    bool globalActiveDefragment;
    bool reserve[3];
    uint32_t pageSize;
    HpPageTypeE heapType;
    ConcurrencyControlE ccType;
    uint32_t tableSpaceId;
    uint32_t fragmentationRateThreshold;
    uint64_t fragmentationMemoryThreshold;
} HeapCfgStatT;

// FSM的状态统计信息
typedef struct FsmStatT {
    uint32_t fsmFileId;
    uint16_t availSize;
    uint16_t reserveSize;
    uint32_t maxBlockCnt;
    uint32_t actualPageCnt;
    uint32_t fsmPageCnt;
    uint32_t pageSize;
    uint32_t fsmFreeSize;
    uint16_t maxSlotPerFsmPage;
    uint16_t listRange[FSM_LIST_COUNT];
    uint16_t slotCntSumOfPerList[FSM_LIST_COUNT];
} FsmStat;

typedef struct HeapLabelMemoryStat {
    char *labelName;
    uint32_t labelNameLen;
    uint32_t pageHeadSize;         // size of each page head of vertex
    uint32_t rowHeadSize;          // size of each row head
    uint32_t payloadSize;          // size of each row payload
    uint32_t totalPhyRowNum;       // total physical row's number of each vertexlabel
    uint32_t totalPageNum;         // number of pages occupied by each vertexlabel
    uint32_t eachPageSize;         // size of each page
    uint32_t memUtilizationRatio;  // memory utilization ratio (%)
    uint32_t reserve;
    uint64_t totalPayloadSize;
    uint64_t memUnusedSize;  // unused memory size
    uint64_t totalPageSize;  // size of total page
    uint64_t totalMemorySize;
    uint32_t fsmPageNum;       // number of pages occupied by fsm
    uint32_t fsmEachPageSize;  // size of each fsm page
    uint64_t totalFsmSize;     // size of total fsm page
    bool isSupportReservedMemory;
} HeapLabelMemoryStatT;

typedef struct TagClusteredHashStatT {
    uint32_t fileId;
    uint32_t indexId;
    uint64_t version;
    uint32_t dirPageCount;
    uint32_t segPageCount;
    uint32_t overflowPageCount;
    uint32_t pageCount;
    uint32_t heapPageCount;
    uint32_t perPageSize;
    uint64_t pageSize;
    uint32_t hashBucketNumPerPage;
    uint32_t hashEntryPerBucket;
    uint32_t hashEntryPerStashBucket;
    uint32_t tupleNumPerOverflowPage;
    uint32_t overflowPageTupleUsed;
    uint32_t stashBucketNum;
    uint32_t dirDepth;
    uint32_t dirCap;
    uint32_t fixRowDataSize;
    uint32_t slotExtendSize;
#ifndef NDEBUG
    uint32_t scaleInUpgradePageCnt;
#endif
    uint32_t scaleInProbCnt;
    uint32_t scaleInBeginCnt;
    uint32_t scaleInPageCnt;
    uint64_t usedMemSize;
    uint64_t entryUsed;
    uint64_t totalEntry;
    uint64_t tupleUsed;
    uint64_t totalTuple;
    uint64_t heapItemCnt;
    uint64_t deleteBytes;
    uint64_t writeBytes;
    double loadFactor;
    double tupleUsedRate;
    double tupleUsedRateThreshold;
    bool isSupportReservedMemory;
} ClusteredHashStatT;

typedef struct UndoPurgerInfo {
    uint32_t histLen;
    uint64_t purgeUndoLogNum;
    uint64_t purgeUndoRecordNum;
} UndoPurgerInfoT;

typedef struct TagTableSpaceStatInfo {
    bool isFree;
    bool isUseRsm;
    uint16_t initSize;  // 以下unit: MB
    uint16_t stepSize;
    uint16_t maxSize;
    uint32_t tableSpaceId;
    uint32_t usedSize;  // unit: B
    double usedRatio;
    // isUseRsm为true，下面字段才有值
    uint32_t compressCnt;         // 触发过几次搬迁回收任务
    uint64_t freeDeviceCnt;       // 释放过多少块device内存
    uint64_t moveRsmSubBlockCnt;  // 搬迁过几个页
} TspStatInfoT;

typedef struct EdgeMemoryStat {
    char *labelName;
    uint32_t labelNameLen;  // strlen(labelName) + 1
    uint32_t tableSpaceId;
    uint32_t reserve;
    uint32_t pageHeadSize;         // size of each page head of edge topo
    uint32_t rowHeadSize;          // size of each row head
    uint32_t edgePayloadSize;      // size of each row payload
    uint32_t totalRowNum;          // total row's number of each edgelabel
    uint32_t totalPageNum;         // number of pages occupied by each edgelabel
    uint32_t eachPageSize;         // size of each page
    uint32_t memUtilizationRatio;  // memory utilization ratio (%)
    uint64_t memUnusedSize;        // unused memory size (B)
    uint64_t totalPageSize;        // size of total page
    uint32_t fsmPageNum;           // number of pages occupied by fsm
    uint32_t fsmEachPageSize;      // size of each fsm page
    uint64_t totalFsmSize;         // size of total fsm page
    uint64_t fsmFreeSize;          // size of free in fsm pages
    bool isSupportReservedMemory;
} EdgeMemoryStatT;

typedef struct UndoStat {
    uint32_t rollbackPageNum;
    uint32_t undologPageNum;
    uint32_t pageSize;
    uint32_t insertUndoSize;
    uint32_t updateUndoSize;
    uint32_t deleteUndoSize;
    uint32_t totalUndoSize;
    uint32_t memUnusedSize;
    uint32_t memUtilizationRatio;
    uint16_t numRsegs;
    uint32_t numUndoSegs;
    uint32_t numUndoRecs;
    uint32_t numRollUndoRecs;
} UndoStatT;

typedef struct BufpoolStat {
    uint64_t size;
    uint32_t pageSize;
    uint16_t recyclePolicy;
    uint8_t resizeStatus;  //   bufferpool 在线调整时候resize状态
    uint32_t capacity;
    uint32_t hwm;
    uint32_t priorityListMinimum;
    uint32_t scanListLimit;
    uint32_t lruListCount;
    uint32_t scanListCount;
    uint32_t priorityListCount;
    uint32_t bucketNum;  // Bucket Num
} BufpoolStatT;

typedef struct LoadTableItemStatus {
    uint32_t labelId;
    uint8_t loadStatus;
} LoadTableItemStatusT;

typedef struct TagDumemStatT {
    uint32_t allocatedPageCnt;  // 申请的总device包含的总页数allocatedPageCnt = usedPageCnt + freePageCnt +
                                // unusedPageCnt
    uint32_t usedPageCnt;       // 使用的页数
    uint32_t freePageCnt;       // 归还后空闲的页数
    uint32_t unusedPageCnt;     // 从未被使用的页数
    double usedPageRatio;       // usedPageCnt / allocatedPageCnt
} DumemStatT;

typedef struct DataFileStat {
    uint32_t id;
    uint64_t size;
    uint64_t usedSize;
} DataFileStatT;

typedef struct SpaceStat {
    char name[DB_MAX_NAME_LEN];
    uint32_t id;
    uint32_t type;
    uint64_t maxSize;     // unit: B
    uint32_t deviceSize;  // unit: KB
    uint64_t dbFileSize;  // unit: B
    uint32_t fileCntHwm;
    uint32_t dataFileCount;
    uint64_t curSize;
    uint64_t actualSize;
    bool isFound;  // 是否查询到了信息
    DataFileStatT fileStat[STAT_MAX_FILE_CNT_PER_SPACE];
} SpaceStatT;

typedef struct CompressStat {
    uint32_t capacity;
    uint32_t compressedPageCnt;
    uint64_t usedSize;
    uint64_t swapInCnt;
} CompressAreaStatT;

typedef struct RedoStat {
    volatile uint32_t redoSize;  // 这种类型redo日志已使用的大小
    volatile uint32_t redoNum;   // 这种类型redo日志已经记录的个数
} RedoStatT;

Status UndoLogGetMemStat(SeRunCtxHdT seRunCtx, UndoStatT *undoStat);

/**
 * @brief 查询Bufferpool的DFX信息
 * @param SeInstanceT : 存储实例指针
 * @param BufpoolStatT : 出参, Bufferpool统计信息的结构体
 * @return 成功或者其他失败原因
 */
Status BufpoolGetStatDfx(BufpoolStatT *bufpoolStat, DbInstanceHdT dbInstance);

Status DumemGetStatDfx(SeInstanceT *seIns, DumemStatT *dumemStat);

/**
 * @brief 查询Compress的DFX信息
 * @param SeInstanceT : 存储实例指针
 * @param CompressAreaStatT : 出参, Compress统计信息的结构体
 * @return 成功或者其他失败原因
 */
Status CompressAreaGetStatDfx(SeInstanceT *seIns, CompressAreaStatT *compressStat);

/**
 * @brief 查询Space的DFX信息
 * @param seIns : 存储实例指针
 * @param spaceIndex : space数组下标
 * @param spaceStat : 出参, Space统计信息的结构体
 * @return 成功或者其他失败原因
 */
SO_EXPORT_FOR_TS Status SpaceGetNextUsedStat(SeInstanceT *seIns, uint16_t *spaceIndex, SpaceStatT *spaceStat);

/**
 * @brief 查询FSM的DFX信息
 * @param heapCntrAcsInfo : 需要修改的heap的相关信息
 * @param heapFsmStat : 出参, FSM统计信息的结构体
 * @return 成功或者其他失败原因
 */
Status HeapLabelGetLfsFsmStat(HeapCntrAcsInfoT *heapCntrAcsInfo, FsmStat *heapFsmStat);

/**
 * @brief 查询一个 heap label的DFX信息
 * @param heapCntrAcsInfo : 需要修改的heap的相关信息
 * @param heapPerfStat : 出参, heap性能统计信息的结构体
 * @return 成功或者其他失败原因
 */
Status HeapLabelGetPerfStat(HeapCntrAcsInfoT *heapCntrAcsInfo, HeapPerfStatT *heapPerfStat, HeapCfgStatT *heapCfgStat);

/**
 * @brief 保留内存重启恢复场景，重新刷新dfx
 * @param heapCntrAcsInfo : 需要修改的heap的相关信息
 * @param recordNum : 逻辑计数与物理计数
 * @param writeBytes : 写入字节数
 * @return 成功或者其他失败原因
 */
Status HeapLabelSetPerfStatAfterRecovery(HeapCntrAcsInfoT *heapCntrAcsInfo, uint64_t recordNum, uint64_t writeBytes);

Status HeapLabelGetDefragStat(HeapCntrAcsInfoT *heapCntrAcsInfo, HeapDefragStatT *defragStat);

/**
 * @brief 查询一个 heap label 的DFX信息
 * @param heapCntrAcsInfo : 需要修改的heap的相关信息
 * @param heapMemoryStat : 出参, heap内存使用情况统计信息的结构体
 * @return 成功或者其他失败原因
 */
Status HeapLabelGetMemoryStat(HeapCntrAcsInfoT *heapCntrAcsInfo, HeapLabelMemoryStatT *heapMemoryStat);

/**
 * @brief 查询一个 heap label的DFX信息
 * @param heapOpenCfg: 入参
 * @param usrMemCtx 入参，HeapLabelOpen时设置的memCtx
 * @param heapPerfStat : 出参, heap lock信息的结构体
 * @return 成功或者其他失败原因
 */
Status HeapLabelGetLockStat(HeapRunCtxAllocCfgT *heapRunCtxAllocCfg, DbMemCtxT *usrMemCtx, LockStatT *heapLockStat);

/* *
 * @brief 从存储引擎实例中获取MemData的状态信息
 * @param seInstance  入参，存储引擎实例ID
 * @param memDataStat  出参, 存放memData的统计信息
 * @return 成功或失败原因
 */
Status SeGetMemdataStat(SeRunCtxHdT seRunCtx, MemDataInfoT **memDataStat, DbMemCtxT *dynamicMemCtx);

void SeGetLockOverView(SeRunCtxHdT seRunCtx, SeLockOverviewStatT *lockStat);

Status SeInitMemdataInfo(DbMemCtxT *dynamicMemCtx, SeRunCtxHdT seRunCtxHd, MemDataInfoT **memDataStat);

/**
 * @brief 获取所有tableSpace的信息
 * @param seRunCtx  入参，存储运行上下文
 * @param tspInfoArr 入参 & 出参, 需要设置tableSpaceId，查找对应的tableSpace，其他字段为出参
 * @return 成功或失败原因
 **/
Status SeGetStatTspInfo(SeRunCtxHdT seRunCtx, TspStatInfoT *tspInfo);

Status TrxGetUndoStat(SeRunCtxHdT seRunCtx, UndoStatT *trxUndoStat);

/**
 * @brief 获取当前活跃事务状态信息并打印
 * @param seRunCtx  入参，存储运行上下文
 **/
SO_EXPORT_FOR_TS void SeTrxFetchAndPrintfActiveTrxStat(SeRunCtxHdT seRunCtx);

Status ResColGetPoolStat(ResoucePoolStatT *resPoolStat, uint32_t scanPoolCnt, bool *isEof);

Status ResColGetPoolAndTableStat(ResoucePoolAndTableStatT *resPoolAndTableStat, uint32_t scanPoolCnt, bool *isEof);

Status ResColGetPoolBitmapStat(
    DbMemCtxT *memCtx, ResoucePoolBitmapStatT *resPoolBitmapStat, uint32_t scanPoolCnt, bool *isEof);

Status ResColGetStartIndexStatByResPoolShmPtr(
    ResoucePoolStartIndexStatT *stat, ShmemPtrT resPoolShmPtr, uint32_t startIndex, uint32_t count);

Status ResColGetResPoolStatByResId(ResoucePoolResIdStatT *stat, uint64_t resId);

Status ResColGetResPoolStatByShmPtr(ResoucePoolStatT *stat, ShmemPtrT resPoolShmPtr);

Status ResColGetExternResPoolStatByShmPtr(ResoucePoolStatT *stat, ShmemPtrT resPoolShmPtr);

void HeapDfxHandleOnTrxCommit(HpRunHdlT heapRunHdl);

void HeapDfxHandleWhenRrTrxCommit(
    const TrxT *trx, HpRunHdlT heapRunHdl, uint32_t writeBytes, uint32_t deleteBytes, uint32_t opType);

void HeapUpdateLockStat(HeapCntrAcsInfoT *heapCntrAcsInfo, LockStatT *lockStat);

Status UndoGetPurgerInfo(UndoPurgerInfoT *info, DbInstanceHdT dbInstance);

Status ClusteredHashGetLabelStat(ClusteredHashStatT *stat, ShmemPtrT shmAddr);

/**
 * @brief 获取事务的savePoint数量
 * @param seRunCtx  入参，存储运行上下文
 * @return uint32_t 当前事务的savePoint数量
 **/
uint32_t TrxGetSavePointNum(SeRunCtxHdT seRunCtx);

/**
 * @brief 获取指定savePoint的名字信息
 * @param seRunCtx  入参，存储运行上下文
 * @param index  入参, savePoint链表下标
 * @param savePointName  出参, 指定savePoint的名字信息
 * @return 成功或失败原因
 **/
Status TrxGetSavePointNameByIndex(SeRunCtxHdT seRunCtx, uint32_t index, SeTrxSavepointNameT *savePointName);

Status SeGetRsmBlockStat(
    SeRunCtxHdT seRunCtx, uint32_t rsmBlockId, DbMemCtxT *dynamicMemCtx, RsmBlockInfoT **rsmBlockStat);

#ifdef __cplusplus
}
#endif

#endif
