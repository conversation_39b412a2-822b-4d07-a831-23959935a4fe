/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Provides five external interfaces for creating, writing, reading, locating, and releasing temporary
 * files.
 * Create: 2024-1-2
 */

#ifndef SE_TEMP_FILE_H
#define SE_TEMP_FILE_H

#include "db_mem_context.h"

#ifdef __cplusplus
extern "C" {
#endif

#define BLCKSZ SIZE_K(8)                 // One buffer page size
#define MAX_PHYSICAL_FILESIZE SIZE_G(1)  // The maximum size of a temporary file is 1 GB

typedef union BufferFileBlock {
    char data[BLCKSZ];  // buffer page
} BufferFileBlockT;

typedef struct TempFileMgr TempFileMgrT;

typedef struct BufferFile {
    bool isDirty;               // does buffer need to be written?
    bool isExtendFile;          // Whether to extend temporary files
    uint32_t vfdCnt;            // number of physical files in set
    uint32_t vfdIdx;            // file index (0..n) part of current pos
    uint32_t vfdCap;            // number of current file descriptors
    uint32_t *vfds;             // array with vfdCnt entries
    uint32_t nBytes;            // total # of valid bytes in buffer
    uint32_t pos;               // next read/write position in buffer
    int64_t curOffset;          // offset part of current pos
    int64_t maxFileSize;        // the maximum size of a temporary file
    BufferFileBlockT buffer;    // buffer page
    DbMemCtxT *bufFileMemCtx;   // memory created for buffer file
    TempFileMgrT *tempFileMgr;  // managing temporary files
} BufferFileT;

/*
 * @brief Create a temporary file.
 * @param[in] memCtx : Memory contex for alloc parameter.
 * @param[out] file : Temporary file manager(null file manager pointer reference).
 * @return : GMERR_OK, or other errors.
 */
Status SeBufFileCreate(DbMemCtxT *memCtx, BufferFileT **file);

/*
 * @brief Write data to temporary file.
 * @param[in] file : Temporary file manager.
 * @param[in] ptr : Pointer to the buffer page.
 * @param[in] size : Data size.
 * @param[in] isOffset : false: write by block, true: write by offset
 * @param[out] result : Size of the data that is successfully written.
 * @return : GMERR_OK, or other errors.
 */
SO_EXPORT_FOR_TS Status SeBufFileWrite(BufferFileT *file, void *ptr, size_t size, size_t *result, bool isOffset);

/*
 * @brief Flush buffer to tempfile.
 * @param[in] file : Temporary file manager.
 * @return : GMERR_OK, or other errors.
 */
Status SeBufFileFlush(BufferFileT *file);

/*
 * @brief Read data from temporary files
 * @param[in] file : Temporary file manager.
 * @param[in] ptr : Pointer to the buffer page.
 * @param[in] size : Data size.
 * @param[out] result : Size of the data successfully read.
 * @return : GMERR_OK, or other errors.
 */
Status SeBufFileRead(BufferFileT *file, void *ptr, size_t size, size_t *result);

/*
 * @brief Find the location of the block number in the temporary file.
 * @param[in] file : Temporary file manager.
 * @param[in] blockNum : The temporary file is divided into multiple blocks of the BLCKSZ size.
 *                       You can find the corresponding read/write location based on the block number.
 * @return : GMERR_OK, or other errors.
 */
Status SeBufFileSeekBlock(BufferFileT *file, uint64_t blockNum);

/*
 * @brief Find the location of the offset in the temporary file.
 * @param[in] file : Temporary file manager.
 * @param[in] offset : Determine the read/write location.
 * @return : GMERR_OK, or other errors.
 */
Status SeBufFileSeekOffset(BufferFileT *file, uint64_t offset);

/*
 * @brief clear temporary files memory.
 * @param[in] file : Temporary file manager.
 */
void SeBufFileClear(BufferFileT *file);

/*
 * @brief Close and clear temporary files.
 * @param[in] file : Temporary file manager.
 */
SO_EXPORT_FOR_TS void SeBufFileClose(BufferFileT *file);

/*
 * @brief Close temporary files without deletion.
 * @param[in] file : Temporary file manager.
 */
SO_EXPORT_FOR_TS void SeBufFileCloseWithoutDelete(BufferFileT *file);

/*
 * @brief Create a file in a specified directory.
 * @param[in] memCtx : Memory contex for alloc parameter.
 * @param[in] filePath : a specified directory.Precautions: The interface does not verify the validity of the path.
 * @param[in] isLargeResult : is for large result.
 * @param[out] file : Temporary file manager(null file manager pointer reference).
 * @return : GMERR_OK, or other errors.
 */
SO_EXPORT_FOR_TS Status SeSpecifiedBufFileCreate(
    DbMemCtxT *memCtx, char *filePath, bool isLargeResult, BufferFileT **file);

/*
 * @brief load a file with a given specified directory.
 * @param[in] memCtx : Memory contex for alloc parameter.
 * @param[in] filePath : a specified directory.Precautions: The interface does not verify the validity of the path.
 * @param[out] file : Temporary file manager(null file manager pointer reference).
 * @return : GMERR_OK, or other errors.
 */
Status SeSpecifiedBufFileLoad(DbMemCtxT *memCtx, char *filePath, BufferFileT **file);

/*
 * @brief Read data from temporary files
 * @param[in] file : Temporary file manager.
 * @param[in] buffer : Content to be written.
 * @param[in] size : Data size.
 * @return : GMERR_OK, or other errors.
 */
Status SeSpecifiedBufFileWrite(BufferFileT *file, void *buffer, size_t size);

/*
 * @brief fsync temporary file
 * @param[in] file : Temporary file manager.
 * @return : GMERR_OK, or other errors.
 */
Status SeBufFileFsync(BufferFileT *file);

/*
 * @brief Get temporary file path
 * @param[out] file : Temporary file path.
 * @return : GMERR_OK, or other errors.
 */
SO_EXPORT_FOR_TS Status GetTempFileDirForLargeResult(char *filepath);

/*
 * @brief Set temporary file not to be expanded, This function cannot be used together with the file tape and seek
 * functions.
 * @param[in] file : Temporary file manager.
 */
SO_EXPORT_FOR_TS void SeSetBufFileNoExtend(BufferFileT *file);

/*
 * @brief Clear temp file directory.
 */
SO_EXPORT_FOR_TS Status SeClearTempFile(void);

#ifdef __cplusplus
}
#endif
#endif
