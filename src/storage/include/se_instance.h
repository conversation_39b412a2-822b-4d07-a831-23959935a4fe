/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: 存儲實例，提供存儲引擎初始化，以及獲取存儲實例等相關接口。
 * 以及相關的存儲實例配置都會在此模塊中定義。
 * Author: yangenle
 * Create: 2020-8-12
 */
#ifndef SE_INSTANCE_H
#define SE_INSTANCE_H

#include "db_mem_context.h"
#include "db_storage_dfgmt.h"
#include "se_capacity_def_pub.h"
#include "db_se_heap_serialization.h"
#include "db_se_trx.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef uint16_t PageSizeT;
typedef uint32_t PageTotalSizeT;  // 64K的页，表示总量时需要32位，否则会溢出
typedef uint16_t SeInstanceIdT;

struct TagSeRunCtxT;
typedef struct TagSeRunCtxT *SeRunCtxHdT;
typedef struct TagSeInstanceT SeInstanceT;
typedef SeInstanceT *SeInstanceHdT;

typedef struct StRedoMgr RedoMgrT;
typedef struct StDatabase StDatabaseT;

typedef Status (*SeUndoPurgerCreateFunc)(DbInstanceHdT dbInstance);
typedef Status (*SeDafPurgerCreateFunc)(void);
typedef bool (*SeKeepThreadAliveFunc)(DbInstanceHdT dbInstance);

typedef struct TagSeConfigT {
    uint32_t deviceSize;    // size of device, unit: K
    uint32_t extendSize;    // size of extend, unit: K
    uint16_t pageSize;      // size of page, unit: K
    uint16_t ctrlPageSize;  // size of ctrl page, unit: K
    uint32_t maxSeMem;      // total size of memory, unit: K (当前支持16G，更大需要使用uint32)
    uint16_t instanceId;    // current storage engine of id
    bool isUseRsm;
    bool isUseRsmExtendBlock;
    bool enableReleaseDevice;
    uint16_t maxLockShareCnt;   // 最大session数除以2
    uint16_t bufferPoolPolicy;  // the policy of recycling pages,default value is BUF_RECYCLE_TABLE(1)
    uint16_t maxTrxNum;     // 从配置文件中读出最大事务个数 + DB自己预留的个数(MAX_BG_WORKER_NUM)
    uint32_t rsmBlockCnt;   // 需要扣除给控制区的1个, 这里是存储的
    uint32_t rsmBlockSize;  // size of rsm, unit: M
    uint32_t rsmExtendBlockSize;            // size of rsm extend block, unit: M
    uint32_t rsmDfgmtRateThreshold;         // 触发保留内存搬迁回收的空闲率阈值(%)
    uint32_t fragmentationRateThreshold;    // 单表触发缩容的最小碎片率(%)
    uint32_t lockWakeupPeriod;              // 事务锁等待时被唤醒尝试加锁的周期(ms)
    uint32_t deadlockCheckPeriod;           // 事务锁等待时进行死锁检测的周期(ms)
    uint32_t lockJumpQueuePeriod;           // 事务锁等待时插队到等待队列队首的周期(ms)
    uint32_t lockTimeOut;                   // 事务锁等待超时的时间(ms)
    uint32_t fragmentationMemoryThreshold;  // 单表触发缩容的最小内存(M)
    SeHpTupleAddrMode heapTupleAddrMode;
    SeHpTupleAddrMode rsmHeapTupleAddrMode;
    SeUndoPurgerCreateFunc undoPurgerCreate;
    SeDafPurgerCreateFunc dafPurgerCreate;
    SeKeepThreadAliveFunc seKeepThreadAlive;

    // redo config
    uint32_t persCompMode;
    uint32_t redoPubBufSize;  // uint: B
    uint32_t redoBufParts;
    uint32_t redoFlushByTrx;
    uint32_t redoFlushBySize;  // uint: B
    uint32_t redoFlushByTime;
    uint32_t redoFlushCheckPeriod;
    char **redoDir;         // [DB_MAX_MULTIZONE][路径长度，按需分配];
    uint32_t redoFileSize;  // uint: B
    uint32_t redoFileCount;
    uint32_t redoFileDropOnClose;

    // check point config
    uint32_t ckptPeriod;     // checkpoint触发时间周期(s)
    uint32_t ckptThreshold;  // checkpoint触发队列水位
    uint32_t dwrBlockNum;
    uint32_t bufferPoolSize;  // size of bufferpool, unit: KB
    uint32_t bufferPoolNum;   // number of bufferpool instances
    uint32_t bpChunkSize;     // chunk size while page buf split , unit: KB, max 4G(cfg) 4T(field type)
    bool isRedoConfigInit;
    bool dwrEnable;  // double write enabled config
    bool crcCheckEnable;
    bool isReadOnlyInstance;  // read only instance , 此标志位决定checkpoint是否能落盘
    bool shaCheckEnable;
    bool condensedCtrlPages;  // ctrl page 紧密排布，按需申请，没有预留页
    bool fixPersistEnable;

    // encrypt config
    bool encryptReservedEnable;

    // space
    char **dataFileDirPath;  // [DB_MAX_MULTIZONE][路径长度，按需分配];
    char **ctrlFileDirPath;  // [DB_MAX_MULTIZONE][路径长度，按需分配];
    char recoveryPath[DB_MAX_PATH];
    char *ctrlFileName;
    char *systemSpaceFileName;
    char *undoSpaceFileName;
    char *userSpaceFileName;
    char *safeFileName;
    char *redoFilePrefix;
    char *tamperProofSoPath;  // 防篡改so路径，DB动态加载so中的摘要计算函数
    uint32_t dbFilesMaxCnt;
    uint32_t spaceMaxNum;
    uint32_t dbFileSize;         // unit: K
    bool enableSyncWriteFile;    // 是否以同步的方式写文件
    bool compressSpaceEnable;    // 是否打开压缩的space
    bool tamperProofEnable;      // 是否打开防篡改
    uint8_t bufferpoolMemType;   // Bufferpool在动态内存或共享内存部署
    uint32_t compressSpaceSize;  // 设置压缩的space大小

    // 多区持久化
    uint32_t recoveryZoneId;
    uint32_t multiZoneNum;

    uint16_t sharedModeEnable;

    bool preFetchPagesEnable;
    uint32_t maxPreFetchThreNum;
    uint32_t loadTablePriorityRatio;
    uint32_t bufferPoolPriorityRatio;
} SeConfigT;

Status StorageConfigGet(DbInstanceHdT dbInstance, SeConfigT *config, const char *recoveryPath);

typedef enum EnumStorageStatus {
    // corectrl still in memory[creating phase]/storage is run on memory, this CAN'T be seem in file
    SE_IN_MEMORY_ONLINE,
    SE_ON_DISK_CREATE = SE_IN_MEMORY_ONLINE,
    // db is running, if this is shown in corectrl at reboot, meaning db is shut abnormally
    SE_ON_DISK_ONLINE,
    // db is closed, if this is shown in corectrl at reboot, meaning db is shut normally
    SE_ON_DISK_OFFLINE,
    // when corectrl shows SE_ON_DISK_OFFLINE on reboot, meaning db is shut normally, db would just open it
    SE_ON_DISK_OPEN_LOAD = SE_ON_DISK_OFFLINE,
    // when corectrl shows SE_ON_DISK_ONLINE on reboot, meaning db is shut abnormally, db would go through recover
    // process
    SE_ON_DISK_OPEN_RECOVER,
    // disk file is damaged, all services are denied, this CAN'T be seem in file
    SE_ON_DISK_EMRGNCY,
    // when corectrl shows SE_ON_DISK_BACKING_UP on reboot, meaning this is unfinished backup fileset, abort start
    SE_ON_DISK_BACKING_UP,
    // when corectrl shows SE_ON_DISK_BACK_UP_FULL on reboot, meaning this is finished backup fileset with data
    SE_ON_DISK_BACK_UP_FULL,
    // when corectrl shows SE_ON_DISK_BACK_UP_SCHEMA on reboot, meaning this is finished backup fileset without data
    SE_ON_DISK_BACK_UP_SCHEMA,
    SE_ON_DISK_UPGRADE,
    SE_STATUS_END,
} StorageStatusE;

SO_EXPORT_FOR_TS void SeSetStorageEmergency(SeInstanceHdT seIns, char *emergencyMsg);
SO_EXPORT_FOR_TS void SeSetStorageNotEmergency(SeInstanceHdT seIns);
// 只能设置emergency之外的状态
SO_EXPORT_FOR_TS Status SeSetStorageStatus(SeInstanceHdT seIns, StorageStatusE status);
SO_EXPORT_FOR_TS StorageStatusE SeGetStorageStatus(SeInstanceHdT seIns);
/* *
 * @brief 创建一个存储实例出来，这个存储实例会从共享内存创建出来。
 * @param dbInstance DB实例句柄，仅在终端场景下有效，非终端场景可传空
 * @param topShmMemCtx  存储引擎的memctx将由这个入参派生出来。
 * @param config 存储实例的配置项，从配置文件读取出来，进行初始化。
 * @param instance 出参，创建出来的存储引擎实例。
 * @return void 返回一个存储实例的句柄。
 */
Status SeCreateInstance(
    DbInstanceHdT dbInstance, DbMemCtxT *dbTopShmMemCtx, const SeConfigT *config, SeInstanceHdT *instance);

SO_EXPORT void SeDestroyInstance(uint16_t instanceId);

SO_EXPORT void SeDestroyGlobalResource(void);

/* *
 * @brief According to instaceId, get a handle of stroage top memctx
 * @return void* return a handle of staorge instance
 */
SO_EXPORT_FOR_HAC_AND_TS void *SeGetInstance(uint16_t instanceId);

SO_EXPORT_FOR_TS bool SeBufPoolDeployInDynMem(uint16_t instanceId);

typedef struct TagSeOpenCfg {
    bool isClient;
    bool isDirectWrite;
    uint16_t trxSlot;
    uint32_t reserved2;
    ShmemPtrT rsmCtxPtr;
    DbMemCtxT *reserveMemCtx;  // 直连写预留
    DbMemCtxT *pageMgrMemCtx;  // 客户端：g_gmdbCltInstance.memCtx
} SeOpenCfgT;

typedef struct TagSeInitMemCtx {
    DbMemCtxT *dbTopShmMemCtx;
    DbMemCtxT *seTopMemCtx;
    DbMemCtxT *seTopShmMemCtx;
    DbMemCtxT *seEscapeMemCtx;
} SeInitMemCtxT;

/* *
 * @brief 在当前线程或连接下，打开一个存储运行上下文。
 * @param instanceId  打开某一个存储实例ID的运行上下文。
 * @param sessionDynMemCtx 会话级生命周期的内存上下文，由上层模块提供
 * @param cltSeOpenCfg 客户端SeOpen所需的参数
 * @return int32_t 当内部出现错误后，返回一个错误码。
 */
SO_EXPORT_FOR_TS Status SeOpen(
    uint16_t instanceId, DbMemCtxT *sessionDynMemCtx, const SeOpenCfgT *seOpenCfg, SeRunCtxHdT *seRunCtx);

/* *
 * @brief 关闭一个存储运行的上下文。如当前线程退出，或链接关闭，调用此接口，释放内部内存等资源。
 * @param seRunCtx   存储运行的上下文。
 * @return int32_t 当内部出现错误后，会返回一个错误码。
 */
SO_EXPORT_FOR_TS Status SeClose(SeRunCtxHdT seRunCtx);

/* *
 * @brief 存储桩接口, 获取一个fileId(自增)
 */
GMDB_EXPORT uint32_t SeStubGetNewFileId(void);

Status SeLockResourceDestroy(uint16_t instanceId);

SO_EXPORT_FOR_HAC_AND_TS void *SeShmAlloc(DbMemCtxT *shmCtx, uint32_t size, ShmemPtrT *shmPtr) __attribute__((malloc))
__attribute__((alloc_size(2)));

SO_EXPORT_FOR_HAC void *SeShmAllocAlign(DbMemCtxT *shmCtx, uint32_t size, ShmemPtrT *shmPtr, uint32_t alignSize)
    __attribute__((malloc)) __attribute__((alloc_size(2)));

/* *
 * @brief 获取用于存储userData的共享内存的使用率。
 * @param memCtx 入参，sysview实例的内存上下文
 * @param usedRatio 出参，共享内存的使用率。
 * @return int32_t 当内部出现错误后，会返回一个错误码。
 */
Status SeGetUserDataAlmRatio(DbMemCtxT *memCtx, double *usedRatio);

/* *
 * @brief 获取所有tableSpace中最高的使用率。
 * @param memCtx 入参，sysview实例的内存上下文
 * @param usedRatio 出参，所有tableSpace中最高的使用率。
 * @return int32_t 当内部出现错误后，会返回一个错误码。
 */
Status SeGetUserTableSpaceAlmRatio(DbMemCtxT *memCtx, double *usedRatio);

/* *
 * @brief 获取保留内存中所有tableSpace中最高的使用率。
 * @param memCtx 入参，sysview实例的内存上下文
 * @param usedRatio 出参，所有tableSpace中最高的使用率。
 * @return int32_t 当内部出现错误后，会返回一个错误码。
 */
Status SeGetRsmUserTableSpaceAlmRatio(DbMemCtxT *memCtx, double *usedRatio);

/*
 * @brief 用于流控获取存储实际使用的page的共享内存总和
 * @param ctx: 存储的实例seInstance
 * @return uint64_t 存储实际使用的page的共享内存总和
 */
uint64_t SeGetUsedPageMemSize(void *seInstance);

Status SeGetStorageSpaceAlmRatio(DbMemCtxT *memCtx, double *usedRatio);

Status SeGetStorageSpaceAlmRatioByPeriod(DbMemCtxT *memCtx, double *usedRatio);

Status SeGetStorageFileAlmRatio(DbMemCtxT *memCtx, double *usedRatio);

void DbSeAlarmUpdateLastSuccCnt(uint32_t succCnt);

void DbSeAlarmUpdateLastFailCnt(uint32_t failedCnt);

SeHpTupleAddrMode SeGetAddrModeWithSeIns(const SeInstanceT *seIns);

/*
 * @brief 获取服务端存储上下文中的权限模式
 * @param ctx: 存储运行上下文SeRunCtx
 * @return int32_t 权限模式类型（0：不authenticate，1：宽容模式，2：强制authenticate）
 */
uint32_t SeGetPolicyMode(const SeRunCtxHdT seRunCtx);

void SeCalcHeapTupleAddrMode(SeConfigT *config);

Status SeGetHeapTupleAddrLenByInstanceId(uint16_t instanceId, uint32_t *len);

SO_EXPORT void SeClearDevCache(void);
SO_EXPORT void SeReleaseCltPageMgr(DbMemCtxT *memCtx);

// key reource dfx
Status SeGetTopShmctxUsedSize(uint16_t instanceId, uint64_t *size);

Status SeGetDevShmctxUsedSize(uint16_t instanceId, uint64_t *size);

SO_EXPORT void SeThreadVarSwitch(SeRunCtxHdT seRunCtx);

SO_EXPORT_FOR_TS Status SeMallocPathConfigMem(DbMemCtxT *memCtx, SeConfigT *config, char ***path);
void SeReleasePathConfigMem(DbMemCtxT *memCtx, SeConfigT *config, char **path);
SO_EXPORT void SeReleaseConfigMem(DbMemCtxT *memCtx, SeConfigT *config);

Status InitSeInstance(
    DbInstanceHdT dbInstance, SeInitMemCtxT *seInitMemCtx, const SeConfigT *config, SeInstanceT *seInsPtr);
void SeSetInterProcUtils(SeInstanceHdT seInsPtr);
void SeInitSingleProcess(SeInstanceT *seIns);

Status SeBindCpuSet(SeRunCtxHdT seRunCtx, void *cpuSet);

// 表明当前这个seRunCtx对应的操作是swap操作；后续该seRunCtx不会再等待或上其他trxLatch锁
SO_EXPORT_FOR_TS void SeMarkSwapCtx(SeRunCtxHdT seRunCtx);
SO_EXPORT_FOR_TS void SeUnMarkSwapCtx(SeRunCtxHdT seRunCtx);

#ifdef __cplusplus
}
#endif

#endif
