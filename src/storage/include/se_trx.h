/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: Transaction 对外提供的接口和结构
 * Author: SE Team
 * Create: 2020-11-20
 * Notes:
 */

#ifndef SE_TRX_H
#define SE_TRX_H

#include "se_instance.h"
#include "db_se_trx.h"
#include "db_list.h"
#include "db_resource_session_pub.h"
#include "db_rsm_undo.h"

#ifdef __cplusplus
extern "C" {
#endif
#define SAVEPOINT_NAME_MAX_SIZE 128
#define SAVEPOINT_NAME_MAX_SIZE_SYSVIEW_STR SYSVIEW_NUM_TO_STRING(SAVEPOINT_NAME_MAX_SIZE)

#define SE_CONN_INFO_MAX_LEN 220
#define SE_LABEL_INFO_BUFF_LEN (MAX_TABLE_NAME_LEN + 20)
// 由于乐观事务增强日志前面会记录日期时间信息、错误码对应的固定注释等, 日志截断长度调整为100
#define SE_PEER_AND_LABELS_INFO_THRESHOLD 100
#define SE_LABELS_CHAR_BUFF_LEN (SE_PEER_AND_LABELS_INFO_THRESHOLD + SE_LABEL_INFO_BUFF_LEN)

typedef uint64_t TrxIdT;

typedef struct UndoLiteRecord UndoLiteRecordT;

typedef struct Trx TrxT;

typedef struct TrxBaseInfo TrxBaseInfoT;

typedef struct TrxParam TrxParamT;  // 事务通用参数

typedef enum {
    TRX_STATE_NOT_STARTED,
    TRX_STATE_ROLLBACK,
    TRX_STATE_ACTIVE,
    TRX_STATE_ABORT,
    TRX_STATE_COMMITTED,
    TRX_STATE_FAILURE
} TrxStateE;

typedef enum {
    TRX_ClONE_TYPE_DEFAULT_TRX = 0,  // 普通事务，不支持克隆特性
    TRX_ClONE_TYPE_CLONABLE_TRX = 1,  // 支持克隆特性的事务，可以被克隆，如果不为1需要配套修改视图
    TRX_ClONE_TYPE_SUCCESSOR_TRX = 2,  // 一个连接的事务被克隆后，该连接生成的继承者事务（原事务不再继续执行操作）
    TRX_ClONE_TYPE_CLONE_TRX = 3,  // 克隆出来的事务
    TRX_ClONE_TYPE_CLONED = 4,     // 父事务被克隆后，进入已被克隆状态，无法再被克隆
} TrxCloneTypeE;

// 事务直连读状态，使用原子操作切换
typedef enum {
    TRX_DIRECT_READ_STATE_READABLE,         // 没有超时，也没有读
    TRX_DIRECT_READ_STATE_READING,          // 没有超时，正在直连读
    TRX_DIRECT_READ_STATE_TIMEOUT_READING,  // 超时，正在直连读
    TRX_DIRECT_READ_STATE_UNREADABLE        // 超时，没有读，此时不允许再读需要回滚
} TrxDrStateE;

typedef enum {
    TRX_EDGELABEL_HEAP,
    TRX_KVLABEL_HEAP,
    TRX_VERTEXLABEL_HEAP,
    TRX_CHECK_READVIEW_NUM
} TrxCheckLabelReadViewTypeE;

typedef void (*UpdateStatusMergeListFunc)(void *statusMergeSubDataSet);
typedef struct {
    UpdateStatusMergeListFunc func;
    void *statusMergeSubDataSet;
} UpdateStatusMergeListCfgT;

typedef Status (*TrxCommitCallBackFunc)(void *para);
typedef struct {
    TrxCommitCallBackFunc func;
    void *parameter;
} TrxCommitCallBackCfgT;

void SeSetTrxCommitStatusMergeCfg(SeRunCtxHdT seRunCtx, const UpdateStatusMergeListCfgT *cfg);

void SeSetTrxCommitCallBackCfg(SeRunCtxHdT seRunCtx, const TrxCommitCallBackCfgT *cfg);

typedef Status (*OptiTrxSetLabelLastTrxIdAndTrxCommitTime)(
    uint32_t labelId, uint64_t trxId, uint64_t trxCommitTime, bool trxIsModify, DbInstanceHdT dbInstance);

typedef Status (*OptiTrxGetLabelLastTrxIdAndTrxCommitTime)(
    uint32_t labelId, uint64_t *trxId, uint64_t *trxIdLastModify, uint64_t *trxCommitTime, DbInstanceHdT dbInstance);

typedef Status (*OptiTrxGetLabelName)(uint32_t labelId, char *labelName, DbInstanceHdT dbInstance);

typedef struct TagTrxCfg {
    bool readOnly;
    bool isLiteTrx;
    bool isBackGround;
    bool isInteractive;
    bool isTrxForceCommit;  // 乐观事务使用，提交发生冲突后，是否进行重试处理，默认为false（GmcForceCommitModeE）;
    bool isRetryTrx;  // isForceCommit为true时，会新开一个事务进行重演，该标记位表示是否为新开的重试事务，默认为false
    bool isTrxAllowCloned;  // 乐观事务使用，是否允许被clone，默认为false
    bool isCloneTrx;        // isTrxAllowCloned为true时，开启克隆事务进行操作，默认为false
#ifdef FEATURE_GQL
    bool skipRowLockPessimisticRR;  // cfg designed for FES, default: false
    bool reserve;
#endif
    uint16_t connId;
    TrxTypeE trxType;
    IsolationLevelE isolationLevel;
} TrxCfgT;

typedef struct SeTrxSavePointName {
    uint32_t length;
    char name[SAVEPOINT_NAME_MAX_SIZE];
} SeTrxSavepointNameT;

typedef struct SavePoint {
    uint32_t savePointId;
    uint32_t reserve;
    uint64_t retainedUndoItemPtr;
    uint64_t normalUndoItemPtr;
    DbListT dfxItemList;  // SavePointDfxInfoT, 存储不同heap的dfx信息
    SeTrxSavepointNameT savePointName;
} SavePointT;

/* *
 * @brief SeRunCtx中打开一个readView
 * @param seRunCtx 存储运行上下文
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT_FOR_TS Status SeTransAssignReadView(SeRunCtxHdT seRunCtx);

/**
 * @brief 获取SeRunCtx中对应事务的事务状态
 * @param seRunCtx 存储运行上下文
 * @return TrxStateE 事务状态
 */
SO_EXPORT TrxStateE SeTransGetState(const struct TagSeRunCtxT *seRunCtx);

/**
 * @brief 获取SeRunCtx中对应事务的初始配置
 * @param seRunCtx 存储运行上下文
 * @param TrxCfgT 事务初始配置
 */
SO_EXPORT void SeTransGetCfg(SeRunCtxHdT seRunCtx, TrxCfgT *cfg);

/**
 * @brief 显式开启一个事务
 * @param seRunCtx 存储运行上下文
 * @param trxCfg 对事务的属性设置，包括隔离级别和只读模式
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT_FOR_TS Status SeTransBegin(SeRunCtxHdT seRunCtx, const TrxCfgT *cfg);

/**
 * @brief 事务处预留BuffPool资源（当前只有mini场景调用）
 * @param seRunCtx 存储运行上下文
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT Status SeReserveBuffPoolPages(SeRunCtxHdT seRunCtx);

/**
 * @brief 事务处释放BuffPool资源（当前只有mini场景调用）
 * @param seRunCtx 存储运行上下文
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT Status SeReleaseBuffPoolPages(SeRunCtxHdT seRunCtx);

/**
 * @brief 对事务进行回滚
 * @param seRunCtx 存储运行的上下文
 * @param isTimeout 是否是事务超时回滚场景
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT_FOR_TS Status SeTransRollback(SeRunCtxHdT seRunCtx, bool isTimeout);

/**
 * @brief 对事务进行提交
 * @param seRunCtx 存储运行的上下文
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT_FOR_TS Status SeTransCommit(SeRunCtxHdT seRunCtx);

/**
 * @brief 对事务进行恢复，保留内存场景使用，该接口只支持轻量化事务
 * @param seRunCtx 存储运行的上下文
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT Status SeTransRecovery(SeRunCtxHdT seRunCtx);

/**
 * @brief 对事务设置恢复状态，仅重启阶段事务可以调用
 * @param seRunCtx 存储运行的上下文
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT_FOR_TS void SeTransSetRecoveryFlag(SeRunCtxHdT seRunCtx);

/**
 * @brief 对乐观事务进行冲突检测
 * @param seRunCtx 存储运行的上下文
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT Status SeOptimisticTrxConflictCheck(const SeRunCtxHdT seRunCtx);

/**
 * @brief 对事务进行终止
 * @param seRunCtx 存储运行的上下文
 */
SO_EXPORT void SeTransAbort(SeRunCtxHdT seRunCtx);

/**
 * @brief 获取事务ID
 * @param seRunCtx 存储运行的上下文
 * @return TrxIdT 事务ID
 */
SO_EXPORT TrxIdT SeTransGetTrxId(const SeRunCtxHdT seRunCtx);

/**
 * @brief 判断事务是否提交
 * @param seRunCtx 存储运行的上下文
 * @param trxId 事务ID
 * @return bool 事务是否提交
 */
SO_EXPORT bool SeTransIsCommit(const SeRunCtxHdT seRunCtx, TrxIdT trxId);

/**
 * @brief 轻量化事务设置操作数(轻量化事务的批量操作前需要设置)
 * @param seRunCtx 存储运行的上下文
 * @param batchNum 批量操作的记录数
 */
SO_EXPORT void SeTransSetDmlHint4BatchNum(SeRunCtxHdT seRunCtx, uint32_t batchNum);

/**
 * @brief 轻量化事务设置操作数和步长(轻量化事务的批量操作前需要设置)
 * @param seRunCtx 存储运行的上下文
 * @param batchNum 批量操作的记录数
 */
SO_EXPORT void SeTransSetDmlHint4BatchExtendNum(SeRunCtxHdT seRunCtx, uint32_t batchNum);

/**
 * @brief 轻量化事务设置可扩展UndoLog的标记位，只用于范围更新的场景
 * @param seRunCtx 存储运行的上下文
 */
SO_EXPORT void SeTransSetDmlHint4RangeUpdate(SeRunCtxHdT seRunCtx);

/**
 * @brief 轻量化事务下，设置失败场景触发订阅的标记位
 * @param seRunCtx 存储运行的上下文
 */
SO_EXPORT void SeTransSetSendSubFlag(SeRunCtxHdT seRunCtx);

/**
 * @brief 轻量化事务下，获取 失败场景触发订阅的标记位
 * @param seRunCtx 存储运行的上下文
 * @return bool 失败场景触发订阅的标记位
 */
SO_EXPORT bool SeTransGetSendSubFlag(const SeRunCtxHdT seRunCtx);

/**
 * @brief 轻量化事务、保留内存下，设置rsmUndo的指针
 * @param seRunCtx 存储运行的上下文
 */
SO_EXPORT void SeTransSetRsmUndo(const SeRunCtxHdT seRunCtx, RsmUndoRecordT *rsmUndoRec);

/**
 * @brief 获取 事务的类型
 * @param seRunCtx 存储运行的上下文
 * @return TrxTypeE 事务的类型
 */
SO_EXPORT_FOR_TS TrxTypeE SeTransGetTrxType(const struct TagSeRunCtxT *seRunCtx);

/**
 * @brief 设置事务索引相关上下文，事务处理时使用
 * @param idxOpenCfg 入参，索引在当前执行的OpenCfg
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT Status SeSetTrxCtxForIndex(const TrxParamT *idxParam);

/**
 * @brief 获取事务的隔离级别
 * @param seRunCtx 存储运行的上下文
 * @return IsolationLevelE 事务的隔离级别
 */
SO_EXPORT_FOR_TS IsolationLevelE SeTransGetIsolationLevel(const struct TagSeRunCtxT *seRunCtx);

/**
 * @brief 设置ID为labelId的表 为已修改状态
 * @param seRunCtx 存储运行的上下文
 * @param labelId LabelId ID
 * @param type check的类型（edge、Kv、Vertex表）
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT_FOR_TS Status SeTransSetLabelModified(
    SeRunCtxHdT seRunCtx, uint32_t labelId, TrxCheckLabelReadViewTypeE type, bool isModify);

/**
 * @brief 设置ID为labelId的表 为已修改状态
 * @param seRunCtx 存储运行的上下文
 * @param targetSavePointId 回滚到的savePoint点的Id
 */
SO_EXPORT void SeTransRollBackLabelModified(SeRunCtxHdT seRunCtx, uint32_t targetSavePointId);

/**
 * @brief 检查是否已经设置ID为labelId的表 为已修改状态，如果没有则进行设置，设置过了就直接return
 * @param seRunCtx 存储运行的上下文
 * @param labelId LabelId ID
 * @param type check的类型（edge、Kv、Vertex表）
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT_FOR_TS Status SeTransCheckAndSetLabelModified(
    SeRunCtxHdT seRunCtx, uint32_t labelId, TrxCheckLabelReadViewTypeE type, bool isModify);

/**
 * @brief 设置trx在事务提交阶段进行冲突域检查
 * @param seRunCtx 存储运行的上下文
 */
SO_EXPORT_FOR_TS void SeTransSetLabelModifiedActive(SeRunCtxHdT seRunCtx);

/**
 * @brief 根据事务ID获取对应的连接ID
 * @param seRunCtx 存储运行的上下文
 * @param id 可能是事务ID或者cloneID
 * @param isClonedId 是否是cloneId
 * @param trxBaseInfo 出参, 包含一些事务的基础信息
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT Status SeTransGetTrxBaseInfoById(
    const SeRunCtxHdT seRunCtx, uint64_t id, bool isClonedId, TrxBaseInfoT *trxBaseInfo);

/* *
 * @brief 创建一个savepoint
 * @param seRunCtx   存储运行的上下文。
 * @param name   savepoint name。
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT Status SeTrxCreateSavepoint(SeRunCtxHdT seRunCtx, const SeTrxSavepointNameT *name);

/* *
 * @brief 回滚到一个savepoint
 * @param seRunCtx   存储运行的上下文。
 * @param name   savepoint name。
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT Status SeTrxRollbackToSavepoint(SeRunCtxHdT seRunCtx, const SeTrxSavepointNameT *name);

/* *
 * @brief 释放一个savepoint
 * @param seRunCtx   存储运行的上下文。
 * @param name   savepoint name。
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT Status SeTrxReleaseSavepoint(SeRunCtxHdT seRunCtx, const SeTrxSavepointNameT *name);

/**
 * @brief 在涉及事务的直连读开始前，进行状态检查以及状态转换
 * @param seRunCtx 存储运行的上下文
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT Status SeTransStartDirectRead(SeRunCtxHdT seRunCtx);

/**
 * @brief 在涉及事务的直连读结束后，进行状态检查以及状态转换
 * @param seRunCtx 存储运行的上下文
 */
SO_EXPORT void SeTransEndDirectRead(SeRunCtxHdT seRunCtx);

/**
 * @brief 仅V1使用,压缩结束后释放预留undopage,保证内存紧凑性;
 * @param seRunCtx : 入参, 存储运行的上下文
 * @param trx : 入参, trx资源指针
 * @return 成功(DB_SUCCESS)或者其他失败原因
 */
SO_EXPORT Status SeTrxReleaseRetainUndoPage(SeRunCtxHdT seRunCtx);

#ifdef EXPERIMENTAL_GUANGQI
/**
 * @brief 事务克隆，通过一个已有事务，克隆出一个子事务
 * @param seRunCtx : 入参, 当前存储运行的上下文
 * @param TrxCfgT : 入参，事务配置参数
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT Status SeTransClone(SeRunCtxHdT seRunCtx, const TrxCfgT *cfg, uint32_t parentCloneId);

/**
 * @brief 事务克隆，自调度消息请求使用，父事务线程调用，克隆出一个继承者事务，供连接继续使用
 * @param seRunCtx : 入参, 当前存储运行的上下文
 * @param TrxCfgT : 入参，事务配置参数
 * @param cloneId : 入参, 当前父事务的cloneId
 * @param initiateConnId : 入参，发起事务克隆的连接Id
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT Status SeTransCreateSuccessor(
    SeRunCtxHdT seRunCtx, const TrxCfgT *cfg, uint32_t cloneId, uint16_t initiateConnId);

/**
 * @brief 事务合并，子事务调用，把自身数据合并到父事务连接上
 * @param seRunCtx : 入参, 当前存储运行的上下文
 * @param selfConnId : 入参，子事务自身的连接Id
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT Status SeTransMerge(SeRunCtxHdT seRunCtx, uint16_t selfConnId);

/**
 * @brief 事务合并，自调度消息请求使用，父事务线程调用, 整合子事务的数据，生成一个组合事务，供连接继续使用
 * @param seRunCtx : 入参, 当前存储运行的上下文
 * @param TrxCfgT : 入参，事务配置参数
 * @param cloneTrxId : 入参, 子事务的事务ID
 * @param initiateConnId : 入参，子事务的连接Id
 * @return int32_t 当内部出现错误后，会返回一个错误码
 */
SO_EXPORT Status SeTransCreateCombineTrx(
    SeRunCtxHdT seRunCtx, const TrxCfgT *cfg, uint32_t cloneId, TrxIdT cloneTrxId, uint16_t initiateConnId);

/**
 * @brief 查询SeRunCtx中对应事务的是否是克隆出来的子事务
 * @param seRunCtx 存储运行上下文
 * @return bool
 */
SO_EXPORT bool SeTransIsCloneTrx(const struct TagSeRunCtxT *seRunCtx);

/**
 * @brief 查询SeRunCtx中对应事务的是否是克隆类型的事务（包括父子事务）
 * @param seRunCtx 存储运行上下文
 * @return bool
 */
SO_EXPORT bool SeTransIsCloneTypeTrx(const struct TagSeRunCtxT *seRunCtx);

/**
 * @brief 获取本事务的cloneId
 * @param seRunCtx 存储运行的上下文
 * @return uint32_t 事务cloneId
 */
SO_EXPORT uint32_t SeTransGetCloneId(const SeRunCtxHdT seRunCtx);

/**
 * @brief 获取父事务的cloneId
 * @param seRunCtx 存储运行的上下文
 * @return uint32_t 事务cloneId
 */
SO_EXPORT uint32_t SeTransGetParentCloneId(const SeRunCtxHdT seRunCtx);

/**
 * @brief 判断目标事务Id，是不是当前事务的最早的祖先事务
 * @param seRunCtx : 入参, 当前存储运行的上下文
 * @param targetTrxId : 入参，目标事务Id
 * @return bool 返回值
 */
SO_EXPORT bool SeTransIsTargetTrxOldestParent(const SeRunCtxHdT seRunCtx, TrxIdT targetTrxId);

typedef struct TrxMergeInfo {
    uint64_t trxId;
    uint32_t validateState;
} TrxMergeInfoT;

SO_EXPORT void *SeTransGetTrxMemCtxForTrxMerge(const SeRunCtxHdT seRunCtx);
SO_EXPORT void SeTransSetYangInfoForTrxMerge(const SeRunCtxHdT seRunCtx, TrxMergeInfoT *mergeInfo);
SO_EXPORT void SeTransSetLabelLatchInfoForTrxMerge(const SeRunCtxHdT seRunCtx, void *labelLatchInfo);
#endif

static inline bool SeTransStartDirectReadCheck(SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);
    return SeTransGetIsolationLevel(seRunCtx) == REPEATABLE_READ && SeTransGetState(seRunCtx) != TRX_STATE_NOT_STARTED;
}

SO_EXPORT Status SeInitTrxMgrCheckFunc(uint16_t instanceId,
    OptiTrxSetLabelLastTrxIdAndTrxCommitTime setFunc[(uint32_t)TRX_CHECK_READVIEW_NUM],
    OptiTrxGetLabelLastTrxIdAndTrxCommitTime getFunc[(uint32_t)TRX_CHECK_READVIEW_NUM],
    OptiTrxGetLabelName getLabelName[(uint32_t)TRX_CHECK_READVIEW_NUM]);

typedef struct LabelInfo {
    uint16_t instanceId;
    uint16_t labelType;
    uint32_t labelId;
    uint32_t latchId;
    uint32_t latchVersionId;
    void *labelLatch;
    void *label;
    ShmemPtrT containerShmAddr;
} LabelInfoT;

static inline LabelInfoT InitLabelInfoT(uint16_t instanceId, uint32_t labelId, uint16_t labelType)
{
    return (LabelInfoT){
        .instanceId = instanceId,
        .labelType = labelType,
        .labelId = labelId,
        .latchId = 0,
        .latchVersionId = 0,
        .labelLatch = NULL,
        .label = NULL,
        .containerShmAddr = DB_INVALID_SHMPTR,
    };
}

typedef Status (*TrxRecoveryGetLabelInfoFunc)(SeRunCtxHdT seRunCtx, LabelInfoT *labelInfo);
typedef Status (*TrxRecoveryReleaseLabelInfoFunc)(LabelInfoT *labelInfo);
void SeTrxModifyPubsubList(TrxT *trx);
void TrxRecoveryLabelInfoHandleRegister(
    TrxRecoveryGetLabelInfoFunc getLabelFunc, TrxRecoveryReleaseLabelInfoFunc releaseLabelFunc);

void TrxMgrFreeTrxForDirectWrite(DbSessionCtxT *sessionCtx, SeRunCtxHdT seRunCtx);

typedef struct SeSimpleLabelInfo {
    uint32_t labelsCnt;
    uint32_t *alterLabelsExt;
} SeSimpleLabelInfoT;

// 记录乐观事务相关的信息
typedef struct SeOptiTrxInfo {
    uint64_t trxId;                      // 事务ID，也是事务开始时刻的逻辑时钟
    uint64_t commitTs;                   // commit ID, 也是事务提交时刻的逻辑时钟
    char *peerProcInfo;                  // 记录该事务对应的对端信息，固定长度SE_CONN_INFO_MAX_LEN
    SeSimpleLabelInfoT vertexLabelInfo;  // 记录该事务所操作过的vertex表
    SeSimpleLabelInfoT kvLabelInfo;      // 记录该事务所操作过的kv表
    SeSimpleLabelInfoT edgeLabelInfo;    // 记录该事务所操作过的边表
    struct SeOptiTrxInfo *nextTrxInfo;   // 链表结构，指向一下个optiTrxInfo
} SeOptiTrxInfoT;

typedef struct SeOptiTrxInfoList {
    uint32_t listLen;
    SeOptiTrxInfoT *head;  // 链表中第一个optiTrxInfo
    SeOptiTrxInfoT *tail;  // 链表中最后一个optiTrxInfo
} SeOptiTrxInfoListT;

inline static void InitSeOptiLabelInfo(SeSimpleLabelInfoT *labelInfo)
{
    labelInfo->alterLabelsExt = NULL;
    labelInfo->labelsCnt = 0;
}

inline static void InitSeOptiTrxInfo(SeOptiTrxInfoT *optiTrxInfo)
{
    optiTrxInfo->trxId = 0;
    optiTrxInfo->commitTs = 0;
    optiTrxInfo->peerProcInfo = NULL;
    InitSeOptiLabelInfo(&optiTrxInfo->vertexLabelInfo);
    InitSeOptiLabelInfo(&optiTrxInfo->kvLabelInfo);
    InitSeOptiLabelInfo(&optiTrxInfo->edgeLabelInfo);
    optiTrxInfo->nextTrxInfo = NULL;
}

inline static void InitSeOptiTrxInfoList(SeOptiTrxInfoListT *optiTrxInfoList)
{
    optiTrxInfoList->listLen = 0;
    optiTrxInfoList->head = NULL;
    optiTrxInfoList->tail = NULL;
}

void TrxSetIsAllocNewFlag(SeRunCtxHdT seRunCtx, bool isAllocNew);

bool TrxGetIsAllocNewFlag(TrxT *trx);

void TrxGetMaxTrxNum(TrxT *trx, uint32_t *maxTrxNum);
void TrxGetUsedTrxNum(TrxT *trx, uint32_t *usedTrxNum);

StatusInter TrxUndoReleaseRetainPage(SeRunCtxHdT seRunCtx, TrxT *trx);

#ifdef __cplusplus
}
#endif

#endif  // SE_TRX_PUB_H
