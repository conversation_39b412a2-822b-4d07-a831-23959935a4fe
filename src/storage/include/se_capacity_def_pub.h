/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: 存储模块能力定义(pub), 外部模块能感知的能力定义, 外部可以调整的能力
 * Author: panghaisheng
 * Create: 2021-03-25
 */
#ifndef SE_CAPACITY_DEF_PUB_H
#define SE_CAPACITY_DEF_PUB_H

#include "adpt_define.h"
#include "db_dyn_load.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 默认配置定义 */
#define SE_DEFAULT_DEV_SIZE (4 * DB_KIBI)    // uint: Kb
#define SE_DEFAULT_PAGE_SIZE 32              // uint: Kb
#define SE_DEFAULT_EXTENT_SIZE 32            // uint: Kb
#define SE_DEFAULT_PAGE_MAX_SIZE 64          // uint: Kb
#define SE_DEFAULT_MAX_MEM (4096 * DB_KIBI)  // uint: Kb

#define SE_PERSIST_MIN_DEVICE_COUNT 4

#define SE_CONF_MAX_DEVICE_SIZE (1024 * DB_KIBI)  // uint: K
#define SE_CONF_MIN_DEVICE_SIZE DB_KIBI           // unit: K
#define SE_CONF_MAX_PAGE_SIZE 64                  // unit: K
#define SE_CONF_MIN_PAGE_SIZE 4                   // unit: K
#define PAGE_SIZE_MOD_NUM 4                       // uint: Kb. page的size对齐参数
#define HEAP_MAX_ROW_SIZE (31 * DB_KIBI)          // uint: b, heap支持最大行

/* 非功能性规格定义 */
#define FSM_LIST_COUNT 8

/* 扫描时指定从头开始搜索特殊值 */
#define BEFORE_FIRST_TUPLE_ADDR DB_MAX_UINT64
#define BEFORE_FIRST_TUPLE_BLOCK_ID 0

#define BEFORE_FIRST_TUPLE                                                            \
    (HpTupleCombineAddrT)                                                             \
    {                                                                                 \
        .tupleAddr = BEFORE_FIRST_TUPLE_ADDR, .blockId = BEFORE_FIRST_TUPLE_BLOCK_ID, \
        .pageDeviceId = DB_INVALID_UINT32, .pageBlockId = DB_INVALID_UINT32           \
    }

// 事务锁相关配置
#define SE_LOCK_POOL_CAL_COEFFICIENTS 3    // 锁池个数计算的系数
#define SE_LOCK_BUCKET_CAL_COEFFICIENTS 2  // 锁桶个数计算的系数
// 小型化:
#ifdef FEATURE_SIMPLEREL
#define SE_LOCK_SPECIFICATION_IOT 256  // V1嵌入式场景下默认连接数为256
#else
#define SE_LOCK_SPECIFICATION_IOT 100  // 默认连接数为100
#endif
#ifdef FEATURE_STREAM
// 流计算典配场景会有7张逻辑表，按照interval分成多个物理表（设置为1day，按照有限磁盘空间下最多存2周的数据）
// 即每张逻辑表对应14张物理表，7*14=98，按照锁桶+锁池为13+97能满足典配场景需求。后续考虑做成动态扩展逻辑
#define LOCK_POOL_PUBLIC_ENTRY_NUM_IOT 97
#else
#define LOCK_POOL_PUBLIC_ENTRY_NUM_IOT 282
#endif
#define LOCK_POOL_RESERVE_ENTRY_NUM_IOT 18

// 正常申请notify pool，但是先初始化一部分的信号量，其他信号量在运行时动态申请出来
// 所以也会存在申请不到信号量的情况
#define SE_LOCK_NOTIFY_INIT_NUM 138

// 正常:
#define SE_LOCK_SPECIFICATION_NORMAL MAX_CONN_NUM
#define LOCK_POOL_PUBLIC_ENTRY_NUM 2848
#define LOCK_POOL_RESERVE_ENTRY_NUM 224

#define LITTLE_LOB_SIZE (DB_MEBI + DB_KIBI)
#define NORMAL_LOB_SIZE (10 * (DB_MEBI + DB_KIBI))
#define SERIALIZE_LOB_SIZE DB_MEBI

#define HEAP_SCAN_CURSOR_OPENER_TOP_3 3

inline static uint32_t GetStorageLobMaxSize(uint32_t pageSize, bool isUseNormalLob, bool isTypeKv)
{
    if (pageSize >= SE_DEFAULT_PAGE_SIZE * DB_KIBI && isUseNormalLob) {
        // yang场景需要放开到10M，此时约束pageSize为32KB以上，存储层逻辑支持相关处理
        return NORMAL_LOB_SIZE;
    } else if (isTypeKv) {
        // 存储先限制 1Mb + 1kb; 修改历史: 之前是返回 DB_MEBI, 多出的 DB_KIBI 用于存放 KV 的 key、 keyLen 和 valueLen
        return LITTLE_LOB_SIZE;
    }
    // 非KV表最大支持1MB的序列化对象
    return SERIALIZE_LOB_SIZE;
}

#ifdef __cplusplus
}
#endif
#endif  // GMDBV5_SE_CAPACITY_DEF_PUB_H
