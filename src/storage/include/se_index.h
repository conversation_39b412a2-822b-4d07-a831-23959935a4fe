/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: se_index.h
 * Description: storage index access method public head file
 * Author: pengfengbin
 * Create: 2021-04-18
 */
#ifndef SE_INDEX_H
#define SE_INDEX_H

#include "db_resource_session_pub.h"
#include "dm_data_basic.h"
#include "dm_data_define.h"
#include "dm_data_prop_seri.h"
#include "dm_meta_index_label.h"
#include "se_common.h"
#include "se_define.h"
#include "se_heap.h"
#include "se_index_base.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct IndexRemovePara {
    bool isErase;
    bool isGc;
#ifdef FEATURE_SQL
    LpasPqAddrInfoT *pq;
#endif
} IndexRemoveParaT;

typedef void *IndexScanItrT;
typedef struct ArtMemMgr ArtMemMgrT;
typedef struct ArtMemMgrRunCtx ArtMemMgrRunCtxT;
typedef struct ArtTree ArtTreeT;

typedef union IdxTupleOrIter {
    HpTupleAddr addr;
    IndexScanItrT iter;
} IdxTupleOrIterT;

typedef enum IdxValueType {
    IDX_IS_NOT_FOUND = 0,
    IDX_IS_TUPLE_ADDR,  // 1对1场景
    IDX_IS_ITERATOR,    // 1对多场景，是迭代器
    IDX_MAX_VALUE_TYPE
} IdxValueTypeE;

typedef enum {
    SEC_INDEX_OP_DELETE,
    SEC_INDEX_OP_UPDATE,
    SEC_INDEX_OP_INSERT,
    SEC_INDEX_OP_BUTT,  // Invalid data type
} SecIdxOpTypeE;

typedef struct SecIdxPreload {
    SecIdxOpTypeE op;
    HpTupleAddr tupleAddr;
    IndexKeyT indexKey;
} SecIdxPreloadT;

typedef enum TTreeSeType {
    TTREE_FIX_ROW = 0,   // 定长行，普通表场景
    TTREE_VAR_ROW = 1,   // 变长行，公共空间场景
    TTREE_SUBS_ROW = 2,  // 分片行，大对象场景
    TTREE_INV_ROW
} TTreeSeTypeE;  // 兼容V1使用，标识索引类型

typedef Status (*IdxKeyCmpFunc)(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr, int32_t *cmpRet, bool *isMatch);
typedef Status (*IdxMultiVersionKeyCmpFunc)(
    IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr, KeyCmpResT *keyCmpRes);
typedef Status (*IdxKeyCmpBufFunc)(
    IndexCtxT *idxCtx, IndexKeyT idxKey, const HeapTupleBufT *hpBuf, int32_t *cmpRet, bool *isMatch);
typedef Status (*IdxAddrCheckAndFetchFunc)(
    IndexCtxT *idxCtx, const HpRunHdlT heapRunHdl, HpTupleAddr addr, bool *isExist);
typedef Status (*IdxAddrCheckFunc)(const Handle heapRunHdl, HpTupleAddr addr, bool *isExist);

typedef Status (*ChIdxKeyCmpFunc)(
    Handle hcRunCtx, IndexKeyT idxKey, const HeapTupleBufT *heapTupleBuf, int32_t *cmpRet, bool *isMatch);

typedef Status (*HashClusterIdxKeyCmpFunc)(
    Handle hcRunCtx, IndexKeyT idxKey, const HeapTupleBufT *heapTupleBuf, int32_t *cmpRet, bool *isMatch);

typedef HeapHacInfoT (*IdxGetHacInfoFunc)(const HpRunHdlT heapRunHdl);

typedef Status (*IdxExprEvalValuesFunc)(
    void *expr, DmVertexT *vertex, DmValueT *values, uint32_t *valNum, uint32_t *valBufSize);

typedef Status (*IdxSetPqIdFunc)(IndexCtxT *idxCtx, LpasPqAddrInfoT pqInfo);

typedef Status (*IdxCategorizeFunc)(
    IndexCtxT *idxCtx, IdxBatchLookupIterT *iter, IdxTupleOrIterT addrOrIter, uint32_t pos, IdxValueTypeE valueType);

typedef struct IdxBatchLookupPara {    // batchLookup 回调函数与执行层句柄
    IdxCategorizeFunc categorizeFunc;  // 依据isFound进行分类，replace或delete流程中
    IdxBatchLookupIterT *iter;         // batch lookup执行层句柄
} IdxBatchLookupParaT;

inline static IdxTupleOrIterT TupleAddr2IdxTupleOrIter(HpTupleAddr addr)
{
    return (IdxTupleOrIterT){.addr = addr};
}

typedef struct IdxCallBackFunc {
    IdxKeyCmpFunc keyCmp;
    IdxMultiVersionKeyCmpFunc multiVersionKeyCmp;
    IdxKeyCmpBufFunc hpBufCmp;
    IdxAddrCheckFunc addrCheck;
    IdxAddrCheckAndFetchFunc addrCheckAndFetch;
    IdxGetHacInfoFunc getHacInfo;
    IdxExprEvalValuesFunc exprValuesEval;
#ifdef FEATURE_SQL
    IdxSetPqIdFunc idxSetPqIdFunc;
#endif
} IdxCallBackFuncT;

typedef enum IndexRangeType {
    INDEX_RANGE_CLOSED,    /* [low, high] range scan */
    INDEX_RANGE_OPENLEFT,  /* (low, high] range scan */
    INDEX_RANGE_OPENRIGHT, /* [low, high) range scan */
    INDEX_RANGE_OPENBOTH,  /* (low, high) range scan */
    INDEX_RANGE_BUTT
} IndexRangeTypeE;

typedef enum IndexFilterResult {
    INDEX_FILTER_UNSATISFIED, /* pass the filter unsuccessfully */
    INDEX_FILTER_SATISFIED,   /* successful to pass the filter */
} IndexFilterResultE;

typedef enum IndexScanDirection {
    INDEX_SCAN_ASCEND,  /* ascend scan */
    INDEX_SCAN_DESCEND, /* descend scan */
    INDEX_SCAN_BUTT
} IndexScanDirectionE;

typedef enum IndexMultiVersionType {
    INDEX_ONE_VERSION_TYPE = 0,      // 对应 PCC, RU
    INDEX_MARK_DELETE_VERSION_TYPE,  // 对应 PCC, RC
    INDEX_PCC_RR_TYPE,
    INDEX_MULTI_VERSION_TYPE,  // 对应 OCC
    INDEX_PCC_RC_TYPE,
} IndexMultiVersionTypeE;

typedef struct VecScanPara {  // 向量查询入参
    uint16_t dim;
    uint32_t probe;  // 向量查询list区域数量
    uint32_t topN;   // 向量查询前N个最接近向量
    DbDataTypeE type;
    void *vector;
    double distanceUpperBound;  // distance upper bound
    uint64_t *dataIdList;       // filtered data ids
    uint32_t dataIdListSize;    // size of filtered data ids
    uint32_t scanOffset;        // 扩展查询的偏移
    uint64_t totalNum;          // 索引中的数据量
    DataIdAndPqAddrT *data;     // 前过滤得到的pq形式
    uint32_t dataCount;         // 前过滤得到的数据总数
    bool isVecExtendSearch;
} VecScanParaT;

typedef struct IndexScanCfg {
    IndexRangeTypeE scanType;       /* index scan type */
    IndexScanDirectionE scanDirect; /* index scan direction */
    IndexKeyT *leftKey;             /* the left bound key of the scan */
    IndexKeyT *rightKey;            /* the right bound key of the scan */
    uint8_t scanMode;               /* the mode of scan op */
    uint32_t scanLmit;
    void *scanPara;
    uint64_t nullDisableBitMap; /* the null disable bitmap of the index prope for scan */
                                /* 0x5 means prop_0 and prop_2 is null disabled */
                                /* the bits is up to DM_MAX_KEY_PROPE_NUM */
    MatchTypeE matchType;
    uint8_t condIdx;       /**前缀匹配的索引项 */
    uint8_t matchBytesNum; /**前缀匹配多少个字节 */
    uint8_t nearAIdx;      /**临近查询的A字段*/
    uint8_t nearBIdx;      /**临近查询的B字段，查询离A最近且B字段相同的数据*/
} IndexScanCfgT;

typedef struct IndexOpenCfg {
    SeRunCtxHdT seRunCtx;     // for iterator alloc
    void *vertex;             // 作为Key比较函数里面的vertex
    HpRunHdlT heapHandle;     // 索引调用heap获取heapTuple和key buffer
    ChLabelRunHdlT chHandle;  // 索引调用clustered hash获取tuple和key buffer

    // 1. 使用 union 封装
    // 2. DmIndexLabelBase 增加标记区分 kv 和 vl 类型
    // 用于排序索引进行key大小的比较
    union {
        DmIndexLabelBaseT *indexLabel;  // 基类
        DmVlIndexLabelT *vlIndexLabel;  // vertex
        DmKvIndexLabelT *kvIndexLabel;  // kv
    };
    IdxCallBackFuncT callbackFunc;
    void *userData;
    void *keyInfo;
    void *vertexLabel;  // 适配catalog放共享内存
    bool checkAged;
    bool needCheckIndexSatisfied;
    bool openDirectWrite;
    uint8_t reserve;
    DmIndexTypeE indexType;
} IndexOpenCfgT;  // 对外结构体，open 排序索引时初始化handle所用

typedef struct IndexMetaCfg {
    uint32_t indexId;
    DmIndexTypeE idxType;
    DmIndexTypeE realIdxType;
    DmIndexConstraintE idxConstraint;
    IndexMultiVersionTypeE indexMultiVersionType;
    // 索引 应使用固定的seInstanceId;
    uint32_t indexCap : 31;  // 索引初始容量，主要用于配置hash索引初始化配置
    // 排序索引需包含一些属性的配置信息 property, 需要落盘的信息
    uint32_t isLabelLatchMode : 1;  // 是否是大表锁模式，大表锁模式不用加page latch
    uint32_t tableSpaceId;
    uint32_t tableSpaceIndex;
    uint8_t nullInfoBytes;  // keyBuf中位图的字节长度，主要供lpm索引使用
    bool isUseClusteredHash;
    bool hasVarchar;
    bool isHcGlobalLatch;  // hashcluster是否已使用全局锁
    bool isMemFirst;       // 是否使用内存优先模式，当前该成员仅对排序索引生效
    bool isVertexUseRsm;
#ifdef FEATURE_SIMPLEREL
    uint8_t idxSeType;  // v1存储索引类型 TTreeSeTypeE
#endif
    DbLatchT *hcLatch;
    uint32_t keyDataType;
    void *extendParam;  // 根据索引类型确定对应的参数
} IndexMetaCfgT;

typedef Handle IndexRunCtxT;
struct IdxBase;
typedef struct IdxBase IdxBaseT;

typedef struct HcDeletePrefetchCtx {
    bool isPrefetch;
    uint8_t filterResult;
    uint32_t bucketIndex;
    HpTupleAddr oldHeadTupleAddr;
    HpTupleAddr delTupleAddr;
    HpTupleAddr prevTupleAddr;
    HpTupleAddr nextTupleAddr;
    void *oldHeadHcPtr;
    void *delHcPtr;
    void *prevHcPtr;  // 需要维护它的链表后项
    void *nextHcPtr;  // 需要维护本记录链表前项
} HcDeletePrefetchCtxT;

typedef struct HcInsertPrefetchCtx {
    bool isPrefetch;
    uint8_t filterResult;
    uint32_t bucketIndex;
    HpTupleAddr insertTupleAddr;
    void *insertHcPtr;  // 需要维护本记录链表后项
} HcInsertPrefetchCtxT;

typedef struct HcPrefetchCtx {
    HcDeletePrefetchCtxT hcDeletePrefetchCtx;
    HcInsertPrefetchCtxT hcInsertPrefetchCtx;
} HcPrefetchCtxT;

struct IndexCtx {              // 开放的执行态的上下文信息
    IndexOpenCfgT idxOpenCfg;  // open使用的runcontext
    IndexMetaCfgT idxMetaCfg;  // 索引元数据，包含indexId等
    IdxBaseT *idxHandle;       // 特定索引的元数据 HashTableT, NonUniqueIndexT etc.
                               // metadatahandle 需要拷贝存储持久化IndexMetaCfgT内的元信息
    TupleBufT tupleBuf;
    uint64_t trxId;
    TupleAddr oldRowId;  // yang 场景中会对同一事务中主键相同的的插入\删除要求放回第一条数据的RowId,乐观事务索引是多版本
    ShmemPtrT idxShmAddr;
    IndexRunCtxT *idxRunCtx;
#ifdef FEATURE_SIMPLEREL
    bool isKeyChange;
    uint32_t ttreeUpdateNodeId;
    uint16_t rowCntTTree;      // 兼容v1,定长表页内行数量
    uint16_t oneRowSizeTTree;  // 兼容v1,定长表数据大小
#endif
    IndexKeyT idxKey;
    uint32_t destNode;  // TTREE查询返回插入位置,todo 多个唯一索引场景暂未处理
    bool isAcquireLockByTryOnce;
    bool constructedWhenOpen;
    bool batchLocked;  // 批量操作上锁判断，避免重复加解锁
    bool batchReadLock;
    bool batchWriteLock;
    bool isMemFirst;  // 是否使用内存优先模式，创建索引时确定
    uint8_t idxAllocType;  // 该idxCtx申请空间时使用的indextype，如果使用了不一致的indexType进行IdxOpen将报错
    bool isBatch;
    bool isInsert;
    bool hasOpened;  // 该idxCtx是否执行idxOpen成功
#ifdef ART_CONTAINER
    bool isUpdate;
#endif
    bool isLookupOldRecord;  // 仅用于 YANG diff 场景，标志通过主键查找旧 NPA 表数据
    bool scanDirChanged;
#ifdef FEATURE_HAC
    bool hasLookup;        // 插入前是否lookup过
    bool isKeyCmpByHac;    // 是否使用硬化的keycmp
    bool isHacBatchAsync;  // 是否使用异步通信模式
#endif
    void *kvBuffer;  // 仅用于 mini kv 场景, cursor中的缓存kv hpTupleBuf指针，用于move场景重新定位
#ifdef FEATURE_GQL
    bool isBatchReplaceOrDelete;  // 当前索引runCtx是否是batch replace 或者 batch delete场景
    HeapTupleBufT preAllocHpTupleBuf;  // 此块内存默认为空，非空时为hpTupleBuf预分配内存，减少申请内存的次数
    uint32_t preAllocHpTupleBufOffset;  // 记录预分配内存的偏移量
#endif
    DataInfoT ttreeDataInfo;  // TTree索引场景，需要提供节点物理addr和标记信息
    HcPrefetchCtxT hcPrefetchCtx;  // 更新、删除操作提前缓存，解决直连写arm32虚拟内存不足无法映射时，数据完整性问题
    uint32_t ttreePos;  // 记录更新删除TTree位置信息
#ifdef FEATURE_SQL
    uint64_t indexAddrInfo;
    void *autoIdIndexCtx;      // 拓展的自增ID索引上下文
    uint32_t extendedKeySize;  // 在索引上的key后方拓展的内容长度
    uint32_t posOfPqAddr;      // 检索时需要取的PQ相对位置
    uint64_t autoId;           // 检索时返回给上层的自增ID，用于二次检索PQ值
    LpasPqAddrInfoT *pqInfo;   // 二次检索得到的PQ值
    void *pqIndexPlanState;    // 用于pq索引算法排序和scan的执行计划
    bool isSinglePqIndex;  // 如果是单PQ索引，直接将pq信息存在标量索引的拓展key后，能大幅提高检索性能
#endif
    uint8_t idxSeType;  // 兼容v1使用，标识为（定长 | 普通变长 | 大对象行）的索引 TTreeTypeE
    uint64_t rollptrUpdOrDel;  // 用于TTree插入场景,迁移后的undo链rollptr
    uint8_t *bigObjBuf;        // TTree 大对象数据中间缓存，用于 TTreeSearchDataInfo
    void *vertexIdBuffer;      // 用于Insert时返回VertexId, 或传入VertexId查找Vector
};

typedef struct IndexUpdateInfo {
    IndexKeyT oldIdxKey;
    IndexKeyT newIdxKey;
    HpTupleAddr oldAddr;
    HpTupleAddr newAddr;
} IndexUpdateInfoT;

#ifdef FEATURE_VLIVF
// 单个簇的统计信息
typedef struct VlIvfClusStatistics {
    char disToCentNumStr[DM_MAX_DISTANCE_RANGE_NUM][DM_MAX_DISTANCE_RANGE_STR_LEN];  // 该簇下向量到簇中心的距离分布
    char path[DM_MAX_CLUSTER_PATH_LENGTH];                                           // 该簇的路径
    uint64_t scanNum;                                                                // 该簇累计被查询的次数
    uint32_t pageNum;                                                                // 该簇使用的页面数量
    uint32_t vectorNum;                                                              // 该簇关联的向量数量
    uint8_t level;                                                                   // 该簇的层数
    float clusterRadius;                                                             // 该簇的半径
    bool isOod;                                                                      // 该簇是否为OOD
} VlIvfClusterStatisticsT;

// VLIVF索引的统计信息
typedef struct VlIvfIndexStatistics {
    uint64_t totalMemorySize;          // 实际申请的page总大小=totalMemorySize = pageSize * pageCount;
    uint64_t usedMemSize;              // 实际使用的内存空间
    uint32_t pageCount;                // 申请的page总个数
    uint32_t clusterSize;              // 簇的数量
    VlIvfClusterStatisticsT *cluster;  // 簇的统计信息
} VlIvfIndexStatisticsT;
#endif

typedef union IndexStatistics {
    struct {
        uint32_t entryCapacity;  // dir 的capacity
        uint32_t entryUsed;      // entry 的使用个数
        uint32_t segmentCnt;     // segment 的个数
        uint32_t mvPageCnt;      // 使用的多版本页个数，非多版本页场景下为0
        uint32_t scaleInCnt;     // 主键索引成功缩容的次数
        uint32_t cacheLineLen;   // 最大冲突链长度（线性探测长度）
        IndexHashPerfStatT indexHashPerfStat;
    } hashIndex;
    struct {
        uint32_t bucketCnt;     // bucket 个数
        uint32_t bucketSize;    // 每个bucket 大小
        uint32_t nodeCnt;       // 链表节点个数
        uint32_t groupCnt;      // 聚簇个数
        uint32_t maxGroupLen;   // 最长聚簇链长度
        uint32_t averGroupLen;  // 聚簇链平均长度
        IndexHashPerfStatT indexHashPerfStat;
    } hashLinklistIndex;
    struct {
        uint32_t bucketCount;  // bucket总数
        uint32_t bucketUsed;   // 已使用的bucket数量
        uint64_t minListLen;   // 最短聚簇链长度
        uint64_t maxListLen;   // 最长聚簇链长度
        uint64_t averListLen;  // 聚簇链平均长度
        IndexHashPerfStatT indexHashPerfStat;
    } hcIndex;
#ifdef FEATURE_HAC
    struct {
        uint32_t bucketCnt;     // 一级索引一共分配的桶数量
        uint32_t bucketUsed;    // 一级索引已使用的桶数量
        uint64_t minTableSize;  // 二级表最小内存占用
        uint64_t maxTableSize;  // 二级表最大内存占用
        uint64_t avgTableSize;  // 二级表平均内存占用
        uint32_t cacheLineLen;  // 最大冲突链长度（线性探测长度）
        IndexHashPerfStatT indexHashPerfStat;
    } multiHashIndex;
#endif
    struct {
        char indexType[DM_MAX_NAME_LENGTH];
        uint64_t insertDataCount;
        uint32_t nodeSizes[ART_NODE_TYPE_NUM];
        ArtMemPerfStatT artMemPerfStat;
        bool isHashcluster;             // 临时变量，给gmsysview做标注，把HC完全记忆抹除之后将移除
        uint32_t insertKeyCallbackCnt;  // ART插入时调用keycmp回调函数的次数
        uint64_t lpmArrCnt;             // count of lpm address array
    } artIndex;
#ifdef FEATURE_VLIVF
    VlIvfIndexStatisticsT vlivfIndex;  // vlivf索引信息
#endif
    BTreeIndexStatisticsT bTreeIndex;
    struct {
        uint32_t pkCachelineLen;  // 主键索引最大冲突链长度，依据当前实现，固定设置为线性探测长度
        uint32_t localHashCachelineLen;  // localHash最大冲突链长度（二级索引，hash唯一与非唯一）
        uint32_t pkAvgCollisionRate;     // 主键索引全局平均冲突率
        uint32_t localHashAvgCollisionRate;  // localHash索引全局平均冲突率（二级索引，hash唯一与非唯一）
    } indexGlobal;
} IndexStatisticsT;  // 索引的统计信息

/**
 * @brief 判断索引是否支持TableLoad
 * @param indexType : 索引类型
 * @return 成功或者失败
 */
SO_EXPORT_FOR_TS bool SeIndexIsSupportTableLoad(DmIndexTypeE indexType);

/**
 * @brief 创建索引接口，调用指定的索引类型做一些初始化的工作
 * @param idxMetaCfg : 入参, 描述索引的属性
 * @param idxShmAddr: 出参, 需要调用者保管
 * @return 成功或者其他失败原因
 */
Status IdxCreate(SeRunCtxHdT seRunCtx, IndexMetaCfgT idxMetaCfg, ShmemPtrT *idxShmAddr);

/**
 * @brief 清空索引接口
 * @param indexType : 入参, 索引类型
 * @param idxShmAddr : 入参, 需要删除的索引的共享内存ptr
 */
SO_EXPORT_FOR_TS Status IdxDrop(SeRunCtxHdT seRunCtx, uint8_t indexType, ShmemPtrT idxShmAddr);

void IdxCommitDrop(SeRunCtxHdT seRunCtx, uint8_t indexType, ShmemPtrT idxShmAddr);

/**
 * @brief 截断索引表；清除数据，保留元数据和原来的内存addr
 * @param indexType : 入参, 索引类型
 * @param idxShmAddr : 索引表的共享内存ptr
 */
Status IdxTruncate(SeRunCtxHdT seRunCtx, uint8_t indexType, ShmemPtrT idxShmAddr);

void IdxCommitTruncate(SeRunCtxHdT seRunCtx, uint8_t indexType, ShmemPtrT idxShmAddr);

/**
 * @brief 申请索引上下文
 * @param idxShmAddr : 入参, SeOpen后拿到的存储引擎上下文，用来申请内存
 * @param idxCtx : 出参，索引在当前执行的上下文句柄，其中：
 * @return 成功或者其他失败原因
 */
Status IdxAlloc(SeRunCtxHdT seRunCtxPtr, DmIndexTypeE indexType, IndexCtxT **idxCtx);

/**
 * @brief 打开 索引: 初始化 idxCtx(attach共享内存等)
 * @param idxShmAddr : 入参, 索引的共享内存ptr
 * @param idxCfg : 用户设置的打开索引的参数
 * @param idxCtx : 出参，索引在当前执行的上下文句柄，其中：
            索引Cmp 为调用者传入的回调函数，LookUp后判断 key 是否一致，一致返回1，不一致返回0
            heapHandle 为调用者传入的 heap 句柄，
            vertexLabel 为调用者传入的 DmVertexLabelT，
 * @return 成功或者其他失败原因
 */
Status IdxOpen(ShmemPtrT idxShmAddr, const IndexOpenCfgT *idxCfg, IndexCtxT *idxCtx);

/**
 * @brief 关闭 索引：对索引上下文字段赋值
 * @param idxCtx : 入参，索引在当前执行的上下文句柄
 */
void IdxClose(IndexCtxT *idxCtx);

/**
 * @brief 释放 索引 动态申请的资源
 * @param idxCtx : 入参，索引在当前执行的上下文句柄
 */
void IdxRelease(IndexCtxT *idxCtx);

/**
 * @brief 从 索引 中 读取 key 对应的数据
 * @param idxCtx : 入参，索引在当前执行的上下文句柄
 * @param idxKey : 入参 keyData keyLen
 * @param addr : 出参
 * @param isfound : 出差，读取结果，是否取得key对应的索引表项
 * @return 成功或者其他失败原因
 */
Status IdxLookup(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr *addr, bool *isFound);

/**
 * @brief 从索引中批量读取key对应的数据
 * @param idxCtx : 入参，索引在当前执行的上下文句柄
 * @param idxKey[] : 入参 keyData keyLen 数组
 * @param batchNum : 入参, 一批插入索引的数量
 * @param para : 入参，批量查询分类
                categorizeFunc：分类函数，replace或delete流程中
                iter：执行器关于分类句柄
 * @return 成功或者其他失败原因
 */
Status IdxBatchLookup(IndexCtxT *idxCtx, IndexKeyT idxKey[], uint32_t batchNum, IdxBatchLookupParaT *para);

/**
 * @brief 把 buf 指向的内容插入 索引
 * @param idxCtx : 入参，索引在当前执行的上下文句柄
 * @param idxKey : 入参 keyData keyLen
 * @param addr : 入参
 * @return 成功或者其他失败原因
 */
Status IdxInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr);

/**
 * @brief 批量的把 buf 指向的内容插入 索引
 * @param idxCtx : 入参，索引在当前执行的上下文句柄
 * @param idxKey[] : 入参 keyData keyLen 数组
 * @param addr[] : 入参
 * @param batchNum : 入参, 一批插入索引的数量
 * @return 成功或者其他失败原因
 */
Status IdxBatchInsert(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum);

/**
 * @brief 把 keyData  从索引中删除
 * @param idxCtx : 入参，索引在当前执行的上下文句柄
 * @param idxKey : 入参 keyData keyLen
 * @param addr : 入参
          unique==true   判断addr是否匹配然后删除
          unique==false  看addr删一个
                         addr是HEAP_INVALID_ADDR 批量删
 * @param removePara: 入参 删除的类型
          isErase : 入参，是否真正删除
          isGc : 入参，是否做垃圾回收
 * @return 成功或者其他失败原因
 */
Status IdxDelete(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr, IndexRemoveParaT removePara);

/**
 * @brief 批量 把 keyData  从索引中删除
 * @param idxCtx : 入参，索引在当前执行的上下文句柄
 * @param idxKey[] : 入参 keyData keyLen 数组
 * @param addr : 入参
          unique==true   判断addr是否匹配然后删除
          unique==false  看addr删一个
                         addr是HEAP_INVALID_ADDR 批量删
 * @param batchNum : 入参, 一批删除索引的数量
 * @param removePara: 入参 删除的类型
          isErase : 入参，是否真正删除
          isGc : 入参，是否做垃圾回收
 * @return 成功或者其他失败原因
 */
Status IdxBatchDelete(
    IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum, IndexRemoveParaT removePara);

Status IdxUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara);

/**
 * @brief 索引批量更新
 * @param idxCtx: 入参，索引在当前执行的上下文句柄
 * @param updateInfo: 批量更新的数据，包括oldKey,oldAddr,newKey,newAddr等（需要保证updateInfo数据和实际更新顺序相同！）
 * @param batchNum: 批处理个数
 * @param removePara: 删除操作的标志位
 * @return 成功或者其他失败原因
 */
Status IdxBatchUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo[], uint32_t batchNum, IndexRemoveParaT removePara);

/**
 * @brief 索引异步批量操作的剩余逻辑处理，当前必须在IdxBatchInsert/IdxBatchDelete之后调用
 * @param idxCtx: 入参，索引在当前执行的上下文句柄
 * @return 成功或者其他失败原因
 */
Status IdxEndBatchModify(IndexCtxT *idxCtx);

/**
 * @brief 开始对范围索引表扫描，做参数初始化
 * @param IndexCtxT: 入参，索引在当前执行的上下文句柄
 * @param IndexScanCfgT: 开始范围索引的配置，包括高低值，开闭区间和扫描顺序
 * @param iter: 出参，指向当前节点的扫描游标
 * @return 成功或者其他失败原因
 */
Status IdxBeginScan(IndexCtxT *idxCtx, IndexScanCfgT scanCfg, IndexScanItrT *iter);

/**
 * @brief 利用 IdxBeginScan 创建的迭代器， 对范围索引表扫描
 * @param IndexCtxT: 入参，索引在当前执行的上下文句柄
 * @param iter: 出参，指向当前节点的扫描游标
 * @param addr: 出参
 * @return 成功或者其他失败原因
 */
Status IdxScan(IndexCtxT *idxCtx, IndexScanItrT iter, HpTupleAddr *addr, bool *isFound);

/**
 * @brief Load指定表加载
 * @param[in] idxCtx        index runtime context
 */
SO_EXPORT_FOR_TS Status IdxTableLoad(IndexCtxT *idxCtx);

/**
 * @brief 预加载索引
 * @param[in] idxCtx        index runtime context
 * @param[in] userData
 */
Status IdxPreload(IndexCtxT *idxCtx, void *userData);

/**
 * @brief 结束当前对排序索引表的扫描，并释放游标空间
 * @param IndexCtxT: 入参，索引在当前执行的上下文句柄
 * @param iter: 入参，指向当前节点的扫描游标
 */
void IdxEndScan(IndexCtxT *idxCtx, IndexScanItrT iter);

/**
 * @brief 外部加载索引
 * @param idxCtx : 入参，索引在当前执行的上下文句柄
 * @param idxKey : 入参 keyData keyLen
 * @param addr : 入参
 * @return 成功或者其他失败原因
 */
Status IdxLoad(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr);

/**
 * @brief Set the index scanning direction.
 * @param idxCtx: input，context handle of the current execution of the index
 * @param iter: output, scan iter,pointing to the current node
 * @param scanDirection: input，scanning direction
 * @return Success or other failure causes
 */
Status IdxScanItrSetDirection(IndexCtxT *idxCtx, IndexScanItrT iter, IndexScanDirectionE scanDirection);

/**
 * @brief 把idxKey指向的内容插入索引的操作回滚
 * @param idxCtx : 入参，索引在当前执行的上下文句柄
 * @param idxKey : 入参 keyData keyLen
 * @param addr : 入参
 * @return 成功或者其他失败原因
 */
Status IdxUndoInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr);

/**
 * @brief 把idxKey指向的内容的删除索引操作回滚
 * @param idxCtx : 入参，索引在当前执行的上下文句柄
 * @param idxKey : 入参 keyData keyLen
 * @param addr : 入参
 * @return 成功或者其他失败原因
 */
Status IdxUndoRemove(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr);

Status IdxUndoUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara);

Status IdxFetchAddr(IndexCtxT *idxCtx, uint8_t *idxHead, uint32_t updateSize, IndexUpdateInfoT updateInfo[]);

/**
 * @brief 获取idxKey的索引项的数目（唯一索引固定为1， 非唯一索引获取数目）
 * @param idxCtx : 入参，索引在当前执行的上下文句柄
 * @param idxKey : 入参 keyData keyLen
 * @return 成功或者其他失败原因
 */
Status IdxGetKeyCount(IndexCtxT *idxCtx, IndexKeyT idxKey, uint64_t *count);

/**
 * @brief 获取索引的统计信息
 * @param indexType : 入参, 索引类型
 * @param idxShmAddr : 入参， 索引表的共享内存ptr
 * @param idxStatics : 出参， 索引的统计信息
 * @return 成功或者其他失败原因
 */
Status IdxStatView(DmIndexTypeE indexType, ShmemPtrT idxShmAddr, uint16_t instanceId, IndexStatisticsT *idxStat);

/**
 * @brief 获取索引的page size
 * @param indexType : 入参, 索引类型
 * @param idxShmAddr : 入参， 索引表的共享内存ptr
 * @param idxPageSize : 出参， 索引的page size
 * @return 成功或者其他失败原因
 */
Status IdxViewGetPageSize(uint8_t indexType, ShmemPtrT idxShmAddr, uint16_t instanceId, uint64_t *idxPageSize);

typedef uint64_t (*IndexScaleInGetCurTime)(void);
typedef struct IndexScaleInCfg {
    double splitTime;    // 入参，长任务分片时间
    uint64_t startTime;  // 入参，分片开始的时间
    bool isOverTime;     // 出参，运行是否超过时间片
    uint8_t reserve;
    uint16_t reserve1;
    uint32_t reserve2;
} IndexScaleInCfgT;

/**
 * @brief 索引缩容（支持 ART_INDEX_LOCAL 和 ART_INDEX_HASHCLUSTER)
 * @param idxCtx : 入参，索引在当前执行的上下文句柄
 * @param idxScaleCfg : 索引缩容所需配置
 * @return 成功或者其他失败原因
 */

Status IdxScaleIn(IndexCtxT *idxCtx, IndexScaleInCfgT *idxScaleCfg);

/**
 * @brief 获取记录数为 count 时索引的预估内存
 * @param idxMetaCfg : 入参, 描述索引的属性
 * @param count : 入参 记录数
 * @param keyLen : 入参 key长度
 * @param size : 出参 预估size
 * @return 成功或者其他失败原因
 */
Status IdxGetEstimateMemSize(
    uint16_t instanceId, IndexMetaCfgT idxMetaCfg, uint64_t count, uint32_t keyLen, uint64_t *size);

void IdxSetIsLookupOldRecord(IndexCtxT *idxCtx, bool isLookupOldRecord);

SO_EXPORT_FOR_HAC IndexScanItrT IdxGetIterator(IndexCtxT *idxCtx);

static inline void SerializeLpasPqAddrInfo(
    IndexCtxT *idxCtx, LpasPqAddrInfoT *inSrcPqinfo, LpasPqAddrInfoOnIndexT *outSerPqInfo)
{
    outSerPqInfo->serPageId = SerializePageId((PageMgrT *)idxCtx->idxOpenCfg.seRunCtx->pageMgr, inSrcPqinfo->slotId);
    outSerPqInfo->offset = inSrcPqinfo->offset;
}

static inline void DeserializeLpasPqAddrInfo(
    IndexCtxT *idxCtx, LpasPqAddrInfoOnIndexT *inSerPqinfo, LpasPqAddrInfoT *outPqInfo)
{
    if (outPqInfo == NULL) {
        return;
    }
    outPqInfo->offset = inSerPqinfo->offset;
    outPqInfo->slotId = DeserializePageId((PageMgrT *)idxCtx->idxOpenCfg.seRunCtx->pageMgr, inSerPqinfo->serPageId);
}

static inline void IdxAutoIdKeyInit(IndexKeyT *key, uint64_t id, uint8_t *valueBuf, uint32_t valueSize)
{
    if (valueSize != AUTO_ID_SIZE + AUTO_ID_NULL_BYTES_SIZE) {
        // autoId前面有一个字节用于记录nullInfoBytes
        return;
    }
    // keyData内容格式为 nullInfoBytes + id ，其中nullInfoBytes在自增ID场景下固定为1，所以这里简单构造即可
    key->keyData = valueBuf;
    key->keyData[0] = AUTO_ID_NULL_BYTES_VALUE;
    *(uint64_t *)(void *)(key->keyData + AUTO_ID_NULL_BYTES_SIZE) = id;
    key->keyLen = valueSize;
    key->prefixPropeNum = AUTO_ID_PREFIX_NUM;
}

/**
 * @brief 在key后方拓展的内容上写入数据
 * @param idxCtx : 入参，索引在当前执行的上下文句柄
 * @param idxKey : 入参 keyData keyLen
 * @param offset : 入参 要在extend区域写入的位置
 * @param value : 入参 要写入的内容
 * @param size : 入参 写入内容大小
 * @return 成功或者其他失败原因
 */
Status IdxSetExtendKey(IndexCtxT *idxCtx, IndexKeyT idxKey, uint32_t offset, uint8_t *value, uint32_t len);

inline static IndexMultiVersionTypeE IdxGetMutiVersionType(IsolationLevelE isolationLevel, TrxTypeE trxType)
{
    if (isolationLevel == REPEATABLE_READ && trxType == OPTIMISTIC_TRX) {
        return INDEX_MULTI_VERSION_TYPE;
    } else if (isolationLevel == REPEATABLE_READ && trxType == PESSIMISTIC_TRX) {
        return INDEX_PCC_RR_TYPE;
    } else if (isolationLevel == READ_COMMITTED) {
        return INDEX_PCC_RC_TYPE;
    } else {
        return INDEX_ONE_VERSION_TYPE;
    }
}

typedef enum {
    LIST_LOCALHASH_ID_MIN = 0,
    LIST_LOCALHASH_ID_CCEH = LIST_LOCALHASH_ID_MIN,
    LIST_LOCALHASH_ID_CHAINED,
    LIST_LOCALHASH_ID_HASH_LINKLIST,
    LIST_LOCALHASH_ID__MAX
} LIST_LOCALHASH_ID_E;

static inline uint32_t IdxGetListLocalhashId(DmIndexTypeE type, bool unique)
{
    if (!unique) {
        return (uint32_t)LIST_LOCALHASH_ID_HASH_LINKLIST;
    } else if (type == CHAINED_HASH_INDEX) {
        return (uint32_t)LIST_LOCALHASH_ID_CHAINED;
    } else {
        return (uint32_t)LIST_LOCALHASH_ID_CCEH;
    }
}
ALWAYS_INLINE static void HcInitHcDeletePrefetchCtx(HcDeletePrefetchCtxT *delPrefetchCtx)
{
    DB_POINTER(delPrefetchCtx);
    delPrefetchCtx->isPrefetch = false;
    delPrefetchCtx->filterResult = (uint8_t)INDEX_FILTER_SATISFIED;
    delPrefetchCtx->oldHeadTupleAddr = HEAP_INVALID_ADDR;
    delPrefetchCtx->delTupleAddr = HEAP_INVALID_ADDR;
    delPrefetchCtx->prevTupleAddr = HEAP_INVALID_ADDR;
    delPrefetchCtx->nextTupleAddr = HEAP_INVALID_ADDR;
    delPrefetchCtx->oldHeadHcPtr = NULL;
    delPrefetchCtx->delHcPtr = NULL;
    delPrefetchCtx->prevHcPtr = NULL;
    delPrefetchCtx->nextHcPtr = NULL;
}

ALWAYS_INLINE static void HcInitHcPrefetchCtx(HcPrefetchCtxT *hcPrefetchCtx)
{
    DB_POINTER(hcPrefetchCtx);
    hcPrefetchCtx->hcDeletePrefetchCtx.isPrefetch = false;
    hcPrefetchCtx->hcInsertPrefetchCtx.isPrefetch = false;
}

ALWAYS_INLINE static void HcInitHcPrefetchCtxArr(IndexCtxT **idxCtx, uint32_t n)
{
    DB_POINTER(idxCtx);
    DB_ASSERT(n < DM_MAX_KEY_COUNT);
    for (uint32_t i = 0; i <= n; ++i) {
        HcInitHcPrefetchCtx(&idxCtx[i]->hcPrefetchCtx);
    }
}

void IdxAmUninit(void);
void IdxRecoveryCallBackFuncRegister(IdxKeyCmpFunc keyCmpCallback);
IdxKeyCmpFunc IdxGetRecoveryCallBackFunc(void);
bool IsIdxSupportPersist(uint8_t indexType);

#ifdef __cplusplus
}
#endif

#endif
