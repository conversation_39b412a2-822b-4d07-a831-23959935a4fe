/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 存储引擎DFX接口 对外提供的事务相关的接口和结构
 * Author: GMDBV5 SE Team
 * Create: 2025-07-13
 */
#ifndef SE_DFX_TRX_H
#define SE_DFX_TRX_H

#include "se_trx.h"

#ifdef __cplusplus
extern "C" {
#endif

// 事务池中所有事务状态信息
typedef struct TrxStatAll {
    uint16_t trxUsdNum;
    bool optiRetryTrxOpen;
    uint8_t optiRetryState;
    uint32_t trxCnt;
    uint32_t rwTrxICnt;
    uint32_t roTrxICnt;
    uint64_t maxTrxId;
    uint64_t minActTrxId;
    uint64_t maxTrxTimeUse;
    uint64_t curRetryTrxId;
    uint32_t retryTrxQueueLen;
    uint32_t commitTrxQueueLen;
} TrxStatAllT;

typedef struct TagSeLockAcqNodeT {
    uint32_t lockAcqId;
    uint32_t holdNum;
    uint32_t waitNum;
    uint32_t labelId;
    uint32_t pageId;
    uint32_t slotId;
    uint16_t dbId;
    uint16_t lockMode;     // 锁的模式
    uint16_t lockType;     // 锁的类型
    uint16_t lockState;    // 锁的授权状态
    uint16_t waitForUpdX;  // 持有某种共享锁后, 欲升级为排他锁的事务的个数
    uint16_t shareUpdX;    // 持有某种共享锁后, 升级为排他锁的事务的个数
} SeLockAcqStatT;

typedef struct TagSeLockNotifyNodeT {
    uint32_t lockAcqId;
    uint32_t labelId;
    uint32_t pageId;
    uint32_t slotId;
    uint32_t holdNum;
    uint16_t dbId;
    uint16_t lockMode;
} SeLockNotifyStatT;

#ifdef EXPERIMENTAL_GUANGQI
typedef struct TrxCloneInfo {
    uint32_t cloneId;       // 本事务的cloneId
    uint16_t trxCloneType;  // TrxCloneTypeE
    bool successorTrxActive;
    bool childTrxActive;
    TrxIdT successorTrxId;  // 父事务所在连接的子事务
    TrxIdT childTrxId;      // 另外一个连接的子事务
} TrxCloneInfoT;
#endif

// 事务详细信息，包括事务本身状态，持有事务锁详细信息
typedef struct TrxDetail {
    // 事务状态
    uint64_t trxId;
    uint16_t connId;
    uint16_t trxSlot;
    uint16_t isAbort;
    uint16_t isReadOnly;
    uint16_t trxState;
    uint16_t isRetryTrx;
    uint32_t isolationLevel;
    uint64_t startTime;
    uint64_t tid;
    uint32_t retainedUndoRecCnt;
    uint32_t normalUndoRecCnt;
    // save point状态
    DbListT savePointList;
    uint32_t savePointIdForCheckActive;
    // 事务持有锁状态
    uint32_t holdLockNum;
    uint32_t holdLockAcqId;
    uint32_t waitLockNotifyId;
    SeLockAcqStatT *lockAcqStat;
    SeLockNotifyStatT lockNotifyStat;
#ifdef EXPERIMENTAL_GUANGQI
    TrxCloneInfoT trxCloneInfo;
#endif
} TrxDetailT;

typedef struct TagLastOptiTrxLabelsInfoT {
    uint32_t labelId;
    DmLabelTypeE labelType;
} LastOptiTrxLabelsInfoT;

typedef struct TagHeapLastOptiTrxStatT {
    uint64_t trxId;                           // 事务ID，也是事务开始时刻的逻辑时钟
    char peerProcInfo[SE_CONN_INFO_MAX_LEN];  // 记录该事务对应的对端信息，固定长度SE_CONN_INFO_MAX_LEN
    uint32_t labelCnt;
    LastOptiTrxLabelsInfoT *labelArr;
} HeapLastOptiTrxStatT;

/**
 * @brief 获取一个事务持有以及等待的锁信息
 * @param slot  入参 & 出参，事务池的slot和下一个要查询事务的slot
 * @param SeTrxtLockStatT  出参, 存放当前事务的锁信息
 * @param instanceId  入参, 存储引擎实例ID
 * @param dynamicMemCtx  入参, 动态内存上下文
 * @return 成功或失败原因
 **/
Status SeGetTrxtLockDetail(uint16_t *slot, TrxDetailT *trxDetail, SeRunCtxHdT seRunCtx);

void TrxGetSingleStat(TrxDetailT *trxDetail, const TrxT *trx);

void TrxGetTrxStatAll(TrxStatAllT *trxStat, const SeRunCtxHdT seRunCtxHd);

Status TrxGetLastOptiTrxStat(
    HeapLastOptiTrxStatT *heapOptiTrxStat, const SeRunCtxHdT seRunCtxHd, DbMemCtxT *memCtx, uint64_t labelLastTrxId);

#ifdef __cplusplus
}
#endif

#endif
