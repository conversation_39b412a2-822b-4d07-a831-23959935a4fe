/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: art_container_inner.h
 * Description: Art聚簇容器内部接口
 * Author: panpeixian
 * Create: 2024/7/26
 */
#ifndef ART_CONTAINER_INNER_H
#define ART_CONTAINER_INNER_H

#include "art_container.h"
#include "art_base.h"
#include "se_art_scan.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef ART_CONTAINER

#define ART_CONTAINER_MAX_KEY_LEN SE_IDX_MAX_KEY_LENGTH + 1

typedef struct ArtCtxMgr {
    DbMemCtxT *parentMemCtx;
    DbMemCtxT **memCtxArr;  // 多线程内存分配不冲突
    bool isInit;
    uint8_t memCtxArrIdx;
} ArtCtxMgrT;

typedef struct ArtContainerLabel {
    ArtContainerMetaCfgT metaCfg;
    DbLatchT latch;
    uint32_t isConstructed;
    ArtCtxMgrT artCtxMgr;
    uint32_t shmemCtxId;
    uint32_t validCode;
    uint64_t version;
    uint16_t flag;
    ArtTreeT artTree;
    ContainerArtCfgT artCfg;
    uint32_t signedFlag;
} ArtContainerLabelT;

typedef struct ArtScanIter {
    IndexKeyT upperBound;
    DmVlIndexLabelT *indexLabel;
    bool lowerInclude;
    bool upperIncluded;
    bool isAscend;
    bool isScanBeginOutOfArt;
    uint8_t upperKeyBuf[ART_CONTAINER_MAX_KEY_LEN];
    ArtIteratorT artIterator;
    MatchTypeE keyMatchType;  // Art容器下对于key的匹配规则
    IndexKeyT originKey;
    uint8_t originKeyBuf[ART_CONTAINER_MAX_KEY_LEN];
    bool isKeyEq;
    int32_t nearDiff;
} ArtScanIterT;

typedef struct ArtScanPara {
    bool leftIncluded;
    bool rightIncluded;
    bool isAscend;
    bool keyEq;
    IndexKeyT *leftKey;
    IndexKeyT *rightKey;
    MatchTypeE keyMatchType;
    uint8_t condIdx;       /**前缀匹配的索引项 */
    uint8_t matchBytesNum; /**前缀匹配多少个字节 */
    uint8_t nearAIdx;      /**临近查询的A字段*/
    uint8_t nearBIdx;      /**临近查询的B字段，查询离A最近且B字段相同的数据*/
} ArtScanParaT;

typedef struct ArtContainerRunCtx {
    ContainerTypeE containerType;
    uint64_t version;
    ArtContainerOpenCfgT openCfg;
    ArtContainerLabelT *artLabel;
    SeRunCtxT *seRunCtx;
    HeapTupleBufT heapTuple;
    TupleBufT tupleBuf;  // 用于替换heapTuple
    ShmemPtrT shmAddr;
    bool isFirstScan;
    bool isNeedCpyBuf;
    uint16_t reserve;
    ArtRunningCtxT artRunCtx;
    ArtScanIterT itr;
    ArtScanParaT scanParam;
    DbMemCtxT *memCtx;  // 申请数据内存的memCtx
    uint8_t memCtxIdx;  // 用的该表的ArtCtxMgrT中memCtxArr的第几个memCtx
} ArtContainerRunCtxT;

ALWAYS_INLINE static void ArtContainerRlock(const ArtContainerRunCtxT *runCtx)
{
    DbRWLatchR(&runCtx->artLabel->latch);
}

ALWAYS_INLINE static void ArtContainerRUnlock(const ArtContainerRunCtxT *runCtx)
{
    DbRWUnlatchR(&runCtx->artLabel->latch);
}

ALWAYS_INLINE static void ArtContainerWlock(const ArtContainerRunCtxT *runCtx)
{
    DbRWLatchW(&runCtx->artLabel->latch);
}

ALWAYS_INLINE static void ArtContainerWUnlock(const ArtContainerRunCtxT *runCtx)
{
    DbRWUnlatchW(&runCtx->artLabel->latch);
}

Status ArtContainerCreateMemCtx(ArtContainerLabelT *artContainer);

void ArtContainerInitArtRunCtx(ArtContainerRunCtxT *runCtx, ArtTreeT *art);

#endif
#ifdef __cplusplus
}
#endif
#endif
