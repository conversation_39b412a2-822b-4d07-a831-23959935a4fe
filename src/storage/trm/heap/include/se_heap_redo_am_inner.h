/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: heap redo 公开调用接口
 */
#ifndef SE_HEAP_REDO_AM_INNER_H
#define SE_HEAP_REDO_AM_INNER_H

#include "se_instance.h"
#include "db_internal_error.h"
#include "se_define.h"
#include "se_heap.h"
#include "se_heap_page.h"
#include "se_page.h"
#include "se_redo.h"
#include "se_heap_redo_am.h"
#include "se_persistcap_heap_redo.h"

#ifdef __cplusplus
extern "C" {
#endif
SO_EXPORT void HeapRedoAmInit(SeInstanceT *seIns);
extern HeapRedoAmT g_heapRedoAm;
static inline HeapRedoAmT *HeapGetRedoAm(void)
{
    return &g_heapRedoAm;
}
StatusInter HeapReplayFuncRegister(SeInstanceT *seIns);

static inline void HeapRedoForFixPageInit(HeapRunCtxT *ctx, PageIdT pageAddr, const HFPageCfgT *pageCfg)
{
    HeapRedoAmT *heapRedoAm = HeapGetRedoAm();
    if (heapRedoAm->redoForFixPageInitFunc != NULL && ctx->heapCfg.isPersistent) {
        heapRedoAm->redoForFixPageInitFunc(pageAddr, pageCfg);
    }
}

static inline void HeapRedoForSchemaChange(HeapRunCtxT *ctx, HFPageHeadT *pageHead, HFPageCfgT *pageCfg, uint8_t flag)
{
    HeapRedoAmT *heapRedoAm = HeapGetRedoAm();
    if (heapRedoAm->redoForSchemaChangeFunc != NULL && ctx->heapCfg.isPersistent) {
        heapRedoAm->redoForSchemaChangeFunc(pageHead, pageCfg, flag);
    }
}

static inline void HeapRedoForVarPageInit(HeapRunCtxT *ctx, PageIdT *addr, uint32_t slotExtendSize)
{
    HeapRedoAmT *heapRedoAm = HeapGetRedoAm();
    if (heapRedoAm->redoForVarPageInitFunc != NULL && ctx->heapCfg.isPersistent) {
        heapRedoAm->redoForVarPageInitFunc(addr, (PageSizeT)slotExtendSize);
    }
}

static inline void HeapRedoForFullPage(HeapRunCtxT *ctx, HVPageHeadT *pageHead)
{
    HeapRedoAmT *heapRedoAm = HeapGetRedoAm();
    if (heapRedoAm->redoForFullPageFunc != NULL && ctx->heapCfg.isPersistent) {
        heapRedoAm->redoForFullPageFunc(ctx, pageHead);
    }
}

static inline void HeapRedoForInsert(
    HeapRunCtxT *ctx, HpItemPointerT *addr, uint32_t redoOpType, HpPageRowOpInfoT *opInfo)
{
    HeapRedoAmT *heapRedoAm = HeapGetRedoAm();
    if (heapRedoAm->redoForInsertFunc != NULL && ctx->heapCfg.isPersistent) {
        heapRedoAm->redoForInsertFunc(ctx, addr, redoOpType, opInfo);
    }
}

static inline void HeapRedoForBatchInsert(
    HeapRunCtxT *ctx, PageIdT pageId, uint64_t rollPtr, HpPageGroupRowInfoT *groupRow, uint32_t fsmAvailSize)
{
    HeapRedoAmT *heapRedoAm = HeapGetRedoAm();
    if (heapRedoAm->redoForBatchInsertFunc != NULL && ctx->heapCfg.isPersistent) {
        return heapRedoAm->redoForBatchInsertFunc(ctx, pageId, rollPtr, groupRow, fsmAvailSize);
    }
}

static inline void HeapRedoForUpdate(HeapRunCtxT *ctx, HeapRedoUpdateParams *params)
{
    HeapRedoAmT *heapRedoAm = HeapGetRedoAm();
    if (heapRedoAm->redoForUpdateFunc != NULL && ctx->heapCfg.isPersistent) {
        heapRedoAm->redoForUpdateFunc(params);
    }
}

static inline void HeapRedoForDelete(HeapRunCtxT *ctx, HeapRedoDeleteParams *params)
{
    HeapRedoAmT *heapRedoAm = HeapGetRedoAm();
    if (heapRedoAm->redoForDeleteFunc != NULL && ctx->heapCfg.isPersistent) {
        heapRedoAm->redoForDeleteFunc(params);
    }
}

static inline void HeapRedoForCreate(PageIdT heapAddr, HeapT *heap)
{
    DB_POINTER(heap);
    HeapRedoAmT *heapRedoAm = HeapGetRedoAm();
    if (heapRedoAm->redoForCreateFunc != NULL && heap->constInfo.isPersistent) {
        heapRedoAm->redoForCreateFunc(heapAddr, heap);
    }
}

static inline void HeapRedoForModify(bool isPersistent, uint8_t *buf, uint32_t bufSize, PageIdT *addr, uint16_t offset)
{
    HeapRedoAmT *heapRedoAm = HeapGetRedoAm();
    if (heapRedoAm->redoForModifyFunc != NULL && isPersistent) {
        heapRedoAm->redoForModifyFunc(buf, bufSize, addr, offset);
    }
}

static inline void HeapRedoForRollback(
    HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchCurRowInfo, bool rollbackNormalRow, uint8_t *buf, uint32_t bufSize)
{
    HeapRedoAmT *heapRedoAm = HeapGetRedoAm();
    if (heapRedoAm->redoForRollbackImplFunc != NULL && ctx->heapCfg.isPersistent) {
        heapRedoAm->redoForRollbackImplFunc(ctx, fetchCurRowInfo, rollbackNormalRow, buf, bufSize);
    }
}

static inline void HeapRedoForRowHead(
    HeapRunCtxT *ctx, PageHeadT *pageHead, uint32_t type, uint32_t offset, uint32_t size)
{
    HeapRedoAmT *heapRedoAm = HeapGetRedoAm();
    if (heapRedoAm->redoForRowHeadFunc != NULL && ctx->heapCfg.isPersistent) {
        heapRedoAm->redoForRowHeadFunc(ctx, pageHead, type, offset, size);
    }
}

static inline StatusInter HeapRedoForInsertOrUpdateLobRow(HpRunHdlT heapRunHdl, HpPageRowOpInfoT *opInfo)
{
    if (g_heapRedoAm.redoForInsertOrUpdateLobRowFunc != NULL && heapRunHdl->heapCfg.isPersistent) {
        return g_heapRedoAm.redoForInsertOrUpdateLobRowFunc(heapRunHdl, opInfo);
    }
    return STATUS_OK_INTER;
}

static inline void HeapRedoForBindSliceSubRowToDirRow(
    HpRunHdlT heapRunHdl, HpItemPointerT dirRowPtr, HpSliceRowDirT *dirRow)
{
    if (g_heapRedoAm.redoForBindSliceSubRowToDirRowFunc != NULL && heapRunHdl->heapCfg.isPersistent) {
        g_heapRedoAm.redoForBindSliceSubRowToDirRowFunc(heapRunHdl, dirRowPtr, dirRow);
    }
}

static inline void HeapRedoForBindDstRow(HpRunHdlT heapRunHdl, HeapRowInfoT *srcRowInfo)
{
    if (g_heapRedoAm.redoForBindDstRowFunc != NULL && heapRunHdl->heapCfg.isPersistent) {
        g_heapRedoAm.redoForBindDstRowFunc(heapRunHdl, srcRowInfo);
    }
}

static inline void HeapRedoForUpdateDirRowListPtr(
    HpRunHdlT heapRunHdl, RedoLogTypeE type, HpItemPointerT originPtr, HpItemPointerT listPtr)
{
    if (g_heapRedoAm.redoForUpdateDirRowListPtrFunc != NULL && heapRunHdl->heapCfg.isPersistent) {
        g_heapRedoAm.redoForUpdateDirRowListPtrFunc(heapRunHdl, type, originPtr, listPtr);
    }
}

static inline void HeapRedoForDelSliceSubRow(HpRunHdlT heapRunHdl, HeapRowInfoT *dirRowInfo)
{
    if (g_heapRedoAm.redoForDelSliceSubRowFunc != NULL && heapRunHdl->heapCfg.isPersistent) {
        g_heapRedoAm.redoForDelSliceSubRowFunc(heapRunHdl, dirRowInfo);
    }
}

// 涉及isUpgNotSatisfy逻辑时，主要修改 freeSize, isUpgNotSatisfy，此函数负责其redo
static inline void HeapRedoForUpgradeMarker(bool isPersistent, HFPageHeadT *pageHead)
{
    PageIdT pageId = pageHead->baseHead.pageHead.addr;
    HeapRedoForModify(isPersistent, (uint8_t *)&pageHead->baseHead.pageHead.freeSize,
        sizeof(pageHead->baseHead.pageHead.freeSize), &pageId, offsetof(PageHeadT, freeSize));

    // 记录isUpgNotSatisfy, 该字段由位域表示，无法直接获取字段偏移量，因此连同前一个字段nextFreeSlotId一起记录
    HeapRedoForModify(isPersistent, (uint8_t *)&pageHead->nextFreeSlotId,
        sizeof(pageHead->nextFreeSlotId) + sizeof(uint16_t), &pageId, offsetof(HFPageHeadT, nextFreeSlotId));
}

#ifdef __cplusplus
}
#endif

#endif
