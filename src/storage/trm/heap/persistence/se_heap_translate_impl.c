/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: heap 持久化升级翻译接口
 * Author: yang<PERSON>gji
 * Create: 2024-9-24
 */

#include "se_heap_translate_impl.h"

// 管理页转换
// 行头转换（扫描，删除，插入流程）
void HeapTranslateOlderHeapTImpl(uint8_t *oldHeap)
{
    // 开发负责将新老管理页转换，得到符合当前最新版本的newHeap
    // 增量场景还需要记录redo日志
    // 如当前版本没有升级需求，可定义为空。或者不应该注册HeapTranslateInit，或者不编译upgrade部分代码
}

SO_EXPORT_FOR_TS void HeapTranslateAmInit(SeInstanceT *seIns)
{
    HeapTranslateAmT heapTranslateAm = {
        HeapTranslateOlderHeapTImpl,
    };
    // gmdb base全局变量赋有效指针
    HeapTranslateSetAmStruct(&heapTranslateAm);
}
