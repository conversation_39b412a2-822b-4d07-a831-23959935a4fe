/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: implementation of B-tree
 * Author: wangyixuan
 * Create: 2022-10-19
 */

#include "se_btree_index.h"
#include "drt_instance.h"
#include "se_btree_redo_am.h"
#include "se_btree_search.h"
#include "se_buffer_pool_priority_recycle.h"
#include "se_heap_base.h"
#include "se_page_desc.h"
#include "se_undo.h"
#include "se_redo_inner.h"

static void BTreeIndexGetCtxSize(size_t *ctxSize, size_t *iterSize)
{
    *ctxSize = sizeof(BTreeIndexCtxT);
    // the `INDEX_CTX_KEY_NUM` here for scan: left key + right key + current key
    *iterSize = sizeof(BTreeScanItrT) + MAX_BTREE_KEY_LENGTH * INDEX_CTX_KEY_NUM;
}

SO_EXPORT_FOR_TS void BTreeIndexAMInit(void)
{
    IdxFuncT bTreeIdxFunc = IdxEmptyIdxFunc();
    bTreeIdxFunc.idxCreate = BTreeIndexCreate;
    bTreeIdxFunc.idxDrop = BTreeIndexMarkDrop;
    bTreeIdxFunc.idxCommitDrop = BTreeIndexDrop;
    bTreeIdxFunc.idxTruncate = BTreeIndexMarkTruncate;
    bTreeIdxFunc.idxCommitTruncate = BTreeIndexTruncate;
    bTreeIdxFunc.idxOpen = BTreeIndexOpen;
    bTreeIdxFunc.idxClose = BTreeIndexClose;
    bTreeIdxFunc.idxInsert = BTreeIndexInsert;
    bTreeIdxFunc.idxBatchInsert = BTreeIndexBatchInsert;
    bTreeIdxFunc.idxDelete = BTreeIndexDelete;
    bTreeIdxFunc.idxBatchDelete = BTreeIndexBatchDelete;
    bTreeIdxFunc.idxUpdate = BTreeIndexUpdate;
    bTreeIdxFunc.idxBatchUpdate = BTreeIndexBatchUpdate;
    bTreeIdxFunc.idxLookup = BTreeIndexLookup;
    bTreeIdxFunc.idxBatchLookup = BTreeIndexBatchLookup;
    bTreeIdxFunc.idxBeginScan = BTreeIndexBeginScan;
    bTreeIdxFunc.idxScan = BTreeIndexScan;
    bTreeIdxFunc.idxSetDirection = BTreeIndexSetDirection;
    bTreeIdxFunc.idxUndoInsert = BTreeIndexUndoInsert;
    bTreeIdxFunc.idxUndoRemove = BTreeIndexUndoRemove;
    bTreeIdxFunc.idxUndoUpdate = BTreeIndexUpdate;
    bTreeIdxFunc.idxGetKeyCount = BTreeIndexGetKeyCount;
    bTreeIdxFunc.idxStatView = BTreeIndexStatView;
    bTreeIdxFunc.idxGetPageSize = BTreeIndexGetPageSize;
    bTreeIdxFunc.idxScaleIn = BTreeIndexScaleIn;
    bTreeIdxFunc.idxGetEstimateMemSize = BTreeIndexGetEstimateMemSize;
    bTreeIdxFunc.idxGetCtxSize = BTreeIndexGetCtxSize;
    bTreeIdxFunc.idxTableLoad = NULL;
#ifdef FEATURE_LPASMEM
    bTreeIdxFunc.idxSetExtendKey = BTreeSetExtendKey;
#endif
    IdxAmFuncRegister((uint8_t)BTREE_INDEX, &bTreeIdxFunc);
}

/**
 * @brief Get page according to page ID.
 * @param[in] pageMgr page manager
 * @param[in] pageId  page ID
 * @param[in] level  level of page in btree
 * @param[in] isWrite whether the page is used for write operation
 * @param[out] page   *page is the address of the first byte of the page corresponding to `pageId`
 */
StatusInter BTreeGetPage(PageMgrT *pageMgr, PageIdT pageId, uint32_t level, uint8_t **page, bool isWrite)
{
    uint32_t option = ENTER_PAGE_NORMAL;
    if (isWrite) {
        option |= ENTER_PAGE_WRITE;
    }
    PriorityRecyArgT recyArg = {SE_RECYCLE_PRIORITY_MULTI, level + 1};
    StatusInter interErrno = SeGetPageWithArg(pageMgr, pageId, option, &recyArg, page);
    if (interErrno != STATUS_OK_INTER) {
        // we guarantee that `pageId` is valid, thus `SeGetPage` should not fail
        SE_ERROR(interErrno, "GetPage, deviceId:%" PRIu32 ", blockId:%" PRIu32, pageId.deviceId, pageId.blockId);
    }
    return interErrno;
}

/*
 * @brief Initialization of B-tree statistics information.
 * @param[in,out] meta  the meta-info of the tree
 */
static void BTreeInitStat(BTreeT *meta)
{
    // the basic statistics information
    meta->stat.bTreeIndex.recordCount = 0;
    meta->stat.bTreeIndex.treeHeight = 1;

    // the memory-related statistics information
    BTreeMemStatT *memStat = &meta->stat.bTreeIndex.memStat;
    memStat->totalNodeCount = 1;  // initially, the tree contains only one node, i.e., the root
    memStat->leafCount = 1;       // the root is a leaf at the beginning
    memStat->internalNodeCount = 0;
    memStat->bigKeyPageCount = 0;
    memStat->totalMemorySize = memStat->pageSize * memStat->totalNodeCount;
    // meta header: sizeof(PageHeadT) + sizeof(BTreeT); data part: sizeof(BTreeNodeHdrT)
    memStat->occupiedMemorySize = (uint64_t)(sizeof(PageHeadT) + sizeof(BTreeT) + sizeof(BTreeNodeHdrT));

    // the performance-related statistics information
    BTreePerfStatT *perfStat = &meta->stat.bTreeIndex.perfStat;
    perfStat->insertCount = 0;
    perfStat->deleteCount = 0;
    perfStat->splitCount = 0;
    perfStat->freeCount = 0;
}

static inline void BTreeInitStatWithPagesize(BTreeT *meta, uint32_t pageSize)
{
    meta->stat.bTreeIndex.memStat.pageSize = pageSize;
    BTreeInitStat(meta);
}

/**
 * @brief Creation and initialization of the root of the tree.
 * @param[in] cfg           pointer to the configuration
 * @param[in] pageSizeInKb  the size of page in kilobytes
 * @param[in] pageMgr       page manager
 * @param[in] redoCtx       the redo runtime context
 * @param[out] ShmAddr      the address of the index, which is that of the meta page
 */
static StatusInter BTreeCreateRoot(
    IndexMetaCfgT *cfg, SeInstanceT *seInstance, PageMgrT *pageMgr, RedoRunCtxT *redoCtx, ShmemPtrT *idxShmAddr)
{
    uint32_t trmId = SeGetNewTrmId(seInstance);
    // allocate a new page for the meta page
    PageIdT metaPageId;
    AllocPageParamT allocPageParam = SeInitAllocPageParam(
        cfg->tableSpaceIndex, trmId, RSM_INVALID_LABEL_ID, seInstance->dbInstance, NULL);  // index不使用rsmUndo
    StatusInter interErrno = SeAllocPage(pageMgr, &allocPageParam, &metaPageId);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "Alloc meta page, spaceId:%" PRIu32, cfg->tableSpaceIndex);
        return interErrno;
    }

    // read the meta page
    PageHeadT *metaPage = NULL;
    interErrno = BTreeGetPage(pageMgr, metaPageId, HIGHEST_PRIORITY_PAGE, (uint8_t **)&metaPage, true);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: GetPage");
        FreePageParamT freePageParam = SeInitFreePageParam(
            cfg->tableSpaceIndex, metaPageId, seInstance->dbInstance, NULL, SE_INVALID_LABEL_ID, false);
        (void)SeFreePage(pageMgr, &freePageParam);
        return interErrno;
    }
    BTreeT *bTree = (BTreeT *)(void *)(metaPage + 1);
    bTree->idxBase.indexCfg = *cfg;
    bTree->trmId = trmId;
    bTree->magicNum = BTREE_VALID_MAGIC_NUM;
    // initialize the statistics
    BTreeInitStatWithPagesize(bTree, SIZE_K(seInstance->seConfig.pageSize));
    BTreeRedoForCreate(metaPageId, bTree, redoCtx);

    // initialize data node
    BTreeInitPage(true, (uint8_t *)metaPage);
    BTreeRedoForInitNode(metaPageId, true, redoCtx);

    SeLeavePage(pageMgr, metaPageId, true);

    *idxShmAddr = *(ShmemPtrT *)(&(metaPageId));

    DB_LOG_INFO("Create index, indexId: %" PRIu32 ", tableSpaceId: %" PRIu32 ", trmId: %" PRIu32, cfg->indexId,
        cfg->tableSpaceIndex, trmId);
    return STATUS_OK_INTER;
}

static StatusInter BTreeLogDDLUndo(SeRunCtxT *seRunCtx, IndexMetaCfgT *cfg, PageIdT metaAddr, UndoRowOpTypeE opType)
{
    UndoRowOpInfoT rowOpInfo = {0};
    rowOpInfo.isRetained = false;
    rowOpInfo.resType = TRX_RES_BTREE;
    rowOpInfo.labelId = cfg->indexId;
    rowOpInfo.isPersistent = true;
    rowOpInfo.isRsm = false;
    rowOpInfo.opType = opType;
    rowOpInfo.containerAddr = *(uint64_t *)(void *)&metaAddr;
    SeUndoCtxT *undoCtx = (SeUndoCtxT *)seRunCtx->undoCtx;
    TrxT *trx = (TrxT *)seRunCtx->trx;
    uint64_t rollPtr;
    return TrxUndoReportRowOperation(undoCtx, trx, &rowOpInfo, &rollPtr);
}

static inline PageMgrT *BTreeIndexGetPageMgr(SeInstanceT *seInstance, IndexMetaCfgT *cfg)
{
    if (seInstance->duMemMgr == NULL) {
        // 有可能是直连写的情景，所以使用SeGetPageMgr
        return SeGetPageMgr(seInstance->instanceId);
    }
    // 恢复阶段，重建索引流程也走bufferpool即可
    if (DbRecoveryDoing(seInstance)) {
        return (PageMgrT *)seInstance->pageMgr;
    }
    // 双持久化情景
    if (cfg->tableSpaceIndex == 0) {
        // 系统表，走bufferpool流程
        return (PageMgrT *)seInstance->pageMgr;
    }
    return (PageMgrT *)seInstance->duMemMgr;
}

/**
 * @brief Create a B-tree index, which contains only the empty root node.
 * @param[in] cfg       the configuration
 * @param[out] idxShmAddr  the address of the B tree
 */
SO_EXPORT Status BTreeIndexCreate(SeRunCtxT *seRunCtx, IndexMetaCfgT cfg, ShmemPtrT *idxShmAddr)
{
    DB_POINTER(idxShmAddr);
    if (cfg.keyDataType >= SE_INDEX_DATATYPE_BUTT) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "BTree: inv key type");
        return GMERR_INTERNAL_ERROR;
    }
    SeInstanceT *seInstance = (SeInstanceT *)seRunCtx->seIns;
    if (seInstance == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "BTree: seInstance");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    PageMgrT *pageMgr = BTreeIndexGetPageMgr(seInstance, &cfg);
    if (pageMgr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "BTree: pagemgr");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    // prepare for redo log
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    // 目前btree暂不支持临时表，如需要支持临时表再提需求，且pageManager应该是memdata而不是SeGetPageMgr
    RedoLogBegin(redoCtx);

    // create and initialize the root node; the resulting node is empty
    StatusInter ret = BTreeCreateRoot(&cfg, seInstance, pageMgr, redoCtx, idxShmAddr);
    if (ret != STATUS_OK_INTER) {
        (void)RedoLogEnd(redoCtx, false);
        return DbGetExternalErrno(ret);
    }

    ret = BTreeLogDDLUndo(seRunCtx, &cfg, *(PageIdT *)idxShmAddr, TRX_OP_CREATE);
    if (ret != STATUS_OK_INTER) {
        BTreeIndexDrop(seRunCtx, *idxShmAddr);
        *idxShmAddr = DB_INVALID_SHMPTR;
        (void)RedoLogEnd(redoCtx, false);
        return DbGetExternalErrno(ret);
    }

    ret = RedoLogEnd(redoCtx, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: redoLog");
    }

    return DbGetExternalErrno(ret);
}

StatusInter BTreeFreeBigKeyInDescendants(
    BTreeEntryHdrT *entryHdr, uint32_t spaceId, PageMgrT *pageMgr, RedoRunCtxT *redoCtx)
{
    if (!entryHdr->isBigKey || entryHdr->size == 0) {
        return STATUS_OK_INTER;
    }
    PageIdT bigKeyPageId = BTreeGetBigKeyPageId(entryHdr);
    if (DbIsPageIdValid(bigKeyPageId)) {
        RedoLogBegin(redoCtx);
        FreePageParamT freePageParam = SeInitFreePageParam(
            spaceId, bigKeyPageId, redoCtx->redoMgr->seIns->dbInstance, NULL, SE_INVALID_LABEL_ID, false);
        StatusInter ret = SeFreePage(pageMgr, &freePageParam);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Free page, pageId:%" PRIu32 ", %" PRIu32, bigKeyPageId.deviceId, bigKeyPageId.blockId);
            (void)RedoLogEnd(redoCtx, false);
            return ret;
        }
        ret = RedoLogEnd(redoCtx, true);
        if (ret != STATUS_OK_INTER) {
            // there is no way to rollback `SeFreePage`
            SE_ERROR(ret, "Redo Log End.");
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter TryFreeNonLeafNodeDescendants(
    PageIdT ancPageId, uint32_t spaceId, PageMgrT *pageMgr, const int16_t numSlots, const uint16_t level);

/**
 * @brief Free the descendants of the node of address `ancPageId`.
 * @param[in] ancPageId  the address of the ancestor node
 * @param[in] spaceId  the space ID
 * @param[in] pageMgr  the page manager
 * @param[in] redoCtx  the redo context
 */
StatusInter BTreeFreeDescendants(PageIdT ancPageId, uint32_t spaceId, PageMgrT *pageMgr, RedoRunCtxT *redoCtx)
{
    // get `level` and `numSlots`, will get page again soon, so level here is not important
    uint8_t *ancPage = NULL;
    StatusInter interErrno = BTreeGetPage(pageMgr, ancPageId, 0, &ancPage, false);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: GetPage");
        return interErrno;
    }
    BTreeNodeHdrT *nodeHdr = BTreeGetNodeHdr(ancPage);
    const uint16_t level = nodeHdr->level;
    const int16_t numSlots = nodeHdr->numSlots;
    DbInstanceHdT dbInstance = redoCtx ? redoCtx->redoMgr->seIns->dbInstance : NULL;

    // this is a leaf; there is no descendant
    if (level == 0) {
        for (int16_t i = 0; i < numSlots; ++i) {
            BTreeEntryHdrT *entryHdr = NULL;
            interErrno = BTreeGetEntryHdr(ancPage, i, &entryHdr);
            if (interErrno != STATUS_OK_INTER) {
                // when free descendants A
                SE_ERROR(interErrno, "BTree: Get entryHdr.");
                SeLeavePage(pageMgr, ancPageId, false);
                return interErrno;
            }
            // Release the bigKeyPage that may exist in each slot of the leaf node. If the release fails,
            // the complete process continues.
            (void)BTreeFreeBigKeyInDescendants(entryHdr, spaceId, pageMgr, redoCtx);
        }
        SeLeavePage(pageMgr, ancPageId, false);
        // Feed the watchdog each time after a leaf node is released to prevent suspension caused by slow release of
        // all leaf nodes in the tree.
        (void)DrtKeepThisWorkerAlive(dbInstance);
        return STATUS_OK_INTER;
    }
    SeLeavePage(pageMgr, ancPageId, false);
    // traverse the subtrees
    return TryFreeNonLeafNodeDescendants(ancPageId, spaceId, pageMgr, numSlots, level);
}

StatusInter TryFreeNonLeafNodeDescendants(
    PageIdT ancPageId, uint32_t spaceId, PageMgrT *pageMgr, const int16_t numSlots, const uint16_t level)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    // traverse the subtrees
    for (int16_t slot = 0; slot < numSlots; ++slot) {
        // get the child page ID
        uint8_t *ancPage = NULL;
        StatusInter interErrno = BTreeGetPage(pageMgr, ancPageId, level, &ancPage, false);
        if (interErrno != STATUS_OK_INTER) {
            SE_ERROR(interErrno, "GetPage, traverse the subtrees");
            return interErrno;
        }
        BTreeEntryHdrT *entryHdr = NULL;
        interErrno = BTreeGetEntryHdr(ancPage, slot, &entryHdr);
        if (interErrno != STATUS_OK_INTER) {
            SE_ERROR(interErrno, "Get entryHdr, free descendantsB");
            SeLeavePage(pageMgr, ancPageId, false);
            return interErrno;
        }
        interErrno = BTreeFreeBigKeyInDescendants(entryHdr, spaceId, pageMgr, redoCtx);
        if (interErrno != STATUS_OK_INTER) {
            SE_ERROR(interErrno, "Free big key, free descendantsB");
            SeLeavePage(pageMgr, ancPageId, false);
            return interErrno;
        }
        const PageIdT childPageId = entryHdr->childPageId;
        SeLeavePage(pageMgr, ancPageId, false);

        // the child has been freed
        if (!DbIsPageIdValid(childPageId)) {
            continue;
        }

        // free the descendants in the subtree rooted at the node of address `childPageId`
        interErrno = BTreeFreeDescendants(childPageId, spaceId, pageMgr, redoCtx);
        if (interErrno != STATUS_OK_INTER) {
            SE_ERROR(interErrno, "Free descendants, child pageId:%" PRIu32 ", %" PRIu32, childPageId.deviceId,
                childPageId.blockId);
            return interErrno;
        }
        BTreePageAddrT ancNode = {.pageId = ancPageId, .page = ancPage};
        NodeFreeParaT para = {.parentNode = ancNode, .childPageId = childPageId, .slot = slot};
        interErrno = FreeChildNodeAndModifyParentNode(redoCtx, pageMgr, spaceId, level, para);
        if (interErrno != STATUS_OK_INTER) {
            return interErrno;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter FreeNodeRecursivelyAndGetSpaceId(
    PageMgrT *pageMgr, PageIdT metaPageId, RedoRunCtxT *redoCtx, uint32_t *spaceId)
{
    // read the meta page to get `spaceId`
    uint8_t *metaPage = NULL;
    StatusInter interErrno = BTreeGetPage(pageMgr, metaPageId, HIGHEST_PRIORITY_PAGE, &metaPage, false);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: unable to getPage when free node recursively.");
        return interErrno;
    }
    // get `spaceId`
    BTreeT *meta = BTreeGetTreeHdr(metaPage);
    *spaceId = meta->idxBase.indexCfg.tableSpaceIndex;
    SeLeavePage(pageMgr, metaPageId, false);

    // recursively free nodes
    interErrno = BTreeFreeDescendants(metaPageId, *spaceId, pageMgr, redoCtx);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: Free all descendants.");
    }
    return interErrno;
}

SO_EXPORT Status BTreeIndexMarkDrop(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    const PageIdT metaPageId = *(PageIdT *)(void *)(&(idxShmAddr));
    PageMgrT *pageMgr = SeGetPageMgrByPageId(seRunCtx->seIns, metaPageId);
    if (pageMgr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "BTree: null pagemgr.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    // start atomic operation
    RedoLogBegin(redoCtx);

    uint8_t *metaPage = NULL;
    StatusInter ret = BTreeGetPage(pageMgr, metaPageId, HIGHEST_PRIORITY_PAGE, &metaPage, false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: GetPage when drop.");
        (void)RedoLogEnd(redoCtx, false);
        return DbGetExternalErrno(ret);
    }

    BTreeT *meta = BTreeGetTreeHdr(metaPage);
    ret = BTreeLogDDLUndo(seRunCtx, &meta->idxBase.indexCfg, metaPageId, TRX_OP_DROP);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: Write undo.");
        SeLeavePage(pageMgr, metaPageId, false);
        (void)RedoLogEnd(redoCtx, false);
        return DbGetExternalErrno(ret);
    }
    SeLeavePage(pageMgr, metaPageId, false);
    ret = RedoLogEnd(redoCtx, false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: Write redo.");
    }
    return DbGetExternalErrno(ret);
}

static void DropInfoForFreeBtreeMeta(PageMgrT *pageMgr, FreePageParamT *freePageParam, uint32_t indexId, uint32_t trmId)
{
    // free the meta page
    StatusInter interErrno = SeFreePage(pageMgr, freePageParam);
    if (interErrno != STATUS_OK_INTER) {
        SE_WARN(interErrno, "BTree: free meta.");
        return;
    }

    DB_LOG_INFO("Drop index, indexId: %" PRIu32 ", spaceId: %" PRIu32 ", trmId: %" PRIu32, indexId,
        freePageParam->spaceId, trmId);
}
/**
 * @brief Drop the index.
 * @param[in] idxShmAddr  the index address
 */
SO_EXPORT void BTreeIndexDrop(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    const PageIdT metaPageId = *(PageIdT *)(&(idxShmAddr));
    PageMgrT *pageMgr = SeGetPageMgrByPageId(seRunCtx->seIns, metaPageId);
    if (pageMgr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "BTree: null pagemgr");
        return;
    }

    uint8_t *metaPage = NULL;
    StatusInter interErrno = BTreeGetPage(pageMgr, metaPageId, HIGHEST_PRIORITY_PAGE, &metaPage, false);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: getPage when drop.");
        return;
    }

    BTreeT *meta = BTreeGetTreeHdr(metaPage);
    uint32_t trmId = meta->trmId;
    uint32_t indexId = meta->idxBase.indexCfg.indexId;
    uint32_t metaMagicNum = meta->magicNum;
    SeLeavePage(pageMgr, metaPageId, false);
    if (metaMagicNum == BTREE_INVALID_MAGIC_NUM) {
        // already drop btree meta
        DB_LOG_INFO("already drop meta pageId:%" PRIu32 ", %" PRIu32, metaPageId.deviceId, metaPageId.blockId);
        return;
    }

    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    // 该接口为 UndoBTreeCreate / UndoCommitBTreeOperation 调用，内部自己设定原子范围，先关闭外部原子范围
    (void)RedoLogEnd(redoCtx, false);
    uint32_t spaceId = 0;
    interErrno = FreeNodeRecursivelyAndGetSpaceId(pageMgr, metaPageId, redoCtx, &spaceId);
    if (interErrno != STATUS_OK_INTER) {
        (void)RedoLogBegin(redoCtx);
        SE_ERROR(interErrno, "BTree: unable to free node when drop index.");
        return;
    }
    // start atomic operation, ends at UndoCommit/RollbackOperation
    RedoLogBegin(redoCtx);

    metaPage = NULL;
    interErrno = BTreeGetPage(pageMgr, metaPageId, HIGHEST_PRIORITY_PAGE, &metaPage, true);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: unable to getPage when try free meta.");
        return;
    }
    meta = BTreeGetTreeHdr(metaPage);
    meta->magicNum = BTREE_INVALID_MAGIC_NUM;
    SeLeavePage(pageMgr, metaPageId, true);
    BTreeRedoForFreeMeta(metaPageId, redoCtx);
    DbInstanceHdT dbIns = redoCtx ? redoCtx->redoMgr->seIns->dbInstance : NULL;
    FreePageParamT freePageParam = SeInitFreePageParam(spaceId, metaPageId, dbIns, NULL, SE_INVALID_LABEL_ID, false);
    // free the meta page
    DropInfoForFreeBtreeMeta(pageMgr, &freePageParam, indexId, trmId);
}

SO_EXPORT Status BTreeIndexMarkTruncate(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    const PageIdT metaPageId = *(PageIdT *)(void *)(&(idxShmAddr));
    PageMgrT *pageMgr = SeGetPageMgrByPageId(seRunCtx->seIns, metaPageId);
    if (pageMgr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "BTree: Truncate: null pagemgr.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    uint8_t *metaPage = NULL;
    StatusInter ret = BTreeGetPage(pageMgr, metaPageId, HIGHEST_PRIORITY_PAGE, &metaPage, false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: unable to getPage when truncate.");
        (void)RedoLogEnd(redoCtx, false);
        return DbGetExternalErrno(ret);
    }
    BTreeT *meta = BTreeGetTreeHdr(metaPage);
    ret = BTreeLogDDLUndo(seRunCtx, &meta->idxBase.indexCfg, metaPageId, TRX_OP_TRUNCATE);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: unable to write undo when mark truncate.");
        SeLeavePage(pageMgr, metaPageId, false);
        (void)RedoLogEnd(redoCtx, false);
        return DbGetExternalErrno(ret);
    }
    SeLeavePage(pageMgr, metaPageId, false);
    ret = RedoLogEnd(redoCtx, false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: unable to write redo when mark truncate.");
    }
    return DbGetExternalErrno(ret);
}

/**
 * @brief Truncate the index.
 * The caller should have acquired an exclusive lock.** See function "QryAcqLabelLatchForNormalMode".
 * @param[in] idxAddr  the index address
 */
SO_EXPORT void BTreeIndexTruncate(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    const PageIdT metaPageId = *(PageIdT *)(&(idxShmAddr));
    PageMgrT *pageMgr = SeGetPageMgrByPageId(seRunCtx->seIns, metaPageId);
    if (pageMgr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "BTree: null pagemgr when truncate index.");
        return;
    }

    uint32_t spaceId = 0;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    // 该接口为 UndoBTreeCreate / UndoCommitBTreeOperation 调用，内部自己设定原子范围，先关闭外部原子范围
    (void)RedoLogEnd(redoCtx, false);
    StatusInter interErrno = FreeNodeRecursivelyAndGetSpaceId(pageMgr, metaPageId, redoCtx, &spaceId);
    if (interErrno != STATUS_OK_INTER) {
        (void)RedoLogBegin(redoCtx);
        SE_ERROR(interErrno, "Free node when truncate index.");
        return;
    }
    // start atomic operation
    RedoLogBegin(redoCtx);
    uint8_t *metaPage = NULL;
    interErrno = BTreeGetPage(pageMgr, metaPageId, HIGHEST_PRIORITY_PAGE, &metaPage, true);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "GetPage when truncate.");
        // 异常分支保留redo log begin和外部一致
        return;
    }
    BTreeT *meta = BTreeGetTreeHdr(metaPage);
    BTreeInitPage(true, metaPage);
    BTreeRedoForInitNode(metaPageId, true, redoCtx);

    BTreeInitStat(meta);
    BTreeRedoForCreate(metaPageId, meta, redoCtx);
    SeLeavePage(pageMgr, metaPageId, true);
}

/**
 * @brief Open a B-tree index, initializing the corresponding runtime context.
 * @param[in,out] idxCtx the index runtime context
 */
SO_EXPORT Status BTreeIndexOpen(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    PageMgrT *pageMgr = BTreeIndexGetPageMgr((SeInstanceT *)idxCtx->idxOpenCfg.seRunCtx->seIns, &idxCtx->idxMetaCfg);
    if (pageMgr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "BTree: null pagemgr.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    BTreeIndexCtxT *treeCtx = (BTreeIndexCtxT *)(void *)idxCtx->idxRunCtx;
    treeCtx->pageMgr = pageMgr;
    // to avoid the deadlock between latch and transaction lock
    idxCtx->isAcquireLockByTryOnce = true;
#ifdef IDS_HAOTIAN
    idxCtx->idxMetaCfg.isLabelLatchMode = idxCtx->idxOpenCfg.indexLabel->isLabelLatchMode;
#endif

    return GMERR_OK;
}

/**
 * @brief Close a B-tree index.
 * @param[in,out] idxCtx the index runtime context
 */
SO_EXPORT void BTreeIndexClose(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);

    BTreeIndexCtxT *treeCtx = (BTreeIndexCtxT *)(void *)idxCtx->idxRunCtx;
    treeCtx->pageMgr = NULL;
}

/**
 * @brief Write undo log for deletion.
 */
StatusInter BTreeWriteUndoDelete(IndexCtxT *idxCtx, BTreeCursorT *cursor, bool isRollback)
{
    if (isRollback) {
        return STATUS_OK_INTER;
    }

    uint32_t propeNum =
        BTreeGetIndexPropeNum(cursor->idxCtx->idxOpenCfg.vlIndexLabel, cursor->idxCtx->idxMetaCfg.keyDataType);
    BTreeKeyT key = {0};
    StatusInter ret = BTreeGetKey(idxCtx, cursor->curAddr.page, cursor->slot, propeNum, false, &key);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: getKey, pageId:%" PRIu32 ", %" PRIu32 ", slot: %" PRId16, cursor->curAddr.pageId.deviceId,
            cursor->curAddr.pageId.blockId, cursor->slot);
        return ret;
    }

    UndoRowOpInfoT undoInfo = {0};
    undoInfo.isRetained = false;
    undoInfo.resType = TRX_RES_BTREE;
    undoInfo.opType = TRX_OP_DELETE;
    // we do not use `rowTrxId`; thus use it to store the index ID
    undoInfo.rowTrxId = idxCtx->idxMetaCfg.indexId;
    undoInfo.containerAddr = *(uint64_t *)(&(idxCtx->idxShmAddr));
    undoInfo.rowId = key.rowId;
    undoInfo.rowSize = key.isBigKey ? key.actualSize : key.keySize;
    undoInfo.rowBuf = key.keyData;
    undoInfo.labelType = idxCtx->idxOpenCfg.heapHandle->dmDetail.labelType;
    undoInfo.labelId = idxCtx->idxOpenCfg.heapHandle->heapCfg.labelId;
    undoInfo.isPersistent = idxCtx->idxOpenCfg.heapHandle->heapCfg.isPersistent;
    SeRunCtxT *seRunCtx = idxCtx->idxOpenCfg.seRunCtx;
    ret = TrxUndoReportRowOperation(seRunCtx->undoCtx, seRunCtx->trx, &undoInfo, NULL);
    BTreeFreeKeyDataAsBigKey(cursor->idxCtx, &key);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: report undo delete.");
    }
    return ret;
}

/**
 * @brief Physically delete an entry from the node.
 * @param[in] cursor  btree cursor
 * @param[in] pageId  page ID
 * @param[in] page    the page in memory corresponding to `pageId`
 * @param[in] slot    the slot ID to delete
 */
StatusInter BTreeDeleteEntry(BTreeCursorT *cursor, PageIdT pageId, uint8_t *page, int16_t slot)
{
    BTreeNodeHdrT *nodeHdr = BTreeGetNodeHdr(page);
    DB_ASSERT(0 <= slot && slot < nodeHdr->numSlots);
    BTreeEntryHdrT *entryHdr = NULL;
    StatusInter ret = BTreeGetEntryHdr(page, slot, &entryHdr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: get entryHdr");
        return ret;
    }

    ret = BTreeDeleteBigKey(cursor, entryHdr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: delete big key, slotId: %" PRId16 "", slot);
        return ret;
    }

    // the size of the entry to delete
    const size_t eraseSize = BTreeCalculateEntrySize(entryHdr->isBigKey, entryHdr->size);
    // the size of the part of the slot array that needs to be moved
    const size_t moveSize = sizeof(uint16_t) * (nodeHdr->numSlots - (slot + 1));
    (void)memmove_s(nodeHdr->slots + slot, moveSize, nodeHdr->slots + slot + 1, moveSize);
    // update the page information
    PageHeadT *pageHdr = (PageHeadT *)(void *)page;
    pageHdr->freeSize += (uint16_t)eraseSize;
    nodeHdr->numSlots -= 1;

    // update the version
    ++nodeHdr->version;

    // write redo log
    BTreeRedoForDelete(pageId, slot, cursor->redoCtx);

    // update statistics
    cursor->stat.occupiedMemorySizeChange += (uint32_t)eraseSize;
    return STATUS_OK_INTER;
}

/**
 * @brief Delete the entry pointed by `cursor`.
 * @param[in] idxCtx  the index context
 * @param[in] cursor  the cursor
 * @param[in] para    remove parameter
 */
StatusInter BTreeDeleteSlot(IndexCtxT *idxCtx, BTreeCursorT *cursor, IndexRemoveParaT *para, bool isRollback)
{
    StatusInter interErrno = STATUS_OK_INTER;

    BTreeNodeHdrT *nodeHdr = BTreeGetNodeHdr(cursor->curAddr.page);
    DB_ASSERT(0 <= cursor->slot && cursor->slot < nodeHdr->numSlots);
    BTreeEntryHdrT *entryHdr = NULL;
    interErrno = BTreeGetEntryHdr(cursor->curAddr.page, cursor->slot, &entryHdr);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: get entryHdr");
        return interErrno;
    }
    // case 1: mark deletion
    if (!para->isErase && !para->isGc) {
        if (!entryHdr->isDeleted) {
            // write undo log
            interErrno = BTreeWriteUndoDelete(idxCtx, cursor, isRollback);
            if (interErrno == STATUS_OK_INTER) {
                entryHdr->isDeleted = 1;
                // write redo log
                BTreeRedoForMarkDelete(cursor->curAddr.pageId, cursor->slot, cursor->redoCtx);
            }
        }
    } else if (para->isErase || entryHdr->isDeleted) {
        // case 2: (isErase) or (isGc and the entry is mark deleted), should erase the entry
        // these cases are in the commit phase, thus should not write undo log
        interErrno = BTreeDeleteEntry(cursor, cursor->curAddr.pageId, cursor->curAddr.page, cursor->slot);
        // update statistics
        cursor->stat.recordCountChange = 1;
    }
    return interErrno;
}

/**
 * @brief Update the statistics according to `cursor`.
 * @param[in] cursor  the cursor
 * @param[in] metaPageId  the page ID of the meta page (for redo logging)
 * @param[in,out] meta the meta information
 */
void BTreeDeleteUpdateStat(const BTreeCursorT *cursor, const PageIdT metaPageId, BTreeT *meta)
{
    DB_ASSERT(meta->stat.bTreeIndex.recordCount >= cursor->stat.recordCountChange);  // for debug only
    meta->stat.bTreeIndex.recordCount -= cursor->stat.recordCountChange;
    DB_ASSERT(meta->stat.bTreeIndex.treeHeight > cursor->stat.treeHeightChange);  // for debug only
    meta->stat.bTreeIndex.treeHeight -= cursor->stat.treeHeightChange;

    BTreeMemStatT *memStat = &meta->stat.bTreeIndex.memStat;
    memStat->totalNodeCount -= cursor->stat.totalNodeCountChange;
    memStat->leafCount -= cursor->stat.leafCountChange;
    memStat->internalNodeCount -= cursor->stat.internalNodeCountChange;
    memStat->totalMemorySize = memStat->pageSize * (uint64_t)memStat->totalNodeCount;
    memStat->occupiedMemorySize -= cursor->stat.occupiedMemorySizeChange;
    memStat->bigKeyPageCount += cursor->stat.bigKeyPageCountChange;
    // the following ASSERTs are for debug only
    DB_ASSERT(memStat->totalNodeCount == memStat->leafCount + memStat->internalNodeCount);

    BTreePerfStatT *perfStat = &meta->stat.bTreeIndex.perfStat;
    // if the control flow enters this function, that means the deletion is successful
    perfStat->deleteCount += 1;
    perfStat->freeCount += cursor->stat.freeCountChange;
    perfStat->coalesceCount += cursor->stat.coalesceCountChange;

    BTreeRedoForUpdateStatistics(metaPageId, &meta->stat.bTreeIndex, cursor->redoCtx);
}

/**
 * @brief Delete the given key.
 * @param[in] idxCtx     the index runtime context
 * @param[in] deleteKey  the key to delete
 * @param[in] rowId      the heap tuple ID
 * @param[in] para       `para->isErase` means physical deletion and `para->isGc` means physical deletion
 *                       of a mark-deleted entry; if both of them are false, mark deletion is used.
 */
SO_EXPORT Status BTreeIndexDelete(IndexCtxT *idxCtx, IndexKeyT deleteKey, HpTupleAddr rowId, IndexRemoveParaT para)
{
    DB_POINTER(idxCtx);
    // this case should never happen; otherwise, it is a bug
    if (rowId == HEAP_INVALID_ADDR) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "BTree: check rowId unable.");
        return GMERR_INTERNAL_ERROR;
    }
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    // GC情况下为事务调用，调用层已开启原子操作，此处不允许再开，其他情况则需要正常开启
    if (redoCtx == NULL || para.isGc) {
        return BTreeDelete(idxCtx, deleteKey, rowId, para, false);
    }
    // start atomic operation
    RedoLogBegin(redoCtx);
    Status ret = BTreeDelete(idxCtx, deleteKey, rowId, para, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "BTree: delete key, rowId:%" PRIu64, rowId);
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
}

SO_EXPORT Status BTreeIndexUndoInsert(IndexCtxT *idxCtx, IndexKeyT key, HpTupleAddr rowId)
{
    DB_POINTER2(idxCtx, key.keyData);
    BTreeIndexCtxT *treeCtx = (BTreeIndexCtxT *)(void *)idxCtx->idxRunCtx;
    IndexRemoveParaT para = {.isErase = !treeCtx->markDel, .isGc = false};
    return BTreeDelete(idxCtx, key, rowId, para, true);
}

/**
 * @brief Get the number of keys.
 * @param[in] idxCtx    the index runtime context
 * @param[in] countKey  the key to count
 * @param[out] count    the number of visible `countKey` in the index
 */
SO_EXPORT Status BTreeIndexGetKeyCount(IndexCtxT *idxCtx, IndexKeyT countKey, uint64_t *count)
{
    DB_POINTER2(idxCtx, count);
    // we get the count by scan
    uint32_t propeNum = BTreeGetIndexPropeNum(idxCtx->idxOpenCfg.vlIndexLabel, idxCtx->idxMetaCfg.keyDataType);
    countKey.prefixPropeNum = propeNum;
    IndexScanCfgT scanCfg = {0};
    scanCfg.leftKey = &countKey;
    scanCfg.rightKey = &countKey;
    scanCfg.scanDirect = INDEX_SCAN_ASCEND;
    scanCfg.scanType = INDEX_RANGE_CLOSED;

    // begin the scan
    IndexScanItrT scanItr = NULL;
    Status ret = BTreeIndexBeginScan(idxCtx, scanCfg, &scanItr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Begin scan when getKey count.");
        return ret;
    }

    uint64_t rowId = HEAP_INVALID_ADDR;
    bool found = true;
    uint64_t localCount = 0;

    // scan the tree
    while (found) {
        ret = BTreeIndexScan(idxCtx, scanItr, &rowId, &found);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Scan next key.");
            return ret;
        }
        if (found) {
            DB_LOG_DBG_DEBUG("BTree: KeyCount: rowId = %" PRIu64, rowId);
            ++localCount;
        }
    }

    // set the final result
    *count = localCount;

    return GMERR_OK;
}

/**
 * @brief Get the statistics of the tree.
 * @param[in] idxShmAddr   the address of the index (meta page)
 * @param[out] idxStat  the index statistics
 */
SO_EXPORT Status BTreeIndexStatView(ShmemPtrT idxShmAddr, uint16_t instanceId, IndexStatisticsT *idxStat)
{
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(instanceId);
    if (seInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "BTree: null seIns.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    const PageIdT metaPageId = *(PageIdT *)(&(idxShmAddr));
    PageMgrT *pageMgr = SeGetPageMgrByPageId(seInstance, metaPageId);
    if (pageMgr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "BTree: null pageMgr.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    // read the meta page
    uint8_t *metaPage = NULL;
    StatusInter ret = BTreeGetPage(pageMgr, metaPageId, HIGHEST_PRIORITY_PAGE, &metaPage, false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "GetPage, get statistics of btree.");
        return DbGetExternalErrno(ret);
    }
    DbRWLatchR(&((PageHeadT *)(void *)metaPage)->lock);
    // get the statistics
    *idxStat = BTreeGetTreeHdr(metaPage)->stat;
    DbRWUnlatchR(&((PageHeadT *)(void *)metaPage)->lock);
    SeLeavePage(pageMgr, metaPageId, false);
    return GMERR_OK;
}

/**
 * @brief Get the size of all pages of the tree.
 * @param[in] idxShmAddr    the address of the index (meta page)
 * @param[out] pageSize  the size of all pages
 */
SO_EXPORT Status BTreeIndexGetPageSize(ShmemPtrT idxShmAddr, uint16_t instanceId, uint64_t *pageSize)
{
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(instanceId);
    if (seInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "BTree: null seIns.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    const PageIdT metaPageId = *(PageIdT *)(&(idxShmAddr));
    PageMgrT *pageMgr = SeGetPageMgrByPageId(seInstance, metaPageId);
    if (pageMgr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "BTree: null pageMgr.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    // read the meta page
    uint8_t *metaPage = NULL;
    StatusInter ret = BTreeGetPage(pageMgr, metaPageId, HIGHEST_PRIORITY_PAGE, &metaPage, false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "BTree: getPage");
        return DbGetExternalErrno(ret);
    }
    DbRWLatchR(&((PageHeadT *)(void *)metaPage)->lock);
    // get the page size
    *pageSize = BTreeGetTreeHdr(metaPage)->stat.bTreeIndex.memStat.totalMemorySize;
    DbRWUnlatchR(&((PageHeadT *)(void *)metaPage)->lock);
    SeLeavePage(pageMgr, metaPageId, false);
    return GMERR_OK;
}

SO_EXPORT Status BTreeIndexScaleIn(IndexCtxT *ctx, IndexScaleInCfgT *idxScaleCfg)
{
    DB_UNUSED(ctx);
    DB_UNUSED(idxScaleCfg);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

/**
 * @brief Give an estimation about storage size.
 * @param[in] cfg     the configuration
 * @param[in] count   number of keys
 * @param[in] keyLen  key size
 * @param[out] size   the estimated size
 */
SO_EXPORT Status BTreeIndexGetEstimateMemSize(
    uint16_t instanceId, IndexMetaCfgT cfg, uint64_t count, uint32_t keyLen, uint64_t *size)
{
    DB_POINTER(size);
    DB_UNUSED(cfg);

    SeInstanceT *seInstance = SeGetInstance(instanceId);
    if (seInstance == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "BTree: null instance.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    const uint32_t pageSize = (uint32_t)seInstance->seConfig.pageSize * DB_KIBI;
    uint8_t isBigKey = BTreeCheckLenIsBigKey(pageSize, keyLen) ? 1 : 0;
    const uint32_t entrySize = (uint32_t)BTreeCalculateEntrySize(isBigKey, keyLen);
    // number of entries per page, assuming that each page is half-full
    const uint32_t numPerPage = (uint32_t)((pageSize - (sizeof(PageHeadT) + sizeof(BTreeNodeHdrT))) / (entrySize * 2));
    // number of leaves
    const uint64_t numOfLeaves = count / numPerPage;

    uint64_t totalNumOfNodes = 0;
    uint64_t numOfNodesInLevel = numOfLeaves;
    while (numOfNodesInLevel > 0) {
        totalNumOfNodes += numOfNodesInLevel;
        numOfNodesInLevel /= numPerPage;  // the estimated # of nodes in next higher level
    }
    totalNumOfNodes += 1;  // plus the root

    *size = totalNumOfNodes * pageSize;

    return GMERR_OK;
}

SO_EXPORT Status BTreeIndexBatchLookup(
    IndexCtxT *idxCtx, IndexKeyT searchKeys[], uint32_t batchNum, IdxBatchLookupParaT *para)
{
    DB_POINTER4(idxCtx, searchKeys, para, para->categorizeFunc);

    HpTupleAddr rowId;
    bool found = false;

    for (uint32_t i = 0; i < batchNum; ++i) {
        // `BTreeIndexLookup` always returns `GMERR_OK`
        (void)BTreeIndexLookup(idxCtx, searchKeys[i], &rowId, &found);
        // categorize
        Status ret = para->categorizeFunc(
            idxCtx, para->iter, TupleAddr2IdxTupleOrIter(rowId), i, found ? IDX_IS_TUPLE_ADDR : IDX_IS_NOT_FOUND);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "BTree: categorize.");
            return ret;
        }
    }

    return GMERR_OK;
}

SO_EXPORT Status BTreeIndexBatchInsert(IndexCtxT *idxCtx, IndexKeyT keys[], HpBatchOutT addrs[], uint32_t batchNum)
{
    DB_POINTER3(idxCtx, keys, addrs);
    for (uint32_t i = 0; i < batchNum; ++i) {
        Status ret = BTreeIndexInsert(idxCtx, keys[i], addrs[i].addrOut);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "BTree: insert, index: %" PRIu32, i);
            return ret;
        }
    }
    return GMERR_OK;
}

SO_EXPORT Status BTreeIndexBatchDelete(
    IndexCtxT *idxCtx, IndexKeyT keys[], HpBatchOutT addrs[], uint32_t batchNum, IndexRemoveParaT para)
{
    DB_POINTER3(idxCtx, keys, addrs);
    for (uint32_t i = 0; i < batchNum; ++i) {
        Status ret = BTreeIndexDelete(idxCtx, keys[i], addrs[i].addrOut, para);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "BTree: delete, index: %" PRIu32, i);
            return ret;
        }
    }
    return GMERR_OK;
}

SO_EXPORT Status BTreeIndexBatchUpdate(
    IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo[], uint32_t batchNum, IndexRemoveParaT removePara)
{
    DB_POINTER2(idxCtx, updateInfo);
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < batchNum; ++i) {
        ret = BTreeIndexDelete(idxCtx, updateInfo[i].oldIdxKey, updateInfo[i].oldAddr, removePara);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "BTree: Unable to delete when batch update, index: %" PRIu32 ".", i);
            return ret;
        }
        ret = BTreeIndexInsert(idxCtx, updateInfo[i].newIdxKey, updateInfo[i].newAddr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "BTree: Unable to insert when batch update, index: %" PRIu32 ".", i);
            return ret;
        }
    }
    return ret;
}
#ifdef FEATURE_SQL
typedef struct BtreeExtendKeyCtx {
    uint32_t offset;
    uint8_t *value;
    uint32_t len;
} BtreeExtendKeyCtxT;

static Status BTreeExtendKeyCmp(
    IndexCtxT *idxCtx, BTreeCursorT *cursor, int16_t slot, DmIndexKeyCmpInfoT *cmpInfo, BtreeExtendKeyCtxT *keyCtx)
{
    BTreeKeyT *key = &cursor->key;
    BTreeKeyT cmpKey = {0};
    StatusInter ret = BTreeGetKey(idxCtx, cursor->curAddr.page, slot, key->prefixPropeNum, false, &cmpKey);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret,
            "BTree: unable to get key when cmp entend key, pageid: (%" PRIu32 ", %" PRIu32 "), slot: %" PRId16 "",
            cursor->curAddr.pageId.deviceId, cursor->curAddr.pageId.blockId, slot);
        return DbGetExternalErrno(ret);
    }

    BTreeCmpKeysInner(key, &cmpKey, cmpInfo);
    if (cmpInfo->cmpResult == DM_INDEX_KEY_EQ) {
        cmpInfo->rowId = cmpKey.rowId;
        cmpInfo->found = true;
        Status ret2 = memcpy_s(cmpKey.keyData + (cmpKey.keySize - idxCtx->extendedKeySize + keyCtx->offset),
            idxCtx->extendedKeySize - keyCtx->offset, keyCtx->value, keyCtx->len);
        if (ret2 != GMERR_OK) {
            return ret2;
        }
    } else {
        cmpInfo->outOfRange = true;
    }

    return GMERR_OK;
}

static Status BTreeExtendKeySearch(
    IndexCtxT *idxCtx, BTreeCursorT *cursor, DmIndexKeyCmpInfoT *cmpInfo, BtreeExtendKeyCtxT *keyCtx)
{
    Status ret = GMERR_OK;
    uint8_t level = BTreeGetNodeHdr(cursor->curAddr.page)->level;
    BTreePageRUnlatch(idxCtx, cursor->curAddr.page);
    SeLeavePage(cursor->pageMgr, cursor->curAddr.pageId, false);

    StatusInter interErrno = BTreeGetPage(cursor->pageMgr, cursor->curAddr.pageId, level, &cursor->curAddr.page, true);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: GetPage when latch");
        return DbGetExternalErrno(interErrno);
    }
    BTreePageWLatch(idxCtx, cursor->curAddr.page);

    int16_t numSlots = BTreeGetNodeHdr(cursor->curAddr.page)->numSlots;
    for (int16_t i = cursor->slot; i < numSlots; i++) {
        ret = BTreeExtendKeyCmp(idxCtx, cursor, i, cmpInfo, keyCtx);
        if (BTreeIsCmpEof(cmpInfo, ret)) {
            cmpInfo->cmpEof = true;
            goto EXIT_SET_EXTENDKEY;
        }
    }

EXIT_SET_EXTENDKEY:
    BTreePageWUnlatch(idxCtx, cursor->curAddr.page);
    SeLeavePage(cursor->pageMgr, cursor->curAddr.pageId, true);
    interErrno = BTreeGetPage(cursor->pageMgr, cursor->curAddr.pageId, level, &cursor->curAddr.page, false);
    if (interErrno != STATUS_OK_INTER) {
        SE_ERROR(interErrno, "BTree: GetPage when exit");
        return DbGetExternalErrno(interErrno);
    }
    BTreePageRLatch(idxCtx, cursor->curAddr.page);
    return ret;
}

/**
 * @brief 找到key的位置，在后方写入拓展的内容.
 * @param[in] idxCtx     index runtime context
 * @param[in] key        the key to set
 * @param[out] offset    offset of extend area
 * @param[out] value     value
 * @param[out] len       value len
 */
Status BTreeSetExtendKey(IndexCtxT *idxCtx, IndexKeyT key, uint32_t offset, uint8_t *value, uint32_t len)
{
    BTreeCursorT cursor;
    BTreeCursorSet(&cursor, idxCtx, key, CURSOR_LOOKUP);
    cursor.key.rowId = (idxCtx->indexAddrInfo == 0 ? BTREE_KEY_ROW_ID_NO_COMPARE : idxCtx->indexAddrInfo);
    Status ret = GMERR_LOCK_NOT_AVAILABLE;
    uint32_t lockTryCnt = 0;
    BtreeExtendKeyCtxT keyCtx = {.offset = offset, .value = value, .len = len};
    bool isFound = false;
    uint64_t begTime = DbRdtsc();
    do {
        const PageIdT metaPageId = *(PageIdT *)(&idxCtx->idxShmAddr);
        uint8_t *metaPage = NULL;
        StatusInter retInter = BTreeGetPage(cursor.pageMgr, metaPageId, HIGHEST_PRIORITY_PAGE, &metaPage, false);
        if (retInter != STATUS_OK_INTER) {
            SE_ERROR(retInter, "BTree: unable to getPage when search and leave page.");
            return DbGetExternalErrno(retInter);
        }
        BTreeT *meta = BTreeGetTreeHdr(metaPage);
        BTreeIndexRLatch(idxCtx, meta);
        DmIndexKeyCmpInfoT cmpInfo = BTreeGetInitKeyCmpInfo(&cursor, false);
        retInter = BTreeOptLatchCoupling(&cursor, false);
        if (retInter != STATUS_OK_INTER) {
            SE_ERROR(retInter, "BTree: unable to optLatch coupling when search and leave page.");
            BTreeIndexRUnlatch(idxCtx, meta);
            SeLeavePage(cursor.pageMgr, metaPageId, false);
            return DbGetExternalErrno(retInter);
        }
        while (cursor.state == CURSOR_VALID) {
            ret = BTreeExtendKeySearch(idxCtx, &cursor, &cmpInfo, &keyCtx);
            if (ret != GMERR_OK) {
                BTreeCursorLeavePage(&cursor, false);
                break;
            }
            if (cmpInfo.cmpEof) {
                BTreeCursorLeavePage(&cursor, false);
                isFound = cmpInfo.found;
                break;
            }
            ret = DbGetExternalErrno(BTreeCursorNextLeaf4ExtendKey(&cursor, false));
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "BTree: unable move cursor to next leaf when search and leave page.");
                BTreeCursorLeavePage(&cursor, false);
                break;
            }
        }
        BTreeIndexRUnlatch(idxCtx, meta);
        SeLeavePage(cursor.pageMgr, metaPageId, false);
    } while (IdxContinueTry(idxCtx->idxOpenCfg.seRunCtx, &lockTryCnt, ret, begTime));
    return isFound ? ret : GMERR_INVALID_VALUE;
}
#endif
