/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: se_index_page_mgr.h
 * Description: public interface of idx page cache
 * Author:
 * Create: 2022-6-13
 */

#ifndef SE_INDEX_PAGE_MGR_H
#define SE_INDEX_PAGE_MGR_H

#include "adpt_types.h"
#include "db_resource_session_pub.h"
#include "db_internal_error.h"
#include "se_index_inner.h"
#include "se_page.h"

#ifdef __cplusplus
extern "C" {
#endif

#define IDX_PAGE_CACHE_COUNT 8u
#define EH_DIR_AND_SEG_PAGE 2u

typedef struct IdxPageCacheItem {
    DbMemAddrT pageInfo;
    uint32_t weight;
    uint32_t pageId;
} IdxPageCacheItemT;

typedef struct IdxPageCache {
    uint64_t version;
    uint32_t itemNum;
    uint32_t reserved;
    IdxPageCacheItemT items[IDX_PAGE_CACHE_COUNT];
} IdxPageCacheT;

typedef struct EhTrcPageCache {
    PageIdT pageId[EH_DIR_AND_SEG_PAGE];
    uint32_t cnt;
} EhTrcPageCacheT;

#define EH_TRC_PAGE_CACHE_INIT_VALUE ((EhTrcPageCacheT){{SE_INVALID_PAGE_ADDR, SE_INVALID_PAGE_ADDR}, 0})

void IdxPageCacheInit(IdxPageCacheT *pageCache, uint64_t version);

void IdxPageCacheCheck(IdxPageCacheT *pageCache, uint64_t version);

DbMemAddrT IdxPageCacheFindPage(IdxPageCacheT *pageCache, uint32_t pageId, uint64_t version);

void IdxPageCacheAdd(IdxPageCacheT *pageCache, uint32_t pageId, uint8_t *page, PageIdT pageAddr);

void IdxPageCacheReplace(IdxPageCacheT *pageCache, uint32_t pageId, uint8_t *page, PageIdT pageAddr);

typedef PageIdT IdxCachePageAddrT;
// 为确保truncate操作一定成功，缓存一些重新初始化需要用的页，确保申请页能成功
typedef struct IdxTruncatePageCache {
    uint32_t capacity;  // 缓存页最大个数
    uint32_t size;      // 当前缓存页的个数
    ShmemPtrT pagesAddr;
} IdxTruncatePageCacheT;

#ifdef __cplusplus
}
#endif

#endif
