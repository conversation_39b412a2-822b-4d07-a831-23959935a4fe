/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: se_index_common.h
 * Description: common function of index
 * Author: qipengcheng
 * Create: 2021-11-09
 */

#ifndef SE_INDEX_COMMON_H
#define SE_INDEX_COMMON_H

#include "se_define.h"
#include "adpt_sleep.h"
#include "se_index_inner.h"
#include "se_undo_pub.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct IdxNodePtr {
    uint16_t blockId : 15;
    uint16_t isDeleted : 1;
    uint16_t offset;  // page must not bigger than 64 KB
} IdxNodePtrT;

/* 索引通过heap访问时获取事务锁失败 时, 需要适当地 重试 + 休眠;
 * 超时阈值同事务锁超时时间，见配置文件
 * */
#define INDEX_HEAP_TRY_TRX_LOCK_SLEEP_STEP_CNT 10u            // 休眠步长 (每 N 次失败进行一次休眠)
#define INDEX_HEAP_TRY_TRX_LOCK_SLEEP_US SPIN_MIN_DELAY_USEC  // 每次休眠时间 500 us (参考自旋锁休眠时间)
#define SE_INDEX_INCLUDED_PAR_NUM 4

typedef struct IsIncludePara {
    bool isLeftIncluded;
    bool isRightIncluded;
} IsIncludeParaT;

typedef struct ContinueTryLockMethod {
    void (*continueTryLock)(const IndexCtxT *idxCtx);
    void (*continueTryUnlock)(const IndexCtxT *idxCtx);
} ContinueTryLockMethodT;

extern IsIncludeParaT g_gmdbIdxIsIncludePara[SE_INDEX_INCLUDED_PAR_NUM];

inline static bool IndexIsLabelLatchMode(const IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    return (bool)idxCtx->idxMetaCfg.isLabelLatchMode;
}

inline static void IdxRWLatchWLLBase(const IndexCtxT *idxCtx)
{
    bool isLabelLatchMode = IndexIsLabelLatchMode(idxCtx);
    DbRWLatchWLL(isLabelLatchMode, &idxCtx->idxHandle->idxLatch);
}

static void IdxRWLatchWLL(const IndexCtxT *idxCtx)
{
    if (idxCtx->batchLocked) {
        return;
    }
    IdxRWLatchWLLBase(idxCtx);
}

inline static void IdxRWUnlatchWLLBase(const IndexCtxT *idxCtx)
{
    bool isLabelLatchMode = IndexIsLabelLatchMode(idxCtx);
    DbRWUnlatchWLL(isLabelLatchMode, &idxCtx->idxHandle->idxLatch);
}

static void IdxRWUnlatchWLL(const IndexCtxT *idxCtx)
{
    if (idxCtx->batchLocked) {
        return;
    }
    IdxRWUnlatchWLLBase(idxCtx);
}

#define AUTO_INDEX_RW_LATCH_WLL(idxCtx, op) \
    do {                                    \
        IdxRWLatchWLL(idxCtx);              \
        op;                                 \
        IdxRWUnlatchWLL(idxCtx);            \
    } while (0)

inline static void IdxBatchBegin(IndexCtxT *idxCtx)
{
    IdxRWLatchWLL(idxCtx);
    idxCtx->batchWriteLock = true;  // 上面上了写锁
    idxCtx->batchLocked = true;     // 设置为true后，索引锁操作均无效
}

inline static void IdxBatchEnd(IndexCtxT *idxCtx)
{
    idxCtx->batchLocked = false;  // 设置为false后，索引锁操作均正常
    idxCtx->batchWriteLock = false;
    IdxRWUnlatchWLL(idxCtx);
}

bool IdxContinueTryBatched(
    IndexCtxT *idxCtx, uint32_t *lockConflictTryCnt, Status ret, uint64_t begin, ContinueTryLockMethodT lockMethod);

bool IdxWLockIfNotConstructed(IdxBaseT *idxBase);

SO_EXPORT_FOR_HAC bool IdxExceedSplitTime(const IndexScaleInCfgT *idxScaleCfg);

StatusInter IndexLogDDLUndo(
    SeRunCtxT *seRunCtx, IndexMetaCfgT *cfg, PageIdT metaAddr, UndoTrxResTypeE trxRes, UndoRowOpTypeE opType);
Status IndexMarkDrop(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr);
Status IndexMarkTruncate(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr);

#ifdef __cplusplus
}
#endif
#endif  // __SE_INDEX_COMMON_H__
