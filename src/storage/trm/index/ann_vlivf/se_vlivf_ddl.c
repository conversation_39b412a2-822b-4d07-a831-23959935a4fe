/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: implementation of vlivf ddl
 * Author:
 * Create: 2024-9-14
 */

#include "se_vlivf_base.h"
#include "se_vlivf_page.h"
#include "se_vlivf_cluster.h"

static inline void VlIvfInitCentNum(
    VlIvfCentInfoT *centInfo, uint8_t nLevel, uint32_t *nCent, uint32_t pageSize, uint32_t tupleSize)
{
    centInfo->nLevel = nLevel;
    for (uint8_t i = 0; i < nLevel; i++) {
        centInfo->nCent[i] = nCent[i];
    }
}

static void VlIvfInitLoadProc(VlIvfLoadProcT *loadProc)
{
    loadProc->path.level = 0;
    loadProc->path.clusPath[0] = 0;
    loadProc->buildFinish = false;
}

static inline uint32_t VlIvfTupleSize(
    uint32_t dim, DbVectorQuantTypeE quantType, uint8_t nCodebits, uint8_t nullInfoBytes)
{
    uint32_t size = sizeof(VlIvfTupleT) + nullInfoBytes;
    if (quantType == (uint8_t)DB_QUANT_TYPE_LVQ) {
        size += DbLVQCodeSize(dim, nCodebits);
    } else {
        size += dim * sizeof(float);
    }
    return VlIvfAlign(size);
}

StatusInter VlIvfAllocMetaPage(VlIvfClusMgrT *clusMgr, SeInstanceT *seIns, IndexMetaCfgT *idxCfg,
    VlIvfIdxMetaPageT **pmetaPage, PageIdT *pmetaPageId)
{
    DmVlIvfIndexInfoT *vlivfInfo = (DmVlIvfIndexInfoT *)idxCfg->extendParam;
    uint32_t pageSize = seIns->seConfig.pageSize * DB_KIBI;
    uint32_t tupleSize =
        VlIvfTupleSize(vlivfInfo->dim, vlivfInfo->quantType, vlivfInfo->nCodeBits, idxCfg->nullInfoBytes);
    uint32_t trmId = SeGetNewTrmId(seIns);
    PageIdT metaPageId = SE_INVALID_PAGE_ADDR;
    VlIvfAllocPageParaT para = {
        .tableSpaceIndex = idxCfg->tableSpaceIndex, .trmId = trmId, .pageType = VLIVF_META_PAGE};
    PageHeadT *pageHead = NULL;
    StatusInter interErrno = VlIvfAllocAndGetPage(clusMgr, &para, &metaPageId, &pageHead);
    if (interErrno != STATUS_OK_INTER) {
        return interErrno;
    }
    VlIvfIdxMetaPageT *metaPage = (VlIvfIdxMetaPageT *)pageHead;
    DbRWLatchInit(&metaPage->idxBase.idxLatch);
    metaPage->idxBase.indexCfg = *idxCfg;
    metaPage->trmId = trmId;
    metaPage->magicNumber = 0;
    metaPage->version = 0;
    metaPage->pageSize = pageSize;
    metaPage->nTuplePerClusEntryPage = VlIvfTupleNumPerPage(pageSize, tupleSize, true);
    metaPage->nTuplePerPage = VlIvfTupleNumPerPage(pageSize, tupleSize, false);
    VlIvfInitCentNum(&metaPage->centInfo, vlivfInfo->nlevel, vlivfInfo->ncentroids, pageSize, tupleSize);
    metaPage->vecInfo.itemSize = tupleSize;
    metaPage->vecInfo.dim = vlivfInfo->dim;
    metaPage->vecInfo.metric = vlivfInfo->metric;
    metaPage->vecInfo.oodMetric = vlivfInfo->oodMetric;
    metaPage->vecInfo.quantType = vlivfInfo->quantType;
    metaPage->vecInfo.nCodeBits = vlivfInfo->nCodeBits;
    metaPage->vecInfo.payloadOffset = idxCfg->nullInfoBytes;
    metaPage->oodThreshold = vlivfInfo->oodThreshold;
    metaPage->scanRatio = vlivfInfo->scanRatio;
    metaPage->candiRatio = vlivfInfo->candiRatio;
    metaPage->oodMetric = vlivfInfo->oodMetric;
    VlIvfInitLoadProc(&metaPage->loadProc);
    *pmetaPage = metaPage;
    *pmetaPageId = metaPageId;
    return STATUS_OK_INTER;
}

StatusInter VlIvfIdxCreateCluster(VlIvfClusMgrT *clusMgr, VlIvfIdxMetaPageT *metaPage)
{
    VlIvfClusPathT path = {.level = 0};
    StatusInter interErrno = VlIvfCreateClusterRecursively(clusMgr, &path, &metaPage->entryPageL0);
    if (interErrno != STATUS_OK_INTER) {
        return interErrno;
    }

    path.level = metaPage->centInfo.nLevel;
    interErrno = VlIvfCreateClusterRecursively(clusMgr, &path, &metaPage->oodEntry);
    if (interErrno != STATUS_OK_INTER) {
        VlIvfReleaseClusterRecursively(clusMgr, metaPage->entryPageL0);
    }
    return interErrno;
}

Status VlIvfIndexCreate(SeRunCtxT *seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr)
{
    DB_POINTER2(seRunCtx, idxShmAddr);
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    DbSessionCtxT *resSessionCtx = &seRunCtx->resSessionCtx;
    if (seIns == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "SeInstance is null.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    PageMgrT *pageMgr = SeGetPageMgr(seIns->instanceId);
    if (pageMgr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Se PageMgr is null.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    PageIdT metaPageId = SE_INVALID_PAGE_ADDR;
    VlIvfIdxMetaPageT *metaPage = NULL;
    VlIvfClusMgrT clusMgr = {.resSessionCtx = resSessionCtx, .pageMgr = pageMgr};
    StatusInter interErrno = VlIvfAllocMetaPage(&clusMgr, seIns, &idxCfg, &metaPage, &metaPageId);
    if (interErrno != STATUS_OK_INTER) {
        return DbGetExternalErrno(interErrno);
    }

    VlIvfIdxInitClusMgr(&clusMgr, pageMgr, seIns->seServerMemCtx, metaPage, resSessionCtx);
    interErrno = VlIvfIdxCreateCluster(&clusMgr, metaPage);
    if (interErrno != STATUS_OK_INTER) {
        VlIvfLeavePage(resSessionCtx, pageMgr, metaPageId, &metaPage->pageHead, true, true);
        VlIvfFreePage(pageMgr, idxCfg.tableSpaceIndex, metaPageId, seIns->dbInstance);
        return DbGetExternalErrno(interErrno);
    }

    VlIvfLeavePage(resSessionCtx, pageMgr, metaPageId, &metaPage->pageHead, true, true);
    *idxShmAddr = *(ShmemPtrT *)(&metaPageId);
    return GMERR_OK;
}

Status VlIvfIndexDrop(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    DB_POINTER(seRunCtx);
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    DbSessionCtxT *resSessionCtx = &seRunCtx->resSessionCtx;
    PageMgrT *pageMgr = SeGetPageMgr(seIns->instanceId);
    PageIdT metaPageId = *(PageIdT *)(&idxShmAddr);
    VlIvfIdxMetaPageT *metaPage = NULL;
    StatusInter interErrno = VlIvfGetMetaPage(resSessionCtx, pageMgr, metaPageId, &metaPage, false);
    if (interErrno != STATUS_OK_INTER) {
        DB_LOG_AND_SET_LASERR(DbGetExternalErrno(interErrno), "Get meta page go wrong when drop index.");
        return DbGetExternalErrno(interErrno);
    }
    VlIvfClusMgrT clusMgr;
    VlIvfIdxInitClusMgr(&clusMgr, pageMgr, seIns->seServerMemCtx, metaPage, resSessionCtx);
    interErrno = VlIvfReleaseClusterRecursively(&clusMgr, metaPage->entryPageL0);
    if (interErrno != STATUS_OK_INTER) {
        DB_LOG_AND_SET_LASERR(DbGetExternalErrno(interErrno), "Release cluster go wrong when drop index.");
        return DbGetExternalErrno(interErrno);
    }
    interErrno = VlIvfReleaseClusterRecursively(&clusMgr, metaPage->oodEntry);
    if (interErrno != STATUS_OK_INTER) {
        DB_LOG_AND_SET_LASERR(DbGetExternalErrno(interErrno), "Release ood cluster go wrong when drop index.");
        return DbGetExternalErrno(interErrno);
    }
    uint32_t tableSpaceIndex = metaPage->idxBase.indexCfg.tableSpaceIndex;
    VlIvfLeavePage(resSessionCtx, pageMgr, metaPageId, &metaPage->pageHead, false, false);
    VlIvfFreePage(pageMgr, tableSpaceIndex, metaPageId, seIns->dbInstance);
    return STATUS_OK_INTER;
}
