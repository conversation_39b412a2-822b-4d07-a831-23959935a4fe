/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: se_hash_mem.h
 * Description: hash index mem interfaces
 * Author: pengfengbin  tanglu
 * Create: 2020/12/18
 */

#ifndef SE_HASH_MEM_H
#define SE_HASH_MEM_H

#include "se_index_page_mgr.h"
#include "se_index_common.h"
#include "se_memdata.h"
#include "se_page_mgr.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SWIZZLE_SHMARRAY_CAPACITY 20  // every extent can record about 40k pageDescs i.e. 40k pages
#define HASH_PAGE_HEAD_SIZE (sizeof(PageHeadT))
#define HASHLINKLIST_FREELIST_COUNT 2  // FREELIST COUNT for recycle memory
#define HASH_INDEX_TIMEOUT 10u         // unit:s

typedef struct HashDirectoryHead {
    uint32_t dirDepth;  // directory depth to indicate how many bits hashcode are used in directory
    uint32_t dirCap;    // diretory capacity of segments
} HashDirectoryHeadT;

// page 0 reserve for hashtable metadata
typedef struct CcehMemMeta {
    uint32_t dirPageCount;  // dir占用的page数目 for unique index (need update in use for freepage in drop)
    uint32_t segPageCount;  // entry占用的page数目 for unique index (need update in use for freepage in drop)
    uint32_t hashSegBits;
    uint32_t hashEntryNumPerPage;
    uint32_t hashSegNumPerPage;
    HashDirectoryHeadT dir;
    uint32_t scaleInCnt;      // 缩容次数，每1s成功缩容一次，约需要136年才会达到计数上限值
    uint32_t dirNextPageId;   // 遍历dir过程中，上一次遍历结束时的dir所在的页id
    uint32_t dirNextItemIdx;  // 遍历dir page过程中，上一次遍历结束时的dir的idx
    uint32_t hashEntriesPerCacheline;
    uint32_t hashEntrySize;
    uint32_t hashEntryMetaSize;
    uint32_t hashMaxProbeLen;
} CcehMemMetaT;

typedef struct HashLinklistMeta {
    uint32_t groupCnt;          // number of group
    uint32_t maxGroupLen;       // max length of group
    uint32_t bucketNumPerPage;  // number of bucket per page
    uint32_t bucketNum;         // number of bucket
    uint32_t bucketPageNum;     // number of bucket page, bucket page id [0, bucketPageNum)
    uint32_t usedMemSize;       // the size of used memory
    uint32_t pageUsedCount;     // available page id to insert new node (need update in use for freepage in drop)
    IdxNodePtrT nodePtr[HASHLINKLIST_FREELIST_COUNT];
} HashLinklistMemMetaT;

typedef struct HashMemMgrBase {
    DbShmArrayT hashSwizzleArray;
    uint64_t version;
    bool isVertexUseRsm;
    int32_t seInstanceId;
    uint32_t fileId;
    uint32_t tableSpaceIndex;
} HashMemMgrBaseT;

typedef struct HashMemMgr {
    HashMemMgrBaseT base;
    DbShmArrayT hashMVPageArray;
    union {
        HashLinklistMemMetaT hllMeta;
        CcehMemMetaT ccehMeta;  // Cacheline-Conscious Extendible Hashing
    };
    bool allocFirst;  // for not unique index
} HashMemMgrT;

typedef struct HashMemRunCtx {
    HashMemMgrT *memMgr;
    MdMgrT *mdMgr;
    uint32_t pagePtrArrCapacity;
    PageIdT *pageId;
    IdxPageCacheT dirIdCache;    // dir页缓存
    IdxPageCacheT blockIdCache;  // blockId页缓存
    IdxPageCacheT pageIdCache;   // 数据页缓存
} HashMemRunCtxT;

typedef struct HashAllocPageParam {
    uint32_t tableSpaceIndex;
    bool isDir;
} HashAllocPageParamT;

Status HtAllocMemPage(HashMemRunCtxT *memRunCtx, const HashAllocPageParamT *allocParam, uint32_t *blockId,
    DbMemAddrT *pageInfo, uint32_t *pageId);
Status HtFreeMemPage(HashMemRunCtxT *memRunCtx, uint32_t pageId, uint32_t blockId, EhTrcPageCacheT *cache);
Status HtGetMemPage(HashMemRunCtxT *memRunCtx, uint32_t blockId, uint32_t pageId, bool isDir, DbMemAddrT *pageInfo);

Status HtGetDirPage(HashMemRunCtxT *memRunCtx, uint32_t blockId, DbMemAddrT *pageInfo);
Status HtGetDataPage(HashMemRunCtxT *memRunCtx, uint32_t blockId, uint32_t pageId, DbMemAddrT *pageInfo);

inline static PageHeadT *HtGetPageHead(uint8_t *segPageAddr)
{
    DB_POINTER(segPageAddr);
    return ((PageHeadT *)(void *)(segPageAddr - HASH_PAGE_HEAD_SIZE));
}

typedef struct {
    PageIdT pageId;
} HashPageDescT;

Status HtSwizzleCreate(HashMemMgrT *memMgr, uint32_t shmCtxId);
void HtSwizzleReset(HashMemRunCtxT *memRunCtx, EhTrcPageCacheT *cache);
void HashLinklistSwizzleReset(HashMemRunCtxT *memRunCtx, PageIdT *pageCache, uint32_t cnt);
void HtSwizzleDestroy(HashMemRunCtxT *memRunCtx, DbInstanceHdT dbInstance);
void HashLinklistSwizzleDestroy(HashMemRunCtxT *memRunCtx, DbInstanceHdT dbInstance);
void HashArrayDirScaleIn(HashMemRunCtxT *memRunCtx, uint32_t dirBeginId);

inline static Status HashMemRunCtxInit(
    HashMemMgrT *memMgr, ShmemPtrT pageAddr, uint32_t dirPageCaps, HashMemRunCtxT *memRunCtx)
{
    DB_POINTER2(memMgr, memRunCtx);
    memRunCtx->memMgr = memMgr;
    memRunCtx->pageId = (PageIdT *)DbShmPtrToAddr(pageAddr);
    memRunCtx->pagePtrArrCapacity = dirPageCaps;
    if (DbCommonIsServer()) {
        SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance((uint16_t)memMgr->base.seInstanceId);
        if (seInstance == NULL) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get SE instance unsucc. SE instance %" PRIu16 " novalid",
                (uint16_t)memMgr->base.seInstanceId);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        memRunCtx->mdMgr = (MdMgrT *)seInstance->mdMgr;
    } else {
        memRunCtx->mdMgr = (MdMgrT *)(void *)SeGetPageMgrByType((uint16_t)memMgr->base.seInstanceId, SE_MEMDATA);
    }
    return memRunCtx->mdMgr == NULL ? GMERR_UNEXPECTED_NULL_VALUE : GMERR_OK;
}

inline static void HashMemRunCtxUnInit(HashMemRunCtxT *memRunCtx)
{
    DB_POINTER(memRunCtx);
    memRunCtx->memMgr = NULL;
    memRunCtx->pageId = NULL;
    memRunCtx->pagePtrArrCapacity = DB_INVALID_UINT32;
    memRunCtx->mdMgr = NULL;
}

inline static Status HashMemRunCtxUpdatePageId(HashMemRunCtxT *memRunCtx, ShmemPtrT pageAddr)
{
    memRunCtx->pageId = (PageIdT *)DbShmPtrToAddr(pageAddr);
    if (memRunCtx->pageId == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE,
            "unable to update pageId with ptr:(%" PRIu32 ",%" PRIu32 ",%" PRIu64 ")", pageAddr.segId, pageAddr.offset,
            *(uint64_t *)(void *)&pageAddr);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return GMERR_OK;
}

Status HtFreePage(HashMemRunCtxT *memRunCtx, PageIdT pageAddr);

/* api for multiversion page in PCC_RR scene begin */
// 创建multiversion page管理结构
Status HtCreateMVPageSwizzle(HashMemMgrT *memMgr, uint32_t shmCtxId);
// 销毁multiversion page管理结构
void HtDestroyMVPageSwizzle(HashMemRunCtxT *memRunCtx, DbInstanceHdT dbInstance);
// truncate multiversion page，保留管理结构
void HtTruncateMVPageSwizzle(HashMemRunCtxT *memRunCtx);
// 申请multiversion page
Status HtAllocMVMemPage(HashMemRunCtxT *memRunCtx, uint32_t tableSpaceIndex, uint16_t *blockId, DbMemAddrT *pageInfo);
// 释放multiversion page
Status HtFreeMVMemPage(HashMemRunCtxT *memRunCtx, uint16_t blockId);

/* api for multiversion page in PCC_RR scene end */

#ifdef __cplusplus
}
#endif
#endif
