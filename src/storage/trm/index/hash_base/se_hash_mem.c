/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: se_hash_mem.c
 * Description: Implementation of hash index mem
 * Author: pengfengbin  tanglu
 * Create: 2020/12/18
 */

#include "se_hash_mem.h"
#include "db_shm_array.h"
#include "se_capacity_def_inner.h"
#include "se_log.h"
#include "db_instance.h"

#ifdef __cplusplus
extern "C" {
#endif
static inline void ConvertPageInfo(DbMemAddrT *pageInfo, uint8_t *page, PageIdT pageId)
{
    DB_POINTER2(pageInfo, page);
    pageInfo->virtAddr = page + HASH_PAGE_HEAD_SIZE;
    pageInfo->pageAddr = pageId;
}

static Status HtGetDataPageByPageId(HashMemRunCtxT *memRunCtx, uint32_t pageId, DbMemAddrT *pageInfo)
{
    *pageInfo = IdxPageCacheFindPage(&memRunCtx->pageIdCache, pageId, memRunCtx->memMgr->base.version);
    if (pageInfo->virtAddr != NULL) {
        return GMERR_OK;
    }
    uint8_t *page = NULL;
    PageIdT pageAddr = DeserializePageId((PageMgrT *)memRunCtx->mdMgr, pageId);
    StatusInter ret = MdGetPage(memRunCtx->mdMgr, pageAddr, &page, ENTER_PAGE_NORMAL, false);
    if (ret == STATUS_OK_INTER) {
        ConvertPageInfo(pageInfo, page, pageAddr);
        IdxPageCacheAdd(&memRunCtx->pageIdCache, pageId, pageInfo->virtAddr, pageInfo->pageAddr);
    }
    return DbGetExternalErrno(ret);
}

static Status HtGetDataPageByBlockId(HashMemRunCtxT *memRunCtx, uint32_t blockId, DbMemAddrT *pageInfo)
{
    *pageInfo = IdxPageCacheFindPage(&memRunCtx->blockIdCache, blockId, memRunCtx->memMgr->base.version);
    if (pageInfo->virtAddr != NULL) {
        return GMERR_OK;
    }
    DbArrayAddrT itemAddr;
    HashPageDescT *item =
        (HashPageDescT *)DbShmArrayGetItemById(&memRunCtx->memMgr->base.hashSwizzleArray, blockId, &itemAddr);
    if (item == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR,
            "Hash Get Mem Page unsucc, itemId: %" PRIu32 ", shmArry count: %" PRIu32 ", used: %" PRIu32 "", blockId,
            memRunCtx->memMgr->base.hashSwizzleArray.count, memRunCtx->memMgr->base.hashSwizzleArray.used);
        return GMERR_INTERNAL_ERROR;
    }
    bool isPageIdValid = DbIsPageIdValid(item->pageId);
    DB_ASSERT(isPageIdValid);
    uint8_t *page = NULL;
    StatusInter ret = MdGetPage(memRunCtx->mdMgr, item->pageId, &page, ENTER_PAGE_NORMAL, false);
    if (ret == STATUS_OK_INTER) {
        ConvertPageInfo(pageInfo, page, item->pageId);
        IdxPageCacheAdd(&memRunCtx->blockIdCache, blockId, pageInfo->virtAddr, pageInfo->pageAddr);
        IdxPageCacheCheck(&memRunCtx->pageIdCache, memRunCtx->memMgr->base.version);
        IdxPageCacheReplace(&memRunCtx->pageIdCache,
            SerializePageId((PageMgrT *)memRunCtx->mdMgr, ((PageHeadT *)page)->addr), pageInfo->virtAddr,
            pageInfo->pageAddr);
    }
    return DbGetExternalErrno(ret);
}

inline Status HtGetDataPage(HashMemRunCtxT *memRunCtx, uint32_t blockId, uint32_t pageId, DbMemAddrT *pageInfo)
{
    return (pageId == SE_INVALID_PAGE_ID) ? HtGetDataPageByBlockId(memRunCtx, blockId, pageInfo) :
                                            HtGetDataPageByPageId(memRunCtx, pageId, pageInfo);
}

Status HtGetDirPage(HashMemRunCtxT *memRunCtx, uint32_t blockId, DbMemAddrT *pageInfo)
{
    DB_POINTER4(memRunCtx, memRunCtx->memMgr, memRunCtx->mdMgr, pageInfo);
    DB_ASSERT(blockId < memRunCtx->pagePtrArrCapacity);
    *pageInfo = IdxPageCacheFindPage(&memRunCtx->dirIdCache, blockId, memRunCtx->memMgr->base.version);
    if (pageInfo->virtAddr != NULL) {
        return GMERR_OK;
    }
    uint8_t *page = NULL;
    PageIdT pageId = memRunCtx->pageId[blockId];
    StatusInter ret = MdGetPage(memRunCtx->mdMgr, pageId, &page, ENTER_PAGE_PINNED, false);
    if (ret != STATUS_OK_INTER) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            DbGetExternalErrno(ret), "Unable to align shm addr|blockId : %" PRIu32 "|", blockId);
        return DbGetExternalErrno(ret);
    }
    ConvertPageInfo(pageInfo, page, pageId);
    IdxPageCacheAdd(&memRunCtx->dirIdCache, blockId, pageInfo->virtAddr, pageInfo->pageAddr);
    return GMERR_OK;
}

Status HtGetMemPage(HashMemRunCtxT *memRunCtx, uint32_t blockId, uint32_t pageId, bool isDir, DbMemAddrT *pageInfo)
{
    DB_POINTER4(memRunCtx, memRunCtx->memMgr, memRunCtx->mdMgr, pageInfo);
    if (isDir) {
        return HtGetDirPage(memRunCtx, blockId, pageInfo);
    }
    return HtGetDataPage(memRunCtx, blockId, pageId, pageInfo);
}

Status HtFreeMemPage(HashMemRunCtxT *memRunCtx, uint32_t pageId, uint32_t blockId, EhTrcPageCacheT *cache)
{
    DB_POINTER3(memRunCtx, memRunCtx->memMgr, memRunCtx->mdMgr);
    DbArrayAddrT itemAddr;
    HashPageDescT *item =
        (HashPageDescT *)DbShmArrayGetItemById(&memRunCtx->memMgr->base.hashSwizzleArray, blockId, &itemAddr);
    if (item == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR,
            "Hash Get Mem Page unsucc when free, itemId: %" PRIu32 ", shmArry count: %" PRIu32 ", used: %" PRIu32,
            blockId, memRunCtx->memMgr->base.hashSwizzleArray.count, memRunCtx->memMgr->base.hashSwizzleArray.used);
        return GMERR_INTERNAL_ERROR;
    }

    if (!DbIsPageIdValid(item->pageId)) {
        DB_ASSERT(cache == NULL || cache->cnt > 0);
        return GMERR_OK;
    }
    if (cache != NULL && cache->cnt < EH_DIR_AND_SEG_PAGE) {
        cache->pageId[cache->cnt++] = item->pageId;
        item->pageId = SE_INVALID_PAGE_ADDR;
        return GMERR_OK;
    }
    DB_ASSERT(cache == NULL || cache->cnt > 0);
    // reset pageId to SE_INVALID_PAGE_ID
    Status ret = HtFreePage(memRunCtx, item->pageId);
    if (ret != GMERR_OK) {
        return ret;
    }
    item->pageId = SE_INVALID_PAGE_ADDR;
    return GMERR_OK;
}

static Status FreePage(HashMemRunCtxT *memRunCtx, PageIdT pageAddr)
{
    FreePageParamT freePageParam =
        SeInitFreePageParam(memRunCtx->memMgr->base.tableSpaceIndex, pageAddr, NULL, NULL, SE_INVALID_LABEL_ID, false);
    StatusInter freeRet = MdFreePage(memRunCtx->mdMgr, &freePageParam);
    Status ret = DbGetExternalErrno(freeRet);
    if (ret == GMERR_OK) {
        memRunCtx->memMgr->base.version++;
    } else {
        DB_LOG_ERROR(ret, "Unable to free unsucc new hash page |deviceId:%" PRIu32 ", blockId:%" PRIu32 "|",
            pageAddr.deviceId, pageAddr.blockId);
    }
    return ret;
}

Status HtFreePage(HashMemRunCtxT *memRunCtx, PageIdT pageAddr)
{
    DB_POINTER2(memRunCtx->memMgr, memRunCtx->mdMgr);
    return FreePage(memRunCtx, pageAddr);
}

// just for code check
inline static void CacheMemPage(
    HashMemRunCtxT *memRunCtx, const DbMemAddrT *pageInfo, uint32_t blockId, uint32_t pageId)
{
    IdxPageCacheCheck(&memRunCtx->blockIdCache, memRunCtx->memMgr->base.version);
    IdxPageCacheAdd(&memRunCtx->blockIdCache, blockId, pageInfo->virtAddr, pageInfo->pageAddr);
    IdxPageCacheCheck(&memRunCtx->pageIdCache, memRunCtx->memMgr->base.version);
    IdxPageCacheAdd(&memRunCtx->pageIdCache, pageId, pageInfo->virtAddr, pageInfo->pageAddr);
}

static Status HtAllocNewPage(HashMemRunCtxT *memRunCtx, uint32_t itemId, uint32_t *pageId, PageHeadT **page)
{
    PageIdT pageAddr = SE_INVALID_PAGE_ADDR;
    // index不会使用到rsmUndo
    AllocPageParamT allocPageParam = SeInitAllocPageParam(
        memRunCtx->memMgr->base.tableSpaceIndex, memRunCtx->memMgr->base.fileId, RSM_INVALID_LABEL_ID, NULL, NULL);
    StatusInter status = MdAllocPage(memRunCtx->mdMgr, &allocPageParam, &pageAddr);
    if (status != STATUS_OK_INTER) {
        return DbGetExternalErrno(status);
    }
    status = MdGetPage(memRunCtx->mdMgr, pageAddr, (uint8_t **)page, ENTER_PAGE_NORMAL, false);
    if (status != STATUS_OK_INTER) {
        // AllocPage返回成功的情况下，立刻get不应该失败，如果失败就是有bug
        FreePageParamT freePageParam = SeInitFreePageParam(
            memRunCtx->memMgr->base.tableSpaceIndex, pageAddr, NULL, NULL, SE_INVALID_LABEL_ID, false);
        (void)MdFreePage(memRunCtx->mdMgr, &freePageParam);
        DB_ASSERT(0);
        return DbGetExternalErrno(status);
    }

    // todo SeSetPageType接口待持久化移之后，恢复调用（设置为data页）
    *pageId = SerializePageId((PageMgrT *)memRunCtx->mdMgr, (*page)->addr);
    return GMERR_OK;
}

static Status AllocNewPage(HashMemRunCtxT *memRunCtx, uint32_t itemId, DbMemAddrT *pageInfo, uint32_t *pageId)
{
    PageHeadT *page;
    Status ret = HtAllocNewPage(memRunCtx, itemId, pageId, &page);
    if (ret != GMERR_OK) {
        return ret;
    }
    ConvertPageInfo(pageInfo, (uint8_t *)page, page->addr);
    return GMERR_OK;
}

Status HtAllocMemPageInner(HashMemRunCtxT *memRunCtx, uint32_t itemId, DbMemAddrT *pageInfo, uint32_t *pageId)
{
    return AllocNewPage(memRunCtx, itemId, pageInfo, pageId);
}

// start from header
Status HtAllocMemPage(HashMemRunCtxT *memRunCtx, const HashAllocPageParamT *allocParam, uint32_t *blockId,
    DbMemAddrT *pageInfo, uint32_t *pageId)
{
    DB_POINTER5(memRunCtx, memRunCtx->mdMgr, memRunCtx->memMgr, blockId, pageInfo);
    DB_POINTER(allocParam);
    // get a new item from shm array for the new page desc id and addr
    uint32_t itemId = 0;
    HashPageDescT *item = NULL;
    DbArrayAddrT itemAddr;
    Status ret = GMERR_OK;
    // seg页才使用swizzleArray管理
    if (!allocParam->isDir) {
        ret = DbShmArrayGetItem(&memRunCtx->memMgr->base.hashSwizzleArray, &itemId, (void **)&item, &itemAddr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret,
                "ShmArray Get Item unsucc, blockId: %" PRIu32 ", shmArry count: %" PRIu32 ", used: %" PRIu32 "",
                *blockId, memRunCtx->memMgr->base.hashSwizzleArray.count,
                memRunCtx->memMgr->base.hashSwizzleArray.used);
            return ret;
        }
        *blockId = itemId;
    }

    uint32_t newPageId = SE_INVALID_PAGE_ID;
    ret = HtAllocMemPageInner(memRunCtx, itemId, pageInfo, &newPageId);
    if (ret != GMERR_OK) {
        if (!allocParam->isDir) {
            (void)DbShmArrayRemoveItem(&memRunCtx->memMgr->base.hashSwizzleArray, itemId);
        }
        return ret;
    }
    if (!allocParam->isDir) {
        CacheMemPage(memRunCtx, pageInfo, *blockId, newPageId);
        item->pageId = pageInfo->pageAddr;
    } else {
        DB_ASSERT(*blockId < memRunCtx->pagePtrArrCapacity);
        memRunCtx->pageId[*blockId] = pageInfo->pageAddr;
        IdxPageCacheCheck(&memRunCtx->dirIdCache, memRunCtx->memMgr->base.version);
        IdxPageCacheAdd(&memRunCtx->dirIdCache, (uint16_t)(*blockId), pageInfo->virtAddr, pageInfo->pageAddr);
    }
    if (pageId != NULL) {  // 新的seg页面，将pageId存入HashDirSegmentT项目中
        *pageId = newPageId;
    }
    bool isPageIdValid = DbIsPageIdValid(pageInfo->pageAddr);
    DB_ASSERT(isPageIdValid);
    return GMERR_OK;
}

Status HtSwizzleCreate(HashMemMgrT *memMgr, uint32_t shmCtxId)
{
    DB_POINTER(memMgr);
    memMgr->base.version = 1;  // 版本号从1开始
    DbInstanceT *dbIns = NULL;
    // seInstanceId和dbInstanceId值相同
    Status ret = DbGetInstanceById(memMgr->base.seInstanceId, &dbIns);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbShmArrayInit(
        &memMgr->base.hashSwizzleArray, SWIZZLE_SHMARRAY_CAPACITY, sizeof(HashPageDescT), shmCtxId, dbIns);
    DbReleaseInstance(dbIns);
    return ret;
}

void HashArrayDirScaleIn(HashMemRunCtxT *memRunCtx, uint32_t dirBeginId)
{
    DB_POINTER3(memRunCtx, memRunCtx->mdMgr, memRunCtx->memMgr);

    DB_ASSERT(memRunCtx->memMgr->ccehMeta.dirPageCount <= memRunCtx->pagePtrArrCapacity);
    for (uint32_t i = dirBeginId; i < memRunCtx->memMgr->ccehMeta.dirPageCount; i++) {
        PageIdT pageAddr = memRunCtx->pageId[i];
        Status ret = HtFreePage(memRunCtx, pageAddr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                ret, "Unable to free dir page|%" PRIu32 ".%" PRIu32 "|.", memRunCtx->memMgr->base.fileId, i);
        }
    }
}

void HtSwizzleReset(HashMemRunCtxT *memRunCtx, EhTrcPageCacheT *cache)
{
    DB_POINTER3(memRunCtx, memRunCtx->memMgr, memRunCtx->mdMgr);
    DB_ASSERT(cache == NULL || cache->cnt > 0);
    DB_ASSERT(memRunCtx->memMgr->ccehMeta.dirPageCount >= 1 || cache == NULL);
    for (uint32_t i = 0; i < memRunCtx->memMgr->ccehMeta.dirPageCount; i++) {
        PageIdT pageAddr = memRunCtx->pageId[i];
        memRunCtx->pageId[i] = SE_INVALID_PAGE_ADDR;
        if (cache != NULL && cache->cnt < EH_DIR_AND_SEG_PAGE) {
            cache->pageId[cache->cnt++] = pageAddr;
            continue;
        }
        Status ret = HtFreePage(memRunCtx, pageAddr);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "|SE-Hash| Unable to free dir page |%" PRIu32 ".%" PRIu32 "| when reset hash arr",
                memRunCtx->memMgr->base.fileId, i);
        }
    }
    DB_ASSERT(cache == NULL || cache->cnt == EH_DIR_AND_SEG_PAGE);
    DbShmArrayReset(&memRunCtx->memMgr->base.hashSwizzleArray);
    memRunCtx->memMgr->ccehMeta.dirPageCount = 0;
    memRunCtx->memMgr->base.version++;
}

void HtSwizzleDestroy(HashMemRunCtxT *memRunCtx, DbInstanceHdT dbInstance)
{
    DB_POINTER(memRunCtx);

    DB_LOG_INFO("|SE-Hash|destroy lfs manager|hashFileId:%" PRIu32 "|", memRunCtx->memMgr->base.fileId);

    HtSwizzleReset(memRunCtx, NULL);
    // free corresponding shmArray
    DbShmArrayDestroy(&memRunCtx->memMgr->base.hashSwizzleArray, dbInstance);
}

void HashLinklistSwizzleReset(HashMemRunCtxT *memRunCtx, PageIdT *pageCache, uint32_t cnt)
{
    DB_POINTER3(memRunCtx, memRunCtx->memMgr, memRunCtx->mdMgr);
    if (memRunCtx->memMgr->hllMeta.pageUsedCount == 0) {
        DB_ASSERT(cnt == 0 && pageCache == NULL);
        return;
    }
    DB_ASSERT(memRunCtx->memMgr->hllMeta.pageUsedCount >= cnt);
    // at least 1 after HtOpen
    uint32_t idx = 0;
    int32_t pos = (int32_t)cnt;
    DbShmArrayIteratorT iterator;
    for (DbShmArrayInitIterator(&iterator, &(memRunCtx->memMgr->base.hashSwizzleArray));
         DbShmArrayIteratorValid(&iterator); DbShmArrayIteratorNext(&iterator)) {
        // space id 应该来自memRunCtx。pageid是提前存好，取出来的，这里应该是从 hashSwizzleArray 取出来
        HashPageDescT *item = (HashPageDescT *)DbShmArrayIteratorGetItem(&iterator);
        DB_POINTER(item);
        PageIdT pageAddr = item->pageId;
        if (pos-- > 0) {
            pageCache[idx++] = pageAddr;
            continue;
        }
        Status ret = HtFreePage(memRunCtx, pageAddr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(
                ret, "|HLL|reset page %" PRIu32 ".%" PRIu32 "", memRunCtx->memMgr->base.fileId, iterator.itemId);
        }
    }
    DB_ASSERT(idx == cnt);
    DbShmArrayReset(&(memRunCtx->memMgr->base.hashSwizzleArray));
    memRunCtx->memMgr->base.version++;
    memRunCtx->memMgr->hllMeta.pageUsedCount = 0;
}

void HashLinklistSwizzleDestroy(HashMemRunCtxT *memRunCtx, DbInstanceHdT dbInstance)
{
    DB_POINTER2(memRunCtx, memRunCtx->memMgr);

    DB_LOG_INFO("|HLL|destroy lfs manager|hashFileId:%" PRIu32 "|", memRunCtx->memMgr->base.fileId);

    HashLinklistSwizzleReset(memRunCtx, NULL, 0);

    // free corresponding shmArray
    DbShmArrayDestroy(&memRunCtx->memMgr->base.hashSwizzleArray, dbInstance);
}

Status HtCreateMVPageSwizzle(HashMemMgrT *memMgr, uint32_t shmCtxId)
{
    DB_POINTER(memMgr);
    return DbShmArrayInit(&memMgr->hashMVPageArray, SWIZZLE_SHMARRAY_CAPACITY, sizeof(HashPageDescT), shmCtxId, NULL);
}

Status HtAllocMVMemPage(HashMemRunCtxT *memRunCtx, uint32_t tableSpaceIndex, uint16_t *blockId, DbMemAddrT *pageInfo)
{
    DB_POINTER4(memRunCtx, memRunCtx->memMgr, blockId, pageInfo);
    uint32_t itemId = 0;
    HashPageDescT *item = NULL;
    DbArrayAddrT arrayAddr;
    Status ret = DbShmArrayGetItem(&memRunCtx->memMgr->hashMVPageArray, &itemId, (void **)&item, &arrayAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret,
            "|MVPage| get item from hashMVPageArray. shmArry count: %" PRIu32 ", used: %" PRIu32,
            memRunCtx->memMgr->hashMVPageArray.count, memRunCtx->memMgr->hashMVPageArray.used);
        return ret;
    }
    *blockId = (uint16_t)itemId;

    uint32_t allocPageId = SE_INVALID_PAGE_ID;
    ret = AllocNewPage(memRunCtx, itemId, pageInfo, &allocPageId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret,
            "alloc new page with item id %" PRIu32 ", page desc: (%" PRIu32 ", %" PRIu32 ")", itemId,
            item->pageId.blockId, item->pageId.deviceId);
        (void)DbShmArrayRemoveItem(&memRunCtx->memMgr->hashMVPageArray, itemId);
        return ret;
    }
    item->pageId = pageInfo->pageAddr;
    DB_ASSERT(DbIsPageIdValid(pageInfo->pageAddr));
    return GMERR_OK;
}

Status HtFreeMVMemPage(HashMemRunCtxT *memRunCtx, uint16_t blockId)
{
    DB_POINTER3(memRunCtx, memRunCtx->memMgr, memRunCtx->mdMgr);
    DbShmArrayT *blockArray = &memRunCtx->memMgr->hashMVPageArray;
    DbArrayAddrT itemAddr;
    HashPageDescT *item = (HashPageDescT *)DbShmArrayGetItemById(blockArray, (uint32_t)blockId, &itemAddr);
    if (item == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR,
            "|MVPage| get item from hashMVPageArray when free page. shmArry count: %" PRIu32 ", used: %" PRIu32,
            blockArray->count, blockArray->used);
        return GMERR_INTERNAL_ERROR;
    }

    if (!DbIsPageIdValid(item->pageId)) {
        return GMERR_OK;
    }
    Status ret = FreePage(memRunCtx, item->pageId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|MVPage| free page (deviceId,blockId):(%" PRIu32 ", %" PRIu32 ")",
            item->pageId.deviceId, item->pageId.blockId);
        return ret;
    }
    // 页删除后，对应 hashMVPageArray 上对应的item
    ret = DbShmArrayRemoveItem(blockArray, (uint32_t)blockId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|MVPage| remove block id from hashMVPageArray, itemId: %" PRIu16, blockId);
    }
    return GMERR_OK;
}

static void ResetMVPageSwizzle(HashMemRunCtxT *memRunCtx, DbShmArrayT *mvPageArray)
{
    DB_POINTER2(memRunCtx, mvPageArray);
    DbShmArrayIteratorT iter;
    // 释放所有mv page
    for (DbShmArrayInitIterator(&iter, mvPageArray); DbShmArrayIteratorValid(&iter); DbShmArrayIteratorNext(&iter)) {
        // for循环 DbShmArrayIteratorValid 已对 pageDesc 进行有效性判断
        HashPageDescT *pageDesc = (HashPageDescT *)DbShmArrayIteratorGetItem(&iter);
        PageIdT pageId = pageDesc->pageId;
        (void)FreePage(memRunCtx, pageId);
    }
    // 释放 hashMVPageArray 结构
    DbShmArrayReset(mvPageArray);
}

void HtDestroyMVPageSwizzle(HashMemRunCtxT *memRunCtx, DbInstanceHdT dbInstance)
{
    DB_POINTER(memRunCtx->memMgr);
    DbShmArrayT *mvPageArray = &memRunCtx->memMgr->hashMVPageArray;
    if (mvPageArray->isFree) {
        return;
    }
    ResetMVPageSwizzle(memRunCtx, mvPageArray);
    DbShmArrayDestroy(mvPageArray, dbInstance);
}

void HtTruncateMVPageSwizzle(HashMemRunCtxT *memRunCtx)
{
    DB_POINTER(memRunCtx->memMgr);
    DbShmArrayT *mvPageArr = &memRunCtx->memMgr->hashMVPageArray;
    if (mvPageArr->isFree) {
        return;
    }
    ResetMVPageSwizzle(memRunCtx, mvPageArr);
}

#ifdef __cplusplus
}
#endif
