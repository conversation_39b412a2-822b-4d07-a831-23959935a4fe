/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: se_hash_linklist_index.h
 * Description: hash linklist interfaces
 * Author: caiyueshun
 * Create: 2020-11-23
 */

#ifndef SE_HASH_LINKLIST_INDEX_H
#define SE_HASH_LINKLIST_INDEX_H

#include "se_hash_common.h"
#include "db_dyn_array.h"

#ifdef __cplusplus
extern "C" {
#endif

#define HASH_LINK_LIST_ITER_MAX_STATIC_ADDR_NUM 450
#define GROUP_SIZE_LOG_THRESHOLD 1000
#define GROUP_SIZE_LOG_STEP 100
#pragma pack(4)
typedef struct LinklistNode {
    IdxNodePtrT prev;  // prev.isDeleted means whether the node is logical deleted
    IdxNodePtrT next;
    uint8_t addr[];
} LinklistNodeT;
#pragma pack()

#pragma pack(4)
typedef struct LinklistGroup {
    HtHashCodeT hashCode;
    uint32_t nodeCnt;  // number of node in one group
    IdxNodePtrT lastNodePtr;
    LinklistNodeT node;
} LinklistGroupT;
#pragma pack()

typedef struct LinklistBucket {
    IdxNodePtrT firstGroupPtr;
} LinklistBucketT;

typedef struct LinklistBucketHead {
    DbRWSpinLockT bucketPageLatch;
} LinklistBucketHeadT;

typedef struct LinklistIterator {
    IndexKeyT key;
    uint64_t htVersion;
    TupleAddr staticAddrs[HASH_LINK_LIST_ITER_MAX_STATIC_ADDR_NUM];
    uint32_t staticAddrsNum;
    uint32_t staticArrCursor;
    DbDynArrayT dynAddrs;
    uint32_t dynArrCursor;
#ifdef SYS32BITS
    DbOamapT dumpAddrsMap;
#endif
} LinklistIteratorT;

size_t GetLinklistNodeAddrSize(const IndexCtxT *idxCtx);

/**
 * @brief 创建二级索引表
 * @param ht : 入参，特定索引的元数据 HashTableT, NonUniqueIndexT etc.
 * @param hashCap : 入参， 二级索引表的初始bucket个数
 * @return 成功或者其他失败原因
 */
Status HashLinklistCreateHashTable(SeRunCtxHdT seRunCtx, HashTableT *ht, uint32_t hashCap);

/**
 * @brief 分配节点内存空间
 * @param idxCtx : 入参，由 HtOpen 创建的hash table 句柄idxCtx
 * @param nodeSize: 入参 ，分配节点的相关参数
 * @param page: 出参
 * @param seNodePtr: 出参， 内存对应的位置指针
 * @return 成功或者其他失败原因
 */
Status HashLinklistAllocNodeMem(IndexCtxT *idxCtx, uint16_t nodeSize, uint8_t **page, IdxNodePtrT *seNodePtr);

/*
hash linklist index access method interface define
*/
Status HashLinklistIndexCreate(SeRunCtxT *seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr);

Status HashLinklistIndexDrop(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr);

Status HashLinklistIndexTruncate(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr);

Status HashLinklistIndexOpen(IndexCtxT *idxCtx);

void HashLinklistIndexClose(IndexCtxT *idxCtx);

Status HashLinklistIndexInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr);

Status HashLinklistIndexBatchInsert(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum);

Status HashLinklistIndexDelete(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr, IndexRemoveParaT removePara);

Status HashLinklistIndexBatchDelete(
    IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum, IndexRemoveParaT removePara);

Status HashLinklistIndexBatchUpdate(
    IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo[], uint32_t batchNum, IndexRemoveParaT removePara);

inline static Status HashLinklistIndexUpdate(
    IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara)
{
    return HashLinklistIndexBatchUpdate(idxCtx, &updateInfo, 1, removePara);
}

Status HashLinklistIndexBeginScan(IndexCtxT *idxCtx, IndexScanCfgT scanCfg, IndexScanItrT *iter);

Status HashLinklistIndexScan(IndexCtxT *idxCtx, IndexScanItrT iter, TupleAddr *addr, bool *isFound);

void HashLinklistIndexEndScan(const IndexCtxT *idxCtx, IndexScanItrT iter);

Status HashLinklistIndexUndoInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr);

Status HashLinklistIndexUndoRemove(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr);

Status HashLinklistIndexUndoUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara);

Status HashLinklistIndexGetKeyCount(IndexCtxT *idxCtx, IndexKeyT idxKey, uint64_t *count);

Status HashLinklistIndexStatView(ShmemPtrT idxShmAddr, uint16_t instanceId, IndexStatisticsT *idxStat);

Status HashLinklistViewGetPageSize(ShmemPtrT idxShmAddr, uint16_t instanceId, uint64_t *idxPageSize);

Status HashLinklistIndexGetEstimateMemSize(
    uint16_t instanceId, IndexMetaCfgT idxMetaCfg, uint64_t count, uint32_t keyLen, uint64_t *size);

static inline void HashLinklistGetCtxSize(size_t *ctxSize, size_t *iterSize)
{
    *ctxSize = sizeof(HashIndexCtxT);
    *iterSize = sizeof(LinklistIteratorT);
}

ALWAYS_INLINE static bool HashLinkListHashTableIsConstructed(HashTableT *ht)
{
    return ht->idxBase.isConstructed;
}

#ifdef __cplusplus
}
#endif
#endif  // __SE_HASH_LINKLIST_INDEX_H__
