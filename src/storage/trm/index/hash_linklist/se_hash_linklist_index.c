/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: se_hash_linklist_index.c
 * Description: Implementation of hash linklist index
 * Author: qipengcheng gaohaiyang
 * Create: 2020-11-23
 */

#include "se_hash_linklist_index.h"
#include "se_hash_mem.h"
#include "dm_data_index.h"
#include "se_log.h"

#ifdef __cplusplus
extern "C" {
#endif

/*
 * Hash Function hashes a key into Hash Space (linear array), Hash Space consists of Bucket Pages.
 * Bucket Page is of following format:
 *  +-------------+
 *  | Page Head   |
 *  |-------------|<--- pageInfo.virtAddr
 *  | Bucket Head |
 *  |-------------|
 *  | Bucket #1   |
 *  | Bucket #2   |
 *  |  ...        |
 *  | Bucket #n   |
 *  +-------------+
 *  HtGetMemPage() will set pageInfo.virtAddr
 *
 *  Node Page is of following format:
 *  --- +-------------+
 *   |  | Page Head   |
 * o |  |-------------|<--- pageInfo.virtAddr
 * f |  | Node #1     |
 * f |  | Node #2     |
 * s |  | Node #3     |
 * e |  |  ...        |
 * t |  |  ...        |
 *   |  |  ...        |
 *  --- | Node #k     |
 *      |  ...        |
 *      | Node #n     |
 *      +-------------+
 * ptr.offset is the offset from begin of page to Node #k
 */

#define LINKLIST_ADDR_MAX_BLOCK_ID 0x7fff
#define LINKLIST_ADDR_MAX_OFFSET 0xffff

#pragma pack(4)
typedef struct LinklistNodeAddr {
    LinklistNodeT *node;
    IdxNodePtrT nodePtr;
} LinklistNodeAddrT;
#pragma pack()

typedef struct LinklistPara {
    LinklistBucketHeadT *bucketHead;
    LinklistNodeT *lastNode;
    LinklistGroupT *group;
    IdxNodePtrT groupPtr;
} LinklistParaT;

typedef struct LinklistGroupResult {
    LinklistGroupT *group;
    bool isFound;
} LinklistGroupResultT;

typedef struct LinklistNodeResult {
    LinklistNodeT *node;
    bool isFound;
} LinklistNodeResultT;

inline static uint8_t *NodePtr2Addr(HashMemRunCtxT *memRunCtx, IdxNodePtrT ptr)
{
    if (ptr.blockId == HT_INVALID_BLOCK_ID || ptr.blockId == 0u || ptr.offset == DB_INVALID_UINT16 ||
        ptr.offset == 0u) {
        return NULL;
    }
    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;
    Status ret = HtGetMemPage(memRunCtx, ptr.blockId, SE_INVALID_PAGE_ID, false, &pageInfo);
    if (ret != GMERR_OK) {
        return NULL;
    }
    return (uint8_t *)((uintptr_t)HtGetPageHead(pageInfo.virtAddr) + ptr.offset);
}

inline static LinklistNodeT *IdxNodePtr2NodeAddr(const IndexCtxT *idxCtx, IdxNodePtrT ptr)
{
    return (LinklistNodeT *)(void *)NodePtr2Addr(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, ptr);
}

inline static LinklistGroupT *IdxNodePtr2GroupAddr(const IndexCtxT *idxCtx, IdxNodePtrT ptr)
{
    return (LinklistGroupT *)(void *)NodePtr2Addr(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, ptr);
}

inline static uint16_t GetLinklistGroupSize(SeHpTupleAddrMode heapTupleAddrMode)
{
    return (uint16_t)(sizeof(LinklistGroupT) + HashGetAddrSize(heapTupleAddrMode));
}

inline static uint16_t GetLinklistNodeSize(SeHpTupleAddrMode heapTupleAddrMode)
{
    return (uint16_t)(sizeof(LinklistNodeT) + HashGetAddrSize(heapTupleAddrMode));
}

inline static bool IsLinklistNodePtrEqual(IdxNodePtrT nodePtrFst, IdxNodePtrT nodePtrSec)
{
    return (nodePtrFst.blockId == nodePtrSec.blockId && nodePtrFst.offset == nodePtrSec.offset);
}

inline static bool IsLinklistNodePtrValid(IdxNodePtrT nodePtr)
{
    return (nodePtr.blockId != HT_INVALID_BLOCK_ID && nodePtr.offset != INVALID_OFFSET16 && nodePtr.blockId != 0u &&
            nodePtr.offset != 0u);
}

inline static void SetLinklistNodePtr(IdxNodePtrT *nodePtrDst, const IdxNodePtrT nodePtrSrc)
{
    DB_POINTER(nodePtrDst);
    nodePtrDst->blockId = nodePtrSrc.blockId;
    nodePtrDst->offset = nodePtrSrc.offset;
}

Status HashLinklistGroupNodeMemReuse(IndexCtxT *idxCtx, IdxNodePtrT *nodePtr, LinklistGroupT **currentGroup)
{
    DB_POINTER2(idxCtx->idxHandle, nodePtr);
    // 内部调用的接口，保证level一定不大于HASHLINKLIST_FREELIST_COUNT
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    if (!IsLinklistNodePtrValid(ht->memMgr.hllMeta.nodePtr[0])) {
        // 正常情况 不用打日志
        return GMERR_INSUFFICIENT_RESOURCES;
    }
    IdxNodePtrT tmpNode = ht->memMgr.hllMeta.nodePtr[0];
    LinklistGroupT *group =
        (LinklistGroupT *)(void *)NodePtr2Addr(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, tmpNode);
    if (group == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED,
            "|HashLinklist| reuse group node is null, blockId %" PRIu32 ", offset %" PRIu32 ", indexId %" PRIu32 ".",
            tmpNode.blockId, tmpNode.offset, ht->idxBase.indexCfg.indexId);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    LinklistNodeT *linkListNode = &group->node;
    ht->memMgr.hllMeta.nodePtr[0] = linkListNode->next;
    *nodePtr = tmpNode;
    *currentGroup = group;
    return GMERR_OK;
}

Status HashLinklistNodeMemReuse(IndexCtxT *idxCtx, LinklistNodeAddrT *nodeAddr, uint32_t level)
{
    DB_POINTER2(idxCtx->idxHandle, nodeAddr);
    DB_ASSERT(level < HASHLINKLIST_FREELIST_COUNT && level != 0);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    if (!IsLinklistNodePtrValid(ht->memMgr.hllMeta.nodePtr[level])) {
        // 正常情况 不用打日志
        return GMERR_INSUFFICIENT_RESOURCES;
    }
    IdxNodePtrT tmpNode = ht->memMgr.hllMeta.nodePtr[level];
    LinklistNodeT *linkListNode =
        (LinklistNodeT *)(void *)NodePtr2Addr(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, tmpNode);
    if (linkListNode == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED,
            "|HashLinklist| reuse node %" PRIu32 " is null, blockId %" PRIu32 ", offset %" PRIu32 ", indexId %" PRIu32
            ".",
            level, tmpNode.blockId, tmpNode.offset, ht->idxBase.indexCfg.indexId);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    ht->memMgr.hllMeta.nodePtr[level] = linkListNode->next;
    nodeAddr->nodePtr = tmpNode;
    nodeAddr->node = linkListNode;
    return GMERR_OK;
}

inline static void SetLinklistNodeAddr(const IndexCtxT *idxCtx, LinklistNodeT *node, TupleAddr addrUncompress)
{
    DB_POINTER2(idxCtx, node);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint8_t *addr = (uint8_t *)(node + 1);
    HeapCompressTupleAddr(addrUncompress, ht->heapTupleAddrMode, addr);
}

inline static TupleAddr GetLinklistNodeAddr(const IndexCtxT *idxCtx, LinklistNodeT *node)
{
    DB_POINTER2(idxCtx, node);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint8_t *addr = (uint8_t *)(node + 1);
    return HeapUncompressTupleAddr(addr, ht->heapTupleAddrMode);
}

static Status HashLinklistNewNode(IndexCtxT *idxCtx, TupleAddr htAddr, LinklistNodeAddrT *nodeAddr)
{
    Status ret = HashLinklistNodeMemReuse(idxCtx, nodeAddr, 1u);
    if (ret != GMERR_OK) {
        HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
        uint16_t nodeSize = GetLinklistNodeSize(ht->heapTupleAddrMode);
        uint8_t *addr = NULL;
        ret = HashLinklistAllocNodeMem(idxCtx, nodeSize, &addr, &nodeAddr->nodePtr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to alloc node mem");
            return ret;
        }
        nodeAddr->node = (LinklistNodeT *)(void *)addr;
    }
    if (nodeAddr->node == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INSUFFICIENT_RESOURCES, "|HashLinklist| hashtable is null");
        return GMERR_INSUFFICIENT_RESOURCES;
    }
    SetLinklistNodeAddr(idxCtx, nodeAddr->node, htAddr);
    nodeAddr->node->prev.isDeleted = 0u;
    SetLinklistNodePtr(&nodeAddr->node->prev, HtNullNodePtr());
    SetLinklistNodePtr(&nodeAddr->node->next, HtNullNodePtr());
    return GMERR_OK;
}

static void SetGroupFirstNode(
    const IndexCtxT *idxCtx, LinklistGroupT *group, HtHashCodeT hashCode, TupleAddr addr, IdxNodePtrT nodePtr)
{
    DB_POINTER(group);
    SetLinklistNodeAddr(idxCtx, &group->node, addr);
    // 此处不能调用SetLinklistNodePtr，要把prev.isDeleted置为0，否则可能导致节点误被回收
    group->node.prev = HtNullNodePtr();
    SetLinklistNodePtr(&group->node.next, HtNullNodePtr());
    group->hashCode = hashCode;
    group->nodeCnt = 1u;
    group->lastNodePtr = nodePtr;
}

// new a group & allocate the first node
Status HashLinklistNewGroup(
    IndexCtxT *idxCtx, TupleAddr addr, HtHashCodeT hashCode, LinklistGroupT **group, IdxNodePtrT *groupAddr)
{
    DB_POINTER3(idxCtx, group, groupAddr);
    LinklistGroupT *currentGroup = NULL;
    uint8_t *pagePtr = NULL;
    IdxNodePtrT seNodePtrTmp;
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    Status ret = HashLinklistGroupNodeMemReuse(idxCtx, &seNodePtrTmp, &currentGroup);
    if (ret != GMERR_OK) {
        uint16_t nodeSize = GetLinklistGroupSize(ht->heapTupleAddrMode);
        ret = HashLinklistAllocNodeMem(idxCtx, nodeSize, &pagePtr, &seNodePtrTmp);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to alloc group node mem, indexId %" PRIu32 ".",
                ht->idxBase.indexCfg.indexId);
            return ret;
        }
        currentGroup = (LinklistGroupT *)(void *)pagePtr;
    }
    if (SECUREC_UNLIKELY(currentGroup == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INSUFFICIENT_RESOURCES,
            "|HashLinklist| hashtable is null, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return GMERR_INSUFFICIENT_RESOURCES;
    }
    ht->memMgr.hllMeta.groupCnt++;
    *group = currentGroup;
    *groupAddr = seNodePtrTmp;
    SetGroupFirstNode(idxCtx, *group, hashCode, addr, seNodePtrTmp);
    return GMERR_OK;
}

inline static LinklistBucketT *GetBucketById(LinklistBucketHeadT *bucketHead, uint16_t bucketId)
{
    DB_POINTER(bucketHead);
    return (LinklistBucketT *)(void *)((uintptr_t)bucketHead + sizeof(LinklistBucketHeadT) +
                                       bucketId * sizeof(LinklistBucketT));
}

/*
 * low 8 bits of hashcode is the same in one linklist
 * nodes in one group share the same hashcode, but their key may be different
 * different groups of one linklist have different hashcode
 */
inline static bool GroupIsMatch(const LinklistGroupT *group, HtHashCodeT hashCode)
{
    return group->hashCode == hashCode;
}

// given a hashcode, get pageId and return bucketId
inline static uint32_t HashLinklistGetPageAndBucketId(IndexCtxT *idxCtx, HtHashCodeT hashCode, uint32_t *pageId)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    HashLinklistMemMetaT *hllMeta = &ht->memMgr.hllMeta;
    if (hllMeta->bucketNum == 0u || hllMeta->bucketNumPerPage == 0u) {
        return DB_INVALID_UINT32;
    }
    uint32_t bucketIdInAllBucketPage = (uint32_t)(hashCode % ((HtHashCodeT)(hllMeta->bucketNum)));
    uint32_t bucketNumPerPage = hllMeta->bucketNumPerPage;
    *pageId = bucketIdInAllBucketPage / bucketNumPerPage;
    return bucketIdInAllBucketPage % bucketNumPerPage;
}

static Status GetBucketHead(
    IndexCtxT *idxCtx, HtHashCodeT hashCode, IdxNodePtrT *bucketAddr, LinklistBucketHeadT **bucketHead)
{
    DB_POINTER3(idxCtx, bucketAddr, bucketHead);
    uint32_t bucketId = 0u;  // hash table array is consist of Bucket Pages
    uint32_t offset = HashLinklistGetPageAndBucketId(idxCtx, hashCode, &bucketId);
    if (SECUREC_UNLIKELY(offset == DB_INVALID_UINT32)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "|HashLinklist| Unable to get hash page ID");
        return GMERR_OUT_OF_MEMORY;
    }
    DB_ASSERT(bucketId <= LINKLIST_ADDR_MAX_BLOCK_ID && offset <= LINKLIST_ADDR_MAX_OFFSET);
    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;
    // 目前hashLinkList的Dir页也按照Seg页处理，存在swizzleArray里
    Status ret =
        HtGetMemPage(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, bucketId, SE_INVALID_PAGE_ID, false, &pageInfo);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to get hash mem page");
        return ret;
    }

    IdxNodePtrT bktAddr = {.blockId = (uint16_t)(bucketId & 0x7FFFu), .isDeleted = 0, .offset = (uint16_t)offset};
    *bucketAddr = bktAddr;
    *bucketHead = (LinklistBucketHeadT *)(void *)pageInfo.virtAddr;
    return GMERR_OK;
}

inline static LinklistBucketT *GetBucketFromHeadOffset(LinklistBucketHeadT *bucketHead, uint16_t offsetNum)
{
    DB_POINTER(bucketHead);
    DB_ASSERT(offsetNum != DB_INVALID_UINT16);
    uint32_t bucketOffset = (uint32_t)(offsetNum * sizeof(LinklistBucketT));
    return (LinklistBucketT *)(void *)((uintptr_t)bucketHead + sizeof(LinklistBucketHeadT) + bucketOffset);
}

static Status GetBucketAndBucketHead(IndexCtxT *idxCtx, HtHashCodeT hashCode, LinklistBucketT **bucket,
    LinklistBucketHeadT **bucketHead, IdxNodePtrT *bucketAddr)
{
    DB_POINTER2(idxCtx, bucket);
    IdxNodePtrT bktAddr;
    LinklistBucketHeadT *bktHead = NULL;
    Status ret = GetBucketHead(idxCtx, hashCode, &bktAddr, &bktHead);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to get hash bucket head");
        return ret;
    }
    LinklistBucketT *bkt = GetBucketFromHeadOffset(bktHead, bktAddr.offset);
    if (SECUREC_UNLIKELY(bkt == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "|HashLinklist| Unable to get link list bucket");
        return GMERR_INTERNAL_ERROR;
    }
    *bucket = bkt;
    if (bucketHead != NULL) {
        *bucketHead = bktHead;
    }
    if (bucketAddr != NULL) {
        *bucketAddr = bktAddr;
    }
    return GMERR_OK;
}

static Status AllocGroupAndInsertBucket(
    IndexCtxT *idxCtx, LinklistBucketT *bucket, IdxNodePtrT bucketAddr, HtHashCodeT hashCode, TupleAddr addr)
{
    LinklistGroupT *group = NULL;
    IdxNodePtrT groupPtr;
    Status ret = HashLinklistNewGroup(idxCtx, addr, hashCode, &group, &groupPtr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to alloc a new group");
        return ret;
    }
    DB_ASSERT(group != NULL);
    bucket->firstGroupPtr = groupPtr;
    SetLinklistNodePtr(&group->node.prev, bucketAddr);  // firstNode of a bucket, its prev point to bucket
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    ht->entryUsed++;
    ht->memMgr.hllMeta.maxGroupLen = DB_MAX(ht->memMgr.hllMeta.maxGroupLen, group->nodeCnt);
    return GMERR_OK;
}

inline static Status GetNextGroup(const IndexCtxT *idxCtx, LinklistGroupT *group, IdxNodePtrT *groupPtr)
{
    LinklistNodeT *lastNode = (group->nodeCnt == 1 ? &group->node : IdxNodePtr2NodeAddr(idxCtx, group->lastNodePtr));
    if (lastNode == NULL) {
        *groupPtr = HtNullNodePtr();
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "|HashLinklist| can not get last node");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *groupPtr = lastNode->next;
    return GMERR_OK;
}

/*
 * insert a node to group
 *  - insert to second position and swap node content with first node
 *  NOTE: upgrade linked list to skip list if necessary
 */
static Status Insert2MatchedGroup(IndexCtxT *idxCtx, LinklistGroupT *group, IdxNodePtrT groupPtr, TupleAddr addr)
{
    DB_POINTER2(idxCtx, group);
    // insert after firstNode and swap their value
    LinklistNodeAddrT insertNode = {.node = NULL, .nodePtr = HtNullNodePtr()};
    Status ret = HashLinklistNewNode(idxCtx, addr, &insertNode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "|HashLinklist| Unable to alloc a new node, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return ret;
    }
    DB_POINTER(insertNode.node);
    // 插入到secNode之前，但secNode可能是下一个group的头结点，需要特殊处理
    if (group->nodeCnt == 1u) {
        if (IsLinklistNodePtrValid(group->node.next)) {
            LinklistGroupT *nextGroup = IdxNodePtr2GroupAddr(idxCtx, group->node.next);
            if (nextGroup == NULL) {
                DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
                    "|HashLinklist| Unable to get next group, indexId %" PRIu32 ".", idxCtx->idxMetaCfg.indexId);
                return GMERR_UNEXPECTED_NULL_VALUE;
            }
            SetLinklistNodePtr(&nextGroup->node.prev, insertNode.nodePtr);
        }
        SetLinklistNodePtr(&group->lastNodePtr, insertNode.nodePtr);
    } else {
        LinklistNodeT *secNode = IdxNodePtr2NodeAddr(idxCtx, group->node.next);
        if (SECUREC_UNLIKELY(secNode == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|HashLinklist| Unable to get node addr.");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        SetLinklistNodePtr(&secNode->prev, insertNode.nodePtr);
    }
    SetLinklistNodePtr(&insertNode.node->next, group->node.next);
    SetLinklistNodePtr(&insertNode.node->prev, groupPtr);
    SetLinklistNodePtr(&group->node.next, insertNode.nodePtr);
    group->nodeCnt++;
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    if (group->nodeCnt > ht->memMgr.hllMeta.maxGroupLen) {
        ht->memMgr.hllMeta.maxGroupLen = group->nodeCnt;
    }
    if (group->nodeCnt >= GROUP_SIZE_LOG_THRESHOLD && group->nodeCnt % GROUP_SIZE_LOG_STEP == 0) {
        DB_LOG_WARN(GMERR_OK,
            "|HashLinklist| indexId: %d, curr group's nodes num: %d, maxGroupLen: %d, group count: %d, entry used: %d, "
            "usedMemSize: %d.",
            idxCtx->idxMetaCfg.indexId, group->nodeCnt, ht->memMgr.hllMeta.maxGroupLen, ht->memMgr.hllMeta.groupCnt,
            ht->entryUsed, ht->memMgr.hllMeta.usedMemSize);
    }
    return GMERR_OK;
}

inline static bool IsFirstNodeOfBucket(const HashTableT *ht, IdxNodePtrT nodePtr)
{
    DB_POINTER(ht);
    return (nodePtr.blockId < ht->memMgr.hllMeta.bucketPageNum);
}

/*
 * create a new group that contains only a single node(first node)
 * insert the group at the very beginning of linked list, a.k.a this group is pointed by hash bucket
 * insertLastPara.lastNode may be NULL, it's normal
 */
static Status Insert2NewGroup(IndexCtxT *idxCtx, LinklistParaT *insertPara, HtHashCodeT hashCode, const TupleAddr addr)
{
    DB_POINTER3(idxCtx, insertPara, insertPara->group);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    LinklistGroupT *group = NULL;
    IdxNodePtrT groupPtr;
    Status ret = HashLinklistNewGroup(idxCtx, addr, hashCode, &group, &groupPtr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "|HashLinklist| Unable to alloc a new group, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return ret;
    }
    DB_ASSERT(group != NULL);
    LinklistGroupT *currentGroup = insertPara->group;
    // insert new group to bucket
    SetLinklistNodePtr(&group->node.prev, currentGroup->node.prev);
    SetLinklistNodePtr(&currentGroup->node.prev, groupPtr);
    SetLinklistNodePtr(&group->node.next, insertPara->groupPtr);

    // update firstNodeId in bucket
    LinklistBucketT *bucket = GetBucketById(insertPara->bucketHead, group->node.prev.offset);
    bucket->firstGroupPtr = groupPtr;

    if (group->nodeCnt > ht->memMgr.hllMeta.maxGroupLen) {
        ht->memMgr.hllMeta.maxGroupLen = group->nodeCnt;
    }
    return GMERR_OK;
}

/*
 * insert a node into linked list
 * - matched group found, insert into that group
 * - no matched group, create a new group of 1 node, then insert
 *
 * NOTE #1
 * linklist is guaranteed not to be NULL
 */
Status LinklistInsert(IndexCtxT *idxCtx, LinklistParaT *insertPara, HtHashCodeT hashCode, const TupleAddr addr)
{
    DB_POINTER2(insertPara, insertPara->group);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    LinklistGroupT *currentGroup = insertPara->group;
    IdxNodePtrT currentGroupPtr = insertPara->groupPtr;
    Status ret = GMERR_OK;
    while (currentGroup != NULL) {
        if (GroupIsMatch(currentGroup, hashCode)) {
            ht->hashCollisionCnt++;
            return Insert2MatchedGroup(idxCtx, currentGroup, currentGroupPtr, addr);
        }
        ret = GetNextGroup(idxCtx, currentGroup, &currentGroupPtr);
        if (ret != GMERR_OK) {
            return ret;
        }
        currentGroup = IdxNodePtr2GroupAddr(idxCtx, currentGroupPtr);
        if (currentGroup == NULL && IsLinklistNodePtrValid(currentGroupPtr)) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "|HashLinklist| can not get current group");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
    }
    DB_ASSERT(IsFirstNodeOfBucket(ht, insertPara->group->node.prev));
    return Insert2NewGroup(idxCtx, insertPara, hashCode, addr);
}

static Status InsertIndex(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    DB_POINTER(idxCtx);
    LinklistBucketT *bucket = NULL;
    LinklistBucketHeadT *bucketHead = NULL;
    IdxNodePtrT bucketAddr;
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    HtHashCodeT hashCode = HtGetHashCode(idxKey);
    Status ret = GetBucketAndBucketHead(idxCtx, hashCode, &bucket, &bucketHead, &bucketAddr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to get bucket and bucket head, indexId %" PRIu32 ".",
            ht->idxBase.indexCfg.indexId);
        return ret;
    }
    DB_POINTER(bucket);
    // first bucket node insert
    ht->hashInsertCnt++;  // 有insert就计数+1，不考虑是否成功插入
    if (!IsLinklistNodePtrValid(bucket->firstGroupPtr)) {
        return AllocGroupAndInsertBucket(idxCtx, bucket, bucketAddr, hashCode, addr);
    }
    LinklistGroupT *firstGroup = IdxNodePtr2GroupAddr(idxCtx, bucket->firstGroupPtr);
    if (SECUREC_UNLIKELY(firstGroup == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
            "|HashLinklist| first node page is null, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    LinklistParaT insertPara = {
        .bucketHead = bucketHead,
        .lastNode = NULL,
        .group = firstGroup,
        .groupPtr = bucket->firstGroupPtr,
    };
    ret = LinklistInsert(idxCtx, &insertPara, hashCode, addr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "|HashLinklist| Unable to insert index, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return ret;
    }
    ht->entryUsed++;  // number of node (including firstNode)
    return GMERR_OK;
}

static Status VerifyAndConstructHashTable(SeRunCtxT *seRunCtx, HashTableT *ht)
{
    DB_POINTER(ht);
    IdxBaseT *idxBase = &ht->idxBase;
    if (!IdxIsConstructed(idxBase)) {
        // if false is returned, the hash table is locked.
        if (IdxWLockIfNotConstructed(idxBase)) {
            return GMERR_OK;
        }
        Status ret = HashLinklistCreateHashTable(seRunCtx, ht, idxBase->indexCfg.indexCap);
        if (ret != GMERR_OK) {
            DbRWUnlatchW(&idxBase->idxLatch);
            DB_LOG_ERROR_AND_SET_LASTERR(
                ret, "|HashLinklist| Unable to create hash table, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
            return ret;
        }
        IdxConstructedFinish(idxBase);
        DB_ASSERT(ht->memMgr.hllMeta.bucketPageNum != 0);
        DbRWUnlatchW(&idxBase->idxLatch);
    }
    return GMERR_OK;
}

Status HashLinklistIndexInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    DB_POINTER(idxCtx);
    uint8_t cmpRes = (uint8_t)INDEX_FILTER_SATISFIED;
    Status ret = IdxCheckFilterSatisfied(idxCtx, idxKey, &cmpRes);
    if (cmpRes == (uint8_t)INDEX_FILTER_UNSATISFIED || ret != GMERR_OK) {
        return ret;
    }

    AUTO_INDEX_RW_LATCH_WLL(idxCtx, (ret = InsertIndex(idxCtx, idxKey, addr)));
    return ret;
}

Status HashLinklistIndexBatchInsert(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum)
{
    DB_POINTER3(idxCtx, idxKey, addr);
    IdxBatchBegin(idxCtx);
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < batchNum; ++i) {
        ret = HashLinklistIndexInsert(idxCtx, idxKey[i], addr[i].addrOut);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret,
                "|SE-IDX|HashLinkList index batch insert unsucc, i %" PRIu32 "batch num:%" PRIu32 ", indexId %" PRIu32,
                i, batchNum, idxCtx->idxMetaCfg.indexId);
            break;
        }
    }
    IdxBatchEnd(idxCtx);
    return ret;
}

// given a key, get the group from linklist
Status LinklistGetGroup(const IndexCtxT *idxCtx, LinklistGroupT *group, IdxNodePtrT groupPtr, IndexKeyT hashKey,
    LinklistGroupResultT *foundResult)
{
    DB_POINTER2(idxCtx, group);
    LinklistGroupT *currentGroup = group;
    IdxNodePtrT currentGroupPtr = groupPtr;
    HtHashCodeT hashCode = HtGetHashCode(hashKey);
    Status ret = GMERR_OK;
    while (currentGroup != NULL) {
        if (GroupIsMatch(currentGroup, hashCode)) {
            foundResult->group = currentGroup;
            foundResult->isFound = true;
            return GMERR_OK;
        }
        ret = GetNextGroup(idxCtx, currentGroup, &currentGroupPtr);
        if (ret != GMERR_OK) {
            return ret;
        }
        currentGroup = IdxNodePtr2GroupAddr(idxCtx, currentGroupPtr);
        if (IsLinklistNodePtrValid(currentGroupPtr) && currentGroup == NULL) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "|HashLinklist| can not get current group");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
    }
    return GMERR_OK;
}

/* NOTE:
 * in a group, multiple nodes may have same addr:
 * among those nodes, only 1 node has isDelete == 0,
 * but may exist 0 or multiple nodes whose isDelete == 1
 * (Consider a Trx in-place update Key A -> Key B -> Key A, addr not changed)
 * [Key A, isDelete == 1, addr]
 * [Key B, isDelete == 0, addr]
 * [Key A, isDelete == 0, addr]
 *
 * WHAT THIS FUNC DOES:
 * find a node in a specific group that matches addr.
 * NOTE: not check whether first node matches, first node check is handled elsewhere
 */
Status LinklistFindNode(const IndexCtxT *idxCtx, LinklistGroupT *group, TupleAddr addr, uint16_t fdMarkDeletedNode,
    LinklistNodeResultT *findResult)
{
    DB_POINTER(group);
    LinklistNodeT *currentNode = &group->node;
    uint32_t nodeCnt = group->nodeCnt;
    IdxNodePtrT nextNodePtr = currentNode->next;
    for (uint32_t i = 1u; i < nodeCnt; i++) {
        currentNode = IdxNodePtr2NodeAddr(idxCtx, nextNodePtr);
        if (currentNode == NULL) {
            if (IsLinklistNodePtrValid(nextNodePtr)) {
                DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "|HashLinklist| can not get current node");
                return GMERR_UNEXPECTED_NULL_VALUE;
            }
            break;
        }
        nextNodePtr = currentNode->next;
        TupleAddr currentNodeAddr = GetLinklistNodeAddr(idxCtx, currentNode);
        // undo查找时，对标记位没有要求
        if (addr == currentNodeAddr && (currentNode->prev.isDeleted == fdMarkDeletedNode ||
                                           idxCtx->idxMetaCfg.indexMultiVersionType == INDEX_MULTI_VERSION_TYPE)) {
            findResult->node = currentNode;
            findResult->isFound = true;
            return GMERR_OK;
        }
    }
    return GMERR_OK;
}

LinklistNodeResultT LinklistFindNodeForUndo(const IndexCtxT *idxCtx, LinklistGroupT *group, TupleAddr addr)
{
    DB_POINTER(group);
    LinklistNodeT *currentNode = &group->node;
    uint32_t nodeCnt = group->nodeCnt;
    for (uint32_t i = 1u; i < nodeCnt; i++) {
        currentNode = IdxNodePtr2NodeAddr(idxCtx, currentNode->next);
        if (currentNode == NULL) {
            break;
        }
        TupleAddr currentNodeAddr = GetLinklistNodeAddr(idxCtx, currentNode);
        // undo查找时，对标记位没有要求
        if (addr == currentNodeAddr) {
            LinklistNodeResultT findResult = {.node = currentNode, .isFound = true};
            return findResult;
        }
    }
    return (LinklistNodeResultT){.node = NULL, .isFound = false};
}

static Status HashLinklistGroupNodeMemRecycle(const IndexCtxT *idxCtx, LinklistNodeT *linkListNode, IdxNodePtrT nodePtr)
{
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    linkListNode->next = ht->memMgr.hllMeta.nodePtr[0];
    ht->memMgr.hllMeta.nodePtr[0] = nodePtr;
    return GMERR_OK;
}

static Status HashLinklistNodeMemRecycle(
    const IndexCtxT *idxCtx, LinklistNodeT *linkListNode, IdxNodePtrT nodePtr, uint32_t level)
{
    DB_ASSERT(level < HASHLINKLIST_FREELIST_COUNT && level != 0);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    linkListNode->next = ht->memMgr.hllMeta.nodePtr[level];
    ht->memMgr.hllMeta.nodePtr[level] = nodePtr;
    return GMERR_OK;
}

static Status EraseFirstNodeIn1NodeGroup(const IndexCtxT *idxCtx, LinklistParaT *removePara)
{
    DB_POINTER3(idxCtx, removePara, removePara->group);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    LinklistGroupT *currentGroup = removePara->group;
    // 判断是否存在下一个node
    bool isNextNodeExist = IsLinklistNodePtrValid(currentGroup->node.next);
    LinklistGroupT *nextGroup = NULL;
    if (isNextNodeExist) {
        nextGroup = IdxNodePtr2GroupAddr(idxCtx, currentGroup->node.next);
        if (nextGroup == NULL) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "|HashLinklist| can not get next node, indexId %" PRIu32 ".",
                ht->idxBase.indexCfg.indexId);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
    }
    bool isFirstOfBucket = IsFirstNodeOfBucket(ht, currentGroup->node.prev);
    // change pointer of last node/bucket
    if (isFirstOfBucket) {
        LinklistBucketHeadT *bucketHead = removePara->bucketHead;
        LinklistBucketT *bucket = GetBucketById(bucketHead, currentGroup->node.prev.offset);
        // currentGroup->firstNode.next may NULL or next group
        SetLinklistNodePtr(&bucket->firstGroupPtr, currentGroup->node.next);
    } else {
        LinklistNodeT *prevGroupLastNode = removePara->lastNode;
        if (prevGroupLastNode == NULL) {
            DB_LOG_DBG_ERROR(GMERR_NO_DATA, "|HashLinklist| hash prev node is null, indexId %" PRIu32 ".",
                ht->idxBase.indexCfg.indexId);
            return GMERR_NO_DATA;
        }
        // currentGroup->firstNode.next may NULL or next group
        SetLinklistNodePtr(&prevGroupLastNode->next, currentGroup->node.next);
    }
    // if next node exist, change its previous pointer
    if (nextGroup != NULL) {
        // currentGroup->firstNode.prev may be a bucket of a group
        SetLinklistNodePtr(&nextGroup->node.prev, currentGroup->node.prev);
    }
    ht->memMgr.hllMeta.groupCnt--;
    // secNode memory reuse
    return HashLinklistGroupNodeMemRecycle(idxCtx, &removePara->group->node, removePara->groupPtr);
}

static Status EraseFirstNodeFromGroup(const IndexCtxT *idxCtx, LinklistParaT *removePara)
{
    DB_POINTER3(idxCtx, removePara, removePara->group);
    LinklistGroupT *group = removePara->group;
    if (group->nodeCnt > 1u) {
        SetLinklistNodeAddr(idxCtx, &group->node, HEAP_INVALID_ADDR);
        group->node.prev.isDeleted = 0;
        return GMERR_OK;
    }
    // delete this group, need judge preNode or bucket then redirect to FirstNode
    return EraseFirstNodeIn1NodeGroup(idxCtx, removePara);
}

/*
 * assume group has >= 2 nodes
 * remove a node from group
 * NOTE: not first node, which is handled elsewhere
 */
static Status ListGroupEraseNode(
    const IndexCtxT *idxCtx, LinklistGroupT *group, IdxNodePtrT groupPtr, LinklistNodeT *deleteNode)
{
    DB_POINTER3(idxCtx, group, deleteNode);
    LinklistNodeT *prevNode =
        (IsLinklistNodePtrEqual(deleteNode->prev, groupPtr) ? &group->node :
                                                              IdxNodePtr2NodeAddr(idxCtx, deleteNode->prev));
    if (prevNode == NULL) {
        DB_LOG_DBG_ERROR(GMERR_NO_DATA, "|HashLinklist| prev node is null");
        return GMERR_NO_DATA;
    }
    IdxNodePtrT deleteNodePtr = prevNode->next;
    SetLinklistNodePtr(&prevNode->next, deleteNode->next);
    if (IsLinklistNodePtrEqual(deleteNodePtr, group->lastNodePtr)) {
        if (IsLinklistNodePtrValid(deleteNode->next)) {
            LinklistGroupT *nextGroup = IdxNodePtr2GroupAddr(idxCtx, deleteNode->next);
            if (nextGroup == NULL) {
                DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
                    "|HashLinklist| can not get next group, indexId %" PRIu32 ".", idxCtx->idxMetaCfg.indexId);
                return GMERR_UNEXPECTED_NULL_VALUE;
            }
            SetLinklistNodePtr(&nextGroup->node.prev, deleteNode->prev);
        }
        SetLinklistNodePtr(&group->lastNodePtr, deleteNode->prev);
    } else {
        LinklistNodeT *nextNode = IdxNodePtr2NodeAddr(idxCtx, deleteNode->next);
        if (SECUREC_UNLIKELY(nextNode == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|HashLinklist| Unable to get node addr.");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        SetLinklistNodePtr(&nextNode->prev, deleteNode->prev);
    }
    group->nodeCnt--;
    return HashLinklistNodeMemRecycle(idxCtx, deleteNode, deleteNodePtr, 1u);
}

static Status EraseOtherNodeFromGroup(
    const IndexCtxT *idxCtx, LinklistGroupT *group, IdxNodePtrT groupPtr, HashLookupParaT findPara)
{
    bool fdMarkDeletedNode = findPara.isGC;
    LinklistNodeResultT findResult = {.node = NULL, .isFound = false};
    Status ret = LinklistFindNode(idxCtx, group, findPara.addr, (uint16_t)fdMarkDeletedNode, &findResult);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (!findResult.isFound) {
        return GMERR_NO_DATA;
    }
    DB_POINTER(findResult.node);
    if (findPara.isGC || findPara.isErase) {
        // 1. Trx commit, start GC, physically remove node whose delete mark is set
        // 2. Trx rollback, undo insertion, physically remove inserted node
        return ListGroupEraseNode(idxCtx, group, groupPtr, findResult.node);
    }
    if (idxCtx->idxMetaCfg.indexMultiVersionType == INDEX_MULTI_VERSION_TYPE) {
        return GMERR_OK;
    }
    // Trx on the fly, set delete mark on node instead of physically remove node
    findResult.node->prev.isDeleted = 1u;
    return GMERR_OK;
}

/* NOTE:
 * WHAT THIS FUNC DOES:
 * delete foundNode in the group, not first node which is handled elsewhere
 *
 * 1. for physical deletion, 2 possibilities:
 *  - trx commit, start GC, findNode is guaranteed that isDelete == 1
 *  - trx rollback, undo insertion, findNode is guaranteed that isDelete == 0
 * 2. for mark deletion:
 *  - trx on the fly, findNode is guaranteed that isDelete == 0
 *
 * find node within a group & remove that node from group
 * NOTE: removal of first node is handled elsewhere
 *
 * FYI:
 * 事务执行 ee
 * 1. insert(key, addr)  -- 头插法
 * 2. delete(key, addr)  -- isDelete change 0 -> 1  √
 *
 * 事务提交
 * 1. commit_insert()  -- 无GC操作
 * 2. commit_delete() -- GC操作物理删除一个 isDelete == 1 的 addr  √
 * 3. rollback_insert(key, addr): after rollback, no addr whose isDelete == 0 exists.
 *    - 物理删除 isDelete == 0 的 addr  √
 * 4. rollback_delete(key, addr): after rollback, there exist a addr whose isDelete == 0.
 *    - if an addr whose isDelete == 1 is found, set isDelete = 0
 *    - else, addr not found, insert one
 *
 * WHAT THIS FUNC DOES:
 * delete a node from group, 2 possibilities:
 * - delete first node
 * - delete ordinary node
 *
 * 1. for physical deletion, 2 possibilities:
 *  - trx commit, start GC, find a node whose isDelete == 1 and remove that node
 *  - trx rollback, undo insertion, find a node whose isDelete == 0 and remove that node
 * 2. for mark deletion:
 *  - trx on the fly, find a node whose isDelete == 0 && set isDelete to 1
 */
static Status RemoveNode(const IndexCtxT *idxCtx, LinklistParaT *removePara, HashLookupParaT findPara)
{
    DB_POINTER3(idxCtx, removePara, removePara->group);
    LinklistNodeT *firstNode = &removePara->group->node;
    TupleAddr addr = findPara.addr;
    // if first node matches addr
    TupleAddr firstNodeAddr = GetLinklistNodeAddr(idxCtx, firstNode);
    if (addr == firstNodeAddr) {
        if (findPara.isGC) {
            // Trx commit, start GC, physically remove node whose delete mark is set
            if (firstNode->prev.isDeleted == 1u ||
                idxCtx->idxMetaCfg.indexMultiVersionType == INDEX_MULTI_VERSION_TYPE) {
                return EraseFirstNodeFromGroup(idxCtx, removePara);
            }
            return GMERR_OK;
        }
        if (findPara.isErase && firstNode->prev.isDeleted == 0u) {
            // Trx rollback, undo insertion, physically remove inserted node
            return EraseFirstNodeFromGroup(idxCtx, removePara);
        }
        if (idxCtx->idxMetaCfg.indexMultiVersionType == INDEX_MULTI_VERSION_TYPE) {
            return GMERR_OK;
        }
        if (!findPara.isGC && !findPara.isErase && firstNode->prev.isDeleted == 0u) {
            // Trx on the fly, set delete mark on node instead of physically remove node
            firstNode->prev.isDeleted = 1u;
            return GMERR_OK;
        }
    }
    return EraseOtherNodeFromGroup(idxCtx, removePara->group, removePara->groupPtr, findPara);
}

// remove a whole group (mark deletion)
static Status MarkDeleteGroup(const IndexCtxT *idxCtx, LinklistGroupT *group)
{
    DB_POINTER2(idxCtx, group);
    uint32_t nodeCnt = group->nodeCnt;
    LinklistNodeT *currentNode = &group->node;
    if (idxCtx->idxMetaCfg.indexMultiVersionType != INDEX_MULTI_VERSION_TYPE) {
        currentNode->prev.isDeleted = 1u;
    }
    for (uint32_t i = 1; i < nodeCnt; i++) {
        // move to next node
        currentNode = IdxNodePtr2NodeAddr(idxCtx, currentNode->next);
        if (currentNode == NULL) {
            HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
            DB_LOG_DBG_ERROR(GMERR_NO_DATA, "|HashLinklist| no second node in this group, indexId %" PRIu32 ".",
                ht->idxBase.indexCfg.indexId);
            return GMERR_NO_DATA;
        }
        if (idxCtx->idxMetaCfg.indexMultiVersionType != INDEX_MULTI_VERSION_TYPE) {
            currentNode->prev.isDeleted = 1u;
        }
    }
    return GMERR_OK;
}

/*
 * remove a whole group (real deletion)
 * always remove second node, till there is only 1 node in group, then remove first node
 */
static Status EraseGroup(const IndexCtxT *idxCtx, LinklistParaT *removePara)
{
    DB_POINTER3(idxCtx, removePara, removePara->group);
    LinklistGroupT *group = removePara->group;
    uint32_t nodeCnt = group->nodeCnt;
    IdxNodePtrT groupPtr = removePara->groupPtr;
    LinklistNodeT *firstNode = &group->node;
    for (uint32_t i = 1; i < nodeCnt; i++) {
        // always delete secNode
        LinklistNodeT *foundNode = IdxNodePtr2NodeAddr(idxCtx, firstNode->next);
        Status ret = ListGroupEraseNode(idxCtx, group, groupPtr, foundNode);
        if (ret != GMERR_OK) {
            HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
            DB_LOG_DBG_ERROR(
                ret, "|HashLinklist| Unable to erase node, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
            return ret;
        }
    }
    return EraseFirstNodeIn1NodeGroup(idxCtx, removePara);
}

inline static Status RemoveGroup(const IndexCtxT *idxCtx, LinklistParaT *removePara, bool isErase)
{
    DB_POINTER(idxCtx);
    return isErase ? EraseGroup(idxCtx, removePara) : MarkDeleteGroup(idxCtx, removePara->group);
}

// traverse the whole linklist to remove a node or a group of nodes
Status LinklistRemove(const IndexCtxT *idxCtx, LinklistParaT *removePara, IndexKeyT hashKey, HashLookupParaT findPara)
{
    DB_POINTER3(idxCtx, removePara, removePara->group);
    LinklistGroupT *currentGroup = removePara->group;
    IdxNodePtrT currentGroupPtr = removePara->groupPtr;
    HtHashCodeT hashCode = HtGetHashCode(hashKey);
    Status ret = GMERR_OK;
    while (currentGroup != NULL) {
        if (GroupIsMatch(currentGroup, hashCode)) {
            removePara->group = currentGroup;
            removePara->groupPtr = currentGroupPtr;
            return findPara.addr == HEAP_INVALID_ADDR ? RemoveGroup(idxCtx, removePara, findPara.isErase) :
                                                        RemoveNode(idxCtx, removePara, findPara);
        }
        removePara->lastNode = (currentGroup->nodeCnt == 1u ? &currentGroup->node :
                                                              IdxNodePtr2NodeAddr(idxCtx, currentGroup->lastNodePtr));
        ret = GetNextGroup(idxCtx, currentGroup, &currentGroupPtr);
        if (ret != GMERR_OK) {
            return ret;
        }
        currentGroup = IdxNodePtr2GroupAddr(idxCtx, currentGroupPtr);
        if (currentGroup == NULL && IsLinklistNodePtrValid(currentGroupPtr)) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "|HashLinklist| can not get current group");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
    }
    return GMERR_OK;
}

static Status DeleteIndex(IndexCtxT *idxCtx, IndexKeyT secKey, HashLookupParaT findPara)
{
    DB_POINTER2(idxCtx, secKey.keyData);
    LinklistBucketT *bucket = NULL;
    LinklistBucketHeadT *bucketHead = NULL;
    HtHashCodeT hashCode = HtGetHashCode(secKey);
    Status ret = GetBucketAndBucketHead(idxCtx, hashCode, &bucket, &bucketHead, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }
    DB_POINTER(bucket);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    // 无效属于正常情况，允许上层删除不存在数据
    if (!IsLinklistNodePtrValid(bucket->firstGroupPtr)) {
        return GMERR_NO_DATA;
    }
    LinklistGroupT *firstGroup = IdxNodePtr2GroupAddr(idxCtx, bucket->firstGroupPtr);
    if (firstGroup == NULL) {
        if (findPara.isGC || findPara.isUndo) {
            return GMERR_OK;  // GC没有fetch流程，直接根据key删除，key有可能已经被删除了导致group为空，属于正常情况
        }
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
            "|HashLinklist| first node page is null, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    LinklistParaT removePara = {
        .bucketHead = bucketHead,
        .lastNode = NULL,
        .group = firstGroup,
        .groupPtr = bucket->firstGroupPtr,
    };
    ret = LinklistRemove(idxCtx, &removePara, secKey, findPara);
    if ((ret == GMERR_OK) && (findPara.isErase || findPara.isGC)) {
        ht->entryUsed--;
    }
    return ret;
}

Status HashLinklistIndexDelete(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr, IndexRemoveParaT removePara)
{
    DB_POINTER3(idxCtx, idxKey.keyData, idxCtx->idxHandle);
    uint8_t cmpRes = (uint8_t)INDEX_FILTER_SATISFIED;
    Status ret = IdxCheckFilterSatisfied(idxCtx, idxKey, &cmpRes);
    if (cmpRes == (uint8_t)INDEX_FILTER_UNSATISFIED || ret != GMERR_OK) {
        return ret;
    }

    HashLookupParaT findPara = {
        .isUndo = false,
        .isRemove = true,
        .isGC = removePara.isGc,
        .isErase = removePara.isErase,
        .isMarkDelete = false,
        .addr = addr,
        .hashKey = {0},
        .hashCode = 0,
        .targetSlot = 0,
        .pageBody = NULL,
        .pageHead = NULL,
    };

    AUTO_INDEX_RW_LATCH_WLL(idxCtx, (ret = DeleteIndex(idxCtx, idxKey, findPara)));
    return (ret == GMERR_NO_DATA) ? GMERR_OK : ret;
}

Status HashLinklistIndexBatchDelete(
    IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum, IndexRemoveParaT removePara)
{
    DB_POINTER3(idxCtx, idxKey, addr);
    Status ret = GMERR_OK;
    IdxBatchBegin(idxCtx);
    for (uint32_t i = 0; i < batchNum; ++i) {
        ret = HashLinklistIndexDelete(idxCtx, idxKey[i], addr[i].addrOut, removePara);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret,
                "|SE-IDX|HashLinkList index batch delete unsucc, i %" PRIu32 "batch num:%" PRIu32 ", indexId %" PRIu32,
                i, batchNum, idxCtx->idxMetaCfg.indexId);
            break;
        }
    }
    IdxBatchEnd(idxCtx);
    return ret;
}

Status HashLinklistIndexBatchUpdate(
    IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo[], uint32_t batchNum, IndexRemoveParaT removePara)
{
    DB_POINTER2(idxCtx, updateInfo);
    Status ret = GMERR_OK;
    uint32_t indexId = idxCtx->idxMetaCfg.indexId;
    IdxBatchBegin(idxCtx);
    for (uint32_t i = 0; i < batchNum; ++i) {
        ret = HashLinklistIndexDelete(idxCtx, updateInfo[i].oldIdxKey, updateInfo[i].oldAddr, removePara);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret,
                "|HLL| index batch update unsucc when delete, i %" PRIu32 ",batch num %" PRIu32 ", indexId %" PRIu32, i,
                batchNum, indexId);
            break;
        }
        ret = HashLinklistIndexInsert(idxCtx, updateInfo[i].newIdxKey, updateInfo[i].newAddr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret,
                "|HLL| index batch update unsucc when insert, i %" PRIu32 ",batch num %" PRIu32 ", indexId %" PRIu32, i,
                batchNum, indexId);
            break;
        }
    }
    IdxBatchEnd(idxCtx);
    return ret;
}

static Status AllocPage(const IndexCtxT *idxCtx, uint8_t **page)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;
    uint32_t blockId = 0u;
    HashAllocPageParamT allocParam = {.tableSpaceIndex = idxCtx->idxMetaCfg.tableSpaceIndex, .isDir = false};
    Status ret =
        HtAllocMemPage(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, &allocParam, &blockId, &pageInfo, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "|HashLinklist| Unable to alloc mem page, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return ret;
    }
    DB_POINTER(pageInfo.virtAddr);
    (void)memset_s((void *)pageInfo.virtAddr, ht->pageSize, 0, ht->pageSize);
    if (page != NULL) {
        *page = pageInfo.virtAddr;
    }
    return GMERR_OK;
}

Status HashLinklistAllocNodeMem(IndexCtxT *idxCtx, uint16_t nodeSize, uint8_t **page, IdxNodePtrT *seNodePtr)
{
    DB_POINTER3(idxCtx, seNodePtr, page);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    PageHeadT *pageBase = NULL;
    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;
    Status ret;
    HashLinklistMemMetaT *hllMeta = &ht->memMgr.hllMeta;
    if (ht->memMgr.allocFirst) {
        ret = AllocPage(idxCtx, &pageInfo.virtAddr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                ret, "|HashLinklist| Unable to alloc page, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
            return ret;
        }
        ht->memMgr.allocFirst = false;
    } else {
        ret = HtGetMemPage(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, hllMeta->pageUsedCount, SE_INVALID_PAGE_ID,
            false, &pageInfo);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                ret, "|HashLinklist| Unable to get mem page, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
            return ret;
        }
    }
    pageBase = HtGetPageHead(pageInfo.virtAddr);
    if ((uint32_t)(nodeSize + pageBase->beginPos) > ht->pageSize) {
        uint8_t *pageExpand = NULL;
        ret = AllocPage(idxCtx, &pageExpand);
        if (ret != GMERR_OK) {
            return ret;
        }
        hllMeta->pageUsedCount++;
        pageBase = HtGetPageHead(pageExpand);
    }
    *page = (uint8_t *)((uintptr_t)pageBase + pageBase->beginPos);
    seNodePtr->blockId = (uint16_t)(hllMeta->pageUsedCount & 0x7FFFu);
    seNodePtr->offset = pageBase->beginPos;
    seNodePtr->isDeleted = 0u;
    pageBase->beginPos = (uint16_t)(pageBase->beginPos + nodeSize);
    hllMeta->usedMemSize += nodeSize;
    return GMERR_OK;
}

void HashLinklistInitBucketPage(uint8_t *bucketPage, uint32_t pageSize, uint32_t bucketNumPerPage)
{
    (void)memset_s((void *)bucketPage, pageSize, 0, pageSize);
    LinklistBucketT *bucket = (LinklistBucketT *)(void *)(bucketPage + sizeof(LinklistBucketHeadT));
    for (uint32_t slot = 0; slot < bucketNumPerPage; slot++) {
        SetLinklistNodePtr(&bucket->firstGroupPtr, HtNullNodePtr());
        bucket++;
    }
}

static Status InitBucket(HashTableT *ht, uint32_t hashCap)
{
    DB_POINTER(ht);
    uint32_t maxPageSize = ht->pageSize;
    uint32_t bucketPageSize = (uint32_t)(maxPageSize - sizeof(LinklistBucketHeadT));
    uint32_t bucketNumPerPage = (uint32_t)(bucketPageSize / sizeof(LinklistBucketT));
    DB_ASSERT(bucketNumPerPage != 0u);
    uint32_t bucketPageNum = (hashCap - 1u) / bucketNumPerPage + 1u;  // at least 1
    HashLinklistMemMetaT *hllMeta = &ht->memMgr.hllMeta;
    hllMeta->bucketNum = bucketPageNum * bucketNumPerPage;
    hllMeta->bucketNumPerPage = bucketNumPerPage;
    hllMeta->bucketPageNum = bucketPageNum;
    hllMeta->pageUsedCount = 0u;

    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;
    HashMemRunCtxT memRunCtx = {0};
    Status ret = HashMemRunCtxInit(&ht->memMgr, ht->pageAddr, ht->pageAddrArrCapacity, &memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "|HashLinklist| Unable to init hash mem run ctx, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return ret;
    }
    // allocate all bucket page，目前hashLinkList的Dir页也按照Seg页处理，存在swizzleArray里
    HashAllocPageParamT allocParam = {.tableSpaceIndex = ht->idxBase.indexCfg.tableSpaceIndex, .isDir = false};
    for (uint32_t id = 0u; id < bucketPageNum; id++) {
        ret = HtAllocMemPage(&memRunCtx, &allocParam, &id, &pageInfo, NULL);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                ret, "|HashLinklist| Unable to alloc mem page, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
            HashLinklistSwizzleReset(&memRunCtx, NULL, 0);
            return ret;
        }
        HashLinklistInitBucketPage(pageInfo.virtAddr, ht->pageSize, bucketNumPerPage);
        hllMeta->pageUsedCount++;
    }
    DB_ASSERT(hllMeta->pageUsedCount == bucketPageNum);
    return GMERR_OK;
}

static Status GetInstanceAndMemCtx(uint16_t instanceId, SeInstanceT **seInstance, DbMemCtxT **hashLinlistMemCtx)
{
    SeInstanceT *instance = (SeInstanceT *)SeGetInstance(instanceId);
    if (instance == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|HashLinklist| se instance is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbMemCtxT *memCtx = (DbMemCtxT *)DbGetShmemCtxById(instance->hashLinklistShmMemCtxId, instanceId);
    if (memCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|HashLinklist| mem context is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *seInstance = instance;
    *hashLinlistMemCtx = memCtx;
    return GMERR_OK;
}

static Status HashLinklistAllocPageArrMem(SeRunCtxT *seRunCtx, HashTableT *ht)
{
    // truncate场景下，pageAddr有效，不重新申请内存
    if (DbIsShmPtrValid(ht->pageAddr)) {
        return GMERR_OK;
    }
    SeInstanceT *instance = NULL;
    DbMemCtxT *htMemCtx = NULL;
    Status ret = GetInstanceAndMemCtx(seRunCtx->instanceId, &instance, &htMemCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    DB_UNUSED(instance);
    uint32_t allocSize = ht->pageAddrArrCapacity * (uint32_t)sizeof(ShmemPtrT);
    /* 索引drop时删除 */
    PageIdT *pageIdArr = (PageIdT *)SeShmAlloc(htMemCtx, allocSize, &ht->pageAddr);
    if (pageIdArr == NULL || !DbIsShmPtrValid(ht->pageAddr)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY,
            "unable to alloc page array, size: %" PRIu32 ", index id: %" PRIu32, allocSize,
            ht->idxBase.indexCfg.indexId);
        return GMERR_OUT_OF_MEMORY;
    }
    for (uint32_t i = 0; i < ht->pageAddrArrCapacity; i++) {
        pageIdArr[i] = SE_INVALID_PAGE_ADDR;
    }
    return GMERR_OK;
}

Status HashLinklistCreateHashTable(SeRunCtxT *seRunCtx, HashTableT *ht, uint32_t hashCap)
{
    DB_POINTER(ht);
    Status ret = HashLinklistAllocPageArrMem(seRunCtx, ht);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t cap = (hashCap != 0u) ? hashCap : 1u;
    ht->entryUsed = 0u;

    ret = InitBucket(ht, cap);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "|HashLinklist| init bucket, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return ret;
    }
    HashLinklistMemMetaT *hllMeta = &ht->memMgr.hllMeta;
    hllMeta->groupCnt = 0u;
    hllMeta->maxGroupLen = 0u;
    hllMeta->usedMemSize = hllMeta->pageUsedCount * (ht->pageSize + (uint32_t)HASH_PAGE_HEAD_SIZE);
    DB_LOG_DBG_INFO("|HashLinklist| create hash table indexId %" PRIu32, ht->idxBase.indexCfg.indexId);
    return GMERR_OK;
}

static void InitHashTable(
    SeInstanceT *seInstance, const IndexMetaCfgT *idxCfg, HashTableT *hashTable, uint32_t pagePtrArrCapacity)
{
    DB_POINTER3(seInstance, idxCfg, hashTable);
    hashTable->idxBase.indexCfg = *idxCfg;
    hashTable->pageAddrArrCapacity = pagePtrArrCapacity;
    hashTable->pageAddr = DB_INVALID_SHMPTR;
    DbRWLatchInit(&(hashTable->idxBase.idxLatch));
    hashTable->idxBase.shmemCtxId = seInstance->hashIndexShmMemCtxId;
    hashTable->hashLinklistShmMemCtxId = seInstance->hashLinklistShmMemCtxId;
    hashTable->pageSize = (uint32_t)(seInstance->seConfig.pageSize * DB_KIBI - HASH_PAGE_HEAD_SIZE);
    IdxMarkUnConstructed(&hashTable->idxBase);
    hashTable->memMgr.allocFirst = true;
    hashTable->memMgr.base.isVertexUseRsm = idxCfg->isVertexUseRsm;
    hashTable->memMgr.base.fileId = SeGetNewTrmId(seInstance);
    hashTable->memMgr.base.seInstanceId = seInstance->instanceId;
    hashTable->memMgr.base.tableSpaceIndex = idxCfg->tableSpaceIndex;
    hashTable->idxBase.validCode = HASH_INDEX_VALID_CODE;
    hashTable->hashInsertCnt = 0u;
    hashTable->hashCollisionCnt = 0u;
    hashTable->htVersion = 0u;
}

static void InitFreeList(HashTableT *ht)
{
    for (uint32_t i = 0u; i < HASHLINKLIST_FREELIST_COUNT; i++) {
        ht->memMgr.hllMeta.nodePtr[i] = HtNullNodePtr();
    }
}

Status HashLinklistIndexCreate(SeRunCtxT *seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr)
{
    DB_POINTER(idxShmAddr);
    *idxShmAddr = DB_INVALID_SHMPTR;
    SeInstanceT *seInstance = NULL;
    DbMemCtxT *memCtx = NULL;
    Status ret = GetInstanceAndMemCtx(seRunCtx->instanceId, &seInstance, &memCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "|HashLinklist| Unable to get seInstance and memctx, indexId %" PRIu32 ".", idxCfg.indexId);
        return ret;
    }
    DB_POINTER2(seInstance, memCtx);
    uint32_t hashCap = idxCfg.indexCap == 0u ? 1u : idxCfg.indexCap;
    uint32_t pageSize = (uint32_t)(seInstance->seConfig.pageSize * DB_KIBI - HASH_PAGE_HEAD_SIZE);
    uint32_t bucketPageSize = (uint32_t)(pageSize - sizeof(LinklistBucketHeadT));
    uint32_t bucketNumPerPage = (uint32_t)(bucketPageSize / sizeof(LinklistBucketT));
    DB_ASSERT(bucketNumPerPage != 0u);
    uint32_t pagePtrArrCapacity = (hashCap - 1u) / bucketNumPerPage + 1u;  // at least 1
    uint32_t htSize = (uint32_t)(sizeof(HashTableT));

    ShmemPtrT htAddr = {0};
    /* 索引实例，在对应drop接口销毁 */
    HashTableT *ht = SeShmAlloc(memCtx, htSize, &htAddr);
    if (ht == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "|HashLinklist| Unable to alloc hash table");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(ht, htSize, 0, htSize);
    InitHashTable(seInstance, &idxCfg, ht, pagePtrArrCapacity);

    /* 初始化hashswizzle，在对应drop/truncate接口销毁 */
    ret = HtSwizzleCreate(&ht->memMgr, ht->hashLinklistShmMemCtxId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret,
            "|HashLinklist| Unable to create ht swizzle when create hash linklist index, indexId %" PRIu32 ".",
            idxCfg.indexId);
        DbShmemCtxFree(memCtx, htAddr);
        return ret;
    }
    InitFreeList(ht);
    *idxShmAddr = htAddr;
    return GMERR_OK;
}

Status HashLinklistIndexDrop(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    HashTableT *ht = (HashTableT *)DbShmPtrToAddr(idxShmAddr);
    if (ht == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
            "|HashLinklist| HC: hashtable is novalid. segid: %" PRIu32 " offset: %" PRIu32 ".", idxShmAddr.segId,
            idxShmAddr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (ht->idxBase.validCode == 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR,
            "|HashLinklist| repeat drop link list index, segid: %" PRIu32 " offset: %" PRIu32 ".", idxShmAddr.segId,
            idxShmAddr.offset);
        return GMERR_INTERNAL_ERROR;
    }
    // remove shmArray and free dir page (which directory stored)
    HashMemRunCtxT memRunCtx = {0};
    Status ret = HashMemRunCtxInit(&ht->memMgr, ht->pageAddr, ht->pageAddrArrCapacity, &memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "|HashLinklist| Unable to init mem run ctx, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return ret;
    }

    DbRWLatchW(&ht->idxBase.idxLatch);
    DbMemCtxT *memCtx = (DbMemCtxT *)DbGetShmemCtxById(ht->hashLinklistShmMemCtxId, seRunCtx->instanceId);
    if (memCtx == NULL) {
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
            "|HashLinklist| HCIN: hash's mem context is novalid, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    HashLinklistSwizzleDestroy(&memRunCtx, DbGetInstanceByMemCtx(memCtx));
    if (DbIsShmPtrValid(ht->pageAddr)) {
        DbShmemCtxFree(memCtx, ht->pageAddr);
    }
    ht->idxBase.validCode = 0u;
    DbRWUnlatchW(&ht->idxBase.idxLatch);
    DbShmemCtxFree(memCtx, idxShmAddr);
    return GMERR_OK;
}

static void ResetMetaData(HashTableT *ht)
{
    DB_POINTER(ht);
    PageIdT *pageArr = (PageIdT *)DbShmPtrToAddr(ht->pageAddr);
    if (pageArr == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE,
            "|hash linklist| unable to get page array addr for index: %" PRIu32 ", pageAddr:(%" PRIu32 ",%" PRIu32
            ", %" PRIu64 ")",
            ht->idxBase.indexCfg.indexId, ht->pageAddr.segId, ht->pageAddr.offset, *(uint64_t *)(void *)&ht->pageAddr);
        return;
    }
    HashLinklistMemMetaT *hllMeta = &ht->memMgr.hllMeta;
    hllMeta->groupCnt = 0u;
    hllMeta->maxGroupLen = 0u;
    hllMeta->usedMemSize = 0u;
    ht->memMgr.allocFirst = true;
    uint32_t pagePtrArrCapacity = ht->pageAddrArrCapacity;
    for (uint32_t i = 0; i < pagePtrArrCapacity; i++) {
        pageArr[i] = SE_INVALID_PAGE_ADDR;
    }
    ht->entryUsed = 0u;
    ht->htVersion++;
    hllMeta->pageUsedCount = 0u;
}

void HashLinklistQuickRestore(
    HashMemRunCtxT *memRunCtx, HashTableT *ht, const PageIdT *pageCache, uint32_t bucketPageNum)
{
    HashLinklistMemMetaT *hllMeta = &ht->memMgr.hllMeta;
    uint32_t itemId;
    HashPageDescT *item = NULL;
    DbArrayAddrT itemAddr;
    for (uint32_t id = 0u; id < bucketPageNum; id++) {
        Status ret = DbShmArrayGetItem(&memRunCtx->memMgr->base.hashSwizzleArray, &itemId, (void **)&item, &itemAddr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                ret, "|HLL| restore get item, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
            return;
        }
        DB_ASSERT(itemId == id);
        uint8_t *page = NULL;
        StatusInter retInter = MdGetPage(memRunCtx->mdMgr, pageCache[id], &page, ENTER_PAGE_NORMAL, false);
        if (retInter != STATUS_OK_INTER) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                DbGetExternalErrno(retInter), "|HLL| restore page, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
            DB_ASSERT(false);  // should not be here
        }
        item->pageId = pageCache[id];
        uint8_t *indexPage = (uint8_t *)page + (uint32_t)HASH_PAGE_HEAD_SIZE;
        HashLinklistInitBucketPage(indexPage, ht->pageSize, hllMeta->bucketNumPerPage);
        hllMeta->pageUsedCount++;
    }
    memRunCtx->memMgr->base.version++;
    IdxConstructedFinish(&ht->idxBase);
    DB_ASSERT(ht->memMgr.hllMeta.bucketPageNum != 0);
}

Status HashLinklistIndexTruncate(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    HashTableT *ht = (HashTableT *)DbShmPtrToAddr(idxShmAddr);
    if (ht == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
            "|HashLinklist| HT: Hash table is novalid. segid: %" PRIu32 " offset: %" PRIu32 ".", idxShmAddr.segId,
            idxShmAddr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    // Lock the table. Determine whether the table has been opened by others or not.
    // If not, it can be truncated. Otherwise, wrong code is returned.
    uint32_t idxId = ht->idxBase.indexCfg.indexId;
    DbRWLatchW(&ht->idxBase.idxLatch);
    // haven't open
    if (!IdxIsConstructed(&ht->idxBase)) {
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        return GMERR_OK;
    }
    if (ht->idxBase.validCode != HASH_INDEX_VALID_CODE) {
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_INTERNAL_ERROR, "|HLL| Hash table has been dropped, indexId %" PRIu32 ".", idxId);
        return GMERR_INTERNAL_ERROR;
    }
    // clear shmArray and free dir page (which directory stored)
    HashMemRunCtxT memRunCtx = {0};
    Status ret = HashMemRunCtxInit(&ht->memMgr, ht->pageAddr, ht->pageAddrArrCapacity, &memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HLL| Unable to init hash mem run ctx, indexId %" PRIu32 ".", idxId);
        return ret;
    }
    DB_ASSERT(ht->memMgr.hllMeta.bucketPageNum != 0);  // at least 1
    PageIdT *pageCache = DB_MALLOC(sizeof(PageIdT) * ht->memMgr.hllMeta.bucketPageNum);
    if (pageCache == NULL) {
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "|HLL| alloc trc page, indexId %" PRIu32 ".", idxId);
        return GMERR_OUT_OF_MEMORY;
    }
    HashLinklistSwizzleReset(&memRunCtx, pageCache, ht->memMgr.hllMeta.bucketPageNum);
    InitFreeList(ht);
    ResetMetaData(ht);
    IdxMarkUnConstructed(&ht->idxBase);
    HashLinklistQuickRestore(&memRunCtx, ht, pageCache, ht->memMgr.hllMeta.bucketPageNum);
    DbRWUnlatchW(&ht->idxBase.idxLatch);
    DB_FREE(pageCache);
    return ret;
}

static void UnInitIndexRunCtx(IndexCtxT *idxCtx)
{
    idxCtx->idxShmAddr = (ShmemPtrT){0};
    idxCtx->idxMetaCfg = (IndexMetaCfgT){0};
    idxCtx->idxHandle = NULL;
    idxCtx->idxShmAddr = (ShmemPtrT){0};
}

Status HashLinklistIndexOpen(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t indexId = ht->idxBase.indexCfg.indexId;
    idxCtx->batchLocked = false;
    idxCtx->isAcquireLockByTryOnce = false;
    ((HashIndexCtxT *)idxCtx->idxRunCtx)->hashIndexVersion = ht->htVersion;
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)(void *)idxCtx->idxRunCtx)->memRunCtx;
    Status ret = HashMemRunCtxInit(&ht->memMgr, ht->pageAddr, ht->pageAddrArrCapacity, memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to init hash mem run ctx, indexId %" PRIu32, indexId);
        return ret;
    }
    // construct ht while Hashtable Open
    ret = VerifyAndConstructHashTable(idxCtx->idxOpenCfg.seRunCtx, ht);
    if (ret != GMERR_OK) {
        HashMemRunCtxUnInit(memRunCtx);
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to construct ht in open, indexId %" PRIu32, indexId);
        return ret;
    }
    // pageArr内存在open时候申请，需要刷新memRunCtx对应值
    ret = HashMemRunCtxUpdatePageId(memRunCtx, ht->pageAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to update page id in open, indexId %" PRIu32, indexId);
        HashMemRunCtxUnInit(memRunCtx);
    }
    return ret;
}

void HashLinklistIndexClose(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    UnInitIndexRunCtx(idxCtx);
}

static LinklistIteratorT *LinkListIterInit(IndexCtxT *idxCtx, const IndexScanCfgT *scanCfg)
{
    DB_POINTER2(idxCtx, scanCfg);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    LinklistIteratorT *iter = (LinklistIteratorT *)(void *)IdxGetIterator(idxCtx);
    DB_ASSERT(iter != NULL);
    iter->key = *scanCfg->leftKey;
    iter->htVersion = ht->htVersion;
    iter->staticAddrsNum = 0;
    iter->staticArrCursor = 0;
    iter->dynAddrs = (DbDynArrayT){0};
    iter->dynArrCursor = 0;
#ifdef SYS32BITS
    iter->dumpAddrsMap = (DbOamapT){0};
#endif
    return iter;
}

#define HASH_LINK_LIST_DYN_ADDRS_INIT_SIZE 1024u

static Status HashLinkListDumpExtraAddrsHandleLastNode(
    LinklistIteratorT *indexIter, IdxNodePtrT ptr, uint32_t cursor, uint32_t nodeCnt)
{
    // 最后一个节点的next可以是NULL，否则内存可能有问题
    if (SECUREC_LIKELY(!IsLinklistNodePtrValid(ptr))) {
        if (cursor != nodeCnt - 1) {
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
                "|HashLinklist| The linked list is novalid, curCnt %" PRIu32 ", nodeCnt %" PRIu32 ".", cursor, nodeCnt);
        }
        /* 此处可能是与删除并发，因此只是记录一行日志，用于认为扫描可能不全的证据，但非问题 */
        return GMERR_OK;
    }
    DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR,
        "|HashLinklist| %" PRIu32 "th is NULL, block %" PRIu32 " offset %" PRIu32 ".", cursor, ptr.blockId, ptr.offset);
    DbDynArrayDestroy(&indexIter->dynAddrs);
    return GMERR_INTERNAL_ERROR;
}

static Status HashLinkListDumpExtraAddrs(
    IndexCtxT *idxCtx, LinklistIteratorT *indexIter, uint32_t nodeCursor, uint32_t nodeCnt, LinklistNodeT *node)
{
    DB_POINTER3(idxCtx, indexIter, node);
    DB_ASSERT(nodeCursor < nodeCnt);  // 外部保证了该逻辑
    /* 逻辑稳定后可删除该判断 */
    if (SECUREC_UNLIKELY(idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_UNEXPECTED_NULL_VALUE, "|HashLinklist| session's memctx is NULL when begin to scan.");
    }
    /* HashLinklistIndexEndScan中释放，由上层保证流程 */
    Status ret = DbDynArrayInit(&indexIter->dynAddrs, idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx, sizeof(TupleAddr),
        DB_MIN(nodeCnt - nodeCursor, HASH_LINK_LIST_DYN_ADDRS_INIT_SIZE), nodeCnt - nodeCursor);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret,
            "|HashLinklist| dynAddrs unable to init, nodeCursor %" PRIu32 ", nodeCnt %" PRIu32 ".", nodeCursor,
            nodeCnt);
        return ret;
    }
    uint32_t itemId;
    LinklistNodeT *curNode = node;
    for (uint32_t i = nodeCursor; i < nodeCnt; i++) {
        TupleAddr firstNodeAddr = GetLinklistNodeAddr(idxCtx, curNode);
#ifdef SYS32BITS
        ret = DbOamapInsert(&indexIter->dumpAddrsMap, DbHash32((const uint8_t *)&firstNodeAddr, sizeof(TupleAddr)),
            &firstNodeAddr, &firstNodeAddr, NULL);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_WARN(ret, "find remain index, skip it, indexId: %" PRIu32 ".", idxCtx->idxMetaCfg.indexId);
            // 将firstNodeAddr置为HEAP_INVALID_ADDR，不会统计该条数据，同时可以复用last node处理逻辑
            firstNodeAddr = HEAP_INVALID_ADDR;
        }
#endif
        if (SECUREC_LIKELY(firstNodeAddr != HEAP_INVALID_ADDR)) {
            TupleAddr *addrPtr = (TupleAddr *)DbDynArrayAllocItem(&indexIter->dynAddrs, &itemId);
            if (SECUREC_UNLIKELY(addrPtr == NULL)) {
                DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY,
                    "|HashLinklist| dynAddrs alloc %" PRIu32 " times is NULL, total allocCnt %" PRIu32 ".", i,
                    DbDynArrayGetAllocNum(&indexIter->dynAddrs));
                DbDynArrayDestroy(&indexIter->dynAddrs);
                return GMERR_OUT_OF_MEMORY;
            }
            *addrPtr = firstNodeAddr;
        }
        IdxNodePtrT ptr = curNode->next;
        curNode = IdxNodePtr2NodeAddr(idxCtx, ptr);
        if (SECUREC_LIKELY(curNode != NULL)) {
            continue;
        }
        return HashLinkListDumpExtraAddrsHandleLastNode(indexIter, ptr, i, nodeCnt);
    }
    return GMERR_OK;
}

static Status HashLinkListDumpAddrs(IndexCtxT *idxCtx, LinklistGroupResultT foundResult, LinklistIteratorT *indexIter)
{
    DB_POINTER3(idxCtx, foundResult.group, indexIter);
    Status ret = GMERR_OK;
#ifdef SYS32BITS
    ret = DbOamapInit(&indexIter->dumpAddrsMap, HASH_LINK_LIST_ITER_MAX_STATIC_ADDR_NUM, DbOamapUint64Compare,
        idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx, true);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Unable to init dump addrs map.");
        return ret;
    }
#endif

    uint32_t nodeCnt = foundResult.group->nodeCnt;
    uint32_t nodeCursor = 0;
    LinklistNodeT *curNode = &foundResult.group->node;
    /* add addr to static array */
    while (curNode != NULL && nodeCursor < nodeCnt &&
           indexIter->staticAddrsNum < HASH_LINK_LIST_ITER_MAX_STATIC_ADDR_NUM) {
        nodeCursor++;
        TupleAddr firstNodeAddr = GetLinklistNodeAddr(idxCtx, curNode);
#ifdef SYS32BITS
        ret = DbOamapInsert(&indexIter->dumpAddrsMap, DbHash32((const uint8_t *)&firstNodeAddr, sizeof(TupleAddr)),
            &firstNodeAddr, &firstNodeAddr, NULL);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_WARN(ret, "find remain index, skip it, indexId: %" PRIu32 ".", idxCtx->idxMetaCfg.indexId);
            curNode = IdxNodePtr2NodeAddr(idxCtx, curNode->next);
            continue;
        }
#endif
        if (SECUREC_LIKELY(firstNodeAddr != HEAP_INVALID_ADDR)) {
            indexIter->staticAddrs[indexIter->staticAddrsNum++] = firstNodeAddr;
        }
        curNode = IdxNodePtr2NodeAddr(idxCtx, curNode->next);
    }
    // 扫描结束则直接返回，否则加入到动态内存数组中管理
    ret = (curNode == NULL || nodeCursor == nodeCnt) ?
              GMERR_OK :
              HashLinkListDumpExtraAddrs(idxCtx, indexIter, nodeCursor, nodeCnt, curNode);
#ifdef SYS32BITS
    DbOamapDestroy(&indexIter->dumpAddrsMap);
#endif
    return ret;
}

// Given a key, set up iter to the first matched tuple's addr
Status HashLinklistIndexBeginScan(IndexCtxT *idxCtx, IndexScanCfgT scanCfg, IndexScanItrT *iter)
{
    DB_POINTER4(idxCtx, idxCtx->idxHandle, scanCfg.leftKey->keyData, iter);
    DB_POINTER2(idxCtx->idxOpenCfg.seRunCtx, idxCtx->idxOpenCfg.seRunCtx->sessionMemCtx);
    *iter = NULL;
    if (SECUREC_UNLIKELY(!HashLinkListHashTableIsConstructed(HtCastIdxAsHashTable(idxCtx->idxHandle)))) {
        return GMERR_OK;
    }
    HashRlockTL(idxCtx);
    // 1. get the linked list
    LinklistBucketT *bucket = NULL;
    HtHashCodeT hashCode = HtGetHashCode(*scanCfg.leftKey);
    Status ret = GetBucketAndBucketHead(idxCtx, hashCode, &bucket, NULL, NULL);
    if (ret != GMERR_OK) {
        HashRUnlockTL(idxCtx);
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to get bucket and bucket head");
        return ret;
    }
    DB_POINTER(bucket);
    /* indexIter必定不为NULL */
    LinklistIteratorT *indexIter = LinkListIterInit(idxCtx, &scanCfg);
    *iter = (IndexScanItrT)(void *)indexIter;

    LinklistGroupT *firstGroup = IdxNodePtr2GroupAddr(idxCtx, bucket->firstGroupPtr);
    if (firstGroup == NULL) {
        HashRUnlockTL(idxCtx);
        // no wrong is reported for the empty table
        return GMERR_OK;
    }
    // 2. get the group whose hashcode matches the key's hashcode from linked list
    LinklistGroupResultT foundResult = {.group = NULL, .isFound = false};
    ret = LinklistGetGroup(idxCtx, firstGroup, bucket->firstGroupPtr, *scanCfg.leftKey, &foundResult);
    if (ret != GMERR_OK) {
        HashRUnlockTL(idxCtx);
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to get group.");
        return ret;
    }
    if (!foundResult.isFound) {
        HashRUnlockTL(idxCtx);
        return GMERR_OK;
    }

    ret = HashLinkListDumpAddrs(idxCtx, foundResult, indexIter);
    HashRUnlockTL(idxCtx);

    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Cannot loaded all addrs to iter");
        return ret;
    }
    /* iter must be not null if ret is OK */
    DB_ASSERT(*iter != NULL);
    return GMERR_OK;
}

static Status ScanCheckInputParamValid(const HashTableT *ht, const LinklistIteratorT *itr)
{
    DB_POINTER2(ht, itr);
    if (ht->idxBase.indexCfg.idxConstraint == PRIMARY) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "|HashLinklist| key type mismatch, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    if (itr->key.keyData == NULL || itr->key.keyLen == 0u) {
        DB_LOG_DBG_ERROR(GMERR_DATA_EXCEPTION, "|HashLinklist| novalid linklist buffer, indexId %" PRIu32 ".",
            ht->idxBase.indexCfg.indexId);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

inline static Status HashLinkListFetchReCheck(
    IndexCtxT *idxCtx, TupleAddr addr, const LinklistIteratorT *itr, bool *isFound)
{
    DB_POINTER3(idxCtx, itr, isFound);
    int32_t cmpRet;
    Status ret = idxCtx->idxOpenCfg.callbackFunc.keyCmp(idxCtx, itr->key, addr, &cmpRet, isFound);
    return SeIsFilterErrorCode(ret) ? GMERR_OK : ret;
}

static Status HashLinklistFetchOneInStaticAddrsArr(
    IndexCtxT *idxCtx, LinklistIteratorT *itr, TupleAddr *addrOut, bool *isFound)
{
    while (itr->staticArrCursor < itr->staticAddrsNum) {
        TupleAddr curAddr = itr->staticAddrs[itr->staticArrCursor];
        itr->staticArrCursor++;
        Status ret = HashLinkListFetchReCheck(idxCtx, curAddr, itr, isFound);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            if (ret != GMERR_LOCK_NOT_AVAILABLE) {
                DB_LOG_ERROR_AND_SET_LASTERR(
                    ret, "|HashLinklist| hash recheck unsucc when fetch in static addr, rowId: %" PRIu64, curAddr);
            }
            return ret;
        }
        if (*isFound) {
            if (addrOut != NULL) {
                *addrOut = curAddr;
            }
            return GMERR_OK;
        }
    }
    return GMERR_OK;
}

static Status HashLinklistFetchOneInDynamicAddrsArr(
    IndexCtxT *idxCtx, LinklistIteratorT *itr, TupleAddr *addr, bool *isFound)
{
    while (itr->dynArrCursor < DbDynArrayGetItemNum(&itr->dynAddrs)) {
        TupleAddr *addrPtr = (TupleAddr *)DbDynArrayGetItemById(&itr->dynAddrs, itr->dynArrCursor);
        itr->dynArrCursor++;
        if (addrPtr == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_INTERNAL_ERROR, "|HashLinklist| hash scan unsucc when get item %" PRIu32, itr->dynArrCursor);
            return GMERR_INTERNAL_ERROR;
        }
        TupleAddr curAddr = *addrPtr;
        if (curAddr == HEAP_INVALID_ADDR) {
            continue;
        }
        Status ret = HashLinkListFetchReCheck(idxCtx, curAddr, itr, isFound);
        if (ret != GMERR_OK) {
            if (ret != GMERR_LOCK_NOT_AVAILABLE) {
                DB_LOG_ERROR_AND_SET_LASTERR(
                    ret, "|HashLinklist| hash recheck unsucc when fetch in dynamic addr, rowId: %" PRIu64, curAddr);
            }
            return ret;
        }
        if (*isFound) {
            if (addr != NULL) {
                *addr = curAddr;
            }
            return GMERR_OK;
        }
    }
    return GMERR_OK;
}

// get the next matched tuple addr
Status HashLinklistIndexScan(IndexCtxT *idxCtx, IndexScanItrT iter, TupleAddr *addr, bool *isFound)
{
    DB_POINTER3(idxCtx, isFound, idxCtx->idxHandle);
    *isFound = false;
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    if (SECUREC_UNLIKELY(!HashLinkListHashTableIsConstructed(ht))) {
        return GMERR_OK;
    }
    if (iter == NULL) {
        return GMERR_OK;
    }
    LinklistIteratorT *itr = (LinklistIteratorT *)(void *)iter;
    if (itr->htVersion != ht->htVersion) {
        // 这里scan 中间被truncate过，老版本的数据应该是被清除，所以认为扫描结束
        return GMERR_OK;
    }
    Status ret = ScanCheckInputParamValid(ht, itr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Scan check input param is novalid, indexId %" PRIu32 ".",
            ht->idxBase.indexCfg.indexId);
        return ret;
    }
    if (itr->staticArrCursor < itr->staticAddrsNum) {
        ret = HashLinklistFetchOneInStaticAddrsArr(idxCtx, itr, addr, isFound);
        if (ret != GMERR_OK || *isFound) {
            /* 接口内部有日志输出 */
            return ret;
        }
    }
    if (itr->dynArrCursor < DbDynArrayGetItemNum(&itr->dynAddrs)) {
        ret = HashLinklistFetchOneInDynamicAddrsArr(idxCtx, itr, addr, isFound);
        if (ret != GMERR_OK || *isFound) {
            /* 接口内部有日志输出 */
            return ret;
        }
    }
    return GMERR_OK;
}

ALWAYS_INLINE_C void HashLinklistIndexEndScan(const IndexCtxT *idxCtx, IndexScanItrT iter)
{
    DB_POINTER(idxCtx);
    DB_UNUSED(idxCtx);
    LinklistIteratorT *itr = (LinklistIteratorT *)(void *)iter;
    if (itr != NULL) {
        DbDynArrayDestroy(&itr->dynAddrs);
    }
}

Status HashLinklistIndexGetKeyCount(IndexCtxT *idxCtx, IndexKeyT idxKey, uint64_t *count)
{
    DB_POINTER3(idxCtx, idxKey.keyData, count);
    *count = 0;
    if (SECUREC_UNLIKELY(!HashLinkListHashTableIsConstructed(HtCastIdxAsHashTable(idxCtx->idxHandle)))) {
        return GMERR_OK;
    }
    IndexScanCfgT scanCfg = {
        .scanType = 0, .scanDirect = 0, .leftKey = &idxKey, .rightKey = NULL, .scanMode = (uint8_t)IDX_SCAN_BEGIN};
    IndexScanItrT iter = NULL;
    Status ret = HashLinklistIndexBeginScan(idxCtx, scanCfg, &iter);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to begin scan when get index key count.");
        return ret;
    }
    do {
        bool isFound = false;
        ret = HashLinklistIndexScan(idxCtx, iter, NULL, &isFound);
        if (ret != GMERR_OK || !isFound) {
            break;
        }
        (*count)++;
    } while (ret == GMERR_OK);
    HashLinklistIndexEndScan(idxCtx, iter);
    if (SECUREC_UNLIKELY(ret == GMERR_NO_DATA)) {
        return GMERR_OK;
    }
    if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to get index key count.");
    }
    return ret;
}

Status HashLinklistIndexUndoInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    DB_POINTER3(idxCtx, idxKey.keyData, idxCtx->idxHandle);
    DB_ASSERT(HashLinkListHashTableIsConstructed(HtCastIdxAsHashTable(idxCtx->idxHandle)));
    // Delete non-unique secondary indexes should not handle filters
    HashLookupParaT findPara = {
        .isUndo = true,
        .isRemove = true,
        .isGC = false,
        .isErase = true,
        .isMarkDelete = false,
        .addr = addr,
        .hashCode = 0,
        .hashKey = {0},
        .targetSlot = 0,
        .pageHead = NULL,
        .pageBody = NULL,
    };
    Status ret = GMERR_OK;
    AUTO_INDEX_RW_LATCH_WLL(idxCtx, (ret = DeleteIndex(idxCtx, idxKey, findPara)));
    return (ret == GMERR_NO_DATA) ? GMERR_OK : ret;
}

/*
 * rollback_delete(key, addr): after rollback, there exist a addr whose isDelete == 0.
 *    - if an addr whose isDelete == 1 is found, set isDelete = 0
 *    - else, addr not found, insert one
 */
Status HashLinklistIndexUndoRemove(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    DB_POINTER(idxCtx);
    DB_ASSERT(HashLinkListHashTableIsConstructed(HtCastIdxAsHashTable(idxCtx->idxHandle)));
    if (idxCtx->idxMetaCfg.indexMultiVersionType == INDEX_MULTI_VERSION_TYPE) {
        return GMERR_OK;
    }
    // 1. get the linked list
    LinklistBucketT *bucket = NULL;
    HtHashCodeT hashCode = HtGetHashCode(idxKey);
    Status ret = GetBucketAndBucketHead(idxCtx, hashCode, &bucket, NULL, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to get bucket and bucket head.");
        return ret;
    }
    DB_POINTER(bucket);
    LinklistGroupT *firstGroup = IdxNodePtr2GroupAddr(idxCtx, bucket->firstGroupPtr);
    // if linked list is empty, then addr not found, insert one
    if (firstGroup == NULL) {
        return HashLinklistIndexInsert(idxCtx, idxKey, addr);
    }
    // 2. get the group whose hashcode matches the key's hashcode from linked list
    LinklistGroupResultT foundResult = {.group = NULL, .isFound = false};
    ret = LinklistGetGroup(idxCtx, firstGroup, bucket->firstGroupPtr, idxKey, &foundResult);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // undo流程不可能失败，一定能取到group
        DB_ASSERT(false);
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to get group.");
        return ret;
    }
    if (!foundResult.isFound) {
        return HashLinklistIndexInsert(idxCtx, idxKey, addr);
    }
    LinklistGroupT *group = foundResult.group;
    DB_POINTER(group);
    // 3. if an addr whose isDelete == 1 is found, set isDelete = 0
    LinklistNodeT *firstNode = &group->node;
    // if first node matches addr
    if (addr == GetLinklistNodeAddr(idxCtx, firstNode)) {
        if (firstNode->prev.isDeleted == 0) {
            DB_LOG_INFO("HashLinkList first node exist same addr %" PRIu64 " when undo delete", addr);
        }
        firstNode->prev.isDeleted = 0u;
        return GMERR_OK;
    }
    // 如果找到相同addr的node，不管是否被标记删除，都置为未删除状态。防止回滚时候多插入数据。
    LinklistNodeResultT findResult = LinklistFindNodeForUndo(idxCtx, group, addr);
    if (findResult.isFound) {
        if (findResult.node->prev.isDeleted == 0) {
            DB_LOG_INFO("HashLinkList exist same addr %" PRIu64 " when undo delete", addr);
        }
        findResult.node->prev.isDeleted = 0u;
        return GMERR_OK;
    }
    return HashLinklistIndexInsert(idxCtx, idxKey, addr);
}

Status HashLinklistIndexUndoUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara)
{
    DB_POINTER(idxCtx);
    DB_UNUSED(removePara);
    DB_ASSERT(HashLinkListHashTableIsConstructed(HtCastIdxAsHashTable(idxCtx->idxHandle)));
    IdxBatchBegin(idxCtx);
    Status ret = HashLinklistIndexUndoInsert(idxCtx, updateInfo.oldIdxKey, updateInfo.oldAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "|SE-IDX|HashLinkList undo insert, , indexId %" PRIu32, idxCtx->idxMetaCfg.indexId);
    }
    ret = HashLinklistIndexInsert(idxCtx, updateInfo.newIdxKey, updateInfo.newAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-IDX|HashLinkList undo delete, , indexId %" PRIu32, idxCtx->idxMetaCfg.indexId);
    }
    IdxBatchEnd(idxCtx);
    return ret;
}

static Status PrepareView(uint16_t instanceId, HashTableT **ht, SeInstanceT **seInstance, ShmemPtrT htShmAddr)
{
    HashTableT *hashTable = DbShmPtrToAddr(htShmAddr);
    if (hashTable == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|HashLinklist| hash table is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    SeInstanceT *instance = (SeInstanceT *)SeGetInstance(instanceId);
    if (instance == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
            "|HashLinklist| se instance is novalid, indexId %" PRIu32 ".", hashTable->idxBase.indexCfg.indexId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *ht = hashTable;
    *seInstance = instance;
    return GMERR_OK;
}

Status HashLinklistViewGetPageSize(ShmemPtrT idxShmAddr, uint16_t instanceId, uint64_t *idxPageSize)
{
    DB_POINTER(idxPageSize);
    HashTableT *ht;
    SeInstanceT *seInstance;
    Status ret = PrepareView(instanceId, &ht, &seInstance, idxShmAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to prepare view.");
        return ret;
    }

    uint64_t perPageSize = (uint64_t)seInstance->seConfig.pageSize * DB_KIBI;
    uint64_t pageCount = (uint64_t)ht->memMgr.hllMeta.pageUsedCount;
    *idxPageSize = perPageSize * pageCount;
    return GMERR_OK;
}

Status HashLinklistIndexStatView(ShmemPtrT idxShmAddr, uint16_t instanceId, IndexStatisticsT *idxStat)
{
    DB_POINTER(idxStat);
    HashTableT *ht;
    SeInstanceT *seInstance;
    Status ret = PrepareView(instanceId, &ht, &seInstance, idxShmAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|HashLinklist| Unable to prepare view.");
        return ret;
    }
    IndexHashPerfStatT indexHashPerfStat;
    HashLinklistMemMetaT *hllMeta = &ht->memMgr.hllMeta;
    indexHashPerfStat.perPageSize = seInstance->seConfig.pageSize * DB_KIBI;
    indexHashPerfStat.pageCount = hllMeta->pageUsedCount;
    indexHashPerfStat.pageSize = (uint64_t)indexHashPerfStat.perPageSize * (uint64_t)indexHashPerfStat.pageCount;
    indexHashPerfStat.usedMemSize = hllMeta->usedMemSize;
    indexHashPerfStat.hashInsertCnt = ht->hashInsertCnt;
    indexHashPerfStat.hashCollisionCnt = ht->hashCollisionCnt;

    uint8_t hashCollisionRate = 0u;
    if (indexHashPerfStat.hashInsertCnt != 0u) {
        hashCollisionRate =
            (uint8_t)((indexHashPerfStat.hashCollisionCnt * DB_PERCENTAGE_BASE) / indexHashPerfStat.hashInsertCnt);
    }
    indexHashPerfStat.hashCollisionRate = hashCollisionRate;

    idxStat->hashLinklistIndex.indexHashPerfStat = indexHashPerfStat;
    idxStat->hashLinklistIndex.bucketCnt = hllMeta->bucketNum;
    idxStat->hashLinklistIndex.bucketSize = (uint32_t)(sizeof(LinklistBucketT));
    idxStat->hashLinklistIndex.nodeCnt = ht->entryUsed;
    idxStat->hashLinklistIndex.groupCnt = hllMeta->groupCnt;
    idxStat->hashLinklistIndex.maxGroupLen = hllMeta->maxGroupLen;
    if (hllMeta->groupCnt == 0u) {
        idxStat->hashLinklistIndex.averGroupLen = ht->entryUsed;
    } else {
        idxStat->hashLinklistIndex.averGroupLen = ht->entryUsed / hllMeta->groupCnt;
    }
    return GMERR_OK;
}

inline static void CalcIndexMemSize(
    const SeInstanceT *seInstance, uint32_t bucketCap, uint64_t entryCnt, uint64_t *size)
{
    DB_POINTER2(seInstance, size);
    // 1. bucket
    uint32_t cap = (bucketCap != 0u) ? bucketCap : 1u;
    uint32_t maxPageSize = (uint32_t)(seInstance->seConfig.pageSize * DB_KIBI - HASH_PAGE_HEAD_SIZE);
    uint32_t bucketPageSize = (uint32_t)(maxPageSize - sizeof(LinklistBucketHeadT));
    uint32_t bucketNumPerPage = (uint32_t)(bucketPageSize / sizeof(LinklistBucketT));
    DB_ASSERT(bucketNumPerPage != 0u);
    uint32_t bucketPageNum = (cap - 1u) / bucketNumPerPage + 1u;  // at least 1
    // 2. bucket + Node
    uint64_t nodeSize = entryCnt * (uint64_t)sizeof(LinklistNodeT);
    *size = (uint64_t)((uint64_t)bucketPageNum * seInstance->seConfig.pageSize * DB_KIBI + nodeSize);
}

Status HashLinklistIndexGetEstimateMemSize(
    uint16_t instanceId, IndexMetaCfgT idxMetaCfg, uint64_t count, uint32_t keyLen, uint64_t *size)
{
    DB_POINTER(size);
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(instanceId);
    if (seInstance == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|HashLinklist| se instance is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    CalcIndexMemSize(seInstance, idxMetaCfg.indexCap, count, size);
    return GMERR_OK;
}

size_t GetLinklistNodeAddrSize(const IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    return HashGetAddrSize(ht->heapTupleAddrMode);
}

void HashLinklistIdxAmInit(void)
{
    IdxFuncT hashLinklistIndexFuncHandle = IdxEmptyIdxFunc();
    hashLinklistIndexFuncHandle.idxCreate = HashLinklistIndexCreate;
    hashLinklistIndexFuncHandle.idxDrop = HashLinklistIndexDrop;
    hashLinklistIndexFuncHandle.idxTruncate = HashLinklistIndexTruncate;
    hashLinklistIndexFuncHandle.idxOpen = HashLinklistIndexOpen;
    hashLinklistIndexFuncHandle.idxClose = HashLinklistIndexClose;
    hashLinklistIndexFuncHandle.idxInsert = HashLinklistIndexInsert;
    hashLinklistIndexFuncHandle.idxBatchInsert = HashLinklistIndexBatchInsert;
    hashLinklistIndexFuncHandle.idxDelete = HashLinklistIndexDelete;
    hashLinklistIndexFuncHandle.idxBatchDelete = HashLinklistIndexBatchDelete;
    hashLinklistIndexFuncHandle.idxUpdate = HashLinklistIndexUpdate;
    hashLinklistIndexFuncHandle.idxBatchUpdate = HashLinklistIndexBatchUpdate;
    hashLinklistIndexFuncHandle.idxBeginScan = HashLinklistIndexBeginScan;
    hashLinklistIndexFuncHandle.idxScan = HashLinklistIndexScan;
    hashLinklistIndexFuncHandle.idxEndScan = HashLinklistIndexEndScan;
    hashLinklistIndexFuncHandle.idxUndoInsert = HashLinklistIndexUndoInsert;
    hashLinklistIndexFuncHandle.idxUndoRemove = HashLinklistIndexUndoRemove;
    hashLinklistIndexFuncHandle.idxUndoUpdate = HashLinklistIndexUndoUpdate;
    hashLinklistIndexFuncHandle.idxGetKeyCount = HashLinklistIndexGetKeyCount;
    hashLinklistIndexFuncHandle.idxStatView = HashLinklistIndexStatView;
    hashLinklistIndexFuncHandle.idxGetPageSize = HashLinklistViewGetPageSize;
    hashLinklistIndexFuncHandle.idxGetEstimateMemSize = HashLinklistIndexGetEstimateMemSize;
    hashLinklistIndexFuncHandle.idxGetCtxSize = HashLinklistGetCtxSize;
    IdxAmFuncRegister((uint8_t)HASH_LINKLIST_INDEX, &hashLinklistIndexFuncHandle);
}

#ifdef __cplusplus
}
#endif
