/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2024. All rights reserved.
 * File Name: se_hac_hash_index.c
 * Description: hash hac index 支持硬件卸载的主键索引
 * Author: lijianchuan
 * Create: 2022/8/26
 */

#ifdef FEATURE_HAC
#include "se_capacity_def_inner.h"
#include "se_hac_hash_index.h"

#define HAC_HASH_PREFETCH_STEPS 10
// hachash最长可卸载长度：127B
#define HAC_HASH_MAX_KEY_LEN_FOR_OFFLOADING 0x7F
// hachash最长tuple长度：256B
#define HAC_HASH_MAX_TUPLE_LEN_FOR_OFFLOADING 0x100

typedef struct HacHashIterator {
    bool isEOF;
    TupleAddr addr;
    IndexKeyT idxKey;
} HacHashIteratorT;

void HacHashIdxAMInitImpl(void)
{
    IdxFuncT hacHashFunc = IdxEmptyIdxFunc();
    hacHashFunc.idxCreate = HacHashIdxCreate;
    hacHashFunc.idxDrop = HacHashIdxDrop;
    hacHashFunc.idxTruncate = HacHashIdxTruncate;
    hacHashFunc.idxOpen = HacHashIdxOpen;
    hacHashFunc.idxClose = HacHashIdxClose;
    hacHashFunc.idxInsert = HacHashIdxInsert;
    hacHashFunc.idxBatchInsert = HacHashIdxBatchInsert;
    hacHashFunc.idxDelete = HacHashIdxDelete;
    hacHashFunc.idxBatchDelete = HacHashIdxBatchDelete;
    hacHashFunc.idxUpdate = HacHashIdxUpdate;
    hacHashFunc.idxEndBatchModify = HacHashIdxEndBatchModify;
    hacHashFunc.idxLookup = HacHashIdxLookup;
    hacHashFunc.idxBatchLookup = HacHashIdxBatchLookup;
    hacHashFunc.idxBeginScan = HacHashIdxBeginScan;
    hacHashFunc.idxScan = HacHashIdxScan;
    hacHashFunc.idxUndoInsert = HacHashIdxUndoInsert;
    hacHashFunc.idxUndoRemove = HacHashIdxUndoRemove;
    hacHashFunc.idxUndoUpdate = HacHashIdxUndoUpdate;
    hacHashFunc.idxGetKeyCount = HacHashIdxGetKeyCount;
    hacHashFunc.idxStatView = HacHashIdxStatView;
    hacHashFunc.idxGetPageSize = HacHashIdxViewGetPageSize;
    hacHashFunc.idxScaleIn = HacHashIdxScaleIn;
    hacHashFunc.idxGetEstimateMemSize = HacHashIdxGetEstimateMemSize;
    hacHashFunc.idxGetCtxSize = HacHashIdxGetCtxSize;
    IdxAmFuncRegister((uint8_t)HAC_HASH_INDEX, &hacHashFunc);
}

Status HacHashIdxCreate(SeRunCtxHdT seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr)
{
    if (idxCfg.idxConstraint == NON_UNIQUE) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "|Se-HH| do not support non unique index");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    Status ret = HacHashIdxCreateCommon(seRunCtx, idxCfg, idxShmAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Se-HH| Unable to create HacHashIdx.");
        return ret;
    }
    (void)DbAtomicInc(&HacGetStatsImpl()->hacHashIdxCnt);
    return GMERR_OK;
}

static void HacHashTableDrop(DbMemCtxT *memCtx, HacHashTableT *ht)
{
    HashTableDrop(memCtx, &ht->hashHac, ht);
}

Status HacHashIdxDrop(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    Status ret = HacHashIdxDropCommon(seRunCtx, idxShmAddr, HacHashTableDrop);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Se-HH| Unable to drop HacHashIdx.");
        return ret;
    }
    (void)DbAtomicDec(&HacGetStatsImpl()->hacHashIdxCnt);
    return GMERR_OK;
}

static void HacHashTableTruncate(DbMemCtxT *memCtx, HacHashTableT *ht)
{
    HashTableTruncate(memCtx, &ht->hashHac, ht);
}

Status HacHashIdxTruncate(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    Status ret = HacHashIdxTruncateCommon(seRunCtx, idxShmAddr, HacHashTableTruncate);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Se-HH| Unable to truncate HacHashIdx.");
        return ret;
    }
    return GMERR_OK;
}

Status HacHashIdxOpen(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    Status ret = HacHashIdxOpenCommon(idxCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Se-HH| Unable to open HacHashIdx.");
        return ret;
    }
    return GMERR_OK;
}

void HacHashIdxClose(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    HacHashIdxCloseCommon(idxCtx);
}

// 以下为DML操作

Status HashIdxInsertOrDelete(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr, bool isInsert)
{
    DB_POINTER3(idxCtx, idxCtx->idxHandle, idxKey.keyData);
    uint8_t cmpRes = (uint8_t)INDEX_FILTER_SATISFIED;
    Status ret = IdxCheckFilterSatisfied(idxCtx, idxKey, &cmpRes);
    if (cmpRes == (uint8_t)INDEX_FILTER_UNSATISFIED || ret != GMERR_OK) {
        return ret;
    }

    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t hashCode = HtGetHashCode(idxKey);
    uint32_t lockOffset = HashGetLockOffset(hashCode, &ht->hashHac);
    HashDMLParaT para = {
        .idxCtx = idxCtx, .hashHac = &ht->hashHac, .hashKey = idxKey, .hashCode = hashCode, .addr = addr};
    HashBucketLock(idxCtx, lockOffset, false);
    if (isInsert) {
        ++ht->hashInsertCnt;
        ret = HashInsertMatchEntry(&para);
    } else {
        ret = HashDeleteMatchEntry(&para);
    }
    HashBucketUnlock(idxCtx, lockOffset, false);
    return ret;
}

static Status HashIdxLookup(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr *addr, bool *isFound)
{
    uint32_t hashCode = HtGetHashCode(idxKey);
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);

    uint32_t lockOffset = HashGetLockOffset(hashCode, &ht->hashHac);

    *isFound = false;
    HashDMLParaT para = {
        .idxCtx = idxCtx, .hashHac = &ht->hashHac, .hashKey = idxKey, .hashCode = hashCode, .addr = HEAP_INVALID_ADDR};
    HashBucketLock(idxCtx, lockOffset, true);
    Status ret = HashSearchMatchEntry(&para);
    HashBucketUnlock(idxCtx, lockOffset, true);
    if (ret == GMERR_OK) {
        if (addr != NULL) {
            *addr = para.addr;
        }
        *isFound = true;
    } else if (ret == GMERR_NO_DATA) {  // not find is normal
        ret = GMERR_OK;
    }
    return ret;
}

static inline void HashIdxPrefetch(IndexCtxT *idxCtx, IndexKeyT idxKey)
{
    DB_POINTER2(idxCtx, idxCtx->idxRunCtx);
    uint32_t hashCode = HtGetHashCode(idxKey);
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    HacBucketT *bucket = HashGetBucketByHashCode(&ht->hashHac, hashCode);
    DB_PREFETCH(bucket, (int)DB_PREFECH_READ, (int)DB_PREFECH_LOCALITY_NONE);
}

Status HashIdxBatchLookup(IndexCtxT *idxCtx, IndexKeyT idxKey[], uint32_t batchNum, IdxBatchLookupParaT *para)
{
    DB_POINTER2(para, para->categorizeFunc);
    const IdxCategorizeFunc categorizeFunc = para->categorizeFunc;
    Status ret = GMERR_OK;
    TupleAddr addr;
    bool isFound = false;
    HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);

    for (uint32_t i = 0; i < batchNum; i++) {
        uint32_t idx = i + HAC_HASH_PREFETCH_STEPS;
        if (idx < batchNum) {
            HashIdxPrefetch(idxCtx, idxKey[i]);
        }
        if (idxCtx->isKeyCmpByHac) {
            idxCtx->tupleBuf.len = hacCtx->tupleSize;
            idxCtx->tupleBuf.buf = hacCtx->tuplesAddr + (DB_UINTPTR)hacCtx->queryIdx * hacCtx->tupleSize;
        }
        ret = HashIdxLookup(idxCtx, idxKey[i], &addr, &isFound);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|Se-HH| batch lookup unsucc");
            break;
        }
        ret = categorizeFunc(
            idxCtx, para->iter, TupleAddr2IdxTupleOrIter(addr), i, isFound ? IDX_IS_TUPLE_ADDR : IDX_IS_NOT_FOUND);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|Se-HH| categorize unsucc");
            break;
        }
    }
    return ret;
}

Status HashIdxBatchInsert(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum)
{
    Status ret = GMERR_OK;
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t i = 0;
    for (; i < batchNum; ++i) {
        ret = HashIdxInsertOrDelete(idxCtx, idxKey[i], addr[i].addrOut, true);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|Se-HH| batch insert unsucc");
            break;
        }
    }
    (void)DbAtomicAdd(&ht->hashHac.entryNum, i);  // 成功插入的个数
    return ret;
}

Status HashIdxBatchDelete(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum)
{
    Status ret = GMERR_OK;
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t deleteCnt = 0;
    for (uint32_t i = 0; i < batchNum; ++i) {
        ret = HashIdxInsertOrDelete(idxCtx, idxKey[i], addr[i].addrOut, false);
        if (ret == GMERR_OK) {
            deleteCnt++;
        } else if (ret != GMERR_NO_DATA) {
            DB_LOG_ERROR(ret, "|Se-HH| batch delete unsucc");
            break;
        }
    }
    (void)DbAtomicSub(&ht->hashHac.entryNum, deleteCnt);
    if (ret == GMERR_NO_DATA) {
        return GMERR_OK;
    }
    return ret;
}

Status HacHashIdxLookup(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr *addr, bool *isFound)
{
    DB_POINTER3(idxCtx, idxKey.keyData, isFound);
    HashTableLock(idxCtx, true);
    Status ret = HashIdxLookup(idxCtx, idxKey, addr, isFound);
    HashTableUnlock(idxCtx, true);
    return ret;
}

// 当前hachash支持卸载的必要条件：不带变长字段，key长不超过127B（且不为0B），tuple长不超过256B
bool IsHacHashSupportOffloading(IndexCtxT *idxCtx, IndexKeyT idxKey[])
{
    if (!HacIsAcceleratorMode()) {
        return false;
    }
    if (GetHacMgr()->ccType == CONCURRENCY_CONTROL_NORMAL) {
        return false;
    }
    if (SECUREC_UNLIKELY(idxCtx->idxMetaCfg.hasVarchar)) {
        return false;
    }
    if (idxKey[0].keyLen > HAC_HASH_MAX_KEY_LEN_FOR_OFFLOADING) {
        return false;
    }
    if (SECUREC_UNLIKELY(idxKey[0].keyLen == 0)) {
        return false;
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    if (ht->keyCmpCtx.rawRowSize > HAC_HASH_MAX_TUPLE_LEN_FOR_OFFLOADING) {
        return false;
    }
    return true;
}

Status HacHashIdxSingleLookup(IndexCtxT *idxCtx, IndexKeyT idxKey, IdxBatchLookupParaT *para)
{
    DB_POINTER2(para, para->categorizeFunc);
    bool isFound = false;
    TupleAddr addr = (TupleAddr){0};
    Status ret = HacHashIdxLookup(idxCtx, idxKey, &addr, &isFound);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Se-HH| Unable to hac hash lookup.");
        return ret;
    }
    ret = para->categorizeFunc(
        idxCtx, para->iter, TupleAddr2IdxTupleOrIter(addr), 0, isFound ? IDX_IS_TUPLE_ADDR : IDX_IS_NOT_FOUND);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Se-HH| Unable to categorize.");
        return ret;
    }
    return GMERR_OK;
}

Status HacHashIdxBatchLookup(IndexCtxT *idxCtx, IndexKeyT idxKey[], uint32_t batchNum, IdxBatchLookupParaT *para)
{
    DB_POINTER2(idxCtx, idxKey);
    if (SECUREC_UNLIKELY(batchNum == 1)) {
        return HacHashIdxSingleLookup(idxCtx, idxKey[0], para);
    }
    Status ret = HashInitKeyCmpCtx(idxCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
    if (idxCtx->isKeyCmpByHac) {
        hacCtx->reqNum = batchNum;
        hacCtx->queryIdx = 0u;
        hacCtx->tupleSize = ht->keyCmpCtx.rawRowSize;
        if ((ret = HacInitTupleAddr(hacCtx)) != GMERR_OK) {
            return ret;
        }
    } else {
        (void)DbAtomicInc64(&HacGetStatsImpl()->batchExcapedCnt);
    }
    TupleBufReset(&idxCtx->tupleBuf);
    HashTableLockForBatch(idxCtx, true);
    if (IsHacHashSupportOffloading(idxCtx, idxKey)) {
        hacCtx->idxLookupFunc = HashIdxLookup;
        hacCtx->idxKey = idxKey;
        hacCtx->hacType = ht->stats.hacHash.isKeyCmpByHac ? HAC_HASH_LOOKUP : HAC_HASH_LOOKUP_KEYCMP_BY_SOFT;
        hacCtx->para = para;
        hacCtx->isInsert = false;
        (void)DbAtomicInc64(&HacGetStatsImpl()->batchHacCnt);
        ret = HacHashBatchExecute(hacCtx, batchNum);
    } else {
        (void)DbAtomicInc64(&HacGetStatsImpl()->batchSoftwareCnt);
        ret = HashIdxBatchLookup(idxCtx, idxKey, batchNum, para);
    }
    HashTableUnlockForBatch(idxCtx, true);
    if (idxCtx->isKeyCmpByHac) {
        // tupleBuf.buf内存由索引自行回收
        idxCtx->tupleBuf.buf = NULL;
        idxCtx->tupleBuf.len = 0;
    }
    return ret;
}

inline static HacHashIteratorT *HacHashIterInit(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    HacHashIteratorT *iter = (HacHashIteratorT *)(void *)IdxGetIterator(idxCtx);
    DB_POINTER(iter);
    return iter;
}

Status HacHashIdxBeginScan(IndexCtxT *idxCtx, IndexScanCfgT scanCfg, IndexScanItrT *iter)
{
    DB_POINTER2(idxCtx, scanCfg.leftKey->keyData);

    HacHashIteratorT **itr = (HacHashIteratorT **)iter;
    HacHashIteratorT *iterator = HacHashIterInit(idxCtx);
    iterator->isEOF = false;
    iterator->addr = HEAP_INVALID_ADDR;
    iterator->idxKey = *scanCfg.leftKey;
    *itr = iterator;

    return GMERR_OK;
}

Status HacHashIdxScan(IndexCtxT *idxCtx, IndexScanItrT iter, TupleAddr *addr, bool *isFound)
{
    DB_POINTER3(idxCtx, addr, isFound);
    *isFound = false;
    if (iter == NULL) {
        return GMERR_OK;
    }
    HacHashIteratorT *itr = (HacHashIteratorT *)iter;
    if (itr->idxKey.keyData == NULL || itr->idxKey.keyLen == 0u) {
        DB_LOG_DBG_ERROR(GMERR_NO_DATA, "|Se-HH| key buffer is wrong");
        return GMERR_NO_DATA;
    }
    if (itr->isEOF) {
        return GMERR_OK;
    }
    Status ret = HacHashIdxLookup(idxCtx, itr->idxKey, addr, isFound);
    if (ret != GMERR_OK) {
        DB_LOG_DBG_WARN(ret, "|Se-HH| lookup goes wrong");
    }
    itr->isEOF = true;
    return ret;
}

Status HacHashIdxInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    DB_POINTER2(idxCtx, idxKey.keyData);
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    HashTableLock(idxCtx, false);
    Status ret = HashIdxInsertOrDelete(idxCtx, idxKey, addr, true);
    HashTableUnlock(idxCtx, false);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)DbAtomicInc(&ht->hashHac.entryNum);  // 插入成功就计数
    return HashCheckExpand(idxCtx, &ht->hashHac);
}

Status HacHashIdxBatchInsert(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum)
{
    DB_POINTER3(idxCtx, idxKey, addr);
    Status ret = HashInitKeyCmpCtx(idxCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
    if (idxCtx->isKeyCmpByHac) {
        hacCtx->tupleSize = ht->keyCmpCtx.rawRowSize;
    } else {
        (void)DbAtomicInc64(&HacGetStatsImpl()->batchExcapedCnt);
    }
    HashTableLockForBatch(idxCtx, false);
    if (IsHacHashSupportOffloading(idxCtx, idxKey) && GetHacBatchExectueToken()) {
        hacCtx->idxInsertOrDeleteFunc = HashIdxInsertOrDelete;
        hacCtx->idxKey = idxKey;
        hacCtx->addr = addr;
        hacCtx->hacType = ht->stats.hacHash.isKeyCmpByHac ? HAC_HASH_INSERT : HAC_HASH_INSERT_KEYCMP_BY_SOFT;
        hacCtx->isInsert = true;
        ht->hashInsertCnt += batchNum;
        (void)DbAtomicInc64(&HacGetStatsImpl()->batchHacCnt);
        ret = HacHashBatchExecute(hacCtx, batchNum);
        if (ret == GMERR_OK && idxCtx->isHacBatchAsync) {  // 异步模式下，执行完直接返回
            return GMERR_OK;
        }
        ReleaseHacBatchExectueToken();
    } else {
        (void)DbAtomicInc64(&HacGetStatsImpl()->batchSoftwareCnt);
        idxCtx->isHacBatchAsync = false;
        ret = HashIdxBatchInsert(idxCtx, idxKey, addr, batchNum);
    }
    HashTableUnlockForBatch(idxCtx, false);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = HashCheckExpand(idxCtx, &ht->hashHac);
    return ret;
}

Status HacHashIdxDelete(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr, IndexRemoveParaT removePara)
{
    DB_POINTER3(idxCtx, idxCtx->idxHandle, idxKey.keyData);
    if (!removePara.isErase) {  // 只支持erase删除，不支持标记删除
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "|Se-HH| do not support mark delete");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    HashTableLock(idxCtx, false);
    Status ret = HashIdxInsertOrDelete(idxCtx, idxKey, addr, false);
    HashTableUnlock(idxCtx, false);
    if (ret == GMERR_OK) {
        (void)DbAtomicDec(&ht->hashHac.entryNum);
    }
    if (ret == GMERR_NO_DATA) {  // not find is normal
        ret = GMERR_OK;
    }
    return ret;
}

Status HacHashIdxBatchDelete(
    IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum, IndexRemoveParaT removePara)
{
    DB_POINTER3(idxCtx, idxKey, addr);
    if (!removePara.isErase) {  // 只支持erase删除，不支持标记删除,
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "|Se-HH| do not support mark delete");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    Status ret = GMERR_OK;
    HashTableLockForBatch(idxCtx, false);
    if (IsHacHashSupportOffloading(idxCtx, idxKey) && GetHacBatchExectueToken()) {
        HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
        hacCtx->idxKey = idxKey;
        hacCtx->addr = addr;
        hacCtx->hacType = HAC_HASH_DELETE;
        hacCtx->isInsert = false;
        (void)DbAtomicInc64(&HacGetStatsImpl()->batchHacCnt);
        ret = HacHashBatchExecute(hacCtx, batchNum);
        if (ret == GMERR_OK && idxCtx->isHacBatchAsync) {  // 异步模式下，执行完直接返回
            return GMERR_OK;
        }
        ReleaseHacBatchExectueToken();
    } else {
        (void)DbAtomicInc64(&HacGetStatsImpl()->batchSoftwareCnt);
        idxCtx->isHacBatchAsync = false;
        ret = HashIdxBatchDelete(idxCtx, idxKey, addr, batchNum);
    }
    HashTableUnlockForBatch(idxCtx, false);
    return ret;
}

Status HacHashIdxEndBatchModify(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    Status ret = HacHashGetMsgAndReturn(idxCtx);
    ReleaseHacBatchExectueToken();
    HashTableUnlockForBatch(idxCtx, false);
    if (ret != GMERR_OK) {
        return ret;
    }
    HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
    if (hacCtx->isInsert) {
        HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
        return HashCheckExpand(idxCtx, &ht->hashHac);
    }
    return GMERR_OK;
}

Status HacHashIdxStatView(ShmemPtrT idxShmAddr, uint16_t instanceId, IndexStatisticsT *idxStat)
{
    DB_POINTER(idxStat);
    HacHashTableT *ht = (HacHashTableT *)HashGetShmAddr(idxShmAddr);
    if (ht == NULL) {
        DB_LOG_DEBUG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-HH| Hash table is null");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(instanceId);
    if (seInstance == NULL) {
        DB_LOG_DEBUG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-HH| hash's SE Instance is null");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint8_t hashCollisionRate = 0;
    if (ht->hashInsertCnt != 0u) {
        hashCollisionRate = (uint8_t)((ht->hashCollisionCnt * DB_PERCENTAGE_BASE) / ht->hashInsertCnt);
    }
    uint32_t hashEntryPerBucket = seInstance->seConfig.heapTupleAddrMode == SE_HEAP_TUPLE_ADDR_32 ?
                                      HAC_HASH_ENTRY_PER_BUCKET_32_BITS :
                                      HAC_HASH_ENTRY_PER_BUCKET_64_BITS;
    IndexHashPerfStatT *indexHashPerfStat = &idxStat->hashIndex.indexHashPerfStat;
    indexHashPerfStat->perPageSize = DB_MAX_UINT32;
    indexHashPerfStat->pageCount = DB_MAX_UINT32;
    indexHashPerfStat->pageSize = (uint64_t)(ht->hashHac.hashSize * HASH_BUCKET_SIZE);
    indexHashPerfStat->usedMemSize = (uint64_t)(ht->hashHac.entryNum * HASH_BUCKET_SIZE / hashEntryPerBucket);
    indexHashPerfStat->hashInsertCnt = ht->hashInsertCnt;
    indexHashPerfStat->hashCollisionCnt = ht->hashCollisionCnt;
    indexHashPerfStat->hashCollisionRate = hashCollisionRate;
    idxStat->hashIndex.entryCapacity = (ht->hashHac.hashSize + ht->extendListNum) * hashEntryPerBucket;
    idxStat->hashIndex.cacheLineLen = (uint32_t)HASH_BUCKET_SIZE;
    idxStat->hashIndex.entryUsed = ht->hashHac.entryNum;
    idxStat->hashIndex.segmentCnt = DB_MAX_UINT32;
    idxStat->hashIndex.scaleInCnt = ht->stats.hacHash.scaleInCnt;
    return GMERR_OK;
}

Status HacHashIdxViewGetPageSize(ShmemPtrT idxShmAddr, uint16_t instanceId, uint64_t *idxPageSize)
{
    DB_POINTER(idxPageSize);
    const HacHashTableT *ht = (HacHashTableT *)HashGetShmAddr(idxShmAddr);
    if (ht == NULL) {
        DB_LOG_DEBUG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-HH| Hash table is null");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *idxPageSize = (uint64_t)(ht->hashHac.hashSize * HASH_BUCKET_SIZE);
    return GMERR_OK;
}

Status HacHashIdxGetKeyCount(IndexCtxT *idxCtx, IndexKeyT key, uint64_t *count)
{
    DB_POINTER3(idxCtx, key.keyData, count);
    bool isFound = false;
    Status ret = HacHashIdxLookup(idxCtx, key, NULL, &isFound);
    if (ret != GMERR_OK || !isFound) {
        *count = 0;
        return ret;
    }
    *count = 1;
    return GMERR_OK;
}

Status HacHashIdxUndoInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    DB_POINTER3(idxCtx, idxCtx->idxHandle, idxKey.keyData);
    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    return HacHashIdxDelete(idxCtx, idxKey, addr, removePara);
}

Status HacHashIdxUndoRemove(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    DB_POINTER3(idxCtx, idxCtx->idxHandle, idxKey.keyData);
    return HacHashIdxInsert(idxCtx, idxKey, addr);
}

Status HacHashIdxUndoUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara)
{
    DB_POINTER(idxCtx);
    DB_UNUSED(removePara);
    Status ret = HacHashIdxUndoInsert(idxCtx, updateInfo.oldIdxKey, updateInfo.oldAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "|Se-HH| HacHash undo update unsucc when delete, indexId %" PRIu32, idxCtx->idxMetaCfg.indexId);
    }
    ret = HacHashIdxInsert(idxCtx, updateInfo.newIdxKey, updateInfo.newAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "|Se-HH| HacHash undo update unsucc when insert, indexId %" PRIu32, idxCtx->idxMetaCfg.indexId);
    }
    return ret;
}

Status HacHashIdxGetEstimateMemSize(
    uint16_t instanceId, IndexMetaCfgT idxMetaCfg, uint64_t count, uint32_t keyLen, uint64_t *size)
{
    DB_POINTER(size);
    return HashGetEstimateMemSize(idxMetaCfg, count, keyLen, size);
}

inline void HacHashIdxGetCtxSize(size_t *ctxSize, size_t *iterSize)
{
    *ctxSize = sizeof(HacCtxT);
    *iterSize = sizeof(HacHashIteratorT);
}

Status HashIdxBatchUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo[], uint32_t batchNum)
{
    Status ret = GMERR_OK;
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    HashTableLockForBatch(idxCtx, false);
    uint32_t deleteCnt = 0;

    for (uint32_t i = 0; i < batchNum; ++i) {
        ret = HashIdxInsertOrDelete(idxCtx, updateInfo[i].oldIdxKey, updateInfo[i].oldAddr, false);
        if (ret == GMERR_OK) {
            deleteCnt++;
        } else if (ret != GMERR_NO_DATA) {
            DB_LOG_ERROR(ret, "|Se-HH| batch delete for update unsucc");
            (void)DbAtomicSub(&ht->hashHac.entryNum, deleteCnt);
            HashTableUnlockForBatch(idxCtx, false);
            return ret;
        }
    }
    (void)DbAtomicSub(&ht->hashHac.entryNum, deleteCnt);

    uint32_t i = 0;
    for (; i < batchNum; ++i) {
        ret = HashIdxInsertOrDelete(idxCtx, updateInfo[i].newIdxKey, updateInfo[i].newAddr, true);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|Se-HH| batch insert for update unsucc");
            break;
        }
    }
    HashTableUnlockForBatch(idxCtx, false);
    (void)DbAtomicAdd(&ht->hashHac.entryNum, i);
    return ret;  // 先删后插理论上扩容概率较小，这里不再检查扩容
}

// 当前更新仅限轻量化事务，没有标记删除
// 这里将单点和批量分开，因为批量需要走硬件
Status HacHashIdxUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara)
{
    DB_POINTER(idxCtx);
    if (!removePara.isErase) {  // 只支持erase删除，不支持标记删除
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "|Se-HH| do not support mark delete");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    HashTableLock(idxCtx, false);
    Status ret = HashIdxInsertOrDelete(idxCtx, updateInfo.oldIdxKey, updateInfo.oldAddr, false);
    if (ret == GMERR_OK) {
        (void)DbAtomicDec(&ht->hashHac.entryNum);
    } else if (ret != GMERR_NO_DATA) {  // 删除异常，找不到不算异常
        HashTableUnlock(idxCtx, true);
        return ret;
    }

    ret = HashIdxInsertOrDelete(idxCtx, updateInfo.newIdxKey, updateInfo.newAddr, true);
    HashTableUnlock(idxCtx, false);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)DbAtomicInc(&ht->hashHac.entryNum);
    return GMERR_OK;  // 先删后插理论上扩容概率较小，这里不再检查扩容
}

Status HacHashIdxScaleIn(IndexCtxT *idxCtx, IndexScaleInCfgT *idxScaleCfg)
{
    DB_POINTER3(idxCtx, idxCtx->idxHandle, idxScaleCfg);
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    HashTableLock(idxCtx, false);

    if (ht->idxBase.validCode != HAC_HASH_INDEX_VALID_CODE) {
        HashTableUnlock(idxCtx, false);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "|Se-HH| table has been dropped");
        return GMERR_INTERNAL_ERROR;
    }
    Status ret = HashCheckScaleIn(idxCtx, &ht->hashHac, idxScaleCfg);
    HashTableUnlock(idxCtx, false);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 检查block状态，释放未在使用的block
    HacMemMgrScaleIn();
    return ret;
}

#endif
