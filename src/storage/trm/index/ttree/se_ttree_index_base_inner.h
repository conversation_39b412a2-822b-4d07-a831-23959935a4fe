/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: implementation of T-tree
 * Author: ma<PERSON><PERSON><PERSON>e
 * Create: 2024-07-10
 */
#ifndef SE_TTREE_INDEX_BASE_INNER_H
#define SE_TTREE_INDEX_BASE_INNER_H

#include "se_ttree_index_base.h"
#include "se_index_inner.h"
#include "tpc_api_def.h"
#include <math.h>

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

// 每个节点的记录数
#define TTREENODE_SIZE 100

// ttree 二维数组每个按照50个node为一组
#define TTREE_EACH_ARR_CNT 50

#define TTREE_EXPEND_TEN 10

#define TTREE_ARRLIST_ENTEND_SIZE 10

// 低于这个值删除的时候需要移动子节点key到当前节点
#define TTREENODE_BORDERLINE (TTREENODE_SIZE - 2)
// 最大深度
#define TTREELEVEL_MAX 40

#define TTREE_BALANCE_ZERO 0
#define TTREE_BALANCE_ONE 1
#define TTREE_BALANCE_TWO 2
#define TTREE_BALANCE_NEG_ONE (-1)
#define TTREE_BALANCE_NEG_TWO (-2)

#define INVALID_LENGTH32 0xffffffffu
#define TTREE_MAX_SIZE 0x04000000
#define TTREE_STEP_LEVEL 14
extern const uint32_t g_TtreeStepLevelSize[];

typedef struct TTreeNodeCommon {
    uint8_t ucCurIdxNum;
    int8_t scBalance;
    bool isNormalIdx;
    uint8_t aucReserve;
    uint32_t nodeParent;
    uint32_t nodeLChild;
    uint32_t nodeRChild;
} TTreeNodeCommonT;

typedef struct TTreeNodeBigObj {
    uint8_t ucCurIdxNum;
    int8_t scBalance;
    bool isNormalIdx;
    uint8_t aucReserve;
    uint32_t nodeParent;
    uint32_t nodeLChild;
    uint32_t nodeRChild;
    DataInfoT dataInfo[TTREENODE_SIZE];
} TTreeNodeBigObjT;

typedef struct TTreeNode {
    uint8_t ucCurIdxNum;
    int8_t scBalance;
    bool isNormalIdx;
    uint8_t aucReserve;
    uint32_t nodeParent;
    uint32_t nodeLChild;
    uint32_t nodeRChild;
    uint8_t *buf[TTREENODE_SIZE];
} TTreeNodeT;

typedef struct TTreeNodeArr {
    ShmemPtrT addr;  // 共享内存addr
    void *nodes;     // TTreeNodeT || TTreeNodeBigObjT 数组
} TTreeNodeArrT;

static ALWAYS_INLINE DataInfoT GetDataInfoNormal(TTreeNodeT *node, uint32_t pos)
{
    uint8_t *buf = node->buf[pos];
    DataInfoT temp = {.addr32.pageIdAndSlotId = *(uint32_t *)buf,
        .hasModify = ((HpNormalFixRowHead *)(buf - sizeof(HpNormalFixRowHead)))->rowState.hasModify,
        .buf = buf + EXTERN_DATA_BUF_LEN};
    return temp;
}

static ALWAYS_INLINE DataInfoT GetDataInfoByArray(uint8_t *array[], uint32_t pos)
{
    uint8_t *buf = array[pos];
    DataInfoT temp = {.addr32.pageIdAndSlotId = *(uint32_t *)buf,
        .hasModify = ((HpNormalFixRowHead *)(buf - sizeof(HpNormalFixRowHead)))->rowState.hasModify,
        .buf = buf + EXTERN_DATA_BUF_LEN};
    return temp;
}

static ALWAYS_INLINE DataInfoT GetDataInfo(void *node, uint32_t pos)
{
    if (((TTreeNodeCommonT *)node)->isNormalIdx) {
        return GetDataInfoNormal(node, pos);
    }
    return ((TTreeNodeBigObjT *)node)->dataInfo[pos];
}

static ALWAYS_INLINE void SetDataInfoByPos(void *node, uint32_t pos, DataInfoT src)
{
    if (((TTreeNodeCommonT *)node)->isNormalIdx) {
        ((TTreeNodeT *)node)->buf[pos] = src.buf - EXTERN_DATA_BUF_LEN;
        return;
    }
    ((TTreeNodeBigObjT *)node)->dataInfo[pos] = src;
}

static ALWAYS_INLINE uint32_t GetDataInfoAddr(void *node, uint32_t pos)
{
    if (((TTreeNodeCommonT *)node)->isNormalIdx) {
        uint8_t *buf = ((TTreeNodeT *)node)->buf[pos];
        return *(uint32_t *)buf;
    }
    return ((TTreeNodeBigObjT *)node)->dataInfo[pos].addr32.pageIdAndSlotId;
}

static ALWAYS_INLINE void SetDataInfoAddr(void *node, uint32_t pos, TuplePointer32T addr)
{
    if (((TTreeNodeCommonT *)node)->isNormalIdx) {
        *(uint32_t *)((TTreeNodeT *)node)->buf[pos] = addr.pageIdAndSlotId;
        return;
    }
    ((TTreeNodeBigObjT *)node)->dataInfo[pos].addr32 = addr;
}

static ALWAYS_INLINE uint8_t *GetDataInfoBuf(void *node, uint32_t pos)
{
    if (((TTreeNodeCommonT *)node)->isNormalIdx) {
        uint8_t *buf = ((TTreeNodeT *)node)->buf[pos];
        return buf + EXTERN_DATA_BUF_LEN;
    }
    return ((TTreeNodeBigObjT *)node)->dataInfo[pos].buf;
}

static ALWAYS_INLINE void SetDataInfoBuf(void *node, uint32_t pos, uint8_t *buf)
{
    if (((TTreeNodeCommonT *)node)->isNormalIdx) {
        ((TTreeNodeT *)node)->buf[pos] = buf;
        return;
    }
    ((TTreeNodeBigObjT *)node)->dataInfo[pos].buf = buf;
}

static ALWAYS_INLINE uint8_t GetDataInfoModify(void *node, uint32_t pos)
{
    if (((TTreeNodeCommonT *)node)->isNormalIdx) {
        uint8_t *buf = ((TTreeNodeT *)node)->buf[pos];
        return ((HpNormalFixRowHead *)(buf - sizeof(HpNormalFixRowHead)))->rowState.hasModify;
    }
    return ((TTreeNodeBigObjT *)node)->dataInfo[pos].hasModify;
}

static ALWAYS_INLINE void SetDataInfoModify(void *node, uint32_t pos, uint8_t modify)
{
    if (((TTreeNodeCommonT *)node)->isNormalIdx) {
        uint8_t *buf = ((TTreeNodeT *)node)->buf[pos];
        ((HpNormalFixRowHead *)(buf - sizeof(HpNormalFixRowHead)))->rowState.hasModify = modify;
        return;
    }
    ((TTreeNodeBigObjT *)node)->dataInfo[pos].hasModify = modify;
}

static ALWAYS_INLINE DbMemCtxT *GetTTreeIdxShmMemCtx(SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);
    SeInstanceT *seInstance = (SeInstanceT *)seRunCtx->seIns;
    if (seInstance == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Get instance unsucess.");
        return NULL;
    }
    return (DbMemCtxT *)DbGetShmemCtxById(seInstance->indexTTreeShmMemCtxId, seInstance->instanceId);
}

typedef enum TagTtreeTraceType {
    TTREE_TRACE_TYPE_INSERT = 0,
    TTREE_TRACE_TYPE_DELETE,
    TTREE_TRACE_TYPE_UPDATE,
    TTREE_TRACE_TYPE_SCAN,
    TTREE_TRACE_TYPE_BUTT
} TtreeTraceTypeE;  // 和V1原生, 需要和 DB_OPTYPE_ENUM 保持兼容

#define MAX_TRACE_PATH_DEPTH 32

/*
    内存结构[TTREE HEAD][NODE0][NODE1][NODE2][NODE3]
    树状结构   [NODE2]
                /  \
          [NODE1] [NODE3]
              /
          [NODE0]
    head中rootOffset指向NODE2,值为2
*/
typedef struct TTreeHead {
    IdxBaseT idxBase;
    uint32_t nodeCnt;          // 总节点数
    uint32_t ulUsedTTreeNode;  // 占用节点数
    uint32_t rootNode;         // 根节点
    uint32_t freeNode;         // 下一个可用节点
    uint8_t isNormalIdx;
    uint8_t reserve[3];
    uint32_t idxSearchCnt;                 // 索引扫描次数（不导出至文件）
    DbListT nodeArrList;                   // TTreeNodeArrT *
    char tracePath[MAX_TRACE_PATH_DEPTH];  // 记录TtreeTrace
} TTreeHeadT;

static ALWAYS_INLINE TTreeNodeCommonT *GetCommonTTreeNode(TTreeHeadT *head, uint32_t pos)
{
    TTreeNodeArrT *nodeArr = DbListItem(&head->nodeArrList, pos / TTREE_EACH_ARR_CNT);
    if (head->isNormalIdx) {
        return (TTreeNodeCommonT *)&((TTreeNodeT *)nodeArr->nodes)[pos % TTREE_EACH_ARR_CNT];
    }
    return (TTreeNodeCommonT *)&((TTreeNodeBigObjT *)nodeArr->nodes)[pos % TTREE_EACH_ARR_CNT];
}

static ALWAYS_INLINE TTreeNodeT *GetTTreeNode(TTreeHeadT *head, uint32_t pos)
{
    TTreeNodeArrT *nodeArr = DbListItem(&head->nodeArrList, pos / TTREE_EACH_ARR_CNT);
    return &((TTreeNodeT *)nodeArr->nodes)[pos % TTREE_EACH_ARR_CNT];
}

static ALWAYS_INLINE TTreeNodeBigObjT *GetBigObjTTreeNode(TTreeHeadT *head, uint32_t pos)
{
    TTreeNodeArrT *nodeArr = DbListItem(&head->nodeArrList, pos / TTREE_EACH_ARR_CNT);
    return &((TTreeNodeBigObjT *)nodeArr->nodes)[pos % TTREE_EACH_ARR_CNT];
}

// 参考DBFI_CalcNumTTreeNode
// ulStructNum * ulStructSize + ulSize越界检测
inline static uint32_t TTreeGetNodesSize(uint32_t ulSize, uint32_t ulStructNum, uint32_t ulStructSize)
{
    uint64_t result = (uint64_t)ulStructNum * (uint64_t)ulStructSize;
    if (result > INVALID_LENGTH32 - ulSize) {
        return INVALID_LENGTH32;
    }
    return ((uint32_t)result + ulSize);
}

static inline __attribute__((always_inline)) void TTreeInit(
    IndexMetaCfgT *cfg, TTreeHeadT *ttreeIdx, SeInstanceT *seInstance)
{
    DbRWLatchInit(&(ttreeIdx->idxBase.idxLatch));
    ttreeIdx->idxBase.indexCfg = *cfg;
    ttreeIdx->rootNode = NULL_NODEID;
    ttreeIdx->freeNode = NULL_NODEID;
    DbCreateListWithExtendSize(&ttreeIdx->nodeArrList, sizeof(TTreeNodeArrT), TTREE_ARRLIST_ENTEND_SIZE,
        (DbMemCtxT *)DbGetTopDynMemCtx(seInstance->dbInstance));
#ifdef FEATURE_SIMPLEREL
    ttreeIdx->isNormalIdx = cfg->isNormalIdx;
#endif
    ttreeIdx->idxBase.shmemCtxId = seInstance->indexTTreeShmMemCtxId;
}

ALWAYS_INLINE static void TTreeSetFreeNodes(TTreeHeadT *ttreeIdx, uint32_t usedNodeCnt, uint32_t newNodeCnt)
{
    for (uint32_t ulCnt = usedNodeCnt; ulCnt < newNodeCnt; ulCnt++) {
        GetCommonTTreeNode(ttreeIdx, ulCnt)->nodeLChild = ulCnt + 1;
        GetCommonTTreeNode(ttreeIdx, ulCnt)->nodeRChild = ulCnt + 1;
        GetCommonTTreeNode(ttreeIdx, ulCnt)->ucCurIdxNum = 0;
        GetCommonTTreeNode(ttreeIdx, ulCnt)->isNormalIdx = ttreeIdx->isNormalIdx;
    }
    GetCommonTTreeNode(ttreeIdx, newNodeCnt - 1)->nodeLChild = INVALID_LENGTH32;
    GetCommonTTreeNode(ttreeIdx, newNodeCnt - 1)->nodeRChild = INVALID_LENGTH32;
    ttreeIdx->freeNode = usedNodeCnt == newNodeCnt ? NULL_NODEID : usedNodeCnt;
    ttreeIdx->nodeCnt = newNodeCnt;
}

// ttreeIdx已全部set为0，参考DBFI_TTREE_InitBlock
ALWAYS_INLINE static Status TTreeExpand(TTreeHeadT *ttreeIdx, SeRunCtxHdT seRunCtx)
{
    Status ret;
    uint32_t nodesize = ttreeIdx->isNormalIdx ? sizeof(TTreeNodeT) : sizeof(TTreeNodeBigObjT);
    uint32_t newNodeCnt;
    uint32_t acquireNodeCnt;
    bool isNewArr = false;
    if (ttreeIdx->nodeCnt >= g_TtreeStepLevelSize[TTREE_STEP_LEVEL - 1]) {
        newNodeCnt = ttreeIdx->nodeCnt + g_TtreeStepLevelSize[TTREE_STEP_LEVEL - 1];
        acquireNodeCnt = g_TtreeStepLevelSize[TTREE_STEP_LEVEL - 1];
        isNewArr = true;
    } else if (ttreeIdx->nodeCnt < TTREE_EXPEND_TEN) {
        newNodeCnt = ttreeIdx->nodeCnt + 1;
        acquireNodeCnt = newNodeCnt;
    } else {
        newNodeCnt = ttreeIdx->nodeCnt + TTREE_EXPEND_TEN;
        acquireNodeCnt = newNodeCnt;
    }

    uint32_t oldMemSize = TTreeGetNodesSize(0, ttreeIdx->nodeCnt, nodesize);
    uint32_t newMemSize = TTreeGetNodesSize(0, acquireNodeCnt, nodesize);

    DbMemCtxT *sIdxMemCtx = GetTTreeIdxShmMemCtx(seRunCtx);
    ShmemPtrT newNodeShmAddr = {0};
    void *newNodeArray = (void *)SeShmAlloc(sIdxMemCtx, newMemSize, &newNodeShmAddr);
    if (newNodeArray == NULL) {
        DB_LOG_AND_SET_LASERR(VOS_ERRNO_DB_MEMALLOCFAILURE,
            "Unable to alloc ttree node array, array cnt(%" PRIu32 "), size(%" PRIu32 ").", acquireNodeCnt, newMemSize);
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    TTreeNodeArrT nodeArr = {.addr = newNodeShmAddr, .nodes = newNodeArray};
    if (!isNewArr && ttreeIdx->nodeCnt != 0) {
        TTreeNodeArrT *oldNodeArr = DbListItem(&ttreeIdx->nodeArrList, 0);
        (void)memcpy_s(newNodeArray, newMemSize, oldNodeArr->nodes, oldMemSize);
        DbShmemCtxFree(sIdxMemCtx, oldNodeArr->addr);
        ret = DbSetListItem(&ttreeIdx->nodeArrList, &nodeArr, 0);
    } else {
        ret = DbAppendListItem(&ttreeIdx->nodeArrList, &nodeArr);
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Expend ttree operate nodeArr list.");
        return ret;
    }

    TTreeSetFreeNodes(ttreeIdx, ttreeIdx->nodeCnt, newNodeCnt);
    return STATUS_OK_INTER;
}

static inline __attribute__((always_inline)) TTreeHeadT *TTreeIndexCastIdxAsTTreeIndex(IdxBaseT *idxBase)
{
    DB_POINTER(idxBase);
    return (TTreeHeadT *)(void *)idxBase;
}

Status TTreeCalcInsBalance(TTreeHeadT *ttreeIdx, TTreeNodeCommonT *pstTTreeNode, bool bAdd, bool *pbAffect);

Status TTreeCalcDelBalance(TTreeHeadT *ttreeIdx, TTreeNodeCommonT *pstRecTTreeNode, bool bAdd, bool *pbAffect);

Status TTreeAllocNode(TTreeHeadT *ttreeIdx, uint32_t ParentNodeId, bool isLeftChild, TTreeNodeCommonT **ppstTTreeNode,
    TTreeNodeCommonT **ppstTTreeNodeParent, SeRunCtxHdT seRunCtx);

Status TTreeInsertInTree(IndexCtxT *idxCtx, TTreeHeadT *ttreeIdx, uint32_t nodeTTree, DataInfoT insertDataInfo,
    uint8_t *pucInputRec, SiteFldT *pstFldSet, bool *pbAffect, bool bDirectIns, uint32_t *insertTTreeNode);

Status TTreeInsertInTreeBigObj(IndexCtxT *idxCtx, TTreeHeadT *ttreeIdx, uint32_t nodeTTree, DataInfoT insertDataInfo,
    uint8_t *pucInputRec, SiteFldT *pstFldSet, bool *pbAffect, bool bDirectIns, uint32_t *insertTTreeNode);

Status TTreeDeleteInTree(IndexCtxT *idxCtx, TTreeHeadT *ttreeIdx, uint32_t nodeTTree, DataInfoT delDataInfo,
    uint8_t *pucInputRec, SiteFldT *pstFldSet, bool *pbAffect, bool *pbDelete);

Status TTreeDeleteInTreeBigObj(IndexCtxT *idxCtx, TTreeHeadT *ttreeIdx, uint32_t nodeTTree, DataInfoT delDataInfo,
    uint8_t *pucInputRec, SiteFldT *pstFldSet, bool *pbAffect, bool *pbDelete);

void TTreeDeleteFreeNode(TTreeHeadT *ttreeIdx, uint32_t nodeTTree, bool *pbAffect, bool *pbDelete);

void TTreeFreeAllNodes(TTreeHeadT *ttreeIdx, DbMemCtxT *idxMemCtx);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
