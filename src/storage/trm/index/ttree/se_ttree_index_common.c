/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: implementation of T-tree
 * Author: maowenjie
 * Create: 2024-07-10
 */
#include "se_ttree_index.h"
#include "se_common.h"
#include "se_heap_base.h"
#include "se_heap_access_inner.h"
#include "se_heap_inner.h"
#include "clt_utils.h"

void *g_pageMgr = NULL;

uint8_t *SimpleGetUndoRowData(PageHeadT *pageHead, uint8_t idxSeType, uint32_t offset)
{
    UndoPageHeaderT *targetPage = (void *)pageHead;
    DB_ASSERT(targetPage->magic == UNDO_PAGE_HEADER_MAGIC);
    UndoModifyRecHdrT *rec = (UndoModifyRecHdrT *)((uintptr_t)targetPage + offset);
    void *updRec = (void *)&rec->base;
    if (SECUREC_LIKELY(idxSeType == TTREE_FIX_ROW)) {
        return (uint8_t *)(updRec + sizeof(UndoUpdHeapRecHdrT) + sizeof(HpNormalFixRowHead));
    } else if (idxSeType == TTREE_VAR_ROW) {
        return (uint8_t *)(updRec + sizeof(UndoUpdHeapRecHdrT) + sizeof(HpNormalRowHeadT));
    }
    return (uint8_t *)(updRec + sizeof(UndoUpdHeapRecHdrT));
}

void SimpleGetUndoRowDataInfo(PageHeadT *pageHead, uint8_t ixdSeType, uint32_t offset, DataInfoT *dataInfo)
{
    UndoPageHeaderT *targetPage = (void *)pageHead;
    DB_ASSERT(targetPage->magic == UNDO_PAGE_HEADER_MAGIC);
    UndoModifyRecHdrT *rec = (UndoModifyRecHdrT *)((uintptr_t)targetPage + offset);
    dataInfo->masterAddr64 = rec->rowid;
    void *updRec = (void *)&rec->base;
    dataInfo->trxId = rec->trxId;
    if (ixdSeType == TTREE_FIX_ROW) {
        dataInfo->buf = (uint8_t *)(updRec + sizeof(UndoUpdHeapRecHdrT) + sizeof(HpNormalFixRowHead));
    } else if (ixdSeType == TTREE_VAR_ROW) {
        dataInfo->buf = (uint8_t *)(updRec + sizeof(UndoUpdHeapRecHdrT) + sizeof(HpNormalRowHeadT));
    } else {
        dataInfo->buf = (uint8_t *)(updRec + sizeof(UndoUpdHeapRecHdrT));
    }
}

uint8_t *SimpleGetFixRowData(PageHeadT *pageHead, uint32_t slotId)
{
    HFPageHeadT *hfpageHead = (void *)pageHead;
    const HFPageCfgT *pageHeadCfg = &hfpageHead->pageCfg;
    DB_ASSERT(slotId < hfpageHead->pageCfg.rowCnt);
    uint64_t slotOffset = (uint64_t)(slotId) * (pageHeadCfg->oneRowSize + pageHeadCfg->slotExtendSize) +
                          pageHeadCfg->slotExtendSize + pageHeadCfg->rowBegin;
    return (uint8_t *)((void *)hfpageHead + slotOffset + sizeof(HpNormalFixRowHead));
}

uint8_t *SimpleGetVarRowData(PageHeadT *pageHead, uint8_t idxSeType, uint32_t slotId)
{
    HVPageHeadT *hvpageHead = (void *)pageHead;
    uint64_t oneSlotSize = (PageSizeT)(sizeof(HpRowSlotT) + hvpageHead->slotExtendSize);  // HeapVarPageGetOneSlotSize()
    uint64_t offset = (uint64_t)HEAP_ALIGN_SIZE_OF(HVPageHeadT) + (slotId * oneSlotSize);
    DB_ASSERT(offset <= DB_INVALID_UINT16);
    DB_ASSERT(offset >= HEAP_ALIGN_SIZE_OF(HVPageHeadT));
    DB_ASSERT(offset <= hvpageHead->slotDirEnd - oneSlotSize);
    DB_ASSERT(offset == CALC_ALIGN_SIZE(offset, ALIGN_SIZE_TWO));
    HpRowSlotT *hvslot = (HpRowSlotT *)((uintptr_t)hvpageHead + (uintptr_t)offset);
    DB_ASSERT(hvslot->isFree == false);
    PageSizeT realOffset = PAGE_GET_ROW_REAL_OFFSET(hvslot->offset);
    if (idxSeType == TTREE_VAR_ROW) {
        return (uint8_t *)((void *)hvpageHead + realOffset + sizeof(HpNormalRowHeadT));
    }
    return (uint8_t *)((void *)hvpageHead + realOffset);
}

uint8_t *GetAddrByRowId(HpTupleAddr rowId, uint8_t idxSeType)
{
    PageIdT pageBlockId = TTreeDeserializePageId(((HpItemPointerT *)(void *)&rowId)->pageId);
    DB_ASSERT(g_devEntryCacheHac[pageBlockId.deviceId] != NULL);
    PageHeadT *pageHead = (void *)g_devEntryCacheHac[pageBlockId.deviceId] +
                          ((uint64_t)pageBlockId.blockId << g_AddrCompressMgr.pageSizeShiftBit);
    if (pageHead->pageType >= PERSISTENCE_PAGE_TYPE_UNDO_RSEG && pageHead->pageType <= PERSISTENCE_PAGE_TYPE_UNDO) {
        return SimpleGetUndoRowData(pageHead, idxSeType, ((HpItemPointerT *)(void *)&rowId)->slotId);
    }
    if (idxSeType == TTREE_FIX_ROW) {
        return SimpleGetFixRowData(pageHead, ((HpItemPointerT *)(void *)&rowId)->slotId);
    }
    return SimpleGetVarRowData(pageHead, idxSeType, ((HpItemPointerT *)(void *)&rowId)->slotId);
}

DataInfoT GetDataInfoByRowId(HpTupleAddr rowId, uint8_t idxSeType)
{
    DataInfoT dataInfo = {.addr64 = rowId, .idxSeType = idxSeType, .spId = DB_INVALID_UINT32};
    PageIdT pageBlockId = TTreeDeserializePageId(((HpItemPointerT *)(void *)&rowId)->pageId);
    DB_ASSERT(g_devEntryCacheHac[pageBlockId.deviceId] != NULL);
    PageHeadT *pageHead = (void *)g_devEntryCacheHac[pageBlockId.deviceId] +
                          ((uint64_t)pageBlockId.blockId << g_AddrCompressMgr.pageSizeShiftBit);
    if (pageHead->pageType >= PERSISTENCE_PAGE_TYPE_UNDO_RSEG && pageHead->pageType <= PERSISTENCE_PAGE_TYPE_UNDO) {
        dataInfo.isUndoData = true;
        SimpleGetUndoRowDataInfo(pageHead, idxSeType, ((HpItemPointerT *)(void *)&rowId)->slotId, &dataInfo);
        return dataInfo;
    }
    dataInfo.isUndoData = false;
    dataInfo.masterAddr64 = rowId;
    if (idxSeType == TTREE_FIX_ROW) {
        dataInfo.buf = SimpleGetFixRowData(pageHead, ((HpItemPointerT *)(void *)&rowId)->slotId);
        HpNormalFixRowHead *head = (HpNormalFixRowHead *)(dataInfo.buf - sizeof(HpNormalFixRowHead));
        dataInfo.trxId = head->trxId;
        return dataInfo;
    }
    dataInfo.buf = SimpleGetVarRowData(pageHead, idxSeType, ((HpItemPointerT *)(void *)&rowId)->slotId);
    if (idxSeType == TTREE_VAR_ROW) {
        dataInfo.trxId = RowGetTrxId(&((HpNormalRowHeadT *)(dataInfo.buf - sizeof(HpNormalRowHeadT)))->trxInfo);
    } else {
        dataInfo.trxId = RowGetTrxId(&((HpLinkRowHeadT *)dataInfo.buf)->srcTrxInfo);
    }
    return dataInfo;
}

inline static TrxIdT TTreeGetTrxIdFromBuf(IndexCtxT *idxCtx, uint8_t *recBuf)
{
    if (idxCtx->idxSeType == TTREE_FIX_ROW) {
        return ((HpNormalFixRowHead *)(recBuf - sizeof(HpNormalFixRowHead)))->trxId;
    }

    RowTrxInfoT *rowTxInfo = &((HpNormalRowHeadT *)(recBuf - sizeof(HpNormalRowHeadT)))->trxInfo;
    return RowGetTrxId(rowTxInfo);
}

Status FetchFixedTupleBufByTTree(
    IndexCtxT *idxCtx, ScanDataCtxT *scanDataCtx, bool readDataInsertedIndex, EmbSimpleQueryCtxT *pstSearchPara)
{
    Status ret = STATUS_OK_INTER;
    if (readDataInsertedIndex) {
        scanDataCtx->heapTupleBuf.buf = scanDataCtx->dataInfo.buf;
        return GMERR_OK;
    }

    TrxT *trx = (TrxT *)idxCtx->idxOpenCfg.seRunCtx->trx;
    ReadViewT *readView = &trx->trx.base.readView;
    ReadViewT readViewRc = {.isOpen = false, .activeTrxIdsShm = DB_INVALID_SHMPTR, .activeTrxIdsTmp = NULL};
    if (trx->trx.base.isolationLevel == READ_COMMITTED) {
        ret = ReadViewPrepareForRc(idxCtx->idxOpenCfg.heapHandle->seRunCtx, &readViewRc, false);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
        readView = &readViewRc;
    }
    ret = ReadViewIsTrxVisible(readView, TTreeGetTrxIdFromBuf(idxCtx, scanDataCtx->dataInfo.buf));
    if (ret != STATUS_OK_INTER) {
        return NO_DATA_INTER;
    }

    if (!scanDataCtx->dataInfo.isUndoData) {
        // 没有被修改过且可见的数据直接获取
        scanDataCtx->heapTupleBuf.buf = scanDataCtx->dataInfo.buf;
        return GMERR_OK;
    }

    if (pstSearchPara->lastFetchAddr == scanDataCtx->dataInfo.masterAddr64) {
        // 与上一次fetch的addr一致，可直接获取buf
        scanDataCtx->heapTupleBuf.buf = pstSearchPara->lastFetchBuf;
    } else {
        HpRunHdlT hpHandle = idxCtx->idxOpenCfg.heapHandle;
        HeapLabelResetNewTupleLockInfo(hpHandle);
        hpHandle->hpControl.isReadLatestCommittedBuf = false;
        ret = HeapFetchHpTupleBufferTrxInfo(
            hpHandle, scanDataCtx->dataInfo.masterAddr64, (void *)&scanDataCtx->heapTupleBuf);
        if (ret == GMERR_NO_DATA) {
            pstSearchPara->lastInvalidAddr = scanDataCtx->dataInfo.masterAddr64;
        } else {
            pstSearchPara->lastFetchAddr = scanDataCtx->dataInfo.masterAddr64;
            pstSearchPara->lastFetchBuf = scanDataCtx->heapTupleBuf.buf;
        }
    }
    bool canRead = scanDataCtx->heapTupleBuf.buf == scanDataCtx->dataInfo.buf;  // 过滤重复
    if (ret == GMERR_NO_DATA || !canRead) {
        return NO_DATA_INTER;
    }
    return GMERR_OK;
}

static void TtreeRecordCompareData(DmValueT *dmv1, DmValueT *dmv2, uint32_t index, bool isSetIndex)
{
    uint32_t printLen1 = DmGetPropertyValuePrintLen(dmv1) + 1;
    uint32_t printLen2 = DmGetPropertyValuePrintLen(dmv2) + 1;
    char *str1 = (char *)DbDynMemCtxAlloc((DbMemCtxT *)DbGetTopDynMemCtx(NULL), printLen1);
    char *str2 = (char *)DbDynMemCtxAlloc((DbMemCtxT *)DbGetTopDynMemCtx(NULL), printLen1);
    if (str1 == NULL || str2 == NULL) {
        return;
    }
    char *strCursor1 = str1;
    DmPrintPropertyValue(dmv1, &strCursor1, printLen1);
    char *strCursor2 = str2;
    DmPrintPropertyValue(dmv2, &strCursor2, printLen2);
    if (isSetIndex) {
        DB_LOG_WARN_UNFOLD(
            GMERR_OK, "TTree Trace Info:find: %s, cmp(index: %" PRIu32 ", current: %s).", str1, index, str2);
    } else {
        DB_LOG_WARN_UNFOLD(GMERR_OK, "TTree Trace Info:find: %s, cmp(current: %s).", str1, str2);
    }

    DbDynMemCtxFree((DbMemCtxT *)DbGetTopDynMemCtx(NULL), str1);
    DbDynMemCtxFree((DbMemCtxT *)DbGetTopDynMemCtx(NULL), str2);
}

void DataCompareRecord(const SiteFldT *pstFieldSet, uint8_t *pucFirstRecord, uint8_t *pucSecondRecord,
    int8_t *pulCompareRslt, uint32_t index)
{
    Status ret = GMERR_OK;
    int8_t compareResult = 0;
    uint16_t customTypeId;
    for (uint16_t usCnt = 0; compareResult == 0 && usCnt < pstFieldSet->ucFieldNum; usCnt++) {
        if (pstFieldSet->astField[usCnt].usDataType == DB_DATATYPE_UINT32) {
            uint32_t d1 = *(uint32_t *)(pucFirstRecord + pstFieldSet->astField[usCnt].offset);
            uint32_t d2 = *(uint32_t *)(pucSecondRecord + pstFieldSet->astField[usCnt].offset);
            if (d1 > d2) {
                compareResult = 1;
            } else if (d1 < d2) {
                compareResult = -1;
            }
#ifndef NDEBUG
            if (SECUREC_UNLIKELY(TtreeGetTraceSwitchState())) {
                DB_LOG_WARN_UNFOLD(GMERR_OK,
                    "TTree Trace Info:find: %" PRIu32 ", cmp(index: %" PRIu32 ", current: %" PRIu32 ").", d1, index,
                    d2);
            }
#endif
            continue;
        }
        customTypeId = pstFieldSet->astField[usCnt].customTypeId;
        // 内部比较，不会出错
        ret = V1ValueCmp(pucFirstRecord + pstFieldSet->astField[usCnt].offset,
            pucSecondRecord + pstFieldSet->astField[usCnt].offset, pstFieldSet->astField[usCnt].usDefLen, customTypeId,
            &compareResult);
        DB_ASSERT(ret == GMERR_OK);
#ifndef NDEBUG
        if (SECUREC_UNLIKELY(TtreeGetTraceSwitchState())) {
            DmValueT dmv1 = {0};
            DmValueT dmv2 = {0};
            ret = EmbSetDmValue(&dmv1, pstFieldSet->astField[usCnt].usDataType,
                pucFirstRecord + pstFieldSet->astField[usCnt].offset, pstFieldSet->astField[usCnt].usDefLen,
                pstFieldSet->astField[usCnt].usDataType);
            ret = EmbSetDmValue(&dmv2, pstFieldSet->astField[usCnt].usDataType,
                pucSecondRecord + pstFieldSet->astField[usCnt].offset, pstFieldSet->astField[usCnt].usDefLen,
                customTypeId);
            TtreeRecordCompareData(&dmv1, &dmv2, index, true);
        }
#endif
    }

    *pulCompareRslt = compareResult;
}

#define MAX_TRACE_PATH_BUFF_LEN 256

static char *TtreeGetOpStrByType(TtreeTraceTypeE type)
{
    switch (type) {
        case TTREE_TRACE_TYPE_INSERT:
            return "insert";
        case TTREE_TRACE_TYPE_DELETE:
            return "delete";
        case TTREE_TRACE_TYPE_UPDATE:
            return "update";
        case TTREE_TRACE_TYPE_SCAN:
            return "scan";
        default:
            return NULL;
    }
    return NULL;
}

void TtreeRecordTracePath(TTreeHeadT *ttreeIdx, TtreeTraceTypeE type, char *path)
{
    // 如果传入的是根节点，则将path里的内容全部清空
    if (strcmp("root", path) == 0) {
        (void)memset_s(ttreeIdx->tracePath, MAX_TRACE_PATH_DEPTH, 0x00, MAX_TRACE_PATH_DEPTH);
        ttreeIdx->tracePath[0] = 'R';
    } else if (strcmp("left", path) == 0 || strcmp("right", path) == 0) {
        for (uint32_t i = 0; i < MAX_TRACE_PATH_DEPTH; i++) {
            if (ttreeIdx->tracePath[i] == 0) {
                ttreeIdx->tracePath[i] = strcmp("left", path) == 0 ? 'l' : 'r';
                break;
            }
        }
    } else if (strcmp("cascade", path) == 0) {
        for (uint32_t i = 0; i < MAX_TRACE_PATH_DEPTH; i++) {
            if (ttreeIdx->tracePath[i] == 0) {
                ttreeIdx->tracePath[i] = 'c';
                break;
            }
        }
    } else {
        return;
    }
    char *opTypeStr = TtreeGetOpStrByType(type);
    char tracePath[MAX_TRACE_PATH_BUFF_LEN] = {0};
    for (uint32_t i = 0; i < MAX_TRACE_PATH_DEPTH; i++) {
        if (ttreeIdx->tracePath[i] == 0) {
            break;
        }
        switch (ttreeIdx->tracePath[i]) {
            case 'R':
                (void)strcat_s(tracePath, MAX_TRACE_PATH_BUFF_LEN, "root");
                break;
            case 'l':
                (void)strcat_s(tracePath, MAX_TRACE_PATH_BUFF_LEN, "left");
                break;
            case 'r':
                (void)strcat_s(tracePath, MAX_TRACE_PATH_BUFF_LEN, "rigth");
                break;
            case 'c':
                (void)strcat_s(tracePath, MAX_TRACE_PATH_BUFF_LEN, "cascade");
                break;
            default:
                return;
        }
        if (i + 1 < MAX_TRACE_PATH_DEPTH && ttreeIdx->tracePath[i + 1] != 0) {
            (void)strcat_s(tracePath, MAX_TRACE_PATH_BUFF_LEN, ">");
        }
    }
    DB_LOG_WARN_UNFOLD(GMERR_OK, "TTree Trace Info:Ttree %s index. current node : %s", opTypeStr, tracePath);
}

ALWAYS_INLINE_C static Status DataMatchFilterCondInternal(
    ScanDataCtxT *scanDataCtx, uint32_t condNum, EmbSimpleCondItemT *pstCond, bool *pbMatch, uint8_t *pucResult)
{
    DB_UNUSED(pucResult);
    if (condNum == 0) {
        *pbMatch = true;
        return STATUS_OK_INTER;
    }
    return SimpleRelMatchFilterCond(&scanDataCtx->heapTupleBuf, pstCond, condNum, pbMatch);
}

ALWAYS_INLINE_C static Status DataMatchIdxCondGetCompareRet(
    EmbSimpleCondItemT *pstCond, uint8_t *dataBuf, int8_t *cmpRet)
{
    Status ret = STATUS_OK_INTER;
    if (pstCond->v5Type == DB_DATATYPE_UINT32) {
        uint32_t v1 = *(uint32_t *)(dataBuf + pstCond->fldOffset);
        uint32_t v2 = *(uint32_t *)(pstCond->value);
        if (v1 < v2) {
            *cmpRet = -1;
        } else if (v1 > v2) {
            *cmpRet = 1;
        }
        if (SECUREC_UNLIKELY(TtreeGetTraceSwitchState())) {
            DB_LOG_WARN_UNFOLD(GMERR_OK, "TTree Trace Info:find: %" PRIu32 ", cmp(current: %" PRIu32 ").", v2, v1);
        }
    } else {
        // 内部比较，不会出错
        ret = V1ValueCmp(dataBuf + pstCond->fldOffset, pstCond->value, pstCond->propeMaxLen, pstCond->v1Type, cmpRet);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            DB_LOG_ERROR(ret, "Unable to compare idx conds.");
            return ret;
        }
        if (SECUREC_UNLIKELY(TtreeGetTraceSwitchState())) {
            DmValueT dmv1 = {0};
            uint32_t typeId = pstCond->v1Type;
            (void)EmbSetDmValue(&dmv1, pstCond->v5Type, dataBuf + pstCond->fldOffset, pstCond->propeMaxLen, typeId);
            DmValueT dmv2 = {0};
            (void)EmbSetDmValue(&dmv2, pstCond->v5Type, pstCond->value, pstCond->propeMaxLen, typeId);
            TtreeRecordCompareData(&dmv1, &dmv2, 0, false);
        }
    }
    return STATUS_OK_INTER;
}

ALWAYS_INLINE_C static Status DataMatchIdxCondInternal(
    ScanDataCtxT *scanDataCtx, uint32_t condNum, EmbSimpleCondItemT *pstCond, bool *pbMatch, uint8_t *pucResult)
{
    Status ret = STATUS_OK_INTER;
    uint16_t usCnt;
    uint8_t *pucData = scanDataCtx->heapTupleBuf.buf;

    for (usCnt = 0; usCnt < condNum; usCnt++) {
        uint8_t dir;
        VOS_BOOL bMatch;
        ret = V1ValueMatch(pucData + pstCond[usCnt].fldOffset, pstCond[usCnt].value, pstCond[usCnt].propeMaxLen,
            pstCond[usCnt].enOp, pstCond[usCnt].v1Type, &bMatch, &dir);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        if (dir != DBTC_SEARCH_ALL) {
            *pbMatch = false;
            *pucResult = dir;
            return STATUS_OK_INTER;
        }
    }
    // 所有索引条件均匹配
    *pbMatch = true;
    *pucResult = DBTC_SEARCH_ALL;
    return STATUS_OK_INTER;
}

ALWAYS_INLINE_C Status DataMatchIdxCond(
    IndexCtxT *idxCtx, DataInfoT dataInfo, EmbSimpleQueryCtxT *pstSearchPara, bool *pbMatch, uint8_t *pucResult)
{
    ScanDataCtxT scanDataCtx = {.dataInfo = dataInfo, .heapTupleBuf = {.bufSize = 0, .buf = idxCtx->tupleBuf.buf}};
    scanDataCtx.heapTupleBuf.buf = scanDataCtx.dataInfo.buf;

    return DataMatchIdxCondInternal(
        &scanDataCtx, pstSearchPara->idxCondNum, pstSearchPara->idxConds, pbMatch, pucResult);
}

/* 比较缓存的dataInfo与当前dataInfo大小  0 resume = cur; 1 resume > cur; -1 resume < cur */
inline static int8_t DataInfoCompare(DataInfoT *resumeDataInfo, DataInfoT *curDataInfo)
{
    if (IsDataInfoEqual(resumeDataInfo->addr64, curDataInfo->addr64)) {
        return 0;
    }

    if (IsDataInfoGT(*resumeDataInfo, *curDataInfo)) {
        return 1;
    }

    return -1;
}

static void DataMatchDataInfoWithFetch(
    DataInfoT *curDataInfo, DataInfoT *resumeDataInfo, GME_CMPTYPE_ENUM enOp, bool *pbMatch, uint8_t *pucResult)
{
    int8_t cmpRet = DataInfoCompare(resumeDataInfo, curDataInfo);
    if (cmpRet == 0) {  // 同一条数据，需要skip
        *pbMatch = false;
        *pucResult = enOp <= GME_CMP_LESSEQUAL ? DBTC_SEARCH_SMALL : DBTC_SEARCH_LARGE;
    } else if (cmpRet == 1) {  // 缓存的data info更大
        if (enOp <= GME_CMP_LESSEQUAL) {
            *pbMatch = true;
            *pucResult = DBTC_SEARCH_ALL;
        } else {
            *pbMatch = false;
            *pucResult = DBTC_SEARCH_LARGE;
        }
    } else {  // 缓存的data info更小
        if (enOp <= GME_CMP_LESSEQUAL) {
            *pbMatch = false;
            *pucResult = DBTC_SEARCH_SMALL;
        } else {
            *pbMatch = true;
            *pucResult = DBTC_SEARCH_ALL;
        }
    }
}

ALWAYS_INLINE_C static Status DataMatchIdxCondWithFetchInternal(
    ScanDataCtxT *scanDataCtx, EmbSimpleQueryCtxT *pstSearchPara, bool *pbMatch, uint8_t *pucResult)
{
    uint32_t condNum = pstSearchPara->idxCondNum;
    EmbSimpleCondItemT *pstCond = pstSearchPara->idxConds;
    if (SECUREC_UNLIKELY(condNum == 0)) {
        *pbMatch = true;
        *pucResult = DBTC_SEARCH_ALL;
        return STATUS_OK_INTER;
    }
    Status ret = STATUS_OK_INTER;
    uint8_t *pucData = scanDataCtx->heapTupleBuf.buf;
    uint8_t dir;
    VOS_BOOL bMatch;
    uint32_t i;
    for (i = 0; i < condNum; i++) {
        ret = V1ValueMatch(pucData + pstCond[i].fldOffset, pstCond[i].value, pstCond[i].propeMaxLen, pstCond[i].enOp,
            pstCond[i].v1Type, &bMatch, &dir);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }

        if (dir != DBTC_SEARCH_ALL) {
            *pbMatch = false;
            *pucResult = dir;
            return STATUS_OK_INTER;
        }

        ret = V1ValueMatch(pucData + pstCond[i].fldOffset, pstCond[i].value, pstCond[i].propeMaxLen, GME_CMP_EQUAL,
            pstCond[i].v1Type, &bMatch, &dir);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        if (!bMatch) {
            // 索引值不相等，退出比较
            break;
        }
    }

    *pbMatch = true;
    *pucResult = DBTC_SEARCH_ALL;
    if (i == condNum && bMatch) {
        DataMatchDataInfoWithFetch(
            &scanDataCtx->dataInfo, pstSearchPara->resumeDataInfo, pstCond[0].enOp, pbMatch, pucResult);
    }

    if (*pucResult == DBTC_SEARCH_ALL && pstSearchPara->isCmpOpOpposed) {
        return DataMatchIdxCondInternal(
            scanDataCtx, pstSearchPara->idxResumeCondNum, pstSearchPara->idxResumeConds, pbMatch, pucResult);
    }
    return STATUS_OK_INTER;
}

ALWAYS_INLINE_C Status DataMatchIdxCondWithFetch(
    IndexCtxT *idxCtx, DataInfoT dataInfo, EmbSimpleQueryCtxT *pstSearchPara, bool *pbMatch, uint8_t *pucResult)
{
    ScanDataCtxT scanDataCtx = {.dataInfo = dataInfo, .heapTupleBuf = {.bufSize = 0, .buf = idxCtx->tupleBuf.buf}};
    scanDataCtx.heapTupleBuf.buf = scanDataCtx.dataInfo.buf;

    return DataMatchIdxCondWithFetchInternal(&scanDataCtx, pstSearchPara, pbMatch, pucResult);
}

ALWAYS_INLINE_C Status DataMatchIdxCondBigObj(
    IndexCtxT *idxCtx, DataInfoT dataInfo, EmbSimpleQueryCtxT *pstSearchPara, bool *pbMatch, uint8_t *pucResult)
{
    ScanDataCtxT scanDataCtx = {.dataInfo = dataInfo, .heapTupleBuf = {.bufSize = 0, .buf = idxCtx->tupleBuf.buf}};
    (void)FetchVarTupleBufByTTree(idxCtx, &scanDataCtx, true, NULL);

    return DataMatchIdxCondInternal(
        &scanDataCtx, pstSearchPara->idxCondNum, pstSearchPara->idxConds, pbMatch, pucResult);
}

ALWAYS_INLINE_C Status DataMatchIdxCondWithFetchBigObj(
    IndexCtxT *idxCtx, DataInfoT dataInfo, EmbSimpleQueryCtxT *pstSearchPara, bool *pbMatch, uint8_t *pucResult)
{
    ScanDataCtxT scanDataCtx = {.dataInfo = dataInfo, .heapTupleBuf = {.bufSize = 0, .buf = idxCtx->tupleBuf.buf}};
    (void)FetchVarTupleBufByTTree(idxCtx, &scanDataCtx, true, NULL);

    return DataMatchIdxCondWithFetchInternal(&scanDataCtx, pstSearchPara, pbMatch, pucResult);
}

Status TTreeSelectRecordsInNode(
    IndexCtxT *idxCtx, EmbSimpleQueryCtxT *pstSearchPara, TTreeNodeT *pstTTreeNode, uint32_t nodeTTree)
{
    bool bMatch = true;
    uint8_t nRecNo;
    uint8_t ucDir = 0;
    Status ret = STATUS_OK_INTER;
    pstSearchPara->scanUserData.ttreePos = nodeTTree;

    for (nRecNo = 0; nRecNo < pstTTreeNode->ucCurIdxNum && !pstSearchPara->scanUserData.scanFinish; nRecNo++) {
        if (GetDataInfoMasterAddr(pstTTreeNode, nRecNo) == pstSearchPara->lastInvalidAddr) {
            continue;
        }
        ScanDataCtxT scanDataCtx = {.dataInfo = GetNormalDataInfo(idxCtx, pstTTreeNode, nRecNo),
            .heapTupleBuf = {.bufSize = 0, .buf = idxCtx->tupleBuf.buf}};
        scanDataCtx.heapTupleBuf.buf = scanDataCtx.dataInfo.buf;

        /* Check if the record matched with the filter condition  */
        ret = DataMatchFilterCondInternal(
            &scanDataCtx, pstSearchPara->filterCondNum, pstSearchPara->filterConds, &bMatch, &ucDir);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
        /* If there is a match then insert the record in the result set of the commm area */
        if (bMatch) {
            ret = FetchFixedTupleBufByTTree(idxCtx, &scanDataCtx, false, pstSearchPara);
            if (ret == NO_DATA_INTER) {
                ret = STATUS_OK_INTER;  // skip不可见数据
                continue;
            } else if (ret != STATUS_OK_INTER) {
                return ret;
            }
            /* Store the record to result set only if it required */
            ret = pstSearchPara->idxStoreDataFunc(&scanDataCtx, pstSearchPara, &pstSearchPara->scanUserData);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return ret;
}

inline static bool IsDataInfoMatch(DataInfoT d1, DataInfoT d2, uint8_t sortType)
{
    if (SECUREC_UNLIKELY(IsDataInfoEqual(d1.addr64, d2.addr64))) {
        return false;
    }

    if (sortType == DB_SORTTYPE_DESCEND && IsDataInfoLT(d1, d2)) {
        return true;
    }

    if (sortType == DB_SORTTYPE_ASCEND && IsDataInfoGT(d1, d2)) {
        return true;
    }

    return false;
}

inline static int8_t DataCompareResumeConds(uint32_t condNum, EmbSimpleCondItemT *resumeConds, uint8_t *dataBuf)
{
    if (condNum == 0) {
        return -1;
    }
    int8_t compareResult = 0;  // 默认相等
    for (uint32_t usCnt = 0; usCnt < condNum && compareResult == 0; usCnt++) {
        // 这里内部使用确定不会出错
        (void)DataMatchIdxCondGetCompareRet(&resumeConds[usCnt], dataBuf, &compareResult);
    }
    return compareResult;
}

inline static bool IsDataInfoValid(DataInfoT *dataInfo)
{
    return dataInfo->addr64 != DB_INVALID_UINT64;
}

static bool TTreeNeedSkipCurData(
    IndexCtxT *idxCtx, EmbSimpleQueryCtxT *pstSearchPara, ScanDataCtxT *curData, bool *needCheckSkip)
{
    EmbSimpleCondItemT *resumeConds = pstSearchPara->idxConds;  // 前面将额外条件用于快速找到起始位置
    int8_t compRet = DataCompareResumeConds(pstSearchPara->idxCondNum, resumeConds, curData->heapTupleBuf.buf);
    if (compRet == 0) {
        // 索引值一样，需要比较dataInfo
        if (IsDataInfoMatch(curData->dataInfo, *(pstSearchPara->resumeDataInfo), pstSearchPara->sortItem.sortType)) {
            *needCheckSkip = false;
            return false;
        } else {
            return true;
        }
    }

    if ((compRet > 0 && pstSearchPara->sortItem.sortType == DB_SORTTYPE_DESCEND) ||
        (compRet < 0 && pstSearchPara->sortItem.sortType == DB_SORTTYPE_ASCEND)) {
        return true;
    }

    *needCheckSkip = false;
    return false;
}

inline static void TTreeSetResumeSelectDirection(
    TTreeNodeT *pstTTreeNode, bool direct, int8_t *nRecNo, int8_t *stopRecNo, int8_t *incrVal)
{
    if (direct) {
        *incrVal = 1;
        *nRecNo = 0;
        *stopRecNo = (int8_t)pstTTreeNode->ucCurIdxNum;  // ucCurIdxNum最大值为TTREENODE_SIZE
    } else {
        *incrVal = -1;
        *nRecNo = (int8_t)pstTTreeNode->ucCurIdxNum - 1;
        *stopRecNo = -1;
    }
}

inline static void TTreeResumeSelectSetOriginIdxConds(EmbSimpleQueryCtxT *pstSearchPara,
    EmbSimpleCondItemT **originIdxConds, uint32_t *originIdxCondNum, bool *needCheckSkip)
{
    /*
        第一次fetch查询，不用校验是否要skip数据，直接查询，原始条件挂在idxConds下
        非第一次fetch查询，索引额外条件放在idxConds用于查找索引位置，防止重头开始查找，原始索引条件放在idxResumeConds下
    */
    if (pstSearchPara->isFirstFetch) {
        *needCheckSkip = false;
        *originIdxCondNum = pstSearchPara->idxCondNum;
        *originIdxConds = pstSearchPara->idxConds;
    } else {
        *needCheckSkip = true;
        *originIdxCondNum = pstSearchPara->idxResumeCondNum;
        *originIdxConds = pstSearchPara->idxResumeConds;
    }
}

// fetch场景的恢复查找，需要顺序遍历 direct为true，从小到大，为false从大到小
Status TTreeResumeSelectRecordsInNode(
    IndexCtxT *idxCtx, EmbSimpleQueryCtxT *pstSearchPara, TTreeNodeT *pstTTreeNode, uint32_t nodeTTree, bool direct)
{
    bool bMatch = true, needCheckSkip;
    uint8_t ucDir = 0;
    EmbSimpleCondItemT *originIdxConds;
    uint32_t originIdxCondNum;
    TTreeResumeSelectSetOriginIdxConds(pstSearchPara, &originIdxConds, &originIdxCondNum, &needCheckSkip);

    pstSearchPara->scanUserData.ttreePos = nodeTTree;
    int8_t nRecNo, stopRecNo, incrVal;
    TTreeSetResumeSelectDirection(pstTTreeNode, direct, &nRecNo, &stopRecNo, &incrVal);
    while (nRecNo != stopRecNo && !pstSearchPara->scanUserData.scanFinish) {
        ScanDataCtxT scanDataCtx = {.dataInfo = GetNormalDataInfo(idxCtx, pstTTreeNode, nRecNo),
            .heapTupleBuf = {.bufSize = 0, .buf = idxCtx->tupleBuf.buf}};
        Status ret = FetchFixedTupleBufByTTree(idxCtx, &scanDataCtx, false, pstSearchPara);
        nRecNo += incrVal;
        if (ret == NO_DATA_INTER) {
            continue;  // skip不可见数据
        } else if (ret != STATUS_OK_INTER) {
            return ret;
        }
        // skip前面已经fetch过的数据
        if (needCheckSkip && TTreeNeedSkipCurData(idxCtx, pstSearchPara, &scanDataCtx, &needCheckSkip)) {
            continue;
        }
        // skip不可用逻辑addr
        if (scanDataCtx.dataInfo.masterAddr64 == pstSearchPara->lastInvalidAddr) {
            continue;
        }
        // 比较原始条件
        ret = DataMatchIdxCondInternal(&scanDataCtx, originIdxCondNum, originIdxConds, &bMatch, &ucDir);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
        /* If there is no match then we can break out of the loop */
        if (false == bMatch) {
            pstSearchPara->lastInvalidAddr = scanDataCtx.dataInfo.masterAddr64;
            continue;
        }

        ret = DataMatchFilterCondInternal(
            &scanDataCtx, pstSearchPara->filterCondNum, pstSearchPara->filterConds, &bMatch, &ucDir);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }

        /* If there is a match then insert the record in the result set of the commm area */
        if (bMatch) {
            /* Store the record to result set only if it required */
            ret = pstSearchPara->idxStoreDataFunc(&scanDataCtx, pstSearchPara, &pstSearchPara->scanUserData);
            if (ret != GMERR_OK) {
                return ret;
            }
        } else {
            pstSearchPara->lastInvalidAddr = scanDataCtx.dataInfo.masterAddr64;  // 记录不可用逻辑addr
        }
    }
    return STATUS_OK_INTER;
}

Status BisearchOne(IndexCtxT *idxCtx, EmbSimpleQueryCtxT *pstSearchPara, uint32_t *recArray, uint32_t ulRecNum,
    bool *pbFind, uint32_t *pulRecPos)
{
    bool bMatch;       /* Boolean to hold the comparison result */
    uint8_t ucDir = 0; /* Comparison result */
    uint32_t ulMin;
    uint32_t ulMax;
    uint32_t ulMid;
    Status errCode;
    /*
     * Initialize ulMin with 0, ulMax with max number of records and ulMid
     * with record number at the middle of the list
     */
    ulMin = 0;
    ulMax = ulRecNum;
    ulMid = (ulMin + ulMax) >> 1;
    bMatch = false;
    /* Loop until ulMin is less than ulMax */
    while (ulMin < ulMax) {
        /* Check if the record matches the condition */
        errCode = DataMatchIdxCond(
            idxCtx, GetNormalDataInfoByAddr32(idxCtx, recArray[ulMid]), pstSearchPara, &bMatch, &ucDir);
        if (errCode != STATUS_OK_INTER) {
            DB_LOG_ERROR(errCode, "failure of data match condition operation");
            return errCode;
        }

        /* Record matches the condition the break from the loop */
        if (bMatch) {
            break;
        }

        /* Record is smaller than the search condition */
        if (ucDir == DBTC_SEARCH_SMALL) {
            ulMax = ulMid;
        } else {
            /* Update the new minimum to one more than  the middle value */
            ulMin = ulMid + 1;
        }

        /* Calculate new middle record number */
        ulMid = (ulMin + ulMax) >> 1;
    }

    /* Set output parameters */
    *pbFind = bMatch;
    *pulRecPos = ulMid;

    return STATUS_OK_INTER;
}

Status BisearchOneBigObj(IndexCtxT *idxCtx, EmbSimpleQueryCtxT *pstSearchPara, uint32_t *recArray, uint32_t ulRecNum,
    bool *pbFind, uint32_t *pulRecPos)
{
    bool bMatch;       /* Boolean to hold the comparison result */
    uint8_t ucDir = 0; /* Comparison result */
    uint32_t ulMin;
    uint32_t ulMax;
    uint32_t ulMid;
    Status errCode;
    /*
     * Initialize ulMin with 0, ulMax with max number of records and ulMid
     * with record number at the middle of the list
     */
    ulMin = 0;
    ulMax = ulRecNum;
    ulMid = (ulMin + ulMax) >> 1;
    bMatch = false;
    /* Loop until ulMin is less than ulMax */
    while (ulMin < ulMax) {
        /* Check if the record matches the condition */
        errCode = DataMatchIdxCondBigObj(
            idxCtx, GetDataInfoByAddr32(recArray[ulMid], idxCtx->idxSeType), pstSearchPara, &bMatch, &ucDir);
        if (errCode != STATUS_OK_INTER) {
            DB_LOG_ERROR(errCode, "failure of data match condition operation");
            return errCode;
        }

        /* Record matches the condition the break from the loop */
        if (bMatch) {
            break;
        }

        /* Record is smaller than the search condition */
        if (ucDir == DBTC_SEARCH_SMALL) {
            ulMax = ulMid;
        } else {
            /* Update the new minimum to one more than  the middle value */
            ulMin = ulMid + 1;
        }

        /* Calculate new middle record number */
        ulMid = (ulMin + ulMax) >> 1;
    }

    /* Set output parameters */
    *pbFind = bMatch;
    *pulRecPos = ulMid;

    return STATUS_OK_INTER;
}

Status TTreeCheckRecsAboveMatched(IndexCtxT *idxCtx, EmbSimpleQueryCtxT *pstSearchPara, TTreeNodeT *pstTTreeNode,
    uint32_t ulRecPos, uint32_t nodeTTree)
{
    Status errCode;
    bool bMatch = true;
    uint32_t nRecNo;
    uint8_t ucDir = 0;
    for (nRecNo = ulRecPos; (int32_t)nRecNo >= 0; nRecNo--) {
        if (GetDataInfoMasterAddr(pstTTreeNode, nRecNo) == pstSearchPara->lastInvalidAddr) {
            continue;
        }
        ScanDataCtxT scanDataCtx = {.dataInfo = GetNormalDataInfo(idxCtx, pstTTreeNode, nRecNo),
            .heapTupleBuf = {.bufSize = 0, .buf = idxCtx->tupleBuf.buf}};
        scanDataCtx.heapTupleBuf.buf = scanDataCtx.dataInfo.buf;
        errCode =
            DataMatchIdxCondInternal(&scanDataCtx, pstSearchPara->idxCondNum, pstSearchPara->idxConds, &bMatch, &ucDir);
        if (SECUREC_UNLIKELY(errCode != STATUS_OK_INTER)) {
            return errCode;
        }
        /* If there is no match then we can break out of the loop */
        if (false == bMatch) {
            break;
        }

        /* Check if the matched record matches the filter condition */
        errCode = DataMatchFilterCondInternal(
            &scanDataCtx, pstSearchPara->filterCondNum, pstSearchPara->filterConds, &bMatch, &ucDir);
        if (SECUREC_UNLIKELY(errCode != STATUS_OK_INTER)) {
            return errCode;
        }
        if (bMatch == false) {
            continue;
        }
        errCode = FetchFixedTupleBufByTTree(idxCtx, &scanDataCtx, false, pstSearchPara);
        if (errCode == NO_DATA_INTER) {
            errCode = STATUS_OK_INTER;  // skip不可见数据
            continue;
        } else if (errCode != STATUS_OK_INTER) {
            return errCode;
        }
        pstSearchPara->scanUserData.ttreePos = nodeTTree;
        errCode = pstSearchPara->idxStoreDataFunc(&scanDataCtx, pstSearchPara, &pstSearchPara->scanUserData);
        if (pstSearchPara->scanUserData.scanFinish || errCode != STATUS_OK_INTER) {
            return errCode;
        }
    }
    return STATUS_OK_INTER;
}

Status TTreeCheckRecsBelowMatched(IndexCtxT *idxCtx, EmbSimpleQueryCtxT *pstSearchPara, TTreeNodeT *pstTTreeNode,
    uint32_t ulRecPos, uint32_t nodeTTree)
{
    Status errCode;
    bool bMatch = true;
    uint32_t nRecNo;
    uint8_t ucDir = 0;
    uint32_t nCurIdxNum = pstTTreeNode->ucCurIdxNum;

    for (nRecNo = (ulRecPos + 1); nRecNo < nCurIdxNum; nRecNo++) {
        if (GetDataInfoMasterAddr(pstTTreeNode, nRecNo) == pstSearchPara->lastInvalidAddr) {
            continue;
        }
        ScanDataCtxT scanDataCtx = {.dataInfo = GetNormalDataInfo(idxCtx, pstTTreeNode, nRecNo),
            .heapTupleBuf = {.bufSize = 0, .buf = idxCtx->tupleBuf.buf}};
        scanDataCtx.heapTupleBuf.buf = scanDataCtx.dataInfo.buf;
        errCode =
            DataMatchIdxCondInternal(&scanDataCtx, pstSearchPara->idxCondNum, pstSearchPara->idxConds, &bMatch, &ucDir);
        if (SECUREC_UNLIKELY(errCode != STATUS_OK_INTER)) {
            return errCode;
        }
        /* If there is no match then break out of the loop */
        if (false == bMatch) {
            break;
        }

        /* Check if the record matched with the filter condition  */
        errCode = DataMatchFilterCondInternal(
            &scanDataCtx, pstSearchPara->filterCondNum, pstSearchPara->filterConds, &bMatch, &ucDir);
        if (SECUREC_UNLIKELY(errCode != STATUS_OK_INTER)) {
            return errCode;
        }
        if (bMatch == false) {
            continue;
        }
        errCode = FetchFixedTupleBufByTTree(idxCtx, &scanDataCtx, false, pstSearchPara);
        if (errCode == NO_DATA_INTER) {
            errCode = STATUS_OK_INTER;  // skip不可见数据
            continue;
        } else if (errCode != STATUS_OK_INTER) {
            return errCode;
        }
        pstSearchPara->scanUserData.ttreePos = nodeTTree;
        errCode = pstSearchPara->idxStoreDataFunc(&scanDataCtx, pstSearchPara, &pstSearchPara->scanUserData);
        if (pstSearchPara->scanUserData.scanFinish || errCode != STATUS_OK_INTER) {
            return errCode;
        }
    }
    return STATUS_OK_INTER;
}

Status TTreeSearchInNode(
    IndexCtxT *idxCtx, EmbSimpleQueryCtxT *pstSearchPara, TTreeNodeT *pstTTreeNode, uint32_t nodeTTree)
{
    Status errCode;
    bool bFind;
    uint32_t ulRecPos;

    /* Do a binary search on the current node */
    errCode =
        BisearchOne(idxCtx, pstSearchPara, pstTTreeNode->simpleAddr, pstTTreeNode->ucCurIdxNum, &bFind, &ulRecPos);
    if (errCode != STATUS_OK_INTER) {
        return errCode;
    }
    /* If there is no match then return SUCCESS */
    if (false == bFind) {
        return STATUS_OK_INTER;
    }

    /* Check if the records above the current matched record matches with the index condition */
    /* Return when error occurs or successfully find required records */
    errCode = TTreeCheckRecsAboveMatched(idxCtx, pstSearchPara, pstTTreeNode, ulRecPos, nodeTTree);
    if (errCode != STATUS_OK_INTER || pstSearchPara->scanUserData.scanFinish) {
        return errCode;
    }

    /* Check if the records below the current matched record matches with the index condition */
    /* Return when error occurs or successfully find required records */
    errCode = TTreeCheckRecsBelowMatched(idxCtx, pstSearchPara, pstTTreeNode, ulRecPos, nodeTTree);
    if (errCode != STATUS_OK_INTER || pstSearchPara->scanUserData.scanFinish) {
        return errCode;
    }

    // DBFI_RsltSetRetainReqRecord(pstRelDes, // (*pstSearchPara->userScanRowProc),
    return STATUS_OK_INTER;
}

Status TTreeSearchDescInTree(EmbSimpleQueryCtxT *pstSearchPara, TTreeHeadT *ttreeIdx, uint32_t nodeTTree,
    IndexCtxT *idxCtx, TTreeMatchIdxCondFunT *idxCondCmpFun)
{
    bool bMaxMatch = false, bMinMatch = false;
    uint8_t ucMaxDir = 0, ucMinDir = 0;
    TTreeNodeT *pstTTreeNode = GetTTreeNode(ttreeIdx, nodeTTree);

    if (pstTTreeNode->ucCurIdxNum == 0 || pstSearchPara->scanUserData.scanFinish) {
        return STATUS_OK_INTER;
    }

    Status errCode =
        idxCondCmpFun(idxCtx, GetSetRightDataInfoForCmp(idxCtx, pstTTreeNode), pstSearchPara, &bMaxMatch, &ucMaxDir);
    if (errCode != STATUS_OK_INTER) {
        return errCode;
    }

    if (DBTC_SEARCH_SMALL != ucMaxDir) {
        if (NULL_NODEID != pstTTreeNode->nodeRChild) {
            TTREE_TRACE_DEBUG_LOG(ttreeIdx, TTREE_TRACE_TYPE_SCAN, "right");
            errCode = TTreeSearchDescInTree(pstSearchPara, ttreeIdx, pstTTreeNode->nodeRChild, idxCtx, idxCondCmpFun);
            if (STATUS_OK_INTER != errCode || pstSearchPara->scanUserData.scanFinish) {
                return errCode;
            }
        }
        if (DBTC_SEARCH_LARGE == ucMaxDir) {
            return STATUS_OK_INTER;
        }
    }

    if ((pstTTreeNode->ucCurIdxNum - 1) != 0) {
        errCode =
            idxCondCmpFun(idxCtx, GetSetLeftDataInfoForCmp(idxCtx, pstTTreeNode), pstSearchPara, &bMinMatch, &ucMinDir);
        if (errCode != STATUS_OK_INTER) {
            return errCode;
        }
    } else {
        ucMinDir = ucMaxDir;
    }

    if (DBTC_SEARCH_SMALL != ucMinDir) {
        errCode = TTreeResumeSelectRecordsInNode(idxCtx, pstSearchPara, pstTTreeNode, nodeTTree, false);
        if (STATUS_OK_INTER != errCode) {
            return errCode;
        }
    }

    if (DBTC_SEARCH_LARGE != ucMinDir) {
        if (NULL_NODEID != pstTTreeNode->nodeLChild) {
            TTREE_TRACE_DEBUG_LOG(ttreeIdx, TTREE_TRACE_TYPE_SCAN, "left");
            errCode = TTreeSearchDescInTree(pstSearchPara, ttreeIdx, pstTTreeNode->nodeLChild, idxCtx, idxCondCmpFun);
            if (STATUS_OK_INTER != errCode || pstSearchPara->scanUserData.scanFinish) {
                return errCode;
            }
        }
    }
    return STATUS_OK_INTER;
}

Status TTreeSearchCurNode(
    EmbSimpleQueryCtxT *pstSearchPara, TTreeHeadT *ttreeIdx, uint32_t nodeTTree, IndexCtxT *idxCtx, bool match)
{
    if (idxCtx->destNode == NULL_NODEID) {
        idxCtx->destNode = nodeTTree;
    }
    Status errCode;
    TTreeNodeT *pstTTreeNode = GetTTreeNode(ttreeIdx, nodeTTree);
    if (pstSearchPara->isFetchSelect) {
        errCode = TTreeResumeSelectRecordsInNode(idxCtx, pstSearchPara, pstTTreeNode, nodeTTree, true);
        if (STATUS_OK_INTER != errCode) {
            return errCode;
        }
    } else {
        if (match) {
            /* Add the whole node to result list */
            errCode = TTreeSelectRecordsInNode(idxCtx, pstSearchPara, pstTTreeNode, nodeTTree);
            if (STATUS_OK_INTER != errCode) {
                return errCode;
            }
        } else {
            errCode = TTreeSearchInNode(idxCtx, pstSearchPara, pstTTreeNode, nodeTTree);
            if (STATUS_OK_INTER != errCode) {
                return errCode;
            }
        }
    }
    return errCode;
}

static inline void SetDestNode(IndexCtxT *idxCtx, bool needSet, uint32_t nodeTTree)
{
    if (needSet) {
        idxCtx->destNode = nodeTTree;
    }
}

// normal对象函数
Status TTreeSearchInTree(EmbSimpleQueryCtxT *pstSearchPara, TTreeHeadT *ttreeIdx, uint32_t nodeTTree, IndexCtxT *idxCtx,
    TTreeMatchIdxCondFunT *idxCondCmpFun)
{
    bool bMaxMatch = false;
    bool bMinMatch = false;
    uint8_t ucMaxDir = 0;
    uint8_t ucMinDir = 0;
    TTreeNodeT *pstTTreeNode = GetTTreeNode(ttreeIdx, nodeTTree);
    if (pstTTreeNode->ucCurIdxNum == 0 || pstSearchPara->scanUserData.scanFinish) {
        return STATUS_OK_INTER;
    }

    Status errCode =
        idxCondCmpFun(idxCtx, GetSetLeftDataInfoForCmp(idxCtx, pstTTreeNode), pstSearchPara, &bMinMatch, &ucMinDir);
    if (errCode != STATUS_OK_INTER) {
        return errCode;
    }

    // 查小或者查所有，则搜索当前左节点
    SetDestNode(idxCtx, (NULL_NODEID == idxCtx->destNode && ucMinDir == DBTC_SEARCH_ALL), nodeTTree);

    if (DBTC_SEARCH_LARGE != ucMinDir) {
        if (NULL_NODEID != pstTTreeNode->nodeLChild) {
            TTREE_TRACE_DEBUG_LOG(ttreeIdx, TTREE_TRACE_TYPE_SCAN, "left");
            errCode = TTreeSearchInTree(pstSearchPara, ttreeIdx, pstTTreeNode->nodeLChild, idxCtx, idxCondCmpFun);
            if (STATUS_OK_INTER != errCode || pstSearchPara->scanUserData.scanFinish) {
                return errCode;
            }
        }
        SetDestNode(idxCtx, (NULL_NODEID == idxCtx->destNode), nodeTTree);
        if (DBTC_SEARCH_SMALL == ucMinDir) {
            return STATUS_OK_INTER;
        }
    }

    if ((pstTTreeNode->ucCurIdxNum - 1) != 0) {
        errCode = idxCondCmpFun(
            idxCtx, GetSetRightDataInfoForCmp(idxCtx, pstTTreeNode), pstSearchPara, &bMaxMatch, &ucMaxDir);
        if (errCode != STATUS_OK_INTER) {
            return errCode;
        }
    } else {
        bMaxMatch = bMinMatch;
        ucMaxDir = ucMinDir;
    }
    SetDestNode(idxCtx, (NULL_NODEID == idxCtx->destNode && ucMaxDir == DBTC_SEARCH_ALL), nodeTTree);

    if (DBTC_SEARCH_LARGE != ucMaxDir) {
        errCode = TTreeSearchCurNode(pstSearchPara, ttreeIdx, nodeTTree, idxCtx, bMinMatch && bMaxMatch);
        if (errCode != STATUS_OK_INTER) {
            return errCode;
        }
    }
    /* If the search result is search all or search large the we search in the right child of the current node */
    if (DBTC_SEARCH_SMALL != ucMaxDir) {
        if (NULL_NODEID != pstTTreeNode->nodeRChild) {
            TTREE_TRACE_DEBUG_LOG(ttreeIdx, TTREE_TRACE_TYPE_SCAN, "right");
            errCode = TTreeSearchInTree(pstSearchPara, ttreeIdx, pstTTreeNode->nodeRChild, idxCtx, idxCondCmpFun);
            if (STATUS_OK_INTER != errCode || pstSearchPara->scanUserData.scanFinish) {
                return errCode;
            }
        }
        SetDestNode(idxCtx, (NULL_NODEID == idxCtx->destNode), nodeTTree);
    }
    return STATUS_OK_INTER;
}

// 父节点处理在外层
void TTreeFreeNode(TTreeHeadT *ttreeIdx, uint32_t nodeTTree)
{
    TTreeNodeT *pstTTreeNode = GetTTreeNode(ttreeIdx, nodeTTree);

    /* Update the free list for the segment */
    pstTTreeNode->nodeRChild = ttreeIdx->freeNode;
    pstTTreeNode->nodeLChild = ttreeIdx->freeNode;
    pstTTreeNode->leftTTreeBuf = NULL;
    pstTTreeNode->rightTTreeBuf = NULL;
    ttreeIdx->freeNode = nodeTTree;
    ttreeIdx->ulUsedTTreeNode--;
    return;
}

Status TTreeAllocNode(TTreeHeadT *ttreeIdx, uint32_t parentNodeId, bool isLeftChild, TTreeNodeCommonT **ppstTTreeNode,
    TTreeNodeCommonT **ppstTTreeNodeParent, SeRunCtxHdT seRunCtx)
{
    // 节点扩展，需要更新上层函数节点Addr
    if (ttreeIdx->freeNode == NULL_NODEID || ttreeIdx->nodeCnt == 0) {
        Status ret = TTreeExpand(ttreeIdx, seRunCtx);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        if (ppstTTreeNodeParent != NULL) {
            *ppstTTreeNodeParent = GetCommonTTreeNode(ttreeIdx, parentNodeId);
        }
    }

    *ppstTTreeNode = GetCommonTTreeNode(ttreeIdx, ttreeIdx->freeNode);

    if (ppstTTreeNodeParent != NULL) {
        if (isLeftChild) {
            (*ppstTTreeNodeParent)->nodeLChild = ttreeIdx->freeNode;
        } else {
            (*ppstTTreeNodeParent)->nodeRChild = ttreeIdx->freeNode;
        }
    } else {
        ttreeIdx->rootNode = ttreeIdx->freeNode;
    }
    ttreeIdx->freeNode = (*ppstTTreeNode)->nodeRChild;
    ttreeIdx->ulUsedTTreeNode++;

    (*ppstTTreeNode)->nodeLChild = NULL_NODEID;
    (*ppstTTreeNode)->nodeRChild = NULL_NODEID;
    (*ppstTTreeNode)->nodeParent = NULL_NODEID;
    (*ppstTTreeNode)->scBalance = 0;
    (*ppstTTreeNode)->ucCurIdxNum = 0;
    (*ppstTTreeNode)->idxSeType = ttreeIdx->idxSeType;
    return STATUS_OK_INTER;
}

static inline void BiSearchSetResult(bool *pbFind, uint16_t *pusRecPos, bool find, uint16_t ulLoop)
{
    *pbFind = find;
    *pusRecPos = ulLoop;
}

// 处理相等的索引，pusRecPos是按索引+recno排序后的插入位置
void BisearchTwoForCompareEqual(IndexCtxT *idxCtx, SiteFldT *pstFldSet, DataInfoT recNo, uint8_t *pucInputRec,
    uint32_t *recArray, uint32_t ulRecNum, uint32_t ulMid, bool *pbFind, uint16_t *pusRecPos, DataInfoT midDataInfo)
{
    uint32_t ulLoop;
    int8_t ulCompare = 0;
    if (IsDataInfoGT(recNo, midDataInfo)) {
        ulLoop = ulMid + 1;
        while (ulLoop < ulRecNum) {
            DataInfoT loopDataInfo = GetNormalDataInfoByAddr32(idxCtx, recArray[ulLoop]);
            if (recNo.addr64 == loopDataInfo.addr64) {
                *pusRecPos = (uint16_t)ulLoop;
                return;
            } else if (IsDataInfoGT(loopDataInfo, recNo)) {
                BiSearchSetResult(pbFind, pusRecPos, false, (uint16_t)ulLoop);
                return;
            }
            DataCompareRecord(pstFldSet, pucInputRec, loopDataInfo.buf, &ulCompare, ulLoop);

            if (ulCompare != 0) {
                break;
            }

            ulLoop++;
        }
    } else {
        ulLoop = ulMid;

        /*
         * Check the record numbers to the left of the obtained
         * position to find the suitable position to insert the
         * current record so as to maintain the sorted order.
         */
        while (ulLoop != 0) {
            ulLoop--;
            DataInfoT loopDataInfo = GetNormalDataInfoByAddr32(idxCtx, recArray[ulLoop]);
            if (recNo.addr64 == loopDataInfo.addr64) {
                *pusRecPos = (uint16_t)ulLoop;
                return;
            } else if (IsDataInfoGT(recNo, loopDataInfo)) {
                /* RecNo did not match, reset pbFind to FALSE */
                BiSearchSetResult(pbFind, pusRecPos, false, (uint16_t)(ulLoop + 1));
                return;
            }
            DataCompareRecord(pstFldSet, pucInputRec, loopDataInfo.buf, &ulCompare, ulLoop);

            if (ulCompare != 0) {
                ulLoop = ulLoop + 1;
                break;
            }
        }
    }
    *pbFind = false;
    *pusRecPos = (uint16_t)ulLoop;
}

/* 查找插入位置，输出pusRecPos为插入位置
    比如将0.5，插入数组0,1,2,3,4,5
    ulMin ulMax ulMid值动态变化为（0,6,3）（0,3,1）（0,1,0）（1,1,1）,pusRecPos最终为1
    normal对象函数
*/
void BisearchTwo(IndexCtxT *idxCtx, SiteFldT *pstFldSet, DataInfoT recNo, uint8_t *pucInputRec, uint32_t *recArray,
    uint32_t ulRecNum, bool *pbFind, uint16_t *pusRecPos)
{
    int8_t ulCompare;
    uint32_t ulMin;
    uint32_t ulMax;
    uint32_t ulMid;
    /*
     * Initialize ulMin with 0, ulMax with max number of records and ulMid
     * with record number at the middle of the list
     */
    ulMin = 0;
    ulMax = ulRecNum;
    ulMid = (ulMin + ulMax) >> 1;

    /* Loop until ulMin is less than ulMax */
    while (ulMin < ulMax) {
        DataInfoT dataInfo = GetNormalDataInfoByAddr32(idxCtx, recArray[ulMid]);
        DataCompareRecord(pstFldSet, pucInputRec, dataInfo.buf, &ulCompare, ulMid);

        switch (ulCompare) {
            case -1: {
                ulMax = ulMid;
                break;
            }
            case 1: {
                ulMin = ulMid + 1;
                break;
            }
            case 0: {
                /* Set the output variable to TRUE since the comparison result is EQUAL */
                *pbFind = true;
                /* Set the output parameter with the sort segment record position */
                if (dataInfo.addr64 == recNo.addr64) {
                    // 删除场景复用，已存在对应的heapAddr，事务场景后续做特殊处理
                    *pusRecPos = (uint16_t)ulMid;
                    return;
                }
                // 相同索引则比较recno，即heapAddr大小排序
                return BisearchTwoForCompareEqual(
                    idxCtx, pstFldSet, recNo, pucInputRec, recArray, ulRecNum, ulMid, pbFind, pusRecPos, dataInfo);
            }

            default: {
                break;
            }
        }

        ulMid = (ulMin + ulMax) >> 1;
    }

    /* Input record not found */
    *pbFind = false;

    /* Set the output parameter with the sort segment record position */
    *pusRecPos = (uint16_t)ulMid;
}

void TTreeDeleteInNode(TTreeNodeT *pstTTreeNode, IndexCtxT *idxCtx, SiteFldT *pstFldSet, DataInfoT recNo,
    uint8_t *pucInputRec, bool *pbDelete)
{
    bool bFind = false;
    uint16_t usRecPos = 0;
    uint32_t addr32 = TTreeCompressAddr64(recNo.addr64);
    for (uint8_t i = 0; i < pstTTreeNode->ucCurIdxNum; i++) {
        if (pstTTreeNode->simpleAddr[i] == addr32) {
            bFind = true;
            usRecPos = i;
        }
    }

    /* If the record is not found then return */
    if ((false == bFind) || (usRecPos >= pstTTreeNode->ucCurIdxNum)) {
        *pbDelete = false;
        return;
    }
    if (usRecPos == 0) {
        SetFixedLeftDataInfoNull(pstTTreeNode);
    }
    if (usRecPos == pstTTreeNode->ucCurIdxNum - 1) {
        SetFixedRightDataInfoNull(pstTTreeNode);
    }
    uint16_t usRecNo;
    /* Move all the records in higer position as compared to the  record found by one position up */
    for (usRecNo = usRecPos; usRecNo < (uint16_t)(pstTTreeNode->ucCurIdxNum - 1); usRecNo++) {
        pstTTreeNode->simpleAddr[usRecNo] = pstTTreeNode->simpleAddr[usRecNo + 1UL];
    }
    pstTTreeNode->ucCurIdxNum--;
    *pbDelete = true;
    return;
}

void TTreeRemoveSmallAndInsert(TTreeNodeT *pstTTreeNode, IndexCtxT *idxCtx, SiteFldT *pstFldSet, DataInfoT recNo,
    uint8_t *pucInputRec, DataInfoT *precSmall)
{
    DataInfoT recSmall;
    bool bFind;
    uint16_t usRecNo;
    uint16_t usRecPos = 0;

    /* Get the first record in the node */
    recSmall = GetNormalDataInfo(idxCtx, pstTTreeNode, 0);

    BisearchTwo(
        idxCtx, pstFldSet, recNo, pucInputRec, pstTTreeNode->simpleAddr, pstTTreeNode->ucCurIdxNum, &bFind, &usRecPos);

    // 待插入序列0,1,2,3,4,5，待插入数字2.5，则usRecPos为3，0移出，1、2左移
    if (usRecPos != 0) {
        usRecPos--;
        /* Delete smallest record & insert the current record into "usRecPos" */
        for (usRecNo = 0; usRecNo < usRecPos; usRecNo++) {
            pstTTreeNode->simpleAddr[usRecNo] = pstTTreeNode->simpleAddr[usRecNo + 1UL];
        }
    }
    SetTTreeNodePosData(pstTTreeNode, usRecPos, recNo.addr64);
    SetFixedLeftDataInfoNull(pstTTreeNode);
    if (usRecPos == 0) {
        SetFixedLeftDataInfo(pstTTreeNode, recNo.buf, recNo.masterAddr64);
    }
    if (usRecPos == pstTTreeNode->ucCurIdxNum - 1) {
        SetFixedRightDataInfo(pstTTreeNode, recNo.buf, recNo.masterAddr64);
    }
    *precSmall = recSmall;
}

void TTreeInsertInNode(
    TTreeNodeT *pstTTreeNode, IndexCtxT *idxCtx, SiteFldT *pstFldSet, DataInfoT recNo, uint8_t *pucInputRec)
{
    bool bFind;
    uint16_t usRecPos = 0;
    uint16_t usRecNo;

    BisearchTwo(
        idxCtx, pstFldSet, recNo, pucInputRec, pstTTreeNode->simpleAddr, pstTTreeNode->ucCurIdxNum, &bFind, &usRecPos);

    for (usRecNo = pstTTreeNode->ucCurIdxNum; usRecNo > usRecPos; usRecNo--) {
        pstTTreeNode->simpleAddr[usRecNo] = pstTTreeNode->simpleAddr[usRecNo - 1];
    }
    SetTTreeNodePosData(pstTTreeNode, usRecPos, recNo.addr64);
    pstTTreeNode->ucCurIdxNum++;
    if (usRecPos == pstTTreeNode->ucCurIdxNum - 1) {
        SetFixedRightDataInfo(pstTTreeNode, recNo.buf, recNo.masterAddr64);
    }
    if (usRecPos == 0) {
        SetFixedLeftDataInfo(pstTTreeNode, recNo.buf, recNo.masterAddr64);
    }
}

// normal对象函数
Status TTreeInsertInCurNode(IndexCtxT *idxCtx, TTreeHeadT *ttreeIdx, uint32_t nodeTTree, DataInfoT insertDataInfo,
    uint8_t *pucInputRec, SiteFldT *pstFldSet, bool *pbAffect, bool bDirectIns, uint32_t *insertTTreeNode)
{
    *insertTTreeNode = nodeTTree;
    TTreeNodeT *pstNewTTreeNode = NULL;
    DataInfoT recSmall;
    Status errCode;
    TTreeNodeT *pstTTreeNode = GetTTreeNode(ttreeIdx, nodeTTree);
    if (pstTTreeNode->ucCurIdxNum < TTREENODE_SIZE) {
        TTreeInsertInNode(pstTTreeNode, idxCtx, pstFldSet, insertDataInfo, pucInputRec);
        *pbAffect = false;
        return STATUS_OK_INTER;
    }
    TTreeRemoveSmallAndInsert(pstTTreeNode, idxCtx, pstFldSet, insertDataInfo, pucInputRec, &recSmall);

    TTREE_TRACE_DEBUG_LOG(ttreeIdx, TTREE_TRACE_TYPE_INSERT, "cascade");
    if (pstTTreeNode->nodeLChild == NULL_NODEID) {
        /* If the left child is NULL, then allocate a new node and insert the removed smallest record */
        errCode = TTreeAllocNode(
            ttreeIdx, nodeTTree, true, (void *)&pstNewTTreeNode, (void *)&pstTTreeNode, idxCtx->idxOpenCfg.seRunCtx);
        if (errCode != STATUS_OK_INTER) {
            return errCode;
        }
        SetTTreeNodePosData(pstNewTTreeNode, 0, recSmall.addr64);
        pstNewTTreeNode->ucCurIdxNum++;
        pstNewTTreeNode->nodeParent = nodeTTree;
        SetFixedRightDataInfo(pstNewTTreeNode, recSmall.buf, recSmall.masterAddr64);
        SetFixedLeftDataInfo(pstNewTTreeNode, recSmall.buf, recSmall.masterAddr64);
    } else {
        uint32_t newNode;
        errCode = TTreeInsertInTree(
            idxCtx, ttreeIdx, pstTTreeNode->nodeLChild, recSmall, recSmall.buf, pstFldSet, pbAffect, false, &newNode);
        if ((false == *pbAffect) || (STATUS_OK_INTER != errCode)) {
            return errCode;
        }
    }
    // TTreeInsertInTree操作可能触发节点扩展导致pstTTreeNode失效需要重新使用node数组访问
    return TTreeCalcInsBalance(ttreeIdx, (void *)GetTTreeNode(ttreeIdx, nodeTTree), true, pbAffect);
}

// nomral 对象函数
Status TTreeInsertInLeftTree(IndexCtxT *idxCtx, TTreeHeadT *ttreeIdx, uint32_t nodeTTree, DataInfoT insertDataInfo,
    uint8_t *pucInputRec, SiteFldT *pstFldSet, bool *pbAffect, uint32_t *insertTTreeNode)
{
    Status errCode;
    TTreeNodeT *pstNewTTreeNode = NULL;
    TTreeNodeT *pstTTreeNode = GetTTreeNode(ttreeIdx, nodeTTree);
    if (pstTTreeNode->nodeLChild == NULL_NODEID) {
        if (pstTTreeNode->ucCurIdxNum < TTREENODE_SIZE) {
            /* Mark the TTree node page as dirty */
            *insertTTreeNode = nodeTTree;
            TTreeInsertInNode(pstTTreeNode, idxCtx, pstFldSet, insertDataInfo, pucInputRec);
            *pbAffect = false;
            return STATUS_OK_INTER;
        }
        errCode = TTreeAllocNode(
            ttreeIdx, nodeTTree, true, (void *)&pstNewTTreeNode, (void *)&pstTTreeNode, idxCtx->idxOpenCfg.seRunCtx);
        if (errCode != STATUS_OK_INTER) {
            return errCode;
        }
        *insertTTreeNode = pstTTreeNode->nodeLChild;
        SetTTreeNodePosData(pstNewTTreeNode, 0, insertDataInfo.addr64);
        pstNewTTreeNode->ucCurIdxNum++;
        pstNewTTreeNode->nodeParent = nodeTTree;
    } else {
        errCode = TTreeInsertInTree(idxCtx, ttreeIdx, pstTTreeNode->nodeLChild, insertDataInfo, pucInputRec, pstFldSet,
            pbAffect, false, insertTTreeNode);
        if ((false == *pbAffect) || (STATUS_OK_INTER != errCode)) {
            return errCode;
        }
    }
    // TTreeInsertInTree操作可能触发节点扩展导致pstTTreeNode失效需要重新使用node数组访问
    return TTreeCalcInsBalance(ttreeIdx, (void *)GetTTreeNode(ttreeIdx, nodeTTree), true, pbAffect);
}

Status TTreeInsertInRightTree(IndexCtxT *idxCtx, TTreeHeadT *ttreeIdx, uint32_t nodeTTree, DataInfoT insertDataInfo,
    uint8_t *pucInputRec, SiteFldT *pstFldSet, bool *pbAffect, uint32_t *insertTTreeNode)
{
    Status errCode;
    TTreeNodeT *pstNewTTreeNode = NULL;
    TTreeNodeT *pstTTreeNode = GetTTreeNode(ttreeIdx, nodeTTree);
    if (pstTTreeNode->nodeRChild == NULL_NODEID) {
        if (pstTTreeNode->ucCurIdxNum < TTREENODE_SIZE) {
            *insertTTreeNode = nodeTTree;
            TTreeInsertInNode(pstTTreeNode, idxCtx, pstFldSet, insertDataInfo, pucInputRec);
            *pbAffect = false;
            return STATUS_OK_INTER;
        }
        errCode = TTreeAllocNode(
            ttreeIdx, nodeTTree, false, (void *)&pstNewTTreeNode, (void *)&pstTTreeNode, idxCtx->idxOpenCfg.seRunCtx);
        if (errCode != STATUS_OK_INTER) {
            return errCode;
        }
        *insertTTreeNode = pstTTreeNode->nodeRChild;
        SetTTreeNodePosData(pstNewTTreeNode, 0, insertDataInfo.addr64);
        pstNewTTreeNode->ucCurIdxNum++;
        pstNewTTreeNode->nodeParent = nodeTTree;
    } else {
        errCode = TTreeInsertInTree(idxCtx, ttreeIdx, pstTTreeNode->nodeRChild, insertDataInfo, pucInputRec, pstFldSet,
            pbAffect, false, insertTTreeNode);
        if ((false == *pbAffect) || (STATUS_OK_INTER != errCode)) {
            return errCode;
        }
    }
    // TTreeInsertInTree操作可能触发节点扩展导致pstTTreeNode失效需要重新使用node数组访问
    return TTreeCalcInsBalance(ttreeIdx, (void *)GetTTreeNode(ttreeIdx, nodeTTree), false, pbAffect);
}

// bDirectIns为true表示 比较只要左右子树为空且节点未满可直接插入当前节点
// insertTTreeNode返回实际插入的节点node
// normal对象函数
Status TTreeInsertInTree(IndexCtxT *idxCtx, TTreeHeadT *ttreeIdx, uint32_t nodeTTree, DataInfoT insertDataInfo,
    uint8_t *pucInputRec, SiteFldT *pstFldSet, bool *pbAffect, bool bDirectIns, uint32_t *insertTTreeNode)
{
    int8_t ulMinCompare;
    int8_t ulMaxCompare;
    TTreeNodeT *pstTTreeNode = GetTTreeNode(ttreeIdx, nodeTTree);
    // 无子树且有位置，直接插入当前节点
    bool bNoLeftRigth =
        (bool)(((pstTTreeNode->nodeLChild == NULL_NODEID) && (pstTTreeNode->nodeRChild == NULL_NODEID)) &&
               (pstTTreeNode->ucCurIdxNum < TTREENODE_SIZE));
    if (true == bNoLeftRigth) {
        *pbAffect = false;
        *insertTTreeNode = nodeTTree;
        TTreeInsertInNode(pstTTreeNode, idxCtx, pstFldSet, insertDataInfo, pucInputRec);
        return STATUS_OK_INTER;
    }
    DataInfoT dataInfo = GetSetLeftDataInfoForCmp(idxCtx, pstTTreeNode);
    /* Compare the record to be inserted with the first record in the node */
    DataCompareRecord(pstFldSet, pucInputRec, dataInfo.buf, &ulMinCompare, 0);
    if ((ulMinCompare < 0) || ((0 == ulMinCompare) && IsDataInfoGT(dataInfo, insertDataInfo))) {
        TTREE_TRACE_DEBUG_LOG(ttreeIdx, TTREE_TRACE_TYPE_INSERT, "left");
        return TTreeInsertInLeftTree(
            idxCtx, ttreeIdx, nodeTTree, insertDataInfo, pucInputRec, pstFldSet, pbAffect, insertTTreeNode);
    }
    DataInfoT rdataInfo = GetSetRightDataInfoForCmp(idxCtx, pstTTreeNode);
    if ((pstTTreeNode->ucCurIdxNum - 1) != 0) {
        DataCompareRecord(pstFldSet, pucInputRec, rdataInfo.buf, &ulMaxCompare, pstTTreeNode->ucCurIdxNum - 1);
    } else {
        ulMaxCompare = ulMinCompare;
    }

    if ((ulMaxCompare < 0) || ((ulMaxCompare == 0) && IsDataInfoGT(rdataInfo, insertDataInfo))) {
        return TTreeInsertInCurNode(
            idxCtx, ttreeIdx, nodeTTree, insertDataInfo, pucInputRec, pstFldSet, pbAffect, bDirectIns, insertTTreeNode);
    }
    TTREE_TRACE_DEBUG_LOG(ttreeIdx, TTREE_TRACE_TYPE_INSERT, "right");
    return TTreeInsertInRightTree(
        idxCtx, ttreeIdx, nodeTTree, insertDataInfo, pucInputRec, pstFldSet, pbAffect, insertTTreeNode);
}

// normal对象函数
void TTreeGetLargestRec(
    IndexCtxT *idxCtx, TTreeHeadT *ttreeIdx, uint32_t nodeTTree, DataInfoT *precLargest, uint32_t *newNode)
{
    TTreeNodeT *pstTTreeNode = GetTTreeNode(ttreeIdx, nodeTTree);
    /* If the nodes right child is not NULL then get the right child of the node */
    while (NULL_NODEID != pstTTreeNode->nodeRChild) {
        *newNode = pstTTreeNode->nodeRChild;
        pstTTreeNode = GetTTreeNode(ttreeIdx, pstTTreeNode->nodeRChild);
    }
    *precLargest = GetNormalDataInfo(idxCtx, pstTTreeNode, pstTTreeNode->ucCurIdxNum - 1);
    return;
}

// normal对象函数
Status TTreeDeleteInRecLarge(
    IndexCtxT *idxCtx, TTreeHeadT *ttreeIdx, uint32_t nodeTTree, SiteFldT *pstFldSet, bool *pbAffect, bool *pbDelete)
{
    TTreeNodeT *pstTTreeNode = GetTTreeNode(ttreeIdx, nodeTTree);
    uint32_t newNode;
    DataInfoT recLarge;
    uint8_t *pucLargeRec = NULL;
    TTreeGetLargestRec(idxCtx, ttreeIdx, pstTTreeNode->nodeLChild, &recLarge, &newNode);

    // 先插入再删除，不受删除树平衡影响。左子树最大上移，节点平移动作都在当前节点。
    uint16_t usRecNum;
    pucLargeRec = recLarge.buf;
    for (usRecNum = pstTTreeNode->ucCurIdxNum; usRecNum > 0; usRecNum--) {
        pstTTreeNode->simpleAddr[usRecNum] = pstTTreeNode->simpleAddr[usRecNum - 1];
    }
    SetTTreeNodePosData(pstTTreeNode, 0, recLarge.addr64);
    SetFixedLeftDataInfo(pstTTreeNode, recLarge.buf, recLarge.masterAddr64);
    if (pstTTreeNode->ucCurIdxNum - 1 == 0) {
        SetFixedRightDataInfo(pstTTreeNode, recLarge.buf, recLarge.masterAddr64);
    }
    /* Increment the number of records in current node */
    pstTTreeNode->ucCurIdxNum++;
    idxCtx->destNode = NULL_NODEID;  // 更新场景，删除挪动可能导致插入位置变化，之前记录的插入位置失效

    (void)TTreeDeleteInTree(
        idxCtx, ttreeIdx, pstTTreeNode->nodeLChild, recLarge, pucLargeRec, pstFldSet, pbAffect, pbDelete);
    if (true == *pbAffect) {
        Status errCode = TTreeCalcDelBalance(ttreeIdx, (void *)pstTTreeNode, false, pbAffect);
        if (STATUS_OK_INTER != errCode) {
            return errCode;
        }
    }
    *pbDelete = true;
    return STATUS_OK_INTER;
}

void TTreeDeleteFreeNode(TTreeHeadT *ttreeIdx, uint32_t nodeTTree, bool *pbAffect, bool *pbDelete)
{
    TTreeNodeCommonT *pstParent = NULL;
    TTreeNodeCommonT *pstTTreeNode = GetCommonTTreeNode(ttreeIdx, nodeTTree);
    if (NULL_NODEID == pstTTreeNode->nodeParent) {
        ttreeIdx->rootNode = pstTTreeNode->nodeRChild;
    } else {
        pstParent = GetCommonTTreeNode(ttreeIdx, pstTTreeNode->nodeParent);
        /* Make the current nodes parent point to the current nodes right child */
        if (pstParent->nodeLChild == nodeTTree) {
            pstParent->nodeLChild = pstTTreeNode->nodeRChild;
        } else {
            pstParent->nodeRChild = pstTTreeNode->nodeRChild;
        }
    }
    if (NULL_NODEID != pstTTreeNode->nodeRChild) {
        pstParent = GetCommonTTreeNode(ttreeIdx, pstTTreeNode->nodeRChild);
        pstParent->nodeParent = pstTTreeNode->nodeParent;
    }
    /* Free the current node */
    TTreeFreeNode(ttreeIdx, nodeTTree);

    *pbAffect = true;
    *pbDelete = true;
}

// normal对象函数
Status TTreeDeleteDataInfo(
    IndexCtxT *idxCtx, SiteFldT *pstFldSet, uint32_t nodeTTree, uint16_t usRecPos, bool *pbAffect, bool *pbDelete)
{
    TTreeHeadT *ttreeIdx = (TTreeHeadT *)idxCtx->idxRunCtx;
    TTreeNodeT *pstTTreeNode = GetTTreeNode(ttreeIdx, nodeTTree);
    if (usRecPos == 0) {
        SetFixedLeftDataInfoNull(pstTTreeNode);
    }
    if (usRecPos == pstTTreeNode->ucCurIdxNum - 1) {
        SetFixedRightDataInfoNull(pstTTreeNode);
    }
    /* Move all the records in higer position as compared to the  record found by one position up */
    for (uint16_t usRecNo = usRecPos; usRecNo < (uint16_t)(pstTTreeNode->ucCurIdxNum - 1); usRecNo++) {
        pstTTreeNode->simpleAddr[usRecNo] = pstTTreeNode->simpleAddr[usRecNo + 1UL];
    }
    pstTTreeNode->ucCurIdxNum--;

    if ((NULL_NODEID != pstTTreeNode->nodeLChild) && ((TTREENODE_BORDERLINE) > pstTTreeNode->ucCurIdxNum)) {
        TTREE_TRACE_DEBUG_LOG(ttreeIdx, TTREE_TRACE_TYPE_DELETE, "cascade");
        return TTreeDeleteInRecLarge(idxCtx, ttreeIdx, nodeTTree, pstFldSet, pbAffect, pbDelete);
    }
    if ((NULL_NODEID == pstTTreeNode->nodeLChild) && (pstTTreeNode->ucCurIdxNum == 0)) {
        TTreeDeleteFreeNode(ttreeIdx, nodeTTree, pbAffect, pbDelete);
        return STATUS_OK_INTER;
    }

    *pbAffect = false;
    *pbDelete = true;
    return STATUS_OK_INTER;
}

Status TTreeDeleteLeft(IndexCtxT *idxCtx, TTreeHeadT *ttreeIdx, uint32_t nodeTTree, DataInfoT delDataInfo,
    uint8_t *pucInputRec, SiteFldT *pstFldSet, bool *pbAffect, bool *pbDelete)
{
    TTreeNodeT *pstTTreeNode = GetTTreeNode(ttreeIdx, nodeTTree);
    Status errCode = TTreeDeleteInTree(
        idxCtx, ttreeIdx, pstTTreeNode->nodeLChild, delDataInfo, pucInputRec, pstFldSet, pbAffect, pbDelete);
    if (STATUS_OK_INTER != errCode) {
        return errCode;
    }
    if (true == *pbAffect) {
        /* Update the balance of the tree after deletion */
        return TTreeCalcDelBalance(ttreeIdx, (void *)pstTTreeNode, false, pbAffect);
    }
    return STATUS_OK_INTER;
}

Status TTreeDeleteRight(IndexCtxT *idxCtx, TTreeHeadT *ttreeIdx, uint32_t nodeTTree, DataInfoT delDataInfo,
    uint8_t *pucInputRec, SiteFldT *pstFldSet, bool *pbAffect, bool *pbDelete)
{
    TTreeNodeT *pstTTreeNode = GetTTreeNode(ttreeIdx, nodeTTree);
    Status errCode = TTreeDeleteInTree(
        idxCtx, ttreeIdx, pstTTreeNode->nodeRChild, delDataInfo, pucInputRec, pstFldSet, pbAffect, pbDelete);
    if (STATUS_OK_INTER != errCode) {
        return errCode;
    }
    if (true == *pbAffect) {
        return TTreeCalcDelBalance(ttreeIdx, (void *)pstTTreeNode, true, pbAffect);
    }
    return STATUS_OK_INTER;
}

// nomal对象函数
Status TTreeDeleteInTree(IndexCtxT *idxCtx, TTreeHeadT *ttreeIdx, uint32_t nodeTTree, DataInfoT delDataInfo,
    uint8_t *pucInputRec, SiteFldT *pstFldSet, bool *pbAffect, bool *pbDelete)
{
    int8_t ulMinCompare, ulMaxCompare;
    if (NULL_NODEID == nodeTTree) {
        *pbDelete = false;
        *pbAffect = false;
        return STATUS_OK_INTER;
    }
    TTreeNodeT *pstTTreeNode = GetTTreeNode(ttreeIdx, nodeTTree);
    if (pstTTreeNode->simpleAddr[0] == TTreeCompressAddr64(delDataInfo.addr64)) {
        return TTreeDeleteDataInfo(idxCtx, pstFldSet, nodeTTree, 0, pbAffect, pbDelete);
    }
    uint8_t lastId = pstTTreeNode->ucCurIdxNum - 1;
    if (pstTTreeNode->ucCurIdxNum > 1 && pstTTreeNode->simpleAddr[lastId] == TTreeCompressAddr64(delDataInfo.addr64)) {
        return TTreeDeleteDataInfo(idxCtx, pstFldSet, nodeTTree, lastId, pbAffect, pbDelete);
    }
    DataInfoT dataInfo = GetSetLeftDataInfoForCmp(idxCtx, pstTTreeNode);
    DataCompareRecord(pstFldSet, pucInputRec, dataInfo.buf, &ulMinCompare, 0);

    if ((ulMinCompare < 0) || ((0 == ulMinCompare) && IsDataInfoGT(dataInfo, delDataInfo))) {
        TTREE_TRACE_DEBUG_LOG(ttreeIdx, TTREE_TRACE_TYPE_DELETE, "left");
        return TTreeDeleteLeft(idxCtx, ttreeIdx, nodeTTree, delDataInfo, pucInputRec, pstFldSet, pbAffect, pbDelete);
    }
    DataInfoT rDataInfo = GetSetRightDataInfoForCmp(idxCtx, pstTTreeNode);
    /* Compare the record to be deleted with the last record in the node */
    DataCompareRecord(pstFldSet, pucInputRec, rDataInfo.buf, &ulMaxCompare, lastId);

    if ((ulMaxCompare > 0) || ((0 == ulMaxCompare) && IsDataInfoGT(delDataInfo, rDataInfo))) {
        TTREE_TRACE_DEBUG_LOG(ttreeIdx, TTREE_TRACE_TYPE_DELETE, "right");
        return TTreeDeleteRight(idxCtx, ttreeIdx, nodeTTree, delDataInfo, pucInputRec, pstFldSet, pbAffect, pbDelete);
    }
    /* Try deleting the ecord in the current node */
    TTreeDeleteInNode(pstTTreeNode, idxCtx, pstFldSet, delDataInfo, pucInputRec, pbDelete);

    if (false == *pbDelete) {
        return STATUS_OK_INTER;
    }
    if ((NULL_NODEID != pstTTreeNode->nodeLChild) && ((TTREENODE_BORDERLINE) > pstTTreeNode->ucCurIdxNum)) {
        TTREE_TRACE_DEBUG_LOG(ttreeIdx, TTREE_TRACE_TYPE_DELETE, "cascade");
        return TTreeDeleteInRecLarge(idxCtx, ttreeIdx, nodeTTree, pstFldSet, pbAffect, pbDelete);
    }
    if ((NULL_NODEID == pstTTreeNode->nodeLChild) && (pstTTreeNode->ucCurIdxNum == 0)) {
        TTreeDeleteFreeNode(ttreeIdx, nodeTTree, pbAffect, pbDelete);
        return STATUS_OK_INTER;
    }

    *pbAffect = false;
    *pbDelete = true;
    return STATUS_OK_INTER;
}

// 大对象函数
Status TTreeResumeSelectRecordsInNodeBigObj(
    IndexCtxT *idxCtx, EmbSimpleQueryCtxT *pstSearchPara, TTreeNodeT *pstTTreeNode, uint32_t nodeTTree, bool direct)
{
    bool bMatch = true, needCheckSkip;
    uint8_t ucDir = 0;
    EmbSimpleCondItemT *originIdxConds;
    uint32_t originIdxCondNum;
    TTreeResumeSelectSetOriginIdxConds(pstSearchPara, &originIdxConds, &originIdxCondNum, &needCheckSkip);
    pstSearchPara->scanUserData.ttreePos = nodeTTree;
    int8_t nRecNo, stopRecNo, incrVal;
    TTreeSetResumeSelectDirection((void *)pstTTreeNode, direct, &nRecNo, &stopRecNo, &incrVal);
    while (nRecNo != stopRecNo && !pstSearchPara->scanUserData.scanFinish) {
        ScanDataCtxT scanDataCtx = {
            .dataInfo = GetDataInfo(pstTTreeNode, nRecNo), .heapTupleBuf = {.bufSize = 0, .buf = idxCtx->tupleBuf.buf}};
        Status ret = FetchVarTupleBufByTTree(idxCtx, &scanDataCtx, false, pstSearchPara);
        nRecNo += incrVal;
        if (ret == NO_DATA_INTER) {
            continue;  // skip不可见数据
        } else if (ret != STATUS_OK_INTER) {
            return ret;
        }
        // skip前面已经fetch过的数据
        if (needCheckSkip && TTreeNeedSkipCurData(idxCtx, pstSearchPara, &scanDataCtx, &needCheckSkip)) {
            continue;
        }
        // skip不可用逻辑addr
        if (scanDataCtx.dataInfo.masterAddr64 == pstSearchPara->lastInvalidAddr) {
            continue;
        }
        // 比较原始条件
        ret = DataMatchIdxCondInternal(&scanDataCtx, originIdxCondNum, originIdxConds, &bMatch, &ucDir);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
        /* If there is no match then we can break out of the loop */
        if (false == bMatch) {
            pstSearchPara->lastInvalidAddr = scanDataCtx.dataInfo.masterAddr64;
            continue;
        }

        ret = DataMatchFilterCondInternal(
            &scanDataCtx, pstSearchPara->filterCondNum, pstSearchPara->filterConds, &bMatch, &ucDir);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }

        /* If there is a match then insert the record in the result set of the commm area */
        if (bMatch) {
            /* Store the record to result set only if it required */
            ret = pstSearchPara->idxStoreDataFunc(&scanDataCtx, pstSearchPara, &pstSearchPara->scanUserData);
            if (ret != GMERR_OK) {
                return ret;
            }
        } else {
            pstSearchPara->lastInvalidAddr = scanDataCtx.dataInfo.masterAddr64;  // 记录不可用逻辑addr
        }
    }
    return STATUS_OK_INTER;
}

Status TTreeCheckRecsAboveMatchedBigObj(IndexCtxT *idxCtx, EmbSimpleQueryCtxT *pstSearchPara, TTreeNodeT *pstTTreeNode,
    uint32_t ulRecPos, uint32_t nodeTTree)
{
    Status errCode;
    bool bMatch = true;
    uint32_t nRecNo;
    uint8_t ucDir = 0;
    for (nRecNo = ulRecPos; (int32_t)nRecNo >= 0; nRecNo--) {
        if (GetDataInfoMasterAddr(pstTTreeNode, nRecNo) == pstSearchPara->lastInvalidAddr) {
            continue;
        }
        ScanDataCtxT scanDataCtx = {
            .dataInfo = GetDataInfo(pstTTreeNode, nRecNo), .heapTupleBuf = {.bufSize = 0, .buf = idxCtx->tupleBuf.buf}};
        (void)FetchVarTupleBufByTTree(idxCtx, &scanDataCtx, true, NULL);
        errCode =
            DataMatchIdxCondInternal(&scanDataCtx, pstSearchPara->idxCondNum, pstSearchPara->idxConds, &bMatch, &ucDir);
        if (SECUREC_UNLIKELY(errCode != STATUS_OK_INTER)) {
            return errCode;
        }
        /* If there is no match then we can break out of the loop */
        if (false == bMatch) {
            break;
        }

        /* Check if the matched record matches the filter condition */
        errCode = DataMatchFilterCondInternal(
            &scanDataCtx, pstSearchPara->filterCondNum, pstSearchPara->filterConds, &bMatch, &ucDir);
        if (SECUREC_UNLIKELY(errCode != STATUS_OK_INTER)) {
            return errCode;
        }
        if (bMatch == false) {
            continue;
        }
        errCode = FetchVarTupleBufByTTree(idxCtx, &scanDataCtx, false, pstSearchPara);
        if (errCode == NO_DATA_INTER) {
            errCode = STATUS_OK_INTER;  // skip不可见数据
            continue;
        } else if (errCode != STATUS_OK_INTER) {
            return errCode;
        }
        pstSearchPara->scanUserData.ttreePos = nodeTTree;
        errCode = pstSearchPara->idxStoreDataFunc(&scanDataCtx, pstSearchPara, &pstSearchPara->scanUserData);
        if (pstSearchPara->scanUserData.scanFinish || errCode != STATUS_OK_INTER) {
            return errCode;
        }
    }
    return STATUS_OK_INTER;
}

Status TTreeCheckRecsBelowMatchedBigObj(IndexCtxT *idxCtx, EmbSimpleQueryCtxT *pstSearchPara, TTreeNodeT *pstTTreeNode,
    uint32_t ulRecPos, uint32_t nodeTTree)
{
    Status errCode;
    bool bMatch = true;
    uint32_t nRecNo;
    uint8_t ucDir = 0;
    uint32_t nCurIdxNum = pstTTreeNode->ucCurIdxNum;
    for (nRecNo = (ulRecPos + 1); nRecNo < nCurIdxNum; nRecNo++) {
        if (GetDataInfoMasterAddr(pstTTreeNode, nRecNo) == pstSearchPara->lastInvalidAddr) {
            continue;
        }
        ScanDataCtxT scanDataCtx = {
            .dataInfo = GetDataInfo(pstTTreeNode, nRecNo), .heapTupleBuf = {.bufSize = 0, .buf = idxCtx->tupleBuf.buf}};
        (void)FetchVarTupleBufByTTree(idxCtx, &scanDataCtx, true, NULL);
        errCode =
            DataMatchIdxCondInternal(&scanDataCtx, pstSearchPara->idxCondNum, pstSearchPara->idxConds, &bMatch, &ucDir);
        if (SECUREC_UNLIKELY(errCode != STATUS_OK_INTER)) {
            return errCode;
        }
        /* If there is no match then break out of the loop */
        if (false == bMatch) {
            break;
        }

        /* Check if the record matched with the filter condition  */
        errCode = DataMatchFilterCondInternal(
            &scanDataCtx, pstSearchPara->filterCondNum, pstSearchPara->filterConds, &bMatch, &ucDir);
        if (SECUREC_UNLIKELY(errCode != STATUS_OK_INTER)) {
            return errCode;
        }
        if (bMatch == false) {
            continue;
        }
        errCode = FetchVarTupleBufByTTree(idxCtx, &scanDataCtx, false, pstSearchPara);
        if (errCode == NO_DATA_INTER) {
            errCode = STATUS_OK_INTER;  // skip不可见数据
            continue;
        } else if (errCode != STATUS_OK_INTER) {
            return errCode;
        }
        pstSearchPara->scanUserData.ttreePos = nodeTTree;
        errCode = pstSearchPara->idxStoreDataFunc(&scanDataCtx, pstSearchPara, &pstSearchPara->scanUserData);
        if (pstSearchPara->scanUserData.scanFinish || errCode != STATUS_OK_INTER) {
            return errCode;
        }
    }
    return STATUS_OK_INTER;
}

void TTreeFreeAllNodes(TTreeHeadT *ttreeIdx, DbMemCtxT *idxMemCtx)
{
    for (uint32_t i = 0; i < DbListGetItemCnt(&ttreeIdx->nodeArrList); i++) {
        TTreeNodeArrT *nodeArr = DbListItem(&ttreeIdx->nodeArrList, i);
        if (SECUREC_LIKELY(DbIsShmPtrValid(nodeArr->addr))) {
            DbShmemCtxFree(idxMemCtx, nodeArr->addr);
            nodeArr->addr = DB_INVALID_SHMPTR;
        }
    }
    DbDestroyList(&ttreeIdx->nodeArrList);
    ttreeIdx->nodeArrList = (DbListT){0};
}
