/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: se_hash_index stash_page.c
 * Description: Implementation of Hash Index Stash Page
 * Author:
 * Create: 2022/12/05
 */

#include "se_hash_index_stash_page.h"
#include "se_hash_mem.h"
#include "se_hash_index.h"
#include "se_hash_index_version_page.h"
#include "db_bit.h"

#ifdef __cplusplus
extern "C" {
#endif

static uint8_t *HashStashPageGetBitmap(HtStashPageHeadT *head)
{
    return head->hashEntry;
}

/*
 * description: 根据entryIdx，查看该entry对应的bitmap位是否非0
 * param {HtStashPageHeadT} *head [in] stash page头，用来定位bitmap位置
 * param {uint32_t} entryIdx [in] entry下标
 * return {*} true表示entry在使用，false表示未使用
 */
static bool HashStashPageBitmapIsSet(HtStashPageHeadT *head, uint32_t entryIdx)
{
    DB_POINTER(head);
    uint8_t *bitmap = HashStashPageGetBitmap(head);
    uint32_t idx = entryIdx >> 3;   // 右移3位，除以8
    uint32_t pos = entryIdx & 0x7;  // 对8取余
    return BitMapIsSet((uint32_t)bitmap[idx], pos);
}

static void HashStashPageBitMapSet(HtStashPageHeadT *head, uint32_t entryIdx)
{
    DB_POINTER(head);
    uint8_t *bitmap = HashStashPageGetBitmap(head);
    uint32_t idx = entryIdx >> 3;   // 右移3位，除以8
    uint32_t pos = entryIdx & 0x7;  // 对8取余
    bitmap[idx] = (uint8_t)BitMapSet((uint32_t)bitmap[idx], pos);
}

static void HashStashPageBitMapUnSet(HtStashPageHeadT *head, uint32_t entryIdx)
{
    DB_POINTER(head);
    uint8_t *bitmap = HashStashPageGetBitmap(head);
    uint32_t idx = entryIdx >> 3;   // 右移3位，除以8
    uint32_t pos = entryIdx & 0x7;  // 对8取余
    bitmap[idx] = (uint8_t)BitMapUnSet((uint32_t)bitmap[idx], pos);
}

Status HashGetStashPageHeadByPageId(HashMemRunCtxT *memRunCtx, uint32_t pageId, HtStashPageHeadT **stashPageHead)
{
    DB_POINTER2(memRunCtx, stashPageHead);
    DbMemAddrT stashPage = DB_INVALID_MEM_ADDR;
    Status ret = HtGetDataPage(memRunCtx, SE_INVALID_BLOCK_ID, pageId, &stashPage);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "SE-Hash, Unable to get stash page head by page id %" PRIu32 ".", pageId);
        return ret;
    }
    *stashPageHead = HashGetStashPageHeadByAddr(stashPage.virtAddr);
    if (*stashPageHead == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "SE-Hash, get stash page head unsucc, page id %" PRIu32 ".", pageId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return GMERR_OK;
}

Status InitBitmap(HtStashPageHeadT *stashPageHead)
{
    // 一个hashEntry的bit数
    uint32_t bitCountPerEntry = stashPageHead->hashEntrySize * DB_8BIT;
    // 计算需要把几个entry的空间腾出作为bitmap使用才能表示页上所有的entry
    uint32_t useForBitmap = DB_INVALID_UINT32;
    for (uint32_t i = 1; i <= stashPageHead->hashEntryPerStashPage; i++) {
        if (i * bitCountPerEntry >= stashPageHead->hashEntryPerStashPage - i) {
            useForBitmap = i;
            break;
        }
    }
    if (useForBitmap == DB_INVALID_UINT32) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
            "SE-Hash, unable to init bitmap, entry size: %" PRIu32 ", entry2bit: %" PRIu32 ", stash pageId %" PRIu32,
            stashPageHead->hashEntrySize, bitCountPerEntry, stashPageHead->blockId);
        return GMERR_INTERNAL_ERROR;
    }
    // 按 uint8_t 划分得到bitmap数组大小，每位对应一个hashEntry是否使用
    stashPageHead->bitmapArrSize = (uint32_t)(useForBitmap * stashPageHead->hashEntrySize / sizeof(uint8_t));
    uint8_t *bitmap = HashStashPageGetBitmap(stashPageHead);
    // bitmap初始值置0
    (void)memset_s(
        bitmap, sizeof(uint8_t) * stashPageHead->bitmapArrSize, 0, sizeof(uint8_t) * stashPageHead->bitmapArrSize);
    // 更新page剩余的entry个数
    stashPageHead->hashEntryPerStashPage -= useForBitmap;
    return GMERR_OK;
}

// stash page页面结构： HtStashPageHeadT | bitmap... | hashEntry....
static Status HashStashPageInit(
    HashTableT *ht, HashMemRunCtxT *memRunCtx, HtStashPageHeadT *newStashPageHead, uint32_t pageId)
{
    DB_POINTER3(ht, memRunCtx, newStashPageHead);
    newStashPageHead->hashEntryUsedNum = 0;
    newStashPageHead->hashEntrySize = ht->memMgr.ccehMeta.hashEntrySize;
    newStashPageHead->hashEntryPerStashPage =
        (ht->pageSize - (uint32_t)sizeof(HtStashPageHeadT)) / newStashPageHead->hashEntrySize;
    newStashPageHead->probeLen = 0;
    DB_ASSERT(newStashPageHead->hashEntryPerStashPage != 0);
    Status ret;
    if (ht->stashPageId == SE_INVALID_PAGE_ID) {
        newStashPageHead->prevPageId = SE_INVALID_PAGE_ID;
        newStashPageHead->nextPageId = SE_INVALID_PAGE_ID;
    } else {
        // 当前stash page头节点已经存在
        HtStashPageHeadT *oldStashPageHead = NULL;
        ret = HashGetStashPageHeadByPageId(memRunCtx, ht->stashPageId, &oldStashPageHead);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "SE-Hash, get page unsucc when init stash page, page id %" PRIu32 "", ht->stashPageId);
            return ret;
        }
        DB_POINTER(oldStashPageHead);
        newStashPageHead->prevPageId = SE_INVALID_PAGE_ID;
        newStashPageHead->nextPageId = ht->stashPageId;
        oldStashPageHead->prevPageId = pageId;
    }
    ret = InitBitmap(newStashPageHead);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 初始化stash page所有entry
    for (uint32_t entryIdx = 0; entryIdx < newStashPageHead->hashEntryPerStashPage; entryIdx++) {
        HashEntryBaseT *currentEntryBase = HashStashPageGetHashEntryBase(newStashPageHead, entryIdx);
        SetHashEntryTpAddr(ht, currentEntryBase, HEAP_INVALID_ADDR);
        SetHashEntryExtend(
            ht, currentEntryBase, (HashEntryExtendT){DB_INVALID_TRX_ID, HEAP_INVALID_ADDR, INVALID_MV_NODE});
        *currentEntryBase = (HashEntryBaseT){0};
    }
    ht->stashPageId = pageId;
    return GMERR_OK;
}

Status HashStashPageCreate(IndexCtxT *idxCtx, HtStashPageHeadT **stashPageHead)
{
    DB_POINTER3(idxCtx, idxCtx->idxRunCtx, stashPageHead);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;
    HashAllocPageParamT allocParam = {.tableSpaceIndex = ht->idxBase.indexCfg.tableSpaceIndex, .isDir = false};
    uint32_t blockId = SE_INVALID_BLOCK_ID;
    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;
    uint32_t pageId;
    Status ret = HtAllocMemPage(memRunCtx, &allocParam, &blockId, &pageInfo, &pageId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "SE-Hash, Unable to alloc stash page.");
        return ret;
    }
    HtStashPageHeadT *newStashPageHead = HashGetStashPageHeadByAddr(pageInfo.virtAddr);
    newStashPageHead->blockId = blockId;
    ret = HashStashPageInit(ht, memRunCtx, newStashPageHead, pageId);
    if (ret != GMERR_OK) {
        Status result = HtFreeMemPage(memRunCtx, pageId, blockId, NULL);
        if (result != GMERR_OK) {
            DB_LOG_ERROR(
                result, "SE-Hash,Unable to free hash stash page after init unsucc, page id = %" PRIu32 ".", pageId);
        }
        return ret;
    }
    *stashPageHead = newStashPageHead;
    return GMERR_OK;
}

#define TIMEOUT_LOG_FORMAT                                                                                \
    "SE-CCEH, lookup stash page %" PRIu32 ", loop %" PRIu32 ", get page: %" PRIu32 "ms, lookup: %" PRIu32 \
    "ms, total: %" PRIu32 "ms, probelen %" PRIu32 ", %" PRIu32 ", indexId %" PRIu32 ""

Status HashStashPageLookupWithOldRecord(
    IndexCtxT *idxCtx, HashLookupParaT *para, bool *foundNewAddr, TupleAddr *newAddr)
{
    DB_POINTER4(idxCtx, para, foundNewAddr, newAddr);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t stashPageId = ht->stashPageId;
    uint64_t start = DbRdtsc();
    for (uint32_t i = 0; i < ht->stashPageNum && stashPageId != SE_INVALID_PAGE_ID; i++) {
        uint64_t stepOne = DbRdtsc();
        HtStashPageHeadT *stashPageHead = NULL;
        Status ret =
            HashGetStashPageHeadByPageId(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, stashPageId, &stashPageHead);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to get stash page head when lookup");
            return ret;
        }
        uint64_t stepTwo = DbRdtsc();
        uint32_t entryIdx;
        for (entryIdx = 0; entryIdx < stashPageHead->probeLen; entryIdx++) {
            if (!HashStashPageBitmapIsSet(stashPageHead, entryIdx)) {
                continue;
            }
            HashEntryBaseT *currentEntryBase =
                HashStashPageGetHashEntryBaseAndAddr(ht, stashPageHead, entryIdx, &para->addr);
            if (para->addr == HEAP_INVALID_ADDR || currentEntryBase->hashCode != para->hashCode) {
                continue;
            }
            HtHashLookupParaT htLookupPara = {.entry = currentEntryBase, .isMatch = false, .isSelfDel = false};
            ret = HashSegmentLookupCmp(idxCtx, para, &htLookupPara);
            if (ret != GMERR_OK) {
                return ret;
            }
            if (HashCheckLookupResult(idxCtx, &htLookupPara, para, foundNewAddr, newAddr)) {
                return GMERR_OK;
            }
        }
        uint64_t stepThree = DbRdtsc();
        if ((uint64_t)DbToSeconds(stepThree - stepOne) > HASH_INDEX_TIMEOUT) {
            DB_LOG_WARN(GMERR_INTERNAL_ERROR, TIMEOUT_LOG_FORMAT, ht->stashPageNum, i,
                (uint32_t)DbToMseconds(stepTwo - stepOne), (uint32_t)DbToMseconds(stepThree - stepOne),
                (uint32_t)DbToMseconds(stepThree - start), entryIdx, stashPageHead->probeLen,
                idxCtx->idxMetaCfg.indexId);
        }
        stashPageId = stashPageHead->nextPageId;
    }
    if (*foundNewAddr) {
        para->addr = *newAddr;
        return GMERR_OK;
    }
    return GMERR_NO_DATA;
}

Status HashStashPageLookupWithoutOldRecord(IndexCtxT *idxCtx, HashLookupParaT *param)
{
    DB_POINTER2(idxCtx, param);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t stashPageId = ht->stashPageId;
    uint64_t startTime = DbRdtsc();
    for (uint32_t i = 0; i < ht->stashPageNum && stashPageId != SE_INVALID_PAGE_ID; i++) {
        uint64_t stepOneTime = DbRdtsc();
        HtStashPageHeadT *stashPageHead = NULL;
        Status ret =
            HashGetStashPageHeadByPageId(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, stashPageId, &stashPageHead);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to get stash page head when lookup");
            return ret;
        }
        uint64_t stepTwoTime = DbRdtsc();
        uint32_t entryIdx;
        for (entryIdx = 0; entryIdx < stashPageHead->probeLen; entryIdx++) {
            if (!HashStashPageBitmapIsSet(stashPageHead, entryIdx)) {
                continue;
            }
            HashEntryBaseT *currentEntryBase =
                HashStashPageGetHashEntryBaseAndAddr(ht, stashPageHead, entryIdx, &param->addr);
            if (param->addr == HEAP_INVALID_ADDR || currentEntryBase->hashCode != param->hashCode) {
                continue;
            }
            HtHashLookupParaT htLookupPara = {.entry = currentEntryBase, .isMatch = false, .isSelfDel = false};
            ret = HashSegmentLookupCmp(idxCtx, param, &htLookupPara);
            if (ret != GMERR_OK || (htLookupPara.isMatch && !htLookupPara.isSelfDel)) {
                return ret;
            }
        }
        uint64_t stepThreeTime = DbRdtsc();
        if ((uint64_t)DbToSeconds(stepThreeTime - stepOneTime) > HASH_INDEX_TIMEOUT) {
            DB_LOG_WARN(GMERR_INTERNAL_ERROR,
                "SE-CCEH, lookup stash page %" PRIu32 ", loop %" PRIu32 ", get page: %" PRIu32 "ms, lookup: %" PRIu32
                "ms, total: %" PRIu32 "ms, probelen %" PRIu32 ", %" PRIu32 ", indexId %" PRIu32,
                ht->stashPageNum, i, (uint32_t)DbToMseconds(stepTwoTime - stepOneTime),
                (uint32_t)DbToMseconds(stepThreeTime - stepOneTime), (uint32_t)DbToMseconds(stepThreeTime - startTime),
                entryIdx, stashPageHead->probeLen, idxCtx->idxMetaCfg.indexId);
        }
        stashPageId = stashPageHead->nextPageId;
        DB_ASSERT((i == ht->stashPageNum - 1) == (stashPageId == SE_INVALID_PAGE_ID));
    }
    return GMERR_NO_DATA;
}

inline static void HashStashPageEraseEntry(
    IndexCtxT *idxCtx, HashTableT *hashTable, const HashLookupParaT *lookupPara, HashRemoveParaT *rmPara)
{
    if (HashIndexIsMultiVersion(hashTable)) {
        HashMVNodeT *node = GetHashEntryMVNodeHeader(hashTable, rmPara->entry);
        if (lookupPara->isErase && lookupPara->isUndo && !HashMVNodeIsInvalid(node)) {
#ifndef NDEBUG
            TupleAddr addr = GetHashEntryBaseAddr(hashTable, rmPara->entry);
            DB_ASSERT(lookupPara->addr == addr);
#endif
            // 多版本数据挪到主版本
            (void)HashMVNodeUndoInsert(idxCtx, rmPara, *node);
            return;
        }
    }
    rmPara->entry->hashCode = 0u;
    rmPara->entry->isDeleted = 0u;
    SetHashEntryTpAddr(hashTable, rmPara->entry, HEAP_INVALID_ADDR);
    SetHashEntryExtend(
        hashTable, rmPara->entry, (HashEntryExtendT){DB_INVALID_TRX_ID, HEAP_INVALID_ADDR, INVALID_MV_NODE});
    (void)DbAtomicSub(&hashTable->entryUsed, 1u);
}

static void HashStashPageRemoveFoundHashEntry(
    IndexCtxT *idxCtx, HtStashPageHeadT *stashPageHead, HashRemoveParaT *rmPara, const HashLookupParaT *para)
{
    rmPara->probeEnd = true;
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint64_t entryVersion = GetHashEntryVerion(ht, rmPara->entry);
    if (para->isGC && !para->isMarkDelete) {       // GC回收, OCC
        DB_ASSERT(rmPara->entry->isDeleted == 0);  // 多版本下不会标记删除
        // GC 线程回收多版本的情况下，比较保证entryVersion 是已提交的版本
        if (SeTransIsCommit(idxCtx->idxOpenCfg.seRunCtx, entryVersion)) {
            HashStashPageEraseEntry(idxCtx, ht, para, rmPara);
        } else {
            rmPara->probeEnd = false;
        }
    } else if (para->isGC && para->isMarkDelete) {  // GC回收，PCC
        if (rmPara->entry->isDeleted == 1) {
            HashStashPageEraseEntry(idxCtx, ht, para, rmPara);
        }
    } else if (para->isErase && !para->isMarkDelete) {
        // 多版本的回滚 必须保证entryVersion 和本次操作的version 一致
        uint64_t selfVersion = SeTransGetTrxId(idxCtx->idxOpenCfg.seRunCtx);
        if (entryVersion != selfVersion) {
            rmPara->probeEnd = false;
        } else {
            HashStashPageEraseEntry(idxCtx, ht, para, rmPara);
        }
    } else if (para->isErase && para->isMarkDelete) {
        // 非多版本可以直接回滚
        HashStashPageEraseEntry(idxCtx, ht, para, rmPara);
    } else {
        if (para->isMarkDelete) {
            rmPara->entry->isDeleted = 1u;
        } else {
            HashRemoveHashEntrySetOldAddr(idxCtx, rmPara, ht, entryVersion);
        }
    }
    if (GetHashEntryBaseAddr(ht, rmPara->entry) == HEAP_INVALID_ADDR) {
        HashStashPageBitMapUnSet(stashPageHead, rmPara->entryIdx);
    }
}

#ifndef NDEBUG
inline static void StashPageRemoveCheckMVList(
    IndexCtxT *idxCtx, HashTableT *hashTable, TupleAddr addr, HashEntryBaseT *majorVersionEntry)
{
    if (!HashIndexIsMultiVersion(hashTable)) {
        return;
    }
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)(void *)idxCtx->idxRunCtx)->memRunCtx;
    HashMVNodeT mvListHeadNode = *GetHashEntryMVNodeHeader(hashTable, majorVersionEntry);
    // 主版本addr不在版本链上
    FindUnexpectedAddrInMVNodeList(hashTable, memRunCtx, addr, mvListHeadNode);
}
#endif

Status TryRemoveEntryInStashPage(IndexCtxT *idxCtx, HtStashPageHeadT *pageHead, const HashLookupParaT *para,
    HashRemoveParaT *rmPara, uint32_t *probeLen)
{
    DB_POINTER5(idxCtx, pageHead, para, rmPara, probeLen);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    bool isMultiVersion = HashIndexIsMultiVersion(ht);
    uint32_t entryIdx;
    // 根据页上探测长度，从0遍历每个entry
    for (entryIdx = 0; entryIdx < pageHead->probeLen; entryIdx++) {
        // entry为空，继续遍历
        if (!HashStashPageBitmapIsSet(pageHead, entryIdx)) {
            continue;
        }
        TupleAddr addr = HEAP_INVALID_ADDR;
        HashEntryBaseT *currentEntryBase = HashStashPageGetHashEntryBaseAndAddr(ht, pageHead, entryIdx, &addr);
        // 非多版本下，entry的addr不是目标addr或hashcode不是目标hashcode，说明这个entry不是目标entry，继续遍历
        if ((addr != para->addr && !isMultiVersion) || currentEntryBase->hashCode != para->hashCode) {
            continue;
        }
        // isMultiVersion 场景下，hashcode相同但是目标addr不同，因为目标addr可能存在版本链中，因此需要探测版本链
        // 记录主版本信息
        rmPara->entry = currentEntryBase;
        rmPara->entryIdx = entryIdx;
        if (para->isGC && isMultiVersion && addr != para->addr &&
            (!HashMVNodeIsInvalid(GetHashEntryMVNodeHeader(ht, currentEntryBase)))) {
            Status ret = HashMVPageRemove(idxCtx, para, rmPara);
            // 目标不在版本链中，继续探测
            if (ret == GMERR_NO_DATA) {
                continue;
            }
            // 出错或删除成功，都结束探测
            return ret;
        }
#ifndef NDEBUG
        StashPageRemoveCheckMVList(idxCtx, ht, addr, currentEntryBase);
#endif
        if (addr != para->addr) {
            continue;
        }
        // 找到hashcode和addr相同的entry，尝试删除并通过 probeEnd 的值判断是否需要继续探测
        HashStashPageRemoveFoundHashEntry(idxCtx, pageHead, rmPara, para);
        if (rmPara->probeEnd) {
            return GMERR_OK;
        }
    }
    // 如果在此页上删除成功了，应该在for循环内已经返回，执行到此说明页上没有目标数据
    return GMERR_NO_DATA;
}

Status HashStashPageRemove(IndexCtxT *idxCtx, const HashLookupParaT *para, HashRemoveParaT *rmPara)
{
    DB_POINTER4(idxCtx, idxCtx->idxHandle, rmPara, para);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint64_t start = DbRdtsc();
    DB_ASSERT(ht->stashPageNum <= STASH_PAGE_MAX_NUM);
    uint32_t stashPageId = ht->stashPageId;
    // 遍历所有stash page
    for (uint32_t i = 0; i < ht->stashPageNum && stashPageId != SE_INVALID_PAGE_ID; i++) {
        uint64_t stepOne = DbRdtsc();
        HtStashPageHeadT *curPageHead = NULL;
        Status ret = HashGetStashPageHeadByPageId(
            &((HashIndexCtxT *)(void *)idxCtx->idxRunCtx)->memRunCtx, stashPageId, &curPageHead);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to get stash page head when remove");
            return ret;
        }
        uint64_t stepTwo = DbRdtsc();
        uint32_t probeLen = 0;
        // 探测当前stash page
        ret = TryRemoveEntryInStashPage(idxCtx, curPageHead, para, rmPara, &probeLen);
        // 探测过程发生错误，返回
        if (ret != GMERR_NO_DATA && ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret,
                "SE-CCEH,  try probe entry in stash page %" PRIu32 ", multversion: %" PRIu8 ", hashcode:%" PRIu32
                ",addr:%" PRIu64,
                stashPageId, (uint8_t)HashIndexIsMultiVersion(ht), (uint32_t)para->hashCode, (uint64_t)para->addr);
            return ret;
        }
        // 当前stash page没有探测到数据，需要继续探测
        if (ret == GMERR_NO_DATA) {
            uint64_t stepThree = DbRdtsc();
            if ((uint64_t)DbToSeconds(stepThree - stepOne) > HASH_INDEX_TIMEOUT) {
                DB_LOG_WARN(GMERR_INTERNAL_ERROR,
                    "SE-CCEH, stash page %" PRIu32 " loop %" PRIu32 " get page: %" PRIu32 "ms, remove: %" PRIu32
                    "ms, total: %" PRIu32 ", probelen %" PRIu32 ", %" PRIu32 " indexId %" PRIu32,
                    ht->stashPageNum, i, (uint32_t)DbToMseconds(stepTwo - stepOne),
                    (uint32_t)DbToMseconds(stepThree - stepOne), (uint32_t)DbToMseconds(stepThree - start), probeLen,
                    curPageHead->probeLen, idxCtx->idxMetaCfg.indexId);
            }
            stashPageId = curPageHead->nextPageId;
            DB_ASSERT((i == ht->stashPageNum - 1) == (stashPageId == SE_INVALID_PAGE_ID));
            continue;
        }
        // 探测到数据并进行了删除，探测结束
        if (rmPara->probeEnd) {
            return GMERR_OK;
        }
    }
    return GMERR_NO_DATA;
}

void HashStashPageInsert2EmptySlot(HashTableT *ht, HtInsertProbeCtxT *probeCtx, HashInsertParaT *htInsertPara)
{
    DB_POINTER4(ht, probeCtx, probeCtx->stashPageHead, probeCtx->emptyEntry);
    HashEntryExtendT hashExtend = {
        .hashEntryVersion = htInsertPara->version, .oldAddr = htInsertPara->lastAddr, .mvNodeHeader = INVALID_MV_NODE};
    HtStashPageHeadT *stashPageHead = probeCtx->stashPageHead;
    uint32_t entryIdx = probeCtx->stashPageEntryIdx;
    HashEntryBaseT *foundEmptyEntryBase = probeCtx->emptyEntry;
    SetHashEntry(ht, foundEmptyEntryBase, htInsertPara->hashCode, htInsertPara->addr, hashExtend);
    HashStashPageBitMapSet(stashPageHead, probeCtx->emptyEntryIdx);
    uint32_t maxEntryIdx = stashPageHead->hashEntryPerStashPage;
    if (entryIdx > stashPageHead->probeLen) {
        stashPageHead->probeLen = entryIdx;
    }
    if (entryIdx >= maxEntryIdx) {
        stashPageHead->probeLen = maxEntryIdx;
    }
    (void)DbAtomicAdd(&ht->entryUsed, 1u);
    stashPageHead->hashEntryUsedNum++;
}

static Status HashStashPageCheckUniqueAndInsertEmptySlot(IndexCtxT *idxCtx, HashInsertParaT *htInsertPara,
    HtInsertProcResT *res, HtStashPageHeadT *stashPageHead, HtInsertProbeCtxT *probeCtx)
{
    DB_POINTER5(idxCtx, htInsertPara, res, stashPageHead, probeCtx);
    Status ret = GMERR_OK;
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    bool foundStashPageSlot = false;
    HashEntryBaseT *foundEmptyEntryBase = NULL;
    uint32_t entryIdx = 0;
    uint32_t probeLen = (stashPageHead->probeLen < stashPageHead->hashEntryPerStashPage) ?
                            (stashPageHead->probeLen + 1) :
                            stashPageHead->hashEntryPerStashPage;
    for (; entryIdx < probeLen; entryIdx++) {
        TupleAddr addr = HEAP_INVALID_ADDR;
        HashEntryBaseT *currentEntryBase = HashStashPageGetHashEntryBaseAndAddr(ht, stashPageHead, entryIdx, &addr);
        bool currentEmpty = (addr == HEAP_INVALID_ADDR);
        if (!foundStashPageSlot && currentEmpty) {
            foundStashPageSlot = true;
            foundEmptyEntryBase = currentEntryBase;
            probeCtx->emptyEntryIdx =
                (probeCtx->emptyEntryIdx != DB_INVALID_UINT32 ? probeCtx->emptyEntryIdx : entryIdx);
        }
        if (!currentEmpty && currentEntryBase->hashCode == htInsertPara->hashCode) {
            res->needExpand = false;
            ret = HashSegmentInsertCmp(idxCtx, htInsertPara, currentEntryBase, addr, res);
            if (SECUREC_UNLIKELY(res->needExpand)) {
                continue;
            }
            probeCtx->probeEnd = true;
            return ret;
        }
    }
    if (!probeCtx->foundSegEmptySlot && !probeCtx->foundStashSlot && foundStashPageSlot) {
        DB_ASSERT(probeCtx->emptyEntry == NULL);
        probeCtx->emptyEntry = foundEmptyEntryBase;
        probeCtx->foundStashSlot = true;
        probeCtx->stashPageHead = stashPageHead;
        probeCtx->stashPageEntryIdx = entryIdx;
    }
    return GMERR_OK;
}

static Status HashStashPageInsertEmptySlot(IndexCtxT *idxCtx, HtInsertProcResT *res, HtInsertProbeCtxT *probeCtx)
{
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    HtStashPageHeadT *stashPageHead = NULL;
    if (ht->stashPageNum >= STASH_PAGE_MAX_NUM) {
        res->needExpand = false;  // stashPage超限，不扩容
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "SE-Hash, stash page num %" PRId32 " exceeded the limit", ht->stashPageNum);
        return GMERR_OUT_OF_MEMORY;
    }
    // 当前没找到空槽位，无stash page，需要创建一个
    Status ret = HashStashPageCreate(idxCtx, &stashPageHead);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "SE-Hash, unable to create stash page");
        return ret;
    }
    DB_POINTER(stashPageHead);
    HashEntryBaseT *targetEntryBase = (HashEntryBaseT *)(void *)HashStashPageGetStartEntry(stashPageHead);
    DB_POINTER(targetEntryBase);
    ht->stashPageNum++;
    probeCtx->emptyEntry = targetEntryBase;
    probeCtx->emptyEntryIdx = 0;
    probeCtx->foundSegEmptySlot = false;
    probeCtx->foundStashSlot = true;
    probeCtx->stashPageEntryIdx = 1;
    probeCtx->stashPageHead = stashPageHead;
    DB_LOG_INFO("SE-Hash, stash page create, id %" PRIu32 ", total num %" PRIu32, ht->stashPageId, ht->stashPageNum);
    return GMERR_OK;
}

Status HashStashPageInsert(IndexCtxT *idxCtx, HashInsertParaT *para, HtInsertProcResT *res, HtInsertProbeCtxT *probeCtx)
{
    DB_POINTER5(idxCtx, idxCtx->idxHandle, para, res, probeCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    HtStashPageHeadT *stashPageHead = NULL;
    Status ret = GMERR_OK;
    DB_ASSERT(ht->stashPageNum <= STASH_PAGE_MAX_NUM);
    uint32_t stashPageId = ht->stashPageId;
    uint64_t start = DbRdtsc();
    for (uint32_t i = 0; i < ht->stashPageNum && stashPageId != SE_INVALID_PAGE_ID; i++) {
        uint64_t stepOne = DbRdtsc();
        ret =
            HashGetStashPageHeadByPageId(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, stashPageId, &stashPageHead);
        if (ret != GMERR_OK) {
            return ret;
        }
        uint64_t stepTwo = DbRdtsc();
        DB_POINTER(stashPageHead);
        ret = HashStashPageCheckUniqueAndInsertEmptySlot(idxCtx, para, res, stashPageHead, probeCtx);
        uint64_t stepThree = DbRdtsc();
        if ((uint64_t)DbToSeconds(stepThree - stepOne) > HASH_INDEX_TIMEOUT) {
            DB_LOG_WARN(GMERR_INTERNAL_ERROR,
                "SE-CCEH, stash page %" PRIu32 ", loop %" PRIu32 " get page: %" PRIu32 "ms, insert: %" PRIu32
                "ms, total: %" PRIu32 ".",
                ht->stashPageNum, i, (uint32_t)DbToMseconds(stepTwo - stepOne),
                (uint32_t)DbToMseconds(stepThree - stepOne), (uint32_t)DbToMseconds(stepThree - start));
        }
        if (ret != GMERR_OK || probeCtx->probeEnd) {
            return ret;
        }
        stashPageId = stashPageHead->nextPageId;
        DB_ASSERT((i == ht->stashPageNum - 1) == (stashPageId == SE_INVALID_PAGE_ID));
    }
    return GMERR_OK;
}

Status HashStashPageDrop(HashTableT *ht, HashMemRunCtxT *memRunCtx)
{
    DB_POINTER2(ht, memRunCtx);
    Status ret = GMERR_OK;
    uint32_t pageId = ht->stashPageId;
    while (pageId != SE_INVALID_PAGE_ID) {
        DbMemAddrT stashPage = DB_INVALID_MEM_ADDR;
        ret = HtGetDataPage(memRunCtx, SE_INVALID_BLOCK_ID, pageId, &stashPage);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "SE-Hash, Unable to get stash page when drop, page id = %" PRId32 ".", pageId);
            break;
        }
        HtStashPageHeadT *stashPageHead = HashGetStashPageHeadByAddr(stashPage.virtAddr);
        for (uint32_t entryIdx = 0; entryIdx < stashPageHead->probeLen; entryIdx++) {
            TupleAddr addr = HEAP_INVALID_ADDR;
            HashEntryBaseT *currentEntryBase = HashStashPageGetHashEntryBaseAndAddr(ht, stashPageHead, entryIdx, &addr);
            if (addr != HEAP_INVALID_ADDR) {
                SetHashEntryTpAddr(ht, currentEntryBase, HEAP_INVALID_ADDR);
                SetHashEntryExtend(
                    ht, currentEntryBase, (HashEntryExtendT){DB_INVALID_TRX_ID, HEAP_INVALID_ADDR, INVALID_MV_NODE});
            }
            *currentEntryBase = (HashEntryBaseT){0};
        }
        uint32_t nextPageId = stashPageHead->nextPageId;
        ret = HtFreeMemPage(memRunCtx, pageId, stashPageHead->blockId, NULL);
        pageId = nextPageId;
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "SE-Hash,Unable to free hash stash page when drop, page id = %" PRId32 ".", pageId);
            break;
        }
    }
    ht->stashPageId = SE_INVALID_PAGE_ID;
    return ret;
}

void IdxStashPageRlock(const IndexCtxT *idxCtx, LatchAddrTypeE latchAddrType)
{
    DB_POINTER2(idxCtx, idxCtx->idxHandle);
    LATCH_GET_START_WAITTIMES(RLATCH, idxCtx->idxHandle->stashPageLatch);
    DbSessionCtxT *sessionCtx = &idxCtx->idxOpenCfg.seRunCtx->resSessionCtx;
    if (sessionCtx->isDirectRead) {
        ShmemPtrT latchAddr = idxCtx->idxShmAddr;
        GET_MEMBER_SHMPTR(latchAddr, offsetof(IdxBaseT, stashPageLatch));
        DbRWSpinRLockWithSession(sessionCtx, &idxCtx->idxHandle->stashPageLatch, &latchAddr, latchAddrType);
    } else {
        DbRWLatchR(&idxCtx->idxHandle->stashPageLatch);
    }
    LATCH_GET_END_WAITTIMES(RLATCH, idxCtx->idxHandle->stashPageLatch, sessionCtx->session);
}

void IdxStashPageRUnlock(const IndexCtxT *idxCtx)
{
    DB_POINTER2(idxCtx, idxCtx->idxHandle);
    LATCH_GET_START_WAITTIMES(RLATCH, idxCtx->idxHandle->stashPageLatch);
    DbSessionCtxT *sessionCtx = &idxCtx->idxOpenCfg.seRunCtx->resSessionCtx;
    if (sessionCtx->isDirectRead) {
        DbRWSpinRUnlockWithSession(sessionCtx, &idxCtx->idxHandle->stashPageLatch);
    } else {
        DbRWUnlatchR(&idxCtx->idxHandle->stashPageLatch);
    }
    LATCH_GET_END_WAITTIMES(RLATCH, idxCtx->idxHandle->stashPageLatch, sessionCtx->session);
}

Status HashStashPageCheckUniqueAndFindSlot(
    IndexCtxT *idxCtx, HashInsertParaT *para, HtInsertProcResT *res, HtInsertProbeCtxT *probeCtx)
{
    DB_POINTER5(idxCtx, para, res, probeCtx, probeCtx->lastEntry);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    DbRWLatchW(&ht->idxBase.stashPageLatch);
    Status ret = HashStashPageInsert(idxCtx, para, res, probeCtx);
    if (ret != GMERR_OK) {
        DbRWUnlatchW(&ht->idxBase.stashPageLatch);
        DB_LOG_ERROR(ret,
            "SE-Hash, stash page insert unsucc, last entry flag %" PRIu32 ", same num %" PRIu32 ", probe %" PRIu32 "",
            (uint32_t)probeCtx->lastEntry->probeLen, probeCtx->similarHashCodeCnt, probeCtx->probeLen);
        return ret;
    }
    // 找到自己删除的，插入多版本链上
    if (res->oldEntry != NULL) {
        ret = HashMVPageInsert(idxCtx, para, res->oldEntry);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "MVPage unable to insert mv entry to stash page");
        }
        res->needExpand = false;
        probeCtx->probeEnd = true;
        DbRWUnlatchW(&ht->idxBase.stashPageLatch);
        return ret;
    }
    if (probeCtx->probeEnd) {
        DbRWUnlatchW(&ht->idxBase.stashPageLatch);
        return GMERR_OK;
    }
    // 原target bucket中hashCode无法散列，则直接插入stash-page，否则都应该rehash
    if (probeCtx->similarHashCodeCnt == probeCtx->maxProbeLen) {
        if (!probeCtx->foundStashSlot) {
            // hashCode相同，必定在seg页上没有空槽位
            DB_ASSERT(!probeCtx->foundSegEmptySlot && probeCtx->emptyEntry == NULL);
            ret = HashStashPageInsertEmptySlot(idxCtx, res, probeCtx);
        }
        if (ret == GMERR_OK && probeCtx->foundStashSlot) {
            DB_ASSERT(probeCtx->emptyEntry != NULL);
            DB_ASSERT(probeCtx->stashPageHead != NULL);
            HashInsertEmptySlotSetOldRowId(para, res);
            HashStashPageInsert2EmptySlot(ht, probeCtx, para);
            res->needExpand = false;
            HashEntryBaseSetStashPageFlag(probeCtx->lastEntry);
        }
    }
    DbRWUnlatchW(&ht->idxBase.stashPageLatch);
    return ret;
}

static void HashLookupAndRecordCount(HtStashPageHeadT *stashPageHead, HashTableT *ht, const HtHashCodeT *hashCodeArray,
    uint32_t *stashPageCount, uint32_t minLen)
{
    uint32_t entryIdx;
    for (entryIdx = 0; entryIdx < stashPageHead->probeLen; entryIdx++) {
        TupleAddr addr = HEAP_INVALID_ADDR;
        HashEntryBaseT *currentEntryBase = HashStashPageGetHashEntryBaseAndAddr(ht, stashPageHead, entryIdx, &addr);
        if (addr == HEAP_INVALID_ADDR) {
            continue;
        }
        for (uint32_t i = 0; i < minLen; i++) {
            if (hashCodeArray[i] == currentEntryBase->hashCode) {
                stashPageCount[i]++;
            }
        }
    }
}

static void HashLogAbnormalStashPageStatistics(IndexCtxT *idxCtx, HtHashCodeT *hashCodeArray, uint32_t arrLen)
{
    uint32_t stashPageCount[HASH_STATISTICS_HASH_CODE_NUM] = {0};
    uint32_t minLen = arrLen < HASH_STATISTICS_HASH_CODE_NUM ? arrLen : HASH_STATISTICS_HASH_CODE_NUM;
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t stashPageId = ht->stashPageId;
    while (stashPageId != SE_INVALID_PAGE_ID) {
        HtStashPageHeadT *stashPageHead = NULL;
        Status ret =
            HashGetStashPageHeadByPageId(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, stashPageId, &stashPageHead);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to get stash page head when record unsuc statistics.");
            return;
        }
        DB_POINTER(stashPageHead);
        HashLookupAndRecordCount(stashPageHead, ht, hashCodeArray, stashPageCount, minLen);
        stashPageId = stashPageHead->nextPageId;
    }

    char *logStr = (char *)DB_MALLOC(MAX_TOTAL_LOG_TEXT_LEN);
    if (logStr == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Db malloc str arr unsuc");
        return;
    }
    errno_t err =
        sprintf_s(logStr, MAX_TOTAL_LOG_TEXT_LEN, "SE-Hash, stash page expend unsucc, stash page already insert:");
    if (err < 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "write log str unsuc");
        DB_FREE(logStr);
        return;
    }
    for (uint32_t i = 0; i < minLen; i++) {
        if (hashCodeArray[i] == 0) {
            continue;
        }
        err = sprintf_s(logStr + strlen(logStr), MAX_TOTAL_LOG_TEXT_LEN - strlen(logStr),
            ", hashcode:%" PRIu32 ", count:%" PRIu32 "", hashCodeArray[i], stashPageCount[i]);
        if (err < 0) {
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "sprintf_s record hash code in stash page unsuc");
            DB_FREE(logStr);
            return;
        }
    }
    DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "%s", logStr);
    DB_FREE(logStr);
}

void HashStashPageAbnormalStatistics(
    IndexCtxT *idxCtx, HashInsertParaT *para, uint8_t *pageBody, const HtInsertProbeCtxT *probeCtx)
{
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    if (ht->stashPageNum < STASH_PAGE_MAX_NUM) {
        return;
    }
    HtHashCodeT statisticHashCode[HASH_STATISTICS_HASH_CODE_NUM] = {0};
    uint32_t indexPageCount[HASH_STATISTICS_HASH_CODE_NUM] = {0};
    uint32_t minProLen =
        probeCtx->maxProbeLen < HASH_STATISTICS_HASH_CODE_NUM ? probeCtx->maxProbeLen : HASH_STATISTICS_HASH_CODE_NUM;
    uint32_t probeLen = 0;
    // check key uniqueness && find empty slot
    for (; probeLen < minProLen; ++probeLen) {
        uint32_t slot = HashGetSlot(para->targetSlot, probeLen);
        TupleAddr addr = HEAP_INVALID_ADDR;
        HashEntryBaseT *currentEntryBase = GetHashEntryBaseAndAddr(ht, pageBody, slot, &addr);
        DB_ASSERT(addr == HEAP_INVALID_ADDR ? currentEntryBase->hashCode == 0 : true);
        bool alreadyRecord = false;
        // 一定为非空槽位
        for (uint32_t i = 0; i < minProLen; i++) {
            // 若之前已记录过该hashcode则自增
            if (statisticHashCode[i] == currentEntryBase->hashCode) {
                indexPageCount[i]++;
                alreadyRecord = true;
                break;
            }
        }
        if (!alreadyRecord) {
            statisticHashCode[probeLen] = currentEntryBase->hashCode;
            indexPageCount[probeLen]++;
        }
    }
    char logStr[MAX_TOTAL_LOG_TEXT_LEN] = "SE-Hash, stash page expend unsucc, index page already insert:";
    for (uint32_t i = 0; i < minProLen; i++) {
        if (statisticHashCode[i] == 0) {
            continue;
        }
        char logStr2[MAX_SINGLE_LOG_TEXT_LEN];
        errno_t err = sprintf_s(logStr2, MAX_SINGLE_LOG_TEXT_LEN, ", hashcode:%" PRIu32 ", count:%" PRIu32 "",
            statisticHashCode[i], indexPageCount[i]);
        if (err < 0) {
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "sprintf_s record hash code in index page unsuc");
            return;
        }
        err = strcat_s(logStr, MAX_TOTAL_LOG_TEXT_LEN, logStr2);
        if (err < 0) {
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "strcat_s record hash code in index page unsuc");
            return;
        }
    }

    DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "%s", logStr);
    HashLogAbnormalStashPageStatistics(idxCtx, statisticHashCode, minProLen);
}

#ifdef __cplusplus
}
#endif
