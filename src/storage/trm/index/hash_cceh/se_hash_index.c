/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: se_hash_index.c
 * Description: Implementation of Hash Index
 * Author: tanglu
 * Create: 2020/8/10
 */

#include "se_hash_index.h"
#include "se_hash_index_stash_page.h"
#include "se_hash_mem.h"
#include "se_index.h"
#include "db_utils.h"
#include "dm_data_index.h"
#include "db_memcpy.h"
#include "se_hash_index_version_page.h"
#include "se_hash_common.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct ExpandHashPara {
    HtHashCodeT hashCode;
    uint32_t segId;
} ExpandHashParaT;

typedef struct SegmentPage {
    HashDirSegmentT *segment;
    PageHeadT *page;
    uint32_t pattern;
} SegmentPageT;

void HashCalcPagePtrArrCapacity(const SeInstanceT *seInstance, uint32_t *arrCapacity)
{
    DB_POINTER2(seInstance, arrCapacity);
    // 1. get config options from file
    int32_t maxTotalShmSizeInMB = DbCfgGetInt32Lite(DB_CFG_SERVER_TOTAL_SHM_SIZE, NULL);

    uint32_t maxTotalShmSizeInKB = (uint32_t)maxTotalShmSizeInMB * (uint32_t)DB_KIBI;
    uint32_t pageSizeInKB = seInstance->seConfig.pageSize;
    uint32_t seMaxMemInKB = seInstance->seConfig.maxSeMem;
    DB_ASSERT(seMaxMemInKB < maxTotalShmSizeInKB);

    // 2. calc array capacity
    uint32_t upperbound = seMaxMemInKB;
    DB_ASSERT(pageSizeInKB != 0);
    uint32_t dbMaxSegPageNum = (upperbound + pageSizeInKB - 1) / pageSizeInKB;
    uint32_t pageContentSize = (uint32_t)(pageSizeInKB * (uint32_t)DB_KIBI - (uint32_t)HASH_PAGE_HEAD_SIZE);
    uint32_t hashSegNumPerPage = (uint32_t)(pageContentSize / sizeof(HashDirSegmentT));
    DB_ASSERT(hashSegNumPerPage != 0);
    *arrCapacity = ((dbMaxSegPageNum + hashSegNumPerPage - 1) * DOUBLE_SIZE / hashSegNumPerPage);
}

uint8_t *HashGetPageBySeg(const IndexCtxT *idxCtx, const HashDirSegmentT *seg, PageIdT *pageAddr)
{
    DB_POINTER4(idxCtx, idxCtx->idxRunCtx, seg, pageAddr);
    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;
    Status ret = HtGetMemPage(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, seg->pageAddr.blockId,
        seg->pageAddr.pageId, false, &pageInfo);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "SE-Hash,hash table get mem page unsucc,|%" PRIu32 ",%" PRIu32 "|", seg->pageAddr.blockId,
            seg->pageAddr.pageId);
        return NULL;
    }
    *pageAddr = pageInfo.pageAddr;
    return pageInfo.virtAddr;
}

/*
 * description: 根据segId获取dir在dir页内的addr
 * param {const IndexCtxT} *idxCtx [in] idx上下文
 * param {uint32_t} segId [in] dir所在segment的id
 * param {uint8_t} **dirSegPage [out] dir页的虚拟addr
 * param {PageIdT} *segPageAddr [out] dir页的页id，可以为NULL。该出参当前仅缩容场景传入了非NULL，其余场景均使用了NULL
 * return {HashDirSegmentT *} dir的页内addr或NULL
 */
HashDirSegmentT *HashGetHashDirSeg(const IndexCtxT *idxCtx, uint32_t segId, uint8_t **dirSegPage, PageIdT *segPageAddr)
{
    DB_POINTER3(idxCtx, idxCtx->idxHandle, dirSegPage);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    DB_ASSERT(ccehMeta->hashSegNumPerPage != 0u);
    uint32_t dirPageId = segId / ccehMeta->hashSegNumPerPage;
    uint32_t dirSlotId = segId % ccehMeta->hashSegNumPerPage;
    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;
    Status ret =
        HtGetMemPage(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, dirPageId, SE_INVALID_PAGE_ID, true, &pageInfo);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return NULL;
    }
    *dirSegPage = pageInfo.virtAddr;
    if (segPageAddr != NULL) {
        *segPageAddr = pageInfo.pageAddr;
    }
    return (HashDirSegmentT *)(void *)(*dirSegPage + dirSlotId * sizeof(HashDirSegmentT));
}

void HashPageLock(const IndexCtxT *idxCtx, uint8_t *page, bool isWrite, const PageIdT *pageAddr)
{
    DB_POINTER2(idxCtx, page);
    SeRunCtxT *seRunCtx = idxCtx->idxOpenCfg.seRunCtx;
    DB_POINTER(seRunCtx);
    DbSessionCtxT *ctx = &seRunCtx->resSessionCtx;
    PageHeadT *baseHead = HtGetPageHead(page);
    LATCH_GET_START_WAITTIMES(isWrite, baseHead->lock);
    if (ctx->isDirectRead || ctx->isDirectWrite) {
        DB_POINTER(pageAddr);
#ifndef NDEBUG
        // bufferpool客户端直连读不能获取memdata创建出来的数据页
        PageMgrT *pageMgr = SeBufPoolDeployInDynMem(seRunCtx->instanceId) ? seRunCtx->pageMgr : seRunCtx->mdMgr;
        PageHeadT *pageHead;
        StatusInter ret = SeGetPage(pageMgr, *pageAddr, (uint8_t **)&pageHead, 0, false);
        DB_ASSERT(pageHead == baseHead);
        DB_ASSERT(ret == STATUS_OK_INTER);
#endif
        if (SECUREC_UNLIKELY(isWrite)) {
            DbRWSpinWLockWithSession(ctx, &baseHead->lock, (const ShmemPtrT *)pageAddr, LATCH_ADDR_PAGEID);
        } else {
            DbRWSpinRLockWithSession(ctx, &baseHead->lock, (const ShmemPtrT *)pageAddr, LATCH_ADDR_PAGEID);
        }
    } else {
        if (isWrite) {
            DbRWLatchW(&baseHead->lock);
        } else {
            DbRWLatchR(&baseHead->lock);
        }
    }
    LATCH_GET_END_WAITTIMES((bool)!isWrite, baseHead->lock, ctx->session);
}

void HashPageUnLock(const IndexCtxT *idxCtx, uint8_t *page, bool isWrite)
{
    DB_POINTER2(idxCtx, page);
    SeRunCtxT *seRunCtx = idxCtx->idxOpenCfg.seRunCtx;
    DB_POINTER(seRunCtx);
    DbSessionCtxT *ctx = &seRunCtx->resSessionCtx;
    PageHeadT *baseHead = HtGetPageHead(page);
    if (ctx->isDirectRead || ctx->isDirectWrite) {
        if (isWrite) {
            DbRWSpinWUnlockWithSession(ctx, &baseHead->lock);
        } else {
            DbRWSpinRUnlockWithSession(ctx, &baseHead->lock);
        }
    } else {
        if (isWrite) {
            DbRWUnlatchW(&baseHead->lock);
        } else {
            DbRWUnlatchR(&baseHead->lock);
        }
    }
}

// entry insertion for segment expanding and merging
Status HashInsertIntoSeg(
    const IndexCtxT *idxCtx, PageHeadT *segmentPage, const HashEntryT *entry, uint32_t index, uint32_t *entryIdx)
{
    DB_POINTER5(idxCtx, idxCtx->idxHandle, segmentPage, entry, entryIdx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    DB_ASSERT(ccehMeta->hashEntryNumPerPage != 0u);
    uint32_t targetSlot = index % ccehMeta->hashEntryNumPerPage;
    uint8_t *pageBody = (uint8_t *)(segmentPage + 1);
    uint32_t entryNum = ccehMeta->hashMaxProbeLen;
    for (uint32_t probeLen = 0; probeLen < entryNum; ++probeLen) {
        uint32_t slot = HashGetSlot(targetSlot, probeLen);
        TupleAddr addr = HEAP_INVALID_ADDR;
        HashEntryBaseT *currentEntryBase = GetHashEntryBaseAndAddr(ht, pageBody, slot, &addr);
        if (addr == HEAP_INVALID_ADDR) {
            currentEntryBase->hashCode = entry->hashEntryBase.hashCode;
            currentEntryBase->isDeleted = entry->hashEntryBase.isDeleted;
            HashEntrySetProbeLen(ht, pageBody, targetSlot, probeLen);
            SetHashEntryTpAddr(ht, currentEntryBase, entry->addr);
            SetHashEntryExtend(ht, currentEntryBase, entry->hashExtend);
            *entryIdx = slot;
            segmentPage->entryUsedNum++;
            return GMERR_OK;  // gradually update segment entries
        }
    }
    return GMERR_INTERNAL_ERROR;
}

static void HtSegmentMergeRevert(
    const HashTableT *ht, PageHeadT *segmentPage, const uint32_t *modifiedEntryId, uint32_t modifiedCount)
{
    DB_POINTER3(ht, segmentPage, modifiedEntryId);
    uint8_t *entryStart = (uint8_t *)(segmentPage + 1);
    // clean the modified entries
    HashEntryT invalidHashEntry = {.hashEntryBase = {0},
        .addr = HEAP_INVALID_ADDR,
        .hashExtend = {
            .hashEntryVersion = DB_INVALID_TRX_ID, .oldAddr = HEAP_INVALID_ADDR, .mvNodeHeader = INVALID_MV_NODE}};
    for (uint32_t idx = 0; idx < modifiedCount; ++idx) {
        // probeLen为特殊标记位，记录当前bucket实际探测长度或是否有数据落在stashPage上，不能设置为0
        HashEntryBaseT *hashEntryBase = GetHashEntryBase(ht, entryStart, modifiedEntryId[idx]);
        invalidHashEntry.hashEntryBase.probeLen = hashEntryBase->probeLen;
        SetHashEntryBySlot(ht, entryStart, modifiedEntryId[idx], invalidHashEntry);
    }
    segmentPage->entryUsedNum -= modifiedCount;
}

static Status HtSegmentMergeUpdateSegments(
    IndexCtxT *idxCtx, const SegmentPageT *segmentA, const SegmentPageT *segmentB)
{
    DB_POINTER3(idxCtx, segmentA, segmentB);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    HashDirSegmentT segA = *(segmentA->segment);
    HashDirSegmentT segB = *(segmentB->segment);
    uint32_t oldSegDepth = segA.segDepth;
    DB_ASSERT(oldSegDepth >= 1u);
    uint32_t newSegDepth = oldSegDepth - 1u;
    uint32_t segmentUpdateNum = (uint32_t)((HtHashCodeT)0x01 << (ht->memMgr.ccehMeta.dir.dirDepth - newSegDepth));
    uint32_t patternMask = (uint32_t)(((HtHashCodeT)0x01 << newSegDepth) - 1);
    uint32_t newPattern = segmentA->pattern & patternMask;
    // all the segments pointing to page A and page B will have same value
    HashDirSegmentT newSegment = {.segDepth = newSegDepth, .pattern = newPattern, .pageAddr = segA.pageAddr};
    for (uint32_t idx = 0; idx < segmentUpdateNum; idx++) {
        uint32_t pattern = newPattern | (idx << newSegDepth);
        uint8_t *segmentPageBody = NULL;
        PageIdT segPageAddr;
        HashDirSegmentT *segment = HashGetHashDirSeg(idxCtx, pattern, &segmentPageBody, &segPageAddr);
        if (SECUREC_UNLIKELY(segment == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to get hash dir segment");
            return GMERR_OUT_OF_MEMORY;
        }
        // lock the page that the segment is in
        HashPageLockTL(idxCtx, segmentPageBody, true, &segPageAddr);
        DB_ASSERT(segment->segDepth == oldSegDepth);
        if (segment->pattern != segA.pattern) {
            DB_ASSERT(segment->pattern == segB.pattern);
            DB_ASSERT(segment->pageAddr.blockId == segB.pageAddr.blockId);
            DB_ASSERT(segment->pageAddr.pageId == segB.pageAddr.pageId);
        } else {
            DB_ASSERT(segment->pageAddr.blockId == segA.pageAddr.blockId);
            DB_ASSERT(segment->pageAddr.pageId == segA.pageAddr.pageId);
        }
        *segment = newSegment;
        HashPageUnLockTL(idxCtx, segmentPageBody, true);
    }

    return GMERR_OK;
}

static void HashSegmentMergeFreePage(const IndexCtxT *idxCtx, const HashDirSegmentT *segment)
{
    DB_POINTER2(idxCtx, segment);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t blockId = segment->pageAddr.blockId;
    Status ret =
        HtFreeMemPage(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, segment->pageAddr.pageId, blockId, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "SE-Hash,Unable to free hash merge page|%" PRIu32 ".%" PRIu32 ".%" PRIu32 "|",
            ht->memMgr.base.fileId, segment->pageAddr.pageId, blockId);
    }
    ret = DbShmArrayRemoveItem(&ht->memMgr.base.hashSwizzleArray, blockId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "SE-Hash,Unable to remove item in hashSwizzleArray for itemid|%" PRIu32 ".|", blockId);
    }
    ht->memMgr.ccehMeta.segPageCount--;
}

static Status HashSegmentCopy(
    IndexCtxT *idxCtx, SegmentPageT *segmentA, SegmentPageT *segmentB, uint32_t *modifiedEntryId)
{
    DB_POINTER4(idxCtx, segmentA, segmentB, modifiedEntryId);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t entryNum = ht->memMgr.ccehMeta.hashEntryNumPerPage;
    uint32_t hashEntriesPerCacheline = ht->memMgr.ccehMeta.hashEntriesPerCacheline;
    uint32_t modifiedCount = 0;
    uint8_t *pageBodyB = (uint8_t *)(segmentB->page + 1);
    Status ret = GMERR_OK;

    for (uint32_t idx = 0; idx < entryNum; ++idx) {
        HashEntryT hashEntry = GetHashEntry(ht, pageBodyB, idx);
        if (HashIsLastCachelineEntryWithStashPageFlag(hashEntry.hashEntryBase, hashEntriesPerCacheline, idx)) {
            HashSetLastEntryStashPageFlag(ht, segmentA->page, idx);
        }
        if (hashEntry.addr == HEAP_INVALID_ADDR) {
            continue;
        }
        uint32_t entryIdx;
        ret = HashInsertIntoSeg(
            idxCtx, segmentA->page, &hashEntry, HashGetTargetSlot(idxCtx, hashEntry.hashEntryBase.hashCode), &entryIdx);
        if (ret != GMERR_OK) {
            // merge unsucc, then revert it
            HtSegmentMergeRevert(ht, segmentA->page, modifiedEntryId, modifiedCount);
            DB_SET_LASTERR(GMERR_INTERNAL_ERROR, "unable to auto scale hash-index");
            break;
        }
        modifiedEntryId[modifiedCount] = entryIdx;
        modifiedCount++;
    }
    return ret;
}

#ifndef NDEBUG
static void HashBucketCheck(const IndexCtxT *idxCtx, uint8_t *pageBody, uint32_t targetSlot)
{
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    HashEntryBaseT *hashEntryBase = GetHashEntryBase(ht, pageBody, targetSlot);
    uint32_t probeLen = hashEntryBase->probeLen;
    // probeLen后的entry数据，都不应该属于targetSlot，否则可能导致漏扫数据
    for (uint32_t i = probeLen + 1; i < ht->memMgr.ccehMeta.hashMaxProbeLen; i++) {
        uint32_t slot = HashGetSlot(targetSlot, i);
        TupleAddr addr = HEAP_INVALID_ADDR;
        HashEntryBaseT *currentEntryBase = GetHashEntryBaseAndAddr(ht, pageBody, slot, &addr);
        // addr与hashcode保持一致，要么都无效，要么都有效
        if (addr == HEAP_INVALID_ADDR) {
            DB_ASSERT(currentEntryBase->hashCode == 0);
            continue;
        }
        DB_ASSERT(currentEntryBase->hashCode != 0);
        uint32_t bucketSlot = HashGetTargetSlot(idxCtx, currentEntryBase->hashCode);
        // probelen之后entry slot必定不属于targetSlot，否则可能会导致数据丢失
        DB_ASSERT(ht->memMgr.ccehMeta.hashEntryNumPerPage != 0);
        DB_ASSERT((bucketSlot % ht->memMgr.ccehMeta.hashEntryNumPerPage) !=
                  (targetSlot % ht->memMgr.ccehMeta.hashEntryNumPerPage));
    }
}

static void HashTableCheckPageEntry(IndexCtxT *idxCtx, uint8_t *pageBody)
{
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    for (uint32_t i = 0; i < ht->memMgr.ccehMeta.hashEntryNumPerPage; i++) {
        DB_ASSERT(ccehMeta->hashEntriesPerCacheline != 0);  // 创建索引时能够保证该值不为0
        // 表明为bucket的第一个entry，校验该bucket的probeLen准确性
        if (i % ccehMeta->hashEntriesPerCacheline == 0) {
            HashBucketCheck(idxCtx, pageBody, i);
        }
    }
}
#endif

static Status HashSegmentMerge(IndexCtxT *idxCtx, SegmentPageT *segmentA, SegmentPageT *segmentB)
{
    // merge entries from segment B to segment A
    DB_POINTER3(idxCtx, segmentA, segmentB);
    Status ret = GMERR_OK;
    SeRunCtxT *seRunCtxPtr = idxCtx->idxOpenCfg.seRunCtx;
    if (SECUREC_UNLIKELY(seRunCtxPtr == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "Hash's run context been dropped");
        return GMERR_INTERNAL_ERROR;
    }
    // alloc a array to store the ID of entry in segment A, which will be inserted with entry from segment B
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t maxMergeEntryNum = ht->memMgr.ccehMeta.hashEntryNumPerPage;
    /* 就近释放 */
    uint32_t *modifiedEntryId =
        (uint32_t *)DbDynMemCtxAlloc(seRunCtxPtr->sessionMemCtx, maxMergeEntryNum * sizeof(uint32_t));
    if (SECUREC_UNLIKELY(modifiedEntryId == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to alloc hash merge entry, size:%" PRIu32,
            (uint32_t)(maxMergeEntryNum * sizeof(uint32_t)));
        return GMERR_OUT_OF_MEMORY;
    }

    if (SECUREC_UNLIKELY(segmentB->page == NULL)) {
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(seRunCtxPtr->sessionMemCtx, modifiedEntryId);
        return GMERR_OUT_OF_MEMORY;
    }
    uint8_t *pageBodyB = (uint8_t *)(segmentB->page + 1);
    // segment B will be read, no modification
    AUTO_HASH_PAGE_LOCK_TL(
        idxCtx, pageBodyB, false, NULL, (ret = HashSegmentCopy(idxCtx, segmentA, segmentB, modifiedEntryId)));
#ifndef NDEBUG
    HashTableCheckPageEntry(idxCtx, (uint8_t *)(segmentA->page + 1));
#endif
    // 局部变量，外部函数无法获得，未置NULL，无UAF风险
    DbDynMemCtxFree(seRunCtxPtr->sessionMemCtx, modifiedEntryId);
    if (ret != GMERR_OK) {
#ifndef NDEBUG
        HashTableCheckPageEntry(idxCtx, (uint8_t *)(segmentB->page + 1));
#endif
        return GMERR_OK;  // ignore merge failure
    }
    // merge successfully, then free page B and update segments
    HashSegmentMergeFreePage(idxCtx, segmentB->segment);
    return HtSegmentMergeUpdateSegments(idxCtx, segmentA, segmentB);
}

static void HtSegmentSetValue(
    SegmentPageT *segment, HashDirSegmentT *dir, PageHeadT *pageHeadSeg, uint32_t dirSegPattern)
{
    DB_POINTER(segment);
    segment->segment = dir;
    segment->page = pageHeadSeg;
    segment->pattern = dirSegPattern;
}

/*
 * description: 找到segA，并判断可否缩容
 * param {*} runCtx:hash运行上下文,dirPage:当前dir页,dirSlotId:页内slot id,mergeThreshold:使用率阈值,segmentA:出参
 * return {*} STATUS_HASH_ERROR:过程中有错,STATUS_HASH_CONTINUE:遍历下一个,GMERR_OK:找到可缩容的segA
 */
HashStatus HtSegmentFindAndCheckSegA(
    const IndexCtxT *idxCtx, uint8_t *dirPage, uint32_t dirSlotId, uint32_t mergeThreshold, SegmentPageT *segmentA)
{
    DB_POINTER2(idxCtx, dirPage);
    // 获取segmentA的dirA
    HashDirSegmentT *dirA = (HashDirSegmentT *)(void *)(dirPage + dirSlotId * sizeof(HashDirSegmentT));
    // segA缩容判断1:没有对称的segB,不可缩容
    if (dirA->segDepth == 0u) {
        return STATUS_HASH_CONTINUE;
    }
    // 获取segA的所在页后,偏移获得页头信息
    PageIdT pageAddr = SE_INVALID_PAGE_ADDR;
    uint8_t *pageBodySegA = HashGetPageBySeg(idxCtx, dirA, &pageAddr);
    if (SECUREC_UNLIKELY(pageBodySegA == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NO_DATA, "the page seg is null");
        return STATUS_HASH_ERROR;
    }
    // segA缩容判断2:使用率大于等于25%
    PageHeadT *pageHeadSegA = HtGetPageHead(pageBodySegA);
    if (pageHeadSegA->entryUsedNum >= mergeThreshold) {
        return STATUS_HASH_CONTINUE;
    }
    // segA赋值
    HtSegmentSetValue(segmentA, dirA, pageHeadSegA, dirA->pattern);
    return STATUS_HASH_OK;
}

/*
 * description: 根据segA的值,找到segB,并判断可否缩容
 * param {*} runCtx:hash运行上下文,segmentA:segA的参数,mergeThreshold:使用率阈值,segmentB:出参
 * return {*} STATUS_HASH_ERROR:过程中有错,STATUS_HASH_CONTINUE:遍历下一个,GMERR_OK:找到可缩容的segB
 */
HashStatus HtSegmentFindAndCheckSegB(
    const IndexCtxT *idxCtx, const SegmentPageT *segmentA, uint32_t mergeThreshold, SegmentPageT *segmentB)
{
    DB_POINTER2(idxCtx, segmentA);
    // 根据dirA找到segA对称的segB，判断segB是否可以缩容
    HashDirSegmentT *dirA = segmentA->segment;
    uint32_t dirSegBPattern = (uint32_t)(((HtHashCodeT)0x01 << (dirA->segDepth - 1u)) ^ (dirA->pattern));
    uint8_t *dirPageB = NULL;
    HashDirSegmentT *dirB = HashGetHashDirSeg(idxCtx, dirSegBPattern, &dirPageB, NULL);
    if (SECUREC_UNLIKELY(dirB == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NO_DATA, "the hash dir seg is null");
        return STATUS_HASH_ERROR;
    }
    // segA和segB深度不一样,不可以缩容
    if (dirA->segDepth != dirB->segDepth) {
        return STATUS_HASH_CONTINUE;
    }
    PageIdT pageAddr = SE_INVALID_PAGE_ADDR;
    uint8_t *pageBodySegB = HashGetPageBySeg(idxCtx, dirB, &pageAddr);
    if (SECUREC_UNLIKELY(pageBodySegB == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NO_DATA, "the page seg is null");
        return STATUS_HASH_ERROR;
    }
    // segB缩容判断1:使用率大于等于25%
    PageHeadT *pageHeadSegB = HtGetPageHead(pageBodySegB);
    if (pageHeadSegB->entryUsedNum >= mergeThreshold) {
        return STATUS_HASH_CONTINUE;
    }
    // segB赋值
    HtSegmentSetValue(segmentB, dirB, pageHeadSegB, dirSegBPattern);
    return STATUS_HASH_OK;
}

/*
 * description: 遍历找到第一对符合缩容条件的segmentA和segmentB
 * param {*}  runCtx：入参; segmentA,segmentB：出参
 * return {*} 找到则返回true,否则返回false
 */
bool HtSegmentFindSegAB(IndexCtxT *idxCtx, SegmentPageT *segmentA, SegmentPageT *segmentB)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *priHt = &ht->memMgr.ccehMeta;
    uint32_t mergeThreshold = priHt->hashEntryNumPerPage >> HASH_TWO_BIT_SHIFT;  // value * 1/4
    uint32_t dirItemCnt = 0;
    uint32_t dirItemMax = priHt->dir.dirCap >> 1;
    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;
    uint32_t dirItemPerPage = 0u;
    // 从上次遍历结束的地方继续遍历
    for (uint32_t pageId = priHt->dirNextPageId; pageId < priHt->dirPageCount; pageId++) {
        Status ret =
            HtGetMemPage(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, pageId, SE_INVALID_PAGE_ID, true, &pageInfo);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return false;
        }
        dirItemPerPage = (pageId == priHt->dirPageCount - 1u) ? (priHt->dir.dirCap % priHt->hashSegNumPerPage) :
                                                                priHt->hashSegNumPerPage;

        for (uint32_t dirSlotId = priHt->dirNextItemIdx; dirSlotId < dirItemPerPage; dirSlotId++) {
            // 由于dir具有对称性,且页内顺序存放,页间有序,因此如果遍历了一半dir后还没找到则说明没有可缩容的segA和segB
            if (dirItemCnt > dirItemMax) {
                return false;
            }
            priHt->dirNextItemIdx++;
            dirItemCnt++;
            // 获取segmentA并检查缩容条件
            HashStatus retHash =
                HtSegmentFindAndCheckSegA(idxCtx, pageInfo.virtAddr, dirSlotId, mergeThreshold, segmentA);
            if (retHash == STATUS_HASH_CONTINUE) {
                continue;
            } else if (SECUREC_UNLIKELY(retHash != STATUS_HASH_OK)) {
                return false;
            }
            // 获取segmentB并检查缩容条件
            retHash = HtSegmentFindAndCheckSegB(idxCtx, segmentA, mergeThreshold, segmentB);
            if (retHash == STATUS_HASH_CONTINUE) {
                continue;
            } else if (SECUREC_UNLIKELY(retHash != STATUS_HASH_OK)) {
                return false;
            }
            return true;
        }
        // 遍历完一页，重置页内idx
        priHt->dirNextItemIdx = 0u;
        // 如果遍历完所有页了，需重置页id
        priHt->dirNextPageId = ((pageId == priHt->dirPageCount - 1u) ? 0u : (priHt->dirNextPageId + 1u));
    }
    return false;
}

/*
 * description: 检查对称的dirA和dirB是否都指向了同一块segment
 * param {HashRunCtxT} *runCtx
 * return {*} 对称的dir均指向同一块segment则返回true
 */
bool HashDirIsShareSameSeg(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint8_t *dirSeg = NULL;
    uint32_t dirCnt = 0;
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    uint32_t dirMax = ccehMeta->dir.dirCap >> 1;
    Status ret;
    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;
    for (uint32_t i = 0; i < ccehMeta->dirPageCount; i++) {
        ret = HtGetMemPage(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, i, SE_INVALID_PAGE_ID, true, &pageInfo);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return false;
        }
        dirSeg = pageInfo.virtAddr;
        for (uint32_t slotId = 0; slotId < ccehMeta->hashSegNumPerPage; slotId++) {
            if (dirCnt > dirMax) {
                return true;
            }
            HashDirSegmentT *dirA = (HashDirSegmentT *)(void *)(dirSeg + slotId * sizeof(HashDirSegmentT));
            dirCnt++;
            // 如果只有一个 segment，不缩容
            if (dirA->segDepth == 0) {
                return false;
            }
            uint32_t dirCodeB = (uint32_t)(((HtHashCodeT)0x01 << (dirA->segDepth - 1u)) ^ (dirA->pattern));
            uint8_t *dirPageB = NULL;
            HashDirSegmentT *dirB = HashGetHashDirSeg(idxCtx, dirCodeB, &dirPageB, NULL);
            if (SECUREC_UNLIKELY(dirB == NULL)) {
                return false;
            }
            // dirA和dirB没有指向同一块segment
            if (dirA->pageAddr.pageId != dirB->pageAddr.pageId || dirA->pageAddr.blockId != dirB->pageAddr.blockId) {
                return false;
            }
        }
    }
    return false;
}

void HashDirPageScaleIn(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    uint32_t dirCap = ccehMeta->dir.dirCap;
    uint32_t hashEntryUsedPageCnt = ccehMeta->segPageCount;
    // 缩容条件1:hashentry占用的page个数最多只有dir数量的一半
    if (hashEntryUsedPageCnt * DOUBLE_SIZE > dirCap) {
        DB_ASSERT(ccehMeta->dir.dirCap == 0 ||
                  ccehMeta->dirPageCount == (ccehMeta->dir.dirCap - 1u) / ccehMeta->hashSegNumPerPage + 1u);
        return;
    }
    // 缩容条件2:对称的dir都指向了同一个segment
    if (!HashDirIsShareSameSeg(idxCtx)) {
        DB_ASSERT(ccehMeta->dir.dirCap == 0 ||
                  ccehMeta->dirPageCount == (ccehMeta->dir.dirCap - 1u) / ccehMeta->hashSegNumPerPage + 1u);
        return;
    }
    uint32_t dirCntPerPage = ccehMeta->hashSegNumPerPage;
    DB_ASSERT(dirCntPerPage != 0u);
    uint32_t pageNeedNum = ((dirCap >> 1) - 1u) / dirCntPerPage + 1u;
    uint32_t freePageBeginId = pageNeedNum;
    HashArrayDirScaleIn(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, freePageBeginId);
    ccehMeta->dirPageCount = freePageBeginId;
    ccehMeta->dir.dirDepth--;
    ccehMeta->dir.dirCap = dirCap >> 1;
    DB_ASSERT(ccehMeta->dirPageCount == (ccehMeta->dir.dirCap - 1u) / ccehMeta->hashSegNumPerPage + 1u);
}

Status HashIndexScaleIn(IndexCtxT *idxCtx, IndexScaleInCfgT *idxScaleCfg)
{
    DB_POINTER3(idxCtx, idxCtx->idxHandle, idxScaleCfg);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    HashWlockTL(idxCtx);
    if (ht->idxBase.validCode != HASH_INDEX_VALID_CODE) {
        HashWUnlockTL(idxCtx);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "Hash table has been dropped");
        return GMERR_INTERNAL_ERROR;
    }
    // hashentry page缩容
    SegmentPageT segmentA = {0};
    SegmentPageT segmentB = {0};
    // 从头开始扫描可缩容的页
    bool findSegAB = HtSegmentFindSegAB(idxCtx, &segmentA, &segmentB);
    Status ret = GMERR_OK;
    while (findSegAB) {
        ret = segmentA.pattern < segmentB.pattern ? HashSegmentMerge(idxCtx, &segmentA, &segmentB) :
                                                    HashSegmentMerge(idxCtx, &segmentB, &segmentA);
        ht->htVersion++;
        if (ret != GMERR_OK) {
            break;
        }
        if (IdxExceedSplitTime(idxScaleCfg)) {  // 时间片用完就直接返回
            HashWUnlockTL(idxCtx);
            DB_LOG_INFO("Exceed the split time when compress HashIndex.");
            idxScaleCfg->isOverTime = true;
            return GMERR_OK;
        }
        findSegAB = HtSegmentFindSegAB(idxCtx, &segmentA, &segmentB);
    }
    if (ret == GMERR_OK) {
        // dir page缩容
        HashDirPageScaleIn(idxCtx);
        ht->htVersion++;
        CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
        ccehMeta->scaleInCnt++;
        ccehMeta->dirNextPageId = 0;
        ccehMeta->dirNextItemIdx = 0;
    }
    HashWUnlockTL(idxCtx);
    return ret;
}

Status HashIndexCalcMemSize(
    const SeInstanceT *seInstance, uint64_t hashCap, uint64_t *size, bool isUseClusteredHash, bool isVertexUseRsm)
{
    DB_POINTER2(seInstance, size);

    // 1. hash table manager size
    uint32_t pagePtrArrCapacity;
    HashCalcPagePtrArrCapacity(seInstance, &pagePtrArrCapacity);

    uint32_t hashTableSize = (uint32_t)(pagePtrArrCapacity * sizeof(ShmemPtrT));

    // 2. hash table data size
    uint32_t pageSize = (uint32_t)(seInstance->seConfig.pageSize * DB_KIBI > HASH_PAGE_HEAD_SIZE ?
                                       seInstance->seConfig.pageSize * DB_KIBI - HASH_PAGE_HEAD_SIZE :
                                       0);
    uint32_t hashSegNumPerPage = (uint32_t)(pageSize / sizeof(HashDirSegmentT));
    // 根据类型计算hashEntry长度
    SeHpTupleAddrMode heapTupleAddrMode =
        isUseClusteredHash ? SE_HEAP_TUPLE_ADDR_64 : IdxGetTupleAddrMode(seInstance, isVertexUseRsm);
    uint32_t hashEntrySize = (uint32_t)sizeof(HtHashCodeT) + HashGetAddrSize(heapTupleAddrMode);
    uint32_t hashEntryNumPerPage = pageSize / hashEntrySize;
    DB_ASSERT(hashEntryNumPerPage != 0u);
    DB_ASSERT(hashSegNumPerPage != 0u);
    uint32_t dirDepth = DbLog2(hashCap / hashEntryNumPerPage);
    uint32_t dirCap = (1u << dirDepth);

    if ((dirCap - 1u) / hashSegNumPerPage + 1u > pagePtrArrCapacity) {
        return GMERR_INSUFFICIENT_RESOURCES;
    }

    uint32_t segPageCount = dirCap;                                  // for entry
    uint32_t dirPageCount = (dirCap - 1u) / hashSegNumPerPage + 1u;  // for dir
    uint32_t hashDataSize = (segPageCount + dirPageCount) * seInstance->seConfig.pageSize * DB_KIBI;

    // 3. hash table manager + data size
    *size = hashTableSize + hashDataSize;
    return GMERR_OK;
}

Status HashIndexGetEstimateMemSize(
    uint16_t instanceId, IndexMetaCfgT idxMetaCfg, uint64_t count, uint32_t keyLen, uint64_t *size)
{
    DB_POINTER(size);
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(instanceId);
    if (seInstance == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "SE-Hash, HCIN: hash's SE instance is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    // 40%
    uint64_t estimateCount = count * HASH_ESTIMATE_FULL_FILL / HASH_ESTIMATE_FILL_RATE;
    // get max
    uint64_t hashCap = (estimateCount > (uint64_t)idxMetaCfg.indexCap) ? estimateCount : (uint64_t)idxMetaCfg.indexCap;
    return HashIndexCalcMemSize(seInstance, hashCap, size, idxMetaCfg.isUseClusteredHash, idxMetaCfg.isVertexUseRsm);
}

uint8_t *HashGetHashDirSegPageWithLock(IndexCtxT *idxCtx, uint32_t segId, uint32_t segIdOld, bool isWrite)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    DB_ASSERT(ccehMeta->hashSegNumPerPage != 0u);
    uint32_t dirPageIdOld = segIdOld / ccehMeta->hashSegNumPerPage;
    uint32_t dirPageId = segId / ccehMeta->hashSegNumPerPage;
    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;
    Status ret;
    if (dirPageId < ccehMeta->dirPageCount) {
        ret = HtGetMemPage(
            &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, dirPageId, SE_INVALID_PAGE_ID, true, &pageInfo);
        if (ret != GMERR_OK) {
            return NULL;
        }
    } else {
        HashAllocPageParamT allocParam = {.tableSpaceIndex = ht->idxBase.indexCfg.tableSpaceIndex, .isDir = true};
        ret =
            HtAllocMemPage(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, &allocParam, &dirPageId, &pageInfo, NULL);
        if (ret != GMERR_OK) {
            return NULL;
        }
        ccehMeta->dirPageCount++;
    }
    if (dirPageIdOld != dirPageId) {
        HashPageLockTL(idxCtx, pageInfo.virtAddr, isWrite, &pageInfo.pageAddr);
    }
    return pageInfo.virtAddr;
}

static Status HashPreAllocHashDirSegPage(IndexCtxT *idxCtx, uint32_t *pageAllocCount, HashDirSegmentT *segOld)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    DB_ASSERT(ccehMeta->hashSegNumPerPage != 0u);

    uint32_t oldCapacity = ccehMeta->dir.dirCap;
    uint32_t newCapacity = 1u << (ccehMeta->dir.dirDepth + 1);
    DB_ASSERT(oldCapacity * DOUBLE_SIZE == newCapacity);
    uint32_t newDirPageCount = (newCapacity - 1u) / ccehMeta->hashSegNumPerPage + 1u;
    if (newDirPageCount > ht->pageAddrArrCapacity) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
            "unable to double dir size due to insufficient capacity, dir page count = %" PRId32 ", capacity = %" PRId32
            ".",
            newDirPageCount, ht->pageAddrArrCapacity);
        return GMERR_OUT_OF_MEMORY;
    }

    // 局部深度小于全局深度，不申请新dirPage
    if (segOld->segDepth < ccehMeta->dir.dirDepth || newDirPageCount - ccehMeta->dirPageCount == 0) {
        *pageAllocCount = 0;
        return GMERR_OK;
    }
    *pageAllocCount = newDirPageCount - ccehMeta->dirPageCount;

    uint32_t dirPageId;
    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;
    HashAllocPageParamT allocParam = {.tableSpaceIndex = ht->idxBase.indexCfg.tableSpaceIndex, .isDir = true};

    for (uint32_t i = 0; i < *pageAllocCount; i++) {
        dirPageId = ccehMeta->dirPageCount;
        Status ret =
            HtAllocMemPage(&((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, &allocParam, &dirPageId, &pageInfo, NULL);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to pre alloc hash dir seg page, allocCount = %" PRIu32, *pageAllocCount);
            HashFreeHashDirPage(ht, i);
            return ret;
        }
        ccehMeta->dirPageCount++;
    }
    return GMERR_OK;
}

void UnLockIfNeed(IndexCtxT *idxCtx, uint8_t *dirSegPage, uint32_t i, uint32_t segId, bool isWrite)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    DB_ASSERT(ht->memMgr.ccehMeta.hashSegNumPerPage != 0u);
    uint32_t dirPageIdOld = segId / ht->memMgr.ccehMeta.hashSegNumPerPage;
    uint32_t dirPageId = i / ht->memMgr.ccehMeta.hashSegNumPerPage;
    if (dirPageIdOld != dirPageId) {
        HashPageUnLockTL(idxCtx, dirSegPage, isWrite);
    }
}

inline static bool HashIndexIsUseLastBucketCache(
    const HashIndexCtxT *hashIndexCtx, uint32_t pattern, uint64_t htVersion)
{
    DB_POINTER(hashIndexCtx);
    return (hashIndexCtx->lastBucketAddr.virtAddr != NULL && hashIndexCtx->pattern == pattern &&
            hashIndexCtx->hashIndexVersion == htVersion);
}

static Status HashGetSegmentPageByHashKey(IndexCtxT *idxCtx, HtHashCodeT hashCode)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);

    uint32_t pattern = HtGetPattern(hashCode, ht->memMgr.ccehMeta.dir.dirCap);
    HashIndexCtxT *hashIndexCtx = (HashIndexCtxT *)(void *)idxCtx->idxRunCtx;
    if (HashIndexIsUseLastBucketCache(hashIndexCtx, pattern, ht->htVersion)) {
#ifndef NDEBUG
        uint8_t *dirSegPage = NULL;
        HashDirSegmentT *segment = HashGetHashDirSeg(idxCtx, pattern, &dirSegPage, NULL);
        if (segment == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_UNEXPECTED_NULL_VALUE, "Unable to get hash dir segment, pattern:%" PRIu32, pattern);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        uint32_t patternMask = ((uint32_t)1 << segment->segDepth) - 1;
        DB_ASSERT((pattern & patternMask) == segment->pattern);
        uint8_t *pageBody = HashGetPageBySeg(idxCtx, segment, &hashIndexCtx->lastBucketAddr.pageAddr);
        if (pageBody == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get bucket page address");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        DB_ASSERT(pageBody == hashIndexCtx->lastBucketAddr.virtAddr);
#endif
        // 直接使用idxCtx缓冲的bucketaddr
        return GMERR_OK;
    }
    uint8_t *dirSegPage = NULL;
    HashDirSegmentT *segment = HashGetHashDirSeg(idxCtx, pattern, &dirSegPage, NULL);
    if (SECUREC_UNLIKELY(segment == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to get hash dir segment, pattern:%" PRIu32, pattern);
        return GMERR_OUT_OF_MEMORY;
    }
    uint8_t *pageBody = HashGetPageBySeg(idxCtx, segment, &hashIndexCtx->lastBucketAddr.pageAddr);
    if (SECUREC_UNLIKELY(pageBody == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to get bucket page address");
        return GMERR_OUT_OF_MEMORY;
    }
    // 更新缓冲的bucketaddr
    hashIndexCtx->pattern = pattern;
    hashIndexCtx->hashIndexVersion = ht->htVersion;
    hashIndexCtx->dirSegPage = dirSegPage;
    hashIndexCtx->lastBucketAddr.virtAddr = pageBody;

    return GMERR_OK;
}

#ifndef NDEBUG
inline static void HashCheckReservedEntry(HashTableT *ht, uint32_t probeIdx, const HashLookupParaT *para)
{
    for (uint32_t i = probeIdx; i < ht->memMgr.ccehMeta.hashMaxProbeLen; i++) {
        uint32_t slot = HashGetSlot(para->targetSlot, i);
        TupleAddr addr = HEAP_INVALID_ADDR;
        HashEntryBaseT *currentEntryBase = GetHashEntryBaseAndAddr(ht, para->pageBody, slot, &addr);
        DB_ASSERT(
            addr == HEAP_INVALID_ADDR ? currentEntryBase->hashCode == 0 : currentEntryBase->hashCode != para->hashCode);
    }
}
#endif

static Status HashLookupHashEntryProcWithoutOldRecord(IndexCtxT *idxCtx, HashLookupParaT *param)
{
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    HashEntryBaseT *targetEntry = GetHashEntryBase(ht, param->pageBody, param->targetSlot);
    const uint32_t probeLen = HashEntryGetProbeLen(targetEntry);  // get probe length from first entry
    uint32_t probeIdx = 0;

    // 取等号不会越界，probeLen的最大取值是maxProbeLen-1
    for (; probeIdx <= probeLen; ++probeIdx) {
        uint32_t slot = HashGetSlot(param->targetSlot, probeIdx);
        HashEntryBaseT *currentEntryBase = GetHashEntryBaseAndAddr(ht, param->pageBody, slot, &param->addr);
        if (param->addr == HEAP_INVALID_ADDR || currentEntryBase->hashCode != param->hashCode) {
            continue;
        }
        HtHashLookupParaT htLookupPara = {.entry = currentEntryBase, .isMatch = false, .isSelfDel = false};
        Status ret = HashSegmentLookupCmp(idxCtx, param, &htLookupPara);
        if (ret != GMERR_OK || (htLookupPara.isMatch && !htLookupPara.isSelfDel)) {
            return ret;
        }
    }
    uint32_t maxProbeLen = ht->memMgr.ccehMeta.hashMaxProbeLen;
    HashEntryBaseT *lastEntryBase = GetHashEntryBase(ht, param->pageBody, (param->targetSlot + maxProbeLen) - 1);
    if (HashEntryBaseIsSetStashPageFlag(lastEntryBase)) {
        IdxStashPageRlock(idxCtx, LATCH_ADDR_STASH_PAGE_SHMEM);
        Status ret = HashStashPageLookupWithoutOldRecord(idxCtx, param);
        IdxStashPageRUnlock(idxCtx);
        return ret;
    }
#ifndef NDEBUG
    HashCheckReservedEntry(ht, probeIdx, param);
#endif
    return GMERR_NO_DATA;
}

static Status HashLookupHashEntryProcWithOldRecord(IndexCtxT *idxCtx, HashLookupParaT *para)
{
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    HashEntryBaseT *targetEntry = GetHashEntryBase(ht, para->pageBody, para->targetSlot);
    const uint32_t probeLen = HashEntryGetProbeLen(targetEntry);  // get probe length from first entry
    uint32_t probeIdx = 0;
    bool foundNewAddr = false;
    TupleAddr newAddr = HEAP_INVALID_ADDR;
    // 取等号不会越界，probeLen的最大取值是maxProbeLen-1
    for (; probeIdx <= probeLen; ++probeIdx) {
        uint32_t slot = HashGetSlot(para->targetSlot, probeIdx);
        HashEntryBaseT *currentEntryBase = GetHashEntryBaseAndAddr(ht, para->pageBody, slot, &para->addr);
        if (para->addr == HEAP_INVALID_ADDR || currentEntryBase->hashCode != para->hashCode) {
            continue;
        }
        HtHashLookupParaT htLookupPara = {.entry = currentEntryBase, .isMatch = false, .isSelfDel = false};
        Status ret = HashSegmentLookupCmp(idxCtx, para, &htLookupPara);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (HashCheckLookupResult(idxCtx, &htLookupPara, para, &foundNewAddr, &newAddr)) {
            return GMERR_OK;
        }
    }
    uint32_t maxProbeLen = ht->memMgr.ccehMeta.hashMaxProbeLen;
    HashEntryBaseT *lastEntryBase = GetHashEntryBase(ht, para->pageBody, (para->targetSlot + maxProbeLen) - 1);
    if (HashEntryBaseIsSetStashPageFlag(lastEntryBase)) {
        IdxStashPageRlock(idxCtx, LATCH_ADDR_STASH_PAGE_SHMEM);
        Status ret = HashStashPageLookupWithOldRecord(idxCtx, para, &foundNewAddr, &newAddr);
        IdxStashPageRUnlock(idxCtx);
        return ret;
    }
#ifndef NDEBUG
    HashCheckReservedEntry(ht, probeIdx, para);
#endif
    if (foundNewAddr) {
        para->addr = newAddr;
        return GMERR_OK;
    }
    return GMERR_NO_DATA;
}

static Status HashLookupHashEntryProc(IndexCtxT *idxCtx, HashLookupParaT *para)
{
    if (para->isLookupOldRecord) {
        idxCtx->oldRowId = HEAP_INVALID_ADDR;  // 如果要查找旧记录，先将 oldRowId 初始化为 0
        return HashLookupHashEntryProcWithOldRecord(idxCtx, para);
    } else {
        return HashLookupHashEntryProcWithoutOldRecord(idxCtx, para);
    }
}

static void HashRemoveFoundHashEntry(IndexCtxT *idxCtx, const HashLookupParaT *para, HashRemoveParaT *rmPara)
{
    rmPara->probeEnd = true;
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint64_t entryVersion = GetHashEntryVerion(ht, rmPara->entry);
    if (para->isGC && !para->isMarkDelete) {       // GC回收, OCC
        DB_ASSERT(rmPara->entry->isDeleted == 0);  // 多版本下不会标记删除
        // GC 线程回收多版本的情况下，比较保证entryVersion 是已提交的版本
        if (SeTransIsCommit(idxCtx->idxOpenCfg.seRunCtx, entryVersion)) {
            HashEraseEntry(idxCtx, ht, para, rmPara);
        } else {
            rmPara->probeEnd = false;
        }
    } else if (para->isGC && para->isMarkDelete) {  // GC回收，PCC
        if (rmPara->entry->isDeleted == 1) {
            HashEraseEntry(idxCtx, ht, para, rmPara);
        }
    } else if (para->isErase && !para->isMarkDelete) {
        // 多版本的回滚 必须保证entryVersion 和本次操作的version 一致
        uint64_t selfVersion = SeTransGetTrxId(idxCtx->idxOpenCfg.seRunCtx);
        if (entryVersion != selfVersion) {
            rmPara->probeEnd = false;
        } else {
            HashEraseEntry(idxCtx, ht, para, rmPara);
        }
    } else if (para->isErase && para->isMarkDelete) {
        if (idxCtx->idxMetaCfg.indexMultiVersionType == INDEX_PCC_RR_TYPE) {
            if (rmPara->entry->isDeleted == 0) {
                HashEraseEntry(idxCtx, ht, para, rmPara);
            }
        } else {
            // 非多版本可以直接回滚, RC模式下不会存在isDeleted=1的数据
            HashEraseEntry(idxCtx, ht, para, rmPara);
        }
    } else {
        if (para->isMarkDelete) {
            rmPara->entry->isDeleted = 1u;
        } else {
            HashRemoveHashEntrySetOldAddr(idxCtx, rmPara, ht, entryVersion);
        }
    }
}

inline static bool NeedContinueProbe(
    HashLookupParaT *para, HashEntryBaseT *currentEntryBase, bool isMultiVersion, TupleAddr addr)
{
    return addr == HEAP_INVALID_ADDR || currentEntryBase->hashCode != para->hashCode ||
           (!isMultiVersion && addr != para->addr);
}

inline static void InitHashRemovePara(HashRemoveParaT *rmPara)
{
    rmPara->entry = NULL;
    rmPara->entryIdx = DB_INVALID_UINT32;
    rmPara->needUpdateProbeLen = false;
    rmPara->probeEnd = true;
}

inline static bool NeedProbeMVPage(const HashTableT *ht, bool isMultiVersion, HashEntryBaseT *currentEntryBase,
    TupleAddr findAddr, TupleAddr expectAddr)
{
    if (!isMultiVersion || findAddr == expectAddr) {
        return false;
    }
    HashMVNodeT *mvListHeader = GetHashEntryMVNodeHeader(ht, currentEntryBase);
    return !HashMVNodeIsInvalid(mvListHeader);
}

inline static Status TryToRemoveFromStashPage(
    IndexCtxT *idxCtx, HashTableT *ht, HashLookupParaT *para, HashRemoveParaT *rmPara)
{
    DbRWLatchW(&ht->idxBase.stashPageLatch);
    Status ret = HashStashPageRemove(idxCtx, para, rmPara);
    DbRWUnlatchW(&ht->idxBase.stashPageLatch);
    return ret;
}

#ifndef NDEBUG
inline static void SegmentPageRemoveCheckMVList(
    IndexCtxT *idxCtx, HashTableT *ht, TupleAddr addr, HashEntryBaseT *majorVersionEntry)
{
    if (!HashIndexIsMultiVersion(ht)) {
        return;
    }
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)(void *)idxCtx->idxRunCtx)->memRunCtx;
    HashMVNodeT mvListHeadNode = *GetHashEntryMVNodeHeader(ht, majorVersionEntry);
    // 主版本addr不在版本链上
    FindUnexpectedAddrInMVNodeList(ht, memRunCtx, addr, mvListHeadNode);
}
#endif

#define RM_HASH_ENTRY_LOG \
    "hash delete no data, indexId %" PRIu32 ", rowId: %" PRIu64 ", hashcode %" PRIu32 ", probeLen %" PRIu32 "."

static Status HashRemoveHashEntryProc(IndexCtxT *idxCtx, HashLookupParaT *para)
{
    DB_POINTER(idxCtx);
    HashRemoveParaT rmPara;
    InitHashRemovePara(&rmPara);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    bool isMultiVersion = HashIndexIsMultiVersion(ht);
    HashEntryBaseT *targetEntry = GetHashEntryBase(ht, para->pageBody, para->targetSlot);
    const uint32_t probeLen = HashEntryGetProbeLen(targetEntry);  // get probe length from first entry
    uint32_t probeIdx = 0;
    // 取等号不会越界，probeLen的最大取值是maxProbeLen-1
    for (; probeIdx <= probeLen; ++probeIdx) {
        uint32_t slot = HashGetSlot(para->targetSlot, probeIdx);
        TupleAddr addr = HEAP_INVALID_ADDR;
        HashEntryBaseT *currentEntryBase = GetHashEntryBaseAndAddr(ht, para->pageBody, slot, &addr);
        if (NeedContinueProbe(para, currentEntryBase, isMultiVersion, addr)) {
            continue;
        }
        rmPara.entry = currentEntryBase;
        rmPara.needUpdateProbeLen = (probeIdx == probeLen);
        if (para->isGC && NeedProbeMVPage(ht, isMultiVersion, currentEntryBase, addr, para->addr)) {
            Status ret = HashMVPageRemove(idxCtx, para, &rmPara);
            // 目标不在版本链中，继续探测
            if (ret == GMERR_NO_DATA) {
                continue;
            }
            // 出错或者成功均返回
            return ret;
        }
#ifndef NDEBUG
        SegmentPageRemoveCheckMVList(idxCtx, ht, addr, currentEntryBase);
#endif
        if (addr != para->addr) {
            continue;
        }
        HashRemoveFoundHashEntry(idxCtx, para, &rmPara);
        if (rmPara.probeEnd) {
            return GMERR_OK;
        }
    }
    uint32_t maxProbeLen = ht->memMgr.ccehMeta.hashMaxProbeLen;
    HashEntryBaseT *lastEntryBase = GetHashEntryBase(ht, para->pageBody, (para->targetSlot + maxProbeLen) - 1);
    if (HashEntryBaseIsSetStashPageFlag(lastEntryBase)) {
        return TryToRemoveFromStashPage(idxCtx, ht, para, &rmPara);
    }
#ifndef NDEBUG
    HashCheckReservedEntry(ht, probeIdx, para);
#endif
    DB_LOG_INFO(RM_HASH_ENTRY_LOG, ht->idxBase.indexCfg.indexId, para->addr, para->hashCode, probeLen);
    return GMERR_NO_DATA;
}

inline static Status HashFindHashEntrySlotBySlot(IndexCtxT *idxCtx, HashLookupParaT *para)
{
    DB_POINTER(idxCtx);
    return para->isRemove ? HashRemoveHashEntryProc(idxCtx, para) : HashLookupHashEntryProc(idxCtx, para);
}

inline static Status HashFindHashEntryInner(IndexCtxT *idxCtx, HashLookupParaT *para)
{
    DB_POINTER2(idxCtx, para);
    Status ret = HashGetSegmentPageByHashKey(idxCtx, para->hashCode);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbMemAddrT *pageInfo = &((HashIndexCtxT *)idxCtx->idxRunCtx)->lastBucketAddr;
    para->pageBody = pageInfo->virtAddr;
    para->pageHead = (PageHeadT *)(void *)pageInfo->virtAddr - 1;
    AUTO_HASH_PAGE_LOCK_TL(idxCtx, pageInfo->virtAddr, para->isRemove, &pageInfo->pageAddr,
        (ret = HashFindHashEntrySlotBySlot(idxCtx, para)));
    return ret;
}

static Status HashFindHashEntry(IndexCtxT *idxCtx, HashLookupParaT *para)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    if (ht->entryUsed == 0) {
        return GMERR_NO_DATA;
    }
    Status ret;
    uint32_t lockConflictTryCnt = 0;
    uint64_t begin = DbRdtsc();
    ContinueTryLockMethodT lockMethod = {.continueTryLock = HashWLockLLBase, .continueTryUnlock = HashWUnlockLLBase};
    if (idxCtx->batchReadLock) {
        lockMethod.continueTryLock = HashRLockLLBase;
        lockMethod.continueTryUnlock = HashRUnlockLLBase;
    }
    do {
        AUTO_HASH_RLOCK_TL(idxCtx, (ret = HashFindHashEntryInner(idxCtx, para)));
    } while (IdxContinueTryBatched(idxCtx, &lockConflictTryCnt, ret, begin, lockMethod));
    return ret;
}

Status HashIndexLookup(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr *addr, bool *isFound)
{
    DB_POINTER3(idxCtx, idxCtx->idxHandle, isFound);
    *isFound = false;
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    if (SECUREC_UNLIKELY(!HashIndexHashTableIsConstructed(ht))) {
        return GMERR_OK;
    }
    DB_POINTER(idxCtx->idxOpenCfg.callbackFunc.keyCmp);
    DB_ASSERT(ht->memMgr.ccehMeta.hashEntryNumPerPage != 0u);
    HtHashCodeT hashCode = HtGetHashCode(idxKey);
    HashLookupParaT para = {
        .isUndo = false,
        .isRemove = false,
        .isGC = false,
        .isErase = false,
        .isMarkDelete = false,
        .isLookupOldRecord = idxCtx->isLookupOldRecord,
        .addr = HEAP_INVALID_ADDR,
        .hashKey = idxKey,
        .hashCode = hashCode,
        .targetSlot = HashGetTargetSlot(idxCtx, hashCode),
        .pageBody = NULL,
        .pageHead = NULL,
    };
    Status ret = HashFindHashEntry(idxCtx, &para);
    if (SECUREC_LIKELY(ret == GMERR_OK)) {
        if (addr != NULL) {
            *addr = para.addr;
        }
        *isFound = true;
    } else if (ret == GMERR_NO_DATA) {  // not find is normal
        ret = GMERR_OK;
    }
    return ret;
}

Status HashIndexBatchLookup(IndexCtxT *idxCtx, IndexKeyT idxKey[], uint32_t batchNum, IdxBatchLookupParaT *para)
{
    DB_POINTER4(idxCtx, idxKey, para, para->categorizeFunc);
    // HashIndexBatchLookup 不在此处判断isConstructed，因为 HashIndexLookup 方法中已经判断
    const IdxCategorizeFunc categorizeFunc = para->categorizeFunc;
    Status ret = GMERR_OK;
    HpTupleAddr addr;
    bool isFound = false;
    HashIndexBatchBegin(idxCtx, false);
    for (uint32_t i = 0; i < batchNum; i++) {
        ret = HashIndexLookup(idxCtx, idxKey[i], &addr, &isFound);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "SE-Hash, lookup wrong, idxIdx:%" PRIu32, idxCtx->idxMetaCfg.indexId);
            break;
        }
        ret = categorizeFunc(
            idxCtx, para->iter, TupleAddr2IdxTupleOrIter(addr), i, isFound ? IDX_IS_TUPLE_ADDR : IDX_IS_NOT_FOUND);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                ret, "SE-Hash, categorize unsucc, idxIdx:%" PRIu32, idxCtx->idxMetaCfg.indexId);
            break;
        }
    }
    HashIndexBatchEnd(idxCtx, false);
    return ret;
}

void HashInsert2EmptySlot(HashTableT *ht, uint8_t *pageBody, HashEntryBaseT *hashEntryBase,
    const HashInsertParaT *htInsertPara, uint32_t probeLen)
{
    DB_POINTER3(ht, pageBody, htInsertPara);
    // 如果是多版本，entrybase 后面还需要记录version和 oldAddr
    HashEntryExtendT hashExtend = {
        .hashEntryVersion = htInsertPara->version, .oldAddr = htInsertPara->lastAddr, .mvNodeHeader = INVALID_MV_NODE};
    SetHashEntry(ht, hashEntryBase, htInsertPara->hashCode, htInsertPara->addr, hashExtend);
    HashEntrySetProbeLen(ht, pageBody, htInsertPara->targetSlot, probeLen);
    PageHeadT *pageHead = HtGetPageHead(pageBody);
    pageHead->entryUsedNum++;
    (void)DbAtomicAdd(&ht->entryUsed, 1u);
}

inline static void HashUpdateCollisionCount(HashTableT *ht, HtInsertProcResT *res, uint32_t probeLen)
{
    DB_POINTER2(ht, res);
    if (res->isFirstCollision && probeLen != 0u) {
        (void)DbAtomicInc64(&ht->hashCollisionCnt);
        res->isFirstCollision = false;
    }
}

inline static bool HtHashCodeDisableRehash(
    const HashTableT *ht, HtHashCodeT currentHashCode, HtHashCodeT insertHashCode)
{
    uint32_t compareDepth =
        DB_MIN(ht->memMgr.ccehMeta.dir.dirDepth + HASH_ONE_INSERT_EXPAND_LIMITED, ht->pageAddrArrCapBits);
    uint32_t mask = (1u << compareDepth) - 1u;
    return (((uint32_t)currentHashCode & mask) == ((uint32_t)insertHashCode & mask));
}

inline static void HashUpdateProbeCtx(const HashTableT *ht, HtInsertProbeCtxT *probeCtx, uint8_t *pageBody,
    const HashInsertParaT *para, uint32_t probeLen)
{
    probeCtx->probeLen = probeLen;
    probeCtx->lastEntry = GetHashEntryBase(ht, pageBody, (para->targetSlot + probeCtx->maxProbeLen) - 1);
    // 有stashPage标记，需要查找stashPage确保唯一性
    // 探测到的所有hashCode都相同，需要查找stashPage中的emptySlot
    probeCtx->needProbeStashPage = (bool)(HashEntryBaseIsSetStashPageFlag(probeCtx->lastEntry) ||
                                          (probeCtx->similarHashCodeCnt == probeCtx->maxProbeLen));
}

static Status HashCheckUniqueAndFindEmptySlot(IndexCtxT *idxCtx, const HashInsertParaT *para, uint8_t *pageBody,
    HtInsertProcResT *res, HtInsertProbeCtxT *probeCtx)
{
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t probeLen = 0;
    HashEntryBaseT *targetEntry = GetHashEntryBase(ht, pageBody, para->targetSlot);
    bool isPccRcMode = HashIndexIsPccRc(ht);
    for (; probeLen < probeCtx->maxProbeLen; ++probeLen) {
        TupleAddr addr = HEAP_INVALID_ADDR;
        HashEntryBaseT *entry = GetHashEntryBaseAndAddr(ht, pageBody, HashGetSlot(para->targetSlot, probeLen), &addr);
        bool isCurrentEmpty = (addr == HEAP_INVALID_ADDR);
        if (!probeCtx->foundSegEmptySlot && isCurrentEmpty) {  // 说明这个位置是空的
            probeCtx->foundSegEmptySlot = true;
            probeCtx->emptyEntry = entry;
        }
        DB_ASSERT(addr == HEAP_INVALID_ADDR ? entry->hashCode == 0 : true);
        if (!isCurrentEmpty && HtHashCodeDisableRehash(ht, entry->hashCode, para->hashCode)) {
            probeCtx->similarHashCodeCnt++;
        }
        // check key uniqueness
        if (!isCurrentEmpty && probeLen <= targetEntry->probeLen && entry->hashCode == para->hashCode) {
            res->needExpand = false;  // 插入前 *needExpand 先置为 false
#ifdef SYS32BITS
            if (para->addr == addr && idxCtx->idxMetaCfg.indexMultiVersionType == INDEX_ONE_VERSION_TYPE) {
                // 说明当前entry是残留的，不执行插入逻辑，返回OK
                probeCtx->probeEnd = true;
                DB_LOG_WARN(GMERR_OK, "find remain entry, reuse it, addr: %" PRIu64 " indexId: %" PRIu32 ".", addr,
                    idxCtx->idxMetaCfg.indexId);
                return GMERR_OK;
            }
#endif
            // 悲观事务下RC事务empty entry后存在前置数据，则触发搬迁，保证插入顺序
            if (isPccRcMode && probeCtx->foundSegEmptySlot) {
                HashEntryExtendT hashExtend = {
                    .hashEntryVersion = para->version, .oldAddr = para->lastAddr, .mvNodeHeader = INVALID_MV_NODE};
                SetHashEntry(ht, probeCtx->emptyEntry, para->hashCode, addr, hashExtend);
                probeCtx->emptyEntry = entry;
            }
            Status ret = HashSegmentInsertCmp(idxCtx, para, entry, addr, res);
            if (SECUREC_UNLIKELY(res->needExpand)) {  // 继续探测情况下，ret必定为OK
                DB_ASSERT(ret == GMERR_OK);
                continue;
            }
            probeCtx->probeEnd = true;
            return ret;
        }
        if (probeLen >= targetEntry->probeLen && probeCtx->foundSegEmptySlot) {
            break;
        }
    }
    HashUpdateProbeCtx(ht, probeCtx, pageBody, para, probeLen);
    return GMERR_OK;
}

void InitInsertProbeCtx(const HashTableT *ht, HtInsertProbeCtxT *probeCtx)
{
    probeCtx->emptyEntry = NULL;
    probeCtx->emptyEntryIdx = DB_INVALID_UINT32;
    probeCtx->probeLen = 0;
    probeCtx->maxProbeLen = ht->memMgr.ccehMeta.hashMaxProbeLen;
    probeCtx->similarHashCodeCnt = 0;
    probeCtx->stashPageHead = NULL;
    probeCtx->stashPageEntryIdx = 0;
    probeCtx->lastEntry = NULL;
    probeCtx->foundSegEmptySlot = false;
    probeCtx->foundStashSlot = false;
    probeCtx->needProbeStashPage = false;
    probeCtx->probeEnd = false;
}

/*
 * This function will do one of following actions:
 * 1. insert a key successfully if not violate uniqueness
 * 2. return wrong if inserted key violates uniqueness constraint
 * 3. when trx rollback, clear deletion mark
 * This func shall consider maintain following variable carefully:
 * NOTE probeLen to record current slot max probe length
 * NOTE needExpand to indicate whether need to expand segment at caller
 * NOTE hashCollisionCnt to record collision cnt
 */
Status HashCheckUniqueAndInsertEmptySlot(
    IndexCtxT *idxCtx, HashInsertParaT *para, uint8_t *pageBody, HtInsertProcResT *res)
{
    DB_POINTER4(idxCtx, para, res, idxCtx->idxHandle);
    res->needExpand = true;
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    DB_ASSERT(ht->memMgr.ccehMeta.hashEntryNumPerPage != 0u);
    HtInsertProbeCtxT probeCtx;
    InitInsertProbeCtx(ht, &probeCtx);
    Status ret = HashCheckUniqueAndFindEmptySlot(idxCtx, para, pageBody, res, &probeCtx);
    if (ret != GMERR_OK || probeCtx.probeEnd) {
        return ret;
    }
    if (SECUREC_UNLIKELY(probeCtx.needProbeStashPage)) {
        ret = HashStashPageCheckUniqueAndFindSlot(idxCtx, para, res, &probeCtx);
        if (ret != GMERR_OK || probeCtx.probeEnd) {
            // 如果是插入stashPage失败则做详细的hashcode统计
            if (ret == GMERR_OUT_OF_MEMORY) {
                HashStashPageAbnormalStatistics(idxCtx, para, pageBody, &probeCtx);
            }
            return ret;
        }
    }
    // 如果是同一OCC_RR事务且发生数据冲突，则本次插入应插入version page
    if (res->oldEntry != NULL) {
        ret = HashMVPageInsert(idxCtx, para, res->oldEntry);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|MVPage| unable to insert mv entry");
        }
        res->needExpand = false;
    } else {
        if (probeCtx.foundSegEmptySlot) {  // 找到空位，并且需要插入，则插入
            HashInsertEmptySlotSetOldRowId(para, res);
            HashInsert2EmptySlot(ht, pageBody, probeCtx.emptyEntry, para, probeCtx.probeLen);
            res->needExpand = false;
        }
    }
    HashUpdateCollisionCount(ht, res, probeCtx.probeLen);
    return ret;
}

void HashUpdateDirSegMapAgain(IndexCtxT *idxCtx, UpdateParaT updatePara, HashDirSegmentT seg0, HashDirSegmentT seg1)
{
    uint32_t halfCap = updatePara.dirCapacity >> 1;
    UpdateParaT updateParaAgain = {.segDepth = updatePara.segDepth + 1u,
        .dirDepth = updatePara.dirDepth,
        .dirCapacity = halfCap,
        .segId = updatePara.segId - halfCap,
        .segIdOld = updatePara.segIdOld};
    DB_ASSERT(updatePara.dirCapacity != 0u);
    if ((updatePara.segId % updatePara.dirCapacity) >= halfCap) {
        HashUpdateDirSegMap(idxCtx, updateParaAgain, seg0, seg1);
        updateParaAgain.segId = updatePara.segId;
        HashUpdateDirSegMap(idxCtx, updateParaAgain, seg0, seg1);
    } else {
        updateParaAgain.segId = updatePara.segId;
        HashUpdateDirSegMap(idxCtx, updateParaAgain, seg0, seg1);
        updateParaAgain.segId = updatePara.segId + halfCap;
        HashUpdateDirSegMap(idxCtx, updateParaAgain, seg0, seg1);
    }
}

// 该接口内部上锁，配合UnLockIfNeed使用
Status GetSegBySegIdWithLock(
    IndexCtxT *idxCtx, uint32_t segId, uint32_t segIdOld, uint8_t **dirSegPage, HashDirSegmentT **seg)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);

    DB_ASSERT(ht->memMgr.ccehMeta.hashSegNumPerPage != 0u);

    *dirSegPage = HashGetHashDirSegPageWithLock(idxCtx, segId, segIdOld, 1);
    if (SECUREC_UNLIKELY(*dirSegPage == NULL)) {
        return GMERR_INTERNAL_ERROR;
    }

    *seg = (HashDirSegmentT *)(void *)(*dirSegPage +
                                       (segId % ht->memMgr.ccehMeta.hashSegNumPerPage) * sizeof(HashDirSegmentT));
    return GMERR_OK;
}

void HashUpdateDirSegMap(IndexCtxT *idxCtx, UpdateParaT updatePara, HashDirSegmentT seg0, HashDirSegmentT seg1)
{
    DB_POINTER(idxCtx);
    uint32_t depthDiff = updatePara.dirDepth - updatePara.segDepth;
    uint8_t *dirSegPage = NULL;
    HashDirSegmentT *seg = NULL;
    DB_ASSERT(updatePara.dirCapacity != 0u);
    Status ret = GMERR_OK;
    if (depthDiff == 0u) {
        uint32_t halfCap = updatePara.dirCapacity >> 1;
        if ((updatePara.segId % updatePara.dirCapacity) >= halfCap) {
            uint32_t pairSeg = updatePara.segId - halfCap;
            ret = GetSegBySegIdWithLock(idxCtx, pairSeg, updatePara.segIdOld, &dirSegPage, &seg);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_ERROR(ret, "unable to get seg by pair seg when update dir.");
                return;
            }
            *seg = seg0;
            UnLockIfNeed(idxCtx, dirSegPage, pairSeg, updatePara.segIdOld, 1);

            ret = GetSegBySegIdWithLock(idxCtx, updatePara.segId, updatePara.segIdOld, &dirSegPage, &seg);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_ERROR(ret, "unable to get seg by update seg when update dir.");
                return;
            }
            *seg = seg1;
            UnLockIfNeed(idxCtx, dirSegPage, updatePara.segId, updatePara.segIdOld, 1);
        } else {
            ret = GetSegBySegIdWithLock(idxCtx, updatePara.segId, updatePara.segIdOld, &dirSegPage, &seg);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_ERROR(ret, "unable to get seg by update seg when update dir.");
                return;
            }
            *seg = seg0;
            UnLockIfNeed(idxCtx, dirSegPage, updatePara.segId, updatePara.segIdOld, 1);

            uint32_t pairSeg = updatePara.segId + halfCap;
            ret = GetSegBySegIdWithLock(idxCtx, pairSeg, updatePara.segIdOld, &dirSegPage, &seg);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_ERROR(ret, "unable to get seg by pair seg when update dir.");
                return;
            }
            *seg = seg1;
            UnLockIfNeed(idxCtx, dirSegPage, pairSeg, updatePara.segIdOld, 1);
        }
    } else {
        HashUpdateDirSegMapAgain(idxCtx, updatePara, seg0, seg1);
    }
}

Status HashDirDoubleSize(HashDirectoryHeadT *oldDir, IndexCtxT *idxCtx, uint32_t segId, HashDirSegmentT expNew)
{
    DB_POINTER2(oldDir, idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    DB_ASSERT(ccehMeta->hashSegNumPerPage != 0u);

    for (uint32_t i = oldDir->dirCap; i < (oldDir->dirCap * DOUBLE_SIZE); ++i) {
        if (i == (segId + oldDir->dirCap)) {
            uint8_t *dirSegPageNew = HashGetHashDirSegPageWithLock(idxCtx, i, segId, 1);
            if (SECUREC_UNLIKELY(dirSegPageNew == NULL)) {
                DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
                    "unable to double dir size when get segid new page, seg id = %" PRId32 ".", segId);
                return GMERR_OUT_OF_MEMORY;
            }
            uint32_t segSlotId = i % ccehMeta->hashSegNumPerPage;
            HashDirSegmentT *segNew = (HashDirSegmentT *)(void *)(dirSegPageNew + segSlotId * sizeof(HashDirSegmentT));
            *segNew = expNew;
            UnLockIfNeed(idxCtx, dirSegPageNew, i, segId, 1);
        } else {
            uint8_t *dirSegPageNew = HashGetHashDirSegPageWithLock(idxCtx, i, segId, 1);
            if (SECUREC_UNLIKELY(dirSegPageNew == NULL)) {
                DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
                    "dir double size unsucc when get new dir seg page, seg id = %" PRId32 ".", segId);
                return GMERR_OUT_OF_MEMORY;
            }
            uint8_t *dirSegPageOld = HashGetHashDirSegPageWithLock(idxCtx, i - oldDir->dirCap, segId, 0);
            if (SECUREC_UNLIKELY(dirSegPageOld == NULL)) {
                DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
                    "dir double size unsucc when get old dir seg page, seg id = %" PRId32 ".", segId);
                UnLockIfNeed(idxCtx, dirSegPageNew, i, segId, 1);
                return GMERR_OUT_OF_MEMORY;
            }
            uint32_t segSlotIdNew = i % ccehMeta->hashSegNumPerPage;
            HashDirSegmentT *segNew =
                (HashDirSegmentT *)(void *)(dirSegPageNew + segSlotIdNew * sizeof(HashDirSegmentT));
            uint32_t segSlotIdOld = (i - oldDir->dirCap) % ccehMeta->hashSegNumPerPage;
            HashDirSegmentT *segOld =
                (HashDirSegmentT *)(void *)(dirSegPageOld + segSlotIdOld * sizeof(HashDirSegmentT));
            *segNew = *segOld;
            UnLockIfNeed(idxCtx, dirSegPageNew, i, segId, 1);
            UnLockIfNeed(idxCtx, dirSegPageOld, i - oldDir->dirCap, segId, 0);
        }
    }
    ht->htVersion++;
    oldDir->dirDepth++;
    uint32_t capacity = 1u << oldDir->dirDepth;
    oldDir->dirCap = capacity;
    DB_ASSERT(ccehMeta->dirPageCount ==
              (capacity - 1u) / ccehMeta->hashSegNumPerPage + 1u);  // dirPageCount已经在预分配中修改
    return GMERR_OK;
}

Status HashSegInit(IndexCtxT *idxCtx, HashDirSegmentT *seg, uint32_t segDepth, DbMemAddrT *pageInfo)
{
    // valid seg pointer
    DB_POINTER2(idxCtx, seg);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);

    seg->segDepth = segDepth;
    seg->pattern = 0u;
    HashAllocPageParamT allocParam = {.tableSpaceIndex = ht->idxBase.indexCfg.tableSpaceIndex, .isDir = false};
    uint32_t allocPageId;
    Status ret = HtAllocMemPage(
        &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx, &allocParam, &seg->pageAddr.blockId, pageInfo, &allocPageId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to alloc hash page");
        return GMERR_OUT_OF_MEMORY;
    }

    seg->pageAddr.pageId = allocPageId;

    uint8_t *hashEntry = pageInfo->virtAddr;
    HashEntryT invalidHashEntry = {.hashEntryBase = {0},
        .addr = HEAP_INVALID_ADDR,
        .hashExtend = {
            .hashEntryVersion = DB_INVALID_TRX_ID, .oldAddr = HEAP_INVALID_ADDR, .mvNodeHeader = INVALID_MV_NODE}};
    HashPageLockTL(idxCtx, hashEntry, true, &pageInfo->pageAddr);
    for (uint32_t i = 0; i < ht->memMgr.ccehMeta.hashEntryNumPerPage; ++i) {
        SetHashEntryBySlot(ht, hashEntry, i, invalidHashEntry);
    }
    HashPageUnLockTL(idxCtx, hashEntry, true);
    return GMERR_OK;
}

void HashExpandRehash(IndexCtxT *idxCtx, DbMemAddrT pageInfo, const HashDirSegmentT *oldSeg, uint8_t *oldPageBody)
{
    DB_POINTER3(idxCtx, oldSeg, oldPageBody);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    // rehashing
    PageHeadT *newPageHead = (PageHeadT *)(void *)pageInfo.virtAddr - 1;
    PageHeadT *oldPageHead = (PageHeadT *)(void *)oldPageBody - 1;
    HashEntryT invalidHashEntry = {.hashEntryBase = {0},
        .addr = HEAP_INVALID_ADDR,
        .hashExtend = {
            .hashEntryVersion = DB_INVALID_TRX_ID, .oldAddr = HEAP_INVALID_ADDR, .mvNodeHeader = INVALID_MV_NODE}};
    HtHashCodeT newPageMask = (HtHashCodeT)1 << oldSeg->segDepth;
    DbFastMemcpy(pageInfo.virtAddr, ht->pageSize, oldPageBody, ht->pageSize);
    for (uint32_t idx = 0; idx < ht->memMgr.ccehMeta.hashEntryNumPerPage; ++idx) {
        HashEntryBaseT *hashEntryBase = GetHashEntryBase(ht, oldPageBody, idx);
        TupleAddr addr =
            HeapUncompressTupleAddr((uint8_t *)((uintptr_t)hashEntryBase + sizeof(HtHashCodeT)), ht->heapTupleAddrMode);
        if (addr == HEAP_INVALID_ADDR) {
            continue;
        }
        invalidHashEntry.hashEntryBase.probeLen = hashEntryBase->probeLen;
        // 当前数据属于旧页
        if ((hashEntryBase->hashCode & newPageMask) == 0u) {
            // 清理新页数据
            SetHashEntryBySlot(ht, pageInfo.virtAddr, idx, invalidHashEntry);
        } else {
            // 清理旧页数据
            *hashEntryBase = invalidHashEntry.hashEntryBase;
            SetHashEntryTpAddr(ht, hashEntryBase, invalidHashEntry.addr);
            SetHashEntryExtend(ht, hashEntryBase, invalidHashEntry.hashExtend);
            oldPageHead->entryUsedNum--;
            newPageHead->entryUsedNum++;
        }
    }
#ifndef NDEBUG
    HashTableCheckPageEntry(idxCtx, pageInfo.virtAddr);
    HashTableCheckPageEntry(idxCtx, oldPageBody);
#endif
    ht->htVersion++;
    ht->memMgr.ccehMeta.segPageCount++;
}

Status HashExpandHashSegment(
    IndexCtxT *idxCtx, HashDirSegmentT *expand, const HashDirSegmentT *oldSeg, uint8_t *oldPageBody)
{
    DB_POINTER4(idxCtx, expand, oldSeg, oldPageBody);
    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;
    if (HashSegInit(idxCtx, expand, oldSeg->segDepth + 1u, &pageInfo) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "segment init unsucc when expand hash segment");
        return GMERR_OUT_OF_MEMORY;
    }

    // rehashing
    Status ret = GMERR_OK;
    AUTO_HASH_PAGE_LOCK_TL(
        idxCtx, pageInfo.virtAddr, true, &pageInfo.pageAddr, (HashExpandRehash(idxCtx, pageInfo, oldSeg, oldPageBody)));
    return ret;
}

static void HashUpdateSegDepthAndPattern(HtHashCodeT hashCode, HashDirSegmentT *oldSeg, HashDirSegmentT *newSeg)
{
    uint32_t patternSpan = 1u << oldSeg->segDepth;
    DB_ASSERT(patternSpan != 0u);
    oldSeg->segDepth += 1u;
    oldSeg->pattern = (uint32_t)(hashCode % ((HtHashCodeT)patternSpan));
    newSeg->pattern = oldSeg->pattern + patternSpan;
}

#ifdef SYS32BITS
ALWAYS_INLINE_C static Status HtGetAllDirPages(HashTableT *ht)
{
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;

    HashMemRunCtxT memRunCtx = {0};
    Status ret = HashMemRunCtxInit(&ht->memMgr, ht->pageAddr, ht->pageAddrArrCapacity, &memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "SE-Hash, unable to init mem runctx, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return ret;
    }
    for (uint32_t i = 0; i < ccehMeta->dirPageCount; i++) {
        ret = HtGetMemPage(&memRunCtx, i, SE_INVALID_PAGE_ID, true, &pageInfo);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "INDEX, Hash index get dir page unsucc, i:%" PRIu32 ", dir page count:%" PRIu32, i,
                ccehMeta->dirPageCount);
            return ret;
        }
    }
    return GMERR_OK;
}
#endif

static void HashFreshDirSegMap(
    IndexCtxT *idxCtx, HashDirSegmentT *segOld, HashDirectoryHeadT *dir, uint32_t segId, HashDirSegmentT *newSeg)
{
    UpdateParaT updatePara = {
        .segDepth = segOld->segDepth,
        .dirDepth = dir->dirDepth,
        .dirCapacity = dir->dirCap,
        .segId = segId,
        .segIdOld = segId,
    };
    HashUpdateDirSegMap(idxCtx, updatePara, *segOld, *newSeg);
}

Status HashExpandUpdateSegAndDir(
    IndexCtxT *idxCtx, ExpandHashParaT expandPara, uint8_t *dirSegPage, uint32_t *expandCount)
{
    DB_POINTER3(idxCtx, dirSegPage, expandCount);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    Status ret;
#ifdef SYS32BITS
    ret = HtGetAllDirPages(ht);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    DB_ASSERT(ccehMeta->hashSegNumPerPage != 0u);
    uint64_t dirSlotIdOld = expandPara.segId % ccehMeta->hashSegNumPerPage;
    HashDirSegmentT *segOld = (HashDirSegmentT *)(void *)(dirSegPage + dirSlotIdOld * sizeof(HashDirSegmentT));
    PageIdT pageAddr = SE_INVALID_PAGE_ADDR;
    uint8_t *oldSegPageHeader = HashGetPageBySeg(idxCtx, segOld, &pageAddr);
    if (SECUREC_UNLIKELY(oldSegPageHeader == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "old segment page header is null, old slot id:%" PRIu64, dirSlotIdOld);
        return GMERR_OUT_OF_MEMORY;
    }

    // 预分配double size需要的页
    uint32_t preAllocCount;
    ret = HashPreAllocHashDirSegPage(idxCtx, &preAllocCount, segOld);
    if (ret != GMERR_OK) {
        return ret;
    }

    HashPageLockTL(idxCtx, oldSegPageHeader, true, &pageAddr);
    HashDirectoryHeadT *dir = &(ccehMeta->dir);
    HashDirSegmentT newSeg = {0};
    ret = HashExpandHashSegment(idxCtx, &newSeg, segOld, oldSegPageHeader);  // expand hash seg and rehash
    if (ret != GMERR_OK) {
        HashPageUnLockTL(idxCtx, oldSegPageHeader, true);
        HashFreeHashDirPage(ht, preAllocCount);
        DB_ASSERT(ccehMeta->dirPageCount == (ccehMeta->dir.dirCap - 1u) / ccehMeta->hashSegNumPerPage + 1u);
        DB_LOG_ERROR(ret, "expand hash segment unsucc");
        return ret;
    }

    HashUpdateSegDepthAndPattern(expandPara.hashCode, segOld, &newSeg);
    if (segOld->segDepth - 1u < dir->dirDepth) {
        HashFreshDirSegMap(idxCtx, segOld, dir, expandPara.segId, &newSeg);
        DB_ASSERT(preAllocCount == 0);
    } else {
        ret = HashDirDoubleSize(dir, idxCtx, expandPara.segId, newSeg);
        DB_ASSERT(ret == GMERR_OK);  // 由于预分配了页，理论上HashDirDoubleSize一定能成功
        (*expandCount)++;
    }
    DB_ASSERT(ccehMeta->dirPageCount == (ccehMeta->dir.dirCap - 1u) / ccehMeta->hashSegNumPerPage + 1u);
    HashPageUnLockTL(idxCtx, oldSegPageHeader, true);
    return ret;
}

Status HashExpandHashTable(const HtInsertProcResT *res, IndexCtxT *idxCtx, HtHashCodeT hashCode, uint32_t *expandCount)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    if (SECUREC_UNLIKELY(ht->idxBase.validCode != HASH_INDEX_VALID_CODE)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "Hash table's run context is novalid");
        return GMERR_INTERNAL_ERROR;
    }
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    if (SECUREC_UNLIKELY(res->dirCap < ccehMeta->dir.dirCap)) {
        return GMERR_OK;
    }
    DB_ASSERT(res->dirCap == ccehMeta->dir.dirCap);  // 此时没有其他线程触发扩容
    SeRunCtxT *seRunCtxPtr = idxCtx->idxOpenCfg.seRunCtx;
    if (SECUREC_UNLIKELY(seRunCtxPtr == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Cant find se runctx ptr when expand hash table");
        return GMERR_OUT_OF_MEMORY;
    }
    uint32_t segId = HtGetPattern(hashCode, ccehMeta->dir.dirCap);
    ExpandHashParaT expandPara = {.hashCode = hashCode, .segId = segId};
    return HashExpandUpdateSegAndDir(idxCtx, expandPara, res->dirSegPage, expandCount);
}

Status HashTrySegInsertOnce(IndexCtxT *idxCtx, HashInsertParaT *para, HtInsertProcResT *res)
{
    DB_POINTER3(idxCtx, para, idxCtx->idxRunCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    res->dirCap = ht->memMgr.ccehMeta.dir.dirCap;
    // segId is the position of the segment in directory for this hashcode
    // get dir page and get dir
    Status ret = HashGetSegmentPageByHashKey(idxCtx, para->hashCode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    res->dirSegPage = ((HashIndexCtxT *)idxCtx->idxRunCtx)->dirSegPage;
    // slot is the start position in segment for this hashcode
    PageIdT *pageAddr = &((HashIndexCtxT *)idxCtx->idxRunCtx)->lastBucketAddr.pageAddr;
    uint8_t *pageBody = ((HashIndexCtxT *)idxCtx->idxRunCtx)->lastBucketAddr.virtAddr;
    // atomic set the entries other than; make sure hashEntryT is 16bytes aligned first
    AUTO_HASH_PAGE_LOCK_TL(
        idxCtx, pageBody, true, pageAddr, (ret = HashCheckUniqueAndInsertEmptySlot(idxCtx, para, pageBody, res)));
    return ret;
}

Status HashTrySegInsert(IndexCtxT *idxCtx, HashInsertParaT *para, HtInsertProcResT *res)
{
    DB_POINTER2(idxCtx, para);
    Status ret;
    uint32_t lockConflictTryCnt = 0;
    uint64_t begin = DbRdtsc();
    ContinueTryLockMethodT lockMethod = {.continueTryLock = HashWLockLLBase, .continueTryUnlock = HashWUnlockLLBase};
    do {
        AUTO_HASH_RLOCK_TL(idxCtx, (ret = HashTrySegInsertOnce(idxCtx, para, res)));
    } while (IdxContinueTryBatched(idxCtx, &lockConflictTryCnt, ret, begin, lockMethod));
    return ret;
}

ALWAYS_INLINE_C static HashInsertParaT HashInitInsertPara(
    const IndexCtxT *idxCtx, const HashTableT *ht, IndexKeyT hashKey, const TupleAddr addr, bool isUndo)
{
    HtHashCodeT hashCode = HtGetHashCode(hashKey);
    uint32_t slot = HashGetTargetSlot(idxCtx, hashCode);
    bool isMultiVersion = HashIndexIsMultiVersion(ht);
    HashInsertParaT insertPara = {.addr = addr,
        .hashKey = hashKey,
        .version = isMultiVersion ? SeTransGetTrxId(idxCtx->idxOpenCfg.seRunCtx) : DB_INVALID_TRX_ID,
        .hashCode = hashCode,
        .targetSlot = slot,
        .lastAddr = HEAP_INVALID_ADDR,
        .isUndo = isUndo,
        .isMultiVersion = isMultiVersion};
    return insertPara;
}

Status HashInsertInternal(IndexCtxT *idxCtx, IndexKeyT hashKey, const TupleAddr addr, bool isUndo)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t expandCount = 0;
    HtInsertProcResT res = {.isFirstCollision = true,  // 当第一次检测到冲突时，冲突计数+1
        .needExpand = false,
        .reserve = 0,
        .dirCap = 0,
        .oldRowId = HEAP_INVALID_ADDR,
        .oldEntry = NULL,
        .dirSegPage = NULL};
    HashInsertParaT insertPara = HashInitInsertPara(idxCtx, ht, hashKey, addr, isUndo);
    Status ret = GMERR_INSUFFICIENT_RESOURCES;
    // retry after segment split
    (void)DbAtomicInc64(&ht->hashInsertCnt);  // 有insert就计数+1，不考虑是否成功插入
    while (expandCount <= HASH_ONE_INSERT_EXPAND_LIMITED) {
        ret = HashTrySegInsert(idxCtx, &insertPara, &res);
        if (!res.needExpand) {
            idxCtx->oldRowId = res.oldRowId;
            break;
        }
        AUTO_HASH_WLOCK_TL(idxCtx, (ret = HashExpandHashTable(&res, idxCtx, insertPara.hashCode, &expandCount)));
        if (ret == GMERR_OUT_OF_MEMORY) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                ret, "Unable to expand hash table, indexId %" PRIu32, idxCtx->idxMetaCfg.indexId);
            return ret;
        }
        if (SECUREC_UNLIKELY(expandCount > HASH_ONE_INSERT_EXPAND_LIMITED)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INSUFFICIENT_RESOURCES,
                "|SE-CCEH|Expand exceed %" PRIu32 " times in a insert, indexId %" PRIu32, expandCount,
                idxCtx->idxMetaCfg.indexId);
            return GMERR_INSUFFICIENT_RESOURCES;  // unlikely in this branch
        }
    }
    return ret;
}

Status HashIndexInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    DB_POINTER2(idxCtx, idxKey.keyData);
#ifndef SYS32BITS
    DB_ASSERT(((HashIndexCtxT *)(void *)idxCtx->idxRunCtx)->memRunCtx.pageId ==
              (PageIdT *)DbShmPtrToAddr(HtCastIdxAsHashTable(idxCtx->idxHandle)->pageAddr));
#endif
    uint8_t cmpRes = (uint8_t)INDEX_FILTER_SATISFIED;
    Status ret = IdxCheckFilterSatisfied(idxCtx, idxKey, &cmpRes);
    if (cmpRes == (uint8_t)INDEX_FILTER_UNSATISFIED || ret != GMERR_OK) {
        return ret;
    }

    if (SECUREC_UNLIKELY(idxCtx->idxOpenCfg.callbackFunc.keyCmp == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "SE-Hash, HUIU: hash comparing unsucc");
        return GMERR_INTERNAL_ERROR;
    }
    ret = HashInsertInternal(idxCtx, idxKey, addr, false);
    if (SECUREC_UNLIKELY(ret == GMERR_LOCK_NOT_AVAILABLE)) {
        DB_SET_LASTERR(ret, "Hash acquire trx lock unsucc");
    }
    return ret;
}

Status HashIndexBatchInsert(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum)
{
    DB_POINTER3(idxCtx, idxKey, addr);
#ifndef SYS32BITS
    DB_ASSERT(((HashIndexCtxT *)(void *)idxCtx->idxRunCtx)->memRunCtx.pageId ==
              (PageIdT *)DbShmPtrToAddr(HtCastIdxAsHashTable(idxCtx->idxHandle)->pageAddr));
#endif
    Status ret = GMERR_OK;
    HashIndexBatchBegin(idxCtx, true);
    idxCtx->isBatch = true;
    for (uint32_t i = 0; i < batchNum; ++i) {
        ret = HashIndexInsert(idxCtx, idxKey[i], addr[i].addrOut);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret,
                "SE-IDX, Hash index batch insert unsucc, i %" PRIu32 ",batch num %" PRIu32 ", indexId %" PRIu32, i,
                batchNum, idxCtx->idxMetaCfg.indexId);
            break;
        }
    }
    idxCtx->isBatch = false;
    HashIndexBatchEnd(idxCtx, true);
    return ret;
}

// 走到delete说明找到一个当前隔离级别可见的数据
Status HashIndexDelete(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr, IndexRemoveParaT removePara)
{
    DB_POINTER3(idxCtx, idxCtx->idxHandle, idxKey.keyData);
#ifndef SYS32BITS
    DB_ASSERT(((HashIndexCtxT *)(void *)idxCtx->idxRunCtx)->memRunCtx.pageId ==
              (PageIdT *)DbShmPtrToAddr(HtCastIdxAsHashTable(idxCtx->idxHandle)->pageAddr));
#endif
    uint8_t cmpRes = (uint8_t)INDEX_FILTER_SATISFIED;
    Status ret = IdxCheckFilterSatisfied(idxCtx, idxKey, &cmpRes);
    if (cmpRes == (uint8_t)INDEX_FILTER_UNSATISFIED || ret != GMERR_OK) {
        return ret;
    }

    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    DB_POINTER(idxCtx->idxOpenCfg.callbackFunc.keyCmp);
    DB_ASSERT(ht->memMgr.ccehMeta.hashEntryNumPerPage != 0u);
    HtHashCodeT hashCode = HtGetHashCode(idxKey);
    HashLookupParaT para = {
        .isUndo = false,
        .isRemove = true,
        .isGC = removePara.isGc,
        .isErase = removePara.isErase,
        .isMarkDelete = !HashIndexIsMultiVersion(ht),
        .addr = addr,
        .hashKey = idxKey,
        .hashCode = hashCode,
        .targetSlot = HashGetTargetSlot(idxCtx, hashCode),
    };
    ret = HashFindHashEntry(idxCtx, &para);
    if (ret == GMERR_NO_DATA) {
        ret = GMERR_OK;
    }
    return ret;
}

Status HashIndexBatchDelete(
    IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum, IndexRemoveParaT removePara)
{
    DB_POINTER3(idxCtx, idxKey, addr);
#ifndef SYS32BITS
    DB_ASSERT(((HashIndexCtxT *)(void *)idxCtx->idxRunCtx)->memRunCtx.pageId ==
              (PageIdT *)DbShmPtrToAddr(HtCastIdxAsHashTable(idxCtx->idxHandle)->pageAddr));
#endif
    Status ret = GMERR_OK;
    HashIndexBatchBegin(idxCtx, true);
    for (uint32_t i = 0; i < batchNum; ++i) {
        ret = HashIndexDelete(idxCtx, idxKey[i], addr[i].addrOut, removePara);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret,
                "SE-IDX, Hash index batch delete unsucc, i %" PRIu32 ",batch num %" PRIu32 ", indexId %" PRIu32, i,
                batchNum, idxCtx->idxMetaCfg.indexId);
            break;
        }
    }
    HashIndexBatchEnd(idxCtx, true);
    return ret;
}

Status HashIndexBatchUpdate(
    IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo[], uint32_t batchNum, IndexRemoveParaT removePara)
{
    DB_POINTER2(idxCtx, updateInfo);
#ifndef SYS32BITS
    DB_ASSERT(((HashIndexCtxT *)(void *)idxCtx->idxRunCtx)->memRunCtx.pageId ==
              (PageIdT *)DbShmPtrToAddr(HtCastIdxAsHashTable(idxCtx->idxHandle)->pageAddr));
#endif
    Status ret = GMERR_OK;
    HashIndexBatchBegin(idxCtx, true);
    for (uint32_t i = 0; i < batchNum; ++i) {
        ret = HashIndexDelete(idxCtx, updateInfo[i].oldIdxKey, updateInfo[i].oldAddr, removePara);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret,
                "SE-IDX, Hash index batch update unsucc when delete, i %" PRIu32 ",batch num %" PRIu32
                ", indexId %" PRIu32,
                i, batchNum, idxCtx->idxMetaCfg.indexId);
            break;
        }
        ret = HashIndexInsert(idxCtx, updateInfo[i].newIdxKey, updateInfo[i].newAddr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret,
                "SE-IDX, Hash index batch update unsucc when insert, i %" PRIu32 ",batch num %" PRIu32
                ", indexId %" PRIu32,
                i, batchNum, idxCtx->idxMetaCfg.indexId);
            break;
        }
    }
    HashIndexBatchEnd(idxCtx, true);
    return ret;
}

Status HashIndexBeginScan(IndexCtxT *idxCtx, IndexScanCfgT scanCfg, IndexScanItrT *iter)
{
    DB_POINTER3(idxCtx, scanCfg.leftKey, iter);
    // HashIndexBeginScan 不在此处判断isConstructed，因为 HashIndexLookup 方法中已经判断
    TupleAddr addr;
    bool isFound = false;
    IndexKeyT secKey = {.keyData = scanCfg.leftKey->keyData, .keyLen = scanCfg.leftKey->keyLen};
    Status ret = HashIndexLookup(idxCtx, secKey, &addr, &isFound);
    if (ret != GMERR_OK) {
        return ret;
    }
    HashIteratorT **itr = (HashIteratorT **)iter;
    if (isFound) {
        *itr = (HashIteratorT *)HtIterInit(idxCtx);
        (*itr)->addr = addr;
        (*itr)->secKey = *scanCfg.leftKey;
    } else {
        *itr = NULL;
    }
    return GMERR_OK;
}

Status HashIndexScan(IndexCtxT *idxCtx, IndexScanItrT iter, TupleAddr *addr, bool *isFound)
{
    DB_POINTER3(idxCtx, addr, isFound);
    *isFound = false;
    if (SECUREC_UNLIKELY(!HashIndexHashTableIsConstructed(HtCastIdxAsHashTable(idxCtx->idxHandle)))) {
        return GMERR_OK;
    }
    if (iter == NULL) {
        return GMERR_OK;
    }
    HashIteratorT *itr = (HashIteratorT *)(void *)iter;
    if (itr->secKey.keyData == NULL || itr->secKey.keyLen == 0u) {
        DB_LOG_DBG_ERROR(GMERR_INVALID_BUFFER, "key buffer is novalid");
        return GMERR_INVALID_BUFFER;
    }
    Status ret = GMERR_OK;
    if (itr->addr != HEAP_INVALID_ADDR) {
        *addr = itr->addr;
        ret = HashFetchReCheck(idxCtx, *addr, itr, isFound);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "hash recheck unsucc, rowId: %" PRIu64 "", itr->addr);
        }
    }
    itr->addr = HEAP_INVALID_ADDR;
    return ret;
}

Status HashIndexUndoInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    DB_POINTER3(idxCtx, idxCtx->idxHandle, idxKey.keyData);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    DB_ASSERT(HashIndexHashTableIsConstructed(ht));
    uint8_t cmpRes = (uint8_t)INDEX_FILTER_SATISFIED;
    Status ret = IdxCheckFilterSatisfied(idxCtx, idxKey, &cmpRes);
    if (cmpRes == (uint8_t)INDEX_FILTER_UNSATISFIED || ret != GMERR_OK) {
        return ret;
    }
    DB_POINTER(idxCtx->idxOpenCfg.callbackFunc.keyCmp);
    DB_ASSERT(ht->memMgr.ccehMeta.hashEntryNumPerPage != 0u);
    HtHashCodeT hashCode = HtGetHashCode(idxKey);
    HashLookupParaT para = {
        .isUndo = true,
        .isRemove = true,
        .isGC = false,
        .isErase = true,
        .isMarkDelete = !HashIndexIsMultiVersion(ht),
        .addr = addr,
        .hashKey = idxKey,
        .hashCode = hashCode,
        .targetSlot = HashGetTargetSlot(idxCtx, hashCode),
    };
    ret = HashFindHashEntry(idxCtx, &para);
    if (ret == GMERR_NO_DATA) {
        ret = GMERR_OK;
    }
    return ret;
}

Status HashIndexUndoRemove(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    DB_POINTER2(idxCtx, idxKey.keyData);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    DB_ASSERT(HashIndexHashTableIsConstructed(ht));
    if (idxCtx->idxOpenCfg.callbackFunc.keyCmp == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "SE-Hash, HUIU: hash comparing unsucc");
        return GMERR_INTERNAL_ERROR;
    }
    uint8_t cmpRes = (uint8_t)INDEX_FILTER_SATISFIED;
    Status ret = IdxCheckFilterSatisfied(idxCtx, idxKey, &cmpRes);
    if (cmpRes == (uint8_t)INDEX_FILTER_UNSATISFIED || ret != GMERR_OK) {
        return ret;
    }
    // OCC 下删除不会标记，所以删除回滚的时候不用进行任何操作。
    if (HashIndexIsMultiVersion(ht)) {
        return GMERR_OK;
    }
    return HashInsertInternal(idxCtx, idxKey, addr, true);
}

Status HashIndexGetKeyCount(IndexCtxT *idxCtx, IndexKeyT idxKey, uint64_t *count)
{
    DB_POINTER3(idxCtx, idxKey.keyData, count);
    // HashIndexGetKeyCount 不在此处判断isConstructed，因为 HashIndexLookup 方法中已经判断
    *count = 0;
    bool isFound = false;
    Status ret = HashIndexLookup(idxCtx, idxKey, NULL, &isFound);
    if (ret != GMERR_OK || !isFound) {
        return ret;
    }
    *count = 1;
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
