/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: se_hash_index_ddl.c
 * Description: Implementation of Hash Index
 * Author:
 * Create: 2023/8/25
 */

#include "se_hash_index.h"
#include "se_hash_index_stash_page.h"
#include "db_inter_process_rwlatch.h"
#include "db_utils.h"

#ifdef __cplusplus
extern "C" {
#endif

#define HASH_TRUNCATE_PAGE_CACHE_NUM 2
#define MV_ENTRY_PER_BUCKET 3

// start from data
Status HashCreateInitInsAndCtx(uint16_t instanceId, SeInstanceT **seInstance, DbMemCtxT **htMemCtx)
{
    DB_POINTER(htMemCtx);
    *seInstance = (SeInstanceT *)SeGetInstance(instanceId);
    if (*seInstance == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "SE-Hash, HCIN: hash's SE instance is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *htMemCtx = (DbMemCtxT *)DbGetShmemCtxById((*seInstance)->hashIndexShmMemCtxId, instanceId);
    if (*htMemCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "SE-Hash, HCIN: hash's mem context is novalid");
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

static void InitHtMemMgr(HashTableT *newHt, SeInstanceT *seInstance, const IndexMetaCfgT *idxCfg)
{
    newHt->memMgr.base.fileId = SeGetNewTrmId(seInstance);
    newHt->memMgr.base.seInstanceId = seInstance->instanceId;
    newHt->memMgr.base.tableSpaceIndex = idxCfg->tableSpaceIndex;
    newHt->memMgr.allocFirst = true;
    newHt->memMgr.base.isVertexUseRsm = idxCfg->isVertexUseRsm;
}

static void InitMVPageInfo(HashTableT *newHt)
{
    newHt->mvPageInfo.reserve = 0;
    newHt->mvPageInfo.mvPageUsedCnt = 0;
    newHt->mvPageInfo.curMVPageBlockId = DB_INVALID_UINT16;
    DbSpinInit(&newHt->mvPageInfo.lock);
}

void HashCreateInitNewHt(
    SeInstanceT *seInstance, const IndexMetaCfgT *idxCfg, HashTableT *newHt, uint32_t pagePtrArrCapacity)
{
    DB_POINTER3(seInstance, idxCfg, newHt);
    (void)memset_s(newHt, sizeof(HashTableT), 0, sizeof(HashTableT));
    newHt->idxBase.indexCfg = *idxCfg;
    newHt->pageAddrArrCapacity = pagePtrArrCapacity;
    newHt->pageAddr = DB_INVALID_SHMPTR;
    DbRWLatchInit(&(newHt->idxBase.idxLatch));
    newHt->idxBase.shmemCtxId = seInstance->hashIndexShmMemCtxId;
    newHt->hashLinklistShmMemCtxId = seInstance->hashLinklistShmMemCtxId;
    newHt->pageSize = (uint32_t)(seInstance->seConfig.pageSize * DB_KIBI > HASH_PAGE_HEAD_SIZE ?
                                     seInstance->seConfig.pageSize * DB_KIBI - HASH_PAGE_HEAD_SIZE :
                                     0);
    newHt->stashPageId = SE_INVALID_PAGE_ID;
    newHt->stashPageNum = 0;
    InitMVPageInfo(newHt);
    DbRWLatchInit(&newHt->idxBase.stashPageLatch);
    CcehMemMetaT *ccehMeta = &newHt->memMgr.ccehMeta;
    ccehMeta->hashSegNumPerPage = (uint32_t)(newHt->pageSize / sizeof(HashDirSegmentT));
    newHt->pageAddrArrCapBits = DbLog2(pagePtrArrCapacity * ccehMeta->hashSegNumPerPage);
    newHt->heapTupleAddrMode =
        idxCfg->isUseClusteredHash ? SE_HEAP_TUPLE_ADDR_64 : IdxGetTupleAddrMode(seInstance, idxCfg->isVertexUseRsm);
    ccehMeta->hashEntrySize = (uint32_t)sizeof(HtHashCodeT);
    // 根据类型计算hashEntry长度
    uint32_t rowAddrSize = HashGetAddrSize(newHt->heapTupleAddrMode);
    ccehMeta->hashEntrySize += rowAddrSize;
    ccehMeta->hashEntryMetaSize = ccehMeta->hashEntrySize;
    // 索引多版本下，每个entry 需要多记录一个版本号和老数据addr以及版本链信息，用于回滚和删除
    if (HashIndexIsMultiVersion(newHt)) {
        ccehMeta->hashEntrySize += (uint32_t)(sizeof(uint64_t) + rowAddrSize + sizeof(HashMVNodeT));
    }
    // 为了segment page页面布局不变，多版本下，一个bucket大小为3个entry大小；其他场景为一个cacheline
    uint32_t bucketSize =
        HashIndexIsMultiVersion(newHt) ? (ccehMeta->hashEntrySize * MV_ENTRY_PER_BUCKET) : CACHELINE_SIZE;
    ccehMeta->hashSegBits = DbLog2(seInstance->seConfig.pageSize * DB_KIBI / bucketSize);
    ccehMeta->hashEntryNumPerPage = newHt->pageSize / ccehMeta->hashEntrySize;
    ccehMeta->hashEntriesPerCacheline = bucketSize / ccehMeta->hashEntrySize;
    ccehMeta->hashMaxProbeLen =
        ccehMeta->hashEntriesPerCacheline *
        (HashIndexIsMultiVersion(newHt) ? SE_HASH_PROBE_CACHELINE_NUM_MULTIVERSION : SE_HASH_PROBE_CACHELINE_NUM);
    DB_ASSERT(ccehMeta->hashEntriesPerCacheline != 0);
    IdxMarkUnConstructed(&newHt->idxBase);
    InitHtMemMgr(newHt, seInstance, idxCfg);
    newHt->idxBase.validCode = HASH_INDEX_VALID_CODE;
}

Status HashIndexCreate(SeRunCtxT *seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr)
{
    DB_POINTER(idxShmAddr);
    *idxShmAddr = DB_INVALID_SHMPTR;
    SeInstanceT *seInstance = (SeInstanceT *)seRunCtx->seIns;
    DbMemCtxT *htMemCtx = (DbMemCtxT *)DbGetShmemCtxById(seInstance->hashIndexShmMemCtxId, seRunCtx->instanceId);
    if (htMemCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "SE-Hash, HCIN: hash's mem context is novalid");
        return GMERR_INTERNAL_ERROR;
    }
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_INDEX, "Hash-dmsg, HashIndexCreate indexId %" PRIu32 ".", idxCfg.indexId);
    ShmemPtrT newHtShmAddr = {0};

    uint32_t pagePtrArrCapacity;
    HashCalcPagePtrArrCapacity(seInstance, &pagePtrArrCapacity);
    /* 索引drop时删除 */
    HashTableT *newHt = SeShmAlloc(htMemCtx, (uint32_t)(sizeof(HashTableT)), &newHtShmAddr);
    if (newHt == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INSUFFICIENT_RESOURCES,
            "SE-Hash, HC: unable to alloc new hashtable indexId %" PRIu32 ", size:%" PRIu32, idxCfg.indexId,
            (uint32_t)(sizeof(HashTableT)));
        return GMERR_INSUFFICIENT_RESOURCES;
    }
    HashCreateInitNewHt(seInstance, &idxCfg, newHt, pagePtrArrCapacity);

    /* 索引drop/tuncate reset */
    Status ret = HtSwizzleCreate(&newHt->memMgr, seInstance->hashIndexShmMemCtxId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "SE-Hash, alloc hashswizzle, indexId %" PRIu32 ".", idxCfg.indexId);
        DbShmemCtxFree(htMemCtx, newHtShmAddr);
        return ret;
    }
    // 多版本场景下，才创建多版本页
    if (HashIndexIsMultiVersion(newHt)) {
        ret = HtCreateMVPageSwizzle(&newHt->memMgr, seInstance->hashIndexShmMemCtxId);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "SE-Hash, alloc mv page array, indexId %" PRIu32, idxCfg.indexId);
            DbShmArrayDestroy(&newHt->memMgr.base.hashSwizzleArray, seInstance->dbInstance);
            DbShmemCtxFree(htMemCtx, newHtShmAddr);
            return ret;
        }
    }

    *idxShmAddr = newHtShmAddr;
    return GMERR_OK;
}

static Status HashFreeSegPagesInner(
    HashTableT *ht, HashMemRunCtxT *memRunCtx, DbMemAddrT *pageInfo, uint32_t *freeSegCount, EhTrcPageCacheT *cache)
{
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    HashDirectoryHeadT *dir = &(ccehMeta->dir);
    for (uint32_t segSlotId = 0; segSlotId < ccehMeta->hashSegNumPerPage; segSlotId++) {
        if (*freeSegCount >= dir->dirCap) {
            DB_ASSERT(cache == NULL || cache->cnt > 0);
            return GMERR_PROGRAM_LIMIT_EXCEEDED;
        }
        HashDirSegmentT *seg = (HashDirSegmentT *)(void *)(pageInfo->virtAddr + segSlotId * sizeof(HashDirSegmentT));
        if (seg->pageAddr.pageId == SE_INVALID_PAGE_ID) {
            continue;
        }
        Status ret = HtFreeMemPage(memRunCtx, seg->pageAddr.pageId, seg->pageAddr.blockId, cache);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "SE-Hash, Unable to free hash page %" PRIu32 ",%" PRIu32 ",%" PRIu32 "",
                ht->memMgr.base.fileId, seg->pageAddr.pageId, seg->pageAddr.blockId);
        }
        (*freeSegCount)++;
        DB_ASSERT(cache == NULL || cache->cnt > 0);
    }
    DB_ASSERT(cache == NULL || cache->cnt > 0);
    return GMERR_OK;
}

void HashFreeSegPages(HashTableT *ht, EhTrcPageCacheT *cache)
{
    DB_POINTER(ht);
    ht->htVersion++;
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    uint32_t freeSegCount = 0;
    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;
    DB_ASSERT(cache == NULL || cache->cnt == 0);
    HashMemRunCtxT memRunCtx = {0};
    Status ret = HashMemRunCtxInit(&ht->memMgr, ht->pageAddr, ht->pageAddrArrCapacity, &memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "SE-Hash, unable to init mem runctx, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return;
    }
    for (uint32_t i = 0; i < ccehMeta->dirPageCount; i++) {
        ret = HtGetMemPage(&memRunCtx, i, SE_INVALID_PAGE_ID, true, &pageInfo);
        DEBUG_EXTRAMSG(EXTRAMSG_LOG_INDEX,
            "Hash-dmsg, HtGetMemPage indexId %" PRIu32 ", ret = %" PRId32 ", dirPageCnt %" PRIu32 ", i %" PRIu32 ".",
            ht->idxBase.indexCfg.indexId, (int32_t)ret, ccehMeta->dirPageCount, i);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            ccehMeta->segPageCount = 0;
            DB_LOG_ERROR(ret,
                "INDEX, Hash index get dir page unsucc, i:%" PRIu32 ", dir page count:%" PRIu32
                ", seg page count:%" PRIu32,
                i, ccehMeta->dirPageCount, ccehMeta->segPageCount);
            return;
        }
        ret = HashFreeSegPagesInner(ht, &memRunCtx, &pageInfo, &freeSegCount, cache);
        if (ret != GMERR_OK) {
            DB_ASSERT(cache == NULL || cache->cnt > 0);
            break;
        }
    }
    ccehMeta->segPageCount = 0u;
    DB_ASSERT(cache == NULL || cache->cnt > 0);
    return;
}

static Status HashIndexDropStashPage(HashTableT *ht, HashMemRunCtxT *memRunCtx)
{
    if (ht->stashPageId == SE_INVALID_PAGE_ID) {
        return GMERR_OK;
    }
    DbRWLatchW(&ht->idxBase.stashPageLatch);
    Status ret = HashStashPageDrop(ht, memRunCtx);
    DbRWUnlatchW(&ht->idxBase.stashPageLatch);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret,
            "SE-Hash, unable to drop stash page when drop hash index, indexId %" PRIu32 ".",
            ht->idxBase.indexCfg.indexId);
    }
    return ret;
}

Status HashIndexDrop(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    HashTableT *ht = (HashTableT *)DbShmPtrToAddr(idxShmAddr);
    if (ht == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR,
            "SE-Hash, HC: hashtable is novalid. segid: %" PRIu32 " offset: %" PRIu32 "", idxShmAddr.segId,
            idxShmAddr.offset);
        return GMERR_INTERNAL_ERROR;
    }
    if (ht->idxBase.validCode == 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR,
            "SE-Hash, repeat drop hash index, segid: %" PRIu32 " offset: %" PRIu32 ".", idxShmAddr.segId,
            idxShmAddr.offset);
        return GMERR_INTERNAL_ERROR;
    }
    DbRWLatchW(&ht->idxBase.idxLatch);
    // free seg page (which entry stored) first
    HashFreeSegPages(ht, NULL);
    // remove shmArray and free dir page (which directory stored) second
    HashMemRunCtxT memRunCtx = {0};
    Status ret = HashMemRunCtxInit(&ht->memMgr, ht->pageAddr, ht->pageAddrArrCapacity, &memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        DB_LOG_ERROR(ret, "SE-Hash, unable to init mem runctx, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return ret;
    }
    DbInstanceHdT dbInstance = ((SeInstanceT *)(seRunCtx->seIns))->dbInstance;
    // 释放多版本页
    if (HashIndexIsMultiVersion(ht)) {
        HtDestroyMVPageSwizzle(&memRunCtx, dbInstance);
    }
    ret = HashIndexDropStashPage(ht, &memRunCtx);
    if (ret != GMERR_OK) {
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        return ret;
    }
    HtSwizzleDestroy(&memRunCtx, dbInstance);

    void *htMemCtx = DbGetShmemCtxById(ht->idxBase.shmemCtxId, seRunCtx->instanceId);
    if (htMemCtx == NULL) {
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR,
            "SE-Hash, HCIN: hash's mem context is novalid, shmId:%" PRIu32, ht->idxBase.shmemCtxId);
        return GMERR_INTERNAL_ERROR;
    }
    if (DbIsShmPtrValid(ht->pageAddr)) {
        DbShmemCtxFree(htMemCtx, ht->pageAddr);
    }
    ht->idxBase.validCode = 0u;
    DbRWUnlatchW(&ht->idxBase.idxLatch);
    DbShmemCtxFree(htMemCtx, idxShmAddr);
    return GMERR_OK;
}

Status HashIndexTruncateStashPage(HashTableT *uniqueHt, HashMemRunCtxT *memRunCtx)
{
    DbRWLatchW(&uniqueHt->idxBase.stashPageLatch);
    Status ret = HashStashPageDrop(uniqueHt, memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret,
            "SE-Hash, unable to drop stash page when truncate hash index, indexId %" PRIu32,
            uniqueHt->idxBase.indexCfg.indexId);
    }
    DbRWUnlatchW(&uniqueHt->idxBase.stashPageLatch);
    return ret;
}

Status HashSegConstructor(HashTableT *newHt, uint32_t *blockId, uint32_t *pageId)
{
    DB_POINTER(newHt);
    CcehMemMetaT *ccehMeta = &newHt->memMgr.ccehMeta;
    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;
    HashMemRunCtxT memRunCtx = {0};
    Status ret = HashMemRunCtxInit(&newHt->memMgr, newHt->pageAddr, newHt->pageAddrArrCapacity, &memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "SE-Hash, unable to init mem runctx, indexId %" PRIu32 ".", newHt->idxBase.indexCfg.indexId);
        return ret;
    }
    HashAllocPageParamT allocParam = {.tableSpaceIndex = newHt->idxBase.indexCfg.tableSpaceIndex, .isDir = false};

    uint32_t allocPageId;
    ret = HtAllocMemPage(&memRunCtx, &allocParam, blockId, &pageInfo, &allocPageId);
    if (ret != GMERR_OK) {
        return ret;
    }
    *pageId = allocPageId;
    (void)memset_s(pageInfo.virtAddr, newHt->pageSize, 0x00, newHt->pageSize);
    ccehMeta->segPageCount++;
    PageHeadT *segPage = (PageHeadT *)(void *)pageInfo.virtAddr - 1;
    DbInterProcRWLatchW(&segPage->lock);
    uint8_t *segPageEntry = pageInfo.virtAddr;
    HashEntryT invalidHashEntry = {.hashEntryBase = {0},
        .addr = HEAP_INVALID_ADDR,
        .hashExtend = {
            .hashEntryVersion = DB_INVALID_TRX_ID, .oldAddr = HEAP_INVALID_ADDR, .mvNodeHeader = INVALID_MV_NODE}};
    for (uint32_t i = 0; i < newHt->memMgr.ccehMeta.hashEntryNumPerPage; ++i) {
        SetHashEntryBySlot(newHt, segPageEntry, i, invalidHashEntry);
    }
    DbInterProcRWUnlatchW(&segPage->lock);
    return GMERR_OK;
}

Status HashInitDirSegPage(
    HashTableT *ht, uint32_t initDirBlockRemain, uint32_t blockId, uint32_t dirDepth, uint32_t *segPageId)
{
    DB_POINTER(segPageId);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    DbMemAddrT pageInfo = DB_INVALID_MEM_ADDR;
    HashMemRunCtxT memRunCtx = {0};
    Status ret = HashMemRunCtxInit(&ht->memMgr, ht->pageAddr, ht->pageAddrArrCapacity, &memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "SE-Hash, unable to init mem runctx, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return ret;
    }
    HashAllocPageParamT allocParam = {.tableSpaceIndex = ht->idxBase.indexCfg.tableSpaceIndex, .isDir = true};
    ret = HtAllocMemPage(&memRunCtx, &allocParam, &blockId, &pageInfo, NULL);
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_INDEX,
        "Hash-dmsg, HtAllocMemPage indexId %" PRIu32 ", ret = %" PRId32 ", dirPagecount %" PRIu32 ".",
        ht->idxBase.indexCfg.indexId, (int32_t)ret, ccehMeta->dirPageCount);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "SE-Hash, unable to alloc dir page, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return ret;
    }
    ccehMeta->dirPageCount++;
    PageHeadT *dirPage = (PageHeadT *)(void *)pageInfo.virtAddr - 1;
    DbInterProcRWLatchW(&dirPage->lock);
    HashDirSegmentT *seg = (HashDirSegmentT *)(void *)pageInfo.virtAddr;
    for (uint32_t j = 0; j < initDirBlockRemain; j++) {
        seg->pattern = *segPageId;
        seg->segDepth = dirDepth;
        ret = HashSegConstructor(ht, &seg->pageAddr.blockId, &seg->pageAddr.pageId);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(
                ret, "SE-Hash, unable to constructor seg page, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
            DEBUG_EXTRAMSG(EXTRAMSG_LOG_INDEX,
                "Hash-dmsg, HashSegConstructor indexId %" PRIu32 ", ret = %" PRId32 ", dirPagecount %" PRIu32
                ", segCnt %" PRIu32 ", j %" PRIu32 ".",
                ht->idxBase.indexCfg.indexId, (int32_t)ret, ccehMeta->dirPageCount, (*segPageId), j);
            for (uint32_t k = j; k < ccehMeta->hashSegNumPerPage; k++) {
                *seg = InvalidHashDirSegment();
                seg++;
            }
            DbRWUnlatchW(&dirPage->lock);
            return ret;
        }
        seg++;
        (*segPageId)++;
    }
    DbInterProcRWUnlatchW(&dirPage->lock);
    return GMERR_OK;
}

Status HashInitFullDirSeg(HashTableT *ht, uint32_t initDirBlockCnt, uint32_t dirDepth, uint32_t *segPageId)
{
    DB_POINTER(ht);
    for (uint32_t i = 0; i < initDirBlockCnt; i++) {
        Status ret = HashInitDirSegPage(ht, ht->memMgr.ccehMeta.hashSegNumPerPage, i, dirDepth, segPageId);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(
                ret, "SE-Hash, init dir seg page unsucessful, i:%" PRIu32 ", seg page id:%" PRIu32, i, *segPageId);
            return ret;
        }
    }
    return GMERR_OK;
}

Status HashDirConstructor(HashTableT *newHt, uint32_t dirDepth)  // save to page
{
    DB_POINTER(newHt);
    uint32_t capacity = (1u << dirDepth);
    // set page 0 dir head page
    CcehMemMetaT *ccehMeta = &newHt->memMgr.ccehMeta;
    DB_ASSERT(ccehMeta->hashSegNumPerPage != 0u);
    uint32_t initDirBlockCnt = capacity / ccehMeta->hashSegNumPerPage;
    uint32_t initDirBlockRemain = capacity % ccehMeta->hashSegNumPerPage;
    uint32_t indexId = newHt->idxBase.indexCfg.indexId;
    // 大于号左边是需要的Dir页个数（向上取整）
    if ((capacity - 1u) / ccehMeta->hashSegNumPerPage + 1u > newHt->pageAddrArrCapacity) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INSUFFICIENT_RESOURCES,
            "SE-Hash, novalid cap, indexId %" PRIu32 ", caps %" PRIu32 ", arrCaps %" PRIu32 ".", indexId,
            ((capacity - 1u) / ccehMeta->hashSegNumPerPage + 1u), newHt->pageAddrArrCapacity);
        return GMERR_INSUFFICIENT_RESOURCES;
    }
    uint32_t segPageId = 0u;
    Status ret = HashInitFullDirSeg(newHt, initDirBlockCnt, dirDepth, &segPageId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret,
            "SE-Hash, unable to init full dirSeg, indexId %" PRIu32 ", dir blk cnt:%" PRIu32, indexId, initDirBlockCnt);
        return ret;
    }
    // set page 1 segment pagesegDepth
    if (initDirBlockRemain != 0u) {
        ret = HashInitDirSegPage(newHt, initDirBlockRemain, initDirBlockCnt, dirDepth, &segPageId);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "SE-Hash, unable to init dirSeg, indexId %" PRIu32 ".", indexId);
            return ret;
        }
    }
    return GMERR_OK;
}

static Status AllocPageArrMem(SeRunCtxHdT seRunCtx, HashTableT *ht)
{
    // truncate场景下，pageAddr有效，不重新申请内存
    if (DbIsShmPtrValid(ht->pageAddr)) {
        return GMERR_OK;
    }
    SeInstanceT *seInstance = NULL;
    DbMemCtxT *htMemCtx = NULL;
    Status ret = HashCreateInitInsAndCtx(seRunCtx->instanceId, &seInstance, &htMemCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "SE-Hash create init ins and ctx, index id: %" PRIu32, ht->idxBase.indexCfg.indexId);
        return ret;
    }
    uint32_t pageArrSize = ht->pageAddrArrCapacity * (uint32_t)sizeof(ShmemPtrT);
    /* 索引drop时删除 */
    PageIdT *pageAddr = (PageIdT *)SeShmAlloc(htMemCtx, pageArrSize, &ht->pageAddr);
    if (pageAddr == NULL || !DbIsShmPtrValid(ht->pageAddr)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY,
            "unable to alloc page array, size: %" PRIu32 ", index id: %" PRIu32, pageArrSize,
            ht->idxBase.indexCfg.indexId);
        return GMERR_OUT_OF_MEMORY;
    }
    for (uint32_t i = 0; i < ht->pageAddrArrCapacity; i++) {
        pageAddr[i] = SE_INVALID_PAGE_ADDR;
    }
    return GMERR_OK;
}

Status HashCreateHashTable(SeRunCtxHdT seRunCtx, HashTableT *newHt, uint32_t hashCap)
{
    DB_POINTER(newHt);
    Status ret = AllocPageArrMem(seRunCtx, newHt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "SE-Hash alloc page arr mem, index id: %" PRIu32, newHt->idxBase.indexCfg.indexId);
        return ret;
    }
    CcehMemMetaT *ccehMeta = &newHt->memMgr.ccehMeta;
    DB_ASSERT(ccehMeta->hashEntryNumPerPage != 0u);
    DB_ASSERT(ccehMeta->hashSegNumPerPage != 0u);
    uint32_t dirDepth = DbLog2(hashCap / ccehMeta->hashEntryNumPerPage);
    uint32_t dirCap = (1u << dirDepth);
    newHt->entryUsed = 0u;
    newHt->htVersion++;
    // Hash table directory pointer point to the shared memory offset
    ccehMeta->dir.dirCap = dirCap;
    ccehMeta->dir.dirDepth = dirDepth;
    ret = HashDirConstructor(newHt, dirDepth);
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_INDEX, "Hash-dmsg, HashDirConstructor indexId %" PRIu32 ", ret = %" PRId32 ".",
        newHt->idxBase.indexCfg.indexId, ret);
    if (ret != GMERR_OK) {
        HashFreeSegPages(newHt, NULL);
        ccehMeta->dir.dirCap = 0;
        ccehMeta->dir.dirDepth = 0;
        HashFreeHashDirPage(newHt, ccehMeta->dirPageCount);
        DB_LOG_ERROR(ret, "SE-Hash, Unable to construct hashtable dir, depth:%" PRIu32, dirDepth);
        return ret;
    }
    ccehMeta->segPageCount = dirCap;
    ccehMeta->dirPageCount = (dirCap - 1u) / ccehMeta->hashSegNumPerPage + 1u;
    // 初始化时，将缩容参数置0，之后每次缩容成功后再置0，缩容时间片用完不置0
    ccehMeta->dirNextPageId = 0;
    ccehMeta->dirNextItemIdx = 0;
    return GMERR_OK;
}

typedef struct HashGetPagePara {
    uint8_t *dirPage;
    uint8_t *segPage;
    HashPageDescT *item;
} HashGetPageParaT;

Status HashGetDirPageAndSegPage(
    HashTableT *ht, HashMemRunCtxT *memRunCtx, EhTrcPageCacheT *cache, HashGetPageParaT *output)
{
    StatusInter ret = MdGetPage(memRunCtx->mdMgr, cache->pageId[0], &output->dirPage, ENTER_PAGE_NORMAL, false);
    if (ret != STATUS_OK_INTER) {
        return DbGetExternalErrno(ret);  // should not be here
    }
    ret = MdGetPage(memRunCtx->mdMgr, cache->pageId[1], &output->segPage, ENTER_PAGE_NORMAL, false);
    if (ret != STATUS_OK_INTER) {
        return DbGetExternalErrno(ret);  // should not be here
    }
    uint32_t itemId = 0;
    DbArrayAddrT itemAddr;
    ret = DbShmArrayGetItem(&ht->memMgr.base.hashSwizzleArray, &itemId, (void **)&output->item, &itemAddr);
    DB_ASSERT(itemId == 0);
    return GMERR_OK;
}

void HashIndexReconstruct(HashTableT *ht, HashMemRunCtxT *memRunCtx, EhTrcPageCacheT *cache)
{
    DB_ASSERT(cache->cnt == EH_DIR_AND_SEG_PAGE);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    ht->entryUsed = 0u;
    ht->htVersion++;
    PageIdT *pageIds = (PageIdT *)DbShmPtrToAddr(ht->pageAddr);
    if (pageIds == NULL) {
        return;
    }
    HashGetPageParaT op;
    Status ret = HashGetDirPageAndSegPage(ht, memRunCtx, cache, &op);
    if (ret != GMERR_OK) {
        return;
    }
    // 初始化dir数组
    pageIds[0] = cache->pageId[0];
    for (uint32_t i = 1; i < ht->pageAddrArrCapacity; i++) {
        pageIds[i] = SE_INVALID_PAGE_ADDR;
    }
    ccehMeta->dir.dirCap = 1;
    ccehMeta->dir.dirDepth = 0;
    ccehMeta->dirPageCount = 1;
    (void)memset_s(op.dirPage + HASH_PAGE_HEAD_SIZE, ht->pageSize, 0x00, ht->pageSize);
    HashDirSegmentT *seg = (HashDirSegmentT *)(void *)(op.dirPage + HASH_PAGE_HEAD_SIZE);
    seg->pattern = 0;
    seg->segDepth = 0;
    seg->pageAddr.blockId = 0;
    seg->pageAddr.pageId = SerializePageId((PageMgrT *)memRunCtx->mdMgr, ((PageHeadT *)op.segPage)->addr);
    // 初始化数据页
    op.item->pageId = ((PageHeadT *)op.segPage)->addr;
    (void)memset_s(op.segPage + HASH_PAGE_HEAD_SIZE, ht->pageSize, 0x00, ht->pageSize);
    ccehMeta->segPageCount++;
    uint8_t *segPageEntry = op.segPage + HASH_PAGE_HEAD_SIZE;
    HashEntryT invalidHashEntry = {.hashEntryBase = {0},
        .addr = HEAP_INVALID_ADDR,
        .hashExtend = {
            .hashEntryVersion = DB_INVALID_TRX_ID, .oldAddr = HEAP_INVALID_ADDR, .mvNodeHeader = INVALID_MV_NODE}};
    for (uint32_t i = 0; i < ht->memMgr.ccehMeta.hashEntryNumPerPage; ++i) {
        SetHashEntryBySlot(ht, segPageEntry, i, invalidHashEntry);
    }
    IdxConstructedFinish(&ht->idxBase);
}

Status HashIndexTruncate(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    HashTableT *ht = (HashTableT *)DbShmPtrToAddr(idxShmAddr);
    if (ht == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR,
            "SE-Hash, HC: unique hashtable is novalid. segid: %" PRIu32 " offset: %" PRIu32 "", idxShmAddr.segId,
            idxShmAddr.offset);
        return GMERR_INTERNAL_ERROR;
    }

    // Lock the table. Determine whether the table has been opened by other clients or not.
    // If not, it can be truncated. Otherwise, wrong code is returned.
    DbRWLatchW(&ht->idxBase.idxLatch);

    // haven't open
    if (!IdxIsConstructed(&ht->idxBase)) {
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        return GMERR_OK;
    }
    if (ht->idxBase.validCode != HASH_INDEX_VALID_CODE) {
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "SE-Hash, HUT: Hash table has been dropped");
        return GMERR_INTERNAL_ERROR;
    }

    EhTrcPageCacheT cache = EH_TRC_PAGE_CACHE_INIT_VALUE;
    // First, free seg page (which entry stored)
    HashFreeSegPages(ht, &cache);

    // Second, clear shmArray and free dir page (which directory stored)
    uint32_t idxId = ht->idxBase.indexCfg.indexId;
    HashMemRunCtxT memRunCtx = {0};
    Status ret = HashMemRunCtxInit(&ht->memMgr, ht->pageAddr, ht->pageAddrArrCapacity, &memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbRWUnlatchW(&ht->idxBase.idxLatch);
        DB_LOG_ERROR(ret, "SE-Hash, unable to init mem runctx, indexId %" PRIu32, idxId);
        return ret;
    }
    // truncate多版本页
    if (HashIndexIsMultiVersion(ht)) {
        HtTruncateMVPageSwizzle(&memRunCtx);
        InitMVPageInfo(ht);
    }
    if (ht->stashPageId != SE_INVALID_PAGE_ID) {
        ret = HashIndexTruncateStashPage(ht, &memRunCtx);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DbRWUnlatchW(&ht->idxBase.idxLatch);
            return ret;
        }
    }

    HtSwizzleReset(&memRunCtx, &cache);
    IdxMarkUnConstructed(&ht->idxBase);
    HashIndexReconstruct(ht, &memRunCtx, &cache);
    DbRWUnlatchW(&ht->idxBase.idxLatch);
    return GMERR_OK;
}

void HashFreeHashDirPage(HashTableT *ht, uint32_t freeCount)
{
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    HashMemRunCtxT memRunCtx = {0};
    Status ret = HashMemRunCtxInit(&ht->memMgr, ht->pageAddr, ht->pageAddrArrCapacity, &memRunCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "SE-Hash, unable to init mem runctx, indexId %" PRIu32 ".", ht->idxBase.indexCfg.indexId);
        return;
    }
    for (uint32_t i = 0; i < freeCount; i++) {
        uint32_t dirId = (ccehMeta->dirPageCount - i) - 1;
        PageIdT addr = memRunCtx.pageId[dirId];
        memRunCtx.pageId[dirId] = SE_INVALID_PAGE_ADDR;
        ret = HtFreePage(&memRunCtx, addr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to free pre alloced hash dir seg page, blockId=%" PRIu32 ", deviceId=%" PRIu32,
                addr.blockId, addr.deviceId);
            DB_ASSERT(0);  // 刚刚申请成功的内存free一定成功
        }
        memRunCtx.pageId[dirId] = SE_INVALID_PAGE_ADDR;
    }
    ccehMeta->dirPageCount -= freeCount;
}

Status HashInitRunCtx(IndexCtxT *idxCtx, HashTableT *ht)
{
    DB_POINTER(idxCtx);
    /* When the HASH index accesses the HEAP, HEAP will apply for transaction lock of row
     * and the latch of the index page is held in the meantime, which may lead to deadlocks
     * caused by interdependency between transaction locks and index page latches.
     * To avoid this case, set isAcquireLockByTryOnce as true,
     * and HEAP will not wait if lock conflict applying for row's transaction locks. */
    idxCtx->isAcquireLockByTryOnce = true;

    ((HashIndexCtxT *)idxCtx->idxRunCtx)->lastBucketAddr = DB_INVALID_MEM_ADDR;
    idxCtx->batchLocked = false;
    ((HashIndexCtxT *)idxCtx->idxRunCtx)->hashIndexVersion = ht->htVersion;
    HashMemRunCtxT *memRunCtx = &((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx;
    memRunCtx->mdMgr = idxCtx->idxOpenCfg.seRunCtx->mdMgr;

    return HashMemRunCtxInit(&ht->memMgr, ht->pageAddr, ht->pageAddrArrCapacity, memRunCtx);
}

void HashUnInitRunCtx(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    idxCtx->idxShmAddr = DB_INVALID_SHMPTR;
    idxCtx->idxMetaCfg = (IndexMetaCfgT){0};
    idxCtx->idxHandle = NULL;
    ((HashIndexCtxT *)idxCtx->idxRunCtx)->hashIndexVersion = 0u;
    ((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx.pageId = NULL;
    ((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx.mdMgr = NULL;
    ((HashIndexCtxT *)idxCtx->idxRunCtx)->memRunCtx.memMgr = NULL;
}

Status HashIndexOpen(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    HashTableT *ht = HtCastIdxAsHashTable(idxCtx->idxHandle);
    Status ret = HashInitRunCtx(idxCtx, ht);
    if (ret != GMERR_OK) {
        return ret;
    }
    // construct ht while Hashtable Open
    ret = HashIndexVerifyAndConstructHashTable(idxCtx->idxOpenCfg.seRunCtx, ht);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Hash Index, Open hash index unsucc, indexId %" PRIu32, ht->idxBase.indexCfg.indexId);
        HashUnInitRunCtx(idxCtx);
        return ret;
    }
    // pageArr内存在open时候申请，需要刷新memRunCtx对应值
    ret = HashMemRunCtxUpdatePageId(&((HashIndexCtxT *)(void *)idxCtx->idxRunCtx)->memRunCtx, ht->pageAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Hash Index, update page id unsucc, indexId %" PRIu32, ht->idxBase.indexCfg.indexId);
        HashUnInitRunCtx(idxCtx);
    }
    return ret;
}

void HashIndexClose(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    HashUnInitRunCtx(idxCtx);
}

Status HashIndexStatView(ShmemPtrT idxShmAddr, uint16_t instanceId, IndexStatisticsT *idxStat)
{
    DB_POINTER(idxStat);

    HashTableT *ht = DbShmPtrToAddr(idxShmAddr);
    if (ht == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "SE-Hash, HPHIS: Hash table is novalid");
        return GMERR_INTERNAL_ERROR;
    }
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(instanceId);
    if (seInstance == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "SE-Hash, HCIN: hash's SE Instance is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbRWLatchR(&ht->idxBase.idxLatch);
    CcehMemMetaT *ccehMeta = &ht->memMgr.ccehMeta;
    uint32_t pageCount = ccehMeta->segPageCount + ccehMeta->dirPageCount;
    uint32_t perPageSize = seInstance->seConfig.pageSize * DB_KIBI;
    uint8_t hashCollisionRate = 0;
    if (ht->hashInsertCnt != 0u) {
        hashCollisionRate = (uint8_t)((ht->hashCollisionCnt * DB_PERCENTAGE_BASE) / ht->hashInsertCnt);
    }

    IndexHashPerfStatT indexHashPerfStat = {
        .pageCount = pageCount,
        .perPageSize = perPageSize,
        .pageSize = (uint64_t)perPageSize * pageCount,
        .usedMemSize = (uint64_t)ccehMeta->dir.dirCap * sizeof(HashDirSegmentT) +
                       (uint64_t)ht->entryUsed * ccehMeta->hashEntrySize,
        .hashInsertCnt = ht->hashInsertCnt,
        .hashCollisionCnt = ht->hashCollisionCnt,
        .hashCollisionRate = hashCollisionRate,
    };
    idxStat->hashIndex.indexHashPerfStat = indexHashPerfStat;

    // entryCapacity : the number of segment page * hashEntryNumPerPage
    idxStat->hashIndex.entryCapacity = ccehMeta->segPageCount * ccehMeta->hashEntryNumPerPage;
    idxStat->hashIndex.entryUsed = ht->entryUsed;
    idxStat->hashIndex.segmentCnt = ccehMeta->segPageCount;
    idxStat->hashIndex.scaleInCnt = ccehMeta->scaleInCnt;
    idxStat->hashIndex.cacheLineLen = ccehMeta->hashMaxProbeLen;
    idxStat->hashIndex.mvPageCnt = HashIndexIsMultiVersion(ht) ? ht->mvPageInfo.mvPageUsedCnt : 0;
    DbRWUnlatchR(&ht->idxBase.idxLatch);
    return GMERR_OK;
}

Status HashIndexViewGetPageSize(ShmemPtrT idxShmAddr, uint16_t instanceId, uint64_t *idxPageSize)
{
    DB_POINTER(idxPageSize);

    HashTableT *ht = DbShmPtrToAddr(idxShmAddr);
    if (ht == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "SE-Hash, HIVGPS: Hash table is novalid");
        return GMERR_INTERNAL_ERROR;
    }
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(instanceId);
    if (seInstance == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "SE-Hash, HIVGPS: hash's SE Instance is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint64_t perPageSize = (uint64_t)seInstance->seConfig.pageSize * DB_KIBI;
    DbRWLatchR(&ht->idxBase.idxLatch);
    uint64_t pageCount = (uint64_t)ht->memMgr.ccehMeta.segPageCount + (uint64_t)ht->memMgr.ccehMeta.dirPageCount;
    DbRWUnlatchR(&ht->idxBase.idxLatch);
    *idxPageSize = perPageSize * pageCount;
    return GMERR_OK;
}

Status HashIndexVerifyAndConstructHashTable(SeRunCtxHdT seRunCtx, HashTableT *ht)
{
    DB_POINTER(ht);
    IdxBaseT *idxBase = &ht->idxBase;
    if (!IdxIsConstructed(idxBase)) {
        // if false is returned, the hash table is locked.
        if (IdxWLockIfNotConstructed(idxBase)) {
            return GMERR_OK;
        }
        Status ret = HashCreateHashTable(seRunCtx, ht, idxBase->indexCfg.indexCap);
        if (ret != GMERR_OK) {
            DbRWUnlatchW(&idxBase->idxLatch);
            return ret;
        }
        IdxConstructedFinish(idxBase);
        DbRWUnlatchW(&idxBase->idxLatch);
    }
    return GMERR_OK;
}

void HashIndexAMInit(void)
{
    IdxFuncT hashIndexFuncHandle = IdxEmptyIdxFunc();
    hashIndexFuncHandle.idxCreate = HashIndexCreate;
    hashIndexFuncHandle.idxDrop = HashIndexDrop;
    hashIndexFuncHandle.idxTruncate = HashIndexTruncate;
    hashIndexFuncHandle.idxOpen = HashIndexOpen;
    hashIndexFuncHandle.idxClose = HashIndexClose;
    hashIndexFuncHandle.idxInsert = HashIndexInsert;
    hashIndexFuncHandle.idxBatchInsert = HashIndexBatchInsert;
    hashIndexFuncHandle.idxDelete = HashIndexDelete;
    hashIndexFuncHandle.idxBatchDelete = HashIndexBatchDelete;
    hashIndexFuncHandle.idxUpdate = HashIndexUpdate;
    hashIndexFuncHandle.idxBatchUpdate = HashIndexBatchUpdate;
    hashIndexFuncHandle.idxLookup = HashIndexLookup;
    hashIndexFuncHandle.idxBatchLookup = HashIndexBatchLookup;
    hashIndexFuncHandle.idxBeginScan = HashIndexBeginScan;
    hashIndexFuncHandle.idxScan = HashIndexScan;
    hashIndexFuncHandle.idxUndoInsert = HashIndexUndoInsert;
    hashIndexFuncHandle.idxUndoRemove = HashIndexUndoRemove;
    hashIndexFuncHandle.idxUndoUpdate = HashIndexUndoUpdate;
    hashIndexFuncHandle.idxGetKeyCount = HashIndexGetKeyCount;
    hashIndexFuncHandle.idxStatView = HashIndexStatView;
    hashIndexFuncHandle.idxGetPageSize = HashIndexViewGetPageSize;
    hashIndexFuncHandle.idxScaleIn = HashIndexScaleIn;
    hashIndexFuncHandle.idxGetEstimateMemSize = HashIndexGetEstimateMemSize;
    hashIndexFuncHandle.idxGetCtxSize = HashIndexGetCtxSize;
    IdxAmFuncRegister((uint8_t)HASH_INDEX, &hashIndexFuncHandle);
}

#ifdef __cplusplus
}
#endif
