/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2024. All rights reserved.
 * File Name: se_hac_common.c
 * Description: hac common
 * Author: lijianchuan
 * Create: 2022/10/17
 */

#ifdef FEATURE_HAC
#include "se_hac_common.h"

#include "adpt_sleep.h"
#include "db_mem_context.h"
#include "se_capacity_def_inner.h"
#include "db_dyn_load.h"
#include "se_device.h"
#include "adpt_io.h"

#define HAC_MEM_PATH "/dev/mem"
#define HAC_SVM_PATH "/dev/svm0"

#define RESP_SIZE (uint32_t)sizeof(uint8_t)
#define TUPLE_ADDR_SIZE64 (uint32_t)sizeof(HpTupleAddr)
#define TUPLE_ADDR_SIZE32 (uint32_t)sizeof(TuplePointer32T)
static_assert(TUPLE_ADDR_SIZE32 == 4, "TuplePointer32T must be 4 bytes");
static_assert(TUPLE_ADDR_SIZE64 == 8, "HpTupleAddr must be 8 bytes");
#define HAC_SLEEP_TIMEOUT_US 1000u
#define HAC_SLEEP_TIME_US 1u
#define HAC_SLEEP_TIMES (HAC_SLEEP_TIMEOUT_US / HAC_SLEEP_TIME_US)
#define RW_LOCK_SIZE (uint32_t)sizeof(DbLatchT)

#define DB_REGIONS_BASE_ADDR (0x203000000)
#define DB_REGIONS_SIZE (0x100000)
#define SVM_IOCTL_PROCESS_BIND 0xffff
#define SVM_STREAM_ID 0x7fa0

#define HAC_MAX_BATCH_REQ 32

// 加速器初始化参数
#define HAC_MAX_LIST_SIZE 2  // 最大挂链数，1~10，默认值2

// hash函数参数
#define HAC_HASH_SEED 10  // hash函数seed
#define HAC_HASH_CONST_1 0x9E3779B1U
#define HAC_HASH_CONST_2 0x85EBCA77U
#define HAC_HASH_CONST_3 0xC2B2AE3DU
#define HAC_HASH_CONST_4 0x27D4EB2FU
#define HAC_HASH_CONST_5 0x165667B1U

// SVM(Shared Virtual Memory)信息
typedef struct {
    int32_t pid;  // 进程id
    int32_t reserved1[5];
    int32_t pasId;  // process addr space id, 由ioctl生成，传给HacDriverSmmuInfoT的subStreamId
    int32_t reserved2[3];
} HacSvmInfoT;

typedef struct HacBatchPara {
    IndexKeyT *idxKeys;
    HpBatchOutT *addrs;
    IdxBatchLookupParaT *para;
} HacBatchParaT;

typedef struct HacRespPara {
    HacStatusInterE hacStatus;
    IndexKeyT idxKey;
    HpTupleAddr addr;
    IdxBatchLookupParaT *lookupPara;
} HacRespParaT;

// addr强转，将virtual memory addr写入报文,仅支持64位内存
inline static uint64_t HacAddrToUInt64(uint8_t *addr)
{
#if defined(__aarch64__) || defined(__arm64__)
    return (uint64_t)(uintptr_t)addr;
#else
#error "unsupported cpu arch"
#endif
}

HacMgrCtxT g_hacMgrCtx = {0};

inline HacMgrT *GetHacMgr(void)
{
    DB_POINTER(g_hacMgrCtx.hacMgr);
    return g_hacMgrCtx.hacMgr;
}

inline DbMemCtxT *GetHacMemCtx(void)
{
    DB_POINTER(g_hacMgrCtx.hacMemCtx);
    return g_hacMgrCtx.hacMemCtx;
}

inline HacMemMgrT *GetHacMemMgr(void)
{
    DB_POINTER(g_hacMgrCtx.hacMemMgr);
    return g_hacMgrCtx.hacMemMgr;
}

HacStatisticsT *HacGetStatsImpl(void)
{
    return &GetHacMgr()->hacStats;
}

inline bool HacIsTupleMode64(void)
{
    DB_POINTER(g_hacMgrCtx.hacMgr);
    return g_hacMgrCtx.hacMgr->tupleAddrMode == HAC_DRIVER_TUPLE_ADDR_WIDTH_64BIT;
}

inline bool HacIsAcceleratorMode(void)
{
    DB_POINTER(g_hacMgrCtx.hacMgr);
    return g_hacMgrCtx.hacMgr->hacMode == ENABLE_HAC_WITH_ACCELERATOR;
}

inline bool IsHacInitializedImpl(void)
{
    return g_hacMgrCtx.hacMgr != NULL;
}

inline static uint32_t HacGetTupleAddrSize(void)
{
    DB_POINTER(g_hacMgrCtx.hacMgr);
    return g_hacMgrCtx.hacMgr->tupleAddrSize;
}

inline static bool IsBatchTypeHacHashLookupByHac(HacBatchTypeE batchtype)
{
    return (batchtype & HAC_OP_MASK) == HAC_HASH_LOOKUP_OP;
}

bool GetHacBatchExectueToken(void)
{
    uint32_t tokenId = DbAtomicInc(&GetHacMgr()->tokenCnt);
    if (tokenId >= HAC_MAX_BATCH_REQ) {
        (void)DbAtomicDec(&GetHacMgr()->tokenCnt);
        return false;
    }
    return true;
}

void ReleaseHacBatchExectueToken(void)
{
    (void)DbAtomicDec(&GetHacMgr()->tokenCnt);
}

static StatusInter HacMgrAlloc(DbMemCtxT *memCtx, ShmemPtrT *pHacMgrShmPtr, HacMgrT **pHacMgr)
{
    *pHacMgrShmPtr = DbShmemStructAllocById(memCtx, sizeof(HacMgrT), DB_SE_HAC_MGR_ID);
    *pHacMgr = (HacMgrT *)DbShmPtrToAddr(*pHacMgrShmPtr);
    if (*pHacMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "hac Mgr ptr is null, Addr:%" PRIu32 ",%" PRIu32 "!",
            pHacMgrShmPtr->segId, pHacMgrShmPtr->offset);
        DbShmemStructFreeById(memCtx, *pHacMgrShmPtr, DB_SE_HAC_MGR_ID);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    memset_s(*pHacMgr, sizeof(HacMgrT), 0, sizeof(HacMgrT));
    return STATUS_OK_INTER;
}

static StatusInter HacMgrInit(DbMemCtxT *memCtx, ShmemPtrT *pHacMgrShmPtr, HacMgrT **pHacMgr)
{
    StatusInter ret = STATUS_OK_INTER;

    if ((ret = HacMgrAlloc(memCtx, pHacMgrShmPtr, pHacMgr)) != STATUS_OK_INTER) {
        return ret;
    }

    HacMgrT *hacMgr = *pHacMgr;
    bool isLiteTrx = DbCfgGetBoolLite(DB_CFG_IS_FAST_READ_UNCOMMITTED, NULL);
    bool isLabelLatchMode = DbCfgGetBoolLite(DB_CFG_TABLE_LOCK_IS_ENABLED, NULL);
    ConcurrencyControlE ccType = isLabelLatchMode ? CONCURRENCY_CONTROL_LABEL_LATCH : CONCURRENCY_CONTROL_READ_UNCOMMIT;
    hacMgr->ccType = isLiteTrx ? ccType : CONCURRENCY_CONTROL_NORMAL;

    int32_t deviceSize = DbCfgGetInt32Lite(DB_CFG_SE_DEV_SIZE, NULL);
    int32_t pageSize = DbCfgGetInt32Lite(DB_CFG_SE_PAGE_SIZE, NULL);
    hacMgr->chunkCntPerDev = (uint32_t)deviceSize * DB_KIBI / (uint32_t)pageSize;
    hacMgr->pageSize = (uint32_t)pageSize * DB_KIBI;

    int32_t shmSize = DbCfgGetInt32Lite(DB_CFG_SERVER_TOTAL_SHM_SIZE, NULL);
    int32_t seMaxSize = DbCfgGetInt32Lite(DB_CFG_SE_MAX_MEM, NULL);
    hacMgr->hashMaxMemSize = (uint32_t)DB_MIN(shmSize, seMaxSize);

    hacMgr->devMaxCount = (uint32_t)(seMaxSize / deviceSize);
    uint32_t size = hacMgr->pageSize;
    while (size != 1) {
        size = size >> 1;
        hacMgr->pageSizeShiftBit++;
    }
    size = hacMgr->chunkCntPerDev;
    while (size != 1) {
        size = size >> 1;
        hacMgr->addrShiftBit++;
    }

    hacMgr->tupleShiftBit32 = SE_PAGE_ID_BIT_FIELD_32;
    hacMgr->tupleShiftBit = DB_32BIT;

    hacMgr->multiHashBucketCnt = (uint32_t)DbCfgGetInt32Lite(DB_CFG_MULTI_HASH_BUCKET_CNT, NULL);
    if (hacMgr->multiHashBucketCnt < MULTI_HASH_MIN_SIZE) {
        hacMgr->multiHashBucketCnt = MULTI_HASH_MIN_SIZE;
    }
    hacMgr->tupleAddrSize =
        hacMgr->tupleAddrMode == HAC_DRIVER_TUPLE_ADDR_WIDTH_32BIT ? TUPLE_ADDR_SIZE32 : TUPLE_ADDR_SIZE64;
    (void)memset_s(&hacMgr->hacStats, sizeof(HacStatisticsT), 0, sizeof(HacStatisticsT));

    DbSpinInit(&hacMgr->sendMsgLock);
    DbSpinInit(&hacMgr->receiveMsgLock);
    return STATUS_OK_INTER;
}

// 仅供server初始化调用
Status HacInitIteratorAddr(HacCtxT *hacCtx)
{
    uint32_t iteratorLen = hacCtx->iteratorSize * hacCtx->reqNum;
    if (iteratorLen > hacCtx->iteratorLen) {
        if (hacCtx->iteratorAddr != NULL) {
            HashFreeDynAddr(hacCtx->memCtx, hacCtx->iteratorAddr);
        }
        hacCtx->iteratorAddr = HashAllocDynAddr(hacCtx->memCtx, iteratorLen);
        if (hacCtx->iteratorAddr == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "|Se-Hac|: Alloc tuplesAddr unsucc");
            hacCtx->iteratorLen = 0;
            return GMERR_OUT_OF_MEMORY;
        }
        hacCtx->iteratorLen = iteratorLen;
    }
    // 可以保证置位一定不会报错
    (void)memset_s(hacCtx->iteratorAddr, iteratorLen, 0, iteratorLen);
    return GMERR_OK;
}

// 以下函数调用的adapt层函数不会设置last_error，因此这里统一设置last_error
// 包括HacDriverInitAndOpen、HacConfigSet、HacAddrModeSet、HacCallBackSet、HacHashFuncSet、HacInjectMemory

// 驱动初始化
Status HacDriverInitAndOpen(void)
{
    int32_t fileFd = DB_INVALID_FD;
    Status ret = DbOpenFile(HAC_MEM_PATH, O_RDWR | O_SYNC, 0, &fileFd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: Open file worthless, os ret no: %" PRId32 ".", (int32_t)errno);
        return ret;
    }
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    uint8_t *vir_base =
        ioCtx->mmap(NULL, DB_REGIONS_SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, fileFd, DB_REGIONS_BASE_ADDR);
    if (vir_base == MAP_FAILED) {
        DbCloseFile(fileFd);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FILE_OPERATE_FAILED, "|Se-Hac|: mmap file unsucc");
        return GMERR_FILE_OPERATE_FAILED;
    }

    HacDriverCtxT driverCtx = {.baseAddr = vir_base, .size = DB_REGIONS_SIZE};
    Handle hacHandle = DbHacDriverOpen(driverCtx);
    DbCloseFile(fileFd);

    if (hacHandle == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "|Se-Hac|: driver open unsucc");
        return GMERR_INTERNAL_ERROR;
    }

    ret = DbHacDriverInit();
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: driver init unsucc");
        (void)DbHacDriverClose();
        return ret;
    }
    return GMERR_OK;
}

// 部分配置参数设置
Status HacConfigSet(void)
{
    Status ret = DbHacDriverSetMaxListLen(HAC_MAX_LIST_SIZE);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: SetMaxListLen unsucc");
        return ret;
    }

    ret = DbHacDriverSetListDeleteMode(HAC_DRIVER_NO_DELE);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: SetListDeleteMode unsucc");
        return ret;
    }

    ret = DbHacDriverSetBdFifoEnable();
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: SetBdFifoEnable unsucc");
        return ret;
    }
    return GMERR_OK;
}

// addrmode设置及smmu使能
Status HacAddrModeSet(void)
{
    Status ret = DbHacDriverSetAddrMode();
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: SetAddrMode unsucc");
        return ret;
    }
    int32_t fileFd = DB_INVALID_FD;
    ret = DbOpenFile(HAC_SVM_PATH, O_RDWR | O_SYNC, 0, &fileFd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: Open file worthless, osno: %" PRId32 ".", (int32_t)errno);
        return ret;
    }

    HacSvmInfoT bind = {0};
    bind.pid = (int32_t)DbAdptGetpid();
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t innerRet = ioCtx->ioctl(fileFd, SVM_IOCTL_PROCESS_BIND, &bind);
    DbCloseFile(fileFd);
    if (innerRet == -1) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_INTERNAL_ERROR, "|Se-Hac|: ioctl file worthless, osno: %" PRId32 ".", (int32_t)errno);
        return GMERR_INTERNAL_ERROR;
    }

    HacDriverSmmuInfoT info;
    info.streamId = SVM_STREAM_ID;
    info.subStreamIdEn = 1;
    info.subStreamId = (uint32_t)bind.pasId;
    DB_LOG_INFO("|Se-Hac|: Smmu set pasid %" PRIu32, info.subStreamId);
    ret = DbHacDriverSetSmmuEnable(&info);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: SetSmmuEnable unsucc");
        return ret;
    }
    return GMERR_OK;
}

// 索引回调函数相关配置参数设置
Status HacCallBackSet(HacMgrT *hacMgr)
{
    // 设置tuple addr映射表基址
    uint8_t **virAddr = DevGetEntryHac();
    if (SECUREC_UNLIKELY(virAddr == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-Hac|: find device cache null");
        // debug某些场景不会初始化device，可能为空
#ifdef NDEBUG
        return GMERR_UNEXPECTED_NULL_VALUE;
#endif
    }
    Status ret = DbHacDriverSetDevAddr((uint64_t)(uintptr_t)virAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: SetDevAddr unsucc");
        return ret;
    }

    ret = DbHacDriverSetTupleAddrWidth(hacMgr->tupleAddrMode);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: SetTupleAddrWidth unsucc");
        return ret;
    }

    HacDriverAddrShiftOffsetT offset = {
        .blockIdShift = hacMgr->addrShiftBit,
        .blockSizeShift = hacMgr->pageSizeShiftBit,
        .slotIdShift = HacIsTupleMode64() ? DB_32BIT : SE_TUPLE_SLOT_ID_BIT_FIELD,
    };
    ret = DbHacDriverSetLogicAddrOffset(offset);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: SetLogicAddrOffset unsucc");
        return ret;
    }

    ret = DbHacDriverSetDevMaxCnt(hacMgr->devMaxCount);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: setDevMaxCnt unsucc");
        return ret;
    }
    return GMERR_OK;
}

// hash函数相关设置
Status HacHashFuncSet(void)
{
    Status ret = DbHacDriverSetHashAlgMode(HAC_DRIVER_ALG_XXHASH);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: SetHashAlgMode unsucc");
        return ret;
    }

    ret = DbHacDriverSetHashSeed(HAC_HASH_SEED);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: SetHashSeed unsucc");
        return ret;
    }

    HacDriverHashConstT hashConst = {
        .prime1 = HAC_HASH_CONST_1,
        .prime2 = HAC_HASH_CONST_2,
        .prime3 = HAC_HASH_CONST_3,
        .prime4 = HAC_HASH_CONST_4,
        .prime5 = HAC_HASH_CONST_5,
    };
    ret = DbHacDriverSetHashConst(hashConst);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: SetHashConst unsucc");
        return ret;
    }
    return GMERR_OK;
}

// 注入一块128KB内存
Status HacInjectMemory(HacMgrT *hacMgr)
{
    HacMemMgrT *hacMemMgr = GetHacMemMgr();

    ShmemPtrT blockShmPtr;
    DbMemCtxT *memCtx = GetHacMemCtx();
    HacBucketT *block = SeShmAllocAlign(memCtx, HAC_BLOCK_SIZE, &blockShmPtr, HAC_BLOCK_SIZE);
    if (block == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "|Se-Hac|: Alloc block unsucc");
        return GMERR_OUT_OF_MEMORY;
    }
    for (uint32_t i = 0u; i < HAC_BLOCK_BUCKET_NUM; i++) {
        block[i].nextBucket = HAC_INVALID_LOGICADDR;
    }
    uint32_t blockId = DB_INVALID_UINT32;
    Status ret = DbHacDriverAddBlock((uint64_t)(uintptr_t)block, &blockId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: Add block unsucc");
        DbShmemCtxFree(memCtx, blockShmPtr);
        return ret;
    }

    hacMemMgr->blockMgr[blockId].block = blockShmPtr;
    hacMemMgr->blockMgr[blockId].type = HAC_BLOCK_USED;
    DbAtomicInc8(&HacGetStatsImpl()->allocHacBlockCnt);
    return GMERR_OK;
}

void HacRecycleMemory(HacMgrT *hacMgr)
{
    uint64_t addr = DB_MAX_UINT64;
    uint32_t blockId = DB_MAX_UINT32;
    Status ret = DbHacDriverDelBlock((void *)&addr, &blockId);
    if (ret != GMERR_OK) {  // 回收加速器内存失败说明没有内存需要回收，直接返回即可(不设置lasterr)
        return;
    }
    HacMemMgrT *hacMemMgr = GetHacMemMgr();
    if (hacMemMgr->blockMgr[blockId].type != HAC_BLOCK_USED) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "|Se-Hac|: BlockId is not used by software");
        return;
    }
    HashShmAddrFree(GetHacMemCtx(), hacMemMgr->blockMgr[blockId].block);
    hacMemMgr->blockMgr[blockId].type = HAC_BLOCK_UN_ALLOCATED;
    hacMemMgr->blockMgr[blockId].block = HAC_INVALID_SHMPTR;
    DbAtomicDec8(&HacGetStatsImpl()->allocHacBlockCnt);
}

inline HacLogicAddressT HacAllocMemFromAccelerator(void)
{
    uint64_t hacAddr = DB_INVALID_UINT64;
    Status ret = DbHacDriverAllocBuffer(&hacAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: Alloc buffer unsucc");
        return HAC_INVALID_LOGICADDR;
    }
    return (HacLogicAddressT){
        .blockId = hacAddr >> HAC_BLOCK_SHIFT_BITS, .bucketId = hacAddr & (HAC_BLOCK_BUCKET_NUM - 1)};
}

inline void HacFreeMemToAccelerator(HacLogicAddressT addr)
{
    if (!IsHacLogicAddrValidForAcc(addr)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE,
            "|Se-Hac|: Unsucc to free mem to acc, (blockId: %" PRIu32 ", bucketId: %" PRIu32 ").", addr.blockId,
            addr.bucketId);
        return;
    }
    uint64_t hacAddr = (addr.blockId << HAC_BLOCK_SHIFT_BITS) | addr.bucketId;
    (void)DbHacDriverFreeBuffer(hacAddr);
}

Status HacAcceleratorInit(HacMgrT *hacMgr)
{
    Status ret = DbLoadHacDriverSo();
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = HacDriverInitAndOpen();
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = HacConfigSet();
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = HacAddrModeSet();
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = HacCallBackSet(hacMgr);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = HacHashFuncSet();
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = HacInjectMemory(hacMgr);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = 1; i < ACCELERATOR_MAX_BLOCK_NUM; i++) {
        ret = HacInjectMemory(hacMgr);
        if (ret != GMERR_OK) {
            // HacInjectMemory保证至少注入一个内存块即可，后续注入失败可以不报错，直接返回OK
            return GMERR_OK;
        }
    }

    return GMERR_OK;
}

static StatusInter HacMemMgrInit(ShmemPtrT *hacMemMgrPtr, DbMemCtxT *memCtx)
{
    HacMemMgrT *hacMemMgr = (HacMemMgrT *)HashAllocShmAddr(memCtx, (uint32_t)sizeof(HacMemMgrT), hacMemMgrPtr);
    if (hacMemMgr == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_MEM_FAILED, "|Se-Hac|: Unable to alloc hacMemMgr");
        return OUT_OF_MEMORY_MEM_FAILED;
    }
    DbSpinInit(&hacMemMgr->hacMemLock);
    for (uint32_t i = 0; i < HAC_MAX_BLOCK_NUM; i++) {
        hacMemMgr->blockMgr[i].type = HAC_BLOCK_UN_ALLOCATED;
        hacMemMgr->blockMgr[i].block = DB_INVALID_SHMPTR;
    }
    return STATUS_OK_INTER;
}

static StatusInter HacLockAllocAndCfgSet(SeInstanceT *seInsPtr, DbMemCtxT *memCtx, HacMgrT *hacMgr)
{
    uint32_t lockCount = ACCELERATOR_MAX_POOL_NUM * ACCELERATOR_MAX_LATCH_NUM;
    DbLatchT *lock = (DbLatchT *)HashAllocShmAddr(memCtx, lockCount * RW_LOCK_SIZE, &hacMgr->lockShm);
    if (lock == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_MEM_FAILED, "|Se-Hac|: Unable to alloc lock");
        return OUT_OF_MEMORY_MEM_FAILED;
    }
    (void)memset_s(lock, lockCount * RW_LOCK_SIZE, 0, lockCount * RW_LOCK_SIZE);

    for (uint32_t i = 0; i < lockCount; i++) {
        DbRWLatchInit(&lock[i]);
    }
    if (seInsPtr != NULL) {  // 当前仅支持32bits和64bits
        hacMgr->tupleAddrMode = (seInsPtr->seConfig.heapTupleAddrMode == SE_HEAP_TUPLE_ADDR_32) ?
                                    HAC_DRIVER_TUPLE_ADDR_WIDTH_32BIT :
                                    HAC_DRIVER_TUPLE_ADDR_WIDTH_64BIT;
    } else {  // 默认32bits
        hacMgr->tupleAddrMode = HAC_DRIVER_TUPLE_ADDR_WIDTH_32BIT;
    }
    return STATUS_OK_INTER;
}

StatusInter HacMgrCtxInitImpl(SeInstanceT *seInsPtr, DbMemCtxT *memCtx, uint32_t hacMode)
{
    ShmemPtrT hacMgrShmPtr = {0};
    HacMgrT *hacMgr = NULL;
    StatusInter ret = HacMgrInit(memCtx, &hacMgrShmPtr, &hacMgr);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    hacMgr->hacMode = (HacModeE)hacMode;

    if ((ret = HacLockAllocAndCfgSet(seInsPtr, memCtx, hacMgr)) != STATUS_OK_INTER) {
        goto EXIT2;
    }

    if ((ret = HacMemMgrInit(&hacMgr->hacMemMgr, memCtx)) != STATUS_OK_INTER) {
        SE_ERROR(ret, "|Se-Hac|: Unable to init hacMemMgr");
        goto EXIT2;
    }

    if (hacMgr->hacMode == ENABLE_HAC_WITH_ACCELERATOR) {  // 硬件卸载是加速器模式
        // 加速器初始化
        ret = DbGetStatusInterErrno(HacAcceleratorInit(hacMgr));
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "|Se-Hac|: Unable to init hac accelerator");
            goto EXIT1;
        }
    }

    g_hacMgrCtx.hacMgr = hacMgr;
    g_hacMgrCtx.hacMemCtx = memCtx;
    g_hacMgrCtx.hacMemMgr = HashGetShmAddr(hacMgr->hacMemMgr);
    DB_LOG_INFO("|Se-Hac|: Hac Mgr init with %s", HacIsTupleMode64() ? "64 bits" : "32 bits");

    return STATUS_OK_INTER;
EXIT1:
    DbShmemCtxFree(memCtx, hacMgr->hacMemMgr);
EXIT2:
    DbShmemStructFreeById(memCtx, hacMgrShmPtr, DB_SE_HAC_MGR_ID);
    return ret;
}

Status CltHacMgrCtxInitImpl(uint16_t instanceId)
{
    HacMgrT *hacMgr = (HacMgrT *)DbGetShmemStructById(DB_SE_HAC_MGR_ID, instanceId);
    if (SECUREC_UNLIKELY(hacMgr == NULL)) {  // 如果hacMode=0,或者开启了聚簇容器，硬件卸载不开启，hacMgr为null
        DB_LOG_INFO("|Se-Hac|: Hac is disabled.");
        return GMERR_OK;
    }

    SeInstanceT *seInstance = SeGetInstance(instanceId);
    if (seInstance == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-Hac|: SE instance is null");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbMemCtxT *hacMemCtx = DbGetShmemCtxById(seInstance->hacShmMemCtxId, instanceId);
    if (hacMemCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-Hac|: Hac mem context is null");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    g_hacMgrCtx.hacMgr = hacMgr;
    g_hacMgrCtx.hacMemCtx = hacMemCtx;
    g_hacMgrCtx.hacMemMgr = HashGetShmAddr(hacMgr->hacMemMgr);
    return GMERR_OK;
}

// 申请和注入一个block

Status HacOpen(IndexCtxT *idxCtx, DbMemCtxT *memCtx)
{
    DB_POINTER2(idxCtx, idxCtx->idxRunCtx);
    if (idxCtx->idxOpenCfg.seRunCtx && (idxCtx->idxOpenCfg.seRunCtx->resSessionCtx.isDirectRead ||
                                           idxCtx->idxOpenCfg.seRunCtx->resSessionCtx.isDirectWrite)) {
        return GMERR_OK;
    }

    HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
    *hacCtx = (HacCtxT){0};
    hacCtx->callId = idxCtx->idxMetaCfg.indexId;
    hacCtx->indexCtx = idxCtx;
    hacCtx->idxType = idxCtx->idxMetaCfg.idxType;
    hacCtx->memCtx = memCtx;
    if (hacCtx->idxType == MULTI_HASH_INDEX) {
        DbCreateList(&hacCtx->list, sizeof(MultiHashDMLParaT), hacCtx->memCtx);
    } else if (GetHacMgr()->ccType != CONCURRENCY_CONTROL_NORMAL) {  // hachash轻量化事务，期望走硬化keyCmp逻辑
        idxCtx->isKeyCmpByHac = true;
    }
    return GMERR_OK;
}

void HacClose(IndexCtxT *idxCtx)
{
    HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
    if (hacCtx->reqBodyAddr != NULL) {
        HashFreeDynAddr(hacCtx->memCtx, hacCtx->reqBodyAddr);
        hacCtx->reqBodyAddr = NULL;
        hacCtx->reqBodyLen = 0;
    }
    if (hacCtx->respBodyAddr != NULL) {
        HashFreeDynAddr(hacCtx->memCtx, hacCtx->respBodyAddr);
        hacCtx->respBodyAddr = NULL;
        hacCtx->respBodyLen = 0;
    }
    if (hacCtx->tuplesAddr != NULL) {
        HashFreeDynAddr(hacCtx->memCtx, hacCtx->tuplesAddr);
        hacCtx->tuplesAddr = NULL;
        hacCtx->tupleAddrLen = 0;
    }
    return;
}

void AccLatchR(const IndexCtxT *idxCtx, uint32_t latchPoolId, uint32_t latchId)
{
#ifndef ACCELERATOR_MODE
    ShmemPtrT lockShm = GetHacMgr()->lockShm;
    DbLatchT *latch = (DbLatchT *)HashGetShmAddr(lockShm);
    if (latch == NULL) {
        return;
    }
    uint32_t offset = latchPoolId * ACCELERATOR_MAX_LATCH_NUM + latchId;
    if (idxCtx->idxOpenCfg.seRunCtx->resSessionCtx.isDirectRead) {
        LatchStackItemT latchStack = {
            {
                .virtAddr = (void *)(latch + offset),
                .shmAddr = {.offset = lockShm.offset + sizeof(DbLatchT) * offset, .segId = lockShm.segId},
            },
            .latchAcqMode = LATCH_ACQ_READ,
            .latchType = LATCH_ADDR_ACCELERATOR_LOCK,
        };
        DbAccRLockWithSession(&idxCtx->idxOpenCfg.seRunCtx->resSessionCtx, &latchStack);
    } else {
        DbRWLatchR(latch + offset);
    }
#else
    if (idxCtx->idxOpenCfg.seRunCtx->resSessionCtx.isDirectRead) {
        LatchStackItemT latchStack = {
            {
                .latchPoolId = latchPoolId,
                .latchId = latchId,
            },
            .latchAcqMode = LATCH_ACQ_READ,
            .latchType = LATCH_ADDR_ACCELERATOR_LOCK,
        };
        DbAccRLockWithSession(&idxCtx->idxOpenCfg.seRunCtx->resSessionCtx, &latchStack);
    } else {
        DbAccLatchR(latchPoolId, latchId);
    }
#endif
}

void AccUnlatchR(const IndexCtxT *idxCtx, uint32_t latchPoolId, uint32_t latchId)
{
#ifndef ACCELERATOR_MODE
    DbLatchT *latch = (DbLatchT *)HashGetShmAddr(GetHacMgr()->lockShm);
    if (latch == NULL) {
        return;
    }
    uint32_t offset = latchPoolId * ACCELERATOR_MAX_LATCH_NUM + latchId;
    if (idxCtx->idxOpenCfg.seRunCtx->resSessionCtx.isDirectRead) {
        DbAccRUnlockWithSession(&idxCtx->idxOpenCfg.seRunCtx->resSessionCtx, latch + offset);
    } else {
        DbRWUnlatchR(latch + offset);
    }
#else
    if (idxCtx->idxOpenCfg.seRunCtx->resSessionCtx.isDirectRead) {
        DbAccRUnlockWithSession(&idxCtx->idxOpenCfg.seRunCtx->resSessionCtx, NULL);
    } else {
        DbAccUnlatchR(latchPoolId, latchId);
    }
#endif
}

inline void AccLatchW(uint32_t latchPoolId, uint32_t latchId)
{
#ifndef ACCELERATOR_MODE
    DbLatchT *latch = (DbLatchT *)HashGetShmAddr(GetHacMgr()->lockShm);
    if (latch == NULL) {
        return;
    }
    uint32_t offset = latchPoolId * ACCELERATOR_MAX_LATCH_NUM + latchId;
    DbRWLatchW(latch + offset);
#else
    DbAccLatchW(latchPoolId, latchId);
#endif
}

inline bool AccLatchTryW(uint32_t latchPoolId, uint32_t latchId)
{
#ifndef ACCELERATOR_MODE
    DbLatchT *latch = (DbLatchT *)HashGetShmAddr(GetHacMgr()->lockShm);
    if (latch == NULL) {
        return false;
    }
    uint32_t offset = latchPoolId * ACCELERATOR_MAX_LATCH_NUM + latchId;
    return DbRWLatchTryW(latch + offset);
#else
    DbAccLatchW(latchPoolId, latchId);
    return true;
#endif
}

inline void AccUnlatchW(uint32_t latchPoolId, uint32_t latchId)
{
#ifndef ACCELERATOR_MODE
    DbLatchT *latch = (DbLatchT *)HashGetShmAddr(GetHacMgr()->lockShm);
    if (latch == NULL) {
        return;
    }
    uint32_t offset = latchPoolId * ACCELERATOR_MAX_LATCH_NUM + latchId;
    DbRWUnlatchW(latch + offset);
#else
    DbAccUnlatchW(latchPoolId, latchId);
#endif
}

Status HacSendMsg(HacCtxT *hacCtx, uint32_t reqLen)
{
    hacCtx->reqMes.addr = HacAddrToUInt64(hacCtx->reqBodyAddr);
    DB_ASSERT(reqLen <= DB_INVALID_UINT16);
    hacCtx->reqMes.len = (uint16_t)reqLen;
    HacDriverRequestBodyT req = {.bdAddr = (void *)&hacCtx->reqMes, .bdNum = 1};
    uint32_t submitNum;
    DbSpinLock(&GetHacMgr()->sendMsgLock);
    Status ret = DbHacDriverAddRequestToBdFifo(&req, &submitNum);
    DbSpinUnlock(&GetHacMgr()->sendMsgLock);
    return ret;
}

Status HacReceiveMsg(HacCtxT *hacCtx)
{
    // 同步场景先sleep一次再去get
    if (!hacCtx->indexCtx->isHacBatchAsync) {
        DbUsleep(HAC_SLEEP_TIME_US);
    }
    uint32_t loopCnt = 0u;
    HacDriverResponseBodyT req = {
        .bdAddr = (void *)&hacCtx->respMes, .tid = &hacCtx->callId, .bdNum = 1, .completedNum = 0};
    do {
        DbSpinLock(&GetHacMgr()->receiveMsgLock);
        Status ret = DbHacDriverGetResponseFromBdFifo(&req);
        DbSpinUnlock(&GetHacMgr()->receiveMsgLock);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: GetResponseFromBdFifo unsucc");
            return ret;
        }
        if (hacCtx->respMes.ack == 0) {
            return GMERR_OK;
        }
        DbUsleep(HAC_SLEEP_TIME_US);
    } while (loopCnt++ < HAC_SLEEP_TIMES);
    // 批处理1ms内超时
    DB_LOG_ERROR_AND_SET_LASTERR(GMERR_REQUEST_TIME_OUT, "|Se-Hac| resv message over time");
    return GMERR_REQUEST_TIME_OUT;
}
// 以上为软件仿真模拟方案，最后需要根据芯片提供的接口适配

// 查看批量上一次缓存的内存空间是否足够使用，足够则复用。
Status HacGetBodyAddr(HacCtxT *hacCtx, uint32_t reqBodyLen, uint32_t respBodyLen)
{
    if (reqBodyLen > hacCtx->reqBodyLen) {
        if (hacCtx->reqBodyAddr != NULL) {
            HashFreeDynAddr(hacCtx->memCtx, hacCtx->reqBodyAddr);
        }
        hacCtx->reqBodyAddr = HashAllocDynAddr(hacCtx->memCtx, reqBodyLen);
        if (hacCtx->reqBodyAddr == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "|Se-Hac|: Alloc reqBodyAddr OOM");
            hacCtx->reqBodyLen = 0;
            return GMERR_OUT_OF_MEMORY;
        }
        hacCtx->reqBodyLen = reqBodyLen;
    }
    if (respBodyLen > hacCtx->respBodyLen) {
        if (hacCtx->respBodyAddr != NULL) {
            HashFreeDynAddr(hacCtx->memCtx, hacCtx->respBodyAddr);
        }
        hacCtx->respBodyAddr = HashAllocDynAddr(hacCtx->memCtx, respBodyLen);
        if (hacCtx->respBodyAddr == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "|Se-Hac|: Alloc respBodyAddr OOM");
            hacCtx->respBodyLen = 0;
            // 这里no need释放hacCtx->reqHeaderAddr的内存，因为内存可能不是这次调用申请的。由外层释放（HacClose）
            return GMERR_OUT_OF_MEMORY;
        }
        hacCtx->respBodyLen = respBodyLen;
    }
    // 可以保证置位一定不会报错，初始化不能为0x0或0xff(当前0x0、0xff都有特殊语义)
    (void)memset_s(hacCtx->reqBodyAddr, reqBodyLen, HAC_ERROR_UNINTI, reqBodyLen);
    (void)memset_s(hacCtx->respBodyAddr, respBodyLen, HAC_ERROR_UNINTI, respBodyLen);
    return GMERR_OK;
}

// 查看批量上一次缓存的内存空间是否足够使用，足够则复用。
Status HacInitTupleAddr(HacCtxT *hacCtx)
{
    uint32_t tupleAddrLen = hacCtx->tupleSize * hacCtx->reqNum;
    if (tupleAddrLen > hacCtx->tupleAddrLen) {
        if (hacCtx->tuplesAddr != NULL) {
            HashFreeDynAddr(hacCtx->memCtx, hacCtx->tuplesAddr);
        }
        hacCtx->tuplesAddr = HashAllocDynAddr(hacCtx->memCtx, tupleAddrLen);
        if (hacCtx->tuplesAddr == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "|Se-Hac|: Alloc tuplesAddr unsucc");
            hacCtx->tupleAddrLen = 0;
            return GMERR_OUT_OF_MEMORY;
        }
        hacCtx->tupleAddrLen = tupleAddrLen;
    }
    // 可以保证置位一定不会报错
    (void)memset_s(hacCtx->tuplesAddr, tupleAddrLen, 0, tupleAddrLen);
    return GMERR_OK;
}

// 填充写报文
Status HacFillMsgBodyForWriteInner(HacCtxT *hacCtx, IndexKeyT idxKey[], HpBatchOutT addr[])
{
    uint8_t keyLen = (uint8_t)idxKey[0].keyLen;
    HacHashReqHeaderT *bodyHeader = (HacHashReqHeaderT *)(void *)hacCtx->reqBodyAddr;
    uint8_t *body = (uint8_t *)(bodyHeader + 1);
    errno_t err = EOK;
    for (uint32_t i = 0u; i < hacCtx->batchNum; i++) {
        err = memcpy_s(body, keyLen, idxKey[i].keyData, keyLen);
        if (err != EOK) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_FIELD_OVERFLOW, "|Se-Hac|: copy write msg body unsucc: errCode|%d|", err);
            return GMERR_FIELD_OVERFLOW;
        }
        body += keyLen;
        if (SECUREC_UNLIKELY(HacIsTupleMode64())) {
            *(HpTupleAddr *)(void *)body = addr[i].addrOut;
        } else {
            *(TuplePointer32T *)(void *)body = HeapCompressTupleAddr32Hac(addr[i].addrOut);
        }
        body += DB_8BIT;
    }
    return GMERR_OK;
}

// 填充读报文
Status HacFillMsgBodyForReadInner(HacCtxT *hacCtx, IndexKeyT idxKey[], bool isKeyCmpBySoft)
{
    uint8_t keyLen = (uint8_t)idxKey[0].keyLen;
    HacHashReqHeaderT *reqBodyHeader = (HacHashReqHeaderT *)(void *)hacCtx->reqBodyAddr;
    uint8_t *body = (uint8_t *)(reqBodyHeader + 1);
    errno_t err = EOK;
    for (uint32_t i = 0u; i < hacCtx->batchNum; i++) {
        err = memcpy_s(body, keyLen, idxKey[i].keyData, keyLen);
        if (err != EOK) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "|Se-Hac|: copy read msg body unsucc: errCode|%d|", err);
            return GMERR_FIELD_OVERFLOW;
        }
        body += keyLen;
        if (!isKeyCmpBySoft) {  // 非软件比较，需要传入拷贝tuple的addr
            *(uint64_t *)(void *)body = (uint64_t)(DB_UINTPTR)(hacCtx->tuplesAddr + i * hacCtx->tupleSize);
        }
        body += DB_8BIT;
    }
    return GMERR_OK;
}

Status HacFillMsgBodyForRead(HacCtxT *hacCtx, IndexKeyT idxKey[], uint32_t *reqBodyLen)
{
    uint32_t batchNum = hacCtx->batchNum;
    bool isKeyCmpBySoft = hacCtx->hacType == HAC_HASH_LOOKUP_KEYCMP_BY_SOFT;
    uint32_t oneOptSize = idxKey[0].keyLen + DB_8BIT;
    *reqBodyLen = (uint32_t)sizeof(HacHashReqHeaderT) + batchNum * oneOptSize;
    // 每个应答操作包含1B定长应答字段和8B的tupleAddr
    uint32_t respBodyLen = batchNum * (RESP_SIZE + DB_8BIT);
    Status ret = HacGetBodyAddr(hacCtx, *reqBodyLen, respBodyLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    return HacFillMsgBodyForReadInner(hacCtx, idxKey, isKeyCmpBySoft);
}

Status HacFillMsgBodyForWrite(HacCtxT *hacCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t *reqBodyLen)
{
    uint32_t batchNum = hacCtx->batchNum;
    // 每个请求操作包含定长key和4/8B的tupleAddr
    uint32_t oneOptSize = idxKey[0].keyLen + DB_8BIT;
    *reqBodyLen = (uint32_t)sizeof(HacHashReqHeaderT) + batchNum * oneOptSize;
    // 每个应答操作包含1B定长应答字段
    uint32_t respBodyLen = batchNum * RESP_SIZE;
    Status ret = HacGetBodyAddr(hacCtx, *reqBodyLen, respBodyLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    return HacFillMsgBodyForWriteInner(hacCtx, idxKey, addr);
}

// 填充报文头部
void HacHashFillMsgHeader(HacCtxT *hacCtx, HacBatchTypeE hacType, uint32_t keySize, IdxHacHashT *hashHac)
{
    DB_POINTER2(hacCtx->indexCtx, hacCtx->indexCtx->idxHandle);
    HacHashReqHeaderT *reqHeader = (HacHashReqHeaderT *)(void *)hacCtx->reqBodyAddr;
    reqHeader->srcId = hacCtx->callId;
    reqHeader->reqId = 0;
    reqHeader->batchType = (uint8_t)hacType;
    reqHeader->optNum = hacCtx->batchNum;
    reqHeader->keySize = (uint8_t)keySize;  // 保证不会超过uint8_max
    reqHeader->responseAddr = HacAddrToUInt64(hacCtx->respBodyAddr);
    reqHeader->hashCtxAddr = HacAddrToUInt64((void *)&hashHac->bucketHead);  // 与硬件约定的内存上下文
}

inline static void MultiHashFillMsgHeader(HacCtxT *hacCtx, HacBatchTypeE hacType, uint32_t keySize)
{
    MultiHashReqHeaderT *reqHeader = (MultiHashReqHeaderT *)(void *)hacCtx->reqBodyAddr;
    reqHeader->srcId = hacCtx->callId;
    reqHeader->reqId = 0;
    reqHeader->batchType = (uint8_t)hacType;
    reqHeader->optNum = hacCtx->batchNum;
    reqHeader->keySize = (uint8_t)keySize;  // 保证不会超过uint8_max
    reqHeader->responseAddr = HacAddrToUInt64(hacCtx->respBodyAddr);
}

// 处理成功或warnig场景
Status HacRespWithNoErrorForRead(HacCtxT *hacCtx, HacRespParaT respPara, bool *isFinish)
{
    DB_POINTER(isFinish);
    *isFinish = true;
    bool isMatch = false;
    Status ret = GMERR_OK;
    IndexCtxT *idxCtx = hacCtx->indexCtx;
    HpTupleAddr tupleAddr = respPara.addr;
    HacStatusInterE hacStatus = respPara.hacStatus;
    int32_t cmpRet = 0;
    switch (hacStatus) {
        case HAC_SUCCESS:
            isMatch = true;
            idxCtx->tupleBuf.len = hacCtx->tupleSize;
            idxCtx->tupleBuf.buf = hacCtx->tuplesAddr + (DB_UINTPTR)hacCtx->queryIdx * hacCtx->tupleSize;
            break;
        case HAC_WARNING_LOOKUP_NO_DATA:
            (void)DbAtomicInc64(&HacGetStatsImpl()->hacRspWarnCnt);
            break;
        case HAC_WARNING_LOOKUP_SINGLE_CONFLICT:
            (void)DbAtomicInc64(&HacGetStatsImpl()->hacRspWarnCnt);
            ret = idxCtx->idxOpenCfg.callbackFunc.keyCmp(idxCtx, respPara.idxKey, tupleAddr, &cmpRet, &isMatch);
            break;
        case HAC_WARNING_LOOKUP_MULTI_CONFLICT:
            (void)DbAtomicInc64(&HacGetStatsImpl()->hacRspWarnCnt);
            ret = hacCtx->idxLookupFunc(idxCtx, respPara.idxKey, &tupleAddr, &isMatch);
            break;
        default:
            *isFinish = false;
            return GMERR_OK;
    }
    if (ret != GMERR_OK && !SeIsFilterErrorCode(ret)) {
        return ret;
    }
    IdxBatchLookupParaT *para = respPara.lookupPara;
    if (para == NULL) {
        return GMERR_OK;
    }

    if (isMatch) {
        ret = para->categorizeFunc(
            idxCtx, para->iter, TupleAddr2IdxTupleOrIter(tupleAddr), hacCtx->queryIdx, IDX_IS_TUPLE_ADDR);
        if (hacStatus == HAC_SUCCESS) {  // 成功场景，内存由索引内部回收
            idxCtx->tupleBuf.buf = NULL;
            idxCtx->tupleBuf.len = 0;
        }
        return ret;
    }
    return para->categorizeFunc(
        idxCtx, para->iter, TupleAddr2IdxTupleOrIter(SE_HEAP_INVALID_ADDR), hacCtx->queryIdx, IDX_IS_NOT_FOUND);
}

// 解析返回读报文
Status HacGetRespMsgForRead(HacCtxT *hacCtx, IndexKeyT idxKey[], IdxBatchLookupParaT *para)
{
    uint8_t *hacStatus = hacCtx->respBodyAddr;
    void *addrStart = (void *)((DB_UINTPTR)hacStatus + hacCtx->batchNum * RESP_SIZE);
    HpTupleAddr addr = SE_HEAP_INVALID_ADDR;
    bool isFinish = false;
    Status lastErrRet = GMERR_OK;
    for (uint32_t i = 0; i < hacCtx->batchNum; i++, hacCtx->queryIdx++) {
        if (!HacIsTupleMode64()) {
            addr = HeapUncompressTupleAddr32Hac(*(TuplePointer32T *)addrStart);
            addrStart = (TuplePointer32T *)addrStart + 1;
        } else {
            addr = *(HpTupleAddr *)addrStart;
            addrStart = (HpTupleAddr *)addrStart + 1;
        }
        HacRespParaT respPara = (HacRespParaT){.hacStatus = (HacStatusInterE)hacStatus[i],
            .idxKey = idxKey[hacCtx->queryIdx],
            .addr = addr,
            .lookupPara = para};
        Status ret = HacRespWithNoErrorForRead(hacCtx, respPara, &isFinish);
        if (ret == GMERR_OK && isFinish) {
            continue;
        }
        lastErrRet = isFinish ? ret : GMERR_INTERNAL_ERROR;
        DB_LOG_ERROR_AND_SET_LASTERR(lastErrRet,
            "|Se-Hac|: HacHash Lookup unsucc, batchId is: |%" PRIu32 "|, errCode is |%" PRIu8
            "|, tupleAddr is |%" PRIu64 "|",
            hacCtx->queryIdx, hacStatus[i], addr);
    }
    return lastErrRet;
}

static void UpdateHacHashTableStatsCommon(HacHashTableT *ht, HacRespMessageT *recMes, bool isInsert)
{
    if (isInsert) {
        ht->extendListNum += recMes->extendedListNum;
        ht->hashCollisionCnt += recMes->hashCollisionCnt;
    } else if (recMes->addedOrDeletedExtendedListNum == 1) {  // 删除时，会删除旧链
        ht->extendListNum -= recMes->extendedListNum;
    }
}

Status HacHashCopeRespMsgForInsert(HacCtxT *hacCtx, IndexKeyT idxKey[], HpBatchOutT addr[])
{
    uint8_t *hacStatus = hacCtx->respBodyAddr;
    Status lastErrRet = GMERR_OK;
    IndexCtxT *idxCtx = hacCtx->indexCtx;
    bool hasInjectMem = false;
    uint32_t failedNum = 0;
    for (uint32_t i = 0; i < hacCtx->batchNum; i++, hacCtx->queryIdx++) {
        HacStatusInterE hacState = hacStatus[i];
        if (hacState == HAC_SUCCESS) {
            continue;
        }
        if (hacState == HAC_WARNING_WATERMARK_SHORT) {
            (void)DbAtomicInc64(&HacGetStatsImpl()->hacRspWarnCnt);
            if (!hasInjectMem) {
                (void)HacInjectMemory(GetHacMgr());
                hasInjectMem = true;
            }
            continue;
        }
        if (hacState == HAC_ERROR_INDEX_UNIQUE_VIOLATION && idxCtx->idxMetaCfg.idxConstraint == PRIMARY) {
            (void)DbAtomicInc64(&HacGetStatsImpl()->hacRspNormalErrCnt);
            lastErrRet = GMERR_UNIQUE_VIOLATION;
        } else if (hacState == HAC_ERROR_INDEX_UNIQUE_VIOLATION ||  // 唯一索引场景，软件需要额外做老化检查
                   hacState == HAC_WARNING_INSERT_CONFLICT) {  // 出现hash冲突，软件对相应数据重新插入
            (void)DbAtomicInc64(&HacGetStatsImpl()->hacRspWarnCnt);
            Status ret =
                hacCtx->idxInsertOrDeleteFunc(idxCtx, idxKey[hacCtx->queryIdx], addr[hacCtx->queryIdx].addrOut, true);
            if (ret == GMERR_OK) {
                continue;
            }
            lastErrRet = ret;
        } else if (hacState == HAC_ERROR_INSERT_LIST_FAILED) {
            (void)DbAtomicInc64(&HacGetStatsImpl()->hacRspNormalErrCnt);
            lastErrRet = GMERR_OUT_OF_MEMORY;
        } else {
            (void)DbAtomicInc64(&HacGetStatsImpl()->hacRspSpecialErrCnt);
            lastErrRet = GMERR_INTERNAL_ERROR;
        }
        failedNum++;
        DB_LOG_ERROR_AND_SET_LASTERR(lastErrRet,
            "|Se-Hac|: HacHash Insert unsucc, batchId is: |%" PRIu32 "|, errCode is |%" PRIu8 "|, batchRes is |%" PRIu8
            "|",
            hacCtx->queryIdx, hacState, hacCtx->respMes.batchRes);
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(hacCtx->indexCtx->idxHandle);
    ht->hashHac.entryNum = ht->hashHac.entryNum + hacCtx->batchNum - failedNum;
    return lastErrRet;
}

Status HacHashCopeRespMsgForDelete(HacCtxT *hacCtx)
{
    uint8_t *hacStatus = hacCtx->respBodyAddr;
    Status lastErrRet = GMERR_OK;
    uint32_t failedNum = 0;
    for (uint32_t i = 0; i < hacCtx->batchNum; i++, hacCtx->queryIdx++) {
        HacStatusInterE hacState = hacStatus[i];
        if (hacState == HAC_SUCCESS) {
            continue;
        }
        failedNum++;
        if (hacState == HAC_WARNING_DELETE_NO_DATA) {
            (void)DbAtomicInc64(&HacGetStatsImpl()->hacRspWarnCnt);
            continue;
        }
        (void)DbAtomicInc64(&HacGetStatsImpl()->hacRspSpecialErrCnt);
        lastErrRet = GMERR_INTERNAL_ERROR;
        DB_LOG_ERROR_AND_SET_LASTERR(lastErrRet,
            "|Se-Hac|: HacHash Delete unsucc, batchId is: |%" PRIu32 "|, errCode is |%" PRIu8 "|, batchRes is |%" PRIu8
            "|",
            hacCtx->queryIdx, hacState, hacCtx->respMes.batchRes);
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(hacCtx->indexCtx->idxHandle);
    ht->hashHac.entryNum = ht->hashHac.entryNum + failedNum - hacCtx->batchNum;
    return lastErrRet;
}

// 解析返回写报文
Status HacHashGetRespMsgForWrite(HacCtxT *hacCtx, IndexKeyT idxKey[], HpBatchOutT addr[])
{
    Status ret = HacReceiveMsg(hacCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(hacCtx->indexCtx->idxHandle);
    UpdateHacHashTableStatsCommon(ht, &hacCtx->respMes, hacCtx->isInsert);
    if ((HacStatusInterE)hacCtx->respMes.batchRes == HAC_SUCCESS) {
        // 当前加速器批写事务仅限轻量化事务，已保证并发问题
        ht->hashHac.entryNum =
            hacCtx->isInsert ? (ht->hashHac.entryNum + hacCtx->batchNum) : (ht->hashHac.entryNum - hacCtx->batchNum);
        return GMERR_OK;
    }
    return hacCtx->isInsert ? HacHashCopeRespMsgForInsert(hacCtx, idxKey, addr) : HacHashCopeRespMsgForDelete(hacCtx);
}

Status MultiHashModifyStatsAndCheckExpand(HacCtxT *hacCtx)
{
    HacHashTableT *ht = HacCastIdxAsHashTable(hacCtx->indexCtx->idxHandle);

    ht->hashHac.entryNum =
        hacCtx->isInsert ? (ht->hashHac.entryNum + hacCtx->batchNum) : (ht->hashHac.entryNum - hacCtx->batchNum);
    if (SECUREC_UNLIKELY(hacCtx->isSameSecEntry)) {
        MultiHashDMLParaT *para = (MultiHashDMLParaT *)DbListItem(&hacCtx->list, 0u);
        SecHashTableT *secHt = para->secHash;
        if (SECUREC_UNLIKELY(secHt == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-MH|: unable to get hash's secHt");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        IdxHacHashT *hashHac = &secHt->hashHac;
        if (hacCtx->isInsert) {
            hashHac->entryNum += hacCtx->batchNum;
            if (SECUREC_UNLIKELY(hashHac->entryNum >= hashHac->expandNum)) {
                (void)HashSecHtCheckExpand(hacCtx->indexCtx, hashHac);
            }
        } else {
            hashHac->entryNum -= hacCtx->batchNum;
        }
    } else {
        uint32_t listCnt = DbListGetItemCnt(&hacCtx->list);
        for (uint32_t i = 0; i < listCnt; i++) {
            MultiHashDMLParaT *dmlPara = (MultiHashDMLParaT *)DbListItem(&hacCtx->list, i);
            SecHashTableT *secHt = dmlPara->secHash;
            if (SECUREC_UNLIKELY(secHt == NULL)) {
                DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-MH|: unable to get hash's secHt");
                return GMERR_UNEXPECTED_NULL_VALUE;
            }
            IdxHacHashT *hashHac = &secHt->hashHac;
            if (hacCtx->isInsert) {
                hashHac->entryNum++;
                if (SECUREC_UNLIKELY(hashHac->entryNum >= hashHac->expandNum)) {
                    (void)HashSecHtCheckExpand(hacCtx->indexCtx, hashHac);
                }
            } else {
                hashHac->entryNum--;
            }
        }
    }
    return GMERR_OK;
}

Status MultiHashCopeRespMsgForInsert(HacCtxT *hacCtx)
{
    uint8_t *hacStatus = hacCtx->respBodyAddr;
    Status lastErrRet = GMERR_OK;
    bool hasInjectMem = false;
    uint32_t failedNum = 0;
    for (uint32_t i = 0; i < hacCtx->batchNum; i++, hacCtx->queryIdx++) {
        MultiHashDMLParaT *para = (MultiHashDMLParaT *)DbListItem(&hacCtx->list, hacCtx->queryIdx);
        SecHashTableT *secHt = para->secHash;
        if (SECUREC_UNLIKELY(secHt == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-MH|: unable to get hash's secHt");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        HacStatusInterE hacState = (HacStatusInterE)hacStatus[i];
        if (SECUREC_UNLIKELY(hacState != HAC_SUCCESS && hacState != HAC_WARNING_WATERMARK_SHORT)) {
            failedNum++;
            if (hacState == HAC_ERROR_INSERT_LIST_FAILED) {
                (void)DbAtomicInc64(&HacGetStatsImpl()->hacRspNormalErrCnt);
                lastErrRet = GMERR_OUT_OF_MEMORY;
            } else {
                (void)DbAtomicInc64(&HacGetStatsImpl()->hacRspSpecialErrCnt);
                lastErrRet = GMERR_INTERNAL_ERROR;
            }
            DB_LOG_ERROR_AND_SET_LASTERR(lastErrRet,
                "|Se-Hac|: MultiHash insert unsucc, batchId is: |%" PRIu32 "|, errCode is |%" PRIu8
                "|, batchRes is |%" PRIu8 "|",
                hacCtx->queryIdx, hacState, hacCtx->respMes.batchRes);
            continue;
        }

        if (hacState == HAC_WARNING_WATERMARK_SHORT) {
            (void)DbAtomicInc64(&HacGetStatsImpl()->hacRspWarnCnt);
            if (!hasInjectMem) {
                (void)HacInjectMemory(GetHacMgr());
                hasInjectMem = true;
            }
        }
        secHt->hashHac.entryNum++;
        if (SECUREC_UNLIKELY(secHt->hashHac.entryNum >= secHt->hashHac.expandNum)) {
            (void)HashSecHtCheckExpand(hacCtx->indexCtx, &secHt->hashHac);
        }
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(hacCtx->indexCtx->idxHandle);
    ht->hashHac.entryNum = ht->hashHac.entryNum + hacCtx->batchNum - failedNum;
    return lastErrRet;
}

Status MultiHashCopeRespMsgForDelete(HacCtxT *hacCtx)
{
    uint8_t *hacStatus = hacCtx->respBodyAddr;
    Status lastErrRet = GMERR_OK;
    uint32_t failedNum = 0;
    for (uint32_t i = 0; i < hacCtx->batchNum; i++, hacCtx->queryIdx++) {
        HacStatusInterE hacState = (HacStatusInterE)hacStatus[i];
        if (hacState == HAC_SUCCESS) {
            MultiHashDMLParaT *para = (MultiHashDMLParaT *)DbListItem(&hacCtx->list, hacCtx->queryIdx);
            SecHashTableT *secHt = para->secHash;
            secHt->hashHac.entryNum--;
            continue;
        }
        failedNum++;
        if (hacState == HAC_WARNING_DELETE_NO_DATA) {
            (void)DbAtomicInc64(&HacGetStatsImpl()->hacRspWarnCnt);
            continue;
        }
        (void)DbAtomicInc64(&HacGetStatsImpl()->hacRspSpecialErrCnt);
        lastErrRet = GMERR_INTERNAL_ERROR;
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR,
            "|Se-Hac|: MultiHash delete unsucc, batchId is: |%" PRIu32 "|, errCode is |%" PRIu8
            "|, batchRes is |%" PRIu8 "|",
            hacCtx->queryIdx, hacState, hacCtx->respMes.batchRes);
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(hacCtx->indexCtx->idxHandle);
    ht->hashHac.entryNum = ht->hashHac.entryNum + failedNum - hacCtx->batchNum;
    return lastErrRet;
}

Status MultiHashGetRespMsgForWrite(HacCtxT *hacCtx)
{
    Status ret = HacReceiveMsg(hacCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(hacCtx->indexCtx->idxHandle);
    UpdateHacHashTableStatsCommon(ht, &hacCtx->respMes, hacCtx->isInsert);
    if ((HacStatusInterE)hacCtx->respMes.batchRes == HAC_SUCCESS) {
        return MultiHashModifyStatsAndCheckExpand(hacCtx);
    }
    return hacCtx->isInsert ? MultiHashCopeRespMsgForInsert(hacCtx) : MultiHashCopeRespMsgForDelete(hacCtx);
}

// 处理索引的批量请求分为以下几步：
// 1.构建报文体，需要申请此次报文需要的请求报文体与应答报文体的内存，允许同一上下文复用（上一批次内存足够时）。
// 2.构建报文头部信息。报文头部信息为固定大小，可以一直复用。
// 3.发送与接收报文。芯片上线后会有相关接口，先用软件模拟或GEM5模拟软硬交互，但逻辑并不一致（现无法模拟硬件行为）
// 4.处理接收报文。查看应答消息并处理返回结果，查询还需拷贝内存。

// 对批量查询的约束：批量查询会对元组进行浅拷贝，软件情况下内存需要回收，硬件模式由Hac管理器回收
Status HacHashBatchReadAndGetRespMsg(HacCtxT *hacCtx, IndexKeyT idxKey[], IdxBatchLookupParaT *para)
{
    uint32_t reqLen = 0;
    Status ret = HacFillMsgBodyForRead(hacCtx, idxKey, &reqLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(hacCtx->indexCtx->idxHandle);
    HacHashFillMsgHeader(hacCtx, hacCtx->hacType, idxKey[0].keyLen, &ht->hashHac);
    ret = HacSendMsg(hacCtx, reqLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)DbAtomicInc64(&HacGetStatsImpl()->batchHacSyncCnt);
    (void)DbAtomicAdd64(&HacGetStatsImpl()->hacRqstCnt, hacCtx->batchNum);
    ret = HacReceiveMsg(hacCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    return HacGetRespMsgForRead(hacCtx, idxKey, para);
}

Status HacHashBatchWrite(HacCtxT *hacCtx, IndexKeyT idxKey[], HpBatchOutT addr[])
{
    uint32_t reqLen = 0;
    Status ret = HacFillMsgBodyForWrite(hacCtx, idxKey, addr, &reqLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(hacCtx->indexCtx->idxHandle);
    HacHashFillMsgHeader(hacCtx, hacCtx->hacType, idxKey[0].keyLen, &ht->hashHac);
    ret = HacSendMsg(hacCtx, reqLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    hacCtx->indexCtx->isHacBatchAsync ? (void)DbAtomicInc64(&HacGetStatsImpl()->batchHacAsyncCnt) :
                                        (void)DbAtomicInc64(&HacGetStatsImpl()->batchHacSyncCnt);
    (void)DbAtomicAdd64(&HacGetStatsImpl()->hacRqstCnt, hacCtx->batchNum);
    return GMERR_OK;
}

Status HacHashBatchExecute(HacCtxT *hacCtx, uint32_t batchNum)
{
    // 一个批<=255，且为索引批写时，支持异步
    if (hacCtx->indexCtx->isHacBatchAsync && batchNum <= HAC_BATCH_LIMIT &&
        !IsBatchTypeHacHashLookupByHac(hacCtx->hacType)) {
        hacCtx->queryIdx = 0u;
        hacCtx->batchNum = batchNum;
        return HacHashBatchWrite(hacCtx, hacCtx->idxKey, hacCtx->addr);
    }
    uint32_t startPos = 0u;
    uint32_t leftNum = batchNum;
    Status ret = GMERR_OK;

    while (leftNum > 0) {
        if (leftNum > HAC_BATCH_LIMIT) {  // 一个批量有255个请求的限制
            hacCtx->batchNum = HAC_BATCH_LIMIT;
        } else {
            hacCtx->batchNum = (uint8_t)leftNum;  // HAC_BATCH_LIMIT保证leftNum必然不超过255
        }
        hacCtx->queryIdx = startPos;  // 硬件卸载场景复用queryIdx，做批处理的偏移
        if (IsBatchTypeHacHashLookupByHac(hacCtx->hacType)) {
            ret = HacHashBatchReadAndGetRespMsg(hacCtx, hacCtx->idxKey + startPos, hacCtx->para);
        } else {
            ret = HacHashBatchWrite(hacCtx, hacCtx->idxKey + startPos, hacCtx->addr + startPos);
            if (ret != GMERR_OK) {
                return ret;
            }
            ret = HacHashGetRespMsgForWrite(hacCtx, hacCtx->idxKey + startPos, hacCtx->addr + startPos);
        }
        if (ret != GMERR_OK) {
            return ret;
        }
        startPos += hacCtx->batchNum;
        leftNum -= hacCtx->batchNum;
    }
    return GMERR_OK;
}

Status HacFillMsgBodySameEntry(HacCtxT *hacCtx, HpBatchOutT addr[], uint32_t startPos)
{
    // 相同key场景，复用hachash的模式优化性能
    HacHashReqHeaderT *bodyHeader = (HacHashReqHeaderT *)(void *)hacCtx->reqBodyAddr;
    uint8_t *body = (uint8_t *)(bodyHeader + 1);
    for (uint32_t i = 0u; i < hacCtx->batchNum; i++) {
        if (HacIsTupleMode64()) {
            *(uint64_t *)(void *)body = addr[startPos + i].addrOut;
            body += DB_8BIT;
            *(HpTupleAddr *)(void *)body = addr[startPos + i].addrOut;
            body += DB_8BIT;
        } else {
            TuplePointer32T addr32 = HeapCompressTupleAddr32Hac(addr[startPos + i].addrOut);
            *(TuplePointer32T *)(void *)body = addr32;
            body += DB_4BIT;  // 作为传入的key，为实际长度
            *(TuplePointer32T *)(void *)body = addr32;
            body += DB_8BIT;  // 作为传入的value，统一为8B
        }
    }
    return GMERR_OK;
}

Status HacFillMsgBodyDiffEntry(HacCtxT *hacCtx, DbListT *list, HpBatchOutT addr[], uint32_t startPos)
{
    MultiHashReqHeaderT *bodyHeader = (MultiHashReqHeaderT *)(void *)hacCtx->reqBodyAddr;
    uint8_t *body = (uint8_t *)(bodyHeader + 1);
    for (uint32_t i = 0u; i < hacCtx->batchNum; i++) {
        MultiHashDMLParaT *para = (MultiHashDMLParaT *)DbListItem(list, startPos + i);
        SecHashTableT *secHt = para->secHash;
        if (SECUREC_UNLIKELY(secHt == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-Hac|: get hash's secHt null");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        *(uint64_t *)body = HacAddrToUInt64((void *)&secHt->hashHac.bucketHead);
        body += DB_8BIT;
        if (HacIsTupleMode64()) {
            *(HpTupleAddr *)(void *)body = addr[i + startPos].addrOut;
        } else {
            *(TuplePointer32T *)(void *)body = HeapCompressTupleAddr32Hac(addr[i + startPos].addrOut);
        }
        body += DB_8BIT;
    }
    return GMERR_OK;
}

Status HacFillMsgBodyForMultiHashDiffEntry(
    HacCtxT *hacCtx, DbListT *list, HpBatchOutT addr[], uint32_t startPos, uint32_t *reqBodyLen)
{
    uint32_t batchNum = hacCtx->batchNum;
    uint32_t oneOptSize = DB_8BIT + DB_8BIT;

    *reqBodyLen = (uint32_t)sizeof(MultiHashReqHeaderT) + batchNum * oneOptSize;
    uint32_t respBodyLen = batchNum * RESP_SIZE;
    Status ret = HacGetBodyAddr(hacCtx, *reqBodyLen, respBodyLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    return HacFillMsgBodyDiffEntry(hacCtx, list, addr, startPos);
}

Status HacFillMsgBodyForMultiHashSameEntry(HacCtxT *hacCtx, HpBatchOutT addr[], uint32_t startPos, uint32_t *reqBodyLen)
{
    uint32_t batchNum = hacCtx->batchNum;
    // MultiHash复用HacHash模式时，key和addr都是tupleAddr，单个操作长度为2倍tupleAddr长
    uint32_t oneOptSize = DB_8BIT + DB_8BIT;
    *reqBodyLen = (uint32_t)sizeof(HacHashReqHeaderT) + batchNum * oneOptSize;
    uint32_t respBodyLen = batchNum * RESP_SIZE;
    Status ret = HacGetBodyAddr(hacCtx, *reqBodyLen, respBodyLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    return HacFillMsgBodySameEntry(hacCtx, addr, startPos);
}

Status MultiHashBatchWrite(HacCtxT *hacCtx, DbListT *list, HpBatchOutT addr[], uint32_t startPos, bool isSameEntry)
{
    if (hacCtx->hacType != MULTI_HASH_DELETE && hacCtx->hacType != MULTI_HASH_INSERT) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "|Se-Hac|: hactype request unmatch");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    uint32_t reqLen = 0;
    Status ret = GMERR_OK;
    if (isSameEntry) {
        ret = HacFillMsgBodyForMultiHashSameEntry(hacCtx, addr, startPos, &reqLen);
        if (ret != GMERR_OK) {
            return ret;
        }
        MultiHashDMLParaT *para = (MultiHashDMLParaT *)DbListItem(list, 0);
        SecHashTableT *secHt = para->secHash;
        if (SECUREC_UNLIKELY(secHt == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-Hac|: get hash's secHt null");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }

        HacHashFillMsgHeader(hacCtx,
            hacCtx->hacType == MULTI_HASH_INSERT ? HAC_HASH_INSERT_COMPARE_TUPLE : HAC_HASH_DELETE,
            HacGetTupleAddrSize(), &secHt->hashHac);
    } else {
        ret = HacFillMsgBodyForMultiHashDiffEntry(hacCtx, list, addr, startPos, &reqLen);
        if (ret != GMERR_OK) {
            return ret;
        }
        MultiHashFillMsgHeader(hacCtx, hacCtx->hacType, HacGetTupleAddrSize());
    }
    ret = HacSendMsg(hacCtx, reqLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    hacCtx->indexCtx->isHacBatchAsync ? (void)DbAtomicInc64(&HacGetStatsImpl()->batchHacAsyncCnt) :
                                        (void)DbAtomicInc64(&HacGetStatsImpl()->batchHacSyncCnt);
    (void)DbAtomicAdd64(&HacGetStatsImpl()->hacRqstCnt, hacCtx->batchNum);
    return GMERR_OK;
}

Status MultiHashBatchExecuteByHac(HacCtxT *hacCtx, DbListT *list)
{
    uint32_t batchNum = DbListGetItemCnt(list);
    if (batchNum == 0) {
        return GMERR_OK;
    }
    if (hacCtx->indexCtx->isHacBatchAsync && batchNum <= HAC_BATCH_LIMIT) {
        hacCtx->queryIdx = 0u;
        hacCtx->batchNum = batchNum;
        return MultiHashBatchWrite(hacCtx, list, hacCtx->addr, 0, hacCtx->isSameSecEntry);
    }

    hacCtx->hacType = hacCtx->isInsert ? MULTI_HASH_INSERT : MULTI_HASH_DELETE;
    uint32_t startPos = 0u;
    Status ret = GMERR_OK;
    while (batchNum > 0) {
        if (SECUREC_LIKELY(batchNum <= HAC_BATCH_LIMIT)) {
            hacCtx->batchNum = (uint8_t)batchNum;  // HAC_BATCH_LIMIT保证leftNum必然不超过255
        } else {
            hacCtx->batchNum = HAC_BATCH_LIMIT;
        }
        hacCtx->queryIdx = startPos;  // 硬件卸载场景复用queryIdx，做批处理的偏移
        ret = MultiHashBatchWrite(hacCtx, list, hacCtx->addr, startPos, hacCtx->isSameSecEntry);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = MultiHashGetRespMsgForWrite(hacCtx);
        if (ret != GMERR_OK) {
            return ret;
        }
        startPos += hacCtx->batchNum;
        batchNum -= hacCtx->batchNum;
    }
    return ret;
}

Status HacHeapFetch(HacKeyCmpCtxT *keyCmpCtx, uint64_t addr, HeapTupleBufT *buf)
{
    uint8_t tupleShiftBit = GetHacMgr()->tupleShiftBit;
    uint32_t pageId = (addr << (DB_64BIT - tupleShiftBit)) >> (DB_64BIT - tupleShiftBit);
    uint32_t slotId = addr >> tupleShiftBit;

    uint8_t addrShiftBit = GetHacMgr()->addrShiftBit;
    uint32_t deviceId = pageId >> addrShiftBit;
    uint32_t blockId = (pageId << (DB_32BIT - addrShiftBit)) >> (DB_32BIT - addrShiftBit);

    if (SECUREC_UNLIKELY(deviceId >= GetHacMgr()->devMaxCount)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "dev id: %" PRIu32 " exceeds max cache capacity: %" PRIu32,
            deviceId, GetHacMgr()->devMaxCount);
        return GMERR_DATA_EXCEPTION;
    }
    uint8_t **virAddr = DevGetEntryHac();
    if (SECUREC_UNLIKELY(virAddr == NULL || virAddr[deviceId] == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "find device cache null");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint8_t *pageHeadAddr = (void *)((uintptr_t)virAddr[deviceId] + (blockId << GetHacMgr()->pageSizeShiftBit));

    if (slotId >= keyCmpCtx->rowCnt) {
        return GMERR_NO_DATA;
    }
    uint32_t offset =
        keyCmpCtx->rowBegin + slotId * (keyCmpCtx->oneRowSize + keyCmpCtx->slotExtendSize) + keyCmpCtx->slotExtendSize;
    if (offset > DB_INVALID_UINT16) {
        return GMERR_NO_DATA;
    }

    HeapTupleBufT tupleBuf = {
        .buf = (void *)((uintptr_t)pageHeadAddr + offset + keyCmpCtx->fixHeadSize), .bufSize = keyCmpCtx->rawRowSize};
    errno_t err = memcpy_s(buf->buf, buf->bufSize, tupleBuf.buf, tupleBuf.bufSize);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "copy tupleBuf.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

bool HacIsDmKeyMatch(HacKeyCmpCtxT *keyCmpCtx, HeapTupleBufT buf, IndexKeyT indexKey)
{
    uint8_t *bufCursor = buf.buf + keyCmpCtx->dmOffset;
    uint8_t *keyBufCursor = indexKey.keyData + keyCmpCtx->nullInfoBytes;
    IdxDmKeyInfoT *keyInfo = keyCmpCtx->keyInfo;
    for (uint32_t i = 0; i < keyCmpCtx->keySegNum; i++) {
        if (memcmp(keyBufCursor, bufCursor + keyInfo[i].segOffsets, keyInfo[i].segLengths) != 0) {
            return false;
        }
        keyBufCursor += keyCmpCtx->keyInfo[i].segLengths;
    }
    return true;
}

Status HacKeyCmp(HacKeyCmpCtxT *keyCmpCtx, IndexKeyT indexKey, uint64_t addr, HeapTupleBufT hpBuf, bool *match)
{
    Status ret = HacHeapFetch(keyCmpCtx, addr, &hpBuf);
    if (ret != GMERR_OK) {
        return ret;
    }

    *match = HacIsDmKeyMatch(keyCmpCtx, hpBuf, indexKey);
    return GMERR_OK;
}

#endif
