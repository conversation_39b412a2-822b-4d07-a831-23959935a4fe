/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2024. All rights reserved.
 * File Name: se_hac_hash_common.h
 * Description: hac hash common interface
 * Author: lijianchuan
 * Create: 2022/11/15
 */

#ifdef FEATURE_HAC
#ifndef SE_HAC_HASH_COMMON_H
#define SE_HAC_HASH_COMMON_H

#include "db_hash.h"
#include "se_hac_common.h"
#include "se_index_common.h"

#define UINT32_BITS 32
#define HASH_CODE_MASK 0x7fffffff

#define HAC_HASH_ENTRY_PER_BUCKET_64_BITS 10u
#define HAC_HASH_ENTRY_PER_BUCKET_32_BITS 14u
#define MULTI_HASH_ENTRY_PER_BUCKET 10u      // MultiHash第一层tuple无法压缩，只有一种模式
#define HAC_HASH_PER_ENTRY_SIZE_32_BITS 8u   // 4B hash code + 4B hash value
#define HAC_HASH_PER_ENTRY_SIZE_64_BITS 12u  // 4B hash code + 8B hash value
#define INVALID_ENTRY_INDEX HAC_HASH_ENTRY_PER_BUCKET_32_BITS  // 两种模式可以通用一个无效值
#define HAC_HASH_MIN_SIZE 2u          // 最小单元（2*2cacheline），hashSize只能是整数倍
#define HAC_HASH_MAX_SIZE 134217728u  // 134217728->2^27->16GB，最大扩容上限
#define HAC_HASH_INDEX_VALID_CODE 0x5cd123ab8accee32u

#define HAC_HASH_BUCKET_LOCK 1
#define MULTI_HASH_TABLE_LOCK 0

#ifdef __cplusplus
extern "C" {
#endif

typedef struct HacHashCode {
    uint32_t hashCode : 31;  // 当前HacHash和MultiHash两种格式不同
    uint32_t presence : 1;
} HacHashCodeT;

typedef struct HacHashBucket32bits {
    HacLogicAddressT nextBucket;                                   // 8B
    HacHashCodeT hacHashCode[HAC_HASH_ENTRY_PER_BUCKET_32_BITS];   // 4*14=56B
    TuplePointer32T tupleAddr[HAC_HASH_ENTRY_PER_BUCKET_32_BITS];  // 56B
    uint64_t reserved;                                             // 补齐2cacheline
} HacHashBucket32bitsT;

typedef struct HacHashBucket64bits {
    HacLogicAddressT nextBucket;                                  // 8B
    HacHashCodeT hacHashCode[HAC_HASH_ENTRY_PER_BUCKET_64_BITS];  // 4*10=40B
    TupleAddr tupleAddr[HAC_HASH_ENTRY_PER_BUCKET_64_BITS];       // 80B
} HacHashBucket64bitsT;

typedef union HacTupleAddr {
    TupleAddr tupleAddr64bits;
    TuplePointer32T tupleAddr32bits;
} HacTupleAddrT;

typedef struct HashReHashPara {
    HacBucketT *dstBucket1;
    HacBucketT *dstBucket2;
    uint32_t bucketIndex1;
    uint32_t bucketIndex2;
} HashReHashParaT;

typedef struct HashMergePara {
    HacBucketT *dstBucket;
    uint32_t bucketIndex;
} HashMergeParaT;

typedef struct HashDMLPara {
    IndexCtxT *idxCtx;
    IdxHacHashT *hashHac;
    IndexKeyT hashKey;
    uint32_t hashCode;
    TupleAddr addr;  // insert、delete有效
} HashDMLParaT;      // 做dml的辅助结构体

typedef struct MultiHashIterator {
    IndexKeyT idxKey;
    SecHashTableT *hashTable;
    uint32_t hashCode;
    HacBucketT *secBucketHead;
    HacBucketT *curBucket;
    uint32_t bucketIndex;
    uint32_t index;
    bool isEOF;
} MultiHashIteratorT;

typedef void (*HacHashTableFuncT)(DbMemCtxT *, HacHashTableT *);
Status HacHashIdxCreateCommon(SeRunCtxT *seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr);
Status HacHashIdxDropCommon(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr, HacHashTableFuncT dropFunc);
Status HacHashIdxTruncateCommon(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr, HacHashTableFuncT truncateFunc);
Status HacHashIdxOpenCommon(IndexCtxT *idxCtx);
void HacHashIdxCloseCommon(IndexCtxT *idxCtx);

inline static void HacHashInitRunCtx(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);

    idxCtx->isAcquireLockByTryOnce = true;
    idxCtx->batchLocked = false;
    idxCtx->hasLookup = false;
}

inline static void HacHashUnInitRunCtx(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    idxCtx->idxShmAddr = HAC_INVALID_SHMPTR;
    idxCtx->idxMetaCfg = (IndexMetaCfgT){0};
    idxCtx->idxHandle = NULL;
}

// 获取不比cap小的最小的2^x，要求调用者保证不为0且不大于0x80000000
inline static uint32_t HashGetRealCap(uint32_t cap)
{
    return 1u << (UINT32_BITS - __builtin_clz(cap - 1));
}

// 硬件卸载预留硬化hash函数，在MHGetHashCode函数内调用本函数已提前看护
uint32_t HacCrcHash32(const uint8_t *key, uint32_t len, uint32_t seed);

// 计算hash值
inline static uint32_t HtGetHashCode(IndexKeyT hashKey)
{
    DB_POINTER(hashKey.keyData);
    return DbHash32(hashKey.keyData, hashKey.keyLen) & HASH_CODE_MASK;
}

inline static uint32_t MHGetHashCode(IndexKeyT hashKey)
{
    DB_POINTER(hashKey.keyData);
    return HacCrcHash32(hashKey.keyData, hashKey.keyLen, DB_XXHASH_SEED);
}

uint32_t HtGetSecHashCode(TupleAddr addr);

inline static uint32_t HtGetHashCodeWithShmPtr(ShmemPtrT shm)
{
    return DbHash32((const uint8_t *)&shm, sizeof(ShmemPtrT));
}

inline static Status HashCheckValid(HacHashTableT *ht, bool *shouldReturn)
{
    *shouldReturn = true;

    if (ht->idxBase.validCode != HAC_HASH_INDEX_VALID_CODE) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "|Se-HH|: hashtable has been drop");
        return GMERR_INTERNAL_ERROR;
    }

    if (!IdxIsConstructed(&ht->idxBase)) {
        return GMERR_OK;
    }

    *shouldReturn = false;
    return GMERR_OK;
}

inline static uint32_t GetHashCodeIndex(uint32_t hashCode)
{
    return hashCode & (ACCELERATOR_MAX_LATCH_NUM - 1u);
}

// 表写锁使用场景：扩缩容、DDL，表锁模式下保留逻辑
// 表读锁使用场景：DML操作，表锁模式下表读锁不用加
// 参数isRead表示是否加读锁，而并非是否为读操作
// HashTablexx：索引全表锁，在hachash和multihash里通用
// MultiHashExpandxx：扩容锁，仅适用于multihash场景(当前使用锁，后续考虑使用引用计数)
// HashBucketxx：桶锁，hachash和multihash里的桶通用；且这里区分软件锁和硬件锁两种模式
void HashTableLock(const IndexCtxT *idxCtx, bool isReadOperation);
void HashTableUnlock(const IndexCtxT *idxCtx, bool isReadOperation);

// 针对批量设计的加锁，非正常事务时批量时加一把大锁
void HashTableLockForBatch(IndexCtxT *idxCtx, bool isReadOperation);
void HashTableUnlockForBatch(IndexCtxT *idxCtx, bool isReadOperation);

inline static void MultiHashExpandLock(const IndexCtxT *idxCtx, bool isRead)
{
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t latchId = GetHashCodeIndex(ht->hashHac.lockOffset);
    if (isRead) {
        AccLatchR(idxCtx, MULTI_HASH_TABLE_LOCK, latchId);
    } else {
        AccLatchW(MULTI_HASH_TABLE_LOCK, latchId);
    }
}

inline static void MultiHashExpandUnlock(const IndexCtxT *idxCtx, bool isRead)
{
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t latchId = GetHashCodeIndex(ht->hashHac.lockOffset);
    if (isRead) {
        AccUnlatchR(idxCtx, MULTI_HASH_TABLE_LOCK, latchId);
    } else {
        AccUnlatchW(MULTI_HASH_TABLE_LOCK, latchId);
    }
}

inline static bool HashTableTryWLock(const IndexCtxT *idxCtx)
{
    if (SECUREC_LIKELY(!IndexIsLabelLatchMode(idxCtx))) {
        return DbRWLatchTryW(&idxCtx->idxHandle->idxLatch);
    }
    return true;
}

inline static void HashTableWUnlock(const IndexCtxT *idxCtx)
{
    if (SECUREC_LIKELY(!IndexIsLabelLatchMode(idxCtx))) {
        DbRWUnlatchW(&idxCtx->idxHandle->idxLatch);
    }
}

// 桶写锁使用场景:写操作写具体桶
// 桶读锁使用场景:读操作读具体桶
// 当前轻量化事务场景通过加索引级别锁避免对桶再加锁
// 具体：写加索引写锁、读加索引读锁，保证一写多读场景下对一个索引的读写操作不会并发
inline static void HashBucketLock(const IndexCtxT *idxCtx, uint32_t hashCode, bool isRead)
{
    if (SECUREC_LIKELY(idxCtx->batchLocked || GetHacMgr()->ccType != CONCURRENCY_CONTROL_NORMAL)) {
        return;
    }
    uint32_t latchId = GetHashCodeIndex(hashCode);
    if (isRead) {
        AccLatchR(idxCtx, HAC_HASH_BUCKET_LOCK, latchId);
    } else {
        AccLatchW(HAC_HASH_BUCKET_LOCK, latchId);
    }
}

inline static void HashBucketUnlock(const IndexCtxT *idxCtx, uint32_t hashCode, bool isRead)
{
    if (SECUREC_LIKELY(idxCtx->batchLocked || GetHacMgr()->ccType != CONCURRENCY_CONTROL_NORMAL)) {
        return;
    }
    uint32_t latchId = GetHashCodeIndex(hashCode);
    if (isRead) {
        AccUnlatchR(idxCtx, HAC_HASH_BUCKET_LOCK, latchId);
    } else {
        AccUnlatchW(HAC_HASH_BUCKET_LOCK, latchId);
    }
}

inline static bool MultiHashExpandTryWLock(const IndexCtxT *idxCtx)
{
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t latchId = GetHashCodeIndex(ht->hashHac.lockOffset);
    return AccLatchTryW(MULTI_HASH_TABLE_LOCK, latchId);
}

inline static void MultiHashTableWUnlock(const IndexCtxT *idxCtx)
{
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    uint32_t latchId = GetHashCodeIndex(ht->hashHac.lockOffset);
    AccUnlatchW(MULTI_HASH_TABLE_LOCK, latchId);
}

inline static uint32_t HashGetBucketIndex(uint32_t hashCode, IdxHacHashT *hashHac)
{
    return hashCode & (hashHac->hashSize - 1u);
}

inline static uint32_t HashGetLockOffset(uint32_t hashCode, IdxHacHashT *hashHac)  // 用于加解锁计算
{
    return HashGetBucketIndex(hashCode, hashHac) + hashHac->lockOffset;  // 偏移计算减少并发冲突
}

inline static HacBucketT *HashGetBucketByHashCode(IdxHacHashT *hashHac, uint32_t hashCode)
{
    HacBucketT *bucket = (HacBucketT *)HashGetShmAddrAlign(hashHac->bucketShmPtr);
    DB_POINTER(bucket);
    return &bucket[HashGetBucketIndex(hashCode, hashHac)];
}

inline static ShmemPtrT *HashGetBucketShmPtrByHashCode(IdxHacHashT *hashHac, uint32_t hashCode)
{
    ShmemPtrT *shmPtr = (ShmemPtrT *)HashGetShmAddrAlign(hashHac->bucketShmPtr);
    DB_POINTER(shmPtr);
    return &shmPtr[HashGetBucketIndex(hashCode, hashHac)];
}

Status HashKeyCmp(IndexCtxT *idxCtx, IndexKeyT indexKey, HpTupleAddr addr, bool *match);

Status HacConstructHtInOpen(IndexCtxT *idxCtx);

Status HashInitKeyCmpCtx(IndexCtxT *idxCtx);

void HashTableDrop(DbMemCtxT *memCtx, IdxHacHashT *hashHac, HacHashTableT *table);

void MultiHashTableDrop(DbMemCtxT *memCtx, HacHashTableT *table);

void HashExtendBucketDrop(HacHashTableT *table, HacBucketT *bucket);

HacBucketT *HashGetExtendBucket(HacLogicAddressT logicAddr);

void HashFreeExtendBucket(HacLogicAddressT logicAddr);

void HashCleanAllExtendBucket(void);

// DML
Status HashInsertMatchEntry(HashDMLParaT *para);

Status HashSearchMatchEntry(HashDMLParaT *para);

Status HashDeleteMatchEntry(HashDMLParaT *para);

// 扩容
Status HashCheckExpand(IndexCtxT *idxCtx, IdxHacHashT *hacHash);

// 缩容
Status HashCheckScaleIn(IndexCtxT *idxCtx, IdxHacHashT *hacHash, IndexScaleInCfgT *idxScaleCfg);

void HacMemMgrScaleIn(void);

Status HashNewExtendBucket(HacHashTableT *ht, HacBucketT **selectBucket, HacLogicAddressT *logicAddr);

Status HashNewExtendBucketInBlock(HacMemMgrT *hacMemMgr, HacBucketT **selectBucket, HacLogicAddressT *logicAddr);

Status HashFetchNext(IndexCtxT *idxCtx, MultiHashIteratorT *iter, TupleAddr *addr, bool *isFound);

Status HashGetEstimateMemSize(IndexMetaCfgT idxMetaCfg, uint64_t count, uint32_t keyLen, uint64_t *size);

void HashTableTruncate(DbMemCtxT *memCtx, IdxHacHashT *hashHac, HacHashTableT *table);

uint32_t HashGetExpectSize(uint32_t hashCap);

uint32_t HashGetExpandSize(uint32_t tableSize);
#ifdef __cplusplus
}
#endif

#endif
#endif
