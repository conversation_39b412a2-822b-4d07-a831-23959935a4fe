/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2024. All rights reserved.
 * File Name: se_multi_hash_index.c
 * Description: multi hash 支持硬件卸载的二级非唯一索引
 * Author: lijianchuan
 * Create: 2022/11/15
 */
#ifdef FEATURE_HAC
#include "se_multi_hash_index.h"
#include "se_capacity_def_inner.h"

void MultiHashIdxAMInitImpl(void)
{
    IdxFuncT multiHashFunc = IdxEmptyIdxFunc();
    multiHashFunc.idxCreate = MultiHashIdxCreate;
    multiHashFunc.idxDrop = MultiHashIdxDrop;
    multiHashFunc.idxTruncate = MultiHashIdxTruncate;
    multiHashFunc.idxOpen = MultiHashIdxOpen;
    multiHashFunc.idxClose = MultiHashIdxClose;
    multiHashFunc.idxInsert = MultiHashIdxInsert;
    multiHashFunc.idxBatchInsert = MultiHashIdxBatchInsert;
    multiHashFunc.idxDelete = MultiHashIdxDelete;
    multiHashFunc.idxBatchDelete = MultiHashIdxBatchDelete;
    multiHashFunc.idxUpdate = MultiHashIdxUpdate;
    multiHashFunc.idxBatchUpdate = MultiHashIdxBatchUpdate;
    multiHashFunc.idxBatchLookup = MultiHashIdxBatchLookup;
    multiHashFunc.idxEndBatchModify = MultiHashIdxEndBatchModify;
    multiHashFunc.idxBeginScan = MultiHashIdxBeginScan;
    multiHashFunc.idxScan = MultiHashIdxScan;
    multiHashFunc.idxEndScan = MultiHashIdxEndScan;
    multiHashFunc.idxUndoInsert = MultiHashIdxUndoInsert;
    multiHashFunc.idxUndoRemove = MultiHashIdxUndoRemove;
    multiHashFunc.idxUndoUpdate = MultiHashIdxUndoUpdate;
    multiHashFunc.idxGetKeyCount = MultiHashIdxGetKeyCount;
    multiHashFunc.idxStatView = MultiHashIdxStatView;
    multiHashFunc.idxGetPageSize = MultiHashIdxViewGetPageSize;
    multiHashFunc.idxGetEstimateMemSize = MultiHashIdxGetEstimateMemSize;
    multiHashFunc.idxGetCtxSize = MultiHashIdxGetCtxSize;
    IdxAmFuncRegister((uint8_t)MULTI_HASH_INDEX, &multiHashFunc);
}

Status MultiHashIdxCreate(SeRunCtxHdT seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr)
{
    if (idxCfg.idxConstraint != NON_UNIQUE) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "|Se-MH|: do not support unique index");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    Status ret = HacHashIdxCreateCommon(seRunCtx, idxCfg, idxShmAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Se-MH|: Unable to create MultiHashIdx.");
        return ret;
    }
    (void)DbAtomicInc(&HacGetStatsImpl()->multiHashIdxCnt);
    return GMERR_OK;
}

Status MultiHashIdxDrop(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    Status ret = HacHashIdxDropCommon(seRunCtx, idxShmAddr, MultiHashTableDrop);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Se-MH|: Unable to drop MultiHashIdx.");
        return ret;
    }
    (void)DbAtomicDec(&HacGetStatsImpl()->multiHashIdxCnt);
    return GMERR_OK;
}

Status MultiHashSecHashTruncate(DbMemCtxT *memCtx, HacHashTableT *table, ShmemPtrT bucket)
{
    if (HashIsShmValid(bucket)) {
        SecHashTableT *secHac = (SecHashTableT *)HashGetShmAddrAlign(bucket);
        if (secHac == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_MEMORY_OPERATE_FAILED, "|Se-Hac|: unsucc to convert shmptr to second hash table");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        HashTableTruncate(memCtx, &secHac->hashHac, table);
    }
    return GMERR_OK;
}

void MultiHashTableTruncate(DbMemCtxT *memCtx, HacHashTableT *table)
{
    if (!HashIsShmValid(table->hashHac.bucketShmPtr)) {
        return;
    }
    ShmemPtrT *srcBucket = (ShmemPtrT *)HashGetShmAddrAlign(table->hashHac.bucketShmPtr);
    if (srcBucket == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "srcBucket is null.");
        return;
    }
    for (uint32_t i = 0; i < table->hashHac.hashSize; i++) {
        MultiHashSecHashTruncate(memCtx, table, srcBucket[i]);
    }
}

Status MultiHashIdxTruncate(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    Status ret = HacHashIdxTruncateCommon(seRunCtx, idxShmAddr, MultiHashTableTruncate);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Se-MH|: Unable to truncate MultiHashIdx.");
        return ret;
    }
    return GMERR_OK;
}

inline void MultiHashIdxGetCtxSize(size_t *ctxSize, size_t *iterSize)
{
    *ctxSize = sizeof(HacCtxT);
    *iterSize = sizeof(MultiHashIteratorT);
}

Status MultiHashIdxOpen(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    Status ret = HacHashIdxOpenCommon(idxCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Se-MH|: Unable to open MultiHashIdx.");
        return ret;
    }
    return GMERR_OK;
}

void MultiHashIdxClose(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    HacHashIdxCloseCommon(idxCtx);
}

static Status HashInsertOrDeleteSecHash(
    IndexCtxT *idxCtx, IdxHacHashT *hashHac, IndexKeyT indexKey, TupleAddr addr, bool isInsert)
{
    uint32_t hashCode = HtGetSecHashCode(addr);
    uint32_t lockOffset = HashGetLockOffset(hashCode, hashHac);
    HashBucketLock(idxCtx, lockOffset, false);
    HashDMLParaT para = {.idxCtx = idxCtx, .hashHac = hashHac, .hashKey = indexKey, .hashCode = hashCode, .addr = addr};
    Status ret = isInsert ? HashInsertMatchEntry(&para) : HashDeleteMatchEntry(&para);
    HashBucketUnlock(idxCtx, lockOffset, false);
    return ret;
}

static Status MultiHashInsertSecEntry(HashDMLParaT *para, ShmemPtrT shmPtr)
{
    SecHashTableT *secHt = (SecHashTableT *)HashGetShmAddrAlign(shmPtr);
    if (SECUREC_UNLIKELY(secHt == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-MH|: unable to get hash's secHt");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    IdxHacHashT *hashHac = &secHt->hashHac;
    MultiHashExpandLock(para->idxCtx, true);
    Status ret = HashInsertOrDeleteSecHash(para->idxCtx, hashHac, para->hashKey, para->addr, true);
    if (ret == GMERR_OK) {
        (void)DbAtomicInc(&hashHac->entryNum);  // 插入成功就计数,二级hash
        HacHashTableT *hashTable = HacCastIdxAsHashTable(para->idxCtx->idxHandle);
        (void)DbAtomicInc(&hashTable->hashHac.entryNum);
    }
    MultiHashExpandUnlock(para->idxCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    return HashSecHtCheckExpand(para->idxCtx, hashHac);
}

Status MultiHashInsertSameSecHash(
    IndexCtxT *idxCtx, DbListT *list, IdxHacHashT *hashHac, IndexKeyT idxKey[], HpBatchOutT addr[])
{
    Status ret = GMERR_OK;
    MultiHashExpandLock(idxCtx, true);
    uint32_t listCnt = DbListGetItemCnt(list);
    uint32_t i = 0;
    for (; i < listCnt; i++) {
        ret = HashInsertOrDeleteSecHash(idxCtx, hashHac, idxKey[i], addr[i].addrOut, true);
        if (ret != GMERR_OK) {
            break;
        }
    }
    (void)DbAtomicAdd(&hashHac->entryNum, i);
    HacHashTableT *hashTable = HacCastIdxAsHashTable(idxCtx->idxHandle);
    (void)DbAtomicAdd(&hashTable->hashHac.entryNum, i);
    MultiHashExpandUnlock(idxCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    return HashSecHtCheckExpand(idxCtx, hashHac);
}

Status MultiHashDeleteSameSecHash(
    IndexCtxT *idxCtx, DbListT *list, IdxHacHashT *hashHac, IndexKeyT idxKey[], HpBatchOutT addr[])
{
    Status ret = GMERR_OK;
    MultiHashDMLParaT *dmlPara = (MultiHashDMLParaT *)DbListItem(list, 0);
    SecHashTableT *secHt = dmlPara->secHash;
    if (SECUREC_UNLIKELY(secHt == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-Hac|: DeleteSameSec get hash's secHt null");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    MultiHashExpandLock(idxCtx, true);
    uint32_t listCnt = DbListGetItemCnt(list);
    uint32_t succNum = 0;
    for (uint32_t i = 0; i < listCnt; i++) {
        ret = HashInsertOrDeleteSecHash(idxCtx, &secHt->hashHac, idxKey[i], addr[i].addrOut, false);
        if (ret == GMERR_OK) {
            succNum++;
        } else if (ret != GMERR_NO_DATA) {
            break;
        }
    }
    (void)DbAtomicSub(&secHt->hashHac.entryNum, succNum);
    HacHashTableT *hashTable = HacCastIdxAsHashTable(idxCtx->idxHandle);
    (void)DbAtomicSub(&hashTable->hashHac.entryNum, succNum);
    MultiHashExpandUnlock(idxCtx, true);
    if (ret == GMERR_NO_DATA) {
        return GMERR_OK;
    }
    return ret;
}

Status MultiHashCheckExpand(IndexCtxT *idxCtx, DbListT *list)
{
    Status ret = GMERR_OK;
    uint32_t listCnt = DbListGetItemCnt(list);
    for (uint32_t i = 0; i < listCnt; i++) {
        MultiHashDMLParaT *dmlPara = (MultiHashDMLParaT *)DbListItem(list, i);
        SecHashTableT *secHt = dmlPara->secHash;
        if (SECUREC_UNLIKELY(secHt == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-Hac|: CheckExpand get hash's secHt null");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        IdxHacHashT *hashHac = &secHt->hashHac;
        if (SECUREC_LIKELY(hashHac->entryNum < hashHac->expandNum)) {
            continue;
        }
        ret = HashSecHtCheckExpand(idxCtx, hashHac);
        if (ret != GMERR_OK) {
            break;
        }
    }
    return ret;
}

Status MultiHashInsertDifferentSecHash(IndexCtxT *idxCtx, DbListT *list, IndexKeyT idxKey[], HpBatchOutT addr[])
{
    Status ret = GMERR_OK;
    uint32_t listCnt = DbListGetItemCnt(list);
    MultiHashExpandLock(idxCtx, true);
    uint32_t i = 0;
    for (; i < listCnt; i++) {
        MultiHashDMLParaT *dmlPara = (MultiHashDMLParaT *)DbListItem(list, i);
        SecHashTableT *secHt = dmlPara->secHash;
        if (SECUREC_UNLIKELY(secHt == NULL)) {
            ret = GMERR_UNEXPECTED_NULL_VALUE;
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: InsertDifferentSec get hash's secHt null");
            break;
        }
        IdxHacHashT *hashHac = &secHt->hashHac;
        ret = HashInsertOrDeleteSecHash(idxCtx, hashHac, idxKey[i], addr[i].addrOut, true);
        if (ret != GMERR_OK) {
            break;
        }
        (void)DbAtomicInc(&hashHac->entryNum);
    }
    HacHashTableT *hashTable = HacCastIdxAsHashTable(idxCtx->idxHandle);
    (void)DbAtomicAdd(&hashTable->hashHac.entryNum, i);
    MultiHashExpandUnlock(idxCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = MultiHashCheckExpand(idxCtx, list);
    return ret;
}

Status MultiHashDeleteDifferentSecHash(IndexCtxT *idxCtx, DbListT *list, IndexKeyT idxKey[], HpBatchOutT addr[])
{
    uint32_t listCnt = DbListGetItemCnt(list);
    Status ret = GMERR_OK;
    MultiHashExpandLock(idxCtx, true);
    uint32_t i = 0;
    for (; i < listCnt; i++) {
        MultiHashDMLParaT *dmlPara = (MultiHashDMLParaT *)DbListItem(list, i);
        SecHashTableT *secHt = dmlPara->secHash;
        if (SECUREC_UNLIKELY(secHt == NULL)) {
            ret = GMERR_UNEXPECTED_NULL_VALUE;
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-Hac|: DeleteDifferentSec get hash's secHt null");
            break;
        }
        IdxHacHashT *hashHac = &secHt->hashHac;
        ret = HashInsertOrDeleteSecHash(idxCtx, hashHac, idxKey[i], addr[i].addrOut, false);
        if (ret == GMERR_NO_DATA) {
            ret = GMERR_OK;
            continue;
        }
        if (ret != GMERR_OK) {
            break;
        }
        (void)DbAtomicDec(&hashHac->entryNum);
    }
    HacHashTableT *hashTable = HacCastIdxAsHashTable(idxCtx->idxHandle);
    (void)DbAtomicSub(&hashTable->hashHac.entryNum, i);
    MultiHashExpandUnlock(idxCtx, true);
    return ret;
}

// 当前仅支持并发插入同一个二层hash
Status MultiHashBatchInsertOrDeleteSecEntry(IndexCtxT *idxCtx, DbListT *list)
{
    if (DbListGetItemCnt(list) == 0) {  // 没有二级hash
        return GMERR_OK;
    }
    Status ret = GMERR_OK;
    HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
    IndexKeyT *idxKey = hacCtx->idxKey;
    HpBatchOutT *addr = hacCtx->addr;
    if (hacCtx->isSameSecEntry) {  // 批量插入优化
        MultiHashDMLParaT *para = (MultiHashDMLParaT *)DbListItem(list, 0u);
        SecHashTableT *secHt = para->secHash;
        if (SECUREC_UNLIKELY(secHt == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_UNEXPECTED_NULL_VALUE, "|Se-Hac|: InsertOrDeleteSecEntry get hash's secHt null");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        IdxHacHashT *hashHac = &secHt->hashHac;
        if (hacCtx->isInsert) {
            ret = MultiHashInsertSameSecHash(idxCtx, list, hashHac, idxKey, addr);
        } else {
            ret = MultiHashDeleteSameSecHash(idxCtx, list, hashHac, idxKey, addr);
        }
    } else {
        ret = hacCtx->isInsert ? MultiHashInsertDifferentSecHash(idxCtx, list, idxKey, addr) :
                                 MultiHashDeleteDifferentSecHash(idxCtx, list, idxKey, addr);
    }
    return ret;
}

static Status MultiHashDeleteSecEntry(HashDMLParaT *para, ShmemPtrT shmPtr)
{
    SecHashTableT *secHt = (SecHashTableT *)HashGetShmAddrAlign(shmPtr);
    if (SECUREC_UNLIKELY(secHt == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-MH|: unable to get hash's secHt");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    IdxHacHashT *hashHac = &secHt->hashHac;
    MultiHashExpandLock(para->idxCtx, true);
    Status ret = HashInsertOrDeleteSecHash(para->idxCtx, hashHac, para->hashKey, para->addr, false);
    if (ret == GMERR_OK) {
        (void)DbAtomicDec(&hashHac->entryNum);  // 删除成功就计数,二级hash
        HacHashTableT *hashTable = HacCastIdxAsHashTable(para->idxCtx->idxHandle);
        (void)DbAtomicDec(&hashTable->hashHac.entryNum);
    }
    MultiHashExpandUnlock(para->idxCtx, true);
    return ret;
}

Status HashNewSecHashTable(HashDMLParaT *para, ShmemPtrT *shmPtr)
{
    DbMemCtxT *memCtx = DbGetShmemCtxById(para->idxCtx->idxHandle->shmemCtxId, DbGetProcGlobalId());
    if (memCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "|Se-MH|: hash's mem context is useless");
        return GMERR_INTERNAL_ERROR;
    }
    ShmemPtrT shm = HAC_INVALID_SHMPTR;
    SecHashTableT *secHt = (SecHashTableT *)HashAllocShmAddrAlign(memCtx, sizeof(SecHashTableT), &shm);
    if (secHt == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "|Se-MH|: Alloc HacHash Table unsucc");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    IdxHacHashT *hashHac = &secHt->hashHac;
    (void)memset_s(hashHac, sizeof(IdxHacHashT), 0, sizeof(IdxHacHashT));

    hashHac->hashSize = HashGetExpectSize(para->idxCtx->idxMetaCfg.indexCap / GetHacMgr()->multiHashBucketCnt);
    hashHac->lockOffset = (uint16_t)HtGetHashCodeWithShmPtr(shm);

    hashHac->bucketHead =
        (HacBucketT *)HashAllocShmAddrAlign(memCtx, hashHac->hashSize * HASH_BUCKET_SIZE, &hashHac->bucketShmPtr);
    if (hashHac->bucketHead == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "|Se-MH|: Alloc HacHash Table unsucc");
        HashShmAddrFree(memCtx, shm);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(hashHac->bucketHead, hashHac->hashSize * HASH_BUCKET_SIZE, 0, hashHac->hashSize * HASH_BUCKET_SIZE);
    hashHac->entryNum = 0u;
    for (uint32_t i = 0; i < hashHac->hashSize; i++) {
        (hashHac->bucketHead + i)->nextBucket = HAC_INVALID_LOGICADDR;
    }
    hashHac->expandNum = HashGetExpandSize(hashHac->hashSize);
    *shmPtr = shm;
    return GMERR_OK;
}

inline static void DbAtomicGetShmPtr(ShmemPtrT *srcShmPtr, ShmemPtrT *dstShmPtr)
{
    uint64_t shmAddr = DbAtomicGet64((void *)srcShmPtr);
    *dstShmPtr = *(ShmemPtrT *)&shmAddr;
}

static Status MultiHashGetFirstEntryForInsert(HashDMLParaT *para, ShmemPtrT *shmPtr)
{
    ShmemPtrT *bucketShm = HashGetBucketShmPtrByHashCode(para->hashHac, para->hashCode);
    DbAtomicGetShmPtr(bucketShm, shmPtr);

    if (HashIsShmValid(*shmPtr)) {  // 大部分场景的返回
        HacHashTableT *ht = HacCastIdxAsHashTable(para->idxCtx->idxHandle);
        (void)DbAtomicInc64(&ht->hashCollisionCnt);
        return GMERR_OK;
    }
    Status ret = HashNewSecHashTable(para, shmPtr);
    if (ret == GMERR_OK) {
        ShmemPtrT curShm = HAC_INVALID_SHMPTR;
        if (DbAtomicBoolCAS64((uint64_t *)bucketShm, *(uint64_t *)&curShm, *(uint64_t *)shmPtr)) {  // cas成功
            HacHashTableT *ht = (HacHashTableT *)HashGetShmAddr(para->idxCtx->idxShmAddr);
            if (ht == NULL) {
                DB_LOG_ERROR_AND_SET_LASTERR(
                    GMERR_MEMORY_OPERATE_FAILED, "|Se-Hac|: unsucc to convert shmptr to hac hash table");
                return GMERR_MEMORY_OPERATE_FAILED;
            }
            (void)DbAtomicInc(&ht->stats.multiHash.bucketUsed);
            return GMERR_OK;
        }
        // cas失败回收内存，流程已经保证memctx不为空
        DbMemCtxT *memCtx = DbGetShmemCtxById(para->idxCtx->idxHandle->shmemCtxId, DbGetProcGlobalId());
        if (memCtx == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_OUT_OF_MEMORY, "|Se-MH|: unable to get memCtx by Id(%u).", para->idxCtx->idxHandle->shmemCtxId);
            return GMERR_OUT_OF_MEMORY;
        }
        SecHashTableT *ht = (SecHashTableT *)HashGetShmAddrAlign(*shmPtr);
        if (ht == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "|Se-MH|: unable to get hash's secHac");
            return GMERR_OUT_OF_MEMORY;
        }
        HashShmAddrFree(memCtx, ht->hashHac.bucketShmPtr);
        HashShmAddrFree(memCtx, *shmPtr);
    }
    DbAtomicGetShmPtr(bucketShm, shmPtr);

    // 由于并发场景存在，所以在其他线程分配好对应内存空间后，可以在这里截断内存不足的问题
    if (HashIsShmValid(*shmPtr)) {
        return GMERR_OK;
    }
    return ret;
}

static void MultiHashGetFirstEntry(HashDMLParaT *para, ShmemPtrT *shmPtr)
{
    ShmemPtrT *bucketShm = HashGetBucketShmPtrByHashCode(para->hashHac, para->hashCode);
    DbAtomicGetShmPtr(bucketShm, shmPtr);
}

static Status HashIdxInsert(HashDMLParaT *para)
{
    uint8_t cmpRes = (uint8_t)INDEX_FILTER_SATISFIED;
    Status ret = IdxCheckFilterSatisfied(para->idxCtx, para->hashKey, &cmpRes);
    if (cmpRes == (uint8_t)INDEX_FILTER_UNSATISFIED || ret != GMERR_OK) {
        return ret;
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(para->idxCtx->idxHandle);
    DbAtomicInc64(&ht->hashInsertCnt);

    ShmemPtrT shmPtr;
    ret = MultiHashGetFirstEntryForInsert(para, &shmPtr);
    if (ret == GMERR_OK) {
        ret = MultiHashInsertSecEntry(para, shmPtr);
    }
    return ret;
}

static Status HashIdxDelete(HashDMLParaT *para)
{
    uint8_t cmpRes = (uint8_t)INDEX_FILTER_SATISFIED;
    Status ret = IdxCheckFilterSatisfied(para->idxCtx, para->hashKey, &cmpRes);
    if (cmpRes == (uint8_t)INDEX_FILTER_UNSATISFIED || ret != GMERR_OK) {
        return ret;
    }

    ShmemPtrT shmPtr;
    MultiHashGetFirstEntry(para, &shmPtr);
    if (!HashIsShmValid(shmPtr)) {
        return GMERR_NO_DATA;
    }
    return MultiHashDeleteSecEntry(para, shmPtr);
}

Status MultiHashIdxInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    DB_POINTER2(idxCtx, idxKey.keyData);
    uint32_t hashCode = MHGetHashCode(idxKey);
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    HashDMLParaT para = {
        .idxCtx = idxCtx, .hashHac = &ht->hashHac, .hashKey = idxKey, .hashCode = hashCode, .addr = addr};
    HashTableLock(idxCtx, false);
    Status ret = HashIdxInsert(&para);
    HashTableUnlock(idxCtx, false);
    return ret;
}

static Status MultiHashPreparePara(IndexCtxT *idxCtx, uint32_t batchNum, HashDMLParaT *para, DbListT *dbList)
{
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    *para = (HashDMLParaT){0};
    para->hashHac = &ht->hashHac;
    para->idxCtx = idxCtx;
    Status ret = DbListReserve(dbList, batchNum);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Se-MH|: reserve list unsucc.");
        return ret;
    }
    DbClearList(dbList);
    return GMERR_OK;
}

Status MultiHashBatchPrepare(
    IndexCtxT *idxCtx, IndexKeyT idxKey[], uint32_t batchNum, bool isInsert, bool *isSameSecEntry)
{
    HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
    HashDMLParaT para = {0};
    Status ret = MultiHashPreparePara(idxCtx, batchNum, &para, &hacCtx->list);
    if (ret != GMERR_OK) {
        return ret;
    }
    ShmemPtrT curShm = HAC_INVALID_SHMPTR;
    ShmemPtrT cacheShm = HAC_INVALID_SHMPTR;
    uint32_t i = 0;
    for (; i < batchNum; ++i) {
        uint8_t cmpRes = (uint8_t)INDEX_FILTER_SATISFIED;
        ret = IdxCheckFilterSatisfied(idxCtx, idxKey[i], &cmpRes);
        if (cmpRes == (uint8_t)INDEX_FILTER_UNSATISFIED || ret != GMERR_OK) {
            return ret;
        }

        para.hashCode = MHGetHashCode(idxKey[i]);
        para.hashKey = idxKey[i];
        if (isInsert) {
            ret = MultiHashGetFirstEntryForInsert(&para, &curShm);
            if (ret != GMERR_OK) {
                break;
            }
        } else {
            MultiHashGetFirstEntry(&para, &curShm);
            if (!HashIsShmValid(curShm)) {
                continue;
            }
        }
        if (*isSameSecEntry && i > 0 && !IsShmemPtrEqual(cacheShm, curShm)) {
            *isSameSecEntry = false;  // fes-path 同key优化
        }
        cacheShm = curShm;
        SecHashTableT *secHt = (SecHashTableT *)HashGetShmAddrAlign(curShm);
        if (SECUREC_UNLIKELY(secHt == NULL)) {
            ret = GMERR_UNEXPECTED_NULL_VALUE;
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "|Se-MH|: unable to get secHt for insert");
            break;
        }
        MultiHashDMLParaT dmlPara = {.secHash = secHt, .isInsert = isInsert};
        ret = DbAppendListItem(&hacCtx->list, &dmlPara);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|Se-MH|: batch delete unsucc");
            break;
        }
    }
    return ret;
}

inline static bool IsMultiHashSupportOffloading(void)
{
    return HacIsAcceleratorMode();
}

Status MultiHashIdxEndBatchModify(IndexCtxT *idxCtx)
{
    Status ret = MultiHashGetMsgAndReturn(idxCtx);
    ReleaseHacBatchExectueToken();
    MultiHashExpandUnlock(idxCtx, true);
    HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
    DbClearList(&hacCtx->list);
    HashTableUnlockForBatch(idxCtx, false);
    return ret;
}

Status MultiHashBatchInsertOrDelete(
    IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum, bool isInsert)
{
    DB_POINTER3(idxCtx, idxKey, addr);
    bool isSameSecEntry = true;
    HashTableLockForBatch(idxCtx, false);
    Status ret = MultiHashBatchPrepare(idxCtx, idxKey, batchNum, isInsert, &isSameSecEntry);
    if (ret == GMERR_OK) {  // 处理二级hash
        HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
        hacCtx->idxKey = idxKey;
        hacCtx->addr = addr;
        hacCtx->isInsert = isInsert;
        if (isInsert) {
            HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
            ht->hashInsertCnt += batchNum;
            hacCtx->hacType = MULTI_HASH_INSERT;
        } else {
            hacCtx->hacType = MULTI_HASH_DELETE;
        }
        if (IsMultiHashSupportOffloading() && GetHacBatchExectueToken()) {
            MultiHashExpandLock(idxCtx, true);
            (void)DbAtomicInc64(&HacGetStatsImpl()->batchHacCnt);
            ret = MultiHashBatchExecuteByHac(hacCtx, &hacCtx->list);
            if (ret == GMERR_OK && idxCtx->isHacBatchAsync) {  // 异步模式下，执行完直接返回
                return GMERR_OK;
            }
            ReleaseHacBatchExectueToken();
            MultiHashExpandUnlock(idxCtx, true);
        } else {
            (void)DbAtomicInc64(&HacGetStatsImpl()->batchSoftwareCnt);
            idxCtx->isHacBatchAsync = false;
            ret = MultiHashBatchInsertOrDeleteSecEntry(idxCtx, &hacCtx->list);
        }
        DbClearList(&hacCtx->list);
    }
    HashTableUnlockForBatch(idxCtx, false);
    return ret;
}

Status MultiHashIdxDelete(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr, IndexRemoveParaT removePara)
{
    DB_POINTER3(idxCtx, idxCtx->idxHandle, idxKey.keyData);
    if (!removePara.isErase) {  // 只支持erase删除，不支持标记删除
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "|Se-MH|: do not support mark delete");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    uint32_t hashCode = MHGetHashCode(idxKey);
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    HashDMLParaT para = {
        .idxCtx = idxCtx, .hashHac = &ht->hashHac, .hashKey = idxKey, .hashCode = hashCode, .addr = addr};

    HashTableLock(idxCtx, false);
    Status ret = HashIdxDelete(&para);
    HashTableUnlock(idxCtx, false);
    if (ret == GMERR_NO_DATA) {  // not find is normal
        return GMERR_OK;
    }
    return ret;
}

Status MultiHashIdxUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara)
{
    DB_POINTER2(idxCtx, idxCtx->idxHandle);
    if (!removePara.isErase) {  // 只支持erase删除，不支持标记删除
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "|Se-MH|: do not support mark delete");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    uint32_t hashCode = MHGetHashCode(updateInfo.oldIdxKey);
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    HashDMLParaT para = {.idxCtx = idxCtx,
        .hashHac = &ht->hashHac,
        .hashKey = updateInfo.oldIdxKey,
        .hashCode = hashCode,
        .addr = updateInfo.oldAddr};

    HashTableLock(idxCtx, false);
    Status ret = HashIdxDelete(&para);
    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        DB_LOG_ERROR(ret, "|Se-MH|: delete for update unsucc");
        HashTableUnlock(idxCtx, false);
        return ret;
    }
    para.hashKey = updateInfo.newIdxKey;
    para.hashCode = MHGetHashCode(updateInfo.newIdxKey);
    para.addr = updateInfo.newAddr;
    ret = HashIdxInsert(&para);
    HashTableUnlock(idxCtx, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|Se-MH|: insert for update unsucc");
        return ret;
    }
    return GMERR_OK;
}

static bool MultiHashInitIterAndIsFinished(IndexCtxT *idxCtx, ShmemPtrT shmPtr, MultiHashIteratorT *iter)
{
    DB_POINTER2(idxCtx, iter);
    iter->hashTable = (SecHashTableT *)HashGetShmAddrAlign(shmPtr);
    if (SECUREC_UNLIKELY(iter->hashTable == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-MH|: unexpected null hashtable");
        return false;
    }
    iter->secBucketHead = (HacBucketT *)HashGetShmAddrAlign(iter->hashTable->hashHac.bucketShmPtr);
    if (SECUREC_UNLIKELY(iter->secBucketHead == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-MH|: unexpected null hashtable");
        return false;
    }
    iter->curBucket = iter->secBucketHead;
    return true;
}

Status MultiHashIdxLookup(IndexCtxT *idxCtx, IndexKeyT idxKey, IdxTupleOrIterT *tupleOrIter, IdxValueTypeE *valueType)
{
    uint32_t hashCode = MHGetHashCode(idxKey);
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    HashDMLParaT dmlPara = {
        .idxCtx = idxCtx, .hashHac = &ht->hashHac, .hashKey = idxKey, .hashCode = hashCode, .addr = HEAP_INVALID_ADDR};
    ShmemPtrT curShm = HAC_INVALID_SHMPTR;
    MultiHashGetFirstEntry(&dmlPara, &curShm);

    if (!HashIsShmValid(curShm)) {
        *valueType = IDX_IS_NOT_FOUND;
    } else {
        HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
        MultiHashIteratorT *iterator = (MultiHashIteratorT *)HacGetScanItrHd(hacCtx);
        if (MultiHashInitIterAndIsFinished(idxCtx, curShm, iterator)) {
            *valueType = IDX_IS_ITERATOR;
            iterator->idxKey = idxKey;
            iterator->hashCode = hashCode;
            iterator->isEOF = false;
            tupleOrIter->iter = (IndexScanItrT)iterator;
        } else {
            *valueType = IDX_IS_NOT_FOUND;
        }
    }
    return GMERR_OK;
}

Status MultiHashIdxBatchLookup(IndexCtxT *idxCtx, IndexKeyT idxKey[], uint32_t batchNum, IdxBatchLookupParaT *para)
{
    DB_POINTER5(idxCtx, idxKey, para, para->iter, para->categorizeFunc);
    (void)DbAtomicInc64(&HacGetStatsImpl()->batchSoftwareCnt);
    HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
    hacCtx->reqNum = batchNum;
    hacCtx->iteratorSize = (uint32_t)sizeof(MultiHashIteratorT);
    Status ret = HacInitIteratorAddr(hacCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    IdxValueTypeE valueType = IDX_MAX_VALUE_TYPE;
    IdxTupleOrIterT tupleOrIter = {.addr = HEAP_INVALID_ADDR};
    HashTableLock(idxCtx, true);
    MultiHashExpandLock(idxCtx, true);
    for (uint32_t i = 0u; i < batchNum; i++) {
        hacCtx->queryIdx = i;
        ret = MultiHashIdxLookup(idxCtx, idxKey[i], &tupleOrIter, &valueType);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|Se-MH|: batchlookup unsucc");
            break;
        }
        ret = para->categorizeFunc(idxCtx, para->iter, tupleOrIter, i, valueType);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|Se-MH|: categorize unsucc");
            break;
        }
    }
    MultiHashExpandUnlock(idxCtx, true);
    HashTableUnlock(idxCtx, true);
    return ret;
}

static Status InsertOrDeleteSecEntryForUpdateInner(
    IndexCtxT *idxCtx, DbListT *list, uint32_t listCnt, IndexUpdateInfoT updateInfo[])
{
    Status ret = GMERR_OK;
    int32_t succNum = 0;
    for (uint32_t i = 0; i < listCnt; i++) {
        MultiHashDMLParaT *dmlPara = (MultiHashDMLParaT *)DbListItem(list, i);
        SecHashTableT *secHt = dmlPara->secHash;
        if (SECUREC_UNLIKELY(secHt == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-MH|: unable to get secHt for delete");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        IdxHacHashT *hashHac = &secHt->hashHac;
        uint32_t offset = i >> 1;  // 更新操作，每两条共用一个offset
        if (dmlPara->isInsert) {   // 插入时匹配新值
            ret = HashInsertOrDeleteSecHash(
                idxCtx, hashHac, updateInfo[offset].newIdxKey, updateInfo[offset].newAddr, true);
        } else {  // 删除时匹配旧值
            ret = HashInsertOrDeleteSecHash(
                idxCtx, hashHac, updateInfo[offset].oldIdxKey, updateInfo[offset].oldAddr, false);
        }
        if (ret == GMERR_NO_DATA) {
            ret = GMERR_OK;
            continue;
        }
        if (ret != GMERR_OK) {
            break;
        }
        if (dmlPara->isInsert) {
            (void)DbAtomicInc(&hashHac->entryNum);
            succNum++;
        } else {
            (void)DbAtomicDec(&hashHac->entryNum);
            succNum--;
        }
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    if (succNum > 0) {
        (void)DbAtomicAdd(&ht->hashHac.entryNum, (uint32_t)succNum);
    } else if (succNum < 0) {
        (void)DbAtomicSub(&ht->hashHac.entryNum, (uint32_t)-succNum);
    }

    return ret;
}

static Status MultiHashBatchInsertOrDeleteSecEntryForUpdate(
    IndexCtxT *idxCtx, DbListT *list, IndexUpdateInfoT updateInfo[])
{
    uint32_t listCnt = DbListGetItemCnt(list);
    if (listCnt == 0) {  // 没有二级hash
        return GMERR_OK;
    }
    Status ret = GMERR_OK;
    MultiHashExpandLock(idxCtx, true);
    ret = InsertOrDeleteSecEntryForUpdateInner(idxCtx, list, listCnt, updateInfo);
    MultiHashExpandUnlock(idxCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = MultiHashCheckExpand(idxCtx, list);
    return ret;
}

Status MultiHashBatchUpdatePrepare(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo[], uint32_t batchNum)
{
    HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
    HashDMLParaT para = {0};
    // 一个更新相当于一个删除加一个插入，因此一个请求算两个索引子操作，batchNum需要乘以2
    Status ret = MultiHashPreparePara(idxCtx, batchNum << 1u, &para, &hacCtx->list);
    if (ret != GMERR_OK) {
        return ret;
    }
    ShmemPtrT curShm = HAC_INVALID_SHMPTR;
    for (uint32_t i = 0; i < batchNum; ++i) {
        para.hashCode = MHGetHashCode(updateInfo[i].oldIdxKey);
        para.hashKey = updateInfo[i].oldIdxKey;
        para.addr = updateInfo[i].oldAddr;
        MultiHashGetFirstEntry(&para, &curShm);
        SecHashTableT *secHt = (SecHashTableT *)HashGetShmAddrAlign(curShm);
        if (SECUREC_UNLIKELY(secHt == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-MH|: unable to get secHt for delete");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        MultiHashDMLParaT dmlPara = {.secHash = secHt, .isInsert = false};
        ret = DbAppendListItem(&hacCtx->list, &dmlPara);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|Se-MH|: batch update unsucc");
            return ret;
        }
        para.hashCode = MHGetHashCode(updateInfo[i].newIdxKey);
        para.hashKey = updateInfo[i].newIdxKey;
        para.addr = updateInfo[i].newAddr;
        ret = MultiHashGetFirstEntryForInsert(&para, &curShm);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|Se-MH|: batch update unsucc");
            return ret;
        }
        secHt = (SecHashTableT *)HashGetShmAddrAlign(curShm);
        if (SECUREC_UNLIKELY(secHt == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-MH|: unable to get secHt for delete");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        dmlPara.secHash = secHt;
        dmlPara.isInsert = true;
        ret = DbAppendListItem(&hacCtx->list, &dmlPara);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|Se-MH|: batch update unsucc");
            return ret;
        }
    }
    return ret;
}

static Status MultiHashBatchUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo[], uint32_t batchNum)
{
    HacCtxT *hacCtx = HacCastCtxAsHacCtx(idxCtx);
    HashTableLockForBatch(idxCtx, false);
    Status ret = MultiHashBatchUpdatePrepare(idxCtx, updateInfo, batchNum);
    if (ret == GMERR_OK) {  // 处理二级hash
        HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
        ht->hashInsertCnt += batchNum;
        ret = MultiHashBatchInsertOrDeleteSecEntryForUpdate(idxCtx, &hacCtx->list, updateInfo);
    }
    HashTableUnlockForBatch(idxCtx, false);
    if (ret == GMERR_NO_DATA) {
        return GMERR_OK;
    }
    return ret;
}

Status MultiHashIdxBatchUpdate(
    IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo[], uint32_t batchNum, IndexRemoveParaT removePara)
{
    DB_POINTER(idxCtx);
    if (!removePara.isErase) {  // 只支持erase删除，不支持标记删除
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "|Se-MH|: do not support mark delete");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    return MultiHashBatchUpdate(idxCtx, updateInfo, batchNum);
}

Status MultiHashIdxBatchInsert(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum)
{
    DB_POINTER3(idxCtx, idxKey, addr);
    return MultiHashBatchInsertOrDelete(idxCtx, idxKey, addr, batchNum, true);
}

Status MultiHashIdxBatchDelete(
    IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum, IndexRemoveParaT removePara)
{
    DB_POINTER3(idxCtx, idxKey, addr);
    if (!removePara.isErase) {  // 只支持erase删除，不支持标记删除
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "|Se-MH|: do not support mark delete");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    return MultiHashBatchInsertOrDelete(idxCtx, idxKey, addr, batchNum, false);
}

#ifndef NDEBUG  // DEBUG下用于验证插入删除是否成功,不加锁，只做验证。
void HashGetSlotFromBucket(HashDMLParaT *para, ShmemPtrT shm, bool *isFound)
{
    SecHashTableT *secHt = (SecHashTableT *)HashGetShmAddrAlign(shm);
    if (SECUREC_UNLIKELY(secHt == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-MH|: unable to get hash's secHt");
        return;
    }
    uint32_t hashCode = HtGetSecHashCode(para->addr);
    HashDMLParaT para2Lookup = {.idxCtx = para->idxCtx,
        .hashHac = &secHt->hashHac,
        .hashKey = para->hashKey,
        .hashCode = hashCode,
        .addr = para->addr};
    *isFound = HashSearchMatchEntry(&para2Lookup) == GMERR_OK;
}

Status MultiHashIdxLookupWithAddr(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr, bool *isFound)
{
    *isFound = false;
    uint32_t hashCode = MHGetHashCode(idxKey);
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    HashDMLParaT para = {
        .idxCtx = idxCtx, .hashHac = &ht->hashHac, .hashKey = idxKey, .hashCode = hashCode, .addr = addr};

    ShmemPtrT curShm = HAC_INVALID_SHMPTR;
    MultiHashGetFirstEntry(&para, &curShm);
    if (!HashIsShmValid(curShm)) {
        return GMERR_NO_DATA;
    }
    HashGetSlotFromBucket(&para, curShm, isFound);
    return GMERR_OK;
}
#endif

inline static MultiHashIteratorT *MultiHashIterInit(IndexCtxT *idxCtx)
{
    DB_POINTER(idxCtx);
    MultiHashIteratorT *iter = (MultiHashIteratorT *)(void *)IdxGetIterator(idxCtx);
    DB_POINTER(iter);
    *iter = (MultiHashIteratorT){0};
    return iter;
}

Status MultiHashIdxBeginScan(IndexCtxT *idxCtx, IndexScanCfgT scanCfg, IndexScanItrT *iter)
{
    DB_POINTER2(idxCtx, scanCfg.leftKey->keyData);
    IndexKeyT idxKey = *scanCfg.leftKey;
    uint32_t hashCode = MHGetHashCode(idxKey);
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    HashDMLParaT para = {
        .idxCtx = idxCtx, .hashHac = &ht->hashHac, .hashKey = idxKey, .hashCode = hashCode, .addr = HEAP_INVALID_ADDR};
    HashTableLock(idxCtx, true);
    ShmemPtrT curShm = HAC_INVALID_SHMPTR;
    MultiHashGetFirstEntry(&para, &curShm);
    HashTableUnlock(idxCtx, true);

    MultiHashIteratorT **itr = (MultiHashIteratorT **)iter;
    if (!HashIsShmValid(curShm)) {
        *itr = NULL;
    } else {
        MultiHashIteratorT *iterator = MultiHashIterInit(idxCtx);
        MultiHashExpandLock(idxCtx, true);
        if (MultiHashInitIterAndIsFinished(idxCtx, curShm, iterator)) {
            iterator->idxKey = *scanCfg.leftKey;
            iterator->hashCode = hashCode;
            // 加限制扩容的锁，与HashTableLock没有加锁先后的约束，且已保证不会死锁，MultiHashIdxEndScan时释放
            iterator->isEOF = false;
            *itr = iterator;
        } else {
            MultiHashExpandUnlock(idxCtx, true);
            *itr = NULL;
        }
    }
    return GMERR_OK;
}

// 当前扫描间隙会持有限制扩容锁，必须endscan或客户端异常退出才会释放锁
Status MultiHashIdxScan(IndexCtxT *idxCtx, IndexScanItrT iter, TupleAddr *addr, bool *isFound)
{
    DB_POINTER3(idxCtx, addr, isFound);
    *isFound = false;
    if (iter == NULL) {
        return GMERR_OK;
    }
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    if (ht->idxBase.indexCfg.idxConstraint != NON_UNIQUE) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "|Se-MH|: key type doesn't match");
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    MultiHashIteratorT *itr = (MultiHashIteratorT *)iter;
    if (itr->idxKey.keyData == NULL || itr->idxKey.keyLen == 0u) {
        DB_LOG_DBG_ERROR(GMERR_NO_DATA, "|Se-MH|: key buffer is useless");
        return GMERR_NO_DATA;
    }
    if (itr->isEOF) {
        return GMERR_OK;
    }
    Status ret = GMERR_OK;
    HashTableLock(idxCtx, true);
    ret = HashFetchNext(idxCtx, itr, addr, isFound);
    HashTableUnlock(idxCtx, true);
    return ret;
}

void MultiHashIdxEndScan(const IndexCtxT *idxCtx, IndexScanItrT iter)
{
    if (iter != NULL) {
        MultiHashExpandUnlock(idxCtx, true);
    }
}

static void MultiHashCalcMemStats(HacHashTableT *ht, IndexStatisticsT *idxStat, IndexHashPerfStatT *perfStat)
{
    uint64_t *minTableSize = &idxStat->multiHashIndex.minTableSize;
    uint64_t *maxTableSize = &idxStat->multiHashIndex.maxTableSize;
    uint64_t *avgTableSize = &idxStat->multiHashIndex.avgTableSize;
    uint64_t *usedMem = &perfStat->usedMemSize;
    uint64_t *totalMem = &perfStat->pageSize;
    ShmemPtrT *srcBucket = (ShmemPtrT *)HashGetShmAddrAlign(ht->hashHac.bucketShmPtr);
    if (srcBucket == NULL || ht->stats.multiHash.bucketUsed == 0u) {
        *minTableSize = 0u;
        *maxTableSize = 0u;
        *avgTableSize = 0u;
        *usedMem = (uint64_t)sizeof(HacHashTableT);
        *totalMem = (uint64_t)sizeof(HacHashTableT);
        return;
    }

    uint64_t totalBucketNum = 0u;
    uint64_t minBucketNum = DB_MAX_UINT64;
    uint64_t maxBucketNum = 0u;
    for (uint32_t i = 0u; i < ht->hashHac.hashSize; ++i) {
        SecHashTableT *secHac = (SecHashTableT *)HashGetShmAddrAlign(srcBucket[i]);
        if (secHac == NULL) {
            continue;
        }
        if (secHac->hashHac.hashSize > maxBucketNum) {
            maxBucketNum = (uint64_t)secHac->hashHac.hashSize;
        }
        if (secHac->hashHac.hashSize < minBucketNum) {
            minBucketNum = (uint64_t)secHac->hashHac.hashSize;
        }
        totalBucketNum += (uint64_t)secHac->hashHac.hashSize;
    }
    if (minBucketNum == DB_MAX_UINT64) {
        minBucketNum = 0u;
    }
    *minTableSize = minBucketNum * (uint64_t)sizeof(HacBucketT) + (uint64_t)sizeof(SecHashTableT);
    *maxTableSize = maxBucketNum * (uint64_t)sizeof(HacBucketT) + (uint64_t)sizeof(SecHashTableT);
    if (totalBucketNum < DB_MAX_UINT64 / (uint64_t)sizeof(HacBucketT)) {  // 防止溢出
        *avgTableSize = (totalBucketNum) * (uint64_t)sizeof(HacBucketT) / (uint64_t)ht->stats.multiHash.bucketUsed +
                        (uint64_t)sizeof(SecHashTableT);
    } else {
        *avgTableSize = (totalBucketNum) / (uint64_t)ht->stats.multiHash.bucketUsed * (uint64_t)sizeof(HacBucketT) +
                        (uint64_t)sizeof(SecHashTableT);
    }

    uint64_t tableMetaMemUsed =
        (uint64_t)sizeof(HacHashTableT) + (uint64_t)ht->stats.multiHash.bucketUsed * (uint64_t)sizeof(SecHashTableT);
    uint64_t entrySize =
        (HacIsTupleMode64() ? (uint64_t)HAC_HASH_PER_ENTRY_SIZE_64_BITS : (uint64_t)HAC_HASH_PER_ENTRY_SIZE_32_BITS);
    *usedMem = tableMetaMemUsed + (uint64_t)ht->hashHac.entryNum * entrySize;
    *totalMem = tableMetaMemUsed + totalBucketNum * (uint64_t)sizeof(HacBucketT);
}

Status MultiHashIdxStatView(ShmemPtrT idxShmAddr, uint16_t instanceId, IndexStatisticsT *idxStat)
{
    DB_POINTER(idxStat);
    HacHashTableT *ht = (HacHashTableT *)HashGetShmAddr(idxShmAddr);
    if (ht == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-MH|: Hash table is useless");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(instanceId);
    if (seInstance == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-MH|: hash's SE Instance is useless");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint8_t hashCollisionRate = 0;
    if (ht->hashInsertCnt != 0u) {
        hashCollisionRate = (uint8_t)((ht->hashCollisionCnt * DB_PERCENTAGE_BASE) / ht->hashInsertCnt);
    }
    IndexHashPerfStatT *indexHashPerfStat = &idxStat->multiHashIndex.indexHashPerfStat;
    indexHashPerfStat->perPageSize = DB_MAX_UINT32;
    indexHashPerfStat->pageCount = DB_MAX_UINT32;
    indexHashPerfStat->hashInsertCnt = ht->hashInsertCnt;
    indexHashPerfStat->hashCollisionCnt = ht->hashCollisionCnt;
    indexHashPerfStat->hashCollisionRate = hashCollisionRate;
    idxStat->multiHashIndex.cacheLineLen = (uint32_t)HASH_BUCKET_SIZE;
    idxStat->multiHashIndex.bucketCnt = ht->hashHac.hashSize;
    idxStat->multiHashIndex.bucketUsed = ht->stats.multiHash.bucketUsed;
    MultiHashCalcMemStats(ht, idxStat, indexHashPerfStat);
    return GMERR_OK;
}

Status MultiHashIdxViewGetPageSize(ShmemPtrT idxShmAddr, uint16_t instanceId, uint64_t *idxPageSize)
{
    DB_POINTER(idxPageSize);
    const HacHashTableT *ht = (HacHashTableT *)HashGetShmAddr(idxShmAddr);
    if (ht == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "|Se-MH|: Hash table is useless");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *idxPageSize = (uint64_t)(ht->hashHac.hashSize * HASH_BUCKET_SIZE);
    return GMERR_OK;
}

Status MultiHashIdxGetKeyCount(IndexCtxT *idxCtx, IndexKeyT key, uint64_t *count)
{
    DB_POINTER3(idxCtx, key.keyData, count);
    uint32_t hashCode = MHGetHashCode(key);
    HacHashTableT *ht = HacCastIdxAsHashTable(idxCtx->idxHandle);
    HashDMLParaT para = {
        .idxCtx = idxCtx, .hashHac = &ht->hashHac, .hashKey = key, .hashCode = hashCode, .addr = HEAP_INVALID_ADDR};

    HashTableLock(idxCtx, true);
    ShmemPtrT curShm = HAC_INVALID_SHMPTR;
    MultiHashGetFirstEntry(&para, &curShm);
    if (!HashIsShmValid(curShm)) {
        *count = 0;
        HashTableUnlock(idxCtx, true);
        return GMERR_OK;
    }

    MultiHashIteratorT itr = (MultiHashIteratorT){0};
    itr.idxKey = key;
    MultiHashExpandLock(idxCtx, true);
    if (!MultiHashInitIterAndIsFinished(idxCtx, curShm, &itr)) {
        *count = 0;
        MultiHashExpandUnlock(idxCtx, true);
        HashTableUnlock(idxCtx, true);
        return GMERR_OK;
    }

    Status ret = GMERR_OK;
    TupleAddr addr = HEAP_INVALID_ADDR;
    bool isFound = false;
    while (ret == GMERR_OK && !itr.isEOF) {
        isFound = false;
        ret = HashFetchNext(idxCtx, &itr, &addr, &isFound);
        if (isFound) {
            (*count)++;
        }
    }
    MultiHashExpandUnlock(idxCtx, true);
    HashTableUnlock(idxCtx, true);
    return GMERR_OK;
}

Status MultiHashIdxUndoInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    DB_POINTER3(idxCtx, idxCtx->idxHandle, idxKey.keyData);
    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    return MultiHashIdxDelete(idxCtx, idxKey, addr, removePara);
}

Status MultiHashIdxUndoRemove(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    DB_POINTER3(idxCtx, idxCtx->idxHandle, idxKey.keyData);
    return MultiHashIdxInsert(idxCtx, idxKey, addr);
}

Status MultiHashIdxUndoUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara)
{
    DB_POINTER(idxCtx);
    DB_UNUSED(removePara);
    Status ret = MultiHashIdxUndoInsert(idxCtx, updateInfo.oldIdxKey, updateInfo.oldAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "|Se-MH|: MultiHash undo update unsucc when delete, indexId %" PRIu32, idxCtx->idxMetaCfg.indexId);
    }
    ret = MultiHashIdxInsert(idxCtx, updateInfo.newIdxKey, updateInfo.newAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "|Se-MH|: MultiHash undo update unsucc when insert, indexId %" PRIu32, idxCtx->idxMetaCfg.indexId);
    }
    return ret;
}

Status MultiHashIdxGetEstimateMemSize(
    uint16_t instanceId, IndexMetaCfgT idxMetaCfg, uint64_t count, uint32_t keyLen, uint64_t *size)
{
    DB_POINTER(size);
    return HashGetEstimateMemSize(idxMetaCfg, count, keyLen, size);
}

#endif
