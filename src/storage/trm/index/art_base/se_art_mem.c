/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: se_art_mem.c
 * Description: commom memory manage module for ART
 * Author: chenjianhui
 * Create: 2020/3/12
 */

#include "se_page_mgr.h"
#include "se_art_mem.h"

#ifdef __cplusplus
extern "C" {
#endif

static void ArtMemInitMgr(const ArtCfgT *artCfg, const SeInstanceT *seInstance, ArtMemMgrT *artMemMgr)
{
    DB_POINTER3(artCfg, seInstance, artMemMgr);
    artMemMgr->spaceId = artCfg->spaceId;
    artMemMgr->mode = artCfg->mode;
    artMemMgr->seInstanceId = seInstance->instanceId;
    artMemMgr->fileId = artCfg->fileId;
    artMemMgr->artShmArrayId = seInstance->indexArtShmMemCtxId;

    artMemMgr->pageDivideLength = ART_PAGE_DIVIDE_LENGTH;
    artMemMgr->pageBaseSize = (uint16_t)sizeof(PageHeadT);

    artMemMgr->blockIdUsed = -1;
    artMemMgr->pageIdUsed = SE_INVALID_PAGE_ADDR;
    artMemMgr->pageReleasedCount = 0;
    artMemMgr->pageAllocatedCount = 0;
    artMemMgr->scaleInCount = 0;

    artMemMgr->pageSize = SIZE_K(seInstance->seConfig.pageSize);
    artMemMgr->bitmapArrayLength = (uint16_t)((artMemMgr->pageSize / ART_PAGE_DIVIDE_LENGTH) / ART_BITMAP_VAR_LENGTH);
}

Status ArtMemMgrShmArraysInit(ArtMemMgrT *artMemMgr)
{
    DB_POINTER(artMemMgr);
    /* 三处均在drop时释放，ArtMemDestroy */
    uint32_t itemSize = (uint32_t)sizeof(ArtMemPageAddrT);
    Status ret =
        DbShmArrayInit(&artMemMgr->pageIdArray, ART_SHMARRAY_CAPACITY, itemSize, artMemMgr->artShmArrayId, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }
    itemSize = (uint32_t)sizeof(uint32_t);
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(artMemMgr->seInstanceId);
    if (seInstance == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "SE-ART, AI: hash's SE instance is novalid");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    ret =
        DbShmArrayInit(&artMemMgr->pageToReleaseArray, ART_SHMARRAY_CAPACITY, itemSize, artMemMgr->artShmArrayId, NULL);
    if (ret != GMERR_OK) {
        DbShmArrayDestroy(&artMemMgr->pageIdArray, seInstance->dbInstance);
        return ret;
    }
    ret = DbShmArrayInit(&artMemMgr->pageToMergeArray, ART_SHMARRAY_CAPACITY, itemSize, artMemMgr->artShmArrayId, NULL);
    if (ret != GMERR_OK) {
        DbShmArrayDestroy(&artMemMgr->pageIdArray, seInstance->dbInstance);
        DbShmArrayDestroy(&artMemMgr->pageToReleaseArray, seInstance->dbInstance);
        return ret;
    }
    ret = DbShmArrayInit(&artMemMgr->artMVPageArray, ART_SHMARRAY_CAPACITY, (uint32_t)sizeof(ArtMVPageDescT),
        artMemMgr->artShmArrayId, NULL);
    if (ret != GMERR_OK) {
        DbShmArrayDestroy(&artMemMgr->pageToMergeArray, seInstance->dbInstance);
        DbShmArrayDestroy(&artMemMgr->pageIdArray, seInstance->dbInstance);
        DbShmArrayDestroy(&artMemMgr->pageToReleaseArray, seInstance->dbInstance);
        return ret;
    }
    return GMERR_OK;
}

inline static void ArtMemMgrShmArraysReset(ArtMemMgrT *artMemMgr)
{
    DbShmArrayReset(&artMemMgr->artMVPageArray);
    DbShmArrayReset(&artMemMgr->pageIdArray);
    DbShmArrayReset(&artMemMgr->pageToReleaseArray);
    DbShmArrayReset(&artMemMgr->pageToMergeArray);
}
/*
 * get minimun N. requirement: N % pageDivideLength == 0 and N >= nodeSize
 */
static uint32_t ArtNodeAlineSize(uint32_t nodeSize, uint16_t pageDivideLength)
{
    DB_ASSERT(pageDivideLength != 0);
    uint32_t alineNodeSize = nodeSize;
    if (alineNodeSize % pageDivideLength != 0) {
        // fixedNodeSize can't exceed size of free node
        alineNodeSize = (alineNodeSize < sizeof(FreeNodeT)) ? sizeof(FreeNodeT) : alineNodeSize;
        // get min num >= (nodeSize / 8), return num * 8
        return (uint32_t)((alineNodeSize & ((uint16_t)(~(pageDivideLength - 1u)))) + pageDivideLength);
    } else {
        return alineNodeSize;
    }
}

inline static void ArtMemInitFreeList(ArtMemMgrT *artMemMgr, uint32_t *nodesSize)
{
    FreeListT freeList = {.firstNodePtr = ART_MEM_NULL_NODE_PTR, .nodeSize = 0u, .nodeCount = 0u};
    for (uint16_t i = 0; i < ART_NODE_TYPE_NUM; i++) {
        freeList.nodeSize = ArtNodeAlineSize(nodesSize[i], artMemMgr->pageDivideLength);
        artMemMgr->freeList[i] = freeList;
    }
}

Status ArtMemInit(const ArtCfgT *artCfg, ArtMemMgrT *artMemMgr, uint32_t *nodesSize)
{
    DB_POINTER(artCfg);

    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(artCfg->instanceId);
    if (seInstance == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    ArtMemInitMgr(artCfg, seInstance, artMemMgr);
    ArtMemInitFreeList(artMemMgr, nodesSize);
    Status ret = ArtMemMgrShmArraysInit(artMemMgr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "SE-Art, AMI unable to init art shm arrs.");
        return ret;
    }
    artMemMgr->version = 1;  // 版本从1开始，非0
    return GMERR_OK;
}

Status ArtMemReset(SeInstanceT *seInstance, const ArtCfgT *artCfg, ArtMemMgrT *artMemMgr, uint32_t *nodesSize)
{
    DB_POINTER(artCfg);
    ArtMemInitMgr(artCfg, seInstance, artMemMgr);
    ArtMemInitFreeList(artMemMgr, nodesSize);
    ArtMemMgrShmArraysReset(artMemMgr);
    artMemMgr->version++;
    return GMERR_OK;
}

uint8_t *ArtMemGetPageByPageId(const ArtMemMgrRunCtxT *memMgrCtx, PageIdT pageId)
{
    DB_POINTER3(memMgrCtx, memMgrCtx->artMemMgr, memMgrCtx->mdMgr);
    uint8_t *page = NULL;
    StatusInter retInter = MdGetPage(memMgrCtx->mdMgr, pageId, &page, ENTER_PAGE_NORMAL, true);
    if (SECUREC_UNLIKELY(retInter != STATUS_OK_INTER)) {
        DB_LOG_ERROR_AND_SET_LASTERR(DbGetExternalErrno(retInter),
            "Art get page unsucc. pageId: (device:%" PRIu32 " blockId:%" PRIu32, pageId.deviceId, pageId.blockId);
        return NULL;
    }
    DB_ASSERT(page != NULL);
    return page;
}

Status ArtMemGetPageByBlockId(const ArtMemMgrRunCtxT *memMgrCtx, uint32_t blockId, uint8_t **addr)
{
    DB_POINTER4(memMgrCtx, addr, memMgrCtx->artMemMgr, memMgrCtx->mdMgr);
    DbArrayAddrT arrAddrItem = {{.shmPtr = 0}};
    DbShmArrayT *pageIdArray = &memMgrCtx->artMemMgr->pageIdArray;
    ArtMemPageAddrT *logicAddr = (ArtMemPageAddrT *)DbShmArrayGetItemById(pageIdArray, blockId, &arrAddrItem);
    if (SECUREC_UNLIKELY(logicAddr == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "the logical addr of the Node is null");
        return GMERR_INTERNAL_ERROR;
    }
    if (SECUREC_UNLIKELY(logicAddr->hasFree)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NO_DATA, "Art get page has freed. blockId: %" PRIu32, blockId);
        return GMERR_NO_DATA;
    }
    uint8_t *page = NULL;
    StatusInter retInter = MdGetPage(memMgrCtx->mdMgr, logicAddr->pageId, &page, ENTER_PAGE_NORMAL, true);
    if (SECUREC_UNLIKELY(retInter != STATUS_OK_INTER)) {
        Status ret = DbGetExternalErrno(retInter);
        DB_LOG_ERROR_AND_SET_LASTERR(ret,
            "Art get page unsucc. pageId: (device:%" PRIu32 " blockId:%" PRIu32 "), blockId: %" PRIu32,
            logicAddr->pageId.deviceId, logicAddr->pageId.blockId, blockId);
        return ret;
    }
    *addr = page;
    return GMERR_OK;
}

Status ArtMemFreePageById(ArtMemMgrRunCtxT *memMgrCtx, uint32_t blockId)
{
    DB_POINTER3(memMgrCtx, memMgrCtx->artMemMgr, memMgrCtx->mdMgr);
    DbArrayAddrT arrAddrItem = {{.shmPtr = 0}};
    DbShmArrayT *pageIdArray = &memMgrCtx->artMemMgr->pageIdArray;
    ArtMemPageAddrT *logicAddr = (ArtMemPageAddrT *)DbShmArrayGetItemById(pageIdArray, blockId, &arrAddrItem);
    if (logicAddr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "the logical addr of the mem page is null");
        return GMERR_INTERNAL_ERROR;
    }

    if (logicAddr->hasFree) {
        return GMERR_OK;
    }
    FreePageParamT freePageParam = SeInitFreePageParam(memMgrCtx->artMemMgr->spaceId, logicAddr->pageId,
        DbGetInstanceByMemCtx(memMgrCtx->memctx), NULL, SE_INVALID_LABEL_ID, false);
    StatusInter ret = MdFreePage(memMgrCtx->mdMgr, &freePageParam);
    if (ret == STATUS_OK_INTER) {
        logicAddr->hasFree = true;
        memMgrCtx->artMemMgr->version++;
    }

    return DbGetExternalErrno(ret);
}

Status ArtMemGetNodeAddrByBlockId(ArtMemMgrRunCtxT *memMgrCtx, ArtNodePtrT nodePtr, uint8_t **addr)
{
    DB_POINTER2(memMgrCtx, addr);
    DbMemAddrT memAddr = IdxPageCacheFindPage(&memMgrCtx->pageCache, nodePtr.blockId, memMgrCtx->artMemMgr->version);
    if (SECUREC_LIKELY(memAddr.virtAddr != NULL)) {
        *addr = memAddr.virtAddr + nodePtr.offset;
        return GMERR_OK;
    }
    Status ret = ArtMemGetPageByBlockId(memMgrCtx, nodePtr.blockId, &memAddr.virtAddr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK || memAddr.virtAddr == NULL)) {
        return ret;
    }
    IdxPageCacheAdd(&memMgrCtx->pageCache, nodePtr.blockId, memAddr.virtAddr, SE_INVALID_PAGE_ADDR);
    *addr = memAddr.virtAddr + nodePtr.offset;
    return GMERR_OK;
}

static void ArtSetBitmap(const ArtMemMgrT *artMemMgr, uint32_t *bitmap, uint32_t offset, bool setBit)
{
    DB_POINTER2(artMemMgr, bitmap);
    uint16_t pageDivideLength = artMemMgr->pageDivideLength;
    DB_ASSERT(pageDivideLength != 0);
    uint16_t idx = (uint16_t)(offset / (pageDivideLength * ART_BITMAP_VAR_LENGTH));
    uint16_t loc = (uint16_t)((offset / pageDivideLength) & (ART_BITMAP_VAR_LENGTH - 1u));
    if (setBit) {
        // set 1 on mapping bit from offset
        bitmap[idx] = bitmap[idx] | (1u << ((ART_BITMAP_VAR_LENGTH - loc) - 1u));
    } else {
        // set 0 on mapping bit from offset
        bitmap[idx] = bitmap[idx] & ~(1u << ((ART_BITMAP_VAR_LENGTH - loc) - 1u));
    }
}

bool ArtMemIsBitMapSet(const ArtMemMgrT *artMemMgr, const uint32_t *bitMap, uint16_t offset)
{
    DB_POINTER2(artMemMgr, bitMap);
    uint16_t pageDivideLength = artMemMgr->pageDivideLength;
    DB_ASSERT(pageDivideLength != 0);
    uint16_t idx = (uint16_t)(offset / (pageDivideLength * ART_BITMAP_VAR_LENGTH));
    uint16_t loc = (uint16_t)((offset / pageDivideLength) & (ART_BITMAP_VAR_LENGTH - 1u));
    return bitMap[idx] & (1u << ((ART_BITMAP_VAR_LENGTH - loc) - 1u));
}

static inline void ArtInitBitMap(uint32_t *bitMap, uint16_t bitMapLength)
{
    DB_POINTER(bitMap);
    for (uint16_t i = 0; i < bitMapLength; i++) {
        bitMap[i] = 0;
    }
}

static void ArtInitPageInfo(const ArtMemMgrT *artMemMgr, uint8_t *pageAddr)
{
    DB_POINTER2(artMemMgr, pageAddr);
    PageInfoT *pageInfo = (PageInfoT *)(void *)(pageAddr + sizeof(PageHeadT));
    pageInfo->beginOffset = (uint16_t)(artMemMgr->pageBaseSize + sizeof(PageInfoT));  // header boundary addr
    uint32_t *artNodeAddrMap = NULL;
    uint32_t *artFNodeAddrMap = NULL;
    if (artMemMgr->mode == ART_NODE_ADDR_DETECT || artMemMgr->mode == ART_FREE_NODE_DETECT) {
        artNodeAddrMap = (uint32_t *)(void *)((uintptr_t)pageAddr + pageInfo->beginOffset);
        ArtInitBitMap(artNodeAddrMap, artMemMgr->bitmapArrayLength);
        pageInfo->beginOffset = (uint16_t)(pageInfo->beginOffset + artMemMgr->bitmapArrayLength * sizeof(uint32_t));
    }
    if (artMemMgr->mode == ART_FREE_NODE_DETECT) {
        artFNodeAddrMap = (uint32_t *)(void *)((uintptr_t)pageAddr + pageInfo->beginOffset);
        ArtInitBitMap(artFNodeAddrMap, artMemMgr->bitmapArrayLength);
        pageInfo->beginOffset = (uint16_t)(pageInfo->beginOffset + artMemMgr->bitmapArrayLength * sizeof(uint32_t));
    }
    pageInfo->beginOffset = (uint16_t)SIZE_ALIGN8(pageInfo->beginOffset);
    pageInfo->offset = pageInfo->beginOffset;
    pageInfo->pageFreeSpace = (uint16_t)(artMemMgr->pageSize - pageInfo->offset);
}

uint32_t ArtMemGetNodeSize(
    const ArtMemMgrT *artMemMgr, const uint32_t *nodeAddrMap, uint16_t beginOffset, uint16_t endOffset)
{
    DB_POINTER2(artMemMgr, nodeAddrMap);
    uint16_t offset = beginOffset;
    do {
        offset = (uint16_t)(offset + artMemMgr->pageDivideLength);
    } while (!(ArtMemIsBitMapSet(artMemMgr, nodeAddrMap, offset) || offset == endOffset));
    return (uint32_t)(offset - beginOffset);
}

/*
 * update node info of MemPerfStat based on nodeDesc
 */
static void UpdateArtMemPerfStat(
    const ArtMemMgrT *artMemMgr, const uint32_t *nodeSizes, ArtMemPerfStatT *artMemPerfStat, ArtMemStatParaT para)
{
    DB_POINTER3(artMemMgr, nodeSizes, artMemPerfStat);
    for (uint16_t nodeIdx = 0; nodeIdx < para.nodeTypeNum; nodeIdx++) {
        if (nodeSizes[nodeIdx] == para.nodeSize) {
            if (para.isFree) {
                artMemPerfStat->freeNodeCounts[nodeIdx]++;
                artMemPerfStat->freeNodeCount++;
            } else {
                artMemPerfStat->nodeCounts[nodeIdx]++;
                artMemPerfStat->nodeCount++;
            }
            break;
        }
    }
}

Status ArtMemGetPageInfo(ArtMemMgrT *artMemMgr, uint32_t blockId, const uint32_t *nodeSizes, uint16_t nodeTypeNum,
    ArtMemPerfStatT *artMemPerfStat)
{
    DB_POINTER3(artMemMgr, nodeSizes, artMemPerfStat);
    DB_ASSERT(artMemMgr->mode == ART_FREE_NODE_DETECT);
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(artMemMgr->seInstanceId);
    if (seInstance == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    ArtMemMgrRunCtxT memMgrCtx;
    memMgrCtx.mdMgr = (MdMgrT *)(void *)seInstance->mdMgr;
    memMgrCtx.artMemMgr = artMemMgr;
    uint8_t *pageAddr = NULL;
    Status ret = ArtMemGetPageByBlockId(&memMgrCtx, blockId, &pageAddr);
    if (ret != GMERR_OK) {
        return ret;
    }
    PageInfoT *pageInfo = ArtMemPageInfoAddr(pageAddr);
    uint32_t *artNodeAddrMap = ArtGetNodeAddrMap(artMemMgr, pageAddr);
    uint32_t *artFNodeAddrMap = ArtGetFNodeAddrMap(artMemMgr, pageAddr);
    uint16_t endOffset = (uint16_t)(pageInfo->offset - pageInfo->beginOffset);
    uint16_t offset = 0;
    while (offset != endOffset) {
        if (ArtMemIsBitMapSet(artMemMgr, artNodeAddrMap, offset)) {
            bool isFree = ArtMemIsBitMapSet(artMemMgr, artFNodeAddrMap, offset);
            uint32_t nodeSize = ArtMemGetNodeSize(artMemMgr, artNodeAddrMap, offset, endOffset);
            ArtMemStatParaT para = {.nodeSize = nodeSize, .nodeTypeNum = nodeTypeNum, .isFree = isFree};
            UpdateArtMemPerfStat(artMemMgr, nodeSizes, artMemPerfStat, para);
            offset = (uint16_t)(offset + nodeSize);
        } else {
            offset = (uint16_t)(offset + ART_PAGE_DIVIDE_LENGTH);
        }
    }
    return GMERR_OK;
}

uint64_t ArtMemGetPageSize(const ArtMemMgrT *artMemMgr)
{
    DB_POINTER(artMemMgr);
    uint64_t pageCount = (uint64_t)(artMemMgr->pageAllocatedCount - artMemMgr->pageReleasedCount);
    uint64_t pageSize = (uint64_t)artMemMgr->pageSize;
    return pageCount * pageSize;
}

Status ArtMemStatistics(
    ArtMemMgrT *artMemMgr, const uint32_t *nodeSizes, uint16_t nodeTypeNum, ArtMemPerfStatT *artMemPerfStat)
{
    DB_POINTER3(artMemMgr, nodeSizes, artMemPerfStat);

    artMemPerfStat->pageCount = artMemMgr->pageAllocatedCount - artMemMgr->pageReleasedCount;
    artMemPerfStat->pageReleasedCount = artMemMgr->pageReleasedCount;
    artMemPerfStat->scaleInCount = artMemMgr->scaleInCount;
    artMemPerfStat->pageSize = (uint16_t)artMemMgr->pageSize;
    artMemPerfStat->usedShm = (uint64_t)(artMemMgr->pageSize * artMemPerfStat->pageCount);

    // no page is used
    if (artMemMgr->pageAllocatedCount == 0) {
        return GMERR_OK;
    }

    DbArrayAddrT arrAddrItem = {{.shmPtr = 0}};
    // get info by page, update node infos
    DbShmArrayT *pageIdArray = &artMemMgr->pageIdArray;
    for (uint32_t blockId = 0; blockId < pageIdArray->curPos; blockId++) {
        ArtMemPageAddrT *logicAddr = (ArtMemPageAddrT *)DbShmArrayGetItemById(pageIdArray, blockId, &arrAddrItem);
        if (logicAddr == NULL) {
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "AMS logicAddr null");
            return GMERR_INTERNAL_ERROR;
        }
        if (logicAddr->hasFree) {
            continue;
        }
        Status ret = ArtMemGetPageInfo(artMemMgr, blockId, nodeSizes, nodeTypeNum, artMemPerfStat);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // avoid dividend 0
    if (artMemPerfStat->usedShm == 0) {
        return GMERR_OK;
    }

    uint64_t nodeUsedShm = 0;
    for (uint16_t i = 0; i < nodeTypeNum; i++) {
        nodeUsedShm += (uint64_t)nodeSizes[i] * artMemPerfStat->nodeCounts[i];
    }
    artMemPerfStat->shmUtilization = (uint16_t)(nodeUsedShm * DB_PERCENTAGE_BASE / artMemPerfStat->usedShm);

    return GMERR_OK;
}

static Status ArtFreeListInsertNode(
    ArtMemMgrRunCtxT *memMgrCtx, FreeListT *freeList, FreeNodeT *freeNode, ArtNodePtrT nodePtr)
{
    DB_POINTER3(memMgrCtx, freeList, freeNode);
    Status ret = GMERR_OK;
    if (!ArtMemNodeIsEqual(freeList->firstNodePtr, ART_MEM_NULL_NODE_PTR)) {
        DB_ASSERT(freeList->nodeCount != 0);
        ArtNodePtrT firstNodePtr = freeList->firstNodePtr;
        uint8_t *nodeAddr = NULL;
        if ((ret = ArtMemGetNodeAddrByBlockId(memMgrCtx, firstNodePtr, &nodeAddr)) != GMERR_OK) {
            return ret;
        }
        DB_POINTER(nodeAddr);
        FreeNodeT *firstFreeNode = (FreeNodeT *)(void *)nodeAddr;

        ArtNodePtrT nextNodePtr = firstFreeNode->next;
        if ((ret = ArtMemGetNodeAddrByBlockId(memMgrCtx, nextNodePtr, &nodeAddr)) != GMERR_OK) {
            return ret;
        }
        DB_POINTER(nodeAddr);
        FreeNodeT *nextFreeNode = (FreeNodeT *)(void *)nodeAddr;

        firstFreeNode->next = nodePtr;
        nextFreeNode->prev = nodePtr;
        freeNode->prev = firstNodePtr;
        freeNode->next = nextNodePtr;
    } else {
        freeList->firstNodePtr = nodePtr;
        freeNode->prev = nodePtr;
        freeNode->next = nodePtr;
    }
    freeList->nodeCount++;

    uint8_t *pageAddr = NULL;
    if ((ret = ArtMemGetPageByBlockId(memMgrCtx, nodePtr.blockId, &pageAddr)) != GMERR_OK || pageAddr == NULL) {
        return ret;
    }
    ArtMemMgrT *artMemMgr = memMgrCtx->artMemMgr;
    PageInfoT *pageInfo = ArtMemPageInfoAddr(pageAddr);
    if (artMemMgr->mode == ART_FREE_NODE_DETECT) {
        uint16_t offset = (uint16_t)(nodePtr.offset - pageInfo->beginOffset);
        uint32_t *artNodeAddrMap = ArtGetNodeAddrMap(artMemMgr, pageAddr);
        uint32_t *artFNodeAddrMap = ArtGetFNodeAddrMap(artMemMgr, pageAddr);
        bool bit = ArtMemIsBitMapSet(artMemMgr, artNodeAddrMap, offset);
        DB_ASSERT(bit);
        ArtSetBitmap(artMemMgr, artFNodeAddrMap, offset, true);
    }
    pageInfo->pageFreeSpace = (uint16_t)(pageInfo->pageFreeSpace + freeList->nodeSize);
    return GMERR_OK;
}

Status ArtFreeListDeleteNode(ArtMemMgrRunCtxT *memMgrCtx, FreeListT *freeList, ArtNodePtrT nodePtr, uint8_t **addr)
{
    DB_POINTER2(memMgrCtx, freeList);
    uint8_t *nodeAddr = NULL;
    Status ret = ArtMemGetNodeAddrByBlockId(memMgrCtx, nodePtr, &nodeAddr);
    if (ret != GMERR_OK) {
        return ret;
    }
    DB_POINTER(nodeAddr);
    if (addr != NULL) {
        *addr = nodeAddr;
    }
    uint8_t *pageAddr = nodeAddr - nodePtr.offset;
    DB_POINTER(pageAddr);
    if (freeList->nodeCount != 1) {
        FreeNodeT *freeNode = (FreeNodeT *)(void *)nodeAddr;

        ArtNodePtrT prevNodePtr = freeNode->prev;
        ret = ArtMemGetNodeAddrByBlockId(memMgrCtx, prevNodePtr, &nodeAddr);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        DB_POINTER(nodeAddr);
        FreeNodeT *prevFreeNode = (FreeNodeT *)(void *)nodeAddr;

        ArtNodePtrT nextNodePtr = freeNode->next;
        ret = ArtMemGetNodeAddrByBlockId(memMgrCtx, nextNodePtr, &nodeAddr);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        DB_POINTER(nodeAddr);
        FreeNodeT *nextFreeNode = (FreeNodeT *)(void *)nodeAddr;

        prevFreeNode->next = nextNodePtr;
        nextFreeNode->prev = prevNodePtr;
        freeList->firstNodePtr = nextNodePtr;
    } else {
        freeList->firstNodePtr = ART_MEM_NULL_NODE_PTR;
    }
    freeList->nodeCount--;

    PageInfoT *pageInfo = ArtMemPageInfoAddr(pageAddr);
    if (memMgrCtx->artMemMgr->mode == ART_FREE_NODE_DETECT) {
        ArtSetBitmap(memMgrCtx->artMemMgr, ArtGetFNodeAddrMap(memMgrCtx->artMemMgr, pageAddr),
            (uint16_t)(nodePtr.offset - pageInfo->beginOffset), false);
    }
    pageInfo->pageFreeSpace = (uint16_t)(pageInfo->pageFreeSpace - freeList->nodeSize);
    return GMERR_OK;
}

static ArtMemInterStatusE ArtGetNodeFromFreeList(
    ArtMemMgrRunCtxT *memMgrCtx, uint32_t nodeType, uint8_t **addr, ArtNodePtrT *newNodePtr)
{
    DB_POINTER4(memMgrCtx, memMgrCtx->artMemMgr, addr, newNodePtr);
    DB_ASSERT(nodeType < ART_NODE_TYPE_NUM);
    uint8_t *nodeAddr = NULL;
    FreeListT *freeList = &memMgrCtx->artMemMgr->freeList[nodeType];
    if (freeList->nodeCount == 0) {
        return ART_MEM_NO_FREE_NODE;
    }
    ArtNodePtrT nodePtr = freeList->firstNodePtr;
    Status ret = ArtFreeListDeleteNode(memMgrCtx, freeList, nodePtr, &nodeAddr);
    if (ret != GMERR_OK) {
        return ART_MEM_INTER_ERROR;
    }
    *addr = nodeAddr;
    *newNodePtr = nodePtr;
    return ART_MEM_OK;
}

Status ArtAllocNewPageInner(ArtMemMgrRunCtxT *memMgrCtx, uint32_t tableSpaceIndex, PageIdT *pageAddr, uint8_t **page)
{
    // necessary param for page alloc
    ArtMemMgrT *artMemMgr = memMgrCtx->artMemMgr;
    PageIdT newPageAddr;
    MdMgrT *mdMgr = memMgrCtx->mdMgr;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(memMgrCtx->memctx);
    AllocPageParamT allocPageParam = SeInitAllocPageParam(
        tableSpaceIndex, artMemMgr->fileId, RSM_INVALID_LABEL_ID, dbInstance, NULL);  // index不会使用到rsmUndo
    StatusInter retInter = MdAllocPage(mdMgr, &allocPageParam, &newPageAddr);
    if (SECUREC_UNLIKELY(retInter != STATUS_OK_INTER)) {
        Status ret = DbGetExternalErrno(retInter);
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Alloc new page");
        return ret;
    }
    // get new page
    PageHeadT *pageBeginAddr = NULL;
    retInter = MdGetPage(mdMgr, newPageAddr, (uint8_t **)&pageBeginAddr, ENTER_PAGE_NORMAL, true);
    if (SECUREC_UNLIKELY(retInter != STATUS_OK_INTER)) {
        FreePageParamT freePageParam =
            SeInitFreePageParam(tableSpaceIndex, newPageAddr, dbInstance, NULL, SE_INVALID_LABEL_ID, false);
        (void)MdFreePage(mdMgr, &freePageParam);
        Status ret = DbGetExternalErrno(retInter);
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Get page from memdata");
        return ret;
    }

    // SeSetPageType接口待持久化移之后，恢复调用（设置为data页）
    *pageAddr = newPageAddr;
    *page = (uint8_t *)pageBeginAddr;
    return GMERR_OK;
}

static inline void ArtSetMemPageAddr(ArtMemPageAddrT *pageAddr, PageIdT pageId, bool hasFree)
{
    pageAddr->pageId = pageId;
    pageAddr->hasFree = hasFree;
}

static Status ArtAllocNewPage(ArtMemMgrRunCtxT *memMgrCtx, uint32_t tableSpaceIndex)
{
    DB_POINTER3(memMgrCtx, memMgrCtx->artMemMgr, memMgrCtx->mdMgr);
    ArtMemMgrT *artMemMgr = memMgrCtx->artMemMgr;
    DbShmArrayT *pageIdArray = &artMemMgr->pageIdArray;
    uint32_t candidatePageId = 0;
    ArtMemPageAddrT *candidatePageAddr = NULL;
    DbArrayAddrT arrAddrItem = {{.shmPtr = 0}};
    uint32_t curPos = pageIdArray->curPos;
    // 遍历page
    for (; candidatePageId < curPos; candidatePageId++) {
        candidatePageAddr = (ArtMemPageAddrT *)DbShmArrayGetItemById(pageIdArray, candidatePageId, &arrAddrItem);
        if (SECUREC_UNLIKELY(candidatePageAddr == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "the logical addr of the Node is null");
            return GMERR_INTERNAL_ERROR;
        }
        if (candidatePageAddr->hasFree) {
            break;
        }
        candidatePageAddr = NULL;
    }

    PageIdT pageAddr = SE_INVALID_PAGE_ADDR;
    uint8_t *pageBeginAddr;
    Status ret = ArtAllocNewPageInner(memMgrCtx, tableSpaceIndex, &pageAddr, &pageBeginAddr);
    if (ret != GMERR_OK) {
        return ret;
    }
    artMemMgr->pageAllocatedCount += 1;
    if (candidatePageAddr != NULL) {
        // 重用以前的page id
        ArtSetMemPageAddr(candidatePageAddr, pageAddr, false);
    } else {
        ArtMemPageAddrT *memPageAddr = NULL;
        uint32_t itemId = 0u;
        ret = DbShmArrayGetItem(pageIdArray, &itemId, (void **)&memPageAddr, &arrAddrItem);
        if (SECUREC_UNLIKELY(ret != GMERR_OK || memPageAddr == NULL)) {
            FreePageParamT freePageParam = SeInitFreePageParam(
                tableSpaceIndex, pageAddr, DbGetInstanceByMemCtx(memMgrCtx->memctx), NULL, SE_INVALID_LABEL_ID, false);
            (void)MdFreePage(memMgrCtx->mdMgr, &freePageParam);
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Insert db list wrong");
            return ret;
        }
        ArtSetMemPageAddr(memPageAddr, pageAddr, false);
    }
    artMemMgr->blockIdUsed = (int32_t)candidatePageId;
    artMemMgr->pageIdUsed = pageAddr;
    ArtInitPageInfo(artMemMgr, pageBeginAddr);
    return GMERR_OK;
}

/*
 * determine whether page has enough space, if so, alloc node
 */
static ArtMemInterStatusE ArtAllocNodeMemOnPage(
    ArtMemMgrRunCtxT *memMgrCtx, uint32_t nodeSize, uint8_t **addr, ArtNodePtrT *newNodePtr)
{
    DB_POINTER(memMgrCtx);
    ArtMemMgrT *artMemMgr = memMgrCtx->artMemMgr;
    if (artMemMgr->pageAllocatedCount == 0) {
        return ART_MEM_NO_ENOUGH_PAGE_SPACE;
    }

    uint8_t *pageAddr = NULL;
    Status ret = ArtMemGetPageByBlockId(memMgrCtx, (uint32_t)artMemMgr->blockIdUsed, &pageAddr);
    if (ret != GMERR_OK || pageAddr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "page begin addr is null");
        return ART_MEM_INTER_ERROR;
    }

    // get page Info
    PageInfoT *pageInfo = ArtMemPageInfoAddr(pageAddr);
    uint16_t offset = pageInfo->offset;
    if (artMemMgr->pageSize - offset < nodeSize) {
        return ART_MEM_NO_ENOUGH_PAGE_SPACE;
    }

    // available addr
    *addr = pageAddr + offset;

    if (artMemMgr->mode == ART_NODE_ADDR_DETECT || artMemMgr->mode == ART_FREE_NODE_DETECT) {
        ArtSetBitmap(
            artMemMgr, ArtGetNodeAddrMap(artMemMgr, pageAddr), (uint16_t)(offset - pageInfo->beginOffset), true);
    }

    // create and return logic node
    newNodePtr->pageId = SerializePageId((PageMgrT *)(void *)memMgrCtx->mdMgr, artMemMgr->pageIdUsed);
    newNodePtr->blockId = (uint16_t)((uint32_t)artMemMgr->blockIdUsed & 0x7FFFu);
    newNodePtr->offset = offset;

    pageInfo->offset = (uint16_t)(pageInfo->offset + nodeSize);
    pageInfo->pageFreeSpace = (uint16_t)(pageInfo->pageFreeSpace - nodeSize);
    return ART_MEM_OK;
}

Status ArtMemAllocNode(
    ArtMemMgrRunCtxT *memMgrCtx, ArtAllocNodeParamT *allocParam, uint8_t **addr, ArtNodePtrT *newNodePtr)
{
    DB_POINTER4(memMgrCtx, allocParam, addr, newNodePtr);
    ArtMemInterStatusE retInter = ArtGetNodeFromFreeList(memMgrCtx, allocParam->nodeType, addr, newNodePtr);
    if (SECUREC_UNLIKELY(retInter == ART_MEM_OK)) {
        return GMERR_OK;
    }
    if (retInter == ART_MEM_INTER_ERROR) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "alloc node from free node unsucc");
        return GMERR_INTERNAL_ERROR;
    }

    uint32_t alineNodeSize = ArtNodeGetAlineSize(memMgrCtx->artMemMgr, allocParam->nodeType);
    retInter = ArtAllocNodeMemOnPage(memMgrCtx, alineNodeSize, addr, newNodePtr);
    if (retInter == ART_MEM_OK) {
        return GMERR_OK;
    }
    if (retInter == ART_MEM_INTER_ERROR) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "alloc node on page unsucc");
        return GMERR_INTERNAL_ERROR;
    }

    // no page free space, so alloc new page
    Status ret = ArtAllocNewPage(memMgrCtx, allocParam->tableSpaceIndex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "alloc new page unsucc");
        return ret;
    }

    retInter = ArtAllocNodeMemOnPage(memMgrCtx, alineNodeSize, addr, newNodePtr);
    if (retInter != ART_MEM_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "alloc node on page unsucc");
        return GMERR_INTERNAL_ERROR;
    }

    return GMERR_OK;
}

Status ArtMemFreeNode(ArtMemMgrRunCtxT *memMgrCtx, uint32_t nodeType, ArtNodePtrT nodePtr)
{
    DB_POINTER2(memMgrCtx, memMgrCtx->artMemMgr);
    uint8_t *nodeAddr = NULL;
    Status ret = ArtMemGetNodeAddrByBlockId(memMgrCtx, nodePtr, &nodeAddr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INSUFFICIENT_RESOURCES, "ANTFN: freenode is null");
        return GMERR_INSUFFICIENT_RESOURCES;
    }
    DB_POINTER(nodeAddr);
    ArtMemMgrT *artMemMgr = memMgrCtx->artMemMgr;
    FreeListT *freeList = &artMemMgr->freeList[nodeType];

    return ArtFreeListInsertNode(memMgrCtx, freeList, (FreeNodeT *)(void *)nodeAddr, nodePtr);
}

void ArtMemFreeArtTree(ArtMemMgrT *artMemMgr)
{
    DB_POINTER(artMemMgr);
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(artMemMgr->seInstanceId);
    if (seInstance == NULL) {
        return;
    }
    ArtMemMgrRunCtxT memMgrCtx;
    memMgrCtx.mdMgr = (MdMgrT *)(void *)seInstance->mdMgr;
    memMgrCtx.artMemMgr = artMemMgr;

    DbShmArrayT *pageIdArray = &artMemMgr->pageIdArray;
    uint32_t pageCount = pageIdArray->curPos;
    for (uint32_t i = 0; i < pageCount; i++) {
        Status ret = ArtMemFreePageById(&memMgrCtx, i);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "AMFAT: free page %" PRIu32 " is null, page count %" PRIu32 ".", i, pageCount);
        }
    }
    artMemMgr->pageIdUsed = SE_INVALID_PAGE_ADDR;
    artMemMgr->blockIdUsed = -1;
    artMemMgr->pageReleasedCount = 0;
    artMemMgr->pageAllocatedCount = 0;
    artMemMgr->scaleInCount = 0;
}

void ArtMemDestroy(ArtMemMgrT *artMemMgr, DbInstanceHdT dbInstance)
{
    DB_POINTER(artMemMgr);
    ArtMemFreeArtTree(artMemMgr);
    DbShmArrayDestroy(&artMemMgr->pageIdArray, dbInstance);
    DbShmArrayDestroy(&artMemMgr->pageToReleaseArray, dbInstance);
    DbShmArrayDestroy(&artMemMgr->pageToMergeArray, dbInstance);
    DbShmArrayDestroy(&artMemMgr->artMVPageArray, dbInstance);
}

Status ArtAllocMVPage(ArtMemMgrRunCtxT *memMgrCtx, uint32_t tableSpaceIndex, uint16_t *blockId, uint8_t **pageBeginAddr)
{
    uint32_t itemId = 0;
    ArtMVPageDescT *item = NULL;
    DbArrayAddrT arrayAddr;
    Status ret = DbShmArrayGetItem(&memMgrCtx->artMemMgr->artMVPageArray, &itemId, (void **)&item, &arrayAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret,
            "MVPage, get item from artMVPageArray. shmArry count: %" PRIu32 ", used: %" PRIu32,
            memMgrCtx->artMemMgr->artMVPageArray.count, memMgrCtx->artMemMgr->artMVPageArray.used);
        return ret;
    }
    *blockId = (uint16_t)itemId;

    PageIdT pageAddr = SE_INVALID_PAGE_ADDR;
    uint8_t *pageBegin;
    ret = ArtAllocNewPageInner(memMgrCtx, tableSpaceIndex, &pageAddr, &pageBegin);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "alloc new page with item id %" PRIu32, itemId);
        (void)DbShmArrayRemoveItem(&memMgrCtx->artMemMgr->artMVPageArray, itemId);
        return ret;
    }
    *pageBeginAddr = (pageBegin + sizeof(PageHeadT));
    DB_UNUSED(pageBeginAddr);
    item->pageId = pageAddr;
    DB_ASSERT((DbIsPageIdValid(pageAddr)));
    return GMERR_OK;
}

Status ArtFreeMVPageById(ArtMemMgrRunCtxT *memMgrCtx, uint32_t tableSpaceIndex, uint16_t blockId)
{
    DB_POINTER2(memMgrCtx, memMgrCtx->artMemMgr);
    DbShmArrayT *blockArray = &memMgrCtx->artMemMgr->artMVPageArray;
    DbArrayAddrT itemAddr;
    ArtMVPageDescT *item = (ArtMVPageDescT *)DbShmArrayGetItemById(blockArray, (uint32_t)blockId, &itemAddr);
    if (item == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR,
            "MVPage, get item from artMVPageArray when free page. shmArry count: %" PRIu32 ", used: %" PRIu32,
            blockArray->count, blockArray->used);
        return GMERR_INTERNAL_ERROR;
    }

    if (!DbIsPageIdValid(item->pageId)) {
        return GMERR_OK;
    }
    FreePageParamT freePageParam = SeInitFreePageParam(
        tableSpaceIndex, item->pageId, DbGetInstanceByMemCtx(memMgrCtx->memctx), NULL, SE_INVALID_LABEL_ID, false);
    StatusInter freeRet = MdFreePage(memMgrCtx->mdMgr, &freePageParam);
    Status ret = DbGetExternalErrno(freeRet);
    if (ret == GMERR_OK) {
        memMgrCtx->artMemMgr->version++;
    } else {
        DB_LOG_ERROR(ret, "Unable to free unsucc new hash page deviceId:%" PRIu32 ", blockId:%" PRIu32 "",
            item->pageId.deviceId, item->pageId.blockId);
        return ret;
    }
    // 页删除后，对应 artMVPageArray 上对应的item
    ret = DbShmArrayRemoveItem(blockArray, (uint32_t)blockId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "MVPage, remove block id from artMVPageArray, itemId: %" PRIu16, blockId);
    }
    return ret;
}

Status GetArtMVPageByPageId(ArtMemMgrRunCtxT *memRunCtx, PageIdT pageId, uint8_t **pageBeginAddr)
{
    DB_POINTER2(memRunCtx, pageBeginAddr);
    uint8_t *page = NULL;
    StatusInter ret = MdGetPage(memRunCtx->mdMgr, pageId, &page, ENTER_PAGE_NORMAL, false);
    if (page != NULL) {
        *pageBeginAddr = page + sizeof(PageHeadT);
    }
    return DbGetExternalErrno(ret);
}

Status GetArtMVPageByBlockId(ArtMemMgrRunCtxT *memRunCtx, uint16_t blockId, uint8_t **pageBeginAddr)
{
    DB_POINTER2(memRunCtx, pageBeginAddr);
    DbShmArrayT *pageSwizzleArray = &memRunCtx->artMemMgr->artMVPageArray;
    DbArrayAddrT itemAddr;
    ArtMVPageDescT *item = (ArtMVPageDescT *)DbShmArrayGetItemById(pageSwizzleArray, (uint32_t)blockId, &itemAddr);
    if (item == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
            "ArtMVPage, get mv page, itemId: %" PRIu16 ", shmArry count: %" PRIu32 ", used: %" PRIu32 "", blockId,
            pageSwizzleArray->count, pageSwizzleArray->used);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    PageIdT pageId = item->pageId;
    if (!DbIsPageIdValid(item->pageId)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "ArtMVPage, Not valid page id. %" PRIu32 ", %" PRIu32,
            pageId.blockId, pageId.deviceId);
        return GMERR_DATA_EXCEPTION;
    }
    return GetArtMVPageByPageId(memRunCtx, pageId, pageBeginAddr);
}

#ifdef __cplusplus
}
#endif
