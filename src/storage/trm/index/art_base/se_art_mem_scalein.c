/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: se_art_mem_scalein.c
 * Description: Implementation of ART scale-in
 * Author:
 * Create: 2021-11-24
 */

#include "se_art.h"

#define ART_FRAGMENTATION_RATE_THRESHOLD 75
#define ART_MIN_PAGE_COUNT_TO_MERGE 2

#ifdef __cplusplus
extern "C" {
#endif

typedef struct ArtMoveNodePara {
    ArtNodeT *srcNode;
    ArtNodePtrT srcNodePtr;
    uint32_t dstBlockId;
} ArtMoveNodeParaT;

static Status ArtDeleteNodeBySize(ArtMemMgrRunCtxT *memMgrCtx, ArtNodePtrT nodePtr, uint32_t nodeSize)
{
    DB_POINTER(memMgrCtx);
    for (uint32_t i = 0; i < ART_NODE_TYPE_NUM; i++) {
        FreeListT *freeList = &memMgrCtx->artMemMgr->freeList[i];
        if (freeList->nodeSize == nodeSize && freeList->nodeCount > 0u) {
            return ArtFreeListDeleteNode(memMgrCtx, freeList, nodePtr, NULL);
        }
    }
    DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "No target free node has same node size %" PRIu16 ".", nodeSize);
    return GMERR_INTERNAL_ERROR;
}

static ArtMemInterStatusE ArtGetNodeFromOffsetList(const DbShmArrayT *offsetLists, uint32_t nodeSize, uint32_t *offset)
{
    DB_POINTER2(offsetLists, offset);
    uint32_t count = offsetLists->curPos;
    DbArrayAddrT arrAddrItem = {{.shmPtr = 0}};
    for (uint32_t i = 0; i < count; i++) {
        NodeOffsetListT *offsetsList = (NodeOffsetListT *)DbShmArrayGetItemById(offsetLists, i, &arrAddrItem);
        if (offsetsList == NULL) {
            DB_LOG_ERROR(
                NO_DATA_NULL_POINTER, "the offset list of the FNode is null, node size = %" PRIu16 ".", nodeSize);
            return ART_MEM_INTER_ERROR;
        }
        uint32_t usedNum = offsetsList->offsets->used;
        if (offsetsList->nodeSize == nodeSize && usedNum > 0) {
            *offset = *(uint32_t *)DbShmArrayGetItemById(offsetsList->offsets, usedNum - 1, &arrAddrItem);
            (void)DbShmArrayRemoveItem(offsetsList->offsets, usedNum - 1);
            return ART_MEM_OK;
        }
    }
    return ART_MEM_FNODE_NODESIZE_NOT_FOUND;
}

static Status ArtOffsetListInsertNewSizeNode(
    DbShmArrayT *offsetLists, uint32_t nodeSize, uint16_t offset, DbMemCtxT *memCtx, uint32_t artShmArrayId)
{
    NodeOffsetListT fnodeOffsetList;
    fnodeOffsetList.nodeSize = nodeSize;
    /* 上层ArtFreeOffsetList统一释放 */
    fnodeOffsetList.offsets = (DbShmArrayT *)DbDynMemCtxAlloc(memCtx, sizeof(DbShmArrayT));
    if (fnodeOffsetList.offsets == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY,
            "Unable to alloc art dblist when insert node, node size = %" PRIu16 ", offset = %" PRIu16 ".", nodeSize,
            offset);
        return GMERR_OUT_OF_MEMORY;
    }
    /* 上层ArtFreeOffsetList统一释放 */
    Status ret = DbShmArrayInit(fnodeOffsetList.offsets, ART_SHMARRAY_CAPACITY, sizeof(uint32_t), artShmArrayId, NULL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(memCtx, fnodeOffsetList.offsets);
        return ret;
    }
    uint32_t itemId = 0u;
    uint16_t *offsetItem = NULL;
    NodeOffsetListT *offSetListsItem = NULL;
    DbArrayAddrT arrAddrItem = {{.shmPtr = 0}};
    ret = DbShmArrayGetItem(fnodeOffsetList.offsets, &itemId, (void **)&offsetItem, &arrAddrItem);
    if (ret != GMERR_OK || offsetItem == NULL) {
        DbShmArrayDestroy(fnodeOffsetList.offsets, DbGetInstanceByMemCtx(memCtx));
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(memCtx, fnodeOffsetList.offsets);
        return ret;
    }
    *offsetItem = offset;
    ret = DbShmArrayGetItem(offsetLists, &itemId, (void **)&offSetListsItem, &arrAddrItem);
    if (ret != GMERR_OK || offSetListsItem == NULL) {
        DbShmArrayDestroy(fnodeOffsetList.offsets, DbGetInstanceByMemCtx(memCtx));
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(memCtx, fnodeOffsetList.offsets);
        return ret;
    }
    *offSetListsItem = fnodeOffsetList;
    return GMERR_OK;
}

static Status ArtOffsetListInsertNode(
    DbShmArrayT *offsetLists, uint32_t nodeSize, uint16_t offset, DbMemCtxT *memCtx, uint32_t artShmArrayId)
{
    DB_POINTER2(offsetLists, memCtx);
    uint32_t count = offsetLists->curPos;
    DbArrayAddrT arrAddrItem = {{.shmPtr = 0}};
    uint32_t itemId = 0u;
    uint16_t *offsetItem = NULL;
    for (uint32_t i = 0; i < count; i++) {
        NodeOffsetListT *offsetList = (NodeOffsetListT *)DbShmArrayGetItemById(offsetLists, i, &arrAddrItem);
        if (offsetList == NULL) {
            DB_LOG_ERROR(GMERR_NO_DATA,
                "the offset list of the FNode is null, node size = %" PRIu16 ", offset = %" PRIu16 ".", nodeSize,
                offset);
            return GMERR_NO_DATA;
        }
        if (offsetList->nodeSize == nodeSize) {
            Status ret = DbShmArrayGetItem(offsetList->offsets, &itemId, (void **)&offsetItem, &arrAddrItem);
            if (ret == GMERR_OK && offsetItem != NULL) {
                *offsetItem = offset;
            }
            return ret;
        }
    }
    return ArtOffsetListInsertNewSizeNode(offsetLists, nodeSize, offset, memCtx, artShmArrayId);
}

static Status ArtAllocOffsetList(DbMemCtxT *memCtx, ArtMemMgrT *artMemMgr, DbShmArrayT **offsetLits)
{
    /* 上层ArtFreeOffsetList统一释放 */
    DbShmArrayT *nodeOffsetList = (DbShmArrayT *)DbDynMemCtxAlloc(memCtx, sizeof(DbShmArrayT));
    if (nodeOffsetList == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc art dblist");
        return GMERR_OUT_OF_MEMORY;
    }
    /* 上层ArtFreeOffsetList统一释放 */
    Status ret =
        DbShmArrayInit(nodeOffsetList, ART_SHMARRAY_CAPACITY, sizeof(NodeOffsetListT), artMemMgr->artShmArrayId, NULL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(memCtx, nodeOffsetList);
    }
    *offsetLits = nodeOffsetList;
    return ret;
}

static Status ArtGetOffsetList(ArtMemMgrRunCtxT *memRunCtx, uint32_t blockId, DbShmArrayT *offsetList)
{
    DB_POINTER3(memRunCtx->artMemMgr, memRunCtx->memctx, offsetList);
    uint8_t *pageAddr = NULL;
    Status ret = ArtMemGetPageByBlockId(memRunCtx, blockId, &pageAddr);
    if (ret != GMERR_OK) {
        return ret;
    }
    PageInfoT *pageInfo = ArtMemPageInfoAddr(pageAddr);
    uint32_t *nodeAddrMap = ArtGetNodeAddrMap(memRunCtx->artMemMgr, pageAddr);
    uint32_t *fNodeAddrMap = ArtGetFNodeAddrMap(memRunCtx->artMemMgr, pageAddr);
    ArtMemMgrT *artMemMgr = memRunCtx->artMemMgr;
    uint16_t endOffset = (uint16_t)(pageInfo->offset - pageInfo->beginOffset);
    uint16_t offset = 0;
    while (offset != endOffset) {
        if (!ArtMemIsBitMapSet(artMemMgr, nodeAddrMap, offset)) {
            offset = (uint16_t)(offset + ART_PAGE_DIVIDE_LENGTH);
            continue;
        }
        bool isFree = ArtMemIsBitMapSet(artMemMgr, fNodeAddrMap, offset);
        uint32_t nodeSize = ArtMemGetNodeSize(artMemMgr, nodeAddrMap, offset, endOffset);
        if (isFree) {
            ret = ArtOffsetListInsertNode(offsetList, nodeSize, (uint16_t)(pageInfo->beginOffset + offset),
                memRunCtx->memctx, artMemMgr->artShmArrayId);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        offset = (uint16_t)(offset + nodeSize);
    }
    return GMERR_OK;
}

static void ArtFreeOffsetList(DbMemCtxT *memCtx, DbShmArrayT *offsetLists)
{
    DB_POINTER2(offsetLists, memCtx);
    DbArrayAddrT arrAddrItem = {{.shmPtr = 0}};
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(memCtx);
    for (uint32_t i = 0; i < offsetLists->curPos; i++) {
        NodeOffsetListT *offsetList = (NodeOffsetListT *)DbShmArrayGetItemById(offsetLists, i, &arrAddrItem);
        if (offsetList != NULL && offsetList->offsets != NULL) {
            DbShmArrayDestroy(offsetList->offsets, dbInstance);
            // 释放后，外部函数不再访问，未置NULL，无UAF风险
            DbDynMemCtxFree(memCtx, offsetList->offsets);
        }
    }
    DbShmArrayDestroy(offsetLists, dbInstance);
    // 释放后，外部函数不再访问，未置NULL，无UAF风险
    DbDynMemCtxFree(memCtx, offsetLists);
    return;
}

static ArtMemInterStatusE ArtMoveNodeToPage(
    ArtRunningCtxT *artCtx, ArtMoveNodeParaT paras, const DbShmArrayT *offsetList)
{
    DB_POINTER4(artCtx, artCtx->art, paras.srcNode, offsetList);
    uint32_t nodeSize = ArtGetNodeSizeByType(artCtx->art, paras.srcNode->meta.type);
    uint32_t offset = 0;
    ArtMemInterStatusE retInter = ArtGetNodeFromOffsetList(offsetList, nodeSize, &offset);
    if (retInter != ART_MEM_OK) {
        return retInter;
    }
    DB_LOG_DBG_DEBUG("move art node, node type is %" PRIu8 ", node size is %" PRIu16 ", src blockId: %" PRIu16
                     ", src pageOffset: %" PRIu16 ", "
                     "dest blockId: %" PRIu32 ", dest pageOffset: %" PRIu32 ".\n",
        paras.srcNode->meta.type, nodeSize, paras.srcNodePtr.blockId, paras.srcNodePtr.offset, paras.dstBlockId,
        offset);
    // pageId设为无效，表示通过blockId获取页
    ArtNodePtrT destNodePtr = {.blockId = (uint16_t)(paras.dstBlockId & 0x7FFFu),
        .pageId = SE_INVALID_PAGE_ID,
        .isDeleted = false,
        .offset = (uint16_t)offset};
    uint32_t nodeType = (uint32_t)paras.srcNode->meta.type;
    Status ret = ArtDeleteNodeBySize(&artCtx->memRunCtx, destNodePtr, nodeSize);
    if (ret != GMERR_OK) {
        return ART_MEM_INTER_ERROR;
    }
    uint8_t *destPageAddr = NULL;
    ret = ArtMemGetPageByBlockId(&artCtx->memRunCtx, paras.dstBlockId, &destPageAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "dest page begin addr is null");
        return ART_MEM_INTER_ERROR;
    }
    // 移动节点
    if (memcpy_s(destPageAddr + offset, nodeSize, (uint8_t *)paras.srcNode, nodeSize) != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "memcpy unsucc");
        return ART_MEM_INTER_ERROR;
    }
    // 调用artTree的node搬迁函数
    ArtChangeNodePtrWhenMove(artCtx, paras.srcNode, paras.srcNodePtr, destNodePtr);
    // 把源节点标记为Free
    ret = ArtMemFreeNode(&artCtx->memRunCtx, nodeType, paras.srcNodePtr);
    if (ret != GMERR_OK) {
        return ART_MEM_INTER_ERROR;
    }
    return ART_MEM_OK;
}

static Status ArtFreeSpacePage(ArtMemMgrRunCtxT *memMgrCtx, uint32_t blockId)
{
    DB_POINTER2(memMgrCtx, memMgrCtx->artMemMgr);
    DB_LOG_DBG_INFO("Begin release art block, block id is %" PRIu32 ".", blockId);
    uint8_t *pageAddr = NULL;
    Status ret = ArtMemGetPageByBlockId(memMgrCtx, blockId, &pageAddr);
    if (ret != GMERR_OK) {
        return ret;
    }
    ArtMemMgrT *artMemMgr = memMgrCtx->artMemMgr;
    PageInfoT *pageInfo = ArtMemPageInfoAddr(pageAddr);
    uint32_t *artNodeAddrMap = ArtGetNodeAddrMap(memMgrCtx->artMemMgr, pageAddr);
    uint32_t *artFNodeAddrMap = ArtGetFNodeAddrMap(memMgrCtx->artMemMgr, pageAddr);
    uint16_t deleteCount = 0;
    uint16_t endOffset = (uint16_t)(pageInfo->offset - pageInfo->beginOffset);
    uint16_t offset = 0;
    while (offset != endOffset) {
        if (ArtMemIsBitMapSet(artMemMgr, artFNodeAddrMap, offset)) {
            uint32_t nodeSize = ArtMemGetNodeSize(artMemMgr, artNodeAddrMap, offset, endOffset);
            // pageId设为无效，表示通过blockId获取页
            ArtNodePtrT nodePtr = {.pageId = SE_INVALID_PAGE_ID,
                .blockId = (uint16_t)(blockId & 0x7FFFu),
                .isDeleted = false,
                .offset = (uint16_t)(pageInfo->beginOffset + offset)};
            ret = ArtDeleteNodeBySize(memMgrCtx, nodePtr, nodeSize);
            if (ret != GMERR_OK) {
                return ret;
            }
            DB_LOG_DBG_DEBUG("Deleted node node size %" PRIu16 ", blockId %" PRIu16 ", offset %" PRIu16 ".", nodeSize,
                nodePtr.blockId, nodePtr.offset);
            deleteCount++;
            offset = (uint16_t)(offset + nodeSize);
        } else {
            offset = (uint16_t)(offset + ART_PAGE_DIVIDE_LENGTH);
        }
    }
    ret = ArtMemFreePageById(memMgrCtx, blockId);
    if (ret != GMERR_OK) {
        return ret;
    }
    artMemMgr->pageReleasedCount++;
    DB_LOG_DBG_INFO("Finish release art page, delete node count %" PRIu16 ".", deleteCount);
    return GMERR_OK;
}

uint32_t ArtGetPageFreeSizeById(ArtMemMgrRunCtxT *memMgrCtx, uint32_t blockId)
{
    DB_POINTER(memMgrCtx);
    uint8_t *pageBeginAddr = NULL;
    Status ret = ArtMemGetPageByBlockId(memMgrCtx, blockId, &pageBeginAddr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK || pageBeginAddr == NULL)) {
        return 0;
    }
    PageInfoT *pageInfo = ArtMemPageInfoAddr(pageBeginAddr);
    return pageInfo->pageFreeSpace;
}

// 判断page是否需要释放
bool IsArtPageNeedToRelease(ArtMemMgrRunCtxT *memMgrCtx, uint32_t blockId)
{
    DB_POINTER(memMgrCtx);
    uint8_t *pageBeginAddr = NULL;
    Status ret = ArtMemGetPageByBlockId(memMgrCtx, blockId, &pageBeginAddr);
    DB_ASSERT(ret == GMERR_OK);
    uint32_t *artNodeAddrMap = ArtGetNodeAddrMap(memMgrCtx->artMemMgr, pageBeginAddr);
    uint32_t *artFNodeAddrMap = ArtGetFNodeAddrMap(memMgrCtx->artMemMgr, pageBeginAddr);
    uint16_t bitmapArrayLength = memMgrCtx->artMemMgr->bitmapArrayLength;
    for (uint16_t i = 0; i < bitmapArrayLength; i++) {
        if (artNodeAddrMap[i] != artFNodeAddrMap[i]) {
            return false;
        }
    }
    return true;
}

bool IsArtPageNeedToMerge(ArtMemMgrRunCtxT *memMgrCtx, uint32_t blockId)
{
    DB_POINTER(memMgrCtx);
    uint32_t pageFreeSpace = ArtGetPageFreeSizeById(memMgrCtx, blockId);
    return pageFreeSpace >
           memMgrCtx->artMemMgr->pageSize * ((double)ART_FRAGMENTATION_RATE_THRESHOLD / DB_PERCENTAGE_BASE);
}

static Status ArtPageCheckAfterMerge(ArtMemMgrRunCtxT *memRunCtx, uint32_t srcPageId)
{
    DB_POINTER(memRunCtx);
    return IsArtPageNeedToRelease(memRunCtx, srcPageId) ? ArtFreeSpacePage(memRunCtx, srcPageId) : GMERR_OK;
}

static inline bool IfFreeNode(ArtMemMgrT *artMemMgr, PageInfoT *srcPageInfo, uint8_t *srcPageAddr, uint16_t offset)
{
    return ArtMemIsBitMapSet(artMemMgr, ArtGetFNodeAddrMap(artMemMgr, srcPageAddr), offset);
}

static inline uint32_t GetNodeSize(
    ArtMemMgrT *artMemMgr, PageInfoT *srcPageInfo, uint8_t *srcPageAddr, uint16_t offset, uint16_t endOffset)
{
    return ArtMemGetNodeSize(artMemMgr, ArtGetNodeAddrMap(artMemMgr, srcPageAddr), offset, endOffset);
}

static Status ArtMergeSpacePage(ArtRunningCtxT *artCtx, uint32_t srcBlockId, uint32_t dstBlockId)
{
    DB_POINTER2(artCtx, artCtx->art);
    DB_LOG_DBG_INFO("Begin merge art page, srcBlockId %" PRIu32 ", dstBlockId %" PRIu32, srcBlockId, dstBlockId);
    uint8_t *srcPageAddr = NULL;
    ArtMemMgrRunCtxT *memRunCtx = &artCtx->memRunCtx;
    Status ret = ArtMemGetPageByBlockId(memRunCtx, srcBlockId, &srcPageAddr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    PageInfoT *srcPageInfo = ArtMemPageInfoAddr(srcPageAddr);
    DbShmArrayT *offsetList = NULL;
    /* 就近释放 */
    if ((ret = ArtAllocOffsetList(memRunCtx->memctx, memRunCtx->artMemMgr, &offsetList)) != GMERR_OK) {
        return ret;
    }
    if ((ret = ArtGetOffsetList(memRunCtx, dstBlockId, offsetList)) != GMERR_OK) {
        ArtFreeOffsetList(memRunCtx->memctx, offsetList);
        return ret;
    }
    ArtMemMgrT *artMemMgr = memRunCtx->artMemMgr;
    uint16_t movedCount = 0;
    uint16_t endOffset = (uint16_t)(srcPageInfo->offset - srcPageInfo->beginOffset);
    uint16_t offset = 0;
    while (offset != endOffset) {
        bool ifFreeNode = IfFreeNode(artMemMgr, srcPageInfo, srcPageAddr, offset);
        uint32_t nodeSize = GetNodeSize(artMemMgr, srcPageInfo, srcPageAddr, offset, endOffset);
        if (!ifFreeNode) {
            // pageId设为无效，表示通过blockId获取页
            ArtNodePtrT srcNodePtr = {.pageId = SE_INVALID_PAGE_ID,
                .blockId = (uint16_t)(srcBlockId & 0x7FFFu),
                .isDeleted = false,
                .offset = (uint16_t)(srcPageInfo->beginOffset + offset)};
            ArtNodeT *srcNode = (ArtNodeT *)(void *)(srcPageAddr + srcNodePtr.offset);
            ArtMoveNodeParaT paras = {.srcNode = srcNode, .srcNodePtr = srcNodePtr, .dstBlockId = dstBlockId};
            ArtMemInterStatusE retInter = ArtMoveNodeToPage(artCtx, paras, offsetList);
            if (retInter == ART_MEM_FNODE_NODESIZE_NOT_FOUND) {
                DB_LOG_DBG_INFO("Target node size %" PRIu16 " not found, stop merge art page.", nodeSize);
                break;
            }
            if (retInter != ART_MEM_OK) {
                ArtFreeOffsetList(memRunCtx->memctx, offsetList);
                return GMERR_INTERNAL_ERROR;
            }
            movedCount++;
        }
        offset = (uint16_t)(offset + nodeSize);
    }
    ArtFreeOffsetList(memRunCtx->memctx, offsetList);
    DB_LOG_DBG_INFO("Finish merge art page, moved node count %" PRIu16 ".", movedCount);
    return ArtPageCheckAfterMerge(memRunCtx, srcBlockId);
}

static Status ArtAddPageToMerge(ArtMemMgrRunCtxT *memMgrCtx, uint32_t addblockId)
{
    DB_POINTER(memMgrCtx);
    DbShmArrayT *pageToMergeArray = &memMgrCtx->artMemMgr->pageToMergeArray;
    uint32_t addPageFreeSpace = ArtGetPageFreeSizeById(memMgrCtx, addblockId);
    uint32_t count = pageToMergeArray->curPos;
    DbArrayAddrT arrAddrItem = {{.shmPtr = 0}};
    uint32_t itemId = 0u;
    for (uint32_t i = 0; i < count; i++) {
        uint32_t *blockId = (uint32_t *)DbShmArrayGetItemById(pageToMergeArray, i, &arrAddrItem);
        if (blockId == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NO_DATA, "the page id is null");
            return GMERR_NO_DATA;
        }
        uint32_t pageFreeSpace = ArtGetPageFreeSizeById(memMgrCtx, *blockId);
        if (addPageFreeSpace > pageFreeSpace) {
            *blockId = addblockId;
            return GMERR_OK;
        }
    }
    uint32_t *addPageIdItem = NULL;
    Status ret = DbShmArrayGetItem(pageToMergeArray, &itemId, (void **)&addPageIdItem, &arrAddrItem);
    if (ret != GMERR_OK || addPageIdItem == NULL) {
        return ret;
    }
    *addPageIdItem = addblockId;
    return GMERR_OK;
}

static Status ArtPrepareFreePageArr(
    DbShmArrayT *pageToMergeArray, DbShmArrayT *pageToReleaseArray, DbInstanceHdT dbInstance)
{
    DB_POINTER2(pageToMergeArray, pageToReleaseArray);
    DbShmArrayDestroy(pageToMergeArray, dbInstance);
    DbShmArrayDestroy(pageToReleaseArray, dbInstance);
    Status ret = DbShmArrayInit(
        pageToMergeArray, ART_SHMARRAY_CAPACITY, sizeof(uint32_t), pageToMergeArray->arrayMemCtxId, NULL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = DbShmArrayInit(
        pageToReleaseArray, ART_SHMARRAY_CAPACITY, sizeof(uint32_t), pageToReleaseArray->arrayMemCtxId, NULL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbShmArrayDestroy(pageToMergeArray, dbInstance);
    }
    return ret;
}

static Status ArtCheckMemPageFreeSpace(ArtMemMgrRunCtxT *memMgrCtx)
{
    DB_POINTER2(memMgrCtx, memMgrCtx->artMemMgr);
    ArtMemMgrT *artMemMgr = memMgrCtx->artMemMgr;
    DbShmArrayT *pageIdArray = &artMemMgr->pageIdArray;
    DbShmArrayT *pageToMergeArray = &artMemMgr->pageToMergeArray;
    DbShmArrayT *pageToReleaseArray = &artMemMgr->pageToReleaseArray;
    Status ret = ArtPrepareFreePageArr(pageToMergeArray, pageToReleaseArray, DbGetInstanceByMemCtx(memMgrCtx->memctx));
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    DbArrayAddrT arrAddrItem = {{.shmPtr = 0}};
    uint32_t itemId = 0u;
    for (uint32_t blockId = 0; blockId < pageIdArray->curPos; blockId++) {
        if (blockId == (uint32_t)artMemMgr->blockIdUsed) {
            continue;
        }
        ArtMemPageAddrT *logicAddr = (ArtMemPageAddrT *)DbShmArrayGetItemById(pageIdArray, blockId, &arrAddrItem);
        if (logicAddr == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NO_DATA, "the logical addr of the mem page is null");
            return GMERR_NO_DATA;
        }
        if (logicAddr->hasFree) {
            continue;
        }
        if (IsArtPageNeedToRelease(memMgrCtx, blockId)) {
            uint32_t *releaseItem = NULL;
            ret = DbShmArrayGetItem(pageToReleaseArray, &itemId, (void **)&releaseItem, &arrAddrItem);
            if (ret != GMERR_OK) {
                return ret;
            }
            *releaseItem = blockId;
        } else if (IsArtPageNeedToMerge(memMgrCtx, blockId)) {
            ret = ArtAddPageToMerge(memMgrCtx, blockId);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

Status ArtMemPageScaleIn(struct ArtRunningCtx *artCtx, IndexScaleInCfgT *idxScaleCfg)
{
    DB_POINTER3(artCtx, artCtx->art, idxScaleCfg);
    Status ret = ArtCheckMemPageFreeSpace(&artCtx->memRunCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbArrayAddrT arrAddrItem = {{.shmPtr = 0}};
    DbShmArrayT *pageToReleaseArray = &artCtx->art->artMemMgr.pageToReleaseArray;
    for (uint32_t i = 0; i < pageToReleaseArray->curPos; i++) {
        uint32_t *blockId = (uint32_t *)DbShmArrayGetItemById(pageToReleaseArray, i, &arrAddrItem);
        DB_POINTER(blockId);
        ret = ArtFreeSpacePage(&artCtx->memRunCtx, *blockId);
        if (ret != GMERR_OK) {
            return ret;
        }
        artCtx->art->artMemMgr.scaleInCount++;
        if (IdxExceedSplitTime(idxScaleCfg)) {  // 时间片用完就直接返回
            idxScaleCfg->isOverTime = true;
            DB_LOG_INFO("Exceed the split time when compress ArtIndex.");
            return GMERR_OK;
        }
    }
    DbShmArrayT *pageToMergeArray = &artCtx->art->artMemMgr.pageToMergeArray;
    uint32_t pageToMergeCount = pageToMergeArray->curPos;
    if (pageToMergeCount < ART_MIN_PAGE_COUNT_TO_MERGE) {
        return GMERR_OK;
    }
    for (uint32_t i = 0; i < pageToMergeCount - 1; i = i + ART_MIN_PAGE_COUNT_TO_MERGE) {
        uint32_t *srcBlockId = (uint32_t *)DbShmArrayGetItemById(pageToMergeArray, i, &arrAddrItem);
        uint32_t *dstBlockId = (uint32_t *)DbShmArrayGetItemById(pageToMergeArray, i + 1, &arrAddrItem);
        if (srcBlockId == NULL || dstBlockId == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NO_DATA, "source block or dest block is null.");
            return GMERR_NO_DATA;
        }
        ret = ArtMergeSpacePage(artCtx, *srcBlockId, *dstBlockId);
        if (ret != GMERR_OK) {
            return ret;
        }
        artCtx->art->artMemMgr.scaleInCount++;
        if (IdxExceedSplitTime(idxScaleCfg)) {  // 时间片用完就直接返回
            DB_LOG_INFO("Exceed the split time when compress ArtIndex.");
            idxScaleCfg->isOverTime = true;
            return GMERR_OK;
        }
    }
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
