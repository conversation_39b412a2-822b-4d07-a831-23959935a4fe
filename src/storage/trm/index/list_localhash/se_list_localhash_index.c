/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: se_hash_index.c
 * Description: Implementation of Hash Index
 * Author: tanglu
 * Create: 2020/8/10
 */

#include "se_list_localhash_index.h"
#include "se_hash_index_stash_page.h"
#include "se_hash_mem.h"
#include "se_index.h"
#include "db_utils.h"
#include "dm_data_index.h"
#include "se_chained_hash_index.h"
#include "se_hash_index.h"
#include "se_hash_linklist_index.h"
#include "db_memcpy.h"

#ifdef __cplusplus
extern "C" {
#endif

void ListLocalEndScanStub(const IndexCtxT *idxCtx, IndexScanItrT iter)
{
    DB_UNUSED(idxCtx);
    DB_UNUSED(iter);
}

IdxFuncT g_hashLocalIndexFuncHandle[(uint32_t)LIST_LOCALHASH_ID__MAX] = {
    {
        .idxCreate = HashIndexCreate,
        .idxDrop = HashIndexDrop,
        .idxCommitDrop = NULL,
        .idxTruncate = HashIndexTruncate,
        .idxCommitTruncate = NULL,
        .idxOpen = HashIndexOpen,
        .idxClose = HashIndexClose,
        .idxInsert = HashIndexInsert,
        .idxBatchInsert = HashIndexBatchInsert,
        .idxDelete = HashIndexDelete,
        .idxBatchDelete = HashIndexBatchDelete,
        .idxUpdate = HashIndexUpdate,
        .idxBatchUpdate = HashIndexBatchUpdate,
        .idxLookup = HashIndexLookup,
        .idxBatchLookup = HashIndexBatchLookup,
        .idxBeginScan = HashIndexBeginScan,
        .idxScan = HashIndexScan,
        .idxEndScan = ListLocalEndScanStub,
        .idxSetDirection = NULL,
        .idxUndoInsert = HashIndexUndoInsert,
        .idxUndoRemove = HashIndexUndoRemove,
        .idxUndoUpdate = HashIndexUpdate,
        .idxGetKeyCount = NULL,
        .idxStatView = HashIndexStatView,
        .idxGetPageSize = HashIndexViewGetPageSize,
        .idxScaleIn = NULL,
        .idxGetEstimateMemSize = HashIndexGetEstimateMemSize,
        .idxGetCtxSize = HashIndexGetCtxSize,
        .idxPreload = NULL,
        .idxTableLoad = NULL,
    },
    {
        .idxCreate = CHIndexCreate,
        .idxDrop = CHIndexDrop,
        .idxCommitDrop = NULL,
        .idxTruncate = CHIndexTruncate,
        .idxCommitTruncate = NULL,
        .idxOpen = CHIndexOpen,
        .idxClose = CHIndexClose,
        .idxInsert = CHIndexInsert,
        .idxBatchInsert = CHIndexBatchInsert,
        .idxDelete = CHIndexDelete,
        .idxBatchDelete = CHIndexBatchDelete,
        .idxUpdate = CHIndexUpdate,
        .idxBatchUpdate = CHIndexBatchUpdate,
        .idxLookup = CHIndexLookup,
        .idxBatchLookup = CHIndexBatchLookup,
        .idxBeginScan = CHIndexBeginScan,
        .idxScan = CHIndexScan,
        .idxEndScan = ListLocalEndScanStub,
        .idxSetDirection = NULL,
        .idxUndoInsert = CHIndexUndoInsert,
        .idxUndoRemove = CHIndexUndoRemove,
        .idxUndoUpdate = CHIndexUpdate,
        .idxGetKeyCount = NULL,
        .idxStatView = CHIndexStatView,
        .idxGetPageSize = CHIndexViewGetPageSize,
        .idxScaleIn = NULL,
        .idxGetEstimateMemSize = CHIndexGetEstimateMemSize,
        .idxGetCtxSize = CHIndexGetCtxSize,
        .idxPreload = NULL,
        .idxTableLoad = NULL,
    },
    {
        .idxCreate = HashLinklistIndexCreate,
        .idxDrop = HashLinklistIndexDrop,
        .idxCommitDrop = NULL,
        .idxTruncate = HashLinklistIndexTruncate,
        .idxCommitTruncate = NULL,
        .idxOpen = HashLinklistIndexOpen,
        .idxClose = HashLinklistIndexClose,
        .idxInsert = HashLinklistIndexInsert,
        .idxBatchInsert = HashLinklistIndexBatchInsert,
        .idxDelete = HashLinklistIndexDelete,
        .idxBatchDelete = HashLinklistIndexBatchDelete,
        .idxUpdate = HashLinklistIndexUpdate,
        .idxBatchUpdate = HashLinklistIndexBatchUpdate,
        .idxLookup = NULL,
        .idxBatchLookup = NULL,
        .idxBeginScan = HashLinklistIndexBeginScan,
        .idxScan = HashLinklistIndexScan,
        .idxEndScan = HashLinklistIndexEndScan,
        .idxSetDirection = NULL,
        .idxUndoInsert = HashLinklistIndexUndoInsert,
        .idxUndoRemove = HashLinklistIndexUndoRemove,
        .idxUndoUpdate = HashLinklistIndexUndoUpdate,
        .idxGetKeyCount = HashLinklistIndexGetKeyCount,
        .idxStatView = HashLinklistIndexStatView,
        .idxGetPageSize = HashLinklistViewGetPageSize,
        .idxScaleIn = NULL,
        .idxGetEstimateMemSize = HashLinklistIndexGetEstimateMemSize,
        .idxGetCtxSize = HashLinklistGetCtxSize,
        .idxPreload = NULL,
        .idxTableLoad = NULL,
    },
};

void ListLocalhashIndexEndScan(const IndexCtxT *idxCtx, IndexScanItrT iter)
{
    uint32_t id = IdxGetListLocalhashId(idxCtx->idxMetaCfg.realIdxType, idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    g_hashLocalIndexFuncHandle[id].idxEndScan(idxCtx, iter);
}

Status ListLocalhashIndexCreate(SeRunCtxHdT seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr)
{
    uint32_t id = IdxGetListLocalhashId(idxCfg.realIdxType, idxCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxCreate(seRunCtx, idxCfg, idxShmAddr);
}

Status ListLocalhashIndexDrop(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    IdxBaseT *idxBase = (IdxBaseT *)DbShmPtrToAddr(idxShmAddr);
    if (SECUREC_UNLIKELY(idxBase == NULL)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    uint32_t id = IdxGetListLocalhashId(idxBase->indexCfg.realIdxType, idxBase->indexCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxDrop(seRunCtx, idxShmAddr);
}

Status ListLocalhashIndexTruncate(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr)
{
    IdxBaseT *idxBase = (IdxBaseT *)DbShmPtrToAddr(idxShmAddr);
    if (SECUREC_UNLIKELY(idxBase == NULL)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    uint32_t id = IdxGetListLocalhashId(idxBase->indexCfg.realIdxType, idxBase->indexCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxTruncate(seRunCtx, idxShmAddr);
}

Status ListLocalhashIndexOpen(IndexCtxT *idxCtx)
{
    uint32_t id = IdxGetListLocalhashId(idxCtx->idxMetaCfg.realIdxType, idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxOpen(idxCtx);
}

void ListLocalhashIndexClose(IndexCtxT *idxCtx)
{
    uint32_t id = IdxGetListLocalhashId(idxCtx->idxMetaCfg.realIdxType, idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    g_hashLocalIndexFuncHandle[id].idxClose(idxCtx);
}

Status ListLocalhashIndexInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    uint32_t id = IdxGetListLocalhashId(idxCtx->idxMetaCfg.realIdxType, idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxInsert(idxCtx, idxKey, addr);
}

Status ListLocalhashIndexBatchInsert(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum)
{
    uint32_t id = IdxGetListLocalhashId(idxCtx->idxMetaCfg.realIdxType, idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxBatchInsert(idxCtx, idxKey, addr, batchNum);
}

Status ListLocalhashIndexDelete(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr, IndexRemoveParaT removePara)
{
    uint32_t id = IdxGetListLocalhashId(idxCtx->idxMetaCfg.realIdxType, idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxDelete(idxCtx, idxKey, addr, removePara);
}

Status ListLocalhashIndexBatchDelete(
    IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum, IndexRemoveParaT removePara)
{
    uint32_t id = IdxGetListLocalhashId(idxCtx->idxMetaCfg.realIdxType, idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxBatchDelete(idxCtx, idxKey, addr, batchNum, removePara);
}

Status ListLocalhashIndexUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara)
{
    uint32_t id = IdxGetListLocalhashId(idxCtx->idxMetaCfg.realIdxType, idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxUpdate(idxCtx, updateInfo, removePara);
}

Status ListLocalhashIndexBatchUpdate(
    IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo[], uint32_t batchNum, IndexRemoveParaT removePara)
{
    uint32_t id = IdxGetListLocalhashId(idxCtx->idxMetaCfg.realIdxType, idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxBatchUpdate(idxCtx, updateInfo, batchNum, removePara);
}

Status ListLocalhashIndexLookup(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr *addr, bool *isFound)
{
    uint32_t id = IdxGetListLocalhashId(idxCtx->idxMetaCfg.realIdxType, idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxLookup(idxCtx, idxKey, addr, isFound);
}

Status ListLocalhashIndexBatchLookup(
    IndexCtxT *idxCtx, IndexKeyT idxKey[], uint32_t batchNum, IdxBatchLookupParaT *para)
{
    uint32_t id = IdxGetListLocalhashId(idxCtx->idxMetaCfg.realIdxType, idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxBatchLookup(idxCtx, idxKey, batchNum, para);
}

Status ListLocalhashIndexBeginScan(IndexCtxT *idxCtx, IndexScanCfgT scanCfg, IndexScanItrT *iter)
{
    uint32_t id = IdxGetListLocalhashId(idxCtx->idxMetaCfg.realIdxType, idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxBeginScan(idxCtx, scanCfg, iter);
}

Status ListLocalhashIndexScan(IndexCtxT *idxCtx, IndexScanItrT iter, HpTupleAddr *addr, bool *isFound)
{
    uint32_t id = IdxGetListLocalhashId(idxCtx->idxMetaCfg.realIdxType, idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxScan(idxCtx, iter, addr, isFound);
}

Status ListLocalhashIndexUndoInsert(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    uint32_t id = IdxGetListLocalhashId(idxCtx->idxMetaCfg.realIdxType, idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxUndoInsert(idxCtx, idxKey, addr);
}

Status ListLocalhashIndexUndoRemove(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr)
{
    uint32_t id = IdxGetListLocalhashId(idxCtx->idxMetaCfg.realIdxType, idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxUndoRemove(idxCtx, idxKey, addr);
}

Status ListLocalhashIndexUndoUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara)
{
    uint32_t id = IdxGetListLocalhashId(idxCtx->idxMetaCfg.realIdxType, idxCtx->idxMetaCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxUndoUpdate(idxCtx, updateInfo, removePara);
}

Status ListLocalhashIndexStatView(ShmemPtrT idxShmAddr, uint16_t instanceId, IndexStatisticsT *idxStat)
{
    IdxBaseT *idxBase = (IdxBaseT *)DbShmPtrToAddr(idxShmAddr);
    if (SECUREC_UNLIKELY(idxBase == NULL)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    uint32_t id = IdxGetListLocalhashId(idxBase->indexCfg.realIdxType, idxBase->indexCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxStatView(idxShmAddr, instanceId, idxStat);
}

Status ListLocalhashIndexViewGetPageSize(ShmemPtrT idxShmAddr, uint16_t instanceId, uint64_t *idxPageSize)
{
    IdxBaseT *idxBase = (IdxBaseT *)DbShmPtrToAddr(idxShmAddr);
    if (SECUREC_UNLIKELY(idxBase == NULL)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    uint32_t id = IdxGetListLocalhashId(idxBase->indexCfg.realIdxType, idxBase->indexCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxGetPageSize(idxShmAddr, instanceId, idxPageSize);
}

Status ListLocalhashIndexGetEstimateMemSize(
    uint16_t instanceId, IndexMetaCfgT idxMetaCfg, uint64_t count, uint32_t keyLen, uint64_t *size)
{
    uint32_t id = IdxGetListLocalhashId(idxMetaCfg.realIdxType, idxMetaCfg.idxConstraint != NON_UNIQUE);
    return g_hashLocalIndexFuncHandle[id].idxGetEstimateMemSize(instanceId, idxMetaCfg, count, keyLen, size);
}

void ListLocalhashIndexGetCtxSize(size_t *ctxSize, size_t *iterSize)
{
    size_t ctxSizeTmp = 0;
    size_t iterSizeTmp = 0;
    *ctxSize = 0;
    *iterSize = 0;
    for (uint32_t i = LIST_LOCALHASH_ID_MIN; i < LIST_LOCALHASH_ID__MAX; ++i) {
        g_hashLocalIndexFuncHandle[i].idxGetCtxSize(&ctxSizeTmp, &iterSizeTmp);
        *ctxSize = DB_MAX(*ctxSize, ctxSizeTmp);
        *iterSize = DB_MAX(*iterSize, iterSizeTmp);
    }
}

void HashLocalIndexAMInit(void)
{
    IdxFuncT hashLocalIndexFuncHandle = IdxEmptyIdxFunc();
    hashLocalIndexFuncHandle.idxCreate = ListLocalhashIndexCreate;
    hashLocalIndexFuncHandle.idxDrop = ListLocalhashIndexDrop;
    hashLocalIndexFuncHandle.idxTruncate = ListLocalhashIndexTruncate;
    hashLocalIndexFuncHandle.idxOpen = ListLocalhashIndexOpen;
    hashLocalIndexFuncHandle.idxClose = ListLocalhashIndexClose;
    hashLocalIndexFuncHandle.idxInsert = ListLocalhashIndexInsert;
    hashLocalIndexFuncHandle.idxBatchInsert = ListLocalhashIndexBatchInsert;
    hashLocalIndexFuncHandle.idxDelete = ListLocalhashIndexDelete;
    hashLocalIndexFuncHandle.idxBatchDelete = ListLocalhashIndexBatchDelete;
    hashLocalIndexFuncHandle.idxUpdate = ListLocalhashIndexUpdate;
    hashLocalIndexFuncHandle.idxBatchUpdate = ListLocalhashIndexBatchUpdate;
    hashLocalIndexFuncHandle.idxLookup = ListLocalhashIndexLookup;
    hashLocalIndexFuncHandle.idxBatchLookup = ListLocalhashIndexBatchLookup;
    hashLocalIndexFuncHandle.idxBeginScan = ListLocalhashIndexBeginScan;
    hashLocalIndexFuncHandle.idxScan = ListLocalhashIndexScan;
    hashLocalIndexFuncHandle.idxEndScan = ListLocalhashIndexEndScan;
    hashLocalIndexFuncHandle.idxUndoInsert = ListLocalhashIndexUndoInsert;
    hashLocalIndexFuncHandle.idxUndoRemove = ListLocalhashIndexUndoRemove;
    hashLocalIndexFuncHandle.idxUndoUpdate = ListLocalhashIndexUndoUpdate;
    hashLocalIndexFuncHandle.idxStatView = ListLocalhashIndexStatView;
    hashLocalIndexFuncHandle.idxGetPageSize = ListLocalhashIndexViewGetPageSize;
    hashLocalIndexFuncHandle.idxGetEstimateMemSize = ListLocalhashIndexGetEstimateMemSize;
    hashLocalIndexFuncHandle.idxGetCtxSize = ListLocalhashIndexGetCtxSize;  // 该接口目前只获取所有索引类型最大的内存值
    IdxAmFuncRegister((uint8_t)LIST_LOCALHASH_INDEX, &hashLocalIndexFuncHandle);
}

#ifdef __cplusplus
}
#endif
