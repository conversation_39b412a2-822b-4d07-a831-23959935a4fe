/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: se_list_localhash_index.h
 * Description: list-localhash interfaces
 * Author: lujiahao
 * Create: 2024-4-7
 */

#ifndef SE_HASH_LIST_LOCALHASH_INDEX_H
#define SE_HASH_LIST_LOCALHASH_INDEX_H

#include "se_hash_common.h"

#ifdef __cplusplus
extern "C" {
#endif

Status ListLocalhashIndexBatchInsert(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum);

Status ListLocalhashIndexBatchDelete(
    IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum, IndexRemoveParaT removePara);

Status ListLocalhashIndexBatchUpdate(
    IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo[], uint32_t batchNum, IndexRemoveParaT removePara);

Status ListLocalhashIndexUndoRemove(IndexCtxT *idxCtx, IndexKeyT idxKey, TupleAddr addr);

Status ListLocalhashIndexUndoUpdate(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara);

Status ListLocalhashIndexStatView(ShmemPtrT idxShmAddr, uint16_t instanceId, IndexStatisticsT *idxStat);

Status ListLocalhashIndexViewGetPageSize(ShmemPtrT idxShmAddr, uint16_t instanceId, uint64_t *idxPageSize);

Status ListLocalhashIndexGetEstimateMemSize(
    uint16_t instanceId, IndexMetaCfgT idxMetaCfg, uint64_t count, uint32_t keyLen, uint64_t *size);

#ifdef __cplusplus
}
#endif
#endif  // __SE_HASH_LIST_LOCALHASH_INDEX_H__
