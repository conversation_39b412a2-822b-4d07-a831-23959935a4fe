/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: implementation of diskann ctxt
 * Author: wanyi
 * Create: 2024-02-19
 */
#include "se_diskann_storage.h"

#include <float.h>
#include "dm_data_annvector.h"
#include "se_ann_index.h"
#include "se_ann_utils.h"
#include "se_database.h"
#include "se_diskann_redo_am.h"
#include "se_diskann_vertex_manager.h"
#include "se_diskann_undo.h"
#include "se_diskann_utils.h"
#include "se_index_common.h"
#include "se_vectorarray.h"

#define CROSS_TERM_COEFFICIENT (2)
#define DISKANN_MAGIC_NUMBER (123)

// 要对空指针的情况做处理, 所以进行了一次封装
static inline void DeepCopy(void *dest, const void *src, uint32_t size)
{
    if (src == NULL || dest == NULL) {
        return;
    }
    (void)memcpy_s(dest, size, src, size);
}

Status DiskAnnStorageAdapterInit(const IndexMetaCfgT *idxCfg, uint16_t instanceId, DiskAnnCtxT **newAdapter)
{
    DB_POINTER(newAdapter);
    // 给adapter申请内存
    SeInstanceHdT seInstance = SeGetInstance(instanceId);
    if (seInstance == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "|SE-DISKANN| unable to get se instance with id");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DbMemCtxT *diskAnnMemCtx = (DbMemCtxT *)seInstance->seServerMemCtx;
    if (diskAnnMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "|SE-DISKANN| unable to get seServerMemCtx.");
        return GMERR_INTERNAL_ERROR;
    }
    DiskAnnCtxT *adpt = NULL;
    Status ret = SeAnnAllocAndInitItem(diskAnnMemCtx, sizeof(DiskAnnCtxT), 0, (uint8_t **)&adpt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnStorageAdapterInit| Unable to SeAnnAllocAndInitItem");
        return ret;
    }
    // 初始化必要的成员变量（以使用PageMgr)
    adpt->pageMgr = SeGetPageMgr(seInstance->instanceId);
    if (adpt->pageMgr == NULL) {
        DB_LOG_ERROR(
            GMERR_INTERNAL_ERROR, "|SE-DISKANN| unable to get pageMgr with id %" PRIu16, seInstance->instanceId);
        SeAnnFreeItem(diskAnnMemCtx, (uint8_t **)&adpt);
        return GMERR_INTERNAL_ERROR;
    }
    adpt->lastDis = 0.0;
    adpt->lastPos = 0;
    adpt->qCtx = NULL;
    adpt->meta.frozens = NULL;
    adpt->meta.trmId = SeGetNewTrmId(seInstance);
    adpt->meta.pageSize = seInstance->seConfig.pageSize * DB_KIBI;  // 注意！这里的pageSize是以kb为基准
    adpt->fileVersion = seInstance->db->core.fileFormatVersion;
    if (idxCfg != NULL) {
        adpt->meta.idxBase.indexCfg = *idxCfg;
    }
    *newAdapter = adpt;
    return GMERR_OK;
}

void DiskAnnStorageAdapterFree(DiskAnnCtxT *adpt, uint16_t instanceId)
{
    if (adpt == NULL) {
        return;
    }
    SeInstanceHdT seInstance = SeGetInstance(instanceId);
    if (seInstance == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "|SE-DISKANN| unable to get se instance with id");
        return;
    }
    DbMemCtxT *diskAnnMemCtx = (DbMemCtxT *)seInstance->seServerMemCtx;
    if (diskAnnMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "|SE-DISKANN| unable to get seServerMemCtx.");
        return;
    }
    if (adpt->memCtx != NULL && adpt->meta.frozens != NULL) {
        // Create接口也会调用这个函数，但Create场景没有申请，不释放
        SeAnnFreeItem(adpt->memCtx, (uint8_t **)&(adpt->meta.frozens));
    }
    SeAnnFreeItem(diskAnnMemCtx, (uint8_t **)&adpt);
    return;
}

Status DiskAnnLoadData(DiskAnnCtxT *adpt, uint64_t dataId, void *vector, DiskAnnAddrT *outId)
{
    DB_POINTER3(adpt, vector, outId);
    DiskAnnNodeT node = {0};
    node.vertexId = SE_INVALID_VERTEX_ID;
    node.masterId = SE_INVALID_VERTEX_ID;
    node.preSlaveId = SE_INVALID_VERTEX_ID;
    node.nextSlaveId = SE_INVALID_VERTEX_ID;
    node.flag = 0;
    node.dataID = dataId;
    Status ret = GMERR_OK;
    if (adpt->meta.quantMode == ANN_QUANT_LVQ) {
        ret = AnnQuantGenLvq(adpt->qCtx, 0, vector, (uint8_t **)&(node.vecs));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnLoadData| Unable to AnnQuantGenLvq");
            return ret;
        }
    } else {
        AnnVecT tmpVec = {.type = adpt->meta.dataType, .dim = adpt->meta.dim, .items = vector};
        double sqrSum = 0.0;
        ret = DmAnnVectorGetNorm(TYPE_VECNORM_SQUARE, &tmpVec, &sqrSum);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnLoadData| Unable to VectorGetNorm");
            return ret;
        }
        node.sqrSum = sqrSum;
        node.vecs = vector;
    }
    ret = DiskAnnNodeSet(adpt, &node);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnLoadData| Unable to DiskAnnNodeSet");
        return ret;
    }
    *outId = node.vertexId;
    if (adpt->meta.quantMode == ANN_QUANT_LVQ) {
        // lvq模式node.vecs是临时申请的内存，需要释放
        SeAnnFreeItem(adpt->qCtx->memCtx, (uint8_t **)&(node.vecs));
    }
    return GMERR_OK;
}

static void DiskAnnInitMetaPageHead(uint8_t *page)
{
    PageHeadT *pageHdr = (PageHeadT *)(void *)page;
    pageHdr->beginPos = (uint32_t)sizeof(PageHeadT);
    pageHdr->beginPos += (uint32_t)sizeof(DiskAnnMetaT);
    pageHdr->freeSize = (uint16_t)(pageHdr->endPos - pageHdr->beginPos);
}

static bool DiskAnnPageSizeValid(const DiskAnnCtxT *adpt, uint8_t *page)
{
    size_t requiredSize =
        sizeof(PageHeadT) + sizeof(DiskAnnNodeHdrT) + SIZE_OF_SLOTID +
        GetDiskAnnNodeSize(adpt->meta.reserveDist, adpt->meta.numQuantBit, adpt->meta.dim, adpt->meta.outDegree);
    if (!(((PageHeadT *)(void *)page)->endPos >= requiredSize)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE,
            "|SE-DISKANN| |DiskAnnInitMetaData| pageSize isn't large enough |dim||%u|, |pqVecDim||%u|, "
            "|outDegree||%u|, |requiredSize||%ld|, |available||%u|",
            adpt->meta.dim, adpt->meta.pqVecDim, adpt->meta.outDegree, requiredSize,
            ((PageHeadT *)(void *)page)->endPos);
        return false;
    }
    return true;
}

ALWAYS_INLINE_C static void InitMetaNumQuantBit(AnnMetaCfgT *metaCfg, DiskAnnMetaT *meta)
{
    if (metaCfg->quantMode == ANN_QUANT_LVQ) {
        meta->numQuantBit = metaCfg->numQuantBit;
    } else {
        // 32是float类型的比特数, 16是float16类型的比特数
        meta->numQuantBit = DmDataTypeIsFloatVector(metaCfg->dataType) ? 32 : 16;
    }
}

static void InitMetaParaInner(DiskAnnCtxT *adpt, uint8_t *head, const DiskAnnPageIdT *metaPageId, DiskAnnMetaT *meta)
{
    meta->idxBase.indexCfg = adpt->meta.idxBase.indexCfg;
    meta->magicNumber = DISKANN_MAGIC_NUMBER;
    meta->version = 1;  // set from 0 to 1 on 0821-1121
    meta->trmId = adpt->meta.trmId;
    meta->tableSpaceIndex = adpt->meta.idxBase.indexCfg.tableSpaceIndex;
    meta->pageSize = adpt->meta.pageSize * DB_KIBI;
    AnnMetaCfgT *metaCfg = (AnnMetaCfgT *)(adpt->meta.idxBase.indexCfg.extendParam);
    meta->rawVecDim = metaCfg->vectorDim;
    meta->dim = metaCfg->quantMode == ANN_QUANT_LVQ ? GetAnnLvqSize(metaCfg->vectorDim, metaCfg->numQuantBit) :
                                                      metaCfg->vectorDim;
    InitMetaNumQuantBit(metaCfg, meta);
    meta->vertexTypeSize = CalVertexTypeSize(meta->numQuantBit, meta->dim);
    meta->outDegree = metaCfg->outDegree;
    meta->robustAlpha = metaCfg->robustAlpha;
    meta->numFrozens = metaCfg->numFrozens;
    meta->enablePQ = false;
    meta->reserveDist = metaCfg->reserveDist;
    meta->acceleratePrune = metaCfg->acceleratePrune;
    meta->dataType = metaCfg->dataType;
    meta->quantMode = metaCfg->quantMode;
    meta->distType = metaCfg->distType;
    meta->isVaccumDone = false;
    meta->pqVecDim = 0;
    if (meta->enablePQ) {
        meta->pqVecDim = 0;
        uint16_t pqNodeSize = CalPqVecSize(meta->pqVecDim);
        DiskAnnNodeMgrInit(&meta->pqNodeMgr, pqNodeSize, CalMaxSlotNum(head, pqNodeSize), DISKANN_PQ);
    }
    uint16_t vertexNodeSize = GetDiskAnnNodeSize(meta->reserveDist, meta->numQuantBit, meta->dim, meta->outDegree);
    DiskAnnNodeMgrInit(&meta->vertexNodeMgr, vertexNodeSize, CalMaxSlotNum(head, vertexNodeSize), DISKANN_VERTEX);
    size_t delListNodeSize = sizeof(DiskAnnAddrT);
    DiskAnnNodeMgrInit(
        &meta->delNodeListPageIdActivated, delListNodeSize, CalMaxSlotNum(head, delListNodeSize), DISKANN_LIST);
    DiskAnnNodeMgrInit(
        &meta->delNodeListPageIdLocked, delListNodeSize, CalMaxSlotNum(head, delListNodeSize), DISKANN_LIST);
    meta->startVertexId = SE_INVALID_VERTEX_ID;
    meta->endVertexId = SE_INVALID_VERTEX_ID;
    meta->metaPageId = *metaPageId;
    meta->nextPageToVaccum = SE_INVALID_DISKANN_ADDR;
    meta->numNodes = 0;
    meta->numFakeDelNodes = 0;
    meta->vaccumPara = metaCfg->vaccumParaCfg;
    meta->queueSize = metaCfg->queueSize;
    (void)memset_s(GetFrozenPointsFromMeta(meta), GetFrozenPointsSize(metaCfg->numFrozens), 0,
        GetFrozenPointsSize(metaCfg->numFrozens));
}

static bool DiskAnnCompatibleCheck(DiskAnnCtxT *adpt)
{
    AnnMetaCfgT *metaCfg = (AnnMetaCfgT *)(adpt->meta.idxBase.indexCfg.extendParam);
    if (!AnnIsFirstVersion4DiskAnn(adpt->fileVersion)) {
        return true;
    }
    if (metaCfg->acceleratePrune || (!metaCfg->reserveDist) || (metaCfg->quantMode != ANN_QUANT_NONE)) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED,
            "version doesn't support accelerate_prune %d, or not reserve dist %d or quant %d", metaCfg->acceleratePrune,
            !metaCfg->reserveDist, metaCfg->quantMode);
        return false;
    }
    return true;
}

static Status DiskAnnInitMetaPara(DiskAnnCtxT *adpt, uint8_t *head, DiskAnnPageIdT *metaPageId, RedoRunCtxT *redoCtx)
{
    // 开始给Meta页赋值
    DiskAnnMetaT *meta = GetDiskAnnMeta(head);
    DiskAnnUndoForInitMeta(adpt, *metaPageId, meta);
    DbRWLatchInit(&meta->idxBase.idxLatch);
    // 兼容性校验，老版本不允许使用新特性
    if (!DiskAnnCompatibleCheck(adpt)) {
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    // meta页赋值
    InitMetaParaInner(adpt, head, metaPageId, meta);
    IdxConstructedFinish(&meta->idxBase);
    DiskAnnInitMetaPageHead(head);
    DiskAnnRedoForInitMeta(adpt, *metaPageId, meta, redoCtx);
    adpt->meta = *meta;
    return GMERR_OK;
}

Status DiskAnnInitMetaData(DiskAnnCtxT *adpt, DiskAnnPageIdT *metaPageId)
{
    DB_POINTER2(adpt, metaPageId);
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    Status ret = DiskAnnAllocPage(adpt, metaPageId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnInitMetaData| Unable to DiskAnnAllocPage");
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    uint8_t *head = NULL;
    ret = DiskAnnGetPage(adpt, *metaPageId, true, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnInitMetaData| Unable to DiskAnnGetPage procedure");
        (void)DiskAnnFreePage(adpt, *metaPageId);
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    ret = DiskAnnInitMetaPara(adpt, head, metaPageId, redoCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnInitMetaData| Unable to DiskAnnInitMetaPara");
        DiskAnnLeavePage(adpt, *metaPageId, false);
        DiskAnnFreePage(adpt, *metaPageId);
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    if (!DiskAnnPageSizeValid(adpt, head)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "|SE-DISKANN| |DiskAnnInitMetaData| pageSize isn't large enough");
        DiskAnnLeavePage(adpt, *metaPageId, false);
        (void)DiskAnnFreePage(adpt, *metaPageId);
        (void)RedoLogEnd(redoCtx, false);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DiskAnnLeavePage(adpt, *metaPageId, true);
    ret = DbGetExternalErrno(IndexLogDDLUndo(adpt->seRunCtx, &adpt->meta.idxBase.indexCfg,
        DeserializeDiskAnnPageId(adpt->pageMgr, *metaPageId), TRX_RES_DISKANN, TRX_OP_CREATE));
    if (ret != GMERR_OK) {
        (void)DiskAnnFreePage(adpt, *metaPageId);
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    adpt->meta.metaPageId = *metaPageId;
    return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
}

Status DiskAnnLoadMetaData(DiskAnnCtxT *adpt, const DiskAnnPageIdT *metaPageId)
{
    DB_POINTER2(adpt, metaPageId);
    uint8_t *head = NULL;
    Status ret = DiskAnnGetPage(adpt, *metaPageId, false, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnLoadMetaData| Unable to DiskAnnGetPage");
        return ret;
    }
    DiskAnnMetaT *meta = GetDiskAnnMeta(head);
    adpt->meta = *meta;
    if (AnnIsFirstVersion4DiskAnn(adpt->fileVersion)) {
        adpt->meta.numQuantBit = 32;  // 32是float类型的比特数
        adpt->meta.quantMode = ANN_QUANT_NONE;
        adpt->meta.rawVecDim = meta->dim;  // 旧版本的维度保存在dim中
        adpt->meta.reserveDist = true;
        adpt->meta.acceleratePrune = false;
    }
    ret = SeAnnAllocAndInitItem(
        adpt->memCtx, GetFrozenPointsSize(adpt->meta.numFrozens), 0, (uint8_t **)(&adpt->meta.frozens));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnLoadMetaData| Unable to SeAnnAllocAndInitItem");
        DiskAnnLeavePage(adpt, *metaPageId, false);
        return ret;
    }
    DeepCopy(adpt->meta.frozens, GetFrozenPointsFromMeta(meta), GetFrozenPointsSize(adpt->meta.numFrozens));
    DiskAnnLeavePage(adpt, *metaPageId, false);
    return GMERR_OK;
}

static Status FreeDataRecursively(DiskAnnCtxT *adpt, DiskAnnPageIdT firtPageToFree)
{
    DB_POINTER(adpt);
    DiskAnnPageIdT pageToFree = firtPageToFree;
    DiskAnnPageIdT nextPage = SE_INVALID_DISKANN_ADDR;
    while (!IsPageUnused(&pageToFree)) {
        // 根据vertexId找到下一个节点
        uint8_t *head = NULL;
        Status ret = DiskAnnGetPage(adpt, pageToFree, false, &head);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |FreeDataRecursively| Unable to DiskAnnGetVertexPoint");
            return ret;
        }
        nextPage = GetDiskAnnNodeHdrFromPageHead(head)->nextPageId;
        DiskAnnLeavePage(adpt, pageToFree, false);
        // 释放当前节点
        RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
        RedoLogBegin(redoCtx);
        ret = DiskAnnFreePage(adpt, pageToFree);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |FreeDataRecursively| Unable to DiskAnnFreePage");
            (void)RedoLogEnd(redoCtx, false);
            return ret;
        }
        ret = DbGetExternalErrno(RedoLogEnd(redoCtx, true));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |FreeDataRecursively| Unable to RedoLogEnd");
            return ret;
        }
        // 更新需要被释放的节点
        if (IsPageEqual(pageToFree, nextPage)) {
            break;
        }
        pageToFree = nextPage;
    }
    return GMERR_OK;
}

static Status DiskAnnFreePageDelNodeList(DiskAnnCtxT *adpt)
{
    DB_POINTER(adpt);

    DiskAnnPageIdT delNodeListPageIdActivated = adpt->meta.delNodeListPageIdActivated.dataListHead;
    Status ret = FreeDataRecursively(adpt, delNodeListPageIdActivated);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnFreePageDelNodeList| Unable to DiskAnnFreePage Activated");
        return ret;
    }
    DiskAnnPageIdT delNodeListPageIdLocked = adpt->meta.delNodeListPageIdLocked.dataListHead;
    ret = FreeDataRecursively(adpt, delNodeListPageIdLocked);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnFreePageDelNodeList| Unable to DiskAnnFreePageLocked");
    }
    return ret;
}

Status DiskAnnFreeData(DiskAnnCtxT *adpt, const DiskAnnPageIdT *metaPageId, bool shouldFreeMeta)
{
    DB_POINTER2(adpt, metaPageId);
    // 从metaPageId获得metaPageId
    uint8_t *head = NULL;
    Status ret = DiskAnnGetPage(adpt, *metaPageId, false, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnFreeData| Unable to DiskAnnGetPage with read");
        return ret;
    }
    DiskAnnMetaT *meta = GetDiskAnnMeta(head);
    adpt->meta = *meta;
    adpt->meta.frozens = NULL;
    DiskAnnLeavePage(adpt, *metaPageId, false);

    ret = FreeDataRecursively(adpt, adpt->meta.vertexNodeMgr.dataListHead);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnFreeData| Unable to FreeDataRecursively");
        return ret;
    }
    ret = DiskAnnFreePageDelNodeList(adpt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnFreeData| Unable to DiskAnnFreePageDelNodeList");
        return ret;
    }

    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    if (!shouldFreeMeta) {
        // build失败, 仅重置索引的数据部分
        ret = DiskAnnGetPage(adpt, *metaPageId, true, &head);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnFreeData| Unable to DiskAnnGetPage with write");
            return ret;
        }
        meta->vertexNodeMgr.dataListHead = SE_INVALID_DISKANN_ADDR;
        meta->vertexNodeMgr.dataListEnd = SE_INVALID_DISKANN_ADDR;
        (void)memset_s(GetFrozenPointsFromMeta(meta), GetFrozenPointsSize(meta->numFrozens), 0,
            GetFrozenPointsSize(meta->numFrozens));
        DiskAnnRedoForResetMeta(adpt, *metaPageId, meta, redoCtx);
        DiskAnnLeavePage(adpt, *metaPageId, true);
        return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
    }
    // drop索引, 释放元数据页
    ret = DiskAnnFreePage(adpt, *metaPageId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnFreeData| Unable to DiskAnnFreePage Meta");
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }

    return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
}

Status DiskAnnGetMasterNode(DiskAnnCtxT *adpt, const DiskAnnAddrT nodeId, DiskAnnAddrT *masterId)
{
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, nodeId, false, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnGetMasterNode| Unable to DiskAnnGetVertexPoint");
        return ret;
    }
    DiskAnnVertexT *vertex = GetVertexFromPage(head);
    *masterId = vertex->masterId;
    DiskAnnLeaveVertexPoint(adpt, nodeId, false);
    return GMERR_OK;
}

static void RemoveNodeFromNbsWithRedo(
    DiskAnnCtxT *adpt, uint8_t *masterHead, DiskAnnAddrT vertexId, RedoRunCtxT *redoCtx)
{
    uint8_t *edge = GetEdgeFromPage(masterHead, adpt->meta.vertexTypeSize);
    uint32_t numNb = GetNumNbFromEdge(edge);
    bool isFound = false;
    uint32_t pos = 0;
    DiskAnnAddrT *ids = (DiskAnnAddrT *)(void *)GetNbVertexIdsFromEdge(edge);
    float *dis = (float *)GetNbDistsFromEdge(edge, adpt->meta.outDegree);
    DiskAnnRemoveFromNbsParaT undoParam = {0};
    for (uint32_t i = 0; i < numNb; i++) {
        if (IsVertexEqual(ids[i], vertexId)) {
            isFound = true;
            pos = i;
            break;
        }
    }
    if (!isFound) {
        return;
    }
    uint32_t last = (uint32_t)(numNb - 1);
    undoParam.idxToRemove = pos;
    undoParam.lastIdxInNbs = last;
    undoParam.idsPos = ids[pos];
    undoParam.idsLast = ids[last];
    undoParam.disPos = dis[pos];
    undoParam.disLast = dis[last];
    if (last == pos) {
        ids[pos] = SE_INVALID_VERTEX_ID;
        dis[pos] = SE_INVALID_DISTANCE_IN_EDGE;
    } else {
        ids[pos] = ids[last];
        dis[pos] = dis[last];
        ids[last] = SE_INVALID_VERTEX_ID;
        dis[last] = SE_INVALID_DISTANCE_IN_EDGE;
    }
    *(uint32_t *)edge = numNb - 1;
    DiskAnnRedoUndoParamT redoParam = {
        .vertexId = GetVertexIdFromPage(masterHead),
        .edgeOffsetFromVertex = adpt->meta.vertexTypeSize,
        .outDegree = adpt->meta.outDegree,
        .rawVecDim = adpt->meta.dim,
    };
    DiskAnnRedoForRemoveFromNbs(adpt, &redoParam, pos, last, redoCtx);
    DiskAnnUndoForRemoveFromNbs(adpt, &redoParam, undoParam);
}

static Status DiskAnnSetPreSlaveForVertexWithRedo(
    DiskAnnCtxT *adpt, DiskAnnAddrT vertexId, DiskAnnAddrT preSlave, RedoRunCtxT *redoCtx)
{
    DB_POINTER2(adpt, redoCtx);
    if (IsVertexUnused(&vertexId)) {
        DB_LOG_ERROR(
            GMERR_INVALID_PARAMETER_VALUE, "|SE-DISKANN| |DiskAnnSetPreSlaveForVertexWithRedo| vertexId unused");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, vertexId, true, (uint8_t **)&head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnSetPreSlaveForVertexWithRedo| Unable to DiskAnnGetVertexPoint");
        return ret;
    }
    // 写入节点信息
    DiskAnnVertexT *vertex = GetVertexFromPage(head);
    ret = DiskAnnUndoForSetPreSlaveId(adpt, vertexId, vertex->preSlaveId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnSetPreSlaveForVertexWithRedo| Unable to DiskAnnUndoForSetPreSlaveId");
        DiskAnnLeaveVertexPoint(adpt, vertexId, false);
        return ret;
    }
    vertex->preSlaveId = preSlave;
    DiskAnnRedoForSetPreSlaveId(adpt, vertexId, preSlave, redoCtx);
    DiskAnnLeaveVertexPoint(adpt, vertexId, true);
    return GMERR_OK;
}

static Status DiskAnnSetNextSlaveForVertexWithRedo(
    DiskAnnCtxT *adpt, DiskAnnAddrT vertexId, DiskAnnAddrT nextSlave, RedoRunCtxT *redoCtx)
{
    DB_POINTER2(adpt, redoCtx);
    if (IsVertexUnused(&vertexId)) {
        DB_LOG_ERROR(
            GMERR_INVALID_PARAMETER_VALUE, "|SE-DISKANN| |DiskAnnSetNextSlaveForVertexWithRedo| vertexId unused");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, vertexId, true, (uint8_t **)&head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnSetNextSlaveForVertexWithRedo| Unable to DiskAnnGetVertexPoint");
        return ret;
    }
    // 写入节点信息
    DiskAnnVertexT *vertex = GetVertexFromPage(head);
    ret = DiskAnnUndoForSetNextSlaveId(adpt, vertexId, vertex->nextSlaveId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnSetNextSlaveForVertexWithRedo| Unable to DiskAnnUndoForSetPreSlaveId");
        DiskAnnLeaveVertexPoint(adpt, vertexId, false);
        return ret;
    }
    vertex->nextSlaveId = nextSlave;
    DiskAnnRedoForSetPreSlaveId(adpt, vertexId, nextSlave, redoCtx);
    DiskAnnLeaveVertexPoint(adpt, vertexId, true);
    return GMERR_OK;
}

static Status CheckIfInSlaveChain(
    DiskAnnCtxT *ctxt, DiskAnnAddrT vertexId, DiskAnnAddrT preSlaveId, DiskAnnAddrT nextSlaveId, bool *isInSlaveChain)
{
    // 判断节点是否在slaveChain中，防止对孤立节点的前后驱进行connect
    *isInSlaveChain = true;
    // 没有前驱节点，肯定不在slaveChain中
    if (IsVertexUnused(&preSlaveId)) {
        *isInSlaveChain = false;
        return GMERR_OK;
    }

    // 判断pre节点的next是否是当前节点
    DiskAnnVertexT *node = NULL;
    Status ret = DiskAnnVertexGet(ctxt, preSlaveId, false, &node);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |CheckIfInSlaveChain| Unable to DiskAnnVertexGet");
        DiskAnnVertexFree(ctxt, &node);
        return ret;
    }
    if (!IsVertexEqual(node->nextSlaveId, vertexId)) {
        *isInSlaveChain = false;
        DiskAnnVertexFree(ctxt, &node);
        return GMERR_OK;
    }
    DiskAnnVertexFree(ctxt, &node);
    node = NULL;

    // 后驱节点为空，可以在slaveChain中
    if (IsVertexUnused(&nextSlaveId)) {
        *isInSlaveChain = true;
        return GMERR_OK;
    }

    // 判断next节点的pre是否是当前节点
    ret = DiskAnnVertexGet(ctxt, nextSlaveId, false, &node);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |CheckIfInSlaveChain| Unable to DiskAnnVertexGet");
        DiskAnnVertexFree(ctxt, &node);
        return ret;
    }
    if (!IsVertexEqual(node->preSlaveId, vertexId)) {
        *isInSlaveChain = false;
    }
    DiskAnnVertexFree(ctxt, &node);
    node = NULL;
    return GMERR_OK;
}

static Status ConnectSlaveNeighborsInner(
    DiskAnnCtxT *ctxt, DiskAnnAddrT vertexId, DiskAnnAddrT preSlaveId, DiskAnnAddrT nextSlaveId)
{
    // 只有节点在slaveChain中才需要conn操作
    bool isInSlaveChain = true;
    Status ret = CheckIfInSlaveChain(ctxt, vertexId, preSlaveId, nextSlaveId, &isInSlaveChain);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |ConnectSlaveNeighbors| Unable to CheckIfInSlaveChain");
        return ret;
    }
    if (isInSlaveChain == false) {
        return GMERR_OK;
    }

    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);

    // nextSlave为空是末尾节点的正常情况，需要skip SetPreSlave方法，否则参数检查会报错
    if (!IsVertexUnused(&nextSlaveId)) {
        ret = DiskAnnSetPreSlaveForVertexWithRedo(ctxt, nextSlaveId, preSlaveId, redoCtx);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |ConnectSlaveNeighbors| Unable to SetPreSlaveForVertexWithRedo");
            (void)RedoLogEnd(redoCtx, false);
            return ret;
        }
    }

    ret = DiskAnnSetNextSlaveForVertexWithRedo(ctxt, preSlaveId, nextSlaveId, redoCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |ConnectSlaveNeighbors| Unable to SetNextSlaveForVertexWithRedo");
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
}

Status ConnectSlaveNeighbors(DiskAnnCtxT *ctxt, DiskAnnAddrT vertexId)
{
    DB_POINTER(ctxt);
    if (IsVertexUnused(&vertexId)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "|SE-DISKANN| |ConnectSlaveNeighbors| vertexId unused");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DiskAnnVertexT *node = NULL;
    Status ret = DiskAnnVertexGet(ctxt, vertexId, false, &node);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |ConnectSlaveNeighbors| Unable to DiskAnnVertexGet");
        return ret;
    }
    DiskAnnAddrT preSlaveId = node->preSlaveId;
    DiskAnnAddrT nextSlaveId = node->nextSlaveId;
    if ((node->flag & DISKANN_SLAVE_NODE) != DISKANN_SLAVE_NODE) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "|SE-DISKANN| |ConnectSlaveNeighbors| node cannot be master");
        DiskAnnVertexFree(ctxt, &node);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DiskAnnVertexFree(ctxt, &node);

    return ConnectSlaveNeighborsInner(ctxt, vertexId, preSlaveId, nextSlaveId);
}

static Status DiskAnnSetNewSlaveNode(DiskAnnCtxT *adpt, DiskAnnVertexT *newSlaveNode, RedoRunCtxT *redoCtx)
{
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, newSlaveNode->vertexId, true, (uint8_t **)&head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnGetVertexPoint| Unexpected event has happened during "
                          "DiskAnnGetVertexPoint procedure");
        return ret;
    }
    // 写入节点信息
    DiskAnnVertexT *vertex = GetVertexFromPage(head);
    ret = DiskAnnUndoForSetNewSlave(adpt, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnSetNewSlaveNode| Unable to DiskAnnUndoForSetNewSlave");
        DiskAnnLeaveVertexPoint(adpt, newSlaveNode->vertexId, false);
        return ret;
    }
    vertex->flag |= DISKANN_SLAVE_NODE;
    vertex->preSlaveId = newSlaveNode->preSlaveId;
    vertex->nextSlaveId = newSlaveNode->nextSlaveId;
    vertex->masterId = newSlaveNode->masterId;
    // 给边信息赋0值
    void *edge = GetEdgeFromPage(head, adpt->meta.vertexTypeSize);
    uint32_t numNb = GetNumNbFromEdge(edge);
    DiskAnnAddrT *ids = (DiskAnnAddrT *)(void *)GetNbVertexIdsFromEdge(edge);
    float *dis = (float *)GetNbDistsFromEdge(edge, adpt->meta.outDegree);
    for (uint16_t pos = 0; pos < numNb; pos++) {
        ids[pos] = SE_INVALID_VERTEX_ID;
        dis[pos] = SE_INVALID_DISTANCE_IN_EDGE;
    }
    SetNbInEdge((uint8_t *)edge, 0);
    DiskAnnRedoForSetNewSlave(adpt, vertex, redoCtx);
    DiskAnnLeaveVertexPoint(adpt, newSlaveNode->vertexId, true);
    return GMERR_OK;
}

static Status IsMasterContainSlave(DiskAnnCtxT *adpt, DiskAnnVertexT *masterVertex, DiskAnnAddrT newSlaveId)
{
    DiskAnnAddrT nextSlave = masterVertex->nextSlaveId;
    while (!IsVertexUnused(&nextSlave)) {
        if (IsVertexEqual(nextSlave, newSlaveId)) {
            return GMERR_DUPLICATE_OBJECT;
        }

        DiskAnnVertexT *vertex = NULL;
        Status ret = DiskAnnVertexGet(adpt, nextSlave, false, &vertex);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |IsMasterContainSlave| Unable to DiskAnnVertexGet");
            return ret;
        }
        nextSlave = vertex->nextSlaveId;
        DiskAnnVertexFree(adpt, &vertex);
    }
    return GMERR_OK;
}

static Status InsertSlaveToList(
    DiskAnnCtxT *adpt, uint8_t *masterHead, DiskAnnVertexT *newSlaveNode, RedoRunCtxT *redoCtx)
{
    DB_POINTER4(adpt, masterHead, newSlaveNode, redoCtx);
    DiskAnnVertexT *masterVertex = GetVertexFromPage(masterHead);
    DiskAnnAddrT masterId = newSlaveNode->masterId;
    DiskAnnAddrT slaveId = newSlaveNode->vertexId;
    Status ret = DiskAnnSetNewSlaveNode(adpt, newSlaveNode, redoCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret,
            "|SE-DISKANN| |InsertSlaveToList| Unexpected event has happened during DiskAnnSetNewSlaveNode procedure");
        return ret;
    }
    if (!IsVertexUnused(&masterVertex->nextSlaveId)) {
        ret = DiskAnnSetPreSlaveForVertexWithRedo(adpt, masterVertex->nextSlaveId, slaveId, redoCtx);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |InsertSlaveToList| Unexpected event has happened during "
                              "DiskAnnSetPreSlaveForVertexWithRedo procedure");
            return ret;
        }
    }
    ret = DiskAnnUndoForSetNextSlaveId(adpt, masterId, masterVertex->nextSlaveId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |InsertSlaveToList| Unable to DiskAnnUndoForSetNextSlaveId");
        return ret;
    }
    /* change master in the last step to make sure the original list can be enumerated by search */
    masterVertex->nextSlaveId = slaveId;
    DiskAnnRedoForSetNextSlaveId(adpt, masterId, slaveId, redoCtx);
    /* delete slave from neighbor list of parent */
    RemoveNodeFromNbsWithRedo(adpt, masterHead, slaveId, redoCtx);
    return GMERR_OK;
}

Status DiskAnnAddSlaveNode(
    DiskAnnCtxT *adpt, const DiskAnnAddrT masterId, const DiskAnnAddrT slaveId, bool *addedAsSlave)
{
    if (IsVertexEqual(masterId, slaveId)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE,
            "|SE-DISKANN| |DiskAnnAddSlaveNode| master id should not be same as slave id");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    uint8_t *masterHead = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, masterId, true, (uint8_t **)&masterHead);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret,
            "|SE-DISKANN| |DiskAnnAddSlaveNode| Unexpected event has happened during DiskAnnGetVertexPoint procedure");
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    // 写入节点信息
    DiskAnnVertexT *masterVertex = GetVertexFromPage(masterHead);
    ret = IsMasterContainSlave(adpt, masterVertex, slaveId);
    if (ret == GMERR_DUPLICATE_OBJECT) {
        ret = GMERR_OK;
        goto EXIT;
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnAddSlaveNode| Unable to IsMasterContainSlave");
        goto EXIT;
    }
    /* insert slave into the list */
    DiskAnnVertexT newSlaveNode = {
        .vertexId = slaveId, .preSlaveId = masterId, .nextSlaveId = masterVertex->nextSlaveId, .masterId = masterId};
    ret = InsertSlaveToList(adpt, masterHead, &newSlaveNode, redoCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnAddSlaveNode| Unable to IsMasterContainSlave");
        goto EXIT;
    }
    DiskAnnLeaveVertexPoint(adpt, masterId, true);

    *addedAsSlave = true;
    return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
EXIT:
    DiskAnnLeaveVertexPoint(adpt, masterId, false);
    (void)RedoLogEnd(redoCtx, false);
    return ret;
}

Status GetDistBetweenVertexs(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId1, const DiskAnnAddrT vertexId2, double *res)
{
    DB_POINTER2(adpt, res);
    void *vecs1 = NULL;
    Status ret = VectorGet(adpt, vertexId1, &vecs1);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |GetDistBetweenVertexs| Unable to VectorGet from vertexId1");
        return ret;
    }
    void *vecs2 = NULL;
    ret = VectorGet(adpt, vertexId2, &vecs2);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |GetDistBetweenVertexs| Unable to VectorGet from vertexId2");
        SeAnnFreeItem(adpt->memCtx, (uint8_t **)&vecs1);
        return ret;
    }
    if (adpt->meta.quantMode == ANN_QUANT_LVQ) {
        *res = AnnQuantCalDistByLvq(adpt->qCtx, (uint8_t *)vecs1, (uint8_t *)vecs2);
        SeAnnFreeItem(adpt->memCtx, (uint8_t **)&vecs1);
        SeAnnFreeItem(adpt->memCtx, (uint8_t **)&vecs2);
        return GMERR_OK;
    }
    AnnVecT vecs1ForCalc = {.type = adpt->meta.dataType, .dim = adpt->meta.dim, .items = vecs1};
    AnnVecT vecs2ForCalc = {.type = adpt->meta.dataType, .dim = adpt->meta.dim, .items = vecs2};
    ret = DmAnnVectorGetDistance(adpt->meta.distType, &vecs1ForCalc, &vecs2ForCalc, res);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |GetDistBetweenVertexs| Unable to VectorGetDistance");
    }
    SeAnnFreeItem(adpt->memCtx, (uint8_t **)&vecs1);
    SeAnnFreeItem(adpt->memCtx, (uint8_t **)&vecs2);
    return ret;
}

Status VectorGet(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, void **vecs)
{
    DB_POINTER2(adpt, vecs);
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, vertexId, false, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "|SE-DISKANN| |VectorGet| Unexpected event has happened during DiskAnnGetVertexPoint procedure");
        return ret;
    }

    if (*vecs == NULL) {
        ret = SeAnnAllocAndInitItem(
            adpt->memCtx, GetRawVecSize(adpt->meta.numQuantBit, adpt->meta.dim), 0, (uint8_t **)vecs);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |VectorGet| Unexpected event has happened during "
                              "SeAnnAllocAndInitItem procedure");
            DiskAnnLeaveVertexPoint(adpt, vertexId, false);
            return ret;
        }
    }
    DeepCopy(*vecs, GetVecsFromPage(head), GetRawVecSize(adpt->meta.numQuantBit, adpt->meta.dim));

    DiskAnnLeaveVertexPoint(adpt, vertexId, false);
    return GMERR_OK;
}

Status VectorGetWithDecode(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, void **vecs)
{
    DB_POINTER2(adpt, vecs);
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, vertexId, false, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |VectorGetWithDecode| Unable to DiskAnnGetVertexPoint");
        return ret;
    }
    AnnQuantGenRawVec(adpt->qCtx, (LvqVectorT *)GetVecsFromPage(head), *vecs);
    DiskAnnLeaveVertexPoint(adpt, vertexId, false);
    return GMERR_OK;
}

static Status CalNodeVecDistance(
    DiskAnnCtxT *adpt, uint8_t *head, AnnVecT *queryVector, const double vecSqrSum, double *distance)
{
    DB_POINTER4(adpt, head, queryVector, distance);
    if (adpt->meta.quantMode == ANN_QUANT_LVQ) {
        *distance = AnnQuantCalDistByLvq(adpt->qCtx, (uint8_t *)GetVecsFromPage(head), (uint8_t *)(queryVector->items));
        return GMERR_OK;
    }
    if (adpt->meta.distType == DIST_TYPE_L2) {
        double vecs1SqrSum = DiskAnnGetSqrSum(head);
        AnnVecT vec1T = {.type = adpt->meta.dataType, .dim = adpt->meta.dim, .items = GetVecsFromPage(head)};
        double innerProduct = 0.0;
        Status ret = DmAnnVectorGetDistance(DIST_TYPE_DOT_PRODUCT, &vec1T, queryVector, &innerProduct);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |CalNodeVecDistance| Unable to DmAnnVectorGetDistance DIST_TYPE_L2");
            return ret;
        }
        double distSqr = vecs1SqrSum + vecSqrSum - CROSS_TERM_COEFFICIENT * innerProduct;
        distSqr = distSqr > 0 ? distSqr : 0;
        *distance = sqrt(distSqr);
    } else {
        AnnVecT vec1T = {.type = adpt->meta.dataType, .dim = adpt->meta.dim, .items = GetVecsFromPage(head)};
        double cosDist = 0.0;
        Status ret = DmAnnVectorGetDistance(DIST_TYPE_COSINE, &vec1T, queryVector, &cosDist);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |CalNodeVecDistance| Unable to DmAnnVectorGetDistance DIST_TYPE_COSINE");
            return ret;
        }
        *distance = cosDist;
    }
    return GMERR_OK;
}

Status GetAccuDistWithVertexInfoFromVec(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId1,
    DiskAnnSearchParamsT *searchParams, double *distance, DiskAnnVertexT *out)
{
    DB_POINTER4(adpt, searchParams, distance, out);
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, vertexId1, false, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |GetAccuDistWithVertexInfoFromVec| Unexpected event has happened "
                          "during DiskAnnGetPage procedure");
        return ret;
    }
    ret = CalNodeVecDistance(adpt, head, searchParams->queryVector, searchParams->querySqrSum, distance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |GetAccuDistWithVertexInfoFromVec| Unexpected event has happened "
                          "during CalNodeVecDistance procedure");
        goto INFO_GET_EXIT;
    }
    out->flag = *GetTagFromPage(head);
    out->dataID = DiskAnnGetDataId(head);
INFO_GET_EXIT:
    DiskAnnLeaveVertexPoint(adpt, vertexId1, false);
    return ret;
}

Status GetAccuDistFromVec(
    DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId1, AnnVecT *queryVector, const double vecSqrSum, double *distance)
{
    DB_POINTER3(adpt, queryVector, distance);
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, vertexId1, false, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "|SE-DISKANN| |GetAccuDistFromVec| Unexpected event has happened during DiskAnnGetPage procedure");
        return ret;
    }
    ret = CalNodeVecDistance(adpt, head, queryVector, vecSqrSum, distance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |GetAccuDistFromVec| Unexpected event has happened "
                          "during CalNodeVecDistance procedure");
    }
    DiskAnnLeaveVertexPoint(adpt, vertexId1, false);
    return ret;
}

Status GenPqVector(DiskAnnCtxT *adpt)
{
    return GMERR_OK;
}

bool IsVertexDeleted(DiskAnnVertexT *node)
{
    return (node->flag & DISKANN_DELETED_NODE) == DISKANN_DELETED_NODE;
}

Status DiskAnnIsDeleted(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, bool *isDeleted)
{
    DB_POINTER2(adpt, isDeleted);
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, vertexId, false, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE,
            "|SE-DISKANN| |DiskAnnIsDeleted| Unexpected event has happened during DiskAnnGetVertexPoint procedure");
        return ret;
    }
    *isDeleted = ((*GetTagFromPage(head) & DISKANN_DELETED_NODE) == DISKANN_DELETED_NODE);
    DiskAnnLeaveVertexPoint(adpt, vertexId, false);
    return GMERR_OK;
}

Status DiskAnnIsMaster(DiskAnnCtxT *adpt, const DiskAnnAddrT masterId, bool *isMaster)
{
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, masterId, false, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnGetVertexPoint| Unable to DiskAnnGetVertexPoint");
        return ret;
    }
    DiskAnnVertexT *vertex = GetVertexFromPage(head);
    *isMaster = (vertex->flag & DISKANN_SLAVE_NODE) == 0 ? true : false;
    DiskAnnLeaveVertexPoint(adpt, masterId, false);
    return ret;
}

Status DataIdGet(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, uint64_t *dataId)
{
    DB_POINTER2(adpt, dataId);
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, vertexId, false, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE,
            "|SE-DISKANN| |DataIdGet| Unexpected event has happened during DiskAnnGetVertexPoint procedure");
        return ret;
    }
    *dataId = DiskAnnGetDataId(head);
    DiskAnnLeaveVertexPoint(adpt, vertexId, false);
    return GMERR_OK;
}

Status SqrSumGet(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, double *sqrSum)
{
    DB_POINTER2(adpt, sqrSum);
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, vertexId, false, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE,
            "|SE-DISKANN| |SqrSumGet| Unexpected event has happened during DiskAnnGetVertexPoint procedure");
        return ret;
    }
    *sqrSum = DiskAnnGetSqrSum(head);
    DiskAnnLeaveVertexPoint(adpt, vertexId, false);
    return GMERR_OK;
}

static Status TryRenewMasterNodeWithRedo(DiskAnnCtxT *ctxt, DiskAnnVertexT *originMaster, RedoRunCtxT *redoCtx)
{
    // WIP. 为真删除场景预留
    return GMERR_OK;
}

Status MarkDeleted(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId)
{
    DB_POINTER(adpt);
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, vertexId, true, (uint8_t **)&head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "|SE-DISKANN| |MarkDeleted| Unexpected event has happened during DiskAnnGetVertexPoint procedure");
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    DiskAnnVertexT *vertex = GetVertexFromPage(head);
    if (vertex == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "|SE-DISKANN| |MarkDeleted| Unable to Get Vertex From Page");
        DiskAnnLeaveVertexPoint(adpt, vertexId, false);
        (void)RedoLogEnd(redoCtx, false);
        return GMERR_INTERNAL_ERROR;
    }
    ret = DiskAnnUndoForSetTag(adpt, vertexId, vertex->flag);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |MarkDeleted| Unable to DiskAnnUndoForSetTag");
        DiskAnnLeaveVertexPoint(adpt, vertexId, false);
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    vertex->flag |= DISKANN_DELETED_NODE;
    DiskAnnRedoForSetFlag(adpt, vertexId, vertex->flag, redoCtx);
    ret = TryRenewMasterNodeWithRedo(adpt, vertex, redoCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |MarkDeleted| Unable to TryRenewMasterNodeWithRedo");
        DiskAnnLeaveVertexPoint(adpt, vertexId, false);
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    DiskAnnLeaveVertexPoint(adpt, vertexId, true);
    return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
}

Status DiskAnnSetFrozens(DiskAnnCtxT *adpt, DiskAnnPageIdT metaPageId)
{
    DB_POINTER(adpt);
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    uint8_t *head = NULL;
    Status ret = DiskAnnGetPage(adpt, metaPageId, true, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnSetFrozens| Unable to DiskAnnGetPage");
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    DiskAnnAddrT *frozens = GetFrozenPointsFromMeta(GetDiskAnnMeta((uint8_t *)(head)));
    ret = DiskAnnUndoForSetFrozens(adpt, metaPageId, frozens, adpt->meta.numFrozens);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "|SE-DISKANN| |DiskAnnSetFrozens| Unable to DiskAnnUndoForSetFrozens %d", adpt->meta.numFrozens);
        DiskAnnLeavePage(adpt, metaPageId, false);
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    (void)memcpy_s(frozens, GetFrozenPointsSize(adpt->meta.numFrozens), adpt->meta.frozens,
        GetFrozenPointsSize(adpt->meta.numFrozens));
    DiskAnnRedoForSetFrozens(adpt, metaPageId, adpt->meta.frozens, adpt->meta.numFrozens, redoCtx);
    DiskAnnLeavePage(adpt, metaPageId, true);
    return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
}

Status DiskAnnRenewMeta(DiskAnnCtxT *adpt, void (*metaOperation)(DiskAnnCtxT *adpt, DiskAnnMetaT *meta))
{
    (void)adpt;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    uint8_t *head = NULL;
    Status ret = DiskAnnGetPage(adpt, adpt->meta.metaPageId, true, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "|SE-DISKANN| |AppendToVertexListWithRedo| Unexpected event has happened during DiskAnnGetPage");
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    ret = DiskAnnUndoForSetMeta(adpt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnAlgoVaccum| Unable to DiskAnnUndoForSetMeta");
        DiskAnnLeavePage(adpt, adpt->meta.metaPageId, false);
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    DiskAnnMetaT *meta = GetDiskAnnMeta(head);
    metaOperation(adpt, meta);
    DiskAnnRedoForSetMeta(adpt, redoCtx);
    DiskAnnLeavePage(adpt, adpt->meta.metaPageId, true);
    return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
}

static Status TryAllocVertex(DiskAnnCtxT *adpt, const DiskAnnNodeT *node, DiskAnnAddrT *vertexId)
{
    Status ret = GMERR_OK;
    if (IsVertexUnused(vertexId)) {
        ret = DiskAnnAllocVertex(adpt, vertexId);
    } else {
        *vertexId = node->vertexId;
    }
    return ret;
}

static void DiskAnnSetEdgeInner(DiskAnnCtxT *adpt, uint8_t *head, const DiskAnnEdgeT *input)
{
    DB_POINTER3(adpt, head, input);
    void *edge = GetEdgeFromPage(head, adpt->meta.vertexTypeSize);
    *(uint32_t *)edge = input->numNeighbor;
    DeepCopy(GetNbVertexIdsFromEdge(edge), input->nexts, GetNbVertexIdsArrSize(adpt->meta.outDegree));
    if (adpt->meta.reserveDist) {
        DeepCopy(GetNbDistsFromEdge(edge, adpt->meta.outDegree), input->distance,
            GetDistArrSize(adpt->meta.reserveDist, adpt->meta.outDegree));
    }
}

static void DiskAnnSetEdgeFromNode(DiskAnnCtxT *adpt, uint8_t *head, const DiskAnnNodeT *node)
{
    DB_POINTER3(adpt, head, node);
    void *edge = GetEdgeFromPage(head, adpt->meta.vertexTypeSize);
    *(uint32_t *)edge = node->numNeighbor;
    DeepCopy(GetNbVertexIdsFromEdge(edge), node->nexts, GetNbVertexIdsArrSize(adpt->meta.outDegree));
    if (adpt->meta.reserveDist) {
        DeepCopy(GetNbDistsFromEdge(edge, adpt->meta.outDegree), node->distance,
            GetDistArrSize(adpt->meta.reserveDist, adpt->meta.outDegree));
    }
}

Status DiskAnnNodeSet(DiskAnnCtxT *adpt, DiskAnnNodeT *node)
{
    DB_POINTER2(adpt, node);
    DiskAnnAddrT vertexId = SE_INVALID_VERTEX_ID;
    Status ret = TryAllocVertex(adpt, node, &vertexId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnNodeSet| TryAllocVertex unsucc");
        return ret;
    }
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    uint8_t *head = NULL;
    ret = DiskAnnGetVertexPoint(adpt, vertexId, true, (uint8_t **)&head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnNodeSet| DiskAnnGetVertexPoint unsucc");
        (void)RedoLogEnd(redoCtx, false);
        (void)DiskAnnFreeVertexPoint(adpt, vertexId);
        return ret;
    }
    ret = DiskAnnUndoForSetNode(adpt, vertexId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnNodeSet| Unable to DiskAnnUndoForSetNode");
        DiskAnnLeaveVertexPoint(adpt, vertexId, false);
        (void)RedoLogEnd(redoCtx, false);
        (void)DiskAnnFreeVertexPoint(adpt, vertexId);
        return ret;
    }
    // 写入节点信息
    DiskAnnVertexT *vertex = GetVertexFromPage(head);
    vertex->vertexId = vertexId;
    if (IsVertexUnused(&node->masterId)) {
        vertex->masterId = vertexId;
        node->masterId = vertexId;
    } else {
        vertex->masterId = node->masterId;
    }
    vertex->preSlaveId = node->preSlaveId;
    vertex->nextSlaveId = node->nextSlaveId;
    vertex->flag = node->flag;
    vertex->dataID = node->dataID;
    vertex->sqrSum = node->sqrSum;
    vertex->vecs = GetVecsFromPage(head);
    DeepCopy(vertex->vecs, node->vecs, GetRawVecSize(adpt->meta.numQuantBit, adpt->meta.dim));
    // 写入边信息
    DiskAnnSetEdgeFromNode(adpt, head, node);
    // 写入Page后，回填vertexId到DiskAnnNode结构体
    node->vertexId = vertexId;
    DiskAnnRedoForSetNode(adpt, node, redoCtx);
    DiskAnnLeaveVertexPoint(adpt, vertexId, true);
    return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
}

// LCOV_EXCL_BR_START
static Status GetEdgeToNode(DiskAnnCtxT *adpt, DiskAnnVertexT *vertexHead, DiskAnnNodeT *node)
{
    void *edge = GetEdgeFromVertex(vertexHead, adpt->meta.vertexTypeSize);
    node->numNeighbor = GetNumNbFromEdge(edge);
    uint16_t outDegree = adpt->meta.outDegree;
    Status ret = SeAnnAllocAndCopy(
        adpt->memCtx, GetNbVertexIdsFromEdge(edge), GetNbVertexIdsArrSize(outDegree), (uint8_t **)&(node->nexts));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |GetEdgeToNode| Unable to SeAnnAllocAndCopy nbs' vertexIds");
        return ret;
    }
    if (!adpt->meta.reserveDist) {
        return GMERR_OK;
    }
    ret = SeAnnAllocAndCopy(adpt->memCtx, GetNbDistsFromEdge(edge, outDegree),
        GetDistArrSize(adpt->meta.reserveDist, outDegree), (uint8_t **)&(node->distance));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |GetEdgeToNode| Unable to SeAnnAllocAndCopy nbs' vertexIds");
        SeAnnFreeItem(adpt->memCtx, (uint8_t **)&(node->nexts));
    }
    return ret;
}
// LCOV_EXCL_BR_STOP

Status DiskAnnNodeReset(DiskAnnCtxT *adpt, DiskAnnAddrT vertexId)
{
    DB_POINTER(adpt);
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, vertexId, true, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnNodeGet| Unable to DiskAnnGetVertexPoint");
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    DiskAnnVertexT *vertex = GetVertexFromPage(head);
    vertex->masterId = vertex->vertexId;
    vertex->preSlaveId = SE_INVALID_VERTEX_ID;
    vertex->nextSlaveId = SE_INVALID_VERTEX_ID;
    vertex->flag = 0;
    void *edge = GetEdgeFromVertex(vertex, adpt->meta.vertexTypeSize);
    size_t edgeSize = CalEdgeTypeSize(adpt->meta.reserveDist, adpt->meta.outDegree);
    errno_t err = memset_s(edge, edgeSize, 0, edgeSize);
    if (err != EOK) {
        DB_LOG_ERROR(
            GMERR_MEMORY_OPERATE_FAILED, "|SE-DISKANN| |DiskAnnNodeReset| memset go wrong, edgeSize = %lu", edgeSize);
        DiskAnnLeaveVertexPoint(adpt, vertexId, false);
        (void)RedoLogEnd(redoCtx, false);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    DiskAnnRedoForResetNode(adpt, vertexId, adpt->meta.vertexTypeSize, edgeSize, redoCtx);
    DiskAnnLeaveVertexPoint(adpt, vertexId, true);
    return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
}

// LCOV_EXCL_BR_START
Status DiskAnnNodeGet(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, DiskAnnNodeT *node)
{
    DB_POINTER2(adpt, node);
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, vertexId, false, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnNodeGet| Unable to DiskAnnGetVertexPoint");
        return ret;
    }
    DiskAnnVertexT *vertex = GetVertexFromPage(head);
    node->vertexId = vertex->vertexId;
    node->masterId = vertex->masterId;
    node->preSlaveId = vertex->preSlaveId;
    node->nextSlaveId = vertex->nextSlaveId;
    node->flag = vertex->flag;
    node->dataID = vertex->dataID;
    node->sqrSum = vertex->sqrSum;
    ret = SeAnnAllocAndCopy(adpt->memCtx, (void *)(vertex->vecs), GetRawVecSize(adpt->meta.numQuantBit, adpt->meta.dim),
        (uint8_t **)&(node->vecs));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnNodeGet| Unable to SeAnnAllocAndCopy node vecs");
        DiskAnnLeaveVertexPoint(adpt, vertexId, false);
        return ret;
    }
    ret = GetEdgeToNode(adpt, vertex, node);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnNodeGet| Unable to GetEdgeToNode");
        DiskAnnNodeFree(adpt, &node);
        DiskAnnLeaveVertexPoint(adpt, vertexId, false);
        return ret;
    }
    DiskAnnLeaveVertexPoint(adpt, vertexId, false);
    return GMERR_OK;
}
// LCOV_EXCL_BR_STOP

Status DiskAnnVertexGet(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, bool requireVec, DiskAnnVertexT **out)
{
    DB_POINTER2(adpt, out);
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, vertexId, false, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret,
            "|SE-DISKANN| |DiskAnnVertexGet| Unexpected event has happened during DiskAnnGetVertexPoint procedure");
        return ret;
    }
    uint32_t dim = requireVec ? adpt->meta.dim : 0u;
    ret = SeAnnAllocAndCopy(adpt->memCtx, (const uint8_t *)GetVertexFromPage(head),
        CalVertexTypeSize(adpt->meta.numQuantBit, dim), (uint8_t **)out);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret,
            "|SE-DISKANN| |DiskAnnVertexGet| Unexpected event has happened during DiskAnnGetVertexPoint procedure");
    }
    DiskAnnLeaveVertexPoint(adpt, vertexId, false);
    return ret;
}

Status DiskAnnEdgeGet(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, DiskAnnEdgeT **outEdge)
{
    DB_POINTER2(adpt, outEdge);
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, vertexId, false, &head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "|SE-DISKANN| |DiskAnnEdgeGet| Unexpected event has happened during DiskAnnGetVertexPoint procedure");
        return ret;
    }
    DiskAnnEdgeT *out = NULL;
    ret = SeAnnAllocAndInitItem(adpt->memCtx, sizeof(DiskAnnEdgeT), 0, (uint8_t **)&out);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|SE-DISKANN| |DiskAnnEdgeGet| Unable to Allocate mem for edge");
        goto DISKANN_EDGE_GET_EXIT;
    }
    void *edge = GetEdgeFromPage(head, adpt->meta.vertexTypeSize);
    out->numNeighbor = GetNumNbFromEdge(edge);
    uint16_t outDegree = adpt->meta.outDegree;
    ret = SeAnnAllocAndCopy(
        adpt->memCtx, GetNbVertexIdsFromEdge(edge), outDegree * sizeof(DiskAnnAddrT), (uint8_t **)&(out->nexts));
    if (ret != GMERR_OK) {
        SeAnnFreeItem(adpt->memCtx, (uint8_t **)&(out));
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnEdgeGet| Unable to SeAnnAllocAndCopy nbs' vertexIds");
        goto DISKANN_EDGE_GET_EXIT;
    }
    if (adpt->meta.reserveDist) {
        ret = SeAnnAllocAndCopy(
            adpt->memCtx, GetNbDistsFromEdge(edge, outDegree), outDegree * sizeof(float), (uint8_t **)&(out->distance));
        if (ret != GMERR_OK) {
            SeAnnFreeItem(adpt->memCtx, (uint8_t **)&(out->nexts));
            SeAnnFreeItem(adpt->memCtx, (uint8_t **)&(out));
            DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnEdgeGet| Unable to SeAnnAllocAndCopy nbs' vertexIds");
            goto DISKANN_EDGE_GET_EXIT;
        }
    }

    *outEdge = out;
DISKANN_EDGE_GET_EXIT:
    DiskAnnLeaveVertexPoint(adpt, vertexId, false);
    return ret;
}

void DiskAnnEdgeFree(DiskAnnCtxT *adpt, DiskAnnEdgeT **outEdge)
{
    DB_POINTER(adpt);
    if (outEdge == NULL || *outEdge == NULL) {
        return;
    }
    SeAnnFreeItem(adpt->memCtx, (uint8_t **)&((*outEdge)->nexts));
    SeAnnFreeItem(adpt->memCtx, (uint8_t **)&((*outEdge)->distance));
    SeAnnFreeItem(adpt->memCtx, (uint8_t **)outEdge);
}

// LCOV_EXCL_BR_START
void DiskAnnNodeFree(DiskAnnCtxT *adpt, DiskAnnNodeT **node)
{
    DB_POINTER(adpt);
    if (node == NULL || *node == NULL) {
        return;
    }
    SeAnnFreeItem(adpt->memCtx, (uint8_t **)&((*node)->vecs));
    SeAnnFreeItem(adpt->memCtx, (uint8_t **)&((*node)->nexts));
    SeAnnFreeItem(adpt->memCtx, (uint8_t **)&((*node)->distance));
    SeAnnFreeItem(adpt->memCtx, (uint8_t **)&((*node)->neighborVecs));
    SeAnnFreeItem(adpt->memCtx, (uint8_t **)node);
}
// LCOV_EXCL_BR_STOP

void DiskAnnVertexFree(DiskAnnCtxT *adpt, DiskAnnVertexT **vertex)
{
    DB_POINTER(adpt);
    if (vertex == NULL || *vertex == NULL) {
        return;
    }
    SeAnnFreeItem(adpt->memCtx, (uint8_t **)vertex);
}

Status DiskAnnEdgeSet(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, const DiskAnnEdgeT *edge)
{
    DB_POINTER2(adpt, edge);
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    uint8_t *head = NULL;
    Status ret = DiskAnnGetVertexPoint(adpt, vertexId, true, (uint8_t **)&head);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "|SE-DISKANN| |DiskAnnEdgeSet| Unexpected event has happened during DiskAnnGetVertexPoint procedure");
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    DiskAnnRedoUndoParamT redoParam = GetRedoUndoParam(adpt, vertexId);
    uint16_t pqVecDim = adpt->meta.enablePQ ? adpt->meta.dim : 0;
    void *tmpEdge = GetEdgeFromPage(head, adpt->meta.vertexTypeSize);
    DiskAnnEdgeT originEdge = {.numNeighbor = GetNumNbFromEdge(tmpEdge),
        .nexts = (DiskAnnAddrT *)(void *)GetNbVertexIdsFromEdge(tmpEdge),
        .distance = (float *)GetNbDistsFromEdge(tmpEdge, adpt->meta.outDegree)};
    ret = DiskAnnUndoForSetEdge(adpt, &redoParam, &originEdge, pqVecDim);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnEdgeSet| Unable to DiskAnnUndoForSetEdge");
        DiskAnnLeaveVertexPoint(adpt, vertexId, false);
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }

    DiskAnnSetEdgeInner(adpt, head, edge);
    DiskAnnRedoForSetEdge(adpt, &redoParam, edge, redoCtx);
    DiskAnnLeaveVertexPoint(adpt, vertexId, true);
    return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
}

Status DiskAnnEdgeInit(DiskAnnCtxT *adpt, DiskAnnEdgeT **outEdge)
{
    DB_POINTER2(adpt, outEdge);
    DiskAnnEdgeT *edge = NULL;
    Status ret = SeAnnAllocAndInitItem(adpt->memCtx, sizeof(DiskAnnEdgeT), 0, (uint8_t **)&edge);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnEdgeInit| Unable to alloc memory for edge");
        return ret;
    }
    ret =
        SeAnnAllocAndInitItem(adpt->memCtx, GetNbVertexIdsArrSize(adpt->meta.outDegree), 0, (uint8_t **)&(edge->nexts));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnEdgeInit| Unable to SeAnnAllocAndInitItem for edge nexts");
        goto DISKANN_EDGE_INIT_EXIT;
    }
    if (adpt->meta.reserveDist) {
        ret = SeAnnAllocAndInitItem(adpt->memCtx, GetDistArrSize(adpt->meta.reserveDist, adpt->meta.outDegree), 0,
            (uint8_t **)&(edge->distance));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnEdgeInit| Unable to SeAnnAllocAndInitItem for edge distance");
            goto DISKANN_EDGE_INIT_EXIT;
        }
    }
    *outEdge = edge;
    return GMERR_OK;
DISKANN_EDGE_INIT_EXIT:
    DiskAnnEdgeFree(adpt, &edge);
    return ret;
}

// LCOV_EXCL_BR_START
Status IsNeighbor(const DiskAnnAddrT vertexId, const uint32_t potentialNeighborId, bool *isNeighbor)
{
    return GMERR_OK;
}

Status GetNeighborInfo(const DiskAnnAddrT vertexId, DiskannNeighborInfoT *neighborInfo)
{
    return GMERR_OK;
}

Status DiskAnnInitDelNodeListByPageId(DiskAnnCtxT *adpt, DiskAnnPageIdT *pageId, RedoRunCtxT *redoCtx)
{
    DB_POINTER3(adpt, pageId, redoCtx);
    (void)adpt;
    (void)pageId;
    (void)redoCtx;
    return GMERR_OK;
}

Status DiskAnnDelNodeListAdd(DiskAnnCtxT *adpt, DiskAnnAddrT vertexId)
{
    DB_POINTER(adpt);
    // Add操作对象永远是Activated页
    DiskAnnAddrT delAddr = {0};
    Status ret = DiskAnnNodeMgrAlloc(adpt, &(adpt->meta.delNodeListPageIdActivated), &delAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnDelNodeListAdd| Unable to DiskAnnNodeMgrAlloc");
        return ret;
    }
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    uint8_t *headActivated = NULL;
    ret = DiskAnnNodeMgrGet(adpt, &(adpt->meta.delNodeListPageIdActivated), delAddr, true, &headActivated);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnDelNodeListAdd| Unable to DiskAnnNodeMgrAlloc");
        (void)DiskAnnNodeMgrFree(adpt, &(adpt->meta.delNodeListPageIdActivated), delAddr);
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    *(DiskAnnAddrT *)(headActivated) = vertexId;
    DiskAnnRedoForAddToNodeList(adpt, delAddr, vertexId, adpt->meta.delNodeListPageIdActivated.nodeSize, redoCtx);
    DiskAnnNodeMgrLeave(adpt, delAddr, true);
    return DbGetExternalErrno(RedoLogEnd(redoCtx, true));
}

static void SetLockedDelList(DiskAnnCtxT *adpt, DiskAnnMetaT *meta)
{
    DB_POINTER2(adpt, meta);
    meta->delNodeListPageIdLocked = adpt->meta.delNodeListPageIdLocked;
}

static Status ReInitDelNodeList(DiskAnnCtxT *adpt)
{
    uint16_t nodeSize = adpt->meta.delNodeListPageIdLocked.nodeSize;
    uint16_t maxSlotNum = adpt->meta.delNodeListPageIdLocked.maxSlotNums;
    Status ret = FreeDataRecursively(adpt, adpt->meta.delNodeListPageIdLocked.dataListHead);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "|SE-DISKANN| |ReInitDelNodeList| Unable to FreeDataRecursively");
        return ret;
    }
    DiskAnnNodeMgrInit(&adpt->meta.delNodeListPageIdLocked, nodeSize, maxSlotNum, DISKANN_LIST);
    return DiskAnnRenewMeta(adpt, SetLockedDelList);
}

static void SetVaccumDoneFlag(DiskAnnCtxT *adpt, DiskAnnMetaT *meta)
{
    DB_POINTER2(adpt, meta);
    meta->isVaccumDone = adpt->meta.isVaccumDone;
}

Status DiskAnnDelNodeListDelte(DiskAnnCtxT *adpt, int32_t *fakeDelNodeChg)
{
    DB_POINTER2(adpt, fakeDelNodeChg);
    Status ret = GMERR_OK;
    uint32_t totalDelNum = 0;
    DiskAnnPageIdT pageToFree = adpt->meta.delNodeListPageIdLocked.dataListHead;  // Delete操作对象永远是Locked页
    while (!IsPageUnused(&pageToFree)) {
        uint8_t *headLocked = NULL;
        ret = DiskAnnGetPage(adpt, pageToFree, false, &headLocked);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnDelNodeListDelte| Unable to DiskAnnGetPage");
            return ret;
        }
        DiskAnnNodeHdrT *nodeList = GetDiskAnnNodeHdrFromPageHead(headLocked);
        DiskAnnPageIdT nextPage = nodeList->nextPageId;
        uint32_t delNum = 0;
        for (uint32_t i = 0; i < nodeList->maxSlotNums; ++i) {
            if (nodeList->isSlotFree[i]) {
                continue;
            }
            DiskAnnAddrT vertexToFree =
                *(DiskAnnAddrT *)(GetNodeHead(headLocked, i + 1, adpt->meta.delNodeListPageIdLocked.nodeSize));
            ret = DiskAnnFreeVertexPoint(adpt, vertexToFree);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "|SE-DISKANN| |DiskAnnDelNodeListDelte| Unable to DiskAnnFreeVertexPoint %d",
                    vertexToFree.slotId);
                DiskAnnLeavePage(adpt, pageToFree, false);
                return ret;
            }
            delNum++;
        }
        totalDelNum += delNum;
        DiskAnnLeavePage(adpt, pageToFree, false);
        pageToFree = nextPage;
    }
    adpt->meta.isVaccumDone = false;
    DiskAnnRenewMeta(adpt, SetVaccumDoneFlag);
    // fakeDelNodeChg代表变化量，此处删去了nodeList->size个节点，所以变化量是负的
    *fakeDelNodeChg = (int32_t)(totalDelNum) * (-1);
    return ReInitDelNodeList(adpt);
}
// LCOV_EXCL_BR_STOP
