/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author:
 * Create: 2024-04-15
 */
#include "se_daf_inner.h"
#include "se_trx_inner.h"
#include "se_heap_inner.h"
#include "db_memcpy.h"
#include "se_trx_mgr.h"
#include "dm_data_prop.h"

DafMgrT g_dafMgr = {0};

bool IsDafMgrInitedImpl(void)
{
    return g_dafMgr.isInit;
}

StatusInter DafCreateActionCollectionImpl(TrxT *trx)
{
    if (!g_dafMgr.isInit) {
        DB_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "daf feature not supported.");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    trx->trx.base.dafCommitActionCollection = DbDynMemCtxAlloc(g_dafMgr.memCtx, sizeof(DafCommitActionCollectionT));
    if (trx->trx.base.dafCommitActionCollection == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "Unable to alloc DAF commit action collection");
        return OUT_OF_MEMORY_INTER;
    }
    DbLinkedListInit(&trx->trx.base.dafCommitActionCollection->commitActions);
    return STATUS_OK_INTER;
}

Status DafExecuteIndexCommitActionInner(TrxT *trx, IndexCtxT *idxCtx, IdxDeleteActionsT *action)
{
    Status ret = GMERR_OK;
    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    if (action->batchNum == 1u) {
        ret = IdxDelete(idxCtx, action->idxKey[0], action->addr[0], removePara);
    } else {
        HpBatchOutT *batchOut =
            (HpBatchOutT *)DbDynMemCtxAlloc(trx->trxPeriodMemCtx, action->batchNum * sizeof(HpBatchOutT));
        if (batchOut == NULL) {
            DB_SET_LASTERR(GMERR_OUT_OF_MEMORY, "out of memory when batch erase index.");
            return GMERR_OUT_OF_MEMORY;
        }
        for (uint32_t j = 0; j < action->batchNum; j++) {
            batchOut[j].addrOut = action->addr[j];
        }
        ret = IdxBatchDelete(idxCtx, action->idxKey, batchOut, action->batchNum, removePara);
        DbDynMemCtxFree(trx->trxPeriodMemCtx, batchOut);
    }
    return ret;
}

void DafExecuteIndexCommitActionImpl(TrxT *trx, IdxDeleteActionsT *action)
{
    IndexCtxT *idxCtx = NULL;
    HpRunHdlT heapRunHdl = TrxGetHeapHandle(trx, action->labelId);
    if (heapRunHdl == NULL) {
        // 没拿到就试着拿个新的
        return;
    }
    SeContainerHdl seContainer = (SeContainerHdl){.hpRunHdl = heapRunHdl};
    StatusInter innerRet = TrxGetHashHandle(
        trx, seContainer, false, &action->idxIter, &idxCtx);  // 无法确定索引上下文是否已被回收，因此统一需要alloc
    if (innerRet != STATUS_OK_INTER) {
        DB_SET_LASTERR(DbGetExternalErrno(innerRet), "get hash handle unable, daf erase index entry unable.");
        return;
    }
    Status ret = DafExecuteIndexCommitActionInner(trx, idxCtx, action);
    if (ret != GMERR_OK) {
        DB_SET_LASTERR(ret, "daf unable to erase index entry.");  // 在Undo期间不应报错而是执行完
    }
}

StatusInter DafCreateAndStoreHpRunCtx(DafPurgerRunCtxT *purgerCtx, uint32_t resId, HpRunHdlT *heapRunHdl,
    DmLabelTypeE labelType, uint64_t curUndoRecTrxId)
{
    SeTrxContainerCtxT *container = NULL;
    TrxT *trx = purgerCtx->seCtx->trx;
    HeapRunCtxT *heapRunCtx = NULL;
    Status ret = purgerCtx->cfg.getHeapRunCtxFunc(
        resId, (DbMemCtxT *)trx->trxPeriodMemCtx, purgerCtx->seCtx, labelType, &heapRunCtx);
    if (ret != GMERR_OK) {
        DB_LOG_WARN(ret, "purger unable to get heap run ctx");
        return DbGetStatusInterErrno(ret);
    }
    bool isNewCtx = false;
    StatusInter retInter = TrxStoreContainerCtx(trx, resId, &container, &isNewCtx);
    DB_UNUSED(isNewCtx);
    if (retInter != STATUS_OK_INTER) {
        SE_ERROR(retInter, "trxSlot(%" PRIu16 ") unable to store label(%" PRIu32 ")", trx->base.trxSlot, resId);
        return retInter;
    }
    container->ctxHead.type = TRX_HEAP_HANDLE;
    // heapRunCtx由purger后续继续释放
    heapRunCtx->heapTrxCtx->ctxHead.isOpened = true;
    heapRunCtx->heapTrxCtx->heapRunHdl = heapRunCtx;
    container->heapTrxCtx = *heapRunCtx->heapTrxCtx;
    // 3. 保存稍后需要释放的资源
    if (labelType == VERTEX_LABEL) {
        ret = DbAppendListItem(&purgerCtx->vertexLabelList, &heapRunCtx->dmDetail.dmInfo);
    } else {
        ret = DbAppendListItem(&purgerCtx->kvLabelList, &heapRunCtx->dmDetail.dmInfo);
    }
    if (ret != GMERR_OK) {
        (void)purgerCtx->cfg.releaseLabelAndLatchFunc(labelType, heapRunCtx->dmDetail.dmInfo);
        DB_LOG_WARN(ret, "Unable to save label pointer.");
        return DbGetStatusInterErrno(ret);
    }
    // 4. 检查undoRec的trxId，与heap上的lastTruncateTrxId比较，检查是否经历过了truncate，经历过的话，旧版本就不用回收了
    if (HeapGetLastTruncateTrxId(heapRunCtx) > curUndoRecTrxId) {
        return NO_DATA_UNDO_REC_HANDLE;
    }
    TrxSetContainerIsUsed(container);
    *heapRunHdl = heapRunCtx;
    return DbGetStatusInterErrno(ret);
}

static void DafRemoveIndexCommitAction(IdxDeleteActionsT *action)
{
    if (!g_dafMgr.isInit) {
        DB_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "daf feature not supported.");
        return;
    }
    DbDynMemCtxFree(g_dafMgr.memCtx, action);
}

static Status DafAllocAction(DafMgrT *dafMgr, uint32_t idxKeyLen, uint32_t batchNum, IdxDeleteActionsT **action)
{
    uint32_t idxKeyOffset = (uint32_t)sizeof(IdxDeleteActionsT);
    uint32_t idxKeyDataOffset = idxKeyOffset + batchNum * (uint32_t)sizeof(IndexKeyT);
    uint32_t actionAddrOffset = idxKeyDataOffset + idxKeyLen;
    uint32_t actionTotalSize = actionAddrOffset + batchNum * (uint32_t)sizeof(HpTupleAddr);

    uint8_t *buffer = (uint8_t *)DbDynMemCtxAlloc(dafMgr->memCtx, actionTotalSize);
    if (buffer == NULL) {
        DB_SET_LASTERR(GMERR_OUT_OF_MEMORY, "unable to alloc memory for daf action.");
        return GMERR_OUT_OF_MEMORY;
    }
    IdxDeleteActionsT *delAction = (IdxDeleteActionsT *)(void *)buffer;
    delAction->batchNum = batchNum;
    delAction->idxKey = (IndexKeyT *)(void *)(buffer + idxKeyOffset);
    delAction->idxKey[0].keyData = buffer + idxKeyDataOffset;
    delAction->addr = (HpTupleAddr *)(void *)(buffer + actionAddrOffset);

    *action = delAction;
    return GMERR_OK;
}

Status DafDelayDeleteImpl(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr)
{
    SeRunCtxT *seRunCtxPtr = (SeRunCtxT *)idxCtx->idxOpenCfg.seRunCtx;
    TrxT *trx = (TrxT *)seRunCtxPtr->trx;
    if (!g_dafMgr.isInit) {
        DB_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "daf feature not supported.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    IdxDeleteActionsT *action = NULL;
    Status ret = DafAllocAction(&g_dafMgr, idxKey.keyLen, 1u, &action);
    if (ret != GMERR_OK) {
        // log inner
        return ret;
    }

    HeapDeSrlObjHdlT objHdl = {0};
    objHdl.handle = idxCtx->idxOpenCfg.indexLabel;
    HeapAmGetDmKeyIter(idxCtx->idxOpenCfg.heapHandle, objHdl, &action->idxIter, &action->labelId);
    action->labelType = idxCtx->idxOpenCfg.heapHandle->dmDetail.labelType;
    (void)memcpy_s(action->idxKey[0].keyData, idxKey.keyLen, idxKey.keyData, idxKey.keyLen);
    action->idxKey[0].keyLen = idxKey.keyLen;
    action->addr[0] = addr;
    DbLinkedListAppend(&trx->trx.base.dafCommitActionCollection->commitActions, &action->node);
    return GMERR_OK;
}

Status DafDelayBatchDeleteImpl(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum)
{
    SeRunCtxT *seRunCtxPtr = (SeRunCtxT *)idxCtx->idxOpenCfg.seRunCtx;
    TrxT *trx = (TrxT *)seRunCtxPtr->trx;
    if (!g_dafMgr.isInit) {
        DB_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "daf feature not supported.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    uint32_t keyLen = 0u;
    for (uint32_t j = 0; j < batchNum; j++) {
        keyLen += idxKey[j].keyLen;
    }

    IdxDeleteActionsT *action = NULL;
    Status ret = DafAllocAction(&g_dafMgr, keyLen, batchNum, &action);
    if (ret != GMERR_OK) {
        // log inner
        return ret;
    }

    HeapDeSrlObjHdlT objHdl = {0};
    objHdl.handle = idxCtx->idxOpenCfg.indexLabel;
    HeapAmGetDmKeyIter(idxCtx->idxOpenCfg.heapHandle, objHdl, &action->idxIter, &action->labelId);
    action->labelType = idxCtx->idxOpenCfg.heapHandle->dmDetail.labelType;

    uint32_t offset = 0u;
    for (uint32_t j = 0; j < batchNum; j++) {
        action->idxKey[j].keyData = action->idxKey[0].keyData + offset;
        // 此处因为性能原因使用GMDB项目自己实现的DbFastMemcpy而非memcpy_s，需要保证内存复制合法。
        DbFastMemcpy(action->idxKey[j].keyData, idxKey[j].keyLen, idxKey[j].keyData, idxKey[j].keyLen);
        action->idxKey[j].keyLen = idxKey[j].keyLen;
        action->addr[j] = addr[j].addrOut;
        offset += idxKey[j].keyLen;
    }
    DbLinkedListAppend(&trx->trx.base.dafCommitActionCollection->commitActions, &action->node);

    return GMERR_OK;
}

void DafCommitActionCollectionImpl(TrxT *trx)
{
    DB_POINTER2(trx, trx->trx.base.dafCommitActionCollection);
    if (!g_dafMgr.isInit) {
        DB_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "daf feature not supported.");
        return;
    }
    IdxDeleteActionsT *item = NULL;
    IdxDeleteActionsT *itemTmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(item, itemTmp, &trx->trx.base.dafCommitActionCollection->commitActions, node)
    {
        DafExecuteIndexCommitActionImpl(trx, item);
        DbDynMemCtxFree(g_dafMgr.memCtx, item);
    }
    DbDynMemCtxFree(g_dafMgr.memCtx, trx->trx.base.dafCommitActionCollection);
    trx->trx.base.dafCommitActionCollection = NULL;
}

void DafStoreCommitActionCollectionImpl(TrxT *trx)
{
    DB_POINTER2(trx, trx->trx.base.dafCommitActionCollection);
    if (!IsDafMgrInited() || DbLinkedListEmpty(&trx->trx.base.dafCommitActionCollection->commitActions)) {
        return;
    }
    DbSpinLock(&g_dafMgr.lock);
    TagLinkedListT *dafActionList = &g_dafMgr.dafActionList;
    IdxDeleteActionsT *item = NULL;
    IdxDeleteActionsT *itemTmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(item, itemTmp, &trx->trx.base.dafCommitActionCollection->commitActions, node)
    {
        item->commitTs = trx->base.commitTs;
        DbLinkedListAppend(dafActionList, &item->node);
    }
    DbSpinUnlock(&g_dafMgr.lock);
    DbDynMemCtxFree(g_dafMgr.memCtx, trx->trx.base.dafCommitActionCollection);
    trx->trx.base.dafCommitActionCollection = NULL;
}
void DafRollbackImpl(TrxT *trx)
{
    if (trx->trx.base.dafCommitActionCollection == NULL) {
        return;
    }
    if (!g_dafMgr.isInit) {
        DB_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "daf feature not supported.");
        return;
    }
    IdxDeleteActionsT *item = NULL;
    IdxDeleteActionsT *itemTmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(item, itemTmp, &trx->trx.base.dafCommitActionCollection->commitActions, node)
    {
        DbLinkedListRemove(&item->node);
        DafRemoveIndexCommitAction(item);
    }
    DbDynMemCtxFree(g_dafMgr.memCtx, trx->trx.base.dafCommitActionCollection);
    trx->trx.base.dafCommitActionCollection = NULL;
}

void DafExecuteIndexCommitActionOnPurgerImpl(DafPurgerRunCtxT *purgerCtx, IdxDeleteActionsT *action)
{
    Status ret;
    IndexCtxT *idxCtx = NULL;
    TrxT *trx = purgerCtx->seCtx->trx;
    HpRunHdlT heapRunHdl = TrxGetHeapHandle(trx, action->labelId);
    if (heapRunHdl == NULL) {
        // purger里首次操作一个label时是拿不到的，需要拿一个缓存起来
        StatusInter retInner =
            DafCreateAndStoreHpRunCtx(purgerCtx, action->labelId, &heapRunHdl, action->labelType, trx->base.trxId);
        if (retInner == GMERR_UNDEFINED_TABLE_INTER || retInner == NO_DATA_UNDO_REC_HANDLE) {
            // 表不存在，略过即可
            return;
        }
        if (retInner != STATUS_OK_INTER || heapRunHdl == NULL) {
            ret = DbGetExternalErrno(retInner);
            DB_LOG_ERROR(ret, "daf purger unable to get heap handle.");
            if (ret != GMERR_MEMORY_OPERATE_FAILED && ret != GMERR_OUT_OF_MEMORY &&
                ret != GMERR_INSUFFICIENT_RESOURCES) {
                DB_LOG_DBG_INFO("unable to clean daf action, labelId: %" PRIu32, action->labelId);
            }
            return;
        }
    }
    SeContainerHdl seContainer = (SeContainerHdl){.hpRunHdl = heapRunHdl};
    StatusInter innerRet = TrxGetHashHandle(
        trx, seContainer, false, &action->idxIter, &idxCtx);  // 无法确定索引上下文是否已被回收，因此统一需要alloc
    if (innerRet != STATUS_OK_INTER) {
        DB_SET_LASTERR(DbGetExternalErrno(innerRet), "get hash handle unable, daf erase index entry unable.");
        return;
    }
    ret = DafExecuteIndexCommitActionInner(trx, idxCtx, action);
    if (ret != GMERR_OK) {
        DB_SET_LASTERR(ret, "daf unable to erase index entry.");  // 在Undo期间不应报错而是执行完
    }
}

void DafCutInvisibleDafActions(TagLinkedListT *cleanActions, TrxIdT minTrxId)
{
    DB_POINTER(cleanActions);
    TagLinkedListT *dafActionList = &g_dafMgr.dafActionList;
    DbSpinLock(&g_dafMgr.lock);
    if (DbLinkedListEmpty(dafActionList)) {
        DbSpinUnlock(&g_dafMgr.lock);
        return;
    }
    IdxDeleteActionsT *item = NULL;
    IdxDeleteActionsT *itemTmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(item, itemTmp, dafActionList, node)
    {
        if (item->commitTs > minTrxId) {
            break;
        }
        DbLinkedListRemove(&item->node);
        DbLinkedListAppend(cleanActions, &item->node);
    }
    DbSpinUnlock(&g_dafMgr.lock);
}

void DafRegressUnCommitDafActions(TagLinkedListT *cleanActions)
{
    DB_POINTER(cleanActions);
    if (!g_dafMgr.isInit) {
        return;
    }
    TagLinkedListT *dafActionList = &g_dafMgr.dafActionList;
    DbSpinLock(&g_dafMgr.lock);
    IdxDeleteActionsT *item = NULL;
    IdxDeleteActionsT *itemTmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(item, itemTmp, cleanActions, node)
    {
        DbLinkedListRemove(&item->node);
        DbLinkedListPrepend(dafActionList, &item->node);
    }
    DbSpinUnlock(&g_dafMgr.lock);
}
static void PurgerClearVertexList(DafPurgerRunCtxT *purgerCtx)
{
    uint32_t labelNum = DbListGetItemCnt(&purgerCtx->vertexList);
    for (uint32_t i = 0; i < labelNum; ++i) {
        void **itemPtr = DbListItem(&purgerCtx->vertexList, i);
        if (itemPtr == NULL) {
            continue;
        }
        DmDestroyVertex(*itemPtr);
    }
    DbClearList(&purgerCtx->vertexList);
}

static void PurgerClearLabelResList(const DafPurgerRunCtxT *purgerCtx, DbListT *labelList, DmLabelTypeE labelType)
{
    uint32_t labelNum = DbListGetItemCnt(labelList);
    for (uint32_t i = 0; i < labelNum; ++i) {
        void **itemPtr = DbListItem(labelList, i);
        if (itemPtr == NULL) {
            continue;
        }
        Status ret = purgerCtx->cfg.releaseLabelAndLatchFunc(labelType, *itemPtr);
        if (ret != GMERR_OK) {
            SE_ERROR(ret, "Undo purger release label and latch unsucc.");
        }
    }
    DbClearList(labelList);
}

static void DafClearResList(DafPurgerRunCtxT *purgerCtx)
{
    DB_POINTER(purgerCtx);
    PurgerClearVertexList(purgerCtx);
    PurgerClearLabelResList(purgerCtx, &purgerCtx->vertexLabelList, VERTEX_LABEL);
    PurgerClearLabelResList(purgerCtx, &purgerCtx->kvLabelList, KV_TABLE);
    PurgerClearLabelResList(purgerCtx, &purgerCtx->edgeLabelList, EDGE_LABEL);
}

StatusInter DafCommitDafActions(DafPurgerRunCtxT *purgerRunCtx, TagLinkedListT *dafActions)
{
    DB_POINTER2(purgerRunCtx, dafActions);
    uint64_t startTime = DbClockGetTsc();
    if (DbLinkedListEmpty(dafActions)) {
        return STATUS_OK_INTER;
    }
    TrxCfgT trxCfg = {
        .readOnly = false,
        .isLiteTrx = false,
        .isBackGround = true,
        .isInteractive = false,
        .connId = DB_INVALID_UINT16,
        .trxType = OPTIMISTIC_TRX,
        .isolationLevel = REPEATABLE_READ,
    };
    StatusInter ret = TrxBegin(purgerRunCtx->seCtx->trx, &trxCfg);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Daf purge unable to begin transaction.");
        return ret;
    }
    DbMemCtxT *dafMemCtx = g_dafMgr.memCtx;
    IdxDeleteActionsT *item = NULL;
    IdxDeleteActionsT *itemTmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(item, itemTmp, dafActions, node)
    {
        DbLinkedListRemove(&item->node);
        DafExecuteIndexCommitActionOnPurgerImpl(purgerRunCtx, item);
        DbDynMemCtxFree(dafMemCtx, item);
        if (DbExceedTime(startTime, purgerRunCtx->cfg.splitTime)) {
            break;
        }
    }
    ret = TrxCommit(purgerRunCtx->seCtx->trx);
    if (ret != STATUS_OK_INTER) {
        // 只有乐观事务校验冲突才会失败，但是Purger线程的事务不会跟任何事务冲突
        SE_ERROR(ret, "Daf purge unable to commit transaction.");
    }
    DafClearResList(purgerRunCtx);
    TrxHandleContainerCtxList(purgerRunCtx->seCtx->trx, TRX_CLEAR_HANDLE);
    return ret;
}
bool DafPurgerMainImpl(DafPurgerRunCtxT *purgerRunCtx)
{
    if (!g_dafMgr.isInit) {
        DB_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "daf feature not supported.");
        return true;
    }
    // deferred actions
    TagLinkedListT cleanDafActions = {0};
    DbLinkedListInit(&cleanDafActions);
    // 1. 获取当前最小活跃事务，用于判断不可见版本
    TrxIdT minTrxId = TrxMgrGetMinTrxId(purgerRunCtx->seCtx->trxMgr);
    // 2. 截断Daf，将不可见的移动到cleanDafActions
    DafCutInvisibleDafActions(&cleanDafActions, minTrxId);
    if (!DbLinkedListEmpty(&cleanDafActions)) {
        // 触发Daf
        StatusInter ret = DafCommitDafActions(purgerRunCtx, &cleanDafActions);
        if (ret != STATUS_OK_INTER) {
            return false;
        }
    } else {
        return true;
    }
    if (!DbLinkedListEmpty(&cleanDafActions)) {
        // 超时，重新把没执行的，再接回去
        DafRegressUnCommitDafActions(&cleanDafActions);
    }
    return false;
}

void DafCommitImpl(TrxT *trx)
{
    if (trx->trx.base.dafCommitActionCollection == NULL) {
        return;
    }
    if (TrxGetIsolationLevel(trx) == REPEATABLE_READ) {
        // do not execute DAF actions at the commit time for RR
        DafStoreCommitActionCollectionImpl(trx);
    } else {
        DafCommitActionCollection(trx);
    }
}

DafFuncT DafAmInitFunc(void)
{
    return (DafFuncT){
        .isDafMgrInited = IsDafMgrInitedImpl,
        .dafCreateActionCollection = DafCreateActionCollectionImpl,
        .idxDelayDelete = DafDelayDeleteImpl,
        .idxDelayBatchDelete = DafDelayBatchDeleteImpl,
        .dafCommitActionCollection = DafCommitActionCollectionImpl,
        .dafCommit = DafCommitImpl,
        .dafRollbackActions = DafRollbackImpl,
        .dafPurgerMain = DafPurgerMainImpl,
    };
}

StatusInter DafMgrInit(DbMemCtxT *memCtx)
{
    g_dafMgr.isInit = false;
    DbMemCtxArgsT args = {0};
    args.dynCtxNeedRecycle = false;
    args.collectAllocSizeOnThisTree = true;
    DbMemCtxT *dafMemCtx = DbCreateDynMemCtx(memCtx, true, "DAF_MEM", &args);
    if (dafMemCtx == NULL) {
        SE_ERROR(OUT_OF_MEMORY_INTER, "cannot init daf");
        return OUT_OF_MEMORY_INTER;
    }
    DbSpinInit(&g_dafMgr.lock);
    DbLinkedListInit(&g_dafMgr.dafActionList);
    g_dafMgr.memCtx = dafMemCtx;
    g_dafMgr.isInit = true;
    DafFuncT dafFunc = DafAmInitFunc();
    DafSetAmFunc(&dafFunc);
    return STATUS_OK_INTER;
}

void DafMgrUnInit(void)
{
    if (g_dafMgr.memCtx != NULL) {
        DbDeleteDynMemCtx(g_dafMgr.memCtx);
        g_dafMgr.memCtx = NULL;
    }
    g_dafMgr.isInit = false;
}
