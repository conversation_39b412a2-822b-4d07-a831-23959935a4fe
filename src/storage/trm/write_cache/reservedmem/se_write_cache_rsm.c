/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:  write cache rsm in SE
 * Author:
 * Create: 2024-04-29
 */

#include "se_write_cache_rsm.h"
#include "se_write_cache.h"
#include "db_table_space.h"

WCachePageHeadT *SeGetRsmPageWithMgr(WriteCacheDescT *writeCacheDesc, MdMgrT *mdMgr, PageIdT pageId)
{
    DB_POINTER2(writeCacheDesc, mdMgr);
    uint8_t *pagePtr = NULL;
    StatusInter innerRet = MdGetPage(mdMgr, pageId, &pagePtr, ENTER_PAGE_NORMAL, true);
    if (SECUREC_UNLIKELY(innerRet != STATUS_OK_INTER || pagePtr == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED,
            "get rsm page worthless. innerRet = %" PRId32 ", pageId = %" PRIu32 " : %" PRIu32 " ", (int32_t)innerRet,
            pageId.deviceId, pageId.blockId);
        return NULL;
    }
    // 偏移PageHeadT大小，因为这个PageHeadT是heap页默认的头，缓存页有自己的头
    WCachePageHeadT *wcPageHead = (WCachePageHeadT *)(void *)((char *)pagePtr + sizeof(PageHeadT));
    return wcPageHead;
}

WCachePageHeadT *SeGetRsmPage(WriteCacheDescT *writeCacheDesc, PageIdT pageId)
{
    DB_POINTER(writeCacheDesc);
    MdMgrT *mdMgr = (MdMgrT *)(void *)SeGetPageMgr((uint16_t)writeCacheDesc->dbInstanceId);
    if (SECUREC_UNLIKELY(mdMgr == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "get page manager worthless.");
        return NULL;
    }
    uint8_t *pagePtr = NULL;
    StatusInter innerRet = MdGetPage(mdMgr, pageId, &pagePtr, ENTER_PAGE_NORMAL, true);
    if (SECUREC_UNLIKELY(innerRet != STATUS_OK_INTER || pagePtr == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED,
            "get rsm page worthless. innerRet = %" PRId32 ", pageId = %" PRIu32 " : %" PRIu32 " ", (int32_t)innerRet,
            pageId.deviceId, pageId.blockId);
        return NULL;
    }
    // 偏移PageHeadT大小，因为这个PageHeadT是heap页默认的头，缓存页有自己的头
    WCachePageHeadT *wcPageHead = (WCachePageHeadT *)(void *)((char *)pagePtr + sizeof(PageHeadT));
    return wcPageHead;
}

Status SeInitPageLock4Desc(WriteCacheDescT *desc)
{
    DB_POINTER(desc);
    PageIdT beginPage = desc->writePageId;
    MdMgrT *mdMgr = (MdMgrT *)(void *)SeGetPageMgr((uint16_t)desc->dbInstanceId);
    if (SECUREC_UNLIKELY(mdMgr == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "get page manager worthless.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    WCachePageHeadT *pageHead = SeGetRsmPageWithMgr(desc, mdMgr, beginPage);
    if (pageHead == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
            "SeGetRsmPageWithMgr beginPage worthless. beginPage = %" PRIu32 ":%" PRIu32 "", beginPage.deviceId,
            beginPage.blockId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbSpinInit(&pageHead->lock);
    uint32_t pageNum = 1;  // writePage
    PageIdT nextPage = pageHead->nextPageId;
    while (!DbIsPageIdEqual(beginPage, nextPage)) {
        WCachePageHeadT *tmpPageHead = SeGetRsmPageWithMgr(desc, mdMgr, nextPage);
        if (tmpPageHead == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
                "SeGetRsmPageWithMgr worthless. nextPage = %" PRIu32 ":%" PRIu32 "", nextPage.deviceId,
                nextPage.blockId);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        DbSpinInit(&tmpPageHead->lock);
        pageNum++;
        nextPage = tmpPageHead->nextPageId;
    }
    desc->pageNum = pageNum;
    return GMERR_OK;
}

void WcFreeRsmPage(WriteCacheDescT *writeCacheDesc, PageIdT pageId)
{
    DB_POINTER(writeCacheDesc);
    MdMgrT *mdMgr = (MdMgrT *)(void *)SeGetPageMgr((uint16_t)writeCacheDesc->dbInstanceId);
    if (mdMgr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "se get page manager worthless.");
        return;
    }
    DbInstanceT *dbInstance = NULL;
    Status ret = DbGetInstanceById(writeCacheDesc->dbInstanceId, &dbInstance);
    if (ret != GMERR_OK) {
        return;
    }
    FreePageParamT freePageParam = SeInitFreePageParam(writeCacheDesc->tableSpaceIndex, pageId, dbInstance,
        &writeCacheDesc->srvRsmUndoRecord, SE_INVALID_LABEL_ID, false);
    StatusInter innerRet = MdFreePage(mdMgr, &freePageParam);
    DbReleaseInstance(dbInstance);
    if (innerRet != STATUS_OK_INTER) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED,
            "free rsm page in WcFreeRsmPage worthless. pageId = %" PRIu32 ":%" PRIu32 "", pageId.deviceId,
            pageId.blockId);
        return;
    }
}

Status CacheAllocRsmPage(PageIdT *outputPageId, WriteCacheDescT *writeCacheDesc)
{
    *outputPageId = SE_INVALID_PAGE_ADDR;
    MdMgrT *mdMgr = (MdMgrT *)(void *)SeGetPageMgr(DbGetProcGlobalId());
    if (mdMgr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "get page manager worthless.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    // 传入无效的labelId,页搬迁不搬迁
    PageIdT newPageId;
    AllocPageParamT allocPageParam = SeInitAllocPageParam(writeCacheDesc->tableSpaceIndex,
        writeCacheDesc->writeCacheTrmId, RSM_INVALID_LABEL_ID, NULL, &writeCacheDesc->srvRsmUndoRecord);
    StatusInter innerRet = MdAllocPage(mdMgr, &allocPageParam, &newPageId);
    if (innerRet != STATUS_OK_INTER) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            MEMORY_OPERATE_FAILED_INTER, "alloc rsm page worthless. innerRet = %" PRId32 "", (int32_t)innerRet);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    uint8_t *newPagePtr = NULL;
    innerRet = MdGetPage(mdMgr, newPageId, &newPagePtr, ENTER_PAGE_NORMAL, true);
    if (innerRet != STATUS_OK_INTER || newPagePtr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED,
            "get rsm page by pageid worthless. innerRet = %" PRId32 ", pageId = %" PRIu32 ":%" PRIu32 "",
            (int32_t)innerRet, newPageId.deviceId, newPageId.blockId);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    // 偏移PageHeadT大小，因为这个PageHeadT是heap页默认的头，缓存页有自己的头
    WCachePageHeadT *wcPageHead = (WCachePageHeadT *)(void *)(newPagePtr + sizeof(PageHeadT));
    DbSpinInit(&wcPageHead->lock);
    wcPageHead->currPageId = newPageId;
    wcPageHead->nextPageId = newPageId;                          // 写缓存初始只有一个页
    wcPageHead->freeOffset = (uint32_t)sizeof(WCachePageHeadT);  // 偏移页头部
    wcPageHead->mergeBeginOffset = wcPageHead->freeOffset;       // 初始为第一个req的偏移

    *outputPageId = newPageId;
    return GMERR_OK;
}

Status SeWriteCacheCreateRsm(
    uint32_t namespaceId, uint32_t metaId, bool isPartition, ShmemPtrT *writeCachePtr, DbInstanceHdT dbInstance)
{
    uint16_t instanceId = DbGetInstanceId(dbInstance);
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(instanceId);
    if (seInstance == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "storage instance %" PRIu16 " novalid", instanceId);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    // 申请WriteCacheDescT结构体的保留内存
    ShmemPtrT writeCacheDescPtr;
    WriteCacheDescT *writeCacheDesc = DbRsmAlloc(sizeof(WriteCacheDescT), &writeCacheDescPtr);
    if (writeCacheDesc == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "RsmAlloc writeCacheDescPtr worthless.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(writeCacheDesc, sizeof(WriteCacheDescT), 0, sizeof(WriteCacheDescT));
    DbSpinInit(&writeCacheDesc->reqLock);

    writeCacheDesc->tableSpaceIndex = DB_WRITE_CACHE_RSM_TABLE_SPACE_INDEX;
    writeCacheDesc->writeCacheTrmId = SeGetNewTrmId(seInstance);
    writeCacheDesc->labelId = metaId;

    RsmUndoRecordInit(&writeCacheDesc->cltRsmUndoRecord, RSM_UNDO_INIT_RECORD_NUM);
    RsmUndoRecordInit(&writeCacheDesc->srvRsmUndoRecord, RSM_UNDO_INIT_RECORD_NUM);

    PageIdT pageId = SE_INVALID_PAGE_ADDR;
    Status ret = CacheAllocRsmPage(&pageId, writeCacheDesc);
    if (ret != GMERR_OK) {
        DbRsmFree(writeCacheDescPtr);
        return ret;
    }

    writeCacheDesc->dbInstanceId = (int32_t)instanceId;
    writeCacheDesc->namespaceId = namespaceId;
    writeCacheDesc->isWriteCacheUseRsm = true;
    writeCacheDesc->isPartition = isPartition;
    writeCacheDesc->checkingStatus = 0;
    writeCacheDesc->writePageId = pageId;
    writeCacheDesc->mergePageId = pageId;
    writeCacheDesc->pageNum = 1;
    writeCacheDesc->mergedReqNum = 0;
    *writeCachePtr = writeCacheDescPtr;
    return ret;
}

void SeWriteCacheFreeWhenInitRsm(ShmemPtrT writeCachePtr, DbInstanceHdT dbInstance)
{
    WriteCacheDescT *writeCacheDesc = (WriteCacheDescT *)DbShmPtrToAddr(writeCachePtr);
    if (writeCacheDesc == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "DbShmPtrToAddr writeCachePtr worthless.");
        return;
    }
    MdMgrT *mdMgr = (MdMgrT *)(void *)SeGetPageMgr(DbGetInstanceId(dbInstance));
    if (mdMgr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "se get page manager worthless.");
        return;
    }
    FreePageParamT freePageParam = SeInitFreePageParam(writeCacheDesc->tableSpaceIndex, writeCacheDesc->writePageId,
        dbInstance, &writeCacheDesc->srvRsmUndoRecord, SE_INVALID_LABEL_ID, false);
    StatusInter innerRet = MdFreePage(mdMgr, &freePageParam);
    if (innerRet != STATUS_OK_INTER) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED,
            "free rsm page worthless. pageId = %" PRIu32 ":%" PRIu32 "", writeCacheDesc->writePageId.deviceId,
            writeCacheDesc->writePageId.blockId);
        return;
    }
    DbRsmFree(writeCachePtr);
}

void SeFreeWriteCachePageRing(WriteCacheDescT *writeCacheDesc, PageIdT beginPageId)
{
    DB_POINTER(writeCacheDesc);
    MdMgrT *mdMgr = (MdMgrT *)(void *)SeGetPageMgr((uint16_t)writeCacheDesc->dbInstanceId);
    if (SECUREC_UNLIKELY(mdMgr == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "get page manager worthless.");
        return;
    }
    WCachePageHeadT *pageHead = SeGetRsmPageWithMgr(writeCacheDesc, mdMgr, beginPageId);
    if (pageHead == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
            "beginPage worthless. beginPage = %" PRIu32 ":%" PRIu32 "", beginPageId.deviceId, beginPageId.blockId);
        return;
    }
    // 循环遍历每一页，释放内存
    PageIdT curPageId = pageHead->nextPageId;
    while (!DbIsPageIdEqual(curPageId, beginPageId)) {
        pageHead = SeGetRsmPageWithMgr(writeCacheDesc, mdMgr, curPageId);
        if (pageHead == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
                "curPage worthless. curPage = %" PRIu32 ":%" PRIu32, curPageId.deviceId, curPageId.blockId);
            return;
        }
        curPageId = pageHead->nextPageId;                     // 先获取下一页
        WcFreeRsmPage(writeCacheDesc, pageHead->currPageId);  // 释放自身
    }
    WcFreeRsmPage(writeCacheDesc, curPageId);  // 释放开始页
}

void SeFreeWriteCacheMemWithRsm(WriteCacheDescT *writeCacheDesc, ShmemPtrT nodePtr)
{
    DB_POINTER(writeCacheDesc);
    SeFreeWriteCachePageRing(writeCacheDesc, writeCacheDesc->writePageId);
    DbRsmFree(nodePtr);  // 最后释放WriteCacheDescT结构体内存
}

void CacheRsmUndoLogForDeletePage(WriteCacheDescT *desc, PageIdT newNextPageId)
{
    DB_POINTER(desc);
    if (!IsNeedRsmUndo(desc)) {
        return;
    }
    RsmUndoRecordT *rsmUndoRecord = &desc->srvRsmUndoRecord;
    RsmUndoOpRecordT *opRecord = RsmUndoGetNextFreeRecord(rsmUndoRecord, rsmUndoRecord->usedUndoRecNum);
    opRecord->recordType = RSM_UNDO_WRITE_CACHE_DELETE_PAGE;
    opRecord->rsmWriteCacheDeletePageRec.deviceId = newNextPageId.deviceId;
    opRecord->rsmWriteCacheDeletePageRec.blockId = newNextPageId.blockId;
    RsmUndoMoveNextFreeRecord(rsmUndoRecord);
}

void DoDeleteRsmFreePages(WriteCacheDescT *desc, int32_t numToScaleIn, WCachePageHeadT *writePageHead)
{
    int32_t scaleNum = 0;
    PageIdT mergePageId = desc->mergePageId;
    PageIdT curPageId = writePageHead->nextPageId;  // 从writePage下一个开始遍历，不释放writePage和mergePage
    WCachePageHeadT *pageHead = NULL;
    while (!DbIsPageIdEqual(curPageId, mergePageId) && scaleNum < numToScaleIn) {  // writePage到mergePage才可能为free
        pageHead = SeGetRsmPage(desc, curPageId);
        if (pageHead == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
                "SeGetRsmPage curPage worthless. curPage = %" PRIu32 ":%" PRIu32 "", curPageId.deviceId,
                curPageId.blockId);
            return;
        }
        curPageId = pageHead->nextPageId;
        scaleNum++;
    }
    if (scaleNum == 0) {
        return;  // 无可释放的空闲页，直接返回
    }
    PageIdT deleteBeginPageId = writePageHead->nextPageId;
    CacheRsmUndoLogForDeletePage(desc, curPageId);
    SHM_CRASHPOINT_ONE(SHM_CRASH_WC_SCALE_IN_AFTER_LOG_DELETE_PAGE);
    writePageHead->nextPageId = curPageId;  // 将空闲页链表从环中摘除
    SHM_CRASHPOINT_ONE(SHM_CRASH_WC_SCALE_IN_AFTER_UPDATE_NEXT_PAGE_ID);
    CacheSrvRsmUndoLogLeave(desc);
    SHM_CRASHPOINT_ONE(SHM_CRASH_WC_SCALE_IN_AFTER_LEAVE_DELETE_PAGE_LOG);
    desc->pageNum -= scaleNum;
    SHM_CRASHPOINT_ONE(SHM_CRASH_WC_SCALE_IN_AFTER_DEC_PAGE_NUM);
    pageHead->nextPageId = deleteBeginPageId;           // 空闲页链表形成环
    SeFreeWriteCachePageRing(desc, deleteBeginPageId);  // 释放空闲页环的内存
    SHM_CRASHPOINT_ONE(SHM_CRASH_WC_SCALE_IN_AFTER_FREE_PAGE);
}

void SeDeleteRsmFreePages(WriteCacheDescT *desc, int32_t numToScaleIn)
{
    PageIdT writePageId = desc->writePageId;  // 遍历开始页，从写开始页开始遍历
    WCachePageHeadT *writePageHead = SeGetRsmPage(desc, writePageId);
    if (writePageHead == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
            "SeGetRsmPage beginPage worthless. beginPage = %" PRIu32 ":%" PRIu32 "", writePageId.deviceId,
            writePageId.blockId);
        return;
    }
    if (DbIsPageIdEqual(writePageId, writePageHead->nextPageId)) {
        return;  // 说明当前只有一个页，则直接返回
    }
#ifndef NDEBUG
    DbRsmKernelEntry(DB_RSM_KERNEL_WRITE_CACHE_DML);  // 缓存缩容前进入内核态
#endif
    DoDeleteRsmFreePages(desc, numToScaleIn, writePageHead);
#ifndef NDEBUG
    DbRsmKernelLeave(DB_RSM_KERNEL_WRITE_CACHE_DML);  // 缓存缩容后离开内核态
#endif
}

Status SeGetRsmFreePageNum(WriteCacheDescT *desc, int32_t *freePageNum)
{
    DB_POINTER2(desc, freePageNum);
    MdMgrT *mdMgr = (MdMgrT *)(void *)SeGetPageMgr((uint16_t)desc->dbInstanceId);
    if (SECUREC_UNLIKELY(mdMgr == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "get page manager worthless.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    PageIdT curPageId = desc->writePageId;
    const WCachePageHeadT *pageHead = SeGetRsmPageWithMgr(desc, mdMgr, curPageId);
    if (pageHead == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
            "SeGetRsmPageWithMgr writePage worthless. curPage = %" PRIu32 ":%" PRIu32 "", curPageId.deviceId,
            curPageId.blockId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    int32_t freeNum = 0;
    if (IsFreePage(pageHead)) {
        freeNum++;
    }
    curPageId = pageHead->nextPageId;
    PageIdT mergePageId = desc->mergePageId;
    while (!DbIsPageIdEqual(curPageId, mergePageId)) {  // writePage到mergePage才可能为free，包括writePage本身
        pageHead = SeGetRsmPageWithMgr(desc, mdMgr, curPageId);
        if (pageHead == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
                "SeGetRsmPageWithMgr curPage worthless. curPage = %" PRIu32 ":%" PRIu32 "", curPageId.deviceId,
                curPageId.blockId);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        freeNum++;
        curPageId = pageHead->nextPageId;
    }
    *freePageNum = freeNum;
    return GMERR_OK;
}

bool SeRsmIsNextPageEqualCurrPage(const WCachePageHeadT *page)
{
    return DbIsPageIdEqual(page->nextPageId, page->currPageId);
}

bool SeRsmIsNextPageEqualMergePage(const WriteCacheDescT *writeCacheDesc, const WCachePageHeadT *page)
{
    return DbIsPageIdEqual(page->nextPageId, writeCacheDesc->mergePageId);
}

void CacheRsmUndoLogForUpdateWritePage(WriteCacheDescT *desc, PageIdT newWritePageId)
{
    DB_POINTER(desc);
    if (!IsNeedRsmUndo(desc)) {
        return;
    }
    RsmUndoRecordT *rsmUndoRecord = &desc->cltRsmUndoRecord;
    RsmUndoOpRecordT *opRecord = RsmUndoGetNextFreeRecord(rsmUndoRecord, rsmUndoRecord->usedUndoRecNum);
    opRecord->recordType = RSM_UNDO_WRITE_CACHE_UPDATE_WRITE_PAGE;
    opRecord->rsmWriteCacheUpdateWritePageRec.deviceId = newWritePageId.deviceId;
    opRecord->rsmWriteCacheUpdateWritePageRec.blockId = newWritePageId.blockId;
    RsmUndoMoveNextFreeRecord(rsmUndoRecord);
}

void SeRsmSetWritePage(WriteCacheDescT *desc, const WCachePageHeadT *page)
{
    CacheRsmUndoLogForUpdateWritePage(desc, page->currPageId);
    SHM_CRASHPOINT(SHM_CRASH_WC_SET_REQ_AFTER_LOG_WRITE_PAGE_ID);
    desc->writePageId = page->currPageId;
    SHM_CRASHPOINT(SHM_CRASH_WC_SET_REQ_AFTER_UPDATE_WRITE_PAGE);
    CacheCltRsmUndoLogLeave(desc);
    SHM_CRASHPOINT(SHM_CRASH_WC_SET_REQ_AFTER_LEAVE_WRITE_PAGE_LOG);
}

void CacheRsmUndoLogForUpdateMergePage(WriteCacheDescT *desc, PageIdT newMergePageId)
{
    DB_POINTER(desc);
    if (!IsNeedRsmUndo(desc)) {
        return;
    }
    RsmUndoRecordT *rsmUndoRecord = &desc->srvRsmUndoRecord;
    RsmUndoOpRecordT *opRecord = RsmUndoGetNextFreeRecord(rsmUndoRecord, rsmUndoRecord->usedUndoRecNum);
    opRecord->recordType = RSM_UNDO_WRITE_CACHE_UPDATE_MERGE_PAGE;
    opRecord->rsmWriteCacheUpdateMergePageRec.deviceId = newMergePageId.deviceId;
    opRecord->rsmWriteCacheUpdateMergePageRec.blockId = newMergePageId.blockId;
    RsmUndoMoveNextFreeRecord(rsmUndoRecord);
}

void SeRsmUpdateMergePage(const WCachePageHeadT *mergePage, WriteCacheDescT *desc)
{
    if (DbIsPageIdEqual(desc->mergePageId, desc->writePageId)) {  // 保证mergePage不越过writePage
        return;
    }
    CacheRsmUndoLogForUpdateMergePage(desc, mergePage->nextPageId);
    SHM_CRASHPOINT_ONE(SHM_CRASH_WC_MERGE_AFTER_LOG_NEW_MERGE_PAGE_ID);
    desc->mergePageId = mergePage->nextPageId;
    SHM_CRASHPOINT_ONE(SHM_CRASH_WC_MERGE_AFTER_UPDATE_MERGE_PAGE);
    CacheSrvRsmUndoLogLeave(desc);
    SHM_CRASHPOINT_ONE(SHM_CRASH_WC_MERGE_AFTER_LEAVE_UPDATE_MERGE_PAGE_LOG);
}
