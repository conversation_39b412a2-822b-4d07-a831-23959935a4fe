/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: se_clustered_hash_page_mgr.c
 * Description: clustered hash label page mgr source file
 * Author: zhangyoujian
 * Create: 2022/09/13
 */
#include "se_clustered_hash_page_mgr.h"
#include "se_log.h"
#include "se_hash_mem.h"
#ifdef __cplusplus
extern "C" {
#endif

inline static __attribute__((always_inline)) void SwapDirPageItem(
    DirPageCacheItemT *itemDst, DirPageCacheItemT *itemSrc)
{
    if (itemDst == itemSrc) {
        return;
    }
    DirPageCacheItemT tmp = *itemDst;
    *itemDst = *itemSrc;
    *itemSrc = tmp;
}

inline static __attribute__((always_inline)) uint8_t *FindDirPageFromCache(
    ChLabelPageMemRunCtxT *pageMemRunCtx, uint32_t dirPageId)
{
    if (SECUREC_UNLIKELY(pageMemRunCtx->version != pageMemRunCtx->memMgr->base.version)) {
        // 说明表已经被drop或者truncate, 缓存失效
        (void)memset_s(pageMemRunCtx->dirCache, sizeof(pageMemRunCtx->dirCache), 0, sizeof(pageMemRunCtx->dirCache));
        pageMemRunCtx->version = pageMemRunCtx->memMgr->base.version;
    }
    for (uint32_t i = 0; i < IDX_PAGE_CACHE_COUNT; ++i) {
        if (pageMemRunCtx->dirCache[i].dirPageId == dirPageId && pageMemRunCtx->dirCache[i].dirPageAddr != NULL) {
            SwapDirPageItem(&pageMemRunCtx->dirCache[i], &pageMemRunCtx->dirCache[0]);
            return pageMemRunCtx->dirCache[0].dirPageAddr;
        }
    }
    return NULL;
}

inline static __attribute__((always_inline)) void AddDirPageToCache(
    ChLabelPageMemRunCtxT *pageMemRunCtx, uint32_t dirPageId, uint8_t *dirPageAddr)
{
    uint32_t i = 0;
    for (; i < IDX_PAGE_CACHE_COUNT; ++i) {
        if (pageMemRunCtx->dirCache[i].dirPageId == 0 && pageMemRunCtx->dirCache[i].dirPageAddr == NULL) {
            break;
        }
    }
    i = (i < IDX_PAGE_CACHE_COUNT) ? i : IDX_PAGE_CACHE_COUNT - 1;
    pageMemRunCtx->dirCache[i].dirPageId = dirPageId;
    pageMemRunCtx->dirCache[i].dirPageAddr = dirPageAddr;
}

static StatusInter ClusteredHashAllocDirPageInner(
    ChLabelPageMemRunCtxT *pageMemRunCtx, RsmUndoRecordT *rsmUndoRec, uint32_t dirPageId, PageIdT *pageId)
{
    AllocPageParamT allocPageParam = SeInitAllocPageParam(pageMemRunCtx->memMgr->base.tableSpaceIndex,
        pageMemRunCtx->memMgr->base.fileId, pageMemRunCtx->labelId, NULL, rsmUndoRec);
    StatusInter ret = MdAllocPage(pageMemRunCtx->mdMgr, &allocPageParam, pageId);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash alloc new dirPage unsucc. dirPageId: %" PRIu32, dirPageId);
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashAllocDirPage(
    ChLabelPageMemRunCtxT *pageMemRunCtx, RsmUndoRecordT *rsmUndoRec, uint32_t dirPageId, uint8_t **dirPageAddr)
{
    DB_POINTER3(pageMemRunCtx, dirPageAddr, pageMemRunCtx->pageAddr);
    PageIdT pageId;
    StatusInter ret = ClusteredHashAllocDirPageInner(pageMemRunCtx, rsmUndoRec, dirPageId, &pageId);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    ret = MdGetPage(pageMemRunCtx->mdMgr, pageId, dirPageAddr, ENTER_PAGE_NORMAL, false);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        FreePageParamT freePageParam = SeInitFreePageParam(
            pageMemRunCtx->memMgr->base.tableSpaceIndex, pageId, NULL, rsmUndoRec, SE_INVALID_LABEL_ID, false);
        (void)MdFreePage(pageMemRunCtx->mdMgr, &freePageParam);
        SE_ERROR(ret, "clustered hash get new dirPage unsucc. dirPageId: %" PRIu32, dirPageId);
        return ret;
    }
    *dirPageAddr = *dirPageAddr + CHLABEL_PAGE_HEAD_OFFSET;
    AddDirPageToCache(pageMemRunCtx, dirPageId, *dirPageAddr);
    pageMemRunCtx->pageAddr[dirPageId] = pageId;
    return STATUS_OK_INTER;
}

inline __attribute__((always_inline)) StatusInter ClusteredHashGetDirPage(
    ChLabelPageMemRunCtxT *pageMemRunCtx, uint32_t dirPageId, uint8_t **dirPageAddr)
{
    uint8_t *pageCached = FindDirPageFromCache(pageMemRunCtx, dirPageId);
    if (SECUREC_UNLIKELY(pageCached == NULL) || DbCommonGetWarmReboot()) {
        PageIdT pageId = pageMemRunCtx->pageAddr[dirPageId];
        StatusInter ret = MdGetPage(pageMemRunCtx->mdMgr, pageId, &pageCached, ENTER_PAGE_PINNED, false);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "get dirPage from dirPageId unsucc: %" PRIu32, dirPageId);
            return ret;
        }
        pageCached = pageCached + CHLABEL_PAGE_HEAD_OFFSET;
        AddDirPageToCache(pageMemRunCtx, dirPageId, pageCached);
    }
    *dirPageAddr = pageCached;
    return STATUS_OK_INTER;
}

// *isNewPage，出参，表示page是否是新申请的
ALWAYS_INLINE_C static StatusInter AllocPageFromCacheOrMd(ChLabelPageMemRunCtxT *pageMemRunCtx,
    RsmUndoRecordT *labelRsmUndo, uint32_t blockId, PageIdT *pageId, bool *isNewPage)
{
    AllocPageParamT allocPageParam = SeInitAllocPageParam(pageMemRunCtx->memMgr->base.tableSpaceIndex,
        pageMemRunCtx->memMgr->base.fileId, pageMemRunCtx->labelId, NULL, labelRsmUndo);
    StatusInter ret = MdAllocPage(pageMemRunCtx->mdMgr, &allocPageParam, pageId);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash alloc new segPage unsucc. dirPageId: %" PRIu32, blockId);
        return ret;
    }
    *isNewPage = true;
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashAllocSegPage(
    ChLabelPageMemRunCtxT *pageMemRunCtx, RsmUndoRecordT *labelRsmUndo, uint32_t blockId, DbMemAddrT *pageInfo)
{
    DB_POINTER2(pageMemRunCtx, pageInfo);
    PageIdT pageId;
    bool isNewPage = false;
    StatusInter ret = AllocPageFromCacheOrMd(pageMemRunCtx, labelRsmUndo, blockId, &pageId, &isNewPage);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    uint8_t *pageAddr = NULL;
    ret = MdGetPage(pageMemRunCtx->mdMgr, pageId, &pageAddr, ENTER_PAGE_NORMAL, false);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        if (isNewPage) {
            FreePageParamT freePageParam = SeInitFreePageParam(
                pageMemRunCtx->memMgr->base.tableSpaceIndex, pageId, NULL, labelRsmUndo, SE_INVALID_LABEL_ID, false);
            (void)MdFreePage(pageMemRunCtx->mdMgr, &freePageParam);
        }
        SE_ERROR(ret, "clustered hash get new segPage unsucc. blockId: %" PRIu32, blockId);
        return ret;
    }
    pageInfo->virtAddr = pageAddr + CHLABEL_PAGE_HEAD_OFFSET;
    pageInfo->pageAddr = pageId;
    return ret;
}

StatusInter ClusteredHashFreeAllDirPage(
    ChLabelPageMemRunCtxT *pageMemRunCtx, uint32_t dirPageCount, RsmUndoRecordT *rsmUndoRec, EhTrcPageCacheT *cache)
{
    DB_POINTER(pageMemRunCtx);
    for (uint32_t i = 0; i < dirPageCount; i++) {
        PageIdT pageId = pageMemRunCtx->pageAddr[i];
        if (cache != NULL && cache->cnt < EH_DIR_AND_SEG_PAGE) {
            cache->pageId[cache->cnt++] = pageId;
        } else {
            Status ret = ChFreePageToMd(pageMemRunCtx, pageId, rsmUndoRec);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                StatusInter tmpRet = DbGetStatusInterErrno(ret);
                SE_ERROR(tmpRet, "clustered hash, free dir page ((%" PRIu32 ".%" PRIu32 ")) unsucc",
                    pageMemRunCtx->memMgr->base.fileId, i);
                return tmpRet;
            }
        }
        pageMemRunCtx->pageAddr[i] = SE_INVALID_PAGE_ADDR;
    }
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashAllocOverFlowPage(ChLabelPageMemRunCtxT *pageMemRunCtx, RsmUndoRecordT *rsmUndoRec,
    uint32_t blockId, DbMemAddrT *pageInfo, DbInstanceHdT dbInstance)
{
    DB_POINTER2(pageMemRunCtx, pageInfo);
    ClusteredHashMemMgrT *memMgr = pageMemRunCtx->memMgr;
    PageIdT pageId;
    AllocPageParamT allocPageParam = SeInitAllocPageParam(pageMemRunCtx->memMgr->base.tableSpaceIndex,
        pageMemRunCtx->memMgr->base.fileId, pageMemRunCtx->labelId, dbInstance, rsmUndoRec);
    StatusInter ret = MdAllocPage(pageMemRunCtx->mdMgr, &allocPageParam, &pageId);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash alloc new segPage unsucc. blockId: %" PRIu32, blockId);
        return ret;
    }
    uint8_t *pageAddr = NULL;
    ret = MdGetPage(pageMemRunCtx->mdMgr, pageId, &pageAddr, ENTER_PAGE_NORMAL, false);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        FreePageParamT freePageParam = SeInitFreePageParam(
            pageMemRunCtx->memMgr->base.tableSpaceIndex, pageId, dbInstance, rsmUndoRec, SE_INVALID_LABEL_ID, false);
        (void)MdFreePage(pageMemRunCtx->mdMgr, &freePageParam);
        SE_ERROR(ret, "clustered hash get new segPage unsucc. blockId: %" PRIu32, blockId);
        return ret;
    }
    DB_POINTER(pageAddr);
    pageInfo->virtAddr = pageAddr + CHLABEL_PAGE_HEAD_OFFSET;
    pageInfo->pageAddr = pageId;

    uint32_t itemId;
    DbArrayAddrT itemAddr;
    PageIdT *item = NULL;
    Status retTmp = DbShmArrayGetItem(&memMgr->base.hashSwizzleArray, &itemId, (void **)&item, &itemAddr);
    if (SECUREC_UNLIKELY(retTmp != GMERR_OK)) {
        FreePageParamT freePageParam = SeInitFreePageParam(
            pageMemRunCtx->memMgr->base.tableSpaceIndex, pageId, dbInstance, rsmUndoRec, SE_INVALID_LABEL_ID, false);
        (void)MdFreePage(pageMemRunCtx->mdMgr, &freePageParam);
        SE_LAST_ERROR(retTmp, "hashSwizzleArray is NULL");
        return DbGetStatusInterErrno(retTmp);
    }
    DB_ASSERT(itemId == blockId);  // 分配的itemId应该和blockId是一致的
    *item = pageInfo->pageAddr;
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashGetOverFlowPage(
    ChLabelPageMemRunCtxT *pageMemRunCtx, uint32_t blockId, uint32_t overflowPageCount, DbMemAddrT *pageInfo)
{
    DB_POINTER2(pageMemRunCtx, pageInfo);
    ClusteredHashMemMgrT *memMgr = pageMemRunCtx->memMgr;
    if (blockId >= overflowPageCount) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER, "Unable to get overFlow page by blockId: %" PRIu32, blockId);
        return INTERNAL_ERROR_INTER;
    }
    DbArrayAddrT itemAddr;
    PageIdT *pageAddr = (PageIdT *)DbShmArrayGetItemById(&memMgr->base.hashSwizzleArray, blockId, &itemAddr);
    if (pageAddr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "clustered hash get hashSwizzleArray item unsucc");
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    uint8_t *page = NULL;
    StatusInter ret = MdGetPage(pageMemRunCtx->mdMgr, *pageAddr, &page, ENTER_PAGE_NORMAL, true);
    if (ret != STATUS_OK_INTER) {
        Status externalRet = DbGetExternalErrno(ret);
        SE_LAST_ERROR(externalRet, "get overFlow page unsucc. seg %" PRIu32 " offset %" PRIu32, pageAddr->deviceId,
            pageAddr->blockId);
        return DbGetExternalErrno(ret);
    }
    pageInfo->virtAddr = page + CHLABEL_PAGE_HEAD_OFFSET;
    pageInfo->pageAddr = *pageAddr;
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashFreeAllOverFlowPage(
    ChLabelPageMemRunCtxT *pageMemRunCtx, uint32_t overflowPageCount, RsmUndoRecordT *rsmUndoRec)
{
    DB_POINTER(pageMemRunCtx);
    ClusteredHashMemMgrT *memMgr = pageMemRunCtx->memMgr;
    DbArrayAddrT itemAddr;
    PageIdT *pageAddr = NULL;
    for (uint32_t blockId = 0; blockId < overflowPageCount; ++blockId) {
        pageAddr = (PageIdT *)DbShmArrayGetItemById(&memMgr->base.hashSwizzleArray, blockId, &itemAddr);
        if (pageAddr == NULL) {
            SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "clustered hash get hashSwizzleArray item unsucc");
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        Status retTmp = ChFreePageToMd(pageMemRunCtx, *pageAddr, rsmUndoRec);
        if (retTmp != GMERR_OK) {
            SE_ERROR(retTmp, "unsucc to free overFlow page (%" PRIu32 ", deviceId:%" PRIu32 ", blockId:%" PRIu32 ")",
                blockId, pageAddr->deviceId, pageAddr->blockId);
            return DbGetStatusInterErrno(retTmp);
        }
    }
    return STATUS_OK_INTER;
}

#ifdef __cplusplus
}
#endif
