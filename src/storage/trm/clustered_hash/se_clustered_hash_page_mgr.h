/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: se_clustered_hash_page_mgr.h
 * Description: clustered hash label page mgr head file
 * Author: zhangyoujian
 * Create: 2022/09/13
 */
#ifndef SE_CLUSTERED_HASH_PAGE_MGR_H
#define SE_CLUSTERED_HASH_PAGE_MGR_H
#include "se_hash_mem.h"
#include "se_hash_index.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CLUSTERED_HASH_INVALID_SLOTID DB_INVALID_ID16

typedef struct {
    uint16_t nextSlotId;
} ChLabelSlotId;

typedef struct ChLabelPageMetaData {
    uint32_t pageRowSize;
    uint32_t tupleSize;
    uint16_t bucketPos;
    uint16_t stashBucketPos;
    uint16_t tuplePos;
    uint16_t tupleCntPerPage;
    uint16_t hashNormalBucketNumPerPage;
    uint16_t maxTupleCntPerOverFlowPage;
    uint32_t firstEntryPosPerOverFlowPage;
    uint32_t firstTuplePosPerOverFlowPage;
    PageIdT nextPageAddr;
} ChLabelPageMetaDataT;

typedef struct ChLabelPageHeader {
    PageHeadT baseHead;
    uint32_t blockId;
    uint16_t freeSlotCount;
    uint16_t freeSlotId;
} ChLabelPageHeaderT;

#define CHLABEL_PAGE_HEAD_OFFSET sizeof(ChLabelPageHeaderT)
#define CHLABEL_PAGE_METADATA_OFFSET sizeof(ChLabelPageMetaDataT)

typedef struct DashEhLabelMeta {
    uint32_t bucketSize;
    uint32_t availPageSize;
#ifndef NDEBUG
    uint32_t scaleInUpgradePageCnt;
#endif
    uint32_t scaleInProbCnt;
    uint32_t scaleInPageCnt;
    uint32_t overflowPageTupleUsed;
    uint16_t bitMapSize;
    uint16_t bloomFilterSize;
    uint16_t hashEntryPerNormalBucket;
    uint16_t bitmapSizePerOverFlowPage;
    uint16_t reverseCnt;
    ChLabelPageMetaDataT metaData;
} DashEhLabelMetaT;

typedef struct TagDirPageCacheItemT {
    uint32_t reserved;
    uint32_t dirPageId;
    uint8_t *dirPageAddr;
} DirPageCacheItemT;

typedef struct DashEhMemMeta {
    uint32_t hashSegBits;
    uint32_t hashEntryNumPerPage;
    uint32_t hashSegNumPerPage;
    uint32_t scaleInCnt;      // 缩容次数，每1s成功缩容一次，约需要136年才会达到计数上限值
    uint32_t dirNextItemIdx;  // 遍历dir page过程中，上一次遍历结束时的dir的idx
    uint32_t dirCurItemIdx;   // 搬迁升降级数据遍历dir page过程中，当前需要探测的dir的idx
    uint32_t dirCurItemCnt;   // 搬迁升降级数据遍历dir page过程中，当前dir已经遍历的次数
    uint32_t hashEntrySize;
} DashEhMemMetaT;

typedef struct ClusteredHashMemMgr {
    HashMemMgrBaseT base;
    DashEhMemMetaT dashMemMeta;
} ClusteredHashMemMgrT;

typedef struct TagChLabelPageMemRunCtxT {
    ClusteredHashMemMgrT *memMgr;
    MdMgrT *mdMgr;
    PageIdT *pageAddr;
    uint64_t version;
    DirPageCacheItemT dirCache[IDX_PAGE_CACHE_COUNT];
    uint32_t pagePtrArrCapacity;
    uint32_t labelId;
} ChLabelPageMemRunCtxT;

StatusInter ClusteredHashAllocDirPage(
    ChLabelPageMemRunCtxT *pageMemRunCtx, RsmUndoRecordT *rsmUndoRec, uint32_t dirPageId, uint8_t **dirPageAddr);
StatusInter ClusteredHashGetDirPage(ChLabelPageMemRunCtxT *pageMemRunCtx, uint32_t dirPageId, uint8_t **dirPageAddr);

StatusInter ClusteredHashAllocSegPage(
    ChLabelPageMemRunCtxT *pageMemRunCtx, RsmUndoRecordT *labelRsmUndo, uint32_t blockId, DbMemAddrT *pageInfo);

#ifndef NDEBUG
inline static void ClusteredHashCheckFileId(uint32_t fileId, uint8_t *segPageAddr)
{
    ChLabelPageHeaderT *pageHead = ((ChLabelPageHeaderT *)(void *)(segPageAddr - CHLABEL_PAGE_HEAD_OFFSET));
    if (SECUREC_UNLIKELY(fileId != pageHead->baseHead.trmId)) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER,
            "clustered hash, check fileId unsucc, label fileId = %" PRIu32 ", pageHead fileId = %" PRIu32 ".", fileId,
            pageHead->baseHead.trmId);
    }
}
#endif

static ALWAYS_INLINE StatusInter ClusteredHashGetSegPage(
    MdMgrT *mdMgr, PageIdT pageAddr, uint32_t fileId, uint8_t **segPageAddr)
{
    DB_POINTER2(mdMgr, segPageAddr);
    StatusInter ret = MdGetPage(mdMgr, pageAddr, segPageAddr, ENTER_PAGE_NORMAL, true);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get segment page from shmPtr(%" PRIu32 ", %" PRIu32 ") unsucc",
            pageAddr.deviceId, pageAddr.blockId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    *segPageAddr = *segPageAddr + CHLABEL_PAGE_HEAD_OFFSET;
#if !defined(NDEBUG) && !defined(FEATURE_RSMEM)
    ClusteredHashCheckFileId(fileId, *segPageAddr);
#endif
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashAllocOverFlowPage(ChLabelPageMemRunCtxT *pageMemRunCtx, RsmUndoRecordT *rsmUndoRec,
    uint32_t blockId, DbMemAddrT *pageInfo, DbInstanceHdT dbInstance);
StatusInter ClusteredHashGetOverFlowPage(
    ChLabelPageMemRunCtxT *pageMemRunCtx, uint32_t blockId, uint32_t overflowPageCount, DbMemAddrT *pageInfo);

ALWAYS_INLINE static Status ChFreePageToMd(
    ChLabelPageMemRunCtxT *pageMemRunCtx, PageIdT pageAddr, RsmUndoRecordT *rsmUndoRec)
{
    DB_POINTER2(pageMemRunCtx->memMgr, pageMemRunCtx->mdMgr);
    uint32_t spaceId = pageMemRunCtx->memMgr->base.tableSpaceIndex;
    FreePageParamT freePageParam = SeInitFreePageParam(spaceId, pageAddr, NULL, rsmUndoRec, SE_INVALID_LABEL_ID, false);
    StatusInter ret = MdFreePage(pageMemRunCtx->mdMgr, &freePageParam);
    if (ret != STATUS_OK_INTER) {
        DB_LOG_ERROR(DbGetExternalErrno(ret), "Unable to free new hash page.deviceId:%" PRIu32 ", blockId:%" PRIu32 "",
            pageAddr.deviceId, pageAddr.blockId);
        return DbGetExternalErrno(ret);
    }
    pageMemRunCtx->memMgr->base.version++;
    return DbGetExternalErrno(ret);
}

StatusInter ClusteredHashFreeAllDirPage(
    ChLabelPageMemRunCtxT *pageMemRunCtx, uint32_t dirPageCount, RsmUndoRecordT *rsmUndoRec, EhTrcPageCacheT *cache);
inline static StatusInter ClusteredHashFreeOneSegPage(
    ChLabelPageMemRunCtxT *pageMemRunCtx, RsmUndoRecordT *rsmUndoRec, PageIdT pageAddr)
{
    DB_POINTER(pageMemRunCtx);
    Status statusRet = ChFreePageToMd(pageMemRunCtx, pageAddr, rsmUndoRec);
    if (SECUREC_UNLIKELY(statusRet != GMERR_OK)) {
        StatusInter ret = DbGetStatusInterErrno(statusRet);
        SE_ERROR(ret, "free segPage (%" PRIu32 ", %" PRIu32 ") unsucc", pageAddr.deviceId, pageAddr.blockId);
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashFreeAllOverFlowPage(
    ChLabelPageMemRunCtxT *pageMemRunCtx, uint32_t overflowPageCount, RsmUndoRecordT *rsmUndoRec);

inline static ChLabelPageHeaderT *ClusteredHashGetPageHead(uint8_t *segPageAddr)
{
    DB_POINTER(segPageAddr);
    return ((ChLabelPageHeaderT *)(void *)(segPageAddr - CHLABEL_PAGE_HEAD_OFFSET));
}

inline static __attribute__((always_inline)) HashDirSegmentT *ClusteredHashGetDirSegmentBySegId(
    ChLabelPageMemRunCtxT *pageMemRunCtx, uint32_t pattern)
{
    uint32_t hashSegNumPerPage = pageMemRunCtx->memMgr->dashMemMeta.hashSegNumPerPage;
    uint32_t dirPageId = pattern / hashSegNumPerPage;
    uint32_t dirSlotId = pattern % hashSegNumPerPage;
    uint8_t *dirPageAddr = NULL;
    StatusInter ret = ClusteredHashGetDirPage(pageMemRunCtx, dirPageId, &dirPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return NULL;
    }
    return (HashDirSegmentT *)(void *)(dirPageAddr + dirSlotId * sizeof(HashDirSegmentT));
}

ALWAYS_INLINE bool ClusteredHashIsPageAddrValid(PageIdT pageAddr)
{
    return (pageAddr.deviceId != DB_INVALID_UINT32) && (pageAddr.blockId != DB_INVALID_UINT32);
}

#ifdef __cplusplus
}
#endif
#endif
