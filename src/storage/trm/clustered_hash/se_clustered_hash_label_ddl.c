/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: se_clustered_hash_label_ddl.c
 * Description: clustered hash label ddl implementation
 * Author: zhangyoujian
 * Create: 2022/8/15
 */
#include "se_clustered_hash_label_ddl.h"
#include "se_clustered_hash_label_upgrade.h"
#include "db_utils.h"
#include "se_heap_page.h"
#include "se_log.h"
#include "se_clustered_hash_rsm.h"

#ifdef __cplusplus
extern "C" {
#endif

static_assert(
    (HASH_ENTRY_PER_BUCKET & (HASH_ENTRY_PER_BUCKET - 1)) == 0, "HASH_ENTRY_PER_BUCKET must be an integer power of 2.");

static void ClusteredHashInitDashMemMeta(ClusteredHashLabelT *label, uint32_t normalBucketCntPerPage)
{
    label->memMgr.dashMemMeta.hashSegBits = DbLog2(normalBucketCntPerPage);
    label->memMgr.dashMemMeta.hashSegNumPerPage = label->dashMeta.availPageSize / (uint32_t)sizeof(HashDirSegmentT);
    label->memMgr.dashMemMeta.hashEntryNumPerPage =
        normalBucketCntPerPage * HASH_ENTRY_PER_BUCKET + HASH_ENTRY_PER_STASH_BUCKET;
    label->memMgr.dashMemMeta.scaleInCnt = 0u;
    label->memMgr.dashMemMeta.dirNextItemIdx = 0u;
    label->memMgr.dashMemMeta.hashEntrySize = (uint16_t)sizeof(ClusteredHashEntryT);
}

static void ClusteredHashInitOverflowInfo(
    DashEhLabelMetaT *dashMeta, uint32_t avaliPageSize, uint32_t tupleSize, uint32_t hashEntrySize)
{
    // 初始化溢出页内存布局参数
    uint32_t maxTupleCountPerOverFlowPage = ClusteredHashGetOptimalOverFlowTupleCount(avaliPageSize, tupleSize);
    DB_ASSERT(maxTupleCountPerOverFlowPage > 0);

    dashMeta->bitmapSizePerOverFlowPage = (uint16_t)ClusteredHashAlign32Bits(maxTupleCountPerOverFlowPage);
    dashMeta->metaData.maxTupleCntPerOverFlowPage = (uint16_t)maxTupleCountPerOverFlowPage;
    dashMeta->metaData.firstEntryPosPerOverFlowPage = dashMeta->bitmapSizePerOverFlowPage;
    dashMeta->metaData.firstTuplePosPerOverFlowPage =
        dashMeta->bitmapSizePerOverFlowPage + maxTupleCountPerOverFlowPage * hashEntrySize;
#ifndef NDEBUG
    dashMeta->scaleInUpgradePageCnt = 0;
#endif
    dashMeta->scaleInProbCnt = 0;
    dashMeta->scaleInPageCnt = 0;
}

static void ClusteredHashInitDashMeta(ClusteredHashLabelT *label, uint32_t normalBucketCntPerPage, uint32_t tupleSize,
    uint32_t avaliPageSize, uint32_t hashEntrySize)
{
    uint16_t tupleCntPerPage =
        (uint16_t)((normalBucketCntPerPage * HASH_ENTRY_PER_BUCKET + HASH_ENTRY_PER_STASH_BUCKET) * MAX_LOAT_FACTOR);
    uint16_t bitMapSize = (uint16_t)ClusteredHashAlign32Bits(tupleCntPerPage);
    uint16_t bloomFilterSize = bitMapSize * (uint16_t)BLOOM_FILTER_BYTES_PER_GROUP;
    DB_ASSERT(bloomFilterSize >= bitMapSize && bloomFilterSize >= (uint16_t)BLOOM_FILTER_BYTES_PER_GROUP);
    uint32_t normalBucketSize = (uint32_t)(sizeof(ChBucketMetaT) + HASH_ENTRY_PER_BUCKET * hashEntrySize);
    uint16_t stashBucketSize = (uint16_t)(sizeof(ChStashBucketMetaT) + HASH_ENTRY_PER_STASH_BUCKET * hashEntrySize);

    DashEhLabelMetaT *dashMeta = &label->dashMeta;
    dashMeta->metaData.pageRowSize = label->labelCfg.fixRowDataSize;
    dashMeta->metaData.nextPageAddr = (PageIdT){DB_INVALID_UINT32, DB_INVALID_UINT32};
    dashMeta->bucketSize = normalBucketSize;
    dashMeta->metaData.tupleSize = tupleSize;
    dashMeta->availPageSize = avaliPageSize;
    dashMeta->metaData.bucketPos = (uint16_t)(bloomFilterSize + bitMapSize);
    dashMeta->metaData.stashBucketPos =
        (uint16_t)(dashMeta->metaData.bucketPos + (uint16_t)(normalBucketSize * normalBucketCntPerPage));
    dashMeta->metaData.tuplePos = (uint16_t)(dashMeta->metaData.stashBucketPos + stashBucketSize);
    // note: tuplePos必须是uint32_t对齐, 因为每个tuple头部的hashcluster的逻辑addr的引用计数是通过32位原子操作更新的
    DB_ASSERT(dashMeta->metaData.tuplePos % BYTES_SIZE_PER_WORD == 0);
    dashMeta->bitMapSize = bitMapSize;
    dashMeta->bloomFilterSize = bloomFilterSize;
    dashMeta->metaData.tupleCntPerPage = tupleCntPerPage;
    dashMeta->metaData.hashNormalBucketNumPerPage = (uint16_t)normalBucketCntPerPage;
    dashMeta->hashEntryPerNormalBucket = HASH_ENTRY_PER_BUCKET;
    dashMeta->reverseCnt = 0;
}

StatusInter InitClusteredHashLabel(ClusteredHashLabelT *label, SeInstanceT *seInstance, const ChLabelCfgT *labelCfg)
{
    // HASH_ENTRY_PER_BUCKET必须为2的整数次幂
    label->labelCfg = *labelCfg;
    uint32_t avaliPageSize = seInstance->seConfig.pageSize * DB_KIBI -
                             ((uint32_t)CHLABEL_PAGE_HEAD_OFFSET + (uint32_t)CHLABEL_PAGE_METADATA_OFFSET);
    label->memMgr.base.seInstanceId = seInstance->instanceId;
    label->memMgr.base.fileId = SeGetNewTrmId(seInstance);
    label->memMgr.base.tableSpaceIndex = labelCfg->tableSpaceIndex;
    label->memMgr.base.isVertexUseRsm = labelCfg->isUseRsm;
    label->memMgr.base.version = 1;  // 版本号从1开始
    label->magicNum = CLUSTERED_HASH_MAGIC_VALID;
    label->shmemCtxId = seInstance->clusteredHashShmMemCtxId;
    DbRWLatchInit(&(label->latch));

    // 每一个tuple的内存布局 slotExtendSize | ChRowHeadT | tupleBuf
    uint32_t tupleSize = SIZE_ALIGN4((labelCfg->slotExtendSize + sizeof(ChRowHeadT) + labelCfg->fixRowDataSize));
    uint32_t maxTupleSize = 0;
    uint32_t normalBucketCntPerPage = ClusteredHashGetOptimalNormalBucketCount(avaliPageSize, tupleSize, &maxTupleSize);
    if (normalBucketCntPerPage < MIN_NORMAL_BUCKET_COUNT) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER,
            "the number of normal bucket is less than %" PRIu32 ", hashNormalBucketNumPerPage: %" PRIu16
            ", cur tupleSize is %" PRIu32 ", max tupleSize is %" PRIu32 ".",
            MIN_NORMAL_BUCKET_COUNT, normalBucketCntPerPage, tupleSize, maxTupleSize);
        return DATA_EXCEPTION_INTER;
    }
    uint32_t hashEntrySize = (uint32_t)sizeof(ClusteredHashEntryT);
    ClusteredHashInitDashMeta(label, normalBucketCntPerPage, tupleSize, avaliPageSize, hashEntrySize);

    ClusteredHashInitOverflowInfo(&label->dashMeta, avaliPageSize, tupleSize, hashEntrySize);
    ClusteredHashInitDashMemMeta(label, normalBucketCntPerPage);
    label->version = 0;
    return STATUS_OK_INTER;
}

static StatusInter ClusteredHashFreeSegAndDirPage(
    ClusteredHashLabelT *label, RsmUndoRecordT *rsmUndoRec, EhTrcPageCacheT *cache);

static StatusInter ClusteredHashBuildOneDirPage(
    ClusteredHashLabelT *label, RsmUndoRecordT *rsmUndoRec, uint32_t blockId, uint32_t initSegCount, uint32_t dirDepth)
{
    ClusteredHashLabelVarInfoT *labelVarInfo = ClusteredHashGetLabelVarInfo(label);
    if (labelVarInfo == NULL) {
        return NULL_VALUE_NOT_ALLOWED_INTER;
    }
    ChLabelPageMemRunCtxT memRunCtx = {0};
    ClusteredHashInitPageMemRunCtx(label, ClusteredHashGetDirPageAddrs(labelVarInfo), &memRunCtx);
    uint8_t *dirPageAddr = NULL;
    StatusInter ret = ClusteredHashAllocDirPage(&memRunCtx, rsmUndoRec, blockId, &dirPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    labelVarInfo->dirPageCount++;
    label->version++;
#ifndef NDEBUG
    DashEhMemMetaT *dashMemMeta = &label->memMgr.dashMemMeta;
    DB_ASSERT(((labelVarInfo->dirCap + dashMemMeta->hashSegNumPerPage - 1) / dashMemMeta->hashSegNumPerPage) >=
              labelVarInfo->dirPageCount);
#endif
    HashDirSegmentT *seg = (HashDirSegmentT *)(void *)dirPageAddr;
    uint32_t hashSegNumPerPage = memRunCtx.memMgr->dashMemMeta.hashSegNumPerPage;
    DbMemAddrT segPageInfo;
    for (uint32_t i = 0; i < initSegCount; i++) {
        ret = ClusteredHashAllocAndInitSegPage(label, &memRunCtx, rsmUndoRec, &segPageInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            // 目录页上对应的seg页逐个申请，中途若失败，后面的seg页也应初始化
            for (uint32_t j = i; j < hashSegNumPerPage; ++j) {
                seg->segDepth = DB_INVALID_UINT32;
                seg->pattern = DB_INVALID_UINT32;
                seg->pageAddr.pageAddr = SE_INVALID_PAGE_ADDR;
                seg += 1;
            }
            // 初始化失败流程不再缓存page
            StatusInter freeRet = ClusteredHashFreeSegAndDirPage(label, rsmUndoRec, NULL);
            DB_ASSERT(freeRet == STATUS_OK_INTER);
            DB_ASSERT(labelVarInfo->dirPageCount == 0);
            return ret;
        }
        seg->segDepth = dirDepth;
        seg->pattern = blockId * hashSegNumPerPage + i;
        seg->pageAddr.pageAddr = segPageInfo.pageAddr;
        seg += 1;
    }
    for (uint32_t i = initSegCount; i < hashSegNumPerPage; ++i) {
        seg->segDepth = 0;
        seg->pattern = 0;
        seg->pageAddr.pageAddr = SE_INVALID_PAGE_ADDR;
        seg += 1;
    }
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashConstructDir(
    ClusteredHashLabelT *label, ClusteredHashLabelVarInfoT *labelVarInfo, RsmUndoRecordT *rsmUndoRec)
{
    DB_POINTER(label);
    DashEhMemMetaT *dashMemMeta = &label->memMgr.dashMemMeta;
    uint32_t hashSegNumPerPage = dashMemMeta->hashSegNumPerPage;
    uint32_t capacity = label->labelCfg.initIndexCap;

    // 创建聚簇容器时已经保证
    DB_ASSERT(hashSegNumPerPage != 0u);
    DB_ASSERT(dashMemMeta->hashEntryNumPerPage != 0u);

    uint32_t dirDepth = DbLog2(capacity / dashMemMeta->hashEntryNumPerPage);
    uint32_t dirCap = (1u << dirDepth);
    DB_ASSERT(labelVarInfo->isConstructed == 0);
    labelVarInfo->dirDepth = dirDepth;
    labelVarInfo->dirCap = dirCap;

    uint32_t fullDirPageCount = dirCap / hashSegNumPerPage;
    uint32_t remainSegPageCount = dirCap % hashSegNumPerPage;
    if ((dirCap - 1u) / hashSegNumPerPage + 1u > label->pageAddrArrCapacity) {
        return INSUFF_RES_INTER;
    }

    label->version++;
    StatusInter ret = STATUS_OK_INTER;
    for (uint32_t i = 0; i < fullDirPageCount; i++) {
        ret = ClusteredHashBuildOneDirPage(label, rsmUndoRec, i, hashSegNumPerPage, dirDepth);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    if (remainSegPageCount > 0) {
        ret = ClusteredHashBuildOneDirPage(label, rsmUndoRec, fullDirPageCount, remainSegPageCount, dirDepth);
    }
    labelVarInfo->isConstructed = (ret == STATUS_OK_INTER) ? 1 : 0;
    return ret;
}

void ClusteredHashResetMetaData(ClusteredHashLabelT *label)
{
    ClusteredHashLabelVarInfoT *labelVarInfo = ClusteredHashGetLabelVarInfo(label);
    if (labelVarInfo == NULL) {
        return;
    }
    labelVarInfo->dirPageCount = 0;
    labelVarInfo->segPageCount = 0;
    labelVarInfo->overflowPageCount = 0;
    labelVarInfo->dirCap = 0;
    labelVarInfo->isConstructed = 0;
    DashEhMemMetaT *dashMemMeta = &label->memMgr.dashMemMeta;
    dashMemMeta->dirCurItemIdx = 0;
    dashMemMeta->dirCurItemCnt = 0;
    PageIdT *dirPagesAddr = ClusteredHashGetDirPageAddrs(labelVarInfo);
    for (uint32_t i = 0; i < label->pageAddrArrCapacity; i++) {
        dirPagesAddr[i] = SE_INVALID_PAGE_ADDR;
    }
    label->keyLen = 0;
    ClusteredHashResetUpgradeVersion(label, labelVarInfo);
}

StatusInter ClusteredHashCreate(const ChLabelCfgT *tableCfg, ShmemPtrT *shmAddr)
{
    DB_POINTER2(shmAddr, tableCfg);
    ShmemPtrT labelShmAddr;
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance((uint16_t)tableCfg->seInstanceId);
    if (seInstance == NULL) {
        SE_ERROR(INVALID_PARAMETER_VALUE_INTER, "storage instance %" PRIu16 " novalid", tableCfg->seInstanceId);
        return INVALID_PARAMETER_VALUE_INTER;
    }
    DbMemCtxT *labelMemCtx = DbGetShmemCtxById(seInstance->clusteredHashShmMemCtxId, seInstance->instanceId);
    if (labelMemCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "clustered hash: hash's mem context is novalid");
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    uint32_t pagePtrArrCapacity;
    HashCalcPagePtrArrCapacity(seInstance, &pagePtrArrCapacity);

    uint32_t labelSize = (uint32_t)sizeof(ClusteredHashLabelT);
    uint32_t totalAllocSize =
        labelSize + (uint32_t)sizeof(ClusteredHashLabelVarInfoT) + pagePtrArrCapacity * (uint32_t)sizeof(ShmemPtrT);
    // 为聚簇容器申请一块共享内存, 在聚簇容器的drop操作中销毁
    ClusteredHashLabelT *label = SeShmAlloc(labelMemCtx, totalAllocSize, &labelShmAddr);
    if (label == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "clustered hash: alloc new cluster hash table unsucc");
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_s((void *)label, totalAllocSize, 0, totalAllocSize);
    label->labelVarInfoPtr = (ShmemPtrT){.offset = labelShmAddr.offset + labelSize, .segId = labelShmAddr.segId};
    label->pageAddrArrCapacity = pagePtrArrCapacity;
    PageIdT *dirPagesAddr =
        (PageIdT *)((void *)label + sizeof(ClusteredHashLabelT) + sizeof(ClusteredHashLabelVarInfoT));
    for (uint32_t i = 0; i < label->pageAddrArrCapacity; i++) {
        dirPagesAddr[i] = SE_INVALID_PAGE_ADDR;
    }
    StatusInter ret = InitClusteredHashLabel(label, seInstance, tableCfg);
    if (ret != STATUS_OK_INTER) {
        DbShmemCtxFree(labelMemCtx, labelShmAddr);
        return ret;
    }
    *shmAddr = labelShmAddr;
    DB_LOG_INFO("clustered hash table created. fileId: %" PRIu32 "", label->memMgr.base.fileId);
    return ret;
}

static StatusInter ClusteredHashGetAndFreeSegPage(ClusteredHashLabelT *chLabel, ChLabelPageMemRunCtxT *memRunCtx,
    HashDirSegmentT *seg, RsmUndoRecordT *rsmUndoRec, EhTrcPageCacheT *cache)
{
    ClusteredHashLabelVarInfoT *labelVarInfo = ClusteredHashGetLabelVarInfo(chLabel);
    if (labelVarInfo == NULL) {
        return NULL_VALUE_NOT_ALLOWED_INTER;
    }
    uint32_t fileId = chLabel->memMgr.base.fileId;
    uint32_t dirCap = labelVarInfo->dirCap;
    uint8_t *segPageAddr = NULL;
    StatusInter ret = ClusteredHashGetSegPage(memRunCtx->mdMgr, seg->pageAddr.pageAddr, fileId, &segPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        DB_LOG_ERROR(ret,
            "clustered hash truncate get seg page unsucc, labelId: %u, "
            "segPageCount: %u, dirCap: %u, upgradeVersion: %u, scaleInPageCnt: %u",
            chLabel->labelCfg.labelId, labelVarInfo->segPageCount, dirCap, labelVarInfo->upgradeVersion,
            chLabel->dashMeta.scaleInPageCnt);
        return ret;
    }
#if !defined(NDEBUG) && !defined(FEATURE_RSMEM)
    ChLabelPageHeaderT *pageHead = ((ChLabelPageHeaderT *)(void *)(segPageAddr - CHLABEL_PAGE_HEAD_OFFSET));
    if (SECUREC_UNLIKELY(fileId != pageHead->baseHead.trmId)) {
        ClusteredHashLabelT tmpChLabel = *chLabel;
        ClusteredHashLabelVarInfoT tmpLabelVarInfo = *labelVarInfo;
        DB_LOG_ERROR(INTERNAL_ERROR_INTER,
            "clustered hash truncate seg page has been freed, labelId: %u, upgradeVersion: %u, page fileId: %u.",
            tmpChLabel.labelCfg.labelId, tmpLabelVarInfo.upgradeVersion, pageHead->baseHead.trmId);
        DB_ASSERT(false);
    }
#endif
    ret = ClusteredHashFreeSegPageChain(memRunCtx, rsmUndoRec, seg, &segPageAddr, cache);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        DB_LOG_ERROR(ret,
            "clustered hash truncate free seg page unsucc, labelId: %u, "
            "segPageCount: %u, dirCap: %u, upgradeVersion: %u, scaleInPageCnt: %u",
            chLabel->labelCfg.labelId, labelVarInfo->segPageCount, dirCap, labelVarInfo->upgradeVersion,
            chLabel->dashMeta.scaleInPageCnt);
        return ret;
    }
    return STATUS_OK_INTER;
}

static StatusInter ClusteredHashFreeAllSegPage(
    ClusteredHashLabelT *chLabel, RsmUndoRecordT *rsmUndoRec, EhTrcPageCacheT *cache)
{
    DB_POINTER(chLabel);
    ClusteredHashLabelVarInfoT *labelVarInfo = ClusteredHashGetLabelVarInfo(chLabel);
    if (labelVarInfo == NULL) {
        return NULL_VALUE_NOT_ALLOWED_INTER;
    }
    ChLabelPageMemRunCtxT memRunCtx = {0};
    ClusteredHashInitPageMemRunCtx(chLabel, ClusteredHashGetDirPageAddrs(labelVarInfo), &memRunCtx);
    uint32_t dirCap = labelVarInfo->dirCap;
    uint8_t *dirPageAddr = NULL;
    uint32_t dirPageCnt = labelVarInfo->dirPageCount;
    uint32_t hashSegNumPerPage = chLabel->memMgr.dashMemMeta.hashSegNumPerPage;
    StatusInter ret = STATUS_OK_INTER;
    for (uint32_t i = 0; i < dirPageCnt; i++) {
        ret = ClusteredHashGetDirPage(&memRunCtx, i, &dirPageAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
        for (uint32_t segSlotId = 0; segSlotId < hashSegNumPerPage; segSlotId++) {
            uint32_t pattern = i * hashSegNumPerPage + segSlotId;
            // pattern 表示每个segment页的序号, segment页的序号不可能超过目录页存储的数据页的总容量大小
            if (pattern >= dirCap) {
                break;
            }
            HashDirSegmentT *seg = (HashDirSegmentT *)(void *)(dirPageAddr + segSlotId * sizeof(HashDirSegmentT));
            if (pattern != seg->pattern) {
                continue;
            }
            ret = ClusteredHashGetAndFreeSegPage(chLabel, &memRunCtx, seg, rsmUndoRec, cache);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                return ret;
            }
        }
    }
    labelVarInfo->segPageCount = 0;
    ClusteredHashResetUpgradeVersion(chLabel, labelVarInfo);
    return ret;
}

static StatusInter ClusteredHashFreeDirPage(
    ClusteredHashLabelT *chLabel, RsmUndoRecordT *rsmUndoRec, EhTrcPageCacheT *cache)
{
    DB_POINTER(chLabel);
    ClusteredHashLabelVarInfoT *labelVarInfo = ClusteredHashGetLabelVarInfo(chLabel);
    if (labelVarInfo == NULL) {
        return NULL_VALUE_NOT_ALLOWED_INTER;
    }
    ChLabelPageMemRunCtxT memRunCtx = {0};
    ClusteredHashInitPageMemRunCtx(chLabel, ClusteredHashGetDirPageAddrs(labelVarInfo), &memRunCtx);
    StatusInter ret = ClusteredHashFreeAllDirPage(&memRunCtx, labelVarInfo->dirPageCount, rsmUndoRec, cache);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_TRUNCATE_VERTEX_TRUNC_CH_FREE_DIR_PAGE);
    labelVarInfo->dirPageCount = 0;
    return STATUS_OK_INTER;
}

inline static StatusInter ClusteredHashFreeSegAndDirPage(
    ClusteredHashLabelT *label, RsmUndoRecordT *rsmUndoRec, EhTrcPageCacheT *cache)
{
    StatusInter ret = ClusteredHashFreeAllSegPage(label, rsmUndoRec, cache);
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_TRUNCATE_VERTEX_TRUNC_CH_FREE_SEG_PAGE);
    return ret == STATUS_OK_INTER ? ClusteredHashFreeDirPage(label, rsmUndoRec, cache) : ret;
}

void ClusteredHashDrop(ShmemPtrT shmAddr, RsmUndoRecordT *rsmUndoRec)
{
    ClusteredHashLabelT *label = (ClusteredHashLabelT *)DbShmPtrToAddr(shmAddr);
    if (label == NULL) {
        SE_ERROR(NULL_VALUE_NOT_ALLOWED_INTER,
            "get hash cluster table unsucc while drop table. segid: %" PRIu32 " offset: %" PRIu32 "", shmAddr.segId,
            shmAddr.offset);
        return;
    }
    ClusteredHashLabelVarInfoT *labelVarInfo = ClusteredHashGetLabelVarInfo(label);
    if (labelVarInfo == NULL) {
        return;
    }
    // free directory page and seg page
    StatusInter ret = ClusteredHashFreeSegAndDirPage(label, rsmUndoRec, NULL);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "free seg&dir page unsucc while drop table. segid: %" PRIu32 " offset: %" PRIu32 "",
            shmAddr.segId, shmAddr.offset);
        return;
    }
    ret = ClusteredHashDestroyArray(label, rsmUndoRec);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "destroy array unsucc while drop table. segid: %" PRIu32 " offset: %" PRIu32 "", shmAddr.segId,
            shmAddr.offset);
        return;
    }
    label->magicNum = CLUSTERED_HASH_MAGIC_INVALID;
    void *labelMemCtx = DbGetShmemCtxById(label->shmemCtxId, DbGetProcGlobalId());
    if (labelMemCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR,
            "Clustered-Hash, HCIN: hash's mem context is novalid, shmemCtxId:%" PRIu32 ".", label->shmemCtxId);
        return;
    }
    DbShmemCtxFree(labelMemCtx, shmAddr);
    return;
}

void ClusteredHashReconstruct(
    ClusteredHashLabelT *label, EhTrcPageCacheT *cache, ClusteredHashLabelVarInfoT *labelVarInfo)
{
    DB_ASSERT(cache->cnt == EH_DIR_AND_SEG_PAGE);
    ChLabelPageMemRunCtxT memRunCtx = {0};
    ClusteredHashInitPageMemRunCtx(label, ClusteredHashGetDirPageAddrs(labelVarInfo), &memRunCtx);
    uint8_t *dirPage = NULL;
    StatusInter ret = MdGetPage(memRunCtx.mdMgr, cache->pageId[0], &dirPage, ENTER_PAGE_NORMAL, false);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return;
    }
    dirPage += CHLABEL_PAGE_HEAD_OFFSET;
    uint8_t *segPage;
    ret = MdGetPage(memRunCtx.mdMgr, cache->pageId[1], &segPage, ENTER_PAGE_NORMAL, false);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return;
    }
    segPage += CHLABEL_PAGE_HEAD_OFFSET;
    labelVarInfo->dirPageCount = 1;
    labelVarInfo->segPageCount = 1;
    labelVarInfo->dirCap = 1;
    labelVarInfo->dirDepth = 0;
    memRunCtx.pageAddr[0] = cache->pageId[0];
    HashDirSegmentT *seg = (HashDirSegmentT *)(void *)dirPage;
    seg->segDepth = 0;
    seg->pattern = 0;
    seg->pageAddr.pageAddr = cache->pageId[1];
    for (uint32_t i = 1; i < memRunCtx.memMgr->dashMemMeta.hashSegNumPerPage; ++i) {
        seg += 1;
        seg->segDepth = 0;
        seg->pattern = 0;
        seg->pageAddr.pageAddr = SE_INVALID_PAGE_ADDR;
    }
    ClusteredHashInitSegPage(label, labelVarInfo, segPage, 0);
    label->version++;
    labelVarInfo->isConstructed = 1;
}

void ClusteredHashTruncate(ShmemPtrT shmAddr, RsmUndoRecordT *rsmUndoRec)
{
    ClusteredHashLabelT *label = (ClusteredHashLabelT *)DbShmPtrToAddr(shmAddr);
    if (label == NULL) {
        SE_ERROR(NULL_VALUE_NOT_ALLOWED_INTER,
            "get clustered hash label unsucc while truncate table. segid: %" PRIu32 " offset: %" PRIu32 "",
            shmAddr.segId, shmAddr.offset);
        return;
    }
    if (label->magicNum != CLUSTERED_HASH_MAGIC_VALID) {
        SE_ERROR(INTERNAL_ERROR_INTER, "clustered hash label has been dropped.");
        return;
    }
    ClusteredHashLabelVarInfoT *labelVarInfo = ClusteredHashGetLabelVarInfo(label);
    if (labelVarInfo == NULL) {
        return;
    }
    label->memMgr.base.version++;
    if (!labelVarInfo->isConstructed) {
        return;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_TRUNCATE_VERTEX_TRUNC_CH_CACHE_ENABLE);
    EhTrcPageCacheT cache = EH_TRC_PAGE_CACHE_INIT_VALUE;
    // free directory page and seg page
    StatusInter ret = ClusteredHashFreeSegAndDirPage(label, rsmUndoRec, &cache);
    if (ret != STATUS_OK_INTER) {
        return;
    }
    ret = ClusteredHashResetArray(label, rsmUndoRec);
    if (ret != STATUS_OK_INTER) {
        return;
    }
    ClusteredHashResetMetaData(label);
    (void)memset_s(&label->perfStat, sizeof(label->perfStat), 0, sizeof(label->perfStat));
    labelVarInfo->tupleUsed = 0;
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_TRUNCATE_VERTEX_TRUNC_CH_RESET_DATA);
    ClusteredHashReconstruct(label, &cache, labelVarInfo);
    DB_LOG_INFO("SE-clustered hash label, Truncate filed:%" PRIu32 "", label->memMgr.base.fileId);
}

StatusInter ChLabelInitRunHdlWhenFirstOpen(
    SeTrxContainerCtxT *trxCntrCtx, const ChLabelRunCtxT *runCtx, const TrxT *trx)
{
    trxCntrCtx->chTrxCtx.ctxHead.type = TRX_CLUSTERED_HASH_HANDLE;
    trxCntrCtx->chTrxCtx.chShmAddr = runCtx->openCfg.labelShmAddr;
    trxCntrCtx->chTrxCtx.pKIndexKeyCmpFunc = runCtx->openCfg.keyCmp;
    trxCntrCtx->chTrxCtx.labelLatchVersionId = runCtx->openCfg.labelLatchVersionId;
    trxCntrCtx->chTrxCtx.labelRWLatch = runCtx->openCfg.labelRWLatch;
    TrxSetContainerIsUsed(trxCntrCtx);
    DB_POINTER(trx->trxPeriodMemCtx);
    if (trxCntrCtx->chTrxCtx.chRunCtx != NULL) {
        trxCntrCtx->chTrxCtx.chRunCtx->openCfg.vertexLabel = trxCntrCtx->chTrxCtx.vertexLabel;
        trxCntrCtx->chTrxCtx.chRunCtx->openCfg.vertex = trxCntrCtx->chTrxCtx.vertex;
        trxCntrCtx->chTrxCtx.chRunCtx->openCfg.usrMemCtx = trx->trxPeriodMemCtx;
        trxCntrCtx->chTrxCtx.chRunCtx->openCfg.userData = NULL;
        trxCntrCtx->chTrxCtx.chRunCtx->openCfg.indexKeyInfo = NULL;
        trxCntrCtx->chTrxCtx.chRunCtx->openCfg.secIndexNum = runCtx->openCfg.secIndexNum;
        trxCntrCtx->chTrxCtx.chRunCtx->openCfg.secIdxCtx = runCtx->openCfg.secIdxCtx;
        trxCntrCtx->chTrxCtx.chRunCtx->openCfg.isUseRsm = runCtx->openCfg.isUseRsm;
        trxCntrCtx->chTrxCtx.chRunCtx->openCfg.tupleBuf = runCtx->openCfg.tupleBuf;
        trxCntrCtx->chTrxCtx.chRunCtx->labelVarInfo = runCtx->labelVarInfo;
        trxCntrCtx->chTrxCtx.chRunCtx->dirPagesAddr = runCtx->dirPagesAddr;
        return STATUS_OK_INTER;
    }
    ChLabelOpenCfgT config = {
        .containerType = CONTAINER_CLUSTERED_HASH,
        .labelShmAddr = trxCntrCtx->chTrxCtx.chShmAddr,
        .seRunCtx = trx->seRunCtx,
        .vertexLabel = trxCntrCtx->chTrxCtx.vertexLabel,
        .vertex = trxCntrCtx->chTrxCtx.vertex,
        .tupleBuf = runCtx->openCfg.tupleBuf,
        .userData = NULL,
        .indexKeyInfo = NULL,
        .secIndexNum = runCtx->openCfg.secIndexNum,
        .secIdxCtx = runCtx->openCfg.secIdxCtx,
        .keyCmp = trxCntrCtx->chTrxCtx.pKIndexKeyCmpFunc,
        .labelLatchVersionId = trxCntrCtx->chTrxCtx.labelLatchVersionId,
        .labelRWLatch = trxCntrCtx->chTrxCtx.labelRWLatch,
        .isUseRsm = runCtx->openCfg.isUseRsm,
    };
    Status status = ChLabelAllocAndInitRunCtx(&config, &trxCntrCtx->chTrxCtx.chRunCtx);
    if (status != GMERR_OK) {
        TrxSetContainerNotUsed(trxCntrCtx);
        SE_ERROR(status, "TrxId(%" PRIu64 ") alloc and init clustered hash runCtx unsucc.", trx->base.trxId);
        return DbGetStatusInterErrno(status);
    }
    trxCntrCtx->chTrxCtx.chRunCtx->openCfg.usrMemCtx = trx->trxPeriodMemCtx;
    return STATUS_OK_INTER;
}

static StatusInter ClusteredHashSaveTrxInfo(ChLabelRunCtxT *runCtx)
{
    DB_POINTER2(runCtx, runCtx->openCfg.seRunCtx);
    SeRunCtxT *seRunCtx = runCtx->openCfg.seRunCtx;
    TrxT *trx = (TrxT *)seRunCtx->trx;
    if (SECUREC_UNLIKELY(trx->base.state == TRX_STATE_NOT_STARTED)) {
        SE_LAST_ERROR(TRANS_MODE_MISMATCH_INTER, "novalid trx %" PRIu64 " state %" PRId32, trx->base.trxId,
            (int32_t)trx->base.state);
        return TRANS_MODE_MISMATCH_INTER;
    }
    if (trx->base.state == TRX_STATE_ACTIVE) {
        DmVertexLabelT *vtxLabel = (DmVertexLabelT *)runCtx->openCfg.vertexLabel;
        if (SECUREC_UNLIKELY(vtxLabel == NULL)) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "null vertexlabel found.");
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        SeTrxContainerCtxT *trxCntrCtx = NULL;
        bool isNewCtx = false;
        StatusInter ret = TrxStoreContainerCtx(trx, runCtx->chLabel->labelCfg.labelId, &trxCntrCtx, &isNewCtx);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "get clustered hash container unsucc.");
            return ret;
        }
        runCtx->chTrxCtx = &trxCntrCtx->chTrxCtx;
        // 因为表降级的原因，labelId不会变（事务性容器会复用），但是vertexLabel发生了变化，这里每次都刷新一下
        trxCntrCtx->chTrxCtx.vertexLabel = vtxLabel;
        trxCntrCtx->chTrxCtx.vertex = runCtx->openCfg.vertex;
        if (isNewCtx) {
            return ChLabelInitRunHdlWhenFirstOpen(trxCntrCtx, runCtx, trx);
        }
        trxCntrCtx->chTrxCtx.chRunCtx->openCfg.tupleBuf = runCtx->openCfg.tupleBuf;
        trxCntrCtx->chTrxCtx.chRunCtx->openCfg.vertex = runCtx->openCfg.vertex;
    }
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashOpen(ChLabelRunCtxT *runCtx, HpOpTypeE opType)
{
    DB_POINTER(runCtx);
    if (opType == HEAP_OPTYPE_NORMALREAD) {
        return STATUS_OK_INTER;
    }
    StatusInter ret = ClusteredHashSaveTrxInfo(runCtx);
#ifndef NDEBUG
    if (ret == STATUS_OK_INTER) {
        DbRsmKernelEntry(DB_RSM_KERNEL_CLUSTERED_HASH_DML);
    }
#endif
    return ret;
}

inline Status ChLabelCreate(const ChLabelCfgT *tableCfg, ShmemPtrT *shmAddr, ShmemPtrT *containerInfo)
{
    StatusInter ret = STATUS_OK_INTER;
    if (tableCfg->isUseRsm) {
        ret = ClusteredHashRsmCreate(tableCfg, shmAddr, containerInfo, false);
    } else {
        ret = ClusteredHashCreate(tableCfg, shmAddr);
    }
    return DbGetExternalErrno(ret);
}

inline void ChLabelDrop(ShmemPtrT shmAddr, RsmUndoRecordT *rsmUndoRec)
{
    ClusteredHashDrop(shmAddr, rsmUndoRec);
}

inline void ChLabelTruncate(ShmemPtrT shmAddr, RsmUndoRecordT *rsmUndoRec)
{
    ClusteredHashTruncate(shmAddr, rsmUndoRec);
}

Status ChLabelOpen(ChLabelRunHdlT chRunHdl, void *vertex, HpOpTypeE opType, DbMemCtxT *usrMemCtx)
{
    DB_POINTER3(chRunHdl, vertex, usrMemCtx);
    ClusteredHashLabelT *clusterHashLabel = chRunHdl->chLabel;
    ClusteredHashLabelVarInfoT *labelVarInfo = ClusteredHashGetLabelVarInfo(clusterHashLabel);
    if (SECUREC_UNLIKELY(labelVarInfo == NULL)) {
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    chRunHdl->openCfg.usrMemCtx = usrMemCtx;
    chRunHdl->openCfg.vertex = vertex;
    if (SECUREC_UNLIKELY(labelVarInfo->isConstructed == 0)) {
        DbRWLatchW(&clusterHashLabel->latch);
        if (labelVarInfo->isConstructed == 0) {  // double check
            StatusInter ret = ClusteredHashConstructDir(
                clusterHashLabel, labelVarInfo, ((TrxT *)(chRunHdl->seRunCtx->trx))->liteTrx.rsmUndoRec);
            if (ret != STATUS_OK_INTER) {
                DbRWUnlatchW(&clusterHashLabel->latch);
                return ret;
            }
        }
        DbRWUnlatchW(&clusterHashLabel->latch);
    }
    return DbGetExternalErrno(ClusteredHashOpen(chRunHdl, opType));
}

Status ChLabelAllocRunCtx(ChLabelRunHdlT *chRunHdl, SeRunCtxHdT seRunCtxHd)
{
    DB_POINTER(chRunHdl);
    // 申请聚簇容器运行上下文，在断连的时候配对释放
    ChLabelRunCtxT *runCtx = DbDynMemCtxAlloc(seRunCtxHd->sessionMemCtx, sizeof(ChLabelRunCtxT));
    if (runCtx == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "hash cluster alloc runCtx unsucc.");
        return GMERR_OUT_OF_MEMORY;
    }
    *runCtx = (ChLabelRunCtxT){0};
    runCtx->seRunCtx = seRunCtxHd;
    *chRunHdl = runCtx;
    return GMERR_OK;
}

static StatusInter ChLabelRunCtxInitLabelPtr(ChLabelRunCtxT *runCtx, const ChLabelOpenCfgT *openCfg)
{
    DB_POINTER2(runCtx, openCfg);
    runCtx->chLabel = DbShmPtrToAddr(openCfg->labelShmAddr);
    if (runCtx->chLabel == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "get clustered hash label unsucc. segid: %" PRIu32 " offset: %" PRIu32 "", openCfg->labelShmAddr.segId,
            openCfg->labelShmAddr.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    ClusteredHashLabelT *chLabel = runCtx->chLabel;
    if (!chLabel->labelCfg.isUseRsm) {
        runCtx->labelVarInfo = (ClusteredHashLabelVarInfoT *)(void *)(chLabel + 1);
    } else {
        runCtx->labelVarInfo = ClusteredHashGetLabelVarInfo(chLabel);
        if (runCtx->labelVarInfo == NULL) {
            return NULL_VALUE_NOT_ALLOWED_INTER;
        }
    }
    runCtx->dirPagesAddr = (PageIdT *)(void *)(runCtx->labelVarInfo + 1);
    return STATUS_OK_INTER;
}

Status ChLabelRunCtxInit(const ChLabelOpenCfgT *openCfg, ChLabelRunHdlT chRunHdl)
{
    DB_POINTER3(openCfg, chRunHdl, openCfg->labelRWLatch);
    // 先判断锁版本号, 回滚流程如果发现锁版本号已经更新说明在DML操作过程中发生了删表操作
    if (LabelLatchCheckVersion(openCfg->labelRWLatch, openCfg->labelLatchVersionId) != GMERR_OK) {
        SE_LAST_ERROR(GMERR_UNDEFINED_TABLE_INTER, "clustered hash label has been dropped.");
        return GMERR_UNDEFINED_TABLE;
    }
    StatusInter ret = ChLabelRunCtxInitLabelPtr(chRunHdl, openCfg);
    if (ret != STATUS_OK_INTER) {
        return DbGetExternalErrno(ret);
    }
    ClusteredHashLabelT *clusterHashLabel = chRunHdl->chLabel;
    if (clusterHashLabel->labelCfg.fixRowDataSize <= (uint32_t)sizeof(TupleAddr)) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "novalid fixRowSize: %" PRIu32, clusterHashLabel->labelCfg.fixRowDataSize);
        return GMERR_DATA_EXCEPTION;
    }
    chRunHdl->openCfg = *openCfg;
    chRunHdl->seRunCtx = openCfg->seRunCtx;
    chRunHdl->isPageReadOnly = false;
    chRunHdl->isHoldHcIndexLockByUpper = openCfg->labelRWLatch->isAddHcLatch;
    chRunHdl->isDegrade = false;
    if (!chRunHdl->seRunCtx->resSessionCtx.isDirectRead) {
        chRunHdl->newSchemaVersionLength = DbCommonIsServer() ? DmGetFixVertexLabelLen4QE(openCfg->vertexLabel) :
                                                                DmGetFixVertexLabelLen(openCfg->vertexLabel);
    }
    chRunHdl->isBatchOperation = false;
    chRunHdl->heapTuple = (HeapTupleBufT){0, NULL};
    chRunHdl->cachedHeapTuple = (HeapTupleBufT){0, NULL};
    chRunHdl->partialsStoreSize = chRunHdl->chLabel->labelCfg.fixRowDataSize - (uint32_t)sizeof(TupleAddr);
    chRunHdl->chTrxCtx = NULL;
    chRunHdl->pageCtx.dirSeg = NULL;
    chRunHdl->containerType = CONTAINER_CLUSTERED_HASH;
#ifndef NDEBUG
    chRunHdl->lockPage = NULL;
#endif
    ClusteredHashInitPageMemRunCtx(chRunHdl->chLabel, chRunHdl->dirPagesAddr, &chRunHdl->memRunCtx);
    return GMERR_OK;
}

inline void ChLabelRunCtxReset(ChLabelRunHdlT chRunHdl)
{
    chRunHdl->isPageReadOnly = false;
    chRunHdl->isBatchOperation = false;
    chRunHdl->chTrxCtx = NULL;
    if (chRunHdl->seRunCtx != NULL && chRunHdl->seRunCtx->resSessionCtx.isDirectWrite) {
        return;
    }
    chRunHdl->openCfg.usrMemCtx = NULL;
}

Status ChLabelAllocAndInitRunCtx(const ChLabelOpenCfgT *openCfg, ChLabelRunHdlT *chRunHdl)
{
    DB_POINTER2(openCfg, chRunHdl);
    // ChLabelAllocRunCtx内申请聚簇容器运行上下文，在断连的时候配对释放
    Status ret = ChLabelAllocRunCtx(chRunHdl, openCfg->seRunCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = ChLabelRunCtxInit(openCfg, *chRunHdl);
    if (ret != GMERR_OK) {
        ChLabelReleaseRunCtx(*chRunHdl);
        *chRunHdl = NULL;
    }
    return ret;
}

void ChLabelReleaseRunCtx(ChLabelRunHdlT chRunHdl)
{
    ClusteredHashFreeCtxTupleBuf(chRunHdl);
    ClusteredHashFreeCachedTupleBuf(chRunHdl);
    DbMemCtxT *sessionMemCtx = chRunHdl->seRunCtx->sessionMemCtx;
    DbDynMemCtxFree(sessionMemCtx, chRunHdl);
    // no use after free, local variable
}

bool ChLabelCheckCreateCondition(uint32_t tupleBufSize, uint32_t slotExtendSize)
{
    uint16_t instanceId = DbGetProcGlobalId();
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(instanceId);
    if (seInstance == NULL) {
        SE_ERROR(INVALID_PARAMETER_VALUE_INTER, "storage instance %" PRIu16 " novalid", instanceId);
        return false;
    }
    uint32_t avaliPageSize = seInstance->seConfig.pageSize * DB_KIBI - (uint32_t)CHLABEL_PAGE_HEAD_OFFSET;
    uint32_t tupleSize = SIZE_ALIGN4((slotExtendSize + sizeof(ChRowHeadT) + tupleBufSize));
    uint32_t maxTupleSize = 0;
    uint32_t optimalBucketCnt = ClusteredHashGetOptimalNormalBucketCount(avaliPageSize, tupleSize, &maxTupleSize);
    if (optimalBucketCnt < MIN_NORMAL_BUCKET_COUNT) {
        DB_LOG_WARN(GMERR_DATA_EXCEPTION,
            "clustered hash, Label tupleSize is too large, cur tupleSize is %" PRIu32 ", max tupleSize is %" PRIu32 ".",
            tupleSize, maxTupleSize);
        return false;
    }
    return true;
}

#ifdef __cplusplus
}
#endif
