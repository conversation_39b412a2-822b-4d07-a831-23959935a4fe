/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: se_clustered_hash_label_upgrade.c
 * Description: clustered hash label upgrade implementation
 * Author: lujiahao
 * Create: 2023/09/22
 */
#include "se_clustered_hash_label_upgrade.h"
#include "se_clustered_hash_label_stat.h"
#include "se_clustered_hash_access_dm.h"
#include "se_clustered_hash_rsm.h"

#ifdef __cplusplus
extern "C" {
#endif

ALWAYS_INLINE_C StatusInter ClusteredHashCheckPageRowSize(
    ChLabelRunCtxT *runCtx, HashDirSegmentT *dirSeg, DbMemAddrT *segAddr)
{
    DB_POINTER3(runCtx, dirSeg, segAddr);
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataWithAddr(runCtx, segAddr->virtAddr);
    if (SECUREC_UNLIKELY(runCtx->newSchemaVersionLength != pageData->pageRowSize)) {
        PageSizeT oldRowDataSize;
        bool flag = runCtx->chLabel->labelCfg.fixRowDataSize != runCtx->newSchemaVersionLength;
        if (flag) {
            oldRowDataSize = runCtx->chLabel->labelCfg.fixRowDataSize;
            runCtx->chLabel->labelCfg.fixRowDataSize = (uint16_t)runCtx->newSchemaVersionLength;
            DB_ASSERT(runCtx->labelVarInfo->upgradeVersion != DB_INVALID_UINT16);
            runCtx->labelVarInfo->upgradeVersion++;
        }
        StatusInter ret = ClusteredHashAllocNewPage4MulVersion(runCtx, dirSeg, segAddr);
        if (ret != STATUS_OK_INTER) {
            if (flag) {
                runCtx->labelVarInfo->upgradeVersion--;
                runCtx->chLabel->labelCfg.fixRowDataSize = oldRowDataSize;
            }
            return ret;
        }
        if (runCtx->labelVarInfo->upgradeVersion == DB_INVALID_UINT16) {
            runCtx->labelVarInfo->upgradeVersion = 1;
            runCtx->chLabel->dashMeta.reverseCnt++;
        }
    }
    return STATUS_OK_INTER;
}

static void ClusteredHashUpdateDirPage(ChLabelRunCtxT *runCtx, PageIdT oldPageAddr, DbMemAddrT segPageInfo)
{
    // 探测并修改dirPage指向
    uint8_t *dirPageAddr = NULL;
    uint32_t dirPageCnt = runCtx->labelVarInfo->dirPageCount;
    uint32_t hashSegNumPerPage = runCtx->chLabel->memMgr.dashMemMeta.hashSegNumPerPage;
    for (uint32_t i = 0; i < dirPageCnt; i++) {
        StatusInter ret = ClusteredHashGetDirPage(&runCtx->memRunCtx, i, &dirPageAddr);
        DB_ASSERT(ret == STATUS_OK_INTER);
        for (uint32_t segSlotId = 0; segSlotId < hashSegNumPerPage; segSlotId++) {
            HashDirSegmentT *seg = (HashDirSegmentT *)(void *)(dirPageAddr + segSlotId * sizeof(HashDirSegmentT));
            if (seg->pageAddr.pageAddr.deviceId == oldPageAddr.deviceId &&
                seg->pageAddr.pageAddr.blockId == oldPageAddr.blockId) {
                seg->pageAddr.pageAddr.deviceId = segPageInfo.pageAddr.deviceId;
                SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_EXPAND_UPDATE_SEG_DEVICE_ID);
                seg->pageAddr.pageAddr.blockId = segPageInfo.pageAddr.blockId;
                SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_EXPAND_UPDATE_SEG_BLOCK_ID);
            }
        }
    }
}

static StatusInter ClusteredHashChangeToTableWriteLock(
    ChLabelRunCtxT *runCtx, bool isWriteLockFlag, bool isHoldHcIndexLockFlag)
{
    if (!isWriteLockFlag) {
        if (isHoldHcIndexLockFlag) {
            ClusteredHashWUnLockHcIndexByUpper(runCtx);
        }
        ClusteredHashTableUnLock(runCtx, true);
        StatusInter ret = ClusteredHashTableLock(runCtx, false);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            // 加锁失败说明在重新上锁的间隙表已经被删除,新页newPageData也会删除；此时不应再对hashcluster索引上锁，直接返回
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

static StatusInter ClusteredHashChangeToTableReadLock(
    ChLabelRunCtxT *runCtx, bool isWriteLockFlag, bool isHoldHcIndexLockFlag)
{
    if (!isWriteLockFlag) {
        ClusteredHashTableUnLock(runCtx, false);
        StatusInter ret = ClusteredHashTableLock(runCtx, true);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            // 加锁失败说明在重新上锁的间隙表已经被删除, 此时不应再对hashcluster索引上锁，应该直接返回
            return ret;
        }
        if (isHoldHcIndexLockFlag) {
            // 上层持有了hashcluster索引的锁，需要重新加回去
            ClusteredHashWLockHcIndexByUpper(runCtx);
        }
    }
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashAllocNewPage4MulVersion(ChLabelRunCtxT *runCtx, HashDirSegmentT *dirSeg, DbMemAddrT *segAddr)
{
    DB_POINTER3(runCtx, dirSeg, segAddr);
    StatusInter ret;
#ifdef SYS32BITS
    ret = ClusteredHashGetAllDirPages(runCtx->chLabel);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
#endif
    // 在申请新页前记录RsmUndo，因为申请新页会修改数据页dfx信息
    PageIdT oldPageAddr = dirSeg->pageAddr.pageAddr;
    ret = ClusteredHashMulVersionExpandUndoLog(runCtx, dirSeg, oldPageAddr);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    DbMemAddrT segPageInfo;
    ret = ClusteredHashAllocAndInitSegPage(
        runCtx->chLabel, &runCtx->memRunCtx, ((TrxT *)(runCtx->seRunCtx->trx))->liteTrx.rsmUndoRec, &segPageInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret, "unable to alloc new segPage, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_EXPAND_BEFORE);
    // 旧页挂载至新页pageData中
    ChLabelPageMetaDataT *newPageData = ClusteredHashGetPageMetaData(segPageInfo.virtAddr);
    ClusteredHashSetPageMetaData(newPageData, runCtx->chLabel, runCtx->labelVarInfo, runCtx->newSchemaVersionLength);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_EXPAND_SET_CHAIN);
    newPageData->nextPageAddr = oldPageAddr;
    bool isWriteLockFlag = DbRWLatchIsWriteLock(&runCtx->openCfg.labelRWLatch->rwlatch);
    bool isHoldHcIndexLockFlag = runCtx->isHoldHcIndexLockByUpper;
    ret = ClusteredHashChangeToTableWriteLock(runCtx, isWriteLockFlag, isHoldHcIndexLockFlag);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_EXPAND_ADD_LOCK);
    // 将所有指向旧页的dir改为指向新页
    ClusteredHashUpdateDirPage(runCtx, oldPageAddr, segPageInfo);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_EXPAND_UPDATE_DIR);
    // 校验dir指向与新页一致
    DB_ASSERT(dirSeg->pageAddr.pageAddr.deviceId == segPageInfo.pageAddr.deviceId &&
              dirSeg->pageAddr.pageAddr.blockId == segPageInfo.pageAddr.blockId);
    *segAddr = segPageInfo;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_EXPAND_SET_OUTPUT);
    runCtx->chLabel->version++;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_EXPAND_FINISHED);
    ret = ClusteredHashChangeToTableReadLock(runCtx, isWriteLockFlag, isHoldHcIndexLockFlag);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    ClusteredHashRsmUndoLogMovePrevRec(runCtx);
    ClusteredHashInitHdr(runCtx, &runCtx->pageCtx);
    return STATUS_OK_INTER;
}

ALWAYS_INLINE_C StatusInter ClusteredHashGetNextPage(ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, PageIdT pageAddr)
{
    DB_POINTER2(runCtx, pageCtx);
    pageCtx->segAddr.pageAddr = pageAddr;
    StatusInter ret = ClusteredHashGetSegPage(
        runCtx->memRunCtx.mdMgr, pageAddr, runCtx->chLabel->memMgr.base.fileId, &pageCtx->segAddr.virtAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret,
            "unable to get clustered hash seg page when fast fetch page, deviceId = %" PRIu32 ", blockId = %" PRIu32
            ", labelId: %" PRIu32 ", indexId: %" PRIu32,
            pageAddr.deviceId, pageAddr.blockId, runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    ClusteredHashInitHdr(runCtx, pageCtx);
    ClusteredHashInitPageOffset(runCtx, pageCtx);
    DB_LOG_INFO("clustered hash, get next page succ, deviceId = %" PRIu32 ", blockId = %" PRIu32 ", labelId: %" PRIu32
                ", indexId: %" PRIu32,
        pageAddr.deviceId, pageAddr.blockId, runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
    return STATUS_OK_INTER;
}

ALWAYS_INLINE_C StatusInter ClusteredHashFindEntryInPageChain(ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx)
{
    StatusInter ret;
    ChLabelPageMetaDataT *pageData = NULL;
    // 遍历page链表探测数据
    do {
        ret = ClusteredHashFindHashEntry(runCtx, lookUpCtx);
        if (SECUREC_LIKELY(lookUpCtx->isFind || ret != STATUS_OK_INTER || runCtx->labelVarInfo->upgradeVersion == 0)) {
            break;
        }
        pageData = ClusteredHashGetPageMetaDataWithAddr(runCtx, runCtx->pageCtx.segAddr.virtAddr);
        if (ClusteredHashIsInvalidPageId(pageData->nextPageAddr)) {
            break;
        }
        ret = ClusteredHashGetNextPage(runCtx, &runCtx->pageCtx, pageData->nextPageAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
        pageData = ClusteredHashGetPageMetaDataByPageCtx(&runCtx->pageCtx);
    } while (true);
    return ret;
}

StatusInter ClusteredHashFindEntryInPageChain4MulVersion(ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx)
{
    // 插入流程需缓存pageCtx，因为升降级流程需插入第一页，ClusteredHashFindEntryInPageChain会修改pageCtx
    ChLabelPageCtxT cachePageCtx = runCtx->pageCtx;
    StatusInter ret = ClusteredHashFindEntryInPageChain(runCtx, lookUpCtx);
    // 恢复pageCtx
    runCtx->pageCtx = cachePageCtx;
    return ret;
}

void ClusteredHashRollBackPageChainSplit(ChLabelRunCtxT *runCtx, ChLabelExpandCtxT *expandCtx)
{
    DB_POINTER2(runCtx, expandCtx);
    ClusteredHashLabelVarInfoT *labelVarInfo = runCtx->labelVarInfo;
    PageIdT pageAddr = expandCtx->newPageCtx.segAddr.pageAddr;
    ChLabelPageMetaDataT *pageData =
        ClusteredHashGetPageMetaDataWithAddr(runCtx, expandCtx->newPageCtx.segAddr.virtAddr);
    do {
        StatusInter ret = ClusteredHashFreeOneSegPage(
            &runCtx->memRunCtx, ((TrxT *)(runCtx->seRunCtx->trx))->liteTrx.rsmUndoRec, pageAddr);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_ROLLBACK_FREE_SEG);
        DB_ASSERT(ret == STATUS_OK_INTER);  // 归还segment页预期一定成功，失败需要定位
        labelVarInfo->segPageCount--;
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_ROLLBACK_UPDATE_SEG_CNT);
        ClusteredHashResetUpgradeVersion(runCtx->chLabel, labelVarInfo);
        if (SECUREC_LIKELY(ClusteredHashIsInvalidPageId(pageData->nextPageAddr))) {
            break;
        }
        DB_ASSERT(labelVarInfo->segPageCount > 0);
        pageAddr = pageData->nextPageAddr;
        pageData = ClusteredHashGetPageMetaDataByPageId(&runCtx->memRunCtx, pageData->nextPageAddr);
    } while (true);
}

StatusInter ClusteredHashPageChainSplit(ChLabelRunCtxT *runCtx, ChLabelExpandCtxT *expandCtx)
{
    DB_POINTER2(runCtx, expandCtx);
    ClusteredHashLabelVarInfoT *labelVarInfo = runCtx->labelVarInfo;
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataWithAddr(runCtx, runCtx->pageCtx.segAddr.virtAddr);
    ChLabelPageMetaDataT *lastPageData = NULL;
    bool isAddrValid = true;
    do {
        DbMemAddrT pageInfo;
        StatusInter ret = ClusteredHashAllocSegPage(&runCtx->memRunCtx,
            ((TrxT *)(runCtx->seRunCtx->trx))->liteTrx.rsmUndoRec, labelVarInfo->segPageCount, &pageInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_LAST_ERROR(ret, "unable to alloc segment page for rehash, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            if (expandCtx->newPageCtx.segAddr.virtAddr != NULL) {
                // 释放申请成功的page
                ClusteredHashRollBackPageChainSplit(runCtx, expandCtx);
            }
            return ret;
        }
        ClusteredHashRsmSplitInitPage(runCtx, pageInfo.virtAddr);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE);
        ChLabelPageMetaDataT *newPageData = ClusteredHashGetPageMetaData(pageInfo.virtAddr);
        *newPageData = *pageData;
        newPageData->nextPageAddr = (PageIdT){DB_INVALID_UINT32, DB_INVALID_UINT32};
        if (expandCtx->newPageCtx.segAddr.virtAddr == NULL) {
            expandCtx->newPageCtx.segAddr = pageInfo;
            lastPageData = newPageData;
        } else {
            DB_POINTER(lastPageData);
            lastPageData->nextPageAddr = pageInfo.pageAddr;
            lastPageData = newPageData;
        }
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE);
        labelVarInfo->segPageCount++;
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE_COUNT);
        if (SECUREC_LIKELY(ClusteredHashIsInvalidPageId(pageData->nextPageAddr))) {
            break;
        }
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_ALLOC_NEXT_SEGPAGE_BEFORE);
        isAddrValid = ClusteredHashIsPageAddrValid(pageData->nextPageAddr);
        pageData = ClusteredHashGetPageMetaDataByPageId(&runCtx->memRunCtx, pageData->nextPageAddr);
    } while (isAddrValid && pageData != NULL);
    if (SECUREC_UNLIKELY(isAddrValid && pageData == NULL && expandCtx->newPageCtx.segAddr.virtAddr != NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "unable to get segment page for rehash, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        ClusteredHashRollBackPageChainSplit(runCtx, expandCtx);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashPageChainBucketsSplit(
    ChLabelRunCtxT *runCtx, ChLabelExpandCtxT *expandCtx, uint32_t patternSpan)
{
    DB_POINTER2(runCtx, expandCtx);
    ClusteredHashLabelVarInfoT *labelVarInfo = runCtx->labelVarInfo;
    ChLabelPageMetaDataT *oldPageData = ClusteredHashGetPageMetaDataWithAddr(runCtx, runCtx->pageCtx.segAddr.virtAddr);
    ChLabelPageMetaDataT *newPageData =
        ClusteredHashGetPageMetaDataWithAddr(runCtx, expandCtx->newPageCtx.segAddr.virtAddr);
    uint32_t copyLength = runCtx->chLabel->dashMeta.availPageSize;
    uint32_t fileId = runCtx->chLabel->memMgr.base.fileId;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_BEFORE);
    do {
        errno_t err = memcpy_s(expandCtx->newPageCtx.segAddr.virtAddr + (uint32_t)CHLABEL_PAGE_METADATA_OFFSET,
            copyLength, runCtx->pageCtx.segAddr.virtAddr + (uint32_t)CHLABEL_PAGE_METADATA_OFFSET, copyLength);
        DB_ASSERT(err == EOK);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_MEMCPY);
        if (runCtx->pageCtx.dirSeg->segDepth <= labelVarInfo->dirDepth) {
            // 处理old segment page
            ClusteredHashProcSegment(runCtx, &runCtx->pageCtx, patternSpan, true);
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_PROC_OLD_PAGE);
        }
        // 处理new segment page
        ClusteredHashProcSegment(runCtx, &expandCtx->newPageCtx, patternSpan, false);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_PROC_NEW_PAGE);
        if (SECUREC_LIKELY(ClusteredHashIsInvalidPageId(oldPageData->nextPageAddr))) {
            break;
        }
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_PROC_NEXT_PAGE);
        // 指向下一页后，需要重新初始化pageCtx
        runCtx->pageCtx.segAddr.pageAddr = oldPageData->nextPageAddr;
        StatusInter ret = ClusteredHashGetSegPage(
            runCtx->memRunCtx.mdMgr, oldPageData->nextPageAddr, fileId, &runCtx->pageCtx.segAddr.virtAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
        ClusteredHashInitHdr(runCtx, &runCtx->pageCtx);
        ClusteredHashInitPageOffset(runCtx, &runCtx->pageCtx);
        expandCtx->newPageCtx.segAddr.pageAddr = newPageData->nextPageAddr;
        ret = ClusteredHashGetSegPage(
            runCtx->memRunCtx.mdMgr, newPageData->nextPageAddr, fileId, &expandCtx->newPageCtx.segAddr.virtAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
        ClusteredHashInitHdr(runCtx, &expandCtx->newPageCtx);
        ClusteredHashInitPageOffset(runCtx, &expandCtx->newPageCtx);
        oldPageData = ClusteredHashGetPageMetaDataByPageId(&runCtx->memRunCtx, oldPageData->nextPageAddr);
        newPageData = ClusteredHashGetPageMetaDataByPageId(&runCtx->memRunCtx, newPageData->nextPageAddr);
    } while (true);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_AFTER);
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashProcOldPageChain(ChLabelRunCtxT *runCtx)
{
    DB_POINTER(runCtx);
    uint32_t patternSpan = (uint32_t)1 << (runCtx->pageCtx.dirSeg->segDepth - 1);
    // 缓存pageCtx
    ChLabelPageCtxT tmpPageCtx = runCtx->pageCtx;
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataWithAddr(runCtx, runCtx->pageCtx.segAddr.virtAddr);
    uint32_t fileId = runCtx->chLabel->memMgr.base.fileId;
    do {
        // 处理old segment page
        ClusteredHashProcSegment(runCtx, &runCtx->pageCtx, patternSpan, true);
        if (SECUREC_LIKELY(ClusteredHashIsInvalidPageId(pageData->nextPageAddr))) {
            break;
        }
        // 指向下一页后，需要重新初始化pageCtx
        runCtx->pageCtx.segAddr.pageAddr = pageData->nextPageAddr;
        StatusInter ret = ClusteredHashGetSegPage(
            runCtx->memRunCtx.mdMgr, pageData->nextPageAddr, fileId, &runCtx->pageCtx.segAddr.virtAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, get segment page unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
        ClusteredHashInitHdr(runCtx, &runCtx->pageCtx);
        pageData = ClusteredHashGetPageMetaDataByPageId(&runCtx->memRunCtx, pageData->nextPageAddr);
    } while (true);
    runCtx->pageCtx = tmpPageCtx;
    return STATUS_OK_INTER;
}

ALWAYS_INLINE_C static StatusInter ClusteredHashDeleteByRowId4Upgrade(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx)
{
    ChLabelPageCtxT newPageCtx = runCtx->pageCtx;
    ChLabelLookupProcCtxT procCtx;
    procCtx.opType = CHLABEL_OPT_DELETE;
    procCtx.isLogicalTuple = true;
    procCtx.isRead = false;
    procCtx.deleteCtx = (ChLabelDeleteCtxT){NULL};
    StatusInter ret =
        ClusteredHashLookupByLogicRowId(runCtx, lookUpCtx->logicRowId, &procCtx, ClusteredEntryEraseWithoutLock);
    // arm32下可能prefetch失败
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "clustered hash look by logic rowid, tuple id: %d", lookUpCtx->logicRowId.tupleId);
    }
    runCtx->pageCtx = newPageCtx;
    return ret;
}

StatusInter ClusteredHashInsert4MulVersion(
    ChLabelRunCtxT *runCtx, IndexKeyT idxKey, const HeapTupleBufT *tupleBuf, ChLogicRowId *logicRowId)
{
    DB_POINTER3(runCtx, tupleBuf, logicRowId);
    runCtx->chLabel->keyLen = idxKey.keyLen;
    runCtx->chLabel->perfStat.chInsertCnt++;

    ClusteredHashStatInsert(runCtx);
    ChLabelInsertCtxT insertCtx =
        ClusteredHashInitInsertCtx(idxKey, tupleBuf->buf, tupleBuf->bufSize, ClusteredHashGet26BitHashCode(idxKey));
    insertCtx.isMulVersion = true;
    insertCtx.logicRowId = *logicRowId;
    insertCtx.phyRowId = *(ChPhyRowId *)(void *)logicRowId;
    return ClusteredHashInsertProc(runCtx, &insertCtx, logicRowId);
}

StatusInter ClusteredHashInsert4MulVersionRollBack(
    ChLabelRunCtxT *runCtx, IndexKeyT idxKey, const HeapTupleBufT *tupleBuf, ChLabelLookupCtxT *lookUpCtx)
{
    DB_POINTER2(runCtx, tupleBuf);
    runCtx->chLabel->keyLen = idxKey.keyLen;
    runCtx->chLabel->perfStat.chInsertCnt++;
    ChLabelInsertCtxT insertCtx =
        ClusteredHashInitInsertCtx(idxKey, tupleBuf->buf, tupleBuf->bufSize, ClusteredHashGet26BitHashCode(idxKey));
    insertCtx.phyRowId.version = lookUpCtx->phyRowId.version;
    insertCtx.phyRowId.tupleId = lookUpCtx->phyRowId.tupleId;
    insertCtx.logicRowId.version = lookUpCtx->logicRowId.version;
    insertCtx.logicRowId.tupleId = lookUpCtx->logicRowId.tupleId;
    insertCtx.isMulVersion = true;
    StatusInter ret = ClusteredHashInitPageCtxAndOffset(runCtx, &runCtx->pageCtx, insertCtx.hashCode);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret, "get segment page unsucc. (hashCode: %" PRIu32 "), labelId: %" PRIu32 ", indexId: %" PRIu32,
            insertCtx.hashCode, runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataWithAddr(runCtx, runCtx->pageCtx.segAddr.virtAddr);
    // 遍历page链表插入数据
    do {
        if (pageData->pageRowSize >= insertCtx.bufSize) {
            ClusteredHashStatInsert(runCtx);
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_ROLLBACK_MULVERSION_UPDATE_STAT);
            ret = ClusteredHashInsertWithoutLookUp(runCtx, idxKey, &insertCtx);
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_ROLLBACK_MULVERSION_INSERT);
            if (ret == STATUS_OK_INTER && !insertCtx.needExpand) {
                break;
            } else {
                // 插入失败，需要将ret置成错误值
                insertCtx.needExpand = false;
                ret = INTERNAL_ERROR_INTER;
            }
            if (ClusteredHashIsInvalidPageId(pageData->nextPageAddr)) {
                break;
            }
        }
        DB_ASSERT(pageData->nextPageAddr.deviceId != DB_INVALID_UINT32 &&
                  pageData->nextPageAddr.blockId != DB_INVALID_UINT32);
        ret = ClusteredHashGetNextPage(runCtx, &runCtx->pageCtx, pageData->nextPageAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
        pageData = ClusteredHashGetPageMetaDataByPageCtx(&runCtx->pageCtx);
    } while (true);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_ROLLBACK_MULVERSION_FINISHED);
    DB_ASSERT(ret == STATUS_OK_INTER);
    return ret;
}

void ClusteredHashHandleInsertResult4MulVersion(ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, IndexKeyT idxKey,
    HeapTupleBufT *rollbackTupleBuf, StatusInter ret)
{
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "clustered hash, upgrade proc insert data unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        StatusInter returnRet = ClusteredHashInsert4MulVersionRollBack(runCtx, idxKey, rollbackTupleBuf, lookUpCtx);
        if (returnRet != STATUS_OK_INTER) {
            // 回滚数据预期不会失败
            SE_LAST_ERROR(ret,
                "clustered hash, upgrade rollback delete unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            DB_ASSERT(false);
        }
    }
}

StatusInter ClusteredHashAllocKeyData(ChLabelRunCtxT *runCtx, IndexKeyT *idxKey, ChRowHeadT *rowHead, uint8_t **keyData)
{
    *keyData = DB_MALLOC(idxKey->keyLen + rowHead->totalBufSize);
    if (*keyData == NULL) {
        SE_ERROR(OUT_OF_MEMORY_INTER,
            "clustered hash, alloc tmp memory unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return OUT_OF_MEMORY_INTER;
    }
    errno_t err = memcpy_s(*keyData, idxKey->keyLen, idxKey->keyData, idxKey->keyLen);
    DB_ASSERT(err == EOK);
    // 准备回滚tuple
    uint8_t *buf = *keyData + idxKey->keyLen;
    err = memcpy_s(buf, rowHead->totalBufSize, (uint8_t *)(rowHead + 1), rowHead->totalBufSize);
    DB_ASSERT(err == EOK);
    return STATUS_OK_INTER;
}

StatusInter ClusteredUpdate4MulVersion(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelUpdateCtxT *updateCtx, ChRowHeadT *rowHead)
{
    StatusInter ret;
    IndexKeyT idxKey = {0};
    if (lookUpCtx->isLookUpByKey) {
        idxKey = lookUpCtx->idxKey;
    } else {
        ret = ClusteredHashGetIndexKey((const DmVertexT *)runCtx->openCfg.vertex, runCtx->chLabel->labelCfg.indexId,
            (DmIndexKeyBufInfoT **)&runCtx->openCfg.indexKeyInfo, (uint8_t *)(rowHead + 1), &idxKey);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "clustered hash extract pk from tupleBuf unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
#ifndef NDEBUG
        lookUpCtx->idxKey = idxKey;
#endif
    }
    uint8_t *keyData = NULL;
    ret = ClusteredHashAllocKeyData(runCtx, &idxKey, rowHead, &keyData);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    HeapTupleBufT rollbackTupleBuf = {.bufSize = rowHead->totalBufSize, .buf = keyData + idxKey.keyLen};
    uint64_t phyItemNum = runCtx->chLabel->perfStat.phyItemNum;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_MULVERSION_BEFORE_DELETE);
    // 先执行删除操作
    ret = ClusteredHashDeleteByRowId4Upgrade(runCtx, lookUpCtx);
    if (ret != STATUS_OK_INTER) {
        DB_FREE(keyData);
        SE_ERROR(ret, "clustered hash del by rowid for upgrade, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_MULVERSION_DELETE);
    DB_ASSERT(phyItemNum == runCtx->chLabel->perfStat.phyItemNum + 1);
    // 后执行插入步骤
    HeapTupleBufT tupleBuf = {.bufSize = updateCtx->bufSize, .buf = updateCtx->buf};
    ChLogicRowId logicRowId = lookUpCtx->logicRowId;
    idxKey.keyData = keyData;
#ifndef NDEBUG
    HtHashCodeT hashCode = ClusteredHashGet26BitHashCode(idxKey);
    DB_ASSERT(hashCode == lookUpCtx->logicRowId.hashCode);
#endif
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_MULVERSION_BEFORE_INSERT);
    ret = ClusteredHashInsert4MulVersion(runCtx, idxKey, &tupleBuf, &logicRowId);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_MULVERSION_INSERT);
    ClusteredHashHandleInsertResult4MulVersion(runCtx, lookUpCtx, idxKey, &rollbackTupleBuf, ret);
    DB_ASSERT(phyItemNum == runCtx->chLabel->perfStat.phyItemNum);
    DB_FREE(keyData);
    return ret;
}

StatusInter ClusteredUpdateRollBack4MulVersion(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelUpdateCtxT *updateCtx, ChRowHeadT *oldRowHead)
{
    // 升降级更新回滚
    // 先执行删除步骤
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_ROLLBACK_MULVERSION_BEFORE);
    ChLabelLookupProcCtxT procCtx;
    procCtx.opType = CHLABEL_OPT_DELETE;
    procCtx.isLogicalTuple = true;
    procCtx.isRead = false;
    procCtx.deleteCtx = (ChLabelDeleteCtxT){NULL};
    StatusInter ret = ClusteredEntryEraseWithoutLock(runCtx, lookUpCtx, &procCtx);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_ROLLBACK_MULVERSION_DELETE);
    // 删除数据预期不会失败，失败一定是代码问题
    DB_ASSERT(ret == STATUS_OK_INTER);
    // 再执行插入步骤
    IndexKeyT idxKey = {0};
    ret = ClusteredHashGetIndexKey((const DmVertexT *)runCtx->openCfg.vertex, runCtx->chLabel->labelCfg.indexId,
        (DmIndexKeyBufInfoT **)&runCtx->openCfg.indexKeyInfo, (uint8_t *)(oldRowHead + 1), &idxKey);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret,
            "clustered hash extract primary key from tupleBuf unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    HeapTupleBufT tupleBuf = {.bufSize = oldRowHead->totalBufSize, .buf = (uint8_t *)(oldRowHead + 1)};
    return ClusteredHashInsert4MulVersionRollBack(runCtx, idxKey, &tupleBuf, lookUpCtx);
}

// 判断segPage chainB是不是segPage chainA的子串，并返回chainA的匹配chainB的pageHead
bool ClusteredHashIsChainMatch(ChLabelRunCtxT *runCtx, ChLabelPageHeaderT *pageHeadB, ChLabelPageHeaderT *pageHeadA,
    ChLabelPageHeaderT **targetPageHead, uint8_t **targetSegPageAddr)

{
    ChLabelPageMetaDataT *pageDataA;
    ChLabelPageMetaDataT *pageDataB = ClusteredHashGetPageMetaDataWithAddr(runCtx, (uint8_t *)(pageHeadA + 1));
    *targetPageHead = pageHeadA;
    ChLabelPageMetaDataT *cachePageDataB = NULL;
    do {
        pageDataA = ClusteredHashGetPageMetaDataWithAddr(runCtx, (uint8_t *)(pageHeadB + 1));
        cachePageDataB = pageDataB;
        do {
            if (pageDataA->pageRowSize != pageDataB->pageRowSize) {
                break;
            }
            if (SECUREC_LIKELY(ClusteredHashIsInvalidPageId(pageDataA->nextPageAddr))) {
                return true;
            }
            pageDataA = ClusteredHashGetPageMetaDataByPageId(&runCtx->memRunCtx, pageDataA->nextPageAddr);
            if (ClusteredHashIsInvalidPageId(pageDataB->nextPageAddr)) {
                *targetSegPageAddr = NULL;
                return false;
            }
            pageDataB = ClusteredHashGetPageMetaDataByPageId(&runCtx->memRunCtx, pageDataB->nextPageAddr);
        } while (true);
        if (ClusteredHashIsInvalidPageId(cachePageDataB->nextPageAddr)) {
            *targetSegPageAddr = NULL;
            return false;
        }
        *targetPageHead = ClusteredHashGetPageHeadAndPageAddrByPageId(
            &runCtx->memRunCtx, cachePageDataB->nextPageAddr, targetSegPageAddr);
        pageDataB = ClusteredHashGetPageMetaDataByPageId(&runCtx->memRunCtx, cachePageDataB->nextPageAddr);
    } while (*targetPageHead != NULL);
    return false;
}

inline static bool ClusteredHashSegPairTupleUsageBelowThreshold(
    ChLabelPageHeaderT *pageHeadA, ChLabelPageHeaderT *pageHeadB)
{
    ChLabelPageMetaDataT pageDataA = *ClusteredHashGetPageMetaData((uint8_t *)(pageHeadA + 1));
    ChLabelPageMetaDataT pageDataB = *ClusteredHashGetPageMetaData((uint8_t *)(pageHeadB + 1));
    DB_ASSERT(pageDataA.tupleCntPerPage == pageDataB.tupleCntPerPage);
    uint16_t threshold = pageDataA.tupleCntPerPage >> 1;
    return pageDataA.tupleCntPerPage - pageHeadA->freeSlotCount < threshold &&
           pageDataB.tupleCntPerPage - pageHeadB->freeSlotCount < threshold;
}

#ifndef NDEBUG
void ClusteredHashJudgePageAlloc(ChLabelRunCtxT *runCtx, ChLabelSegmentPageT *segmentA, ChLabelSegmentPageT *segmentB)
{
    if (runCtx->labelVarInfo->upgradeVersion == 0) {
        // 一次性申请2个页
        runCtx->judgePageA = malloc(DEBUG_PAGE_SIZE * 2);
        if (runCtx->judgePageA != NULL) {
            runCtx->judgePageB = runCtx->judgePageA + DEBUG_PAGE_SIZE;
            (void)memcpy_s(runCtx->judgePageA, DEBUG_PAGE_SIZE, segmentA->pageHead, DEBUG_PAGE_SIZE);
            (void)memcpy_s(runCtx->judgePageB, DEBUG_PAGE_SIZE, segmentB->pageHead, DEBUG_PAGE_SIZE);
        }
    }
}
#endif

// 判断segmentB是否满足条件合入segmentA
bool ClusteredHashIsPageChainSatisfy(ChLabelRunCtxT *runCtx, ChLabelPageHeaderT *targetPageHead,
    uint8_t *targetSegPageAddr, ChLabelSegmentPageT *segmentB, ChLabelSegmentPageT *segmentA)
{
    bool flag = false;
    ChLabelPageHeaderT *sourcePageHead = segmentB->pageHead;
    uint8_t *sourceSegPageAddr = segmentB->segPageAddr;
    ChLabelPageMetaDataT sourcePageData;
    ChLabelPageMetaDataT targetPageData;
    do {
        // 判断tuple使用率是否达到缩容门槛
        if (!ClusteredHashSegPairTupleUsageBelowThreshold(sourcePageHead, targetPageHead)) {
            flag = false;
            break;
        }
        sourcePageData = *ClusteredHashGetPageMetaData(sourceSegPageAddr);
        targetPageData = *ClusteredHashGetPageMetaData(targetSegPageAddr);
        // 快速判断：如果其中一页为空，那么一定可以缩容
        if (sourcePageHead->freeSlotCount == sourcePageData.tupleCntPerPage ||
            targetPageHead->freeSlotCount == targetPageData.tupleCntPerPage) {
            flag = true;
            if (SECUREC_LIKELY(ClusteredHashIsInvalidPageId(sourcePageData.nextPageAddr))) {
#ifndef NDEBUG
                ClusteredHashJudgePageAlloc(runCtx, segmentA, segmentB);
#endif
                break;
            }
            sourcePageHead = ClusteredHashGetPageHeadAndPageAddrByPageId(
                &runCtx->memRunCtx, sourcePageData.nextPageAddr, &sourceSegPageAddr);
            targetPageHead = ClusteredHashGetPageHeadAndPageAddrByPageId(
                &runCtx->memRunCtx, targetPageData.nextPageAddr, &targetSegPageAddr);
            continue;
        }
        // 详细检查缩容条件（逻辑上按照和实际处理一样的方式预判断，但不实际进行搬移等操作）
        if (ClusteredHashSegPairCanScaleIn(
                runCtx, &sourcePageData, &sourceSegPageAddr, &targetPageData, &targetSegPageAddr)) {
            flag = true;
#ifndef NDEBUG
            ClusteredHashJudgePageAlloc(runCtx, segmentA, segmentB);
#endif
            if (SECUREC_LIKELY(ClusteredHashIsInvalidPageId(sourcePageData.nextPageAddr))) {
                break;
            }
            targetPageHead = ClusteredHashGetPageHeadAndPageAddrByPageId(
                &runCtx->memRunCtx, targetPageData.nextPageAddr, &targetSegPageAddr);
            sourcePageHead = ClusteredHashGetPageHeadAndPageAddrByPageId(
                &runCtx->memRunCtx, sourcePageData.nextPageAddr, &sourceSegPageAddr);
            continue;
        }
        flag = false;
        break;
    } while (true);
    return (flag && ClusteredHashIsInvalidPageId(sourcePageData.nextPageAddr));
}

static void ClusteredHashScaleInFreeSegPage(ChLabelRunCtxT *runCtx, PageIdT pageAddr)
{
    ClusteredHashLabelVarInfoT *labelVarInfo = runCtx->labelVarInfo;
    StatusInter ret = ClusteredHashFreeOneSegPage(
        &runCtx->memRunCtx, ((TrxT *)(runCtx->seRunCtx->trx))->liteTrx.rsmUndoRec, pageAddr);
    DB_ASSERT(labelVarInfo->segPageCount > 0);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_SCALE_IN_FREE_EMPTY_PAGE);
    labelVarInfo->segPageCount--;
    ClusteredHashResetUpgradeVersion(runCtx->chLabel, labelVarInfo);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Unable to free seg page after scale in, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return;
    }
}

static void ClusteredHashScaleInUpdateDir(ChLabelSegmentPageT *segmentA, ChLabelSegmentPageT *segmentB)
{
    HashDirSegmentT *segA = segmentA->dirSeg;
    HashDirSegmentT *segB = segmentB->dirSeg;
    // 缩容后对应字典entry的深度减1，同时二者指向同一个页
    if (segA->pattern > segB->pattern) {
        segA->pattern = segB->pattern;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_SCALE_IN_UPDATE_PATTERN);
    segA->segDepth--;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_SCALE_IN_UPDATE_SEG_DEPTH);
    *segB = *segA;
}

void ClusteredHashFreeMergeSegment(
    ChLabelRunCtxT *runCtx, ChLabelSegmentPageT *segmentA, ChLabelSegmentPageT *segmentB, PageIdT pageAddr)
{
    DB_POINTER4(runCtx, segmentA, segmentB, segmentA->dirSeg);
    if (SECUREC_LIKELY(runCtx->labelVarInfo->upgradeVersion == 0)) {
        StatusInter ret = ClusteredHashFreeEmptyUndoLog(runCtx, segmentA->dirSeg, segmentB->dirSeg);
        if (ret != STATUS_OK_INTER) {
            DB_LOG_ERROR(ret, "Record free empty page undo log unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return;
        }
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_SCALE_IN_FREE_EMPTY_PAGE_BEFORE);
        ClusteredHashScaleInFreeSegPage(runCtx, pageAddr);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_SCALE_IN_UPDATE_DIR_BEFORE);
        ClusteredHashScaleInUpdateDir(segmentA, segmentB);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_SCALE_IN_FREE_EMPTY_PAGE_END);
        ClusteredHashRsmUndoLogMovePrevRec(runCtx);
    } else {
        HashDirSegmentT dirSeg = *segmentB->dirSeg;
        ChLabelPageCtxT pageCtx = {0};
        pageCtx.segAddr = (DbMemAddrT){segmentB->segPageAddr, segmentB->pageHead->baseHead.addr};
        pageCtx.pageMetaData = *ClusteredHashGetPageMetaData(segmentB->segPageAddr);

        if (runCtx->isExpand && segmentB->pageHead->freeSlotCount != pageCtx.pageMetaData.tupleCntPerPage) {
            return;
        }

        ClusteredHashFreeEmptyPage(runCtx, &pageCtx, dirSeg);
    }
}

bool ClusteredHashSegmentMerge(ChLabelRunCtxT *runCtx, ChLabelPageMergeCtxT *mergeCtx,
    const ChLabelPageMetaDataT *dstPageData, const ChLabelPageMetaDataT *srcPageDataB)
{
    uint8_t *orgDstPage = (uint8_t *)ClusteredHashGetPageHead(mergeCtx->dstSeg->segPageAddr);
    uint8_t *orgSrcPage = (uint8_t *)ClusteredHashGetPageHead(mergeCtx->srcSeg->segPageAddr);
    if (mergeCtx->dstPageBackup != NULL && mergeCtx->srcPageBackup != NULL) {
        (void)memcpy_s(mergeCtx->dstPageBackup, mergeCtx->pageSize, orgDstPage, mergeCtx->pageSize);
        (void)memcpy_s(mergeCtx->srcPageBackup, mergeCtx->pageSize, orgSrcPage, mergeCtx->pageSize);
    }
    StatusInter ret = ClusteredHashMergeHorizontalUndoLog(runCtx, mergeCtx->dstSeg, mergeCtx->srcSeg);
    if (ret != STATUS_OK_INTER) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE,
            "Record horizontal segment merge undo log unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return false;
    }
    if (ClusteredHashSegmentTryMerge(runCtx, mergeCtx->dstSeg, mergeCtx->srcSeg, dstPageData, srcPageDataB)) {
        ClusteredHashRsmUndoLogMovePrevRec(runCtx);
        if (runCtx->labelVarInfo->upgradeVersion == 0) {
            ClusteredHashFreeMergeSegment(
                runCtx, mergeCtx->dstSeg, mergeCtx->srcSeg, mergeCtx->srcSeg->dirSeg->pageAddr.pageAddr);
        }
        return true;
    }
    if (mergeCtx->dstPageBackup != NULL && mergeCtx->srcPageBackup != NULL) {
        (void)memcpy_s(orgDstPage, mergeCtx->pageSize, mergeCtx->dstPageBackup, mergeCtx->pageSize);
        (void)memcpy_s(orgSrcPage, mergeCtx->pageSize, mergeCtx->srcPageBackup, mergeCtx->pageSize);
    }
    ClusteredHashRsmUndoLogMovePrevRec(runCtx);
    return false;
}

static void ClusteredHashFreePageChainAndUpdateDir(
    ChLabelRunCtxT *runCtx, PageIdT pageAddr, HashDirSegmentT *srcDirSeg, HashDirSegmentT *dstDirSeg)
{
    PageIdT targetPageAddr = pageAddr;
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataByPageId(&runCtx->memRunCtx, targetPageAddr);
    if (SECUREC_UNLIKELY(pageData == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "unable to get page metadata, deviceId: %" PRIu32 ", blockId: %" PRIu32, targetPageAddr.deviceId,
            targetPageAddr.blockId);
        return;
    }
    // 遍历page链表清空数据
    do {
        PageIdT nextTargetPageAddr = pageData->nextPageAddr;
        StatusInter ret = ClusteredHashFreeOneSegPage(
            &runCtx->memRunCtx, ((TrxT *)(runCtx->seRunCtx->trx))->liteTrx.rsmUndoRec, targetPageAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_LAST_ERROR(ret, "Clustered hash free seg page unsucc.");
            DB_ASSERT(false);
            return;
        }
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_SCALE_IN_FREE_PAGE_CHAIN_HALF);
        runCtx->labelVarInfo->segPageCount--;
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_SCALE_IN_FREE_PAGE_CHAIN_UPDATE_COUNT);
        if (SECUREC_LIKELY(ClusteredHashIsInvalidPageId(nextTargetPageAddr))) {
            break;
        }
        targetPageAddr = nextTargetPageAddr;
        pageData = ClusteredHashGetPageMetaDataByPageId(&runCtx->memRunCtx, targetPageAddr);
    } while (true);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_SCALE_IN_UPDATE_DIR_BEFORE);
    ClusteredHashFixSegAfterRecycle(runCtx, srcDirSeg, dstDirSeg);
}

void ClusteredHashMergePageChainRollBack(ChLabelRunCtxT *runCtx, HashDirSegmentT *srcDirSeg, HashDirSegmentT *dstDirSeg,
    PageIdT dstTargetPageAddr, uint32_t pageSize)
{
    // 取源链表第一个页
    uint8_t *curSrcSegPageAddr;
    StatusInter ret = ClusteredHashGetSegPage(
        runCtx->memRunCtx.mdMgr, srcDirSeg->pageAddr.pageAddr, runCtx->chLabel->memMgr.base.fileId, &curSrcSegPageAddr);
    DB_ASSERT(ret == STATUS_OK_INTER);

    // 取目标链表第一个页
    uint8_t *curDstSegPageAddr;
    ret = ClusteredHashGetSegPage(
        runCtx->memRunCtx.mdMgr, dstTargetPageAddr, runCtx->chLabel->memMgr.base.fileId, &curDstSegPageAddr);
    DB_ASSERT(ret == STATUS_OK_INTER);
    ChLabelPageHeaderT *srcPageHead = ((ChLabelPageHeaderT *)(void *)(curSrcSegPageAddr - CHLABEL_PAGE_HEAD_OFFSET));
    ChLabelPageMetaDataT *srcPageData = NULL;
    ChLabelPageMetaDataT *dstPageData = NULL;
    uint32_t availPageSize = pageSize - CHLABEL_PAGE_HEAD_OFFSET - CHLABEL_PAGE_METADATA_OFFSET;
    while (srcPageHead->freeSlotCount == DB_INVALID_UINT16) {
        // 将已经完成缩容的目标链表页数据拷贝至源链表的页中
        errno_t err = memcpy_s((uint8_t *)(curSrcSegPageAddr + CHLABEL_PAGE_METADATA_OFFSET), availPageSize,
            (uint8_t *)(curDstSegPageAddr + CHLABEL_PAGE_METADATA_OFFSET), availPageSize);
        DB_ASSERT(err == EOK);
        // 划分数据至不同页
        DB_ASSERT(srcDirSeg->segDepth == dstDirSeg->segDepth);
        uint32_t patternSpan = 1u << (srcDirSeg->segDepth - 1);
        // 处理src page
        ChLabelPageCtxT srcPageCtx;
        srcPageCtx.segAddr.virtAddr = curSrcSegPageAddr;
        ClusteredHashInitHdr(runCtx, &srcPageCtx);
        ClusteredHashProcSegment(runCtx, &srcPageCtx, patternSpan, (patternSpan & srcDirSeg->pattern) == 0);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_SCALE_IN_ROLLBACK_SRC_PAGE);
        // 处理dst page
        ChLabelPageCtxT dstPageCtx;
        dstPageCtx.segAddr.virtAddr = curDstSegPageAddr;
        ClusteredHashInitHdr(runCtx, &dstPageCtx);
        ClusteredHashProcSegment(runCtx, &dstPageCtx, patternSpan, (patternSpan & dstDirSeg->pattern) == 0);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_SCALE_IN_ROLLBACK_DST_PAGE);
        // 维护计数
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_SCALE_IN_ROLLBACK_UPDATE_COUNT);
        runCtx->chLabel->dashMeta.scaleInPageCnt--;
        // 取下一页
        srcPageData = ClusteredHashGetPageMetaDataWithAddr(runCtx, curSrcSegPageAddr);
        if (SECUREC_LIKELY(ClusteredHashIsInvalidPageId(srcPageData->nextPageAddr))) {
            break;
        }
        ret = ClusteredHashGetSegPage(runCtx->memRunCtx.mdMgr, srcPageData->nextPageAddr,
            runCtx->chLabel->memMgr.base.fileId, &curSrcSegPageAddr);
        DB_ASSERT(ret == STATUS_OK_INTER);
        dstPageData = ClusteredHashGetPageMetaDataWithAddr(runCtx, curDstSegPageAddr);
        ret = ClusteredHashGetSegPage(runCtx->memRunCtx.mdMgr, dstPageData->nextPageAddr,
            runCtx->chLabel->memMgr.base.fileId, &curDstSegPageAddr);
        DB_ASSERT(ret == STATUS_OK_INTER);
        srcPageHead = ((ChLabelPageHeaderT *)(void *)((uint8_t *)curSrcSegPageAddr - CHLABEL_PAGE_HEAD_OFFSET));
        if (srcPageHead->freeSlotCount != DB_INVALID_UINT16) {
            break;
        }
    }
}

static void ClusteredHashUpdatePageChainAfterMerge4Rsm(
    ChLabelRunCtxT *runCtx, ChLabelPageMergeCtxT *mergeCtx, bool isScaleInSucc)
{
    if (isScaleInSucc) {
        mergeCtx->rsmUndoChMulVersionScaleInRec->type = CLUSTERED_HASH_SCALE_IN_SUCC;
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_SCALE_IN_FREE_PAGE_CHAIN_BEFORE);
        // 升降级场景统一释放所有缩容页并维护dir
        ClusteredHashFreePageChainAndUpdateDir(
            runCtx, mergeCtx->srcSeg->dirSeg->pageAddr.pageAddr, mergeCtx->srcSeg->dirSeg, mergeCtx->dstSeg->dirSeg);
    } else {
        mergeCtx->rsmUndoChMulVersionScaleInRec->type = CLUSTERED_HASH_SCALE_IN_ROLLBACK;
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_SCALE_IN_ROLLBACK_BEFORE);
        if (mergeCtx->targetSegPageAddr == mergeCtx->dstSeg->segPageAddr) {
            return;
        }
        // 升降级场景缩容失败，需要回滚已经缩容完成的页
        PageIdT dstTargetPageAddr = (PageIdT){mergeCtx->rsmUndoChMulVersionScaleInRec->targetDeviceId,
            mergeCtx->rsmUndoChMulVersionScaleInRec->targetBlockId};
        ClusteredHashMergePageChainRollBack(
            runCtx, mergeCtx->srcSeg->dirSeg, mergeCtx->dstSeg->dirSeg, dstTargetPageAddr, mergeCtx->pageSize);
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_SCALE_IN_FINISHED);
    mergeCtx->rsmUndoChMulVersionScaleInRec->type = CLUSTERED_HASH_SCALE_IN_FINISHED;
}

static void ClusteredHashUpdatePageChainAfterMerge(
    ChLabelRunCtxT *runCtx, ChLabelPageMergeCtxT *mergeCtx, bool isScaleInSucc)
{
    if (isScaleInSucc) {
        // 升降级场景统一释放所有缩容页并维护dir
        ClusteredHashFreePageChainAndUpdateDir(
            runCtx, mergeCtx->srcSeg->dirSeg->pageAddr.pageAddr, mergeCtx->srcSeg->dirSeg, mergeCtx->dstSeg->dirSeg);
    } else {
        if (mergeCtx->targetSegPageAddr == mergeCtx->dstSeg->segPageAddr) {
            return;
        }
        // 升降级场景缩容失败，需要回滚已经缩容完成的页
        ChLabelPageHeaderT *targetPageHead =
            ((ChLabelPageHeaderT *)(void *)(mergeCtx->targetSegPageAddr - CHLABEL_PAGE_HEAD_OFFSET));
        ClusteredHashMergePageChainRollBack(runCtx, mergeCtx->srcSeg->dirSeg, mergeCtx->dstSeg->dirSeg,
            targetPageHead->baseHead.addr, mergeCtx->pageSize);
    }
}

// segmentB合并到segmentA
void ClusteredHashMergePageChain4Rsm(ChLabelRunCtxT *runCtx, ChLabelPageMergeCtxT *mergeCtx)
{
    StatusInter ret = ClusteredHashMergeMulVersionHorizontalUndoLog(runCtx, mergeCtx);
    if (ret != STATUS_OK_INTER) {
        return;
    }
    ChLabelPageMetaDataT pageDataA;
    ChLabelPageMetaDataT pageDataB;
    bool isScaleInSucc = true;
    uint32_t freePageCnt = 0;
    mergeCtx->targetSegPageAddr = mergeCtx->dstSeg->segPageAddr;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_SCALE_IN_BEFORE);
    do {
        pageDataA = *ClusteredHashGetPageMetaData(mergeCtx->dstSeg->segPageAddr);
        pageDataB = *ClusteredHashGetPageMetaData(mergeCtx->srcSeg->segPageAddr);
        if (!mergeCtx->isGc) {
            if (!ClusteredHashSegmentMerge(runCtx, mergeCtx, &pageDataA, &pageDataB)) {
                isScaleInSucc = false;
                break;
            }
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_SCALE_IN_PART_SUCC);
            freePageCnt++;
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_SCALE_IN_RECORD_COUNT);
            if (runCtx->labelVarInfo->upgradeVersion != 0) {
                mergeCtx->rsmUndoChMulVersionScaleInRec->type = CLUSTERED_HASH_SCALE_IN_PART_SUCC;
            }
        } else {
            DB_ASSERT(mergeCtx->srcSeg->pageHead->freeSlotCount == pageDataB.tupleCntPerPage);
            ClusteredHashEmptySegmentMerge(runCtx, mergeCtx->dstSeg, mergeCtx->srcSeg);
        }
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_SCALE_IN_HALF);
        runCtx->chLabel->dashMeta.scaleInPageCnt++;  // 实际缩容的页的个数
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_SCALE_IN_UPDATE_PAGE_CNT);
        if (ClusteredHashIsInvalidPageId(pageDataB.nextPageAddr)) {
            break;
        }
        mergeCtx->dstSeg->pageHead = ClusteredHashGetPageHeadAndPageAddrByPageId(
            &runCtx->memRunCtx, pageDataA.nextPageAddr, &mergeCtx->dstSeg->segPageAddr);
        mergeCtx->srcSeg->pageHead = ClusteredHashGetPageHeadAndPageAddrByPageId(
            &runCtx->memRunCtx, pageDataB.nextPageAddr, &mergeCtx->srcSeg->segPageAddr);
    } while (mergeCtx->dstSeg->pageHead != NULL && mergeCtx->srcSeg->pageHead != NULL);
    if (runCtx->labelVarInfo->upgradeVersion == 0 || mergeCtx->isGc) {
        return;
    }
    mergeCtx->rsmUndoChMulVersionScaleInRec->freePageCnt = freePageCnt;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_SCALE_IN_UPDATE_PAGE_CHAIN);
    ClusteredHashUpdatePageChainAfterMerge4Rsm(runCtx, mergeCtx, isScaleInSucc);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_SCALE_IN_END);
    ClusteredHashRsmUndoLogMovePrevRec(runCtx);
}

void ClusteredHashMergePageChain(ChLabelRunCtxT *runCtx, ChLabelPageMergeCtxT *mergeCtx)
{
    ChLabelPageMetaDataT pageDataA;
    ChLabelPageMetaDataT pageDataB;
    bool isScaleInSucc = true;
    mergeCtx->targetSegPageAddr = mergeCtx->dstSeg->segPageAddr;
    do {
        pageDataA = *ClusteredHashGetPageMetaData(mergeCtx->dstSeg->segPageAddr);
        pageDataB = *ClusteredHashGetPageMetaData(mergeCtx->srcSeg->segPageAddr);
        if (!mergeCtx->isGc) {
            if (!ClusteredHashSegmentMerge(runCtx, mergeCtx, &pageDataA, &pageDataB)) {
                isScaleInSucc = false;
                break;
            }
        } else {
            DB_ASSERT(mergeCtx->srcSeg->pageHead->freeSlotCount == pageDataB.tupleCntPerPage);
            ClusteredHashEmptySegmentMerge(runCtx, mergeCtx->dstSeg, mergeCtx->srcSeg);
        }
        runCtx->chLabel->dashMeta.scaleInPageCnt++;  // 实际缩容的页的个数
        if (ClusteredHashIsInvalidPageId(pageDataB.nextPageAddr)) {
            break;
        }
        mergeCtx->srcSeg->pageHead = ClusteredHashGetPageHeadAndPageAddrByPageId(
            &runCtx->memRunCtx, pageDataB.nextPageAddr, &mergeCtx->srcSeg->segPageAddr);
        mergeCtx->dstSeg->pageHead = ClusteredHashGetPageHeadAndPageAddrByPageId(
            &runCtx->memRunCtx, pageDataA.nextPageAddr, &mergeCtx->dstSeg->segPageAddr);
    } while (mergeCtx->dstSeg->pageHead != NULL && mergeCtx->srcSeg->pageHead != NULL);
    if (runCtx->labelVarInfo->upgradeVersion == 0 || mergeCtx->isGc) {
        return;
    }
    ClusteredHashUpdatePageChainAfterMerge(runCtx, mergeCtx, isScaleInSucc);
}

StatusInter ClusteredHashFreeSegPageChain(ChLabelPageMemRunCtxT *memRunCtx, RsmUndoRecordT *rsmUndoRec,
    HashDirSegmentT *seg, uint8_t **segPageAddr, EhTrcPageCacheT *cache)
{
    DB_POINTER(segPageAddr);
    PageIdT targetPageAddr = seg->pageAddr.pageAddr;
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaData(*segPageAddr);
    // 遍历page链表清空数据
    do {
        PageIdT nextTargetPageAddr = pageData->nextPageAddr;
        if (cache != NULL && cache->cnt < EH_DIR_AND_SEG_PAGE) {
            cache->pageId[cache->cnt++] = targetPageAddr;
        } else {
            StatusInter ret = ClusteredHashFreeOneSegPage(memRunCtx, rsmUndoRec, targetPageAddr);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                return ret;
            }
        }
        if (SECUREC_LIKELY(ClusteredHashIsInvalidPageId(nextTargetPageAddr))) {
            break;
        }
        targetPageAddr = nextTargetPageAddr;
        pageData = ClusteredHashGetPageMetaDataByPageId(memRunCtx, targetPageAddr);
        if (pageData == NULL) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
                "unable to get page metadata, deviceId: %" PRIu32 ", blockId: %" PRIu32, targetPageAddr.deviceId,
                targetPageAddr.blockId);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
    } while (true);
    return STATUS_OK_INTER;
}

void ChLabelGetPageChainScanSegment(ChLabelRunCtxT *ctx, ChLabelScanCursorT *cursor, HashDirSegmentT *segment,
    ChLabelPageMetaDataT *pageData, uint8_t **segPageAddr)
{
    uint32_t fileId = ctx->chLabel->memMgr.base.fileId;
    bool isAddrValid = true;
    do {
        if (pageData->nextPageAddr.deviceId == cursor->scanPos.pageAddr.deviceId &&
            pageData->nextPageAddr.blockId == cursor->scanPos.pageAddr.blockId) {
            segment->pageAddr.pageAddr = pageData->nextPageAddr;
            StatusInter ret =
                ClusteredHashGetSegPage(ctx->memRunCtx.mdMgr, pageData->nextPageAddr, fileId, segPageAddr);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                SE_LAST_ERROR(ret,
                    "unable to get clustered hash seg page when fast fetch page, deviceId = %" PRIu32
                    ", blockId = %" PRIu32 ", labelId: %" PRIu32 ", indexId: %" PRIu32,
                    pageData->nextPageAddr.deviceId, pageData->nextPageAddr.blockId, ctx->chLabel->labelCfg.labelId,
                    ctx->chLabel->labelCfg.indexId);
                *segPageAddr = NULL;
            }
            break;
        }
        // 扫描页不在链表上，发生过truncate，返回EOF
        if (SECUREC_UNLIKELY(pageData->nextPageAddr.deviceId == DB_INVALID_UINT32 ||
                             pageData->nextPageAddr.blockId == DB_INVALID_UINT32)) {
            cursor->isScanEnd = true;
            return;
        }
        isAddrValid = ClusteredHashIsPageAddrValid(pageData->nextPageAddr);
        pageData = ClusteredHashGetPageMetaDataByPageId(&ctx->memRunCtx, pageData->nextPageAddr);
    } while (isAddrValid && pageData != NULL);
    if (SECUREC_UNLIKELY(isAddrValid && pageData == NULL)) {
        *segPageAddr = NULL;
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "unable to get segment page for scan, labelId: %" PRIu32 ", indexId: %" PRIu32,
            ctx->chLabel->labelCfg.labelId, ctx->chLabel->labelCfg.indexId);
    }
}

StatusInter ChLabelFetchRowsInPageChain(
    ChLabelRunCtxT *ctx, ChLabelScanCursorT *cursor, ChLabelScanSubsCondDataT *userScanCondData)
{
    StatusInter ret = STATUS_OK_INTER;
    ChLabelPageFetchRowInfoT *fetchRowInfo = &cursor->fetchRowInfo;
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataWithAddr(ctx, fetchRowInfo->segPageHead);
    ChLabelScanPosT lastScanPos = cursor->scanPos;
    bool isContinue = true;
    bool isAddrValid = true;
    do {
        while (isContinue) {
            ret = ChLabelFetchNextValidTuple(cursor, fetchRowInfo);
            if (ret != STATUS_OK_INTER) {
                DB_ASSERT(ret == NO_DATA_HEAP_ITEM_NOT_EXIST);
                break;
            }
            ret = ChLabelFetchNextHandleByUserProc(cursor, userScanCondData);
            if (ret != STATUS_OK_INTER) {
                isContinue = false;
                break;
            }
            ret = ChLabelDoActionAfterProc(userScanCondData, lastScanPos, cursor, &isContinue);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret,
                    "clustered hash, Fetch Successfully, proc data unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                    ctx->chLabel->labelCfg.labelId, ctx->chLabel->labelCfg.indexId);
                isContinue = false;
                break;
            }
        }
        if (ret == STATUS_OK_INTER || ClusteredHashIsInvalidPageId(pageData->nextPageAddr)) {
            break;
        }
        fetchRowInfo->pageAddr = pageData->nextPageAddr;
        isAddrValid = ClusteredHashIsPageAddrValid(fetchRowInfo->pageAddr);
        pageData =
            ClusteredHashGetPageMetaAndAddrById(&ctx->memRunCtx, pageData->nextPageAddr, &fetchRowInfo->segPageHead);
        cursor->scanPos.isStartFromPageHead = true;
    } while (isContinue && isAddrValid && pageData != NULL);
    if (SECUREC_UNLIKELY(isAddrValid && pageData == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "unable to get segment page for fetch rows, labelId: %" PRIu32 ", indexId: %" PRIu32,
            ctx->chLabel->labelCfg.labelId, ctx->chLabel->labelCfg.indexId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    return ret;
}

StatusInter ChLabelFetchRowsInPageChain4ScanDel(ChLabelRunCtxT *ctx, ChLabelScanCursorT *cursor,
    ChLabelScanDeletePrepare scanDeletePrepare, ChLabelScanProcCtxT *procCtx, ChLabelScanCtxT *scanCtx)
{
    StatusInter ret = STATUS_OK_INTER;
    ChLabelPageFetchRowInfoT *fetchRowInfo = &cursor->fetchRowInfo;
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataWithAddr(ctx, fetchRowInfo->segPageHead);
    bool isContinueFetchNext = true;
    do {
        do {
            ret = ChLabelFetchNextValidTuple(cursor, fetchRowInfo);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                DB_ASSERT(ret == NO_DATA_HEAP_ITEM_NOT_EXIST);
                isContinueFetchNext = true;
                break;
            }
            ret = ChLabelFetchNextHandleByUserProc4ScanDel(cursor, scanDeletePrepare, &isContinueFetchNext, procCtx);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                break;
            }
            if (isContinueFetchNext) {
                continue;
            }
            ret = ChLabelExtractHashCodeFromCursor(cursor);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                SE_ERROR(ret, "Unable to extract primary key from tupleBuf while scan del.");
                isContinueFetchNext = false;
                break;
            }
            ret = ClusteredHashLookupByPhyRowIdScanDel(ctx, cursor->scanPos.phyRowId, scanCtx);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                SE_ERROR(ret, "Unable to get row, rowId:%" PRIu32 ", labelId: %" PRIu32 ", indexId: %" PRIu32,
                    cursor->scanPos.phyRowId.tupleId, ctx->chLabel->labelCfg.labelId, ctx->chLabel->labelCfg.indexId);
                isContinueFetchNext = false;
                break;
            }
            ChLogicRowId logicRowId = ChLabelGetLogicalAddrByPhysicalAddr(cursor->ctx, cursor->scanPos.phyRowId);
            ret = ClusteredBucketEraseByIdScanDel(ctx, scanCtx);
            if (SECUREC_LIKELY(ret == STATUS_OK_INTER)) {
                procCtx->deleteCtx.userData->rowId = *(uint64_t *)(void *)(&logicRowId);
                procCtx->deleteCtx.userData->scanCurBlockId = *(uint32_t *)&cursor->scanPos.segmentPos;
            }
            break;
        } while (isContinueFetchNext);
        if (!isContinueFetchNext || ret == STATUS_OK_INTER || ClusteredHashIsInvalidPageId(pageData->nextPageAddr)) {
            break;
        }
        fetchRowInfo->pageAddr = pageData->nextPageAddr;
        pageData =
            ClusteredHashGetPageMetaAndAddrById(&ctx->memRunCtx, pageData->nextPageAddr, &fetchRowInfo->segPageHead);
        cursor->scanPos.isStartFromPageHead = true;
    } while (isContinueFetchNext);
    return ret;
}

static bool ClusteredHashGetMoveSeg(
    ChLabelRunCtxT *runCtx, ChLabelSegmentPageT *segmentA, ChLabelSegmentPageT *segmentB)
{
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    ChLabelPageHeaderT *tmpPageHead = segmentA->pageHead;
    ChLabelPageMetaDataT pageData = *ClusteredHashGetPageMetaData(segmentA->segPageAddr);
    uint32_t firstPageRowSize = pageData.pageRowSize;
    uint32_t nextPageCnt = 0;
    do {
        if (ClusteredHashIsInvalidPageId(pageData.nextPageAddr)) {
            break;
        }
        uint8_t *segPageAddr = NULL;
        tmpPageHead =
            ClusteredHashGetPageHeadAndPageAddrByPageId(&runCtx->memRunCtx, pageData.nextPageAddr, &segPageAddr);
        pageData = *ClusteredHashGetPageMetaData(segPageAddr);
        if (pageData.pageRowSize <= firstPageRowSize) {
            nextPageCnt++;
            if (nextPageCnt < dashMemMeta->dirCurItemCnt) {
                continue;
            }
            segmentB->pageHead = tmpPageHead;
            segmentB->segPageAddr = segPageAddr;
            return true;
        }
    } while (tmpPageHead != NULL);
    return false;
}

// 判断B页是否满足条件合入A页
static bool ClusteredHashUpgradeSegCanScaleIn(ChLabelRunCtxT *runCtx, const DashEhLabelMetaT *dashMeta,
    ChLabelSegmentPageT *segmentA, ChLabelSegmentPageT *segmentB)
{
    ChLabelPageMetaDataT pageDataA = *ClusteredHashGetPageMetaData(segmentA->segPageAddr);
    ChLabelPageMetaDataT pageDataB = *ClusteredHashGetPageMetaData(segmentB->segPageAddr);
    // 快速判断：如果其中一页为空，那么一定可以缩容
    if (segmentB->pageHead->freeSlotCount == pageDataB.tupleCntPerPage) {
        return true;
    }
    if (segmentA->pageHead->freeSlotCount < (pageDataB.tupleCntPerPage - segmentB->pageHead->freeSlotCount)) {
        return false;
    }
    // 详细检查缩容条件（逻辑上按照和实际处理一样的方式预判断，但不实际进行搬移等操作）
    if (ClusteredHashSegPairCanScaleIn4MulVersion(
            runCtx, &pageDataA, &segmentA->segPageAddr, &pageDataB, &segmentB->segPageAddr)) {
        return true;
    }
    return false;
}

// 在一个segment中找行大小不同的两个页
StatusInter ClusteredHashFindSegPairCanMoveData(
    ChLabelRunCtxT *runCtx, ChLabelSegmentPageT *segmentA, ChLabelSegmentPageT *segmentB, bool *result)
{
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    ClusteredHashLabelVarInfoT *labelVarInfo = runCtx->labelVarInfo;
    uint32_t fileId = runCtx->chLabel->memMgr.base.fileId;
    for (uint32_t dirSlotId = dashMemMeta->dirCurItemIdx; dirSlotId < labelVarInfo->dirCap; dirSlotId++) {
        segmentA->dirSeg = ClusteredHashGetDirSegmentBySegId(&runCtx->memRunCtx, dirSlotId);
        if (SECUREC_UNLIKELY(segmentA->dirSeg == NULL)) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "unable to get clustered hash dir seg ");
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        StatusInter ret = ClusteredHashGetSegPage(
            runCtx->memRunCtx.mdMgr, segmentA->dirSeg->pageAddr.pageAddr, fileId, &segmentA->segPageAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
        segmentA->pageHead = ClusteredHashGetPageHead(segmentA->segPageAddr);
        segmentB->dirSeg = segmentA->dirSeg;
        if (ClusteredHashGetMoveSeg(runCtx, segmentA, segmentB)) {
            dashMemMeta->dirCurItemCnt++;
            DB_ASSERT(labelVarInfo->segPageCount >= dashMemMeta->dirCurItemCnt);
            *result = true;
            return STATUS_OK_INTER;
        }
    }
    dashMemMeta->dirCurItemIdx++;
    dashMemMeta->dirCurItemCnt = 0;
    *result = false;
    return STATUS_OK_INTER;
}

// 升降级合并页
StatusInter ClusteredHashFindSegAndMoveData(ChLabelRunCtxT *runCtx)
{
    if (SECUREC_LIKELY(runCtx->labelVarInfo->upgradeVersion == 0)) {
        return STATUS_OK_INTER;
    }
    uint64_t start = DbRdtsc();
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    if (dashMemMeta->dirCurItemIdx >= runCtx->labelVarInfo->dirCap) {
        dashMemMeta->dirCurItemIdx = 0;
        dashMemMeta->dirCurItemCnt = 0;
    }
    ChLabelSegmentPageT segmentA;
    ChLabelSegmentPageT segmentB;
    bool findSegPageToMoveData = false;
    StatusInter ret = ClusteredHashFindSegPairCanMoveData(runCtx, &segmentA, &segmentB, &findSegPageToMoveData);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    if (!findSegPageToMoveData) {
        return STATUS_OK_INTER;
    }
    while (findSegPageToMoveData) {
        // 判断是否满足搬迁数据条件
        if (ClusteredHashUpgradeSegCanScaleIn(runCtx, &runCtx->chLabel->dashMeta, &segmentA, &segmentB)) {
            ChLabelPageMetaDataT pageDataA = *ClusteredHashGetPageMetaData((uint8_t *)(segmentA.pageHead + 1));
            ChLabelPageMetaDataT pageDataB = *ClusteredHashGetPageMetaData((uint8_t *)(segmentB.pageHead + 1));
            ClusteredHashSegmentMerge4MulVersion(runCtx, &segmentA, &segmentB, pageDataA, pageDataB);
            DB_ASSERT(dashMemMeta->dirCurItemCnt > 0);
            dashMemMeta->dirCurItemCnt--;
#ifndef NDEBUG
            runCtx->chLabel->dashMeta.scaleInUpgradePageCnt++;
#endif
        }
        if (DbToMseconds(DbRdtsc() - start) > CLUSTERED_HASH_UPGRADE_MERGE_TIME) {
            break;
        }
        ret = ClusteredHashFindSegPairCanMoveData(runCtx, &segmentA, &segmentB, &findSegPageToMoveData);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

#ifdef __cplusplus
}
#endif
