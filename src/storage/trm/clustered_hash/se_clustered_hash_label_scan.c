/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: se_cluster_hash_label_scan.h
 * Description: implementation of scan interfaces of clustered hash label
 * Author: ch<PERSON><PERSON>wen
 * Create: 2022/8/19
 */
#include "se_clustered_hash_label_scan.h"
#include "se_clustered_hash_label_dml.h"
#include "se_clustered_hash_access_dm.h"
#include "se_heap_access.h"
#include "se_clustered_hash_label_overflow_page.h"
#include "se_clustered_hash_label_stat.h"
#include "se_clustered_hash_label_upgrade.h"
#include "se_log.h"
#include "se_clustered_hash_hc_index.h"
#include "se_clustered_hash_rsm.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CLUSTERED_HASH_OP_BLOCK_SLEEP_TIME 100              // unit: us
#define CLUSTERED_HASH_OP_TIMEOUT (2 * USECONDS_IN_SECOND)  // 2s

ALWAYS_INLINE static void ChLabelGetNextScanPattern(ChLabelScanCursorT *cursor, uint32_t *pattern)
{
    uint32_t depth = cursor->scanPos.segmentPos.depth;
    while (depth > 0) {
        uint32_t tmp = ((uint32_t)1 << (depth - 1));
        if ((cursor->scanPos.segmentPos.pattern & tmp) == 0) {
            cursor->scanPos.segmentPos.pattern = (uint32_t)((cursor->scanPos.segmentPos.pattern | tmp) & 0xFFFFFF);
            *pattern = cursor->scanPos.segmentPos.pattern;
            cursor->scanPos.pageAddr = (PageIdT){DB_INVALID_UINT32, DB_INVALID_UINT32};
            return;
        }
        cursor->scanPos.segmentPos.pattern = (uint32_t)((cursor->scanPos.segmentPos.pattern & (~tmp)) & 0xFFFFFF);
        depth--;
    }
    *pattern = DB_INVALID_UINT32;
    cursor->scanPos.pageAddr = (PageIdT){DB_INVALID_UINT32, DB_INVALID_UINT32};
}

static inline bool ChLabelCheckIsNoData(const ChLabelRunCtxT *ctx)
{
    return (ctx->chLabel->perfStat.phyItemNum == 0);
}

static inline bool ChLabelCheckDataPageIsFree(const HashDirSegmentT *segment, uint32_t pattern)
{
    return (segment->pattern != pattern);
}

static inline void ChLabelUpdateScanDepth(ChLabelScanCursorT *cursor, uint32_t depth)
{
    cursor->scanPos.segmentPos.depth = (uint32_t)(depth & 0x7F);
}

StatusInter ChLabelFetchScanDataPage(ChLabelRunCtxT *ctx, HashDirSegmentT segment, ChLabelScanCursorT *cursor)
{
    uint8_t *segPageAddr = NULL;
    StatusInter ret = ClusteredHashGetSegPage(
        ctx->memRunCtx.mdMgr, segment.pageAddr.pageAddr, ctx->chLabel->memMgr.base.fileId, &segPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret,
            "Unable to get data page, pageId(%" PRIu32 ", %" PRIu32 "), pattern:%" PRIu32 ", depth:%" PRIu32
            ", labelId: %" PRIu32 ", indexId: %" PRIu32,
            segment.pageAddr.pageAddr.deviceId, segment.pageAddr.pageAddr.blockId, segment.pattern, segment.segDepth,
            ctx->chLabel->labelCfg.labelId, ctx->chLabel->labelCfg.indexId);
        return ret;
    }
    cursor->fetchRowInfo.firstPageHead = segPageAddr;
    cursor->fetchRowInfo.firstPageAddr = segment.pageAddr.pageAddr;
    if ((cursor->scanPos.pageAddr.deviceId != DB_INVALID_UINT32 &&
            cursor->scanPos.pageAddr.blockId != DB_INVALID_UINT32) &&
        (segment.pageAddr.pageAddr.deviceId != cursor->scanPos.pageAddr.deviceId ||
            segment.pageAddr.pageAddr.blockId != cursor->scanPos.pageAddr.blockId)) {
        ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataWithAddr(ctx, segPageAddr);
        // 扫描页不在链表上，发生过truncate，返回EOF
        if (SECUREC_UNLIKELY(pageData->nextPageAddr.deviceId == DB_INVALID_UINT32 ||
                             pageData->nextPageAddr.blockId == DB_INVALID_UINT32)) {
            cursor->isScanEnd = true;
            return STATUS_OK_INTER;
        }
        ChLabelGetPageChainScanSegment(ctx, cursor, &segment, pageData, &segPageAddr);
        if (SECUREC_UNLIKELY(cursor->isScanEnd)) {
            return STATUS_OK_INTER;
        }
        if (segPageAddr == NULL) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
                "unable to get segment page, labelId: %" PRIu32 ", indexId: %" PRIu32, ctx->chLabel->labelCfg.labelId,
                ctx->chLabel->labelCfg.indexId);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
    }
    cursor->fetchRowInfo.segPageHead = segPageAddr;
    cursor->fetchRowInfo.pageAddr = segment.pageAddr.pageAddr;
    cursor->fetchRowInfo.isOverFlowPage = false;
    ChLabelRecoveryPageLatch(cursor, &segPageAddr);
    return STATUS_OK_INTER;
}

StatusInter ChLabelCursorAppendExpandedInfo(ChLabelRunCtxT *ctx, ChLabelScanCursorT *cursor, uint32_t newDepth)
{
    DB_POINTER2(ctx, cursor);
    // 此expInfo申请的内存，会插入到expandList.list中，后续由上层调用 ChGetScanPageBeginTupleId 进行释放
    ChScanExpandedInfoT *expInfo = DbDynMemCtxAlloc(ctx->seRunCtx->sessionMemCtx, sizeof(ChScanExpandedInfoT));
    if (SECUREC_UNLIKELY(expInfo == NULL)) {
        SE_ERROR(OUT_OF_MEMORY_INTER,
            "Unable to alloc ChScanExpandedInfoT memory, labelId: %" PRIu32 ", indexId: %" PRIu32,
            ctx->chLabel->labelCfg.labelId, ctx->chLabel->labelCfg.indexId);
        return OUT_OF_MEMORY_INTER;
    }
    expInfo->orgSegPattern = cursor->scanPos.segmentPos.pattern;
    expInfo->orgSegDepth = cursor->scanPos.segmentPos.depth;
    expInfo->expSegDepth = (uint16_t)newDepth;
    expInfo->tupleId = cursor->scanPos.phyRowId.tupleId + 1;
    expInfo->matchCount = 0;
    DbSpinLock(&cursor->expandedList.lock);
    DbLinkedListAppend(&cursor->expandedList.list, &expInfo->expandNode);
    DbSpinUnlock(&cursor->expandedList.lock);
    return STATUS_OK_INTER;
}

inline static bool ChExpandedInfoMatch(ChScanExpandedInfoT *expInfo, uint32_t pattern)
{
    bool match = expInfo->orgSegPattern == (pattern & ((1u << (uint32_t)expInfo->orgSegDepth) - 1u));
    if (match) {
        expInfo->matchCount++;
    }
    return match;
}

inline static bool ChExpandedInfoBetterThan(const ChScanExpandedInfoT *targe, const ChScanExpandedInfoT *org)
{
    return (targe->orgSegDepth > org->orgSegDepth) ||
           (targe->orgSegDepth == org->orgSegDepth && targe->tupleId > org->tupleId);
}

inline static uint32_t ChExpandedInfoMaxMatchCount(const ChLabelScanCursorT *cursor, const ChScanExpandedInfoT *expInfo)
{
    uint32_t dirDepth = cursor->ctx->labelVarInfo->dirDepth;
    DB_ASSERT(dirDepth > expInfo->orgSegDepth);  // 发送扩展全局depth一定比原seg depth大
    return (1u << (dirDepth - (uint32_t)expInfo->orgSegDepth)) - 1u;
}

uint16_t ChGetScanPageBeginTupleId(ChLabelScanCursorT *cursor)
{
    DB_POINTER(cursor);
    if (cursor->scanMode == CH_SCAN_ONE_PAGE) {
        return 0;
    }
    ChScanExpandedInfoT matchInfo = {0};
    ChScanExpandedInfoT *node = NULL;
    ChScanExpandedInfoT *tmpNode = NULL;
    DbSpinLock(&cursor->expandedList.lock);
    LIST_FOR_EACH_ENTRY_SAFE(node, tmpNode, &cursor->expandedList.list, expandNode)
    {
        if (ChExpandedInfoMatch(node, cursor->scanPos.segmentPos.pattern)) {
            if (ChExpandedInfoBetterThan(node, &matchInfo)) {
                matchInfo = *node;
            }
            if (node->matchCount == ChExpandedInfoMaxMatchCount(cursor, node)) {
                DbLinkedListRemove(&node->expandNode);
                DbDynMemCtxFree(cursor->ctx->seRunCtx->sessionMemCtx, node);
                node = NULL;
            }
        }
    }
    DbSpinUnlock(&cursor->expandedList.lock);
    return matchInfo.tupleId;
}

static inline void ChLabelInitCursorForOverflowPage(ChLabelScanCursorT *cursor)
{
    cursor->scanPos.isStartFromPageHead = true;
    cursor->scanPos.isScanOverFlowPage = true;
}

StatusInter ChLabelSelectScanPage(ChLabelRunCtxT *ctx, ChLabelScanCursorT *cursor)
{
    // 1.无数据;
    // 2.扫描期间发生truncate；
    // 3.起始扫描位置已经超出页范围
    if ((ChLabelCheckIsNoData(ctx) || cursor->scanPos.segmentPos.depth > ctx->labelVarInfo->dirDepth) &&
        !DbCommonGetWarmReboot()) {
        cursor->isScanEnd = true;
        return STATUS_OK_INTER;
    }
    bool isFind = false;
    do {
        uint32_t pattern;
        bool isOldPage = false;
        if (cursor->isFirstFetch) {
            cursor->isFirstFetch = false;
            pattern = cursor->scanPos.segmentPos.pattern;
        } else if (cursor->scanPos.isStartFromPageHead) {
            cursor->scanPos.segmentPos.isOverFlowPage = 0;
            ChLabelGetNextScanPattern(cursor, &pattern);
        } else {
            pattern = cursor->scanPos.segmentPos.pattern;
            isOldPage = true;
        }
        if (pattern == DB_INVALID_UINT32) {
            // segment页已经扫描结束, 开始扫描溢出页
            ChLabelInitCursorForOverflowPage(cursor);
            return STATUS_OK_INTER;
        }
        HashDirSegmentT *segment = ClusteredHashGetDirSegmentBySegId(&ctx->memRunCtx, pattern);
        if (SECUREC_UNLIKELY(segment == NULL)) {
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        if (ChLabelCheckDataPageIsFree(segment, pattern)) {
            cursor->scanPos.isStartFromPageHead = true;
            ChLabelUpdateScanDepth(cursor, segment->segDepth);
            continue;
        }
        StatusInter ret = ChLabelFetchScanDataPage(ctx, *segment, cursor);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER || cursor->isScanEnd)) {
            return ret;
        }
        if (isOldPage && segment->segDepth != cursor->scanPos.segmentPos.depth) {
            DB_ASSERT(segment->segDepth > cursor->scanPos.segmentPos.depth);  // 缩容和扫描互斥，不可能小于
            ret = ChLabelCursorAppendExpandedInfo(ctx, cursor, segment->segDepth);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                return ret;
            }
        }
        ChLabelUpdateScanDepth(cursor, segment->segDepth);
        isFind = true;
    } while (!isFind);
    return STATUS_OK_INTER;
}

bool ChLabelFindFirstValidTuple(const uint8_t *bitmap, uint32_t bitmapSize, uint32_t startId, uint16_t *targetTupleId)
{
    uint32_t bitOffset = startId & 0x7;  // 与0x7，对8取余
    uint32_t wordOffset = startId >> 3;  // 右移3位，除以8
    uint8_t startWord = bitmap[wordOffset];
    if (bitOffset > 0) {
        startWord &= (uint8_t)(((uint32_t)DB_INVALID_UINT32 << bitOffset) & 0xFF);
    }
    for (uint32_t i = wordOffset; i < bitmapSize; i++) {
        if (startWord == 0) {
            startWord = bitmap[i + 1];
            bitOffset = 0;
            continue;
        }
        while (bitOffset < DB_8BIT) {
            if ((startWord & ((uint32_t)1 << bitOffset)) > 0) {
                *targetTupleId = (uint16_t)((i << 3) + bitOffset);  // 左移3位，乘8
                return true;
            }
            bitOffset++;
        }
    }
    return false;
}

ALWAYS_INLINE void ChLabelScanFetchTupleFromCache(
    ChLabelScanCursorT *cursor, ChRowHeadT *rowHead, uint8_t **buf, uint32_t *bufSize)
{
    *buf = (uint8_t *)(rowHead + 1);
    *bufSize = rowHead->totalBufSize;
}

static void ChLabelFetchNextRow(ChLabelScanCursorT *cursor, uint32_t tupleId, ChLabelPageFetchRowInfoT *fetchRowInfo)
{
    DB_POINTER2(cursor, fetchRowInfo);
    ChLabelRunCtxT *ctx = cursor->ctx;
#ifndef NDEBUG
    if (ctx->lockPage != NULL && ctx->labelVarInfo->upgradeVersion == 0) {
        DB_ASSERT(ctx->lockPage == fetchRowInfo->segPageHead);
    }
#endif
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaData(fetchRowInfo->segPageHead);
    uint32_t firstTuplePos = fetchRowInfo->isOverFlowPage ? pageData->firstTuplePosPerOverFlowPage : pageData->tuplePos;
    uint8_t *tupleHdr =
        (uint8_t *)((uintptr_t)fetchRowInfo->segPageHead + firstTuplePos + CHLABEL_PAGE_METADATA_OFFSET);
    ChRowHeadT *rowHead = ClusteredHashGetRowHeadByTupleId(
        tupleHdr, pageData->tupleSize, ctx->chLabel->labelCfg.slotExtendSize, (uint16_t)tupleId);
    ChLabelRecoveryRowHeadInfo(cursor, rowHead);
    ChLabelScanFetchTupleFromCache(cursor, rowHead, (uint8_t **)&fetchRowInfo->buf, &fetchRowInfo->bufSize);
    fetchRowInfo->isGetBuf = true;
}

ALWAYS_INLINE StatusInter ChLabelFetchNextValidTuple(ChLabelScanCursorT *cursor, ChLabelPageFetchRowInfoT *fetchRowInfo)
{
    ChLabelRunCtxT *runCtx = cursor->ctx;
    if (cursor->scanPos.isStartFromPageHead) {
        cursor->scanPos.isStartFromPageHead = false;
        cursor->scanPos.phyRowId.tupleId = ChGetScanPageBeginTupleId(cursor);
    } else {
        cursor->scanPos.phyRowId.tupleId++;
    }
#ifndef NDEBUG
    if (runCtx->lockPage != NULL && runCtx->labelVarInfo->upgradeVersion == 0) {
        DB_ASSERT(runCtx->lockPage == fetchRowInfo->segPageHead);
    }
#endif
    // 这里需要找entry获取物理tuple对应的逻辑slot
    uint8_t *bitmap =
        fetchRowInfo->segPageHead + runCtx->chLabel->dashMeta.bloomFilterSize + CHLABEL_PAGE_METADATA_OFFSET;
    bool isFind = ChLabelFindFirstValidTuple(bitmap, runCtx->chLabel->dashMeta.bitMapSize,
        cursor->scanPos.phyRowId.tupleId, &cursor->scanPos.phyRowId.tupleId);
    if (SECUREC_UNLIKELY(!isFind)) {
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }

    ChLabelFetchNextRow(cursor, cursor->scanPos.phyRowId.tupleId, fetchRowInfo);
    return STATUS_OK_INTER;
}

StatusInter ChLabelFetchNextValidTupleFromOverFlowPage(
    ChLabelScanCursorT *cursor, ChLabelPageFetchRowInfoT *fetchRowInfo)
{
    DB_ASSERT(fetchRowInfo->isOverFlowPage);
    DB_ASSERT(cursor->scanPos.isScanOverFlowPage);

    ChLabelRunCtxT *runCtx = cursor->ctx;
    if (cursor->scanPos.isStartFromPageHead) {
        cursor->scanPos.isStartFromPageHead = false;
        cursor->scanPos.overFlowTupleId = 0;
    } else {
        cursor->scanPos.overFlowTupleId++;
    }
#ifndef NDEBUG
    if (cursor->ctx->lockPage != NULL && cursor->ctx->labelVarInfo->upgradeVersion == 0) {
        DB_ASSERT(cursor->ctx->lockPage == fetchRowInfo->segPageHead);
    }
#endif
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaData(fetchRowInfo->segPageHead);
    if (cursor->scanPos.overFlowTupleId >= pageData->maxTupleCntPerOverFlowPage) {
        cursor->scanPos.overFlowTupleId = 0;
        cursor->scanPos.overFlowBlockId++;
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }

    uint8_t *bitmap = fetchRowInfo->segPageHead + CHLABEL_PAGE_METADATA_OFFSET;
    uint32_t bitMapSize = runCtx->chLabel->dashMeta.bitmapSizePerOverFlowPage;
    bool isFind = ChLabelFindFirstValidTuple(
        bitmap, bitMapSize, cursor->scanPos.overFlowTupleId, &cursor->scanPos.overFlowTupleId);
    if (!isFind) {
        // 当前溢出页找不到一条有效的记录，则扫描下一个页
        cursor->scanPos.overFlowBlockId++;
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }

    ChLabelFetchNextRow(cursor, cursor->scanPos.overFlowTupleId, fetchRowInfo);
    return STATUS_OK_INTER;
}

ALWAYS_INLINE static void ChLabelFetchNextHandleInitBuffAndRowInfo(
    ChLabelScanCursorT *cursor, ChLabelRunCtxT *chRunCtx, HeapTupleBufAndRowInfoT *heapBuffAndRowInfo)
{
    DB_POINTER(cursor);
    heapBuffAndRowInfo->tupleBuf = (HeapTupleBufT){cursor->fetchRowInfo.bufSize, (uint8_t *)cursor->fetchRowInfo.buf};
    if (cursor->scanPos.isScanOverFlowPage) {
        DB_ASSERT(cursor->fetchRowInfo.segPageHead != NULL);
        ChLabelPageMetaDataT *pageData =
            ClusteredHashGetPageMetaDataWithAddr(chRunCtx, cursor->fetchRowInfo.segPageHead);
        // 如果是扫描溢出页，则对rowId做转换
        cursor->scanPos.phyRowId.tupleId = cursor->scanPos.overFlowTupleId + pageData->tupleCntPerPage;
        cursor->scanPos.phyRowId.tupleId +=
            (uint16_t)cursor->scanPos.overFlowBlockId * pageData->maxTupleCntPerOverFlowPage;
    }
    cursor->scanPos.segmentPos.isOverFlowPage = cursor->scanPos.isScanOverFlowPage;
    heapBuffAndRowInfo->fetchedRowInfo.curHeapTupleAddr =
        (HpTupleCombineAddrT){TransformChPhyRowId(cursor->scanPos.phyRowId), *(uint32_t *)&cursor->scanPos.segmentPos};
    heapBuffAndRowInfo->fetchedRowInfo.trxId = 0;
    heapBuffAndRowInfo->cacheBufSize = 0;
}

StatusInter ChLabelFetchNextHandleByUserProc(ChLabelScanCursorT *cursor, ChLabelScanSubsCondDataT *userScanCondData)
{
    ChLabelRunCtxT *chRunCtx = cursor->ctx;
    HeapTupleBufAndRowInfoT heapBuffAndRowInfo = {0};
    ChLabelFetchNextHandleInitBuffAndRowInfo(cursor, chRunCtx, &heapBuffAndRowInfo);

    userScanCondData->auxInfo.subAction = (HpScanSubsActT){0};
    HpScanSubsActT *subAction = &userScanCondData->auxInfo.subAction;

    StatusInter ret = userScanCondData->userScanRowProc(
        &heapBuffAndRowInfo, cursor->actualScanRowsCnt, userScanCondData->userData, subAction);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret,
            "unable to fetch, isMatched %" PRIu32 ", isRollBackScan %" PRIu32 ", isScanBreak %" PRIu32
            ", labelId: %" PRIu32 ", indexId: %" PRIu32,
            (uint32_t)subAction->isMatched, (uint32_t)subAction->isRollBackScan, (uint32_t)subAction->isScanBreak,
            chRunCtx->chLabel->labelCfg.labelId, chRunCtx->chLabel->labelCfg.indexId);
    }
    return ret;
}

StatusInter ChLabelExtractHashCodeFromCursor(ChLabelScanCursorT *cursor)
{
    DB_POINTER(cursor);
    DB_ASSERT(cursor->fetchRowInfo.isGetBuf);
    ChLabelRunCtxT *ctx = cursor->ctx;
    IndexKeyT idxKey;
    StatusInter ret = ClusteredHashGetIndexKey((const DmVertexT *)ctx->openCfg.vertex, ctx->chLabel->labelCfg.indexId,
        (DmIndexKeyBufInfoT **)&ctx->openCfg.indexKeyInfo, (uint8_t *)cursor->fetchRowInfo.buf, &idxKey);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret,
            "clustered hash extract primary key from tupleBuf unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            ctx->chLabel->labelCfg.labelId, ctx->chLabel->labelCfg.indexId);
        return ret;
    }
    cursor->scanPos.phyRowId.hashCode = ClusteredHashGet26BitHashCode(idxKey);
    return STATUS_OK_INTER;
}

StatusInter ChLabelDoActionAfterProc(ChLabelScanSubsCondDataT *userScanCondData, ChLabelScanPosT lastScanPos,
    ChLabelScanCursorT *cursor, bool *isContinueScan)
{
    *isContinueScan = true;
    const HpScanSubsActT *subAction = &userScanCondData->auxInfo.subAction;
    if (subAction->isRollBackScan) {  // 需要回滚意味着匹配函数有错误
        cursor->scanPos = lastScanPos;
        *isContinueScan = false;
    }

    if (subAction->isMatched) {
        cursor->isFetched = true;
        cursor->scannedRowCnt++;
        cursor->actualScanRowsCnt++;
        if (cursor->scanMode == CH_SCAN_ROWS) {
            StatusInter ret = ChLabelExtractHashCodeFromCursor(cursor);
            if (ret != STATUS_OK_INTER) {
                cursor->scannedRowCnt--;
                cursor->actualScanRowsCnt--;
                *isContinueScan = false;
                return ret;
            }
            ChLogicRowId addr = {
                cursor->scanPos.phyRowId.hashCode, cursor->scanPos.phyRowId.version, cursor->scanPos.phyRowId.tupleId};
            if (userScanCondData->getRowId) {
                addr = ChLabelGetLogicalAddrByPhysicalAddr(cursor->ctx, cursor->scanPos.phyRowId);
            }
            if (userScanCondData->userAppendRowIdProc != NULL) {
                addr = userScanCondData->getRowId ?
                           addr :
                           ChLabelGetLogicalAddrByPhysicalAddr(cursor->ctx, cursor->scanPos.phyRowId);
                userScanCondData->userAppendRowIdProc(
                    userScanCondData->userData, cursor->actualScanRowsCnt - 1, TransformChLogicRowId(addr));
            }
            cursor->fetchRowInfo.addr = TransformChLogicRowId(addr);
            if (cursor->actualScanRowsCnt == cursor->maxFetchNum) {
                *isContinueScan = false;
            }
        }
    }

    if (subAction->isScanBreak) {
        *isContinueScan = false;
    }
    cursor->scanPos.pageAddr = cursor->fetchRowInfo.pageAddr;
    return STATUS_OK_INTER;
}

StatusInter ChLabelFetchRows(
    ChLabelRunCtxT *ctx, ChLabelScanCursorT *cursor, ChLabelScanSubsCondDataT *userScanCondData)
{
    ChLabelPageFetchRowInfoT *fetchRowInfo = &cursor->fetchRowInfo;
    ClusteredHashPageLock(ctx, true, fetchRowInfo->firstPageHead, &fetchRowInfo->firstPageAddr);
    StatusInter ret = ChLabelFetchRowsInPageChain(ctx, cursor, userScanCondData);
    ClusteredHashPageUnlock(ctx, true, fetchRowInfo->firstPageHead);
    return ret;
}

static inline bool ChLabelFetchLoopIsBreak(const HpScanSubsActT *subAction)
{
    return (subAction->isRollBackScan || subAction->isScanBreak);
}

static inline bool ChLabelIsOnlyScanOnePage(const ChLabelScanCursorT *cursor)
{
    return (cursor->scanMode == CH_SCAN_ONE_PAGE && cursor->actualScanRowsCnt != 0);
}

StatusInter ChLabelSelectScanOverFlowPage(ChLabelRunCtxT *runCtx, ChLabelScanCursorT *cursor)
{
    if (ChLabelCheckIsNoData(runCtx)) {
        cursor->isScanEnd = true;
        return STATUS_OK_INTER;
    }
    uint32_t maxBlockCount = runCtx->labelVarInfo->overflowPageCount;
    if (cursor->scanPos.overFlowBlockId >= maxBlockCount) {
        cursor->isScanEnd = true;
        cursor->scanPos.isScanOverFlowPage = false;  // 溢出页扫描结束
        cursor->fetchRowInfo.isOverFlowPage = false;
        cursor->scanPos.segmentPos.isOverFlowPage = 0;
        return STATUS_OK_INTER;
    }
    DbMemAddrT pageAddr;
    StatusInter ret = ClusteredHashGetOverFlowPage(
        &runCtx->memRunCtx, cursor->scanPos.overFlowBlockId, runCtx->labelVarInfo->overflowPageCount, &pageAddr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Unable to select overFlow page by blockId :%" PRIu32 ", labelId: %" PRIu32 ", indexId: %" PRIu32,
            cursor->scanPos.overFlowBlockId, runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    cursor->fetchRowInfo.segPageHead = pageAddr.virtAddr;
    cursor->fetchRowInfo.pageAddr = pageAddr.pageAddr;
    cursor->fetchRowInfo.isOverFlowPage = true;
    cursor->scanPos.segmentPos.isOverFlowPage = 1;
    return STATUS_OK_INTER;
}

StatusInter ChLabelFetchRowsInOneOverFlowPage(
    ChLabelRunCtxT *ctx, ChLabelScanCursorT *cursor, ChLabelScanSubsCondDataT *userScanCondData)
{
    ChLabelPageFetchRowInfoT *fetchRowInfo = &cursor->fetchRowInfo;
    ClusteredHashPageLock(ctx, true, fetchRowInfo->segPageHead, &fetchRowInfo->pageAddr);
    StatusInter ret = STATUS_OK_INTER;
    ChLabelScanPosT lastScanPos = cursor->scanPos;
    bool isContinue = true;
    while (isContinue) {
        ret = ChLabelFetchNextValidTupleFromOverFlowPage(cursor, fetchRowInfo);
        if (ret != STATUS_OK_INTER) {
            break;
        }
        ret = ChLabelFetchNextHandleByUserProc(cursor, userScanCondData);
        if (ret != STATUS_OK_INTER) {
            break;
        }
        ret = ChLabelDoActionAfterProc(userScanCondData, lastScanPos, cursor, &isContinue);
        if (ret != STATUS_OK_INTER) {
            break;
        }
    }
    ClusteredHashPageUnlock(ctx, true, fetchRowInfo->segPageHead);
    return ret;
}

StatusInter ChLabelFetchNextFromOverFlowPages(
    ChLabelRunCtxT *ctx, ChLabelScanCursorT *cursor, ChLabelScanSubsCondDataT *userScanCondData)
{
    if (cursor->isScanEnd || !cursor->scanPos.isScanOverFlowPage) {
        return STATUS_OK_INTER;
    }
    StatusInter ret = STATUS_OK_INTER;
    cursor->isFetched = false;
    while (!cursor->isScanEnd) {
        // select one overflow page
        ret = ChLabelSelectScanOverFlowPage(ctx, cursor);
        if (ret != STATUS_OK_INTER) {
            break;
        }
        if (cursor->isScanEnd) {
            break;
        }
        // fetch mutiply rows from current overFlow Page and update overFlowTupleId
        ret = ChLabelFetchRowsInOneOverFlowPage(ctx, cursor, userScanCondData);
        if (ret == STATUS_OK_INTER) {
            break;
        }
        if (ret != NO_DATA_HEAP_ITEM_NOT_EXIST || ChLabelFetchLoopIsBreak(&userScanCondData->auxInfo.subAction)) {
            break;
        }
        ret = STATUS_OK_INTER;
        cursor->scannedPageCnt++;
        if (cursor->scannedPageCnt >= cursor->requireScanPageCnt) {
            cursor->isScanEnd = true;
            break;
        }
        cursor->scanPos.isStartFromPageHead = true;
        if (ChLabelIsOnlyScanOnePage(cursor)) {
            break;
        }
    }
    return ret;
}

StatusInter ChLabelFetchNext(
    ChLabelRunCtxT *ctx, ChLabelScanCursorT *cursor, ChLabelScanSubsCondDataT *userScanCondData)
{
    DB_POINTER3(ctx, cursor, userScanCondData);
    StatusInter ret = STATUS_OK_INTER;
    cursor->isFetched = false;

    while (!cursor->isScanEnd && !cursor->scanPos.isScanOverFlowPage) {
        ret = ChLabelSelectScanPage(ctx, cursor);
        if (ret != STATUS_OK_INTER) {
            break;
        }
        if (cursor->isScanEnd || cursor->scanPos.isScanOverFlowPage) {
            break;
        }
        ret = ChLabelFetchRows(ctx, cursor, userScanCondData);
        if (ret == STATUS_OK_INTER) {
            break;
        }
        if (ret != NO_DATA_HEAP_ITEM_NOT_EXIST || ChLabelFetchLoopIsBreak(&userScanCondData->auxInfo.subAction)) {
            break;
        }
        ret = STATUS_OK_INTER;
        cursor->scannedPageCnt++;
        if (cursor->scannedPageCnt >= cursor->requireScanPageCnt) {
            cursor->isScanEnd = true;
            break;
        }
        cursor->scanPos.isStartFromPageHead = true;
        if (ChLabelIsOnlyScanOnePage(cursor)) {
            break;
        }
    }
    ret = (ret != STATUS_OK_INTER) ? ret : ChLabelFetchNextFromOverFlowPages(ctx, cursor, userScanCondData);
    return ret;
}

bool ChCursorTryLock(ChLabelRunCtxT *ctx)
{
    DB_POINTER(ctx);
    DbSessionCtxT *resSessionCtx = &ctx->seRunCtx->resSessionCtx;
    // 通过try lock 加锁控制cluster hash容器扫描引用计数更新, 解锁操作在上层函数调用ChCursorUnlock
    if (resSessionCtx->isDirectRead) {
        // 转换为latch的addr
        ShmemPtrT lockShmPtr = ctx->openCfg.labelShmAddr;
        GET_MEMBER_SHMPTR(lockShmPtr, offsetof(ClusteredHashLabelT, scanCursorLock));
        return DbRWSpinTryLockOwnerWithSession(
            resSessionCtx, &ctx->chLabel->scanCursorLock, &lockShmPtr, LATCH_ADDR_CLUSTERED_HASH_CURSOR_LOCK);
    } else {
        return DbSpinTryLockOwner(DB_SPINLOCK_OWNER, &ctx->chLabel->scanCursorLock);
    }
}

void ChCursorLock(ChLabelRunCtxT *ctx)
{
    DB_POINTER(ctx);

    DbSessionCtxT *resSessionCtx = &ctx->seRunCtx->resSessionCtx;
    // 加锁控制cluster hash容器扫描引用计数更新, 解锁操作在上层函数调用ChCursorUnlock
    if (resSessionCtx->isDirectRead) {
        ShmemPtrT lockShmPtr = ctx->openCfg.labelShmAddr;
        GET_MEMBER_SHMPTR(lockShmPtr, offsetof(ClusteredHashLabelT, scanCursorLock));
        DbSpinlockOwnerWithSession(
            resSessionCtx, &ctx->chLabel->scanCursorLock, &lockShmPtr, LATCH_ADDR_CLUSTERED_HASH_CURSOR_LOCK);
    } else {
        DbSpinLockOwner(DB_SPINLOCK_OWNER, &ctx->chLabel->scanCursorLock);
    }
}

void ChCursorUnlock(ChLabelRunCtxT *ctx)
{
    DB_POINTER(ctx);

    DbSessionCtxT *resSessionCtx = &ctx->seRunCtx->resSessionCtx;
    if (resSessionCtx->isDirectRead) {
        DbSpinUnlockOwnerWithSession(resSessionCtx, &ctx->chLabel->scanCursorLock);
    } else {
        DbSpinUnlock(&ctx->chLabel->scanCursorLock);
    }
}

StatusInter ChCursorCountUpdate(
    ChLabelRunCtxT *ctx, ShmemPtrT labelLatchShmAddr, uint32_t labelLatchVersionId, bool isIncrease)
{
    DB_POINTER(ctx);

    ShmRefItem item = {.shmType = REF_SHM_CLUSTERED_HASH_CURSOR,
        .latchOffset = offsetof(ClusteredHashLabelT, scanCursorLock),
        .openCntOffset = offsetof(ClusteredHashLabelT, scanCursorRef),
        .refCount = 1,
        .shmAddr = ctx->openCfg.labelShmAddr,
        .labelLatchVersionId = labelLatchVersionId,
        .labelLatchShmAddr = labelLatchShmAddr};
    Status ret = DbSessionUpdateShmRefCount(&ctx->seRunCtx->resSessionCtx, &item, isIncrease);
    if (ret != GMERR_OK) {
        SE_ERROR(ret,
            "clustered hash Cursor update refer count unsucc. isIncrease: %" PRIu32 ", labelId: %" PRIu32
            ", indexId: %" PRIu32,
            (uint32_t)isIncrease, ctx->chLabel->labelCfg.labelId, ctx->chLabel->labelCfg.indexId);
        return DbGetStatusInterErrno(ret);
    }
    return STATUS_OK_INTER;
}

bool ChScanCursorRefTimedMutex(ChLabelRunCtxT *ctx, uint32_t timeoutUs)
{
    DB_POINTER(ctx);
    DB_ASSERT(timeoutUs > 0);
    uint32_t timeRemain = timeoutUs;
    if (timeRemain > CLUSTERED_HASH_OP_TIMEOUT) {
        timeRemain = CLUSTERED_HASH_OP_TIMEOUT;
    }
    while (timeRemain > 0) {
        uint32_t sleepTime =
            timeRemain < CLUSTERED_HASH_OP_BLOCK_SLEEP_TIME ? timeRemain : CLUSTERED_HASH_OP_BLOCK_SLEEP_TIME;
        if (ChCursorTryLock(ctx)) {
            if (ctx->chLabel->scanCursorRef == 0) {
                ctx->chLabel->scanCursorRef = CLUSTERED_HASH_INVALID_CURSOR_NUM;
                ChCursorUnlock(ctx);
                return true;
            } else {
                ChCursorUnlock(ctx);
            }
        }
        timeRemain -= sleepTime;
        if (timeRemain == 0) {
            break;
        }
        DbUsleep(sleepTime);
    }
    return false;
}

StatusInter ChScanCursorRefInc(ChLabelRunCtxT *ctx, const LabelLatchInfoT *chLabelLatchInfoForClt)
{
    StatusInter ret = STATUS_OK_INTER;
    while (true) {
        if (!ChCursorTryLock(ctx)) {
            continue;
        }
        CRASHPOINT(DB_CRASH_EVENT_CLUSTERED_HASH_SCAN_BEGIN, DB_CRASH_STATE_CLUSTERED_HASH_CURSOR_UNLOCK);
        if (ctx->chLabel->scanCursorRef != CLUSTERED_HASH_INVALID_CURSOR_NUM) {
            // 更新引用计数时将该操作记录下来，防止客户端异常退出后引用计数无法复原
            ret = ChCursorCountUpdate(
                ctx, chLabelLatchInfoForClt->labelLatchShmAddr, chLabelLatchInfoForClt->labelLatchVersionId, true);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                ChCursorUnlock(ctx);
                return ret;
            }
            ctx->chLabel->scanCursorRef++;
            CRASHPOINT(DB_CRASH_EVENT_CLUSTERED_HASH_SCAN_BEGIN, DB_CRASH_STATE_CLUSTERED_HASH_CURSOR_REF_SAVED);
            ChCursorUnlock(ctx);
            break;
        }
        ChCursorUnlock(ctx);
    }
    // 注入异常退出点: 引用计数++，但是未--就异常退出
    CRASHPOINT(DB_CRASH_EVENT_CLUSTERED_HASH_SCAN_BEGIN, DB_CRASH_STATE_SESSION_REF_UPDATE);
    return ret;
}

void ChScanCursorRefDec(ChLabelRunCtxT *ctx, bool isScaleIn)
{
    DB_POINTER(ctx);
    LabelRWLatchT *labelRWLatch = ctx->openCfg.labelRWLatch;
    uint32_t labelLatchVersionId = ctx->openCfg.labelLatchVersionId;
    Status checkRet = LabelLatchCheckVersion(labelRWLatch, labelLatchVersionId);
    if (checkRet != GMERR_OK) {
        SE_ERROR(checkRet,
            "label have been dropped, labelRWLatchVersionId: %" PRIu32 ", labelLatchVersionId: %" PRIu32 ".",
            labelRWLatch->versionId, labelLatchVersionId);
        return;
    }
    ChCursorLock(ctx);
    CRASHPOINT(DB_CRASH_EVENT_CLUSTERED_HASH_SCAN_END, DB_CRASH_STATE_CLUSTERED_HASH_CURSOR_UNLOCK);
    if (!isScaleIn) {
        DB_ASSERT(ctx->chLabel->scanCursorRef > 0);
        ctx->chLabel->scanCursorRef--;
        // 必须先将引用计数--，再将引用计数从记录中移除，只要引用计数的记录还在就可以恢复
        CRASHPOINT(DB_CRASH_EVENT_CLUSTERED_HASH_SCAN_END, DB_CRASH_STATE_CLUSTERED_HASH_CURSOR_REF_NOT_SAVE);
        StatusInter ret = ChCursorCountUpdate(ctx, DB_INVALID_SHMPTR, DB_INVALID_UINT32, false);
        DB_ASSERT(ret == STATUS_OK_INTER);
        CRASHPOINT(DB_CRASH_EVENT_CLUSTERED_HASH_SCAN_END, DB_CRASH_STATE_CLUSTERED_HASH_CURSOR_REF_SAVED);
    } else {
        ctx->chLabel->scanCursorRef = 0;
    }
    ChCursorUnlock(ctx);
}

inline static void ChExpandedListInit(ChExpandedInfoListT *expanedList)
{
    DbSpinInit(&expanedList->lock);
    DbLinkedListInit(&expanedList->list);
}

inline static void ChLabelSetScanCursor(
    ChLabelRunCtxT *ctx, const ChLabelBeginScanCfgT *cfg, ChLabelScanCursorT *cursor)
{
    cursor->ctx = ctx;
    cursor->isFirstFetch = true;
    cursor->isFetched = false;
    cursor->isScanEnd = false;
    cursor->actualScanRowsCnt = 0;
    cursor->scanMode = cfg->scanMode;
    cursor->isRecovery = cfg->isRecovery;
}

void ChLabelScanCursorPosInit(ChLabelRunCtxT *ctx, const ChLabelBeginScanCfgT *cfg, ChLabelScanCursorT *cursor)
{
    cursor->scanPos.isStartFromPageHead = cfg->isDefragmentation;  // 碎片整理肯定是整页搜索
    // 碎片整理目前不考虑溢出页，如果后面要多溢出页做整理, 则需要做相应的适配
    cursor->scanPos.segmentPos.pattern = cfg->beginAddr.segmentPos.pattern;
    cursor->scanPos.segmentPos.depth = cfg->beginAddr.segmentPos.depth;
    cursor->scanPos.isScanOverFlowPage = cfg->beginAddr.segmentPos.isOverFlowPage;
    if (cfg->beginAddr.deviceId == 0 && cfg->beginAddr.blockId == 0) {
        cursor->scanPos.pageAddr = (PageIdT){DB_INVALID_UINT32, DB_INVALID_UINT32};
    } else {
        cursor->scanPos.pageAddr.deviceId = cfg->beginAddr.deviceId;
        cursor->scanPos.pageAddr.blockId = cfg->beginAddr.blockId;
    }

    ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(cfg->beginAddr.rowId);
    cursor->scanPos.phyRowId = ChLabelGetPhysicalAddrByLogicalAddr(ctx, logicRowId);
    if (cfg->beginAddr.segmentPos.isOverFlowPage) {
        // 上一次正在扫描溢出页，则记录的tupleId一定不小于hashEntryNumPerPage
        cursor->scanPos.overFlowBlockId = ClusteredHashGetOverFlowPageBlockId(ctx, logicRowId.tupleId);
        cursor->scanPos.overFlowTupleId = ClusteredHashGetInnerTupleId(ctx, logicRowId.tupleId);
    } else {
        cursor->scanPos.overFlowBlockId = 0;
        cursor->scanPos.overFlowTupleId = 0;
    }
}

inline static void ChLabelScanCursorFromHead(ChLabelScanCursorT *cursor)
{
    cursor->scanPos.isStartFromPageHead = true;
    cursor->scanPos.isScanOverFlowPage = false;
    cursor->scanPos.segmentPos.pattern = 0;
    cursor->scanPos.segmentPos.depth = 0;
    cursor->scanPos.overFlowBlockId = 0;
    cursor->scanPos.overFlowTupleId = 0;
    cursor->scanPos.pageAddr = (PageIdT){DB_INVALID_UINT32, DB_INVALID_UINT32};
}

StatusInter ChLabelScanCursorInit(ChLabelRunCtxT *ctx, const ChLabelBeginScanCfgT *cfg, ChLabelScanCursorT *cursor)
{
    (void)memset_s(cursor, sizeof(ChLabelScanCursorT), 0, sizeof(ChLabelScanCursorT));
    ChLabelSetScanCursor(ctx, cfg, cursor);
    uint32_t maxTupleCount;
    if (ctx->pageCtx.segAddr.virtAddr == NULL) {
        maxTupleCount = DB_MAX(ctx->chLabel->dashMeta.metaData.tupleCntPerPage,
            ctx->chLabel->dashMeta.metaData.maxTupleCntPerOverFlowPage);
    } else {
        ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataByPageCtx(&ctx->pageCtx);
        maxTupleCount = DB_MAX(pageData->tupleCntPerPage, pageData->maxTupleCntPerOverFlowPage);
    }

    cursor->maxFetchNum = (cfg->scanMode == CH_SCAN_ONE_PAGE) ? maxTupleCount : cfg->maxFetchNum;

    if (cfg->beginAddr.rowId == BEFORE_FIRST_TUPLE_ADDR) {  // 表示执行第一个tuple之前，那么从头开始搜索
        ChLabelScanCursorFromHead(cursor);
    } else {
        ChLabelScanCursorPosInit(ctx, cfg, cursor);
    }

    if (SECUREC_UNLIKELY(cfg->isDefragmentation)) {
        cursor->isDefragmentation = true;
        cursor->requireScanPageCnt = 1;  // 后台碎片整理线程一次只扫描一个页
    } else {
        cursor->requireScanPageCnt = 0xFFFFFFFFu;
    }
    ChExpandedListInit(&cursor->expandedList);
    return STATUS_OK_INTER;
}

StatusInter ChLabelAllocScanCursor(ChLabelRunCtxT *ctx, const ChLabelBeginScanCfgT *cfg, ChLabelScanCursorT **cursor)
{
    DB_POINTER3(ctx, cfg, cursor);
    // 最后由上层调用 ChScanCursorDestory 进行释放;
    ChLabelScanCursorT *newScanCursor = DbDynMemCtxAlloc(ctx->seRunCtx->sessionMemCtx, sizeof(ChLabelScanCursorT));
    if (newScanCursor == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "out of memory, labelId: %" PRIu32 ", indexId: %" PRIu32,
            ctx->chLabel->labelCfg.labelId, ctx->chLabel->labelCfg.indexId);
        return OUT_OF_MEMORY_INTER;
    }
    StatusInter ret = ChLabelScanCursorInit(ctx, cfg, newScanCursor);
    if (ret != STATUS_OK_INTER) {
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(ctx->seRunCtx->sessionMemCtx, newScanCursor);
        newScanCursor = NULL;
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    *cursor = newScanCursor;
    return STATUS_OK_INTER;
}

Status ChLabelScan(ChLabelRunHdlT ctx, ChLabelScanCursorHdlT chLabelCursor, ChLabelScanSubsCondDataT *userScanCondData,
    HpFetchedAuxInfoT *auxInfo)
{
    DB_POINTER2(ctx, chLabelCursor);
    ChLabelScanCursorT *cursor = chLabelCursor;
    cursor->actualScanRowsCnt = 0;
    userScanCondData->auxInfo.subAction = (HpScanSubsActT){0};
    userScanCondData->userAppendRowIdProc = NULL;
#ifndef NDEBUG
    ChLabelRunCtxT *runCtx = ctx;
    DB_ASSERT(runCtx->openCfg.labelRWLatch->rwlatch.latchMode != (uint32_t)DB_LATCH_IDLE);
    if (runCtx->isHoldHcIndexLockByUpper && DbCommonIsServer()) {
        DB_ASSERT(runCtx->openCfg.labelRWLatch->hcLatch.latchMode != (uint32_t)DB_LATCH_IDLE);
    }
#endif
    StatusInter ret = ChLabelFetchNext(ctx, cursor, userScanCondData);
    ChLabelTupleCombineAddr *hpTupleAddr = &userScanCondData->auxInfo.curHeapTupleAddr;
    hpTupleAddr->rowId = chLabelCursor->fetchRowInfo.addr;
    hpTupleAddr->segmentPos.pattern = cursor->scanPos.segmentPos.pattern;
    hpTupleAddr->segmentPos.depth = cursor->scanPos.segmentPos.depth;
    hpTupleAddr->segmentPos.isOverFlowPage = cursor->scanPos.isScanOverFlowPage;
    hpTupleAddr->deviceId = cursor->scanPos.pageAddr.deviceId;
    hpTupleAddr->blockId = cursor->scanPos.pageAddr.blockId;
    auxInfo->actualFetchRowsCnt = cursor->actualScanRowsCnt;
    auxInfo->isEof = cursor->isScanEnd;
#ifndef NDEBUG
    DB_ASSERT(runCtx->openCfg.labelRWLatch->rwlatch.latchMode != (uint32_t)DB_LATCH_IDLE);
    if (runCtx->isHoldHcIndexLockByUpper && DbCommonIsServer()) {
        DB_ASSERT(runCtx->openCfg.labelRWLatch->hcLatch.latchMode != (uint32_t)DB_LATCH_IDLE);
    }
#endif
    return DbGetExternalErrno(ret);
}

StatusInter ChLabelFetchNextHandleByUserProc4ScanDel(ChLabelScanCursorT *cursor,
    ChLabelScanDeletePrepare scanDeletePrepare, bool *isContinueFetchNext, ChLabelScanProcCtxT *procCtx)
{
    *isContinueFetchNext = false;
    ChLabelRunCtxT *chRunCtx = cursor->ctx;
    HeapTupleBufAndRowInfoT heapBuffAndRowInfo;
    ChLabelFetchNextHandleInitBuffAndRowInfo(cursor, chRunCtx, &heapBuffAndRowInfo);
    bool isMatch = false;
    // QryMatchAgeCondAndGenerateSubsFunc
    Status ret = scanDeletePrepare(procCtx->deleteCtx.userData, &heapBuffAndRowInfo.tupleBuf, &isMatch);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret,
            "Clustered hash prepare before update unsucc, trxId is: %" PRIu64 ", blockId is:%" PRIu32
            ", labelId: %" PRIu32 ", indexId: %" PRIu32,
            heapBuffAndRowInfo.fetchedRowInfo.trxId, heapBuffAndRowInfo.fetchedRowInfo.curHeapTupleAddr.blockId,
            chRunCtx->chLabel->labelCfg.labelId, chRunCtx->chLabel->labelCfg.indexId);
        return DbGetStatusInterErrno(ret);
    }

    if (isMatch) {
        StatusInter retInter = DbGetStatusInterErrno(TupleBufPut(
            chRunCtx->openCfg.tupleBuf, heapBuffAndRowInfo.tupleBuf.bufSize, heapBuffAndRowInfo.tupleBuf.buf));
        if (SECUREC_UNLIKELY(retInter != STATUS_OK_INTER)) {
            return retInter;
        }
    } else {
        *isContinueFetchNext = true;
    }
    return STATUS_OK_INTER;
}

ALWAYS_INLINE static void ClusteredHashFreeOneSegPageAfterScanDeleteProc(
    ChLabelRunCtxT *runCtx, ChLabelScanCtxT *scanCtx)
{
    if (scanCtx->isPageEmpty && runCtx->pageCtx.dirSeg != NULL) {
        ClusteredHashFreeOneSegPageAfterDelete(runCtx, *runCtx->pageCtx.dirSeg);
        (void)ClusteredHashInitPageCtx(runCtx, &runCtx->pageCtx,
            scanCtx->idxKey.keyLen == 0 || scanCtx->idxKey.keyData == NULL ? scanCtx->phyRowId.hashCode :
                                                                             HtGetHashCode(scanCtx->idxKey));
    }
}

ALWAYS_INLINE static void ClusteredHashErasePageScanDel(
    ChLabelRunCtxT *runCtx, ChRowHeadT *rowHead, ClusteredHashEntryT *hashEntry, ChLabelScanCtxT *scanCtx)
{
    DB_POINTER2(rowHead, hashEntry);
#ifndef NDEBUG
    if (runCtx->lockPage != NULL && runCtx->labelVarInfo->upgradeVersion == 0) {
        DB_ASSERT(runCtx->lockPage == runCtx->pageCtx.segAddr.virtAddr);
    }
#endif
    ClusteredHashEntryClear(hashEntry);
    scanCtx->isPageEmpty =
        TupleSlotFree(&runCtx->pageCtx, scanCtx->phyRowId.tupleId, runCtx->labelVarInfo, &runCtx->chLabel->dashMeta);
}

StatusInter ClusteredBucketEraseByIdScanDel(ChLabelRunCtxT *runCtx, ChLabelScanCtxT *scanCtx)
{
    if (SECUREC_UNLIKELY(!scanCtx->isFind)) {
        return NO_DATA_INTER;
    }
    StatusInter ret = ClusteredHashDeleteUndoLog(
        runCtx, scanCtx->logicRowId, scanCtx->phyRowId.tupleId, scanCtx->targetBucket, scanCtx->bucket);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    // 缓存当前pageCtx
    ChLabelPageCtxT pageCtxCache = runCtx->pageCtx;
    ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
    const DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    ClusteredHashEntryT *hashEntry = NULL;
    if (SECUREC_LIKELY(!scanCtx->isCheckOverFlowPages)) {
        ChRowHeadT *rowHead =
            ClusteredHashGetRowHeadByTupleId(pageCtx->tupleHdr, runCtx->pageCtx.pageMetaData.tupleSize,
                runCtx->chLabel->labelCfg.slotExtendSize, (uint16_t)scanCtx->phyRowId.tupleId);
        ret = ClusteredHashBucketEraseHcIndex(runCtx, scanCtx->logicRowId, rowHead, &pageCtxCache);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            ClusteredHashRsmUndoLogMovePrevRec(runCtx);
            return ret;
        }
        if (SECUREC_UNLIKELY(scanCtx->isStashBucket)) {
            ChStashBucketMetaT *stashBucket = (ChStashBucketMetaT *)(void *)scanCtx->bucket;
            hashEntry =
                ClusteredGetEntryByStashBucket(stashBucket, scanCtx->entrySlotInBucket, dashMemMeta->hashEntrySize);
            ClusteredHashClearStashBucketScanDel(stashBucket, scanCtx);
        } else {
            hashEntry = ClusteredGetEntryByBucket(
                (ChBucketMetaT *)(void *)scanCtx->bucket, scanCtx->entrySlotInBucket, dashMemMeta->hashEntrySize);
            ClusteredHashNormalBucketFreeSlot((ChBucketMetaT *)(void *)scanCtx->bucket, scanCtx->entrySlotInBucket);
        }
        ClusteredHashErasePageScanDel(runCtx, rowHead, hashEntry, scanCtx);
    } else {  // 要删除的数据在溢出页
        ret = ClusteredHashEraseOverflowPageHcScanDel(runCtx, scanCtx);
        // 由于删除hashcluster索引会刷新pageCtx，因此需要重新初始化一遍
        runCtx->pageCtx = pageCtxCache;
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, merge delete HcIndex unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            ClusteredHashRsmUndoLogMovePrevRec(runCtx);
            return ret;
        }
        ClusteredHashEraseOverflowPageScanDel(runCtx, scanCtx);
    }
    ClusteredHashStatDelete(runCtx);
    ClusteredHashRsmUndoLogMovePrevRec(runCtx);
    return STATUS_OK_INTER;
}

// 入参rowId是经过scan得到的，应为物理rowId
StatusInter ClusteredHashLookupByPhyRowIdScanDel(ChLabelRunCtxT *runCtx, ChPhyRowId phyRowId, ChLabelScanCtxT *scanCtx)
{
    ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
    StatusInter ret = ClusteredHashInitPageCtx(runCtx, pageCtx, phyRowId.hashCode);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    // tupleId超过正常segment页的tupleId，说明要访问的记录在溢出页中
    if (SECUREC_UNLIKELY(
            phyRowId.tupleId >= pageCtx->pageMetaData.tupleCntPerPage && runCtx->labelVarInfo->upgradeVersion == 0)) {
        return ClusteredHashLookupByPhyRowIdForOverFlowPagesScanDel(runCtx, phyRowId, scanCtx);
    }
    // 外层有加锁,这里不加锁
    ChLabelLookupCtxT lookUpCtx =
        ClusteredHashInitLookUpCtxByRowId(phyRowId.hashCode, phyRowId.version, phyRowId.tupleId, false, false);
    // 由于是fetch流程，procCtx->needGetBucket 都为true.(原逻辑为false时才给isFind赋值为true)
    ret = ClusteredHashFindEntryInPageChain(runCtx, &lookUpCtx);
#ifndef NDEBUG
    DB_ASSERT(!lookUpCtx.notExistsInBloomFilter || !lookUpCtx.isFind);
#endif
    if (ret != STATUS_OK_INTER || !TupleBitMapIsSet(pageCtx->bitmapHdr, phyRowId.tupleId)) {
        return NO_DATA_INTER;
    }
    ClusteredHashLookupByPhyRowIdGetScanCtx(runCtx, phyRowId, lookUpCtx, scanCtx);
    return ret;
}

StatusInter ChLabelFetchRowsInOnePage4ScanDel(ChLabelRunCtxT *ctx, ChLabelScanCursorT *cursor,
    ChLabelScanDeletePrepare scanDeletePrepare, ChLabelScanProcCtxT *procCtx, ChLabelScanCtxT *scanCtx)
{
    ChLabelPageFetchRowInfoT *fetchRowInfo = &cursor->fetchRowInfo;
    ClusteredHashPageLock(ctx, false, fetchRowInfo->firstPageHead, &fetchRowInfo->firstPageAddr);
    StatusInter ret = ChLabelFetchRowsInPageChain4ScanDel(ctx, cursor, scanDeletePrepare, procCtx, scanCtx);
    ClusteredHashPageUnlock(ctx, false, fetchRowInfo->firstPageHead);
    ClusteredHashFreeOneSegPageAfterScanDeleteProc(ctx, scanCtx);
    return ret;
}

StatusInter ChLabelFetchRowsInOneOverFlowPage4ScanDel(ChLabelRunCtxT *ctx, ChLabelScanCursorT *cursor,
    ChLabelScanDeletePrepare scanDeletePrepare, ChLabelScanProcCtxT *procCtx, ChLabelScanCtxT *scanCtx)
{
    ChLabelPageFetchRowInfoT *fetchRowInfo = &cursor->fetchRowInfo;
    ClusteredHashPageLock(ctx, false, fetchRowInfo->segPageHead, &fetchRowInfo->pageAddr);
    StatusInter retInter = STATUS_OK_INTER;
    bool isContinueFetchNext = true;
    while (isContinueFetchNext) {
        retInter = ChLabelFetchNextValidTupleFromOverFlowPage(cursor, fetchRowInfo);
        if (retInter != STATUS_OK_INTER) {
            break;
        }
        retInter = ChLabelFetchNextHandleByUserProc4ScanDel(cursor, scanDeletePrepare, &isContinueFetchNext, procCtx);
        if (retInter != STATUS_OK_INTER) {
            break;
        }
        if (isContinueFetchNext) {
            continue;
        }
        retInter = ClusteredBucketEraseByIdScanDel(ctx, scanCtx);
        if (retInter == STATUS_OK_INTER) {
            procCtx->deleteCtx.userData->rowId = *(uint64_t *)&cursor->scanPos.phyRowId;
        }
    }
    ClusteredHashPageUnlock(ctx, false, fetchRowInfo->segPageHead);
    ClusteredHashFreeOneSegPageAfterScanDeleteProc(ctx, scanCtx);
    return retInter;
}

StatusInter ChLabelFetchNextFromOverFlowPages4ScanDel(ChLabelRunCtxT *ctx, ChLabelScanCursorT *chLabelCursor,
    ChLabelScanDeletePrepare scanDeletePrepare, ChLabelScanProcCtxT *procCtx)
{
    ChLabelScanCtxT scanCtx = {0};
    if (chLabelCursor->isScanEnd || !chLabelCursor->scanPos.isScanOverFlowPage) {
        return STATUS_OK_INTER;
    }
    StatusInter ret = STATUS_OK_INTER;
    chLabelCursor->isFetched = false;

    while (!chLabelCursor->isScanEnd) {
        // select one overflow page
        ret = ChLabelSelectScanOverFlowPage(ctx, chLabelCursor);
        if (ret != STATUS_OK_INTER) {
            break;
        }
        if (chLabelCursor->isScanEnd) {
            procCtx->deleteCtx.userData->isScanEnd = chLabelCursor->isScanEnd;
            break;
        }
        // fetch mutiply rows from current overFlow Page and update overFlowTupleId
        ret = ChLabelFetchRowsInOneOverFlowPage4ScanDel(ctx, chLabelCursor, scanDeletePrepare, procCtx, &scanCtx);
        if (ret == STATUS_OK_INTER) {
            break;
        }
        if (ret != NO_DATA_HEAP_ITEM_NOT_EXIST) {
            break;
        }
        chLabelCursor->scannedPageCnt++;
        ret = STATUS_OK_INTER;
        if (chLabelCursor->scannedPageCnt >= chLabelCursor->requireScanPageCnt) {
            chLabelCursor->isScanEnd = true;
            procCtx->deleteCtx.userData->isScanEnd = chLabelCursor->isScanEnd;
            break;
        }
        chLabelCursor->scanPos.isStartFromPageHead = true;
        if (ChLabelIsOnlyScanOnePage(chLabelCursor)) {
            break;
        }
    }
    return ret;
}

Status ClusteredHashScanEntry(ChLabelRunHdlT ctx, ChLabelScanDeletePrepare scanDeletePrepare,
    ChLabelScanCursorHdlT chLabelCursor, ChLabelScanProcCtxT *procCtx)
{
    DB_POINTER2(ctx, procCtx);
    ChLabelScanCursorT *chCursor = chLabelCursor;
    // 在 ClusteredHashLookupByRowIdGetScanCtx 中注入必要参数
    ChLabelScanCtxT scanCtx = {0};
    StatusInter ret = STATUS_OK_INTER;
    chCursor->isFetched = false;

    while (!chCursor->isScanEnd && !chCursor->scanPos.isScanOverFlowPage) {
        ret = ChLabelSelectScanPage(ctx, chCursor);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            break;
        }
        if (chCursor->isScanEnd || chCursor->scanPos.isScanOverFlowPage) {
            procCtx->deleteCtx.userData->isScanEnd = chCursor->isScanEnd;
            break;
        }
        ret = ChLabelFetchRowsInOnePage4ScanDel(ctx, chCursor, scanDeletePrepare, procCtx, &scanCtx);
        if (ret == STATUS_OK_INTER) {
            break;
        }
        if (ret != NO_DATA_HEAP_ITEM_NOT_EXIST) {
            break;
        }
        ret = STATUS_OK_INTER;
        chCursor->scannedPageCnt++;
        if (chCursor->scannedPageCnt >= chCursor->requireScanPageCnt) {
            chCursor->isScanEnd = true;
            procCtx->deleteCtx.userData->isScanEnd = chCursor->isScanEnd;
            break;
        }
        chCursor->scanPos.isStartFromPageHead = true;
        if (ChLabelIsOnlyScanOnePage(chCursor)) {
            break;
        }
    }
    ret = (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) ?
              ret :
              ChLabelFetchNextFromOverFlowPages4ScanDel(ctx, chCursor, scanDeletePrepare, procCtx);
    return DbGetExternalErrno(ret);
}

// 内部直接调用了删除接口
Status ChLabelScanDelete(ChLabelRunHdlT chRunHdl, ChLabelScanCursorHdlT chLabelCursor,
    ChLabelScanDeletePrepare scanDeletePrepare, ChLabelScanDeleteParaT *userData)
{
    DB_POINTER2(chRunHdl, chLabelCursor);
    ChLabelScanProcCtxT procCtx;
    procCtx.opType = CHLABEL_SCAN_OPT_DELETE;
    procCtx.deleteCtx = (ChLabelScanDeleteCtxT){userData};
    return ClusteredHashScanEntry(chRunHdl, scanDeletePrepare, chLabelCursor, &procCtx);
}

void ChScanCursorDestroy(const ChLabelRunHdlT runHdl, ChLabelScanCursorHdlT heapCursor)
{
    DB_POINTER2(runHdl, heapCursor);
    ChLabelScanCursorT *cursor = heapCursor;
    ChLabelRunCtxT *chLabelRunCtx = (ChLabelRunCtxT *)runHdl;
    ChScanExpandedInfoT *node = NULL;
    ChScanExpandedInfoT *tmpNode = NULL;
    DbSpinLock(&cursor->expandedList.lock);
    LIST_FOR_EACH_ENTRY_SAFE(node, tmpNode, &cursor->expandedList.list, expandNode)
    {
        DbLinkedListRemove(&node->expandNode);
        DbDynMemCtxFree(cursor->ctx->seRunCtx->sessionMemCtx, node);
        node = NULL;
    }
    DbSpinUnlock(&cursor->expandedList.lock);
    ClusteredHashReleaseTuple(chLabelRunCtx, &cursor->fetchRowInfo.cacheBuf);
    // 释放后，外部函数不再访问，未置NULL，无UAF风险
    DbDynMemCtxFree(chLabelRunCtx->seRunCtx->sessionMemCtx, cursor);
}

Status ChLabelBeginScanCursor(
    const ChLabelRunHdlT chRunHdl, const ChLabelBeginScanCfgT *beginScanCfg, ChLabelScanCursorHdlT *heapCursor)
{
    DB_POINTER(chRunHdl);
#ifndef NDEBUG
    ChLabelRunCtxT *runCtx = chRunHdl;
    DB_ASSERT(runCtx->openCfg.labelRWLatch->rwlatch.latchMode != (uint32_t)DB_LATCH_IDLE || beginScanCfg->isRecovery);
    if (runCtx->isHoldHcIndexLockByUpper && DbCommonIsServer() && !DbCommonGetWarmReboot()) {
        DB_ASSERT(runCtx->openCfg.labelRWLatch->hcLatch.latchMode != (uint32_t)DB_LATCH_IDLE);
    }
#endif
    StatusInter ret = ChLabelAllocScanCursor(chRunHdl, beginScanCfg, (ChLabelScanCursorT **)heapCursor);
    if (ret != STATUS_OK_INTER) {
        return DbGetExternalErrno(ret);
    }
    ret = ChScanCursorRefInc(chRunHdl, &beginScanCfg->chLabelLatchInfoForClt);
    if (ret != STATUS_OK_INTER) {
        ChScanCursorDestroy(chRunHdl, *heapCursor);
    }
#ifndef NDEBUG
    DB_ASSERT(runCtx->openCfg.labelRWLatch->rwlatch.latchMode != (uint32_t)DB_LATCH_IDLE || beginScanCfg->isRecovery);
    if (runCtx->isHoldHcIndexLockByUpper && DbCommonIsServer() && !DbCommonGetWarmReboot()) {
        DB_ASSERT(runCtx->openCfg.labelRWLatch->hcLatch.latchMode != (uint32_t)DB_LATCH_IDLE);
    }
#endif
    return DbGetExternalErrno(ret);
}

Status ChLabelPin(const ChLabelRunHdlT chRunHdl)
{
    DB_POINTER(chRunHdl);
    LabelLatchInfoT chLabelLatchInfo = ChLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR);
    return DbGetExternalErrno(ChScanCursorRefInc(chRunHdl, &chLabelLatchInfo));
}

void ChLabelUnPin(const ChLabelRunHdlT chRunHdl)
{
    DB_POINTER(chRunHdl);
    ChScanCursorRefDec(chRunHdl, false);
    if (DbCommonIsServer()) {
        // 约束使用场景，按需拓展
        DB_ASSERT(chRunHdl->openCfg.labelRWLatch->hcLatch.latchMode == (uint32_t)DB_LATCH_SERVER_X ||
                  !chRunHdl->isHoldHcIndexLockByUpper);
        ClusteredHashArrangeFreeEmptyPage(chRunHdl);
    }
}

void ChLabelEndScan(const ChLabelRunHdlT runHdl, ChLabelScanCursorHdlT heapCursor)
{
    DB_POINTER2(runHdl, heapCursor);
    ChScanCursorRefDec((ChLabelRunCtxT *)runHdl, false);
    ChScanCursorDestroy(runHdl, heapCursor);
}

void ChLabelFetchNextAppendRowId(void *userData, uint32_t tupleBufPos, HpTupleAddr tupleAddr)
{
    DB_POINTER(userData);
    HpAmBatchFetchTpBufsT *batchTupBufInfo = (HpAmBatchFetchTpBufsT *)userData;
    HeapTupleBufAndRowInfoT *heapCopyTupleBuf = &batchTupBufInfo->heapTupleBuf[tupleBufPos];
    heapCopyTupleBuf->fetchedRowInfo.curHeapTupleAddr.tupleAddr = tupleAddr;
}

void ChLabelFetchNextAppendRowInfo(void *userData, uint32_t tupleBufPos, HpTupleAddr tupleAddr)
{
    DB_POINTER(userData);
    HpAmScannedFetchTpBufsT *batchTupBufInfo = (HpAmScannedFetchTpBufsT *)userData;
    HeapFetchedRowInfoT *heapCopyTupleBuf = &batchTupBufInfo->tupleBufWithRowInfo[tupleBufPos];
    heapCopyTupleBuf->curHeapTupleAddr.tupleAddr = tupleAddr;
}

Status ChLabelFetchNextTuples(ChLabelRunHdlT runHdl, ChLabelScanCursorHdlT chLabelScanCursor, TupleBufT heapTupleBufs[],
    HeapFetchedRowInfoT fetchedRowInfo[], ChLabelFetchedAuxInfoT *auxInfo)
{
    DB_POINTER2(chLabelScanCursor, fetchedRowInfo);
    ChLabelScanCursorT *cursor = chLabelScanCursor;
    ChLabelRunCtxT *chRunHdl = runHdl;

    cursor->actualScanRowsCnt = 0;
    ChLabelScanSubsCondDataT userScanCondData = {0};
    HpAmScannedFetchTpBufsT tupleBuf = {.bufs = heapTupleBufs, .tupleBufWithRowInfo = fetchedRowInfo};
    userScanCondData.userData = (uint8_t *)&tupleBuf;
    userScanCondData.userScanRowProc = HeapScanCopyHpTupleBuf;
    userScanCondData.userAppendRowIdProc = ChLabelFetchNextAppendRowInfo;
    StatusInter ret = ChLabelFetchNext(chRunHdl, cursor, &userScanCondData);
    if (cursor->isFetched) {
        DB_ASSERT(cursor->actualScanRowsCnt <= cursor->maxFetchNum);
        for (uint32_t i = 0; i < cursor->actualScanRowsCnt; i++) {
            heapTupleBufs[i] = tupleBuf.bufs[i];
        }
    }
    auxInfo->actualFetchRowsCnt = cursor->actualScanRowsCnt;
    auxInfo->isEof = cursor->isScanEnd;
    for (uint32_t i = 0; i < cursor->actualScanRowsCnt; i++) {
        ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(fetchedRowInfo[i].curHeapTupleAddr.tupleAddr);
        fetchedRowInfo[i].curHeapTupleAddr.tupleAddr = TransformChLogicRowId(logicRowId);
        fetchedRowInfo[i].curHeapTupleAddr.pageBlockId = cursor->scanPos.pageAddr.blockId;
        fetchedRowInfo[i].curHeapTupleAddr.pageDeviceId = cursor->scanPos.pageAddr.deviceId;
    }
    return DbGetExternalErrno(ret);
}

#ifdef __cplusplus
}
#endif
