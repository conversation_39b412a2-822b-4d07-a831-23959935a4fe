/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: se_clustered_hash_label_upgrade.h
 * Description: clustered hash label upgrade header file
 * Author: lujiahao
 * Create: 2023/09/22
 */
#ifndef SE_CLUSTERED_HASH_LABEL_UPGRADE_H
#define SE_CLUSTERED_HASH_LABEL_UPGRADE_H

#include "se_clustered_hash_label_base.h"
#include "se_clustered_hash_label_dml.h"
#include "se_clustered_hash_label_scalein.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CLUSTERED_HASH_UPGRADE_MERGE_TIME 100  // 100ms
#define CLUSTERED_HASH_MERGE_PAGE_COUNT 2

typedef struct ChLabelPageMergeCtx {
    ChLabelSegmentPageT *dstSeg;
    ChLabelSegmentPageT *srcSeg;
    uint8_t *dstPageBackup;
    uint8_t *srcPageBackup;
    uint8_t *targetSegPageAddr;
    uint32_t pageSize;
    bool isGc;
    RsmUndoChMulVersionScaleInRec *rsmUndoChMulVersionScaleInRec;
} ChLabelPageMergeCtxT;

StatusInter ClusteredHashCheckPageRowSize(ChLabelRunCtxT *runCtx, HashDirSegmentT *dirSeg, DbMemAddrT *segAddr);

StatusInter ClusteredHashAllocNewPage4MulVersion(ChLabelRunCtxT *runCtx, HashDirSegmentT *dirSeg, DbMemAddrT *segAddr);

StatusInter ClusteredHashFindEntryInPageChain(ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx);

StatusInter ClusteredHashFindEntryInPageChain4MulVersion(ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx);

StatusInter ClusteredHashGetNextPage(ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, PageIdT pageAddr);

StatusInter ClusteredUpdate4MulVersion(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelUpdateCtxT *updateCtx, ChRowHeadT *rowHead);

StatusInter ClusteredUpdateRollBack4MulVersion(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelUpdateCtxT *updateCtx, ChRowHeadT *oldRowHead);

void ClusteredHashRollBackPageChainSplit(ChLabelRunCtxT *runCtx, ChLabelExpandCtxT *expandCtx);

StatusInter ClusteredHashPageChainSplit(ChLabelRunCtxT *runCtx, ChLabelExpandCtxT *expandCtx);

StatusInter ClusteredHashPageChainBucketsSplit(
    ChLabelRunCtxT *runCtx, ChLabelExpandCtxT *expandCtx, uint32_t patternSpan);

StatusInter ClusteredHashProcOldPageChain(ChLabelRunCtxT *runCtx);

bool ClusteredHashIsChainMatch(ChLabelRunCtxT *runCtx, ChLabelPageHeaderT *pageHeadB, ChLabelPageHeaderT *pageHeadA,
    ChLabelPageHeaderT **targetPageHead, uint8_t **targetSegPageAddr);

bool ClusteredHashIsPageChainSatisfy(ChLabelRunCtxT *runCtx, ChLabelPageHeaderT *targetPageHead,
    uint8_t *targetSegPageAddr, ChLabelSegmentPageT *segmentB, ChLabelSegmentPageT *segmentA);

void ClusteredHashMergePageChain(ChLabelRunCtxT *runCtx, ChLabelPageMergeCtxT *mergeCtx);

void ClusteredHashMergePageChain4Rsm(ChLabelRunCtxT *runCtx, ChLabelPageMergeCtxT *mergeCtx);

StatusInter ClusteredHashFreeSegPageChain(ChLabelPageMemRunCtxT *memRunCtx, RsmUndoRecordT *rsmUndoRec,
    HashDirSegmentT *seg, uint8_t **segPageAddr, EhTrcPageCacheT *cache);

void ChLabelGetPageChainScanSegment(ChLabelRunCtxT *ctx, ChLabelScanCursorT *cursor, HashDirSegmentT *segment,
    ChLabelPageMetaDataT *pageData, uint8_t **segPageAddr);

StatusInter ChLabelFetchRowsInPageChain(
    ChLabelRunCtxT *ctx, ChLabelScanCursorT *cursor, ChLabelScanSubsCondDataT *userScanCondData);

StatusInter ChLabelFetchRowsInPageChain4ScanDel(ChLabelRunCtxT *ctx, ChLabelScanCursorT *cursor,
    ChLabelScanDeletePrepare scanDeletePrepare, ChLabelScanProcCtxT *procCtx, ChLabelScanCtxT *scanCtx);

StatusInter ClusteredHashFindSegAndMoveData(ChLabelRunCtxT *runCtx);

inline static bool ClusteredHashIsInvalidPageId(PageIdT pageAddr)
{
    return pageAddr.deviceId == DB_INVALID_UINT32 || pageAddr.blockId == DB_INVALID_UINT32;
}

StatusInter ClusteredHashInsert4MulVersion(
    ChLabelRunCtxT *runCtx, IndexKeyT idxKey, const HeapTupleBufT *tupleBuf, ChLogicRowId *logicRowId);

void ClusteredHashFreeMergeSegment(
    ChLabelRunCtxT *runCtx, ChLabelSegmentPageT *segmentA, ChLabelSegmentPageT *segmentB, PageIdT pageAddr);

void ClusteredHashMergePageChainRollBack(ChLabelRunCtxT *runCtx, HashDirSegmentT *srcDirSeg, HashDirSegmentT *dstDirSeg,
    PageIdT dstTargetPageAddr, uint32_t pageSize);

#ifdef __cplusplus
}
#endif
#endif
