/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: se_cluster_hash_label_scan.h
 * Description: scan interfaces of clustered hash label
 * Author: chen<PERSON>wen
 * Create: 2022/8/19
 */

#ifndef SE_CLUSTERED_HASH_LABEL_SCAN_H
#define SE_CLUSTERED_HASH_LABEL_SCAN_H

#include "se_clustered_hash_label_base.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CLUSTERED_HASH_INVALID_CURSOR_NUM 0x7fffffff

typedef struct HcLabelScanPosition {
    ChLabelSegmentPos segmentPos;
    ChPhyRowId phyRowId;
    PageIdT pageAddr;
    uint32_t overFlowBlockId;  // 当前扫描到的溢出页编号
    uint16_t overFlowTupleId;  // 当前扫描到溢出页的第几个tuple
    bool isStartFromPageHead;  // 为true时，表示从blockId所指页第一行开始扫描
    bool isScanOverFlowPage;   // 为true时，表示当前正在扫描溢出页
    bool reserved[2];
} ChLabelScanPosT;

typedef struct TagChLabelPageFetchRowInfoT {
    uint8_t *firstPageHead;
    PageIdT firstPageAddr;
    uint8_t *segPageHead;  // 已经对页头做了偏移
    PageIdT pageAddr;
    uint32_t bufSize;
    void *buf;
    ChTupleT cacheBuf;
    HpTupleAddr addr;
    bool isOverFlowPage;
    bool isGetBuf;
    bool reserved[2];
} ChLabelPageFetchRowInfoT;

typedef struct TagChExpandedInfoList {
    DbSpinLockT lock;  // 保护list的读写并发
    TagLinkedListT list;  // 记录扫描期间Segment扩展的信息，用以过滤重复记录，节点为 ChScanExpandedInfoT
} ChExpandedInfoListT;

typedef struct ChScanExpandedInfo {
    TagLinkedListT expandNode;  // ChLabelScanCursorT::expandList中的节点
    uint32_t orgSegPattern;     // 全表扫描上次保存的seg pattern
    uint16_t orgSegDepth;       // 全表扫描上次保存的seg depth
    uint16_t expSegDepth;       // 当前seg发生扩展后的depth
    uint16_t tupleId;           // 当前保存的扫描到的tuple
    uint32_t matchCount;        // 已匹配次数
} ChScanExpandedInfoT;

typedef struct TagChLabelScanCursorT {
    ChLabelRunCtxT *ctx;
    ChLabelScanMode scanMode;
    uint32_t maxFetchNum;        // 上层设置的最大批扫描数目
    uint32_t actualScanRowsCnt;  // 本次Fetch实际扫描的行数目
    uint32_t requireScanPageCnt;
    uint32_t scannedPageCnt;  // 全表扫描的行数
    uint32_t scannedRowCnt;
    ChLabelPageFetchRowInfoT fetchRowInfo;
    ChLabelScanPosT scanPos;
    bool isFirstFetch;
    bool isFetched;
    bool isScanEnd;
    bool isDefragmentation;
    bool isRecovery;  // 用于warm reboot恢复流程判断
    bool reserved[2];
    ChExpandedInfoListT expandedList;
} ChLabelScanCursorT;

typedef struct ChLabelScanDeleteCtxT {
    ChLabelScanDeleteParaT *userData;
} ChLabelScanDeleteCtxT;

typedef enum TagChLabelScanOptTypeE {
    CHLABEL_SCAN_OPT_DELETE,
} ChLabelScanOptTypeE;

typedef struct ChLabelScanProcCtx {
    ChLabelScanOptTypeE opType;
    bool needGetBucket;
    bool isLogicalTuple;
    bool isRead;
    union {
        ChLabelScanDeleteCtxT deleteCtx;
    };
} ChLabelScanProcCtxT;

typedef struct ChLabelScanCtx {
    IndexKeyT idxKey;
    ChLogicRowId logicRowId;
    ChPhyRowId phyRowId;
    bool isFind;                  // 出参
    bool isTargetBucketOverFlow;  // 出参, target bucket是否发生过溢出
    bool isCheckOverFlowPages;    // 出参, 当前是否正在检查溢出页
    bool isStashBucket;           // 出参
    bool isPageEmpty;             // 出参
    uint8_t *bucket;              // 出参
    ChBucketMetaT *targetBucket;  // 出参
    uint16_t entrySlotInBucket;   // 出参
} ChLabelScanCtxT;

bool ChScanCursorRefTimedMutex(ChLabelRunCtxT *ctx, uint32_t timeoutUs);
void ChScanCursorRefDec(ChLabelRunCtxT *ctx, bool isScaleIn);
StatusInter ChLabelFetchNextValidTuple(ChLabelScanCursorT *cursor, ChLabelPageFetchRowInfoT *fetchRowInfo);
StatusInter ChLabelFetchNextHandleByUserProc(ChLabelScanCursorT *cursor, ChLabelScanSubsCondDataT *userScanCondData);
StatusInter ChLabelDoActionAfterProc(ChLabelScanSubsCondDataT *userScanCondData, ChLabelScanPosT lastScanPos,
    ChLabelScanCursorT *cursor, bool *isContinueScan);
StatusInter ChLabelFetchNextHandleByUserProc4ScanDel(ChLabelScanCursorT *cursor,
    ChLabelScanDeletePrepare scanDeletePrepare, bool *isContinueFetchNext, ChLabelScanProcCtxT *procCtx);
StatusInter ChLabelExtractHashCodeFromCursor(ChLabelScanCursorT *cursor);

#ifdef __cplusplus
}
#endif
#endif
