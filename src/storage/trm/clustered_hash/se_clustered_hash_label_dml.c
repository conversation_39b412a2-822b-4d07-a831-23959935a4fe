/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: se_clustered_hash_label_dml.c
 * Description: clustered hash label dml implementation
 * Author: zhangyoujian
 * Create: 2022/8/15
 */
#include "se_clustered_hash_label_dml.h"
#include "se_clustered_hash_access_dm.h"
#include "se_clustered_hash_label_base.h"
#include "se_clustered_hash_label_upgrade.h"
#include "se_clustered_hash_label_overflow_page.h"
#include "db_memcpy.h"
#include "se_clustered_hash_hc_index.h"
#include "se_clustered_hash_label_scan.h"
#include "se_clustered_hash_label_stat.h"
#include "se_log.h"
#include "se_undo.h"
#include "db_crash_debug.h"
#include "dm_data_prop_seri.h"
#include "se_clustered_hash_rsm.h"
#include "se_clustered_hash_label_ddl.h"

#ifdef __cplusplus
extern "C" {
#endif

#define TARGET_NEIGHBOR_STASH_ENTRY_NUM 32

StatusInter ClusteredEntryUpdate(ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelLookupProcCtxT *procCtx);
StatusInter ClusteredBucketErase(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, const ChLabelLookupProcCtxT *procCtx);
StatusInter ClusteredBucketEraseById(ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx);
StatusInter ClusteredEntryInsertHcIndexAndWriteUndoLog(ChLabelRunCtxT *runCtx, ChLabelInsertCtxT *insertCtx);
void ClusteredHashFreeOneSegPageAfterDelete(ChLabelRunCtxT *runCtx, HashDirSegmentT dirSeg);
StatusInter ClusteredHashUndoLogForUpdate(
    ChLabelRunCtxT *runCtx, ChLogicRowId logicRowId, const uint8_t *tupleData, uint32_t tupleLen);
StatusInter ClusteredHashUndoLogForInsert(ChLabelRunCtxT *runCtx, ChLogicRowId logicRowId);
StatusInter ClusteredHashMoveData(ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, HashDirSegmentT *dirSeg);
StatusInter ClusteredHashMoveDataAfterExpand(ChLabelRunCtxT *runCtx, ChLabelExpandCtxT *expandCtx);

inline static StatusInter ClusteredEntryLockPageAndUpdate(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelLookupProcCtxT *procCtx)
{
    ChLabelPageCtxT pageCtxCache = runCtx->pageCtx;
    DbMemAddrT segAddr =
        lookUpCtx->isCheckOverFlowPages ? runCtx->pageCtx.overFlowPageCtx.pageAddr : lookUpCtx->firstSegAddr;
    ClusteredHashPageLock(runCtx, false, segAddr.virtAddr, &segAddr.pageAddr);
    StatusInter ret = ClusteredEntryUpdate(runCtx, lookUpCtx, procCtx);
    runCtx->pageCtx = pageCtxCache;
    ClusteredHashPageUnlock(runCtx, false, segAddr.virtAddr);
    return ret;
}

inline static StatusInter ClusteredEntryUpdateWithoutLock(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelLookupProcCtxT *procCtx)
{
    ChLabelPageCtxT pageCtxCache = runCtx->pageCtx;
    StatusInter ret = ClusteredEntryUpdate(runCtx, lookUpCtx, procCtx);
    runCtx->pageCtx = pageCtxCache;
    return ret;
}

ALWAYS_INLINE_C static void ClusteredHashFreeOneSegPageAfterDeleteProc(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx)
{
    if (lookUpCtx->isPageEmpty) {
        ClusteredHashFreeOneSegPageAfterDelete(runCtx, *runCtx->pageCtx.dirSeg);
        (void)ClusteredHashInitPageCtxAndOffset(runCtx, &runCtx->pageCtx,
            lookUpCtx->idxKey.keyLen == 0 || lookUpCtx->idxKey.keyData == NULL ? lookUpCtx->phyRowId.hashCode :
                                                                                 HtGetHashCode(lookUpCtx->idxKey));
    }
}

inline static StatusInter ClusteredEntryLockPageAndErase(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelLookupProcCtxT *procCtx)
{
    DB_UNUSED(procCtx);
    DbMemAddrT segAddr =
        lookUpCtx->isCheckOverFlowPages ? runCtx->pageCtx.overFlowPageCtx.pageAddr : lookUpCtx->firstSegAddr;
    ClusteredHashPageLock(runCtx, false, segAddr.virtAddr, &segAddr.pageAddr);
    StatusInter ret = ClusteredHashDeleteUndoLog(
        runCtx, lookUpCtx->logicRowId, lookUpCtx->phyRowId.tupleId, lookUpCtx->targetBucket, lookUpCtx->bucket);
    if (ret != STATUS_OK_INTER) {
        ClusteredHashPageUnlock(runCtx, false, segAddr.virtAddr);
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_DELETE_BEFORE);
    ret = ClusteredBucketEraseById(runCtx, lookUpCtx);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_DELETE_FINISHED);
    ClusteredHashRsmUndoLogMovePrevRec(runCtx);
    ClusteredHashPageUnlock(runCtx, false, segAddr.virtAddr);
    ClusteredHashFreeOneSegPageAfterDeleteProc(runCtx, lookUpCtx);
    return ret;
}

StatusInter ClusteredEntryEraseWithoutLock(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelLookupProcCtxT *procCtx)
{
    DB_POINTER2(runCtx, lookUpCtx);
    DB_UNUSED(procCtx);
    StatusInter ret = ClusteredHashDeleteUndoLog(
        runCtx, lookUpCtx->logicRowId, lookUpCtx->phyRowId.tupleId, lookUpCtx->targetBucket, lookUpCtx->bucket);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_DELETE_BEFORE);
    ret = ClusteredBucketEraseById(runCtx, lookUpCtx);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_DELETE_FINISHED);
    ClusteredHashRsmUndoLogMovePrevRec(runCtx);
    return ret;
}

// note: 该函数为性能关键路径，使用强制内联有益于性能
ALWAYS_INLINE_C StatusInter ClusteredHashLookupByKey(
    ChLabelRunCtxT *runCtx, IndexKeyT idxKey, ChLabelLookupProcCtxT *procCtx, ClusteredEntryProc proc)
{
    HtHashCodeT hashCode = ClusteredHashGet26BitHashCode(idxKey);
    StatusInter ret = ClusteredHashInitPageCtx(runCtx, &runCtx->pageCtx, hashCode);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
    ChLabelLookupCtxT lookUpCtx = ClusteredHashInitLookUpCtxByKey(idxKey, false, false);
    lookUpCtx.firstSegAddr = pageCtx->segAddr;
    // 注意: 对于读操作如fetch或者lookup, 需要立即加锁, 对于写操作, 在各自的回调函数中加锁
    if (procCtx->opType == CHLABEL_OPT_FETCH) {
        ClusteredHashPageLock(
            runCtx, procCtx->isRead, lookUpCtx.firstSegAddr.virtAddr, &lookUpCtx.firstSegAddr.pageAddr);
    }

    ret = ClusteredHashFindEntryInPageChain(runCtx, &lookUpCtx);
#ifndef NDEBUG
    DB_ASSERT(!lookUpCtx.notExistsInBloomFilter || !lookUpCtx.isFind);
#endif
    if (SECUREC_LIKELY(ret == STATUS_OK_INTER)) {
        ret = proc(runCtx, &lookUpCtx, procCtx);
    }

    if (procCtx->opType == CHLABEL_OPT_FETCH) {
        ClusteredHashPageUnlock(runCtx, procCtx->isRead, lookUpCtx.firstSegAddr.virtAddr);
    }
    // 在正常segment页中没有找到, 并且散列的bucket发生过溢出, 则需要将所有溢出页探测一遍
    if (SECUREC_UNLIKELY(!lookUpCtx.isFind && (runCtx->chLabel->dashMeta.overflowPageTupleUsed > 0) &&
                         lookUpCtx.isTargetBucketOverFlow)) {
        if ((ret == STATUS_OK_INTER) || (ret == NO_DATA_INTER && procCtx->opType == CHLABEL_OPT_UPDATE)) {
            ret = ClusteredHashLookUpOverFlowPages(runCtx, idxKey, hashCode, procCtx, proc);
        }
    }

    return ret;
}

static StatusInter ClusteredHashCheckEntryProc(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChBucketMetaT *bucket, bool isProbBucket)
{
    DB_POINTER3(runCtx, lookUpCtx, bucket);
    StatusInter ret = STATUS_OK_INTER;
    uint32_t hashEntrySize = runCtx->chLabel->memMgr.dashMemMeta.hashEntrySize;
    HtHashCodeT hashCode = runCtx->pageCtx.hashCode;

    uint8_t bitSetMask = (uint8_t)(isProbBucket ? bucket->prevBitMap : ~bucket->prevBitMap);
    bitSetMask &= bucket->allocBitMap;
    uint32_t entryNum = runCtx->chLabel->dashMeta.hashEntryPerNormalBucket;
    uint32_t cnt = 0;
    uint32_t bucketSize = bucket->cnt;
    uint32_t probeLen = 0;
    for (; cnt < bucketSize && probeLen < entryNum; ++probeLen) {
        if (!BitMapIsSet((uint32_t)bitSetMask, probeLen)) {
            continue;
        }
        cnt++;
        ClusteredHashEntryT *curHashEntry = ClusteredGetEntryByBucket(bucket, probeLen, hashEntrySize);
        if (curHashEntry->hashCode == hashCode) {
            lookUpCtx->repeatHashCodeNum++;
            if (SECUREC_LIKELY(lookUpCtx->isLookUpByKey)) {
                ret = ClusteredHashCheckEntryKey(runCtx, lookUpCtx, curHashEntry);
            } else {
                ret = ClusteredHashCheckEntrySlot(runCtx, lookUpCtx, curHashEntry);
            }
            if (lookUpCtx->isFind) {
                lookUpCtx->isStashBucket = false;
                lookUpCtx->entrySlotInBucket = (uint8_t)probeLen;
                lookUpCtx->bucket = (uint8_t *)bucket;
                break;
            } else if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                SE_LAST_ERROR(ret,
                    "hashEntry check unsucc. normal bucket hashEntry slot: %" PRIu32 ", labelId: %" PRIu32
                    ", indexId: %" PRIu32,
                    probeLen, runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
                return ret;
            }
        }
    }
    lookUpCtx->probeLen = probeLen;
    return STATUS_OK_INTER;
}

static StatusInter ClusteredHashCheckStashBucketEntryProc(ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx)
{
    DB_POINTER3(runCtx, lookUpCtx, lookUpCtx->targetBucket);
    StatusInter ret = STATUS_OK_INTER;
    ChBucketMetaT *target = lookUpCtx->targetBucket;
    ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
    HtHashCodeT hashCode = pageCtx->hashCode;
    ChStashBucketMetaT *stashBucket = (ChStashBucketMetaT *)(void *)pageCtx->stashBucketHdr;
    DB_ASSERT((target->stashedBitMap & stashBucket->allocBitMap) == target->stashedBitMap);

    uint32_t stashedCnt = target->stashedCnt;
    uint32_t stashedBitMap = target->stashedBitMap;
#ifndef NDEBUG
    DB_ASSERT(target->stashedCnt <= HASH_ENTRY_PER_STASH_BUCKET);
    BitMapCheck(stashedBitMap, stashedCnt);
#endif
    int32_t step = (int32_t)runCtx->chLabel->memMgr.dashMemMeta.hashEntrySize;
    int32_t curOffset = -step;
    for (uint32_t i = 0; stashedCnt != 0 && i < HASH_ENTRY_PER_STASH_BUCKET; i++) {
        curOffset += step;
        if (!BitMapIsSet(stashedBitMap, i)) {
            continue;
        }
        stashedCnt--;
        ClusteredHashEntryT *curHashEntry = ClusteredGetStashBucketEntryByOffset(stashBucket, (uint32_t)curOffset);
        if (curHashEntry->hashCode == hashCode) {
            lookUpCtx->repeatHashCodeNum++;
            // 检测是否有hash 冲突
            ret = lookUpCtx->isLookUpByKey ? ClusteredHashCheckEntryKey(runCtx, lookUpCtx, curHashEntry) :
                                             ClusteredHashCheckEntrySlot(runCtx, lookUpCtx, curHashEntry);
            if (lookUpCtx->isFind) {
                lookUpCtx->isStashBucket = true;
                lookUpCtx->entrySlotInBucket = (uint8_t)i;
                lookUpCtx->bucket = pageCtx->stashBucketHdr;
                return STATUS_OK_INTER;
            } else if (ret != STATUS_OK_INTER) {
                SE_LAST_ERROR(ret,
                    "hashEntry check unsucc. stash bucket hashEntry slot: %" PRIu32 ", labelId: %" PRIu32
                    ", indexId: %" PRIu32,
                    i, runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
                return ret;
            }
        }
    }
    return STATUS_OK_INTER;
}

ALWAYS_INLINE_C static void ClusteredHashStatCollision(ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx)
{
    if (lookUpCtx->isFirstCollision && lookUpCtx->probeLen != 0) {
        lookUpCtx->isFirstCollision = false;
        runCtx->chLabel->perfStat.chCollisionCnt++;
    }
}

#ifndef NDEBUG
void ClusteredHashBucketCheck(ChLabelRunCtxT *runCtx, ChLogicRowId logicRowId, ChBucketMetaT *target,
    ChBucketMetaT *neighbor, ChStashBucketMetaT *stashBucket)
{
    if (TransformChLogicRowId(logicRowId) == HEAP_INVALID_ADDR || runCtx->labelVarInfo->upgradeVersion != 0) {
        return;
    }
    uint32_t hashEntrySize = runCtx->chLabel->memMgr.dashMemMeta.hashEntrySize;
    uint32_t entryNum = runCtx->chLabel->dashMeta.hashEntryPerNormalBucket;
    uint32_t probeLen = 0;
    uint32_t sameCnt = 0;
    for (; probeLen < entryNum; ++probeLen) {
        ClusteredHashEntryT *curHashEntry = ClusteredGetEntryByBucket(target, probeLen, hashEntrySize);
        if (curHashEntry->hashCode == logicRowId.hashCode && curHashEntry->logicSlot == logicRowId.tupleId &&
            curHashEntry->version == logicRowId.version) {
            sameCnt++;
        }
    }
    probeLen = 0;
    if (target != neighbor) {
        for (; probeLen < entryNum; ++probeLen) {
            ClusteredHashEntryT *curHashEntry = ClusteredGetEntryByBucket(neighbor, probeLen, hashEntrySize);
            if (curHashEntry->hashCode == logicRowId.hashCode && curHashEntry->logicSlot == logicRowId.tupleId &&
                curHashEntry->version == logicRowId.version) {
                sameCnt++;
            }
        }
    }

    if (target->stashedCnt != 0) {
        DB_ASSERT((target->stashedBitMap & stashBucket->allocBitMap) == target->stashedBitMap);
        uint32_t stashedCnt = target->stashedCnt;
        uint32_t stashedBitMap = target->stashedBitMap;
        for (uint32_t i = 0; i < stashedCnt; i++) {
            uint32_t entrySlot = (uint32_t)ffs((int32_t)stashedBitMap);
            DB_ASSERT(entrySlot > 0 && entrySlot <= HASH_ENTRY_PER_STASH_BUCKET);
            entrySlot--;
            stashedBitMap = BitMapUnSet(stashedBitMap, entrySlot);
            ClusteredHashEntryT *curHashEntry = ClusteredGetEntryByStashBucket(stashBucket, entrySlot, hashEntrySize);
            if (curHashEntry->hashCode == logicRowId.hashCode && curHashEntry->logicSlot == logicRowId.tupleId &&
                curHashEntry->version == logicRowId.version) {
                sameCnt++;
            }
        }
    }
    DB_ASSERT(sameCnt <= 1);
}
#endif

// note: 性能关键路径，使用强制内联有益于性能
ALWAYS_INLINE_C StatusInter ClusteredHashFindHashEntry(ChLabelRunCtxT *chLabelRunCtx, ChLabelLookupCtxT *lookUpCtx)
{
    DB_POINTER2(chLabelRunCtx, lookUpCtx);
    ChLabelPageCtxT *pageCtx = &chLabelRunCtx->pageCtx;
#ifndef NDEBUG
    lookUpCtx->notExistsInBloomFilter = false;
    if (chLabelRunCtx->lockPage != NULL && chLabelRunCtx->labelVarInfo->upgradeVersion == 0) {
        DB_ASSERT(chLabelRunCtx->lockPage == chLabelRunCtx->pageCtx.segAddr.virtAddr);
    }
#endif
    if (lookUpCtx->isLookUpByKey &&
        !ClusteredHashIsBloomFilterSet(pageCtx->hashCode, pageCtx->bloomFilterHdr,
            chLabelRunCtx->chLabel->dashMeta.bloomFilterSize, &chLabelRunCtx->chLabel->memMgr.dashMemMeta)) {
#ifndef NDEBUG
        // debug模式下即使bloomfilter判断数据不存在，也会执行搜索流程以防止bloomfilter出现错误判断
        lookUpCtx->notExistsInBloomFilter = true;
#else
        lookUpCtx->isTargetBucketOverFlow = true;
        return STATUS_OK_INTER;
#endif
    }

    ClusteredHashInitPageOffset(chLabelRunCtx, pageCtx);
    lookUpCtx->targetBucket = ClusteredHashGetBucketHdr(pageCtx->normalBucketHdr, pageCtx->targetSlot);
#ifndef NDEBUG
    ChBucketMetaT *neighborBucketDebug = ClusteredHashGetBucketHdr(pageCtx->normalBucketHdr, pageCtx->neighborSlot);
    if (!lookUpCtx->isLookUpByKey) {
        ChStashBucketMetaT *stashBucket = (ChStashBucketMetaT *)(void *)pageCtx->stashBucketHdr;
        ClusteredHashBucketCheck(
            chLabelRunCtx, lookUpCtx->logicRowId, lookUpCtx->targetBucket, neighborBucketDebug, stashBucket);
    }
#endif
    lookUpCtx->isTargetBucketOverFlow = ChLabelIsTargetBucketOverFlow(lookUpCtx->targetBucket);
    StatusInter ret = ClusteredHashCheckEntryProc(chLabelRunCtx, lookUpCtx, lookUpCtx->targetBucket, false);
    if (lookUpCtx->isWrite) {
        ClusteredHashStatCollision(chLabelRunCtx, lookUpCtx);
    }
    if (lookUpCtx->isFind || ret != STATUS_OK_INTER) {
        return ret;
    }
    ChBucketMetaT *neighborBucket = ClusteredHashGetBucketHdr(pageCtx->normalBucketHdr, pageCtx->neighborSlot);
    if (ClusteredHashCheckPrevBitMap(neighborBucket)) {
        ret = ClusteredHashCheckEntryProc(chLabelRunCtx, lookUpCtx, neighborBucket, true);
        if (lookUpCtx->isWrite) {
            ClusteredHashStatCollision(chLabelRunCtx, lookUpCtx);
        }
        if (lookUpCtx->isFind || ret != STATUS_OK_INTER) {
            return ret;
        }
    }

    if (lookUpCtx->targetBucket->stashedCnt != 0) {
        return ClusteredHashCheckStashBucketEntryProc(chLabelRunCtx, lookUpCtx);
    }
    return ret;
}

SEC_IDX_READ_FUNC StatusInter ClusteredHashLookupByLogicRowId(
    ChLabelRunCtxT *runCtx, ChLogicRowId logicRowId, ChLabelLookupProcCtxT *procCtx, ClusteredEntryProc proc)
{
    ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
    StatusInter ret = ClusteredHashInitPageCtx(runCtx, pageCtx, logicRowId.hashCode);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    if (((TrxT *)(runCtx->openCfg.seRunCtx->trx))->base.isRecovery) {
        PageHeadT *baseHead = (PageHeadT *)(void *)ClusteredHashGetPageHead(pageCtx->segAddr.virtAddr);
        DbRWLatchInit(&baseHead->lock);
    }
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataByPageCtx(pageCtx);
    // tupleId超过正常segment页的tupleId，说明要访问的记录在溢出页中
    if (SECUREC_UNLIKELY(logicRowId.tupleId >= pageData->tupleCntPerPage &&
                         logicRowId.version == runCtx->labelVarInfo->upgradeVersion)) {
        return ClusteredHashLookupByLogicRowIdForOverFlowPages(runCtx, logicRowId, procCtx, proc);
    }
    ChLabelLookupCtxT lookUpCtx = ClusteredHashInitLookUpCtxByRowId(
        logicRowId.hashCode, logicRowId.version, logicRowId.tupleId, procCtx->isLogicalTuple, false);
    // 加segment 页读锁
    lookUpCtx.firstSegAddr = pageCtx->segAddr;
    if (procCtx->opType == CHLABEL_OPT_FETCH || procCtx->opType == CHLABEL_OPT_UPDATE) {
        ClusteredHashPageLock(
            runCtx, procCtx->isRead, lookUpCtx.firstSegAddr.virtAddr, &lookUpCtx.firstSegAddr.pageAddr);
    }
    uint32_t phyTupleId;
    // 如果通过逻辑RowId查找且缩容过，需要走到ClusteredHashFindHashEntry，逐一查找。
    if ((procCtx->opType != CHLABEL_OPT_FETCH && procCtx->opType != CHLABEL_OPT_UPDATE) ||
        (procCtx->isLogicalTuple && runCtx->labelVarInfo->hasScaledIn) || runCtx->labelVarInfo->upgradeVersion != 0) {
        ret = ClusteredHashFindEntryInPageChain(runCtx, &lookUpCtx);
#ifndef NDEBUG
        DB_ASSERT(!lookUpCtx.notExistsInBloomFilter || !lookUpCtx.isFind);
#endif
        phyTupleId = lookUpCtx.phyRowId.tupleId;
    } else {
        ClusteredHashInitPageOffset(runCtx, pageCtx);
        lookUpCtx.isFind = true;
        phyTupleId = logicRowId.tupleId;
    }
    if (ret != STATUS_OK_INTER || !lookUpCtx.isFind || !TupleBitMapIsSet(pageCtx->bitmapHdr, phyTupleId)) {
        if (procCtx->opType == CHLABEL_OPT_FETCH || procCtx->opType == CHLABEL_OPT_UPDATE) {
            ClusteredHashPageUnlock(runCtx, procCtx->isRead, lookUpCtx.firstSegAddr.virtAddr);
        }
        return NO_DATA_INTER;
    }
    ret = proc(runCtx, &lookUpCtx, procCtx);
    if (procCtx->opType == CHLABEL_OPT_FETCH || procCtx->opType == CHLABEL_OPT_UPDATE) {
        ClusteredHashPageUnlock(runCtx, procCtx->isRead, lookUpCtx.firstSegAddr.virtAddr);
    }
    return ret;
}

ALWAYS_INLINE_C void ClusteredHashLookupByPhyRowIdGetScanCtx(
    ChLabelRunCtxT *runCtx, ChPhyRowId phyRowId, ChLabelLookupCtxT lookUpCtx, ChLabelScanCtxT *scanCtx)
{
    // 给scanCtx赋值
    scanCtx->logicRowId = ChLabelGetLogicalAddrByPhysicalAddr(runCtx, phyRowId);
    scanCtx->phyRowId = phyRowId;
    scanCtx->isFind = lookUpCtx.isFind;
    scanCtx->isCheckOverFlowPages = lookUpCtx.isCheckOverFlowPages;
    scanCtx->isStashBucket = lookUpCtx.isStashBucket;
    scanCtx->isPageEmpty = lookUpCtx.isPageEmpty;
    scanCtx->bucket = lookUpCtx.bucket;
    scanCtx->targetBucket = lookUpCtx.targetBucket;
    scanCtx->isTargetBucketOverFlow = lookUpCtx.isTargetBucketOverFlow;
    scanCtx->entrySlotInBucket = lookUpCtx.entrySlotInBucket;
}

static StatusInter ClusteredEntryFetch(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelLookupProcCtxT *procCtx)
{
    if (!lookUpCtx->isFind) {
        return NO_DATA_INTER;
    }
    ChLabelFetchCtxT *fetchCtx = &procCtx->fetchCtx;
    if (fetchCtx->readRowCtx.func == NULL) {
        return STATUS_OK_INTER;
    }
    uint8_t *tupleHdr = ClusteredHashTupleHdr(&runCtx->pageCtx, lookUpCtx->isCheckOverFlowPages);
    uint32_t tupleId = ClusteredHashGetInnerTupleId(runCtx, lookUpCtx->phyRowId.tupleId);
    ChRowHeadT *rowHead = ClusteredHashGetRowHeadByTupleId(
        tupleHdr, runCtx->pageCtx.pageMetaData.tupleSize, runCtx->chLabel->labelCfg.slotExtendSize, (uint16_t)tupleId);
    ChTupleT tuple = {0};
    ClusteredHashPrepareTuple(runCtx, rowHead, &tuple, false);
    StatusInter ret = fetchCtx->readRowCtx.func(tuple.buf, tuple.pos, fetchCtx->readRowCtx.userData);
    ClusteredHashReleaseTuple(runCtx, &tuple);
    return ret;
}

void ClusteredHashTupleInsert(const ChLabelRunCtxT *runCtx, ChRowHeadT *rowHead, ChLabelInsertCtxT *insertCtx)
{
#ifndef NDEBUG
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataWithAddr(runCtx, runCtx->pageCtx.segAddr.virtAddr);
    DB_ASSERT(insertCtx->bufSize <= pageData->pageRowSize);
#endif
    rowHead->trxId = ((TrxT *)(runCtx->openCfg.seRunCtx->trx))->base.trxId;
    rowHead->totalBufSize = insertCtx->bufSize;
    DbFastMemcpy((uint8_t *)(rowHead + 1), insertCtx->bufSize, insertCtx->buf, insertCtx->bufSize);
}

ALWAYS_INLINE_C StatusInter ClusteredHashFetch(
    ChLabelRunCtxT *runCtx, ChLogicRowId logicRowId, const ChLabelReadRowProc func, void *userData)
{
    ChLabelLookupProcCtxT procCtx;
    procCtx.opType = CHLABEL_OPT_FETCH;
    procCtx.isLogicalTuple = true;
    procCtx.isRead = true;
    procCtx.fetchCtx.readRowCtx = (ReadRowCtxT){.func = func, .userData = userData};
    return ClusteredHashLookupByLogicRowId(runCtx, logicRowId, &procCtx, ClusteredEntryFetch);
}

ALWAYS_INLINE_C static StatusInter ClusteredEntryGetAllRowId(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelLookupProcCtxT *procCtx)
{
    DB_POINTER3(runCtx, lookUpCtx, procCtx);
    if (lookUpCtx->isFind) {
        *procCtx->fetchCtx.lookUpCtx.logicRowId = lookUpCtx->logicRowId;
        *procCtx->fetchCtx.lookUpCtx.phyRowId = lookUpCtx->phyRowId;
        *procCtx->fetchCtx.lookUpCtx.isExist = true;
    } else {
        *procCtx->fetchCtx.lookUpCtx.logicRowId =
            (ChLogicRowId){0, runCtx->labelVarInfo->upgradeVersion, DB_INVALID_ID16};
        *procCtx->fetchCtx.lookUpCtx.phyRowId = (ChPhyRowId){0, runCtx->labelVarInfo->upgradeVersion, DB_INVALID_ID16};
        *procCtx->fetchCtx.lookUpCtx.isExist = false;
    }
    return STATUS_OK_INTER;
}

ALWAYS_INLINE_C static StatusInter ClusteredHashUpdateWhenFound(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelLookupProcCtxT *procCtx)
{
    if (SECUREC_UNLIKELY(!lookUpCtx->isFind)) {
        *procCtx->updateCtx.isFind = lookUpCtx->isFind;
        *procCtx->updateCtx.newLogicRowId = (ChLogicRowId){0, runCtx->labelVarInfo->upgradeVersion, DB_INVALID_ID16};
        return STATUS_OK_INTER;
    }
    *procCtx->updateCtx.isFind = lookUpCtx->isFind;
    *procCtx->updateCtx.newLogicRowId = lookUpCtx->logicRowId;
    ChLabelUpdateParamsT *params = procCtx->updateCtx.params;
    params->isFind = lookUpCtx->isFind;
    params->newVertexBuf = ChLabelGetCachedTupleBuf(runCtx, runCtx->heapTuple.bufSize);
    if (SECUREC_UNLIKELY(params->newVertexBuf == NULL)) {
        return OUT_OF_MEMORY_INTER;
    }
    params->tupleAddr = *(HpTupleAddr *)&lookUpCtx->logicRowId;
    Status ret = procCtx->updateCtx.updatePrepare(procCtx->updateCtx.params, &runCtx->heapTuple, params->newVertexBuf);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Unable to prepare clustered hash data before update");
        return DbGetStatusInterErrno(ret);
    }

    if (SECUREC_UNLIKELY(params->isAged)) {
        *procCtx->updateCtx.isFind = false;
        return STATUS_OK_INTER;
    }

    procCtx->opType = CHLABEL_OPT_UPDATE;
    procCtx->isLogicalTuple = false;
    procCtx->isRead = false;
    procCtx->updateCtx = (ChLabelUpdateCtxT){
        .buf = params->newVertexBuf->buf, .bufSize = params->newVertexBuf->bufSize, .isRollBack = false};
    return ClusteredEntryLockPageAndUpdate(runCtx, lookUpCtx, procCtx);
}

ALWAYS_INLINE_C static StatusInter ClusteredHashLookUpAllRowId(
    ChLabelRunCtxT *runCtx, IndexKeyT idxKey, ChLogicRowId *logicRowId, ChPhyRowId *phyRowId, bool *isFind)
{
    DB_POINTER5(runCtx, runCtx->chLabel, logicRowId, phyRowId, isFind);
    if (SECUREC_UNLIKELY(runCtx->chLabel->perfStat.phyItemNum == 0)) {
        *isFind = false;
        return STATUS_OK_INTER;
    }

    ChLabelLookupProcCtxT procCtx;
    procCtx.opType = CHLABEL_OPT_FETCH;
    procCtx.isLogicalTuple = true;
    procCtx.isRead = true;
    procCtx.fetchCtx.lookUpCtx = (LookUpCtxT){logicRowId, isFind, phyRowId};
    return ClusteredHashLookupByKey(runCtx, idxKey, &procCtx, ClusteredEntryGetAllRowId);
}

ALWAYS_INLINE_C static StatusInter ClusteredHashLookUp4Update(
    ChLabelRunCtxT *runCtx, IndexKeyT idxKey, ChLabelUpdateParamsT *params, ChLabelUpdatePrepare updatePrepare)
{
    ChLogicRowId *logicRowId = (ChLogicRowId *)(void *)&params->tupleAddr;
    bool *isFind = &params->isFind;
    DB_POINTER4(runCtx, runCtx->chLabel, logicRowId, isFind);
    if (SECUREC_UNLIKELY(runCtx->chLabel->perfStat.phyItemNum == 0)) {
        *isFind = false;
        return STATUS_OK_INTER;
    }

    ChLabelLookupProcCtxT procCtx;
    procCtx.opType = CHLABEL_OPT_UPDATE;
    procCtx.isLogicalTuple = true;
    procCtx.isRead = false;
    procCtx.updateCtx = (ChLabelUpdateCtxT){
        .isFind = isFind, .newLogicRowId = logicRowId, .params = params, .updatePrepare = updatePrepare};
    return ClusteredHashLookupByKey(runCtx, idxKey, &procCtx, ClusteredHashUpdateWhenFound);
}

ALWAYS_INLINE_C StatusInter ClusteredHashLookUp(
    ChLabelRunCtxT *runCtx, IndexKeyT idxKey, ChLogicRowId *logicRowId, bool *isFind)
{
    DB_POINTER4(runCtx, runCtx->chLabel, logicRowId, isFind);
    ChPhyRowId phyRowId = {0, 0, DB_INVALID_ID16};
    return ClusteredHashLookUpAllRowId(runCtx, idxKey, logicRowId, &phyRowId, isFind);
}

// 检查当前hashENtry在当前segment页进行rehash后是保留还是需要删除，返回true则需要保留，false则需要删除
inline static bool ClusteredHashCheckHashEntry(const ClusteredHashEntryT *hashEntry, uint32_t pattern, bool isOldSeg)
{
    return isOldSeg ? ((hashEntry->hashCode & pattern) == 0u) : ((hashEntry->hashCode & pattern) != 0u);
}

void ClusteredHashProcStashBucket(
    const ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, ChBucketMetaT *bucket, uint32_t pattern, bool isOldSeg)
{
    if (bucket->stashedCnt == 0) {
        return;
    }
    const DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint8_t *bitmapHdr = pageCtx->bitmapHdr;
    uint8_t *bloomFilter = pageCtx->bloomFilterHdr;
    ChStashBucketMetaT *stashBucket = (ChStashBucketMetaT *)(void *)pageCtx->stashBucketHdr;
    uint32_t stashedCnt = bucket->stashedCnt;
    uint32_t curOverflowBitMap = bucket->stashedBitMap;
    uint32_t newOverflowBitMap = bucket->stashedBitMap;
    uint8_t count = 0;

    int32_t step = (int32_t)runCtx->chLabel->memMgr.dashMemMeta.hashEntrySize;
    int32_t curOffset = -step;
    for (uint32_t i = 0; stashedCnt != 0 && i < HASH_ENTRY_PER_STASH_BUCKET; i++) {
        curOffset += step;
        if (!BitMapIsSet(curOverflowBitMap, i)) {
            continue;
        }
        stashedCnt--;
        ClusteredHashEntryT *hashEntry = ClusteredGetStashBucketEntryByOffset(stashBucket, (uint32_t)curOffset);
        if (ClusteredHashCheckHashEntry(hashEntry, pattern, isOldSeg)) {
            ClusteredHashSetBloomFilter(
                hashEntry->hashCode, bloomFilter, dashMeta->bloomFilterSize, &runCtx->chLabel->memMgr.dashMemMeta);
            continue;
        }
        // 处理stash bucket中的allocBitMap
        stashBucket->allocBitMap = BitMapUnSet(stashBucket->allocBitMap, i);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_PROC_STASHED_UPDATE_ALLOC_BIMAP);
        DB_ASSERT(stashBucket->count > 0);
        stashBucket->count--;
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_PROC_STASHED_UPDATE_CNT);
        // 处理页头bitmap
        TupleBitMapUnSet(bitmapHdr, hashEntry->phySlot);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_PROC_STASHED_UPDATE_BITMAP);
        ClusteredHashEntryClear(hashEntry);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_PROC_STASHED_CLEAR_ENTRY);
        newOverflowBitMap = BitMapUnSet(newOverflowBitMap, i);
        count++;
    }

    bucket->stashedBitMap = newOverflowBitMap;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_PROC_STASHED_UPDATE_STASH_BITMAP);
    DB_ASSERT(bucket->stashedCnt >= count);
    bucket->stashedCnt = (uint8_t)((uint8_t)(bucket->stashedCnt - count) & 0x7F);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_PROC_STASHED_UPDATE_STASH_CNT);
    DB_ASSERT((bucket->stashedBitMap & stashBucket->allocBitMap) == bucket->stashedBitMap);
}

void ClusteredHashUpdateFreeSlotList(const ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx)
{
    ChLabelPageHeaderT *pageHead = ClusteredHashGetPageHead(pageCtx->segAddr.virtAddr);
    pageHead->freeSlotCount = 0;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_RESET_FREE_SLOT_CNT);
    pageHead->freeSlotId = CLUSTERED_HASH_INVALID_SLOTID;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_RESET_FREE_SLOT_ID);
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataByPageCtx(pageCtx);
    uint32_t tupleCountPerPage = pageData->tupleCntPerPage;
    uint32_t tupleSize = pageData->tupleSize;

    uint8_t *bitmap = pageCtx->bitmapHdr;
    uint8_t *tupleHdr = pageCtx->tupleHdr;
    int32_t curSlot = -(int32_t)tupleSize;
    for (uint32_t slotId = 0; slotId < tupleCountPerPage; ++slotId) {
        curSlot += (int32_t)tupleSize;
        if (TupleBitMapIsSet(bitmap, slotId)) {
            continue;
        }
        pageHead->freeSlotCount++;
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_UPDATE_FREE_SLOT_CNT);
        ChLabelSlotId *freeSlot = (ChLabelSlotId *)(void *)(tupleHdr + (DB_UINTPTR)(uint32_t)curSlot);
        freeSlot->nextSlotId = pageHead->freeSlotId;
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_UPDATE_NEXT_SLOT_ID);
        pageHead->freeSlotId = (uint16_t)slotId;
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_UPDATE_FREE_SLOT_ID);
    }
}

void ClusteredHashProcSegment(const ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, uint32_t pattern, bool isOldSeg)
{
    const DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint8_t *bitmapHdr = pageCtx->bitmapHdr;
    uint8_t *normalBucketHdr = pageCtx->normalBucketHdr;
    uint8_t *bloomFilter = pageCtx->bloomFilterHdr;
    uint32_t hashNormalBucketNumPerPage = pageCtx->pageMetaData.hashNormalBucketNumPerPage;
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    uint32_t hashEntrySize = dashMemMeta->hashEntrySize;
    uint16_t bloomFilterSize = dashMeta->bloomFilterSize;
    ClusteredHashClearBloomFilter(bloomFilter, bloomFilterSize);
    for (uint32_t i = 0; i < hashNormalBucketNumPerPage; ++i) {
        ChBucketMetaT *bucket = ClusteredHashGetBucketByIdx(normalBucketHdr, i, dashMeta->bucketSize);
#ifndef NDEBUG
        CheckNormalBucketBitMap(bucket);
#endif
        uint32_t maxCnt = bucket->cnt;
        uint32_t cnt = 0;
        for (uint32_t entrySlot = 0; entrySlot < HASH_ENTRY_PER_BUCKET && cnt < maxCnt; ++entrySlot) {
            if (!BitMapIsSet((uint32_t)bucket->allocBitMap, entrySlot)) {
                continue;
            }
            cnt++;
            ClusteredHashEntryT *hashEntry = ClusteredGetEntryByBucket(bucket, entrySlot, hashEntrySize);
            if (ClusteredHashCheckHashEntry(hashEntry, pattern, isOldSeg)) {
                ClusteredHashSetBloomFilter(hashEntry->hashCode, bloomFilter, bloomFilterSize, dashMemMeta);
                continue;
            }
            // 处理bucket中的allocBitMap 和 prevBitMap
            bucket->allocBitMap = (uint8_t)BitMapUnSet((uint32_t)bucket->allocBitMap, entrySlot);
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_PROC_BUCKET_UPDATE_ALLOC_BITMAP);
            if (!BitMapIsSet((uint32_t)bucket->prevBitMap, entrySlot)) {
                bucket->localEntryCnt--;
                SHM_CRASHPOINT_ONE(SHM_CRASH_CH_PROC_BUCKET_UPDATE_LOCAL_ENTRY_CNT);
            }
            bucket->prevBitMap = (uint8_t)BitMapUnSet((uint32_t)bucket->prevBitMap, entrySlot);
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_PROC_BUCKET_UPDATE_PREV_BITMAP);
            DB_ASSERT(bucket->cnt > 0);
            bucket->cnt--;
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_PROC_BUCKET_UPDATE_CNT);
            // 处理页头bitmap
            TupleBitMapUnSet(bitmapHdr, hashEntry->phySlot);
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_PROC_BUCKET_UPDATE_BITMAP);
            ClusteredHashEntryClear(hashEntry);
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_PROC_BUCKET_CLEAR_ENTRY);
        }
        // 处理stash bucket
        ClusteredHashProcStashBucket(runCtx, pageCtx, bucket, pattern, isOldSeg);
#ifndef NDEBUG
        CheckNormalBucketBitMap(bucket);
#endif
    }
    // 维护segment页上的空闲链表
    ClusteredHashUpdateFreeSlotList(runCtx, pageCtx);
}

void ClusteredHashBucketsSplit(ChLabelRunCtxT *runCtx, ChLabelExpandCtxT *expandCtx, uint32_t patternSpan)
{
    // 缓存pageCtx
    ChLabelPageCtxT pageCtx = runCtx->pageCtx;
    ChLabelPageCtxT newPageCtx = expandCtx->newPageCtx;
    StatusInter ret = ClusteredHashPageChainBucketsSplit(runCtx, expandCtx, patternSpan);
    // 处理bucket数据不会失败，如果失败则有问题
    DB_ASSERT(ret == STATUS_OK_INTER);
    runCtx->pageCtx = pageCtx;
    expandCtx->newPageCtx = newPageCtx;
    runCtx->chLabel->version++;
}

StatusInter ClusteredHashSegmentSplit(ChLabelRunCtxT *runCtx, ChLabelExpandCtxT *expandCtx)
{
    ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_BEFORE);
    StatusInter ret = ClusteredHashPageChainSplit(runCtx, expandCtx);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret, "unable to alloc segment page for rehash, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    if (runCtx->chLabel->labelCfg.isUseRsm) {
        expandCtx->rsmChExpandRec->newDeviceId = expandCtx->newPageCtx.segAddr.pageAddr.deviceId;
        expandCtx->rsmChExpandRec->newBlockId = expandCtx->newPageCtx.segAddr.pageAddr.blockId;
        expandCtx->rsmChExpandRec->oldDirSegDepth = pageCtx->dirSeg->segDepth;
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_AFTER);
        expandCtx->rsmChExpandRec->expandType = CLUSTERED_HASH_EXPAND_ALLOC_SEGPAGE;
    }
    uint32_t patternSpan = 1u << pageCtx->dirSeg->segDepth;
    uint32_t newPattern = pageCtx->hashCode % patternSpan;

    expandCtx->newDir.pageAddr.pageAddr = expandCtx->newPageCtx.segAddr.pageAddr;
    expandCtx->newDir.segDepth = pageCtx->dirSeg->segDepth + 1;
    expandCtx->newDir.pattern = newPattern + patternSpan;

    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_BEFORE);
    pageCtx->dirSeg->segDepth++;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_SEG_DEPTH);
    pageCtx->dirSeg->pattern = newPattern;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_AFTER);

    ClusteredHashInitHdr(runCtx, &expandCtx->newPageCtx);
    ClusteredHashInitPageOffset(runCtx, &expandCtx->newPageCtx);
    ClusteredHashBucketsSplit(runCtx, expandCtx, patternSpan);
    if (runCtx->chLabel->labelCfg.isUseRsm) {
        expandCtx->rsmChExpandRec->expandType = CLUSTERED_HASH_SPILIT_BUCKET;
    }
    return STATUS_OK_INTER;
}

void ClusteredHashRollBackSegmentSplit(ChLabelRunCtxT *runCtx, ChLabelExpandCtxT *expandCtx)
{
    ClusteredHashRollBackPageChainSplit(runCtx, expandCtx);
    ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
    DB_ASSERT(pageCtx->dirSeg->segDepth > 0);
    pageCtx->dirSeg->segDepth--;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_ROLLBACK_UPDATE_DIR_DEPTH);
    pageCtx->dirSeg->pattern = pageCtx->pattern;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_ROLLBACK_UPDATE_DIR_PATTERN);
}

void ClusteredHashUpdateDirSegMapRecursion(
    ChLabelRunCtxT *runCtx, ChLabelExpandCtxT *expandCtx, UpdateParaT updatePara, HashDirSegmentT oldSeg)
{
    uint32_t halfDirCap = (updatePara.dirCapacity >> 1);  // 右移1位，取半
    UpdateParaT updateParaAgain = {.segDepth = updatePara.segDepth + 1u,
        .dirDepth = updatePara.dirDepth,
        .dirCapacity = halfDirCap,
        .segId = updatePara.segId - halfDirCap,
        .segIdOld = updatePara.segIdOld};
    if ((updatePara.segId % updatePara.dirCapacity) >= halfDirCap) {
        ClusteredHashFreshDirectory(runCtx, expandCtx, updateParaAgain, oldSeg);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_HALF);
        updateParaAgain.segId = updatePara.segId;
        ClusteredHashFreshDirectory(runCtx, expandCtx, updateParaAgain, oldSeg);
    } else {
        updateParaAgain.segId = updatePara.segId;
        ClusteredHashFreshDirectory(runCtx, expandCtx, updateParaAgain, oldSeg);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_HALF);
        updateParaAgain.segId = updatePara.segId + halfDirCap;
        ClusteredHashFreshDirectory(runCtx, expandCtx, updateParaAgain, oldSeg);
    }
}

void ClusteredHashFreshDirectory(
    ChLabelRunCtxT *runCtx, ChLabelExpandCtxT *expandCtx, UpdateParaT updatePara, HashDirSegmentT oldSeg)
{
    uint32_t depthDiff = updatePara.dirDepth - updatePara.segDepth;
    DB_ASSERT(updatePara.dirCapacity != 0u);
    if (depthDiff == 0u) {
        uint32_t smallSegId;
        uint32_t largeSegId;
        uint32_t halfDirCap = (updatePara.dirCapacity >> 1);
        if ((updatePara.segId % updatePara.dirCapacity) >= halfDirCap) {
            smallSegId = updatePara.segId - halfDirCap;
            largeSegId = updatePara.segId;
        } else {
            smallSegId = updatePara.segId;
            largeSegId = updatePara.segId + halfDirCap;
        }
        HashDirSegmentT *smallSeg = ClusteredHashGetDirSegmentBySegId(&runCtx->memRunCtx, smallSegId);
        if (smallSeg == NULL) {
            return;
        }
        HashDirSegmentT *largeSeg = ClusteredHashGetDirSegmentBySegId(&runCtx->memRunCtx, largeSegId);
        if (largeSeg == NULL) {
            return;
        }
        *smallSeg = oldSeg;
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_FRESH_DIR_HALF);
        *largeSeg = expandCtx->newDir;
    } else {
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_BEFORE);
        ClusteredHashUpdateDirSegMapRecursion(runCtx, expandCtx, updatePara, oldSeg);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_FRESH_DIR_MAP_RECURSION_AFTER);
    }
}

StatusInter ClusteredHashDoubleDirectoryAllocPages(
    ChLabelRunCtxT *runCtx, DashEhMemMetaT *dashMemMeta, uint32_t extendDirPageCnt)
{
    uint8_t *dirPageAddr = NULL;
    StatusInter ret;
    for (uint32_t i = 0; i < extendDirPageCnt; ++i) {
        ret = ClusteredHashAllocDirPage(&runCtx->memRunCtx, ((TrxT *)(runCtx->seRunCtx->trx))->liteTrx.rsmUndoRec,
            runCtx->labelVarInfo->dirPageCount, &dirPageAddr);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_DOUBLE_DIR_ALLOC_PAGE);
        runCtx->labelVarInfo->dirPageCount++;
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_DOUBLE_DIR_RECORD_PAGE_COUNT);
    }
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashDoubleDirectory(ChLabelRunCtxT *runCtx, ChLabelExpandCtxT *expandCtx, uint32_t oldSegId)
{
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    uint32_t hashSegNumPerPage = dashMemMeta->hashSegNumPerPage;
    uint32_t oldDirCap = runCtx->labelVarInfo->dirCap;
    uint32_t newDirCap = runCtx->labelVarInfo->dirCap << 1;
    uint32_t newTotalDirPageCnt = (newDirCap + hashSegNumPerPage - 1) / hashSegNumPerPage;
    uint32_t extendDirPageCnt = newTotalDirPageCnt - runCtx->labelVarInfo->dirPageCount;
    DB_ASSERT(newTotalDirPageCnt >= runCtx->labelVarInfo->dirPageCount);
    if (newTotalDirPageCnt > runCtx->chLabel->pageAddrArrCapacity) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER,
            "directory page has exceed limits. %" PRIu32 ", labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->pageAddrArrCapacity, runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return OUT_OF_MEMORY_INTER;
    }
    StatusInter ret = ClusteredHashDoubleDirectoryAllocPages(runCtx, dashMemMeta, extendDirPageCnt);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    uint8_t *dirPageAddr = NULL;
    uint32_t newSegId = oldSegId + oldDirCap;
    for (uint32_t i = oldDirCap; i < newDirCap; i++) {
        uint32_t dirPageId = i / hashSegNumPerPage;
        uint32_t dirSlotId = i % hashSegNumPerPage;
        dirPageAddr = NULL;
        ret = ClusteredHashGetDirPage(&runCtx->memRunCtx, dirPageId, &dirPageAddr);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        HashDirSegmentT *newSeg = (HashDirSegmentT *)(void *)(dirPageAddr + dirSlotId * sizeof(HashDirSegmentT));
        // 不是扩展的dir，获取对应的oldDir
        if (i != newSegId) {
            dirPageId = (i - oldDirCap) / hashSegNumPerPage;
            dirSlotId = (i - oldDirCap) % hashSegNumPerPage;
            ret = ClusteredHashGetDirPage(&runCtx->memRunCtx, dirPageId, &dirPageAddr);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_OLD_DIR);
            *newSeg = *(HashDirSegmentT *)(void *)(dirPageAddr + dirSlotId * sizeof(HashDirSegmentT));
        } else {
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_NEW_DIR);
            *newSeg = expandCtx->newDir;
        }
    }

    runCtx->labelVarInfo->dirDepth++;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_DIR_DEPTH);
    runCtx->labelVarInfo->dirCap = newDirCap;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_DIR_CAP);
    runCtx->chLabel->version++;
    return STATUS_OK_INTER;
}

static void ClusteredHashErasePage(ChLabelRunCtxT *runCtx, ClusteredHashEntryT *hashEntry, ChLabelLookupCtxT *lookUpCtx)
{
    DB_POINTER(hashEntry);
#ifndef NDEBUG
    if (runCtx->lockPage != NULL && runCtx->labelVarInfo->upgradeVersion == 0) {
        DB_ASSERT(runCtx->lockPage == lookUpCtx->firstSegAddr.virtAddr);
        DB_ASSERT(runCtx->pageCtx.segAddr.virtAddr == lookUpCtx->firstSegAddr.virtAddr);
    }
#endif
    ClusteredHashEntryClear(hashEntry);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_DELETE_CLEAR_ENTRY);
    lookUpCtx->isPageEmpty =
        TupleSlotFree(&runCtx->pageCtx, lookUpCtx->phyRowId.tupleId, runCtx->labelVarInfo, &runCtx->chLabel->dashMeta);
}

StatusInter ClusteredBucketErase(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, const ChLabelLookupProcCtxT *procCtx)
{
    DB_UNUSED(procCtx);
    if (SECUREC_UNLIKELY(!lookUpCtx->isFind)) {
        return NO_DATA_INTER;
    }
    const DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    ClusteredHashEntryT *hashEntry = NULL;
    if (SECUREC_LIKELY(!lookUpCtx->isCheckOverFlowPages)) {
        if (SECUREC_LIKELY(lookUpCtx->isStashBucket)) {
            ChStashBucketMetaT *stashBucket = (ChStashBucketMetaT *)(void *)lookUpCtx->bucket;
            hashEntry =
                ClusteredGetEntryByStashBucket(stashBucket, lookUpCtx->entrySlotInBucket, dashMemMeta->hashEntrySize);
            ClusteredHashClearStashBucket(stashBucket, lookUpCtx);
        } else {
            hashEntry = ClusteredGetEntryByBucket(
                (ChBucketMetaT *)(void *)lookUpCtx->bucket, lookUpCtx->entrySlotInBucket, dashMemMeta->hashEntrySize);
            ClusteredHashNormalBucketFreeSlot((ChBucketMetaT *)(void *)lookUpCtx->bucket, lookUpCtx->entrySlotInBucket);
        }
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_DELETE_CLEAR_BUCKET);
        ClusteredHashErasePage(runCtx, hashEntry, lookUpCtx);
    } else {  // 要删除的数据在溢出页
        ClusteredHashEraseOverflowPage(runCtx, lookUpCtx);
    }
    ClusteredHashStatDelete(runCtx);
    return STATUS_OK_INTER;
}

StatusInter ClusteredBucketEraseById(ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx)
{
    if (SECUREC_UNLIKELY(!lookUpCtx->isFind)) {
        return NO_DATA_INTER;
    }
    // 缓存当前pageCtx
    ChLabelPageCtxT pageCtxCache = runCtx->pageCtx;
    ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
    const DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    ClusteredHashEntryT *hashEntry = NULL;
    StatusInter ret = STATUS_OK_INTER;
    if (SECUREC_LIKELY(!lookUpCtx->isCheckOverFlowPages)) {
        ChRowHeadT *rowHead = ClusteredHashGetRowHeadByTupleId(pageCtx->tupleHdr, pageCtx->pageMetaData.tupleSize,
            runCtx->chLabel->labelCfg.slotExtendSize, (uint16_t)lookUpCtx->phyRowId.tupleId);
        ret = ClusteredHashBucketEraseHcIndex(runCtx, lookUpCtx->logicRowId, rowHead, &pageCtxCache);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
        if (SECUREC_UNLIKELY(lookUpCtx->isStashBucket)) {
            ChStashBucketMetaT *stashBucket = (ChStashBucketMetaT *)(void *)lookUpCtx->bucket;
            hashEntry =
                ClusteredGetEntryByStashBucket(stashBucket, lookUpCtx->entrySlotInBucket, dashMemMeta->hashEntrySize);
            ClusteredHashClearStashBucket(stashBucket, lookUpCtx);
        } else {
            hashEntry = ClusteredGetEntryByBucket(
                (ChBucketMetaT *)(void *)lookUpCtx->bucket, lookUpCtx->entrySlotInBucket, dashMemMeta->hashEntrySize);
            ClusteredHashNormalBucketFreeSlot((ChBucketMetaT *)(void *)lookUpCtx->bucket, lookUpCtx->entrySlotInBucket);
        }
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_DELETE_CLEAR_BUCKET);
        ClusteredHashErasePage(runCtx, hashEntry, lookUpCtx);
    } else {  // 要删除的数据在溢出页
        ret = ClusteredHashEraseOverflowPageHc(runCtx, lookUpCtx);
        // 由于删除hashcluster索引会刷新pageCtx，因此需要重新初始化一遍
        runCtx->pageCtx = pageCtxCache;
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, merge delete HcIndex unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
        ClusteredHashEraseOverflowPage(runCtx, lookUpCtx);
    }
    ClusteredHashStatDelete(runCtx);
    return STATUS_OK_INTER;
}

bool ClusteredHashCheckIsDropByVersion(ChLabelRunCtxT *runCtx, ChLabelOptTypeE opType,
    const HeapTupleBufT *oldHeapTupleBuf, const HeapTupleBufT *newHeapTupleBuf, uint64_t lastVersion)
{
    DmVertexLabelT *vertexLabel = (DmVertexLabelT *)runCtx->openCfg.vertexLabel;
    if (SECUREC_LIKELY(!vertexLabel->commonInfo->dlrInfo.isDataSyncLabel)) {
        return false;
    }
    if (SECUREC_LIKELY(DmIsLabelSupportStatusMerge(vertexLabel))) {
        return false;
    }
    // 获取数据同步版本
    uint64_t oldVersion = DB_MAX_UINT64;
    uint64_t newVersion = DB_MAX_UINT64;
    if (oldHeapTupleBuf != NULL) {
        ClusteredHashGetSyncVersion(vertexLabel, oldHeapTupleBuf, &oldVersion);
    }
    if (newHeapTupleBuf != NULL) {
        ClusteredHashGetSyncVersion(vertexLabel, newHeapTupleBuf, &newVersion);
    }
    if (opType == CHLABEL_OPT_REPLACE) {
        if (oldVersion > newVersion) {
            DB_LOG_INFO("SyncData check replace drop now operation.");
            return true;
        }
        return false;
    } else if (opType == CHLABEL_OPT_DELETE) {
        if (oldVersion > lastVersion) {
            DB_LOG_INFO("SyncData check delete drop now operation.");
            return true;
        }
        return false;
    } else {
        return false;
    }
}

void ClusteredHashFreeOneSegPageAfterDelete(ChLabelRunCtxT *runCtx, HashDirSegmentT dirSeg)
{
    bool isWriteLock = DbRWLatchIsWriteLock(&runCtx->openCfg.labelRWLatch->rwlatch);
    bool isHcIndexLockByUpper = runCtx->isHoldHcIndexLockByUpper;
    if (!isWriteLock) {
        if (isHcIndexLockByUpper) {
            ClusteredHashWUnLockHcIndexByUpper(runCtx);
        }
        ClusteredHashTableUnLock(runCtx, true);
    }
    uint32_t retryCount = CH_CURSOR_LOCK_OVER_TIME_RETRY_COUNT;
    StatusInter ret = STATUS_OK_INTER;
    do {
        if (!isWriteLock) {
            ret = ClusteredHashTableLock(runCtx, false);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                // 加锁失败说明在重新上锁的间隙表已经被删除, 此时不应再对hashcluster索引上锁，应该直接返回
                SE_ERROR(ret, "clustered hash Wlock table unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                    runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
                return;
            }
        }
        if (ChScanCursorRefTimedMutex(runCtx, 1)) {
            ClusteredHashFreeEmptyPage(runCtx, &runCtx->pageCtx, dirSeg);
            ChScanCursorRefDec(runCtx, true);
            retryCount = 0;
        } else {
            retryCount--;
        }
        if (!isWriteLock) {
            ClusteredHashTableUnLock(runCtx, false);
        }
    } while (retryCount > 0);
    if (!isWriteLock) {
        ret = ClusteredHashTableLock(runCtx, true);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            // 加锁失败说明在重新上锁的间隙表已经被删除, 此时不应再对hashcluster索引上锁，应该直接返回
            SE_ERROR(ret, "clustered hash Rlock table unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return;
        }
        if (isHcIndexLockByUpper) {
            // 上层持有了hashcluster索引的锁，需要重新加回去
            ClusteredHashWLockHcIndexByUpper(runCtx);
        }
    }
}

static StatusInter ClusteredEntryEraseTuple(ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx,
    ChLabelLookupProcCtxT *procCtx, ChLabelDeleteOrReplaceParaT *userData, const HeapTupleBufT *oldTuple)
{
    DB_POINTER5(runCtx, lookUpCtx, procCtx, userData, oldTuple);
    ChLabelPageCtxT lastPageCtx = runCtx->pageCtx;
    DbMemAddrT segAddr =
        lookUpCtx->isCheckOverFlowPages ? lastPageCtx.overFlowPageCtx.pageAddr : lookUpCtx->firstSegAddr;
    ClusteredHashPageLock(runCtx, false, segAddr.virtAddr, &segAddr.pageAddr);

    // 聚簇容器删除操作订阅消息生成函数不为空
    DB_ASSERT(userData->deletePreOper.func != NULL);
    Status cbRet = userData->deletePreOper.func(
        userData->deletePreOper.stmt, userData->deletePreOper.labelCursor, oldTuple, userData->isAged);
    if (SECUREC_UNLIKELY(cbRet != GMERR_OK)) {
        ClusteredHashPageUnlock(runCtx, false, segAddr.virtAddr);
        SE_ERROR(DbGetStatusInterErrno(cbRet),
            "clustered hash, generate subs message unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        runCtx->pageCtx = lastPageCtx;
        return DbGetStatusInterErrno(cbRet);
    }

    StatusInter ret = ChLabelHcIndexMergeDelete(runCtx, oldTuple->buf, lookUpCtx->logicRowId, NULL);
    // 由于删除hashcluster索引会刷新pageCtx，因此需要重新初始化一遍
    runCtx->pageCtx = lastPageCtx;
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        ClusteredHashPageUnlock(runCtx, false, segAddr.virtAddr);
        SE_ERROR(ret, "clustered hash, merge delete HcIndex unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }

    ret = ClusteredHashDeleteUndoLog(
        runCtx, lookUpCtx->logicRowId, lookUpCtx->phyRowId.tupleId, lookUpCtx->targetBucket, lookUpCtx->bucket);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        ClusteredHashPageUnlock(runCtx, false, segAddr.virtAddr);
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_DELETE_BEFORE);

    (void)ClusteredBucketErase(runCtx, lookUpCtx, procCtx);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_DELETE_FINISHED);
    ClusteredHashRsmUndoLogMovePrevRec(runCtx);
    ClusteredHashPageUnlock(runCtx, false, segAddr.virtAddr);
    ClusteredHashFreeOneSegPageAfterDeleteProc(runCtx, lookUpCtx);
    userData->isFind = true;
    userData->tupleAddr = *(HpTupleAddr *)(void *)&lookUpCtx->logicRowId;
    return STATUS_OK_INTER;
}

static StatusInter ClusteredEntryEraseByKey(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelLookupProcCtxT *procCtx)
{
    DB_POINTER4(runCtx, lookUpCtx, procCtx, procCtx->deleteCtx.delPara);
    ChLabelDeleteOrReplaceParaT *userData = procCtx->deleteCtx.delPara;

    userData->isFind = false;
    userData->isAged = false;
    userData->isNeedUpdateViewNum = false;
    userData->deleteMark = 0;
    if (!lookUpCtx->isFind) {
        return STATUS_OK_INTER;
    }

    const HeapTupleBufT *oldTuple = ChLabelGetLastCompareTupleBuf(runCtx);
    if (userData->isReplay) {
        userData->isDrop =
            ClusteredHashCheckIsDropByVersion(runCtx, procCtx->opType, oldTuple, NULL, userData->syncVersion);
        if (SECUREC_UNLIKELY(userData->isDrop)) {
            return STATUS_OK_INTER;
        }
    }
    Status delRet = userData->checkMarkDeleteFunc(oldTuple, userData);
    if (SECUREC_UNLIKELY(delRet != GMERR_OK)) {
        SE_ERROR(DbGetStatusInterErrno(delRet),
            "clustered hash, check age unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return DbGetStatusInterErrno(delRet);
    }

    if (runCtx->isBatchOperation && userData->isNeedUpdateViewNum) {
        // 对于批量删除操作, 如果是标记删除数据或者老化记录则不可见, 不用任何处理
        userData->isFind = false;
        return STATUS_OK_INTER;
    }

    return ClusteredEntryEraseTuple(runCtx, lookUpCtx, procCtx, userData, oldTuple);
}

ALWAYS_INLINE_C static StatusInter ClusteredHashDeleteByKey(
    ChLabelRunCtxT *runCtx, IndexKeyT idxKey, ChLabelDeleteOrReplaceParaT *userData)
{
    ChLabelLookupProcCtxT procCtx;
    procCtx.opType = CHLABEL_OPT_DELETE;
    procCtx.isLogicalTuple = true;
    procCtx.isRead = false;
    procCtx.deleteCtx = (ChLabelDeleteCtxT){userData};
    return ClusteredHashLookupByKey(runCtx, idxKey, &procCtx, ClusteredEntryEraseByKey);
}

StatusInter ClusteredEntryInsertHcIndexAndWriteUndoLog(ChLabelRunCtxT *runCtx, ChLabelInsertCtxT *insertCtx)
{
    uint8_t *tupleHdr = ClusteredHashTupleHdr(&runCtx->pageCtx, insertCtx->needVisitOverFlowPages);
    ChHashClusterInsertParamT insertPara = {
        .logicRowId = insertCtx->logicRowId,
        .phyRowId = insertCtx->phyRowId,
        .buf = insertCtx->buf,
    };
    StatusInter ret = ChLabelHcIndexMergeInsert(runCtx, &insertPara, tupleHdr, NULL);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret,
            "clustered hash merge insert hc index unsucc. inner rowId(%" PRIu32 ", %" PRIu32 "), labelId: %" PRIu32
            ", indexId: %" PRIu32,
            insertCtx->phyRowId.hashCode, insertCtx->phyRowId.tupleId, runCtx->chLabel->labelCfg.labelId,
            runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    if (SECUREC_UNLIKELY(insertCtx->isMulVersion)) {
        // 升降级更新场景中，如果拆分成删除和插入操作，插入操作不记录undo
        return STATUS_OK_INTER;
    }
    ret = ClusteredHashUndoLogForInsert(runCtx, insertCtx->logicRowId);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        // 如果记录undo失败, 删除聚簇二级索引
        (void)ChLabelHcIndexMergeDelete(runCtx, insertCtx->buf, insertCtx->logicRowId, NULL);
        SE_LAST_ERROR(ret,
            "clustered hash write undo log unsucc. inner rowId(%" PRIu32 ", %" PRIu32 "), logic rowId(%" PRIu32
            ", %" PRIu32 "), labelId: %" PRIu32 ", indexId: %" PRIu32,
            insertCtx->phyRowId.hashCode, insertCtx->phyRowId.tupleId, insertCtx->logicRowId.hashCode,
            insertCtx->logicRowId.tupleId, runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    return STATUS_OK_INTER;
}

void CollectLogicalTupleIdOfSameHashCode(ChLabelRunCtxT *runCtx, HtHashCodeT hashCode, ClusteredHashEntryT *entry,
    uint16_t *sameHashCodeSlotArr, uint16_t *arrPos)
{
    uint32_t hashEntrySize = runCtx->chLabel->memMgr.dashMemMeta.hashEntrySize;
    uint32_t entryNum = runCtx->chLabel->dashMeta.hashEntryPerNormalBucket;
    ChLabelPageCtxT pageCtx = runCtx->pageCtx;
    ChBucketMetaT *targetBucket = ClusteredHashGetBucketHdr(pageCtx.normalBucketHdr, pageCtx.targetSlot);
    ChBucketMetaT *neighborBucket = ClusteredHashGetBucketHdr(pageCtx.normalBucketHdr, pageCtx.neighborSlot);
    ChStashBucketMetaT *stashBucket = (ChStashBucketMetaT *)(void *)pageCtx.stashBucketHdr;
    for (uint32_t probeLen = 0; probeLen < entryNum; ++probeLen) {
        ClusteredHashEntryT *curHashEntry = ClusteredGetEntryByBucket(targetBucket, probeLen, hashEntrySize);
        if (curHashEntry->hashCode == hashCode && curHashEntry->version == runCtx->labelVarInfo->upgradeVersion &&
            curHashEntry != entry) {
            sameHashCodeSlotArr[(*arrPos)++] = curHashEntry->logicSlot;
        }
    }
    for (uint32_t probeLen = 0; probeLen < entryNum; ++probeLen) {
        ClusteredHashEntryT *curHashEntry = ClusteredGetEntryByBucket(neighborBucket, probeLen, hashEntrySize);
        if (curHashEntry->hashCode == hashCode && curHashEntry->version == runCtx->labelVarInfo->upgradeVersion &&
            curHashEntry != entry) {
            sameHashCodeSlotArr[(*arrPos)++] = curHashEntry->logicSlot;
        }
    }

    if (targetBucket->stashedCnt != 0) {
        DB_ASSERT((targetBucket->stashedBitMap & stashBucket->allocBitMap) == targetBucket->stashedBitMap);
        uint32_t stashedBitMap = targetBucket->stashedBitMap;
        uint32_t stashedCnt = targetBucket->stashedCnt;
#ifndef NDEBUG
        DB_ASSERT(targetBucket->stashedCnt <= HASH_ENTRY_PER_STASH_BUCKET);
        BitMapCheck(stashedBitMap, targetBucket->stashedCnt);
#endif
        int32_t step = (int32_t)hashEntrySize;
        int32_t curOffset = -step;
        for (uint32_t i = 0; stashedCnt != 0 && i < HASH_ENTRY_PER_STASH_BUCKET; i++) {
            curOffset += step;
            if (!BitMapIsSet(stashedBitMap, i)) {
                continue;
            }
            stashedCnt--;
            ClusteredHashEntryT *curHashEntry = ClusteredGetStashBucketEntryByOffset(stashBucket, (uint32_t)curOffset);
            if (curHashEntry->hashCode == hashCode && curHashEntry->version == runCtx->labelVarInfo->upgradeVersion &&
                curHashEntry != entry) {
                sameHashCodeSlotArr[(*arrPos)++] = curHashEntry->logicSlot;
            }
        }
    }
}

StatusInter CollectLogicalTupleId4MulVersion(ChLabelRunCtxT *runCtx, HtHashCodeT hashCode, ClusteredHashEntryT *entry,
    uint16_t *sameHashCodeSlotArr, uint16_t *result)
{
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataByPageCtx(&runCtx->pageCtx);
    StatusInter ret;
    do {
        CollectLogicalTupleIdOfSameHashCode(runCtx, hashCode, entry, sameHashCodeSlotArr, result);
        if (ClusteredHashIsInvalidPageId(pageData->nextPageAddr)) {
            break;
        }
        ret = ClusteredHashGetNextPage(runCtx, &runCtx->pageCtx, pageData->nextPageAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
        pageData = ClusteredHashGetPageMetaDataByPageCtx(&runCtx->pageCtx);
    } while (true);
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashGenNonRepeatTupleId(ChLabelRunCtxT *runCtx, ClusteredHashEntryT *entry,
    ChLabelInsertCtxT *insertCtx, uint16_t nowSlot, uint16_t *logicalTupleId)
{
    uint32_t capacity = (uint32_t)((runCtx->chLabel->dashMeta.reverseCnt + 1u) * TARGET_NEIGHBOR_STASH_ENTRY_NUM);
    uint16_t *sameHashCodeSlotArr = (uint16_t *)DB_MALLOC((uint32_t)(capacity * sizeof(uint16_t)));
    if (sameHashCodeSlotArr == NULL) {
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_s(sameHashCodeSlotArr, capacity, 0xff, capacity);
    uint16_t arrPos = 0;
    StatusInter ret =
        CollectLogicalTupleId4MulVersion(runCtx, insertCtx->phyRowId.hashCode, entry, sameHashCodeSlotArr, &arrPos);
    DB_ASSERT(arrPos < capacity);
    if (ret != STATUS_OK_INTER || arrPos == 0) {
        *logicalTupleId = nowSlot;
        DB_FREE(sameHashCodeSlotArr);
        return ret;
    }
    bool needChangeSlotId = false;
    for (uint16_t j = 0; j < arrPos; j++) {
        if (nowSlot == sameHashCodeSlotArr[j]) {
            needChangeSlotId = true;
            break;
        }
    }
    if (!needChangeSlotId) {
        *logicalTupleId = nowSlot;
        DB_FREE(sameHashCodeSlotArr);
        return STATUS_OK_INTER;
    }
    uint32_t tupleCntPerPage = runCtx->pageCtx.pageMetaData.tupleCntPerPage;
    for (uint16_t i = 0; i < tupleCntPerPage; i++) {
        bool isFind = true;
        for (uint16_t j = 0; j < arrPos; j++) {
            if (i == sameHashCodeSlotArr[j]) {
                isFind = false;
                break;
            }
        }
        if (isFind) {
            DB_LOG_INFO("clustered hash, change logic rowId. hashCode: %u, logicalTupleId:%u to %u",
                insertCtx->phyRowId.hashCode, *logicalTupleId, i);
            *logicalTupleId = i;
            DB_FREE(sameHashCodeSlotArr);
            return STATUS_OK_INTER;
        }
    }
    DB_FREE(sameHashCodeSlotArr);
    DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "clusterded hash, cant find a diff tupleId");
    DB_ASSERT(false);
    return INTERNAL_ERROR_INTER;
}

inline static void SetInsertCtxRowId(
    ChLabelInsertCtxT *insertCtx, uint16_t phyTupleId, uint16_t logicTupleId, ClusteredHashEntryT *entry)
{
    insertCtx->phyRowId.tupleId = phyTupleId;
    insertCtx->phyRowId.version = entry->version;
    insertCtx->logicRowId.tupleId = logicTupleId;
    insertCtx->logicRowId.version = entry->version;
}

static void ClusteredHashSetTupleId(ClusteredHashEntryT *entry, ChLabelInsertCtxT *insertCtx, uint16_t freeTupleId)
{
    entry->logicSlot = insertCtx->logicRowId.tupleId;
    entry->version = insertCtx->logicRowId.version;
    insertCtx->phyRowId.tupleId = freeTupleId;
}

static StatusInter ClusteredBucketInsert(
    ChLabelRunCtxT *runCtx, ChLabelInsertCtxT *insertCtx, ChBucketMetaT *bucket, bool isPrev)
{
    StatusInter ret;
    ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
#ifndef NDEBUG
    if (runCtx->lockPage != NULL && runCtx->labelVarInfo->upgradeVersion == 0) {
        DB_ASSERT(runCtx->lockPage == pageCtx->segAddr.virtAddr);
    }
#endif
    const DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    // alloc free hashEntry slot
    uint8_t freeEntrySlot = ClusteredHashNormalBucketAllocSlot(bucket, isPrev);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_ALLOC_ENTRY);
    // alloc free tuple
    uint16_t freeTupleId = TupleSlotAlloc(pageCtx, runCtx->labelVarInfo);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_ALLOC_TUPLE_BEFORE_WRITE);
    ClusteredHashEntryT *entry = ClusteredGetEntryByBucket(bucket, freeEntrySlot, dashMemMeta->hashEntrySize);
    ChRowHeadT *rowHead = ClusteredHashGetRowHeadByTupleId(
        pageCtx->tupleHdr, pageCtx->pageMetaData.tupleSize, runCtx->chLabel->labelCfg.slotExtendSize, freeTupleId);
    ClusteredHashEntryInit(entry, insertCtx->phyRowId.hashCode, freeTupleId, runCtx->labelVarInfo->upgradeVersion);
    if (SECUREC_UNLIKELY(insertCtx->isMulVersion)) {
        ClusteredHashSetTupleId(entry, insertCtx, freeTupleId);
    } else {
        uint16_t logicalTupleId = freeTupleId;
        if (SECUREC_UNLIKELY(runCtx->labelVarInfo->hasScaledIn || runCtx->labelVarInfo->upgradeVersion != 0)) {
            ChLabelPageCtxT tmpPageCtx = runCtx->pageCtx;
            ret = ClusteredHashGenNonRepeatTupleId(runCtx, entry, insertCtx, freeTupleId, &logicalTupleId);
            runCtx->pageCtx = tmpPageCtx;
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                SE_ERROR(ret,
                    "clustered hash, find non repeat logical tupleId unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                    runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
                ClusteredHashEntryClear(entry);
                ClusteredHashNormalBucketFreeSlot(bucket, freeEntrySlot);
                (void)TupleSlotFree(pageCtx, freeTupleId, runCtx->labelVarInfo, dashMeta);
                insertCtx->phyRowId.tupleId = 0;
                return ret;
            }
            entry->logicSlot = logicalTupleId;
        }
        SetInsertCtxRowId(insertCtx, freeTupleId, logicalTupleId, entry);
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_WRITE_ENTRY);
    ClusteredHashTupleInsert(runCtx, rowHead, insertCtx);
    // set bloom filter
    ClusteredHashSetBloomFilter(insertCtx->hashCode, pageCtx->bloomFilterHdr, dashMeta->bloomFilterSize, dashMemMeta);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_WRITE_TUPLE);
    // note: 在插入hashcluster二级索引过程中可能刷新pageCtx
    ret = ClusteredEntryInsertHcIndexAndWriteUndoLog(runCtx, insertCtx);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, bucket insert unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        // 记录undo log失败，原先申请的slot需要释放
        (void)ClusteredHashInitPageCtxAndOffset(runCtx, pageCtx, insertCtx->phyRowId.hashCode);
        ClusteredHashEntryClear(entry);
        ClusteredHashNormalBucketFreeSlot(bucket, freeEntrySlot);
        (void)TupleSlotFree(pageCtx, freeTupleId, runCtx->labelVarInfo, dashMeta);
        insertCtx->phyRowId.tupleId = 0;
        return ret;
    }
    return STATUS_OK_INTER;
}

// displace bucket and return a bucket which can inserted
ChBucketMetaT *ClusteredBucketDisplace(
    const ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, ChBucketMetaT *target, ChBucketMetaT *neighbor)
{
    const DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    const DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    StatusInter ret;
    // probingBucket迁移一个entry到probingBucketNext
    // 1. probingBucketNext有空位
    // 2. 在probingBucket中存在一个非prev的entry
    ChBucketMetaT *nextNeighbor = ClusteredHashGetBucketHdr(pageCtx->normalBucketHdr, pageCtx->nextNeighborSlot);
    if (!ClusteredHashBucketFull(dashMeta, nextNeighbor)) {
        uint8_t notPrevSlot = ClusteredBucketFindNotPrevSlot(neighbor);
        if (notPrevSlot != DB_INVALID_ID8) {
            ret = ClusteredHashInsertDisplaceUndoLog(runCtx, neighbor, nextNeighbor, notPrevSlot, false);
            if (ret != STATUS_OK_INTER) {
                return NULL;
            }
            (void)ClusteredHashMoveEntryOfDifferentSrc(dashMemMeta, neighbor, notPrevSlot, nextNeighbor, false);
            ClusteredHashRsmUndoLogMovePrevRec(runCtx);
            return neighbor;
        }
    }

    // targetBucket迁移一个entry到prevBucket
    // 1. prevBucket有空槽位
    // 2. targetBucket有prev的entry
    ChBucketMetaT *prevNeighbor = ClusteredHashGetBucketHdr(pageCtx->normalBucketHdr, pageCtx->prevNeighborSlot);
    if (!ClusteredHashBucketFull(dashMeta, prevNeighbor)) {
        uint8_t prevSlot = ClusteredBucketFindPrevSlot(target);
        if (prevSlot != DB_INVALID_ID8) {
            ret = ClusteredHashInsertDisplaceUndoLog(runCtx, target, prevNeighbor, prevSlot, true);
            if (ret != STATUS_OK_INTER) {
                return NULL;
            }
            (void)ClusteredHashMoveEntryOfDifferentSrc(dashMemMeta, target, prevSlot, prevNeighbor, true);
            ClusteredHashRsmUndoLogMovePrevRec(runCtx);
            return target;
        }
    }
    return NULL;
}

StatusInter ClusteredHashSetNonRepeatLogicalTupleId(
    ChLabelRunCtxT *runCtx, ChLabelInsertCtxT *insertCtx, ClusteredHashEntryT *freeEntry, uint16_t freeTupleId)
{
    uint16_t logicalTupleId = freeTupleId;
    if (SECUREC_UNLIKELY(runCtx->labelVarInfo->hasScaledIn || runCtx->labelVarInfo->upgradeVersion != 0)) {
        ChLabelPageCtxT tmpPageCtx = runCtx->pageCtx;
        StatusInter ret = ClusteredHashGenNonRepeatTupleId(runCtx, freeEntry, insertCtx, freeTupleId, &logicalTupleId);
        runCtx->pageCtx = tmpPageCtx;
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret,
                "clustered hash, find non repeat logical tupleId unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
        freeEntry->logicSlot = logicalTupleId;
    }
    SetInsertCtxRowId(insertCtx, freeTupleId, logicalTupleId, freeEntry);
    return STATUS_OK_INTER;
}

StatusInter ClusteredStashBucketInsertInner(const DashEhMemMetaT *dashMemMeta, ChLabelRunCtxT *runCtx,
    ChLabelInsertCtxT *insertCtx, ChBucketMetaT *targetBucket, ChLabelPageCtxT *pageCtx,
    ChStashBucketMetaT *stashBucket)
{
    // alloc free hashEntry slot
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint8_t freeEntrySlot = ClusteredHashStashBucketAllocSlot(stashBucket);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_ALLOC_STASH_ENTRY);
    DB_ASSERT(freeEntrySlot < HASH_ENTRY_PER_STASH_BUCKET);
    // alloc free tuple
    uint16_t freeTupleId = TupleSlotAlloc(pageCtx, runCtx->labelVarInfo);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_ALLOC_TUPLE_BEFORE_WRITE);

    ClusteredHashEntryT *freeEntry =
        ClusteredGetEntryByStashBucket(stashBucket, freeEntrySlot, dashMemMeta->hashEntrySize);
    ChRowHeadT *rowHead = ClusteredHashGetRowHeadByTupleId(
        pageCtx->tupleHdr, pageCtx->pageMetaData.tupleSize, runCtx->chLabel->labelCfg.slotExtendSize, freeTupleId);

    ClusteredHashEntryInit(freeEntry, insertCtx->hashCode, freeTupleId, runCtx->labelVarInfo->upgradeVersion);
    if (SECUREC_UNLIKELY(insertCtx->isMulVersion)) {
        freeEntry->logicSlot = insertCtx->logicRowId.tupleId;
        freeEntry->version = insertCtx->logicRowId.version;
        insertCtx->phyRowId.tupleId = freeTupleId;
    } else {
        StatusInter ret = ClusteredHashSetNonRepeatLogicalTupleId(runCtx, insertCtx, freeEntry, freeTupleId);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            ClusteredHashEntryClear(freeEntry);
            ClusteredHashStashBucketFreeSlot(stashBucket, (uint16_t)freeEntrySlot);
            (void)TupleSlotFree(pageCtx, freeTupleId, runCtx->labelVarInfo, dashMeta);
            insertCtx->phyRowId.tupleId = 0;
            return ret;
        }
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_WRITE_ENTRY);
    ClusteredHashTupleInsert(runCtx, rowHead, insertCtx);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_WRITE_TUPLE);
    ClusteredHashSetStashedBit(targetBucket, freeEntrySlot);
    // set bloom filter
    ClusteredHashSetBloomFilter(insertCtx->hashCode, pageCtx->bloomFilterHdr, dashMeta->bloomFilterSize, dashMemMeta);
    // note: 在插入hashcluster二级索引过程中可能刷新pageCtx
    StatusInter ret = ClusteredEntryInsertHcIndexAndWriteUndoLog(runCtx, insertCtx);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, stash bucket insert unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        // 记录undo log失败，原先申请的slot需要释放
        (void)ClusteredHashInitPageCtxAndOffset(runCtx, pageCtx, insertCtx->phyRowId.hashCode);
        ClusteredHashEntryClear(freeEntry);
        ClusteredHashClearStashedBit(targetBucket, freeEntrySlot);
        ClusteredHashStashBucketFreeSlot(stashBucket, (uint16_t)freeEntrySlot);
        (void)TupleSlotFree(pageCtx, freeTupleId, runCtx->labelVarInfo, dashMeta);
        insertCtx->phyRowId.tupleId = 0;
        return ret;
    }
    DB_ASSERT((targetBucket->stashedBitMap & stashBucket->allocBitMap) == targetBucket->stashedBitMap);
    return STATUS_OK_INTER;
}

inline static StatusInter ClusteredStashBucketInsert(
    ChLabelRunCtxT *runCtx, ChLabelInsertCtxT *insertCtx, ChBucketMetaT *targetBucket)
{
    ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
#ifndef NDEBUG
    if (runCtx->lockPage != NULL && runCtx->labelVarInfo->upgradeVersion == 0) {
        DB_ASSERT(runCtx->lockPage == pageCtx->segAddr.virtAddr);
    }
#endif
    const DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    ChStashBucketMetaT *stashBucket = (ChStashBucketMetaT *)(void *)pageCtx->stashBucketHdr;
    if (SECUREC_UNLIKELY(ClusteredHashStashBucketFull(stashBucket))) {
        insertCtx->needExpand = true;
        return STATUS_OK_INTER;
    }
    return ClusteredStashBucketInsertInner(dashMemMeta, runCtx, insertCtx, targetBucket, pageCtx, stashBucket);
}

inline static StatusInter ClusteredHashBucketInsertProc(
    ChLabelRunCtxT *runCtx, ChLabelInsertCtxT *insertCtx, ChBucketMetaT *target, ChBucketMetaT *neighbor)
{
    ChLabelPageHeaderT *pageHead = ClusteredHashGetPageHead(runCtx->pageCtx.segAddr.virtAddr);
    if (SECUREC_UNLIKELY(pageHead->freeSlotCount == 0 || pageHead->freeSlotId == CLUSTERED_HASH_INVALID_SLOTID)) {
        // freeSlotCount与freeSlotId必须满足前者为0时，后者必定无效，否则大概率为踩存问题
        DB_ASSERT(pageHead->freeSlotCount == 0 && pageHead->freeSlotId == CLUSTERED_HASH_INVALID_SLOTID);
        // 当前segment空闲tuple已经用完, 需要rehash分裂
        insertCtx->needExpand = true;
        return STATUS_OK_INTER;
    }
    // freeSlotId必定小于最大值，或非指定的无效值，否则大概率为踩存问题
    DB_ASSERT(pageHead->freeSlotId < runCtx->pageCtx.pageMetaData.tupleCntPerPage ||
              pageHead->freeSlotId == CLUSTERED_HASH_INVALID_SLOTID);
    StatusInter ret;
    // try insert
    if (target->cnt <= neighbor->cnt) {
        if (SECUREC_LIKELY(!ClusteredHashBucketFull(&runCtx->chLabel->dashMeta, target))) {
            ret = ClusteredHashInsertUndoLog(runCtx, target, neighbor);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_BEFORE);
            ret = ClusteredBucketInsert(runCtx, insertCtx, target, false);
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_FINISHED);
            ClusteredHashRsmUndoLogMovePrevRec(runCtx);
            return ret;
        }
    } else {
        DB_ASSERT(!ClusteredHashBucketFull(&runCtx->chLabel->dashMeta, neighbor));
        ret = ClusteredHashInsertUndoLog(runCtx, target, neighbor);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_BEFORE);
        ret = ClusteredBucketInsert(runCtx, insertCtx, neighbor, true);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_FINISHED);
        ClusteredHashRsmUndoLogMovePrevRec(runCtx);
        return ret;
    }
    // displace
    ChBucketMetaT *newTarget = ClusteredBucketDisplace(runCtx, &runCtx->pageCtx, target, neighbor);
    ret = ClusteredHashInsertUndoLog(runCtx, target, neighbor);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_BEFORE);
    // insert again
    // newTarget不是当前target，则为prev insert
    ret = SECUREC_LIKELY(newTarget == NULL) ? ClusteredStashBucketInsert(runCtx, insertCtx, target) :
                                              ClusteredBucketInsert(runCtx, insertCtx, newTarget, newTarget != target);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_INSERT_FINISHED);
    ClusteredHashRsmUndoLogMovePrevRec(runCtx);
    return ret;
}

static StatusInter ClusteredEntryReplaceForUpdate(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelLookupProcCtxT *procCtx)
{
    DB_POINTER3(runCtx, lookUpCtx, procCtx);
    // 做老化判断
    ChLabelDeleteOrReplaceParaT *userData = procCtx->replaceCtx.userData;
    const HeapTupleBufT *oldTuple = ChLabelGetLastCompareTupleBuf(runCtx);
    StatusInter ret;
    if (userData->checkBeforeUpdateFunc != NULL) {
        ret = DbGetStatusInterErrno(userData->checkBeforeUpdateFunc(userData, oldTuple, procCtx->replaceCtx.tupleBuf));
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, check before update unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
    }
    if (SECUREC_UNLIKELY(userData->isReplay)) {
        userData->isDrop = ClusteredHashCheckIsDropByVersion(
            runCtx, procCtx->opType, oldTuple, procCtx->replaceCtx.tupleBuf, DB_MAX_UINT64);
        if (userData->isDrop) {
            return STATUS_OK_INTER;
        }
    } else {
        ret = ClusteredHashSustainDataSyncVersion(runCtx, procCtx->replaceCtx.tupleBuf);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret,
                "clustered hash, sustain sync versio unsucc when update, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
    }
    ret = DbGetStatusInterErrno(userData->checkMarkDeleteFunc(oldTuple, userData));
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, check age unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    // 如果是insert转为的rep流程，且找到的数据不为标记删除或者老化的数据，则返回violation
    if (SECUREC_UNLIKELY(userData->isIns2Rep && !userData->isNeedUpdateViewNum)) {
        return UNIQUE_VIOLATION_INTER;
    }

    // 进行正式的更新
    ChLabelReplaceCtxT replaceCtx = procCtx->replaceCtx;
    procCtx->updateCtx = (ChLabelUpdateCtxT){
        .buf = replaceCtx.tupleBuf->buf, .bufSize = replaceCtx.tupleBuf->bufSize, .isRollBack = false};
    ret = ClusteredEntryLockPageAndUpdate(runCtx, lookUpCtx, procCtx);
    if (SECUREC_LIKELY(ret == STATUS_OK_INTER)) {
        userData->tupleAddr = *(HpTupleAddr *)(void *)&lookUpCtx->logicRowId;
        userData->isFind = true;
    }
    return ret;
}

StatusInter ClusteredEntryReplaceForInsert(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelLookupProcCtxT *procCtx)
{
    DB_POINTER3(runCtx, lookUpCtx, procCtx);
    runCtx->chLabel->perfStat.chInsertCnt++;
    StatusInter ret;
    if (SECUREC_LIKELY(!procCtx->replaceCtx.userData->isReplay)) {
        ret = ClusteredHashSustainDataSyncVersion(runCtx, procCtx->replaceCtx.tupleBuf);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret,
                "clustered hash, sustain sync version unsucc when insert, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
    }
    if (!runCtx->isBatchOperation) {
        ClusteredHashStatInsert(runCtx);
    }
    ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
    ChLabelInsertCtxT insertCtx = ClusteredHashInitInsertCtx(lookUpCtx->idxKey, procCtx->replaceCtx.tupleBuf->buf,
        procCtx->replaceCtx.tupleBuf->bufSize, runCtx->pageCtx.hashCode);

    if (SECUREC_UNLIKELY(runCtx->labelVarInfo->upgradeVersion > 0 ||
                         runCtx->newSchemaVersionLength != pageCtx->pageMetaData.pageRowSize)) {
        ret = ClusteredHashInitPageCtxAndOffsetWithCheck(runCtx, &runCtx->pageCtx, insertCtx.hashCode);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_LAST_ERROR(ret, "get segment page unsucc. (%" PRIu32 "), labelId: %" PRIu32 ", indexId: %" PRIu32,
                insertCtx.hashCode, runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
    }

    ChBucketMetaT *target = ClusteredHashGetBucketHdr(pageCtx->normalBucketHdr, pageCtx->targetSlot);
    ChBucketMetaT *neighbor = ClusteredHashGetBucketHdr(pageCtx->normalBucketHdr, pageCtx->neighborSlot);
    // InsertProc 中会记录undo, 同时也会插入hashcluster索引
    DbMemAddrT segAddr = pageCtx->segAddr;
    ClusteredHashPageLock(runCtx, false, segAddr.virtAddr, &segAddr.pageAddr);
    ret = ClusteredHashBucketInsertProc(runCtx, &insertCtx, target, neighbor);
    ClusteredHashPageUnlock(runCtx, false, segAddr.virtAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        // 没有记录undo log需要将计数复原，防止逻辑计数误统计
        ClusteredHashStatInsertFailed(runCtx);
        return ret;
    }

    if (SECUREC_LIKELY(!insertCtx.needExpand)) {
        // 插入成功，返回rowId
        procCtx->replaceCtx.userData->tupleAddr = *(HpTupleAddr *)(void *)&insertCtx.logicRowId;
    } else {
        // 需要进行rehash后才能插入
        procCtx->opType = CHLABEL_OPT_INSERT;
    }
    return STATUS_OK_INTER;
}

ALWAYS_INLINE_C static StatusInter ClusteredEntryReplaceCallBack4DirectWrite(
    ChLabelRunCtxT *runCtx, const ChLabelLookupCtxT *lookUpCtx, ChLabelLookupProcCtxT *procCtx)
{
    DirectWriteUpdateStatusMergeNode *update = &(procCtx->replaceCtx.userData->dwUpdateStatusMergeNode);
    DirectWriteInsertStatusMergeNode *insert = &(procCtx->replaceCtx.userData->dwInsertStatusMergeNode);

    Status ret = GMERR_OK;
    if (lookUpCtx->isFind && update->func != NULL) {
        HeapTupleBufT *oldTuple = ChLabelGetLastCompareTupleBuf(runCtx);
        ret = update->func(update->subData, update->vertexLabel, oldTuple, procCtx->replaceCtx.tupleBuf);
    }
    if (!lookUpCtx->isFind && insert->func != NULL) {
        ret = insert->func(insert->subData, insert->vertexLabel, procCtx->replaceCtx.tupleBuf);
    }
    return DbGetStatusInterErrno(ret);
}

ALWAYS_INLINE_C static StatusInter ClusteredEntryReplaceCallBack4Server(
    ChLabelRunCtxT *runCtx, const ChLabelLookupCtxT *lookUpCtx, ChLabelReplaceCtxT *replaceCtx)
{
    UpdateStatusMergeNode *update = &(replaceCtx->userData->updateStatusMergeNode);
    InsertStatusMergeNode *insert = &(replaceCtx->userData->insertStatusMergeNode);

    Status ret = GMERR_OK;
    if (!lookUpCtx->isFind && insert->func != NULL) {
        ret = insert->func(insert->stmgDataSet, insert->vertexLabel, replaceCtx->tupleBuf, insert->endPos);
    } else if (lookUpCtx->isFind && update->func != NULL) {
        HeapTupleBufT *oldTuple = ChLabelGetLastCompareTupleBuf(runCtx);
        ret = update->func(update->stmgDataSet, update->vertexLabel, oldTuple, replaceCtx->tupleBuf);
    }
    return DbGetStatusInterErrno(ret);
}

ALWAYS_INLINE_C static StatusInter ClusteredEntryReplaceCallBack(
    ChLabelRunCtxT *runCtx, const ChLabelLookupCtxT *lookUpCtx, ChLabelLookupProcCtxT *procCtx)
{
    if (runCtx->seRunCtx != NULL && runCtx->seRunCtx->resSessionCtx.isDirectWrite) {
        return ClusteredEntryReplaceCallBack4DirectWrite(runCtx, lookUpCtx, procCtx);
    }
    return ClusteredEntryReplaceCallBack4Server(runCtx, lookUpCtx, &procCtx->replaceCtx);
}

static StatusInter ClusteredEntryReplace(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelLookupProcCtxT *procCtx)
{
    StatusInter ret;
    ChLabelReplaceCtxT *replaceCtx = &procCtx->replaceCtx;
    ChLabelDeleteOrReplaceParaT *userData = replaceCtx->userData;
    userData->isFind = false;
    userData->isAged = false;
    userData->isNeedUpdateViewNum = false;
    userData->deleteMark = 0;
    // 如果targetBucket初始化位置调整，需要评估这里的判断条件是否正确
    if (lookUpCtx->targetBucket == NULL || runCtx->labelVarInfo->upgradeVersion != 0) {
        ClusteredHashInitPageOffset(runCtx, &runCtx->pageCtx);
    }
    if (userData->isStmgVl) {
        // 状态合并表回调
        ret = ClusteredEntryReplaceCallBack(runCtx, lookUpCtx, procCtx);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered replace, status merge callback unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
    }

    if (lookUpCtx->isFind) {
        return ClusteredEntryReplaceForUpdate(runCtx, lookUpCtx, procCtx);
    }
    const HeapTupleBufT *tupleBuf = replaceCtx->tupleBuf;

    if (SECUREC_LIKELY(!lookUpCtx->isCheckOverFlowPages)) {
        if (SECUREC_LIKELY(
                (runCtx->chLabel->dashMeta.overflowPageTupleUsed == 0) || !lookUpCtx->isTargetBucketOverFlow)) {
            // 先尝试原地插入, 性能更高

            ret = ClusteredEntryReplaceForInsert(runCtx, lookUpCtx, procCtx);
            if (ret == STATUS_OK_INTER && procCtx->opType == CHLABEL_OPT_INSERT) {
                // 原地插入未能成功, 说明当前bucket已经满了, 需要走正常流程rehash之后再插入
                ChLogicRowId *logicRowId = (ChLogicRowId *)(void *)&userData->tupleAddr;
                ChLabelInsertCtxT insertCtx = ClusteredHashInitInsertCtx(
                    lookUpCtx->idxKey, tupleBuf->buf, tupleBuf->bufSize, runCtx->pageCtx.hashCode);
                ret = ClusteredHashInsertWithExpandAndOverflowPage(runCtx, &insertCtx, logicRowId);
            }
            return ret;
        }
        // 执行到下面说明在探测普通的segment页时没有找到匹配的key, 且当前bucket发生过溢出,
        // 则需要去探测一遍溢出页才能确定是执行insert还是update
        return STATUS_OK_INTER;
    } else {
        return ClusteredHashInsert(runCtx, lookUpCtx->idxKey, tupleBuf, (ChLogicRowId *)(void *)&userData->tupleAddr);
    }
}

static inline void ClusteredHashTupleUpdateCh(
    ChRowHeadT *rowHead, ChLabelUpdateCtxT *updateCtx, uint8_t **buf, uint32_t *bufSize)
{
    rowHead->totalBufSize = updateCtx->bufSize;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_CHANGE_BUFFER_SIZE);
    *buf = (uint8_t *)(rowHead + 1);
    *bufSize = updateCtx->bufSize;
}

StatusInter ClusteredHashTupleUpdate(ChLabelRunCtxT *runCtx, ChRowHeadT *rowHead, ChLabelUpdateCtxT *updateCtx)
{
    uint8_t *buf = NULL;
    uint32_t bufSize = 0;
    DB_ASSERT(updateCtx->bufSize <= runCtx->pageCtx.pageMetaData.pageRowSize);
    rowHead->trxId = ((TrxT *)(runCtx->openCfg.seRunCtx->trx))->base.trxId;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_CHANGE_TRX_ID);
    ClusteredHashTupleUpdateCh(rowHead, updateCtx, &buf, &bufSize);
    // 更新聚簇容器segment页上的数据
    DbFastMemcpy(buf, bufSize, updateCtx->buf, bufSize);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_CHANGE_ROW);
    return STATUS_OK_INTER;
}

static inline StatusInter ClusteredHashReplace(
    ChLabelRunCtxT *runCtx, IndexKeyT idxKey, HeapTupleBufT *tupleBuf, ChLabelDeleteOrReplaceParaT *userData)
{
    ChLabelLookupProcCtxT procCtx;
    procCtx.opType = CHLABEL_OPT_REPLACE;
    procCtx.isLogicalTuple = true;
    procCtx.isRead = false;
    procCtx.replaceCtx = (ChLabelReplaceCtxT){tupleBuf, userData};
    return ClusteredHashLookupByKey(runCtx, idxKey, &procCtx, ClusteredEntryReplace);
}

StatusInter ClusteredHashWriteUndoLogForUpdate(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChRowHeadT *rowHead)
{
    ChTupleT tuple;
    ClusteredHashPrepareTuple(runCtx, rowHead, &tuple, true);

    // 聚簇容器的update undo log仅记录 ChRowHeadT + rowHead->totalBufSize大小的undo
    uint32_t undoBufSize = rowHead->totalBufSize + (uint32_t)sizeof(ChRowHeadT);
    DB_ASSERT(undoBufSize <= tuple.pos);
    StatusInter ret = ClusteredHashUndoLogForUpdate(runCtx, lookUpCtx->logicRowId, tuple.buf, undoBufSize);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret,
            "clustered hash write update undo log unsucc, rowId(%" PRIu32 ", %" PRIu32 "), labelId: %" PRIu32
            ", indexId: %" PRIu32,
            lookUpCtx->logicRowId.hashCode, lookUpCtx->logicRowId.tupleId, runCtx->chLabel->labelCfg.labelId,
            runCtx->chLabel->labelCfg.indexId);
    }
    return ret;
}

StatusInter ClusteredUpdateWithChangeLock(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelUpdateCtxT *updateCtx, ChRowHeadT *rowHead)
{
    ClusteredHashPageUnlock(runCtx, false, lookUpCtx->firstSegAddr.virtAddr);
    bool isHoldHcIndexLockByUpper = runCtx->isHoldHcIndexLockByUpper;
    if (isHoldHcIndexLockByUpper) {
        ClusteredHashWUnLockHcIndexByUpper(runCtx);
    }
    ClusteredHashTableUnLock(runCtx, true);
    StatusInter ret = ClusteredHashTableLock(runCtx, false);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        // 加锁失败说明在重新上锁的间隙表已经被删除, 此时不应再对hashcluster索引上锁，应该直接返回
        return ret;
    }
    StatusInter returnRet = ClusteredUpdate4MulVersion(runCtx, lookUpCtx, updateCtx, rowHead);
    ClusteredHashTableUnLock(runCtx, false);
    ret = ClusteredHashTableLock(runCtx, true);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        // 加锁失败说明在重新上锁的间隙表已经被删除, 此时不应再对hashcluster索引上锁，应该直接返回
        return ret;
    }
    if (isHoldHcIndexLockByUpper) {
        // 上层持有了hashcluster索引的锁，需要重新加回去
        ClusteredHashWLockHcIndexByUpper(runCtx);
    }
    ClusteredHashPageLock(runCtx, false, lookUpCtx->firstSegAddr.virtAddr, &lookUpCtx->firstSegAddr.pageAddr);
    return returnRet;
}

StatusInter ClusteredEntryNormalUpdate(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelUpdateCtxT *updateCtx)
{
    uint8_t *tupleHdr = ClusteredHashTupleHdr(&runCtx->pageCtx, lookUpCtx->isCheckOverFlowPages);
    uint32_t tupleId = ClusteredHashGetInnerTupleId(runCtx, lookUpCtx->phyRowId.tupleId);
    ChRowHeadT *rowHead = ClusteredHashGetRowHeadByTupleId(
        tupleHdr, runCtx->pageCtx.pageMetaData.tupleSize, runCtx->chLabel->labelCfg.slotExtendSize, (uint16_t)tupleId);
    StatusInter ret = ClusteredHashWriteUndoLogForUpdate(runCtx, lookUpCtx, rowHead);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    ret = ClusteredHashUpdateUndoLog(runCtx, lookUpCtx, updateCtx->isRollBack);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    if (SECUREC_LIKELY(updateCtx->bufSize <= runCtx->pageCtx.pageMetaData.pageRowSize)) {
        ret = ClusteredEntryNormalUpdateHc(runCtx, rowHead, updateCtx, lookUpCtx);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, update hc unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            ClusteredHashRsmUndoLogMovePrevRec(runCtx);
            return ret;
        }
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_BEFORE);
        ret = ClusteredHashTupleUpdate(runCtx, rowHead, updateCtx);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_FINISHED);
        ClusteredHashRsmUndoLogMovePrevRec(runCtx);
        return ret;
    } else if (runCtx->isDegrade && updateCtx->bufSize > runCtx->pageCtx.pageMetaData.pageRowSize) {
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_MULVERSION_NO_CHANGE);
        ClusteredHashRsmUndoLogMovePrevRec(runCtx);
        // 连续升级后，降级至某个中间版本，更新最老版本数据时会走到
        return STATUS_OK_INTER;
    } else {
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_MULVERSION_BEFORE);
        ret = ClusteredUpdateWithChangeLock(runCtx, lookUpCtx, updateCtx, rowHead);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_MULVERSION_FINISHED);
        ClusteredHashRsmUndoLogMovePrevRec(runCtx);
        return ret;
    }
}

StatusInter ClusteredEntryRollBackUpdate(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelUpdateCtxT *updateCtx)
{
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataWithAddr(runCtx, runCtx->pageCtx.segAddr.virtAddr);
    uint8_t *tupleHdr = ClusteredHashTupleHdr(&runCtx->pageCtx, lookUpCtx->isCheckOverFlowPages);
    uint16_t tupleId = ClusteredHashGetInnerTupleId(runCtx, lookUpCtx->phyRowId.tupleId);
    ChRowHeadT *masterRowHead = ClusteredHashGetRowHeadByTupleId(
        tupleHdr, pageData->tupleSize, runCtx->chLabel->labelCfg.slotExtendSize, tupleId);
    ChRowHeadT *oldRowHead = (ChRowHeadT *)(void *)updateCtx->buf;
    if (masterRowHead->totalBufSize == oldRowHead->totalBufSize) {
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_ROLLBACK_BEFORE);
        DB_ASSERT((pageData->pageRowSize + sizeof(ChRowHeadT)) >= updateCtx->bufSize);
        DbFastMemcpy((uint8_t *)(masterRowHead + 1), masterRowHead->totalBufSize, (uint8_t *)(oldRowHead + 1),
            masterRowHead->totalBufSize);
        *masterRowHead = *oldRowHead;
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_UPDATE_ROLLBACK_FINISHED);
        return STATUS_OK_INTER;
    } else {
        return ClusteredUpdateRollBack4MulVersion(runCtx, lookUpCtx, updateCtx, oldRowHead);
    }
}

StatusInter ClusteredEntryUpdate(ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, ChLabelLookupProcCtxT *procCtx)
{
#ifndef NDEBUG
    if (runCtx->lockPage != NULL && runCtx->labelVarInfo->upgradeVersion == 0) {
        DB_ASSERT(runCtx->lockPage == lookUpCtx->firstSegAddr.virtAddr);
        DB_ASSERT(runCtx->pageCtx.segAddr.virtAddr == lookUpCtx->firstSegAddr.virtAddr);
    }
#endif
    if (SECUREC_UNLIKELY(!lookUpCtx->isFind)) {
        return NO_DATA_INTER;
    }
    ChLabelUpdateCtxT *updateCtx = &procCtx->updateCtx;
    if (SECUREC_UNLIKELY(updateCtx->isRollBack)) {
        StatusInter ret = ClusteredHashUpdateUndoLog(runCtx, lookUpCtx, updateCtx->isRollBack);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        ret = ClusteredEntryRollBackUpdate(runCtx, lookUpCtx, updateCtx);
        ClusteredHashRsmUndoLogMovePrevRec(runCtx);
        return ret;
    } else {
        return ClusteredEntryNormalUpdate(runCtx, lookUpCtx, updateCtx);
    }
}

inline static StatusInter ClusteredHashDeleteByRowId(ChLabelRunCtxT *runCtx, ChLogicRowId logicRowId)
{
    ChLabelLookupProcCtxT procCtx;
    procCtx.opType = CHLABEL_OPT_DELETE;
    procCtx.isLogicalTuple = true;
    procCtx.isRead = false;
    procCtx.deleteCtx = (ChLabelDeleteCtxT){NULL};
    return ClusteredHashLookupByLogicRowId(runCtx, logicRowId, &procCtx, ClusteredEntryLockPageAndErase);
}

ALWAYS_INLINE_C StatusInter ClusteredHashUpdateByKey(
    ChLabelRunCtxT *runCtx, IndexKeyT idxKey, uint8_t *buf, uint32_t bufSize)
{
    ChLabelLookupProcCtxT procCtx;
    procCtx.opType = CHLABEL_OPT_UPDATE;
    procCtx.isLogicalTuple = true;
    procCtx.isRead = false;
    procCtx.updateCtx = (ChLabelUpdateCtxT){.buf = buf, .bufSize = bufSize, .isRollBack = false};
    return ClusteredHashLookupByKey(runCtx, idxKey, &procCtx, ClusteredEntryLockPageAndUpdate);
}

StatusInter ClusteredHashUpdateByRowId(
    ChLabelRunCtxT *runCtx, ChLogicRowId logicRowId, const HeapTupleBufT *tupleBuf, bool isRollBack)
{
    ChLabelLookupProcCtxT procCtx;
    procCtx.opType = CHLABEL_OPT_UPDATE;
    procCtx.isLogicalTuple = true;
    procCtx.isRead = false;
    procCtx.updateCtx =
        (ChLabelUpdateCtxT){.buf = tupleBuf->buf, .bufSize = tupleBuf->bufSize, .isRollBack = isRollBack};
    // update fetch过程中已加锁，钩子函数不加page锁
    return ClusteredHashLookupByLogicRowId(runCtx, logicRowId, &procCtx, ClusteredEntryUpdateWithoutLock);
}

inline Status ChLabelInsert(
    ChLabelRunHdlT chRunHdl, IndexKeyT idxKey, const HeapTupleBufT *tupleBuf, HpTupleAddr *tupleAddr)
{
    return DbGetExternalErrno(ClusteredHashInsert(chRunHdl, idxKey, tupleBuf, (ChLogicRowId *)(void *)tupleAddr));
}

inline Status ChLabelReplace(
    ChLabelRunHdlT chRunHdl, IndexKeyT idxKey, HeapTupleBufT *tupleBuf, ChLabelDeleteOrReplaceParaT *userData)
{
    return DbGetExternalErrno(ClusteredHashReplace(chRunHdl, idxKey, tupleBuf, userData));
}

inline Status ChLabelDelete(ChLabelRunHdlT chRunHdl, HpTupleAddr tupleAddr)
{
    return DbGetExternalErrno(ClusteredHashDeleteByRowId(chRunHdl, TransformTupleAddr2LogicRowId(tupleAddr)));
}

inline Status ChLabelDeleteByKey(ChLabelRunHdlT chRunHdl, IndexKeyT idxKey, ChLabelDeleteOrReplaceParaT *userData)
{
    return DbGetExternalErrno(ClusteredHashDeleteByKey(chRunHdl, idxKey, userData));
}

inline Status ChLabelUpdate4Degrade(ChLabelRunHdlT chRunHdl, const HeapTupleBufT *tupleBuf, HpTupleAddr tupleAddr)
{
    chRunHdl->isDegrade = true;
    StatusInter ret = ClusteredHashUpdateByRowId(chRunHdl, TransformTupleAddr2LogicRowId(tupleAddr), tupleBuf, false);
    chRunHdl->isDegrade = false;
    return DbGetExternalErrno(ret);
}

inline Status ChLabelUpdate(ChLabelRunHdlT chRunHdl, const HeapTupleBufT *tupleBuf, HpTupleAddr tupleAddr)
{
    return DbGetExternalErrno(
        ClusteredHashUpdateByRowId(chRunHdl, TransformTupleAddr2LogicRowId(tupleAddr), tupleBuf, false));
}

ALWAYS_INLINE_C Status ChLabelLookUp(ChLabelRunHdlT chRunHdl, IndexKeyT idxKey, HpTupleAddr *tupleAddr, bool *isFind)
{
    DB_POINTER3(chRunHdl, tupleAddr, isFind);
    return DbGetExternalErrno(ClusteredHashLookUp(chRunHdl, idxKey, (ChLogicRowId *)(void *)tupleAddr, isFind));
}

inline Status ChLabelFetchTupleBuf(ChLabelRunHdlT chRunHdl, HpTupleAddr tupleAddr, HeapTupleBufT *heapTuple)
{
    ChTupleDataT tupleData = {.usrMemCtx = chRunHdl->openCfg.usrMemCtx, .heapTuple = heapTuple};
    DB_ASSERT(chRunHdl->openCfg.usrMemCtx != NULL);
    StatusInter ret = ClusteredHashFetch(chRunHdl, TransformTupleAddr2LogicRowId(tupleAddr), ClusteredHashCopyTupleBuf,
        &tupleData);  // 申请内存存储旧数据，由上层调用者调用ChLabelFreeTupleBuf释放
    return DbGetExternalErrno(ret);
}

inline Status ChLabelFetchTupleBuffer(ChLabelRunHdlT chRunHdl, HpTupleAddr tupleAddr, TupleBufT *tupleBuf)
{
    return DbGetExternalErrno(
        ClusteredHashFetch(chRunHdl, TransformTupleAddr2LogicRowId(tupleAddr), ClusteredHashCopyTupleBuffer, tupleBuf));
}

Status ChLabelBatchInsert(ChLabelRunHdlT chRunHdl, IndexKeyT idxKey[], const HeapTupleBufT tuples[],
    HpBatchOutT batchOuts[], uint32_t batchSize)
{
    // 批量插入涉及到rehash, 因此上层对容器加写锁, 避免频繁加锁和解锁
    ChLabelBatchBegin(chRunHdl, false);
    Status ret = GMERR_OK;
    uint32_t index = 0;
    for (; index < batchSize; ++index) {
        ret = ChLabelInsert(chRunHdl, idxKey[index], &tuples[index], &batchOuts[index].addrOut);
        if (SECUREC_UNLIKELY(ret == GMERR_UNDEFINED_TABLE)) {
            ChLabelBatchEnd(chRunHdl, false);
            // rehash过程发生了并发删表操作, 应该直接退出, 后续流程不应再去访问聚簇容器,
            // 包括对hashcluster索引解锁，更新计数等。
            return ret;
        }
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            SE_LAST_ERROR(ret,
                "clustered hash batch insert unsucc when insert tuple: %" PRIu32 ", labelId: %" PRIu32
                ", indexId: %" PRIu32,
                index, chRunHdl->chLabel->labelCfg.labelId, chRunHdl->chLabel->labelCfg.indexId);
            break;
        }
    }
    ChLabelBatchEnd(chRunHdl, false);
    ClusteredHashStatBatchInsert(chRunHdl, index);
    return GMERR_OK;
}

Status ChLabelBatchReplace(ChLabelRunHdlT chRunHdl, IndexKeyT idxKey[], HeapTupleBufT tupleBuf[],
    ChLabelBatchDeleteOrReplaceParaT *userData, uint32_t batchNum)
{
    DB_POINTER4(chRunHdl, idxKey, tupleBuf, userData);
    Status ret = GMERR_OK;
    userData->existCount = 0;
    ChLabelBatchBegin(chRunHdl, false);

    HeapTupleBufT originTupleBuf = *ChLabelGetLastCompareTupleBuf(chRunHdl);
    uint32_t heapTupleAllocSize = chRunHdl->heapTupleAllocSize;

    ChLabelDeleteOrReplaceParaT outData = {userData->isIns2Rep, userData->isReplay, userData->isStmgVl, false, false,
        false, false, 0, userData->labelDef, userData->markDeleteCheckFunc, userData->checkBeforeUpdateFunc, {0}, {0},
        {0}, {0}, {0}, DB_MAX_UINT64, HEAP_INVALID_ADDR};
    ChLabelSetStatusMergeNodePara(&outData, userData);
    uint32_t i = 0;
    for (; i < batchNum; i++) {
        chRunHdl->heapTuple = userData->oldBufArray[i];
        chRunHdl->heapTupleAllocSize = userData->oldBufArray[i].bufSize;
        InitChLabelDeleteOrReplaceOutPara(&outData);
        ret = ChLabelReplace(chRunHdl, idxKey[i], &tupleBuf[i], &outData);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            if (ret == GMERR_UNDEFINED_TABLE) {
                chRunHdl->heapTuple = originTupleBuf;
                chRunHdl->heapTupleAllocSize = heapTupleAllocSize;
                ChLabelBatchEnd(chRunHdl, false);
                // rehash过程发生了并发删表操作, 应该直接退出, 后续流程不应再去访问聚簇容器,
                // 包括对hashcluster索引解锁，更新计数等。
                return ret;
            }

            SE_LAST_ERROR(ret,
                "clustered hash, batch replace tuple %" PRIu32 "unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32, i,
                chRunHdl->chLabel->labelCfg.labelId, chRunHdl->chLabel->labelCfg.indexId);
            break;
        }
        ChLabelSetBatchUserDataForReplace(chRunHdl, &outData, i, userData);
    }
    chRunHdl->heapTuple = originTupleBuf;
    chRunHdl->heapTupleAllocSize = heapTupleAllocSize;
    ChLabelBatchEnd(chRunHdl, false);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_BATCH_REPLACE_WITHOUT_UPDATE_CNT);
    // 处理insert流程的需要刷新的物理计数和逻辑计数
    ClusteredHashStatBatchInsert(chRunHdl, i - userData->existCount);
    return ret;
}

static inline void ChLabelSetGenerateSubsPara(
    ChLabelDeleteOrReplaceParaT *outData, const ChLabelBatchDeleteOrReplaceParaT *userData)
{
    outData->deletePreOper.func = userData->deletePreOper.func;
    outData->deletePreOper.stmt = userData->deletePreOper.stmt;
    outData->deletePreOper.labelCursor = userData->deletePreOper.labelCursor;
}

Status ChLabelBatchDeleteByKey(
    ChLabelRunHdlT chRunHdl, IndexKeyT idxKey[], ChLabelBatchDeleteOrReplaceParaT *userData, uint32_t batchNum)
{
    DB_POINTER3(chRunHdl, idxKey, userData);
    Status ret = GMERR_OK;
    userData->existCount = 0;
    chRunHdl->isBatchOperation = true;
    uint32_t i = 0;
    for (; i < batchNum; ++i) {
        ChLabelDeleteOrReplaceParaT outPara = {userData->isIns2Rep, userData->isReplay, userData->isStmgVl, false,
            false, false, false, 0, userData->labelDef, userData->markDeleteCheckFunc, userData->checkBeforeUpdateFunc,
            {0}, {0}, {0}, {0}, {0}, DB_MAX_UINT64, HEAP_INVALID_ADDR};
        ChLabelSetGenerateSubsPara(&outPara, userData);
        ret = ChLabelDeleteByKey(chRunHdl, idxKey[i], &outPara);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            SE_LAST_ERROR(ret,
                "clustered hash, batch delete tuple %" PRIu32 "unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32, i,
                chRunHdl->chLabel->labelCfg.labelId, chRunHdl->chLabel->labelCfg.indexId);
            break;
        }
        ChLabelSetBatchUserData(chRunHdl, &outPara, i, userData);
    }
    chRunHdl->isBatchOperation = false;
    ClusteredHashStatBatchDelete(chRunHdl, (uint64_t)userData->existCount);
    return ret;
}

ALWAYS_INLINE_C static StatusInter ClusteredHashFindOverflowPage(ChLabelRunCtxT *runCtx, ChLabelInsertCtxT *insertCtx)
{
    bool isFind = false;
    ChLogicRowId logicRowId;
    ChPhyRowId phyRowId;
    ChLabelLookupProcCtxT procCtx;
    procCtx.opType = CHLABEL_OPT_INSERT;
    procCtx.isRead = false;
    procCtx.fetchCtx.lookUpCtx = (LookUpCtxT){&logicRowId, &isFind, &phyRowId};
    StatusInter ret = ClusteredHashLookUpOverFlowPages(
        runCtx, insertCtx->idxKey, insertCtx->hashCode, &procCtx, ClusteredEntryGetAllRowId);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    if (isFind) {
        return UNIQUE_VIOLATION_INTER;  // 发生主键冲突
    }
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashInsertCheckUnique(
    ChLabelRunCtxT *runCtx, ChLabelInsertCtxT *insertCtx, uint16_t *repeatHashCodeNum)
{
    StatusInter ret;
    ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
    ChLabelLookupCtxT lookUpCtx;
    if (insertCtx->needCheckUnique) {
        lookUpCtx = ClusteredHashInitLookUpCtxByKey(insertCtx->idxKey, false, true);
        if (runCtx->labelVarInfo->upgradeVersion == 0) {
            ret = ClusteredHashFindEntryInPageChain(runCtx, &lookUpCtx);
        } else {
            // 升降级流程需缓存pageCtx
            ret = ClusteredHashFindEntryInPageChain4MulVersion(runCtx, &lookUpCtx);
        }
#ifndef NDEBUG
        DB_ASSERT(!lookUpCtx.notExistsInBloomFilter || !lookUpCtx.isFind);
#endif
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, find hash entry unsucc when insert, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
        *repeatHashCodeNum = lookUpCtx.repeatHashCodeNum;
        lookUpCtx.targetBucket = ClusteredHashGetBucketHdr(pageCtx->normalBucketHdr, pageCtx->targetSlot);
        lookUpCtx.isTargetBucketOverFlow = ChLabelIsTargetBucketOverFlow(lookUpCtx.targetBucket);
        if (lookUpCtx.isFind) {
            return UNIQUE_VIOLATION_INTER;  // 发生主键冲突
        } else if (lookUpCtx.isTargetBucketOverFlow) {
            // 如果当前bucket增加发生溢出过，则需要探测溢出页
            ret = ClusteredHashFindOverflowPage(runCtx, insertCtx);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret,
                    "clustered hash, find overflow page hash entry unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                    runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
                return ret;
            }
        }
    }
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashPageInsertProc(ChLabelRunCtxT *runCtx, ChLabelInsertCtxT *insertCtx)
{
    // 注意: 下面在检查主键唯一性时未加页锁。
    // 因为探测过程不会修改页, 聚簇容器仅在轻量化事务下使用, 同一时刻不会有其他写线程在修改该页
    uint16_t repeatHashCodeNum = 0;
    insertCtx->needExpand = false;
    ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
    ChBucketMetaT *target = ClusteredHashGetBucketHdr(pageCtx->normalBucketHdr, pageCtx->targetSlot);
    ChBucketMetaT *neighbor = ClusteredHashGetBucketHdr(pageCtx->normalBucketHdr, pageCtx->neighborSlot);
    DbMemAddrT segAddr = pageCtx->segAddr;
    StatusInter ret = ClusteredHashInsertCheckUnique(runCtx, insertCtx, &repeatHashCodeNum);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(insertCtx->isMulVersion)) {
        ret = ClusteredHashBucketInsertProc(runCtx, insertCtx, target, neighbor);
    } else {
        ClusteredHashPageLock(runCtx, false, segAddr.virtAddr, &segAddr.pageAddr);
        ret = ClusteredHashBucketInsertProc(runCtx, insertCtx, target, neighbor);
        ClusteredHashPageUnlock(runCtx, false, segAddr.virtAddr);
    }
    if (SECUREC_UNLIKELY(insertCtx->needExpand && repeatHashCodeNum >= DUPLICATE_HASH_CODE_LIMITS)) {
        insertCtx->needVisitOverFlowPages = true;
    }
    return ret;
}

StatusInter ClusteredHashInsertOnce(ChLabelRunCtxT *runCtx, ChLabelInsertCtxT *insertCtx)
{
    StatusInter ret = ClusteredHashInitPageCtxAndOffsetWithCheck(runCtx, &runCtx->pageCtx, insertCtx->hashCode);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret, "get segment page unsucc. (%" PRIu32 "), labelId: %" PRIu32 ", indexId: %" PRIu32,
            insertCtx->hashCode, runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    return ClusteredHashPageInsertProc(runCtx, insertCtx);
}

static StatusInter ClusteredHashDealWithInsertFailure(ChLabelRunCtxT *runCtx, StatusInter ret)
{
    // 版本号不一致说明表已经被删除
    if (LabelLatchCheckVersion(runCtx->openCfg.labelRWLatch, runCtx->openCfg.labelLatchVersionId) != GMERR_OK) {
        return GMERR_UNDEFINED_TABLE_INTER;
    }
    ClusteredHashStatInsertFailed(runCtx);
    return ret;
}

static StatusInter ClusteredHashUpdateDirectory(
    ChLabelRunCtxT *runCtx, const ChLabelPageCtxT *pageCtx, ChLabelExpandCtxT *expandCtx)
{
    StatusInter ret = STATUS_OK_INTER;
    ClusteredHashLabelVarInfoT *labelVarInfo = runCtx->labelVarInfo;
    if (pageCtx->dirSeg->segDepth <= labelVarInfo->dirDepth) {
        UpdateParaT updatePara = {.segDepth = pageCtx->dirSeg->segDepth,
            .dirDepth = labelVarInfo->dirDepth,
            .dirCapacity = labelVarInfo->dirCap,
            .segId = pageCtx->pattern,
            .segIdOld = pageCtx->pattern};
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_FRESH_DIR_BEFORE);
        ClusteredHashFreshDirectory(runCtx, expandCtx, updatePara, *pageCtx->dirSeg);
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_FRESH_DIR_AFTER);
    } else {
        if (runCtx->chLabel->labelCfg.isUseRsm) {
            expandCtx->rsmChExpandRec->expandType = CLUSTERED_HASH_EXPAND_DIR;
        }
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_DOUBLE_DIR_BEFORE);
        ret = ClusteredHashDoubleDirectory(runCtx, expandCtx, pageCtx->pattern);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "clustered hash, double dir unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_DOUBLE_DIR_FAIL);
            ClusteredHashRollBackSegmentSplit(runCtx, expandCtx);
        } else {
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_DOUBLE_DIR_AFTER);
            // 对于需要对目录页进行扩容方式的rehash, 需要将旧segment页中已经搬迁到新segment页上的记录进行删除
            ret = ClusteredHashProcOldPageChain(runCtx);
            expandCtx->hasExpand = true;
        }
    }
    return ret;
}

StatusInter ClusteredHashUpdateSegAndDir(ChLabelRunCtxT *runCtx, ChLabelExpandCtxT *expandCtx)
{
    StatusInter ret = STATUS_OK_INTER;
#ifdef SYS32BITS
    ret = ClusteredHashGetAllDirPages(runCtx->chLabel);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, get all dir pages unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
#endif
    ret = ClusteredHashExpandUndoLog(runCtx, expandCtx);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_BEFORE);
    const ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
    ret = ClusteredHashSegmentSplit(runCtx, expandCtx);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, segment split unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        ClusteredHashRsmUndoLogMovePrevRec(runCtx);
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_SEG_SPLIT_END);
    ret = ClusteredHashUpdateDirectory(runCtx, pageCtx, expandCtx);
    if (runCtx->chLabel->labelCfg.isUseRsm) {
        expandCtx->rsmChExpandRec->expandType = CLUSTERED_HASH_EXPAND_FINISHED;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_EXPAND_FINISHED);
    ClusteredHashRsmUndoLogMovePrevRec(runCtx);
    return ret;
}

StatusInter ClusteredHashMoveData(ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, HashDirSegmentT *dirSeg)
{
    DB_ASSERT(runCtx->labelVarInfo->upgradeVersion != 0);
    uint32_t fileId = runCtx->chLabel->memMgr.base.fileId;
    ChLabelSegmentPageT segmentA;
    ChLabelSegmentPageT segmentB;

    segmentA.dirSeg = dirSeg;
    StatusInter ret = ClusteredHashGetSegPage(
        runCtx->memRunCtx.mdMgr, segmentA.dirSeg->pageAddr.pageAddr, fileId, &segmentA.segPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret,
            "clustered hash, get segmentA page unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32 ", pageAddr: (%" PRIu32
            ", %" PRIu32 ")",
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId,
            segmentA.dirSeg->pageAddr.pageAddr.deviceId, segmentA.dirSeg->pageAddr.pageAddr.blockId);
        return ret;
    }
    segmentA.pageHead = ClusteredHashGetPageHead(segmentA.segPageAddr);
    ChLabelPageMetaDataT pageDataA = *ClusteredHashGetPageMetaData((uint8_t *)(segmentA.pageHead + 1));

    ChLabelPageMetaDataT pageDataB = *ClusteredHashGetPageMetaData(segmentA.segPageAddr);
    PageIdT nxtPageId = pageDataB.nextPageAddr;
    segmentB.dirSeg = dirSeg;

    // 顺序遍历版本链
    while (!ClusteredHashIsInvalidPageId(nxtPageId)) {
        // segmentA 已满, 退出
        if (segmentA.pageHead->freeSlotCount == 0) {
            break;
        }

        // segmentB赋值
        ret = ClusteredHashGetSegPage(runCtx->memRunCtx.mdMgr, nxtPageId, fileId, &segmentB.segPageAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret,
                "clustered hash, get segmentB page unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32
                ", pageAddr: (%" PRIu32 ", %" PRIu32 ")",
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId, nxtPageId.deviceId,
                nxtPageId.blockId);
            return ret;
        }
        segmentB.pageHead = ClusteredHashGetPageHead(segmentB.segPageAddr);

        // 更新nxtPageId
        pageDataB = *ClusteredHashGetPageMetaData(segmentB.segPageAddr);
        nxtPageId = pageDataB.nextPageAddr;

        if (segmentA.pageHead->freeSlotCount < (pageDataB.tupleCntPerPage - segmentB.pageHead->freeSlotCount) ||
            !ClusteredHashSegPairCanScaleIn4MulVersion(
                runCtx, &pageDataA, &segmentA.segPageAddr, &pageDataB, &segmentB.segPageAddr)) {
            continue;
        }

        // segmentB数据尽可能往segmentA写
        ClusteredHashSegmentMerge4MulVersion(runCtx, &segmentA, &segmentB, pageDataA, pageDataB);
    }

    return STATUS_OK_INTER;
}

ALWAYS_INLINE_C StatusInter ClusteredHashMoveDataAfterExpand(ChLabelRunCtxT *runCtx, ChLabelExpandCtxT *expandCtx)
{
    if (SECUREC_LIKELY(runCtx->labelVarInfo->upgradeVersion == 0)) {
        return STATUS_OK_INTER;
    }

    // 尝试合并旧版本链
    StatusInter ret = ClusteredHashMoveData(runCtx, &runCtx->pageCtx, runCtx->pageCtx.dirSeg);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, move data for old segment unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }

    // 尝试合并新版本链
    ret = ClusteredHashMoveData(runCtx, &expandCtx->newPageCtx, &expandCtx->newDir);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, move data for expand segment unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }

    return ret;
}

static StatusInter ClusteredHashTryExpand(ChLabelRunCtxT *runCtx, ChLabelExpandCtxT *expandCtx)
{
    expandCtx->hasExpand = false;
    bool isWriteLock = DbRWLatchIsWriteLock(&runCtx->openCfg.labelRWLatch->rwlatch);
    bool isHoldHcIndexLockFlag = runCtx->isHoldHcIndexLockByUpper;
    StatusInter ret;
    if (!isWriteLock) {
        if (isHoldHcIndexLockFlag) {
            ClusteredHashWUnLockHcIndexByUpper(runCtx);
        }
        ClusteredHashTableUnLock(runCtx, true);
        ret = ClusteredHashTableLock(runCtx, false);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            // 加锁失败说明在重新上锁的间隙表已经被删除, 此时不应再对hashcluster索引上锁，应该直接返回
            return ret;
        }
    }

    runCtx->isExpand = true;

    // 扩容前尝试合并旧版本链
    if (SECUREC_UNLIKELY(runCtx->labelVarInfo->upgradeVersion != 0)) {
        ret = ClusteredHashMoveData(runCtx, &runCtx->pageCtx, runCtx->pageCtx.dirSeg);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, move data unsucc before expand, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            goto RELEASELOCK;
        }
    }

    ret = ClusteredHashUpdateSegAndDir(runCtx, expandCtx);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, update seg and dir unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        goto RELEASELOCK;
    }

    // 扩容后尝试合并新、旧版本链
    ret = ClusteredHashMoveDataAfterExpand(runCtx, expandCtx);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, move data unsucc after expand, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        goto RELEASELOCK;
    }

RELEASELOCK:
    runCtx->isExpand = false;
    if (!isWriteLock) {
        ClusteredHashTableUnLock(runCtx, false);
        StatusInter status = ClusteredHashTableLock(runCtx, true);
        if (SECUREC_UNLIKELY(status != STATUS_OK_INTER)) {
            // 加锁失败说明在重新上锁的间隙表已经被删除, 此时不应再对hashcluster索引上锁，应该直接返回
            return status;
        }
        if (isHoldHcIndexLockFlag) {
            // 上层持有了hashcluster索引的锁，需要重新加回去
            ClusteredHashWLockHcIndexByUpper(runCtx);
        }
    }
    return ret;
}

StatusInter ClusteredHashInsertWithExpandAndOverflowPage(
    ChLabelRunCtxT *runCtx, ChLabelInsertCtxT *insertCtx, ChLogicRowId *logicRowId)
{
    StatusInter ret;
    // 需要扩容，并且当前已经检测到散列的bucket发生了溢出, 则直接往溢出页插入数据
    if (!insertCtx->needVisitOverFlowPages) {
        insertCtx->needCheckUnique = true;
        uint32_t expandCount = 0;
        while (expandCount < HASH_ONE_INSERT_EXPAND_LIMITED) {
            // 扩容后再次尝试插入
            ChLabelExpandCtxT expandCtx = {0};
            ret = ClusteredHashTryExpand(runCtx, &expandCtx);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                SE_ERROR(ret, "clustered hash, expand unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                    runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
                return ClusteredHashDealWithInsertFailure(runCtx, ret);
            }
            if (expandCtx.hasExpand) {
                expandCount++;
            }
            ret = ClusteredHashInsertOnce(runCtx, insertCtx);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                SE_ERROR(ret, "clustered hash, insert unsucc after expand, labelId: %" PRIu32 ", indexId: %" PRIu32,
                    runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
                return ClusteredHashDealWithInsertFailure(runCtx, ret);
            }
            if (!insertCtx->needExpand) {
                *logicRowId = insertCtx->logicRowId;
                return STATUS_OK_INTER;
            } else if (insertCtx->needVisitOverFlowPages) {
                // 需要扩容，并且当前已经检测到散列的bucket发生了溢出, 则直接往溢出页插入数据
                break;
            }
        }
    }
    ChBucketMetaT *target = ClusteredHashGetBucketHdr(runCtx->pageCtx.normalBucketHdr, runCtx->pageCtx.targetSlot);
    ChLabelSetTargetBucketOverFlow(target);
    // rehash次数超过上限或者检测到当前bucket已经溢出, 则将记录插入到溢出页中
    insertCtx->needVisitOverFlowPages = true;
    ret = ClusteredHashInsertOverflowPage(runCtx, insertCtx);
    if (ret == STATUS_OK_INTER) {
        *logicRowId = TransformPhyRowId2LogicRowId(insertCtx->phyRowId);
        runCtx->chLabel->dashMeta.overflowPageTupleUsed++;
        return STATUS_OK_INTER;
    }
    return ClusteredHashDealWithInsertFailure(runCtx, ret);
}

ALWAYS_INLINE_C StatusInter ClusteredHashInsertProc(
    ChLabelRunCtxT *runCtx, ChLabelInsertCtxT *insertCtx, ChLogicRowId *logicRowId)
{
    StatusInter ret = ClusteredHashInsertOnce(runCtx, insertCtx);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ClusteredHashDealWithInsertFailure(runCtx, ret);
    }
    if (!insertCtx->needExpand) {
        *logicRowId = insertCtx->logicRowId;
        return STATUS_OK_INTER;
    }
    return ClusteredHashInsertWithExpandAndOverflowPage(runCtx, insertCtx, logicRowId);
}

StatusInter ClusteredHashInsert(
    ChLabelRunCtxT *runCtx, IndexKeyT idxKey, const HeapTupleBufT *tupleBuf, ChLogicRowId *logicRowId)
{
    DB_POINTER3(runCtx, tupleBuf, logicRowId);
    runCtx->chLabel->keyLen = idxKey.keyLen;
    runCtx->chLabel->perfStat.chInsertCnt++;

    if (!runCtx->isBatchOperation) {
        ClusteredHashStatInsert(runCtx);
    }
    ChLabelInsertCtxT insertCtx =
        ClusteredHashInitInsertCtx(idxKey, tupleBuf->buf, tupleBuf->bufSize, ClusteredHashGet26BitHashCode(idxKey));
    return ClusteredHashInsertProc(runCtx, &insertCtx, logicRowId);
}

StatusInter ClusteredHashUndoLogForInsert(ChLabelRunCtxT *runCtx, ChLogicRowId logicRowId)
{
    DB_POINTER(runCtx);
    TrxT *trx = (TrxT *)runCtx->openCfg.seRunCtx->trx;
    StatusInter ret = TrxLiteUndoLogInit(trx);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret,
            "Unable to init clustered hash trx lite undo log for insert, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    UndoLiteRecordT *undoLiteRec = trx->liteTrx.undoLiteRec;
    undoLiteRec->isChLabel = true;
    UndoLiteOpRecord *opRecord = TrxLiteGetNextFreeRecord(undoLiteRec, undoLiteRec->usedUndoRecNum);
    opRecord->recordType = TRX_OP_CH_INSERT;
    opRecord->labelId = runCtx->chLabel->labelCfg.labelId;
    // 批量操作老化对账计数的更新由上层保证其原子性
    opRecord->record.insertRec.trxId = DB_INVALID_TRX_ID;
    opRecord->record.insertRec.rowId = *(uint64_t *)(void *)&logicRowId;
    TrxLiteMoveNextFreeRecord(undoLiteRec);
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashAllocUndoRecBufForUpdate(
    const uint8_t *tupleData, uint32_t tupleLen, TrxT *trx, UndoLiteOpRecord **opRecord, AdvancedPtrT *buf)
{
    DB_POINTER3(tupleData, trx, buf);
    UndoLiteRecordT *undoLiteRec = trx->liteTrx.undoLiteRec;
    StatusInter ret = STATUS_OK_INTER;
    if (undoLiteRec->usedUndoRecNum < UNDO_LITE_INIT_RECORD_NUM) {
        *buf = TrxLiteAllocBuf(trx, tupleLen);
        if (SECUREC_UNLIKELY(APtrIsNULL(*buf))) {
            return OUT_OF_MEMORY_INTER;
        }
        (*opRecord)->record.updateRec.isUseBatchAllocMem = false;
        return STATUS_OK_INTER;
    }
    DB_ASSERT(!APtrIsNULL(undoLiteRec->lastNodePtr));
    UndoLiteRecordsNodeT *node = APtrGetAccessPtr(undoLiteRec->lastNodePtr);
    if (node->isAllocBatchTupleMem) {
        DB_ASSERT(!APtrIsNULL(node->batchAllocTuple));
        if (tupleLen + node->usedTupleSize > node->allocTupleSize) {
            ret = TrxLiteUndoLogExtend(trx);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                return ret;
            }
            node = APtrGetAccessPtr(undoLiteRec->lastNodePtr);
            *opRecord = TrxLiteGetNextFreeRecord(undoLiteRec, undoLiteRec->usedUndoRecNum);
            ret = TrxLiteUndoLogBatchAllocTupleInit(trx, tupleLen);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                return ret;
            }
            *buf = node->batchAllocTuple;
        } else {
            DB_ASSERT(*opRecord != NULL);
            *buf = APtrOffset(node->batchAllocTuple, node->usedTupleSize);
        }
    } else {
        ret = TrxLiteUndoLogBatchAllocTupleInit(trx, tupleLen);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
        *buf = node->batchAllocTuple;
    }
    node->usedTupleSize += tupleLen;
    (*opRecord)->record.updateRec.isUseBatchAllocMem = true;
    return STATUS_OK_INTER;
}

StatusInter ClusteredHashUndoLogForUpdate(
    ChLabelRunCtxT *runCtx, ChLogicRowId logicRowId, const uint8_t *tupleData, uint32_t tupleLen)
{
    DB_POINTER2(runCtx, tupleData);
    TrxT *trx = (TrxT *)runCtx->openCfg.seRunCtx->trx;
    if (SECUREC_UNLIKELY(trx->base.state != TRX_STATE_ACTIVE)) {
        return STATUS_OK_INTER;
    }
    StatusInter ret = TrxLiteUndoLogInit(trx);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret,
            "Unable to init clustered hash trx lite undo log for update, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }

    UndoLiteRecordT *undoLiteRec = trx->liteTrx.undoLiteRec;
    undoLiteRec->isChLabel = true;
    UndoLiteOpRecord *opRecord = TrxLiteGetNextFreeRecord(undoLiteRec, undoLiteRec->usedUndoRecNum);
    // 申请一块undo log buf, 在事务提交或者回滚的时候释放
    AdvancedPtrT buf = APtrNULL();
    ret = ClusteredHashAllocUndoRecBufForUpdate(tupleData, tupleLen, trx, &opRecord, &buf);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(ret,
            "Unable to alloc clustered hash trx lite buf for update, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    uint8_t *tupleBuf = APtrGetAccessPtr(buf);
    DbFastMemcpy(tupleBuf, tupleLen, tupleData, tupleLen);
    opRecord->record.updateRec.tupleBuffer = buf;
    opRecord->recordType = TRX_OP_CH_UPDATE;
    opRecord->labelId = runCtx->chLabel->labelCfg.labelId;
    opRecord->record.updateRec.trxId = trx->base.trxId;
    opRecord->record.updateRec.rowId = *(uint64_t *)(void *)&logicRowId;
    opRecord->record.updateRec.tupleLen = tupleLen;
    TrxLiteMoveNextFreeRecord(undoLiteRec);
    return STATUS_OK_INTER;
}

Status ChLabelMergeUpdateVertex(IndexKeyT indexKey, ChLabelUpdatePrepare updatePrepare, ChLabelUpdateParamsT *params)
{
    DB_POINTER3(params->hcRunCtx, updatePrepare, params);
    ChLabelRunHdlT runCtx = params->hcRunCtx;
    return DbGetExternalErrno(ClusteredHashLookUp4Update(runCtx, indexKey, params, updatePrepare));
}

StatusInter ClusteredHashInsertWithoutLookUp(ChLabelRunCtxT *runCtx, IndexKeyT idxKey, ChLabelInsertCtxT *insertCtx)
{
    ChLabelPageCtxT *pageCtx = &runCtx->pageCtx;
    ChBucketMetaT *target = ClusteredHashGetBucketHdr(pageCtx->normalBucketHdr, pageCtx->targetSlot);
    ChBucketMetaT *neighbor = ClusteredHashGetBucketHdr(pageCtx->normalBucketHdr, pageCtx->neighborSlot);
    StatusInter ret = ClusteredHashBucketInsertProc(runCtx, insertCtx, target, neighbor);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER || insertCtx->needExpand)) {
        return ClusteredHashDealWithInsertFailure(runCtx, ret);
    }
    return ret;
}

#ifdef __cplusplus
}
#endif
