/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: se_clustered_hash_label_scalein.c
 * Description: clustered hash label scale in implementation
 * Author: luoxiaoyu
 * Create: 2023/3/2
 */

#include "se_clustered_hash_label_dml.h"
#include "db_memcpy.h"
#include "dm_yang_interface.h"
#include "se_clustered_hash_hc_index.h"
#include "se_clustered_hash_label_stat.h"
#include "se_clustered_hash_label_scan.h"
#include "se_clustered_hash_label_upgrade.h"
#include "se_log.h"
#include "se_clustered_hash_rsm.h"
#include "se_clustered_hash_label_ddl.h"
#include "se_clustered_hash_label_scalein.h"

StatusInter ClusteredHashGetSegPair(
    ChLabelRunCtxT *runCtx, ChLabelSegmentPageT *segmentA, ChLabelSegmentPageT *segmentB, uint32_t index)
{
    uint32_t halfDir = runCtx->labelVarInfo->dirCap >> 1;
    segmentA->dirSeg = ClusteredHashGetDirSegmentBySegId(&runCtx->memRunCtx, index);
    if (SECUREC_UNLIKELY(segmentA->dirSeg == NULL)) {
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    segmentB->dirSeg = ClusteredHashGetDirSegmentBySegId(&runCtx->memRunCtx, index + halfDir);
    if (SECUREC_UNLIKELY(segmentB->dirSeg == NULL)) {
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    if (segmentA->dirSeg->pattern == segmentB->dirSeg->pattern ||
        (segmentA->dirSeg->pageAddr.pageId == segmentB->dirSeg->pageAddr.pageId &&
            segmentA->dirSeg->pageAddr.blockId == segmentB->dirSeg->pageAddr.blockId)) {
        return STATUS_OK_INTER;
    }
    uint32_t fileId = runCtx->chLabel->memMgr.base.fileId;
    StatusInter ret = ClusteredHashGetSegPage(
        runCtx->memRunCtx.mdMgr, segmentA->dirSeg->pageAddr.pageAddr, fileId, &segmentA->segPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    ret = ClusteredHashGetSegPage(
        runCtx->memRunCtx.mdMgr, segmentB->dirSeg->pageAddr.pageAddr, fileId, &segmentB->segPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    segmentA->pageHead = ClusteredHashGetPageHead(segmentA->segPageAddr);
    segmentB->pageHead = ClusteredHashGetPageHead(segmentB->segPageAddr);
    return STATUS_OK_INTER;
}

static uint32_t ClusteredHashGetCanMoveUpCount(
    const ChBucketMetaT *bucketANext, const ChBucketMetaT *bucketBNext, uint32_t maxMoveCount)
{
    DB_ASSERT(bucketANext->cnt >= bucketANext->localEntryCnt);
    DB_ASSERT(bucketBNext->cnt >= bucketBNext->localEntryCnt);
    return DB_MIN(
        (uint32_t)(((((uint32_t)bucketANext->cnt & 0x0000000F)) - ((uint32_t)bucketANext->localEntryCnt & 0x0000000F)) +
                   (((uint32_t)bucketBNext->cnt & 0x0000000F) - ((uint32_t)bucketBNext->localEntryCnt & 0x0000000F))),
        maxMoveCount);
}

static uint32_t ClusteredHashGetCanMoveDownCount(
    const ChBucketMetaT *bucketA, const ChBucketMetaT *bucketB, uint32_t maxMoveCount)
{
    return DB_MIN((uint32_t)(bucketA->localEntryCnt + bucketB->localEntryCnt), maxMoveCount);
}

static uint32_t ClusteredHashGetCanMoveToStashCount(
    const ChBucketMetaT *bucketA, const ChBucketMetaT *bucketB, uint32_t invalidCount, uint32_t maxMoveCount)
{
    DB_ASSERT((uint32_t)(bucketA->localEntryCnt + bucketB->localEntryCnt) >= invalidCount);
    return DB_MIN(
        (uint32_t)(((uint32_t)bucketA->localEntryCnt & 0x0000000F) + ((uint32_t)bucketB->localEntryCnt & 0x0000000F)) -
            invalidCount,
        maxMoveCount);
}

typedef struct JudgeSegPairPara {
    uint32_t bucketNum;
    uint32_t entryNum;
    uint32_t stashEntryNum;
    uint32_t bucketSize;
    uint32_t indexCount;
    uint8_t *bucketHdrA;
    uint8_t *bucketHdrB;
    ChBucketMetaT *bucketA;
    ChBucketMetaT *bucketB;
} JudgeSegPairParaT;

static bool EnableKeepMove(JudgeSegPairParaT *para, uint32_t *bkCnt, ChBucketMetaT *bucketANext,
    ChBucketMetaT *bucketBNext, uint32_t nextIndex)
{
    DB_ASSERT(
        nextIndex == 0 || bkCnt[nextIndex] >= (uint32_t)(bucketANext->localEntryCnt + bucketBNext->localEntryCnt));
    return (nextIndex != 0 && (bkCnt[nextIndex] - (uint32_t)(((uint32_t)bucketANext->localEntryCnt & 0x0000000F) +
                                                             ((uint32_t)bucketBNext->localEntryCnt & 0x0000000F)) >
                                  para->entryNum));
}

static bool ClusteredHashJudgeSegPair(JudgeSegPairParaT *para, uint32_t *bkCnt)
{
    uint32_t stashedBucketCnt = 0;
    for (uint32_t i = 0; i < para->bucketNum; i++) {
        uint32_t index = i % para->bucketNum;
        uint32_t nextIndex = (index + 1) % para->bucketNum;
        ChBucketMetaT *bucketANext = ClusteredHashGetBucketByIdx(para->bucketHdrA, nextIndex, para->bucketSize);
        ChBucketMetaT *bucketBNext = ClusteredHashGetBucketByIdx(para->bucketHdrB, nextIndex, para->bucketSize);
        bkCnt[nextIndex] =
            nextIndex != 0 ?
                (uint32_t)(((uint32_t)bucketANext->cnt & 0x0000000F) + ((uint32_t)bucketBNext->cnt & 0x0000000F)) :
                bkCnt[nextIndex];
        DB_ASSERT(
            nextIndex == 0 || bkCnt[nextIndex] >= (uint32_t)(bucketANext->localEntryCnt + bucketBNext->localEntryCnt));
        uint32_t nextPreCount = bkCnt[nextIndex] - (((uint32_t)bucketANext->localEntryCnt & 0x0000000F) +
                                                       ((uint32_t)bucketBNext->localEntryCnt & 0x0000000F));
        uint32_t currStashBkCnt =
            ((uint32_t)para->bucketA->stashedCnt & 0x0000007F) + ((uint32_t)para->bucketB->stashedCnt & 0x0000007F);
        uint32_t moveCount = 0;
        // 当前bucket中entry太多，且next bucket有空位，可以移动部分记录过去（尽可能少移动）
        if (i != para->bucketNum - 1 && bkCnt[index] > para->entryNum && nextPreCount < para->entryNum &&
            bkCnt[nextIndex] < DOUBLE_SIZE * para->entryNum) {
            moveCount = DB_MIN(para->entryNum - nextPreCount, DOUBLE_SIZE * para->entryNum - bkCnt[nextIndex]);
            moveCount = ClusteredHashGetCanMoveDownCount(
                para->bucketA, para->bucketB, DB_MIN(bkCnt[index] - para->entryNum, moveCount));
            bkCnt[index] -= moveCount;
            bkCnt[nextIndex] += moveCount;
        }
        // 当前bucket中entry太多，且stash bucket存在空位，可以移动部分记录过去（尽可能少移动）
        if (bkCnt[index] > para->entryNum && bkCnt[para->indexCount - 1] < DOUBLE_SIZE * para->stashEntryNum) {
            moveCount = ClusteredHashGetCanMoveToStashCount(para->bucketA, para->bucketB, moveCount,
                DB_MIN(bkCnt[index] - para->entryNum, DOUBLE_SIZE * para->stashEntryNum - bkCnt[para->indexCount - 1]));
            bkCnt[index] -= moveCount;
            bkCnt[para->indexCount - 1] += moveCount;
            currStashBkCnt += moveCount;
        }
        // 当前bucket中entry太多，无法移动，缩容失败
        if (bkCnt[index] > para->entryNum || EnableKeepMove(para, bkCnt, bucketANext, bucketBNext, nextIndex)) {
            return false;
        }
        // 当前bucket中entry数量合适，但是为了后面的bucket更容易缩容，尽可能多的将next bucket的记录移到本bucket
        if (i != para->bucketNum - 1 && bkCnt[index] < para->entryNum) {
            moveCount = ClusteredHashGetCanMoveUpCount(bucketANext, bucketBNext, para->entryNum - bkCnt[index]);
            bkCnt[index] += moveCount;
            bkCnt[nextIndex] -= moveCount;
        }
        // 当前bucket中entry数量合适，但是为了后面的bucket更容易缩容，尽可能多的将stash bucket的记录移到本bucket
        if (bkCnt[index] < para->entryNum) {
            moveCount =
                DB_MIN(para->entryNum - bkCnt[index], (uint32_t)(((uint32_t)para->bucketA->stashedCnt & 0x0000007F) +
                                                                 ((uint32_t)para->bucketB->stashedCnt & 0x0000007F)));
            DB_ASSERT(moveCount <= bkCnt[para->indexCount - 1]);
            bkCnt[index] += moveCount;
            bkCnt[para->indexCount - 1] -= moveCount;
            currStashBkCnt -= moveCount;
        }
        stashedBucketCnt += currStashBkCnt;
        if (stashedBucketCnt > para->stashEntryNum) {
            return false;
        }
        // 继续探测next bucket
        para->bucketA = bucketANext;
        para->bucketB = bucketBNext;
    }
    return bkCnt[para->indexCount - 1] <= HASH_ENTRY_PER_STASH_BUCKET;
}

static bool ClusteredHashJudgeSingleEntry(ChLabelRunCtxT *runCtx, ClusteredHashEntryT *curHashEntry,
    const ChLabelPageMetaDataT *pageDataA, uint32_t *bucketAllocCount, uint32_t *bucketLocalCount)
{
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint32_t entryNum = dashMeta->hashEntryPerNormalBucket;
    uint32_t minBucketNum = pageDataA->hashNormalBucketNumPerPage;
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    uint32_t targetBucketIdx =
        ChLabelGetBucketIndexByHashCode(curHashEntry->hashCode, dashMemMeta) % pageDataA->hashNormalBucketNumPerPage;
    uint32_t prevBucketIdx =
        (targetBucketIdx + pageDataA->hashNormalBucketNumPerPage - 1) % pageDataA->hashNormalBucketNumPerPage;
    uint32_t nextBucketIdx = (targetBucketIdx + 1) % pageDataA->hashNormalBucketNumPerPage;
    if (bucketAllocCount[targetBucketIdx] < entryNum) {
        bucketAllocCount[targetBucketIdx] += 1;
        bucketLocalCount[targetBucketIdx] += 1;
        return true;
    } else if (bucketAllocCount[prevBucketIdx] < entryNum &&
               bucketAllocCount[targetBucketIdx] - bucketLocalCount[targetBucketIdx] > 0) {
        bucketAllocCount[prevBucketIdx] += 1;
        bucketLocalCount[prevBucketIdx] += 1;
        bucketLocalCount[targetBucketIdx] += 1;
        return true;
    } else if (bucketAllocCount[nextBucketIdx] < entryNum) {
        bucketAllocCount[nextBucketIdx] += 1;
        return true;
    } else if (bucketAllocCount[minBucketNum] < HASH_ENTRY_PER_STASH_BUCKET) {
        bucketAllocCount[minBucketNum]++;
        return true;
    } else {
        return false;
    }
}

static bool ClusteredHashJudgeNormalBucket(ChLabelRunCtxT *runCtx, const ChLabelPageMetaDataT *pageDataA,
    const ChLabelPageMetaDataT *pageDataB, uint32_t *bucketAllocCount, uint8_t **bucketHdrB)
{
    // 遍历source page中的bucket
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    uint32_t hashEntrySize = dashMemMeta->hashEntrySize;
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint32_t entryNum = dashMeta->hashEntryPerNormalBucket;
    uint32_t maxBucketNum = pageDataB->hashNormalBucketNumPerPage;
    uint32_t indexCount = pageDataA->hashNormalBucketNumPerPage + 1;
    uint32_t *bucketLocalCount = (uint32_t *)((uint8_t *)bucketAllocCount + sizeof(uint32_t) * indexCount);

    for (uint32_t i = 0; i < maxBucketNum; i++) {
        ChBucketMetaT *bucketB = ClusteredHashGetBucketByIdx(*bucketHdrB, i, dashMeta->bucketSize);
        uint8_t bitSetMask = bucketB->allocBitMap;
        // 遍历bucket中的entry
        for (uint32_t cnt = 0, probeLen = 0; cnt < bucketB->cnt && probeLen < entryNum; ++probeLen) {
            if (!BitMapIsSet((uint32_t)bitSetMask, probeLen)) {
                continue;
            }
            cnt++;
            ClusteredHashEntryT *curHashEntry = ClusteredGetEntryByBucket(bucketB, probeLen, hashEntrySize);
            bool flag =
                ClusteredHashJudgeSingleEntry(runCtx, curHashEntry, pageDataA, bucketAllocCount, bucketLocalCount);
#ifndef NDEBUG
            for (uint32_t k = 0; k < pageDataA->hashNormalBucketNumPerPage; k++) {
                DB_ASSERT(bucketAllocCount[k] <= dashMeta->hashEntryPerNormalBucket);
            }
            DB_ASSERT(bucketAllocCount[indexCount] <= HASH_ENTRY_PER_STASH_BUCKET);
#endif
            if (!flag) {
                return false;
            }
        }
    }
#ifndef NDEBUG
    for (uint32_t k = 0; k < pageDataA->hashNormalBucketNumPerPage; k++) {
        DB_ASSERT(bucketAllocCount[k] <= dashMeta->hashEntryPerNormalBucket);
    }
    DB_ASSERT(bucketAllocCount[indexCount] <= HASH_ENTRY_PER_STASH_BUCKET);
#endif
    return true;
}

static bool ClusteredHashJudgeStashBucket(ChLabelRunCtxT *runCtx, const ChLabelPageMetaDataT *pageDataA,
    const ChLabelPageMetaDataT *pageDataB, uint32_t *bucketAllocCount, uint8_t **segPageAddrB)
{
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    uint32_t hashEntrySize = dashMemMeta->hashEntrySize;
    uint32_t indexCount = pageDataA->hashNormalBucketNumPerPage + 1;
    uint32_t *bucketLocalCount = (uint32_t *)((uint8_t *)bucketAllocCount + sizeof(uint32_t) * indexCount);
    ChStashBucketMetaT *stashBucketB =
        (ChStashBucketMetaT *)(void *)(*segPageAddrB + pageDataB->stashBucketPos + CHLABEL_PAGE_METADATA_OFFSET);
    uint32_t stashedCnt = stashBucketB->count;
    uint32_t stashedBitMap = stashBucketB->allocBitMap;
#ifndef NDEBUG
    DB_ASSERT(stashBucketB->count <= HASH_ENTRY_PER_STASH_BUCKET);
    BitMapCheck(stashedBitMap, stashedCnt);
#endif
    // 遍历stash bucket中的entry
    int32_t step = (int32_t)hashEntrySize;
    int32_t curOffset = -step;
    for (uint32_t i = 0; stashedCnt != 0 && i < HASH_ENTRY_PER_STASH_BUCKET; i++) {
        curOffset += step;
        if (!BitMapIsSet(stashedBitMap, i)) {
            continue;
        }
        stashedCnt--;
        ClusteredHashEntryT *curHashEntry = ClusteredGetStashBucketEntryByOffset(stashBucketB, (uint32_t)curOffset);
        if (!ClusteredHashJudgeSingleEntry(runCtx, curHashEntry, pageDataA, bucketAllocCount, bucketLocalCount)) {
#ifndef NDEBUG
            for (uint32_t k = 0; k < pageDataA->hashNormalBucketNumPerPage; k++) {
                DB_ASSERT(bucketAllocCount[k] <= runCtx->chLabel->dashMeta.hashEntryPerNormalBucket);
            }
            DB_ASSERT(bucketAllocCount[indexCount] <= HASH_ENTRY_PER_STASH_BUCKET);
#endif
            return false;
        }
    }
#ifndef NDEBUG
    for (uint32_t k = 0; k < pageDataA->hashNormalBucketNumPerPage; k++) {
        DB_ASSERT(bucketAllocCount[k] <= runCtx->chLabel->dashMeta.hashEntryPerNormalBucket);
    }
    DB_ASSERT(bucketAllocCount[indexCount] <= HASH_ENTRY_PER_STASH_BUCKET);
#endif
    return true;
}

bool ClusteredHashSegPairCanScaleIn4MulVersion(ChLabelRunCtxT *runCtx, const ChLabelPageMetaDataT *pageDataA,
    uint8_t **segPageAddrA, const ChLabelPageMetaDataT *pageDataB, uint8_t **segPageAddrB)
{
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint32_t minBucketNum = pageDataA->hashNormalBucketNumPerPage;
    uint32_t indexCount = minBucketNum + 1;
    uint8_t *bucketHdrA = *segPageAddrA + pageDataA->bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *bucketHdrB = *segPageAddrB + pageDataB->bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    // 申请数组，用于记录target bucket中的entry数量
    // 记录bucket的allocNum及prevNum，所以要乘2
    uint32_t capacity = (uint32_t)(sizeof(uint32_t) * indexCount * 2);
    uint32_t *bucketAllocCount = (uint32_t *)DB_MALLOC(capacity);
    if (bucketAllocCount == NULL) {
        return false;
    }
    (void)memset_s(bucketAllocCount, capacity, 0, capacity);
    uint32_t *bucketLocalCount = (uint32_t *)((uint8_t *)bucketAllocCount + sizeof(uint32_t) * indexCount);
    // 初始化统计每个bucket的entry数量
    ChBucketMetaT *bucketA = NULL;
    for (uint32_t i = 0; i < minBucketNum; i++) {
        bucketA = ClusteredHashGetBucketByIdx(bucketHdrA, i % minBucketNum, dashMeta->bucketSize);
        bucketAllocCount[i] = bucketA->cnt;
        bucketLocalCount[i] = bucketA->localEntryCnt;
    }
    ChStashBucketMetaT *stashBucketA =
        (ChStashBucketMetaT *)(void *)(*segPageAddrA + pageDataA->stashBucketPos + CHLABEL_PAGE_METADATA_OFFSET);
    bucketAllocCount[minBucketNum] = stashBucketA->count;
    bucketLocalCount[minBucketNum] = DB_INVALID_UINT32;
    if (!ClusteredHashJudgeNormalBucket(runCtx, pageDataA, pageDataB, bucketAllocCount, &bucketHdrB)) {
        DB_FREE(bucketAllocCount);
        return false;
    }
    if (!ClusteredHashJudgeStashBucket(runCtx, pageDataA, pageDataB, bucketAllocCount, segPageAddrB)) {
        DB_FREE(bucketAllocCount);
        return false;
    }
    DB_FREE(bucketAllocCount);
    return true;
}

static JudgeSegPairParaT ClusteredHashGetJudgePara(const DashEhLabelMetaT *dashMeta,
    const ChLabelPageMetaDataT *pageDataA, uint8_t **segPageAddrA, const ChLabelPageMetaDataT *pageDataB,
    uint8_t **segPageAddrB)
{
    DB_ASSERT(pageDataA->hashNormalBucketNumPerPage == pageDataB->hashNormalBucketNumPerPage);
    uint32_t bucketNum = pageDataA->hashNormalBucketNumPerPage;
    uint8_t *bucketHdrA = *segPageAddrA + pageDataA->bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *bucketHdrB = *segPageAddrB + pageDataB->bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    uint32_t indexCount = bucketNum + 1;
    ChBucketMetaT *bucketA = ClusteredHashGetBucketByIdx(bucketHdrA, 0, dashMeta->bucketSize);
    ChBucketMetaT *bucketB = ClusteredHashGetBucketByIdx(bucketHdrB, 0, dashMeta->bucketSize);
    JudgeSegPairParaT para = {
        .bucketNum = bucketNum,
        .entryNum = dashMeta->hashEntryPerNormalBucket,
        .stashEntryNum = HASH_ENTRY_PER_STASH_BUCKET,
        .indexCount = indexCount,
        .bucketHdrA = bucketHdrA,
        .bucketHdrB = bucketHdrB,
        .bucketSize = dashMeta->bucketSize,
        .bucketA = bucketA,
        .bucketB = bucketB,
    };
    return para;
}

bool ClusteredHashSegPairCanScaleIn(ChLabelRunCtxT *runCtx, const ChLabelPageMetaDataT *pageDataA,
    uint8_t **segPageAddrA, const ChLabelPageMetaDataT *pageDataB, uint8_t **segPageAddrB)
{
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    JudgeSegPairParaT para = ClusteredHashGetJudgePara(dashMeta, pageDataA, segPageAddrA, pageDataB, segPageAddrB);
    ChBucketMetaT *bucketA = para.bucketA;
    ChBucketMetaT *bucketB = para.bucketB;
    if ((uint32_t)((uint32_t)bucketA->cnt + (uint32_t)bucketB->cnt -
                   ((uint32_t)bucketA->localEntryCnt + (uint32_t)bucketB->localEntryCnt)) > para.entryNum) {
        return false;
    }
    uint32_t capacity = (uint32_t)(sizeof(uint32_t) * para.indexCount);
    uint32_t *bucketCount = (uint32_t *)DB_MALLOC(capacity);
    if (bucketCount == NULL) {
        return false;
    }
    (void)memset_s(bucketCount, capacity, 0, capacity);
    ChStashBucketMetaT *stashBucketA =
        (ChStashBucketMetaT *)(void *)(*segPageAddrA + pageDataA->stashBucketPos + CHLABEL_PAGE_METADATA_OFFSET);
    ChStashBucketMetaT *stashBucketB =
        (ChStashBucketMetaT *)(void *)(*segPageAddrB + pageDataB->stashBucketPos + CHLABEL_PAGE_METADATA_OFFSET);
    bucketCount[para.indexCount - 1] = stashBucketA->count + stashBucketB->count;
    bucketCount[0] = ((uint32_t)bucketA->cnt & 0x0000000F) + ((uint32_t)bucketB->cnt & 0x0000000F);

    // 预判断，不实际移动entry
    bool result = ClusteredHashJudgeSegPair(&para, bucketCount);
    if (!result) {
        DB_FREE(bucketCount);
        return false;
    }
    result = (bucketCount[para.indexCount - 1] <= HASH_ENTRY_PER_STASH_BUCKET);
#ifndef NDEBUG
    if (result && runCtx->labelVarInfo->upgradeVersion == 0) {
        // 释放点：缩容完成之后，走到这里必定可以缩容
        runCtx->bucketCount = malloc(capacity);
        if (runCtx->bucketCount != NULL) {
            (void)memcpy_s(runCtx->bucketCount, capacity, bucketCount, capacity);
        }
    }
#endif
    DB_FREE(bucketCount);
    return result;
}

// 搜寻两个能合并的页segmentA和segmentB
bool ClusteredHashFindSegPair(
    ChLabelRunCtxT *runCtx, ChLabelSegmentPageT *segmentA, ChLabelSegmentPageT *segmentB, ChScaleInTypeE *scaleInType)
{
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    uint32_t dirItemMax = runCtx->labelVarInfo->dirCap >> 1;
    uint32_t dirSlotId = dashMemMeta->dirNextItemIdx;
    for (; dirSlotId < dirItemMax; dirSlotId++) {
        dashMemMeta->dirNextItemIdx++;
        // 获取segmentA和segmentB
        StatusInter ret = ClusteredHashGetSegPair(runCtx, segmentA, segmentB, dirSlotId);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return false;
        }
        // segmentA和segmentB的深度相同，说明指向了同一页，不缩容（只有1页的情况也不缩容）
        if (segmentA->dirSeg->pattern == segmentB->dirSeg->pattern ||
            (segmentA->dirSeg->pageAddr.pageId == segmentB->dirSeg->pageAddr.pageId &&
                segmentA->dirSeg->pageAddr.blockId == segmentB->dirSeg->pageAddr.blockId)) {
            continue;
        }
        // 判断tuple使用率是否达到缩容门槛
        ChLabelPageHeaderT *targetPageHead = NULL;
        uint8_t *targetSegPageAddr = NULL;
        if (ClusteredHashIsChainMatch(
                runCtx, segmentA->pageHead, segmentB->pageHead, &targetPageHead, &targetSegPageAddr)) {
            if (targetSegPageAddr != NULL) {
                segmentB->pageHead = ClusteredHashGetPageHead(targetSegPageAddr);
                segmentB->segPageAddr = targetSegPageAddr;
            } else {
                targetSegPageAddr = segmentB->segPageAddr;
            }
            *scaleInType = CHLABEL_SCALE_IN_REVERSE;
            if (ClusteredHashIsPageChainSatisfy(runCtx, targetPageHead, targetSegPageAddr, segmentA, segmentB)) {
                return true;
            }
        } else if (ClusteredHashIsChainMatch(
                       runCtx, segmentB->pageHead, segmentA->pageHead, &targetPageHead, &targetSegPageAddr)) {
            if (targetSegPageAddr != NULL) {
                segmentA->pageHead = ClusteredHashGetPageHead(targetSegPageAddr);
                segmentA->segPageAddr = targetSegPageAddr;
            } else {
                targetSegPageAddr = segmentA->segPageAddr;
            }
            *scaleInType = CHLABEL_SCALE_IN_NORMAL;
            if (ClusteredHashIsPageChainSatisfy(runCtx, targetPageHead, targetSegPageAddr, segmentB, segmentA)) {
                return true;
            }
        }
    }
    return false;
}

ClusteredHashEntryT *ClusteredHashMoveStashEntry(const DashEhMemMetaT *dashMemMeta, uint8_t srcSlot,
    ChStashBucketCtxT srcStashBucketCtx, ChStashBucketCtxT dstStashBucketCtx)
{
    ChBucketMetaT *srcBucket = srcStashBucketCtx.bucket;
    ChStashBucketMetaT *src = srcStashBucketCtx.stashBucket;
    ChBucketMetaT *dstBucket = dstStashBucketCtx.bucket;
    ChStashBucketMetaT *dst = dstStashBucketCtx.stashBucket;
#ifndef NDEBUG
    CheckNormalAndStashBucketBitMap(srcBucket, src);
    CheckNormalAndStashBucketBitMap(dstBucket, dst);
#endif
    uint8_t freeSlot = ClusteredHashStashBucketAllocSlot(dst);
    ClusteredHashEntryT *dstEntry = ClusteredGetEntryByStashBucket(dst, freeSlot, dashMemMeta->hashEntrySize);
    ClusteredHashEntryT *srcEntry = ClusteredGetEntryByStashBucket(src, srcSlot, dashMemMeta->hashEntrySize);
#ifndef NDEBUG
    ClusteredHashCheckEntry(dstEntry);
#endif
    *dstEntry = *srcEntry;
    ClusteredHashStashBucketFreeSlot(src, srcSlot);
    ClusteredHashEntryClear(srcEntry);
    ClusteredHashSetStashedBit(dstBucket, freeSlot);
    ClusteredHashClearStashedBit(srcBucket, srcSlot);
#ifndef NDEBUG
    CheckNormalAndStashBucketBitMap(srcBucket, src);
    CheckNormalAndStashBucketBitMap(dstBucket, dst);
#endif
    return dstEntry;
}

ClusteredHashEntryT *ClusteredHashMoveNormalToStashEntry(
    const DashEhMemMetaT *dashMemMeta, uint8_t srcSlot, ChBucketMetaT *src, ChStashBucketCtxT dstStashBucketCtx)
{
    ChBucketMetaT *dstBucket = dstStashBucketCtx.bucket;
    ChStashBucketMetaT *dst = dstStashBucketCtx.stashBucket;
#ifndef NDEBUG
    CheckNormalBucketBitMap(src);
    CheckNormalAndStashBucketBitMap(dstBucket, dst);
#endif
    uint8_t freeSlot = ClusteredHashStashBucketAllocSlot(dst);
    ClusteredHashEntryT *dstEntry = ClusteredGetEntryByStashBucket(dst, freeSlot, dashMemMeta->hashEntrySize);
    ClusteredHashEntryT *srcEntry = ClusteredGetEntryByBucket(src, srcSlot, dashMemMeta->hashEntrySize);
#ifndef NDEBUG
    ClusteredHashCheckEntry(dstEntry);
#endif
    *dstEntry = *srcEntry;
    ClusteredHashNormalBucketFreeSlot(src, srcSlot);
    ClusteredHashEntryClear(srcEntry);
    ClusteredHashSetStashedBit(dstBucket, freeSlot);
#ifndef NDEBUG
    CheckNormalAndStashBucketBitMap(dstBucket, dst);
    CheckNormalBucketBitMap(src);
#endif
    return dstEntry;
}

ClusteredHashEntryT *ClusteredHashMoveStashToNormalEntry(
    const DashEhMemMetaT *dashMemMeta, uint8_t srcSlot, ChStashBucketCtxT srcStashBucketCtx, ChBucketMetaT *dst)
{
    ChBucketMetaT *srcBucket = srcStashBucketCtx.bucket;
    ChStashBucketMetaT *src = srcStashBucketCtx.stashBucket;
#ifndef NDEBUG
    CheckNormalBucketBitMap(dst);
    CheckNormalAndStashBucketBitMap(srcBucket, src);
#endif
    uint8_t freeSlot = ClusteredHashNormalBucketAllocSlot(dst, false);
    ClusteredHashEntryT *dstEntry = ClusteredGetEntryByBucket(dst, freeSlot, dashMemMeta->hashEntrySize);
    ClusteredHashEntryT *srcEntry = ClusteredGetEntryByStashBucket(src, srcSlot, dashMemMeta->hashEntrySize);
#ifndef NDEBUG
    ClusteredHashCheckEntry(dstEntry);
#endif
    *dstEntry = *srcEntry;
    ClusteredHashStashBucketFreeSlot(src, (uint16_t)srcSlot);
    ClusteredHashEntryClear(srcEntry);
    ClusteredHashClearStashedBit(srcBucket, srcSlot);
#ifndef NDEBUG
    CheckNormalAndStashBucketBitMap(srcBucket, src);
    CheckNormalBucketBitMap(dst);
#endif
    return dstEntry;
}

// 将一个普通页的tuple内容深拷贝到另一个普通页
static void ClusteredHashCopyNormalTuple(
    ChLabelRunCtxT *runCtx, ChLabelPageCtxT *srcPageCtx, ChLabelPageCtxT *dstPageCtx, ClusteredHashEntryT *dstEntry)
{
    uint32_t srcTupleSize = srcPageCtx->pageMetaData.tupleSize;
    uint32_t dstTupleSize = dstPageCtx->pageMetaData.tupleSize;
    uint32_t srcTupleSlot = dstEntry->phySlot;
    uint8_t *tupleSrc = ClusteredHashGetTupleBySlot(srcPageCtx->tupleHdr, srcTupleSize, srcTupleSlot);
    uint16_t freeTupleSlot = TupleSlotAlloc(dstPageCtx, runCtx->labelVarInfo);
    uint8_t *tupleDst = ClusteredHashGetTupleBySlot(dstPageCtx->tupleHdr, dstTupleSize, freeTupleSlot);
    dstEntry->phySlot = freeTupleSlot;
    uint32_t minTupleSize = DB_MIN(srcTupleSize, dstTupleSize);
    DbFastMemcpy(tupleDst, minTupleSize, tupleSrc, minTupleSize);
    (void)TupleSlotFree(srcPageCtx, srcTupleSlot, runCtx->labelVarInfo, &runCtx->chLabel->dashMeta);
}

static ChLabelPageCtxT ClusteredHashInitPageCtxWithSeg(ChLabelRunCtxT *runCtx, ChLabelSegmentPageT *segment)
{
    ChLabelPageCtxT pageCtx;
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint8_t *pageAddr = segment->segPageAddr;
    pageCtx.dirSeg = segment->dirSeg;
    pageCtx.segAddr = (DbMemAddrT){pageAddr, segment->dirSeg->pageAddr.pageAddr};
    pageCtx.pageMetaData = *ClusteredHashGetPageMetaDataWithAddr(runCtx, segment->segPageAddr);
    pageCtx.bloomFilterHdr = pageAddr + CHLABEL_PAGE_METADATA_OFFSET;
    pageCtx.bitmapHdr = pageAddr + dashMeta->bloomFilterSize + CHLABEL_PAGE_METADATA_OFFSET;
    pageCtx.normalBucketHdr = pageAddr + pageCtx.pageMetaData.bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    pageCtx.stashBucketHdr = pageAddr + pageCtx.pageMetaData.stashBucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    pageCtx.tupleHdr = pageAddr + pageCtx.pageMetaData.tuplePos + CHLABEL_PAGE_METADATA_OFFSET;
    pageCtx.pattern = segment->dirSeg->pattern;
    pageCtx.needVisitOverFlowPage = false;
    return pageCtx;
}

// 将A和B的bucket[index]中属于上一个bucket的记录尽可能多的移到上一个bucket（注：上一个bucket已合并到A）
static void ClusteredHashMoveUp(
    ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtxA, ChLabelPageCtxT *pageCtxB, uint32_t index)
{
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    DB_ASSERT(pageCtxA->pageMetaData.hashNormalBucketNumPerPage == pageCtxB->pageMetaData.hashNormalBucketNumPerPage);
    uint32_t bucketNum = pageCtxA->pageMetaData.hashNormalBucketNumPerPage;
    uint32_t entryNum = dashMeta->hashEntryPerNormalBucket;
    uint32_t bucketSize = dashMeta->bucketSize;
    ChBucketMetaT *bucketLast =
        ClusteredHashGetBucketByIdx(pageCtxA->normalBucketHdr, (bucketNum + index - 1) % bucketNum, bucketSize);
    ChBucketMetaT *bucketA =
        ClusteredHashGetBucketByIdx(pageCtxA->normalBucketHdr, (bucketNum + index) % bucketNum, bucketSize);
    ChBucketMetaT *bucketB = ClusteredHashGetBucketByIdx(pageCtxB->normalBucketHdr, index % bucketNum, bucketSize);
    // 不同页 B-->A
    uint8_t prevSlot = ClusteredBucketFindPrevSlot(bucketB);
    while (prevSlot != DB_INVALID_ID8 && bucketLast->cnt < entryNum) {
        ClusteredHashEntryT *entry =
            ClusteredHashMoveEntryOfDifferentSrc(dashMemMeta, bucketB, prevSlot, bucketLast, true);
        ClusteredHashCopyNormalTuple(runCtx, pageCtxB, pageCtxA, entry);
        prevSlot = ClusteredBucketFindPrevSlot(bucketB);
    }
    // 同一页  A-->A
    prevSlot = ClusteredBucketFindPrevSlot(bucketA);
    while (prevSlot != DB_INVALID_ID8 && bucketLast->cnt < entryNum) {
        (void)ClusteredHashMoveEntryOfDifferentSrc(dashMemMeta, bucketA, prevSlot, bucketLast, true);
        prevSlot = ClusteredBucketFindPrevSlot(bucketA);
    }
}

// 将A和B的bucket[index]中属于该bucket的记录尽可能少的移到下一个bucket（注：下一个bucket尚未合并到A）
static void ClusteredHashMoveDown(
    ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtxA, ChLabelPageCtxT *pageCtxB, uint32_t index, uint32_t maxMoveCount)
{
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint32_t entryNum = dashMeta->hashEntryPerNormalBucket;
    uint32_t bucketSize = dashMeta->bucketSize;
    DB_ASSERT(pageCtxA->pageMetaData.hashNormalBucketNumPerPage == pageCtxB->pageMetaData.hashNormalBucketNumPerPage);
    uint32_t bucketNum = pageCtxA->pageMetaData.hashNormalBucketNumPerPage;
    ChBucketMetaT *bucketB = ClusteredHashGetBucketByIdx(pageCtxB->normalBucketHdr, index % bucketNum, bucketSize);
    ChBucketMetaT *nextBucketB =
        ClusteredHashGetBucketByIdx(pageCtxB->normalBucketHdr, (index + 1) % bucketNum, bucketSize);
    ChBucketMetaT *bucketA =
        ClusteredHashGetBucketByIdx(pageCtxA->normalBucketHdr, (bucketNum + index) % bucketNum, bucketSize);
    ChBucketMetaT *nextBucketA =
        ClusteredHashGetBucketByIdx(pageCtxA->normalBucketHdr, (bucketNum + index + 1) % bucketNum, bucketSize);
    uint32_t moveCount = 0;
    // 同一页
    // A --> A
    uint8_t notPrevSlot = ClusteredBucketFindNotPrevSlot(bucketA);
    while (notPrevSlot != DB_INVALID_ID8 && nextBucketA->cnt < entryNum && moveCount < maxMoveCount) {
        moveCount++;
        (void)ClusteredHashMoveEntryOfDifferentSrc(dashMemMeta, bucketA, notPrevSlot, nextBucketA, false);
        notPrevSlot = ClusteredBucketFindNotPrevSlot(bucketA);
    };  // B-->B
    notPrevSlot = ClusteredBucketFindNotPrevSlot(bucketB);
    while (notPrevSlot != DB_INVALID_ID8 && nextBucketB->cnt < entryNum && moveCount < maxMoveCount) {
        moveCount++;
        (void)ClusteredHashMoveEntryOfDifferentSrc(dashMemMeta, bucketB, notPrevSlot, nextBucketB, false);
        notPrevSlot = ClusteredBucketFindNotPrevSlot(bucketB);
    }
    // 不同页
    // B --> A
    notPrevSlot = ClusteredBucketFindNotPrevSlot(bucketB);
    while (notPrevSlot != DB_INVALID_ID8 && nextBucketA->cnt < entryNum && moveCount < maxMoveCount) {
        moveCount++;
        ClusteredHashEntryT *entry =
            ClusteredHashMoveEntryOfDifferentSrc(dashMemMeta, bucketB, notPrevSlot, nextBucketA, false);
        ClusteredHashCopyNormalTuple(runCtx, pageCtxB, pageCtxA, entry);
        notPrevSlot = ClusteredBucketFindNotPrevSlot(bucketB);
    }
    // A -->B
    notPrevSlot = ClusteredBucketFindNotPrevSlot(bucketA);
    while (notPrevSlot != DB_INVALID_ID8 && nextBucketB->cnt < entryNum && moveCount < maxMoveCount) {
        moveCount++;
        ClusteredHashEntryT *entry =
            ClusteredHashMoveEntryOfDifferentSrc(dashMemMeta, bucketA, notPrevSlot, nextBucketB, false);
        ClusteredHashCopyNormalTuple(runCtx, pageCtxA, pageCtxB, entry);
        notPrevSlot = ClusteredBucketFindNotPrevSlot(bucketA);
    }
    DB_ASSERT(moveCount == maxMoveCount);
}

// 将A和B的bucket[index]中的记录尽可能少的移到stash bucket
static void ClusteredHashMoveToStashBucket(
    ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtxA, ChLabelPageCtxT *pageCtxB, uint32_t index, uint32_t minMoveCount)
{
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint32_t stashEntryNum = HASH_ENTRY_PER_STASH_BUCKET;
    uint32_t bucketSize = dashMeta->bucketSize;
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    DB_ASSERT(pageCtxA->pageMetaData.hashNormalBucketNumPerPage == pageCtxB->pageMetaData.hashNormalBucketNumPerPage);
    uint32_t bucketNum = pageCtxA->pageMetaData.hashNormalBucketNumPerPage;
    ChBucketMetaT *bucketB = ClusteredHashGetBucketByIdx(pageCtxB->normalBucketHdr, index % bucketNum, bucketSize);
    ChBucketMetaT *bucketA =
        ClusteredHashGetBucketByIdx(pageCtxA->normalBucketHdr, (bucketNum + index) % bucketNum, bucketSize);
    ChStashBucketMetaT *stashBucketA = (ChStashBucketMetaT *)(void *)pageCtxA->stashBucketHdr;
    ChStashBucketMetaT *stashBucketB = (ChStashBucketMetaT *)(void *)pageCtxB->stashBucketHdr;
    ChStashBucketCtxT stashBucketCtxA = {bucketA, stashBucketA};
    ChStashBucketCtxT stashBucketCtxB = {bucketB, stashBucketB};
    uint32_t moveCount = 0;
    // 同一页
    uint8_t notPrevSlot = ClusteredBucketFindNotPrevSlot(bucketA);
    while (notPrevSlot != DB_INVALID_ID8 && stashBucketA->count < stashEntryNum && moveCount < minMoveCount) {
        moveCount++;
        (void)ClusteredHashMoveNormalToStashEntry(dashMemMeta, notPrevSlot, bucketA, stashBucketCtxA);
        notPrevSlot = ClusteredBucketFindNotPrevSlot(bucketA);
    }
    notPrevSlot = ClusteredBucketFindNotPrevSlot(bucketB);
    while (notPrevSlot != DB_INVALID_ID8 && stashBucketB->count < stashEntryNum && moveCount < minMoveCount) {
        moveCount++;
        (void)ClusteredHashMoveNormalToStashEntry(dashMemMeta, notPrevSlot, bucketB, stashBucketCtxB);
        notPrevSlot = ClusteredBucketFindNotPrevSlot(bucketB);
    }
    // 不同页
    notPrevSlot = ClusteredBucketFindNotPrevSlot(bucketB);
    while (notPrevSlot != DB_INVALID_ID8 && stashBucketA->count < stashEntryNum && moveCount < minMoveCount) {
        moveCount++;
        ClusteredHashEntryT *entry =
            ClusteredHashMoveNormalToStashEntry(dashMemMeta, notPrevSlot, bucketB, stashBucketCtxA);
        ClusteredHashCopyNormalTuple(runCtx, pageCtxB, pageCtxA, entry);
        notPrevSlot = ClusteredBucketFindNotPrevSlot(bucketB);
    }
    notPrevSlot = ClusteredBucketFindNotPrevSlot(bucketA);
    while (notPrevSlot != DB_INVALID_ID8 && stashBucketB->count < stashEntryNum && moveCount < minMoveCount) {
        moveCount++;
        ClusteredHashEntryT *entry =
            ClusteredHashMoveNormalToStashEntry(dashMemMeta, notPrevSlot, bucketA, stashBucketCtxB);
        ClusteredHashCopyNormalTuple(runCtx, pageCtxA, pageCtxB, entry);
        notPrevSlot = ClusteredBucketFindNotPrevSlot(bucketA);
    }
    DB_ASSERT(moveCount == minMoveCount);
}

// 将A和B的bucket[index]中存放到了stash bucket中的记录尽可能多的取回（注：本bucket已合并到A）
static void ClusteredHashMoveFromStashBucket(
    ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtxA, ChLabelPageCtxT *pageCtxB, uint32_t index)
{
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint32_t entryNum = dashMeta->hashEntryPerNormalBucket;
    uint32_t bucketSize = dashMeta->bucketSize;
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    DB_ASSERT(pageCtxA->pageMetaData.hashNormalBucketNumPerPage == pageCtxB->pageMetaData.hashNormalBucketNumPerPage);
    uint32_t bucketNum = pageCtxA->pageMetaData.hashNormalBucketNumPerPage;
    ChBucketMetaT *bucketA =
        ClusteredHashGetBucketByIdx(pageCtxA->normalBucketHdr, (bucketNum + index) % bucketNum, bucketSize);
    ChBucketMetaT *bucketB = ClusteredHashGetBucketByIdx(pageCtxB->normalBucketHdr, index % bucketNum, bucketSize);
    ChStashBucketMetaT *stashBucketA = (ChStashBucketMetaT *)(void *)pageCtxA->stashBucketHdr;
    ChStashBucketMetaT *stashBucketB = (ChStashBucketMetaT *)(void *)pageCtxB->stashBucketHdr;
    ChStashBucketCtxT stashBucketCtxA = {bucketA, stashBucketA};
    ChStashBucketCtxT stashBucketCtxB = {bucketB, stashBucketB};
    // 不同页
    while (bucketB->stashedBitMap > 0 && bucketA->cnt < entryNum) {
        uint8_t overflowSlot = ClusteredBucketFindOverflowSlot(bucketB);
        ClusteredHashEntryT *entry =
            ClusteredHashMoveStashToNormalEntry(dashMemMeta, overflowSlot, stashBucketCtxB, bucketA);
        ClusteredHashCopyNormalTuple(runCtx, pageCtxB, pageCtxA, entry);
    }
    // 同一页
    while (bucketA->stashedBitMap > 0 && bucketA->cnt < entryNum) {
        uint8_t overflowSlot = ClusteredBucketFindOverflowSlot(bucketA);
        (void)ClusteredHashMoveStashToNormalEntry(dashMemMeta, overflowSlot, stashBucketCtxA, bucketA);
    }
}

// 将B的bucket[index]全部合并到A的bucket[index]
static void ClusteredHashMergeNormalBucket(
    ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtxA, ChLabelPageCtxT *pageCtxB, uint32_t index)
{
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint32_t entryNum = dashMeta->hashEntryPerNormalBucket;
    uint32_t bucketSize = dashMeta->bucketSize;
    DB_ASSERT(pageCtxA->pageMetaData.hashNormalBucketNumPerPage == pageCtxB->pageMetaData.hashNormalBucketNumPerPage);
    uint32_t bucketNum = pageCtxA->pageMetaData.hashNormalBucketNumPerPage;
    ChBucketMetaT *bucketA =
        ClusteredHashGetBucketByIdx(pageCtxA->normalBucketHdr, (bucketNum + index) % bucketNum, bucketSize);
    ChBucketMetaT *bucketB = ClusteredHashGetBucketByIdx(pageCtxB->normalBucketHdr, index % bucketNum, bucketSize);
    // 不同页
    while (bucketB->cnt > 0 && bucketA->cnt < entryNum) {
        uint8_t notEmptySlot = ClusteredBucketFindNotEmptySlotInNormalBucket(bucketB);
        bool isPrev = BitMapIsSet((uint32_t)(bucketB->allocBitMap & bucketB->prevBitMap), notEmptySlot);
        ClusteredHashEntryT *entry =
            ClusteredHashMoveEntryOfSameSrc(dashMemMeta, bucketB, notEmptySlot, bucketA, isPrev);
        ClusteredHashCopyNormalTuple(runCtx, pageCtxB, pageCtxA, entry);
    }
    // 必须全部合并成功
    DB_ASSERT(bucketB->cnt == 0);
}

// 将B的某个bucket中存在stash bucket的数据全部合并到A的stash bucket，并转移溢出页标志
static void ClusteredHashMergeStashBucket(
    ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtxA, ChLabelPageCtxT *pageCtxB, uint32_t index)
{
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint32_t bucketSize = dashMeta->bucketSize;
    ChStashBucketMetaT *stashBucketA = (ChStashBucketMetaT *)(void *)pageCtxA->stashBucketHdr;
    ChStashBucketMetaT *stashBucketB = (ChStashBucketMetaT *)(void *)pageCtxB->stashBucketHdr;
    DB_ASSERT(pageCtxA->pageMetaData.hashNormalBucketNumPerPage == pageCtxB->pageMetaData.hashNormalBucketNumPerPage);
    uint32_t bucketNum = pageCtxA->pageMetaData.hashNormalBucketNumPerPage;
    ChBucketMetaT *bucketA =
        ClusteredHashGetBucketByIdx(pageCtxA->normalBucketHdr, (bucketNum + index) % bucketNum, bucketSize);
    ChBucketMetaT *bucketB = ClusteredHashGetBucketByIdx(pageCtxB->normalBucketHdr, index % bucketNum, bucketSize);
    ChStashBucketCtxT stashBucketCtxA = {bucketA, stashBucketA};
    ChStashBucketCtxT stashBucketCtxB = {bucketB, stashBucketB};
    // 不同页
    while (bucketB->stashedBitMap > 0 && stashBucketA->count < HASH_ENTRY_PER_STASH_BUCKET) {
        uint8_t overflowSlot = ClusteredBucketFindOverflowSlot(bucketB);
        ClusteredHashEntryT *entry =
            ClusteredHashMoveStashEntry(dashMemMeta, overflowSlot, stashBucketCtxB, stashBucketCtxA);
        ClusteredHashCopyNormalTuple(runCtx, pageCtxB, pageCtxA, entry);
    }
    // 需要转移B的溢出位标记到A上面，否则假如A没有溢出，而B有溢出，那么B溢出的数据会找不到
    if (ChLabelIsTargetBucketOverFlow(bucketB)) {
        ChLabelSetTargetBucketOverFlow(bucketA);
        ChLabelClearTargetBucketOverFlow(bucketB);
    }
}

static void ClusteredHashMergeAllStashBucket(
    ChLabelRunCtxT *runCtx, ChStashBucketMetaT *stashBucket, ChLabelPageCtxT *pageCtxA, ChLabelPageCtxT *pageCtxB)
{
    if (stashBucket->count == 0) {
        return;
    }
    DB_ASSERT(pageCtxA->pageMetaData.hashNormalBucketNumPerPage == pageCtxB->pageMetaData.hashNormalBucketNumPerPage);
    uint32_t bucketNum = pageCtxA->pageMetaData.hashNormalBucketNumPerPage;
    for (uint32_t i = 0; i < bucketNum; i++) {
        ClusteredHashMergeStashBucket(runCtx, pageCtxA, pageCtxB, i);
    }
    DB_ASSERT(stashBucket->count == 0);
}

// 重新设置布隆过滤器（先清空，再设置）
static void ClusteredHashUpdateBloomFilter(ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx)
{
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    uint8_t *bloomFilterHdr = pageCtx->bloomFilterHdr;
    uint16_t bloomFilterSize = runCtx->chLabel->dashMeta.bloomFilterSize;
    ClusteredHashClearBloomFilter(bloomFilterHdr, bloomFilterSize);
    uint32_t maxBucketNum = pageCtx->pageMetaData.hashNormalBucketNumPerPage;
    uint32_t entryNum = dashMeta->hashEntryPerNormalBucket;
    uint32_t hashEntrySize = dashMemMeta->hashEntrySize;
    uint8_t *bucketHdr = pageCtx->normalBucketHdr;
    uint32_t bucketSize = dashMeta->bucketSize;
    for (uint32_t i = 0; i < maxBucketNum; i++) {
        ChBucketMetaT *bucket = ClusteredHashGetBucketByIdx(bucketHdr, i, bucketSize);
        uint8_t allocBitMap = bucket->allocBitMap;
        for (uint32_t probeLen = 0; probeLen < entryNum; ++probeLen) {
            ClusteredHashEntryT *entry = ClusteredGetEntryByBucket(bucket, probeLen, hashEntrySize);
            if (!BitMapIsSet((uint32_t)allocBitMap, probeLen)) {
#ifndef NDEBUG
                ClusteredHashCheckEntry(entry);
#endif
                continue;
            }
#ifndef NDEBUG
            bool isSet = TupleBitMapIsSet(pageCtx->bitmapHdr, entry->phySlot);
            DB_ASSERT(isSet);
#endif
            ClusteredHashSetBloomFilter(entry->hashCode, bloomFilterHdr, bloomFilterSize, dashMemMeta);
        }
    }
    ChStashBucketMetaT *stashBucket = (ChStashBucketMetaT *)(void *)pageCtx->stashBucketHdr;
    for (uint32_t i = 0; i < HASH_ENTRY_PER_STASH_BUCKET; i++) {
        ClusteredHashEntryT *entry = ClusteredGetEntryByStashBucket(stashBucket, i, hashEntrySize);
        if (!BitMapIsSet((uint32_t)stashBucket->allocBitMap, i)) {
#ifndef NDEBUG
            ClusteredHashCheckEntry(entry);
#endif
            continue;
        }
#ifndef NDEBUG
        bool isSet = TupleBitMapIsSet(pageCtx->bitmapHdr, entry->phySlot);
        DB_ASSERT(isSet);
#endif
        ClusteredHashSetBloomFilter(entry->hashCode, bloomFilterHdr, bloomFilterSize, dashMemMeta);
    }
}

// 正式合并两个页（B合并到A，其中B为空页）
void ClusteredHashEmptySegmentMerge(
    ChLabelRunCtxT *runCtx, ChLabelSegmentPageT *segmentA, ChLabelSegmentPageT *segmentB)
{
    ClusteredHashFreeMergeSegment(runCtx, segmentA, segmentB, segmentB->dirSeg->pageAddr.pageAddr);
}

#ifndef NDEBUG
// 用于debug模式下对缩容流程进行校验
void ClusteredHashDebugPageAlloc(ChLabelRunCtxT *runCtx, ChLabelSegmentPageT *segmentA, ChLabelSegmentPageT *segmentB)
{
    if (runCtx->labelVarInfo->upgradeVersion == 0) {
        uint16_t bucketCnt = runCtx->chLabel->dashMeta.metaData.hashNormalBucketNumPerPage;
        uint32_t bucketSize = (uint32_t)(bucketCnt * DEBUG_SCALEIN_PROCESS_BASE_SIZE);
        // 一次性申请2个页 + 缩容中间过程存储大小
        runCtx->mergePageA = malloc(DEBUG_PAGE_SIZE * 2 + bucketSize);
        if (runCtx->mergePageA != NULL) {
            runCtx->mergePageB = runCtx->mergePageA + DEBUG_PAGE_SIZE;
            (void)memcpy_s(runCtx->mergePageA, DEBUG_PAGE_SIZE, segmentA->pageHead, DEBUG_PAGE_SIZE);
            (void)memcpy_s(runCtx->mergePageB, DEBUG_PAGE_SIZE, segmentB->pageHead, DEBUG_PAGE_SIZE);
            if (memcmp(runCtx->mergePageA, runCtx->judgePageA, DEBUG_PAGE_SIZE) == 0) {
                DB_ASSERT(memcmp(runCtx->mergePageB, runCtx->judgePageB, DEBUG_PAGE_SIZE) == 0);
            } else {
                DB_ASSERT(memcmp(runCtx->mergePageA, runCtx->judgePageB, DEBUG_PAGE_SIZE) == 0);
                DB_ASSERT(memcmp(runCtx->mergePageB, runCtx->judgePageA, DEBUG_PAGE_SIZE) == 0);
            }
            runCtx->scaleInProcessInfo.bucketA = (ChBucketMetaT *)(void *)(runCtx->mergePageB + DEBUG_PAGE_SIZE);
            (void)memset_s(runCtx->scaleInProcessInfo.bucketA, bucketSize, 0, bucketSize);
            runCtx->scaleInProcessInfo.bucketB = runCtx->scaleInProcessInfo.bucketA + bucketCnt;
            runCtx->scaleInProcessInfo.bkNextA = runCtx->scaleInProcessInfo.bucketB + bucketCnt;
            runCtx->scaleInProcessInfo.bkNextB = runCtx->scaleInProcessInfo.bkNextA + bucketCnt;
            runCtx->scaleInProcessInfo.stashBucketA =
                (ChStashBucketMetaT *)(void *)(runCtx->scaleInProcessInfo.bkNextB + bucketCnt);
            runCtx->scaleInProcessInfo.stashBucketB = runCtx->scaleInProcessInfo.stashBucketA + bucketCnt;
        }
    }
}

void ClusteredHashDebugPageFree(ChLabelRunCtxT *runCtx)
{
    // 释放debug的2个页
    if (runCtx->labelVarInfo->upgradeVersion == 0) {
        if (runCtx->bucketCount != NULL) {
            free(runCtx->bucketCount);
        }
        if (runCtx->mergePageA != NULL) {
            free(runCtx->mergePageA);
        }
        if (runCtx->judgePageA != NULL) {
            free(runCtx->judgePageA);
        }
        runCtx->bucketCount = NULL;
        runCtx->mergePageA = NULL;
        runCtx->mergePageB = NULL;
        runCtx->judgePageA = NULL;
        runCtx->judgePageB = NULL;
        runCtx->scaleInProcessInfo.bucketA = NULL;
        runCtx->scaleInProcessInfo.bucketB = NULL;
        runCtx->scaleInProcessInfo.stashBucketA = NULL;
        runCtx->scaleInProcessInfo.stashBucketB = NULL;
    }
}

void ClusteredHashDebugSetBucketInfo(
    ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtxA, ChLabelPageCtxT *pageCtxB, uint32_t bucketIndex)
{
    if (runCtx->labelVarInfo->upgradeVersion != 0) {
        return;
    }
    uint32_t bucketNum = runCtx->chLabel->dashMeta.metaData.hashNormalBucketNumPerPage;
    uint32_t bucketSize = runCtx->chLabel->dashMeta.bucketSize;
    uint32_t bucketMetaSize = (uint32_t)sizeof(ChBucketMetaT);
    uint32_t stashBucketMetaSize = (uint32_t)sizeof(ChStashBucketMetaT);
    ChBucketMetaT *bucketA = ClusteredHashGetBucketByIdx(pageCtxA->normalBucketHdr, bucketIndex, bucketSize);
    ChBucketMetaT *bucketB = ClusteredHashGetBucketByIdx(pageCtxB->normalBucketHdr, bucketIndex, bucketSize);
    uint32_t nextIndex = (bucketIndex + 1) % bucketNum;
    ChBucketMetaT *bkNextA = ClusteredHashGetBucketByIdx(pageCtxA->normalBucketHdr, nextIndex, bucketSize);
    ChBucketMetaT *bkNextB = ClusteredHashGetBucketByIdx(pageCtxB->normalBucketHdr, nextIndex, bucketSize);
    ChStashBucketMetaT *stashBucketA = (ChStashBucketMetaT *)(void *)pageCtxA->stashBucketHdr;
    ChStashBucketMetaT *stashBucketB = (ChStashBucketMetaT *)(void *)pageCtxB->stashBucketHdr;
    (void)memcpy_s((&runCtx->scaleInProcessInfo.bucketA[bucketIndex]), bucketMetaSize, bucketA, bucketMetaSize);
    (void)memcpy_s(&runCtx->scaleInProcessInfo.bucketB[bucketIndex], bucketMetaSize, bucketB, bucketMetaSize);
    (void)memcpy_s(&runCtx->scaleInProcessInfo.bkNextA[bucketIndex], bucketMetaSize, bkNextA, bucketMetaSize);
    (void)memcpy_s(&runCtx->scaleInProcessInfo.bkNextB[bucketIndex], bucketMetaSize, bkNextB, bucketMetaSize);
    (void)memcpy_s(
        &runCtx->scaleInProcessInfo.stashBucketA[bucketIndex], stashBucketMetaSize, stashBucketA, stashBucketMetaSize);
    (void)memcpy_s(
        &runCtx->scaleInProcessInfo.stashBucketB[bucketIndex], stashBucketMetaSize, stashBucketB, stashBucketMetaSize);
}

#endif

// 正式合并两个页（B合并到A）
bool ClusteredHashSegmentTryMerge(ChLabelRunCtxT *runCtx, ChLabelSegmentPageT *segmentA, ChLabelSegmentPageT *segmentB,
    const ChLabelPageMetaDataT *pageDataA, const ChLabelPageMetaDataT *pageDataB)
{
#ifndef NDEBUG
    ClusteredHashDebugPageAlloc(runCtx, segmentA, segmentB);
#endif
    ChLabelPageCtxT pageCtxA = ClusteredHashInitPageCtxWithSeg(runCtx, segmentA);
    ChLabelPageCtxT pageCtxB = ClusteredHashInitPageCtxWithSeg(runCtx, segmentB);
    DB_ASSERT(pageDataA->hashNormalBucketNumPerPage == pageDataB->hashNormalBucketNumPerPage);
    uint32_t bucketNum = pageDataA->hashNormalBucketNumPerPage;
    uint32_t entryNum = runCtx->chLabel->dashMeta.hashEntryPerNormalBucket;
    uint32_t bucketSize = runCtx->chLabel->dashMeta.bucketSize;
    ChStashBucketMetaT *stashBucketA = (ChStashBucketMetaT *)(void *)pageCtxA.stashBucketHdr;
    ChStashBucketMetaT *stashBucketB = (ChStashBucketMetaT *)(void *)pageCtxB.stashBucketHdr;
    ChBucketMetaT *bucketA = ClusteredHashGetBucketByIdx(pageCtxA.normalBucketHdr, 0, bucketSize);
    ChBucketMetaT *bucketB = ClusteredHashGetBucketByIdx(pageCtxB.normalBucketHdr, 0, bucketSize);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_HORIZONTAL_SCALE_IN_BEFORE);
    for (uint32_t i = 0; i < bucketNum; i++) {
        ChBucketMetaT *bkANext = ClusteredHashGetBucketByIdx(pageCtxA.normalBucketHdr, (i + 1) % bucketNum, bucketSize);
        ChBucketMetaT *bkBNext = ClusteredHashGetBucketByIdx(pageCtxB.normalBucketHdr, (i + 1) % bucketNum, bucketSize);
        uint32_t nextTotalCount = (uint32_t)bkANext->cnt + (uint32_t)bkBNext->cnt;
        uint32_t nextPreCount = nextTotalCount - ((uint32_t)bkANext->localEntryCnt + (uint32_t)bkBNext->localEntryCnt);
        if (i != bucketNum - 1 && (uint32_t)bucketA->cnt + (uint32_t)bucketB->cnt > entryNum &&
            nextPreCount < entryNum && nextTotalCount < DOUBLE_SIZE * entryNum) {
            uint32_t maxMoveCount =
                DB_MIN((uint32_t)bucketA->cnt + (uint32_t)bucketB->cnt - entryNum, entryNum - nextPreCount);
            maxMoveCount = DB_MIN(maxMoveCount, DOUBLE_SIZE * entryNum - nextTotalCount);
            ClusteredHashMoveDown(runCtx, &pageCtxA, &pageCtxB, i, maxMoveCount);
        }
        if ((uint32_t)bucketA->cnt + (uint32_t)bucketB->cnt > entryNum &&
            stashBucketA->count + stashBucketB->count < DOUBLE_SIZE * HASH_ENTRY_PER_STASH_BUCKET) {
            uint32_t minMoveCount = DB_MIN((uint32_t)bucketA->cnt + (uint32_t)bucketB->cnt - entryNum,
                DOUBLE_SIZE * HASH_ENTRY_PER_STASH_BUCKET - (stashBucketA->count + stashBucketB->count));
            ClusteredHashMoveToStashBucket(runCtx, &pageCtxA, &pageCtxB, i, minMoveCount);
        }
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_HORIZONTAL_SCALE_IN_MERGE_HALF);
        if ((uint32_t)bucketA->cnt + (uint32_t)bucketB->cnt > entryNum) {
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_HORIZONTAL_SCALE_IN_EXIT);
#ifndef NDEBUG
            ClusteredHashDebugSetBucketInfo(runCtx, &pageCtxA, &pageCtxB, i);
            DB_ASSERT(false);
#endif
            return false;
        }
        ClusteredHashMergeNormalBucket(runCtx, &pageCtxA, &pageCtxB, i);
        if (i != bucketNum - 1) {
            ClusteredHashMoveUp(runCtx, &pageCtxA, &pageCtxB, i + 1);
        }
        ClusteredHashMoveFromStashBucket(runCtx, &pageCtxA, &pageCtxB, i);
        ClusteredHashMergeStashBucket(runCtx, &pageCtxA, &pageCtxB, i);
#ifndef NDEBUG
        ClusteredHashDebugSetBucketInfo(runCtx, &pageCtxA, &pageCtxB, i);
#endif
        bucketA = bkANext;
        bucketB = bkBNext;
        if ((uint32_t)(bucketA->cnt + bucketB->cnt - (bucketA->localEntryCnt + bucketB->localEntryCnt)) > entryNum) {
            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_HORIZONTAL_SCALE_IN_EXIT);
            DB_ASSERT(false);
            return false;
        }
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_HORIZONTAL_SCALE_IN_MERGE_BUCKET);
    ClusteredHashMergeAllStashBucket(runCtx, stashBucketB, &pageCtxA, &pageCtxB);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_HORIZONTAL_SCALE_IN_MERGE_STASH_BUCKET);
    segmentB->pageHead->freeSlotCount = DB_INVALID_UINT16;
    ClusteredHashUpdateBloomFilter(runCtx, &pageCtxA);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_HORIZONTAL_SCALE_IN_END);
#ifndef NDEBUG
    ClusteredHashDebugPageFree(runCtx);
#endif
    return true;
}

static bool ClusteredHashMergeSingleEntry(ChLabelRunCtxT *runCtx, ClusteredHashEntryT *curHashEntry,
    ChStashBucketMetaT *stashBucketA, ChLabelPageCtxT *pageCtxA, ChLabelPageCtxT *pageCtxB)
{
    DashEhMemMetaT *memMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint32_t entryNum = dashMeta->hashEntryPerNormalBucket;
    ChLabelPageMetaDataT pageDataA = pageCtxA->pageMetaData;
    uint32_t targetBucketIdx = ChLabelGetBucketIndexByHashCode(curHashEntry->hashCode, memMeta);
    uint32_t targetSlot = ChLabelGetBucketSlotByIndex(targetBucketIdx, dashMeta, pageDataA.hashNormalBucketNumPerPage);
    ChBucketMetaT *targetBucket = ClusteredHashGetBucketHdr(pageCtxA->normalBucketHdr, targetSlot);
    uint32_t prevSlot = ChLabelGetBucketSlotByIndex(
        (targetBucketIdx + pageDataA.hashNormalBucketNumPerPage - 1) % pageDataA.hashNormalBucketNumPerPage, dashMeta,
        pageDataA.hashNormalBucketNumPerPage);
    ChBucketMetaT *prevBucket = ClusteredHashGetBucketHdr(pageCtxA->normalBucketHdr, prevSlot);
    uint32_t nextSlot = ChLabelGetBucketSlotByIndex(
        (targetBucketIdx + 1) % pageDataA.hashNormalBucketNumPerPage, dashMeta, pageDataA.hashNormalBucketNumPerPage);
    ChBucketMetaT *nextBucket = ClusteredHashGetBucketHdr(pageCtxA->normalBucketHdr, nextSlot);
    if (targetBucket->cnt < entryNum) {
        uint8_t freeEntrySlot = ClusteredHashNormalBucketAllocSlot(targetBucket, false);
        ClusteredHashEntryT *targetEntry =
            ClusteredGetEntryByBucket(targetBucket, freeEntrySlot, memMeta->hashEntrySize);
        *targetEntry = *curHashEntry;
        ClusteredHashCopyNormalTuple(runCtx, pageCtxB, pageCtxA, targetEntry);
    } else if (prevBucket->cnt < entryNum && targetBucket->cnt - targetBucket->localEntryCnt > 0) {
        uint8_t prevEntrySlot = ClusteredBucketFindPrevSlot(targetBucket);
        ClusteredHashEntryT *targetEntry =
            ClusteredGetEntryByBucket(targetBucket, prevEntrySlot, memMeta->hashEntrySize);
        uint8_t freeEntrySlot = ClusteredHashNormalBucketAllocSlot(prevBucket, false);
        ClusteredHashEntryT *prevEntry = ClusteredGetEntryByBucket(prevBucket, freeEntrySlot, memMeta->hashEntrySize);
        *prevEntry = *targetEntry;
        *targetEntry = *curHashEntry;
        ClusteredHashCopyNormalTuple(runCtx, pageCtxB, pageCtxA, targetEntry);
        targetBucket->prevBitMap = (uint8_t)BitMapUnSet((uint32_t)targetBucket->prevBitMap, prevEntrySlot);
        targetBucket->localEntryCnt++;
    } else if (nextBucket->cnt < entryNum) {
        uint8_t freeEntrySlot = ClusteredHashNormalBucketAllocSlot(nextBucket, true);
        ClusteredHashEntryT *nextEntry = ClusteredGetEntryByBucket(nextBucket, freeEntrySlot, memMeta->hashEntrySize);
        *nextEntry = *curHashEntry;
        ClusteredHashCopyNormalTuple(runCtx, pageCtxB, pageCtxA, nextEntry);
    } else if (stashBucketA->count < HASH_ENTRY_PER_STASH_BUCKET) {
        uint8_t freeEntrySlot = ClusteredHashStashBucketAllocSlot(stashBucketA);
        ClusteredHashEntryT *freeEntry =
            ClusteredGetEntryByStashBucket(stashBucketA, freeEntrySlot, memMeta->hashEntrySize);
        *freeEntry = *curHashEntry;
        ClusteredHashCopyNormalTuple(runCtx, pageCtxB, pageCtxA, freeEntry);
        ClusteredHashSetStashedBit(targetBucket, freeEntrySlot);
    } else {
        DB_ASSERT(runCtx->isExpand);
        return false;
    }
    return true;
}

static bool ClusteredHashCmpPageAndTupleRowSize(
    ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtxA, ChLabelPageCtxT *pageCtxB, ClusteredHashEntryT *curHashEntry)
{
    if (!runCtx->isExpand || pageCtxA->pageMetaData.pageRowSize >= pageCtxB->pageMetaData.pageRowSize) {
        return true;
    }

    uint32_t pageRowSizeA = pageCtxA->pageMetaData.pageRowSize;
    uint32_t tupleSizeB = pageCtxB->pageMetaData.tupleSize;
    uint8_t *tupleHdrB = pageCtxB->tupleHdr;
    ChRowHeadT *chRowHead = ClusteredHashGetRowHeadByTupleId(
        tupleHdrB, tupleSizeB, runCtx->chLabel->labelCfg.slotExtendSize, curHashEntry->phySlot);

    return chRowHead->totalBufSize <= pageRowSizeA;
}

static void ClusteredHashMergeAllStashBucket4MulVersion(ChLabelRunCtxT *runCtx, ChStashBucketMetaT *stashBucketA,
    ChStashBucketMetaT *stashBucketB, ChLabelPageCtxT *pageCtxA, ChLabelPageCtxT *pageCtxB)
{
    if (stashBucketB->count == 0) {
        return;
    }
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint32_t hashEntrySize = dashMemMeta->hashEntrySize;
    uint32_t stashedCnt = stashBucketB->count;
    uint32_t stashedBitMap = stashBucketB->allocBitMap;
#ifndef NDEBUG
    DB_ASSERT(runCtx->isExpand || stashBucketB->count <= HASH_ENTRY_PER_STASH_BUCKET);
    BitMapCheck(stashedBitMap, stashBucketB->count);
#endif

    ChLabelPageMetaDataT pageDataB = pageCtxB->pageMetaData;

    // 遍历stash bucket中的entry
    int32_t step = (int32_t)hashEntrySize;
    int32_t curOffset = -step;
    ChLabelPageHeaderT *pageHeadA = ClusteredHashGetPageHead(pageCtxA->segAddr.virtAddr);

    for (uint32_t i = 0; stashedCnt != 0 && i < HASH_ENTRY_PER_STASH_BUCKET && pageHeadA->freeSlotCount != 0; i++) {
        curOffset += step;
        if (!BitMapIsSet(stashedBitMap, i)) {
            continue;
        }
        stashedCnt--;
        ClusteredHashEntryT *curHashEntry = ClusteredGetStashBucketEntryByOffset(stashBucketB, (uint32_t)curOffset);
        if (ClusteredHashCmpPageAndTupleRowSize(runCtx, pageCtxA, pageCtxB, curHashEntry) &&
            ClusteredHashMergeSingleEntry(runCtx, curHashEntry, stashBucketA, pageCtxA, pageCtxB)) {
            // 扩容合并场景，删除后重置bucket和entry信息
            ClusteredHashStashBucketFreeSlot(stashBucketB, (uint16_t)i);
            uint32_t targetBucketIdx = ChLabelGetBucketIndexByHashCode(curHashEntry->hashCode, dashMemMeta);
            uint32_t targetSlot =
                ChLabelGetBucketSlotByIndex(targetBucketIdx, dashMeta, pageDataB.hashNormalBucketNumPerPage);
            ChBucketMetaT *targetBucket = ClusteredHashGetBucketHdr(pageCtxB->normalBucketHdr, targetSlot);
            ClusteredHashClearStashedBit(targetBucket, i);
            ClusteredHashEntryClear(curHashEntry);
        }
    }
#ifndef NDEBUG
    DB_ASSERT(runCtx->isExpand || stashBucketB->count == 0);
#endif
}

// 升降级同一segment从下往上搬迁数据
void ClusteredHashSegmentMerge4MulVersion(ChLabelRunCtxT *runCtx, ChLabelSegmentPageT *segmentA,
    ChLabelSegmentPageT *segmentB, ChLabelPageMetaDataT pageDataA, ChLabelPageMetaDataT pageDataB)
{
    StatusInter ret = ClusteredHashMergeVerticalUndoLog(runCtx, segmentA, segmentB);
    if (ret != STATUS_OK_INTER) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE,
            "Record vertical segment merge undo log unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return;
    }
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    ChLabelPageCtxT pageCtxA = ClusteredHashInitPageCtxWithSeg(runCtx, segmentA);
    ChLabelPageCtxT pageCtxB = ClusteredHashInitPageCtxWithSeg(runCtx, segmentB);
    pageCtxA.pageMetaData = pageDataA;
    pageCtxB.pageMetaData = pageDataB;
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint32_t maxBucketNum = pageDataB.hashNormalBucketNumPerPage;
    uint32_t entryNum = dashMeta->hashEntryPerNormalBucket;
    uint32_t bucketSize = dashMeta->bucketSize;
    uint8_t *bucketHdrB = pageCtxB.normalBucketHdr;
    ChStashBucketMetaT *stashBucketA = (ChStashBucketMetaT *)(void *)pageCtxA.stashBucketHdr;
    ChStashBucketMetaT *stashBucketB = (ChStashBucketMetaT *)(void *)pageCtxB.stashBucketHdr;
    uint32_t hashEntrySize = dashMemMeta->hashEntrySize;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_VERTICAL_SCALE_IN_BEFORE);
    ChLabelPageHeaderT *pageHeadA = ClusteredHashGetPageHead(pageCtxA.segAddr.virtAddr);
    for (uint32_t i = 0; i < maxBucketNum && pageHeadA->freeSlotCount != 0; i++) {
        ChBucketMetaT *bucketB = ClusteredHashGetBucketByIdx(bucketHdrB, i, bucketSize);
        uint8_t bitSetMask = bucketB->allocBitMap;
        // 遍历bucket中的entry
        for (uint32_t cnt = 0, probeLen = 0; cnt < bucketB->cnt && probeLen < entryNum && pageHeadA->freeSlotCount != 0;
             ++probeLen) {
            if (!BitMapIsSet((uint32_t)bitSetMask, probeLen)) {
                continue;
            }
            cnt++;
            ClusteredHashEntryT *curHashEntry = ClusteredGetEntryByBucket(bucketB, probeLen, hashEntrySize);
            if (ClusteredHashCmpPageAndTupleRowSize(runCtx, &pageCtxA, &pageCtxB, curHashEntry) &&
                ClusteredHashMergeSingleEntry(runCtx, curHashEntry, stashBucketA, &pageCtxA, &pageCtxB)) {
                // 扩容合并场景，删除后重置bucket和entry信息
                ClusteredHashNormalBucketFreeSlot(bucketB, (uint16_t)probeLen);
                ClusteredHashEntryClear(curHashEntry);
            }

            SHM_CRASHPOINT_ONE(SHM_CRASH_CH_VERTICAL_SCALE_IN_MERGE_HALF);
        }
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_VERTICAL_SCALE_IN_MERGE_BUCKET);
    ClusteredHashMergeAllStashBucket4MulVersion(runCtx, stashBucketA, stashBucketB, &pageCtxA, &pageCtxB);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_VERTICAL_SCALE_IN_MERGE_STASH_BUCKET);
    ClusteredHashUpdateBloomFilter(runCtx, &pageCtxA);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_VERTICAL_SCALE_IN_END);
    ClusteredHashRsmUndoLogMovePrevRec(runCtx);
    // 必须全部合并成功
    ClusteredHashFreeMergeSegment(runCtx, segmentA, segmentB, segmentB->dirSeg->pageAddr.pageAddr);
}

static bool ClusteredHashIsShareSameSeg(ChLabelRunCtxT *runCtx)
{
    uint32_t halfDirCap = runCtx->labelVarInfo->dirCap >> 1;
    // 同CCEH，此种情况下只有1页，不用缩容
    if (halfDirCap == 0) {
        return false;
    }
    for (uint32_t i = 0; i < halfDirCap; i++) {
        HashDirSegmentT *smallSeg = ClusteredHashGetDirSegmentBySegId(&runCtx->memRunCtx, i);
        if (SECUREC_UNLIKELY(smallSeg == NULL)) {
            return false;
        }
        HashDirSegmentT *largeSeg = ClusteredHashGetDirSegmentBySegId(&runCtx->memRunCtx, i + halfDirCap);
        if (SECUREC_UNLIKELY(largeSeg == NULL)) {
            return false;
        }
        if (smallSeg->pageAddr.blockId != largeSeg->pageAddr.blockId ||
            smallSeg->pageAddr.pageId != largeSeg->pageAddr.pageId) {
            return false;
        }
    }
    return true;
}

static void ClusteredHashArrayDirScaleIn(
    ChLabelPageMemRunCtxT *pageMemRunCtx, uint32_t dirBeginId, uint32_t dirPageCount, RsmUndoRecordT *rsmUndoRec)
{
    DB_POINTER(pageMemRunCtx);
    for (uint32_t i = dirBeginId; i < dirPageCount; i++) {
        PageIdT pageAddr = pageMemRunCtx->pageAddr[i];
        Status ret = ChFreePageToMd(pageMemRunCtx, pageAddr, rsmUndoRec);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            StatusInter tmpRet = DbGetStatusInterErrno(ret);
            SE_ERROR(tmpRet, "clustered hash, free dir page %" PRIu32 ".%" PRIu32 " unsucc",
                pageMemRunCtx->memMgr->base.fileId, i);
            return;
        }
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_SCALEIN_DIR_FREE_DIR_PAGE);
        pageMemRunCtx->pageAddr[i] = SE_INVALID_PAGE_ADDR;
    }
}

void ClusteredHashDirPageScaleIn(ChLabelRunCtxT *runCtx)
{
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    uint32_t dirCap = runCtx->labelVarInfo->dirCap;
    uint32_t hashEntryUsedPageCnt = runCtx->labelVarInfo->segPageCount;
    // 缩容条件1:hash entry占用的page个数最多只有dir数量的一半
    if (hashEntryUsedPageCnt * DOUBLE_SIZE > dirCap) {
        return;
    }
    // 缩容条件2:对称的dir都指向了同一个segment
    if (!ClusteredHashIsShareSameSeg(runCtx)) {
        return;
    }
    uint32_t hashSegNumPerPage = dashMemMeta->hashSegNumPerPage;
    DB_ASSERT(hashSegNumPerPage != 0u);
    uint32_t halfDirCap = dirCap >> 1;
    uint32_t pageNeedNum = (halfDirCap - 1u) / hashSegNumPerPage + 1u;
    uint32_t freePageBeginId = pageNeedNum;
    StatusInter ret = ClusteredHashScaleInDirUndoLog(runCtx, dashMemMeta, freePageBeginId);
    if (ret != STATUS_OK_INTER) {
        return;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_SCALEIN_DIR_BEFORE);
    ClusteredHashArrayDirScaleIn(&runCtx->memRunCtx, freePageBeginId, runCtx->labelVarInfo->dirPageCount,
        ((TrxT *)(runCtx->seRunCtx->trx))->liteTrx.rsmUndoRec);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_SCALEIN_DIR_FREE_DIR_PAGE_FINISHED);
    runCtx->labelVarInfo->dirPageCount = pageNeedNum;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_SCALEIN_DIR_UPDATE_DIR_PAGE_CNT);
    runCtx->labelVarInfo->dirCap = halfDirCap;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_SCALEIN_DIR_UPDATE_DIR_CAP);
    runCtx->labelVarInfo->dirDepth--;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_SCALEIN_DIR_FINISHED);
    ClusteredHashRsmUndoLogMovePrevRec(runCtx);
    DB_ASSERT(((runCtx->labelVarInfo->dirCap + dashMemMeta->hashSegNumPerPage - 1) / dashMemMeta->hashSegNumPerPage) >=
              runCtx->labelVarInfo->dirPageCount);
}

bool ClusteredHashExceedSplitTime(const ChLabelScaleInCfgT *scaleCfg)
{
    DB_POINTER(scaleCfg);
    uint64_t startTime = scaleCfg->startTime;
    double timeThreshold = scaleCfg->splitTime;
    uint64_t endTime = DbRdtsc();
    if (endTime <= startTime) {
        return false;
    }
    double td = (double)DbToMseconds(endTime - startTime);
    return td > timeThreshold;
}

static void ClusteredHashMergeSegPair(ChLabelRunCtxT *runCtx, ChLabelPageMergeCtxT *mergeCtx,
    ChLabelScaleInCfgT *scaleCfg, DashEhMemMetaT *dashMemMeta, uint32_t *dirItemMax)
{
    ChLabelSegmentPageT segmentA;
    ChLabelSegmentPageT segmentB;
    ChScaleInTypeE scaleInType = CHLABEL_SCALE_IN_NORMAL;
    bool findSegPair;
    do {
        findSegPair = ClusteredHashFindSegPair(runCtx, &segmentA, &segmentB, &scaleInType);
        if (!findSegPair) {
            if (dashMemMeta->dirNextItemIdx < *dirItemMax) {
                return;
            }
            ClusteredHashDirPageScaleIn(runCtx);
            *dirItemMax = runCtx->labelVarInfo->dirCap >> 1;
            dashMemMeta->dirNextItemIdx = 0;
            findSegPair = ClusteredHashFindSegPair(runCtx, &segmentA, &segmentB, &scaleInType);
            if (!findSegPair) {
                return;
            }
        }
        runCtx->labelVarInfo->hasScaledIn = true;
        runCtx->chLabel->version++;
        DB_ASSERT(segmentA.dirSeg->pattern != segmentB.dirSeg->pattern);
#ifndef NDEBUG
        ChLabelPageMetaDataT pageDataA = *ClusteredHashGetPageMetaData(segmentA.segPageAddr);
        ChLabelPageMetaDataT pageDataB = *ClusteredHashGetPageMetaData(segmentB.segPageAddr);
        DB_ASSERT(pageDataA.pageRowSize == pageDataB.pageRowSize);
#endif
        mergeCtx->dstSeg = scaleInType == CHLABEL_SCALE_IN_NORMAL ? &segmentA : &segmentB;
        mergeCtx->srcSeg = scaleInType == CHLABEL_SCALE_IN_NORMAL ? &segmentB : &segmentA;
        if (!runCtx->chLabel->labelCfg.isUseRsm) {
            ClusteredHashMergePageChain(runCtx, mergeCtx);
        } else {
            ClusteredHashMergePageChain4Rsm(runCtx, mergeCtx);
        }
        runCtx->chLabel->dashMeta.scaleInPageCnt++;  // 实际缩容的页的个数
    } while (findSegPair);
}

static void ClusteredHashScaleInProc(SeInstanceT *seInstance, ChLabelRunCtxT *runCtx, ChLabelScaleInCfgT *scaleCfg)
{
    DashEhMemMetaT *dashMemMeta = &runCtx->chLabel->memMgr.dashMemMeta;
    uint32_t dirItemMax = runCtx->labelVarInfo->dirCap >> 1;
    if (dashMemMeta->dirNextItemIdx >= dirItemMax) {
        dashMemMeta->dirNextItemIdx = 0;
    }
    // 2份操作页内存，缩容使用内存，直接从操作系统获取
    uint8_t *dstPage = malloc(seInstance->seConfig.pageSize * 2);
    if (dstPage == NULL) {
        DB_LOG_WARN(GMERR_MEMORY_OPERATE_FAILED, "opPage alloc unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return;
    }
    uint8_t *srcPage = dstPage + seInstance->seConfig.pageSize;
    runCtx->chLabel->memMgr.dashMemMeta.scaleInCnt++;  // 缩容函数进入的次数
    ChLabelPageMergeCtxT mergeCtx = {
        .dstSeg = NULL,
        .srcSeg = NULL,
        .dstPageBackup = dstPage,
        .srcPageBackup = srcPage,
        .pageSize = seInstance->seConfig.pageSize,
        .isGc = false,
    };
    ClusteredHashMergeSegPair(runCtx, &mergeCtx, scaleCfg, dashMemMeta, &dirItemMax);
    free(dstPage);
    if (dashMemMeta->dirNextItemIdx >= dirItemMax) {
        dashMemMeta->dirNextItemIdx = 0;
        ClusteredHashDirPageScaleIn(runCtx);
    }
}

static void ClusteredHashUnLock(ChLabelRunCtxT *runCtx, bool isHcIndexLockByUpper, bool isLatchWriteLock)
{
    if (isHcIndexLockByUpper) {
        ClusteredHashWUnLockHcIndexByUpper(runCtx);
    }
    if (!isLatchWriteLock) {
        ClusteredHashTableUnLock(runCtx, true);
    }
}

static StatusInter ClusteredHashLock(ChLabelRunCtxT *runCtx, bool isHcIndexLockByUpper, bool isLatchWriteLock)
{
    StatusInter ret = STATUS_OK_INTER;
    if (!isLatchWriteLock) {
        ret = ClusteredHashTableLock(runCtx, true);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            // 加锁失败说明在重新上锁的间隙表已经被删除, 此时不应再对hashcluster索引上锁，应该直接返回
            SE_ERROR(ret,
                "label have been dropped, labelRWLatchVersionId: %" PRIu32 ", labelLatchVersionId: %" PRIu32 ".",
                runCtx->openCfg.labelRWLatch->versionId, runCtx->openCfg.labelLatchVersionId);
            return ret;
        }
    }
    if (isHcIndexLockByUpper) {
        // 上层持有了hashcluster索引的锁，需要重新加回去
        ClusteredHashWLockHcIndexByUpper(runCtx);
    }
    return ret;
}

StatusInter ClusteredHashTryScaleIn(ChLabelRunCtxT *runCtx, ChLabelScaleInCfgT *scaleCfg)
{
    if (DbCommonGetWarmReboot()) {
        return STATUS_OK_INTER;
    }
    DB_POINTER2(runCtx, scaleCfg);
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance((uint16_t)runCtx->chLabel->memMgr.base.seInstanceId);
    if (seInstance == NULL) {
        SE_ERROR(INVALID_PARAMETER_VALUE_INTER, "storage instance %" PRIu16 " novalid",
            (uint16_t)runCtx->chLabel->memMgr.base.seInstanceId);
        return INVALID_PARAMETER_VALUE_INTER;
    }
    bool isHcIndexLockByUpper = runCtx->isHoldHcIndexLockByUpper;
    bool isLatchWriteLock = runCtx->openCfg.labelRWLatch->rwlatch.latchMode == (uint32_t)DB_LATCH_SERVER_X;

    ClusteredHashUnLock(runCtx, isHcIndexLockByUpper, isLatchWriteLock);

    uint32_t retryCount = CH_CURSOR_LOCK_OVER_TIME_RETRY_COUNT;
    StatusInter ret;
    while (retryCount > 0) {
        if (!isLatchWriteLock) {
            ret = ClusteredHashTableLock(runCtx, false);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                // 加锁失败说明在重新上锁的间隙表已经被删除, 此时不应再对hashcluster索引上锁，应该直接返回
                return ret;
            }
        }
        // 缩容与全表扫描互斥
        if (ChScanCursorRefTimedMutex(runCtx, 1)) {
            ClusteredHashScaleInProc(seInstance, runCtx, scaleCfg);
            ret = ClusteredHashFindSegAndMoveData(runCtx);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "clustered hash,Unable to move upgrade data, labelId: %" PRIu32 ", indexId: %" PRIu32,
                    runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            }
            ChScanCursorRefDec(runCtx, true);
            retryCount = 0;
        } else {
            retryCount--;
        }
        if (!isLatchWriteLock) {
            ClusteredHashTableUnLock(runCtx, false);
        }
    }

    return ClusteredHashLock(runCtx, isHcIndexLockByUpper, isLatchWriteLock);
}

Status ClusteredHashScaleIn(ChLabelRunHdlT chRunHdl, ChLabelScaleInCfgT *scaleCfg)
{
    return DbGetExternalErrno(ClusteredHashTryScaleIn(chRunHdl, scaleCfg));
}

static StatusInter ClusteredHashMoveAllOverflowPageFlag(
    ChLabelRunCtxT *runCtx, HashDirSegmentT *dir, HashDirSegmentT *otherSeg)
{
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    uint32_t fileId = runCtx->chLabel->memMgr.base.fileId;
    uint8_t *segPageAddr = NULL;
    StatusInter ret = ClusteredHashGetSegPage(runCtx->memRunCtx.mdMgr, dir->pageAddr.pageAddr, fileId, &segPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    uint8_t *otherSegPageAddr = NULL;
    ret = ClusteredHashGetSegPage(runCtx->memRunCtx.mdMgr, otherSeg->pageAddr.pageAddr, fileId, &otherSegPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaDataWithAddr(runCtx, segPageAddr);
    ChLabelPageMetaDataT *otherPageData = ClusteredHashGetPageMetaDataWithAddr(runCtx, otherSegPageAddr);
    uint8_t *bucketHdr = segPageAddr + pageData->bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    uint8_t *otherBucketHdr = otherSegPageAddr + otherPageData->bucketPos + CHLABEL_PAGE_METADATA_OFFSET;
    uint32_t maxBucketNum = pageData->hashNormalBucketNumPerPage;
    uint32_t bucketSize = dashMeta->bucketSize;
    for (uint32_t i = 0; i < maxBucketNum; i++) {
        ChBucketMetaT *bucket = ClusteredHashGetBucketByIdx(bucketHdr, i, bucketSize);
        ChBucketMetaT *otherBucket = ClusteredHashGetBucketByIdx(otherBucketHdr, i, bucketSize);
        if (ChLabelIsTargetBucketOverFlow(bucket)) {
            ChLabelSetTargetBucketOverFlow(otherBucket);
            ChLabelClearTargetBucketOverFlow(bucket);
        }
    }
    return STATUS_OK_INTER;
}

static uint32_t PatternBitIter(uint32_t pattern, uint32_t highDepth, uint32_t lowDepth)
{
    uint32_t depth = highDepth;
    uint32_t tmpPattern = pattern;
    while (depth > lowDepth) {
        uint32_t depBit = 1u << (depth - 1);
        if ((tmpPattern & depBit) == 0) {
            tmpPattern = tmpPattern | depBit;
            return tmpPattern;
        } else {
            tmpPattern = tmpPattern & ~depBit;
            depth--;
        }
    }
    return DB_INVALID_UINT32;
}

void ClusteredHashFixSegAfterRecycle(ChLabelRunCtxT *runCtx, HashDirSegmentT *seg, HashDirSegmentT *otherSeg)
{
    uint32_t patternSpan = 1u << (seg->segDepth - 1);
    uint32_t minSegId = seg->pattern % patternSpan;
    HashDirSegmentT *minSeg = ClusteredHashGetDirSegmentBySegId(&runCtx->memRunCtx, minSegId);
    if (minSeg == NULL) {
        DB_LOG_WARN(GMERR_NULL_VALUE_NOT_ALLOWED, "ClusteredHashFixSegAfterRecycle: minSeg is null.");
        return;
    }
    minSeg->pageAddr.pageAddr = otherSeg->pageAddr.pageAddr;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_RECYCLE_PAGE_UPDATE_PAGE_ADDR);
    minSeg->segDepth--;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_RECYCLE_PAGE_UPDATE_SEG_DEPTH);

    uint32_t segId = minSegId;
    uint32_t depth = runCtx->labelVarInfo->dirDepth;
    while (segId != DB_INVALID_UINT32) {
        segId = PatternBitIter(segId, depth, minSeg->segDepth);
        if (segId == DB_INVALID_UINT32) {
            break;
        }
        HashDirSegmentT *tmpSeg = ClusteredHashGetDirSegmentBySegId(&runCtx->memRunCtx, segId);
        DB_ASSERT(tmpSeg != NULL);
        if (SECUREC_UNLIKELY(tmpSeg == NULL)) {
            DB_LOG_ERROR(GMERR_NULL_VALUE_NOT_ALLOWED, "ClusteredHashFixSegAfterRecycle: minSeg is null.");
            return;
        }
        // 两个seg可以合并，depth一定相等
        DB_ASSERT(tmpSeg->segDepth == minSeg->segDepth + 1 || DbCommonGetWarmReboot());
        *tmpSeg = *minSeg;
        SHM_CRASHPOINT_ONE(SHM_CRASH_CH_RECYCLE_PAGE_UPDATE_OTHER_SEG);
    }
}

static bool IsSegCanRecycle(ChLabelRunCtxT *runCtx, HashDirSegmentT *seg, HashDirSegmentT **otherSegOut)
{
    uint32_t depBit = 1u << (seg->segDepth - 1);
    uint32_t otherSegId = seg->pattern >= depBit ? (seg->pattern & ~depBit) : (seg->pattern | depBit);
    HashDirSegmentT *otherSeg = ClusteredHashGetDirSegmentBySegId(&runCtx->memRunCtx, otherSegId);
    DB_ASSERT(otherSeg != NULL);
    if (otherSeg == NULL) {
        return false;
    }
    *otherSegOut = otherSeg;
    uint32_t minSegId = seg->pattern % depBit;
    HashDirSegmentT *minSeg = ClusteredHashGetDirSegmentBySegId(&runCtx->memRunCtx, minSegId);
    if (minSeg == NULL) {
        return false;
    }
    return otherSeg->segDepth == seg->segDepth;
}

static bool ClusteredHashFreePageAndResetSeg(ChLabelRunCtxT *runCtx, HashDirSegmentT *dir)
{
    HashDirSegmentT *otherSeg = NULL;
    if (dir->segDepth == 0 || !IsSegCanRecycle(runCtx, dir, &otherSeg)) {
        return false;
    }
    StatusInter ret;
    DashEhLabelMetaT *dashMeta = &runCtx->chLabel->dashMeta;
    if (dashMeta->overflowPageTupleUsed > 0) {
        ret = ClusteredHashMoveAllOverflowPageFlag(runCtx, dir, otherSeg);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return false;
        }
    }
#ifdef SYS32BITS
    ret = ClusteredHashGetAllDirPages(runCtx->chLabel);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, get all dir pages unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return false;
    }
#endif
    // 记录回收空页的undo
    RsmUndoChRecyclePageRec *rsmChNormalRecyclePageRec = NULL;
    ret = ClusteredHashRecyclePageUndoLog(runCtx, otherSeg, dir->pattern, &rsmChNormalRecyclePageRec);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_RECYCLE_PAGE_BEFORE);
    ret = ClusteredHashFreeOneSegPage(
        &runCtx->memRunCtx, ((TrxT *)(runCtx->seRunCtx->trx))->liteTrx.rsmUndoRec, dir->pageAddr.pageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return false;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_RECYCLE_PAGE_FREE_PAGE);
    if (runCtx->chLabel->labelCfg.isUseRsm) {
        rsmChNormalRecyclePageRec->recycleType = CLUSTERED_HASH_RECYCLE_UPDATE_DIR;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_RECYCLE_PAGE_UPDATE_TYPE);
    ClusteredHashFixSegAfterRecycle(runCtx, dir, otherSeg);
    runCtx->labelVarInfo->segPageCount--;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_RECYCLE_PAGE_UPDATE_SEG_CNT);
    if (runCtx->chLabel->labelCfg.isUseRsm) {
        rsmChNormalRecyclePageRec->recycleType = CLUSTERED_HASH_RECYCLE_FINISHED;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_RECYCLE_PAGE_FINISHED);
    ClusteredHashResetUpgradeVersion(runCtx->chLabel, runCtx->labelVarInfo);
    ClusteredHashRsmUndoLogMovePrevRec(runCtx);
    return true;
}

static void ClusteredHashFreeFirstPageInChain(ChLabelRunCtxT *runCtx, HashDirSegmentT dirSeg,
    HashDirSegmentT *curDirSeg, ChLabelPageHeaderT *targetPageHead, ChLabelPageMetaDataT pageData)
{
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_BEFORE);
    StatusInter ret;
    // 探测并修改dirPage指向
    uint8_t *dirPageAddr = NULL;
    uint32_t dirPageCnt = runCtx->labelVarInfo->dirPageCount;
    uint32_t hashSegNumPerPage = runCtx->chLabel->memMgr.dashMemMeta.hashSegNumPerPage;
    for (uint32_t i = 0; i < dirPageCnt; i++) {
        ret = ClusteredHashGetDirPage(&runCtx->memRunCtx, i, &dirPageAddr);
        DB_ASSERT(ret == STATUS_OK_INTER);
        for (uint32_t segSlotId = 0; segSlotId < hashSegNumPerPage; segSlotId++) {
            HashDirSegmentT *seg = (HashDirSegmentT *)(void *)(dirPageAddr + segSlotId * sizeof(HashDirSegmentT));
            if (seg->pageAddr.pageAddr.deviceId == dirSeg.pageAddr.pageAddr.deviceId &&
                seg->pageAddr.pageAddr.blockId == dirSeg.pageAddr.pageAddr.blockId) {
                SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_CHANGE_ALL_SEG);
                seg->pageAddr.pageAddr = pageData.nextPageAddr;
            }
        }
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_CHANGE_CUR_SEG);
    curDirSeg->pageAddr.pageAddr = pageData.nextPageAddr;
    runCtx->pageCtx.segAddr.pageAddr = pageData.nextPageAddr;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_FREE_PAGE_BEFORE);
    ret = ClusteredHashFreeOneSegPage(
        &runCtx->memRunCtx, ((TrxT *)(runCtx->seRunCtx->trx))->liteTrx.rsmUndoRec, targetPageHead->baseHead.addr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, free page unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_FREE_PAGE);
    runCtx->labelVarInfo->segPageCount--;
    ClusteredHashResetUpgradeVersion(runCtx->chLabel, runCtx->labelVarInfo);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_RECYCLE_FIRST_PAGE_FINISHED);
}

static void ClusteredHashFreeLastPageInChain(ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, uint8_t *segPageAddr,
    RsmUndoChRecycleMulPageRec **rsmChNormalRecycleMulPageRec)
{
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_RECYCLE_LAST_PAGE_BEFORE);
    ChLabelPageMetaDataT *pageData = ClusteredHashGetPageMetaData(segPageAddr);
    bool isAddrValid = true;
    while (pageData != NULL && isAddrValid &&
           (pageData->nextPageAddr.deviceId != pageCtx->segAddr.pageAddr.deviceId ||
               pageData->nextPageAddr.blockId != pageCtx->segAddr.pageAddr.blockId)) {
        isAddrValid = ClusteredHashIsPageAddrValid(pageData->nextPageAddr);
        pageData = ClusteredHashGetPageMetaDataByPageId(&runCtx->memRunCtx, pageData->nextPageAddr);
    }
    if (pageData == NULL || !isAddrValid) {
        // isAddrValid不可能为false，必定可以在链上找到目标数据页
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "unable to get segment page for free empty page, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return;
    }
    if (runCtx->chLabel->labelCfg.isUseRsm) {
        PageIdT prevPageAddr =
            ((ChLabelPageHeaderT *)(void *)((uint8_t *)pageData - CHLABEL_PAGE_HEAD_OFFSET))->baseHead.addr;
        (*rsmChNormalRecycleMulPageRec)->prevDeviceId = prevPageAddr.deviceId;
        (*rsmChNormalRecycleMulPageRec)->prevBlockId = prevPageAddr.blockId;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_RECYCLE_LAST_PAGE_GET_PRE_ADDR);
    DB_ASSERT(pageData->nextPageAddr.deviceId == pageCtx->segAddr.pageAddr.deviceId &&
              pageData->nextPageAddr.blockId == pageCtx->segAddr.pageAddr.blockId);
    pageData->nextPageAddr = SE_INVALID_PAGE_ADDR;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_RECYCLE_LAST_PAGE_CHANGE_SEG);
    StatusInter ret = ClusteredHashFreeOneSegPage(
        &runCtx->memRunCtx, ((TrxT *)(runCtx->seRunCtx->trx))->liteTrx.rsmUndoRec, pageCtx->segAddr.pageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, free last page unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_RECYCLE_LAST_PAGE_FREE_PAGE);
    runCtx->labelVarInfo->segPageCount--;
    ClusteredHashResetUpgradeVersion(runCtx->chLabel, runCtx->labelVarInfo);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_RECYCLE_LAST_PAGE_FINISHED);
}

static void ClusteredHashFreeNormalPageInChain(ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, uint8_t *segPageAddr,
    RsmUndoChRecycleMulPageRec **rsmChNormalRecycleMulPageRec)
{
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_RECYCLE_MIDDLE_PAGE_BEFORE);
    ChLabelPageMetaDataT targetPageData = pageCtx->pageMetaData;
    ChLabelPageMetaDataT *pageMetaData = ClusteredHashGetPageMetaData(segPageAddr);
    bool isAddrValid = true;
    while (pageMetaData != NULL && isAddrValid &&
           (pageMetaData->nextPageAddr.deviceId != pageCtx->segAddr.pageAddr.deviceId ||
               pageMetaData->nextPageAddr.blockId != pageCtx->segAddr.pageAddr.blockId)) {
        isAddrValid = ClusteredHashIsPageAddrValid(pageMetaData->nextPageAddr);
        pageMetaData = ClusteredHashGetPageMetaDataByPageId(&runCtx->memRunCtx, pageMetaData->nextPageAddr);
    }
    if (pageMetaData == NULL || !isAddrValid) {
        // isAddrValid不可能为false，必定可以在链上找到目标数据页
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "unable to get segment page for free empty page, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return;
    }
    if (runCtx->chLabel->labelCfg.isUseRsm) {
        PageIdT prevPageAddr =
            ((ChLabelPageHeaderT *)(void *)((uint8_t *)pageMetaData - CHLABEL_PAGE_HEAD_OFFSET))->baseHead.addr;
        (*rsmChNormalRecycleMulPageRec)->prevDeviceId = prevPageAddr.deviceId;
        (*rsmChNormalRecycleMulPageRec)->prevBlockId = prevPageAddr.blockId;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_RECYCLE_MIDDLE_PAGE_GET_PRE_ADDR);
    DB_ASSERT(pageMetaData->nextPageAddr.deviceId == pageCtx->segAddr.pageAddr.deviceId &&
              pageMetaData->nextPageAddr.blockId == pageCtx->segAddr.pageAddr.blockId);
    pageMetaData->nextPageAddr = targetPageData.nextPageAddr;
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_RECYCLE_MIDDLE_PAGE_CHANGE_SEG);
    StatusInter ret = ClusteredHashFreeOneSegPage(
        &runCtx->memRunCtx, ((TrxT *)(runCtx->seRunCtx->trx))->liteTrx.rsmUndoRec, pageCtx->segAddr.pageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, free normal page unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_RECYCLE_MIDDLE_PAGE_FREE_PAGE);
    runCtx->labelVarInfo->segPageCount--;
    ClusteredHashResetUpgradeVersion(runCtx->chLabel, runCtx->labelVarInfo);
    SHM_CRASHPOINT_ONE(SHM_CRASH_CH_MUL_VERSION_RECYCLE_MIDDLE_PAGE_FINISHED);
}

typedef struct ChLabelFreePageCtx {
    ChLabelPageCtxT *pageCtx;
    ChLabelPageHeaderT *targetPageHead;
    ChLabelPageMetaDataT targetPageData;
    HashDirSegmentT dirSeg;
    HashDirSegmentT *curDirSeg;
    uint8_t *segPageAddr;
} ChLabelFreePageCtxT;

static void ClusteredHashFreeEmptyPage4MulVersion(ChLabelRunCtxT *runCtx, ChLabelFreePageCtxT *freePageCtx)
{
    StatusInter ret;
    RsmUndoChRecycleMulPageRec *rsmChNormalRecycleMulPageRec = NULL;
    if (freePageCtx->pageCtx->segAddr.virtAddr == freePageCtx->segPageAddr &&
        ClusteredHashIsInvalidPageId(freePageCtx->targetPageData.nextPageAddr)) {
        // 只有一个page，空了
        if (ClusteredHashFreePageAndResetSeg(runCtx, freePageCtx->curDirSeg)) {
            ClusteredHashDirPageScaleIn(runCtx);
        }
    } else if (freePageCtx->pageCtx->segAddr.virtAddr == freePageCtx->segPageAddr) {
#ifdef SYS32BITS
        ret = ClusteredHashGetAllDirPages(runCtx->chLabel);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, get all dir pages unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return;
        }
#endif
        // 第一个page空了
        ret = ClusteredHashRecycleMulPageUndoLog(runCtx, freePageCtx->targetPageData, &freePageCtx->dirSeg,
            CLUSTERED_HASH_RECYCLE_MUL_PAGE_FIRST, &rsmChNormalRecycleMulPageRec);
        if (ret != STATUS_OK_INTER) {
            return;
        }
        ClusteredHashFreeFirstPageInChain(runCtx, freePageCtx->dirSeg, freePageCtx->curDirSeg,
            freePageCtx->targetPageHead, freePageCtx->targetPageData);
        ClusteredHashRsmUndoLogMovePrevRec(runCtx);
    } else if (ClusteredHashIsInvalidPageId(freePageCtx->targetPageData.nextPageAddr)) {
        // 最后一个page空了
        ret = ClusteredHashRecycleMulPageUndoLog(runCtx, freePageCtx->targetPageData, &freePageCtx->dirSeg,
            CLUSTERED_HASH_RECYCLE_MUL_PAGE_LAST, &rsmChNormalRecycleMulPageRec);
        if (ret != STATUS_OK_INTER) {
            return;
        }
        ClusteredHashFreeLastPageInChain(
            runCtx, freePageCtx->pageCtx, freePageCtx->segPageAddr, &rsmChNormalRecycleMulPageRec);
        ClusteredHashRsmUndoLogMovePrevRec(runCtx);
    } else {
        // 中间page空了
        ret = ClusteredHashRecycleMulPageUndoLog(runCtx, freePageCtx->targetPageData, &freePageCtx->dirSeg,
            CLUSTERED_HASH_RECYCLE_MUL_PAGE_MIDDLE, &rsmChNormalRecycleMulPageRec);
        if (ret != STATUS_OK_INTER) {
            return;
        }
        ClusteredHashFreeNormalPageInChain(
            runCtx, freePageCtx->pageCtx, freePageCtx->segPageAddr, &rsmChNormalRecycleMulPageRec);
        ClusteredHashRsmUndoLogMovePrevRec(runCtx);
    }
}

void ClusteredHashFreeEmptyPage(ChLabelRunCtxT *runCtx, ChLabelPageCtxT *pageCtx, HashDirSegmentT dirSeg)
{
    HashDirSegmentT *curDirSeg = ClusteredHashGetDirSegmentBySegId(&runCtx->memRunCtx, dirSeg.pattern);
    if (SECUREC_UNLIKELY(curDirSeg == NULL)) {
        return;
    }
    // 获取segment页
    uint8_t *segPageAddr = NULL;
    StatusInter ret = ClusteredHashGetSegPage(
        runCtx->memRunCtx.mdMgr, curDirSeg->pageAddr.pageAddr, runCtx->chLabel->memMgr.base.fileId, &segPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return;
    }
    // 返回OK，segPageAddr必定不为NULL
    DB_ASSERT(segPageAddr != NULL);
    ChLabelPageHeaderT *targetPageHead = ClusteredHashGetPageHead(pageCtx->segAddr.virtAddr);
    ChLabelPageMetaDataT targetPageData = pageCtx->pageMetaData;
    if (runCtx->labelVarInfo->segPageCount <= 1 || targetPageHead->freeSlotCount != targetPageData.tupleCntPerPage) {
        // 存在并发缩容问题可能不为空了，不再删除页
        return;
    }
    runCtx->chLabel->version++;
    if (SECUREC_LIKELY(runCtx->labelVarInfo->upgradeVersion == 0)) {
        if (ClusteredHashFreePageAndResetSeg(runCtx, curDirSeg)) {
            ClusteredHashDirPageScaleIn(runCtx);
        }
        return;
    }
    ChLabelFreePageCtxT freePageCtx = {
        .pageCtx = pageCtx,
        .targetPageHead = targetPageHead,
        .targetPageData = targetPageData,
        .dirSeg = dirSeg,
        .curDirSeg = curDirSeg,
        .segPageAddr = segPageAddr,
    };
    ClusteredHashFreeEmptyPage4MulVersion(runCtx, &freePageCtx);
}

// 过滤掉不满足缩容的加锁情况
bool ClusteredHashCantArrangeEmptyPage(ChLabelRunCtxT *runCtx)
{
    // 1.未加表锁；
    // 2.客户端加写锁；
    // 3.hashcluster为读锁；
    // 4.客户端加hashcluster加写锁；
    // 5.上层应该加锁但没加hashcluster锁；
    return (runCtx->openCfg.labelRWLatch->rwlatch.latchMode == (uint32_t)DB_LATCH_IDLE ||
            runCtx->openCfg.labelRWLatch->rwlatch.latchMode == (uint32_t)DB_LATCH_CLIENT_X ||
            runCtx->openCfg.labelRWLatch->hcLatch.latchMode == (uint32_t)DB_LATCH_S ||
            runCtx->openCfg.labelRWLatch->hcLatch.latchMode == (uint32_t)DB_LATCH_CLIENT_X ||
            (runCtx->isHoldHcIndexLockByUpper &&
                runCtx->openCfg.labelRWLatch->hcLatch.latchMode == (uint32_t)DB_LATCH_IDLE));
}

void ClusteredHashArrangeFreeEmptyPage(ChLabelRunHdlT chHandle)
{
    DB_POINTER(chHandle);
    ChLabelRunCtxT *runCtx = (ChLabelRunCtxT *)chHandle;
    if (ClusteredHashCantArrangeEmptyPage(runCtx)) {
        return;
    }
    StatusInter ret;
    bool writeLock = DbRWLatchIsWriteLock(&runCtx->openCfg.labelRWLatch->rwlatch);
    bool hcGlobalLock = runCtx->isHoldHcIndexLockByUpper;
    if (!writeLock) {
        DB_ASSERT(hcGlobalLock ? (runCtx->openCfg.labelRWLatch->hcLatch.latchMode == (uint32_t)DB_LATCH_SERVER_X) :
                                 (runCtx->openCfg.labelRWLatch->hcLatch.latchMode == (uint32_t)DB_LATCH_IDLE));
        if (hcGlobalLock) {
            ClusteredHashWUnLockHcIndexByUpper(runCtx);
        }
        ClusteredHashTableUnLock(runCtx, true);
        ret = ClusteredHashTableLock(runCtx, false);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            // 加锁失败说明在重新上锁的间隙表已经被删除, 此时不应再对hashcluster索引上锁，应该直接返回
            return;
        }
    }
    // 删页与缩容、全表扫描互斥
    if (ChScanCursorRefTimedMutex(runCtx, 1)) {
        ret = ClusteredHashFindSegAndMoveData(runCtx);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "clustered hash,Unable to move free empty page, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        }
        ChScanCursorRefDec(runCtx, true);
    }
    if (!writeLock) {
        ClusteredHashTableUnLock(runCtx, false);
        ret = ClusteredHashTableLock(runCtx, true);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            // 加锁失败说明在重新上锁的间隙表已经被删除, 此时不应再对hashcluster索引上锁，应该直接返回
            return;
        }
        if (hcGlobalLock) {
            ClusteredHashWLockHcIndexByUpper(runCtx);
        }
    }
}
