/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: se_clustered_hash_hc_index.c
 * Description: 基于聚簇哈希容器的聚簇索引的相关实现
 * Author: <PERSON><PERSON><PERSON><PERSON>jian
 * Create: 2022/8/15
 */
#include "se_clustered_hash_hc_index.h"
#include "se_hash_cluster_index.h"
#include "se_clustered_hash_access_dm.h"
#include "adpt_atomic.h"
#include "se_log.h"
#ifndef NDEBUG
#include "se_clustered_hash_label_ddl.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif

typedef struct TagChDeleteHcPtrT {
    ChIndexHcPtrT *deleteHcPtr;
    ChIndexHcPtrT *prevHcPtr;
    ChIndexHcPtrT *nextHcPtr;
} ChDeleteHcPtrT;

typedef struct TagChHcIndexFetchUserData {
    ChLabelRunHdlT chHandle;
    ChLogicRowId currAddr;
    uint32_t hcIndex;
    HpHcScanCtxT *scanCtx;
    HpHcFetchDataT *fetchedData;
} ChHcIndexFetchUserDataT;

// 入参必定是逻辑rowId，上层不应感知物理rowId
static StatusInter ChLabelHcIndexFetchRowHcPtr(
    ChLabelRunCtxT *runCtx, ChLogicRowId logicRowId, uint32_t hcIndex, ChIndexHcPtrT **hcPtr)
{
    DB_POINTER2(runCtx, hcPtr);
    StatusInter ret = ClusteredHashInitPageCtxByRowId(runCtx, &runCtx->pageCtx, logicRowId);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret,
            "get segment page unsucc when fetch row header. (%" PRIu32 ", %" PRIu32 "), labelId: %" PRIu32
            ", indexId: %" PRIu32,
            logicRowId.hashCode, logicRowId.tupleId, runCtx->chLabel->labelCfg.labelId,
            runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    ChPhyRowId phyRowId = ChLabelGetPhysicalAddrByLogicalAddr(runCtx, logicRowId);
    uint32_t tupleId = ClusteredHashGetInnerTupleId(runCtx, phyRowId.tupleId);  // 必须用物理的
    // 获取聚簇索引前后向指针
    uint8_t *tupleHead = ClusteredHashTupleHdr(&runCtx->pageCtx, runCtx->pageCtx.needVisitOverFlowPage);
    *hcPtr = (ChIndexHcPtrT *)(void *)ClusteredHashGetTupleBySlot(
        tupleHead, runCtx->pageCtx.pageMetaData.tupleSize, tupleId);
    // 由上层保证，hcIndex不可能大于最大hashcluster数量
    DB_ASSERT(hcIndex < runCtx->chLabel->labelCfg.slotExtendSize / sizeof(ChIndexHcPtrT));
    (*hcPtr) += hcIndex;
    return STATUS_OK_INTER;
}

inline static void ChLabelHcIndexListUnlink(
    ChLogicRowId prevTupleAddr, ChLogicRowId nextTupleAddr, HpHcDeleteInfoT *deleteInfo)
{
    DB_POINTER(deleteInfo);
    uint8_t rowIdValid = 0x0;
    if (TransformChLogicRowId(prevTupleAddr) != HEAP_INVALID_ADDR) {
        rowIdValid |= 0x2;
    }

    if (TransformChLogicRowId(nextTupleAddr) != HEAP_INVALID_ADDR) {
        rowIdValid |= 0x1;
    }

    if (rowIdValid == 0x0) {
        deleteInfo->isListEmpty = true;
    } else if (rowIdValid == 0x1) {
        deleteInfo->isListHeadDeleted = true;
        deleteInfo->newHeadTupleAddr = TransformChLogicRowId(nextTupleAddr);
    }
}

StatusInter ChLabelHcIndexListFetchTuple(
    ChLabelRunCtxT *runCtx, ChLogicRowId logicRowId, const ChLabelReadRowProc func, void *userData)
{
    DB_POINTER3(runCtx, func, userData);
    ChIndexHcPtrT *rowHcPtr = NULL;
    runCtx->isPageReadOnly = true;
    StatusInter ret = ChLabelHcIndexFetchRowHcPtr(runCtx, logicRowId, HEAP_INVALID_ADDR, &rowHcPtr);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    ret = func((uint8_t *)rowHcPtr, ClusteredHashGetFixRowSize(runCtx), userData);
    return ret;
}

void ChLabelHcIndexListInsertWithPrefetch(
    ChLabelRunCtxT *runCtx, HcInsertPrefetchCtxT *prefetchCtx, uint32_t hcIndex, HpTupleAddr oldHeadAddr)
{
    DB_POINTER(runCtx);
    ChLogicRowId oldHeadRowId = TransformTupleAddr2LogicRowId(oldHeadAddr);
    ChIndexHcPtrT *headHcPtr = NULL;
    StatusInter ret = ChLabelHcIndexFetchRowHcPtr(runCtx, oldHeadRowId, hcIndex, &headHcPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        // 这个分支不可能走到，因为前面delete流程或者insert流程已经prefetch过
        SE_ERROR(ret, "get head HcIndex rowPtr unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        DB_ASSERT(false);
        return;
    }
    ChIndexHcPtrT *insertHcPtr = (ChIndexHcPtrT *)prefetchCtx->insertHcPtr;
    DB_ASSERT(headHcPtr != insertHcPtr);
    headHcPtr->prevPtr = TransformTupleAddr2LogicRowId(prefetchCtx->insertTupleAddr);
    *(HpTupleAddr *)(void *)&insertHcPtr->prevPtr = HEAP_INVALID_ADDR;
    insertHcPtr->nextPtr = oldHeadRowId;
}

StatusInter ChLabelHcIndexListInsert(
    ChLabelRunCtxT *runCtx, const ChLogicRowId insertRowId, const ChLogicRowId headRowId, uint32_t hcIndex)
{
    DB_POINTER(runCtx);
    ChIndexHcPtrT *headHcPtr = NULL;
    StatusInter ret = ChLabelHcIndexFetchRowHcPtr(runCtx, headRowId, hcIndex, &headHcPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "get head HcIndex rowPtr unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    ChIndexHcPtrT *insertHcPtr = NULL;
    ret = ChLabelHcIndexFetchRowHcPtr(runCtx, insertRowId, hcIndex, &insertHcPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "get insert HcIndex rowPtr unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    headHcPtr->prevPtr = insertRowId;
    *(HpTupleAddr *)(void *)&insertHcPtr->prevPtr = HEAP_INVALID_ADDR;
    insertHcPtr->nextPtr = headRowId;
    return STATUS_OK_INTER;
}

StatusInter ChLabelHcIndexListPrefetch4Insert(
    ChLabelRunCtxT *runCtx, HcInsertPrefetchCtxT *prefetchCtx, uint32_t hcIndex, HpTupleAddr oldHeadAddr)
{
    DB_POINTER2(runCtx, prefetchCtx);
#ifndef NDEBUG
    uint8_t *tmpLockPage = runCtx->lockPage;
    runCtx->lockPage = NULL;
#endif
    ChLabelPageCtxT lastPageCtx = runCtx->pageCtx;
    ChIndexHcPtrT *headHcPtr = NULL, *insertHcPtr = NULL;
    if (oldHeadAddr != HEAP_INVALID_ADDR) {
        ChLogicRowId headRowId = TransformTupleAddr2LogicRowId(oldHeadAddr);
        StatusInter ret = ChLabelHcIndexFetchRowHcPtr(runCtx, headRowId, hcIndex, &headHcPtr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "get head HcIndex head rowPtr unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            runCtx->pageCtx = lastPageCtx;
#ifndef NDEBUG
            runCtx->lockPage = tmpLockPage;
#endif
            prefetchCtx->isPrefetch = false;
            return ret;
        }
        // 这里不能缓存headHcPtr，因为更新场景可能正好删掉了
    }
    if (prefetchCtx->insertHcPtr == NULL) {
        ChLogicRowId insertRowId = TransformTupleAddr2LogicRowId(prefetchCtx->insertTupleAddr);
        StatusInter ret = ChLabelHcIndexFetchRowHcPtr(runCtx, insertRowId, hcIndex, &insertHcPtr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "get head HcIndex insert rowPtr unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            runCtx->pageCtx = lastPageCtx;
#ifndef NDEBUG
            runCtx->lockPage = tmpLockPage;
#endif
            prefetchCtx->isPrefetch = false;
            return ret;
        }
        prefetchCtx->insertHcPtr = insertHcPtr;
    }

    prefetchCtx->isPrefetch = true;
    prefetchCtx->filterResult = (uint8_t)INDEX_FILTER_SATISFIED;
    runCtx->pageCtx = lastPageCtx;
#ifndef NDEBUG
    runCtx->lockPage = tmpLockPage;
#endif
    return STATUS_OK_INTER;
}

static StatusInter ChLabelHcIndexListNextTupleSamePattern(ChLabelRunCtxT *runCtx, ChLogicRowId tupleAddr,
    uint32_t hcIndex, ChLogicRowId prevTupleAddr, ChLogicRowId nextTupleAddr)
{
    ChIndexHcPtrT *nextHcPtr = NULL;
    ChIndexHcPtrT *prevHcPtr = NULL;
    StatusInter ret = ChLabelHcIndexFetchRowHcPtr(runCtx, nextTupleAddr, hcIndex, &nextHcPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "Unable to get next HcIndex rowPtr when delete, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    DB_ASSERT(TransformChLogicRowId(nextHcPtr->prevPtr) == TransformChLogicRowId(tupleAddr));
    nextHcPtr->prevPtr = prevTupleAddr;
    if (TransformChLogicRowId(prevTupleAddr) != HEAP_INVALID_ADDR) {
        ret = ChLabelHcIndexFetchRowHcPtr(runCtx, prevTupleAddr, hcIndex, &prevHcPtr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            nextHcPtr->prevPtr = tupleAddr;
            SE_ERROR(ret, "Unable to get prev HcIndex rowPtr when delete, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
        DB_ASSERT(TransformChLogicRowId(prevHcPtr->nextPtr) == TransformChLogicRowId(tupleAddr));
        prevHcPtr->nextPtr = nextTupleAddr;
    }
    return STATUS_OK_INTER;
}

static StatusInter ChLabelHcIndexListNextTupleDiffPattern(ChLabelRunCtxT *runCtx, ChLogicRowId tupleAddr,
    uint32_t hcIndex, ChLogicRowId prevTupleAddr, ChLogicRowId nextTupleAddr)
{
    ChIndexHcPtrT *nextHcPtr = NULL;
    ChIndexHcPtrT *prevHcPtr = NULL;
    StatusInter ret = STATUS_OK_INTER;
    if (TransformChLogicRowId(prevTupleAddr) != HEAP_INVALID_ADDR) {
        ret = ChLabelHcIndexFetchRowHcPtr(runCtx, prevTupleAddr, hcIndex, &prevHcPtr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "Unable to get prev HcIndex rowPtr when delete, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
        DB_ASSERT(TransformChLogicRowId(prevHcPtr->nextPtr) == TransformChLogicRowId(tupleAddr));
        prevHcPtr->nextPtr = nextTupleAddr;
    }
    if (TransformChLogicRowId(nextTupleAddr) != HEAP_INVALID_ADDR) {
        ret = ChLabelHcIndexFetchRowHcPtr(runCtx, nextTupleAddr, hcIndex, &nextHcPtr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            if (prevHcPtr != NULL) {
                prevHcPtr->nextPtr = tupleAddr;
            }
            SE_ERROR(ret, "Unable to get next HcIndex rowPtr when delete, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
        DB_ASSERT(TransformChLogicRowId(nextHcPtr->prevPtr) == TransformChLogicRowId(tupleAddr));
        nextHcPtr->prevPtr = prevTupleAddr;
    }
    return STATUS_OK_INTER;
}

ALWAYS_INLINE_C static StatusInter ChLabelHcIndexListNextTupleSamePatternPrefetch(
    ChLabelRunCtxT *runCtx, HcDeletePrefetchCtxT *prefetchCtx, uint32_t hcIndex)
{
    ChIndexHcPtrT *nextHcPtr = NULL, *prevHcPtr = NULL;

    ChLogicRowId nextRowId = TransformTupleAddr2LogicRowId(prefetchCtx->nextTupleAddr);
    StatusInter ret = ChLabelHcIndexFetchRowHcPtr(runCtx, nextRowId, hcIndex, &nextHcPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "Unable to prefetch next HcIndex rowPtr when delete, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        prefetchCtx->isPrefetch = false;
        return ret;
    }
    DB_ASSERT(TransformChLogicRowId(nextHcPtr->prevPtr) == prefetchCtx->delTupleAddr);
    prefetchCtx->nextHcPtr = nextHcPtr;

    if (prefetchCtx->prevTupleAddr == prefetchCtx->oldHeadTupleAddr) {
        prefetchCtx->prevHcPtr = prefetchCtx->oldHeadHcPtr;
    } else if (prefetchCtx->prevTupleAddr != HEAP_INVALID_ADDR) {
        ChLogicRowId prevRowId = TransformTupleAddr2LogicRowId(prefetchCtx->prevTupleAddr);
        ret = ChLabelHcIndexFetchRowHcPtr(runCtx, prevRowId, hcIndex, &prevHcPtr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "Unable to prefetch prev HcIndex rowPtr when delete, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            prefetchCtx->isPrefetch = false;
            return ret;
        }
        DB_ASSERT(TransformChLogicRowId(prevHcPtr->nextPtr) == prefetchCtx->delTupleAddr);
        prefetchCtx->prevHcPtr = prevHcPtr;
    }
    prefetchCtx->isPrefetch = true;
    prefetchCtx->filterResult = (uint8_t)INDEX_FILTER_SATISFIED;
    return STATUS_OK_INTER;
}

ALWAYS_INLINE_C static StatusInter ChLabelHcIndexListNextTupleDiffPatternPrefetch(
    ChLabelRunCtxT *runCtx, HcDeletePrefetchCtxT *prefetchCtx, uint32_t hcIndex)
{
    ChIndexHcPtrT *nextHcPtr = NULL, *prevHcPtr = NULL;
    StatusInter ret;
    if (prefetchCtx->prevTupleAddr == prefetchCtx->oldHeadTupleAddr) {
        prefetchCtx->prevHcPtr = prefetchCtx->oldHeadHcPtr;
    } else if (prefetchCtx->prevTupleAddr != HEAP_INVALID_ADDR) {
        ChLogicRowId prevRowId = TransformTupleAddr2LogicRowId(prefetchCtx->prevTupleAddr);
        ret = ChLabelHcIndexFetchRowHcPtr(runCtx, prevRowId, hcIndex, &prevHcPtr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "Unable to prefetch prev HcIndex rowPtr when delete, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            prefetchCtx->isPrefetch = false;
            return ret;
        }
        DB_ASSERT(TransformChLogicRowId(prevHcPtr->nextPtr) == prefetchCtx->delTupleAddr);
        prefetchCtx->prevHcPtr = prevHcPtr;
    }

    if (prefetchCtx->nextTupleAddr != HEAP_INVALID_ADDR) {
        ChLogicRowId nextRowId = TransformTupleAddr2LogicRowId(prefetchCtx->nextTupleAddr);
        ret = ChLabelHcIndexFetchRowHcPtr(runCtx, nextRowId, hcIndex, &nextHcPtr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "Unable to prefetch next HcIndex rowPtr when delete, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            prefetchCtx->isPrefetch = false;
            return ret;
        }
        DB_ASSERT(TransformChLogicRowId(nextHcPtr->prevPtr) == prefetchCtx->delTupleAddr);
        prefetchCtx->nextHcPtr = nextHcPtr;
    }

    prefetchCtx->isPrefetch = true;
    prefetchCtx->filterResult = (uint8_t)INDEX_FILTER_SATISFIED;
    return STATUS_OK_INTER;
}

void ChLabelHcIndexListDeleteWithPrefetch(
    ChLabelRunCtxT *runCtx, HcDeletePrefetchCtxT *prefetchCtx, uint32_t hcIndex, HpHcDeleteInfoT *deleteInfo)
{
    DB_POINTER2(runCtx, prefetchCtx);
    ChIndexHcPtrT *delHcPtr = prefetchCtx->delHcPtr;
    ChIndexHcPtrT *prevHcPtr = prefetchCtx->prevHcPtr;
    ChIndexHcPtrT *nextHcPtr = prefetchCtx->nextHcPtr;
    ChLogicRowId prevTupleAddr = TransformTupleAddr2LogicRowId(prefetchCtx->prevTupleAddr);
    ChLogicRowId nextTupleAddr = TransformTupleAddr2LogicRowId(prefetchCtx->nextTupleAddr);
    if (prevHcPtr != NULL) {
        DB_ASSERT(TransformChLogicRowId(prevHcPtr->nextPtr) == prefetchCtx->delTupleAddr);
        prevHcPtr->nextPtr = nextTupleAddr;
    }
    if (nextHcPtr != NULL) {
        DB_ASSERT(TransformChLogicRowId(nextHcPtr->prevPtr) == prefetchCtx->delTupleAddr);
        nextHcPtr->prevPtr = prevTupleAddr;
    }
    delHcPtr->prevPtr = TransformTupleAddr2LogicRowId(HEAP_INVALID_ADDR);
    delHcPtr->nextPtr = TransformTupleAddr2LogicRowId(HEAP_INVALID_ADDR);
    ChLabelHcIndexListUnlink(prevTupleAddr, nextTupleAddr, deleteInfo);
}

StatusInter ChLabelHcIndexListDelete(
    ChLabelRunCtxT *runCtx, ChLogicRowId tupleAddr, uint32_t hcIndex, HpHcDeleteInfoT *deleteInfo)
{
    DB_POINTER2(runCtx, deleteInfo);
    ChIndexHcPtrT *deleteHcPtr = NULL;
    StatusInter ret = ChLabelHcIndexFetchRowHcPtr(runCtx, tupleAddr, hcIndex, &deleteHcPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "Unable to get delete HcIndex rowPtr when delete, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    ChLogicRowId prevTupleAddr = deleteHcPtr->prevPtr;
    ChLogicRowId nextTupleAddr = deleteHcPtr->nextPtr;
    deleteHcPtr->prevPtr = TransformTupleAddr2LogicRowId(HEAP_INVALID_ADDR);
    deleteHcPtr->nextPtr = TransformTupleAddr2LogicRowId(HEAP_INVALID_ADDR);
    if (TransformChLogicRowId(nextTupleAddr) != HEAP_INVALID_ADDR &&
        HtGetPattern(nextTupleAddr.hashCode, runCtx->labelVarInfo->dirCap) == runCtx->pageCtx.pattern) {
        ret = ChLabelHcIndexListNextTupleSamePattern(runCtx, tupleAddr, hcIndex, prevTupleAddr, nextTupleAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            deleteHcPtr->prevPtr = prevTupleAddr;
            deleteHcPtr->nextPtr = nextTupleAddr;
            return ret;
        }
    } else {
        ret = ChLabelHcIndexListNextTupleDiffPattern(runCtx, tupleAddr, hcIndex, prevTupleAddr, nextTupleAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            deleteHcPtr->prevPtr = prevTupleAddr;
            deleteHcPtr->nextPtr = nextTupleAddr;
            return ret;
        }
    }
    ChLabelHcIndexListUnlink(prevTupleAddr, nextTupleAddr, deleteInfo);
    return STATUS_OK_INTER;
}

StatusInter ChLabelHcIndexListPrefetch4Delete(
    ChLabelRunCtxT *runCtx, HcDeletePrefetchCtxT *prefetchCtx, uint32_t hcIndex)
{
    DB_POINTER2(runCtx, prefetchCtx);
#ifndef NDEBUG
    uint8_t *tmpLockPage = runCtx->lockPage;
    runCtx->lockPage = NULL;
#endif
    ChIndexHcPtrT *delHcPtr = NULL, *oldHeadHcPtr = NULL;
    StatusInter ret = ChLabelHcIndexFetchRowHcPtr(
        runCtx, TransformTupleAddr2LogicRowId(prefetchCtx->delTupleAddr), hcIndex, &delHcPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "Unable to get delete HcIndex rowPtr when delete, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
#ifndef NDEBUG
        runCtx->lockPage = tmpLockPage;
#endif
        prefetchCtx->isPrefetch = false;
        return ret;
    }
    prefetchCtx->delHcPtr = delHcPtr;
    prefetchCtx->prevTupleAddr = TransformChLogicRowId(delHcPtr->prevPtr);
    prefetchCtx->nextTupleAddr = TransformChLogicRowId(delHcPtr->nextPtr);

    if (prefetchCtx->oldHeadTupleAddr == prefetchCtx->delTupleAddr) {
        prefetchCtx->oldHeadHcPtr = delHcPtr;
    } else if (prefetchCtx->oldHeadTupleAddr != HEAP_INVALID_ADDR) {
        ret = ChLabelHcIndexFetchRowHcPtr(
            runCtx, TransformTupleAddr2LogicRowId(prefetchCtx->oldHeadTupleAddr), hcIndex, &oldHeadHcPtr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "Unable to get next HcIndex rowPtr when delete, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
#ifndef NDEBUG
            runCtx->lockPage = tmpLockPage;
#endif
            prefetchCtx->isPrefetch = false;
            return ret;
        }
        prefetchCtx->oldHeadHcPtr = oldHeadHcPtr;
    }
    if (prefetchCtx->nextTupleAddr == HEAP_INVALID_ADDR) {
        ret = ChLabelHcIndexListNextTupleDiffPatternPrefetch(runCtx, prefetchCtx, hcIndex);
    } else if (HtGetPattern(delHcPtr->nextPtr.hashCode, runCtx->labelVarInfo->dirCap) != runCtx->pageCtx.pattern) {
        ret = ChLabelHcIndexListNextTupleDiffPatternPrefetch(runCtx, prefetchCtx, hcIndex);
    } else {
        ret = ChLabelHcIndexListNextTupleSamePatternPrefetch(runCtx, prefetchCtx, hcIndex);
    }
#ifndef NDEBUG
    runCtx->lockPage = tmpLockPage;
#endif
    return ret;
}

static StatusInter ChLabelHcIndexKeyCmpAndFetchNextProc(uint8_t *tuple, uint32_t bufSize, void *userData)
{
    DB_POINTER2(tuple, userData);
    // note: 入参tuple不仅包含数据还包括聚簇索引的相关信息
    ChHcIndexFetchUserDataT *hcFetchCtx = (ChHcIndexFetchUserDataT *)userData;
    DB_POINTER3(hcFetchCtx->scanCtx, hcFetchCtx->fetchedData, hcFetchCtx->chHandle);
    DB_POINTER2(hcFetchCtx->scanCtx->idxCtx, hcFetchCtx->scanCtx->hpBufCmp);

    ChLabelRunCtxT *runCtx = hcFetchCtx->chHandle;
    IndexCtxT *idxCtx = hcFetchCtx->scanCtx->idxCtx;

    // 由上层保证，hcIndex不可能大于最大hashcluster数量
    DB_ASSERT(hcFetchCtx->hcIndex < runCtx->chLabel->labelCfg.slotExtendSize / sizeof(ChIndexHcPtrT));
    ChIndexHcPtrT *hcRowPtr = (ChIndexHcPtrT *)(void *)tuple;
    hcRowPtr += hcFetchCtx->hcIndex;

    ChRowHeadT *rowHead = (ChRowHeadT *)(void *)(tuple + runCtx->chLabel->labelCfg.slotExtendSize);
    ChTupleT fullTupleBuf = {0};
    ClusteredHashPrepareTuple(runCtx, rowHead, &fullTupleBuf, false);
    HeapTupleBufT heapBuf = {fullTupleBuf.pos, fullTupleBuf.buf};
    int32_t cmp;
    bool isMatch;
    Status status = hcFetchCtx->scanCtx->hpBufCmp(idxCtx, hcFetchCtx->scanCtx->idxKey, &heapBuf, &cmp, &isMatch);
    if (status != GMERR_OK && status != GMERR_NO_DATA) {
        ClusteredHashReleaseTuple(runCtx, &fullTupleBuf);
        return DbGetStatusInterErrno(status);
    }
    hcFetchCtx->fetchedData->curAddrMatch = (status == GMERR_OK && isMatch);
    hcFetchCtx->fetchedData->nextTupleAddr = TransformChLogicRowId(hcRowPtr->nextPtr);
    ClusteredHashReleaseTuple(runCtx, &fullTupleBuf);
    return STATUS_OK_INTER;
}

StatusInter ChLabelHcIndexListGetKeyCount(
    ChLabelRunCtxT *runCtx, ChLogicRowId tupleAddr, uint32_t hcIndex, HpHcScanCtxT *scanCtx, uint64_t *count)
{
    DB_POINTER3(runCtx, scanCtx, count);
    HpHcFetchDataT fetchedData = {0};
    ChHcIndexFetchUserDataT hcFetchUserData = {
        .chHandle = runCtx,
        .currAddr = tupleAddr,
        .hcIndex = hcIndex,
        .scanCtx = scanCtx,
        .fetchedData = &fetchedData,
    };
    StatusInter ret = STATUS_OK_INTER;
    while (TransformChLogicRowId(hcFetchUserData.currAddr) != HEAP_INVALID_ADDR) {
        ret = ChLabelHcIndexListFetchTuple(
            runCtx, hcFetchUserData.currAddr, ChLabelHcIndexKeyCmpAndFetchNextProc, (void *)&hcFetchUserData);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, hc index fetch tuple unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
        if (fetchedData.curAddrMatch) {
            (*count)++;
        }
        hcFetchUserData.currAddr = TransformTupleAddr2LogicRowId(fetchedData.nextTupleAddr);
    }
    return ret;
}

StatusInter ChLabelHcIndexInitDynArray(ChLabelRunCtxT *runCtx, HashClusterIterT *hcIter)
{
    if (SECUREC_UNLIKELY(runCtx->seRunCtx->sessionMemCtx == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(UNEXPECTED_NULL_VALUE_INTER,
            "clustered hash, session's memctx is NULL when begin to scan, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    /* HcIndexEndScan中释放内存 */
    Status ret = DbDynArrayInit(&hcIter->dynAddrs, runCtx->seRunCtx->sessionMemCtx, sizeof(ChLogicRowId),
        HASH_CLUSTER_LIST_ITER_DYN_ADDRS_INIT_SIZE, DB_MAX_DYN_ARRAY_SIZE);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret,
            "clustered hash, dynAddrs unable to init, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return DbGetStatusInterErrno(ret);
    }
    return STATUS_OK_INTER;
}

StatusInter ChLabelHcIndexIterDumpExtraAddrs(
    ChLabelRunCtxT *runCtx, ChLogicRowId addr, uint32_t hcIndex, HashClusterIterT *hcIter)
{
    StatusInter retInter = ChLabelHcIndexInitDynArray(runCtx, hcIter);
    if (retInter != STATUS_OK_INTER) {
        return retInter;
    }
    ChLogicRowId curAddr = addr;
    uint32_t itemId;
    uint32_t loadTimes = 0;
    while (TransformChLogicRowId(curAddr) != HEAP_INVALID_ADDR) {
        loadTimes++;
        ChIndexHcPtrT *hcPtr = NULL;
        retInter = ChLabelHcIndexFetchRowHcPtr(runCtx, curAddr, hcIndex, &hcPtr);
        if (retInter == INT_ERR_PAGE_SORTER_OUT_OF_ARRAY) {
            continue;
        }
        if (retInter != STATUS_OK_INTER && DbGetExternalErrno(retInter) != GMERR_NO_DATA) {
            DbDynArrayDestroy(&hcIter->dynAddrs);
            DB_LOG_ERROR_AND_SET_LASTERR(retInter,
                "clustered hash, fetch addr %" PRIu64 " header, hcIndex %" PRIu32 ",  times %" PRIu32
                ", labelId: %" PRIu32 ", indexId: %" PRIu32,
                TransformChLogicRowId(curAddr), hcIndex, loadTimes, runCtx->chLabel->labelCfg.labelId,
                runCtx->chLabel->labelCfg.indexId);
            return retInter;
        }
        if (hcPtr == NULL) {
            return STATUS_OK_INTER;
        }
        ChLogicRowId *addrPtr = (ChLogicRowId *)DbDynArrayAllocItem(&hcIter->dynAddrs, &itemId);
        if (SECUREC_UNLIKELY(addrPtr == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(DbGetExternalErrno(OUT_OF_MEMORY_INTER),
                "clustered hash, %" PRIu32 "th is NULL, alloc num %" PRIu32 ", labelId: %" PRIu32 ", indexId: %" PRIu32,
                loadTimes, DbDynArrayGetAllocNum(&hcIter->dynAddrs), runCtx->chLabel->labelCfg.labelId,
                runCtx->chLabel->labelCfg.indexId);
            DbDynArrayDestroy(&hcIter->dynAddrs);
            return OUT_OF_MEMORY_INTER;
        }
        *addrPtr = curAddr;
        curAddr = hcPtr->nextPtr;
    }
    return STATUS_OK_INTER;
}

StatusInter ChLabelHcIndexIterDumpAddrs(
    ChLabelRunCtxT *runCtx, ChLogicRowId addr, uint32_t hcIndex, HashClusterIterT *hcIter)
{
    DB_POINTER2(runCtx, hcIter);
    hcIter->addrsNum = 0;
    hcIter->cursor = 0;
    StatusInter ret = STATUS_OK_INTER;
    ChLogicRowId curAddr = addr;
    while (TransformChLogicRowId(curAddr) != HEAP_INVALID_ADDR &&
           hcIter->addrsNum < HASH_CLUSTER_LIST_ITER_MAX_STATIC_ADDR_NUM) {
        ChIndexHcPtrT *hcRowPtr = NULL;
        ret = ChLabelHcIndexFetchRowHcPtr(runCtx, curAddr, hcIndex, &hcRowPtr);
        if (SECUREC_UNLIKELY(ret == INT_ERR_PAGE_SORTER_OUT_OF_ARRAY)) {
            continue;
        }
        if (ret != STATUS_OK_INTER && DbGetExternalErrno(ret) != GMERR_NO_DATA) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret,
                "clustered hash, addrs %" PRIu64 " unable to fetch row header, addrNum %" PRIu32 ", labelId: %" PRIu32
                ", indexId: %" PRIu32,
                TransformChLogicRowId(curAddr), hcIter->addrsNum, runCtx->chLabel->labelCfg.labelId,
                runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
        if (hcRowPtr == NULL) {
            return STATUS_OK_INTER;
        }
        hcIter->addrs[hcIter->addrsNum++] = TransformChLogicRowId(curAddr);
        curAddr = hcRowPtr->nextPtr;
    }
    if (TransformChLogicRowId(curAddr) != HEAP_INVALID_ADDR) {
        ret = ChLabelHcIndexIterDumpExtraAddrs(runCtx, curAddr, hcIndex, hcIter);
    }
    return ret;
}

inline __attribute__((always_inline)) StatusInter ChLabelCheckHcIndexKeyBuf(
    const IndexCtxT *idxCtx, const IndexKeyT *idxKey, bool *isSatisfied)
{
    if (SECUREC_UNLIKELY(idxKey->keyLen > SE_IDX_MAX_KEY_LENGTH + idxCtx->idxMetaCfg.nullInfoBytes)) {
        return PROGRAM_LIMIT_EXCEEDED_INTER;
    }
    *isSatisfied = true;
    uint8_t cmpRes = (uint8_t)INDEX_FILTER_SATISFIED;
    Status ret = IdxCheckFilterSatisfied(idxCtx, *idxKey, &cmpRes);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return DbGetStatusInterErrno(ret);
    }
    if (SECUREC_UNLIKELY(cmpRes == (uint8_t)INDEX_FILTER_UNSATISFIED)) {
        *isSatisfied = false;
    }

    return STATUS_OK_INTER;
}

static inline bool ChLabelHcIsIdxEqual(IndexCtxT *iterIdxCtx, const ChLabelUpdateCtxT *updateCtx)
{
    DB_ASSERT(!iterIdxCtx->idxOpenCfg.indexLabel->isKvLabel);
    return (iterIdxCtx->idxMetaCfg.idxType != HASHCLUSTER_INDEX ||
            (updateCtx != NULL && updateCtx->isEqualHcKey[iterIdxCtx->idxOpenCfg.vlIndexLabel->hcIndexId]));
}

// 输入的二级索引key和旧的索引不相等的时候，缓存第一组二级索引的key(buf是复用dm内存)
static inline bool ChLabelHcIsNeedGetVertexBuf(IndexCtxT *idxCtx, const ChLabelUpdateCtxT *updateCtx)
{
    DB_ASSERT(!idxCtx->idxOpenCfg.indexLabel->isKvLabel);
    return (updateCtx != NULL && updateCtx->hcCacheValid &&
            (uint32_t)updateCtx->hcIdCached == idxCtx->idxOpenCfg.vlIndexLabel->hcIndexId);
}

ALWAYS_INLINE_C static void ChLabelHcSetHeader(
    HpHcDeleteInfoT *deleteInfo, IndexCtxT *iterIdxCtx, HtHashCodeT hashCode, ChLogicRowId addr)
{
    HpTupleAddr tmpAddr = TransformChLogicRowId(addr);
    if (deleteInfo->isListEmpty) {
        HpTupleAddr oldHeadAddr = HcIndexGetBucketHeadAddr(iterIdxCtx, hashCode);
        if (SECUREC_LIKELY(oldHeadAddr == tmpAddr)) {
            HcIndexSetBucketHeadAddr(iterIdxCtx, hashCode, HEAP_INVALID_ADDR);
        } else {
            // addr为链表头，但对应bucket上的oldHeadAddr与addr不同，需要重新遍历整个bucket找到正确槽位置无效值
            // 回滚流程才可能走到该分支，因此对性能影响基本为0
            HcIndexTraverseSetBucketAddr(iterIdxCtx, tmpAddr, HEAP_INVALID_ADDR);
        }
    } else if (deleteInfo->isListHeadDeleted) {
        HpTupleAddr oldHeadAddr = HcIndexGetBucketHeadAddr(iterIdxCtx, hashCode);
        if (SECUREC_LIKELY(oldHeadAddr == tmpAddr)) {
            HcIndexSetBucketHeadAddr(iterIdxCtx, hashCode, deleteInfo->newHeadTupleAddr);
        } else {
            // addr为链表头，但对应bucket上的oldHeadAddr与addr不同，需要重新遍历整个bucket找到正确槽位（undo场景出现）
            HcIndexTraverseSetBucketAddr(iterIdxCtx, tmpAddr, deleteInfo->newHeadTupleAddr);
        }
    }
}

#ifdef ARM32
StatusInter ChLabelHcIndexPrefetch4MergeDelete(ChLabelRunCtxT *runCtx, IndexCtxT *idxCtx, uint8_t *buf,
    ChLogicRowId logicRowId, ChLabelUpdateCtxT *updateCtx, IndexKeyT *idxKey, bool *needUpdate)
{
    StatusInter ret;
    HcDeletePrefetchCtxT *delPrefetchCtx = &idxCtx->hcPrefetchCtx.hcDeletePrefetchCtx;
    if (ChLabelHcIsNeedGetVertexBuf(idxCtx, updateCtx)) {
        DmVertexGetVertexKeyBuf(runCtx->openCfg.vertex, &idxKey->keyData);
        idxKey->keyLen = updateCtx->keyBufCachedKeyLen;
    } else {
        ret = ClusteredHashGetIndexKey(runCtx->openCfg.vertex, idxCtx->idxMetaCfg.indexId,
            (DmIndexKeyBufInfoT **)&idxCtx->idxOpenCfg.keyInfo, buf, idxKey);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, get hc index key unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
    }
    bool isSatisfied;
    ret = ChLabelCheckHcIndexKeyBuf(idxCtx, idxKey, &isSatisfied);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, check hc index key unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    if (!isSatisfied) {
        delPrefetchCtx->isPrefetch = true;
        delPrefetchCtx->filterResult = (uint8_t)INDEX_FILTER_UNSATISFIED;
        *needUpdate = false;
        return STATUS_OK_INTER;
    }
    DB_ASSERT(!idxCtx->idxOpenCfg.indexLabel->isKvLabel);
    uint32_t hcIndex = idxCtx->idxOpenCfg.vlIndexLabel->hcIndexId;
    uint8_t *bucketContainer = HcGetBucketContainer(idxCtx);
    uint8_t *bucketBitmap = HcGetBucketBitmap(idxCtx);
    HtContainerT *container = HcCastIdxAsHashCluster(idxCtx->idxHandle);
    HcInitHcDeletePrefetchCtx(delPrefetchCtx);
    delPrefetchCtx->bucketIndex = HcGetBucketIndexByKey(*idxKey, container);
    delPrefetchCtx->oldHeadTupleAddr =
        HcGetBucketAddr(container, delPrefetchCtx->bucketIndex, bucketContainer, bucketBitmap);
    delPrefetchCtx->delTupleAddr = TransformChLogicRowId(logicRowId);
    ret = ChLabelHcIndexListPrefetch4Delete(runCtx, delPrefetchCtx, hcIndex);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, prefetch key unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
    }
    *needUpdate = true;
    return ret;
}
#endif

ALWAYS_INLINE_C static StatusInter ChLabelHcIndexSetIdxKey(
    ChLabelRunCtxT *runCtx, IndexCtxT *iterIdxCtx, ChLabelUpdateCtxT *updateCtx, uint8_t *buf, IndexKeyT *idxKey)
{
    StatusInter ret = STATUS_OK_INTER;
    if (ChLabelHcIsNeedGetVertexBuf(iterIdxCtx, updateCtx)) {
        DmVertexGetVertexKeyBuf(runCtx->openCfg.vertex, &idxKey->keyData);
        idxKey->keyLen = updateCtx->keyBufCachedKeyLen;
    } else {
        ret = ClusteredHashGetIndexKey(runCtx->openCfg.vertex, iterIdxCtx->idxMetaCfg.indexId,
            (DmIndexKeyBufInfoT **)&iterIdxCtx->idxOpenCfg.keyInfo, buf, idxKey);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, get hc index key unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

ALWAYS_INLINE_C static StatusInter ChLabelHcIndexMergeDelete4Batch(
    ChLabelRunCtxT *runCtx, uint8_t *buf, ChLogicRowId logicRowId, ChLabelUpdateCtxT *updateCtx)
{
#ifndef NDEBUG
    uint8_t *tmpLockPage = runCtx->lockPage;
    runCtx->lockPage = NULL;
#endif
    IndexCtxT **idxCtx = runCtx->openCfg.secIdxCtx;
    uint32_t secIndexNum = runCtx->openCfg.secIndexNum;
    bool isSatisfied;
    IndexKeyT idxKey;
    StatusInter ret = STATUS_OK_INTER;
    IndexCtxT *iterIdxCtx = NULL;
    for (uint32_t i = 0; i < secIndexNum; ++i) {
        iterIdxCtx = idxCtx[i];
        if (ChLabelHcIsIdxEqual(iterIdxCtx, updateCtx)) {
            continue;
        }
        ret = ChLabelHcIndexSetIdxKey(runCtx, iterIdxCtx, updateCtx, buf, &idxKey);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            break;
        }
        ret = ChLabelCheckHcIndexKeyBuf(iterIdxCtx, &idxKey, &isSatisfied);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, check hc index key unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            break;
        }
        if (SECUREC_UNLIKELY(!isSatisfied)) {
            continue;
        }
        HpHcDeleteInfoT deleteInfo = {
            .isListHeadDeleted = false, .isListEmpty = false, .newHeadTupleAddr = HEAP_INVALID_ADDR, .preserved = 0u};
        DB_ASSERT(!iterIdxCtx->idxOpenCfg.indexLabel->isKvLabel);
        ret = ChLabelHcIndexListDelete(runCtx, logicRowId, iterIdxCtx->idxOpenCfg.vlIndexLabel->hcIndexId, &deleteInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, delete hc index unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            break;
        }
        HtHashCodeT hashCode = HtGetHashCode(idxKey);
        ChLabelHcSetHeader(&deleteInfo, iterIdxCtx, hashCode, logicRowId);
    }
#ifndef NDEBUG
    runCtx->lockPage = tmpLockPage;
#endif
    return ret;
}

#ifdef ARM32
ALWAYS_INLINE_C static StatusInter ChLabelHcIndexMergeDeleteWithPrefetch(
    ChLabelRunCtxT *runCtx, uint8_t *buf, ChLogicRowId logicRowId, ChLabelUpdateCtxT *updateCtx)
{
#ifndef NDEBUG
    uint8_t *tmpLockPage = runCtx->lockPage;
    runCtx->lockPage = NULL;
#endif
    IndexCtxT **idxCtx = runCtx->openCfg.secIdxCtx;
    uint32_t secIndexNum = runCtx->openCfg.secIndexNum;
    IndexKeyT idxKey[DM_MAX_KEY_COUNT];
    bool needUpdate[DM_MAX_KEY_COUNT];
    StatusInter ret;
    for (uint32_t i = 0; i < secIndexNum; ++i) {
        IndexCtxT *iterIdxCtx = idxCtx[i];
        if (ChLabelHcIsIdxEqual(iterIdxCtx, updateCtx)) {
            needUpdate[i] = false;
            continue;
        }
        ret = ChLabelHcIndexPrefetch4MergeDelete(
            runCtx, iterIdxCtx, buf, logicRowId, updateCtx, &idxKey[i], &needUpdate[i]);
        if (ret != STATUS_OK_INTER) {
#ifndef NDEBUG
            runCtx->lockPage = tmpLockPage;
#endif
            HcInitHcPrefetchCtxArr(idxCtx, i);
            return ret;
        }
    }

    for (uint32_t i = 0; i < secIndexNum; ++i) {
        IndexCtxT *iterIdxCtx = idxCtx[i];
        if (needUpdate[i]) {
            HcDeletePrefetchCtxT *delPrefetchCtx = &iterIdxCtx->hcPrefetchCtx.hcDeletePrefetchCtx;
            HpHcDeleteInfoT deleteInfo = {.isListHeadDeleted = false,
                .isListEmpty = false,
                .newHeadTupleAddr = HEAP_INVALID_ADDR,
                .preserved = 0u};
            DB_ASSERT(!iterIdxCtx->idxOpenCfg.indexLabel->isKvLabel);
            ChLabelHcIndexListDeleteWithPrefetch(
                runCtx, delPrefetchCtx, iterIdxCtx->idxOpenCfg.vlIndexLabel->hcIndexId, &deleteInfo);
            HtHashCodeT hashCode = HtGetHashCode(idxKey[i]);
            ChLabelHcSetHeader(&deleteInfo, iterIdxCtx, hashCode, logicRowId);
            delPrefetchCtx->isPrefetch = false;
        }
    }
#ifndef NDEBUG
    runCtx->lockPage = tmpLockPage;
#endif
    return STATUS_OK_INTER;
}
#endif

// 入参必须是逻辑addr
StatusInter ChLabelHcIndexMergeDelete(
    ChLabelRunCtxT *runCtx, uint8_t *buf, ChLogicRowId logicRowId, ChLabelUpdateCtxT *updateCtx)
{
    DB_POINTER2(runCtx, buf);
    if (SECUREC_UNLIKELY(runCtx->chLabel->labelCfg.slotExtendSize == 0)) {
        return STATUS_OK_INTER;
    }
#ifdef ARM32
    if (!runCtx->isBatchOperation) {
        return ChLabelHcIndexMergeDeleteWithPrefetch(runCtx, buf, logicRowId, updateCtx);
    }
#endif  // ARM32
    return ChLabelHcIndexMergeDelete4Batch(runCtx, buf, logicRowId, updateCtx);
}

void ClusterHashHcIndexRollbackHeader(ChLabelRunCtxT *runCtx, uint8_t *buf, ChLabelUpdateCtxT *updateCtx,
    const HpTupleAddr *oldHeadPtr, ChIndexHcPtrT *hcPtr, uint32_t maxNum)
{
    IndexCtxT **idxCtx = runCtx->openCfg.secIdxCtx;
    IndexKeyT idxKey;
    for (uint32_t i = 0; i < maxNum; ++i) {
        if (idxCtx[i]->idxMetaCfg.idxType != HASHCLUSTER_INDEX) {
            continue;
        }
        DB_ASSERT(!idxCtx[i]->idxOpenCfg.indexLabel->isKvLabel);
        uint32_t hcIndex = idxCtx[i]->idxOpenCfg.vlIndexLabel->hcIndexId;
        if (updateCtx != NULL && updateCtx->isEqualHcKey[hcIndex]) {
            continue;
        }
        ChIndexHcPtrT *targetHcPtr = hcPtr + hcIndex;
        *(HpTupleAddr *)(void *)&targetHcPtr->prevPtr = HEAP_INVALID_ADDR;
        *(HpTupleAddr *)(void *)&targetHcPtr->nextPtr = HEAP_INVALID_ADDR;
        // 获取失败了说明正常流程也是失败的，不用异常处理
        StatusInter ret = ClusteredHashGetIndexKey(runCtx->openCfg.vertex, idxCtx[i]->idxMetaCfg.indexId,
            (DmIndexKeyBufInfoT **)&idxCtx[i]->idxOpenCfg.keyInfo, buf, &idxKey);
        if (ret == STATUS_OK_INTER) {
            HtHashCodeT hashCode = HtGetHashCode(idxKey);
            HcIndexSetBucketHeadAddr(idxCtx[i], hashCode, oldHeadPtr[hcIndex]);  // 回退到旧的头
            if (oldHeadPtr[hcIndex] == HEAP_INVALID_ADDR) {
                continue;
            }
            // 回退旧head的前向
            ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(oldHeadPtr[hcIndex]);
            ret = ClusteredHashInitPageCtxByRowId(runCtx, &runCtx->pageCtx, logicRowId);
            if (ret != STATUS_OK_INTER) {
                continue;  // 失败说明正常流程也是失败的，不用异常处理
            }
            ChPhyRowId phyRowId = ChLabelGetPhysicalAddrByLogicalAddr(runCtx, logicRowId);
            if (SECUREC_UNLIKELY(TransformChPhyRowId(phyRowId) == HEAP_INVALID_ADDR)) {
                continue;  // 失败说明正常流程也是失败的，不用异常处理
            }
            uint32_t tupleId = ClusteredHashGetInnerTupleId(runCtx, phyRowId.tupleId);
            uint8_t *tupleHdr = ClusteredHashTupleHdr(&runCtx->pageCtx, runCtx->pageCtx.needVisitOverFlowPage);
            ChIndexHcPtrT *oldHeadHcPtr = (ChIndexHcPtrT *)(void *)ClusteredHashGetTupleBySlot(
                tupleHdr, runCtx->pageCtx.pageMetaData.tupleSize, tupleId);
            oldHeadHcPtr += hcIndex;
            *(HpTupleAddr *)(void *)&oldHeadHcPtr->prevPtr = HEAP_INVALID_ADDR;
        }
    }
}

StatusInter ClusteredHashHcIndexGetIndexKey(
    const ChLabelRunCtxT *runCtx, IndexCtxT *idxCtx, ChLabelUpdateCtxT *updateCtx, uint8_t *buf, IndexKeyT *idxKey)
{
    if (ChLabelHcIsNeedGetVertexBuf(idxCtx, updateCtx)) {
        DmVertexGetCompareKeyBuf(runCtx->openCfg.vertex, &idxKey->keyData);
        idxKey->keyLen = updateCtx->cmpBufCachedKeyLen;
    } else {
        StatusInter ret = ClusteredHashGetIndexKey(runCtx->openCfg.vertex, idxCtx->idxMetaCfg.indexId,
            (DmIndexKeyBufInfoT **)&idxCtx->idxOpenCfg.keyInfo, buf, idxKey);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret,
                "clustered hash get key unsucc, rollback index list header. hcIndexId:%" PRIu32 ", labelId: %" PRIu32
                ", indexId: %" PRIu32,
                idxCtx->idxMetaCfg.indexId, runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

void ClusteredHcIndexUpdateALLNewHeader(IndexCtxT *idxCtx, HpTupleAddr *oldHeadAddr, ChIndexHcPtrT *hcPtr)
{
    DB_ASSERT(!idxCtx->idxOpenCfg.indexLabel->isKvLabel);
    uint32_t hcIndex = idxCtx->idxOpenCfg.vlIndexLabel->hcIndexId;
    ChIndexHcPtrT *targetHcPtr = (hcPtr + hcIndex);
    *(HpTupleAddr *)(void *)&targetHcPtr->prevPtr = HEAP_INVALID_ADDR;
    targetHcPtr->nextPtr = TransformTupleAddr2LogicRowId(oldHeadAddr[hcIndex]);
}

StatusInter ClusteredHcIndexLinkALLOldHeader(
    ChLabelRunCtxT *runCtx, IndexCtxT *idxCtx, ChLogicRowId insertRowId, HpTupleAddr *oldHeadAddr)
{
    DB_ASSERT(!idxCtx->idxOpenCfg.indexLabel->isKvLabel);
    uint32_t hcIndex = idxCtx->idxOpenCfg.vlIndexLabel->hcIndexId;
    // 这里不用对当前页加页锁，因为聚簇索引在上层已经上了写锁, 聚簇索引冲突连不会有读写并发冲突
    // 同时在轻量化事务下, 一写多读, 当前页也不会被其他写线程修改
    ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(oldHeadAddr[hcIndex]);
    StatusInter ret = ClusteredHashInitPageCtxByRowId(runCtx, &runCtx->pageCtx, logicRowId);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret,
            "get segment page unsucc when batch link old header. (%" PRIu32 ", %" PRIu32 "), labelId: %" PRIu32
            ", indexId: %" PRIu32,
            logicRowId.hashCode, logicRowId.tupleId, runCtx->chLabel->labelCfg.labelId,
            runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    ChPhyRowId phyRowId = ChLabelGetPhysicalAddrByLogicalAddr(runCtx, logicRowId);
    if (SECUREC_UNLIKELY(TransformChPhyRowId(phyRowId) == HEAP_INVALID_ADDR)) {
        // 轻量化事务不存在二级索引残留情况，需要定位
        SE_LAST_ERROR(NO_DATA_INTER,
            "get segment page unsucc when batch link old header. (%" PRIu32 ", %" PRIu32 "), labelId: %" PRIu32
            ", indexId: %" PRIu32,
            logicRowId.hashCode, logicRowId.tupleId, runCtx->chLabel->labelCfg.labelId,
            runCtx->chLabel->labelCfg.indexId);
        return NO_DATA_INTER;
    }
    // 获取聚簇索引前后向指针
    uint32_t tupleId = ClusteredHashGetInnerTupleId(runCtx, phyRowId.tupleId);
    uint8_t *tupleHdr = ClusteredHashTupleHdr(&runCtx->pageCtx, runCtx->pageCtx.needVisitOverFlowPage);
    ChIndexHcPtrT *targetHcPtr =
        (ChIndexHcPtrT *)(void *)ClusteredHashGetTupleBySlot(tupleHdr, runCtx->pageCtx.pageMetaData.tupleSize, tupleId);
    targetHcPtr += hcIndex;
    targetHcPtr->prevPtr = insertRowId;
    return STATUS_OK_INTER;
}

static inline HpTupleAddr ClusteredHcIndexGetOldAddrAndSetNew(
    IndexCtxT *idxCtx, HtHashCodeT hashCode, HpTupleAddr newAddr)
{
    DB_POINTER2(idxCtx, idxCtx->idxHandle);
    IdxBaseT *idxBase = idxCtx->idxHandle;
    HtContainerT *container = HcCastIdxAsHashCluster(idxBase);
    uint32_t bucketIndex = HcGetBucketIndexByHashCode(hashCode, container);
    uint8_t *bucket = GetBucketAddr(container, bucketIndex, HcGetBucketContainer(idxCtx), HcGetBucketBitmap(idxCtx));
    HpTupleAddr resAddr = HeapUncompressTupleAddr64(*(const TuplePointerT *)bucket);
    *(TuplePointerT *)bucket = HeapCompressTupleAddr64(newAddr);
    return resAddr;
}

StatusInter ClusteredHcIndexMergeInsertInner(
    ChLabelRunCtxT *runCtx, ChHashClusterInsertParamT *insertPara, uint8_t *tupleHdr, ChLabelUpdateCtxT *updateCtx)
{
    uint32_t secIdxNum = runCtx->openCfg.secIndexNum;
    IndexCtxT **idxCtx = runCtx->openCfg.secIdxCtx;
    HpTupleAddr oldHeadAddr[DM_MAX_KEY_COUNT];
    uint32_t tupleId = ClusteredHashGetInnerTupleId(runCtx, insertPara->phyRowId.tupleId);
    ChIndexHcPtrT *hcPtr =
        (ChIndexHcPtrT *)(void *)ClusteredHashGetTupleBySlot(tupleHdr, runCtx->pageCtx.pageMetaData.tupleSize, tupleId);
    bool isSatisfied;
    IndexKeyT idxKey;
    uint8_t *buf = insertPara->buf;
    HpTupleAddr addr = TransformChLogicRowId(insertPara->logicRowId);

    for (uint32_t i = 0; i < secIdxNum; ++i) {
        uint32_t hcIndex = idxCtx[i]->idxOpenCfg.vlIndexLabel->hcIndexId;
        if (idxCtx[i]->idxMetaCfg.idxType != HASHCLUSTER_INDEX ||
            (updateCtx != NULL && updateCtx->isEqualHcKey[hcIndex])) {
            continue;
        }
        StatusInter ret = ClusteredHashHcIndexGetIndexKey(runCtx, idxCtx[i], updateCtx, buf, &idxKey);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            ClusterHashHcIndexRollbackHeader(runCtx, buf, updateCtx, oldHeadAddr, hcPtr, i);
            return ret;
        }

        ret = ChLabelCheckHcIndexKeyBuf(idxCtx[i], &idxKey, &isSatisfied);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret,
                "clustered hash check key unsucc, rollback index list header. hcIndexId:%" PRIu32 ", labelId: %" PRIu32
                ", indexId: %" PRIu32,
                idxCtx[i]->idxMetaCfg.indexId, runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            ClusterHashHcIndexRollbackHeader(runCtx, buf, updateCtx, oldHeadAddr, hcPtr, i);
            return ret;
        }

        // 由上层保证，hcIndex不可能大于最大hashcluster数量
        DB_ASSERT(hcIndex < secIdxNum);
        if (SECUREC_UNLIKELY(!isSatisfied)) {
            oldHeadAddr[hcIndex] = HEAP_INVALID_ADDR;
            continue;
        }
        HtHashCodeT hashCode = HtGetHashCode(idxKey);
        // step1: 先更新hash表头，避免cachemiss
        oldHeadAddr[hcIndex] = ClusteredHcIndexGetOldAddrAndSetNew(idxCtx[i], hashCode, addr);
        if (oldHeadAddr[hcIndex] != HEAP_INVALID_ADDR) {
            // step2: 更新旧head的前向
            ret = ClusteredHcIndexLinkALLOldHeader(runCtx, idxCtx[i], insertPara->logicRowId, oldHeadAddr);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                // 此处bucketHead已经修改，因此需要传入i + 1
                ClusterHashHcIndexRollbackHeader(runCtx, buf, updateCtx, oldHeadAddr, hcPtr, i + 1);
                return ret;
            }
        }
        // step3: 更新新head的后向
        ClusteredHcIndexUpdateALLNewHeader(idxCtx[i], oldHeadAddr, hcPtr);
    }
    return STATUS_OK_INTER;
}

StatusInter ClusteredHcIndexLinkOldHeaderSingle(
    ChLabelRunCtxT *runCtx, const ChLogicRowId insertRowId, HpTupleAddr oldHeadPtr, uint32_t hcIndex)
{
    // 这里不用对当前页加页锁，因为聚簇索引在上层已经上了写锁, 聚簇索引冲突连不会有读写并发冲突
    // 同时在轻量化事务下, 一写多读, 当前页也不会被其他写线程修改
    ChLogicRowId logicRowId = TransformTupleAddr2LogicRowId(oldHeadPtr);
    StatusInter ret = ClusteredHashInitPageCtxByRowId(runCtx, &runCtx->pageCtx, logicRowId);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret,
            "get segment page unsucc when link old header. (%" PRIu32 ", %" PRIu32 "), labelId: %" PRIu32
            ", indexId: %" PRIu32,
            logicRowId.hashCode, logicRowId.tupleId, runCtx->chLabel->labelCfg.labelId,
            runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    ChPhyRowId phyRowId = ChLabelGetPhysicalAddrByLogicalAddr(runCtx, logicRowId);
    if (SECUREC_UNLIKELY(TransformChPhyRowId(phyRowId) == HEAP_INVALID_ADDR)) {
        // 轻量化事务不存在二级索引残留情况，需要定位
        SE_LAST_ERROR(NO_DATA_INTER,
            "get segment page unsucc when link old header. (%" PRIu32 ", %" PRIu32 "), labelId: %" PRIu32
            ", indexId: %" PRIu32,
            logicRowId.hashCode, logicRowId.tupleId, runCtx->chLabel->labelCfg.labelId,
            runCtx->chLabel->labelCfg.indexId);
        DB_ASSERT(false);
        return NO_DATA_INTER;
    }
    // 获取聚簇索引前后向指针
    uint32_t tupleId = ClusteredHashGetInnerTupleId(runCtx, phyRowId.tupleId);
    uint8_t *tupleHdr = ClusteredHashTupleHdr(&runCtx->pageCtx, runCtx->pageCtx.needVisitOverFlowPage);
    ChIndexHcPtrT *targetHcPtr =
        (ChIndexHcPtrT *)(void *)ClusteredHashGetTupleBySlot(tupleHdr, runCtx->pageCtx.pageMetaData.tupleSize, tupleId);
    targetHcPtr += hcIndex;
    targetHcPtr->prevPtr = insertRowId;

    return STATUS_OK_INTER;
}

StatusInter ChLabelHcIndexMergeInsert(
    ChLabelRunCtxT *runCtx, ChHashClusterInsertParamT *insertPara, uint8_t *tupleHdr, ChLabelUpdateCtxT *updateCtx)
{
    DB_POINTER4(runCtx, insertPara, insertPara->buf, tupleHdr);
    // 由上层保证，插入流程，物理addr不可能为无效值
    DB_ASSERT(TransformChPhyRowId(insertPara->phyRowId) != HEAP_INVALID_ADDR);
    if (SECUREC_UNLIKELY(runCtx->chLabel->labelCfg.slotExtendSize == 0)) {
        return STATUS_OK_INTER;
    }
    if (SECUREC_UNLIKELY(runCtx->openCfg.secIndexNum > DM_MAX_KEY_COUNT)) {
        SE_ERROR(PROGRAM_LIMIT_EXCEEDED_INTER,
            "too much secondary index, count: %" PRIu32 ", labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->openCfg.secIndexNum, runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return PROGRAM_LIMIT_EXCEEDED_INTER;
    }
#ifndef NDEBUG
    uint8_t *tmpLockPage = runCtx->lockPage;
    runCtx->lockPage = NULL;
#endif
    // 不初始化，函数内部会赋值
    StatusInter ret = ClusteredHcIndexMergeInsertInner(runCtx, insertPara, tupleHdr, updateCtx);
#ifndef NDEBUG
    runCtx->lockPage = tmpLockPage;
#endif
    return ret;
}

uint32_t ChLabelGetHcIndexSlotSpace(uint32_t indexCount)
{
    return indexCount * (uint32_t)sizeof(ChIndexHcPtrT);
}

Status ChLabelHcListPrefetch4Insert(
    ChLabelRunHdlT chRunHdl, HcInsertPrefetchCtxT *prefetchCtx, uint32_t hcIndex, HpTupleAddr oldHeadAddr)
{
    return DbGetExternalErrno(ChLabelHcIndexListPrefetch4Insert(chRunHdl, prefetchCtx, hcIndex, oldHeadAddr));
}

Status ChLabelHcListPrefetch4Delete(ChLabelRunHdlT chRunHdl, HcDeletePrefetchCtxT *prefetchCtx, uint32_t hcIndex)
{
    return DbGetExternalErrno(ChLabelHcIndexListPrefetch4Delete(chRunHdl, prefetchCtx, hcIndex));
}

void ChLabelHcListDeleteWithPrefetch(
    ChLabelRunHdlT chRunHdl, HcDeletePrefetchCtxT *prefetchCtx, uint32_t hcIndex, HpHcDeleteInfoT *deleteInfo)
{
    ChLabelHcIndexListDeleteWithPrefetch(chRunHdl, prefetchCtx, hcIndex, deleteInfo);
}

void ChLabelHcListInsertWithPrefetch(
    ChLabelRunHdlT chRunHdl, HcInsertPrefetchCtxT *prefetchCtx, uint32_t hcIndex, HpTupleAddr oldHeadAddr)
{
    ChLabelHcIndexListInsertWithPrefetch(chRunHdl, prefetchCtx, hcIndex, oldHeadAddr);
}

Status ChLabelHcListInsert(
    ChLabelRunHdlT chRunHdl, HpTupleAddr insertTupleAddr, HpTupleAddr headTupleAddr, uint32_t hcIndex)
{
    return DbGetExternalErrno(ChLabelHcIndexListInsert(chRunHdl, TransformTupleAddr2LogicRowId(insertTupleAddr),
        TransformTupleAddr2LogicRowId(headTupleAddr), hcIndex));
}

Status ChLabelHcListDelete(
    ChLabelRunHdlT chRunHdl, HpTupleAddr deleteTupleAddr, uint32_t hcIndex, HpHcDeleteInfoT *deleteInfo)
{
    return DbGetExternalErrno(
        ChLabelHcIndexListDelete(chRunHdl, TransformTupleAddr2LogicRowId(deleteTupleAddr), hcIndex, deleteInfo));
}

Status ChLabelHcListGetKeyCount(
    ChLabelRunHdlT chRunHdl, HpTupleAddr currTupleAddr, uint32_t hcIndex, HpHcScanCtxT *scanCtx, uint64_t *count)
{
    return DbGetExternalErrno(
        ChLabelHcIndexListGetKeyCount(chRunHdl, TransformTupleAddr2LogicRowId(currTupleAddr), hcIndex, scanCtx, count));
}

Status ChLabelHcIterDumpAddrs(
    ChLabelRunHdlT chRunHdl, HpTupleAddr currTupleAddr, uint32_t hcIndex, HashClusterIterT *hcIter)
{
    return DbGetExternalErrno(
        ChLabelHcIndexIterDumpAddrs(chRunHdl, TransformTupleAddr2LogicRowId(currTupleAddr), hcIndex, hcIter));
}

ALWAYS_INLINE_C static bool RefreshChLabelUpdateCtx(
    ChLabelUpdateCtxT *updateCtx, const IndexCtxT *idxCtx, const IndexKeyT *idxKeyOld, const IndexKeyT *idxKeyNew)
{
    // 若没有对HashCluster索引所在的字段进行修改，则不进行额外操作；
    if (idxKeyOld->keyLen == idxKeyNew->keyLen &&
        memcmp(idxKeyOld->keyData, idxKeyNew->keyData, idxKeyOld->keyLen) == 0) {
        updateCtx->hcCacheValid = false;
        DB_ASSERT(!idxCtx->idxOpenCfg.indexLabel->isKvLabel);
        updateCtx->isEqualHcKey[idxCtx->idxOpenCfg.vlIndexLabel->hcIndexId] = true;
        return false;
    }
    // 标记这块buf里的内容是曾经被解析过的,对应的二级索引的id是hcCache,两块buf分别对应的长度是keyBufCachedKeyLen和cmpBufCachedKeyLen
    updateCtx->hcCacheValid = true;
    DB_ASSERT(!idxCtx->idxOpenCfg.indexLabel->isKvLabel);
    updateCtx->hcIdCached = idxCtx->idxOpenCfg.vlIndexLabel->hcIndexId;
    updateCtx->keyBufCachedKeyLen = idxKeyOld->keyLen;
    updateCtx->cmpBufCachedKeyLen = idxKeyNew->keyLen;
    return true;
}

ALWAYS_INLINE static StatusInter ClusteredHashGetOldAndNewIndexKey(ChLabelRunCtxT *runCtx, IndexCtxT *idxCtx,
    ChTupleT *tuple, ChLabelUpdateCtxT *updateCtx, IndexKeyT *idxKeyOld, IndexKeyT *idxKeyNew)
{
    // 获取旧key
    StatusInter ret = ClusteredHashGetIndexKey(runCtx->openCfg.vertex, idxCtx->idxMetaCfg.indexId,
        (DmIndexKeyBufInfoT **)&idxCtx->idxOpenCfg.keyInfo, tuple->buf, idxKeyOld);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, get oldKey unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    // 获取新key
    ret = ClusteredHashGetIndexKeyOnCmpMem(runCtx->openCfg.vertex, idxCtx->idxMetaCfg.indexId,
        (DmIndexKeyBufInfoT **)&idxCtx->idxOpenCfg.keyInfo, updateCtx->buf, idxKeyNew);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, get newKey unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
    }
    return ret;
}

#ifdef ARM32
ALWAYS_INLINE_C static StatusInter ChLablePrefetchOldKey4UpdateHc(ChLabelRunCtxT *runCtx, IndexCtxT *idxCtx,
    ChTupleT *tuple, ChLogicRowId updateRowId, ChLabelUpdateCtxT *updateCtx, IndexKeyT *idxKeyOld,
    HtHashCodeT *hashCode)
{
    bool isSatisfied;
    DB_ASSERT(!idxCtx->idxOpenCfg.indexLabel->isKvLabel);
    uint32_t hcIndex = idxCtx->idxOpenCfg.vlIndexLabel->hcIndexId;
    uint8_t *bucketContainer = HcGetBucketContainer(idxCtx);
    uint8_t *bucketBitmap = HcGetBucketBitmap(idxCtx);
    HtContainerT *container = HcCastIdxAsHashCluster(idxCtx->idxHandle);
    HpTupleAddr updateAddr = TransformChLogicRowId(updateRowId);
    StatusInter ret = ChLabelCheckHcIndexKeyBuf(idxCtx, idxKeyOld, &isSatisfied);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, check hc index old key unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    HcDeletePrefetchCtxT *delPrefetchCtx = &idxCtx->hcPrefetchCtx.hcDeletePrefetchCtx;
    if (isSatisfied) {
        HcInitHcDeletePrefetchCtx(delPrefetchCtx);
        delPrefetchCtx->bucketIndex = HcGetBucketIndexByKey(*idxKeyOld, container);
        delPrefetchCtx->oldHeadTupleAddr =
            HcGetBucketAddr(container, delPrefetchCtx->bucketIndex, bucketContainer, bucketBitmap);
        delPrefetchCtx->delTupleAddr = updateAddr;
        ret = ChLabelHcIndexListPrefetch4Delete(runCtx, delPrefetchCtx, hcIndex);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, prefetch oldkey unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
    } else {
        delPrefetchCtx->isPrefetch = true;
        delPrefetchCtx->filterResult = (uint8_t)INDEX_FILTER_UNSATISFIED;
    }
    *hashCode = HtGetHashCode(*idxKeyOld);
    return ret;
}

ALWAYS_INLINE_C static StatusInter ChLablePrefetchNewKey4UpdateHc(ChLabelRunCtxT *runCtx, IndexCtxT *idxCtx,
    ChTupleT *tuple, ChLogicRowId updateRowId, ChLabelUpdateCtxT *updateCtx, IndexKeyT *idxKeyNew)
{
    bool isSatisfied;
    DB_ASSERT(!idxCtx->idxOpenCfg.indexLabel->isKvLabel);
    uint32_t hcIndex = idxCtx->idxOpenCfg.vlIndexLabel->hcIndexId;
    uint8_t *bucketContainer = HcGetBucketContainer(idxCtx);
    uint8_t *bucketBitmap = HcGetBucketBitmap(idxCtx);
    HtContainerT *container = HcCastIdxAsHashCluster(idxCtx->idxHandle);
    HpTupleAddr updateAddr = TransformChLogicRowId(updateRowId);
    HcInsertPrefetchCtxT *insertPrefetchCtx = &idxCtx->hcPrefetchCtx.hcInsertPrefetchCtx;
    StatusInter ret = ChLabelCheckHcIndexKeyBuf(idxCtx, idxKeyNew, &isSatisfied);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, check hc index new key unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        insertPrefetchCtx->isPrefetch = false;
        return ret;
    }
    if (isSatisfied) {
        insertPrefetchCtx->bucketIndex = HcGetBucketIndexByKey(*idxKeyNew, container);
        HpTupleAddr oldHeadAddr =
            HcGetBucketAddr(container, insertPrefetchCtx->bucketIndex, bucketContainer, bucketBitmap);
        insertPrefetchCtx->insertTupleAddr = updateAddr;
        insertPrefetchCtx->insertHcPtr =
            (idxCtx->hcPrefetchCtx.hcDeletePrefetchCtx.filterResult == INDEX_FILTER_SATISFIED) ?
                idxCtx->hcPrefetchCtx.hcDeletePrefetchCtx.delHcPtr :
                NULL;
        ret = ChLabelHcIndexListPrefetch4Insert(runCtx, insertPrefetchCtx, hcIndex, oldHeadAddr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, prefetch newKey unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
    } else {
        insertPrefetchCtx->isPrefetch = true;
        insertPrefetchCtx->filterResult = (uint8_t)INDEX_FILTER_UNSATISFIED;
    }
    return ret;
}

StatusInter ChLablePrefetch4UpdateHc(ChLabelRunCtxT *runCtx, IndexCtxT *idxCtx, ChTupleT *tuple,
    ChLogicRowId updateRowId, ChLabelUpdateCtxT *updateCtx, HtHashCodeT *hashCode, bool *needUpdate)
{
    IndexKeyT idxKeyOld, idxKeyNew;
    StatusInter ret = ClusteredHashGetOldAndNewIndexKey(runCtx, idxCtx, tuple, updateCtx, &idxKeyOld, &idxKeyNew);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    if (!RefreshChLabelUpdateCtx(updateCtx, idxCtx, &idxKeyOld, &idxKeyNew)) {
        *needUpdate = false;
        return STATUS_OK_INTER;
    }
    ChLabelPageCtxT lastPageCtx = runCtx->pageCtx;
    ret = ChLablePrefetchOldKey4UpdateHc(runCtx, idxCtx, tuple, updateRowId, updateCtx, &idxKeyOld, hashCode);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        runCtx->pageCtx = lastPageCtx;
        return ret;
    }
    ret = ChLablePrefetchNewKey4UpdateHc(runCtx, idxCtx, tuple, updateRowId, updateCtx, &idxKeyNew);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        runCtx->pageCtx = lastPageCtx;
        return ret;
    }
    *needUpdate = true;
    runCtx->pageCtx = lastPageCtx;
    return STATUS_OK_INTER;
}

ALWAYS_INLINE_C static void ChLabelHcIndexDeleteWithPrefetch(
    ChLabelRunCtxT *runCtx, IndexCtxT *idxCtx, ChLogicRowId logicRowId, HtHashCodeT hashCode)
{
    DB_POINTER2(runCtx, idxCtx);
#ifndef NDEBUG
    uint8_t *tmpLockPage = runCtx->lockPage;
    runCtx->lockPage = NULL;
#endif
    HcDeletePrefetchCtxT *delPrefetchCtx = &idxCtx->hcPrefetchCtx.hcDeletePrefetchCtx;
    if (delPrefetchCtx->filterResult != (uint8_t)INDEX_FILTER_UNSATISFIED) {
        DB_ASSERT(!idxCtx->idxOpenCfg.indexLabel->isKvLabel);
        uint32_t hcIndex = idxCtx->idxOpenCfg.vlIndexLabel->hcIndexId;
        HpHcDeleteInfoT deleteInfo = {
            .isListHeadDeleted = false, .isListEmpty = false, .newHeadTupleAddr = HEAP_INVALID_ADDR, .preserved = 0u};
        ChLabelHcIndexListDeleteWithPrefetch(runCtx, delPrefetchCtx, hcIndex, &deleteInfo);
        ChLabelHcSetHeader(&deleteInfo, idxCtx, hashCode, logicRowId);
    }
    delPrefetchCtx->isPrefetch = false;
#ifndef NDEBUG
    runCtx->lockPage = tmpLockPage;
#endif
}

ALWAYS_INLINE_C static void ChLabelHcIndexInsertWithPrefetch(ChLabelRunCtxT *runCtx, IndexCtxT *idxCtx)
{
    DB_POINTER2(runCtx, idxCtx);
    HcInsertPrefetchCtxT *insertPrefetchCtx = &idxCtx->hcPrefetchCtx.hcInsertPrefetchCtx;
    if (insertPrefetchCtx->filterResult == (uint8_t)INDEX_FILTER_UNSATISFIED) {
        insertPrefetchCtx->isPrefetch = false;
        return;
    }
#ifndef NDEBUG
    uint8_t *tmpLockPage = runCtx->lockPage;
    runCtx->lockPage = NULL;
#endif
    DB_ASSERT(!idxCtx->idxOpenCfg.indexLabel->isKvLabel);
    uint32_t hcIndex = idxCtx->idxOpenCfg.vlIndexLabel->hcIndexId;
    HtContainerT *container = HcCastIdxAsHashCluster(idxCtx->idxHandle);
    uint8_t *bucketContainer = HcGetBucketContainer(idxCtx);
    uint8_t *bucketBitmap = HcGetBucketBitmap(idxCtx);
    HpTupleAddr oldHeadAddr = HcGetBucketAddr(container, insertPrefetchCtx->bucketIndex, bucketContainer, bucketBitmap);
    HcSetBucketAddr(
        container, insertPrefetchCtx->bucketIndex, insertPrefetchCtx->insertTupleAddr, bucketContainer, bucketBitmap);
    if (oldHeadAddr != HEAP_INVALID_ADDR) {
        ChLabelHcIndexListInsertWithPrefetch(runCtx, insertPrefetchCtx, hcIndex, oldHeadAddr);
    }
    insertPrefetchCtx->isPrefetch = false;
#ifndef NDEBUG
    runCtx->lockPage = tmpLockPage;
#endif
}

StatusInter ClusteredEntryNormalUpdateHc(
    ChLabelRunCtxT *runCtx, ChRowHeadT *rowHead, ChLabelUpdateCtxT *updateCtx, ChLabelLookupCtxT *lookUpCtx)
{
    ChTupleT tuple;
    ClusteredHashPrepareTuple(runCtx, rowHead, &tuple, false);
    IndexCtxT **idxCtx = runCtx->openCfg.secIdxCtx;
    uint32_t secIndexNum = runCtx->openCfg.secIndexNum;
    DB_ASSERT(secIndexNum <= DM_MAX_KEY_COUNT);
    bool needUpdate[DM_MAX_KEY_COUNT];
    HtHashCodeT hashCode[DM_MAX_KEY_COUNT];
    ChLogicRowId logicRowId = lookUpCtx->logicRowId;
    updateCtx->hcCacheValid = false;
    for (uint32_t i = 0; i < secIndexNum; ++i) {
        updateCtx->isEqualHcKey[i] = false;
        if (idxCtx[i]->idxMetaCfg.idxType != HASHCLUSTER_INDEX) {
            needUpdate[i] = false;
            continue;
        }
        StatusInter ret =
            ChLablePrefetch4UpdateHc(runCtx, idxCtx[i], &tuple, logicRowId, updateCtx, &hashCode[i], &needUpdate[i]);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            HcInitHcPrefetchCtxArr(idxCtx, i);
            return ret;
        }
    }

    for (uint32_t i = 0; i < secIndexNum; ++i) {
        if (needUpdate[i]) {
            ChLabelPageCtxT lastPageCtx = runCtx->pageCtx;
            ChLabelHcIndexDeleteWithPrefetch(runCtx, idxCtx[i], logicRowId, hashCode[i]);
            // 若未缩容，逻辑addr必定与物理addr相等
            DB_ASSERT(runCtx->labelVarInfo->hasScaledIn || runCtx->labelVarInfo->upgradeVersion != 0 ||
                      (logicRowId.hashCode == lookUpCtx->phyRowId.hashCode &&
                          logicRowId.tupleId == lookUpCtx->phyRowId.tupleId));
            ChLabelHcIndexInsertWithPrefetch(runCtx, idxCtx[i]);
            runCtx->pageCtx = lastPageCtx;
        }
    }
    return STATUS_OK_INTER;
}
#else  // ARM32
StatusInter ChLabelHcIndexDelete(
    ChLabelRunCtxT *runCtx, ChLogicRowId logicRowId, IndexCtxT *iterIdxCtx, const IndexKeyT *idxKeyOld)
{
    DB_POINTER(runCtx);
#ifndef NDEBUG
    uint8_t *tmpLockPage = runCtx->lockPage;
    runCtx->lockPage = NULL;
#endif
    bool isSatisfied;
    StatusInter ret = ChLabelCheckHcIndexKeyBuf(iterIdxCtx, idxKeyOld, &isSatisfied);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, check hc index old key unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    if (!isSatisfied) {
#ifndef NDEBUG
        runCtx->lockPage = tmpLockPage;
#endif
        return STATUS_OK_INTER;
    }

    HpHcDeleteInfoT deleteInfo = {
        .isListHeadDeleted = false, .isListEmpty = false, .newHeadTupleAddr = HEAP_INVALID_ADDR, .preserved = 0u};
    DB_ASSERT(!iterIdxCtx->idxOpenCfg.indexLabel->isKvLabel);
    ret = ChLabelHcIndexListDelete(runCtx, logicRowId, iterIdxCtx->idxOpenCfg.vlIndexLabel->hcIndexId, &deleteInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, delete hc index unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    HtHashCodeT hashCode = HtGetHashCode(*idxKeyOld);
    ChLabelHcSetHeader(&deleteInfo, iterIdxCtx, hashCode, logicRowId);
#ifndef NDEBUG
    runCtx->lockPage = tmpLockPage;
#endif
    return ret;
}

StatusInter ChLabelHcIndexInsert(
    ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, IndexCtxT *iterIdxCtx, const IndexKeyT *idxKeyNew)
{
    DB_POINTER4(runCtx, lookUpCtx, iterIdxCtx, idxKeyNew);
    // 由上层保证，插入流程，物理addr不可能为无效值
    DB_ASSERT(TransformChPhyRowId(lookUpCtx->phyRowId) != HEAP_INVALID_ADDR);
#ifndef NDEBUG
    uint8_t *tmpLockPage = runCtx->lockPage;
    runCtx->lockPage = NULL;
#endif
    bool isSatisfied;
    StatusInter ret = ChLabelCheckHcIndexKeyBuf(iterIdxCtx, idxKeyNew, &isSatisfied);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "clustered hash, check hc index new key unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    if (!isSatisfied) {
#ifndef NDEBUG
        runCtx->lockPage = tmpLockPage;
#endif
        return STATUS_OK_INTER;
    }

    HtHashCodeT hashCode = HtGetHashCode(*idxKeyNew);
    HpTupleAddr oldHeadPtr = HcIndexGetBucketHeadAddr(iterIdxCtx, hashCode);
    HpTupleAddr addr = TransformChLogicRowId(lookUpCtx->logicRowId);
    HcIndexSetBucketHeadAddr(iterIdxCtx, hashCode, addr);

    uint8_t *tupleHdr = ClusteredHashTupleHdr(&runCtx->pageCtx, lookUpCtx->isCheckOverFlowPages);
    uint32_t tupleId = ClusteredHashGetInnerTupleId(runCtx, lookUpCtx->phyRowId.tupleId);
    ChIndexHcPtrT *hcPtr =
        (ChIndexHcPtrT *)(void *)ClusteredHashGetTupleBySlot(tupleHdr, runCtx->pageCtx.pageMetaData.tupleSize, tupleId);
    DB_ASSERT(!iterIdxCtx->idxOpenCfg.indexLabel->isKvLabel);
    uint32_t hcIndex = iterIdxCtx->idxOpenCfg.vlIndexLabel->hcIndexId;
    ChIndexHcPtrT *targetHcPtr = (hcPtr + hcIndex);
    *(HpTupleAddr *)(void *)&targetHcPtr->prevPtr = HEAP_INVALID_ADDR;
    targetHcPtr->nextPtr = TransformTupleAddr2LogicRowId(oldHeadPtr);

    if (oldHeadPtr == HEAP_INVALID_ADDR) {
#ifndef NDEBUG
        runCtx->lockPage = tmpLockPage;
#endif
        return STATUS_OK_INTER;
    }
    ret = ClusteredHcIndexLinkOldHeaderSingle(runCtx, lookUpCtx->logicRowId, oldHeadPtr, hcIndex);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        HcIndexSetBucketHeadAddr(iterIdxCtx, hashCode, oldHeadPtr);
    }

#ifndef NDEBUG
    runCtx->lockPage = tmpLockPage;
#endif
    return ret;
}

StatusInter ChLabelHcIndexUpdateSingle(ChLabelRunCtxT *runCtx, ChLabelLookupCtxT *lookUpCtx, IndexCtxT *iterIdxCtx,
    const IndexKeyT *idxKeyOld, const IndexKeyT *idxKeyNew)
{
    // 对于update流程，update hc = delete hc + insert hc
    ChLabelPageCtxT lastPageCtx = runCtx->pageCtx;
    StatusInter ret = ChLabelHcIndexDelete(runCtx, lookUpCtx->logicRowId, iterIdxCtx, idxKeyOld);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        runCtx->pageCtx = lastPageCtx;
        return ret;
    }

    // 若未缩容，逻辑addr必定与物理addr相等
    DB_ASSERT(runCtx->labelVarInfo->hasScaledIn || runCtx->labelVarInfo->upgradeVersion != 0 ||
              (lookUpCtx->logicRowId.hashCode == lookUpCtx->phyRowId.hashCode &&
                  lookUpCtx->logicRowId.tupleId == lookUpCtx->phyRowId.tupleId));

    runCtx->pageCtx = lastPageCtx;
    ret = ChLabelHcIndexInsert(runCtx, lookUpCtx, iterIdxCtx, idxKeyNew);
    runCtx->pageCtx = lastPageCtx;
    return ret;
}

StatusInter ClusteredEntryNormalUpdateHc(
    ChLabelRunCtxT *runCtx, ChRowHeadT *rowHead, ChLabelUpdateCtxT *updateCtx, ChLabelLookupCtxT *lookUpCtx)
{
    ChTupleT tuple;
    ClusteredHashPrepareTuple(runCtx, rowHead, &tuple, false);
    IndexCtxT **idxCtx = runCtx->openCfg.secIdxCtx;
    IndexKeyT idxKeyOld;
    IndexKeyT idxKeyNew;
    updateCtx->hcCacheValid = false;
    uint32_t secIndexNum = runCtx->openCfg.secIndexNum;
    if (SECUREC_UNLIKELY(secIndexNum > DM_MAX_KEY_COUNT)) {
        SE_ERROR(PROGRAM_LIMIT_EXCEEDED_INTER,
            "too much secondary index, count: %" PRIu32 ", labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->openCfg.secIndexNum, runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return PROGRAM_LIMIT_EXCEEDED_INTER;
    }
    for (uint32_t i = 0; i < secIndexNum; ++i) {
        IndexCtxT *iterIdxCtx = idxCtx[i];
        updateCtx->isEqualHcKey[i] = false;
        if (iterIdxCtx->idxMetaCfg.idxType != HASHCLUSTER_INDEX) {
            continue;
        }
        // 获取旧key
        StatusInter ret = ClusteredHashGetIndexKey(runCtx->openCfg.vertex, iterIdxCtx->idxMetaCfg.indexId,
            (DmIndexKeyBufInfoT **)&iterIdxCtx->idxOpenCfg.keyInfo, tuple.buf, &idxKeyOld);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, get old index key unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
        // 获取新key
        ret = ClusteredHashGetIndexKeyOnCmpMem(runCtx->openCfg.vertex, iterIdxCtx->idxMetaCfg.indexId,
            (DmIndexKeyBufInfoT **)&iterIdxCtx->idxOpenCfg.keyInfo, updateCtx->buf, &idxKeyNew);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "clustered hash, get new index key unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
                runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
        if (!RefreshChLabelUpdateCtx(updateCtx, iterIdxCtx, &idxKeyOld, &idxKeyNew)) {
            continue;
        }
        ret = ChLabelHcIndexUpdateSingle(runCtx, lookUpCtx, iterIdxCtx, &idxKeyOld, &idxKeyNew);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_LAST_ERROR(ret,
                "clustered hash update hc index unsucc, rowId(%" PRIu32 ", %" PRIu32 "), labelId: %" PRIu32
                ", indexId: %" PRIu32,
                lookUpCtx->phyRowId.hashCode, lookUpCtx->phyRowId.tupleId, runCtx->chLabel->labelCfg.labelId,
                runCtx->chLabel->labelCfg.indexId);
            return ret;
        }
    }
    return STATUS_OK_INTER;
}
#endif  // ARM32

StatusInter ClusteredHashBucketEraseHcIndex(
    ChLabelRunCtxT *runCtx, ChLogicRowId logicRowId, ChRowHeadT *rowHead, ChLabelPageCtxT *pageCtxCache)
{
    ChTupleT oldTuple;
    ClusteredHashPrepareTuple(runCtx, rowHead, &oldTuple, false);
    StatusInter ret = ChLabelHcIndexMergeDelete(runCtx, oldTuple.buf, logicRowId, NULL);
    // 由于删除hashcluster索引会刷新pageCtx，因此需要重新初始化一遍
    runCtx->pageCtx = *pageCtxCache;
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        ClusteredHashReleaseTuple(runCtx, &oldTuple);
        SE_ERROR(ret, "clustered hash, merge delete HcIndex unsucc, labelId: %" PRIu32 ", indexId: %" PRIu32,
            runCtx->chLabel->labelCfg.labelId, runCtx->chLabel->labelCfg.indexId);
        return ret;
    }
    ClusteredHashReleaseTuple(runCtx, &oldTuple);
    return STATUS_OK_INTER;
}

void ChLabelHcRecoveryHcPtr(ChLabelRunCtxT *runCtx, ChRowHeadT *rowHead)
{
    DB_POINTER2(runCtx, rowHead);
    // warm reboot恢复流程刷新hc指针，重建索引时再赋值
    DmVertexLabelT *vertexLabel = (DmVertexLabelT *)runCtx->openCfg.vertexLabel;
    uint32_t hcIndexNum = vertexLabel->metaVertexLabel->hcIndexNum;
    uint32_t slotExtendSize = runCtx->chLabel->labelCfg.slotExtendSize;
    ChIndexHcPtrT *hcPtr = (ChIndexHcPtrT *)(void *)((uintptr_t)rowHead - slotExtendSize);
    for (uint32_t i = 0; i < hcIndexNum; i++) {
        ChIndexHcPtrT *tagetHcPtr = (hcPtr + i);
        *(HpTupleAddr *)(void *)&tagetHcPtr->prevPtr = HEAP_INVALID_ADDR;
        *(HpTupleAddr *)(void *)&tagetHcPtr->nextPtr = HEAP_INVALID_ADDR;
    }
}

#ifdef __cplusplus
}
#endif
