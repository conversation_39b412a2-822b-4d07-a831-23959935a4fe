/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: 执行dml操作对存储层的访问方法
 * Author: panghaisheng
 * Create: 2021-03-25
 */

#include "se_heap_access_dm.h"
#include "se_heap.h"
#include "se_index.h"
#include "dm_data_prop.h"
#include "dm_yang_interface.h"
#include "dm_meta_kv_label.h"
#include "dm_data_kv.h"
#include "dm_data_keybuf_opti.h"
#ifdef FEATURE_SQL
#include "dm_data_index_sql.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif

Status HeapAmVertexSerial(const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl, HeapTupleBufT *serialBuf)
{
    DB_UNUSED(heapRunHdl);
    DmVertexT *vertex = deSrlObjHdl.handle;  // 序列化后的内存是在vertex中, 随着vertex的生命周期结束时 释放;
    return DmSerializeVertex(vertex, (uint8_t **)&serialBuf->buf, &serialBuf->bufSize);
}

void HeapAmVertexFreeSerialBuf(const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl, HeapTupleBufT *serialBuf)
{
    DB_UNUSED(heapRunHdl);
    DB_UNUSED(deSrlObjHdl);
    // 序列化后的内存是在vertex中(vertexSeriBuf), 随着vertex的生命周期结束时 释放;
    serialBuf->buf = NULL;
}

Status HeapAmVertexDeSerial(const HpRunHdlT heapRunHdl, HeapConstBufT *serialBuf, HeapDeSrlObjHdlT *deSrlObjHdl)
{
    DmVertexLabelT *dmLabel = HeapAmGetDmInfo(heapRunHdl);
    void *usrMemCtx = HeapAmGetUsrMemCtx(heapRunHdl);
    Status ret = GMERR_OK;
    ret = DmCreateEmptyVertexWithMemCtx(usrMemCtx, dmLabel, (DmVertexT **)deSrlObjHdl);
    if (ret == GMERR_OK && serialBuf->constBuf != NULL) {
        ret = DmDeSerialize2ExistsVertex(
            (uint8_t *)serialBuf->constBuf, serialBuf->bufSize, *(DmVertexT **)deSrlObjHdl, DmGetCheckMode());
    }
    return ret;
}

Status HeapAmVertexFreeDeSerialObj(const HpRunHdlT heapRunHdl, HeapDeSrlObjHdlT deSrlObjHdl)
{
    DB_UNUSED(heapRunHdl);
    DmVertexT *vertex = deSrlObjHdl.handle;
    DmDestroyVertex(vertex);
    return GMERR_OK;
}

#ifdef FEATURE_SQL
#ifndef IDS_HAOTIAN
Status ExprIdxGetKeyBufFromVertexBuf(
    IndexCtxT *indexCtx, DmVlIndexLabelT *indexLabel, HeapConstBufT *serialBuf, HeapTupleBufT *buf)
{
    DmVertexT *vertex = indexCtx->idxOpenCfg.vertex;
    Status ret = DmDeSerialize2ExistsVertex(serialBuf->constBuf, serialBuf->bufSize, vertex, DmGetCheckMode());
    if (ret != GMERR_OK) {
        SE_LAST_ERROR(ret, "unable to deSerialize, labelname = %s.", vertex->vertexDesc->labelName);
        return ret;
    }

    DmValueT resValues[DM_MAX_KEY_PROPE_NUM];
    uint32_t valNum;
    uint32_t memSize = indexLabel->nullInfoBytes;
    void *exprs = indexLabel->exprIdxInfo->exprs;
    ret = indexCtx->idxOpenCfg.callbackFunc.exprValuesEval(exprs, vertex, resValues, &valNum, &memSize);
    if (ret != GMERR_OK) {
        SE_LAST_ERROR(ret, "unable to invoke expr eval.");
        return ret;
    }

    buf->buf = vertex->vertexKeyBuf;
    buf->bufSize = memSize;
    IndexKeyPropeParaT para = {.values = resValues, .propeNum = valNum, .fixedPropeNum = 0};
    uint32_t bufLen = 0;
    ret = DmIndexKeyBufSeriFromExprValues(indexLabel, &para, &buf->buf, &bufLen);
    if (ret != GMERR_OK) {
        SE_LAST_ERROR(ret, "unable to serrialize expr eval result.");
        return ret;
    }
    DB_ASSERT(memSize == bufLen);
    return GMERR_OK;
}
#endif
#endif

Status HeapAmVertexExtraKeyBySerialBuf(const HpRunHdlT heapRunHdl, HeapDeSrlObjHdlT emptyObjHdl,
    HeapConstBufT *serialBuf, uint32_t indexId, HeapTupleBufT *buf)
{
    DB_UNUSED(heapRunHdl);
    if (buf->buf == NULL) {
        // vertex表反序列化需要传入有效的buf
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    IndexCtxT *indexCtx = (IndexCtxT *)(emptyObjHdl.handle);
    DmVertexT *vertex = (DmVertexT *)indexCtx->idxOpenCfg.vertex;
    DmVertexLabelT *vertexLabel = (DmVertexLabelT *)indexCtx->idxOpenCfg.vertexLabel;
    DmVlIndexLabelT *indexLabel = NULL;
    Status ret = DmGetIndexLabelByIndex(vertexLabel, indexId, &indexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
#ifdef FEATURE_SQL
#ifndef IDS_HAOTIAN
    if (indexLabel->isExprIdx) {
        return ExprIdxGetKeyBufFromVertexBuf(indexCtx, indexLabel, serialBuf, buf);
    }
#endif
#endif
    DmIndexKeyBufInfoT *indexKeyInfo = NULL;
    ret = DmVertexGetIndexKeyInfo(vertex, indexId, (DmIndexKeyBufInfoT **)&indexKeyInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmGetKeyBufFromVertexBuf(indexKeyInfo, (uint8_t *)serialBuf->constBuf, (uint8_t *)buf->buf, &buf->bufSize);
    return GMERR_OK;
}

Status HeapAmVertexExtraListUniqueKeyBySerialBuf(const HpRunHdlT heapRunHdl, HeapDeSrlObjHdlT emptyObjHdl,
    HeapConstBufT *serialBuf, uint32_t indexId, HeapTupleBufT *buf)
{
    if (buf->buf == NULL) {
        // vertex表反序列化需要传入有效的buf
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    IndexCtxT *indexCtx = (IndexCtxT *)(emptyObjHdl.handle);
    DmVertexLabelT *vertexLabel = (DmVertexLabelT *)indexCtx->idxOpenCfg.vertexLabel;
    DmVlIndexLabelT *indexLabel = NULL;
    Status ret = DmGetIndexLabelByIndex(vertexLabel, indexId, &indexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
#ifdef FEATURE_SQL
#ifndef IDS_HAOTIAN
    if (indexLabel->isExprIdx) {
        return ExprIdxGetKeyBufFromVertexBuf(indexCtx, indexLabel, serialBuf, buf);
    }
#endif
#endif
    DmVertexT *vertex = NULL;
    ret = DmDeSerializeVertexWithMemCtx(
        HeapAmGetUsrMemCtx(heapRunHdl), (uint8_t *)serialBuf->constBuf, serialBuf->bufSize, vertexLabel, &vertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmIndexKeyBufInfoT *indexKeyInfo = NULL;
    ret = DmVertexGetIndexKeyInfo(vertex, indexId, (DmIndexKeyBufInfoT **)&indexKeyInfo);
    if (ret != GMERR_OK) {
        DmDestroyVertex(vertex);
        return ret;
    }
    DmGetListUniqueKeyBufFromVertexWithKeyInfo(vertex, indexKeyInfo, (uint8_t *)buf->buf, &buf->bufSize);
    DmDestroyVertex(vertex);
    return GMERR_OK;
}

void HeapAmVertexGetCmpKeyBuf(const HpRunHdlT heapRunHdl, HeapDeSrlObjHdlT emptyObjHdl, HeapTupleBufT *buf)
{
    DB_UNUSED(heapRunHdl);
    DmVertexT *vertex = emptyObjHdl.handle;
    DmVertexGetCompareKeyBuf(vertex, &buf->buf);
}

Status HeapAmVertexExtraKeyByVertex(const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl, uint32_t indexId,
    uint8_t **keyBuf, uint32_t *length)
{
    DB_UNUSED(heapRunHdl);
    return DmGetKeyBufFromVertex((DmVertexT *)deSrlObjHdl.handle, indexId, keyBuf, length);
}

Status HeapAmVertexExtraListUniqueKeyByVertex(const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl,
    uint32_t indexId, uint8_t **keyBuf, uint32_t *length)
{
    DB_UNUSED(heapRunHdl);
    return DmGetListUniqueKeyBufFromVertex((DmVertexT *)deSrlObjHdl.handle, indexId, keyBuf, length);
}

void HeapAmVertexGetDmDetailInfo(const HpRunHdlT heapRunHdl, HeapDmDetailT *dmDetail)
{
    DB_UNUSED(heapRunHdl);
    DmVertexLabelT *dmLabel = HeapAmGetDmInfo(heapRunHdl);
    dmDetail->heapShmAddr = dmLabel->commonInfo->heapInfo.heapShmAddr;
    dmDetail->dmInfo = dmLabel;
    dmDetail->dmUuid = dmLabel->metaCommon.metaId;
    dmDetail->labelType = VERTEX_LABEL;
}

inline static void HeapAmVertexInitDmIndexInfo(HeapDmIndexIterT *dmKeyInfo, DmVlIndexLabelT *indexLabel)
{
    DB_POINTER2(dmKeyInfo, indexLabel);

    dmKeyInfo->isGet = true;
    dmKeyInfo->dmIndex = indexLabel;
    dmKeyInfo->dmUuid = indexLabel->idxLabelBase.indexId;
#ifdef SYS32
    (void)memcpy_s(&(dmKeyInfo->idxShmAddr), sizeof(indexLabel->idxLabelBase.shmAddr),
        &(indexLabel->idxLabelBase.shmAddr), sizeof(indexLabel->idxLabelBase.shmAddr));
#else
    dmKeyInfo->idxShmAddr = indexLabel->idxLabelBase.shmAddr;
#endif
    dmKeyInfo->indexType = (uint32_t)indexLabel->idxLabelBase.indexType;
    dmKeyInfo->isUniq = indexLabel->idxLabelBase.indexConstraint != NON_UNIQUE;
    dmKeyInfo->needCheckIndexSatisfied = indexLabel->indexFilter.conditionNum != 0;
}

void HeapAmVertexGetDmPriKeyInfo(const HpRunHdlT heapRunHdl, HeapDmIndexIterT *dmPriKeyInfo)
{
    DmVertexLabelT *label = HeapAmGetDmInfo(heapRunHdl);
    dmPriKeyInfo->iterId = 0;
    if (label->metaVertexLabel->pkIndexOffset != 0) {
        HeapAmVertexInitDmIndexInfo(dmPriKeyInfo, MEMBER_PTR(label->metaVertexLabel, pkIndex));
    } else {
        dmPriKeyInfo->isGet = false;
    }
}

void HeapGetDmNextSndKey(HeapDmIndexIterT *dmSndKeyInfo, DmVertexLabelT *label)
{
    uint32_t iterId = dmSndKeyInfo->iterId;
    if (!dmSndKeyInfo->isIterInited) {
        dmSndKeyInfo->isIterInited = true;
        iterId = 0;
    } else {
        iterId++;
    }
    if (iterId >= label->metaVertexLabel->secIndexNum) {
        dmSndKeyInfo->isGet = false;
        return;
    }
    dmSndKeyInfo->iterId = iterId;
    DmVlIndexLabelT *secIndexes = MEMBER_PTR(label->metaVertexLabel, secIndexes);
    HeapAmVertexInitDmIndexInfo(dmSndKeyInfo, &secIndexes[iterId]);
}

void HeapAmVertexGetDmNextSndKeyFun(const HpRunHdlT heapRunHdl, HeapDmIndexIterT *dmSndKeyInfo)
{
    DmVertexLabelT *label = HeapAmGetDmInfo(heapRunHdl);
    HeapGetDmNextSndKey(dmSndKeyInfo, label);
}

Status HeapAmVertexIsKeyPropeIncludeNull(const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl,
    uint32_t iterId, const HeapTupleBufT *tupleBuf, bool *isKeyPropeIncludeNull)
{
    DB_POINTER(isKeyPropeIncludeNull);
    DmVertexLabelT *label = HeapAmGetDmInfo(heapRunHdl);
    DB_ASSERT(iterId < label->metaVertexLabel->secIndexNum);
    DmVlIndexLabelT *secIndexes = &label->metaVertexLabel->secIndexes[iterId];
    if (secIndexes->isCheckNullProp) {
        DmIndexKeyBufInfoT *indexKeyInfo;
        Status ret =
            DmVertexGetIndexKeyInfo((DmVertexT *)deSrlObjHdl.handle, secIndexes->idxLabelBase.indexId, &indexKeyInfo);
        if (ret != GMERR_OK) {
            return ret;
        }
        *isKeyPropeIncludeNull =
            !DmVertexBufIsAllSetKeyBufProp(indexKeyInfo, ((DmVertexT *)deSrlObjHdl.handle)->vertexDesc, tupleBuf->buf);
        return GMERR_OK;
    }
    *isKeyPropeIncludeNull = false;
    return GMERR_OK;
}

void HeapAmVertexInitDmIndexInfoByIdx(
    const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT emptyObjHdl, HeapDmIndexIterT *dmKeyInfo, uint32_t *labelId)
{
    DmVlIndexLabelT *label = (DmVlIndexLabelT *)emptyObjHdl.handle;
    dmKeyInfo->dmUuid = label->idxLabelBase.indexId;
    dmKeyInfo->dmIndex = emptyObjHdl.handle;
    dmKeyInfo->idxShmAddr = label->idxLabelBase.shmAddr;
    dmKeyInfo->indexType = (uint32_t)label->idxLabelBase.indexType;
    dmKeyInfo->isGet = true;
    dmKeyInfo->isUniq = label->idxLabelBase.indexConstraint != NON_UNIQUE;
    DmVertexLabelT *dmLabel = HeapAmGetDmInfo(heapRunHdl);
    *labelId = dmLabel->metaCommon.metaId;
}

Status HeapAmKvSerial(const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl, HeapTupleBufT *serialBuf)
{
    DB_UNUSED(heapRunHdl);
    DmKvT *kv = deSrlObjHdl.handle;
    return DmSerializeKv(&kv->base, (uint8_t **)&serialBuf->buf, &serialBuf->bufSize);
}

void HeapAmKvFreeSerialBuf(const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl, HeapTupleBufT *serialBuf)
{
    DB_UNUSED(heapRunHdl);
    DmKvT *kv = deSrlObjHdl.handle;
    DbDynMemCtxFree(kv->base.memCtx, serialBuf->buf);
    serialBuf->buf = NULL;
}

Status HeapAmKvDeSerial(const HpRunHdlT heapRunHdl, HeapConstBufT *serialBuf, HeapDeSrlObjHdlT *deSrlObjHdl)
{
    deSrlObjHdl->handle = NULL;
    void *usrMemCtx = HeapAmGetUsrMemCtx(heapRunHdl);
    DmKvLabelT *kvTable = HeapAmGetDmInfo(heapRunHdl);

    HeapTupleBufT keyBuf = {0};
    HeapTupleBufT valueBuf = {0};
    DmGetKeyFromKvBuf((uint8_t *)serialBuf->constBuf, (uint8_t **)&keyBuf.buf, &keyBuf.bufSize);
    DmGetValueFromKvBuf(
        (uint8_t *)serialBuf->constBuf, serialBuf->bufSize, (uint8_t **)&valueBuf.buf, &valueBuf.bufSize);

    DmObjectT *obj = NULL;
    // obj申请的内存，释放在ctx销毁或者函数 DmDestroyKv 中
    Status ret = DmCreateEmptyKv(usrMemCtx, kvTable, &obj);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DmSetKey2ExistKv(obj, keyBuf.buf, keyBuf.bufSize);
    if (ret != GMERR_OK) {
        DmDestroyKv(obj);
        return ret;
    }

    ret = DmSetValue2ExistKv(obj, valueBuf.buf, valueBuf.bufSize);
    if (ret != GMERR_OK) {
        DmDestroyKv(obj);
        return ret;
    }
    deSrlObjHdl->handle = obj;
    return GMERR_OK;
}

Status HeapAmKvFreeDeSerialObj(const HpRunHdlT heapRunHdl, HeapDeSrlObjHdlT deSrlObjHdl)
{
    DB_UNUSED(heapRunHdl);
    DmDestroyKv(deSrlObjHdl.handle);
    deSrlObjHdl.handle = NULL;
    return GMERR_OK;
}

Status HeapAmKvExtraKeyByKvBuf(const HpRunHdlT heapRunHdl, HeapDeSrlObjHdlT emptyObjHdl, HeapConstBufT *serialBuf,
    uint32_t indexId, HeapTupleBufT *buf)
{
    DB_UNUSED(heapRunHdl);
    DB_UNUSED(emptyObjHdl);
    DB_UNUSED(indexId);
    DmGetKeyFromKvBuf((uint8_t *)serialBuf->constBuf, (uint8_t **)&buf->buf, &buf->bufSize);
    return GMERR_OK;
}

void HeapAmKvGetCmpKeyBuf(const HpRunHdlT heapRunHdl, HeapDeSrlObjHdlT emptyObjHdl, HeapTupleBufT *buf)
{
    DB_UNUSED(heapRunHdl);
    DB_UNUSED(emptyObjHdl);
    DB_ASSERT(false);  // 这个钩子函数是用于二级索引key比较时调用，kv表没有二级索引，防止误用加上此assert
}

Status HeapAmKvExtraKeyByKvObj(const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl, uint32_t indexId,
    uint8_t **keyBuf, uint32_t *length)
{
    DB_UNUSED(heapRunHdl);
    DB_UNUSED(indexId);
    DmKvT *kvObj = deSrlObjHdl.handle;
    *keyBuf = kvObj->keyBuf;
    *length = kvObj->keyLen;
    return GMERR_OK;
}

void HeapAmKvGetDmDetailInfo(const HpRunHdlT heapRunHdl, HeapDmDetailT *dmDetail)
{
    DmKvLabelT *kvTable = (DmKvLabelT *)HeapAmGetDmInfo(heapRunHdl);
    dmDetail->heapShmAddr = kvTable->heapShmAddr;
    dmDetail->dmInfo = kvTable;
    dmDetail->dmUuid = kvTable->metaCommon.metaId;
    dmDetail->labelType = KV_TABLE;
}

void HeapAmKvGetDmPriKeyInfo(const HpRunHdlT heapRunHdl, HeapDmIndexIterT *dmPriKeyInfo)
{
    DmKvLabelT *label = HeapAmGetDmInfo(heapRunHdl);
    dmPriKeyInfo->iterId = 0;
    dmPriKeyInfo->dmUuid = label->index->idxLabelBase.indexId;
    dmPriKeyInfo->dmIndex = label->index;
#if defined(SYS32BITS) && !defined(NDEBUG)
    (void)memcpy_s(
        &dmPriKeyInfo->idxShmAddr, sizeof(ShmemPtrT), &label->index->idxLabelBase.shmAddr, sizeof(ShmemPtrT));
#else
    dmPriKeyInfo->idxShmAddr = label->index->idxLabelBase.shmAddr;
#endif
    dmPriKeyInfo->indexType = (uint32_t)label->index->idxLabelBase.indexType;
    dmPriKeyInfo->isIterInited = true;
    dmPriKeyInfo->isGet = true;
    dmPriKeyInfo->isUniq = true;
    dmPriKeyInfo->needCheckIndexSatisfied = false;
}

void HeapAmKvGetDmNextSndKeyFun(const HpRunHdlT heapRunHdl, HeapDmIndexIterT *dmSndKeyInfo)
{
    // Kv table doesn't have second key.
    DB_UNUSED(heapRunHdl);
    dmSndKeyInfo->isIterInited = true;
    dmSndKeyInfo->isGet = false;
}

Status HeapAmKvIsKeyPropeIncludeNull(const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT deSrlObjHdl, uint32_t iterId,
    const HeapTupleBufT *tupleBuf, bool *isKeyPropeIncludeNull)
{
    DB_POINTER(isKeyPropeIncludeNull);
    // kv表没有二级索引，直接返回false即可
    *isKeyPropeIncludeNull = false;
    return GMERR_OK;
}

void HeapAmKvInitDmIndexInfoByIdx(
    const HpRunHdlT heapRunHdl, const HeapDeSrlObjHdlT emptyObjHdl, HeapDmIndexIterT *dmKeyInfo, uint32_t *labelId)
{
    DB_UNUSED(emptyObjHdl);
    DmBaseLabelT *label = HeapAmGetDmInfo(heapRunHdl);
    DmKvLabelT *kvTable = (DmKvLabelT *)(void *)label;
    dmKeyInfo->iterId = 0;
    dmKeyInfo->dmUuid = kvTable->index->idxLabelBase.indexId;
    dmKeyInfo->dmIndex = kvTable->index;
    dmKeyInfo->idxShmAddr = kvTable->index->idxLabelBase.shmAddr;
    dmKeyInfo->indexType = (uint32_t)kvTable->index->idxLabelBase.indexType;
    dmKeyInfo->isIterInited = true;
    dmKeyInfo->isGet = true;
    dmKeyInfo->isUniq = true;
    *labelId = label->metaId;
}

// heap容器针对vertexlabel类型的表与数据buf序列化和反序列化有关钩子函数的结合
// 在编码阶段赋值, 不涉及并发初始化，初始化后只读，不涉及并发读写
const HeapAmForDmT g_gmdbHeapAmForVertex = {
    HeapAmVertexSerial,
    HeapAmVertexFreeSerialBuf,
    HeapAmVertexDeSerial,
    HeapAmVertexFreeDeSerialObj,
    HeapAmVertexExtraKeyBySerialBuf,
    HeapAmVertexExtraListUniqueKeyBySerialBuf,
    HeapAmVertexGetCmpKeyBuf,
    HeapAmVertexExtraKeyByVertex,
    HeapAmVertexExtraListUniqueKeyByVertex,
    HeapAmVertexGetDmDetailInfo,
    HeapAmVertexGetDmPriKeyInfo,
    HeapAmVertexGetDmNextSndKeyFun,
    HeapAmVertexIsKeyPropeIncludeNull,
    HeapAmVertexInitDmIndexInfoByIdx,
};

// heap容器针对kv table类型的表与数据buf序列化和反序列化有关钩子函数的结合
// 在编码阶段赋值, 不涉及并发初始化，初始化后只读，不涉及并发读写
const HeapAmForDmT g_gmdbHeapAmForKv = {
    HeapAmKvSerial,
    HeapAmKvFreeSerialBuf,
    HeapAmKvDeSerial,
    HeapAmKvFreeDeSerialObj,
    HeapAmKvExtraKeyByKvBuf,
    HeapAmKvExtraKeyByKvBuf,
    HeapAmKvGetCmpKeyBuf,
    HeapAmKvExtraKeyByKvObj,
    HeapAmKvExtraKeyByKvObj,
    HeapAmKvGetDmDetailInfo,
    HeapAmKvGetDmPriKeyInfo,
    HeapAmKvGetDmNextSndKeyFun,
    HeapAmKvIsKeyPropeIncludeNull,
    HeapAmKvInitDmIndexInfoByIdx,
};

#ifdef __cplusplus
}
#endif
