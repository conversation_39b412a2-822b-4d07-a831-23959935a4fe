/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: Heap 模块公共的函数
 * Author: yuanjincheng
 * Create: 2021-10-13
 */

#ifndef SE_HEAP_BASE_H
#define SE_HEAP_BASE_H

#include "se_heap.h"
#include "se_lfsmgr.h"
#include "se_lock.h"
#include "se_page_latch_sorter.h"
#include "se_trx_base.h"
#include "db_label_latch_mgr.h"
#include "se_page.h"
#include "container_access.h"
#include "se_persistcap_heap.h"

#ifdef __cplusplus
extern "C" {
#endif
#define HEAP_BYTE_LENGTH 8u

#define ALIGN_SIZE_TWO 2u  // 2字节对齐.

#define CALC_ALIGN_SIZE(size, alignSize) (((size) + ((alignSize)-1)) & (~((alignSize)-1)))
#define HEAP_CALC_ALIGN_SIZE(size) CALC_ALIGN_SIZE(size, HEAP_DEFAULT_ALIGN_SIZE)
#define HEAP_ALIGN_SIZE_OF(type) HEAP_CALC_ALIGN_SIZE(sizeof(type))
#define HEAP_ASSERT_ALIGNED(size) DB_ASSERT((size) == HEAP_CALC_ALIGN_SIZE(size))

#define HEAP_INVALID_CURSOR_NUM 0x1fffffff

#define PAGE_SIZE_INVALID 0xffffu
#define PAGE_GET_ROW_REAL_OFFSET(logicOffset) (uint16_t)((logicOffset) << PAGE_SIZE_T_RESERVE)
#define PAGE_GET_ROW_LOGIC_OFFSET(realOffSet) \
    (uint16_t)((uint16_t){(uint16_t)((realOffSet) >> PAGE_SIZE_T_RESERVE)} & 0x7FFF)
#define PAGE_ROW_INVALID_LOGIC_OFFSET 0  // 最大是: PAGE_GET_ROW_LOGIC_OFFSET(PAGE_SIZE_INVALID)

#define HEAP_PAGE_FSM_RESERVED_SIZE(pageSize) ((pageSize) / 64u)  // 当前一个页典型大小为32k, 一个记录大小为100-300字节

#define HEAP_FIX_PAGE_BITMAP_BIT_LEN 32
#define HEAP_FIX_PAGE_BITMAP_BIT_LEN_TWO_POWER 5

#define HEAP_PAGE_HOLD_RESERVE 0

typedef struct {
    DbSpinLockT lock;
    bool isInit;
    uint8_t reserved[3];
    HeapAccessCfgT cfg;
    ShmemPtrT lockStatShmPtr;
    ShmemPtrT heapShmAddr;
} HeapJumpT;

typedef struct TagHeapRuntimeInfo {
    DbSpinLockT scanCursorLock;    // 控制 totalCursorNum 变量的并发
    DbSpinLockT heapLazyInitLock;  // 原HeapT中lock，控制perfStatShmPtr并发初始化的锁
    uint32_t totalCursorNum;       // 处理后台碎片整理线程与业务扫描线程的互斥
    uint32_t allocCursorFailTimes;
    uint32_t cacheVersion;
    bool cachePageAvail;  // 缓存页可用
    bool cursorOpenerInit[HEAP_SCAN_CURSOR_OPENER_TOP_3];
    ScanCursorOpenerT cursorOpener[HEAP_SCAN_CURSOR_OPENER_TOP_3];
    // 缓存上一次申请的页addr信息，pageAddr和pageHead需配套使用，客户端不能直接使用pageHead，需根据pageAddr重新获取页
    PageAddrInfoT cachePageAddr;
    PageTotalSizeT cachePageFreeSize;  // 缓存的页上还有多少的可用空间
    PageSizeT cachePageMaxRowSize;     // 记录缓存页上的最大行大小，归还FSM需要用
    uint8_t rsmMigrationVersion;
    uint8_t reserve;
    // 如果有DbAtomicAdd64访问，需要8字节对齐
    uint64_t historyLinkRows;  // 记录累计出现过的跳转行的数量，STORAGE_HEAP_STAT视图查询时返回
} HeapRuntimeInfoT;
DB_CHECK_ALIGN_ATOMIC64_MEMBER(struct TagHeapRuntimeInfo, historyLinkRows);

#ifdef FEATURE_PERSISTENCE
typedef struct TagHeapMgrMemFieldsT {
    ShmemPtrT heapPtr;
    ShmemPtrT runtimeInfoShmPtr;
    ShmemPtrT perfStatShmPtr;
    ShmemPtrT lockStatShmPtr;
    LfsMgrMemFieldT lfsMem;
} HeapMgrMemFieldsT;
#endif

typedef enum EnumHeapPageRowType {
    HEAP_FIX_ROW_NORMAL_ROW = 0,
    HEAP_FIX_ROW_LINK_SRC_ROW,
    HEAP_FIX_ROW_LINK_DST_ROW,
    HEAP_VAR_ROW_NORMAL_ROW,
    HEAP_VAR_ROW_LINK_SRC_ROW,
    HEAP_VAR_ROW_LINK_DST_ROW,
    HEAP_VAR_ROW_LINK_DST_SLICE_DIR_ROW,
    HEAP_VAR_ROW_SLICE_SUB_ROW,
    HEAP_INVALID_ROW_TYPE,
} HpPageRowTypeE;

static inline bool RowIsFixPageRow(HpPageRowTypeE rowType)
{
    return rowType < HEAP_VAR_ROW_NORMAL_ROW;
}

typedef union TagHeapRowHead {  // 本结构, 主要用于计算各个row类型最大的管理开销
    HpNormalFixRowHead normalFixRowHead;
    HpLinkSrcFixRowHead linkSrcFixRowHead;
    HpLinkDstFixRowHead linkDstFixRowHead;
    HpNormalRowHeadT normalRowHead;
    HpLinkRowHeadT linkRowHead;
    HpSliceSubRowHeadT sliceSubRowHead;
} HpRowHeadT;
#define HEAP_PAGE_ROW_MAX_MGR_LEN HEAP_ALIGN_SIZE_OF(HpRowHeadT)

typedef struct {
    /* 在heap流程中设置的，不用reset */
    HpTrxAmTypeE hpAmIndex;  // heapAmForTrxFun的下标
    bool isAddRowLockOnFetch;  // 表示是否需要在heap内部的fetch row前加锁; 加锁的模式为  rowLockModeOnFetch
    /* isAcquireLockByTryOnce 字段为了避免上层的latch和事务锁在多个事务见互相依赖导致死锁:
     * 索引访问heap的 row1 时, 一般是持有latch的, 如果此时加事务锁需要等待(其他事务持有该行的锁), 而持有 row1的事务
     * 可能需要访问索引 此时, 就构成循环依赖, 为了避免该场景, 当持有上层 latch 和 事务锁 有交织场景, 使用 try
     * 的方式获取事务锁 try失败时, 释放latch, 并重试 */
    bool isAcquireLockByTryOnce;  // 事务锁的加锁模式时可以 try模式
    bool isRowLockExist;  // 表示是否已经获取了事务锁:注意,修改isRowLockExist为true时, 也要设置isNeedReadView
    bool isNeedReadView;  // RR模式下使用，如果 false, 表示直接访问master version, 否则需要检查readView
    uint32_t rowLockModeOnFetch;  // fetch 流程, 对行加锁; 枚举 SeLockModeE: SE_LOCK_MODE_MAX 表示不用加锁
    uint32_t reserve2;
    /* HeapAllocRunctx后不会变的（一个连接只缓存一个表） */
    const HeapAmForTrxT *heapAmForTrxFun;
} HeapTrxControlT;

typedef struct {
    bool isLiteTrx;  // 并发模式：事务锁模式，轻量级事务模式
    bool isSingleWriter;  // 表示Heap并发模式是否_总是_只有一个写者。比如轻量级事务，无论什么操作都是一个写者
} HeapConcurrencyInfoT;

inline static HeapTrxControlT EmptyHeapTrxControl(void)
{
    return (HeapTrxControlT){.hpAmIndex = HEAP_TRX_AM_MAX_TYPE,
        .isAddRowLockOnFetch = false,
        .isAcquireLockByTryOnce = false,
        .isRowLockExist = false,
        .isNeedReadView = false,
        .rowLockModeOnFetch = 0,
        .reserve2 = 0,
        .heapAmForTrxFun = 0};
}

typedef struct {
    /* HeapAllocRunctx后不会变的（一个连接只缓存一个表） */
    ConcurrencyControlE ccType;  // 并发控制的类型
    HpAccessMethodE accessMethodType;
    bool isSkipUndoIndex;  // 在yang场景中，若此标志位为true则更新边addr时不记录undo日志(有条件)，事务提交/回滚
                           // 不会去处理索引，但要处理dfx(dm做了边压缩，bufSize会改变)
    bool isToGetOldestVisibleBuf;    // yang场景getDiff使用(与canAccessSelfDeleted不会同时为true来使用),
                                     // 获取本条记录对本事务的最老可见版本
    bool isCanAccessSelfDeletedBuf;  // yang场景getDiff使用, 可以读取本事务自己删除的版本
    bool isReadLatestCommittedBuf;   // 在RR事务下，为了感知并发冲突，需要读取最新已提交数据
    bool isBackGround;
    bool isFixPage;                // 是否是定长page场景
    bool isNeedRowLock;            // 是否需要加事务行锁
    bool isLabelLockSerializable;  // 串行化隔离级别下是否需要加表锁
    /* 在heap流程中设置的，不用reset */
    bool isPageReadOnly;  // 访问heap的page, 是否只读
    // isRsmRecovery为true表示是保留内存的恢复(包含回滚/提交)流程，
    // 恢复扫描流程需要重置页latch、trxId、系统字段等，需要skip rsmUndo的生成
    // 如果事务回滚/提交，可能会重复执行(异常前执行一次，恢复后执行一次)，通过该标志位skip一些校验、rsmUndo记录等
    bool isRsmRecovery;
    bool isNeedMarkDelete;  // V1兼容 是否需要标记删除 表锁+乐观RR，需要heap标记删除
    HeapTrxControlT trxContrl;
} HeapControlT;

typedef struct TagHeapRollBackInfoT {
    TrxIdT oldTrxId;      // 用于回滚场景, 保存row设置上个版本的事务id
    uint64_t oldRollPtr;  // 用于回滚场景, 保存row设置上个版本的 undo roll ptr
    bool oldIsDeleted;    // 乐观事务下，用于回滚场景, 保存row设置上个版本的isDeleted
    bool reserve[7];
} HpRollBackInfo;

// 页缓存失效有两种模式：
// (1).不会归还给os的页（当前DB默认的配置），当heap删空一个页后，就直接归还这个页给memdata，这些页始终在DB这一层复用，
// 即使从缓存拿到这个页，也能通过trmId校验出来不属于本表。
// (2).会归还给os的（打开enableReleaseDevice配置），当heap删空一个页后，会把这个页挂在fsm的特殊一级链表上，等待后台回收任务触发，
// 在回收任务中会加DDL的表锁，然后释放这级链表上的所有页，然后清空缓存，将缓存版本号+1使得ctx级别缓存失效，最后再解表锁。
typedef struct {
    HpItemPointerT lastPageLogicPtr;    // 上次访问的page的逻辑addr (性能优化)
    void *lastPageHeadPtr;              // 上次访问的addr的page addr(vir)
    HpItemPointerT lastRowLogicPtr;     // 上次访问的行的逻辑addr(性能优化)
    HpItemPointerT hcLastPageLogicPtr;  // 以下四个为额外供hashcluster使用的缓存
    void *hcLastPageHeadPtr;
    void *hcFirstPageHeadPtr;
    ShmemPtrT hcLastPageShmAddr;
    HpItemPointerT hcLastRowLogicPtr;
    SeLockModeE lastRowLockMode;  // 缓存上次的加锁模式 (性能优化)
    uint32_t lastPageBlockId;
    uint32_t cacheVersion;
} HeapPageCacheT;

typedef struct StaticPageInfo {
    bool isUseCache;  // MainStoreVertexLookUpHashOpt场景为true
    uint8_t reserve;  // 用于占位
    uint16_t rowCnt;
    PageSizeT oneRowOffset;        // oneRowSize + slotExtendSize
    PageSizeT firstRowHeadOffset;  // rowBegin + slotExtendSize
} StaticPageInfoT;

typedef Status (*SimpleIdxUpd)(HpTupleAddr addr, void *ttreeIdxUpdCtx, void *ctx);
typedef struct HeapCompV1Info {
    void *realInsertPtr;  // insert操作写入的物理addr, 更新大对象时为fetch插入获取到的物理addr
    uint64_t rollptrUpdOrDel;  // Update操作新增Undo链的rollptr
    void *masterPointer;       // 主版本物理ptr
    void *undoReclaimPhyAddr;  // undo回收时数据在page的物理addr，大对象link src row，normal row为实际数据
    void *ttreeIdxUpdCtx;
    IndexCtxT **idxCtxArray;
    SimpleIdxUpd updFun;
    uint64_t rowIdOrrollPtr;       // TTree索引记录的压缩前64位逻辑addr
    uint64_t fetchRowIdOrRollptr;  // heapfetch读操作获取的数据逻辑addr
    bool isFetchByTTree;           // TTree通过大对象src row head Fetch时不校验可见性
    bool isDropTblCleanRec;        // 是否是删除表清理所有数据
} HeapCompV1InfoT;

typedef struct TagHeapOpCtxT {
    /* HeapAllocRunctx后不会变的（一个连接只缓存一个表） */
    ContainerTypeE containerType;
    SeRunCtxT *seRunCtx;
    HeapJumpT *heapJump;  // 小型化需求，非持久化下，新增heapJump
    PageMgrT *pageMgr;
    HeapConstInfoT heapCfg;
    StaticPageInfoT staticPageInfo;
    ShmemPtrT heapShmAddr;
    ShmemPtrT heapJumpShmAddr;
    HeapT *heap;
    ShmemPtrT runtimeInfoShmPtr;
    HeapRuntimeInfoT *runtimeInfo;
    ShmemPtrT perfStatShmPtr;
    HeapPerfStatT *perfStat;
    ShmemPtrT lockStatShmPtr;
    LockStatT *lockStat;
    union {
        FsmRunCtxT *fsmRunCtx;  // heap使用
        LfsMgrT *fsmMgr;        // heap_mem使用
    };
    HeapDmDetailT dmDetail;  // 保存一份dmDetail，加速HeapAmGetDmDetailInfo直接返回
    const HeapAmForDmT *heapAmForDmFun;
    void *usrMemCtx;
    /* 需要通过reset来复用的(事务生命周期内的) */
    HeapControlT hpControl;
    HpOpTypeE hpOperation;
    uint32_t cursorNum : 30;
    uint32_t enableCache : 1;
    uint32_t enableCacheHeap : 1;
    HeapTrxCtxT *heapTrxCtx;  // 当前事务对应的上下文 (事务内, 同一个heap多次打开, 均对应同一个 heapTrxCtx)
    HeapPageCacheT pageCache;
    SePgLatchSorterT pageSorter;
    HpRollBackInfo rollBackInfo;
    HeapCompV1InfoT compV1Info;  // Used by Compatible V1
#ifdef FEATURE_SQL
    DmIndexTypeE backUpdateIdxType;
#endif
    uint16_t *offset;
    bool isTryGetPage;
} HeapRunCtxT;

inline static HeapRunCtxT EmptyHeapRunCtxT(void)
{
    return (HeapRunCtxT){};
}

#ifdef __cplusplus
}
#endif

#endif  // SE_HEAP_UTILS_H
