/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: Implementation for label free space manager
 * Author: SE Team
 * Create: 2020-8-10
 */

#include "se_lfsmgr_inner.h"
#include "db_internal_error.h"
#include "db_inter_process_rwlatch.h"
#include "se_capacity_def_pub.h"
#include "se_log.h"
#include "db_crash_debug.h"
#include "se_heap_base.h"
#include "se_lfsmgr_redo.h"
#include "se_lfsmgr_redo_am.h"
#include "se_persist.h"
#include "se_redo.h"
#include "se_redo_inner.h"
#include "se_durable_memdata.h"
#include "db_timer.h"
#include "drt_instance.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define LFS_DEFAULT_SPILT_TIME 1000  // 1秒

_Static_assert((int32_t)FSM_LIST_COUNT == 8, "The number of lists must be consistent with FSM_LIST_COUNT");
_Static_assert((int32_t)FSM_LIST_COUNT == FSM_VAR_LIST_COUNT, "FSM_LIST_COUNT must be the same as FSM_VAR_LIST_COUNT");
_Static_assert(
    FSM_VAR_LIST_COUNT - 2 == (uint32_t)FSM_LIST_ID_MAX, "FSM_VAR_LIST_COUNT - 2 must equal FSM_LIST_ID_MAX");

typedef struct FsmFragmentPara {
    LfsMgrT *mgr;
    uint32_t fsmListIdx;
    int32_t fragListId;
} FsmFragmentParaT;

typedef struct FsmBlockAddr {
    uint32_t fsmPageIdx;
    uint32_t slotId;
} FsmBlockAddrT;

inline static bool IsSlotInFreeList(uint32_t listId)
{
    return listId == FSM_LIST_ID_MAX;
}

static StatusInter FsmLatchMember(FsmRunCtxT *fsmRunCtx, LfsMgrT *fsmMgr, bool write)
{
    if (!fsmRunCtx->enableLatch) {
        return STATUS_OK_INTER;
    }
    StatusInter ret = STATUS_OK_INTER;
    DbLatchT *pageLatch = (DbLatchT *)((uint8_t *)fsmMgr - fsmMgr->cfgInfo.mgrOffset);
    if (!fsmRunCtx->directAccess) {
        if (write) {
            ret = DbInterProcRWLatchW(pageLatch);
        } else {
            ret = DbInterProcRWLatchR(pageLatch);
        }
    } else {
        DbSessionCtxT *sessionCtx = &fsmRunCtx->seRunCtx->resSessionCtx;
        if (write) {
            DbRWSpinWLockWithSession(sessionCtx, pageLatch, (ShmemPtrT *)&fsmRunCtx->fsmMgrAddr, LATCH_ADDR_PAGEID);
        } else {
            DbRWSpinRLockWithSession(sessionCtx, pageLatch, (ShmemPtrT *)&fsmRunCtx->fsmMgrAddr, LATCH_ADDR_PAGEID);
        }
    }
    return ret;
}

static void FsmUnlatchMember(FsmRunCtxT *fsmRunCtx, LfsMgrT *fsmMgr, bool write)
{
    if (!fsmRunCtx->enableLatch) {
        return;
    }
    DbLatchT *pageLatch = (DbLatchT *)((uint8_t *)fsmMgr - fsmMgr->cfgInfo.mgrOffset);
    if (!fsmRunCtx->directAccess) {
        if (write) {
            DbRWUnlatchW(pageLatch);
        } else {
            DbRWUnlatchR(pageLatch);
        }
    } else {
        DbSessionCtxT *sessionCtx = &fsmRunCtx->seRunCtx->resSessionCtx;
        if (write) {
            DbRWSpinWUnlockWithSession(sessionCtx, pageLatch);
        } else {
            DbRWSpinRUnlockWithSession(sessionCtx, pageLatch);
        }
    }
}

static StatusInter LfsGetSlotVirAddrByList(
    PageMgrT *pageMgr, LfsMgrT *mgr, FsmListT *fsmList, FsmListNodeT **slot, bool isWrite)
{
    DB_POINTER4(pageMgr, mgr, fsmList, slot);

    FsmSlotInfoT fsmSlotInfo = {.fsmPageIdx = fsmList->fsmPageIdx, .slotId = fsmList->slotId};
    StatusInter ret = LfsGetSlotByFsmPageIdxAndSlotId(pageMgr, mgr, fsmSlotInfo, slot, isWrite);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Get fsmSlot, fsmPageIdx:%" PRIu32 " slotId:%" PRIu32, fsmList->fsmPageIdx, fsmList->slotId);
    }

    return ret;
}

static StatusInter LfsGetLv1TablePageId(PageMgrT *pageMgr, const LfsMgrT *mgr, uint32_t fsmPageIdx, PageIdT *pageId)
{
    DB_POINTER2(pageMgr, mgr);
    DB_ASSERT(fsmPageIdx >= mgr->fsmPageIdNumsInMgr);  // 此处传入的fsmPageIdx一定合法
    // parse level2 fsmPage in fsmAddrInfo.fsmPageId list
    uint16_t lv2TablePageIdx = GetLv2TablePageIdx(mgr->fsmPageIdNumsInMgr, mgr->maxSlotPerFsmTablePage, fsmPageIdx);
    PageIdT lv2TablePageId = mgr->lv2TablePageId[lv2TablePageIdx];
    if (!DbIsPageIdValid(lv2TablePageId)) {
        SE_LAST_ERROR(INT_ERR_LFS_INVALID_FSM_PAGE_ID, "pageIdx:%" PRIu16 ", pageId(%" PRIu32 ",%" PRIu32 ").",
            lv2TablePageIdx, lv2TablePageId.deviceId, lv2TablePageId.blockId);
        return INT_ERR_LFS_INVALID_FSM_PAGE_ID;
    }
    // 1.get page from pageMgr
    FsmPageTableT *lv2FsmPageTable = NULL;
    StatusInter ret = SeGetPage(pageMgr, lv2TablePageId, (uint8_t **)&lv2FsmPageTable, ENTER_PAGE_NORMAL, false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Lv2Page(%" PRIu32 ",%" PRIu32 ") wrong", lv2TablePageId.deviceId, lv2TablePageId.blockId);
        return ret;
    }
    // 2.read offset from the page get from step1
    // calculate the index lv 1
    uint16_t lv2TableSlotId = GetLv2PageTableSlotId(mgr->fsmPageIdNumsInMgr, mgr->maxSlotPerFsmTablePage, fsmPageIdx);
    // 3.get the the lv1TablePageId
    PageIdT lv1TablePageId = lv2FsmPageTable->pageId[lv2TableSlotId];
    SeLeavePage(pageMgr, lv2TablePageId, false);
    if (!DbIsPageIdValid(lv1TablePageId)) {
        SE_LAST_ERROR(INT_ERR_LFS_INVALID_FSM_PAGE_ID, "pageIdx(%" PRIu32 "), pageId(%" PRIu32 ",%" PRIu32 ")",
            fsmPageIdx, lv1TablePageId.deviceId, lv1TablePageId.blockId);
        return INT_ERR_LFS_INVALID_FSM_PAGE_ID;
    }
    *pageId = lv1TablePageId;
    return STATUS_OK_INTER;
}

static StatusInter LfsGetFsmPageIdFromPageTable(
    PageMgrT *pageMgr, const LfsMgrT *mgr, uint32_t fsmPageIdx, PageIdT *pageId)
{
    DB_POINTER2(pageMgr, mgr);

    PageIdT lv1TablePageId;
    StatusInter ret = LfsGetLv1TablePageId(pageMgr, mgr, fsmPageIdx, &lv1TablePageId);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    FsmPageTableT *lv1FsmPageTable = NULL;
    ret = SeGetPage(pageMgr, lv1TablePageId, (uint8_t **)&lv1FsmPageTable, ENTER_PAGE_NORMAL, false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get Lv1Page(%" PRIu32 ",%" PRIu32 ")", lv1TablePageId.deviceId, lv1TablePageId.blockId);
        return ret;
    }
    uint16_t lv1TableSlotId = GetLv1PageTableSlotId(mgr->fsmPageIdNumsInMgr, mgr->maxSlotPerFsmTablePage, fsmPageIdx);
    PageIdT targetPageId = lv1FsmPageTable->pageId[lv1TableSlotId];
    SeLeavePage(pageMgr, lv1TablePageId, false);
    if (!DbIsPageIdValid(targetPageId)) {
        SE_ERROR(INT_ERR_LFS_INVALID_FSM_PAGE_ID, "tableSlot: %" PRIu16 ", pageId(%" PRIu32 ",%" PRIu32 ")",
            lv1TableSlotId, targetPageId.deviceId, targetPageId.blockId);
        return INT_ERR_LFS_INVALID_FSM_PAGE_ID;
    }
    *pageId = targetPageId;
    return STATUS_OK_INTER;
}

StatusInter LfsGetFsmPageId(PageMgrT *pageMgr, const LfsMgrT *mgr, uint32_t fsmPageIdx, PageIdT *pageId)
{
    DB_POINTER(mgr);

    if (fsmPageIdx >= mgr->allFsmPageNum) {
        SE_LAST_ERROR(INT_ERR_LFS_TOO_MANY_FSM_PAGE, "fsmPageIdx:(%" PRIu32 ")", fsmPageIdx);
        DB_ASSERT(false);
        return INT_ERR_LFS_TOO_MANY_FSM_PAGE;
    }

    if (fsmPageIdx >= mgr->fsmPageIdNumsInMgr) {
        return LfsGetFsmPageIdFromPageTable(pageMgr, mgr, fsmPageIdx, pageId);
    }
    if (!DbIsPageIdValid(mgr->fsmPageIds[fsmPageIdx])) {
        SE_ERROR(INT_ERR_LFS_INVALID_FSM_PAGE_ID, "fsmPageIdx:(%" PRIu32 ")", fsmPageIdx);
        return INT_ERR_LFS_INVALID_FSM_PAGE_ID;
    }
    *pageId = mgr->fsmPageIds[fsmPageIdx];
    return STATUS_OK_INTER;
}

void LfsLeavePageByValidFsmPageIdx(PageMgrT *pageMgr, const LfsMgrT *mgr, uint32_t fsmPageIdx, bool isChanged)
{
    DB_POINTER(pageMgr);
    if (fsmPageIdx != SLOT_IN_MGR_VIRTUAL_FSM_PAGE_IDX) {  // 只释放有效的fsm页
        PageIdT fsmPageId = {0};
        if (LfsGetFsmPageId(pageMgr, mgr, fsmPageIdx, &fsmPageId) == STATUS_OK_INTER) {
            SeLeavePage(pageMgr, fsmPageId, isChanged);
        }
    }
}

inline static StatusInter LfsGetFsmSlot(FsmPageHeadT *page, BlockNumber slotId, FsmListNodeT **slot)
{
    DB_POINTER2(page, slot);
    if (slotId >= page->fsmHead.usedSlotCnt) {
        SE_LAST_ERROR(INT_ERR_LFS_INVALID_BLOCK_ID, "slot not used (%" PRIu32 ", %" PRIu16 ")", slotId,
            page->fsmHead.usedSlotCnt);
        return INT_ERR_LFS_INVALID_BLOCK_ID;
    }
    *slot = &(page->fsmListNode[slotId]);
    return STATUS_OK_INTER;
}

inline static uint32_t LfsGetBlockIdByFsmPageInfo(uint32_t fsmPageIdx, uint32_t slotId, uint16_t maxSlotPerFsmPage)
{
    // fsmPage中的blockId需要计算扩展slot的偏移
    return (fsmPageIdx == SLOT_IN_MGR_VIRTUAL_FSM_PAGE_IDX) ?
               slotId :
               (slotId + fsmPageIdx * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR);
}

inline static uint32_t LfsGetFsmPageIdxByBlockId(uint32_t blockId, uint16_t maxSlotPerFsmPage)
{
    return (blockId < FSM_SLOT_NUM_IN_MGR) ? SLOT_IN_MGR_VIRTUAL_FSM_PAGE_IDX :
                                             ((blockId - FSM_SLOT_NUM_IN_MGR) / maxSlotPerFsmPage);
}

inline static uint32_t LfsGetSlotIdByBlockId(uint32_t blockId, uint16_t maxSlotPerFsmPage)
{
    return (blockId < FSM_SLOT_NUM_IN_MGR) ? blockId : ((blockId - FSM_SLOT_NUM_IN_MGR) % maxSlotPerFsmPage);
}

inline static void LfsSetBlockAddr(
    const LfsMgrT *mgr, uint32_t fsmPageIdx, uint32_t slotId, const FsmListNodeT *slot, PageAddrT *addr)
{
    DB_POINTER3(mgr, slot, addr);
    addr->blockId = LfsGetBlockIdByFsmPageInfo(fsmPageIdx, slotId, mgr->maxSlotPerFsmPage);
    addr->pageId = slot->dataPageId;
}

/* FsmPage struct: | PageBaseHead | FsmHead | FsmListNode | PageBaseTail | */
// 参数不加const会有warnning，加了const会有另外的warnning。但从函数语义上来说应该不用const
void LfsInitFsmPage(FsmPageHeadT *page, uint32_t fsmFileId, uint32_t fsmPageIdx, PageTotalSizeT pageSize)
{
    DB_POINTER(page);
    page->fsmHead.fsmCnt = (fsmPageIdx == 0) ? 1 : 0;
    page->fsmHead.fsmFileId = fsmFileId;
    page->fsmHead.fsmPageIdx = fsmPageIdx;
    page->fsmHead.pageSize = pageSize;
    page->fsmHead.maxSlotPerFsmPage = (pageSize - (uint32_t)sizeof(FsmPageHeadT)) / (uint32_t)sizeof(FsmListNodeT);
    page->fsmHead.usedSlotCnt = 0;
}

// 左闭右开[0, 第1级链表的size), [第1级链表的size, 第2级链表的size)......
int32_t LfsGetOwnerListId(const LfsMgrT *mgr, uint32_t availSize)
{
    DB_POINTER(mgr);
    for (int32_t i = (int32_t)FSM_LIST_ID_0; i < (int32_t)FSM_LIST_ID_MAX; i++) {
        if (availSize < mgr->listRange[i + 1]) {
            return i;
        }
    }
    return (int32_t)FSM_LIST_ID_MAX;
}

uint32_t LfsGetAllocListId(const LfsMgrT *mgr, uint32_t requireSize)
{
    DB_POINTER(mgr);

    int32_t id = LfsGetOwnerListId(mgr, requireSize);
    if (mgr->listRange[id] != requireSize) {
        // 找到这一级链表，但这一级链表上的页（除了requireSize正好等于链表范围的左闭区间的），其他不一定满足要求
        // eg:
        // 第1级链表范围为[10, 100)，这时来了一个requireSize为99的，虽然分配到第1级链表，但链表上的页不一定满足需求，
        // 剩余size为[10, 98]的页也放在这一级链表上。
        // 所以当mgr->listRange[id] != requireSize，id++去下一级链表去取，一定能满足需求
        DB_ASSERT(id < (int32_t)FSM_LIST_ID_MAX);
        id++;
    }

    return (uint32_t)id;
}

// 从共享addr缓存内取fsm页
static inline StatusInter LfsGetFsmPage(
    PageMgrT *pageMgr, const LfsMgrT *mgr, uint32_t fsmPageIdx, FsmPageHeadT **page, bool isWrite)
{
    DB_POINTER3(pageMgr, mgr, page);

    PageIdT pageId;
    StatusInter ret = LfsGetFsmPageId(pageMgr, mgr, fsmPageIdx, &pageId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get fsm page unsucc fsmPageIdx(%" PRIu32 ")", fsmPageIdx);
        return ret;
    }

    ret = SeGetPage(pageMgr, pageId, (uint8_t **)page, isWrite ? ENTER_PAGE_WRITE : ENTER_PAGE_NORMAL, isWrite);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get fsmPage, pageId(%" PRIu32 ",%" PRIu32 ")", pageId.deviceId, pageId.blockId);
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter LfsGetFreeSizeInFsmPage(LfsMgrT *mgr, PageMgrT *pageMgr, uint32_t fsmPageIdx, uint64_t *freeSize)
{
    DB_POINTER3(mgr, pageMgr, freeSize);

    FsmPageHeadT *fsmPageHead;
    StatusInter ret = LfsGetFsmPage(pageMgr, mgr, fsmPageIdx, &fsmPageHead, false);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    for (uint32_t i = 0; i < fsmPageHead->fsmHead.usedSlotCnt; i++) {
        if (fsmPageHead->fsmListNode[i].dataPageId != SE_ALLOC_NEW_PAGE_ID) {
            (*freeSize) += fsmPageHead->fsmListNode[i].freeSize;
        }
    }

    SeLeavePage(pageMgr, fsmPageHead->pageHead.addr, false);
    return STATUS_OK_INTER;
}

StatusInter LfsGetTotalFreeSize(FsmRunCtxT *fsmRunCtx, uint64_t *totalFreeSize)
{
    DB_POINTER2(fsmRunCtx, totalFreeSize);

    LfsMgrT *fsmMgr = NULL;
    StatusInter ret = FsmRunCtxGetMgr(fsmRunCtx, false, &fsmMgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Get mgr");
        return ret;
    }
    ret = FsmLatchMember(fsmRunCtx, fsmMgr, false);
    if (ret != STATUS_OK_INTER) {
        FsmRunCtxLeaveMgr(fsmRunCtx, false);
        return ret;
    }
    uint64_t freeSize = LfsGetAllSlotDataFreeSize(fsmMgr);

    for (uint32_t i = 0; i < fsmMgr->fsmPageCnt; i++) {
        ret = LfsGetFreeSizeInFsmPage(fsmMgr, fsmRunCtx->pageMgr, i, &freeSize);
        if (ret != STATUS_OK_INTER) {
            FsmUnlatchMember(fsmRunCtx, fsmMgr, false);
            FsmRunCtxLeaveMgr(fsmRunCtx, false);
            return ret;
        }
    }

    *totalFreeSize = freeSize;
    FsmUnlatchMember(fsmRunCtx, fsmMgr, false);
    FsmRunCtxLeaveMgr(fsmRunCtx, false);
    return STATUS_OK_INTER;
}

StatusInter LfsGetFsmPageByCache(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t fsmPageIdx, FsmPageHeadT **page, bool isWrite)
{
    DB_POINTER3(pageMgr, mgr, page);
    return LfsGetFsmPage(pageMgr, mgr, fsmPageIdx, page, isWrite);
}

StatusInter LfsAllocLv2FsmPageTable(PageMgrT *pageMgr, LfsMgrT *mgr, DbInstanceHdT dbInstance)
{
    DB_POINTER2(pageMgr, mgr);
    uint16_t lv2TablePageIdx =
        GetLv2TablePageIdx(mgr->fsmPageIdNumsInMgr, mgr->maxSlotPerFsmTablePage, mgr->fsmPageCnt);
    if (lv2TablePageIdx == mgr->lv2TableCnt - 1) {
        return STATUS_OK_INTER;
    }
    PageIdT newLv2TablePageId = {0};
    AllocPageParamT allocPageParam = SeInitAllocPageParam(mgr->cfgInfo.tableSpaceIndex, mgr->cfgInfo.fsmFileId,
        RSM_INVALID_LABEL_ID, dbInstance, NULL);  // fsm不使用rsmUndo
    StatusInter ret = SeAllocPage(pageMgr, &allocPageParam, &newLv2TablePageId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "fsmpage-alloc ");
        return ret;
    }

    mgr->lv2TablePageId[lv2TablePageIdx] = newLv2TablePageId;
    mgr->lv2TableCnt++;
    LfsRedoForAllocNewPageTable(mgr, LV2_FSM_PAGE_TABLE_ID, mgr->cfgInfo.mgrPageId, lv2TablePageIdx, newLv2TablePageId);
    return STATUS_OK_INTER;
}

StatusInter LfsAllocLv1FsmPageTable(PageMgrT *pageMgr, LfsMgrT *mgr, DbInstanceHdT dbInstance)
{
    DB_POINTER2(pageMgr, mgr);
    uint16_t lv2TablePageIdx =
        GetLv2TablePageIdx(mgr->fsmPageIdNumsInMgr, mgr->maxSlotPerFsmTablePage, mgr->fsmPageCnt);
    PageIdT lv2TablePageId = mgr->lv2TablePageId[lv2TablePageIdx];
    DB_ASSERT(DbIsPageIdValid(lv2TablePageId));  // 前面alloc出来的，不可能失败
    FsmPageTableT *lv2FsmPageTable = NULL;
    StatusInter ret = SeGetPage(pageMgr, lv2TablePageId, (uint8_t **)&lv2FsmPageTable, ENTER_PAGE_WRITE, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get Lv2Page(%" PRIu32 ",%" PRIu32 ")", lv2TablePageId.deviceId, lv2TablePageId.blockId);
        return ret;
    }

    uint16_t lv2TableSlotId =
        GetLv2PageTableSlotId(mgr->fsmPageIdNumsInMgr, mgr->maxSlotPerFsmTablePage, mgr->fsmPageCnt);
    if (lv2TablePageIdx * mgr->maxSlotPerFsmTablePage + lv2TableSlotId == mgr->lv1TableCnt - 1) {
        goto EXIT;  // 返回正常
    }
    PageIdT newLv1TablePageId = {0};
    AllocPageParamT allocPageParam = SeInitAllocPageParam(mgr->cfgInfo.tableSpaceIndex, mgr->cfgInfo.fsmFileId,
        RSM_INVALID_LABEL_ID, dbInstance, NULL);  // fsm不使用rsmUndo
    ret = SeAllocPage(pageMgr, &allocPageParam, &newLv1TablePageId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "fsmpage-alloc");
        goto EXIT;
    }

    lv2FsmPageTable->pageId[lv2TableSlotId] = newLv1TablePageId;
    SePageHeadSetType(&lv2FsmPageTable->pageHead, PERSISTENCE_PAGE_TYPE_FSM_PAGE_TABLE);
    mgr->lv1TableCnt++;
    LfsRedoForAllocNewPageTable(mgr, LV1_FSM_PAGE_TABLE_ID, lv2TablePageId, lv2TableSlotId, newLv1TablePageId);

EXIT:
    SeLeavePage(pageMgr, lv2TablePageId, ret == STATUS_OK_INTER);
    return ret;
}

StatusInter LfsSetFsmPageIdInPageTable(PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t fsmPageIdx, PageIdT targetPageId)
{
    DB_POINTER2(pageMgr, mgr);
    uint16_t lv2TablePageIdx = GetLv2TablePageIdx(mgr->fsmPageIdNumsInMgr, mgr->maxSlotPerFsmTablePage, fsmPageIdx);
    PageIdT lv2TablePageId = mgr->lv2TablePageId[lv2TablePageIdx];
    DB_ASSERT(DbIsPageIdValid(lv2TablePageId));  // 前面alloc出来的或已经使用过，不可能失败
    FsmPageTableT *lv2FsmPageTable = NULL;
    StatusInter ret = SeGetPage(pageMgr, lv2TablePageId, (uint8_t **)&lv2FsmPageTable, ENTER_PAGE_NORMAL, false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get Lv2Page(%" PRIu32 ",%" PRIu32 ")", lv2TablePageId.deviceId, lv2TablePageId.blockId);
        return ret;
    }

    uint16_t lv2TableSlotId = GetLv2PageTableSlotId(mgr->fsmPageIdNumsInMgr, mgr->maxSlotPerFsmTablePage, fsmPageIdx);
    PageIdT lv1TablePageId = lv2FsmPageTable->pageId[lv2TableSlotId];
    SeLeavePage(pageMgr, lv2TablePageId, false);
    DB_ASSERT(DbIsPageIdValid(lv1TablePageId));  // 前面alloc出来的或已经使用过，不可能失败
    FsmPageTableT *lv1FsmPageTable = NULL;
    ret = SeGetPage(pageMgr, lv1TablePageId, (uint8_t **)&lv1FsmPageTable, ENTER_PAGE_WRITE, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get Lv1Page(%" PRIu32 ",%" PRIu32 ")", lv1TablePageId.deviceId, lv1TablePageId.blockId);
        return ret;
    }
    uint16_t lv1TableSlotId = GetLv1PageTableSlotId(mgr->fsmPageIdNumsInMgr, mgr->maxSlotPerFsmTablePage, fsmPageIdx);
    lv1FsmPageTable->pageId[lv1TableSlotId] = targetPageId;
    SePageHeadSetType(&lv1FsmPageTable->pageHead, PERSISTENCE_PAGE_TYPE_FSM_PAGE_TABLE);
    LfsRedoModifyFsmPageTable(mgr, FSM_PAGE_ID_IN_PAGE_TABLE, lv1TablePageId, lv1TableSlotId, targetPageId);
    SeLeavePage(pageMgr, lv1TablePageId, ret == STATUS_OK_INTER);
    return ret;
}

StatusInter LfsSetFsmPageId(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t fsmPageIdx, PageIdT targetPageId, FsmPageHeadT *fsmPage)
{
    DB_POINTER2(pageMgr, mgr);

    if (fsmPageIdx < mgr->fsmPageIdNumsInMgr) {
        mgr->fsmPageIds[fsmPageIdx] = targetPageId;
        LfsRedoModifyFsmPageTable(mgr, FSM_PAGE_ID_IN_MGR, mgr->cfgInfo.mgrPageId, fsmPageIdx, targetPageId);
        return STATUS_OK_INTER;
    }

    return LfsSetFsmPageIdInPageTable(pageMgr, mgr, fsmPageIdx, targetPageId);
}

inline static bool LfsNeedAllocLv1AndLv2FsmPageTable(const LfsMgrT *mgr)
{
    if (mgr->fsmPageCnt < mgr->fsmPageIdNumsInMgr) {
        return false;
    }
    return GetLv1PageTableSlotId(mgr->fsmPageIdNumsInMgr, mgr->maxSlotPerFsmTablePage, mgr->fsmPageCnt) == 0u;
}

StatusInter LfsGetNewFsmPage(PageMgrT *pageMgr, LfsMgrT *mgr, FsmPageHeadT **page, DbInstanceHdT dbInstance)
{
    DB_POINTER3(pageMgr, mgr, page);
    PageIdT addr = SE_INVALID_PAGE_ADDR;
    AllocPageParamT allocPageParam = SeInitAllocPageParam(mgr->cfgInfo.tableSpaceIndex, mgr->cfgInfo.fsmFileId,
        RSM_INVALID_LABEL_ID, dbInstance, NULL);  // fsm不使用rsmUndo
    StatusInter ret = SeAllocPage(pageMgr, &allocPageParam, &addr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "alloc fsmPage");
        return ret;
    }

    ret = SeGetPage(pageMgr, addr, (uint8_t **)page, ENTER_PAGE_WRITE, true);
    if (ret != STATUS_OK_INTER) {
        FreePageParamT freePageParam =
            SeInitFreePageParam(mgr->cfgInfo.tableSpaceIndex, addr, dbInstance, NULL, SE_INVALID_LABEL_ID, false);
        (void)SeFreePage(pageMgr, &freePageParam);
        SE_ERROR(ret, "get fsmPage(%" PRIu32 ",%" PRIu32 ")", addr.deviceId, addr.blockId);
    }
    return ret;
}

StatusInter LfsStoreFsmPageId(PageMgrT *pageMgr, LfsMgrT *mgr, FsmPageHeadT *page, DbInstanceHdT dbInstance)
{
    DB_POINTER3(pageMgr, mgr, page);

    StatusInter ret = STATUS_OK_INTER;
    if (LfsNeedAllocLv1AndLv2FsmPageTable(mgr)) {
        // alloc level2 fsmPage and set lv2 fsmPage
        ret = LfsAllocLv2FsmPageTable(pageMgr, mgr, dbInstance);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "alloc lv2 fsmPage");
            return ret;
        }
        // alloc level1 fsmPage and set lv1 fsmPage
        ret = LfsAllocLv1FsmPageTable(pageMgr, mgr, dbInstance);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "alloc lv1 fsmPage");
            return ret;
        }
    }

    // 将新申请到的fsm page 放入 fsmPageIds
    ret = LfsSetFsmPageId(pageMgr, mgr, mgr->fsmPageCnt, page->pageHead.addr, page);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "set fsmPage, fsmPageCnt(%" PRIu32 "), page(%" PRIu32 ", %" PRIu32 ")", mgr->fsmPageCnt,
            page->pageHead.addr.deviceId, page->pageHead.addr.blockId);
    }
    return ret;
}

// upper page: Lfs所管理的上层模块的page
// for a new block, FSM need to MdGetMemPageById to get and record the pageId
StatusInter LfsGetNewUpperPage(
    PageMgrT *pageMgr, LfsMgrT *mgr, PageAddrT *pageAddr, FsmDataPageHeadT **dataPage, DbInstanceHdT dbInstance)
{
    DB_POINTER3(pageMgr, mgr, pageAddr);

    PageIdT newPageAddr = SE_INVALID_PAGE_ADDR;
    StatusInter ret = STATUS_OK_INTER;
    AllocPageParamT allocPageParam = SeInitAllocPageParam(mgr->cfgInfo.tableSpaceIndex, mgr->cfgInfo.dataFileId,
        RSM_INVALID_LABEL_ID, dbInstance, NULL);  // 持久化的fsm不使用rsmUndo
    ret = SeAllocPage(pageMgr, &allocPageParam, &newPageAddr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "alloc upper page ");
        return ret;
    }

    ret = SeGetPage(pageMgr, newPageAddr, (uint8_t **)dataPage, ENTER_PAGE_WRITE, true);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "upper page(%" PRIu32 ",%" PRIu32 ")", newPageAddr.deviceId, newPageAddr.blockId);
        FreePageParamT freePageParam = SeInitFreePageParam(
            mgr->cfgInfo.tableSpaceIndex, newPageAddr, dbInstance, NULL, SE_INVALID_LABEL_ID, false);
        (void)SeFreePage(pageMgr, &freePageParam);
        return ret;
    }

    pageAddr->pageId = SerializePageId(pageMgr, (*dataPage)->pageHead.addr);
    return STATUS_OK_INTER;
}

// upper page: Lfs所管理的上层模块的page
// 释放锁管理的上层页，保留对应的blockId
static StatusInter LfsReleaseUpperPage(PageMgrT *pageMgr, LfsMgrT *mgr, bool releasePage, FsmSlotParaT *slotPara)
{
    DB_POINTER3(pageMgr, mgr, slotPara);

    FsmListNodeT *slot = slotPara->slot;
    DB_ASSERT(slot->dataPageId != SE_ALLOC_NEW_PAGE_ID);

    if (releasePage) {
        PageAddrT addr;
        LfsSetBlockAddr(mgr, slotPara->fsmPageIdx, slotPara->slotId, slot, &addr);
        FreePageParamT freePageParam = SeInitFreePageParam(mgr->cfgInfo.tableSpaceIndex,
            DeserializePageId(pageMgr, addr.pageId), slotPara->dbInstance, NULL, SE_INVALID_LABEL_ID, false);
        StatusInter ret = SeFreePage(pageMgr, &freePageParam);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    if (mgr->lastUpperPageId == slot->dataPageId) {
        mgr->lastUpperPageAddr = NULL;
        mgr->lastUpperPageId = SE_INVALID_PAGE_ID;
    }
    slot->dataPageId = SE_ALLOC_NEW_PAGE_ID;
    mgr->releasedPageCnt++;
    if (IsSlotInFreeList(slot->listId)) {
        mgr->freePageCnt--;
    } else {
        mgr->usedPageCnt--;
    }
    return STATUS_OK_INTER;
}

StatusInter LfsReleaseUpperPageWithRedo(
    PageMgrT *pageMgr, PageIdT fsmAddr, uint32_t offset, uint32_t fsmPageIdx, uint32_t slotId)
{
    DB_POINTER(pageMgr);
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    FsmRedoLogBegin(redoCtx, (pageMgr->type != SE_MEMDATA));

    LfsMgrT *mgr = NULL;
    StatusInter ret = FsmGetMgr(pageMgr, fsmAddr, offset, true, &mgr);
    if (ret != STATUS_OK_INTER) {
        (void)FsmRedoLogEnd(redoCtx, pageMgr->type != SE_MEMDATA);
        SE_LAST_ERROR(ret, "get mgr (%" PRIu32 ", %" PRIu32 ").", fsmAddr.deviceId, fsmAddr.blockId);
        return ret;
    }

    FsmListNodeT *slot;
    FsmSlotInfoT fsmSlotInfo = {.fsmPageIdx = fsmPageIdx, .slotId = slotId};
    ret = LfsGetSlotByFsmPageIdxAndSlotId(pageMgr, mgr, fsmSlotInfo, &slot, true);
    if (ret != STATUS_OK_INTER) {
        FsmLeaveMgr(pageMgr, fsmAddr, false);
        (void)FsmRedoLogEnd(redoCtx, pageMgr->type != SE_MEMDATA);
        return ret;
    }

    if (slot->dataPageId == SE_ALLOC_NEW_PAGE_ID) {
        LfsLeavePageByValidFsmPageIdx(pageMgr, mgr, fsmPageIdx, false);
        FsmLeaveMgr(pageMgr, fsmAddr, false);
        ret = FsmRedoLogEnd(redoCtx, pageMgr->type != SE_MEMDATA);
    } else {
        FsmSlotParaT slotPara = {.slot = slot,
            .fsmPageIdx = fsmPageIdx,
            .slotId = slotId,
            .dbInstance = redoCtx ? redoCtx->redoMgr->seIns->dbInstance : NULL};
        ret = LfsReleaseUpperPage(pageMgr, mgr, true, &slotPara);
        if (ret != STATUS_OK_INTER) {
            LfsLeavePageByValidFsmPageIdx(pageMgr, mgr, fsmPageIdx, false);
            FsmLeaveMgr(pageMgr, fsmAddr, false);
            (void)FsmRedoLogEnd(redoCtx, pageMgr->type != SE_MEMDATA);
            return ret;
        }
        LfsRedoForReleaseUpperPage(pageMgr, mgr, fsmPageIdx, slotId, slot);
        LfsLeavePageByValidFsmPageIdx(pageMgr, mgr, fsmPageIdx, true);
        FsmLeaveMgr(pageMgr, fsmAddr, true);

        ret = FsmRedoLogEnd(redoCtx, pageMgr->type != SE_MEMDATA);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "redo end");
        }
    }
    return ret;
}

static void LfsUpdateInfo4GetNewUpperPage(
    LfsMgrT *mgr, uint32_t fsmPageIdx, uint32_t slotId, PageAddrT *addr, FsmDataPageHeadT *dataPage)
{
    DB_POINTER3(mgr, addr, dataPage);
    addr->blockId = LfsGetBlockIdByFsmPageInfo(fsmPageIdx, slotId, mgr->maxSlotPerFsmPage);
    dataPage->slotIdx = addr->blockId;
    mgr->lastUpperPageId = addr->pageId;
    mgr->lastUpperPageAddr = (uint8_t *)dataPage;
}

// upper page: Lfs所管理的上层模块的page
static StatusInter LfsReuseFreeUpperPage(
    PageMgrT *pageMgr, LfsMgrT *mgr, FsmSlotParaT *slotPara, PageAddrT *addr, DbInstanceHdT dbInstance)
{
    // 只有最高级链表才有free page
    bool inFreeList = IsSlotInFreeList(slotPara->slot->listId);
    DB_ASSERT(inFreeList);
    FsmDataPageHeadT *dataPage = NULL;
    StatusInter ret = LfsGetNewUpperPage(pageMgr, mgr, addr, &dataPage, dbInstance);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    LfsUpdateInfo4GetNewUpperPage(mgr, slotPara->fsmPageIdx, slotPara->slotId, addr, dataPage);
    DB_ASSERT(addr->pageId != SE_ALLOC_NEW_PAGE_ID);
    slotPara->slot->dataPageId = addr->pageId;
    mgr->releasedPageCnt--;
    mgr->freePageCnt++;
    return STATUS_OK_INTER;
}

// When LFS alloc a new FSM page, record the page desc id of new page
StatusInter LfsAllocNewFsmPage(PageMgrT *pageMgr, LfsMgrT *mgr, FsmPageHeadT **newFsmPage, DbInstanceHdT dbInstance)
{
    DB_POINTER2(pageMgr, mgr);
    if (mgr->fsmPageCnt >= mgr->allFsmPageNum) {
        // 目前可支持持久化前 mgr->allFsmPageNum 个fsm page
        SE_LAST_ERROR(INT_ERR_LFS_TOO_MANY_FSM_PAGE, "fsmPageCnt: %" PRIu32, mgr->fsmPageCnt);
        return INT_ERR_LFS_TOO_MANY_FSM_PAGE;
    }

    FsmPageHeadT *page = NULL;
    StatusInter ret = LfsGetNewFsmPage(pageMgr, mgr, &page, dbInstance);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    SePageHeadSetType(&page->pageHead, PERSISTENCE_PAGE_TYPE_FSM);
    if (newFsmPage != NULL) {
        *newFsmPage = page;
    }
    return STATUS_OK_INTER;
}

void LfsMgrInitMemVars(LfsMgrT *mgr)
{
    DB_POINTER(mgr);

    // 以下字段或缓存仅在memdata场景下有效，在持久化场景下无效
    mgr->lastUpperPageId = SE_INVALID_PAGE_ID;  // 上一次缓存的数据页序列化后的pageId
    mgr->lastUpperPageAddr = NULL;              // 上一次缓存的数据页虚拟addr
}

void LfsMgrResetMemVars(LfsMgrT *mgr, LfsMgrMemFieldT *memData)
{
    DB_POINTER(mgr);

    memData->lastUpperPageId = mgr->lastUpperPageId;
    memData->lastUpperPageAddr = mgr->lastUpperPageAddr;

    // 持久化时某些地方仍用到了，DB_ASSERT(mgr->lastUpperPageId == SE_INVALID_PAGE_ID);
    mgr->lastUpperPageId = SE_INVALID_PAGE_ID;
    mgr->lastUpperPageAddr = NULL;
}

void LfsMgrRestoreMemVars(LfsMgrT *mgr, LfsMgrMemFieldT *memData)
{
    DB_POINTER(mgr);

    mgr->lastUpperPageId = memData->lastUpperPageId;
    mgr->lastUpperPageAddr = memData->lastUpperPageAddr;
}

static StatusInter LfsTryGetSpecBlockByFsmSlot(
    const LfsMgrT *mgr, FsmListNodeT *slot, uint32_t fsmPageIdx, BlockNumber slotId, PageAddrT *pAddr)
{
    DB_POINTER(mgr);

    // 只要是在最高级链表上的页，都当做被释放了处理
    bool isFree = IsSlotInFreeList(slot->listId);
    if (isFree) {
        return INT_ERR_LFS_BLOCK_HAS_RELEASED;  // 不一定是错误场景，不打日志
    }

    if (pAddr) {
        DB_ASSERT(slot->dataPageId != SE_ALLOC_NEW_PAGE_ID);
        LfsSetBlockAddr(mgr, fsmPageIdx, slotId, slot, pAddr);
    }

    return STATUS_OK_INTER;
}

void LfsGetSlotInMgr(LfsMgrT *mgr, uint32_t slotId, FsmListNodeT **slot)
{
    DB_POINTER2(mgr, slot);
    DB_ASSERT(slotId < FSM_SLOT_NUM_IN_MGR);
    *slot = &mgr->slotArr[slotId];
}

static StatusInter LfsGetFsmSlotById(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t fsmPageIdx, uint32_t slotId, FsmListNodeT **slot, bool isWrite)
{
    DB_POINTER3(pageMgr, mgr, slot);
    if (fsmPageIdx == SLOT_IN_MGR_VIRTUAL_FSM_PAGE_IDX) {
        LfsGetSlotInMgr(mgr, slotId, slot);
        return STATUS_OK_INTER;
    }
    FsmPageHeadT *page;
    StatusInter ret = LfsGetFsmPageByCache(pageMgr, mgr, fsmPageIdx, &page, isWrite);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    ret = LfsGetFsmSlot(page, slotId, slot);
    if (ret != STATUS_OK_INTER) {
        SeLeavePage(pageMgr, page->pageHead.addr, false);
    }
    return ret;
}

// 清空slot里面的link信息
static void LfsResetSlot(FsmListNodeT *slot)
{
    slot->prevFsmPageIdx = LFS_INVALID_PAGE_IDX;
    slot->prevSlotId = LFS_INVALID_SLOT_ID;
    slot->nextFsmPageIdx = LFS_INVALID_PAGE_IDX;
    slot->nextSlotId = LFS_INVALID_SLOT_ID;
    slot->listId = LFS_INVALID_LIST_ID;
}

static inline void LfsUpdataPrevSlotInfo(FsmListNodeT *slot, uint32_t prevFsmPageIdx, uint32_t prevSlotId)
{
    DB_POINTER(slot);
    slot->prevFsmPageIdx = prevFsmPageIdx;
    slot->prevSlotId = prevSlotId;
}

static inline void LfsUpdataNextSlotInfo(FsmListNodeT *slot, uint32_t nextFsmPageIdx, uint32_t nextSlotId)
{
    DB_POINTER(slot);
    slot->nextFsmPageIdx = nextFsmPageIdx;
    slot->nextSlotId = nextSlotId;
}

static void LfsUnlinkFsmList(PageMgrT *pageMgr, LfsMgrT *mgr, FsmListNodeT *slot)
{
    DB_POINTER3(pageMgr, mgr, slot);

    FsmListT *fsmList = &mgr->fsmList[slot->listId];

    FsmListNodeT *nextSlot = NULL;
    // 先更新nextSlot，方便后面更新prevSlot

    if (slot->nextSlotId != LFS_INVALID_SLOT_ID) {
        // 不为空，使用后向指针获取nextSlot
        StatusInter ret = LfsGetFsmSlotById(pageMgr, mgr, slot->nextFsmPageIdx, slot->nextSlotId, &nextSlot, true);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "get nextSlot, nextPageId: %" PRIu32 ", nextSlotId: %" PRIu32, (uint32_t)slot->nextFsmPageIdx,
                (uint32_t)slot->nextSlotId);
            DB_ASSERT(false);  // 后向指针所指一定是存在的slot，否则fsm的并发控制有问题
            return;
        }
        // 更新一下nextSlot的前向
        LfsUpdataPrevSlotInfo(nextSlot, slot->prevFsmPageIdx, slot->prevSlotId);
        LfsLeavePageByValidFsmPageIdx(pageMgr, mgr, slot->nextFsmPageIdx, true);
    }

    if (slot->prevSlotId == LFS_INVALID_SLOT_ID) {
        // slot在链表头，需要更新链表头
        fsmList->fsmPageIdx = slot->nextFsmPageIdx;
        fsmList->slotId = slot->nextSlotId;
    } else {
        FsmListNodeT *prevSlot = NULL;
        // 不为空，使用前向指针更新prevSlot，这段逻辑和更新nextSlot应该相似，抽取成函数
        StatusInter ret = LfsGetFsmSlotById(pageMgr, mgr, slot->prevFsmPageIdx, slot->prevSlotId, &prevSlot, true);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "get prevSlot, prevPageId: %" PRIu32 ", prevSlotId: %" PRIu32, (uint32_t)slot->prevFsmPageIdx,
                (uint32_t)slot->prevSlotId);
            DB_ASSERT(false);  // 后向指针所指一定是存在的slot，否则fsm的并发控制有问题
            return;
        }
        // 更新一下nextSlot的前向
        LfsUpdataNextSlotInfo(prevSlot, slot->nextFsmPageIdx, slot->nextSlotId);
        LfsLeavePageByValidFsmPageIdx(pageMgr, mgr, slot->prevFsmPageIdx, true);
    }
    DB_ASSERT(fsmList->listLen > 0);
    fsmList->listLen--;

    if (IsSlotInFreeList(slot->listId)) {
        mgr->freePageCnt--;
        mgr->usedPageCnt++;
    }
    LfsResetSlot(slot);
}

static void LfsLinkFsmList(
    PageMgrT *pageMgr, LfsMgrT *mgr, FsmListNodeT *slot, uint32_t newListId, uint32_t fsmPageIdx, uint32_t slotId)
{
    DB_POINTER3(pageMgr, mgr, slot);
    // link到链表上，那肯定是原本不在链表上
    DB_ASSERT(slot->listId == LFS_INVALID_LIST_ID);
    DB_ASSERT(slot->prevSlotId == LFS_INVALID_SLOT_ID);
    DB_ASSERT(slot->nextSlotId == LFS_INVALID_SLOT_ID);

    FsmListT *fsmList = &mgr->fsmList[newListId];

    // 更新nextSlot
    FsmListNodeT *headSlot = NULL;

    if (fsmList->slotId != LFS_INVALID_SLOT_ID) {
        // 不为空，使用后向指针获取nextSlot
        StatusInter ret = LfsGetFsmSlotById(pageMgr, mgr, fsmList->fsmPageIdx, fsmList->slotId, &headSlot, true);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "get headSlot, pageId: %" PRIu32 ", slotId: %" PRIu32, fsmList->fsmPageIdx, fsmList->slotId);
            DB_ASSERT(false);  // 后向指针所指一定是存在的slot，否则fsm的并发控制有问题
            return;
        }
        // 更新一下nextSlot的前向
        DB_ASSERT(headSlot != NULL);
        headSlot->prevFsmPageIdx = fsmPageIdx;
        headSlot->prevSlotId = slotId & LFS_INVALID_SLOT_ID;
        LfsLeavePageByValidFsmPageIdx(pageMgr, mgr, fsmList->fsmPageIdx, true);
    }

    // 更新slot的link信息
    slot->nextFsmPageIdx = fsmList->fsmPageIdx;
    slot->nextSlotId = fsmList->slotId & LFS_INVALID_SLOT_ID;
    slot->listId = newListId & LFS_INVALID_LIST_ID;

    // 插入链表头部
    fsmList->fsmPageIdx = fsmPageIdx;
    fsmList->slotId = slotId;

    if (IsSlotInFreeList(slot->listId)) {
        DB_ASSERT(mgr->usedPageCnt > 0);
        mgr->usedPageCnt--;
        mgr->freePageCnt++;
    }
    fsmList->listLen++;
}

StatusInter LfsGetAddrByPageIdSlotId(PageMgrT *pageMgr, LfsMgrT *mgr, PageAddrT *addr, FsmListNodeT *slot,
    uint32_t fsmPageIdx, uint32_t slotId, DbInstanceHdT dbInstance)
{
    addr->pageId = slot->dataPageId;
    if (slot->dataPageId == SE_ALLOC_NEW_PAGE_ID) {
        FsmSlotParaT slotPara = {.slot = slot, .fsmPageIdx = fsmPageIdx, .slotId = slotId, .dbInstance = dbInstance};
        StatusInter ret = LfsReuseFreeUpperPage(pageMgr, mgr, &slotPara, addr, dbInstance);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter LfsGetNewFreeSlot(
    PageMgrT *pageMgr, LfsMgrT *mgr, PageAddrT *addr, FsmPageHeadT **page, DbInstanceHdT dbInstance)
{
    if (mgr->maxBlockCnt < FSM_SLOT_NUM_IN_MGR) {
        return STATUS_OK_INTER;
    }
    // 从fsm页中获取slot
    StatusInter ret;
    if (mgr->maxBlockCnt == mgr->maxSlotPerFsmPage * mgr->fsmPageCnt + FSM_SLOT_NUM_IN_MGR) {
        ret = LfsAllocNewFsmPage(pageMgr, mgr, page, dbInstance);
    } else {
        ret = LfsGetFsmPageByCache(pageMgr, mgr, mgr->fsmPageCnt - 1, page, true);
    }
    return ret;
}

static void LfsUpdateInfo4GetNewFreeSlot(
    LfsMgrT *mgr, PageAddrT *addr, uint32_t requireSize, FsmPageHeadT *fsmPage, FsmSlotParaT *slotPara)
{
    DB_POINTER3(mgr, addr, slotPara);
    if (mgr->maxBlockCnt < FSM_SLOT_NUM_IN_MGR) {  // 从mgr上获取slot, 已在mgr初始化流程中初始化
        slotPara->fsmPageIdx = SLOT_IN_MGR_VIRTUAL_FSM_PAGE_IDX;
        slotPara->slotId = mgr->maxBlockCnt;
        LfsGetSlotInMgr(mgr, mgr->maxBlockCnt, &slotPara->slot);
    } else {
        DB_POINTER(fsmPage);  // 在 fsmPage 需要使用的时候才需要判空
        // 初始化新申请的 fsm page
        if (mgr->maxBlockCnt == mgr->maxSlotPerFsmPage * mgr->fsmPageCnt + FSM_SLOT_NUM_IN_MGR) {
            LfsInitFsmPage(fsmPage, mgr->cfgInfo.fsmFileId, mgr->fsmPageCnt, mgr->cfgInfo.pageSize);
            mgr->lastFsmPageId = mgr->fsmPageCnt;
            mgr->fsmPageCnt++;
        }
        DB_ASSERT(mgr->maxBlockCnt < mgr->maxSlotPerFsmPage * mgr->fsmPageCnt + FSM_SLOT_NUM_IN_MGR);

        // 更新slot参数
        FsmHeadT *fsmHead = &fsmPage->fsmHead;
        FsmListNodeT *fsmListNode = fsmPage->fsmListNode;
        DB_ASSERT(fsmHead->fsmPageIdx == mgr->fsmPageCnt - 1);  // 扩展slot应该发生在FSM最后一页
        slotPara->slotId = fsmHead->usedSlotCnt;
        slotPara->fsmPageIdx = fsmHead->fsmPageIdx;
        slotPara->slot = &(fsmListNode[slotPara->slotId]);

        // 更新fsm page使用计数
        fsmPage->fsmHead.usedSlotCnt++;
    }

    // slot参数更新，freeSize初始为最大
    LfsInitSlot(slotPara->slot, addr->pageId, (uint16_t)mgr->cfgInfo.availSize);
    DB_ASSERT(slotPara->slot->freeSize >= requireSize);
    DB_ASSERT(slotPara->fsmPageIdx != LFS_INVALID_PAGE_IDX);  // 信息更新完后不能为设定非法值
    DB_ASSERT(slotPara->slotId != LFS_INVALID_SLOT_ID);       // 信息更新完后不能为设定非法值
}

static StatusInter LfsSaveFsmPageId(
    PageMgrT *pageMgr, LfsMgrT *mgr, PageAddrT *addr, FsmPageHeadT *page, DbInstanceHdT dbInstance)
{
    bool allocNewFsmPage = (mgr->maxBlockCnt == mgr->maxSlotPerFsmPage * mgr->fsmPageCnt + FSM_SLOT_NUM_IN_MGR);
    if (allocNewFsmPage) {
        StatusInter ret = LfsStoreFsmPageId(pageMgr, mgr, page, dbInstance);
        if (ret != STATUS_OK_INTER) {
            PageIdT fsmPageId = page->pageHead.addr;
            SeLeavePage(pageMgr, fsmPageId, false);
            uint32_t spaceId = mgr->cfgInfo.tableSpaceIndex;
            FreePageParamT freePageParamFsmPageId =
                SeInitFreePageParam(spaceId, fsmPageId, dbInstance, NULL, SE_INVALID_LABEL_ID, false);
            SeFreePage(pageMgr, &freePageParamFsmPageId);
            PageIdT dataPageId = DeserializePageId(pageMgr, addr->pageId);
            SeLeavePage(pageMgr, dataPageId, false);
            FreePageParamT freePageParamDataPageId =
                SeInitFreePageParam(spaceId, dataPageId, dbInstance, NULL, SE_INVALID_LABEL_ID, false);
            SeFreePage(pageMgr, &freePageParamDataPageId);
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter LfsExtentFsmSlot(
    PageMgrT *pageMgr, LfsMgrT *mgr, PageAddrT *addr, uint32_t requireSize, DbInstanceHdT dbInstance)
{
    DB_POINTER3(pageMgr, mgr, addr);

    // 1.找到一个空闲slot
    FsmSlotParaT slotPara = {
        .slot = NULL, .fsmPageIdx = LFS_INVALID_PAGE_IDX, .slotId = LFS_INVALID_SLOT_ID, .dbInstance = dbInstance};
    FsmPageHeadT *page = NULL;
    bool allocNewFsmPage = (mgr->maxBlockCnt == mgr->maxSlotPerFsmPage * mgr->fsmPageCnt + FSM_SLOT_NUM_IN_MGR);
    StatusInter ret = LfsGetNewFreeSlot(pageMgr, mgr, addr, &page, dbInstance);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    // 2.申请数据页
    FsmDataPageHeadT *dataPage = NULL;
    ret = LfsGetNewUpperPage(pageMgr, mgr, addr, &dataPage, dbInstance);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        if (page != NULL) {  // page不为空说明上面的流程中有get fsm page，异常分支需要leave page
            PageIdT fsmPageId = page->pageHead.addr;
            SeLeavePage(pageMgr, fsmPageId, false);
            if (allocNewFsmPage) {
                uint32_t spaceId = mgr->cfgInfo.tableSpaceIndex;
                FreePageParamT freePageParam =
                    SeInitFreePageParam(spaceId, fsmPageId, dbInstance, NULL, SE_INVALID_LABEL_ID, false);
                SeFreePage(pageMgr, &freePageParam);
            }
        }
        return ret;
    }

    // 3.存储fsm pageId
    ret = LfsSaveFsmPageId(pageMgr, mgr, addr, page, dbInstance);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    // 4.新slot信息更新 + 新数据页信息更新
    LfsUpdateInfo4GetNewFreeSlot(mgr, addr, requireSize, page, &slotPara);
    LfsUpdateInfo4GetNewUpperPage(mgr, slotPara.fsmPageIdx, slotPara.slotId, addr, dataPage);

    // 5.mgr计数更新
    mgr->maxBlockCnt++;
    mgr->usedPageCnt++;

    LfsRedoForExtentFsmSlot(pageMgr, mgr, allocNewFsmPage, &slotPara, *addr, page);
    if (page != NULL) {
        SeLeavePage(pageMgr, page->pageHead.addr, true);
    }
    return STATUS_OK_INTER;
}

StatusInter LfsGetBlockByExistSlot(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t listId, uint32_t requireSize, PageAddrT *addr, DbInstanceHdT dbInstance)
{
    StatusInter ret = STATUS_OK_INTER;
    FsmListT *fsmList = &mgr->fsmList[listId];
    FsmListNodeT *slot;
    ret = LfsGetSlotVirAddrByList(pageMgr, mgr, fsmList, &slot, true);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    // 链表头节点信息可能发生修改，所以这里先保存fsmPageIdx
    uint32_t fsmPageIdx = fsmList->fsmPageIdx;
    bool reUseUpperPage = (slot->dataPageId == SE_ALLOC_NEW_PAGE_ID);  // 记录slot修改之前的dataPageId
    ret = LfsGetAddrByPageIdSlotId(pageMgr, mgr, addr, slot, fsmList->fsmPageIdx, fsmList->slotId, dbInstance);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        LfsLeavePageByValidFsmPageIdx(pageMgr, mgr, fsmPageIdx, false);
        return ret;
    }

    FsmSlotParaT slotPara = {
        .slot = slot, .fsmPageIdx = fsmList->fsmPageIdx, .slotId = fsmList->slotId, .dbInstance = dbInstance};
    FsmUpdatedSlotInfoT unlinkedSlotInfo = {.oldSlot = *slot, .slotPara = slotPara};
    DB_ASSERT(slot->freeSize >= requireSize);
    LfsUnlinkFsmList(pageMgr, mgr, slot);  // 通过lfs获取的页对应的slot在获取之后一定不在链上

    ret = LfsRedoForGetBlockByExistSlot(
        pageMgr, mgr, &unlinkedSlotInfo, reUseUpperPage, DeserializePageId(pageMgr, slot->dataPageId), addr->blockId);
    LfsLeavePageByValidFsmPageIdx(pageMgr, mgr, fsmPageIdx, true);
    return ret;
}

StatusInter LfsSearchAvailBlock(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t requireSize, PageAddrT *addr, bool *isNewBlock, DbInstanceHdT dbInstance)
{
    DB_POINTER3(mgr, addr, isNewBlock);
    if (SECUREC_UNLIKELY(requireSize > mgr->listRange[FSM_LIST_ID_MAX])) {
        SE_LAST_ERROR(INT_ERR_LFS_INVALID_REQ_SIZE, "out of max size (%" PRIu32 ", %" PRIu16 ")", requireSize,
            mgr->listRange[FSM_LIST_ID_MAX]);
        return INT_ERR_LFS_INVALID_REQ_SIZE;
    }
    uint32_t listId = LfsGetAllocListId(mgr, requireSize);

    for (uint32_t i = listId; i < FSM_VAR_LIST_COUNT; i++) {
        if (mgr->fsmList[i].listLen == 0) {  // 这里是server申请，可以使用slot缓存
            continue;
        }
        *isNewBlock = IsSlotInFreeList(i);
        return LfsGetBlockByExistSlot(pageMgr, mgr, i, requireSize, addr, dbInstance);
    }
    // 此处必然是新block
    *isNewBlock = true;
    // 扩展一个slot
    return LfsExtentFsmSlot(pageMgr, mgr, addr, requireSize, dbInstance);
}

// 缓存并获取最近申请的上层页信息
StatusInter LfsGetAndCacheUpperPage(PageMgrT *pageMgr, LfsMgrT *mgr, PageAddrInfoT *addrInfo, bool isNewBlock)
{
    DB_POINTER3(pageMgr, mgr, addrInfo);

    if (SeGetPersistMode() != PERSIST_INCREMENT && (pageMgr->type != SE_BUFFER_POOL)) {
        // memdata和按需持久化场景下走该分支, lastUpperPageAddr字段有效
        if (addrInfo->pageAddr.pageId == mgr->lastUpperPageId) {
            addrInfo->pageHead = (PageHeadT *)(void *)mgr->lastUpperPageAddr;
            return STATUS_OK_INTER;
        }

        StatusInter ret = SeGetPage(pageMgr, DeserializePageId(pageMgr, addrInfo->pageAddr.pageId),
            &mgr->lastUpperPageAddr, ENTER_PAGE_WRITE, true);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "get upper pageId:%" PRIu32, addrInfo->pageAddr.pageId);
            return ret;
        }

        mgr->lastUpperPageId = addrInfo->pageAddr.pageId;
        addrInfo->pageHead = (PageHeadT *)(void *)mgr->lastUpperPageAddr;
    } else {                // 持久化场景走该分支, 缓存的lastUpperPageAddr字段仅临时使用
        if (!isNewBlock) {  // 1.防止新页重复get  2.旧页需要重新获取虚拟addr后返回
            PageHeadT *page;
            // 上层数据页在归还时才会leave
            StatusInter ret = SeGetPage(pageMgr, DeserializePageId(pageMgr, addrInfo->pageAddr.pageId),
                (uint8_t **)&page, ENTER_PAGE_WRITE, true);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "get upper pageId:%" PRIu32, addrInfo->pageAddr.pageId);
                return ret;
            }
            mgr->lastUpperPageAddr = (uint8_t *)page;  // 将旧页的虚拟addr临时缓存在该字段
        }
        addrInfo->pageHead = (PageHeadT *)(void *)mgr->lastUpperPageAddr;  // 新页的虚拟addr在获取时已临时缓存在该字段
        // 持久化场景下以下字段仅作临时字段使用
        mgr->lastUpperPageAddr = NULL;
        mgr->lastUpperPageId = SE_INVALID_PAGE_ID;
    }

    return STATUS_OK_INTER;
}

static StatusInter LfsGetBlockInternal(PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t requireSize, bool *isNewBlock,
    PageAddrInfoT *addrInfo, DbInstanceHdT dbInstance)
{
    DB_POINTER4(pageMgr, mgr, addrInfo, isNewBlock);

    if (SECUREC_UNLIKELY(requireSize > mgr->cfgInfo.availSize)) {
        SE_LAST_ERROR(
            INT_ERR_LFS_INVALID_REQ_SIZE, "get block(%" PRIu32 ", %" PRIu32 ")", requireSize, mgr->cfgInfo.availSize);
        return INT_ERR_LFS_INVALID_REQ_SIZE;
    }

    *isNewBlock = false;
    StatusInter ret = LfsSearchAvailBlock(pageMgr, mgr, requireSize, &addrInfo->pageAddr, isNewBlock, dbInstance);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    ret = LfsGetAndCacheUpperPage(pageMgr, mgr, addrInfo, *isNewBlock);
    return ret;
}

inline StatusInter LfsGetAvailBlock(
    FsmRunCtxT *fsmRunCtx, uint32_t requireSize, bool *isNewBlock, PageAddrInfoT *addrInfo, DbInstanceHdT dbInstance)
{
    // noteb: optimize the performance
    LfsMgrT *lfsMgr = NULL;
    StatusInter ret = FsmRunCtxGetMgr(fsmRunCtx, true, &lfsMgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get mgr");
        return ret;
    }
    ret = FsmLatchMember(fsmRunCtx, lfsMgr, true);
    if (ret != STATUS_OK_INTER) {
        FsmRunCtxLeaveMgr(fsmRunCtx, false);
        return ret;
    }
    ret = LfsGetBlockInternal(fsmRunCtx->pageMgr, lfsMgr, requireSize, isNewBlock, addrInfo, dbInstance);
    FsmUnlatchMember(fsmRunCtx, lfsMgr, true);
    FsmRunCtxLeaveMgr(fsmRunCtx, ret == STATUS_OK_INTER);
    return ret;
}

void LfsUpdateSlotLinkInfo(
    PageMgrT *pageMgr, LfsMgrT *mgr, FsmListNodeT *slot, const FsmBlockAddrT *blockAddr, uint32_t listId, bool relink)
{
    DB_POINTER4(pageMgr, mgr, slot, blockAddr);

    if (slot->listId == listId) {
        DB_ASSERT(relink == false);  // 当前block在链表上，上层应该不主动修改链表等级
        return;
    }

    if (slot->listId != LFS_INVALID_LIST_ID) {
        DB_ASSERT(relink == false);  // 当前block在链表上，上层应该不主动修改链表等级，LFS内部切换等级
        LfsUnlinkFsmList(pageMgr, mgr, slot);
        LfsLinkFsmList(pageMgr, mgr, slot, listId, blockAddr->fsmPageIdx, blockAddr->slotId);
        return;
    }

    if (relink) {  // 当前block不在链表上，上层主动relink
        DB_ASSERT(slot->listId == LFS_INVALID_LIST_ID);
        LfsLinkFsmList(pageMgr, mgr, slot, listId, blockAddr->fsmPageIdx, blockAddr->slotId);
        return;
    }

    // 当前block不在链表上，也不relink，不用任何处理，不用挂回链表
    DB_ASSERT(slot->listId == LFS_INVALID_LIST_ID);
    return;
}

StatusInter LfsUpdateBlockFreeSpace(PageMgrT *pageMgr, LfsMgrT *mgr, FsmSlotParaT *slotPara,
    FsmBlockUpdateParaT updatePara, bool relink, uint32_t listId)
{
    DB_POINTER2(pageMgr, mgr);
    FsmListNodeT *slot = slotPara->slot;
    slot->maxRowSize = (uint16_t)DB_MAX(slot->maxRowSize, updatePara.maxRowSize);
    slot->maxRowSize = (uint16_t)DB_MIN(slot->maxRowSize, mgr->cfgInfo.availSize);
    slot->freeSize = (uint16_t)updatePara.freeSize;
    FsmBlockAddrT blockAddr = {.fsmPageIdx = slotPara->fsmPageIdx, .slotId = slotPara->slotId};
    LfsUpdateSlotLinkInfo(pageMgr, mgr, slot, &blockAddr, listId, relink);
    StatusInter ret = STATUS_OK_INTER;
    if (slot->listId == (uint32_t)FSM_LIST_ID_MAX) {
        bool tmp = IsSlotInFreeList(listId);  // 注意只能释放最高级链表
        DB_ASSERT(tmp);
        ret = LfsReleaseUpperPage(pageMgr, mgr, updatePara.releasePage, slotPara);
    }

    return ret;
}

static StatusInter LfsSetBlockFreeSpaceInternal(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint8_t *dataPage, BlockInfoT blockInfo, DbInstanceHdT dbInstance)
{
    DB_POINTER2(pageMgr, mgr);
    if (SECUREC_UNLIKELY(blockInfo.freeSize > mgr->listRange[FSM_LIST_ID_MAX])) {
        SE_LAST_ERROR(INT_ERR_LFS_INVALID_REQ_SIZE, "out of max availSize (%" PRIu32 ", %" PRIu16 ")",
            blockInfo.freeSize, mgr->listRange[FSM_LIST_ID_MAX]);
        return INT_ERR_LFS_INVALID_REQ_SIZE;
    }

    FsmListNodeT *slot = NULL;
    StatusInter ret = STATUS_OK_INTER;

    DB_ASSERT(mgr->maxSlotPerFsmPage != 0);
    uint32_t slotIdx = ((FsmDataPageHeadT *)((void *)dataPage))->slotIdx;
    // 释放上层data页
    if (blockInfo.leavePage) {
        PageIdT dataPageId = ((PageHeadT *)((void *)dataPage))->addr;
        SeLeavePage(pageMgr, dataPageId, true);
    }

    uint32_t fsmPageIdx = LfsGetFsmPageIdxByBlockId(slotIdx, mgr->maxSlotPerFsmPage);
    uint32_t slotIdWithinPage = LfsGetSlotIdByBlockId(slotIdx, mgr->maxSlotPerFsmPage);
    FsmBlockUpdateParaT updatePara = {
        .freeSize = blockInfo.freeSize, .maxRowSize = blockInfo.maxRowSize, .releasePage = true};
    FsmSlotInfoT fsmSlotInfo = {.fsmPageIdx = fsmPageIdx, .slotId = slotIdWithinPage};
    ret = LfsGetSlotByFsmPageIdxAndSlotId(pageMgr, mgr, fsmSlotInfo, &slot, true);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    FsmSlotParaT slotPara = {
        .slot = slot, .fsmPageIdx = fsmPageIdx, .slotId = slotIdWithinPage, .dbInstance = dbInstance};

    uint32_t newListId = (uint32_t)LfsGetOwnerListId(mgr, updatePara.freeSize);

    FsmListT oldLinkFsmList = mgr->fsmList[newListId];  // 记录新链修改前的副本
    FsmUpdatedSlotInfoT targetSlot = {.oldSlot = *slot, .slotPara = slotPara};
    ret = LfsUpdateBlockFreeSpace(pageMgr, mgr, &slotPara, updatePara, blockInfo.relink, newListId);
    if (ret != STATUS_OK_INTER) {
        LfsLeavePageByValidFsmPageIdx(pageMgr, mgr, fsmPageIdx, true);
        return ret;
    }

    ret = LfsRedoForSetBlockFreeSpace(pageMgr, mgr, &targetSlot, newListId, blockInfo.relink, &oldLinkFsmList);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "write redo log when set block free space to fsm");
    }
    LfsLeavePageByValidFsmPageIdx(pageMgr, mgr, fsmPageIdx, true);
    return ret;
}

StatusInter LfsGetMaxFreeSizeBlock(
    FsmRunCtxT *fsmRunCtx, bool *isNewBlock, PageAddrInfoT *addrInfo, DbInstanceHdT dbInstance)
{
    DB_POINTER3(fsmRunCtx, addrInfo, isNewBlock);
    LfsMgrT *lfsMgr = NULL;
    StatusInter ret = FsmRunCtxGetMgr(fsmRunCtx, true, &lfsMgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get mgr");
        return ret;
    }
    ret = FsmLatchMember(fsmRunCtx, lfsMgr, true);
    if (ret != STATUS_OK_INTER) {
        FsmRunCtxLeaveMgr(fsmRunCtx, false);
        return ret;
    }
    *isNewBlock = false;
    int32_t listId = (int32_t)FSM_LIST_ID_MAX;
    for (int32_t i = (int32_t)FSM_LIST_ID_MAX - 1; i > (int32_t)FSM_LIST_ID_0; i--) {
        if (lfsMgr->fsmList[i].listLen != 0) {
            listId = i;
            break;
        }
    }
    ret = STATUS_OK_INTER;
    if (listId < (int32_t)FSM_LIST_ID_MAX) {
        FsmListT *fsmList = &lfsMgr->fsmList[listId];
        FsmListNodeT *slot;
        ret = LfsGetSlotVirAddrByList(fsmRunCtx->pageMgr, lfsMgr, fsmList, &slot, false);
        if (ret != STATUS_OK_INTER) {
            FsmUnlatchMember(fsmRunCtx, lfsMgr, true);
            FsmRunCtxLeaveMgr(fsmRunCtx, false);
            return ret;
        }
        uint32_t requireSize = slot->freeSize;
        LfsLeavePageByValidFsmPageIdx(fsmRunCtx->pageMgr, lfsMgr, fsmList->fsmPageIdx, false);
        ret = LfsGetBlockByExistSlot(
            fsmRunCtx->pageMgr, lfsMgr, (uint32_t)listId, requireSize, &addrInfo->pageAddr, dbInstance);
    } else {
        // 返回一个新的空闲页
        ret = LfsSearchAvailBlock(
            fsmRunCtx->pageMgr, lfsMgr, lfsMgr->cfgInfo.availSize, &addrInfo->pageAddr, isNewBlock, dbInstance);
    }

    if (ret == STATUS_OK_INTER) {
        ret = LfsGetAndCacheUpperPage(fsmRunCtx->pageMgr, lfsMgr, addrInfo, *isNewBlock);
    }
    FsmUnlatchMember(fsmRunCtx, lfsMgr, true);
    FsmRunCtxLeaveMgr(fsmRunCtx, ret == STATUS_OK_INTER);
    return ret;
}

StatusInter LfsGetSlotByFsmPageIdxAndSlotId(
    PageMgrT *pageMgr, LfsMgrT *mgr, FsmSlotInfoT fsmSlotInfo, FsmListNodeT **slot, bool isWrite)
{
    if (fsmSlotInfo.fsmPageIdx == SLOT_IN_MGR_VIRTUAL_FSM_PAGE_IDX) {
        LfsGetSlotInMgr(mgr, fsmSlotInfo.slotId, slot);
        return STATUS_OK_INTER;
    }
    StatusInter ret = STATUS_OK_INTER;
    FsmPageHeadT *fsmPage = NULL;
    ret = LfsGetFsmPageByCache(pageMgr, mgr, fsmSlotInfo.fsmPageIdx, &fsmPage, isWrite);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    ret = LfsGetFsmSlot(fsmPage, fsmSlotInfo.slotId, slot);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "get fsmSlotId: %" PRIu32, fsmSlotInfo.slotId);
        SeLeavePage(pageMgr, fsmPage->pageHead.addr, false);
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter LfsSetBlockFreeSpace(FsmRunCtxT *fsmRunCtx, uint8_t *dataPage, BlockInfoT blockInfo)
{
    LfsMgrT *lfsMgr = NULL;
    StatusInter ret = FsmRunCtxGetMgr(fsmRunCtx, true, &lfsMgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "fsmRunCtx get mgr");
        return ret;
    }
    ret = FsmLatchMember(fsmRunCtx, lfsMgr, true);
    if (ret != STATUS_OK_INTER) {
        FsmRunCtxLeaveMgr(fsmRunCtx, false);
        return ret;
    }
    DbInstanceHdT dbInstance = fsmRunCtx->seRunCtx ? ((SeInstanceT *)(fsmRunCtx->seRunCtx->seIns))->dbInstance : NULL;
    ret = LfsSetBlockFreeSpaceInternal(fsmRunCtx->pageMgr, lfsMgr, dataPage, blockInfo, dbInstance);
    FsmUnlatchMember(fsmRunCtx, lfsMgr, true);
    FsmRunCtxLeaveMgr(fsmRunCtx, ret == STATUS_OK_INTER);
    return ret;
}

StatusInter LfsGetSpecificSlotByFsmPageIdxAndSlotId(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t fsmPageIdx, uint32_t slotId, FsmListNodeT **slot)
{
    if (fsmPageIdx == SLOT_IN_MGR_VIRTUAL_FSM_PAGE_IDX) {
        LfsGetSlotInMgr(mgr, slotId, slot);
        return STATUS_OK_INTER;
    }
    FsmPageHeadT *fsmPage = NULL;
    StatusInter ret = LfsGetFsmPage(pageMgr, mgr, fsmPageIdx, &fsmPage, false);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    ret = LfsGetFsmSlot(fsmPage, slotId, slot);
    if (ret != STATUS_OK_INTER) {
        SeLeavePage(pageMgr, fsmPage->pageHead.addr, false);
    }
    return ret;
}

StatusInter LfsTryGetSpecificBlock(FsmRunCtxT *fsmRunCtx, BlockNumber blockId, PageAddrT *addr)
{
    // 直连读流程可能会走到这里，需要记录session中的锁栈，由外面加锁控制并发 (补上读锁)
    DB_POINTER(fsmRunCtx);
    LfsMgrT *lfsMgr = NULL;
    StatusInter ret = FsmRunCtxGetMgr(fsmRunCtx, false, &lfsMgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get mgr");
        return ret;
    }
    ret = FsmLatchMember(fsmRunCtx, lfsMgr, false);
    if (ret != STATUS_OK_INTER) {
        FsmRunCtxLeaveMgr(fsmRunCtx, false);
        return ret;
    }

    if (blockId >= lfsMgr->maxBlockCnt) {
        SE_LAST_ERROR(INT_ERR_LFS_INVALID_BLOCK_ID, "out of max block cnt (%" PRIu32 ", %" PRIu32 ")", blockId,
            lfsMgr->maxBlockCnt);
        FsmUnlatchMember(fsmRunCtx, lfsMgr, false);
        FsmRunCtxLeaveMgr(fsmRunCtx, false);
        return INT_ERR_LFS_INVALID_BLOCK_ID;
    }
    DB_ASSERT(lfsMgr->maxSlotPerFsmPage > 0);
    uint32_t slotId = LfsGetSlotIdByBlockId(blockId, lfsMgr->maxSlotPerFsmPage);
    uint32_t fsmPageIdx = LfsGetFsmPageIdxByBlockId(blockId, lfsMgr->maxSlotPerFsmPage);
    // 注入异常退出点，加锁成功后异常退出
    CRASHPOINT(DB_CRASH_EVENT_LFS_ADD_PID_LOCK, DB_CRASH_STATE_PIDLOCK_UNLOCK);

    FsmListNodeT *slot = NULL;
    ret = LfsGetSpecificSlotByFsmPageIdxAndSlotId(fsmRunCtx->pageMgr, lfsMgr, fsmPageIdx, slotId, &slot);
    if (ret != STATUS_OK_INTER) {
        FsmUnlatchMember(fsmRunCtx, lfsMgr, false);
        FsmRunCtxLeaveMgr(fsmRunCtx, false);
        return ret;
    }
    ret = LfsTryGetSpecBlockByFsmSlot(lfsMgr, slot, fsmPageIdx, slotId, addr);
    LfsLeavePageByValidFsmPageIdx(fsmRunCtx->pageMgr, lfsMgr, fsmPageIdx, false);
    FsmUnlatchMember(fsmRunCtx, lfsMgr, false);
    FsmRunCtxLeaveMgr(fsmRunCtx, false);
    return ret;
}

static StatusInter LfsCalcFragBlockForMove(
    const LfsMgrT *mgr, uint32_t blkListId, const FsmListNodeT *slot, bool *canMove)
{
    DB_POINTER3(mgr, slot, canMove);
    *canMove = false;
    if (mgr->fragmentedInfo.lastFragmentedPageId == slot->dataPageId) {
        return STATUS_OK_INTER;  // 找到与前一次相同的页，直接返回，外部游标++
    }
    uint32_t rowSize = (mgr->cfgInfo.pageType == HEAP_FIX_LEN_ROW_PAGE) ? mgr->cfgInfo.reserveSize : slot->maxRowSize;
    if (SECUREC_UNLIKELY(rowSize > mgr->listRange[FSM_LIST_ID_MAX])) {
        SE_LAST_ERROR(INT_ERR_LFS_INVALID_REQ_SIZE, "out of max availSize (%" PRIu32 ", %" PRIu16 ")", rowSize,
            mgr->listRange[FSM_LIST_ID_MAX]);
        return INT_ERR_LFS_INVALID_REQ_SIZE;
    }
    uint32_t allocListId = LfsGetAllocListId(mgr, rowSize);

    DB_ASSERT(allocListId != LFS_INVALID_LIST_ID);
    // 上层函数循环从最高级链表（最空闲链表）开始计算，必然不会出现需要从某一级链表搬往更高级链表的情况
    if (allocListId > blkListId) {
        DB_LOG_INFO("inv alloc list id (%" PRIu32 ", %" PRIu32 ")", allocListId, blkListId);
        return INT_ERR_LFS_NO_FRAG_BLOCK;
    }

    // 是否能找到一个合适的页来存放被搬迁页的数据， 只能在非空页链表中搜索
    for (uint32_t j = allocListId; j < blkListId; j++) {
        if (mgr->fsmList[j].listLen > 0) {
            *canMove = true;
            return STATUS_OK_INTER;
        }
    }
    return STATUS_OK_INTER;
}

bool LfsIsGetFragmentedSlotId(PageMgrT *pageMgr, LfsMgrT *mgr, int32_t fragListId, uint32_t lastFsmListIdx,
    uint32_t *fsmPageIdx, uint32_t *slotId)
{
    DB_POINTER3(mgr, slotId, fsmPageIdx);
    uint32_t tmpSlotId = (uint32_t)mgr->fsmList[fragListId].slotId;
    uint32_t tmpPageIdx = (uint32_t)mgr->fsmList[fragListId].fsmPageIdx;
    FsmListNodeT *slot = NULL;
    DB_ASSERT(tmpSlotId != LFS_INVALID_SLOT_ID);  // 走到这里肯定不为LFS_INVALID_SLOT_ID, 外面已经校验过
    for (uint32_t i = lastFsmListIdx; i > 0; i--) {
        StatusInter ret = LfsGetFsmSlotById(pageMgr, mgr, tmpPageIdx, tmpSlotId, &slot, false);
        if (ret != STATUS_OK_INTER) {
            return false;
        }
        uint32_t oldTemPageIdx = tmpPageIdx;
        tmpPageIdx = slot->nextFsmPageIdx;
        tmpSlotId = slot->nextSlotId;
        LfsLeavePageByValidFsmPageIdx(pageMgr, mgr, oldTemPageIdx, false);
        if (tmpSlotId == LFS_INVALID_SLOT_ID) {
            return false;
        }
    }
    *slotId = tmpSlotId;
    *fsmPageIdx = tmpPageIdx;
    return true;
}

StatusInter LfsGetFragmentedSlot(PageMgrT *pageMgr, FsmFragmentParaT *fragmentPara, PageAddrT *addr, bool *canMove)
{
    StatusInter ret = STATUS_OK_INTER;
    LfsMgrT *mgr = fragmentPara->mgr;
    int32_t fragListId = fragmentPara->fragListId;
    uint32_t fsmListIdx = mgr->fragmentedInfo.lastFragmentedFsmListIdx;
    // 对这个页上的这一级链表进行遍历，看是否能搬迁到其他页
    for (; fsmListIdx < mgr->fsmList[fragListId].listLen; fsmListIdx++) {
        uint32_t curFsmPageIdx;
        uint32_t curSlotId;
        fragmentPara->fsmListIdx = fsmListIdx;
        if (!LfsIsGetFragmentedSlotId(pageMgr, mgr, fragListId, fsmListIdx, &curFsmPageIdx, &curSlotId)) {
            break;  // 当前链表已找完，退出这一层循环
        }
        FsmListNodeT *slot = NULL;
        ret = LfsGetFsmSlotById(pageMgr, mgr, curFsmPageIdx, curSlotId, &slot, false);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        DB_ASSERT(slot->maxRowSize <= mgr->cfgInfo.availSize);
        // 计算这个页里的数据是否可以被搬迁到其他页
        ret = LfsCalcFragBlockForMove(mgr, (uint32_t)fragListId, slot, canMove);
        if (ret != STATUS_OK_INTER || *canMove) {  // 发生了错误或者搜索到了可搬迁的页，都赋值页addr后退出
            LfsSetBlockAddr(mgr, curFsmPageIdx, curSlotId, slot, addr);

            mgr->fragmentedInfo.lastFragmentedPageId =
                (*canMove == true) ? slot->dataPageId : mgr->fragmentedInfo.lastFragmentedPageId;
            LfsLeavePageByValidFsmPageIdx(pageMgr, mgr, curFsmPageIdx, false);
            return ret;
        }
        LfsLeavePageByValidFsmPageIdx(pageMgr, mgr, curFsmPageIdx, false);
    }
    return ret;
}

// 去碎片化函数lfs层内部开启redo原子操作
StatusInter LfsGetFragmentedBlock(FsmRunCtxT *fsmRunCtx, PageAddrT *addr)
{
    DB_POINTER2(fsmRunCtx, addr);
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    FsmRedoLogBegin(redoCtx, fsmRunCtx->pageMgr->type != SE_MEMDATA);
    LfsMgrT *lfsMgr = NULL;
    StatusInter ret = FsmRunCtxGetMgr(fsmRunCtx, true, &lfsMgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get mgr");
        return ret;
    }
    ret = FsmLatchMember(fsmRunCtx, lfsMgr, true);
    if (ret != STATUS_OK_INTER) {
        FsmRunCtxLeaveMgr(fsmRunCtx, false);
        return ret;
    }

    LfsMgrInitFragmentedId(lfsMgr);

    // 由高到低遍历各个级别的非空的FSM链表
    ret = INT_ERR_LFS_NO_FRAG_BLOCK;
    int32_t fragListId = lfsMgr->fragmentedInfo.lastFragmentedListId;
    uint32_t fsmListIdx = lfsMgr->fragmentedInfo.lastFragmentedFsmListIdx;
    FsmFragmentParaT fragmentPara = {.mgr = lfsMgr, .fragListId = fragListId, .fsmListIdx = fsmListIdx};
    for (; fragListId >= 0; fragListId--) {
        fragmentPara.fragListId = fragListId;
        if (lfsMgr->fsmList[fragListId].listLen == 0) {  // 当前级的链表为空，重置lastPageId/SlotId
            lfsMgr->fragmentedInfo.lastFragmentedFsmListIdx = 0;
            continue;
        }
        // 从fragmentPara缓存的当前链表，遍历链上的slot，会导致在fsm页间反复跳转
        bool canMove = false;
        // 在这一级链表上找, 遍历过程会更新fragmentPara中的fsmListIdx
        ret = LfsGetFragmentedSlot(fsmRunCtx->pageMgr, &fragmentPara, addr, &canMove);
        if (ret != STATUS_OK_INTER || canMove) {  // 发生了错误或者搜索到了可搬迁的页，都退出
            goto EXIT;
        }
        // 对这一级链表的所有页都没找到合适的，去下一级链表去找，重置lastPageId/SlotId
        lfsMgr->fragmentedInfo.lastFragmentedFsmListIdx = 0;
    }
    /*
     * 流程走到这里，此时除了最高级链表以外，其他的链表都没有页
     * 但是全部的页都不在链表上，发生的概率极小，这些页的数量应该是极少的， 故此处也认为没有可整理的页
     */
EXIT:
    LfsMgrSetLastFragmentedId(lfsMgr, ret, fragmentPara.fragListId, fragmentPara.fsmListIdx);
    LfsRedoForMgrFragment(lfsMgr);
    FsmUnlatchMember(fsmRunCtx, lfsMgr, true);
    FsmRunCtxLeaveMgr(fsmRunCtx, true);
    StatusInter result = FsmRedoLogEnd(redoCtx, fsmRunCtx->pageMgr->type != SE_MEMDATA);
    if (SECUREC_UNLIKELY(result != STATUS_OK_INTER)) {
        SE_ERROR(result, "redo end");
    }
    return ret != STATUS_OK_INTER ? ret : result;
}

static StatusInter LfsReleaseBlockInList(PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t listId, DbInstanceHdT dbInstance)
{
    DB_POINTER2(pageMgr, mgr);

    uint32_t fsmPageIdx = mgr->fsmList[listId].fsmPageIdx;
    uint32_t slotId = mgr->fsmList[listId].slotId;
    StatusInter ret = STATUS_OK_INTER;
    for (uint32_t i = 0; i < mgr->fsmList[listId].listLen; i++) {
        FsmListNodeT *slot = NULL;
        FsmSlotInfoT fsmSlotInfo = {.fsmPageIdx = fsmPageIdx, .slotId = slotId};
        ret = LfsGetSlotByFsmPageIdxAndSlotId(pageMgr, mgr, fsmSlotInfo, &slot, true);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }

        if (slot->dataPageId != SE_ALLOC_NEW_PAGE_ID) {
            FsmSlotParaT slotPara = {
                .slot = slot, .fsmPageIdx = fsmPageIdx, .slotId = slotId, .dbInstance = dbInstance};
            bool tmpRet = IsSlotInFreeList(slot->listId);  // 注意只能释放最高级链表
            DB_ASSERT(tmpRet);
            ret = LfsReleaseUpperPage(pageMgr, mgr, true, &slotPara);
            if (ret != STATUS_OK_INTER) {
                LfsLeavePageByValidFsmPageIdx(pageMgr, mgr, fsmPageIdx, false);
                return ret;
            }
            LfsRedoForSlot(pageMgr, mgr, fsmPageIdx, slotId, slot);
        }
        uint32_t nextFsmPageIdx = slot->nextFsmPageIdx;
        slotId = slot->nextSlotId;
        LfsLeavePageByValidFsmPageIdx(pageMgr, mgr, fsmPageIdx, true);
        fsmPageIdx = nextFsmPageIdx;
    }

    return STATUS_OK_INTER;
}

StatusInter LfsReleaseAllFreeBlock(FsmRunCtxT *fsmRunCtx)
{
    DB_POINTER(fsmRunCtx);
    LfsMgrT *lfsMgr = NULL;
    StatusInter ret = FsmRunCtxGetMgr(fsmRunCtx, true, &lfsMgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get mgr");
        return ret;
    }
    ret = FsmLatchMember(fsmRunCtx, lfsMgr, true);
    if (ret != STATUS_OK_INTER) {
        FsmRunCtxLeaveMgr(fsmRunCtx, false);
        return ret;
    }

    DbInstanceHdT dbInstance = fsmRunCtx->seRunCtx ? ((SeInstanceT *)(fsmRunCtx->seRunCtx->seIns))->dbInstance : NULL;
    ret = LfsReleaseBlockInList(fsmRunCtx->pageMgr, lfsMgr, FSM_LIST_ID_MAX, dbInstance);
    if (ret != STATUS_OK_INTER) {
        FsmUnlatchMember(fsmRunCtx, lfsMgr, true);
        FsmRunCtxLeaveMgr(fsmRunCtx, false);
        return ret;
    }
    DB_ASSERT(lfsMgr->freePageCnt == 0);  // 所有空闲页应该都要被释放
    DB_ASSERT(
        lfsMgr->maxBlockCnt == lfsMgr->usedPageCnt + lfsMgr->freePageCnt + lfsMgr->releasedPageCnt);  // 确保计数一致性
    LfsRedoForMgrPageCnt(lfsMgr);
    FsmUnlatchMember(fsmRunCtx, lfsMgr, true);
    FsmRunCtxLeaveMgr(fsmRunCtx, true);
    return STATUS_OK_INTER;
}

static void LfsKeepThreadAlive(uint64_t *splitStartTime)
{
    DB_POINTER(splitStartTime);
    if (DbExceedTime(*splitStartTime, LFS_DEFAULT_SPILT_TIME)) {
        // SQL场景不用喂狗
        if (!DbIsMultiInstanceEnabled() && !DrtKeepThisWorkerAlive(NULL)) {
            SE_ERROR(INTERNAL_ERROR_INTER, "keep thread alive");
        }
        *splitStartTime = DbClockGetTsc();
    }
}

static StatusInter LfsReleaseAllBlockInFsmPage(PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t fsmPageIdx)
{
    DB_POINTER2(pageMgr, mgr);

    PageIdT fsmPageId;
    StatusInter ret = LfsGetFsmPageId(pageMgr, mgr, fsmPageIdx, &fsmPageId);
    if (ret != STATUS_OK_INTER) {
        return ret == INT_ERR_LFS_INVALID_FSM_PAGE_ID ? STATUS_OK_INTER : ret;
    }

    FsmPageHeadT *page = NULL;
    ret = LfsGetFsmPageByCache(pageMgr, mgr, fsmPageIdx, &page, false);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    uint16_t usedSlotCnt = page->fsmHead.usedSlotCnt;
    SeLeavePage(pageMgr, page->pageHead.addr, false);

    // fsm管理页面上统计的 usedSlotCnt 应该在 总体mgr 统计的范围内
    DB_ASSERT(usedSlotCnt + fsmPageIdx * mgr->maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR <= mgr->maxBlockCnt);
    uint64_t splitStartTime = DbClockGetTsc();
    for (BlockNumber slotId = 0; slotId < (uint32_t)usedSlotCnt; slotId++) {
        ret = LfsReleaseUpperPageWithRedo(pageMgr, mgr->cfgInfo.mgrPageId, mgr->cfgInfo.mgrOffset, fsmPageIdx, slotId);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        LfsKeepThreadAlive(&splitStartTime);
    }
    return STATUS_OK_INTER;
}

static StatusInter LfsSlotInMgrReleaseAllBlock(
    PageMgrT *pageMgr, PageIdT fsmAddr, uint32_t offset, uint32_t maxBlockCnt)
{
    DB_POINTER(pageMgr);
    uint32_t useSlotNumInMgr = DB_MIN(FSM_SLOT_NUM_IN_MGR, maxBlockCnt);
    for (uint32_t slotId = 0; slotId < useSlotNumInMgr; slotId++) {
        StatusInter ret =
            LfsReleaseUpperPageWithRedo(pageMgr, fsmAddr, offset, SLOT_IN_MGR_VIRTUAL_FSM_PAGE_IDX, slotId);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter LfsReleaseAllBlock(PageMgrT *pageMgr, PageIdT fsmAddr, uint32_t offset)
{
    DB_POINTER(pageMgr);
    LfsMgrT *lfsMgr = NULL;
    StatusInter ret = FsmGetMgr(pageMgr, fsmAddr, offset, false, &lfsMgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get mgr wrong");
        return ret;
    }

    ret = LfsSlotInMgrReleaseAllBlock(pageMgr, fsmAddr, offset, lfsMgr->maxBlockCnt);
    if (ret != STATUS_OK_INTER) {
        FsmLeaveMgr(pageMgr, fsmAddr, false);
        return ret;
    }

    for (uint32_t fsmPageIdx = 0; fsmPageIdx < lfsMgr->fsmPageCnt; fsmPageIdx++) {
        ret = LfsReleaseAllBlockInFsmPage(pageMgr, lfsMgr, fsmPageIdx);
        if (ret != STATUS_OK_INTER) {
            FsmLeaveMgr(pageMgr, fsmAddr, false);
            return ret;
        }
    }

    DB_ASSERT(lfsMgr->usedPageCnt == 0);                        // 所有页应该都要被释放
    DB_ASSERT(lfsMgr->freePageCnt == 0);                        // 所有页应该都要被释放
    DB_ASSERT(lfsMgr->maxBlockCnt == lfsMgr->releasedPageCnt);  // 所有页应该都要被释放
    FsmLeaveMgr(pageMgr, fsmAddr, false);
    return STATUS_OK_INTER;
}

StatusInter FsmAllocRunCtx(SeRunCtxT *seRunCtx, void *memCtx, FsmRunCtxT **fsmRunCtx)
{
    FsmRunCtxT *newCtx = DbDynMemCtxAlloc(memCtx, sizeof(FsmRunCtxT));
    if (newCtx == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "Alloc fsmRunCtx");
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_s(newCtx, sizeof(FsmRunCtxT), 0, sizeof(FsmRunCtxT));
    newCtx->seRunCtx = seRunCtx;
    *fsmRunCtx = newCtx;
    return STATUS_OK_INTER;
}

void FsmResetRunCtx(FsmRunCtxT *fsmRunCtx, SeRunCtxT *seRunCtx)
{
    DB_POINTER(fsmRunCtx);
    fsmRunCtx->seRunCtx = seRunCtx;
}

StatusInter FsmInitRunCtx(const FsmRunCtxCfgT *fsmCtxCfg, FsmRunCtxT *fsmRunCtx)
{
    fsmRunCtx->pageMgr = fsmCtxCfg->pageMgr;
    fsmRunCtx->enableCache = fsmCtxCfg->enableCache;
    fsmRunCtx->enableRedo = fsmCtxCfg->enableRedo;
    fsmRunCtx->enableLatch = fsmCtxCfg->enableLatch;
    fsmRunCtx->directAccess = fsmCtxCfg->directAccess;
    if (fsmRunCtx->enableCache) {
        StatusInter ret =
            FsmGetMgr(fsmRunCtx->pageMgr, fsmCtxCfg->fsmAddr, fsmCtxCfg->offset, false, &fsmRunCtx->fsmMgrPtr);
        if (ret != STATUS_OK_INTER) {
            SE_LAST_ERROR(ret, "fsmMgr init runCtx");
            return ret;
        }
        FsmLeaveMgr(fsmRunCtx->pageMgr, fsmCtxCfg->fsmAddr, false);
    }
    fsmRunCtx->fsmMgrAddr = fsmCtxCfg->fsmAddr;
    fsmRunCtx->offset = fsmCtxCfg->offset;
    return STATUS_OK_INTER;
}

void FsmFreeRunCtx(void *memCtx, FsmRunCtxT **fsmRunCtx)
{
    if ((*fsmRunCtx) == NULL) {
        return;
    }
    DbDynMemCtxFree(memCtx, *fsmRunCtx);
    *fsmRunCtx = NULL;
}

StatusInter HeapGetPageWithLoad(PageMgrT *mgr, PageIdT pageId, uint8_t **page)
{
    uint32_t option = ENTER_PAGE_NORMAL;  // 读 or 写
    TablePriorityRecyArgT recycleArg = {.recycleFlag = SE_RECYCLE_PRIORITY, .priority = TABLE_LOAD_PRIORITY_HIGH};
    StatusInter ret = SeGetPageWithArg(mgr, pageId, option, &recycleArg, page);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get page with arg high");
    }
    return ret;
}

void HeapLeavePageWithLoad(PageMgrT *mgr, PageIdT pageId)
{
    SeLeavePage(mgr, pageId, false);
}

StatusInter LfsTableLoadDataPage(
    PageMgrT *pageMgr, PageIdT fsmAddr, uint32_t offset, uint32_t fsmPageIdx, uint32_t slotId)
{
    DB_POINTER(pageMgr);
    LfsMgrT *fsmMgr = NULL;
    StatusInter ret = FsmGetMgr(pageMgr, fsmAddr, offset, true, &fsmMgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get mgr");
        return ret;
    }

    FsmListNodeT *slot;
    FsmSlotInfoT fsmSlotInfo = {.fsmPageIdx = fsmPageIdx, .slotId = slotId};
    ret = LfsGetSlotByFsmPageIdxAndSlotId(pageMgr, fsmMgr, fsmSlotInfo, &slot, false);
    if (ret != STATUS_OK_INTER) {
        FsmLeaveMgr(pageMgr, fsmAddr, false);
        return ret;
    }

    uint8_t *dataPage = NULL;
    PageAddrT addr;
    LfsSetBlockAddr(fsmMgr, fsmPageIdx, slotId, slot, &addr);
    PageIdT dataPageId = DeserializePageId(pageMgr, addr.pageId);
    ret = HeapGetPageWithLoad(pageMgr, dataPageId, &dataPage);
    HeapLeavePageWithLoad(pageMgr, dataPageId);
    if (ret != STATUS_OK_INTER) {
        LfsLeavePageByValidFsmPageIdx(pageMgr, fsmMgr, fsmPageIdx, false);
        FsmLeaveMgr(pageMgr, fsmAddr, false);
        return ret;
    }
    LfsLeavePageByValidFsmPageIdx(pageMgr, fsmMgr, fsmPageIdx, true);
    FsmLeaveMgr(pageMgr, fsmAddr, true);
    return ret;
}

static StatusInter LfsTableLoadScanAllBlockInFsmPage(PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t fsmPageIdx)
{
    DB_POINTER2(pageMgr, mgr);

    PageIdT fsmPageId = SE_INVALID_PAGE_ADDR;
    StatusInter ret = LfsGetFsmPageId(pageMgr, mgr, fsmPageIdx, &fsmPageId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get fsmPageIdx(%" PRIu32 ")", fsmPageIdx);
        return ret;
    }

    if (!DbIsPageIdValid(fsmPageId)) {
        return STATUS_OK_INTER;
    }

    FsmPageHeadT *page = NULL;
    ret = LfsGetFsmPageByCache(pageMgr, mgr, fsmPageIdx, &page, false);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    uint16_t usedSlotCnt = page->fsmHead.usedSlotCnt;
    SeLeavePage(pageMgr, page->pageHead.addr, false);

    uint64_t splitStartTime = DbClockGetTsc();
    for (BlockNumber slotId = 0; slotId < (uint32_t)usedSlotCnt; slotId++) {
        ret = LfsTableLoadDataPage(pageMgr, mgr->cfgInfo.mgrPageId, mgr->cfgInfo.mgrOffset, fsmPageIdx, slotId);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        LfsKeepThreadAlive(&splitStartTime);
    }
    return STATUS_OK_INTER;
}

StatusInter LfsLoadTableScanAllBlock(PageMgrT *pageMgr, PageIdT fsmAddr, uint32_t offset, LfsMgrT *lfsMgr)
{
    DB_POINTER(pageMgr);
    uint32_t useSlotNumInMgr = DB_MIN(FSM_SLOT_NUM_IN_MGR, lfsMgr->maxBlockCnt);
    for (uint32_t slotId = 0; slotId < useSlotNumInMgr; slotId++) {
        StatusInter ret = LfsTableLoadDataPage(pageMgr, fsmAddr, offset, SLOT_IN_MGR_VIRTUAL_FSM_PAGE_IDX, slotId);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    for (uint32_t fsmPageIdx = 0; fsmPageIdx < lfsMgr->fsmPageCnt; fsmPageIdx++) {
        StatusInter ret = LfsTableLoadScanAllBlockInFsmPage(pageMgr, lfsMgr, fsmPageIdx);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }

    return STATUS_OK_INTER;
}

StatusInter LfsTableLoadScanAllFsmPage(PageMgrT *pageMgr, PageIdT fsmAddr, uint32_t offset)
{
    LfsMgrT *fsmMgr = NULL;
    StatusInter ret = FsmGetMgr(pageMgr, fsmAddr, offset, true, &fsmMgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get fsmMgr");
        return ret;
    }

    uint32_t fsmPageCnt = fsmMgr->fsmPageCnt;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;
    FsmLeaveMgr(pageMgr, fsmAddr, false);
    for (uint32_t i = 0; i < fsmPageCnt; i++) {
        PageIdT fsmPageId = SE_INVALID_PAGE_ADDR;
        ret = LfsGetFsmPageId(pageMgr, fsmMgr, i, &fsmPageId);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "get fsmPageIdx(%" PRIu32 ")", i);
            return ret;
        }
        uint8_t *dataPage = NULL;
        ret = HeapGetPageWithLoad(pageMgr, fsmPageId, &dataPage);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "load page scan fsmPageIdx(%" PRIu32 ")", i);
            return ret;
        }
        HeapLeavePageWithLoad(pageMgr, fsmPageId);
    }
    if (fsmPageCnt <= fsmPageIdNumsInMgr) {
        // no need to free lv1/lv2 fsmPage
        return STATUS_OK_INTER;
    }
    // try load all page table like LfsFreePageTable
    return STATUS_OK_INTER;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
