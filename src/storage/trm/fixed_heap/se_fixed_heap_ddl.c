/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author: baiyang
 * Create: 2024/10/14
 */
#include "se_fixed_heap_base.h"
#include "se_trx_mgr.h"
#include "adpt_spinlock.h"
#include "se_log.h"
#include "db_internal_error.h"
#include "se_dfx.h"
#include "se_lfsmgr.h"

#ifdef TS_MULTI_INST
#ifdef FEATURE_PERSISTENCE
#undef FEATURE_PERSISTENCE
#endif
#endif

#ifndef FEATURE_PERSISTENCE
static StatusInter FixedHeapFreeAllPages(SeInstanceT *seIns, FixedHeapT *fixedHeap)
{
    DB_POINTER(fixedHeap);
    MdMgrT *md = (MdMgrT *)(void *)seIns->pageMgr;
    StatusInter ret = LfsReleaseAllBlock(md, &fixedHeap->fsm, NULL, seIns->dbInstance);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "free all page (%" PRIu32 ")", fixedHeap->meta.fixedHeapFileId);
    }

    return ret;
}

static StatusInter FixedHeapCreateCachedPageList(MdMgrT *mdMgr, uint32_t tspIdx, uint32_t labelId)
{
    DB_POINTER(mdMgr);
    // 开启device归还才会创建缓存页链表
    StatusInter ret = STATUS_OK_INTER;
    if (mdMgr->enableReleaseDevice) {
        ret = MdCreateCachedPageList(mdMgr, tspIdx, labelId);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "create cache page list, spaceId = %" PRIu32 ", labelId = %" PRIu32, tspIdx, labelId);
        }
    }
    return ret;
}

static StatusInter FixedHeapClearCachedPageList(SeInstanceT *seIns, uint32_t tspIdx, uint32_t labelId, bool deleteList)
{
    DB_POINTER(seIns);
    // 开启device归还才清理缓存页链表
    StatusInter ret = STATUS_OK_INTER;
    MdMgrT *mdMgr = (MdMgrT *)seIns->pageMgr;
    if (mdMgr->enableReleaseDevice) {
        FreeCachedPageParamT chchedPagePara = SeInitCachedPageParam(tspIdx, labelId, seIns->dbInstance, NULL);
        ret = MdClearCachedPageList(mdMgr, &chchedPagePara, NULL, deleteList);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "fixed heap clear list, spcId = %" PRIu32 ", labelId = %" PRIu32, tspIdx, labelId);
        }
    }
    return ret;
}

StatusInter FixedHeapRealDropImpl(ShmemPtrT fixedHeapIns)
{
    // check whether the open count is zero
    FixedHeapT *fixedHeap = DbShmPtrToAddr(fixedHeapIns);
    if (fixedHeap == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATA_EXCEPTION, "segId:%" PRIu32 ", offset:%" PRIu32, fixedHeapIns.segId, fixedHeapIns.offset);
        return DATA_EXCEPTION_FIXED_HEAP_INVALID_PARAMETER;
    }

    Handle fixedHeapShmMemCtx = DbGetShmemCtxById(fixedHeap->shmMemCtxId, DbGetProcGlobalId());
    if (fixedHeapShmMemCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "shmMemCtxId %" PRIu32 " novalid", fixedHeap->shmMemCtxId);
        return OBJ_NOT_PREREQUISITE_SE_INVALID_POINTER;
    }
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(fixedHeap->meta.seInstanceId);
    if (seInstance == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "seInstanceId: %" PRIu16 ", labelId:%" PRIu32,
            fixedHeap->meta.seInstanceId, fixedHeap->meta.labelId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    MdMgrT *md = (MdMgrT *)(void *)seInstance->pageMgr;
    if (md == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "seInstanceId: %" PRIu16 ", labelId:%" PRIu32,
            fixedHeap->meta.seInstanceId, fixedHeap->meta.labelId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    DbRWSpinWLock(&fixedHeap->rwlock);
    StatusInter ret = FixedHeapFreeAllPages(seInstance, fixedHeap);
    if (ret != STATUS_OK_INTER) {
        DbRWSpinWUnlock(&fixedHeap->rwlock);
        SE_ERROR(ret, "labelId: %" PRIu32, fixedHeap->meta.labelId);
        // 内部有DbShmPtrToAddr的使用，因此此处不能DB_ASSERT，内部经过排查没有申请内存的
        return ret;
    }
    DbRWSpinWUnlock(&fixedHeap->rwlock);

    (void)LfsMgrDestroy(md, &fixedHeap->fsm, NULL);
    DB_LOG_INFO("Drop(labelId:%" PRIu32 ")", fixedHeap->meta.labelId);
    DbShmemCtxFree((DbMemCtxT *)fixedHeapShmMemCtx, fixedHeapIns);

    return STATUS_OK_INTER;
}

StatusInter FixedHeapJumpDropImpl(ShmemPtrT fixedHeapJumpIns)
{
    // check whether the open count is zero
    FixedHeapJumpT *fixedHeapJump = DbShmPtrToAddr(fixedHeapJumpIns);
    if (fixedHeapJump == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "segId:%" PRIu32 ", offset:%" PRIu32, fixedHeapJumpIns.segId,
            fixedHeapJumpIns.offset);
        return DATA_EXCEPTION_FIXED_HEAP_INVALID_PARAMETER;
    }

    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(fixedHeapJump->meta.seInstanceId);
    if (seInstance == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "seInstanceId: %" PRIu16 ", labelId:%" PRIu32,
            fixedHeapJump->meta.seInstanceId, fixedHeapJump->meta.labelId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    // get the memory context for fixed heaps
    Handle fixedHeapMemCtx = DbGetShmemCtxById(seInstance->fixedHeapShmMemCtxId, DbGetProcGlobalId());
    if (fixedHeapMemCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "fixedHeapShmMemCtxId %" PRIu32 " novalid",
            seInstance->fixedHeapShmMemCtxId);
        return OBJ_NOT_PREREQUISITE_SE_INVALID_POINTER;
    }

    (void)FixedHeapClearCachedPageList(
        seInstance, fixedHeapJump->meta.tableSpaceIndex, fixedHeapJump->meta.labelId, true);

    DB_LOG_INFO("Drop(labelId:%" PRIu32 ")", fixedHeapJump->meta.labelId);
    DbShmemCtxFree((DbMemCtxT *)fixedHeapMemCtx, fixedHeapJumpIns);

    return STATUS_OK_INTER;
}

StatusInter FixedHeapRealTruncate(MdMgrT *md, SeInstanceT *seInstance, FixedHeapT *fixedHeap, TrxMgrT *trxMgr)
{
    DbRWSpinWLock(&fixedHeap->rwlock);
    StatusInter ret =
        FixedHeapClearCachedPageList(seInstance, fixedHeap->meta.tableSpaceIndex, fixedHeap->meta.labelId, false);
    if (ret != STATUS_OK_INTER) {
        DbRWSpinWUnlock(&fixedHeap->rwlock);
        return ret;
    }
    ret = FixedHeapFreeAllPages(seInstance, fixedHeap);
    if (ret != STATUS_OK_INTER) {
        DbRWSpinWUnlock(&fixedHeap->rwlock);
        SE_ERROR(ret, "truncate fixedheap");
        return ret;
    }
    LfsMgrReset(md, &fixedHeap->fsm, seInstance->dbInstance);
    FixedHeapStatInitNewHeap(fixedHeap);
    DbRWSpinWUnlock(&fixedHeap->rwlock);
    fixedHeap->lastTruncateTrxId = TrxMgrGetAndIncMaxTrxId(trxMgr);
    DB_LOG_INFO("Truncate(labelId:%" PRIu32 ")", fixedHeap->meta.labelId);
    return STATUS_OK_INTER;
}
#endif

StatusInter FixedHeapDropImpl(ShmemPtrT fixedHeapJumpIns, bool isPersistent)
{
#ifdef FEATURE_PERSISTENCE
    return FixedHeapDoDrop(*(PageIdT *)&fixedHeapJumpIns, isPersistent);
#else
    FixedHeapJumpT *fixedHeapJump = DbShmPtrToAddr(fixedHeapJumpIns);
    if (fixedHeapJump == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "segId:%" PRIu32 ", offset:%" PRIu32, fixedHeapJumpIns.segId,
            fixedHeapJumpIns.offset);
        return DATA_EXCEPTION_FIXED_HEAP_INVALID_PARAMETER;
    }
    if (SECUREC_LIKELY(fixedHeapJump->isInit)) {
        (void)FixedHeapRealDropImpl(fixedHeapJump->fixedHeapIns);
    }
    return FixedHeapJumpDropImpl(fixedHeapJumpIns);
#endif
}

StatusInter FixedHeapTruncateImpl(ShmemPtrT fixedHeapJumpIns, bool isPersistent)
{
#ifdef FEATURE_PERSISTENCE
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    StatusInter ret = FixedHeapDoTruncate(*(PageIdT *)&fixedHeapJumpIns, isPersistent);
    if (ret != STATUS_OK_INTER) {
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }
    StatusInter redoRet = RedoLogEnd(redoCtx, true);
    if (redoRet != STATUS_OK_INTER) {
        return redoRet;
    }
    return STATUS_OK_INTER;
#else
    FixedHeapJumpT *fixedHeapJump = DbShmPtrToAddr(fixedHeapJumpIns);
    if (fixedHeapJump == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "segId:%" PRIu32 ", offset:%" PRIu32, fixedHeapJumpIns.segId,
            fixedHeapJumpIns.offset);
        return DATA_EXCEPTION_FIXED_HEAP_INVALID_PARAMETER;
    }

    if (SECUREC_UNLIKELY(!fixedHeapJump->isInit)) {
        return STATUS_OK_INTER;
    }

    FixedHeapT *fixedHeap = DbShmPtrToAddr(fixedHeapJump->fixedHeapIns);
    if (fixedHeap == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "segId:%" PRIu32 ", offset:%" PRIu32,
            fixedHeapJump->fixedHeapIns.segId, fixedHeapJump->fixedHeapIns.offset);
        return DATA_EXCEPTION_FIXED_HEAP_INVALID_PARAMETER;
    }
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(fixedHeap->meta.seInstanceId);
    if (seInstance == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "seInstanceId: %" PRIu16 ", labelId:%" PRIu32,
            fixedHeap->meta.seInstanceId, fixedHeap->meta.labelId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    TrxMgrT *trxMgr = (TrxMgrT *)DbShmPtrToAddr(seInstance->trxMgrShm);
    if (trxMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "(segid: %" PRIu32 " offset: %" PRIu32 "), labelId:%" PRIu32,
            seInstance->trxMgrShm.segId, seInstance->trxMgrShm.offset, fixedHeap->meta.labelId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    MdMgrT *md = (MdMgrT *)(void *)seInstance->pageMgr;
    if (md == NULL) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "fixedHeapFileId (%" PRIu32 ")", fixedHeap->meta.fixedHeapFileId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    return FixedHeapRealTruncate(md, seInstance, fixedHeap, trxMgr);
#endif
}

inline static StatusInter FixedGetMemCtx(uint32_t fixedHeapShmMemCtxId, Handle *fixedHeapMemCtx)
{
    // get the memory context for fixed heaps
    *fixedHeapMemCtx = DbGetShmemCtxById(fixedHeapShmMemCtxId, DbGetProcGlobalId());
    if (*fixedHeapMemCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "fixedHeapShmMemCtxId %" PRIu32 " novalid", fixedHeapShmMemCtxId);
        return OBJ_NOT_PREREQUISITE_SE_INVALID_POINTER;
    }
    return STATUS_OK_INTER;
}

StatusInter FixedHeapRealAlloc(FixedHeapMetaT *meta, ShmemPtrT *fixedHeapJumpIns)
{
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(meta->seInstanceId);
    if (seIns == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "instanceId:%" PRIu32 ".", meta->seInstanceId);
        return DATA_EXCEPTION_FIXED_HEAP_INVALID_PARAMETER;
    }

    // get the memory context for fixed heaps
    Handle fixedHeapMemCtx = NULL;
    StatusInter ret = FixedGetMemCtx(seIns->fixedHeapShmMemCtxId, &fixedHeapMemCtx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    // allocate memory for a new fixed heap instance
    FixedHeapT *fixedHeap = SeShmAlloc(fixedHeapMemCtx, sizeof(FixedHeapT), fixedHeapJumpIns);
    if (fixedHeap == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "shmMalloc fixed heap instance");
        return OUT_OF_MEMORY_MEM_FAILED;
    }

    // init members
    (void)memset_s(fixedHeap, sizeof(FixedHeapT), 0, sizeof(FixedHeapT));

    // init fixed heap instance
    DbRWSpinInit(&fixedHeap->rwlock);
    FixedHeapInitMeta(seIns, meta);

    fixedHeap->meta = *meta;
    fixedHeap->shmMemCtxId = seIns->fixedHeapShmMemCtxId;
    fixedHeap->fixedHeapStat.rowNum = 0;

    // create fsm
    FixedHeapInitLfs(seIns, meta, fixedHeap);
    DB_LOG_INFO("Init fixedheap. (label Id:%" PRIu32 ")", fixedHeap->meta.labelId);
    return STATUS_OK_INTER;
}

StatusInter FixedHeapLazyInit(FixedHeapJumpT *fixedHeapJump)
{
    DB_POINTER(fixedHeapJump);
    StatusInter ret = STATUS_OK_INTER;
    DbSpinLock(&fixedHeapJump->lock);
    if (SECUREC_LIKELY(!fixedHeapJump->isInit)) {
        ret = FixedHeapRealAlloc(&fixedHeapJump->meta, &fixedHeapJump->fixedHeapIns);
        if (SECUREC_LIKELY(ret == STATUS_OK_INTER)) {
            fixedHeapJump->isInit = true;
        }
    }
    DbSpinUnlock(&fixedHeapJump->lock);
    return ret;
}

void FixedHeapInitMeta(SeInstanceT *seIns, FixedHeapMetaT *meta)
{
    DB_POINTER2(seIns, meta);
    uint32_t pageSize = seIns->seConfig.pageSize * DB_KIBI;
    uint32_t pageHeadSize = (uint32_t)sizeof(FixedHeapPageHeadT);
    DB_ASSERT(meta->rowSize != 0);

    // determine the number of rows stored in a page
    uint32_t rowCnt = (pageSize - pageHeadSize) / meta->rowSize;  // without bitmapSize
    uint32_t bitmapSize = rowCnt / BITS_PER_BYTE;

    // adjust the number of rows in a page
    uint64_t reservedSize = pageHeadSize;
    uint64_t pageSizeByte = (uint64_t)pageSize;
    while ((reservedSize + HEAP_CALC_ALIGN_SIZE(bitmapSize) + (uint64_t)rowCnt * meta->rowSize) > pageSizeByte) {
        bitmapSize = (--rowCnt) / BITS_PER_BYTE;
    }

    while (rowCnt > bitmapSize * BITS_PER_BYTE) {
        rowCnt--;
    }

    DB_LOG_DEBUG("numRows=%" PRIu32 ", bitmap: %" PRIu32 " bits, %" PRIu32 ", rowSize=%" PRIu32 "", rowCnt,
        (bitmapSize * BITS_PER_BYTE), HEAP_CALC_ALIGN_SIZE(bitmapSize), meta->rowSize);

    meta->pageRowCnt = (uint16_t)rowCnt;
    meta->bitmapSize = (uint16_t)bitmapSize;
    meta->fixedHeapFileId = SeGetNewTrmId(seIns);
    meta->fixedHeapFsmFileId = SeGetNewTrmId(seIns);
    DB_ASSERT(rowCnt == bitmapSize * BITS_PER_BYTE);
}

void FixedHeapInitLfs(const SeInstanceT *seIns, const FixedHeapMetaT *meta, FixedHeapT *fixedHeap)
{
    DB_POINTER3(seIns, meta, fixedHeap);

    uint32_t pageSize = seIns->seConfig.pageSize * DB_KIBI;
    uint32_t fixedRowSize = FixedHeapGetAlignRowAllSize(meta->rowDataSize);
    uint32_t availSize = (pageSize - (uint32_t)sizeof(FixedHeapPageHeadT)) - HEAP_CALC_ALIGN_SIZE(meta->bitmapSize);

    uint32_t newRevSize = pageSize - (meta->pageRowCnt * fixedRowSize);
#ifdef FEATURE_PERSISTENCE
    LfsCfgT lfsCfg = {
        .fsmFileId = meta->fixedHeapFsmFileId,
        .dataFileId = meta->fixedHeapFileId,
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .pageSize = pageSize,
        .needLock = true,
        .isPersistent = meta->isPersistent,
        .availSize = availSize,
        .reserveSize = (uint16_t)newRevSize,
        .tableSpaceIndex = meta->tableSpaceIndex,
        .mgrOffset = sizeof(PageHeadT) + offsetof(FixedHeapT, fsm),
        .mgrPageId = ((PageHeadT *)((uint8_t *)fixedHeap - sizeof(PageHeadT)))->addr,
    };
#else
    LfsCfgT lfsCfg = {.fsmFileId = meta->fixedHeapFsmFileId,
        .dataFileId = meta->fixedHeapFileId,
        .shmArrayMemCtxId = DB_SE_HEAP_SHM_CTX_ID,
        .labelId = meta->labelId,
        .pageType = HEAP_FIX_LEN_ROW_PAGE,
        .pageSize = pageSize,
        .needLock = true,
        .isUseRsm = false,
        .availSize = availSize,
        .reserveSize = (uint16_t)newRevSize,
        .tableSpaceIndex = meta->tableSpaceIndex};

#endif
    LfsMgrInit(&fixedHeap->fsm, &lfsCfg);
}

StatusInter FixedHeapCreateImpl(FixedHeapMetaT *meta, ShmemPtrT *fixedHeapJumpIns, FixedHeapT **topoAddrCache)
{
    DB_POINTER2(meta, fixedHeapJumpIns);
    if (meta->rowDataSize == 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "rowDataSize is 0");
        return DATA_EXCEPTION_FIXED_HEAP_INVALID_PARAMETER;
    }

    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(meta->seInstanceId);
    if (seIns == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "instanceId:%" PRIu32 ".", meta->seInstanceId);
        return DATA_EXCEPTION_FIXED_HEAP_INVALID_PARAMETER;
    }

    if (meta->rowSize == 0) {
        meta->rowSize = FixedHeapGetAlignRowAllSize(meta->rowDataSize);
    }

#ifdef FEATURE_PERSISTENCE
    return FixedHeapDoCreate(seIns, meta, fixedHeapJumpIns, topoAddrCache);
#else
    // 内存态不能设置为持久化表
    if (meta->isPersistent) {
        SE_LAST_ERROR(DATA_EXCEPTION_FIXED_HEAP_INVALID_PARAMETER, "create persist fixed heap.");
        return DATA_EXCEPTION_FIXED_HEAP_INVALID_PARAMETER;
    }
    if (meta->labelId == SE_INVALID_LABEL_ID) {
        SE_LAST_ERROR(DATA_EXCEPTION_FIXED_HEAP_INVALID_PARAMETER, "fix heap labelId = %" PRIu32, meta->labelId);
        return DATA_EXCEPTION_FIXED_HEAP_INVALID_PARAMETER;
    }

    // get the memory context for fixed heaps
    Handle fixedHeapMemCtx = NULL;
    StatusInter ret = FixedGetMemCtx(seIns->fixedHeapShmMemCtxId, &fixedHeapMemCtx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    FixedHeapJumpT *fixedJump = SeShmAlloc(fixedHeapMemCtx, sizeof(FixedHeapJumpT), fixedHeapJumpIns);
    if (fixedJump == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "shmMalloc fixed heap jump instance");
        return OUT_OF_MEMORY_MEM_FAILED;
    }

    (void)memset_s(fixedJump, sizeof(FixedHeapJumpT), 0, sizeof(FixedHeapJumpT));
    fixedJump->fixedHeapIns = DB_INVALID_SHMPTR;
    fixedJump->meta = *meta;
    fixedJump->isInit = false;
    DbSpinInit(&fixedJump->lock);
    ret = FixedHeapCreateCachedPageList((MdMgrT *)seIns->mdMgr, meta->tableSpaceIndex, meta->labelId);
    if (ret != STATUS_OK_INTER) {
        DbShmemCtxFree(fixedHeapMemCtx, *fixedHeapJumpIns);
        *fixedHeapJumpIns = DB_INVALID_SHMPTR;
        SE_ERROR(ret, "fixed heap create cache list, spaceId = %" PRIu32 ", labelId = %" PRIu32, meta->tableSpaceIndex,
            meta->labelId);
    }
    return ret;
#endif
}

StatusInter FixedHeapMemGet(ShmemPtrT fixedHeapJumpIns, FixedHeapT **fixedHeap)
{
    FixedHeapJumpT *fixedHeapJump = DbShmPtrToAddr(fixedHeapJumpIns);
    if (fixedHeapJump == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "segId:%" PRIu32 ", offset:%" PRIu32, fixedHeapJumpIns.segId,
            fixedHeapJumpIns.offset);
        return DATA_EXCEPTION_FIXED_HEAP_INVALID_PARAMETER;
    }
    if (SECUREC_UNLIKELY(!fixedHeapJump->isInit)) {
        StatusInter ret = FixedHeapLazyInit(fixedHeapJump);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }

    *fixedHeap = DbShmPtrToAddr(fixedHeapJump->fixedHeapIns);
    if (*fixedHeap == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "SE_LAST_ERRORsegId:%" PRIu32 ", offset:%" PRIu32,
            fixedHeapJump->fixedHeapIns.segId, fixedHeapJump->fixedHeapIns.offset);
        return DATA_EXCEPTION_FIXED_HEAP_INVALID_PARAMETER;
    }
    return STATUS_OK_INTER;
}

#ifdef FEATURE_PERSISTENCE
inline static StatusInter CacheGetFixedHeap(
    FixedHeapOpenCfgT *cfg, PageMgrT *pageMgr, FixedHeapT **fixedHeap, bool hasCachePage)
{
    PageIdT fixedHeapAddr = *(PageIdT *)(void *)&(cfg->edgeLabel->topoShmAddr);
    if (hasCachePage) {
        *fixedHeap = (FixedHeapT *)cfg->edgeLabel->topoAddrCache;
        return STATUS_OK_INTER;
    } else {
        return GetFixedHeap(pageMgr, fixedHeapAddr, false, fixedHeap);
    }
}
#endif

inline static void CacheLeaveFixedHeap(PageMgrT *pageMgr, PageIdT fixedHeapAddr, bool isChanged, bool useCache)
{
    if (useCache) {
        return;
    }
    return LeaveFixedHeap(pageMgr, fixedHeapAddr, false);
}

StatusInter FixedHeapHandleAllocAndInit(
    FixedHeapOpenCfgT *cfg, SeRunCtxT *seCtx, FixedHpOpTypeE opType, FixedHeapRunCtxT **ctx, DbMemCtxT *usrMemCtx)
{
    StatusInter ret = STATUS_OK_INTER;
#ifdef FEATURE_PERSISTENCE
    PageMgrT *pageMgr = (PageMgrT *)(cfg->edgeLabel->metaCommon.isPersistent ? seCtx->pageMgr : seCtx->mdMgr);
    FixedHeapT *fixedHeap = NULL;
    bool hasCachePage = (cfg->edgeLabel->topoAddrCache != NULL) && !seCtx->resSessionCtx.isDirectRead;
    if ((ret = CacheGetFixedHeap(cfg, pageMgr, &fixedHeap, hasCachePage)) != STATUS_OK_INTER) {
        SE_ERROR(ret, "get fixed heap.");
        return ret;
    }
#else
    PageMgrT *pageMgr = (PageMgrT *)seCtx->mdMgr;
    FixedHeapT *fixedHeap = NULL;
    ret = FixedHeapMemGet(cfg->fixedHeapIns, &fixedHeap);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
#endif
    // allocate runtime ctx from dynamic memory and free in FixedHeapClose
    FixedHeapRunCtxT *newCtx = DbDynMemCtxAlloc(seCtx->sessionMemCtx, sizeof(FixedHeapRunCtxT));
    if (newCtx == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_MEM_FAILED, "alloc fixed heap runCtx.");
        return OUT_OF_MEMORY_MEM_FAILED;
    }
    (void)memset_s(newCtx, sizeof(FixedHeapRunCtxT), 0, sizeof(FixedHeapRunCtxT));

    // init members
    // FixHeapInitFsmRunCtx中会用到newCtx的其他字段，所以这里初始化顺序不能改
    newCtx->pageMgr = pageMgr;
    newCtx->seCtx = seCtx;
    newCtx->usrMemCtx = usrMemCtx;
    newCtx->opType = opType;
    newCtx->cursorCnt = 0;
    newCtx->oldTrxId = DB_INVALID_TRX_ID;
    newCtx->oldRollPtr = DB_INVALID_UINT64;
    newCtx->oldIsDeleted = false;
    newCtx->isToGetOldestVisibleBuf = false;
    newCtx->meta = fixedHeap->meta;
#ifdef FEATURE_PERSISTENCE
    newCtx->fixedHeapAddr = *(PageIdT *)&(cfg->edgeLabel->topoShmAddr);
    ret = FixHeapInitFsmRunCtx(newCtx, newCtx->fixedHeapAddr, fixedHeap);
    CacheLeaveFixedHeap(pageMgr, newCtx->fixedHeapAddr, false, hasCachePage);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "malloc and init fsm ctx");
        DbDynMemCtxFree(seCtx->sessionMemCtx, newCtx);
        return ret;
    }
#else
    // 这两个成员不可放在持久化下的 FixedHeapRunCtxT 里
    newCtx->fixedHeap = fixedHeap;
    newCtx->fsmMgr = &fixedHeap->fsm;
#endif
    *ctx = newCtx;
    return STATUS_OK_INTER;
}

StatusInter FixedHeapHandleAllocAndInitForTrxMerge(
    FixedHeapOpenCfgT *cfg, SeRunCtxT *seCtx, FixedHpOpTypeE opType, FixedHeapRunCtxT **ctx, DbMemCtxT *usrMemCtx)
{
    StatusInter ret = STATUS_OK_INTER;
    FixedHeapT *fixedHeap = NULL;
#ifdef FEATURE_PERSISTENCE
    PageMgrT *pageMgr = (PageMgrT *)(cfg->edgeLabel->metaCommon.isPersistent ? seCtx->pageMgr : seCtx->mdMgr);
    bool hasCachePage = (cfg->edgeLabel->topoAddrCache != NULL) && !seCtx->resSessionCtx.isDirectRead;
    if ((ret = CacheGetFixedHeap(cfg, pageMgr, &fixedHeap, hasCachePage)) != STATUS_OK_INTER) {
        SE_ERROR(ret, "get fixed heap.");
        return ret;
    }
#else
    PageMgrT *pageMgr = (PageMgrT *)seCtx->mdMgr;
    if ((ret = FixedHeapMemGet(cfg->fixedHeapIns, &fixedHeap)) != STATUS_OK_INTER) {
        return ret;
    }
#endif
    void *memctx =
        (((TrxT *)seCtx->trx)->trxCloneMemCtx == NULL) ? seCtx->sessionMemCtx : ((TrxT *)seCtx->trx)->trxCloneMemCtx;
    // allocate runtime ctx from dynamic memory and free in FixedHeapClose
    FixedHeapRunCtxT *newCtx = DbDynMemCtxAlloc(memctx, sizeof(FixedHeapRunCtxT));
    if (newCtx == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_MEM_FAILED, "alloc fixed heap runCtx.");
        return OUT_OF_MEMORY_MEM_FAILED;
    }
    (void)memset_s(newCtx, sizeof(FixedHeapRunCtxT), 0, sizeof(FixedHeapRunCtxT));

    // init members
    // FixHeapInitFsmRunCtx中会用到newCtx的其他字段，所以这里初始化顺序不能改
    newCtx->pageMgr = pageMgr;
    newCtx->seCtx = seCtx;
    newCtx->usrMemCtx = usrMemCtx;
    newCtx->opType = opType;
    newCtx->cursorCnt = 0;
    newCtx->oldTrxId = DB_INVALID_TRX_ID;
    newCtx->oldRollPtr = DB_INVALID_UINT64;
    newCtx->oldIsDeleted = false;
    newCtx->isToGetOldestVisibleBuf = false;
    newCtx->meta = fixedHeap->meta;
#ifdef FEATURE_PERSISTENCE
    newCtx->fixedHeapAddr = *(PageIdT *)&(cfg->edgeLabel->topoShmAddr);
    ret = FixHeapInitFsmRunCtx(newCtx, newCtx->fixedHeapAddr, fixedHeap);
    CacheLeaveFixedHeap(pageMgr, newCtx->fixedHeapAddr, false, hasCachePage);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "malloc and init fsm ctx");
        DbDynMemCtxFree(memctx, newCtx);
        return ret;
    }
#else
    // 这两个成员不可放在持久化下的 FixedHeapRunCtxT 里
    newCtx->fixedHeap = fixedHeap;
    newCtx->fsmMgr = &fixedHeap->fsm;
#endif
    *ctx = newCtx;
    return STATUS_OK_INTER;
}

// page format: [page hdr | bitmap | data rows | page tail]
//  row format: [row hdr | data]
void FixedHeapInitPageHead(FixedHeapPageHeadT *pageHead, const FixedHeapMetaT *meta)
{
    // skip page head and bitmap
    pageHead->bitmapPos = (uint16_t)HEAP_ALIGN_SIZE_OF(FixedHeapPageHeadT);
    pageHead->firstRowPos = (uint16_t)(pageHead->bitmapPos + HEAP_CALC_ALIGN_SIZE((uint32_t)meta->bitmapSize));
    pageHead->capacity = meta->pageRowCnt;
    pageHead->freeSlotCnt = meta->pageRowCnt;
    pageHead->rowSize = (uint16_t)HEAP_CALC_ALIGN_SIZE(meta->rowDataSize + (uint32_t)sizeof(FixedHeapRowHeadT));
    pageHead->baseHead.pageHead.freeSize = meta->pageRowCnt * pageHead->rowSize;
    pageHead->baseHead.pageHead.pageState = PAGE_USING;
    SePageHeadSetType(&pageHead->baseHead.pageHead, PERSISTENCE_PAGE_TYPE_FIXED_HEAP_DATA);
    HEAP_ASSERT_ALIGNED(pageHead->bitmapPos);
    HEAP_ASSERT_ALIGNED(pageHead->firstRowPos);
    HEAP_ASSERT_ALIGNED(pageHead->rowSize);

    // init bitmap
    uint8_t *bitmap = (uint8_t *)((uintptr_t)pageHead + pageHead->bitmapPos);
    uint32_t size = HEAP_CALC_ALIGN_SIZE(meta->bitmapSize);
    (void)memset_s(bitmap, size, 0, size);
    // assert number of bits and number of rows
    DB_ASSERT(meta->bitmapSize * BITS_PER_BYTE == pageHead->capacity);
}
