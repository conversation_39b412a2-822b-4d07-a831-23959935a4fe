/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: Heap 接口实现
 * 调用层次划分: se_heap_access.c 感知模型
 *             se_heap.c 不感知模型层, 处理连续buffer的存储, 负责调用fsm, memData等其他模块
 *             se_heap_page.c 负责单个page上的操作
 * Author: panghaisheng
 * Create: 2020-8-12
 */
#include "se_heap_mem_inner.h"
#include "se_heap_access_dm.h"
#include "se_heap_slice_row.h"
#include "se_heap_addr.h"
#include "se_undo.h"
#include "se_trx_mgr.h"
#include "se_heap_stats.h"
#include "se_heap_fetch.h"
#include "se_heap_trx_acc_inner.h"
#include "se_log.h"
#include "adpt_spinlock.h"
#include "se_heap_fixed.h"
#include "se_heap_utils.h"
#include "se_undo_trx_resource.h"

#include "se_heap_common.h"
#include "se_heap_am_inner.h"

#ifdef __cplusplus
extern "C" {
#endif

#define RETRY_TIMES_ALLOWED_FOR_CUR_PAGE 10

typedef struct TagHeapPageListPara {
    bool isUseRsm;
    uint32_t tableSpaceIndex;
    uint32_t labelId;
} HeapPageListParaT;

static StatusInter HeapClearCachedPageList(SeInstanceT *seIns, HeapPageListParaT *para, bool deleteList)
{
    DB_POINTER2(seIns, para);
    /*
    1.仅开启device归还才清理缓存页链表
    2.保留内存不支持缓存页链表
    */
    StatusInter ret = STATUS_OK_INTER;
    MdMgrT *mdMgr = (MdMgrT *)seIns->mdMgr;
    if (mdMgr->enableReleaseDevice && !para->isUseRsm) {
        FreeCachedPageParamT cachedPagePara =
            SeInitCachedPageParam(para->tableSpaceIndex, para->labelId, seIns->dbInstance, NULL);
        ret = MdClearCachedPageList(mdMgr, &cachedPagePara, NULL, deleteList);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "heap clear list, spcId=%" PRIu32 ", labelId=%" PRIu32, para->tableSpaceIndex, para->labelId);
        }
    }
    return ret;
}

static StatusInter HeapCreateCachedPageList(MdMgrT *mdMgr, HeapJumpT *heapJump)
{
    DB_POINTER2(mdMgr, heapJump);
    /*
    1.仅开启device归还才会创建缓存页链表
    2.保留内存不支持缓存页链表
    */
    StatusInter ret = STATUS_OK_INTER;
    if (mdMgr->enableReleaseDevice && !heapJump->cfg.isUseRsm) {
        ret = MdCreateCachedPageList(mdMgr, heapJump->cfg.tableSpaceIndex, heapJump->cfg.labelId);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "create cache page list, spaceId = %" PRIu32 ", labelId = %" PRIu32,
                heapJump->cfg.tableSpaceIndex, heapJump->cfg.labelId);
        }
    }
    return ret;
}

void HeapMarkDeleteModiRowInfo(const HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    // 修改row上的信息: 更新事务字段信息, 设置删除标记位
    DB_POINTER2(ctx, opInfo);
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    if (ctx->hpControl.isFixPage) {
        HeapMarkDeleteModiFixRowInfo(fetchRowInfo, opInfo);
    } else if (fetchRowInfo->srcRowInfo.rowType == HEAP_VAR_ROW_NORMAL_ROW) {
        HpNormalRowHeadT *normalRowHead = fetchRowInfo->srcRowInfo.rowHeadPtr.normalRowHead;
        normalRowHead->rowState.isDeleted = true;
        normalRowHead->trxInfo = opInfo->allocRowInfo->newTrxInfo;
    } else {
        HpLinkRowHeadT *linkRowHead = fetchRowInfo->srcRowInfo.rowHeadPtr.linkRowHead;
        linkRowHead->rowState.isDeleted = true;
        linkRowHead->srcTrxInfo = opInfo->allocRowInfo->newTrxInfo;
    }
}

StatusInter HeapDeleteRowSetMarkImpl(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    uint64_t newRollPtr = 0;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    // 修改前, 获取事务字段的信息: 记录undo信息获取新的事务信息 或者 获取回滚的事务信息
    // Normal模式下: HeapUndoLogForUpdOrDel (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
    StatusInter ret = HeapLogUndoFunc(ctx, (uint32_t)TRX_OP_DELETE, opInfo, &newRollPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    // Normal模式下: HeapPrepareTrxInfoForUpdOrDel (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
    ret = HeapSaveTrxInfoFunc(ctx, TRX_OP_DELETE, opInfo, newRollPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    fetchRowInfo->isUndoOperationOk = true;

#ifndef NDEBUG
    HpRowStateT *rowState = fetchRowInfo->srcRowInfo.rowHeadPtr.rowState;
    if (SeTransGetTrxType(ctx->seRunCtx) == PESSIMISTIC_TRX) {
        DB_ASSERT(!rowState->isDeleted);
    }
#endif
    HeapMarkDeleteModiRowInfo(ctx, opInfo);

    DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP, "MarkDelete label %" PRIu32 " row (%" PRIu32 ",%" PRIu32 "), trxId:%" PRIu64,
        ctx->heapCfg.labelId, fetchRowInfo->srcRowInfo.itemPtr.pageId, fetchRowInfo->srcRowInfo.itemPtr.slotId,
        ((TrxT *)ctx->seRunCtx->trx)->base.trxId);

    return ret;
}

void HeapInitLfs(const SeInstanceT *seInstance, HeapT *newHeap, PageTotalSizeT pageSize)
{
    DB_POINTER2(seInstance, newHeap);

    _Static_assert(sizeof(HVPageHeadT) == HEAP_ALIGN_SIZE_OF(HVPageHeadT), "The page header structure must be aligned");

    uint32_t availSize = GetHeapPageAvailSize(newHeap, pageSize);
    LfsCfgT lfsCfg = {.fsmFileId = newHeap->constInfo.heapFsmTrmId,
        .dataFileId = newHeap->constInfo.heapTrmId,
        .shmArrayMemCtxId = DB_SE_HEAP_SHM_CTX_ID,
        .labelId = newHeap->constInfo.labelId,
        .pageSize = pageSize,
        .pageType = newHeap->constInfo.pageType,
        .needLock = !DmIsLabelLatchMode(newHeap->constInfo.ccType),  // 表锁模式下，lfs操作可以不用加锁
        .isUseRsm = newHeap->constInfo.isUseRsm,
        .reserveSize = (uint16_t)HEAP_PAGE_FSM_RESERVED_SIZE(pageSize),
        .availSize = availSize,
        .tableSpaceIndex = newHeap->constInfo.tableSpaceIndex,
        .fsmTableSpaceIndex = newHeap->constInfo.fsmTableSpaceIndex};
    if (newHeap->constInfo.pageType == HEAP_FIX_LEN_ROW_PAGE) {
        DB_ASSERT(newHeap->regularInfo.maxRowSize < DB_MAX_UINT16);
        lfsCfg.reserveSize =
            HeapPageGetMinRowSize((PageSizeT)newHeap->regularInfo.maxRowSize, HEAP_FIX_ROW_NORMAL_ROW) +
            newHeap->constInfo.slotExtendSize;
    }

    LfsMgrInit(&newHeap->fsm, &lfsCfg);
}

Status HeapLabelDowngradeFetchPageImpl(HpRunHdlT ctx, HeapScanCursorHdlT cursor, bool *isScanEnd)
{
    return GMERR_OK;
}

void HeapCreateInitConstMember(
    const HeapAccessCfgT *heapCfg, PageTotalSizeT pageSize, SeInstanceT *seInstance, HeapT *newHeap)
{
    HeapConstInfoT *constInfo = &newHeap->constInfo;
    constInfo->pageType = heapCfg->pageType;
    constInfo->tupleType = heapCfg->tupleType;
    constInfo->slotExtendSize = heapCfg->slotExtendSize;
    constInfo->heapShmMemCtxId = seInstance->heapShmMemCtxId;
    constInfo->isYangBigStore = heapCfg->isYangBigStore;
    constInfo->isStatusMergeSubs = heapCfg->isStatusMergeSubs;
    constInfo->isPersistent = heapCfg->isPersistent;
    // 目前heap_mem暂不支持持久化，默认使用全量dfx功能
    constInfo->isLabelLockSerializable = heapCfg->isLabelLockSerializable;
    constInfo->verboseDfx = true;
    constInfo->isUseRsm = heapCfg->isUseRsm;
    constInfo->ccType = heapCfg->ccType;
    constInfo->trxType = heapCfg->trxType;
    constInfo->isolation = heapCfg->isolation;
    constInfo->labelId = heapCfg->labelId;
    constInfo->tableSpaceId = heapCfg->tableSpaceId;
    constInfo->tableSpaceIndex = heapCfg->tableSpaceIndex;
    constInfo->fsmTableSpaceIndex = heapCfg->fsmTableSpaceIndex;
    constInfo->maxItemNum = heapCfg->maxItemNum;
    constInfo->maxRowRawSizeInPage = HeapGetMaxRowRawSize(newHeap, pageSize);
    HEAP_ASSERT_ALIGNED(constInfo->maxRowRawSizeInPage);  // 每个分片应该是对齐的 (4字节)
    constInfo->heapTrmId = heapCfg->heapFileId;
    constInfo->heapFsmTrmId = heapCfg->heapFsmFileId;
}

void HeapRuntimeInfoInit(HeapRuntimeInfoT *runtimeInfo)
{
    runtimeInfo->cachePageAvail = false;
    runtimeInfo->cachePageAddr = EmptyPageAddrInfoT();
    runtimeInfo->cachePageFreeSize = 0;
    runtimeInfo->cachePageMaxRowSize = FSM_INVALID_MAX_ROW_SIZE;
    runtimeInfo->totalCursorNum = 0;
    runtimeInfo->cacheVersion = 0;
    runtimeInfo->rsmMigrationVersion = 0;
    DbSpinInit(&runtimeInfo->scanCursorLock);
}

static StatusInter HeapCreateInitRuntimeInfo(const ShmemPtrT baseShmAddr, ShmemPtrT *heapRuntimeInfoPtr)
{
    ShmemPtrT runtimeInfoPtr = (ShmemPtrT){
        .segId = baseShmAddr.segId,
        .offset = baseShmAddr.offset + sizeof(HeapT),
    };
    HeapRuntimeInfoT *runtimeInfo = (HeapRuntimeInfoT *)DbShmPtrToAddr(runtimeInfoPtr);
    if (runtimeInfo == NULL) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "heapRuntimeInfo, segId:%" PRIu32 ", offset:%" PRIu32,
            runtimeInfoPtr.segId, runtimeInfoPtr.offset);
        return INVALID_PARAMETER_VALUE_INTER;
    }
    HeapRuntimeInfoInit(runtimeInfo);
    runtimeInfo->totalCursorNum = 0;
    for (uint32_t i = 0; i < HEAP_SCAN_CURSOR_OPENER_TOP_3; i++) {
        runtimeInfo->cursorOpenerInit[i] = false;
    }
    runtimeInfo->allocCursorFailTimes = 0;
    *heapRuntimeInfoPtr = runtimeInfoPtr;
    return STATUS_OK_INTER;
}

static StatusInter HeapCreateInitPerfStat(const HeapJumpT *heapJump, ShmemPtrT *heapPerfStatShmPtr)
{
    ShmemPtrT perfStatPtr = (ShmemPtrT){
        .segId = heapJump->heapShmAddr.segId,
        .offset = heapJump->heapShmAddr.offset + sizeof(HeapT) + sizeof(HeapRuntimeInfoT),
    };
    HeapPerfStatT *perfStat = (HeapPerfStatT *)DbShmPtrToAddr(perfStatPtr);
    if (perfStat == NULL) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "perfStat, segId:%" PRIu32 ", offset:%" PRIu32, perfStatPtr.segId,
            perfStatPtr.offset);
        return INVALID_PARAMETER_VALUE_INTER;
    }
    DbSpinInit(&perfStat->lock);
    *heapPerfStatShmPtr = perfStatPtr;
    return STATUS_OK_INTER;
}

StatusInter HeapCreateInitNewHeap(SeInstanceT *seInstance, const HeapJumpT *heapJump, HeapT *newHeap)
{
    DB_POINTER3(seInstance, heapJump, newHeap);
    if (heapJump->cfg.pageType >= HEAP_INVALID_PAGE_TYPE) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "novalid page type %" PRId32 "", (int32_t)heapJump->cfg.pageType);
        return INVALID_PARAMETER_VALUE_INTER;
    }
    newHeap->magicNum = HEAP_VALID_MAGIC_NUM;
    PageTotalSizeT pageSize = (PageTotalSizeT)seInstance->seConfig.pageSize * DB_KIBI;
    HeapCreateInitConstMember(&heapJump->cfg, pageSize, seInstance, newHeap);
    StatusInter ret = HeapCreateInitRegularMember(&heapJump->cfg, pageSize, newHeap);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    ret = HeapCreateInitRuntimeInfo(heapJump->heapShmAddr, &newHeap->runtimeInfoShmPtr);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    ret = HeapCreateInitPerfStat(heapJump, &newHeap->perfStatShmPtr);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    newHeap->lockStatShmPtr = heapJump->lockStatShmPtr;
    HeapInitLfs(seInstance, newHeap, pageSize);
    return STATUS_OK_INTER;
}

typedef struct TagHeapShmemGroup {
    HeapT heap;
    HeapRuntimeInfoT heapRuntimeInfo;
    HeapPerfStatT heapPerfStat;
} HeapShmemGroupT;

StatusInter HeapLazyInit(HeapJumpT *heapJump)
{
    DB_POINTER(heapJump);

    StatusInter ret = STATUS_OK_INTER;
    DbSpinLock(&heapJump->lock);
    if (heapJump->isInit) {
        goto EXIT;
    }

    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(heapJump->cfg.seInstanceId);
    if (seInstance == NULL) {
        ret = INVALID_PARAMETER_VALUE_INTER;
        SE_LAST_ERROR(ret, "NULL seInstance:%" PRIu16, heapJump->cfg.seInstanceId);
        goto EXIT;
    }

    DbMemCtxT *heapMemCtx = (DbMemCtxT *)DbGetShmemCtxById(seInstance->heapShmMemCtxId, heapJump->cfg.seInstanceId);
    if (heapMemCtx == NULL) {
        ret = UNEXPECTED_NULL_VALUE_INTER;
        SE_LAST_ERROR(ret, "memCtxId %" PRIu32 " novalid", seInstance->heapShmMemCtxId);
        goto EXIT;
    }

    // 申请创建heap容器所需共享内存, 在删除heap的时候释放
    HeapT *heap = SeShmAlloc(heapMemCtx, sizeof(HeapShmemGroupT), &heapJump->heapShmAddr);
    if (heap == NULL) {
        ret = OUT_OF_MEMORY_INTER;
        SE_LAST_ERROR(ret, "shmMalloc heap");
        goto EXIT;
    }
    (void)memset_sp((void *)heap, sizeof(HeapShmemGroupT), 0, sizeof(HeapShmemGroupT));

    ret = HeapCreateInitNewHeap(seInstance, heapJump, heap);
    if (ret != STATUS_OK_INTER) {
        DbShmemCtxFree(heapMemCtx, heapJump->heapShmAddr);
        heapJump->heapShmAddr = DB_INVALID_SHMPTR;
        goto EXIT;
    }
    heapJump->isInit = true;
EXIT:
    DbSpinUnlock(&heapJump->lock);
    return ret;
}

StatusInter HeapCreate(SeRunCtxHdT seRunCtx, const HeapAccessCfgT *heapCfg, ShmemPtrT *heapShmAddr)
{
    DB_UNUSED(seRunCtx);
    DB_POINTER2(heapCfg, heapShmAddr);
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(heapCfg->seInstanceId);
    if (seInstance == NULL) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "NULL seInstance:%" PRIu16, heapCfg->seInstanceId);
        return INVALID_PARAMETER_VALUE_INTER;
    }

    DbMemCtxT *heapMemCtx = (DbMemCtxT *)DbGetShmemCtxById(seInstance->heapShmMemCtxId, heapCfg->seInstanceId);
    if (heapMemCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "inv memCtxId:%" PRIu32, seInstance->heapShmMemCtxId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    if (heapCfg->pageType >= HEAP_INVALID_PAGE_TYPE) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "page type %" PRId32, (int32_t)heapCfg->pageType);
        return INVALID_PARAMETER_VALUE_INTER;
    }

    if (heapCfg->pageType == HEAP_FIX_LEN_ROW_PAGE) {
        PageTotalSizeT pageSize = (PageTotalSizeT)seInstance->seConfig.pageSize * DB_KIBI;
        if (heapCfg->fixRowSize > SeGetUndoPageMaxRecordSize(pageSize)) {
            SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "row size %" PRIu16, heapCfg->fixRowSize);
            return INVALID_PARAMETER_VALUE_INTER;
        }
    }
    if (heapCfg->labelId == SE_INVALID_LABEL_ID) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "heap labelId = %" PRId32, heapCfg->labelId);
        return INVALID_PARAMETER_VALUE_INTER;
    }
    // 申请创建heap容器中间体所需共享内存, 在删除heap的时候释放
    HeapJumpT *newHeapJump = SeShmAlloc(heapMemCtx, sizeof(HeapJumpT), heapShmAddr);
    if (newHeapJump == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "heap shmMalloc");
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_sp((void *)newHeapJump, sizeof(HeapJumpT), 0, sizeof(HeapJumpT));
    newHeapJump->cfg = *heapCfg;
    newHeapJump->cfg.heapFileId = SeGetNewTrmId(seInstance);
    newHeapJump->cfg.heapFsmFileId = SeGetNewTrmId(seInstance);
    newHeapJump->heapShmAddr = DB_INVALID_SHMPTR;
    newHeapJump->lockStatShmPtr = DB_INVALID_SHMPTR;
    newHeapJump->isInit = false;
    DbSpinInit(&newHeapJump->lock);
    StatusInter ret = HeapCreateCachedPageList((MdMgrT *)seInstance->mdMgr, newHeapJump);
    if (ret != STATUS_OK_INTER) {
        DbShmemCtxFree(heapMemCtx, *heapShmAddr);
        *heapShmAddr = DB_INVALID_SHMPTR;
        return ret;
    }
    return STATUS_OK_INTER;
}

void HeapReleaseAllBlock(SeInstanceT *seIns, HeapT *heap, RsmUndoRecordT *rsmUndoRec)
{
    MdMgrT *md = (MdMgrT *)seIns->mdMgr;
    StatusInter ret = LfsReleaseAllBlock(md, &heap->fsm, rsmUndoRec, seIns->dbInstance);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "free all page unsucc (%" PRIu32 ")", heap->constInfo.heapTrmId);
    }
    (void)LfsMgrDestroy(md, &heap->fsm, seIns->dbInstance);
}

static inline void HeapFreeLockStat(DbMemCtxT *heapMemCtx, HeapT *heap, HeapJumpT *heapJump)
{
    if (DbIsShmPtrValid(heap->lockStatShmPtr)) {
        DbShmemCtxFree(heapMemCtx, heap->lockStatShmPtr);
        heap->lockStatShmPtr = DB_INVALID_SHMPTR;
        heapJump->lockStatShmPtr = DB_INVALID_SHMPTR;
    }
}

StatusInter HeapDrop(SeRunCtxT *seRunCtx, HeapCntrAcsInfoT *heapCntrAcsInfo)
{
    DB_UNUSED(seRunCtx);
    HeapJumpT *heapJump = (HeapJumpT *)DbShmPtrToAddr(heapCntrAcsInfo->heapShmAddr);
    if (SECUREC_UNLIKELY(heapJump == NULL)) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "heapJump in drop, segId:%" PRIu32 ", offset:%" PRIu32,
            heapCntrAcsInfo->heapShmAddr.segId, heapCntrAcsInfo->heapShmAddr.offset);
        return INVALID_PARAMETER_VALUE_INTER;
    }

    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(heapJump->cfg.seInstanceId);
    if (seInstance == NULL) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "NULL seInstance:%" PRIu16, heapJump->cfg.seInstanceId);
        return INVALID_PARAMETER_VALUE_INTER;
    }

    HeapPageListParaT para = {.isUseRsm = heapJump->cfg.isUseRsm,
        .tableSpaceIndex = heapJump->cfg.tableSpaceIndex,
        .labelId = heapJump->cfg.labelId};
    StatusInter ret = HeapClearCachedPageList(seInstance, &para, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "heap drop page list, spcId=%" PRIu32 ", labelId=%" PRIu32, para.tableSpaceIndex, para.labelId);
        return ret;
    }

    DbMemCtxT *heapMemCtx = (DbMemCtxT *)DbGetShmemCtxById(seInstance->heapShmMemCtxId, heapJump->cfg.seInstanceId);
    if (heapMemCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "memCtxId %" PRIu32 " inv", seInstance->heapShmMemCtxId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    if (SECUREC_LIKELY(heapJump->isInit)) {  // 释放真正的heap内存
        HeapT *heap = (HeapT *)DbShmPtrToAddr(heapJump->heapShmAddr);
        if (heap == NULL) {
            SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "heap in drop, segId:%" PRIu32 ", offset:%" PRIu32,
                heapJump->heapShmAddr.segId, heapJump->heapShmAddr.offset);
            return INVALID_PARAMETER_VALUE_INTER;
        }
        HeapReleaseAllBlock(seInstance, heap, NULL);
        heap->magicNum = HEAP_INVALID_MAGIC_NUM;
        DB_LOG_INFO("Drop Heap, labelId:%" PRIu32, heap->constInfo.labelId);
        HeapFreeLockStat(heapMemCtx, heap, heapJump);
        DbShmemCtxFree(heapMemCtx, heapJump->heapShmAddr);
    }
    if (DbIsShmPtrValid(heapJump->lockStatShmPtr)) {
        DbShmemCtxFree(heapMemCtx, heapJump->lockStatShmPtr);
        heapJump->lockStatShmPtr = DB_INVALID_SHMPTR;
    }
    DB_LOG_INFO("Drop heap jumper, labelId:%" PRIu32, heapJump->cfg.labelId);
    DbShmemCtxFree(heapMemCtx, heapCntrAcsInfo->heapShmAddr);
    return STATUS_OK_INTER;
}

StatusInter HeapTruncate(SeRunCtxT *seRunCtx, HeapCntrAcsInfoT *hpAcsInfo)
{
    return STATUS_OK_INTER;
}

StatusInter HeapAllocVarRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    bool isCompressed = false;
    HeapPageAllocCtx heapPageAllocCtx = EmptyHeapPageAllocCtx();
    HpPageAllocRowInfoT *allocRowInfo = opInfo->allocRowInfo;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;

    StatusInter ret = HeapAllocVarPageBySize(ctx, &heapPageAllocCtx, allocRowInfo->requireSize, &isCompressed);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    HVPageHeadT *pageHead = heapPageAllocCtx.pageHeadPtr.varPageHead;
    HeapSetAddrPid(&allocRowInfo->newRowInfo.itemPtr, heapPageAllocCtx.pageInfo.pageId);
    allocRowInfo->newRowInfo.pageHeadPtr.varPageHead = pageHead;

    // 如果是更新场景申请 新的 link row, 并且fsm分配到 和 src row 所在相同的 page, 则应该使用 normal row的格式进行更新
    // 但是 分片的各行, 不限制. --> linkSrc(sliceHead) 和 linkDst(sliceHead Dir) 可以在同一个页
    // undoLog合并场景，有可能是往版本链写，此时允许src与dst在同一页
    if (fetchRowInfo && fetchRowInfo->srcRowInfo.pageHeadPtr.pageHead == &pageHead->baseHead.pageHead) {
        if (isCompressed) {
            // 页面可能被整理过了，需要重新获取一下行头
            fetchRowInfo->srcRowInfo.rowHeadPtr.rowState =
                HeapVarPageGetRowStateImpl(pageHead, fetchRowInfo->srcRowInfo.slot);
        }
        if (!allocRowInfo->sliceRowInfo.isSliceRow && !allocRowInfo->isAllowLinkRowInOnePage) {
            // 什么场景会走入该分支?
            // 1. 更新场景 寻找合适的 link row的新page, 如果reLatch (src page离开了临界区),
            // 新查询 link page空间不够 (fsm 和 page实际大小不一致, 因为二者之间更新顺序没有强一致关系),
            // 再次从fsm寻页, 此时, 由于 src page 曾经离开过临界区, 其他线程操作, 可能空间又足够了
            // 2. 一开始尝试在本页更新时页空间不够，后面尝试转为跳转行时本页又足够了，要再次重试原地更新
            ret = INT_ERR_HEAP_UPD_ALLOC_SAME_PAG;
        }
    }
    if (SECUREC_LIKELY(ret != INT_ERR_HEAP_UPD_ALLOC_SAME_PAG)) {
        ret = HeapVarPageAllocRow(ctx, opInfo);
    }

    /* 把page归还FSM */
    HeapReleasePage(ctx, &heapPageAllocCtx, allocRowInfo->requireSize, pageHead->baseHead.pageHead.freeSize);
    return ret;
}

void HeapAllocRowInfoInit(HpPageAllocRowInfoT *pageAllocRowInfo, HpPageRowTypeE rowType, bool isNewSlot)
{
    DB_POINTER(pageAllocRowInfo);
    HeapAllocRowInfoInitHelper(pageAllocRowInfo, rowType, (PageSizeT)pageAllocRowInfo->bufSize, isNewSlot);
}

StatusInter HeapInsertRowInVarPage(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    DB_ASSERT(!ctx->heapCfg.isUseRsm);
    if (HeapCheckIsNormalRowByBufSize(opInfo->allocRowInfo->bufSize)) {
        HeapAllocRowInfoInit(opInfo->allocRowInfo, HEAP_VAR_ROW_NORMAL_ROW, true);
        return HeapAllocVarRow(ctx, opInfo);
    }
    StatusInter ret = HeapInsertSliceRows(ctx, opInfo);
    if (ret != STATUS_OK_INTER) {
        HeapClearSliceRow4Failed(ctx, opInfo, false);
    }
    return ret;
}

StatusInter HeapInsertAllocRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    StatusInter ret;
    HpPageAllocRowInfoT *pageAllocRowInfo = opInfo->allocRowInfo;
    if (ctx->hpControl.isFixPage) {
        ret = HeapFixedRowInsert(ctx, opInfo);
    } else {
        ret = HeapInsertRowInVarPage(ctx, opInfo);
    }
    /* 释放 pageSorter 中的锁 */
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    // 缓存本次page的addr, 提高下次访问该page的速度
    ctx->pageCache.lastPageLogicPtr = pageAllocRowInfo->newRowInfo.itemPtr;
    ctx->pageCache.lastPageHeadPtr = pageAllocRowInfo->newRowInfo.pageHeadPtr.pageHead;
    ctx->pageCache.cacheVersion = ctx->runtimeInfo->cacheVersion;
    // lastPageBlockId置为INVALID，防止扫描使用该页addr，主要问题是
    // 此处离开了页锁保护，而且在缩容流程中，扫描和插入的页可能是不一样的BlockId，扫描流程无法利用
    ctx->pageCache.lastPageBlockId = SE_INVALID_BLOCK_ID;
    // 注意: 下面流程都是离开了 page 的 latch;
    // 加行锁 & 记录undo; 如果失败, 需要释放刚刚申请到的 row (因为undo没有记录下来)
    // Normal模式下: HeapAcquireRowLockForInsert (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
    ret = HeapAcquireRowLockFunc(ctx, ctx->hpControl.trxContrl.hpAmIndex, pageAllocRowInfo->newRowInfo.itemPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        // Normal模式下: HeapAcquireRowLockForInsertFailedClear (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
        HeapRecoverFunc(ctx, opInfo);
        return ret;
    }
    uint64_t newRollPtr;
    // Normal模式下: HeapUndoLogForInsert (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
    ret = HeapLogUndoFunc(ctx, TRX_OP_INSERT, opInfo, &newRollPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        // Normal模式下: HeapAcquireRowLockForInsertFailedClear (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
        HeapRecoverFunc(ctx, opInfo);
        return ret;
    }
    pageAllocRowInfo->isUndoOperationOk = true;
    HeapInsertFreeMemInNormalPro(ctx, opInfo);
    return ret;
}

StatusInter HeapInsertImpl(HeapRunCtxT *ctx, const uint8_t *buf, uint32_t bufSize, HpItemPointerT *itemPtr)
{
    DB_POINTER3(ctx, buf, itemPtr);
    DB_ASSERT(ctx->hpOperation < HEAP_OPTYPE_MODIFY);

    HpPageAllocRowInfoT pageAllocRowInfo = EmptyHpPageAllocRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(NULL, &pageAllocRowInfo);
    StatusInter ret = HeapInitAllocRowInfoImpl(ctx, buf, bufSize, &pageAllocRowInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    HeapSetPageLatchActionForWrite(ctx);
    HeapSetOperationForInsert(ctx);
    // Normal模式下: HeapPrepareTrxInfoForInsert (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
    uint64_t newRollPtr = UndoGetDefaultRollPtr(ctx->seRunCtx->trx);
    ret = HeapSaveTrxInfoFunc(ctx, (uint32_t)TRX_OP_INSERT, &opInfo, newRollPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    ret = HeapStatInsert(ctx, DEFAULT_OPERATE_ITEM_NUM, bufSize);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    ret = HeapInsertAllocRow(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        if (!pageAllocRowInfo.isUndoOperationOk) {  // undo日志是否记录成功，避免与回滚流程dfx重复统计
            HeapStatInsertFailed(ctx, DEFAULT_OPERATE_ITEM_NUM, bufSize);
        }
        return ret;
    }
    // fix normal row | var normal row：实际数据物理addr(需要偏移数据头)，分片行：获取link src row物理addr
    if (SECUREC_LIKELY(ctx->hpControl.isFixPage)) {
        ctx->compV1Info.realInsertPtr = pageAllocRowInfo.newRowInfo.rowHeadPtr.normalFixRowHead + 1;
    } else if (pageAllocRowInfo.newRowInfo.rowHeadPtr.rowState->isLinkSrc) {
        ctx->compV1Info.realInsertPtr = pageAllocRowInfo.newRowInfo.rowHeadPtr.linkRowHead;
    } else {
        ctx->compV1Info.realInsertPtr = pageAllocRowInfo.newRowInfo.rowHeadPtr.normalRowHead + 1;
    }
    *itemPtr = pageAllocRowInfo.newRowInfo.itemPtr;
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP,
        "Insert label %" PRIu32 " insert row (%" PRIu32 ",%" PRIu32 "), bufSize:%" PRIu32 ", trxId:%" PRIu64,
        ctx->heapCfg.labelId, itemPtr->pageId, itemPtr->slotId, bufSize, ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_ITEMNUM,
        "HeapInsertImpl labelId:%" PRIu32 " insert row (%" PRIu32 ",%" PRIu32 "), phyItemNum:%" PRIu64,
        ctx->heapCfg.labelId, itemPtr->pageId, itemPtr->slotId, ctx->perfStat->phyItemNum);
    return STATUS_OK_INTER;
}
void HeapCopyRowInfoForAllocInSamePage(HeapRowInfoT *newRowInfo, const HeapRowInfoT *samePageRowInfo)
{
    DB_ASSERT(false);
}

void HeapUpdateInPageMemmoveRow(
    HpPageRowOpInfoT *opInfo, HVPageHeadT *pageHead, const HpNormalRowHeadT *rowHead, PageTotalSizeT diffSize)
{
    DB_ASSERT(false);
}

StatusInter HeapUpdateInPagePrepare(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, HVPageHeadT *pageHead,
    const HpNormalRowHeadT *rowHead, PageTotalSizeT diffSize)
{
    DB_ASSERT(false);
    return STATUS_OK_INTER;
}

// 搬迁部分旧行，原地更新row
StatusInter HeapUpdateInPage(
    HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, const HpNormalRowHeadT *rowHead, PageTotalSizeT diffSize)
{
    DB_ASSERT(false);
    return STATUS_OK_INTER;
}

StatusInter HeapUpdateLinkSrcAndDstRowInCurPage(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_ASSERT(false);
    // 不能本页更新完毕就报错，外面会尝试跳转行更新
    return MEMORY_OPERATE_FAILED_INTER;
}

StatusInter HeapUpdateLinkSrcAndDstRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_ASSERT(false);
    return OUT_OF_MEMORY_INTER;
}

static StatusInter HeapUpdateNotExpandRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, bool isNeedChangeFreeSize)
{
    DB_POINTER2(ctx, opInfo);
    HeapRowInfoT *srcRowInfo = &opInfo->fetchRowInfo->srcRowInfo;
    HpNormalRowHeadT *rowHead = srcRowInfo->rowHeadPtr.normalRowHead;
    DB_ASSERT(!ctx->heapCfg.isUseRsm);
    HeapVarPageInitNormalRowImpl(ctx, rowHead, opInfo->allocRowInfo);  // 直接复用rowHead, 并更新
    opInfo->hasUpdateRowInfo = true;
    return STATUS_OK_INTER;
}

StatusInter HeapUpdateNormalRowInCurPage(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    HpPageAllocRowInfoT *pageAllocRowInfo = opInfo->allocRowInfo;
    HeapRowInfoT *srcRowInfo = &opInfo->fetchRowInfo->srcRowInfo;
    HVPageHeadT *pageHead = srcRowInfo->pageHeadPtr.varPageHead;
    HpNormalRowHeadT *rowHead = srcRowInfo->rowHeadPtr.normalRowHead;
    // 默认情况下, normal row 优先 原地覆盖
    HeapAllocRowInfoInit(pageAllocRowInfo, HEAP_VAR_ROW_NORMAL_ROW, false);
    if (SECUREC_LIKELY(opInfo->updInPageOldStoreSize >= pageAllocRowInfo->storeSize)) {
        // 原来的空间足够, 直接原地更新
        // 活跃事务：
        // (1) 更新前后大小相同，no need修改freeSize，传false
        // (2) 由大到小更新，需要保证回滚时空间足够，也no need修改freeSize，传false
        // (3) 由小到大更新，走下面HeapUpdateInPage的逻辑，此分支不考虑
        // 回滚事务：外面HeapVarRowClearReserveSize都会刷新freeSize，因此传true
        return HeapUpdateNotExpandRow(ctx, opInfo, ((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_ROLLBACK);
    }
    // 回滚流程理应整行都够，不应该走到这里
    DB_ASSERT(((TrxT *)ctx->seRunCtx->trx)->base.state != TRX_STATE_ROLLBACK);

    bool isCompressed = false;
    // row空间不足, 检查本页的空间是否满足更新
    bool isSizeEnough = false;
    StatusInter ret =
        HeapVarPageConfirmSize(ctx, pageHead, pageAllocRowInfo->requireSize, &isCompressed, &isSizeEnough);
    DB_UNUSED(ret);  // 页面整理即使失败也没关系，外面会尝试跳转行更新
    if (SECUREC_UNLIKELY(isCompressed)) {
        // 如果page压缩, 则row会发生偏移, 重新获取 rowHead
        srcRowInfo->rowHeadPtr.rowState = HeapVarPageGetRowStateImpl(pageHead, srcRowInfo->slot);
        rowHead = srcRowInfo->rowHeadPtr.normalRowHead;
    }
    if (SECUREC_LIKELY(isSizeEnough)) {  // 在页内更新
        HeapAllocRowInfoInit(pageAllocRowInfo, HEAP_VAR_ROW_NORMAL_ROW, false);
        HeapCopyRowInfoForAllocInSamePage(&pageAllocRowInfo->newRowInfo, srcRowInfo);  // 只需要拷贝 page相关信息
        return HeapUpdateInPage(ctx, opInfo, rowHead, pageAllocRowInfo->storeSize - opInfo->updInPageOldStoreSize);
    }
    // 不能本页更新完毕就报错，外面会尝试跳转行更新
    return MEMORY_OPERATE_FAILED_INTER;
}

void HeapUpdateFlashRowHead(
    HeapRunCtxT *ctx, HeapRowInfoT *srcRowInfo, HVPageHeadT *pageHead, HpNormalRowHeadT **rowHead)
{
    DB_ASSERT(false);
}

/*
乐观事务的更新，为啥需要在记录undo前申请出跳转行/大对象行？
考虑以下场景：
1. 假设trx1来进行更新，此时已经记录undo了，但还没刷新masterVersion

                            trx1写的undo
-------------------     ------------------
| masterVersion(v0)|--->|   undoLog1(v0)  |
-------------------     -------------------
    trx1的修改             trx0插入的版本

2. 更新masterVersion时，发现当前位置不够，且页面整理后也不够，触发normal-->linkRow的逻辑，申请跳转行，触发了relatch，
释放掉了本页的latch锁。此时发生并发，trx2也来修改这一行，抢先加上页latch，并记录了undo：

                            trx2写的undo          trx1写的undo
-------------------     ------------------     ------------------
| masterVersion(v2)|--->|   undoLog2(v0)  |--->|   undoLog1(v0)  |
-------------------     -------------------    -------------------
    trx2的修改             trx0插入的版本         trx0插入的版本

3. trx2更新完后就释放页latch了，此时trx1再次拿到本页的latch锁，此时trx1不知道trx2的存在，会直接刷新masterVersion，
覆盖掉trx2的v2版本

                            trx2写的undo          trx1写的undo
-------------------     ------------------     ------------------
| masterVersion(v1)|--->|   undoLog2(v0)  |--->|   undoLog1(v0)  |
-------------------     -------------------    -------------------
    trx1的修改             trx0插入的版本         trx0插入的版本

4. 这样就造成了trx2的v2版本数据丢失，因此需要在记录undo前申请出跳转行/大对象行，避免relatch的发生。
同时对pageHead上的freeSize字段的维护也非常重要，决定了是否提前申请出跳转行（大对象是一定要提前申请的）
*/
StatusInter HeapUpdateNormalRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    HpPageAllocRowInfoT *pageAllocRowInfo = opInfo->allocRowInfo;
    HeapRowInfoT *srcRowInfo = &opInfo->fetchRowInfo->srcRowInfo;
    HVPageHeadT *pageHead = srcRowInfo->pageHeadPtr.varPageHead;
    HpNormalRowHeadT *rowHead = srcRowInfo->rowHeadPtr.normalRowHead;
    pageAllocRowInfo->newRowInfo.rollbackReserveSize = rowHead->rollbackReserveSize;
    uint32_t retryTimes = 0;
    while (retryTimes < RETRY_TIMES_ALLOWED_FOR_CUR_PAGE) {
        StatusInter ret = HeapUpdateNormalRowInCurPage(ctx, opInfo);
        if (SECUREC_LIKELY(ret == STATUS_OK_INTER)) {  // 本页更新成功就返回
            return STATUS_OK_INTER;
        } else if (((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_ROLLBACK) {
            // 回滚场景，保留内存可能会申请rsmUndo导致失败，其他场景回滚都不应该申请新dst(新页)
            DB_ASSERT(ctx->heapCfg.isUseRsm);
            return ret;
        }
        if (SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX) {
            // 乐观事务，在HeapUpdate已经提前申请好了dstRow，不会走到此处
            // 在release模式下，如果此处不报错返回，会有并发问题发生(见HeapUpdateNormalRow上面的注释)，应该防止故障扩散
            SE_LAST_ERROR(
                INTERNAL_ERROR_INTER, "freeSize: %" PRIu32 " less allocSize", pageHead->baseHead.pageHead.freeSize);
            DB_ASSERT(false);
            return INTERNAL_ERROR_INTER;
        }
        DB_ASSERT(!ctx->hpControl.isRsmRecovery);  // 恢复阶段只允许normal->normal, src->src(实现了dst原地更新)
        rowHead = srcRowInfo->rowHeadPtr.normalRowHead;  // 内部做了页整理，需要刷新一下
        DB_ASSERT(rowHead->magicNum == HEAP_ROW_MAGIC_NUM);

        // 本page空间不够, 需要从 normal row扩展为 src + dst;
        HeapAllocRowInfoInit(pageAllocRowInfo, HEAP_VAR_ROW_LINK_DST_ROW, true);
        pageAllocRowInfo->sliceRowInfo.linkSrcRowPtr = srcRowInfo->itemPtr;
        ret = HeapAllocVarRow(ctx, opInfo);  // 跨页申请

        HeapUpdateFlashRowHead(ctx, srcRowInfo, pageHead, &rowHead);
        if (SECUREC_UNLIKELY(ret == INT_ERR_HEAP_UPD_ALLOC_SAME_PAG)) {
            retryTimes++;
            continue;  // 由于跨页申请流程中, 可能reLatch, 申请到相同的page, 重试, 优先本页更新
        } else if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "alloc row in other page");
            return ret;
        }
        DB_ASSERT(rowHead->magicNum == HEAP_ROW_MAGIC_NUM);
        (void)DbAtomicAdd64(&ctx->perfStat->historyLinkRows, (uint64_t)1);
        // 更新旧page上的信息: normal row -> linkSrc，pageHead的freeSize不变，保留行预留空间
        HeapUpdateVarRowToLinkSrcSetInfo(ctx, opInfo, true);
        return STATUS_OK_INTER;
    }
    SE_ERROR(OUT_OF_MEMORY_INTER,
        "update normal row, trxId:%" PRIu64 ", label:%" PRIu32 ", row:(%" PRIu32 ", %" PRIu32 ")",
        ((TrxT *)ctx->seRunCtx->trx)->base.trxId, ctx->heapCfg.labelId, srcRowInfo->itemPtr.pageId,
        srcRowInfo->itemPtr.slotId);
    return OUT_OF_MEMORY_INTER;
}

// 用于回滚以及提交时行预留空间的清理，包含3个作用：
// 1.更新rollbackReserveSize字段为存储的实际长度：normal行更新为bufSize，linkSrc行更新为0
// 2.若有差值，刷新到pageHead的freeSize
// 3.若页上的freeSize有变化，更新到lfs
void HeapVarRowClearReserveSize(
    HeapRunCtxT *ctx, HpRowHeadPtrT rowHeadPtr, HVPageHeadT *pageHead, uint16_t oldFreeSize, bool isNeedMoveRsmUndo)
{
    DB_POINTER2(ctx, pageHead);
    PageSizeT rowReserveSize = HeapPageGetRowReserveSize(rowHeadPtr);
    if (HeapPageIsNormalRow(rowHeadPtr.rowState)) {
        if (rowReserveSize > HeapPageGetMinRowSize(rowHeadPtr.normalRowHead->size, HEAP_VAR_ROW_NORMAL_ROW)) {
            pageHead->baseHead.pageHead.freeSize =
                (uint16_t)(oldFreeSize + rowReserveSize -
                           HeapPageGetMinRowSize(rowHeadPtr.normalRowHead->size, HEAP_VAR_ROW_NORMAL_ROW));
        }
        rowHeadPtr.normalRowHead->rollbackReserveSize = (PageSizeT)HEAP_CALC_ALIGN_SIZE(rowHeadPtr.normalRowHead->size);
        DB_ASSERT(rowHeadPtr.normalRowHead->rollbackReserveSize <= ctx->heapCfg.maxRowRawSizeInPage);
    } else if (HeapPageIsLinkSrcRow(rowHeadPtr.rowState)) {
        if (rowHeadPtr.linkRowHead->rollbackReserveSize > 0) {
            pageHead->baseHead.pageHead.freeSize =
                (uint16_t)(oldFreeSize + rowReserveSize - HeapPageGetLinkSrcRowSize());
        }
        rowHeadPtr.linkRowHead->rollbackReserveSize = 0;
    }
    if (oldFreeSize != pageHead->baseHead.pageHead.freeSize) {
        // freeSize产生变动后，需要刷新在页面整理时需要搬迁的数据行在页内的offset上界标记位，同时更新lfs
        HeapPageUpdatePageStat(pageHead, rowHeadPtr.rowState);
        LfsOpInfoT lfsOpInfo = {.mgr = ctx->fsmMgr,
            .md = ctx->seRunCtx->pageMgr,
            .rsmUndoRec = ((TrxT *)(ctx->seRunCtx->trx))->liteTrx.rsmUndoRec,
            .useCachePage = !ctx->seRunCtx->resSessionCtx.isDirectWrite,
            .needReleasePage = false};
        StatusInter ret = LfsSetBlockFreeSpace(
            &lfsOpInfo, (uint8_t *)pageHead, pageHead->baseHead.pageHead.freeSize, FSM_INVALID_MAX_ROW_SIZE, false);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "set block freeSpace, blockId %" PRIu32, pageHead->baseHead.slotIdx);
        }
    }
}

inline static void HeapDeleteDstRowIfRollBack(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    if (HeapUpdateIsRollbackLinkRow(ctx, opInfo)) {
        bool isMasterVersionMarkDeleted = opInfo->fetchRowInfo->srcRowInfo.isMarkDeleted;
        if (isMasterVersionMarkDeleted) {
            // masterVersion是删除的状态时，它的内存可能是已回滚事务的内存，此时不应该去访问dstRow
            return;
        }
        bool isNoSlice = !HeapUpdateIsNewTupleNeedSlice(ctx, opInfo);
        DB_ASSERT(isNoSlice);  // 上层回滚流程校验了oldRowIsNormalRow，大对象不会走进该流程
        HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
        StatusInter ret = HeapDeleteLinkDstRowImpl(ctx, &fetchRowInfo->dstRowInfo, fetchRowInfo->isSliceRows, false);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "label %" PRIu32 " del row, trxId %" PRIu64, ctx->heapCfg.labelId,
                ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
            DB_ASSERT(false);  // 删除流程不应该有失败, 出现应该定位;
        }
    }
}

void HeapUpdateSetRowOpInfo(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, UndoRowOpInfoT *rowOpInfo)
{
    DB_ASSERT(false);
}

inline static void HeapTryLfsSetBlockFreeSpace(const HeapRunCtxT *ctx, PageHeadT *pageBaseHead,
    const HVPageHeadT *pageHead, const HpPageAllocRowInfoT *pageAllocRowInfo)
{
    DB_ASSERT(false);
}

StatusInter HeapUpdateVarRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    TrxT *trx = (TrxT *)ctx->seRunCtx->trx;
    HpPageAllocRowInfoT *pageAllocRowInfo = opInfo->allocRowInfo;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    HeapRowInfoT *srcRowInfo = &fetchRowInfo->srcRowInfo;
    HVPageHeadT *pageHead = srcRowInfo->pageHeadPtr.varPageHead;
    PageHeadT *pageBaseHead = srcRowInfo->pageHeadPtr.pageHead;
    uint16_t oldFreeSize = pageHead->baseHead.pageHead.freeSize;
    HeapPageUpdatePageStat(pageHead, srcRowInfo->rowHeadPtr.rowState);
    StatusInter ret;
#ifndef NDEBUG
    if (SeTransGetTrxType(ctx->seRunCtx) == PESSIMISTIC_TRX) {
        // 乐观下，可能有多个事务同时操作同一行，因此此处可能会触发isDeleted不为true的断言
        DB_ASSERT(!srcRowInfo->rowHeadPtr.rowState->isDeleted);
    }
#endif
    DB_ASSERT(!HeapUpdateIsNewTupleNeedSlice(ctx, opInfo));
    if (SECUREC_LIKELY(HeapPageIsNormalRow(srcRowInfo->rowHeadPtr.rowState))) {
        ret = HeapUpdateNormalRow(ctx, opInfo);
    } else {
        ret = INT_ERR_HEAP_UNEXPECT_ERROR;  // 内部非预期错误
        SE_LAST_ERROR(ret, "novalid row type, type is: %" PRId32 ".", (int32_t)srcRowInfo->rowType);
    }
    // 1. 回滚流程：变长表normal行在masterVersion回滚已提交版本成功，还原行占用空间为真实大小，同时涵盖乐观和悲观的情况
    // 2. 正常更新流程：页上的freeSize有变化，再更新 lfs，否则不用更新
    if (trx->base.state == TRX_STATE_ROLLBACK) {
        bool isOldRowCommitted = false;
        StatusInter res = TrxMgrCheckTrxIsCommitted(trx->trxMgr, ctx->rollBackInfo.oldTrxId, &isOldRowCommitted);
        if (SECUREC_UNLIKELY(res != STATUS_OK_INTER)) {
            return res;
        }
        if (ret == STATUS_OK_INTER && isOldRowCommitted) {
            // 此函数中已经包括lfs更新
            // 回滚流程最后一步设置freeSize，设置完成后，需要看是否还有dst行需要删除
            HeapVarRowClearReserveSize(
                ctx, srcRowInfo->rowHeadPtr, pageHead, oldFreeSize, !fetchRowInfo->is2LinkDstRow);
            SHM_CRASHPOINT_ONE(SHM_CRASH_VH_UPD_NORMAL_ROW_ROLLBACK_FINISH);
        }
    } else if (SECUREC_UNLIKELY(oldFreeSize != pageHead->baseHead.pageHead.freeSize)) {
        // 这页不是从fsm取的，所以要使用NoRelink模式
        HeapTryLfsSetBlockFreeSpace(ctx, pageBaseHead, pageHead, pageAllocRowInfo);
    }
    return ret;
}

StatusInter HeapUpdateVarRowWithOptimisticTrx(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    HeapRowInfoT *srcRowInfo = &opInfo->fetchRowInfo->srcRowInfo;
    HpTrxPrevVersionRowInfoT *prevVersionRowInfo = &opInfo->fetchRowInfo->prevVersionRowInfo;
    if (opInfo->allocRowInfo->undoBypassType != UNDO_OP_NORMAL && !prevVersionRowInfo->isOnMasterVersion) {
        // 更新到版本链上，肯定是大对象或者DstRow的情况
        DB_ASSERT(opInfo->allocRowInfo->sliceRowInfo.isSliceRow || opInfo->allocRowInfo->isNewSlot);
        uint64_t masterRollPtr = (srcRowInfo->rowType == HEAP_VAR_ROW_NORMAL_ROW) ?
                                     srcRowInfo->rowHeadPtr.normalRowHead->trxInfo.rollPtr :
                                     srcRowInfo->rowHeadPtr.linkRowHead->srcTrxInfo.rollPtr;
        UndoRowOpInfoT rowOpInfo = {0};
        HeapUpdateSetRowOpInfo(ctx, opInfo, &rowOpInfo);
        // undo 日志, 只记录首行的信息. 不记录跳转dts, 分片行的内容.
        HpLinkRowHeadT linkRowHead = {0};
        linkRowHead.rowState.isLinkSrc = true;
        linkRowHead.rollbackReserveSize = 0;  // 该数值只在masterVersion改动时有效，此处赋0即可
        DB_ASSERT(opInfo->allocRowInfo->newTrxInfo.rollPtr == prevVersionRowInfo->undoRollPtr);
        HeapVarPageInitLinkSrcRowImpl(ctx, &linkRowHead, opInfo->allocRowInfo, true);
        rowOpInfo.rowSize = HeapPageGetMinRowSize(0, HEAP_VAR_ROW_LINK_SRC_ROW);
        rowOpInfo.rowBuf = (uint8_t *)&linkRowHead;
        return UndoWriteRecInVersionChain(
            ctx->seRunCtx->undoCtx, masterRollPtr, &rowOpInfo, prevVersionRowInfo->undoRollPtr, false);
    } else {
        // 以下2种情况会走到该分支：
        // 1. 不能合并
        // 2. 能合并，并且在masterVersion上
        if (opInfo->allocRowInfo->sliceRowInfo.isSliceRow || opInfo->allocRowInfo->isNewSlot) {
            HeapVarPageInitLinkSrcRowImpl(
                ctx, opInfo->fetchRowInfo->srcRowInfo.rowHeadPtr.linkRowHead, opInfo->allocRowInfo, true);
            return STATUS_OK_INTER;
        } else {  // 普通行
            return HeapUpdateVarRow(ctx, opInfo);
        }
    }
}

StatusInter HeapUpdateRowImpl(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    StatusInter ret;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    opInfo->updInPageOldStoreSize =
        HeapPageGetMinRowSize(fetchRowInfo->srcRowInfo.rollbackReserveSize, fetchRowInfo->srcRowInfo.rowType);
    if (ctx->heapCfg.pageType == HEAP_FIX_LEN_ROW_PAGE) {
        ret = HeapFixedRowUpdate(ctx, opInfo);
    } else {
        if ((SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX) &&
            ((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_ACTIVE) {
            // 外面可能已经申请了跳转行/分片行信息来避免relatch，且不一定是往masterVersion上更新，单独写流程
            ret = HeapUpdateVarRowWithOptimisticTrx(ctx, opInfo);
        } else {
            ret = HeapUpdateVarRow(ctx, opInfo);
        }
    }
    if (ret != OUT_OF_MEMORY_INTER) {
        // 保留内存申请rsmUndo可能会失败，返回出去切换逃生内存
        // 当场切换会持有页锁去等待 控制逃生内存串行使用的锁，造成死锁, 等切换完毕后第二次进入再删除dst行
        HeapDeleteDstRowIfRollBack(ctx, opInfo);
    }
    return ret;
}

StatusInter HeapUpdLogUndoAndSetTrxInfoImpl(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    uint32_t undoOpType = opInfo->allocRowInfo->isPartialUpdate ? TRX_OP_UPDATE_PART : TRX_OP_UPDATE;
    uint64_t newRollPtr = UNDO_INVALID_ROLLPTR;
    // 修改前, 获取事务字段的信息: 记录undo信息获取新的事务信息 或者 获取回滚的事务信息
    // Normal模式下: HeapUndoLogForUpdOrDel (LITE模式，详细见g_heapTrxAmForGroup的赋值)
    StatusInter ret = HeapLogUndoFunc(ctx, undoOpType, opInfo, &newRollPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    opInfo->allocRowInfo->isUndoOperationOk = true;
    SHM_CRASHPOINT_ONE(SHM_CRASH_FH_UPD_UNDO_LOG_AFTER);
    // Normal模式下: HeapPrepareTrxInfoForUpdOrDel (LITE模式，详细见g_heapTrxAmForGroup的赋值)
    return HeapSaveTrxInfoFunc(ctx, undoOpType, opInfo, newRollPtr);
}

// 删除历史版本的linkSrc 指向的 linkDst.
void HeapUpdateCommitCleanImpl(HeapRunCtxT *ctx, const UndoRowOpInfoT *rowOpInfo, bool isLiteTrxNeedHandleDst)
{
    DB_POINTER2(ctx, rowOpInfo);
    HpRowHeadPtrT oldRowInLog = {.rowState = NULL};
    oldRowInLog.rowState = (HpRowStateT *)rowOpInfo->rowBuf;
    if (HeapPageIsNormalRow(oldRowInLog.rowState)) {
        return;
    }

    bool isLinkSrcRow = HeapPageIsLinkSrcRow(oldRowInLog.rowState);
    DB_ASSERT(isLinkSrcRow);
    DB_ASSERT(rowOpInfo->rowSize == sizeof(HpLinkRowHeadT) || rowOpInfo->rowSize == sizeof(HpLinkSrcFixRowHead));
    if (ctx->hpControl.isFixPage) {
        HeapCleanLinkDstRow(ctx, oldRowInLog.linkSrcFixRowHead->linkItemPtr);
        return;
    }
    HeapCleanLinkDstRow(ctx, oldRowInLog.linkRowHead->linkItemPtr);
}

void HeapUpdateForUpdateFailedClear(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    if ((SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX)) {
        // 未刷新masterVersion的，都需要由heap回收掉
        if (opInfo->allocRowInfo->sliceRowInfo.isSliceRow) {
            HeapClearSliceRow4Failed(ctx, opInfo, true);
        } else if (opInfo->allocRowInfo->newRowInfo.slot) {  // 优先判断分片，再判断slot
            StatusInter ret = HeapPageDeleteRowImpl(ctx, &opInfo->allocRowInfo->newRowInfo);
            DB_ASSERT(ret == STATUS_OK_INTER);  // 刚申请出来的页，不应该删失败
        }
    }
}

static StatusInter HeapHeapUpdatePreAllocSliceRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    HpPageAllocRowInfoT *pageAllocRowInfo = opInfo->allocRowInfo;
    HeapRowSetTrxId(&opInfo->allocRowInfo->newTrxInfo, ((TrxT *)(ctx->seRunCtx->trx))->base.trxId);
    pageAllocRowInfo->isAllowLinkRowInOnePage = true;
    StatusInter ret = HeapUpdateAllocSliceRows(ctx, opInfo);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    // 重新给srcRow加锁，HeapUpdateAllocSliceRows会释放srcRow的锁，此处重新上锁
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    ret = HeapIsFreeLatchUnderLabel(ctx) ? HeapLabelLatchCheckPage(ctx, &fetchRowInfo->srcRowInfo) :
                                           HeapPageAddLatch(ctx, &fetchRowInfo->srcRowInfo);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    return HeapVarRowRefetch(ctx, opInfo);
}

StatusInter HeapUpdatePreWithOptimisticTrx(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    if (ctx->heapCfg.pageType == HEAP_FIX_LEN_ROW_PAGE) {
        // 乐观暂不支持表升降级，所以是fix表时，肯定都是normal行，因此不用提前申请link行或者分片行，直接return即可
        return STATUS_OK_INTER;
    }
    if (SECUREC_UNLIKELY(!HeapCheckIsNormalRowByBufSize(opInfo->allocRowInfo->bufSize))) {  // 判断大对象行
        return HeapHeapUpdatePreAllocSliceRow(ctx, opInfo);
    }
    // 普通变长行，V1场景按照定长行处理
    return STATUS_OK_INTER;
}

void HeapUpdateInit(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, const HpItemPointerT *itemPtr)
{
    DB_POINTER3(ctx, opInfo, itemPtr);
    opInfo->fetchRowInfo->srcRowInfo.itemPtr = *itemPtr;
    opInfo->fetchRowInfo->curRowInfo = &opInfo->fetchRowInfo->srcRowInfo;
    // RR事务需要读主版本，用来记录undo，因此也需要读markDelRow
    // 在linkSrc行头存储了bufSize，可以不访问dstRow了（需要尽可能避免乐观下出现relatch）
    bool isOptimisticTrx = (SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX);
    opInfo->fetchRowInfo->isCanAccessMarkDelRow = (SeTransGetIsolationLevel(ctx->seRunCtx) == REPEATABLE_READ);
    opInfo->fetchRowInfo->isFindPrevVersionInfo = isOptimisticTrx;
    opInfo->fetchRowInfo->isOnlyReadSrcRow = isOptimisticTrx;  // 保留内存恢复流程需要去读dst行
    HeapSetPageLatchActionForWrite(ctx);
    HeapSetOperationForUpdate(ctx);
    // Normal模式下: HeapSetLockActionBeforeUpdate (LITE模式，详细见g_heapTrxAmForGroup的赋值)
    HeapSetLockFunc(ctx, ctx->hpControl.trxContrl.hpAmIndex);
}

StatusInter HeapUpdatePrepare(HeapRunCtxT *ctx, const HeapTupleBufT *tupleBuf, HpPageRowOpInfoT *opInfo)
{
    // 先查询, 获取 当前 row的信息
    StatusInter ret = HeapFetchRow(ctx, opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    if (SECUREC_UNLIKELY(
            ctx->hpControl.isFixPage && (opInfo->fetchRowInfo->bufSize > ctx->heap->regularInfo.maxRowSize))) {
        SE_LAST_ERROR(PROGRAM_LIMIT_EXCEEDED_HEAP_MAX_ROW_SIZE,
            "fix heap update, old row size too long. bufSize:%" PRIu32 ", maxRowSize:%" PRIu32 ", labelID:%" PRIu32
            ", trxId:%" PRIu64 ".",
            opInfo->fetchRowInfo->bufSize, ctx->heap->regularInfo.maxRowSize, ctx->heapCfg.labelId,
            ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
        // 出现这种情况，后续遇到事务回滚会出问题，此处assert维护分析
        DB_ASSERT(false);
        return PROGRAM_LIMIT_EXCEEDED_HEAP_MAX_ROW_SIZE;
    }

    // 在RR事务下，为了获知并发冲突，需要读取最新已提交数据以判断是否可见
    // 由于当前乐观设定为表级别冲突域不存在上述冲突，因此这里判断为悲观事务
    ret = HeapCheckRrTrxConflict(ctx, opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    ret = HeapInitAllocRowInfoImpl(ctx, tupleBuf->buf, tupleBuf->bufSize, opInfo->allocRowInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    if (SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX) {
        ret = HeapUpdatePreWithOptimisticTrx(ctx, opInfo);
        if (ret != STATUS_OK_INTER) {
            HeapUpdateForUpdateFailedClear(ctx, opInfo);
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

static void HeapMergeUndoRecInUpd(HeapRunCtxT *ctx, TrxT *trx, HpPageFetchRowInfo *fetchRowInfo, HeapUpdOutPara *out)
{
    // 1.找到一个可以被优化的undo记录A
    UndoTupleCombineAddrT undoTupleInfo = {0};
    StatusInter ret = UndoGetMergeableRec(
        ctx->seRunCtx->undoCtx, &trx->trx.base.readView, fetchRowInfo->rowTrxInfo.rollPtr, &undoTupleInfo);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get mergeable undo rec unsucc.");
        return;
    } else if (undoTupleInfo.tuple.len == 0 || undoTupleInfo.tuple.data == NULL) {
        return;
    }

    // 2.合并该记录，即将前一版本内容B取出来写入到该记录A上
    uint64_t preRecPtr = undoTupleInfo.tuple.rollPtr;
    UndoRowOpInfoT rowOpInfo = {0};
    ret = HeapRepReadReReadRollBackInfo(ctx, preRecPtr, &rowOpInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "get next undo rec info unsucc.");
        return;
    }
    DmVertexLabelT *vertexLabel = HeapAmGetDmInfo(ctx);
    uint32_t secIndexNum = vertexLabel->metaVertexLabel->secIndexNum;
    // 索引删除记录A的物理addr
    uint32_t headSize = vertexLabel->commonInfo->isUseComArea ? sizeof(HpNormalRowHeadT) : sizeof(HpNormalFixRowHead);
    HeapDataT heapData = {.addr = *(HpTupleAddr *)(void *)&fetchRowInfo->srcRowInfo.itemPtr,
        .targetRollPtr = preRecPtr,
        .tupleBuf.buf = undoTupleInfo.tuple.data + headSize,
        .phyAddr = undoTupleInfo.tuple.data + headSize};
    heapData.undoTargetRollPtr = undoTupleInfo.fetchRowIdOrRollptr;
    UndoSimpleRelIdx(secIndexNum, &heapData, ctx, UNDO_TTREE_UPDATE_DEL, ctx->compV1Info.idxCtxArray);
    heapData.undoTargetRollPtr = preRecPtr;
    ret = UndoWriteRecInVersionChain(
        ctx->seRunCtx->undoCtx, fetchRowInfo->rowTrxInfo.rollPtr, &rowOpInfo, preRecPtr, true);
    if (ret != STATUS_OK_INTER) {
        return;
    }

    // 3.将B置为无效记录, 避免undo回滚/提交再次处理
    ret = RowUndoSetRecycled(ctx->seRunCtx->undoCtx, preRecPtr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "set undo rec recycle unsucc.");
        return;
    }

    // 索引更新前一版本B
    uint64_t rollptr = ctx->compV1Info.rollptrUpdOrDel;
    ctx->compV1Info.rollptrUpdOrDel = undoTupleInfo.fetchRowIdOrRollptr;
    // 合并后heapData.phyAddr内容已经是preRecPtr的内容
    UndoSimpleRelIdx(secIndexNum, &heapData, ctx, UNDO_TTREE_UPDATE, ctx->compV1Info.idxCtxArray);
    ctx->compV1Info.rollptrUpdOrDel = rollptr;
    return;
}

StatusInter HeapUpdateImpl(
    HeapRunCtxT *ctx, const HeapTupleBufT *tupleBuf, const HpItemPointerT *itemPtr, HeapUpdOutPara *out)
{
    DB_POINTER3(ctx, tupleBuf, itemPtr);
    DB_ASSERT(((TrxT *)ctx->seRunCtx->trx)->base.state != TRX_STATE_ABORT);
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageAllocRowInfoT pageAllocRowInfo = EmptyHpPageAllocRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, &pageAllocRowInfo);
    HeapUpdateInit(ctx, &opInfo, itemPtr);
    StatusInter ret = HeapUpdatePrepare(ctx, tupleBuf, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto EXIT;
    }

    ret = HeapUpdLogUndoAndSetTrxInfoImpl(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        HeapUpdateForUpdateFailedClear(ctx, &opInfo);
        goto EXIT;
    }
    // 索引第一段更新无失败返回  SimpRelSecIdxesUpdate
    (void)ctx->compV1Info.updFun(*(const HpTupleAddr *)(const void *)itemPtr, ctx->compV1Info.ttreeIdxUpdCtx, ctx);
    // 一般的更新, 只会发生reLatch, 但是各个page还是持有的. 但是大对象的更新, 最后是没有持有page的
    ret = HeapUpdateRowImpl(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "update row(%" PRIu32 ", %" PRIu32 ") unsucc, trxId %" PRIu64 " label %" PRIu32, itemPtr->pageId,
            itemPtr->slotId, ((TrxT *)ctx->seRunCtx->trx)->base.trxId, ctx->heapCfg.labelId);
        HeapUpdateForUpdateFailedClear(ctx, &opInfo);
    } else {
        HeapHandleAfterUpdateSuccessfully(ctx, tupleBuf, itemPtr, &opInfo, out->isUndoBypass);
    }
    HeapStatUpdate(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto EXIT;
    }
    ret = ctx->compV1Info.updFun(*(const HpTupleAddr *)(const void *)itemPtr, ctx->compV1Info.ttreeIdxUpdCtx, ctx);
    if ((ret == STATUS_OK_INTER) && SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX &&
        (ctx->hpControl.isFixPage || ((DmVertexLabelT *)HeapAmGetDmInfo(ctx))->commonInfo->isUseComArea)) {
        // todo 需要在TTree索引维护后再执行合并动作，用TRX_TYPE_NUM拦截，待重构
        // 仅在乐观场景，尽力而为去处理，处理失败不要影响正常流程
        HeapMergeUndoRecInUpd(ctx, (TrxT *)ctx->seRunCtx->trx, opInfo.fetchRowInfo, out);
    }
EXIT:
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter,
        SE_PG_LATCH_SORTER_NO_EXCEPTION);  // 所有操作结束后, 释放过程中未释放的 page
    return ret;
}

Status HeapLabelUpdateCompactWithHpTupleBuf(
    HpRunHdlT heapRunHdl, const HeapTupleBufT *heapTupleBuf, HpTupleAddr heapTupleAddr)
{
    // 仅光启 YANG 持久化恢复场景支持
    DB_UNUSED3(heapRunHdl, heapTupleBuf, heapTupleAddr);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

StatusInter HeapUpdatePartialImpl(
    HeapRunCtxT *ctx, const HeapTupleBufT *tupleBuf, const HpItemPointerT *itemPtr, uint32_t offsetOfRawData)
{
    return STATUS_OK_INTER;
}

// noteb: 可靠性看护, 长稳运行稳定后, 可以删除
void HeapConfirmForRealDel(const HeapRunCtxT *ctx, const HpPageFetchRowInfo *fetchRowInfo)
{
    DB_POINTER4(ctx, fetchRowInfo, ctx->seRunCtx, ctx->seRunCtx->trx);
    TrxT *trx = (TrxT *)ctx->seRunCtx->trx;
    if (trx->base.state == TRX_STATE_ROLLBACK) {
        // 插入操作的回滚，isDeleted肯定为false，并且一定是同一个事务才能这样操作
        DB_ASSERT(fetchRowInfo->srcRowInfo.rowHeadPtr.rowState->isDeleted == false);
        TrxIdT rowTrxId = HeapRowGetTrxId(&fetchRowInfo->rowTrxInfo);
        bool isSameTrx = rowTrxId == ((TrxT *)ctx->seRunCtx->trx)->base.trxId;
        DB_ASSERT(isSameTrx);
    } else if (((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_ACTIVE) {
        HeapCheckIsRealDelete(ctx, trx);
    }
}

StatusInter HeapDeleteInnerRowImpl(HeapRunCtxT *ctx, HpItemPointerT itemPtr, uint32_t *bufSize)
{
    DB_POINTER(ctx);
    // 一堆上下文的构造...
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageAllocRowInfoT pageAllocRowInfo = EmptyHpPageAllocRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, &pageAllocRowInfo);
    fetchRowInfo.srcRowInfo.itemPtr = itemPtr;
    HpPageFetchRowInfoInitCtrlFlag(&fetchRowInfo);
    fetchRowInfo.canAccessLinkDst = true;
    fetchRowInfo.canAccessSubRow = true;
    fetchRowInfo.isOnlyReadSrcRow = true;  // 只删除addr指向的 行, 不去查询关联的行!
    fetchRowInfo.isCanAccessMarkDelRow = true;
    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;

    DB_ASSERT(ctx->hpOperation <= HEAP_OPTYPE_MODIFY);  // 后面的fetch过程中, page加X锁
    uint16_t oldPageNum = ctx->pageSorter.pageNum;      // 记录当前page的个数
    HeapSetPageLatchActionForWrite(ctx);
    HeapSetLockActionBeforeDelete(ctx);
    // 查询, 获取当前 row 的偏移信息
    StatusInter ret = HeapFetchRow(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        HeapFreeOnePageLatch(ctx, itemPtr, oldPageNum);  // 只释放新增的page 的latch.
        return ret;
    }
    DB_ASSERT(fetchRowInfo.srcRowInfo.rowType == HEAP_VAR_ROW_LINK_DST_ROW ||
              fetchRowInfo.srcRowInfo.rowType == HEAP_VAR_ROW_LINK_DST_SLICE_DIR_ROW ||
              fetchRowInfo.srcRowInfo.rowType == HEAP_VAR_ROW_SLICE_SUB_ROW ||
              (fetchRowInfo.srcRowInfo.rowType == HEAP_VAR_ROW_LINK_SRC_ROW && fetchRowInfo.isSliceRows));
    ret = HeapPageDeleteRowImpl(ctx, &fetchRowInfo.srcRowInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "label %" PRIu32 " del row (type%" PRIu32 "), trxId %" PRIu64 "", ctx->heapCfg.labelId,
            (uint32_t)fetchRowInfo.srcRowInfo.rowType, ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
    }
    // 只释放新增的page 的latch.
    HeapFreeOnePageLatch(ctx, itemPtr, oldPageNum);
    return ret;
}

StatusInter HeapDeleteLinkDstRowImpl(
    HeapRunCtxT *ctx, HeapRowInfoT *linkDstRowInfo, bool isSliceRows, bool isKeepOtherPageLatch)
{
    DB_POINTER2(ctx, linkDstRowInfo);
    StatusInter ret;
    if (!isKeepOtherPageLatch && !HeapIsFreeLatchUnderLabel(ctx)) {
        // 只需要保留目的行的页锁，其他页锁全部释放
        // 一般场景如，单行删除，回滚等都没有必要继续持有除目的行以外的锁。
        // 但批量删除场景是在一页上逐行删除，需要保留源行页
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, linkDstRowInfo->itemPtr.pageId);
    }
    if (!isSliceRows) {  // 非分片场景, 后面只需要删除 linkDst 即可.
        ret = HeapPageDeleteRowImpl(ctx, linkDstRowInfo);
    } else {
        ret = HeapDeleteSliceDirAndSubRows(ctx, linkDstRowInfo);
    }
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "label %" PRIu32 " del row, trxId %" PRIu64 "", ctx->heapCfg.labelId,
            ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
    }
    return ret;
}

StatusInter HeapRealDelete(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo)
{
    StatusInter ret = STATUS_OK_INTER;
#ifndef NDEBUG
    HeapConfirmForRealDel(ctx, fetchRowInfo);  // noteb: 可靠性看护, 长稳运行稳定后, 可以删除
#endif
    ret = HeapPageDeleteRowImpl(ctx, &fetchRowInfo->srcRowInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "label %" PRIu32 " del row, trxId %" PRIu64 "", ctx->heapCfg.labelId,
            ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
    } else {
        DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP,
            "HeapDeleteImpl label %" PRIu32 " del row (%" PRIu32 ",%" PRIu32 "), trxId:%" PRIu64, ctx->heapCfg.labelId,
            fetchRowInfo->srcRowInfo.itemPtr.pageId, fetchRowInfo->srcRowInfo.itemPtr.slotId,
            ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
    }
    if (fetchRowInfo->is2LinkDstRow) {
        ret = HeapDeleteLinkDstRowImpl(ctx, &fetchRowInfo->dstRowInfo, fetchRowInfo->isSliceRows, false);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "label %" PRIu32 " del row, trxId %" PRIu64 "", ctx->heapCfg.labelId,
                ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
        }
    }
    if (ret == STATUS_OK_INTER) {
        return HeapStatDelete(ctx, fetchRowInfo);
    } else {
#ifndef SYS32
        // arm32 内存满的情况下，客户端可能attach不到FSM page返回错误, 但是要确保删除流程成功
        DB_ASSERT(false);
#endif
        // normal模式只有回滚、清理版本链会走到这里，不应该失败
        // 大表锁、RU模式下真正删除失败，因为没有记undo，不会回滚，对curItem无法进行维护
    }
    return ret;
}

StatusInter HeapDeleteImpl(HeapRunCtxT *ctx, HpItemPointerT itemPtr, bool isMarkDelete)
{
    DB_POINTER2(ctx, ctx->seRunCtx);
    DB_ASSERT(((TrxT *)ctx->seRunCtx->trx)->base.state != TRX_STATE_ABORT);
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageAllocRowInfoT pageAllocRowInfo = EmptyHpPageAllocRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, &pageAllocRowInfo);
    fetchRowInfo.srcRowInfo.itemPtr = itemPtr;
    // 物理删除可以访问被标记为删除的行
    // RR事务需要读主版本，用来记录undo，因此也需要读markDelRow
    fetchRowInfo.isCanAccessMarkDelRow = !isMarkDelete || (SeTransGetIsolationLevel(ctx->seRunCtx) == REPEATABLE_READ);
    fetchRowInfo.isOnlyReadSrcRow = isMarkDelete;
    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;

    DB_ASSERT(ctx->hpOperation <= HEAP_OPTYPE_MODIFY);  // 后面的fetch过程中, page加X锁
    HeapSetPageLatchActionForWrite(ctx);
    HeapSetOperationForDelete(ctx, isMarkDelete);
    // Normal模式下: isMarkDelete为真时, HeapSetLockActionBeforeMarkDelete
    // isMarkDelete == false时, HeapSetLockActionBeforeDelete (LITE模式，详细见g_heapTrxAmForGroup的赋值)
    HeapSetLockFunc(ctx, ctx->hpControl.trxContrl.hpAmIndex);

    // 查询, 获取当前 row 的信息
    StatusInter ret = HeapFetchRow(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
        return ret;
    }

    // 在RR事务下，为了获知并发冲突，需要读取最新已提交数据以判断是否可见
    // 由于当前乐观设定为表级别冲突域不存在上述冲突，因此这里判断为悲观事务
    ret = HeapCheckRrTrxConflict(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
        return ret;
    }

    if (isMarkDelete) {
        ret = HeapDeleteRowSetMarkImpl(ctx, &opInfo);
        if (ret == STATUS_OK_INTER) {
            HeapStatMarkDelete(ctx, &fetchRowInfo);
        }
    } else {
        ret = HeapRealDelete(ctx, &fetchRowInfo);
    }
    /* 释放 pageSorter 持有的latch */
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    return ret;
}

inline uint64_t HeapGetLastTruncateTrxIdImpl(HeapRunCtxT *ctx)
{
    return ctx->heap->regularInfo.lastTruncateTrxId;
}

#ifdef __cplusplus
}
#endif
