/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: Heap page的接口实现
 * Author: panghaisheng
 * Create: 2020-09-01
 */

#include "se_heap_page.h"
#include "se_heap_mem_inner.h"
#include "se_heap_slice_row.h"
#include "se_heap_addr.h"
#include "db_memcpy.h"

#ifdef __cplusplus
extern "C" {
#endif

#define HEAP_GET_VAR_PAGE_MAX_TRY_CNT (1)

void HeapVarPageInitNewBlock(HeapRunCtxT *ctx, HVPageHeadT *pageHead)
{
    HeapVarPageInitPage(pageHead, ctx->heapCfg.slotExtendSize);
}

inline static void HeapVarPageResetMaxFreePosEnd(HVPageHeadT *pageHead)
{
    // 当page没有row时, freePosEnd 就是 下一个page的起始addr了.
    pageHead->freePosEnd = SeGetPageTotalSize(&pageHead->baseHead.pageHead);
}

void HeapVarPageInitPage(HVPageHeadT *pageHead, uint16_t slotExtendSize)
{
    DB_POINTER(pageHead);
    // free 链表上所有slot都是 isFree=true, 以offset=无效值 作为结束
    pageHead->freeSlotHead.isFree = true;
    // 压缩流程需要以offset做[降序]排序, 初值应该设置为最小
    pageHead->freeSlotHead.offset = PAGE_ROW_INVALID_LOGIC_OFFSET;
    pageHead->slotCnt = 0;
    pageHead->freeSlotCnt = 0;
    pageHead->slotDirEnd = (PageSizeT)HEAP_ALIGN_SIZE_OF(HVPageHeadT);
    HeapVarPageResetMaxFreePosEnd(pageHead);
    pageHead->maxModifyRowOffset = (uint16_t)pageHead->freePosEnd;
    pageHead->baseHead.pageHead.freeSize = HeapVarPageGetContinueFreeSize(pageHead);
    pageHead->slotExtendSize = slotExtendSize;
    COMPILER_BARRIER;
    pageHead->baseHead.pageHead.pageState = PAGE_USING;
}

HOT_FUN_READ HpRowStateT *HeapVarPageGetRowStateImpl(HVPageHeadT *pageHead, const HpRowSlotT *itemSlot)
{
    DB_POINTER2(pageHead, itemSlot);
    PageSizeT realOffset = PAGE_GET_ROW_REAL_OFFSET(itemSlot->offset);
    return HeapVarPageGetRowStateByRowOffset(pageHead, realOffset);
}

void HeapVarPageInitRowForInsertTuple(HpNormalRowHeadT *rowHead, const HeapTupleBufT *tuple, RowTrxInfoT txInfo)
{
    DB_POINTER2(rowHead, tuple);
    HeapResetRowState(&rowHead->rowState);
    rowHead->magicNum = HEAP_ROW_MAGIC_NUM;
    rowHead->trxInfo = txInfo;
    rowHead->size = (PageSizeT)tuple->bufSize;
    rowHead->rollbackReserveSize = rowHead->size;
    // 此处因为性能原因使用GMDB项目自己实现的DbFastMemcpy而非memcpy_s，需要保证内存复制合法。
    DbFastMemcpy((uint8_t *)(rowHead + 1), tuple->bufSize, tuple->buf, tuple->bufSize);
}

void HeapVarPageInitNormalRowImpl(
    const HeapRunCtxT *ctx, HpNormalRowHeadT *rowHead, const HpPageAllocRowInfoT *allocRowInfo)
{
    DB_POINTER2(rowHead, allocRowInfo);
    DB_UNUSED(ctx);  // 保留后面做事务
    HeapResetRowState(&rowHead->rowState);
    rowHead->magicNum = HEAP_ROW_MAGIC_NUM;
    rowHead->trxInfo = allocRowInfo->newTrxInfo;
    rowHead->rowState.isDeleted = allocRowInfo->markDeletedState;
    rowHead->size = (PageSizeT)allocRowInfo->bufSize;
    // 此处因为性能原因使用GMDB项目自己实现的DbFastMemcpy而非memcpy_s，需要保证内存复制合法。
    DbFastMemcpy((uint8_t *)(rowHead + 1), rowHead->size, allocRowInfo->buf, allocRowInfo->bufSize);
    // undoReserveSize在normal行设定规则：
    // 1.初始化时，undoReserveSize赋值为存储的实际size
    // 2.更新时，undoReserveSize赋值为存储过的size在回滚/提交前的最大长度
    rowHead->rollbackReserveSize = DB_MAX(rowHead->size, allocRowInfo->newRowInfo.rollbackReserveSize);
    DB_ASSERT(rowHead->rollbackReserveSize <= ctx->heapCfg.maxRowRawSizeInPage);
}

void HeapVarPageInitLinkSrcRowImpl(
    const HeapRunCtxT *ctx, HpLinkRowHeadT *rowHead, const HpPageAllocRowInfoT *allocRowInfo, bool isKeepRowReserve)
{
    DB_POINTER2(rowHead, allocRowInfo);
    DB_UNUSED(ctx);  // 保留后面做事务
    // undoReserveSize在LinkSrc行设定规则：
    // 1.分片行新插入/跳转行首次更新的情况，预留rollbackReserveSize为0即可
    // 2.当normal行更新为跳转行，为了undo成功需要保留原来normal行的预留空间字段，rollbackReserveSize的大小继续预留
    PageSizeT rollbackReserveSize = 0;
    if (isKeepRowReserve) {
        rollbackReserveSize = rowHead->rollbackReserveSize;
        if (HeapPageIsNormalRow(&rowHead->rowState)) {
            rollbackReserveSize = ((HpNormalRowHeadT *)rowHead)->rollbackReserveSize;
        }
        DB_ASSERT(rollbackReserveSize <= ctx->heapCfg.maxRowRawSizeInPage);
    }
    HeapResetRowState(&rowHead->rowState);
    rowHead->rowState.isLinkSrc = true;
    rowHead->magicNum = HEAP_ROW_MAGIC_NUM;
    rowHead->rollbackReserveSize = rollbackReserveSize;
    rowHead->bufSize = allocRowInfo->bufSize;
    HpItemPointerT linkItemPtr;
    if (allocRowInfo->sliceRowInfo.isSliceRow) {
        rowHead->rowState.isSliceHead = true;
        linkItemPtr = allocRowInfo->sliceRowInfo.sliceDirRowPtr;
    } else {
        linkItemPtr = allocRowInfo->newRowInfo.itemPtr;  // 已经存在的跳转目的行
    }
    HeapSetLinkRowAddr(rowHead, linkItemPtr);
    rowHead->srcTrxInfo = allocRowInfo->newTrxInfo;
    rowHead->rowState.isDeleted = allocRowInfo->markDeletedState;
}

void HeapUpdateVarRowToLinkSrcSetInfo(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, bool isKeepRowReserve)
{
    const HpPageAllocRowInfoT *allocRowInfo = opInfo->allocRowInfo;
    HeapRowInfoT *srcRowInfo = &opInfo->fetchRowInfo->srcRowInfo;
    HpLinkRowHeadT *rowHead = (HpLinkRowHeadT *)srcRowInfo->rowHeadPtr.normalRowHead;
    HeapVarPageInitLinkSrcRowImpl(ctx, rowHead, allocRowInfo, isKeepRowReserve);
}

void HeapVarPageInitLinkDstRowImpl(
    const HeapRunCtxT *ctx, HpLinkRowHeadT *rowHead, const HpPageAllocRowInfoT *allocRowInfo)
{
    DB_POINTER2(rowHead, allocRowInfo);
    DB_UNUSED(ctx);  // 保留后面做事务
    HeapResetRowState(&rowHead->rowState);
    rowHead->rowState.isLinkDst = true;
    rowHead->magicNum = HEAP_ROW_MAGIC_NUM;
    HeapSetLinkRowAddr(rowHead, allocRowInfo->sliceRowInfo.linkSrcRowPtr);  // 非 slice 场景, 也复用这个字段
    rowHead->dstInfo.trxId = allocRowInfo->newTrxInfo.trxId;
    rowHead->dstInfo.size = (PageSizeT)allocRowInfo->bufSize;
    // 此处因为性能原因使用GMDB项目自己实现的DbFastMemcpy而非memcpy_s，需要保证内存复制合法。
    DbFastMemcpy((uint8_t *)(rowHead + 1), allocRowInfo->bufSize, allocRowInfo->buf, allocRowInfo->bufSize);
}

void HeapVarPageInitLinkDstSliceDirRowImpl(
    const HeapRunCtxT *ctx, HpLinkRowHeadT *rowHead, const HpPageAllocRowInfoT *allocRowInfo)
{
    DB_POINTER3(ctx, rowHead, allocRowInfo);
    HeapResetRowState(&rowHead->rowState);
    rowHead->rowState.isLinkDst = true;
    rowHead->rowState.isSliceHead = true;
    rowHead->magicNum = HEAP_ROW_MAGIC_NUM;
    // SliceDirRow行头也记录大对象的大小，否则update commit/rollback fetch 历史版本时，去获取大对象大小会比较麻烦
    rowHead->bufSize = allocRowInfo->bufSize;
    HeapSetLinkRowAddr(rowHead, allocRowInfo->sliceRowInfo.linkSrcRowPtr);
    rowHead->dstInfo.size = (PageSizeT)allocRowInfo->sliceRowInfo.sliceDirMemLen;
    rowHead->dstInfo.trxId = allocRowInfo->newTrxInfo.trxId;

    HpSliceRowDirT *sliceDir = (HpSliceRowDirT *)(rowHead + 1);
    *sliceDir = *allocRowInfo->sliceRowInfo.sliceDir;  // 拷贝目录信息
    if (sliceDir->sliceRowNum > 0) {  // 根据目录数量拷贝. 由于申请分片的顺序, 如果分片addr未产生, 就不用拷贝了.
        errno_t ret = memcpy_s(sliceDir->sliceRowEntries, rowHead->dstInfo.size - sizeof(HpSliceRowDirT),
            allocRowInfo->sliceRowInfo.sliceDir->sliceRowEntries, sliceDir->sliceRowNum * sizeof(HpSliceRowDirEntryT));
        DB_ASSERT(ret == EOK);  // 上面的计算虽然麻烦, 实际从代码逻辑看是等价的, 可以考虑简化?
    }
}

void HeapVarPageAllocNewSlot(HVPageHeadT *pageHead)
{
    PageSizeT slotOffset = HeapVarPageExtendSlotDir(pageHead);
    HpRowSlotT *slot = (HpRowSlotT *)((uintptr_t)pageHead + slotOffset);
    slot->isFree = true;
    *slot = pageHead->freeSlotHead;
    pageHead->freeSlotHead.offset = PAGE_GET_ROW_LOGIC_OFFSET(slotOffset);
    pageHead->freeSlotHead.isFree = true;
    pageHead->freeSlotCnt++;
}

void HeapVarPageInsertSlot(HVPageHeadT *pageHead, PageSizeT storeSize, HeapRowInfoT *newRowInfo, bool *isNewAllocSlot)
{
    DB_POINTER2(pageHead, newRowInfo);
    HpRowSlotT *slot = NULL;
    PageSizeT slotOffset;
    DB_ASSERT(pageHead->freeSlotHead.offset != PAGE_ROW_INVALID_LOGIC_OFFSET); /* 复用 slot的 freeList */
    slotOffset = PAGE_GET_ROW_REAL_OFFSET(pageHead->freeSlotHead.offset);
    slot = (HpRowSlotT *)((uintptr_t)pageHead + slotOffset);
    DB_ASSERT(slot->isFree);
    // 更新 freeSlotHead
    pageHead->freeSlotHead = *slot;
    DB_ASSERT(pageHead->freeSlotCnt > 0);
    pageHead->freeSlotCnt--;

    DB_ASSERT(slotOffset < pageHead->slotDirEnd);
    PageSizeT slotId = HeapVarPageGetSlotIdBySlotOffset(slotOffset, pageHead->slotExtendSize);
    HeapSetAddrSid(&newRowInfo->itemPtr, slotId);
    newRowInfo->slotOffset = slotOffset;
    newRowInfo->slot = slot;
    HeapVarPageSetRowOffset(pageHead, slot, storeSize);
    DB_ASSERT(
        PAGE_GET_ROW_REAL_OFFSET(slot->offset) == ((uintptr_t)(newRowInfo->rowHeadPtr.rowState) - (uintptr_t)pageHead));
    pageHead->baseHead.pageHead.freeSize = (uint16_t)(pageHead->baseHead.pageHead.freeSize - storeSize);
}

StatusInter HeapVarPageInsertNewSlot(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, bool *isNewAllocSlot)
{
    HpPageAllocRowInfoT *allocRowInfo = opInfo->allocRowInfo;
    HeapRowInfoT *newRowInfo = &allocRowInfo->newRowInfo;
    HVPageHeadT *pageHead = newRowInfo->pageHeadPtr.varPageHead;
    if (pageHead->freeSlotHead.offset == PAGE_ROW_INVALID_LOGIC_OFFSET) {
        HeapVarPageAllocNewSlot(pageHead); /* 扩展 slot dictionary */
        *isNewAllocSlot = true;
    }
    HeapVarPageInsertSlot(pageHead, allocRowInfo->storeSize, newRowInfo, NULL);
    return STATUS_OK_INTER;
}

inline void HeapVarPageUpdateSlot(
    const HeapRunCtxT *ctx, HVPageHeadT *pageHead, HpRowSlotT *slot, PageSizeT oldStoreSize, PageSizeT newStoreSize)
{
    DB_POINTER3(ctx, pageHead, slot);
    HeapVarPageSetRowOffset(pageHead, slot, newStoreSize);
    int32_t freeSize = (int32_t)pageHead->baseHead.pageHead.freeSize + oldStoreSize;
    int32_t availSize = (int32_t)ctx->fsmMgr->cfgInfo.availSize;
    DB_ASSERT(freeSize <= availSize);
    freeSize = freeSize - (int32_t)newStoreSize;
    DB_ASSERT(freeSize >= 0);
    pageHead->baseHead.pageHead.freeSize = (uint16_t)freeSize;
}

StatusInter HeapAllocVarPageBySize(
    HeapRunCtxT *ctx, HeapPageAllocCtx *heapPageAllocCtx, PageSizeT requireSize, bool *isCompressed)
{
    DB_POINTER3(ctx, heapPageAllocCtx, isCompressed);
    StatusInter ret = STATUS_OK_INTER;
    bool isSizeEnough = false;
    uint32_t loopCnt = 0;
    const uint32_t maxRetry = 10;  // 超过重试次数，打印warn日志信息便于定位.
    while (true) {
        heapPageAllocCtx->isNewBlock = false;
        /*
         * 第一次通过原requireSize获取页
         * 由于兼容v1关闭了页整理，一次获取不到时使用最大值获取新页，避免死循环
         * 原可能死循环的原因:对应page的(free size)大于requireSize, 但是(连续free size)小于requireSize, 一直判断同一个页
         * v1分片大小固定，申请新的页不会导致原有空间浪费
         */
        ret = (loopCnt < HEAP_GET_VAR_PAGE_MAX_TRY_CNT) ?
                  HeapFsmGetPageBySize(ctx, requireSize, heapPageAllocCtx) :
                  HeapFsmGetPageBySize(ctx, ctx->fsmMgr->cfgInfo.availSize, heapPageAllocCtx);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "get page, size %" PRIu16, requireSize);
            return ret;
        }

        HVPageHeadT *pageHead = heapPageAllocCtx->pageHeadPtr.varPageHead;
        /* page 加写锁, 如果本次操作前已经加了锁, pageLatchNum 不变, 本函数也不要加解锁, 避免错误地解锁 */
        uint32_t pageLatchNum = (uint32_t)ctx->pageSorter.pageNum;
        HeapLatchPageWhenAllocingRow(ctx, heapPageAllocCtx);
        if (heapPageAllocCtx->isNewBlock) {
            HeapVarPageInitNewBlock(ctx, pageHead);
        }
        /* 只有判断压缩之后, 空间足够场景, 才会进行压缩, 所以while循环最多执行了当前page的一个压缩 */
        StatusInter confirmRet = HeapVarPageConfirmSize(ctx, pageHead, requireSize, isCompressed, &isSizeEnough);
        if (isSizeEnough) {
            break;
        }
        /* 空间不足, 释放页锁, 把page归还FSM */
        HeapReleasePage(ctx, heapPageAllocCtx, FSM_INVALID_MAX_ROW_SIZE, pageHead->baseHead.pageHead.freeSize);
        /* 释放page的锁, 重新查找合适的page */
        if (pageLatchNum != (uint32_t)ctx->pageSorter.pageNum) {
            // 由于本次查找合适page的过程产生的新page, 大小不合适, 释放锁
            SePgLatchSorterRemovePage(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, heapPageAllocCtx->pageInfo.pageId);
        }
        if (confirmRet != STATUS_OK_INTER) {  // 页面整理失败了（动态内存不足），多次重试会挂死，直接报错处理
            return confirmRet;
        }
        if (++loopCnt > maxRetry) {
            DB_LOG_WARN(GMERR_INTERNAL_ERROR, "AllocVarPage, tryCnt:%" PRIu32 " trxId %" PRIu64 " label %" PRIu32,
                loopCnt, ((TrxT *)ctx->seRunCtx->trx)->base.trxId, ctx->heapCfg.labelId);
            loopCnt = 0;  // 重置一下
        }
    }
    return STATUS_OK_INTER;
}

StatusInter HeapVarPageAllocRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER(opInfo);
    HpPageAllocRowInfoT *allocRowInfo = opInfo->allocRowInfo;
    HeapRowInfoT *newRowInfo = &allocRowInfo->newRowInfo;
    HVPageHeadT *pageHead = newRowInfo->pageHeadPtr.varPageHead;
    /* 格式化row head, buf内容拷贝 */
    newRowInfo->rowHeadPtr.rowState =
        (HpRowStateT *)((uintptr_t)pageHead + HeapVarPageGetNewRowOffset(pageHead, allocRowInfo->storeSize));
    bool isNewAllocSlot = false;
#ifndef NDEBUG
    uint16_t oldFreeSize = pageHead->baseHead.pageHead.freeSize;
#endif
    if (allocRowInfo->isNewSlot) {
        newRowInfo->rollbackReserveSize = 0;
        HeapPageInitRowByRowType(ctx, allocRowInfo, newRowInfo->rowHeadPtr);
        StatusInter ret = HeapVarPageInsertNewSlot(ctx, opInfo, &isNewAllocSlot);
        if (ret != STATUS_OK_INTER) {
            // 唯一的失败场景是申请trxUndo失败，失败时还没有设置slot相关信息，因此HeapPageInitRowByRowType中的步骤不会生效
            return ret;
        }
    } else {
        DB_ASSERT(false);  // 预期这段逻辑走不到了
        newRowInfo->rollbackReserveSize = opInfo->fetchRowInfo->curRowInfo->rollbackReserveSize;
        newRowInfo->itemPtr = opInfo->fetchRowInfo->srcRowInfo.itemPtr;
        newRowInfo->slot = opInfo->fetchRowInfo->srcRowInfo.slot;
        HeapPageInitRowByRowType(ctx, allocRowInfo, newRowInfo->rowHeadPtr);
        HeapVarPageUpdateSlot(
            ctx, pageHead, newRowInfo->slot, (PageSizeT)opInfo->updInPageOldStoreSize, allocRowInfo->storeSize);
    }
#ifndef NDEBUG
    uint16_t newAllocSlotSize = isNewAllocSlot ? HeapVarPageGetOneSlotSize(pageHead->slotExtendSize) : 0;
    // 使用的大小不能超过请求的大小
    DB_ASSERT(oldFreeSize >= pageHead->baseHead.pageHead.freeSize + newAllocSlotSize);
    DB_ASSERT((oldFreeSize - pageHead->baseHead.pageHead.freeSize) - newAllocSlotSize <= allocRowInfo->requireSize);
#endif
    opInfo->hasUpdateRowInfo = true;
    return STATUS_OK_INTER;
}

StatusInter HeapVarPageReadPrevVersionImpl(const HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo)
{
    return STATUS_OK_INTER;
}

void HeapVarPageFreeRow(HeapRowInfoT *delRowInfo, uint32_t fsmAvailSize)
{
    DB_POINTER(delRowInfo);
    PageSizeT slotOffset = delRowInfo->slotOffset;
    HpRowSlotT *slot = delRowInfo->slot;
    PageSizeT tupleStoreSize;

    HpRowStateT *rowState = HeapVarPageGetRowStateImpl(delRowInfo->pageHeadPtr.varPageHead, slot);
    DB_ASSERT(rowState != NULL);
    HpRowHeadPtrT rowHead = {.rowState = rowState};
    if (HeapPageIsNormalRow(rowState)) {
        DB_ASSERT(delRowInfo->rowType == HEAP_VAR_ROW_NORMAL_ROW);
        DB_ASSERT(rowHead.normalRowHead->magicNum == HEAP_ROW_MAGIC_NUM);
        tupleStoreSize = HeapPageGetMinRowSize(rowHead.normalRowHead->size, HEAP_VAR_ROW_NORMAL_ROW);
    } else if (HeapPageIsLinkSrcRow(rowState)) {
        DB_ASSERT(delRowInfo->rowType == HEAP_VAR_ROW_LINK_SRC_ROW);
        DB_ASSERT(rowHead.linkRowHead->magicNum == HEAP_ROW_MAGIC_NUM);
        tupleStoreSize = HeapPageGetMinRowSize(0, HEAP_VAR_ROW_LINK_SRC_ROW);
    } else if (HeapPageIsLinkDstRow(rowState)) {
        DB_ASSERT(delRowInfo->rowType == HEAP_VAR_ROW_LINK_DST_ROW ||
                  delRowInfo->rowType == HEAP_VAR_ROW_LINK_DST_SLICE_DIR_ROW);
        DB_ASSERT(rowHead.linkRowHead->magicNum == HEAP_ROW_MAGIC_NUM);
        tupleStoreSize = HeapPageGetMinRowSize(rowHead.linkRowHead->dstInfo.size, HEAP_VAR_ROW_LINK_DST_ROW);
    } else if (HeapPageIsSliceSubRow(rowState)) {
        DB_ASSERT(rowHead.sliceSubRowHead->magicNum == HEAP_ROW_MAGIC_NUM);
        tupleStoreSize = HeapPageGetMinRowSize(rowHead.sliceSubRowHead->size, HEAP_VAR_ROW_SLICE_SUB_ROW);
    } else {
        DB_ASSERT(false);  // 不可能
        return;
    }
    HeapVarPageDeleteSlot(delRowInfo->pageHeadPtr.varPageHead, tupleStoreSize, slot, slotOffset);
    DB_ASSERT(delRowInfo->pageHeadPtr.varPageHead->baseHead.pageHead.freeSize <= fsmAvailSize);
    HeapPageUpdatePageStat(delRowInfo->pageHeadPtr.varPageHead, rowState);
    return;
}

void HeapVarPageDeleteRow(HeapRunCtxT *ctx, HeapRowInfoT *delRowInfo)
{
    HeapVarPageFreeRow(delRowInfo, ctx->fsmMgr->cfgInfo.availSize);
}

void HeapVarPageDeleteSlot(HVPageHeadT *pageHead, PageSizeT tupleStoreSize, HpRowSlotT *slot, PageSizeT slotOffset)
{
    DB_POINTER2(pageHead, slot);
    DB_ASSERT(slotOffset == ((uintptr_t)slot - (uintptr_t)pageHead));
    /* 加入 slot的 freeList, 更新 freeSlotHead */
    *slot = pageHead->freeSlotHead;
    pageHead->freeSlotHead.offset = PAGE_GET_ROW_LOGIC_OFFSET(slotOffset);
    pageHead->freeSlotHead.isFree = true;
    pageHead->freeSlotCnt++;
    pageHead->baseHead.pageHead.freeSize = (uint16_t)(pageHead->baseHead.pageHead.freeSize + tupleStoreSize);
}

HOT_FUN_READ StatusInter HeapVarPageGetSlotBySlotIdImpl(
    HVPageHeadT *pageHead, uint32_t rowId, PageSizeT *slotOffset, HpRowSlotT **itemSlot)
{
    DB_POINTER3(pageHead, slotOffset, itemSlot);
    uint64_t oneSlotSize = HeapVarPageGetOneSlotSize(pageHead->slotExtendSize);
    uint64_t offset = (uint64_t)HEAP_ALIGN_SIZE_OF(HVPageHeadT) + (rowId * oneSlotSize);
    DB_ASSERT(offset <= DB_INVALID_UINT16);
    bool isInvalidOffset = offset < HEAP_ALIGN_SIZE_OF(HVPageHeadT) || offset > pageHead->slotDirEnd - oneSlotSize ||
                           offset != CALC_ALIGN_SIZE(offset, ALIGN_SIZE_TWO);
    if (SECUREC_UNLIKELY(isInvalidOffset)) {
        SE_LAST_ERROR(NO_DATA_HEAP_ITEM_OFFSET_INVALID, "slotOffset(%" PRIu64 ") out of range(%zu, %zu)", offset,
            HEAP_ALIGN_SIZE_OF(HVPageHeadT), (size_t)(pageHead->slotDirEnd - oneSlotSize));
        return NO_DATA_HEAP_ITEM_OFFSET_INVALID;
    }

    HpRowSlotT *slot = (HpRowSlotT *)((uintptr_t)pageHead + (uintptr_t)offset);
    if (SECUREC_UNLIKELY(slot->isFree)) {
        return NO_DATA_HEAP_ITEM_NOT_EXIST;
    }
    *slotOffset = (PageSizeT)offset;
    *itemSlot = slot;
    return STATUS_OK_INTER;
}

int32_t HeapVarPageRowSlotCmpByOffset2(const void *p1, const void *p2)
{
    const HeapCompressNodeT *slot1 = p1;
    const HeapCompressNodeT *slot2 = p2;
    return slot2->slotOffset - slot1->slotOffset;
}

void HeapVarPageSortSlotDir(HVPageHeadT *pageHead, HeapCompressNodeT *sortSlotDir)
{
    DB_POINTER2(pageHead, sortSlotDir);
    HpRowSlotT *slot = (HpRowSlotT *)(void *)((uintptr_t)pageHead + HEAP_ALIGN_SIZE_OF(HVPageHeadT));
    PageSizeT oneSlotSize = HeapVarPageGetOneSlotSize(pageHead->slotExtendSize);
    bool isStrictOrder = true;
    PageSizeT lastSlotOffset = DB_MAX_UINT16;
    for (uint32_t i = 0; i < pageHead->slotCnt; ++i) {  // 获取page上的slot dir的指针
        sortSlotDir[i].slot = slot;
        sortSlotDir[i].slotOffset = slot->offset;
        if (isStrictOrder) {
            if (lastSlotOffset < sortSlotDir[i].slotOffset) {
                isStrictOrder = false;
            }
            lastSlotOffset = sortSlotDir[i].slotOffset;
        }
        slot = (HpRowSlotT *)(void *)((uintptr_t)slot + oneSlotSize);
    }
    if (!isStrictOrder) {
        /* 快速排序, 按照 slot指向的 offset [降序]排序, 确保在这个slot dir上, 所有slot指向的 offset 在内存的顺序排序的
         * 搬迁则可以直接根据sortSlotDir进行遍历, 然后搬迁 */
        qsort(sortSlotDir, pageHead->slotCnt, sizeof(HeapCompressNodeT), HeapVarPageRowSlotCmpByOffset2);
    }
}

StatusInter HeapVarPageCompress(HeapRunCtxT *ctx, HVPageHeadT *pageHead)
{
    DB_POINTER2(ctx, pageHead);
    /* 申请内存, 使用相同数量的指针指向各个 slot, 在本函数释放 */
    HeapCompressNodeT *sortSlotDir =
        DbDynMemCtxAlloc(ctx->seRunCtx->sessionMemCtx, sizeof(HeapCompressNodeT) * pageHead->slotCnt);
    if (sortSlotDir == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "malloc unsucc for page compress,labelId %" PRIu32 "", ctx->heapCfg.labelId);
        return OUT_OF_MEMORY_INTER;
    }
    // 获取[row的offset降序]的slot dir, 首个slot指向页尾的row; 逐个向页尾紧密搬迁
    HeapVarPageSortSlotDir(pageHead, sortSlotDir);
    HeapVarPageResetMaxFreePosEnd(pageHead);  // 重置freePosEnd, 然后逐个搬迁
    PageSizeT maxModifyRowOffset = pageHead->maxModifyRowOffset;
    for (uint32_t i = 0; i < pageHead->slotCnt; ++i) {
        HpRowSlotT *slot = sortSlotDir[i].slot;
        PageSizeT rowOffset = PAGE_GET_ROW_REAL_OFFSET(sortSlotDir[i].slotOffset);
        if (rowOffset < pageHead->slotDirEnd) {
            /* rowOffset 在压缩到了 page的slot dictionary 区间, 表示结束压缩流程 */
            DB_ASSERT(slot->isFree || slot->offset == PAGE_ROW_INVALID_LOGIC_OFFSET);
            break;
        }
        if (rowOffset > maxModifyRowOffset) {
            // 在 maxModifyRowOffset 之后, 说明该行其实没有更新或者删除, 不用搬迁
            pageHead->freePosEnd = rowOffset;
            continue;
        }

        // 移动一个有效的slot对应的row, 向 页尾 搬迁
        PageSizeT storeSize = INVALID_OFFSET16;
        HpRowHeadPtrT rowHead;
        rowHead.rowState = HeapVarPageGetRowStateByRowOffset(pageHead, rowOffset);
        if (HeapPageIsNormalRow(rowHead.rowState) || HeapPageIsLinkSrcRow(rowHead.rowState)) {
            storeSize = HeapPageGetRowReserveSize(rowHead);
        } else if (HeapPageIsLinkDstRow(rowHead.rowState)) {
            storeSize = HeapPageGetMinRowSize(rowHead.linkRowHead->dstInfo.size, HEAP_VAR_ROW_LINK_DST_ROW);
        } else if (HeapPageIsSliceSubRow(rowHead.rowState)) {
            storeSize = HeapPageGetMinRowSize(rowHead.sliceSubRowHead->size, HEAP_VAR_ROW_SLICE_SUB_ROW);
        } else {
            DB_ASSERT(false);  // 该场景不可能出现
        }
        // 如果位置一致, 不用搬迁, 不一致则需要搬迁
        PageSizeT newRowOffset = HeapVarPageGetNewRowOffset(pageHead, storeSize);
        if (newRowOffset != rowOffset) {
            DB_ASSERT(rowOffset < newRowOffset);
            errno_t ret =
                memmove_s((uint8_t *)((uintptr_t)pageHead + newRowOffset), storeSize, rowHead.rowState, storeSize);
            DB_ASSERT(ret == EOK);
            HeapVarPageSetRowOffset(pageHead, slot, storeSize);
        } else {
            pageHead->freePosEnd = newRowOffset;
        }
    }
    DbDynMemCtxFree(ctx->seRunCtx->sessionMemCtx, sortSlotDir);
    pageHead->baseHead.pageHead.freeSize = HeapVarPageGetContinueFreeSize(pageHead);
    pageHead->maxModifyRowOffset = (uint16_t)pageHead->freePosEnd;
    return STATUS_OK_INTER;
}

StatusInter HeapVarPageCompressRow(HeapRunCtxT *ctx, HVPageHeadT *pageHead)
{
    return HeapVarPageCompress(ctx, pageHead);
}

// 返回ok，表示连续空间足够或者页面整理正常执行，需结合isSizeEnough看是否满足大小
StatusInter HeapVarPageConfirmSize(
    HeapRunCtxT *ctx, HVPageHeadT *pageHead, PageSizeT requireSize, bool *isCompressed, bool *isSizeEnough)
{
    DB_POINTER(isCompressed);
    *isCompressed = false;
    PageSizeT continueSize = HeapVarPageGetContinueFreeSize(pageHead);
    if (continueSize >= requireSize) {
        *isSizeEnough = true;
        return STATUS_OK_INTER;
    }

    *isSizeEnough = false;
    return STATUS_OK_INTER;
}

#ifdef __cplusplus
}
#endif
