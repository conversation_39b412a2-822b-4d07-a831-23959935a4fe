/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: Heap access 接口实现 (涉及模型层的调用)
 * Author: panghaisheng
 * Create: 2020-8-12
 */

#include "se_heap_access_inner.h"
#include "db_utils.h"
#include "adpt_define.h"
#include "securec.h"
#include "se_dfx.h"
#include "se_heap_base.h"
#include "se_heap_mem_inner.h"
#include "se_heap_fetch.h"
#include "se_heap_batch.h"
#include "se_heap_hc_inner.h"
#include "se_heap_utils.h"
#include "se_heap_stats.h"
#include "se_log.h"
#include "se_daemon.h"
#include "se_rsm_heap.h"
#include "adpt_rdtsc.h"
#include "dm_data_prop.h"
#include "dm_data_kv.h"
#include "db_crash_debug.h"
#include "db_tuple_buffer.h"
#include "db_memcpy.h"
#include "se_heap_fixed.h"
#include "db_rsm_undo.h"
#include "se_heap_access_common.h"
#include "se_heap_am_inner.h"

#define HEAP_EXIST_BIT_MASK (1u << 5)

#ifdef __cplusplus
extern "C" {
#endif

inline Status HeapLabelCreateImpl(SeRunCtxHdT seRunCtx, HeapAccessCfgT *heapCfg, ShmemPtrT *heapShmAddr)
{
    StatusInter ret = STATUS_OK_INTER;
    if (heapCfg->isUseRsm) {
        ret = HeapRsmCreate(heapCfg, false, heapShmAddr);
    } else {
        ret = HeapCreate(NULL, heapCfg, heapShmAddr);
    }
    return DbGetExternalErrno(ret);
}

Status HeapLabelRecoveryInfoCreateImpl(HeapAccessCfgT *heapCfg, ShmemPtrT *containerInfoShmAddr)
{
    RsmHeapContainerInfoT *heapContainerInfo = DbRsmAlloc(sizeof(RsmHeapContainerInfoT), containerInfoShmAddr);
    if (heapContainerInfo == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc containerInfo, labelId:%" PRIu32, heapCfg->labelId);
        return GMERR_OUT_OF_MEMORY;
    }
    heapContainerInfo->fixRowSize = heapCfg->fixRowSize;
    heapContainerInfo->heapTrmId = heapCfg->heapFileId;
    heapContainerInfo->tableSpaceId = heapCfg->tableSpaceId;
    heapContainerInfo->tableSpaceIndex = heapCfg->tableSpaceIndex;
    heapContainerInfo->maxItemNum = heapCfg->maxItemNum;
    return GMERR_OK;
}

void HeapRsmConfigUndoLogForMaxItemNum(
    RsmUndoRecordT *rsmUndoRecord, ShmemPtrT containerInfoShmAddr, uint64_t maxItemNum)
{
    DB_POINTER(rsmUndoRecord);
    RsmUndoOpRecordT *opRecord = RsmUndoGetNextFreeRecord(rsmUndoRecord, rsmUndoRecord->usedUndoRecNum);
    opRecord->recordType = RSM_UNDO_HEAP_ALTER_CONFIG;
    opRecord->rsmUndoContainerConfigRec.maxItemNum = maxItemNum;
    opRecord->rsmUndoContainerConfigRec.shmAddr = containerInfoShmAddr;
    RsmUndoMoveNextFreeRecord(rsmUndoRecord);
}

Status HeapLabelRecoveryInfoUpdateMaxItemNum(
    ShmemPtrT containerInfoShmAddr, uint64_t maxItemNum, RsmUndoRecordT *rsmUndoRec)
{
    RsmHeapContainerInfoT *heapContainerInfo = DbShmPtrToAddr(containerInfoShmAddr);
    if (heapContainerInfo == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get heap info, segId:%" PRIu32 ", offset:%" PRIu32,
            containerInfoShmAddr.segId, containerInfoShmAddr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    HeapRsmConfigUndoLogForMaxItemNum(rsmUndoRec, containerInfoShmAddr, heapContainerInfo->maxItemNum);
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_ALTER_CONFIG_CHANGE_HEAP_BEFORE);
    heapContainerInfo->maxItemNum = maxItemNum;
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_ALTER_CONFIG_CHANGE_HEAP_AFTER);
    RsmUndoRollBackCurRecord(rsmUndoRec);
    return GMERR_OK;
}

void HeapRsmConfigUndoLogForFixRowSize(
    RsmUndoRecordT *rsmUndoRecord, ShmemPtrT containerInfoShmAddr, uint16_t fixRowSize)
{
    DB_POINTER(rsmUndoRecord);
    RsmUndoOpRecordT *opRecord = RsmUndoGetNextFreeRecord(rsmUndoRecord, rsmUndoRecord->usedUndoRecNum);
    opRecord->recordType = RSM_UNDO_TABLE_INFO_CHANGE_FIX_ROW_SIZE;
    opRecord->rsmUndoFixRowSizeRec.fixRowSize = fixRowSize;
    opRecord->rsmUndoFixRowSizeRec.shmAddr = containerInfoShmAddr;
    RsmUndoMoveNextFreeRecord(rsmUndoRecord);
}

Status HeapLabelRecoveryInfoUpdateFixRowSize(
    ShmemPtrT containerInfoShmAddr, PageSizeT fixRowSize, RsmUndoRecordT *rsmUndoRec, bool isUpgrade)
{
    RsmHeapContainerInfoT *heapContainerInfo = DbShmPtrToAddr(containerInfoShmAddr);
    if (heapContainerInfo == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get heap info, segId:%" PRIu32 ", offset:%" PRIu32,
            containerInfoShmAddr.segId, containerInfoShmAddr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    HeapRsmConfigUndoLogForFixRowSize(rsmUndoRec, containerInfoShmAddr, fixRowSize);
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_CHANGE_HEAP_FIX_ROW_SIZE_BEFORE);
    heapContainerInfo->fixRowSize = fixRowSize;
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_CHANGE_HEAP_FIX_ROW_SIZE_AFTER);
    if (!isUpgrade) {
        RsmUndoRollBackCurRecord(rsmUndoRec);
    }  // 升级场景在QryRsmSetTableUpgradeInfo中一起回收rsmUndo
    return GMERR_OK;
}

Status HeapLabelRecoveryInfoGetFixRowSize(ShmemPtrT containerInfoShmAddr, PageSizeT *fixRowSize)
{
    RsmHeapContainerInfoT *heapContainerInfo = DbShmPtrToAddr(containerInfoShmAddr);
    if (heapContainerInfo == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get heap info, segId:%" PRIu32 ", offset:%" PRIu32,
            containerInfoShmAddr.segId, containerInfoShmAddr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *fixRowSize = heapContainerInfo->fixRowSize;
    return GMERR_OK;
}

Status HeapLabelInfoRecoveryImpl(
    HeapAccessCfgT *heapCfg, ShmemPtrT containerInfoShmAddr, bool isTruncate, ShmemPtrT *heapShmAddr)
{
    RsmHeapContainerInfoT *heapContainerInfo = DbShmPtrToAddr(containerInfoShmAddr);
    if (heapContainerInfo == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get heap info, segId:%" PRIu32 ", offset:%" PRIu32,
            containerInfoShmAddr.segId, containerInfoShmAddr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (heapCfg->tableSpaceId != heapContainerInfo->tableSpaceId ||
        heapCfg->tableSpaceIndex != heapContainerInfo->tableSpaceIndex) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE,
            "rebuild heap, labelId:%" PRIu32 ", curTspId:%" PRIu32 ", oldTspId:%" PRIu32 ", curTspIdx:%" PRIu32
            ", oldTspIdx:%" PRIu32,
            heapCfg->labelId, heapCfg->tableSpaceId, heapContainerInfo->tableSpaceId, heapCfg->tableSpaceIndex,
            heapContainerInfo->tableSpaceIndex);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    heapCfg->maxItemNum = heapContainerInfo->maxItemNum;
    heapCfg->heapFileId = heapContainerInfo->heapTrmId;
    // 当所有升级版本导入完成后，再将heapContainerInfo上的fixRowSize刷新到heap上，不然此处直接刷新会导致升级版本导入失败
    // 恢复阶段表处于truncate状态不用重建fsm
    StatusInter ret = HeapRsmCreate(heapCfg, !isTruncate, heapShmAddr);
    return DbGetExternalErrno(ret);
}

Status HeapLabelInitMemFieldsImpl(SeRunCtxHdT seRunCtx, ShmemPtrT heapShmAddr)
{
    return DbGetExternalErrno(STATUS_OK_INTER);
}

Status HeapLabelResetFixRowImpl(HeapCntrAcsInfoT *heapCntrAcsInfo, PageSizeT fixRowSize)
{
    DB_ASSERT(!heapCntrAcsInfo->isUseRsm);
    StatusInter ret = HeapFixedRowSizeReset(heapCntrAcsInfo, fixRowSize);
    return DbGetExternalErrno(ret);
}

Status HeapLabelDropImpl(SeRunCtxHdT seRunCtx, HeapCntrAcsInfoT *heapCntrAcsInfo, RsmUndoRecordT *rsmUndoRec)
{
    if (!heapCntrAcsInfo->isUseRsm) {
        StatusInter ret = HeapDrop(seRunCtx, heapCntrAcsInfo);
        return DbGetExternalErrno(ret);
    } else {
        StatusInter ret = HeapRsmDrop(seRunCtx, heapCntrAcsInfo, rsmUndoRec);
        return DbGetExternalErrno(ret);
    }
}

inline Status HeapLabelCommitDropImpl(SeRunCtxT *seRunCtx, HeapCntrAcsInfoT *heapCntrAcsInfo)
{
    return GMERR_OK;
}

inline Status HeapLabelTruncateImpl(SeRunCtxT *seRunCtx, HeapCntrAcsInfoT *heapCntrAcsInfo, RsmUndoRecordT *rsmUndoRec)
{
    if (!heapCntrAcsInfo->isUseRsm) {
        StatusInter ret = HeapTruncate(seRunCtx, heapCntrAcsInfo);
        return DbGetExternalErrno(ret);
    } else {
        StatusInter ret = HeapRsmTruncate(seRunCtx, heapCntrAcsInfo, rsmUndoRec);
        return DbGetExternalErrno(ret);
    }
}

inline Status HeapLabelCommitTruncateImpl(SeRunCtxHdT seRunCtx, HeapCntrAcsInfoT *heapCntrAcsInfo)
{
    return GMERR_OK;
}

ALWAYS_INLINE_C Status HeapLabelInitRunctxImpl(HeapRunCtxAllocCfgT *cfg, HpRunHdlT heapRunHdl)
{
    HeapRunCtxT *heapRunCtx = heapRunHdl;
    // 客户端存在stmt复用，防止HeapInitRunCtx失败，heapRunHdl->seRunCtx被置空
    SeRunCtxT *seRunCtx = heapRunHdl->seRunCtx;
    *heapRunCtx = EmptyHeapRunCtxT();
    heapRunCtx->seRunCtx = seRunCtx;
    StatusInter ret = STATUS_OK_INTER;
    if (cfg->isUseRsm) {
        ret = HeapInitRsmRunCtx(heapRunCtx, cfg);
    } else {
        ret = HeapInitRunCtx(heapRunCtx, cfg, false);
    }
    return DbGetExternalErrno(ret);
}

inline Status HeapLabelAllocAndInitRunctxImpl(HeapRunCtxAllocCfgT *cfg, HpRunHdlT *heapRunHdl)
{
    DB_POINTER2(heapRunHdl, cfg);
    // 申请heap容器的运行上下文, 在HeapReleaseRunctx中配对释放
    StatusInter ret = HeapAllocRunctxImpl(cfg->seRunCtx, heapRunHdl);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return DbGetExternalErrno(ret);
    }
    if (cfg->isUseRsm) {
        ret = HeapInitRsmRunCtx(*heapRunHdl, cfg);
    } else {
        ret = HeapInitRunCtx(*heapRunHdl, cfg, false);
    }
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        DbDynMemCtxFree((*heapRunHdl)->seRunCtx->sessionMemCtx, *heapRunHdl);
        *heapRunHdl = NULL;
    }
    return DbGetExternalErrno(ret);
}

StatusInter HeapAllocRunctxForTrxMerge(SeRunCtxHdT seRunCtxHd, void *trxCloneMemCtx, HeapRunCtxT **ctx)
{
    DB_POINTER2(seRunCtxHd, ctx);
    SeRunCtxT *seRunCtx = seRunCtxHd;
    // 申请heap容器的运行上下文, 在HeapReleaseRunctx中配对释放
    HeapRunCtxT *newCtx = DbDynMemCtxAlloc(trxCloneMemCtx, sizeof(HeapRunCtxT));
    if (SECUREC_UNLIKELY(newCtx == NULL)) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc heap ctx");
        return OUT_OF_MEMORY_INTER;
    }
    *newCtx = EmptyHeapRunCtxT();
    newCtx->seRunCtx = seRunCtx;
    *ctx = newCtx;
    return STATUS_OK_INTER;
}

Status HeapLabelAllocAndInitRunctxForTrxMergeImpl(HeapRunCtxAllocCfgT *cfg, HpRunHdlT *heapRunHdl)
{
    DB_POINTER2(heapRunHdl, cfg);
    void *memCtx = (((TrxT *)cfg->seRunCtx->trx)->trxCloneMemCtx == NULL) ?
                       cfg->seRunCtx->sessionMemCtx :
                       ((TrxT *)cfg->seRunCtx->trx)->trxCloneMemCtx;
    // 申请heap容器的运行上下文, 在HeapReleaseRunctx中配对释放
    StatusInter ret = HeapAllocRunctxForTrxMerge(cfg->seRunCtx, memCtx, heapRunHdl);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return DbGetExternalErrno(ret);
    }
    if (cfg->isUseRsm) {
        ret = HeapInitRsmRunCtx(*heapRunHdl, cfg);
    } else {
        ret = HeapInitRunCtx(*heapRunHdl, cfg, false);
    }
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        DbDynMemCtxFree(memCtx, *heapRunHdl);
        *heapRunHdl = NULL;
    }
    return DbGetExternalErrno(ret);
}

inline Status HeapLabelTryInitRunctxImpl(HeapRunCtxAllocCfgT *cfg, HpRunHdlT heapRunHdl)
{
    HeapRunCtxT *heapRunCtx = heapRunHdl;
    // 客户端存在stmt复用，防止HeapInitRunCtx失败，heapRunHdl->seRunCtx被置空
    SeRunCtxT *seRunCtx = heapRunHdl->seRunCtx;
    *heapRunCtx = EmptyHeapRunCtxT();
    heapRunCtx->seRunCtx = seRunCtx;
    StatusInter ret = STATUS_OK_INTER;
    ret = cfg->isUseRsm ? HeapInitRsmRunCtx(heapRunCtx, cfg) : HeapInitRunCtx(heapRunCtx, cfg, true);
    return DbGetExternalErrno(ret);
}

inline void HeapLabelSetCommitCallback(const HpRunHdlT heapRunHdl, const CallBackStructT *commitCallBack)
{
    DB_POINTER2(heapRunHdl, commitCallBack);
    if (!heapRunHdl->heapTrxCtx) {
        // Temporarily silence log, adding back
        SE_ERROR(INTERNAL_ERROR_INTER, "NULL heapTrxCtx");
        DB_ASSERT(false);
        return;
    }
    heapRunHdl->heapTrxCtx->commitCallback = *commitCallBack;
}

Status HeapLabelInsertTupleBatchImpl(
    HpRunHdlT heapRunHdl, uint32_t batchSize, const HeapTupleBufT tuples[], HpBatchOutT batchOut[])
{
    HEAP_PERF_GET_START();
    _Static_assert(sizeof(HpBatchOutT) == sizeof(BatchOutT),
        "Memory requested by the upper layer, inconsistent size will cause out-of-bounds");
    StatusInter ret = HeapInsertTupleBatch(heapRunHdl, batchSize, tuples, (BatchOutT *)batchOut);
    HEAP_PERF_TIME_STAT(heapRunHdl, ret, HEAP_OP_INSERT, batchSize);
    return DbGetExternalErrno(ret);
}

Status HeapLabelHcLisPrefetch4InsertImpl(
    HpRunHdlT heapRunHdl, HcInsertPrefetchCtxT *prefetchCtx, uint32_t hcIndex, TupleAddr oldHeadAddr)
{
    StatusInter ret = HeapHcListPrefetch4Insert(heapRunHdl, prefetchCtx, hcIndex, oldHeadAddr);
    return DbGetExternalErrno(ret);
}

Status HeapLabelHcListPrefetch4DeleteImpl(HpRunHdlT heapRunHdl, HcDeletePrefetchCtxT *prefetchCtx, uint32_t hcIndex)
{
    StatusInter ret = HeapHcListPrefetch4Delete(heapRunHdl, prefetchCtx, hcIndex);
    return DbGetExternalErrno(ret);
}

void HeapLabelHcListDeleteWithPrefetchImpl(
    HpRunHdlT heapRunHdl, HcDeletePrefetchCtxT *prefetchCtx, uint32_t hcIndex, HpHcDeleteInfoT *deleteInfo)
{
    HeapHcListDeleteWithPrefetch(heapRunHdl, prefetchCtx, hcIndex, deleteInfo);
}

Status HeapLabelDeleteHpTupleImpl(HpRunHdlT heapRunHdl, HpTupleAddr heapTupleAddr)
{
    bool isMarkDelete = HeapIsOnNormalTrxLockMode(heapRunHdl) || heapRunHdl->hpControl.isBackGround ||
                        HeapLabelGetIsNeedMarkDelete(heapRunHdl);
    HEAP_PERF_GET_START();
    StatusInter ret = HeapDeleteImpl(heapRunHdl, *(HpItemPointerT *)&heapTupleAddr, isMarkDelete);
    HEAP_PERF_TIME_STAT(heapRunHdl, ret, HEAP_OP_DELETE, ONE_ROW);
    return DbGetExternalErrno(ret);
}

inline Status HeapLabelScanIncCursorNumImpl(HpRunHdlT heapRunHdl, const HeapBeginScanCfgT *beginScanCfg)
{
    DB_POINTER(beginScanCfg);
    StatusInter ret = HeapScanIncCursorNum(heapRunHdl, beginScanCfg);
    return DbGetExternalErrno(ret);
}

inline void HeapLabelRecordLongTermCursorImpl(HpRunHdlT heapRunHdl, const ScanCursorOpenerT *opener)
{
    HeapRecordLongTermCursor(heapRunHdl, opener->pid, opener->threadId, opener->openTime);
}

void HeapLabelEndScanImpl(HpRunHdlT heapRunHdl, HeapScanCursorHdlT heapCursor)
{
    DB_POINTER2(heapRunHdl, heapCursor);
    HeapScanCursorT *cursor = heapCursor;
    HeapScanDecCursorNumAndUnrecordLongCursor(heapRunHdl, cursor->isDefragmentation, &cursor->cursorOpener);
    HeapLabelReleaseScan(heapRunHdl, heapCursor);
}

void HeapLabelScanDecCursorNumAndUnrecordLongCursorImpl(
    HpRunHdlT heapRunHdl, bool isDefragmentation, const ScanCursorOpenerT *cursorOpener)
{
    DB_POINTER2(heapRunHdl, cursorOpener);
    HeapScanDecCursorNumAndUnrecordLongCursor(heapRunHdl, isDefragmentation, cursorOpener);
}

Status HeapLabelGetPhyItemNumImpl(HpRunHdlT heapRunHdl, uint64_t *phyItemNum)
{
    DB_POINTER(phyItemNum);
    if (heapRunHdl == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "NULL heap");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    bool isLableLatchMode = HeapIsFreeLatchUnderLabel(heapRunHdl);
    DbSpinLockLL(isLableLatchMode, &heapRunHdl->perfStat->lock);
    *phyItemNum = heapRunHdl->perfStat->phyItemNum;
    DbSpinUnlockLL(isLableLatchMode, &heapRunHdl->perfStat->lock);
    return GMERR_OK;
}

uint64_t HeapLabelGetPhyItemNumNoLockImpl(HpRunHdlT heapRunHdl)
{
    return heapRunHdl->perfStat->phyItemNum;
}

Status HeapLabelSetNewMaxItemNum(HeapCntrAcsInfoT *heapCntrAcsInfo, uint64_t maxItemNum)
{
    if (heapCntrAcsInfo->isUseRsm) {
        return DbGetExternalErrno(HeapLabelSetRsmNewMaxItemNum(heapCntrAcsInfo, maxItemNum));
    }
    HeapJumpT *heapJump = (HeapJumpT *)DbShmPtrToAddr(heapCntrAcsInfo->heapShmAddr);
    if (heapJump == NULL) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "heapJump addr novalid, segId:%" PRIu32 ", offset:%" PRIu32,
            heapCntrAcsInfo->heapShmAddr.segId, heapCntrAcsInfo->heapShmAddr.offset);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (!heapJump->isInit) {
        bool isLableLatchMode = DmIsLabelLatchMode(heapJump->cfg.ccType);
        DbSpinLockLL(isLableLatchMode, &heapJump->lock);
        heapJump->cfg.maxItemNum = maxItemNum;
        DbSpinUnlockLL(isLableLatchMode, &heapJump->lock);
        return GMERR_OK;
    }
    HeapT *heap = (HeapT *)DbShmPtrToAddr(heapJump->heapShmAddr);
    if (heap == NULL) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "heap addr novalid, segId:%" PRIu32 ", offset:%" PRIu32,
            heapJump->heapShmAddr.segId, heapJump->heapShmAddr.offset);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    HeapPerfStatT *perfStat = DbShmPtrToAddr(heap->perfStatShmPtr);
    bool isLableLatchMode = DmIsLabelLatchMode(heap->constInfo.ccType);
    DbSpinLockLL(isLableLatchMode, &perfStat->lock);
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_ITEMNUM,
        "HeapLabelSetNewMaxItemNum labelId:%" PRIu32 ", maxItemNum:%" PRIu64 ", new maxItemNum:%" PRIu64,
        heap->constInfo.labelId, heap->constInfo.maxItemNum, maxItemNum);
    heap->constInfo.maxItemNum = maxItemNum;
    DbSpinUnlockLL(isLableLatchMode, &perfStat->lock);
    return GMERR_OK;
}

Status HeapCalculateDFGRateOnTrxCommit(
    HpRunHdlT heapRunHdl, uint64_t writeBytes, uint64_t deleteBytes, uint64_t curItemNum, bool *fragRateCond)
{
    SeInstanceT *seInstance = (SeInstanceT *)heapRunHdl->seRunCtx->seIns;
    if (SECUREC_UNLIKELY(seInstance == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "SE instance %" PRIu16 " novalid", heapRunHdl->seRunCtx->instanceId);
        DB_ASSERT(false);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    HeapT *heap = heapRunHdl->heap;
    if (SECUREC_UNLIKELY(heap == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "NULL heap");
        DB_ASSERT(false);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint64_t totalUsedSize = writeBytes - deleteBytes;  // unit：B
    uint32_t pageCnt = LfsGetRealBlockCnt(&heap->fsm);
    uint64_t totalRowUsedSize;
    uint64_t totalPageHeadSize;
    if (heap->constInfo.pageType == HEAP_VAR_LEN_ROW_PAGE) {
        uint32_t rowHeadSize = DB_MIN(sizeof(HpNormalRowHeadT), sizeof(HpLinkRowHeadT));
        totalRowUsedSize =
            (uint64_t)curItemNum * (HeapVarPageGetOneSlotSize(heap->constInfo.slotExtendSize) + rowHeadSize);
        totalPageHeadSize = (uint64_t)pageCnt * HEAP_ALIGN_SIZE_OF(HVPageHeadT);
    } else {
        uint32_t rowHeadSize = DB_MIN(sizeof(HpNormalFixRowHead), sizeof(HpLinkSrcFixRowHead));
        totalRowUsedSize = (uint64_t)curItemNum * (rowHeadSize + heap->constInfo.slotExtendSize);
        totalPageHeadSize = HeapFixPageGetMgrInfoLen(heap->regularInfo.fixPageCfg) * pageCnt;
    }
    totalUsedSize += totalRowUsedSize + totalPageHeadSize;

    uint16_t pageSize = seInstance->seConfig.pageSize;  // unit：K
    uint64_t totalMemSize = (uint64_t)pageSize * pageCnt * DB_KIBI;
    if (totalMemSize == 0) {
        return GMERR_OK;
    }
    double fragmentationRate = (1 - ((double)totalUsedSize / (double)totalMemSize)) * DB_PERCENTAGE_BASE;
    uint64_t memoryThreshold = (uint64_t)seInstance->seConfig.fragmentationMemoryThreshold;
    if (totalMemSize > (memoryThreshold * DB_KIBI * DB_KIBI) &&
        fragmentationRate > seInstance->seConfig.fragmentationRateThreshold) {
        *fragRateCond = true;
    }
    return GMERR_OK;
}

Status HeapProcOnTrxCommitImpl(HpRunHdlT heapRunHdl, bool enableDefrag, bool *isLabelTruncate, bool *fragRateCond)
{
    DB_POINTER3(heapRunHdl, isLabelTruncate, fragRateCond);

    *isLabelTruncate = *fragRateCond = false;
    // lite模式(非后台线程)的phyItemNum、UserDefItemNum已在真正删除流程中维护
    HeapConcurrencyInfoT cncrInfo = HeapGetConcurrencyInfo(heapRunHdl);
    uint32_t deleteNum = cncrInfo.isLiteTrx ? 0 : heapRunHdl->heapTrxCtx->opStat.deleteNum;
    deleteNum = (heapRunHdl->hpControl.isBackGround) ? heapRunHdl->heapTrxCtx->opStat.deleteNum : deleteNum;
    uint64_t writeBytes, deleteBytes, phyItemNum;
    bool isLableLatchMode = HeapIsFreeLatchUnderLabel(heapRunHdl);
    DbSpinLockLL(isLableLatchMode, &heapRunHdl->perfStat->lock);
    DB_ASSERT(heapRunHdl->perfStat->phyItemNum >= deleteNum);
    heapRunHdl->perfStat->phyItemNum -= deleteNum;
    phyItemNum = heapRunHdl->perfStat->phyItemNum;
    writeBytes = heapRunHdl->perfStat->writeBytes;
    deleteBytes = heapRunHdl->perfStat->deleteBytes;
    DbSpinUnlockLL(isLableLatchMode, &heapRunHdl->perfStat->lock);

    DB_ASSERT(writeBytes >= deleteBytes);
    if (SECUREC_UNLIKELY(phyItemNum == 0 && cncrInfo.isSingleWriter && writeBytes != deleteBytes)) {
        // 只有一个写者，表中无数据以及写数据量 == 删数据量应该是同时达成，不应该走到这里
        // 当存在多个写者时，如果有并发的回滚事务，会导致暂时不一致
        // 乐观事务现在是后台purge来更新这个计数，因此事务提交时不应该检查（此时cncrInfo.isSingleWrite为false）
        SE_ERROR(
            INTERNAL_ERROR_INTER, "Label empty, write(%" PRIu64 ") != delete(%" PRIu64 ")", writeBytes, deleteBytes);
        DB_ASSERT(false);
        return GMERR_INTERNAL_ERROR;
    }
    if (!enableDefrag) {  // 没有开启碎片整理
        return GMERR_OK;
    }

    if (phyItemNum == 0) {  // 如果此时该表数据已全部删完，强制触发一次缩容
        *isLabelTruncate = true;
        return GMERR_OK;
    }

    return HeapCalculateDFGRateOnTrxCommit(heapRunHdl, writeBytes, deleteBytes, phyItemNum, fragRateCond);
}

void HeapLabelChangeFsmListIdxImpl(HpRunHdlT heapRunHdl)
{
    HeapRunCtxT *ctx = (HeapRunCtxT *)heapRunHdl;
    ctx->fsmMgr->fragmentedInfo.lastFragmentedFsmListIdx++;
}

void HeapLabelGetDefragmentationPageImpl(
    HpRunHdlT heapRunHdl, HpTupleCombineAddrT *defragmentAddr, bool *defragmentationEof)
{
    DB_POINTER3(heapRunHdl, defragmentAddr, defragmentationEof);

    PageAddrT addr = {0};
    HeapRunCtxT *ctx = (HeapRunCtxT *)heapRunHdl;
    StatusInter ret = LfsGetFragmentedBlock(ctx->seRunCtx->pageMgr, ctx->fsmMgr, &addr);
    if (ret != STATUS_OK_INTER) {
        *defragmentationEof = true;
        return;
    }
    HpItemPointerT *pageAddrPtr = (HpItemPointerT *)&defragmentAddr->tupleAddr;
    pageAddrPtr->pageId = addr.pageId;
    pageAddrPtr->slotId = 0;
    defragmentAddr->blockId = addr.blockId;
    return;
}

Status HeapLabelFreshCache(HpRunHdlT heapRunHdl)
{
    DB_POINTER(heapRunHdl);
    HeapRunCtxT *ctx = (HeapRunCtxT *)heapRunHdl;
    // pageCache失效
    ctx->runtimeInfo->cacheVersion++;

    // 缓存数据页指针刷新
    if (ctx->runtimeInfo->cachePageAddr.pageHead != NULL) {
        MdMgrT *md = (MdMgrT *)ctx->seRunCtx->pageMgr;
        PageAddrT pageAddr = ctx->runtimeInfo->cachePageAddr.pageAddr;
        PageIdT pageId = MdDeserializePageId(md, pageAddr.pageId, ctx->heapCfg.isUseRsm);
        StatusInter ret =
            MdGetPage(md, pageId, (uint8_t **)&ctx->runtimeInfo->cachePageAddr.pageHead, ENTER_PAGE_NORMAL, true);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "heap get cache page %" PRIu32, pageAddr.pageId);
            ctx->runtimeInfo->cachePageAddr.pageHead = NULL;
            return DbGetExternalErrno(ret);
        }
    }
    return GMERR_OK;
}

Status HeapFreeCachedPageImpl(HpRunHdlT heapRunHdl, bool *isOverTime)
{
    DB_POINTER(heapRunHdl);

    HeapRunCtxT *ctx = (HeapRunCtxT *)heapRunHdl;
    heapRunHdl->runtimeInfo->cacheVersion++;

    uint32_t spaceId = ctx->fsmMgr->cfgInfo.tableSpaceIndex;
    uint32_t labelId = ctx->heapCfg.labelId;
    DbInstanceHdT dbInstance = ((SeInstanceHdT)(ctx->seRunCtx->seIns))->dbInstance;
    void *labelRsmUndo = ((TrxT *)(heapRunHdl->seRunCtx->trx))->liteTrx.rsmUndoRec;
    FreeCachedPageParamT para = SeInitCachedPageParam(spaceId, labelId, dbInstance, labelRsmUndo);
    StatusInter ret = MdClearCachedPageList((MdMgrT *)(void *)ctx->pageMgr, &para, isOverTime, false);
    return DbGetExternalErrno(ret);
}

void HeapLabelProcAfterDfgmtImpl(HpRunHdlT heapRunHdl)
{
    DB_POINTER(heapRunHdl);
    StatusInter ret = LfsReleaseAllFreeBlock(heapRunHdl->seRunCtx->pageMgr, heapRunHdl->fsmMgr, true,
        ((TrxT *)(heapRunHdl->seRunCtx->trx))->liteTrx.rsmUndoRec);
    if (ret != STATUS_OK_INTER) {
        return;
    }
    bool isLableLatchMode = HeapIsFreeLatchUnderLabel(heapRunHdl);
    DbSpinLockLL(isLableLatchMode, &heapRunHdl->perfStat->lock);
    heapRunHdl->perfStat->defragmentationCnt++;
    heapRunHdl->perfStat->lastDefragTime = DbRdtsc();
    DbSpinUnlockLL(isLableLatchMode, &heapRunHdl->perfStat->lock);
}

Status HeapLabelGetMaxRowCapacityImpl(const HpRunHdlT heapRunHdl, uint32_t *maxRowCapacity)
{
    DB_POINTER(heapRunHdl);
    *maxRowCapacity = heapRunHdl->heap->regularInfo.maxRowSize;
    return GMERR_OK;
}

inline bool HeapLabelIsExceedBatchInsertSizeLimitImpl(const HpRunHdlT heapRunHdl, uint32_t maxBufSize)
{
    DB_POINTER2(heapRunHdl, heapRunHdl->heap);
    return maxBufSize < heapRunHdl->heapCfg.maxRowRawSizeInPage;
}

inline void HeapLabelSetRsmRecoveryFlag(HeapRunCtxT *ctx, bool isRsmRecovery)
{
    ctx->hpControl.isRsmRecovery = isRsmRecovery;
}

HeapHacInfoT HeapLabelGetHacInfoImpl(const HpRunHdlT heapRunHdl)
{
    DB_POINTER(heapRunHdl);
    HeapHacInfoT hacInfo = (HeapHacInfoT){0};
    if (heapRunHdl->hpControl.isFixPage) {
        // 这里为表级定义，每个表单独配置
        hacInfo.ccType = heapRunHdl->hpControl.ccType;
        hacInfo.isFixPage = heapRunHdl->hpControl.isFixPage;
        hacInfo.heapTrmId = heapRunHdl->heapCfg.heapTrmId;
        hacInfo.oneRowSize = heapRunHdl->heap->regularInfo.fixPageCfg.oneRowSize;
        hacInfo.rawRowSize = heapRunHdl->heap->regularInfo.fixPageCfg.rawRowSize;
        hacInfo.rowBegin = heapRunHdl->heap->regularInfo.fixPageCfg.rowBegin;
        hacInfo.rowCnt = heapRunHdl->heap->regularInfo.fixPageCfg.rowCnt;
        hacInfo.slotExtendSize = heapRunHdl->heap->regularInfo.fixPageCfg.slotExtendSize;

        // 这里为全局定义，全局配置一份，仅第一次初始化有效，不支持表级修改
        hacInfo.fixRowHead = sizeof(HpNormalFixRowHead);
        hacInfo.trmOffset = offsetof(PageHeadT, trmId);
        hacInfo.existBitMask = HEAP_EXIST_BIT_MASK;
        hacInfo.existOffset = 0;
    }
    return hacInfo;
}

Status HeapLabelRelabelIdImpl(SeRunCtxT *seRunCtx, uint64_t heapAddr, uint32_t labelId)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status HeapLabelRollbackRelabelIdImpl(SeRunCtxT *seRunCtx, uint64_t heapAddr, uint32_t labelId)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status HeapLabelCacheImpl(const HeapAccessCfgT *heapCfg, ShmemPtrT heapShmAddr, HeapT **heapPtr)
{
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
