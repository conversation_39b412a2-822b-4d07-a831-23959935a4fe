/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: Heap 接口实现
 * 调用层次划分: se_heap_access.c 感知模型
 *             se_heap.c 不感知模型层, 处理连续buffer的存储, 负责调用fsm, memData等其他模块
 *             se_heap_page.c 负责单个page上的操作
 * Author: panghaisheng
 * Create: 2020-8-12
 */
#include "se_heap_mem_inner.h"
#include "se_heap_access_dm.h"
#include "se_heap_slice_row.h"
#include "se_heap_addr.h"
#include "se_undo.h"
#include "se_trx_mgr.h"
#include "se_heap_stats.h"
#include "se_heap_fetch.h"
#include "se_heap_trx_acc_inner.h"
#include "se_heap_rsm_undo.h"
#include "se_log.h"
#include "adpt_spinlock.h"
#include "se_rsm_tablespace_am.h"
#include "se_rsm_heap.h"
#include "se_heap_fixed.h"
#include "se_heap_utils.h"
#include "se_undo_trx_resource.h"

#include "se_heap_common.h"
#include "se_heap_am_inner.h"

#ifdef __cplusplus
extern "C" {
#endif

#define RETRY_TIMES_ALLOWED_FOR_CUR_PAGE 10

inline static bool HeapLabelDowngradeSplitTime(uint64_t startTime)
{
    return DbExceedTime(startTime, DOWNGRADE_TIME_EXCEED);
}

typedef struct TagHeapPageListPara {
    bool isUseRsm;
    uint32_t tableSpaceIndex;
    uint32_t labelId;
} HeapPageListParaT;

static StatusInter HeapClearCachedPageList(SeInstanceT *seIns, HeapPageListParaT *para, bool deleteList)
{
    DB_POINTER2(seIns, para);
    /*
    1.仅开启device归还才清理缓存页链表
    2.保留内存不支持缓存页链表
    */
    StatusInter ret = STATUS_OK_INTER;
    MdMgrT *mdMgr = (MdMgrT *)seIns->mdMgr;
    if (mdMgr->enableReleaseDevice && !para->isUseRsm) {
        FreeCachedPageParamT cachePagePara =
            SeInitCachedPageParam(para->tableSpaceIndex, para->labelId, seIns->dbInstance, NULL);
        ret = MdClearCachedPageList(mdMgr, &cachePagePara, NULL, deleteList);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "heap clear list, spcId=%" PRIu32 ", labelId=%" PRIu32, para->tableSpaceIndex, para->labelId);
        }
    }
    return ret;
}

static StatusInter HeapCreateCachedPageList(MdMgrT *mdMgr, HeapJumpT *heapJump)
{
    DB_POINTER2(mdMgr, heapJump);
    /*
    1.仅开启device归还才会创建缓存页链表
    2.保留内存不支持缓存页链表
    */
    StatusInter ret = STATUS_OK_INTER;
    if (mdMgr->enableReleaseDevice && !heapJump->cfg.isUseRsm) {
        ret = MdCreateCachedPageList(mdMgr, heapJump->cfg.tableSpaceIndex, heapJump->cfg.labelId);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "create cache page list, spaceId = %" PRIu32 ", labelId = %" PRIu32,
                heapJump->cfg.tableSpaceIndex, heapJump->cfg.labelId);
        }
    }
    return ret;
}

void HeapMarkDeleteModiRowInfo(const HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    // 修改row上的信息: 更新事务字段信息, 设置删除标记位
    DB_POINTER2(ctx, opInfo);
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    if (ctx->hpControl.isFixPage) {
        HeapMarkDeleteModiFixRowInfo(fetchRowInfo, opInfo);
    } else if (fetchRowInfo->srcRowInfo.rowType == HEAP_VAR_ROW_NORMAL_ROW) {
        HpNormalRowHeadT *normalRowHead = fetchRowInfo->srcRowInfo.rowHeadPtr.normalRowHead;
        normalRowHead->rowState.isDeleted = true;
        normalRowHead->trxInfo = opInfo->allocRowInfo->newTrxInfo;
    } else {
        HpLinkRowHeadT *linkRowHead = fetchRowInfo->srcRowInfo.rowHeadPtr.linkRowHead;
        linkRowHead->rowState.isDeleted = true;
        linkRowHead->srcTrxInfo = opInfo->allocRowInfo->newTrxInfo;
    }
}

StatusInter HeapDeleteRowSetMarkImpl(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    uint64_t newRollPtr = 0;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    // 修改前, 获取事务字段的信息: 记录undo信息获取新的事务信息 或者 获取回滚的事务信息
    // Normal模式下: HeapUndoLogForUpdOrDel (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
    StatusInter ret = HeapLogUndoFunc(ctx, (uint32_t)TRX_OP_DELETE, opInfo, &newRollPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    // Normal模式下: HeapPrepareTrxInfoForUpdOrDel (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
    ret = HeapSaveTrxInfoFunc(ctx, TRX_OP_DELETE, opInfo, newRollPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    fetchRowInfo->isUndoOperationOk = true;

#ifndef NDEBUG
    HpRowStateT *rowState = fetchRowInfo->srcRowInfo.rowHeadPtr.rowState;
    if (SeTransGetTrxType(ctx->seRunCtx) == PESSIMISTIC_TRX) {
        DB_ASSERT(!rowState->isDeleted);
    }
#endif
    if (!ctx->heapCfg.isUseRsm) {
        HeapMarkDeleteModiRowInfo(ctx, opInfo);
    } else {
        SHM_CRASHPOINT_ONE(SHM_CRASH_HP_MARK_DEL_ROW_BEFORE_RSM_UNDO);
        HeapRsmUndoLogForMarkDelete(ctx, fetchRowInfo->srcRowInfo.itemPtr.pageId,
            (uint16_t)fetchRowInfo->srcRowInfo.itemPtr.slotId, RowGetTrxId(&fetchRowInfo->rowTrxInfo));
        SHM_CRASHPOINT_ONE(SHM_CRASH_HP_MARK_DEL_ROW_BEFORE);
        HeapMarkDeleteModiRowInfo(ctx, opInfo);
        SHM_CRASHPOINT_ONE(SHM_CRASH_HP_MARK_DEL_ROW_AFTER);
        HeapRsmUndoLogMovePrevRecord(ctx);
    }

    DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP, "MarkDelete label %" PRIu32 " row (%" PRIu32 ",%" PRIu32 "), trxId:%" PRIu64,
        ctx->heapCfg.labelId, fetchRowInfo->srcRowInfo.itemPtr.pageId, fetchRowInfo->srcRowInfo.itemPtr.slotId,
        ((TrxT *)ctx->seRunCtx->trx)->base.trxId);

    return ret;
}

void HeapInitLfs(const SeInstanceT *seInstance, HeapT *newHeap, PageTotalSizeT pageSize)
{
    DB_POINTER2(seInstance, newHeap);

    _Static_assert(sizeof(HVPageHeadT) == HEAP_ALIGN_SIZE_OF(HVPageHeadT), "The page header structure must be aligned");

    uint32_t availSize = GetHeapPageAvailSize(newHeap, pageSize);
    LfsCfgT lfsCfg = {.fsmFileId = newHeap->constInfo.heapFsmTrmId,
        .dataFileId = newHeap->constInfo.heapTrmId,
        .shmArrayMemCtxId = DB_SE_HEAP_SHM_CTX_ID,
        .labelId = newHeap->constInfo.labelId,
        .pageSize = pageSize,
        .pageType = newHeap->constInfo.pageType,
        .needLock = !DmIsLabelLatchMode(newHeap->constInfo.ccType),  // 表锁模式下，lfs操作可以不用加锁
        .isUseRsm = newHeap->constInfo.isUseRsm,
        .reserveSize = (uint16_t)HEAP_PAGE_FSM_RESERVED_SIZE(pageSize),
        .availSize = availSize,
        .tableSpaceIndex = newHeap->constInfo.tableSpaceIndex,
        .fsmTableSpaceIndex = newHeap->constInfo.fsmTableSpaceIndex};
    if (newHeap->constInfo.pageType == HEAP_FIX_LEN_ROW_PAGE) {
        DB_ASSERT(newHeap->regularInfo.maxRowSize < DB_MAX_UINT16);
        lfsCfg.reserveSize =
            HeapPageGetMinRowSize((PageSizeT)newHeap->regularInfo.maxRowSize, HEAP_FIX_ROW_NORMAL_ROW) +
            newHeap->constInfo.slotExtendSize;
    }

    LfsMgrInit(&newHeap->fsm, &lfsCfg);
}

Status HeapLabelDowngradeFetchPageImpl(HpRunHdlT ctx, HeapScanCursorHdlT cursor, bool *isScanEnd)
{
    DB_POINTER2(ctx, cursor);
    Status ret = GMERR_OK;
    DB_ASSERT(cursor->heapLabelDowngrade.isHeapLabelDowngrade);
    // 初始化的信息为全局的信息 放在while循环之外
    if (cursor->isFirstFetch) {
        HeapFixedRowDowngradeInitCfg(ctx->heap, cursor->heapLabelDowngrade.fixRowSize);
        cursor->isFirstFetch = false;
    }
#ifdef FEATURE_GQL
    if (ctx->heap->constInfo.skipRowLockPessimisticRR) {
        SE_ERROR(INTERNAL_ERROR_INTER, "this heap cannot upgrade");
        return GMERR_INTERNAL_ERROR;
    }
#endif
    uint64_t startTime = DbClockGetTsc();
    while (!cursor->isScanEnd) {
        if (cursor->scanPos.blockId >= LfsGetBlockCnt(&ctx->heap->fsm)) {
            // 全部page扫描结束
            cursor->isScanEnd = true;
            *isScanEnd = true;
            ret = GMERR_OK;
            break;
        }
        StatusInter retInter = HeapDowngradeFetchOnePage(cursor);
        // 降级流程可以修改页，会存在降级时有别的dml操作，当页被删空时，扫描到空页并返回页不存在，是正常情况。
        DB_ASSERT(retInter == STATUS_OK_INTER || retInter == INT_ERR_LFS_BLOCK_HAS_RELEASED ||
                  retInter == NO_DATA_HEAP_PAGE_NOT_EXIST);
        cursor->scannedPageCnt++;
        cursor->scanPos.blockId++;
        if (HeapLabelDowngradeSplitTime(startTime)) {
            // 如果跳出去了，这里cursor->isScanEnd还是false，证明不是扫描完了，而是需要喂狗
            ret = GMERR_OK;
            break;
        }
    }
    return ret;
}

void HeapCreateInitConstMember(
    const HeapAccessCfgT *heapCfg, PageTotalSizeT pageSize, SeInstanceT *seInstance, HeapT *newHeap)
{
    HeapConstInfoT *constInfo = &newHeap->constInfo;
    constInfo->pageType = heapCfg->pageType;
    constInfo->tupleType = heapCfg->tupleType;
    constInfo->slotExtendSize = heapCfg->slotExtendSize;
    constInfo->heapShmMemCtxId = seInstance->heapShmMemCtxId;
    constInfo->isYangBigStore = heapCfg->isYangBigStore;
    constInfo->isStatusMergeSubs = heapCfg->isStatusMergeSubs;
    constInfo->isPersistent = heapCfg->isPersistent;
    // 目前heap_mem暂不支持持久化，默认使用全量dfx功能
    constInfo->isLabelLockSerializable = heapCfg->isLabelLockSerializable;
    constInfo->verboseDfx = true;
    constInfo->isUseRsm = heapCfg->isUseRsm;
#ifdef FEATURE_GQL
    constInfo->skipRowLockPessimisticRR = heapCfg->skipRowLockPessimisticRR;
#endif
    constInfo->ccType = heapCfg->ccType;
    constInfo->trxType = heapCfg->trxType;
    constInfo->isolation = heapCfg->isolation;
    constInfo->labelId = heapCfg->labelId;
    constInfo->tableSpaceId = heapCfg->tableSpaceId;
    constInfo->tableSpaceIndex = heapCfg->tableSpaceIndex;
    constInfo->fsmTableSpaceIndex = heapCfg->fsmTableSpaceIndex;
    constInfo->maxItemNum = heapCfg->maxItemNum;
    constInfo->maxRowRawSizeInPage = HeapGetMaxRowRawSize(newHeap, pageSize);
    HEAP_ASSERT_ALIGNED(constInfo->maxRowRawSizeInPage);  // 每个分片应该是对齐的 (4字节)
    constInfo->heapTrmId = heapCfg->heapFileId;
    constInfo->heapFsmTrmId = heapCfg->heapFsmFileId;
}

void HeapRuntimeInfoInit(HeapRuntimeInfoT *runtimeInfo)
{
    runtimeInfo->cachePageAvail = false;
    runtimeInfo->cachePageAddr = EmptyPageAddrInfoT();
    runtimeInfo->cachePageFreeSize = 0;
    runtimeInfo->cachePageMaxRowSize = FSM_INVALID_MAX_ROW_SIZE;
    runtimeInfo->totalCursorNum = 0;
    runtimeInfo->cacheVersion = 0;
    runtimeInfo->rsmMigrationVersion = 0;
    DbSpinInit(&runtimeInfo->scanCursorLock);
}

static StatusInter HeapCreateInitRuntimeInfo(const ShmemPtrT baseShmAddr, ShmemPtrT *heapRuntimeInfoPtr)
{
    ShmemPtrT runtimeInfoPtr = (ShmemPtrT){
        .segId = baseShmAddr.segId,
        .offset = baseShmAddr.offset + sizeof(HeapT),
    };
    HeapRuntimeInfoT *runtimeInfo = (HeapRuntimeInfoT *)DbShmPtrToAddr(runtimeInfoPtr);
    if (runtimeInfo == NULL) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "heapRuntimeInfo, segId:%" PRIu32 ", offset:%" PRIu32,
            runtimeInfoPtr.segId, runtimeInfoPtr.offset);
        return INVALID_PARAMETER_VALUE_INTER;
    }
    HeapRuntimeInfoInit(runtimeInfo);
    runtimeInfo->totalCursorNum = 0;
    for (uint32_t i = 0; i < HEAP_SCAN_CURSOR_OPENER_TOP_3; i++) {
        runtimeInfo->cursorOpenerInit[i] = false;
    }
    runtimeInfo->allocCursorFailTimes = 0;
    *heapRuntimeInfoPtr = runtimeInfoPtr;
    return STATUS_OK_INTER;
}

static StatusInter HeapCreateInitPerfStat(const HeapJumpT *heapJump, ShmemPtrT *heapPerfStatShmPtr)
{
    ShmemPtrT perfStatPtr = (ShmemPtrT){
        .segId = heapJump->heapShmAddr.segId,
        .offset = heapJump->heapShmAddr.offset + sizeof(HeapT) + sizeof(HeapRuntimeInfoT),
    };
    HeapPerfStatT *perfStat = (HeapPerfStatT *)DbShmPtrToAddr(perfStatPtr);
    if (perfStat == NULL) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "perfStat, segId:%" PRIu32 ", offset:%" PRIu32, perfStatPtr.segId,
            perfStatPtr.offset);
        return INVALID_PARAMETER_VALUE_INTER;
    }
    DbSpinInit(&perfStat->lock);
    *heapPerfStatShmPtr = perfStatPtr;
    return STATUS_OK_INTER;
}

StatusInter HeapCreateInitNewHeap(SeInstanceT *seInstance, const HeapJumpT *heapJump, HeapT *newHeap)
{
    DB_POINTER3(seInstance, heapJump, newHeap);
    if (heapJump->cfg.pageType >= HEAP_INVALID_PAGE_TYPE) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "novalid page type %" PRId32 "", (int32_t)heapJump->cfg.pageType);
        return INVALID_PARAMETER_VALUE_INTER;
    }
    newHeap->magicNum = HEAP_VALID_MAGIC_NUM;
    PageTotalSizeT pageSize = (PageTotalSizeT)seInstance->seConfig.pageSize * DB_KIBI;
    HeapCreateInitConstMember(&heapJump->cfg, pageSize, seInstance, newHeap);
    StatusInter ret = HeapCreateInitRegularMember(&heapJump->cfg, pageSize, newHeap);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    ret = HeapCreateInitRuntimeInfo(heapJump->heapShmAddr, &newHeap->runtimeInfoShmPtr);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    ret = HeapCreateInitPerfStat(heapJump, &newHeap->perfStatShmPtr);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    newHeap->lockStatShmPtr = heapJump->lockStatShmPtr;
    HeapInitLfs(seInstance, newHeap, pageSize);
    return STATUS_OK_INTER;
}

typedef struct TagHeapShmemGroup {
    HeapT heap;
    HeapRuntimeInfoT heapRuntimeInfo;
    HeapPerfStatT heapPerfStat;
} HeapShmemGroupT;

StatusInter HeapLazyInit(HeapJumpT *heapJump)
{
    DB_POINTER(heapJump);

    StatusInter ret = STATUS_OK_INTER;
    DbSpinLock(&heapJump->lock);
    if (heapJump->isInit) {
        goto EXIT;
    }

    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(heapJump->cfg.seInstanceId);
    if (seInstance == NULL) {
        ret = INVALID_PARAMETER_VALUE_INTER;
        SE_LAST_ERROR(ret, "NULL seInstance:%" PRIu16, heapJump->cfg.seInstanceId);
        goto EXIT;
    }

    DbMemCtxT *heapMemCtx = (DbMemCtxT *)DbGetShmemCtxById(seInstance->heapShmMemCtxId, heapJump->cfg.seInstanceId);
    if (heapMemCtx == NULL) {
        ret = UNEXPECTED_NULL_VALUE_INTER;
        SE_LAST_ERROR(ret, "memCtxId %" PRIu32 " novalid", seInstance->heapShmMemCtxId);
        goto EXIT;
    }

    // 申请创建heap容器所需共享内存, 在删除heap的时候释放
    HeapT *heap = SeShmAlloc(heapMemCtx, sizeof(HeapShmemGroupT), &heapJump->heapShmAddr);
    if (heap == NULL) {
        ret = OUT_OF_MEMORY_INTER;
        SE_LAST_ERROR(ret, "shmMalloc heap");
        goto EXIT;
    }
    (void)memset_sp((void *)heap, sizeof(HeapShmemGroupT), 0, sizeof(HeapShmemGroupT));

    ret = HeapCreateInitNewHeap(seInstance, heapJump, heap);
    if (ret != STATUS_OK_INTER) {
        DbShmemCtxFree(heapMemCtx, heapJump->heapShmAddr);
        heapJump->heapShmAddr = DB_INVALID_SHMPTR;
        goto EXIT;
    }
    heapJump->isInit = true;
    DB_LOG_INFO("lazy init heap ok, labelId:%" PRIu32, heapJump->cfg.labelId);
EXIT:
    DbSpinUnlock(&heapJump->lock);
    return ret;
}

StatusInter HeapCreate(SeRunCtxHdT seRunCtx, const HeapAccessCfgT *heapCfg, ShmemPtrT *heapShmAddr)
{
    DB_UNUSED(seRunCtx);
    DB_POINTER2(heapCfg, heapShmAddr);
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(heapCfg->seInstanceId);
    if (seInstance == NULL) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "NULL seInstance:%" PRIu16, heapCfg->seInstanceId);
        return INVALID_PARAMETER_VALUE_INTER;
    }

    DbMemCtxT *heapMemCtx = (DbMemCtxT *)DbGetShmemCtxById(seInstance->heapShmMemCtxId, heapCfg->seInstanceId);
    if (heapMemCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "inv memCtxId:%" PRIu32, seInstance->heapShmMemCtxId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    if (heapCfg->pageType >= HEAP_INVALID_PAGE_TYPE) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "page type %" PRId32, (int32_t)heapCfg->pageType);
        return INVALID_PARAMETER_VALUE_INTER;
    }

    if (heapCfg->pageType == HEAP_FIX_LEN_ROW_PAGE) {
        PageTotalSizeT pageSize = (PageTotalSizeT)seInstance->seConfig.pageSize * DB_KIBI;
        if (heapCfg->fixRowSize > SeGetUndoPageMaxRecordSize(pageSize)) {
            SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "row size %" PRIu16, heapCfg->fixRowSize);
            return INVALID_PARAMETER_VALUE_INTER;
        }
    }

    if (heapCfg->labelId == SE_INVALID_LABEL_ID) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "heap labelId = %" PRId32, heapCfg->labelId);
        return INVALID_PARAMETER_VALUE_INTER;
    }

    // 申请创建heap容器中间体所需共享内存, 在删除heap的时候释放
    HeapJumpT *newHeapJump = SeShmAlloc(heapMemCtx, sizeof(HeapJumpT), heapShmAddr);
    if (newHeapJump == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "heap shmMalloc");
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_sp((void *)newHeapJump, sizeof(HeapJumpT), 0, sizeof(HeapJumpT));
    newHeapJump->cfg = *heapCfg;
    newHeapJump->cfg.heapFileId = SeGetNewTrmId(seInstance);
    newHeapJump->cfg.heapFsmFileId = SeGetNewTrmId(seInstance);
    newHeapJump->heapShmAddr = DB_INVALID_SHMPTR;
    newHeapJump->lockStatShmPtr = DB_INVALID_SHMPTR;
    newHeapJump->isInit = false;
    DbSpinInit(&newHeapJump->lock);
    StatusInter ret = HeapCreateCachedPageList((MdMgrT *)seInstance->mdMgr, newHeapJump);
    if (ret != STATUS_OK_INTER) {
        DbShmemCtxFree(heapMemCtx, *heapShmAddr);
        *heapShmAddr = DB_INVALID_SHMPTR;
        return ret;
    }
    DB_LOG_INFO("create heap jump ok, labelId:%" PRIu32, heapCfg->labelId);
    return STATUS_OK_INTER;
}

void HeapReleaseAllBlock(SeInstanceT *seIns, HeapT *heap, RsmUndoRecordT *rsmUndoRec)
{
    MdMgrT *md = (MdMgrT *)seIns->mdMgr;
    StatusInter ret = LfsReleaseAllBlock(md, &heap->fsm, rsmUndoRec, seIns->dbInstance);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "free all page unsucc (%" PRIu32 ")", heap->constInfo.heapTrmId);
    }
    (void)LfsMgrDestroy(md, &heap->fsm, seIns->dbInstance);
}

static inline void HeapFreeLockStat(DbMemCtxT *heapMemCtx, HeapT *heap, HeapJumpT *heapJump)
{
    if (DbIsShmPtrValid(heap->lockStatShmPtr)) {
        DbShmemCtxFree(heapMemCtx, heap->lockStatShmPtr);
        heap->lockStatShmPtr = DB_INVALID_SHMPTR;
        heapJump->lockStatShmPtr = DB_INVALID_SHMPTR;
    }
}

StatusInter HeapDrop(SeRunCtxT *seRunCtx, HeapCntrAcsInfoT *heapCntrAcsInfo)
{
    DB_UNUSED(seRunCtx);
    HeapJumpT *heapJump = (HeapJumpT *)DbShmPtrToAddr(heapCntrAcsInfo->heapShmAddr);
    if (SECUREC_UNLIKELY(heapJump == NULL)) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "heapJump in drop, segId:%" PRIu32 ", offset:%" PRIu32,
            heapCntrAcsInfo->heapShmAddr.segId, heapCntrAcsInfo->heapShmAddr.offset);
        return INVALID_PARAMETER_VALUE_INTER;
    }

    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(heapJump->cfg.seInstanceId);
    if (seInstance == NULL) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "NULL seInstance:%" PRIu16, heapJump->cfg.seInstanceId);
        return INVALID_PARAMETER_VALUE_INTER;
    }

    HeapPageListParaT para = {.isUseRsm = heapJump->cfg.isUseRsm,
        .tableSpaceIndex = heapJump->cfg.tableSpaceIndex,
        .labelId = heapJump->cfg.labelId};
    StatusInter ret = HeapClearCachedPageList(seInstance, &para, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "heap drop page list, spcId=%" PRIu32 ", labelId=%" PRIu32, para.tableSpaceIndex, para.labelId);
        return ret;
    }

    DbMemCtxT *heapMemCtx = (DbMemCtxT *)DbGetShmemCtxById(seInstance->heapShmMemCtxId, heapJump->cfg.seInstanceId);
    if (heapMemCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "memCtxId %" PRIu32 " inv", seInstance->heapShmMemCtxId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    if (SECUREC_LIKELY(heapJump->isInit)) {  // 释放真正的heap内存
        HeapT *heap = (HeapT *)DbShmPtrToAddr(heapJump->heapShmAddr);
        if (heap == NULL) {
            SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "heap in drop, segId:%" PRIu32 ", offset:%" PRIu32,
                heapJump->heapShmAddr.segId, heapJump->heapShmAddr.offset);
            return INVALID_PARAMETER_VALUE_INTER;
        }
        HeapReleaseAllBlock(seInstance, heap, NULL);
        heap->magicNum = HEAP_INVALID_MAGIC_NUM;
        DB_LOG_INFO("Drop Heap, labelId:%" PRIu32, heap->constInfo.labelId);
        HeapFreeLockStat(heapMemCtx, heap, heapJump);
        DbShmemCtxFree(heapMemCtx, heapJump->heapShmAddr);
    }
    if (DbIsShmPtrValid(heapJump->lockStatShmPtr)) {
        DbShmemCtxFree(heapMemCtx, heapJump->lockStatShmPtr);
        heapJump->lockStatShmPtr = DB_INVALID_SHMPTR;
    }
    DB_LOG_INFO("Drop heap jumper, labelId:%" PRIu32, heapJump->cfg.labelId);
    DbShmemCtxFree(heapMemCtx, heapCntrAcsInfo->heapShmAddr);
    return STATUS_OK_INTER;
}

StatusInter HeapTruncateImpl(MdMgrT *md, HeapT *heap, TrxMgrT *trxMgr, RsmUndoRecordT *rsmUndoRec)
{
    // Truncate所有页都释放了，缓存应该置空,且此处不用加锁
    HeapRuntimeInfoT *runtimeInfo = DbShmPtrToAddr(heap->runtimeInfoShmPtr);
    if (SECUREC_UNLIKELY(runtimeInfo == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "runtimeInfo inv, segId:%" PRIu32 ", offset:%" PRIu32,
            heap->runtimeInfoShmPtr.segId, heap->runtimeInfoShmPtr.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    // Truncate所有页都释放了，缓存应该置空,且此处不用加锁
    runtimeInfo->cachePageAvail = false;
    runtimeInfo->cachePageAddr = EmptyPageAddrInfoT();
    runtimeInfo->cachePageFreeSize = 0;
    runtimeInfo->cachePageMaxRowSize = FSM_INVALID_MAX_ROW_SIZE;
    runtimeInfo->cacheVersion++;
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(trxMgr->instanceId);
    if (seInstance == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "NULL seInstance:%" PRIu16, trxMgr->instanceId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_TRUNCATE_HEAP_RESET_INFO);
    HeapPageListParaT para = {.isUseRsm = heap->constInfo.isUseRsm,
        .tableSpaceIndex = heap->constInfo.tableSpaceIndex,
        .labelId = heap->constInfo.labelId};
    StatusInter ret = HeapClearCachedPageList(seInstance, &para, false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "heap clear page list, spcId=%" PRIu32 ", labelId=%" PRIu32, para.tableSpaceIndex, para.labelId);
    }
    ret = LfsReleaseAllBlock(md, &heap->fsm, rsmUndoRec, seInstance->dbInstance);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "free all page(%" PRIu32 ")", heap->constInfo.heapTrmId);
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_TRUNCATE_HEAP_FREE_BLOCK);
    LfsMgrReset(md, &heap->fsm, seInstance->dbInstance);
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_TRUNCATE_HEAP_RESET_FSM);
    HeapStatInitNewHeap(heap);
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_TRUNCATE_HEAP_INIT_STAT);
    heap->regularInfo.lastTruncateTrxId = TrxMgrGetAndIncMaxTrxId(trxMgr);
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_TRUNCATE_HEAP_RECORD_TRX_ID);
    DB_LOG_INFO("Truncate, labelId:%" PRIu32, heap->constInfo.labelId);
    return STATUS_OK_INTER;
}

StatusInter HeapTruncate(SeRunCtxT *seRunCtx, HeapCntrAcsInfoT *hpAcsInfo)
{
    DB_UNUSED(seRunCtx);
    HeapJumpT *heapJump = (HeapJumpT *)DbShmPtrToAddr(hpAcsInfo->heapShmAddr);
    if (SECUREC_UNLIKELY(heapJump == NULL)) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "heapJump in truncate, segId:%" PRIu32 ", offset:%" PRIu32,
            hpAcsInfo->heapShmAddr.segId, hpAcsInfo->heapShmAddr.offset);
        return INVALID_PARAMETER_VALUE_INTER;
    }

    if (SECUREC_UNLIKELY(!heapJump->isInit)) {
        DB_LOG_INFO("Truncate label:%" PRIu32 ", heap uninit", heapJump->cfg.labelId);
        return STATUS_OK_INTER;
    }

    HeapT *heap = (HeapT *)DbShmPtrToAddr(heapJump->heapShmAddr);
    if (SECUREC_UNLIKELY(heap == NULL)) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "heap in truncate, segId:%" PRIu32 ", offset:%" PRIu32,
            heapJump->heapShmAddr.segId, heapJump->heapShmAddr.offset);
        return INVALID_PARAMETER_VALUE_INTER;
    }

    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(hpAcsInfo->instanceId);
    if (seInstance == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "NULL seInstance:%" PRIu16 " in truncate, labelId:%" PRIu32,
            hpAcsInfo->instanceId, heap->constInfo.labelId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    TrxMgrT *trxMgr = (TrxMgrT *)DbShmPtrToAddr(seInstance->trxMgrShm);
    if (trxMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "get trxMgr in truncate, segId:%" PRIu32 ", offset:%" PRIu32 ", labelId:%" PRIu32,
            seInstance->trxMgrShm.segId, seInstance->trxMgrShm.offset, heap->constInfo.labelId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    MdMgrT *md = (MdMgrT *)seInstance->pageMgr;
    if (md == NULL) {
        SE_ERROR(INTERNAL_ERROR_INTER, "NULL pageMgr, heapTrmId:%" PRIu32 ", labelId:%" PRIu32,
            heap->constInfo.heapTrmId, heap->constInfo.labelId);
        return INTERNAL_ERROR_INTER;
    }
    return HeapTruncateImpl(md, heap, trxMgr, NULL);
}

StatusInter HeapAllocVarRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    bool isCompressed = false;
    HeapPageAllocCtx heapPageAllocCtx = EmptyHeapPageAllocCtx();
    HpPageAllocRowInfoT *allocRowInfo = opInfo->allocRowInfo;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;

    StatusInter ret = HeapAllocVarPageBySize(ctx, &heapPageAllocCtx, allocRowInfo->requireSize, &isCompressed);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    HVPageHeadT *pageHead = heapPageAllocCtx.pageHeadPtr.varPageHead;
    HeapSetAddrPid(&allocRowInfo->newRowInfo.itemPtr, heapPageAllocCtx.pageInfo.pageId);
    allocRowInfo->newRowInfo.pageHeadPtr.varPageHead = pageHead;

    // 如果是更新场景申请 新的 link row, 并且fsm分配到 和 src row 所在相同的 page, 则应该使用 normal row的格式进行更新
    // 但是 分片的各行, 不限制. --> linkSrc(sliceHead) 和 linkDst(sliceHead Dir) 可以在同一个页
    // undoLog合并场景，有可能是往版本链写，此时允许src与dst在同一页
    if (fetchRowInfo && fetchRowInfo->srcRowInfo.pageHeadPtr.pageHead == &pageHead->baseHead.pageHead) {
        if (isCompressed) {
            // 页面可能被整理过了，需要重新获取一下行头
            fetchRowInfo->srcRowInfo.rowHeadPtr.rowState =
                HeapVarPageGetRowStateImpl(pageHead, fetchRowInfo->srcRowInfo.slot);
        }
        if (!allocRowInfo->sliceRowInfo.isSliceRow && !allocRowInfo->isAllowLinkRowInOnePage) {
            // 什么场景会走入该分支?
            // 1. 更新场景 寻找合适的 link row的新page, 如果reLatch (src page离开了临界区),
            // 新查询 link page空间不够 (fsm 和 page实际大小不一致, 因为二者之间更新顺序没有强一致关系),
            // 再次从fsm寻页, 此时, 由于 src page 曾经离开过临界区, 其他线程操作, 可能空间又足够了
            // 2. 一开始尝试在本页更新时页空间不够，后面尝试转为跳转行时本页又足够了，要再次重试原地更新
            ret = INT_ERR_HEAP_UPD_ALLOC_SAME_PAG;
        }
    }
    if (SECUREC_LIKELY(ret != INT_ERR_HEAP_UPD_ALLOC_SAME_PAG)) {
        ret = HeapVarPageAllocRow(ctx, opInfo);
    }

    /* 把page归还FSM */
    HeapReleasePage(ctx, &heapPageAllocCtx, allocRowInfo->requireSize, pageHead->baseHead.pageHead.freeSize);
    return ret;
}

void HeapAllocRowInfoInit(HpPageAllocRowInfoT *pageAllocRowInfo, HpPageRowTypeE rowType, bool isNewSlot)
{
    DB_POINTER(pageAllocRowInfo);
    HeapAllocRowInfoInitHelper(pageAllocRowInfo, rowType, (PageSizeT)pageAllocRowInfo->bufSize, isNewSlot);
}

StatusInter HeapInsertRowInVarPage(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    if (!HeapIsTupleNeedSlice(ctx, opInfo->allocRowInfo->bufSize)) {
        HeapAllocRowInfoInit(opInfo->allocRowInfo, HEAP_VAR_ROW_NORMAL_ROW, true);
        return HeapAllocVarRow(ctx, opInfo);
    }
    DB_ASSERT(!ctx->heapCfg.isUseRsm);
    StatusInter ret = HeapInsertSliceRows(ctx, opInfo);
    if (ret != STATUS_OK_INTER) {
        HeapClearSliceRow4Failed(ctx, opInfo, false);
    }
    return ret;
}

StatusInter HeapInsertAllocRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    StatusInter ret;
    HpPageAllocRowInfoT *pageAllocRowInfo = opInfo->allocRowInfo;
    if (ctx->hpControl.isFixPage) {
        ret = HeapFixedRowInsert(ctx, opInfo);
    } else {
        ret = HeapInsertRowInVarPage(ctx, opInfo);
    }
    /* 释放 pageSorter 中的锁 */
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    // 缓存本次page的addr, 提高下次访问该page的速度
    ctx->pageCache.lastPageLogicPtr = pageAllocRowInfo->newRowInfo.itemPtr;
    ctx->pageCache.lastPageHeadPtr = pageAllocRowInfo->newRowInfo.pageHeadPtr.pageHead;
    ctx->pageCache.cacheVersion = ctx->runtimeInfo->cacheVersion;
    // lastPageBlockId置为INVALID，防止扫描使用该页addr，主要问题是
    // 此处离开了页锁保护，而且在缩容流程中，扫描和插入的页可能是不一样的BlockId，扫描流程无法利用
    ctx->pageCache.lastPageBlockId = SE_INVALID_BLOCK_ID;
    // 注意: 下面流程都是离开了 page 的 latch;
    // 加行锁 & 记录undo; 如果失败, 需要释放刚刚申请到的 row (因为undo没有记录下来)
    // Normal模式下: HeapAcquireRowLockForInsert (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
    ret = HeapAcquireRowLockFunc(ctx, ctx->hpControl.trxContrl.hpAmIndex, pageAllocRowInfo->newRowInfo.itemPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        // Normal模式下: HeapAcquireRowLockForInsertFailedClear (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
        HeapRecoverFunc(ctx, opInfo);
        return ret;
    }
    if (!ctx->heapCfg.isUseRsm) {
        uint64_t newRollPtr;
        // Normal模式下: HeapUndoLogForInsert (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
        ret = HeapLogUndoFunc(ctx, TRX_OP_INSERT, opInfo, &newRollPtr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            // Normal模式下: HeapAcquireRowLockForInsertFailedClear (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
            HeapRecoverFunc(ctx, opInfo);
            return ret;
        }
        pageAllocRowInfo->isUndoOperationOk = true;
    }
    HeapInsertFreeMemInNormalPro(ctx, opInfo);
    return ret;
}

StatusInter HeapInsertImpl(HeapRunCtxT *ctx, const uint8_t *buf, uint32_t bufSize, HpItemPointerT *itemPtr)
{
    DB_POINTER3(ctx, buf, itemPtr);
    DB_ASSERT(ctx->hpOperation < HEAP_OPTYPE_MODIFY);

    HpPageAllocRowInfoT pageAllocRowInfo = EmptyHpPageAllocRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(NULL, &pageAllocRowInfo);
    StatusInter ret = HeapInitAllocRowInfoImpl(ctx, buf, bufSize, &pageAllocRowInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    HeapSetPageLatchActionForWrite(ctx);
    HeapSetOperationForInsert(ctx);
    // Normal模式下: HeapPrepareTrxInfoForInsert (LITE模式，详细见g_gmdbHeapTrxAmForGroup的赋值)
    uint64_t newRollPtr = UndoGetDefaultRollPtr(ctx->seRunCtx->trx);
    ret = HeapSaveTrxInfoFunc(ctx, (uint32_t)TRX_OP_INSERT, &opInfo, newRollPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    ret = HeapStatInsert(ctx, DEFAULT_OPERATE_ITEM_NUM, bufSize);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    ret = HeapInsertAllocRow(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        if (!pageAllocRowInfo.isUndoOperationOk) {  // undo日志是否记录成功，避免与回滚流程dfx重复统计
            HeapStatInsertFailed(ctx, DEFAULT_OPERATE_ITEM_NUM, bufSize);
        }
        return ret;
    }

    *itemPtr = pageAllocRowInfo.newRowInfo.itemPtr;
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP,
        "Insert label %" PRIu32 " insert row (%" PRIu32 ",%" PRIu32 "), bufSize:%" PRIu32 ", trxId:%" PRIu64,
        ctx->heapCfg.labelId, itemPtr->pageId, itemPtr->slotId, bufSize, ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_ITEMNUM,
        "HeapInsertImpl labelId:%" PRIu32 " insert row (%" PRIu32 ",%" PRIu32 "), phyItemNum:%" PRIu64,
        ctx->heapCfg.labelId, itemPtr->pageId, itemPtr->slotId, ctx->perfStat->phyItemNum);
    return STATUS_OK_INTER;
}

void HeapCopyRowInfoForAllocInSamePage(HeapRowInfoT *newRowInfo, const HeapRowInfoT *samePageRowInfo)
{
    DB_POINTER2(newRowInfo, samePageRowInfo);
    // 只拷贝 page 相关的信息, 其他信息不要覆盖
    newRowInfo->itemPtr = samePageRowInfo->itemPtr;
    newRowInfo->itemBlockId = samePageRowInfo->itemBlockId;
    newRowInfo->pageHeadPtr = samePageRowInfo->pageHeadPtr;
}

StatusInter HeapPreBufForRsmUndoWhenRollback(
    HeapRunCtxT *ctx, HeapRowInfoT *srcRowInfo, uint32_t *tupleLen, ShmemPtrT *tupleBufPos)
{
    // 定长与变长共用此逻辑，拷贝一整行出来
    // 事务活跃时复用trxUndo的buf，回滚时需要rsmUndo自己申请
    PageSizeT bufSize = HeapPageGetMinRowSize(srcRowInfo->rawRowSize, srcRowInfo->rowType);
    ShmemPtrT tmpPtr = DbShmemCtxAlloc(ctx->seRunCtx->rsmCtx, bufSize);
    uint8_t *buf = DbShmPtrToAddr(tmpPtr);
    if (buf == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "HeapPreBuf undo rollback, labelId:%" PRIu32 ", bufSize:%" PRIu32,
            ctx->heapCfg.labelId, bufSize);
        return OUT_OF_MEMORY_INTER;
    }
    errno_t err = memcpy_s(buf, bufSize, srcRowInfo->rowHeadPtr.rowState, bufSize);
    if (err != EOK) {
        SE_LAST_ERROR(
            MEMORY_OPERATE_FAILED_INTER, "cpy, labelId:%" PRIu32 ", bufSize:%" PRIu32, ctx->heapCfg.labelId, bufSize);
        DbShmemCtxFree(ctx->seRunCtx->rsmCtx, tmpPtr);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    *tupleLen = bufSize;
    *tupleBufPos = tmpPtr;
    return STATUS_OK_INTER;
}

StatusInter HeapUpdatePartPreBufForRsmUndo(
    HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, uint16_t rowHeadSize, uint32_t *tupleLen, ShmemPtrT *tupleBufPos)
{
    // 事务活跃时复用trxUndo的buf，回滚时需要rsmUndo自己申请
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    HpPageAllocRowInfoT *allocRowInfo = opInfo->allocRowInfo;

    // 部分更新的undoLog/rsmLog, 统一记录（行头+部分更新的旧内容）
    PageSizeT bufSize = (uint16_t)HEAP_CALC_ALIGN_SIZE(rowHeadSize + allocRowInfo->bufSize);
    ShmemPtrT tmpPtr = DbShmemCtxAlloc(ctx->seRunCtx->rsmCtx, bufSize);
    uint8_t *buf = DbShmPtrToAddr(tmpPtr);
    if (buf == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc buf in rollback, labelId:%" PRIu32 ", bufSize:%" PRIu32,
            ctx->heapCfg.labelId, bufSize);
        return OUT_OF_MEMORY_INTER;
    }
    // 记录normal/src行头内容
    errno_t err = memcpy_s(buf, rowHeadSize, (uint8_t *)fetchRowInfo->srcRowInfo.rowHeadPtr.rowState, rowHeadSize);
    if (err != EOK) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "cpy, labelId:%" PRIu32 ", cpySize:%" PRIu32, ctx->heapCfg.labelId,
            rowHeadSize);
        DbShmemCtxFree(ctx->seRunCtx->rsmCtx, tmpPtr);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    // 记录要被更新掉的部分旧内容
    err = memcpy_s(buf + rowHeadSize, allocRowInfo->bufSize,
        (uint8_t *)fetchRowInfo->buf + allocRowInfo->offsetOfRawData, allocRowInfo->bufSize);
    if (err != EOK) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "cpy, labelId:%" PRIu32 ", cpySize:%" PRIu32, ctx->heapCfg.labelId,
            allocRowInfo->bufSize);
        DbShmemCtxFree(ctx->seRunCtx->rsmCtx, tmpPtr);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    *tupleLen = bufSize;
    *tupleBufPos = tmpPtr;
    return STATUS_OK_INTER;
}

StatusInter HeapUpdateNotExpandRowPrepareRsmUndoBuf(
    HeapRunCtxT *ctx, HeapRowInfoT *srcRowInfo, uint32_t *tupleLen, ShmemPtrT *tupleBufPos, bool *isNeedFreeTuple)
{
    if (((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_ROLLBACK && !ctx->hpControl.isRsmRecovery) {
        StatusInter ret = HeapPreBufForRsmUndoWhenRollback(ctx, srcRowInfo, tupleLen, tupleBufPos);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        *isNeedFreeTuple = true;
    } else {
        // active情况下，rsmUndo才能复用undo的buf
        TrxLiteReUseLastUpdUndoLiteRec((TrxT *)ctx->seRunCtx->trx, tupleLen, tupleBufPos);
    }
    return STATUS_OK_INTER;
}

static StatusInter HeapUpdateNotExpandRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, bool isNeedChangeFreeSize)
{
    DB_POINTER2(ctx, opInfo);
    HeapRowInfoT *srcRowInfo = &opInfo->fetchRowInfo->srcRowInfo;
    HpNormalRowHeadT *rowHead = srcRowInfo->rowHeadPtr.normalRowHead;
    if (!ctx->heapCfg.isUseRsm) {
        HeapVarPageInitNormalRowImpl(ctx, rowHead, opInfo->allocRowInfo);  // 直接复用rowHead, 并更新
        opInfo->hasUpdateRowInfo = true;
        return STATUS_OK_INTER;
    }
    bool isSrcRow = HeapPageIsLinkSrcRow(srcRowInfo->rowHeadPtr.rowState);  // 区分当前行是不是src行
    bool isNeedFreeTuple = false;
    uint32_t tupleLen = 0;
    ShmemPtrT tupleBufPos = DB_INVALID_SHMPTR;
    StatusInter ret =
        HeapUpdateNotExpandRowPrepareRsmUndoBuf(ctx, srcRowInfo, &tupleLen, &tupleBufPos, &isNeedFreeTuple);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    RsmUndoUpdInfoT rsmUndoUpdInfo = {.pageId = srcRowInfo->itemPtr.pageId,
        .slotId = (PageSizeT)srcRowInfo->itemPtr.slotId,
        .isNeedFreeTuple = isNeedFreeTuple,
        .tupleLen = tupleLen,
        .tupleBufPos = tupleBufPos,
        .freeSize = srcRowInfo->pageHeadPtr.pageHead->freeSize};
    HeapRsmUndoLogForUpdate(ctx, &rsmUndoUpdInfo);
    SHM_CRASHPOINT_ONE(SHM_CRASH_VH_UPD_NORMAL_ROW_BEFORE);
    SHM_CRASHPOINT_ONE(SHM_CRASH_VH_UPD_NORMAL_ROW_ROLLBACK_BEFORE);
    HeapVarPageInitNormalRowImpl(ctx, rowHead, opInfo->allocRowInfo);
    SHM_CRASHPOINT_ONE(SHM_CRASH_VH_UPD_NORMAL_ROW_AFTER);
    SHM_CRASHPOINT_ONE(SHM_CRASH_VH_UPD_NORMAL_ROW_ROLLBACK_AFTER);
    if (((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_ROLLBACK && isSrcRow) {
        // normal->src更新，执行回滚的情况，需要保证dst与src的一致性，此处的rsmUndo在HeapVarPageDeleteDstRow中删除
    } else {
        // 活跃事务，外面可能需要修改freeSize，因此不能回退rsmUndo，根据标记位判断
        // 回滚事务一定会修改freeSize，也不能回退rsmUndo
        if (!isNeedChangeFreeSize) {
            HeapRsmUndoLogMovePrevRecord(ctx);
            SHM_CRASHPOINT_ONE(SHM_CRASH_VH_UPD_NORMAL_ROW_FINISH);
        }
    }
    opInfo->hasUpdateRowInfo = true;
    return STATUS_OK_INTER;
}

void HeapUpdateInPageMemmoveRow(
    HpPageRowOpInfoT *opInfo, HVPageHeadT *pageHead, const HpNormalRowHeadT *rowHead, PageTotalSizeT diffSize)
{
    uint8_t *pageRowBeginSrc = (uint8_t *)((uintptr_t)pageHead) + pageHead->freePosEnd;
    // 移动包括本行前面的所有行, 包括本行在内
    PageTotalSizeT moveSize =
        (uint32_t)((uintptr_t)rowHead - (uintptr_t)pageRowBeginSrc) + opInfo->updInPageOldStoreSize;
    uint8_t *pageRowBeginDes = (uint8_t *)((uintptr_t)pageRowBeginSrc - diffSize);
    // 页面上行整体向前移动, 空间一定是满足的
    errno_t err = memmove_s(pageRowBeginDes, moveSize, pageRowBeginSrc, moveSize);
    DB_ASSERT(err == EOK);
    // 刷新被移动的行的slot
    PageSizeT moveRowLogicOffset = PAGE_GET_ROW_LOGIC_OFFSET((uint32_t)((uintptr_t)rowHead - (uintptr_t)pageHead));
    PageSizeT logicDiffOffset = PAGE_GET_ROW_LOGIC_OFFSET(diffSize);
    for (uint32_t i = 0; i < pageHead->slotCnt; ++i) {
        PageSizeT slotOffset = 0;
        HpRowSlotT *slot = NULL;
        if (HeapVarPageGetSlotBySlotIdImpl(pageHead, i, &slotOffset, &slot) == STATUS_OK_INTER) {
            if (slot->offset <= moveRowLogicOffset) {
                slot->offset = (uint16_t)((uint32_t)(slot->offset - logicDiffOffset) & ((1 << PAGE_SIZE_T_OFFSET) - 1));
            }
        }
    }
    pageHead->freePosEnd -= diffSize;
}

StatusInter HeapUpdateInPagePrepare(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, HVPageHeadT *pageHead,
    const HpNormalRowHeadT *rowHead, PageTotalSizeT diffSize)
{
    if (!ctx->heapCfg.isUseRsm) {
        HeapUpdateInPageMemmoveRow(opInfo, pageHead, rowHead, diffSize);
        return STATUS_OK_INTER;
    }
    uint32_t pageSize = ctx->heap->fsm.cfgInfo.pageSize;
    uint8_t *bufBeginPos = ((uint8_t *)pageHead);  // freeSize在开头，整个页memcpy
    StatusInter ret = HeapRsmUndoLogForMemMoveRow(
        ctx, pageHead->baseHead.pageHead.addr, RSM_UNDO_VAR_HEAP_MEMOVE_PAGE, pageSize, bufBeginPos);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "alloc rsmUndo in page update, labelId:%" PRIu32, ctx->heapCfg.labelId);
        return ret;
    }
    SHM_CRASHPOINT(SHM_CRASH_VH_MEMMOVE_ROW_BEFORE);
    HeapUpdateInPageMemmoveRow(opInfo, pageHead, rowHead, diffSize);
    SHM_CRASHPOINT(SHM_CRASH_VH_MEMMOVE_ROW_AFTER);
    HeapRsmUndoLogMovePrevRecord(ctx);
    return STATUS_OK_INTER;
}

// 搬迁部分旧行，原地更新row
StatusInter HeapUpdateInPage(
    HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, const HpNormalRowHeadT *rowHead, PageTotalSizeT diffSize)
{
    HeapRowInfoT *srcRowInfo = &opInfo->fetchRowInfo->srcRowInfo;
    HVPageHeadT *pageHead = srcRowInfo->pageHeadPtr.varPageHead;
    if (HeapVarPageGetContinueFreeSize(pageHead) < diffSize) {
        StatusInter ret = HeapVarPageCompressRow(ctx, pageHead);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        DB_ASSERT((uint32_t)HeapVarPageGetContinueFreeSize(pageHead) >= diffSize);
    }

    StatusInter ret = HeapUpdateInPagePrepare(ctx, opInfo, pageHead, rowHead, diffSize);
    if (ret != STATUS_OK_INTER) {
        // 唯一失败场景是rsmUndo申请内存失败，此时还未搬迁，因此no need重新获取行头
        return ret;
    }

    // 重新获取行头, 格式化新行
    srcRowInfo->rowHeadPtr.rowState = HeapVarPageGetRowStateImpl(pageHead, srcRowInfo->slot);
    // 整体往前移动，腾出了空间，使得原来的空间+腾出的空间足够, 直接原地更新
    // 因为是由小到大更新，需要修改freeSize
    HeapUpdateNotExpandRow(ctx, opInfo, true);
    pageHead->baseHead.pageHead.freeSize = (uint16_t)(pageHead->baseHead.pageHead.freeSize - diffSize);
    DB_ASSERT(pageHead->baseHead.pageHead.freeSize <= ctx->fsmMgr->cfgInfo.availSize);
    if (ctx->heapCfg.isUseRsm) {
        HeapRsmUndoLogMovePrevRecord(ctx);
        SHM_CRASHPOINT_ONE(SHM_CRASH_VH_UPD_NORMAL_ROW_FINISH);
    }
    opInfo->hasUpdateRowInfo = true;

    return STATUS_OK_INTER;
}

StatusInter HeapUpdateLinkSrcAndDstRowInCurPage(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    HpPageAllocRowInfoT *pageAllocRowInfo = opInfo->allocRowInfo;
    HeapRowInfoT *srcRowInfo = &opInfo->fetchRowInfo->srcRowInfo;
    HVPageHeadT *pageHead = srcRowInfo->pageHeadPtr.varPageHead;
    HpLinkRowHeadT *linkRowHead = srcRowInfo->rowHeadPtr.linkRowHead;
    // 默认情况下, 优先原地覆盖
    HeapAllocRowInfoInit(pageAllocRowInfo, HEAP_VAR_ROW_NORMAL_ROW, false);
    if (SECUREC_LIKELY(opInfo->updInPageOldStoreSize >= pageAllocRowInfo->storeSize)) {
        // 原来的空间足够, 直接原地更新
        // 活跃事务，传false
        // (1) 更新前后大小相同，no need修改freeSize
        // (2) 由大到小更新，需要保证回滚时空间足够，也no need修改freeSize
        // 回滚事务：外面HeapVarRowClearReserveSize都会刷新freeSize，因此传true
        return HeapUpdateNotExpandRow(ctx, opInfo, ((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_ROLLBACK);
    }
    // 回滚流程理应整行都够，不应该走到这里
    DB_ASSERT(((TrxT *)ctx->seRunCtx->trx)->base.state != TRX_STATE_ROLLBACK);
    bool isCompressed = false;
    // 其次, 再优先迁回 : link -> normal row
    bool isSizeEnough = false;
    StatusInter ret =
        HeapVarPageConfirmSize(ctx, pageHead, pageAllocRowInfo->requireSize, &isCompressed, &isSizeEnough);
    DB_UNUSED(ret);  // 页面整理即使失败也没关系，外面会尝试跳转行更新
    if (isCompressed) {
        // 如果page压缩, 则row会发生偏移, 重新获取 rowHead
        srcRowInfo->rowHeadPtr.rowState = HeapVarPageGetRowStateImpl(pageHead, srcRowInfo->slot);
        linkRowHead = srcRowInfo->rowHeadPtr.linkRowHead;
        DB_ASSERT(linkRowHead->magicNum == HEAP_ROW_MAGIC_NUM);
    }
    if (isSizeEnough) {
        HeapCopyRowInfoForAllocInSamePage(&pageAllocRowInfo->newRowInfo, srcRowInfo);  // 只需要拷贝 page相关信息
        // HeapUpdateInPage里面申请内存，可能会失败，直接return错误码，外面会尝试跳转行更新
        return HeapUpdateInPage(ctx, opInfo, (HpNormalRowHeadT *)(void *)linkRowHead,
            pageAllocRowInfo->storeSize - opInfo->updInPageOldStoreSize);
    }
    // 不能本页更新完毕就报错，外面会尝试跳转行更新
    return MEMORY_OPERATE_FAILED_INTER;
}

StatusInter HeapUpdateLinkSrcAndDstRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    HpPageAllocRowInfoT *pageAllocRowInfo = opInfo->allocRowInfo;
    HeapRowInfoT *srcRowInfo = &opInfo->fetchRowInfo->srcRowInfo;
    HVPageHeadT *pageHead = srcRowInfo->pageHeadPtr.varPageHead;
    HpLinkRowHeadT *linkRowHead = srcRowInfo->rowHeadPtr.linkRowHead;

    pageAllocRowInfo->newRowInfo.rollbackReserveSize = linkRowHead->rollbackReserveSize;
    if (ctx->hpControl.isRsmRecovery && ((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_ACTIVE) {
        // 恢复阶段的正常事务只允许normal->normal, src->src(实现了dst原地更新)
        return HeapUpdateDstRowWhenRsmRecovery(ctx, opInfo);
    }
    uint32_t retryTimes = 0;
    while (retryTimes < RETRY_TIMES_ALLOWED_FOR_CUR_PAGE) {
        StatusInter ret = HeapUpdateLinkSrcAndDstRowInCurPage(ctx, opInfo);
        if (SECUREC_LIKELY(ret == STATUS_OK_INTER)) {  // 本页更新成功就返回
            return STATUS_OK_INTER;
        }
        if (SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX) {
            // 乐观事务，在HeapUpdate已经提前申请好了dstRow，不会走到此处
            // 在release模式下，如果此处不报错返回，会有并发问题发生(见HeapUpdateNormalRow上面的注释)，应该防止故障扩散
            SE_LAST_ERROR(
                INTERNAL_ERROR_INTER, "freeSize: %" PRIu32 " less allocSize", pageHead->baseHead.pageHead.freeSize);
            DB_ASSERT(false);
            return INTERNAL_ERROR_INTER;
        }
        linkRowHead = srcRowInfo->rowHeadPtr.linkRowHead;  // 内部做了页整理，需要刷新一下
        DB_ASSERT(linkRowHead->magicNum == HEAP_ROW_MAGIC_NUM);

        /* linkSrcRow 所在的 page 空间不够, 依然需要 src + dst; */
        HeapAllocRowInfoInit(pageAllocRowInfo, HEAP_VAR_ROW_LINK_DST_ROW, true);
        pageAllocRowInfo->sliceRowInfo.linkSrcRowPtr = srcRowInfo->itemPtr;
        ret = HeapAllocVarRow(ctx, opInfo);  // 跨页申请
        if (SePgLatchSorterIsPageReLatch(&ctx->pageSorter, srcRowInfo->itemPtr.pageId)) {
            srcRowInfo->rowHeadPtr.rowState = HeapVarPageGetRowStateImpl(pageHead, srcRowInfo->slot);
            linkRowHead = srcRowInfo->rowHeadPtr.linkRowHead;
            SePgLatchSorterClearPageReLatchState(&ctx->pageSorter, srcRowInfo->itemPtr.pageId);
        }

        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            if (ret == INT_ERR_HEAP_UPD_ALLOC_SAME_PAG) {
                retryTimes++;
                continue;  // 由于跨页申请流程中, 可能reLatch, 申请到相同的page, 重试, 优先本页更新
            }
            SE_ERROR(ret, "alloc row in other page");
            return ret;
        }
        DB_ASSERT(linkRowHead->magicNum == HEAP_ROW_MAGIC_NUM);
        // 更新旧page上的信息，只有linkSrc变更，pageHead的freeSize不变，保留行预留空间
        HeapUpdateVarRowToLinkSrcSetInfo(ctx, opInfo, true);
        return STATUS_OK_INTER;
    }
    SE_ERROR(OUT_OF_MEMORY_INTER,
        "update link src, trxId:%" PRIu64 ", label:%" PRIu32 ", row:(%" PRIu32 ", %" PRIu32 ")",
        ((TrxT *)ctx->seRunCtx->trx)->base.trxId, ctx->heapCfg.labelId, srcRowInfo->itemPtr.pageId,
        srcRowInfo->itemPtr.slotId);
    return OUT_OF_MEMORY_INTER;
}

StatusInter HeapUpdateNormalRowInCurPage(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    HpPageAllocRowInfoT *pageAllocRowInfo = opInfo->allocRowInfo;
    HeapRowInfoT *srcRowInfo = &opInfo->fetchRowInfo->srcRowInfo;
    HVPageHeadT *pageHead = srcRowInfo->pageHeadPtr.varPageHead;
    HpNormalRowHeadT *rowHead = srcRowInfo->rowHeadPtr.normalRowHead;
    // 默认情况下, normal row 优先 原地覆盖
    HeapAllocRowInfoInit(pageAllocRowInfo, HEAP_VAR_ROW_NORMAL_ROW, false);
    if (SECUREC_LIKELY(opInfo->updInPageOldStoreSize >= pageAllocRowInfo->storeSize)) {
        // 原来的空间足够, 直接原地更新
        // 活跃事务：
        // (1) 更新前后大小相同，no need修改freeSize，传false
        // (2) 由大到小更新，需要保证回滚时空间足够，也no need修改freeSize，传false
        // (3) 由小到大更新，走下面HeapUpdateInPage的逻辑，此分支不考虑
        // 回滚事务：外面HeapVarRowClearReserveSize都会刷新freeSize，因此传true
        return HeapUpdateNotExpandRow(ctx, opInfo, ((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_ROLLBACK);
    }
    // 回滚流程理应整行都够，不应该走到这里
    DB_ASSERT(((TrxT *)ctx->seRunCtx->trx)->base.state != TRX_STATE_ROLLBACK);

    bool isCompressed = false;
    // row空间不足, 检查本页的空间是否满足更新
    bool isSizeEnough = false;
    StatusInter ret =
        HeapVarPageConfirmSize(ctx, pageHead, pageAllocRowInfo->requireSize, &isCompressed, &isSizeEnough);
    DB_UNUSED(ret);  // 页面整理即使失败也没关系，外面会尝试跳转行更新
    if (SECUREC_UNLIKELY(isCompressed)) {
        // 如果page压缩, 则row会发生偏移, 重新获取 rowHead
        srcRowInfo->rowHeadPtr.rowState = HeapVarPageGetRowStateImpl(pageHead, srcRowInfo->slot);
        rowHead = srcRowInfo->rowHeadPtr.normalRowHead;
    }
    if (SECUREC_LIKELY(isSizeEnough)) {  // 在页内更新
        HeapAllocRowInfoInit(pageAllocRowInfo, HEAP_VAR_ROW_NORMAL_ROW, false);
        HeapCopyRowInfoForAllocInSamePage(&pageAllocRowInfo->newRowInfo, srcRowInfo);  // 只需要拷贝 page相关信息
        return HeapUpdateInPage(ctx, opInfo, rowHead, pageAllocRowInfo->storeSize - opInfo->updInPageOldStoreSize);
    }
    // 不能本页更新完毕就报错，外面会尝试跳转行更新
    return MEMORY_OPERATE_FAILED_INTER;
}

void HeapUpdateFlashRowHead(
    HeapRunCtxT *ctx, HeapRowInfoT *srcRowInfo, HVPageHeadT *pageHead, HpNormalRowHeadT **rowHead)
{
    // 如果跨page申请新row, 重新加解锁了, 重新获取 rowHead
    bool isReLatchSrcRow = SePgLatchSorterIsPageReLatch(&ctx->pageSorter, srcRowInfo->itemPtr.pageId);
    if (isReLatchSrcRow) {
        srcRowInfo->rowHeadPtr.rowState = HeapVarPageGetRowStateImpl(pageHead, srcRowInfo->slot);
        *rowHead = srcRowInfo->rowHeadPtr.normalRowHead;
        SePgLatchSorterClearPageReLatchState(&ctx->pageSorter, srcRowInfo->itemPtr.pageId);
    }
}

/*
乐观事务的更新，为啥需要在记录undo前申请出跳转行/大对象行？
考虑以下场景：
1. 假设trx1来进行更新，此时已经记录undo了，但还没刷新masterVersion

                            trx1写的undo
-------------------     ------------------
| masterVersion(v0)|--->|   undoLog1(v0)  |
-------------------     -------------------
    trx1的修改             trx0插入的版本

2. 更新masterVersion时，发现当前位置不够，且页面整理后也不够，触发normal-->linkRow的逻辑，申请跳转行，触发了relatch，
释放掉了本页的latch锁。此时发生并发，trx2也来修改这一行，抢先加上页latch，并记录了undo：

                            trx2写的undo          trx1写的undo
-------------------     ------------------     ------------------
| masterVersion(v2)|--->|   undoLog2(v0)  |--->|   undoLog1(v0)  |
-------------------     -------------------    -------------------
    trx2的修改             trx0插入的版本         trx0插入的版本

3. trx2更新完后就释放页latch了，此时trx1再次拿到本页的latch锁，此时trx1不知道trx2的存在，会直接刷新masterVersion，
覆盖掉trx2的v2版本

                            trx2写的undo          trx1写的undo
-------------------     ------------------     ------------------
| masterVersion(v1)|--->|   undoLog2(v0)  |--->|   undoLog1(v0)  |
-------------------     -------------------    -------------------
    trx1的修改             trx0插入的版本         trx0插入的版本

4. 这样就造成了trx2的v2版本数据丢失，因此需要在记录undo前申请出跳转行/大对象行，避免relatch的发生。
同时对pageHead上的freeSize字段的维护也非常重要，决定了是否提前申请出跳转行（大对象是一定要提前申请的）
*/
StatusInter HeapUpdateNormalRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    HpPageAllocRowInfoT *pageAllocRowInfo = opInfo->allocRowInfo;
    HeapRowInfoT *srcRowInfo = &opInfo->fetchRowInfo->srcRowInfo;
    HVPageHeadT *pageHead = srcRowInfo->pageHeadPtr.varPageHead;
    HpNormalRowHeadT *rowHead = srcRowInfo->rowHeadPtr.normalRowHead;
    pageAllocRowInfo->newRowInfo.rollbackReserveSize = rowHead->rollbackReserveSize;
    uint32_t retryTimes = 0;
    while (retryTimes < RETRY_TIMES_ALLOWED_FOR_CUR_PAGE) {
        StatusInter ret = HeapUpdateNormalRowInCurPage(ctx, opInfo);
        if (SECUREC_LIKELY(ret == STATUS_OK_INTER)) {  // 本页更新成功就返回
            return STATUS_OK_INTER;
        } else if (((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_ROLLBACK) {
            // 回滚场景，保留内存可能会申请rsmUndo导致失败，其他场景回滚都不应该申请新dst(新页)
            DB_ASSERT(ctx->heapCfg.isUseRsm);
            return ret;
        }
        if (SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX) {
            // 乐观事务，在HeapUpdate已经提前申请好了dstRow，不会走到此处
            // 在release模式下，如果此处不报错返回，会有并发问题发生(见HeapUpdateNormalRow上面的注释)，应该防止故障扩散
            SE_LAST_ERROR(
                INTERNAL_ERROR_INTER, "freeSize: %" PRIu32 " less allocSize", pageHead->baseHead.pageHead.freeSize);
            DB_ASSERT(false);
            return INTERNAL_ERROR_INTER;
        }
        DB_ASSERT(!ctx->hpControl.isRsmRecovery);  // 恢复阶段只允许normal->normal, src->src(实现了dst原地更新)
        rowHead = srcRowInfo->rowHeadPtr.normalRowHead;  // 内部做了页整理，需要刷新一下
        DB_ASSERT(rowHead->magicNum == HEAP_ROW_MAGIC_NUM);

        // 本page空间不够, 需要从 normal row扩展为 src + dst;
        HeapAllocRowInfoInit(pageAllocRowInfo, HEAP_VAR_ROW_LINK_DST_ROW, true);
        pageAllocRowInfo->sliceRowInfo.linkSrcRowPtr = srcRowInfo->itemPtr;
        ret = HeapAllocVarRow(ctx, opInfo);  // 跨页申请

        HeapUpdateFlashRowHead(ctx, srcRowInfo, pageHead, &rowHead);
        if (SECUREC_UNLIKELY(ret == INT_ERR_HEAP_UPD_ALLOC_SAME_PAG)) {
            retryTimes++;
            continue;  // 由于跨页申请流程中, 可能reLatch, 申请到相同的page, 重试, 优先本页更新
        } else if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "alloc row in other page");
            return ret;
        }
        DB_ASSERT(rowHead->magicNum == HEAP_ROW_MAGIC_NUM);
        (void)DbAtomicAdd64(&ctx->perfStat->historyLinkRows, (uint64_t)1);
        // 更新旧page上的信息: normal row -> linkSrc，pageHead的freeSize不变，保留行预留空间
        HeapUpdateVarRowToLinkSrcSetInfo(ctx, opInfo, true);
        return STATUS_OK_INTER;
    }
    SE_ERROR(OUT_OF_MEMORY_INTER,
        "update normal row, trxId:%" PRIu64 ", label:%" PRIu32 ", row:(%" PRIu32 ", %" PRIu32 ")",
        ((TrxT *)ctx->seRunCtx->trx)->base.trxId, ctx->heapCfg.labelId, srcRowInfo->itemPtr.pageId,
        srcRowInfo->itemPtr.slotId);
    return OUT_OF_MEMORY_INTER;
}

// 用于回滚以及提交时行预留空间的清理，包含3个作用：
// 1.更新rollbackReserveSize字段为存储的实际长度：normal行更新为bufSize，linkSrc行更新为0
// 2.若有差值，刷新到pageHead的freeSize
// 3.若页上的freeSize有变化，更新到lfs
void HeapVarRowClearReserveSize(
    HeapRunCtxT *ctx, HpRowHeadPtrT rowHeadPtr, HVPageHeadT *pageHead, uint16_t oldFreeSize, bool isNeedMoveRsmUndo)
{
    DB_POINTER2(ctx, pageHead);
    PageSizeT rowReserveSize = HeapPageGetRowReserveSize(rowHeadPtr);
    if (HeapPageIsNormalRow(rowHeadPtr.rowState)) {
        if (rowReserveSize > HeapPageGetMinRowSize(rowHeadPtr.normalRowHead->size, HEAP_VAR_ROW_NORMAL_ROW)) {
            pageHead->baseHead.pageHead.freeSize =
                (uint16_t)(oldFreeSize + rowReserveSize -
                           HeapPageGetMinRowSize(rowHeadPtr.normalRowHead->size, HEAP_VAR_ROW_NORMAL_ROW));
        }
        rowHeadPtr.normalRowHead->rollbackReserveSize = (PageSizeT)HEAP_CALC_ALIGN_SIZE(rowHeadPtr.normalRowHead->size);
        DB_ASSERT(rowHeadPtr.normalRowHead->rollbackReserveSize <= ctx->heapCfg.maxRowRawSizeInPage);
    } else if (HeapPageIsLinkSrcRow(rowHeadPtr.rowState)) {
        if (rowHeadPtr.linkRowHead->rollbackReserveSize > 0) {
            pageHead->baseHead.pageHead.freeSize =
                (uint16_t)(oldFreeSize + rowReserveSize - HeapPageGetLinkSrcRowSize());
        }
        rowHeadPtr.linkRowHead->rollbackReserveSize = 0;
    }
    if (ctx->heapCfg.isUseRsm && isNeedMoveRsmUndo) {
        HeapRsmUndoLogMovePrevRecord(ctx);
    }
    if (oldFreeSize != pageHead->baseHead.pageHead.freeSize) {
        // freeSize产生变动后，需要刷新在页面整理时需要搬迁的数据行在页内的offset上界标记位，同时更新lfs
        HeapPageUpdatePageStat(pageHead, rowHeadPtr.rowState);
        LfsOpInfoT lfsOpInfo = {.mgr = ctx->fsmMgr,
            .md = ctx->seRunCtx->pageMgr,
            .rsmUndoRec = ((TrxT *)(ctx->seRunCtx->trx))->liteTrx.rsmUndoRec,
            .useCachePage = !ctx->seRunCtx->resSessionCtx.isDirectWrite,
            .needReleasePage = false};
        StatusInter ret = LfsSetBlockFreeSpace(
            &lfsOpInfo, (uint8_t *)pageHead, pageHead->baseHead.pageHead.freeSize, FSM_INVALID_MAX_ROW_SIZE, false);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "set block freeSpace, blockId %" PRIu32, pageHead->baseHead.slotIdx);
        }
    }
}

inline static void HeapTryLfsSetBlockFreeSpace(const HeapRunCtxT *ctx, PageHeadT *pageBaseHead,
    const HVPageHeadT *pageHead, const HpPageAllocRowInfoT *pageAllocRowInfo)
{
    LfsOpInfoT lfsOpInfo = {.mgr = ctx->fsmMgr,
        .md = ctx->seRunCtx->pageMgr,
        .rsmUndoRec = ((TrxT *)(ctx->seRunCtx->trx))->liteTrx.rsmUndoRec,
        .useCachePage = !ctx->seRunCtx->resSessionCtx.isDirectWrite,
        .needReleasePage = false};
    StatusInter tmpRet = LfsSetBlockFreeSpace(
        &lfsOpInfo, (uint8_t *)pageBaseHead, pageHead->baseHead.pageHead.freeSize, pageAllocRowInfo->bufSize, false);
    if (SECUREC_UNLIKELY(tmpRet != STATUS_OK_INTER)) {
        SE_ERROR(tmpRet, "set block freeSpace, blockId %" PRIu32, *(uint32_t *)(pageBaseHead + 1));
    }
}

StatusInter HeapUpdateVarRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    TrxT *trx = (TrxT *)ctx->seRunCtx->trx;
    HpPageAllocRowInfoT *pageAllocRowInfo = opInfo->allocRowInfo;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    HeapRowInfoT *srcRowInfo = &fetchRowInfo->srcRowInfo;
    HVPageHeadT *pageHead = srcRowInfo->pageHeadPtr.varPageHead;
    PageHeadT *pageBaseHead = srcRowInfo->pageHeadPtr.pageHead;
    uint16_t oldFreeSize = pageHead->baseHead.pageHead.freeSize;
    HeapPageUpdatePageStat(pageHead, srcRowInfo->rowHeadPtr.rowState);
    StatusInter ret;
#ifndef NDEBUG
    if (SeTransGetTrxType(ctx->seRunCtx) == PESSIMISTIC_TRX) {
        // 乐观下，可能有多个事务同时操作同一行，因此此处可能会触发isDeleted不为true的断言
        DB_ASSERT(!srcRowInfo->rowHeadPtr.rowState->isDeleted);
    }
#endif
    if (SECUREC_UNLIKELY(HeapUpdateIsNewTupleNeedSlice(ctx, opInfo))) {
        // 乐观的正常流程，已经在外面申请好了分片行，不会走到这个逻辑
        // 乐观的回滚流程，已经判断了undoLog里面是normal行才会走进本函数，因此也不会走这个逻辑
        DB_ASSERT(SeTransGetTrxType(ctx->seRunCtx) == PESSIMISTIC_TRX);
        if (SECUREC_UNLIKELY((ret = HeapUpdateSliceRows(ctx, opInfo)) != STATUS_OK_INTER)) {
            HeapClearSliceRow4Failed(ctx, opInfo, true);
        }
        return ret;
    }
    if (SECUREC_LIKELY(HeapPageIsNormalRow(srcRowInfo->rowHeadPtr.rowState))) {
        ret = HeapUpdateNormalRow(ctx, opInfo);
    } else if (HeapPageIsLinkSrcRow(srcRowInfo->rowHeadPtr.rowState)) {
        ret = HeapUpdateLinkSrcAndDstRow(ctx, opInfo);
    } else {
        ret = INT_ERR_HEAP_UNEXPECT_ERROR;  // 内部非预期错误
        SE_LAST_ERROR(ret, "inv row type:%" PRId32, (int32_t)srcRowInfo->rowType);
    }
    // 1. 回滚流程：变长表normal行在masterVersion回滚已提交版本成功，还原行占用空间为真实大小，同时涵盖乐观和悲观的情况
    // 2. 正常更新流程：页上的freeSize有变化，再更新 lfs，否则不用更新
    if (trx->base.state == TRX_STATE_ROLLBACK) {
        bool isOldRowCommitted = false;
        StatusInter res = TrxMgrCheckTrxIsCommitted(trx->trxMgr, ctx->rollBackInfo.oldTrxId, &isOldRowCommitted);
        if (SECUREC_UNLIKELY(res != STATUS_OK_INTER)) {
            return res;
        }
        if (ret == STATUS_OK_INTER && isOldRowCommitted) {
            // 此函数中已经包括lfs更新
            // 回滚流程最后一步设置freeSize，设置完成后，需要看是否还有dst行需要删除
            HeapVarRowClearReserveSize(
                ctx, srcRowInfo->rowHeadPtr, pageHead, oldFreeSize, !fetchRowInfo->is2LinkDstRow);
            SHM_CRASHPOINT_ONE(SHM_CRASH_VH_UPD_NORMAL_ROW_ROLLBACK_FINISH);
        }
    } else if (SECUREC_UNLIKELY(oldFreeSize != pageHead->baseHead.pageHead.freeSize)) {
        // 这页不是从fsm取的，所以要使用NoRelink模式
        HeapTryLfsSetBlockFreeSpace(ctx, pageBaseHead, pageHead, pageAllocRowInfo);
    }
    return ret;
}

inline static void HeapDeleteDstRowIfRollBack(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    if (HeapUpdateIsRollbackLinkRow(ctx, opInfo)) {
        bool isMasterVersionMarkDeleted = opInfo->fetchRowInfo->srcRowInfo.isMarkDeleted;
        if (isMasterVersionMarkDeleted) {
            // masterVersion是删除的状态时，它的内存可能是已回滚事务的内存，此时不应该去访问dstRow
            return;
        }
        bool isNoSlice = !HeapUpdateIsNewTupleNeedSlice(ctx, opInfo);
        DB_ASSERT(isNoSlice);  // 上层回滚流程校验了oldRowIsNormalRow，大对象不会走进该流程
        HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
        StatusInter ret = HeapDeleteLinkDstRowImpl(ctx, &fetchRowInfo->dstRowInfo, fetchRowInfo->isSliceRows, false);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "label %" PRIu32 " del row, trxId %" PRIu64, ctx->heapCfg.labelId,
                ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
            DB_ASSERT(false);  // 删除流程不应该有失败, 出现应该定位;
        }
    }
}

void HeapUpdateSetRowOpInfo(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, UndoRowOpInfoT *rowOpInfo)
{
    DB_POINTER3(ctx, opInfo, rowOpInfo);
    HeapRowInfoT *srcRowInfo = &opInfo->fetchRowInfo->srcRowInfo;
    HpTrxPrevVersionRowInfoT *prevVersionRowInfo = &opInfo->fetchRowInfo->prevVersionRowInfo;
    rowOpInfo->isRetained = true;
    rowOpInfo->isSkipUndoIndex = prevVersionRowInfo->isSkipUndoIndex;
    rowOpInfo->resType = TRX_RES_HEAP;
    rowOpInfo->labelType = HeapGetLabelTypeByTupleType(ctx->heapCfg.tupleType);
    rowOpInfo->opType = prevVersionRowInfo->opType;
    rowOpInfo->labelId = ctx->heapCfg.labelId;
    rowOpInfo->rowTrxId = ((TrxT *)(ctx->seRunCtx->trx))->base.trxId;
    rowOpInfo->rowRollPtr = prevVersionRowInfo->undoRollPtr;
    rowOpInfo->rowId = *(uint64_t *)(void *)&srcRowInfo->itemPtr;
    rowOpInfo->savePointId = prevVersionRowInfo->savePointId;
    rowOpInfo->bufSize = (uint16_t)opInfo->allocRowInfo->bufSize;
    rowOpInfo->isDeleted = false;
}

StatusInter HeapUpdateVarRowWithOptimisticTrx(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    HeapRowInfoT *srcRowInfo = &opInfo->fetchRowInfo->srcRowInfo;
    HpTrxPrevVersionRowInfoT *prevVersionRowInfo = &opInfo->fetchRowInfo->prevVersionRowInfo;
    if (opInfo->allocRowInfo->undoBypassType != UNDO_OP_NORMAL && !prevVersionRowInfo->isOnMasterVersion) {
        // 更新到版本链上，肯定是大对象或者DstRow的情况
        DB_ASSERT(opInfo->allocRowInfo->sliceRowInfo.isSliceRow || opInfo->allocRowInfo->isNewSlot);
        uint64_t masterRollPtr = (srcRowInfo->rowType == HEAP_VAR_ROW_NORMAL_ROW) ?
                                     srcRowInfo->rowHeadPtr.normalRowHead->trxInfo.rollPtr :
                                     srcRowInfo->rowHeadPtr.linkRowHead->srcTrxInfo.rollPtr;
        UndoRowOpInfoT rowOpInfo = {0};
        HeapUpdateSetRowOpInfo(ctx, opInfo, &rowOpInfo);
        // undo 日志, 只记录首行的信息. 不记录跳转dts, 分片行的内容.
        HpLinkRowHeadT linkRowHead = {0};
        linkRowHead.rowState.isLinkSrc = true;
        linkRowHead.rollbackReserveSize = 0;  // 该数值只在masterVersion改动时有效，此处赋0即可
        DB_ASSERT(opInfo->allocRowInfo->newTrxInfo.rollPtr == prevVersionRowInfo->undoRollPtr);
        HeapVarPageInitLinkSrcRowImpl(ctx, &linkRowHead, opInfo->allocRowInfo, true);
        rowOpInfo.rowSize = HeapPageGetMinRowSize(0, HEAP_VAR_ROW_LINK_SRC_ROW);
        rowOpInfo.rowBuf = (uint8_t *)&linkRowHead;
        return UndoWriteRecInVersionChain(
            ctx->seRunCtx->undoCtx, masterRollPtr, &rowOpInfo, prevVersionRowInfo->undoRollPtr, false);
    } else {
        // 以下2种情况会走到该分支：
        // 1. 不能合并
        // 2. 能合并，并且在masterVersion上
        if (opInfo->allocRowInfo->sliceRowInfo.isSliceRow || opInfo->allocRowInfo->isNewSlot) {
            HeapVarPageInitLinkSrcRowImpl(
                ctx, opInfo->fetchRowInfo->srcRowInfo.rowHeadPtr.linkRowHead, opInfo->allocRowInfo, true);
            return STATUS_OK_INTER;
        } else {  // 普通行
            return HeapUpdateVarRow(ctx, opInfo);
        }
    }
}

StatusInter HeapUpdateRowImpl(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    StatusInter ret;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    opInfo->updInPageOldStoreSize =
        HeapPageGetMinRowSize(fetchRowInfo->srcRowInfo.rollbackReserveSize, fetchRowInfo->srcRowInfo.rowType);
    if (ctx->heapCfg.pageType == HEAP_FIX_LEN_ROW_PAGE) {
        ret = HeapFixedRowUpdate(ctx, opInfo);
    } else {
        if ((SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX) &&
            ((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_ACTIVE) {
            // 外面可能已经申请了跳转行/分片行信息来避免relatch，且不一定是往masterVersion上更新，单独写流程
            ret = HeapUpdateVarRowWithOptimisticTrx(ctx, opInfo);
        } else {
            ret = HeapUpdateVarRow(ctx, opInfo);
        }
    }
    if (ret != OUT_OF_MEMORY_INTER) {
        // 保留内存申请rsmUndo可能会失败，返回出去切换逃生内存
        // 当场切换会持有页锁去等待 控制逃生内存串行使用的锁，造成死锁, 等切换完毕后第二次进入再删除dst行
        HeapDeleteDstRowIfRollBack(ctx, opInfo);
    }
    return ret;
}

StatusInter HeapFixPageUpdatePartialRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    HpPageAllocRowInfoT *allocRowInfo = opInfo->allocRowInfo;
    HeapRowInfoT *srcRowInfo = &fetchRowInfo->srcRowInfo;
    HeapRowInfoT *dstRowInfo = &fetchRowInfo->dstRowInfo;
    errno_t err;
    if (HeapPageIsNormalRow(srcRowInfo->rowHeadPtr.rowState)) {
        HpNormalFixRowHead *rowHead = srcRowInfo->rowHeadPtr.normalFixRowHead;
        rowHead->trxInfo = allocRowInfo->newTrxInfo;
        err = memcpy_s(((uint8_t *)(rowHead + 1) + allocRowInfo->offsetOfRawData), allocRowInfo->bufSize,
            allocRowInfo->buf, allocRowInfo->bufSize);
    } else if (fetchRowInfo->is2LinkDstRow && !fetchRowInfo->isSliceRows) {
        DB_ASSERT(srcRowInfo->rowHeadPtr.rowState->isLinkSrc);
        DB_ASSERT(dstRowInfo->rowHeadPtr.rowState->isLinkDst);
        srcRowInfo->rowHeadPtr.linkSrcFixRowHead->trxInfo = allocRowInfo->newTrxInfo;
        HpLinkDstFixRowHead *rowHead = dstRowInfo->rowHeadPtr.linkDstFixRowHead;
        rowHead->trxId = allocRowInfo->newTrxInfo.trxId;
        err = memcpy_s(((uint8_t *)(rowHead + 1) + allocRowInfo->offsetOfRawData), allocRowInfo->bufSize,
            allocRowInfo->buf, allocRowInfo->bufSize);
    } else {
        // 内部非预期错误
        SE_LAST_ERROR(INT_ERR_HEAP_UNEXPECT_ERROR,
            "inv row type%" PRId32 ". labelId:%" PRIu32 ", srcPageId:%" PRIu32 ", srcSlotId:%" PRIu32,
            (int32_t)srcRowInfo->rowType, ctx->heapCfg.labelId, srcRowInfo->itemPtr.pageId, srcRowInfo->itemPtr.slotId);
        return INT_ERR_HEAP_UNEXPECT_ERROR;
    }
    if (err != EOK) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER,
            "Partial Update. labelId:%" PRIu32 ", dstPageId:%" PRIu32 ", dstSlotId:%" PRIu32, ctx->heapCfg.labelId,
            dstRowInfo->itemPtr.pageId, dstRowInfo->itemPtr.slotId);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    opInfo->hasUpdateRowInfo = true;
    return STATUS_OK_INTER;
}

StatusInter HeapVarPageUpdatePartialRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    HpPageAllocRowInfoT *allocRowInfo = opInfo->allocRowInfo;
    HeapRowInfoT *srcRowInfo = &fetchRowInfo->srcRowInfo;
    HeapRowInfoT *dstRowInfo = &fetchRowInfo->dstRowInfo;
    errno_t err = EOK;
    if (HeapPageIsNormalRow(srcRowInfo->rowHeadPtr.rowState)) {
        HpNormalRowHeadT *rowHead = srcRowInfo->rowHeadPtr.normalRowHead;
        rowHead->trxInfo = allocRowInfo->newTrxInfo;
        err = memcpy_s(((uint8_t *)(rowHead + 1) + allocRowInfo->offsetOfRawData), allocRowInfo->bufSize,
            allocRowInfo->buf, allocRowInfo->bufSize);
    } else if (fetchRowInfo->is2LinkDstRow && !fetchRowInfo->isSliceRows) {
        DB_ASSERT(srcRowInfo->rowHeadPtr.rowState->isLinkSrc);
        DB_ASSERT(dstRowInfo->rowHeadPtr.rowState->isLinkDst);
        DB_ASSERT(!dstRowInfo->rowHeadPtr.rowState->isSliceHead);
        srcRowInfo->rowHeadPtr.linkRowHead->srcTrxInfo = allocRowInfo->newTrxInfo;
        HpLinkRowHeadT *rowHead = dstRowInfo->rowHeadPtr.linkRowHead;
        rowHead->dstInfo.trxId = allocRowInfo->newTrxInfo.trxId;
        err = memcpy_s(((uint8_t *)(rowHead + 1) + allocRowInfo->offsetOfRawData), allocRowInfo->bufSize,
            allocRowInfo->buf, allocRowInfo->bufSize);
    } else if (fetchRowInfo->is2LinkDstRow && fetchRowInfo->isSliceRows) {
        StatusInter ret = HeapUpdatePartialSliceSubRows(ctx, opInfo);
        if (ret != STATUS_OK_INTER) {
            SE_LAST_ERROR(ret,
                "Slice Rows Partial Update. labelId:%" PRIu32 ", srcPageId:%" PRIu32 ", srcSlotId:%" PRIu32,
                ctx->heapCfg.labelId, srcRowInfo->itemPtr.pageId, srcRowInfo->itemPtr.slotId);
            return ret;
        }
    } else {
        // 内部非预期错误
        SE_LAST_ERROR(INT_ERR_HEAP_UNEXPECT_ERROR,
            "inv row type:%" PRId32 ". labelId:%" PRIu32 ", srcPageId:%" PRIu32 ", srcSlotId:%" PRIu32,
            (int32_t)srcRowInfo->rowType, ctx->heapCfg.labelId, srcRowInfo->itemPtr.pageId, srcRowInfo->itemPtr.slotId);
        return INT_ERR_HEAP_UNEXPECT_ERROR;
    }
    if (err != EOK) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER,
            "Partial Update. labelId:%" PRIu32 ", dstPageId:%" PRIu32 ", dstSlotId:%" PRIu32, ctx->heapCfg.labelId,
            dstRowInfo->itemPtr.pageId, dstRowInfo->itemPtr.slotId);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    opInfo->hasUpdateRowInfo = true;
    return STATUS_OK_INTER;
}

StatusInter HeapUpdateRowPartialInner(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    // 原地更新，不涉及页状态变化、页空间整理、行长度变化、normal/link类型变化等，直接更新即可
    if (ctx->heapCfg.pageType == HEAP_FIX_LEN_ROW_PAGE) {
        return HeapFixPageUpdatePartialRow(ctx, opInfo);
    } else {
        return HeapVarPageUpdatePartialRow(ctx, opInfo);
    }
}

StatusInter HeapUpdateRowPartial(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    if (!ctx->heapCfg.isUseRsm) {
        return HeapUpdateRowPartialInner(ctx, opInfo);
    }

    StatusInter ret;
    HeapRowInfoT *srcRowInfo = &opInfo->fetchRowInfo->srcRowInfo;

    // 保留内存不支持大对象行
    DB_ASSERT(opInfo->fetchRowInfo->isSliceRows == false);

    uint16_t rowHeadSize;
    if (srcRowInfo->rowType == HEAP_VAR_ROW_NORMAL_ROW) {
        rowHeadSize = (uint16_t)sizeof(HpNormalRowHeadT);
    } else if (srcRowInfo->rowType == HEAP_VAR_ROW_LINK_SRC_ROW) {
        rowHeadSize = (uint16_t)sizeof(HpLinkRowHeadT);
    } else if (srcRowInfo->rowType == HEAP_FIX_ROW_NORMAL_ROW) {
        rowHeadSize = (uint16_t)sizeof(HpNormalFixRowHead);
    } else if (srcRowInfo->rowType == HEAP_FIX_ROW_LINK_SRC_ROW) {
        rowHeadSize = (uint16_t)sizeof(HpLinkSrcFixRowHead);
    } else {
        DB_ASSERT(false);
        SE_ERROR(DATA_EXCEPTION_INTER, "rsm undo, inv row type:%" PRId32, srcRowInfo->rowType);
        return DATA_EXCEPTION_INTER;
    }

    bool isNeedFreeTuple = false;
    uint32_t tupleLen = 0;
    ShmemPtrT tupleBufPos = DB_INVALID_SHMPTR;
    if (((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_ROLLBACK && !ctx->hpControl.isRsmRecovery) {
        ret = HeapUpdatePartPreBufForRsmUndo(ctx, opInfo, rowHeadSize, &tupleLen, &tupleBufPos);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        isNeedFreeTuple = true;
    } else {
        // active情况下，rsmUndo才能复用undo的buf
        TrxLiteReUseLastUpdUndoLiteRec((TrxT *)ctx->seRunCtx->trx, &tupleLen, &tupleBufPos);
    }
    RsmUndoPrtUpdInfoT rsmUndoPrtUpdInfo = {.pageId = srcRowInfo->itemPtr.pageId,
        .slotId = (PageSizeT)srcRowInfo->itemPtr.slotId,
        .isNeedFreeTuple = isNeedFreeTuple,
        .rowHeadSize = rowHeadSize,
        .partDataLen = (uint16_t)opInfo->allocRowInfo->bufSize,
        .offsetOfRawData = opInfo->allocRowInfo->offsetOfRawData,
        .tupleLen = tupleLen,
        .tupleBufPos = tupleBufPos};
    HeapRsmUndoLogForPartialUpdate(ctx, &rsmUndoPrtUpdInfo);
    SHM_CRASHPOINT_ONE(SHM_CRASH_HP_PARTIAL_UPD_BEFORE);
    ret = HeapUpdateRowPartialInner(ctx, opInfo);
    SHM_CRASHPOINT_ONE(SHM_CRASH_HP_PARTIAL_UPD_AFTER);
    // 原地更新，更新成功后，直接删除rsmUndo即可
    HeapRsmUndoLogMovePrevRecord(ctx);
    return ret;
}

StatusInter HeapUpdLogUndoAndSetTrxInfoImpl(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    uint32_t undoOpType = opInfo->allocRowInfo->isPartialUpdate ? TRX_OP_UPDATE_PART : TRX_OP_UPDATE;
    uint64_t newRollPtr = UNDO_INVALID_ROLLPTR;
    // 修改前, 获取事务字段的信息: 记录undo信息获取新的事务信息 或者 获取回滚的事务信息
    // Normal模式下: HeapUndoLogForUpdOrDel (LITE模式，详细见g_heapTrxAmForGroup的赋值)
    StatusInter ret = HeapLogUndoFunc(ctx, undoOpType, opInfo, &newRollPtr);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }
    opInfo->allocRowInfo->isUndoOperationOk = true;
    SHM_CRASHPOINT_ONE(SHM_CRASH_FH_UPD_UNDO_LOG_AFTER);
    // Normal模式下: HeapPrepareTrxInfoForUpdOrDel (LITE模式，详细见g_heapTrxAmForGroup的赋值)
    return HeapSaveTrxInfoFunc(ctx, undoOpType, opInfo, newRollPtr);
}

// 删除历史版本的linkSrc 指向的 linkDst.
void HeapUpdateCommitCleanImpl(HeapRunCtxT *ctx, const UndoRowOpInfoT *rowOpInfo, bool isLiteTrxNeedHandleDst)
{
    DB_POINTER2(ctx, rowOpInfo);
    HpRowHeadPtrT oldRowInLog = {.rowState = NULL};
    oldRowInLog.rowState = (HpRowStateT *)rowOpInfo->rowBuf;
    if (HeapPageIsNormalRow(oldRowInLog.rowState)) {
        return;
    } else {
        if (ctx->hpControl.isRsmRecovery && !isLiteTrxNeedHandleDst) {
            // 恢复阶段有两种情况的更新提交：
            // 1.处理重启前已经提交但未完成的事务，继续提交，处理重启前的undo，此时需要去删除dst
            // 2.恢复阶段中发生的更新(比如更新合并订阅节点addr)，因为不想申请新dst(触发申请新页)，此时的更新，都是normal->normal,
            // src->src(实现了dst原地更新)，不会出现回迁/跳转的情况，因此不删除dst行
            return;
        }
    }

    bool isLinkSrcRow = HeapPageIsLinkSrcRow(oldRowInLog.rowState);
    DB_ASSERT(isLinkSrcRow);
    DB_ASSERT(rowOpInfo->rowSize == sizeof(HpLinkRowHeadT) || rowOpInfo->rowSize == sizeof(HpLinkSrcFixRowHead));
    if (ctx->hpControl.isFixPage) {
        HeapCleanLinkDstRow(ctx, oldRowInLog.linkSrcFixRowHead->linkItemPtr);
        return;
    }
    HeapCleanLinkDstRow(ctx, oldRowInLog.linkRowHead->linkItemPtr);
}

void HeapUpdateForUpdateFailedClear(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    if ((SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX)) {
        // 未刷新masterVersion的，都需要由heap回收掉
        if (opInfo->allocRowInfo->sliceRowInfo.isSliceRow) {
            HeapClearSliceRow4Failed(ctx, opInfo, true);
        } else if (opInfo->allocRowInfo->newRowInfo.slot) {  // 优先判断分片，再判断slot
            StatusInter ret = HeapPageDeleteRowImpl(ctx, &opInfo->allocRowInfo->newRowInfo);
            DB_ASSERT(ret == STATUS_OK_INTER);  // 刚申请出来的页，不应该删失败
        }
    }
}

static StatusInter HeapUpdatePreAllocLinkDstRowCheckSrcPageStatus(
    HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, bool *isNeedAllocDstRow)
{
    // 在页面整理时是使用完整一行的size来计算，这里保持一致
    HpPageAllocRowInfoT *pageAllocRowInfo = opInfo->allocRowInfo;
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    HpRowStateT **rowState = &fetchRowInfo->srcRowInfo.rowHeadPtr.rowState;
    HVPageHeadT *varPageHead = fetchRowInfo->srcRowInfo.pageHeadPtr.varPageHead;
    PageSizeT requireSize =
        (PageSizeT)(HeapPageGetMinRowSize((uint16_t)pageAllocRowInfo->bufSize, HEAP_VAR_ROW_NORMAL_ROW) +
                    HeapVarPageGetOneSlotSize(pageAllocRowInfo->slotExtendSize));
    bool isCompressed = false;
    bool isSizeEnough = false;
    StatusInter ret = HeapVarPageConfirmSize(ctx, varPageHead, requireSize, &isCompressed, &isSizeEnough);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    if (isCompressed) {
        *rowState = HeapVarPageGetRowStateImpl(varPageHead, fetchRowInfo->srcRowInfo.slot);
    }
    HpPageRowTypeE srcRowType = HeapPageIsNormalRow(*rowState) ? HEAP_VAR_ROW_NORMAL_ROW : HEAP_VAR_ROW_LINK_SRC_ROW;
    PageSizeT curRowSize =
        (PageSizeT)(HeapPageGetMinRowSize((PageSizeT)fetchRowInfo->srcRowInfo.rawRowSize, srcRowType) +
                    HeapVarPageGetOneSlotSize(pageAllocRowInfo->slotExtendSize));
    *isNeedAllocDstRow = (curRowSize < requireSize && !isSizeEnough);
    return STATUS_OK_INTER;
}

static StatusInter HeapHeapUpdatePreAllocSliceRow(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    HpPageAllocRowInfoT *pageAllocRowInfo = opInfo->allocRowInfo;
    HeapRowSetTrxId(&opInfo->allocRowInfo->newTrxInfo, ((TrxT *)(ctx->seRunCtx->trx))->base.trxId);
    pageAllocRowInfo->isAllowLinkRowInOnePage = true;
    StatusInter ret = HeapUpdateAllocSliceRows(ctx, opInfo);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    // 重新给srcRow加锁，HeapUpdateAllocSliceRows会释放srcRow的锁，此处重新上锁
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    ret = HeapIsFreeLatchUnderLabel(ctx) ? HeapLabelLatchCheckPage(ctx, &fetchRowInfo->srcRowInfo) :
                                           HeapPageAddLatch(ctx, &fetchRowInfo->srcRowInfo);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    return HeapVarRowRefetch(ctx, opInfo);
}

StatusInter HeapUpdatePreWithOptimisticTrx(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo)
{
    DB_POINTER2(ctx, opInfo);
    if (ctx->heapCfg.pageType == HEAP_FIX_LEN_ROW_PAGE) {
        // 乐观暂不支持表升降级，所以是fix表时，肯定都是normal行，因此不用提前申请link行或者分片行，直接return即可
        return STATUS_OK_INTER;
    }
    StatusInter ret;
    HpPageAllocRowInfoT *pageAllocRowInfo = opInfo->allocRowInfo;
    if (SECUREC_UNLIKELY(HeapUpdateIsNewTupleNeedSlice(ctx, opInfo))) {  // 判断大对象行
        return HeapHeapUpdatePreAllocSliceRow(ctx, opInfo);
    }
    // 判断是否需要申请跳转行
    bool isNeedAllocDstRow = false;
    TrxT *trx = ((TrxT *)(ctx->seRunCtx->trx));
    HpPageFetchRowInfo *fetchRowInfo = opInfo->fetchRowInfo;
    HpTrxPrevVersionRowInfoT *prevVersionInfo = &opInfo->fetchRowInfo->prevVersionRowInfo;
    if (prevVersionInfo->hasPrevVersion && prevVersionInfo->prevSavePointId == trx->trx.base.savePointId &&
        !prevVersionInfo->isOnMasterVersion) {  // 可以合并但不在masterVersion上，则需要申请DstRow
        isNeedAllocDstRow = true;
    }
    if (!isNeedAllocDstRow) {
        // 页面整理需申请动态内存，可能失败，需提前页面整理
        ret = HeapUpdatePreAllocLinkDstRowCheckSrcPageStatus(ctx, opInfo, &isNeedAllocDstRow);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    if (isNeedAllocDstRow) {
        HeapRowSetTrxId(&opInfo->allocRowInfo->newTrxInfo, trx->base.trxId);
        pageAllocRowInfo->isAllowLinkRowInOnePage = true;
        // 结合pageHead->FreeSize、rollBackReserveSize与更新的tupleBuf->bufSize比较，判断是否需要申请跳转行
        HeapAllocRowInfoInit(pageAllocRowInfo, HEAP_VAR_ROW_LINK_DST_ROW, true);
        pageAllocRowInfo->sliceRowInfo.linkSrcRowPtr = fetchRowInfo->srcRowInfo.itemPtr;
        ret = HeapAllocVarRow(ctx, opInfo);  // 跨页申请
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        (void)DbAtomicAdd64(&ctx->perfStat->historyLinkRows, (uint64_t)1);
        if (SePgLatchSorterIsPageReLatch(&ctx->pageSorter, fetchRowInfo->srcRowInfo.itemPtr.pageId)) {
            SePgLatchSorterClearPageReLatchState(&ctx->pageSorter, fetchRowInfo->srcRowInfo.itemPtr.pageId);
            return HeapVarRowRefetch(ctx, opInfo);
        }
    }
    return STATUS_OK_INTER;
}

void HeapUpdateInit(HeapRunCtxT *ctx, HpPageRowOpInfoT *opInfo, const HpItemPointerT *itemPtr)
{
    DB_POINTER3(ctx, opInfo, itemPtr);
    opInfo->fetchRowInfo->srcRowInfo.itemPtr = *itemPtr;
    opInfo->fetchRowInfo->curRowInfo = &opInfo->fetchRowInfo->srcRowInfo;
    // RR事务需要读主版本，用来记录undo，因此也需要读markDelRow
    // 在linkSrc行头存储了bufSize，可以不访问dstRow了（需要尽可能避免乐观下出现relatch）
    bool isOptimisticTrx = (SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX);
    opInfo->fetchRowInfo->isCanAccessMarkDelRow = (SeTransGetIsolationLevel(ctx->seRunCtx) == REPEATABLE_READ);
    opInfo->fetchRowInfo->isFindPrevVersionInfo = isOptimisticTrx;
    opInfo->fetchRowInfo->isOnlyReadSrcRow = isOptimisticTrx;  // 保留内存恢复流程需要去读dst行
    HeapSetPageLatchActionForWrite(ctx);
    HeapSetOperationForUpdate(ctx);
    // Normal模式下: HeapSetLockActionBeforeUpdate (LITE模式，详细见g_heapTrxAmForGroup的赋值)
    HeapSetLockFunc(ctx, ctx->hpControl.trxContrl.hpAmIndex);
}

StatusInter HeapUpdatePrepare(HeapRunCtxT *ctx, const HeapTupleBufT *tupleBuf, HpPageRowOpInfoT *opInfo)
{
    // 先查询, 获取 当前 row的信息
    StatusInter ret = HeapFetchRow(ctx, opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    if (SECUREC_UNLIKELY(
            ctx->hpControl.isFixPage && (opInfo->fetchRowInfo->bufSize > ctx->heap->regularInfo.maxRowSize))) {
        SE_LAST_ERROR(PROGRAM_LIMIT_EXCEEDED_HEAP_MAX_ROW_SIZE,
            "fix heap update, old row size too long. bufSize:%" PRIu32 ", maxRowSize:%" PRIu32 ", labelID:%" PRIu32
            ", trxId:%" PRIu64 ".",
            opInfo->fetchRowInfo->bufSize, ctx->heap->regularInfo.maxRowSize, ctx->heapCfg.labelId,
            ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
        // 出现这种情况，后续遇到事务回滚会出问题，此处assert维护分析
        DB_ASSERT(false);
        return PROGRAM_LIMIT_EXCEEDED_HEAP_MAX_ROW_SIZE;
    }

    // 在RR事务下，为了获知并发冲突，需要读取最新已提交数据以判断是否可见
    // 由于当前乐观设定为表级别冲突域不存在上述冲突，因此这里判断为悲观事务
    ret = HeapCheckRrTrxConflict(ctx, opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    ret = HeapInitAllocRowInfoImpl(ctx, tupleBuf->buf, tupleBuf->bufSize, opInfo->allocRowInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        return ret;
    }

    if (SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX) {
        ret = HeapUpdatePreWithOptimisticTrx(ctx, opInfo);
        if (ret != STATUS_OK_INTER) {
            HeapUpdateForUpdateFailedClear(ctx, opInfo);
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter HeapUpdateImpl(
    HeapRunCtxT *ctx, const HeapTupleBufT *tupleBuf, const HpItemPointerT *itemPtr, HeapUpdOutPara *out)
{
    DB_POINTER3(ctx, tupleBuf, itemPtr);
    DB_ASSERT(((TrxT *)ctx->seRunCtx->trx)->base.state != TRX_STATE_ABORT);
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageAllocRowInfoT pageAllocRowInfo = EmptyHpPageAllocRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, &pageAllocRowInfo);
    HeapUpdateInit(ctx, &opInfo, itemPtr);
    StatusInter ret = HeapUpdatePrepare(ctx, tupleBuf, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto EXIT;
    }

    ret = HeapUpdLogUndoAndSetTrxInfoImpl(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        HeapUpdateForUpdateFailedClear(ctx, &opInfo);
        goto EXIT;
    }

    // 一般的更新, 只会发生reLatch, 但是各个page还是持有的. 但是大对象的更新, 最后是没有持有page的
    ret = HeapUpdateRowImpl(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "update row(%" PRIu32 ", %" PRIu32 ") unsucc, trxId %" PRIu64 " label %" PRIu32, itemPtr->pageId,
            itemPtr->slotId, ((TrxT *)ctx->seRunCtx->trx)->base.trxId, ctx->heapCfg.labelId);
        HeapUpdateForUpdateFailedClear(ctx, &opInfo);
    } else {
        HeapHandleAfterUpdateSuccessfully(ctx, tupleBuf, itemPtr, &opInfo, out->isUndoBypass);
    }
    HeapStatUpdate(ctx, &opInfo);
EXIT:
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter,
        SE_PG_LATCH_SORTER_NO_EXCEPTION);  // 所有操作结束后, 释放过程中未释放的 page
    return ret;
}

Status HeapLabelUpdateCompactWithHpTupleBuf(
    HpRunHdlT heapRunHdl, const HeapTupleBufT *heapTupleBuf, HpTupleAddr heapTupleAddr)
{
    // 仅光启 YANG 持久化恢复场景支持
    DB_UNUSED3(heapRunHdl, heapTupleBuf, heapTupleAddr);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

StatusInter HeapUpdatePartialImpl(
    HeapRunCtxT *ctx, const HeapTupleBufT *tupleBuf, const HpItemPointerT *itemPtr, uint32_t offsetOfRawData)
{
    DB_POINTER3(ctx, tupleBuf, itemPtr);
    if (SeTransGetTrxType(ctx->seRunCtx) == OPTIMISTIC_TRX ||
        SeTransGetIsolationLevel(ctx->seRunCtx) == REPEATABLE_READ) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER,
            "Partial update not supported. trxType:%" PRIu32 ", isolationLevel:%" PRIu32 ", labelId:%" PRIu32 ".",
            SeTransGetTrxType(ctx->seRunCtx), SeTransGetIsolationLevel(ctx->seRunCtx), ctx->heapCfg.labelId);
        return INTERNAL_ERROR_INTER;
    }
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageAllocRowInfoT pageAllocRowInfo = EmptyHpPageAllocRowInfo();
    pageAllocRowInfo.isPartialUpdate = true;
    pageAllocRowInfo.offsetOfRawData = offsetOfRawData;
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, &pageAllocRowInfo);
    HeapUpdateInit(ctx, &opInfo, itemPtr);
    StatusInter ret = HeapUpdatePrepare(ctx, tupleBuf, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto EXIT;
    }

    if (opInfo.allocRowInfo->bufSize > opInfo.fetchRowInfo->bufSize) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "buf, Partial update size:%" PRIu32 " large origin size:%" PRIu32,
            opInfo.allocRowInfo->bufSize, opInfo.fetchRowInfo->bufSize);
        ret = DATA_EXCEPTION_INTER;
        goto EXIT;
    }

    // 如果是大对象行，在此处先读出要部分更新的那个分片，存到fetchRowInfo.buf里(不修改fetchRowInfo其他内容)，后续记undo用
    // 目前部分更新只考虑系统字段，所以要更新的内容只会在第一个分片的前面，不涉及更新内容跨分片的场景
    if (fetchRowInfo.isSliceRows) {
        ret = HeapUpdatePartialFetchSliceRowBuf(ctx, &opInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_LAST_ERROR(ret, "acquire sub row buff unsucc.");
            goto EXIT;
        }
    }

    ret = HeapUpdLogUndoAndSetTrxInfo(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        goto EXIT;
    }

    // 一般的更新, 只会发生reLatch, 但是各个page还是持有的. 但是大对象的更新, 最后是没有持有page的
    ret = HeapUpdateRowPartial(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "Partial update row(%" PRIu32 ", %" PRIu32 "), trxId %" PRIu64 " label %" PRIu32, itemPtr->pageId,
            itemPtr->slotId, ((TrxT *)ctx->seRunCtx->trx)->base.trxId, ctx->heapCfg.labelId);
        goto EXIT;
    }
    HeapStatUpdate(ctx, &opInfo);
EXIT:
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter,
        SE_PG_LATCH_SORTER_NO_EXCEPTION);  // 所有操作结束后, 释放过程中未释放的 page
    return ret;
}

// noteb: 可靠性看护, 长稳运行稳定后, 可以删除
void HeapConfirmForRealDel(const HeapRunCtxT *ctx, const HpPageFetchRowInfo *fetchRowInfo)
{
    DB_POINTER4(ctx, fetchRowInfo, ctx->seRunCtx, ctx->seRunCtx->trx);
    TrxT *trx = (TrxT *)ctx->seRunCtx->trx;
    if (trx->base.state == TRX_STATE_ROLLBACK) {
        // 插入操作的回滚，isDeleted肯定为false，并且一定是同一个事务才能这样操作
        DB_ASSERT(fetchRowInfo->srcRowInfo.rowHeadPtr.rowState->isDeleted == false);
        TrxIdT rowTrxId = HeapRowGetTrxId(&fetchRowInfo->rowTrxInfo);
        bool isSameTrx = rowTrxId == ((TrxT *)ctx->seRunCtx->trx)->base.trxId;
        DB_ASSERT(isSameTrx || ctx->hpControl.isRsmRecovery);
    } else if (((TrxT *)ctx->seRunCtx->trx)->base.state == TRX_STATE_ACTIVE) {
        HeapCheckIsRealDelete(ctx, trx);
    }
}

StatusInter HeapDeleteInnerRowImpl(HeapRunCtxT *ctx, HpItemPointerT itemPtr, uint32_t *bufSize)
{
    DB_POINTER(ctx);
    // 一堆上下文的构造...
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageAllocRowInfoT pageAllocRowInfo = EmptyHpPageAllocRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, &pageAllocRowInfo);
    fetchRowInfo.srcRowInfo.itemPtr = itemPtr;
    HpPageFetchRowInfoInitCtrlFlag(&fetchRowInfo);
    fetchRowInfo.canAccessLinkDst = true;
    fetchRowInfo.canAccessSubRow = true;
    fetchRowInfo.isOnlyReadSrcRow = true;  // 只删除addr指向的 行, 不去查询关联的行!
    fetchRowInfo.isCanAccessMarkDelRow = true;
    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;

    DB_ASSERT(ctx->hpOperation <= HEAP_OPTYPE_MODIFY);  // 后面的fetch过程中, page加X锁
    uint16_t oldPageNum = ctx->pageSorter.pageNum;      // 记录当前page的个数
    HeapSetPageLatchActionForWrite(ctx);
    HeapSetLockActionBeforeDelete(ctx);
    // 查询, 获取当前 row 的偏移信息
    StatusInter ret = HeapFetchRow(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        HeapFreeOnePageLatch(ctx, itemPtr, oldPageNum);  // 只释放新增的page 的latch.
        return ret;
    }
    DB_ASSERT(fetchRowInfo.srcRowInfo.rowType == HEAP_VAR_ROW_LINK_DST_ROW ||
              fetchRowInfo.srcRowInfo.rowType == HEAP_VAR_ROW_LINK_DST_SLICE_DIR_ROW ||
              fetchRowInfo.srcRowInfo.rowType == HEAP_VAR_ROW_SLICE_SUB_ROW ||
              (fetchRowInfo.srcRowInfo.rowType == HEAP_VAR_ROW_LINK_SRC_ROW && fetchRowInfo.isSliceRows));
    ret = HeapPageDeleteRowImpl(ctx, &fetchRowInfo.srcRowInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "label %" PRIu32 " del row (type%" PRIu32 "), trxId %" PRIu64 "", ctx->heapCfg.labelId,
            (uint32_t)fetchRowInfo.srcRowInfo.rowType, ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
    }
    // 只释放新增的page 的latch.
    HeapFreeOnePageLatch(ctx, itemPtr, oldPageNum);
    return ret;
}

StatusInter HeapDeleteLinkDstRowImpl(
    HeapRunCtxT *ctx, HeapRowInfoT *linkDstRowInfo, bool isSliceRows, bool isKeepOtherPageLatch)
{
    DB_POINTER2(ctx, linkDstRowInfo);
    StatusInter ret;
    if (!isKeepOtherPageLatch && !HeapIsFreeLatchUnderLabel(ctx)) {
        // 只需要保留目的行的页锁，其他页锁全部释放
        // 一般场景如，单行删除，回滚等都没有必要继续持有除目的行以外的锁。
        // 但批量删除场景是在一页上逐行删除，需要保留源行页
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, linkDstRowInfo->itemPtr.pageId);
    }
    if (!isSliceRows) {  // 非分片场景, 后面只需要删除 linkDst 即可.
        ret = HeapPageDeleteRowImpl(ctx, linkDstRowInfo);
    } else {
        ret = HeapDeleteSliceDirAndSubRows(ctx, linkDstRowInfo);
    }
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "label %" PRIu32 " del row, trxId %" PRIu64 "", ctx->heapCfg.labelId,
            ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
    }
    return ret;
}

StatusInter HeapRealDelete(HeapRunCtxT *ctx, HpPageFetchRowInfo *fetchRowInfo)
{
    StatusInter ret = STATUS_OK_INTER;
#ifndef NDEBUG
    HeapConfirmForRealDel(ctx, fetchRowInfo);  // noteb: 可靠性看护, 长稳运行稳定后, 可以删除
#endif
    ret = HeapPageDeleteRowImpl(ctx, &fetchRowInfo->srcRowInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "label %" PRIu32 " del row, trxId %" PRIu64 "", ctx->heapCfg.labelId,
            ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
    } else {
        DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP,
            "HeapDeleteImpl label %" PRIu32 " del row (%" PRIu32 ",%" PRIu32 "), trxId:%" PRIu64, ctx->heapCfg.labelId,
            fetchRowInfo->srcRowInfo.itemPtr.pageId, fetchRowInfo->srcRowInfo.itemPtr.slotId,
            ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
    }
    if (fetchRowInfo->is2LinkDstRow) {
        ret = HeapDeleteLinkDstRowImpl(ctx, &fetchRowInfo->dstRowInfo, fetchRowInfo->isSliceRows, false);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "label %" PRIu32 " del row, trxId %" PRIu64 "", ctx->heapCfg.labelId,
                ((TrxT *)ctx->seRunCtx->trx)->base.trxId);
        }
    }
    if (ret == STATUS_OK_INTER) {
        return HeapStatDelete(ctx, fetchRowInfo);
    } else {
#ifndef SYS32
        // arm32 内存满的情况下，客户端可能attach不到FSM page返回错误, 但是要确保删除流程成功
        DB_ASSERT(false);
#endif
        // normal模式只有回滚、清理版本链会走到这里，不应该失败
        // 大表锁、RU模式下真正删除失败，因为没有记undo，不会回滚，对curItem无法进行维护
    }
    return ret;
}

StatusInter HeapDeleteImpl(HeapRunCtxT *ctx, HpItemPointerT itemPtr, bool isMarkDelete)
{
    DB_POINTER2(ctx, ctx->seRunCtx);
    DB_ASSERT(((TrxT *)ctx->seRunCtx->trx)->base.state != TRX_STATE_ABORT);
    HpPageFetchRowInfo fetchRowInfo = EmptyHpPageFetchRowInfo();
    HpPageAllocRowInfoT pageAllocRowInfo = EmptyHpPageAllocRowInfo();
    HpPageRowOpInfoT opInfo = InitHpPageRowOpInfo(&fetchRowInfo, &pageAllocRowInfo);
    fetchRowInfo.srcRowInfo.itemPtr = itemPtr;
    // 物理删除可以访问被标记为删除的行
    // RR事务需要读主版本，用来记录undo，因此也需要读markDelRow
    fetchRowInfo.isCanAccessMarkDelRow = !isMarkDelete || (SeTransGetIsolationLevel(ctx->seRunCtx) == REPEATABLE_READ);
    fetchRowInfo.isOnlyReadSrcRow = isMarkDelete;
    fetchRowInfo.curRowInfo = &fetchRowInfo.srcRowInfo;

    DB_ASSERT(ctx->hpOperation <= HEAP_OPTYPE_MODIFY);  // 后面的fetch过程中, page加X锁
    HeapSetPageLatchActionForWrite(ctx);
    HeapSetOperationForDelete(ctx, isMarkDelete);
    // Normal模式下: isMarkDelete为真时, HeapSetLockActionBeforeMarkDelete
    // isMarkDelete == false时, HeapSetLockActionBeforeDelete (LITE模式，详细见g_heapTrxAmForGroup的赋值)
    HeapSetLockFunc(ctx, ctx->hpControl.trxContrl.hpAmIndex);

    // 查询, 获取当前 row 的信息
    StatusInter ret = HeapFetchRow(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
        if (ctx->hpControl.isRsmRecovery) {
            DB_ASSERT(!isMarkDelete);
            return STATUS_OK_INTER;  // 保留内存恢复可能会重复进入
        }
        return ret;
    }

    // 在RR事务下，为了获知并发冲突，需要读取最新已提交数据以判断是否可见
    // 由于当前乐观设定为表级别冲突域不存在上述冲突，因此这里判断为悲观事务
    ret = HeapCheckRrTrxConflict(ctx, &opInfo);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
        return ret;
    }

    if (isMarkDelete) {
        ret = HeapDeleteRowSetMarkImpl(ctx, &opInfo);
        if (ret == STATUS_OK_INTER) {
            HeapStatMarkDelete(ctx, &fetchRowInfo);
        }
    } else {
        ret = HeapRealDelete(ctx, &fetchRowInfo);
    }
    /* 释放 pageSorter 持有的latch */
    SePgLatchSorterReset(ctx->seRunCtx, ctx->pageMgr, &ctx->pageSorter, SE_PG_LATCH_SORTER_NO_EXCEPTION);
    return ret;
}

inline uint64_t HeapGetLastTruncateTrxIdImpl(HeapRunCtxT *ctx)
{
    return ctx->heap->regularInfo.lastTruncateTrxId;
}

#ifdef __cplusplus
}
#endif
