/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: se_index_am.h
 * Description: storage index access method head file
 * Author: pengfengbin
 * Create: 2021/4/26
 */

#ifndef SE_INDEX_AM_H
#define SE_INDEX_AM_H

#include "dm_data_basic.h"
#include "dm_data_index.h"
#include "dm_meta_basic.h"
#include "dm_meta_prop_label.h"
#include "dm_meta_kv_label.h"
#include "se_define.h"
#include "se_index.h"

#define IDX_PREFETCH_BATCH_SIZE 10u
#define SE_INDEX_TRYLOCK_DELAY_TIME 100u

#ifdef __cplusplus
extern "C" {
#endif

typedef struct IdxFunc {
    Status (*idxCreate)(SeRunCtxHdT seRunCtx, IndexMetaCfgT idxCfg, ShmemPtrT *idxShmAddr);
    Status (*idxDrop)(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr);
    void (*idxCommitDrop)(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr);
    Status (*idxTruncate)(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr);
    void (*idxCommitTruncate)(SeRunCtxHdT seRunCtx, ShmemPtrT idxShmAddr);
    Status (*idxOpen)(IndexCtxT *idxCtx);
    void (*idxClose)(IndexCtxT *idxCtx);
    Status (*idxInsert)(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr);
    Status (*idxBatchInsert)(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum);
    Status (*idxDelete)(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr, IndexRemoveParaT removePara);
    Status (*idxBatchDelete)(
        IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum, IndexRemoveParaT removePara);
    Status (*idxUpdate)(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara);
    Status (*idxBatchUpdate)(
        IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo[], uint32_t batchNum, IndexRemoveParaT removePara);
    Status (*idxEndBatchModify)(IndexCtxT *idxCtx);
    Status (*idxLookup)(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr *addr, bool *isFound); /* 可以为空 */
    Status (*idxBatchLookup)(IndexCtxT *idxCtx, IndexKeyT idxKey[], uint32_t batchNum, IdxBatchLookupParaT *para);
    Status (*idxBeginScan)(IndexCtxT *idxCtx, IndexScanCfgT scanCfg, IndexScanItrT *iter);
    Status (*idxScan)(IndexCtxT *idxCtx, IndexScanItrT iter, HpTupleAddr *addr, bool *isFound);
    void (*idxEndScan)(const IndexCtxT *idxCtx, IndexScanItrT iter);
    Status (*idxSetDirection)(
        IndexCtxT *idxCtx, IndexScanItrT iter, IndexScanDirectionE scanDirection); /* 目前仅btree存在，其他可以为空 */
    Status (*idxUndoInsert)(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr);
    Status (*idxUndoRemove)(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr);
    Status (*idxUndoUpdate)(IndexCtxT *idxCtx, IndexUpdateInfoT updateInfo, IndexRemoveParaT removePara);
    Status (*idxGetKeyCount)(IndexCtxT *idxCtx, IndexKeyT idxKey, uint64_t *count); /* 可以为空 */
    Status (*idxStatView)(ShmemPtrT idxShmAddr, uint16_t instanceId, IndexStatisticsT *idxStat);
    Status (*idxGetPageSize)(ShmemPtrT idxShmAddr, uint16_t instanceId, uint64_t *idxPageSize);
    Status (*idxScaleIn)(IndexCtxT *idxCtx, IndexScaleInCfgT *idxScaleCfg); /* 可以为空 */
    Status (*idxGetEstimateMemSize)(
        uint16_t instanceId, IndexMetaCfgT idxMetaCfg, uint64_t count, uint32_t keyLen, uint64_t *size);
    void (*idxGetCtxSize)(size_t *ctxSize, size_t *iterSize);
    Status (*idxPreload)(IndexCtxT *idxCtx, void *userData);
    Status (*idxTableLoad)(IndexCtxT *idxCtx);
    Status (*idxLoad)(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr);
#ifdef FEATURE_LPASMEM
    Status (*idxSetExtendKey)(IndexCtxT *idxCtx, IndexKeyT key, uint32_t offset, uint8_t *value, uint32_t len);
#endif
#ifdef FEATURE_SIMPLEREL
    Status (*idxFetchAddr)(IndexCtxT *idxCtx, uint8_t *idxHead, uint32_t updateSize, IndexUpdateInfoT updateInfo[]);
#endif
} IdxFuncT;

struct IdxBase {
    IndexMetaCfgT indexCfg;
    DbLatchT idxLatch;
    uint32_t isConstructed;
    uint32_t shmemCtxId;
    DbLatchT stashPageLatch;
    uint64_t validCode;
};

typedef enum IdxScanMode {
    IDX_SCAN_BEGIN = 0,             // GME_SCAN_BUTTOM
    IDX_SCAN_EQUAL_OR_LESS_KEY,     // GME_SCAN_EQUAL_OR_LESS_KEY
    IDX_SCAN_EQUAL_OR_GREATER_KEY,  // GME_SCAN_EQUAL_OR_GREATER_KEY
    IDX_SCAN_FIRST,                 // GME_SCAN_FIRST
    IDX_SCAN_LAST,                  // GME_SCAN_LAST
    IDX_SCAN_PREFIX,                // GME_SCAN_PREFIX
    IDX_SCAN_BUTT
} IdxScanModeE;

#define DB_INVALID_MEM_ADDR ((DbMemAddrT){NULL, SE_INVALID_PAGE_ADDR})

typedef struct TagDbMemAddr {
    uint8_t *virtAddr;
    PageIdT pageAddr;
} DbMemAddrT;

/**
 * `BTreeIndexCtxT` contains B-tree specific runtime information. `IndexCtxT` and `BTreeIndexCtxT`
 * together form the runtime context for B-tree.
 * 由于se_undo_trx_resource.c依赖BTreeIndexCtxT 会改动markDel，故搬移到此
 */
typedef struct BTreeIndexCtx {
    PageMgrT *pageMgr;       ///< page manager
    uint32_t markDel : 1;    ///< rollback insertion by mark deletion
    uint32_t reserved : 31;  ///< reserved field
} BTreeIndexCtxT;

#ifdef EXTRAMSG
// 打印索引中关键路径信息用于排查错误
#define IDX_DML_EXTRAMSG(format, key, addr, idxId, idxType) IdxDmlExtraMsg(format, key, addr, idxId, idxType)
#else
#define IDX_DML_EXTRAMSG(format, key, addr, idxId, idxType)
#endif

void IdxDmlExtraMsg(const char *format, IndexKeyT key, TupleAddr addr, uint32_t idxId, uint8_t idxType);

SO_EXPORT_FOR_HAC_AND_TS IdxFuncT IdxEmptyIdxFunc(void);
SO_EXPORT_FOR_HAC_AND_TS void IdxAmFuncRegister(uint8_t indexType, const IdxFuncT *const idxFunc);
SO_EXPORT void HashIndexAMInit(void);
SO_EXPORT void HashLocalIndexAMInit(void);
SO_EXPORT void HashLinklistIdxAmInit(void);
SO_EXPORT void SortedIndexAMInit(void);
SO_EXPORT void ArtHashClusterAMInit(void);
SO_EXPORT void Lpm4IndexAMInit(void);
SO_EXPORT void Lpm6IndexAMInit(void);
SO_EXPORT void HcIndexAmInit(void);
SO_EXPORT void CHIndexAMinit(void);
SO_EXPORT void BTreeIndexAMInit(void);

inline static IndexCtxT *IdxGetCtx(Handle idxRunCtx)
{
    return (IndexCtxT *)((uint8_t *)idxRunCtx - sizeof(IndexCtxT));
}

inline static bool IdxIsConstructed(const IdxBaseT *idxBase)
{
    DB_POINTER(idxBase);
    return idxBase->isConstructed == 1u;
}

inline static void IdxConstructedFinish(IdxBaseT *idxBase)
{
    DB_POINTER(idxBase);
    idxBase->isConstructed = 1u;
}

inline static void IdxMarkUnConstructed(IdxBaseT *idxBase)
{
    DB_POINTER(idxBase);
    idxBase->isConstructed = 0u;
}

static inline void IdxRlock(const IndexCtxT *idxCtx, LatchAddrTypeE latchAddrType)
{
    LATCH_GET_START_WAITTIMES(RLATCH, idxCtx->idxHandle->idxLatch);
    DbSessionCtxT *sessionCtx = &((SeRunCtxT *)idxCtx->idxOpenCfg.seRunCtx)->resSessionCtx;
    if (sessionCtx->isDirectRead) {
        ShmemPtrT latchAddr = idxCtx->idxShmAddr;
        GET_MEMBER_SHMPTR(latchAddr, offsetof(IdxBaseT, idxLatch));
        DbRWSpinRLockWithSession(sessionCtx, &idxCtx->idxHandle->idxLatch, &latchAddr, latchAddrType);
    } else {
        DbRWLatchR(&idxCtx->idxHandle->idxLatch);
    }
    LATCH_GET_END_WAITTIMES(RLATCH, idxCtx->idxHandle->idxLatch, sessionCtx->session);
}

static inline void IdxRUnlock(const IndexCtxT *idxCtx)
{
    SeRunCtxT *seRunCtx = idxCtx->idxOpenCfg.seRunCtx;
    DbSessionCtxT *sessionCtx = &seRunCtx->resSessionCtx;
    if (sessionCtx->isDirectRead) {
        DbRWSpinRUnlockWithSession(sessionCtx, &idxCtx->idxHandle->idxLatch);
    } else {
        DbRWUnlatchR(&idxCtx->idxHandle->idxLatch);
    }
}

static inline void IdxWlock(const IndexCtxT *idxCtx, LatchAddrTypeE latchAddrType)
{
    LATCH_GET_START_WAITTIMES(WLATCH, idxCtx->idxHandle->idxLatch);
    DbSessionCtxT *sessionCtx = &((SeRunCtxT *)idxCtx->idxOpenCfg.seRunCtx)->resSessionCtx;
    if (sessionCtx->isDirectRead) {
        ShmemPtrT latchAddr = idxCtx->idxShmAddr;
        GET_MEMBER_SHMPTR(latchAddr, offsetof(IdxBaseT, idxLatch));
        DbRWSpinWLockWithSession(sessionCtx, &idxCtx->idxHandle->idxLatch, &latchAddr, latchAddrType);
    } else {
        DbRWLatchW(&idxCtx->idxHandle->idxLatch);
    }
    LATCH_GET_END_WAITTIMES(WLATCH, idxCtx->idxHandle->idxLatch, sessionCtx->session);
}

static inline void IdxWUnlock(const IndexCtxT *idxCtx)
{
    SeRunCtxT *seRunCtx = idxCtx->idxOpenCfg.seRunCtx;
    DbSessionCtxT *ctx = &seRunCtx->resSessionCtx;
    if (ctx->isDirectRead) {
        DbRWSpinWUnlockWithSession(ctx, &idxCtx->idxHandle->idxLatch);
    } else {
        DbRWUnlatchW(&idxCtx->idxHandle->idxLatch);
    }
}

StatusInter IndexSeShmMemCreate(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx);

SO_EXPORT_FOR_TS bool IdxContinueTry(
    const SeRunCtxT *seRunCtx, uint32_t *lockConflictTryCnt, Status ret, uint64_t begin);

SO_EXPORT_FOR_HAC Status IdxCheckFilterSatisfied(const IndexCtxT *idxCtx, IndexKeyT key, uint8_t *cmpRes);

static inline bool SeIndexIsSupportUndoRollback(DmIndexTypeE indexType)
{
    // 当前仅btree,diskann索引支持undo rollback
    return (indexType == BTREE_INDEX || indexType == DISKANN_INDEX);
}

// 获取比n大的最小的2^x
ALWAYS_INLINE static uint32_t IdxGetRealCap(uint32_t cap)
{
    uint32_t n = cap - 1;
    uint32_t shiftRight = 1;
    n |= n >> shiftRight;
    shiftRight <<= 1;
    n |= n >> shiftRight;
    shiftRight <<= 1;
    n |= n >> shiftRight;
    shiftRight <<= 1;
    n |= n >> shiftRight;
    shiftRight <<= 1;
    n |= n >> shiftRight;
    return n + 1;
}

SeHpTupleAddrMode IdxGetTupleAddrMode(const SeInstanceT *seIns, bool isUseRsm);

#ifdef FEATURE_HAC
SO_EXPORT StatusInter IdxHacInit(SeInstanceT *seInsPtr, DbMemCtxT *memCtx);
#endif

#ifdef __cplusplus
}
#endif

#endif
