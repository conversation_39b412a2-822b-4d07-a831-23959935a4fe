/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: db级别的数据库对象管理
 * Create: 2023-6
 */
#include "se_database.h"
#include "adpt_string.h"
#include "se_ctrl_page_pool.h"
#include "se_db_file_device.h"
#include "se_db_file_space.h"
#include "se_db_file.h"
#include "se_dfx.h"
#include "se_log.h"
#include "se_space_inner.h"
#include "se_page_mgr.h"
#include "se_durable_memdata.h"
#include "db_timer.h"
#include "db_crc.h"
#include "db_dyn_load.h"
#include "db_table_space.h"
#include "db_utils.h"
#include "se_undo_page.h"
#include "adpt_compress.h"
#include "se_space_redo.h"
#include "adpt_io.h"
#include "se_persist_inner.h"
#include "se_recovery_inner.h"
#include "se_ckpt_inner.h"
#include "gme_api.h"
#include "db_persist_version.h"
#include "se_verify_inner.h"
#include "se_vfd.h"
#include "se_heap_redo_am.h"
#include "se_persistence_translate_impl.h"
#include "se_db_file_core.h"
#include "se_ctrl_page.h"
#include "se_space_condensed.h"
#include "srv_data_ha.h"
#include "se_mem_struct.h"
#include "se_space_ddl.h"
#include "se_db_file.h"
#include "se_db_file_extend.h"
#include "se_space_virtual_disk.h"

#define DEFAULT_TRX_MAX_ID 1
#define SPACE_BLOCK_NUM_PER_EXTENT 1
#define DB_GET_LATCH_SPLIT_TIMEOUT (100 * 1000)  // us

// 初始化失败和SeDestroyIns都会用此释放函数
void DbReleaseInitMem(SeInstanceT *seIns)
{
    DB_POINTER(seIns);

    StDatabaseT *db = seIns->db;
    if (db == NULL) {
        return;
    }
    DB_ASSERT(seIns->db->pfMgr.ctx == NULL);
    SeFreeDbCtrlExtend(seIns);
    CtrlGroupClear(&db->spaceGroup, SpaceDestroyFileList, NULL);
    CtrlGroupClear(&db->fileGroup, SeCloseDataFile, seIns);
    CtrlGroupClear(&db->deviceGroup, NULL, NULL);

    SeCloseCtrlFile((uint8_t *)&db->ctrlFile, NULL);

    if (seIns->db->safeFile.handle != DB_INVALID_FD) {
        SeFileClose(&seIns->db->safeFile.handle);
    }

    if (seIns->vfdMgr != NULL) {
        VfdMgrDestructor(seIns);
    }

    if (seIns->db->vd != NULL) {
        SeVirtualDiskDestroy(seIns);
    }

    SeCtrlPagePoolClear(&db->ctrlPagePool);
    SeDestroyMemStructMap(seIns);
    DbDynMemCtxFree(seIns->seServerMemCtx, db);
    seIns->db = NULL;
}

StatusInter DbDatabaseMemInit(SeInstanceT *seIns, StDatabaseT *db)
{
    DB_POINTER2(seIns, db);
    CtrlGroupInit(&db->spaceGroup, db->memCtx, sizeof(SpaceT));
    CtrlGroupInit(&db->fileGroup, db->memCtx, sizeof(DbFileT));
    CtrlGroupInit(&db->deviceGroup, db->memCtx, sizeof(DbDeviceT));
    return SeInitDbCtrlExtend(seIns);
}

StatusInter DbAllocMemOnDemand(SeInstanceT *seIns, StDatabaseT *db)
{
    uint32_t preAllocSize = SE_CTRL_PAGE_POOL_INIT_SIZE;
    db->core.ctrlPageMgr.blockHwm = preAllocSize;
    StatusInter ret =
        SeCtrlPagePoolInit(seIns, db->memCtx, &db->ctrlPagePool, preAllocSize, SIZE_K(seIns->seConfig.ctrlPageSize));
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    // 预留ctrl页上没有内容，内存不用申请
    SeCtrlPagePoolUnloadPage(&db->ctrlPagePool, RESERVE_CTRL_PAGE_ID);
    return STATUS_OK_INTER;
}

static StatusInter DataBaseInitPubCtx(SeInstanceT *seIns)
{
    if (!DbIsShmPtrValid(seIns->coreCtrlCtxShm)) {
        seIns->db->pubCtx = (StDataBasePubCtxT *)DbWrappedMemCtxAllocAddr(
            seIns->memUtils.memCtx, sizeof(StDataBasePubCtxT), &seIns->coreCtrlCtxShm);
        if (seIns->db->pubCtx == NULL) {
            SE_ERROR(OUT_OF_MEMORY_MEM_FAILED, "Alloc core ctrl, size = %zu", sizeof(StDataBasePubCtxT));
            return OUT_OF_MEMORY_MEM_FAILED;
        }
        (void)memset_s(seIns->db->pubCtx, sizeof(StDataBasePubCtxT), 0, sizeof(StDataBasePubCtxT));
        seIns->db->pubCtx->pagesShm = DB_INVALID_SHMPTR;
        DbSpinInit(&seIns->db->pubCtx->ctrlLock);
        return STATUS_OK_INTER;
    }
    seIns->db->pubCtx = (StDataBasePubCtxT *)DbDynShmemPtrToAddr(seIns->coreCtrlCtxShm);
    if (seIns->db->pubCtx == NULL) {
        SE_ERROR(OUT_OF_MEMORY_MEM_FAILED, "Map core ctrl");
        return OUT_OF_MEMORY_MEM_FAILED;
    }
    SE_WARN(STATUS_OK_INTER, "Ctrl page already inited");
    return STATUS_OK_INTER;
}

StatusInter DatabaseMemInit(SeInstanceT *seIns)
{
    StDatabaseT *db = (StDatabaseT *)DbDynMemCtxAlloc(seIns->seServerMemCtx, sizeof(StDatabaseT));
    if (db == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_MEM_FAILED, "Alloc data buf size, size = %" PRIu32, (uint32_t)sizeof(StDatabaseT));
        return OUT_OF_MEMORY_MEM_FAILED;
    }
    (void)memset_s(db, sizeof(StDatabaseT), 0, sizeof(StDatabaseT));
    seIns->db = db;
    db->enable = true;
    // DB初始化时，赋值为一个随机值；DB重启时，继承文件头中的魔术字
    // 用于重启时，文件首尾的校验；也用于增量刷盘
    db->checkMagic = SeGetPersistMode() == PERSIST_ON_DEMAND ? DB_INVALID_ID64 : GenCheckMagic();
    // DB初始化时，赋值为ALL_FLUSH_INIT_MAGIC_NUM；DB重启时，继承文件头中的魔术字
    // 用于全量刷盘（增量或按需），每刷一次就加一，可校验全量刷盘的部分写场景
    db->flushCheckMagic = ALL_FLUSH_INIT_MAGIC_NUM;
    db->diskBlockSize = 4 * DB_KIBI;  // 设置系统默认文件对齐大小为4K
    DbRWLatchInit(&db->coreLock);
    db->memCtx = seIns->seServerMemCtx;
    SeInitMultiZoneCtrl(&db->zoneCtrl, seIns);
    // 先使用当前配置项pageSize，如果与core中的不一样，DB启动失败
    DbFileInit(&db->ctrlFile, SIZE_K(seIns->seConfig.ctrlPageSize), sizeof(PersCtrlFileHeadT), NULL);
    db->safeFile.handle = DB_INVALID_FD;

    StatusInter ret = DataBaseInitPubCtx(seIns);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "DataBaseInitPubCtx");
        goto EXIT;
    }

    ret = DbDatabaseMemInit(seIns, db);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "DbDatabaseMemInit");
        goto EXIT;
    }

    ret = DbAllocMemOnDemand(seIns, db);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }

    ret = SeCreateMemStructMap(seIns);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }

    return STATUS_OK_INTER;
EXIT:
    DbReleaseInitMem(seIns);
    return ret;
}

static StatusInter SeCtrlInitSetCorePageFreeSize(SeInstanceT *seIns)
{
    PageHeadT *page = NULL;
    StatusInter ret = SeGetCtrlPage(seIns->db, CORE_CTRL_PAGE_ID, (uint8_t **)&page);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    SePageHeadSetType((PageHeadT *)page, PERSISTENCE_PAGE_TYPE_CORE_CTRL);
    page->freeSize = (uint16_t)(SeGetCtrlBufSize(seIns->db) - sizeof(StCoreCtrlT));
    return STATUS_OK_INTER;
}

StatusInter SeCtrlInitImpl(SeInstanceT *seIns)
{
    StatusInter ret = DatabaseMemInit(seIns);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "DatabaseMemInit");
        return ret;
    }

    DbSetCoreCfg(seIns);

    ret = SeCtrlInitSetCorePageFreeSize(seIns);
    if (ret != STATUS_OK_INTER) {
        DbReleaseInitMem(seIns);
        return ret;
    }
    uint32_t offset = CORE_CTRL_PAGES;
    seIns->db->core.spaceCtrlEntry = offset++;
    seIns->db->core.fileCtrlEntry = offset++;
    seIns->db->core.devCtrlEntry = offset++;
    seIns->db->core.extendCtrlEntry = SE_INVALID_BLOCK_ID;
    DB_ASSERT(offset == seIns->db->core.ctrlPageMgr.blockHwm);
    return STATUS_OK_INTER;
}

// 这里传进的path非空，是默认路径，或用户传入的路径
StatusInter SeCtrlFileOpen(SeInstanceT *seIns, const char *path, int32_t *ctrlFileHandle)
{
    DB_POINTER2(seIns, path);
    char filePath[DB_MAX_WHOLE_PATH] = {0};
    // 这里先使用内存态的配置项，如果与core中的不一致，文件将无法打开
    StatusInter ret = SeFileConstructPath(path, seIns->seConfig.ctrlFileName, filePath, sizeof(filePath));
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Generate file path when open, filename(%s), path(%s).", seIns->seConfig.ctrlFileName, path);
        return ret;
    }
    if (!DbFileExist(filePath)) {
        return NO_DATA_FILE_NOT_EXIST;
    }
    ret = SeFileOpen(filePath, 0, ctrlFileHandle);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Open ctrl path when verify, filePath(%s), handle(%" PRId32 ").", filePath, *ctrlFileHandle);
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter DbFlushCtrlFile(SeInstanceT *seIns, uint32_t zoneId)
{
    DB_POINTER(seIns);

    StatusInter ret;
    DbStoreCore(seIns, seIns->db);
    for (uint32_t i = SeGetCoreCtrlPageId(seIns); i < seIns->db->core.ctrlPageMgr.blockHwm; i++) {
        ret = DbSaveCtrlPage(seIns, i, zoneId);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }

    // write file head
    ret = SpcWriteCtrlFileHead(seIns, seIns->db->ctrlFile.handle[zoneId], seIns->db->flushCheckMagic);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Write ctrl file head, zoneId (%" PRIu32 "), handle(%" PRIu32 "), check magic(%" PRIu64 ")",
            zoneId, seIns->db->ctrlFile.handle[zoneId], seIns->db->flushCheckMagic);
        return ret;
    }

    CRASHPOINT(DB_CRASH_EVENT_DEMAND_FLUSH, DB_CRASH_STATUS_PERSIST_DEMAND_FLUSH_CTRLFILE);

    // write file tail
    uint64_t tailOffset = SeGetCtrlFilePageOffset(&seIns->db->ctrlFile, seIns->db->core.ctrlPageMgr.blockHwm);
    ret = SpcWriteFileTail(seIns, seIns->db->ctrlFile.handle[zoneId], tailOffset, seIns->db->flushCheckMagic);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Write ctrl file head, zoneId (%" PRIu32 "), handle(%" PRIu32 "), check magic(%" PRIu64 ")",
            zoneId, seIns->db->ctrlFile.handle[zoneId], seIns->db->flushCheckMagic);
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter DbBuildCtrlFilesPersIncrement(SeInstanceT *seIns, uint32_t zoneId)
{
    StDatabaseT *db = seIns->db;
    const SeConfigT *cfg = &seIns->seConfig;
    char filePath[DB_MAX_WHOLE_PATH];
    StatusInter ret = SeFileConstructPath(cfg->dataFileDirPath[zoneId], cfg->ctrlFileName, filePath, DB_MAX_WHOLE_PATH);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    ret = SeFileOpenCreate(filePath, SeGetFileOpExtraFlag(seIns), &db->ctrlFile.handle[zoneId]);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Create ctrlfile, path %s", filePath);
        return ret;
    }

    SpcWriteCtrlFileHead(seIns, db->ctrlFile.handle[zoneId], db->checkMagic);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Write ctrlfile head, path %s, handle (%" PRIu32 ")", filePath, db->ctrlFile.handle[zoneId]);
        return ret;
    }

    DbStoreCore(seIns, seIns->db);
    for (uint32_t i = SeGetCoreCtrlPageId(seIns); i < db->core.ctrlPageMgr.blockHwm; i++) {
        ret = DbSaveCtrlPage(seIns, i, zoneId);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Save ctrl page, path %s, page index (%" PRIu32 ")", filePath, i);
            return ret;
        }
    }

    int64_t offset = SeGetCtrlFilePageOffset(&db->ctrlFile, db->core.ctrlPageMgr.blockHwm);
    ret = SpcWriteFileTail(seIns, db->ctrlFile.handle[zoneId], offset, db->checkMagic);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Write ctrlfile head, path %s, handle (%" PRIu32 ")", filePath, db->ctrlFile.handle[zoneId]);
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter DbBuildCtrlFiles(SeInstanceT *seIns, const char *ctrlFiles)
{
    DB_POINTER(seIns);
    StDatabaseT *db = seIns->db;

    db->core.truncPoint = RedoGenInitPoint();
    db->core.lrpPoint = RedoGenInitPoint();
    db->core.version = GMDB_RD_CTRL_VERSION;
    db->core.fileFormatVersion = SeDataBaseGetCurrentFileFormatVersion();
    db->core.isEncrypted = 0;
    // 第一次启动
#ifdef NDEBUG
    db->core.isDebug = false;
#else
    db->core.isDebug = true;
#endif
    DbStoreCore(seIns, db);

    if (SeGetPersistMode() == PERSIST_INCREMENT) {
        for (uint32_t i = 0; i < seIns->seConfig.multiZoneNum; i++) {
            StatusInter ret = DbBuildCtrlFilesPersIncrement(seIns, i);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
        }
    }

    return STATUS_OK_INTER;
}

// 给space额外添加一个文件，当前仅dumem和bufferpool共存场景使用
static StatusInter SpaceAddDumemFile(SeInstanceT *seIns, uint32_t spaceId)
{
    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, spaceId);
    if (space == NULL) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "space not exist, id %" PRIu32, spaceId);
        return DATA_EXCEPTION_INTER;
    }
    SeRunCtxT tmpCtx = {0};
    tmpCtx.seIns = seIns;

    // 给space多添加一个文件用于索引数据
    StatusInter ret = SeSpaceAddDefaultDataFileImpl(&tmpCtx, space);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Space(%s) add data file", space->ctrl->cfg.name);
        return ret;
    }

    uint32_t fileId = *(uint32_t *)SpaceFileListGet(space, 0);
    DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, fileId);
    if (file == NULL) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "file not exist, id %" PRIu32, fileId);
        return DATA_EXCEPTION_INTER;
    }
    ret = SeSetDbFileType(seIns, file, BUFFER_POOL_FILE);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    fileId = *(uint32_t *)SpaceFileListGet(space, 1);
    file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, fileId);
    if (file == NULL) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "file not exist, id %" PRIu32, fileId);
        return DATA_EXCEPTION_INTER;
    }
    return SeSetDbFileType(seIns, file, DURABLE_MEM_FILE);
}

// 双形态模式设置space第一个datafile文件类型
static StatusInter SetSpaceFileWithTypeOnCoMode(SeInstanceT *seIns, uint32_t spaceId, uint8_t fileType)
{
    if (SeIsBufpoolAndDumemCoexist(seIns)) {
        SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, spaceId);
        if (space == NULL) {
            SE_LAST_ERROR(DATA_EXCEPTION_INTER, "space not exist, id %" PRIu32, spaceId);
            return DATA_EXCEPTION_INTER;
        }
        uint32_t fileId = *(uint32_t *)SpaceFileListGet(space, 0);
        DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, fileId);
        if (file == NULL) {
            SE_LAST_ERROR(DATA_EXCEPTION_INTER, "file not exist, id %" PRIu32, fileId);
            return DATA_EXCEPTION_INTER;
        }
        return SeSetDbFileType(seIns, file, fileType);
    }

    return STATUS_OK_INTER;
}

static StatusInter DbBuildSpace(SeInstanceT *seIns, const DatabaseConfT *def)
{
    StDatabaseT *db = seIns->db;
    StCoreCtrlT *coreCtrl = &db->core;
    StatusInter ret = SpaceCreate(seIns, NULL, &def->systemSpcConf, &coreCtrl->systemSpaceId);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }
    ret = SetSpaceFileWithTypeOnCoMode(seIns, coreCtrl->systemSpaceId, BUFFER_POOL_FILE);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }

    ret = SpaceCreate(seIns, NULL, &def->undoSpcConf, &coreCtrl->undoSpaceId);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }
    ret = SetSpaceFileWithTypeOnCoMode(seIns, coreCtrl->undoSpaceId, BUFFER_POOL_FILE);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }
    DB_ASSERT(coreCtrl->undoSpaceId == DB_UNDO_TABLE_SPACE_INDEX);

    ret = SpaceCreate(seIns, NULL, &def->userSpcConf, &coreCtrl->userSpaceId);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }
    if (SeIsBufpoolAndDumemCoexist(seIns)) {
        // 添加文件和赋标签
        ret = SpaceAddDumemFile(seIns, coreCtrl->userSpaceId);
        if (ret != STATUS_OK_INTER) {
            goto EXIT;
        }
    }

    return DbSaveCore(seIns);
EXIT:
    SE_ERROR(ret, "Default space create");
    return ret;
}

static Status CreateNoExistDbCtrlFileDirByZoneId(SeInstanceT *seIns, uint32_t zoneId)
{
    if (!DbDirExist(seIns->seConfig.ctrlFileDirPath[zoneId])) {
        Status ret = DbMakeDirWithGRPRXPermission(seIns->seConfig.ctrlFileDirPath[zoneId]);
        SeFileAlarmUpdate(ret);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(
                ret, "make dir, path(%s), zone id(%" PRIu32 ")", seIns->seConfig.dataFileDirPath[zoneId], zoneId);
            return ret;
        }
    }
    return GMERR_OK;
}

StatusInter MultiZoneCtrlfilesPrepare(SeInstanceT *seIns)
{
    // 初始化多分区路径句柄
    StatusInter ret = STATUS_OK_INTER;
    char filePath[DB_MAX_WHOLE_PATH];
    const SeConfigT *cfg = &seIns->seConfig;
    StDatabaseT *db = seIns->db;
    for (uint32_t i = 0; i < seIns->seConfig.multiZoneNum; i++) {
        ret = SeFileConstructPath(cfg->dataFileDirPath[i], cfg->ctrlFileName, filePath, DB_MAX_WHOLE_PATH);
        if (ret != STATUS_OK_INTER) {
            goto EXIT;
        }
        if (DbFileExist(filePath)) {
            ret = SeFileOpen(filePath, SeGetFileOpExtraFlag(seIns), &db->ctrlFile.handle[i]);
        } else {
            ret = DbGetInternalErrno(CreateNoExistDbCtrlFileDirByZoneId(seIns, i));
            if (ret != STATUS_OK_INTER) {
                goto EXIT;
            }
            ret = SeFileCreate(filePath, SeGetFileOpExtraFlag(seIns), &db->ctrlFile.handle[i]);
        }
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "DbOpenDevice (%s)", filePath);
            goto EXIT;
        }
    }
EXIT:
    if (ret != STATUS_OK_INTER) {
        SeCloseCtrlFile((uint8_t *)&db->ctrlFile, NULL);
        return ret;
    }
    return STATUS_OK_INTER;
}

static void DbInitCtrlPoint(SeInstanceT *seIns)
{
    if (!SeGetNormalShutDown(seIns)) {
        return;
    }
    StDatabaseT *db = seIns->db;
    db->core.truncPoint = RedoGenInitPoint();
    db->core.lrpPoint = RedoGenInitPoint();
}

static StatusInter ArrayPageToLinkPage(SeInstanceT *seIns, uint32_t *startId, uint32_t endId)
{
    if (*startId == endId) {
        // 只有一个ctrl页，不用转换
        return STATUS_OK_INTER;
    }
    PageHeadT *curCtrlPage = NULL;
    PageIdT nextPageId = SE_INVALID_PAGE_ADDR;
    StatusInter ret = STATUS_OK_INTER;
    for (uint32_t i = *startId; i <= endId; i++) {
        ret = SeGetCtrlPage(seIns->db, i, (uint8_t **)&curCtrlPage);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Get ctrl page when upgrade.");
            return ret;
        }
        curCtrlPage->nextPageId = nextPageId;
        nextPageId.blockId = i;
    }
    *startId = nextPageId.blockId;
    return STATUS_OK_INTER;
}

static StatusInter UpgradeCtrlFileLinkCtrl(SeInstanceT *seIns)
{
    if (seIns->db->core.fileFormatVersion != SE_ARRAY_CTRL_PAGE_FILE_FORMAT_VERSION) {
        return STATUS_OK_INTER;
    }

    StatusInter ret = ArrayPageToLinkPage(seIns, &seIns->db->core.spaceCtrlEntry, seIns->db->core.fileCtrlEntry - 1);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Upgrade space ctrl pages, ctrlEntry(%" PRIu32 ")", seIns->db->core.spaceCtrlEntry);
        return ret;
    }

    ret = ArrayPageToLinkPage(seIns, &seIns->db->core.fileCtrlEntry, seIns->db->core.devCtrlEntry - 1);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Upgrade file ctrl pages, ctrlEntry(%" PRIu32 ")", seIns->db->core.fileCtrlEntry);
        return ret;
    }

    ret = ArrayPageToLinkPage(seIns, &seIns->db->core.devCtrlEntry, seIns->db->core.ctrlPageMgr.blockHwm - 1);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Upgrade device ctrl pages, ctrlEntry(%" PRIu32 ")", seIns->db->core.devCtrlEntry);
        return ret;
    }

    // 成功升级到版本4:ctrl页由数组升级为链表
    seIns->db->core.fileFormatVersion = SE_LINK_CTRL_PAGE_FILE_FORMAT_VERSION;
    return STATUS_OK_INTER;
}

/*
旧版本:每个space只有一个文件，space head entry 存在 space ctrl中
新版本:每个space可以支持多个文件，取消space head，原先的space head entry需要转存到文件ctrl 中
注意:该更新函数依赖spaceGroup和fileGroup，需要在他们初始化后调用
*/
StatusInter UpgradeFileHeadEntry(SeInstanceT *seIns)
{
    if (seIns->db->core.fileFormatVersion != SE_LINK_CTRL_PAGE_FILE_FORMAT_VERSION) {
        return STATUS_OK_INTER;
    }

    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->fileGroup); i++) {
        DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, i);
        if (!DbFileIsOnline(file->ctrl)) {
            continue;
        }
        uint32_t spaceId = file->ctrl->spaceId;
        SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, spaceId);
        if (space == NULL) {
            SE_LAST_ERROR(DATA_EXCEPTION_INTER, "space not exist, id %" PRIu32, spaceId);
            return DATA_EXCEPTION_INTER;
        }
        file->ctrl->entry = space->ctrl->entry;
        DB_LOG_INFO("upgrade file id = %" PRIu32 ", entry(%" PRIu32 ",%" PRIu32 ")", file->ctrl->id,
            file->ctrl->entry.deviceId, file->ctrl->entry.blockId);
    }

    // 成功升级到版本5:文件头head pageId由文件ctrl保存
    seIns->db->core.fileFormatVersion = SE_SPACE_CTRL_MULTI_FILE_FORMAT_VERSION;
    DB_LOG_INFO("UpgradeCtrlFile to version %" PRIu32 " success.", seIns->db->core.fileFormatVersion);
    return STATUS_OK_INTER;
}

StatusInter LoopUpgradeCtrlPageType(SeInstanceT *seIns, uint32_t cursor, PersistencePageTypeE type)
{
    PageHeadT *page = NULL;
    StatusInter ret = STATUS_OK_INTER;
    uint32_t loopCursor = cursor;
    while (loopCursor != SE_INVALID_BLOCK_ID) {
        ret = SeGetCtrlPage(seIns->db, loopCursor, (uint8_t **)&page);
        if (ret != STATUS_OK_INTER) {
            SE_LAST_ERROR(ret, "Get CtrlPage (%" PRIu32 ")", loopCursor);
            return ret;
        }
        SePageHeadSetType(page, type);
        loopCursor = page->nextPageId.blockId;
    }
    return STATUS_OK_INTER;
}

StatusInter UpgradeCtrlPageType(SeInstanceT *seIns)
{
    if (seIns->db->core.fileFormatVersion != SE_SPACE_CTRL_MULTI_FILE_FORMAT_VERSION) {
        return STATUS_OK_INTER;
    }
    StatusInter ret = LoopUpgradeCtrlPageType(seIns, seIns->db->core.spaceCtrlEntry, PERSISTENCE_PAGE_TYPE_SPACE_CTRL);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "Upgrade Space Ctrl Page Type");
        return ret;
    }
    ret = LoopUpgradeCtrlPageType(seIns, seIns->db->core.fileCtrlEntry, PERSISTENCE_PAGE_TYPE_FILE_CTRL);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "Upgrade File Ctrl Page Type");
        return ret;
    }
    ret = LoopUpgradeCtrlPageType(seIns, seIns->db->core.devCtrlEntry, PERSISTENCE_PAGE_TYPE_DEVICE_CTRL);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "Upgrade Device Ctrl Page Type");
        return ret;
    }
    seIns->db->core.extendCtrlEntry = SE_INVALID_BLOCK_ID;
    // 成功升级到版本6:支持extendCtrlPage
    seIns->db->core.fileFormatVersion = SE_EXTEND_CTRL_PAGE_FILE_FORMAT_VERSION;
    DB_LOG_INFO("UpgradeCtrlFile to version %" PRIu32 " success.", seIns->db->core.fileFormatVersion);
    return STATUS_OK_INTER;
}

StatusInter UpgradeCtrlFile(SeInstanceT *seIns)
{
    // 后续ctrl升级写到这里
    return UpgradeCtrlFileLinkCtrl(seIns);
}

static StatusInter DbMapCtrls(SeInstanceT *seIns)
{
    StatusInter ret = SeLoadSpaceCtrl(seIns);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Load space ctrl to group.");
        return ret;
    }
    ret = SeLoadFileCtrl(seIns);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Load file ctrl to group.");
        return ret;
    }
    ret = SeLoadDevCtrl(seIns);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Load device ctrl to group.");
        return ret;
    }
    // 光启兼容旧版本持久化文件
    ret = UpgradeFileHeadEntry(seIns);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Upgrade ctrl file to version %" PRIu32 ".", SE_SPACE_CTRL_MULTI_FILE_FORMAT_VERSION);
        return ret;
    }

    ret = UpgradeCtrlPageType(seIns);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    ret = SeLoadExtendCtrl(seIns);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Load extend ctrl to group.");
        return ret;
    }
    return STATUS_OK_INTER;
}

static StatusInter DbExecLoadCtrlFile(SeInstanceT *seIns, const char *filePath, uint32_t zoneId, bool isReload)
{
    StDatabaseT *db = seIns->db;
    StatusInter ret = STATUS_OK_INTER;
    if ((ret = SpcGetCheckMagic(seIns, db->ctrlFile.handle[zoneId])) != STATUS_OK_INTER) {
        SE_ERROR(ret, "Get check magic from ctrlfile(%s)", filePath);
        return ret;
    }

    if ((ret = DbLoadCore(seIns, zoneId)) != STATUS_OK_INTER) {
        SE_ERROR(ret, "DbLoadCore (%s)", filePath);
        return ret;
    }

    int64_t offset = SeGetCtrlFilePageOffset(&db->ctrlFile, db->core.ctrlPageMgr.blockHwm);
    if ((ret = SpcVerifyFileTail(seIns, db->ctrlFile.handle[zoneId], offset)) != STATUS_OK_INTER) {
        SE_ERROR(ret, "Verify ctrlfile(%s) tail", filePath);
        return ret;
    }

    if (!db->isCtrlPageInited || isReload) {
        if ((ret = DbLoadCtrlPages(seIns, zoneId)) != STATUS_OK_INTER) {
            SE_ERROR(ret, "DbLoadCtrlPages (%s)", filePath);
            return ret;
        }
    }

    // 光启兼容旧版本持久化文件
    ret = UpgradeCtrlFile(seIns);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Update ctrl file.");
        return ret;
    }

    if ((ret = DbMapCtrls(seIns)) != STATUS_OK_INTER) {
        SE_ERROR(ret, "Map ctrls");
        return ret;
    }

    return DbCheckMemCtrlDigest(seIns);
}

StatusInter SeCtrlRecoveryImpl(SeInstanceT *seInstance)
{
    DbSpinInit(&seInstance->db->pubCtx->ctrlLock);
    return DbLoadCtrlFileImpl(seInstance, true);
}

static StatusInter DbLoadCtrlfileInner(SeInstanceT *seIns, const char *filePath, uint32_t zoneId, bool isReload)
{
    StDatabaseT *db = seIns->db;
    StatusInter ret = SeFileOpen(filePath, SeGetFileOpExtraFlag(seIns), &db->ctrlFile.handle[zoneId]);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Open ctrl file (%s)", filePath);
        return ret;
    }
    ret = DbExecLoadCtrlFile(seIns, filePath, zoneId, isReload);
    SeCloseCtrlFileByZoneId(&db->ctrlFile, zoneId);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    if (SeGetPersistMode() == PERSIST_INCREMENT) {
        if ((ret = MultiZoneCtrlfilesPrepare(seIns)) != STATUS_OK_INTER) {
            return ret;
        }
    }
    DbInitCtrlPoint(seIns);
    return STATUS_OK_INTER;
}

static StatusInter DbCheckSystemUndoSpace(SeInstanceT *seIns, StCoreCtrlT *core)
{
    StatusInter ret = STATUS_OK_INTER;
    SpaceCfgT cfg = {0};
    uint64_t maxSize = (DB_INVALID_UINT64 / SIZE_K(seIns->seConfig.deviceSize)) * SIZE_K(seIns->seConfig.deviceSize);
    uint32_t spaceId = core->undoSpaceId;
    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, spaceId);
    if (space == NULL || !space->ctrl->used) {
        StorageStatusE seStatus = SeGetStorageStatus(seIns);
        if (seStatus == SE_ON_DISK_BACK_UP_SCHEMA) {
            InitSpaceCfg(seIns, maxSize, SPACE_TYPE_UNDO, seIns->seConfig.undoSpaceFileName, &cfg);
            ret = SpaceCreate(seIns, NULL, &cfg, &core->undoSpaceId);
            if (ret != STATUS_OK_INTER) {
                SE_LAST_ERROR(ret, "Db (%" PRIu32 ") rebuild space(%" PRIu32 ").", seStatus, spaceId);
                return ret;
            }
            DB_ASSERT(core->undoSpaceId == DB_UNDO_TABLE_SPACE_INDEX);
        } else {
            SE_LAST_ERROR(
                INTERNAL_ERROR_INTER, "Db (%" PRIu32 ") required space(%" PRIu32 ") exist.", seStatus, spaceId);
            return INTERNAL_ERROR_INTER;
        }
    }
    return STATUS_OK_INTER;
}

static StatusInter DbCheckSystemUserDefaultSpace(SeInstanceT *seIns, StCoreCtrlT *core)
{
    StatusInter ret = STATUS_OK_INTER;
    SpaceCfgT cfg = {0};
    uint64_t maxSize = (DB_INVALID_UINT64 / SIZE_K(seIns->seConfig.deviceSize)) * SIZE_K(seIns->seConfig.deviceSize);
    uint32_t spaceId = core->userSpaceId;
    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, spaceId);
    if (space == NULL || !space->ctrl->used) {
        StorageStatusE seStatus = SeGetStorageStatus(seIns);
        if (seStatus == SE_ON_DISK_BACK_UP_SCHEMA) {
            InitSpaceCfg(seIns, maxSize, SPACE_TYPE_USER_DEFAULT, seIns->seConfig.userSpaceFileName, &cfg);
            ret = SpaceCreate(seIns, NULL, &cfg, &core->userSpaceId);
            if (ret != STATUS_OK_INTER) {
                SE_LAST_ERROR(ret, "Db (%" PRIu32 ") rebuild space(%" PRIu32 ").", seStatus, spaceId);
                return ret;
            }
        } else {
            SE_LAST_ERROR(
                INTERNAL_ERROR_INTER, "Db (%" PRIu32 ") required space(%" PRIu32 ") exist.", seStatus, spaceId);
            return INTERNAL_ERROR_INTER;
        }
    }
    return STATUS_OK_INTER;
}

static StatusInter DbCheckSystemRequiredSpace(SeInstanceT *seIns)
{
    StCoreCtrlT *core = &seIns->db->core;
    uint32_t spaceId = core->systemSpaceId;
    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, spaceId);
    if (space == NULL || !space->ctrl->used) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER, "Db (%" PRIu32 ") required space(%" PRIu32 ") exist.",
            SeGetStorageStatus(seIns), spaceId);
        return INTERNAL_ERROR_INTER;
    }

    StatusInter ret = DbCheckSystemUndoSpace(seIns, core);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    return DbCheckSystemUserDefaultSpace(seIns, core);
}

StatusInter DbLoadSpace(SeInstanceT *seIns)
{
    return DbCheckSystemRequiredSpace(seIns);
}

Status CreateDbDir(SeInstanceT *seInstance)
{
    Status ret;
    for (uint32_t i = 0; i < seInstance->seConfig.multiZoneNum; i++) {
        if (!DbDirExist(seInstance->seConfig.dataFileDirPath[i])) {
            ret = DbMakeDirWithGRPRXPermission(seInstance->seConfig.dataFileDirPath[i]);
            SeFileAlarmUpdate(ret);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(
                    ret, "Make dir, path(%s), zone id(%" PRIu32 ")", seInstance->seConfig.dataFileDirPath[i], i);
                return ret;
            }
        }

        ret = CreateNoExistDbCtrlFileDirByZoneId(seInstance, i);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static uint32_t GetCtrlFileLens(SeInstanceT *seInstance)
{
    uint32_t size = 0;
    const SeConfigT *seConfig = &seInstance->seConfig;
    uint32_t ctrlFileNameLen = strlen(seConfig->ctrlFileName);
    for (uint32_t i = 0; i < seConfig->multiZoneNum; ++i) {
        uint32_t pathLen = 0;
        if (seConfig->ctrlFileDirPath[i] != NULL) {
            pathLen = strlen(seConfig->ctrlFileDirPath[i]);
        }
        size += (pathLen + ctrlFileNameLen + 1);
    }
    return size;
}

static Status GetCtrlFiles(SeInstanceT *seInstance, char **ctrlFiles)
{
    const SeConfigT *seConfig = &seInstance->seConfig;
    uint32_t size = GetCtrlFileLens(seInstance);
    char *files = (char *)DbDynMemCtxAlloc(seInstance->seServerMemCtx, size);
    if (files == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "Alloc mem when get ctrl file, len (%" PRIu32 ")", size);
        return GMERR_OUT_OF_MEMORY;
    }

    char *file = files;
    uint32_t ctrlFileNameLen = strlen(seConfig->ctrlFileName);
    for (uint32_t i = 0; i < seConfig->multiZoneNum; i++) {
        uint32_t pathLen = 0;
        const char *path = "";
        if (seConfig->ctrlFileDirPath[i] != NULL) {
            pathLen = strlen(seConfig->ctrlFileDirPath[i]);
            path = seConfig->ctrlFileDirPath[i];
        }
        uint32_t fileNameLen = pathLen + ctrlFileNameLen + 1;
        int32_t printSize = sprintf_s(file, fileNameLen, "%s%s", path, seConfig->ctrlFileName);
        if (printSize <= 0) {
            SE_LAST_ERROR(INT_ERR_SECUREC_MEMORY_COPY_FAIL, "sprintf, (%s, %s).", path, seConfig->ctrlFileName);
            DbDynMemCtxFree(seInstance->seServerMemCtx, files);
            return GMERR_INTERNAL_ERROR;
        }
        file += fileNameLen;
    }

    *ctrlFiles = files;
    return GMERR_OK;
}

static Status InitCtrlFiles(SeInstanceT *seInstance, char **ctrlFiles)
{
    Status ret = GetCtrlFiles(seInstance, ctrlFiles);
    if (ret != GMERR_OK) {
        return ret;
    }

    StatusInter innerRet = DbBuildCtrlFiles(seInstance, *ctrlFiles);
    if (innerRet != STATUS_OK_INTER) {
        SE_ERROR(innerRet, "DbBuildCtrlFiles");
        return DbGetExternalErrno(innerRet);
    }
    return GMERR_OK;
}

static void ReleaseDatabaseConf(SeInstanceT *seIns, DatabaseConfT *cfg)
{
    if (cfg->ctrlFiles != NULL) {
        DbDynMemCtxFree(seIns->seServerMemCtx, cfg->ctrlFiles);
        cfg->ctrlFiles = NULL;
    }
}

Status SeDatabaseInit(SeInstanceT *seInstance)
{
    DatabaseConfT cfg = {0};

    Status ret = DbGetExternalErrno(VfdMgrConstructor(seInstance));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init vfd mgr");
        return ret;
    }

    if (SeGetPersistMode() == PERSIST_INCREMENT) {
        ret = CreateDbDir(seInstance);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    ret = SeCreateSafeFile(seInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DbInitSafeFile");
        return ret;
    }

    ret = InitCtrlFiles(seInstance, &cfg.ctrlFiles);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DbInitCtrlFiles");
        return ret;
    }

    uint64_t devSize = seInstance->seConfig.deviceSize * SIZE_K(1);
    DB_ASSERT(devSize != 0);
    uint64_t maxSize = (DB_INVALID_UINT64 / devSize) * devSize;
    InitSpaceCfg(seInstance, maxSize, SPACE_TYPE_SYSTEM, seInstance->seConfig.systemSpaceFileName, &cfg.systemSpcConf);
    InitSpaceCfg(seInstance, maxSize, SPACE_TYPE_UNDO, seInstance->seConfig.undoSpaceFileName, &cfg.undoSpcConf);
    InitSpaceCfg(
        seInstance, maxSize, SPACE_TYPE_USER_DEFAULT, seInstance->seConfig.userSpaceFileName, &cfg.userSpcConf);

    StatusInter innerRet = STATUS_OK_INTER;
    innerRet = DbBuildSpace(seInstance, &cfg);
    if (innerRet != STATUS_OK_INTER) {
        SE_ERROR(innerRet, "DbBuildSpace");
        return DbGetExternalErrno(innerRet);
    }

    // 异常场景依赖memCtx统一释放
    ReleaseDatabaseConf(seInstance, &cfg);
    return GMERR_OK;
}

static inline StatusInter MultiZoneDataFileIsConsist(SeInstanceT *seIns, bool isZonePathConsist, bool isZoneDataConsist)
{
    if (seIns->storageType == SE_DURABLE_MEMDATA || isZonePathConsist || isZoneDataConsist) {
        return STATUS_OK_INTER;
    }
    return DATA_EXCEPTION_INTER;
}

static StatusInter MultiZoneDataFileOperate(
    SeInstanceT *seIns, const char *filePath, DbFileT *df, uint32_t zoneId, bool *isZoneDataConsist)
{
#ifdef FEATURE_TS
    if (df->handle[DB_RESERVE_HANDLE_INDEX] == DB_INVALID_FD) {
        return STATUS_OK_INTER;
    }
#endif
    StatusInter ret = STATUS_OK_INTER;
    if (DbFileExist(filePath)) {
        ret = VfdFileOpen(seIns->vfdMgr, filePath, SeGetFileOpExtraFlag(seIns), &df->handle[zoneId]);
        *isZoneDataConsist = false;
    } else {
        ret = VfdFileCreate(seIns->vfdMgr, filePath, SeGetFileOpExtraFlag(seIns), &df->handle[zoneId]);
    }
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Open data file, file path %s", filePath);
    }
    return ret;
}

static StatusInter DataFilePrepareByZoneId(
    SeInstanceT *seIns, uint32_t zoneId, bool *isZonePathConsist, bool *isZoneDataConsist)
{
    StatusInter ret = STATUS_OK_INTER;
    if (!DbDirExist(seIns->seConfig.dataFileDirPath[zoneId])) {
        ret = DbGetStatusInterErrno(DbMakeDirWithGRPRXPermission(seIns->seConfig.dataFileDirPath[zoneId]));
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->fileGroup); i++) {
        DbFileT *df = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, i);
        if (!DbFileIsOnline(df->ctrl)) {
            continue;
        }
        char filePath[DB_MAX_WHOLE_PATH] = {0};
        const char *realPath = seIns->seConfig.dataFileDirPath[zoneId];
        *isZonePathConsist = (*isZonePathConsist || DbStrCmp(seIns->seConfig.recoveryPath, realPath, false) == 0);
        errno_t err = snprintf_s(filePath, DB_MAX_WHOLE_PATH, DB_MAX_WHOLE_PATH - 1, "%s%s", realPath, df->ctrl->name);
#if defined(FEATURE_TS)
        SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, df->ctrl->spaceId);
        if (space == NULL) {
            SE_LAST_ERROR(DATA_EXCEPTION_INTER, "space not exist, id %" PRIu32, df->ctrl->spaceId);
            return DATA_EXCEPTION_INTER;
        }
        if (space->ctrl->cfg.isCustomFilePath) {
            (void)memset_s(filePath, DB_MAX_WHOLE_PATH, 0, DB_MAX_WHOLE_PATH);
            err = snprintf_s(filePath, DB_MAX_WHOLE_PATH, DB_MAX_WHOLE_PATH - 1, "%s%s",
                space->ctrl->cfg.customFilePath, df->ctrl->name);
        }
#endif
#if defined(IDS_HAOTIAN)
        if (df->ctrl->customDir) {
            (void)memset_s(filePath, DB_MAX_WHOLE_PATH, 0, DB_MAX_WHOLE_PATH);
            err = snprintf_s(filePath, DB_MAX_WHOLE_PATH, DB_MAX_WHOLE_PATH - 1, "%s%s", df->ctrl->dir, df->ctrl->name);
        }
#endif
        if (err < 0) {
            SE_LAST_ERROR(DATA_EXCEPTION_INTER, "Prepare data file, path %s too long", realPath);
            ret = DATA_EXCEPTION_INTER;
            return ret;
        }
        ret = MultiZoneDataFileOperate(seIns, filePath, df, zoneId, isZoneDataConsist);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }

    return STATUS_OK_INTER;
}

StatusInter MultiZoneDataFilePrepare(SeInstanceT *seIns)
{
    StatusInter ret = STATUS_OK_INTER;
    bool isZonePathConsist = SeIsStringEmpty(seIns->seConfig.recoveryPath);
    bool isZoneDataConsist = true;
    for (uint32_t zoneId = 0; zoneId < seIns->seConfig.multiZoneNum; zoneId++) {
        ret = DataFilePrepareByZoneId(seIns, zoneId, &isZonePathConsist, &isZoneDataConsist);
        if (ret != STATUS_OK_INTER) {
            break;
        }
    }
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    if ((ret = MultiZoneDataFileIsConsist(seIns, isZonePathConsist, isZoneDataConsist)) != STATUS_OK_INTER) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "Datafile specific another recovery zone.");
    }
    return ret;
}

static inline StatusInter StartCompressMultiThreads(SeInstanceT *seIns)
{
    if (StartCompressThreadsCondition(seIns)) {
        return SeVirtualDiskFullCompressInit(seIns);
    }
    return STATUS_OK_INTER;
}

static inline StatusInter EndCompressMultiThreads(SeInstanceT *seIns)
{
    if (StartCompressThreadsCondition(seIns)) {
        return SeVirtualDiskFullCompressStart(seIns);
    }
    return STATUS_OK_INTER;
}

static StatusInter LoadDataFile(SeInstanceT *seIns)
{
    if (SeIsBufpoolAndDumemCoexist(seIns) ||
        (seIns->storageType == SE_BUFFER_POOL && !SpaceCompressionIsEnable(seIns))) {
        // dumemdata和bufpool共存场景 以及 bufpool不开启压缩区情况下，不执行剩余逻辑
        return STATUS_OK_INTER;
    }
    StatusInter ret = StartCompressMultiThreads(seIns);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    uint32_t zoneId = SeIsBufpoolAndDumemCoexist(seIns) ? SeGetMainZoneId(seIns) : DB_RESERVE_HANDLE_INDEX;
    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->fileGroup); i++) {
        DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, i);
        // handle无效表示磁盘文件不存在，可能是文件丢了，或者是create/drop
        // Space过程中掉电导致的数据不一致，需要在undo后校验
        if (!DbFileIsOnline(file->ctrl) || file->handle[zoneId] == DB_INVALID_FD) {
            continue;
        }
        ret = SeLoadDbFile(seIns, file);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    return EndCompressMultiThreads(seIns);
}

// 重启过程中，需遍历所有deivce，将对应的deviceId从devMgr中的free device list上摘除
// 保证重启后devMgr的free device list的正确性（例如firstFreeDevId的正确性）
static StatusInter DbAllocFileDeviceById(SeInstanceT *seIns)
{
    StatusInter ret = STATUS_OK_INTER;
    DbDeviceT *device = NULL;
    DbFileT *file = NULL;
    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(seIns->devMgrShm);
    if (devMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get device mgr, shm(%" PRIu32 ", %" PRIu32 ")",
            seIns->devMgrShm.offset, seIns->devMgrShm.segId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->deviceGroup); ++i) {
        device = (DbDeviceT *)CtrlGroupGetItem(&seIns->db->deviceGroup, i);
        file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, device->ctrl->fileId);
        if (file == NULL) {
            SE_LAST_ERROR(DATA_EXCEPTION_INTER, "file not exist, id %" PRIu32, device->ctrl->fileId);
            return DATA_EXCEPTION_INTER;
        }

        ret = DevAllocDeviceById(devMgr, device->ctrl->id, false);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Alloc device by id (%" PRIu32 ")", device->ctrl->id);
            break;
        }
    }
    return ret;
}

static Status SeRemoveDeviceIdFromFreeList(SeInstanceHdT seIns)
{
    // 只在重启流程中才需要将已经使用的deviceId从free list中摘除
    if (SeGetStorageStatus(seIns) == SE_ON_DISK_CREATE) {
        return GMERR_OK;
    }

    StatusInter ret = DbAllocFileDeviceById(seIns);
    if (ret != STATUS_OK_INTER) {
        return DbGetExternalErrno(ret);
    }
    return GMERR_OK;
}

Status SeProcedureAfterRecoveryImpl(DbInstanceHdT dbInstance, SeInstanceHdT seIns)
{
    // dumemdata和bufferpool共存模式下，在recover完成redo回放后，需触发全量刷盘并reset bufferpool，load dumem文件
    Status ret = GMERR_OK;
    bool isCoexist = SeIsBufpoolAndDumemCoexist(seIns);
    if (isCoexist) {
        DB_LOG_INFO("begin trigger ckpt");
        ret = CkptTrigger(seIns, CKPT_MODE_BOOT_FULL, true, WAIT_MSECONDS_FOR_CHECKPOINT);
        if (ret != GMERR_OK) {
            return ret;
        }
        DB_LOG_INFO("end trigger ckpt");
        ret = SeBufferpoolReset(seIns);
        if (ret != GMERR_OK) {
            return ret;
        }
        // reset bufferpool后，处理file对应的device，后续异步将dumem文件load到内存中
        ret = SeRemoveDeviceIdFromFreeList(seIns);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (SeGetStorageStatus(seIns) != SE_ON_DISK_CREATE) {
        bool isPrefetch = seIns->seConfig.preFetchPagesEnable;
        if (isPrefetch || isCoexist) {
            // 双形态+预取 传入 SE_ALL_FILE
            // 双形态+非预取 传入 SE_DURABLE_FILE
            // 非双形态+预取 传入 SE_BUFFER_POOL_FILE
            SeFileTypeE type = isCoexist ? isPrefetch ? SE_ALL_FILE : SE_DURABLE_FILE : SE_BUFFER_POOL_FILE;
            ret = SeDbPreLoadPages(dbInstance, type);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return SeVirtualDiskFullCompressEnd(seIns, false);
}

Status SeDatabaseOpen(SeInstanceT *seIns, const char *path)
{
    // 只有持久化才会走进此流程
    StatusInter ret = VfdMgrConstructor(seIns);
    if (ret != STATUS_OK_INTER) {
        return DbGetExternalErrno(ret);
    }

    if (!SeIsZoneOnline(seIns, DB_RESERVE_HANDLE_INDEX)) {
        ret = SeTakeZoneOnline(seIns, DB_RESERVE_HANDLE_INDEX);
        if (ret != STATUS_OK_INTER) {
            return DbGetExternalErrno(ret);
        }
    }

    ret = SpaceDataFilePrepare(seIns, path, true);
    if (ret != STATUS_OK_INTER) {
        return DbGetExternalErrno(ret);
    }

    ret = DatabaseRecoverFromSafeFile(seIns);
    if (ret != STATUS_OK_INTER) {
        return DbGetExternalErrno(ret);
    }

    ret = LoadDataFile(seIns);
    if (ret != STATUS_OK_INTER) {
        return DbGetExternalErrno(ret);
    }

    Status retExt = VerifyDataFileDigestForReboot(seIns);
    if (retExt != GMERR_OK) {
        DB_LOG_ERROR(retExt, "Verify datafile digest when init storage.");
        return retExt;
    }

    if (SeGetPersistMode() == PERSIST_INCREMENT) {
        ret = MultiZoneDataFilePrepare(seIns);
        if (ret != STATUS_OK_INTER) {
            return DbGetExternalErrno(ret);
        }
        ret = MultiZoneSynRecoveryData(seIns);
        if (ret != STATUS_OK_INTER) {
            DB_LOG_ERROR(ret, "Syn data under multi-zone bufferpool.");
            return DbGetExternalErrno(ret);
        }
    } else {
        // 增量模式在recovery redo回放后，校验完成datafile后，再释放datafile handle
        SeTakeZoneOffline(seIns, DB_RESERVE_HANDLE_INDEX);
    }

    return DbGetExternalErrno(DbLoadSpace(seIns));
}

StatusInter DbLoadCtrlFileImpl(SeInstanceT *seIns, bool isReload)
{
    DB_POINTER(seIns);
    const char *dataDir = SeGetMainDataDirPath(seIns);
    if (SeIsStringEmpty(dataDir)) {
        DB_LOG_INFO_UNFOLD("data dir empty, create new db");
        return DbGetStatusInterErrno(SeSetStorageStatus(seIns, SE_ON_DISK_CREATE));
    }

#ifdef FEATURE_REPLICATION
    if (DbHaGetRole() == DB_HA_ROLE_SLAVE && DbDirExist(dataDir)) {
        StatusInter ret = DbGetStatusInterErrno(DbRemoveDir(dataDir));
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        ret = DbGetStatusInterErrno(DbMakeDirWithGRPRXPermission(dataDir));
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
#endif

    char filePath[DB_MAX_WHOLE_PATH];
    StatusInter ret = SeFileConstructPath(dataDir, seIns->seConfig.ctrlFileName, filePath, DB_MAX_WHOLE_PATH);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    if (!DbFileExist(filePath)) {
        if (SeGetMainZoneId(seIns) == DB_RESERVE_HANDLE_INDEX) {
            SE_LAST_ERROR(NO_DATA_FILE_NOT_EXIST, "Ctrl file %s not exist with -r option, load ctrl", filePath);
            return NO_DATA_FILE_NOT_EXIST;
        } else {
            DB_LOG_INFO_UNFOLD("ctrl File %s not exist, create new db", filePath);
            return DbGetStatusInterErrno(SeSetStorageStatus(seIns, SE_ON_DISK_CREATE));
        }
    }
    size_t fileSize = 0;
    ret = DbGetStatusInterErrno(DbFileSize(filePath, &fileSize));
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Calculate db ctrl file size");
        return ret;
    }
    if (fileSize == 0) {
        DB_LOG_INFO_UNFOLD("ctrl File %s empty, create new db", filePath);
        return DbGetStatusInterErrno(SeSetStorageStatus(seIns, SE_ON_DISK_CREATE));
    }
    ret = DbLoadCtrlfileInner(seIns, filePath, SeGetMainZoneId(seIns), isReload);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Load ctrl file, path(%s), isReload(%" PRIu32 ")", filePath, isReload);
        return ret;
    }
    return ret;
}

Status StartupDatabaseImpl(SeInstanceT *seIns)
{
    RedoRunCtxT *redoCtx = NULL;
    Status ret = DbGetExternalErrno(RedoCtxCreate(seIns->redoMgr, &redoCtx));
    if (ret != GMERR_OK) {
        return ret;
    }
    SeSetCurRedoCtx(redoCtx);
    // check whether the control file exists
    if (SeGetStorageStatus(seIns) == SE_ON_DISK_CREATE) {
        DB_LOG_INFO("Start database from DB initialization.");
        ret = SeDatabaseInit(seIns);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Start database from DB initialization");
        }
    } else {
        DB_LOG_INFO("Start database from DB open.");
        ret = SeDatabaseOpen(seIns, SeGetMainDataDirPath(seIns));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Start database from existed file");
        }
    }
    SeSetCurRedoCtx(NULL);
    RedoCtxRelease(redoCtx);
    return ret;
}

void SeSetNormalShutDownImpl(SeInstanceT *seIns, bool isNormalShutDown)
{
    DB_POINTER(seIns);
    if (seIns->db != NULL) {
        seIns->db->core.isNormalShutDown = isNormalShutDown;
    }
}

bool SeGetNormalShutDownImpl(SeInstanceT *seIns)
{
    DB_POINTER(seIns);
    if (seIns->db != NULL) {
        return seIns->db->core.isNormalShutDown;
    }
    return false;
}

bool SeEnableCompressPersistFile(void)
{
    return DbEnableCompress();
}
