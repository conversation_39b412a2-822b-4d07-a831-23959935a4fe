/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: space ddl op
 * Author: zhangfengyu
 * Create: 2023-6
 */
#include "se_space_ddl.h"
#include "se_ckpt_inner.h"
#include "se_database.h"
#include "se_db_file.h"
#include "se_db_file_device.h"
#include "se_db_file_space.h"
#include "se_page_mgr.h"
#include "se_space_redo.h"
#include "se_space_undo.h"

static StatusInter SpaceCreateCheckConfig(const SeInstanceT *seIns, const SpaceCfgT *cfg)
{
    uint32_t devSize = seIns->seConfig.deviceSize * SIZE_K(1);
    if ((cfg->maxSize < devSize) || (cfg->maxSize % devSize != 0)) {
        SE_LAST_ERROR(INT_ERR_SPACE_CFG,
            "Space maxSize(%" PRIu64 "(B)) must be a positive integer multiple of deviceSize(%" PRIu32 "(B))",
            cfg->maxSize, devSize);
        return INT_ERR_SPACE_CFG;
    }
#if defined(FEATURE_TS)
    if (cfg->isVolatile) {
        if (!cfg->isCustomFilePath) {
            // The table_path must be set in the with clause of the SQL statement when is_volatile_label is true.
            SE_LAST_ERROR(INT_ERR_SPACE_CFG, "set table_path when is_volatile_label");
            return INT_ERR_SPACE_CFG;
        }
        char customPath[DB_MAX_WHOLE_PATH] = {0};
        Status ret = DbGetRealPath(cfg->customFilePath, customPath, DB_MAX_WHOLE_PATH);
        if (ret != GMERR_OK) {
            SE_ERROR(DbGetStatusInterErrno(ret), "get customFilePath");
            return DbGetStatusInterErrno(ret);
        }
        for (uint8_t zoneId = 0; zoneId < seIns->seConfig.multiZoneNum; zoneId++) {
            char dfPath[PATH_MAX] = {0};
            ret = DbGetRealPath(seIns->seConfig.dataFileDirPath[zoneId], dfPath, PATH_MAX);
            if (ret != GMERR_OK) {
                SE_ERROR(DbGetStatusInterErrno(ret), "get dataFileDirPath");
                return DbGetStatusInterErrno(ret);
            }
            if (strncmp(customPath, dfPath, (uint16_t)strlen(customPath)) == 0) {
                SE_LAST_ERROR(INT_ERR_SPACE_CFG, "table_path parent of dataFileDirPath");
                return INT_ERR_SPACE_CFG;
            }
        }
    }
#endif
    return STATUS_OK_INTER;
}

static StatusInter SpaceCreateCheckSpaceName(SeInstanceT *seIns, const SpaceCfgT *cfg)
{
    SpaceT *space = NULL;
    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->spaceGroup); ++i) {
        space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, i);
        if (space->ctrl->used) {
            if (strcmp(cfg->name, (const char *)space->ctrl->cfg.name) == 0) {
                SE_LAST_ERROR(INT_ERR_SPACE_ALREADY_EXIST, "space already exist(%s)", space->ctrl->cfg.name);
                return INT_ERR_SPACE_ALREADY_EXIST;
            }
        }
    }
    return STATUS_OK_INTER;
}

static StatusInter SpaceCreateWriteUndoLog(SeRunCtxHdT seRunCtx, uint32_t spaceId)
{
    // 系统space无法回滚，因为undoSpace此时还没有创建，无法记录undo日志
    // create流程如果涉及到自定义space创建（start MaintainSysTable），不支持回滚
    if (seRunCtx->trx == NULL) {
        return STATUS_OK_INTER;
    }
    SpaceUndoCfgT cfg = {
        .opType = TRX_OP_CREATE,
        .spaceOrFileId = spaceId,
    };
    StatusInter ret = SpaceLogUndo(seRunCtx, &cfg);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Log space undo, spaceId(%" PRIu32 ").", spaceId);
    }
    return ret;
}

static StatusInter AllocSpaceCtrl(SeRunCtxT *seRunCtx, const SpaceCfgT *cfg, uint32_t *spaceId)
{
    StatusInter ret = SpacePreAllocUndo(seRunCtx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    ret = CtrlRedoLogBegin(redoCtx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    SeInstanceT *seIns = seRunCtx->seIns;

    ret = SpaceCreateCheckSpaceName(seIns, cfg);
    if (ret != STATUS_OK_INTER) {
        (void)CtrlRedoLogEnd(redoCtx, false);
        return ret;
    }

    uint32_t newSpaceId = DB_INVALID_TABLE_SPACE_INDEX;
    ret = SeAllocSpaceCtrl(seIns, cfg, &newSpaceId);
    if (ret != STATUS_OK_INTER) {
        (void)CtrlRedoLogEnd(redoCtx, false);
        return ret;
    }

    ret = SpaceCreateWriteUndoLog(seRunCtx, newSpaceId);
    if (ret != STATUS_OK_INTER) {
        SeFreeSpaceCtrl(seIns, newSpaceId);
        (void)CtrlRedoLogEnd(redoCtx, false);
        return ret;
    }

    ret = CtrlRedoLogEnd(redoCtx, true);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    *spaceId = newSpaceId;
    return STATUS_OK_INTER;
}

static StatusInter AllocFileCtrlInitCfg(const SeInstanceT *seIns, SpaceT *space, const char *fileName, const char *dir,
    bool writeUndo, DbFileCreateCfgT *cfg)
{
    if (strlen(fileName) >= (uint32_t)DB_MAX_NAME_LEN) {
        SE_ERROR(INVALID_NAME_INTER, "fileName(%s, len = %" PRIu32 "), max name len = %" PRIu32, fileName,
            (uint32_t)strlen(fileName), (uint32_t)(DB_MAX_NAME_LEN - 1));
        return INVALID_NAME_INTER;
    }
    int32_t size = sprintf_s(cfg->name, DB_MAX_NAME_LEN, "%s", fileName);
    if (size < 0) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER, "Sprintf_s cfg file, name(%s), id(%" PRIu32 ")", fileName, space->ctrl->id);
        return INTERNAL_ERROR_INTER;
    }

    cfg->spaceId = space->ctrl->id;
    cfg->blockSize = SIZE_K(seIns->seConfig.pageSize);
    cfg->dataOffset = DB_CALC_ALIGN(sizeof(PersDataFileHeadT), seIns->db->diskBlockSize);
    cfg->writeUndo = writeUndo;
    cfg->fileType = DEFAULT_FILE;

    if (dir == NULL) {
        cfg->customDir = false;
        return STATUS_OK_INTER;
    }

    if (strlen(dir) >= (uint32_t)DB_MAX_PATH) {
        SE_ERROR(INVALID_NAME_INTER, "dir(%s, len = %" PRIu32 "), max dir len = %" PRIu32, dir, (uint32_t)strlen(dir),
            (uint32_t)(DB_MAX_PATH - 1));
        return INVALID_NAME_INTER;
    }
    size = sprintf_s(cfg->dir, DB_MAX_PATH, "%s", dir);
    if (size < 0) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER, "sprintf_s(size = %" PRId32 ") cfg dir unsucc, name(%s), dir(%s)", size,
            fileName, dir);
        return INTERNAL_ERROR_INTER;
    }
    cfg->customDir = true;

    return STATUS_OK_INTER;
}

static bool SpaceFileNameCheck(SeInstanceT *seIns, DbFileCreateCfgT *cfg, DbFileCtrlT *ctrl)
{
    // 判断文件名是否相同，如果不相同，则返回false
    if (strcmp(cfg->name, (const char *)ctrl->name) != 0) {
        return false;
    }
#if defined(IDS_HAOTIAN)
    // 当前IDS_HAOTIAN场景，不支持多区
    uint32_t zoneId = SeGetMainZoneId(seIns);
    const char *ctrlDir = ctrl->customDir ? ctrl->dir : seIns->seConfig.dataFileDirPath[zoneId];
    const char *cfgDir = cfg->customDir ? cfg->dir : seIns->seConfig.dataFileDirPath[zoneId];
    if (strcmp(ctrlDir, cfgDir) != 0) {
        return false;
    }
#endif
    return true;
}

StatusInter SpaceFileCheck(SeRunCtxHdT seRunCtx, DbFileCreateCfgT *cfg)
{
    DB_POINTER2(seRunCtx, cfg);

    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->fileGroup); i++) {
        DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, i);
        if (DbFileIsOnline(file->ctrl) && SpaceFileNameCheck(seIns, cfg, file->ctrl)) {
            SE_ERROR(INVALID_NAME_INTER, "fileName(%s) is already used by the fileId(%" PRIu32 ")", cfg->name, i);
            return INVALID_NAME_INTER;
        }
    }
#if defined(IDS_HAOTIAN)
    // 当前IDS_HAOTIAN场景，不支持多区
    uint32_t zoneId = SeGetMainZoneId(seIns);
    const char *cfgDir = cfg->customDir ? cfg->dir : seIns->seConfig.dataFileDirPath[zoneId];
    char fullPath[DB_MAX_WHOLE_PATH] = {0};
    StatusInter ret = SeFileConstructPath(cfgDir, cfg->name, fullPath, DB_MAX_WHOLE_PATH);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    // 校验文件是否已经存在
    if (DbFileExist(fullPath)) {
        SE_LAST_ERROR(FILE_OPERATE_FAILED_INTER, "create file(%s), file already exist.", fullPath);
        return FILE_OPERATE_FAILED_INTER;
    }
#endif

    return STATUS_OK_INTER;
}

static StatusInter SpcCreateFileDone(SeInstanceT *seIns, uint32_t fileId)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    CtrlRedoLogBegin(redoCtx);
    DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, fileId);
    if (file == NULL) {
        SE_ERROR(INTERNAL_ERROR_INTER, "file id (%" PRIu32 ") unexpect.", fileId);
        (void)CtrlRedoLogEnd(redoCtx, false);
        return INTERNAL_ERROR_INTER;
    }

    file->ctrl->status = FILE_ONLINE;
    uint16_t statusOffset = (uint16_t)(file->ctrl->offset + offsetof(DbFileCtrlT, id) + (uint32_t)sizeof(uint32_t));
    CtrlRedoLogWrite(seIns, FileCtrlGetPageHead(file->ctrl), statusOffset, sizeof(uint8_t),
        ((uint8_t *)&file->ctrl->id + sizeof(uint32_t)));
    StatusInter ret = CtrlRedoLogEnd(redoCtx, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "end ctrl redo");
        return ret;
    }
    return ret;
}

static StatusInter SpaceCreateFile(SeRunCtxT *seRunCtx, SpaceT *space, DbFileCreateCfgT *fileCfg)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    CtrlRedoLogBegin(redoCtx);
    StatusInter ret = SpaceFileCheck(seRunCtx, fileCfg);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Space create file check para");
        (void)CtrlRedoLogEnd(redoCtx, false);
        return ret;
    }

    ret = SeCreateDbFile(seRunCtx, fileCfg);
    if (ret != STATUS_OK_INTER) {
        (void)CtrlRedoLogEnd(redoCtx, false);
        SE_ERROR(ret, "Space create file (%s)", fileCfg->name);
        return ret;
    }

    ret = CtrlRedoLogEnd(redoCtx, true);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    // 此处失败通过事务进行回滚
    ret = SpcCreateDiskFile(seRunCtx->seIns, fileCfg->fileId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "create disk file, fileId: %" PRIu32, fileCfg->fileId);
        return ret;
    }

    return SpcCreateFileDone(seRunCtx->seIns, fileCfg->fileId);
}

static StatusInter SpaceCreateAllocResource(SeRunCtxT *seRunCtx, const SpaceCfgT *cfg, uint32_t *res, uint32_t *fileId)
{
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    uint32_t spaceId = DB_INVALID_TABLE_SPACE_INDEX;
    StatusInter ret = AllocSpaceCtrl(seRunCtx, cfg, &spaceId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Alloc space ctrl. name:%s", cfg->name);
        return ret;
    }

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, spaceId);
    DbFileCreateCfgT fileCfg = {0};
    ret = AllocFileCtrlInitCfg(seRunCtx->seIns, space, space->ctrl->cfg.name, NULL, false, &fileCfg);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "space init create file cfg (%s) unsucc", space->ctrl->cfg.name);
        return ret;
    }

    ret = SpaceCreateFile(seRunCtx, space, &fileCfg);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Create file. spaceId:%" PRIu32 ", name:%s", spaceId, cfg->name);
        return ret;
    }

    RedoLogBegin(SeGetCurRedoCtx());
    ret = SeExtendDbFile(seIns, fileCfg.fileId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Extend file. fileId:%" PRIu32 ", name:%s", fileCfg.fileId, cfg->name);
        (void)RedoLogEnd(SeGetCurRedoCtx(), false);
        return ret;
    }
    ret = RedoLogEnd(SeGetCurRedoCtx(), true);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    *fileId = fileCfg.fileId;
    *res = spaceId;
    return STATUS_OK_INTER;
}

static inline void SpaceHeadInit(SeInstanceT *seIns, SpaceHeadT *spaceHead, DbFileT *file, DbDeviceT *device)
{
    spaceHead->blockHwm = 1;
    spaceHead->lastDeviceId = device->ctrl->id;
    spaceHead->freePageList = (PageListT){0, SE_INVALID_PAGE_ADDR, SE_INVALID_PAGE_ADDR};
    spaceHead->allocedSize = SIZE_K(seIns->seConfig.extendSize);
    spaceHead->usedSize = SIZE_K(seIns->seConfig.pageSize);
    spaceHead->freeDevCnt = 0;
    SpaceRedoSpaceHead((PageIdT){.deviceId = device->ctrl->id, .blockId = SPACE_HEAD_BLOCKID}, spaceHead);
}

static StatusInter SpaceHeadInitSetCtrlEntry(SeInstanceT *seIns, SpaceT *space, DbFileT *file, PageIdT entry)
{
    StatusInter ret = CtrlRedoLogBegin(SeGetCurRedoCtx());
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    file->ctrl->entry = entry;
    uint64_t offset = file->ctrl->offset + offsetof(DbFileCtrlT, entry);
    CtrlRedoLogWrite(seIns, FileCtrlGetPageHead(file->ctrl), offset, sizeof(PageIdT), (uint8_t *)&file->ctrl->entry);

    if (SpaceFileListGetLen(space) == 0) {
        space->ctrl->entry = entry;  // space 保存第一个文件的head pageId
        offset = space->ctrl->offset + offsetof(SpaceCtrlT, entry);
        CtrlRedoLogWrite(
            seIns, SpaceCtrlGetPageHead(space->ctrl), offset, sizeof(PageIdT), (uint8_t *)&space->ctrl->entry);
    }

    return CtrlRedoLogEnd(SeGetCurRedoCtx(), true);
}

StatusInter SpaceInitSpaceHead(SeInstanceT *seIns, SpaceCreateInfoT *info)
{
    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, info->spaceId);
    DB_ASSERT(space);
    DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, info->fileId);
    DB_ASSERT(file->ctrl && file->ctrl->spaceId == info->spaceId);
    DbDeviceT *device = SeFindDeviceByFileIndex(&seIns->db->deviceGroup, file->ctrl->id, 0);
    DB_ASSERT(device->ctrl && device->ctrl->spcId == info->spaceId);
    PageMgrT *pageMgr = (PageMgrT *)seIns->pageMgr;
    PageHeadT *page = NULL;
    PageIdT pageAddr = {.deviceId = device->ctrl->id, .blockId = SPACE_HEAD_BLOCKID};
    StatusInter ret = SpaceAllocPageById(seIns, pageAddr, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Register space head, entry(%" PRIu32 ", %" PRIu32 ")", pageAddr.deviceId, pageAddr.blockId);
        return ret;
    }
    ret = SeGetPageInit(pageMgr, PAGE_INVALID_TRM_ID, pageAddr, ENTER_PAGE_INIT, (uint8_t **)&page);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Get space head, entry(%" PRIu32 ", %" PRIu32 ")", pageAddr.deviceId, pageAddr.blockId);
        return ret;
    }
    ret = SpaceHeadInitSetCtrlEntry(seIns, space, file, pageAddr);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }
    SpaceHeadT *spaceHead = (SpaceHeadT *)(void *)((uint8_t *)page + sizeof(PageHeadT));
    SePageHeadSetType(page, PERSISTENCE_PAGE_TYPE_SPACE_MANAGER);
    SpaceHeadInit(seIns, spaceHead, file, device);
    space->usedSize += spaceHead->usedSize;
    space->allocedSize += spaceHead->allocedSize;
EXIT:
    SeLeavePage(pageMgr, pageAddr, true);
    return ret;
}

StatusInter SpaceCreate(SeInstanceT *seIns, SeRunCtxHdT seRunCtx, const SpaceCfgT *cfg, uint32_t *spaceId)
{
    StatusInter ret = SpaceCreateCheckConfig(seIns, cfg);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    uint32_t newSpaceId = DB_INVALID_TABLE_SPACE_INDEX;
    uint32_t fileId = SE_INVALID_FILE_ID;
    if (seRunCtx == NULL) {
        // 默认三个space不会使用seRunctx
        DB_ASSERT(cfg->type != SPACE_TYPE_USER_DEFINED);
        SeRunCtxT tmpCtx = {0};
        tmpCtx.seIns = seIns;
        ret = SpaceCreateAllocResource(&tmpCtx, cfg, &newSpaceId, &fileId);
    } else {
        ret = SpaceCreateAllocResource(seRunCtx, cfg, &newSpaceId, &fileId);
    }
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Alloc ctrl for space create %s", cfg->name);
        return ret;
    }

    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);
    // 修改初始化space head页
    SpaceCreateInfoT info = {.spaceId = newSpaceId, .fileId = fileId};
    ret = SpaceInitSpaceHead(seIns, &info);
    if (ret != STATUS_OK_INTER) {
        (void)RedoLogEnd(redoCtx, true);
        return ret;
    }
    ret = RedoLogEnd(redoCtx, true);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, newSpaceId);
    ret = SpaceAddFile(space, info.fileId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "space add file (%s) unsucc", space->ctrl->cfg.fileName);
        return ret;
    }

    *spaceId = newSpaceId;
    return ret;
}

#if defined(FEATURE_TS)
static void InitCustomSpaceCfg(const SeSpaceCreateCfgT *spaceBaseConf, SpaceCfgT *cfg)
{
    cfg->isVolatile = spaceBaseConf->isVolatile;
    if (!spaceBaseConf->isCustomFilePath) {
        return;
    }
    errno_t err = strcpy_s(cfg->customFilePath, DB_MAX_PATH, spaceBaseConf->customFilePath);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Copy custom file path, strcpy_s os ret:%" PRId32, err);
        return;
    }
    cfg->isCustomFilePath = spaceBaseConf->isCustomFilePath;
}
#endif

StatusInter SeSpaceCreateImpl(SeRunCtxHdT seRunCtx, const SeSpaceCreateCfgT *baseCfg, uint32_t *spaceId)
{
    DB_POINTER2(baseCfg, spaceId);
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    SpaceCfgT cfg = {0};
    InitSpaceCfg(seIns, baseCfg->maxSize, SPACE_TYPE_USER_DEFINED, baseCfg->fileName, &cfg);
#if defined(FEATURE_TS)
    InitCustomSpaceCfg(baseCfg, &cfg);
#endif
    StatusInter ret = SpaceCreate(seIns, seRunCtx, &cfg, spaceId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "create space");
        return ret;
    }
    // ctrl修改需要及时刷盘，否则重启后ctrlRedo重演前，可能会用到ctrl信息
    ret = CkptTriggerImpl(seIns, CKPT_MODE_FULL, true, 0);
    // 当前checkpoint只有文件操作失败时，会触发锁库，返回错误码
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Trigger checkpoint after create space, spaceId(%" PRIu32 ").", *spaceId);
        return ret;
    }
    DB_LOG_INFO("space create succ(id:%" PRIu32 ", fileName:%s, maxSize:%" PRIu64 ") ", *spaceId, baseCfg->fileName,
        baseCfg->maxSize);
    return ret;
}

static StatusInter SpaceCheckSpcId(SeInstanceT *seIns, uint32_t spaceId)
{
    DB_POINTER(seIns);
    if (spaceId >= seIns->seConfig.spaceMaxNum) {
        SE_LAST_ERROR(INT_ERR_SPACE_INVALID_SPACE_ID, "space id(%" PRIu32 ") is larger than maxSpaceNum(%" PRIu32 ")",
            spaceId, seIns->seConfig.spaceMaxNum);
        return INT_ERR_SPACE_INVALID_SPACE_ID;
    }

    StCoreCtrlT *core = &seIns->db->core;
    if ((spaceId == core->systemSpaceId) || (spaceId == core->undoSpaceId) || (spaceId == core->userSpaceId)) {
        SE_LAST_ERROR(INT_ERR_SPACE_INVALID_SPACE_ID,
            "space id(%" PRIu32 "), can not be default space(%" PRIu32 ", %" PRIu32 ", %" PRIu32 ")", spaceId,
            core->systemSpaceId, core->undoSpaceId, core->userSpaceId);
        return INT_ERR_SPACE_INVALID_SPACE_ID;
    }

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, spaceId);
    if (!space->ctrl || !space->ctrl->used) {
        SE_LAST_ERROR(INT_ERR_SPACE_INVALID_SPACE_ID, "Try drop non-exist space %" PRIu32, spaceId);
        DB_ASSERT(false);
        return INT_ERR_SPACE_INVALID_SPACE_ID;
    }

    return STATUS_OK_INTER;
}

StatusInter SeSpaceMarkDropImpl(SeRunCtxHdT seRunCtx, uint32_t spaceId)
{
    DB_POINTER(seRunCtx);
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;

    StatusInter ret = SpaceCheckSpcId(seIns, spaceId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Mark drop space, wrong spaceId (%" PRIu32 ").", spaceId);
        return ret;
    }

    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);

    SpaceUndoCfgT cfg = {
        .opType = TRX_OP_DROP,
        .spaceOrFileId = spaceId,
    };
    ret = SpaceLogUndo(seRunCtx, &cfg);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Create space undo log, spaceId (%" PRIu32 ").", spaceId);
        (void)RedoLogEnd(redoCtx, true);
        return ret;
    }

    return RedoLogEnd(redoCtx, true);
}

#define CLEAR_DEVICE_NO_EXCEPTION SE_INVALID_DEVICE_ID

static StatusInter ClearDevice(SeInstanceT *seIns, const SpaceT *space, uint32_t exception)
{
    StatusInter ret = STATUS_OK_INTER;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->deviceGroup); ++i) {
        DbDeviceT *device = (DbDeviceT *)CtrlGroupGetItem(&seIns->db->deviceGroup, i);
        if (device->ctrl->spcId != space->ctrl->id || device->ctrl->id == exception) {
            continue;
        }
        DB_ASSERT(device->ctrl->online);
        ret = CtrlRedoLogBegin(redoCtx);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, device->ctrl->fileId);
        if (file == NULL) {
            SE_LAST_ERROR(DATA_EXCEPTION_INTER, "file not exist, fileId: %" PRIu32, device->ctrl->fileId);
            return DATA_EXCEPTION_INTER;
        }
        SeFreeDbDevice(seIns, file, i);
        ret = CtrlRedoLogEnd(redoCtx, true);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

#define CLEAR_FILE_NO_EXCEPTION SE_INVALID_FILE_ID

static StatusInter ClearFile(SeInstanceT *seIns, SpaceT *space, uint32_t exception)
{
    StatusInter ret = STATUS_OK_INTER;
    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->fileGroup); ++i) {
        DbFileT *cursor = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, i);
        if (!cursor || cursor->ctrl->id == exception || cursor->ctrl->spaceId != space->ctrl->id) {
            continue;
        }
        // fileCtrl的spaceId和status是同步改的，所以ctrl不可能是offline的情况
        DB_ASSERT(!DbFileIsOffline(cursor->ctrl) && cursor->ctrl->spaceId == space->ctrl->id);
        SpaceRemoveFile(space, i);
        ret = SeRemoveDbFile(seIns, i);
        if (ret != STATUS_OK_INTER) {
            break;
        }
    }
    return ret;
}

StatusInter SeSpaceCommitDropImpl(SeInstanceT *seIns, uint32_t spaceId)
{
    // 事务提交时才会调用，需要先关闭外部的原子范围
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    StatusInter ret = RedoLogEnd(redoCtx, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "End redo log when commit drop, spaceId (%" PRIu32 ").", spaceId);
        return ret;
    }

    // 防止文件删除后，文件内的脏页无法落盘
    ret = CkptTriggerFileErase(seIns);
    // 当前checkpoint只有文件操作失败时，会触发锁库，返回错误码，事务提交失败
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Trigger checkpoint before drop space, spaceId(%" PRIu32 ")", spaceId);
        RedoLogBegin(redoCtx);
        return ret;
    }

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, spaceId);
    ret = ClearDevice(seIns, space, CLEAR_DEVICE_NO_EXCEPTION);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Clear devices at drop space, spaceId(%" PRIu32 ")", spaceId);
        RedoLogBegin(redoCtx);
        return ret;
    }

    ret = ClearFile(seIns, space, CLEAR_FILE_NO_EXCEPTION);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Clear files at drop space, spaceId(%" PRIu32 ")", spaceId);
        RedoLogBegin(redoCtx);
        return ret;
    }

    ret = CtrlRedoLogBegin(redoCtx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    SeFreeSpaceCtrl(seIns, space->ctrl->id);
    DB_LOG_INFO("space (%" PRIu32 ") exec drop succ", spaceId);
    return STATUS_OK_INTER;
}

StatusInter SeSpaceMarkTruncateImpl(SeRunCtxHdT seRunCtx, uint32_t spaceId)
{
    DB_POINTER(seRunCtx);
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;

    StatusInter ret = SpaceCheckSpcId(seIns, spaceId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Mark truncate space, wrong spaceId (%" PRIu32 ").", spaceId);
        return ret;
    }

    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogBegin(redoCtx);

    SpaceUndoCfgT cfg = {
        .opType = TRX_OP_TRUNCATE,
        .spaceOrFileId = spaceId,
    };
    ret = SpaceLogUndo(seRunCtx, &cfg);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Create space undo log, spaceId (%" PRIu32 ").", spaceId);
        (void)RedoLogEnd(redoCtx, false);
        return ret;
    }

    return RedoLogEnd(redoCtx, true);
}

static StatusInter SpaceCommitTruncateClearResource(SeInstanceT *seIns, uint32_t spaceId)
{
    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, spaceId);
    DB_ASSERT(space != NULL && space->ctrl->used == true);
    DbDeviceT *device = (DbDeviceT *)CtrlGroupGetItem(&seIns->db->deviceGroup, space->ctrl->entry.deviceId);
    DB_ASSERT(device != NULL && device->ctrl->online == true);
    StatusInter ret = ClearDevice(seIns, space, device->ctrl->id);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Clear devices at truncate space, spaceId(%" PRIu32 ")", space->ctrl->id);
        return ret;
    }

    ret = ClearFile(seIns, space, device->ctrl->fileId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Clear files at truncate space, spaceId(%" PRIu32 ")", space->ctrl->id);
        return ret;
    }

    ret = SeTruncateDbFile(seIns, device->ctrl->fileId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Truncate file at truncate space, spaceId(%" PRIu32 ")", space->ctrl->id);
    }
    return ret;
}

StatusInter SeSpaceCommitTruncateImpl(SeInstanceT *seIns, uint32_t spaceId)
{
    // 结束外层原子范围
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    StatusInter ret = RedoLogEnd(redoCtx, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "RedoLogEnd when truncate space.");
        RedoLogBegin(redoCtx);  // 维持开启状态，最终由外部关闭
        return ret;
    }

    // 防止文件删除后，文件内的脏页无法落盘
    ret = CkptTriggerFileErase(seIns);
    // 当前checkpoint只有文件操作失败时，会触发锁库，返回错误码，事务提交失败
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Trigger checkpoint when truncate space, spaceId(%" PRIu32 ")", spaceId);
        RedoLogBegin(redoCtx);
        return ret;
    }

    ret = SpaceCommitTruncateClearResource(seIns, spaceId);
    if (ret != STATUS_OK_INTER) {
        RedoLogBegin(redoCtx);
        return ret;
    }

    RedoLogBegin(redoCtx);
    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, spaceId);
    DB_ASSERT(space != NULL && space->ctrl->used == true);
    DbDeviceT *device = (DbDeviceT *)CtrlGroupGetItem(&seIns->db->deviceGroup, space->ctrl->entry.deviceId);
    DB_ASSERT(device != NULL && device->ctrl->online == true);
    SpaceCreateInfoT info = {.spaceId = spaceId, .fileId = device->ctrl->fileId};
    ret = SpaceInitSpaceHead(seIns, &info);
    if (ret == STATUS_OK_INTER) {
        DB_LOG_INFO("Space (%" PRIu32 ") exec truncate", spaceId);
    }
    return ret;
}

static StatusInter SpaceUpdateDeviceInfo(SeInstanceT *seIns, SpaceT *space, DbFileT *file, PageMgrT *pageMgr)
{
    StatusInter ret = SeGetFileHead(pageMgr, file, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "file Id(%" PRIu32 ") get file head", file->ctrl->id);
        return ret;
    }
    // 维护allocedSize
    uint64_t allocedPageSize = (uint64_t)file->ctrl->extendNum * SIZE_K(seIns->seConfig.extendSize);
    DB_ASSERT(file->head->allocedSize < allocedPageSize);
    file->head->allocedSize += SIZE_K(seIns->seConfig.extendSize);
    space->allocedSize += SIZE_K(seIns->seConfig.extendSize);

    /* 由于CtrlPage是直接落盘的，那么可能会出现这种情况，重启后：
     * blockHwm = 128; usedSize = 2; allocedSize = 2; lastDeviceId: 2; fileCtrl->deviceNum = 5(提前落盘)
     * 所以需要先找到spaceHead里的最后一个device，然后向后找到有效的deviceId，防止泄露
     * 因为bufferpool场景不存在归还的可能,而durableMemdata使用CtrlRedo机制归还device,
     * 即不可能出现有计数但是device不存在的情况 所有后续的device一定是有效的
     */
    DbDeviceT *device = (DbDeviceT *)CtrlGroupGetItem(&seIns->db->deviceGroup, file->head->lastDeviceId);
    if (file->head->blockHwm == SeGetPageCntPerDev(seIns)) {  // 说明必定要拓展一个新的device
        DB_ASSERT(device != NULL && device->ctrl->online);
        device = SeFindDeviceByFileIndex(&seIns->db->deviceGroup, file->ctrl->id, device->ctrl->fileIndex + 1);
        DB_ASSERT(device != NULL && device->ctrl->online);
        file->head->lastDeviceId = device->ctrl->id;
        file->head->blockHwm = 0;
    } else {
        DB_ASSERT(file->head->allocedSize - file->head->usedSize < (uint64_t)SIZE_K(seIns->seConfig.deviceSize));
    }
    SpaceRedoAppendDevice(file->head, device->ctrl);
    SeLeaveFileHead(pageMgr, file, true);
    return STATUS_OK_INTER;
}

StatusInter SpaceGetAvailableFileDevice(SeInstanceT *seIns, SpaceT *space, DbFileT *file, PageMgrT *pageMgr)
{
    StatusInter ret = STATUS_OK_INTER;
    uint64_t fileMaxSize = (uint64_t)space->ctrl->cfg.dbFileSize;
    uint64_t fileUsedSize = DB_INVALID_UINT64;
    ret = SeGetFileUsedSize(seIns, file, &fileUsedSize);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Get File (%" PRIu32 ") used size.", file->ctrl->id);
        return ret;
    }
    if (SeGetFileFullSize(seIns, file) > fileUsedSize) {
        return SpaceUpdateDeviceInfo(seIns, space, file, pageMgr);
    }
    if (SeGetFileFullSize(seIns, file) + SIZE_K(seIns->seConfig.extendSize) > fileMaxSize) {
        return INSUFF_RES_DATAFILE;  // 文件满，非异常场景，不用输出错误日志
    }
    // Extend成功后，即使上层的操作失败后续回滚了，也不会回收文件空间
    ret = SeExtendDbFile(seIns, file->ctrl->id);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret,
            "Space (%" PRIu32 ") extend file (%" PRIu32 "), cur file status (extendNum: %" PRIu32
            ", deviceNum: %" PRIu32 ")",
            space->ctrl->id, file->ctrl->id, file->ctrl->extendNum, file->ctrl->deviceNum);
        return ret;
    }
    return SpaceUpdateDeviceInfo(seIns, space, file, pageMgr);
}

// 新添加的文件会申请一个device并初始化文件头
StatusInter SpaceAddDataFileInner(SeRunCtxHdT seRunCtx, SpaceT *space, const char *fileName, const char *dir)
{
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    DbFileCreateCfgT cfg = {0};
    StatusInter ret = AllocFileCtrlInitCfg(seIns, space, fileName, dir, true, &cfg);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Space init file cfg (%s)", fileName);
        return ret;
    }

    ret = SpacePreAllocUndo(seRunCtx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    ret = SpaceCreateFile(seRunCtx, space, &cfg);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Space create file, spaceName = %s, fileName = %s", space->ctrl->cfg.name, fileName);
        return ret;
    }

    RedoLogBegin(SeGetCurRedoCtx());
    ret = SeExtendDbFile(seIns, cfg.fileId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Extend file. fileId:%" PRIu32 ", name:%s", cfg.fileId, fileName);
        (void)RedoLogEnd(SeGetCurRedoCtx(), false);
        return ret;
    }

    // 初始化file head页
    SpaceCreateInfoT info = {.spaceId = space->ctrl->id, .fileId = cfg.fileId};
    ret = SpaceInitSpaceHead(seIns, &info);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Init file head. fileId:%" PRIu32 ", name:%s", info.fileId, fileName);
        (void)RedoLogEnd(SeGetCurRedoCtx(), false);
        return ret;
    }

    ret = RedoLogEnd(SeGetCurRedoCtx(), true);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    // Dumem和BufPool共存场景，初始化完成头页后需全量刷盘一次并移除对应文件头的bufDesc
    if (SeIsBufpoolAndDumemCoexist(seIns)) {
        DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, info.fileId);
        ret = DbGetStatusInterErrno(SeInvalidBufferpoolPage(seIns, file->ctrl->entry));
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Remove new fileHead page. fileId:%" PRIu32 ", name:%s", cfg.fileId, fileName);
            return ret;
        }
    }

    ret = SpaceAddFile(space, info.fileId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Space add file (%s)", fileName);
    }

    return ret;
}

static StatusInter SeSplitFilePath(const char *filePath, char *dir, uint32_t dirLen, char *fileName, uint32_t nameLen)
{
    char *lastSlash = strrchr(filePath, '/');
    // 1.只包含文件名
    if (lastSlash == NULL) {
        if (strlen(filePath) >= nameLen) {
            SE_ERROR(INVALID_NAME_INTER, "fileName(%s, len = %" PRIu32 "), max name len = %" PRIu32, filePath,
                (uint32_t)strlen(filePath), (uint32_t)(nameLen - 1));
            return INVALID_NAME_INTER;
        }
        (void)memcpy_s(fileName, nameLen, filePath, strlen(filePath) + 1);
        (void)memset_s(dir, dirLen, 0x0, dirLen);
        return STATUS_OK_INTER;
    }

    char realFilePath[PATH_MAX] = {0};
    Status status = DbGetRealPath(filePath, realFilePath, PATH_MAX);
    if (status != STATUS_OK_INTER) {
        DB_LOG_ERROR(status, "get real path succ, filePath(%s)", filePath);
        return status;
    }
    lastSlash = strrchr(filePath, '/');
    DB_ASSERT(lastSlash != NULL);
    uint32_t realNameLen = (uint32_t)strlen(lastSlash + 1);
    // 2.只包含路径的情况
    if (realNameLen == 0) {
        if (strlen(filePath) >= dirLen) {
            SE_ERROR(INVALID_NAME_INTER, "dir(%s, len = %" PRIu32 "), max dir len = %" PRIu32, filePath,
                (uint32_t)strlen(filePath), (uint32_t)(dirLen - 1));
            return INVALID_NAME_INTER;
        }
        (void)memcpy_s(dir, dirLen, filePath, strlen(filePath) + 1);
        (void)memset_s(fileName, nameLen, 0x0, nameLen);
        return STATUS_OK_INTER;
    }

    // 3.包含路径和文件名
    uint32_t realDirLen = (uint32_t)strlen(realFilePath) - realNameLen;
    if (realDirLen >= dirLen) {
        SE_ERROR(INVALID_NAME_INTER, "file path(%s), dir len = %" PRIu32 ", max dir len = %" PRIu32, realFilePath,
            realDirLen, (uint32_t)(dirLen - 1));
        return INVALID_NAME_INTER;
    }
    (void)memcpy_s(dir, dirLen, realFilePath, realDirLen);
    dir[realDirLen] = '\0';

    if (realNameLen >= nameLen) {
        SE_ERROR(INVALID_NAME_INTER, "fileName(%s, len = %" PRIu32 "), max name len = %" PRIu32, lastSlash + 1,
            realNameLen, (uint32_t)(nameLen - 1));
        return INVALID_NAME_INTER;
    }
    (void)memcpy_s(fileName, nameLen, lastSlash + 1, realNameLen + 1);
    return STATUS_OK_INTER;
}

StatusInter SeSpaceAddDefaultDataFileImpl(SeRunCtxHdT seRunCtx, SpaceT *space)
{
    char fileName[DB_MAX_NAME_LEN] = {0};
    int32_t size =
        sprintf_s(fileName, DB_MAX_NAME_LEN, "%s_%" PRIu32, space->ctrl->cfg.name, SpaceFileListGetLen(space));
    if (size < 0) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER, "sprintf_s unsucc, size(%" PRIu32 ")", size);
        return INTERNAL_ERROR_INTER;
    }
    StatusInter ret = SpaceAddDataFileInner(seRunCtx, space, fileName, NULL);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Space(%s) add data file(%s)", space->ctrl->cfg.name, fileName);
        return ret;
    }
    return ret;
}

StatusInter SeSpaceAddDataFileImpl(SeRunCtxHdT seRunCtx, const char *spaceName, FileCfgT *fileCfg)
{
    DB_POINTER3(seRunCtx, spaceName, fileCfg->filePath);

    SeInstanceT *seIns = seRunCtx->seIns;
    SpaceT *space = NULL;
    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->spaceGroup); i++) {
        space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, i);
        if (space->ctrl->used && strcmp(spaceName, (const char *)space->ctrl->cfg.name) == 0) {
            // 当前暂不支持为systemSpace和undoSpace增加文件
            if ((i == SpaceGetIdByTypeImpl(seIns, SPACE_TYPE_SYSTEM)) ||
                (i == SpaceGetIdByTypeImpl(seIns, SPACE_TYPE_UNDO))) {
                SE_LAST_ERROR(INVALID_NAME_INTER, "spaceName can not be %s", space->ctrl->cfg.name);
                return INVALID_NAME_INTER;
            }
            break;
        }
        space = NULL;
    }

    if (space == NULL) {
        SE_LAST_ERROR(INVALID_NAME_INTER, "Get space by name, spaceName = %s", spaceName);
        return INVALID_NAME_INTER;
    }

    char fileName[DB_MAX_NAME_LEN] = {0};
    char dir[DB_MAX_PATH] = {0};
    StatusInter ret = SeSplitFilePath(fileCfg->filePath, dir, DB_MAX_PATH, fileName, DB_MAX_NAME_LEN);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "split file path(%s) unsucc", fileCfg->filePath);
        return ret;
    }

    if (strlen(dir) != 0 && !DbDirExist(dir)) {
        SE_LAST_ERROR(INVALID_NAME_INTER, "dir(%s) not exist", dir);
        return INVALID_NAME_INTER;
    }

    // 只包含文件路径，则文件名由db内部生成
    if (strlen(fileName) == 0) {
        int32_t size =
            sprintf_s(fileName, DB_MAX_NAME_LEN, "%s_%" PRIu32, space->ctrl->cfg.name, SpaceFileListGetLen(space));
        if (size < 0) {
            SE_LAST_ERROR(INTERNAL_ERROR_INTER, "sprintf_s unsucc, size(%" PRIu32 ")", size);
            return INTERNAL_ERROR_INTER;
        }
    }

    char *dirPath = strlen(dir) == 0 ? NULL : dir;
    ret = SpaceAddDataFileInner(seRunCtx, space, fileName, dirPath);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Space(%s) add data file(%s)", spaceName, fileName);
    }

    return ret;
}

static StatusInter ClearFileDevice(SeInstanceT *seIns, DbFileT *file)
{
    if (file->ctrl->deviceNum == 0) {
        return STATUS_OK_INTER;
    }

    StatusInter ret = STATUS_OK_INTER;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->deviceGroup); ++i) {
        DbDeviceT *device = (DbDeviceT *)CtrlGroupGetItem(&seIns->db->deviceGroup, i);
        if (device->ctrl->fileId != file->ctrl->id) {
            continue;
        }
        DB_ASSERT(device->ctrl->online);
        ret = CtrlRedoLogBegin(redoCtx);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        SeFreeDbDevice(seIns, file, i);
        ret = CtrlRedoLogEnd(redoCtx, true);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

static StatusInter SeGetFileSize(SeInstanceT *seIns, DbFileT *file, uint32_t *usedSize, uint32_t *allocedSize)
{
    // 文件entry还未赋值或者文件未打开
    uint32_t mainZoneId = SeGetMainZoneId(seIns);
    if (!DbIsPageIdValid(file->ctrl->entry) || file->handle[mainZoneId] == DB_INVALID_FD) {
        *usedSize = 0;
        *allocedSize = 0;
        return STATUS_OK_INTER;
    }

    PageHeadT *page;
    PageMgrT *pageMgr = (PageMgrT *)seIns->pageMgr;
    StatusInter ret = SeGetPage(pageMgr, file->ctrl->entry, (uint8_t **)&page, ENTER_PAGE_NORMAL, false);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    SpaceHeadT *head = (SpaceHeadT *)(void *)(page + sizeof(PageHeadT));
    *usedSize = head->usedSize;
    *allocedSize = head->allocedSize;
    SeLeavePage(pageMgr, file->ctrl->entry, false);
    return STATUS_OK_INTER;
}

StatusInter SeSpaceCommitDropFileImpl(SeInstanceT *seIns, uint32_t fileId)
{
    DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, fileId);
    if (DbFileIsOffline(file->ctrl)) {
        return STATUS_OK_INTER;
    }
    // 事务提交时才会调用，需要先关闭外部的原子范围
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    StatusInter ret = RedoLogEnd(redoCtx, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "RedoLogEnd when commit drop file, fileId (%" PRIu32 ").", fileId);
        return ret;
    }

    // 防止文件删除后，文件内的脏页无法落盘
    ret = CkptTriggerFileErase(seIns);
    // 当前checkpoint只有文件操作失败时，会触发锁库，返回错误码，事务提交失败
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Trigger checkpoint before drop file, fileId(%" PRIu32 ")", fileId);
        RedoLogBegin(redoCtx);
        return ret;
    }

    DB_ASSERT(file->ctrl->spaceId != DB_INVALID_TABLE_SPACE_ID);
    char fileName[DB_MAX_NAME_LEN] = {0};
    DB_ASSERT(sizeof(fileName) >= sizeof(file->ctrl->name));
    (void)memcpy_s(fileName, sizeof(fileName), file->ctrl->name, sizeof(file->ctrl->name));
    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, file->ctrl->spaceId);

    uint32_t usedSize = 0;
    uint32_t allocedSize = 0;
    ret = SeGetFileSize(seIns, file, &usedSize, &allocedSize);
    if (ret != STATUS_OK_INTER) {
        RedoLogBegin(redoCtx);
        return ret;
    }

    ret = ClearFileDevice(seIns, file);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Clear devices at drop file, name(%s), fileId(%" PRIu32 ")", file->ctrl->name, fileId);
        RedoLogBegin(redoCtx);
        return ret;
    }

    SpaceRemoveFile(space, fileId);
    ret = SeRemoveDbFile(seIns, fileId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Space delete file, spaceId = %" PRIu32 ", fileId = %" PRIu32, space->ctrl->id, fileId);
    }

    space->usedSize -= usedSize;
    space->allocedSize -= allocedSize;
    RedoLogBegin(redoCtx);
    DB_LOG_INFO("space (id:%" PRIu32 ", name:%s) exec drop data file(%s) succ", space->ctrl->id, space->ctrl->cfg.name,
        fileName);
    return ret;
}

#if defined(FEATURE_TS)
static StatusInter RebuildVolatileSpace(SeRunCtxT *seRunCtx, SpaceT *space)
{
    StatusInter ret = DbGetStatusInterErrno(SeTransBegin(seRunCtx, NULL));
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    ret = SeSpaceMarkTruncateImpl(seRunCtx, space->ctrl->id);
    if (ret != STATUS_OK_INTER) {
        (void)SeTransRollback(seRunCtx, false);
        return ret;
    }
    const char *dir = space->ctrl->cfg.customFilePath;
    if (!DbDirExist(dir)) {
        ret = DbGetStatusInterErrno(DbMakeDirWithGRPRXPermission(dir));
        if (ret != STATUS_OK_INTER) {
            (void)SeTransRollback(seRunCtx, false);
            return ret;
        }
    }
    uint32_t cursor = *(uint32_t *)SpaceFileListGet(space, 0);
    ret = SpcCreateDiskFile((SeInstanceT *)seRunCtx->seIns, cursor);
    if (ret != STATUS_OK_INTER) {
        (void)SeTransRollback(seRunCtx, false);
        return ret;
    }
    return DbGetStatusInterErrno(SeTransCommit(seRunCtx));
}
#endif

StatusInter SeCheckDiskIntegrity(SeRunCtxT *seRunCtx)
{
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    StatusInter ret = STATUS_OK_INTER;
    DbFileT *file = NULL;
    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->fileGroup); i++) {
        file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, i);
        if (!DbFileIsOnline(file->ctrl)) {
            continue;
        }
        if (file->handle[SeGetMainZoneId(seIns)] == DB_INVALID_FD) {
#if defined(FEATURE_TS)
            SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, file->ctrl->spaceId);
            DB_ASSERT(space != NULL && space->ctrl->used);
            if (space->ctrl->cfg.isVolatile) {
                ret = RebuildVolatileSpace(seRunCtx, space);
                if (ret != STATUS_OK_INTER) {
                    return ret;
                }
            } else {
                return NO_DATA_FILE_NOT_EXIST;
            }
#else
            ret = NO_DATA_FILE_NOT_EXIST;
            return ret;
#endif
        }
    }
    // undo回滚后校验不会失败，如果失败，说明DB文件一致性出现了问题
    ret = SpcCheckDataFileHeadAndTail(seIns, SeGetMainZoneId(seIns), SeGetMainDataDirPath(seIns), false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "file head or tail unexpect.");
        return ret;
    }
    return STATUS_OK_INTER;
}
