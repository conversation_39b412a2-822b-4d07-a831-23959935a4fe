/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 表空间管理
 * Create: 2023-6
 */
#include "se_space.h"
#include "db_crc.h"
#include "db_internal_error.h"
#include "db_inter_process_rwlatch.h"
#include "db_utils.h"
#include "db_alarm.h"
#include "se_db_file.h"
#include "se_db_file_device.h"
#include "se_define.h"
#include "se_log.h"
#include "se_database.h"
#include "se_recovery.h"
#include "se_space_redo.h"
#include "se_page_mgr.h"
#include "se_space_inner.h"
#include "se_durable_memdata.h"
#include "se_ckpt.h"
#include "se_undo_purger.h"
#include "se_ckpt_inner.h"
#include "adpt_compress.h"
#include "adpt_atomic.h"
#include "adpt_io.h"
#include "db_timer.h"
#include "db_sha.h"
#include "se_redo_inner.h"
#include "se_space_undo.h"
#include "se_space_ddl.h"
#include "se_verify_inner.h"
#include "se_vfd.h"
#include "se_space_condensed.h"
#include "adpt_sleep.h"
#include "se_ctrl_page.h"
#include "se_space_virtual_disk.h"

#define SPACE_DEFAULT_SPILT_TIME 1000  // 1秒
#define SE_INVALID_THREAD_HANDLE 0

typedef enum EnumSeCompressStat {
    COMP_STATE_IN_PROGRESS,
    COMP_STATE_COMPRESSED,  // 压缩线程已完成device压缩
    COMP_STATE_FLUSHED,     // 主线程已将压缩device刷盘
} SeCompressStatE;

typedef struct DbCompressCtx {
    SeInstanceT *seIns;
    volatile bool isRunning;
    uint32_t fileId;
    uint32_t startDevFileIndex;
    volatile SeCompressStatE compressStat;
    uint8_t *compressedDev;
    volatile uint32_t compressSize;
    volatile StatusInter threadRet;
    DbThreadHandle threadHandle;
} DbCompressCtxT;

static inline bool SpaceSkipMount(DbFileT *file, uint32_t zoneId)
{
    bool headNotReady = !DbIsPageIdValid(file->ctrl->entry);
    bool fileNotReady = file->handle[zoneId] == DB_INVALID_FD && SeGetPersistMode() == PERSIST_INCREMENT;
    return fileNotReady || headNotReady;
}

static StatusInter SpaceMountInner(SeInstanceT *seIns, SpaceT *space)
{
    DB_POINTER2(seIns, space);

    PageHeadT *page;
    PageMgrT *pageMgr = (PageMgrT *)seIns->pageMgr;
    StatusInter ret = STATUS_OK_INTER;
    // 防止计算出错，先初始化
    space->usedSize = 0;
    space->allocedSize = 0;
    uint32_t mainZoneId = SeGetMainZoneId(seIns);
    for (uint32_t i = 0; i < SpaceFileListGetLen(space); i++) {
        uint32_t fileId = *(uint32_t *)SpaceFileListGet(space, i);
        DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, fileId);
        if (file == NULL) {
            SE_LAST_ERROR(DATA_EXCEPTION_INTER, "file not exist, id:%" PRIu32, fileId);
            return DATA_EXCEPTION_INTER;
        }
        DB_ASSERT(!DbFileIsOffline(file->ctrl) && file->ctrl->spaceId == space->ctrl->id);
        PageIdT headEntry = file->ctrl->entry;
        if (SpaceSkipMount(file, mainZoneId)) {
            continue;  // 文件entry还未赋值或者文件未打开
        }
        ret = SeGetPage(pageMgr, headEntry, (uint8_t **)&page, ENTER_PAGE_NORMAL, false);
        if (ret != STATUS_OK_INTER) {
            // maybe mount a dropped space, page has already released
            SE_WARN(ret, "space mount:%" PRIu32 ", %" PRIu32, headEntry.deviceId, headEntry.blockId);
            continue;
        }

        SpaceHeadT *head = (SpaceHeadT *)(void *)((char *)page + sizeof(PageHeadT));
        space->allocedSize += head->allocedSize;
        space->usedSize += head->usedSize;
        SeLeavePage(pageMgr, headEntry, false);
    }

    return STATUS_OK_INTER;
}

StatusInter SpaceMount(SeInstanceT *seIns)
{
    DB_POINTER(seIns);

    SpaceT *spc;
    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->spaceGroup); i++) {
        spc = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, i);
        if (spc->ctrl->used == false) {
            continue;
        }

        StatusInter ret = SpaceMountInner(seIns, spc);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "space mount get wrong, spcId (%" PRIu32 ")", i);
            return ret;
        }
    }

    return STATUS_OK_INTER;
}

void InitSpaceCfg(SeInstanceT *seInstance, uint64_t maxSize, SpaceTypeE type, const char *fileName, SpaceCfgT *cfg)
{
    DB_POINTER3(cfg, seInstance, fileName);
    cfg->maxSize = maxSize;
    (void)strcpy_s(cfg->fileName, DB_MAX_NAME_LEN, fileName);
    cfg->type = type;

    (void)strcpy_s(cfg->name, DB_MAX_NAME_LEN, fileName);
    cfg->dbFileSize = (uint64_t)seInstance->seConfig.dbFileSize * SIZE_K(1);
    cfg->extendSize = (uint64_t)seInstance->seConfig.extendSize * SIZE_K(1);
#if defined(FEATURE_TS)
    cfg->isCustomFilePath = false;
    (void)memset_s(cfg->customFilePath, DB_MAX_PATH, 0, DB_MAX_PATH);
    cfg->isVolatile = false;
#endif
    // 上层已经初始化SpaceConfT cfg，给SpaceConfT padding字段赋值会重复
}

// 获取space多区多文件占用的总磁盘空间大小
StatusInter SeSpaceGetFileSizeImpl(SeInstanceT *seIns, uint32_t spcId, uint64_t *fileSize)
{
    DB_POINTER2(seIns, fileSize);

    if (spcId >= seIns->seConfig.spaceMaxNum) {
        SE_LAST_ERROR(INT_ERR_SPACE_INVALID_SPACE_ID, "space id (%" PRIu32 ") exceeded maxSpaceNum(%" PRIu32 ")", spcId,
            seIns->seConfig.spaceMaxNum);
        return INT_ERR_SPACE_INVALID_SPACE_ID;
    }

    DbFileT *file = NULL;
    size_t totalSize = 0;
    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->fileGroup); ++i) {
        file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, i);
        if (file->ctrl->spaceId != spcId || !DbFileIsOnline(file->ctrl)) {
            continue;
        }
        totalSize += SeGetFileFullSize(seIns, file);
    }

    totalSize *= seIns->seConfig.multiZoneNum;  // 每个区的文件大小都相等
    *fileSize = totalSize;
    return STATUS_OK_INTER;
}

static StatusInter SpaceAllocPageInner(
    SeInstanceT *seIns, uint8_t fileType, SpaceT *space, PageIdT *pageId, bool *isNew)
{
    StatusInter ret = STATUS_OK_INTER;
    for (uint32_t i = 0; i < SpaceFileListGetLen(space); i++) {
        uint32_t fileId = *(uint32_t *)SpaceFileListGet(space, i);
        DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, fileId);
        /* dumem和bufferpool共存场景下，需判断文件类型
         * 1.如果type一致，直接使用
         * 2.如果不一致且非DEFAULT_FILE, continue
         * 3.如果是DEFAULT_FILE，则说明拿到了未使用的文件，需设置文件类型
         */
        if (SeIsBufpoolAndDumemCoexist(seIns) && file->ctrl->fileType != fileType) {
            if (file->ctrl->fileType != DEFAULT_FILE) {
                continue;
            }

            ret = SeSetDbFileType(seIns, file, fileType);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "Space(%s) set data file type, fileId : %" PRIu32 ", fileType : %" PRIu32 ".",
                    space->ctrl->cfg.name, fileId, fileType);
                return ret;
            }
        }
        ret = file->allocPage(seIns, file, pageId, isNew);
        if (ret == INSUFF_RES_DATAFILE) {  // 文件满为正常情况，继续尝试下一个文件
            continue;
        }
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "space (id:%" PRIu32 ", name:%s) alloc page from fileIdx(idx:%" PRIu32 ") unsucc",
                space->ctrl->id, space->ctrl->cfg.name, i);
            break;
        }
        break;  //  分配成功，跳出循环
    }
    if (ret == INSUFF_RES_DATAFILE) {  // 所有文件都满了，需要为该space新增文件后才可使用
        ret = INSUFF_RES_DATAFILE_PER_SPACE;
        SE_ERROR(INSUFF_RES_DATAFILE_PER_SPACE,
            "space(id:%" PRIu32 ", name:%s) full, plz add a new file and try again.", space->ctrl->id,
            space->ctrl->cfg.name);
    }
    return ret;
}

StatusInter SpaceAllocPage(SeInstanceT *seIns, uint8_t fileType, uint32_t spaceId, PageIdT *pageId, bool *isNew)
{
    DB_POINTER(seIns);
    if (spaceId >= seIns->db->spaceGroup.size) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "spaceId unexpect, spaceId(%" PRIu32 "), group size(%" PRIu32 ").", spaceId,
            seIns->db->spaceGroup.size);
        return DATA_EXCEPTION_INTER;
    }
    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, spaceId);
    if (space->ctrl->used == false) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "space has been dropped when alloc page, spaceId(%" PRIu32 ").", spaceId);
        return DATA_EXCEPTION_INTER;
    }

    uint32_t fileId = *(uint32_t *)SpaceFileListGet(space, 0);
    DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, fileId);
    DB_ASSERT(file != NULL && DbFileIsOnline(file->ctrl));

    PageMgrT *pageMgr = SeGetPageMgrByFileType(seIns, file);
    if (pageMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get page mgr, file(%" PRIu32 ") type( %" PRIu32 ")", file->ctrl->id,
            file->ctrl->fileType);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    StatusInter ret = SeGetFileHead(pageMgr, file, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "file Id(%" PRIu32 ") get file head", file->ctrl->id);
        return ret;
    }

    PageHeadT *page = (PageHeadT *)((uint8_t *)file->head - sizeof(PageHeadT));
    ret = DbInterProcRWLatchW(&page->lock);
    if (ret != STATUS_OK_INTER) {
        SeLeaveFileHead(pageMgr, file, true);
        return ret;
    }

    ret = SpaceAllocPageInner(seIns, fileType, space, pageId, isNew);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(
            ret, "space (id:%" PRIu32 ", name:%s) alloc page from file unsucc", space->ctrl->id, space->ctrl->cfg.name);
    }

    DbInterProcRWUnlatchW(&page->lock);
    SeLeaveFileHead(pageMgr, file, true);
    return ret;
}

StatusInter SpaceAllocPageById(SeInstanceT *seIns, PageIdT addr, bool valid)
{
    DB_POINTER(seIns);
    DbDeviceT *device = (DbDeviceT *)CtrlGroupGetItem(&seIns->db->deviceGroup, addr.deviceId);
    DB_ASSERT(device != NULL && device->ctrl->online);
    DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, device->ctrl->fileId);
    DB_ASSERT(file != NULL && DbFileIsOnline(file->ctrl));
    return file->allocPageById(seIns, file, addr, valid);
}

static StatusInter SpaceFreePagePreCheck(SeInstanceT *seIns, uint32_t spcId, PageIdT pageId)
{
    if (spcId >= seIns->db->spaceGroup.size) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER,
            "SpaceId unexpect when free page, spaceId(%" PRIu32 "), group size(%" PRIu32 ").", spcId,
            seIns->db->spaceGroup.size);
        return DATA_EXCEPTION_INTER;
    }

    if (pageId.deviceId >= seIns->db->deviceGroup.size) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER,
            "deviceId is unexpect when free page, deviceId(%" PRIu32 "), group size(%" PRIu32 ").", pageId.deviceId,
            seIns->db->deviceGroup.size);
        return DATA_EXCEPTION_INTER;
    }
    return STATUS_OK_INTER;
}

StatusInter SpaceFreePage(SeInstanceT *seIns, uint32_t spcId, PageIdT pageId)
{
    StatusInter ret = SpaceFreePagePreCheck(seIns, spcId, pageId);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, spcId);
    if (space == NULL || space->ctrl->used == false) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "Space has been dropped, spaceId(%" PRIu32 ").", spcId);
        return DATA_EXCEPTION_INTER;
    }

    DbDeviceT *device = (DbDeviceT *)CtrlGroupGetItem(&seIns->db->deviceGroup, pageId.deviceId);
    if (device == NULL || device->ctrl->online == false) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "device unuse when free page, deviceId(%" PRIu32 ").", pageId.deviceId);
        return DATA_EXCEPTION_INTER;
    }

    // 当前BufferPool支持多文件，free流程按space粒度控制并发
    uint32_t fileId = *(uint32_t *)SpaceFileListGet(space, 0);
    DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, fileId);
    DB_ASSERT(file != NULL && DbFileIsOnline(file->ctrl));

    PageMgrT *pageMgr = SeGetPageMgrByFileType(seIns, file);
    if (pageMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get page mgr, file(%" PRIu32 ") type( %" PRIu32 ")", file->ctrl->id,
            file->ctrl->fileType);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    ret = SeGetFileHead(pageMgr, file, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "firstFile Id(%" PRIu32 ") get file head", file->ctrl->id);
        return ret;
    }
    PageHeadT *page = (PageHeadT *)((uint8_t *)file->head - sizeof(PageHeadT));
    ret = DbInterProcRWLatchW(&page->lock);
    if (ret != STATUS_OK_INTER) {
        SeLeaveFileHead(pageMgr, file, true);
        return ret;
    }

    DbFileT *targetFile = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, device->ctrl->fileId);
    ret = file->freePage(seIns, targetFile, pageId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Add to freeExtentsList of file(id:%" PRIu32 ", name:%s)", file->ctrl->id, file->ctrl->name);
    }

    DbInterProcRWUnlatchW(&page->lock);
    SeLeaveFileHead(pageMgr, file, true);
    return ret;
}

uint32_t SpaceGetIdByTypeImpl(SeInstanceT *seIns, SpaceTypeE spaceType)
{
    DB_POINTER2(seIns, seIns->db);
    StDatabaseT *db = seIns->db;
    switch (spaceType) {
        case SPACE_TYPE_SYSTEM:
            return db->core.systemSpaceId;
        case SPACE_TYPE_UNDO:
            return db->core.undoSpaceId;
        case SPACE_TYPE_USER_DEFAULT:
            return db->core.userSpaceId;
        default:
            DB_ASSERT(0);
    }
    return DB_INVALID_ID32;
}

// 设置页的压缩状态, 只有持久化模式才会使用
void SpaceSetPageToCompressStateImpl(PageHeadT *srcPage, bool isCompress)
{
    srcPage->isCompressed = isCompress;
}

// 页压缩函数，srcPage为待压缩页，destPage为压缩页，内存由外部管理, diskBlockSize为系统文件默认对齐大小
StatusInter DataPageCompress(SeInstanceT *seIns, PageHeadT *srcPage, uint32_t pageSize, PageHeadT **destPage, BufT *buf)
{
    DB_POINTER(srcPage);
    // 如果压缩的页是COMPRESSED, then pass.
    if (srcPage->isCompressed) {
        return STATUS_OK_INTER;
    }
    // 拷贝页头
    errno_t err = memcpy_s(*destPage, pageSize, (void *)srcPage, sizeof(PageHeadT));
    if (err != EOK) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER,
            "Copy page head when compressing. destMax(%" PRIu32 "), count(%" PRIu32 ")", pageSize,
            (uint32_t)sizeof(PageHeadT));
        return MEMORY_OPERATE_FAILED_INTER;
    }
    // 原始页压缩的起始位置
    uint8_t *src = (uint8_t *)srcPage + sizeof(PageHeadT);
    // 目的页接收的起始位置
    uint8_t *dest = (uint8_t *)*destPage + sizeof(PageHeadT);
    // 处理过后需要填充0的buff大小
    uint32_t payloadSize = 0;
    // 压缩前的大小，页尾也一起压缩
    uint32_t srcPageSize = pageSize - sizeof(PageHeadT);
    // 入参做为dest的大小，出参做为压缩后的大小
    uint32_t compressSize = pageSize - sizeof(PageHeadT);
    // 压缩page，对齐结论若压缩失败，则不压缩，不影响程序后续操作
    StatusInter ret = DbGetStatusInterErrno(DbDataCompress(dest, &compressSize, src, srcPageSize));
    if (ret != STATUS_OK_INTER) {
        DB_LOG_WARN_AND_SET_LASTERR(GMERR_CONFIG_ERROR, "Compress page with registered function.");
        return STATUS_OK_INTER;
    }
    uint32_t compressPageSize = sizeof(PageHeadT) + compressSize;
    // 打洞最小为diskBlockSize，若压缩完成后节约空间少于diskBlockSize，则不压缩
    if (pageSize - compressPageSize < seIns->db->diskBlockSize || compressSize >= srcPageSize) {
        return STATUS_OK_INTER;
    }
    // 字节对齐diskBlockSize，payload填充0
    uint32_t multiple = compressPageSize / (seIns->db->diskBlockSize);
    uint32_t remainder = compressPageSize % (seIns->db->diskBlockSize);
    multiple = remainder == 0 ? multiple : multiple + 1;
    payloadSize = multiple * seIns->db->diskBlockSize - compressPageSize;
    uint8_t *payloadDest = (uint8_t *)*destPage + compressPageSize;
    (void)memset_s((void *)payloadDest, payloadSize, 0, payloadSize);
    // 有必要压缩先更新一版pageHead，更新pageHead中的isCompressed为True，更新pageCompressedSize
    ((PageHeadT *)*destPage)->isCompressed = true;
    ((PageHeadT *)*destPage)->pageCompressedSize = (uint16_t)compressSize;
    buf->buf = (void *)*destPage;
    buf->size = multiple * seIns->db->diskBlockSize;
    return STATUS_OK_INTER;
}

StatusInter SpaceWriteBlock(SeInstanceT *seIns, PageHeadT *page, PageIdT pageId, uint32_t zoneId, bool isFlushAll)
{
    DB_ASSERT(pageId.deviceId < CtrlGroupGetSize(&seIns->db->deviceGroup));
    if (SeGetFlushCompMode(seIns) == COMP_MODE_PAGE && !SeEnableCompressPersistFile()) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "No compression function is registered.");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    DevCtrlT *devCtrl = SeGetDevCtrlById(seIns, pageId.deviceId);
    if (devCtrl == NULL) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "device is null, id (%" PRIu32 ")", pageId.deviceId);
        return DATA_EXCEPTION_INTER;
    }
    DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, devCtrl->fileId);
    if (file == NULL) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "datafile is null, id (%" PRIu32 ")", devCtrl->fileId);
        return DATA_EXCEPTION_INTER;
    }
    uint32_t blockSize = seIns->seConfig.pageSize * DB_KIBI;
    int64_t offset = SeGetFileDeviceOffset(seIns, file, devCtrl) + pageId.blockId * blockSize;
    BufT buf = {
        .buf = (void *)page,
        .size = blockSize,
    };
    StatusInter ret = STATUS_OK_INTER;
    if (SeGetFlushCompMode(seIns) == COMP_MODE_PAGE) {
        // 使用申请好的临时页，不必次次都申请，释放会在申请函数域去释放
        uint8_t *tmpPage = isFlushAll ? seIns->db->compBuf : seIns->ckptCtx->ckptCompBuf;
        ret = DataPageCompress(seIns, page, blockSize, (PageHeadT **)&tmpPage, &buf);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Compress page when writing block.");
            return ret;
        }
    }
    // 构造buf写入文件
    ret = SpcWriteDatafile(seIns, file, offset, file->handle[zoneId], buf);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "SpcWriteDatafile");
        return ret;
    }
    if (SeEnableCompressPersistFile() && blockSize - buf.size > 0) {
        // 计算压缩空间对文件进行打洞，打洞的位置应该是offset+压缩后完整的页大小，打洞的长度应该为正常页大小-压缩后完整页大小
        ret = VfdFallocate(seIns->vfdMgr, file->handle[zoneId], FALLOC_FL_PUNCH_HOLE | FALLOC_FL_KEEP_SIZE,
            offset + buf.size, blockSize - buf.size);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Fallocate page when writing block");
            return ret;
        }
    }

    return ret;
}

StatusInter DataPageDecompress(uint8_t *tmpPage, uint32_t pageSize, uint8_t *page)
{
    StatusInter ret = STATUS_OK_INTER;
    // 拷贝页头
    errno_t err = memcpy_s(page, pageSize, (void *)(PageHeadT *)tmpPage, sizeof(PageHeadT));
    if (err != EOK) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER,
            "Copy page head when decompressing. destMax(%" PRIu32 "), count(%" PRIu32 ")", pageSize,
            (uint32_t)sizeof(PageHeadT));
        return MEMORY_OPERATE_FAILED_INTER;
    }
    // 读出来的页查看页头，针对isCompressed取值不同做不同处理
    if (!((PageHeadT *)page)->isCompressed) {
        // 页头页尾都拷贝
        (void)memcpy_s((void *)page, pageSize, (void *)tmpPage, pageSize);
    } else {
        uint32_t compressSize = ((PageHeadT *)page)->pageCompressedSize;
        // 则根据读取被压缩的buffer，进行解压，得到的数据写到入参的page中
        // 拷贝页内容的起始终止位置
        uint8_t *src = tmpPage + sizeof(PageHeadT);
        uint8_t *dest = page + sizeof(PageHeadT);
        // 解压page
        uint32_t decompressSize = pageSize - sizeof(PageHeadT);
        ret = DbGetStatusInterErrno(DbDataDecompress(dest, &decompressSize, src, compressSize));
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        // 修改页头的状态为PAGE_USING，压缩数据大小为0
        ((PageHeadT *)page)->pageCompressedSize = 0;
        ((PageHeadT *)page)->isCompressed = false;
    }
    return STATUS_OK_INTER;
}

StatusInter SpaceReadBlock(SeInstanceT *seIns, int32_t handle, PageIdT pageId, uint8_t *page)
{
    DB_ASSERT(pageId.deviceId < CtrlGroupGetSize(&seIns->db->deviceGroup));
    DevCtrlT *devCtrl = SeGetDevCtrlById(seIns, pageId.deviceId);
    DB_ASSERT(devCtrl->fileId < seIns->seConfig.dbFilesMaxCnt);
    DbFileT *df = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, devCtrl->fileId);
    if (df == NULL) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "file not exist, id:%" PRIu32, devCtrl->fileId);
        return DATA_EXCEPTION_INTER;
    }
    int64_t offset = SeGetFileDeviceOffset(seIns, df, devCtrl) + pageId.blockId * SIZE_K(seIns->seConfig.pageSize);
    uint32_t pageSize = seIns->seConfig.pageSize * DB_KIBI;
    StatusInter ret = STATUS_OK_INTER;
    BufT buf = {0};
    buf.size = pageSize;
    // 压缩模式为page，恢复只取决于文件中压缩的模式
    if (SeGetFileCompMode(seIns) == COMP_MODE_PAGE) {
        // 使用申请好的临时页，不必次次都申请，释放会在申请函数域去释放
        uint8_t *tmpPage = seIns->db->compBuf;
        // 用临时页取页大小的数据
        buf.buf = tmpPage;
        ret = SpcReadDatafile(seIns, df, handle, offset, buf);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        ret = DataPageDecompress(tmpPage, pageSize, page);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    } else {
        buf.buf = page;
        ret = SpcReadDatafile(seIns, df, handle, offset, buf);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        if (((PageHeadT *)page)->isCompressed) {
            SE_LAST_ERROR(DATA_EXCEPTION_INTER, "Compressed page should not appear.");
            return DATA_EXCEPTION_INTER;
        }
    }
    ret = SeCheckPageDigest(seIns, page, pageSize);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "check page digest, fileName(%s), pageId(%" PRIu32 ", %" PRIu32 ")", df->ctrl->name,
            pageId.deviceId, pageId.blockId);
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter SpaceLoadPageFromDisk(SeInstanceT *seIns, PageIdT pageId, void *page)
{
    if (pageId.deviceId >= CtrlGroupGetSize(&seIns->db->deviceGroup)) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "Load buf page, device id %" PRIu32, pageId.deviceId);
        return INVALID_PARAMETER_VALUE_INTER;
    }
    DevCtrlT *deviceCtrl = SeGetDevCtrlById(seIns, pageId.deviceId);
    if (deviceCtrl->fileId >= CtrlGroupGetSize(&seIns->db->fileGroup)) {
        SE_LAST_ERROR(
            INVALID_PARAMETER_VALUE_INTER, "Unexpected deviceCtrl, fileId of deviceCtrl: %" PRIu32, deviceCtrl->fileId);
        return INVALID_PARAMETER_VALUE_INTER;
    }
    DbFileT *df = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, deviceCtrl->fileId);
    // if multi-zone enable or gmserver -r to specify datafilePath, handle need to set properly.
    StatusInter ret = STATUS_OK_INTER;
    uint32_t zoneId = DB_INVALID_ZONE_ID;
    do {
        zoneId = SeGetMainZoneId(seIns);
        // 文件完整性校验放到了redo重演后，所以redo重演过程中，可能会传进来无效的文件 handle
        if (df->handle[zoneId] == DB_INVALID_FD) {
            SE_LAST_ERROR(NO_DATA_FILE_NOT_EXIST, "file handle unexpect");
            return NO_DATA_FILE_NOT_EXIST;
        }
        ret = SpaceReadBlock(seIns, df->handle[zoneId], pageId, page);
        if (ret == STATUS_OK_INTER || !seIns->seConfig.fixPersistEnable) {
            break;
        }
        SE_WARN(ret, "Read block from space in bufferpool, zone(%" PRIu32 ") then retry", zoneId);
        ret = SeTakeZoneOffline(seIns, zoneId);
    } while (ret != NO_DATA_INTER);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "read disk block.");
        SeSetStorageEmergency(seIns, "read disk block emergency");
        return ret;
    }
    return ret;
}

StatusInter SpaceReadBlockToPage(SeInstanceT *seIns, PageIdT pageId, void *page)
{
    StatusInter ret = STATUS_OK_INTER;
    if (!SpaceCompressionIsEnable(seIns)) {  // 增量无压缩区从持久化文件加载
        ret = SpaceLoadPageFromDisk(seIns, pageId, page);
    } else {  // 有压缩区从压缩区加载
        ret = SeVirtualDiskReadBlock(seIns, pageId, page);
    }
    DbRWLatchInit(&((PageHeadT *)page)->lock);
    return ret;
}

StatusInter SpaceReadDeviceById(SeInstanceT *seIns, int32_t handle, uint32_t deviceId, uint8_t *device)
{
    DevCtrlT *devCtrl = SeGetDevCtrlById(seIns, deviceId);
    DbFileT *df = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, devCtrl->fileId);
    if (df == NULL) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "df not exist, id:%" PRIu32, devCtrl->fileId);
        return DATA_EXCEPTION_INTER;
    }
    int64_t deviceOffset = SeGetFileDeviceOffset(seIns, df, devCtrl);
    BufT buf = {0};
    buf.size = seIns->seConfig.deviceSize * DB_KIBI;
    buf.buf = device;
    StatusInter ret = SpcReadDatafile(seIns, df, handle, deviceOffset, buf);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Read device from file, deviceOffset(%" PRId64 "), handle(%" PRIu32 ").", deviceOffset, handle);
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter SpaceDirPrepare(SeInstanceT *seIns, const char *path)
{
    if (path != NULL) {
        if (!DbDirExist(path)) {
            SE_LAST_ERROR(DIR_OPERATE_FAILED_INTER, "specified dir %s not exist", path);
            return DIR_OPERATE_FAILED_INTER;
        }
    } else {
        if (seIns->seConfig.ctrlFileDirPath[0] == NULL || !DbDirExist(seIns->seConfig.ctrlFileDirPath[0])) {
            SE_LAST_ERROR(
                DIR_OPERATE_FAILED_INTER, "default ctrl dir %s not exist", seIns->seConfig.ctrlFileDirPath[0]);
            return DIR_OPERATE_FAILED_INTER;
        }
        if (seIns->seConfig.dataFileDirPath[0] == NULL || !DbDirExist(seIns->seConfig.dataFileDirPath[0])) {
            SE_LAST_ERROR(
                DIR_OPERATE_FAILED_INTER, "default data dir %s not exist", seIns->seConfig.dataFileDirPath[0]);
            return DIR_OPERATE_FAILED_INTER;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter SpaceCtrlFilePrepare(SeInstanceT *seIns, const char *path)
{
    uint32_t zoneId = DB_RESERVE_HANDLE_INDEX;
    const char *dir = path == NULL ? SeGetMainDataDirPath(seIns) : path;
    char filePath[DB_MAX_WHOLE_PATH] = {0};
    StatusInter ret = SeFileConstructPath(dir, seIns->seConfig.ctrlFileName, filePath, DB_MAX_WHOLE_PATH);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Prepare ctrl file");
        return ret;
    }
    if (DbFileExist(filePath)) {
        ret = SeFileOpen(filePath, SeGetFileOpExtraFlag(seIns), &seIns->db->ctrlFile.handle[zoneId]);
    } else {
        ret = SeFileCreate(filePath, SeGetFileOpExtraFlag(seIns), &seIns->db->ctrlFile.handle[zoneId]);
    }
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Open ctrl file, file path %s", path);
        return ret;
    }
    return STATUS_OK_INTER;
}

static StatusInter SpaceDataFileOperate(
    SeInstanceT *seIns, DbFileT *df, const char *realPath, char *filePath, bool isOpen)
{
    StatusInter ret = SpcGetDataFileFullPath(seIns, df, realPath, filePath);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "perpare data file full path");
        return ret;
    }
    uint32_t zoneId = SeIsBufpoolAndDumemCoexist(seIns) ? SeGetMainZoneId(seIns) : DB_RESERVE_HANDLE_INDEX;
    if (DbFileExist(filePath)) {
        ret = VfdFileOpen(seIns->vfdMgr, filePath, SeGetFileOpExtraFlag(seIns), &df->handle[zoneId]);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Open datafile in preparing datafile.");
            return ret;
        }
    } else if (!isOpen) {
        // DB is not in recovery status, but in flush mode.
        ret = VfdFileCreate(seIns->vfdMgr, filePath, SeGetFileOpExtraFlag(seIns), &df->handle[zoneId]);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Create datafile in data flushing process.");
            return ret;
        }
    }
    return ret;
}

StatusInter SpaceDataFilePrepare(SeInstanceT *seIns, const char *path, bool isOpen)
{
    StatusInter ret = STATUS_OK_INTER;
    const char *realPath = path;
    if (SeIsStringEmpty(path)) {
        realPath = SeGetMainDataDirPath(seIns);
    }
    uint32_t zoneId = SeIsBufpoolAndDumemCoexist(seIns) ? SeGetMainZoneId(seIns) : DB_RESERVE_HANDLE_INDEX;
    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->fileGroup); i++) {
        DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, i);
        DB_ASSERT(file->handle[zoneId] == DB_INVALID_FD);
        // offline 或者 creating 状态都不用open，否则可能会打开一个空文件，或者只有文件头的文件，在后续校验时失败
        if (!DbFileIsOnline(file->ctrl)) {
            continue;
        }
        char filePath[DB_MAX_WHOLE_PATH] = {0};
        ret = SpaceDataFileOperate(seIns, file, realPath, filePath, isOpen);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Operate datafile, file path %s", filePath);
            return ret;
        }
        if (!isOpen) {
            continue;
        }
        SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, file->ctrl->spaceId);
        ret = SpaceAddFile(space, file->ctrl->id);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    // load文件时才校验文件头尾
    if (!isOpen) {
        return ret;
    }
    // 这里不用检查creating的文件，undo回滚时会将其删掉
    ret = SpcCheckDataFileHeadAndTail(seIns, zoneId, realPath, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "check file stat and head");
        return ret;
    }
    return ret;
}

static StatusInter SpaceFilePrepare(SeInstanceT *seIns, const char *path)
{
    StatusInter ret = SeTakeZoneOnline(seIns, DB_RESERVE_HANDLE_INDEX);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    ret = SpaceDirPrepare(seIns, path);
    if (ret != STATUS_OK_INTER) {
        SeTakeZoneOffline(seIns, DB_RESERVE_HANDLE_INDEX);
        return ret;
    }
    ret = SpaceCtrlFilePrepare(seIns, path);
    if (ret != STATUS_OK_INTER) {
        SeTakeZoneOffline(seIns, DB_RESERVE_HANDLE_INDEX);
        return ret;
    }
    ret = SpaceDataFilePrepare(seIns, path, false);
    if (ret != STATUS_OK_INTER) {
        SeTakeZoneOffline(seIns, DB_RESERVE_HANDLE_INDEX);
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter SpaceWriteDevCompressOnPage(
    SeInstanceT *seIns, uint8_t *devData, uint32_t devId, uint32_t zoneIndex, DbFileItemCursorT *itemCursor)
{
    DevCtrlT *devCtrl = SeGetDevCtrlById(seIns, devId);
    uint32_t usePageNum = SE_INVALID_BLOCK_ID;
    StatusInter ret = SeGetFileDeviceBlockHwm(seIns, devCtrl, &usePageNum);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Fetch Device (%" PRIu32 " blockHwm)", devCtrl->id);
        return ret;
    }
    uint32_t pageSize = seIns->seConfig.pageSize * DB_KIBI;
    // 遍历Device中使用的Page，压缩page
    uint8_t *page = NULL;
    for (uint32_t j = 0; j < usePageNum; j++) {
        page = devData + (uint64_t)(j * pageSize);
        // 先计算 block 的 CRC ，再写入文件
        ret = SeUpdateDataPageDigest(seIns, page, pageSize);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        PageIdT addr = {.deviceId = devId, .blockId = j};
        ret = SpaceWriteBlock(seIns, (PageHeadT *)page, addr, zoneIndex, true);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, devCtrl->fileId);
    // 没使用到的block，整个打洞，打洞的长度为deviceSize - 使用的页数*页大小
    uint64_t deviceCompressSize = (uint64_t)seIns->seConfig.deviceSize * DB_KIBI - (uint64_t)usePageNum * pageSize;
    // 打洞的起始位置为初始偏移位置 + 使用的页数*页大小
    uint64_t fallocateOffset = SeGetFileDeviceOffset(seIns, file, devCtrl) + (uint64_t)usePageNum * pageSize;
    // 打洞前判断是否需要打洞
    if (deviceCompressSize > 0) {
        ret = VfdFallocate(seIns->vfdMgr, file->handle[zoneIndex], FALLOC_FL_PUNCH_HOLE | FALLOC_FL_KEEP_SIZE,
            fallocateOffset, deviceCompressSize);
    }
    itemCursor->dataCursor += seIns->seConfig.deviceSize * DB_KIBI;
    return ret;
}

void SpaceCompDevice(uint8_t *devData, uint8_t *destDev, uint32_t srcSize, uint32_t *destSize, BufT *buf)
{
    uint8_t *destData = destDev;
    uint8_t *srcData = devData;
    // 做数据压缩，如果压缩失败，也不必返回错误码，以不压缩的方式写入
    Status ret = DbDataCompress(destData, destSize, srcData, srcSize);
    if (ret == GMERR_OK) {
        // 压缩成功后，将压缩后的大小写入devHead，用于恢复的时候解压
        buf->buf = (void *)destDev;
        buf->size = *destSize;
    } else {
        *destSize = 0;
        DB_LOG_WARN_AND_SET_LASTERR(GMERR_CONFIG_ERROR, "Compress device with registered function.");
    }
}

StatusInter SpaceWriteDevCompressOnDevice(
    SeInstanceT *seIns, uint8_t *devData, uint32_t devId, uint32_t zoneIndex, DbFileItemCursorT *itemCursor)
{
    DevCtrlT *devCtrl = SeGetDevCtrlById(seIns, devId);
    // 按device压缩但是没有注册压缩函数
    if (!SeEnableCompressPersistFile()) {
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "No compression function is registered.");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    uint32_t useBlockNum = SE_INVALID_BLOCK_ID;
    StatusInter ret = SeGetFileDeviceBlockHwm(seIns, devCtrl, &useBlockNum);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Fetch Device (%" PRIu32 " blockHwm)", devCtrl->id);
        return ret;
    }
    // 给device中的每个页更新CRC值
    uint32_t blockSize = seIns->seConfig.pageSize * DB_KIBI;
    ret = SeUpdateMutiDataPageDigest(seIns, devData, blockSize, useBlockNum);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "update multi page digest");
        return ret;
    }
    // 计算压缩前的大小
    uint32_t srcSize = useBlockNum * blockSize;
    // 入参为压缩前tmpDev的大小，出参为压缩后的大小
    uint32_t destSize = srcSize;
    // 为压缩的dev申请临时内存
    uint8_t *tmpDev = seIns->db->compBuf;
    // 压缩后的数据指针与大小会被更新到buf中
    BufT buf = {
        .buf = devData,
        .size = seIns->seConfig.deviceSize * DB_KIBI,
    };
    SpaceCompDevice(devData, tmpDev, srcSize, &destSize, &buf);
    // 如果压缩失败，buf为未压缩的数据；如果压缩成功，buf为压缩后的数据
    ret = FlushNextCompDevItem(seIns, devCtrl->fileId, zoneIndex, itemCursor, buf);
    if (ret != STATUS_OK_INTER) {
        DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, devCtrl->fileId);
        SE_ERROR(ret, "Write data file(name:%s, handle:%" PRId32 ")", file->ctrl->name, file->handle[zoneIndex]);
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter SpaceWriteDevNoCompress(
    SeInstanceT *seIns, uint8_t *devData, uint32_t devId, uint32_t zoneId, DbFileItemCursorT *itemCursor)
{
    StatusInter ret;
    DevCtrlT *devCtrl = SeGetDevCtrlById(seIns, devId);
    uint32_t blockSize = seIns->seConfig.pageSize * DB_KIBI;
    // 计算device中每个page的CRC值
    uint32_t useBlockNum = SE_INVALID_BLOCK_ID;
    ret = SeGetFileDeviceBlockHwm(seIns, devCtrl, &useBlockNum);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Fetch Device (%" PRIu32 " blockHwm)", devCtrl->id);
        return ret;
    }
    ret = SeUpdateMutiDataPageDigest(seIns, devData, blockSize, useBlockNum);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, devCtrl->fileId);
    if (file == NULL) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "file not exist, id:%" PRIu32, devCtrl->fileId);
        return DATA_EXCEPTION_INTER;
    }
    BufT buf = {
        .buf = (void *)devData,
        .size = seIns->seConfig.deviceSize * DB_KIBI,
    };
    ret = SpcWriteDatafile(seIns, file, SeGetFileDeviceOffset(seIns, file, devCtrl), file->handle[zoneId], buf);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "SpaceWriteDev");
        return ret;
    }
    itemCursor->dataCursor += seIns->seConfig.deviceSize * DB_KIBI;
    return STATUS_OK_INTER;
}

StatusInter SpaceWriteDev(
    SeInstanceT *seIns, uint8_t *devData, uint32_t devId, uint32_t zoneId, DbFileItemCursorT *itemCursor)
{
    DB_POINTER2(seIns, devData);
    DB_ASSERT(devId < CtrlGroupGetSize(&seIns->db->deviceGroup));
    StatusInter ret = STATUS_OK_INTER;
    switch (SeGetFlushCompMode(seIns)) {
        case COMP_MODE_NONE:
            // 不压缩
            ret = SpaceWriteDevNoCompress(seIns, devData, devId, zoneId, itemCursor);
            break;
        case COMP_MODE_DEVICE:
            // device 压缩
            ret = SpaceWriteDevCompressOnDevice(seIns, devData, devId, zoneId, itemCursor);
            break;
        case COMP_MODE_PAGE:
            // page 压缩
            ret = SpaceWriteDevCompressOnPage(seIns, devData, devId, zoneId, itemCursor);
            break;
        default:
            ret = DATA_EXCEPTION_INTER;
            break;
    }
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Space write device (device id:%" PRId32 ", zone id:%" PRId32 ", compress mode:%" PRId32 ")",
            devId, zoneId, SeGetFlushCompMode(seIns));
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter SpaceAllocTempBufForComp(SeInstanceT *seIns, SePersistCompModeE compressMode)
{
    DB_POINTER(seIns);

    if (seIns->db->compBuf != NULL) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "Buffer not empty before flushing disk.");
        return DATA_EXCEPTION_INTER;
    }

    // 不压缩
    if (compressMode == COMP_MODE_NONE) {
        return STATUS_OK_INTER;
    }
    uint32_t allocSize = 0;
    if (compressMode == COMP_MODE_PAGE) {
        // 页压缩
        allocSize = seIns->seConfig.pageSize * DB_KIBI;
    } else if (compressMode == COMP_MODE_DEVICE) {
        // device压缩
        allocSize = seIns->seConfig.deviceSize * DB_KIBI;
    } else {
        SE_LAST_ERROR(
            DATA_EXCEPTION_INTER, "Alloc temp buffer, unexpected compressMode=%" PRIu32 ".", (uint32_t)compressMode);
        return DATA_EXCEPTION_INTER;
    }

    seIns->db->compBuf = DbDynMemCtxAlloc(seIns->seServerMemCtx, allocSize);
    if (seIns->db->compBuf == NULL) {
        // persCompMode压缩种类异常，或DbDynMemCtxAlloc申请内存失败
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "Alloc temp buffer for compress, alloc_size=%" PRIu32 ".", allocSize);
        return OUT_OF_MEMORY_INTER;
    }
    return STATUS_OK_INTER;
}

void SpaceFreeTempBufForComp(SeInstanceT *seIns)
{
    DbDynMemCtxFree(seIns->seServerMemCtx, seIns->db->compBuf);
    seIns->db->compBuf = NULL;
}

void SpaceKeepThreadAlive(SeInstanceT *seIns, uint64_t *splitStartTime)
{
    DB_POINTER2(seIns, splitStartTime);
    if (DbExceedTime(*splitStartTime, SPACE_DEFAULT_SPILT_TIME)) {
        if (!seIns->seConfig.seKeepThreadAlive(seIns->dbInstance)) {
            SE_LAST_ERROR(INTERNAL_ERROR_INTER, "Keep thread alive when lfs keep thread alive.");
        }
        *splitStartTime = DbClockGetTsc();
    }
}

StatusInter PageMgrExportDevice(SeInstanceT *seIns, DbDeviceT *device, uint8_t **devData)
{
    if (seIns->storageType == SE_DURABLE_MEMDATA) {
        DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(seIns->devMgrShm);
        if (devMgr == NULL) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get device mgr, shm(%" PRIu32 ", %" PRIu32 ")",
                seIns->devMgrShm.offset, seIns->devMgrShm.segId);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        *devData = DevGetEntry(devMgr, device->ctrl->id);
        return STATUS_OK_INTER;
    }
    uint32_t useBlockNum = SE_INVALID_BLOCK_ID;
    StatusInter ret = SeGetFileDeviceBlockHwm(seIns, device->ctrl, &useBlockNum);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Fetch Device (%" PRIu32 " blockHwm)", device->ctrl->id);
        return ret;
    }
    uint32_t pageSize = SIZE_K(seIns->seConfig.pageSize);
    uint8_t *buffer = (uint8_t *)DbDynMemCtxAlloc(seIns->seServerMemCtx, SIZE_K(seIns->seConfig.deviceSize));
    if (buffer == NULL) {
        return OUT_OF_MEMORY_INTER;
    }
    PageIdT pageAddr = SE_INVALID_PAGE_ADDR;
    uint8_t *cursor = NULL;
    for (uint32_t i = 0; i < useBlockNum; ++i) {
        pageAddr = (PageIdT){device->ctrl->id, i};
        ret = SeGetPage(seIns->pageMgr, pageAddr, &cursor, ENTER_PAGE_ACTIVE, false);
        if (ret == STATUS_OK_INTER) {
            (void)memcpy_s(buffer + i * pageSize, pageSize, cursor, pageSize);
            SeLeavePage(seIns->pageMgr, pageAddr, false);
        } else if (ret == NO_DATA_INTER) {
            ret = SeVirtualDiskReadBlock(seIns, pageAddr, buffer + i * pageSize);
        }
        if (ret != STATUS_OK_INTER) {
            DbDynMemCtxFree(seIns->seServerMemCtx, buffer);
            return ret;
        }
    }
    *devData = buffer;
    return STATUS_OK_INTER;
}

void PageMgrReleaseDevice(SeInstanceT *seIns, uint8_t *devData)
{
    if (seIns->storageType == SE_DURABLE_MEMDATA) {
        return;
    }
    DbDynMemCtxFree(seIns->seServerMemCtx, devData);
}

static StatusInter ThreadCompressDevice(DbCompressCtxT *compCtx, DbDeviceT *device, uint32_t deviceSize)
{
    uint32_t useBlockNum = SE_INVALID_BLOCK_ID;
    StatusInter ret = SeGetFileDeviceBlockHwm(compCtx->seIns, device->ctrl, &useBlockNum);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Fetch Device (%" PRIu32 " blockHwm)", device->ctrl->id);
        return ret;
    }
    uint8_t *devData = NULL;
    ret = PageMgrExportDevice(compCtx->seIns, device, &devData);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    uint32_t pageSize = compCtx->seIns->seConfig.pageSize * DB_KIBI;
    ret = SeUpdateMutiDataPageDigest(compCtx->seIns, devData, pageSize, useBlockNum);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "update multi page digest");
        PageMgrReleaseDevice(compCtx->seIns, devData);
        return ret;
    }
    BufT devDataBuf = {.buf = devData, .size = deviceSize};
    uint32_t srcSize = useBlockNum * pageSize;
    uint32_t destSize = srcSize;
    // 如果压缩成功，devDataBuf为压缩后的数据；压缩失败，devDataBuf为devMgr中未压缩的device
    SpaceCompDevice(devData, compCtx->compressedDev, srcSize, &destSize, &devDataBuf);
    // 压缩失败，直接拷贝未压缩的device
    if (destSize == 0) {
        if (memcpy_s(compCtx->compressedDev, deviceSize, devData, deviceSize) != EOK) {
            SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "Memcpy no compress device data");
            PageMgrReleaseDevice(compCtx->seIns, devData);
            return MEMORY_OPERATE_FAILED_INTER;
        }
    }
    compCtx->compressSize = devDataBuf.size;
    PageMgrReleaseDevice(compCtx->seIns, devData);
    return STATUS_OK_INTER;
}

void *CompressDeviceProc(void *param)
{
    DbCompressCtxT *compCtx = (DbCompressCtxT *)param;
    DbSetServerThreadFlag();
    uint32_t deviceSize = compCtx->seIns->seConfig.deviceSize * DB_KIBI;
    compCtx->compressedDev = DbDynMemCtxAlloc(compCtx->seIns->seServerMemCtx, deviceSize);
    if (compCtx->compressedDev == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "Alloc compress buffer, size is (%" PRIu32 ")", deviceSize);
        compCtx->threadRet = OUT_OF_MEMORY_INTER;
        return NULL;
    }
    DbFileT *file = (DbFileT *)CtrlGroupGetItem(&compCtx->seIns->db->fileGroup, compCtx->fileId);
    uint32_t devFileIndex = compCtx->startDevFileIndex;
    while (compCtx->isRunning) {
        // 该线程所有任务已完成
        if (devFileIndex >= file->ctrl->deviceNum && DbAtomicGet(&compCtx->compressStat) == COMP_STATE_FLUSHED) {
            break;
        }
        if (DbAtomicGet(&compCtx->compressStat) == COMP_STATE_COMPRESSED) {
            DbSleep(SE_COMPRESS_PROC_INTERVAL);
            // 这里需要continue，防止主线程需要停掉压缩线程
            continue;
        }
        DbDeviceT *device = SeFindDeviceByFileIndex(&compCtx->seIns->db->deviceGroup, compCtx->fileId, devFileIndex);
        if (device == NULL || !device->ctrl->online) {
            // 当前device已归还，不用刷，寻找下一个device
            // 4个device为一组，4个线程同时压缩
            devFileIndex += SE_COMPRESS_THREAD_NUM;
            continue;
        }
        StatusInter ret = ThreadCompressDevice(compCtx, device, deviceSize);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Thread compress device, id (%" PRIu32 ")", device->ctrl->id);
            compCtx->threadRet = ret;
            break;
        }
        DbAtomicSet(&compCtx->compressStat, COMP_STATE_COMPRESSED);
        // 4个device为一组，4个线程同时压缩
        devFileIndex += SE_COMPRESS_THREAD_NUM;
    }
    DbDynMemCtxFree(compCtx->seIns->seServerMemCtx, compCtx->compressedDev);
    return NULL;
}

static void DbCompressCtxInit(SeInstanceT *seIns, DbFileT *file, uint32_t threadIndex, DbCompressCtxT *compCtx)
{
    compCtx->seIns = seIns;
    compCtx->isRunning = true;
    DbAtomicSet(&compCtx->compressStat, COMP_STATE_FLUSHED);
    compCtx->fileId = file->ctrl->id;
    compCtx->startDevFileIndex = threadIndex;
    compCtx->compressedDev = NULL;
    compCtx->compressSize = 0;
    compCtx->threadRet = STATUS_OK_INTER;
    compCtx->threadHandle = SE_INVALID_THREAD_HANDLE;
}

static StatusInter SpaceStartCompressThreads(SeInstanceT *seIns, DbFileT *file, DbCompressCtxT *compCtx)
{
    for (uint32_t i = 0; i < SE_COMPRESS_THREAD_NUM; i++) {
        DbCompressCtxInit(seIns, file, i, &compCtx[i]);
        ThreadAttrsT attr = {.priority = THREAD_PRIORITY_MIDDLE,
            .type = DB_THREAD_JOINABLE,
            .entryFunc = CompressDeviceProc,
            .exitFunc = NULL,
            .entryArgs = (void *)&compCtx[i],
            .exitArgs = NULL,
            .stackSize = DB_DEFAULT_THREAD_STACK_SIZE,
            .bindCpuFlag = 0,
            .cpu = 0,
            .userAttr = NULL};
        int32_t size = sprintf_s(attr.name, DB_THREAD_NAME_MAX_LEN, "CompressProc_%u", i);
        if (size < 0) {
            SE_LAST_ERROR(INTERNAL_ERROR_INTER, "Sprintf_s thread name (%s), i(%" PRIu32 ")", attr.name, i);
            return INTERNAL_ERROR_INTER;
        }
        Status ret = DbThreadCreate(&attr, &compCtx[i].threadHandle);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Create load device thread.");
            return DbGetStatusInterErrno(ret);
        }
    }
    return STATUS_OK_INTER;
}

static StatusInter SpaceStopCompressThreads(DbCompressCtxT *compCtx)
{
    // 先回收所有的压缩线程
    StatusInter joinRet = STATUS_OK_INTER;
    for (uint32_t i = 0; i < SE_COMPRESS_THREAD_NUM; i++) {
        if (compCtx[i].isRunning && compCtx[i].threadHandle != SE_INVALID_THREAD_HANDLE) {
            compCtx[i].isRunning = false;
            Status ret = DbThreadJoin(compCtx[i].threadHandle, NULL);
            if (ret != GMERR_OK) {
                // join 一个线程失败时，不返回，继续join其他的线程
                DB_LOG_ERROR(ret, "Join thread, id (%" PRId32 ")", i);
                joinRet = DbGetStatusInterErrno(ret);
            }
            compCtx[i].threadHandle = SE_INVALID_THREAD_HANDLE;
        }
    }
    if (joinRet != STATUS_OK_INTER) {
        SE_ERROR(joinRet, "Join compress threads.");
        return joinRet;
    }
    // 检查压缩线程是否产生了错误码
    for (uint32_t i = 0; i < SE_COMPRESS_THREAD_NUM; i++) {
        if (compCtx[i].threadRet != STATUS_OK_INTER) {
            SE_ERROR(compCtx[i].threadRet, "Compress thread result is unexpected.");
            return compCtx[i].threadRet;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter SpaceMultiThreadsFlushDevices(
    SeInstanceT *seIns, DbFileT *file, uint32_t zoneId, DbFileItemCursorT *itemCursor)
{
    DbCompressCtxT compCtx[SE_COMPRESS_THREAD_NUM] = {0};
    // 启动多个线程
    StatusInter ret = SpaceStartCompressThreads(seIns, file, compCtx);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Start compress threads.");
        (void)SpaceStopCompressThreads(compCtx);
        return ret;
    }

    uint64_t splitStartTime = DbClockGetTsc();
    SpaceKeepThreadAlive(seIns, &splitStartTime);
    for (uint32_t curDevFileIndex = 0; curDevFileIndex < file->ctrl->deviceNum; curDevFileIndex++) {
        DbDeviceT *device = SeFindDeviceByFileIndex(&compCtx->seIns->db->deviceGroup, file->ctrl->id, curDevFileIndex);
        if (device == NULL || !device->ctrl->online) {
            // 归还的device，sizeArray留空，数据文件不留空
            DbSkptEmptyDevItem(seIns, SeGetFlushCompMode(seIns), itemCursor);
            continue;
        }
        uint32_t curThreadIndex = curDevFileIndex % SE_COMPRESS_THREAD_NUM;
        while (DbAtomicGet(&compCtx[curThreadIndex].compressStat) == COMP_STATE_FLUSHED) {
            DbSleep(SE_COMPRESS_PROC_INTERVAL);
            // 压缩线程产生错误码时，将错误返回上层
            if (compCtx[curThreadIndex].threadRet != STATUS_OK_INTER) {
                (void)SpaceStopCompressThreads(compCtx);
                return compCtx[curThreadIndex].threadRet;
            }
        }

        BufT devDataBuf = {.buf = compCtx[curThreadIndex].compressedDev, .size = compCtx[curThreadIndex].compressSize};
        ret = FlushNextCompDevItem(seIns, file->ctrl->id, zoneId, itemCursor, devDataBuf);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Write data file(name:%s, handle:%" PRId32 ")", file->ctrl->name, file->handle[zoneId]);
            (void)SpaceStopCompressThreads(compCtx);
            return ret;
        }

        DbAtomicSet(&compCtx[curThreadIndex].compressStat, COMP_STATE_FLUSHED);
        SpaceKeepThreadAlive(seIns, &splitStartTime);
    }

    // 回收压缩线程
    return SpaceStopCompressThreads(compCtx);
}

StatusInter SpaceFlushDevice(SeInstanceT *seIns, DbFileT *df, uint32_t zoneId, DbFileItemCursorT *itemCursor)
{
    // 开启了device压缩，并且这个文件的device数量大于1个时，才会触发多线程解压
    if (SeGetFlushCompMode(seIns) == COMP_MODE_DEVICE &&
        df->ctrl->deviceNum >= SE_COMPRESS_MULTI_THREAD_THRESHOLD_NUM) {
        return SpaceMultiThreadsFlushDevices(seIns, df, zoneId, itemCursor);
    }
    // 根据压缩模式，申请一块内存，用于暂存压缩数据
    StatusInter ret = SpaceAllocTempBufForComp(seIns, SeGetFlushCompMode(seIns));
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    DbDeviceT *device = NULL;
    uint64_t splitStartTime = DbClockGetTsc();
    SpaceKeepThreadAlive(seIns, &splitStartTime);
    for (uint32_t i = 0; i < df->ctrl->deviceNum; ++i) {
        device = SeFindDeviceByFileIndex(&seIns->db->deviceGroup, df->ctrl->id, i);
        if (device == NULL) {
            DbSkptEmptyDevItem(seIns, SeGetFlushCompMode(seIns), itemCursor);
            continue;
        }
        // 并发控制：由于全量刷盘时没有其他DML操作，此处操作是安全的。
        uint8_t *buf = NULL;
        ret = PageMgrExportDevice(seIns, device, &buf);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        DB_ASSERT(buf != NULL);
        ret = SpaceWriteDev(seIns, buf, device->ctrl->id, zoneId, itemCursor);
        PageMgrReleaseDevice(seIns, buf);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "SpaceWriteDev, device id %" PRIu32, device->ctrl->id);
            break;
        }
        SpaceKeepThreadAlive(seIns, &splitStartTime);
    }
    SpaceFreeTempBufForComp(seIns);
    return ret;
}

static StatusInter SpaceFlushDataFileByDevice(SeInstanceT *seIns, DbFileT *df, uint32_t zoneId)
{
    StatusInter ret = SpcWriteDataFileHead(seIns, df->handle[zoneId], seIns->db->flushCheckMagic, NULL);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Write data file head, zoneId=%" PRIu32 ",handle(%" PRId32 "), file name %s", zoneId,
            df->handle[zoneId], df->ctrl->name);
        return ret;
    }

    DbFileItemCursorT itemCursor = {0};
    ret = DbFileFlushItemCursorInit(seIns, df->ctrl, SeGetFlushCompMode(seIns), &itemCursor);
    if (ret != STATUS_OK_INTER) {
        SpaceFreeTempBufForComp(seIns);
        return ret;
    }
    ret = SpaceFlushDevice(seIns, df, zoneId, &itemCursor);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Flush data and calculate digest, datafile id(%" PRIu32 ").", df->ctrl->id);
        DbFileItemCursorUnInit(seIns, &itemCursor);
        return ret;
    }

    ret = DbFileFlushSizeArray(seIns, df->handle[zoneId], df->ctrl->id, SeGetFlushCompMode(seIns), &itemCursor);
    if (ret != STATUS_OK_INTER) {
        DbFileItemCursorUnInit(seIns, &itemCursor);
        return ret;
    }
    ret = UpdateDataFileDigestForZone(seIns, zoneId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Write datafile digest, zoneId=%" PRIu32 ",handle(%" PRId32 "), file name %s", zoneId,
            df->handle[zoneId], df->ctrl->name);
        DbFileItemCursorUnInit(seIns, &itemCursor);
        return ret;
    }
    ret = SpcWriteFileTailVfd(seIns, df->handle[zoneId], itemCursor.dataCursor, seIns->db->flushCheckMagic);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Write data file tail, zoneId=%" PRIu32 " , file name %s", zoneId, df->ctrl->name);
        DbFileItemCursorUnInit(seIns, &itemCursor);
        return ret;
    }

    // 覆盖刷时，文件尾可能有残余数据
    ret = VfdFtruncate(seIns->vfdMgr, df->handle[zoneId], (int64_t)(itemCursor.dataCursor + sizeof(PersFileTailT)));
    DbFileItemCursorUnInit(seIns, &itemCursor);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    // 全量刷盘，将压缩方式在此处写入core只需要写入一次
    seIns->db->core.compMode = SeGetFlushCompMode(seIns);
    return STATUS_OK_INTER;
}

static StatusInter SpaceFlushDataFile(SeInstanceT *seIns, DbFileT *df, uint32_t zoneId)
{
    PageMgrT *pageMgr = (PageMgrT *)seIns->pageMgr;
    switch (pageMgr->type) {
        case SE_BUFFER_POOL:
            if (!SpaceCompressionIsEnable(seIns)) {
                return STATUS_OK_INTER;
            }
            return SpaceFlushDataFileByDevice(seIns, df, zoneId);
        case SE_DURABLE_MEMDATA:
            return SpaceFlushDataFileByDevice(seIns, df, zoneId);
        default:
            SE_LAST_ERROR(
                FEATURE_NOT_SUPPORTED_INNER, "page mgr type %" PRIu32 " not supported flushing", pageMgr->type);
            return FEATURE_NOT_SUPPORTED_INNER;
    }
    return STATUS_OK_INTER;
}

StatusInter SpaceFlushOneZone(SeInstanceT *seIns, uint32_t zoneId)
{
    DB_POINTER(seIns);
    StatusInter ret = STATUS_OK_INTER;
    SeMultiZoneCtrlT zoneCtrl = seIns->db->zoneCtrl;
    if (SeCanSkipOfflineZone(seIns, &zoneCtrl, zoneId)) {
        // 当分区句柄已经被释放或损坏，不会继续向副分区落盘
        SE_WARN(ret, "Zone(%" PRIu32 ") released then stop flushing", zoneId);
        return STATUS_OK_INTER;
    }
    // 每全量刷一次盘，序列号加一
    // 用于重启校验部分刷盘后的场景
    seIns->db->flushCheckMagic++;
    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->fileGroup); i++) {
        DbFileT *df = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, i);
        if (!DbFileIsOnline(df->ctrl)) {
            continue;
        }
        ret = SpaceFlushDataFile(seIns, df, zoneId);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Write data file, file=%s, zoneId=%" PRIu32, df->ctrl->name, zoneId);
            return ret;
        }
    }
    ret = DbFlushCtrlFile(seIns, zoneId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Write ctrl file, zoneId=%" PRIu32, zoneId);
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter SeSpaceFlushAllZone(SeInstanceT *seIns)
{
    DB_POINTER(seIns);
    // 恢复流程会更新数据页，所以需要再算一次文件摘要
    if (seIns->seConfig.shaCheckEnable) {
        CkptEnableDigestUpdate(seIns);
    }
    // 如果是重启流程（boot线程），选择CKPT_MODE_BOOT_FULL模式，trigger时不会喂狗
    CkptModeT ckptMode = DbRecoveryDoing(seIns) ? CKPT_MODE_BOOT_FULL : CKPT_MODE_FULL;
    CkptSetEnable(seIns, true);
    StatusInter ret = DbGetStatusInterErrno(CkptTrigger(seIns, ckptMode, true, 0));
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    // 如果只有一个分区，不用保证两个分区的二进制一致性
    if (seIns->seConfig.multiZoneNum == 1) {
        return STATUS_OK_INTER;
    }
    // 为应对副区损坏，并且两个分区的一致性，需要进行全量刷盘
    for (uint32_t zoneId = 0; zoneId < seIns->seConfig.multiZoneNum; zoneId++) {
        ret = SpaceFlushOneZone(seIns, zoneId);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Flush zone, zoneId=%" PRIu32 ".", zoneId);
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

static void ResetRedoPointInCore(
    SeInstanceT *seIns, RedoPointT *tmpTruncPoint, RedoPointT *tmpLrpPoint, RedoPointT *tmpDigestRedoPoint)
{
    // 如果是增量模式的全量刷盘，不会刷redo文件，所以刷下去的core文件truncatePoint和lrpPoint都需要重置
    if (SeGetPersistMode() != PERSIST_INCREMENT) {
        return;
    }
    *tmpTruncPoint = seIns->db->core.truncPoint;
    *tmpLrpPoint = seIns->db->core.lrpPoint;
    *tmpDigestRedoPoint = seIns->db->core.digestRedoPoint;
    seIns->db->core.truncPoint = RedoGenInitPoint();
    seIns->db->core.lrpPoint = RedoGenInitPoint();
    seIns->db->core.digestRedoPoint = RedoGenInitPoint();
}

static void RestoreRedoPointInCore(
    SeInstanceT *seIns, RedoPointT *tmpTruncPoint, RedoPointT *tmpLrpPoint, RedoPointT *tmpDigestRedoPoint)
{
    if (SeGetPersistMode() != PERSIST_INCREMENT) {
        return;
    }
    seIns->db->core.truncPoint = *tmpTruncPoint;
    seIns->db->core.lrpPoint = *tmpLrpPoint;
    seIns->db->core.digestRedoPoint = *tmpDigestRedoPoint;
}

static StatusInter DoFlushCtrlFile(SeInstanceT *seIns)
{
    // 如果是增量模式的全量刷盘，不会刷redo文件，所以刷下去的core文件truncatePoint和lrpPoint都需要重置
    RedoPointT tmpTruncPoint = {0};
    RedoPointT tmpLrpPoint = {0};
    RedoPointT tmpDigestRedoPoint = {0};
    ResetRedoPointInCore(seIns, &tmpTruncPoint, &tmpLrpPoint, &tmpDigestRedoPoint);
    StatusInter ret = DbFlushCtrlFile(seIns, DB_RESERVE_HANDLE_INDEX);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "flush ctrl file");
        RestoreRedoPointInCore(seIns, &tmpTruncPoint, &tmpLrpPoint, &tmpDigestRedoPoint);
        return ret;
    }
    ret = DbGetStatusInterErrno(UpdateCtrlFileDigest(seIns, false));
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "update ctrl file digest");
        RestoreRedoPointInCore(seIns, &tmpTruncPoint, &tmpLrpPoint, &tmpDigestRedoPoint);
        return ret;
    }
    RestoreRedoPointInCore(seIns, &tmpTruncPoint, &tmpLrpPoint, &tmpDigestRedoPoint);
    return STATUS_OK_INTER;
}

Status DoFlushAll(SeInstanceT *seIns, const char *path, SeRunCtxHdT seRunCtx, bool binCheck)
{
    uint64_t startTime = DbRdtsc();
    // 每全量刷一次盘，序列号加一
    // 用于重启校验部分刷盘后的场景
    seIns->db->flushCheckMagic++;

    StatusInter ret = SpaceFilePrepare(seIns, path);
    if (ret != STATUS_OK_INTER) {
        return DbGetExternalErrno(ret);
    }
    // flush data file
    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->fileGroup); i++) {
        DbFileT *df = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, i);
        if (!DbFileIsOnline(df->ctrl)) {
            continue;
        }
        ret = SpaceFlushDataFile(seIns, df, DB_RESERVE_HANDLE_INDEX);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Flush data file, Id=%" PRIu32 ".", i);
            goto EXIT;
        }
        CRASHPOINT(DB_CRASH_EVENT_DEMAND_FLUSH, DB_CRASH_STATUS_PERSIST_DEMAND_FLUSH_DATAFILE);
    }
    ret = DoFlushCtrlFile(seIns);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }
EXIT:
    SeTakeZoneOffline(seIns, DB_RESERVE_HANDLE_INDEX);
    DB_LOG_INFO("flush to path:%s, took: %" PRIu64 " ms", path, DbToMseconds(DbRdtsc() - startTime));
    return DbGetExternalErrno(ret);
}

static Status DoTriggerCkpt(SeInstanceT *seIns, const char *path, HeapResetAndRestoreCtxT *ctx)
{
    if (ctx != NULL) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "Bin consistency check with path: %s", path);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (seIns->seConfig.shaCheckEnable) {
        CkptEnableDigestUpdate(seIns);
    }
    Status ret = CkptTrigger(seIns, CKPT_MODE_FULL, true, 0);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Trigger checkpoint.");
        return ret;
    }
    return GMERR_OK;
}

// 入参SeFlushCfgT中的超时时间为该函数的总超时时间，0表示一直等待
Status DoTriggerCkptGentle(SeRunCtxHdT seRunCtx, SeFlushCfgT *flushCfg)
{
    DB_POINTER2(seRunCtx, flushCfg);
    uint64_t startTime = DbClockGetTsc();
    uint64_t timeoutMs = flushCfg->timeoutMs == 0 ? DB_MAX_UINT64 : (uint64_t)flushCfg->timeoutMs;
    uint32_t availTimeMs = (uint32_t)timeoutMs;

    // 等待所有执行中的事务结束
    StatusInter ret = SeRegisterFlush(seRunCtx, availTimeMs);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Acquire Dblatch beofre flush data. timeoutMs = %" PRIu64 ", usedTimeMs = %" PRIu64, timeoutMs,
            DbToMseconds(DbClockGetTsc() - startTime));
        return DbGetExternalErrno(ret);
    }

    uint64_t usedTimeMs = DbToMseconds(DbClockGetTsc() - startTime);
    if (usedTimeMs > timeoutMs) {
        SeUnRegisterFlush(seRunCtx);
        SE_LAST_ERROR(REQUEST_TIME_OUT_INTER,
            "flush data timeout because wait all trx finish, timeoutMs = %" PRIu64 ", usedTimeMs = %" PRIu64, timeoutMs,
            usedTimeMs);
        return GMERR_REQUEST_TIME_OUT;
    }
    availTimeMs = timeoutMs - usedTimeMs;

    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    if (seIns->undoPurgerStarted) {
        ret = PurgerWaitHistoryAllDone(seRunCtx->pageMgr, seRunCtx->trx, availTimeMs);
        if (ret != STATUS_OK_INTER) {
            SeUnRegisterFlush(seRunCtx);
            SE_ERROR(ret, "Wait purger done before flush data. timeoutMs = %" PRIu64 ", usedTimeMs = %" PRIu64,
                timeoutMs, DbToMseconds(DbClockGetTsc() - startTime));
            return ret == REQUEST_TIME_OUT_INTER ? GMERR_REQUEST_TIME_OUT : DbGetExternalErrno(ret);
        }
    }

    usedTimeMs = DbToMseconds(DbClockGetTsc() - startTime);
    if (usedTimeMs > timeoutMs) {
        SeUnRegisterFlush(seRunCtx);
        SE_LAST_ERROR(REQUEST_TIME_OUT_INTER,
            "flush data timeout because wait purger, timeoutMs = %" PRIu64 ", usedTimeMs = %" PRIu64, timeoutMs,
            usedTimeMs);
        return GMERR_REQUEST_TIME_OUT;
    }
    availTimeMs = timeoutMs - usedTimeMs;

    ret = CkptTriggerImpl(seIns, flushCfg->mode, flushCfg->wait, availTimeMs);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Ckpt trigger when flush data. timeoutMs = %" PRIu64 ", usedTimeMs = %" PRIu64, timeoutMs,
            DbToMseconds(DbClockGetTsc() - startTime));
    }
    SeUnRegisterFlush(seRunCtx);
    return ret == INT_ERR_WAIT_CKPT_TIMEOUT ? GMERR_REQUEST_TIME_OUT : DbGetExternalErrno(ret);
}

static inline Status CheckAndRestoreHeapMenData(SeRunCtxHdT seRunCtx, HeapResetAndRestoreCtxT *ctx, bool isReset)
{
    if (ctx != NULL && ctx->binConsistency) {
        if (isReset) {
            return ctx->reset(seRunCtx, &ctx->heapMemData);
        } else {
            return ctx->restore(seRunCtx, &ctx->heapMemData);
        }
    }
    return GMERR_OK;
}

static Status SpaceExecFlush(SeRunCtxHdT seRunCtx, const char *path, HeapResetAndRestoreCtxT *ctx)
{
    Status ret = GMERR_OK;
    if (ctx != NULL && ctx->binConsistency) {
        // 加锁成功后才做操作, 若失败，内部会回滚
        // reset内部申请内存heapMemData
        ret = ctx->reset(seRunCtx, &ctx->heapMemData);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    CRASHPOINT(DB_CRASH_EVENT_DEMAND_FLUSH, DB_CRASH_STATUS_PERSIST_DEMAND_FLUSH_PREPARE_FINISHED);

    bool binConsistency = ctx == NULL ? false : ctx->binConsistency;
    ret = DoFlushAll(seRunCtx->seIns, path, seRunCtx, binConsistency);
    if (ret != GMERR_OK) {
        if (ctx != NULL && ctx->binConsistency) {
            (void)ctx->restore(seRunCtx, &ctx->heapMemData);
        }
        return ret;
    }
    CRASHPOINT(DB_CRASH_EVENT_DEMAND_FLUSH, DB_CRASH_STATUS_PERSIST_DEMAND_FLUSH_FINISHED);

    if (ctx != NULL && ctx->binConsistency) {
        // reset会释放从reset申请的内存
        ret = ctx->restore(seRunCtx, &ctx->heapMemData);
    }
    return ret;
}

Status SpaceFlushAllImpl(SeRunCtxHdT seRunCtx, const char *path, HeapResetAndRestoreCtxT *ctx, SeFlushCfgT *flushCfg)
{
    DB_POINTER(seRunCtx);
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    if (SeGetPersistMode() == PERSIST_INCREMENT && (path == NULL || SeIsDefaultPath(seIns, path))) {
        return flushCfg != NULL ? DoTriggerCkptGentle(seRunCtx, flushCfg) : DoTriggerCkpt(seIns, path, ctx);
    }

    if (SeGetPersistMode() != PERSIST_INCREMENT && seIns->undoPurgerStarted) {
        // 如果开启bep且且处于等待刷盘状态，则设置为开始刷盘状态
        if (SeGetBepState() == SE_BEP_STATE_WAITING_FLUSH) {
            SeSetBepState(SE_BEP_STATE_FLUSHING);
            // 按需模式下需要等待purger 线程处理结束
            StatusInter intRet = PurgerWaitHistoryAllDone(seRunCtx->pageMgr, seRunCtx->trx, DB_FLUSH_LATCH_TIMEOUTMS);
            if (intRet != STATUS_OK_INTER) {
                SE_ERROR(intRet, "Wait purger done");
                SeSetBepState(SE_BEP_STATE_WAITING_FLUSH);
                return DbGetExternalErrno(intRet);
            }
        }
    }

    Status ret = DbGetExternalErrno(SeRegisterFlush(seRunCtx, DB_FLUSH_LATCH_TIMEOUTMS));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Acquire Dblatch.");
        if (SeGetBepState() != SE_BEP_STATE_NONE) {
            SeSetBepState(SE_BEP_STATE_WAITING_FLUSH);
        }
        return ret;
    }

    ret = SpaceExecFlush(seRunCtx, path, ctx);
    // 如果是开启bep，则设置为等待刷盘状态
    if (SeGetBepState() != SE_BEP_STATE_NONE) {
        SeSetBepState(SE_BEP_STATE_WAITING_FLUSH);
    }
    SeUnRegisterFlush(seRunCtx);
    return ret;
}

StatusInter SeSpaceGetPageMgrByPageIdImpl(SeInstanceT *seIns, PageIdT pageId, PageMgrT **pageMgr)
{
    DB_POINTER2(seIns, pageMgr);
    if (seIns->duMemMgr == NULL) {
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    if (pageId.deviceId >= CtrlGroupGetSize(&seIns->db->deviceGroup)) {
        SE_ERROR(INTERNAL_ERROR_INTER, "INVLD deviceId");
        return INTERNAL_ERROR_INTER;
    }
    DevCtrlT *devCtrl = SeGetDevCtrlById(seIns, pageId.deviceId);
    if (devCtrl->fileId >= CtrlGroupGetSize(&seIns->db->fileGroup)) {
        SE_ERROR(INTERNAL_ERROR_INTER, "INVLD fileId");
        return INTERNAL_ERROR_INTER;
    }
    DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, devCtrl->fileId);
    *pageMgr = SeGetPageMgrByFileType(seIns, file);
    if (*pageMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get page mgr, file(%" PRIu32 ") type( %" PRIu32 ")", file->ctrl->id,
            file->ctrl->fileType);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    return STATUS_OK_INTER;
}

#if defined(FEATURE_TS)
Status SeSpaceGetCustomPathImpl(SeRunCtxHdT seRunCtx, uint32_t spaceId, char *customPath, uint32_t pathLen)
{
    DB_POINTER3(seRunCtx, seRunCtx->seIns, customPath);
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, spaceId);
    if (!space || !space->ctrl || !space->ctrl->used) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Space not exist,(%" PRIu32 ").", spaceId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (!space->ctrl->cfg.isCustomFilePath) {
        SE_LAST_ERROR(NO_DATA_INTER, "Space not cfg custom,(%" PRIu32 ").", spaceId);
        return GMERR_NO_DATA;
    }

    errno_t err = strcpy_s(customPath, pathLen, space->ctrl->cfg.customFilePath);
    if (err != EOK) {
        SE_ERROR(OUT_OF_MEMORY_MEM_FAILED, "Copy custom file path, strcpy_s os ret:%" PRId32, err);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}
#endif

SO_EXPORT_FOR_TS void SpaceAmInit(SeInstanceT *seIns)
{
    SpaceAmFuncT spaceAmFunc = {0};
    spaceAmFunc.creatSpaceFunc = SeSpaceCreateImpl;
    spaceAmFunc.dropSpaceFunc = SeSpaceMarkDropImpl;
    spaceAmFunc.truncateSpaceFunc = SeSpaceMarkTruncateImpl;
    spaceAmFunc.commitDropSpaceFunc = SeSpaceCommitDropImpl;
    spaceAmFunc.commitTruncateSpaceFunc = SeSpaceCommitTruncateImpl;
    spaceAmFunc.spaceGetFileSizeFunc = SeSpaceGetFileSizeImpl;
    spaceAmFunc.spaceGetIdByTypeFunc = SpaceGetIdByTypeImpl;
    spaceAmFunc.spaceFlushAllFunc = SpaceFlushAllImpl;
    spaceAmFunc.seSpaceReplayRegisterFunc = SpaceReplayFuncRegisterImpl;
    spaceAmFunc.seSetPageToCompressStateFunc = SpaceSetPageToCompressStateImpl;
    spaceAmFunc.spaceGetStatFunc = SpaceGetNextUsedStatImpl;
    spaceAmFunc.seCtrlRecoveryFunc = SeCtrlRecoveryImpl;
    spaceAmFunc.spaceAddDataFileFunc = SeSpaceAddDataFileImpl;
    spaceAmFunc.spaceCommitDropFileFunc = SeSpaceCommitDropFileImpl;
    spaceAmFunc.seSpaceGetPageMgrByPageIdFunc = SeSpaceGetPageMgrByPageIdImpl;
#if defined(FEATURE_TS)
    spaceAmFunc.seSpaceGetCustomPathFunc = SeSpaceGetCustomPathImpl;
#endif
    SetSpaceAmFunc(&spaceAmFunc);
}

StatusInter SpaceCheckDiskBlock(PageHeadT *page, PageIdT pageId)
{
    bool pageIdCheck = DbIsPageIdEqual(pageId, page->addr);
    // 被free过的page是invalid pageId
    if (DbIsPageIdValid(page->addr) && !pageIdCheck) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER,
            "PageAddr mismatch. Expect PageId:%" PRIu32 ", %" PRIu32 "; pageAddr:%" PRIu32 ", %" PRIu32,
            pageId.deviceId, pageId.blockId, page->addr.deviceId, page->addr.blockId);
        return DATA_EXCEPTION_INTER;
    }
    return STATUS_OK_INTER;
}
