/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 看护持久化页面关键结构体，database
 * Author: yang<PERSON><PERSON><PERSON>
 * Create: 2024-9-24
 */

#ifndef SE_PERSISTCAP_DATABASE_H
#define SE_PERSISTCAP_DATABASE_H

#include "db_table_space.h"
#include "se_page.h"
#include "se_replay.h"
#include "se_persist.h"
#include "se_persistcap_space.h"
#include "se_persist_config.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CORE_ENCRYPT_CHECK_LENGTH 32
#define CORE_ENCRYPT_SALT_LENGTH 16
#define CORE_ENCRYPT_ENCRYPT_FLAG_LEN 1
#define CORE_ENCRYPT_ENCRYPT_MODEL_LEN 7
#define CORE_ENCRYPT_ENCRYPT_RESERVED_LEN 3
#define CORE_RESERVE_SIZE 199

#define FILE_DIGEST_LENGTH 32
// 此处宏的定义参考CATA_NAMESPACE_MAX
#define SE_NAMESPACE_MAX 64

#define ALL_FLUSH_INIT_MAGIC_NUM 0

#define CORE_CTRL_MAGIC_NUM 0xabcdefab

// StorageTypeE 下的枚举类型有3个, 共存形态不在StorageTypeE下增加枚举类型，此处为记录在StCoreCtrlT中做校验的值，设置为4
#define SE_BUFFPOOL_AND_DUMEM_STORAGE_TYPE 4

// 标识文件类型，仅数存durable memdata和buffer pool共存场景使用
#define DEFAULT_FILE 0
#define BUFFER_POOL_FILE 1
#define DURABLE_MEM_FILE 2

#define FIRST_BUFFER_POOL_FILE_INDEX 0
#define FIRST_DURABLE_MEM_FILE_INDEX 1

// 持久化关键结构体对齐： (不区分64/32位平台)强制8字节对齐
#pragma pack(8)
typedef struct StCtrlVersion {
    uint16_t main;
    uint16_t major;
    uint16_t revision;
    uint16_t inner;
} StCtrlVersionT;

typedef struct PersFileDigest {
    uint8_t sha256[FILE_DIGEST_LENGTH];
} PersFileDigestT;

typedef struct TagSeCtrlPageMgr {
    uint32_t blockHwm;       // ctrl page alloced so far
    uint32_t freeBlockCnt;   // free ctrl page cnt
    uint32_t freeBlockHead;  // first free ctrl page addr
} SeCtrlPageMgrT;

typedef struct StCoreCtrl {
    StCtrlVersionT version;
    uint32_t openCount;
    SeCtrlPageMgrT ctrlPageMgr;  // for manage ctrl page alloc & free
    uint32_t spaceCtrlEntry;     // space ctrl entry
    uint32_t fileCtrlEntry;      // file ctrl entry
    uint32_t devCtrlEntry;       // device ctrl entry
    uint32_t extendCtrlEntry;    // 8 byte align
    union {
        uint64_t padding;  // 确保指针跨平台大小一致
        time_t initTime;
    };
    uint16_t fileFormatVersion;  // ctrl文件版本
    uint16_t padding2;
    volatile StorageStatusE coreStatus;
    SePersistCompModeE compMode;
    uint32_t yangModelCheckNspIds[SE_NAMESPACE_MAX];  // yang语义校验中的模型校验使用的namespace id

    // sys tbl
    SysTblEntryT sysUserEntry;
    SysTblEntryT sysRoleEntry;
    SysTblEntryT sysNspEntry;
    SysTblEntryT sysvlEntry;
    SysTblEntryT sysElEntry;
    SysTblEntryT sysYangEntry;
    SysTblEntryT sysNodeEntry;
    SysTblEntryT sysPropEntry;
    SysTblEntryT sysIdxHeapEntry;
#ifdef FEATURE_SQL
    SysTblEntryT sysSqlRebuildHeapEntry;
#endif
    SysTblEntryT sysTsMapHeapEntry;
    uint32_t padding3;
    // ckpt
    RedoPointT truncPoint;
    RedoPointT lrpPoint;
    uint64_t lsn;
    // log

    uint32_t systemSpaceId;
    uint32_t undoSpaceId;
    uint32_t userSpaceId;
    uint32_t tempSpaceId;

    // undo
    PageIdT undoRsegPageId;
    // trx
    uint64_t maxTrxId;
    uint64_t magicNum;

    // encrypt, 从非加密到加密，或者重新加密的场景中以下值可能变化，所以没有放在StCoreCfgT中
    uint8_t hmac[DB_ENCRYPT_MAC_LENGTH];
    uint8_t iv[DB_ENCRYPT_IV_LENGTH];
    uint8_t salt[CORE_ENCRYPT_SALT_LENGTH];                 // 盐值
    uint8_t isEncrypted : CORE_ENCRYPT_ENCRYPT_FLAG_LEN;    // 是否加密
    uint8_t encryptModel : CORE_ENCRYPT_ENCRYPT_MODEL_LEN;  // 加密模型
    uint8_t reserved[CORE_ENCRYPT_ENCRYPT_RESERVED_LEN];
    uint8_t checkEncrypts[CORE_ENCRYPT_CHECK_LENGTH];  // 用于开库时校验密码

    // 不允许修改的关键参数，用于启动校验
    uint32_t persistMode;
    uint32_t deviceSize;
    uint32_t pageSize;
    uint32_t maxSeMem;
    uint32_t dbFilesMaxCnt;
    uint32_t dbFileSize;
    uint32_t redoFileCount;
    uint32_t redoFileSize;
    uint32_t crcCheckEnable;
    uint32_t sharedModeEnable;  // Shared mode enable, true means multi process is supported
    uint32_t storageType;
    uint32_t spaceMaxNum;
    uint32_t encryptReservedEnable;  // 开启后会在每个页页未保留32个字节，用于保存页加密信息

    bool isNormalShutDown;  // 标记优雅重启, 和redoFileDropOnClose打开有关
    bool isDebug;           // 0 release, 1 debug
    uint8_t reserveHolePadding[2];
    uint32_t shaCheckEnable;
    uint32_t ctrlPageSize;
    PersFileDigestT ctrlFileDigest;
    RedoPointT digestRedoPoint;  // 记录文件摘要时的 redo truncate point
    bool tamperProofEnable;

    uint8_t reserve[CORE_RESERVE_SIZE];
} StCoreCtrlT;

/* 终端已商用结构体，留作兼容性分析
typedef struct TagSpcDeviceCtrl {
    uint32_t nextDevId;  // 与本device在同一个DataFile的下一个deviceId
    uint32_t fileId;
    uint32_t spcId;
    uint32_t compSize;
    int64_t offset;
} SpcDeviceCtrlT;
 */

typedef struct TagDevCtrl {
    uint32_t id;         // id of this device
    bool online;         // checked if this device is in use
    uint8_t reserve;     // reserve
    uint16_t offset;     // offset from start of pagehead to dev ctrl
    uint32_t fileIndex;  // device index on file
    uint32_t fileId;     // id of file this device belongs to
    uint32_t spcId;      // id of space this device belongs to
    uint8_t reserve1[4];
} DevCtrlT;

static inline void SeResetDevCtrl(DevCtrlT *ctrl)
{
    ctrl->online = false;
    ctrl->spcId = DB_INVALID_TABLE_SPACE_ID;
    ctrl->fileId = SE_INVALID_FILE_ID;
    ctrl->fileIndex = SE_INVALID_FILE_INDEX;
}

typedef struct TagSafeFileHeadT {
    uint32_t magicNum;
    uint32_t pageCount;
    uint64_t ckptId : 57;
    uint64_t dwrPart : 7;  // max part count 128
} SafeFileHeadT;

typedef struct TagSafeFileTailT {
    uint32_t magicNum;
    uint32_t padding;  // 结构体对齐padding
    uint64_t ckptId : 57;
    uint64_t dwrPart : 7;  // max part count 128
} SafeFileTailT;

// 生命周期，安全文件生成以及恢复阶段RecoverPageFromSafeFile从安全文件覆盖到数据文件
typedef struct DwPageMeta {
    uint32_t fileId;   // safe页所在文件id
    uint32_t padding;  // 8字节对齐
    int64_t offset;    // safe页所在文件偏移
} DwPageMetaT;

typedef struct CompSizeArray {
    uint32_t checkSum;    // 数组的crc校验码
    uint32_t itemSize[];  // 每个压缩device的大小
} CompSizeArrayT;

typedef struct PersDataFileHead {
    uint64_t checkMagic;
    PersFileDigestT dataFileDigest;  // 如果开启的是sha校验，这里放的是文件摘要；如果是tampProof，放的是sizeArray的摘要
    // 非终端场景预留字段
#ifndef HARMONY_OS
    uint8_t reserve[32];
#endif
    CompSizeArrayT compSizeArray;
} PersDataFileHeadT;

typedef struct PersCtrlFileHead {
    uint64_t checkMagic;
    // 非终端场景预留字段
#ifndef HARMONY_OS
    uint8_t reserve[32];
#endif
} PersCtrlFileHeadT;

typedef struct PersFileTail {
    uint64_t checkMagic;
    // 非终端场景预留字段
#ifndef HARMONY_OS
    uint8_t reserve[32];
#endif
} PersFileTailT;

/* 终端商用结构体，记录用于后续分析
typedef struct DatafileCtrl {
    uint32_t id;
    bool used;
    uint8_t reserve[3];
    char name[DB_MAX_NAME_LEN];
    uint64_t size;  // 不包含PersFileHeadT和PersFileTailT
    uint64_t usedSize;
    uint32_t firstDevId;  // 第一个device id
    uint32_t deviceId;    // 当前device id
    uint16_t blockSize;
    int16_t flag;
    uint32_t reserve;
} DatafileCtrlT;
 */

typedef enum {
    FILE_OFFLINE = 0,  // 还完全没有创建
    FILE_ONLINE,       // 已完全成功创建
    FILE_CREATING,     // 仅创建了FileCtrl,磁盘文件还没有创建
} DbFileStatusE;

typedef struct TagDbFileCtrl {
    uint32_t id;
    uint8_t status : 4;  // DbFileStatusE
    uint8_t fileType : 4;
    uint8_t reserve;
    uint16_t offset;
    char name[DB_MAX_NAME_LEN];
    uint32_t spaceId;
    uint32_t pageNum;      // 页面的计数，通过这个计算出文件实际使用的空间
    uint32_t extendNum;    // 扩展的次数, 通过这个计算出文件给数据申请的空间
    uint32_t deviceNum;    // 扩展的次数, 通过这个计算出文件给数据预留的空间
    PageIdT entry;         // 文件head所在页的pageId
    uint8_t reserve1[16];  // 预留16字节，为80字节
#if defined(IDS_HAOTIAN)
    bool customDir;
    char dir[DB_MAX_PATH];
#endif
} DbFileCtrlT;

static inline bool DbFileIsOnline(const DbFileCtrlT *fileCtrl)
{
    return fileCtrl->status == FILE_ONLINE;
}

static inline bool DbFileIsOffline(const DbFileCtrlT *fileCtrl)
{
    return fileCtrl->status == FILE_OFFLINE;
}

static inline bool DbFileIsCreating(const DbFileCtrlT *fileCtrl)
{
    return fileCtrl->status == FILE_CREATING;
}

static inline void SeResetFileCtrl(DbFileCtrlT *ctrl)
{
    ctrl->status = FILE_OFFLINE;
    (void)memset_s(ctrl->name, DB_MAX_NAME_LEN, 0x0, DB_MAX_NAME_LEN);
#if defined(IDS_HAOTIAN)
    (void)memset_s(ctrl->dir, DB_MAX_PATH, 0x0, DB_MAX_PATH);
    ctrl->customDir = false;
#endif
    ctrl->extendNum = 0;
    ctrl->deviceNum = 0;
    ctrl->spaceId = DB_INVALID_TABLE_SPACE_ID;
    ctrl->entry = SE_INVALID_PAGE_ADDR;
}

// redo
typedef struct SpaceHeadSizeLog {
    uint32_t blockHwm;
    uint32_t lastDeviceId;
    union {
        uint64_t allocedSize;
        uint64_t usedSize;
    };
} SpaceHeadSizeLogT;

typedef struct SpaceFreePageLog {
    PageListT list;
} SpaceFreePageLogT;

typedef struct SpaceCtrlRedoLog {
    SpaceCfgT cfg;
    PageIdT entry;
    uint32_t id;
    bool used;
    uint8_t padding[3];
    uint32_t fileNo;  // Space Ctrl中files数组发生修改的元素下标
    uint32_t fileId;  // Space Ctrl中files数组发生修改的元素值
} SpaceCtrlRedoLogT;

typedef struct SpaceCtrl4CreateDFRedoLog {
    PageIdT entry;
    uint32_t id;
    uint32_t fileNo;  // Space Ctrl中files数组发生修改的元素下标
    uint32_t fileId;  // Space Ctrl中files数组发生修改的元素值
} SpaceCtrl4CreateDFRedoLogT;

typedef struct DFCtrl4FileExtendRedoLog {
    uint32_t id;
    uint64_t size;
    uint64_t pageNum;
    uint32_t deviceNum;
} DFCtrl4FileExtendRedoLogT;

typedef struct UpdataNextPageOrExtentRedoLogT {
    PageIdT pageId;
    PageIdT nextPageId;
} UpdataNextPageOrExtentRedoLogT;

typedef struct DeviceCtrlRedoLog {
    uint32_t needAlloc : 1;
    uint32_t id : 31;
    DevCtrlT deviceCtrl;
} DeviceCtrlRedoLogT;

typedef struct DeviceHeadRedoLog {
    uint32_t maxBlocks;
    uint32_t usedBlocks;
    uint16_t beginPos;
} DeviceHeadRedoLogT;

typedef struct CoreCtrlRedoLog {
    uint32_t offset;
    uint32_t dataSize;
} CoreCtrlRedoLogT;

typedef struct DataFileCtrlRedoLog {
    DbFileCtrlT ctrl;
    uint32_t spcId;
} DataFileCtrlRedoLogT;

typedef struct TagCtrlPageRedoLog {
    uint64_t lsn;
    uint32_t pageId;
    uint32_t offset;
    uint32_t dataSize;
    uint32_t reserve;
} CtrlPageRedoLogT;

#pragma pack()

#ifdef __cplusplus
}
#endif

#endif
