/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: space compressed area implementation
 * Create: 2024-12
 */
#include "se_page_mgr.h"
#include "se_database.h"
#include "adpt_compress.h"
#include "se_page_desc.h"
#include "se_space_virtual_disk.h"

StatusInter SeVirtualDiskCreate(SeInstanceT *seIns)
{
    if (!SpaceCompressionIsEnable(seIns)) {
        seIns->db->vd = NULL;
        return STATUS_OK_INTER;
    }
    // bufferpool Mgr creation will checkout whether compression space is open.
    if (!(SeGetPersistMode() == PERSIST_ON_DEMAND && seIns->storageType == SE_BUFFER_POOL) ||
        SeGetFlushCompMode(seIns) == COMP_MODE_PAGE) {
        // Space compression is not support without bufferpool-on-demand persistent mode, or Page-compression of Disk.
        SE_LAST_ERROR(FEATURE_NOT_SUPPORTED_INNER, "cmpArea in current mode");
        return FEATURE_NOT_SUPPORTED_INNER;
    }
    const SeConfigT *cfg = &seIns->seConfig;
    uint32_t capacity = cfg->dbFilesMaxCnt * ((cfg->dbFileSize) / cfg->pageSize);
    uint64_t allocSize = (uint64_t)sizeof(VirtualDiskMgrT) + capacity * (uint64_t)(sizeof(uint8_t) + sizeof(VDNodeT *));
#ifdef SYS32BITS
    if (allocSize >= DB_MAX_UINT32) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "request too big dynmem, size %" PRIu64, allocSize);
        return OUT_OF_MEMORY_INTER;
    }
#endif
    VirtualDiskMgrT *mgr = (VirtualDiskMgrT *)DbDynMemCtxAlloc(seIns->seServerMemCtx, (size_t)allocSize);
    if (mgr == NULL) {
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_s(mgr, allocSize, 0, allocSize);
    DbMemCtxArgsT args = {0};
    args.liteModOn = true;
    // when increasing the amount of data, compressor memctx might excess the limits.
    args.noLimited = true;
    mgr->memCtx = DbCreateDynMemCtx(seIns->seServerMemCtx, true, "CompressorMemCtx", &args);
    if (mgr->memCtx == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "Create compressor DynMemCtx");
        DbDynMemCtxFree(seIns->seServerMemCtx, mgr);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    mgr->compressor = NULL;
    mgr->stat = (CompressAreaStatT){0};
    mgr->stat.capacity = capacity;
    mgr->validMap = (uint8_t *)(mgr + 1);
    mgr->node = (VDNodeT **)((uint8_t *)mgr->validMap + capacity * sizeof(uint8_t));
    seIns->db->vd = mgr;
    return STATUS_OK_INTER;
}

void SeVirtualDiskDestroy(SeInstanceT *seIns)
{
    VirtualDiskMgrT *mgr = (VirtualDiskMgrT *)seIns->db->vd;
    if (mgr == NULL) {
        return;
    }
    (void)SeVirtualDiskFullCompressEnd(seIns, true);
    DbDeleteDynMemCtx(mgr->memCtx);
    DbDynMemCtxFree(seIns->seServerMemCtx, mgr);
    seIns->db->vd = NULL;
}

static StatusInter VDCreateNode(DbMemCtxT *memCtx, uint8_t *page, uint32_t size, VDWriteModeE mode, VDNodeT **node)
{
    StatusInter ret = STATUS_OK_INTER;
    uint32_t cursorSize = size;
    uint8_t *cursor = page;
    uint8_t *compBuf = NULL;
    uint32_t bufSize = size * 2;
    if (DbEnableSpaceCompress() && mode != VD_LOAD) {
        // alloc tmp buffer size to avoid compress size larger than pageSize.
        compBuf = (uint8_t *)DB_MALLOC(bufSize);
    }
    if (compBuf != NULL) {
        ret = DbGetStatusInterErrno(DbSpaceDataCompress(compBuf, &bufSize, cursor, size));
        if (ret != STATUS_OK_INTER) {
            DB_LOG_WARN(GMERR_CONFIG_ERROR, "compress page, size %" PRIu32, bufSize);
            cursorSize = size;
        } else {
            cursorSize = bufSize;
            cursor = compBuf;
        }
    } else if (mode != VD_LOAD) {
        // unable to compress the page, then using the original page.
        DB_LOG_WARN(GMERR_OUT_OF_MEMORY, "alloc comp buf, size %" PRIu32, bufSize);
    }
    uint32_t allocSize = (uint32_t)sizeof(VDNodeT) + cursorSize;
    VDNodeT *newNode = (VDNodeT *)DbDynMemCtxAlloc(memCtx, allocSize);
    if (newNode == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc vd node, size %" PRIu32, allocSize);
        ret = OUT_OF_MEMORY_INTER;
        goto EXIT;
    }
    if (mode == VD_LOAD) {
        newNode->state = NODE_PRISTINE;
    } else if (cursor == compBuf) {
        newNode->state = NODE_COMPRESSED;
    } else {
        newNode->state = NODE_UNCOMPRESSED;
    }
    newNode->size = cursorSize;
    (void)memcpy_s(newNode->page, cursorSize, cursor, cursorSize);
    *node = newNode;
EXIT:
    if (compBuf != NULL) {
        DB_FREE(compBuf);
    }
    return ret;
}

static inline uint32_t PageAddrToBufDescIndex(SeInstanceT *seIns, PageIdT pageId)
{
    return SeGetPageCntPerDev(seIns) * pageId.deviceId + pageId.blockId;
}

StatusInter SeVirtualDiskWriteBlock(SeInstanceT *seIns, PageIdT pageId, uint8_t *page, VDWriteModeE mode)
{
    DB_ASSERT(mode != VD_COMPRESS);
    VirtualDiskMgrT *mgr = (VirtualDiskMgrT *)seIns->db->vd;
    uint32_t index = PageAddrToBufDescIndex(seIns, pageId);
    if (SECUREC_UNLIKELY(index >= mgr->stat.capacity)) {
        SE_ERROR(INTERNAL_ERROR_INTER, "Unrecognize pageId %" PRIu32 ", %" PRIu32, pageId.deviceId, pageId.blockId);
        DB_ASSERT(false);
        return INTERNAL_ERROR_INTER;
    }
    DbAtomicInc64(&mgr->stat.swapInCnt);
    mgr->validMap[index] = true;
    if (mode == VD_DISCARD_CHANGE) {
        return STATUS_OK_INTER;
    }
    VDNodeT *newNode = NULL;
    StatusInter ret = STATUS_OK_INTER;
    if (mode != VD_FREE) {
        ret = VDCreateNode(mgr->memCtx, page, SIZE_K(seIns->seConfig.pageSize), mode, &newNode);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        DbAtomicAdd64(&mgr->stat.usedSize, newNode->size);
        if (newNode->state == NODE_COMPRESSED) {
            DbAtomicInc(&mgr->stat.compressedPageCnt);
        }
    }
    VDNodeT *node = NULL;
    do {
        node = (VDNodeT *)DbAtomicGetPtr((uintptr_t *)&mgr->node[index]);
    } while (!DbAtomicBoolCASForPtr((uintptr_t *)&mgr->node[index], (uintptr_t)node, (uintptr_t)newNode));
    if (node && node->state != NODE_PRISTINE) {
        if (node->state == NODE_COMPRESSED) {
            DbAtomicDec(&mgr->stat.compressedPageCnt);
        }
        DbAtomicSub64(&mgr->stat.usedSize, node->size);
        DbDynMemCtxFree(mgr->memCtx, node);
    }
    return STATUS_OK_INTER;
}

StatusInter SeVirtualDiskReadBlock(SeInstanceT *seIns, PageIdT pageId, uint8_t *page)
{
    VirtualDiskMgrT *mgr = (VirtualDiskMgrT *)seIns->db->vd;
    uint32_t index = PageAddrToBufDescIndex(seIns, pageId);
    if (SECUREC_UNLIKELY(index > mgr->stat.capacity || mgr->validMap[index] == 0)) {
        SE_ERROR(INTERNAL_ERROR_INTER, "Unrecognize pageId %" PRIu32 ", %" PRIu32, pageId.deviceId, pageId.blockId);
        DB_ASSERT(false);
        return INTERNAL_ERROR_INTER;
    }
    VDNodeT *node = (VDNodeT *)DbAtomicGetPtr((uintptr_t *)&mgr->node[index]);
    if (node == NULL) {
        (void)memset_s(page, SIZE_K(seIns->seConfig.pageSize), 0, SIZE_K(seIns->seConfig.pageSize));
        ((PageHeadT *)page)->checkSum = PAGE_HEAD_MAGIC_NUM;
        SeResetPageHead(page);
    } else if (node->state == NODE_COMPRESSED) {
        uint32_t decompressSize = SIZE_K(seIns->seConfig.pageSize);
        Status ret = DbSpaceDataDecompress(page, &decompressSize, node->page, node->size);
        if (ret != STATUS_OK_INTER) {
            DB_LOG_ERROR(ret, "decompress pageId %" PRIu32 ", %" PRIu32 ", size:%" PRIu16, pageId.deviceId,
                pageId.blockId, node->size);
            return DbGetStatusInterErrno(ret);
        }
        if (decompressSize != SIZE_K(seIns->seConfig.pageSize)) {
            DB_LOG_WARN(GMERR_DATA_CORRUPTION,
                "decompress pageId %" PRIu32 ", %" PRIu32 " size:%" PRIu32 " Decmp size: %" PRIu32, pageId.deviceId,
                pageId.blockId, node->size, decompressSize);
            DB_ASSERT(false);
            return DATA_CORRUPTION_INTER;
        }
    } else {
        DB_ASSERT(node->size == SIZE_K(seIns->seConfig.pageSize));
        (void)memcpy_s(page, node->size, node->page, node->size);
    }
    return STATUS_OK_INTER;
}

StatusInter SeVirtualDiskFullCompressInit(SeInstanceT *seIns)
{
    VirtualDiskMgrT *mgr = (VirtualDiskMgrT *)seIns->db->vd;
    VDBGCCompressorT *compressor = (VDBGCCompressorT *)DbDynMemCtxAlloc(mgr->memCtx, sizeof(VDBGCCompressorT));
    if (compressor == NULL) {
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_s(compressor, sizeof(VDBGCCompressorT), 0, sizeof(VDBGCCompressorT));
    DbMemCtxArgsT args = {0};
    args.liteModOn = true;
    args.noLimited = true;
    compressor->loadCtx = DbCreateDynMemCtx(mgr->memCtx, true, "CompressorLoadMemCtx", &args);
    if (compressor->loadCtx == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "Create compressor DynMemCtx");
        DbDynMemCtxFree(mgr->memCtx, compressor);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    mgr->compressor = compressor;
    return STATUS_OK_INTER;
}

StatusInter SeVirtualDiskImportDevice(SeInstanceT *seIns, uint32_t deviceId, uint8_t *buffer, uint32_t bufferSize)
{
    VirtualDiskMgrT *mgr = (VirtualDiskMgrT *)seIns->db->vd;
    if (mgr->compressor == NULL) {
        DB_LOG_ERROR(OBJECT_NOT_IN_PREREQUISITE_STATE_INTER, "Full Compress uninit");
        return OBJECT_NOT_IN_PREREQUISITE_STATE_INTER;
    }
    StatusInter ret = STATUS_OK_INTER;
    uint32_t pageSize = SIZE_K(seIns->seConfig.pageSize);
    uint32_t padding = deviceId * SeGetPageCntPerDev(seIns);
    uint8_t *cursor = buffer;
    for (uint32_t i = 0; i < bufferSize / pageSize; ++i) {
        cursor = buffer + i * pageSize;
        // Node would not be created for free page.
        if (DbIsPageIdValid(((PageHeadT *)cursor)->addr)) {
            ret = VDCreateNode(mgr->compressor->loadCtx, cursor, pageSize, VD_LOAD, &mgr->node[padding + i]);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
        }
        mgr->validMap[padding + i] = true;
    }
    return STATUS_OK_INTER;
}

static void *CompressThreadProc(void *arg)
{
    DbSetServerThreadFlag();
    SeInstanceT *seIns = (SeInstanceT *)arg;
    const SeConfigT *cfg = &seIns->seConfig;
    VirtualDiskMgrT *mgr = (VirtualDiskMgrT *)seIns->db->vd;
    VDBGCCompressorT *cmpr = mgr->compressor;

    VDNodeT *rawNode = NULL;
    VDNodeT *compNode = NULL;
    bool compressed = false;
    uint32_t iterLimit = cfg->dbFilesMaxCnt * (cfg->dbFileSize / cfg->pageSize);
    for (cmpr->procCnt = 0; cmpr->procCnt < iterLimit && !cmpr->forceStop; ++cmpr->procCnt) {
        rawNode = (VDNodeT *)DbAtomicGetPtr((uintptr_t *)&mgr->node[cmpr->procCnt]);
        if (rawNode == NULL || rawNode->state != NODE_PRISTINE) {
            continue;
        }
        cmpr->loadStatus = VDCreateNode(mgr->memCtx, rawNode->page, rawNode->size, VD_COMPRESS, &compNode);
        if (cmpr->loadStatus != STATUS_OK_INTER) {
            break;
        }
        compressed = compNode->state == NODE_COMPRESSED;
        uint64_t size = compNode->size;
        if (!DbAtomicBoolCASForPtr((uintptr_t *)&mgr->node[cmpr->procCnt], (uintptr_t)rawNode, (uintptr_t)compNode)) {
            DbDynMemCtxFree(mgr->memCtx, compNode);
        } else if (compressed) {
            DbAtomicInc(&mgr->stat.compressedPageCnt);
            DbAtomicAdd64(&mgr->stat.usedSize, size);
        }
    }
    cmpr->stopTime = DbRdtsc();
    return NULL;
}

StatusInter SeVirtualDiskFullCompressStart(SeInstanceT *seIns)
{
    VirtualDiskMgrT *mgr = (VirtualDiskMgrT *)seIns->db->vd;
    if (mgr->compressor == NULL) {
        DB_LOG_ERROR(OBJECT_NOT_IN_PREREQUISITE_STATE_INTER, "Full Compress uninit");
        return OBJECT_NOT_IN_PREREQUISITE_STATE_INTER;
    }
    mgr->compressor->startTime = DbRdtsc();
    ThreadAttrsT attr = {
        .name = "CompressProc",
        .priority = THREAD_PRIORITY_MIDDLE,
        .type = DB_THREAD_JOINABLE,
        .entryFunc = CompressThreadProc,
        .exitFunc = NULL,
        .entryArgs = (void *)seIns,
        .exitArgs = NULL,
        .stackSize = DB_DEFAULT_THREAD_STACK_SIZE,
        .bindCpuFlag = 0,
        .cpu = 0,
        .userAttr = NULL,
    };
    Status ret = DbThreadCreate(&attr, &mgr->compressor->handle);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Create compress thread.");
        return DbGetStatusInterErrno(ret);
    }
    return STATUS_OK_INTER;
}

StatusInter SeVirtualDiskFullCompressEnd(SeInstanceT *seIns, bool forceStop)
{
    VirtualDiskMgrT *mgr = (VirtualDiskMgrT *)seIns->db->vd;
    if (mgr == NULL || mgr->compressor == NULL) {
        return STATUS_OK_INTER;
    }
    if (forceStop) {
        mgr->compressor->forceStop = true;
    }
    StatusInter ret = DbGetStatusInterErrno(DbThreadJoin(mgr->compressor->handle, NULL));
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Join thread");
        return ret;
    }
    ret = mgr->compressor->loadStatus;
    DB_LOG_WARN_UNFOLD(ret, "Compressor stop, processed page %" PRIu32 ", used time %" PRIu64, mgr->compressor->procCnt,
        DbToMseconds(mgr->compressor->stopTime - mgr->compressor->startTime));
    DbDeleteDynMemCtx(mgr->compressor->loadCtx);
    DbDynMemCtxFree(mgr->memCtx, mgr->compressor);
    mgr->compressor = NULL;
    return ret;
}

SO_EXPORT_FOR_TS void CompressAreaGetStat(SeInstanceT *seIns, CompressAreaStatT *compressStat)
{
    DB_POINTER(seIns);
    if (!seIns->db->vd) {
        *compressStat = (CompressAreaStatT){0};
        return;
    }
    VirtualDiskMgrT *mgr = (VirtualDiskMgrT *)seIns->db->vd;
    compressStat->capacity = seIns->seConfig.dbFileSize / seIns->seConfig.pageSize;
    compressStat->compressedPageCnt = mgr->stat.compressedPageCnt;
    compressStat->usedSize = mgr->stat.usedSize;
    compressStat->swapInCnt = mgr->stat.swapInCnt;
}
