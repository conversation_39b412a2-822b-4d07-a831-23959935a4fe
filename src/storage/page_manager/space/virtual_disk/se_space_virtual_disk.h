/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: space compressed area header file
 * Create: 2024-12
 */

#ifndef SE_SPACE_VIRTUAL_DISK_H
#define SE_SPACE_VIRTUAL_DISK_H

#include "se_dfx.h"
#include "se_define.h"

#define SE_COMPRESS_THREAD_NUM 4
#define SE_INVALID_THREAD_HANDLE 0
#define SE_COMPRESS_PROC_INTERVAL 1

#ifdef __cplusplus
extern "C" {
#endif

typedef enum EnumVirtualDiskWriteMode {
    VD_LOAD,
    VD_COMMIT_CHANGE,
    VD_DISCARD_CHANGE,
    VD_FREE,
    VD_COMPRESS,
} VDWriteModeE;

typedef enum EnumVirtualDiskNodeState {
    NODE_PRISTINE,
    NODE_COMPRESSED,
    NODE_UNCOMPRESSED,
} VDNodeStateE;

typedef struct TagVirtualDiskNode {
    volatile VDNodeStateE state;
    uint32_t size;
    uint8_t page[];
} VDNodeT;
#define SE_INVALID_VDNODE   \
    (VDNodeT)               \
    {                       \
        NODE_EMPTY, 0, NULL \
    }
#define PRISTINE_LSN 0

typedef struct TagVirtualDiskBackGroundCompressor {
    DbMemCtxT *loadCtx;
    StatusInter loadStatus;
    DbThreadHandle handle;
    volatile bool forceStop;
    uint8_t reserve[3];
    uint32_t procCnt;
    uint64_t startTime;
    uint64_t stopTime;
} VDBGCCompressorT;

typedef struct TagVirtualDiskMgr {
    DbMemCtxT *memCtx;
    VDBGCCompressorT *compressor;
    CompressAreaStatT stat;
    uint8_t *validMap;
    VDNodeT **node;
} VirtualDiskMgrT;

StatusInter SeVirtualDiskCreate(SeInstanceT *seIns);
void SeVirtualDiskDestroy(SeInstanceT *seIns);

StatusInter SeVirtualDiskWriteBlock(SeInstanceT *seIns, PageIdT pageId, uint8_t *page, VDWriteModeE mode);
StatusInter SeVirtualDiskReadBlock(SeInstanceT *seIns, PageIdT pageId, uint8_t *page);

StatusInter SeVirtualDiskImportDevice(SeInstanceT *seIns, uint32_t deviceId, uint8_t *buffer, uint32_t bufferSize);

StatusInter SeVirtualDiskFullCompressInit(SeInstanceT *seIns);
StatusInter SeVirtualDiskFullCompressStart(SeInstanceT *seIns);
StatusInter SeVirtualDiskFullCompressEnd(SeInstanceT *seIns, bool forceStop);

#ifdef __cplusplus
}
#endif

#endif
