/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: backup implementation
 * Author: wenming
 * Create: 2022-06-22
 */
#include "se_database.h"
#include "se_datafile.h"
#include "se_db_file.h"
#include "se_ctrl_page_pool.h"
#include "se_redo_inner.h"
#include "se_database_util.h"
#include "se_db_file_space.h"
#include "se_db_file_device.h"
#include "se_ckpt_inner.h"
#include "db_inter_process_rwlatch.h"
#include "se_redo_file.h"
#include "se_trx_mgr.h"
#include "se_replay.h"
#include "se_space_backup_base.h"
#include "se_ctrl_page.h"
#include "db_timer.h"
#include "db_internal_error.h"
#include "se_verify_inner.h"

#define DB_BACK_UP_LATCH_TIMEOUT (3 * 1000 * 1000)  // us
#define DB_SWAP_LATCH_TIMEOUT (10 * 1000 * 1000)    // us
#define DB_BACK_UP_LATCH_SPLIT_TIMEOUT (1 * 1000)   // us

void SeFreeBackupCtx(SeRunCtxT *seRunCtx, SeBackupCtxT *ctx)
{
    CtrlGroupClear(&ctx->spaceGroup, SpaceDestroyFileList, NULL);
    CtrlGroupClear(&ctx->fileGroup, SeCloseCtrlFile, NULL);
    CtrlGroupClear(&ctx->deviceGroup, NULL, NULL);
    SeCtrlPagePoolClear(&ctx->pool);
    SeFileClose(&ctx->redo.fd);
    DbDynMemCtxFree(seRunCtx->sessionMemCtx, ctx);
    SePersistUnRegisterBackup(seRunCtx);
}

StatusInter SeAllocBackupCtx(SeRunCtxT *seRunCtx, SeBackupCtxT **ctx)
{
    StatusInter ret = SePersistRegisterBackup(seRunCtx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    size_t bufferSize = DB_MAX(SIZE_K(seIns->seConfig.extendSize), seIns->redoMgr->cfg.pubBufSize);
    size_t allocSize = sizeof(SeBackupCtxT) + DB_MAX_PATH + bufferSize;
    SeBackupCtxT *newCtx = (SeBackupCtxT *)DbDynMemCtxAlloc(seRunCtx->sessionMemCtx, allocSize);
    if (newCtx == NULL) {
        SE_ERROR(OUT_OF_MEMORY_INTER, "Alloc back up ctx");
        SePersistUnRegisterBackup(seRunCtx);
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_s(newCtx, allocSize, 0, allocSize);
    newCtx->backupPath = (char *)(newCtx + 1);
    ret = SeCtrlPagePoolInit(seIns, seRunCtx->sessionMemCtx, &newCtx->pool, 0, seIns->db->ctrlFile.blockSize);
    if (ret != STATUS_OK_INTER) {
        DbDynMemCtxFree(seRunCtx->sessionMemCtx, newCtx);
        SePersistUnRegisterBackup(seRunCtx);
        return ret;
    }
    DbFileInit(&newCtx->ctrlFile, seIns->db->ctrlFile.blockSize, sizeof(PersCtrlFileHeadT), NULL);
    CtrlGroupInit(&newCtx->spaceGroup, seRunCtx->sessionMemCtx, sizeof(SpaceT));
    CtrlGroupInit(&newCtx->fileGroup, seRunCtx->sessionMemCtx, sizeof(DbFileT));
    CtrlGroupInit(&newCtx->deviceGroup, seRunCtx->sessionMemCtx, sizeof(DbDeviceT));
    newCtx->redo.fd = DB_INVALID_FD;
    newCtx->redo.startPoint = RedoGenInitPoint();
    newCtx->redo.targetPoint = RedoGenInitPoint();
    newCtx->redo.backupPoint = RedoGenInitPoint();
    newCtx->bufferSize = bufferSize;
    newCtx->mirrorBuffer = (uint8_t *)(newCtx->backupPath + DB_MAX_PATH);
    *ctx = newCtx;
    return STATUS_OK_INTER;
}

StatusInter BackupPrepareDir(SeRunCtxT *seRunCtx, const SeBackupArgsT *args, SeBackupCtxT *ctx)
{
    if (SeIsStringEmpty(args->path)) {
        SE_ERROR(INVALID_PARAMETER_VALUE_INTER, "empty path");
        return INVALID_PARAMETER_VALUE_INTER;
    }
    StatusInter ret = SeFileFullPath(args->path, ctx->backupPath, DB_MAX_PATH);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    char fullPath[DB_MAX_PATH] = {0};
    for (uint32_t i = 0; i < seIns->seConfig.multiZoneNum; ++i) {
        ret = DbGetStatusInterErrno(DbAdptRealPath(seIns->seConfig.dataFileDirPath[i], fullPath));
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        if (SeIsSamePath(fullPath, ctx->backupPath)) {
            SE_ERROR(OBJECT_NOT_IN_PREREQUISITE_STATE_INTER, "backup path (%s) same as active path(%s)",
                ctx->backupPath, fullPath);
            return OBJECT_NOT_IN_PREREQUISITE_STATE_INTER;
        }
    }

    if (DbDirExist(ctx->backupPath)) {
        if (args->replace) {
            ret = DbGetStatusInterErrno(DbRemoveDir(args->path));
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "Clear backup path(%s)", args->path);
                return ret;
            }
        } else {
            SE_ERROR(OBJECT_NOT_IN_PREREQUISITE_STATE_INTER,
                "Assign existed path (%s) in non-replace backup mode not allowed", args->path);
            return OBJECT_NOT_IN_PREREQUISITE_STATE_INTER;
        }
    }
    ret = DbGetStatusInterErrno(DbMakeDirWithGRPRXPermission(ctx->backupPath));
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Create backup path(%s)", args->path);
    }
    return ret;
}

static StatusInter BackupCheckCtrlPage(SeInstanceT *seIns, PageHeadT *page, uint32_t pageId, uint32_t checkSize)
{
    if (page->addr.blockId != pageId) {
        SE_ERROR(DATA_EXCEPTION_INTER, "Backup page file info not consist with logic.");
        return DATA_EXCEPTION_INTER;
    }
    if (page->checkSum != PAGE_HEAD_MAGIC_NUM) {
        SE_ERROR(DATA_EXCEPTION_INTER, "Backup page file info not consist with logic.");
        return DATA_EXCEPTION_INTER;
    }
    if (SeGetPageTotalSize(page) != checkSize) {
        SE_ERROR(DATA_EXCEPTION_INTER, "Backup page file info not consist with logic.");
        return DATA_EXCEPTION_INTER;
    }
    if (SeCtrlPageNeedCRCCheck(seIns, pageId)) {
        return DbCheckCtrlPageCrc(pageId, (StCtrlPageT *)(void *)page, checkSize);
    }
    return STATUS_OK_INTER;
}

static StatusInter BackupLoadCtrlPage(SeInstanceT *seIns, CtrlPagePoolT *pool)
{
    StatusInter ret = STATUS_OK_INTER;
    uint8_t *page = NULL;
    const DbFileT *ctrlFile = &seIns->db->ctrlFile;
    uint32_t zoneId = SeGetMainZoneId(seIns);
    DbSpinLock(&seIns->db->pubCtx->ctrlLock);
    for (uint32_t i = SeGetCoreCtrlPageId(seIns); i < seIns->db->core.ctrlPageMgr.blockHwm; ++i) {
        ret = SeCtrlPagePoolLoadPage(pool, i, true, &page);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Backup alloc ctrl page (%" PRIu32 " )", i);
            break;
        }
        int64_t offset = SeGetCtrlFilePageOffset(ctrlFile, i);
        ret = SeFileRead(ctrlFile->handle[zoneId], offset, (BufT){.buf = page, .size = ctrlFile->blockSize});
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Backup read ctrl page (%" PRIu32 " )", i);
            break;
        }
        ret = BackupCheckCtrlPage(seIns, (PageHeadT *)page, i, ctrlFile->blockSize);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Backup check ctrl page (%" PRIu32 " )", i);
            break;
        }
    }
    DbSpinUnlock(&seIns->db->pubCtx->ctrlLock);
    return ret;
}

static StatusInter BackupMapCoreCtrl(SeBackupCtxT *ctx)
{
    uint8_t *page = NULL;
    StatusInter ret = SeCtrlPagePoolLoadPage(&ctx->pool, CORE_CTRL_PAGE_ID, false, &page);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "backup find core ctrl in mirror page pool.");
        DB_ASSERT(false);
        return ret;
    }
    ctx->core = (StCoreCtrlT *)(page + sizeof(PageHeadT));
    ctx->redo.startPoint = ctx->core->truncPoint;
    DbAtomicSet(&ctx->core->coreStatus, SE_ON_DISK_BACKING_UP);
    ctx->core->truncPoint = RedoGenInitPoint();
    ctx->core->lrpPoint = RedoGenInitPoint();
    return STATUS_OK_INTER;
}

static StatusInter BackupMapSpaceCtrl(SeRunCtxT *seRunCtx, const SeBackupArgsT *args, SeBackupCtxT *ctx)
{
    StatusInter ret = STATUS_OK_INTER;
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    uint32_t cursor = ctx->core->spaceCtrlEntry;
    DB_ASSERT(cursor != SE_INVALID_BLOCK_ID);
    PageHeadT *page = NULL;
    do {
        ret = SeCtrlPagePoolLoadPage(&ctx->pool, cursor, false, (uint8_t **)&page);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "backup find space ctrl in mirror page pool.");
            return ret;
        }
        uint32_t cnt = (uint32_t)((SeGetCtrlBufSize(seIns->db) - page->freeSize) / sizeof(SpaceCtrlT));
        for (uint32_t i = 0; i < cnt; i++) {
            SpaceCtrlT *ctrl = (SpaceCtrlT *)((uint8_t *)page + sizeof(PageHeadT) + i * sizeof(SpaceCtrlT));
            if (!SeBackupShouldKeepSpace(args, ctrl->id)) {
                SeResetSpaceCtrl(ctrl);
            }
#if defined(FEATURE_TS)
            // 自定义路径的表空间，同样备份到备份路径下
            ctrl->cfg.isCustomFilePath = false;
#endif
            SpaceT space = {0};
            SpaceInit(seRunCtx->sessionMemCtx, &space, ctrl);
            ret = CtrlGroupAddItem(&ctx->spaceGroup, i, (uint8_t *)&space);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "backup save space ctrl in mirror ctrl group.");
                return ret;
            }
        }
        cursor = page->nextPageId.blockId;
    } while (cursor != SE_INVALID_BLOCK_ID);
    return STATUS_OK_INTER;
}

static StatusInter BackupMapFileCtrl(SeRunCtxT *seRunCtx, const SeBackupArgsT *args, SeBackupCtxT *ctx)
{
    StatusInter ret = STATUS_OK_INTER;
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    uint32_t blockSize = SIZE_K(seIns->seConfig.pageSize);
    int64_t dataOffset = (int64_t)DB_CALC_ALIGN(sizeof(PersDataFileHeadT), seIns->db->diskBlockSize);
    uint32_t cursor = ctx->core->fileCtrlEntry;
    PageHeadT *page = NULL;
    do {
        ret = SeCtrlPagePoolLoadPage(&ctx->pool, cursor, false, (uint8_t **)&page);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        uint32_t cnt = (uint32_t)((SeGetCtrlBufSize(seIns->db) - page->freeSize) / sizeof(DbFileCtrlT));
        for (uint32_t i = 0; i < cnt; i++) {
            DbFileCtrlT *ctrl = (DbFileCtrlT *)((uint8_t *)page + sizeof(PageHeadT) + i * sizeof(DbFileCtrlT));
            if (!SeBackupShouldKeepSpace(args, ctrl->spaceId)) {
                SeResetFileCtrl(ctrl);
            }
            DbFileT file = {0};
            DbFileInit(&file, blockSize, dataOffset, ctrl);
            ret = CtrlGroupAddItem(&ctx->fileGroup, i, (uint8_t *)&file);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "backup save file ctrl in mirror ctrl group.");
                return ret;
            }
        }
        cursor = page->nextPageId.blockId;
    } while (cursor != SE_INVALID_BLOCK_ID);
    return STATUS_OK_INTER;
}

static StatusInter BackupMapDevCtrl(SeRunCtxT *seRunCtx, const SeBackupArgsT *args, SeBackupCtxT *ctx)
{
    StatusInter ret = STATUS_OK_INTER;
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    uint32_t cursor = ctx->core->devCtrlEntry;
    PageHeadT *page = NULL;
    do {
        ret = SeCtrlPagePoolLoadPage(&ctx->pool, cursor, false, (uint8_t **)&page);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        uint32_t cnt = (uint32_t)((SeGetCtrlBufSize(seIns->db) - page->freeSize) / sizeof(DevCtrlT));
        for (uint32_t i = 0; i < cnt; ++i) {
            DevCtrlT *ctrl = (DevCtrlT *)((uint8_t *)page + sizeof(PageHeadT) + i * sizeof(DevCtrlT));
            if (!SeBackupShouldKeepSpace(args, ctrl->spcId)) {
                SeResetDevCtrl(ctrl);
            }
            DbDeviceT dbDevice = {0};
            dbDevice.ctrl = ctrl;
            DbSpinInit(&dbDevice.lock);
            ret = CtrlGroupAddItem(&ctx->deviceGroup, i, (uint8_t *)&dbDevice);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "backup save file ctrl in mirror ctrl group.");
                DB_ASSERT(false);
                return ret;
            }
        }
        cursor = page->nextPageId.blockId;
    } while (cursor != SE_INVALID_BLOCK_ID);
    return STATUS_OK_INTER;
}

static StatusInter BackupMapCtrl(SeRunCtxT *seRunCtx, const SeBackupArgsT *args, SeBackupCtxT *ctx)
{
    StatusInter ret = BackupMapCoreCtrl(ctx);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Backup proc map core ctrl.");
        return ret;
    }
    ret = BackupMapSpaceCtrl(seRunCtx, args, ctx);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Backup proc map space ctrl.");
        return ret;
    }
    ret = BackupMapFileCtrl(seRunCtx, args, ctx);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Backup proc map file ctrl.");
        return ret;
    }
    ret = BackupMapDevCtrl(seRunCtx, args, ctx);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Backup proc map dev ctrl.");
        return ret;
    }
    return STATUS_OK_INTER;
}

static StatusInter BackupCreateCtrlFile(SeRunCtxT *seRunCtx, SeBackupCtxT *ctx)
{
    char filePath[DB_MAX_WHOLE_PATH] = {0};
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    StatusInter ret = SeFileConstructPath(ctx->backupPath, seIns->seConfig.ctrlFileName, filePath, DB_MAX_WHOLE_PATH);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "backup construct ctrl file path (%s, %s)", ctx->backupPath, seIns->seConfig.ctrlFileName);
        return ret;
    }
    ret = SeFileCreate(filePath, SeGetFileOpExtraFlag(seIns), &ctx->ctrlFile.handle[0]);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "backup create ctrl file (%s)", filePath);
        return ret;
    }
    return STATUS_OK_INTER;
}

static StatusInter BackupFlushCtrlFile(SeRunCtxT *seRunCtx, SeBackupCtxT *ctx)
{
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    StatusInter ret = SpcWriteCtrlFileHead(seIns, ctx->ctrlFile.handle[0], seIns->db->checkMagic);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "backup write ctrl file head");
        return ret;
    }
    int64_t offset = 0;
    BufT buf = {.buf = NULL, .size = ctx->ctrlFile.blockSize};
    for (uint32_t i = 0; i < ctx->core->ctrlPageMgr.blockHwm; ++i) {
        ret = SeCtrlPagePoolLoadPage(&ctx->pool, i, false, (uint8_t **)&buf.buf);
        if (ret == NO_DATA_INTER) {
            SE_WARN(ret, "Skip load Ctrl page(%" PRIu32 "), ctrl file (%s)", i, ctx->backupPath);
            continue;
        }
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "unable to load ctrl page, id%" PRIu32, i);
            return ret;
        }
        offset = SeGetCtrlFilePageOffset(&ctx->ctrlFile, i);
        ret = SeFileWrite(seIns, ctx->ctrlFile.handle[0], offset, buf);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Flush Ctrl page(%" PRIu32 "), ctrl file (%s)", i, ctx->backupPath);
            return ret;
        }
    }

    offset = SeGetCtrlFilePageOffset(&ctx->ctrlFile, ctx->core->ctrlPageMgr.blockHwm);
    ret = SpcWriteFileTail(seIns, ctx->ctrlFile.handle[0], (uint64_t)offset, seIns->db->checkMagic);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "write backup ctrlfile (%s) tail", ctx->backupPath);
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter SeBackupCtrlFile(SeRunCtxT *seRunCtx, const SeBackupArgsT *args, SeBackupCtxT *ctx)
{
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    StatusInter ret = BackupLoadCtrlPage(seIns, &ctx->pool);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    ret = BackupMapCtrl(seRunCtx, args, ctx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    ret = BackupCreateCtrlFile(seRunCtx, ctx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    return BackupFlushCtrlFile(seRunCtx, ctx);
}

static StatusInter BackupCreateDataFile(SeRunCtxT *seRunCtx, SeBackupCtxT *ctx)
{
    StatusInter ret = STATUS_OK_INTER;
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    char filePath[DB_MAX_WHOLE_PATH] = {0};
    uint32_t fileNum = CtrlGroupGetSize(&ctx->fileGroup);
    PersDataFileHeadT fileHead = {.checkMagic = seIns->db->checkMagic};
    PersFileTailT fileTail = {.checkMagic = seIns->db->checkMagic};
    BufT buf = {0};
    for (uint32_t i = 0; i < fileNum; ++i) {
        DbFileT *file = (DbFileT *)CtrlGroupGetItem(&ctx->fileGroup, i);
        if (file == NULL || !DbFileIsOnline(file->ctrl)) {
            continue;
        }
        ret = SeFileConstructPath(ctx->backupPath, file->ctrl->name, filePath, DB_MAX_WHOLE_PATH);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "prepare datafile ,path (%s, %s)", ctx->backupPath, file->ctrl->name);
            return ret;
        }
        ret = SeFileCreate(filePath, SeGetFileOpExtraFlag(seIns), &file->handle[0]);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "prepare datafile create (%s)", filePath);
            return ret;
        }
        buf = (BufT){.buf = &fileHead, .size = sizeof(PersDataFileHeadT)};
        ret = SeFileWrite(seIns, file->handle[0], 0, buf);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "prepare datafile write head (%s)", filePath);
            return ret;
        }
        buf = (BufT){.buf = &fileTail, .size = sizeof(PersFileTailT)};
        ret = SeFileWrite(seIns, file->handle[0], SeGetFileTailOffset(seIns, file), buf);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "prepare datafile write tail (%s)", filePath);
            return ret;
        }
    }
    return ret;
}

static uint32_t BackupGetFileDeviceExtendNum(SeInstanceT *seIns, SeBackupCtxT *ctx, DevCtrlT *devCtrl)
{
    DbFileT *file = (DbFileT *)CtrlGroupGetItem(&ctx->fileGroup, devCtrl->fileId);
    DB_ASSERT(devCtrl != NULL && file != NULL && file->ctrl != NULL && DbFileIsOnline(file->ctrl) &&
              file->ctrl->deviceNum >= 1);
    uint32_t fullCnt = seIns->seConfig.deviceSize / seIns->seConfig.extendSize;
    // 如果不是最后一个device，那么肯定所有的Extend都用满
    // 如果Extend和device数量计算出的总页数一致，也是都用满了
    if (devCtrl->fileIndex < file->ctrl->deviceNum - 1 || file->ctrl->extendNum == file->ctrl->deviceNum * fullCnt) {
        return fullCnt;
    }
    // 以上条件都不满足，那么最后一个device肯定是没有用满的情况，取余获得实际使用的extendNum
    DB_ASSERT(fullCnt > 0);
    return file->ctrl->extendNum % fullCnt;
}

static StatusInter BackupFlushDataFile(SeRunCtxT *seRunCtx, SeBackupCtxT *ctx)
{
    StatusInter ret = STATUS_OK_INTER;
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    uint32_t devNum = CtrlGroupGetSize(&ctx->deviceGroup);
    uint64_t extendSize = SIZE_K(seIns->seConfig.extendSize);
    DB_ASSERT(ctx->bufferSize >= extendSize);
    BufT buf = {.buf = ctx->mirrorBuffer, .size = extendSize};
    int64_t offset = 0;

    for (uint32_t i = 0; i < devNum; ++i) {
        DbDeviceT *device = (DbDeviceT *)CtrlGroupGetItem(&ctx->deviceGroup, i);
        if (device == NULL || !device->ctrl->online) {
            continue;
        }
        // 从工作路径读出数据，SpcReadDatafile内部负责并发保护
        DbFileT *aFile = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, device->ctrl->fileId);
        DB_ASSERT(aFile != NULL && DbFileIsOnline(aFile->ctrl));
        DbFileT *mFile = (DbFileT *)CtrlGroupGetItem(&ctx->fileGroup, device->ctrl->fileId);
        DB_ASSERT(mFile != NULL && DbFileIsOnline(mFile->ctrl));
        uint32_t iter = BackupGetFileDeviceExtendNum(seIns, ctx, device->ctrl);
        for (uint32_t j = 0; j < iter; ++j) {
            offset = SeGetFileDeviceOffset(seIns, mFile, device->ctrl) + (int64_t)(extendSize * j);
            ret = SpcReadDatafile(seIns, aFile, aFile->handle[SeGetMainZoneId(seIns)], offset, buf);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "backup read active file (%s), offset(%" PRIu64 ")", aFile->ctrl->name, offset);
                return ret;
            }

            // 将读出数据写入备份文件，此处无并发
            offset = SeGetFileDeviceOffset(seIns, mFile, device->ctrl) + (int64_t)(extendSize * j);
            ret = SeFileWrite(seIns, mFile->handle[0], offset, buf);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "backup write mirror file (%s), offset(%" PRIu64 ")", mFile->ctrl->name, offset);
                return ret;
            }
        }
    }
    return ret;
}

inline StatusInter SeBackupDataFile(SeRunCtxT *seRunCtx, SeBackupCtxT *ctx)
{
    StatusInter ret = BackupCreateDataFile(seRunCtx, ctx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    return BackupFlushDataFile(seRunCtx, ctx);
}

static StatusInter BackupSyncCoreCtrl(SeRunCtxT *seRunCtx, SeBackupCtxT *ctx)
{
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    DbSpinLock(&seIns->db->pubCtx->ctrlLock);
    ctx->redo.targetPoint = seIns->db->core.lrpPoint;
    DbSpinUnlock(&seIns->db->pubCtx->ctrlLock);
    TrxMgrT *trxMgr = (TrxMgrT *)seRunCtx->trxMgr;
    RW_LATCH_WLATCH_RETURN_IF_FAILED(&trxMgr->latch);
    ctx->core->maxTrxId = TrxMgrGetAndIncMaxTrxId(trxMgr);
    DbInterProcRWUnlatchW(&trxMgr->latch);
    return STATUS_OK_INTER;
}

static StatusInter BackupCreateRedoFile(SeRunCtxT *seRunCtx, SeBackupCtxT *ctx)
{
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    char filePath[DB_MAX_WHOLE_PATH] = {0};
    errno_t err = snprintf_s(filePath, DB_MAX_WHOLE_PATH, DB_MAX_WHOLE_PATH - 1, "%s/%s%u", ctx->backupPath,
        seIns->redoMgr->cfg.redoFilePrefix, ctx->redo.backupPoint.fsn - 1);
    if (err < 0) {
        SE_LAST_ERROR(INT_ERR_SECUREC_MEMORY_COPY_FAIL, "construct file name, ret =%" PRId32, err);
        return INT_ERR_SECUREC_MEMORY_COPY_FAIL;
    }

    StatusInter ret = SeFileCreate(filePath, SeGetFileOpExtraFlag(seIns), &ctx->redo.fd);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "backup create redo file (%s)", filePath);
        return ret;
    }

    uint32_t size = RedoLogFileHeaderSize(seIns->redoMgr->cfg.blockSize);
    DB_ASSERT(size < ctx->bufferSize);
    RedoLogFileHeadT *head = (RedoLogFileHeadT *)ctx->mirrorBuffer;
    (void)memset_s(head, size, 0, size);
    RedoFileHeadInit(head, ctx->redo.backupPoint.fsn, seIns->redoMgr->cfg.blockSize);
    ret = RedoCalcFileHeadChecksum(seIns->redoMgr, head);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "calculate redo head checksum");
        return ret;
    }
    ret = SeFileWrite(seIns, ctx->redo.fd, 0, (BufT){.buf = head, .size = size});
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "backup flush redo file head (%s)", filePath);
        return ret;
    }

    ctx->redo.backupPoint.blockId = REDO_INITIAL_BLOCK;
    return STATUS_OK_INTER;
}

static StatusInter BackupFlushBackupRedo(SeRunCtxT *seRunCtx, SeBackupCtxT *ctx, uint8_t *buf)
{
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    RedoLogBatchT *batch = (RedoLogBatchT *)buf;
    batch->head.point = ctx->redo.backupPoint;
    RedoLogBatchTailT *batchTail = (RedoLogBatchTailT *)(buf + RedoGetBatchTailOffset(seIns->redoMgr, batch));
    batchTail->point = batch->head.point;
    StatusInter ret = RedoCalcBatchChecksum(batch, seIns->redoMgr);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    uint32_t offset = ctx->redo.backupPoint.blockId * seIns->redoMgr->cfg.blockSize;
    ret = SeFileWrite(seIns, ctx->redo.fd, offset, (BufT){.buf = batch, .size = batch->size});
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Backup write redofile (%" PRIu32 ", %" PRIu32 "). batch (" REDO_POINT_LOG_FMT ")",
            ctx->redo.backupPoint.fsn, ctx->redo.fd, REDO_POINT(&batch->head.point));
        return ret;
    }

    uint32_t size = RedoLogFileHeaderSize(seIns->redoMgr->cfg.blockSize);
    DB_ASSERT(size < ctx->bufferSize);
    RedoLogFileHeadT *head = (RedoLogFileHeadT *)ctx->mirrorBuffer;
    (void)memset_s(head, size, 0, size);
    ret = SeFileRead(ctx->redo.fd, 0, (BufT){.buf = head, .size = size});
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Backup read redofile head (%" PRIu32 ", %" PRIu32 "). batch (" REDO_POINT_LOG_FMT ")",
            ctx->redo.backupPoint.fsn, ctx->redo.fd, REDO_POINT(&batch->head.point));
        return ret;
    }
    DB_ASSERT(offset == head->writePos);
    head->writePos += DB_CALC_ALIGN(batch->size, seIns->redoMgr->cfg.blockSize);
    ret = RedoCalcFileHeadChecksum(seIns->redoMgr, head);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "calculate redo head checksum");
        return ret;
    }
    ret = SeFileWrite(seIns, ctx->redo.fd, 0, (BufT){.buf = head, .size = size});
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Backup flush backup redo file head (%" PRIu32 ", %" PRIu32 ")", ctx->redo.backupPoint.fsn,
            ctx->redo.fd);
        return ret;
    }
    ctx->redo.backupPoint.blockId = head->writePos / seIns->redoMgr->cfg.blockSize;
    ctx->redo.backupPoint.batchId++;
    return STATUS_OK_INTER;
}

static StatusInter BackupFlushRedoFile(SeRunCtxT *seRunCtx, SeBackupCtxT *ctx)
{
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    RedoCursorT *cursor = NULL;
    StatusInter ret = RedoScanBegin(seIns->redoMgr, &ctx->redo.startPoint, &cursor);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Backup begin scane redo file.");
        return ret;
    }
    ret = BackupCreateRedoFile(seRunCtx, ctx);
    if (ret != STATUS_OK_INTER) {
        RedoScanClose(cursor);
        return ret;
    }
    RedoLogBatchT *batch = NULL;
    uint32_t freeSize = 0;
    do {
        ret = RedoScanNext(cursor);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret,
                "reach target point (" REDO_POINT_LOG_FMT "), start point (" REDO_POINT_LOG_FMT
                "), cur point (" REDO_POINT_LOG_FMT ")",
                REDO_POINT(&ctx->redo.targetPoint), REDO_POINT(&ctx->redo.startPoint), REDO_POINT(&cursor->point));
            // this may caused by too many concurrent operations in same period with insufficient redo file, so that
            // the redofile has been erased, pleace retry later.
            ret = FLUSH_STATE_ERROR_INTER;
            break;
        }
        // 达到目标回放点，跳出循环
        if (!cursor->hasData) {
            DB_ASSERT(RedoPointCmp(&cursor->point, &ctx->redo.targetPoint) >= 0);  // 扫描结束后，应能遍历到targetPoint
            DB_LOG_INFO_UNFOLD("reaches target point (" REDO_POINT_LOG_FMT ", " REDO_POINT_LOG_FMT "), stop",
                REDO_POINT(&ctx->redo.startPoint), REDO_POINT(&ctx->redo.targetPoint));
            break;
        }
        // 检查当前的备份日志文件大小是否可以满足新的batch插入
        batch = (RedoLogBatchT *)(void *)(cursor->buf.buf);
        freeSize = seIns->redoMgr->cfg.fileSize - ctx->redo.backupPoint.blockId * seIns->redoMgr->cfg.blockSize;
        if (freeSize < batch->size) {
            SeFileClose(&ctx->redo.fd);
            ctx->redo.backupPoint.fsn++;
            ret = BackupCreateRedoFile(seRunCtx, ctx);
        }
        // 空间满足的情况下执行搬迁
        if (ret == STATUS_OK_INTER) {
            ret = BackupFlushBackupRedo(seRunCtx, ctx, cursor->buf.buf);
        }
    } while (ret == STATUS_OK_INTER);
    RedoScanClose(cursor);
    return ret;
}

static StatusInter BackupConcludeCoreCtrl(SeRunCtxT *seRunCtx, const SeBackupArgsT *args, SeBackupCtxT *ctx)
{
    if (args->spaceNum == SE_BACK_UP_KEEP_ALL_SPACE) {
        DbAtomicSet(&ctx->core->coreStatus, SE_ON_DISK_BACK_UP_FULL);
    } else {
        DbAtomicSet(&ctx->core->coreStatus, SE_ON_DISK_BACK_UP_SCHEMA);
    }
    ctx->core->lrpPoint = ctx->redo.backupPoint;
    uint8_t *page = NULL;
    StatusInter ret = SeCtrlPagePoolLoadPage(&ctx->pool, CORE_CTRL_PAGE_ID, false, &page);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "mirror page pool find core ctrl");
        DB_ASSERT(false);
        return ret;
    }
    int64_t offset = SeGetCtrlFilePageOffset(&ctx->ctrlFile, CORE_CTRL_PAGE_ID);
    ret = SeFileWrite(
        seRunCtx->seIns, ctx->ctrlFile.handle[0], offset, (BufT){.buf = page, .size = ctx->ctrlFile.blockSize});
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Flush corectrl at conclude ctrl file (%s)", ctx->backupPath);
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter SeBackupRedoFile(SeRunCtxT *seRunCtx, const SeBackupArgsT *args, SeBackupCtxT *ctx)
{
    StatusInter ret = BackupSyncCoreCtrl(seRunCtx, ctx);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Backup sync coreCtrl");
        return ret;
    }
    if (RedoPointCmp(&ctx->redo.startPoint, &ctx->redo.targetPoint) < 0 &&
        args->spaceNum == SE_BACK_UP_KEEP_ALL_SPACE) {
        ret = BackupFlushRedoFile(seRunCtx, ctx);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Backup flush redoFile");
            return ret;
        }
    }
    ret = BackupConcludeCoreCtrl(seRunCtx, args, ctx);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Backup conclude coreCtrl");
        return ret;
    }
    return STATUS_OK_INTER;
}

Status SeBackupDatabase(SeRunCtxT *seRunCtx, const SeBackupArgsT *args)
{
#ifdef FEATURE_TS
    if (args->spaceNum == SE_BACK_UP_KEEP_ALL_SPACE) {
        DB_LOG_ERROR_UNFOLD(GMERR_FEATURE_NOT_SUPPORTED, "TSDB backup all space");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
#endif

    SeBackupCtxT *ctx = NULL;
    StatusInter ret = SeAllocBackupCtx(seRunCtx, &ctx);
    if (ret != STATUS_OK_INTER) {
        return DbGetExternalErrno(ret);
    }

    // 启动备份前尽可能地推进TruncPoint
    ret = CkptTriggerImpl(seRunCtx->seIns, CKPT_MODE_FULL, true, 0);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "trigger checkpoint before backup db");
        goto EXIT;
    }

    ret = BackupPrepareDir(seRunCtx, args, ctx);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }
    // truncpoint
    ret = SeBackupCtrlFile(seRunCtx, args, ctx);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }

    ret = SeBackupDataFile(seRunCtx, ctx);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }
    // lrppoint
    ret = SeBackupRedoFile(seRunCtx, args, ctx);
EXIT:
    SeFreeBackupCtx(seRunCtx, ctx);
    return DbGetExternalErrno(ret);
}

StatusInter SePersistRegisterBackup(SeRunCtxHdT seRunCtx)
{
    if (SECUREC_UNLIKELY(SeGetStorageStatus(seRunCtx->seIns) == SE_ON_DISK_EMRGNCY)) {
        return DATABASE_NOT_AVAILABLE_INTER;
    }
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    SeTrxLatchCtrlT *ctrl = (SeTrxLatchCtrlT *)seIns->trxLatchCtrl;
    DbSpinLock(&ctrl->lock);
    if (ctrl->backupRegistered) {
        SE_ERROR(LOCK_NOT_AVAILABLE_INTER, "another backup already in progress");
        DbSpinUnlock(&ctrl->lock);
        return LOCK_NOT_AVAILABLE_INTER;
    }
    if (ctrl->swapRegistered) {
        SE_ERROR(LOCK_NOT_AVAILABLE_INTER, "swap already in progress, can't register");
        DbSpinUnlock(&ctrl->lock);
        return LOCK_NOT_AVAILABLE_INTER;
    }
    ctrl->backupRegistered = true;
    uint64_t splitStartTime = DbClockGetTsc();  // 获取开始等待时间
    while (!DbExceedTime(splitStartTime, DB_BACK_UP_LATCH_TIMEOUT / USECONDS_IN_MSECOND)) {
        if (ctrl->ddlOpCnt == 0) {
            DbSpinUnlock(&ctrl->lock);
            return STATUS_OK_INTER;
        }
        DbSpinUnlock(&ctrl->lock);
        DbSleep(DB_BACK_UP_LATCH_SPLIT_TIMEOUT);
        (void)seIns->seConfig.seKeepThreadAlive(seIns->dbInstance);
        SE_WARN(LOCK_NOT_AVAILABLE_INTER, "Backup still waiting for begin");
        DbSpinLock(&ctrl->lock);
    }
    SE_ERROR(LOCK_NOT_AVAILABLE_INTER, "Backup wait too long");
    ctrl->backupRegistered = false;
    DbSpinUnlock(&ctrl->lock);
    return LOCK_NOT_AVAILABLE_INTER;
}

void SePersistUnRegisterBackup(SeRunCtxHdT seRunCtx)
{
    SeTrxLatchCtrlT *ctrl = (SeTrxLatchCtrlT *)((SeInstanceT *)seRunCtx->seIns)->trxLatchCtrl;
    DbSpinLock(&ctrl->lock);
    ctrl->backupRegistered = false;
    DbSpinUnlock(&ctrl->lock);
}

StatusInter SeRegisterFlush(SeRunCtxHdT seRunCtx, uint32_t waitTime)
{
    if (SECUREC_UNLIKELY(SeGetStorageStatus(seRunCtx->seIns) == SE_ON_DISK_EMRGNCY)) {
        return DATABASE_NOT_AVAILABLE_INTER;
    }
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    SeTrxLatchCtrlT *ctrl = (SeTrxLatchCtrlT *)seIns->trxLatchCtrl;
    DbSpinLock(&ctrl->lock);
    if (ctrl->flushRegistered) {
        SE_ERROR(LOCK_NOT_AVAILABLE_INTER, "another flush already in progress");
        DbSpinUnlock(&ctrl->lock);
        return LOCK_NOT_AVAILABLE_INTER;
    }
    if (ctrl->swapRegistered) {
        SE_ERROR(LOCK_NOT_AVAILABLE_INTER, "There's swap already in progress");
        DbSpinUnlock(&ctrl->lock);
        return LOCK_NOT_AVAILABLE_INTER;
    }
    ctrl->flushRegistered = true;
    uint64_t splitStartTime = DbClockGetTsc();  // 获取开始等待时间
    while (!DbExceedTime(splitStartTime, waitTime)) {
        if (ctrl->dmlOpCnt == 0) {
            DB_ASSERT(ctrl->ddlOpCnt == 0);
            DbSpinUnlock(&ctrl->lock);
            return STATUS_OK_INTER;
        }
        DbSpinUnlock(&ctrl->lock);
        DbSleep(DB_BACK_UP_LATCH_SPLIT_TIMEOUT);
        (void)seIns->seConfig.seKeepThreadAlive(seIns->dbInstance);
        SE_WARN(LOCK_NOT_AVAILABLE_INTER, "flush still waiting for begin");
        DbSpinLock(&ctrl->lock);
    }
    SE_ERROR(LOCK_NOT_AVAILABLE_INTER, "flush wait too long");
    ctrl->flushRegistered = false;
    DbSpinUnlock(&ctrl->lock);
    return LOCK_NOT_AVAILABLE_INTER;
}

void SeUnRegisterFlush(SeRunCtxHdT seRunCtx)
{
    SeTrxLatchCtrlT *ctrl = (SeTrxLatchCtrlT *)((SeInstanceT *)seRunCtx->seIns)->trxLatchCtrl;
    DbSpinLock(&ctrl->lock);
    ctrl->flushRegistered = false;
    DbSpinUnlock(&ctrl->lock);
}

StatusInter SeRegisterDataOpImpl(SeRunCtxHdT seRunCtx, bool dml)
{
    DB_POINTER(seRunCtx);
    if (SECUREC_UNLIKELY(SeGetStorageStatus(seRunCtx->seIns) == SE_ON_DISK_EMRGNCY)) {
        return DATABASE_NOT_AVAILABLE_INTER;
    }
    if (seRunCtx->swapCtx) {
        return STATUS_OK_INTER;
    }
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    SeTrxLatchCtrlT *ctrl = (SeTrxLatchCtrlT *)seIns->trxLatchCtrl;
    DbSpinLock(&ctrl->lock);
    uint64_t splitStartTime = DbClockGetTsc();  // 获取开始等待时间
    while (!DbExceedTime(splitStartTime, DB_FLUSH_LATCH_TIMEOUTMS)) {
        if (ctrl->swapRegistered) {
            SE_ERROR(LOCK_NOT_AVAILABLE_INTER, "There's swap already in progress");
            DbSpinUnlock(&ctrl->lock);
            return LOCK_NOT_AVAILABLE_INTER;
        }
        if (!ctrl->flushRegistered || !dml) {
            if (dml) {
                ctrl->dmlOpCnt++;
            } else {
                ctrl->dqlOpCnt++;
            }
            DbSpinUnlock(&ctrl->lock);
            return STATUS_OK_INTER;
        }
        DbSpinUnlock(&ctrl->lock);
        DbSleep(DB_BACK_UP_LATCH_SPLIT_TIMEOUT);
        (void)seIns->seConfig.seKeepThreadAlive(seIns->dbInstance);
        SE_WARN(LOCK_NOT_AVAILABLE_INTER, "dataop still waiting for begin");
        DbSpinLock(&ctrl->lock);
    }
    SE_ERROR(LOCK_NOT_AVAILABLE_INTER, "dataop wait too long");
    DbSpinUnlock(&ctrl->lock);
    return LOCK_NOT_AVAILABLE_INTER;
}

void SeUnRegisterDataOpImpl(SeRunCtxHdT seRunCtx, bool dml)
{
    if (seRunCtx->swapCtx || !seRunCtx->isPersistence) {
        return;
    }
    SeTrxLatchCtrlT *ctrl = (SeTrxLatchCtrlT *)((SeInstanceT *)seRunCtx->seIns)->trxLatchCtrl;
    DbSpinLock(&ctrl->lock);
    if (dml) {
        if (ctrl->dmlOpCnt > 0) {
            ctrl->dmlOpCnt--;
        }
    } else {
        if (ctrl->dqlOpCnt > 0) {
            ctrl->dqlOpCnt--;
        }
    }
    DbSpinUnlock(&ctrl->lock);
}

Status SePersistRegisterDDLOp(SeRunCtxHdT seRunCtx)
{
    if (SECUREC_UNLIKELY(SeGetStorageStatus(seRunCtx->seIns) == SE_ON_DISK_EMRGNCY)) {
        return GMERR_DATABASE_NOT_AVAILABLE;
    }
    if (seRunCtx->ddlRegistered || seRunCtx->swapCtx) {
        return GMERR_OK;
    }
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    SeTrxLatchCtrlT *ctrl = (SeTrxLatchCtrlT *)seIns->trxLatchCtrl;
    DbSpinLock(&ctrl->lock);
    uint64_t splitStartTime = DbClockGetTsc();  // 获取开始等待时间
    while (!DbExceedTime(splitStartTime, DB_BACK_UP_LATCH_TIMEOUT / USECONDS_IN_MSECOND)) {
        DB_ASSERT(ctrl->dmlOpCnt != 0);
        if (!ctrl->backupRegistered) {
            ctrl->ddlOpCnt++;
            seRunCtx->ddlRegistered = true;
            DbSpinUnlock(&ctrl->lock);
            return GMERR_OK;
        }
        DbSpinUnlock(&ctrl->lock);
        DbSleep(DB_BACK_UP_LATCH_SPLIT_TIMEOUT);
        (void)seIns->seConfig.seKeepThreadAlive(seIns->dbInstance);
        SE_WARN(LOCK_NOT_AVAILABLE_INTER, "Systable op still waiting for begin");
        DbSpinLock(&ctrl->lock);
    }
    SE_ERROR(LOCK_NOT_AVAILABLE_INTER, "Systable op wait too long");
    DbSpinUnlock(&ctrl->lock);
    return GMERR_DATABASE_NOT_AVAILABLE;
}

void SeUnRegisterDDLOpImpl(SeRunCtxHdT seRunCtx)
{
    if (!seRunCtx->ddlRegistered || seRunCtx->swapCtx) {
        return;
    }
    SeTrxLatchCtrlT *ctrl = (SeTrxLatchCtrlT *)((SeInstanceT *)seRunCtx->seIns)->trxLatchCtrl;
    DbSpinLock(&ctrl->lock);
    DB_ASSERT(ctrl->ddlOpCnt > 0);
    ctrl->ddlOpCnt--;
    seRunCtx->ddlRegistered = false;
    DbSpinUnlock(&ctrl->lock);
}

static inline bool SwapCheck(SeInstanceT *seIns, SeTrxLatchCtrlT *ctrl)
{
    bool res = false;
    if (SECUREC_LIKELY(SeGetStorageStatus(seIns) != SE_ON_DISK_EMRGNCY)) {
        res = ctrl->dmlOpCnt == 0 && ctrl->dqlOpCnt == 0 && !ctrl->backupRegistered && !ctrl->flushRegistered;
    } else {
        res = SeCheckEmergencyTrxState(seIns) == STATUS_OK_INTER;
    }
    return res;
}

StatusInter SePersistRegisterSwap(SeRunCtxHdT seRunCtx)
{
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    SeTrxLatchCtrlT *ctrl = (SeTrxLatchCtrlT *)seIns->trxLatchCtrl;
    DbSpinLock(&ctrl->lock);
    if (ctrl->swapRegistered) {
        SE_ERROR(LOCK_NOT_AVAILABLE_INTER, "There's swap already in progress, can't register swap");
        DbSpinUnlock(&ctrl->lock);
        return LOCK_NOT_AVAILABLE_INTER;
    }
    ctrl->swapRegistered = true;
    uint64_t splitStartTime = DbClockGetTsc();  // 获取开始等待时间
    while (!DbExceedTime(splitStartTime, DB_SWAP_LATCH_TIMEOUT / USECONDS_IN_MSECOND)) {
        if (SwapCheck(seIns, ctrl)) {
            DbSpinUnlock(&ctrl->lock);
            return STATUS_OK_INTER;
        }
        DbSpinUnlock(&ctrl->lock);
        DbSleep(DB_BACK_UP_LATCH_SPLIT_TIMEOUT);
        (void)seIns->seConfig.seKeepThreadAlive(seIns->dbInstance);
        SE_WARN(LOCK_NOT_AVAILABLE_INTER, "swap still waiting for begin");
        DbSpinLock(&ctrl->lock);
    }
    SE_ERROR(LOCK_NOT_AVAILABLE_INTER, "swap wait too long for begin.");
    ctrl->swapRegistered = false;
    DbSpinUnlock(&ctrl->lock);
    return LOCK_NOT_AVAILABLE_INTER;
}

void SeSePersistUnRegisterSwap(SeRunCtxHdT seRunCtx)
{
    SeTrxLatchCtrlT *ctrl = (SeTrxLatchCtrlT *)((SeInstanceT *)seRunCtx->seIns)->trxLatchCtrl;
    DbSpinLock(&ctrl->lock);
    ctrl->swapRegistered = false;
    DbSpinUnlock(&ctrl->lock);
}
