/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: se层文件操作
 * Create: 2023-6
 */

#include "se_file.h"
#include "db_log.h"
#include "db_timer.h"
#include "db_last_error.h"
#include "se_database_util.h"
#include "se_define.h"
#include "se_log.h"
#include "db_alarm.h"
#include "adpt_file.h"

void DbAlarmUpdateSeFileStatus(double usedRatio)
{
    // 只有磁盘访问故障状态才会刷新
    DbAlarmInfoNodeT *alarmNode = DbGetAlarmNode(DB_ALARM_STORAGE_FILE, 0);
    if (alarmNode == NULL || alarmNode->alarmInfo.alarmData.alarmStatus != DB_ALARM_ACTIVE) {
        return;
    }
    (void)DbCheckGlobalAlarmRes(DB_ALARM_STORAGE_FILE, usedRatio);
}

void SeFileAlarmUpdate(Status operateRet)
{
    // 统计访问磁盘文件故障的次数
    switch (operateRet) {
        case GMERR_OK:
            DbAlarmUpdateSuccCnt(DB_ALARM_STORAGE_SPACE_USED_INFO, 1);
            DbAlarmUpdateSuccCnt(DB_ALARM_STORAGE_FILE, 1);
            DbAlarmUpdateSeFileStatus(0);
            return;
        case GMERR_DISK_NO_SPACE_ERROR:
            DbAlarmUpdateFailCnt(DB_ALARM_STORAGE_SPACE_USED_INFO, 1);
            return;
        case GMERR_FILE_OPERATE_FAILED:
            DbAlarmUpdateFailCnt(DB_ALARM_STORAGE_FILE, 1);
            DB_LOG_ERROR(operateRet, "se file operate exception alarm.");
            (void)DbCheckGlobalAlarmRes(DB_ALARM_STORAGE_FILE, 1);
            return;
        default:
            return;
    }
}

StatusInter SeFileOpen(const char *name, int32_t flags, int32_t *handle)
{
    if (*handle > DB_INVALID_FD) {
        // device already opened, nothing to do.
        return STATUS_OK_INTER;
    }
    uint32_t mode = O_BINARY | O_RDWR | (uint32_t)flags;
    Status ret = DbOpenFile(name, (int32_t)mode, 0, handle);
    SeFileAlarmUpdate(ret);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Open file(%s), os ret no: %" PRId32 ".", name, DbAptGetErrno());
        return DbGetStatusInterErrno(ret);
    }

    return STATUS_OK_INTER;
}

void SeFileClose(int32_t *handle)
{
    if (*handle != DB_INVALID_FD) {
        DbCloseFile(*handle);
        *handle = DB_INVALID_FD;
    }
}

StatusInter SeFileRemove(const char *name)
{
    Status ret = DbRemoveFileNoLog(name);
    SeFileAlarmUpdate(ret);
    // 文件已经删除时，不用打印日志
    if (ret != GMERR_OK && DbAptGetErrno() != ENOENT) {
        DB_LOG_ERROR(ret, "Remove file(%s), os ret no(%" PRId32 ")", name, DbAptGetErrno());
        return DbGetStatusInterErrno(ret);
    }

    return STATUS_OK_INTER;
}

StatusInter SeFileRead(int32_t handle, int64_t offset, BufT bufData)
{
    uint32_t readSize = 0;
    Status ret = DbPreadFile(handle, (char *)bufData.buf, bufData.size, offset, &readSize);
    SeFileAlarmUpdate(ret);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret,
            "Pread file, handle(%" PRId32 "), offset(%" PRId64 "), data size(%" PRIu32 "), os ret no(%" PRId32 ")",
            handle, offset, bufData.size, DbAptGetErrno());
        return DbGetStatusInterErrno(ret);
    }

    return STATUS_OK_INTER;
}

StatusInter SeFileWrite(SeInstanceT *seIns, int32_t handle, int64_t offset, BufT bufData)
{
    // 锁库时的兜底拦截
    if (SeGetStorageStatus(seIns) == SE_ON_DISK_EMRGNCY) {
        SE_LAST_ERROR(DATABASE_NOT_AVAILABLE_INTER, "Write file when disk emergncy or swapping dir");
        return DATABASE_NOT_AVAILABLE_INTER;
    }
    uint64_t startTime = DbClockGetTsc();
    Status ret = DbPwriteFile(handle, (const char *)bufData.buf, bufData.size, offset);
    SeFileAlarmUpdate(ret);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret,
            "write file(fd=%" PRId32 ", offset=%" PRId64 "), data size %" PRIu32 ",  os return: %" PRId32 ".", handle,
            offset, bufData.size, DbAptGetErrno());
        return DbGetStatusInterErrno(ret);
    }
    uint64_t endTime = DbClockGetTsc();
    if (SECUREC_UNLIKELY(DbToMseconds(endTime - startTime) > SE_FILE_TIMEOUT_MS)) {
        DB_LOG_WARN(
            GMERR_REQUEST_TIME_OUT, "Write file too long. (time: %" PRIu64 ")", DbToMseconds(endTime - startTime));
    }
    return STATUS_OK_INTER;
}

StatusInter SeFileCreate(const char *name, int32_t flags, int32_t *handle)
{
    uint32_t mode = O_BINARY | O_RDWR | O_EXCL | O_CREAT | (uint32_t)flags;
    uint32_t perm = ((mode & O_CREAT) != 0) ? (S_IRUSR | S_IWUSR) : 0;
    Status ret = DbOpenFile(name, (int32_t)mode, perm, handle);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_FILE_OPERATE_FAILED, "Create file(%s), os ret no: %" PRId32 ".", name, DbAptGetErrno());
        return FILE_OPERATE_FAILED_INTER;
    }

    return STATUS_OK_INTER;
}

StatusInter SeFileOpenCreate(const char *name, int32_t flags, int32_t *handle)
{
    StatusInter ret = STATUS_OK_INTER;
    if (DbFileExist(name)) {
        size_t fileSize = 0;
        ret = DbGetStatusInterErrno(DbFileSize(name, &fileSize));
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Calculate file(%s), os ret no: %" PRId32 ".", name, DbAptGetErrno());
            return ret;
        }
        if (fileSize == 0) {
            return SeFileOpen(name, flags, handle);
        }
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FILE_OPERATE_FAILED, "File(%s) already exist and not empty", name);
        return FILE_OPERATE_FAILED_INTER;
    }
    return SeFileCreate(name, flags, handle);
}

StatusInter SeFileConstructPath(const char *path, const char *fileName, char *buff, uint32_t buffSize)
{
    if (SeIsStringEmpty(path)) {
        SE_LAST_ERROR(OBJECT_NOT_IN_PREREQUISITE_STATE_INTER, "Try construct empty path");
        return OBJECT_NOT_IN_PREREQUISITE_STATE_INTER;
    }
    uint32_t pathLen = (uint32_t)strlen(path);
    if (pathLen >= buffSize) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "path %s too long, buffer size %" PRIu32, path, buffSize);
        return DATA_EXCEPTION_INTER;
    }
    char *format;
    if (path[pathLen - 1] == '/') {
        format = "%s%s";
    } else {
        format = "%s/%s";
    }
    errno_t err = snprintf_s(buff, buffSize, buffSize - 1, format, path, fileName);
    if (err < 0) {
        SE_LAST_ERROR(
            DATA_EXCEPTION_INTER, "path %s or file name %s too long, buffer size %" PRIu32, path, fileName, buffSize);
        return DATA_EXCEPTION_INTER;
    }
    return STATUS_OK_INTER;
}

StatusInter SeFileFullPath(const char *path, char *realPath, size_t maxLen)
{
    if (path[0] == OS_PATH_SEP_CHAR) {
        errno_t err = strcpy_s(realPath, maxLen, path);
        if (err != EOK) {
            SE_ERROR(
                INVALID_PARAMETER_VALUE_INTER, "SeFileFullPath Copy full path (%s), os no %" PRId32 "", path, errno);
            return INVALID_PARAMETER_VALUE_INTER;
        }
        return STATUS_OK_INTER;
    }
    StatusInter ret = STATUS_OK_INTER;
    char tmpPath[DB_MAX_PATH] = {0};
    errno_t err = strcpy_s(tmpPath, DB_MAX_PATH, path);
    if (err != EOK) {
        SE_ERROR(DATA_EXCEPTION_INTER, "Copy path(%s), os no %" PRId32 "", path, errno);
        return DATA_EXCEPTION_INTER;
    }
    size_t offset = strlen(path);
    while (DbAdptRealPathNoLog(tmpPath, realPath) != GMERR_OK && offset != 0) {
        char *trimPart = strrchr(tmpPath, OS_PATH_SEP_CHAR);
        if (trimPart == NULL) {
            offset = 0;
        } else {
            offset -= strlen(trimPart);
            tmpPath[offset] = '\0';
        }
    }
    if (offset == 0) {
        ret = DbGetStatusInterErrno(DbAdptRealPath("./", realPath));
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    err = strcat_s(realPath, maxLen, path + offset);
    if (err != EOK) {
        SE_ERROR(DATA_EXCEPTION_INTER, "SeFileFullPath concat path(%s), os no %" PRId32 "", path, errno);
        return DATA_EXCEPTION_INTER;
    }
    return STATUS_OK_INTER;
}
