/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: vfd 实现
 */
#include "se_vfd.h"
#include "adpt_io.h"
#include "adpt_string.h"
#include "db_file.h"
#include "db_last_error.h"
#include "db_log.h"
#include "db_timer.h"
#include "se_define.h"
#include "se_log.h"
#include "se_instance.h"
#include "db_internal_error.h"
#include "db_dyn_load.h"

#define SE_VFD_TIMEOUT_MS 100
#define VFD_MAX_PHYSIC_FD_NUMS 512

static inline SeVfdNodeT *VfdGetNodeFromCache(VirtualFdMgrT *vfdMgr, int32_t vfdHandle)
{
    DB_ASSERT(vfdHandle >= 0 && vfdHandle < vfdMgr->vfdCacheSize);
    return &vfdMgr->vfdCache[vfdHandle];
}

static void VfdMgrInitVfdCache(VirtualFdMgrT *vfdMgr)
{
    SeVfdNodeT *vfdCache = vfdMgr->vfdCache;
    for (int32_t i = 0; i < vfdMgr->vfdCacheSize; ++i) {
        SeVfdNodeT *curNode = &vfdCache[i];
        DbAtomicSet16(&curNode->useCount, 0);
        curNode->vfd = i;
        curNode->fd = DB_INVALID_FD;
        curNode->flag = 0;
        curNode->filename = NULL;
        curNode->nodeState = VFD_CLOSED;
        curNode->isResident = false;
        if (i == 0) {
            curNode->next = i + 1;
            curNode->prev = DB_INVALID_FD;
        } else if (i == vfdMgr->vfdCacheSize - 1) {
            curNode->next = DB_INVALID_FD;
            curNode->prev = i - 1;
        } else {
            curNode->next = i + 1;
            curNode->prev = i - 1;
        }
    }
}

static StatusInter VfdMgrInit(VirtualFdMgrT *vfdMgr, SeInstanceT *seIns)
{
    // 需要预留一套句柄，在DB启动恢复时使用
    vfdMgr->vfdCacheSize = (int32_t)seIns->seConfig.dbFilesMaxCnt * ((int32_t)seIns->seConfig.multiZoneNum + 1);
    // 巡检功能会临时打开dbFilesMaxCnt个句柄
    if (seIns->seConfig.crcCheckEnable || seIns->seConfig.shaCheckEnable) {
        vfdMgr->vfdCacheSize += (int32_t)seIns->seConfig.dbFilesMaxCnt;
    }
    size_t vfdCacheMemSize = (size_t)vfdMgr->vfdCacheSize * sizeof(SeVfdNodeT);
    vfdMgr->vfdCache = (SeVfdNodeT *)DbDynMemCtxAlloc(seIns->seServerMemCtx, vfdCacheMemSize);
    if (vfdMgr->vfdCache == NULL) {
        SE_LAST_ERROR(
            MEMORY_OPERATE_FAILED_INTER, "Alloc vfd cache for mgr, size = %" PRIu32 "", (uint32_t)vfdCacheMemSize);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    vfdMgr->physicFdLimit = DB_MIN((uint32_t)VFD_MAX_PHYSIC_FD_NUMS, (uint32_t)vfdMgr->vfdCacheSize);
    vfdMgr->seDynMemCtx = seIns->seServerMemCtx;
    vfdMgr->nextFree = 0;
    vfdMgr->lruMoreRecently = DB_INVALID_FD;
    vfdMgr->lruLessRecently = DB_INVALID_FD;
    DbAtomicSet16(&vfdMgr->curOpeningFileNum, 0);
    vfdMgr->seIns = seIns;
    VfdMgrInitVfdCache(vfdMgr);
    DbSpinInit(&vfdMgr->lruLock);
    DbSpinInit(&vfdMgr->freeListLock);
    return STATUS_OK_INTER;
}

static bool VfdMgrNeedOpen(const VirtualFdMgrT *vfdMgr)
{
    return vfdMgr != NULL && vfdMgr->seIns->needVfd;
}

StatusInter VfdMgrConstructor(SeInstanceT *seIns)
{
    if (seIns->vfdMgr != NULL || !seIns->needVfd) {
        return STATUS_OK_INTER;
    }
    size_t vfdMgrSize = sizeof(VirtualFdMgrT);
    VirtualFdMgrT *vfdMgr = (VirtualFdMgrT *)DbDynMemCtxAlloc(seIns->seServerMemCtx, vfdMgrSize);
    if (vfdMgr == NULL) {
        SE_LAST_ERROR(
            MEMORY_OPERATE_FAILED_INTER, "Alloc vfd mgr to construct, size = %" PRIu32 "", (uint32_t)vfdMgrSize);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    (void)memset_s(vfdMgr, vfdMgrSize, 0, vfdMgrSize);
    StatusInter ret = VfdMgrInit(vfdMgr, seIns);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Init vfd mgr when construct");
        DbDynMemCtxFree(seIns->seServerMemCtx, vfdMgr);
        return ret;
    }
    seIns->vfdMgr = vfdMgr;
    return ret;
}

void VfdMgrDestructor(SeInstanceT *seIns)
{
    VirtualFdMgrT *vfdMgr = seIns->vfdMgr;
    if (vfdMgr == NULL) {
        return;
    }
    for (int32_t i = 0; i < vfdMgr->vfdCacheSize; ++i) {
        SeVfdNodeT *curNode = &vfdMgr->vfdCache[i];
        if (curNode->nodeState != VFD_CLOSED && curNode->fd != DB_INVALID_FD) {
            DB_LOG_INFO_UNFOLD("VfdMgrDestructor vfd file close: vfd(%d) fd(%d) file:%s", curNode->vfd, curNode->fd,
                curNode->filename);
            DbCloseFile(curNode->fd);
        }
        if (curNode->filename != NULL) {
            DbDynMemCtxFree(vfdMgr->seDynMemCtx, curNode->filename);
            curNode->filename = NULL;
        }
    }
    DbDynMemCtxFree(vfdMgr->seDynMemCtx, vfdMgr->vfdCache);
    vfdMgr->vfdCache = NULL;
    DbDynMemCtxFree(seIns->seServerMemCtx, seIns->vfdMgr);
    seIns->vfdMgr = NULL;
    DB_LOG_INFO("Vfd destroy success.");
}

/**************************** inner LRU operation &  inner free list operation: begin ↓ ***************************/
// insert to free list
static void VfdFreeListInsertNode(VirtualFdMgrT *vfdMgr, int32_t vfdHandle)
{
    DbSpinLock(&vfdMgr->freeListLock);
    SeVfdNodeT *curNode = VfdGetNodeFromCache(vfdMgr, vfdHandle);
    curNode->prev = DB_INVALID_FD;
    curNode->next = vfdMgr->nextFree;
    vfdMgr->nextFree = vfdHandle;
    if (curNode->next != DB_INVALID_FD) {
        SeVfdNodeT *nextNode = &vfdMgr->vfdCache[curNode->next];
        nextNode->prev = vfdHandle;
    }
    DbSpinUnlock(&vfdMgr->freeListLock);
}

// remove node from free list, return remoevd node vfd
static int32_t VfdFreeListRemoveNode(VirtualFdMgrT *vfdMgr)
{
    DbSpinLock(&vfdMgr->freeListLock);
    if (vfdMgr->nextFree == DB_INVALID_FD) {
        DbSpinUnlock(&vfdMgr->freeListLock);
        return DB_INVALID_FD;
    }
    int32_t res = vfdMgr->nextFree;
    SeVfdNodeT *curNode = &vfdMgr->vfdCache[res];
    vfdMgr->nextFree = curNode->next;
    if (curNode->next != DB_INVALID_FD) {
        SeVfdNodeT *nextNode = &vfdMgr->vfdCache[curNode->next];
        nextNode->prev = DB_INVALID_FD;
    }
    curNode->next = DB_INVALID_FD;
    DbSpinUnlock(&vfdMgr->freeListLock);
    return res;
}

// free node或swapped node   插入到lru node
static void VfdLruInsertNewNode(VirtualFdMgrT *vfdMgr, int32_t vfdHandle)
{
    DbSpinLock(&vfdMgr->lruLock);
    SeVfdNodeT *curNode = VfdGetNodeFromCache(vfdMgr, vfdHandle);
    int32_t oldMoreIdx = vfdMgr->lruMoreRecently;
    vfdMgr->lruMoreRecently = vfdHandle;
    curNode->prev = DB_INVALID_FD;
    curNode->next = oldMoreIdx;
    if (oldMoreIdx != DB_INVALID_FD) {
        SeVfdNodeT *nextNode = &vfdMgr->vfdCache[oldMoreIdx];
        nextNode->prev = vfdHandle;
    } else {
        vfdMgr->lruLessRecently = vfdHandle;
    }
    DbSpinUnlock(&vfdMgr->lruLock);
}

// lru node移动到lru头
static StatusInter VfdLruMoveToHead(VirtualFdMgrT *vfdMgr, int32_t vfdHandle)
{
    DbSpinLock(&vfdMgr->lruLock);
    if (vfdMgr->lruMoreRecently == DB_INVALID_FD || vfdMgr->lruLessRecently == DB_INVALID_FD) {
        SE_ERROR(INTERNAL_ERROR_INTER, "Access lru list when move to head");
        DbSpinUnlock(&vfdMgr->lruLock);
        return INTERNAL_ERROR_INTER;
    }
    // 1. node 在lru头
    if (vfdHandle == vfdMgr->lruMoreRecently) {
        DbSpinUnlock(&vfdMgr->lruLock);
        return STATUS_OK_INTER;
    }
    // 2. node 在lru尾
    SeVfdNodeT *curNode = VfdGetNodeFromCache(vfdMgr, vfdHandle);
    SeVfdNodeT *prevNode = NULL;

    if (vfdMgr->lruLessRecently == vfdHandle) {
        if (curNode->prev == DB_INVALID_FD) {
            SE_ERROR(INTERNAL_ERROR_INTER, "Move single node to head");
            DbSpinUnlock(&vfdMgr->lruLock);
            return INTERNAL_ERROR_INTER;
        }
        prevNode = &vfdMgr->vfdCache[curNode->prev];
        prevNode->next = DB_INVALID_FD;
        curNode->next = vfdMgr->lruMoreRecently;
        vfdMgr->lruMoreRecently = vfdHandle;
        curNode->prev = DB_INVALID_FD;
        vfdMgr->lruLessRecently = prevNode->vfd;
        SeVfdNodeT *nextNode = &vfdMgr->vfdCache[curNode->next];
        nextNode->prev = vfdHandle;
        DbSpinUnlock(&vfdMgr->lruLock);
        return STATUS_OK_INTER;
    }

    // 中间
    if (curNode->prev == DB_INVALID_FD || curNode->next == DB_INVALID_FD) {
        SE_ERROR(INTERNAL_ERROR_INTER, "Wrong state lru list");
        DbSpinUnlock(&vfdMgr->lruLock);
        return INTERNAL_ERROR_INTER;
    }
    prevNode = &vfdMgr->vfdCache[curNode->prev];
    SeVfdNodeT *nextNode = &vfdMgr->vfdCache[curNode->next];
    prevNode->next = nextNode->vfd;
    nextNode->prev = prevNode->vfd;

    nextNode = &vfdMgr->vfdCache[vfdMgr->lruMoreRecently];
    curNode->next = vfdMgr->lruMoreRecently;
    nextNode->prev = vfdHandle;
    vfdMgr->lruMoreRecently = vfdHandle;
    curNode->prev = DB_INVALID_FD;
    DbSpinUnlock(&vfdMgr->lruLock);
    return STATUS_OK_INTER;
}

// lru删除节点,
static StatusInter VfdLruRemoveNode(VirtualFdMgrT *vfdMgr, int32_t vfdHandle)
{
    DbSpinLock(&vfdMgr->lruLock);
    if (vfdMgr->lruMoreRecently == DB_INVALID_FD) {
        SE_ERROR(INTERNAL_ERROR_INTER, "Remove node in empty lru list");
        DbSpinUnlock(&vfdMgr->lruLock);
        return INTERNAL_ERROR_INTER;
    }

    SeVfdNodeT *curNode = VfdGetNodeFromCache(vfdMgr, vfdHandle);
    if (curNode->prev == DB_INVALID_FD) {  // 头节点
        vfdMgr->lruMoreRecently = curNode->next;
        if (curNode->next == DB_INVALID_FD) {  // lru只有当前节点一个节点
            vfdMgr->lruLessRecently = DB_INVALID_FD;
        } else {
            SeVfdNodeT *nextNode = &vfdMgr->vfdCache[curNode->next];
            nextNode->prev = DB_INVALID_FD;
        }
    } else {
        SeVfdNodeT *preNode = &vfdMgr->vfdCache[curNode->prev];
        preNode->next = curNode->next;
        if (curNode->next == DB_INVALID_FD) {  // 移除尾节点,更新lruLessRecently
            vfdMgr->lruLessRecently = preNode->vfd;
        } else {
            SeVfdNodeT *nextNode = &vfdMgr->vfdCache[curNode->next];
            nextNode->prev = preNode->vfd;
        }
    }
    // 重置左右指针,防止操作出错
    curNode->prev = DB_INVALID_FD;
    curNode->next = DB_INVALID_FD;
    DbSpinUnlock(&vfdMgr->lruLock);
    return STATUS_OK_INTER;
}

// 获取lru中最早未使用节点, 用来换出
static int32_t VfdLruGetFreeNodeIdx(VirtualFdMgrT *vfdMgr)
{
    int32_t index = vfdMgr->lruLessRecently;
    while (index != DB_INVALID_FD) {
        SeVfdNodeT *curNode = &vfdMgr->vfdCache[index];
        if (curNode->nodeState == VFD_OPENING && curNode->isResident == false) {
            return index;
        }
        index = curNode->prev;
    }
    return DB_INVALID_FD;
}
/**************************** inner LRU operation &  inner free list operation: end ↑ ***************************/

// assuming node already inserted into lru, 打开vfd并设置状态为opening或resident状态
static StatusInter VfdFileDbOpenFile(
    VirtualFdMgrT *vfdMgr, const char *name, const int32_t *vfdHandle, int32_t mode, uint32_t perm)
{
    SeVfdNodeT *curNode = &vfdMgr->vfdCache[*vfdHandle];
    Status ret = DbOpenFile(name, mode, perm, &curNode->fd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Open file(%s), os ret no: %" PRId32 ".", name, DbAptGetErrno());
        return DbGetStatusInterErrno(ret);
    }

    (void)DbAtomicInc16(&vfdMgr->curOpeningFileNum);
    curNode->flag = (int32_t)((uint32_t)mode & (~O_EXCL));
    uint32_t nameLen = (uint32_t)strlen(name) + 1u;
    if (curNode->filename != NULL) {
        SE_ERROR(INTERNAL_ERROR_INTER, "Vfd file name not null");
        (void)DbAtomicDec16(&vfdMgr->curOpeningFileNum);
        DbCloseFile(curNode->fd);
        curNode->nodeState = VFD_CLOSED;
        DbDynMemCtxFree(vfdMgr->seDynMemCtx, curNode->filename);
        curNode->filename = NULL;
        return INTERNAL_ERROR_INTER;
    }
    curNode->filename = (char *)DbDynMemCtxAlloc(vfdMgr->seDynMemCtx, nameLen * sizeof(char));
    if (curNode->filename == NULL) {
        SE_ERROR(INTERNAL_ERROR_INTER, "Allocate filename when open");
        (void)DbAtomicDec16(&vfdMgr->curOpeningFileNum);
        DbCloseFile(curNode->fd);
        return INTERNAL_ERROR_INTER;
    }
    (void)memcpy_s(curNode->filename, nameLen, name, nameLen);
    curNode->nodeState = VFD_OPENING;
    curNode->isResident = false;
    return STATUS_OK_INTER;
}

static StatusInter VfdFileOpenInner(
    VirtualFdMgrT *vfdMgr, const char *name, int32_t *vfdHandle, int32_t mode, uint32_t perm)
{
    StatusInter ret = STATUS_OK_INTER;
    *vfdHandle = VfdFreeListRemoveNode(vfdMgr);
    if (*vfdHandle == DB_INVALID_FD) {
        SE_ERROR(INTERNAL_ERROR_INTER, "Alloc node for file when open");
        return INTERNAL_ERROR_INTER;
    }
    if (vfdMgr->curOpeningFileNum < vfdMgr->physicFdLimit) {
        VfdLruInsertNewNode(vfdMgr, *vfdHandle);
        ret = VfdFileDbOpenFile(vfdMgr, name, vfdHandle, mode, perm);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Open vfd file file");
            VfdLruRemoveNode(vfdMgr, *vfdHandle);
            VfdFreeListInsertNode(vfdMgr, *vfdHandle);
        }
        return ret;
    }
    int32_t swappedVfd = VfdLruGetFreeNodeIdx(vfdMgr);
    if (swappedVfd == DB_INVALID_FD) {
        SE_ERROR(INTERNAL_ERROR_INTER, "Get file for swapping when open");
        VfdFreeListInsertNode(vfdMgr, *vfdHandle);
        return INTERNAL_ERROR_INTER;
    }
    VfdLruRemoveNode(vfdMgr, swappedVfd);
    SeVfdNodeT *swappedNode = VfdGetNodeFromCache(vfdMgr, swappedVfd);
    DbCloseFile(swappedNode->fd);
    swappedNode->fd = DB_INVALID_FD;
    (void)DbAtomicDec16(&vfdMgr->curOpeningFileNum);
    swappedNode->nodeState = VFD_SWAPPED;
    VfdLruInsertNewNode(vfdMgr, *vfdHandle);
    ret = VfdFileDbOpenFile(vfdMgr, name, vfdHandle, mode, perm);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Open vfd file after swapping node");
        VfdLruRemoveNode(vfdMgr, *vfdHandle);
        VfdFreeListInsertNode(vfdMgr, *vfdHandle);
    }
    return ret;
}

StatusInter VfdFileOpen(VirtualFdMgrT *vfdMgr, const char *name, int32_t flags, int32_t *vfdHandle)
{
    if (!VfdMgrNeedOpen(vfdMgr)) {
        return SeFileOpen(name, flags, vfdHandle);
    }
    uint32_t mode = O_BINARY | O_RDWR | (uint32_t)flags;
    StatusInter ret = VfdFileOpenInner(vfdMgr, name, vfdHandle, (int32_t)mode, 0);
    SeFileAlarmUpdate(ret);
    return ret;
}

StatusInter VfdFileCreate(VirtualFdMgrT *vfdMgr, const char *name, int32_t flags, int32_t *vfdHandle)
{
    if (!VfdMgrNeedOpen(vfdMgr)) {
        return SeFileCreate(name, flags, vfdHandle);
    }
    uint32_t mode = O_BINARY | O_RDWR | O_EXCL | O_CREAT | (uint32_t)flags;
    uint32_t perm = ((mode & O_CREAT) != 0) ? (S_IRUSR | S_IWUSR) : 0;
    return VfdFileOpenInner(vfdMgr, name, vfdHandle, (int32_t)mode, perm);
}

StatusInter VfdFileClose(VirtualFdMgrT *vfdMgr, int32_t *vfdHandle)
{
    if (!VfdMgrNeedOpen(vfdMgr)) {
        SeFileClose(vfdHandle);
        return STATUS_OK_INTER;
    }
    if (*vfdHandle < 0 || *vfdHandle >= (int32_t)vfdMgr->vfdCacheSize) {
        SE_ERROR(INTERNAL_ERROR_INTER, "Wrong vfd file to close");
        return INTERNAL_ERROR_INTER;
    }
    SeVfdNodeT *curNode = &vfdMgr->vfdCache[*vfdHandle];
    if (curNode->nodeState == VFD_CLOSED) {
        return STATUS_OK_INTER;
    } else if (curNode->nodeState == VFD_SWAPPED) {
        curNode->nodeState = VFD_CLOSED;
        DbDynMemCtxFree(vfdMgr->seDynMemCtx, curNode->filename);
        curNode->filename = NULL;
        VfdFreeListInsertNode(vfdMgr, *vfdHandle);
        return STATUS_OK_INTER;
    } else if (curNode->nodeState == VFD_OPENING) {
        VfdLruRemoveNode(vfdMgr, *vfdHandle);
        Status ret = DbFsyncFile(curNode->fd);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Fsync file");
        }
        DbCloseFile(curNode->fd);
        curNode->fd = DB_INVALID_FD;
        curNode->nodeState = VFD_CLOSED;
        DbDynMemCtxFree(vfdMgr->seDynMemCtx, curNode->filename);
        curNode->filename = NULL;
        DbAtomicSet16(&curNode->useCount, 0);
        (void)DbAtomicDec16(&vfdMgr->curOpeningFileNum);
        VfdFreeListInsertNode(vfdMgr, *vfdHandle);
        return STATUS_OK_INTER;
    } else if (curNode->nodeState == VFD_INUSE) {
        SE_ERROR(INTERNAL_ERROR_INTER, "Close vfd file in use");
        return INTERNAL_ERROR_INTER;
    }
    SE_ERROR(INTERNAL_ERROR_INTER, "Close vfd file in wrong state");
    return INTERNAL_ERROR_INTER;
}

StatusInter VfdFileRemove(VirtualFdMgrT *vfdMgr, const char *name)
{
    return SeFileRemove(name);
}

static StatusInter VfdSwappedFileOpen(VirtualFdMgrT *vfdMgr, int32_t vfdHandle)
{
    SeVfdNodeT *curNode = VfdGetNodeFromCache(vfdMgr, vfdHandle);
    curNode->flag = (int32_t)((uint32_t)curNode->flag & (~O_EXCL));

    Status ret = DbOpenFile(curNode->filename, curNode->flag, 0, &curNode->fd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Reopen file(%s), os ret no: %" PRId32 ".", curNode->filename, DbAptGetErrno());
        return DbGetStatusInterErrno(ret);
    }
    (void)DbAtomicInc16(&vfdMgr->curOpeningFileNum);
    return STATUS_OK_INTER;
}

static StatusInter VfdSwapFileReopenForOperation(VirtualFdMgrT *vfdMgr, int32_t vfdHandle)
{
    SeVfdNodeT *curNode = VfdGetNodeFromCache(vfdMgr, vfdHandle);
    curNode->nodeState = VFD_INUSE;
    // 超出打开文件数量则换出
    while (vfdMgr->curOpeningFileNum >= vfdMgr->physicFdLimit) {
        int32_t swappedVfd = VfdLruGetFreeNodeIdx(vfdMgr);
        if (swappedVfd == DB_INVALID_FD) {
            SE_ERROR(INTERNAL_ERROR_INTER, "Swap other file when open");
            curNode->nodeState = VFD_SWAPPED;
            return INTERNAL_ERROR_INTER;
        }
        VfdLruRemoveNode(vfdMgr, swappedVfd);
        SeVfdNodeT *swappedNode = VfdGetNodeFromCache(vfdMgr, swappedVfd);
        DbCloseFile(swappedNode->fd);
        swappedNode->fd = DB_INVALID_FD;
        (void)DbAtomicDec16(&vfdMgr->curOpeningFileNum);
        swappedNode->nodeState = VFD_SWAPPED;
    }
    VfdLruInsertNewNode(vfdMgr, vfdHandle);
    // 重新打开目标文件
    StatusInter ret = VfdSwappedFileOpen(vfdMgr, vfdHandle);
    if (ret != STATUS_OK_INTER) {
        // 重新打开失败,从lru移除
        SE_ERROR(ret, "Open swapped file");
        curNode->nodeState = VFD_SWAPPED;
        VfdLruRemoveNode(vfdMgr, vfdHandle);
        return ret;
    }
    return ret;
}

// 将文件打开并设置为inuse状态
static StatusInter VfdOperateFileInner(VirtualFdMgrT *vfdMgr, int32_t vfdHandle, SeVfdStateE *oldState)
{
    StatusInter ret = STATUS_OK_INTER;
    SeVfdNodeT *curNode = VfdGetNodeFromCache(vfdMgr, vfdHandle);
    if (curNode->nodeState == VFD_OPENING || curNode->nodeState == VFD_INUSE) {
        *oldState = curNode->nodeState;
        curNode->nodeState = VFD_INUSE;
        VfdLruMoveToHead(vfdMgr, vfdHandle);
        (void)DbAtomicInc16(&curNode->useCount);
    } else if (curNode->nodeState == VFD_SWAPPED) {
        ret = VfdSwapFileReopenForOperation(vfdMgr, vfdHandle);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Open swapped file for operate");
        }
        *oldState = VFD_OPENING;
        (void)DbAtomicInc16(&curNode->useCount);
    } else {
        SE_ERROR(INTERNAL_ERROR_INTER, "File closed for operate");
        ret = INTERNAL_ERROR_INTER;
    }
    return ret;
}

StatusInter VfdFileRead(VirtualFdMgrT *vfdMgr, int32_t vfdHandle, int64_t offset, BufT bufData)
{
    if (!VfdMgrNeedOpen(vfdMgr)) {
        return SeFileRead(vfdHandle, offset, bufData);
    }
    SeVfdNodeT *curNode = VfdGetNodeFromCache(vfdMgr, vfdHandle);
    uint32_t readSize = 0;
    SeVfdStateE oldState = curNode->nodeState;
    StatusInter ret = VfdOperateFileInner(vfdMgr, vfdHandle, &oldState);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Open file when read");
        curNode->nodeState = oldState;
        return ret;
    }

    ret = DbGetStatusInterErrno(DbPreadFile(curNode->fd, (char *)bufData.buf, bufData.size, offset, &readSize));
    SeFileAlarmUpdate(ret);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Read file");
    }
    if (DbAtomicDec16(&curNode->useCount) == 0) {
        curNode->nodeState = VFD_OPENING;
    } else {
        curNode->nodeState = oldState;
    }
    return ret;
}

StatusInter VfdFileWrite(SeInstanceT *seIns, int32_t vfdHandle, int64_t offset, BufT bufData)
{
    // 锁库时的兜底拦截
    if (SeGetStorageStatus(seIns) == SE_ON_DISK_EMRGNCY) {
        SE_LAST_ERROR(DATABASE_NOT_AVAILABLE_INTER, "Write vfd file when disk emergency or swapping dir");
        return DATABASE_NOT_AVAILABLE_INTER;
    }
    if (!VfdMgrNeedOpen(seIns->vfdMgr)) {
        return SeFileWrite(seIns, vfdHandle, offset, bufData);
    }
    VirtualFdMgrT *vfdMgr = seIns->vfdMgr;
    SeVfdNodeT *curNode = VfdGetNodeFromCache(vfdMgr, vfdHandle);
    SeVfdStateE oldState = curNode->nodeState;
    StatusInter ret = VfdOperateFileInner(vfdMgr, vfdHandle, &oldState);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Open when write");
        curNode->nodeState = oldState;
        return ret;
    }
    uint64_t startTime = DbClockGetTsc();
    ret = DbGetStatusInterErrno(DbPwriteFile(curNode->fd, (const char *)bufData.buf, bufData.size, offset));
    SeFileAlarmUpdate(ret);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Write file in vfd");
    }
    uint64_t endTime = DbClockGetTsc();
    if (SECUREC_UNLIKELY(DbToMseconds(endTime - startTime) > SE_VFD_TIMEOUT_MS)) {
        DB_LOG_WARN(
            GMERR_REQUEST_TIME_OUT, "Write file too long. (time: %" PRIu64 ")", DbToMseconds(endTime - startTime));
    }
    if (DbAtomicDec16(&curNode->useCount) == 0) {
        curNode->nodeState = VFD_OPENING;
    } else {
        curNode->nodeState = oldState;
    }
    return ret;
}

static StatusInter VfdFileOpenCreateInner(VirtualFdMgrT *vfdMgr, const char *name, int32_t flags, int32_t *vfdHandle)
{
    StatusInter ret = STATUS_OK_INTER;
    if (DbFileExist(name)) {
        size_t fileSize = 0;
        ret = DbGetStatusInterErrno(DbFileSize(name, &fileSize));
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Vfd calculate file(%s), os ret no: %" PRId32 ".", name, DbAptGetErrno());
            return ret;
        }
        if (fileSize == 0) {
            uint32_t mode = O_BINARY | O_RDWR | (uint32_t)flags;
            return VfdFileOpenInner(vfdMgr, name, vfdHandle, (int32_t)mode, 0);
        }
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_FILE_OPERATE_FAILED, "Vdf open or create file(%s), file already exist and not empty", name);
        return FILE_OPERATE_FAILED_INTER;
    }
    uint32_t mode = O_BINARY | O_RDWR | O_EXCL | O_CREAT | (uint32_t)flags;
    uint32_t perm = ((mode & O_CREAT) != 0) ? (S_IRUSR | S_IWUSR) : 0;
    return VfdFileOpenInner(vfdMgr, name, vfdHandle, (int32_t)mode, perm);
}

StatusInter VfdFileOpenCreate(VirtualFdMgrT *vfdMgr, const char *name, int32_t flags, int32_t *vfdHandle)
{
    if (!VfdMgrNeedOpen(vfdMgr)) {
        return SeFileOpenCreate(name, flags, vfdHandle);
    }
    return VfdFileOpenCreateInner(vfdMgr, name, flags, vfdHandle);
}

StatusInter VfdFallocate(VirtualFdMgrT *vfdMgr, int32_t vfdHandle, int mode, off_t offset, off_t len)
{
    Status errCode = GMERR_OK;
    if (VfdMgrNeedOpen(vfdMgr)) {
        SeVfdNodeT *curNode = VfdGetNodeFromCache(vfdMgr, vfdHandle);
        SeVfdStateE oldState = curNode->nodeState;
        StatusInter ret = VfdOperateFileInner(vfdMgr, vfdHandle, &oldState);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Open file when fallocate");
            curNode->nodeState = oldState;
            return ret;
        }
        int32_t fd = curNode->fd;
        errCode = DbAdptFallocate(fd, mode, (uint32_t)len, (int64_t)offset);
        SeFileAlarmUpdate(errCode);
        if (DbAtomicDec16(&curNode->useCount) == 0) {
            curNode->nodeState = VFD_OPENING;
        } else {
            curNode->nodeState = oldState;
        }
        if (errCode != GMERR_OK) {
            SE_LAST_ERROR(DbGetStatusInterErrno(errCode), "Fallocate file when fallocate, os no (%" PRId32 ")", errno);
        }
        return DbGetStatusInterErrno(errCode);
    }

    int32_t fd = vfdHandle;
    errCode = DbAdptFallocate(fd, mode, (uint32_t)len, (int64_t)offset);
    SeFileAlarmUpdate(errCode);
    if (errCode != GMERR_OK) {
        SE_LAST_ERROR(DbGetStatusInterErrno(errCode), "Fallocate file, os no (%" PRId32 ")", errno);
    }
    return DbGetStatusInterErrno(errCode);
}

StatusInter VfdFtruncate(VirtualFdMgrT *vfdMgr, int32_t vfdHandle, int64_t len)
{
    if (VfdMgrNeedOpen(vfdMgr)) {
        SeVfdNodeT *curNode = VfdGetNodeFromCache(vfdMgr, vfdHandle);
        SeVfdStateE oldState = curNode->nodeState;
        StatusInter ret = VfdOperateFileInner(vfdMgr, vfdHandle, &oldState);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Open file when Ftruncate");
            curNode->nodeState = oldState;
            return ret;
        }
        int32_t fd = curNode->fd;
        ret = DbGetStatusInterErrno(DbAdptFtruncate(fd, len));
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Ftruncate file when Ftruncate");
        }
        if (DbAtomicDec16(&curNode->useCount) == 0) {
            curNode->nodeState = VFD_OPENING;
        } else {
            curNode->nodeState = oldState;
        }
        return ret;
    }
    int32_t fd = vfdHandle;
    StatusInter ret = DbGetStatusInterErrno(DbAdptFtruncate(fd, len));
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Ftruncate file when Ftruncate");
    }
    return ret;
}
