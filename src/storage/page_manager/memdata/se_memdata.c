/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: memery data page manager
 * Author: zhangfengyu
 * Create: 2022-06-22
 */

#include "se_memdata.h"
#include "se_log.h"
#include "se_page_mgr.h"
#include "se_spacemgr.h"
#include "se_rsm_tablespace_am.h"
#include "se_memdata_rsm.h"
#include "db_alarm.h"
#include "db_dyn_load.h"

#ifdef __cplusplus
extern "C" {
#endif

inline static StatusInter MdGetPageWrapper(MdMgrT *mgr, PageIdT addr, uint8_t **page, PageOptionE option, bool isWrite)
{
    return MdGetPage(mgr, addr, page, option, isWrite);
}

void MdGetPageAddrShiftBit(MdMgrT *mgr, uint32_t chunkCntPerDev)
{
    DB_POINTER(mgr);
    // 存储层初始化时配置校验保证chunkCntPerDev一定不为0
    DB_ASSERT(chunkCntPerDev != 0);
    uint32_t tmpBlkPerDev = chunkCntPerDev - 1;
    while (tmpBlkPerDev != 0) {
        tmpBlkPerDev = tmpBlkPerDev >> 1;
        mgr->rsmPageAddrShiftBit++;
    }
}

StatusInter MdInitMgr(SeInstanceT *seIns, MdMgrT *mgr)
{
    DB_POINTER2(seIns, mgr);

    mgr->base.type = SE_MEMDATA;
    mgr->base.allocPageFunc = (SeAllocPageFunc)MdAllocPage;
    mgr->base.freePageFunc = (SeFreePageFunc)MdFreePage;
    mgr->base.getPageFunc = (SeGetPageFunc)MdGetPageWrapper;
    mgr->base.getPageWithArgFunc = NULL;
    mgr->base.leavePageFunc = (SeLeavePageFunc)MdLeavePage;
    mgr->pageSize = seIns->seConfig.pageSize * DB_KIBI;
    mgr->rsmDeviceIdBase = seIns->seConfig.maxSeMem / seIns->seConfig.deviceSize + RSM_DEVICE_ID_BASE;
    mgr->enableReleaseDevice = seIns->seConfig.enableReleaseDevice;
    if (seIns->seConfig.isUseRsm) {
        uint32_t rsmSubBlockCntInRsmBlock;
        StatusInter ret = RsmBlockGetRsmSubBlockCntInRsmBlock(mgr->rsmBlockMgr, &rsmSubBlockCntInRsmBlock);
        DB_ASSERT(ret == STATUS_OK_INTER);
        MdGetPageAddrShiftBit(mgr, rsmSubBlockCntInRsmBlock);
        return STATUS_OK_INTER;
    } else {
        mgr->rsmPageAddrShiftBit = 0;
        return STATUS_OK_INTER;
    }
}

StatusInter MdCreatePageMgrWithRsm(SeInstanceT *seIns, MdMgrT *mgr)
{
    DB_POINTER2(seIns, mgr);
    mgr->rsmBlockMgr = DbShmPtrToAddr(seIns->rsmBlockMgrShm);
    if (mgr->rsmBlockMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get rsmBlockMgr(%" PRIu32 ", %" PRIu32 ")",
            seIns->rsmBlockMgrShm.segId, seIns->rsmBlockMgrShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    mgr->rsmSpaceMgr = DbShmPtrToAddr(seIns->rsmSpaceMgrShm);
    if (mgr->rsmSpaceMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get RsmSpaceMgrShm(%" PRIu32 ", %" PRIu32 ")",
            seIns->rsmSpaceMgrShm.segId, seIns->rsmSpaceMgrShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    return STATUS_OK_INTER;
}

StatusInter MdCheckTupleAddrMode(SeInstanceT *seIns, uint32_t pageAddrShiftBit)
{
    DB_POINTER(seIns);
    uint64_t maxDevId = (uint64_t)(seIns->seConfig.maxSeMem / seIns->seConfig.deviceSize) - 1;
    uint64_t maxPageId = (maxDevId << pageAddrShiftBit) + seIns->seConfig.deviceSize / seIns->seConfig.pageSize - 1;

    if (seIns->seConfig.heapTupleAddrMode == SE_HEAP_TUPLE_ADDR_32) {
        if (maxPageId > SE_MAX_PAGE_COUNT_32) {
            seIns->seConfig.heapTupleAddrMode = SE_HEAP_TUPLE_ADDR_48;
            DB_ASSERT(false);  // 不应该有这种情况
            DB_LOG_INFO("Config heapTupleAddrMode:%" PRIu32, (uint32_t)seIns->seConfig.heapTupleAddrMode);
        }
    }
    if (seIns->seConfig.heapTupleAddrMode == SE_HEAP_TUPLE_ADDR_48) {
        if (maxPageId > SE_MAX_PAGE_COUNT_48) {
            SE_LAST_ERROR(PROGRAM_LIMIT_EXCEEDED_HEAP_TUPLE_ADDR,
                "maxSeMem:%" PRIu32 ", deviceSize:%" PRIu32 ", pageSize:%" PRIu32, seIns->seConfig.maxSeMem,
                seIns->seConfig.deviceSize, seIns->seConfig.pageSize);
            DB_ASSERT(false);
            return PROGRAM_LIMIT_EXCEEDED_HEAP_TUPLE_ADDR;
        }
    }
    return STATUS_OK_INTER;
}

static StatusInter MdGetDevAndSpaceMgr(const SeInstanceT *seIns, MdMgrT *mgr)
{
    DB_POINTER2(seIns, mgr);
    mgr->devMgr = DbShmPtrToAddr(seIns->devMgrShm);
    if (mgr->devMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get devMgr(%" PRIu32 ", %" PRIu32 ")", seIns->devMgrShm.segId,
            seIns->devMgrShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    mgr->spaceMgr = DbShmPtrToAddr(seIns->spaceMgrShm);
    if (mgr->spaceMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get SpaceMgrShm(%" PRIu32 ", %" PRIu32 ")",
            seIns->spaceMgrShm.segId, seIns->spaceMgrShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    if (mgr->spaceMgr->chunkCntPerDev == 0) {
        SE_LAST_ERROR(CONFIG_ERROR_INTER, "ChunkCntPerDev = 0");
        return CONFIG_ERROR_INTER;
    }
    return STATUS_OK_INTER;
}

void SeInitDevBit(PageMgrT *pageMgr, uint32_t devSize, uint64_t maxSeShmSize)
{
    DB_POINTER(pageMgr);
    uint32_t devCnt = (uint32_t)(maxSeShmSize / devSize - 1);
    while (devCnt != 0) {
        devCnt = devCnt >> 1;
        pageMgr->devShiftBit++;
    }
}

StatusInter MdRsmCreatePageMgrInner(SeInstanceT *seIns, MdMgrT *mgr)
{
#ifndef FEATURE_SIMPLEREL
    if (seIns->seConfig.isUseRsm && DbCommonGetIsWarmReboot() && DbCommonIsServer()) {  // 仅server拉起时恢复
        MdRsmUndoRecovery(mgr);
        StatusInter ret = RsmSpaceMgrRecovery(seIns);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "RsmSpaceMgrRecovery");
            return ret;
        }
    }
#endif
    return STATUS_OK_INTER;
}

#ifdef FEATURE_SIMPLEREL
AddrCompressT g_AddrCompressMgr;
#endif
StatusInter MdCreatePageMgr(SeInstanceT *seIns, DbMemCtxT *memCtx, PageMgrT **pageMgr)
{
    DB_POINTER2(seIns, pageMgr);
    MdMgrT *mgr = (MdMgrT *)DbDynMemCtxAlloc(memCtx, sizeof(MdMgrT));
    if (mgr == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "size %" PRIu32, (uint32_t)sizeof(MdMgrT));
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_s((void *)mgr, sizeof(MdMgrT), 0x0, sizeof(MdMgrT));

    StatusInter ret = MdGetDevAndSpaceMgr(seIns, mgr);
    if (ret != STATUS_OK_INTER) {
        DbDynMemCtxFree(memCtx, mgr);
        return ret;
    }

    if (seIns->seConfig.isUseRsm) {
        ret = MdCreatePageMgrWithRsm(seIns, mgr);
        if (ret != STATUS_OK_INTER) {
            DbDynMemCtxFree(memCtx, mgr);
            return ret;
        }
    } else {
        mgr->rsmBlockMgr = NULL;
        mgr->rsmSpaceMgr = NULL;
    }

    ret = MdInitMgr(seIns, mgr);
    if (ret != STATUS_OK_INTER) {
        DbDynMemCtxFree(memCtx, mgr);
        SE_LAST_ERROR(ret, "md init");
        return ret;
    }

    SeInitPageAddrInfo(&mgr->base, mgr->pageSize, mgr->spaceMgr->chunkCntPerDev);
    DevSetPageSizeShiftBit(mgr->devMgr, mgr->base.pageSizeShiftBit);

    SeInitDevBit(&mgr->base, seIns->seConfig.deviceSize, seIns->seConfig.maxSeMem);
    ret = MdCheckTupleAddrMode(seIns, mgr->base.pageAddrShiftBit);
    if (ret != STATUS_OK_INTER) {
        DbDynMemCtxFree(memCtx, mgr);
        return ret;
    }
    ret = MdRsmCreatePageMgrInner(seIns, mgr);
    if (ret != STATUS_OK_INTER) {
        DbDynMemCtxFree(memCtx, mgr);
        return ret;
    }
    *pageMgr = (PageMgrT *)mgr;
#ifdef FEATURE_SIMPLEREL
    g_AddrCompressMgr.devShiftBit = (*pageMgr)->devShiftBit;
    g_AddrCompressMgr.pageAddrShiftBit = (*pageMgr)->pageAddrShiftBit;
    g_AddrCompressMgr.pageSizeShiftBit = (*pageMgr)->pageSizeShiftBit;
#endif
    return STATUS_OK_INTER;
}

void MdDestroyPageMgr(SeInstanceT *seIns, PageMgrT *pageMgr)
{
    MdMgrT *mgr = (MdMgrT *)(void *)pageMgr;
    DbDynMemCtxFree(seIns->seServerMemCtx, mgr);
}

void MdGetSpaceId(uint32_t spaceId, uint32_t *realSpaceId, bool *isRsmSpace)
{
    if (spaceId >= DB_RSM_TABLE_SPACE_BASE_INDEX) {
        *realSpaceId = spaceId - DB_RSM_TABLE_SPACE_BASE_INDEX;
        *isRsmSpace = true;
    } else {
        *realSpaceId = spaceId;
        *isRsmSpace = false;
    }
}

StatusInter MdAllocPage(MdMgrT *mgr, AllocPageParamT *allocPageParam, PageIdT *addr)
{
    DB_POINTER2(mgr, addr);
    bool isNew;
    uint8_t *page;

    DB_ASSERT(mgr->base.type == SE_MEMDATA);
    uint32_t realSpaceId;
    bool isRsmSpace;
    MdGetSpaceId(allocPageParam->spaceId, &realSpaceId, &isRsmSpace);
    if (SECUREC_LIKELY(!isRsmSpace)) {
        StatusInter ret = MdAllocChunk(mgr->spaceMgr, realSpaceId, addr, &page, &isNew, allocPageParam->dbInstance);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "DevAllocChunk");
            DbAlarmUpdateFailCnt(DB_ALARM_TABLE_SPACE_USED_INFO, 1);
            return ret;
        }
        // 9号alarm（实际对应orm的5号alarm，orm做了映射），统计成功/失败次数原先为device扩展成功/失败的统计
        // 但当device扩展达到上限，每次申请一个页都会使得失败次数加1，而成功次数在扩展完成后就基本不会变了
        // 这样导致在设备上失败次数的占比很容易就超过50%，容易引起静默故障（静默故障规则：10min内失败占比50%，超过1000次）
        DbAlarmUpdateSuccCnt(DB_ALARM_TABLE_SPACE_USED_INFO, 1);
        SeInitPageHead(page, allocPageParam->trmId, mgr->pageSize, *addr, isNew);
        return STATUS_OK_INTER;
    } else {
        if (((MdMgrT *)mgr)->rsmBlockMgr == NULL) {
            // 传入了一个非法spaceId，可能rsmBlock都没有初始化
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "alloc page get spaceId:%" PRIu32 ", trmId:%" PRIu32,
                allocPageParam->spaceId, allocPageParam->trmId);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        PageIdT realAddr;
        MdRsmAllocPageInfoT allocPageInfo = {.spaceId = realSpaceId,
            .labelId = allocPageParam->labelId,
            .addr = &realAddr,
            .page = &page,
            .isNewChunk = &isNew};
        StatusInter ret = MdRsmAllocChunk(mgr->rsmSpaceMgr, &allocPageInfo);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "rsmAllocChunk");
            DbAlarmUpdateFailCnt(DB_ALARM_RSM_TABLE_SPACE_USED_INFO, 1);
            return ret;
        }
        // 此处复用了对外格式，对于rsm来说，addr.deviceId实际指当前页属于哪一块保留内存
        // addr.blockId实际指在这块保留内存的数组下标
        *addr = (PageIdT){.deviceId = realAddr.deviceId + mgr->rsmDeviceIdBase, .blockId = realAddr.blockId};
        MdRsmInitChunkInfoT initChunkInfo = {.trmId = allocPageParam->trmId,
            .pageSize = mgr->pageSize,
            .pageAddr = *addr,
            .isNewChunk = isNew,
            .realSpaceId = realSpaceId,
            .realAddr = realAddr,
            .rsmUndoRecord = allocPageParam->labelRsmUndo};
        MdRsmInitChunk(mgr->rsmSpaceMgr, page, &initChunkInfo);
        DbAlarmUpdateSuccCnt(DB_ALARM_RSM_TABLE_SPACE_USED_INFO, 1);
        return STATUS_OK_INTER;
    }
}

StatusInter MdFreePage(MdMgrT *mgr, FreePageParamT *freePageParam)
{
    DB_POINTER(mgr);
    bool isRsmSpace;
    uint32_t realSpaceId;
    MdGetSpaceId(freePageParam->spaceId, &realSpaceId, &isRsmSpace);
    StatusInter ret = STATUS_OK_INTER;
    if (SECUREC_LIKELY(!isRsmSpace)) {
        ret = DevCheckAddr(mgr->devMgr, freePageParam->addr);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }

        uint8_t *page = DevGetEntry(mgr->devMgr, freePageParam->addr.deviceId);
        if (SECUREC_UNLIKELY(page == NULL)) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "free page get dev:%" PRIu32 ", devMax %" PRIu32,
                freePageParam->addr.deviceId, mgr->devMgr->devMaxCount);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        page = (uint8_t *)(uintptr_t)(page + ((uint64_t)freePageParam->addr.blockId << mgr->base.pageSizeShiftBit));
        SeResetPageHead(page);
        bool needRelease = mgr->enableReleaseDevice ? true : (freePageParam->spaceId == DB_UNDO_TABLE_SPACE_INDEX);
        MdFreeChunk(mgr->spaceMgr, realSpaceId, freePageParam, page, needRelease);
    } else {
        if (((MdMgrT *)mgr)->rsmBlockMgr == NULL) {
            // 传入了一个非法spaceId，可能rsmBlock都没有初始化
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
                "free page get spaceId:%" PRIu32 ", devId:%" PRIu32 ", blockId:%" PRIu32, freePageParam->spaceId,
                freePageParam->addr.deviceId, freePageParam->addr.blockId);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        freePageParam->addr.deviceId = freePageParam->addr.deviceId - mgr->rsmDeviceIdBase;
        ret = RsmBlockCheckAddr(mgr->rsmBlockMgr, freePageParam->addr);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        uint8_t *page;
        ret = RsmBlockGetEntry(mgr->rsmBlockMgr, freePageParam->addr, &page);
        if (ret != STATUS_OK_INTER) {
            SE_LAST_ERROR(ret, "free page get page(%" PRIu32 ", %" PRIu32 ")", freePageParam->addr.deviceId,
                freePageParam->addr.blockId);
            return ret;
        }
        MdRsmResetChunk(mgr->rsmSpaceMgr, freePageParam->labelRsmUndo, page, realSpaceId);

        MdRsmFreeChunk(mgr->rsmSpaceMgr, realSpaceId, freePageParam->addr);
    }
    return ret;
}

static bool MdFindCachedListByLabelId(
    DbShmArrayT *shmArray, uint32_t labelId, LabelCachePageListT **cachePageList, uint32_t *listId)
{
    DbShmArrayIteratorT iter;
    LabelCachePageListT *list = NULL;
    for (DbShmArrayInitIterator(&iter, shmArray); DbShmArrayIteratorValid(&iter); DbShmArrayIteratorNext(&iter)) {
        list = (LabelCachePageListT *)DbShmArrayIteratorGetItem(&iter);
        if (list->labelId != labelId) {
            continue;
        }
        break;
    }

    if (list == NULL || list->labelId != labelId) {
        return false;
    }

    *cachePageList = list;
    *listId = DbShmArrayIteratorGetItemId(&iter);
    DB_ASSERT(list->labelId == labelId);
    return true;
}

static StatusInter MdFreeCachedPage(
    MdMgrT *mgr, FreeCachedPageParamT *para, const LabelCachePageListT *cachePageList, PageIdT *nextPageId)
{
    DB_POINTER2(mgr, para);

    PageIdT pageId = cachePageList->head;
    StatusInter ret = DevCheckAddr(mgr->devMgr, pageId);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    uint8_t *page = DevGetEntry(mgr->devMgr, pageId.deviceId);
    if (SECUREC_UNLIKELY(page == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "free page get dev:%" PRIu32 ", devMax %" PRIu32, pageId.deviceId,
            mgr->devMgr->devMaxCount);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    page = (uint8_t *)(uintptr_t)(page + ((uint64_t)pageId.blockId << mgr->base.pageSizeShiftBit));
    *nextPageId = ((PageHeadT *)page)->nextPageId;  // 保留新的链表头节点
    ((PageHeadT *)page)->nextPageId = SE_INVALID_PAGE_ADDR;
    SeResetPageHead(page);

    MdSpaceT *space = &mgr->spaceMgr->spaceArray[para->spaceId];
    bool needRelease = mgr->enableReleaseDevice ? true : (para->spaceId == DB_UNDO_TABLE_SPACE_INDEX);
    FreePageParamT freePageParam =
        SeInitFreePageParam(para->spaceId, pageId, para->dbInstance, para->labelRsmUndo, SE_INVALID_LABEL_ID, false);
    // 归还缓存链表上的页一定是直接归还给dev
    MdFreeChunkToDev(mgr->spaceMgr, space, &freePageParam, needRelease);

    return STATUS_OK_INTER;
}

#define SE_FREE_PAGE_TIMEOUT_MS 10
StatusInter MdClearCachedPageList(MdMgrT *mgr, FreeCachedPageParamT *para, bool *isOverTime, bool deleteList)
{
    DB_POINTER2(mgr, para);

    uint64_t startTime = DbRdtsc();
    DB_ASSERT(para->spaceId < DB_TABLE_SPACE_MAX_NUM);
    MdSpaceT *space = &mgr->spaceMgr->spaceArray[para->spaceId];
    DbShmArrayT *shmArray = DbShmPtrToAddr(space->cachedListsShm);
    if (shmArray == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get md space list shm, (segid:%" PRIu32 " offset:%" PRIu32 ")",
            space->cachedListsShm.segId, space->cachedListsShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    SpaceMgrLock(mgr->spaceMgr);
    LabelCachePageListT *cachePageList = NULL;
    uint32_t listId;
    if (!MdFindCachedListByLabelId(shmArray, para->labelId, &cachePageList, &listId)) {
        SpaceMgrUnlock(mgr->spaceMgr);
        return STATUS_OK_INTER;  // 没有找到labelId对应的空闲链表，返回成功
    }

    if (cachePageList->listCnt == 0) {
        if (deleteList) {
            (void)DbShmArrayRemoveItem(shmArray, listId);
        }
        SpaceMgrUnlock(mgr->spaceMgr);
        return STATUS_OK_INTER;
    }

    PageIdT nextPageId;
    while (DbIsPageIdValid(cachePageList->head)) {
        StatusInter ret = MdFreeCachedPage(mgr, para, cachePageList, &nextPageId);
        if (ret != STATUS_OK_INTER) {
            SpaceMgrUnlock(mgr->spaceMgr);
            return ret;
        }

        MdSpaceCacheListDel(cachePageList, nextPageId);
        if (cachePageList->listCnt == 0) {
            DB_ASSERT(!DbIsPageIdValid(nextPageId));  // listCnt为0，释放最后一个节点，则nextPageId必定为INVALID_PAGE_ID
            if (deleteList) {
                (void)DbShmArrayRemoveItem(shmArray, listId);
            }
            goto EXIT;
        }

        // 超时判断
        if (isOverTime && (DbToMseconds(DbRdtsc() - startTime) > SE_FREE_PAGE_TIMEOUT_MS)) {
            *isOverTime = true;
            goto EXIT;
        }
    }

EXIT:
    SpaceMgrUnlock(mgr->spaceMgr);
    return STATUS_OK_INTER;
}

SO_EXPORT StatusInter MdCreateCachedPageList(MdMgrT *mgr, uint32_t spaceId, uint32_t labelId)
{
    DB_POINTER(mgr);

    // 缓存页链表以label为粒度进行管理,因此labelId必须有效
    DB_ASSERT(labelId != SE_INVALID_LABEL_ID);
    DB_ASSERT(spaceId < DB_TABLE_SPACE_MAX_NUM);
    MdSpaceT *space = &mgr->spaceMgr->spaceArray[spaceId];
    DbShmArrayT *shmArray = DbShmPtrToAddr(space->cachedListsShm);
    if (shmArray == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get md space list shm, (segid:%" PRIu32 " offset:%" PRIu32 ")",
            space->cachedListsShm.segId, space->cachedListsShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    SpaceMgrLock(mgr->spaceMgr);
    DbShmArrayIteratorT iter;
    LabelCachePageListT *cachePageList;
    for (DbShmArrayInitIterator(&iter, shmArray); DbShmArrayIteratorValid(&iter); DbShmArrayIteratorNext(&iter)) {
        cachePageList = (LabelCachePageListT *)DbShmArrayIteratorGetItem(&iter);
        if (cachePageList->labelId == labelId) {
            SE_LAST_ERROR(DUPLICATE_OBJECT_INTER, "duplicate cached page labelId:%" PRIu32, labelId);
            SpaceMgrUnlock(mgr->spaceMgr);
            return DUPLICATE_OBJECT_INTER;
        }
    }

    // 新建链表
    uint32_t itemId;
    DbArrayAddrT arrAddr;
    Status status = DbShmArrayGetItem(shmArray, &itemId, (void **)&cachePageList, &arrAddr);
    if (status != GMERR_OK) {
        DB_LOG_ERROR(status,
            "create new cache page list, spaceId:%" PRIu32 ", itemId:%" PRIu32 ", labelId:%" PRIu32 ")", spaceId,
            itemId, labelId);
        SpaceMgrUnlock(mgr->spaceMgr);
        return DbGetStatusInterErrno(status);
    }

    cachePageList->labelId = labelId;
    cachePageList->head = SE_INVALID_PAGE_ADDR;
    cachePageList->listCnt = 0;
    SpaceMgrUnlock(mgr->spaceMgr);
    return STATUS_OK_INTER;
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
__attribute__((noinline)) StatusInter MdGetRsmPage(MdMgrT *mgr, PageIdT addr, uint8_t **page)
{
    DB_POINTER2(mgr, page);
    if (mgr->rsmBlockMgr == NULL) {
        // 传入了一个非法deviceId，可能rsmBlock都没有初始化
        SE_LAST_ERROR(
            UNEXPECTED_NULL_VALUE_INTER, "get page get dev:%" PRIu32 ", blockId:%" PRIu32, addr.deviceId, addr.blockId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    PageIdT realAddr = addr;
    realAddr.deviceId = realAddr.deviceId - mgr->rsmDeviceIdBase;
    StatusInter ret = RsmBlockGetEntry(mgr->rsmBlockMgr, realAddr, page);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "free page get page(%" PRIu32 ", %" PRIu32 ")", realAddr.deviceId, realAddr.blockId);
        return ret;
    }
    SeCheckPageHead(*page, addr);
    if (DbCommonGetWarmReboot()) {
        PageHeadT *pageHead = (PageHeadT *)(void *)*page;
        pageHead->isRsmUsing = 1;
    }
    return STATUS_OK_INTER;
}
#endif /* FEATURE_SIMPLEREL */

void MdLeavePage(const MdMgrT *pageMgr, PageIdT pageAddr, bool isChanged)
{
    DB_UNUSED(pageMgr);
    DB_UNUSED(pageAddr);
    DB_UNUSED(isChanged);
}

StatusInter MdInit(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx)
{
    DB_POINTER2(seIns, seTopShmMemCtx);

    StatusInter ret = DeviceCreate(seIns, seTopShmMemCtx);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "DevCreate.");
        return ret;
    }

    ret = MdSpaceMgrInit(seIns, seTopShmMemCtx);
    if (ret != STATUS_OK_INTER) {
        DeviceDestroy(seIns, seTopShmMemCtx);
        SE_ERROR(ret, "ChunkMgrInit");
        return ret;
    }
#ifndef FEATURE_SIMPLEREL
    if (seIns->seConfig.isUseRsm) {  // 异常场景依赖topRsmemCtx兜底回收
        if (DbCommonGetIsWarmReboot()) {
            ret = RsmBlockInit(seIns, seTopShmMemCtx, true);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "RsmBlockRecoveryCreate");
                goto EXIT;
            }
            // rsmSpaceMgr先创建一个新的，再根据遍历rsmBlockMgr的结果恢复(RsmSpaceMgrRecovery)
            ret = MdRsmSpaceMgrInit(seIns, seTopShmMemCtx);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "RsmSpaceMgrInit");
                goto EXIT;
            }
        } else {
            ret = RsmBlockInit(seIns, seTopShmMemCtx, false);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "RsmCreate");
                goto EXIT;
            }
            ret = MdRsmSpaceMgrInit(seIns, seTopShmMemCtx);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "RsmSpaceMgrInit");
                goto EXIT;
            }
        }
    }
EXIT:
#endif /* FEATURE_SIMPLEREL */
    if (ret != STATUS_OK_INTER) {
        DeviceDestroy(seIns, seTopShmMemCtx);
        MdSpaceMgrDestroy(seIns, seTopShmMemCtx);
        return ret;
    }
    return STATUS_OK_INTER;
}

#ifdef __cplusplus
}
#endif
