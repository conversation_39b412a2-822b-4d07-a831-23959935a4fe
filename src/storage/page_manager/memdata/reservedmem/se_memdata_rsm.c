/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: memery data page manager for rsm
 * Author:
 * Create: 2024-05-11
 */

#include "se_memdata_rsm.h"
#include "se_rsm_tablespace_am.h"
#include "se_rsm_block_am.h"

void BlockUndoAllocDev(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    MdMgrT *mdMgr = (MdMgrT *)ctx->mdMgr;
    const RsmUndoBlkMgrAllocDevRec *record = &opRecord->rsmBlkAllocDevRec;
    PageIdT addr = {.deviceId = record->rsmDevId, .blockId = record->rsmBlkId};
    RsmBlockUndoAllocDev(mdMgr->rsmBlockMgr, addr, record->curDeviceCnt, record->firstFreeRsmSubBlockId,
        record->newFirstFreeRsmSubBlockId);
}

void BlockUndoFreeDev(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    MdMgrT *mdMgr = (MdMgrT *)ctx->mdMgr;
    const RsmUndoBlkMgrFreeDevRec *record = &opRecord->rsmBlkFreeDevRec;
    RsmBlockUndoFreeDev(mdMgr->rsmBlockMgr, record);
}

void BlockUndoReserveDev(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    MdMgrT *mdMgr = (MdMgrT *)ctx->mdMgr;
    const RsmUndoBlkMgrReserveDevRec *record = &opRecord->rsmBlkReserverDevRec;
    RsmBlockUndoReserveDev(mdMgr->rsmBlockMgr, record->freeDevCount);
}

void BlockUndoReuseDev(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    MdMgrT *mdMgr = (MdMgrT *)ctx->mdMgr;
    const RsmUndoBlkMgrReuseDevRec *record = &opRecord->rsmBlkReuseDevRec;
    PageIdT addr = {.deviceId = record->rsmDevId, .blockId = record->rsmBlkId};
    RsmBlockUndoReuseDev(mdMgr->rsmBlockMgr, addr, record->unBindDeviceCnt);
}

void BlockUndoFreePage(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    MdMgrT *mdMgr = (MdMgrT *)ctx->mdMgr;
    const RsmUndoBlkMgrFreeEntryRec *record = &opRecord->rsmBlkFreeEntryRec;
    // (deviceId, blockId) -> (rsmBlockId, subBlockId)
    PageIdT addr = {.deviceId = record->rsmBlkId, .blockId = record->curRsmSubBlockId};
    RsmBlockUndoFreeEntry(mdMgr->rsmBlockMgr, addr, record->rsmDevId, record->freeRsmSubBlkCnt);
}

void BlockUndoMemcpyPage(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    MdMgrT *mdMgr = (MdMgrT *)ctx->mdMgr;
    RsmBlockUndoMemcpyEntry(mdMgr->rsmBlockMgr, &opRecord->rsmBlkMemcpyEntryRec);
}

void BlockUndoAllocPage(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    MdMgrT *mdMgr = (MdMgrT *)ctx->mdMgr;
    const RsmUndoBlkMgrAllocEntryRec *record = &opRecord->rsmBlkAllocEntryRec;
    // (devId, blockId) -> (rsmBlockId, subBlockId)
    PageIdT addr = {.deviceId = record->rsmBlkId, .blockId = record->curRsmSubBlockId};
    RsmBlockUndoAllocEntry(mdMgr->rsmBlockMgr, addr, record->isNewPage, record->rsmDevId, record->freeRsmSubBlkCnt);
}

void BlockUndoAllocBlock(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    MdMgrT *mdMgr = (MdMgrT *)ctx->mdMgr;
    const RsmUndoBlkMgrAllocBlkRec *record = &opRecord->rsmBlkAllocBlkRec;
    RsmBlockUndoAllocBlock(mdMgr->rsmBlockMgr, record->rsmBlkId, record->freeRsmBlockCnt);
}

void BlockUndoMigration(RsmUndoCtxT *ctx, const RsmUndoOpRecordT *opRecord)
{
    MdMgrT *mdMgr = (MdMgrT *)ctx->mdMgr;
    const RsmUndoBlkMgrMigrationRec *record = &opRecord->rsmUndoBlockMigrationRec;
    RsmBlockUndoMigration(mdMgr->rsmBlockMgr, record->path);
}

#define MD_UNDO_HANDLE_NUM (RSM_UNDO_MD_END - RSM_UNDO_MD_BEGIN)
void SetMdRsmUndoHandle(void)
{
    static bool isSet = false;
    if (isSet) {
        return;
    }
    isSet = true;

    const RsmUndoOpRecordHandleConfT conf[] = {
        {RSM_UNDO_BLOCK_ALLOC_BLOCK, BlockUndoAllocBlock},
        {RSM_UNDO_BLOCK_ALLOC_PAGE, BlockUndoAllocPage},
        {RSM_UNDO_BLOCK_FREE_PAGE, BlockUndoFreePage},
        {RSM_UNDO_BLOCK_MEMCPY_PAGE, BlockUndoMemcpyPage},
        {RSM_UNDO_BLOCK_REUSE_DEV, BlockUndoReuseDev},
        {RSM_UNDO_BLOCK_ALLOC_DEV, BlockUndoAllocDev},
        {RSM_UNDO_BLOCK_FREE_DEV, BlockUndoFreeDev},
        {RSM_UNDO_BLOCK_RESERVE_DEV, BlockUndoReserveDev},
        {RSM_UNDO_BLOCK_MIGRATION, BlockUndoMigration},
    };

    static_assert(ELEMENT_COUNT(conf) == MD_UNDO_HANDLE_NUM, "rsm undo conf num is wrong");

    DbRsmUndoSetOpRecordHandle(conf, MD_UNDO_HANDLE_NUM);
}

void MdRsmUndoRecovery(MdMgrT *mdMgr)
{
    SetMdRsmUndoHandle();

    RsmUndoCtxT ctx = {.mdMgr = mdMgr};
    MdRsmSetIsRecovery(mdMgr->rsmSpaceMgr, true);
    // rsmSpace和rsmBlock共用一个rsmUndoRec
    DbRsmUndoRollback(&ctx, RsmBlockGetRsmUndoRec(mdMgr->rsmBlockMgr), RSM_UNDO_ROLLBACK_ALL);
    MdRsmSetIsRecovery(mdMgr->rsmSpaceMgr, false);
}
