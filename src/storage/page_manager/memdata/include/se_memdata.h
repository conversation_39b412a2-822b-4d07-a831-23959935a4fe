/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: memery data page manager
 * Author: zhangfengyu
 * Create: 2022-06-22
 */

#ifndef SE_MEMDATA_H
#define SE_MEMDATA_H

#include "adpt_types.h"
#include "se_define.h"
#include "se_log.h"
#include "se_page.h"
#include "se_device.h"
#include "se_rsm_block_am.h"

#ifdef __cplusplus
extern "C" {
#endif

#define RSM_DEVICE_ID_BASE 1  // 区分一下rsm与普通内存的deviceId

typedef struct TagSpaceMgr SpaceMgrT;
typedef struct TagRsmSpaceMgr RsmSpaceMgrT;
typedef struct TagMdMgrT {
    // devMgr用得比较多，放前边，减少cache miss
    PageMgrT base;
    uint32_t pageSize;
    uint32_t rsmDeviceIdBase;
    DeviceMgrT *devMgr;
    SpaceMgrT *spaceMgr;
    RsmBlkMgrHdlT rsmBlockMgr;
    RsmSpaceMgrT *rsmSpaceMgr;
    uint16_t rsmPageAddrShiftBit;
    bool enableReleaseDevice;
    uint8_t reserve[5];
} MdMgrT;

inline static uint32_t MdSerializePageId(const MdMgrT *mgr, const PageIdT pageId, bool isUseRsm)
{
    DB_POINTER(mgr);
    if (!isUseRsm) {
        return SerializePageId((const PageMgrT *)mgr, pageId);
    } else {
        DB_ASSERT(pageId.deviceId >= mgr->rsmDeviceIdBase);
        return (uint32_t)((pageId.deviceId << mgr->rsmPageAddrShiftBit) | pageId.blockId);
    }
}

inline static PageIdT MdDeserializePageId(const MdMgrT *mgr, const uint32_t id, bool isUseRsm)
{
    DB_POINTER(mgr);
    if (!isUseRsm) {
        return DeserializePageId((const PageMgrT *)(const void *)mgr, id);
    } else {
        uint32_t bitMask = ~(DB_MAX_UINT32 << mgr->rsmPageAddrShiftBit);
        return (PageIdT){.deviceId = id >> mgr->rsmPageAddrShiftBit, .blockId = id & bitMask};
    }
}

SO_EXPORT StatusInter MdInit(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx);
SO_EXPORT StatusInter MdCreatePageMgr(SeInstanceT *seIns, DbMemCtxT *memCtx, PageMgrT **pageMgr);
// MdAllocPage中的labelId，当前只针对heap、聚簇、写缓存这3个有效，其他情况填无效值即可
SO_EXPORT StatusInter MdAllocPage(MdMgrT *mgr, AllocPageParamT *allocPageParam, PageIdT *addr);
SO_EXPORT StatusInter MdFreePage(MdMgrT *mgr, FreePageParamT *freePageParam);
SO_EXPORT StatusInter MdClearCachedPageList(MdMgrT *mgr, FreeCachedPageParamT *para, bool *isOverTime, bool deleteList);
SO_EXPORT StatusInter MdCreateCachedPageList(MdMgrT *mgr, uint32_t spaceId, uint32_t labelId);
SO_EXPORT StatusInter MdAllocExtent(MdMgrT *mgr, uint32_t spaceId, uint32_t trmId, PageIdT *extentId);
SO_EXPORT StatusInter MdFreeExtent(MdMgrT *mgr, uint32_t spaceId, PageIdT *extentId);
SO_EXPORT StatusInter MdGetRsmPage(MdMgrT *mgr, PageIdT addr, uint8_t **page);
ALWAYS_INLINE static StatusInter MdGetPage(MdMgrT *mgr, PageIdT addr, uint8_t **page, PageOptionE option, bool isWrite)
{
    DB_POINTER2(mgr, page);
    DB_UNUSED(option);
    DB_UNUSED(isWrite);
    if (SECUREC_LIKELY(addr.deviceId < mgr->rsmDeviceIdBase)) {
        uint8_t *device = DevGetEntry(mgr->devMgr, addr.deviceId);
        if (SECUREC_UNLIKELY(device == NULL)) {
            SE_LAST_ERROR(NO_DATA_SE_DEVICE_NOT_EXIST, "Get dev:%" PRIu32 " in get page, devMax %" PRIu32,
                addr.deviceId, mgr->devMgr->devMaxCount);
            return NO_DATA_SE_DEVICE_NOT_EXIST;
        }

        *page = (uint8_t *)(uintptr_t)(device + ((uint64_t)addr.blockId << mgr->base.pageSizeShiftBit));

        return STATUS_OK_INTER;
    } else {
        DB_ASSERT(mgr->rsmBlockMgr != NULL);
        return MdGetRsmPage(mgr, addr, page);
    }
}
SO_EXPORT void MdLeavePage(const MdMgrT *pageMgr, PageIdT pageAddr, bool isChanged);
SO_EXPORT void MdDestroyPageMgr(SeInstanceT *seIns, PageMgrT *pageMgr);

SO_EXPORT void MdSpaceMgrGetLowestFreeRateDevId(SpaceMgrT *spaceMgr, uint32_t spaceId, uint32_t *devId);

#ifdef __cplusplus
}
#endif

#endif
