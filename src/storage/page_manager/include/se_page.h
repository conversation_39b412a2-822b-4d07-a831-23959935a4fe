/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: page common
 * Author: zhangfengyu
 * Create: 2022-06-22
 */

#ifndef SE_PAGE_COMMON_H
#define SE_PAGE_COMMON_H

#include "se_capacity_def_inner.h"
#include "se_log.h"
#include "db_internal_error.h"
#include "db_rwlatch.h"
#include "dm_meta_basic_in.h"
#include "se_persistcap_page.h"
#include "se_persist.h"

#ifdef __cplusplus
extern "C" {
#endif

#define PAGE_INVALID_DEV_ID 0xFFFFFFFF
#define PAGE_INVALID_BLK_ID 0xFFFFFFFF
#define PAGE_INVALID_TRM_ID 0xFFFFFFFF
#define PAGE_UNINIT 0x0
#define PAGE_USING 0x1

#define DB_RECYCLE_TABLE_ADD 1
#define DB_RECYCLE_TABLE_DROP 2
#define DB_RECYCLE_PRIORITY 3
#define DB_ENCRYPT_MAC_LENGTH 16
#define DB_ENCRYPT_IV_LENGTH 12
#define DB_ENCRYPT_RESERVED_LENGTH 4
#define DB_PAGE_RESERVED_LENGTH (DB_ENCRYPT_MAC_LENGTH + DB_ENCRYPT_IV_LENGTH + DB_ENCRYPT_RESERVED_LENGTH)

#define PAGE_HEAD_MAGIC_NUM 0xabcd

#define RSM_INVALID_LABEL_ID 0
#define SE_INVALID_LABEL_ID DB_INVALID_LABEL_ID

typedef enum StorageType {
    SE_MEMDATA = 0,
    SE_DURABLE_MEMDATA,
    SE_BUFFER_POOL,
    SE_STORAGE_TYPE_BORDER,
} StorageTypeE;

typedef enum PageType {
    SE_UNDEFINE = 0,
    SE_DATA,
    SE_META,
} PageTypeE;

typedef enum PageOption {
    ENTER_PAGE_NORMAL = 0x0,
    ENTER_PAGE_PINNED = 0x1,
    ENTER_PAGE_RESIDENT = 0x2,
    ENTER_PAGE_SCAN = 0x4,
    ENTER_PAGE_LOAD = 0x8,
    ENTER_PAGE_INIT = 0x10,
    ENTER_PAGE_WRITE = 0x20,
    ENTER_PAGE_NOWAIT = 0x40,
    ENTER_PAGE_ACTIVE = 0x80,
    ENTER_PAGE_FREE = 0x100,
} PageOptionE;

typedef enum PageRecycleType {
    SE_RECYCLE_NORMAL = 0,
    SE_RECYCLE_TABLE,
    SE_RECYCLE_PRIORITY_MULTI,
    SE_RECYCLE_PRIORITY,
} PageRecycleTypeE;

typedef enum TableLoadPriority {
    TABLE_LOAD_PRIORITY_LOW = 0,
    TABLE_LOAD_PRIORITY_MID,
    TABLE_LOAD_PRIORITY_HIGH,
} TableLoadPriorityE;
typedef struct TablePriorityRecyArg {
    uint32_t recycleFlag;  // 当前和DB_RECYCLE_PRIORITY淘汰策略绑定
    TableLoadPriorityE
        priority;  // 如果该页需要按照最高优先级如表load功能，则为INDEX_PRIORITY_HIGH，如果为索引优先，则为INDEX_PRIORITY_MID
} TablePriorityRecyArgT;

typedef struct AllocPageParam {
    uint32_t spaceId;
    uint32_t trmId;
    uint32_t labelId;
    DbInstanceHdT dbInstance;
    void *labelRsmUndo;
} AllocPageParamT;

typedef struct CachePagePara {
    uint32_t labelId;  // 空闲页原先所属表Id
    bool needCache;    // 是否挂在MdSpace空闲页链表上,优先再次分配
} CachePageParaT;

typedef struct FreePageParam {
    uint32_t spaceId;
    PageIdT addr;
    DbInstanceHdT dbInstance;
    void *labelRsmUndo;
    CachePageParaT cachePagePara;  // 仅内存态有效
} FreePageParamT;

typedef struct FreeCachedPageParam {
    uint32_t spaceId;
    uint32_t labelId;
    DbInstanceHdT dbInstance;
    void *labelRsmUndo;
} FreeCachedPageParamT;

typedef struct TagPageMgrT PageMgrT;

typedef StatusInter (*SeAllocPageFunc)(PageMgrT *pageMgr, AllocPageParamT *allocPageParam, PageIdT *pageAddr);
typedef StatusInter (*SeFreePageFunc)(PageMgrT *pageMgr, FreePageParamT *freePageParam);
typedef StatusInter (*SeAllocExtentFunc)(PageMgrT *pageMgr, uint32_t spaceId, uint32_t trmId, PageIdT *extentId);
typedef StatusInter (*SeFreeExtentFunc)(PageMgrT *pageMgr, uint32_t spaceId, const PageIdT *extentId);
typedef StatusInter (*SeGetPageFunc)(
    PageMgrT *pageMgr, PageIdT pageAddr, uint8_t **page, PageOptionE option, bool isWrite);
typedef StatusInter (*SeGetPageFuncWithArg)(
    PageMgrT *pageMgr, PageIdT pageId, uint32_t option, void *recyArg, uint8_t **page);
typedef StatusInter (*SeGetPageInitFunc)(
    PageMgrT *pageMgr, uint32_t trmId, PageIdT pageId, PageOptionE option, uint8_t **page);
typedef void (*SeLeavePageFunc)(PageMgrT *pageMgr, PageIdT pageAddr, bool isChanged);
typedef void (*SeLeaveVirtualPageFunc)(PageMgrT *pageMgr, PageIdT pageAddr, bool isChanged, uint8_t *page);
typedef uint8_t *(*SeGetPageLatchFunc)(void *pageMgr, ShmemPtrT pageAddr);

typedef enum PageMgrExtraFuncType {
    PAGE_MGR_EXTRA_MEMDATA = 0,
    PAGE_MGR_EXTRA_DURABLE_MEMDATA,
    PAGE_MGR_EXTRA_BUFFER_POOL,
    PAGE_MGR_EXTRA_BUFFER_POOL_TRMID_OP_WITH_PAGE,
    PAGE_MGR_EXTRA_BUFFER_POOL_TRMID_OP,
    PAGE_MGR_EXTRA_BUFFER_POOL_PREFETCH_PAGES,
    PAGE_MGR_EXTRA_BUFFER_POOL_STOP_PREFETCH_PROC,
    // 提供resizebufferpool的能力
    PAGE_MGR_EXTRA_BUFFER_POOL_RESIZE,
    // 将指定的页从bufferPool的bucket中移除，模拟换出
    PAGE_MGR_EXTRA_BUFFER_POOL_INVALID_PAGE,
    PAGE_MGR_EXTRA_BUFFER_POOL_RESET,
    PAGE_MGR_EXTRA_BUTT
} PageMgrExtraFuncTypeE;
typedef void *(*SeGetPageMgrRegisterExtraFunc)(void *pageMgr, PageMgrExtraFuncTypeE funcType);

typedef Status (*TrmIdOpWithPageT)(SeInstanceT *seIns, ShmemPtrT metaShmAddr, bool isAdd);
typedef Status (*TrmIdOp)(SeInstanceT *seIns, uint32_t trmId, bool isAdd);
typedef Status (*PreFetchPageFunc)(SeInstanceT *seIns, SeFileTypeE fileType);
typedef void (*StopProcPrefetchFunc)(SeInstanceT *seIns);
typedef Status (*ResizeBufferFunc)(SeInstanceT *seIns, uint32_t bufferPoolSize);
typedef Status (*SeInvalidBufpoolPageFunc)(SeInstanceT *seIns, PageIdT pageAddr);
typedef Status (*ResetBufpoolFunc)(SeInstanceT *seIns);
struct TagPageMgrT {
    // 基于性能考虑，将getPage和leavePage放前边
    SeGetPageFunc getPageFunc;
    SeGetPageFuncWithArg getPageWithArgFunc;
    SeGetPageInitFunc getPageInitFunc;
    SeLeavePageFunc leavePageFunc;
    SeLeaveVirtualPageFunc leaveVirtualPageFunc;
    SeAllocPageFunc allocPageFunc;
    SeFreePageFunc freePageFunc;
    StorageTypeE type;
    uint8_t devShiftBit;        // devid占位
    uint8_t pageSizeShiftBit;   // slotid占位
    uint16_t pageAddrShiftBit;  // blockid占位
    SeGetPageMgrRegisterExtraFunc getExtraFunc;
    // bufferpool直连读用于解栈
    void *sessionCtx;
};

#define CRC_OFFSET (sizeof(DbLatchT))

SO_EXPORT_FOR_TS uint32_t SeGetPageTailReservedSize(void);
SO_EXPORT_FOR_TS void SeSetPageTailReservedSize(const SeConfigT *config);

inline static uint32_t SeGetPageTotalSize(const PageHeadT *page)
{
    return (uint32_t)page->endPos + 1;
}

inline static uint8_t *SeGetPageFreePos(PageHeadT *page)
{
    return (uint8_t *)page + SeGetPageTotalSize(page) - page->freeSize;
}

inline static __attribute__((always_inline)) bool DbIsPageIdValid(PageIdT pageId)
{
    return *(uint64_t *)&pageId != DB_INVALID_UINT64;
}

inline static __attribute__((always_inline)) bool DbIsPageIdEqual(PageIdT a, PageIdT b)
{
    return !(*(uint64_t *)&a ^ *(uint64_t *)&b);
}

inline static __attribute__((always_inline)) bool DbIsPageIdLessThan(PageIdT a, PageIdT b)
{
    return (a.deviceId < b.deviceId) || (a.deviceId == b.deviceId && a.blockId < b.blockId);
}

inline static uint32_t SerializePageId(const PageMgrT *pageMgr, const PageIdT pageId)
{
    return (uint32_t)((pageId.deviceId << pageMgr->pageAddrShiftBit) | pageId.blockId);
}

inline static PageIdT DeserializePageId(const PageMgrT *pageMgr, const uint32_t id)
{
    uint32_t bitMask = ~(DB_MAX_UINT32 << pageMgr->pageAddrShiftBit);
    return (PageIdT){.deviceId = id >> pageMgr->pageAddrShiftBit, .blockId = id & bitMask};
}

inline static FreeCachedPageParamT SeInitCachedPageParam(
    uint32_t spaceId, uint32_t labelId, DbInstanceHdT dbInstance, void *labelRsmUndo)
{
    FreeCachedPageParamT para = {
        .spaceId = spaceId, .labelId = labelId, .dbInstance = dbInstance, .labelRsmUndo = labelRsmUndo};
    return para;
}

inline static AllocPageParamT SeInitAllocPageParam(
    uint32_t spaceId, uint32_t trmId, uint32_t labelId, DbInstanceHdT dbInstance, void *labelRsmUndo)
{
    AllocPageParamT para = {
        .spaceId = spaceId, .trmId = trmId, .labelId = labelId, .dbInstance = dbInstance, .labelRsmUndo = labelRsmUndo};
    return para;
}

inline static FreePageParamT SeInitFreePageParam(
    uint32_t spaceId, PageIdT addr, DbInstanceHdT dbInstance, void *labelRsmUndo, uint32_t labelId, bool needCache)
{
    CachePageParaT cachePagePara = {.labelId = labelId, .needCache = needCache};

    FreePageParamT para = {.spaceId = spaceId,
        .addr = addr,
        .dbInstance = dbInstance,
        .labelRsmUndo = labelRsmUndo,
        .cachePagePara = cachePagePara};
    return para;
}

#ifdef FEATURE_SIMPLEREL
#define SIMPLEREL_ALL_BIT 32
typedef struct TagAddrCompressT {
    uint8_t devShiftBit;       // devid占位
    uint8_t pageSizeShiftBit;  // slotid占位
    uint8_t pageAddrShiftBit;  // blockid占位
} AddrCompressT;

inline static PageIdT TTreeDeserializePageId2(AddrCompressT *pageMgr, const uint32_t id)
{
    uint32_t bitMask = ~(DB_MAX_UINT32 << pageMgr->pageAddrShiftBit);
    return (PageIdT){.deviceId = id >> pageMgr->pageAddrShiftBit, .blockId = id & bitMask};
}

static ALWAYS_INLINE uint64_t SimplerelAddrUnCompress(AddrCompressT *pageMgr, const uint32_t addr)
{
    uint32_t devId = addr >> (SIMPLEREL_ALL_BIT - pageMgr->devShiftBit);
    uint32_t blockId = (addr >> (SIMPLEREL_ALL_BIT - pageMgr->devShiftBit - pageMgr->pageAddrShiftBit)) &
                       ((1 << pageMgr->pageAddrShiftBit) - 1);
    uint32_t pageBlockId = (uint32_t)((devId << pageMgr->pageAddrShiftBit) | blockId);
    TuplePointerOrTupleAddr ret = {
        .ptr =
            {
                .pageId = pageBlockId,
                .slotId = addr & ((1 << (32 - pageMgr->devShiftBit - pageMgr->pageAddrShiftBit)) - 1),
            },
    };
    return ret.addr;
}

// v1兼容，dev（最高）-block（次高）-slot（余位）共32位
static ALWAYS_INLINE uint32_t SimplerelAddrCompress(AddrCompressT *pageMgr, uint64_t addr)
{
    TuplePointerOrTupleAddr in = {.addr = addr};
    DB_ASSERT(in.ptr.slotId < (uint32_t)(1 << pageMgr->pageSizeShiftBit));
    PageIdT pageBlockId = TTreeDeserializePageId2(pageMgr, in.ptr.pageId);
    uint32_t addr32 = (pageBlockId.deviceId << (SIMPLEREL_ALL_BIT - pageMgr->devShiftBit)) |
                      (pageBlockId.blockId << (SIMPLEREL_ALL_BIT - pageMgr->devShiftBit - pageMgr->pageAddrShiftBit)) |
                      in.ptr.slotId;
#ifndef NDEBUG
    uint64_t uncompressAddr = SimplerelAddrUnCompress(pageMgr, addr32);
    if (uncompressAddr != addr) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
            "Compress addr (Origin:%" PRIu64 ", Compress:%" PRIu32 ", Uncompress:%" PRIu64 ")", addr, addr32,
            uncompressAddr);
        DB_ASSERT(false);
    }
#endif /* NDEBUG */
    return addr32;
}

#endif

SO_EXPORT_FOR_TS void SeInitPageAddrInfo(PageMgrT *pageMgr, uint32_t pageSize, uint32_t chunkCntPerDev);

SO_EXPORT void SeCheckPageHead(uint8_t *page, PageIdT addr);
SO_EXPORT_FOR_TS void SeInitPageHead(uint8_t *page, uint32_t trmId, uint32_t pageSize, PageIdT addr, bool isNewChunk);

#ifndef NDEBUG
static inline StatusInter SeFreePagePreCheck(PageHeadT *page, PageIdT pageAddr)
{
    if (!DbIsPageIdValid(page->addr) || !DbIsPageIdEqual(page->addr, pageAddr) || page->trmId == SE_INVALID_TRM_ID) {
        SE_ERROR(OBJECT_NOT_IN_PREREQUISITE_STATE_INTER, "Double Free Page(%" PRIu32 ", %" PRIu32 ")",
            pageAddr.deviceId, pageAddr.blockId);
        DB_ASSERT(false);
        return OBJECT_NOT_IN_PREREQUISITE_STATE_INTER;
    }
    return STATUS_OK_INTER;
}
#endif

SO_EXPORT_FOR_TS void SeResetPageHead(uint8_t *page);

#ifdef __cplusplus
}
#endif

#endif
