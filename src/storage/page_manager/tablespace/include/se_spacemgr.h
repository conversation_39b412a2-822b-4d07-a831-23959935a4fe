/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: chunkmgr模块负责memdata模式下的页管理
 * Author: zhangfengyu
 * Create: 2022-09-22
 */
#ifndef SE_SPACEMGR_H
#define SE_SPACEMGR_H

#include "adpt_spinlock.h"
#include "db_mem_context.h"
#include "se_define.h"
#include "adpt_types.h"
#include "db_internal_error.h"
#include "se_page.h"
#include "se_device.h"
#include "se_memdata.h"
#include "se_table_space_pub.h"
#include "se_dev_node.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MD_SPACE_SHMARRAY_CAPACITY 32

typedef struct TagLabelCachePageList {
    uint32_t labelId;  // 该list上的页对应的表id
    uint32_t listCnt;
    PageIdT head;
} LabelCachePageListT;

typedef struct TagMdSpace {
    uint32_t tableSpaceId;
    bool needRecycle;
    bool inUse;
    uint16_t reserve;
    uint32_t firstDevId;
    uint32_t lastDevId;
    uint32_t lowestFreeRateDevId;
    uint32_t maxDevSize;
    uint32_t protectDevSize;  // 初始dev数量
    uint32_t stepDevSize;     // dev拓展步长
    uint32_t curDevSize;      // 当前持有的dev数量
    ShmemPtrT cachedListsShm;  // 缓存的空闲页无法直接归还给OS,优先被分配,后台线程以表粒度进行free,成为空闲页
} MdSpaceT;
#define SPACE_INVALID_SPACE                                                                                         \
    (MdSpaceT)                                                                                                      \
    {                                                                                                               \
        0, 0, 0, 0, SE_INVALID_DEVICE_ID, SE_INVALID_DEVICE_ID, SE_INVALID_DEVICE_ID, 0, 0, 0, 0, DB_INVALID_SHMPTR \
    }

// NOTE: 如果后续要将device继续卸载到common或底层，可以从ChunkMgr层开始封装新的alloc/release接口
struct TagSpaceMgr {
    DbSpinLockT lock;
    uint16_t curUserSpaceNum;
#ifndef NDEBUG
    uint16_t curThread;
#endif
    uint32_t chunkSize;  // unit: B
    uint32_t chunkCntPerDev;
    ShmemPtrT devMgrShm;
    MdSpaceT spaceArray[DB_TABLE_SPACE_MAX_NUM];
};

StatusInter MdSpaceMgrInit(SeInstanceT *seIns, DbMemCtxT *topMemCtx);
void MdSpaceMgrDestroy(SeInstanceT *seIns, DbMemCtxT *topMemCtx);
StatusInter MdAllocChunk(
    SpaceMgrT *spaceMgr, uint32_t spaceId, PageIdT *addr, uint8_t **page, bool *isNewChunk, DbInstanceHdT dbInstance);
void MdFreeChunk(SpaceMgrT *spaceMgr, uint32_t spaceId, FreePageParamT *freePageParam, uint8_t *page, bool needRelease);
void MdSpaceCacheListDel(LabelCachePageListT *list, PageIdT newHead);
void MdFreeChunkToDev(SpaceMgrT *spaceMgr, MdSpaceT *space, FreePageParamT *freePageParam, bool needRelease);

static inline void SpaceMgrLock(SpaceMgrT *spaceMgr)
{
    DbSpinLock(&spaceMgr->lock);
#if !defined(HPE) && !defined(NDEBUG)
    spaceMgr->curThread = (uint16_t)DbThreadGetSysTid();
#endif
}

static inline void SpaceMgrUnlock(SpaceMgrT *spaceMgr)
{
    DbSpinUnlock(&spaceMgr->lock);
#if !defined(HPE) && !defined(NDEBUG)
    spaceMgr->curThread = 0;
#endif
}

#ifdef __cplusplus
}
#endif
#endif
