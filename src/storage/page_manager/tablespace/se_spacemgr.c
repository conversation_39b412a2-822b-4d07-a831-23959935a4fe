/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: spaceMgr模块负责memdata模式下的页管理
 * Author: zhangfengyu
 * Create: 2022-09-22
 */

#include "se_spacemgr.h"
#include "se_capacity_def_inner.h"
#include "se_dfx.h"
#include "se_log.h"
#include "se_rsm_tablespace_am.h"
#include "db_instance.h"
#include "db_alarm.h"
#include "se_daemon.h"

// storage alarm stat， 保存上个周期内失败/成功次数
DbAlarmStatT g_gmdbSeAlarmStat = {0};

static void MdSpaceFreeAllDevice(SpaceMgrT *spaceMgr, MdSpaceT *space, DbInstanceHdT dbInstance)
{
    DB_POINTER2(spaceMgr, space);
    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(spaceMgr->devMgrShm);
    if (devMgr == NULL) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "free all dev get devMgr(%" PRIu32 " %" PRIu32 ")",
            spaceMgr->devMgrShm.segId, spaceMgr->devMgrShm.offset);
        DB_ASSERT(false);
        return;
    }
    // 如果是非回收型的space，有可能发生reserve了device但没有实际申请，要把这个部分unreserve
    if (!space->needRecycle) {
        DevUnreserveDevice(devMgr, space->maxDevSize - space->curDevSize);
    }
    uint32_t devId = space->firstDevId;
    bool needRelease = (space->tableSpaceId == DB_UNDO_TABLE_SPACE_ID);
    while (devId != SE_INVALID_DEVICE_ID) {
        DevNodeT *devNode = DevNodeGetById(devMgr, devId);
        DevSetStatus(devMgr, devId, DEV_FREE);
        DevReturnDevice(devMgr, devId, needRelease);
        if (needRelease) {
            DevNodeSetNew(devNode, spaceMgr->chunkCntPerDev);
        }
        devId = devNode->nextDevId;
        *devNode = SE_INVALID_DEV_NODE;
    }
}

static StatusInter MdSpaceAllocDevice(SpaceMgrT *spaceMgr, MdSpaceT *space, DbInstanceHdT dbInstance)
{
    DB_POINTER2(spaceMgr, space);
    uint32_t devSize;

    DeviceMgrT *devMgr = DbShmPtrToAddr(spaceMgr->devMgrShm);
    if (SECUREC_UNLIKELY(devMgr == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Alloc dev get devMgr(%" PRIu32 " %" PRIu32 ")",
            spaceMgr->devMgrShm.segId, spaceMgr->devMgrShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    uint32_t devId = SE_INVALID_DEVICE_ID;
    StatusInter ret = DevAllocDevice(devMgr, &devId, &devSize, true);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "Alloc dev");
        return ret;
    }
    DB_ASSERT(devId != SE_INVALID_DEVICE_ID);

    DevNodeT *devDesc = DevNodeGetById(devMgr, devId);
    // 第一个device可能比较小，所以这里需要计算chunkCount
    DevNodeInit(devDesc, devSize / spaceMgr->chunkSize, space->tableSpaceId);
    devDesc->prevDevId = space->lastDevId;
    if (SeGetPersistMode() == PERSIST_INCREMENT && devId == 0) {
        DevNodeAllocChunkById(devDesc, 0);
    }

    if (space->lastDevId != SE_INVALID_DEVICE_ID) {
        DevNodeT *lastDevDesc = DevNodeGetById(devMgr, space->lastDevId);
        lastDevDesc->nextDevId = devId;
    } else {
        space->firstDevId = devId;
    }
    space->lastDevId = devId;
    space->curDevSize++;

    return STATUS_OK_INTER;
}

static StatusInter MdSpaceInitDeviceArray(SpaceMgrT *spaceMgr, MdSpaceT *space, DbInstanceHdT dbInstance)
{
    DB_POINTER2(spaceMgr, space);
    for (uint32_t i = 0; i < space->protectDevSize; ++i) {
        StatusInter ret = MdSpaceAllocDevice(spaceMgr, space, dbInstance);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "Init device(%" PRIu32 ", %" PRIu32 ") unsucc", i, space->protectDevSize);
            MdSpaceFreeAllDevice(spaceMgr, space, dbInstance);
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

static uint32_t MdFindLowestFreeRateDevice(DeviceMgrT *devMgr, const SpaceMgrT *spaceMgr, const MdSpaceT *space)
{
    DB_POINTER3(devMgr, spaceMgr, space);
    // 找到空闲率最低的device
    uint32_t curDevId = space->firstDevId;
    uint32_t freeChunkCount = spaceMgr->chunkCntPerDev;
    uint32_t minFreeRateDevId = SE_INVALID_DEVICE_ID;
#ifdef FEATURE_SIMPLEREL
    if (space->tableSpaceId == DB_DEFAULT_TABLE_SPACE_INDEX) {
        // 重置一下 lowestFreeRateDevId 使其优先从 DEV_CACHE_CAPACITY 之内的device中分配页出来
        for (uint32_t i = 0; i < DEV_CACHE_CAPACITY; ++i) {
            DevNodeT *newDevNode = DevNodeGetById(devMgr, i);
            if (devMgr->devDesc[i].status != DEV_USED || newDevNode->tableSpaceId != DB_DEFAULT_TABLE_SPACE_INDEX) {
                continue;
            }
            if (newDevNode->freeChunkCount > 0) {
                return i;
            }
        }
    }
#endif
    while (curDevId != SE_INVALID_DEVICE_ID) {
        DevNodeT *devDesc = DevNodeGetById(devMgr, curDevId);
        if (devDesc->freeChunkCount != 0 && devDesc->freeChunkCount <= freeChunkCount) {
            freeChunkCount = devDesc->freeChunkCount;
            minFreeRateDevId = curDevId;
        }
        curDevId = devDesc->nextDevId;
    }
    return minFreeRateDevId;
}

static StatusInter MdSpaceInitCachedLists(const SeInstanceT *seIns, MdSpaceT *space)
{
    DbMemCtxT *memCtx = DbGetShmemCtxById(seIns->seTopShmMemCtxId, seIns->instanceId);
    if (memCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get shmMemCtx, shmCtxId: %" PRIu32, seIns->seTopShmMemCtxId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    DbShmArrayT *shmArrayPtr = (DbShmArrayT *)SeShmAlloc(memCtx, sizeof(DbShmArrayT), &space->cachedListsShm);
    if (shmArrayPtr == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "memctx (shmCtxId = %" PRIu32 ") alloc space, size = %" PRIu32,
            seIns->seTopShmMemCtxId, (uint32_t)sizeof(DbShmArrayT));
        return OUT_OF_MEMORY_INTER;
    }

    Status ret = DbShmArrayInit(shmArrayPtr, MD_SPACE_SHMARRAY_CAPACITY, sizeof(LabelCachePageListT),
        seIns->seTopShmMemCtxId, seIns->dbInstance);
    if (ret != GMERR_OK) {
        DbShmemCtxFree(memCtx, space->cachedListsShm);
        space->cachedListsShm = DB_INVALID_SHMPTR;
        SE_LAST_ERROR(INTERNAL_ERROR_INTER, "md space shmArray init");
        return INTERNAL_ERROR_INTER;
    }

    return STATUS_OK_INTER;
}

static StatusInter MdSpaceInitTableSpace(
    const SeInstanceT *seIns, TableSpaceCfgT *tableSpaceCfg, SpaceMgrT *spaceMgr, MdSpaceT *space)
{
    DB_POINTER4(seIns, tableSpaceCfg, spaceMgr, space);
    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(seIns->devMgrShm);
    if (devMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Init tablespace get devMgr(%" PRIu32 " %" PRIu32 ")",
            seIns->devMgrShm.segId, seIns->devMgrShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    StatusInter ret = STATUS_OK_INTER;
    MdSpaceT tmpSpace = SPACE_INVALID_SPACE;
    uint32_t devCnt = 0;
    tmpSpace.tableSpaceId = tableSpaceCfg->tableSpaceId;
    tmpSpace.needRecycle = tableSpaceCfg->maxMemSize == DB_UNLIMIT_SPACE_SIZE;
    tmpSpace.protectDevSize = (uint32_t)((tableSpaceCfg->initSize * DB_KIBI) / seIns->seConfig.deviceSize);
    if (tmpSpace.needRecycle) {
        tmpSpace.maxDevSize = DevGetMaxCount(devMgr);
        devCnt = tmpSpace.protectDevSize;
    } else {
        tmpSpace.maxDevSize = (uint32_t)(tableSpaceCfg->maxMemSize) * DB_KIBI / seIns->seConfig.deviceSize;
        devCnt = tmpSpace.maxDevSize;
    }
    ret = DevReserveDevice(devMgr, devCnt);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        // no enough space
        SE_ERROR(ret, "MdSpace init Space unsucc");
        return ret;
    }
    ret = MdSpaceInitDeviceArray(spaceMgr, &tmpSpace, seIns->dbInstance);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        DevUnreserveDevice(devMgr, devCnt);
        SE_ERROR(ret, "MdSpace init Space unsucc");
        return ret;
    }
    tmpSpace.stepDevSize = (uint32_t)(tableSpaceCfg->stepSize) * DB_KIBI / seIns->seConfig.deviceSize;
    tmpSpace.lowestFreeRateDevId = MdFindLowestFreeRateDevice(devMgr, spaceMgr, &tmpSpace);
    ret = MdSpaceInitCachedLists(seIns, &tmpSpace);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    *space = tmpSpace;
    return STATUS_OK_INTER;
}

static StatusInter MdSpaceFindAvailableSpaceSlot(
    const SpaceMgrT *spaceMgr, const TableSpaceCfgT *tableSpaceCfg, uint32_t *tspIdx)
{
    DB_POINTER3(spaceMgr, tableSpaceCfg, tspIdx);
    uint32_t tmpIdx = DB_TABLE_SPACE_MAX_NUM;
    if (tableSpaceCfg->tableSpaceId == DB_DEFAULT_TABLE_SPACE_ID) {
        tmpIdx = DB_DEFAULT_TABLE_SPACE_INDEX;
    } else if (tableSpaceCfg->tableSpaceId == DB_UNDO_TABLE_SPACE_ID) {
        tmpIdx = DB_UNDO_TABLE_SPACE_INDEX;
    } else {
        // 找到一个空闲可以使用的space slot，顺便检查这tspid是不是已经被使用了
        for (uint32_t i = DB_SYS_TABLE_SPACE_NUM; i < DB_TABLE_SPACE_MAX_NUM; ++i) {
            if (SECUREC_UNLIKELY(spaceMgr->spaceArray[i].tableSpaceId == tableSpaceCfg->tableSpaceId &&
                                 spaceMgr->spaceArray[i].inUse == true)) {
                SE_LAST_ERROR(
                    DUPLICATE_OBJECT_INTER, "tableSpaceId %" PRIu32 "is duplicate", tableSpaceCfg->tableSpaceId);
                return DUPLICATE_OBJECT_INTER;  // 这个错误码固定，EE层要用来做特殊判断
            }
            // 找到一个最前的空的space slot, 而且tspIdx还没有赋值过，继续往后检查tableSpaceId是否重复，不能break
            if (spaceMgr->spaceArray[i].inUse == false && tmpIdx == DB_TABLE_SPACE_MAX_NUM) {
                tmpIdx = i;
            }
        }
    }
    *tspIdx = tmpIdx;
    return STATUS_OK_INTER;
}

StatusInter MdSpaceCreateTableSpace(const SeInstanceT *seIns, TableSpaceCfgT *tableSpaceCfg, uint32_t *tableSpaceIndex)
{
    DB_POINTER2(seIns, tableSpaceCfg);
    if (tableSpaceCfg->isUseRsm) {
        RsmSpaceMgrT *rsmSpaceMgr = (RsmSpaceMgrT *)DbShmPtrToAddr(seIns->rsmSpaceMgrShm);
        if (SECUREC_UNLIKELY(rsmSpaceMgr == NULL)) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
                "Get rsmSpaceMgr(%" PRIu32 " %" PRIu32 ") unsucc when operating tableSpace",
                seIns->rsmSpaceMgrShm.segId, seIns->rsmSpaceMgrShm.offset);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        return MdRsmSpaceCreateTableSpace(seIns, rsmSpaceMgr, tableSpaceCfg, tableSpaceIndex);
    }
    SpaceMgrT *spaceMgr = (SpaceMgrT *)DbShmPtrToAddr(seIns->spaceMgrShm);
    if (SECUREC_UNLIKELY(spaceMgr == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "Get spaceMgr(%" PRIu32 " %" PRIu32 ") unsucc when operating tableSpace", seIns->spaceMgrShm.segId,
            seIns->spaceMgrShm.offset);
        DB_ASSERT(0);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    SpaceMgrLock(spaceMgr);
    if (SECUREC_UNLIKELY(spaceMgr->curUserSpaceNum >= DB_USER_TABLE_SPACE_NUM)) {  // 校验user-tableSpace的个数
        SpaceMgrUnlock(spaceMgr);
        SE_LAST_ERROR(
            PROGRAM_LIMIT_EXCEEDED_INTER, "New tableSpace exceed limit %" PRIu32 "", spaceMgr->curUserSpaceNum);
        return PROGRAM_LIMIT_EXCEEDED_INTER;
    }

    uint32_t tspIdx = DB_TABLE_SPACE_MAX_NUM;
    StatusInter ret = MdSpaceFindAvailableSpaceSlot(spaceMgr, tableSpaceCfg, &tspIdx);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SpaceMgrUnlock(spaceMgr);
        return ret;
    }

    MdSpaceT *space = &spaceMgr->spaceArray[tspIdx];
    ret = MdSpaceInitTableSpace(seIns, tableSpaceCfg, spaceMgr, space);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SpaceMgrUnlock(spaceMgr);
        return ret;
    }

    space->inUse = true;
    if (tableSpaceCfg->tableSpaceId != DB_UNDO_TABLE_SPACE_ID &&
        tableSpaceCfg->tableSpaceId != DB_DEFAULT_TABLE_SPACE_ID) {
        spaceMgr->curUserSpaceNum++;
    }
    SpaceMgrUnlock(spaceMgr);

    *tableSpaceIndex = tspIdx;
    return ret;
}

static StatusInter MdCreateDefaultAndUndoSpace(const SeInstanceT *seIns)
{
    DB_POINTER(seIns);
    TableSpaceCfgT tspCfg = {
        .initSize = (uint16_t)(seIns->seConfig.deviceSize / DB_KIBI),
        .stepSize = (uint16_t)(seIns->seConfig.deviceSize / DB_KIBI),
        .maxMemSize = DB_UNLIMIT_SPACE_SIZE,
        .reserve = 0,
        .isUseRsm = false,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
    };
    uint32_t tspIdx;
    StatusInter ret = MdSpaceCreateTableSpace(seIns, &tspCfg, &tspIdx);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Create default space unsucc.");
        return ret;
    }
    DB_ASSERT(tspIdx == DB_DEFAULT_TABLE_SPACE_INDEX);

    tspCfg = (TableSpaceCfgT){
        .initSize = 0,
        .stepSize = (uint16_t)(seIns->seConfig.deviceSize / DB_KIBI),
        .maxMemSize = DB_UNLIMIT_SPACE_SIZE,
        .reserve = 0,
        .isUseRsm = false,
        .tableSpaceId = DB_UNDO_TABLE_SPACE_ID,
    };
    ret = MdSpaceCreateTableSpace(seIns, &tspCfg, &tspIdx);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Create default space unsucc.");
        return ret;
    }
    DB_ASSERT(tspIdx == DB_UNDO_TABLE_SPACE_INDEX);

    return STATUS_OK_INTER;
}

StatusInter MdSpaceMgrInit(SeInstanceT *seIns, DbMemCtxT *topMemCtx)
{
    DB_POINTER2(seIns, topMemCtx);

    DeviceMgrT *devMgr = DbShmPtrToAddr(seIns->devMgrShm);
    if (devMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get devMgr(%" PRIu32 " %" PRIu32 ") unsucc", seIns->devMgrShm.segId,
            seIns->devMgrShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    uint32_t chunkCntPerDev = seIns->seConfig.deviceSize / seIns->seConfig.pageSize;

    /* SpaceMgr结构：|SpaceMgr|SpaceTable[SpaceDesc]|DevTable[DevDesc]|
     * SpaceDesc: |MdSpaceT1|MdSpaceT2|
     */
    uint32_t allocSize = (uint32_t)sizeof(SpaceMgrT);
    SpaceMgrT *spaceMgr = (SpaceMgrT *)SeShmAlloc(topMemCtx, allocSize, &seIns->spaceMgrShm);
    if (spaceMgr == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "shmMalloc spaceMgr(size %" PRIu32 ") unsucc.", allocSize);
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_s(spaceMgr, allocSize, 0x0, allocSize);

    DbSpinInit(&spaceMgr->lock);
    spaceMgr->curUserSpaceNum = 0;
    spaceMgr->chunkSize = seIns->seConfig.pageSize * DB_KIBI;
    spaceMgr->chunkCntPerDev = chunkCntPerDev;
    spaceMgr->devMgrShm = seIns->devMgrShm;

    StatusInter ret = MdCreateDefaultAndUndoSpace(seIns);
    if (ret != STATUS_OK_INTER) {
        DbShmemCtxFree(topMemCtx, seIns->spaceMgrShm);
        seIns->spaceMgrShm = DB_INVALID_SHMPTR;
    }
    return ret;
}

static StatusInter MdSpaceAllocChunk(
    SpaceMgrT *spaceMgr, MdSpaceT *space, PageIdT *addr, uint8_t **page, bool *isNewChunk)
{
    DB_POINTER5(spaceMgr, space, addr, page, isNewChunk);
    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(spaceMgr->devMgrShm);
    if (devMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get devMgr unsucc(%" PRIu32 " %" PRIu32 ") when alloc chunk",
            spaceMgr->devMgrShm.segId, spaceMgr->devMgrShm.offset);
        DB_ASSERT(false);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    uint8_t *devPtr = DevGetEntry(devMgr, space->lowestFreeRateDevId);
    if (devPtr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get device(%" PRIu32 ") unsucc when alloc trunk, dev max %" PRIu32,
            space->lowestFreeRateDevId, devMgr->devMaxCount);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    DevNodeT *devDesc = DevNodeGetById(devMgr, space->lowestFreeRateDevId);
    uint32_t chunkId = SE_INVALID_BLOCK_ID;
    DevNodeAllocChunk(devDesc, &chunkId, isNewChunk);
    DevSetStatus(devMgr, space->lowestFreeRateDevId, DEV_USED);

    addr->deviceId = space->lowestFreeRateDevId;
    addr->blockId = chunkId;

    *page = (uint8_t *)(uintptr_t)(devPtr + (uint64_t)(addr->blockId * spaceMgr->chunkSize));

    if (devDesc->freeChunkCount == 0) {
        DevSetStatus(devMgr, (uint32_t)space->lowestFreeRateDevId, DEV_FULL);
        space->lowestFreeRateDevId = MdFindLowestFreeRateDevice(devMgr, spaceMgr, space);
    }
    return STATUS_OK_INTER;
}

static StatusInter MdSpaceExpandSpace(SpaceMgrT *spaceMgr, MdSpaceT *space, DbInstanceHdT dbInstance)
{
    if (space->curDevSize == space->maxDevSize) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "Out of mem, tableSpaceId:%" PRIu32 ", maxDeviceSize:%" PRIu32,
            space->tableSpaceId, space->maxDevSize);
        return OUT_OF_MEMORY_INTER;
    }
    StatusInter ret = STATUS_OK_INTER;
    for (uint32_t i = 0; i < space->stepDevSize; ++i) {
        ret = MdSpaceAllocDevice(spaceMgr, space, dbInstance);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Out of mem, remainedSize: %" PRIu32 " stepSize: %" PRIu32 ", tableSpaceId: %" PRIu32,
                space->maxDevSize - space->curDevSize, space->stepDevSize, space->tableSpaceId);
            return ret;
        }
        if (space->curDevSize == space->maxDevSize) {
            // 经过内存归还给os后，可能会出现超的情况，eg步长是2，内存归还了1块device，下次再申请，这里就会超过maxSize
            break;
        }
    }
    space->lowestFreeRateDevId = space->lastDevId;
    return ret;
}

static StatusInter MdReserveAndExpandSpace(
    MdSpaceT *space, DeviceMgrT *devMgr, SpaceMgrT *spaceMgr, DbInstanceHdT dbInstance)
{
    StatusInter ret = STATUS_OK_INTER;
    if (space->needRecycle) {
        ret = DevReserveDevice(devMgr, space->stepDevSize);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "expand space unsucc because reserve device");
            return ret;
        }
    }
    ret = MdSpaceExpandSpace(spaceMgr, space, dbInstance);
    if (ret != STATUS_OK_INTER && space->needRecycle) {
        DevUnreserveDevice(devMgr, space->stepDevSize);
    }
    return ret;
}

StatusInter MdAllocChunkFromDev(
    SpaceMgrT *spaceMgr, uint32_t spaceId, PageIdT *addr, uint8_t **page, bool *isNewChunk, DbInstanceHdT dbInstance)
{
    StatusInter ret = STATUS_OK_INTER;
    MdSpaceT *space = &spaceMgr->spaceArray[spaceId];
    if (space->lowestFreeRateDevId == SE_INVALID_DEVICE_ID) {
        // 需要回收的space每次拓展时都需要去reserve
        DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(spaceMgr->devMgrShm);
        if (devMgr == NULL) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get devMgr(%" PRIu32 " %" PRIu32 ") unsucc when alloc chunk",
                spaceMgr->devMgrShm.segId, spaceMgr->devMgrShm.offset);
            DB_ASSERT(false);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        ret = MdReserveAndExpandSpace(space, devMgr, spaceMgr, dbInstance);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            return ret;
        }
    }
    DB_ASSERT(space->lowestFreeRateDevId != SE_INVALID_DEVICE_ID);
    ret = MdSpaceAllocChunk(spaceMgr, space, addr, page, isNewChunk);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    DB_ASSERT(DbIsPageIdValid(*addr));  // 这种情况不应发生,如果发生就是代码有问题
    return ret;
}

void MdSpaceCacheListDel(LabelCachePageListT *list, PageIdT newHead)
{
    DB_POINTER(list);

    list->head = newHead;
    DB_ASSERT(list->listCnt > 0);
    list->listCnt--;

    return;
}

StatusInter MdAllocChunkFromList(
    SpaceMgrT *spaceMgr, LabelCachePageListT *list, PageIdT *addr, uint8_t **page, bool *isNewChunk)
{
    DB_POINTER5(spaceMgr, list, addr, page, isNewChunk);

    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(spaceMgr->devMgrShm);
    if (devMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get devMgr(%" PRIu32 " %" PRIu32 ") unsucc when alloc chunk",
            spaceMgr->devMgrShm.segId, spaceMgr->devMgrShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    DB_ASSERT(DbIsPageIdValid(list->head));
    PageIdT head = list->head;
    uint8_t *pageHead = DevGetEntry(devMgr, head.deviceId);
    if (SECUREC_UNLIKELY(pageHead == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "alloc chunk get dev:%" PRIu32 ", devMax %" PRIu32, head.deviceId,
            devMgr->devMaxCount);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    pageHead = (uint8_t *)(uintptr_t)(pageHead + ((uint64_t)head.blockId << devMgr->pageSizeShiftBit));
    MdSpaceCacheListDel(list, ((PageHeadT *)pageHead)->nextPageId);

    *page = pageHead;
    *addr = head;
    *isNewChunk = false;
    return STATUS_OK_INTER;
}

// TableSpaceId ：Catalog产生的一个概念,在存储引擎不会有实际使用
// TableSpaceIndex ：spaceId ->申请页,释放页使用的入参
// 上层调用的时候一定要注意入参!
StatusInter MdAllocChunk(
    SpaceMgrT *spaceMgr, uint32_t spaceId, PageIdT *addr, uint8_t **page, bool *isNewChunk, DbInstanceHdT dbInstance)
{
    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(spaceMgr->devMgrShm);
    if (devMgr == NULL) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "alloc chunk get devMgr(%" PRIu32 " %" PRIu32 ")",
            spaceMgr->devMgrShm.segId, spaceMgr->devMgrShm.offset);
        DB_ASSERT(false);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    // 当Undo申请内存且目前已申请的device数量小于Cache的上限时，undo使用defaultSpace表空间申请页
    if (spaceId == DB_UNDO_TABLE_SPACE_INDEX && !UndoSpaceHitSplitThreShold(devMgr)) {
        spaceId = DB_DEFAULT_TABLE_SPACE_INDEX;
    }
    SpaceMgrLock(spaceMgr);
    if (SECUREC_UNLIKELY(!spaceMgr->spaceArray[spaceId].inUse)) {
        SpaceMgrUnlock(spaceMgr);
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "inv tableSpaceIndex: %" PRIu32, spaceId);
        return INVALID_PARAMETER_VALUE_INTER;
    }

    StatusInter ret = STATUS_OK_INTER;
    MdSpaceT *space = &spaceMgr->spaceArray[spaceId];

    DbShmArrayT *shmArray = DbShmPtrToAddr(space->cachedListsShm);
    if (shmArray == NULL) {
        SpaceMgrUnlock(spaceMgr);
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get md space list shm, (segid:%" PRIu32 " offset:%" PRIu32 ")",
            space->cachedListsShm.segId, space->cachedListsShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    DbShmArrayIteratorT iter;
    LabelCachePageListT *pageList = NULL;
    for (DbShmArrayInitIterator(&iter, shmArray); DbShmArrayIteratorValid(&iter); DbShmArrayIteratorNext(&iter)) {
        LabelCachePageListT *cachePageList = (LabelCachePageListT *)DbShmArrayIteratorGetItem(&iter);
        if (cachePageList->listCnt != 0) {
            pageList = cachePageList;
            break;
        }
    }

    if (pageList != NULL) {
        ret = MdAllocChunkFromList(spaceMgr, pageList, addr, page, isNewChunk);
    } else {
        ret = MdAllocChunkFromDev(spaceMgr, spaceId, addr, page, isNewChunk, dbInstance);
#ifdef FEATURE_SIMPLEREL
        if (ret == OUT_OF_MEMORY_INTER && spaceId == DB_UNDO_TABLE_SPACE_INDEX) {
            SpaceMgrUnlock(spaceMgr);
            return MdAllocChunk(spaceMgr, DB_DEFAULT_TABLE_SPACE_INDEX, addr, page, isNewChunk, dbInstance);
        }
#endif
    }
    SpaceMgrUnlock(spaceMgr);
    return ret;
}

static void MdSpaceTryReturnDevice(
    SpaceMgrT *spaceMgr, DeviceMgrT *devMgr, MdSpaceT *space, const FreePageParamT *freePageParam, bool needRelease)
{
    if (!space->needRecycle) {
        return;
    }
    PageIdT addr = freePageParam->addr;
    DevNodeT *devDesc = DevNodeGetById(devMgr, addr.deviceId);
    // 如果device已经不再使用，且目前持有的device数量比initSize多，且需要回收
    if (space->curDevSize > space->protectDevSize) {
        if (devDesc->prevDevId != SE_INVALID_DEVICE_ID) {
            DevNodeT *lastDevDesc = DevNodeGetById(devMgr, devDesc->prevDevId);
            lastDevDesc->nextDevId = devDesc->nextDevId;
        } else {  // 说明这是第一个device
            space->firstDevId = devDesc->nextDevId;
        }
        if (devDesc->nextDevId != SE_INVALID_DEVICE_ID) {
            DevNodeT *nextDevDesc = DevNodeGetById(devMgr, devDesc->nextDevId);
            nextDevDesc->prevDevId = devDesc->prevDevId;
        }
        if (space->lastDevId == addr.deviceId) {
            space->lastDevId = devDesc->prevDevId;
        }
        space->curDevSize--;
        DevReturnDevice(devMgr, addr.deviceId, needRelease);
        // 与DevReturnDevice内部释放device内存的判断一致
        if (needRelease && addr.deviceId >= DEV_CACHE_CAPACITY) {
            DevNodeSetNew(devDesc, spaceMgr->chunkCntPerDev);
        }
        *devDesc = SE_INVALID_DEV_NODE;
    }
}

static bool MdSpaceCheckOwnerSpace(SpaceMgrT *spaceMgr, uint32_t spaceId, uint32_t devId)
{
    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(spaceMgr->devMgrShm);
    if (devMgr == NULL) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get devMgr(%" PRIu32 " %" PRIu32 ") unsucc when get spaceInfo",
            spaceMgr->devMgrShm.segId, spaceMgr->devMgrShm.offset);
        DB_ASSERT(false);
        return false;
    }
    MdSpaceT *space = &spaceMgr->spaceArray[spaceId];
    uint32_t curDevId = space->firstDevId;
    while (curDevId != SE_INVALID_DEVICE_ID) {
        if (curDevId == devId) {
            return true;
        }
        curDevId = DevNodeGetById(devMgr, curDevId)->nextDevId;
    }
    return false;
}

static bool MdSpaceCheckChunkOwnerShip(SpaceMgrT *spaceMgr, uint32_t *spaceId, PageIdT addr)
{
    DB_POINTER(spaceMgr);

    if (!spaceMgr->spaceArray[*spaceId].inUse) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "inv tableSpaceIndex: %" PRIu32 ".", *spaceId);
        return false;
    }
    if (MdSpaceCheckOwnerSpace(spaceMgr, *spaceId, addr.deviceId)) {
        return true;
    }
    if (*spaceId == DB_UNDO_TABLE_SPACE_INDEX &&
        MdSpaceCheckOwnerSpace(spaceMgr, DB_DEFAULT_TABLE_SPACE_INDEX,
            addr.deviceId)) {  // Undo释放的时候，页有可能会在defaultSpace，这里也查询一下
        *spaceId = DB_DEFAULT_TABLE_SPACE_INDEX;
        return true;
    }
    SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "Page(%" PRIu32 ",%" PRIu32 ") no in space: %" PRIu32 ".",
        addr.deviceId, addr.blockId, *spaceId);

    return false;
}

void MdFreeChunkToDev(SpaceMgrT *spaceMgr, MdSpaceT *space, FreePageParamT *freePageParam, bool needRelease)
{
    DB_POINTER3(spaceMgr, space, freePageParam);
    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(spaceMgr->devMgrShm);
    if (devMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get devMgr(%" PRIu32 " %" PRIu32 ") unsucc when free",
            spaceMgr->devMgrShm.segId, spaceMgr->devMgrShm.offset);
        DB_ASSERT(false);
        return;
    }

    PageIdT addr = freePageParam->addr;
    DevNodeT *devDesc = DevNodeGetById(devMgr, addr.deviceId);
    DevNodeFreeChunk(devDesc, addr.blockId);
    DevSetStatus(devMgr, addr.deviceId, DEV_USED);

    if (devDesc->freeChunkCount == spaceMgr->chunkCntPerDev) {
        DevSetStatus(devMgr, addr.deviceId, DEV_FREE);
        MdSpaceTryReturnDevice(spaceMgr, devMgr, space, freePageParam, needRelease);
    }
    space->lowestFreeRateDevId = MdFindLowestFreeRateDevice(devMgr, spaceMgr, space);
}

static void MdSpaceGetSpaceTotalChunkCount(
    SpaceMgrT *spaceMgr, const MdSpaceT *space, uint32_t *totalChunkCount, uint32_t *freeChunkCount)
{
    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(spaceMgr->devMgrShm);
    if (devMgr == NULL) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get devMgr(%" PRIu32 " %" PRIu32 ") unsucc when get spaceInfo",
            spaceMgr->devMgrShm.segId, spaceMgr->devMgrShm.offset);
        DB_ASSERT(false);
        return;
    }
    uint32_t curDevId = space->firstDevId;
    while (curDevId != SE_INVALID_DEVICE_ID) {
        DevNodeT *devDesc = DevNodeGetById(devMgr, curDevId);
        *totalChunkCount += spaceMgr->chunkCntPerDev;
        *freeChunkCount += devDesc->freeChunkCount;
        curDevId = devDesc->nextDevId;
    }
}

/*
totalChunkCount: 所有space已拥有的device(不包含预留的)包含的的页的总数
freeChunkCount: 所有space已拥有的device(不包含预留的)内的空闲页的总数
*/
static uint32_t MdSpaceGetUsedChunkCount(SpaceMgrT *spaceMgr)
{
    DB_POINTER(spaceMgr);

    uint32_t totalChunkCount = 0;
    uint32_t freeChunkCount = 0;
    for (uint32_t i = 0; i < DB_TABLE_SPACE_MAX_NUM; ++i) {
        MdSpaceT *space = &spaceMgr->spaceArray[i];
        if (space->inUse) {
            MdSpaceGetSpaceTotalChunkCount(spaceMgr, space, &totalChunkCount, &freeChunkCount);
        }
    }

    DB_ASSERT(totalChunkCount >= freeChunkCount);
    return totalChunkCount - freeChunkCount;
}

void SeSetStatDeviceInfo(SpaceMgrT *spaceMgr, MemDataInfoT *memDataStat)
{
    DB_POINTER(spaceMgr);

    SpaceMgrLock(spaceMgr);
    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(spaceMgr->devMgrShm);
    if (devMgr == NULL) {
        SpaceMgrUnlock(spaceMgr);
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get deviceMgr(%" PRIu32 " %" PRIu32 ") unsucc.",
            spaceMgr->devMgrShm.segId, spaceMgr->devMgrShm.offset);
        DB_ASSERT(false);
        return;
    }
    memDataStat->totalChunkCount = (uint32_t)(memDataStat->maxMemSize / memDataStat->pageSize);
    memDataStat->totalFreeChunkCount = memDataStat->totalChunkCount - MdSpaceGetUsedChunkCount(spaceMgr);
    memDataStat->chunkCntPerDev = spaceMgr->chunkCntPerDev;
    DevGetDeviceInfo(devMgr, memDataStat);
    memDataStat->deviceCount = 0;
    for (uint32_t i = 0; i < DevGetMaxCount(devMgr); ++i) {
        uint32_t devStatus = DevGetStatus(devMgr, i);
        if (devStatus != DEV_NOT_USED) {
            DevNodeT *devNode = DevNodeGetById(devMgr, i);
            memDataStat->devDesc[memDataStat->deviceCount].freeChunkCountInDev =
                devStatus == DEV_FREE ? spaceMgr->chunkCntPerDev : devNode->freeChunkCount;
            memDataStat->devDesc[memDataStat->deviceCount].tableSpaceId = devNode->tableSpaceId;
            DevGetDevInfo(devMgr, i, &memDataStat->devDesc[memDataStat->deviceCount]);
            memDataStat->deviceCount++;
        }
    }
    SpaceMgrUnlock(spaceMgr);
}

Status SeInitMemdataInfo(DbMemCtxT *dynamicMemCtx, SeRunCtxHdT seRunCtxHd, MemDataInfoT **memDataStat)
{
    DB_POINTER3(dynamicMemCtx, seRunCtxHd, memDataStat);

    uint16_t deviceDescNum;
    Status ret = DevGetStatDescNum(seRunCtxHd, &deviceDescNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t deviceDescSize = (uint32_t)((uint32_t)sizeof(DeviceDescStatT) * (uint32_t)deviceDescNum);
    uint32_t memDataStatSize = (uint32_t)((uint32_t)sizeof(MemDataInfoT) + deviceDescSize);
    // 申请一块动态内存临时存储memdata管理结构的DFX信息，由该函数的上层调用者释放
    MemDataInfoT *memDataStatTmp = (MemDataInfoT *)DbDynMemCtxAlloc((DbMemCtxT *)dynamicMemCtx, memDataStatSize);
    if (memDataStatTmp == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc memDataStat unsucc");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(memDataStatTmp, memDataStatSize, 0x0, memDataStatSize);
    *memDataStat = memDataStatTmp;
    return GMERR_OK;
}

Status SeGetMemdataStat(SeRunCtxHdT seRunCtx, MemDataInfoT **memDataStat, DbMemCtxT *dynamicMemCtx)
{
    Status ret = SeInitMemdataInfo(dynamicMemCtx, seRunCtx, memDataStat);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "memdata init unsucc. ret = %" PRIu32 ".", (uint32_t)ret);
        return ret;
    }

    SeRunCtxT *statRunCtx = seRunCtx;
    SeInstanceT *seIns = (SeInstanceT *)statRunCtx->seIns;

    (*memDataStat)->instanceId = seIns->seConfig.instanceId;
    (*memDataStat)->pageSize = seIns->seConfig.pageSize * DB_KIBI;
    (*memDataStat)->maxMemSize = (uint64_t)seIns->seConfig.maxSeMem * DB_KIBI;

    SpaceMgrT *devMgr = (SpaceMgrT *)DbShmPtrToAddr(seIns->spaceMgrShm);
    if (devMgr == NULL) {
        DbDynMemCtxFree((DbMemCtxT *)dynamicMemCtx, *memDataStat);
        *memDataStat = NULL;
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get spaceMgr(%" PRIu32 " %" PRIu32 ") unsucc when get memdata stat",
            seIns->spaceMgrShm.segId, seIns->spaceMgrShm.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    SeSetStatDeviceInfo(devMgr, (*memDataStat));

    DB_ASSERT((*memDataStat)->pageSize != 0);
    DB_ASSERT((*memDataStat)->totalChunkCount >= (*memDataStat)->totalFreeChunkCount);
    // 性能原因，不使用double，使用uint32_t
    uint32_t totalChunkCount = (*memDataStat)->totalChunkCount;
    uint32_t usedChunkCount = (*memDataStat)->totalChunkCount - (*memDataStat)->totalFreeChunkCount;
    (*memDataStat)->chunkUtilizationRatio = usedChunkCount * DB_PERCENTAGE_BASE / totalChunkCount;

    (*memDataStat)->totalPageDescCount = 0;
    (*memDataStat)->pageDescCountUsed = 0;
    (*memDataStat)->extentArrayCount = 0;
    (*memDataStat)->extentArrayCountUsed = 0;
    (*memDataStat)->eachExtPageDescCount = 0;

    return GMERR_OK;
}

void MdSpaceMgrGetLowestFreeRateDevId(SpaceMgrT *spaceMgr, uint32_t spaceId, uint32_t *devId)
{
    MdSpaceT *space = &spaceMgr->spaceArray[spaceId];
    // 走到这里理论上lowestFreeRateDevId已经被赋过值
    // MdSpaceAllocChunk 可能重置为 SE_INVALID_DEVICE_ID
    *devId = space->lowestFreeRateDevId;  // 当前表持有的页中最空闲的deviceID,初始值为最满的
}

void MdSpaceMgrDestroy(SeInstanceT *seIns, DbMemCtxT *topMemCtx)
{
    DB_POINTER2(seIns, topMemCtx);
    SpaceMgrT *spaceMgr = (SpaceMgrT *)DbShmPtrToAddr(seIns->spaceMgrShm);
    if (spaceMgr == NULL) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get spaceMgr(%" PRIu32 " %" PRIu32 ") unsucc when operating tableSpace",
            seIns->spaceMgrShm.segId, seIns->spaceMgrShm.offset);
        DB_ASSERT(0);
        return;
    }
    SpaceMgrLock(spaceMgr);
    for (uint32_t i = 0; i < DB_TABLE_SPACE_MAX_NUM; ++i) {
        if (spaceMgr->spaceArray[i].inUse) {
            MdSpaceFreeAllDevice(spaceMgr, &spaceMgr->spaceArray[i], seIns->dbInstance);
            spaceMgr->spaceArray[i].inUse = false;
        }
    }
    SpaceMgrUnlock(spaceMgr);
    DbShmemCtxFree(topMemCtx, seIns->spaceMgrShm);
    seIns->spaceMgrShm = DB_INVALID_SHMPTR;
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
static StatusInter MdSpaceFindTargetSpace(SpaceMgrT *spaceMgr, uint32_t tableSpaceId, MdSpaceT **space)
{
    DB_POINTER2(spaceMgr, space);
    uint32_t tspIdx;
    for (tspIdx = 0; tspIdx < DB_TABLE_SPACE_MAX_NUM; ++tspIdx) {
        if (spaceMgr->spaceArray[tspIdx].tableSpaceId == tableSpaceId && spaceMgr->spaceArray[tspIdx].inUse == true) {
            *space = &spaceMgr->spaceArray[tspIdx];
            return STATUS_OK_INTER;
        }
    }
    // 有可能是在rsm-space中，先不打印错误日志
    return INVALID_PARAMETER_VALUE_INTER;
}

StatusInter MdSpaceDropTableSpace(SeInstanceT *seIns, uint32_t tableSpaceId)
{
    DB_POINTER(seIns);
    SpaceMgrT *spaceMgr = (SpaceMgrT *)DbShmPtrToAddr(seIns->spaceMgrShm);
    if (spaceMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "Get spaceMgr unsucc(%" PRIu32 " %" PRIu32 ") when operating tableSpace", seIns->spaceMgrShm.segId,
            seIns->spaceMgrShm.offset);
        DB_ASSERT(0);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(spaceMgr->devMgrShm);
    if (devMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get devMgr unsucc(%" PRIu32 " %" PRIu32 ") when alloc chunk",
            spaceMgr->devMgrShm.segId, spaceMgr->devMgrShm.offset);
        DB_ASSERT(false);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    SpaceMgrLock(spaceMgr);
    MdSpaceT *space = NULL;
    StatusInter ret = MdSpaceFindTargetSpace(spaceMgr, tableSpaceId, &space);
    if (ret != STATUS_OK_INTER) {
        SpaceMgrUnlock(spaceMgr);
        if (seIns->seConfig.isUseRsm) {  // 如果有保留内存，就再去保留内存找找
            return MdRsmSpaceDropTableSpace(seIns, tableSpaceId);
        }
        return ret;
    }

    uint32_t curDevId = space->firstDevId;
    while (curDevId != SE_INVALID_DEVICE_ID) {
        DevNodeT *devDesc = DevNodeGetById(devMgr, curDevId);
        if (devDesc->freeChunkCount != spaceMgr->chunkCntPerDev) {
            SpaceMgrUnlock(spaceMgr);
            SE_LAST_ERROR(
                OBJECT_NOT_IN_PREREQUISITE_STATE_INTER, "tableSpaceId %" PRIu32 " contains in-use page.", tableSpaceId);
            return OBJECT_NOT_IN_PREREQUISITE_STATE_INTER;
        }
        curDevId = devDesc->nextDevId;
    }
    MdSpaceFreeAllDevice(spaceMgr, space, seIns->dbInstance);
    space->inUse = false;
    spaceMgr->curUserSpaceNum--;
    SpaceMgrUnlock(spaceMgr);

    return STATUS_OK_INTER;
}

Status SeCreateUserTableSpace(uint16_t seInstanceId, TableSpaceCfgT *cfg, uint32_t *tableSpaceIndex)
{
    DB_POINTER(cfg);

    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(seInstanceId);
    if (SECUREC_UNLIKELY(seIns == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Storage instance %" PRIu16 " inv", seInstanceId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t tspId = cfg->tableSpaceId;
    if (SECUREC_UNLIKELY(tspId == DB_INVALID_TABLE_SPACE_ID || tspId == DB_DEFAULT_TABLE_SPACE_ID)) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "inv tableSpaceId %" PRIu32, tspId);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (SECUREC_UNLIKELY(cfg->maxMemSize == DB_UNLIMIT_SPACE_SIZE)) {
        if (tspId != DB_UNDO_TABLE_SPACE_ID) {  // 只有undo才能允许和default-tsp共享使用上限
            SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "inv tableSpace maxSize %" PRIu16, cfg->maxMemSize);
            return GMERR_INVALID_PARAMETER_VALUE;
        }
    }

    return DbGetExternalErrno(MdSpaceCreateTableSpace(seIns, cfg, tableSpaceIndex));
}

Status SeDestroyTableSpaceRecoveryMgr(uint16_t seInstanceId)
{
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(seInstanceId);
    if (SECUREC_UNLIKELY(seIns == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Storage instance %" PRIu16 " inv", seInstanceId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return DbGetExternalErrno(RsmSpaceDestoryRecoveryMgr(seIns));
}

Status SeDropUserTableSpace(uint16_t seInstanceId, uint32_t tableSpaceId)
{
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(seInstanceId);
    if (seIns == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Storage instance %" PRIu16 " inv", seInstanceId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    return DbGetExternalErrno(MdSpaceDropTableSpace(seIns, tableSpaceId));
}

StatusInter MdCompressSpace(MdMgrT *mgr, TspCompressCursorT *cursor, uint32_t labelId)
{
    DB_POINTER(mgr);
    TspCompressResE result = TSP_COMPRESS_RES_END;
    StatusInter ret = MdRsmCompressTableSpace(mgr->rsmSpaceMgr, cursor->spaceId, cursor->rsmBlockId, labelId, &result);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    if (result == TSP_COMPRESS_NOT_SATISFY) {  // 其他情况，维测rsmBlockId不变
        cursor->rsmBlockId++;
    }
    return STATUS_OK_INTER;
}

Status SeCompressSpace(uint16_t seInstanceId, TspCompressCursorT *cursor, uint32_t labelId)
{
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(seInstanceId);
    if (seIns == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Storage instance %" PRIu16 " inv", seInstanceId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return DbGetExternalErrno(MdCompressSpace(seIns->mdMgr, cursor, labelId));
}

Status SeCompressSpaceCursor(TspCompressCursorT *cursor)
{
    return DbGetExternalErrno(MdRsmCompressTableSpaceCursor(cursor));
}

Status SeCompressSpaceCheck(uint16_t seInstanceId, TspCompressCursorT *cursor, uint32_t *labelId)
{
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(seInstanceId);
    if (seIns == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Storage instance %" PRIu16 " inv", seInstanceId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return DbGetExternalErrno(MdRsmCompressTableSpaceCheck(((MdMgrT *)seIns->mdMgr)->rsmSpaceMgr, cursor, labelId));
}

void SeGetSingleDefAndUndoTspInfo(
    const SeInstanceT *seIns, SpaceMgrT *spaceMgr, TspStatInfoT *tspInfo, uint32_t remainedChunkCount)
{
    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(spaceMgr->devMgrShm);
    if (devMgr == NULL) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get devMgr(%" PRIu32 " %" PRIu32 ") unsucc when get spaceInfo",
            spaceMgr->devMgrShm.segId, spaceMgr->devMgrShm.offset);
        DB_ASSERT(false);
        return;
    }
    MdSpaceT *defaultSpace = &spaceMgr->spaceArray[DB_DEFAULT_TABLE_SPACE_INDEX];
    MdSpaceT *undoSpace = &spaceMgr->spaceArray[DB_UNDO_TABLE_SPACE_INDEX];
    tspInfo->isFree = false;
    tspInfo->isUseRsm = false;
    tspInfo->compressCnt = 0;
    tspInfo->freeDeviceCnt = 0;
    tspInfo->moveRsmSubBlockCnt = 0;

    uint32_t deviceSizeInMB = seIns->seConfig.deviceSize / DB_KIBI;
    tspInfo->initSize = (uint16_t)(defaultSpace->protectDevSize * deviceSizeInMB);
    tspInfo->stepSize = (uint16_t)(defaultSpace->stepDevSize * deviceSizeInMB);
    uint32_t unsignedDevCount = DevGetFreeDeviceCount(devMgr);
    uint32_t devMaxCount = DevGetMaxCount(devMgr);
    uint32_t remainSeMemSize = (seIns->seConfig.maxSeMem - devMaxCount * seIns->seConfig.deviceSize) / DB_KIBI;
    uint32_t totalDevCount = undoSpace->curDevSize + defaultSpace->curDevSize + unsignedDevCount;
    tspInfo->maxSize = (uint16_t)(totalDevCount * deviceSizeInMB + remainSeMemSize);

    uint32_t unsignedChunkCount = unsignedDevCount * spaceMgr->chunkCntPerDev;
    uint32_t totalChunkCount = remainedChunkCount + unsignedChunkCount;
    uint32_t freeChunkCount = unsignedChunkCount;
    MdSpaceGetSpaceTotalChunkCount(spaceMgr, defaultSpace, &totalChunkCount, &freeChunkCount);
    MdSpaceGetSpaceTotalChunkCount(spaceMgr, undoSpace, &totalChunkCount, &freeChunkCount);
    uint32_t usedChunkCount = totalChunkCount - (freeChunkCount + remainedChunkCount);
    tspInfo->usedSize = usedChunkCount * spaceMgr->chunkSize;
    tspInfo->usedRatio = (double)usedChunkCount / totalChunkCount;
}

void SeGetSingleUsrTspInfo(const SeInstanceT *seIns, SpaceMgrT *spaceMgr, TspStatInfoT *tspInfo, uint32_t usrNum)
{
    MdSpaceT *space = &spaceMgr->spaceArray[usrNum];
    tspInfo->isFree = false;
    tspInfo->initSize = (uint16_t)(space->protectDevSize * seIns->seConfig.deviceSize / DB_KIBI);
    tspInfo->stepSize = (uint16_t)(space->stepDevSize * seIns->seConfig.deviceSize / DB_KIBI);
    tspInfo->maxSize = (uint16_t)(space->maxDevSize * seIns->seConfig.deviceSize / DB_KIBI);
    uint32_t totalChunkCount = 0;
    uint32_t freeChunkCount = 0;
    MdSpaceGetSpaceTotalChunkCount(spaceMgr, space, &totalChunkCount, &freeChunkCount);
    tspInfo->usedSize = (totalChunkCount - freeChunkCount) * spaceMgr->chunkSize;
    // tspChunkCount 包含已分配+未分配（提前占住位置的）
    uint32_t tspChunkCount = spaceMgr->chunkCntPerDev * space->maxDevSize;
    DB_ASSERT(tspChunkCount != 0);
    tspInfo->usedRatio = (double)(totalChunkCount - freeChunkCount) / tspChunkCount;
}

StatusInter SeGetSingleTspInfo(
    SeInstanceT *seIns, SpaceMgrT *spaceMgr, TspStatInfoT *tspInfo, uint32_t remainedChunkCount)
{
    if (tspInfo->tableSpaceId == DB_DEFAULT_TABLE_SPACE_ID) {
        SeGetSingleDefAndUndoTspInfo(seIns, spaceMgr, tspInfo, remainedChunkCount);
        return STATUS_OK_INTER;
    }

    for (uint32_t i = 2; i < DB_TABLE_SPACE_MAX_NUM; i++) {
        if (tspInfo->tableSpaceId == spaceMgr->spaceArray[i].tableSpaceId) {
            SeGetSingleUsrTspInfo(seIns, spaceMgr, tspInfo, i);
            tspInfo->isUseRsm = false;
            tspInfo->compressCnt = 0;
            tspInfo->freeDeviceCnt = 0;
            tspInfo->moveRsmSubBlockCnt = 0;
            return STATUS_OK_INTER;
        }
    }
    // 有可能是在rsm-space中，先不打印错误日志
    return INVALID_PARAMETER_VALUE_INTER;
}

Status SeGetStatTspInfo(SeRunCtxHdT seRunCtx, TspStatInfoT *tspInfo)
{
    DB_POINTER(seRunCtx);
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    if (seIns == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "inv SE instance %" PRIu16 "", seRunCtx->instanceId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    uint32_t remainedChunkCount = 0;
    if (tspInfo->tableSpaceId == DB_DEFAULT_TABLE_SPACE_ID &&
        ((seIns->seConfig.maxSeMem % seIns->seConfig.deviceSize) != 0)) {
        uint32_t remainedDevSize = (uint32_t)SIZE_M(seIns->seConfig.maxSeMem % seIns->seConfig.deviceSize);
        remainedChunkCount = remainedDevSize / SIZE_K(seIns->seConfig.pageSize);
    }

    SpaceMgrT *spaceMgr = (SpaceMgrT *)DbShmPtrToAddr(seIns->spaceMgrShm);
    if (spaceMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "Get spaceMgr(%" PRIu32 " %" PRIu32 ") unsucc when operating tableSpace", seIns->spaceMgrShm.segId,
            seIns->spaceMgrShm.offset);
        DB_ASSERT(0);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    SpaceMgrLock(spaceMgr);
    StatusInter ret = SeGetSingleTspInfo(seIns, spaceMgr, tspInfo, remainedChunkCount);
    SpaceMgrUnlock(spaceMgr);
    if (ret != STATUS_OK_INTER) {
        RsmSpaceMgrT *rsmSpaceMgr = (RsmSpaceMgrT *)DbShmPtrToAddr(seIns->rsmSpaceMgrShm);
        if (rsmSpaceMgr == NULL) {  // 传入了一个错误的tableSpaceId，rsm-spaceMgr可能还没初始化
            SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER,
                "Get spaceMgr(%" PRIu32 " %" PRIu32 ") unsucc when operating tableSpace", seIns->rsmSpaceMgrShm.segId,
                seIns->rsmSpaceMgrShm.offset);
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        SeGetSingleRsmTspInfo(seIns, rsmSpaceMgr, tspInfo);
    }
    return GMERR_OK;
}

Status SeGetUserDataAlmRatio(DbMemCtxT *memCtx, double *usedRatio)
{
    DB_POINTER(usedRatio);
    // 多实例场景不开启
    if (DbIsMultiInstanceEnabled()) {
        return GMERR_OK;
    }
    // memCtx可能为NULL，此处使用全局实例
    uint16_t instanceId = DbGetProcGlobalId();
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(instanceId);
    if (seIns == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "inv SE instance %" PRIu16 "", instanceId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    uint32_t pageSize = seIns->seConfig.pageSize * DB_KIBI;
    uint64_t maxMemSize = (uint64_t)seIns->seConfig.maxSeMem * DB_KIBI;

    SpaceMgrT *spaceMgr = (SpaceMgrT *)DbShmPtrToAddr(seIns->spaceMgrShm);
    if (spaceMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "Get spaceMgr(%" PRIu32 " %" PRIu32 ") unsucc when operating tableSpace", seIns->spaceMgrShm.segId,
            seIns->spaceMgrShm.offset);
        DB_ASSERT(0);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    SpaceMgrLock(spaceMgr);
    uint32_t usedChunkNum = MdSpaceGetUsedChunkCount(spaceMgr);
    DB_ASSERT(maxMemSize != 0);
    *usedRatio = (double)((double)usedChunkNum * pageSize / (double)maxMemSize);
    SpaceMgrUnlock(spaceMgr);

    DbAlarmHeadT *alarmHead = DbGetAlarmHead(DB_ALARM_VERTEX_EDGE_DATA);
    if (alarmHead != NULL && *usedRatio > alarmHead->alarmCfg->cfg.threshold.raisePercent) {
        DbAlarmStatT alarmStat = DbGetAlarmStat(DB_ALARM_VERTEX_EDGE_DATA);
        DB_LOG_WARN(GMERR_INSUFFICIENT_RESOURCES,
            "(Alarm active) vertex-edge data alarm is active, ratio %.2f, usedSize: %" PRIu64 ", maxSize: %" PRIu64
            ", succTimes %" PRIu64 ", unsuccTimes %" PRIu64 ".",
            *usedRatio, (uint64_t)usedChunkNum * pageSize, maxMemSize, alarmStat.succTimes, alarmStat.failTimes);
    }
    return GMERR_OK;
}

// 任意一个tsp超过内存阈值，就会发出alarm，再配合视图查询具体信息
// 因此这个函数只需找到利用率最高的那个tsp，返回其利用率
// 由上层函数负责加解锁
double SeGetHighestUsedRatioTsp(SpaceMgrT *spaceMgr, uint32_t remainedChunkCount)
{
    DB_POINTER(spaceMgr);

    double highestUsedRatio = 0;
    DbAlarmHeadT *alarmHead = DbGetAlarmHead(DB_ALARM_TABLE_SPACE_USED_INFO);
    if (alarmHead == NULL) {
        DB_LOG_WARN(GMERR_NULL_VALUE_NOT_ALLOWED, "SeGetHighestUsedRatioTsp: alarmHead is null.");
        return highestUsedRatio;
    }
    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(spaceMgr->devMgrShm);
    if (devMgr == NULL) {
        DB_LOG_WARN(GMERR_NULL_VALUE_NOT_ALLOWED, "Get deviceMgr(%" PRIu32 " %" PRIu32 ") unsucc",
            spaceMgr->devMgrShm.segId, spaceMgr->devMgrShm.offset);
        return highestUsedRatio;
    }
    for (uint32_t i = 0; i < DB_TABLE_SPACE_MAX_NUM; i++) {
        if (spaceMgr->spaceArray[i].inUse && i != DB_UNDO_TABLE_SPACE_INDEX) {
            MdSpaceT *space = &spaceMgr->spaceArray[i];
            // tspChunkCount 包含已分配+未分配（提前占住位置的）
            double tmpValue = 0;
            uint32_t tspChunkCount = 0;      // tsp总的chunk数量
            uint32_t usedChunkCount = 0;     // tsp正在使用的chunk数量
            uint32_t tspFreeChunkCount = 0;  // tsp未使用的chunk数量
            uint32_t totalTspChunkCount = 0;  // public-tsp需要额外算上undo+无法利用的空间大小，其余tsp等于tspChunkCount
            MdSpaceGetSpaceTotalChunkCount(spaceMgr, space, &tspChunkCount, &tspFreeChunkCount);
            if (i == DB_DEFAULT_TABLE_SPACE_INDEX) {
                // 提前占住位置的 + undoSpace + remainedChunkCount
                uint32_t unsignedChunkCount = DevGetFreeDeviceCount(devMgr) * spaceMgr->chunkCntPerDev;
                MdSpaceT *undoSpace = &spaceMgr->spaceArray[DB_UNDO_TABLE_SPACE_INDEX];
                MdSpaceGetSpaceTotalChunkCount(spaceMgr, undoSpace, &tspChunkCount, &tspFreeChunkCount);
                usedChunkCount = tspChunkCount - tspFreeChunkCount;
                totalTspChunkCount = tspChunkCount + remainedChunkCount + unsignedChunkCount;
            } else {
                usedChunkCount = tspChunkCount - tspFreeChunkCount;
                totalTspChunkCount = tspChunkCount;
            }
            tmpValue = (double)usedChunkCount / totalTspChunkCount;
            if (tmpValue > highestUsedRatio) {
                highestUsedRatio = tmpValue;
            }
            if (tmpValue > alarmHead->alarmCfg->cfg.threshold.raisePercent) {
                // 任意一个tsp达到阈值都会触发warn，因此此处打印满足条件的tsp信息
                DbAlarmStatT alarmStat = DbGetAlarmStat(DB_ALARM_TABLE_SPACE_USED_INFO);
                DB_LOG_WARN(GMERR_INSUFFICIENT_RESOURCES,
                    "TableSpacealarm active, ratio %.2f, tableSpaceId: %" PRIu32 ", usedChunkCnt: %" PRIu32
                    ", maxChunkCnt: %" PRIu32 ", succTimes %" PRIu64 ", unsuccTimes %" PRIu64,
                    tmpValue, space->tableSpaceId, usedChunkCount, totalTspChunkCount, alarmStat.succTimes,
                    alarmStat.failTimes);
            }
        }
    }
    return highestUsedRatio;
}

// se alarm项刷新上个周期内的SuccCnt
void DbSeAlarmUpdateLastSuccCnt(uint32_t succCnt)
{
    (void)DbAtomicSet64(&g_gmdbSeAlarmStat.succTimes, succCnt);
}

// se alarm项刷新上个周期内的FailCnt
void DbSeAlarmUpdateLastFailCnt(uint32_t failedCnt)
{
    (void)DbAtomicSet64(&g_gmdbSeAlarmStat.failTimes, failedCnt);
}

Status SeGetStorageFileAlmRatio(DbMemCtxT *memCtx, double *usedRatio)
{
    DbAlarmHeadT *alarmHead = DbGetAlarmHead(DB_ALARM_STORAGE_FILE);
    DbAlarmInfoNodeT *alarmInfoNode = alarmHead->next;
    *usedRatio = alarmInfoNode->alarmInfo.alarmData.curVal;
    if (*usedRatio >= alarmHead->alarmCfg->cfg.threshold.raisePercent) {
        DbAlarmStatT alarmStat = DbGetAlarmStat(DB_ALARM_STORAGE_FILE);
        DB_LOG_WARN(GMERR_FILE_OPERATE_FAILED,
            "Storage space usage alarm active, ratio %.2f, succTimes %" PRIu64 ", failTimes %" PRIu64 ".", *usedRatio,
            alarmStat.succTimes, alarmStat.failTimes);
    }
    return GMERR_OK;
}

Status SeGetStorageSpaceAlmRatio(DbMemCtxT *memCtx, double *usedRatio)
{
    DbAlarmHeadT *alarmHead = DbGetAlarmHead(DB_ALARM_STORAGE_SPACE_USED_INFO);
    *usedRatio = alarmHead->next->alarmInfo.alarmData.curVal;
    return GMERR_OK;
}

Status SeGetStorageSpaceAlmRatioByPeriod(DbMemCtxT *memCtx, double *usedRatio)
{
    DbAlarmStatT alarmStat = DbGetAlarmStat(DB_ALARM_STORAGE_SPACE_USED_INFO);
    uint64_t succTimes = alarmStat.succTimes - g_gmdbSeAlarmStat.succTimes;
    uint64_t failTimes = alarmStat.failTimes - g_gmdbSeAlarmStat.failTimes;
    uint64_t totalTimes = succTimes + failTimes;
    if (totalTimes != 0) {
        *usedRatio = (double)failTimes / totalTimes;
    } else {
        *usedRatio = 0;
    }
    DbAlarmHeadT *alarmHead = DbGetAlarmHead(DB_ALARM_STORAGE_SPACE_USED_INFO);

    if (*usedRatio >= alarmHead->alarmCfg->cfg.threshold.raisePercent) {
        DB_LOG_WARN(GMERR_FILE_OPERATE_FAILED,
            "Storage space usage alarm active, ratio %.2f, succTimes %" PRIu64 ", failTimes %" PRIu64 ".", *usedRatio,
            alarmStat.succTimes, alarmStat.failTimes);
    }
    return GMERR_OK;
}

Status SeGetUserTableSpaceAlmRatio(DbMemCtxT *memCtx, double *usedRatio)
{
    // 此处memCtx为空
    DB_POINTER(usedRatio);
    // 多实例场景不开启
    if (DbIsMultiInstanceEnabled()) {
        return GMERR_OK;
    }
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(DbGetProcGlobalId());
    if (seIns == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "SeGetInstance unsucc");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    SpaceMgrT *spaceMgr = (SpaceMgrT *)DbShmPtrToAddr(seIns->spaceMgrShm);
    if (spaceMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get spaceMgr(%" PRIu32 " %" PRIu32 ") unsucc",
            seIns->spaceMgrShm.segId, seIns->spaceMgrShm.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t remainedChunkCount = 0;
    if ((seIns->seConfig.maxSeMem % seIns->seConfig.deviceSize) != 0) {
        uint32_t remainedDevSize = (uint32_t)SIZE_M(seIns->seConfig.maxSeMem % seIns->seConfig.deviceSize);
        remainedChunkCount = remainedDevSize / seIns->seConfig.pageSize;
    }

    SpaceMgrLock(spaceMgr);
    *usedRatio = SeGetHighestUsedRatioTsp(spaceMgr, remainedChunkCount);
    SpaceMgrUnlock(spaceMgr);

    return GMERR_OK;
}

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
Status SeGetRsmUserTableSpaceAlmRatio(DbMemCtxT *memCtx, double *usedRatio)
{
    DB_POINTER(usedRatio);
    // 多实例场景不开启
    if (DbIsMultiInstanceEnabled()) {
        return GMERR_OK;
    }
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(DbGetProcGlobalId());
    if (seIns == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "SeGetInstance unsucc");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (!seIns->seConfig.isUseRsm) {
        *usedRatio = 0;
        return GMERR_OK;
    }
    RsmSpaceMgrT *rsmSpaceMgr = (RsmSpaceMgrT *)DbShmPtrToAddr(seIns->rsmSpaceMgrShm);
    if (rsmSpaceMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get rsmSpaceMgr(%" PRIu32 " %" PRIu32 ") unsucc",
            seIns->rsmSpaceMgrShm.segId, seIns->rsmSpaceMgrShm.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    double allTspFreeRatio = 0;
    double highestUsedRatio = 0;
    SeGetHighestUsedRatioRsmTspAndTotalInfo(seIns, rsmSpaceMgr, &allTspFreeRatio, &highestUsedRatio);
    uint32_t allTspFreeRate = allTspFreeRatio * DB_PERCENTAGE_BASE;
    if (seIns->seConfig.enableReleaseDevice && allTspFreeRate >= seIns->seConfig.rsmDfgmtRateThreshold) {
        DfgmtTaskParaT para = {0};
        (void)DbCreateDfgmtTask(MEM_COMPACT_RSM_PAGE, &para);  // 该任务类型是全局的，重复创建会报错，直接忽略错误码
        DB_LOG_INFO("(Defragmentation) create rsm page dfgmt, ratio:%.2f", allTspFreeRatio);
    }
    *usedRatio = highestUsedRatio;
    return GMERR_OK;
}

Status SeGetRsmBlockStat(
    SeRunCtxHdT seRunCtx, uint32_t rsmBlockId, DbMemCtxT *dynamicMemCtx, RsmBlockInfoT **rsmBlockStat)
{
    SeRunCtxT *statRunCtx = seRunCtx;
    SeInstanceT *seIns = (SeInstanceT *)statRunCtx->seIns;

    RsmBlkMgrHdlT rsmBlkMgrHdl = (RsmBlkMgrHdlT)DbShmPtrToAddr(seIns->rsmBlockMgrShm);
    if (rsmBlkMgrHdl == NULL) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get rsmBlkMgr(%" PRIu32 " %" PRIu32 ") unsucc when get rsm stat",
            seIns->rsmBlockMgrShm.segId, seIns->rsmBlockMgrShm.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return DbGetExternalErrno(RsmBlockGetStat(rsmBlkMgrHdl, rsmBlockId, dynamicMemCtx, rsmBlockStat));
}

Status SeRsmMigration(uint16_t seInstanceId, const char *path)
{
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(seInstanceId);
    if (seIns == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "SE instance %" PRIu16 ".", seInstanceId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    RsmBlkMgrHdlT rsmBlkMgrHdl = (RsmBlkMgrHdlT)DbShmPtrToAddr(seIns->rsmBlockMgrShm);
    if (rsmBlkMgrHdl == NULL) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get rsmBlkMgr(%" PRIu32 " %" PRIu32 ") unsucc when rsm migration",
            seIns->rsmBlockMgrShm.segId, seIns->rsmBlockMgrShm.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    Status ret = RsmBlockMigration(rsmBlkMgrHdl, path);
    if (ret != GMERR_OK) {
        return ret;
    }
    seIns->rsmMigrationVersion++;
    return ret;
}
#endif

uint64_t SeGetUsedPageMemSize(void *seInstance)
{
    DB_POINTER(seInstance);
    SeInstanceT *seIns = (SeInstanceT *)seInstance;
    SpaceMgrT *spaceMgr = (SpaceMgrT *)DbShmPtrToAddr(seIns->spaceMgrShm);
    if (spaceMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "Get spaceMgr(%" PRIu32 " %" PRIu32 ") unsucc when getting used page memSize", seIns->spaceMgrShm.segId,
            seIns->spaceMgrShm.offset);
        DB_ASSERT(false);
        return 0;
    }

    SpaceMgrLock(spaceMgr);
    uint64_t shmMemUseSize = (uint64_t)(seIns->seConfig.pageSize) * MdSpaceGetUsedChunkCount(spaceMgr);
    SpaceMgrUnlock(spaceMgr);

    return shmMemUseSize;
}

SeHpTupleAddrMode SeGetAddrModeWithSeIns(const SeInstanceT *seIns)
{
    DB_POINTER(seIns);
    return seIns->seConfig.heapTupleAddrMode;
}
#endif /* FEATURE_SIMPLEREL */

void MdSpaceCacheListAdd(LabelCachePageListT *list, PageHeadT *page, PageIdT head)
{
    DB_POINTER2(list, page);
    if (list->listCnt == 0) {
        DB_ASSERT(!DbIsPageIdValid(list->head));
    }

    page->nextPageId = list->head;
    list->head = head;
    list->listCnt++;
    return;
}

void MdFreeChunkToCacheList(
    MdSpaceT *space, FreePageParamT *freePageParam, uint8_t *page, uint32_t spaceId, bool *addTask)
{
    DB_POINTER2(space, freePageParam);

    // 缓存页链表以label为粒度进行管理,因此labelId必须有效
    uint32_t labelId = freePageParam->cachePagePara.labelId;
    DB_ASSERT(labelId != SE_INVALID_LABEL_ID);

    DbShmArrayT *shmArray = DbShmPtrToAddr(space->cachedListsShm);
    if (shmArray == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get md space list shm, (segid:%" PRIu32 " offset:%" PRIu32 ")",
            space->cachedListsShm.segId, space->cachedListsShm.offset);
        DB_ASSERT(false);
        return;
    }

    DbShmArrayIteratorT iter;
    LabelCachePageListT *cachePageList = NULL;
    for (DbShmArrayInitIterator(&iter, shmArray); DbShmArrayIteratorValid(&iter); DbShmArrayIteratorNext(&iter)) {
        cachePageList = (LabelCachePageListT *)DbShmArrayIteratorGetItem(&iter);
        if (cachePageList->labelId == labelId) {
            break;
        }
    }

    // 必定存在对应的链表,加入该链表中
    DB_ASSERT((cachePageList != NULL) && (cachePageList->labelId == labelId));
    MdSpaceCacheListAdd(cachePageList, (PageHeadT *)page, freePageParam->addr);
    if (cachePageList->listCnt == 1) {
        *addTask = true;  // 上层添加后台任务防止死锁
    }
    return;
}

void MdFreeChunk(SpaceMgrT *spaceMgr, uint32_t spaceId, FreePageParamT *freePageParam, uint8_t *page, bool needRelease)
{
    DB_POINTER2(spaceMgr, freePageParam);
    SpaceMgrLock(spaceMgr);

    if (!MdSpaceCheckChunkOwnerShip(spaceMgr, &spaceId, freePageParam->addr)) {
        SpaceMgrUnlock(spaceMgr);
        DB_ASSERT(false);  // 这种情况不应发生,如果发生就是代码有问题
        return;
    }

    bool addTask = false;
    MdSpaceT *space = &spaceMgr->spaceArray[spaceId];
    if (freePageParam->cachePagePara.needCache) {
        MdFreeChunkToCacheList(space, freePageParam, page, spaceId, &addTask);
    } else {
        MdFreeChunkToDev(spaceMgr, space, freePageParam, needRelease);
    }

    SpaceMgrUnlock(spaceMgr);
    if (addTask) {
        // 添加后台任务
        SeDfgmtAddTask(freePageParam->cachePagePara.labelId, spaceId, true);
    }
    return;
}
