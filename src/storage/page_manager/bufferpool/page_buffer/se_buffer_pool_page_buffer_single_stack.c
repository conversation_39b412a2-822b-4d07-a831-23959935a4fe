/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: page desc manager
 * Author: wenming
 * Create: 2025-06-03
 */
#include "se_buffer_pool_page_buffer_single_stack.h"

StatusInter PageBufferInitSingleStack(DbMemCtxT *memCtx, const PageBufferMgrCreateCfgT *cfg, uint64_t *mgrAddr)
{
    PageBufferMgrT *newMgr = NULL;
    uint64_t alignedSize = SIZE_K(4) - 1;
    uint64_t allocSize = sizeof(PageBufferMgrT) + (uint64_t)cfg->pageSize * DB_KIBI * cfg->capacity + alignedSize;
    if (cfg->shmem) {
        if (allocSize >= DB_MAX_UINT32) {
            SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "Request size %" PRIu64 " too big for shmem pageBuffer", allocSize);
            return OUT_OF_MEMORY_INTER;
        }
        newMgr = (PageBufferMgrT *)SeShmAlloc(memCtx, (uint32_t)allocSize, (ShmemPtrT *)(void *)mgrAddr);
        if (newMgr == NULL) {
            SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc single stack shmem pageBuffer, size %" PRIu64, allocSize);
            return OUT_OF_MEMORY_INTER;
        }
        newMgr->memCtx = memCtx;
        newMgr->shmem = true;
    } else {
        newMgr = (PageBufferMgrT *)DbDynMemCtxAlloc(memCtx, allocSize);
        if (newMgr == NULL) {
            SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc single stack dynmem pageBuffer, size %" PRIu64, allocSize);
            return OUT_OF_MEMORY_INTER;
        }
        newMgr->memCtx = memCtx;
        newMgr->shmem = false;
        *mgrAddr = (uintptr_t)newMgr;
    }
    newMgr->doubleStack = false;
    newMgr->pageSize = cfg->pageSize;
    newMgr->hwm = cfg->hwm;
    newMgr->segSize = cfg->segSize;
    newMgr->capacity = cfg->capacity;
    uint8_t *basePtr = (uint8_t *)newMgr + sizeof(PageBufferMgrT);
    uint8_t *alignedPtr = (uint8_t *)(uintptr_t)(((uintptr_t)basePtr + alignedSize) & ~(alignedSize));
    newMgr->arrOffset = (uint32_t)((uintptr_t)alignedPtr - (uintptr_t)newMgr);
    return STATUS_OK_INTER;
}
