/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: page desc manager
 * Author: wenming
 * Create: 2025-06-03
 */
#include "se_buffer_pool_page_buffer_double_stack.h"

StatusInter PageBufferInitDoubleStack(DbMemCtxT *memCtx, const PageBufferMgrCreateCfgT *cfg, uint64_t *mgrAddr)
{
    PageBufferMgrT *newMgr = NULL;
    uint32_t allocSize = (uint32_t)sizeof(PageBufferMgrT);
    if (cfg->shmem) {
        allocSize += (uint32_t)sizeof(ShmemPtrT) * PageBufferGetTopStackArrSize(cfg->capacity, cfg->segSize);
        newMgr = (PageBufferMgrT *)SeShmAlloc(memCtx, allocSize, (ShmemPtrT *)(void *)mgrAddr);
        if (newMgr == NULL) {
            SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc double stack shmem pageBuffer, size %" PRIu32, allocSize);
            return OUT_OF_MEMORY_INTER;
        }
        newMgr->memCtx = memCtx;
        newMgr->shmem = true;
    } else {
        allocSize += (uint32_t)sizeof(uint8_t **) * PageBufferGetTopStackArrSize(cfg->capacity, cfg->segSize);
        newMgr = (PageBufferMgrT *)DbDynMemCtxAlloc(memCtx, allocSize);
        if (newMgr == NULL) {
            SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc double stack dynmem pageBuffer, size %" PRIu32, allocSize);
            return OUT_OF_MEMORY_INTER;
        }
        newMgr->memCtx = memCtx;
        newMgr->shmem = false;
        *mgrAddr = (uintptr_t)newMgr;
    }
    newMgr->doubleStack = true;
    newMgr->pageSize = cfg->pageSize;
    newMgr->hwm = cfg->hwm;
    newMgr->segSize = cfg->segSize;
    newMgr->capacity = cfg->capacity;
    newMgr->arrOffset = sizeof(PageBufferMgrT);
    return STATUS_OK_INTER;
}

void PageBufferDestroyDoubleStack(SeInstanceT *seIns, PageBufferMgrT *bufMgr)
{
    uint32_t segHwm = PageBufferGetTopStackArrSize(bufMgr->hwm, bufMgr->segSize);
    if (bufMgr->shmem) {
        ShmemPtrT *cursor = (ShmemPtrT *)((uint8_t *)bufMgr + bufMgr->arrOffset);
        for (uint32_t i = 0; i < segHwm; ++i) {
            if (DbIsShmPtrValid(cursor[i])) {
                DbShmemCtxFree(bufMgr->memCtx, cursor[i]);
                cursor[i] = DB_INVALID_SHMPTR;
            }
        }
    } else {
        uint8_t **cursor = (uint8_t **)((uint8_t *)bufMgr + bufMgr->arrOffset);
        for (uint32_t i = 0; i < segHwm; ++i) {
            if (cursor[i]) {
                DbDynMemCtxFree(bufMgr->memCtx, cursor[i]);
                cursor[i] = NULL;
            }
        }
    }
}

StatusInter PageBufferAllocDoubleStack(PageBufferMgrT *bufMgr, uint32_t *index)
{
    uint32_t segmentIndex = bufMgr->hwm / bufMgr->segSize;
    if (bufMgr->hwm % bufMgr->segSize == 0) {
        uint64_t segmentSize = (uint64_t)bufMgr->pageSize * (uint64_t)DB_KIBI * (uint64_t)bufMgr->segSize;
        if (bufMgr->shmem) {
            ShmemPtrT *segmentPtr = (ShmemPtrT *)((uint8_t *)bufMgr + bufMgr->arrOffset);
            segmentPtr[segmentIndex] = DbShmemCtxAlloc(bufMgr->memCtx, segmentSize);
            if (!DbIsShmPtrValid(segmentPtr[segmentIndex])) {
                SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc double stack shmem pageBuffer %" PRIu32 ", size %" PRIu64,
                    segmentIndex, segmentSize);
                return OUT_OF_MEMORY_INTER;
            }
        } else {
            uint8_t **segmentPtr = (uint8_t **)((uint8_t *)bufMgr + bufMgr->arrOffset);
            segmentPtr[segmentIndex] = (uint8_t *)DbDynMemCtxAlloc(bufMgr->memCtx, segmentSize);
            if (segmentPtr[segmentIndex] == NULL) {
                SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc double stack dynmem pageBuffer %" PRIu32 ", size %" PRIu64,
                    segmentIndex, segmentSize);
                return OUT_OF_MEMORY_INTER;
            }
        }
    }
    *index = bufMgr->hwm++;
    return STATUS_OK_INTER;
}

uint32_t PageBufferGetIndexDoubleStack(PageBufferMgrT *bufMgr, void *ptr)
{
    uint32_t cursor = 0;
    void *start = NULL;
    do {
        if (bufMgr->shmem) {
            ShmemPtrT *segmentPtr = (ShmemPtrT *)((uint8_t *)bufMgr + bufMgr->arrOffset);
            start = DbShmPtrToAddr(segmentPtr[cursor]);
        } else {
            uint8_t **segmentPtr = (uint8_t **)((uint8_t *)bufMgr + bufMgr->arrOffset);
            start = segmentPtr[cursor];
        }
        if (start == NULL) {
            break;
        }
        uint8_t *end = start + bufMgr->segSize * SIZE_K(bufMgr->pageSize);
        if (((uintptr_t)ptr >= (uintptr_t)start) && ((uintptr_t)ptr < (uintptr_t)end)) {
            break;
        }
    } while (cursor * bufMgr->segSize < bufMgr->hwm);
    DB_ASSERT(start != NULL);
    return ((uintptr_t)ptr - (uintptr_t)start) / SIZE_K(bufMgr->pageSize) + cursor * bufMgr->segSize;
}
