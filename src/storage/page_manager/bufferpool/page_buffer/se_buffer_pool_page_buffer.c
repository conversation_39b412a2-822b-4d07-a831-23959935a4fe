/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: buffer pool page buffer
 * Author:
 * Create: 2025-2025
 */
#include "se_buffer_pool_page_buffer.h"
#include "se_buffer_pool_page_buffer_single_stack.h"
#include "se_buffer_pool_page_buffer_double_stack.h"
#include "se_buffer_pool.h"

PageBufferMgrCreateCfgT PageBufferInitDefaultCreateCfg(SeInstanceT *seIns)
{
    PageBufferMgrCreateCfgT result = {0};
    const SeConfigT *seConfig = &seIns->seConfig;
    uint64_t poolSize = SeGetBufpoolCurrentSize(seIns) * SeGetBufpoolCount(seIns);
    result.capacity = poolSize / SIZE_K(seConfig->pageSize);
    result.hwm = 0;
    result.shmem = SeGetPageMgrMemType(seIns);
    if (SeBufpoolResizeEnable(seIns)) {
        result.doubleStack = true;
        result.segSize = seConfig->bpChunkSize / seConfig->pageSize;
        DB_ASSERT(result.capacity >= result.segSize && result.capacity % result.segSize == 0);
    } else if (poolSize >= DB_MAX_UINT32 && result.shmem) {
        result.doubleStack = true;
        result.segSize = seConfig->deviceSize / seConfig->pageSize;
    } else {
        result.segSize = result.capacity;
    }
    result.pageSize = seConfig->pageSize;
    return result;
}

static DbMemCtxT *ChooseMemCtx(SeInstanceT *seIns, PageBufferMgrCreateCfgT *cfg)
{
    if (cfg->shmem) {
        return (DbMemCtxT *)DbGetShmemCtxById(seIns->seTopShmMemCtxId, seIns->instanceId);
    }
    return seIns->seServerMemCtx;
}

StatusInter PageBufferCreate(SeInstanceT *seIns, PageBufferMgrCreateCfgT cfg, uint64_t *mgrAddr)
{
    DbMemCtxT *memCtx = ChooseMemCtx(seIns, &cfg);
    StatusInter ret = STATUS_OK_INTER;
    if (cfg.doubleStack) {
        ret = PageBufferInitDoubleStack(memCtx, &cfg, mgrAddr);
    } else {
        ret = PageBufferInitSingleStack(memCtx, &cfg, mgrAddr);
    }
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    return STATUS_OK_INTER;
}

ALWAYS_INLINE void PageBufferDestroy(SeInstanceT *seIns, uint64_t mgrAddr)
{
    PageBufferMgrT *bufMgr = SeGetPageBufferMgr(seIns, mgrAddr);
    if (bufMgr == NULL) {
        return;
    }
    if (bufMgr->doubleStack) {
        PageBufferDestroyDoubleStack(seIns, bufMgr);
    }
    if (bufMgr->shmem) {
        DbShmemCtxFree(bufMgr->memCtx, *(ShmemPtrT *)(void *)&mgrAddr);
    } else {
        DbDynMemCtxFree(bufMgr->memCtx, (void *)(uintptr_t)mgrAddr);
    }
}

void PageBufferResizeEliminateOverlap(SeInstanceT *seIns, uint64_t maintain, uint64_t eliminate)
{
    PageBufferMgrT *m = SeGetPageBufferMgr(seIns, maintain);
    PageBufferMgrT *e = SeGetPageBufferMgr(seIns, eliminate);
    uint32_t end = DB_MIN(
        PageBufferGetTopStackArrSize(m->capacity, m->segSize), PageBufferGetTopStackArrSize(e->capacity, e->segSize));
    if (m->shmem) {
        ShmemPtrT *arr = (ShmemPtrT *)((uint8_t *)e + e->arrOffset);
        for (uint32_t i = 0; i < end; ++i) {
            arr[i] = DB_INVALID_SHMPTR;
        }
    } else {
        uint8_t **arr = (uint8_t **)((uint8_t *)e + e->arrOffset);
        for (uint32_t i = 0; i < end; ++i) {
            arr[i] = NULL;
        }
    }
}

ALWAYS_INLINE StatusInter PageBufferAlloc(PageBufferMgrT *bufMgr, uint32_t *index)
{
    if (bufMgr->hwm == bufMgr->capacity) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "bufdesc reaches its capacity %" PRIu32, bufMgr->capacity);
        return OUT_OF_MEMORY_INTER;
    }
    if (bufMgr->doubleStack) {
        return PageBufferAllocDoubleStack(bufMgr, index);
    }
    PageBufferAllocSingleStack(bufMgr, index);
    return STATUS_OK_INTER;
}

ALWAYS_INLINE uint32_t PageBufferGetIndex(PageBufferMgrT *bufMgr, void *ptr)
{
    if (bufMgr->doubleStack) {
        return PageBufferGetIndexDoubleStack(bufMgr, ptr);
    }
    uintptr_t start = (uintptr_t)bufMgr + bufMgr->arrOffset;
    uintptr_t limit = start + bufMgr->capacity * SIZE_K(bufMgr->pageSize);
    DB_ASSERT((uintptr_t)ptr < limit);
    return ((uintptr_t)ptr - start) / SIZE_K(bufMgr->pageSize);
}
