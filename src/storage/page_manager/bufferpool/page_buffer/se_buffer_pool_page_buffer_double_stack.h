/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: buffer pool page buffer
 * Author:
 * Create: 2025-2025
 */

#ifndef SE_BUFFER_POOL_PAGE_BUFFER_DOUBLE_STACK_H
#define SE_BUFFER_POOL_PAGE_BUFFER_DOUBLE_STACK_H

#include "se_buffer_pool_page_buffer.h"

#ifdef __cplusplus
extern "C" {
#endif

static inline uint32_t PageBufferGetTopStackArrSize(uint32_t capacity, uint32_t segSize)
{
    uint32_t extra = capacity % segSize == 0 ? 0 : 1;
    return capacity / segSize + extra;
}

StatusInter PageBufferInitDoubleStack(DbMemCtxT *memCtx, const PageBufferMgrCreateCfgT *cfg, uint64_t *mgrAddr);
void PageBufferDestroyDoubleStack(SeInstanceT *seIns, PageBufferMgrT *bufMgr);

StatusInter PageBufferAllocDoubleStack(PageBufferMgrT *bufMgr, uint32_t *index);

uint32_t PageBufferGetIndexDoubleStack(PageBufferMgrT *bufMgr, void *ptr);

StatusInter PageBufferResizeDoubleStack(SeInstanceT *seIns, uint32_t capacity);
#ifdef __cplusplus
}
#endif

#endif
