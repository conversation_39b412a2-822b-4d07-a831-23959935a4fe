/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: buffer pool page buffer
 * Author:
 * Create: 2025-2025
 */

#ifndef SE_BUFFER_POOL_PAGE_BUFFER_SINGLE_STACK_H
#define SE_BUFFER_POOL_PAGE_BUFFER_SINGLE_STACK_H

#include "se_buffer_pool_page_buffer.h"

#ifdef __cplusplus
extern "C" {
#endif

StatusInter PageBufferInitSingleStack(DbMemCtxT *memCtx, const PageBufferMgrCreateCfgT *cfg, uint64_t *mgrAddr);

static inline void PageBufferAllocSingleStack(PageBufferMgrT *bufMgr, uint32_t *index)
{
    *index = bufMgr->hwm++;
}

#ifdef __cplusplus
}
#endif

#endif
