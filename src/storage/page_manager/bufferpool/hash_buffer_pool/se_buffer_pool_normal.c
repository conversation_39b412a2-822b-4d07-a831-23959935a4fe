/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: buffer pool normal lru list, enqueue, recycle
 * Author:
 * Create: 2025-02-26
 * Notes:
 */

#include "se_buffer_pool_inner.h"
#include "se_buffer_pool_priority.h"
#include "se_buffer_pool_resize.h"
#include "se_buffer_pool_ringbuffer.h"

#include "se_buffer_pool_bucket.h"
#include "se_buffer_pool_table_recycle.h"

StatusInter TrmIdListMapOamapInit(SeInstanceT *seIns, BufpoolMgrT *mgr)
{
    // 通过开关决定是否开启table load功能（可以和bufferpool淘汰策略配置项绑定）
    DbOamapT *listMap = (DbOamapT *)DbDynMemCtxAlloc(seIns->seServerMemCtx, sizeof(DbOamapT));
    if (SECUREC_UNLIKELY(listMap == NULL)) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "Unable to alloc bufpool table map, size = %zu.", sizeof(DbOamapT));
        return OUT_OF_MEMORY_INTER;
    }

    // 初始化大小和参数capacity有关
    Status ret = DbOamapInit(listMap, DB_TABLE_INIT_NUM, DbOamapPtrCompare, seIns->seServerMemCtx, true);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(seIns->seServerMemCtx, listMap);
        DB_LOG_ERROR(ret, "Unable to init DbOamap when bufferpool init");
        return DbGetStatusInterErrno(ret);
    }
    mgr->trmIdListMap = listMap;
    return STATUS_OK_INTER;
}

void TrmIdListMapOmapDestroy(SeInstanceT *seIns, BufpoolMgrT *mgr)
{
    if (mgr->trmIdListMap == NULL) {
        return;
    }
    DbOamapDestroy(mgr->trmIdListMap);
    DbDynMemCtxFree(seIns->seServerMemCtx, mgr->trmIdListMap);
    mgr->trmIdListMap = NULL;
}

StatusInter BufpoolRegisterRedo(SeInstanceT *seIns)
{
    StatusInter ret = STATUS_OK_INTER;
    if (SeGetPersistMode() == PERSIST_INCREMENT) {
        if (!RedoReplayRegistered(seIns->redoMgr, REDO_INIT_PAGE_HEAD)) {
            ret = RedoLogReplayRegister(seIns->redoMgr, REDO_INIT_PAGE_HEAD, SePageHeadInitReplay);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "init register redoLog replay");
                return ret;
            }
        }

        if (!RedoReplayRegistered(seIns->redoMgr, REDO_RESET_PAGE_HEAD)) {
            ret = RedoLogReplayRegister(seIns->redoMgr, REDO_RESET_PAGE_HEAD, SePageHeadResetReplay);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "reset register redoLog replay");
                return ret;
            }
        }
    }
    return STATUS_OK_INTER;
}
