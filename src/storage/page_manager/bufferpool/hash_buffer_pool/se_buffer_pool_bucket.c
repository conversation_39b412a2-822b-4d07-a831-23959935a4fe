/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: buffer pool bucket
 * Author:
 * Create: 2025-2-26
 */
#include "se_buffer_pool_bucket.h"

static inline uint64_t BufGetBucketMgrSize(uint32_t bucketNum)
{
    return (uint64_t)bucketNum * (uint64_t)sizeof(BufBucketT) + (uint64_t)sizeof(BucketMgrT) + CACHELINE_SIZE;
}

static StatusInter CreateShmemBucketMgr(SeInstanceT *seIns, uint64_t allocSize, BucketMgrT **bucketMgr, uint64_t *addr)
{
    if (allocSize >= DB_MAX_UINT32) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "request bucket size %" PRIu64 " too big!", allocSize);
        return OUT_OF_MEMORY_INTER;
    }
    ShmemPtrT ptr = DB_INVALID_SHMPTR;
    DbMemCtxT *memCtx = DbGetShmemCtxById(seIns->seTopShmMemCtxId, seIns->instanceId);
    ptr = DbShmemCtxAlloc(memCtx, (uint32_t)allocSize);
    *bucketMgr = (BucketMgrT *)DbShmPtrToAddr(ptr);
    if (*bucketMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "fetch bucketmgr {%" PRIu32 ", %" PRIu32 "}", ptr.segId, ptr.offset);
        DbShmemCtxFree(memCtx, ptr);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    *addr = *(uint64_t *)&ptr;
    return STATUS_OK_INTER;
}

static StatusInter CreateDynmemBucketMgr(SeInstanceT *seIns, uint64_t allocSize, BucketMgrT **bucketMgr, uint64_t *addr)
{
    *bucketMgr = (BucketMgrT *)DbDynMemCtxAlloc(seIns->seServerMemCtx, allocSize);
    if (*bucketMgr == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc bucketmgr size %" PRIu64, allocSize);
        return OUT_OF_MEMORY_INTER;
    }
    *addr = (uintptr_t)(*bucketMgr);
    return STATUS_OK_INTER;
}

StatusInter BufpoolCreateBucketMgr(SeInstanceT *seIns, uint32_t bucketNum, uint64_t *bucketMgrAddr)
{
    StatusInter ret = STATUS_OK_INTER;
    uint64_t addr = DB_INVALID_UINT64;
    BucketMgrT *bucketMgr = NULL;
    uint64_t allocSize = BufGetBucketMgrSize(bucketNum);
    if (SeBufferPoolIsShmem(seIns)) {
        ret = CreateShmemBucketMgr(seIns, allocSize, &bucketMgr, &addr);
    } else {
        ret = CreateDynmemBucketMgr(seIns, allocSize, &bucketMgr, &addr);
    }
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    (void)memset_s(bucketMgr, allocSize, 0, allocSize);
    bucketMgr->bucketNum = bucketNum;
    uint64_t alignedSize = CACHELINE_SIZE - 1;
    uint8_t *basePtr = (uint8_t *)bucketMgr + sizeof(BucketMgrT);
    // bucketNum and buckets are reserved in BucketMgrT
    BufBucketT *alignedPtr = (BufBucketT *)(uintptr_t)(((uintptr_t)basePtr + alignedSize) & ~(alignedSize));
    // bucket 的结构体大小是16， 更容易对齐
    bucketMgr->bucketOffset = (uint32_t)((uintptr_t)alignedPtr - (uintptr_t)bucketMgr);
    *bucketMgrAddr = addr;
    return STATUS_OK_INTER;
}

void BufpoolDestoryBucketMgr(SeInstanceT *seIns, uint64_t bucketMgrAddr)
{
    if (bucketMgrAddr == DB_INVALID_UINT64) {
        return;
    }
    if (SeBufferPoolIsShmem(seIns)) {
        DbMemCtxT *memCtx = DbGetShmemCtxById(seIns->seTopShmMemCtxId, seIns->instanceId);
        DbShmemCtxFree(memCtx, *(ShmemPtrT *)&bucketMgrAddr);
    } else {
        DbDynMemCtxFree(seIns->seServerMemCtx, (void *)(uintptr_t)bucketMgrAddr);
    }
}

void BufpoolBucketRemoveDesc(BufBucketT *bucket, BufDescT *bufDesc)
{
    BufDescT *item = bucket->first;
    if (item == NULL || bucket->count == 0) {
        return;  // May already remove, do nothing
    }
    if (item == bufDesc) {
        bucket->first = bufDesc->hashNext;
    } else {
        while (item->hashNext != bufDesc) {
            item = item->hashNext;
            if (item == NULL) {
                return;  // May already remove, do nothing
            }
        }
        item->hashNext = bufDesc->hashNext;
    }
    bucket->count--;
    bufDesc->attachedBucket = NULL;
}
