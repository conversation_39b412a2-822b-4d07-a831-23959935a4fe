/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: hash buffer pool
 * Author:
 * Create: 2025-2-26
 */

#ifndef SE_HASH_BUFFER_POOL_H
#define SE_HASH_BUFFER_POOL_H

#include "se_buffer_pool_inner.h"

#ifdef __cplusplus
extern "C" {
#endif

StatusInter HashBufpoolCreatePageMgr(SeInstanceT *seIns, DbMemCtxT *memCtx, PageMgrT **pageMgr, void *sessionCtx);
void HashBufpoolDestroyPageMgr(SeInstanceT *seIns, BufpoolMgrT *mgr, bool requestTrim);

#ifdef __cplusplus
}
#endif

#endif
