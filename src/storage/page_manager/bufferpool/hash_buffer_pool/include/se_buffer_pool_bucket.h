/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: buffer pool bucket
 * Author:
 * Create: 2025-2-26
 */

#ifndef SE_BUFFER_POOL_BUCKET_H
#define SE_BUFFER_POOL_BUCKET_H

#include "se_buffer_pool_inner.h"

#ifdef __cplusplus
extern "C" {
#endif

#define BUCKET_TIMES 3
#define HASH_SEED 0x811c9dc5

typedef struct TagBucketMgr {
    uint32_t bucketNum;
    uint32_t bucketOffset;
} BucketMgrT;

StatusInter BufpoolCreateBucketMgr(SeInstanceT *seIns, uint32_t bucketNum, uint64_t *bucketMgrAddr);
void BufpoolDestoryBucketMgr(SeInstanceT *seIns, uint64_t bucketMgrAddr);

static inline BucketMgrT *BufpoolGetBucketMgr(SeInstanceT *seIns, uint64_t bucketMgrAddr)
{
    if (bucketMgrAddr == DB_INVALID_UINT64) {
        return NULL;
    }
    if (SeBufferPoolIsShmem(seIns)) {
        return (BucketMgrT *)DbShmPtrToAddr(*(ShmemPtrT *)&bucketMgrAddr);
    }
    return (BucketMgrT *)(uintptr_t)bucketMgrAddr;
}

typedef struct TagBufBucket {
    DbRWSpinLockT lock;
    uint32_t count;
    BufDescT *first;
    uint32_t firstDescId;
} BufBucketT;

static inline BufBucketT *BufpoolGetBucketViaIndex(BucketMgrT *bucketMgr, uint32_t index)
{
    BufBucketT *buckets = (BufBucketT *)((uint8_t *)bucketMgr + bucketMgr->bucketOffset);
    return &buckets[index];
}

static inline uint32_t BufBucketHash(PageIdT pageId, uint32_t range)
{
    DB_ASSERT(range != 0);
    return (HASH_SEED * pageId.blockId + pageId.deviceId) * HASH_SEED % range;
}

static inline BufBucketT *BufpoolGetBucketViaPageAddr(BucketMgrT *bucketMgr, PageIdT pageId)
{
    BufBucketT *buckets = (BufBucketT *)((uint8_t *)bucketMgr + bucketMgr->bucketOffset);
    uint32_t hashId = BufBucketHash(pageId, bucketMgr->bucketNum);
    return &buckets[hashId];
}

static inline BufDescT *BufpoolBucketFindDesc(BufpoolMgrT *mgr, BufBucketT *bucket, PageIdT pageId)
{
    BufDescT *bufDesc = bucket->first;
    while (bufDesc != NULL) {
        if (DbIsPageIdEqual(pageId, bufDesc->pageId)) {
            return bufDesc;
        }
        bufDesc = bufDesc->hashNext;
    }
    return NULL;
}

static inline void BufpoolBucketAddDesc(BufBucketT *bucket, BufDescT *bufDesc)
{
    bufDesc->attachedBucket = bucket;
    bufDesc->hashNext = bucket->first;
    bucket->first = bufDesc;
    bucket->count++;
}

SO_EXPORT void BufpoolBucketRemoveDesc(BufBucketT *bucket, BufDescT *bufDesc);

#ifdef __cplusplus
}
#endif

#endif
