/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: hash buffer pool
 * Author:
 * Create: 2025-2-26
 */

#include "se_hash_buffer_pool.h"
#include "se_buffer_pool_page_buffer.h"
#include "se_buffer_pool_bucket.h"
#include "se_buffer_pool_resize.h"
#include "se_buffer_pool_table_recycle.h"
#include "se_buffer_pool_priority.h"
#include "se_buffer_pool_priority_recycle.h"

static void SetBufDescListBasic(uint32_t options, const void *recyArg, BufDescT *bufDesc)
{
    DB_UNUSED(recyArg);
    bufDesc->listId = ((options & ENTER_PAGE_SCAN) != 0) ? LRU_LIST_STATS_SCAN : LRU_LIST_NORMAL;
}

static StatusInter BufpoolEnqueueByNormal(BufpoolMgrT *mgr, PageOptionE option, void *enqueArg)
{
    DB_UNUSED(option);
    BufEnqueArgT *arg = (BufEnqueArgT *)enqueArg;
    BufPoolT *bufPool = GetBufpoolByPageId(mgr, arg->pageId);
    if (!arg->bufDesc->isResident) {
        BufLruAdd(&bufPool->list[arg->bufDesc->listId], arg->bufDesc, BUF_ADD_HEAD);
    }
    return STATUS_OK_INTER;
}

static StatusInter BufDescSwitchLRUListBasic(
    BufpoolMgrT *mgr, BufPoolT *bufPool, uint32_t options, const void *recyArg, BufDescT *bufDesc)
{
    DB_UNUSED(recyArg);
    if (bufDesc->listId == LRU_LIST_STATS_SCAN && options == ENTER_PAGE_NORMAL) {
        // While Normal or EXTEND option, move bufDesc from ScanLRUList to NormalLRUList or PriorityLRUList
        DbSpinLock(&bufPool->lock);
        if (bufDesc->listId != LRU_LIST_STATS_SCAN) {
            DbSpinUnlock(&bufPool->lock);
            return STATUS_OK_INTER;
        }
        BufLruRemove(&bufPool->list[bufDesc->listId], bufDesc);
        DbSpinLock(&bufDesc->lock);
        bufDesc->listId = LRU_LIST_NORMAL;
        DbSpinUnlock(&bufDesc->lock);
        BufLruAdd(&bufPool->list[LRU_LIST_NORMAL], bufDesc, BUF_ADD_HEAD);
        DbSpinUnlock(&bufPool->lock);
    }
    return STATUS_OK_INTER;
}

StatusInter BufpoolRecycleByNormal(
    BufpoolMgrT *mgr, BufGetDescArgsT *args, void *recyArg, bool reTry, BufDescT **bufDesc)
{
    DB_UNUSED(recyArg);
    DB_UNUSED(reTry);
    BufDescT *item = NULL;
    uint32_t option = args->options;
    bool isScanOp = (option & ENTER_PAGE_SCAN) != 0;
    BufPoolT *bufPool = GetBufpoolByPageId(mgr, args->pageId);
    DbSpinLock(&bufPool->lock);
    StatusInter ret = STATUS_OK_INTER;
    if (isScanOp) {
        if (BufChkScanLimit(bufPool)) {
            ret = BufGetDescFromList(mgr, bufPool, &bufPool->list[LRU_LIST_STATS_SCAN], false, &item);
        } else {
            ret = BufGetDescFromList(mgr, bufPool, &bufPool->list[LRU_LIST_NORMAL], false, &item);
            if (ret == STATUS_OK_INTER && item == NULL) {
                ret = BufGetDescFromList(mgr, bufPool, &bufPool->list[LRU_LIST_STATS_SCAN], false, &item);
            }
        }
    } else {
        ret = BufGetDescFromList(mgr, bufPool, &bufPool->list[LRU_LIST_STATS_SCAN], true, &item);
        if (ret == STATUS_OK_INTER && item == NULL) {
            ret = BufGetDescFromList(mgr, bufPool, &bufPool->list[LRU_LIST_NORMAL], false, &item);
        }
    }
    DbSpinUnlock(&bufPool->lock);
    if (ret == STATUS_OK_INTER) {
        *bufDesc = item;
    }
    return ret;
}

static StatusInter BufPoolOamapInit(SeInstanceT *seIns, BufPoolT *bufPool)
{
    bufPool->tableMap = (DbOamapT *)DbDynMemCtxAlloc(seIns->seServerMemCtx, sizeof(DbOamapT));
    if (SECUREC_UNLIKELY(bufPool->tableMap == NULL)) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc bufpool table map, size = %zu.", sizeof(DbOamapT));
        return OUT_OF_MEMORY_INTER;
    }

    Status ret = DbOamapInit(bufPool->tableMap, DB_TABLE_INIT_NUM, DbOamapPtrCompare, seIns->seServerMemCtx, true);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(seIns->seServerMemCtx, bufPool->tableMap);
        DB_LOG_ERROR(ret, "init DbOamap when bufferpool init");
    }
    return DbGetStatusInterErrno(ret);
}

static void BufPoolOamapDestroy(SeInstanceT *seIns, BufPoolT *bufPool)
{
    if (bufPool->tableMap != NULL) {
        DbOamapDestroy(bufPool->tableMap);
        DbDynMemCtxFree(seIns->seServerMemCtx, bufPool->tableMap);
        bufPool->tableMap = NULL;
    }
}

static void HashBufpoolDestroyBufpool(SeInstanceT *seIns, BufPoolT *bufpool)
{
    BufPoolT *cursor = NULL;
    for (uint32_t i = 0; i < SeGetBufpoolCount(seIns); ++i) {
        cursor = &bufpool[i];
        BufPoolOamapDestroy(seIns, cursor);
        BufpoolDestoryBucketMgr(seIns, cursor->currBucketMgrShm);
        BufpoolDestoryBucketMgr(seIns, cursor->newBucketMgrShm);
    }
}

static void BufpoolInitDefaultValue(SeInstanceT *seIns, uint64_t size, uint32_t index, BufPoolT *pool)
{
    DbSpinInit(&pool->lock);
    pool->index = index;
    uint32_t capacity = (uint32_t)(size / ((uint64_t)seIns->seConfig.pageSize * DB_KIBI));
    pool->scanListLimit = (uint32_t)(capacity * SCAN_PAGE_RATIO_LIMIT);
#if defined(IDS_HAOTIAN)
    pool->priorityListMinimum = (uint32_t)(capacity * (seIns->seConfig.bufferPoolPriorityRatio / FLOAT_NUM_HUNDRED));
    pool->highPriorityListMinimum = (uint32_t)(capacity * (seIns->seConfig.loadTablePriorityRatio / FLOAT_NUM_HUNDRED));
#else
    pool->priorityListMinimum = (uint32_t)(capacity * PRIORITY_PAGE_RATIO_MIN);
    pool->highPriorityListMinimum = 0;
#endif
    pool->currBucketMgrShm = DB_INVALID_UINT64;
    pool->newBucketMgrShm = DB_INVALID_UINT64;
    BufpoolLruInit(pool);
    pool->tableMap = NULL;
}

static StatusInter BufpoolAllocMembers(SeInstanceT *seIns, uint64_t size, BufPoolT *bufpool)
{
    uint32_t capacity = (uint32_t)(size / ((uint64_t)seIns->seConfig.pageSize * DB_KIBI));
    StatusInter ret = BufpoolCreateBucketMgr(seIns, BUCKET_TIMES * capacity, &bufpool->currBucketMgrShm);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc bucketMgr, capacity = %" PRIu32 "", capacity);
        return OUT_OF_MEMORY_INTER;
    }
    ret = BufPoolOamapInit(seIns, bufpool);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "init DbOamap when bufferpool init");
        return ret;
    }
    return STATUS_OK_INTER;
}

static StatusInter HashBufpoolCreateBufpool(SeInstanceT *seIns, BufPoolT **bufpool)
{
    uint32_t poolNum = SeGetBufpoolCount(seIns);
    uint32_t allocSize = poolNum * sizeof(BufPoolT);
    BufPoolT *pool = (BufPoolT *)DbDynMemCtxAlloc(seIns->seServerMemCtx, allocSize);
    if (SECUREC_UNLIKELY(pool == NULL)) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc bufferpool, count %" PRIu32, poolNum);
        return OUT_OF_MEMORY_INTER;
    }
    uint64_t size = SeGetBufpoolCurrentSize(seIns);
    for (uint32_t i = 0; i < poolNum; ++i) {
        BufpoolInitDefaultValue(seIns, size, i, &pool[i]);
    }
    StatusInter ret = STATUS_OK_INTER;
    for (uint32_t i = 0; i < poolNum; ++i) {
        ret = BufpoolAllocMembers(seIns, size, &pool[i]);
        if (ret != STATUS_OK_INTER) {
            HashBufpoolDestroyBufpool(seIns, pool);
            return ret;
        }
    }
    *bufpool = pool;
    return STATUS_OK_INTER;
}

static void BufpoolMgrInitBase(SeInstanceT *seIns, BufpoolMgrT *mgr)
{
    mgr->base.type = SE_BUFFER_POOL;
    mgr->base.allocPageFunc = (SeAllocPageFunc)BufpoolAllocPage;
    mgr->base.freePageFunc = (SeFreePageFunc)BufpoolFreePage;
    mgr->base.getPageFunc = (SeGetPageFunc)BufpoolGetPage;
    mgr->base.getPageWithArgFunc = (SeGetPageFuncWithArg)BufpoolGetPageWithArg;
    mgr->base.getPageInitFunc = (SeGetPageInitFunc)BufpoolGetPageInit;
    mgr->base.leavePageFunc = (SeLeavePageFunc)BufpoolLeavePage;
    mgr->base.leaveVirtualPageFunc = (SeLeaveVirtualPageFunc)BufpoolLeaveVirtualPage;
    mgr->base.getExtraFunc = (SeGetPageMgrRegisterExtraFunc)BufferPoolGetRegisterExtraFunc;
    SeInitPageAddrInfo(&mgr->base, seIns->seConfig.pageSize, SeGetPageCntPerDev(seIns));
}

static void BufpoolMgrInitHook(SeInstanceT *seIns, BufpoolMgrT *mgr)
{
    BufRecyTypeE type = seIns->seConfig.bufferPoolPolicy;
    DB_ASSERT(type < BUF_POLICY_BUTT);
    const BufpoolLRUEnqueueFunc enqueueFunc[BUF_POLICY_BUTT] = {
        BufpoolEnqueueByNormal, BufpoolEnqueueByTable, BufpoolEnqueueByMultiPriority, BufpoolEnqueueByNormal};
    const BufpoolRecycleFunc recycleFunc[BUF_POLICY_BUTT] = {
        BufpoolRecycleByNormal, BufpoolRecycleByTable, BufpoolRecycleByMultiPriority, BufpoolRecycleByPriority};
    const BufPreprocessFunc preprocessFunc[BUF_POLICY_BUTT] = {
        NULL, BufPreProcessForTable, BufPreProcessForMultiPriority, NULL};
    void (*setBufDescListFunc[BUF_POLICY_BUTT])(uint32_t options, const void *recyArg, BufDescT *bufDesc) = {
        SetBufDescListBasic, SetBufDescListBasic, SetBufDescListByMultiPriority, BufpoolSetListByPriority};
    const BufDescSwitchLRUListFunc switchLRUListFunc[BUF_POLICY_BUTT] = {BufDescSwitchLRUListBasic,
        BufDescSwitchLRUListBasic, BufDescSwitchLRUListByMultiPriority, BufpoolSwitchLRUListByPriority};
    mgr->lruEnqueueFunc = enqueueFunc[type];
    mgr->recycleFunc = recycleFunc[type];
    mgr->preprocessFunc = preprocessFunc[type];
    mgr->setBufDescListFunc = setBufDescListFunc[type];
    mgr->switchLRUListFunc = switchLRUListFunc[type];
}

static void BufpoolMgrInit(SeInstanceT *seIns, BufpoolMgrT *mgr)
{
    BufpoolMgrInitBase(seIns, mgr);
    DbSpinInit(&mgr->lock);
    mgr->pageSize = seIns->seConfig.pageSize * DB_KIBI;
    mgr->seIns = seIns;
    mgr->bufPoolNum = SeGetBufpoolCount(seIns);
    BufpoolMgrInitHook(seIns, mgr);
    mgr->memUtils = &seIns->memUtils;
    mgr->seLockFn = &seIns->seLockFn;
    mgr->seRWLockFn = &seIns->seRWLockFn;
    mgr->tableMapFns.tableMapIncPageCntFn = BufTableMapIncPageCount;
    mgr->tableMapFns.tableMapDecPageCntFn = BufTableMapDecPageCount;
    mgr->tableMapFns.tableMapGetPageCntFn = BufTableMapGetPageCount;
    mgr->tableMapFns.tableMapClearFn = BufTableMapClearTableInfo;
    mgr->enableSplitChunk = BufpoolResizeEnable(seIns);
}

static StatusInter HashBufpoolCreateDescMgr(SeInstanceT *seIns, BufpoolMgrT *mgr)
{
    uint64_t totalSize = SeGetBufpoolCurrentSize(seIns) / DB_KIBI * SeGetBufpoolCount(seIns);
    BufDescMgrCreateCfgT cfg = BufDescInitDefaultCreateCfg(seIns, SE_BUFFER_POOL, totalSize);
    uint64_t descMgrAddr = DB_INVALID_UINT64;
    StatusInter ret = BufDescMgrCreate(seIns, cfg, &descMgrAddr);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    if (BufpoolResizeEnable(seIns)) {
        mgr->descMgr = SeGetBufDescMgr(seIns, descMgrAddr);
        DB_ASSERT(mgr->descMgr != NULL);
    } else {
        mgr->descMgrAddr = descMgrAddr;
    }
    return STATUS_OK_INTER;
}

static StatusInter HashBufpoolCreatePageBuffer(SeInstanceT *seIns, BufpoolMgrT *mgr)
{
    PageBufferMgrCreateCfgT cfg = PageBufferInitDefaultCreateCfg(seIns);
    uint64_t bufMgrAddr = DB_INVALID_UINT64;
    StatusInter ret = PageBufferCreate(seIns, cfg, &bufMgrAddr);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    if (BufpoolResizeEnable(seIns)) {
        mgr->bufMgr = SeGetPageBufferMgr(seIns, bufMgrAddr);
        DB_ASSERT(mgr->bufMgr != NULL);
    } else {
        mgr->bufMgrAddr = bufMgrAddr;
    }
    return STATUS_OK_INTER;
}

StatusInter HashBufpoolCreatePageMgr(SeInstanceT *seIns, DbMemCtxT *memCtx, PageMgrT **pageMgr, void *sessionCtx)
{
    BufpoolMgrT *mgr = (BufpoolMgrT *)DbDynMemCtxAlloc(seIns->seServerMemCtx, sizeof(BufpoolMgrT));
    if (mgr == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc BufpoolMgr, size = %zu", sizeof(BufpoolMgrT));
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_s(mgr, sizeof(BufpoolMgrT), 0, sizeof(BufpoolMgrT));
    BufpoolMgrInit(seIns, mgr);
    StatusInter ret = TrmIdListMapOamapInit(seIns, mgr);
    if (ret != STATUS_OK_INTER) {
        HashBufpoolDestroyPageMgr(seIns, mgr, false);
        return ret;
    }
    ret = HashBufpoolCreateDescMgr(seIns, mgr);
    if (ret != STATUS_OK_INTER) {
        HashBufpoolDestroyPageMgr(seIns, mgr, false);
        return ret;
    }
    ret = HashBufpoolCreatePageBuffer(seIns, mgr);
    if (ret != STATUS_OK_INTER) {
        HashBufpoolDestroyPageMgr(seIns, mgr, false);
        return ret;
    }
    // 尝试启动resize线程，启动时候就根据是否split chunk决定开启（后续如果并发resize少，可以懒加载）
    ret = BufferpoolResizeInit(seIns, &mgr->resizeCtx);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "reize proc");
        HashBufpoolDestroyPageMgr(seIns, mgr, false);
        return ret;
    }
    seIns->bufpoolMgr = mgr;
    ret = HashBufpoolCreateBufpool(seIns, &mgr->bufPool);
    if (ret != STATUS_OK_INTER) {
        HashBufpoolDestroyPageMgr(seIns, mgr, false);
        return ret;
    }
    ret = BufpoolRegisterRedo(seIns);
    if (ret != STATUS_OK_INTER) {
        HashBufpoolDestroyPageMgr(seIns, mgr, false);
    } else {
        *pageMgr = (PageMgrT *)(void *)mgr;
    }
    return ret;
}

void HashBufpoolDestroyPageMgr(SeInstanceT *seIns, BufpoolMgrT *mgr, bool requestTrim)
{
    if (seIns == NULL || mgr == NULL) {
        return;
    }
    if (mgr->bufPool) {
        HashBufpoolDestroyBufpool(seIns, mgr->bufPool);
        mgr->bufPool = NULL;
    }
    if (mgr->resizeCtx) {
        BufferpoolResizeDestroy(mgr->resizeCtx);
    }
    PageBufferDestroy(seIns, mgr->bufMgrAddr);
    mgr->bufMgrAddr = DB_INVALID_UINT64;
    BufDescMgrDestroy(seIns, mgr->descMgrAddr);
    mgr->descMgrAddr = DB_INVALID_UINT64;
    TrmIdListMapOmapDestroy(seIns, mgr);
    DbDynMemCtxFree(seIns->seServerMemCtx, mgr);
    seIns->bufpoolMgr = NULL;
    if (requestTrim) {
        (void)DbAdptMallocTrim(0);
    }
}
