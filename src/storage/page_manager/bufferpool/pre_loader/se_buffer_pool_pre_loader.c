/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: pre fetch loader
 * Author:
 * Create: 2025-2025
 * Notes:
 */
#include "se_buffer_pool_inner.h"
#include "se_db_file.h"
#include "se_define.h"
#include "se_database.h"
#include "se_datafile.h"
#include "se_db_file_device.h"
#include "se_buffer_pool_pre_loader.h"

typedef struct TagFilePrefetchInfo {
    uint32_t fileId;
    uint32_t fetchLimit;
} FilePrefetchInfoT;

static void PreFetchJoinMultiThread(PrefetchMgrT *pfMgr)
{
    for (uint32_t index = 0; index < pfMgr->cnt; ++index) {
        ConcurrentPrefetchCtxT *ctx = &pfMgr->ctx[index];
        while (ctx->running) {
            DbUsleep(1);
        }
    }
}

static StatusInter PreFetchFile(ConcurrentPrefetchCtxT *ctx, const FilePrefetchInfoT *fileInfo)
{
    StatusInter ret = STATUS_OK_INTER;
    SeInstanceT *seIns = ctx->seIns;
    DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, fileInfo->fileId);
    if (file == NULL) {
        SE_WARN(UNEXPECTED_NULL_VALUE_INTER, "file %" PRIu32 "miss at prefetch %" PRIu32, fileInfo->fileId, ctx->index);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    PageMgrT *pageMgr = SeGetPageMgrByFileType(seIns, file);
    if (pageMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get page mgr, file(%" PRIu32 ") type( %" PRIu32 ")", file->ctrl->id,
            file->ctrl->fileType);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    DbDeviceT *cursor = NULL;
    PageIdT addr = SE_INVALID_PAGE_ADDR;
    uint8_t *page = NULL;
    uint32_t fetchCount = 0;
    for (uint32_t i = 0; i < CtrlGroupGetSize(&seIns->db->deviceGroup) && !BufpoolAtMaxCapacity(pageMgr); ++i) {
        cursor = (DbDeviceT *)CtrlGroupGetItem(&seIns->db->deviceGroup, i);
        if (SeFilePreFetchShutdown(ctx->pfMgr)) {
            return STATUS_OK_INTER;
        }
        if (!cursor || cursor->ctrl->fileId != file->ctrl->id) {
            continue;
        }
        uint32_t blockNum = SE_INVALID_BLOCK_ID;
        ret = SeGetFileDeviceBlockHwm(seIns, cursor->ctrl, &blockNum);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "PreFetchProc %" PRIu32 " Fetch Device (%" PRIu32 ") BlockHwm", ctx->index, cursor->ctrl->id);
            return ret;
        }
        for (uint32_t j = 0; j < blockNum && !BufpoolAtMaxCapacity(pageMgr) && fetchCount < fileInfo->fetchLimit; j++) {
            fetchCount++;
            addr = (PageIdT){.deviceId = cursor->ctrl->id, .blockId = j};
            ret = SeGetPage(pageMgr, addr, &page, ENTER_PAGE_NORMAL, false);
            if (ret != STATUS_OK_INTER) {
                SE_WARN(ret, "PreFetchProc %" PRIu32 " Fetch Page (%" PRIu32 " %" PRIu32 ")", ctx->index, addr.deviceId,
                    addr.blockId);
                continue;
            }
            SeLeavePage(pageMgr, addr, false);
        }
    }
    return STATUS_OK_INTER;
}

// 持久化文件加载入口，可以预加载页至bufferpool中，也可以异步加载durable持久化文件数据至内存中
static void *PreFetchFileProc(void *param)
{
    DB_POINTER(param);
    ConcurrentPrefetchCtxT *ctx = (ConcurrentPrefetchCtxT *)param;
    uint32_t fileNum = DbListGetItemCnt(&ctx->fileList);
    FilePrefetchInfoT *cursor = NULL;
    uint32_t index = 0;
    for (; index < fileNum && !SeFilePreFetchShutdown(ctx->pfMgr); ++index) {
        cursor = (FilePrefetchInfoT *)DbListItem(&ctx->fileList, index);
        ctx->status = PreFetchFile(ctx, cursor);
        if (ctx->status != STATUS_OK_INTER) {
            SE_ERROR(ctx->status, "PreFetchProc %" PRIu32 " pre fetch file%" PRIu32, ctx->index, cursor->fileId);
            break;
        }
    }
    if (ctx->status == STATUS_OK_INTER) {
        DB_LOG_INFO("PreFetchProc %" PRIu32 " pre fetch file cnt%" PRIu32, ctx->index, index);
    }
    ctx->running = false;
    return NULL;
}

static StatusInter PreFetchStartThread(PrefetchMgrT *pfMgr)
{
    Status ret = GMERR_OK;
    ThreadAttrsT attr = {
        .priority = THREAD_PRIORITY_MIDDLE,
        .type = DB_THREAD_JOINABLE,
        .entryFunc = PreFetchFileProc,
        .exitFunc = NULL,
        .exitArgs = NULL,
        .stackSize = DB_DEFAULT_THREAD_STACK_SIZE,
        .bindCpuFlag = 0,
        .cpu = 0,
        .userAttr = NULL,
    };
    // 依次拉起线程对每个文件进行预取
    for (uint32_t index = 0; index < pfMgr->cnt && !SeFilePreFetchShutdown(pfMgr); ++index) {
        ConcurrentPrefetchCtxT *ctx = &pfMgr->ctx[index];
        int32_t size = sprintf_s(attr.name, DB_THREAD_NAME_MAX_LEN, "PrefetchThread_%u", index);
        if (size < 0) {
            SE_LAST_ERROR(INTERNAL_ERROR_INTER, "Sprintf_s thread name (%s), i(%" PRIu32 ")", attr.name, index);
            return INTERNAL_ERROR_INTER;
        }
        attr.entryArgs = (void *)ctx;
        ctx->running = true;
        ret = DbThreadCreate(&attr, &(ctx->handle));
        if (ret != GMERR_OK) {
            ctx->running = false;
            DB_LOG_ERROR(ret, "Unable to create pre fetch process thread %" PRIu32, index);
            return DbGetStatusInterErrno(ret);
        }
    }
    return STATUS_OK_INTER;
}

static uint32_t PrefetchGetFileCount(SeInstanceT *seInsPtr, SpaceT *space, SeFileTypeE type)
{
    uint32_t totalFileNum = SpaceFileListGetLen(space);
    uint32_t result = 0;
    if (SeIsBufpoolAndDumemCoexist(seInsPtr) && type != ALL_FILE) {
        for (uint32_t index = 0; index < totalFileNum; ++index) {
            uint32_t fileId = *(uint32_t *)SpaceFileListGet(space, index);
            DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seInsPtr->db->fileGroup, fileId);
            if (file->ctrl->fileType == (uint8_t)type) {
                result++;
            }
        }
    } else {
        result = totalFileNum;
    }
    return result;
}

ConcurrentPrefetchCtxT *AllocPreFetchCtx(SeInstanceT *seIns, uint32_t threadNum)
{
    // 生成后台线程参数
    uint32_t allocSize = (uint32_t)sizeof(ConcurrentPrefetchCtxT) * threadNum;
    ConcurrentPrefetchCtxT *ctx = (ConcurrentPrefetchCtxT *)DbDynMemCtxAlloc(seIns->seServerMemCtx, allocSize);
    if (SECUREC_UNLIKELY(ctx == NULL)) {
        DB_LOG_ERROR(OUT_OF_MEMORY_INTER, "prefetchInfo alloc %" PRIu32, allocSize);
        return NULL;
    }
    (void)memset_s(ctx, allocSize, 0x0, allocSize);
    for (uint32_t index = 0; index < threadNum; ++index) {
        DbCreateList(&(ctx[index].fileList), sizeof(FilePrefetchInfoT), seIns->seServerMemCtx);
    }
    return ctx;
}

void FreePreFetchCtx(SeInstanceT *seIns, PrefetchMgrT *pfMgr)
{
    if (pfMgr->ctx == NULL) {
        return;
    }
    PreFetchJoinMultiThread(pfMgr);
    ConcurrentPrefetchCtxT *ctx = pfMgr->ctx;
    for (uint32_t i = 0; i < pfMgr->cnt; ++i) {
        DbDestroyList(&(ctx[i].fileList));
        if (ctx[i].status != STATUS_OK_INTER) {
            SE_ERROR(ctx[i].status, "Prefetch Thread %" PRIu32 ", ret %" PRIu32, ctx[i].index, ctx[i].status);
        }
    }
    DbDynMemCtxFree(seIns->seServerMemCtx, pfMgr->ctx);
    pfMgr->ctx = NULL;
    pfMgr->cnt = 0;
}

void BufpoolStartPreFetchDestructor(SeInstanceT *seIns, PrefetchMgrT *pfMgr)
{
    FreePreFetchCtx(seIns, pfMgr);
    DbSpinUnlock(&pfMgr->lock);
}

static StatusInter InitPreFetchCtrl(SeInstanceT *seIns, SpaceT *space, PrefetchMgrT *pfMgr, uint32_t fileCnt)
{
    uint32_t threadNum = fileCnt >= seIns->seConfig.maxPreFetchThreNum ? seIns->seConfig.maxPreFetchThreNum : fileCnt;
    ConcurrentPrefetchCtxT *fetchInfo = NULL;
    if (pfMgr->ctx == NULL || threadNum > pfMgr->cnt) {
        FreePreFetchCtx(seIns, pfMgr);
        // 生成后台线程参数
        DB_ASSERT(pfMgr->ctx == NULL);
        fetchInfo = AllocPreFetchCtx(seIns, threadNum);
    } else {
        fetchInfo = pfMgr->ctx;
    }
    if (fetchInfo == NULL) {
        return OUT_OF_MEMORY_INTER;
    }
    for (uint32_t index = 0; index < threadNum; ++index) {
        fetchInfo[index].seIns = seIns;
        fetchInfo[index].pfMgr = pfMgr;
        fetchInfo[index].index = index;
        fetchInfo[index].status = STATUS_OK_INTER;
        DbClearList(&(fetchInfo[index].fileList));
        fetchInfo[index].running = false;
    }
    pfMgr->ctx = fetchInfo;
    COMPILER_BARRIER;
    pfMgr->cnt = threadNum;
    return STATUS_OK_INTER;
}

// prefetch线程和dml线程会并发， 拓展后的文件可能bufferpool中还没有同步申请内存
// 最终导致从文件中读出异常页面（页面没有落盘过，bufferpool也没有）
// 所以在准备阶段提前锁定需要fetch的数量，防止后续并发问题
static StatusInter PreFetchInfoFileFetchInfo(SeInstanceT *seIns, DbFileT *file, FilePrefetchInfoT *fileFetchInfo)
{
    PageMgrT *pageMgr = SeGetPageMgrByFileType(seIns, file);
    if (pageMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get page mgr, file(%" PRIu32 ") type( %" PRIu32 ")", file->ctrl->id,
            file->ctrl->fileType);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    StatusInter ret = SeGetFileHead(pageMgr, file, false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "(se-space)get file head page (%" PRIu32 ", %" PRIu32 ") unsucc", file->ctrl->entry.deviceId,
            file->ctrl->entry.blockId);
        return ret;
    }
    PageHeadT *page = (PageHeadT *)((uint8_t *)file->head - sizeof(PageHeadT));
    DbRWLatchR(&page->lock);
    fileFetchInfo->fileId = file->ctrl->id;
    fileFetchInfo->fetchLimit = file->head->usedSize / SIZE_K(seIns->seConfig.pageSize);
    DbRWUnlatchR(&page->lock);
    SeLeaveFileHead(pageMgr, file, false);
    return STATUS_OK_INTER;
}

static StatusInter PreFetchCollectFile(SeInstanceT *seIns, PrefetchMgrT *pfMgr, SpaceT *space, SeFileTypeE type)
{
    StatusInter ret = STATUS_OK_INTER;
    uint32_t cursor = 0;
    uint32_t fileNum = SpaceFileListGetLen(space);
    FilePrefetchInfoT fileFetchInfo = {0};
    for (uint32_t index = 0; index < fileNum && !SeFilePreFetchShutdown(pfMgr); ++index) {
        uint32_t fileId = *(uint32_t *)SpaceFileListGet(space, index);
        DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, fileId);
        if (SeIsBufpoolAndDumemCoexist(seIns)) {
            // 全取但非bp或dr
            if (type == SE_ALL_FILE) {
                if (file->ctrl->fileType == DEFAULT_FILE) {
                    continue;
                }
            } else if (file->ctrl->fileType != type) {
                continue;
            }
        }
        ret = PreFetchInfoFileFetchInfo(seIns, file, &fileFetchInfo);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        ret = DbGetStatusInterErrno(
            DbAppendListItem(&(pfMgr->ctx[cursor % pfMgr->cnt].fileList), (void *)&fileFetchInfo));
        if (ret != STATUS_OK_INTER) {
            SE_LAST_ERROR(ret, "preFetch appendListItem unsucc, fileId:%" PRIu32, fileId);
            return ret;
        }
        cursor++;
    }
    return STATUS_OK_INTER;
}

StatusInter SeFilePreFetchStart(SeInstanceT *seIns, SeFileTypeE type)
{
    DB_POINTER2(seIns, seIns->bufpoolMgr);
    uint32_t spaceId = SpaceGetIdByTypeImpl(seIns, SPACE_TYPE_USER_DEFAULT);
    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, spaceId);
    PrefetchMgrT *pfMgr = &seIns->db->pfMgr;
    if (SeFilePreFetchShutdown(pfMgr)) {
        return STATUS_OK_INTER;
    }
    PreFetchJoinMultiThread(pfMgr);
    DbSpinLock(&pfMgr->lock);
    if (SeFilePreFetchShutdown(pfMgr)) {
        DbSpinUnlock(&pfMgr->lock);
        return STATUS_OK_INTER;
    }

    uint32_t fileCnt = PrefetchGetFileCount(seIns, space, type);
    StatusInter ret = InitPreFetchCtrl(seIns, space, pfMgr, fileCnt);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "init prefech ctrl");  // LCOV_EXCL_LINE
        goto EXIT;
    }

    // 遍历当前space内的所有文件，分配到各个线程中
    ret = PreFetchCollectFile(seIns, pfMgr, space, type);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "collect file");  // LCOV_EXCL_LINE
        goto EXIT;
    }

    ret = PreFetchStartThread(pfMgr);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "start prefetch worker");  // LCOV_EXCL_LINE
        goto EXIT;
    }
    DbSpinUnlock(&pfMgr->lock);
    return STATUS_OK_INTER;
EXIT:
    BufpoolStartPreFetchDestructor(seIns, pfMgr);
    return ret;
}

void SeFilePreFetchStop(SeInstanceT *seIns)
{
    DB_POINTER(seIns);
    // 如果bufpool未初始化，不进行后面流程
    if (seIns->db == NULL || seIns->db->pfMgr.ctx == NULL) {
        return;
    }
    PrefetchMgrT *pfMgr = &seIns->db->pfMgr;
    pfMgr->shutdown = true;
    PreFetchJoinMultiThread(pfMgr);
    DbSpinLock(&pfMgr->lock);
    FreePreFetchCtx(seIns, pfMgr);
    DbSpinUnlock(&pfMgr->lock);
}

bool SeFilePreFetchShutdown(PrefetchMgrT *mgr)
{
    return mgr->shutdown;
}
