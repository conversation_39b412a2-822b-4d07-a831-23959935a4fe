/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: array buffer pool
 * Author:
 * Create: 2025-2025
 */
#include "se_buffer_pool_resize.h"
#include "se_array_buffer_pool_inner.h"
#include "se_buffer_pool_pre_loader.h"
#include "se_array_buffer_pool_extra.h"
#include "se_buffer_pool_ringbuffer.h"
#include "se_buffer_pool_priority_recycle.h"

#define ARRAY_BUFFER_POOL_LOCK_TIME_OUT 100  // 100us

static StatusInter PageWriteToDisk(BufpoolMgrT *mgr, BufDescT *desc)
{
    if (SeGetPersistMode() != PERSIST_ON_DEMAND) {
        return STATUS_OK_INTER;
    }
    PageHeadT *page = (PageHeadT *)desc->page;
    VDWriteModeE mode = VD_DISCARD_CHANGE;
    if (desc->isWriting || desc->isPinned) {
        mode = VD_COMMIT_CHANGE;
    }
    if (!DbIsPageIdValid(page->addr)) {
        mode = VD_FREE;
    }
    return SeVirtualDiskWriteBlock(mgr->seIns, desc->pageId, (uint8_t *)page, mode);
}

static StatusInter PageReadFromDisk(BufpoolMgrT *mgr, BufDescT *desc)
{
    DbSpinLock(&desc->lock);
    if (DbAtomicGet8(&desc->loadStatus) == BUF_IS_LOADED) {
        DbSpinUnlock(&desc->lock);
        return STATUS_OK_INTER;
    }
    StatusInter ret = STATUS_OK_INTER;
    ret = SpaceReadBlockToPage(mgr->seIns, desc->pageId, desc->page);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "read page (%" PRIu32 ", %" PRIu32 ") from disk", desc->pageId.deviceId, desc->pageId.blockId);
        goto EXIT;
    }
    ret = SpaceCheckDiskBlock((PageHeadT *)desc->page, desc->pageId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "check page (%" PRIu32 ", %" PRIu32 ") from disk", desc->pageId.deviceId, desc->pageId.blockId);
    }
EXIT:
    if (ret != STATUS_OK_INTER) {
        DbAtomicSet8(&desc->loadStatus, BUF_LOAD_FAILED);
    } else {
        DbAtomicSet8(&desc->loadStatus, BUF_IS_LOADED);
    }
    DbSpinUnlock(&desc->lock);
    return ret;
}

static StatusInter ArrayAllocPageBuffer(BufpoolMgrT *mgr, BufDescT *desc)
{
    DbSpinLock(&mgr->lock);
    PageBufferMgrT *bufMgr = BufpoolGetPageBufferMgr(mgr);
    StatusInter ret = PageBufferAlloc(bufMgr, &desc->bufId);
    if (ret != STATUS_OK_INTER) {
        DbSpinUnlock(&mgr->lock);
        SE_ERROR(
            ret, "PageBuffer alloc for page(%" PRIu32 ", %" PRIu32 ")", desc->pageId.deviceId, desc->pageId.blockId);
        return ret;
    }
    desc->page = PageBufferGet(bufMgr, desc->bufId);
    if (desc->page == NULL) {
        DB_ASSERT(false);
        SE_ERROR(INTERNAL_ERROR_INTER, "Desc %" PRIu32 " Buffer %" PRIu32 " Empty", desc->descId, desc->bufId);
        return INTERNAL_ERROR_INTER;
    }
    DbSpinUnlock(&mgr->lock);
    return STATUS_OK_INTER;
}

static StatusInter ArrayLruFindDesc(BufpoolMgrT *mgr, uint32_t listId, BufDescT *target)
{
    BufPoolT *pool = mgr->bufPool;
    BufDescListT *list = &pool->list[listId];
    StatusInter ret = STATUS_OK_INTER;
    BufDescT *shift;
    uint32_t i = 0;
    BufDescT *victim = BUF_DESC_LIST_TAIL_ENTRY(list);
    for (; i < list->count; i++) {
        if (!DescIsRecyclable(mgr, victim)) {
            // move pinned page to the list head
            shift = victim;
            victim = BUF_DESC_PREV_ENTRY(victim);
            BufLruShift(list, shift);
            continue;
        }
        BufLruRemove(list, victim);
        if (!DescIsRecyclable(mgr, victim) || (ret = PageWriteToDisk(mgr, victim)) != STATUS_OK_INTER) {
            if (ret != STATUS_OK_INTER) {
                DB_ASSERT(false);
                SE_WARN(ret, "recycle page %" PRIu32 " %" PRIu32 " to virtual disk", victim->pageId.deviceId,
                    victim->pageId.blockId);
                ret = STATUS_OK_INTER;
            }
            BufLruAdd(list, victim, BUF_ADD_HEAD);
            i = 0;
            victim = BUF_DESC_LIST_TAIL_ENTRY(list);
            continue;
        } else {
            victim->listId = LRU_LIST_TYPE_MAX;
            victim->isWriting = false;
            DbAtomicSet8(&victim->loadStatus, BUF_NEED_LOAD);
            break;
        }
    }
    if (i == list->count) {
        return NO_DATA_INTER;
    }
    DB_ASSERT(victim->bufId != DB_INVALID_UINT32);
    target->bufId = victim->bufId;
    target->page = victim->page;
    victim->bufId = DB_INVALID_ID32;
    victim->page = NULL;
    return ret;
}

static void ArrayBufpoolInitDesc(BufpoolMgrT *mgr, PageOptionE option, BufDescT *desc)
{
    if ((option & ENTER_PAGE_INIT) != 0 || (option & ENTER_PAGE_LOAD) != 0) {
        (void)memset_s(desc->page, mgr->pageSize, 0x0, mgr->pageSize);
        DbAtomicSet8(&desc->loadStatus, BUF_IS_LOADED);
    }

    BufLruListTypeE listId = (option & ENTER_PAGE_SCAN) != 0 ? LRU_LIST_STATS_SCAN : LRU_LIST_NORMAL;
    desc->listId = listId;

    BufPoolT *pool = mgr->bufPool;
    BufLruAdd(&pool->list[listId], desc, BUF_ADD_HEAD);
}

static StatusInter ArrayBufpoolFindBufferMain(BufpoolMgrT *mgr, PageOptionE option, BufDescT *desc)
{
    BufPoolT *pool = mgr->bufPool;
    while (!DbSpinTimedLock(&pool->lock, ARRAY_BUFFER_POOL_LOCK_TIME_OUT)) {
        if (ArrayBufpoolBufDescAccessible(desc)) {
            return STATUS_OK_INTER;
        }
    }
    StatusInter ret = STATUS_OK_INTER;
    if (ArrayBufpoolBufDescAccessible(desc)) {
        DbSpinUnlock(&pool->lock);
        return ret;
    }
    BufLruListTypeE listId = LRU_LIST_NORMAL;
    if ((option & ENTER_PAGE_SCAN) == 0 || BufChkScanLimit(pool)) {
        listId = LRU_LIST_STATS_SCAN;
    }
    DB_ASSERT(!desc->isWriting && DbAtomicGet8(&desc->loadStatus) == BUF_NEED_LOAD);
    ret = ArrayAllocPageBuffer(mgr, desc);
    if (ret == STATUS_OK_INTER) {
        goto SUCCESS;
    }
    ret = ArrayLruFindDesc(mgr, listId, desc);
    if (ret == NO_DATA_INTER) {
        if (listId == LRU_LIST_STATS_SCAN) {
            listId = LRU_LIST_NORMAL;
            ret = ArrayLruFindDesc(mgr, listId, desc);
        }
        if (ret == NO_DATA_INTER) {
            goto EXIT;
        }
    }
SUCCESS:
    ArrayBufpoolInitDesc(mgr, option, desc);
EXIT:
    DbSpinUnlock(&pool->lock);
    return ret;
}

static StatusInter ArrayBufpoolFindBufferLoop(BufpoolMgrT *mgr, PageOptionE option, BufDescT *desc)
{
    StatusInter ret = STATUS_OK_INTER;
    uint64_t triggerWarnTimes = 0;
    uint64_t startTime = DbRdtsc();
    uint32_t tryCount = 0;
    while (true) {
        ret = ArrayBufpoolFindBufferMain(mgr, option, desc);
        if (ret == STATUS_OK_INTER) {
            break;
        }

        DB_ASSERT(SeGetPersistMode() == PERSIST_INCREMENT);  // 按需持久化肯定能一次命中，无法通过刷盘清理
        if (ret != NO_DATA_INTER || SeGetPersistMode() != PERSIST_INCREMENT) {
            SE_ERROR(ret, "recycle when get new desc, pageId(%" PRIu32 ",%" PRIu32 ").", desc->pageId.deviceId,
                desc->pageId.blockId);
            return ret;
        }

        // 此处忽略返回值 如果不成功 重试即可, 处理内存不足后ckpt刷不了盘的情况
        // influencing bufferpool get new disk.
        ret = CkptTriggerAndCheckTimeout(mgr->seIns, &startTime, &triggerWarnTimes);
        if (ret == OUT_OF_MEMORY_INTER) {
            SE_ERROR(ret, "trigger checkpoint");
            return ret;
        }

        if ((option & ENTER_PAGE_NOWAIT) != 0) {
            SE_WARN(NO_DATA_BUFFER_POOL_IS_FULL, "buffer pool is full.");
            return NO_DATA_BUFFER_POOL_IS_FULL;
        }

        if (ret == DATABASE_NOT_AVAILABLE_INTER) {
            SE_ERROR(ret, "trigger checkpoint");
            return ret;
        }

        // 每尝试10次打印一次
        if (tryCount++ % TRY_TIMES_WARN_PERIOD == 0) {
            SE_WARN(STATUS_OK_INTER, "try get new desc %" PRIu32 " times.", tryCount);
        }
    }
    desc->operateTime = DbToSeconds(DbRdtsc());
    return STATUS_OK_INTER;
}

static void DescSwitchStatus(BufpoolMgrT *mgr, PageOptionE option, BufDescT *desc)
{
    BufPoolT *pool = mgr->bufPool;
    if ((option & ENTER_PAGE_FREE) != 0) {
        desc->isPinned = false;
        if (desc->isResident) {
            ArrayBufpoolBufDescUnsetResident(pool, desc);
        }
    } else {
        if (SECUREC_UNLIKELY(!desc->isResident && (option & ENTER_PAGE_RESIDENT) != 0)) {
            ArrayBufpoolBufDescSetResident(pool, desc);
        } else if ((option & ENTER_PAGE_PINNED) != 0 && !desc->isPinned) {
            desc->isPinned = true;
        }
    }
}

static void DescSwitchLruList(BufpoolMgrT *mgr, PageOptionE option, BufDescT *desc)
{
    BufPoolT *pool = mgr->bufPool;
    if (desc->listId == LRU_LIST_STATS_SCAN && option == ENTER_PAGE_NORMAL) {
        // While Normal or EXTEND option, move bufDesc from ScanLRUList to NormalLRUList or PriorityLRUList
        DbSpinLock(&pool->lock);
        if (desc->listId == LRU_LIST_STATS_SCAN) {
            BufLruSwitch(&pool->list[LRU_LIST_STATS_SCAN], &pool->list[LRU_LIST_NORMAL], desc);
            desc->listId = LRU_LIST_NORMAL;
        }
        DbSpinUnlock(&pool->lock);
    }
}

static StatusInter DescMarkWrite(BufpoolMgrT *mgr, PageOptionE option, BufDescT *desc)
{
    StatusInter ret = STATUS_OK_INTER;
    if ((option & ENTER_PAGE_WRITE) != 0) {
        DB_ASSERT(DbCommonIsServer());  // 客户端不允许写页
        if (SeGetPersistMode() == PERSIST_ON_DEMAND) {
            desc->isWriting |= true;
        } else {
            ret = WaitPageWriteComplete(mgr->seIns, NULL, desc);
        }
    }
    return ret;
}

static StatusInter GetPageServer(BufpoolMgrT *mgr, PageOptionE option, BufDescT *desc)
{
    StatusInter ret = STATUS_OK_INTER;
    if (!ArrayBufpoolBufDescAccessible(desc)) {
        if ((option & ENTER_PAGE_ACTIVE) == 0) {
            ret = ArrayBufpoolFindBufferLoop(mgr, option, desc);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "ArrayBufpool find buffer, desc page(%" PRIu32 ", %" PRIu32 ")", desc->pageId.deviceId,
                    desc->pageId.blockId);
                return ret;
            }
        } else {
            return NO_DATA_INTER;
        }
    }

    if (DbAtomicGet8(&desc->loadStatus) != BUF_IS_LOADED) {
        ret = PageReadFromDisk(mgr, desc);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(
                ret, "Read from disk, pageId: (%" PRIu32 ",%" PRIu32 ")", desc->pageId.deviceId, desc->pageId.blockId);
            return ret;
        }
    }

    DB_ASSERT(DbAtomicGet8(&desc->loadStatus) == BUF_IS_LOADED);
    DescSwitchStatus(mgr, option, desc);
    DescSwitchLruList(mgr, option, desc);
    return DescMarkWrite(mgr, option, desc);
}

static StatusInter ArrayBufpoolSendQuest(BufpoolMgrT *mgr, BufDescT *desc)
{
    uint32_t ticket = RING_INVALID_TICKET;
    StatusInter ret = DbGetStatusInterErrno(BufpoolEnqueueRingBuffer(mgr, desc->pageId, &ticket));
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "Clt Bufferpool send semaphore.");
        return DbGetStatusInterErrno(ret);
    }
    uint64_t startTime = DbRdtsc();
    uint64_t waitCnt = 0;
    // check if the desc is in the lru list.
    while (!ArrayBufpoolBufDescAccessible(desc)) {
        waitCnt++;
        if (waitCnt % CLT_TRY_TIMES_GET_PAGE_WARN == 0) {
            uint64_t waitTime = DbToMseconds(DbRdtsc() - startTime);
            // send signal to server every 10000 times when try to get Desc.
            SE_WARN(STATUS_OK_INTER,
                "CltBufPool get pageId:%" PRIu32 ",%" PRIu32 " try times:%" PRIu64 ", ticket no %" PRIu32,
                desc->pageId.deviceId, desc->pageId.blockId, waitTime, ticket);
            ret = DbGetStatusInterErrno(BufpoolEnqueueRingBuffer(mgr, desc->pageId, &ticket));
            if (ret != STATUS_OK_INTER) {
                SE_LAST_ERROR(ret, "CltBufPool loop in send semaphore.");
                return ret;
            }
        }
    }
    return STATUS_OK_INTER;
}

static StatusInter GetPageClient(BufpoolMgrT *mgr, BufDescT *desc)
{
    StatusInter ret = STATUS_OK_INTER;
    if (!ArrayBufpoolBufDescAccessible(desc)) {
        if ((ret = ArrayBufpoolSendQuest(mgr, desc)) != STATUS_OK_INTER) {
            return ret;
        }
    }
    uint8_t loadStatus = DbAtomicGet8(&desc->loadStatus);
    while (loadStatus != BUF_IS_LOADED) {
        if (loadStatus == BUF_LOAD_FAILED) {
            SE_ERROR(DATA_CORRUPTION_INTER, "page %" PRIu32 " %" PRIu32 " requested but corrupted",
                desc->pageId.deviceId, desc->pageId.blockId);
            return DATA_CORRUPTION_INTER;
        }
        loadStatus = DbAtomicGet8(&desc->loadStatus);
    }
    DB_ASSERT(ArrayBufpoolBufDescAccessible(desc) && loadStatus == BUF_IS_LOADED);
    return STATUS_OK_INTER;
}

static StatusInter GetPage(BufpoolMgrT *mgr, PageIdT pageId, PageOptionE option, uint8_t **page)
{
    BufDescT *desc = ArrayBufpoolGetDesc(mgr, pageId, option);
    if (!desc) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "page %" PRIu32 " %" PRIu32 "", pageId.deviceId, pageId.blockId);
        DB_ASSERT(false);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    uint16_t refCount = DbAtomicInc16(&desc->refCount);
    DB_ASSERT(refCount > 0);
    StatusInter ret = STATUS_OK_INTER;
    if (DbCommonIsServer()) {
        ret = GetPageServer(mgr, option, desc);
    } else {
        ret = GetPageClient(mgr, desc);
    }
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get page, pageId: (%" PRIu32 ",%" PRIu32 ")", pageId.deviceId, pageId.blockId);
        (void)DbAtomicDec16(&desc->refCount);
        return ret;
    }
    PageHeadT *head = (PageHeadT *)PageBufferGet(BufpoolGetPageBufferMgr(mgr), desc->bufId);
    if (head == NULL) {
        DB_ASSERT(false);
        SE_ERROR(INTERNAL_ERROR_INTER, "Desc %" PRIu32 " Buffer %" PRIu32 " Empty", desc->descId, desc->bufId);
        return INTERNAL_ERROR_INTER;
    }
    *page = (uint8_t *)head;
    return STATUS_OK_INTER;
}

static StatusInter ArrayBufpoolInitDescMgr(SeInstanceT *seIns)
{
    if (SeGetBufDescMgr(seIns, seIns->pageDescArray) != NULL) {
        return STATUS_OK_INTER;
    }
    const SeConfigT *seConfig = &seIns->seConfig;
    uint64_t totalSize = (uint64_t)seConfig->maxSeMem / seConfig->deviceSize * seConfig->deviceSize;
    uint32_t devMaxCount = totalSize / seConfig->deviceSize;
    if ((SIZE_K(seIns->seConfig.pageSize) - sizeof(PageHeadT) - sizeof(SpaceHeadT)) / sizeof(FreeFileDeviceInfoT) <
            devMaxCount &&
        SeGetSpacePolicy() == SE_SPACE_PRIORITY_DEVICE) {
        SE_LAST_ERROR(CONFIGURATION_LIMIT_EXCEEDED_INTER, "MaxDevCnt(%" PRIu32 ") too big!", devMaxCount);
        return CONFIGURATION_LIMIT_EXCEEDED_INTER;
    }
    BufDescMgrCreateCfgT cfg = BufDescInitDefaultCreateCfg(seIns, SE_BUFFER_POOL, totalSize);
    StatusInter ret = BufDescMgrCreate(seIns, cfg, &seIns->pageDescArray);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "create desc mgr, capacity:%" PRIu32, cfg.capacity);
        return ret;
    }
    BufDescMgrT *mgr = SeGetBufDescMgr(seIns, seIns->pageDescArray);
    if (mgr == NULL) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get BufDesc Mgr");
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    uint32_t startIndex;
    ret = BufDescPreAlloc(mgr, cfg.capacity, &startIndex);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "init durable alloc bufdesc");
    } else {
        DB_ASSERT(startIndex == 0);
    }
    return ret;
}

StatusInter ArrayBufpoolInit(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx)
{
    StatusInter ret = ArrayBufpoolInitDescMgr(seIns);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "ArrayBufpool init descMgr");
        return ret;
    }
    PageBufferMgrCreateCfgT cfg = PageBufferInitDefaultCreateCfg(seIns);
    return PageBufferCreate(seIns, cfg, &seIns->pageBufShm);
}

void ArrayBufpoolDestroy(SeInstanceT *seIns)
{
    PageBufferDestroy(seIns, seIns->pageBufShm);
    seIns->pageBufShm = DB_INVALID_UINT64;
    BufDescMgrDestroy(seIns, seIns->pageDescArray);
    seIns->pageDescArray = DB_INVALID_UINT64;
}

void *ArrayBufpoolExtraFunc(PageMgrT *pageMgr, PageMgrExtraFuncTypeE funcType)
{
    switch (funcType) {
        case PAGE_MGR_EXTRA_BUFFER_POOL_TRMID_OP_WITH_PAGE:
            return (void *)(TrmIdOpWithPageT)ArrayBufferMaintainTrmListViaMeta;

        case PAGE_MGR_EXTRA_BUFFER_POOL_TRMID_OP:
            return (void *)(TrmIdOp)ArrayBufferMaintainTrmListViaTrmId;

        case PAGE_MGR_EXTRA_BUFFER_POOL_PREFETCH_PAGES:
            return (void *)(PreFetchPageFunc)SeFilePreFetchStart;

        case PAGE_MGR_EXTRA_BUFFER_POOL_STOP_PREFETCH_PROC:
            return (void *)(StopProcPrefetchFunc)SeFilePreFetchStop;

        case PAGE_MGR_EXTRA_BUFFER_POOL_RESIZE:
            return (void *)(ResizeBufferFunc)ArrayBufpoolResize;

        case PAGE_MGR_EXTRA_BUFFER_POOL_INVALID_PAGE:
            return (void *)(SeInvalidBufpoolPageFunc)ArrayBufpoolRemovePage;

        case PAGE_MGR_EXTRA_BUFFER_POOL_RESET:
            return (void *)(ResetBufpoolFunc)ArrayBufpoolReset;

        default:
            // Get bufferpool extral function type:%x
            SE_LAST_ERROR(NO_DATA_INTER, "extral func:%" PRIu32, (uint32_t)funcType);
            return NULL;
    }
}

static void ArrayBufpoolInitMgr(SeInstanceT *seIns, BufpoolMgrT *mgr, void *sessionCtx)
{
    mgr->base.type = SE_BUFFER_POOL;
    mgr->base.allocPageFunc = (SeAllocPageFunc)ArrayBufpoolAllocPage;
    mgr->base.freePageFunc = (SeFreePageFunc)ArrayBufpoolFreePage;
    mgr->base.getPageFunc = (SeGetPageFunc)ArrayBufpoolGetPage;
    mgr->base.getPageInitFunc = (SeGetPageInitFunc)ArrayBufpoolGetPageInit;
    mgr->base.getPageWithArgFunc = NULL;
    mgr->base.leavePageFunc = (SeLeavePageFunc)ArrayBufpoolLeavePage;
    mgr->base.leaveVirtualPageFunc = NULL;
    mgr->base.getExtraFunc = (SeGetPageMgrRegisterExtraFunc)ArrayBufpoolExtraFunc;
    mgr->pageSize = seIns->seConfig.pageSize * DB_KIBI;
    mgr->base.sessionCtx = sessionCtx;
    SeInitPageAddrInfo(&mgr->base, mgr->pageSize, SeGetPageCntPerDev(seIns));
}

static StatusInter ArrayBufpoolCreateBufpool(DbMemCtxT *memCtx, BufpoolMgrT *mgr)
{
    BufPoolT *pool = (BufPoolT *)DbDynMemCtxAlloc(memCtx, sizeof(BufPoolT));
    if (SECUREC_UNLIKELY(pool == NULL)) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "bufpool instance:%" PRIu32, mgr->bufPoolNum);
        return OUT_OF_MEMORY_INTER;
    }
    PageBufferMgrT *bufMgr = BufpoolGetPageBufferMgr(mgr);
    (void)memset_s(pool, sizeof(BufPoolT), 0, sizeof(BufPoolT));
    DbSpinInit(&pool->lock);
    pool->scanListLimit = (uint32_t)(bufMgr->capacity * SCAN_PAGE_RATIO_LIMIT);
    pool->priorityListMinimum = (uint32_t)(bufMgr->capacity * PRIORITY_PAGE_RATIO_MIN);
    BufpoolLruInit(pool);
    mgr->bufPool = pool;
    return STATUS_OK_INTER;
}

static inline void ArrayBufpoolDestroyBufpool(DbMemCtxT *memCtx, BufpoolMgrT *mgr)
{
    DbDynMemCtxFree(memCtx, mgr->bufPool);
    mgr->bufPool = NULL;
}

static StatusInter ServerExtraProcedure(SeInstanceT *seIns, DbMemCtxT *memCtx, BufpoolMgrT *mgr)
{
    StatusInter ret = ArrayBufpoolCreateBufpool(memCtx, mgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "create buf pool");
        return ret;
    }

    ret = TrmIdListMapOamapInit(seIns, mgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "ListMap oamap init");
        ArrayBufpoolDestroyBufpool(memCtx, mgr);
        return ret;
    }

    ret = BufpoolRegisterRedo(seIns);
    if (ret != STATUS_OK_INTER) {
        goto ERROR;
    }

    ret = BufpoolStartRingBufProc(mgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "start get page thread");
        goto ERROR;
    }
    return STATUS_OK_INTER;
ERROR:
    TrmIdListMapOmapDestroy(seIns, mgr);
    ArrayBufpoolDestroyBufpool(memCtx, mgr);
    return ret;
}

StatusInter ArrayBufpoolCreatePageMgr(SeInstanceT *seIns, DbMemCtxT *memCtx, PageMgrT **pageMgr, void *sessionCtx)
{
    BufpoolMgrT *mgr = (BufpoolMgrT *)DbDynMemCtxAlloc(memCtx, sizeof(BufpoolMgrT));
    if (mgr == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc BufpoolMgr, size = %zu", sizeof(BufpoolMgrT));
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_s(mgr, sizeof(BufpoolMgrT), 0, sizeof(BufpoolMgrT));
    ArrayBufpoolInitMgr(seIns, mgr, sessionCtx);
    mgr->seIns = seIns;
    if (!DbCommonIsServer() || !SeBufpoolResizeEnable(mgr->seIns)) {
        mgr->descMgr = SeGetBufDescMgr(seIns, seIns->pageDescArray);
        if (!mgr->descMgr) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get bufDescMgr %" PRIu64, seIns->pageDescArray);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        mgr->bufMgr = SeGetPageBufferMgr(seIns, seIns->pageBufShm);
        if (!mgr->bufMgr) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get pageBuf %" PRIu64, seIns->pageBufShm);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
    } else {
        mgr->descMgrAddr = seIns->pageDescArray;
        mgr->bufMgrAddr = seIns->pageBufShm;
    }
    if (!DbCommonIsServer()) {
        *pageMgr = (PageMgrT *)(void *)mgr;
        return STATUS_OK_INTER;
    }
    StatusInter ret = ServerExtraProcedure(seIns, memCtx, mgr);
    if (ret == STATUS_OK_INTER) {
        *pageMgr = (PageMgrT *)(void *)mgr;
        seIns->bufpoolMgr = mgr;
    } else {
        DbDynMemCtxFree(memCtx, mgr);
    }
    return ret;
}

void ArrayBufpoolDestroyPageMgr(SeInstanceT *seIns, PageMgrT *mgr)
{
    BufpoolStopRingBufProc((BufpoolMgrT *)(void *)mgr);
    DbDynMemCtxFree(seIns->seServerMemCtx, mgr);
}

StatusInter ArrayBufpoolAllocPage(BufpoolMgrT *mgr, AllocPageParamT *allocPageParam, PageIdT *pageId)
{
    DB_POINTER2(mgr, pageId);
    DB_ASSERT(mgr->base.type == SE_BUFFER_POOL);

    bool isNew;
    StatusInter ret = SpaceAllocPage(mgr->seIns, BUFFER_POOL_FILE, allocPageParam->spaceId, pageId, &isNew);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "Buffer pool alloc space block");
        return ret;
    }
    uint8_t *page = NULL;
    ret = ArrayBufpoolGetPageInit(mgr, allocPageParam->trmId, *pageId, ENTER_PAGE_INIT, &page);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "Buffer pool get page");
        (void)SpaceFreePage(mgr->seIns, allocPageParam->spaceId, *pageId);
        return ret;
    }
    ArrayBufpoolLeavePage(mgr, *pageId, true);

    return STATUS_OK_INTER;
}

StatusInter ArrayBufpoolFreePage(BufpoolMgrT *mgr, FreePageParamT *param)
{
    DB_POINTER(mgr);

    uint8_t *page = NULL;
    StatusInter ret = GetPage(mgr, param->addr, ENTER_PAGE_FREE | ENTER_PAGE_WRITE, &page);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "free page(%" PRIu32 ", %" PRIu32 ") in Bufpool.", param->addr.blockId, param->addr.deviceId);
        return ret;
    }
#ifndef NDEBUG
    ret = SeFreePagePreCheck((PageHeadT *)page, param->addr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(
            ret, "PreCheck free page(%" PRIu32 ", %" PRIu32 ") in Bufpool.", param->addr.blockId, param->addr.deviceId);
        return ret;
    }
#endif
    SeResetPageHead(page);
    SePageHeadResetRedo(&param->addr);
    ArrayBufpoolLeavePage(mgr, param->addr, true);
    ret = SpaceFreePage(mgr->seIns, param->spaceId, param->addr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "free page(%" PRIu32 ", %" PRIu32 ") from space in Bufpool.", param->addr.blockId,
            param->addr.deviceId);
        return ret;
    }

    return STATUS_OK_INTER;
}

StatusInter ArrayBufpoolGetPage(BufpoolMgrT *mgr, PageIdT pageId, uint8_t **page, PageOptionE option, bool isWrite)
{
    StatusInter ret = GetPage(mgr, pageId, option | (isWrite ? ENTER_PAGE_WRITE : ENTER_PAGE_NORMAL), page);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Unable for bufferpool to get page(%" PRIu32 ", %" PRIu32 ").", pageId.deviceId, pageId.blockId);
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter ArrayBufpoolGetPageWithArg(BufpoolMgrT *mgr, PageIdT pageId, uint32_t option, void *recyArg, uint8_t **page)
{
    StatusInter ret = GetPage(mgr, pageId, option | ENTER_PAGE_WRITE, page);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get page(%" PRIu32 ", %" PRIu32 ") with different arguments", pageId.deviceId, pageId.blockId);
        return ret;
    }

    return STATUS_OK_INTER;
}

StatusInter ArrayBufpoolGetPageInit(
    BufpoolMgrT *mgr, uint32_t trmId, PageIdT pageId, PageOptionE option, uint8_t **page)
{
    StatusInter ret = GetPage(mgr, pageId, option | ENTER_PAGE_WRITE, page);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get page(%" PRIu32 ", %" PRIu32 ") from bufferpool inner", pageId.deviceId, pageId.blockId);
        return ret;
    }

    if ((option & ENTER_PAGE_INIT) != 0) {
        SeInitPageHead(*page, trmId, mgr->pageSize, pageId, true);
        SePageHeadInitRedo(&pageId, trmId);
    }
    return STATUS_OK_INTER;
}

void ArrayBufpoolLeavePage(BufpoolMgrT *mgr, PageIdT pageId, bool isChanged)
{
    BufDescT *desc = ArrayBufpoolGetDesc(mgr, pageId, ENTER_PAGE_NORMAL);
    if (!desc) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "page %" PRIu32 " %" PRIu32 "", pageId.deviceId, pageId.blockId);
        DB_ASSERT(false);
        return;
    }
    (void)DbAtomicDec16(&desc->refCount);
}
