/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: array buffer pool
 * Author:
 * Create: 2025-2025
 */

#ifndef SE_ARRAY_BUFFER_POOL_INNER_H
#define SE_ARRAY_BUFFER_POOL_INNER_H
#include "se_array_buffer_pool.h"
#include "se_buffer_pool_page_buffer.h"
#include "se_page_mgr.h"

#ifdef __cplusplus
extern "C" {
#endif

static inline BufDescT *ArrayBufpoolGetDesc(BufpoolMgrT *mgr, PageIdT addr, PageOptionE option)
{
    BufDescT *desc = BufDescGet(BufpoolGetDescMgr(mgr), SeGetPageCntPerDev(mgr->seIns) * addr.deviceId + addr.blockId);
    if (!desc) {
        return NULL;
    }
    if (((option & ENTER_PAGE_INIT) != 0 || (option & ENTER_PAGE_LOAD) != 0) || !BufDescInited(desc, addr)) {
        desc->pageId = addr;
    }
    DB_ASSERT(DbIsPageIdEqual(desc->pageId, addr));
    return desc;
}

static inline bool ArrayBufpoolBufDescAccessible(const BufDescT *desc)
{
    DB_ASSERT(DbAtomicGet16(&desc->refCount));
    return DbAtomicGet8(&desc->isInLruList) || desc->isResident;
}

static inline void ArrayBufpoolBufDescSetResident(BufPoolT *pool, BufDescT *desc)
{
    DbSpinLock(&pool->lock);
    if (!desc->isResident) {
        desc->isResident = true;
        BufLruRemove(&pool->list[desc->listId], desc);
        desc->listId = LRU_LIST_TYPE_MAX;
    }
    DbSpinUnlock(&pool->lock);
}

static inline void ArrayBufpoolBufDescUnsetResident(BufPoolT *pool, BufDescT *desc)
{
    DbSpinLock(&pool->lock);
    if (desc->isResident) {
        desc->listId = LRU_LIST_NORMAL;
        BufLruAdd(&pool->list[desc->listId], desc, BUF_ADD_TAIL);
        desc->isResident = false;
    }
    DbSpinUnlock(&pool->lock);
}

#ifdef __cplusplus
}
#endif

#endif
