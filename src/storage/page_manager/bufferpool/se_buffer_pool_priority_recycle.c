/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: buffer pool
 * Author:
 * Create: 2023-2023
 * Notes:
 */

#include "se_buffer_pool_priority_recycle.h"
#include "db_hashmap.h"
#include "se_buffer_pool_inner.h"

typedef struct BufDescTemp {
    BufDescT *pageNow;
    BufDescT *pageBefore;
} BufDescTempT;

static inline void BucketLockIfNeed(BufBucketT *bucketNow, const BufBucketT *bucketBefore)
{
    if (bucketNow == bucketBefore || bucketNow == NULL) {  // No need lock while bucketNow is NULL
        return;
    }
    DbRWSpinWLock(&bucketNow->lock);
}

static inline void BucketUnlockIfNeed(
    DbInterProcSpinRWLockFnT *lockFn, BufBucketT *bucketNow, const BufBucketT *bucketBefore)
{
    if (bucketNow == bucketBefore || bucketNow == NULL) {  // No need unlock while bucketNow is NULL
        return;
    }
    DbRWSpinWUnlock(&bucketNow->lock);
}

// check if need update priority page to be removed, if need then lock the bucket
static StatusInter NeedUpdatePageWithLock(BufpoolMgrT *mgr, BufPoolT *bufPool, BufDescTempT *bufDescTemp,
    const BufBucketT *normalPageBucket, bool *needUpdate)
{
    BufDescT *pageNow = bufDescTemp->pageNow;
    BufDescT *pageBefore = bufDescTemp->pageBefore;
    DB_ASSERT(pageNow != NULL);
    BufBucketT *bucketNow = pageNow->attachedBucket;
    if (pageBefore == NULL) {
        // 如果bucketNow和normalPage对应的bucket一致，则不用加锁（上层流程已加锁），否则需要加锁
        BucketLockIfNeed(bucketNow, normalPageBucket);
        if (!BufDescCanRecycleWhileChunkRecycle(mgr, bufPool, pageNow)) {
            BucketUnlockIfNeed(mgr->seRWLockFn, bucketNow, normalPageBucket);
            *needUpdate = false;
        } else {
            // 此处由后续流程解锁：当确定pageNow不会被回收（存在优先级更低的页/normal页更久未使用）或者
            // pageNow已经从bucket上摘下后，会对bucketNow解锁
            *needUpdate = true;
        }
        return STATUS_OK_INTER;
    }
    BufBucketT *bucketBefore = pageBefore->attachedBucket;
    // 同一个bucket不用再加锁（之前的锁未释放），也不用再次检查是否可以回收
    if (bucketNow == bucketBefore) {
        // DescIsRecyclable must be true, so return if pageNow priority lower
        *needUpdate = pageNow->priority < pageBefore->priority;
        return STATUS_OK_INTER;
    }
    BucketLockIfNeed(bucketNow, normalPageBucket);
    if (!BufDescCanRecycleWhileChunkRecycle(mgr, bufPool, pageNow) || pageNow->priority >= pageBefore->priority) {
        BucketUnlockIfNeed(mgr->seRWLockFn, bucketNow, normalPageBucket);
        *needUpdate = false;
    } else {
        // 此处由后续流程解锁：当确定pageNow不会被回收（存在优先级更低的页/normal更久未使用）或者
        // pageNow已经从bucket上摘下后，会对bucketNow解锁
        *needUpdate = true;
    }
    return STATUS_OK_INTER;
}

static StatusInter BufGetDescFromPriorityListInner(
    BufpoolMgrT *mgr, BufPoolT *bufPool, const BufDescT *normalPage, BufDescT *removePage, BufDescT **bufDesc)
{
    *bufDesc = NULL;
    if (removePage == NULL) {
        return STATUS_OK_INTER;
    }
    BufDescLinkedListT *list = &bufPool->list[LRU_LIST_PRIORITY];
    BufBucketT *normalBucket = (normalPage != NULL) ? normalPage->attachedBucket : NULL;
    BufBucketT *bucket = removePage->attachedBucket;
    if (normalPage == NULL || normalPage->operateTime > removePage->operateTime) {
        BufRemoveFromBucket(bucket, removePage);
        // bufDesc从bucket摘下来后解锁bucket，如果和normal page同bucket，则在上层流程中解锁
        BucketUnlockIfNeed(mgr->seRWLockFn, bucket, normalBucket);
        StatusInter ret = BufLruRemoveAndCompression(mgr, list, removePage);
        *bufDesc = removePage;
        return ret;
    }
    // 此时，选择淘汰normal的bufDesc，把removePage对应的bucket解锁
    BucketUnlockIfNeed(mgr->seRWLockFn, bucket, normalBucket);
    return STATUS_OK_INTER;
}

static StatusInter BufGetDescFromPriorityList(
    BufpoolMgrT *mgr, BufPoolT *bufPool, BufDescT *normalPage, BufDescT **bufDesc)
{
    BufDescLinkedListT *list = &bufPool->list[LRU_LIST_PRIORITY];
    BufBucketT *normalBucket = (normalPage != NULL) ? normalPage->attachedBucket : NULL;
    BufDescT *item = BUF_DESC_LIST_TAIL_ENTRY(list);
    BufDescT *removePage = NULL;
    // 遍历优先队列，找到可以淘汰的优先级最低的页面进行淘汰，removePage即为遍历过程中当前找到的可淘汰页面
    for (uint32_t i = 0; i < list->count; ++i) {
        if (!BufDescCanRecycleWhileChunkRecycle(mgr, bufPool, item)) {
            BufDescT *shift = item;
            item = BUF_DESC_PREV_ENTRY(item);
            BufLruShift(list, shift);
            continue;
        }
        if (item->attachedBucket == NULL) {
            // 直接获取优先队列中未attach bucket的bufDesc，此时需要检查之前removePage对应的bucket
            // 和normalBucket是否相同，如果相同，则不用解锁（由上层流程解锁），否则需要解锁
            BucketUnlockIfNeed(mgr->seRWLockFn, (removePage != NULL ? removePage->attachedBucket : NULL), normalBucket);
            StatusInter ret = BufLruRemoveAndCompression(mgr, list, item);
            *bufDesc = item;
            return ret;
        }
        bool needUpdate = false;
        BufDescTempT bufDescTemp = {.pageNow = item, .pageBefore = removePage};
        // 检查是否需要更新removePage，即是否找到了优先级更低的可淘汰页面，如果是则需要对新removePage对应的bucket加锁
        StatusInter ret = NeedUpdatePageWithLock(mgr, bufPool, &bufDescTemp, normalBucket, &needUpdate);
        if (ret != STATUS_OK_INTER) {
            return ret;
        } else if (needUpdate) {
            // 检查更新前的page对应的bucket和更新后的page对应的bucket是否一致，是则不用解锁
            if (removePage != NULL && removePage->attachedBucket != item->attachedBucket) {
                // 检查更新前的page对应的bucket和normalBucket是否一致，是则不用解锁
                BucketUnlockIfNeed(mgr->seRWLockFn, removePage->attachedBucket, normalBucket);
            }
            removePage = item;
        }
        item = BUF_DESC_PREV_ENTRY(item);
        if (removePage != NULL && removePage->priority == LOWEST_PRIORITY_PAGE) {
            break;
        }
    }
    return BufGetDescFromPriorityListInner(mgr, bufPool, normalPage, removePage, bufDesc);
}

/* 1、如果找到normal list中未被attach的bufDesc，则返回该bufDesc
 * 2、如果找到priority list中未被attach的bufDesc，则返回该bufDesc
 * 3、normal list存在可淘汰页normalPage，且此时priority list未达到阈值，则返回normalPage的bufDesc
 * 4、normal list存在可淘汰页normalPage，且此时priority list已达到阈值，则从priority list中找到最低优先级的可淘汰页，
 *    返回两者中最久未使用的bufDesc
 * 5、normal list中未发现可淘汰页，则从priority list中找到最低优先级的可淘汰页，返回对应bufDesc
 * 6、normal list和proirity list中均未发现可淘汰页，则返回NULL
 */
static StatusInter BufGetDescForPriority(BufpoolMgrT *mgr, BufPoolT *bufPool, bool needChkTimeout, BufDescT **bufDesc)
{
    DB_UNUSED(needChkTimeout);
    BufDescLinkedListT *list = &bufPool->list[LRU_LIST_NORMAL];
    BufDescT *shift = NULL;
    BufBucketT *bucket = NULL;
    uint32_t i;
    BufDescT *item = BUF_DESC_LIST_TAIL_ENTRY(list);
    bool needCheckPriorityList = bufPool->list[LRU_LIST_PRIORITY].count > bufPool->priorityListMinimum;
    *bufDesc = NULL;
    for (i = 0; i < list->count; ++i) {
        if (!(BufDescCanRecycleWhileChunkRecycle(mgr, bufPool, item))) {
            // move pinned page to the list head
            shift = item;
            item = BUF_DESC_PREV_ENTRY(item);
            BufLruShift(list, shift);
            continue;
        }
        if (!item->attachedBucket) {
            break;
        }

        bucket = item->attachedBucket;
        DbRWSpinWLock(&bucket->lock);
        // Check again in bucket lock.
        if (!(BufDescCanRecycleWhileChunkRecycle(mgr, bufPool, item))) {
            DbRWSpinWUnlock(&bucket->lock);
            item = BUF_DESC_PREV_ENTRY(item);
            continue;
        }
        if (needCheckPriorityList) {
            StatusInter ret = BufGetDescFromPriorityList(mgr, bufPool, item, bufDesc);
            if (ret != STATUS_OK_INTER) {
                DbRWSpinWUnlock(&bucket->lock);
                return ret;
            } else if (*bufDesc != NULL) {
                DbRWSpinWUnlock(&bucket->lock);
                return STATUS_OK_INTER;
            }
        }
        BufRemoveFromBucket(bucket, item);
        DbRWSpinWUnlock(&bucket->lock);
        break;
    }
    // normal list中所有的item都不能回收，则需要从priority list中找可淘汰页
    if (i == list->count) {
        return BufGetDescFromPriorityList(mgr, bufPool, NULL, bufDesc);
    }
    // 找到适合淘汰的Desc
    StatusInter ret = BufLruRemoveAndCompression(mgr, list, item);
    *bufDesc = item;
    return ret;
}

StatusInter BufpoolEnqueueByMultiPriority(BufpoolMgrT *mgr, PageOptionE option, void *enqueArg)
{
    DB_UNUSED(option);
    BufEnqueArgT *arg = (BufEnqueArgT *)enqueArg;
    PriorityRecyArgT *priorityRecyArg = (PriorityRecyArgT *)arg->recyArg;
    BufPoolT *bufPool = GetBufpoolByPageId(mgr, arg->pageId);
    BufBucketT *bucket = arg->bufDesc->attachedBucket;

    DbRWSpinWLock(&bucket->lock);
    if (priorityRecyArg != NULL && priorityRecyArg->flag == SE_RECYCLE_PRIORITY_MULTI) {
        arg->bufDesc->priority = priorityRecyArg->priority;
        DB_ASSERT(arg->bufDesc->listId == LRU_LIST_PRIORITY);
    } else {
        arg->bufDesc->priority = NORMAL_PAGE;
        DB_ASSERT(arg->bufDesc->listId != LRU_LIST_PRIORITY);
    }
    DbRWSpinWUnlock(&bucket->lock);
    if (!arg->bufDesc->isResident) {
        BufLruAdd(&bufPool->list[arg->bufDesc->listId], arg->bufDesc, BUF_ADD_HEAD);
    }
    return STATUS_OK_INTER;
}

StatusInter BufpoolRecycleByMultiPriority(
    BufpoolMgrT *mgr, BufGetDescArgsT *args, void *recyArg, bool reTry, BufDescT **bufDesc)
{
    DB_UNUSED(recyArg);
    DB_UNUSED(reTry);
    BufPoolT *bufPool = GetBufpoolByPageId(mgr, args->pageId);
    BufDescT *item = NULL;
    StatusInter ret = STATUS_OK_INTER;
    DbSpinLock(&bufPool->lock);
    uint32_t option = args->options;
    if ((option & ENTER_PAGE_SCAN) != 0) {
        if (BufChkScanLimit(bufPool)) {
            ret = BufGetDescFromList(mgr, bufPool, &bufPool->list[LRU_LIST_STATS_SCAN], false, &item);
        } else {
            ret = BufGetDescForPriority(mgr, bufPool, false, &item);
            if (ret == STATUS_OK_INTER && item == NULL) {
                ret = BufGetDescFromList(mgr, bufPool, &bufPool->list[LRU_LIST_STATS_SCAN], false, &item);
            }
        }
    } else {
        ret = BufGetDescFromList(mgr, bufPool, &bufPool->list[LRU_LIST_STATS_SCAN], true, &item);
        if (ret == STATUS_OK_INTER && item == NULL) {
            ret = BufGetDescForPriority(mgr, bufPool, false, &item);
        }
    }
    DbSpinUnlock(&bufPool->lock);
    if (ret == STATUS_OK_INTER) {
        *bufDesc = item;
    }
    return ret;
}

void SetBufDescListByMultiPriority(uint32_t options, const void *recyArg, BufDescT *bufDesc)
{
    const PriorityRecyArgT *priorityRecyArg = (const PriorityRecyArgT *)recyArg;
    if (priorityRecyArg != NULL && priorityRecyArg->flag == SE_RECYCLE_PRIORITY_MULTI) {
        bufDesc->priority = priorityRecyArg->priority;
        bufDesc->listId = LRU_LIST_PRIORITY;
        return;
    }
    bufDesc->priority = NORMAL_PAGE;
    bufDesc->listId = ((options & ENTER_PAGE_SCAN) != 0) ? LRU_LIST_STATS_SCAN : LRU_LIST_NORMAL;
}

static bool IsBufDescNeedSwitchLRUList(const BufDescT *bufDesc, uint32_t options, const void *recyArg)
{
    // need switch from scan list to other list
    if (bufDesc->listId == LRU_LIST_STATS_SCAN && options == ENTER_PAGE_NORMAL) {
        return true;
    }

    // priorityRecyArg invalid, bufDesc should be normal
    const PriorityRecyArgT *priorityRecyArg = (const PriorityRecyArgT *)recyArg;
    if (recyArg == NULL || priorityRecyArg->flag != SE_RECYCLE_PRIORITY_MULTI) {
        return bufDesc->priority != NORMAL_PAGE;
    }

    // bufDesc is priority but priorityRecyArg is normal
    if (bufDesc->priority != NORMAL_PAGE) {
        return priorityRecyArg->priority == NORMAL_PAGE;
    }

    // bufDesc is NORMAL_PAGE but priorityRecyArg is not
    return priorityRecyArg->priority != NORMAL_PAGE;
}

StatusInter BufDescSwitchLRUListByMultiPriority(
    BufpoolMgrT *mgr, BufPoolT *bufPool, uint32_t options, const void *recyArg, BufDescT *bufDesc)
{
    if (IsBufDescNeedSwitchLRUList(bufDesc, options, recyArg)) {
        DbSpinLock(&bufPool->lock);
        if (!IsBufDescNeedSwitchLRUList(bufDesc, options, recyArg)) {
            DbSpinUnlock(&bufPool->lock);
            return STATUS_OK_INTER;
        }
        DbSpinLock(&bufDesc->lock);
        BufLruRemove(&bufPool->list[bufDesc->listId], bufDesc);
        const PriorityRecyArgT *priorityRecyArg = (const PriorityRecyArgT *)recyArg;
        if (priorityRecyArg != NULL && priorityRecyArg->flag == SE_RECYCLE_PRIORITY_MULTI) {
            bufDesc->priority = priorityRecyArg->priority;
        } else {
            bufDesc->priority = NORMAL_PAGE;
        }
        bufDesc->listId = bufDesc->priority == NORMAL_PAGE ? LRU_LIST_NORMAL : LRU_LIST_PRIORITY;
        DbSpinUnlock(&bufDesc->lock);
        BufLruAdd(&bufPool->list[bufDesc->listId], bufDesc, BUF_ADD_HEAD);
        DbSpinUnlock(&bufPool->lock);
    }
    return STATUS_OK_INTER;
}

StatusInter BufPreProcessForMultiPriority(BufpoolMgrT *mgr, void *recyArg, BufDescT *bufDesc)
{
    DB_UNUSED(mgr);
    // update priority
    if (recyArg != NULL && ((PriorityRecyArgT *)recyArg)->flag == SE_RECYCLE_PRIORITY_MULTI) {
        bufDesc->priority = ((PriorityRecyArgT *)recyArg)->priority;
    }
    // high priority page should be in high priority list
    DB_ASSERT(bufDesc->listId != LRU_LIST_PRIORITY || bufDesc->priority != NORMAL_PAGE);
    DB_ASSERT(bufDesc->listId != NORMAL_PAGE || bufDesc->priority == NORMAL_PAGE);
    return STATUS_OK_INTER;
}
