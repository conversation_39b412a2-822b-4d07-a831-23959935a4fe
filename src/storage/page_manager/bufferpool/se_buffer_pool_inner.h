/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: buffer pool inner head, include buf pool, buf desc, buf latch
 * Author:
 * Create: 2025-2-26
 */

#ifndef SE_BUFFER_POOL_INNER_H
#define SE_BUFFER_POOL_INNER_H

#include "se_buffer_pool.h"
#include "se_buffer_pool_page_buffer.h"
#include "se_space_virtual_disk.h"

#ifdef __cplusplus
extern "C" {
#endif

#define TRMID_ID_STR_LEN 11
#define PAGEID_MAP_BUFFPOOLID 32
#define TRY_TIMES_WARN_PERIOD 10
#define TRY_TIMES_ERROR_PERIOD 200000
#define HOLD_TIME_THRESHOLD 90
#define CLT_TRY_TIMES_GET_PAGE_WARN 10000
#define FLOAT_NUM_HUNDRED (100.0)

typedef enum BufLoadStatus {
    BUF_NEED_LOAD = 0x00,
    BUF_IS_LOADED = 0x01,
    BUF_LOAD_FAILED = 0x02,
} BufLoadStatusE;

typedef enum BufGetPageStatus {
    GET_PAGE,
    LEAVE_PAGE,
    NO_OPT_REFCOUNT,
} BufGetPageStatusE;

struct BufBucket {
    DbRWSpinLockT lock;
    uint32_t count;
    BufDescT *first;
    uint32_t firstDescId;
};

typedef struct BufLruList {
    TagLinkedListT linkedList;
    uint32_t count;  // buffer count in Lru queue
} BufLruListT;

// 每个chunk维护自己hwm以及capacity
typedef struct BufChunk BufChunkT;

// 和BufPoolT为1:1的关系, 每个BufPoolT维护一个
typedef struct BpChunkMgr BpChunkMgrT;

// 和TagBufpoolMgrT BufpoolMgrT为1:1的关系, 可以管理多个BufPoolT实例
typedef struct BpResizeMgr BpResizeMgrT;

#define BUFFER_POOL_INVALID_REHASH_WRAPPER                      \
    (RehashBucketMgrWrapperT)                                   \
    {                                                           \
        DB_INVALID_UINT64, DB_INVALID_UINT64, DB_INVALID_UINT64 \
    }

typedef struct BufPool {
    DbSpinLockT lock;
    uint32_t index;
    uint32_t reserve;
    uint32_t scanListLimit;
    uint32_t highPriorityListMinimum;
    uint32_t priorityListMinimum;  // the minimum length of list that contains priority page
    uint64_t currBucketMgrShm;
    uint64_t newBucketMgrShm;
    // all lru list
    BufDescListT list[LRU_LIST_TYPE_MAX];
    DbOamapT *tableMap;  // key is tableId, value is the number of pages
} BufPoolT;

typedef struct BufGetDescArgs {
    PageIdT pageId;
    uint32_t options;
} BufGetDescArgsT;

typedef StatusInter (*BufpoolLRUEnqueueFunc)(BufpoolMgrT *mgr, PageOptionE option, void *enqueArg);
typedef StatusInter (*BufpoolRecycleFunc)(
    BufpoolMgrT *mgr, BufGetDescArgsT *args, void *recyArg, bool reTry, BufDescT **bufDesc);
typedef StatusInter (*BufPreprocessFunc)(BufpoolMgrT *mgr, void *recyArg, BufDescT *bufDesc);
typedef void (*BufSetBufDescListFunc)(uint32_t options, const void *recyArg, BufDescT *bufDesc);
typedef StatusInter (*BufDescSwitchLRUListFunc)(
    BufpoolMgrT *mgr, BufPoolT *bufPool, uint32_t options, const void *recyArg, BufDescT *bufDesc);

#define SCAN_PAGE_TIMEOUT 3
#define SCAN_PAGE_RATIO_LIMIT 0.2

typedef struct BufEnqueArg {
    void *recyArg;
    BufDescT *bufDesc;
    PageIdT pageId;
} BufEnqueArgT;

// buffer pool table map清理函数
typedef void (*TableMapClearFn)(void *ctx, void *tableMap, uint32_t tableId);
// buffer pool table map 获取page count函数
typedef uint32_t (*TableMapGetPageCntFn)(void *ctx, void *tableMap, uint32_t tableId);
typedef StatusInter (*TableMapIncPageCntFn)(void *ctx, void *tableMap, uint32_t tableId);
typedef void (*TableMapDecPageCntFn)(void *ctx, void *tableMap, uint32_t tableId);

typedef struct {
    TableMapClearFn tableMapClearFn;
    TableMapGetPageCntFn tableMapGetPageCntFn;
    TableMapIncPageCntFn tableMapIncPageCntFn;
    TableMapDecPageCntFn tableMapDecPageCntFn;
} TableMapFnsT;

typedef struct TagBpRingBufMgr BpRingBufMgrT;

typedef struct TagBufferPoolResizeCtx BpResizeCtxT;

struct TagBufpoolMgrT {
    PageMgrT base;
    DbSpinLockT lock;
    uint32_t pageSize;
    SeInstanceT *seIns;
    BufPoolT *bufPool;
    uint32_t bufPoolNum;  // 多个bufferpool实例的个数
    BufpoolLRUEnqueueFunc lruEnqueueFunc;
    BufpoolRecycleFunc recycleFunc;
    BufPreprocessFunc preprocessFunc;
    BufSetBufDescListFunc setBufDescListFunc;
    BufDescSwitchLRUListFunc switchLRUListFunc;
    MemUtilsT *memUtils;
    DbSpinlockFnT *seLockFn;
    TableMapFnsT tableMapFns;
    DbInterProcSpinRWLockFnT *seRWLockFn;
    BpResizeCtxT *resizeCtx;
    DbOamapT *trmIdListMap;  // key is trmId, value is not used.
    bool enableSplitChunk;   // 静态值，取决于chunkSize是否配置为非0，不会动态变化
    BpResizeMgrT *resizeMgr;
    BpRingBufMgrT *ringbufMgr;
    MemUtilsT memUtilsClt;
    union {
        BufDescMgrT *descMgr;
        volatile uint64_t descMgrAddr;
    };
    union {
        PageBufferMgrT *bufMgr;
        volatile uint64_t bufMgrAddr;
    };
};
static_assert((offsetof(BufpoolMgrT, descMgrAddr) % sizeof(uint64_t)) == 0,
    "field descMgrAddr in BufpoolMgrT must be aligned by 8 bytes");
static_assert((offsetof(BufpoolMgrT, bufMgrAddr) % sizeof(uint64_t)) == 0,
    "field bufMgrAddr in BufpoolMgrT must be aligned by 8 bytes");

#define REF_COUNT_INIT 1
#define BUFFER_POOL_MAX_POOL_NUM 32

static inline bool BufChkTimeout(const BufDescT *desc, const bool needChkTimeout)
{
    return !needChkTimeout || (DbToSeconds(DbRdtsc()) - desc->operateTime > SCAN_PAGE_TIMEOUT);
}

static inline bool BufChkScanLimit(const BufPoolT *bufPool)
{
    return (bufPool->list[LRU_LIST_STATS_SCAN].count + 1) > bufPool->scanListLimit;
}

// 对外非bufferpool模块可能暴露的接口，结构体
typedef enum BufAddPos {
    BUF_ADD_HEAD = 0,
    BUF_ADD_OLD = 1,
    BUF_ADD_TAIL = 2,
} BufAddPosT;

static inline void BufLruAdd(BufDescListT *list, BufDescT *bufDesc, BufAddPosT pos)
{
    DB_ASSERT(!bufDesc->lruLinkedNode.prev && !bufDesc->lruLinkedNode.next);

    if (pos == BUF_ADD_HEAD || bufDesc->isPinned) {
        BufDescLinkedListAddHead(list, bufDesc);
    } else if (pos == BUF_ADD_TAIL) {
        BufDescLinkedListAddTail(list, bufDesc);
    } else {  // ONLY BUF_ADD_HEAD & BUF_ADD_TAIL here, should not call this function with BUF_ADD_OLD
        DB_ASSERT(false);
    }
    DbAtomicSet8(&bufDesc->isInLruList, 1);
}

static inline void BufLruRemove(BufDescListT *list, BufDescT *bufDesc)
{
    BufDescLinkedListRemove(list, bufDesc);
    DbAtomicSet8(&bufDesc->isInLruList, 0);
}

static inline void BufLruShift(BufDescListT *list, BufDescT *bufDesc)
{
    BufLruRemove(list, bufDesc);
    BufLruAdd(list, bufDesc, BUF_ADD_HEAD);
}

static inline void BufLruSwitch(BufDescListT *prev, BufDescListT *next, BufDescT *bufDesc)
{
    BufDescLinkedListRemove(prev, bufDesc);
    BufLruAdd(next, bufDesc, BUF_ADD_HEAD);
}

static inline StatusInter BufWriteToVirtualDisk(BufpoolMgrT *mgr, BufDescT *item)
{
    VDWriteModeE mode = VD_DISCARD_CHANGE;
    if (item->isWriting || item->isPinned) {
        mode = VD_COMMIT_CHANGE;
    }
    if (!DbIsPageIdValid(((PageHeadT *)item->page)->addr)) {
        mode = VD_FREE;
    }
    return SeVirtualDiskWriteBlock(mgr->seIns, item->pageId, (uint8_t *)item->page, mode);
}

static inline StatusInter BufLruRemoveAndCompression(BufpoolMgrT *mgr, BufDescListT *list, BufDescT *bufDesc)
{
    // page will compress if the config is on, page will mark as not writing after flush.
    StatusInter ret = STATUS_OK_INTER;
    if (SpaceCompressionIsEnable(mgr->seIns) && DbIsPageIdValid(bufDesc->pageId)) {
        ret = BufWriteToVirtualDisk(mgr, bufDesc);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        bufDesc->isWriting = false;
    }
    BufLruRemove(list, bufDesc);
    return ret;
}

StatusInter BufForceRecycleBufdesc(
    BufpoolMgrT *mgr, BufPoolT *bufPool, BufDescListT *list, BufDescT **bufDesc, uint32_t targetChunkIdx);

static inline void BufpoolLruInit(BufPoolT *bufPool)
{
    bufPool->list[LRU_LIST_NORMAL].count = 0;
    BufDescLinkedListInit(&bufPool->list[LRU_LIST_NORMAL]);
    // Scan LRU list
    bufPool->list[LRU_LIST_STATS_SCAN].count = 0;
    BufDescLinkedListInit(&bufPool->list[LRU_LIST_STATS_SCAN]);
    // priority LRU list
    bufPool->list[LRU_LIST_PRIORITY].count = 0;
    BufDescLinkedListInit(&bufPool->list[LRU_LIST_PRIORITY]);
    // high priority LRU list
    bufPool->list[LRU_LIST_HIGH_PRIORITY].count = 0;
    BufDescLinkedListInit(&bufPool->list[LRU_LIST_HIGH_PRIORITY]);
}

StatusInter CkptTriggerAndCheckTimeout(SeInstanceT *seIns, uint64_t *startTime, uint64_t *triggerWarnTimes);

StatusInter TrmIdListMapOamapInit(SeInstanceT *seIns, BufpoolMgrT *mgr);
void TrmIdListMapOmapDestroy(SeInstanceT *seIns, BufpoolMgrT *mgr);

StatusInter BufpoolRegisterRedo(SeInstanceT *seIns);

SO_EXPORT StatusInter BufGetDescFromList(
    BufpoolMgrT *mgr, BufPoolT *bufPool, BufDescListT *list, bool needChkTimeout, BufDescT **bufDesc);
SO_EXPORT BufPoolT *GetBufpoolByPageId(BufpoolMgrT *mgr, PageIdT pageId);

bool DescIsRecyclable(const BufpoolMgrT *mgr, const BufDescT *desc);

static inline BufDescMgrT *BufpoolGetDescMgr(BufpoolMgrT *mgr)
{
    if (!DbCommonIsServer() || !SeBufpoolResizeEnable(mgr->seIns)) {
        return mgr->descMgr;
    }
    return SeGetBufDescMgr(mgr->seIns, DbAtomicGet64(&mgr->descMgrAddr));
}

static inline PageBufferMgrT *BufpoolGetPageBufferMgr(BufpoolMgrT *mgr)
{
    if (!DbCommonIsServer() || !SeBufpoolResizeEnable(mgr->seIns)) {
        return mgr->bufMgr;
    }
    return SeGetPageBufferMgr(mgr->seIns, DbAtomicGet64(&mgr->bufMgrAddr));
}

static inline bool BufpoolAtMaxCapacity(PageMgrT *mgr)
{
    if (mgr->type != SE_BUFFER_POOL) {
        return false;
    }
    BufpoolMgrT *bufPoolMgr = (BufpoolMgrT *)(void *)mgr;
    BufDescMgrT *descMgr = BufpoolGetDescMgr(bufPoolMgr);
    return descMgr->hwm >= descMgr->capacity;
}

Status BufferpoolLoadTableDoTrmId(DbOamapT *trmIdListMap, uint32_t trmId, bool isAdd);

#ifdef __cplusplus
}
#endif

#endif
