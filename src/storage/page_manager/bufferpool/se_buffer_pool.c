/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: buffer pool, pageManager life mgr init-reset-destroy, bucket op
 * Author:
 * Create: 2023-2023
 * Notes:
 */

#include "se_buffer_pool.h"
#include "adpt_define.h"
#include "adpt_sleep.h"
#include "db_dyn_load.h"
#include "db_hash.h"
#include "db_inter_process_mgr.h"
#include "db_sysapp_context.h"
#include "gmc_errno.h"
#include "se_buffer_pool_page_buffer.h"
#include "se_buffer_pool_file_map.h"
#include "se_buffer_pool_inner.h"
#include "se_buffer_pool_pre_loader.h"
#include "se_buffer_pool_priority.h"
#include "se_buffer_pool_resize.h"
#include "se_buffer_pool_ringbuffer.h"
#include "se_buffer_pool_table_recycle.h"
#include "se_capacity_def_inner.h"
#include "se_ckpt.h"
#include "se_common.h"
#include "se_database.h"
#include "se_datafile.h"
#include "se_db_file_device.h"
#include "se_file.h"
#include "se_hash_buffer_pool.h"
#include "se_log.h"
#include "db_resource_session_pub.h"
#include "se_replay.h"
#include "se_redo.h"
#include "se_redo_inner.h"
#include "se_space_virtual_disk.h"
#include "se_array_buffer_pool.h"
#include "se_buffer_pool_bucket.h"

static BufDescT g_gmdbInitBufDesc = {0};

ALWAYS_INLINE bool DescIsRecyclable(const BufpoolMgrT *mgr, const BufDescT *desc)
{
    if (DbAtomicGet16(&desc->refCount) != 0) {
        return false;
    }

    if (mgr->resizeCtx && desc->bufId >= mgr->resizeCtx->recycleThreshold) {
        return false;
    }

    // isDirty标记为false并不一定代表脏页已经落盘。为了避免数据刷盘阻塞业务访问和加速刷盘，
    // page在刷盘之前会存在一份checkpoint buffer中的拷贝。拷贝之后为了使新的page修改能够被识别需要将isDirty置为false。
    // 而此时数据页还没有真正落盘，所以增加inCkptBuf标记位，当isDirty和inCkptBuf都未0时代表数据真正落盘。
    return (desc->isDirty == false) && (desc->inCkptBuf == 0) && !desc->isPinned;
}

void BufpoolDestroy(SeInstanceT *seIns)
{
    HashBufpoolDestroyPageMgr(seIns, seIns->bufpoolMgr, false);
}

SO_EXPORT_FOR_TS StatusInter BufpoolCreatePageMgr(
    SeInstanceT *seIns, DbMemCtxT *memCtx, PageMgrT **pageMgr, void *sessionCtx)
{
    DB_POINTER2(seIns, pageMgr);
    // 当前只有光启会用，暂时指定为按需模式，客户端和服务端create array bufferpool.
    if (SpaceCompressionIsEnable(seIns)) {
        return ArrayBufpoolCreatePageMgr(seIns, memCtx, pageMgr, sessionCtx);
    } else if (SeGetPersistMode() == PERSIST_ON_DEMAND) {
        // 按需持久化开启bufpool，必须开启压缩区
        SE_ERROR(CONFIG_ERROR_INTER, "bufferpool on-demand persist without compress space");
        return CONFIG_ERROR_INTER;
    }

    // hash buffer pool 暂不支持直连读写，客户端不能创建
    if (!DbCommonIsServer()) {
        // client of bufferpool under inrement persistence is empty.
        *pageMgr = NULL;
        return STATUS_OK_INTER;
    }
    return HashBufpoolCreatePageMgr(seIns, memCtx, pageMgr, sessionCtx);
}

BufpoolMgrT *BufpoolGetPageMgr(SeInstanceT *seIns)
{
    DB_POINTER(seIns);

    static bool init = false;
    if (!init) {
        (void)RedoLogReplayRegister(seIns->redoMgr, REDO_INIT_PAGE_HEAD, SePageHeadInitReplay);
        (void)RedoLogReplayRegister(seIns->redoMgr, REDO_RESET_PAGE_HEAD, SePageHeadResetReplay);
        init = true;
    }

    return seIns->bufpoolMgr;
}

SO_EXPORT_FOR_TS void BufpoolDestroyPageMgr(SeInstanceT *seIns, PageMgrT *pageMgr)
{
    DB_UNUSED(pageMgr);
    if (SpaceCompressionIsEnable(seIns)) {
        ArrayBufpoolDestroy(seIns);
        ArrayBufpoolDestroyPageMgr(seIns, pageMgr);
        return;
    } else if (SeGetPersistMode() == PERSIST_ON_DEMAND) {
        // 按需持久化开启bufpool，必须开启压缩区
        SE_ERROR(CONFIG_ERROR_INTER, "bufferpool on-demand persist without compress space");
        return;
    }
    HashBufpoolDestroyPageMgr(seIns, (BufpoolMgrT *)pageMgr, false);
}

BufPoolT *GetBufpoolByPageId(BufpoolMgrT *mgr, PageIdT pageId)
{
    if (mgr->bufPoolNum == 1) {
        return &mgr->bufPool[0];
    }
    uint32_t bufPoolId = (((uint64_t)pageId.deviceId << PAGEID_MAP_BUFFPOOLID) + pageId.blockId) % mgr->bufPoolNum;
    if (bufPoolId > mgr->bufPoolNum) {
        DB_LOG_ERROR(OUT_OF_MEMORY_INTER, "bufPoolId: %" PRIu32 ", bufPoolNum: %" PRIu32, bufPoolId, mgr->bufPoolNum);
        DB_ASSERT(false);
    }
    return &mgr->bufPool[bufPoolId];
}

StatusInter BufGetDescFromList(
    BufpoolMgrT *mgr, BufPoolT *bufPool, BufDescListT *list, bool needChkTimeout, BufDescT **bufDesc)
{
    StatusInter ret = STATUS_OK_INTER;
    BufDescT *shift;
    BufBucketT *bucket;
    uint32_t i;

    BufDescT *item = BUF_DESC_LIST_TAIL_ENTRY(list);
    for (i = 0; i < list->count; i++) {
        if (!(DescIsRecyclable(mgr, item) && BufChkTimeout(item, needChkTimeout))) {
            // move pinned page to the list head
            shift = item;
            item = BUF_DESC_PREV_ENTRY(item);
            BufLruShift(list, shift);
            continue;
        }

        if (!item->attachedBucket) {
            break;
        }

        bucket = item->attachedBucket;
        DbRWSpinWLock(&bucket->lock);
        // Check again in bucket lock.
        if (!(DescIsRecyclable(mgr, item) && BufChkTimeout(item, needChkTimeout))) {
            DbRWSpinWUnlock(&bucket->lock);
            item = BUF_DESC_PREV_ENTRY(item);
            continue;
        }

        if (SpaceCompressionIsEnable(mgr->seIns) && DbIsPageIdValid(item->pageId)) {
            ret = BufWriteToVirtualDisk(mgr, item);
            if (ret != STATUS_OK_INTER) {
                DbRWSpinWUnlock(&bucket->lock);
                continue;
            }
            item->isWriting = false;
        }

        BufpoolBucketRemoveDesc(bucket, item);
        DbRWSpinWUnlock(&bucket->lock);
        break;
    }

    if (i == list->count) {
        *bufDesc = NULL;
        return STATUS_OK_INTER;
    }
    // 找到适合淘汰的Desc
    // 当页被换出时，需要把页面信息同步到压缩区
    BufLruRemove(list, item);
    *bufDesc = item;
    return ret;
}

uint32_t BufTrmMapGetTrmIdHash(uint32_t trmId)
{
    char trmIdStr[TRMID_ID_STR_LEN] = {0};
    (void)sprintf_s(trmIdStr, TRMID_ID_STR_LEN, "%" PRIu32, trmId);
    return DbStrToHash32(trmIdStr);
}

Status BufferpoolLoadTableDoTrmId(DbOamapT *trmIdListMap, uint32_t trmId, bool isAdd)
{
    Status ret = GMERR_OK;
    uint32_t hash = BufTrmMapGetTrmIdHash(trmId);
    if (isAdd) {
        ret = DbOamapInsert(trmIdListMap, hash, (void *)(uintptr_t)trmId, NULL, NULL);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "insert trmId: %" PRIu32, trmId);
        }
    } else {
        ret = (DbOamapRemove(trmIdListMap, hash, (const void *)(uintptr_t)trmId) == NULL) ? GMERR_NO_DATA : GMERR_OK;
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "remove trmId: %" PRIu32, trmId);
        }
    }
    return ret;
}

Status BufferpoolLoadTableTrmIdOpImpl(SeInstanceT *seIns, uint32_t trmId, bool isAdd)
{
    return BufferpoolLoadTableDoTrmId(seIns->bufpoolMgr->trmIdListMap, trmId, isAdd);
}

Status BufferpoolLoadTableTrmIdPageOpImpl(SeInstanceT *seIns, ShmemPtrT metaShmAddr, bool isAdd)
{
    DB_POINTER(seIns);
    PageIdT metaPageId = *(PageIdT *)(void *)(&metaShmAddr);
    PageHeadT *pageHead = NULL;
    PageMgrT *pageMgr = seIns->pageMgr;
    StatusInter innerRet =
        BufpoolGetPage((BufpoolMgrT *)pageMgr, metaPageId, (uint8_t **)&pageHead, ENTER_PAGE_NORMAL, false);
    if (innerRet != STATUS_OK_INTER) {
        SE_ERROR(innerRet, "free page in Bufpool.");
        return DbGetExternalErrno(innerRet);
    }

    uint32_t trmId = pageHead->trmId;
    BufpoolLeavePage((BufpoolMgrT *)(void *)pageMgr, metaPageId, false);
    return BufferpoolLoadTableTrmIdOpImpl(seIns, trmId, isAdd);
}

SO_EXPORT_FOR_TS StatusInter BufpoolInit(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx)
{
    DB_POINTER2(seIns, seTopShmMemCtx);
    if (SpaceCompressionIsEnable(seIns)) {
        return ArrayBufpoolInit(seIns, seTopShmMemCtx);
    } else if (SeGetPersistMode() == PERSIST_ON_DEMAND) {
        // 按需持久化开启bufpool，必须开启压缩区
        SE_ERROR(CONFIG_ERROR_INTER, "bufferpool on-demand persist without compress space");
        return CONFIG_ERROR_INTER;
    }
    g_gmdbInitBufDesc.tableId = DB_INVALID_ID32;
    g_gmdbInitBufDesc.listId = LRU_LIST_TYPE_MAX;
    g_gmdbInitBufDesc.bufPoolId = DB_INVALID_ID8;
    g_gmdbInitBufDesc.page = NULL;
    g_gmdbInitBufDesc.priority = NORMAL_PAGE;
    g_gmdbInitBufDesc.lruLinkedNode.prev = NULL;
    g_gmdbInitBufDesc.lruLinkedNode.next = NULL;
    g_gmdbInitBufDesc.pageId = SE_INVALID_PAGE_ADDR;
    return STATUS_OK_INTER;
}

StatusInter BufAllocHwm(BufpoolMgrT *mgr, BufPoolT *bufPool, BufDescT **bufDesc)
{
    DbSpinLock(&mgr->lock);
    BufDescMgrT *descMgr = BufpoolGetDescMgr(mgr);
    if (mgr->resizeCtx && descMgr->hwm >= mgr->resizeCtx->recycleThreshold) {
        DbSpinUnlock(&mgr->lock);
        return OUT_OF_MEMORY_INTER;
    }
    PageBufferMgrT *bufMgr = BufpoolGetPageBufferMgr(mgr);
    uint32_t descId = DB_INVALID_UINT32;
    StatusInter ret = BufDescAllocDesc(descMgr, &descId);
    if (ret != STATUS_OK_INTER) {
        DbSpinUnlock(&mgr->lock);
        return ret;
    }
    BufDescT *desc = BufDescGet(descMgr, descId);
    ret = PageBufferAlloc(bufMgr, &desc->bufId);
    if (ret != STATUS_OK_INTER) {
        BufDescFreeDesc(descMgr, descId);
        DbSpinUnlock(&mgr->lock);
        return ret;
    }
    desc->bufPoolId = (uint8_t)bufPool->index;
    desc->page = (void *)PageBufferGet(bufMgr, desc->bufId);
    if (desc->page == NULL) {
        DB_ASSERT(false);
        SE_ERROR(INTERNAL_ERROR_INTER, "Desc %" PRIu32 " Buffer %" PRIu32 " Empty", desc->descId, desc->bufId);
        return INTERNAL_ERROR_INTER;
    }
    DbSpinUnlock(&mgr->lock);
    *bufDesc = desc;
    return STATUS_OK_INTER;
}

StatusInter CkptTriggerAndCheckTimeout(SeInstanceT *seIns, uint64_t *startTime, uint64_t *triggerWarnTimes)
{
    Status ret = CkptTrigger(seIns, CKPT_MODE_INC, true, WAIT_MSECONDS_FOR_CHECKPOINT);
    // ret != 0 : influencing bufferpool get pages from disk.
    if (ret == GMERR_OUT_OF_MEMORY) {
        SE_ERROR(ret, "checkpoint is out of memory");
        return DbGetStatusInterErrno(ret);
    }
    if (ret == GMERR_DATABASE_NOT_AVAILABLE) {
        SE_ERROR(ret, "database currently unavailable");
        return DbGetStatusInterErrno(ret);
    }
    uint64_t costTime = DbToSeconds(DbRdtsc() - *startTime);
    uint64_t triggerInterval = 60;
    if (costTime >= triggerInterval) {  // 60s
        (*triggerWarnTimes)++;
        DB_LOG_WARN(STATUS_OK_INTER,
            "potential acquire new desc timeout, waitTime: %" PRIu64 "(s), total findTime: %" PRIu64 "(s)", costTime,
            (*triggerWarnTimes) * triggerInterval);
        (*startTime) = DbRdtsc();
        return STATUS_OK_INTER;
    }
    return STATUS_OK_INTER;
}

static inline void BufDescReset(BufDescT *bufDesc)
{
    DB_POINTER(bufDesc);
    uint32_t bufId = bufDesc->bufId;
    uint8_t bufpoolIndex = bufDesc->bufPoolId;
    void *page = bufDesc->page;
    uint32_t descId = bufDesc->descId;
    (*bufDesc) = g_gmdbInitBufDesc;
    bufDesc->bufId = bufId;
    bufDesc->bufPoolId = bufpoolIndex;
    bufDesc->page = page;
    bufDesc->descId = descId;
}

void BufpoolCheckDescStatus(BufpoolMgrT *mgr, bool ignoreHoldTime)
{
    // 这个接口没有加锁，主要用于长期无法换入换出时获取停留时间非常长的页面，对于短期停留的页面可能会出现和实际情况不一致的情况
    double nowTime = DbToSeconds(DbRdtsc());
    BufDescMgrT *descMgr = BufpoolGetDescMgr(mgr);
    uint32_t iterLimit = descMgr->hwm;
    BufDescT *cursor = NULL;
    for (uint32_t i = 0; i < iterLimit; ++i) {
        cursor = BufDescGet(descMgr, i);
        uint64_t holdTime = (uint64_t)(nowTime - cursor->operateTime);
        if (cursor->page != NULL && !cursor->isPinned && !cursor->isResident && cursor->refCount != 0 &&
            (holdTime >= HOLD_TIME_THRESHOLD || ignoreHoldTime)) {
            PageIdT addr = cursor->pageId;
            PageHeadT *page = (PageHeadT *)cursor->page;
            DB_LOG_WARN_UNFOLD(GMERR_OK,
                "Abnrml Page (%" PRIu32 ",%" PRIu32 "), pageType:%" PRIu32 ", refCount:%" PRIu32 ", stay for %" PRIu64
                " sec",
                addr.deviceId, addr.blockId, page->pageType, cursor->refCount, holdTime);
        }
    }
}

static void BufGetDescAlarm(BufpoolMgrT *mgr, uint32_t *tryCount)
{
    // 每尝试10次打印一次
    if (*tryCount % TRY_TIMES_WARN_PERIOD == 0) {
        SE_WARN(STATUS_OK_INTER, "try get new desc %" PRIu32 " times.", *tryCount);
    }
    // 尝试TRY_TIMES_ERROR_PERIOD次后，轮询一次desc数组，排查可能卡死情况
    if (*tryCount == TRY_TIMES_ERROR_PERIOD) {
        BufpoolCheckDescStatus(mgr, false);
    }
    *tryCount += 1;
}

static StatusInter BufGetNewDesc(BufpoolMgrT *mgr, BufDescT **bufDesc, BufGetDescArgsT args, void *recyArg)
{
    uint32_t options = args.options;
    bool isTryGetPage = (options & ENTER_PAGE_NOWAIT) == ENTER_PAGE_NOWAIT;
    BufPoolT *bufPool = GetBufpoolByPageId(mgr, args.pageId);
    BufDescT *item = NULL;
    // Retry flag, while eliminate page by table, if no suitable page is found for first time,
    // the page will be eliminated even if the page is the last page of the table.
    bool reTry = false;
    uint64_t startTime = DbRdtsc();
    uint64_t triggerWarnTimes = 0;
    uint32_t tryCount = 0;
    while (true) {
        StatusInter ret = BufAllocHwm(mgr, bufPool, &item);
        if (ret == STATUS_OK_INTER) {
            DB_ASSERT(item != NULL);
            break;
        }
        if (ret != OUT_OF_MEMORY_INTER) {
            return ret;
        }

        ret = mgr->recycleFunc(mgr, &args, recyArg, reTry, &item);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "recycle when get new desc, pageId(%" PRIu32 ",%" PRIu32 ").", args.pageId.deviceId,
                args.pageId.blockId);
            return ret;
        }
        if (item != NULL) {
            break;
        }
        // 此处忽略返回值 如果不成功 重试即可, 处理内存不足后ckpt刷不了盘的情况
        // influencing bufferpool get new disk.
        ret = CkptTriggerAndCheckTimeout(mgr->seIns, &startTime, &triggerWarnTimes);
        if (ret == OUT_OF_MEMORY_INTER) {
            SE_ERROR(ret, "trigger checkpoint");
            return ret;
        }
        if (isTryGetPage) {
            SE_WARN(NO_DATA_BUFFER_POOL_IS_FULL, "buffer pool is full.");
            return NO_DATA_BUFFER_POOL_IS_FULL;
        }
        if (ret == DATABASE_NOT_AVAILABLE_INTER) {
            SE_ERROR(ret, "trigger checkpoint");
            return ret;
        }

        BufGetDescAlarm(mgr, &tryCount);
        reTry = true;
    }
    BufDescReset(item);
    mgr->setBufDescListFunc(options, recyArg, item);
    item->operateTime = DbToSeconds(DbRdtsc());
    (*bufDesc) = item;
    return STATUS_OK_INTER;
}

static void BufInitDescOption(BufDescT *bufDesc, uint32_t options, uint32_t pageSize)
{
    if ((options & ENTER_PAGE_RESIDENT) != 0) {
        bufDesc->isResident = true;
    } else if ((options & ENTER_PAGE_PINNED) != 0) {
        bufDesc->isPinned = true;
    }

    if ((options & ENTER_PAGE_INIT) != 0 || (options & ENTER_PAGE_LOAD) != 0) {
        bufDesc->loadStatus = (uint8_t)BUF_IS_LOADED;
        if (SeGetPersistMode() == PERSIST_ON_DEMAND) {
            (void)memset_s(bufDesc->page, pageSize, 0x0, pageSize);
        }
    }
}

static inline void CheckBufDesc(const BufDescT *bufDesc, PageIdT pageId)
{
    bool pageIdCheck = DbIsPageIdEqual(pageId, bufDesc->pageId);
    DB_ASSERT(pageIdCheck);
}

static StatusInter VerifyBufDescAfterGet(BufpoolMgrT *mgr, PageIdT pageId, void *recyArg, BufDescT *bufDesc)
{
    CheckBufDesc(bufDesc, pageId);
    if (mgr->preprocessFunc != NULL) {
        return mgr->preprocessFunc(mgr, recyArg, bufDesc);
    }
    return STATUS_OK_INTER;
}

typedef struct PushInBucketParams {
    BufGetDescArgsT *args;
    bool *isNewPage;
    BufDescT **outbufDesc;
    BufEnqueArgT *enqueArg;
    BufBucketT *bucket;
} PushInBucketParamsT;

// 大函数拆分BufGetDesc
StatusInter RecheckDescBeforePushInBucket(BufpoolMgrT *mgr, PushInBucketParamsT *pushBucketParams)
{
    BufGetDescArgsT *args = pushBucketParams->args;
    bool *isNewPage = pushBucketParams->isNewPage;
    BufDescT **outbufDesc = pushBucketParams->outbufDesc;
    BufEnqueArgT *enqueArg = pushBucketParams->enqueArg;
    BufBucketT *bucket = pushBucketParams->bucket;

    PageIdT pageId = args->pageId;
    BufPoolT *bufPool = GetBufpoolByPageId(mgr, pageId);
    DbSpinLock(&bufPool->lock);

    DbRWSpinWLock(&bucket->lock);

    // 并发场景get 同个页，再次校验；后来的线程发现bucket已经有这个页，以此为主（将之前的放回去tail）
    // 否则bucket可能挂两个同样pageId的页
    BufDescT *tmpBufDesc = BufpoolBucketFindDesc(mgr, bucket, pageId);
    if (SECUREC_UNLIKELY(tmpBufDesc != NULL)) {
        // 两个线程使用相同的pageId并发申请页Alloc Hwm：
        // get BufDesc1(T1)    ->    get BufDesc2(T2)    ->
        //           assign pageId(T1)  put BufDesc1 to Bucket(T1) -> put BufDesc1 to LRU header(T1)
        // T2 可以从bucket拿页(这里bucket为主)
        SePageIncRefCountWithLock(mgr->seLockFn, tmpBufDesc);
        DbRWSpinWUnlock(&bucket->lock);
        CheckBufDesc(tmpBufDesc, pageId);
        // 此时需要将页放到tail
        BufLruAdd(&bufPool->list[enqueArg->bufDesc->listId], enqueArg->bufDesc, BUF_ADD_TAIL);
        DbSpinUnlock(&bufPool->lock);
        *isNewPage = false;
        *outbufDesc = tmpBufDesc;
        return STATUS_OK_INTER;
    }

    enqueArg->bufDesc->refCount = REF_COUNT_INIT;
    enqueArg->bufDesc->pageId = pageId;
    BufpoolBucketAddDesc(bucket, enqueArg->bufDesc);
    DbRWSpinWUnlock(&bucket->lock);

    // 第一次拿这个页，重新按照enqueArg->bufDesc->listId插入对应的head中
    StatusInter ret = mgr->lruEnqueueFunc(mgr, args->options, enqueArg);
    DbSpinUnlock(&bufPool->lock);
    *isNewPage = true;
    *outbufDesc = enqueArg->bufDesc;
    return ret;
}

static BufDescT *TryFindDescThroughBucket(BufpoolMgrT *mgr, PageIdT pageId, BufBucketT **bucket)
{
    BufPoolT *bufPool = GetBufpoolByPageId(mgr, pageId);
    BucketMgrT *curBucketMgr = BufpoolGetBucketMgr(mgr->seIns, DbAtomicGet64(&bufPool->currBucketMgrShm));
    BufBucketT *curBucket = BufpoolGetBucketViaPageAddr(curBucketMgr, pageId);
    BucketMgrT *newBucketMgr = BufpoolGetBucketMgr(mgr->seIns, DbAtomicGet64(&bufPool->newBucketMgrShm));
    BufBucketT *newBucket = NULL;
    *bucket = curBucket;
    DbRWSpinRLock(&curBucket->lock);
    BufDescT *bufDesc = BufpoolBucketFindDesc(mgr, curBucket, pageId);
    if (bufDesc == NULL && newBucketMgr && curBucketMgr != newBucketMgr) {
        // 没有则尝试从新的（rehash中查找） 从新的bucket
        newBucket = BufpoolGetBucketViaPageAddr(newBucketMgr, pageId);
        if (newBucket != curBucket) {
            DbRWSpinRUnlock(&curBucket->lock);
            *bucket = newBucket;
            DbRWSpinRLock(&newBucket->lock);
        }
        // 外层函数TryGetBucketAndDescFromBufpool计数后释放锁
        return BufpoolBucketFindDesc(mgr, newBucket, pageId);
    }
    return bufDesc;
}

BufBucketT *TryGetBucketAndDescFromBufpool(
    BufpoolMgrT *mgr, PageIdT pageId, BufGetPageStatusE propose, BufDescT **outBufDesc)
{
    // pageId to bucket，first hashId, then bucketArr, then bucket
    BufBucketT *bucket = NULL;
    BufDescT *bufDesc = TryFindDescThroughBucket(mgr, pageId, &bucket);
    if (bufDesc != NULL && (propose != NO_OPT_REFCOUNT)) {
        if (propose == GET_PAGE) {
            SePageIncRefCountWithLock(mgr->seLockFn, bufDesc);
        } else {
            SePageDecRefCountWithLock(mgr->seLockFn, bufDesc);
        }
    }
    DbRWSpinRUnlock(&bucket->lock);
    if (outBufDesc) {
        *outBufDesc = bufDesc;
    }
    return bucket;
}

StatusInter BufTrySwitchLru(
    BufpoolMgrT *mgr, BufPoolT *bufPool, BufDescT *bufDesc, uint32_t options, BufEnqueArgT *enqueArg)
{
    if (SECUREC_UNLIKELY(!bufDesc->isResident && (options & ENTER_PAGE_RESIDENT) != 0)) {
        DbSpinLock(&bufPool->lock);
        if (!bufDesc->isResident) {
            BufLruRemove(&bufPool->list[bufDesc->listId], bufDesc);
            bufDesc->listId = LRU_LIST_TYPE_MAX;
            bufDesc->isResident = true;
        }
        DbSpinUnlock(&bufPool->lock);
    } else if ((options & ENTER_PAGE_PINNED) != 0 && !bufDesc->isPinned) {
        // set pin status.
        DbSpinLock(&bufPool->lock);
        bufDesc->isPinned = true;
        DbSpinUnlock(&bufPool->lock);
    }
    StatusInter ret = mgr->switchLRUListFunc(mgr, bufPool, options, enqueArg->recyArg, bufDesc);
    if (ret == STATUS_OK_INTER) {
        enqueArg->bufDesc = bufDesc;
    }
    return ret;
}

// 尝试从bucket，从lru中找到一个能用的outbufDesc，否则申请新的(hwm未满之前)
StatusInter BufGetDesc(BufpoolMgrT *mgr, BufGetDescArgsT args, void *recyArg, bool *isNewPage, BufDescT **outbufDesc)
{
    PageIdT pageId = args.pageId;
    BufPoolT *bufPool = GetBufpoolByPageId(mgr, pageId);
    // pageId to bucket，first hashId, then bucketArr, then bucket
    BufEnqueArgT enqueArg = {recyArg, NULL, pageId};
    BufDescT *bufDesc = NULL;
    StatusInter ret = STATUS_OK_INTER;
    BufBucketT *bucket = NULL;
    bucket = TryGetBucketAndDescFromBufpool(mgr, pageId, GET_PAGE, &bufDesc);

    if (bufDesc != NULL) {
        ret = BufTrySwitchLru(mgr, bufPool, bufDesc, args.options, &enqueArg);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        ret = VerifyBufDescAfterGet(mgr, pageId, recyArg, enqueArg.bufDesc);
        if (ret == STATUS_OK_INTER) {
            *isNewPage = false;
            *outbufDesc = enqueArg.bufDesc;
        }
        return ret;
    }

    if (!DbCommonIsServer()) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER, "BufClt page not in bufferpool.");
        return INTERNAL_ERROR_INTER;
    }

    if (args.options & ENTER_PAGE_ACTIVE) {
        return NO_DATA_INTER;
    }

    ret = BufGetNewDesc(mgr, &enqueArg.bufDesc, args, recyArg);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    BufInitDescOption(enqueArg.bufDesc, args.options, SIZE_K(mgr->seIns->seConfig.pageSize));
    PushInBucketParamsT pushBucketParams = {&args, isNewPage, outbufDesc, &enqueArg, bucket};
    return RecheckDescBeforePushInBucket(mgr, &pushBucketParams);
}

StatusInter BufpoolAllocPage(BufpoolMgrT *mgr, AllocPageParamT *allocPageParam, PageIdT *pageId)
{
    DB_POINTER2(mgr, pageId);
    DB_ASSERT(mgr->base.type == SE_BUFFER_POOL);

    bool isNew;
    StatusInter ret = SpaceAllocPage(mgr->seIns, BUFFER_POOL_FILE, allocPageParam->spaceId, pageId, &isNew);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "Buffer pool alloc space block");
        return ret;
    }
    uint8_t *page = NULL;
    ret = BufpoolGetPageInit(mgr, allocPageParam->trmId, *pageId, ENTER_PAGE_INIT, &page);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "Buffer pool get page");
        FreePageParamT freePageParam = SeInitFreePageParam(
            allocPageParam->spaceId, *pageId, allocPageParam->dbInstance, NULL, SE_INVALID_LABEL_ID, false);
        BufpoolFreePage(mgr, &freePageParam);
        return ret;
    }
    BufpoolLeavePage(mgr, *pageId, true);

    return STATUS_OK_INTER;
}

StatusInter BufpoolFreePage(BufpoolMgrT *mgr, FreePageParamT *freePageParam)
{
    DB_POINTER(mgr);
    // unpin the page if exists.
    BufDescT *bufDesc = NULL;
    (void)TryGetBucketAndDescFromBufpool(mgr, freePageParam->addr, NO_OPT_REFCOUNT, &bufDesc);
    if (bufDesc != NULL && bufDesc->isPinned) {
        // set to unpin
        BufPoolT *bufPool = mgr->bufPool;
        DbSpinLock(&bufPool->lock);
        bufDesc->isPinned = false;
        DbSpinUnlock(&bufPool->lock);
    }

    uint8_t *page = NULL;
    StatusInter ret = BufpoolGetPage(mgr, freePageParam->addr, &page, ENTER_PAGE_NORMAL, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "free page in Bufpool.");
        return ret;
    }
#ifndef NDEBUG
    ret = SeFreePagePreCheck((PageHeadT *)page, freePageParam->addr);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
#endif
    SeResetPageHead(page);
    SePageHeadResetRedo(&freePageParam->addr);
    BufpoolLeavePage(mgr, freePageParam->addr, true);
    ret = SpaceFreePage(mgr->seIns, freePageParam->spaceId, freePageParam->addr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "free page from space in Bufpool.");
        return ret;
    }

    return STATUS_OK_INTER;
}

static inline StatusInter BufpoolMarkWritePage(SeInstanceT *seIns, DbSpinlockFnT *seLockFn, BufDescT *bufDesc)
{
    if (SeGetPersistMode() == PERSIST_ON_DEMAND) {
        bufDesc->isWriting = true;
        return STATUS_OK_INTER;
    }
    return WaitPageWriteComplete(seIns, seLockFn, bufDesc);
}

static StatusInter BufpoolGetPageMemAndMarkWritePage(
    BufpoolMgrT *mgr, uint8_t **page, BufDescT *desc, bool isWrite, PageOptionE options)
{
    PageHeadT *head = (PageHeadT *)PageBufferGet(BufpoolGetPageBufferMgr(mgr), desc->bufId);
    if (head == NULL) {
        DB_ASSERT(false);
        SE_ERROR(INTERNAL_ERROR_INTER, "Desc %" PRIu32 " Buffer %" PRIu32 " Empty", desc->descId, desc->bufId);
        return INTERNAL_ERROR_INTER;
    }
    *page = (uint8_t *)head;
    if (isWrite) {
        StatusInter ret = BufpoolMarkWritePage(mgr->seIns, mgr->seLockFn, desc);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Page write incomplete.");
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter BufpoolGetPageInner(
    BufpoolMgrT *pageMgr, PageIdT pageId, PageOptionE options, bool isWrite, void *recyArg, uint8_t **page, bool *isNew)
{
    if (SECUREC_UNLIKELY(SeGetStorageStatus(pageMgr->seIns) == SE_ON_DISK_EMRGNCY)) {
        SE_ERROR(DATABASE_NOT_AVAILABLE_INTER, "db is not available");
        return DATABASE_NOT_AVAILABLE_INTER;
    }
    BufDescT *bufDesc = NULL;
    BufGetDescArgsT arg = {pageId, options};
    DB_ASSERT(pageId.blockId < (uint32_t)(pageMgr->seIns->seConfig.deviceSize / pageMgr->seIns->seConfig.pageSize));
    StatusInter ret = BufGetDesc(pageMgr, arg, recyArg, isNew, &bufDesc);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get BufDescT when getting page inner");
        return ret;
    }
    if (bufDesc->loadStatus != BUF_NEED_LOAD) {
        return BufpoolGetPageMemAndMarkWritePage(pageMgr, page, bufDesc, isWrite, options);
    }
    if (bufDesc->loadStatus == BUF_NEED_LOAD) {
        DbSpinLock(&bufDesc->lock);
        if (bufDesc->loadStatus == BUF_NEED_LOAD) {
            void *bufPage = bufDesc->page;
            ret = SpaceReadBlockToPage(pageMgr->seIns, pageId, bufPage);
            if (ret != STATUS_OK_INTER) {
                (void)DbAtomicDec16(&bufDesc->refCount);
                DbSpinUnlock(&bufDesc->lock);
                SE_ERROR(ret, "load page from Buf");
                return ret;
            }
            ret = SpaceCheckDiskBlock(bufPage, pageId);
            if (ret != STATUS_OK_INTER) {
                DbSpinUnlock(&bufDesc->lock);
                SE_LAST_ERROR(ret, "Unexpect Page load to bufferpool.");
                return ret;
            }
            DbRWLatchInit(&((PageHeadT *)bufPage)->lock);
            bufDesc->loadStatus = (uint8_t)BUF_IS_LOADED;
        }
        DbSpinUnlock(&bufDesc->lock);
    }
    return BufpoolGetPageMemAndMarkWritePage(pageMgr, page, bufDesc, isWrite, options);
}

StatusInter BufpoolGetPage(BufpoolMgrT *pageMgr, PageIdT pageId, uint8_t **page, PageOptionE option, bool isWrite)
{
    bool isNewPage;
    StatusInter ret = BufpoolGetPageInner(pageMgr, pageId, option, isWrite, NULL, page, &isNewPage);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Unable for bufferpool to get page.");
        return ret;
    }

    return STATUS_OK_INTER;
}

StatusInter BufpoolGetPageInit(BufpoolMgrT *pageMgr, uint32_t trmId, PageIdT pageId, PageOptionE option, uint8_t **page)
{
    bool isNewPage;
    StatusInter ret = BufpoolGetPageInner(pageMgr, pageId, option, true, NULL, page, &isNewPage);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get page from bufferpool inner");
        return ret;
    }

    if ((option & ENTER_PAGE_INIT) != 0) {
        SeInitPageHead(*page, trmId, pageMgr->pageSize, pageId, isNewPage);
        SePageHeadInitRedo(&pageId, trmId);
    }
    return STATUS_OK_INTER;
}

StatusInter BufpoolGetPageWithArg(BufpoolMgrT *pageMgr, PageIdT pageId, uint32_t option, void *recyArg, uint8_t **page)
{
    bool isNewPage;
    StatusInter ret =
        BufpoolGetPageInner(pageMgr, pageId, option, option & ENTER_PAGE_WRITE, recyArg, page, &isNewPage);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get page with different arguments");
        return ret;
    }

    return STATUS_OK_INTER;
}

void BufpoolLeavePage(BufpoolMgrT *pageMgr, PageIdT pageId, bool isChanged)
{
    DB_POINTER(pageMgr);
    (void)TryGetBucketAndDescFromBufpool(pageMgr, pageId, LEAVE_PAGE, NULL);
}

void BufpoolLeaveVirtualPage(BufpoolMgrT *mgr, PageIdT pageId, bool isChanged, uint8_t *page)
{
    DB_POINTER(mgr);
    BufDescMgrT *descMgr = BufpoolGetDescMgr(mgr);
    PageBufferMgrT *bufMgr = BufpoolGetPageBufferMgr(mgr);
    uint32_t index = PageBufferGetIndex(bufMgr, page);
    BufDescT *desc = BufDescGet(descMgr, index);
    if (DbIsPageIdEqual(desc->pageId, pageId) && desc->attachedBucket) {
        SePageDecRefCountWithLock(mgr->seLockFn, desc);
    } else {
        BufpoolLeavePage(mgr, pageId, isChanged);
    }
}

SO_EXPORT_FOR_TS void BufpoolGetStat(SeInstanceT *seIns, BufpoolStatT *bufpoolStat)
{
    DB_POINTER(seIns);

    BufpoolMgrT *mgr = (BufpoolMgrT *)seIns->pageMgr;
    PageBufferMgrT *bufMgr = BufpoolGetPageBufferMgr(mgr);
    bufpoolStat->pageSize = mgr->pageSize / DB_KIBI;

    BufPoolT *bufpool = mgr->bufPool;
    bufpoolStat->size = SeGetBufpoolCurrentSize(seIns) / DB_KIBI;
    bufpoolStat->capacity = bufMgr->capacity;
    bufpoolStat->hwm = bufMgr->hwm;
    bufpoolStat->recyclePolicy = seIns->seConfig.bufferPoolPolicy;
    bufpoolStat->priorityListMinimum = bufpool->priorityListMinimum;
    bufpoolStat->scanListLimit = bufpool->scanListLimit;
    bufpoolStat->lruListCount = bufpool->list[LRU_LIST_NORMAL].count;
    bufpoolStat->priorityListCount = bufpool->list[LRU_LIST_PRIORITY].count;
    bufpoolStat->scanListCount = bufpool->list[LRU_LIST_STATS_SCAN].count;

    if (!SeBufferPoolIsShmem(seIns)) {
        BucketMgrT *bucketMgr = BufpoolGetBucketMgr(seIns, bufpool->currBucketMgrShm);
        bufpoolStat->bucketNum = bucketMgr->bucketNum;
        // 当前resize所处状态。状态来自BufpoolMgrT
        bufpoolStat->resizeStatus = (uint8_t)BufpoolGetResizeStatus(mgr->resizeCtx);
        BufpoolCheckDescStatus(mgr, true);
    }
}

Status HashBufpoolReset(SeInstanceT *seIns)
{
    DB_POINTER2(seIns, seIns->bufpoolMgr);
    HashBufpoolDestroyPageMgr(seIns, seIns->bufpoolMgr, true);

    // initialise in-memory struct related to bufferpool.
    Status ret = DbGetExternalErrno(HashBufpoolCreatePageMgr(seIns, NULL, (PageMgrT **)&seIns->pageMgr, NULL));
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "init BufpoolMgr after empty in-memory pages.");
        return ret;
    }
    return ret;
}

Status BufferpoolInvalidPageImpl(SeInstanceT *seInsPtr, PageIdT pageAddr)
{
    BufpoolMgrT *mgr = seInsPtr->pageMgr;
    BufPoolT *bufPool = GetBufpoolByPageId(mgr, pageAddr);

    // 先触发一次全量刷盘，确保pageId对应的页已经刷盘
    Status ret = CkptTrigger(seInsPtr, CKPT_MODE_FULL, true, WAIT_MSECONDS_FOR_CHECKPOINT);
    if (ret == GMERR_OUT_OF_MEMORY) {
        DB_LOG_ERROR(ret, "checkpoint is out of memory");
        return ret;
    }
    if (ret == GMERR_DATABASE_NOT_AVAILABLE) {
        DB_LOG_ERROR(ret, "database currently unavailable");
        return ret;
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "checkpoint while remove page from bufferpool");
        return ret;
    }

    // 通过pageId获取bucket和bufDesc
    BufDescT *bufDesc = NULL;
    BufBucketT *bucket = TryGetBucketAndDescFromBufpool(mgr, pageAddr, GET_PAGE, &bufDesc);
    // 如果bufDesc为NULL，说明此时该页已被换出，直接返回即可
    if (bufDesc == NULL) {
        return GMERR_OK;
    }
    // 此处将需要移除的bufDesc从bucket上摘下，reset后放入LRU链表尾部
    DbSpinLock(&bufPool->lock);
    DbRWSpinWLock(&bucket->lock);
    BufpoolBucketRemoveDesc(bucket, bufDesc);
    DbRWSpinWUnlock(&bucket->lock);
    BufLruListTypeE listId = bufDesc->listId;
    BufLruRemove(&bufPool->list[listId], bufDesc);
    BufDescReset(bufDesc);
    BufLruAdd(&bufPool->list[listId], bufDesc, BUF_ADD_TAIL);
    DbSpinUnlock(&bufPool->lock);
    return GMERR_OK;
}

static inline Status BufpoolResize(SeInstanceT *seIns, uint32_t bufferPoolSize)
{
    if (!SeBufpoolResizeEnable(seIns)) {
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    BufpoolMgrT *mgr = seIns->bufpoolMgr;
    return DbGetExternalErrno(BufferPoolResizeStart(mgr->resizeCtx, bufferPoolSize));
}

void *BufferPoolGetRegisterExtraFunc(PageMgrT *pageMgr, PageMgrExtraFuncTypeE funcType)
{
    switch (funcType) {
        case PAGE_MGR_EXTRA_BUFFER_POOL_TRMID_OP_WITH_PAGE:
            return (void *)(TrmIdOpWithPageT)BufferpoolLoadTableTrmIdPageOpImpl;

        case PAGE_MGR_EXTRA_BUFFER_POOL_TRMID_OP:
            return (void *)(TrmIdOp)BufferpoolLoadTableTrmIdOpImpl;

        case PAGE_MGR_EXTRA_BUFFER_POOL_PREFETCH_PAGES:
            return (void *)(PreFetchPageFunc)SeFilePreFetchStart;

        case PAGE_MGR_EXTRA_BUFFER_POOL_STOP_PREFETCH_PROC:
            return (void *)(StopProcPrefetchFunc)SeFilePreFetchStop;

        case PAGE_MGR_EXTRA_BUFFER_POOL_RESIZE:
            return (void *)(ResizeBufferFunc)BufpoolResize;

        case PAGE_MGR_EXTRA_BUFFER_POOL_INVALID_PAGE:
            return (void *)(SeInvalidBufpoolPageFunc)BufferpoolInvalidPageImpl;

        case PAGE_MGR_EXTRA_BUFFER_POOL_RESET:
            return (void *)(ResetBufpoolFunc)HashBufpoolReset;

        default:
            // Get bufferpool extral function type:%x
            SE_LAST_ERROR(NO_DATA_INTER, "extral func:%" PRIu32, (uint32_t)funcType);
            return NULL;
    }
}
