/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: memery data page manager
 * Author: zhangfengyu
 * Create: 2022-06-22
 */

#include "se_durable_memdata.h"
#include "se_page_mgr.h"
#include "se_log.h"
#include "se_durable_memdata_redo.h"
#include "se_space_inner.h"
#include "se_redo.h"
#include "se_database.h"
#include "se_db_file.h"

static inline void DumemInitDesc(BufDescT *desc, const PageIdT addr, uint8_t *page)
{
    desc->pageId = addr;
    desc->page = page;
}

void DumemInitMgr(DumemMgrT *mgr, uint16_t pageSize)
{
    DB_POINTER(mgr);
    mgr->base.type = SE_DURABLE_MEMDATA;
    mgr->base.allocPageFunc = (SeAllocPageFunc)DumemAllocPage;
    mgr->base.freePageFunc = (SeFreePageFunc)DumemFreePage;
    mgr->base.getPageFunc = (SeGetPageFunc)DumemGetPage;
    mgr->base.getPageInitFunc = (SeGetPageInitFunc)DumemGetPageInit;
    mgr->base.getPageWithArgFunc = NULL;
    mgr->base.leavePageFunc = (SeLeavePageFunc)DumemLeavePage;
    mgr->base.leaveVirtualPageFunc = NULL;
    mgr->pageSize = pageSize * DB_KIBI;
}

StatusInter DumemCreatePageMgr(SeInstanceT *seIns, DbMemCtxT *memCtx, PageMgrT **pageMgr)
{
    DB_POINTER(seIns);
    uint32_t mgrSize = sizeof(DumemMgrT);
    DumemMgrT *mgr = (DumemMgrT *)DbDynMemCtxAlloc(memCtx, mgrSize);
    if (mgr == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "malloc mgr,size:%" PRIu32, mgrSize);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    (void)memset_s((void *)mgr, mgrSize, 0x0, mgrSize);

    mgr->seIns = seIns;
    mgr->devMgr = DbShmPtrToAddr(seIns->devMgrShm);
    if (mgr->devMgr == NULL) {
        DbDynMemCtxFree(memCtx, mgr);
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get deviceMgr(%" PRIu32 " %" PRIu32 ")", seIns->devMgrShm.segId,
            seIns->devMgrShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    mgr->pageCntPerDev = seIns->seConfig.deviceSize / seIns->seConfig.pageSize;
    mgr->descMgr = SeGetBufDescMgr(seIns, seIns->pageDescArray);
    if (!mgr->descMgr) {
        DbDynMemCtxFree(memCtx, mgr);
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get bufDescMgr %" PRIu64, seIns->pageDescArray);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    DumemInitMgr(mgr, seIns->seConfig.pageSize);
    if (mgr->pageCntPerDev == 0) {
        DbDynMemCtxFree(memCtx, mgr);
        SE_LAST_ERROR(CONFIG_ERROR_INTER, "pageCntPerDev is zero unexpectedly.");
        return CONFIG_ERROR_INTER;
    }
    SeInitPageAddrInfo(&mgr->base, mgr->pageSize, mgr->pageCntPerDev);
    DumemReplayFuncRegister(seIns);

    *pageMgr = (PageMgrT *)mgr;
    return STATUS_OK_INTER;
}

void DumemDestroyPageMgr(SeInstanceT *seIns, PageMgrT *pageMgr)
{
    DumemMgrT *mgr = (DumemMgrT *)(void *)pageMgr;
    DbDynMemCtxFree(seIns->seServerMemCtx, mgr);
}

static inline uint32_t PageAddrToBufDescIndex(SeInstanceT *seIns, PageIdT pageId)
{
    return SeGetPageCntPerDev(seIns) * pageId.deviceId + pageId.blockId;
}

static StatusInter DumemLoadSingleDevice(SeInstanceT *seIns, uint32_t deviceId)
{
    DB_POINTER(seIns);
    DbDeviceT *device = (DbDeviceT *)CtrlGroupGetItem(&seIns->db->deviceGroup, deviceId);
    if (device == NULL) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "device (%" PRIu32 ") not found", deviceId);
        return DATA_EXCEPTION_INTER;
    }
    DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, device->ctrl->fileId);
    if (file == NULL) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "file not exist, id %" PRIu32, device->ctrl->fileId);
        return DATA_EXCEPTION_INTER;
    }
    return SeLoadSingleDeviceFromDbFile(seIns, file, device);
}

// 获取device，如果尚未加载至内存中，则从持久化文件中加载
static StatusInter DumemGetDeviceEntry(DumemMgrT *dumemMgr, uint32_t deviceId, uint8_t **deviceEntry)
{
    DB_POINTER(dumemMgr);
    uint8_t *device = DevGetEntry(dumemMgr->devMgr, deviceId);
    if (SECUREC_UNLIKELY(device == NULL)) {
        if (!SeIsBufpoolAndDumemCoexist(dumemMgr->seIns)) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get deviceId:%" PRIu32, deviceId);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        // 如果是双形态，需要从持久化文件中加载device
        StatusInter ret = DumemLoadSingleDevice(dumemMgr->seIns, deviceId);
        if (ret != STATUS_OK_INTER) {
            SE_LAST_ERROR(
                UNEXPECTED_NULL_VALUE_INTER, "durable memdata load single device unsucc, deviceId:%" PRIu32, deviceId);
            DB_ASSERT(false);
            return ret;
        }
        device = DevGetEntry(dumemMgr->devMgr, deviceId);
    }
    if (SECUREC_UNLIKELY(device == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get deviceId:%" PRIu32, deviceId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    *deviceEntry = device;
    return STATUS_OK_INTER;
}

StatusInter DumemAllocPage(DumemMgrT *mgr, AllocPageParamT *allocPageParam, PageIdT *addr)
{
    DB_POINTER2(mgr, addr);
    bool isNew;
    DB_ASSERT(mgr->base.type == SE_DURABLE_MEMDATA);

    StatusInter ret = SpaceAllocPage(mgr->seIns, DURABLE_MEM_FILE, allocPageParam->spaceId, addr, &isNew);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    uint8_t *device = NULL;
    ret = DumemGetDeviceEntry(mgr, addr->deviceId, &device);
    if (ret != STATUS_OK_INTER) {
        DB_ASSERT(false);
        return ret;
    }

    uint8_t *page = device + (uint64_t)(addr->blockId * mgr->pageSize);
    SeInitPageHead(page, allocPageParam->trmId, mgr->pageSize, *addr, isNew);

    BufDescT *desc = BufDescGet(mgr->descMgr, PageAddrToBufDescIndex(mgr->seIns, *addr));
    if (SECUREC_UNLIKELY(desc == NULL)) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get desc:%" PRIu32 ", %" PRIu32, addr->deviceId, addr->blockId);
        DB_ASSERT(false);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    if (!BufDescInited(desc, *addr)) {
        DumemInitDesc(desc, *addr, page);
    }

    SePageHeadInitRedo(addr, allocPageParam->trmId);
    return STATUS_OK_INTER;
}

StatusInter DumemFreePage(DumemMgrT *mgr, FreePageParamT *freePageParam)
{
    DB_POINTER2(mgr, freePageParam);
    uint8_t *page = NULL;
    StatusInter ret = DumemGetDeviceEntry(mgr, freePageParam->addr.deviceId, &page);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    page += (uint64_t)(freePageParam->addr.blockId * mgr->pageSize);
    SeResetPageHead(page);
    (void)SePageHeadResetRedo(&freePageParam->addr);

    return SpaceFreePage(mgr->seIns, freePageParam->spaceId, freePageParam->addr);
}

StatusInter DumemGetPage(DumemMgrT *mgr, PageIdT addr, uint8_t **page, PageOptionE option, bool isWrite)
{
    DB_POINTER2(mgr, page);
    DB_UNUSED(option);
    uint8_t *device = NULL;
    StatusInter ret = DumemGetDeviceEntry(mgr, addr.deviceId, &device);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    *page = device + (uint64_t)(addr.blockId * mgr->pageSize);

#ifndef NDEBUG
    StorageTypeE storageType = DeviceGetStorageType(mgr->devMgr, addr.deviceId);
    DB_ASSERT(storageType == (uint32_t)SE_DURABLE_MEMDATA);
#endif

    if (isWrite && SeGetPersistMode() == PERSIST_INCREMENT) {
        BufDescT *desc = BufDescGet(mgr->descMgr, PageAddrToBufDescIndex(mgr->seIns, addr));
        if (SECUREC_UNLIKELY(desc == NULL || !BufDescInited(desc, addr))) {
            SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get desc:%" PRIu32 ", %" PRIu32, addr.deviceId, addr.blockId);
            DB_ASSERT(false);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        (void)WaitPageWriteComplete(mgr->seIns, &mgr->seIns->seLockFn, desc);  // 只有minikv多进程场景才会出错
    }
    return STATUS_OK_INTER;
}

StatusInter DumemGetPageInit(DumemMgrT *mgr, uint32_t trmId, PageIdT addr, PageOptionE option, uint8_t **page)
{
    uint8_t *device = NULL;
    StatusInter ret = DumemGetDeviceEntry(mgr, addr.deviceId, &device);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    *page = device + (uint64_t)(addr.blockId * mgr->pageSize);

    BufDescT *desc = BufDescGet(mgr->descMgr, PageAddrToBufDescIndex(mgr->seIns, addr));
    if (SECUREC_UNLIKELY(desc == NULL)) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get desc:%" PRIu32 ", %" PRIu32, addr.deviceId, addr.blockId);
        DB_ASSERT(false);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    DumemInitDesc(desc, addr, *page);
    if ((option & ENTER_PAGE_INIT) != 0) {
        if (SeGetPersistMode() == PERSIST_INCREMENT) {
            // 只有minikv多进程场景才会出错
            (void)WaitPageWriteComplete(mgr->seIns, &mgr->seIns->seLockFn, desc);
        }
        SeInitPageHead(*page, trmId, mgr->pageSize, addr, true);
        SePageHeadInitRedo(&addr, trmId);
    }
    return STATUS_OK_INTER;
}

ALWAYS_INLINE_C void DumemLeavePage(DumemMgrT *pageMgr, PageIdT pageAddr, bool isChanged)
{
    return;
}

StatusInter DumemInit(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx)
{
    DB_POINTER2(seIns, seTopShmMemCtx);

    StatusInter ret = DeviceCreate(seIns, seTopShmMemCtx);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "init durable create device");
        return ret;
    }
    const SeConfigT *seConfig = &seIns->seConfig;
    uint64_t totalSize = (uint64_t)seConfig->maxSeMem / seConfig->deviceSize * seConfig->deviceSize;
    uint32_t devMaxCount = totalSize / seConfig->deviceSize;
    if ((SIZE_K(seConfig->pageSize) - sizeof(PageHeadT) - sizeof(SpaceHeadT)) / sizeof(FreeFileDeviceInfoT) <
            devMaxCount &&
        SeGetSpacePolicy() == SE_SPACE_PRIORITY_DEVICE) {
        SE_LAST_ERROR(CONFIGURATION_LIMIT_EXCEEDED_INTER, "MaxDevCnt(%" PRIu32 ") too big!", devMaxCount);
        return CONFIGURATION_LIMIT_EXCEEDED_INTER;
    }
    BufDescMgrCreateCfgT cfg = BufDescInitDefaultCreateCfg(seIns, SE_DURABLE_MEMDATA, totalSize);
    ret = BufDescMgrCreate(seIns, cfg, &seIns->pageDescArray);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "init durable create bufdesc");
        return ret;
    }

    BufDescMgrT *mgr = SeGetBufDescMgr(seIns, seIns->pageDescArray);
    if (SECUREC_UNLIKELY(mgr == NULL)) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get BufDesc Mgr");
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    uint32_t startIndex = 0;
    ret = BufDescPreAlloc(mgr, cfg.capacity, &startIndex);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "init durable alloc bufdesc");
    } else {
        DB_ASSERT(startIndex == 0);
    }
    return ret;
}

StatusInter DumemGetStat(SeInstanceT *seIns, DumemStatT *dumemStat)
{
    DB_POINTER2(seIns, dumemStat);

    SpaceTotalPageInfoT spaceTotalPageInfo = {0};
    StatusInter ret = SpaceGetTotalPageInfo(seIns, &spaceTotalPageInfo, DURABLE_MEM_FILE);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "durable get page info");
        return ret;
    }

    DB_ASSERT(seIns->seConfig.pageSize != 0);
    uint32_t pageSize = SIZE_K(seIns->seConfig.pageSize);
    dumemStat->allocatedPageCnt = spaceTotalPageInfo.totalActualSize / pageSize;
    dumemStat->freePageCnt = spaceTotalPageInfo.totalFreePageCnt;
    dumemStat->usedPageCnt = spaceTotalPageInfo.totalCurSize / pageSize - dumemStat->freePageCnt;
    dumemStat->unusedPageCnt = (spaceTotalPageInfo.totalActualSize - spaceTotalPageInfo.totalCurSize) / pageSize;
    dumemStat->usedPageRatio = (double)dumemStat->usedPageCnt / (double)dumemStat->allocatedPageCnt;
    DB_ASSERT(
        dumemStat->allocatedPageCnt == dumemStat->usedPageCnt + dumemStat->freePageCnt + dumemStat->unusedPageCnt);
    return STATUS_OK_INTER;
}
