/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: device manager
 * Author: zhangfengyu
 * Create: 2022-06-22
 */

#include <sys/mman.h>
#include "se_log.h"
#include "db_dyn_load.h"
#include "db_mem_context.h"
#include "se_dev_node.h"
#include "se_page_mgr.h"
#include "se_device.h"

#define DEV_PAGE_POOL_STEP_PAGE_NUM 1
#define DEV_PAGE_POOL_INIT_PAGE_NUM DEV_PAGE_POOL_STEP_PAGE_NUM

// 前16个device addr存放在全局数组中, 减少cache miss, 加速寻址
SO_EXPORT_FOR_TS DevAddrT g_devEntryCache[DEV_CACHE_CAPACITY] = {0};

// 硬件卸载在服务端缓存所有device addr, 需要在门禁看护
// g_devEntryCacheHac不支持直连读写，不然会有并发问题
uint8_t **g_devEntryCacheHac = NULL;
uint32_t g_devMaxCount = 0;

StatusInter DeviceHacEntryCacheCreate(SeInstanceT *seIns, uint32_t devMaxCount)
{
    size_t allocSize = devMaxCount * sizeof(uint8_t *);
    g_devEntryCacheHac = (uint8_t **)DbDynMemCtxAlloc(seIns->seServerMemCtx, allocSize);
    if (g_devEntryCacheHac == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "dev cache alloc %" PRIu32 " dyn mem", (uint32_t)allocSize);
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_s((void *)g_devEntryCacheHac, allocSize, 0x0, allocSize);
    g_devMaxCount = devMaxCount;
    return STATUS_OK_INTER;
}

void DeviceHacEntryCacheDestory(SeInstanceT *seIns)
{
    DbDynMemCtxFree(seIns->seServerMemCtx, g_devEntryCacheHac);
    g_devEntryCacheHac = NULL;
    g_devMaxCount = 0;
}

void SeClearDevCache(void)
{
    // 共进程模式下客户端和服务端共用g_devEntryCache，其中的资源不能释放
    if (DbCommonIsCltThreadInServProcess()) {
        return;
    }
#ifndef IDS_HAOTIAN
    for (uint32_t i = 0; i < DEV_CACHE_CAPACITY; ++i) {
        g_devEntryCache[i].virAddr = NULL;
    }
#endif
}

// 本接口不支持直连读写调用，仅限硬件卸载服务端场景调用
uint8_t **DevGetEntryHac(void)
{
    return g_devEntryCacheHac;
}

static void DevCacheEntry(const DeviceMgrT *devMgr, uint32_t devId, ShmemPtrT shmAddr)
{
    if (SECUREC_UNLIKELY(devId >= g_devMaxCount)) {
        return;
    }
    uint8_t *virAddr = DbShmPtrToAddrAlign(shmAddr, devMgr->sysPageSize);
    if (SECUREC_UNLIKELY(virAddr == NULL)) {
        return;
    }
#ifndef IDS_HAOTIAN
    if (devId < DEV_CACHE_CAPACITY) {
        g_devEntryCache[devId].virAddr = virAddr;
    }
#endif
    g_devEntryCacheHac[devId] = virAddr;
}

StatusInter DevAllocDeviceMem(const DeviceMgrT *devMgr, ShmemPtrT *devShm)
{
    DB_POINTER2(devMgr, devShm);

    DbMemCtxT *devMemctx = (DbMemCtxT *)DbGetShmemCtxById(devMgr->devMemCtxId, devMgr->ownId);
    if (devMemctx == NULL) {
        SE_LAST_ERROR(
            UNEXPECTED_NULL_VALUE_INTER, "alloc device mem get device memCtx, Id %" PRIu32, devMgr->devMemCtxId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    *devShm = DbShmemCtxAllocAlign(devMemctx, devMgr->devSize, devMgr->sysPageSize);
    if (!DbIsShmPtrValid(*devShm)) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "align alloc mem, free:%" PRIu32 ", max:%" PRIu32, devMgr->freeDevCount,
            devMgr->devMaxCount);
        return OUT_OF_MEMORY_INTER;
    }

    return STATUS_OK_INTER;
}

StatusInter DevAllocDevice(DeviceMgrT *devMgr, uint32_t *devId, uint32_t *devSize, bool alloc)
{
    DB_POINTER3(devMgr, devId, devSize);
    DbSpinLock(&devMgr->lock);
    if (SECUREC_UNLIKELY(devMgr->firstFreeDevId == DEVICE_INVALID_ID)) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "Dev use up.");
        DbSpinUnlock(&devMgr->lock);
        return OUT_OF_MEMORY_INTER;
    }
    uint32_t deviceId = devMgr->firstFreeDevId;
    DB_ASSERT(devMgr->devDesc[deviceId].status < DEV_USED);
    if (!DbIsShmPtrValid(devMgr->devDesc[deviceId].devPtr) && alloc) {
        ShmemPtrT devPtr;
        StatusInter ret = DevAllocDeviceMem(devMgr, &devPtr);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "alloc device mem");
            DbSpinUnlock(&devMgr->lock);
            return ret;
        }
        DevCacheEntry(devMgr, deviceId, devPtr);
        devMgr->devDesc[deviceId].devPtr = devPtr;
    }

    devMgr->firstFreeDevId = devMgr->devDesc[deviceId].nextDevId;
    if (deviceId == devMgr->lastFreeDevId) {
        devMgr->lastFreeDevId = DEVICE_INVALID_ID;
    }

    *devSize = devMgr->devSize;
    *devId = deviceId;
    DevSetStatus(devMgr, deviceId, DEV_USED);
#ifndef NDEBUG
    DeviceSetStorageType(devMgr, deviceId, devMgr->storageType);
#endif
    DbSpinUnlock(&devMgr->lock);
    return STATUS_OK_INTER;
}

static void DeviceListPopDevice(DeviceMgrT *devMgr, uint32_t devId)
{
    uint32_t deviceId = devMgr->firstFreeDevId;
    if (deviceId == devId) {
        devMgr->firstFreeDevId = devMgr->devDesc[deviceId].nextDevId;
        if (deviceId == devMgr->lastFreeDevId) {
            devMgr->lastFreeDevId = DEVICE_INVALID_ID;
        }
    } else {
        while (deviceId != DEVICE_INVALID_ID) {
            if (devMgr->devDesc[deviceId].nextDevId != devId) {
                deviceId = devMgr->devDesc[deviceId].nextDevId;
                continue;
            }
            devMgr->devDesc[deviceId].nextDevId = devMgr->devDesc[devId].nextDevId;
            if (devId == devMgr->lastFreeDevId) {
                devMgr->lastFreeDevId = deviceId;
            }
            break;
        }
    }
    DB_ASSERT(deviceId != DEVICE_INVALID_ID);  // 必然找到
    DB_ASSERT(devMgr->freeDevCount >= 1);
    devMgr->freeDevCount--;
}

StatusInter DevAllocDeviceById(DeviceMgrT *devMgr, uint32_t devId, bool alloc)
{
    DB_POINTER(devMgr);
    if (devId >= devMgr->devMaxCount) {
        DB_ASSERT(SeGetPersistMode() != PERSIST_OFF);
        SE_ERROR(OUT_OF_MEMORY_INTER, "Request dev (%" PRIu32 ") exceed (%" PRIu32 ")", devId, devMgr->devMaxCount);
        return OUT_OF_MEMORY_INTER;
    }
    DbSpinLock(&devMgr->lock);
    if (devMgr->devDesc[devId].status > DEV_USED) {
        SE_ERROR(OBJECT_NOT_IN_PREREQUISITE_STATE_INTER, "Request online dev (%" PRIu32 ")", devId);
        DbSpinUnlock(&devMgr->lock);
        DB_ASSERT(false);
        return OBJECT_NOT_IN_PREREQUISITE_STATE_INTER;
    }

    if (!DbIsShmPtrValid(devMgr->devDesc[devId].devPtr) && alloc) {
        ShmemPtrT devPtr;
        StatusInter ret = DevAllocDeviceMem(devMgr, &devPtr);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "alloc device mem, devId %" PRIu32, devId);
            DbSpinUnlock(&devMgr->lock);
            return ret;
        }
        DevCacheEntry(devMgr, devId, devPtr);
        devMgr->devDesc[devId].devPtr = devPtr;
    }
    if (devMgr->devDesc[devId].status < DEV_USED) {
        DeviceListPopDevice(devMgr, devId);
        DevSetStatus(devMgr, devId, DEV_USED);
#ifndef NDEBUG
        DeviceSetStorageType(devMgr, devId, devMgr->storageType);
#endif
    }
    DbSpinUnlock(&devMgr->lock);
    return STATUS_OK_INTER;
}

static void DeviceInitMgr(SeInstanceT *seIns, uint32_t devMaxCount, DeviceMgrT *devMgr)
{
    devMgr->ownId = seIns->seConfig.instanceId;
    devMgr->pageSize = seIns->seConfig.pageSize * DB_KIBI;
    devMgr->devSize = seIns->seConfig.deviceSize * DB_KIBI;
    DB_ASSERT(devMgr->devSize % devMgr->pageSize == 0);
    devMgr->freeDevCount = devMaxCount;
    devMgr->devMaxCount = devMaxCount;
    devMgr->sysPageSize = DbAdptGetSysPageSize();
    for (uint32_t i = 0; i < devMaxCount; ++i) {
        devMgr->devDesc[i].devPtr = DB_INVALID_SHMPTR;
        devMgr->devDesc[i].status = DEV_NOT_USED;
        devMgr->devDesc[i].nextDevId = i + 1;
#ifndef NDEBUG
        devMgr->devDesc[i].storageType = DB_INVALID_UINT32;
#endif
        DevNodeT *devNode = DevNodeGetById(devMgr, i);
        DevNodeSetNew(devNode, devMgr->devSize / devMgr->pageSize);
        *devNode = SE_INVALID_DEV_NODE;
    }
    devMgr->devDesc[devMaxCount - 1].nextDevId = DEVICE_INVALID_ID;
    devMgr->firstFreeDevId = 0;
    devMgr->lastFreeDevId = devMaxCount - 1;
#ifndef NDEBUG
    devMgr->storageType = DbDynLoadHasFeature(COMPONENT_DURABLE_MEMDATA) ? SE_DURABLE_MEMDATA : SE_MEMDATA;
#endif
    DbSpinInit(&devMgr->lock);
}

static StatusInter DeviceCreateMgr(SeInstanceT *seIns, DbMemCtxT *topMemCtx, DeviceMgrT **devMgrOut)
{
    DB_POINTER3(seIns, topMemCtx, devMgrOut);

    /* 不再向上取整计算device个数，如果存在不能整除的情况，就会导致资源浪费的情况 */
    uint32_t devMaxCount = seIns->seConfig.maxSeMem / seIns->seConfig.deviceSize;
    StatusInter ret = DeviceHacEntryCacheCreate(seIns, devMaxCount);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    uint32_t pageCntPerDev = seIns->seConfig.deviceSize / seIns->seConfig.pageSize;
    uint32_t devMgrSize =
        (uint32_t)sizeof(DeviceMgrT) + (uint32_t)(sizeof(DevDescT) + DevNodeGetSize(pageCntPerDev)) * devMaxCount;
    DeviceMgrT *devMgr = (DeviceMgrT *)SeShmAlloc(topMemCtx, devMgrSize, &seIns->devMgrShm);
    if (devMgr == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "shmMalloc %" PRIu32, devMgrSize);
        DeviceHacEntryCacheDestory(seIns);
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_s((void *)devMgr, devMgrSize, 0x0, devMgrSize);
    DeviceInitMgr(seIns, devMaxCount, devMgr);
    *devMgrOut = devMgr;
    return STATUS_OK_INTER;
}

DbMemCtxT *DevCreateMemCtx(const SeInstanceT *seIns, DeviceMgrT *devMgr, DbMemCtxT *dbTopMemCtx)
{
    DB_POINTER2(devMgr, dbTopMemCtx);
    DbCfgValueT cfgIsUseHugePage;
    bool isUseHugePage = false;
    DbMemCtxArgsT devCtxArgs = {0};
    devCtxArgs.instanceId = devMgr->ownId;
    devCtxArgs.ctxId = DB_SE_DEV_SHMEMCTX_ID;

    // poolMemParam的第二个参数为最大device的个数
    if (DbCfgGet(DbGetCfgHandle(seIns->dbInstance), DB_CFG_HUGE_PAGE, &cfgIsUseHugePage) == GMERR_OK &&
        cfgIsUseHugePage.int32Val == 1) {
        isUseHugePage = true;
        DB_LOG_INFO("Create pool memctx use huge page");
    }

    uint32_t pagePoolPageSize = devMgr->devSize + DbAdptGetSysPageSize();  // 补充物理页addr对齐
    // 如果不能被步长整除，需要向上取整到步长的倍数来满足容量要求
    DB_ASSERT(devMgr->devMaxCount > 0);
    uint32_t maxNum = (((devMgr->devMaxCount + DEV_PAGE_POOL_STEP_PAGE_NUM) - 1) / DEV_PAGE_POOL_STEP_PAGE_NUM) *
                      DEV_PAGE_POOL_STEP_PAGE_NUM;
    DbPageMemParamT poolMemParam = {DEV_PAGE_POOL_INIT_PAGE_NUM, maxNum, DEV_PAGE_POOL_STEP_PAGE_NUM,
        DbGetDevShmPermission(), pagePoolPageSize, isUseHugePage};
    AlgoParamT algoParam;
    algoParam.pageParam = &poolMemParam;
    devCtxArgs.algoParam = &algoParam;
    // Create Storage shmem group for memory threshold constraint.
    // Additional space for segment management struct is requeired.
    uint64_t storageGroupId = DB_INVALID_UINT64;
    // 组的大小:maxSeMem + (每个device的字节对齐的4K + pagePool管理每个device开销 + Hpe每个大块内存底噪4K) * devCount
    uint64_t storageGroupSize =
        SIZE_K(seIns->seConfig.maxSeMem) + (uint64_t)devMgr->devMaxCount * devMgr->sysPageSize +
        (uint64_t)devMgr->devMaxCount * ((uint64_t)DbAdptPagePoolGetSegHdrSize() + devMgr->sysPageSize);
    Status ret = DbInitShmemGroup(STORAGE_SHM_GROUP_ID, storageGroupSize, "GMDB storage shmem group", &storageGroupId);
    if (ret != GMERR_OK) {
        DB_LOG_INFO("storage create shmem group");
        return NULL;
    }
    devCtxArgs.groupId = storageGroupId;
    /* devMgrMemctx 说明
       用    途: 用于存储引擎中device大块内存的申请和释放
       生命周期: 长进程级别
       释放策略: 就近释放，且当前只有Undo space下的device才能归还给OS
       兜底清空措施: 依赖上层dbTopShmMemCtx, server退出时销毁
   */
    DbMemCtxT *poolShmemCtx = (DbMemCtxT *)DbCreatePagePoolShmemCtx(dbTopMemCtx, "devMgrMemctx", &devCtxArgs);
    devMgr->isHugePage = devCtxArgs.algoParam->pageParam->isHugePage;
    return (DbMemCtxT *)poolShmemCtx;
}

StatusInter DeviceCreate(SeInstanceT *seIns, DbMemCtxT *seTopShmCtx)
{
    DB_POINTER2(seIns, seTopShmCtx);
    if (seIns->dataMemMgrCtxId == DB_SE_DEV_SHMEMCTX_ID) {
        return STATUS_OK_INTER;
    }

    DeviceMgrT *devMgr = NULL;
    StatusInter ret = DeviceCreateMgr(seIns, seTopShmCtx, &devMgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "create DevMgr");
        return ret;
    }

    DbMemCtxT *dbTopShmCtx = (DbMemCtxT *)DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, (uint32_t)seIns->instanceId);
    DbMemCtxT *devMgrMemCtx = DevCreateMemCtx(seIns, devMgr, dbTopShmCtx);
    if (devMgrMemCtx == NULL) {
        DbShmemCtxFree(seTopShmCtx, seIns->devMgrShm);
        DeviceHacEntryCacheDestory(seIns);
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "create DbMemCtx in create dev");
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    SeClearDevCache();  // 对devCache进行初始化
    // 存储ctxid 到instance中
    seIns->dataMemMgrCtxId = DB_SE_DEV_SHMEMCTX_ID;
    devMgr->devMemCtxId = DB_SE_DEV_SHMEMCTX_ID;

    return STATUS_OK_INTER;
}

void DeviceDestroy(SeInstanceT *seIns, DbMemCtxT *seTopShmCtx)
{
    DbShmemCtxFree(seTopShmCtx, seIns->devMgrShm);
    seIns->devMgrShm = DB_INVALID_SHMPTR;
    DeviceHacEntryCacheDestory(seIns);
}

uint32_t DevGetMaxCount(DeviceMgrT *devMgr)
{
    DB_POINTER(devMgr);
    return devMgr->devMaxCount;
}

uint32_t DevGetFreeDeviceCount(DeviceMgrT *devMgr)
{
    DB_POINTER(devMgr);
    return devMgr->freeDevCount;
}

uint32_t DevGetFreeCount(DeviceMgrT *devMgr)
{
    uint32_t freeDeviceCount = 0;
    DbSpinLock(&devMgr->lock);
    for (uint32_t i = 0; i < devMgr->devMaxCount; i++) {
        if (devMgr->devDesc[i].status == DEV_NOT_USED || devMgr->devDesc[i].status == DEV_FREE) {
            freeDeviceCount++;
        }
    }
    DbSpinUnlock(&devMgr->lock);
    return freeDeviceCount;
}

uint32_t DevGetUseCount(DeviceMgrT *devMgr)
{
    DB_POINTER(devMgr);
    uint32_t useDeviceCount = 0;
    DbSpinLock(&devMgr->lock);
    for (uint32_t i = 0; i < devMgr->devMaxCount; i++) {
        if (devMgr->devDesc[i].status == DEV_USED || devMgr->devDesc[i].status == DEV_FULL) {
            useDeviceCount++;
        }
    }
    DbSpinUnlock(&devMgr->lock);
    return useDeviceCount;
}

StatusInter DevCheckAddr(const DeviceMgrT *devMgr, PageIdT addr)
{
    if (addr.deviceId >= devMgr->devMaxCount) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "inv DeviceId %" PRIu32, addr.deviceId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    uint32_t deviceSize = devMgr->devSize;
    if (addr.blockId * devMgr->pageSize > deviceSize) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "inv PageId(%" PRIu32 " %" PRIu32 ")", addr.deviceId, addr.blockId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    return STATUS_OK_INTER;
}

void DevFreeDevice(DeviceMgrT *devMgr, uint32_t devId)
{
    DB_ASSERT(devMgr->devDesc[devId].status < DEV_USED);
    if (devMgr->firstFreeDevId == DEVICE_INVALID_ID) {
        devMgr->lastFreeDevId = devId;
        devMgr->devDesc[devId].nextDevId = DEVICE_INVALID_ID;
    } else {
        devMgr->devDesc[devId].nextDevId = devMgr->firstFreeDevId;
    }
    devMgr->firstFreeDevId = devId;
    devMgr->freeDevCount++;
}

void DevReturnDevice(DeviceMgrT *devMgr, uint32_t devId, bool needRelease)
{
    DbSpinLock(&devMgr->lock);
    DB_ASSERT(devMgr->devDesc[devId].status == DEV_FREE);
    if (needRelease && devId >= DEV_CACHE_CAPACITY) {
        DbMemCtxT *memCtx = DbGetShmemCtxById(devMgr->devMemCtxId, devMgr->ownId);
        DbShmemCtxFree(memCtx, devMgr->devDesc[devId].devPtr);
        devMgr->devDesc[devId].devPtr = DB_INVALID_SHMPTR;
        DevSetStatus(devMgr, devId, DEV_NOT_USED);
        if (g_devEntryCacheHac != NULL) {  // g_devEntryCacheHac只在服务端初始化，直连写场景也会归还cache
            g_devEntryCacheHac[devId] = NULL;
        }
    }
    DevFreeDevice(devMgr, devId);
#ifndef NDEBUG
    DeviceSetStorageType(devMgr, devId, SE_STORAGE_TYPE_BORDER);
#endif
    DbSpinUnlock(&devMgr->lock);
}

Status SeGetDevShmctxUsedSize(uint16_t instanceId, uint64_t *size)
{
    DB_POINTER(size);
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(instanceId);
    if (seIns == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "SeInstance %" PRIu16, instanceId);
        return DbGetExternalErrno(UNEXPECTED_NULL_VALUE_INTER);
    }
    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(seIns->devMgrShm);
    if (devMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "static dev shmctx get device Mgr (segid: %" PRIu32 " offset: %" PRIu32 ")", seIns->devMgrShm.segId,
            seIns->devMgrShm.offset);
        return DbGetExternalErrno(UNEXPECTED_NULL_VALUE_INTER);
    }
    void *shmCtx = DbGetShmemCtxById(devMgr->devMemCtxId, instanceId);
    if (shmCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "SeInstance %" PRIu16 "'s dev shmctx inv", instanceId);
        return DbGetExternalErrno(UNEXPECTED_NULL_VALUE_INTER);
    }
    *size = DbShmCtxGetTotalPhySize((DbMemCtxT *)shmCtx, true);
    return GMERR_OK;
}

void DevGetDevInfo(DeviceMgrT *devMgr, uint32_t devId, DeviceDescStatT *devDesc)
{
    DB_POINTER2(devMgr, devDesc);
    devDesc->isUsed = DbIsShmPtrValid(devMgr->devDesc[devId].devPtr);
    devDesc->devPtr.offset = devMgr->devDesc[devId].devPtr.offset;
    devDesc->devPtr.segId = devMgr->devDesc[devId].devPtr.segId;
    devDesc->status = devMgr->devDesc[devId].status;
    devDesc->deviceId = devId;
}

void DevGetDeviceInfo(DeviceMgrT *devMgr, MemDataInfoT *memDataStat)
{
    memDataStat->deviceSize = devMgr->devSize;
    memDataStat->ownId = devMgr->ownId;
    memDataStat->devDescSize = sizeof(DevDescT);
    uint32_t idleDevCount = 0;
    for (uint32_t i = 0; i < devMgr->devMaxCount; i++) {
        if (devMgr->devDesc[i].status == DEV_FREE) {
            idleDevCount++;
        }
    }
    memDataStat->idleDevCount = idleDevCount;
    memDataStat->devMaxCount = devMgr->devMaxCount;
    memDataStat->devMemCtxId = devMgr->devMemCtxId;
}

Status DevGetStatDescNum(SeRunCtxHdT seRunCtxHd, uint16_t *deviceDescNum)
{
    DB_POINTER2(seRunCtxHd, deviceDescNum);
    SeRunCtxT *seRunCtx = seRunCtxHd;
    SeInstanceT *seInstance = (SeInstanceT *)seRunCtx->seIns;
    DeviceMgrT *devMgr = (DeviceMgrT *)DbShmPtrToAddr(seInstance->devMgrShm);
    if (devMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
            "get devMgrShm in StatDescNum (segid: %" PRIu32 " offset: %" PRIu32 ")", seInstance->devMgrShm.segId,
            seInstance->devMgrShm.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *deviceDescNum = (uint16_t)devMgr->devMaxCount;
    return GMERR_OK;
}

void DevUpdateFreeDeviceInfo(
    DeviceMgrT *devMgr, PageIdT pageId, DevCmpInfoT *cmpInfo, uint32_t *secPageId, uint32_t curPageId)
{
    uint32_t newDevId = pageId.deviceId;
    uint32_t oldDevId = cmpInfo->freeDeviceId;
    DevNodeT *newDevNode = DevNodeGetById(devMgr, newDevId);
    // 判断是否满足更新条件
    if (newDevId >= DEV_CACHE_CAPACITY && oldDevId >= DEV_CACHE_CAPACITY) {
        if (newDevNode->freeChunkCount > cmpInfo->freeDevChunkCnt) {
            cmpInfo->freeDeviceId = newDevId;
            cmpInfo->freeDevChunkCnt = newDevNode->freeChunkCount;
            *secPageId = curPageId;
        }
    } else if (newDevId >= DEV_CACHE_CAPACITY && oldDevId < DEV_CACHE_CAPACITY) {
        cmpInfo->freeDeviceId = newDevId;
        cmpInfo->freeDevChunkCnt = newDevNode->freeChunkCount;
        *secPageId = curPageId;
    } else if (newDevId < DEV_CACHE_CAPACITY && oldDevId < DEV_CACHE_CAPACITY) {
        // 优先从后往前迁移
        if (newDevId > oldDevId) {
            cmpInfo->freeDeviceId = newDevId;
            cmpInfo->freeDevChunkCnt = newDevNode->freeChunkCount;
            *secPageId = curPageId;
        }
    }
}
