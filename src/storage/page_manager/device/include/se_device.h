/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: device manager
 * Author: zhangfengyu
 * Create: 2022-06-22
 */

#ifndef SE_DEVICE_H
#define SE_DEVICE_H

#include "adpt_types.h"
#include "se_define.h"
#include "se_dfx.h"
#include "se_log.h"
#include "se_page.h"

#ifdef FEATURE_SIMPLEREL
#define DEV_CACHE_CAPACITY 4
#define MAX_ADDR_COMPRESS_MEM (4 * 1024 * 1024)
#else
#define DEV_CACHE_CAPACITY 16
#endif
#define DEVICE_INVALID_ID 0x7fff
#define DEV_NOT_USED 0
#define DEV_FREE 1
#define DEV_USED 2
#define DEV_FULL 3

#ifdef __cplusplus
extern "C" {
#endif
typedef struct TagDevAddrT {
    uint8_t *virAddr;
} DevAddrT;

typedef struct TagDeviceDescT {
    ShmemPtrT devPtr;  // device的共享内存addr
    uint32_t status : 2;
    uint32_t nextDevId : 30;
#ifndef NDEBUG
    StorageTypeE storageType;
#endif
} DevDescT;

typedef struct TagDeviceMgr {
    DbSpinLockT lock;  // 用于控制reserve/unreserve & alloc/free 并发
    uint32_t ownId;
    uint32_t pageSize;      // 大小字节
    uint32_t devSize;       // 大小字节
    uint32_t freeDevCount;  // 可被预留的device的数量(free的device + 从未被使用也没有被预留过的device)
    uint32_t devMaxCount;  // devDesc数组的大小
    uint32_t devMemCtxId;
    uint32_t sysPageSize;
    uint32_t isHugePage : 1;
    uint32_t firstFreeDevId : 31;
    uint32_t lastFreeDevId;
    uint16_t pageSizeShiftBit;
#ifndef NDEBUG
    StorageTypeE storageType;
#endif
    DevDescT devDesc[];
} DeviceMgrT;

typedef struct DevCmpInfo {
    uint32_t freeDeviceId;
    uint32_t freeDevChunkCnt;
} DevCmpInfoT;

#ifndef NDEBUG
inline static void DeviceSetStorageType(DeviceMgrT *devMgr, uint32_t devId, StorageTypeE storageType)
{
    DB_ASSERT(devId < devMgr->devMaxCount);
    devMgr->devDesc[devId].storageType = storageType;
}
inline static StorageTypeE DeviceGetStorageType(DeviceMgrT *devMgr, uint32_t devId)
{
    DB_ASSERT(devId < devMgr->devMaxCount);
    return devMgr->devDesc[devId].storageType;
}
#endif

SO_EXPORT StatusInter DeviceCreate(SeInstanceT *seIns, DbMemCtxT *seTopShmCtx);
SO_EXPORT void DeviceDestroy(SeInstanceT *seIns, DbMemCtxT *seTopShmCtx);

inline static StatusInter DevReserveDevice(DeviceMgrT *devMgr, uint32_t devCnt)
{
    DbSpinLock(&devMgr->lock);
    if (SECUREC_UNLIKELY(devCnt > devMgr->freeDevCount)) {
        SE_LAST_ERROR(
            OUT_OF_MEMORY_INTER, "reserve dev, devCnt:%" PRIu32 ", freeCnt:%" PRIu32, devCnt, devMgr->freeDevCount);
        DbSpinUnlock(&devMgr->lock);
        return OUT_OF_MEMORY_INTER;
    }

    devMgr->freeDevCount -= devCnt;
    DbSpinUnlock(&devMgr->lock);
    return STATUS_OK_INTER;
}

inline static void DevUnreserveDevice(DeviceMgrT *devMgr, uint32_t devCnt)
{
    DbSpinLock(&devMgr->lock);
    devMgr->freeDevCount += devCnt;
    DB_ASSERT(devMgr->freeDevCount <= devMgr->devMaxCount);
    DbSpinUnlock(&devMgr->lock);
}

SO_EXPORT_FOR_TS StatusInter DevAllocDevice(DeviceMgrT *devMgr, uint32_t *devId, uint32_t *devSize, bool alloc);
SO_EXPORT_FOR_TS StatusInter DevAllocDeviceById(DeviceMgrT *devMgr, uint32_t devId, bool alloc);  // 用于持久化恢复
SO_EXPORT_FOR_TS void DevReturnDevice(DeviceMgrT *devMgr, uint32_t devId, bool needRelease);

#if !defined(FEATURE_SQL) && !defined(FEATURE_SIMPLEREL) && !defined(STREAM_EMB)
extern DevAddrT g_devEntryCache[MAX_INSTANCE_NUM][DEV_CACHE_CAPACITY];
static inline uint32_t DevGetInstanceIndex(const DeviceMgrT *devMgr)
{
    return devMgr->ownId - FIRST_INSTANCE_ID;
}
#endif

#ifdef FEATURE_SIMPLEREL
extern uint8_t **g_devEntryCacheHac;
#endif

inline static uint8_t *DevGetEntry(const DeviceMgrT *devMgr, uint32_t devId)
{
    if (SECUREC_UNLIKELY(devId >= devMgr->devMaxCount)) {
        return NULL;
    }
#if !defined(FEATURE_SQL) && !defined(FEATURE_SIMPLEREL) && !defined(STREAM_EMB)
    if (devId < DEV_CACHE_CAPACITY) {
        DevAddrT *devEntry = &g_devEntryCache[DevGetInstanceIndex(devMgr)][devId];
        if (devEntry->virAddr != NULL) {
            return devEntry->virAddr;
        }
    }
#endif
    // 缓存中没有找到，需要从device层获取
    uint8_t *devAddr = (uint8_t *)DbShmPtrToAddrAlign(devMgr->devDesc[devId].devPtr, devMgr->sysPageSize);
#if !defined(FEATURE_SQL) && !defined(FEATURE_SIMPLEREL) && !defined(STREAM_EMB)
    if ((devId < DEV_CACHE_CAPACITY) && (g_devEntryCache[DevGetInstanceIndex(devMgr)][devId].virAddr == NULL)) {
        g_devEntryCache[DevGetInstanceIndex(devMgr)][devId].virAddr = devAddr;
    }
#endif
    if (devAddr == NULL) {
        SE_ERROR(NO_DATA_SE_DEVICE_NOT_EXIST,
            "Get dev, devId:%" PRIu32 ", status:%" PRIu32 ", segId:%" PRIu32 ", offset:%" PRIu32 ".", devId,
            devMgr->devDesc[devId].status, devMgr->devDesc[devId].devPtr.segId, devMgr->devDesc[devId].devPtr.offset);
    }
    return devAddr;
}

SO_EXPORT_FOR_HAC uint8_t **DevGetEntryHac(void);
SO_EXPORT_FOR_TS uint32_t DevGetMaxCount(DeviceMgrT *devMgr);
SO_EXPORT uint32_t DevGetFreeDeviceCount(DeviceMgrT *devMgr);
SO_EXPORT uint32_t DevGetUseCount(DeviceMgrT *devMgr);
SO_EXPORT uint32_t DevGetFreeCount(DeviceMgrT *devMgr);
SO_EXPORT StatusInter DevCheckAddr(const DeviceMgrT *devMgr, PageIdT addr);

static inline bool UndoSpaceHitSplitThreShold(DeviceMgrT *devMgr)
{
#if !defined(FEATURE_SQL) && !defined(FEATURE_SIMPLEREL) && !defined(STREAM_EMB)
    return g_devEntryCache[DevGetInstanceIndex(devMgr)][DEV_CACHE_CAPACITY - 1].virAddr != NULL;
#else
    return DbIsShmPtrValid(devMgr->devDesc[DEV_CACHE_CAPACITY - 1].devPtr);
#endif
}

SO_EXPORT void DevGetDeviceInfo(DeviceMgrT *devMgr, MemDataInfoT *memDataStat);
SO_EXPORT void DevGetDevInfo(DeviceMgrT *devMgr, uint32_t devId, DeviceDescStatT *devDesc);
SO_EXPORT Status DevGetStatDescNum(SeRunCtxHdT seRunCtxHd, uint16_t *deviceDescNum);

inline static uint32_t DevGetStatus(DeviceMgrT *devMgr, uint32_t devId)
{
    if (devId > devMgr->devMaxCount) {
        DB_ASSERT(SeGetPersistMode() != PERSIST_OFF);
        return DEV_NOT_USED;
    }
    return devMgr->devDesc[devId].status;
}

inline static void DevSetStatus(DeviceMgrT *devMgr, uint32_t devId, uint32_t status)
{
    devMgr->devDesc[devId].status = status;
}

static inline uint8_t *DevGetNodeArr(DeviceMgrT *devMgr)
{
    return (uint8_t *)((uint8_t *)devMgr + sizeof(DeviceMgrT) + devMgr->devMaxCount * sizeof(DevDescT));
}

SO_EXPORT void DevUpdateFreeDeviceInfo(
    DeviceMgrT *devMgr, PageIdT pageId, DevCmpInfoT *cmpInfo, uint32_t *secPageId, uint32_t curPageId);

SO_EXPORT uint32_t DevNodeGetFreeChunkCnt(DeviceMgrT *devMgr, uint32_t deviceId);

static inline void DevSetPageSizeShiftBit(DeviceMgrT *devMgr, uint16_t pageSizeShiftBit)
{
    devMgr->pageSizeShiftBit = pageSizeShiftBit;
}

#ifdef __cplusplus
}
#endif

#endif
