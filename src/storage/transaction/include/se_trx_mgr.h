/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: header file for transaction manager
 */
#ifndef SE_TRX_MGR_H
#define SE_TRX_MGR_H

#include "se_persist_inner.h"
#include "se_trx.h"
#include "se_define.h"
#include "se_trx_inner.h"
#include "se_trx_pool.h"
#include "db_queue.h"

#ifdef __cplusplus
extern "C" {
#endif

#define OPTI_TRX_RETRY_QUEUE_STEP 16
#define OPTI_TRX_COMMIT_QUEUE_STEP 120
typedef struct EmbedTrxControl {
    uint32_t maxPageLimit;
    uint32_t curPageUsage;
    DbSpinLockT lock;
} MultiTrxControlT;

typedef enum {
    OPTI_TRX_COMMIT_NORMAL,  // 没有重试事务的状态，行为与事务正常提交一致
    OPTI_TRX_RETRY_COMMIT,  // 有重试事务正在执行的状态，控制重试事务串行执行、业务事务阻塞提交
    OPTI_TRX_CHANGE_STATE,  // 切换状态，优先处理业务提交事务队列
} OptiTrxRetryCommitStateE;

typedef struct OptimisticTrxRetryCommitInfo {
    bool isInit;
    bool reserve[3];
    OptiTrxRetryCommitStateE commitState;
    TrxIdT retryTrxId;  // 为0表示当前没有重试事务
    DbQueueT retryTrxQueue;
    DbQueueT commitTrxQueue;
} OptiTrxRetryCommitInfoT;  // 对本结构体的操作，都需要在trxMgr->latch下

struct TrxMgr {
    DbLatchT latch;  // 直连读场景加这个锁需要记录session
    int32_t trxCnt;
    TrxIdT maxTrxId;
    uint32_t maxCloneId;
    TrxIdT minActiveNormalTrxId;
    uint16_t maxTrxNum;     // 从配置文件中读出最大事务个数 + DB自己预留的个数(MAX_BG_WORKER_NUM)
    uint8_t isPersistence;  // 是否持久化模式
    uint8_t reserved;
    uint32_t rwTrxIdsOffset;  // 读写事务ID链表的偏移量 TrxIdListT      rwTrxIds
    uint32_t roTrxIdsOffset;  // 只读事务ID链表的偏移量 TrxIdListT      roTrxIds
    uint32_t trxPoolOffset;   // trxPool的偏移量 TrxPoolT        trxPool;
    uint64_t maxTrxTimeUse;
    ShmemPtrT trxMgrShmAddr;  // 用于直连读场景，当客户端进程异常退出后利用其进行恢复
    OptiTrxSetLabelLastTrxIdAndTrxCommitTime
        setFunc[(uint32_t)TRX_CHECK_READVIEW_NUM];  // yang场景设置冲突域trxId的钩子函数
    OptiTrxGetLabelLastTrxIdAndTrxCommitTime
        getFunc[(uint32_t)TRX_CHECK_READVIEW_NUM];  // yang场景获取冲突域trxId的钩子函数
    OptiTrxGetLabelName getLabelName[(uint32_t)TRX_CHECK_READVIEW_NUM];
    // 记录历史乐观事务操作过的表信息的链表，在后台线程进行内存的回收
    SeOptiTrxInfoListT optiTrxHistoryInfoList;
    ShmemPtrT multiTrxMgrShm;  // 仅在Mini KV和sql场景下才可使用
    uint16_t instanceId;
    OptiTrxRetryCommitInfoT optiTrxRetryInfo;
};
DB_CHECK_ALIGN_ATOMIC32_MEMBER(struct TrxMgr, trxCnt);
DB_CHECK_ALIGN_ATOMIC64_MEMBER(struct TrxMgr, maxTrxId);

static inline uint32_t SeTransControlSize(void)
{
    return sizeof(MultiTrxControlT);
}

static inline void InitMultiTrxMgr(MultiTrxControlT *multiTrxMgr, uint32_t capacity)
{
    multiTrxMgr->maxPageLimit = capacity;
    multiTrxMgr->curPageUsage = 0;
    DbSpinInit(&(multiTrxMgr->lock));
}

Status MultiReoverTrxMgr(SeInstanceT *seIns);
StatusInter TrxMgrCreate(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx);
SO_EXPORT_FOR_TS StatusInter TrxMgrReset(SeInstanceT *seIns);
StatusInter TrxMgrAllocTrx(TrxMgrT *trxMgr, SeRunCtxT *seRunCtx, TrxT **trxOut);

void TrxMgrFreeTrx(TrxMgrT *trxMgr, TrxT *trx);

void FreeTrxForDirectWriteInner(TrxMgrT *trxMgr, uint16_t trxSlot);

// 原子操作原因如下：
// 轻量级事务不用维护ReadView相关的最小活跃事务id，需要更高的性能要求而没有加trxMgr的SpinLock
// 轻量级事务不用ReadView原因：
// 1. 轻量级事务特点是表级别、读未提交，所以轻量级事务本身不用ReadView。
// 2. 正常事务不会操作轻量级事务的表，所以不关心轻量级事务可见性，不用使用轻量级事务id去更新最小活跃事务id
static inline TrxIdT TrxMgrGetAndIncMaxTrxId(TrxMgrT *trxMgr)
{
    DB_POINTER(trxMgr);
    // should already own spinlock
    return DbAtomicFetchAndAdd64(&trxMgr->maxTrxId, 1);
}

#ifdef EXPERIMENTAL_GUANGQI
static inline uint32_t TrxMgrGetAndIncMaxCloneId(TrxMgrT *trxMgr)
{
    DB_POINTER(trxMgr);
    // should already own spinlock
    return DbAtomicFetchAndAdd(&trxMgr->maxCloneId, 1);
}
#endif

void TrxMgrRLockWithSession(TrxMgrT *trxMgr, SeRunCtxT *seRunCtx);

void TrxMgrRUnlockWithSession(TrxMgrT *trxMgr, SeRunCtxT *seRunCtx);

/* *
 * @brief 判断某个读写事务是否在活跃事务链表中
 * @param trxMgr 事务管理器
 * @param trxId 读写事务的事务id
 * @return bool true 表示在活跃事务链表中，false则相反
 */
SO_EXPORT_FOR_TS bool TrxMgrIsRwTrxActive(TrxMgrT *trxMgr, TrxIdT trxId);

bool TrxMgrIsRwTrxActiveWithSessionLock(TrxMgrT *trxMgr, SeRunCtxT *seRunCtx, TrxIdT trxId);

StatusInter TrxMgrCheckTrxIsCommitted(TrxMgrT *trxMgr, TrxIdT trxId, bool *isCommitted);

inline static TrxIdT TrxMgrGetMinActiveNormalTrxId(const TrxMgrT *trxMgr)
{
    DB_POINTER(trxMgr);
    return trxMgr->minActiveNormalTrxId;
}

// 获取最小的事务Id
inline static TrxIdT TrxMgrGetMinTrxId(TrxMgrT *trxMgr)
{
    TrxIdT minTrxId;
    DbRWLatchR(&trxMgr->latch);
    minTrxId = TrxMgrGetMinActiveNormalTrxId(trxMgr);
    DbRWUnlatchR(&trxMgr->latch);
    return minTrxId;
}

inline static TrxIdListT *TrxMgrGetRwTrxIds(TrxMgrT *trxMgr)
{
    return (TrxIdListT *)(void *)((uintptr_t)trxMgr + trxMgr->rwTrxIdsOffset);
}

inline static TrxIdListT *TrxMgrGetRoTrxIds(TrxMgrT *trxMgr)
{
    return (TrxIdListT *)(void *)((uintptr_t)trxMgr + trxMgr->roTrxIdsOffset);
}

inline static bool TrxMgrIsTrxCommitted(TrxMgrT *trxMgr, TrxIdT trxId)
{
    return !TrxMgrIsRwTrxActive(trxMgr, trxId);
}

inline static bool TrxMgrIsTrxCommittedWithSessionLock(TrxMgrT *trxMgr, SeRunCtxT *seRunCtx, TrxIdT trxId)
{
    return !TrxMgrIsRwTrxActiveWithSessionLock(trxMgr, seRunCtx, trxId);
}

inline static TrxIdT TrxMgrGetMaxTrxId(TrxMgrT *trxMgr)
{
    return DbAtomicGet64(&trxMgr->maxTrxId);
}

inline static StatusInter TrxMgrRecoverTrxId(TrxMgrT *trxMgr, TrxIdT trxId)
{
    return TrxIdListPushbackId(TrxMgrGetRwTrxIds(trxMgr), trxId);
}

inline static bool TrxMgrHasActiveTrx(TrxMgrT *trxMgr)
{
    return TrxIdListGetLen(TrxMgrGetRwTrxIds(trxMgr)) != 0 || TrxIdListGetLen(TrxMgrGetRoTrxIds(trxMgr)) != 0;
}

inline static void TrxMgrRebuildSortRwTrxIds(TrxMgrT *trxMgr)
{
    TrxIdListT *rwTrxIds = TrxMgrGetRwTrxIds(trxMgr);
    if (TrxIdListGetLen(rwTrxIds) != 0) {
        qsort(rwTrxIds->trxIds, TrxIdListGetLen(rwTrxIds), sizeof(TrxIdT), TrxIdCmp);
        trxMgr->minActiveNormalTrxId = rwTrxIds->trxIds[0];
    }
}

// 在undo 回滚执行完恢复 rwTrxIds 和 minActiveNormalTrxId
inline static void TrxMgrResetTrxIds(TrxMgrT *trxMgr, SeInstanceT *seIns)
{
    TrxIdListInit(TrxMgrGetRwTrxIds(trxMgr), (uint32_t)seIns->seConfig.maxTrxNum);
    trxMgr->minActiveNormalTrxId = trxMgr->maxTrxId;
}

inline static TrxPoolT *TrxMgrGetTrxPool(TrxMgrT *trxMgr)
{
    return (TrxPoolT *)(void *)((uintptr_t)trxMgr + trxMgr->trxPoolOffset);
}

inline static TrxPoolT *TrxMgrGetTrxPoolByRunCtx(SeRunCtxHdT seRunCtx)
{
    TrxMgrT *trxMgr = (TrxMgrT *)((seRunCtx)->trxMgr);
    return TrxMgrGetTrxPool(trxMgr);
}

inline static TrxT *TrxMgrGetTrxBySlot(TrxMgrT *trxMgr, uint16_t slot)
{
    TrxPoolT *trxPool = TrxMgrGetTrxPool(trxMgr);
    return TrxPoolGetTrxBySlot(trxPool, slot);
}

inline static void TrxPoolFreeTrx(TrxMgrT *trxMgr, uint16_t trxSlot)
{
    DB_POINTER(trxMgr);

    TrxPoolT *trxPool = TrxMgrGetTrxPool(trxMgr);
    DbRWLatchW(&trxPool->latch);
    TrxPoolFreeTrxById(trxPool, trxSlot);
    DbRWUnlatchW(&trxPool->latch);
    int32_t oldTrxCnt = DbAtomicFetchAndSubInt(&(trxMgr->trxCnt), 1);  // 检测是否已释放
    DB_ASSERT(oldTrxCnt > 0);
}

#ifdef __cplusplus
}
#endif

#endif
