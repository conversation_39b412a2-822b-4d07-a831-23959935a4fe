/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: header file for transaction
 * Author: SE Team
 * Create: 2020-10-20
 */
#ifndef SE_TRX_INNER_H
#define SE_TRX_INNER_H

#include "se_capacity_def_inner.h"
#include "se_define.h"
#include "se_undo_pub.h"
#include "se_trx.h"
#include "se_index_inner.h"
#include "db_list.h"
#include "se_trx_lite.h"
#include "se_label_readview.h"
#include "se_clustered_hash.h"
#include "se_undo_savepoint.h"
#include "se_trx_lite.h"
#include "se_trx_clone_merge.h"

#ifdef __cplusplus
extern "C" {
#endif

#define TRX_NUM_PER_BLOCK 4
#define TRX_INVALID_PROCESS_IDX 0xff

// 事务id最大63位
#define DB_INVALID_TRX_ID 0x7FFFFFFFFFFFFFFF
#define DB_INVALID_TRX_SLOT DB_INVALID_UINT16
#define DEF_RESVED_ITEM_CNT 4
#define TRX_EMPTY_CONTAINER_ID DB_INVALID_UINT32
#define PURGER_WAIT_ESCAPE_TIME_S 10
#define PURGER_WAIT_ESCAPE_TIME_US (PURGER_WAIT_ESCAPE_TIME_S * USECONDS_IN_SECOND)

// 同时记录trx id和slot，直接寻址，避免通过hashmap来查找slot
typedef struct TrxIdSlot {
    TrxIdT trxId;
    uint16_t trxSlot;
    uint16_t reserved;
    uint32_t reserved1;
} TrxIdSlotT;

typedef enum {
    TRX_SUB_STATE_RUNNING,
    TRX_SUB_STATE_LOCK_WAIT,
    TRX_SUB_STATE_ROLLING_BACK,
    TRX_SUB_STATE_COMMITTING
} TrxSubStateE;

// 用于记录活跃事务ID, 后续需要common模块提供基于共享内存的list
typedef struct TrxIdList {
    uint16_t listLen;
    uint16_t capacity;
    uint32_t reserved;
    TrxIdT trxIds[];
} TrxIdListT;

typedef struct ReadView {
    bool isOpen;
    bool isCloneTrx;
    bool reserved[6];
    TrxIdT creatorTrxId;        // trx id of creating transaction
    TrxIdT highWaterMark;       // 高水位，大于等于highWaterMark的事务对view不可见
    TrxIdT lowWaterMark;        // 低水位，小于lowWaterMark的事务对view一定可见
    ShmemPtrT activeTrxIdsShm;  // 用于RR事务，在trx开始时保存的活跃读写事务ID列表，存于共享内存中由客户端和服务端共用
    ShmemPtrT parentTrxIdsShm;  // 长辈事务列表，从第一个根事务开始记录。isCloneTrx为true时有效
    TrxIdListT *activeTrxIdsTmp;  // 用于RC事务，在fetch操作时保存的活跃读写事务ID列表，存于动态内存使用完毕当场释放
} ReadViewT;

typedef enum {
    TRX_INVALID_CONTAINER_TYPE,  // 初始化 memset为0, 默认是无效句柄
    TRX_FIXED_HEAP_HANDLE,
    TRX_HEAP_HANDLE,
    TRX_INDEX_HANDLE,
    TRX_RES_POOL_HANDLE,
    TRX_CLUSTERED_HASH_HANDLE,
    TRX_USER_HANDLE,
} TrxContainerHdlTypeE;  // 事务性容器的类型

typedef struct TagTrxHeapStatT {
    bool isBytesChange;
    bool reserve[3];
    uint32_t insertNum;    // 事务对表项的写入记录数
    uint32_t deleteNum;    // 事务对表项的删除记录数
    uint64_t writeBytes;   // 事务对表项写入的字节数
    uint64_t deleteBytes;  // 事务对表项删除的字节数
} TrxHeapStatT;

typedef struct TransactionalContainerCtxHead {
    uint32_t id;                   // 对应类型的容器的 id. 比如 对应heap容器, id是 labelId.
    uint16_t type;                 // TrxContainerHdlTypeE 枚举
    uint16_t isGetLabelXLock : 1;  // 是否获取了对应的事务排他锁
    uint16_t isUsed : 1;  // 当前事务已经在使用，由使用容器的调用方设置，避免实际未使用，需要在提交时处理
    uint16_t needOpenHeapHdl : 1;
    uint16_t isFree : 1;  // 对应的资源是否已被释放，在未使用且未释放时，会释放容器保存的资源
    uint16_t isOpened : 1;  // 资源有Open类操作，标记在提交时执行Close
    uint16_t isFixed : 1;   // 当前容器是定长版本
    uint16_t reserve : 10;
    void *parentMemCtx;  // undo场景使用的MemCtx, 表示该容器是从哪个MemCtx申请出来的
} TrxCntrCtxHeadT;

typedef struct HeapTransactionalCtx {
    TrxCntrCtxHeadT ctxHead;
    TrxHeapStatT opStat;  // 统计事务读写操作，用于在提交回滚进行判断或者回滚统计
    ShmemPtrT heapShmAddr;
    HpRunHdlT heapRunHdl;
    CallBackStructT commitCallback;  // 事务提交时的处理, 提供给存储以外的模块(QE/EE)注册使用; 注意: 该回调不应该失败!
    void *dmInfo;
    void *vertex;  // 事务回滚, 在索引比较场景使用. noteb: 后续应该 在索引容器的上下文 维护?
    IdxKeyCmpFunc hashCompare;  // 事务回滚, 在索引比较场景使用. noteb: 后续应该 在索引容器的上下文 维护?
    IdxAddrCheckFunc addrCheck;
    IdxAddrCheckAndFetchFunc addrCheckAndFetch;
    IdxExprEvalValuesFunc exprValuesEval;  // 表达式计算时用到
} HeapTrxCtxT;

typedef struct CHTransactionalCtx {
    TrxCntrCtxHeadT ctxHead;
    ShmemPtrT chShmAddr;
    ChLabelRunHdlT chRunCtx;
    CallBackStructT commitCallback;
    void *vertexLabel;  // 事务回滚, 在索引比较场景使用. noteb: 后续应该 在索引容器的上下文 维护?
    void *vertex;
    ChLabelKeyCmpFunc pKIndexKeyCmpFunc;  // 主键索引key比较钩子函数
    IdxKeyCmpFunc secIndexkeyCmp;         // 二级索引key比较钩子函数
    IdxAddrCheckFunc addrCheck;           // 聚簇容器逻辑addr校验钩子函数
    IdxAddrCheckAndFetchFunc addrCheckAndFetch;
    uint32_t labelLatchVersionId;
    uint32_t reserve;
    LabelRWLatchT *labelRWLatch;
} ChTrxCtxT;

typedef struct TrxCntrTempCtx {
    TrxCntrCtxHeadT ctxHead;
    ShmemPtrT containerShmAddr;
    void *cntrRunHdl;
    void *label;
    void *vertexNode;  // used in purger. . noteb: 后续应该 在索引容器的上下文 维护?
} TrxCntrTempCtxT;

typedef union TagStorageTransactionalContainerCtxT {  // 存储事务性容器上下文
    TrxCntrCtxHeadT ctxHead;                          // common head
    HeapTrxCtxT heapTrxCtx;
    ChTrxCtxT chTrxCtx;
    TrxCntrTempCtxT trxCntrCtx;  // noteb: 后续将不同容器的 事务容器上下文拆分出来. 避免不同容器的信息混合
} SeTrxContainerCtxT;

typedef struct TrxPoolNode {
    uint16_t prev;
    uint16_t next;
} TrxPoolNodeT;

typedef struct DefragCond {
    bool enableDefrag;
    bool isLabelTruncate;
    bool fragRateCond;
} DefragCondT;

typedef struct TrxMgr TrxMgrT;

typedef struct TrxContainerLruCacheNode {
    SeTrxContainerCtxT *container;
    struct TrxContainerLruCacheNode *next;
} TrxContainerLruCacheNodeT;

typedef struct TrxContainerCtxList {
    uint16_t itemCnt;
    uint16_t lruCacheItemCnt;
    bool enableLruCache;
    char reserve[3];
    SeTrxContainerCtxT *freeContainer;
    SeTrxContainerCtxT ctxs[DEF_RESVED_ITEM_CNT];
    DbListT list;
    TrxContainerLruCacheNodeT *lruCacheHead;
    TrxContainerLruCacheNodeT *lruCacheTail;
} TrxContainerCtxListT;

// noteb: 临时定义，后续解决
typedef struct TrxLockIdentifierT {
    uint32_t lockIdField1;  // 32位的锁键值属性1
    uint32_t lockIdField2;  // 32位的锁键值属性2
    uint32_t lockIdField3;  // 32位的锁键值属性3
    uint16_t lockIdField4;  // 16位的锁键值属性4
    uint8_t lockType;       // 锁类型，见 SeLockTypeE
    uint8_t reserved;       // 保留字段 & 字节对齐
} SeTrxLockIdentifierT;

typedef struct LockAcqInfo {
    /* 以下是调用锁申请接口的结果缓存，每次申请重置 */
    SeTrxLockIdentifierT lastAcqId;
    /* 以下是每次锁逻辑实际申请的结果缓存，每次申请重置 */
    uint8_t lockType;      // SeLockTypeE
    uint8_t lastLockMode;  // SeLockModeE
    uint8_t lastAcqType;   // SeLockAcqTypeE
    bool isGetLock;
    bool
        isNewAllocLock;  // 新申请的锁还是复用了已持有锁，对于新申请的锁，某些场景下可以立即释放（两阶段锁的例外场景，目前只有索引遍历）
                         // noteb: 有问题再看看
    bool isDeadLockVictim;            // 是否是死锁回滚事务
    bool isTupleLockEscalateToLabel;  // 是否发生行锁升级表锁
    bool isLockConfit;                // 是否发生锁冲突
    bool isTrySameLock;               // 是否发生锁冲突
    /* 以下是事务历史锁申请的结果缓存，不会主动重置 */
    bool isHoldLabelXLock;  // 当前事务是否已持有表X锁，置位后不会再复位（可能出现false
                            // postive），只能用于快速判断没有持有表锁
    /* 以下是事务历史锁申请的结果缓存，视条件设置 */
    uint8_t firstLockType;  // 记录相同一批try lock下，第一次申请类型
    uint8_t firstLockMode;  // 记录相同一批try lock下，第一次申请模式
    uint8_t firstAcqType;   // 记录相同一批try lock下，第一次申请方式
    uint8_t reserve[3];
} LockAcqInfoT;

struct TrxBaseInfo {
    uint64_t startTime;
    uint64_t tid;     // 线程id
    TrxIdT trxId;     // 事务ID，也是事务开始时刻的逻辑时钟
    uint16_t connId;  // 事务对应的连接ID, 用于事务中断
};

typedef struct TrxBase {
    /* transaction base info */
    uint16_t trxSlot;  // 事务池的slot const
    bool readOnly;
    bool isLiteTrx;  // during lite trx, only lite-trx-label operations and auto-commit allowed
    bool isBackGround;
    bool needSendSubWhenFailed;
    bool isInteractive;  // 是否交互式事务
#ifdef FEATURE_GQL
    bool skipRowLockPessimisticRR;  // cfg designed for FES, default: false
#endif
    bool isRecovery;  // 是否时重启恢复事务, 保留内存的恢复和文件持久化恢复都会使用，使用时注意区分
    bool isAllocNew;   // v1导入使用,标记为true,则在heapOpen时直接创建新容器
    uint8_t *keyData;  // 用于回滚时的hashKey buf
    uint32_t keyLen;   // 记录keyData的长度
    uint16_t connId;   // 事务对应的连接ID, 用于事务中断
    uint16_t reserve2 : 11;
    uint16_t isTrxForceCommit : 1;
    uint16_t isRetryTrx : 1;  // 隐式开启的重试事务
    uint16_t isPurger : 1;
    uint16_t needReset : 1;  // MiniKv多进程场景的重置相关资源
    double splitTime;        // 长任务喂狗分片时间周期
    /* transaction activity info */
    TrxTypeE trxType;
    TrxStateE state;
    TrxDrStateE drState;
    TrxCloneTypeE cloneType;
    uint8_t processIdx;  // MiniKv多进程场景的进程标识
    uint8_t reserve3;
    uint16_t reserve4;
    uint64_t startTime;
    TrxIdT trxId;                 // 事务ID，也是事务开始时刻的逻辑时钟
    TrxIdT commitTs;              // commit ID, 也是事务提交时刻的逻辑时钟
    bool isNeedModifyPubsubList;  // 提交时是否要改变合并订阅链表结构。当前后台缩容事务提交时不改变合并订阅链表结构；
    bool recoveryNeedHandleResource;             // 重启恢复时是否需要自释放资源
    TrxCommitCallBackCfgT trxCommitCallBackCfg;  // 事务提交前需要先执行该函数
} TrxBaseT;

#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
typedef struct DafCommitActionCollection {
    TagLinkedListT commitActions;  // DAF delayed operation linked list, e.g., IdxDeleteActionsT
} DafCommitActionCollectionT;
#endif

typedef struct NormalTrxBase {
    IsolationLevelE isolationLevel;
    uint32_t savePointId;  // trx初始化时为0，本事务创建savePoint时递增，回滚/释放savePoint都不会修改该值
    ReadViewT readView;  // readView
    /* transaction undo segment info */
    TrxRsegT *rseg;             // the rollback segment assigned to trx
    TrxUndoLogT *normalUndo;    // 直连写只支持轻量化事务，不会涉及到客户端申请，服务端访问
    TrxUndoLogT *retainedUndo;  // 直连写只支持轻量化事务，不会涉及到客户端申请，服务端访问
    SavePointInfoT savePointInfo;
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
    DafCommitActionCollectionT *dafCommitActionCollection;  // Collection of committed operations
#endif
} NormalTrxBaseT;

typedef struct LiteTrx {
    bool isUseRsm;  // lite for rsm
    /* cached single undo record */
    bool usedBuf;        // 记录是否已经使用cacheBuf
    bool usedCacheNode;  // 标识有事务在使用cacheExtendArrFirstNode, 维护缓存节点的正确使用。（与缓存节点是否释放无关）
    bool reserved1;
    uint32_t bufLength;  // 记录cacheBuf的长度
    AdvancedPtrT cacheBuf;  // 从sessionMemCtx申请的一段buffer，其生命周期是连接级别 目前用于轻量级更新undo的tuple
    uint32_t cacheNodeSize;                // 记录 cacheExtendArrFirstNode 的长度
    AdvancedPtrT cacheExtendArrFirstNode;  // 从sessionMemCtx申请的缓存，只缓存 recordExtendNodeArr 第一个
                                           // UndoLiteRecordsNodeT 节点
    // uint8_t *
    UndoLiteRecordT cacheUndoLiteRec;  // for default
    UndoLiteRecordT *undoLiteRec;      // only used in lite transaction. at the front of the Trx to reduce cache miss
    RsmUndoRecordT *rsmUndoRec;        // 处理heap/聚簇容器写不完整的情况，指向tableInfo中的实例
    DbMemCtxT *periodMemCtx;           // 连接级别memCtx
} LiteTrxT;

typedef struct PessimisticTrx {
    /* transaction lock info */
    uint32_t holdLockNum;
    uint32_t holdLockAcqId;
    uint32_t lockNotifyId;
    uint32_t reserved;
    LockAcqInfoT lockAcqInfo;
} PessimisticTrxT;

typedef struct OptimisticTrx {
    LabelReadViewT labelReadView;
#ifdef EXPERIMENTAL_GUANGQI
    uint32_t cloneId;
    RelationTrxInfoT relationTrxInfo;    // 记录父子信息
    CloneTrxExtInfoT cloneTrxExtInfo;    // merge时需要传递给父事务的信息
    CloneControlInfoT cloneControlInfo;  // 用于父子事务状态统一的通信结构体
#endif
} OptimisticTrxT;

typedef struct NormalTrx {
    NormalTrxBaseT base;
    PessimisticTrxT pesTrx;  // 此处不能用union，在TrxMgrAllocTrx时还不知道这个事务将用作悲观还是乐观
    OptimisticTrxT optTrx;  // 它们里面的成员需要都初始化，用union会相互覆盖
} NormalTrxT;

struct Trx {
    TrxBaseT base;
    // noteb: 把这两个trx union起来，需要修改一下初始化的地方，避免二者互相覆盖，放到下个commit
    LiteTrxT liteTrx;
    NormalTrxT trx;
    /* transactional resource info */
    // 事务活跃阶段有效的 (dynamic) memory ctx, 每个事务提交/commit时清空一次
    // 如果本事务为克隆场景的父子事务，则从连接级别切换为全局级别
    void *trxPeriodMemCtx;
    void *trxSessionMemCtx;  // 一般情况下，trxPeriodMemCtx等于trxSessionMemCtx，生命周期为连接级别
    void *trxCloneMemCtx;  // 事务克隆合并时，trxPeriodMemCtx等于trxCloneMemCtx，生命周期为事务级别
    UpdateStatusMergeListCfgT updateStatusMergeListCfg;  // 更新状态合并表list回调函数
    TrxMgrT *trxMgr;                   // 某个时刻trx只对应一个线程，存储trxMgr虚拟addr进行加速
    SeRunCtxT *seRunCtx;               // 所在SeRunCtx的虚拟addr指针
    TrxContainerCtxListT cntrCtxList;  // noteb: optimize in lite trx process
    /* transaction dfx info */
    uint64_t tid;  // 线程id
};

SO_EXPORT_FOR_TS StatusInter TrxLiteUndoLogInit(TrxT *trx);

inline static IsolationLevelE TrxGetIsolationLevel(const TrxT *trx)
{
    DB_POINTER(trx);
    return trx->trx.base.isolationLevel;
}

inline static TrxTypeE TrxGetTrxType(const TrxT *trx)
{
    DB_POINTER(trx);
    return trx->base.trxType;
}
#ifdef FEATURE_GQL
inline static bool TrxSkipRowLock(const struct TagSeRunCtxT *seRunCtx)
{
    DB_POINTER(seRunCtx);
    TrxT *trx = (TrxT *)seRunCtx->trx;
    return trx->base.skipRowLockPessimisticRR;
}
#endif  // FEATURE_GQL

StatusInter ReadViewPrepare(TrxT *trx);

SO_EXPORT_FOR_TS StatusInter ReadViewPrepareForRc(SeRunCtxT *seRunCtx, ReadViewT *readView, bool isDirectRead);

SO_EXPORT_FOR_TS StatusInter ReadViewIsTrxVisible(const ReadViewT *readView, TrxIdT trxId);

SO_EXPORT_FOR_TS void ReadViewClose(TrxT *trx);

SO_EXPORT_FOR_TS void ReadViewCloseForRc(SeRunCtxT *seRunCtx, ReadViewT *readView);

StatusInter TrxBegin(TrxT *trx, const TrxCfgT *trxCfg);

StatusInter TrxRollback(TrxT *trx);

void TrxCommitInTrxList(TrxT *trx);

SO_EXPORT bool TrxIdListLookup(const TrxIdListT *ids, TrxIdT trxId);

StatusInter TrxCommit(TrxT *trx);

StatusInter OptimisticTrxCommit(TrxT *trx);
#ifdef FEATURE_YANG
StatusInter OptiTrxRetryBegin(TrxT *trx, char *peerProcInfo, uint32_t peerInfoMaxLen);

void OptiRetryTrxBeginHandle(TrxT *trx, StatusInter ret, char *peerProcInfo, uint32_t peerInfoMaxLen);
#endif

void TrxAbort(TrxT *trx);

void TrxReleaseRes(TrxT *trx);

SO_EXPORT_FOR_TS int32_t TrxIdCmp(const void *left, const void *right);

SO_EXPORT_FOR_TS StatusInter TrxIdListPushbackId(TrxIdListT *ids, TrxIdT trxId);

void TrxIdListRemoveId(TrxIdListT *ids, TrxIdT trxId);

inline static void TrxIdListInit(TrxIdListT *ids, uint32_t capacity)
{
    DB_POINTER(ids);
    ids->listLen = 0;
    ids->capacity = (uint16_t)capacity;
    for (uint32_t i = 0; i < capacity; i++) {
        ids->trxIds[i] = DB_INVALID_TRX_ID;
    }
}

inline static uint16_t TrxIdListGetLen(const TrxIdListT *ids)
{
    return (ids != NULL) ? ids->listLen : (uint16_t)0;
}

SO_EXPORT_FOR_TS StatusInter TrxStoreContainerCtx(
    TrxT *trx, uint32_t containerId, SeTrxContainerCtxT **trxCntrCtx, bool *isNewContainerOpening);

inline static void TrxSetContainerIsUsed(SeTrxContainerCtxT *trxCntrCtx)
{
    trxCntrCtx->ctxHead.isUsed = true;
}

inline static void TrxSetContainerNotUsed(SeTrxContainerCtxT *trxCntrCtx)
{
    trxCntrCtx->ctxHead.isUsed = false;
}

inline static void TrxSetContainerNeedOpenHeapHdl(SeTrxContainerCtxT *trxCntrCtx)
{
    trxCntrCtx->ctxHead.needOpenHeapHdl = true;
}

Handle TrxGetFixedHeapHandle(TrxT *trx, uint32_t resId, uint8_t **label);

HpRunHdlT TrxGetHeapHandle(TrxT *trx, uint32_t containerId);

Handle TrxGetResPoolHandle(TrxT *trx, uint32_t resId);

StatusInter TrxGetHashHandle(
    TrxT *trx, SeContainerHdl containerHdl, bool isChLabel, const HeapDmIndexIterT *indexIter, IndexCtxT **idxCtx);

Handle TrxGetClusteredHashHandle(TrxT *trx, uint32_t containerId);

SeTrxContainerCtxT *TrxGetContainerCtxById(TrxT *trx, uint32_t containerId);

inline static bool TrxContainerIsFree(const SeTrxContainerCtxT *container)
{
    DB_POINTER(container);
    return container->ctxHead.isFree;
}

inline static bool TrxContainerIsUse(const SeTrxContainerCtxT *container)
{
    DB_POINTER(container);
    return container->ctxHead.isUsed;
}

inline static bool TrxContainerIsOpen(const SeTrxContainerCtxT *container)
{
    DB_POINTER(container);
    return container->ctxHead.isOpened;
}

bool TrxIsGetLabelXLock(TrxT *trx, uint32_t labelId);

inline static void TrxMarkGetLabelXLock(TrxT *trx, uint32_t labelId)
{
    // 单个事务可能涉及多个表
    trx->trx.pesTrx.lockAcqInfo.isHoldLabelXLock = true;

    SeTrxContainerCtxT *item = TrxGetContainerCtxById((TrxT *)trx, labelId);
    if (item == NULL) {
        // 该函数目前是事务锁升级流程调用, 应该已加过意向锁，后续才能升级成排他锁, 故事务容器列表中, 应该有记录.
        DB_ASSERT(item != NULL);
        return;
    }
    // 排他锁应该只会获取一次(事务锁模块重入场景会校验), 不应该标记两次
    DB_ASSERT(item->ctxHead.isGetLabelXLock == false);
    item->ctxHead.isGetLabelXLock = true;
}

inline static bool TrxIsLiteTrx(const TrxT *trx)
{
    return trx->base.isLiteTrx;
}

inline static bool TrxIsMemoryLimit(void)
{
    double useRatio = 0.0;
    (void)DbGetDynMemUsedInfoAlarm(&useRatio);
    return useRatio > MEMORY_LIMIT_THRESHOLD;
}

inline static DbMemCtxT *TrxLiteGetPeriodMemCtx(TrxT *trx)
{
    return trx->liteTrx.periodMemCtx;
}

inline static void TrxLiteFreeBuf(TrxT *trx, AdvancedPtrT ptr)
{
    if (!APtrIsNULL(trx->liteTrx.cacheBuf) && APtrIsEqual(ptr, trx->liteTrx.cacheBuf)) {
        DB_ASSERT(
            trx->liteTrx.usedBuf || (trx->base.isRecovery && ptr.ptr == NULL));  // 保留内存恢复阶段，可能已经处理完了
        trx->liteTrx.usedBuf = false;
        return;
    }
    if (trx->base.isRecovery) {
        // 保留内存恢复阶段，memCtx在恢复完成前统一释放
        return;
    }
    APtrMemCtxFree(TrxLiteGetPeriodMemCtx(trx), ptr);
}

inline static void TrxFreeKeyDataBuf(TrxT *trx)
{
    if (trx->base.keyData != NULL) {
        DbDynMemCtxFree(trx->trxPeriodMemCtx, trx->base.keyData);
        trx->base.keyData = NULL;
        trx->base.keyLen = 0;
    }
    DB_ASSERT(trx->base.keyLen == 0);
}

void TrxClearKeyDataBuf(TrxT *trx);

SO_EXPORT_FOR_TS void TrxGetCommitTs(TrxT *trx);

// KeyData的释放由事务最后trxPeriodMemCtx的reset或者是在逃生内存切换或者还原时处理，
// 这个keyDataBuf用于提交回滚反序列化。
// 因为是取用同一段缓存buf，注意要确保不在同一时刻申请多次同时使用。
uint8_t *TrxGetKeyDataBuf(TrxT *trx, uint32_t bufLength, DbMemCtxT *usrMemCtx);

typedef enum { TRX_CLEAR_HANDLE, TRX_COMMIT_HANDLE, TRX_ROLLBACK_HANDLE } TrxHandleTypeE;

void TrxHandleContainerCtxList(TrxT *trx, TrxHandleTypeE type);

#ifdef FEATURE_SIMPLEREL
SO_EXPORT_FOR_TS void TrxHandleReleaseContainerCtx4V1(TrxT *trx, TrxHandleTypeE type, SeTrxContainerCtxT *container);
SO_EXPORT_FOR_TS StatusInter TrxStoreContainerCtx4V1(
    TrxT *trx, uint32_t containerId, SeTrxContainerCtxT **trxCntrCtx, bool isFixed, bool *isNewContainerOpening);
#endif

inline static void TrxSetCtxForIndex(HeapTrxCtxT *heapTrxCtx, const IndexOpenCfgT *idxOpenCfg)
{
    DB_POINTER2(heapTrxCtx, idxOpenCfg);
    heapTrxCtx->vertex = idxOpenCfg->vertex;
    heapTrxCtx->hashCompare = idxOpenCfg->callbackFunc.keyCmp;
    heapTrxCtx->addrCheck = idxOpenCfg->callbackFunc.addrCheck;
    heapTrxCtx->addrCheckAndFetch = idxOpenCfg->callbackFunc.addrCheckAndFetch;
    heapTrxCtx->exprValuesEval = idxOpenCfg->callbackFunc.exprValuesEval;
}

void TrxSetEscapeMemCtx(TrxT *trx);

void TrxResetEscapeMemCtx(TrxT *trx, bool ignore);

Status SeOptimisticTrxConflictCheck(const SeRunCtxHdT seRunCtx);

StatusInter TrxUndoSetTrxState(TrxT *trx, TrxStateE state);

StatusInter TrxUndoRetainedAddToHistory(TrxT *trx, TrxUndoLogT *undo);

SO_EXPORT_FOR_TS void TrxStartUndoPurger(SeInstanceT *seIns);

void TrxStartDafPurger(SeInstanceT *seIns);

StatusInter TrxClearOnCommitOrRollback(TrxT *trx);

StatusInter PessimisticTrxCommit(TrxT *trx);

Status SeSetChLabelTrxCtxForIndex(const IndexOpenCfgT *idxOpenCfg);

Status SeSetHeapTrxCtxForIndex(const IndexOpenCfgT *idxOpenCfg);

void TrxCommitHandleHeapCtx(const TrxT *trx, HpRunHdlT heapRunHdl);

void TrxRollBackHandleHeapTrxCtx(const TrxT *trx, SeTrxContainerCtxT *trxCntrCtx);

void TrxHandleChLabelTrxCtx(TrxT *trx, SeTrxContainerCtxT *trxCntrCtx, TrxHandleTypeE type);

void TrxCommitHandleHeapTrxCtx(TrxT *trx, SeTrxContainerCtxT *trxCntrCtx);

StatusInter TrxMarkContainerRecCnt(TrxT *trx);

void TrxMarkContainerRecCntFailed(TrxT *trx, uint32_t index, uint32_t listIndex);

void *HeapTrxGetVertexLabel(HeapTrxCtxT *heapTrxCtxT);

void *ChTrxGetVertexLabel(ChTrxCtxT *chTrxCtx);

Handle TrxGetFixedHeapHandle(TrxT *trx, uint32_t resId, uint8_t **label);

Handle TrxGetClusteredHashHandle(TrxT *trx, uint32_t containerId);

Status TrxCommitCallBack(TrxT *trx);

// 计算最后一条undo record的位置
inline static int32_t TrxLiteGetLastRecordPos(uint32_t recordNum, bool hasResColUndoLog)
{
    if (SECUREC_LIKELY(!hasResColUndoLog)) {
        return (int32_t)recordNum - 1;
    } else {
        return (int32_t)recordNum;
    }
}

SO_EXPORT_FOR_TS StatusInter SeCheckEmergencyTrxState(SeInstanceT *seIns);
SO_EXPORT_FOR_TS StatusInter SeResetUsedTrxResource(SeInstanceT *seIns);

#ifdef FEATURE_YANG
StatusInter InitSeOptiTrxRetryCommitInfo(TrxMgrT *trxMgr, SeInstanceT *seIns);
#endif

#ifdef FEATURE_YANG
StatusInter OptiTrxRetryCommitHandle(TrxMgrT *trxMgr, TrxT *trx);
#endif

StatusInter TrxClearOnCommit(TrxT *trx);

StatusInter TrxClearOnRollback(TrxT *trx);

TrxT *TrxMgrGetActiveTrxByTrxId(TrxMgrT *trxMgr, uint64_t id, bool isClonedId, uint16_t trxSlot, bool needLock);

Status SeTransGetTrxByTrxId(const SeRunCtxHdT seRunCtx, uint64_t id, bool isClonedId, TrxT **trx);

#ifdef EXPERIMENTAL_GUANGQI
void TrxContainerFlashSeRunCtxForTrxMerge(TrxT *trx, SeTrxContainerCtxT *container, SeRunCtxT *seRunCtx);
#endif

#ifdef __cplusplus
}
#endif

#endif  // SE_TRX_H
