/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: header file for clone merge transaction
 * Author: GMDBV5 SE Team
 * Create: 2025-07-13
 */
#ifndef SE_TRX_CLONE_MERGE_H
#define SE_TRX_CLONE_MERGE_H

#include "se_define.h"
#include "se_trx.h"
#include "db_list.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef EXPERIMENTAL_GUANGQI
typedef struct CloneTrxExtInfo {  // merge时需要传递给父事务的信息
    TrxMergeInfoT mergeInfo;
    void *mergeLabelLatchGaList;  // 使用跨连接memctx申请出来的，由父事务提交/回滚时处理
} CloneTrxExtInfoT;

typedef struct RelationTrxInfo {
    uint32_t parentCloneId;
    uint64_t parentTrxId;
    TrxIdT successorTrxId;
    TrxIdT childTrxId;
    bool successorTrxActive;
    bool childTrxActive;
} RelationTrxInfoT;

typedef enum {
    CLONE_SYNC_PHASE_PREPARE = 0,
    CLONE_SYNC_PHASE_COMMIT,
    CLONE_SYNC_PHASE_ABORT,
} CloneSyncPhaseE;

typedef struct CloneController {
    uint16_t initiateConnId;  // 创建通信结构的连接Id
    uint16_t reserve;
    CloneSyncPhaseE phase;
    Status initiateConnRet;
    uint32_t initiateConnFinish;
    Status parentConnRet;
    uint32_t parentConnFinish;
    TrxIdT initiateTrxId;
} CloneControllerT;

typedef struct CloneControlInfo {
    DbLatchT latch;
    uint32_t refCount;
    ShmemPtrT cloneControllerShm;  // CloneControllerT
} CloneControlInfoT;

StatusInter CloneTrxNonListSetActive(TrxT *trx);

StatusInter ReadViewPrepareByParentTrx(TrxT *trx, TrxT *parentTrx, TrxIdT anotherParentTrxId);

StatusInter OptimisticCloneTypeTrxCommitOrRollBack(TrxT *trx);
#endif

#ifdef __cplusplus
}
#endif

#endif  // SE_TRX_CLONE_MERGE_H
