/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: 事务锁实现
 * Author: panghaisheng
 * Create: 2020-10-23
 */
#include "se_lock.h"
#include "se_deadlock.h"
#include "se_trx_mgr.h"
#include "se_lock_table.h"
#include "se_log.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 加锁有限状态机(FSM:finite-state machine) */
const SeLockStateE G_SE_LOCK_ACQUIRE_FSM[SE_LOCK_MODE_MAX][SE_LOCK_STATE_MAX] = {
    [SE_LOCK_MODE_IS][SE_LOCK_STATE_NO_LOCK] = SE_LOCK_STATE_IS,
    [SE_LOCK_MODE_IS][SE_LOCK_STATE_IS] = SE_LOCK_STATE_IS,
    [SE_LOCK_MODE_IS][SE_LOCK_STATE_IX] = SE_LOCK_STATE_AI,
    [SE_LOCK_MODE_IS][SE_LOCK_STATE_S] = SE_LOCK_STATE_ISS,
    [SE_LOCK_MODE_IS][SE_LOCK_STATE_X] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_IS][SE_LOCK_STATE_AI] = SE_LOCK_STATE_AI,
    [SE_LOCK_MODE_IS][SE_LOCK_STATE_ISS] = SE_LOCK_STATE_ISS,

    [SE_LOCK_MODE_IX][SE_LOCK_STATE_NO_LOCK] = SE_LOCK_STATE_IX,
    [SE_LOCK_MODE_IX][SE_LOCK_STATE_IS] = SE_LOCK_STATE_AI,
    [SE_LOCK_MODE_IX][SE_LOCK_STATE_IX] = SE_LOCK_STATE_IX,
    [SE_LOCK_MODE_IX][SE_LOCK_STATE_S] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_IX][SE_LOCK_STATE_X] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_IX][SE_LOCK_STATE_AI] = SE_LOCK_STATE_AI,
    [SE_LOCK_MODE_IX][SE_LOCK_STATE_ISS] = SE_LOCK_STATE_MAX,

    [SE_LOCK_MODE_S][SE_LOCK_STATE_NO_LOCK] = SE_LOCK_STATE_S,
    [SE_LOCK_MODE_S][SE_LOCK_STATE_IS] = SE_LOCK_STATE_ISS,
    [SE_LOCK_MODE_S][SE_LOCK_STATE_IX] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_S][SE_LOCK_STATE_S] = SE_LOCK_STATE_S,
    [SE_LOCK_MODE_S][SE_LOCK_STATE_X] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_S][SE_LOCK_STATE_AI] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_S][SE_LOCK_STATE_ISS] = SE_LOCK_STATE_ISS,

    [SE_LOCK_MODE_X][SE_LOCK_STATE_NO_LOCK] = SE_LOCK_STATE_X,
    [SE_LOCK_MODE_X][SE_LOCK_STATE_IS] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_X][SE_LOCK_STATE_IX] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_X][SE_LOCK_STATE_S] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_X][SE_LOCK_STATE_X] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_X][SE_LOCK_STATE_AI] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_X][SE_LOCK_STATE_ISS] = SE_LOCK_STATE_MAX,
};

/* 解锁有限状态机(FSM:finite-state machine) */
const SeLockStateE G_SE_LOCK_RELEASE_FSM[SE_LOCK_MODE_MAX][SE_LOCK_STATE_MAX] = {
    [SE_LOCK_MODE_IS][SE_LOCK_STATE_NO_LOCK] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_IS][SE_LOCK_STATE_IS] = SE_LOCK_STATE_NO_LOCK,
    [SE_LOCK_MODE_IS][SE_LOCK_STATE_IX] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_IS][SE_LOCK_STATE_S] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_IS][SE_LOCK_STATE_X] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_IS][SE_LOCK_STATE_AI] = SE_LOCK_STATE_IX,
    [SE_LOCK_MODE_IS][SE_LOCK_STATE_ISS] = SE_LOCK_STATE_S,

    [SE_LOCK_MODE_IX][SE_LOCK_STATE_NO_LOCK] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_IX][SE_LOCK_STATE_IS] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_IX][SE_LOCK_STATE_IX] = SE_LOCK_STATE_NO_LOCK,
    [SE_LOCK_MODE_IX][SE_LOCK_STATE_S] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_IX][SE_LOCK_STATE_X] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_IX][SE_LOCK_STATE_AI] = SE_LOCK_STATE_IS,
    [SE_LOCK_MODE_IX][SE_LOCK_STATE_ISS] = SE_LOCK_STATE_MAX,

    [SE_LOCK_MODE_S][SE_LOCK_STATE_NO_LOCK] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_S][SE_LOCK_STATE_IS] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_S][SE_LOCK_STATE_IX] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_S][SE_LOCK_STATE_S] = SE_LOCK_STATE_NO_LOCK,
    [SE_LOCK_MODE_S][SE_LOCK_STATE_X] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_S][SE_LOCK_STATE_AI] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_S][SE_LOCK_STATE_ISS] = SE_LOCK_STATE_IS,

    [SE_LOCK_MODE_X][SE_LOCK_STATE_NO_LOCK] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_X][SE_LOCK_STATE_IS] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_X][SE_LOCK_STATE_IX] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_X][SE_LOCK_STATE_S] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_X][SE_LOCK_STATE_X] = SE_LOCK_STATE_NO_LOCK,
    [SE_LOCK_MODE_X][SE_LOCK_STATE_AI] = SE_LOCK_STATE_MAX,
    [SE_LOCK_MODE_X][SE_LOCK_STATE_ISS] = SE_LOCK_STATE_MAX,
};

#define SE_LOCK_NEED_CHECK_SHARE_CNT 0x80000000
#define SE_LOCK_TABLE_BUCKET_NUM_DEFAULT 1021

StatusInter SeLockMgrLockEscalation(SeRunCtxT *seRunCtx, SeLockAcqRunCtxT *acqRunCtx);

typedef void (*SeLockUpdXFunc)(SeLockAcqRunCtxT *acqRunCtx);

/*
 * 锁管理器：不支持动态扩展，由4部分组成：SeLockMgr结构体 + SeLockTableT + SeLockAcqPoolT + SeLockNotifyPoolT
 * 其中SeLockTableT中的锁池部分是延迟申请的(SeLockEntryPoolT)
 * 内存占用：根据配置项“最大连接数”来申请(SeLockMgrCalculateSizeByMaxConnNum)
 *  大于100个连接，小于等于1024个连接时：
 *      sizeof(锁管理器) = 631636
 *  小于等于100个连接时：
 *      sizeof(锁管理器) = 343352
 */
struct SeLockMgr {
    uint32_t lockTableOffset;       // SeLockTableT *lockTable;
    uint32_t lockAcqPoolOffset;     // SeLockAcqPoolT *acqPool;
    uint32_t lockNotifyPoolOffset;  // SeLockNotifyPoolT *notifyPool;
    uint32_t reserve;               // 有信号量，一定得字节对齐
};

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
inline static bool SeLockMgrLockCheckStarve(SeLockT *lock, uint16_t maxLockShareCnt)
{
    /* 当容忍插队数比较大时, 不允许新的兼容模式直接获取资源, 返回 isStarve */
    if (SECUREC_UNLIKELY(lock->shareNumAfterX >= maxLockShareCnt + SE_LOCK_NEED_CHECK_SHARE_CNT)) {
        return true;
    }

    lock->shareNumAfterX |= SE_LOCK_NEED_CHECK_SHARE_CNT;
    lock->shareNumAfterX++;  // shareNumAfterX 记录在队列中有被阻塞的等待者时, 新插队的个数
    return false;
}

bool SeLockMgrLockCheckIsStarved(SeLockAcqRunCtxT *acqRunCtx, SeLockStateE newLockState)
{
    DB_POINTER2(acqRunCtx, acqRunCtx->lock);
    // 如果新的lockState 和队列中等待的 mode, 有不兼容场景时, 则认为需要检查是否饿死, 让修改流程的优先级提高 : 防止 X ,
    // IX 饿死
    SeLockT *lock = acqRunCtx->lock;
    uint16_t maxLockShareCnt = acqRunCtx->maxLockShareCnt;
    if (lock->requestedCount[SE_LOCK_MODE_X] != 0u && newLockState != SE_LOCK_STATE_X) {
        return SeLockMgrLockCheckStarve(lock, maxLockShareCnt);
    } else if (lock->requestedCount[SE_LOCK_MODE_IX] != 0u) {
        if (newLockState == SE_LOCK_STATE_S || newLockState == SE_LOCK_STATE_ISS) {
            return SeLockMgrLockCheckStarve(lock, maxLockShareCnt);
        }
    }
    lock->shareNumAfterX = 0u;  // 没有被阻塞的等待者, 清空share个数
    return false;
}

bool SeCompareLockId(const SeLockIdentifierT *lockId1, const SeLockIdentifierT *lockId2)
{
    DB_POINTER2(lockId1, lockId2);
    if (lockId1->lockType != lockId2->lockType || lockId1->lockType == (uint8_t)SE_LOCK_TYPE_NO_USED_LOCK) {
        return false;
    }
    switch (lockId1->lockType) {
        case SE_LOCK_TYPE_LABEL_LOCK:
            return (lockId1->lockIdField1 == lockId2->lockIdField1) && (lockId1->lockIdField2 == lockId2->lockIdField2);
        case SE_LOCK_TYPE_PAGE_LOCK:
            return (lockId1->lockIdField1 == lockId2->lockIdField1) &&
                   (lockId1->lockIdField2 == lockId2->lockIdField2) && (lockId1->lockIdField3 == lockId2->lockIdField3);
        case SE_LOCK_TYPE_TUPLE_LOCK:
            return (lockId1->lockIdField1 == lockId2->lockIdField1) &&
                   (lockId1->lockIdField2 == lockId2->lockIdField2) &&
                   (lockId1->lockIdField3 == lockId2->lockIdField3) && (lockId1->lockIdField4 == lockId2->lockIdField4);
        default:
            return false;  // 不支持的锁类型
    }
}

void SeLockMgrHoldAcqInfoSave(const SeRunCtxT *seRunCtx, TrxT *trx, SeLockT *lock, SeLockAcqEntryT *lockAcqEntry)
{
    DB_POINTER4(seRunCtx, trx, lock, lockAcqEntry);
    /* 一个方向是从lock角度, 查看各个持有者信息, 另外一个方向是持有者角度, 查看持有的各个lock */
    SeCrossCoordHeadT crossCoordHead = {
        .lockCoordHead = (lock->holdNum == 0u) ? SE_LOCK_ACQ_INVALID_ID : lock->holderAcqId,
        .trxCoordHead = (trx->trx.pesTrx.holdLockNum == 0u) ? SE_LOCK_ACQ_INVALID_ID : trx->trx.pesTrx.holdLockAcqId};
    SeLockAcqCrossListPush2Head(seRunCtx, lockAcqEntry, crossCoordHead);
    lock->holderAcqId = lockAcqEntry->acqId;
    trx->trx.pesTrx.holdLockAcqId = lockAcqEntry->acqId;
    lock->holdNum++;
    trx->trx.pesTrx.holdLockNum++;
    trx->trx.pesTrx.lockAcqInfo.isNewAllocLock = true;
}

void SeLockMgrHoldAcqInfoRemoveByTrxHead(const SeRunCtxT *seRunCtx, TrxT *trx, SeLockT *lock)
{
    DB_POINTER3(seRunCtx, trx, lock);
    /* note: trx方向, 应当由持有者修改, 才没有并发问题; 而lock方向, 应当在调用该函数前, 添加lock bucket的锁 */
    DB_ASSERT(lock->holdNum > 0u);
    DB_ASSERT(trx->trx.pesTrx.holdLockNum > 0u);
    SeCrossCoordHeadT crossCoordHead = {
        .lockCoordHead = lock->holderAcqId, .trxCoordHead = trx->trx.pesTrx.holdLockAcqId};
    SeLockAcqCrossListRemoveByTrxHead(seRunCtx, &crossCoordHead);

    SeLockAcqReleaseEntryByAcqId(seRunCtx, trx->trx.pesTrx.holdLockAcqId);
    trx->trx.pesTrx.holdLockAcqId = crossCoordHead.trxCoordHead;
    trx->trx.pesTrx.holdLockNum--;

    lock->holderAcqId = crossCoordHead.lockCoordHead;
    lock->holdNum--;
}

void SeLockMgrWaitListRemoveNode(const SeRunCtxT *seRunCtx, SeLockAcqRunCtxT *acqRunCtx)
{
    DB_POINTER2(seRunCtx, acqRunCtx);
    SeLockNotifyEntryT *lockNotifyEntry = acqRunCtx->lockNotifyEntry;
    // 把节点从等待队列中移除. 节点的内存由被唤醒者负责释放
    SeLockNotifyListRemove(seRunCtx, &acqRunCtx->lock->waitNotifyList, lockNotifyEntry);
    DB_ASSERT(acqRunCtx->lock->waitNum > 0);
    acqRunCtx->lock->waitNum--;
    SeLockModeE waitLockMode = SeLockAcqGetWaitLockMode(acqRunCtx->lockAcqEntry);
    DB_ASSERT(acqRunCtx->lock->requestedCount[waitLockMode] != 0u);
    acqRunCtx->lock->requestedCount[waitLockMode]--;
    if (acqRunCtx->lockAcqEntry->acqInfo.specialAcq == (uint16_t)SE_LOCK_ACQ_S_UPD_X ||
        acqRunCtx->lockAcqEntry->acqInfo.specialAcq == (uint16_t)SE_LOCK_ACQ_I_UPD_X) {
        DB_ASSERT(acqRunCtx->lock->waitForUpdX > 0);
        acqRunCtx->lock->waitForUpdX--;
    }
}

void SeLockFreeLockResForAcquireFailed(const SeRunCtxT *seRunCtx, SeLockAcqRunCtxT *acqRunCtx)
{
    DB_POINTER2(seRunCtx, acqRunCtx);
    if (acqRunCtx->isNewAcqEntry && acqRunCtx->lockAcqEntry) {
        SeLockAcqReleaseEntry(seRunCtx, acqRunCtx->lockAcqEntry);  // 新申请的 lockAcqEntry 也要释放
        acqRunCtx->lockAcqEntry = NULL;
        acqRunCtx->isNewAcqEntry = false;
    }
    // 在申请锁场景, 从 lockPool lookup到一个新的锁时, 如果后续流程失败, 要把 lockType 重置一下, 用来释放
    if (SeLockIsNewAllocLock(acqRunCtx->lock)) {
        acqRunCtx->lock->lockId.lockType = SE_LOCK_TYPE_NO_USED_LOCK;
        // 释放锁池中分配来的锁
        SeLockMgrFreeLockPoolEntryFromBucket(
            acqRunCtx->trx->seRunCtx, acqRunCtx->lock, (SeLockBucketT *)acqRunCtx->bucket);
        acqRunCtx->lock = NULL;
    }
}

void SeLockMgrCheckDeadLock(SeRunCtxT *seRunCtx, TrxT *trx)
{
    DB_POINTER2(seRunCtx, trx);

    uint32_t bufSize = (uint32_t)sizeof(DeadlockInfoBufT) << 1u;  // 申请两个DeadlockInfoBufT结构体
    DeadlockInfoBufT *deadlockCycle = DbDynMemCtxAlloc(seRunCtx->sessionMemCtx, bufSize);
    if (deadlockCycle == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "deadlock checkout malloc");
        return;  // 启动死锁检测失败的时候，只打日志，不返回错误，让上层流程继续执行
    }
    (void)memset_s(deadlockCycle, bufSize, 0, bufSize);
    DeadlockInfoBufT *visited = deadlockCycle + 1;

    StatusInter ret = DeadlockDetect(seRunCtx, trx->base.trxId, trx->base.trxSlot, visited, deadlockCycle);
    if (ret == LK_NO_AVA_DEAD_LOCK_SELF || ret == LK_NO_AVA_DEAD_LOCK_OTHER || ret == LK_NO_AVA_DEAD_LOCK_MAX_DEPTH) {
        TrxIdSlotT victimTrxIdSlot = DeadlockChooseVictim(seRunCtx, deadlockCycle);
        // 设置标记的过程, 避免不同事务的线程提交并发问题, 加读锁即可
        DbRWLatchR(&((TrxMgrT *)seRunCtx->trxMgr)->latch);
        TrxT *victimTrx = (trx->base.trxId == victimTrxIdSlot.trxId) ?
                              trx :
                              TrxMgrGetActiveTrxByTrxId(
                                  seRunCtx->trxMgr, victimTrxIdSlot.trxId, false, victimTrxIdSlot.trxSlot, true);
        if (victimTrx) {  // 仅设置一个标志位;
            victimTrx->trx.pesTrx.lockAcqInfo.isDeadLockVictim = true;
        }
        DbRWUnlatchR(&((TrxMgrT *)seRunCtx->trxMgr)->latch);
    }

    DbDynMemCtxFree(seRunCtx->sessionMemCtx, deadlockCycle);
    deadlockCycle = NULL;
    return;  // 发生死锁/深度过深时，只有victim事务才作为错误返回，其他的事务按正常情况继续执行
}

static void SeLockMgrTryCheckDeadLock(SeRunCtxT *seRunCtx, SeLockAcqRunCtxT *acqRunCtx, uint32_t *deadlockCheckCnt)
{
    DB_POINTER3(seRunCtx, acqRunCtx, deadlockCheckCnt);

    SeConfigT *seConfig = &((SeInstanceT *)seRunCtx->seIns)->seConfig;
    SeLockNotifyEntryT *lockNotifyEntry = acqRunCtx->lockNotifyEntry;
    // 开始死锁检测, 因为 死锁检测 需要反复对不同的 bucket加锁, 故此时不应该持有 lockTable 的锁
    if (lockNotifyEntry->notifyNode.waitTimeMs >= (seConfig->deadlockCheckPeriod * (*deadlockCheckCnt))) {
        (*deadlockCheckCnt)++;
        SeLockTableReleaseBucketLock(acqRunCtx->bucket);  // 死锁检测前, 释放锁
        SeLockMgrCheckDeadLock(seRunCtx, acqRunCtx->trx);
        SeLockTableAddBucketLock(seRunCtx->lockTable, &acqRunCtx->lock->lockId, &acqRunCtx->bucket);  // 重新加锁
    }
    return;
}

static void SeLockMgrTryJumpQueue(SeRunCtxT *seRunCtx, SeLockAcqRunCtxT *acqRunCtx, uint32_t *jumpQueueCnt)
{
    DB_POINTER4(seRunCtx, acqRunCtx, acqRunCtx->lockNotifyEntry, jumpQueueCnt);

    SeConfigT *seConfig = &((SeInstanceT *)seRunCtx->seIns)->seConfig;
    SeLockNotifyEntryT *lockNotifyEntry = acqRunCtx->lockNotifyEntry;
    DB_ASSERT(seConfig->lockJumpQueuePeriod != 0u);
    if (lockNotifyEntry->notifyNode.waitTimeMs >= (seConfig->lockJumpQueuePeriod * (*jumpQueueCnt))) {
        (*jumpQueueCnt)++;
        // 插队: 把节点从等待队列中移除. 并插入队列头部.
        SeLockNotifyListRemove(seRunCtx, &acqRunCtx->lock->waitNotifyList, lockNotifyEntry);
        SeLockNotifyListPush2Head(&acqRunCtx->lock->waitNotifyList, lockNotifyEntry);
    }
}

StatusInter SeLockMgrWaitForWakeUp(SeRunCtxT *seRunCtx, SeLockAcqRunCtxT *acqRunCtx)
{
    DB_POINTER4(seRunCtx, seRunCtx->lockTable, acqRunCtx, acqRunCtx->lockNotifyEntry);

    StatusInter ret = STATUS_OK_INTER;
    SeConfigT *seConfig = &((SeInstanceT *)seRunCtx->seIns)->seConfig;
    SeLockNotifyEntryT *lockNotifyEntry = acqRunCtx->lockNotifyEntry;
    uint32_t deadlockCheckCnt = 1;
    uint32_t jumpQueueCnt = 1;
    uint64_t startTime = DbRdtsc();
    uint64_t endTime = 0;
    while (true) {
        // note: DbSemTimedWait 使用系统函数 sem_timedwait 调用, 如果调整系统时间, 可能导致死锁, noteb: 后续看看怎么调整
        (void)DbSemTimedWait(
            &lockNotifyEntry->notifyNode.sem, seConfig->lockWakeupPeriod * (uint32_t)USECONDS_IN_MSECOND);

        // 检查前, 先加 lock bucket的锁, 控制并发
        SeLockTableAddBucketLock(seRunCtx->lockTable, &acqRunCtx->lock->lockId, &acqRunCtx->bucket);

        // 是否加锁成功
        if (lockNotifyEntry->notifyNode.isGrandWakeUp) {
            break;  // 等待成功, release流程已经把node从等待链表中移走
        }

        // 是否等待超时
        endTime = DbRdtsc();
        lockNotifyEntry->notifyNode.waitTimeMs = (uint32_t)DbToMseconds(endTime - startTime);
        if (lockNotifyEntry->notifyNode.waitTimeMs >= seRunCtx->trxLockTimeOut) {
            ret = LK_NO_AVA_WAIT_TOO_LONG;
            break;
        }

        // 尝试死锁检测，内部有释放latch离开临界区的操作，检测结果不影响事务正常执行，除非事务被选为死锁victim
        SeLockMgrTryCheckDeadLock(seRunCtx, acqRunCtx, &deadlockCheckCnt);

        // 重新判断是否已经获取到锁
        if (lockNotifyEntry->notifyNode.isGrandWakeUp) {
            break;  // 等待成功, release流程已经把node从等待链表中移走
        }

        // 发生死锁，且自己被通知为死锁受害者
        if (acqRunCtx->trx->trx.pesTrx.lockAcqInfo.isDeadLockVictim) {
            ret = LK_NO_AVA_DEAD_LOCK;
            break;
        }

        // 尝试插队提高优先级
        SeLockMgrTryJumpQueue(seRunCtx, acqRunCtx, &jumpQueueCnt);

        // 释放锁, 继续等待
        SeLockTableReleaseBucketLock(acqRunCtx->bucket);
    }

    if (ret != STATUS_OK_INTER) {
        SE_WARN(ret,
            "(%" PRIu64 ") Waiting trx lock too long, ret:%" PRIu32 ", lockId(%" PRIu32 ",%" PRIu32 ",%" PRIu32
            ",%" PRIu16 ") lockType:%" PRIu8 ", waitTime = %" PRIu32 "(ms), timeOut = %" PRIu32 "(ms)",
            ((TrxT *)(seRunCtx->trx))->base.trxId, (uint32_t)ret, acqRunCtx->lock->lockId.lockIdField1,
            acqRunCtx->lock->lockId.lockIdField2, acqRunCtx->lock->lockId.lockIdField3,
            acqRunCtx->lock->lockId.lockIdField4, acqRunCtx->lock->lockId.lockType,
            lockNotifyEntry->notifyNode.waitTimeMs, seRunCtx->trxLockTimeOut);
        SeLockMgrWaitListRemoveNode(seRunCtx, acqRunCtx);  // 内部会assert此时锁必定有等待者（至少包含该事务）
        SeLockFreeLockResForAcquireFailed(seRunCtx, acqRunCtx);
    }

    SeLockTableReleaseBucketLock(acqRunCtx->bucket);
    SeLockNotifyReleaseEntry(seRunCtx, lockNotifyEntry);
    acqRunCtx->trx->trx.pesTrx.lockNotifyId = SE_LOCK_NOTIFY_INVALID_ID;
    return ret;
}

StatusInter SeLockMgrWaitListAddNode(const SeRunCtxT *seRunCtx, SeLockAcqRunCtxT *acqRunCtx)
{
    DB_POINTER3(seRunCtx, acqRunCtx, acqRunCtx->lock);

    // 使用waitNum判断Head是否存在(锁池释放时, lock的内存置空, 但是 共享内存ptr 0 可能是有效值
    if (acqRunCtx->lock->waitNum == 0u) {
        acqRunCtx->lock->waitNotifyList.head = SE_LOCK_NOTIFY_INVALID_ID;
        acqRunCtx->lock->waitNotifyList.tail = SE_LOCK_NOTIFY_INVALID_ID;
    }

    SeLockNotifyEntryT *lockNotifyEntry = SeLockNotifyAllocEntry(seRunCtx);
    if (lockNotifyEntry == NULL) {
        SE_LAST_ERROR(LK_NO_AVA_LOCK_POOL_NO_RESOURCE, "lock notify out of resource.");
        return LK_NO_AVA_LOCK_POOL_NO_RESOURCE;
    }
    SeLockNotifyT *notifyNode = &lockNotifyEntry->notifyNode;
    notifyNode->isGrandWakeUp = false;
    notifyNode->isDeadLockVictim = false;
    notifyNode->waitTimeMs = 0;
    notifyNode->lockAcqId = acqRunCtx->lockAcqEntry->acqId;
    acqRunCtx->lock->waitNum++;
    acqRunCtx->lockAcqEntry->acqInfo.specialAcq = (uint16_t)acqRunCtx->lockAcqState;
    COMPILER_BARRIER;  // 防止编译器优化导致trx->lockNotifyId比notifyNode->lockAcqId提前赋值
    acqRunCtx->lockNotifyEntry = lockNotifyEntry;
    acqRunCtx->trx->trx.pesTrx.lockNotifyId = lockNotifyEntry->lockNotifyId;
    SeLockModeE waitLockMode = SeLockAcqGetWaitLockMode(acqRunCtx->lockAcqEntry);
    acqRunCtx->lock->requestedCount[waitLockMode]++;
    if (SECUREC_UNLIKELY(acqRunCtx->lockAcqEntry->acqInfo.specialAcq == (uint16_t)SE_LOCK_ACQ_S_UPD_X ||
                         acqRunCtx->lockAcqEntry->acqInfo.specialAcq == (uint16_t)SE_LOCK_ACQ_I_UPD_X)) {
        acqRunCtx->lock->waitForUpdX++;  // 升级场景, 插队到队列最前面
        SeLockNotifyListPush2Head(&acqRunCtx->lock->waitNotifyList, lockNotifyEntry);
    } else {
        SeLockNotifyListPush2Tail(seRunCtx, &acqRunCtx->lock->waitNotifyList, lockNotifyEntry);
    }

    return STATUS_OK_INTER;
}

// 支持label/tuple锁升级, 其他场景勿用
void SeLockMgrUpdS2XMode(SeLockAcqRunCtxT *acqRunCtx)
{
    DB_POINTER3(acqRunCtx, acqRunCtx->lock, acqRunCtx->lockAcqEntry);

    SeLockT *lock = acqRunCtx->lock;
    SeLockAcqEntryT *lockAcqEntry = acqRunCtx->lockAcqEntry;
    DB_ASSERT(lock != NULL && (lock->lockId.lockType == (uint8_t)SE_LOCK_TYPE_TUPLE_LOCK ||
                                  lock->lockId.lockType == (uint8_t)SE_LOCK_TYPE_LABEL_LOCK));
    /* 对于label/tuple锁, 只有 X 和 S 场景, 由于事先已经校验过, 同一个事务, 相同模式的锁, 不会重复累加;
     * 故锁升级, 只需要找到原来的持有信息(只有一份持有信息), 并更新即可 */

    DB_ASSERT(lockAcqEntry->acqInfo.lockMode == (uint16_t)SE_LOCK_MODE_S);
    lock->grantLockState = SE_LOCK_STATE_X;
    lock->grantedSum = 1u;
    DB_ASSERT(lock->grantedCount[SE_LOCK_MODE_S] > 0u);
    lock->grantedCount[SE_LOCK_MODE_S]--;  // 升级场景, 减去S计数, 增加X计数
    lock->grantedCount[SE_LOCK_MODE_X] = 1u;
    lock->shareUpdX++;

    lockAcqEntry->acqInfo.lockMode = SE_LOCK_MODE_X;         // 修改lock上持有信息的链表节点
    lockAcqEntry->acqInfo.specialAcq = SE_LOCK_ACQ_S_UPD_X;  // 可能重复赋值, 确保锁申请信息正确
    acqRunCtx->isGetLock = true;
}

// 只支持label锁从IS或者IX升级到排他锁，其他场景勿用
void SeLockMgrUpdI2XMode(SeLockAcqRunCtxT *acqRunCtx)
{
    DB_POINTER3(acqRunCtx, acqRunCtx->lock, acqRunCtx->lockAcqEntry);

    SeLockT *lock = acqRunCtx->lock;
    SeLockAcqEntryT *lockAcqEntry = acqRunCtx->lockAcqEntry;
    DB_ASSERT(lock != NULL && lock->lockId.lockType == (uint8_t)SE_LOCK_TYPE_LABEL_LOCK);
    /* 对于label锁, 只有 IX 和 IS和X 场景, 由于事先已经校验过, 同一个事务, 相同模式的锁, 不会重复累加;
     * 故锁升级, 只需要找到原来的持有信息(只有一份持有信息), 并更新即可 */

    DB_ASSERT(lockAcqEntry->acqInfo.lockMode == (uint16_t)SE_LOCK_MODE_IS ||
              lockAcqEntry->acqInfo.lockMode == (uint16_t)SE_LOCK_MODE_IX);
    lock->grantLockState = SE_LOCK_STATE_X;
    lock->grantedCount[lockAcqEntry->acqInfo.lockMode]--;  // 升级场景, 减去IS或者IX计数, 增加X计数
    DB_ASSERT(lock->grantedCount[lockAcqEntry->acqInfo.lockMode] == 0u);
    DB_ASSERT(lock->grantedCount[SE_LOCK_MODE_X] == 0u);
    lock->grantedCount[SE_LOCK_MODE_X] = 1u;
    lock->shareUpdX++;

    lockAcqEntry->acqInfo.lockMode = SE_LOCK_MODE_X;         // 修改lock上持有信息的链表节点
    lockAcqEntry->acqInfo.specialAcq = SE_LOCK_ACQ_I_UPD_X;  // 可能重复赋值, 确保锁申请信息正确
    acqRunCtx->isGetLock = true;
}

// 只支持lable锁从IS升级到IX, 其他场景勿用
void SeLockMgrUpdIS2IX(SeLockAcqRunCtxT *acqRunCtx)
{
    DB_POINTER3(acqRunCtx, acqRunCtx->lock, acqRunCtx->lockAcqEntry);

    SeLockT *lock = acqRunCtx->lock;
    SeLockAcqEntryT *lockAcqEntry = acqRunCtx->lockAcqEntry;
    DB_ASSERT(lock != NULL && lock->lockId.lockType == (uint8_t)SE_LOCK_TYPE_LABEL_LOCK);
    /* 对于label锁, 只有 IX 和 IS和X 场景, 由于事先已经校验过, 同一个事务, 相同模式的锁, 不会重复累加;
     * 故锁升级, 只需要找到原来的持有信息(只有一份持有信息), 并更新即可 */

    DB_ASSERT(lockAcqEntry->acqInfo.lockMode == (uint16_t)SE_LOCK_MODE_IS);
    DB_ASSERT(lock->grantedCount[SE_LOCK_MODE_IS] > 0u);
    lock->grantedCount[SE_LOCK_MODE_IS]--;  // 升级场景, 减去IS计数, 增加IX计数
    if (lock->grantedCount[SE_LOCK_MODE_IS] == 0u) {
        SeLockStateE newLockState = G_SE_LOCK_RELEASE_FSM[SE_LOCK_MODE_IS][lock->grantLockState];
        DB_ASSERT(newLockState != SE_LOCK_STATE_MAX);
        lock->grantLockState = newLockState;
    }
    if (lock->grantedCount[SE_LOCK_MODE_IX] == 0u) {
        SeLockStateE newLockState = G_SE_LOCK_ACQUIRE_FSM[SE_LOCK_MODE_IX][lock->grantLockState];
        DB_ASSERT(newLockState != SE_LOCK_STATE_MAX);
        lock->grantLockState = newLockState;
    }
    lock->grantedCount[SE_LOCK_MODE_IX]++;

    lockAcqEntry->acqInfo.lockMode = SE_LOCK_MODE_IX;  // 修改lock上持有信息的链表节点
    acqRunCtx->isGetLock = true;
}

typedef SeLockAcqTypeE (*SeLockMgrCheckSameOrUpdFun)(
    SeLockAcqRunCtxT *acqRunCtx, SeLockAcqEntryT *lockAcqEntry, bool *isCheckDone);

SeLockAcqTypeE SeLockMgrTupleLockCheckSameTrxOrUpd(
    SeLockAcqRunCtxT *acqRunCtx, SeLockAcqEntryT *lockAcqEntry, bool *isCheckDone)
{
    DB_POINTER3(acqRunCtx, lockAcqEntry, isCheckDone);

    *isCheckDone = true;
    // 已经持有一样的mode 或者更加 严格的mode, 不重复加锁
    if (SeLockIsSameModeOrHigher(lockAcqEntry->acqInfo.lockMode, acqRunCtx->lockMode)) {
        return SE_LOCK_ACQ_IS_EXIST;
    }

    DB_ASSERT(acqRunCtx->lockMode == SE_LOCK_MODE_X);  // 当前持有S, 正在申请X锁
    DB_ASSERT(acqRunCtx->lock->shareUpdX == 0);  // 升级场景，至少本事务还持有共享锁，不应该有人已经升级成功了
    if (acqRunCtx->lock->waitForUpdX > 0) {
        // 已经等待升级到排他锁的情况下，报错，最多只有一个可升级到X
        DB_ASSERT(acqRunCtx->lock->waitForUpdX == 1);  // 当前只能有一个等待升级者
        SE_LAST_ERROR(LK_NO_AVA_LOCK_UPDATE_CONFLICT,
            "trx:%" PRIu64 " update tuple lock but lock waiting for update(%" PRIu64 ")", acqRunCtx->trx->base.trxId,
            lockAcqEntry->acqInfo.trxId);
        return SE_LOCK_ACQ_MAX;
    }
    acqRunCtx->lockAcqEntry = (SeLockAcqEntryT *)lockAcqEntry;  // 获取之前的锁的申请记录, 不要在check流程中修改字段
    return SE_LOCK_ACQ_S_UPD_X;
}

SeLockAcqTypeE SeLockMgrLabelLockCheckSameTrxOrUpd(
    SeLockAcqRunCtxT *acqRunCtx, SeLockAcqEntryT *lockAcqEntry, bool *isCheckDone)
{
    DB_POINTER3(acqRunCtx, lockAcqEntry, isCheckDone);

    *isCheckDone = true;  // 每个事务最多持有一个事务锁
    // 已经持有一样的mode 或者更加 严格的mode, 不重复加锁
    if (SeLockIsSameModeOrHigher(lockAcqEntry->acqInfo.lockMode, acqRunCtx->lockMode)) {
        return SE_LOCK_ACQ_IS_EXIST;
    }

    DB_ASSERT((acqRunCtx->lockMode == SE_LOCK_MODE_X) || (acqRunCtx->lockMode == SE_LOCK_MODE_IX));
    DB_ASSERT(acqRunCtx->lock->shareUpdX == 0);  // 升级场景，至少本事务还持有共享锁，不应该有人已经升级成功了
    if (acqRunCtx->lockMode == SE_LOCK_MODE_X) {
        if (acqRunCtx->lock->waitForUpdX > 0) {
            // 已经等待升级到排他锁的情况下，报错，最多只有一个可升级到X
            DB_ASSERT(acqRunCtx->lock->waitForUpdX == 1);  // 当前只能有一个等待升级者
            SE_LAST_ERROR(LK_NO_AVA_LOCK_UPDATE_CONFLICT,
                "trx:%" PRIu64 " update label lock but lock waiting for update(%" PRIu64 ")",
                acqRunCtx->trx->base.trxId, lockAcqEntry->acqInfo.trxId);
            return SE_LOCK_ACQ_MAX;
        }
        acqRunCtx->lockAcqEntry = (SeLockAcqEntryT *)lockAcqEntry;  // 获取之前的锁的申请记录, 不要在check流程中修改字段
        if (lockAcqEntry->acqInfo.lockMode == (uint16_t)SE_LOCK_MODE_S) {
            return SE_LOCK_ACQ_S_UPD_X;
        }
        return SE_LOCK_ACQ_I_UPD_X;
    } else {
        if (lockAcqEntry->acqInfo.lockMode == (uint16_t)SE_LOCK_MODE_S) {  // 不存在S->IX锁升级
            SE_LAST_ERROR(LK_NO_AVA_LOCK_UPDATE_CONFLICT, "trx:%" PRIu64 " update label lock but trx%" PRIu64 " get S",
                acqRunCtx->trx->base.trxId, lockAcqEntry->acqInfo.trxId);
            return SE_LOCK_ACQ_MAX;
        }
        acqRunCtx->lockAcqEntry = (SeLockAcqEntryT *)lockAcqEntry;  // 获取之前的锁的申请记录, 不要在check流程中修改字段
        return SE_LOCK_ACQ_IS_UPD_IX;
    }
}

bool SeIsLabelLocked(SeRunCtxT *seRunCtx, uint32_t labelId)
{
    DB_POINTER2(seRunCtx, seRunCtx->trx);
    SeLockIdentifierT labelLock = SeLockIdForLabel(seRunCtx->instanceId, labelId);
    SeLockAcqEntryT *lockAcqEntry =
        SeLockAcqGetEntryByAcqId(seRunCtx->lockAqcPool, ((TrxT *)seRunCtx->trx)->trx.pesTrx.holdLockAcqId);
    while (lockAcqEntry) {
        if (SeCompareLockId(&labelLock, &lockAcqEntry->acqInfo.lock->lockId)) {
            return (lockAcqEntry->acqInfo.lockMode == (uint16_t)SE_LOCK_MODE_X) ||
                   (lockAcqEntry->acqInfo.lockMode == (uint16_t)SE_LOCK_MODE_S);
        }
        lockAcqEntry = SeLockAcqGetNextSameTrxEntry(seRunCtx->lockAqcPool, lockAcqEntry);
    }
    return false;
}

// 按照 trx 顺序遍历, 检查是否存在相同的申请(事务+lock)
SeLockAcqTypeE SeLockMgrCheckIsSameOrCanUpdByTrxCoord(
    SeRunCtxT *seRunCtx, SeLockAcqRunCtxT *acqRunCtx, const SeLockMgrCheckSameOrUpdFun checkFun)
{
    DB_POINTER3(seRunCtx, acqRunCtx, checkFun);

    bool isCheckDone = false;
    SeLockAcqTypeE acqType;
    SeLockAcqEntryT *lockAcqEntry =
        SeLockAcqGetEntryByAcqId(seRunCtx->lockAqcPool, acqRunCtx->trx->trx.pesTrx.holdLockAcqId);
    while (lockAcqEntry) {
        if (SeCompareLockId(acqRunCtx->lockId, &lockAcqEntry->acqInfo.lock->lockId)) {
            acqType = checkFun(acqRunCtx, lockAcqEntry, &isCheckDone);
            if (isCheckDone) {
                return acqType;
            }
        }
        lockAcqEntry = SeLockAcqGetNextSameTrxEntry(seRunCtx->lockAqcPool, lockAcqEntry);
    }
    return SE_LOCK_ACQ_NEW_LOCK;
}

// 按照 lock 的顺序遍历, 检查是否存在相同的申请(事务+lock)
SeLockAcqTypeE SeLockMgrCheckIsSameOrCanUpdByLockCoord(
    SeRunCtxT *seRunCtx, SeLockAcqRunCtxT *acqRunCtx, const SeLockMgrCheckSameOrUpdFun checkFun)
{
    DB_POINTER3(seRunCtx, acqRunCtx, checkFun);

    SeLockT *lock = acqRunCtx->lock;
    TrxT *trx = acqRunCtx->trx;
    bool isCheckDone = false;
    SeLockAcqTypeE acqType;
    SeLockAcqEntryT *lockAcqEntry = SeLockAcqGetEntryByAcqId(seRunCtx->lockAqcPool, lock->holderAcqId);
    while (lockAcqEntry) {
        if (lockAcqEntry->acqInfo.trxId == trx->base.trxId) {
            acqType = checkFun(acqRunCtx, lockAcqEntry, &isCheckDone);
            if (isCheckDone) {
                return acqType;
            }
        }
        lockAcqEntry = SeLockAcqGetNextSameLockEntry(seRunCtx->lockAqcPool, lockAcqEntry);
    }
    return SE_LOCK_ACQ_NEW_LOCK;
}

SeLockAcqTypeE SeLockMgrCheckIsSameOrCanUpd(SeRunCtxT *seRunCtx, SeLockAcqRunCtxT *acqRunCtx)
{
    DB_POINTER2(seRunCtx, acqRunCtx);
    // 本函数内所有流程只应该做check, 不应该修改任何状态
    if (acqRunCtx->trx->trx.pesTrx.holdLockNum == 0u || acqRunCtx->lock->holdNum == 0u) {
        return SE_LOCK_ACQ_NEW_LOCK;
    }
    SeLockMgrCheckSameOrUpdFun checkFun;
    if (acqRunCtx->lockId->lockType == (uint8_t)SE_LOCK_TYPE_LABEL_LOCK) {
        checkFun = SeLockMgrLabelLockCheckSameTrxOrUpd;
    } else {
        checkFun = SeLockMgrTupleLockCheckSameTrxOrUpd;
    }
    if (acqRunCtx->trx->trx.pesTrx.holdLockNum < acqRunCtx->lock->holdNum) {
        // 按照 trx 顺序遍历, 检查是否存在相同的申请(事务+lock)
        return SeLockMgrCheckIsSameOrCanUpdByTrxCoord(seRunCtx, acqRunCtx, checkFun);
    } else {
        // 按照 lock 的顺序遍历, 检查是否存在相同的申请(事务+lock)
        return SeLockMgrCheckIsSameOrCanUpdByLockCoord(seRunCtx, acqRunCtx, checkFun);
    }
}

void SeLockMgrTryGrantLock(const SeRunCtxT *seRunCtx, SeLockAcqRunCtxT *acqRunCtx)
{
    DB_POINTER2(seRunCtx, acqRunCtx);

    DB_ASSERT(acqRunCtx->lockAcqEntry->acqInfo.specialAcq != (uint16_t)SE_LOCK_ACQ_S_UPD_X);  // 升级流程不应该走该函数
    SeLockT *lock = acqRunCtx->lock;
    SeLockModeE lockMode = acqRunCtx->lockAcqEntry->acqInfo.lockMode;
    SeLockStateE newLockState = G_SE_LOCK_ACQUIRE_FSM[lockMode][lock->grantLockState];
    if (SECUREC_UNLIKELY(newLockState == SE_LOCK_STATE_MAX)) {
        acqRunCtx->isGetLock = false;
        return;
    }

    bool isStarved = SeLockMgrLockCheckIsStarved(acqRunCtx, newLockState);
    if (SECUREC_UNLIKELY(isStarved)) {
        acqRunCtx->isGetLock = false;
        return;
    }

    /* 可以申请到锁, 开始保存lock和trx上面关于锁的持有信息 */
    SeLockMgrHoldAcqInfoSave(seRunCtx, acqRunCtx->trx, lock, acqRunCtx->lockAcqEntry);
    lock->grantedSum++;
    lock->grantedCount[lockMode]++;
    lock->grantLockState = newLockState;
    acqRunCtx->isGetLock = true;
}

StatusInter SeLockMgrHandleAcq2XMode(SeLockAcqRunCtxT *acqRunCtx, const SeLockUpdXFunc udpXFunc)
{
    DB_POINTER2(acqRunCtx, udpXFunc);
    DB_ASSERT(acqRunCtx->lockAcqEntry != NULL);
    DB_ASSERT(acqRunCtx->lock->holdNum > 0u);
    DB_ASSERT(acqRunCtx->lock->waitForUpdX == 0);  // 走到这个流程，当前应该只有自己需要升级
    DB_ASSERT(acqRunCtx->lock->shareUpdX == 0);    // 不应该有人已经升级成功

    if (acqRunCtx->lock->holdNum == 1u) {  // 如果当前只有自身持有S/I锁，并且试图升级，则直接升级
        DB_ASSERT(acqRunCtx->lock->waitForUpdX == 0);
        udpXFunc(acqRunCtx);
        return STATUS_OK_INTER;
    }

    // 不止一个事务持有该锁的S/I锁
    return acqRunCtx->isTryOnce ? LK_NO_AVA_LOCK_UPDATE_CONFLICT :
                                  STATUS_OK_INTER;  // 如果是尝试获取锁, 失败时, 应该返回错误
}

StatusInter SeLockMgrLockHandleLockAcqState(
    SeLockAcqTypeE lockAcqState, SeLockAcqRunCtxT *acqRunCtx, const SeRunCtxT *seRunCtx, SeLockModeE lockMode)
{
    DB_POINTER2(acqRunCtx, seRunCtx);

    switch (lockAcqState) {
        case SE_LOCK_ACQ_NEW_LOCK:
            acqRunCtx->lockAcqEntry = SeLockAcqAllocEntry(seRunCtx);
            if (SECUREC_UNLIKELY(acqRunCtx->lockAcqEntry == NULL)) {
                SE_LAST_ERROR(LK_NO_AVA_ACQ_POOL_NO_RESOURCE, "lock acq pool out of resource.");
                return LK_NO_AVA_ACQ_POOL_NO_RESOURCE;
            }
            acqRunCtx->lockAcqEntry->acqInfo.trxId = acqRunCtx->trx->base.trxId;
            acqRunCtx->lockAcqEntry->acqInfo.trxSlot = acqRunCtx->trx->base.trxSlot;
            acqRunCtx->lockAcqEntry->acqInfo.bucket = acqRunCtx->bucket;
            acqRunCtx->lockAcqEntry->acqInfo.lock = acqRunCtx->lock;
            acqRunCtx->lockAcqEntry->acqInfo.lockMode = (uint16_t)lockMode;
            acqRunCtx->lockAcqEntry->acqInfo.specialAcq = SE_LOCK_ACQ_NEW_LOCK;

            SeLockMgrTryGrantLock(seRunCtx, acqRunCtx);
            if (SECUREC_UNLIKELY((!acqRunCtx->isGetLock) && acqRunCtx->isTryOnce)) {
                SeLockAcqReleaseEntry(seRunCtx, acqRunCtx->lockAcqEntry);  // 新申请的 lockAcqEntry 也要释放
                return LK_NO_AVA_LOCK_CONFLICT;  // 如果是尝试获取锁, 失败时, 应该返回错误
            }
            acqRunCtx->isNewAcqEntry = true;
            return STATUS_OK_INTER;

        case SE_LOCK_ACQ_IS_EXIST:
            acqRunCtx->isGetLock = true;
            return STATUS_OK_INTER;
        case SE_LOCK_ACQ_MAX:
            return LK_NO_AVA_LOCK_CONFLICT;
        case SE_LOCK_ACQ_S_UPD_X:
            return SeLockMgrHandleAcq2XMode(acqRunCtx, SeLockMgrUpdS2XMode);
        case SE_LOCK_ACQ_I_UPD_X:
            return SeLockMgrHandleAcq2XMode(acqRunCtx, SeLockMgrUpdI2XMode);
        case SE_LOCK_ACQ_IS_UPD_IX:
            // 表锁从IS升级到IX
            DB_ASSERT(acqRunCtx->lockAcqEntry != NULL);
            SeLockMgrUpdIS2IX(acqRunCtx);
            return STATUS_OK_INTER;
        default:
            DB_ASSERT(false);
            SE_LAST_ERROR(INTERNAL_ERROR_INTER, "inv lockAcqState %" PRId32 ".", (int32_t)lockAcqState);
            return INTERNAL_ERROR_INTER;
    }
}

/* 由于情况比较复杂, 而且当前label/page类型的锁, 目前没有申请S锁场景, 代码可能不考虑部分S锁场景;
 * 场景1: 事务持有相同的锁, 直接返回OK [支持]
 * 场景2: 事务持有排他的锁, 直接返回OK [支持]
 * 场景3: 对于tuple类型的锁, 实际只有S或者X模式:
 *      当只有一个持有者, 持有S 可以升级为 X; [支持]
 *      如果有多个持有者, 需要排队等待升级;   [支持]
 *              a. 在读已提交隔离级别: 在其他共享持有者都释放后, 待申请X者, 逐个升级
 *              b. 可重复读隔离级别: 在其他共享持有者都释放后, 只能有一个持有共享模式的事务可以升级为X,
 * 后等待申请X的共享事务, 返回失败 场景4: 对于label/page类型的锁, 可能有IX, IS, S, X模式. 情况相对复杂: [不支持升级]
 *          1. lock只有一个持有者, 持有任意锁, 持有者申请X, 可以直接升级;
 *          2. lock拥有多个持有者, lock.grantLockState 可能为(IS|IX|S|AI|ISS), 如果持有者想申请X:
 *              a. 在读已提交隔离级别: 在其他共享持有者都释放后, 待申请X者, 逐个升级
 *              b. 可重复读隔离级别: 在其他共享持有者都释放后, 只能有一个持有共享模式的事务可以升级为X,
 * 后等待申请X的共享事务, 返回失败, 进行回滚
 *          3. 如果已经持有IS|IX|S中任意模式, 且申请一个非X模式 :
 *              a. lock.grantLockState 为 IS, 当前申请IX : 可以兼容, 插入队列; -> 一个事务同时拥有一个锁的 IS和IX模式
 *              b. lock.grantLockState 为 IS, 当前申请S :  对应事务可以直接升级为S(修改队列), 也可以直接插入队列
 *              c. lock.grantLockState 为 IX, 当前申请IS : 可以兼容, 插入队列; -> 一个事务同时拥有一个锁的 IS和IX模式
 *              d. lock.grantLockState 为 IX, 无法再兼容S; 如果本事务已经持有 IX, 应该直接报错, 这个场景是异常场景
 *              e. lock.grantLockState 为 AI, 无法再兼容S; 如果本事务已经持有 IX, 应该直接报错, 这个场景是异常场景
 *              f. lock.grantLockState 为 ISS, 如果本事务已经持有 IX, 应该直接报错, 这个场景是异常场景
 */
StatusInter SeLockMgrLockTryAcquireInner(SeRunCtxT *seRunCtx, SeLockAcqRunCtxT *acqRunCtx, SeLockModeE lockMode)
{
    DB_POINTER2(acqRunCtx->bucket, seRunCtx->lockTable);
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    StatusInter ret = SeLockTableLookup(
        acqRunCtx->bucket, seRunCtx->lockTable, acqRunCtx->lockId, &acqRunCtx->lock, seIns->dbInstance);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        if (SECUREC_LIKELY(ret == LK_NO_AVA_LOCK_POOL_NO_RESOURCE)) {
            DB_LOG_INFO("LockMgr find lock, try lock escalate, trxId:%" PRIu64, acqRunCtx->trx->base.trxId);
        } else {
            SE_ERROR(ret, "lockMgr find lock, trxId:%" PRIu64, acqRunCtx->trx->base.trxId);
        }
        return ret;
    }

    if (SECUREC_LIKELY(SeLockIsNewAllocLock(acqRunCtx->lock))) {
        acqRunCtx->lock->waitNotifyList.head = SE_LOCK_NOTIFY_INVALID_ID;
        acqRunCtx->lock->waitNotifyList.tail = SE_LOCK_NOTIFY_INVALID_ID;
        acqRunCtx->lock->holderAcqId = SE_LOCK_ACQ_INVALID_ID;
    }
    SeLockAcqTypeE lockAcqState = SeLockMgrCheckIsSameOrCanUpd(seRunCtx, acqRunCtx);
    acqRunCtx->lockAcqState = lockAcqState;  // 保存在 acqRunCtx(申请运行ctx) 中
    acqRunCtx->trx->trx.pesTrx.lockAcqInfo.lastAcqType = (uint8_t)lockAcqState;
    if (!acqRunCtx->trx->trx.pesTrx.lockAcqInfo.isTrySameLock) {
        acqRunCtx->trx->trx.pesTrx.lockAcqInfo.firstAcqType = (uint8_t)lockAcqState;
    }

    ret = SeLockMgrLockHandleLockAcqState(lockAcqState, acqRunCtx, seRunCtx, lockMode);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER && !acqRunCtx->isTryOnce)) {
        // try的情况外面会进行重试，不打印日志
        SE_ERROR(ret, "lockMgr handle lock acquire state : %" PRId32 ", trxId:%" PRIu64 ", lockAcqState:%" PRIu32,
            (int32_t)ret, acqRunCtx->trx->base.trxId, (uint32_t)lockAcqState);
    }
    return ret;
}

inline static void SeLockMgrMarkGetLabelXLock(TrxT *trx, const SeLockIdentifierT *lockId)
{
    TrxMarkGetLabelXLock(trx, lockId->lockIdField2);
}

StatusInter SeLockMgrCheckNeedLockEscalation(
    StatusInter status, SeLockAcqRunCtxT *acqRunCtx, SeRunCtxT *seRunCtx, bool *isEscalated)
{
    DB_POINTER3(acqRunCtx, seRunCtx, isEscalated);
    if (status != LK_NO_AVA_LOCK_POOL_NO_RESOURCE || acqRunCtx->lockId->lockType == (uint8_t)SE_LOCK_TYPE_LABEL_LOCK) {
        return status;
    }

    DB_LOG_INFO("trx(%" PRIu64 ") label(%" PRIu32 ") Type(%" PRIu8 ") try lock escalate", acqRunCtx->trx->base.trxId,
        acqRunCtx->lockId->lockIdField2, acqRunCtx->lockId->lockType);
    acqRunCtx->trx->trx.pesTrx.lockAcqInfo.isTupleLockEscalateToLabel = true;
    SeLockFreeLockResForAcquireFailed(seRunCtx, acqRunCtx);
    SeLockTableReleaseBucketLock(acqRunCtx->bucket);

    StatusInter ret = SeLockMgrLockEscalation(seRunCtx, acqRunCtx);
    if (ret == STATUS_OK_INTER) {
        *isEscalated = true;
    } else {
        SE_ERROR(ret, "try lock escalate, trxId:%" PRIu64 ", ret:%" PRIu32 ", status:%" PRIu32,
            acqRunCtx->trx->base.trxId, ret, status);
    }
    return ret;
}

inline static void SeLockMgrResetLockAcqInfo(SeLockAcqRunCtxT *acqRunCtx)  // 清理runctx的内容
{
    TrxT *trx = acqRunCtx->trx;
    trx->trx.pesTrx.lockAcqInfo.isNewAllocLock = false;
    trx->trx.pesTrx.lockAcqInfo.isDeadLockVictim = false;
    trx->trx.pesTrx.lockAcqInfo.isTupleLockEscalateToLabel = false;
    trx->trx.pesTrx.lockAcqInfo.isLockConfit = false;
    trx->trx.pesTrx.lockAcqInfo.isTrySameLock = false;
}

static void SeLockMgrInitLockAcqInfo(
    SeLockAcqRunCtxT *acqRunCtx, const SeLockIdentifierT *lockId, SeLockModeE lockMode, bool isTryOnce)
{
    DB_POINTER3(acqRunCtx, lockId, acqRunCtx->trx);

    SeLockMgrResetLockAcqInfo(acqRunCtx);
    TrxT *trx = acqRunCtx->trx;
    trx->trx.pesTrx.lockAcqInfo.lockType = lockId->lockType;
    trx->trx.pesTrx.lockAcqInfo.lastLockMode = (uint8_t)acqRunCtx->lockMode;
    if (!trx->trx.pesTrx.lockAcqInfo.isGetLock && isTryOnce &&
        SeCompareLockId(lockId, (SeLockIdentifierT *)&trx->trx.pesTrx.lockAcqInfo.lastAcqId)) {
        // 判断是否是同一批try lock里的多次操作，如果上层对同一把锁反复try
        // lock（因为一直加不上），后续统计折叠为一次加锁操作 注意，此处可能会误检测该场景：上层真的是两次独立的try
        // lock，恰好是统一把锁，且恰好第一次失败
        trx->trx.pesTrx.lockAcqInfo.isTrySameLock = true;
    }
    if (!trx->trx.pesTrx.lockAcqInfo.isTrySameLock) {
        trx->trx.pesTrx.lockAcqInfo.firstLockMode = (uint8_t)acqRunCtx->lockMode;
        trx->trx.pesTrx.lockAcqInfo.firstLockType = lockId->lockType;
    }
    trx->trx.pesTrx.lockAcqInfo.isGetLock = false;  // 重置
    trx->trx.pesTrx.lockAcqInfo.lastAcqId.lockIdField1 = lockId->lockIdField1;
    trx->trx.pesTrx.lockAcqInfo.lastAcqId.lockIdField2 = lockId->lockIdField2;
    trx->trx.pesTrx.lockAcqInfo.lastAcqId.lockIdField3 = lockId->lockIdField3;
    trx->trx.pesTrx.lockAcqInfo.lastAcqId.lockIdField4 = lockId->lockIdField4;
    trx->trx.pesTrx.lockAcqInfo.lastAcqId.lockType = lockId->lockType;
}

static void SeLockMgrLockAcquireInnerInit(const SeRunCtxT *seRunCtx, SeLockIdentifierT *lockId, SeLockModeE lockMode,
    bool isTryOnce, SeLockAcqRunCtxT *acqRunCtx)
{
    acqRunCtx->lockId = lockId;
    acqRunCtx->lockMode = lockMode;
    acqRunCtx->isTryOnce = isTryOnce;
    acqRunCtx->isGetLock = false;
    acqRunCtx->isNewAcqEntry = false;
    acqRunCtx->trx = seRunCtx->trx;
    acqRunCtx->maxLockShareCnt = ((SeInstanceT *)seRunCtx->seIns)->seConfig.maxLockShareCnt;
}

StatusInter SeLockMgrLockAcquireInner(
    SeRunCtxT *seRunCtx, const SeLockIdentifierT *acqLockId, SeLockModeE lockMode, bool isTryOnce, bool *isEscalated)
{
    DB_POINTER3(seRunCtx, acqLockId, isEscalated);

    // 如果触发行锁升级表锁，lockId会被修改成表锁形式，函数内部复制一份避免影响定位
    SeLockIdentifierT lockId = *acqLockId;
    SeLockAcqRunCtxT acqRunCtx = {0};
    SeLockMgrLockAcquireInnerInit(seRunCtx, &lockId, lockMode, isTryOnce, &acqRunCtx);
    if (SECUREC_UNLIKELY(acqRunCtx.trx == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "trx is null");
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    SeLockMgrInitLockAcqInfo(&acqRunCtx, acqRunCtx.lockId, lockMode, isTryOnce);
    if (acqRunCtx.trx->trx.pesTrx.holdLockNum == 0u) {
        acqRunCtx.trx->trx.pesTrx.holdLockAcqId = SE_LOCK_ACQ_INVALID_ID;
        acqRunCtx.trx->trx.pesTrx.lockNotifyId = SE_LOCK_NOTIFY_INVALID_ID;
    }

    if (SeLockMgrIsGetLabelXLock((TrxT *)seRunCtx->trx, acqRunCtx.lockId)) {
        // 获取行锁前判断是否已获取了表锁
        acqRunCtx.trx->trx.pesTrx.lockAcqInfo.lastAcqType = SE_LOCK_ACQ_IS_EXIST;
        return STATUS_OK_INTER;
    }

    SeLockTableAddBucketLock(seRunCtx->lockTable, acqRunCtx.lockId, &acqRunCtx.bucket);

    StatusInter ret = SeLockMgrLockTryAcquireInner(seRunCtx, &acqRunCtx, lockMode);
    if (SECUREC_LIKELY(acqRunCtx.isGetLock)) {
        SeLockTableReleaseBucketLock(acqRunCtx.bucket);
        return STATUS_OK_INTER;
    }

    // 检测是否存在行锁资源不足的情况，尝试进行锁提升，获取表的排他锁
    // 如果触发行锁升级表锁，acqRunCtx的lockId会变成对应表锁形式
    ret = SeLockMgrCheckNeedLockEscalation(ret, &acqRunCtx, seRunCtx, isEscalated);
    if (acqRunCtx.isGetLock) {
        SeLockTableReleaseBucketLock(acqRunCtx.bucket);
        return STATUS_OK_INTER;
    }

    acqRunCtx.trx->trx.pesTrx.lockAcqInfo.isLockConfit = true;
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SeLockFreeLockResForAcquireFailed(seRunCtx, &acqRunCtx);
        SeLockTableReleaseBucketLock(acqRunCtx.bucket);
        return ret;
    }

    // 添加到等待队列中, 等待被唤醒
    ret = SeLockMgrWaitListAddNode(seRunCtx, &acqRunCtx);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "add wait info");
        SeLockFreeLockResForAcquireFailed(seRunCtx, &acqRunCtx);
        SeLockTableReleaseBucketLock(acqRunCtx.bucket);
        return ret;
    }
    // 先释放锁表的bucket锁, 再进入 lock的等待
    SeLockTableReleaseBucketLock(acqRunCtx.bucket);
    return SeLockMgrWaitForWakeUp(seRunCtx, &acqRunCtx);
}

StatusInter SeLockMgrLockEscalation(SeRunCtxT *seRunCtx, SeLockAcqRunCtxT *acqRunCtx)
{
    DB_POINTER2(seRunCtx, acqRunCtx);

    // 变为申请表X锁
    SeLockIdEscalation2Label(acqRunCtx->lockId);

    acqRunCtx->lockMode = SE_LOCK_MODE_X;
    acqRunCtx->isGetLock = false;
    acqRunCtx->lock = NULL;
    acqRunCtx->trx = seRunCtx->trx;
    if (SECUREC_UNLIKELY(acqRunCtx->trx == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "trx is NULL");
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    acqRunCtx->trx->trx.pesTrx.lockAcqInfo.lockType = SE_LOCK_TYPE_LABEL_LOCK;
    acqRunCtx->trx->trx.pesTrx.lockAcqInfo.lastLockMode = SE_LOCK_MODE_X;
    acqRunCtx->trx->trx.pesTrx.lockAcqInfo.isNewAllocLock = false;
    // 不是在用try申请同一把锁
    if (!acqRunCtx->trx->trx.pesTrx.lockAcqInfo.isTrySameLock) {
        acqRunCtx->trx->trx.pesTrx.lockAcqInfo.firstLockType = SE_LOCK_TYPE_LABEL_LOCK;
        acqRunCtx->trx->trx.pesTrx.lockAcqInfo.firstLockMode = SE_LOCK_MODE_X;
    }
    if (acqRunCtx->trx->trx.pesTrx.holdLockNum == 0u) {
        acqRunCtx->trx->trx.pesTrx.holdLockAcqId = SE_LOCK_ACQ_INVALID_ID;
        acqRunCtx->trx->trx.pesTrx.lockNotifyId = SE_LOCK_NOTIFY_INVALID_ID;
    }

    SeLockTableAddBucketLock(seRunCtx->lockTable, acqRunCtx->lockId, &acqRunCtx->bucket);

    return SeLockMgrLockTryAcquireInner(seRunCtx, acqRunCtx, SE_LOCK_MODE_X);
}

void SeLockMgrExecuteWakeUp(const SeRunCtxT *seRunCtx, SeLockAcqRunCtxT *acqRunCtx)
{
    DB_POINTER2(seRunCtx, acqRunCtx);
    SeLockMgrWaitListRemoveNode(seRunCtx, acqRunCtx);
    // 唤醒对应的执行体/事务
    acqRunCtx->lockNotifyEntry->notifyNode.isGrandWakeUp = true;
    Status status = DbSemPost(&acqRunCtx->lockNotifyEntry->notifyNode.sem);
    if (status != GMERR_OK) {
        DB_LOG_ERROR(status, "sem post unsucc.");
    }
}

void SeLockMgrWakeUpWaiter(SeRunCtxT *seRunCtx, SeLockT *lock)
{
    DB_POINTER2(seRunCtx, lock);
    if (SECUREC_LIKELY(lock->waitNum == 0u)) {  // 该事务锁 没有等待者, 通过设置 lockType 归还给锁池
        lock->lockId.lockType = SE_LOCK_TYPE_NO_USED_LOCK;
        return;
    }

    SeLockAcqRunCtxT acqRunCtx = {0};
    acqRunCtx.lock = lock;
    acqRunCtx.lockNotifyEntry = SeLockNotifyGetEntryById(seRunCtx, lock->waitNotifyList.head);
    while (acqRunCtx.lockNotifyEntry) {
        // 唤醒流程, 等待队列 可能会被修改. 后续访问 next 通过临时变量 nextNotifyId
        uint32_t nextNotifyId = acqRunCtx.lockNotifyEntry->nextNotifyId;
        // 获取等待者的请求内容
        acqRunCtx.lockAcqEntry =
            SeLockAcqGetEntryByAcqId(seRunCtx->lockAqcPool, acqRunCtx.lockNotifyEntry->notifyNode.lockAcqId);
        DB_ASSERT(acqRunCtx.lockAcqEntry != NULL);
        // 虽然涉及更新其他线程的事务信息, 但是由于流程中有 lock bucket的锁, 可以控制并发,
        // 所以下面事务的字段更新是安全的
        DbRWLatchR(&((TrxMgrT *)seRunCtx->trxMgr)->latch);
        acqRunCtx.trx = TrxMgrGetActiveTrxByTrxId(seRunCtx->trxMgr, acqRunCtx.lockAcqEntry->acqInfo.trxId, false,
            acqRunCtx.lockAcqEntry->acqInfo.trxSlot, true);
        DbRWUnlatchR(&((TrxMgrT *)seRunCtx->trxMgr)->latch);
        if (acqRunCtx.trx == NULL) {
            SE_ERROR(INTERNAL_ERROR_INTER, "unable to find active trx %" PRIu64 " to wake up.",
                acqRunCtx.lockAcqEntry->acqInfo.trxId);
            DB_ASSERT(false);  // 由于这个 请求是在 lock的 链表上, 当前处于 lock bucket 下, 不可能为空.
            // release版本 可能没有 assert, 该场景也继续往下处理
            acqRunCtx.lockNotifyEntry = SeLockNotifyGetEntryById(seRunCtx, nextNotifyId);
            continue;
        }
        SeLockMgrTryGrantLock(seRunCtx, &acqRunCtx);
        if (acqRunCtx.isGetLock) {  // 获取成功, 唤醒对应的执行体/事务, 修改 等待队列
            SeLockMgrExecuteWakeUp(seRunCtx, &acqRunCtx);
        }

        if (lock->grantLockState == SE_LOCK_STATE_X) {  // 如果已经获取到排他锁, 后续等待队列的也没有必要遍历了.
            return;
        }
        // 继续遍历唤醒 其他等待者
        acqRunCtx.lockNotifyEntry = SeLockNotifyGetEntryById(seRunCtx, nextNotifyId);
    }

    DB_ASSERT(lock->holdNum > 0u);
}

void SeLockMgrLockReleaseCheckUpd(SeRunCtxT *seRunCtx, SeLockT *lock)
{
    DB_POINTER2(seRunCtx, lock);

    SeLockAcqRunCtxT acqRunCtx = {0};
    acqRunCtx.lock = lock;
    // 剩下的持有者都是等待升级时, 找一个升级
    acqRunCtx.lockNotifyEntry = SeLockNotifyGetEntryById(seRunCtx, lock->waitNotifyList.head);
    while (acqRunCtx.lockNotifyEntry) {
        acqRunCtx.lockAcqEntry =
            SeLockAcqGetEntryByAcqId(seRunCtx->lockAqcPool, acqRunCtx.lockNotifyEntry->notifyNode.lockAcqId);
        if (acqRunCtx.lockAcqEntry == NULL) {
            SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "null lockAcqEntry found.");
            DB_ASSERT(false);  // 不应该发生, debug下看护，release直接返回
            return;
        }
        // 优先处理锁升级场景
        if (acqRunCtx.lockAcqEntry->acqInfo.specialAcq == (uint16_t)SE_LOCK_ACQ_S_UPD_X) {
            SeLockMgrUpdS2XMode(&acqRunCtx);
        } else if (acqRunCtx.lockAcqEntry->acqInfo.specialAcq == (uint16_t)SE_LOCK_ACQ_I_UPD_X) {
            SeLockMgrUpdI2XMode(&acqRunCtx);
        } else {
            acqRunCtx.lockNotifyEntry = SeLockNotifyGetNextEntry(seRunCtx, acqRunCtx.lockNotifyEntry);
            continue;
        }

        // 唤醒对应的执行体/事务
        SeLockMgrExecuteWakeUp(seRunCtx, &acqRunCtx);
        return;
    }
    DB_ASSERT(false);  // 至少应该找到一个
    SE_ERROR(INTERNAL_ERROR_INTER, "null lockAcqEntry found.");
}

void SeLockMgrUpdateLockInfoAfterRelease(SeRunCtxT *seRunCtx, SeLockT *lock, SeLockAcquireT *lockAcqInfo)
{
    DB_POINTER3(seRunCtx, lock, lockAcqInfo);
    DB_ASSERT(lock->grantedSum > 0u);
    DB_ASSERT(lock->grantedCount[lockAcqInfo->lockMode] > 0u);

    lock->grantedSum--;
    lock->grantedCount[lockAcqInfo->lockMode]--;
    if (SECUREC_LIKELY(lock->grantedCount[lockAcqInfo->lockMode] == 0u)) {
        SeLockStateE newLockState = G_SE_LOCK_RELEASE_FSM[lockAcqInfo->lockMode][lock->grantLockState];
        if (SECUREC_LIKELY(newLockState != SE_LOCK_STATE_MAX)) {
            lock->grantLockState = newLockState;
        } else {
            /* 在锁升级场景, 某些事务升级成功, grantLockState 已经变为X; 如果当前事务的锁升级失败, 回滚
             * 释放锁时, 当前实际是个 S锁的待升级者, 不更新锁的授予状态(grantLockState) */
            bool sUpd = (lockAcqInfo->specialAcq == (uint16_t)SE_LOCK_ACQ_S_UPD_X &&
                         lockAcqInfo->lockMode == (uint16_t)SE_LOCK_MODE_S);
            bool iUpd = (lockAcqInfo->specialAcq == (uint16_t)SE_LOCK_ACQ_I_UPD_X &&
                         (lockAcqInfo->lockMode == (uint16_t)SE_LOCK_MODE_IS ||
                             lockAcqInfo->lockMode == (uint16_t)SE_LOCK_MODE_IX));
            DB_ASSERT(sUpd || iUpd);
        }
    }
    // 如果当前无待升级的事务, 都已经升级完了, 清空一下升级的个数
    if (SECUREC_LIKELY(lock->waitForUpdX == 0u)) {
        lock->shareUpdX = 0u;
    } else {
        // 如果持有者 <= 等待升级者, 意味着 等待升级者 能升级
        DB_ASSERT(lock->waitForUpdX == 1);  // 当前最多只有一个等待升级的事务，升级该事务
        if (SECUREC_LIKELY(lock->holdNum <= lock->waitForUpdX)) {
            SeLockMgrLockReleaseCheckUpd(seRunCtx, lock);
        }
    }

    if (SECUREC_LIKELY(lock->holdNum == 0u)) {
        SeLockMgrWakeUpWaiter(seRunCtx, lock);  // 如果当前锁已经没有持有者, 检查等待队列, 尝试唤醒
        // 尝试释放锁资源到资源池
        SeLockMgrFreeLockPoolEntryFromBucket(seRunCtx, lock, (SeLockBucketT *)lockAcqInfo->bucket);
    } else {
        DB_ASSERT(lock->grantLockState != SE_LOCK_STATE_NO_LOCK);
    }
}

void SeLockMgrLockReleaseTopOfTrx(SeRunCtxT *seRunCtx, TrxT *trx)
{
    DB_POINTER3(seRunCtx, trx, seRunCtx->lockAqcPool);
    SeLockAcqEntryT *lockAcqEntry = SeLockAcqGetEntryByAcqId(seRunCtx->lockAqcPool, trx->trx.pesTrx.holdLockAcqId);
    if (SECUREC_UNLIKELY(!lockAcqEntry)) {
        DB_ASSERT(false);  // 该事务应该持有锁, 不应该获取失败
        SE_ERROR(INTERNAL_ERROR_INTER, "null lockAcqEntry found.");
        return;  // release模式下退出
    }

    SeLockAcquireT lockAcqInfo = lockAcqEntry->acqInfo;  // 拷贝出来, 避免后面删除释放再访问

    SeLockBucketT *bucket = lockAcqInfo.bucket;
    SeLockTableAddBucketLockDirectly(bucket);
    SeLockT *lock = lockAcqInfo.lock;

    // 把锁的持有信息 从链表中移走
    SeLockMgrHoldAcqInfoRemoveByTrxHead(seRunCtx, trx, lock);

    // 释放锁持有信息并唤醒等待者
    SeLockMgrUpdateLockInfoAfterRelease(seRunCtx, lock, &lockAcqInfo);

    // 释放桶的latch
    SeLockTableReleaseBucketLock(bucket);
}

bool SeLockMgrIsTargetLabelTupleLock(const SeLockT *lock, const SeLockIdentifierT *lockId)
{
    DB_POINTER2(lock, lockId);
    return (lock->lockId.lockIdField1 == lockId->lockIdField1 && lock->lockId.lockIdField2 == lockId->lockIdField2 &&
            lock->lockId.lockType == (uint8_t)SE_LOCK_TYPE_TUPLE_LOCK);
}

void SeLockMgrAcquireTwoBucketLatch(SeLockBucketT *lastLockBucket, SeLockBucketT *lockBucket)
{
    DB_POINTER(lockBucket);
    if (lastLockBucket == NULL) {
        SeLockTableAddBucketLockDirectly(lockBucket);
        return;
    }

    // 顺序加latch，避免死锁
    if (lastLockBucket < lockBucket) {
        SeLockTableAddBucketLockDirectly(lastLockBucket);
        SeLockTableAddBucketLockDirectly(lockBucket);
    } else if (lockBucket < lastLockBucket) {
        SeLockTableAddBucketLockDirectly(lockBucket);
        SeLockTableAddBucketLockDirectly(lastLockBucket);
    } else {
        SeLockTableAddBucketLockDirectly(lockBucket);
    }
}

void SeLockMgrReleaseTwoBucketLatch(SeLockBucketT *lastLockBucket, SeLockBucketT *lockBucket)
{
    DB_POINTER(lockBucket);

    if (lastLockBucket == NULL) {
        SeLockTableReleaseBucketLock(lockBucket);
        return;
    }

    // 顺序解latch，避免死锁
    if (lastLockBucket < lockBucket) {
        SeLockTableReleaseBucketLock(lockBucket);
        SeLockTableReleaseBucketLock(lastLockBucket);
    } else if (lockBucket < lastLockBucket) {
        SeLockTableReleaseBucketLock(lastLockBucket);
        SeLockTableReleaseBucketLock(lockBucket);
    } else {
        SeLockTableReleaseBucketLock(lockBucket);
    }
}

/*
 * 在事务获取表的排他锁后，释放事务持有事务链表中表下的所有行锁
 */
void SeLockMgrLockReleaseTargetTupleLock(SeRunCtxT *seRunCtx, TrxT *trx, const SeLockIdentifierT *lockId)
{
    DB_POINTER3(seRunCtx, trx, lockId);
    // 最起码持有一把锁
    DB_ASSERT(trx->trx.pesTrx.holdLockNum != 0u);

    uint32_t newHoldLockAcqId = SE_LOCK_ACQ_INVALID_ID;
    SeLockAcqEntryT *lastLockAcqEntry = NULL;
    SeLockAcquireT lastLockAcqInfo;
    lastLockAcqInfo.bucket = NULL;  // 模拟初始值

    SeLockAcqEntryT *lockAcqEntry = SeLockAcqGetEntryByAcqId(seRunCtx->lockAqcPool, trx->trx.pesTrx.holdLockAcqId);
    DB_ASSERT(lockAcqEntry != NULL);  // 该事务应该持有锁, 不应该获取失败

    uint32_t releaseCnt = 0;
    while (lockAcqEntry) {
        SeLockAcqEntryT tmpLockAcqEntry = *lockAcqEntry;  // 拷贝出来, 避免后面删除释放再访问
        SeLockAcquireT *lockAcqInfo = &tmpLockAcqEntry.acqInfo;

        SeLockMgrAcquireTwoBucketLatch(lastLockAcqInfo.bucket, lockAcqInfo->bucket);

        // 找到对应表的行锁并释放
        if (SeLockMgrIsTargetLabelTupleLock(lockAcqInfo->lock, lockId)) {
            releaseCnt++;
            // 把锁的持有信息 从链表中移走, 假设当前节点是TrxHead
            SeLockMgrHoldAcqInfoRemoveByTrxHead(seRunCtx, trx, lockAcqInfo->lock);

            // 释放锁持有信息并唤醒等待者
            SeLockMgrUpdateLockInfoAfterRelease(seRunCtx, lockAcqInfo->lock, lockAcqInfo);
            SeLockMgrReleaseTwoBucketLatch(lastLockAcqInfo.bucket, lockAcqInfo->bucket);
            if (lastLockAcqEntry) {
                lastLockAcqEntry->trxCoordNext = trx->trx.pesTrx.holdLockAcqId;
            }
        } else {
            // 第一个lockId不一致的acq即为新的holdLockAcqId
            if (newHoldLockAcqId == SE_LOCK_ACQ_INVALID_ID) {
                newHoldLockAcqId = lockAcqEntry->acqId;
            }
            SeLockMgrReleaseTwoBucketLatch(lastLockAcqInfo.bucket, lockAcqInfo->bucket);
            trx->trx.pesTrx.holdLockAcqId = tmpLockAcqEntry.trxCoordNext;
            lastLockAcqEntry = lockAcqEntry;
            lastLockAcqInfo = lockAcqEntry->acqInfo;
        }

        lockAcqEntry = SeLockAcqGetEntryByAcqId(seRunCtx->lockAqcPool, tmpLockAcqEntry.trxCoordNext);
    }

    DB_ASSERT(newHoldLockAcqId != SE_LOCK_ACQ_INVALID_ID);
    DB_ASSERT(trx->trx.pesTrx.holdLockNum != 0u);
    trx->trx.pesTrx.holdLockAcqId = newHoldLockAcqId;

    DB_LOG_INFO("(SE-Lock) Release %" PRIu32 " locks of label(%" PRIu32 ").", releaseCnt, lockId->lockIdField2);
}

void SeLockMgrCheckIfEscalated(
    SeRunCtxT *seRunCtx, const SeLockIdentifierT *lockId, StatusInter status, bool isEscalate)
{
    DB_POINTER2(seRunCtx, lockId);
    if (status == STATUS_OK_INTER && isEscalate) {
        SeLockMgrLockReleaseTargetTupleLock(seRunCtx, seRunCtx->trx, lockId);
        // 事务中记录曾获取过表排他锁，后续不获取表下行锁
        SeLockMgrMarkGetLabelXLock((TrxT *)seRunCtx->trx, lockId);
    }
}
#endif /* FEATURE_SIMPLEREL */

// LOCK_TABLE_BUCKET_NUM 必须设置为质数(1021是1024以内的最大质数)
static uint32_t DbGetLockTableBucketNum(SeInstanceT *seIns)
{
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(seIns->dbInstance);
    DbCfgValueT cfgValue;
    Status ret = DbCfgGet(cfgHandle, DB_CFG_LOCK_TABLE_BUCKET_NUM, &cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Use default value for LOCK_TABLE_BUCKET_NUM");
        return SE_LOCK_TABLE_BUCKET_NUM_DEFAULT;
    }
    return (uint32_t)cfgValue.int32Val;
}

// 锁池+锁桶上锁的个数,其中bucketNum必须为质数
static inline uint32_t SeCalculatelockAcqPoolEntryNum(uint32_t connNum, uint32_t bucketNum)
{
    return connNum * SE_LOCK_POOL_CAL_COEFFICIENTS + bucketNum * SE_LOCK_BUCKET_CAL_COEFFICIENTS;
}

StatusInter SeLockMgrCalculateSizeByMaxConnNum(SeInstanceT *seIns, uint32_t maxConnNum, SeLockMgrParaT *para)
{
    DB_POINTER(para);
    if (maxConnNum == 0) {
        return CONFIG_ERROR_INTER;
    }
    // 锁桶太小冲突率可能较大，因此下面两种场景都申请相同数量的锁桶
    if (maxConnNum <= SE_LOCK_SPECIFICATION_IOT + MAX_BG_WORKER_NUM) {  // 最大连接数小于等于100的情况
        para->lockPoolPublicEnrtyNum = LOCK_POOL_PUBLIC_ENTRY_NUM_IOT;
        para->lockPoolReserveEnrtyNum = LOCK_POOL_RESERVE_ENTRY_NUM_IOT;
        para->lockTableBucketNum = DbGetLockTableBucketNum(seIns);
        para->lockAcqPoolEntryNum = SeCalculatelockAcqPoolEntryNum(SE_LOCK_SPECIFICATION_IOT, para->lockTableBucketNum);
        para->lockNotifyEntryNum = maxConnNum;
    } else if (maxConnNum <= SE_LOCK_SPECIFICATION_NORMAL + MAX_BG_WORKER_NUM) {
        para->lockPoolPublicEnrtyNum = LOCK_POOL_PUBLIC_ENTRY_NUM;
        para->lockPoolReserveEnrtyNum = LOCK_POOL_RESERVE_ENTRY_NUM;
        para->lockTableBucketNum = DbGetLockTableBucketNum(seIns);
        para->lockAcqPoolEntryNum =
            SeCalculatelockAcqPoolEntryNum(SE_LOCK_SPECIFICATION_NORMAL, para->lockTableBucketNum);
        para->lockNotifyEntryNum = maxConnNum;
    } else {
        return CONFIG_ERROR_INTER;
    }
    return STATUS_OK_INTER;
}

inline static SeLockTableT *LockMgrGetLockTable(SeLockMgrT *lockMgr)
{
    return (SeLockTableT *)(void *)((uintptr_t)lockMgr + lockMgr->lockTableOffset);
}

inline static SeLockAcqPoolT *LockMgrGetLockAcqPool(SeLockMgrT *lockMgr)
{
    return (SeLockAcqPoolT *)(void *)((uintptr_t)lockMgr + lockMgr->lockAcqPoolOffset);
}

inline static SeLockNotifyPoolT *LockMgrGetLockNotifyPool(SeLockMgrT *lockMgr)
{
    return (SeLockNotifyPoolT *)(void *)((uintptr_t)lockMgr + lockMgr->lockNotifyPoolOffset);
}

void SeLockMgrCreatePhase1(SeInstanceT *seIns)
{
    DB_POINTER(seIns);

    DbSpinInit(&seIns->lockMgrInitLatch);
    seIns->isLockMgrInited = false;
}

StatusInter SeLockMgrCreatePhase2(SeInstanceT *seIns)
{
    DB_POINTER(seIns);

    DbSpinLock(&seIns->lockMgrInitLatch);
    // 通过COMPILER_BARRIER要求重新加载seIns->isLockMgrInited
    COMPILER_BARRIER;
    if (seIns->isLockMgrInited) {
        DbSpinUnlock(&seIns->lockMgrInitLatch);
        return STATUS_OK_INTER;
    }

    // 因为有信号量，所以需要指针对齐
    SeLockMgrParaT para = {0};
    StatusInter ret = SeLockMgrCalculateSizeByMaxConnNum(seIns, (uint32_t)seIns->seConfig.maxTrxNum, &para);
    if (ret != STATUS_OK_INTER) {
        DbSpinUnlock(&seIns->lockMgrInitLatch);
        SE_LAST_ERROR(ret, "seLockMgr para inv, maxTrxNum %" PRIu16 ".", seIns->seConfig.maxTrxNum);
        return ret;
    }

    uint32_t lockTableSize = (uint32_t)(sizeof(SeLockBucketT) * para.lockTableBucketNum + sizeof(SeLockTableT));
    uint32_t lockAcqPoolSize = (uint32_t)(sizeof(SeLockAcqEntryT) * para.lockAcqPoolEntryNum + sizeof(SeLockAcqPoolT));
    uint32_t lockNotifyPoolSize =
        (uint32_t)(sizeof(SeLockNotifyEntryT) * para.lockNotifyEntryNum + sizeof(SeLockNotifyPoolT));
    uint32_t allocSize = (uint32_t)sizeof(SeLockMgrT) + lockTableSize + lockAcqPoolSize + lockNotifyPoolSize;
    // 从大页共享内存MemCtx中申请共享内存承载事务锁管理器, 依赖对应的memCtx在进程退出时销毁
    SeLockMgrT *lockMgr = SeShmAllocAlign(DbGetShmemCtxById(DB_HUGETLB_SHMCTX_ID, seIns->instanceId), allocSize,
        &seIns->lockMgrShm, (uint32_t)sizeof(SeLockMgrT *));
    if (lockMgr == NULL) {
        DbSpinUnlock(&seIns->lockMgrInitLatch);
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "seLockMgr alloc memory");
        return OUT_OF_MEMORY_INTER;
    }

    (void)memset_s(lockMgr, allocSize, 0, allocSize);
    lockMgr->lockTableOffset = (uint32_t)sizeof(SeLockMgrT);
    lockMgr->lockAcqPoolOffset = (uint32_t)sizeof(SeLockMgrT) + lockTableSize;
    lockMgr->lockNotifyPoolOffset = (uint32_t)sizeof(SeLockMgrT) + lockTableSize + lockAcqPoolSize;

    SeLockTableInit(LockMgrGetLockTable(lockMgr), &para);
    SeLockAcqInitPool(LockMgrGetLockAcqPool(lockMgr), &para);
    ret = SeLockNotifyInitPool(LockMgrGetLockNotifyPool(lockMgr), &para);
    if (ret != STATUS_OK_INTER) {
        DbSpinUnlock(&seIns->lockMgrInitLatch);
        SE_ERROR(ret, "seLockMgr init");
        DbShmemCtxFree((DbMemCtxT *)DbGetShmemCtxById(DB_HUGETLB_SHMCTX_ID, seIns->instanceId), seIns->lockMgrShm);
        return ret;
    }

    // 显式加内存屏障，不依赖函数调用引入的隐式内存屏障
    COMPILER_BARRIER;
    seIns->isLockMgrInited = true;
    DbSpinUnlock(&seIns->lockMgrInitLatch);

    return ret;
}

StatusInter SeLockMgrDestroyInner(SeInstanceT *seIns)
{
    DB_POINTER(seIns);

    DbSpinLock(&seIns->lockMgrInitLatch);
    SeLockMgrT *lockMgr = DbShmPtrToAddrAlign(seIns->lockMgrShm, (uint32_t)sizeof(SeLockMgrT *));
    if (lockMgr == NULL) {
        DbSpinUnlock(&seIns->lockMgrInitLatch);
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "seLockMgr destroy mem(segid: %" PRIu32 " offset: %" PRIu32 ")",
            seIns->lockMgrShm.segId, seIns->lockMgrShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    SeLockNotifyDestroyPool(LockMgrGetLockNotifyPool(lockMgr));
    DbMemCtxT *memCtx = (DbMemCtxT *)DbGetShmemCtxById(DB_HUGETLB_SHMCTX_ID, seIns->instanceId);
    if (memCtx == NULL) {
        DbSpinUnlock(&seIns->lockMgrInitLatch);
        SE_LAST_ERROR(
            UNEXPECTED_NULL_VALUE_INTER, "get huge Tlb shmCtx. seInstanceId: %" PRIu16 ".", seIns->instanceId);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    DbShmemCtxFree(memCtx, seIns->lockMgrShm);  // 释放pool申请的内存
    seIns->isLockMgrInited = false;
    seIns->lockMgrShm = DB_INVALID_SHMPTR;
    DbSpinUnlock(&seIns->lockMgrInitLatch);

    return STATUS_OK_INTER;
}

StatusInter SeLockMgrOpen(SeRunCtxT *seRunCtx, const SeInstanceT *seIns)
{
    DB_POINTER2(seRunCtx, seIns);

    SeLockMgrT *lockMgr = DbShmPtrToAddrAlign(seIns->lockMgrShm, (uint32_t)sizeof(SeLockMgrT *));
    if (lockMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "inv lockMgr in seLockMgr(%" PRIu32 ",%" PRIu32 ") init",
            seIns->lockMgrShm.segId, seIns->lockMgrShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    seRunCtx->lockTable = LockMgrGetLockTable(lockMgr);
    SeLockAcqPoolT *lockAcqPool = LockMgrGetLockAcqPool(lockMgr);
    seRunCtx->lockAqcPool = lockAcqPool;
    SeLockNotifyPoolT *lockNotifyPool = LockMgrGetLockNotifyPool(lockMgr);
    seRunCtx->lockNotifyPool = lockNotifyPool;
    seRunCtx->activeTrxIds = NULL;
    return STATUS_OK_INTER;
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
/* 这个函数当前只暴露给 更新顶点时, 遍历索引冲突链, 用于释放非命中的 tuple事务锁;
 * 减小非唯一索引等冲突链遍历, 对锁资源的消耗; */
void SeLockMgrLockReleaseLastNewLock(SeRunCtxT *seRunCtx)
{
    DB_POINTER2(seRunCtx, seRunCtx->trx);
    TrxT *trx = seRunCtx->trx;
    if (trx->trx.pesTrx.lockAcqInfo.isNewAllocLock) {
        DB_ASSERT(trx->trx.pesTrx.holdLockNum > 0u);
        SeLockAcqEntryT *lockAcqEntry = SeLockAcqGetEntryByAcqId(seRunCtx->lockAqcPool, trx->trx.pesTrx.holdLockAcqId);
        if (lockAcqEntry == NULL) {
            SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "lockAcqEntry");
            DB_ASSERT(false);
            return;  // 这个场景不应该出现
        }
        // 此时可能发生锁升级，新增的锁是表锁，此时no need释放这个newLock，在事务提交时统一清理即可
        if (lockAcqEntry->acqInfo.lock->lockId.lockType == (uint8_t)SE_LOCK_TYPE_LABEL_LOCK &&
            lockAcqEntry->acqInfo.lockMode == SE_LOCK_MODE_X) {
            // try release top of trx lock when already upgrade to label lock
            DB_LOG_INFO("lock upgrade release top trx lock");
            return;
        }

        DB_ASSERT(lockAcqEntry->acqInfo.lock->lockId.lockType == (uint8_t)SE_LOCK_TYPE_TUPLE_LOCK);
        SeLockMgrLockReleaseTopOfTrx(seRunCtx, trx);
        DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP,
            "ReleaseLock trx(%" PRIu64 ") db(%" PRIu32 ") label(%" PRIu32 ") page(%" PRIu32 ") slot(%" PRIu16
            ") lockMode(%" PRIu16 ").",
            ((TrxT *)seRunCtx->trx)->base.trxId, lockAcqEntry->acqInfo.lock->lockId.lockIdField1,
            lockAcqEntry->acqInfo.lock->lockId.lockIdField2, lockAcqEntry->acqInfo.lock->lockId.lockIdField3,
            lockAcqEntry->acqInfo.lock->lockId.lockIdField4, lockAcqEntry->acqInfo.lockMode);
        /* 为什么要判断 isNewAllocLock? 因为存储流程加事务锁, 不是每次都是新申请锁
         * 比如如果 某个tuple是本事务新插入的, 事务已经持有 X锁;
         * 在后续更新, 遍历索引冲突如果涉及这个 tuple, 实际上是没有额外加锁的; 所以如果没有命中,
         * 要释放上一次加锁的场景时, 不能误释放 */
        trx->trx.pesTrx.lockAcqInfo.isNewAllocLock = false;
    }
}

void SeLockMgrLockReleaseLastNewLabelAndTupleXLock(SeRunCtxT *seRunCtx)
{
    TrxT *trx = seRunCtx->trx;
    SeLockAcqEntryT *lockAcqEntry = NULL;
    do {
        lockAcqEntry = SeLockAcqGetEntryByAcqId(seRunCtx->lockAqcPool, trx->trx.pesTrx.holdLockAcqId);
        if (lockAcqEntry == NULL) {
            SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "lockAcqEntry in per");
            DB_ASSERT(false);
            return;  // 这个场景不应该出现
        }
        SeLockIdentifierT *lockId = &lockAcqEntry->acqInfo.lock->lockId;
        if (trx->trx.pesTrx.holdLockNum == 1) {
            // 当前使用场景下，上层必然加了事务表锁，最后一次解锁也必然是事务表锁
            DB_ASSERT(lockId->lockIdField2 > 0 && lockId->lockIdField3 == 0 && lockId->lockIdField4 == 0 &&
                      lockId->lockType == (uint8_t)SE_LOCK_TYPE_LABEL_LOCK);
        }
        SeLockMgrLockReleaseTopOfTrx(seRunCtx, trx);
        DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP,
            "ReleaseLock trx(%" PRIu64 ") db(%" PRIu32 ") label(%" PRIu32 ") page(%" PRIu32 ") slot(%" PRIu16
            ") lockMode(%" PRIu16 ") lockType(%" PRIu8 ").",
            ((TrxT *)seRunCtx->trx)->base.trxId, lockId->lockIdField1, lockId->lockIdField2, lockId->lockIdField3,
            lockId->lockIdField4, lockAcqEntry->acqInfo.lockMode, lockId->lockType);
        trx->trx.pesTrx.lockAcqInfo.isNewAllocLock = false;
    } while (trx->trx.pesTrx.holdLockNum > 0);
}

// 级联drop表时，原因：级联drop表是先删边表再删顶点表，与正常访问次序相反会死锁，因此提前释放边表所加的事务表锁
void SeLockMgrLockReleaseLastNewLabelXLock(SeRunCtxT *seRunCtx)
{
    DB_POINTER2(seRunCtx, seRunCtx->trx);

    TrxT *trx = seRunCtx->trx;
    DB_ASSERT(trx->trx.pesTrx.lockAcqInfo.lastLockMode == (uint8_t)SE_LOCK_MODE_X);
    DB_ASSERT(trx->trx.pesTrx.holdLockNum > 0u);
    // 持久化模式下，会先加表锁再加系统表的行锁，所以需要释放上一次表锁和期间加的行锁
    if (SeGetPersistMode() != PERSIST_OFF) {
        return SeLockMgrLockReleaseLastNewLabelAndTupleXLock(seRunCtx);
    }
    // 持久化场景下，在clear namespace时有操作系统表，涉及到对 isNewAllocLock的反复设置和复位，影响
    // QryDropVertexLabelWhenClearingNsp 的释放事务表锁时的看护，所以在持久化下不校验此flag
    DB_ASSERT(trx->trx.pesTrx.lockAcqInfo.isNewAllocLock);  // 一定是新申请的X锁

    SeLockAcqEntryT *lockAcqEntry = SeLockAcqGetEntryByAcqId(seRunCtx->lockAqcPool, trx->trx.pesTrx.holdLockAcqId);
    if (lockAcqEntry == NULL) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "lockAcqEntry");
        DB_ASSERT(false);
        return;  // 这个场景不应该出现
    }
    DB_ASSERT(lockAcqEntry->acqInfo.lock->lockId.lockType == (uint8_t)SE_LOCK_TYPE_LABEL_LOCK);
    SeLockMgrLockReleaseTopOfTrx(seRunCtx, trx);
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP,
        "ReleaseLock trx(%" PRIu64 ") db(%" PRIu32 ") label(%" PRIu32 ") page(%" PRIu32 ") slot(%" PRIu16
        ") lockMode(%" PRIu16 ").",
        ((TrxT *)seRunCtx->trx)->base.trxId, lockAcqEntry->acqInfo.lock->lockId.lockIdField1,
        lockAcqEntry->acqInfo.lock->lockId.lockIdField2, lockAcqEntry->acqInfo.lock->lockId.lockIdField3,
        lockAcqEntry->acqInfo.lock->lockId.lockIdField4, lockAcqEntry->acqInfo.lockMode);
    trx->trx.pesTrx.lockAcqInfo.isNewAllocLock = false;
}

inline bool SeLockMgrIsAnyLockAcquired(SeRunCtxHdT seRunCtx)
{
    return ((TrxT *)seRunCtx->trx)->trx.pesTrx.holdLockNum != 0;
}
#endif /* FEATURE_SIMPLEREL */

void SeLockMgrLockReleaseAll(SeRunCtxT *seRunCtx)
{
    DB_POINTER2(seRunCtx, seRunCtx->trx);
    TrxT *trx = seRunCtx->trx;
    while (trx->trx.pesTrx.holdLockNum != 0u) {
        SeLockMgrLockReleaseTopOfTrx(seRunCtx, trx);
    }
    // 所有锁都已经释放，lockNotifyId和holdLockAcqId应该都是无效值，看护一致性
    DB_ASSERT(trx->trx.pesTrx.lockNotifyId == SE_LOCK_NOTIFY_INVALID_ID);
    DB_ASSERT(trx->trx.pesTrx.holdLockAcqId == SE_LOCK_ACQ_INVALID_ID);
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP, "ReleaseAllLock trx(%" PRIu64 ")", trx->base.trxId);
}

#ifdef __cplusplus
}
#endif
