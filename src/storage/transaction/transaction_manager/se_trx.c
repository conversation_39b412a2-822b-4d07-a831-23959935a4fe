/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: implementation for transaction
 * Author: SE Team
 * Create: 2020-10-20
 */
#include "se_trx_inner.h"
#include "se_trx_mgr.h"
#include "se_log.h"
#include "se_undo.h"
#include "se_heap_utils.h"
#include "se_index_inner.h"
#include "se_lfsmgr.h"
#include "se_trx_base.h"
#include "se_fixed_heap_inner.h"
#include "se_dfx.h"
#include "se_resource_column.h"
#include "dm_meta_prop_label.h"
#include "db_dyn_load.h"
#include "se_daemon.h"
#include "se_clustered_hash_access_dm.h"
#include "se_clustered_hash_label_dml.h"
#include "db_crash_debug.h"
#include "db_mem_context.h"
#include "se_chained_hash_index.h"
#include "se_persist_inner.h"
#include "se_space_inner.h"
#include "se_undo_trx_resource.h"
#include "db_inter_process_rwlatch.h"
#include "se_redo_inner.h"
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
#include "se_daf.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif

#define TRX_DEFAULT_SPILT_TIME (1000)  // 1秒

#define SE_TRX_SHM_CTX_BASE_SIZE (1024 * 512)         // 512K is enough for TRX during server initialization
#define SE_TRX_SHM_CTX_STEP_SIZE (1024 * 1024 * 1)    // 1M is enough for TRX during memory allocation
#define SE_TRX_SHM_CTX_MAX_SIZE (1024 * 1024 * 1023)  // 1023M is enough for total TRX shm space
#define MAX_WAIT_TRX_BEGIN_TIME 300000                // This means total wait time limit is 30 second
#define SLEEP_MICROSEC_ONE_TIME 100

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
// This means total page cost of single trx with a normal object
static inline uint16_t TrxGetPageReserve(SeInstanceT *seIns)
{
    return RedoGetAtomicPageCapacity(seIns->redoMgr);
}
#endif /* FEATURE_SIMPLEREL */

int32_t TrxIdCmp(const void *left, const void *right)
{
    const TrxIdT trxIdLeft = *(const TrxIdT *)left;
    const TrxIdT trxIdRight = *(const TrxIdT *)right;
    if (trxIdLeft != trxIdRight) {
        return (int32_t)(trxIdLeft > trxIdRight ? 1 : -1);
    }
    return (int32_t)0;
}

// 事务提交/回滚完成需要清理buf，如果没有使用逃生memCtx，buf由事务最后对trxPeriodMemCtx的reset完成
void TrxClearKeyDataBuf(TrxT *trx)
{
    DB_POINTER(trx);
    if (trx->base.keyData != NULL) {
        trx->base.keyData = NULL;
        trx->base.keyLen = 0;
    }
    DB_ASSERT(trx->base.keyLen == 0);
}

/* 由上层函数负责加锁 */
inline void TrxGetCommitTs(TrxT *trx)
{
    DB_POINTER(trx);

    TrxMgrT *trxMgr = (TrxMgrT *)trx->trxMgr;
    uint64_t timeConsume = DbRdtsc() - trx->base.startTime;
    // 刷新commitTs, 方便undo模块记录commitTs，用于历史版本回收
    trx->base.commitTs = TrxMgrGetAndIncMaxTrxId(trxMgr);
    trxMgr->maxTrxTimeUse = DB_MAX(timeConsume, trxMgr->maxTrxTimeUse);
}

StatusInter TrxMgrCreateShmMemCtx(const SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx)
{
    DB_POINTER2(seIns, seTopShmMemCtx);
    DbMemCtxArgsT trxShmMemCtxArgs = {0};
    trxShmMemCtxArgs.instanceId = seIns->seConfig.instanceId;
    trxShmMemCtxArgs.ctxId = DB_SE_TRXMGR_SHM_CTX_ID;

    // Use private memory context setting
    char param[sizeof(DbBlockMemParamT)] = {0};
    DbBlockMemParamT *blockParam = (DbBlockMemParamT *)param;
    blockParam->isHugePage = false;
    blockParam->baseSize = SE_TRX_SHM_CTX_BASE_SIZE;
    blockParam->stepSize = SE_TRX_SHM_CTX_STEP_SIZE;
    uint64_t maxSize = DbGetTopShmemMaxSize(seIns->instanceId);
    blockParam->maxSize = (uint64_t)DB_MIN(maxSize, SE_TRX_SHM_CTX_MAX_SIZE);
    blockParam->isReused = false;
    blockParam->allowBigChunk = false;
    blockParam->blkPoolType = BLK_NORMAL;
    AlgoParamT algoParam;
    algoParam.blockParam = blockParam;
    trxShmMemCtxArgs.algoParam = &algoParam;
    /* SeTrxShmMemCtx 说明
        用    途: 用于创建事务池
        生命周期: 长进程级别
        释放策略: 就近释放
        兜底清空措施: 依赖上层seTopShmMemCtx, server退出时销毁
    */
    void *trxShmMemCtx = DbCreateBlockPoolShmemCtx(seTopShmMemCtx, "SeTrxShmMemCtx", &trxShmMemCtxArgs);
    if (trxShmMemCtx == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "trxMgr alloc ShmemCtx, instanceId:%" PRIu32 ", ctxId:%" PRIu32 "",
            trxShmMemCtxArgs.instanceId, trxShmMemCtxArgs.ctxId);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    return STATUS_OK_INTER;
}

uint8_t *TrxGetKeyDataBuf(TrxT *trx, uint32_t bufLength, DbMemCtxT *usrMemCtx)
{
    DB_POINTER2(trx, usrMemCtx);

    if (trx->base.keyLen >= bufLength) {
        DB_ASSERT(trx->base.keyData != NULL);
        return trx->base.keyData;
    }
    // 有就释放原来的内存
    TrxFreeKeyDataBuf(trx);
    uint32_t allocLength = DB_MAX(DB_KIBI, bufLength);  // 较小时，可以预先申请1K，避免反复申请
    // 申请一块内存用于提取主键时临时缓存主键数据, keyData如果长度不够会重新申请, 在事务提交阶段会调用reset
    // memCtx兜底清空
    trx->base.keyData = DbDynMemCtxAlloc(usrMemCtx, allocLength);
    if (trx->base.keyData == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "keyData alloc unsucc, size = %" PRIu32, allocLength);
        return NULL;
    }
    trx->base.keyLen = allocLength;
    return trx->base.keyData;
}

// TrxMgrCreate大函数拆分
static StatusInter InitTrxMgrInfo(
    TrxMgrT *trxMgr, SeInstanceT *seIns, uint32_t trxMgrSize, uint32_t maxTrxNum, uint32_t trxIdListSize)
{
    (void)memset_s((void *)trxMgr, trxMgrSize, 0, trxMgrSize);
    DbRWLatchInit(&trxMgr->latch);
    trxMgr->instanceId = seIns->instanceId;
    trxMgr->trxCnt = 0;
    trxMgr->maxTrxNum = (uint16_t)maxTrxNum;
    trxMgr->maxTrxId = 1;  // need to read from file after support persistent, valid trxId begin from 1
    trxMgr->maxCloneId = 1;
    trxMgr->minActiveNormalTrxId = DB_INVALID_ID64;
    trxMgr->rwTrxIdsOffset = (uint32_t)sizeof(TrxMgrT);
    trxMgr->roTrxIdsOffset = trxMgr->rwTrxIdsOffset + trxIdListSize;
    trxMgr->trxPoolOffset = trxMgr->roTrxIdsOffset + trxIdListSize;
    trxMgr->maxTrxTimeUse = 0;
    trxMgr->trxMgrShmAddr = seIns->trxMgrShm;
    trxMgr->isPersistence = seIns->storageType != SE_MEMDATA;
    trxMgr->multiTrxMgrShm = DB_INVALID_SHMPTR;
    InitSeOptiTrxInfoList(&trxMgr->optiTrxHistoryInfoList);
#ifdef FEATURE_YANG
    bool isForceCommit = DbCfgGetBoolLite(DB_CFG_FORCE_COMMIT_ENABLE, NULL);
    if (!isForceCommit) {
        trxMgr->optiTrxRetryInfo.isInit = false;
        return STATUS_OK_INTER;
    }
    return InitSeOptiTrxRetryCommitInfo(trxMgr, seIns);
#else
    return STATUS_OK_INTER;  // mini下未新增DB_CFG_FORCE_COMMIT_ENABLE全局配置项
#endif
}

static void TrxPoolAddNode(TrxPoolT *trxPool, TrxPoolNodeT *nodes, uint16_t trxSlot)
{
    if (trxPool->freeHead == trxSlot) {
        trxPool->freeHead = nodes[trxSlot].next;
    }
    // 从空闲事务链表中移除
    if (nodes[trxSlot].prev != DB_INVALID_ID16) {
        nodes[nodes[trxSlot].prev].next = nodes[trxSlot].next;
    }
    if (nodes[trxSlot].next != DB_INVALID_ID16) {
        nodes[nodes[trxSlot].next].prev = nodes[trxSlot].prev;
    }
    // 加入已用事务链表
    if (trxPool->usedHead != DB_INVALID_ID16) {
        nodes[trxPool->usedHead].prev = trxSlot;
        nodes[trxSlot].next = trxPool->usedHead;
    } else {
        nodes[trxSlot].next = DB_INVALID_ID16;
    }
    nodes[trxSlot].prev = DB_INVALID_ID16;
    trxPool->usedHead = trxSlot;
    trxPool->usedCnt++;
}

static StatusInter TrxCalcMaxTrxNum(SeInstanceT *seIns, uint32_t *maxTrxNum)
{
    int32_t dwCfg = DbCfgGetDirectWriteLite();
    // 从seIns内的memctx，申请共享内存承载TrxMgr数据结构
    uint32_t num = (dwCfg >= 1) ? (uint32_t)(seIns->seConfig.maxTrxNum * 2) : (uint32_t)seIns->seConfig.maxTrxNum;
#ifdef FEATURE_REPLICATION
    if (DbCfgGetBoolLite(DB_CFG_ENABLE_REPLICATION, seIns->dbInstance)) {
        num *= 2;  //  主备复制，重演最多使用和主机连接数一样的session
    }
#endif
#ifdef IDS_HAOTIAN
    if (DbCfgGetBoolLite(DB_CFG_ENABLE_CONCURRENT_FETCH_PAGE, seIns->dbInstance)) {
        //  并发获取页还需要后台并发线程的session
        num += (uint32_t)DbCfgGetInt32Lite(DB_CFG_MAX_PRE_FETCH_THRE_NUM, seIns->dbInstance);
    }
#endif
#ifdef EXPERIMENTAL_GUANGQI
    int32_t enableTrxClone = DbCfgGetInt32Lite(DB_CFG_ENABLE_TRX_CLONE, NULL);
    if (enableTrxClone) {
        // 事务克隆、合并，会导致一个连接使用多个事务槽，直接扩展1倍
        if (dwCfg >= 1) {
            num += seIns->seConfig.maxTrxNum;
        } else {
            num *= 2;
        }
    }
#endif

    *maxTrxNum = num;
    return STATUS_OK_INTER;
}

Status MultiReoverTrxMgr(SeInstanceT *seIns)
{
    DbMemCtxT *tlbMemctx = (DbMemCtxT *)DbGetShmemCtxById(DB_HUGETLB_SHMCTX_ID, seIns->instanceId);
    if (tlbMemctx == NULL) {
        SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "trxMgr get tlb memctx, instanceId:%" PRIu32 ".", seIns->instanceId);
        return DbGetStatusInterErrno(MEMORY_OPERATE_FAILED_INTER);
    }
    TrxMgrT *trxMgr = (TrxMgrT *)DbShmPtrToAddr(seIns->trxMgrShm);
    if (trxMgr == NULL) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get trxMgr");
        return DbGetStatusInterErrno(UNEXPECTED_NULL_VALUE_INTER);
    }
    DbRWLatchInit(&trxMgr->latch);
    TrxPoolT *trxPool = TrxMgrGetTrxPool(trxMgr);
    ShmemPtrT *trxs = TrxPoolGetTrxs(trxPool);
    // 重置事务节点链表
    TrxPoolResetNodes(trxPool);
    TrxPoolNodeT *nodes = TrxPoolGetNodes(trxPool);
    // TRX若非active状态，当前暂不能处理
    for (uint16_t slot = 0; slot < trxPool->trxPoolNodeNum; slot++) {
        uint16_t shmSlot = slot / TRX_NUM_PER_BLOCK;
        if (!DbIsShmPtrValid(trxs[shmSlot])) {
            // 后面的slot都没有被申请使用过，因此不用再判断，事务已经遍历完成
            break;
        }
        TrxT *trx = TrxMgrGetTrxBySlot(trxMgr, slot);
        if (trx == NULL || trx->base.processIdx == TRX_INVALID_PROCESS_IDX) {
            continue;
        }
        DB_LOG_DEBUG("trxPool slot %u processIdx %d", slot, trx->base.processIdx);
        // 添加事务节点
        TrxPoolAddNode(trxPool, nodes, slot);
        // 标识用以清理相关的资源
        trx->base.needReset = true;
    }
    TrxIdListInit(TrxMgrGetRwTrxIds(trxMgr), (uint32_t)seIns->seConfig.maxTrxNum);
    TrxIdListInit(TrxMgrGetRoTrxIds(trxMgr), (uint32_t)seIns->seConfig.maxTrxNum);
    return GMERR_OK;
}

StatusInter TrxMgrCreateInner(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx, DbMemCtxT *tlbMemctx, uint32_t maxTrxNum)
{
    uint32_t trxsShmPtrArrNum = (maxTrxNum + TRX_NUM_PER_BLOCK - 1) / TRX_NUM_PER_BLOCK;  // 向上取整
    uint32_t trxIdListSize = (uint32_t)(sizeof(TrxIdListT) + maxTrxNum * sizeof(TrxIdT));
    uint32_t trxMgrSize = (uint32_t)sizeof(TrxMgrT) + GetTrxPoolSize(maxTrxNum, trxsShmPtrArrNum) + trxIdListSize +
                          trxIdListSize;  // rwTrxIds roTrxIds trxPool

    // 从大页内存中申请共享内存来承载 trxMgr, 依赖大页内存memCtx, 在server退出时销毁
    TrxMgrT *trxMgr = (TrxMgrT *)SeShmAlloc(tlbMemctx, trxMgrSize, &seIns->trxMgrShm);
    if (trxMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "trxMgr alloc unsucc, size:%" PRIu32, trxMgrSize);
        return UNEXPECTED_NULL_VALUE_INTER;
    }

    StatusInter ret = InitTrxMgrInfo(trxMgr, seIns, trxMgrSize, maxTrxNum, trxIdListSize);
    if (ret != STATUS_OK_INTER) {
        DbShmemCtxFree(tlbMemctx, seIns->trxMgrShm);
        seIns->trxMgrShm = DB_INVALID_SHMPTR;
        return ret;
    }
    // 申请分配trx需要的共享内存上下文trxShmMemCtxId
    ret = TrxMgrCreateShmMemCtx(seIns, seTopShmMemCtx);
    if (ret != STATUS_OK_INTER) {
        DbShmemCtxFree(tlbMemctx, seIns->trxMgrShm);
        seIns->trxMgrShm = DB_INVALID_SHMPTR;
        return ret;
    }
    // 申请事务池
    TrxPoolInit(TrxMgrGetTrxPool(trxMgr), DB_SE_TRXMGR_SHM_CTX_ID, maxTrxNum, trxsShmPtrArrNum);
    TrxIdListInit(TrxMgrGetRwTrxIds(trxMgr), maxTrxNum);
    TrxIdListInit(TrxMgrGetRoTrxIds(trxMgr), maxTrxNum);
    DB_LOG_INFO("(SE-Transaction) Create txrMgr, Addr(segId: %" PRIu32 ", offset: %" PRIu32 ")", seIns->trxMgrShm.segId,
        seIns->trxMgrShm.offset);
    return STATUS_OK_INTER;
}

StatusInter TrxMgrCreate(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx)
{
    DB_POINTER2(seIns, seTopShmMemCtx);
    uint32_t maxTrxNum = 0;
    StatusInter ret = TrxCalcMaxTrxNum(seIns, &maxTrxNum);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    DbMemCtxT *tlbMemctx = (DbMemCtxT *)DbGetShmemCtxById(DB_HUGETLB_SHMCTX_ID, seIns->instanceId);
    if (tlbMemctx == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "trxMgr get tlb memctx, instanceId:%" PRIu32 ", ctxId:%" PRId32 "",
            seIns->instanceId, (int32_t)DB_HUGETLB_SHMCTX_ID);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    return TrxMgrCreateInner(seIns, seTopShmMemCtx, tlbMemctx, maxTrxNum);
}

StatusInter TrxMgrReset(SeInstanceT *seIns)
{
    TrxMgrT *trxMgr = (TrxMgrT *)DbShmPtrToAddr(seIns->trxMgrShm);
    if (trxMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get trxMgr unsucc, (segid: %" PRIu32 " offset: %" PRIu32 ")",
            seIns->trxMgrShm.segId, seIns->trxMgrShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    trxMgr->minActiveNormalTrxId = DB_INVALID_ID64;
    trxMgr->maxTrxId = 1;
    // 当前只有时序场景会用这个接口，不会用到乐观的资源
    DB_ASSERT(trxMgr->optiTrxHistoryInfoList.listLen == 0);
    return STATUS_OK_INTER;
}

static void TrxMgrAllocTrxInitBase(SeRunCtxT *seRunCtx, TrxT *trx, uint16_t trxSlot)
{
    trx->base.trxSlot = trxSlot;
    trx->base.trxId = DB_INVALID_TRX_ID;
    trx->base.startTime = 0;
    trx->base.readOnly = true;
    trx->base.isRecovery = false;
    trx->base.isAllocNew = false;
    trx->base.isNeedModifyPubsubList = true;
    trx->base.recoveryNeedHandleResource = false;
    trx->base.keyData = NULL;
    trx->base.keyLen = 0;
    trx->base.trxCommitCallBackCfg = (TrxCommitCallBackCfgT){0};
    trx->base.isPurger = false;
    trx->base.state = TRX_STATE_NOT_STARTED;
    trx->base.needReset = false;
}

static void TrxMgrAllocTrxInit(TrxMgrT *trxMgr, SeRunCtxT *seRunCtx, TrxT *trx, uint16_t trxSlot)
{
    DB_POINTER(trx);
    TrxMgrAllocTrxInitBase(seRunCtx, trx, trxSlot);
    trx->liteTrx.isUseRsm = false;
    trx->liteTrx.cacheBuf = APtrNULL();
    trx->liteTrx.bufLength = 0;
    trx->liteTrx.usedBuf = false;
    trx->liteTrx.cacheExtendArrFirstNode = APtrNULL();
    trx->liteTrx.cacheNodeSize = 0;
    trx->liteTrx.usedCacheNode = false;
    trx->trx.base.readView.isOpen = false;
    trx->trx.base.readView.activeTrxIdsShm = DB_INVALID_SHMPTR;
    trx->trx.base.readView.activeTrxIdsTmp = NULL;
    trx->trxMgr = (void *)trxMgr;
    trx->seRunCtx = seRunCtx;
    trx->trx.base.rseg = NULL;
    trx->trx.base.normalUndo = NULL;
    trx->trx.base.retainedUndo = NULL;
    trx->trx.pesTrx.holdLockNum = 0;
    trx->trx.pesTrx.holdLockAcqId = SE_LOCK_ACQ_INVALID_ID;
    trx->trx.pesTrx.lockNotifyId = SE_LOCK_ACQ_INVALID_ID;
    trx->trx.optTrx.labelReadView.isTrxCommitCheckActive = 0;
    trx->trx.optTrx.labelReadView.curItemCnt = 0;
    trx->trx.optTrx.labelReadView.savePointIdForCheckActive = 0;
    trx->tid = (uint64_t)(uintptr_t)DbThreadGetTid();
    trx->updateStatusMergeListCfg = (UpdateStatusMergeListCfgT){0};
    trx->cntrCtxList = (TrxContainerCtxListT){0};
    (void)memset_s(trx->trx.optTrx.labelReadView.itemList, DEF_EXT_VIEW_LIST_CNT * sizeof(DbListT), 0,
        DEF_EXT_VIEW_LIST_CNT * sizeof(DbListT));
    trx->liteTrx.rsmUndoRec = NULL;
    trx->liteTrx.periodMemCtx = NULL;
    trx->trx.base.savePointInfo.itemList = (DbListT){0};
    trx->cntrCtxList.lruCacheItemCnt = 0;
    trx->cntrCtxList.enableLruCache = false;
    trx->cntrCtxList.lruCacheHead = NULL;
    trx->cntrCtxList.lruCacheTail = NULL;
    DbCreateList(&trx->cntrCtxList.list, sizeof(char *), seRunCtx->sessionMemCtx);
    DbCreateList(&trx->trx.optTrx.labelReadView.itemList[0], sizeof(LabelReadViewInfoT), seRunCtx->sessionMemCtx);
    DbCreateList(&trx->trx.base.savePointInfo.itemList, sizeof(SavePointT), seRunCtx->sessionMemCtx);
#ifdef EXPERIMENTAL_GUANGQI
    trx->base.cloneType = TRX_ClONE_TYPE_DEFAULT_TRX;
    trx->trx.optTrx.cloneId = 0;
    trx->trx.optTrx.relationTrxInfo = (RelationTrxInfoT){0};
    trx->trx.optTrx.cloneTrxExtInfo = (CloneTrxExtInfoT){0};
    trx->trx.optTrx.cloneControlInfo.refCount = 0;
    trx->trx.optTrx.cloneControlInfo.cloneControllerShm = DB_INVALID_SHMPTR;
    DbRWLatchInit(&trx->trx.optTrx.cloneControlInfo.latch);
#endif
    // 初始化
}

StatusInter TrxMgrAllocTrx(TrxMgrT *trxMgr, SeRunCtxT *seRunCtx, TrxT **trxOut)
{
    DB_POINTER2(trxMgr, seRunCtx);

    TrxT *trx = NULL;
    uint16_t trxSlot = DB_INVALID_ID16;
    TrxPoolT *trxPool = TrxMgrGetTrxPool(trxMgr);
    RW_LATCH_WLATCH_RETURN_IF_FAILED(&trxPool->latch);
    StatusInter ret = TrxPoolAllocTrx(trxPool, seRunCtx->instanceId, &trx, &trxSlot);
    DbInterProcRWUnlatchW(&trxPool->latch);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    (void)DbAtomicFetchAndAddInt(&(trxMgr->trxCnt), 1);
    TrxMgrAllocTrxInit(trxMgr, seRunCtx, trx, trxSlot);
    *trxOut = trx;
    return STATUS_OK_INTER;
}

void SeResetTrxOnNeed(SeRunCtxT *seRunCtx, TrxT *trx)
{
    if (!trx->base.needReset) {
        return;
    }
    TrxClearKeyDataBuf(trx);  // 此处仅作变量清理，内存释放由trxPeriodMemCtx的reset完成
    TrxReleaseRes(trx);
    ReadViewClose(trx);
    trx->base.needReset = false;
    trx->updateStatusMergeListCfg = (UpdateStatusMergeListCfgT){0};
    trx->cntrCtxList = (TrxContainerCtxListT){0};
    (void)memset_s(trx->trx.optTrx.labelReadView.itemList, DEF_EXT_VIEW_LIST_CNT * sizeof(DbListT), 0,
        DEF_EXT_VIEW_LIST_CNT * sizeof(DbListT));
    trx->trx.base.savePointInfo.itemList = (DbListT){0};
    (void)memset_s(trx->liteTrx.undoLiteRec, sizeof(UndoLiteRecordT), 0, sizeof(UndoLiteRecordT));
    DbCreateList(&trx->cntrCtxList.list, sizeof(char *), seRunCtx->sessionMemCtx);
    DbCreateList(&trx->trx.optTrx.labelReadView.itemList[0], sizeof(LabelReadViewInfoT), seRunCtx->sessionMemCtx);
    DbCreateList(&trx->trx.base.savePointInfo.itemList, sizeof(SavePointT), seRunCtx->sessionMemCtx);
    DB_LOG_DEBUG("trx slot %d processIdx %d reset trx", trx->base.trxSlot, trx->base.processIdx);
}

void TrxHandleContainerCtxList(TrxT *trx, TrxHandleTypeE type);

inline static bool TrxAllResFreeCheck(const TrxT *trx)
{
    for (int32_t i = 0; i < trx->cntrCtxList.itemCnt; i++) {
        if (!TrxContainerIsFree(&trx->cntrCtxList.ctxs[i])) {
            return false;
        }
    }
    uint32_t listCnt = DbListGetItemCnt(&trx->cntrCtxList.list);
    return listCnt == 0;
}

static void TrxReleaseResInner(TrxT *trx)
{
    DB_POINTER(trx);
    // 这里由于已经提交清理过一遍，再清理应该释放所有资源
    TrxHandleContainerCtxList(trx, TRX_CLEAR_HANDLE);
    // 如果是失败的事务，没有提交或回归清理资源，所以这里需要清理两次
    if (trx->base.state == TRX_STATE_FAILURE) {
        TrxHandleContainerCtxList(trx, TRX_CLEAR_HANDLE);
    }
    DB_ASSERT(TrxAllResFreeCheck(trx));     // 注意：DB_ASSERT里是耗时检查，看护资源释放，请勿移出
    DbDestroyList(&trx->cntrCtxList.list);  // 释放数组
    TrxCloseSavepoint(trx);
    DbDestroySavepointList(trx);
    OptiTrxDestroyLabelReadview(trx);
}

void TrxReleaseRes(TrxT *trx)
{
    DB_POINTER(trx);
    TrxReleaseResInner(trx);  // 释放事务时，先释放事务申请的相关资源
    ReadViewClose(trx);       // 关闭read view
}

// 针对直连写场景客户端申请的事务槽，服务器侧仅负责归还该事务槽
void FreeTrxForDirectWriteInner(TrxMgrT *trxMgr, uint16_t trxSlot)
{
    TrxPoolFreeTrx(trxMgr, trxSlot);
}

// 该方法用于服务器侧在断链时归还直连写对应的事务槽，而事务槽对应的资源在断链前由客户端释放(SeCloseTrx)
void TrxMgrFreeTrxForDirectWrite(DbSessionCtxT *sessionCtx, SeRunCtxHdT seRunCtx)
{
    if (sessionCtx->session != NULL && sessionCtx->session->dwTrxSlot != DB_INVALID_UINT16) {
        SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(seRunCtx->instanceId);
        if (SECUREC_UNLIKELY(seIns == NULL)) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "storage instance %" PRIu16 " novalid", seRunCtx->instanceId);
            return;
        }
        TrxMgrT *trxMgr = (TrxMgrT *)DbShmPtrToAddr(seIns->trxMgrShm);
        if (SECUREC_UNLIKELY(trxMgr == NULL)) {
            SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "get trxMgr unsucc, (segid: %" PRIu32 " offset: %" PRIu32 ")",
                seIns->trxMgrShm.segId, seIns->trxMgrShm.offset);
        } else {
            FreeTrxForDirectWriteInner(trxMgr, sessionCtx->session->dwTrxSlot);
            sessionCtx->session->dwTrxSlot = DB_INVALID_UINT16;
        }
    }
}

void TrxMgrFreeTrx(TrxMgrT *trxMgr, TrxT *trx)
{
    DB_POINTER2(trxMgr, trx);

    TrxReleaseRes(trx);  // 释放事务申请的相关资源
    trx->base.processIdx = TRX_INVALID_PROCESS_IDX;
    TrxPoolFreeTrx(trxMgr, trx->base.trxSlot);  // 归还事务槽
}

void TrxReleaseIdx(const SeTrxContainerCtxT *item)
{
    DB_POINTER2(item, item->trxCntrCtx.cntrRunHdl);
    IndexCtxT *idxCtx = item->trxCntrCtx.cntrRunHdl;
    DbDynMemCtxFree(item->ctxHead.parentMemCtx, idxCtx);
    idxCtx = NULL;
}

// 释放某个资源句柄
void TrxReleaseHandle(SeTrxContainerCtxT *item)
{
    DB_POINTER(item);

    switch (item->ctxHead.type) {
        case TRX_FIXED_HEAP_HANDLE:
            if (item->trxCntrCtx.cntrRunHdl != NULL) {
                FixedHeapCloseForTrxMerge(item->trxCntrCtx.cntrRunHdl);
                item->trxCntrCtx.cntrRunHdl = NULL;
            }
            break;
        case TRX_HEAP_HANDLE:
            if (item->heapTrxCtx.heapRunHdl != NULL) {
                HeapLabelReleaseRunctxForTrxMerge(item->heapTrxCtx.heapRunHdl);
                item->heapTrxCtx.heapRunHdl = NULL;
            }
            break;
        case TRX_CLUSTERED_HASH_HANDLE:
            if (item->chTrxCtx.chRunCtx != NULL) {
                ChLabelReleaseRunCtx(item->chTrxCtx.chRunCtx);
                item->chTrxCtx.chRunCtx = NULL;
            }
            break;
        case TRX_INDEX_HANDLE:
            if (item->trxCntrCtx.cntrRunHdl != NULL) {
                if (item->ctxHead.id != TRX_EMPTY_CONTAINER_ID) {
                    TupleBufRelease(&((IndexCtxT *)item->trxCntrCtx.cntrRunHdl)->tupleBuf);
                    IdxClose((IndexCtxT *)item->trxCntrCtx.cntrRunHdl);
                }
                TrxReleaseIdx(item);
                item->trxCntrCtx.cntrRunHdl = NULL;
            }
            break;
        case TRX_RES_POOL_HANDLE:
            if (item->trxCntrCtx.cntrRunHdl != NULL) {
                Status ret = ResColPoolClose(item->trxCntrCtx.cntrRunHdl);
                DB_ASSERT(ret == GMERR_OK);
                item->trxCntrCtx.cntrRunHdl = NULL;
            }
            break;
        default:
            break;
    }
}

// LRU缓存的元素删除
// 删除并释放链表的头节点
void TrxCntrLruCacheRemoveNode(TrxContainerCtxListT *cntrCtxList)
{
    DB_POINTER(cntrCtxList);
    if (cntrCtxList->lruCacheHead == NULL) {
        return;
    }

    TrxContainerLruCacheNodeT *tempNode = cntrCtxList->lruCacheHead;
    cntrCtxList->lruCacheHead = cntrCtxList->lruCacheHead->next;
    if (cntrCtxList->lruCacheHead == NULL) {
        cntrCtxList->lruCacheTail = NULL;
    }
    TrxReleaseHandle(tempNode->container);
    void *parentMemCtx = tempNode->container->ctxHead.parentMemCtx;
    DbDynMemCtxFree(parentMemCtx, tempNode->container);
    tempNode->container = NULL;
    DbDynMemCtxFree(parentMemCtx, tempNode);
    cntrCtxList->lruCacheItemCnt--;
}

// LRU缓存的元素添加
// 如果链表未满，申请容器句柄作为新的节点同时添加到链表的尾部；如果链表已满，则删除并释放链表的头节点
StatusInter TrxCntrLruCacheAddNode(TrxT *trx, uint32_t containerId, SeTrxContainerCtxT **trxCntrCtx)
{
    DB_POINTER2(trx, trxCntrCtx);
    // 申请一个新的事务资源运行上下文, 在逃生内存还原以及LRU节点删除时配对释放
    SeTrxContainerCtxT *seTrxCntrCtx = DbDynMemCtxAlloc(trx->seRunCtx->sessionMemCtx, sizeof(SeTrxContainerCtxT));
    if (seTrxCntrCtx == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "TrxCntrLruCacheAddNode: seTrxCntrCtx alloc unsucc.");
        return OUT_OF_MEMORY_INTER;
    }
    TrxContainerLruCacheNodeT *newNode =
        DbDynMemCtxAlloc(trx->seRunCtx->sessionMemCtx, sizeof(TrxContainerLruCacheNodeT));
    if (newNode == NULL) {
        DbDynMemCtxFree(trx->seRunCtx->sessionMemCtx, seTrxCntrCtx);
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "TrxCntrLruCacheAddNode: lruCacheNode alloc unsucc.");
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_sp(seTrxCntrCtx, sizeof(SeTrxContainerCtxT), 0, sizeof(SeTrxContainerCtxT));
    seTrxCntrCtx->ctxHead.id = containerId;
    seTrxCntrCtx->ctxHead.parentMemCtx = trx->seRunCtx->sessionMemCtx;
    newNode->container = seTrxCntrCtx;
    newNode->next = NULL;
    *trxCntrCtx = seTrxCntrCtx;

    if (trx->cntrCtxList.lruCacheHead == NULL) {
        trx->cntrCtxList.lruCacheHead = newNode;
    } else {
        trx->cntrCtxList.lruCacheTail->next = newNode;
    }
    trx->cntrCtxList.lruCacheTail = newNode;

    // 限制链表最大长度
    trx->cntrCtxList.lruCacheItemCnt++;
    if (trx->cntrCtxList.lruCacheItemCnt > SE_TRX_CNTR_LRU_CACHE_SIZE) {
        TrxCntrLruCacheRemoveNode(&trx->cntrCtxList);
    }
    DB_ASSERT(trx->cntrCtxList.lruCacheItemCnt <= SE_TRX_CNTR_LRU_CACHE_SIZE);
    return STATUS_OK_INTER;
}

void TrxCntrLruCacheMoveNodeToEnd(TrxContainerCtxListT *cntrCtxList, TrxContainerLruCacheNodeT *prevNode)
{
    DB_POINTER(cntrCtxList);
    TrxContainerLruCacheNodeT *curNode = NULL;
    if (prevNode == NULL) {
        DB_ASSERT(cntrCtxList->lruCacheHead != NULL);
        curNode = cntrCtxList->lruCacheHead;
        if (curNode == cntrCtxList->lruCacheTail) {
            return;
        }
        DB_ASSERT(cntrCtxList->lruCacheItemCnt > 1);
        cntrCtxList->lruCacheHead = curNode->next;
    } else {
        DB_ASSERT(cntrCtxList->lruCacheItemCnt > 1);
        DB_ASSERT(prevNode->next != NULL);
        DB_ASSERT(prevNode != cntrCtxList->lruCacheTail);
        curNode = prevNode->next;
        if (curNode == cntrCtxList->lruCacheTail) {
            return;
        }
        prevNode->next = curNode->next;
    }

    curNode->next = NULL;
    cntrCtxList->lruCacheTail->next = curNode;
    cntrCtxList->lruCacheTail = curNode;
}

// LRU缓存的元素查找
// 按照容器id遍历链表进行查找；当一个元素被访问时，移动到链表的尾部，使得最久未访问的元素总是在链表的头部
SeTrxContainerCtxT *TrxGetCntrCtxByIdUseLruCache(TrxContainerCtxListT *cntrCtxList, uint32_t containerId)
{
    DB_POINTER(cntrCtxList);
    TrxContainerLruCacheNodeT *curNode = cntrCtxList->lruCacheHead;
    TrxContainerLruCacheNodeT *prevNode = NULL;
    while (curNode != NULL) {
        if (curNode->container->ctxHead.id == containerId) {
            TrxCntrLruCacheMoveNodeToEnd(cntrCtxList, prevNode);
            return curNode->container;
        }
        prevNode = curNode;
        curNode = curNode->next;
    }
    return NULL;
}

SeTrxContainerCtxT *TrxGetContainerCtxById(TrxT *trx, uint32_t containerId)
{
    DB_POINTER(trx);
    for (uint16_t i = 0; i < trx->cntrCtxList.itemCnt; i++) {
        SeTrxContainerCtxT *container = &trx->cntrCtxList.ctxs[i];
        if (!TrxContainerIsFree(container)) {
            if (container->ctxHead.id == containerId) {
                return container;
            }
        } else {
            trx->cntrCtxList.freeContainer = container;
        }
    }

    uint32_t left = 0;
    uint32_t right = DbListGetItemCnt(&trx->cntrCtxList.list);
    while (left < right) {
        uint32_t mid = (left + right) >> 1;
        SeTrxContainerCtxT **item = (SeTrxContainerCtxT **)DbListItem(&trx->cntrCtxList.list, mid);
        SeTrxContainerCtxT *container = *item;
        if (container->ctxHead.id == containerId) {
            return container;
        } else if (container->ctxHead.id > containerId) {
            right = mid;
        } else {
            left = mid + 1;
        }
    }
    return NULL;
}

uint32_t TrxGetContainerCtxInsertPos(TrxT *trx, uint32_t containerId)
{
    uint32_t left = 0;
    uint32_t right = DbListGetItemCnt(&trx->cntrCtxList.list);
    while (left < right) {
        uint32_t mid = (left + right) >> 1;
        SeTrxContainerCtxT **item = (SeTrxContainerCtxT **)DbListItem(&trx->cntrCtxList.list, mid);
        SeTrxContainerCtxT *container = *item;
        if (container->ctxHead.id > containerId) {
            right = mid;
        } else {
            left = mid + 1;
        }
    }
    return right;
}

void TrxRunHdlSetUsrMemCtx(SeTrxContainerCtxT *item, DbMemCtxT *usrMemCtx)
{
    DB_POINTER2(item, usrMemCtx);
    switch (item->ctxHead.type) {
        case TRX_FIXED_HEAP_HANDLE:
            if (item->trxCntrCtx.cntrRunHdl != NULL) {
                ((FixedHeapRunCtxT *)item->trxCntrCtx.cntrRunHdl)->usrMemCtx = usrMemCtx;
            }
            break;
        case TRX_HEAP_HANDLE:
            if (item->heapTrxCtx.heapRunHdl != NULL) {
                item->heapTrxCtx.heapRunHdl->usrMemCtx = usrMemCtx;
            }
            break;
        case TRX_CLUSTERED_HASH_HANDLE:
            if (item->chTrxCtx.chRunCtx != NULL) {
                item->chTrxCtx.chRunCtx->openCfg.usrMemCtx = usrMemCtx;
            }
            break;
        default:
            break;
    }
}

void TrxCntrSetRunHdlUsrMemCtx(TrxT *trx, DbMemCtxT *usrMemCtx)
{
    DB_POINTER2(trx, usrMemCtx);
    for (uint16_t i = 0; i < trx->cntrCtxList.itemCnt; i++) {
        SeTrxContainerCtxT *container = &trx->cntrCtxList.ctxs[i];
        if (!TrxContainerIsFree(container)) {
            TrxRunHdlSetUsrMemCtx(container, usrMemCtx);
        }
    }

    uint32_t listCnt = DbListGetItemCnt(&trx->cntrCtxList.list);
    for (uint32_t i = 0; i < listCnt; i++) {
        SeTrxContainerCtxT **item = (SeTrxContainerCtxT **)DbListItem(&trx->cntrCtxList.list, i);
        SeTrxContainerCtxT *container = *item;
        TrxRunHdlSetUsrMemCtx(container, usrMemCtx);
    }
}

StatusInter TrxStoreContainerCtxToList(TrxT *trx, uint32_t containerId, SeTrxContainerCtxT **trxCntrCtx)
{
    DB_POINTER2(trx, trxCntrCtx);

    SeTrxContainerCtxT *seTrxCntrCtx = NULL;
    if (trx->cntrCtxList.itemCnt < DEF_RESVED_ITEM_CNT) {
        seTrxCntrCtx = &trx->cntrCtxList.ctxs[trx->cntrCtxList.itemCnt];
        trx->cntrCtxList.itemCnt++;
    } else if (trx->cntrCtxList.freeContainer != NULL) {
        // 遍历时有发现空闲的容器
        seTrxCntrCtx = trx->cntrCtxList.freeContainer;
        trx->cntrCtxList.freeContainer = NULL;
    } else {
        // 申请一个新的事务资源运行上下文, 在事务结束时配对释放
        seTrxCntrCtx = DbDynMemCtxAlloc(trx->seRunCtx->sessionMemCtx, sizeof(SeTrxContainerCtxT));
        if (seTrxCntrCtx == NULL) {
            SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "seTrxCntrCtx alloc unsucc.");
            return OUT_OF_MEMORY_INTER;
        }
        uint32_t pos = TrxGetContainerCtxInsertPos(trx, containerId);
        Status ret = DbInsertListItem(&trx->cntrCtxList.list, &seTrxCntrCtx, pos);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(trx->seRunCtx->sessionMemCtx, seTrxCntrCtx);
            seTrxCntrCtx = NULL;
            SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "cntrCtxList append item unsucc.");
            return MEMORY_OPERATE_FAILED_INTER;
        }
    }
    (void)memset_sp(seTrxCntrCtx, sizeof(SeTrxContainerCtxT), 0, sizeof(SeTrxContainerCtxT));
    seTrxCntrCtx->ctxHead.id = containerId;
    seTrxCntrCtx->ctxHead.parentMemCtx = trx->seRunCtx->sessionMemCtx;
    *trxCntrCtx = seTrxCntrCtx;
    return STATUS_OK_INTER;
}

StatusInter TrxStoreContainerCtx(
    TrxT *trx, uint32_t containerId, SeTrxContainerCtxT **trxCntrCtx, bool *isNewContainerOpening)
{
    DB_POINTER3(trx, trxCntrCtx, isNewContainerOpening);

    *isNewContainerOpening = false;
    // 当isAllocNew为true时，直接创建新容器，目前只在V1导入操作中使用
    // 同一个事务里不可多次操作同一张表，否则会创建多个容器
    SeTrxContainerCtxT *seTrxCntrCtx;
    if (SECUREC_LIKELY(!trx->base.isAllocNew)) {
        seTrxCntrCtx = TrxGetContainerCtxById(trx, containerId);
    } else {
        seTrxCntrCtx = NULL;
    }
    if (seTrxCntrCtx != NULL) {
        *isNewContainerOpening = !TrxContainerIsUse(seTrxCntrCtx);  // 判断是否第一次标志本事务使用
        *trxCntrCtx = seTrxCntrCtx;
        return STATUS_OK_INTER;
    }
    *isNewContainerOpening = true;
    return TrxStoreContainerCtxToList(trx, containerId, trxCntrCtx);
}

#ifndef NDEBUG
// Heap容器debug辅助检查，轻量化事务检查主键和Heap的数量是否1：1对应
// Heap容器只要是主键索引肯定是HashTable，如果后续有修改，需要注意
// 轻量化事务场景下只有一个线程可以对一个表进行写操作，且轻量化事务对同一行都是单步事务，不存在同一事务删除再插入场景.
// 所以理论上索引的entry和Heap的物理记录数应该相等.
void HeapDebugCheckPriKeyIdxEntryUsed(HpRunHdlT heapHandle)
{
    if (heapHandle == NULL) {
        return;
    }
    if (heapHandle->heapCfg.ccType == CONCURRENCY_CONTROL_NORMAL) {
        return;
    }
    if (heapHandle->hpControl.isRsmRecovery) {
        // 恢复阶段还未重建索引，不进行校验
        return;
    }
    HeapDmIndexIterT dmPriKeyInfo = {0};
    HeapAmGetDmPriKeyInfo(heapHandle, &dmPriKeyInfo);
    if (dmPriKeyInfo.isGet) {
        IdxBaseT *idxHandle = DbShmPtrToAddr(dmPriKeyInfo.idxShmAddr);
        if (idxHandle != NULL) {
            if (idxHandle->indexCfg.idxType == CHAINED_HASH_INDEX) {
                CHTableT *ht = CHCastIdxAsCHHashTable(idxHandle);
                DB_ASSERT(ht->entryUsed == heapHandle->perfStat->phyItemNum);
            } else if (idxHandle->indexCfg.idxType == HASH_INDEX) {
                HashTableT *ht = HtCastIdxAsHashTable(idxHandle);
                DB_ASSERT(ht->entryUsed == heapHandle->perfStat->phyItemNum);
            }
        }
    }
}
#endif

inline static void TrxHandleHeapTrxCtx(TrxT *trx, SeTrxContainerCtxT *trxCntrCtx, TrxHandleTypeE type)
{
    if (SECUREC_LIKELY(type == TRX_COMMIT_HANDLE)) {
        TrxCommitHandleHeapTrxCtx(trx, trxCntrCtx);
    } else if (type == TRX_ROLLBACK_HANDLE) {
        TrxRollBackHandleHeapTrxCtx(trx, trxCntrCtx);
    } else {
        // 什么都不用做
        DB_ASSERT(type == TRX_CLEAR_HANDLE);
    }
#ifndef NDEBUG
    // 若在HeapOpen时内存不足，heapTrxCtx中用于undo的heap句柄可能申请失败而为空，此时不进行检查
    HpRunHdlT heapRunHdl = trxCntrCtx->heapTrxCtx.heapRunHdl;
    if (TrxIsLiteTrx(trx) && heapRunHdl != NULL && !HeapIsOnLabelLatchMode(heapRunHdl)) {
        HeapDebugCheckPriKeyIdxEntryUsed(heapRunHdl);
    }
#endif
}

inline static void TrxContainerResetDmInfo(TrxT *trx, SeTrxContainerCtxT *container)
{
    if (container->ctxHead.type == (uint16_t)TRX_HEAP_HANDLE) {
        // 如果是直连写则不释放dmlinfo以及vertex
        HeapRunCtxT *heapRunCtx = (HeapRunCtxT *)container->heapTrxCtx.heapRunHdl;
        if (heapRunCtx == NULL || heapRunCtx->seRunCtx == NULL) {  // heapRunCtx有可能为空
            // 此处用于debug下定位，正常情况下heapRunCtx不为空时，seRunCtx也不为空
            DB_ASSERT(heapRunCtx == NULL || heapRunCtx->seRunCtx != NULL);
            return;
        }
        if (heapRunCtx->seRunCtx->resSessionCtx.isDirectWrite) {
            return;
        }
        // 恢复过程拉起过Purge，VertexLabel可能会在此处已经释放，所以dmInfo可能为空
        if (trx->base.isRecovery && trx->base.recoveryNeedHandleResource) {
            if (heapRunCtx->dmDetail.dmInfo == NULL) {
                DB_ASSERT(!TrxContainerIsUse(container));
            } else {
                UndoRecoveryReleaseLabelHandle(trx, heapRunCtx->heapCfg.labelId,
                    HeapGetLabelTypeByTupleType(heapRunCtx->heapCfg.tupleType), heapRunCtx->dmDetail.dmInfo);
            }
        }
        container->heapTrxCtx.dmInfo = NULL;
        container->heapTrxCtx.vertex = NULL;
        if (container->heapTrxCtx.heapRunHdl) {
            container->heapTrxCtx.heapRunHdl->dmDetail.dmInfo = NULL;
        }
        container->heapTrxCtx.commitCallback = (CallBackStructT){NULL, NULL};
    } else if (container->ctxHead.type == (uint16_t)TRX_CLUSTERED_HASH_HANDLE) {
        // 如果是直连写则不释放 vertexLabel 以及 vertex
        ChLabelRunHdlT chRunctx = container->chTrxCtx.chRunCtx;
        if (chRunctx == NULL || chRunctx->seRunCtx == NULL || chRunctx->seRunCtx->resSessionCtx.isDirectWrite) {
            // 此处用于debug下定位，正常情况下chRunctx不为空时，seRunCtx也不为空
            DB_ASSERT(chRunctx == NULL || chRunctx->seRunCtx != NULL);
            return;
        }
        container->chTrxCtx.vertexLabel = NULL;
        container->chTrxCtx.vertex = NULL;
        container->chTrxCtx.commitCallback = (CallBackStructT){NULL, NULL};
        if (container->chTrxCtx.chRunCtx) {
            container->chTrxCtx.chRunCtx->openCfg.vertexLabel = NULL;
            container->chTrxCtx.chRunCtx->openCfg.vertex = NULL;
        }
    }
}

inline static void TrxHandleContainerCtxResetInfo(SeTrxContainerCtxT *container)
{
    DB_POINTER(container);
    container->ctxHead.isUsed = false;    // 清理当前事务使用标识
    container->ctxHead.isOpened = false;  // 标记所有资源都应该执行Close了
}

inline static void TrxHandleChTrxCtx(TrxT *trx, SeTrxContainerCtxT *container)
{
    if (container->chTrxCtx.chRunCtx != NULL) {
        container->chTrxCtx.chRunCtx->openCfg.tupleBuf = NULL;
#ifndef NDEBUG
        if (container->chTrxCtx.chRunCtx->openCfg.isUseRsm) {
            DbRsmKernelLeave(DB_RSM_KERNEL_CLUSTERED_HASH_DML);
        }
#endif
    }
    TrxContainerResetDmInfo(trx, container);
}

void TrxHandleContainerCtxInList(TrxT *trx, TrxHandleTypeE type)
{
    uint32_t listCnt = DbListGetItemCnt(&trx->cntrCtxList.list);
    // 下标i用于计数，下标listIdx记录实际数组当前下标，删除元素时listIdx要调整
    int32_t listIdx = 0;
    for (uint32_t i = 0; i < listCnt; i++, listIdx++) {
        DB_ASSERT(listIdx >= 0);
        SeTrxContainerCtxT **item = (SeTrxContainerCtxT **)DbListItem(&trx->cntrCtxList.list, (uint32_t)listIdx);
        SeTrxContainerCtxT *container = *item;
        if (TrxContainerIsUse(container) && container->ctxHead.type == (uint16_t)TRX_HEAP_HANDLE) {
#ifdef FEATURE_SIMPLEREL
            // V1兼容场景下，存在表已经被删除后回滚，需要保证回滚正常，这里需要对表存在性进行判断
            if (!CataVertexLabelExist(container->ctxHead.id, DbGetInstanceByMemCtx(trx->trxPeriodMemCtx))) {
                TrxHandleContainerCtxResetInfo(container);
                TrxContainerResetDmInfo(trx, container);
            } else {
#endif
                TrxHandleHeapTrxCtx(trx, container, type);
                // list里面本次事务使用过的Heap容器不缓存
                HeapCloseTrxCntrCtx(&container->heapTrxCtx);
                TrxHandleContainerCtxResetInfo(container);
                TrxContainerResetDmInfo(trx, container);
#ifdef FEATURE_SIMPLEREL
                // V1性能需要，保留heap事务性容器的缓存逻辑，V1内存在允许范围
                if (!TrxGetIsAllocNewFlag(trx)) {
                    continue;
                }
            }
#endif
        } else if (TrxContainerIsUse(container) && container->ctxHead.type == (uint16_t)TRX_CLUSTERED_HASH_HANDLE) {
            ChLabelRunCtxReset(container->chTrxCtx.chRunCtx);
            TrxHandleContainerCtxResetInfo(container);
            TrxHandleChTrxCtx(trx, container);
            continue;
        }
        TrxReleaseHandle(container);
        DbDelListItem(&trx->cntrCtxList.list, (uint32_t)listIdx--);
        DbDynMemCtxFree(container->ctxHead.parentMemCtx, container);
    }
}

/**
 * @brief TrxHandleContainerCtxList用于事务执行完提交和回滚undo等操作后，需要对事务容器列表中保存的资源进行最后的处理.
 * 例如Heap容器需要计算是否触发缩容，使用回调函数，设想是不同容器在提交，回滚，以及事务资源回收时有不同操作。
 * @param trx : 入参, trx资源指针
 * @param type : 入参,
 * 表示哪种类型的处理，TRX_CLEAR_HANDLE，TRX_COMMIT_HANDLE，TRX_ROLLBACK_HANDLE分别表示回收清理，提交，回滚
 */
SO_EXPORT_FOR_TS void TrxHandleContainerCtxList(TrxT *trx, TrxHandleTypeE type)
{
    DB_POINTER(trx);
    // 清理本次事务未使用的上下文，关闭本次事务使用的上下文
    for (uint32_t i = 0; i < trx->cntrCtxList.itemCnt; i++) {
        SeTrxContainerCtxT *container = &trx->cntrCtxList.ctxs[i];
        if (TrxContainerIsFree(container)) {
            continue;
        }
        // 非空容器
        if (TrxContainerIsUse(container) && container->ctxHead.type == (uint16_t)TRX_HEAP_HANDLE) {
#ifdef FEATURE_SIMPLEREL
            // V1兼容场景下，存在表已经被删除后回滚，需要保证回滚正常，这里需要对表存在性进行判断
            if (!CataVertexLabelExist(container->ctxHead.id, DbGetInstanceByMemCtx(trx->trxPeriodMemCtx))) {
                TrxContainerResetDmInfo(trx, container);
                TrxReleaseHandle(container);
                container->ctxHead.isFree = true;
            } else {
#endif
                TrxHandleHeapTrxCtx(trx, container, type);
                // 本次事务使用过的Heap容器，不用释放
                HeapCloseTrxCntrCtx(&container->heapTrxCtx);
                TrxContainerResetDmInfo(trx, container);
#ifdef FEATURE_SIMPLEREL
                TrxReleaseHandle(container);
                container->ctxHead.isFree = true;
#else
            // isAllocNew标记为true时，为防止一张表创建多个容器，这里要释放，另外克隆合并场景的事务也不缓存
            if (TrxGetIsAllocNewFlag(trx) || trx->base.cloneType != TRX_ClONE_TYPE_DEFAULT_TRX) {
                TrxReleaseHandle(container);
                container->ctxHead.isFree = true;
            }
#endif
#ifdef FEATURE_SIMPLEREL
            }
#endif
        } else if (TrxContainerIsUse(container) && container->ctxHead.type == (uint16_t)TRX_CLUSTERED_HASH_HANDLE) {
            // 本次事务使用过的clustered hash容器不用释放，由EE层进行释放
            TrxHandleChLabelTrxCtx(trx, container, type);
            if (TrxContainerIsOpen(container)) {
                ChLabelRunCtxReset(container->chTrxCtx.chRunCtx);
            }
            TrxHandleChTrxCtx(trx, container);
        } else {
            TrxContainerResetDmInfo(trx, container);
            TrxReleaseHandle(container);
            container->ctxHead.isFree = true;
        }
        TrxHandleContainerCtxResetInfo(container);
    }

    TrxHandleContainerCtxInList(trx, type);
}

#ifdef FEATURE_SIMPLEREL
SO_EXPORT_FOR_TS void TrxHandleReleaseContainerCtx4V1(TrxT *trx, TrxHandleTypeE type, SeTrxContainerCtxT *container)
{
    DB_POINTER2(trx, container);
    // 非空容器
    if (TrxContainerIsUse(container) && container->ctxHead.type == (uint16_t)TRX_HEAP_HANDLE) {
        // 本次事务使用过的Heap容器，不用释放
        HeapCloseTrxCntrCtx(&container->heapTrxCtx);
        TrxContainerResetDmInfo(trx, container);
        TrxReleaseHandle(container);
        container->ctxHead.isFree = true;
    }
}

SO_EXPORT_FOR_TS StatusInter TrxStoreContainerCtx4V1(
    TrxT *trx, uint32_t containerId, SeTrxContainerCtxT **trxCntrCtx, bool isFixed, bool *isNewContainerOpening)
{
    DB_POINTER3(trx, trxCntrCtx, isNewContainerOpening);

    *isNewContainerOpening = false;
    // 当isAllocNew为true时，直接创建新容器，目前只在V1导入操作中使用
    // 同一个事务里不可多次操作同一张表，否则会创建多个容器
    SeTrxContainerCtxT *seTrxCntrCtx;
    if (SECUREC_LIKELY(!trx->base.isAllocNew)) {
        seTrxCntrCtx = TrxGetContainerCtxById(trx, containerId);
    } else {
        seTrxCntrCtx = NULL;
    }
    if (seTrxCntrCtx != NULL) {
        if (seTrxCntrCtx->ctxHead.isFixed == isFixed) {
            *isNewContainerOpening = !TrxContainerIsUse(seTrxCntrCtx);  // 判断是否第一次标志本事务使用
            *trxCntrCtx = seTrxCntrCtx;
            return STATUS_OK_INTER;
        }
        // 如果当前heap类型发生了改变，则说明发生了空间切换，旧的容器不可再用，释放容器
        TrxHandleReleaseContainerCtx4V1(trx, TRX_CLEAR_HANDLE, seTrxCntrCtx);
        seTrxCntrCtx->ctxHead.id = DB_INVALID_UINT32;
    }
    *isNewContainerOpening = true;
    return TrxStoreContainerCtxToList(trx, containerId, trxCntrCtx);
}
#endif

void TrxSetActiveInitTrx(TrxT *trx, const TrxCfgT *trxCfg)
{
    // 初始化事务资源
    trx->base.readOnly = trxCfg->readOnly;
    trx->base.isLiteTrx = trxCfg->isLiteTrx;
    trx->base.isBackGround = trxCfg->isBackGround;
    trx->base.trxType = trxCfg->trxType;
    trx->base.isInteractive = trxCfg->isInteractive;
    trx->base.isRecovery = false;
    trx->base.recoveryNeedHandleResource = false;
#ifdef FEATURE_GQL
    trx->base.skipRowLockPessimisticRR = trxCfg->skipRowLockPessimisticRR;
#endif  // FEATURE_GQL
    trx->base.needSendSubWhenFailed = false;
    trx->base.startTime = DbRdtsc();
    trx->base.state = TRX_STATE_ACTIVE;
    trx->base.drState = TRX_DIRECT_READ_STATE_READABLE;  // 事务初始化阶段，不会有并发，因此不加原子操作
    trx->base.connId = trxCfg->connId;
    trx->base.splitTime = TRX_DEFAULT_SPILT_TIME;
    trx->base.isTrxForceCommit = trxCfg->isTrxForceCommit;
    trx->base.isRetryTrx = trxCfg->isRetryTrx;
    trx->base.trxCommitCallBackCfg = (TrxCommitCallBackCfgT){0};
    trx->trx.base.isolationLevel = trxCfg->isolationLevel;
    trx->trx.base.savePointId = 0;
    trx->trx.pesTrx.lockAcqInfo.isNewAllocLock = false;
    trx->trx.pesTrx.lockAcqInfo.isHoldLabelXLock = false;
    trx->trx.pesTrx.lockAcqInfo.lastAcqId.lockType = DB_INVALID_UINT8;
    trx->updateStatusMergeListCfg = (UpdateStatusMergeListCfgT){0};
    trx->trx.optTrx.labelReadView.isTrxCommitCheckActive = false;
#ifdef EXPERIMENTAL_GUANGQI
    trx->trx.optTrx.cloneControlInfo.refCount = 0;
    trx->trx.optTrx.cloneControlInfo.cloneControllerShm = DB_INVALID_SHMPTR;
    if (trxCfg->isTrxAllowCloned) {
        trx->trx.optTrx.cloneId = TrxMgrGetAndIncMaxCloneId(trx->trxMgr);
        if (SECUREC_UNLIKELY(trx->trx.optTrx.cloneId == 0)) {  // 防止翻转，0不可用
            trx->trx.optTrx.cloneId = TrxMgrGetAndIncMaxCloneId(trx->trxMgr);
        }
        trx->base.cloneType = TRX_ClONE_TYPE_CLONABLE_TRX;
    } else {
        trx->trx.optTrx.cloneId = 0;
        trx->base.cloneType = TRX_ClONE_TYPE_DEFAULT_TRX;
    }
    trx->trx.optTrx.relationTrxInfo = (RelationTrxInfoT){0};
    trx->trx.optTrx.cloneTrxExtInfo = (CloneTrxExtInfoT){0};
    trx->trx.optTrx.cloneControlInfo.refCount = 0;
    trx->trx.optTrx.cloneControlInfo.cloneControllerShm = DB_INVALID_SHMPTR;
#endif
    DB_ASSERT(trx->trx.pesTrx.holdLockNum == 0);
    DB_ASSERT(trx->trx.pesTrx.holdLockAcqId == SE_LOCK_ACQ_INVALID_ID);
    DB_ASSERT(trx->trx.base.normalUndo == NULL);
    DB_ASSERT(trx->trx.base.retainedUndo == NULL);
}

StatusInter TrxLiteSetActive(TrxT *trx, const TrxCfgT *trxCfg)
{
    trx->base.trxId = TrxMgrGetAndIncMaxTrxId(trx->trxMgr);
    TrxSetActiveInitTrx(trx, trxCfg);
    trx->liteTrx.isUseRsm = false;
    trx->liteTrx.undoLiteRec = &trx->liteTrx.cacheUndoLiteRec;
    UndoLiteRecInit(trx);
    trx->liteTrx.periodMemCtx = trx->trxPeriodMemCtx;
    return STATUS_OK_INTER;
}

StatusInter TrxNonLiteSetActive(TrxT *trx, const TrxCfgT *trxCfg)
{
    StatusInter ret;
    TrxMgrT *trxMgr = (TrxMgrT *)trx->trxMgr;
    TrxIdListT *rwTrxIds = TrxMgrGetRwTrxIds(trxMgr);
    TrxIdListT *roTrxIds = TrxMgrGetRoTrxIds(trxMgr);
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
    ret = DafCreateActionCollection(trx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
#endif
#ifdef EXPERIMENTAL_GUANGQI
    if (trxCfg->isTrxAllowCloned || trxCfg->isCloneTrx) {
        ret = CloneTrxNonListSetActive(trx);
        if (ret != STATUS_OK_INTER) {
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
            DafRollbackActions(trx);
#endif
            return ret;
        }
    } else {
        trx->trxPeriodMemCtx = trx->trxSessionMemCtx;
    }
#endif

    RW_LATCH_WLATCH_RETURN_IF_FAILED(&trxMgr->latch);
    trx->base.trxId = TrxMgrGetAndIncMaxTrxId(trxMgr);

    // 对于读写事务,插入读写事务链表中
    if (!trxCfg->readOnly) {
        ret = TrxIdListPushbackId(rwTrxIds, trx->base.trxId);
    } else {
        ret = TrxIdListPushbackId(roTrxIds, trx->base.trxId);
    }
    if (ret != STATUS_OK_INTER) {
        DbInterProcRWUnlatchW(&trxMgr->latch);
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
        DafRollbackActions(trx);
#endif
        SE_LAST_ERROR(ret, "trxId list is full, push back unsucc.");
        return ret;
    }
    trxMgr->minActiveNormalTrxId = DB_MIN(trx->base.trxId, trxMgr->minActiveNormalTrxId);
    DbInterProcRWUnlatchW(&trxMgr->latch);

    TrxSetActiveInitTrx(trx, trxCfg);
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP, "TrxBegin trxId:%" PRIu64, trx->base.trxId);
    RedoRunCtxSetTrxId(SeGetCurRedoCtx(), trx->base.trxId);
    return STATUS_OK_INTER;
}

StatusInter TrxSetActive(TrxT *trx, const TrxCfgT *trxCfg)
{
    DB_POINTER2(trx, trxCfg);
    if (SECUREC_LIKELY(trxCfg->isLiteTrx)) {
        return TrxLiteSetActive(trx, trxCfg);
    }
    if (SECUREC_LIKELY(!trxCfg->isRetryTrx)) {
        return TrxNonLiteSetActive(trx, trxCfg);
    }
#ifdef FEATURE_YANG
    if (trxCfg->trxType != OPTIMISTIC_TRX || trxCfg->isolationLevel != REPEATABLE_READ) {
        // 重试事务必须是乐观RR的
        SE_ERROR(CONFIG_ERROR_INTER, "RetryTrx trxType:%" PRIu32 ", isolationLevel:%" PRIu32, (uint32_t)trxCfg->trxType,
            (uint32_t)trxCfg->isolationLevel);
        return CONFIG_ERROR_INTER;
    }
    StatusInter ret;
    TrxMgrT *trxMgr = (TrxMgrT *)trx->trxMgr;
    if (SECUREC_UNLIKELY(!trxMgr->optiTrxRetryInfo.isInit)) {
        SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(trxMgr->instanceId);
        if (seInstance == NULL) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get seInstance, id:%" PRIu16, trxMgr->instanceId);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        ret = InitSeOptiTrxRetryCommitInfo(trxMgr, seInstance);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }

    TrxConnStateInfoT info = {0};
    char peerProcInfo[SE_CONN_INFO_MAX_LEN] = {0};
    Status retTem = SeTrxGetConnStateByConnId(trxCfg->connId, &info, NULL);
    if (retTem == GMERR_OK) {  // 如果失败只是DFX功能不全, 不影响主流程
        // peerProcInfo: connId-pid-tid-auditUserInfo(uid-processName)-threadName
        (void)sprintf_s(peerProcInfo, SE_CONN_INFO_MAX_LEN, "%" PRIu16 "-%" PRIu32 "-%" PRIu64 "-%s-%s", info.connId,
            info.pid, info.peerTid, info.auditUserInfo, info.peerThreadName);
    }
    ret = OptiTrxRetryBegin(trx, peerProcInfo, SE_CONN_INFO_MAX_LEN);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    ret = TrxNonLiteSetActive(trx, trxCfg);
    OptiRetryTrxBeginHandle(trx, ret, peerProcInfo, SE_CONN_INFO_MAX_LEN);
    return ret;
#else
    SE_ERROR(FEATURE_NOT_SUPPORTED_INNER, "feat:yang off, isRetryTrx:%" PRIu32, (uint32_t)trxCfg->isRetryTrx);
    return FEATURE_NOT_SUPPORTED_INNER;
#endif
}

inline static StatusInter TrxCfgMatch(const TrxT *trx, const TrxCfgT *trxCfg)
{
    if ((trxCfg->readOnly == trx->base.readOnly) && (trxCfg->isLiteTrx == trx->base.isLiteTrx) &&
        (trxCfg->isBackGround == trx->base.isBackGround) && (trxCfg->trxType == trx->base.trxType) &&
        (trxCfg->isolationLevel == trx->trx.base.isolationLevel)) {
        return STATUS_OK_INTER;
    }
    return CONFIG_ERROR_INTER;
}

StatusInter TrxBegin(TrxT *trx, const TrxCfgT *trxCfg)
{
    DB_POINTER2(trx, trxCfg);
    switch (trx->base.state) {
        case TRX_STATE_NOT_STARTED:
            return TrxSetActive(trx, trxCfg);

        case TRX_STATE_ROLLBACK:
        case TRX_STATE_ABORT:
            (void)TrxRollback(trx);  // 回滚
            return TRANSACTION_ROLLBACK_INTER;

        case TRX_STATE_ACTIVE:
            return TrxCfgMatch(trx, trxCfg);  // 如果事务隔离级别等配置一致，返回OK

            // 暂不支持只读事务与读写事务互相转换
        case TRX_STATE_COMMITTED:
        default:
            break;
    }
    return STATUS_OK_INTER;
}

StatusInter TrxRollback(TrxT *trx)
{
    DB_POINTER(trx);
    StatusInter ret = STATUS_OK_INTER;
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    switch (trx->base.state) {
        case TRX_STATE_NOT_STARTED:
        case TRX_STATE_ROLLBACK:
            return STATUS_OK_INTER;
        case TRX_STATE_FAILURE:
            SE_ERROR(DATABASE_NOT_AVAILABLE_INTER, "trx %" PRIu64 " re-rollback.", trx->base.trxId);
            return DATABASE_NOT_AVAILABLE_INTER;
        case TRX_STATE_ACTIVE:
        case TRX_STATE_ABORT:
            RedoLogBegin(redoCtx);
            ret = TrxUndoSetTrxState(trx, TRX_STATE_ROLLBACK);
            if (ret != STATUS_OK_INTER) {
                (void)RedoLogEnd(redoCtx, false);
                return ret;
            }
            ret = RedoLogEnd(redoCtx, true);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
            DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP, "TrxRollback trxId:%" PRIu64, trx->base.trxId);
            return TrxCommit(trx);
        case TRX_STATE_COMMITTED:
        default:
            return TRANS_MODE_MISMATCH_INTER;
    }
}

StatusInter TrxRollbackOnTimeout(TrxT *trx)
{
    DB_POINTER(trx);
    StatusInter ret = STATUS_OK_INTER;
    // 1.若在超时后进行事务回滚，则修改 TRX_DIRECT_READ_STATE_READING 状态为 TRX_DIRECT_READ_STATE_TIMEOUT_READING
    // 2.或者直连读已经为 TRX_DIRECT_READ_STATE_TIMEOUT_READING 状态，则返回特定Status继续等待该批次读完
    // 3.否则按照正常流程进行回滚
    switch (trx->base.state) {
        case TRX_STATE_NOT_STARTED:
        case TRX_STATE_ROLLBACK:
            return STATUS_OK_INTER;
        case TRX_STATE_FAILURE:
            SE_ERROR(DATABASE_NOT_AVAILABLE_INTER, "trx %" PRIu64 " re-rollback.", trx->base.trxId);
            return DATABASE_NOT_AVAILABLE_INTER;
        case TRX_STATE_ACTIVE:
        case TRX_STATE_ABORT:
            if (DbAtomicGet(&trx->base.drState) == (uint32_t)TRX_DIRECT_READ_STATE_TIMEOUT_READING ||
                DbAtomicBoolCAS(&trx->base.drState, (uint32_t)TRX_DIRECT_READ_STATE_READING,
                    (uint32_t)TRX_DIRECT_READ_STATE_TIMEOUT_READING)) {
                return OBJECT_NOT_IN_PREREQUISITE_STATE_INTER;
            }
            RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
            RedoLogBegin(redoCtx);
            ret = TrxUndoSetTrxState(trx, TRX_STATE_ROLLBACK);
            if (ret != STATUS_OK_INTER) {
                (void)RedoLogEnd(redoCtx, false);
                return ret;
            }
            ret = RedoLogEnd(redoCtx, true);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
            DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP, "TrxRollbackOnTimeout trxId:%" PRIu64, trx->base.trxId);
            return TrxCommit(trx);
        case TRX_STATE_COMMITTED:
        default:
            return TRANS_MODE_MISMATCH_INTER;
    }
}

void SeSetTrxCommitStatusMergeCfg(SeRunCtxHdT seRunCtx, const UpdateStatusMergeListCfgT *cfg)
{
    DB_POINTER2(seRunCtx, cfg);
    SeRunCtxT *runCtx = seRunCtx;
    TrxT *trx = (TrxT *)runCtx->trx;
    trx->updateStatusMergeListCfg = *cfg;
}

inline static void UpdateStatusMergeListCallBack(TrxT *trx)
{
    DB_POINTER(trx);
    UpdateStatusMergeListCfgT *cfg = &(trx->updateStatusMergeListCfg);
    if (cfg->func != NULL && trx->base.isNeedModifyPubsubList) {
        cfg->func(cfg->statusMergeSubDataSet);
    }
}

void SeSetTrxCommitCallBackCfg(SeRunCtxHdT seRunCtx, const TrxCommitCallBackCfgT *cfg)
{
    DB_POINTER2(seRunCtx, cfg);
    SeRunCtxT *runCtx = seRunCtx;
    TrxT *trx = (TrxT *)runCtx->trx;
    trx->base.trxCommitCallBackCfg = *cfg;
}

// 事务提交开始时执行该回调函数, 具体行为由上层定义，当前执行逻辑为超限判断
// 注：1.该函数执行成功后，需要确保提交的后续流程都成功 2.由上层函数加锁保证并发
Status TrxCommitCallBack(TrxT *trx)
{
    DB_POINTER(trx);
    Status ret = GMERR_OK;
    TrxCommitCallBackCfgT *cfg = &(trx->base.trxCommitCallBackCfg);
    if (cfg->func != NULL) {
        ret = cfg->func(cfg->parameter);
    }
    return ret;
}

static inline __attribute__((always_inline)) StatusInter TrxLiteCommit(TrxT *trx)
{
    DB_POINTER(trx);

    // 提交流程需要先执行回调
    StatusInter ret = STATUS_OK_INTER;
    if (trx->base.state == TRX_STATE_ACTIVE) {
        ret = DbGetStatusInterErrno(TrxCommitCallBack(trx));
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "(SE-Transaction) trx lite rollback, trxId: %" PRIu64 ", state: %" PRIu64, trx->base.trxId,
                (uint64_t)trx->base.state);
            // 轻量化事务都是隐式(非交互式)事务，将trx state设置为rollback，内部走回滚流程
            trx->base.state = TRX_STATE_ROLLBACK;
        }
    }

    if (trx->base.state == TRX_STATE_ACTIVE) {
        trx->base.state = TRX_STATE_COMMITTED;
        SHM_CRASHPOINT_ONE(SHM_CRASH_TRX_COMMIT_BEFORE);
        SHM_CRASHPOINT_WITH_CONDITION(SHM_CRASH_BG_TRX_COMMIT_BEFORE, trx->base.isBackGround);
        trx->liteTrx.undoLiteRec->trxCommitFlag = true;
        SHM_CRASHPOINT_ONE(SHM_CRASH_TRX_COMMIT_AFTER);
        SHM_CRASHPOINT_WITH_CONDITION(SHM_CRASH_BG_TRX_COMMIT_AFTER, trx->base.isBackGround);
        DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP, "TrxLiteCommit trxId:%" PRIu64, trx->base.trxId);
        // 需要清理update操作的旧的dst/dir/sub行，其他都不用处理
        // 事务的undo logs申请的内存通过DbMemCtxReset(trx->trxPeriodMemCtx)来释放，在active阶段memset来重置
        // 极限场景下事务的undo logs在逃生通道申请的内存在memCtx还原时保证释放完
        TrxLiteUndoOnCommit(trx);
        // 遍历该trx的容器上下文, 做提交后的处理, 同时清理容器释放事务资源.
        TrxHandleContainerCtxList(trx, TRX_COMMIT_HANDLE);
        TrxResetEscapeMemCtx(trx, true);
        UpdateStatusMergeListCallBack(trx);
    } else {
        // 处理Undo log
        TrxLiteUndoOnRollback(trx);
        TrxHandleContainerCtxList(trx, TRX_ROLLBACK_HANDLE);
        TrxResetEscapeMemCtx(trx, true);
    }

    trx->base.state = TRX_STATE_NOT_STARTED;
    DB_ASSERT(!trx->liteTrx.usedBuf);
    TrxClearKeyDataBuf(trx);  // 此处仅作变量清理，内存释放由trxPeriodMemCtx的reset完成
    DbMemCtxReset((DbMemCtxT *)trx->trxPeriodMemCtx);  // 重置这个内存.（release版本兜底，避免内存泄漏）
    DB_ASSERT(trx->trx.pesTrx.holdLockNum == 0);
    DB_ASSERT(trx->trx.pesTrx.holdLockAcqId == SE_LOCK_ACQ_INVALID_ID);
    DB_ASSERT(trx->trx.base.normalUndo == NULL);
    DB_ASSERT(trx->trx.base.retainedUndo == NULL);
    return ret;
}

// 清空事务容器列表中关于逃生内存的所有句柄以及容器，只能用于事务紧急状态还原，不能用于其他场景
void TrxReleaseContainerLruCache(TrxT *trx)
{
    DB_POINTER(trx);
    while (trx->cntrCtxList.lruCacheHead != NULL) {
        TrxCntrLruCacheRemoveNode(&trx->cntrCtxList);
    }
    DB_ASSERT(trx->cntrCtxList.lruCacheHead == NULL);
    DB_ASSERT(trx->cntrCtxList.lruCacheTail == NULL);
    DB_ASSERT(trx->cntrCtxList.lruCacheItemCnt == 0);
}

void TrxSetEscapeMemCtx(TrxT *trx)
{
    DB_POINTER(trx);
    if (!DbGetUseEscapeFlag()) {
#ifndef NDEBUG
        DB_LOG_INFO_UNFOLD(
            "(SE-Transaction) TrxSetEscapeMemCtx, trxId: %" PRIu64 ", tid: %" PRIu64, trx->base.trxId, trx->tid);
#else
        DB_LOG_INFO(
            "(SE-Transaction) TrxSetEscapeMemCtx, trxId: %" PRIu64 ", tid: %" PRIu64, trx->base.trxId, trx->tid);
#endif
        // 1. 释放事务上的KeyDataBuf缓存
        TrxFreeKeyDataBuf(trx);
        // 2. 开启使用逃生memCtx申请内存的开关
        DbSetUseEscapeFlag(true);
        // 3. 开启使用事务性容器LRU缓存
        trx->cntrCtxList.enableLruCache = true;
    } else {
        // 切换逃生通道后回滚或者提交操作应该执行成功，不能重复替换
        // 此处不使用断言拦截
        DB_LOG_WARN(GMERR_INTERNAL_ERROR, "(UNDO Limit-Situation) TrxSetEscapeMemCtx again. (trxId: %" PRIu64 ")",
            trx->base.trxId);
    }
}

void TrxResetEscapeMemCtx(TrxT *trx, bool ignore)
{
    DB_POINTER(trx);
    // purger通过入参控制是否需要restore逃生通道内存
    if (trx->base.isPurger && ignore) {
        return;
    }
    if (DbGetUseEscapeFlag()) {
        // 1. 清空 list 句柄中关于逃生内存的所有内容，关闭使用事务性容器LRU缓存
        TrxReleaseContainerLruCache(trx);
        trx->cntrCtxList.enableLruCache = false;
        // 2. 释放事务上的KeyDataBuf缓存
        TrxFreeKeyDataBuf(trx);
        // 3. 清理逃生memCtx，关闭使用逃生memCtx申请内存的开关
#ifndef NDEBUG
        DB_LOG_INFO_UNFOLD(
            "(SE-Transaction) TrxResetEscapeMemCtx, trxId: %" PRIu64 ", tid: %" PRIu64 "", trx->base.trxId, trx->tid);
#else
        DB_LOG_INFO(
            "(SE-Transaction) TrxResetEscapeMemCtx, trxId: %" PRIu64 ", tid: %" PRIu64 "", trx->base.trxId, trx->tid);
#endif
        DbSetUseEscapeFlag(false);
    }
}

StatusInter TrxClearOnCommit(TrxT *trx)
{
    /* 提交流程 */
    // 清理事务的undo logs，后续如果要支持Repatable Read级别还需要调整这里的逻辑
    StatusInter ret = TrxUndoOnCommit((SeUndoCtxT *)trx->seRunCtx->undoCtx, trx);
    TrxResetEscapeMemCtx(trx, true);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
    // 清理Daf操作
    DafCommit(trx);
#endif
    // 遍历该trx的容器上下文, 做提交后的处理, 同时清理容器释放事务资源.
    TrxHandleContainerCtxList(trx, TRX_COMMIT_HANDLE);
    if (TrxGetIsolationLevel(trx) == REPEATABLE_READ) {
        // 需要处理事务性容器结束后，才能触发purger回收旧版本
        // 否则purger可能真正物理删除，使得deleteByte先于writeByte增加，不利于维护
        if (trx->trx.base.retainedUndo != NULL) {
            TrxUndoLogT *undo = trx->trx.base.retainedUndo;
            DB_ASSERT(undo->recCnt != 0);
            ret = TrxUndoRetainedSetPurgeHandleFlag((SeUndoCtxT *)trx->seRunCtx->undoCtx, undo);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
            TrxUndoRetainedCacheOrPurge((SeUndoCtxT *)trx->seRunCtx->undoCtx, trx, undo);
        }
    }
    // 事务提交成功，执行回调
    UpdateStatusMergeListCallBack(trx);
    return STATUS_OK_INTER;
}

StatusInter TrxClearOnRollback(TrxT *trx)
{
    TrxMgrT *trxMgr = (TrxMgrT *)trx->trxMgr;
    /* 回滚或检验后失败回滚流程 */
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
    DafRollbackActions(trx);
#endif
    StatusInter ret = TrxUndoOnRollback((SeUndoCtxT *)trx->seRunCtx->undoCtx, trx);
    if (ret != STATUS_OK_INTER) {
        TrxResetEscapeMemCtx(trx, true);
        return ret;
    }
    RW_LATCH_WLATCH_RETURN_IF_FAILED(&trxMgr->latch);
    // 回滚结束后，才能在全局trxMgr里commit该TrxId，否则其他事务可能会看见该事务的修改
    // 该事物的commitTs在之前已经刷新，但是不影响，回滚流程不会使用到
    TrxCommitInTrxList(trx);
    DbInterProcRWUnlatchW(&trxMgr->latch);
    // 遍历容器上下文，做回滚后处理, 同时清理容器释放事务资源.
    TrxHandleContainerCtxList(trx, TRX_ROLLBACK_HANDLE);
    TrxResetEscapeMemCtx(trx, true);
    return STATUS_OK_INTER;
}

StatusInter TrxClearOnCommitOrRollback(TrxT *trx)
{
    DB_POINTER(trx);
    if (TrxIsMemoryLimit()) {
        TrxSetEscapeMemCtx(trx);
    }
    StatusInter ret = STATUS_OK_INTER;
    if (trx->base.state == TRX_STATE_COMMITTED) {
        ret = TrxClearOnCommit(trx);
    } else {
        ret = TrxClearOnRollback(trx);
    }
    // 检测readView是否已释放
    ReadViewClose(trx);

    TrxClearKeyDataBuf(trx);  // 此处仅作变量清理，内存释放由trxPeriodMemCtx的reset完成
    DbMemCtxReset((DbMemCtxT *)trx->trxPeriodMemCtx);  // 重置这个内存.
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    if (!trx->base.isPurger) {
        SePersistUnRegisterDataOp(trx->seRunCtx, !trx->base.readOnly);
    }
    SePersistUnRegisterDDLOp(trx->seRunCtx);
    trx->base.state = TRX_STATE_NOT_STARTED;
    return STATUS_OK_INTER;
}

StatusInter TrxUndoSetTrxState(TrxT *trx, TrxStateE state)
{
    SeUndoCtxT *undoCtx = (SeUndoCtxT *)trx->seRunCtx->undoCtx;
    IsolationLevelE isolationLevel = TrxGetIsolationLevel(trx);
    UndoStateE undoState = state == TRX_STATE_COMMITTED ? TRX_UNDO_COMMIT : TRX_UNDO_ROLLBACK;
    // RR隔离级别可能同时拥有normalUndo和retainedUndo，需要将state设置操作作为一个原子操作保证持久化数据一致性
    StatusInter ret;
    if (trx->trx.base.normalUndo != NULL) {
        ret = TrxUndoCommitOrRollbackStart(undoCtx, trx->trx.base.normalUndo, undoState, isolationLevel);
        if (ret != STATUS_OK_INTER && SeGetStorageStatus(trx->seRunCtx->seIns) == SE_ON_DISK_EMRGNCY) {
            return ret;
        }
    }
    if (trx->trx.base.retainedUndo != NULL) {
        ret = TrxUndoCommitOrRollbackStart(undoCtx, trx->trx.base.retainedUndo, undoState, isolationLevel);
        if (ret != STATUS_OK_INTER && SeGetStorageStatus(trx->seRunCtx->seIns) == SE_ON_DISK_EMRGNCY) {
            return ret;
        }
    }
    trx->base.state = state;
    return STATUS_OK_INTER;
}

StatusInter TrxUndoRetainedAddToHistory(TrxT *trx, TrxUndoLogT *undo)
{
    // 不为NULL且不为0的情况才加入历史链表，savePoint引入了回滚到头的场景，此时没东西需要回收
    if (trx->trx.base.retainedUndo == NULL || trx->trx.base.retainedUndo->recCnt == 0) {
        return STATUS_OK_INTER;
    }
    StatusInter ret = TrxUndoAddToHistory(trx, undo);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "add to history list unsucc trxId: %" PRIu64 ", undo:(type: %" PRIu8 ", trxId: %" PRIu64 ")",
            trx->base.trxId, (uint8_t)(undo->type), undo->trxId);
        DB_ASSERT(false);
    }
    return ret;
}

StatusInter TrxCommit(TrxT *trx)
{
    DB_POINTER(trx);
    // 逃生内存使用看护:提交开始时事务不能为占用逃生通道状态，purger除外，因为purger在提交前就可能会访问逃生通道内存
#ifndef NDEBUG
    if (!trx->base.isPurger) {
        DB_ASSERT(!DbGetUseEscapeFlag());
    }
#endif
    if (SECUREC_UNLIKELY(trx->base.state == TRX_STATE_NOT_STARTED)) {
        return STATUS_OK_INTER;
    }
    if (SECUREC_UNLIKELY(trx->base.state == TRX_STATE_FAILURE)) {
        SE_ERROR(DATABASE_NOT_AVAILABLE_INTER, "trx %" PRIu64 " re-commit.", trx->base.trxId);
        return DATABASE_NOT_AVAILABLE_INTER;
    }
    DB_ASSERT(trx->base.keyData == NULL && trx->base.keyLen == 0);  // 事务提交/回滚进行前，这块buf还未分配
    if (SECUREC_LIKELY(TrxIsLiteTrx(trx))) {
        return TrxLiteCommit(trx);
    }

    StatusInter ret = STATUS_OK_INTER;
    if (TrxGetTrxType(trx) == OPTIMISTIC_TRX) {
        ret = OptimisticTrxCommit(trx);
    } else {
        ret = PessimisticTrxCommit(trx);
    }
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    // 逃生内存使用看护:提交开始时事务不能为占用逃生通道状态，purger除外，因为purger在提交前就可能会访问逃生通道内存
#ifndef NDEBUG
    if (!trx->base.isPurger) {
        DB_ASSERT(!DbGetUseEscapeFlag());
    }
#endif
    if (trx->base.readOnly) {
        return STATUS_OK_INTER;
    }
    return RedoFlushOnTrxCommit(((SeInstanceT *)trx->seRunCtx->seIns)->redoMgr);
}

void TrxAbort(TrxT *trx)
{
    DB_POINTER(trx);
    if (trx->base.state == TRX_STATE_NOT_STARTED) {
        return;
    }
    DB_LOG_INFO("switch abr status type");
    trx->base.state = TRX_STATE_ABORT;
}

Handle TrxGetResPoolHandle(TrxT *trx, uint32_t resId)
{
    DB_POINTER(trx);
    SeTrxContainerCtxT *item = (SeTrxContainerCtxT *)TrxGetContainerCtxById(trx, resId);
    if (item == NULL) {
        return NULL;
    }
    DB_ASSERT(item->trxCntrCtx.ctxHead.type == (uint16_t)TRX_RES_POOL_HANDLE);
    // 句柄空间申请提前，因此需要判断不为空
    DB_ASSERT(item->trxCntrCtx.cntrRunHdl != NULL);

    return item->trxCntrCtx.cntrRunHdl;
}

SeTrxContainerCtxT *TrxGetHashHandleContainerCtxById(TrxT *trx, uint32_t containerId)
{
    DB_POINTER(trx);
    SeTrxContainerCtxT *container = TrxGetContainerCtxById(trx, containerId);
    if (container != NULL) {
        return container;
    }
    if (trx->cntrCtxList.enableLruCache) {
        return TrxGetCntrCtxByIdUseLruCache(&trx->cntrCtxList, containerId);
    }
    return NULL;
}

StatusInter TrxStoreHashHandleContainerCtx(
    TrxT *trx, uint32_t containerId, SeTrxContainerCtxT **trxCntrCtx, bool *isNewContainerOpening)
{
    DB_POINTER3(trx, trxCntrCtx, isNewContainerOpening);
    *isNewContainerOpening = false;
    SeTrxContainerCtxT *seTrxCntrCtx = TrxGetHashHandleContainerCtxById(trx, containerId);
    if (seTrxCntrCtx != NULL) {
        *isNewContainerOpening = !TrxContainerIsUse(seTrxCntrCtx);
        *trxCntrCtx = seTrxCntrCtx;
        return STATUS_OK_INTER;
    }
    *isNewContainerOpening = true;
    if (trx->cntrCtxList.enableLruCache) {
        return TrxCntrLruCacheAddNode(trx, containerId, trxCntrCtx);
    }
    return TrxStoreContainerCtxToList(trx, containerId, trxCntrCtx);
}

StatusInter TrxGetHashHandleStoreLabel(
    TrxT *trx, uint8_t *label, const HeapDmIndexIterT *indexIter, IndexCtxHd indexHdl)
{
    TrxContainerHdlTypeE handleType = TRX_INVALID_CONTAINER_TYPE;
    switch (indexIter->indexType) {
        case NODE_MEMBER_INDEX:
            break;
        default:
            handleType = TRX_INDEX_HANDLE;
            break;
    }
    SeTrxContainerCtxT *trxCntrCtx = NULL;
    bool isNewCtx = false;
    StatusInter ret = TrxStoreHashHandleContainerCtx(trx, indexIter->dmUuid, &trxCntrCtx, &isNewCtx);
    DB_UNUSED(isNewCtx);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "trxSlot(%" PRIu16 ") store label(%" PRIu32 ") unsucc", trx->base.trxSlot, indexIter->dmUuid);
        return ret;
    }
    trxCntrCtx->trxCntrCtx.ctxHead.type = (uint16_t)handleType;
    trxCntrCtx->trxCntrCtx.ctxHead.isOpened = true;
    trxCntrCtx->trxCntrCtx.label = (void *)label;
    trxCntrCtx->trxCntrCtx.containerShmAddr = indexIter->idxShmAddr;
    trxCntrCtx->trxCntrCtx.cntrRunHdl = indexHdl;
    TrxSetContainerIsUsed(trxCntrCtx);
    return STATUS_OK_INTER;
}

StatusInter TrxGetHashHandleWithAlloc(
    TrxT *trx, const HeapDmIndexIterT *indexIter, void *dmInfo, IndexOpenCfgT *idxOpenCfg, IndexCtxT **idxCtx)
{
    DB_POINTER5(trx, indexIter, dmInfo, idxOpenCfg, idxCtx);
    IndexCtxT *idx = NULL;
    Status retStatus = IdxAlloc(trx->seRunCtx, indexIter->indexType, &idx);
    if (retStatus != GMERR_OK) {
        return DbGetStatusInterErrno(retStatus);
    }
    retStatus = IdxOpen(indexIter->idxShmAddr, idxOpenCfg, idx);
    if (retStatus != GMERR_OK) {
        IdxRelease(idx);
        SE_ERROR(
            DbGetStatusInterErrno(retStatus), "open index ctx unsucc, index type: %" PRIu32 "", indexIter->indexType);
        return DbGetStatusInterErrno(retStatus);
    }

    TupleBufInit(&idx->tupleBuf, trx->trxPeriodMemCtx);
    StatusInter ret = TrxGetHashHandleStoreLabel(trx, dmInfo, indexIter, idx);
    if (ret != STATUS_OK_INTER) {
        IdxRelease(idx);
        return ret;
    }
    *idxCtx = idx;
    return STATUS_OK_INTER;
}

StatusInter TrxGetHashHandle(
    TrxT *trx, SeContainerHdl containerHdl, bool isChLabel, const HeapDmIndexIterT *indexIter, IndexCtxT **idxCtx)
{
    DB_POINTER4(trx, containerHdl.hpRunHdl, indexIter, idxCtx);
    SeTrxContainerCtxT *hashItem = (SeTrxContainerCtxT *)TrxGetHashHandleContainerCtxById(trx, indexIter->dmUuid);
    if (hashItem != NULL) {
        *idxCtx = hashItem->trxCntrCtx.cntrRunHdl;
        DB_ASSERT(hashItem->trxCntrCtx.cntrRunHdl);
        return STATUS_OK_INTER;
    }
    void *dmInfo = NULL;
    IndexOpenCfgT idxOpenCfg = {0};
    idxOpenCfg.seRunCtx = trx->seRunCtx;
    idxOpenCfg.indexLabel = indexIter->dmIndex;
    idxOpenCfg.needCheckIndexSatisfied = indexIter->needCheckIndexSatisfied;
    idxOpenCfg.indexType = indexIter->indexType;
    if (isChLabel) {
        ChLabelDmDetailT chDmDetail = ClusteredHashGetDmDetailInfo(containerHdl.chRunHdl);
        SeTrxContainerCtxT *chLabelItem = TrxGetContainerCtxById(trx, chDmDetail.labelId);
        if (chLabelItem == NULL || chDmDetail.dmInfo == NULL) {
            return INTERNAL_ERROR_INTER;
        }
        dmInfo = chDmDetail.dmInfo;
        idxOpenCfg.chHandle = containerHdl.chRunHdl;
        idxOpenCfg.vertex = chLabelItem->chTrxCtx.vertex;
        idxOpenCfg.vertexLabel = ChTrxGetVertexLabel(&chLabelItem->chTrxCtx);
        idxOpenCfg.callbackFunc.keyCmp = chLabelItem->chTrxCtx.secIndexkeyCmp;
        idxOpenCfg.callbackFunc.addrCheck = chLabelItem->chTrxCtx.addrCheck;
        idxOpenCfg.callbackFunc.addrCheckAndFetch = chLabelItem->chTrxCtx.addrCheckAndFetch;
    } else {
        HeapDmDetailT *dmDetail = HeapAmGetDmDetailInfo(containerHdl.hpRunHdl);
        SeTrxContainerCtxT *heapItem = (SeTrxContainerCtxT *)TrxGetContainerCtxById(trx, dmDetail->dmUuid);
        if (heapItem == NULL || dmDetail->dmInfo == NULL) {
            return INTERNAL_ERROR_INTER;
        }
        dmInfo = dmDetail->dmInfo;
        idxOpenCfg.heapHandle = containerHdl.hpRunHdl;
        idxOpenCfg.vertex = heapItem->heapTrxCtx.vertex;
        idxOpenCfg.vertexLabel = HeapTrxGetVertexLabel(&heapItem->heapTrxCtx);
        idxOpenCfg.callbackFunc.keyCmp = heapItem->heapTrxCtx.hashCompare;
        idxOpenCfg.callbackFunc.addrCheck = heapItem->heapTrxCtx.addrCheck;
        idxOpenCfg.callbackFunc.addrCheckAndFetch = heapItem->heapTrxCtx.addrCheckAndFetch;
        idxOpenCfg.callbackFunc.exprValuesEval = heapItem->heapTrxCtx.exprValuesEval;
    }
    return TrxGetHashHandleWithAlloc(trx, indexIter, dmInfo, &idxOpenCfg, idxCtx);
}

Status SeTransAssignReadView(SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);
    SeRunCtxT *seRunCtxPtr = seRunCtx;
    if (SECUREC_UNLIKELY(!seRunCtxPtr->trx)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "trx is null");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    StatusInter ret = ReadViewPrepare(seRunCtxPtr->trx);
    return DbGetExternalErrno(ret);
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
void SeTransGetCfg(SeRunCtxHdT seRunCtx, TrxCfgT *cfg)
{
    DB_POINTER2(seRunCtx, cfg);
    SeRunCtxT *seRunCtxPtr = seRunCtx;
    if (!seRunCtxPtr->trx) {
        cfg->trxType = PESSIMISTIC_TRX;
        cfg->isolationLevel = READ_COMMITTED;
        cfg->readOnly = false;
        cfg->isLiteTrx = false;
        cfg->isBackGround = false;
        cfg->isTrxForceCommit = false;
        cfg->isRetryTrx = false;
        return;
    }
    cfg->trxType = ((TrxT *)seRunCtxPtr->trx)->base.trxType;
    cfg->isolationLevel = ((TrxT *)seRunCtxPtr->trx)->trx.base.isolationLevel;
    cfg->readOnly = ((TrxT *)seRunCtxPtr->trx)->base.readOnly;
    cfg->isLiteTrx = ((TrxT *)seRunCtxPtr->trx)->base.isLiteTrx;
    cfg->isBackGround = ((TrxT *)seRunCtxPtr->trx)->base.isBackGround;
    cfg->isTrxForceCommit = ((TrxT *)seRunCtxPtr->trx)->base.isTrxForceCommit;
    cfg->isRetryTrx = ((TrxT *)seRunCtxPtr->trx)->base.isRetryTrx;
}

static StatusInter SeTransWait(MultiTrxControlT *embedTrxMgr, uint32_t *sleepCnt, DbSpinlockFnT *seLockFn)
{
    if (embedTrxMgr == NULL) {
        return STATUS_OK_INTER;
    }
    DbSpinUnlock(&embedTrxMgr->lock);
    DbUsleep(SLEEP_MICROSEC_ONE_TIME);
    ++(*sleepCnt);
    if (*sleepCnt == MAX_WAIT_TRX_BEGIN_TIME) {
        SE_ERROR(INSUFF_RES_INTER, "wrong transaction-start: long wait time");
        DbSpinLock(&embedTrxMgr->lock);
        return INSUFF_RES_INTER;
    }
    DbSpinLock(&embedTrxMgr->lock);
    return STATUS_OK_INTER;
}

static StatusInter SeTryReserveBuffPoolPages(
    SeInstanceT *seIns, MultiTrxControlT *embedTrxMgr, uint32_t *sleepCnt, DbSpinlockFnT *seLockFn)
{
    if (embedTrxMgr == NULL) {
        return STATUS_OK_INTER;
    }
    StatusInter innerRet = STATUS_OK_INTER;
    while (embedTrxMgr->curPageUsage + TrxGetPageReserve(seIns) > embedTrxMgr->maxPageLimit &&
           *sleepCnt < MAX_WAIT_TRX_BEGIN_TIME && innerRet == STATUS_OK_INTER) {
        innerRet = SeTransWait(embedTrxMgr, sleepCnt, seLockFn);
    }
    if (innerRet == STATUS_OK_INTER) {
        embedTrxMgr->curPageUsage += TrxGetPageReserve(seIns);
    }
    return innerRet;
}

Status SeReserveBuffPoolPages(SeRunCtxHdT seRunCtx)
{
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    TrxMgrT *trxMgr = (TrxMgrT *)DbShmPtrToAddr(seIns->trxMgrShm);
    if (SECUREC_UNLIKELY(trxMgr == NULL)) {
        SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "Unsucc to convert shmPtr to pointer.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    MultiTrxControlT *embedTrxMgr = (MultiTrxControlT *)DbDynShmemPtrToAddr(trxMgr->multiTrxMgrShm);
    if (embedTrxMgr == NULL) {
        return GMERR_OK;
    }
    DbSpinLock(&embedTrxMgr->lock);
    uint32_t sleepCnt = 1;
    StatusInter innerRet = SeTryReserveBuffPoolPages(seIns, embedTrxMgr, &sleepCnt, &seIns->seLockFn);
    if (innerRet != STATUS_OK_INTER) {
        SE_ERROR(innerRet, "Reserve buff pool pages go wrong");
    }
    DbSpinUnlock(&embedTrxMgr->lock);
    return DbGetExternalErrno(innerRet);
}

Status SeReleaseBuffPoolPages(SeRunCtxHdT seRunCtx)
{
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    TrxMgrT *trxMgr = (TrxMgrT *)DbShmPtrToAddr(seIns->trxMgrShm);
    if (SECUREC_UNLIKELY(trxMgr == NULL)) {
        SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "Unsucc to convert shmPtr to pointer.");
        return DbGetExternalErrno(MEMORY_OPERATE_FAILED_INTER);
    }
    MultiTrxControlT *embedTrxMgr = (MultiTrxControlT *)DbDynShmemPtrToAddr(trxMgr->multiTrxMgrShm);
    if (embedTrxMgr == NULL) {
        return GMERR_OK;
    }
    DB_ASSERT(embedTrxMgr->curPageUsage >= TrxGetPageReserve(seIns));  // Otherwise no need to release pages
    DbSpinLock(&embedTrxMgr->lock);
    embedTrxMgr->curPageUsage -= TrxGetPageReserve(seIns);
    DbSpinUnlock(&embedTrxMgr->lock);
    return GMERR_OK;
}
#endif /* FEATURE_SIMPLEREL */

Status SeTransBegin(SeRunCtxHdT seRunCtx, const TrxCfgT *cfg)
{
    DB_POINTER(seRunCtx);
    TrxT *trx = seRunCtx->trx;
    // 此处不要加SECUREC_UNLIKELY预测，会影响CI构建上的直连读用例性能（仅CI机器上有问题，猜测可能是指令布局发生了变化）
    if (seRunCtx->isPersistence) {
        // 如果已经锁库，则不能开启事务
        if (SeGetStorageStatus(seRunCtx->seIns) == SE_ON_DISK_EMRGNCY) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_DATABASE_NOT_AVAILABLE, "Begin trx while db disk emergency or swapping dir.");
            return GMERR_DATABASE_NOT_AVAILABLE;
        }
        if (trx->base.state == TRX_STATE_NOT_STARTED) {
            // 非只读模式才需要上锁
            Status ret = DbGetExternalErrno(SePersistRegisterDataOp(seRunCtx, cfg == NULL || !cfg->readOnly));
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        SeSetCurRedoCtx(seRunCtx->redoCtx);
        SeResetTrxOnNeed(seRunCtx, trx);
    }
    StatusInter ret = STATUS_OK_INTER;
    if (SECUREC_LIKELY(cfg != NULL)) {
        ret = TrxBegin(trx, cfg);
    } else {
        /* 不进行配置则默认使用读已提交隔离级别，非只读模式 */
        TrxCfgT trxCfg = {
            .readOnly = false,
            .isLiteTrx = false,
            .isBackGround = false,
            .isInteractive = false,
            .isTrxForceCommit = false,
            .isRetryTrx = false,
#ifdef FEATURE_GQL
            .skipRowLockPessimisticRR = false,
#endif  // FEATURE_GQL
            .connId = DB_INVALID_UINT16,
            .trxType = PESSIMISTIC_TRX,
            .isolationLevel = READ_COMMITTED,
        };
        ret = TrxBegin(trx, &trxCfg);
    }
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "unable to begin trx");
        // trx begin 报错时，state还没有修改
        if (trx->base.state == TRX_STATE_NOT_STARTED) {
            SePersistUnRegisterDataOp(seRunCtx, cfg == NULL || !cfg->readOnly);
        }
    }
    return DbGetExternalErrno(ret);
}

Status SeTransRollback(SeRunCtxHdT seRunCtx, bool isTimeout)
{
    DB_POINTER(seRunCtx);

    SeRunCtxT *runCtx = seRunCtx;
    if (seRunCtx->isPersistence) {
        SeSetCurRedoCtx(seRunCtx->redoCtx);
    }
    DbMemSetThreadUnlimited();  // 为了保证事务回滚完整性，动态内存的申请不受系统限制
    StatusInter ret = STATUS_OK_INTER;
    if (isTimeout) {
        ret = TrxRollbackOnTimeout((TrxT *)runCtx->trx);
    } else {
        ret = TrxRollback((TrxT *)runCtx->trx);
    }
    DbMemSetThreadLimited();  // 恢复系统限制
    if (seRunCtx->isPersistence) {
        SeSetCurRedoCtx(NULL);
    }

    // 磁盘故障错误时，存储的所有DML操作都返回失败。
    // 最后EE走到事务回滚逻辑，回滚内部忽略错误、记录日志，对EE返回OK
    TrxT *trx = (TrxT *)seRunCtx->trx;
    if (SECUREC_UNLIKELY(SeGetStorageStatus(seRunCtx->seIns) == SE_ON_DISK_EMRGNCY &&
                         trx->base.state != TRX_STATE_NOT_STARTED && trx->base.state != TRX_STATE_FAILURE)) {
        trx->base.state = TRX_STATE_FAILURE;
        SePersistUnRegisterDataOp(seRunCtx, !trx->base.readOnly);
        SePersistUnRegisterDDLOp(trx->seRunCtx);
        SE_ERROR(ret, "unable to rollback while db disk emergency");
        return GMERR_OK;
    }
    return DbGetExternalErrno(ret);
}

Status SeTransCommit(SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);
    DbMemSetThreadUnlimited();  // 为了保证事务提交完整性，动态内存的申请不受系统限制
    SeRunCtxT *runCtx = seRunCtx;
    if (SECUREC_UNLIKELY(seRunCtx->isPersistence)) {
        SeSetCurRedoCtx(seRunCtx->redoCtx);
    }
    StatusInter ret = TrxCommit((TrxT *)runCtx->trx);
    DbMemSetThreadLimited();  // 恢复系统限制
    if (SECUREC_UNLIKELY(seRunCtx->isPersistence)) {
        SeSetCurRedoCtx(NULL);
    }
    TrxT *trx = (TrxT *)seRunCtx->trx;
    if (SECUREC_UNLIKELY(SeGetStorageStatus(seRunCtx->seIns) == SE_ON_DISK_EMRGNCY &&
                         trx->base.state != TRX_STATE_NOT_STARTED && trx->base.state != TRX_STATE_FAILURE)) {
        trx->base.state = TRX_STATE_FAILURE;
        SePersistUnRegisterDataOp(seRunCtx, !trx->base.readOnly);
        SePersistUnRegisterDDLOp(trx->seRunCtx);
        SE_ERROR(ret, "unable to commit while db disk emergency");
    }
    return DbGetExternalErrno(ret);
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
Status SeTransRecovery(SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);
    SeRunCtxT *runCtx = seRunCtx;
    DB_ASSERT(((TrxT *)runCtx->trx)->base.isLiteTrx);  // 当前只支持轻量化事务
    StatusInter ret = TrxLiteRecovery((TrxT *)runCtx->trx);
    return DbGetExternalErrno(ret);
}

void SeTransAbort(SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);

    SeRunCtxT *runCtx = seRunCtx;
    TrxAbort((TrxT *)runCtx->trx);
    SeSetCurRedoCtx(NULL);
}

NO_INLINE Status SeTransStartDirectRead(SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);
    // 直连读要涉及事务的话，限制为交互式事务
    if (SeTransGetState(seRunCtx) != TRX_STATE_ACTIVE || !((TrxT *)seRunCtx->trx)->base.isInteractive) {
        DB_LOG_WARN(GMERR_NO_ACTIVE_TRANSACTION,
            "(SE-Transaction) SeTransStartDirectRead: Not active trx. (trxId: %" PRIu64 ")",
            ((TrxT *)seRunCtx->trx)->base.trxId);
        return GMERR_NO_ACTIVE_TRANSACTION;
    }

    // 1.每次扫描分片前修改 TRX_DIRECT_READ_STATE_READABLE 状态为 TRX_DIRECT_READ_STATE_READING
    // 2.否则如果直连读已经是其他状态或者事务本身状态为非active则报错
    if (!DbAtomicBoolCAS(&((TrxT *)seRunCtx->trx)->base.drState, (uint32_t)TRX_DIRECT_READ_STATE_READABLE,
            (uint32_t)TRX_DIRECT_READ_STATE_READING)) {
        return GMERR_TRANS_MODE_MISMATCH;
    }

    // 由于事务状态没有锁保护，采用double check
    if (SeTransGetState(seRunCtx) != TRX_STATE_ACTIVE || !((TrxT *)seRunCtx->trx)->base.isInteractive) {
        DB_LOG_WARN(GMERR_NO_ACTIVE_TRANSACTION,
            "(SE-Transaction) SeTransStartDirectRead: Not active trx. (trxId: %" PRIu64 ")",
            ((TrxT *)seRunCtx->trx)->base.trxId);
        return GMERR_NO_ACTIVE_TRANSACTION;
    }
    return GMERR_OK;
}

NO_INLINE void SeTransEndDirectRead(SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);

    // 1.每次扫描分片后修改 TRX_DIRECT_READ_STATE_READING 状态为 TRX_DIRECT_READ_STATE_READABLE
    // 2.若已经超时，则修改 TRX_DIRECT_READ_STATE_TIMEOUT_READING 状态为 TRX_DIRECT_READ_STATE_UNREADABLE
    // 3.否则如果直连读已经是其他状态则ASSERT报错
    if (!DbAtomicBoolCAS(&((TrxT *)seRunCtx->trx)->base.drState, (uint32_t)TRX_DIRECT_READ_STATE_READING,
            (uint32_t)TRX_DIRECT_READ_STATE_READABLE)) {
        if (!DbAtomicBoolCAS(&((TrxT *)seRunCtx->trx)->base.drState, (uint32_t)TRX_DIRECT_READ_STATE_TIMEOUT_READING,
                (uint32_t)TRX_DIRECT_READ_STATE_UNREADABLE)) {
            // 当前事务是连接级别的，同个连接的多个stmt也不允许并发操作
            // 因此在直连读的end阶段理论上不会出现状态转换错误的情况
            DB_ASSERT(false);
        }
    }
}
#endif /* FEATURE_SIMPLEREL */

void SeTransSetDmlHint4BatchNum(SeRunCtxHdT seRunCtx, uint32_t batchNum)
{
    SeTransSetDmlUndoLiteSize(seRunCtx, batchNum, false);
}

void SeTransSetDmlHint4RangeUpdate(SeRunCtxHdT seRunCtx)
{
    SeTransSetDmlUndoLiteSize(seRunCtx, TRX_LITE_UNDO_EXTEND_STEP, true);
}

void SeTransSetDmlHint4BatchExtendNum(SeRunCtxHdT seRunCtx, uint32_t batchNum)
{
    SeTransSetDmlUndoLiteExtendSize(seRunCtx, batchNum);
}

void SeTransSetRecoveryFlag(SeRunCtxHdT seRunCtx)
{
    SeRunCtxT *runCtx = seRunCtx;
    TrxT *trx = (TrxT *)runCtx->trx;
    trx->base.isRecovery = true;
}

void SeTransSetRsmUndo(const SeRunCtxHdT seRunCtx, RsmUndoRecordT *rsmUndoRec)
{
    DB_POINTER2(seRunCtx, rsmUndoRec);
    TrxT *trx = (TrxT *)seRunCtx->trx;
    DB_ASSERT(TrxIsLiteTrx(trx));
    trx->liteTrx.rsmUndoRec = rsmUndoRec;
}

TrxT *TrxPoolGetTrxBySlot(TrxPoolT *trxPool, uint16_t slot)
{
    if (slot >= trxPool->trxPoolNodeNum) {
        return NULL;
    }
    uint64_t shmSlot = slot / TRX_NUM_PER_BLOCK;
    ShmemPtrT *trxs = TrxPoolGetTrxs(trxPool);
    TrxT *trxsGroup = (TrxT *)DbShmPtrToAddr(trxs[shmSlot]);
    if (trxsGroup == NULL) {
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "trxs group is NULL (segid: %" PRIu32 " offset: %" PRIu32 ")",
            trxs[shmSlot].segId, trxs[shmSlot].offset);
        return NULL;
    }
    // 这个槽可能已经被释放，这个通过其承载的原来事务的状态来判断
    TrxT *trx = &trxsGroup[slot % TRX_NUM_PER_BLOCK];
    // 此处需要使用局部变量获取state，不能直接断言一个可能被修改状态
    // DB_ASSERT不是原子操作，每次判断时条件都可能被修改，最后判断COMMITTED状态的时候正好被修改为了NOT_STARTED，那么就会断言失败
    TrxStateE state = trx->base.state;
    // 此处依赖trx结构体内状态进行判断，需要该成员确保完成初始化
    DB_ASSERT(state == TRX_STATE_NOT_STARTED || state == TRX_STATE_ROLLBACK || state == TRX_STATE_ACTIVE ||
              state == TRX_STATE_ABORT || state == TRX_STATE_COMMITTED || state == TRX_STATE_FAILURE);
    return trx;
}

bool TrxIsGetLabelXLock(TrxT *trx, uint32_t labelId)
{
    if (!trx->trx.pesTrx.lockAcqInfo.isHoldLabelXLock) {
        return false;
    }
    SeTrxContainerCtxT *item = TrxGetContainerCtxById((TrxT *)trx, labelId);
    if (item == NULL) {
        return false;
    }

    return item->ctxHead.isGetLabelXLock;
}

TrxIdT SeTransGetTrxId(const SeRunCtxHdT seRunCtx)
{
    return ((TrxT *)seRunCtx->trx)->base.trxId;
}

bool SeTransIsCommit(const SeRunCtxHdT seRunCtx, TrxIdT trxId)
{
    TrxMgrT *trxMgr = (TrxMgrT *)seRunCtx->trxMgr;
    return TrxMgrIsTrxCommitted(trxMgr, trxId);
}

bool SeTransGetSendSubFlag(const SeRunCtxHdT seRunCtx)
{
    return ((TrxT *)seRunCtx->trx)->base.needSendSubWhenFailed;
}

inline TrxTypeE SeTransGetTrxType(const struct TagSeRunCtxT *seRunCtx)
{
    DB_POINTER(seRunCtx);
    return TrxGetTrxType((TrxT *)seRunCtx->trx);
}

inline IsolationLevelE SeTransGetIsolationLevel(const struct TagSeRunCtxT *seRunCtx)
{
    DB_POINTER(seRunCtx);
    return TrxGetIsolationLevel((TrxT *)seRunCtx->trx);
}

Status SeSetTrxCtxForIndex(const TrxParamT *idxParam)
{
    const IndexOpenCfgT *idxOpenCfg = (const IndexOpenCfgT *)(const void *)idxParam;
    if (SECUREC_LIKELY(
            idxOpenCfg->chHandle != NULL && idxOpenCfg->chHandle->containerType == CONTAINER_CLUSTERED_HASH)) {
        return SeSetChLabelTrxCtxForIndex(idxOpenCfg);
    } else {
        DB_ASSERT(idxOpenCfg->heapHandle != NULL);
        DB_ASSERT(idxOpenCfg->heapHandle->containerType == CONTAINER_HEAP);
        return SeSetHeapTrxCtxForIndex(idxOpenCfg);
    }
}

Status SeTransGetTrxBaseInfoById(const SeRunCtxHdT seRunCtx, uint64_t id, bool isClonedId, TrxBaseInfoT *trxBaseInfo)
{
    DB_POINTER3(seRunCtx, seRunCtx->trxMgr, trxBaseInfo);
    TrxT *trx = NULL;
    Status ret = SeTransGetTrxByTrxId(seRunCtx, id, isClonedId, &trx);
    if (ret != GMERR_OK) {
        return ret;
    }
    trxBaseInfo->startTime = trx->base.startTime;
    trxBaseInfo->connId = trx->base.connId;
    trxBaseInfo->tid = trx->tid;
    trxBaseInfo->trxId = trx->base.trxId;
    return GMERR_OK;
}

// 缩容流程，提交时不改合并订阅链表结构
void SeTrxModifyPubsubList(TrxT *trx)
{
    DB_POINTER(trx);
    trx->base.isNeedModifyPubsubList = false;
}

void TrxSetIsAllocNewFlag(SeRunCtxHdT seRunCtx, bool isAllocNew)
{
    TrxT *trx = (TrxT *)seRunCtx->trx;
    trx->base.isAllocNew = isAllocNew;
}

bool TrxGetIsAllocNewFlag(TrxT *trx)
{
    return trx->base.isAllocNew;
}

SO_EXPORT_FOR_TS StatusInter TrxMgrCheckTrxIsCommitted(TrxMgrT *trxMgr, TrxIdT trxId, bool *isCommitted)
{
    DB_POINTER(trxMgr);  // LCOV_EXCL_LINE
    if (trxId < trxMgr->minActiveNormalTrxId) {
        *isCommitted = true;
        return STATUS_OK_INTER;
    }
    RW_LATCH_RLATCH_RETURN_IF_FAILED(&trxMgr->latch);
    TrxIdListT *rwTrxIds = TrxMgrGetRwTrxIds(trxMgr);
    // assume this trx is read-write trx, not read only
    *isCommitted = !TrxIdListLookup(rwTrxIds, trxId);
    DbInterProcRWUnlatchR(&trxMgr->latch);
    return STATUS_OK_INTER;
}

StatusInter SeResetUsedTrxResource(SeInstanceT *seIns)
{
    TrxMgrT *trxMgr = (TrxMgrT *)DbShmPtrToAddr(seIns->trxMgrShm);
    if (trxMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get trxMgr unsucc, (segid: %" PRIu32 " offset: %" PRIu32 ")",
            seIns->trxMgrShm.segId, seIns->trxMgrShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    TrxPoolT *trxPool = TrxMgrGetTrxPool(trxMgr);
    RW_LATCH_WLATCH_RETURN_IF_FAILED(&trxPool->latch);
    if (trxPool->usedCnt == 0) {
        DbInterProcRWUnlatchW(&trxPool->latch);
        return STATUS_OK_INTER;
    }
    TrxPoolNodeT *nodes = TrxPoolGetNodes(trxPool);
    uint16_t cursor = trxPool->usedHead;
    TrxT *trx = NULL;
    while (cursor != DB_INVALID_UINT16) {
        trx = TrxPoolGetTrxBySlot(trxPool, cursor);
        if (trx == NULL) {
            SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get trx, num: %" PRIu16 ", cursor: %" PRIu16 ".",
                trxPool->trxPoolNodeNum, cursor);
            DbInterProcRWUnlatchW(&trxPool->latch);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        if (trx->base.state == TRX_STATE_FAILURE) {
            trx->trx.base.normalUndo = NULL;
            trx->trx.base.retainedUndo = NULL;
            SeLockMgrLockReleaseAll(trx->seRunCtx);
            TrxCloseSavepoint(trx);
            trx->base.state = TRX_STATE_NOT_STARTED;
        }
        // 正常是事务提交时清理一次，seClose时清理一次；这里清理两次是因为如果锁库，可能会有事务没有提交
        TrxHandleContainerCtxList(trx, TRX_CLEAR_HANDLE);
        TrxHandleContainerCtxList(trx, TRX_CLEAR_HANDLE);
        DB_ASSERT(TrxAllResFreeCheck(trx));  // 注意：DB_ASSERT里是耗时检查，看护资源释放，请勿移出
        cursor = nodes[cursor].next;
    }
    DbInterProcRWUnlatchW(&trxPool->latch);
    return STATUS_OK_INTER;
}

#ifdef __cplusplus
}
#endif
