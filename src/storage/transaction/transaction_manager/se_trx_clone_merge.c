/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 事务克隆、合并特性相关函数
 * Author: GMDBv5 SE Team
 * Create: 2025/06/16
 */
#include "se_trx_inner.h"
#include "se_trx_mgr.h"
#include "se_log.h"
#include "se_undo.h"
#include "se_heap_utils.h"
#include "se_fixed_heap_inner.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef EXPERIMENTAL_GUANGQI
#define WAIT_CLONE_MERGE_REQ_TIME_OUT_MS 100
#define WAIT_CLONE_MERGE_AWAKE_TIME_US 10

static inline void TrxCloneTypeSwitchMemCtx(TrxT *trx)
{
    DbDestroyList(&trx->cntrCtxList.list);
    DbDestroyList(&trx->trx.optTrx.labelReadView.itemList[0]);
    DbDestroyList(&trx->trx.base.savePointInfo.itemList);
    DbCreateList(&trx->cntrCtxList.list, sizeof(char *), trx->trxCloneMemCtx);
    DbCreateList(&trx->trx.optTrx.labelReadView.itemList[0], sizeof(LabelReadViewInfoT), trx->trxCloneMemCtx);
    DbCreateList(&trx->trx.base.savePointInfo.itemList, sizeof(SavePointT), trx->trxCloneMemCtx);
}

StatusInter CloneTrxNonListSetActive(TrxT *trx)
{
    // 先设置一个非普通事务状态，后面会正确刷新，主要目的是清理事务性容器中的runctx
    // 有可能是普通事务操作了一个表，再开启克隆事务的，就会用到上一次的缓存runctx，而上一次又是从sessionMemCtx中申请的
    trx->base.cloneType = TRX_ClONE_TYPE_CLONABLE_TRX;
    TrxHandleContainerCtxList(trx, TRX_COMMIT_HANDLE);
    DbMemCtxArgsT args = {0};
    trx->trxCloneMemCtx =
        DbCreateDynMemCtx(((SeInstanceT *)trx->seRunCtx->seIns)->seCloneTrxMemCtx, false, "trxCloneMemCtx", &args);
    if (trx->trxCloneMemCtx == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc trxCloneMemCtx");
        return OUT_OF_MEMORY_INTER;
    }
    trx->trxPeriodMemCtx = trx->trxCloneMemCtx;
    // merge到父事务的，需要切换成跨连接的memctx
    TrxCloneTypeSwitchMemCtx(trx);
    return STATUS_OK_INTER;
}

void *SeTransGetTrxMemCtxForTrxMerge(const SeRunCtxHdT seRunCtx)
{
    return ((SeInstanceT *)seRunCtx->seIns)->seCloneTrxMemCtx;
}

void SeTransSetLabelLatchInfoForTrxMerge(const SeRunCtxHdT seRunCtx, void *labelLatchInfo)
{
    ((TrxT *)seRunCtx->trx)->trx.optTrx.cloneTrxExtInfo.mergeLabelLatchGaList = labelLatchInfo;
}

void SeTransSetYangInfoForTrxMerge(const SeRunCtxHdT seRunCtx, TrxMergeInfoT *mergeInfo)
{
    DB_POINTER2(seRunCtx, mergeInfo);
    ((TrxT *)seRunCtx->trx)->trx.optTrx.cloneTrxExtInfo.mergeInfo.validateState = mergeInfo->validateState;
    ((TrxT *)seRunCtx->trx)->trx.optTrx.cloneTrxExtInfo.mergeInfo.trxId = mergeInfo->trxId;
}

uint32_t SeTransGetCloneId(const SeRunCtxHdT seRunCtx)
{
    return ((TrxT *)seRunCtx->trx)->trx.optTrx.cloneId;
}

uint32_t SeTransGetParentCloneId(const SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);
    const SeRunCtxT *seRunCtxPtr = seRunCtx;
    DB_ASSERT(seRunCtxPtr->trx != NULL);
    return ((TrxT *)seRunCtxPtr->trx)->trx.optTrx.relationTrxInfo.parentCloneId;
}

void TrxContainerFlashSeRunCtxForTrxMerge(TrxT *trx, SeTrxContainerCtxT *container, SeRunCtxT *seRunCtx)
{
    if (container->ctxHead.type == (uint16_t)TRX_HEAP_HANDLE) {
        HeapRunCtxT *heapRunCtx = (HeapRunCtxT *)container->heapTrxCtx.heapRunHdl;
        if (heapRunCtx == NULL || heapRunCtx->seRunCtx == NULL) {  // heapRunCtx有可能为空
            // 此处用于debug下定位，正常情况下heapRunCtx不为空时，seRunCtx也不为空
            DB_ASSERT(heapRunCtx == NULL || heapRunCtx->seRunCtx != NULL);
            return;
        }
        heapRunCtx->seRunCtx = seRunCtx;
        FsmResetRunCtx(heapRunCtx->fsmRunCtx, seRunCtx);
    } else if (container->ctxHead.type == (uint16_t)TRX_FIXED_HEAP_HANDLE) {
        FixedHeapRunCtxT *fixedHeapRunCtx = (FixedHeapRunCtxT *)container->trxCntrCtx.cntrRunHdl;
        if (fixedHeapRunCtx == NULL || fixedHeapRunCtx->seCtx == NULL) {  // heapRunCtx有可能为空
            // 此处用于debug下定位，正常情况下fixedHeapRunCtx不为空时，seRunCtx也不为空
            DB_ASSERT(fixedHeapRunCtx == NULL || fixedHeapRunCtx->seCtx != NULL);
            return;
        }
        fixedHeapRunCtx->seCtx = seRunCtx;
        FsmResetRunCtx(&fixedHeapRunCtx->fsmRunCtx, seRunCtx);
    } else {
        DB_ASSERT(false);  // 预期没有其他类型的事务性容器
    }
}

static Status SeSetNewTrxSlotOnSeRunCtx(SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);
    TrxT *oldTrx = seRunCtx->trx;
    // 刷新seRunCtx下的事务槽
    StatusInter retInter = TrxMgrAllocTrx(seRunCtx->trxMgr, seRunCtx, (TrxT **)&(seRunCtx->trx));
    if (retInter != STATUS_OK_INTER) {
        SE_ERROR(retInter, "alloc new trx slot");
        return DbGetExternalErrno(retInter);
    }
    TrxT *trx = (TrxT *)seRunCtx->trx;
    SeUndoCtxT *undoCtx = seRunCtx->undoCtx;
    trx->trx.base.rseg = DbShmPtrToAddr(undoCtx->undoSpace->rsegShmPtr);
    if (trx->trx.base.rseg == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "rseg shmAddr(%" PRIu32 ",%" PRIu32 ")",
            undoCtx->undoSpace->rsegShmPtr.segId, undoCtx->undoSpace->rsegShmPtr.offset);
        TrxMgrFreeTrx(seRunCtx->trxMgr, seRunCtx->trx);
        seRunCtx->trx = oldTrx;
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    trx->trxPeriodMemCtx = oldTrx->trxSessionMemCtx;
    trx->trxSessionMemCtx = oldTrx->trxSessionMemCtx;
    trx->trxCloneMemCtx = NULL;
    return GMERR_OK;
}

static void SeResetOldTrxSlotOnSeRunCtx(SeRunCtxHdT seRunCtx, TrxT *oldTrx)
{
    TrxMgrFreeTrx(seRunCtx->trxMgr, seRunCtx->trx);
    seRunCtx->trx = oldTrx;
}

// 当前设计为父事务连接收到自调度消息后，调用该函数，创建通信结构体
ALWAYS_INLINE static Status SeCreateCloneOrMergeController(
    SeRunCtxHdT seRunCtx, uint16_t initiateConnId, TrxIdT initiateTrxId)
{
    TrxT *trx = seRunCtx->trx;
    // 创建父事务线程与克隆事务线程通信结构体
    CloneControlInfoT *cloneControlInfo = &trx->trx.optTrx.cloneControlInfo;
    DbInterProcRWLatchW(&cloneControlInfo->latch);
    if (cloneControlInfo->refCount != 0 || DbIsShmPtrValid(cloneControlInfo->cloneControllerShm)) {
        DB_LOG_ERROR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "cloneControlInfoShm already exist, trxId:%" PRIu64 "",
            trx->base.trxId);
        DbInterProcRWUnlatchW(&cloneControlInfo->latch);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    DbMemCtxT *shmCtx = (DbMemCtxT *)DbGetShmemCtxById(seIns->seAppShmMemCtxId, seIns->instanceId);
    if (shmCtx == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "memCtxId %" PRIu32 " inv", seIns->seAppShmMemCtxId);
        DbInterProcRWUnlatchW(&cloneControlInfo->latch);
        return DbGetExternalErrno(MEMORY_OPERATE_FAILED_INTER);
    }
    ShmemPtrT tmpShmPtr;
    CloneControllerT *cloneController = SeShmAlloc(shmCtx, (uint32_t)sizeof(CloneControllerT), &tmpShmPtr);
    if (cloneController == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc cloneController, trxId:%" PRIu64 "", trx->base.trxId);
        DbInterProcRWUnlatchW(&cloneControlInfo->latch);
        return DbGetExternalErrno(OUT_OF_MEMORY_INTER);
    }
    cloneController->initiateConnId = initiateConnId;
    cloneController->phase = CLONE_SYNC_PHASE_PREPARE;
    cloneController->initiateConnRet = GMERR_OK;
    cloneController->initiateConnFinish = 0;
    cloneController->parentConnRet = GMERR_OK;
    cloneController->parentConnFinish = 0;
    cloneController->initiateTrxId = initiateTrxId;
    COMPILER_BARRIER;
    cloneControlInfo->cloneControllerShm = tmpShmPtr;
    cloneControlInfo->refCount = 1;  // 创建者默认使用，所以refCount初始化为1
    DbInterProcRWUnlatchW(&cloneControlInfo->latch);
    return GMERR_OK;
}

ALWAYS_INLINE static Status SeCloneOrMergeParentSyncFinalResult(CloneControllerT *cloneController, TrxT *targetTrx)
{
    // selfRet为OK才会走到这里
    Status ret = GMERR_OK;
    CloneSyncPhaseE currentPhase;
    uint64_t startTime = DbClockGetTsc();
    // 等待子事务结束
    while (DbAtomicGet(&cloneController->initiateConnFinish) != 1) {
        uint64_t nowTime = DbClockGetTsc();
        if (DbToMseconds(nowTime - startTime) > WAIT_CLONE_MERGE_REQ_TIME_OUT_MS) {
            (void)DbAtomicBoolCAS((volatile uint32_t *)(&cloneController->phase), (uint32_t)CLONE_SYNC_PHASE_PREPARE,
                (uint32_t)CLONE_SYNC_PHASE_ABORT);
            // 如果CAS没设置成功，说明另一线程已经修改了phase，子事务线程只可能设置为abort, 所以可以直接退出
            DB_LOG_ERROR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
                "parent wait child result time out , targetTrxId:%" PRIu64 ", cloneId:%" PRIu32 "",
                targetTrx->base.trxId, targetTrx->trx.optTrx.cloneId);
            ret = GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
            goto FINAL;
        }
        DbUsleep(WAIT_CLONE_MERGE_AWAKE_TIME_US);
    }
    ret = cloneController->initiateConnRet;
    if (ret == GMERR_OK) {
        (void)DbAtomicBoolCAS((volatile uint32_t *)(&cloneController->phase), (uint32_t)CLONE_SYNC_PHASE_PREPARE,
            (uint32_t)CLONE_SYNC_PHASE_COMMIT);
    } else {
        DB_LOG_ERROR(ret, "child unsucc");
        (void)DbAtomicBoolCAS((volatile uint32_t *)(&cloneController->phase), (uint32_t)CLONE_SYNC_PHASE_PREPARE,
            (uint32_t)CLONE_SYNC_PHASE_ABORT);
    }
FINAL:
    currentPhase = (CloneSyncPhaseE)DbAtomicGet(&cloneController->phase);
    if (SECUREC_UNLIKELY(
            currentPhase == CLONE_SYNC_PHASE_PREPARE || (currentPhase == CLONE_SYNC_PHASE_COMMIT && ret != GMERR_OK))) {
        // 不可能出现该情况，出现说明线程间同步出了状态不一致问题
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
            "clone or merge trx, currentPhase:%" PRIu32 ", targetTrxId:%" PRIu64 ", cloneId:%" PRIu32 "", currentPhase,
            targetTrx->base.trxId, targetTrx->trx.optTrx.cloneId);
        DB_ASSERT(false);
        return GMERR_INTERNAL_ERROR;
    }
    if (currentPhase == CLONE_SYNC_PHASE_ABORT && ret == GMERR_OK) {
        // 只有一种可能, 子事务线程执行成功，这边成功收到结果，但子事务线程超时了
        DB_LOG_ERROR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "initiate thread accept final result time out, targetTrxId:%" PRIu64 ", cloneId:%" PRIu32 "",
            targetTrx->base.trxId, targetTrx->trx.optTrx.cloneId);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    // CLONE_SYNC_PHASE_COMMIT && ret OK，或 CLONE_SYNC_PHASE_ABORT && ret not OK
    return ret;
}

// 父事务连接操作完成后，等待子事务执行结果，设置最终结果
ALWAYS_INLINE static Status SeSetCloneOrMergeFinalResult(TrxT *targetTrx, Status selfRet)
{
    // 该线程创建通信结构时，会持有一个引用计数，所以可以直接读信息
    CloneControllerT *cloneController =
        (CloneControllerT *)DbShmPtrToAddr(targetTrx->trx.optTrx.cloneControlInfo.cloneControllerShm);
    if (cloneController == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "cloneControlInfoShm(%" PRIu32 ",%" PRIu32 ") inv",
            targetTrx->trx.optTrx.cloneControlInfo.cloneControllerShm.offset,
            targetTrx->trx.optTrx.cloneControlInfo.cloneControllerShm.segId);
        return GMERR_INTERNAL_ERROR;
    }
    if (selfRet != GMERR_OK) {
        DbAtomicSet((volatile uint32_t *)&cloneController->parentConnRet, (uint32_t)selfRet);
        DbAtomicSet((volatile uint32_t *)&cloneController->parentConnFinish, 1);
        (void)DbAtomicBoolCAS((volatile uint32_t *)(&cloneController->phase), (uint32_t)CLONE_SYNC_PHASE_PREPARE,
            (uint32_t)CLONE_SYNC_PHASE_ABORT);
        return selfRet;
    }
    return SeCloneOrMergeParentSyncFinalResult(cloneController, targetTrx);
}

// 子事务连接，等待通信结构体创建完成
ALWAYS_INLINE static Status SeInitiateConnGetController(TrxT *targetTrx, uint16_t initiateConnId, TrxIdT selfTrxId)
{
    CloneControlInfoT *cloneControlInfo = &targetTrx->trx.optTrx.cloneControlInfo;
    uint64_t startTime = DbClockGetTsc();
    DbInterProcRWLatchW(&cloneControlInfo->latch);
    while (!DbIsShmPtrValid(targetTrx->trx.optTrx.cloneControlInfo.cloneControllerShm)) {
        uint64_t nowTime = DbClockGetTsc();
        if (DbToMseconds(nowTime - startTime) > WAIT_CLONE_MERGE_REQ_TIME_OUT_MS) {
            DB_LOG_ERROR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "initiate thread wait controller time out");
            DbInterProcRWUnlatchW(&cloneControlInfo->latch);
            return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
        }
        DbInterProcRWUnlatchW(&cloneControlInfo->latch);
        DbUsleep(WAIT_CLONE_MERGE_AWAKE_TIME_US);
        DbInterProcRWLatchW(&cloneControlInfo->latch);
    }
    CloneControllerT *cloneController = (CloneControllerT *)DbShmPtrToAddr(cloneControlInfo->cloneControllerShm);
    if (cloneController == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "cloneControlInfoShm(%" PRIu32 ",%" PRIu32 ") inv",
            targetTrx->trx.optTrx.cloneControlInfo.cloneControllerShm.offset,
            targetTrx->trx.optTrx.cloneControlInfo.cloneControllerShm.segId);
        DbInterProcRWUnlatchW(&cloneControlInfo->latch);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (cloneController->initiateConnId != initiateConnId) {
        DB_LOG_ERROR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "parent conn processing other child conn, cloneController-initiateConnId:%" PRIu16
            ", current-initiateConnId:%" PRIu16,
            cloneController->initiateConnId, initiateConnId);
        DbInterProcRWUnlatchW(&cloneControlInfo->latch);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    if (cloneController->initiateTrxId != selfTrxId) {
        DB_LOG_ERROR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "parent conn processing other child Trx, cloneController-initiateTrxId:%" PRIu64 ", selfTrxId:%" PRIu64,
            cloneController->initiateTrxId, selfTrxId);
        DbInterProcRWUnlatchW(&cloneControlInfo->latch);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    // 等待成功，此时持有锁，增加引用计数。
    cloneControlInfo->refCount++;
    DbInterProcRWUnlatchW(&cloneControlInfo->latch);
    return GMERR_OK;
}

// 子事务连接，做完本身操作后，设置自身结果，并且等待最终结果，如果超时，切换状态为abort
ALWAYS_INLINE static Status SeInitiateConnSetRetAndWaitFinalResult(TrxT *targetTrx, Status selfRet)
{
    // 持有一个引用计数，可以直接读信息
    CloneControllerT *cloneController =
        (CloneControllerT *)DbShmPtrToAddr(targetTrx->trx.optTrx.cloneControlInfo.cloneControllerShm);
    if (cloneController == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "cloneControlInfoShm(%" PRIu32 ",%" PRIu32 ") inv",
            targetTrx->trx.optTrx.cloneControlInfo.cloneControllerShm.offset,
            targetTrx->trx.optTrx.cloneControlInfo.cloneControllerShm.segId);
        return GMERR_INTERNAL_ERROR;
    }
    // 通知父事务，发起线程的操作已经结束
    DbAtomicSet((volatile uint32_t *)&cloneController->initiateConnRet, (uint32_t)selfRet);
    DbAtomicSet((volatile uint32_t *)&cloneController->initiateConnFinish, 1);
    if (selfRet != GMERR_OK) {
        (void)DbAtomicBoolCAS(
            (volatile uint32_t *)(&cloneController->phase), CLONE_SYNC_PHASE_PREPARE, CLONE_SYNC_PHASE_ABORT);
        return selfRet;
    }
    // selfRet为OK才继续往下走
    uint64_t startTime = DbClockGetTsc();
    CloneSyncPhaseE phase = (CloneSyncPhaseE)DbAtomicGet(&cloneController->phase);
    while (phase != CLONE_SYNC_PHASE_COMMIT && phase != CLONE_SYNC_PHASE_ABORT) {
        uint64_t nowTime = DbClockGetTsc();
        if (DbToMseconds(nowTime - startTime) > WAIT_CLONE_MERGE_REQ_TIME_OUT_MS) {
            (void)DbAtomicBoolCAS((volatile uint32_t *)(&cloneController->phase), (uint32_t)CLONE_SYNC_PHASE_PREPARE,
                (uint32_t)CLONE_SYNC_PHASE_ABORT);
            DB_LOG_ERROR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "child wait final result time out");
            // 如果CAS设置成功，超时退出，外层检查最终结果
            // 如果CAS设置不成功，说明另一线程已经收到了这边的结果, 修改了phase, 同样退出，外层检查最终结果
            break;
        }
        DbUsleep(WAIT_CLONE_MERGE_AWAKE_TIME_US);
        phase = (CloneSyncPhaseE)DbAtomicGet(&cloneController->phase);
    }
    phase = (CloneSyncPhaseE)DbAtomicGet(&cloneController->phase);
    if (phase == CLONE_SYNC_PHASE_COMMIT) {
        return GMERR_OK;
    } else if (phase == CLONE_SYNC_PHASE_ABORT) {
        Status ret = GMERR_OK;
        if (DbAtomicGet(&cloneController->parentConnFinish) == 1) {
            ret = (Status)DbAtomicGet((volatile uint32_t *)&cloneController->parentConnRet);
        }
        return (ret == GMERR_OK ? GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE : ret);
    } else {
        // 不可能跑到此处，assert维护
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "child get final result, phase:%" PRIu32 "", phase);
        DB_ASSERT(false);
        return GMERR_INTERNAL_ERROR;
    }
}

// 线程下可能已经切换到继承者事务或者组合事务（合并后的），所以需要传入挂了通信结构体的targetTrx
static void SeDestroyCloneOrMergeControlInfo(SeRunCtxHdT seRunCtx, TrxT *targetTrx)
{
    CloneControlInfoT *cloneControlInfo = &targetTrx->trx.optTrx.cloneControlInfo;
    DbInterProcRWLatchW(&cloneControlInfo->latch);
    if (cloneControlInfo->refCount == 0) {
        DB_ASSERT(!DbIsShmPtrValid(cloneControlInfo->cloneControllerShm));
        DB_LOG_INFO("cloneControllerShm already free");
        DbInterProcRWUnlatchW(&cloneControlInfo->latch);
        return;
    }
    cloneControlInfo->refCount--;
    DB_ASSERT(cloneControlInfo->refCount <= 1);

    if (cloneControlInfo->refCount == 1) {
        DbInterProcRWUnlatchW(&cloneControlInfo->latch);
        return;
    }

    // refCount为0的情况
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    DbMemCtxT *shmCtx = (DbMemCtxT *)DbGetShmemCtxById(seIns->seAppShmMemCtxId, seIns->instanceId);
    if (shmCtx == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "memCtxId %" PRIu32 " inv", seIns->seAppShmMemCtxId);
        DbInterProcRWUnlatchW(&cloneControlInfo->latch);
        return;
    }
    DbShmemCtxFree(shmCtx, cloneControlInfo->cloneControllerShm);
    cloneControlInfo->cloneControllerShm = DB_INVALID_SHMPTR;
    DbInterProcRWUnlatchW(&cloneControlInfo->latch);
}

Status SeTransClone(SeRunCtxHdT seRunCtx, const TrxCfgT *cfg, uint32_t parentCloneId)
{
    DB_POINTER2(seRunCtx, cfg);
    TrxT *parentTrx = NULL;
    Status ret = SeTransGetTrxByTrxId(seRunCtx, (uint64_t)parentCloneId, true, &parentTrx);
    if (ret != GMERR_OK || parentTrx == NULL) {
        DB_LOG_ERROR(ret, "Trx clone: parentTrx not exist, parentCloneId:%" PRIu32 ".", parentCloneId);
        return ret;
    }
    ret = SeInitiateConnGetController(parentTrx, cfg->connId, 0);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SeTransBegin(seRunCtx, cfg);
    if (ret != GMERR_OK) {
        goto ERR;
    }
    TrxT *trx = seRunCtx->trx;
    StatusInter retInter = ReadViewPrepareByParentTrx(trx, parentTrx, DB_MAX_UINT64);
    if (retInter != STATUS_OK_INTER) {
        SE_ERROR(retInter,
            "clone trx prepare readview, trxId:%" PRIu64 ", cloneId:%" PRIu32 ", parentTrxId:%" PRIu64 "",
            trx->base.trxId, parentCloneId, parentTrx->base.trxId);
        ret = DbGetExternalErrno(retInter);
        goto FINAL;
    }
    Status finalRet;
FINAL:
    finalRet = SeInitiateConnSetRetAndWaitFinalResult(parentTrx, ret);
    if (finalRet != GMERR_OK) {
        ReadViewClose(seRunCtx->trx);
        trx->base.state = TRX_STATE_NOT_STARTED;
        SeDestroyCloneOrMergeControlInfo(seRunCtx, parentTrx);
        return finalRet;
    }
    trx->trx.optTrx.relationTrxInfo.parentCloneId = parentCloneId;
    trx->trx.optTrx.relationTrxInfo.parentTrxId = parentTrx->base.trxId;
    trx->base.cloneType = TRX_ClONE_TYPE_CLONE_TRX;
    parentTrx->trx.optTrx.relationTrxInfo.childTrxId = trx->base.trxId;  // 子事务线程此处直接设置父亲的子事务信息
    parentTrx->trx.optTrx.relationTrxInfo.childTrxActive = true;
    SeDestroyCloneOrMergeControlInfo(seRunCtx, parentTrx);
    return GMERR_OK;
ERR:
    finalRet = SeInitiateConnSetRetAndWaitFinalResult(parentTrx, ret);
    SeDestroyCloneOrMergeControlInfo(seRunCtx, parentTrx);
    return finalRet;
}

static Status SeTransCreateSuccessorCheck(
    SeRunCtxHdT seRunCtx, TrxT *parentTrx, uint32_t cloneId, uint16_t initiateConnId)
{
    if (parentTrx->base.state != TRX_STATE_ACTIVE) {
        DB_LOG_ERROR(GMERR_NO_ACTIVE_TRANSACTION, "create successor: parentTrx not active, initiateConnId:%" PRIu16,
            initiateConnId);
        return GMERR_NO_ACTIVE_TRANSACTION;
    }
    if (parentTrx->trx.optTrx.cloneId != cloneId) {
        DB_LOG_ERROR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "create successor: parentTrx cloneId(%" PRIu32 ") not match, targetCloneId:%" PRIu32,
            parentTrx->trx.optTrx.cloneId, cloneId);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    if (parentTrx->base.cloneType != TRX_ClONE_TYPE_CLONABLE_TRX) {
        DB_LOG_ERROR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "Trx create successor: trx not support cloned, trxCloneType:%" PRIu32 ", initiateConnId:%" PRIu16,
            parentTrx->base.cloneType, initiateConnId);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    return GMERR_OK;
}

static inline void SeTransUseNewSlotRestore(SeRunCtxHdT seRunCtx, TrxT *trx, TrxT *oldTrx)
{
    ReadViewClose(trx);
    SeResetOldTrxSlotOnSeRunCtx(seRunCtx, oldTrx);
}

Status SeTransCreateSuccessor(SeRunCtxHdT seRunCtx, const TrxCfgT *cfg, uint32_t cloneId, uint16_t initiateConnId)
{
    DB_POINTER2(seRunCtx, cfg);
    TrxT *parentTrx = seRunCtx->trx;
    Status ret = SeTransCreateSuccessorCheck(seRunCtx, parentTrx, cloneId, initiateConnId);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SeCreateCloneOrMergeController(seRunCtx, initiateConnId, 0);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "CreateCloneControlInfo unsucc, initiateConnId:%" PRIu16, initiateConnId);
        return ret;
    }

    ret = SeSetNewTrxSlotOnSeRunCtx(seRunCtx);
    if (ret != GMERR_OK) {
        goto ERR;
    }

    // 获取继承者事务
    TrxT *trx = seRunCtx->trx;
    ret = SeTransBegin(seRunCtx, cfg);
    if (ret != GMERR_OK) {
        goto FINAL;
    }
    StatusInter retInter = ReadViewPrepareByParentTrx(trx, parentTrx, DB_MAX_UINT64);
    if (retInter != STATUS_OK_INTER) {
        ret = DbGetExternalErrno(retInter);
        goto FINAL;
    }
    Status finalRet;
FINAL:
    finalRet = SeSetCloneOrMergeFinalResult(parentTrx, ret);
    if (finalRet != GMERR_OK) {
        SeTransUseNewSlotRestore(seRunCtx, trx, parentTrx);
        SeDestroyCloneOrMergeControlInfo(seRunCtx, parentTrx);
        return finalRet;
    }
    parentTrx->base.cloneType = TRX_ClONE_TYPE_CLONED;
    parentTrx->trx.optTrx.relationTrxInfo.successorTrxId = trx->base.trxId;
    parentTrx->trx.optTrx.relationTrxInfo.successorTrxActive = true;
    uint32_t tmpCloneId = parentTrx->trx.optTrx.cloneId;
    parentTrx->trx.optTrx.cloneId = DB_INVALID_UINT32;
    trx->trx.optTrx.relationTrxInfo.parentTrxId = parentTrx->base.trxId;
    trx->base.cloneType = TRX_ClONE_TYPE_SUCCESSOR_TRX;
    trx->trx.optTrx.cloneId = tmpCloneId;
    SeDestroyCloneOrMergeControlInfo(seRunCtx, parentTrx);
    return GMERR_OK;
ERR:
    finalRet = SeSetCloneOrMergeFinalResult(parentTrx, ret);
    SeDestroyCloneOrMergeControlInfo(seRunCtx, parentTrx);
    return finalRet;
}

// 先假定一个父事务只有一个克隆事务和一个继任者事务
Status SeTransMerge(SeRunCtxHdT seRunCtx, uint16_t selfConnId)
{
    DB_POINTER(seRunCtx);
    TrxT *trx = seRunCtx->trx;
    TrxT *parentTrx = NULL;
    Status ret =
        SeTransGetTrxByTrxId(seRunCtx, (uint64_t)trx->trx.optTrx.relationTrxInfo.parentCloneId, true, &parentTrx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Trx merge: successorTrx not exist, cloneId:%" PRIu32 ".",
            trx->trx.optTrx.relationTrxInfo.parentCloneId);
        return ret;
    }
    ret = SeInitiateConnGetController(parentTrx, selfConnId, trx->base.trxId);
    if (ret != GMERR_OK) {
        return ret;
    }
    bool isUseNewSlot = false;
    ret = SeSetNewTrxSlotOnSeRunCtx(seRunCtx);
    if (ret != GMERR_OK) {
        goto FINAL;
    }
    isUseNewSlot = true;
    Status finalRet;
FINAL:
    finalRet = SeInitiateConnSetRetAndWaitFinalResult(parentTrx, ret);
    if (finalRet != GMERR_OK) {
        if (isUseNewSlot) {
            SeResetOldTrxSlotOnSeRunCtx(seRunCtx, trx);
        }
        SeDestroyCloneOrMergeControlInfo(seRunCtx, parentTrx);
        return finalRet;
    }
    // 切换se上下文为父事务的se上下文
    trx->seRunCtx = parentTrx->seRunCtx;
    // 合并后的事务，后续不能再使用本连接的sessionMemCtx，重置后置为NULL
    DbMemCtxReset((DbMemCtxT *)trx->trxSessionMemCtx);
    trx->trxSessionMemCtx = NULL;
    SeDestroyCloneOrMergeControlInfo(seRunCtx, parentTrx);
    return GMERR_OK;
}

Status SeTransCreateCombineTrxCheck(TrxT *successorTrx, uint32_t cloneId, uint16_t initiateConnId)
{
    if (successorTrx->trx.optTrx.cloneId != cloneId) {
        DB_LOG_ERROR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "createCombineTrx: successorTrx cloneId(%" PRIu32 ") not match, targetCloneId:%" PRIu32,
            successorTrx->trx.optTrx.cloneId, cloneId);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }

    if (successorTrx->base.state != TRX_STATE_ACTIVE) {
        DB_LOG_ERROR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "createCombineTrx: successorTrx(%" PRIu64 ") not active",
            successorTrx->base.trxId);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }

    if (successorTrx->base.cloneType != TRX_ClONE_TYPE_SUCCESSOR_TRX) {
        DB_LOG_ERROR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "createCombineTrx: trx(%" PRIu64 ") not support, clone type:%" PRIu32 "", successorTrx->base.trxId,
            successorTrx->base.cloneType);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    return GMERR_OK;
}

Status SeTransCreateCombineTrx(
    SeRunCtxHdT seRunCtx, const TrxCfgT *cfg, uint32_t cloneId, TrxIdT cloneTrxId, uint16_t initiateConnId)
{
    DB_POINTER2(seRunCtx, cfg);
    TrxT *successorTrx = seRunCtx->trx;

    Status ret = SeTransCreateCombineTrxCheck(successorTrx, cloneId, initiateConnId);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SeCreateCloneOrMergeController(seRunCtx, initiateConnId, cloneTrxId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "CreateCloneControlInfo unsucc, initiateConnId:%" PRIu16, initiateConnId);
        return ret;
    }

    Status finalRet;

    ret = SeSetNewTrxSlotOnSeRunCtx(seRunCtx);
    if (ret != GMERR_OK) {
        goto ERR;
    }

    TrxT *trx = seRunCtx->trx;
    ret = SeTransBegin(seRunCtx, cfg);
    if (ret != GMERR_OK) {
        goto FINAL;
    }
    StatusInter retInter = ReadViewPrepareByParentTrx(trx, successorTrx, cloneTrxId);
    if (retInter != STATUS_OK_INTER) {
        ret = DbGetExternalErrno(retInter);
        goto FINAL;
    }
FINAL:
    finalRet = SeSetCloneOrMergeFinalResult(successorTrx, ret);
    if (finalRet != GMERR_OK) {
        SeTransUseNewSlotRestore(seRunCtx, trx, successorTrx);
        SeDestroyCloneOrMergeControlInfo(seRunCtx, successorTrx);
        return finalRet;
    }
    successorTrx->trx.optTrx.cloneId = DB_INVALID_UINT32;
    trx->trx.optTrx.relationTrxInfo.parentTrxId = successorTrx->base.trxId;
    trx->base.cloneType = TRX_ClONE_TYPE_CLONABLE_TRX;
    trx->trx.optTrx.cloneId = cloneId;
    SeDestroyCloneOrMergeControlInfo(seRunCtx, successorTrx);
    return GMERR_OK;
ERR:
    finalRet = SeSetCloneOrMergeFinalResult(successorTrx, ret);
    SeDestroyCloneOrMergeControlInfo(seRunCtx, successorTrx);
    return finalRet;
}

static inline bool IsAncestorTrx(TrxIdListT *ancestorTrxlist, TrxIdT trxId)
{
    for (uint32_t i = 0; i < ancestorTrxlist->listLen; ++i) {
        if (ancestorTrxlist->trxIds[i] == trxId) {
            return true;
        }
    }
    return false;
}

static TrxT *TrxMgrGetActiveTrxByTrxSlot(TrxMgrT *trxMgr, uint16_t trxSlot)
{
    DB_POINTER(trxMgr);
    // 无效的trxSlot值
    if (trxSlot >= trxMgr->maxTrxNum) {
        return NULL;
    }
    TrxPoolT *trxPool = TrxMgrGetTrxPool(trxMgr);
    ShmemPtrT *trxs = TrxPoolGetTrxs(trxPool);
    uint64_t slot = trxSlot / TRX_NUM_PER_BLOCK;
    ShmemPtrT trxPtr = trxs[slot];
    TrxT *trxsGroup = (TrxT *)DbShmPtrToAddr(trxPtr);
    if (trxsGroup == NULL) {
        return NULL;
    }
    // 不使用hashmap查找trx时，此处可能出现slot已经失效的问题，需要做一次校验
    // fix me：在做死锁检测时，如何保证已检测的trx没有被释放
    TrxT *trx = &trxsGroup[trxSlot % TRX_NUM_PER_BLOCK];
    if (trx->base.state == TRX_STATE_NOT_STARTED) {
        // trxSlot对应的Trx已经被分配给其他事务使用
        return NULL;
    }
    return trx;
}

int32_t TrxListOrderCmp(const void *item1, const void *item2)
{
    TrxT *const *trx1 = (TrxT *const *)item1;
    TrxT *const *trx2 = (TrxT *const *)item2;
    uint64_t trxId1 = (*trx1)->base.trxId;
    uint64_t trxId2 = (*trx2)->base.trxId;
    return ((trxId1 > trxId2) - (trxId1 < trxId2));
}

int32_t TrxListDescendCmp(const void *item1, const void *item2)
{
    TrxT *const *trx1 = (TrxT *const *)item1;
    TrxT *const *trx2 = (TrxT *const *)item2;
    uint64_t trxId1 = (*trx1)->base.trxId;
    uint64_t trxId2 = (*trx2)->base.trxId;
    return ((trxId1 < trxId2) - (trxId1 > trxId2));
}

static StatusInter OptimisticTrxGetAncestorTrxList(TrxT *trx, DbListT *ancestorTrxList)
{
    SeRunCtxHdT seRunCtx = trx->seRunCtx;
    ReadViewT *readView = &trx->trx.base.readView;
    TrxMgrT *trxMgr = (TrxMgrT *)seRunCtx->trxMgr;
    TrxIdListT *parentTrxs = (TrxIdListT *)DbShmPtrToAddr(readView->parentTrxIdsShm);
    if (SECUREC_UNLIKELY(parentTrxs == NULL)) {
        // 事务没有被克隆过，没有父亲列表，此处返回OK
        return STATUS_OK_INTER;
    }
    // 外层已经拿了trxMgr的锁
    TrxPoolT *trxPool = TrxMgrGetTrxPool(trxMgr);
    DbInterProcRWLatchR(&trxPool->latch);
    uint16_t curNode = trxPool->usedHead;
    // 已用事务链表为空, 直接返回不存在
    if (curNode == DB_INVALID_ID16) {
        DbInterProcRWUnlatchR(&trxPool->latch);
        SE_ERROR(NO_DATA_INTER, "used trx list empty");
        return NO_DATA_INTER;
    }
    TrxPoolNodeT *nodes = TrxPoolGetNodes(trxPool);
    // 遍历已用事务链表, 找到对应ID的事务
    while (curNode != DB_INVALID_ID16) {
        TrxT *findTrx = TrxMgrGetActiveTrxByTrxSlot(trxMgr, curNode);  // 已经加过trxPool的锁了，传false即可
        if (findTrx != NULL && IsAncestorTrx(parentTrxs, findTrx->base.trxId)) {
            DbAppendListItem(ancestorTrxList, &findTrx);
        }
        curNode = nodes[curNode].next;
        if (parentTrxs->listLen == DbListGetItemCnt(ancestorTrxList)) {
            break;
        }
    }
    DbInterProcRWUnlatchR(&trxPool->latch);
    if (parentTrxs->listLen != DbListGetItemCnt(ancestorTrxList)) {
        SE_ERROR(INTERNAL_ERROR_INTER, "parentCnt not match, recordCnt: %" PRIu16 ", findCnt:%" PRIu16 "",
            parentTrxs->listLen, DbListGetItemCnt(ancestorTrxList));
        return INTERNAL_ERROR_INTER;
    }
    return STATUS_OK_INTER;
}

static StatusInter OptiTrxLabelReadViewMerge(TrxT *trx, TrxT *ancestorTrx)
{
    LabelReadViewT *ancestorLableReadView = &ancestorTrx->trx.optTrx.labelReadView;
    if (ancestorLableReadView->isTrxCommitCheckActive == false) {
        return STATUS_OK_INTER;
    } else {
        trx->trx.optTrx.labelReadView.isTrxCommitCheckActive = true;
    }
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < ancestorLableReadView->curItemCnt; ++i) {
        ret = SeTransCheckAndSetLabelModified(trx->seRunCtx, ancestorLableReadView->itemArray[i].labelId,
            ancestorLableReadView->itemArray[i].type, false);
        if (ret != GMERR_OK) {
            return DbGetStatusInterErrno(ret);
        }
    }
    for (uint32_t j = 0; j < DEF_EXT_VIEW_LIST_CNT; j++) {
        uint32_t listCnt = DbListGetItemCnt(&ancestorLableReadView->itemList[j]);
        for (uint32_t i = 0; i < listCnt; ++i) {
            LabelReadViewInfoT *labelInfo = (LabelReadViewInfoT *)DbListItem(&ancestorLableReadView->itemList[j], i);
            ret = SeTransCheckAndSetLabelModified(trx->seRunCtx, labelInfo->labelId, labelInfo->type, false);
            if (ret != GMERR_OK) {
                return DbGetStatusInterErrno(ret);
            }
        }
    }
    return STATUS_OK_INTER;
}

StatusInter OptimisticTrxCheckConflict(TrxT *trx)
{
    if (!trx->trx.optTrx.labelReadView.isTrxCommitCheckActive) {
        // 未激活检查时，说明该事务没做过DML操作, 不会冲突
        // 如果是savePoint回滚回来的，recCnt肯定为0
        DB_ASSERT(trx->trx.base.retainedUndo == NULL || trx->trx.base.retainedUndo->recCnt == 0);
        DB_ASSERT(trx->trx.base.normalUndo == NULL || trx->trx.base.normalUndo->recCnt == 0);
        return STATUS_OK_INTER;
    }
    // 如果事务成功提交, 记录事务操作过的每种表的数量, 作为DFX信息
    uint32_t edgeLabelCnt = 0;
    uint32_t kvLabelCnt = 0;
    uint32_t vertexLabelCnt = 0;
    StatusInter ret = OptiTrxValidateLabelTrxIdForCommit(trx, &edgeLabelCnt, &kvLabelCnt, &vertexLabelCnt);
    if (ret != STATUS_OK_INTER) {
        /* 校验失败，需要用户手动回滚，不修改事务状态 */
        DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP, "OptimisticTrxRollback trxId:%" PRIu64, trx->base.trxId);
        return ret;
    }
    // 校验成功, 更新操作过的label的trxId
    OptiTrxUpdLabelTrxIdForCommit(trx, edgeLabelCnt, kvLabelCnt, vertexLabelCnt);
    return ret;
}

StatusInter OptiTrxProcessCommitInTrxMgrLatch(TrxT *trx)
{
    DB_POINTER(trx);
    // 需要和 TrxCommitInTrxList 在同一原子操作和TrxMgr 锁内，避免并发事务看到不一致的状态
    StatusInter ret = TrxMarkContainerRecCnt(trx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    // 这里忽略回调函数的返回值，仅为了维护视图字段信息
    (void)TrxCommitCallBack(trx);
    TrxCommitInTrxList(trx);
    DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP, "OptimisticTrxCommit trxId:%" PRIu64, trx->base.trxId);
    // 根据 RecoveryUndoCleanUpCommitSeg 的逻辑，事务已经提交的版本默认retainedUndo已经在历史版本链表上
    // 所以此处需要将 TrxUndoSetTrxState 与 TrxUndoRetainedAddToHistory 放在一个原子范围内
    ret = TrxUndoRetainedAddToHistory(trx, trx->trx.base.retainedUndo);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    ret = TrxUndoSetTrxState(trx, TRX_STATE_COMMITTED);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    TrxGetCommitTs(trx);
    return ret;
}

static StatusInter SingleTrxClearOnCommitOrRollBack(TrxT *trx, bool isAncestor)
{
    DB_POINTER(trx);
    if (TrxIsMemoryLimit()) {
        TrxSetEscapeMemCtx(trx);
    }

    // 普通事务提交回滚见 TrxClearOnCommitOrRollback
    StatusInter ret = STATUS_OK_INTER;
    if (trx->base.state == TRX_STATE_COMMITTED) {
        ret = TrxClearOnCommit(trx);
    } else {
        ret = TrxClearOnRollback(trx);
    }

    TrxClearKeyDataBuf(trx);  // 此处仅作变量清理，内存释放由trxPeriodMemCtx的reset完成
    DB_ASSERT(trx->base.cloneType != TRX_ClONE_TYPE_DEFAULT_TRX);
    DB_ASSERT(trx->trxPeriodMemCtx == trx->trxCloneMemCtx);
    TrxReleaseRes(trx);  // 删除trxCloneMemCtx前，先释放资源, 包括readview
    if (isAncestor) {
        // 后续其他连接使用该事务槽，会重新申请，此时置NULL
        trx->trxPeriodMemCtx = NULL;
        trx->trxSessionMemCtx = NULL;
    } else {
        // 如果是当前线程所用事务，重置回连接级memCtx，并且reset memCtx
        DB_ASSERT(trx->trxPeriodMemCtx != trx->trxSessionMemCtx);
        trx->trxPeriodMemCtx = trx->trxSessionMemCtx;
        DbMemCtxReset((DbMemCtxT *)trx->trxPeriodMemCtx);
        // 切换对应事务DbList资源的memCtx, 与 TrxCloneTypeSwitchMemCtx 对应
        DbDestroyList(&trx->cntrCtxList.list);
        DbDestroyList(&trx->trx.optTrx.labelReadView.itemList[0]);
        DbDestroyList(&trx->trx.base.savePointInfo.itemList);
        DbCreateList(&trx->cntrCtxList.list, sizeof(char *), trx->seRunCtx->sessionMemCtx);
        DbCreateList(
            &trx->trx.optTrx.labelReadView.itemList[0], sizeof(LabelReadViewInfoT), trx->seRunCtx->sessionMemCtx);
        DbCreateList(&trx->trx.base.savePointInfo.itemList, sizeof(SavePointT), trx->seRunCtx->sessionMemCtx);
    }
    DbDeleteDynMemCtx((DbMemCtxT *)trx->trxCloneMemCtx);
    trx->trxCloneMemCtx = NULL;

    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "trx clear on commit or rollback, trx id %" PRIu64 ", type %" PRIu32 ", state %" PRIu32,
            trx->base.trxId, (uint32_t)trx->base.trxType, (uint32_t)trx->base.state);
        if (SeGetStorageStatus(trx->seRunCtx->seIns) == SE_ON_DISK_EMRGNCY) {
            return DATABASE_NOT_AVAILABLE_INTER;  // DB不可用状态下，不释放事务锁
        }
        DB_ASSERT(false);
    }

    if (!trx->base.isPurger) {
        SePersistUnRegisterDataOp(trx->seRunCtx, !trx->base.readOnly);
    }
    SePersistUnRegisterDDLOp(trx->seRunCtx);
    trx->base.state = TRX_STATE_NOT_STARTED;

    DB_ASSERT(trx->trx.pesTrx.holdLockNum == 0);
    return STATUS_OK_INTER;
}

StatusInter OptiTrxProcessMergeLatch(TrxT *trx)
{
    if (trx->trx.optTrx.cloneTrxExtInfo.mergeLabelLatchGaList == NULL) {
        return STATUS_OK_INTER;
    }
    // 只有合并进来的事务，才有带进来的表锁信息
    DB_ASSERT(trx->base.cloneType == TRX_ClONE_TYPE_CLONE_TRX);
    void *mergeLabelLatchGaList = trx->trx.optTrx.cloneTrxExtInfo.mergeLabelLatchGaList;
    if (trx->seRunCtx->mergeTrxsLabelLatchList == NULL) {
        DbMemCtxT *memCtx = SeTransGetTrxMemCtxForTrxMerge(trx->seRunCtx);
        DbListT *mergeTrxsLabelLatchList = DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
        if (mergeTrxsLabelLatchList == NULL) {
            SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc mergeTrxsLabelLatchList");
            return OUT_OF_MEMORY_INTER;
        }
        DbCreateList(mergeTrxsLabelLatchList, sizeof(void *), memCtx);
        trx->seRunCtx->mergeTrxsLabelLatchList = mergeTrxsLabelLatchList;
    }
    DbAppendListItem((DbListT *)trx->seRunCtx->mergeTrxsLabelLatchList, &mergeLabelLatchGaList);
    // 锁信息放到seRunCtx下后，子事务不必再维护这份信息
    trx->trx.optTrx.cloneTrxExtInfo.mergeLabelLatchGaList = NULL;
    return STATUS_OK_INTER;
}

StatusInter OptiTrxProcessMergeStateForCommit(TrxT *trx)
{
    if (trx->trx.optTrx.cloneTrxExtInfo.mergeInfo.validateState == 0) {
        return STATUS_OK_INTER;
    }
    // 只有合并进来的事务，才有带进来的表锁信息
    DB_ASSERT(trx->base.cloneType == TRX_ClONE_TYPE_CLONE_TRX);
    DB_ASSERT(trx->base.trxId == trx->trx.optTrx.cloneTrxExtInfo.mergeInfo.trxId);
    if (trx->seRunCtx->mergeTrxInfoList == NULL) {
        DbMemCtxT *memCtx = SeTransGetTrxMemCtxForTrxMerge(trx->seRunCtx);
        DbListT *mergeTrxInfoList = DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
        if (mergeTrxInfoList == NULL) {
            SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc mergeTrxInfoList");
            return OUT_OF_MEMORY_INTER;
        }
        DbCreateList(mergeTrxInfoList, sizeof(TrxMergeInfoT), memCtx);
        trx->seRunCtx->mergeTrxInfoList = mergeTrxInfoList;
    }
    DbAppendListItem((DbListT *)trx->seRunCtx->mergeTrxInfoList, &trx->trx.optTrx.cloneTrxExtInfo.mergeInfo);
    return STATUS_OK_INTER;
}

static StatusInter OptimisticCombineTrxCommitPhase1(TrxT *trx, DbListT *ancestorTrxList)
{
    // 外层已经加trxMgr的锁
    StatusInter ret = STATUS_OK_INTER;

    // 创建多个事务的整合labelReadView
    if (DbListGetItemCnt(ancestorTrxList) != 0) {
        for (uint32_t i = 0; i < DbListGetItemCnt(ancestorTrxList); ++i) {
            TrxT *ancestorTrx = *((TrxT **)DbListItem(ancestorTrxList, i));
            ret = OptiTrxLabelReadViewMerge(trx, ancestorTrx);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "sum labelReadView, trxId:%" PRIu64 ", ancestorTrx:%" PRIu64, trx->base.trxId,
                    ancestorTrx->base.trxId);
                return ret;
            }
        }
    }
    // 检查冲突
    ret = OptimisticTrxCheckConflict(trx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    // 事务按顺序提交
    if (DbListGetItemCnt(ancestorTrxList) != 0) {
        for (uint32_t i = 0; i < DbListGetItemCnt(ancestorTrxList); ++i) {
            TrxT *ancestorTrx = *((TrxT **)DbListItem(ancestorTrxList, i));
            ret = OptiTrxProcessCommitInTrxMgrLatch(ancestorTrx);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "ancestorTrx commit, trxId:%" PRIu64 ", ancestorTrx:%" PRIu64, trx->base.trxId,
                    ancestorTrx->base.trxId);
                return ret;
            }
        }
    }
    ret = OptiTrxProcessCommitInTrxMgrLatch(trx);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Combine trx commit unsucc, trxId:%" PRIu64, trx->base.trxId);
    }
    return ret;
}

void TrxHandleFlashContainerCtxForTrxMerge(TrxT *trx, SeRunCtxT *seRunCtx)
{
    DB_POINTER(trx);
    // 清理本次事务未使用的上下文，关闭本次事务使用的上下文
    for (uint32_t i = 0; i < trx->cntrCtxList.itemCnt; i++) {
        SeTrxContainerCtxT *container = &trx->cntrCtxList.ctxs[i];
        if (TrxContainerIsFree(container)) {
            continue;
        }
        // 非空容器
        if (TrxContainerIsUse(container) && (container->ctxHead.type == (uint16_t)TRX_HEAP_HANDLE ||
                                                container->ctxHead.type == (uint16_t)TRX_FIXED_HEAP_HANDLE)) {
            TrxContainerFlashSeRunCtxForTrxMerge(trx, container, seRunCtx);
        }
    }

    uint32_t listCnt = DbListGetItemCnt(&trx->cntrCtxList.list);
    // 下标i用于计数，下标listIdx记录实际数组当前下标，删除元素时listIdx要调整
    int32_t listIdx = 0;
    for (uint32_t i = 0; i < listCnt; i++, listIdx++) {
        DB_ASSERT(listIdx >= 0);
        SeTrxContainerCtxT **item = (SeTrxContainerCtxT **)DbListItem(&trx->cntrCtxList.list, (uint32_t)listIdx);
        SeTrxContainerCtxT *container = *item;
        if (TrxContainerIsUse(container) && (container->ctxHead.type == (uint16_t)TRX_HEAP_HANDLE ||
                                                container->ctxHead.type == (uint16_t)TRX_FIXED_HEAP_HANDLE)) {
            TrxContainerFlashSeRunCtxForTrxMerge(trx, container, seRunCtx);
        }
    }
}

static StatusInter OptimisticCombineTrxCommitPhase2(TrxT *trx, DbListT *ancestorTrxList)
{
    StatusInter ret = STATUS_OK_INTER;
    if (DbListGetItemCnt(ancestorTrxList) != 0) {
        for (uint32_t i = 0; i < DbListGetItemCnt(ancestorTrxList); ++i) {
            TrxT *ancestorTrx = *((TrxT **)DbListItem(ancestorTrxList, i));
            ancestorTrx->seRunCtx = trx->seRunCtx;
            ancestorTrx->seRunCtx->trx = ancestorTrx;
            TrxHandleFlashContainerCtxForTrxMerge(ancestorTrx, trx->seRunCtx);
            ret = SingleTrxClearOnCommitOrRollBack(ancestorTrx, true);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "ancestorTrx commit phase2 unsucc, trxId:%" PRIu64 ", ancestorTrx:%" PRIu64,
                    trx->base.trxId, ancestorTrx->base.trxId);
                return ret;
            }
            ret = OptiTrxProcessMergeLatch(ancestorTrx);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "process merge latch unsucc, trxId:%" PRIu64 ", ancestorTrx:%" PRIu64, trx->base.trxId,
                    ancestorTrx->base.trxId);
                return ret;
            }
            ret = OptiTrxProcessMergeStateForCommit(ancestorTrx);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
            // 释放事务申请的相关资源 TrxReleaseRes , 在 SingleTrxClearOnCommitOrRollBack 中执行
            ancestorTrx->base.processIdx = TRX_INVALID_PROCESS_IDX;
            TrxPoolFreeTrx((TrxMgrT *)ancestorTrx->trxMgr, ancestorTrx->base.trxSlot);  // 归还事务槽
        }
    }
    trx->seRunCtx->trx = trx;
    ret = SingleTrxClearOnCommitOrRollBack(trx, false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "trx commit phase2 unsucc, trxId:%" PRIu64, trx->base.trxId);
    }
    return ret;
}

StatusInter OptimisticCombineTrxCommit(TrxT *trx)
{
    DB_POINTER(trx);
    StatusInter ret = STATUS_OK_INTER;
    TrxMgrT *trxMgr = (TrxMgrT *)trx->trxMgr;
    if (trx->base.cloneType == TRX_ClONE_TYPE_CLONE_TRX) {
        SE_ERROR(INTERNAL_ERROR_INTER, "clone trx not support direct commit, trxId:%" PRIu64 "", trx->base.trxId);
        return INTERNAL_ERROR_INTER;
    }
    // redoBegin需要放到 trxMgr->latch 锁的外面，否则会死锁
    // 线程1：HeapUpdate - pageDesc（2,1）上锁，ReadViewPrepareForRc - 等待trxMgr->latch
    // 线程2：本函数持有trxMgr->latch，redoLogBegin时，redo文件空间不足，无法begin
    // checkpoint后台线程 ：脏页队列中有（2,1）被上了锁，无法刷盘，redo无法截断，redo文件无法回收
    RedoLogBegin(SeGetCurRedoCtx());
    DbRWLatchW(&trxMgr->latch);

#ifdef FEATURE_YANG
    ret = OptiTrxRetryCommitHandle(trxMgr, trx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
#endif
    // 获取祖先事务列表
    DbListT ancestorTrxList = {0};
    DbCreateList(&ancestorTrxList, sizeof(TrxT *), ((SeInstanceT *)trx->seRunCtx->seIns)->seCloneTrxMemCtx);
    ret = OptimisticTrxGetAncestorTrxList(trx, &ancestorTrxList);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get ancestor trx list");
        DbDestroyList(&ancestorTrxList);
        DbRWUnlatchW(&trxMgr->latch);
        (void)RedoLogEnd(SeGetCurRedoCtx(), false);
        return ret;
    }
    // 一定要按照trxId顺序！！！由于trxId不可能一样，所以可以用qsort
    DbListSort(&ancestorTrxList, TrxListOrderCmp);

    ret = OptimisticCombineTrxCommitPhase1(trx, &ancestorTrxList);
    if (ret != STATUS_OK_INTER) {
        DbDestroyList(&ancestorTrxList);
        DbRWUnlatchW(&trxMgr->latch);
        (void)RedoLogEnd(SeGetCurRedoCtx(), false);
        return ret;
    }

    DbRWUnlatchW(&trxMgr->latch);
    (void)RedoLogEnd(SeGetCurRedoCtx(), true);

    ret = OptimisticCombineTrxCommitPhase2(trx, &ancestorTrxList);
    if (ret != STATUS_OK_INTER) {
        DbDestroyList(&ancestorTrxList);
        return ret;
    }

    // 当前乐观绑定RR隔离级别，事务提交成功，启动purger
    TrxStartUndoPurger(trx->seRunCtx->seIns);
    DbDestroyList(&ancestorTrxList);
    return ret;
}

StatusInter OptiCombineTrxRollBackAfterTrxMgrLatch(TrxT *trx, DbListT *ancestorTrxList)
{
    StatusInter ret = SingleTrxClearOnCommitOrRollBack(trx, false);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "combineTrx rollback: self, trxId:%" PRIu64, trx->base.trxId);
        return ret;
    }
    if (DbListGetItemCnt(ancestorTrxList) != 0) {
        for (uint32_t i = 0; i < DbListGetItemCnt(ancestorTrxList); ++i) {
            TrxT *ancestorTrx = *((TrxT **)DbListItem(ancestorTrxList, i));
            DB_ASSERT(ancestorTrx->trxMgr != NULL);
            ancestorTrx->seRunCtx = trx->seRunCtx;
            ancestorTrx->seRunCtx->trx = ancestorTrx;
            TrxHandleFlashContainerCtxForTrxMerge(ancestorTrx, trx->seRunCtx);
            ret = SingleTrxClearOnCommitOrRollBack(ancestorTrx, true);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(
                    ret, "combineTrx rollback: ancestor, idx:%" PRIu32 ", trxId:%" PRIu64, i, ancestorTrx->base.trxId);
                return ret;
            }
            ret = OptiTrxProcessMergeLatch(ancestorTrx);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "combineTrx rollback: ancestor process merge latch, idx:%" PRIu32 ", trxId:%" PRIu64, i,
                    ancestorTrx->base.trxId);
                return ret;
            }
            // 释放事务申请的相关资源 TrxReleaseRes , 在 SingleTrxClearOnCommitOrRollBack 中执行
            ancestorTrx->base.processIdx = TRX_INVALID_PROCESS_IDX;
            TrxPoolFreeTrx((TrxMgrT *)ancestorTrx->trxMgr, ancestorTrx->base.trxSlot);  // 归还事务槽
        }
    }
    trx->seRunCtx->trx = trx;
    return STATUS_OK_INTER;
}

StatusInter OptimisticCombineTrxRollBack(TrxT *trx)
{
    DB_POINTER(trx);
    StatusInter ret = STATUS_OK_INTER;
    TrxMgrT *trxMgr = (TrxMgrT *)trx->trxMgr;
    // redoBegin需要放到 trxMgr->latch 锁的外面，否则会死锁
    // 线程1：HeapUpdate - pageDesc（2,1）上锁，ReadViewPrepareForRc - 等待trxMgr->latch
    // 线程2：本函数持有trxMgr->latch，redoLogBegin时，redo文件空间不足，无法begin
    // checkpoint后台线程 ：脏页队列中有（2,1）被上了锁，无法刷盘，redo无法截断，redo文件无法回收
    RedoLogBegin(SeGetCurRedoCtx());
    DbRWLatchW(&trxMgr->latch);

    // 获取祖先事务列表
    DbListT ancestorTrxList = {0};
    DB_ASSERT(trx->trxCloneMemCtx != NULL);
    DbCreateList(&ancestorTrxList, sizeof(TrxT *), ((SeInstanceT *)trx->seRunCtx->seIns)->seCloneTrxMemCtx);
    ret = OptimisticTrxGetAncestorTrxList(trx, &ancestorTrxList);
    if (ret != STATUS_OK_INTER) {
        DbDestroyList(&ancestorTrxList);
        DbRWUnlatchW(&trxMgr->latch);
        (void)RedoLogEnd(SeGetCurRedoCtx(), false);
        return ret;
    }
    // 一定要按照trxId逆序！！！
    DbListSort(&ancestorTrxList, TrxListDescendCmp);
    // 回滚不用冲突检查
    TrxGetCommitTs(trx);
    if (DbListGetItemCnt(&ancestorTrxList) != 0) {
        for (uint32_t i = 0; i < DbListGetItemCnt(&ancestorTrxList); ++i) {
            TrxT *ancestorTrx = *((TrxT **)DbListItem(&ancestorTrxList, i));
            DB_ASSERT(ancestorTrx->base.state == TRX_STATE_ACTIVE);
            DB_ASSERT(ancestorTrx->trxMgr != NULL);
            ancestorTrx->base.state = TRX_STATE_ROLLBACK;
            TrxGetCommitTs(ancestorTrx);
        }
    }

    DbRWUnlatchW(&trxMgr->latch);
    (void)RedoLogEnd(SeGetCurRedoCtx(), true);

    ret = OptiCombineTrxRollBackAfterTrxMgrLatch(trx, &ancestorTrxList);
    DbDestroyList(&ancestorTrxList);
    return ret;
}

StatusInter OptimisticCloneOrSuccessorTrxRollBack(TrxT *trx)
{
    DB_POINTER(trx);
    TrxT *parentTrx = NULL;
    Status ret = SeTransGetTrxByTrxId(trx->seRunCtx, trx->trx.optTrx.relationTrxInfo.parentTrxId, false, &parentTrx);
    if (ret != GMERR_OK) {
        if (trx->base.cloneType == TRX_ClONE_TYPE_SUCCESSOR_TRX) {
            DB_LOG_ERROR(ret, "parentTrx not exist, trxId:%" PRIu64 ".", trx->trx.optTrx.relationTrxInfo.parentTrxId);
            return DbGetStatusInterErrno(ret);
        } else {
            DB_ASSERT(trx->base.cloneType == TRX_ClONE_TYPE_CLONE_TRX);
            DB_ASSERT(parentTrx == NULL);
            // 有可能父事务所在连接直接提交了，因此找不到，这种情况单独回滚该子事务
        }
    }
    TrxT *successorTrx = NULL;
    if (trx->base.cloneType == TRX_ClONE_TYPE_CLONE_TRX) {
        ret = SeTransGetTrxByTrxId(trx->seRunCtx, trx->trx.optTrx.relationTrxInfo.parentCloneId, true, &successorTrx);
    }

    if (parentTrx != NULL) {
        // 没有活跃的兄弟事务存在，把整个组合链回滚
        if ((trx->base.cloneType == TRX_ClONE_TYPE_SUCCESSOR_TRX &&
                !parentTrx->trx.optTrx.relationTrxInfo.childTrxActive) ||
            (trx->base.cloneType == TRX_ClONE_TYPE_CLONE_TRX &&
                !parentTrx->trx.optTrx.relationTrxInfo.successorTrxActive)) {
            return OptimisticCombineTrxRollBack(trx);
        }
    }

    TrxMgrT *trxMgr = (TrxMgrT *)trx->trxMgr;
    // 有兄弟事务存在，只回滚自己
    RedoLogBegin(SeGetCurRedoCtx());
    DbRWLatchW(&trxMgr->latch);
    TrxGetCommitTs(trx);
    if (parentTrx != NULL) {
        if (trx->base.cloneType == TRX_ClONE_TYPE_SUCCESSOR_TRX) {
            DB_ASSERT(trx->base.trxId == parentTrx->trx.optTrx.relationTrxInfo.successorTrxId);
            parentTrx->trx.optTrx.relationTrxInfo.successorTrxActive = false;
        } else {
            DB_ASSERT(trx->base.cloneType == TRX_ClONE_TYPE_CLONE_TRX);
            DB_ASSERT(trx->base.trxId == parentTrx->trx.optTrx.relationTrxInfo.childTrxId);
            parentTrx->trx.optTrx.relationTrxInfo.childTrxActive = false;
            // 继承者事务在兄弟事务（克隆事务）回滚后，可以升级为父事务
            if (successorTrx != NULL) {
                DB_ASSERT(successorTrx->base.trxId == parentTrx->trx.optTrx.relationTrxInfo.successorTrxId);
                successorTrx->base.cloneType = TRX_ClONE_TYPE_CLONABLE_TRX;
            }
        }
    }
    DbRWUnlatchW(&trxMgr->latch);
    (void)RedoLogEnd(SeGetCurRedoCtx(), true);

    return SingleTrxClearOnCommitOrRollBack(trx, false);
}

StatusInter OptimisticCloneTypeTrxCommitOrRollBack(TrxT *trx)
{
    if (trx->base.state == TRX_STATE_ACTIVE) {
        // 提交
        DB_ASSERT(trx->base.cloneType != TRX_ClONE_TYPE_CLONE_TRX);  // 希望EE层做校验
        return OptimisticCombineTrxCommit(trx);
    } else {
        // 回滚
        if (trx->base.cloneType == TRX_ClONE_TYPE_CLONABLE_TRX) {
            return OptimisticCombineTrxRollBack(trx);
        } else {
            return OptimisticCloneOrSuccessorTrxRollBack(trx);
        }
    }
}

// 当前不支持类型为 TRX_ClONE_TYPE_CLONE_TRX 的事务使用该函数
// 该函数当前只提供长事务超时自动回滚使用，子事务暂时不支持
bool SeTransIsTargetTrxOldestParent(const SeRunCtxHdT seRunCtx, TrxIdT targetTrxId)
{
    DB_POINTER(seRunCtx);
    TrxT *trx = seRunCtx->trx;
    if (trx->base.trxId == targetTrxId) {
        // 目标ID就是当前ID，直接返回true
        return true;
    }
    if (trx->base.cloneType == TRX_ClONE_TYPE_DEFAULT_TRX || trx->base.cloneType == TRX_ClONE_TYPE_CLONE_TRX) {
        // 暂不支持子事务使用该函数
        return false;
    }
    ReadViewT *readView = &trx->trx.base.readView;
    if (!readView->isOpen) {
        SE_ERROR(INTERNAL_ERROR_INTER, "readview is closed");
        return false;
    }

    if (DbIsShmPtrValid(readView->parentTrxIdsShm)) {
        TrxIdListT *parentTrxIds = (TrxIdListT *)DbShmPtrToAddr(readView->parentTrxIdsShm);
        if (SECUREC_UNLIKELY(parentTrxIds == NULL)) {
            SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "transfer parentTrxIdsShm to ptr");
            return false;
        }
        if (parentTrxIds->listLen == 0) {
            return false;
        }
        if (parentTrxIds->trxIds[0] == targetTrxId) {
            return true;
        }
    }

    return false;
}
#endif

#ifdef __cplusplus
}
#endif
