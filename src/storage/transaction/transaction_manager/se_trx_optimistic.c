/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: trx optimistic 相关函数
 * Author: GMDBv5 SE Team
 * Create: 2023/06/06
 */
#include "se_trx_inner.h"
#include "se_trx_mgr.h"
#include "se_log.h"
#include "se_undo.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef FEATURE_YANG
StatusInter InitSeOptiTrxRetryCommitInfo(TrxMgrT *trxMgr, SeInstanceT *seIns)
{
    trxMgr->optiTrxRetryInfo.commitState = OPTI_TRX_COMMIT_NORMAL;
    trxMgr->optiTrxRetryInfo.retryTrxId = 0;
    Status ret = DbQueueCreateWithSize(
        seIns->seServerMemCtx, &trxMgr->optiTrxRetryInfo.retryTrxQueue, sizeof(TrxIdT), OPTI_TRX_RETRY_QUEUE_STEP);
    if (ret != GMERR_OK) {
        SE_ERROR(DbGetExternalErrno(ret), "create retry queue");
        return DbGetExternalErrno(ret);
    }
    // 提交阶段加入等待队列，队列不够会申请内存扩容，有内存不足风险，按光启最大连接数100+后台线程数进行初始化，避免内存申请
    ret = DbQueueCreateWithSize(
        seIns->seServerMemCtx, &trxMgr->optiTrxRetryInfo.commitTrxQueue, sizeof(TrxIdT), OPTI_TRX_COMMIT_QUEUE_STEP);
    if (ret != GMERR_OK) {
        SE_ERROR(DbGetExternalErrno(ret), "create commit queue");
        DbQueueDestroy(&trxMgr->optiTrxRetryInfo.retryTrxQueue);
        return DbGetExternalErrno(ret);
    }
    trxMgr->optiTrxRetryInfo.isInit = true;
    return STATUS_OK_INTER;
}
#endif

Status SeOptimisticTrxConflictCheck(const SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);
    StatusInter ret = STATUS_OK_INTER;

    SeRunCtxT *runCtx = seRunCtx;
    TrxT *trx = (TrxT *)runCtx->trx;
    TrxMgrT *trxMgr = (TrxMgrT *)trx->trxMgr;

    if (TrxGetTrxType(trx) == OPTIMISTIC_TRX && trx->base.state == TRX_STATE_ACTIVE &&
        trx->trx.optTrx.labelReadView.isTrxCommitCheckActive) {
        DbRWLatchR(&trxMgr->latch);
        ret = OptiTrxValidateLabelTrxIdForCommit(trx, NULL, NULL, NULL);
        DbRWUnlatchR(&trxMgr->latch);
    }

    return DbGetExternalErrno(ret);
}

StatusInter OptimisticTrxCommitActive(TrxT *trx, bool *isTrxCommitSuccess)
{
    StatusInter ret = STATUS_OK_INTER;
    if (trx->trx.optTrx.labelReadView.isTrxCommitCheckActive) {
        // 如果事务成功提交, 记录事务操作过的每种表的数量, 作为DFX信息
        uint32_t edgeLabelCnt = 0;
        uint32_t kvLabelCnt = 0;
        uint32_t vertexLabelCnt = 0;
        ret = OptiTrxValidateLabelTrxIdForCommit(trx, &edgeLabelCnt, &kvLabelCnt, &vertexLabelCnt);
        if (ret == STATUS_OK_INTER) {
            // 需要和 TrxCommitInTrxList 在同一原子操作和TrxMgr 锁内，避免并发事务看到不一致的状态
            ret = TrxMarkContainerRecCnt(trx);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
            // 这里忽略回调函数的返回值，仅为了维护视图字段信息
            (void)TrxCommitCallBack(trx);
            // 校验成功, 更新操作过的label的trxId
            OptiTrxUpdLabelTrxIdForCommit(trx, edgeLabelCnt, kvLabelCnt, vertexLabelCnt);
            TrxCommitInTrxList(trx);
            DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP, "OptimisticTrxCommit trxId:%" PRIu64, trx->base.trxId);
            // 根据 RecoveryUndoCleanUpCommitSeg 的逻辑，事务已经提交的版本默认retainedUndo已经在历史版本链表上
            // 所以此处需要将 TrxUndoSetTrxState 与 TrxUndoRetainedAddToHistory 放在一个原子范围内
            ret = TrxUndoRetainedAddToHistory(trx, trx->trx.base.retainedUndo);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
            *isTrxCommitSuccess = true;
            ret = TrxUndoSetTrxState(trx, TRX_STATE_COMMITTED);
        } else if (trx->base.isInteractive) {
            /* 校验失败，需要用户手动回滚，不修改事务状态 */
            DEBUG_EXTRAMSG(EXTRAMSG_LOG_HEAP, "OptimisticTrxRollback trxId:%" PRIu64, trx->base.trxId);
        } else {
            // 隐式事务，转为rollback，回滚该事务
            StatusInter ret2 = TrxUndoSetTrxState(trx, TRX_STATE_ROLLBACK);
            if (ret2 != STATUS_OK_INTER) {
                SE_ERROR(ret2, "Set Optimistic Trx Rollback, trxId:%" PRIu64, trx->base.trxId);
                DB_ASSERT(false);
            }
        }
    } else {
        // 未激活检查时，说明该事务没做过DML操作, 不会冲突
        // 如果是savePoint回滚回来的，recCnt肯定为0
        DB_ASSERT(trx->trx.base.retainedUndo == NULL || trx->trx.base.retainedUndo->recCnt == 0);
        DB_ASSERT(trx->trx.base.normalUndo == NULL || trx->trx.base.normalUndo->recCnt == 0);
        TrxCommitInTrxList(trx);
        ret = TrxUndoSetTrxState(trx, TRX_STATE_COMMITTED);
    }
    return ret;
}

#ifdef FEATURE_YANG
void OptiTrxRetryStateChange(TrxMgrT *trxMgr, OptiTrxRetryCommitStateE curState)
{
    if (curState == OPTI_TRX_COMMIT_NORMAL) {
        trxMgr->optiTrxRetryInfo.commitState = OPTI_TRX_RETRY_COMMIT;
    } else if (curState == OPTI_TRX_RETRY_COMMIT) {
        trxMgr->optiTrxRetryInfo.commitState = OPTI_TRX_CHANGE_STATE;
    } else {
        DB_ASSERT(curState == OPTI_TRX_CHANGE_STATE);
        trxMgr->optiTrxRetryInfo.commitState =
            DbQueueIsEmpty(&trxMgr->optiTrxRetryInfo.retryTrxQueue) ? OPTI_TRX_COMMIT_NORMAL : OPTI_TRX_RETRY_COMMIT;
    }
}

void OptiRetryTrxBeginHandle(TrxT *trx, StatusInter ret, char *peerProcInfo, uint32_t peerInfoMaxLen)
{
    DB_ASSERT(strlen(peerProcInfo) < peerInfoMaxLen);
    TrxMgrT *trxMgr = (TrxMgrT *)trx->trxMgr;
    DbRWLatchW(&trxMgr->latch);
    if (ret != STATUS_OK_INTER) {
        OptiTrxRetryStateChange(trxMgr, trxMgr->optiTrxRetryInfo.commitState);
        trxMgr->optiTrxRetryInfo.retryTrxId = 0;
    } else {
        DB_ASSERT(trx->trxMgr->optiTrxRetryInfo.retryTrxId == DB_MAX_UINT64);
        trxMgr->optiTrxRetryInfo.retryTrxId = trx->base.trxId;
        // 重试事务DFX打印
        DB_LOG_INFO_UNFOLD("retryTrx begin, trxId:%" PRIu64 ", peerProcInfo:%s", trx->base.trxId, peerProcInfo);
    }
    DbRWUnlatchW(&trxMgr->latch);
}

StatusInter OptiTrxPushRetryTrxQueue(TrxMgrT *trxMgr, TrxIdT curTrxId)
{
    Status ret = DbQueuePush(&trxMgr->optiTrxRetryInfo.retryTrxQueue, &curTrxId);
    if (ret != GMERR_OK) {
        SE_ERROR(DbGetStatusInterErrno(ret), "push retry queue, commitState:%" PRIu32,
            (uint32_t)trxMgr->optiTrxRetryInfo.commitState);
        return DbGetInternalErrno(ret);
    }
    return STATUS_OK_INTER;
}

StatusInter OptiTrxCheckRetryQueueFirstItem(TrxMgrT *trxMgr, TrxIdT curTrxId)
{
    // 此时已在队列中，有以下两种情况
    // (1)OPTI_TRX_CHANGE_STATE, 检查提交事务队列是否为空，不为空则继续等待
    // 为空，则检查是否处于首位，首位可begin，否则继续等待
    // (2)OPTI_TRX_RETRY_COMMIT, 检查retryTrxId是否为0，不为0则继续等待
    // 为0，则检查是否处于首位，首位可begin，否则继续等待
    if (trxMgr->optiTrxRetryInfo.commitState == OPTI_TRX_CHANGE_STATE) {
        DB_ASSERT(trxMgr->optiTrxRetryInfo.retryTrxId == 0);
        if (!DbQueueIsEmpty(&trxMgr->optiTrxRetryInfo.commitTrxQueue)) {
            return NO_DATA_INTER;
        }
    } else {
        if (trxMgr->optiTrxRetryInfo.retryTrxId != 0) {
            return NO_DATA_INTER;
        }
    }
    TrxIdT firstTrxId;
    Status ret = DbQueueFront(&trxMgr->optiTrxRetryInfo.retryTrxQueue, &firstTrxId);
    DB_ASSERT(ret == GMERR_OK);
    if (firstTrxId == curTrxId) {
        if (trxMgr->optiTrxRetryInfo.commitState == OPTI_TRX_CHANGE_STATE) {
            OptiTrxRetryStateChange(trxMgr, trxMgr->optiTrxRetryInfo.commitState);  // 先切换状态再出队
        }
        ret = DbQueuePop(&trxMgr->optiTrxRetryInfo.retryTrxQueue, &firstTrxId);
        DB_ASSERT(ret == GMERR_OK);
        trxMgr->optiTrxRetryInfo.retryTrxId = DB_MAX_UINT64;
        return STATUS_OK_INTER;
    }
    return NO_DATA_INTER;
}

#define WAIT_TIMES 500
#define SLEEP_TIME_US 10000  // 10ms

typedef struct {
    bool isRetryTrxBegin;  // 是否是重试事务begin
    uint64_t startTime;    // while循环的开始时间
    uint64_t id;  // 当isRetryTrxBegin为true时，因为还未开启事务，该id只是一个静态变量；为false时为事务id
    uint32_t waitTimes;  // sleep的次数
    char *peerProcInfo;
} OptiRetryTrxDfxInfoT;

void OptiRetryTrxDfxInfoPrint(TrxMgrT *trxMgr, OptiRetryTrxDfxInfoT *dfxInfo)
{
    uint64_t curTime = DbRdtsc();
    uint64_t costTime = DbToUseconds(curTime - dfxInfo->startTime);
    uint32_t commitTrxQueueLen = DbQueueSize(&trxMgr->optiTrxRetryInfo.commitTrxQueue);
    uint32_t retryTrxQueueLen = DbQueueSize(&trxMgr->optiTrxRetryInfo.retryTrxQueue);
    if (dfxInfo->isRetryTrxBegin) {
        DB_LOG_INFO_UNFOLD("beginWait curId:%" PRIu64 ", commitQueLen:%" PRIu32 ", retryQueLen:%" PRIu32
                           ", state:%" PRIu32 ", waitTimes:%" PRIu32 ", costTime(us):%" PRIu64 ", peerInfo:%s",
            dfxInfo->id, commitTrxQueueLen, retryTrxQueueLen, trxMgr->optiTrxRetryInfo.commitState, dfxInfo->waitTimes,
            costTime, dfxInfo->peerProcInfo);
    } else {
        DB_LOG_INFO_UNFOLD("commitWait trxId:%" PRIu64 ", commitQueLen:%" PRIu32 ", retryQueLen:%" PRIu32
                           ", state:%" PRIu32 ", waitTimes:%" PRIu32 ", costTime(us):%" PRIu64 ", peerInfo:%s",
            dfxInfo->id, commitTrxQueueLen, retryTrxQueueLen, trxMgr->optiTrxRetryInfo.commitState, dfxInfo->waitTimes,
            costTime, dfxInfo->peerProcInfo);
    }
}

StatusInter OptiTrxRetryBegin(TrxT *trx, char *peerProcInfo, uint32_t peerInfoMaxLen)
{
    DB_ASSERT(strlen(peerProcInfo) < peerInfoMaxLen);
    TrxMgrT *trxMgr = (TrxMgrT *)trx->trxMgr;
    bool isInQueue = false;
    // 此处还未分配trxId，用static变量只是为了找到在队列中的位置，需要在trxMgr->latch的保护下++
    static TrxIdT tmpTrxId = 1;
    TrxIdT curTrxId = 0;
    uint32_t waitTimes = 0;
    uint64_t startTime = DbRdtsc();
    do {
        DbRWLatchW(&trxMgr->latch);
        if (trxMgr->optiTrxRetryInfo.commitState == OPTI_TRX_COMMIT_NORMAL) {
            DB_ASSERT(DbQueueIsEmpty(&trxMgr->optiTrxRetryInfo.commitTrxQueue));
            DB_ASSERT(DbQueueIsEmpty(&trxMgr->optiTrxRetryInfo.retryTrxQueue));
            OptiTrxRetryStateChange(trxMgr, trxMgr->optiTrxRetryInfo.commitState);
            // 这里临时赋值为最大值，避免下一个重试事务并发执行
            // 外面begin事务成功后会retryTrxId会真正赋值
            trxMgr->optiTrxRetryInfo.retryTrxId = DB_MAX_UINT64;
            DbRWUnlatchW(&trxMgr->latch);
            return STATUS_OK_INTER;
        } else {
            if (isInQueue) {
                StatusInter ret = OptiTrxCheckRetryQueueFirstItem(trxMgr, curTrxId);
                if (ret == STATUS_OK_INTER) {
                    DbRWUnlatchW(&trxMgr->latch);
                    return STATUS_OK_INTER;
                }
            } else {
                isInQueue = true;
                curTrxId = tmpTrxId++;
                StatusInter ret = OptiTrxPushRetryTrxQueue(trxMgr, curTrxId);
                if (ret != STATUS_OK_INTER) {
                    DbRWUnlatchW(&trxMgr->latch);
                    return ret;
                }
            }
        }
        DbRWUnlatchW(&trxMgr->latch);
        usleep(SLEEP_TIME_US);
        waitTimes++;
        if (waitTimes % WAIT_TIMES == 0) {  // 每等待5秒会打印1次
            OptiRetryTrxDfxInfoT dfxInfo = {.isRetryTrxBegin = true,
                .startTime = startTime,
                .id = curTrxId,
                .waitTimes = waitTimes,
                .peerProcInfo = peerProcInfo};
            OptiRetryTrxDfxInfoPrint(trxMgr, &dfxInfo);
        }
    } while (1);
}

StatusInter OptiTrxPushCommitTrxQueue(TrxMgrT *trxMgr, TrxT *trx)
{
    Status ret = DbQueuePush(&trxMgr->optiTrxRetryInfo.commitTrxQueue, &trx->base.trxId);
    if (ret != GMERR_OK) {
        SE_ERROR(DbGetStatusInterErrno(ret), "trxId:%" PRIu64 ", commitState:%" PRIu32 ", isRetryTrx:%" PRIu8,
            trx->base.trxId, (uint32_t)trxMgr->optiTrxRetryInfo.commitState, trx->base.isRetryTrx);
        return DbGetInternalErrno(ret);
    }
    return STATUS_OK_INTER;
}

StatusInter OptiTrxCheckCommitQueueFirstItem(TrxMgrT *trxMgr, TrxT *trx)
{
    // 已在队列，检查是否处于首位，首位可提交，否则继续等待
    TrxIdT firstTrxId;
    Status ret = DbQueueFront(&trxMgr->optiTrxRetryInfo.commitTrxQueue, &firstTrxId);
    DB_ASSERT(ret == GMERR_OK);
    if (firstTrxId == trx->base.trxId) {
        ret = DbQueuePop(&trxMgr->optiTrxRetryInfo.commitTrxQueue, &firstTrxId);
        DB_ASSERT(ret == GMERR_OK);
        if (DbQueueIsEmpty(&trxMgr->optiTrxRetryInfo.commitTrxQueue)) {
            OptiTrxRetryStateChange(trxMgr, trxMgr->optiTrxRetryInfo.commitState);
        }
        return STATUS_OK_INTER;
    }
    return NO_DATA_INTER;
}

StatusInter OptiNormalTrxCommitHandle(TrxMgrT *trxMgr, TrxT *trx, char *peerProcInfo, uint32_t peerInfoMaxLen)
{
    DB_ASSERT(!trx->base.isRetryTrx);
    DB_ASSERT(strlen(peerProcInfo) < peerInfoMaxLen);
    bool isInQueue = false;
    uint32_t waitTimes = 0;
    uint64_t startTime = DbRdtsc();
    do {
        Status ret;
        if (trxMgr->optiTrxRetryInfo.commitState == OPTI_TRX_RETRY_COMMIT) {
            if (!isInQueue) {  // 如果是业务事务且没在队列中，则加入到队列，如果已经在队列则进入下一轮等待
                isInQueue = true;
                if ((ret = OptiTrxPushCommitTrxQueue(trxMgr, trx)) != STATUS_OK_INTER) {
                    DB_ASSERT(false);  // 提交队列已经按照最大事务数量进行初始化，应该不会出现扩容失败的情况
                    return ret;
                }
                // 加入队列ok的情况，进入下一轮等待
            }
        } else {
            DB_ASSERT(trxMgr->optiTrxRetryInfo.commitState == OPTI_TRX_CHANGE_STATE);
            DB_ASSERT(trxMgr->optiTrxRetryInfo.retryTrxId == 0);
            if (isInQueue) {
                ret = OptiTrxCheckCommitQueueFirstItem(trxMgr, trx);
                if (ret == STATUS_OK_INTER) {  //  处于队列首位即可出队执行，非ok的错误码进入下一轮等待
                    return STATUS_OK_INTER;
                }
            } else if (DbQueueIsEmpty(&trxMgr->optiTrxRetryInfo.commitTrxQueue)) {
                // 没在队列且队列为空，修改状态，可直接执行
                OptiTrxRetryStateChange(trxMgr, trxMgr->optiTrxRetryInfo.commitState);
                return STATUS_OK_INTER;
            } else {  // 队列不为空，需要保证业务的提交顺序，加入队列中等待提交
                isInQueue = true;
                ret = OptiTrxPushCommitTrxQueue(trxMgr, trx);
                if (ret != STATUS_OK_INTER) {
                    DB_ASSERT(false);  // 提交队列已经按照最大事务数量进行初始化，应该不会出现扩容失败的情况
                    return ret;
                }
                // 加入队列ok的情况，进入下一轮等待
            }
        }
        DbRWUnlatchW(&trxMgr->latch);
        usleep(SLEEP_TIME_US);
        waitTimes++;
        if (waitTimes % WAIT_TIMES == 0) {  // 每等待5秒会打印1次
            OptiRetryTrxDfxInfoT dfxInfo = {.isRetryTrxBegin = false,
                .startTime = startTime,
                .id = trx->base.trxId,
                .waitTimes = waitTimes,
                .peerProcInfo = peerProcInfo};
            OptiRetryTrxDfxInfoPrint(trxMgr, &dfxInfo);
        }
        DbRWLatchW(&trxMgr->latch);
    } while (1);
}

void OptiRetryTrxCommitHandle(TrxMgrT *trxMgr, TrxT *trx)
{
    DB_ASSERT(trx->base.isRetryTrx);
    DB_ASSERT(trxMgr->optiTrxRetryInfo.commitState == OPTI_TRX_RETRY_COMMIT);
    DB_ASSERT(trx->base.trxId == trxMgr->optiTrxRetryInfo.retryTrxId);
    OptiTrxRetryStateChange(trxMgr, trxMgr->optiTrxRetryInfo.commitState);
    trxMgr->optiTrxRetryInfo.retryTrxId = 0;
    // 重试事务DFX打印
    DB_LOG_INFO_UNFOLD(
        "retryTrx end, trxId:%" PRIu64 ", trxState:%" PRIu32, trx->base.trxId, (uint32_t)trx->base.state);
}

StatusInter OptiTrxRetryCommitHandle(TrxMgrT *trxMgr, TrxT *trx)
{
    if (!trx->base.isRetryTrx) {
        if (!trxMgr->optiTrxRetryInfo.isInit || trxMgr->optiTrxRetryInfo.commitState == OPTI_TRX_COMMIT_NORMAL ||
            trx->base.state == TRX_STATE_ROLLBACK) {
            // 未初始化或者常规状态或者是要回滚，与原有流程一致，直接返回
            return STATUS_OK_INTER;
        }
        TrxConnStateInfoT info = {0};
        char peerProcInfo[SE_CONN_INFO_MAX_LEN] = {0};
        Status retTem = SeTrxGetConnStateByConnId(trx->base.connId, &info, NULL);
        if (retTem == GMERR_OK) {  // 如果失败只是DFX功能不全, 不影响主流程
            // peerProcInfo: connId-pid-tid-auditUserInfo(uid-processName)-threadName
            (void)sprintf_s(peerProcInfo, SE_CONN_INFO_MAX_LEN, "%" PRIu16 "-%" PRIu32 "-%" PRIu64 "-%s-%s",
                info.connId, info.pid, info.peerTid, info.auditUserInfo, info.peerThreadName);
        }
        return OptiNormalTrxCommitHandle(trxMgr, trx, peerProcInfo, SE_CONN_INFO_MAX_LEN);
    } else {
        OptiRetryTrxCommitHandle(trxMgr, trx);
        return STATUS_OK_INTER;
    }
}
#endif

StatusInter OptimisticTrxCommit(TrxT *trx)
{
    DB_POINTER(trx);
    StatusInter ret = STATUS_OK_INTER;
    TrxMgrT *trxMgr = (TrxMgrT *)trx->trxMgr;
    bool isTrxCommitSuccess = false;
    // redoBegin需要放到 trxMgr->latch 锁的外面，否则会死锁
    // 线程1：HeapUpdate - pageDesc（2,1）上锁，ReadViewPrepareForRc - 等待trxMgr->latch
    // 线程2：本函数持有trxMgr->latch，redoLogBegin时，redo文件空间不足，无法begin
    // checkpoint后台线程 ：脏页队列中有（2,1）被上了锁，无法刷盘，redo无法截断，redo文件无法回收
    RedoLogBegin(SeGetCurRedoCtx());
    DbRWLatchW(&trxMgr->latch);
#ifdef FEATURE_YANG
    ret = OptiTrxRetryCommitHandle(trxMgr, trx);
    if (ret != STATUS_OK_INTER) {
        DbRWUnlatchW(&trxMgr->latch);
        return ret;
    }
#endif
    // 回滚时不用检查冲突
    if (trx->base.state == TRX_STATE_ACTIVE) {
        ret = OptimisticTrxCommitActive(trx, &isTrxCommitSuccess);
        // 写写冲突，交互式事务需要用户手动回滚，隐式事务应该将trx state设置为rollback
        if (ret != STATUS_OK_INTER && (trx->base.isInteractive || trx->base.state != TRX_STATE_ROLLBACK)) {
            DbRWUnlatchW(&trxMgr->latch);
            DB_ASSERT(!trx->base.isRetryTrx);  // 重试事务提交一定成功
            (void)RedoLogEnd(SeGetCurRedoCtx(), false);
            return ret;
        }
    }
    TrxGetCommitTs(trx);
    DbRWUnlatchW(&trxMgr->latch);
    (void)RedoLogEnd(SeGetCurRedoCtx(), true);

    StatusInter ret2 = TrxClearOnCommitOrRollback(trx);
    if (ret2 != STATUS_OK_INTER) {
        SE_ERROR(ret2, "trx clear on commit or rollback, trx id %" PRIu64 ", type %" PRIu32 ", state %" PRIu32,
            trx->base.trxId, (uint32_t)trx->base.trxType, (uint32_t)trx->base.state);
        if (SeGetStorageStatus(trx->seRunCtx->seIns) == SE_ON_DISK_EMRGNCY) {
            return DATABASE_NOT_AVAILABLE_INTER;  // DB不可用状态下，不释放事务锁
        }
        DB_ASSERT(false);
    }
    DB_ASSERT(trx->trx.pesTrx.holdLockNum == 0);
    // 释放namespace readview
    OptiTrxCloseLabelReadview(trx);
    TrxCloseSavepoint(trx);
    if (isTrxCommitSuccess) {
        // 当前乐观绑定RR隔离级别，事务提交成功，启动purger
        TrxStartUndoPurger(trx->seRunCtx->seIns);
    }
    return ret == STATUS_OK_INTER ? ret2 : ret;
}

#ifdef __cplusplus
}
#endif
