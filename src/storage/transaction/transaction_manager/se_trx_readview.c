/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: trx readview 相关函数
 * Author: GMDBv5 SE Team
 * Create: 2024/04/23
 */

#include "se_trx_inner.h"
#include "se_trx_mgr.h"
#include "se_log.h"
#include "dm_meta_prop_label.h"
#include "se_daemon.h"
#include "db_crash_debug.h"
#include "db_inter_process_rwlatch.h"
#include "db_mem_context_internal.h"

#ifdef __cplusplus
extern "C" {
#endif

#define TRX_ID_LIST_MOVE_THRESHOLD (10)

inline static TrxIdT TrxIdListGetFront(const TrxIdListT *ids)
{
    DB_POINTER(ids);
    return (ids->listLen == 0) ? DB_INVALID_TRX_ID : ids->trxIds[0];
}

bool TrxIdListLookup(const TrxIdListT *ids, TrxIdT trxId)
{
    DB_POINTER(ids);

    TrxIdT *trx = (TrxIdT *)bsearch(&trxId, ids->trxIds, ids->listLen, sizeof(TrxIdT), TrxIdCmp);
    return (trx != NULL);
}

inline StatusInter TrxIdListPushbackId(TrxIdListT *ids, TrxIdT trxId)
{
    DB_POINTER(ids);
    if (ids->listLen >= ids->capacity) {
        SE_LAST_ERROR(INVALID_PARAMETER_VALUE_INTER, "trx id list full");
        return INVALID_PARAMETER_VALUE_INTER;
    }

    ids->trxIds[ids->listLen] = trxId;
    ids->listLen++;
    return STATUS_OK_INTER;
}

void TrxIdListRemoveId(TrxIdListT *ids, TrxIdT trxId)
{
    DB_POINTER2(ids, ids->trxIds);

    TrxIdT *trx = (TrxIdT *)bsearch(&trxId, ids->trxIds, ids->listLen, sizeof(TrxIdT), TrxIdCmp);
    if (trx == NULL) {
        return;
    }

    int32_t slot = (int32_t)(trx - ids->trxIds);
    DB_ASSERT(ids->listLen > 0);
    DB_ASSERT(0 <= slot && slot < ids->listLen);
    if (slot == (ids->listLen - 1)) {
        ids->listLen--;
        return;
    }

    // 将后续的id前移一位
    if (ids->listLen < TRX_ID_LIST_MOVE_THRESHOLD) {
        for (uint16_t i = (uint16_t)slot; i < ids->listLen - 1; i++) {
            ids->trxIds[i] = ids->trxIds[i + 1];
        }
    } else {
        // 将后续的id前移一位
        size_t moveSize = (size_t)((size_t)sizeof(ids->trxIds[0]) * (size_t)((ids->listLen - 1) - (uint16_t)slot));
        errno_t ret = memmove_s(&ids->trxIds[slot], moveSize, &ids->trxIds[slot + 1], moveSize);
        if (ret != EOK) {
            SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "remove trxId");
            return;
        }
    }

    ids->listLen--;
}

static ALWAYS_INLINE_C StatusInter TrxIdListClone(TrxIdListT *dst, const TrxIdListT *src)
{
    DB_POINTER2(dst, src);
    if (dst->capacity < src->listLen) {
        SE_LAST_ERROR(OBJECT_NOT_IN_PREREQUISITE_STATE_INTER, "Insufficient list capacity for TrxIdListClone.");
        return OBJECT_NOT_IN_PREREQUISITE_STATE_INTER;
    }
    dst->listLen = src->listLen;
    errno_t ret = memcpy_s(dst->trxIds, sizeof(TrxIdT) * dst->capacity, src->trxIds, sizeof(TrxIdT) * src->listLen);
    if (ret != EOK) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "TrxIdListClone : %" PRIu32 " !", (uint32_t)ret);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    return STATUS_OK_INTER;
}

/* 由上层函数负责加锁
 * 这里是事务的全局控制点，执行完该函数，该事务就转为commit状态并对其他事务可见 */
void TrxCommitInTrxList(TrxT *trx)
{
    TrxMgrT *trxMgr = (TrxMgrT *)trx->trxMgr;

    TrxIdListT *rwTrxIds = TrxMgrGetRwTrxIds(trxMgr);
    TrxIdListT *roTrxIds = TrxMgrGetRoTrxIds(trxMgr);
    if (!trx->base.readOnly) {
        // 从rwTrxIdList中移除本事务
        TrxIdListRemoveId(rwTrxIds, trx->base.trxId);
    } else {
        // 从roTrxIdList中移除本事务
        TrxIdListRemoveId(roTrxIds, trx->base.trxId);
    }

    // 设置minActiveNormalTrxId
    TrxIdT rwMinId = (TrxIdListGetLen(rwTrxIds) == 0) ? trxMgr->maxTrxId : TrxIdListGetFront(rwTrxIds);
    TrxIdT roMinId = (TrxIdListGetLen(roTrxIds) == 0) ? trxMgr->maxTrxId : TrxIdListGetFront(roTrxIds);
    trxMgr->minActiveNormalTrxId = DB_MIN(rwMinId, roMinId);
}

StatusInter ReadViewPrepare(TrxT *trx)
{
    DB_POINTER(trx);
    ReadViewT *readView = &trx->trx.base.readView;
    TrxMgrT *trxMgr = trx->trxMgr;
    if (SECUREC_UNLIKELY(readView->isOpen)) {
        return STATUS_OK_INTER;
    }

    TrxIdListT *rwTrxIds = TrxMgrGetRwTrxIds(trxMgr);

    RW_LATCH_RLATCH_RETURN_IF_FAILED(&trxMgr->latch);
    readView->creatorTrxId = trx->base.trxId;
    readView->highWaterMark = trxMgr->maxTrxId;
    readView->lowWaterMark = trxMgr->minActiveNormalTrxId;
    uint16_t listLen = TrxIdListGetLen(rwTrxIds);
    if (listLen != 0 && (listLen != 1 || TrxIdListGetFront(rwTrxIds) != readView->creatorTrxId)) {
        DB_ASSERT(!DbIsShmPtrValid(readView->activeTrxIdsShm));
        uint32_t allocSize = (uint32_t)sizeof(TrxIdListT) + listLen * (uint32_t)sizeof(TrxIdT);
        // 申请活跃事务链表内存, 在事务提交或回滚阶段释放，并且从共享内存申请
        SeInstanceT *seIns = (SeInstanceT *)trx->seRunCtx->seIns;
        DbMemCtxT *shmCtx = (DbMemCtxT *)DbGetShmemCtxById(seIns->seAppShmMemCtxId, seIns->instanceId);
        if (shmCtx == NULL) {
            DbInterProcRWUnlatchR(&trxMgr->latch);
            SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "memCtxId %" PRIu32 " inv", seIns->seAppShmMemCtxId);
            return MEMORY_OPERATE_FAILED_INTER;
        }

        TrxIdListT *activeTrxIds = SeShmAlloc(shmCtx, allocSize, &readView->activeTrxIdsShm);
        if (activeTrxIds == NULL) {
            DbInterProcRWUnlatchR(&trxMgr->latch);
            SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "trx(%" PRIu64 ") alloc rwTrxIds in readView", trx->base.trxId);
            return OUT_OF_MEMORY_INTER;
        }

        activeTrxIds->capacity = listLen;
        activeTrxIds->listLen = 0;
        StatusInter ret = TrxIdListClone(activeTrxIds, rwTrxIds);
        if (ret != STATUS_OK_INTER) {
            DbShmemCtxFree(shmCtx, readView->activeTrxIdsShm);
            readView->activeTrxIdsShm = DB_INVALID_SHMPTR;
            DbInterProcRWUnlatchR(&trxMgr->latch);
            SE_ERROR(ret, "trx(%" PRIu64 ") clone rwTrxIds in readView", trx->base.trxId);
            return ret;
        }

        readView->lowWaterMark = TrxIdListGetFront(rwTrxIds);
    }
    DbInterProcRWUnlatchR(&trxMgr->latch);
    readView->parentTrxIdsShm = DB_INVALID_SHMPTR;
    readView->isCloneTrx = false;
    readView->isOpen = true;
    return STATUS_OK_INTER;
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
void TrxMgrRLockWithSession(TrxMgrT *trxMgr, SeRunCtxT *seRunCtx)
{
    DbSessionCtxT *sessionCtx = &seRunCtx->resSessionCtx;
    ShmemPtrT latchAddr = trxMgr->trxMgrShmAddr;
    GET_MEMBER_SHMPTR(latchAddr, offsetof(TrxMgrT, latch));  // 转换为latch的addr
    LATCH_GET_START_WAITTIMES(seRunCtx->resSessionCtx.isDirectRead, trxMgr->latch);
    DbRWSpinRLockWithSession(sessionCtx, &trxMgr->latch, &latchAddr, LATCH_ADDR_TRX_MGR_LOCK);
    LATCH_GET_END_WAITTIMES(true, trxMgr->latch, sessionCtx->session);
}

void TrxMgrRUnlockWithSession(TrxMgrT *trxMgr, SeRunCtxT *seRunCtx)
{
    DbSessionCtxT *sessionCtx = &seRunCtx->resSessionCtx;
    DbRWSpinRUnlockWithSession(sessionCtx, &trxMgr->latch);
}
#endif /* FEATURE_SIMPLEREL */

// 外层负责加锁
HOT_FUN_RC StatusInter ReadViewPrepareForRcInLock(
    SeRunCtxT *seRunCtx, ReadViewT *readView, const TrxIdListT *rwTrxIds, uint16_t listLen)
{
    DB_POINTER3(seRunCtx, readView, rwTrxIds);
    DB_ASSERT(readView->activeTrxIdsTmp == NULL);
    uint32_t allocSize = (uint32_t)sizeof(TrxIdListT) + listLen * (uint32_t)sizeof(TrxIdT);
    if (allocSize > seRunCtx->trxIdsSize) {
        if (seRunCtx->activeTrxIds != NULL) {
            DbDynMemCtxFree(seRunCtx->sessionMemCtx, seRunCtx->activeTrxIds);
        }
        seRunCtx->activeTrxIds = DbDynMemCtxAlloc(seRunCtx->sessionMemCtx, allocSize);
        // 申请活跃事务链表内存，从动态内存申请，RC读使用完毕后当场释放
        if (seRunCtx->activeTrxIds == NULL) {
            seRunCtx->trxIdsSize = 0;
            SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "ReadViewPrepareForRc: alloc rwTrxIds in readView");
            return OUT_OF_MEMORY_INTER;
        }
        seRunCtx->trxIdsSize = allocSize;
    }
    readView->activeTrxIdsTmp = seRunCtx->activeTrxIds;
    readView->activeTrxIdsTmp->capacity = listLen;
    StatusInter ret = TrxIdListClone(readView->activeTrxIdsTmp, rwTrxIds);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        readView->activeTrxIdsTmp = NULL;
        SE_ERROR(ret, "ReadViewPrepareForRc: clone rwTrxIds in readView");
        return ret;
    }

    readView->lowWaterMark = TrxIdListGetFront(rwTrxIds);
    return STATUS_OK_INTER;
}
// 由于服务端和客户端都会用到，因此需要针对加锁方式做场景区分
HOT_FUN_RC StatusInter ReadViewPrepareForRc(SeRunCtxT *seRunCtx, ReadViewT *readView, bool isDirectRead)
{
    DB_POINTER2(seRunCtx, readView);
    TrxMgrT *trxMgr = seRunCtx->trxMgr;
    if (SECUREC_UNLIKELY(readView->isOpen)) {
        return STATUS_OK_INTER;
    }

    // 直连读场景不设置creatorTrxId；CS连读场景设为当前事务的trxId以用来判断当前事务修改版本的可见性
    readView->creatorTrxId = isDirectRead ? DB_INVALID_TRX_ID : ((TrxT *)seRunCtx->trx)->base.trxId;
    StatusInter ret = 0;
    TrxIdListT *rwTrxIds = TrxMgrGetRwTrxIds(trxMgr);

    uint16_t listLen = TrxIdListGetLen(rwTrxIds);
    if (listLen != 0) {
        // 写事务数量大于1，则正常加锁读trxMgr中的值赋给readView
        // 加锁
        if (isDirectRead) {
            TrxMgrRLockWithSession(trxMgr, seRunCtx);
            CRASHPOINT(DB_CRASH_EVENT_RLOCK, DB_CRASH_STATE_SESSION_LOCK);
        } else {
            RW_LATCH_RLATCH_RETURN_IF_FAILED(&trxMgr->latch);
        }
        // listLen可能被改, 重新获取
        listLen = TrxIdListGetLen(rwTrxIds);
        readView->highWaterMark = trxMgr->maxTrxId;
        readView->lowWaterMark = trxMgr->minActiveNormalTrxId;
        if (listLen != 0) {
            ret = ReadViewPrepareForRcInLock(seRunCtx, readView, rwTrxIds, listLen);
        }
        // 解锁
        if (isDirectRead) {
            TrxMgrRUnlockWithSession(trxMgr, seRunCtx);
        } else {
            DbInterProcRWUnlatchR(&trxMgr->latch);
        }
    } else {
        /* 写事务数量为0
            1）事务不可能为提交事务
            2）事务为新增事务:minActiveNormalTrxId不变,maxTrxId变大,新增事务必然不会写当前页（已拿到页锁）,对当前行的读没影响
        */
        readView->highWaterMark = trxMgr->maxTrxId;
        readView->lowWaterMark = trxMgr->minActiveNormalTrxId;
        if (readView->activeTrxIdsTmp != NULL) {
            readView->activeTrxIdsTmp->capacity = 0;
            readView->activeTrxIdsTmp->listLen = 0;
        }
    }

    if (ret == 0) {
        readView->isOpen = true;
    }
    return ret;
}

bool TrxMgrIsRwTrxActive(TrxMgrT *trxMgr, TrxIdT trxId)
{
    DB_POINTER(trxMgr);
    DB_ASSERT(!DbIsRWLatchWithCheck());
    // minActiveNormalTrxId单调递增, 如果小于minActiveNormalTrxId, 肯定不在活跃事务链表中
    if (trxId < trxMgr->minActiveNormalTrxId) {
        return false;
    }
    TrxIdListT *rwTrxIds = TrxMgrGetRwTrxIds(trxMgr);
    DbRWLatchR(&trxMgr->latch);
    // assume this trx is read-write trx, not read only
    bool isFind = TrxIdListLookup(rwTrxIds, trxId);
    DbRWUnlatchR(&trxMgr->latch);
    return isFind;
}

bool TrxMgrIsRwTrxActiveWithSessionLock(TrxMgrT *trxMgr, SeRunCtxT *seRunCtx, TrxIdT trxId)
{
    DB_POINTER(trxMgr);

    // minActiveNormalTrxId单调递增, 如果小于minActiveNormalTrxId, 肯定不在活跃事务链表中
    if (trxId < trxMgr->minActiveNormalTrxId) {
        return false;
    }
    TrxIdListT *rwTrxIds = TrxMgrGetRwTrxIds(trxMgr);
    TrxMgrRLockWithSession(trxMgr, seRunCtx);
    // 注入异常退出点，加锁成功后异常退出
    CRASHPOINT(DB_CRASH_EVENT_TRX_MGR_RLOCK, DB_CRASH_STATE_TRX_MGR_RLOCK);
    // assume this trx is read-write trx, not read only
    bool isFind = TrxIdListLookup(rwTrxIds, trxId);
    // 注入异常退出点，解锁前异常退出
    CRASHPOINT(DB_CRASH_EVENT_TRX_MGR_RLOCK, DB_CRASH_STATE_TRX_MGR_UN_RLOCK);
    TrxMgrRUnlockWithSession(trxMgr, seRunCtx);
    return isFind;
}

// 返回值含义
// 1. STATUS_OK_INTER ：可见
// 2. NO_DATA_INTER ：不可见
// 3. MEMORY_OPERATE_FAILED_INTER ：activeTrxIdsShm共享内存转换错误
#ifdef FEATURE_SIMPLEREL
ALWAYS_INLINE_C StatusInter ReadViewIsTrxVisible(const ReadViewT *readView, TrxIdT trxId)
#else
StatusInter ReadViewIsTrxVisible(const ReadViewT *readView, TrxIdT trxId)
#endif
{
    DB_POINTER(readView);

    if (!readView->isOpen) {
        // 后台purge未开启readView，此处打日志会刷屏
        return NO_DATA_INTER;
    }

    if (trxId < readView->lowWaterMark || trxId == readView->creatorTrxId) {
        return STATUS_OK_INTER;
    }

    // 必须先使用parentTrxs判断是否可见，后面再使用highWaterMark去判断
    // 因为所有子孙事务都复制了父事务的高低水位，合并进来的子事务，Id大于高水位。如果先判断高水位，此时子孙事务，无法看到对应的数据
    if (readView->isCloneTrx) {
        if (DbIsShmPtrValid(readView->parentTrxIdsShm)) {
            TrxIdListT *parentTrxs = (TrxIdListT *)DbShmPtrToAddr(readView->parentTrxIdsShm);
            if (SECUREC_UNLIKELY(parentTrxs == NULL)) {
                SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "ReadViewIsTrxVisible: transfer parentTrxIdsShm to ptr");
                return MEMORY_OPERATE_FAILED_INTER;
            }
            if (parentTrxs->listLen != 0 && TrxIdListLookup(parentTrxs, trxId)) {
                return STATUS_OK_INTER;
            }
        }
    }

    if (trxId >= readView->highWaterMark) {
        return NO_DATA_INTER;
    }

    TrxIdListT *activeTrxs = NULL;
    if (DbIsShmPtrValid(readView->activeTrxIdsShm)) {
        activeTrxs = (TrxIdListT *)DbShmPtrToAddr(readView->activeTrxIdsShm);
        if (SECUREC_UNLIKELY(activeTrxs == NULL)) {
            SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "ReadViewIsTrxVisible: transfer activeTrxIdsShm to ptr");
            return MEMORY_OPERATE_FAILED_INTER;
        } else if (activeTrxs->listLen == 0) {
            return STATUS_OK_INTER;
        }
    } else {
        activeTrxs = (TrxIdListT *)readView->activeTrxIdsTmp;
        if (TrxIdListGetLen(activeTrxs) == 0) {
            return STATUS_OK_INTER;
        }
    }

    return TrxIdListLookup(activeTrxs, trxId) ? NO_DATA_INTER : STATUS_OK_INTER;
}

void ReadViewClose(TrxT *trx)
{
    DB_POINTER(trx);

    if (SECUREC_UNLIKELY(!trx->trx.base.readView.isOpen)) {
        return;
    }

    trx->trx.base.readView.isOpen = false;
    DbMemCtxT *shmCtx = (DbMemCtxT *)DbGetShmemCtxById(
        ((SeInstanceT *)trx->seRunCtx->seIns)->seAppShmMemCtxId, ((SeInstanceT *)trx->seRunCtx->seIns)->instanceId);
    if (shmCtx == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "memCtxId %" PRIu32 " inv",
            ((SeInstanceT *)trx->seRunCtx->seIns)->seAppShmMemCtxId);
        return;
    }
    if (DbIsShmPtrValid(trx->trx.base.readView.activeTrxIdsShm)) {
        DbShmemCtxFree(shmCtx, trx->trx.base.readView.activeTrxIdsShm);
        trx->trx.base.readView.activeTrxIdsShm = DB_INVALID_SHMPTR;
    }
    if (DbIsShmPtrValid(trx->trx.base.readView.parentTrxIdsShm)) {
        DbShmemCtxFree(shmCtx, trx->trx.base.readView.parentTrxIdsShm);
        trx->trx.base.readView.parentTrxIdsShm = DB_INVALID_SHMPTR;
    }
}

HOT_FUN_RC void ReadViewCloseForRc(SeRunCtxT *seRunCtx, ReadViewT *readView)
{
    DB_POINTER2(seRunCtx, readView);

    if (SECUREC_UNLIKELY(!readView->isOpen)) {
        return;
    }

    readView->isOpen = false;
    if (readView->activeTrxIdsTmp != NULL) {
        readView->activeTrxIdsTmp = NULL;
    }

    void *ptr = seRunCtx->activeTrxIds;

    DbMemCtxT *escapeCtx = GetEscapeCtx(seRunCtx->sessionMemCtx);
    if (escapeCtx != NULL && ptr != NULL && CheckEscapeCtxMark(seRunCtx->sessionMemCtx, ptr)) {
        DbDynMemCtxFree(seRunCtx->sessionMemCtx, ptr);
        seRunCtx->activeTrxIds = NULL;
        seRunCtx->trxIdsSize = 0;
    }
}

#ifdef EXPERIMENTAL_GUANGQI
static StatusInter ReadViewCreatePutParentTrxId(TrxIdListT *parentTrxs, TrxIdT parentTrxId, TrxIdT anotherParentTrxId)
{
    StatusInter ret = STATUS_OK_INTER;
    if (anotherParentTrxId != DB_MAX_UINT64 && anotherParentTrxId != 0) {
        if (anotherParentTrxId > parentTrxId) {
            ret = TrxIdListPushbackId(parentTrxs, parentTrxId);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "trx(%" PRIu64 ") push parentTrxId in readView", parentTrxId);
                return ret;
            }
            ret = TrxIdListPushbackId(parentTrxs, anotherParentTrxId);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "trx(%" PRIu64 ") push another parentTrxId in readView", anotherParentTrxId);
                return ret;
            }
        } else {
            ret = TrxIdListPushbackId(parentTrxs, anotherParentTrxId);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "trx(%" PRIu64 ") push another parentTrxId in readView", parentTrxId);
                return ret;
            }
            ret = TrxIdListPushbackId(parentTrxs, parentTrxId);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "trx(%" PRIu64 ") push parentTrxId in readView", anotherParentTrxId);
                return ret;
            }
        }
    } else {
        // parentTrxs中新增父事务的trxId，子事务可以看到父事务的数据
        ret = TrxIdListPushbackId(parentTrxs, parentTrxId);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "trx(%" PRIu64 ") push parentTrxId in readView", parentTrxId);
            return ret;
        }
    }
    return ret;
}

static StatusInter ReadViewCreateParentTrxIds(
    DbMemCtxT *shmCtx, ReadViewT *readView, ReadViewT *parentReadView, TrxIdT anotherParentTrxId)
{
    TrxIdListT *fatherParentTrxs = NULL;
    if (DbIsShmPtrValid(parentReadView->parentTrxIdsShm)) {
        fatherParentTrxs = (TrxIdListT *)DbShmPtrToAddr(parentReadView->parentTrxIdsShm);
        if (SECUREC_UNLIKELY(fatherParentTrxs == NULL)) {
            SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "transfer parentTrxIdsShm to ptr");
            return MEMORY_OPERATE_FAILED_INTER;
        }
    }
    uint32_t parentNum = (anotherParentTrxId == DB_MAX_UINT64 || anotherParentTrxId == 0) ? 1 : (1 + 1);
    uint32_t listSize = (fatherParentTrxs != NULL && fatherParentTrxs->listLen != 0) ?
                            (fatherParentTrxs->listLen + parentNum) :
                            parentNum;
    uint32_t parentTrxIdsAllocSize = (uint32_t)sizeof(TrxIdListT) + listSize * (uint32_t)sizeof(TrxIdT);
    TrxIdListT *parentTrxs = SeShmAlloc(shmCtx, parentTrxIdsAllocSize, &readView->parentTrxIdsShm);
    if (parentTrxs == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "trx(%" PRIu64 ") alloc parentTrxs in readView", readView->creatorTrxId);
        return OUT_OF_MEMORY_INTER;
    }
    parentTrxs->capacity = (uint16_t)listSize;
    parentTrxs->listLen = 0;
    StatusInter ret = STATUS_OK_INTER;
    if (fatherParentTrxs != NULL && fatherParentTrxs->listLen != 0) {
        ret = TrxIdListClone(parentTrxs, fatherParentTrxs);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "trx(%" PRIu64 ") clone fatherParentTrxs in readView", readView->creatorTrxId);
            goto ERR;
        }
    }
    ret = ReadViewCreatePutParentTrxId(parentTrxs, parentReadView->creatorTrxId, anotherParentTrxId);
    if (ret != STATUS_OK_INTER) {
        goto ERR;
    }
    return ret;
ERR:
    DbShmemCtxFree(shmCtx, readView->parentTrxIdsShm);
    readView->parentTrxIdsShm = DB_INVALID_SHMPTR;
    return ret;
}

static StatusInter ReadViewCopyActiveTrxIds(DbMemCtxT *shmCtx, ReadViewT *readView, ReadViewT *parentReadView)
{
    StatusInter ret = STATUS_OK_INTER;
    if (DbIsShmPtrValid(parentReadView->activeTrxIdsShm)) {
        TrxIdListT *parentActiveTrxs = (TrxIdListT *)DbShmPtrToAddr(parentReadView->activeTrxIdsShm);
        if (SECUREC_UNLIKELY(parentActiveTrxs == NULL)) {
            SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "transfer activeTrxIdsShm to ptr");
            return MEMORY_OPERATE_FAILED_INTER;
        } else if (parentActiveTrxs->listLen != 0) {
            uint32_t allocSize = (uint32_t)sizeof(TrxIdListT) + parentActiveTrxs->listLen * (uint32_t)sizeof(TrxIdT);
            TrxIdListT *activeTrxIds = SeShmAlloc(shmCtx, allocSize, &readView->activeTrxIdsShm);
            if (activeTrxIds == NULL) {
                SE_LAST_ERROR(
                    OUT_OF_MEMORY_INTER, "trx(%" PRIu64 ") alloc activeTrxIds in readView", readView->creatorTrxId);
                return OUT_OF_MEMORY_INTER;
            }
            activeTrxIds->capacity = parentActiveTrxs->listLen;
            activeTrxIds->listLen = 0;
            ret = TrxIdListClone(activeTrxIds, parentActiveTrxs);
            if (ret != STATUS_OK_INTER) {
                DbShmemCtxFree(shmCtx, readView->activeTrxIdsShm);
                readView->activeTrxIdsShm = DB_INVALID_SHMPTR;
                SE_ERROR(ret, "trx(%" PRIu64 ") clone activeTrxIds in readView", readView->creatorTrxId);
                return ret;
            }
        }
    }
    return ret;
}

StatusInter ReadViewPrepareByParentTrx(TrxT *trx, TrxT *parentTrx, TrxIdT anotherParentTrxId)
{
    DB_POINTER2(trx, parentTrx);
    ReadViewT *readView = &trx->trx.base.readView;
    ReadViewT *parentReadView = &parentTrx->trx.base.readView;
    if (SECUREC_UNLIKELY(readView->isOpen)) {
        SE_ERROR(INTERNAL_ERROR_INTER, "clone trx already open readview");
        return INTERNAL_ERROR_INTER;
    }

    if (SECUREC_UNLIKELY(!parentReadView->isOpen)) {
        SE_ERROR(INTERNAL_ERROR_INTER, "parent trx not open readview");
        return INTERNAL_ERROR_INTER;
    }

    SeInstanceT *seIns = (SeInstanceT *)trx->seRunCtx->seIns;
    DbMemCtxT *shmCtx = (DbMemCtxT *)DbGetShmemCtxById(seIns->seAppShmMemCtxId, seIns->instanceId);
    if (shmCtx == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "memCtxId %" PRIu32 " inv", seIns->seAppShmMemCtxId);
        return MEMORY_OPERATE_FAILED_INTER;
    }

    readView->creatorTrxId = trx->base.trxId;
    readView->highWaterMark = parentReadView->highWaterMark;
    readView->lowWaterMark = parentReadView->lowWaterMark;

    StatusInter ret = ReadViewCreateParentTrxIds(shmCtx, readView, parentReadView, anotherParentTrxId);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    ret = ReadViewCopyActiveTrxIds(shmCtx, readView, parentReadView);
    if (ret != STATUS_OK_INTER) {
        DbShmemCtxFree(shmCtx, readView->parentTrxIdsShm);
        readView->parentTrxIdsShm = DB_INVALID_SHMPTR;
        return ret;
    }

    readView->isCloneTrx = true;
    readView->isOpen = true;
    return STATUS_OK_INTER;
}
#endif

#ifdef __cplusplus
}
#endif
