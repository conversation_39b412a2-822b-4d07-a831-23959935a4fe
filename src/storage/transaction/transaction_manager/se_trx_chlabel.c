/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: trx chlabel 相关函数
 * Author: GMDBv5 SE Team
 * Create: 2023/09/23
 */
#include "se_trx_inner.h"
#include "se_log.h"
#include "se_clustered_hash_label_dml.h"
#include "se_daemon.h"
#include "db_table_space.h"

#ifdef __cplusplus
extern "C" {
#endif

Status SeSetChLabelTrxCtxForIndex(const IndexOpenCfgT *idxOpenCfg)
{
    if (SECUREC_UNLIKELY(!(idxOpenCfg->seRunCtx && idxOpenCfg->chHandle && idxOpenCfg->callbackFunc.keyCmp &&
                           idxOpenCfg->callbackFunc.addrCheck && idxOpenCfg->callbackFunc.addrCheckAndFetch))) {
        SE_LAST_ERROR(OBJ_NOT_PREREQUISITE_SE_INVALID_POINTER, "Incorrect idxParam");
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }

    TrxT *trx = idxOpenCfg->seRunCtx->trx;
    SeTrxContainerCtxT *item = NULL;
    if (idxOpenCfg->chHandle->chTrxCtx != NULL) {
        item = (SeTrxContainerCtxT *)(void *)idxOpenCfg->chHandle->chTrxCtx;
    } else {
        ChLabelDmDetailT dmDetail = ClusteredHashGetDmDetailInfo(idxOpenCfg->chHandle);
        if (SECUREC_UNLIKELY(idxOpenCfg->vertex == NULL)) {
            SE_LAST_ERROR(OBJ_NOT_PREREQUISITE_SE_INVALID_POINTER, "Incorrect vertex in idxParam");
            return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
        }
        item = (SeTrxContainerCtxT *)TrxGetContainerCtxById(trx, dmDetail.labelId);
    }
    if (SECUREC_LIKELY(item != NULL && trx->base.state == TRX_STATE_ACTIVE)) {
        item->chTrxCtx.vertex = idxOpenCfg->vertex;
        item->chTrxCtx.secIndexkeyCmp = idxOpenCfg->callbackFunc.keyCmp;
        item->chTrxCtx.addrCheck = idxOpenCfg->callbackFunc.addrCheck;
        item->chTrxCtx.addrCheckAndFetch = idxOpenCfg->callbackFunc.addrCheckAndFetch;
    }
    return GMERR_OK;
}

// 聚簇容器的提交处理，主要是缩容的判断，以及必要时调用EE层注册的回调函数
static void TrxCommitHandleChLabelTrxCtx(TrxT *trx, SeTrxContainerCtxT *trxCntrCtx)
{
    DB_ASSERT(trxCntrCtx->ctxHead.type == (uint16_t)TRX_CLUSTERED_HASH_HANDLE);
    ChTrxCtxT *chTrxCtx = &trxCntrCtx->chTrxCtx;
    // 事务提交, 触发EE的注册的回调函数
    if (chTrxCtx->commitCallback.callBack) {
        chTrxCtx->commitCallback.callBack(chTrxCtx->commitCallback.parameter);
    }
    ChLabelRunHdlT runCtx = chTrxCtx->chRunCtx;
    ChLabelDmDetailT dmDetail = ClusteredHashGetDmDetailInfo(runCtx);
    // 获取配置是否开启，时间条件是否达成
    bool enableDefrag = false;
    SeGetDefragConfAndTimeCond(dmDetail.dmInfo, VERTEX_LABEL, &enableDefrag);
    if (enableDefrag && ClusteredHashCanAddScaleInTask(runCtx)) {
        uint32_t labelId = ((DmVertexLabelT *)dmDetail.dmInfo)->metaCommon.metaId;
        // 此处任务添加只涉及聚簇容器的缩容，不涉及page的归还
        SeDfgmtAddTask(labelId, DB_INVALID_TABLE_SPACE_INDEX, false);
    }
}

// 聚簇容器的回滚处理，主要是缩容的判断，以及必要调用EE层注册的回调函数
static void TrxRollBackHandleChLabelTrxCtx(const TrxT *trx, SeTrxContainerCtxT *trxCntrCtx)
{
    DB_ASSERT(trxCntrCtx->ctxHead.type == (uint16_t)TRX_CLUSTERED_HASH_HANDLE);
    ChTrxCtxT *chTrxCtx = &trxCntrCtx->chTrxCtx;
    // 事务提交, 触发EE的注册的回调函数
    if (trx->base.needSendSubWhenFailed && chTrxCtx->commitCallback.callBack) {
        chTrxCtx->commitCallback.callBack(chTrxCtx->commitCallback.parameter);
    }
}

void TrxHandleChLabelTrxCtx(TrxT *trx, SeTrxContainerCtxT *trxCntrCtx, TrxHandleTypeE type)
{
    if (SECUREC_LIKELY(type == TRX_COMMIT_HANDLE)) {
        TrxCommitHandleChLabelTrxCtx(trx, trxCntrCtx);
    } else if (type == TRX_ROLLBACK_HANDLE) {
        TrxRollBackHandleChLabelTrxCtx(trx, trxCntrCtx);
    } else {
        // 什么都不用做
        DB_ASSERT(type == TRX_CLEAR_HANDLE);
    }
}

void *ChTrxGetVertexLabel(ChTrxCtxT *chTrxCtx)
{
    if (chTrxCtx != NULL && chTrxCtx->chRunCtx != NULL && chTrxCtx->chRunCtx->seRunCtx != NULL) {
        return chTrxCtx->vertexLabel;
    }
    return NULL;
}

Handle TrxGetClusteredHashHandle(TrxT *trx, uint32_t containerId)
{
    SeTrxContainerCtxT *seTrxCntrCtx = TrxGetContainerCtxById(trx, containerId);
    if (seTrxCntrCtx == NULL) {
        return NULL;
    }

    DB_ASSERT(seTrxCntrCtx->chTrxCtx.ctxHead.type == (uint16_t)TRX_CLUSTERED_HASH_HANDLE);
    ChTrxCtxT *chTrxCtx = &seTrxCntrCtx->chTrxCtx;
    // 句柄空间申请提前，此处仅做初始化，因此需要判断不为空
    DB_ASSERT(chTrxCtx->chRunCtx != NULL);
    if (TrxContainerIsOpen(seTrxCntrCtx)) {
        return chTrxCtx->chRunCtx;
    }

    ClusteredHashOpenByCntr(chTrxCtx->chRunCtx, seTrxCntrCtx);
    seTrxCntrCtx->chTrxCtx.ctxHead.isOpened = true;
    return chTrxCtx->chRunCtx;
}

#ifdef __cplusplus
}
#endif
