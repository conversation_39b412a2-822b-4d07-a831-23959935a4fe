/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: trx stat 相关函数
 * Author: GMDBv5 SE Team
 * Create: 2024/04/23
 */

#include "se_trx_inner.h"
#include "se_trx_mgr.h"
#include "se_log.h"
#include "se_undo.h"
#include "se_heap_utils.h"
#include "se_trx_base.h"
#include "se_dfx.h"
#include "dm_meta_prop_label.h"
#include "se_daemon.h"
#include "drt_base_def.h"
#include "drt_instance.h"

#ifdef __cplusplus
extern "C" {
#endif

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
void TrxGetTrxStatAll(TrxStatAllT *trxStat, const SeRunCtxHdT seRunCtxHd)
{
    DB_POINTER2(trxStat, seRunCtxHd);
    SeRunCtxT *seRunCtx = seRunCtxHd;
    TrxMgrT *trxMgr = (TrxMgrT *)seRunCtx->trxMgr;
    TrxPoolT *trxPool = TrxMgrGetTrxPool(trxMgr);
    TrxIdListT *rwTrxIds = TrxMgrGetRwTrxIds(trxMgr);
    TrxIdListT *roTrxIds = TrxMgrGetRoTrxIds(trxMgr);
#ifdef FEATURE_YANG
    trxStat->optiRetryTrxOpen = DbCfgGetBoolLite(DB_CFG_FORCE_COMMIT_ENABLE, NULL);
#else
    trxStat->optiRetryTrxOpen = false;
#endif

    DbRWLatchR(&trxMgr->latch);
    DbRWLatchR(&trxPool->latch);
    trxStat->trxUsdNum = trxPool->usedCnt;
    DbRWUnlatchR(&trxPool->latch);
    trxStat->maxTrxId = trxMgr->maxTrxId;
    trxStat->minActTrxId = trxMgr->minActiveNormalTrxId;
    trxStat->rwTrxICnt = TrxIdListGetLen(rwTrxIds);
    trxStat->roTrxICnt = TrxIdListGetLen(roTrxIds);
    trxStat->trxCnt = trxStat->rwTrxICnt + trxStat->roTrxICnt;
    trxStat->maxTrxTimeUse = trxMgr->maxTrxTimeUse;
    if (trxMgr->optiTrxRetryInfo.isInit) {
        trxStat->optiRetryState = (uint8_t)trxMgr->optiTrxRetryInfo.commitState;
        trxStat->curRetryTrxId = trxMgr->optiTrxRetryInfo.retryTrxId;
#ifdef FEATURE_YANG
        trxStat->retryTrxQueueLen = DbQueueSize(&trxMgr->optiTrxRetryInfo.retryTrxQueue);
        trxStat->commitTrxQueueLen = DbQueueSize(&trxMgr->optiTrxRetryInfo.commitTrxQueue);
#else
        trxStat->retryTrxQueueLen = 0;
        trxStat->commitTrxQueueLen = 0;
#endif
    } else {
        trxStat->optiRetryState = 0;
        trxStat->curRetryTrxId = 0;
        trxStat->retryTrxQueueLen = 0;
        trxStat->commitTrxQueueLen = 0;
    }
    DbRWUnlatchR(&trxMgr->latch);
}

void TrxGetSingleStat(TrxDetailT *trxDetail, const TrxT *trx)
{
    trxDetail->trxId = trx->base.trxId;
    trxDetail->connId = trx->base.connId;
    trxDetail->trxSlot = trx->base.trxSlot;
    trxDetail->isAbort = (trx->base.state == TRX_STATE_ABORT);
    trxDetail->isReadOnly = trx->base.readOnly;
    trxDetail->isRetryTrx = trx->base.isRetryTrx;
    trxDetail->trxState = (uint16_t)trx->base.state;
    trxDetail->startTime = trx->base.startTime;
    trxDetail->tid = trx->tid;
    trxDetail->isolationLevel = (uint32_t)trx->trx.base.isolationLevel;
    trxDetail->retainedUndoRecCnt = 0;
    if (trx->trx.base.retainedUndo != NULL) {
        trxDetail->retainedUndoRecCnt = (uint32_t)trx->trx.base.retainedUndo->recCnt;
    }
    trxDetail->normalUndoRecCnt = 0;
    if (trx->trx.base.normalUndo != NULL) {
        trxDetail->normalUndoRecCnt = (uint32_t)trx->trx.base.normalUndo->recCnt;
    }

    trxDetail->savePointIdForCheckActive = trx->trx.optTrx.labelReadView.savePointIdForCheckActive;
    trxDetail->savePointList = trx->trx.base.savePointInfo.itemList;
    trxDetail->holdLockNum = trx->trx.pesTrx.holdLockNum;
    trxDetail->holdLockAcqId = trx->trx.pesTrx.holdLockAcqId;
    trxDetail->waitLockNotifyId = trx->trx.pesTrx.lockNotifyId;
#ifdef EXPERIMENTAL_GUANGQI
    trxDetail->trxCloneInfo.cloneId = trx->trx.optTrx.cloneId;
    trxDetail->trxCloneInfo.trxCloneType = (uint16_t)trx->base.cloneType;
    trxDetail->trxCloneInfo.childTrxActive = trx->trx.optTrx.relationTrxInfo.childTrxActive;
    trxDetail->trxCloneInfo.successorTrxActive = trx->trx.optTrx.relationTrxInfo.successorTrxId;
    trxDetail->trxCloneInfo.childTrxId = trx->trx.optTrx.relationTrxInfo.childTrxId;
    trxDetail->trxCloneInfo.successorTrxId = trx->trx.optTrx.relationTrxInfo.successorTrxId;
#endif
}

typedef struct SeScanActiveTrxStat {
    uint32_t scanIdx;
} SeScanActiveTrxStatT;

static void AcquireConnectionNameWithConnectionId(uint32_t connectionId, char *peerProcInfo, size_t maxProcInfoLen)
{
    // 提前截断
    peerProcInfo[0] = 0;
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        DB_LOG_WARN_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Get drtIns when acquire conn peername.");
        return;
    }

    DrtConnectionT *conn = DrtAttachConnById(&drtIns->connMgr, (uint16_t)connectionId);
    if (conn == NULL) {
        DB_LOG_WARN_AND_SET_LASTERR(
            GMERR_NULL_VALUE_NOT_ALLOWED, "Get connection when acquire conn peername, id:%" PRId32 ".", connectionId);
        return;
    }

    // peerProcInfo: connId-pid-tid-auditUserInfo (uid + processName) + threadName
    int32_t err =
        snprintf_s(peerProcInfo, maxProcInfoLen, maxProcInfoLen - 1, "%" PRIu16 "-%" PRIu32 "-%" PRIu64 "-%s-%s",
            conn->id, conn->pid, conn->peerTid, conn->auditUserInfo, conn->peerThreadName);
    if (SECUREC_UNLIKELY(err < 0)) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW,
            "Concat peer proinfo while connId %" PRIu16 ", peer pid %" PRIu32 ", tid %" PRIu64 ", pame %s tname:%s .",
            conn->id, conn->pid, conn->peerTid, conn->auditUserInfo, conn->peerThreadName);
    }

    // 截断避免字符越界
    peerProcInfo[maxProcInfoLen - 1] = 0;
    DrtDetachConnection(conn);
}

static Status TrxPoolGetActiveStat(SeRunCtxHdT seRunCtx, const TrxT *trx, uint8_t *userData)
{
    DB_POINTER3(seRunCtx, trx, userData);
    SeScanActiveTrxStatT *scanTrxStat = (SeScanActiveTrxStatT *)(void *)userData;
    TrxStateE trxState = trx->base.state;
    scanTrxStat->scanIdx++;

    // 排除未开始或者已提交的事务，剩余的是活跃的事务（持久化下调用SeTransBegin的事务）
    if (!(trxState == TRX_STATE_NOT_STARTED || trxState == TRX_STATE_COMMITTED)) {
        // 通过connectionId获取connection，获取事务对端进程信息(进程名+线程名+进程线程id号，申请3倍max name)
        char peerProcInfo[DB_THREAD_NAME_MAX_LEN + DB_THREAD_NAME_MAX_LEN + DB_THREAD_NAME_MAX_LEN] = {0};
        AcquireConnectionNameWithConnectionId(trx->base.connId, peerProcInfo, sizeof(peerProcInfo));
        char timeStr[DB_MAX_NAME_LEN] = {0};
        Status timeFormatRet = DbGetTimestampStr(trx->base.startTime, timeStr, DB_MAX_NAME_LEN, DEFAULT_TIME_FORMAT);
        if (timeFormatRet != GMERR_OK) {
            int32_t spErr =
                snprintf_s(timeStr, sizeof(timeStr), sizeof(timeStr) - 1, "%" PRIu64 "", trx->base.startTime);
            if (spErr) {
                timeStr[DB_MAX_NAME_LEN - 1] = 0;
            }
        }
        // 打印事务id，起始时间，事务state，对端进程信息
        DB_LOG_WARN(0, "Remain trx:%" PRId32 " trxId:%" PRId64 " startTime:%s state:%" PRId32 ", peer:%s",
            scanTrxStat->scanIdx, trx->base.trxId, timeStr, trxState, peerProcInfo);
    }
    return GMERR_OK;
}

void SeTrxFetchAndPrintfActiveTrxStat(SeRunCtxHdT seRunCtx)
{
    SeScanActiveTrxStatT scanTrxStat = {.scanIdx = 0};
    TrxPoolScanAuxInfoT userData;
    userData.userData = (uint8_t *)&scanTrxStat;
    userData.userScanSlotProc = TrxPoolGetActiveStat;
    userData.trxSlot = 0;
    Status ret = GMERR_OK;
    while ((ret = TrxPoolFetchNextWithUserProc(seRunCtx, TrxMgrGetTrxPoolByRunCtx(seRunCtx), &userData)) == GMERR_OK) {
        userData.trxSlot++;
    }
    // 这里只有刷盘异常才会走这里。上层不必关注返回值，仅需要打印表示结束即可
    DB_LOG_WARN(ret, "Get all active Trx over. cnt:%" PRId32 "", scanTrxStat.scanIdx);
}

Status SeTrxGetConnStateByConnId(uint16_t connId, TrxConnStateInfoT *info, DbInstanceHdT dbInstance)
{
    DB_POINTER(info);
    DrtInstanceT *drtIns = DrtGetInstance(dbInstance);
    if (drtIns == NULL) {
        DB_LOG_WARN(GMERR_NULL_VALUE_NOT_ALLOWED, "Get drtIns when acquire conn peername.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    DrtConnectionT *conn = DrtAttachConnById(&drtIns->connMgr, connId);
    if (conn == NULL) {
        DB_LOG_WARN(GMERR_NULL_VALUE_NOT_ALLOWED, "Get connection when acquire conn peername, id:%" PRIu16 ".", connId);
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    info->connId = conn->id;
    info->pid = conn->pid;
    info->peerTid = conn->peerTid;

    errno_t err = strcpy_s(info->auditUserInfo, DB_AUDIT_USER_INFO, conn->auditUserInfo);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_WARN(GMERR_MEMORY_OPERATE_FAILED, "Copy auditUserInfo");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    err = strcpy_s(info->peerThreadName, DB_THREAD_NAME_MAX_LEN, conn->peerThreadName);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_WARN(GMERR_MEMORY_OPERATE_FAILED, "Copy peerThreadName");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    DrtDetachConnection(conn);

    // 截断避免字符串越界
    info->auditUserInfo[DB_AUDIT_USER_INFO - 1] = 0;
    info->peerThreadName[DB_THREAD_NAME_MAX_LEN - 1] = 0;
    return GMERR_OK;
}

inline static uint32_t TrxGetLastOptiTrxLabelCnt(SeOptiTrxInfoT *optiTrxInfo)
{
    return (optiTrxInfo->vertexLabelInfo.labelsCnt + optiTrxInfo->edgeLabelInfo.labelsCnt +
            optiTrxInfo->kvLabelInfo.labelsCnt);
}

Status TrxGetLastOptiTrxStat(
    HeapLastOptiTrxStatT *heapOptiTrxStat, const SeRunCtxHdT seRunCtxHd, DbMemCtxT *memCtx, uint64_t labelLastTrxId)
{
    DB_POINTER2(heapOptiTrxStat, seRunCtxHd);
    SeRunCtxT *seRunCtx = seRunCtxHd;
    TrxMgrT *trxMgr = (TrxMgrT *)seRunCtx->trxMgr;

    DbRWLatchR(&trxMgr->latch);
    SeOptiTrxInfoT *optiTrxInfo = trxMgr->optiTrxHistoryInfoList.head;
    while (optiTrxInfo != NULL) {
        if (optiTrxInfo->trxId == labelLastTrxId) {
            break;
        }
        optiTrxInfo = optiTrxInfo->nextTrxInfo;
    }
    if (optiTrxInfo == NULL) {  // 找不到不影响其他视图信息打印
        DbRWUnlatchR(&trxMgr->latch);
        return GMERR_OK;
    }
    heapOptiTrxStat->trxId = labelLastTrxId;
    errno_t err = strcpy_s(heapOptiTrxStat->peerProcInfo, SE_CONN_INFO_MAX_LEN, optiTrxInfo->peerProcInfo);
    if (err != EOK) {
        DbRWUnlatchR(&trxMgr->latch);
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "strcpy peer proc info, ret: %" PRId32, err);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    uint32_t labelCnt = TrxGetLastOptiTrxLabelCnt(optiTrxInfo);
    heapOptiTrxStat->labelArr = DbDynMemCtxAlloc(memCtx, sizeof(LastOptiTrxLabelsInfoT) * labelCnt);
    if (heapOptiTrxStat->labelArr == NULL) {
        DbRWUnlatchR(&trxMgr->latch);
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc with labelCnt:%" PRIu32, labelCnt);
        return GMERR_OUT_OF_MEMORY;
    }
    heapOptiTrxStat->labelCnt = labelCnt;
    uint32_t index = 0;
    for (uint32_t i = 0; i < optiTrxInfo->vertexLabelInfo.labelsCnt; i++) {
        heapOptiTrxStat->labelArr[index].labelId = optiTrxInfo->vertexLabelInfo.alterLabelsExt[i];
        heapOptiTrxStat->labelArr[index].labelType = VERTEX_LABEL;
        index++;
    }
    for (uint32_t i = 0; i < optiTrxInfo->edgeLabelInfo.labelsCnt; i++) {
        heapOptiTrxStat->labelArr[index].labelId = optiTrxInfo->edgeLabelInfo.alterLabelsExt[i];
        heapOptiTrxStat->labelArr[index].labelType = EDGE_LABEL;
        index++;
    }
    for (uint32_t i = 0; i < optiTrxInfo->kvLabelInfo.labelsCnt; i++) {
        heapOptiTrxStat->labelArr[index].labelId = optiTrxInfo->kvLabelInfo.alterLabelsExt[i];
        heapOptiTrxStat->labelArr[index].labelType = KV_TABLE;
        index++;
    }
    DbRWUnlatchR(&trxMgr->latch);
    return GMERR_OK;
}

StatusInter SeCheckEmergencyTrxState(SeInstanceT *seIns)
{
    TrxMgrT *trxMgr = (TrxMgrT *)DbShmPtrToAddr(seIns->trxMgrShm);
    if (trxMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get trxMgr unsucc, (segid: %" PRIu32 " offset: %" PRIu32 ")",
            seIns->trxMgrShm.segId, seIns->trxMgrShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    TrxPoolT *trxPool = TrxMgrGetTrxPool(trxMgr);
    RW_LATCH_WLATCH_RETURN_IF_FAILED(&trxPool->latch);
    if (trxPool->usedCnt == 0) {
        DbInterProcRWUnlatchW(&trxPool->latch);
        return STATUS_OK_INTER;
    }
    TrxPoolNodeT *nodes = TrxPoolGetNodes(trxPool);
    uint16_t cursor = trxPool->usedHead;
    TrxT *trx = NULL;
    while (cursor != DB_INVALID_UINT16) {
        trx = TrxPoolGetTrxBySlot(trxPool, cursor);
        if (trx == NULL) {
            SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "Get trx, num: %" PRIu16 ", cursor: %" PRIu16 ".",
                trxPool->trxPoolNodeNum, cursor);
            DbInterProcRWUnlatchW(&trxPool->latch);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        if (trx->base.state != TRX_STATE_NOT_STARTED && trx->base.state != TRX_STATE_FAILURE) {
            SE_ERROR(OBJECT_NOT_IN_PREREQUISITE_STATE_INTER, "Trx (%" PRIu64 ") hasn't end yet (%" PRIu32 ").",
                trx->base.trxId, trx->base.state);
            DbInterProcRWUnlatchW(&trxPool->latch);
            return OBJECT_NOT_IN_PREREQUISITE_STATE_INTER;
        }
        cursor = nodes[cursor].next;
    }
    DbInterProcRWUnlatchW(&trxPool->latch);
    return STATUS_OK_INTER;
}

inline TrxStateE SeTransGetState(const struct TagSeRunCtxT *seRunCtx)
{
    DB_POINTER(seRunCtx);
    const SeRunCtxT *seRunCtxPtr = seRunCtx;
    if (SECUREC_UNLIKELY(!seRunCtxPtr->trx)) {
        return TRX_STATE_NOT_STARTED;
    }
    return ((TrxT *)seRunCtxPtr->trx)->base.state;
}

#ifdef EXPERIMENTAL_GUANGQI
inline bool SeTransIsCloneTrx(const struct TagSeRunCtxT *seRunCtx)
{
    DB_POINTER(seRunCtx);
    const SeRunCtxT *seRunCtxPtr = seRunCtx;
    DB_ASSERT(seRunCtxPtr->trx != NULL);
    return ((TrxT *)seRunCtxPtr->trx)->base.cloneType == TRX_ClONE_TYPE_CLONE_TRX;
}

inline bool SeTransIsCloneTypeTrx(const struct TagSeRunCtxT *seRunCtx)
{
    DB_POINTER(seRunCtx);
    const SeRunCtxT *seRunCtxPtr = seRunCtx;
    DB_ASSERT(seRunCtxPtr->trx != NULL);
    return ((TrxT *)seRunCtxPtr->trx)->base.cloneType != TRX_ClONE_TYPE_DEFAULT_TRX;
}
#endif

#endif
Status TrxPoolScanUpdateUndoStat(SeRunCtxHdT seRunCtx, const TrxT *trx, uint8_t *userData)
{
    DB_POINTER3(seRunCtx, trx, userData);
    UndoStatT *trxUndoStat = (UndoStatT *)(void *)userData;
    TrxStateE trxState = trx->base.state;
    trxUndoStat->numRsegs = ((SeUndoCtxT *)(seRunCtx->undoCtx))->undoSpace->meta.numRsegs;
    TrxUndoLogT *undo = trx->trx.base.normalUndo;  // 缓存起来，防止事务并发提交置空，导致对空指针解引用
    uint32_t undoRec = 0;
    if (undo) {
        trxUndoStat->numUndoSegs++;
        undoRec += undo->recCnt;
    }
    undo = trx->trx.base.retainedUndo;
    if (undo) {
        trxUndoStat->numUndoSegs++;
        undoRec += undo->recCnt;
    }
    trxUndoStat->numUndoRecs += undoRec;
    if (trxState == TRX_STATE_ROLLBACK) {
        trxUndoStat->numRollUndoRecs += undoRec;
    }
    return GMERR_OK;
}

void TrxGetMaxTrxNum(TrxT *trx, uint32_t *maxTrxNum)
{
    DB_POINTER2(trx, maxTrxNum);
    TrxMgrT *trxMgr = trx->trxMgr;
    DbRWLatchR(&trxMgr->latch);
    *maxTrxNum = trxMgr->maxTrxNum;
    DbRWUnlatchR(&trxMgr->latch);
}

void TrxGetUsedTrxNum(TrxT *trx, uint32_t *usedTrxNum)
{
    DB_POINTER2(trx, usedTrxNum);
    TrxMgrT *trxMgr = trx->trxMgr;
    DbRWLatchR(&trxMgr->latch);
    *usedTrxNum = trxMgr->trxCnt;
    DbRWUnlatchR(&trxMgr->latch);
}

Status TrxGetUndoStat(SeRunCtxHdT seRunCtx, UndoStatT *trxUndoStat)
{
    TrxPoolScanAuxInfoT userData;
    userData.userData = (uint8_t *)trxUndoStat;
    userData.userScanSlotProc = TrxPoolScanUpdateUndoStat;
    userData.trxSlot = 0;
    Status ret = GMERR_OK;
    while (ret == GMERR_OK) {
        ret = TrxPoolFetchNextWithUserProc(seRunCtx, TrxMgrGetTrxPoolByRunCtx(seRunCtx), &userData);
        userData.trxSlot++;  // 扫描下一个
    }
    if (ret == GMERR_OK || ret == GMERR_NO_DATA) {
        return GMERR_OK;
    }
    return ret;
}

TrxT *TrxMgrGetActiveTrxByTrxId(TrxMgrT *trxMgr, uint64_t id, bool isClonedId, uint16_t trxSlot, bool needLock)
{
    DB_POINTER(trxMgr);

    // 无效的trxSlot值
    if (trxSlot >= trxMgr->maxTrxNum) {
        return NULL;
    }
    TrxPoolT *trxPool = TrxMgrGetTrxPool(trxMgr);
    ShmemPtrT *trxs = TrxPoolGetTrxs(trxPool);

    uint64_t slot = trxSlot / TRX_NUM_PER_BLOCK;
    if (needLock) {  // 防止trxs未完成初始化就被读到，这里加一下trxPool上的锁
        DbRWLatchR(&trxPool->latch);
    }
    ShmemPtrT trxPtr = trxs[slot];
    if (needLock) {
        DbRWUnlatchR(&trxPool->latch);
    }

    TrxT *trxsGroup = (TrxT *)DbShmPtrToAddr(trxPtr);
    if (trxsGroup == NULL) {
        return NULL;
    }

    // 不使用hashmap查找trx时，此处可能出现slot已经失效的问题，需要做一次校验
    // fix me：在做死锁检测时，如何保证已检测的trx没有被释放
    TrxT *trx = &trxsGroup[trxSlot % TRX_NUM_PER_BLOCK];
    if (trx->base.state == TRX_STATE_NOT_STARTED) {
        // trxSlot对应的Trx已经被分配给其他事务使用
        return NULL;
    }
    if (!isClonedId) {
        if (trx->base.trxId == id) {
            return trx;
        }
    } else {
#ifdef EXPERIMENTAL_GUANGQI
        if (trx->trx.optTrx.cloneId == (uint32_t)id) {  // 逻辑保证cloneId不会超过uint32
            return trx;
        }
#endif
    }

    return NULL;
}

Status SeTransGetTrxByTrxId(const SeRunCtxHdT seRunCtx, uint64_t id, bool isClonedId, TrxT **trx)
{
    DB_POINTER3(seRunCtx, seRunCtx->trxMgr, trx);
    *trx = NULL;
    TrxMgrT *trxMgr = (TrxMgrT *)seRunCtx->trxMgr;
    DbInterProcRWLatchR(&trxMgr->latch);
    // minActiveNormalTrxId单调递增, 如果小于minActiveNormalTrxId, 肯定不在活跃事务链表中
    if (id < trxMgr->minActiveNormalTrxId && !isClonedId) {
        DbInterProcRWUnlatchR(&trxMgr->latch);
        DB_LOG_ERROR(GMERR_NO_ACTIVE_TRANSACTION,
            "(SE-Transaction) trxId less than minActiveNormalTrxId. id: %" PRIu64 ", isClonedId:%" PRIu32, id,
            (uint32_t)isClonedId);
        return GMERR_NO_ACTIVE_TRANSACTION;
    }
    TrxPoolT *trxPool = TrxMgrGetTrxPool(trxMgr);
    DbInterProcRWLatchR(&trxPool->latch);
    uint16_t curNode = trxPool->usedHead;
    // 已用事务链表为空, 直接返回不存在
    if (curNode == DB_INVALID_ID16) {
        DbInterProcRWUnlatchR(&trxPool->latch);
        DbInterProcRWUnlatchR(&trxMgr->latch);
        DB_LOG_ERROR(GMERR_NO_ACTIVE_TRANSACTION,
            "(SE-Transaction) used trx list empty. id: %" PRIu64 ", isClonedId:%" PRIu32, id, (uint32_t)isClonedId);
        return GMERR_NO_ACTIVE_TRANSACTION;
    }
    TrxT *findTrx = NULL;
    TrxPoolNodeT *nodes = TrxPoolGetNodes(trxPool);
    // 遍历已用事务链表, 找到对应ID的事务
    while (curNode != DB_INVALID_ID16) {
        findTrx =
            TrxMgrGetActiveTrxByTrxId(trxMgr, id, isClonedId, curNode, false);  // 已经加过trxPool的锁了，传false即可
        if (findTrx != NULL) {
            break;
        }
        curNode = nodes[curNode].next;
    }
    DbInterProcRWUnlatchR(&trxPool->latch);

    if (findTrx == NULL || findTrx->base.connId == DB_INVALID_UINT16) {
        DbInterProcRWUnlatchR(&trxMgr->latch);
        DB_LOG_ERROR(GMERR_NO_ACTIVE_TRANSACTION,
            "(SE-Transaction) transaction not exist or inv connId. id: %" PRIu64 ", isClonedId:%" PRIu32, id,
            (uint32_t)isClonedId);
        return GMERR_NO_ACTIVE_TRANSACTION;
    }
    *trx = findTrx;
    DbInterProcRWUnlatchR(&trxMgr->latch);
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
