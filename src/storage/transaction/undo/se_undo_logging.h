/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: UNDO 特有日志(不与通用日志放一块)
 */
#ifndef SE_UNDO_LOGGING_H
#define SE_UNDO_LOGGING_H

#include "db_internal_error.h"
#include "db_last_error.h"
#include "db_log_debug_extramsg.h"
#include "se_trx_mgr.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef LOG_MODULE
#undef LOG_MODULE
#endif
#define LOG_MODULE "STORAGE"

// 用于回滚或者提交时undo的内存不足场景，只有在使用逃生内存兜底失败时才打印error日志，否则打印第一次失败时的warn级别日志
#define SE_UNDO_ERROR(trx, errCode, format, ...)                                                                     \
    do {                                                                                                             \
        if (!DbIsInsufficientMemoryErr(errCode) || DbGetUseEscapeFlag()) {                                           \
            DB_LOG_ERROR(DbGetExternalErrno(errCode), format, ##__VA_ARGS__);                                        \
        } else {                                                                                                     \
            DB_LOG_WARN(DbGetExternalErrno(errCode), "First OOM, EscapeMemCtx will be used:" format, ##__VA_ARGS__); \
        }                                                                                                            \
    } while (0)

#define SE_UNDO_LAST_ERROR(trx, errCode, format, ...)                                                                \
    do {                                                                                                             \
        if (!DbIsInsufficientMemoryErr(errCode) || DbGetUseEscapeFlag()) {                                           \
            DB_LOG_ERROR_AND_SET_LASTERR(DbGetExternalErrno(errCode), "LASTERR (%" PRIu32 ") " format,               \
                DbGetInternalErrno(errCode), ##__VA_ARGS__);                                                         \
        } else {                                                                                                     \
            DB_LOG_WARN(DbGetExternalErrno(errCode), "First OOM, EscapeMemCtx will be used:" format, ##__VA_ARGS__); \
        }                                                                                                            \
    } while (0)

// 与SE_UNDO_ERROR宏实现相同，区别是直接传外部错误码
#define SE_UNDO_EXT_ERROR(trx, errCode, format, ...)                                              \
    do {                                                                                          \
        if (!DbIsInsufficientMemoryErr(DbGetStatusInterErrno(errCode)) || DbGetUseEscapeFlag()) { \
            DB_LOG_ERROR(errCode, format, ##__VA_ARGS__);                                         \
        } else {                                                                                  \
            DB_LOG_WARN(errCode, "First OOM, EscapeMemCtx will be used:" format, ##__VA_ARGS__);  \
        }                                                                                         \
    } while (0)

#ifdef __cplusplus
}
#endif
#endif  // SE_UNDO_LOGGING_H
