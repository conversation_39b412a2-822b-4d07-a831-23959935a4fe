/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 看护持久化页面关键结构体，undo replay
 * Author: ya<PERSON><PERSON><PERSON><PERSON>
 * Create: 2024-9-24
 */

#ifndef SE_PERSISTCAP_UNDO_REPLAY_H
#define SE_PERSISTCAP_UNDO_REPLAY_H

#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif

// 持久化关键结构体对齐： (不区分64/32位平台)强制8字节对齐
#pragma pack(8)
typedef struct UndoSegTruncateInfo {
    uint32_t recCnt;
    uint32_t logHdrOffset;
} UndoSegTruncateInfoT;
#pragma pack()

#ifdef __cplusplus
}
#endif
#endif  // SE_PERSISTCAP_UNDO_REPLAY_H
