/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: storage inner interface for verify persist data
 * Author: xujing
 * Create: 2024-08-22
 */
#ifndef SE_VERIFY_INNER_H
#define SE_VERIFY_INNER_H

#include "se_instance.h"
#include "se_database.h"
#include "adpt_digest_generate.h"

#ifdef __cplusplus
extern "C" {
#endif

static inline DbDigest32T *SeGetPageTailDigest(uint8_t *page)
{
    return (DbDigest32T *)((uint8_t *)page + ((PageHeadT *)page)->endPos + 1);
}

static inline StatusInter SeDigest32Generate(uint8_t *data, uint32_t dataLen, DbDigest32T *digest, uint32_t digestLen)
{
    return DbGetStatusInterErrno(DbDigest32Generate(data, dataLen, digest, digestLen));
}

static inline void SeResetTamperProofDigest(DbDigest32T *digest)
{
    *digest = (DbDigest32T){0};
}

static inline bool SeIsTamperProofDigestSame(DbDigest32T *digestLeft, DbDigest32T *digestRight)
{
    return (memcmp(digestLeft, digestRight, DB_TAMPER_PROOF_DIGEST_LEN) == 0);
}

static inline void SeTamperProofDigestToString(DbDigest32T *digest, char *digestStr, uint32_t strLen)
{
    DB_ASSERT(strLen >= DB_TAMPER_PROOF_DIGEST_STR_LEN);
    for (uint32_t i = 0; i < DB_TAMPER_PROOF_DIGEST_LEN; i++) {
        (void)sprintf_s(&digestStr[i * DB_HEX_TO_STR_LEN], strLen - i * DB_HEX_TO_STR_LEN, "%02x", digest->digest[i]);
    }
    digestStr[DB_TAMPER_PROOF_DIGEST_LEN * DB_HEX_TO_STR_LEN] = '\0';
}

void SeLogErrorDigest(DbDigest32T *generateDigest, DbDigest32T *actualDigest);
StatusInter SeUpdateDataPageDigest(SeInstanceT *seIns, uint8_t *page, uint32_t pageSize);
StatusInter SeUpdateMutiDataPageDigest(SeInstanceT *seIns, uint8_t *multiPage, uint32_t pageSize, uint32_t pageNum);
StatusInter SeUpdateCtrlPageDigest(SeInstanceT *seIns, uint8_t *page, uint32_t pageSize, uint32_t ctrlPageId);
StatusInter SeCheckPageDigest(const SeInstanceT *seIns, uint8_t *page, uint32_t pageSize);
StatusInter SeCheckSizeArrayDigest(SeInstanceT *seIns, CompSizeArrayT *sizeArray, uint32_t arraySize, int32_t handle);
StatusInter DbRecoveryCheckCtrlPageDigest(
    SeInstanceT *seIns, uint32_t ctrlPageId, StCtrlPageT *ctrlPage, uint32_t blockSize);

Status SeGetMemDevice(SeInstanceT *seIns, int32_t handle, uint32_t devId, bool isVerify, uint8_t **device);

StatusInter LoadAndCalcuSizeArrayDigest(SeInstanceT *seIns, int32_t handle, const DbFileCtrlT *dfCtrl,
    SePersistCompModeE compMode, DbDigestCtxT *digestCtx);

Status UpdateDataFileDigest(SeInstanceT *seIns);

Status UpdateDataFileDigestForZone(SeInstanceT *seIns, uint32_t zoneId);

Status UpdateCtrlFileDigest(SeInstanceT *seIns, bool isMultiZone);

void UpdateFileDigestPoint(SeInstanceT *seIns);

Status VerifyDataFileDigestForReboot(SeInstanceT *seIns);

StatusInter DbCalculateCtrlFileDigest(
    DbDigestCtxT *digestCtx, uint32_t ctrlPageId, StCtrlPageT *ctrlPage, uint32_t ctrlFileSize);

StatusInter DbCheckMemCtrlDigest(SeInstanceT *seIns);

StatusInter DbCheckCtrlPageCrc(uint32_t ctrlPageId, StCtrlPageT *ctrlPage, uint32_t blockSize);

static inline bool SeCtrlPageNeedCRCCheck(SeInstanceT *seIns, uint32_t pageId)
{
    // 1、开了crcCheckEnable，所有ctrl页都需要crc校验
    // 2、开了shaCheckEnable，只有corectrl页需要crc校验
    return (seIns->seConfig.crcCheckEnable || (seIns->seConfig.shaCheckEnable && pageId == CORE_CTRL_PAGE_ID));
}

#ifdef __cplusplus
}
#endif

#endif  // SE_VERIFY_INNER_H
