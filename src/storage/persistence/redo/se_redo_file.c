/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: redo_file.c
 * Description:
 * Author:
 * Create: 2021.8.16
 */
#include "se_redo_file.h"
#include "db_file.h"
#include "db_timer.h"
#include "adpt_sleep.h"
#include "db_crc.h"
#include "db_dyn_load.h"
#include "db_internal_error.h"
#include "db_mem_context.h"
#include "se_log.h"
#include "se_database.h"
#include "se_ckpt.h"
#include "se_recovery.h"
#include "se_redo_buf.h"

static inline uint32_t CycleMoveNext(uint32_t id, uint32_t count)
{
    return (id == (count - 1)) ? 0 : (id + 1);
}

static inline uint32_t RedoGetNextFileId(const RedoLogFileSetT *fileSet)
{
    return CycleMoveNext(fileSet->fileSetPub->curFile, fileSet->fileCount);
}

StatusInter RedoFileFdWrite(SeInstanceHdT seIns, int32_t fd, uint32_t startPos, BufT bufData)
{
    StatusInter ret = SeFileWrite(seIns, fd, startPos, bufData);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "Write file, fd=%" PRId32 "os no:%" PRId32, fd, DbAptGetErrno());
    }
    return ret;
}

static inline StatusInter RedoFileWriteByZoneId(
    SeInstanceHdT seIns, RedoLogFileT *file, uint32_t zoneId, uint32_t startPos, BufT bufData)
{
    return RedoFileFdWrite(seIns, file->fd[zoneId], startPos, bufData);
}

StatusInter RedoGetFilePath(
    RedoLogFileT *file, const RedoLogFileSetT *fileSet, const char *dir, uint32_t zoneIdx, uint32_t fileId)
{
    RedoMgrT *redoMgr = fileSet->redoMgr;
    char *wholePath = (char *)DbDynMemCtxAlloc(redoMgr->seDynMemCtx, sizeof(char) * DB_MAX_WHOLE_PATH);
    if (wholePath == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_MEM_FAILED, "RedoMgrMallocMem unsucc");
        return OUT_OF_MEMORY_MEM_FAILED;
    }
    int32_t err = snprintf_s(
        wholePath, DB_MAX_WHOLE_PATH, DB_MAX_WHOLE_PATH - 1, "%s/%s%u", dir, redoMgr->cfg.redoFilePrefix, fileId);
    if (err < 0) {
        SE_ERROR(INT_ERR_SECUREC_MEMORY_COPY_FAIL, "generate redo filePath, dir=%s, fileId=%" PRIu32 "", dir, fileId);
        DbDynMemCtxFree(redoMgr->seDynMemCtx, wholePath);
        return INT_ERR_SECUREC_MEMORY_COPY_FAIL;
    }
    uint32_t pathLen = (uint32_t)strlen(wholePath);
    DbDynMemCtxFree(redoMgr->seDynMemCtx, wholePath);
    DbDynMemCtxFree(redoMgr->seDynMemCtx, file->filePath[zoneIdx]);
    file->filePath[zoneIdx] = (char *)DbDynMemCtxAlloc(redoMgr->seDynMemCtx, sizeof(char) * (pathLen + 1));
    if (file->filePath[zoneIdx] == NULL) {
        SE_LAST_ERROR(
            OUT_OF_MEMORY_MEM_FAILED, "alloc mem, zoneIdx:%" PRIu32 ", pathLen:%" PRIu32 ".", zoneIdx, pathLen);
        return OUT_OF_MEMORY_MEM_FAILED;
    }
    err =
        snprintf_s(file->filePath[zoneIdx], pathLen + 1, pathLen, "%s/%s%u", dir, redoMgr->cfg.redoFilePrefix, fileId);
    if (err < 0) {
        SE_LAST_ERROR(INT_ERR_SECUREC_MEMORY_COPY_FAIL, "snprintf_s ret =%" PRId32, err);
        return INT_ERR_SECUREC_MEMORY_COPY_FAIL;
    }
    return STATUS_OK_INTER;
}

StatusInter RedoGenFilePath(
    const RedoLogFileArg *arg, const RedoLogFileSetT *fileSet, uint32_t fileId, RedoLogFileT *file)
{
    DB_POINTER3(arg, fileSet, file);

    for (uint32_t i = 0; i < fileSet->multizoneNum; i++) {
        StatusInter ret = RedoGetFilePath(file, fileSet, fileSet->redoMgr->cfg.redoDir[i], i, fileId);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    if (!SeIsStringEmpty(arg->dir)) {
        StatusInter ret = RedoGetFilePath(file, fileSet, arg->dir, DB_RESERVE_HANDLE_INDEX, fileId);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

static StatusInter RedoVerifyFileHeadChecksum(const RedoMgrT *redoMgr, RedoLogFileT *redoFile)
{
    if (redoMgr->crcCheckEnable &&
        !DbCheckCrc((char *)(&redoFile->info->head), sizeof(RedoLogFileHeadT), &redoFile->info->head.checkSum)) {
        SE_LAST_ERROR(INT_ERR_CRC_CHECK_ERROR, "redo file head");
        return INT_ERR_CRC_CHECK_ERROR;
    }
    if (redoMgr->tamperProofEnable) {
        DbDigest32T digestInFile = redoFile->info->head.headDigest;
        SeResetTamperProofDigest(&redoFile->info->head.headDigest);
        DbDigest32T genDigest = {0};
        StatusInter ret = SeDigest32Generate(
            (uint8_t *)&redoFile->info->head, sizeof(RedoLogFileHeadT), &genDigest, DB_TAMPER_PROOF_DIGEST_LEN);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "generate redo head check digest");
            return ret;
        }
        if (!SeIsTamperProofDigestSame(&digestInFile, &genDigest)) {
            SE_LAST_ERROR(DATA_CORRUPTION_INTER, "redo file head");
            SeLogErrorDigest(&genDigest, &digestInFile);
            return DATA_CORRUPTION_INTER;
        }
    }
    return STATUS_OK_INTER;
}

static StatusInter RedoLoadFileHeadInner(RedoMgrT *redoMgr, RedoLogFileT *redoFile, int zoneId, int fileFd)
{
    DB_POINTER(redoFile);
    const char *filePath = redoFile->filePath[zoneId];
    uint32_t readCount;
    Status ret = DbPreadFile(fileFd, (char *)&redoFile->info->head, sizeof(RedoLogFileHeadT), 0, &readCount);
    SeFileAlarmUpdate(ret);
    if (ret != GMERR_OK || readCount != sizeof(RedoLogFileHeadT)) {
        SE_ERROR(NO_DATA_INTER, "Read file, path=%s, ret=%d, readCount=%u", filePath, (int)ret, (uint32_t)readCount);
        return NO_DATA_INTER;
    }

    if (redoFile->info->head.magicNum != REDO_HEAD_MAGIC_NUMBER) {
        SE_LAST_ERROR(DATA_CORRUPTION_INTER, "redo file head unexpect, (%" PRIu32 ")", redoFile->info->head.magicNum);
        return DATA_CORRUPTION_INTER;
    }

    StatusInter retInt = RedoVerifyFileHeadChecksum(redoMgr, redoFile);
    if (retInt != STATUS_OK_INTER) {
        return retInt;
    }
    return STATUS_OK_INTER;
}

StatusInter RedoLoadFileHead(RedoMgrT *redoMgr, RedoLogFileT *redoFile, int32_t zoneId)
{
    DB_POINTER(redoFile);
    const char *filePath = redoFile->filePath[zoneId];
    size_t fileSize = 0;
    StatusInter ret = DbGetStatusInterErrno(DbFileSize(filePath, &fileSize));
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get file size, path=%s", filePath);
        return ret;
    }
    if (fileSize < sizeof(RedoLogFileHeadT)) {
        // 说明create后还没刷文件头就crash了
        ret = DbGetStatusInterErrno(DbRemoveFile(filePath));
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "remove empty redo file, path=%s", filePath);
            return ret;
        }
        return STATUS_OK_INTER;
    }
    int32_t fileFd = DB_INVALID_FD;
    ret = DbGetStatusInterErrno(DbOpenFile(filePath, READ_WRITE, PERM_USRRW, &fileFd));
    SeFileAlarmUpdate(DbGetExternalErrno(ret));
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Open redoFile, path=%s os no:%" PRId32, filePath, DbAptGetErrno());
        return ret;
    }

    ret = RedoLoadFileHeadInner(redoMgr, redoFile, zoneId, fileFd);
    if (ret != STATUS_OK_INTER) {
        DbCloseFile(fileFd);
        return ret;
    }
    redoFile->fd[zoneId] = fileFd;
    return STATUS_OK_INTER;
}

static StatusInter RedoCreateFile(
    RedoLogFileSetT *fileSet, const char *filePath, int32_t *fd, RedoLogFileT *redoFile, bool needGrantOther)
{
    DB_POINTER(redoFile);
    // 如果是open，异常分支不用删文件
    bool isOpen = DbFileExist(filePath);
    uint32_t permission = PERM_USRRW | (needGrantOther ? (S_IROTH | S_IWOTH) : 0);
    Status ret = DbOpenFile(filePath, CREATE_FILE | READ_WRITE, permission, fd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Create redoFile: path=%s os no:%" PRId32, filePath, DbAptGetErrno());
        return DbGetStatusInterErrno(ret);
    }

    StatusInter innerRet = RedoLogFileFlushHead(fileSet, redoFile, filePath, *fd);
    if (innerRet != STATUS_OK_INTER) {
        SE_ERROR(innerRet, "Flush redoFile head, path=%s.", filePath);
        DbCloseFile(*fd);
        *fd = DB_INVALID_FD;
        if (!isOpen) {
            // 如果是create出来的，需要把文件删掉
            (void)DbRemoveFile(filePath);
        }
        return innerRet;
    }
    return STATUS_OK_INTER;
}

StatusInter RedoSynchronizeOtherZone(RedoMgrT *redoMgr)
{
    DB_POINTER(redoMgr);

    RedoLogFileSetT *fileSet = &redoMgr->redoFiles;
    // when using -r to specify filePath, single persist-zone still need to process.
    if (fileSet->multizoneNum == 1) {
        return STATUS_OK_INTER;
    }
    // 做一次全量刷盘
    Status ret = CkptTrigger(redoMgr->seIns, CKPT_MODE_FULL, true, WAIT_MSECONDS_IN_REDO_LOG);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Trigger ckpt syn other zone");
        return DbGetStatusInterErrno(ret);
    }

    // 主分区的redo文件同步到不存在此redo文件的副分区
    for (uint32_t fileIndex = 0; fileIndex < redoMgr->cfg.fileCount; fileIndex++) {
        RedoLogFileT *file = &fileSet->files[fileIndex];
        // 主分区的文件不存在不进行同步
        if (!DbFileExist(file->filePath[fileSet->recoveryZoneId])) {
            continue;
        }
        if (fileSet->recoveryZoneId == DB_RESERVE_HANDLE_INDEX) {
            DbCloseFile(file->fd[DB_RESERVE_HANDLE_INDEX]);
            file->fd[DB_RESERVE_HANDLE_INDEX] = DB_INVALID_FD;
        }
        for (uint32_t i = 0; i < fileSet->multizoneNum; i++) {
            // 主分区不同步
            if (i == fileSet->redoMgr->redoFiles.recoveryZoneId) {
                continue;
            }
            StatusInter innerRet = RedoCreateFile(fileSet, file->filePath[i], &file->fd[i], file, false);
            if (innerRet != STATUS_OK_INTER) {
                return innerRet;
            }
        }
    }
    return STATUS_OK_INTER;
}

static inline uint64_t RedoLogFileFreeSize(const RedoLogFileT *redoFile)
{
    DB_POINTER(redoFile);
    DB_ASSERT(redoFile->info->size >= redoFile->info->head.writePos);

    return (uint64_t)redoFile->info->size - redoFile->info->head.writePos;
}

static inline uint64_t RedoLogFileFreeSizeSingleFile(const RedoLogFileT *redoFile)
{
    DB_ASSERT(
        (uint64_t)redoFile->info->size >= (redoFile->info->head.writePos - redoFile->info->head.readPos) +
                                              DB_CALC_ALIGN(sizeof(RedoLogFileHeadT), redoFile->info->head.blockSize));
    return (uint64_t)redoFile->info->size - (redoFile->info->head.writePos - redoFile->info->head.readPos) -
           DB_CALC_ALIGN(sizeof(RedoLogFileHeadT), redoFile->info->head.blockSize);
}

static StatusInter RedoInitFirstFile(RedoLogFileSetT *fileSet, uint32_t blockSize, uint32_t initFsn)
{
    DB_POINTER(fileSet);

    RedoFileSetPubT *fileSetPub = fileSet->fileSetPub;
    fileSetPub->curFile = 0;
    fileSetPub->firstActiveFile = fileSetPub->curFile;
    fileSetPub->curFsn = initFsn;
    RedoLogFileT *file = &fileSet->files[fileSetPub->curFile];
    RedoFileHeadInit(&file->info->head, fileSetPub->curFsn, blockSize);
    for (uint32_t zoneId = 0; zoneId < fileSet->multizoneNum; zoneId++) {
        StatusInter ret = RedoCreateFile(fileSet, file->filePath[zoneId], &file->fd[zoneId], file, false);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    file->info->status = REDO_LOG_FILE_CURRENT;

    return STATUS_OK_INTER;
}

static inline void RedoFileResetFd(RedoLogFileT *redoFile)
{
    for (uint32_t i = 0; i < DB_MAX_MULTIZONE + DB_RESERVE_HANDLE_NUM; i++) {
        redoFile->fd[i] = DB_INVALID_FD;
    }
}

static inline void RedoFileInit(RedoLogFileT *redoFile, uint32_t blockSize)
{
    RedoFileHeadInit(&redoFile->info->head, REDO_INVALID_FSN, blockSize);
    redoFile->info->status = REDO_LOG_FILE_UNUSED;
    redoFile->info->head.readPos = DB_CALC_ALIGN(sizeof(RedoLogFileHeadT), blockSize);
}

void RedoSetFileStatus(RedoLogFileT *file)
{
    if (file->info->head.fsn == REDO_INVALID_FSN) {
        file->info->status = REDO_LOG_FILE_INACTIVE;
    } else {
        file->info->status = REDO_LOG_FILE_ACTIVE;
    }
}

static void GetRedoFileActiveRange(
    const RedoLogFileArg *arg, RedoLogFileSetT *fileSet, uint32_t *curFile, uint32_t *firstActiveFile)
{
    uint32_t maxFsn = 0;
    uint32_t minFsn = DB_MAX_UINT32;

    for (uint32_t fileId = 0; fileId < arg->fileCount; fileId++) {
        RedoLogFileT *file = &fileSet->files[fileId];
        if (SeGetStorageStatus(arg->seIns) != SE_ON_DISK_CREATE &&
            DbFileExist(file->filePath[fileSet->recoveryZoneId])) {
            if (maxFsn < file->info->head.fsn) {
                maxFsn = file->info->head.fsn;
                *curFile = file->info->fileId;
            }

            if (file->info->head.fsn != REDO_INVALID_FSN && minFsn > file->info->head.fsn) {
                minFsn = file->info->head.fsn;
                *firstActiveFile = file->info->fileId;
            }
        }
    }
    return;
}

static StatusInter RedoAllocFlushHeadBuf(RedoLogFileSetT *fileSet, DbMemCtxT *memCtx)
{
    DB_POINTER2(fileSet, memCtx);
    uint32_t headBufSize = RedoLogFileHeaderSize(fileSet->blockSize);
    fileSet->flushHeadBuf = DbDynMemCtxAlloc(memCtx, headBufSize);
    if (fileSet->flushHeadBuf == NULL) {
        SE_ERROR(INTERNAL_ERROR_INTER, "Alloc buffer mem. size=%" PRIu32, headBufSize);
        return INTERNAL_ERROR_INTER;
    }
    (void)memset_s(fileSet->flushHeadBuf, headBufSize, 0, headBufSize);
    return STATUS_OK_INTER;
}

static StatusInter CreateRedoFileDir(const char *dir)
{
    DB_POINTER(dir);
    if (!DbDirExist(dir)) {
        Status ret = DbMakeDirWithGRPRXPermission(dir);
        SeFileAlarmUpdate(ret);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Create redo file dir:%s", dir);
            return DbGetStatusInterErrno(ret);
        }
    }
    return STATUS_OK_INTER;
}

static void RedoFileRemoveCheck(uint32_t fileId, RedoLogFileT *file, RedoLogFileSetT *fileSet)
{
    if (!SeGetNormalShutDown(fileSet->redoMgr->seIns)) {
        return;
    }
    for (uint32_t index = 0; index < DB_MAX_MULTIZONE + DB_RESERVE_HANDLE_NUM; index++) {
        // remove redo log file when normal shut down
        if (file->filePath[index] != NULL && DbFileExist(file->filePath[index])) {
            (void)DbRemoveFile(file->filePath[index]);
        }
    }
}

static StatusInter RedoFileInfoInit(const RedoLogFileArg *arg, RedoLogFileSetT *fileSet)
{
    StatusInter ret = STATUS_OK_INTER;
    fileSet->recoveryZoneId = SeIsStringEmpty(arg->dir) ? fileSet->recoveryZoneId : DB_RESERVE_HANDLE_INDEX;
    uint32_t recoveryZoneId = fileSet->recoveryZoneId;
    for (uint32_t fileId = 0; fileId < arg->fileCount; fileId++) {
        RedoLogFileT *file = &fileSet->files[fileId];
        ret = RedoGenFilePath(arg, fileSet, fileId, file);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        file->info->fileId = fileId;
        file->info->size = arg->fileMaxSize;
        RedoFileRemoveCheck(fileId, file, fileSet);
        RedoFileInit(file, arg->blockSize);
        RedoFileResetFd(file);
        if (SeGetStorageStatus(arg->seIns) != SE_ON_DISK_CREATE && DbFileExist(file->filePath[recoveryZoneId])) {
            ret = RedoLoadFileHead(fileSet->redoMgr, file, (int32_t)fileSet->recoveryZoneId);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
            RedoSetFileStatus(file);
        }
        if (fileSet->redoMgr->isSingleRedoFile) {
            fileSet->fileSetPub->totalFreeSize = RedoLogFileFreeSizeSingleFile(file);
        } else {
            fileSet->fileSetPub->totalFreeSize += RedoLogFileFreeSize(file);
        }
    }
    return ret;
}

static bool RedoFilesNeedCheck(RedoLogFileSetT *fileSet, const RedoLogFileArg *arg)
{
    if (SeGetStorageStatus(arg->seIns) != SE_ON_DISK_OPEN_RECOVER) {
        return false;
    }
    // 单文件不校验
    if (fileSet->redoMgr->isSingleRedoFile) {
        return false;
    }
    // durable增量，可以全量备份数据文件，不会刷redo文件，这种场景不用校验
    if (RedoPointIsInit(&fileSet->redoMgr->seIns->db->core.lrpPoint) &&
        RedoPointIsInit(&fileSet->redoMgr->seIns->db->core.truncPoint)) {
        return false;
    }
    return true;
}

static StatusInter RedoFilesCheck(RedoLogFileSetT *fileSet, const RedoLogFileArg *arg)
{
    if (!RedoFilesNeedCheck(fileSet, arg)) {
        return STATUS_OK_INTER;
    }
    // 根据lrpPoint校验一下加载上来的redo文件是否被截断过
    uint32_t recoveryZoneId = fileSet->recoveryZoneId;
    RedoPointT lrpPoint = fileSet->redoMgr->seIns->db->core.lrpPoint;
    uint32_t lrpFileIndex = (lrpPoint.fsn - 1) % arg->fileCount;
    char *filePath = NULL;
    size_t fileSize = 0;
    // 这个下标前的redo文件肯定是满的，否则可能被截断过
    for (uint32_t fileId = 0; fileId < lrpFileIndex; fileId++) {
        filePath = fileSet->files[fileId].filePath[recoveryZoneId];
        if (!DbFileExist(filePath)) {
            SE_LAST_ERROR(DATA_CORRUPTION_INTER, "redo file destroyed or not exist, path=%s", filePath);
            return DATA_CORRUPTION_INTER;
        }
        StatusInter ret = DbGetStatusInterErrno(DbFileSize(filePath, &fileSize));
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "get file size, path=%s, lrp:" REDO_POINT_LOG_FMT "", filePath, REDO_POINT(&lrpPoint));
            return ret;
        }
        if (fileSize != arg->fileMaxSize) {
            SE_LAST_ERROR(DATA_CORRUPTION_INTER,
                "redo file(%" PRIu32 ") size unexpect: %" PRIu32 ", lrp:" REDO_POINT_LOG_FMT, fileId,
                (uint32_t)fileSize, REDO_POINT(&lrpPoint));
            return DATA_CORRUPTION_INTER;
        }
    }
    filePath = fileSet->files[lrpFileIndex].filePath[recoveryZoneId];
    // 计算一下lrpPoint对应的文件大小，是否合理
    if (!DbFileExist(filePath)) {
        SE_LAST_ERROR(DATA_CORRUPTION_INTER, "lrp redo file destroyed or not exist, path=%s, lrp:" REDO_POINT_LOG_FMT,
            filePath, REDO_POINT(&lrpPoint));
        return DATA_CORRUPTION_INTER;
    }
    StatusInter ret = DbGetStatusInterErrno(DbFileSize(filePath, &fileSize));
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get file lrp file size, path=%s", filePath);
        return ret;
    }
    uint32_t lrpOffset = (uint32_t)lrpPoint.blockId * arg->blockSize;
    // 文件大小肯定是大于等于lrp点
    if (fileSize < lrpOffset) {
        SE_LAST_ERROR(DATA_CORRUPTION_INTER,
            "redo lrp file(%" PRIu32 ") size unexpect: %" PRIu32 ", lrp: " REDO_POINT_LOG_FMT, lrpFileIndex,
            (uint32_t)fileSize, REDO_POINT(&lrpPoint));
        return DATA_CORRUPTION_INTER;
    }
    // lrpPoint之后的redo日志不关注，丢了就丢了
    return STATUS_OK_INTER;
}

#if defined(FEATURE_TS)
static StatusInter RedoReserveSingleFile(RedoLogFileSetT *fileSet, RedoLogFileT *file)
{
    for (uint32_t zoneId = 0; zoneId < fileSet->multizoneNum; zoneId++) {
        int32_t tempFd = DB_INVALID_FD;
        Status ret = DbOpenFile(file->filePath[zoneId], CREATE_FILE | READ_WRITE, PERM_USRRW, &tempFd);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Create redoFile: path=%s os no:%" PRId32, file->filePath[zoneId], DbAptGetErrno());
            return DbGetStatusInterErrno(ret);
        }

        // 预留磁盘空间，但是文件大小不会变化
        ret = DbAdptFallocate(tempFd, FALLOC_FL_KEEP_SIZE, fileSet->fileMaxSize, 0);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Create redoFile: path=%s os no:%" PRId32, file->filePath[zoneId], DbAptGetErrno());
            DbCloseFile(tempFd);
            return DbGetStatusInterErrno(ret);
        }

        // 把头刷下去，防止文件被认为是crash产生的
        StatusInter innerRet = RedoLogFileFlushHead(fileSet, file, file->filePath[zoneId], tempFd);
        if (innerRet != STATUS_OK_INTER) {
            SE_ERROR(innerRet, "Flush redoFile head, path=%s.", file->filePath[zoneId]);
            DbCloseFile(tempFd);
            return innerRet;
        }

        DbCloseFile(tempFd);
    }
    return STATUS_OK_INTER;
}
#endif

static StatusInter RedoReserveFiles(RedoLogFileSetT *fileSet, const RedoLogFileArg *arg)
{
    // 当前非时序场景不预留
#if defined(FEATURE_TS)
    StatusInter ret = STATUS_OK_INTER;
    for (uint32_t fileIndex = 0; fileIndex < arg->fileCount; fileIndex++) {
        RedoLogFileT *file = &fileSet->files[fileIndex];
        ret = RedoReserveSingleFile(fileSet, file);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "reserve single redo file, fileIndex: %" PRIu32 "", fileIndex);
            return ret;
        }
    }
#endif
    return STATUS_OK_INTER;
}

static StatusInter CreateOrLoadRedoFiles(RedoLogFileSetT *fileSet, const RedoLogFileArg *arg)
{
    DB_POINTER2(fileSet, arg);
    RedoFileSetPubT *fileSetPub = fileSet->fileSetPub;

    StatusInter ret = RedoFileInfoInit(arg, fileSet);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    // redo文件的防截断校验
    ret = RedoFilesCheck(fileSet, arg);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "redo file unexpect.");
        return ret;
    }

    uint32_t lastActiveFile = INVALID_REDO_FILE_ID;
    uint32_t firstActiveFile = INVALID_REDO_FILE_ID;
    GetRedoFileActiveRange(arg, fileSet, &lastActiveFile, &firstActiveFile);
    if (lastActiveFile == INVALID_REDO_FILE_ID) {
        // 没有redo文件，预留并初始化第一个redo文件
        ret = RedoReserveFiles(fileSet, arg);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "reserve redo files");
            return ret;
        }
        ret = RedoInitFirstFile(fileSet, arg->blockSize, REDO_INITIAL_FSN);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "init first redo file");
            return ret;
        }
    } else {
        // 有redo文件
        fileSetPub->curFile = lastActiveFile;
        fileSetPub->firstActiveFile = firstActiveFile;
        fileSet->files[lastActiveFile].info->status = REDO_LOG_FILE_CURRENT;
        fileSetPub->curFsn = fileSet->files[fileSetPub->curFile].info->head.fsn;
    }
    return STATUS_OK_INTER;
}

StatusInter RedoLogFileInit(RedoLogFileArg *arg, RedoMgrT *redoMgr, RedoLogFileSetT *fileSet)
{
    DB_POINTER3(fileSet, arg, redoMgr);
    StatusInter ret = STATUS_OK_INTER;
    DB_ASSERT(arg->fileCount <= MAX_REDO_LOG_FILES);
    RedoFileSetPubT *fileSetPub = &redoMgr->pubCtx->fileSet;
    fileSet->fileCount = arg->fileCount;
    fileSet->fileMaxSize = arg->fileMaxSize;
    fileSet->blockSize = arg->blockSize;
    DbSpinInit(&fileSetPub->lock);
    fileSetPub->totalFreeSize = 0;

    for (uint32_t i = 0; i < fileSet->multizoneNum; i++) {
        ret = CreateRedoFileDir(redoMgr->cfg.redoDir[i]);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(
                ret, "unable to create redo file dir, zone id = %" PRIu32 ", dir = %s", i, redoMgr->cfg.redoDir[i]);
            return ret;
        }
    }

    ret = RedoAllocFlushHeadBuf(fileSet, redoMgr->seDynMemCtx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    fileSet->fileSetPub = fileSetPub;
    fileSet->redoMgr = redoMgr;
    return CreateOrLoadRedoFiles(fileSet, arg);
}

static StatusInter RedoResetAllPoints(RedoMgrT *redoMgr, RedoLogFileSetT *fileSet, uint32_t newRedoFsn)
{
    RedoPointT resetPoint = redoMgr->pubCtx->curPoint;
    resetPoint.fsn = newRedoFsn;
    // 第一个block用于放RedoLogFileHeadT，所以这里初始化为1
    resetPoint.blockId = REDO_INITIAL_BLOCK;
    redoMgr->pubCtx->curPoint = resetPoint;
    fileSet->fileSetPub->curFsn = newRedoFsn;
    CkptRefreshPoint(redoMgr->seIns);
    // 将core中的truncatePoint和lrpPoint落盘
    StatusInter ret = DbSaveCore(redoMgr->seIns);
    if (ret != STATUS_OK_INTER) {
        // 刷盘失败时，DB直接退出
        SE_ERROR(ret, "flush core redopoints");
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter RedoLogFilesReset(RedoLogFileSetT *fileSet)
{
    RedoSetEnableImpl(fileSet->redoMgr, false);
    // 单文件始终为1
    uint32_t newRedoFsn = REDO_INITIAL_FSN;
    if (!fileSet->redoMgr->isSingleRedoFile) {
        // 多文件时，递增而不是重置为1，用于区分已经重置掉的文件，但是又要满足文件索引的映射
        uint32_t nextLoop = (fileSet->fileSetPub->curFsn - 1) / fileSet->fileCount + 1;
        newRedoFsn = nextLoop * fileSet->fileCount + 1;
    }
    // 先刷新core中的point，再覆盖redo文件
    StatusInter ret = RedoResetAllPoints(fileSet->redoMgr, fileSet, newRedoFsn);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "reset all redo points");
        RedoSetEnableImpl(fileSet->redoMgr, true);
        return ret;
    }
    fileSet->fileSetPub->totalFreeSize = 0;
    // 文件不删除，继续复用
    for (uint32_t fileId = 0; fileId < fileSet->fileCount; fileId++) {
        RedoLogFileT *file = &fileSet->files[fileId];
        RedoFileInit(file, fileSet->blockSize);
        // 关闭所有文件的句柄
        for (uint32_t i = 0; i < DB_MAX_MULTIZONE + DB_RESERVE_HANDLE_NUM; i++) {
            if (file->fd[i] != DB_INVALID_FD) {
                DbCloseFile(file->fd[i]);
                file->fd[i] = DB_INVALID_FD;
            }
        }
        if (fileSet->redoMgr->isSingleRedoFile) {
            fileSet->fileSetPub->totalFreeSize = RedoLogFileFreeSizeSingleFile(file);
        } else {
            // 所有redo文件都认为是空的
            fileSet->fileSetPub->totalFreeSize += RedoLogFileFreeSize(file);
        }
    }
    // 使用第一个redoFile
    ret = RedoInitFirstFile(fileSet, fileSet->blockSize, newRedoFsn);
    RedoSetEnableImpl(fileSet->redoMgr, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "init first redo file");
        return ret;
    }
    SE_WARN(STATUS_OK_INTER, "redo files reset, current fsn: %" PRIu32, newRedoFsn);
    return ret;
}

static void RedoLogFileClose(RedoLogFileSetT *fileSet)
{
    RedoLogFileT *file = &fileSet->files[fileSet->fileSetPub->firstActiveFile];
    for (uint32_t index = 0; index < DB_MAX_MULTIZONE + DB_RESERVE_HANDLE_NUM; index++) {
        if (file->fd[index] != DB_INVALID_FD) {
            DbCloseFile(file->fd[index]);
            file->fd[index] = DB_INVALID_FD;
        }
    }
    if (fileSet->flushHeadBuf) {
        DbDynMemCtxFree(fileSet->redoMgr->seDynMemCtx, fileSet->flushHeadBuf);
        fileSet->flushHeadBuf = NULL;
    }
}

void RedoLogFileDestroy(RedoLogFileSetT *fileSet)
{
    DB_POINTER(fileSet);
    bool removeFile = SeGetNormalShutDown(fileSet->redoMgr->seIns) && fileSet->redoMgr->cfg.fileDropOnClose;
    if (fileSet->redoMgr->isSingleRedoFile) {
        RedoLogFileClose(fileSet);
        return;
    }
    for (uint32_t index = 0; index < DB_MAX_MULTIZONE + DB_RESERVE_HANDLE_NUM; index++) {
        for (int32_t i = (int32_t)fileSet->fileCount - 1; i >= 0; --i) {
            RedoLogFileT *file = &fileSet->files[i];
            if (file->fd[index] != DB_INVALID_FD) {
                DbCloseFile(file->fd[index]);
                file->fd[index] = DB_INVALID_FD;
            }
            // remove redo log file when normal shut down
            if (removeFile && file->filePath[index] != NULL && DbFileExist(file->filePath[index])) {
                (void)DbRemoveFile(file->filePath[index]);
            }
        }
    }
    DbDynMemCtxFree(fileSet->redoMgr->seDynMemCtx, fileSet->flushHeadBuf);
    fileSet->flushHeadBuf = NULL;
}

static StatusInter RedoFileAllocate(RedoLogFileSetT *fileSet)
{
    // 切换redo文件前，先将当前的redo文件写满，用于重启时的防截断校验(writePos不变)
    RedoLogFileT *file = &fileSet->files[fileSet->fileSetPub->curFile];
    for (uint32_t zoneId = 0; zoneId < fileSet->multizoneNum; zoneId++) {
        // 这里只是文件大小显示最大，实际占用磁盘的大小不变
        StatusInter ret = DbGetStatusInterErrno(DbAdptFtruncate(file->fd[zoneId], file->info->size));
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Fallocate read file, path=%s", file->filePath[zoneId]);
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter RedoLogFileSwitch(RedoLogFileSetT *fileSet, RedoLogFileT **nextFile)
{
    DB_POINTER2(fileSet, nextFile);
    // 切换redo文件前，先将当前的redo文件写满，用于重启时的防截断校验(writePos不变)
    StatusInter ret = RedoFileAllocate(fileSet);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Fallocate read file");
        return ret;
    }
    uint32_t nextFileId = RedoGetNextFileId(fileSet);
    DB_ASSERT(nextFileId != fileSet->fileSetPub->firstActiveFile);  // file space ensure in RedoLogOpen

    RedoLogFileT *file = &fileSet->files[nextFileId];
    if (file->info->status == REDO_LOG_FILE_UNUSED || file->info->status == REDO_LOG_FILE_INACTIVE) {
        ++fileSet->fileSetPub->curFsn;
        RedoLogFileT *curFile = &fileSet->files[fileSet->fileSetPub->curFile];
        DB_ASSERT(curFile->info->head.fsn + 1 == fileSet->fileSetPub->curFsn);
        RedoLogFileT *mFile = &fileSet->files[nextFileId];
        RedoFileHeadInit(&mFile->info->head, fileSet->fileSetPub->curFsn, fileSet->blockSize);
        for (uint32_t i = 0; i < fileSet->multizoneNum; i++) {
            ret = RedoCreateFile(fileSet, file->filePath[i], &file->fd[i], mFile, false);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
        }
        curFile->info->status = REDO_LOG_FILE_ACTIVE;
        mFile->info->status = REDO_LOG_FILE_CURRENT;
    }
    fileSet->fileSetPub->curFile = nextFileId;
    *nextFile = file;
    SE_WARN(STATUS_OK_INTER, "redo file switch to (%" PRIu32 "), curFsn(%" PRIu32 ")", nextFileId,
        fileSet->fileSetPub->curFsn);
    return STATUS_OK_INTER;
}

static inline bool IsRedoLogFileLTPoint(const RedoLogFileT *redoFile, const RedoPointT *point)
{
    return redoFile->info->head.fsn < point->fsn;
}

void RedoLogFileRecycle(RedoLogFileSetT *fileSet, const RedoPointT *point)
{
    DB_POINTER2(fileSet, point);

    DbSpinLock(&fileSet->fileSetPub->lock);
    uint32_t activeFileId = fileSet->fileSetPub->firstActiveFile;
    RedoLogFileT *file = &fileSet->files[activeFileId];
    while (IsRedoLogFileLTPoint(file, point)) {
        DB_ASSERT(file->info->status != REDO_LOG_FILE_CURRENT);
        file->info->status = REDO_LOG_FILE_INACTIVE;
        file->info->head.writePos = DB_CALC_ALIGN(sizeof(RedoLogFileHeadT), file->info->head.blockSize);
        for (uint32_t i = 0; i < DB_MAX_MULTIZONE + DB_RESERVE_HANDLE_NUM; i++) {
            if (file->fd[i] != DB_INVALID_FD) {
                DbCloseFile(file->fd[i]);
                file->fd[i] = DB_INVALID_FD;
            }
        }
        fileSet->fileSetPub->totalFreeSize += RedoLogFileFreeSize(file);
        DB_LOG_INFO("Recycle redoFile, id:%" PRIu32 ", fsn:%" PRIu32 ", totalFreeSizse:%" PRIu64, file->info->fileId,
            file->info->head.fsn, fileSet->fileSetPub->totalFreeSize);
        activeFileId = CycleMoveNext(activeFileId, fileSet->fileCount);
        file = &fileSet->files[activeFileId];
    }
    fileSet->fileSetPub->firstActiveFile = activeFileId;
    DbSpinUnlock(&fileSet->fileSetPub->lock);
}

void RedoFilesClose(RedoLogFileSetT *fileSet)
{
    for (uint32_t zoneId = 0; zoneId < DB_MAX_MULTIZONE + DB_RESERVE_HANDLE_NUM; zoneId++) {
        for (int32_t i = (int32_t)fileSet->fileCount - 1; i >= 0; --i) {
            RedoLogFileT *file = &fileSet->files[i];
            if (file->fd[zoneId] != DB_INVALID_FD) {
                file->info->status = REDO_LOG_FILE_INACTIVE;
                file->info->head.writePos = DB_CALC_ALIGN(sizeof(RedoLogFileHeadT), file->info->head.blockSize);
                DbCloseFile(file->fd[zoneId]);
                file->fd[zoneId] = DB_INVALID_FD;
            }
        }
    }
}

void RedoLogSingleFileRecycle(RedoLogFileSetT *fileSet, const RedoPointT *point)
{
    DbSpinLock(&fileSet->fileSetPub->lock);
    RedoLogFileT *file = &fileSet->files[fileSet->fileSetPub->curFile];
    // readPos should and must be only increased
    DB_ASSERT(file->info->head.readPos <= point->blockId * file->info->head.blockSize);
    file->info->head.readPos = point->blockId * file->info->head.blockSize;
    fileSet->fileSetPub->totalFreeSize = RedoLogFileFreeSizeSingleFile(file);
    DbSpinUnlock(&fileSet->fileSetPub->lock);
    // Recycle single redo file to
    DB_LOG_INFO("RedoFile to " REDO_POINT_LOG_FMT ", readPos:%" PRIu64 ", freeSize(%" PRIu64 ")", REDO_POINT(point),
        file->info->head.readPos, fileSet->fileSetPub->totalFreeSize);
}

StatusInter RedoLogFileFlushHead(RedoLogFileSetT *fileSet, RedoLogFileT *redoFile, const char *filePath, int32_t fd)
{
    DB_POINTER(redoFile);

    StatusInter ret = RedoCalcFileHeadChecksum(fileSet->redoMgr, &redoFile->info->head);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "calculate redo head checksum");
        return ret;
    }
    DB_POINTER(fileSet->flushHeadBuf);
    uint32_t size = RedoLogFileHeaderSize(fileSet->blockSize);
    errno_t err = memcpy_s(fileSet->flushHeadBuf, size, &redoFile->info->head, sizeof(RedoLogFileHeadT));
    if (err != EOK) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "cp redoHead, SIZE dst:%" PRIu32 " src:%" PRIu32, size,
            (uint32_t)sizeof(RedoLogFileHeadT));
        return MEMORY_OPERATE_FAILED_INTER;
    }
    BufT bufData = {(char *)fileSet->flushHeadBuf, size};
    ret = SeFileWrite(fileSet->redoMgr->seIns, fd, 0, bufData);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "Write file, path=%s, fd=%d", filePath, fd);
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter RedoLogFileFlushHeadByZoneId(RedoLogFileSetT *fileSet, RedoLogFileT *redoFile, uint32_t zoneId)
{
    DB_POINTER(redoFile);

    StatusInter ret = RedoCalcFileHeadChecksum(fileSet->redoMgr, &redoFile->info->head);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "calculate redo head checksum");
        return ret;
    }
    uint32_t size = RedoLogFileHeaderSize(fileSet->blockSize);
    DB_POINTER(fileSet->flushHeadBuf);
    (void)memcpy_s(fileSet->flushHeadBuf, size, &redoFile->info->head, sizeof(RedoLogFileHeadT));

    BufT bufData = {.buf = fileSet->flushHeadBuf, .size = size};
    return RedoFileWriteByZoneId(fileSet->redoMgr->seIns, redoFile, zoneId, 0, bufData);
}

void RedoLogFileFlushSingleFileInit(RedoLogFileSetT *fileSet, uint32_t size, RedoPointT *point)
{
    DbSpinLock(&fileSet->fileSetPub->lock);
    RedoLogFileT *file = &fileSet->files[fileSet->fileSetPub->curFile];
    DB_ASSERT(RedoLogFileFreeSizeSingleFile(file) >= size);
    point->fsn = file->info->head.fsn;
    point->blockId = file->info->head.writePos / file->info->head.blockSize;
    DbSpinUnlock(&fileSet->fileSetPub->lock);
}

StatusInter RedoLogFileFlushInit(RedoLogFileSetT *fileSet, uint32_t size, RedoPointT *point)
{
    DB_POINTER(fileSet);
    if (fileSet->redoMgr->isSingleRedoFile) {
        RedoLogFileFlushSingleFileInit(fileSet, size, point);
        return STATUS_OK_INTER;
    }

    DbSpinLock(&fileSet->fileSetPub->lock);
    RedoLogFileT *file = &fileSet->files[fileSet->fileSetPub->curFile];
    uint64_t fileFreeSize = RedoLogFileFreeSize(file);
    // 如果当前redo文件空间不足，则切换到下一个文件
    if (fileFreeSize < size) {
        StatusInter ret = RedoLogFileSwitch(fileSet, &file);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            DbSpinUnlock(&fileSet->fileSetPub->lock);
            return ret;
        }
        fileSet->fileSetPub->totalFreeSize -= fileFreeSize;
    }
    point->fsn = file->info->head.fsn;
    point->blockId = file->info->head.writePos / file->info->head.blockSize;
    DbSpinUnlock(&fileSet->fileSetPub->lock);

    return STATUS_OK_INTER;
}

static StatusInter RedoFileWriteMutiFiles(
    SeInstanceHdT seIns, RedoLogFileT *file, uint32_t fileId, BufT bufData, uint64_t writePos)
{
    return RedoFileFdWrite(seIns, file->fd[fileId], (uint32_t)writePos, bufData);
}

static StatusInter RedoFileWriteSingleFile(
    SeInstanceHdT seIns, RedoLogFileT *file, uint32_t zoneId, BufT bufData, uint64_t writePos)
{
    uint64_t availableSpace = file->info->size - writePos % file->info->size;
    uint64_t writeSize = bufData.size <= availableSpace ? bufData.size : availableSpace;
    if (writePos % file->info->size + writeSize > file->info->size) {
        SE_ERROR(INTERNAL_ERROR_INTER, "RedoFile write offset inv");
        return INTERNAL_ERROR_INTER;
    }
    // 如果文件剩余空间足够，直接写入文件尾
    BufT bufDataPart1 = {bufData.buf, (uint32_t)writeSize};
    StatusInter ret = RedoFileWriteByZoneId(seIns, file, zoneId, (uint32_t)writePos % file->info->size, bufDataPart1);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "wrong redolog-write");
        return ret;
    }
    if (bufData.size > availableSpace) {
        // 如果文件的剩余空间写不下了，需要将剩余的数据写到文件头
        uint64_t padding = DB_CALC_ALIGN(sizeof(RedoLogFileHeadT), file->info->head.blockSize);
        BufT bufDataPart2 = {bufData.buf + writeSize, (uint32_t)(bufData.size - writeSize)};
        ret = RedoFileWriteByZoneId(
            seIns, file, zoneId, (uint32_t)(writePos + writeSize + padding) % file->info->size, bufDataPart2);
        if ((writePos + writeSize + padding) % file->info->size + bufData.size - writeSize > file->info->size) {
            SE_ERROR(ret, "RedoFile offset inv");
            return DATA_CORRUPTION_INTER;
        }
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "wrong redolog-write");
        }
    }
    return ret;
}

static uint64_t RedoCalPadding(RedoLogFileSetT *fileSet, RedoLogFileT *file, uint32_t size, uint64_t writePos)
{
    if (!fileSet->redoMgr->isSingleRedoFile) {
        return 0;
    }
    uint64_t availableSpace = file->info->size - writePos % file->info->size;
    if (availableSpace <= size) {
        return DB_CALC_ALIGN(sizeof(RedoLogFileHeadT), file->info->head.blockSize);
    }
    return 0;
}

static void RedoFileUpdateWritePos(RedoLogFileSetT *fileSet, RedoLogFileT *file, uint32_t size, uint64_t padding)
{
    if (!fileSet->redoMgr->isSingleRedoFile || padding == 0) {
        file->info->head.writePos += size;
        return;
    }
    uint64_t availableSpace = file->info->size - file->info->head.writePos % file->info->size;
    uint64_t writeSize = size <= availableSpace ? size : availableSpace;
    DB_ASSERT(writeSize == availableSpace);
    file->info->head.writePos += DB_CALC_ALIGN(writeSize, file->info->head.blockSize);
    if (file->info->head.writePos % file->info->size == 0) {
        file->info->head.writePos += DB_CALC_ALIGN(sizeof(RedoLogFileHeadT), file->info->head.blockSize);
    }
    file->info->head.writePos += DB_CALC_ALIGN(size - writeSize, file->info->head.blockSize);
}

static StatusInter RedoWriteMultizone(RedoLogFileSetT *fileSet, RedoLogFileT *file, uint8_t *data, uint32_t size)
{
    bool isSingleRedoFile = fileSet->redoMgr->isSingleRedoFile;
    StatusInter ret = STATUS_OK_INTER;
    uint64_t writePos = file->info->head.writePos;
    // 单redo文件时，如果writePos循环到了redo文件头，需要考虑文件头的偏移；多redo文件时，不用考虑
    uint64_t padding = RedoCalPadding(fileSet, file, size, writePos);
    RedoFileUpdateWritePos(fileSet, file, size, padding);
    for (uint32_t i = 0; i < fileSet->multizoneNum; i++) {
        if (SeCanSkipOfflineZone(fileSet->redoMgr->seIns, &fileSet->redoMgr->seIns->db->zoneCtrl, i) ||
            file->fd[i] == DB_INVALID_FD) {
            continue;
        }
        uint64_t writeStartTime = DbClockGetTsc();
        BufT bufData = {.buf = (char *)data, .size = size};
        if (isSingleRedoFile) {
            ret = RedoFileWriteSingleFile(fileSet->redoMgr->seIns, file, i, bufData, writePos);
        } else {
            ret = RedoFileWriteMutiFiles(fileSet->redoMgr->seIns, file, i, bufData, writePos);
        }
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "Write redo file, path=%s, fd=%" PRId32, file->filePath[i], file->fd[i]);
            file->info->head.writePos = writePos;
            return ret;
        }

        uint64_t writeEndTime = DbClockGetTsc();
        ret = RedoLogFileFlushHeadByZoneId(fileSet, file, i);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "Flush file head, path=%s, fd=%" PRId32, file->filePath[i], file->fd[i]);
            file->info->head.writePos = writePos;
            return ret;
        }

        if (SECUREC_UNLIKELY(DbToMseconds(writeEndTime - writeStartTime) > SE_FILE_TIMEOUT_MS)) {
            DB_LOG_WARN(GMERR_REQUEST_TIME_OUT, "redo write too long. (write: %" PRIu64 ")",
                DbToMseconds(writeEndTime - writeStartTime));
        }
    }
    if (isSingleRedoFile) {
        fileSet->fileSetPub->totalFreeSize = RedoLogFileFreeSizeSingleFile(file);
    } else {
        fileSet->fileSetPub->totalFreeSize -= size;
    }
    return STATUS_OK_INTER;
}

StatusInter RedoLogFileSync(RedoMgrT *redoMgr, bool isInCkpt)
{
    if (!RedoIsEnable(redoMgr)) {
        return STATUS_OK_INTER;
    }

    if (!isInCkpt && redoMgr->cfg.flushByTrx == REDO_LOG_FLUSH_BY_TRX_LAZY_SYNC) {
        return STATUS_OK_INTER;
    }

    uint64_t syncStartTime = DbClockGetTsc();
    RedoLogFileSetT *fileSet = &redoMgr->redoFiles;
    RedoLogFileT *file = &fileSet->files[fileSet->fileSetPub->curFile];

    StatusInter ret = STATUS_OK_INTER;
    for (uint32_t i = 0; i < fileSet->multizoneNum; i++) {
        //  If the zone is offline, skip the sync operation.
        if (SeCanSkipOfflineZone(fileSet->redoMgr->seIns, &fileSet->redoMgr->seIns->db->zoneCtrl, i) ||
            file->fd[i] == DB_INVALID_FD) {
            continue;
        }
        ret = DbFsyncFile(file->fd[i]);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "Sync file, path=%s, fd=%" PRId32, file->filePath[i], file->fd[i]);
            return ret;
        }
    }
    uint64_t syncEndTime = DbClockGetTsc();
    if (SECUREC_UNLIKELY(DbToMseconds(syncEndTime - syncStartTime) > SE_FILE_TIMEOUT_MS)) {
        DB_LOG_WARN(
            GMERR_REQUEST_TIME_OUT, "redo sync too long. sync:%" PRIu64, DbToMseconds(syncEndTime - syncStartTime));
    }
    return STATUS_OK_INTER;
}

StatusInter RedoLogFileFlush(RedoLogFileSetT *fileSet, uint8_t *data, uint32_t size)
{
    DB_POINTER2(fileSet, data);

    StatusInter ret = STATUS_OK_INTER;
    DbSpinLock(&fileSet->fileSetPub->lock);
    RedoLogFileT *file = &fileSet->files[fileSet->fileSetPub->curFile];
    bool isSingleRedoFile = fileSet->redoMgr->isSingleRedoFile;
    uint64_t freeSize = 0;
    if (isSingleRedoFile) {
        freeSize = RedoLogFileFreeSizeSingleFile(file);
    } else {
        freeSize = RedoLogFileFreeSize(file);
    }
    DB_ASSERT(freeSize >= size);
    ret = RedoWriteMultizone(fileSet, file, data, size);
    DbSpinUnlock(&fileSet->fileSetPub->lock);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Flush redoLogFile, curFile:%" PRIu32 ", size:%" PRIu32 ", freeSize:%" PRIu64,
            fileSet->fileSetPub->curFile, size, freeSize);
        return ret;
    }
    return ret;
}

static StatusInter RedoLogLoadReadFile(int32_t fd, int64_t offset, void *buf, size_t count, size_t *readCount)
{
    Status ret = DbPreadFile(fd, buf, (uint32_t)count, offset, (uint32_t *)readCount);
    SeFileAlarmUpdate(ret);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "RedoLog Load file, fd=%" PRId32 "osNo:%" PRId32, fd, DbAptGetErrno());
    }
    return DbGetStatusInterErrno(ret);
}

StatusInter RedoLogLoadSingleFile(
    RedoLogFileSetT *fileSet, RedoLogFileT *redoFile, uint8_t *buf, uint32_t bufSize, uint64_t offset)
{
    DB_POINTER2(redoFile, buf);
    DB_ASSERT(offset < redoFile->info->head.writePos);

    uint64_t size = redoFile->info->head.writePos - offset;
    size = DB_MIN(size, bufSize);
    uint64_t tmpOffset = offset % redoFile->info->size;
    size_t readCount = 0;
    StatusInter ret = STATUS_OK_INTER;
    if (size <= redoFile->info->size - tmpOffset) {
        // here means the redo log not be separated into two parts
        ret = RedoLogLoadReadFile(redoFile->fd[fileSet->recoveryZoneId], tmpOffset, buf, size, &readCount);
    } else {
        uint64_t truncatedSize = redoFile->info->size - tmpOffset;
        ret = RedoLogLoadReadFile(redoFile->fd[fileSet->recoveryZoneId], tmpOffset, buf, truncatedSize, &readCount);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Seek or read file, path=%s", redoFile->filePath[fileSet->recoveryZoneId]);
            return ret;
        }
        size_t readCountTmp = 0;
        ret = RedoLogLoadReadFile(redoFile->fd[fileSet->recoveryZoneId],
            (int64_t)DB_CALC_ALIGN(sizeof(RedoLogFileHeadT), redoFile->info->head.blockSize), buf + truncatedSize,
            size - truncatedSize, &readCountTmp);
        readCount += readCountTmp;
    }
    if (ret != STATUS_OK_INTER) {
        // Unable to seek or read file
        SE_ERROR(ret, "Seek or read file, path=%s", redoFile->filePath[fileSet->recoveryZoneId]);
        return ret;
    }
    if (SECUREC_UNLIKELY(readCount != (size_t)size)) {
        SE_ERROR(DATA_EXCEPTION_INTER, "Check readCount:%zu, size:%" PRIu64 ".", readCount, size);
        return DATA_EXCEPTION_INTER;
    }
    return ret;
}

StatusInter RedoLogLoad(
    RedoLogFileSetT *fileSet, RedoLogFileT *redoFile, uint8_t *buf, uint32_t bufSize, uint64_t offset)
{
    if (fileSet->redoMgr->isSingleRedoFile) {
        return RedoLogLoadSingleFile(fileSet, redoFile, buf, bufSize, offset);
    }
    DB_POINTER2(redoFile, buf);
    DB_ASSERT(offset < redoFile->info->head.writePos);

    uint64_t size = redoFile->info->head.writePos - offset;
    size = size < bufSize ? size : bufSize;
    size_t readCount = 0;
    StatusInter ret = RedoLogLoadReadFile(redoFile->fd[fileSet->recoveryZoneId], offset, buf, size, &readCount);
    if (ret != STATUS_OK_INTER) {
        // Unable to seek or read file
        SE_ERROR(ret, "RedoLog load file, path=%s", redoFile->filePath[fileSet->recoveryZoneId]);
        return ret;
    }
    if (SECUREC_UNLIKELY(readCount != (size_t)size)) {
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "Check readCount:%zu, size:%" PRIu64, readCount, size);
        return DATA_EXCEPTION_INTER;
    }
    return STATUS_OK_INTER;
}

StatusInter RedoReloadLogFile(SeInstanceT *seIns)
{
    DB_POINTER(seIns);
    RedoMgrT *redoMgr = seIns->redoMgr;
    RedoLogFileArg arg = {.seIns = seIns,
        .dir = seIns->seConfig.recoveryPath,
        .fileCount = redoMgr->cfg.fileCount,
        .fileMaxSize = redoMgr->cfg.fileSize,
        .blockSize = redoMgr->cfg.blockSize};
    StatusInter ret = RedoLogFileInit(&arg, redoMgr, &redoMgr->redoFiles);
    if (ret != STATUS_OK_INTER) {
        RedoMgrReleaseMem(redoMgr);
    }
    return ret;
}

StatusInter RedoCalcFileHeadChecksum(const RedoMgrT *redoMgr, RedoLogFileHeadT *head)
{
    if (redoMgr->crcCheckEnable) {
        DbAddCheckSum((char *)(head), sizeof(RedoLogFileHeadT), &head->checkSum);
        return STATUS_OK_INTER;
    }
    if (redoMgr->tamperProofEnable) {
        // 现将摘要字段置为0，然后计算摘要
        SeResetTamperProofDigest(&head->headDigest);
        DbDigest32T tmpPageDigest = {0};
        StatusInter ret =
            SeDigest32Generate((uint8_t *)head, sizeof(RedoLogFileHeadT), &tmpPageDigest, DB_TAMPER_PROOF_DIGEST_LEN);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "update page digest.");
            return ret;
        }
        head->headDigest = tmpPageDigest;
    }
    return STATUS_OK_INTER;
}

StatusInter RedoCalcBatchChecksum(RedoLogBatchT *redoBatch, const RedoMgrT *redoMgr)
{
    if (redoMgr->crcCheckEnable) {
        DbAddCheckSum((char *)redoBatch, redoBatch->size, &redoBatch->checkSum);
        return STATUS_OK_INTER;
    }
    if (redoMgr->tamperProofEnable) {
        // 如果开启了防篡改，将batch摘要记在batch的最后
        DbDigest32T tmpBatchDigest = {0};
        StatusInter ret = SeDigest32Generate(
            (uint8_t *)redoBatch, redoBatch->size - sizeof(DbDigest32T), &tmpBatchDigest, DB_TAMPER_PROOF_DIGEST_LEN);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "update batch digest.");
            return ret;
        }
        *(DbDigest32T *)((uint8_t *)redoBatch + redoBatch->size - sizeof(DbDigest32T)) = tmpBatchDigest;
    }
    return STATUS_OK_INTER;
}
