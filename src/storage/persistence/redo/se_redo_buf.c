/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: redo_buf.c
 * Description:
 * Author:
 * Create: 2021.8.16
 */
#include "se_redo_buf.h"
#include "se_redo_file.h"
#include "adpt_atomic.h"
#include "se_log.h"
#include "adpt_time.h"
#include "db_crc.h"
#include "db_utils.h"
#include "db_crash_debug.h"
#include "db_dyn_load.h"
#include "se_database.h"
#include "se_verify_inner.h"

static inline bool RedoVerifyBatch(const RedoLogBatchT *batch, const RedoLogBatchTailT *batchTail)
{
    if (batch->head.magicNum != REDO_MAGIC_NUMBER || batchTail->magicNum != REDO_MAGIC_NUMBER ||
        batch->head.point.batchId != batchTail->point.batchId || batch->size == 0) {
        return false;
    }
    // fix: verify checksum
    return true;
}

void RedoLogPutRecord(RedoRunCtxT *redoCtx, RedoLogTypeE type, const PageIdT *addr, const uint8_t *data, uint32_t size)
{
    DB_POINTER(redoCtx);

    RedoLogGroupT *group = (RedoLogGroupT *)redoCtx->buf;
    if (redoCtx->bufPos == 0) {
        group->lsn = 0;
        group->trxId = redoCtx->trxId;
        group->size = sizeof(RedoLogGroupT);
        redoCtx->bufPos += sizeof(RedoLogGroupT);
    }
    uint32_t remainSize = redoCtx->bufSize - redoCtx->bufPos;
    uint32_t requestSize = size + sizeof(RedoLogRecordT);
    if (remainSize < requestSize) {
        // RedoRunCtxT buffer no enough
        SE_LAST_ERROR(INT_ERR_REDO_BUFFER_NOT_ENOUGH,
            "RedoCtxBuf, remain:%" PRIu32 ", request:%" PRIu32 " bufSize:%" PRIu32 " bufPos:%" PRIu32 "", remainSize,
            requestSize, redoCtx->bufSize, redoCtx->bufPos);
        DB_ASSERT(false);
    }
    RedoLogRecordT *record = (RedoLogRecordT *)(void *)(redoCtx->buf + redoCtx->bufPos);
    record->type = (uint16_t)type;
    record->size = requestSize;
#if !defined(NDEBUG) && defined(SYS32BITS)
    // 时序arm32长稳，record pointer不是4字节对齐可能导致bus error
    (void)memcpy_s(&record->addr, sizeof(PageIdT), addr, sizeof(PageIdT));
#else
    record->addr = *addr;
#endif
    redoCtx->bufPos += sizeof(RedoLogRecordT);
    if (data != NULL) {
        errno_t err = memcpy_s(redoCtx->buf + redoCtx->bufPos, remainSize - sizeof(RedoLogRecordT), data, size);
        DB_ASSERT(err == EOK);
        redoCtx->bufPos += size;
    }
    group->size = redoCtx->bufPos;
    redoCtx->record = record;
}

void RedoLogAppendData(RedoRunCtxT *redoCtx, const uint8_t *data, uint32_t size)
{
    DB_POINTER2(redoCtx, data);

    uint32_t remainSize = redoCtx->bufSize - redoCtx->bufPos;
    if (remainSize < size) {
        SE_LAST_ERROR(INT_ERR_REDO_BUFFER_NOT_ENOUGH,
            "RedoCtxBuf, remain:%" PRIu32 " request:%" PRIu32 " bufSize:%" PRIu32 " bufPos:%" PRIu32 "", remainSize,
            size, redoCtx->bufSize, redoCtx->bufPos);
        DB_ASSERT(false);
    }
    RedoLogGroupT *group = (RedoLogGroupT *)(void *)redoCtx->buf;
    RedoLogRecordT *record = (RedoLogRecordT *)redoCtx->record;
    errno_t err = memcpy_s(redoCtx->buf + redoCtx->bufPos, remainSize, data, size);
    DB_ASSERT(err == EOK);
    record->size += size;
    group->size += size;
    redoCtx->bufPos += size;
}

inline static void SetPartBufSize(uint8_t *addr, uint32_t size)
{
    RedoLogPartT *part = (RedoLogPartT *)(addr - sizeof(RedoLogPartT));
    part->size = size;
}

inline static void SetBatchCurLsn(RedoBufT *redoBuf, uint64_t curLsn)
{
    RedoLogBatchT *head = (RedoLogBatchT *)(redoBuf->bufAddr + redoBuf->wid * redoBuf->redoPubBuf->flushBufSize);
    head->lsn = curLsn;
}

void PrintPublicBufStatus(RedoMgrT *redoMgr, uint8_t *addr, const char *prefix)
{
#ifndef NDEBUG
    if (DbLogCanWrite(DB_LOG_LVL_DBG)) {
        RedoLogBatchT *head0 = (RedoLogBatchT *)addr;
        RedoLogBatchT *head1 = (RedoLogBatchT *)(addr + head0->size);
        uint8_t *buf0 = addr + sizeof(RedoLogBatchT);
        uint8_t *buf1 = addr + head0->size + sizeof(RedoLogBatchT);
        uint32_t partCount = redoMgr->redoBuf.partCount;
        uint32_t partSize = GetRedoBufPart(&redoMgr->redoBuf, 0, 0)->size + (uint32_t)sizeof(RedoLogPartT);
        DB_LOG_DEBUG("begin-stat: %s; partSize:%" PRIu32 "\n", prefix, partSize);
        DB_LOG_DEBUG("batch0Size:%" PRIu32 ", lsn:%" PRIu64 "; batch1Size:%" PRIu32 ", lsn:%" PRIu64 "\n", head0->size,
            head0->lsn, head1->size, head1->lsn);
        for (uint32_t i = 0; i < partCount; i++) {
            RedoLogPartT *part0 = (RedoLogPartT *)(buf0 + partSize * i);
            RedoLogPartT *part1 = (RedoLogPartT *)(buf1 + partSize * i);
            if (part0->size != 0 || part1->size != 0) {
                DB_LOG_DEBUG(
                    "index:%" PRIu32 ", writeSize part0:%" PRIu32 ", part1:%" PRIu32 "\n", i, part0->size, part1->size);
            }
        }
        DB_LOG_DEBUG("end:%s\n", prefix);
    }
#endif
}

StatusInter CheckReservedPubBuffer(RedoMgrT *redoMgr, uint8_t *addr, uint32_t size, bool forRecovery)
{
    if (addr == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "Redo Reserved buffer, size=%" PRIu32 "", size);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    // redo公共buf会被切分成两个同样大小的交换区使用,所有需要乘以2
    if (size > MAX_REDO_PUB_BUF_SIZE * 2 || size < REDO_DEFAULT_PUB_BUF_SIZE * 2) {
        // Redo Reserved buffer is less than 32 KB or more than 32768 KB
        SE_LAST_ERROR(DATA_EXCEPTION_VALUE_OUT_OF_RANGE, "Redo buf range, size:%" PRIu32 "", size);
        return DATA_EXCEPTION_VALUE_OUT_OF_RANGE;
    }
    RedoLogBatchT *batch0 = (RedoLogBatchT *)addr;
    RedoLogBatchT *batch1 = (RedoLogBatchT *)(addr + (size / REDO_LOG_BUFF_DUAL));
    uint64_t dbCheckMagic = redoMgr->seIns->db->checkMagic;
    if (SeGetStorageStatus(redoMgr->seIns) != SE_ON_DISK_CREATE && forRecovery) {
        // 如果恢复保留内存中的redo时,才需要校验
        if (batch0->head.magicNum != dbCheckMagic || batch1->head.magicNum != dbCheckMagic) {
            // batch magicNum is unexpect
            SE_LAST_ERROR(INT_ERR_CRC_CHECK_ERROR, "MagicNum: db:%" PRIu64 ", batch0:%" PRIu64 ", batch1:%" PRIu64,
                dbCheckMagic, batch0->head.magicNum, batch1->head.magicNum);
            return INT_ERR_CRC_CHECK_ERROR;
        }
    } else {
        // 否则将其初始化;用于校验redo使用保留内存时,与数据文件是同版本的
        batch0->head.magicNum = dbCheckMagic;
        batch1->head.magicNum = dbCheckMagic;
    }
    return STATUS_OK_INTER;
}

StatusInter GetRedoPubBuffer(RedoMgrT *redoMgr, uint8_t **addr, uint32_t *bufSize)
{
    if (DbIsUseReservedBuffer()) {
        uint32_t size = 0;
        bool forRecovery = false;
        DbGetReservedBuffer(addr, &size, &forRecovery);
        StatusInter ret = CheckReservedPubBuffer(redoMgr, *addr, size, forRecovery);
        if (ret != STATUS_OK_INTER) {
            // reserved redo pub buffer is unexpect.
            SE_ERROR(ret, "reserved redo pub buffer");
            return ret;
        }
        // 实际使用的buf为保留内存的1/2
        *bufSize = size / REDO_LOG_BUFF_DUAL;
        redoMgr->useReservedMem = true;
        redoMgr->useReservedMemRecovery = forRecovery;
        // REDO pub buffer usr reserved mem success
        DB_BOOT_LOG_DBG_DEBUG("Redo pub buffer reserved-mem, bufSize:%" PRIu32 "", *bufSize);
        return STATUS_OK_INTER;
    }
    *addr = DbDynMemCtxAlloc(redoMgr->seDynMemCtx, (uint32_t)(*bufSize * REDO_LOG_BUFF_DUAL));
    if (*addr == NULL) {
        SE_ERROR(
            OUT_OF_MEMORY_INTER, "Alloc redo pub buffer, size=%" PRIu32 "", (uint32_t)(*bufSize * REDO_LOG_BUFF_DUAL));
        return OUT_OF_MEMORY_INTER;
    }
    redoMgr->useReservedMem = false;
    redoMgr->useReservedMemRecovery = false;
    return STATUS_OK_INTER;
}

void InitBatchSize(uint8_t *buf, uint32_t size)
{
    RedoLogBatchT *head = (RedoLogBatchT *)buf;
    head->size = size;
}

static void RedoDualBufDestroy(RedoBufT *redoBuf)
{
    for (uint32_t i = 0; i < REDO_LOG_BUFF_DUAL; ++i) {
        if (redoBuf->buffers[i] != NULL) {
            DbDynMemCtxFree(redoBuf->redoMgr->seDynMemCtx, redoBuf->buffers[i]);
            redoBuf->buffers[i] = NULL;
        }
    }
}

static StatusInter RedoDualBufCreate(RedoBufT *redoBuf)
{
    for (uint32_t i = 0; i < REDO_LOG_BUFF_DUAL; ++i) {
        redoBuf->buffers[i] = (RedoDualBufT *)DbDynMemCtxAlloc(redoBuf->redoMgr->seDynMemCtx, sizeof(RedoDualBufT));
        if (redoBuf->buffers[i] == NULL) {
            SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "Alloc redo dual buffer index:%" PRIu32 "", i);
            return OUT_OF_MEMORY_INTER;
        }
        (void)memset_s(redoBuf->buffers[i], sizeof(RedoDualBufT), 0, sizeof(RedoDualBufT));
    }
    return STATUS_OK_INTER;
}

static void RedoInitDualBuf(uint32_t bufSize, uint32_t partCount, uint8_t *bufAddr, RedoBufT *redoBuf)
{
    // |RedoLogBatchT|RedoLogPartT|partSize|RedoLogPartT|partSize|...|RedoLogPartT|partSize|RedoLogBatchTailT|
    uint32_t partSize = (uint32_t)(
        (((bufSize - sizeof(RedoLogBatchT)) - sizeof(RedoLogBatchTailT)) - partCount * sizeof(RedoLogPartT)) /
        partCount);
    partSize = SIZE_FLOOR_ALIGN8(partSize);
    uint8_t *bufAddr0 = bufAddr;
    uint8_t *bufAddr1 = bufAddr + bufSize;
    InitBatchSize(bufAddr0, bufSize);
    InitBatchSize(bufAddr1, bufSize);
    bufAddr0 += sizeof(RedoLogBatchT);
    bufAddr1 += sizeof(RedoLogBatchT);
    for (uint32_t i = 0; i < partCount; ++i) {
        RedoBufPartT *part0 = GetRedoBufPart(redoBuf, 0, i);
        RedoBufPartT *part1 = GetRedoBufPart(redoBuf, 1, i);
        DbSpinInit(&part0->lock);
        DbSpinInit(&part1->lock);
        part0->size = partSize;
        part1->size = partSize;
        part0->addr = bufAddr0 + i * (partSize + sizeof(RedoLogPartT)) + sizeof(RedoLogPartT);
        part1->addr = bufAddr1 + i * (partSize + sizeof(RedoLogPartT)) + sizeof(RedoLogPartT);
    }
}

static StatusInter RedoFlushBufCreateAndInit(uint32_t flushBufSize, RedoBufT *redoBuf)
{
    RedoMgrT *redoMgr = redoBuf->redoMgr;
    redoBuf->redoPubBuf = &redoMgr->pubCtx->redoPubBuf;
    RedoPubBufT *pubBuf = redoBuf->redoPubBuf;
    DbSpinInit(&pubBuf->lock);
    pubBuf->flushBufSize = flushBufSize;
    // 如果是防篡改，flushBuffer结尾需要记一个batch摘要，一起刷到磁盘
    if (redoBuf->redoMgr->tamperProofEnable) {
        pubBuf->flushBufSize += DB_TAMPER_PROOF_DIGEST_LEN;
    }
    pubBuf->flushBufPos = 0;
    pubBuf->flushBufShm = DbDynShmemMemCtxAlloc(redoMgr->memUtils->memCtx, pubBuf->flushBufSize);
    if (!DbIsShmPtrValid(pubBuf->flushBufShm)) {
        SE_ERROR(OUT_OF_MEMORY_INTER, "Alloc redo flush buffer, size=%" PRIu32 "", pubBuf->flushBufSize);
        return OUT_OF_MEMORY_INTER;
    }
    pubBuf->lastFlushTime = DbGetMsec();
    redoBuf->flushBuf = (uint8_t *)DbDynShmemPtrToAddr(pubBuf->flushBufShm);
    if (redoBuf->flushBuf == NULL) {
        // redo get flush go wrong
        SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "Get flush buf seg-offset:%" PRIu32 "-%" PRIu32 "\n",
            pubBuf->flushBufShm.segId, pubBuf->flushBufShm.offset);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    // 异常场景统一RedoMgrInit异常分支清理
    return STATUS_OK_INTER;
}

StatusInter RedoBufInit(RedoBufT *redoBuf, uint32_t partCount, uint32_t pubBufSize, RedoMgrT *redoMgr)
{
    DB_POINTER(redoBuf);
    if (partCount == 0 || partCount > MAX_REDO_LOG_PART) {
        return INT_ERR_REDO_PART_INVALID;
    }
    (void)memset_s(redoBuf, sizeof(RedoBufT), 0, sizeof(RedoBufT));
    redoBuf->partCount = (uint16_t)partCount;
    redoBuf->redoMgr = redoMgr;
    StatusInter ret = RedoDualBufCreate(redoBuf);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }
    uint32_t bufSize = pubBufSize;
    uint8_t *bufAddr = NULL;
    ret = GetRedoPubBuffer(redoMgr, &bufAddr, &bufSize);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }
    redoBuf->bufAddr = bufAddr;
    RedoInitDualBuf(bufSize, partCount, bufAddr, redoBuf);
    ret = RedoFlushBufCreateAndInit(bufSize, redoBuf);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }
    redoBuf->fileSet = &redoMgr->redoFiles;
    return STATUS_OK_INTER;
EXIT:
    RedoDualBufDestroy(redoBuf);
    return ret;
}

void RedoBufDestroy(RedoBufT *redoBuf)
{
    DB_POINTER(redoBuf);

    if (!redoBuf->redoMgr->useReservedMem) {
        DbDynMemCtxFree(redoBuf->redoMgr->seDynMemCtx, redoBuf->bufAddr);
        redoBuf->bufAddr = NULL;
    }
    RedoDualBufDestroy(redoBuf);
    if (redoBuf->redoPubBuf != NULL) {
        DbDynShmemMemCtxFree(redoBuf->redoMgr->memUtils->memCtx, redoBuf->redoPubBuf->flushBufShm);
        redoBuf->redoPubBuf->flushBufShm = DB_INVALID_SHMPTR;
        DbDynShmemMemCtxFree(redoBuf->redoMgr->memUtils->memCtx, redoBuf->redoMgr->seIns->redoCtxShm);
        redoBuf->redoMgr->seIns->redoCtxShm = DB_INVALID_SHMPTR;
        redoBuf->redoPubBuf = NULL;
    }
}

StatusInter RedoBufWrite(RedoBufT *redoBuf, uint64_t id, uint8_t *buf, uint32_t size)
{
    DB_POINTER2(redoBuf, buf);
    if (size <= sizeof(RedoLogGroupT)) {
        SE_LAST_ERROR(NO_DATA_INTER, "RedoCtx empty buffer");
        return NO_DATA_INTER;
    }

    uint32_t index = (uint32_t)(id % redoBuf->partCount);
    RedoBufPartT *bufPart = NULL;
    uint16_t wid = 0;
    for (;;) {
        wid = redoBuf->wid;
        bufPart = GetRedoBufPart(redoBuf, wid, index);
        DB_ASSERT(size <= bufPart->size);

        DbSpinLock(&bufPart->lock);
        if (wid == redoBuf->wid && bufPart->size - bufPart->writePos >= size) {
            break;
        }
        DbSpinUnlock(&bufPart->lock);

        StatusInter ret = RedoBufFlush(redoBuf, redoBuf->fileSet->blockSize, &redoBuf->redoMgr->pubCtx->curPoint);
        if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
            SE_ERROR(ret, "Flush redo log");
            return ret;
        }
    }

    CRASHPOINT(DB_CRASH_EVENT_PERSIST_REDO, DB_CRASH_STATUS_PERSIST_REDO_WRITE_PUBLIC);
    uint64_t curLsn = DbAtomicInc64(&redoBuf->redoPubBuf->curLsn);

    RedoLogGroupT *group = (RedoLogGroupT *)(void *)buf;
    group->lsn = curLsn;
    if (redoBuf->redoMgr->useReservedMem && redoBuf->redoMgr->crcCheckEnable) {
        DbAddCheckSum((char *)buf, size, &group->checkSum);
    }
    errno_t err = memcpy_s(bufPart->addr + bufPart->writePos, bufPart->size - bufPart->writePos, buf, size);
    DB_ASSERT(err == EOK);

    bufPart->lsn = curLsn;
    bufPart->writePos += size;
    SetPartBufSize(bufPart->addr, bufPart->writePos);
    SetBatchCurLsn(redoBuf, curLsn);
#ifndef NDEBUG
    if (DbLogCanWrite(DB_LOG_LVL_DBG)) {
        // redo log write to pub buffer success.
        DB_LOG_DEBUG("trxId(%" PRIu64 ") write pubBuffer ok, lsn:%" PRIu64 " batchId:%" PRIu32 " partIndex:%" PRIu32
                     ", writeSize:%" PRIu32 ", checkSum:%" PRIu32 "",
            id, curLsn, redoBuf->wid, index, bufPart->writePos, group->checkSum);
    }
#endif
    PrintPublicBufStatus(redoBuf->redoMgr, redoBuf->bufAddr, "after RedoBufWrite to public mem");
    DbSpinUnlock(&bufPart->lock);

    return STATUS_OK_INTER;
}

void RedoBatchAssemble(RedoBufT *redoBuf, RedoDualBufT *dualBuf, uint32_t padding, RedoLogBatchT **outBatch,
    RedoLogBatchTailT **outBatchTail)
{
    DB_POINTER3(redoBuf, outBatch, outBatchTail);

    redoBuf->redoPubBuf->flushBufPos = 0;
    RedoLogBatchT *batch = (RedoLogBatchT *)(void *)redoBuf->flushBuf;
    batch->head.magicNum = REDO_MAGIC_NUMBER;
    batch->padding = padding;
    redoBuf->redoPubBuf->flushBufPos += sizeof(RedoLogBatchT);

    uint64_t maxLsn = 0;
    for (uint32_t i = 0; i < redoBuf->partCount; i++) {
        RedoBufPartT *bufPart = &dualBuf->members[i];
        DbSpinLock(&bufPart->lock);
        if (bufPart->writePos == 0) {
            DbSpinUnlock(&bufPart->lock);
            continue;
        }

        RedoLogPartT *part = (RedoLogPartT *)(void *)(redoBuf->flushBuf + redoBuf->redoPubBuf->flushBufPos);
        redoBuf->redoPubBuf->flushBufPos += sizeof(RedoLogPartT);
        part->size = sizeof(RedoLogPartT) + bufPart->writePos;
        errno_t err = memcpy_s(redoBuf->flushBuf + redoBuf->redoPubBuf->flushBufPos,
            redoBuf->redoPubBuf->flushBufSize - redoBuf->redoPubBuf->flushBufPos, bufPart->addr, bufPart->writePos);
        DB_ASSERT(err == EOK);

        redoBuf->redoPubBuf->flushBufPos += bufPart->writePos;
        bufPart->writePos = 0;
        SetPartBufSize(bufPart->addr, 0);
        maxLsn = maxLsn < bufPart->lsn ? bufPart->lsn : maxLsn;
        DbSpinUnlock(&bufPart->lock);
    }

    RedoLogBatchTailT *batchTail = (RedoLogBatchTailT *)(void *)(redoBuf->flushBuf + redoBuf->redoPubBuf->flushBufPos);
    redoBuf->redoPubBuf->flushBufPos += sizeof(RedoLogBatchTailT);
    batchTail->magicNum = REDO_MAGIC_NUMBER;
    // 如果开启了防篡改，将32位摘要放到RedoBatch后面
    if (redoBuf->redoMgr->tamperProofEnable) {
        redoBuf->redoPubBuf->flushBufPos += DB_TAMPER_PROOF_DIGEST_LEN;
    }
    batch->size = redoBuf->redoPubBuf->flushBufPos;
    batch->lsn = maxLsn;

    batch->spaceSize = (uint32_t)DB_CALC_ALIGN(redoBuf->redoPubBuf->flushBufPos, padding);
    redoBuf->redoPubBuf->flushBufPos = batch->spaceSize;
    DB_ASSERT(redoBuf->redoPubBuf->flushBufPos <= redoBuf->redoPubBuf->flushBufSize);

    *outBatch = batch;
    *outBatchTail = batchTail;
}

static StatusInter RedoVerifyBatchChecksum(RedoLogBatchT *redoBatch, const RedoMgrT *redoMgr)
{
    if (redoMgr->crcCheckEnable && !DbCheckCrc((char *)redoBatch, redoBatch->size, &redoBatch->checkSum)) {
        SE_LAST_ERROR(INT_ERR_CRC_CHECK_ERROR, "verify redo batch crc");
        return INT_ERR_CRC_CHECK_ERROR;
    }
    if (redoMgr->tamperProofEnable) {
        DbDigest32T genDigest = {0};
        StatusInter ret = SeDigest32Generate(
            (uint8_t *)redoBatch, redoBatch->size - sizeof(DbDigest32T), &genDigest, DB_TAMPER_PROOF_DIGEST_LEN);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "generate redo batch check digest");
            return ret;
        }
        DbDigest32T *digestInFile = (DbDigest32T *)((uint8_t *)redoBatch + redoBatch->size - sizeof(DbDigest32T));
        if (!SeIsTamperProofDigestSame(&genDigest, digestInFile)) {
            SE_LAST_ERROR(DATA_CORRUPTION_INTER, "verify redo batch check digest");
            SeLogErrorDigest(&genDigest, digestInFile);
            return DATA_CORRUPTION_INTER;
        }
    }
    return STATUS_OK_INTER;
}

StatusInter RedoBufFlush(RedoBufT *redoBuf, uint32_t padding, RedoPointT *point)
{
    DB_POINTER(redoBuf);
    DbSpinLock(&redoBuf->redoPubBuf->lock);
    if (!RedoNeedFlush(redoBuf)) {
        DbSpinUnlock(&redoBuf->redoPubBuf->lock);
        return STATUS_OK_INTER;
    }
    if (SECUREC_UNLIKELY(SeGetStorageStatus(redoBuf->redoMgr->seIns) == SE_ON_DISK_EMRGNCY)) {
        DbSpinUnlock(&redoBuf->redoPubBuf->lock);
        SE_LAST_ERROR(DATABASE_NOT_AVAILABLE_INTER, "Flush redo log, storage emergency or swapping dir");
        return DATABASE_NOT_AVAILABLE_INTER;
    }
    RedoDualBufT *dualBuf = redoBuf->buffers[redoBuf->wid];
    DB_ASSERT(!dualBuf->isFlushing);
    dualBuf->isFlushing = true;
    RedoSwitchBuf(redoBuf);
    RedoLogBatchT *batch = NULL;
    RedoLogBatchTailT *batchTail = NULL;

    CRASHPOINT(DB_CRASH_EVENT_PERSIST_REDO, DB_CRASH_STATUS_PERSIST_REDO_FLUSH);
    StatusInter ret = STATUS_OK_INTER;
    RedoBatchAssemble(redoBuf, dualBuf, padding, &batch, &batchTail);
    DB_ASSERT(batch != NULL && batchTail != NULL);  // for codecheck
    ret = RedoLogFileFlushInit(redoBuf->fileSet, batch->size, &batch->head.point);
    if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
        SE_ERROR(ret, "Init redo log file, batchSize:%" PRIu32, batch->size);
        goto TAG_UNLOCK;
    }
    batch->head.point.batchId = redoBuf->redoPubBuf->curBatchId;
    redoBuf->redoPubBuf->curBatchId++;
    batchTail->point = batch->head.point;
    ret = RedoCalcBatchChecksum(batch, redoBuf->redoMgr);
    if (ret != STATUS_OK_INTER) {
        goto TAG_UNLOCK;
    }
    ret = RedoLogFileFlush(redoBuf->fileSet, redoBuf->flushBuf, redoBuf->redoPubBuf->flushBufPos);
    if (SECUREC_LIKELY(ret == STATUS_OK_INTER)) {
        // redoBatch刷盘成功后，将redoMgr->curPoint更新到这个batch在文件中的结尾点
        *point = batch->head.point;
        // 这里不能直接用spaceSize来算，因为redo单文件时，这个batch可能被分成了两份，blockId还需要加一个文件头
        point->blockId = redoBuf->fileSet->files[redoBuf->fileSet->fileSetPub->curFile].info->head.writePos / padding;
        point->batchId++;
    }
TAG_UNLOCK:
    dualBuf->isFlushing = false;
    DbSpinUnlock(&redoBuf->redoPubBuf->lock);
    if (SECUREC_UNLIKELY(ret == FILE_OPERATE_FAILED_INTER || ret == DISK_NO_SPACE_ERROR_INTER)) {
        SeSetStorageEmergency(redoBuf->redoMgr->seIns, "flush redo emergency");
    }
    PrintPublicBufStatus(redoBuf->redoMgr, redoBuf->bufAddr, "after RedoBufFlush to public mem");
    return ret;
}

inline static uint32_t AlignPowOf2(uint32_t val)
{
    uint64_t ans = 1;
    while (ans < (uint64_t)val) {
        ans = ans << 1;
    }
    return (uint32_t)ans;
}

static StatusInter ReallocBufferAndMoveData(DbMemCtxT *memCtx, DbBufT *buf, uint32_t needSize, uint32_t currDataSize)
{
    uint32_t size = AlignPowOf2(needSize);
    DB_ASSERT(size >= needSize);
    uint8_t *newBuf = DbDynMemCtxAlloc(memCtx, size);
    if (newBuf == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "Realloc buf-mem. size=%" PRIu32 "", size);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    (void)memcpy_s(newBuf, currDataSize, buf->buf, currDataSize);
    DbDynMemCtxFree(memCtx, buf->buf);
    buf->buf = newBuf;
    buf->len = size;
    return STATUS_OK_INTER;
}

StatusInter RedoLogLoadBatch(RedoLogFileSetT *fileSet, RedoLogFileT *file, DbBufT *buf, uint64_t readPos)
{
    uint32_t batchHeadSize = sizeof(RedoLogBatchT);
    DB_ASSERT(buf->len >= batchHeadSize);
    // load batch header
    StatusInter ret = RedoLogLoad(fileSet, file, buf->buf, batchHeadSize, readPos);
    if (ret != STATUS_OK_INTER) {
        // Unable to load redo log of batch header
        SE_ERROR(ret, "Load batch header, readPos=%" PRIu64 ", size=%" PRIu32, readPos, batchHeadSize);
        return ret;
    }

    RedoLogBatchT *batchT = (RedoLogBatchT *)(void *)buf->buf;
    if (batchT->head.magicNum != REDO_MAGIC_NUMBER) {
        SE_LAST_ERROR(DATA_CORRUPTION_INTER, "Batch magic in file is unexpected, readPos=%" PRIu64 "", readPos);
        return DATA_CORRUPTION_INTER;
    }
    // 这里读上来的RedoLogBatchT还没有crc校验，size字段可能已经被篡改或破坏了
    if (batchT->size > MAX_REDO_PUB_BUF_SIZE || batchT->size <= batchHeadSize) {
        // Batch size read from redofile is unexpected
        SE_LAST_ERROR(DATA_CORRUPTION_INTER, "CRC, Size: batch=%" PRIu32 ", buffer=%" PRIu32 ", batchHead=%" PRIu32,
            batchT->size, MAX_REDO_PUB_BUF_SIZE, batchHeadSize);
        return DATA_CORRUPTION_INTER;
    }

    if (batchT->size > buf->len) {  // 可能出现的场景：在修改了redoPubBufSize（改小）后启动DB
        ret = ReallocBufferAndMoveData(fileSet->redoMgr->seDynMemCtx, buf, batchT->size, batchHeadSize);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        batchT = (RedoLogBatchT *)(void *)buf->buf;
    }

    // load redo log of batch data
    ret = RedoLogLoad(fileSet, file, buf->buf + batchHeadSize, batchT->size - batchHeadSize, readPos + batchHeadSize);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Load batch data. readPos=%" PRIu64 " size=%" PRIu32, readPos + batchHeadSize,
            batchT->size - batchHeadSize);
    }
    return ret;
}

static RedoLogFileT *GetCurRedoFile(RedoLogFileSetT *fileSet, RedoPointT *cursorPoint)
{
    uint32_t fileId = RedoFsnToFileId(cursorPoint->fsn, fileSet->fileCount);
    return &fileSet->files[fileId];
}

static bool RedoFileScanIsEnd(const RedoLogFileT *curFile, const RedoPointT *cursorPoint)
{
    if (curFile->info->status != REDO_LOG_FILE_ACTIVE && curFile->info->status != REDO_LOG_FILE_CURRENT) {
        return true;
    }

    // 单文件：fsn固定为1，cursor为2时，表明扫描结束了
    // 多文件：如果文件fsn小于当前fsn，说明是上一轮的文件
    if (curFile->info->head.fsn < cursorPoint->fsn) {
        return true;
    }

    uint64_t readPos = cursorPoint->blockId * curFile->info->head.blockSize;
    if (readPos >= curFile->info->head.writePos) {
        return true;
    }

    return false;
}

static void RedoGetNextCursorPoint(RedoLogFileSetT *fileSet, uint32_t lastBatchSize, RedoPointT *cursorPoint)
{
    RedoLogFileT *file = GetCurRedoFile(fileSet, cursorPoint);
    uint64_t readPos = cursorPoint->blockId * file->info->head.blockSize;
    // 第一个 batch 时，lastBatchSize 为 0，计算一下 truncatePoint（readPos）是否是文件最后一个batch，是否需要切换文件
    if (readPos + lastBatchSize >= file->info->head.writePos) {
        // 单文件：fsn 增长为 2，表示扫描结束，在 RedoFileScanIsEnd 时返回true
        // 多文件：表示需要切换到下一个redo文件了
        cursorPoint->fsn++;
        cursorPoint->blockId = REDO_INITIAL_BLOCK;
        // 如果是第一个 batch，当前 batchId 指向的就是下一个 batch，不用加加
        if (lastBatchSize != 0) {
            cursorPoint->batchId++;
        }
        return;
    }
    // 如果 lastBatchSize 为 0，表示这是第一个 batch，且不用切换文件，直接返回即可
    if (lastBatchSize == 0) {
        return;
    }
    // 同一个redo文件，切换到下一个 batch 点
    uint32_t batchBlockNum = lastBatchSize / file->info->head.blockSize;
    if (fileSet->redoMgr->isSingleRedoFile) {
        // 如果是单文件，blockId需要加上文件头所在的block
        uint32_t fileBlockNum = file->info->size / file->info->head.blockSize;
        if (cursorPoint->blockId % fileBlockNum + batchBlockNum >= fileBlockNum) {
            cursorPoint->blockId++;
        }
    }
    cursorPoint->blockId += batchBlockNum;
    cursorPoint->batchId++;
}

StatusInter RedoLogMoveNextAndLoad(
    RedoLogFileSetT *fileSet, DbBufT *buf, uint32_t *lastBatchSize, RedoPointT *cursorPoint)
{
    DB_POINTER4(fileSet, buf, lastBatchSize, cursorPoint);

    RedoGetNextCursorPoint(fileSet, *lastBatchSize, cursorPoint);

    RedoLogFileT *file = GetCurRedoFile(fileSet, cursorPoint);
    if (RedoFileScanIsEnd(file, cursorPoint)) {
        return INT_ERR_REDO_SCAN_END;
    }

    uint64_t readPos = cursorPoint->blockId * file->info->head.blockSize;
    StatusInter ret = RedoLogLoadBatch(fileSet, file, buf, readPos);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Load redo log, readPos=%" PRIu64 "", readPos);
        return ret;
    }

    // 校验 redoBatch
    RedoLogBatchT *batch = (RedoLogBatchT *)(void *)buf->buf;
    ret = RedoVerifyBatchChecksum(batch, fileSet->redoMgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(
            ret, "check batch digest, fsn=%" PRIu32 ", blockId=%" PRIu64 "", cursorPoint->fsn, cursorPoint->blockId);
        return ret;
    }
    if (RedoPointCmp(&batch->head.point, cursorPoint) != 0) {
        SE_ERROR(INT_ERR_REDO_BATCH_INVALID, "Point unmatch " REDO_POINT_LOG_FMT ", " REDO_POINT_LOG_FMT "",
            REDO_POINT(&batch->head.point), REDO_POINT(cursorPoint));
        return INT_ERR_REDO_BATCH_INVALID;
    }

    RedoLogBatchTailT *batchTail =
        (RedoLogBatchTailT *)(void *)(buf->buf + RedoGetBatchTailOffset(fileSet->redoMgr, batch));
    if (!RedoVerifyBatch(batch, batchTail)) {
        SE_ERROR(INT_ERR_REDO_BATCH_INVALID, "Verify batch, fsn=%" PRIu32 ", blockId=%" PRIu64 "", cursorPoint->fsn,
            cursorPoint->blockId);
        return INT_ERR_REDO_BATCH_INVALID;
    }
    *lastBatchSize = batch->spaceSize;

    return STATUS_OK_INTER;
}
