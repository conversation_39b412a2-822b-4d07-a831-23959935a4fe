/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Create and manage temporary files
 * Create: 2024-1-2
 */
#include <stdio.h>
#include <string.h>
#include "db_file.h"
#include "db_mem_context.h"
#include "db_dyn_load.h"
#include "adpt_thread.h"
#include "se_temp_file_inner.h"

#define TMPFILE_FD_CLOSED DB_INVALID_FD
#define MAX_SAFE_FILE_DESCRIPTORS 32
static DB_THREAD_LOCAL uint32_t g_tempFileCounter;

/*--------------------
 * The Least Recently Used ring is a doubly linked list that begins and
 * ends on element zero.  Element zero is special -- it doesn't represent
 * a file and its "fd" field always == TMPFILE_FD_CLOSED.  Element zero is just an
 * anchor that shows us the beginning/end of the ring.
 * Only VFD elements that are currently really open (have an FD assigned) are
 * in the Lru ring.  Elements that are "virtually" open can be recognized
 * by having a non-null fileName field.
 *
 * example
 *
 *	   /--less----\				   /---------\
 *	   v		   \			  v			  \
 *	 #0 --more---> LeastRecentlyUsed --more-\ \
 *	  ^\									| |
 *	   \\less--> MostRecentlyUsedFile	<---/ |
 *		\more---/					 \--less--/
 *--------------------
 */

Status GetTempFileDirPath(char *path, DbCfgEmItemIdE id, uint32_t length)
{
    DB_POINTER(path);
    DbCfgValueT cfgValue;
    Status ret = DbCfgGet(DbGetCfgHandle(NULL), id, &cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get temp file directory");
        return ret;
    }
    errno_t err = memcpy_s(path, length, cfgValue.str, DB_CFG_PARAM_MAX_STRING);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "copy temp file path");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return ret;
}

static bool IsFileValid(TempFileMgrT *tempFileMgr, uint32_t vfdIndex)
{
    DB_POINTER(tempFileMgr);
    if (((vfdIndex) > 0) && ((vfdIndex) < tempFileMgr->sizeVfdCache) &&
        (tempFileMgr->vfdCache[vfdIndex].fileName != NULL)) {
        return true;
    }
    return false;
}

static bool IsFileClose(TempFileMgrT *tempFileMgr, uint32_t vfdIndex)
{
    DB_POINTER(tempFileMgr);
    DB_ASSERT((vfdIndex > 0) && (vfdIndex < tempFileMgr->sizeVfdCache));
    if (tempFileMgr->vfdCache[vfdIndex].fd == TMPFILE_FD_CLOSED) {
        return true;
    }
    return false;
}

static void DeleteVfd(TempFileMgrT *tempFileMgr, uint32_t vfdIndex)
{
    DB_POINTER(tempFileMgr);
    DB_ASSERT((vfdIndex > 0) && (vfdIndex < tempFileMgr->sizeVfdCache));
    VfdT *vfdP = &tempFileMgr->vfdCache[vfdIndex];
    tempFileMgr->vfdCache[vfdP->lruLessRecently].lruMoreRecently = vfdP->lruMoreRecently;
    tempFileMgr->vfdCache[vfdP->lruMoreRecently].lruLessRecently = vfdP->lruLessRecently;
}

static void LruDelete(TempFileMgrT *tempFileMgr, uint32_t vfdIndex)
{
    DB_POINTER(tempFileMgr);
    DB_ASSERT((vfdIndex > 0) && (vfdIndex < tempFileMgr->sizeVfdCache));
    VfdT *vfdP = &tempFileMgr->vfdCache[vfdIndex];
    DbCloseFile(vfdP->fd);
    vfdP->fd = TMPFILE_FD_CLOSED;
    --tempFileMgr->nfile;
    DeleteVfd(tempFileMgr, vfdIndex);
}

static void InsertVfd(TempFileMgrT *tempFileMgr, uint32_t vfdIndex)
{
    DB_POINTER(tempFileMgr);
    DB_ASSERT((vfdIndex > 0) && (vfdIndex < tempFileMgr->sizeVfdCache));
    VfdT *vfdP = &tempFileMgr->vfdCache[vfdIndex];
    vfdP->lruMoreRecently = 0;
    vfdP->lruLessRecently = tempFileMgr->vfdCache[0].lruLessRecently;
    tempFileMgr->vfdCache[0].lruLessRecently = vfdIndex;
    tempFileMgr->vfdCache[vfdP->lruLessRecently].lruMoreRecently = vfdIndex;
}

static void ReleaseLruFiles(TempFileMgrT *tempFileMgr)
{
    DB_POINTER(tempFileMgr);
    while (tempFileMgr->nfile >= tempFileMgr->maxFileNum) {
        LruDelete(tempFileMgr, tempFileMgr->vfdCache[0].lruMoreRecently);
    }
}

static Status LruInsert(TempFileMgrT *tempFileMgr, uint32_t vfdIndex)
{
    DB_POINTER(tempFileMgr);
    DB_ASSERT((vfdIndex > 0) && (vfdIndex < tempFileMgr->sizeVfdCache));
    int32_t fd = DB_INVALID_FD;
    VfdT *vfdP = &tempFileMgr->vfdCache[vfdIndex];
    Status ret = GMERR_OK;
    bool isFileClose = IsFileClose(tempFileMgr, vfdIndex);
    if (isFileClose) {
        ReleaseLruFiles(tempFileMgr);
        ret = DbOpenFile(vfdP->fileName, vfdP->fileFlags, vfdP->fileMode, &fd);
        if (ret != GMERR_OK) {
            TempFileClose(tempFileMgr, vfdIndex, false);
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "open file");
            return ret;
        }
        vfdP->fd = fd;
        if (vfdP->fd < 0) {
            TempFileClose(tempFileMgr, vfdIndex, false);
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "negative file descriptor returned");
            return GMERR_INTERNAL_ERROR;
        } else {
            ++tempFileMgr->nfile;
        }
    }
    InsertVfd(tempFileMgr, vfdIndex);
    return ret;
}

static Status AllocateVfdMemCtx(TempFileMgrT *tempFileMgr, size_t newCacheSize)
{
    DB_POINTER(tempFileMgr);
    size_t tempVfdCacheLen = sizeof(VfdT) * tempFileMgr->sizeVfdCache;
    VfdT *tempVfdCache = DbDynMemCtxAlloc(tempFileMgr->tempFileMemCtx, tempVfdCacheLen);
    if (tempVfdCache == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "create cache when init temp vfd");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(tempVfdCache, tempVfdCacheLen, 0, tempVfdCacheLen);
    errno_t err = memcpy_s(tempVfdCache, tempVfdCacheLen, tempFileMgr->vfdCache, tempVfdCacheLen);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "copy temp vfd cache");
        DbDynMemCtxFree(tempFileMgr->tempFileMemCtx, tempVfdCache);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    DbDynMemCtxFree(tempFileMgr->tempFileMemCtx, tempFileMgr->vfdCache);
    size_t vfdCacheLen = sizeof(VfdT) * newCacheSize;
    tempFileMgr->vfdCache = DbDynMemCtxAlloc(tempFileMgr->tempFileMemCtx, vfdCacheLen);
    if (tempFileMgr->vfdCache == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "create vfd cache");
        DbDynMemCtxFree(tempFileMgr->tempFileMemCtx, tempVfdCache);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(tempFileMgr->vfdCache, vfdCacheLen, 0, vfdCacheLen);
    err = memcpy_s(tempFileMgr->vfdCache, tempVfdCacheLen, tempVfdCache, tempVfdCacheLen);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "copy vfd cache");
        DbDynMemCtxFree(tempFileMgr->tempFileMemCtx, tempVfdCache);
        DbDynMemCtxFree(tempFileMgr->tempFileMemCtx, tempFileMgr->vfdCache);
        tempFileMgr->vfdCache = NULL;
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    DbDynMemCtxFree(tempFileMgr->tempFileMemCtx, tempVfdCache);
    return GMERR_OK;
}

static Status AllocateVfd(TempFileMgrT *tempFileMgr, uint32_t *vfdIndex)
{
    DB_POINTER2(tempFileMgr, vfdIndex);
    DB_ASSERT(tempFileMgr->sizeVfdCache > 0);
    if (tempFileMgr->vfdCache[0].nextFree == 0) {
        uint32_t newCacheSize = tempFileMgr->sizeVfdCache * 2;
        Status ret = AllocateVfdMemCtx(tempFileMgr, newCacheSize);
        if (ret != GMERR_OK) {
            return ret;
        }
        for (uint32_t i = tempFileMgr->sizeVfdCache; i < newCacheSize; i++) {
            (void)memset_s((char *)&(tempFileMgr->vfdCache[i]), sizeof(VfdT), 0, sizeof(VfdT));
            tempFileMgr->vfdCache[i].nextFree = i + 1;
            tempFileMgr->vfdCache[i].fd = TMPFILE_FD_CLOSED;
        }
        tempFileMgr->vfdCache[newCacheSize - 1].nextFree = 0;
        tempFileMgr->vfdCache[0].nextFree = tempFileMgr->sizeVfdCache;
        tempFileMgr->sizeVfdCache = newCacheSize;
    }
    *vfdIndex = tempFileMgr->vfdCache[0].nextFree;
    tempFileMgr->vfdCache[0].nextFree = tempFileMgr->vfdCache[*vfdIndex].nextFree;
    return GMERR_OK;
}

static void FreeVfd(TempFileMgrT *tempFileMgr, uint32_t vfdIndex)
{
    DB_POINTER(tempFileMgr);
    DB_ASSERT((vfdIndex > 0) && (vfdIndex < tempFileMgr->sizeVfdCache));
    VfdT *vfdP = &tempFileMgr->vfdCache[vfdIndex];
    if (vfdP->fileName != NULL) {
        DbDynMemCtxFree(tempFileMgr->tempFileMemCtx, vfdP->fileName);
        vfdP->fileName = NULL;
    }
    vfdP->nextFree = tempFileMgr->vfdCache[0].nextFree;
    tempFileMgr->vfdCache[0].nextFree = vfdIndex;
}

static void AbnormalTempFileClose(TempFileMgrT *tempFileMgr, uint32_t vfdIndex, const char *fileName)
{
    DB_POINTER2(tempFileMgr, fileName);
    LruDelete(tempFileMgr, vfdIndex);
    Status ret = DbRemoveFile(fileName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "delete temp file");
        return;
    }
    FreeVfd(tempFileMgr, vfdIndex);
}

// 临时文件使用lru管理文件描述符，当释放（ReleaseLruFiles）和最后清除文件（TempFileClose）会关闭文件描述符
// 因为读取和写入的数据量较大，临时文件描述符一直保存在lru，读写时直接使用即可，避免频繁的打开和关闭文件
static Status PathNameOpenFile(
    TempFileMgrT *tempFileMgr, const char *fileName, int32_t fileFlags, uint32_t fileMode, uint32_t *fileIndex)
{
    DB_POINTER2(fileName, fileIndex);
    int32_t fd = DB_INVALID_FD;
    uint32_t vfdIndex;
    Status ret = AllocateVfd(tempFileMgr, &vfdIndex);
    if (ret != GMERR_OK) {
        return ret;
    }
    VfdT *vfdP = &tempFileMgr->vfdCache[vfdIndex];
    ReleaseLruFiles(tempFileMgr);
    ret = DbOpenFile(fileName, fileFlags, fileMode, &fd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "create the temporary file");
        return GMERR_DATA_EXCEPTION;
    }
    vfdP->fd = fd;
    if (vfdP->fd < 0) {
        FreeVfd(tempFileMgr, vfdIndex);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "create the temporary file");
        return GMERR_DATA_EXCEPTION;
    }
    ++tempFileMgr->nfile;
    InsertVfd(tempFileMgr, vfdIndex);
    size_t fileNameLen = PATH_MAX * sizeof(char);
    vfdP->fileName = (char *)DbDynMemCtxAlloc(tempFileMgr->tempFileMemCtx, fileNameLen);
    if (vfdP->fileName == NULL) {
        AbnormalTempFileClose(tempFileMgr, vfdIndex, fileName);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "alloc vfdP file name");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(vfdP->fileName, fileNameLen, 0, fileNameLen);
    errno_t err = memcpy_s(vfdP->fileName, fileNameLen, fileName, fileNameLen);
    if (err != EOK) {
        AbnormalTempFileClose(tempFileMgr, vfdIndex, fileName);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "copy file name");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    vfdP->fileFlags = fileFlags & ~(O_CREAT | O_TRUNC | O_EXCL);
    vfdP->fileMode = fileMode;
    *fileIndex = vfdIndex;
    return ret;
}

static Status FileAccess(TempFileMgrT *tempFileMgr, uint32_t vfdIndex)
{
    DB_POINTER(tempFileMgr);
    DB_ASSERT((vfdIndex > 0) && (vfdIndex < tempFileMgr->sizeVfdCache));
    bool isFileValid = IsFileValid(tempFileMgr, vfdIndex);
    if (!isFileValid) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NO_DATA, "file is not valid");
        return GMERR_NO_DATA;
    }
    bool isFileClose = IsFileClose(tempFileMgr, vfdIndex);
    if (isFileClose) {
        return LruInsert(tempFileMgr, vfdIndex);
    } else if (tempFileMgr->vfdCache[0].lruLessRecently != vfdIndex) {
        DeleteVfd(tempFileMgr, vfdIndex);
        InsertVfd(tempFileMgr, vfdIndex);
    }
    return GMERR_OK;
}

static Status GetTempFileDir(char *fileNamePath, char *fileDirPath, uint32_t length)
{
    DB_POINTER2(fileNamePath, fileDirPath);
    char directory[PATH_MAX] = {0};
    char *fileName = strrchr(fileNamePath, '/');
    if (fileName == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "File name is null");
        return GMERR_INTERNAL_ERROR;
    }
    size_t copyLength = strlen(fileNamePath) - strlen(fileName);
    if (length < copyLength || length > PATH_MAX) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "The file path length unexpect");
        return GMERR_DATA_EXCEPTION;
    }
    errno_t ret = strncpy_s(directory, length, fileNamePath, copyLength);
    if (ret != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "copy file path");
        return GMERR_DATA_EXCEPTION;
    }
    return DbAdptRealPath(directory, fileDirPath);
}

static void DeleteTempFileDir(char *fileDirPath)
{
    DB_POINTER(fileDirPath);
    DbDIRT *dir = NULL;
    Status ret = DbOpenDir(fileDirPath, &dir);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "open temp file path: %s", fileDirPath);
        return;
    }
    char subDirPath[PATH_MAX] = {0};
    int32_t err = snprintf_s(subDirPath, sizeof(subDirPath), sizeof(subDirPath) - 1, "%s", fileDirPath);
    if (err < 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "get subDirPath.");
        DbCloseDir(dir);
        return;
    }
    const uint32_t len = (uint32_t)strlen(subDirPath);
    bool isFileExist = false;
    while ((ret = DbReadDir(dir, subDirPath + len, PATH_MAX - len)) == GMERR_OK) {
        if (strcmp(subDirPath + len, ".") == 0 || strcmp(subDirPath + len, "..") == 0) {
            continue;
        }
        isFileExist = DbFileExist(subDirPath);
        if (isFileExist) {
            break;
        }
    }
    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "read file path. fileDirPath: %s", fileDirPath);
        DbCloseDir(dir);
        return;
    }
    if (!isFileExist) {
        ret = DbRemoveFile(fileDirPath);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "delete temp file dir");
        }
    }
    DbCloseDir(dir);
}

static Status TempFileMakeDirectory(TempFileMgrT *tempFileMgr)
{
    DB_POINTER2(tempFileMgr, tempFileMgr->filePath);
    char fileDirPath[PATH_MAX];
    Status ret = GetTempFileDir(tempFileMgr->filePath, fileDirPath, PATH_MAX);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get temp file dir");
        return ret;
    }
    return DbMakeDirWithGRPRXPermission(fileDirPath);
}

Status TempFileOpen(TempFileMgrT *tempFileMgr, uint32_t *fileIndex)
{
    DB_POINTER2(tempFileMgr, fileIndex);
    char tempFileDirPath[PATH_MAX];
    char tempFileThreadDirPath[PATH_MAX];
    char tempFilePath[PATH_MAX];
    Status ret = GMERR_OK;
    if (tempFileMgr->filePath == NULL) {
        ret = GetTempFileDirPath(tempFileDirPath, DB_CFG_TEMP_FILE_DIR, PATH_MAX);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "get temp file path");
            return ret;
        }
        int32_t err = snprintf_s(tempFileThreadDirPath, sizeof(tempFileThreadDirPath),
            sizeof(tempFileThreadDirPath) - 1, "%s/%llu", tempFileDirPath, tempFileMgr->threadId);
        if (err < 0) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Generate temp file path");
            return GMERR_FIELD_OVERFLOW;
        }
        err = snprintf_s(tempFilePath, sizeof(tempFilePath), sizeof(tempFilePath) - 1, "%s/%u", tempFileThreadDirPath,
            g_tempFileCounter);
        if (err < 0) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Generate temp file path");
            return GMERR_FIELD_OVERFLOW;
        }
        g_tempFileCounter++;
    } else {  // large result
        int32_t err =
            snprintf_s(tempFilePath, sizeof(tempFilePath), sizeof(tempFilePath) - 1, "%s", tempFileMgr->filePath);
        if (err < 0) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Generate temp file path");
            return GMERR_FIELD_OVERFLOW;
        }
    }
    // DbMakeDirectory内部会判断目录是否已创建
    if (tempFileMgr->filePath == NULL) {
        ret = DbMakeDirWithGRPRXPermission(tempFileThreadDirPath);
    } else {
        ret = TempFileMakeDirectory(tempFileMgr);
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "create temp file dir");
        return ret;
    }
    return PathNameOpenFile(
        tempFileMgr, tempFilePath, O_RDWR | O_CREAT | O_TRUNC | O_BINARY, (uint32_t)PERM_GRPR, fileIndex);
}

Status TempFileLoad(TempFileMgrT *tempFileMgr, uint32_t *fileIndex)
{
    DB_POINTER2(tempFileMgr, fileIndex);
    char tempFilePath[PATH_MAX];
    if (tempFileMgr->filePath == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "find target temp file");
        return GMERR_INTERNAL_ERROR;
    } else {
        int32_t err =
            snprintf_s(tempFilePath, sizeof(tempFilePath), sizeof(tempFilePath) - 1, "%s", tempFileMgr->filePath);
        if (err < 0) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Generate temp file path");
            return GMERR_FIELD_OVERFLOW;
        }
    }
    return PathNameOpenFile(tempFileMgr, tempFilePath, O_RDONLY | O_BINARY, (uint32_t)PERM_GRPR, fileIndex);
}

Status TempFileWrite(TempFileMgrT *tempFileMgr, char *buffer, uint32_t amount, int64_t offset, uint32_t *write)
{
    DB_POINTER3(tempFileMgr, buffer, write);
    uint32_t vfdIndex = tempFileMgr->vfdIndex;
    Status ret = FileAccess(tempFileMgr, vfdIndex);
    if (ret != GMERR_OK) {
        return ret;
    }
    VfdT *vfdP = &tempFileMgr->vfdCache[vfdIndex];
    ret = DbPwriteFile(vfdP->fd, buffer, amount, offset);
    if (ret != GMERR_OK) {
        return ret;
    }
    *write = amount;
    return ret;
}

Status TempFileRead(TempFileMgrT *tempFileMgr, char *buffer, uint32_t amount, int64_t offset, uint32_t *read)
{
    DB_POINTER3(tempFileMgr, buffer, read);
    uint32_t vfdIndex = tempFileMgr->vfdIndex;
    Status ret = FileAccess(tempFileMgr, vfdIndex);
    if (ret != GMERR_OK) {
        return ret;
    }
    VfdT *vfdP = &tempFileMgr->vfdCache[vfdIndex];
    uint32_t readSize = 0;
    ret = DbPreadFile(vfdP->fd, buffer, amount, offset, &readSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    *read = readSize;
    return ret;
}

Status TempFileManageInit(TempFileMgrT **tempFileMgr, DbMemCtxT *memCtx)
{
    DB_POINTER2(tempFileMgr, memCtx);
    size_t tfmLen = sizeof(TempFileMgrT);
    TempFileMgrT *tfm = DbDynMemCtxAlloc(memCtx, tfmLen);
    if (tfm == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "alloc temp file mgr");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(tfm, tfmLen, 0, tfmLen);
    tfm->tempFileMemCtx = memCtx;
    size_t vfdCacheLen = sizeof(VfdT);
    tfm->vfdCache = DbDynMemCtxAlloc(tfm->tempFileMemCtx, vfdCacheLen);
    if (tfm->vfdCache == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "alloc vfd cache");
        DbDynMemCtxFree(tfm->tempFileMemCtx, tfm);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(tfm->vfdCache, vfdCacheLen, 0, vfdCacheLen);
    tfm->vfdCache->fd = TMPFILE_FD_CLOSED;
    tfm->sizeVfdCache = 1;
    tfm->nfile = 0;
    tfm->maxFileNum = MAX_SAFE_FILE_DESCRIPTORS;
    tfm->threadId = DbThreadGetSelfId();
    tfm->filePath = NULL;
    *tempFileMgr = tfm;
    return GMERR_OK;
}

static Status ConcatStrNoTruncate(char *strBuf, uint32_t maxLen, const char *str)
{
    if (strlen(strBuf) + strlen(str) >= maxLen) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "String exceeds maximum: %" PRIu32 ".", maxLen);
        return GMERR_DATA_EXCEPTION;
    }
    errno_t err = strcat_s(strBuf, maxLen, str);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "concat strings.");
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

void TempFileClose(TempFileMgrT *tempFileMgr, uint32_t fileIndex, bool isDeleteFileDir)
{
    DB_POINTER(tempFileMgr);
    bool isFileClose = IsFileClose(tempFileMgr, fileIndex);
    if (!isFileClose) {
        LruDelete(tempFileMgr, fileIndex);
    }
    VfdT *vfdP = &tempFileMgr->vfdCache[fileIndex];
    if (DbFileExist(vfdP->fileName)) {
        Status ret = DbRemoveFile(vfdP->fileName);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "delete temp file");
            return;
        }
    }
    if (isDeleteFileDir) {
        char fileDirPath[PATH_MAX];
        Status ret = GetTempFileDir(vfdP->fileName, fileDirPath, PATH_MAX);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "get temp file dir");
            return;
        }
        // 若fileDirPath目录尾部没有'/'，检测目录内是否存在文件时生成的文件路径会存在问题，所以需要补上'/'
        if (fileDirPath[strlen(fileDirPath) - 1] != '/') {
            ret = ConcatStrNoTruncate(fileDirPath, PATH_MAX, "/");
            if (ret != GMERR_OK) {
                DB_LOG_ERROR_AND_SET_LASTERR(ret, "append slash");
                return;
            }
        }
        DeleteTempFileDir(fileDirPath);
    }
    FreeVfd(tempFileMgr, fileIndex);
}

void TempFileCloseWithoutDelete(TempFileMgrT *tempFileMgr, uint32_t fileIndex)
{
    DB_POINTER(tempFileMgr);
    bool isFileClose = IsFileClose(tempFileMgr, fileIndex);
    if (!isFileClose) {
        LruDelete(tempFileMgr, fileIndex);
    }
    FreeVfd(tempFileMgr, fileIndex);
}

void TempFileManageClear(TempFileMgrT *tempFileMgr)
{
    DB_POINTER(tempFileMgr);
    if (tempFileMgr->vfdCache != NULL) {
        DbDynMemCtxFree(tempFileMgr->tempFileMemCtx, tempFileMgr->vfdCache);
        tempFileMgr->vfdCache = NULL;
    }
    if (tempFileMgr->filePath != NULL) {
        DbDynMemCtxFree(tempFileMgr->tempFileMemCtx, tempFileMgr->filePath);
        tempFileMgr->filePath = NULL;
    }
    DbDynMemCtxFree(tempFileMgr->tempFileMemCtx, tempFileMgr);
    tempFileMgr = NULL;
}

static Status InitFileManagerInner(TempFileMgrT **tempFileMgr, DbMemCtxT *memCtx, char *filePath)
{
    DB_POINTER3(tempFileMgr, memCtx, filePath);
    Status ret = TempFileManageInit(tempFileMgr, memCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    char *filePathAddr = DbDynMemCtxAlloc((*tempFileMgr)->tempFileMemCtx, PATH_MAX);
    if (filePathAddr == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "alloc file path memory");
        goto ERROR;
    }
    errno_t err = strncpy_s(filePathAddr, PATH_MAX, filePath, PATH_MAX);
    if (err != EOK) {
        ret = GMERR_FIELD_OVERFLOW;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "copy file path");
        DbDynMemCtxFree((*tempFileMgr)->tempFileMemCtx, filePathAddr);
        goto ERROR;
    }

    (*tempFileMgr)->filePath = filePathAddr;
    return ret;
ERROR:
    TempFileManageClear(*tempFileMgr);
    (*tempFileMgr) = NULL;
    return ret;
}

Status InitSpecifiedFileManager(TempFileMgrT **tempFileMgr, DbMemCtxT *memCtx, char *filePath)
{
    DB_POINTER3(tempFileMgr, memCtx, filePath);
    int32_t fd = DB_INVALID_FD;
    Status ret = DbOpenFile(filePath, O_RDWR | O_CREAT | O_TRUNC | O_BINARY, (uint32_t)PERM_GRPR, &fd);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (fd >= 0) {
        DbCloseFile(fd);
        ret = DbRemoveFile(filePath);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "delete temp file");
            return ret;
        }
    }
    return InitFileManagerInner(tempFileMgr, memCtx, filePath);
}

Status LoadSpecifiedFileManager(TempFileMgrT **tempFileMgr, DbMemCtxT *memCtx, char *filePath)
{
    DB_POINTER3(tempFileMgr, memCtx, filePath);
    int32_t fd = DB_INVALID_FD;
    Status ret = DbOpenFile(filePath, O_RDONLY | O_BINARY, PERM_GRPR, &fd);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (fd <= 0) {
        DbCloseFile(fd);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "find target temp file");
        return GMERR_INTERNAL_ERROR;
    }
    DbCloseFile(fd);
    return InitFileManagerInner(tempFileMgr, memCtx, filePath);
}

Status TempFileFsync(TempFileMgrT *tempFileMgr)
{
    DB_POINTER(tempFileMgr);
    uint32_t vfdIndex = tempFileMgr->vfdIndex;
    Status ret = FileAccess(tempFileMgr, vfdIndex);
    if (ret != GMERR_OK) {
        return ret;
    }
    VfdT *vfdP = &tempFileMgr->vfdCache[vfdIndex];
    ret = DbFsyncFile(vfdP->fd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "fsync buffer file.");
    }
    return ret;
}
