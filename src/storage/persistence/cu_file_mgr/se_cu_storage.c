/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Implementation for CU file management
 * Author: zhangtao
 * Create: 2024-01-02
 */

#include "adpt_log.h"
#include "adpt_define.h"
#include "adpt_string.h"
#include "db_hash.h"
#include "db_file.h"
#include "db_config.h"
#include "db_crc.h"
#include "se_persist.h"
#include "se_cu_storage.h"
#ifdef FEATURE_TS
#include "dm_meta_prop_label.h"
#include "dm_data_ts.h"
#include "ee_background_schedule.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif

#define CU_FILE_PREFIX "cuFile"
#define CU_METAFILE_PREFIX "cuMetaFile"

#if defined(HPE)
#define CU_FILE_FILL_BUFFER (8)
#endif
#define FILE_CREATE_FLAG (O_RDWR | O_CREAT | O_TRUNC | O_BINARY)
#define FILE_OPEN_FLAG (O_RDWR | O_BINARY)
#define FILE_PERM (PERM_USRRW)

typedef struct ThreadLocalCuFd {
    DbMemCtxT *memCtx;           // MemCtx to allocate cuFilePath buffer
    CuStorageKeyT cuStorageKey;  // Cu logical and physical table Ids
    int32_t curCuFileId;         // Current Cu file id which equals to (cuPointer / CU_FILE_MAX_SIZE)
    int32_t curCuFd;             // Current Cu fd
    char *cuFilePath;            // Cached cuFilePath
} ThreadLocalCuFdT;

typedef struct ThreadLocalCuMetaFd {
    CuStorageKeyT cuStorageKey;  // Cu logical and physical table Ids
    int32_t curCuMetaFd;         // Current opened CuMetaFile fd
} ThreadLocalCuMetaFdT;

// Each thread share the same local CuFd to reduce fd operation cost
static DB_THREAD_LOCAL ThreadLocalCuFdT g_threadLocalCuFd = {.memCtx = NULL,
    .cuStorageKey = {.tblSpcId = INVALID_TBL_SPC_ID, .tblId = INVALID_TBL_ID},
    .curCuFileId = INVALID_CU_FILE_ID,
    .curCuFd = DB_INVALID_FD,
    .cuFilePath = NULL};

static DB_THREAD_LOCAL ThreadLocalCuMetaFdT g_threadLocalCuMetaFd = {
    .cuStorageKey = {.tblSpcId = INVALID_TBL_SPC_ID, .tblId = INVALID_TBL_ID}, .curCuMetaFd = DB_INVALID_FD};

bool ThreadLocalCuFdValid(void)
{
    return (g_threadLocalCuMetaFd.curCuMetaFd != DB_INVALID_FD) || (g_threadLocalCuFd.curCuFd != DB_INVALID_FD);
}

void ResetThreadLocalCuFd(void)
{
    if (g_threadLocalCuFd.curCuFd != DB_INVALID_FD) {
        Status ret = DbFsyncFile(g_threadLocalCuFd.curCuFd);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to fsync CuFile.");
        }
        DbCloseFile(g_threadLocalCuFd.curCuFd);
    }
    if (g_threadLocalCuFd.cuFilePath != NULL) {
        DbDynMemCtxFree(g_threadLocalCuFd.memCtx, g_threadLocalCuFd.cuFilePath);
        g_threadLocalCuFd.memCtx = NULL;
        g_threadLocalCuFd.cuFilePath = NULL;
    }
    g_threadLocalCuFd.cuStorageKey.tblSpcId = INVALID_TBL_SPC_ID;
    g_threadLocalCuFd.cuStorageKey.tblId = INVALID_TBL_ID;
    g_threadLocalCuFd.curCuFileId = INVALID_CU_FILE_ID;
    g_threadLocalCuFd.curCuFd = DB_INVALID_FD;
}

void ResetThreadLocalCuMetaFd(void)
{
    if (g_threadLocalCuMetaFd.curCuMetaFd != DB_INVALID_FD) {
        Status ret = DbFsyncFile(g_threadLocalCuMetaFd.curCuMetaFd);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to fsync CuMetaFile.");
        }
        DbCloseFile(g_threadLocalCuMetaFd.curCuMetaFd);
    }
    g_threadLocalCuMetaFd.cuStorageKey.tblSpcId = INVALID_TBL_SPC_ID;
    g_threadLocalCuMetaFd.cuStorageKey.tblId = INVALID_TBL_ID;
    g_threadLocalCuMetaFd.curCuMetaFd = DB_INVALID_FD;
}

static Status ConcatStrNoTruncate(char *strBuf, uint32_t maxLen, const char *str)
{
    // < maxLen: the string should always end in '\0'
    // Max length limit exceeded
    if (strlen(strBuf) + strlen(str) >= maxLen) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "String exceeds the maximum length: (%" PRIu32 ").", maxLen);
        return GMERR_DATA_EXCEPTION;
    }
    if (strcat_s(strBuf, maxLen, str) != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATA_EXCEPTION, "Unable to concat strings, os no %" PRId32 ".", (int32_t)errno);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

static Status GetCuMetaFilePath(char *pathBuf)
{
    DB_POINTER(pathBuf);

    char cuMetaFileName[PATH_MAX] = {0};
    errno_t err = memcpy_s(cuMetaFileName, PATH_MAX, CU_METAFILE_PREFIX, strlen(CU_METAFILE_PREFIX));
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to memset_s cuMetaFile prefix.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    return ConcatStrNoTruncate(pathBuf, PATH_MAX, cuMetaFileName);
}

static Status GetCuFilePath(char *pathBuf, uint32_t tblId, int64_t cuPointer)
{
    DB_POINTER(pathBuf);

    int32_t curCuFileId = GetCuFileId(cuPointer);
    if (curCuFileId == INVALID_CU_FILE_ID) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATA_EXCEPTION, "Unable to get valid cuFileId. cuFileId: (%" PRId32 ").", curCuFileId);
        return GMERR_DATA_EXCEPTION;
    }
    char cuFileName[PATH_MAX] = {0};
    int32_t err = snprintf_s(cuFileName, sizeof(cuFileName), sizeof(cuFileName) - 1,
        CU_FILE_PREFIX "_%" PRIu32 ".%" PRId32, tblId, curCuFileId);
    if (err < 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Unable to get cuFileName.");
        return GMERR_FIELD_OVERFLOW;
    }
    return ConcatStrNoTruncate(pathBuf, PATH_MAX, cuFileName);
}

static Status AppendStrSlashIfNeeded(char *pathBuf)
{
    DB_POINTER(pathBuf);
    if (pathBuf[strlen(pathBuf) - 1] != '/') {
        return ConcatStrNoTruncate(pathBuf, PATH_MAX, "/");
    }
    return GMERR_OK;
}

static Status GetCuFileDirPath(const char *cStoreDirPath, char *pathBuf, uint32_t tblSpcId, uint32_t tblId)
{
    DB_POINTER2(cStoreDirPath, pathBuf);
    Status ret = ConcatStrNoTruncate(pathBuf, PATH_MAX, cStoreDirPath);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = AppendStrSlashIfNeeded(pathBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    // if tblSpcId equals INVALID_TBL_SPC_ID, Cu will store in the default folder(e.g. /data/gmdb/cstore)
    // otherwise Cu will store in tblSpcId folder(e.g. /data/gmdb/cstore/logicalLabelName/physicalLabelName/cuFile_0)
    if (tblSpcId != INVALID_TBL_SPC_ID) {
        char tblSpcStr[PATH_MAX] = {0};
        int32_t err = snprintf_s(tblSpcStr, sizeof(tblSpcStr), sizeof(tblSpcStr) - 1, "%" PRIu32, tblSpcId);
        if (err < 0) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Unable to get tblSpcStr.");
            return GMERR_FIELD_OVERFLOW;
        }
        ret = ConcatStrNoTruncate(pathBuf, PATH_MAX, tblSpcStr);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    ret = AppendStrSlashIfNeeded(pathBuf);
    if (ret != GMERR_OK) {
        return ret;
    }
    // Each tblId corresponds to one folder
    if (tblId != INVALID_TBL_ID) {
        char tblStr[PATH_MAX] = {0};
        int32_t err = snprintf_s(tblStr, sizeof(tblStr), sizeof(tblStr) - 1, "%" PRIu32, tblId);
        if (err < 0) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Unable to get tblStr.");
            return GMERR_FIELD_OVERFLOW;
        }
        ret = ConcatStrNoTruncate(pathBuf, PATH_MAX, tblStr);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return AppendStrSlashIfNeeded(pathBuf);
}

static Status GetCStoreDirPath(char *path)
{
    DB_POINTER(path);
    Status ret = GMERR_OK;
    if (SeGetPersistMode() == PERSIST_OFF) {  // purely for ut test without persistence feature compiled
        DbCfgValueT cfgValue;
        ret = DbCfgGet(DbGetCfgHandle(NULL), DB_CFG_CSTORE_DIR, &cfgValue);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "|CFG| Unable to get CStore dirPath.");
            return ret;
        }
        errno_t err = memcpy_s(path, PATH_MAX, cfgValue.str, DB_CFG_PARAM_MAX_STRING);
        if (err != EOK) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "|CFG| Unable to get memcpy_s data(cu) dirPath.");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    } else {
        SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(DbGetProcGlobalId());
        const char *dbDir = SeGetMainDataDirPath(seIns);
        ret = DbGetExternalErrno(SeFileConstructPath(dbDir, "cstore", path, PATH_MAX));
    }
    return ret;
}

static Status SeFormatSubDirPath(char *buff, uint32_t maxLen, const char *currPath)
{
    int32_t err = snprintf_s(buff, maxLen, maxLen - 1, "%s", currPath);
    if (err < 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Unable to format sub dir path.");
        return GMERR_FIELD_OVERFLOW;
    }
    Status ret = AppendStrSlashIfNeeded(buff);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to add slash.");
        return ret;
    }
    return ret;
}
// 目前的cuFile目录结构，最多只有一层递归调用
Status GetSingleDirFileSizeRecursive(const char *cuDirPath, size_t *totalFileSize)
{
    DbDIRT *cuDir = NULL;
    Status ret = DbOpenDir(cuDirPath, &cuDir);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to open physical table directory path:(%s).", cuDirPath);
        return ret;
    }
    char cuSubDirPath[PATH_MAX] = {0};
    ret = SeFormatSubDirPath(cuSubDirPath, PATH_MAX, cuDirPath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to format cuSubDirPath.");
        DbCloseDir(cuDir);
        return ret;
    }
    const uint32_t len = (uint32_t)strlen(cuSubDirPath);
    while ((ret = DbReadDir(cuDir, cuSubDirPath + len, PATH_MAX - len)) == GMERR_OK) {
        if (strcmp(cuSubDirPath + len, ".") == 0 || strcmp(cuSubDirPath + len, "..") == 0) {
            continue;
        }
        if (DbDirExist(cuSubDirPath)) {
            ret = GetSingleDirFileSizeRecursive(cuSubDirPath, totalFileSize);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to get one physical table dir file size.");
                DbCloseDir(cuDir);
                return ret;
            }
        } else if (DbFileExist(cuSubDirPath)) {
            size_t fileSize = 0;
            DbFileSize(cuSubDirPath, &fileSize);
            *totalFileSize += fileSize;
        }
    }

    DbCloseDir(cuDir);
    return ret == GMERR_NO_DATA ? GMERR_OK : ret;
}

Status GetAllTableCuFileSizeByScanDir(uint32_t *count, CuFileSizeRecordT *recordArray, uint32_t maxTblNum)
{
    DB_POINTER2(count, recordArray);
    char cuDirPath[PATH_MAX] = {0};
    Status ret = GetCStoreDirPath(cuDirPath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "|CFG| SE Config: Unable to get CStore dirPath.");
        return ret;
    }
    // 非重启场景目录不存在属于正常
    if (!DbDirExist(cuDirPath)) {
        return GMERR_OK;
    }
    char cuSubDirPath[PATH_MAX] = {0};
    ret = SeFormatSubDirPath(cuSubDirPath, PATH_MAX, cuDirPath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to format cuDirPath.");
        return ret;
    }
    DbDIRT *cuDir = NULL;
    ret = DbOpenDir(cuDirPath, &cuDir);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_DIRECTORY_OPERATE_FAILED, "Unable to open cstore directory path:(%s).", cuDirPath);
        return ret;
    }

    const uint32_t len = (uint32_t)strlen(cuSubDirPath);
    while ((ret = DbReadDir(cuDir, cuSubDirPath + len, PATH_MAX - len)) == GMERR_OK) {
        // cstore目录下理论上只有目录，没有文件
        if (strcmp(cuSubDirPath + len, ".") == 0 || strcmp(cuSubDirPath + len, "..") == 0 ||
            DbFileExist(cuSubDirPath)) {
            continue;
        }

        (*count)++;
        if (*count > maxTblNum) {
            ret = GMERR_PROGRAM_LIMIT_EXCEEDED;
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "too many subdirs in cstore dir.");
            break;
        }
        uint32_t tblSpcId = 0;
        DbStrToUint32(cuSubDirPath + len, &tblSpcId);
        size_t totalFileSize = 0;
        ret = GetSingleDirFileSizeRecursive(cuSubDirPath, &totalFileSize);
        if (ret != GMERR_OK) {
            break;
        }
        recordArray[*count - 1].tblSpcId = tblSpcId;
        recordArray[*count - 1].size = totalFileSize;
    }

    DbCloseDir(cuDir);
    return ret == GMERR_NO_DATA ? GMERR_OK : ret;
}

#ifdef FEATURE_TS
static bool GetTsTablePathFromMeta(char *cStoreDirPath, uint32_t pathLen, uint32_t tblSpcId)
{
    DmVertexLabelT *logicalLabel = NULL;
    bool hasCustomPath = false;

    if (!CataVertexLabelExist(tblSpcId, NULL)) {
        // 删逻辑表的场景下，这里的确会发生找不到逻辑表的场景。
        return hasCustomPath;
    }

    Status ret = CataGetVertexLabelById(NULL, tblSpcId, &logicalLabel);
    if (logicalLabel == NULL) {
        DB_LOG_INFO("Unable to get logicalLabel, ret=%d, id=%u.", ret, tblSpcId);
        return hasCustomPath;
    }
    DmTsInfoT *tsInfo = (DmTsInfoT *)(logicalLabel->metaVertexLabel->extraInfo.data);
    if (tsInfo->tablePathLen != 0) {
        hasCustomPath = true;
        (void)sprintf_s(cStoreDirPath, pathLen, "%s/cstore/", tsInfo->tablePath);
    }
    CataReleaseVertexLabel(logicalLabel);
    return hasCustomPath;
}

static bool GetTsTablePathFromBgWoker(char *customTablePath, uint32_t pathLen, uint32_t tblSpcId)
{
    char tablePath[PATH_MAX] = {0};
    bool hasCustomPath = false;
    Status ret = TsLcmBgDropTblGetCustomTablePath(tblSpcId, tablePath, PATH_MAX, &hasCustomPath);
    if (ret != GMERR_OK) {
        DB_LOG_WARN(ret, "Unable to get table path");
    }
    if (hasCustomPath) {
        (void)sprintf_s(customTablePath, pathLen, "%s/cstore/", tablePath);
    }
    return hasCustomPath;
}
#endif

Status BuildCuDirPath(char *cuFileDirPath, uint32_t tblSpcId, uint32_t tblId)
{
    DB_POINTER(cuFileDirPath);
    char cStoreDirPath[PATH_MAX] = {0};
    bool hasCustomPath = false;
    Status ret = GMERR_OK;
#ifdef FEATURE_TS
    hasCustomPath = GetTsTablePathFromMeta(cStoreDirPath, PATH_MAX, tblSpcId);
    if (!hasCustomPath) {
        hasCustomPath = GetTsTablePathFromBgWoker(cStoreDirPath, PATH_MAX, tblSpcId);
    }
#endif
    if (!hasCustomPath) {
        ret = GetCStoreDirPath(cStoreDirPath);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "|CFG| SE Config: Unable to get CStore dirPath.");
            return ret;
        }
    }
    ret = GetCuFileDirPath(cStoreDirPath, cuFileDirPath, tblSpcId, tblId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to get Cu file dirPath");
    }
    return ret;
}

static Status PrepareTblDirPath(char *pathBuf, uint32_t tblSpcId, uint32_t tblId)
{
    DB_POINTER(pathBuf);

    char cuFileDirPath[PATH_MAX] = {0};
    Status ret = BuildCuDirPath(cuFileDirPath, tblSpcId, tblId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to build Cu table directory path.");
        return ret;
    }
    if (!DbDirExist(cuFileDirPath)) {
        ret = DbMakeDirWithGRPRXPermission(cuFileDirPath);
        SeFileAlarmUpdate(ret);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to create Cu file dirPath: (%s)", cuFileDirPath);
            return ret;
        }
    }
    ret = ConcatStrNoTruncate(pathBuf, PATH_MAX, cuFileDirPath);
    if (ret != GMERR_OK) {
        return ret;
    }

    return AppendStrSlashIfNeeded(pathBuf);
}

static Status FAllocateCuFileSpace(int32_t fd, int64_t cuPointer, uint32_t cuSize)
{
    // Procedure ensured: cuFileOffset + cuSize <= CU_FILE_MAX_SIZE, no overflows
    // fallocate mode is 0: default behaviour, allocate file space with all zero values
    int32_t curCuFileOffset = GetCuFileOffset(cuPointer);
    if (curCuFileOffset == INVALID_CU_OFFSET) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATA_EXCEPTION, "Unable to get valid cuFileOffset. cuFileOffset: (%" PRId32 ").", curCuFileOffset);
        return GMERR_DATA_EXCEPTION;
    }
    Status ret = DbAdptFallocate(fd, 0, cuSize, curCuFileOffset);
    SeFileAlarmUpdate(ret);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to fallocate Cu file space.");
        return ret;
    }
    return GMERR_OK;
}

Status CreateFileIfNeeded(const char *filePath)
{
    DB_POINTER(filePath);

    if (DbFileExist(filePath)) {
        return GMERR_OK;
    }
    int32_t fd = DB_INVALID_FD;
    Status ret = DbAdptOpen(filePath, FILE_CREATE_FLAG, FILE_PERM, &fd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to create file. filePath: (%s).", filePath);
        return ret;
    }
    DbCloseFile(fd);
    return GMERR_OK;
}

Status ExpandCuFileIfNeeded(const char *cuFilePath, int32_t fd, int64_t cuPointer, uint32_t cuSize)
{
    DB_POINTER(cuFilePath);
    return FAllocateCuFileSpace(fd, cuPointer, cuSize);
}

Status PrepareCuMetaFilePath(char *pathBuf, uint32_t tblSpcId, uint32_t tblId)
{
    DB_POINTER(pathBuf);
    Status ret = PrepareTblDirPath(pathBuf, tblSpcId, tblId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to prepare physical table dirPath.");
        return ret;
    }

    ret = GetCuMetaFilePath(pathBuf);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to get cuMetaFilePath.");
    }
    return ret;
}

Status PrepareCuFilePath(char *pathBuf, DbSpinLockT *cuFileLock, uint32_t tblSpcId, uint32_t tblId, int64_t cuPointer)
{
    DB_POINTER2(pathBuf, cuFileLock);
    Status ret = PrepareTblDirPath(pathBuf, tblSpcId, tblId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to prepare physical table dirPath.");
        return ret;
    }

    ret = GetCuFilePath(pathBuf, tblId, cuPointer);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to get Cu filePath.");
        return ret;
    }

    if (!DbFileExist(pathBuf)) {
        DbSpinLock(cuFileLock);
        ret = CreateFileIfNeeded(pathBuf);
        DbSpinUnlock(cuFileLock);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to create Cu file in prepare stage. cuFilePath: (%s)", pathBuf);
            return ret;
        }
    }
    return ret;
}

Status PrepareCuMetaFile(CuStorageHdlT *cuStorageHdlT)
{
    DB_POINTER(cuStorageHdlT);

    char cuMetaFilePath[PATH_MAX] = {0};
    Status ret =
        PrepareCuMetaFilePath(cuMetaFilePath, cuStorageHdlT->cuStorageKey.tblSpcId, cuStorageHdlT->cuStorageKey.tblId);
    if (ret != GMERR_OK) {
        return ret;
    }
    // CuMetaFile will be created only once
    if (!DbFileExist(cuMetaFilePath)) {
        DbSpinLock(&cuStorageHdlT->cuMetaFileLock);
        ret = CreateFileIfNeeded(cuMetaFilePath);
        DbSpinUnlock(&cuStorageHdlT->cuMetaFileLock);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to create cuMetaFile. cuMetaFilePath: (%s)", cuMetaFilePath);
            return ret;
        }
    }

    return GMERR_OK;
}

static int32_t OpenCuFile(CuStorageHdlT *cuStorageHdl, int64_t cuPointer)
{
    // Notes: Do not lock here because it will block all threads' open requests in concurrency scenario
    int32_t cuFileId = GetCuFileId(cuPointer);
    if (cuFileId == INVALID_CU_FILE_ID) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATA_EXCEPTION, "Unable to get valid cuFileId. cuFileId: (%" PRId32 ").", cuFileId);
        return DB_INVALID_FD;
    }
    if (cuStorageHdl->cuStorageKey.tblSpcId == g_threadLocalCuFd.cuStorageKey.tblSpcId &&
        cuStorageHdl->cuStorageKey.tblId == g_threadLocalCuFd.cuStorageKey.tblId &&
        cuFileId == g_threadLocalCuFd.curCuFileId) {
        return g_threadLocalCuFd.curCuFd;
    }
    if (g_threadLocalCuFd.cuFilePath == NULL) {
        // All cuStorageHdl share the same memCtx, only assign once here
        g_threadLocalCuFd.memCtx = cuStorageHdl->memCtx;
        g_threadLocalCuFd.cuFilePath = (char *)DbDynMemCtxAlloc(cuStorageHdl->memCtx, PATH_MAX);
        if (g_threadLocalCuFd.cuFilePath == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to allocate buffer memory for cuFilePath.");
            return DB_INVALID_FD;
        }
    }
    // Reuse cuFilePath buffer to avoid allocating repeatedly
    (void)memset_s(g_threadLocalCuFd.cuFilePath, PATH_MAX, 0x00, PATH_MAX);
    Status ret = PrepareCuFilePath(g_threadLocalCuFd.cuFilePath, &cuStorageHdl->cuFileLock,
        cuStorageHdl->cuStorageKey.tblSpcId, cuStorageHdl->cuStorageKey.tblId, cuPointer);
    if (ret != GMERR_OK) {
        return DB_INVALID_FD;
    }

    int32_t fd = DB_INVALID_FD;
    ret = DbAdptOpen(g_threadLocalCuFd.cuFilePath, FILE_OPEN_FLAG, FILE_PERM, &fd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to open Cu file: (%s)", g_threadLocalCuFd.cuFilePath);
        return DB_INVALID_FD;
    }
    if (fd != DB_INVALID_FD) {
        // Notes: Close the old CuFd before switch CuFd. thread may switch CuFd many times, SeEndCuFileAccess will close
        // the last CuFd and reset ThreadLocalCuFd
        if (g_threadLocalCuFd.curCuFd != DB_INVALID_FD) {
            ret = DbFsyncFile(g_threadLocalCuFd.curCuFd);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to fsync CuFile.");
            }
            DbCloseFile(g_threadLocalCuFd.curCuFd);
        }
        g_threadLocalCuFd.cuStorageKey.tblSpcId = cuStorageHdl->cuStorageKey.tblSpcId;
        g_threadLocalCuFd.cuStorageKey.tblId = cuStorageHdl->cuStorageKey.tblId;
        g_threadLocalCuFd.curCuFileId = cuFileId;
        g_threadLocalCuFd.curCuFd = fd;
    }
    return fd;
}

static void ErrorHandleForLoadCu(DbMemCtxT *memCtx, DbBufT *buffer)
{
    if (buffer->buf != NULL) {
        DbDynMemCtxFree(memCtx, buffer->buf);
        buffer->buf = NULL;
    }
}

Status PrepareCuFile(CuStorageHdlT *cuStorageHdl, int64_t cuPointer, uint32_t cuSize)
{
    DB_POINTER(cuStorageHdl);
    int32_t fd = OpenCuFile(cuStorageHdl, cuPointer);
    if (fd == DB_INVALID_FD) {
        if (g_threadLocalCuFd.cuFilePath != NULL) {
            DB_LOG_ERROR(GMERR_FILE_OPERATE_FAILED, "Unable to open Cu file in prepare stage. cuFilePath: (%s)",
                g_threadLocalCuFd.cuFilePath);
        }
        return GMERR_FILE_OPERATE_FAILED;
    }
    Status ret = ExpandCuFileIfNeeded(g_threadLocalCuFd.cuFilePath, fd, cuPointer, cuSize);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to expand Cu file in prepare stage. cuFilePath: (%s).", g_threadLocalCuFd.cuFilePath);
        return ret;
    }
    return ret;
}

Status SaveCu(CuStorageHdlT *cuStorageHdl, DbBufT *buffer, int64_t cuPointer)
{
    DB_POINTER2(cuStorageHdl, buffer);
    int32_t cuFileId = GetCuFileId(cuPointer);
    if (cuFileId == INVALID_CU_FILE_ID) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATA_EXCEPTION, "Unable to get valid cuFileId. cuFileId: (%" PRId32 ").", cuFileId);
        return GMERR_DATA_EXCEPTION;
    }
    int64_t curCuFileOffset = GetCuFileOffset(cuPointer);
    if (curCuFileOffset == INVALID_CU_OFFSET) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATA_EXCEPTION, "Unable to get valid cuFileOffset: (%" PRId64 ").", curCuFileOffset);
        return GMERR_DATA_EXCEPTION;
    }
    int32_t cuFileFd = OpenCuFile(cuStorageHdl, cuPointer);
    if (cuFileFd == DB_INVALID_FD) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_FILE_OPERATE_FAILED, "Unable to open CuFile with fd: (%" PRId32 ").", cuFileFd);
        return GMERR_FILE_OPERATE_FAILED;
    }
    Status ret = DbPwriteFile(cuFileFd, (char *)buffer->buf, buffer->len, curCuFileOffset);
    SeFileAlarmUpdate(ret);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to write Cu buffer into CuFile.");
    }
    return ret;
}

Status LoadCu(DbMemCtxT *memCtx, CuStorageHdlT *cuStorageHdl, DbBufT *buffer, int64_t cuPointer)
{
    DB_POINTER3(memCtx, cuStorageHdl, buffer);
    int32_t cuFileId = GetCuFileId(cuPointer);
    if (cuFileId == INVALID_CU_FILE_ID) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATA_EXCEPTION, "Unable to get valid cuFileId. cuFileId: (%" PRId32 ").", cuFileId);
        return GMERR_DATA_EXCEPTION;
    }
    int64_t cuFileOffset = GetCuFileOffset(cuPointer);
    if (cuFileOffset == INVALID_CU_OFFSET) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATA_EXCEPTION, "Unable to get valid cuFileOffset when LoadCu. offset:(%" PRId64 ").", cuFileOffset);
        return GMERR_DATA_EXCEPTION;
    }
    if (SECUREC_UNLIKELY(buffer->len == 0)) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_INVALID_BUFFER, "Unable to get valid buffer length when LoadCu. bufLen:(%" PRIu32 ").", buffer->len);
        return GMERR_INVALID_BUFFER;
    }
    if (buffer->buf == NULL) {
        buffer->buf = (uint8_t *)DbDynMemCtxAlloc(memCtx, sizeof(uint8_t) * buffer->len);
        if (buffer->buf == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to allocate buffer memory when LoadCu.");
            return GMERR_OUT_OF_MEMORY;
        }
    }
    Status ret = GMERR_FILE_OPERATE_FAILED;
    int32_t cuFileFd = OpenCuFile(cuStorageHdl, cuPointer);
    if (cuFileFd == DB_INVALID_FD) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to open CuFile with fd: (%" PRId32 ").", cuFileFd);
        goto ERROR;
    }
    uint32_t readSize = 0;
    ret = DbPreadFile(cuFileFd, (char *)buffer->buf, buffer->len, cuFileOffset, &readSize);
    SeFileAlarmUpdate(ret);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to read data from CuFile.");
        goto ERROR;
    }
    if (readSize != buffer->len) {
        ret = GMERR_FILE_OPERATE_FAILED;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Read Cu size:%" PRIu32 ", required size:%" PRIu32, readSize, buffer->len);
        goto ERROR;
    }
    return ret;
ERROR:
    ErrorHandleForLoadCu(memCtx, buffer);
    return ret;
}

static int32_t OpenCuMetaFile(uint32_t tblSpcId, uint32_t tblId)
{
    if (g_threadLocalCuMetaFd.cuStorageKey.tblSpcId == tblSpcId && g_threadLocalCuMetaFd.cuStorageKey.tblId == tblId) {
        return g_threadLocalCuMetaFd.curCuMetaFd;
    }

    char cuMetaFilePath[PATH_MAX] = {0};
    Status ret = PrepareCuMetaFilePath(cuMetaFilePath, tblSpcId, tblId);
    if (ret != GMERR_OK) {
        return DB_INVALID_FD;
    }

    int32_t fd = DB_INVALID_FD;
    ret = DbAdptOpen(cuMetaFilePath, FILE_OPEN_FLAG, FILE_PERM, &fd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to open cuMetaFile. cuMetaFilePath: (%s)", cuMetaFilePath);
        return DB_INVALID_FD;
    }
    if (fd != DB_INVALID_FD) {
        if (g_threadLocalCuMetaFd.curCuMetaFd != DB_INVALID_FD) {
            ret = DbFsyncFile(g_threadLocalCuMetaFd.curCuMetaFd);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to fsync CuMetaFile.");
            }
            DbCloseFile(g_threadLocalCuMetaFd.curCuMetaFd);
        }
        g_threadLocalCuMetaFd.cuStorageKey.tblSpcId = tblSpcId;
        g_threadLocalCuMetaFd.cuStorageKey.tblId = tblId;
        g_threadLocalCuMetaFd.curCuMetaFd = fd;
    }
    return fd;
}

// cuMetaFile 数据排布： (uint32_t) checkSum | (uint32_t) maxCuId | (int64_t) maxCuPointer
Status SaveCuMeta(CuStorageHdlT *cuStorageHdl, size_t *diskSizeIncrease)
{
    DB_POINTER(cuStorageHdl);
    int32_t cuMetaFileFd = OpenCuMetaFile(cuStorageHdl->cuStorageKey.tblSpcId, cuStorageHdl->cuStorageKey.tblId);
    if (cuMetaFileFd == DB_INVALID_FD) {
        DB_LOG_ERROR(GMERR_FILE_OPERATE_FAILED, "Unable to get valid cuMetaFile fd.");
        return GMERR_FILE_OPERATE_FAILED;
    }
    // Lock protection for atomic write in multiple thread situation.
    DbSpinLock(&cuStorageHdl->cuMetaFileLock);
    size_t cuMetaFileSize = 0;
    Status ret = DbFileSizeByFd(cuMetaFileFd, &cuMetaFileSize);
    if (ret != GMERR_OK) {
        DbSpinUnlock(&cuStorageHdl->cuMetaFileLock);
        DB_LOG_ERROR(ret, "Unable to cu meta file size by fd.");
        return ret;
    }
    CuMetaFileT metaData = {.meta.checkSum = 0,
        .meta.maxCuId = cuStorageHdl->maxCuId,
        .meta.maxCuPointer = (uint64_t)cuStorageHdl->maxCuPointer,
        .meta.rowCnt = cuStorageHdl->rowCnt,
        .cuFileSize = (uint64_t)cuStorageHdl->cuFileSize};

    uint32_t actualDataSize = (uint32_t)sizeof(metaData) - (uint32_t)sizeof(metaData.meta.checkSum);
    DbAddCheckSum((char *)(void *)&metaData.meta.maxCuId, actualDataSize, &metaData.meta.checkSum);
    ret = DbPwriteFile(cuMetaFileFd, (char *)(&metaData), (uint32_t)sizeof(metaData), (int64_t)0);
    SeFileAlarmUpdate(ret);
    if (ret != GMERR_OK) {
        DbSpinUnlock(&cuStorageHdl->cuMetaFileLock);
        DB_LOG_ERROR(ret, "Unable to write Cu meta data into cuMetaFile.");
        return ret;
    }

    if (SECUREC_UNLIKELY(cuMetaFileSize != sizeof(metaData))) {
        *diskSizeIncrease = sizeof(metaData) - cuMetaFileSize;
    }
    DbSpinUnlock(&cuStorageHdl->cuMetaFileLock);
    return GMERR_OK;
}

Status LoadCuMeta(CuStorageHdlT *cuStorageHdl)
{
    DB_POINTER(cuStorageHdl);

    int32_t cuMetaFileFd = OpenCuMetaFile(cuStorageHdl->cuStorageKey.tblSpcId, cuStorageHdl->cuStorageKey.tblId);
    if (cuMetaFileFd == DB_INVALID_FD) {
        DB_LOG_ERROR(GMERR_FILE_OPERATE_FAILED, "Unable to open cuMetaFile. fd:(%" PRId32 ").", cuMetaFileFd);
        return GMERR_FILE_OPERATE_FAILED;
    }

    CuMetaFileT metaData = {0};
    uint32_t readSize = 0;
    Status ret = DbPreadFile(cuMetaFileFd, (char *)(&metaData), sizeof(metaData), (int64_t)0, &readSize);
    SeFileAlarmUpdate(ret);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to load Cu meta data from cuMetaFile.");
        return ret;
    }

    if (readSize == 0) {  // 首次打开文件读readSize为0
        return ret;
    }
    if (readSize != sizeof(CuMetaT) && readSize != sizeof(CuMetaFileT)) {
        DB_LOG_ERROR(GMERR_DATA_CORRUPTION, "unable to load cu meta due to file size doesn't match. readSize:%" PRIu32,
            readSize);
        return GMERR_DATA_CORRUPTION;
    }

    if (readSize == sizeof(CuMetaT) && !DbCheckCrc((char *)(void *)&metaData.meta.maxCuId,
                                           sizeof(CuMetaT) - sizeof(metaData.meta.checkSum), &metaData.meta.checkSum)) {
        DB_LOG_ERROR(GMERR_CRC_CHECK_FAILED, "checksum not match with data when loading CuMetaT.");
        return GMERR_CRC_CHECK_FAILED;
    }
    if (readSize == sizeof(CuMetaFileT) &&
        !DbCheckCrc((char *)(void *)&metaData.meta.maxCuId, sizeof(CuMetaFileT) - sizeof(metaData.meta.checkSum),
            &metaData.meta.checkSum)) {
        DB_LOG_ERROR(GMERR_CRC_CHECK_FAILED, "checksum not match with data when loading CuMetaFileT.");
        return GMERR_CRC_CHECK_FAILED;
    }

    // Update cuStorageHdl members value
    cuStorageHdl->maxCuId = metaData.meta.maxCuId;
    cuStorageHdl->maxCuPointer = (int64_t)metaData.meta.maxCuPointer;
    cuStorageHdl->rowCnt = metaData.meta.rowCnt;
    if (readSize == sizeof(CuMetaFileT)) {
        cuStorageHdl->cuFileSize = (int64_t)metaData.cuFileSize;
    } else {
        cuStorageHdl->cuFileSize = (int64_t)cuStorageHdl->maxCuPointer;
    }

    return GMERR_OK;
}

static Status CuSubDirPathProc(const char *cuSubDirPath, const char *cuTrimFilePath, bool isRemoveCuDir,
    bool isLogicTblDir, size_t *removedFileSize)
{
    bool isFileExist = DbFileExist(cuSubDirPath);
    bool isDirExist = DbDirExist(cuSubDirPath);
    *removedFileSize = 0;
    if (isLogicTblDir) {
        if (isFileExist || isDirExist) {
            DB_LOG_WARN(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
                "Unable to remove tblSpcDir because its subdirectories are not empty.");
            return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
        }
    }
    Status ret = GMERR_OK;
    if (!isRemoveCuDir) {  // Trim 分支
        uint32_t fileLen = (uint32_t)strlen(cuSubDirPath);
        uint32_t trimFileLen = (uint32_t)strlen(cuTrimFilePath);
        if (fileLen < trimFileLen) {  // 目标文件比被裁剪的文件名短，说明需要保留，比如 cuFile_xxx.x 与 cuFile_xxx.xx
            return GMERR_OK;
        } else if (fileLen == trimFileLen) {
            if (strcmp(cuSubDirPath, cuTrimFilePath) <= 0) {  // 目标文件名的序号比被裁剪的文件名序号小或相等，不处理
                return GMERR_OK;
            }
        }
        // 至此说明此文件需要被直接删除
    }
    if (isFileExist) {
        DbFileSize(cuSubDirPath, removedFileSize);
        ret = DbRemoveFileNoLog(cuSubDirPath);
        if (ret != GMERR_OK) {
            DB_LOG_WARN(ret, "Unable to remove single file:(%s).", cuSubDirPath);
            return ret;
        }
    }
    return ret;
}

/**
 * @brief
 * 删除物理表（时序分区表）目录或逻辑表目录，删除物理表目录时可为非空，不允许直接删除非空逻辑表目录（需提前删除其子目录）
 * Notes: 物理表目录中无嵌套子目录，不递归删除。且不判断单文件删除的结果，否则会终止整个删除过程
 * @param[in] cuDirPath : 待删除的完整Cu目录路径，可为物理表表或逻辑表目录路径
 * @param[in] isRemoveCuDir :
 * 是否为裁剪分区目录，false表示trim，true表示remove；前者当前仅用于写数据回滚流程，此变量为false时。isLogicTblDir为false
 * @param[in] isLogicTblDir : 标记是否为逻辑表目录，若为true且目录非空，则不删除直接return
 * @param[out] removedFileSize : 被删除的文件大小
 */
static void RemoveOrTrimCuDirImpl(const char *cuDirPath, const char *cuTrimFilePath, bool isRemoveCuDir,
    bool isLogicTblDir, uint64_t *removedFileSize)
{
    if (!DbDirExist(cuDirPath)) {
        return;
    }
    DbDIRT *cuDir = NULL;
    Status ret = DbOpenDir(cuDirPath, &cuDir);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DIRECTORY_OPERATE_FAILED, "Unable to open Cu directory path:(%s).", cuDirPath);
        return;
    }
    char cuSubDirPath[PATH_MAX] = {0};
    int32_t err = snprintf_s(cuSubDirPath, sizeof(cuSubDirPath), sizeof(cuSubDirPath) - 1, "%s", cuDirPath);
    if (err < 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Unable to get subDirPath.");
        DbCloseDir(cuDir);
        return;
    }
    const uint32_t len = (uint32_t)strlen(cuSubDirPath);
    *removedFileSize = 0;
    while ((ret = DbReadDir(cuDir, cuSubDirPath + len, PATH_MAX - len)) == GMERR_OK) {
        if (strcmp(cuSubDirPath + len, ".") == 0 || strcmp(cuSubDirPath + len, "..") == 0) {
            continue;
        }
        // If logical table directory contains file or directories, stop removing
        // Don't care if one file remove successfully, or it will stop the whole removing progress
        size_t removedSize = 0;
        ret = CuSubDirPathProc(cuSubDirPath, cuTrimFilePath, isRemoveCuDir, isLogicTblDir, &removedSize);
        if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
            DB_LOG_WARN(ret, "Unable to remove not empty tblSpcDir. cuSubDirPath:(%s).", cuSubDirPath);
            DbCloseDir(cuDir);
            return;
        }
        *removedFileSize += (uint64_t)removedSize;
    }
    // GMERR_NO_DATA means all files in cuDirPath are removed successfully
    if (ret != GMERR_NO_DATA) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to read file path. cuDirPath:(%s).", cuDirPath);
    }
    if (isRemoveCuDir) {
        // Remove empty directory finally
        ret = DbRemoveFileNoLog(cuDirPath);
        if (ret != GMERR_OK) {
            DB_LOG_WARN(ret, "remove Cu directory. cuDirPath:(%s).", cuSubDirPath);
        }
    }
    DbCloseDir(cuDir);
}

/**
 * @brief 多线程删除目录有三种场景：
 * 1. 若多个线程同时删除物理表目录，第一个线程会删除成功，后续线程将为空操作
 * 2. 若一个线程请求删除物理表目录，另一个线程请求删除逻辑表目录，由于逻辑表目录非空，逻辑表目录删除请求会失败
 * 3. 若多个线程同时删除空逻辑表，第一个线程会删除成功，后续线程删除将为空操作
 * @param[in] cuStorageKey : Cu的逻辑表和物理表Id
 * @param[in] cuDirLock : 用于并发控制的锁
 * @param[out] removedSize : 被删除的目录中Cu文件总大小
 */
void RemoveCuDir(CuStorageKeyT *cuStorageKey, DbSpinLockT *cuDirLock, uint64_t *removedSize)
{
    DB_POINTER2(cuStorageKey, cuDirLock);
    char cuDirPath[PATH_MAX] = {0};
    Status ret = BuildCuDirPath(cuDirPath, cuStorageKey->tblSpcId, cuStorageKey->tblId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to build Cu directory path.");
        return;
    }
    DbSpinLock(cuDirLock);
    RemoveOrTrimCuDirImpl(cuDirPath, NULL, true, cuStorageKey->tblId == INVALID_TBL_ID, removedSize);
    DbSpinUnlock(cuDirLock);
}

//  裁剪cstore分区文件，用于写数据的事务回滚流程
void TrimCuDir(CuStorageKeyT *cuStorageKey, DbSpinLockT *cuDirLock, uint64_t cuPointer, uint64_t *removedSize)
{
    DB_POINTER3(cuStorageKey, cuDirLock, removedSize);
    DbSpinLock(cuDirLock);
    int32_t fd = DB_INVALID_FD;
    char trimFilePath[PATH_MAX] = {0};
    Status ret = PrepareTblDirPath(trimFilePath, cuStorageKey->tblSpcId, cuStorageKey->tblId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to prepare physical table dirPath.");
        goto EXIT;
    }
    ret = GetCuFilePath(trimFilePath, cuStorageKey->tblId, cuPointer);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to get Cu filePath.");
        goto EXIT;
    }
    if (DbFileExist(trimFilePath)) {
        size_t trimRemovedFileSize = 0;
        DbFileSize(trimFilePath, &trimRemovedFileSize);
        ret = DbAdptOpen(trimFilePath, FILE_OPEN_FLAG, FILE_PERM, &fd);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to open Cu file: (%s)", g_threadLocalCuFd.cuFilePath);
            goto EXIT;
        }
        uint32_t offset = (uint32_t)GetCuFileOffset(cuPointer);
        ret = DbAdptFtruncate(fd, offset);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
        trimRemovedFileSize -= offset;
        *removedSize += trimRemovedFileSize;
    }
    char dirPath[PATH_MAX] = {0};
    ret = BuildCuDirPath(dirPath, cuStorageKey->tblSpcId, cuStorageKey->tblId);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    RemoveOrTrimCuDirImpl(dirPath, trimFilePath, false, false, removedSize);
EXIT:
    if (fd != DB_INVALID_FD) {
        DbCloseFile(fd);
    }
    DbSpinUnlock(cuDirLock);
}

Status BuildCuStorageHdl(DbMemCtxT *memCtx, CuStorageKeyT *cuStorageKey, CuStorageHdlT **cuStorageHdl)
{
    DB_POINTER3(memCtx, cuStorageKey, cuStorageHdl);
    Status ret = GMERR_OK;
    *cuStorageHdl = NULL;
    CuStorageHdlT *newHandle = (CuStorageHdlT *)DbDynMemCtxAlloc(memCtx, sizeof(CuStorageHdlT));
    if (newHandle == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to allocate CuStorageHdl entry.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(newHandle, sizeof(CuStorageHdlT), 0, sizeof(CuStorageHdlT));
    newHandle->memCtx = memCtx;
    newHandle->cuStorageKey.tblSpcId = cuStorageKey->tblSpcId;
    newHandle->cuStorageKey.tblId = cuStorageKey->tblId;
    newHandle->refCnt = 0;
    newHandle->rowCnt = 0;
    newHandle->maxCuId = CU_ID_START;
    newHandle->maxCuPointer = CU_POINTER_START;
    newHandle->cuFileSize = 0;
    DbSpinInit(&newHandle->cuIdLock);
    DbSpinInit(&newHandle->cuPointerLock);
    DbSpinInit(&newHandle->cuMetaFileLock);
    DbSpinInit(&newHandle->cuFileLock);
    DbSpinInit(&newHandle->insertLock);
    DbRWSpinInit(&newHandle->cuMergeLock);

    // Only tblSpcId and tblId are both valid, it will create cuMetaFile if absent
    if (cuStorageKey->tblSpcId != INVALID_TBL_SPC_ID && cuStorageKey->tblId != INVALID_TBL_ID) {
        ret = PrepareCuMetaFile(newHandle);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to create cuMetaFile.");
            DbDynMemCtxFree(memCtx, newHandle);
            return ret;
        }
        ret = LoadCuMeta(newHandle);
        if (ret != GMERR_OK) {
            ResetThreadLocalCuMetaFd();
            DB_LOG_ERROR(ret, "Unable to get meta data from cuMetaFile.");
            DbDynMemCtxFree(memCtx, newHandle);
            return ret;
        }
    }
    *cuStorageHdl = newHandle;
    return ret;
}

SO_EXPORT_FOR_TS Status BuildCuFilePath(char *pathBuf, uint32_t tblSpcId, uint32_t tblId, int64_t cuPointer)
{
    DB_POINTER(pathBuf);
    Status ret = PrepareTblDirPath(pathBuf, tblSpcId, tblId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to prepare physical table dirPath.");
        return ret;
    }

    ret = GetCuFilePath(pathBuf, tblId, cuPointer);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to get Cu filePath.");
        return ret;
    }
    return ret;
}

#ifdef __cplusplus
}
#endif
