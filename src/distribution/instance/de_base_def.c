/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: distribution component function init.
 * Author: liufuchenxing
 * Create: 2024-02-17
 */

#include "de_base_def.h"

#include "de_awareness_pub.h"
#include "de_equip_info.h"
#include "de_instance.h"
#include "de_oplog_pub.h"
#include "de_sync_pub.h"
#include "dm_shared_obj.h"
#include "dm_shared_struct.h"
#include "ee_mini_doc_obj.h"
#include "ee_mini_sequence.h"
#include "ee_mini_shared_obj.h"
#include "ee_operation_history.h"
#include "ee_shared_obj.h"
#include "systable_de_inner.h"

Status DistributionAmFuncInit(DbInstanceHdT dbIns)
{
    DeInstanceAmInit();
    DeSyncAmInit();
    DeOplogAmInit();
    DeEquipIdAmInit();
    DmSharedObjAmFuncInit();
    DmSharedStructAmFuncInit();
    SysTblDeInit();
    MiniSequenceAmFuncInit();
    MiniSharedObjAmFuncInit();
    MiniDocObjAmFuncInit();
    MiniEEShareObjAmFuncInit();
    OpHistoryAmInit();
    DeAwarenessAmInit();
    return GMERR_OK;
}
