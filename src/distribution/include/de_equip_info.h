/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: provides equip id manager for distribution module.
 * Author: liufuchenxing
 * Create: 2024-02-17
 */

#ifndef DE_EQUIP_INFO_H
#define DE_EQUIP_INFO_H

#include "adpt_types.h"
#include "gmc_errno.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifndef GME_DISTRIBUTION_EXPORT
#define GME_DISTRIBUTION_EXPORT __attribute__((visibility("default")))
#endif

#define MAX_EQUIP_ID_LENGTH 128
// Will get from registered method after negotiation with test mates.
#define TMP_DEFAULT_EQUIP_ID "default"

typedef const char *(*EquipIdGetFuncT)(void);

typedef struct {
    Status (*deEquipIdRegister)(EquipIdGetFuncT func);
    const char *(*deEquipIdGet)(void);
    Status (*deEquipIdCheck)(void);
} DeEquipIdAmT;

void DeEquipIdSetAmFunc(const DeEquipIdAmT *am);

void DeEquipIdAmInit(void);

Status DeEquipIdRegister(EquipIdGetFuncT func);

GME_DISTRIBUTION_EXPORT const char *DeEquipIdGet(void);

Status DeEquipIdCheck(void);

#ifdef __cplusplus
}
#endif

#endif /* DE_EQUIP_INFO_H */
