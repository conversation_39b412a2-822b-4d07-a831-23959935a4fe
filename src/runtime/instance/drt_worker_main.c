/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: drt_instance_inner.c
 * Description: .c file for runtime instance
 * Author: gaohaiyang
 * Create: 2020-08-05
 */

#include <sys/timerfd.h>
#include "drt_instance_inner.h"
#include "drt_abnormity.h"
#include "drt_pipe.h"
#include "adpt_process_name.h"
#include "adpt_string.h"
#include "adpt_rdtsc.h"
#include "db_rpc_msg.h"
#include "adpt_sleep.h"
#include "drt_view.h"
#include "sysview_instance.h"
#include "drt_log.h"
#include "db_sysapp_context.h"
#include "drt_common.h"
#include "drt_connection_inner.h"
#include "drt_flow_control_inner.h"
#include "drt_recv_agent.h"
#include "drt_schedule.h"
#include "drt_send_agent.h"
#include "db_instance.h"
#include "drt_instance.h"

#define SIM_CHECK_PID_PERIOD 30
static void DrtWorkerPoolLsnrProc(
    DrtInstanceT *instance, WorkerMgrT *workerMgr, WorkerPoolT *workerPool, uint16_t workerId)
{
    DB_POINTER2(workerMgr, workerPool);
#ifdef HPE_SIMULATION
    uint64_t beginTime = DbRdtsc();
#endif
    DbPipeMonitorT *monitor = workerMgr->reqMonitor;
    (void)DbAtomicInc16(&workerPool->busyNum);
    (void)DbAtomicDec16(&workerPool->freeNum);
    while (!DrtWorkerIsStopping(workerMgr, workerId) && !WorkerPoolIsStopping(workerPool)) {
        DbPipeMonitorProc(monitor, MONITOR_RECV_TIMEOUT_MS);  // RaRequestEventProc
#ifdef HPE_SIMULATION
        uint64_t currTime = DbRdtsc();
        if (DbToSeconds(currTime - beginTime) >= SIM_CHECK_PID_PERIOD) {
            DrtCheckPidProc(instance);
            beginTime = DbRdtsc();
        }

#endif
    }
    (void)DbAtomicDec16(&workerPool->busyNum);
    (void)DbAtomicInc16(&workerPool->freeNum);
    workerPool->lsnrWorkerId = DB_INVALID_UINT16;
}

ALWAYS_INLINE static void DrtWorkerPoolWorkerProc(
    WorkerMgrT *workerMgr, WorkerPoolT *workerPool, uint16_t workerId, WorkerTaskCtxT *taskCtx)
{
    (void)DbAtomicInc16(&workerPool->busyNum);
    (void)DbAtomicDec16(&workerPool->freeNum);
    DrtWorkerUpdateStat(workerMgr, workerId, true);
    DrtSetThreadMsgFromConnMgr(workerMgr->workerType, taskCtx);
    taskCtx->procEntry(taskCtx->ctx, taskCtx->task);  // SmScheProcWrapper
    DbLogClearThreadMsgFromConnMgr();
    DrtWorkerUpdateStat(workerMgr, workerId, false);
    (void)DbAtomicDec16(&workerPool->busyNum);
    (void)DbAtomicInc16(&workerPool->freeNum);
}

#define TRY_PULL_TASK_COUNT 20
static void DrtWorkerPoolProc(WorkerT *worker, WorkerMgrT *workerMgr, WorkerPoolT *workerPool, uint16_t workerId)
{
    WorkerTaskListT *taskList = workerMgr->taskList;
    WorkerTaskCtxT *taskCtx = NULL;
    bool justProcFinished = false;
    uint16_t tryCount = TRY_PULL_TASK_COUNT;
    uint64_t lastProcTime = DbRdtsc();
    Status ret = GMERR_INTERNAL_ERROR;  // 初始代表不是timeWait触发的

    while (!DrtWorkerIsStopping(workerMgr, workerId) && !WorkerPoolIsStopping(workerPool)) {
        if (SECUREC_LIKELY(!DrtWorkerTaskListIsEmpty(taskList))) {
            tryCount = TRY_PULL_TASK_COUNT;
            taskCtx = DrtWorkerTaskListPull(taskList);
            if (SECUREC_LIKELY(taskCtx != NULL)) {
                ret = (ret == GMERR_OK) ? GMERR_INTERNAL_ERROR : DbSemTryWait(&workerPool->sem);
                ret = GMERR_INTERNAL_ERROR;
                DrtWorkerPoolWorkerProc(workerMgr, workerPool, workerId, taskCtx);
                justProcFinished = true;
                continue;
            }
        }

        if (tryCount > 0) {
            tryCount--;
            continue;
        }
        tryCount = TRY_PULL_TASK_COUNT;
        uint64_t curTime = DbRdtsc();
        lastProcTime = justProcFinished ? curTime : lastProcTime;
        justProcFinished = false;
        ret = DbSemTimedWait(&workerPool->sem, WORKER_PEND_TIMEOUTMS * USECONDS_IN_MSECOND);
        // 接收信号量超时才会判断是否要回收worker
        if (ret != GMERR_OK && TryReduceWorker(workerPool, worker, lastProcTime)) {
            return;
        }
    }
}

static Status DrtSeWorkerPoolCreateMemCtx(WorkerMgrT *workerMgr, uint16_t workerId, DbMemCtxT **usrCtx)
{
    DB_POINTER(usrCtx);
    DbMemCtxArgsT args = {0};
    args.collectAllocSizeOnThisTree = false;
    args.noLimited = true;
    char ctxName[DB_MAX_NAME_LEN] = {0};
    int32_t err = snprintf_s(ctxName, DB_MAX_NAME_LEN, DB_MAX_NAME_LEN - 1, "SEWORKER_%" PRIu16 "_MEMCTX", workerId);
    if (err < 0) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW,
            "Init worker name unsucc, workerId: %" PRIu16 ", os no %" PRId32 ", errMsg %s.", workerId, (int32_t)errno,
            strerror(errno));
        return GMERR_FIELD_OVERFLOW;
    }
    *usrCtx = (DbMemCtxT *)DbCreateDynMemCtx(
        (DbMemCtxT *)DbGetTopDynMemCtx(DbGetInstanceByMemCtx(workerMgr->memCtx)), false, ctxName, &args);
    if (*usrCtx == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Create memCtx for worker %" PRIu16 " unsucc.", workerId);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static void DrtSeWorkerPoolProc(WorkerT *worker, WorkerMgrT *workerMgr, WorkerPoolT *workerPool, uint16_t workerId)
{
    WorkerTaskListT *seTaskList = workerMgr->seTaskList;
    WorkerTaskCtxT *taskCtx = NULL;
    bool justProcFinished = false;
    uint64_t lastProcTime = DbNowMilliSec();
    uint16_t tryCount = TRY_PULL_TASK_COUNT;
    Status ret = GMERR_INTERNAL_ERROR;  // 初始代表不是timeWait触发的
    TupleBufInit(&worker->cacheBuf, workerPool->memCtx);

    DbMemCtxT *usrCtx = NULL;
    if (DrtSeWorkerPoolCreateMemCtx(workerMgr, workerId, &usrCtx) != GMERR_OK) {
        return;
    }
    DbMemCtxSwitchTo(usrCtx);
    TupleBufInit(&worker->cacheBuf, usrCtx);

    while (true) {
        justProcFinished = false;
        for (uint32_t i = 0; i < SE_TASK_LIST_NUM; i++) {
            if (SECUREC_LIKELY((taskCtx = DrtWorkerTaskListPull(&seTaskList[i])) != NULL)) {
                taskCtx->worker = worker;
                DrtWorkerPoolWorkerProc(workerMgr, workerPool, workerId, taskCtx);
                justProcFinished = true;
            }
        }

        if (justProcFinished) {
            lastProcTime = DbNowMilliSec();
            tryCount = TRY_PULL_TASK_COUNT;
            continue;
        }

        if (tryCount > 0) {
            tryCount--;
            continue;
        }

        ret = DbSemTimedWait(&workerPool->sem, USECONDS_IN_MSECOND);
        // 接收信号量超时才会判断是否要回收worker
        if ((ret != GMERR_OK && TryReduceWorker(workerPool, worker, lastProcTime)) ||
            DrtWorkerIsStopping(workerMgr, workerId) || WorkerPoolIsStopping(workerPool)) {
            DB_ASSERT(DbGetCurMemCtx() == usrCtx);
            DbDeleteDynMemCtx(DbGetCurMemCtx());
            DbSetCurrMemCtxToNull();
            return;
        }
    }
}

#define WORKER_CREATE_GAP_US 1000
static void *DrtWorkerPoolMain(void *arg, uint16_t workerId)
{
    DB_POINTER(arg);
    WorkerMgrT *workerMgr = (WorkerMgrT *)arg;
    DrtInstanceT *instance = DrtGetInstance(DbGetInstanceByMemCtx(workerMgr->memCtx));
    if (instance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "workerPool,  get DrtIns, workerId:%" PRIu16 "", workerId);
        return NULL;
    }
    DbAtomicSet64(&instance->scheMgr.workerStartTime, DbToUseconds(DbRdtsc()));

    uint64_t startTime = DbAtomicGet64(&instance->scheMgr.workerStartTime);
    uint64_t exitTime = DbAtomicGet64(&instance->scheMgr.workerExitTime);
    if (startTime <= exitTime + WORKER_CREATE_GAP_US) {
        DB_LOG_WARN(GMERR_INTERNAL_ERROR,
            "Create and destroy worker freq, create %" PRIu64 " us , destroy %" PRIu64 " us.", startTime, exitTime);
    }

    WorkerPoolT *workerPool = &workerMgr->workerPool;
    WorkerT *worker = DrtGetWorker(workerMgr, workerId);
    DB_ASSERT(worker != NULL);
    (void)DbAtomicInc16(&workerPool->freeNum);

    if (workerMgr->workerType == DRT_WORKER_STORAGE) {
        DrtSeWorkerPoolProc(worker, workerMgr, workerPool, workerId);
    } else {
        if (workerPool->lsnrWorkerId == DB_INVALID_UINT16) {
            DbSpinLock(&instance->recvAgent.lock);
            if (workerPool->lsnrWorkerId == DB_INVALID_UINT16) {
                workerPool->lsnrWorkerId = workerId;
            }
            DbSpinUnlock(&instance->recvAgent.lock);
        }
        workerPool->lsnrWorkerId == workerId ? DrtWorkerPoolLsnrProc(instance, workerMgr, workerPool, workerId) :
                                               DrtWorkerPoolProc(worker, workerMgr, workerPool, workerId);
    }
    (void)DbAtomicDec16(&workerPool->freeNum);
    (void)DbAtomicDec16(&workerPool->liveNum);
    DB_LOG_INFO("Worker pool worker %" PRIu16 " exit, liveNum:%" PRIu16 " freeNum %" PRIu16 ".", worker->workerId,
        workerPool->liveNum, workerPool->freeNum);

    DbAtomicSet64(&instance->scheMgr.workerExitTime, DbToUseconds(DbRdtsc()));
    return NULL;
}

void *DrtMsgPoolAllocMain(DrtInstanceT *drtIns, uint16_t workerId)
{
    DB_POINTER(drtIns);
    DrtConnMgrT *connMgr = &drtIns->connMgr;
    SharedMsgPoolMgrT *msgPoolMgrT = connMgr->msgPoolMgr;
    bool waitFlag = true;
    uint32_t count = 0;
#if !defined(HPE_SIMULATION)
    struct timespec ts = {.tv_sec = MSG_POOL_WAIT_TIME_MAX, .tv_nsec = 0};
#endif
    while (!DrtWorkerIsStopping(&drtIns->workerMgr, workerId)) {
        DbPrintWorkerThreadRunTime("begin", "DrtMsgPoolAllocMain", count, INTERVAL_COUNT);
        if (waitFlag) {
#if !defined(HPE_SIMULATION)
            (void)DbNdWait(msgPoolMgrT->nd, &ts, DHF_NOTIFY_BLOCK);
#else
            (void)DbSemTimedWait(msgPoolMgrT->sem, MSG_POOL_WAIT_TIME_MAX);
#endif
            waitFlag = false;
        } else {
            DrtWorkerUpdateStat(&drtIns->workerMgr, workerId, true);
            DrtMsgPoolAllocForConn(msgPoolMgrT, connMgr);
            DrtWorkerUpdateStat(&drtIns->workerMgr, workerId, false);
            waitFlag = true;
        }
        DbPrintWorkerThreadRunTime("end", "DrtMsgPoolAllocMain", count, INTERVAL_COUNT);
        count++;
    }
    return NULL;
}

void *DrtServiceWorkerMain(void *arg, uint16_t workerId)
{
    DB_POINTER(arg);

    SmScheProcCtxT *scheProcCtx = (SmScheProcCtxT *)arg;
    DrtConnectionT *conn = scheProcCtx->conn;
    DB_POINTER2(conn, conn->drtPipe);

    DrtSetThreadMsg(conn);  // 设置日志头对端信息
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE,
            "stage1,  main service worker get rt ins . conn:%" PRIu16 ",peerInfo: %" PRIu32 "-%s", conn->id, conn->pid,
            conn->auditUserInfo);
        return NULL;
    }

    WorkerT *worker = DrtGetWorker(&drtIns->workerMgr, workerId);
    if (worker == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE,
            "stage1, main service worker get worker id %" PRIu16 ". conn:%" PRIu16 ",peerInfo: %" PRIu32 "-%s",
            workerId, conn->id, conn->pid, conn->auditUserInfo);
        return NULL;
    }

    DB_LOG_DEBUG("worker %" PRIu16 " with conn %" PRIu16 " pipe %" PRIu16 " running. peerInfo: %" PRIu32 "-%s",
        workerId, conn->id, conn->drtPipe->pipeId, conn->pid, conn->auditUserInfo);

    // [worker创建结束, worker被唤醒调度]，超过1000ms则记录日志
    uint64_t timeUsed = DbToMseconds(DbRdtsc() - worker->workerStat.workerCreateEnd);
    if (timeUsed > MSECONDS_IN_SECOND) {
        DB_LOG_WARN(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "create worker to sch takes too long(ms): %" PRIu64 ". id %" PRIu16 ", conn:%" PRIu16 ",peerInfo: %" PRIu32
            "-%s",
            timeUsed, workerId, conn->id, conn->pid, conn->auditUserInfo);
    }
    if (DbPipeSetNoBlock(&conn->drtPipe->dbPipe, false) != GMERR_OK) {
        DB_LOG_ERROR(GMERR_CONNECTION_EXCEPTION, "worthless to set block, conn:%" PRIu16 ".", conn->id);
        return NULL;
    }
    DrtServiceWorkerProc(drtIns, scheProcCtx, conn, conn->drtPipe, workerId);

    DB_LOG_DEBUG("woker %" PRIu16 " prepared exit, conn %" PRIu16 ", conn stat %" PRIu8 ", exit: %" PRIu8
                 ", peerInfo: %" PRIu32 "-%s",
        workerId, conn->id, (uint8_t)conn->status, (uint8_t)conn->exitStatus, conn->pid, conn->auditUserInfo);
    // step3: 退出自动detach
    DrtDetachConnection(conn);  // groupRef = 0
    return NULL;
}

void *DrtLsnrWorkerMain(void *arg, uint16_t workerId)
{
    DB_POINTER(arg);
    DrtInstanceT *drtIns = (DrtInstanceT *)arg;
    DrtRecvAgentT *ra = &drtIns->recvAgent;
    DB_POINTER(ra);
    DrtPipeListT *pipeList = &drtIns->connMgr.pipeList;
    DrtPipeT *lsnrPipe = DrtGetPipe(pipeList, pipeList->lsnrPipeId);
    if (lsnrPipe == NULL) {
        RaDestroy(ra);
        return NULL;
    }
    // 等待DB所有模块初始化成功后，再起Listener线程
    // 解决IOT设备上DB启动过慢导致的runtime偶现core问题
    uint64_t initTime = DbRdtsc();
    while (!DrtWorkerIsStopping(ra->workerMgr, workerId) && !ra->initComplete) {
        DbSleep(WAIT_TIME);
    }
    if (!ra->initComplete) {
        RaDestroy(ra);
        return NULL;
    }
    DB_LOG_WARN(GMERR_OK, "INFO: Lsnr worker begin sch. initTime: %" PRIu64 ".", DbToMseconds(DbRdtsc() - initTime));
    uint32_t count = 0;
    while (!DrtWorkerIsStopping(ra->workerMgr, workerId)) {
        DbPrintWorkerThreadRunTime("begin", "DrtLsnrWorkerMain", count, INTERVAL_COUNT);
        DrtWorkerUpdateStat(ra->workerMgr, workerId, true);
        RaLsnrProcess(ra, lsnrPipe);
#ifdef EXPERIMENTAL_NERGC
        DrtCheckTcpTimeout(ra, &drtIns->connMgr);
#endif
        DrtWorkerUpdateStat(ra->workerMgr, workerId, false);
        DbPrintWorkerThreadRunTime("end", "DrtLsnrWorkerMain", count, INTERVAL_COUNT);
        count++;
    }
    DB_LOG_WARN(GMERR_OK, "Lsnr worker prepare exit.");
    RaDestroy(ra);
    return NULL;
}

#define DML_THREADHOLD_TIME_US (1000000)
#define LISTEN_TOO_LONG_MS 1000
void *DrtSingleWorkerMain(DrtInstanceT *drtIns, uint16_t workerId)
{
    DB_POINTER(drtIns);
    WorkerMgrT *workerMgr = &drtIns->workerMgr;
    DbPipeMonitorT *monitor = workerMgr->reqMonitor;
    WorkerTaskListT *taskList = workerMgr->taskList;
    WorkerTaskCtxT *taskCtx = NULL;
    uint64_t procUsec = 0, cycleStart = 0;
    int32_t timeOut = CS_RECV_TIMEOUT_MS;
    uint64_t lastListenTime = DbRdtsc();
    while (!DrtWorkerIsStopping(workerMgr, workerId)) {
        cycleStart = DbRdtsc();
        timeOut = (DrtWorkerTaskListIsEmpty(taskList)) ? DB_MIN(timeOut + 1, CS_RECV_TIMEOUT_MS) : 1;
        uint64_t duration = DbToMseconds(cycleStart - lastListenTime);
        if (duration >= LISTEN_TOO_LONG_MS) {
            DB_LOG_WARN(GMERR_INTERNAL_ERROR,
                "curr listen, cost too long %" PRIu64 " ms, high task %" PRIu16 ", normal task %" PRIu16 ".", duration,
                taskList[TASK_PRIORITY_HIGH].count, taskList[TASK_PRIORITY_NORMAL].count);
        }
        DbSpinLock(&drtIns->recvAgent.lock);  // 防止二阶段add event后发生reset by peer，提前处理导致并发
        DbPipeMonitorProc(monitor, timeOut);
        DbSpinUnlock(&drtIns->recvAgent.lock);
        lastListenTime = DbRdtsc();
        procUsec = 0;
        while (!DrtWorkerTaskListIsEmpty(taskList) && procUsec < DML_THREADHOLD_TIME_US &&
               !DrtWorkerIsStopping(workerMgr, workerId)) {
            taskCtx = DrtWorkerTaskListPull(taskList);
            DB_ASSERT(taskCtx != NULL);
            DrtWorkerUpdateStat(workerMgr, workerId, true);
            taskCtx->procEntry(taskCtx->ctx, taskCtx->task);
            DrtWorkerUpdateStat(workerMgr, workerId, false);
            taskCtx = NULL;
            procUsec = DbToUseconds(DbRdtsc() - cycleStart);
        }
    }
    return NULL;
}

DrtInitWorkerMgrParaT DrtCreateWorkerMgrParas(DbMemCtxT *memCtx, uint16_t connMaxNum, WorkerTypeE type)
{
    DB_POINTER(memCtx);
    DrtInitWorkerMgrParaT workerMgrPara = {0};

    workerMgrPara.scheMode = (type == DRT_WORKER_STORAGE) ?
                                 SCHEDULE_THREAD_POOL :
                                 (ScheduleModeE)DbCfgGetInt32Lite(DB_CFG_SCHEDULE_MODE, DbGetInstanceByMemCtx(memCtx));
    workerMgrPara.memCtx = memCtx;
    workerMgrPara.connMaxNum = connMaxNum;
    workerMgrPara.workerType = type;
    switch (workerMgrPara.scheMode) {
        case SCHEDULE_DIRECTELY:
            workerMgrPara.entry = (DrtWorkerEntry)DrtServiceWorkerMain;
            break;
        case SCHEDULE_THREAD_POOL:
            workerMgrPara.entry = (DrtWorkerEntry)DrtWorkerPoolMain;
            break;
        case SCHEDULE_INVALID:
        case SCHEDULE_BOTTOM:
            DB_LOG_EMRG(
                GMERR_INVALID_PARAMETER_VALUE, "inv schedule mode %" PRIu32 "", (int32_t)workerMgrPara.scheMode);
            break;
        default:
            DB_ASSERT(0);  // 不存在的 schedule mode
            break;
    }

    return workerMgrPara;
}
