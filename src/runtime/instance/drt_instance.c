/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: drt_instance_inner.c
 * Description: .c file for runtime instance
 * Author: gaohaiyang
 * Create: 2020-08-05
 */

#include <sys/timerfd.h>
#include "drt_instance_inner.h"
#include "drt_abnormity.h"
#include "drt_pipe.h"
#include "adpt_process_name.h"
#include "adpt_string.h"
#include "adpt_rdtsc.h"
#include "db_rpc_msg.h"
#include "adpt_sleep.h"
#include "drt_view.h"
#include "sysview_instance.h"
#include "drt_log.h"
#include "db_sysapp_context.h"
#include "drt_common.h"
#include "drt_connection_inner.h"
#include "drt_flow_control_inner.h"
#include "drt_recv_agent.h"
#include "drt_schedule.h"
#include "drt_send_agent.h"
#include "db_instance.h"
#include "drt_instance.h"

#ifdef SHUTDOWN
typedef enum ServerRunStatus { SERVER_STATUS_STOPPED = 0, SERVER_STATUS_RUNNING, SERVER_STATUS_BUTT } ServerRunStatusE;
// 表征server运行状态
static ServerRunStatusE g_serverStatus = SERVER_STATUS_STOPPED;
#endif

Status DrtGetServerModeVersion(uint32_t *version, DbInstanceHdT dbInstance)
{
    DB_POINTER(version);
    PrivServerModeT *mode = DrtGetServerMode(dbInstance);
    if (mode == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get srv mode when get mode ver");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *version = mode->version;
    return GMERR_OK;
}

void DrtRegisterFlowCtrlShmMemUsage(DrtInstanceT *drtInst, DrtFlowControlShmMemUsage shmMemUsage, void *userCtx)
{
    DB_POINTER3(drtInst, shmMemUsage, userCtx);
    drtInst->flowCtrlInfo.shmMemUsage = shmMemUsage;
    drtInst->flowCtrlInfo.userCtx = userCtx;
}

bool DrtKeepThreadAlive(uint16_t workerId)
{
    DrtInstanceT *instance = DrtGetInstance(NULL);
    if (instance == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "get rt instance when keep thread alive.");
        return false;
    }
    bool isStopping = DrtWorkerIsStopping(&instance->workerMgr, workerId);
    if (isStopping) {
        DB_LOG_WARN(GMERR_INTERNAL_ERROR, "keep thread(%" PRIu16 ") alive.", workerId);
    }
    return !isStopping;
}

bool DrtKeepThisWorkerAlive(DbInstanceHdT dbInstance)
{
    DrtInstanceT *instance = DrtGetInstance(dbInstance);
    if (instance == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "get rt instance when keep worker alive.");
        return false;
    }
    bool isStopping = DrtWorkerIsStopping(&instance->workerMgr, GetThisWorkerId());
    if (isStopping) {
        DB_LOG_WARN(GMERR_INTERNAL_ERROR, "keep thread(%" PRIu16 ") alive.", GetThisWorkerId());
    }
    return !isStopping;
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
Status DrtConnRsp(DrtConnectionT *conn, FixBufferT *rsp)
{
    DB_POINTER2(conn, rsp);
    if (SECUREC_UNLIKELY(DrtConnFlagBindChannel(conn))) {
        DrtInstanceT *drtIns = DrtGetInstance(NULL);
        if (drtIns == NULL) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get instance when rsp");
            DrtFreeMsg(rsp);
            return GMERR_INTERNAL_ERROR;
        }
        Status ret = SaPushCtrlMsgByConnId(&drtIns->sendAgent, conn->id, rsp);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret,
                "Send msg, pipe %" PRIu16 ", conn %" PRIu16 ", peer pid %" PRIu32 ", pos %" PRIu32 ", seekpos %" PRIu32
                ", total %" PRIu32 ", stat %" PRId32 ", flag %" PRIu32 ".",
                conn->drtPipe->pipeId, conn->id, conn->pid, FixBufGetPos(rsp), FixBufGetSeekPos(rsp),
                FixBufGetTotalLength((rsp)), (int32_t)conn->status, conn->flag);
        }
        DrtFreeMsg(rsp);
        return ret;
    }
    return DrtConnNormalRsp(conn, rsp);
}

inline static bool DrtOpNeedLog(int32_t opStatus, uint32_t operationCount, uint32_t opCode, uint8_t cltType)
{
    if (opStatus != 0 || operationCount == 0 || opCode < MSG_OP_RPC_DML_BEGIN || opCode >= MSG_OP_RPC_DML_END ||
        cltType == (uint8_t)GMC_CONN_TYPE_SUB || cltType == (uint8_t)GMC_CONN_TYPE_BUTT) {
        return false;
    }
    return true;
}

void DrtLongOpLog(DrtProcCtxT *procCtx, int32_t opStatus, uint64_t sendTime, uint64_t totalTimeMs)
{
    DB_POINTER2(procCtx, procCtx->conn);
    LongOperationInfoT *longOperationInfo = &procCtx->longOperationRecord;
    DrtConnectionT *conn = procCtx->conn;
    uint32_t opCode = longOperationInfo->longestQryInfo.opCode;
    if (SECUREC_UNLIKELY(
            totalTimeMs >= (uint64_t)conn->cltTimeoutMs && opCode != 0 &&
            (conn->nodeId.flag == (uint8_t)GMC_CONN_TYPE_SYNC || conn->nodeId.flag == (uint8_t)GMC_CONN_TYPE_ASYNC))) {
        DB_LOG_ERROR(GMERR_REQUEST_TIME_OUT,
            "op too long: conn_id: %" PRIu16 ", conn_type: %s, op_code: %s, time_threhold: %" PRIu32
            " ms, total_time: %" PRIu64 " ms.",
            conn->id, (conn->nodeId.flag == (uint8_t)GMC_CONN_TYPE_SYNC) ? "SYNC" : "ASYNC", DbGetOpCodeMsg(opCode),
            conn->cltTimeoutMs, totalTimeMs);
    }
    if (SECUREC_UNLIKELY(procCtx->longProcTimeThreshold < 0 ||
                         !DrtOpNeedLog(opStatus, longOperationInfo->operationCount, opCode, conn->nodeId.flag))) {
        return;
    }
    uint64_t totalTime =
        DbToUseconds(longOperationInfo->totalReqRecvUsedTime + longOperationInfo->totalScheduleUsedTime +
                     longOperationInfo->totalPrepareUsedTime + longOperationInfo->totalExecuteUsedTime);
    if (SECUREC_LIKELY(totalTime < ((uint64_t)(uint32_t)procCtx->longProcTimeThreshold * USECONDS_IN_MSECOND))) {
        return;
    }
    longOperationInfo->rspSendStartTime = sendTime;
    uint64_t reqRecvTime = DbToUseconds(longOperationInfo->totalReqRecvUsedTime);
    uint64_t scheduleTime = DbToUseconds(longOperationInfo->totalScheduleUsedTime);
    uint64_t prepareTime = DbToUseconds(longOperationInfo->totalPrepareUsedTime);
    uint64_t executeTime = DbToUseconds(longOperationInfo->totalExecuteUsedTime);
    QrySingleInfoT *longestQryInfo = &longOperationInfo->longestQryInfo;
    uint64_t longestPrepareTime =
        (longOperationInfo->isBatchOperation) ?
            DbToUseconds(longestQryInfo->executeStartTime - longestQryInfo->prepareStartTime) :
            prepareTime;
    uint64_t longestExecuteTime = (longOperationInfo->isBatchOperation) ?
                                      DbToUseconds(longestQryInfo->executeEndTime - longestQryInfo->executeStartTime) :
                                      executeTime;
    uint32_t operationCount = longOperationInfo->isBatchOperation ? longOperationInfo->operationCount : 1;
    DB_LOG_WARN(GMERR_CONFIGURATION_LIMIT_EXCEEDED,
        "long op log: time_threshold: %" PRId32 "ms, conn_type: %s, namespace_id: %" PRIu32
        ", table_name: \"%s\", index_name: \"%s\", op_code: %s, conn_id: %" PRIu16 "; total_time: %" PRIu64
        ", req_recv_time: %" PRIu64 ", rt_sch_time: %" PRIu64 ", qry_prepare_time: %" PRIu64 ", qry_exec_time: %" PRIu64
        ", op_count: %" PRIu32 "; longest op info: qry_prepare_time: %" PRIu64 ", qry_exec_time: %" PRIu64 "(us).",
        procCtx->longProcTimeThreshold, ((conn->nodeId.flag == (uint8_t)GMC_CONN_TYPE_SYNC) ? "SYNC" : "ASYNC"),
        longestQryInfo->namespaceId, longestQryInfo->tableName, longestQryInfo->indexName, DbGetOpCodeMsg(opCode),
        conn->id, totalTime, reqRecvTime, scheduleTime, prepareTime, executeTime, operationCount, longestPrepareTime,
        longestExecuteTime);
}

static void DrtAddExtend(DrtConnectionT *conn, FixBufferT *response)
{
#ifdef EXPERIMENTAL_NERGC
    MsgHeaderT *msgHeader = RpcPeekMsgHeader(response);
    msgHeader->sessionId = conn->tcpSessionId;
#else
    DB_UNUSED2(conn, response);
#endif
}

static void DrtCompleteResponseMsg(DrtProcCtxT *procCtx, FixBufferT *response, int32_t opStatus)
{
    DB_POINTER4(procCtx, procCtx->conn, response, response->buf);
    DrtConnectionT *conn = procCtx->conn;
    uint8_t flowCtrlLevel = DrtGetConnFlowCtrlLevel(conn);
    MsgHeaderT *reqMsgHdr = NULL;
    OpHeaderT *reqOpHdr = NULL;
    FixBufferT *request = &procCtx->msg;
    if (request->buf != NULL) {
        reqMsgHdr = RpcPeekMsgHeader(request);
        reqOpHdr = ProtocolPeekFirstOpHeader(request);
    }
    DrtAddExtend(conn, response);
    uint32_t opCode = 0;
    // 修正应答长度，当前先默认一条消息
    MsgHeaderT *msgHeader = RpcPeekMsgHeader(response);
    msgHeader->size = FixBufGetPos(response);
    msgHeader->opStatus = opStatus;
    msgHeader->flowCtrlLevel = flowCtrlLevel;
    msgHeader->seq = 0;
    msgHeader->isFinish = true;
    if (SECUREC_LIKELY(reqMsgHdr != NULL)) {
        msgHeader->serialNumber = reqMsgHdr->serialNumber;
        msgHeader->reqStartTime = reqMsgHdr->reqStartTime;
        msgHeader->stmtId = reqMsgHdr->stmtId;
        msgHeader->serviceId = reqMsgHdr->serviceId;
        msgHeader->modelType = reqMsgHdr->modelType;
    } else {
        msgHeader->serialNumber = 0;
        msgHeader->reqStartTime = 0;
        msgHeader->stmtId = 0;
    }
    OpHeaderT *rspOpHdr = ProtocolPeekFirstOpHeader(response);
    opCode = reqOpHdr == NULL ? MSG_OP_RPC_NONE : reqOpHdr->opCode;
    if (SECUREC_UNLIKELY(msgHeader->serialNumber == 0 && opCode >= MSG_OP_RPC_DDL_BEGIN)) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION,
            "serial num unexcepted. conn %" PRIu16 ", peer pid %" PRIu32 ", procCtx type %" PRIu16 ", opCode %" PRIu32
            ".",
            conn->id, conn->pid, (uint16_t)procCtx->type, opCode);
    }
    RpcFillOpHeader(
        rspOpHdr, opCode, (msgHeader->size - msgHeader->extendSize - MSG_HEADER_ALIGN_SIZE) - MSG_OP_HEADER_ALIGN_SIZE);
    msgHeader->priority = (opCode == MSG_OP_RPC_DISCONNECT) ? MSG_PRIO_HIGH : MSG_PRIO_NORMAL;
}

static void ReuseRequest(DrtConnectionT *conn, FixBufferT *request, FixBufferT *response, uint32_t flags)
{
    DB_POINTER3(conn, request, response);
    FixBufDup(request, response);
    // 复用request内容，部分字段可以不用更新
    MsgHeaderT *msgHeader = RpcPeekMsgHeader(response);
    msgHeader->opStatus = GMERR_MEMORY_OPERATE_FAILED;
    msgHeader->flowCtrlLevel = DrtGetConnFlowCtrlLevel(conn);
    msgHeader->seq = 0;
    msgHeader->isFinish = true;
    msgHeader->extendSize = 0;
    OpHeaderT *opHeader = ProtocolPeekFirstOpHeader(response);
    uint32_t opCode = opHeader->opCode;
    if (SECUREC_UNLIKELY(opCode == MSG_OP_RPC_DISCONNECT)) {
        msgHeader->priority = MSG_PRIO_HIGH;
    } else if (SECUREC_UNLIKELY(opCode == MSG_OP_RPC_HEARTBEAT)) {
        msgHeader->priority = MSG_PRIO_NORMAL;
    }
    response->flags = flags;
}

void DrtAllocResponse(DrtConnectionT *conn, FixBufferT *request, FixBufferT *response)
{
    DB_POINTER2(conn, response);
    Status ret = GMERR_OK;
    bool cacheBufCanUse = !FixBufIsEmpty(&conn->rspCacheBuf) && !DrtConnFlagIsResendRsp(conn);
    if (SECUREC_LIKELY(cacheBufCanUse)) {
        DB_ASSERT(!DrtConnFlagIsResendRsp(conn));
        FixBufMove(&conn->rspCacheBuf, response);
    } else {
        // 如果是大报文且是hpe环境，需要将response的fixbuffer切换为动态内存
        uint32_t flags = FixBufGetFlags(response) |
                         (DrtConnFlagIsLargeObj(conn) ? FIX_BUF_FLAG_LOB_BUFFER : FIX_BUF_FLAG_EXTEND_BUFFER);
        DbMemCtxT *msgMemCtx =
            (DrtConnFlagIsAsyncLargeObj(conn) && DbIsHpeEnv()) ? conn->connMgr->memctx : conn->msgMemCtx;
        ret = FixBufCreate(response, msgMemCtx, CS_PACK_SIZE, flags);
        if (SECUREC_UNLIKELY((ret != GMERR_OK))) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED,
                "DRT alloc msg buffer, totalSize is %" PRIu16 ", conn id %" PRIu16 ", conn flag %" PRIu32 ", "
                "conn exitStatus %" PRIu32 ", "
                "request is %s, request buf is %s",
                CS_PACK_SIZE, conn->id, conn->flag, (uint32_t)conn->exitStatus, (request != NULL) ? "exists" : "NULL",
                (request != NULL && request->buf != NULL) ? "exists" : "NULL");
            // 内存不足也要保证有应答，因此需要复用req内存作为rsp,req不能为NULL，由上层保证(除异常断连外)
            if (request == NULL || FixBufGetBuf(request) == NULL) {
                return;
            }
            ReuseRequest(conn, request, response, flags);
        }
    }
    FixBufInitPut(response, MSG_HEADER_ALIGN_SIZE);
    ret = RpcReserveOpHeader(response, NULL, NULL);
    if (ret != GMERR_OK && response != request) {
        FixBufRelease(response);
        uint32_t flags = DrtConnFlagIsLargeObj(conn) ? FixBufGetFlags(response) | FIX_BUF_FLAG_LOB_BUFFER :
                                                       FixBufGetFlags(response) | FIX_BUF_FLAG_EXTEND_BUFFER;
        ReuseRequest(conn, request, response, flags);  // 复用request内存时可以不reserve op header，保证有此空间
    }
    // 消息头字段赋值
    MsgHeaderT *head = RpcPeekMsgHeader(response);
    head->protocolVersion = MSG_PROTOCOL_VERSION_PRIVATE;
    head->msgMagicNum = MSG_VERIFY_NUMBER;
    head->opNum = 0;
    head->extendSize = 0;
}

Status DrtComplete(DrtProcCtxT *procCtx, FixBufferT *response, int32_t opStatus)
{
    DB_POINTER2(procCtx, procCtx->conn);
    DrtConnectionT *conn = procCtx->conn;
    FixBufferT *request = &procCtx->msg;
    Status ret = GMERR_OK;

    if (response != NULL && response->buf != NULL && procCtx->type != PROC_CTX_TYPE_NO_RESP) {
        uint64_t sendTime = DbGlobalRdtsc();
        uint64_t totalTimeMs = DbToMseconds(sendTime - procCtx->longOperationRecord.reqSendStartTime);
        DrtCompleteResponseMsg(procCtx, response, opStatus);
        // 发送时，seekpos保证为0
        DB_ASSERT(FixBufGetSeekPos(response) == 0);
        ret = DrtConnRsp(conn, response);
        if (!DbIsTcp()) {
            DrtLongOpLog(procCtx, opStatus, sendTime, totalTimeMs);
        }
    } else {
        DB_LOG_DEBUG("Send rsp to cli. connId %" PRIu16 ", peer pid %" PRIu32 ", procCtx type %" PRIu16 ".", conn->id,
            conn->pid, (uint16_t)procCtx->type);
    }

    DrtFreeRequest(conn, request);
    DrtFreeProcCtx(procCtx);
    // 上层业务不感知到发送通道满，由runtime负责重发即可
    return ret == GMERR_CONNECTION_SEND_BUFFER_FULL ? GMERR_OK : ret;
}

static void CheckConnStage2TimeUsed(DrtConnectionT *conn, int32_t opStatus, Status sendRet)
{
    if (DbIsTcp()) {
        return;
    }
    DrtConnReqProcTimeT *procTime = &conn->stat.connReqProcTime;
    // [收到建连业务请求, 发送建连请求应答开始]
    uint64_t timeUsed1 = DbToMseconds(procTime->sendSecConnRspBegin - procTime->recvOneReqOkTimestamp);
    // [发送建连请求应答开始, 发送建连请求应答结束]
    uint64_t timeUsed2 = DbToMseconds(procTime->sendSecConnRspEnd - procTime->sendSecConnRspBegin);
    if (timeUsed1 > MSECONDS_IN_SECOND || timeUsed2 > MSECONDS_IN_SECOND) {
        DB_LOG_WARN(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "stage2 takes too long, connId:%" PRIu16 ", pid:%" PRIu32 ", peer:%s, time used (%" PRIu64 ",%" PRIu64
            ") ,opStat %" PRIu32 ", send ret %" PRIu32,
            conn->id, conn->pid, conn->auditUserInfo, timeUsed1, timeUsed2, opStatus, (uint32_t)sendRet);
    }
}

static Status MonitorPipe(DrtConnectionT *conn, DrtPipeT *drtPipe, DbPipeMonitorT *monitor)
{
    DbPipeMonitorEventParaT monitorEventPara = {conn, RaRequestEventProc, DB_PIPE_MONITOR_EVENT_READ};
    DbPipeSetEtEvent(&monitorEventPara);
    drtPipe->monitor = (void *)monitor;
    Status ret = DbPipeMonitorAddEvent(monitor, &drtPipe->dbPipe, &monitorEventPara);
    if (ret != GMERR_OK) {
        drtPipe->monitor = NULL;
        DB_LOG_ERROR(ret, "add pipe to monitor, connId-Type: %" PRIu16 "-%" PRIu8 ", cli pid-uid-name, %" PRIu32 "-%s",
            conn->id, (uint8_t)conn->nodeId.flag, conn->pid, conn->auditUserInfo);
        return ret;
    }
    return GMERR_OK;
}

DrtConnectionT *DrtAttachConnectionByNodeName(const char *nodeName)
{
    DB_POINTER(nodeName);
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get instance when get conn by node name.");
        return NULL;
    }
    return DrtGetAndAttachConnByNodeName(&drtIns->nodeMgr, &drtIns->connMgr, nodeName);
}

Status DrtCreateReservedConnTokens(const char *userName, const char *processName, uint16_t reservedConnNum)
{
#ifdef EXPERIMENTAL_NERGC
    DB_UNUSED3(userName, processName, reservedConnNum);
    return GMERR_OK;
#endif
    DB_POINTER2(userName, processName);
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "create reserved conn tokens get instance.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DrtConnMgrT *connMgr = &drtIns->connMgr;
    return DrtConnMgrCreateReservedToken(connMgr, userName, processName, reservedConnNum);
}

Status DrtDestroyReservedConnTokens(const char *userName, const char *processName)
{
#ifdef EXPERIMENTAL_NERGC
    DB_UNUSED2(userName, processName);
    return GMERR_OK;
#endif
    DB_POINTER2(userName, processName);
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "destroy reserved conn tokens get instance.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DrtConnMgrT *connMgr = &drtIns->connMgr;
    return DrtConnMgrDestroyToken(connMgr, userName, processName);
}
#endif /* FEATURE_SIMPLEREL */

#define DRT_INSTANCE_DYNA_MEMCTX_NAME "dynaMemCtx"

typedef struct DrtInstanceMgr {
    ShmemPtrT rtInstance[MAX_INSTANCE_NUM];
    bool isInitInstance[MAX_INSTANCE_NUM];
} DrtInstanceMgrT;

Status DrtInstanceInitConfig(DrtInstanceT *instance)
{
    DB_POINTER(instance);
    AdptEnvironmentE env = DbCommonGetEnv();

    DbCfgValueT locatorCfg;
    Status ret = DbCfgGet(DbGetCfgHandle(NULL), DB_CFG_LOCAL_LOCATOR, &locatorCfg);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "DRT, get cfg for rt instance.");
        return GMERR_DATA_EXCEPTION;
    }

    DbLctrT locatorGroup[LCTR_TYPE_NUM] = {0};
    uint32_t parseCnt;
    ret = DbLctrParseGroup(locatorGroup, ELEMENT_COUNT(locatorGroup), locatorCfg.str, &parseCnt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DRT, parse cfg for db locator.");
        return ret;
    }
    if (env == ADPT_HPE) {
        for (uint32_t i = 0; i < parseCnt && i < (uint32_t)LCTR_TYPE_NUM; ++i) {
            const DbLctrT *locator = &locatorGroup[i];
            if (locator->type == LCTR_TYPE_HPE_CHANNEL) {
                instance->selfLctr = *locator;
                return GMERR_OK;
            }
        }
    } else {
        for (uint32_t i = 0; i < parseCnt && i < (uint32_t)LCTR_TYPE_NUM; ++i) {
            const DbLctrT *locator = &locatorGroup[i];
            if (locator->type == LCTR_TYPE_USOCKET || locator->type == LCTR_TYPE_TCP) {
                instance->selfLctr = *locator;
                DbSetIsTcp(locator->type == LCTR_TYPE_TCP);
                return GMERR_OK;
            }
        }
    }
    DB_LOG_ERROR(GMERR_CONNECTION_EXCEPTION, "DRT, inv locator found for env = %" PRIu8, (uint8_t)env);
    return GMERR_CONNECTION_EXCEPTION;
}

typedef enum DrtModuleInitSeq {
    DRT_MODULE_MEMCTX = 0,
    DRT_MODULE_ABN_MGR,
    DRT_MODULE_CONN_MGR,
    DRT_MODULE_SHM_ENTRY_MGR,
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    DRT_MODULE_MSG_POOL_MGR,
#endif
    DRT_MODULE_NODE_MGR,
    DRT_MODULE_WORKER_MGR,
#ifdef IDS_HAOTIAN
    DRT_MODULE_SE_WORKER_MGR,
#endif
    DRT_MODULE_SCHE_MGR,
    DRT_MODULE_FLOW_CTRL,
    DRT_MODULE_VIEW,
    DRT_MODULE_TIMER,
    DRT_MODULE_SA,
    DRT_MODULE_RA
} DrtModuleInitSeqT;

static bool DrtIsModuleInited(uint16_t initStack, DrtModuleInitSeqT module)
{
    uint16_t flag = (uint16_t)(((uint16_t)1) << (uint8_t)module);
    return (initStack & flag) == flag;
}

static void DrtModuleInitSucceed(DrtInstanceT *drtIns, DrtModuleInitSeqT module)
{
    DB_POINTER(drtIns);
    uint16_t flag = (uint16_t)(((uint16_t)1) << (uint8_t)module);
    drtIns->initStack |= flag;
    DB_LOG_INFO("DRT, Init module %" PRIu32 " fin!", (uint32_t)module);
}

static void DrtModuleDestroyed(DrtInstanceT *drtIns, DrtModuleInitSeqT module)
{
    DB_POINTER(drtIns);
    uint16_t flag = (uint16_t)(((uint16_t)1) << (uint8_t)module);
    drtIns->initStack &= (uint16_t)(~flag);
    DB_LOG_INFO("DRT, Destroy module %" PRIu32 " fin!", (uint32_t)module);
}

void DrtInstDestoryAllMemCtx(DrtInstanceT *drtInst)
{
    DB_POINTER(drtInst);
    DbDeleteDynMemCtx(drtInst->memCtx);
    drtInst->memCtx = NULL;
    if (drtInst->rtMsgTopShareMemCtx != NULL) {
        DbDeleteShmemCtx(drtInst->rtMsgTopShareMemCtx);
        drtInst->rtMsgTopShareMemCtx = NULL;
    }
    if ((DbCommonGetEnv() == ADPT_RTOS_SERVER)) {
        DbDeleteDynMemCtx(drtInst->rtMsgTopDynMemCtx);
    }
    drtInst->rtMsgTopDynMemCtx = NULL;
}

void DrtDestroyInstance(DrtInstanceT *drtIns)
{
    DB_POINTER(drtIns);
    if (DrtIsModuleInited(drtIns->initStack, DRT_MODULE_WORKER_MGR)) {
        DrtStopAllWorker(&drtIns->workerMgr);
    }
    if (DrtIsModuleInited(drtIns->initStack, DRT_MODULE_RA)) {
        RaDestroy(&drtIns->recvAgent);
        DrtModuleDestroyed(drtIns, DRT_MODULE_RA);
    }
    if (DrtIsModuleInited(drtIns->initStack, DRT_MODULE_SA)) {
        SaRelease(&drtIns->sendAgent);
        DrtModuleDestroyed(drtIns, DRT_MODULE_SA);
    }
    if (DrtIsModuleInited(drtIns->initStack, DRT_MODULE_VIEW)) {
        DrtModuleDestroyed(drtIns, DRT_MODULE_VIEW);
    }
    if (DrtIsModuleInited(drtIns->initStack, DRT_MODULE_TIMER)) {
        Status ret = DbTimerUnregister(&drtIns->stateRefreshTimer, TIMER_MODE_LOOP);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "DRT, unreg refresh timer.");
        }
        DrtModuleDestroyed(drtIns, DRT_MODULE_TIMER);
    }
    if (DrtIsModuleInited(drtIns->initStack, DRT_MODULE_FLOW_CTRL)) {
        DrtModuleDestroyed(drtIns, DRT_MODULE_FLOW_CTRL);
    }
    if (DrtIsModuleInited(drtIns->initStack, DRT_MODULE_SCHE_MGR)) {
        DrtDestroyScheduleMgr(&drtIns->scheMgr);
        DrtModuleDestroyed(drtIns, DRT_MODULE_SCHE_MGR);
    }
    if (DrtIsModuleInited(drtIns->initStack, DRT_MODULE_CONN_MGR)) {
        DrtDestroyConnMgr(&drtIns->connMgr);
        DrtModuleDestroyed(drtIns, DRT_MODULE_CONN_MGR);
    }
    if (DrtIsModuleInited(drtIns->initStack, DRT_MODULE_NODE_MGR)) {
        DrtDestroyNodeMgr(&drtIns->nodeMgr);
        DrtModuleDestroyed(drtIns, DRT_MODULE_NODE_MGR);
    }
#ifndef FEATURE_SIMPLEREL
    if (DrtIsModuleInited(drtIns->initStack, DRT_MODULE_ABN_MGR)) {
        DrtDestroyAbnMgr(&drtIns->abnormityMgr);
        DrtModuleDestroyed(drtIns, DRT_MODULE_ABN_MGR);
    }
#endif /* FEATURE_SIMPLEREL */
    if (DrtIsModuleInited(drtIns->initStack, DRT_MODULE_SHM_ENTRY_MGR)) {
        DrtShmEntryMgrUninit(&drtIns->shmEntryMgr);
        DrtModuleDestroyed(drtIns, DRT_MODULE_SHM_ENTRY_MGR);
    }
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    if (DrtIsModuleInited(drtIns->initStack, DRT_MODULE_MSG_POOL_MGR)) {
        SharedMsgPoolMgrDestroy(drtIns->memCtx, drtIns->connMgr.msgPoolMgr);
        DrtModuleDestroyed(drtIns, DRT_MODULE_MSG_POOL_MGR);
    }
#endif
    if (DrtIsModuleInited(drtIns->initStack, DRT_MODULE_WORKER_MGR)) {
        DrtDestroyWorkerMgr(&drtIns->workerMgr);
        DrtModuleDestroyed(drtIns, DRT_MODULE_WORKER_MGR);
    }
#ifdef IDS_HAOTIAN
    if (DrtIsModuleInited(drtIns->initStack, DRT_MODULE_SE_WORKER_MGR)) {
        DrtDestroySeWorkerMgr(&drtIns->seWorkerMgr);
        DrtModuleDestroyed(drtIns, DRT_MODULE_SE_WORKER_MGR);
    }
#endif
    if (drtIns->directMsgPoolMgr != NULL) {
        DirectSharedMsgPoolMgrDestroy(drtIns->directMsgPoolMgr);
    }
    if (DrtIsModuleInited(drtIns->initStack, DRT_MODULE_MEMCTX)) {
        DrtShareMsgMgrDestroy(&drtIns->drtShareMsgMgr);
        DrtInstDestoryAllMemCtx(drtIns);
        DrtModuleDestroyed(drtIns, DRT_MODULE_MEMCTX);
    }
}

Status DrtRegisterView(DbInstanceHdT dbInstance)
{
    Status ret = SvRuntimeRegist(DbSvGetSvInstance(dbInstance));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "regist system view.");
    }
    return ret;
}

void DrtStateRefreshProc(void *args)
{
    DB_POINTER(args);
    DrtInstanceT *drtInstance = (DrtInstanceT *)args;
    DrtConnMgrRefreshShmEntryTimestamp(&drtInstance->connMgr);
    DrtConnMgrRefreshAsyncConnAlarmRatio(&drtInstance->connMgr);
    DrtConnMgrUpdateStatIdx(&drtInstance->connMgr);
    SaRefreshSubConnAlarmRatio(&drtInstance->sendAgent);
    (void)DbDetachSegIdFromRecycMgr();
}

#ifdef HPE_SIMULATION
void DrtCheckPidProc(void *args)
{
    DB_POINTER(args);
    DrtInstanceT *drtIns = (DrtInstanceT *)args;
    if (drtIns->scheMgr.scheMode != SCHEDULE_THREAD_POOL) {
        return;
    }
    for (uint16_t i = 0; DbDynArrayValidId(&drtIns->connMgr.connArr, i); i++) {
        DrtConnectionT *conn = DrtAttachConnById(&drtIns->connMgr, i);
        if (conn == NULL) {
            continue;
        }
        bool isExist = (conn->pid != 0 && DbAdptProcessIsExist(conn->pid));
        //  pid不为0，且对应进程不存在，说明进程异常退出，且drtPipe非空需要释放，ConnComplete避免在prepare阶段并发释放
        if (conn->pid != 0 && !isExist && conn->drtPipe != NULL && DrtConnFlagConnComplete(conn)) {
            if (!DrtConnFlagIsRecycle(conn)) {
                DB_LOG_ERROR(GMERR_CONNECTION_RESET_BY_PEER,
                    "sim, conn may not recycle, connId %" PRIu16 ", pid %" PRIu32, conn->id, conn->pid);
            }
            RaProcPeerReset(&drtIns->recvAgent, conn->drtPipe, conn);
        }
        DrtDetachConnection(conn);
    }
}
#endif

#define DRT_TIME_JUMP_DETECT_PERIOD 10000
#define DRT_STATE_REFRESH_PERIOD 10000
#define DRT_CHECK_PID_PERIOD 30000

static Status DrtRegisterTimerProc(DrtInstanceT *drtInstance, uint32_t level3HungThreshold)
{
    DbTimerT connStateRefreshTimer = {
        "DRT_STATE_REFRESH_TIMER",
        DRT_STATE_REFRESH_PERIOD,
        TIMER_MODE_LOOP,
        {.callBack = DrtStateRefreshProc, .parameter = drtInstance},
    };
    Status ret = DbTimerRegister(&connStateRefreshTimer, &drtInstance->stateRefreshTimer);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "reg drt timer.");
        return ret;
    }
    return GMERR_OK;
}

Status DrtRegisterTimer(DrtInstanceT *drtInstance)
{
    DB_POINTER(drtInstance);
    DbCfgValueT cfgValue;
    uint32_t hungThreshold[WORKER_HUNG_THRESHOLD_NUM];
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);

    // 线程挂死时间阈值，unit：秒
    Status ret = DbCfgGet(cfgHandle, DB_CFG_WORKER_HUNG_THRESHOLD, &cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get worker dead threshold cfg.");
        return ret;
    }
    ret = CfgParamThresholdGetValue(cfgValue.str, (uint32_t)strlen(cfgValue.str), hungThreshold,
        WORKER_HUNG_THRESHOLD_NUM, WORKER_HUNG_THRESHOLD_INFO);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get worker hung threshold val, cfgVal: %s.", cfgValue.str);
        return ret;
    }

    // 0: 一级; 1：二级 ; 2：三级; 这里只要获取三级挂死时间，之前校验过，不可能为0
    uint32_t level3HungThreshold = (uint32_t)(MSECONDS_IN_SECOND * hungThreshold[WORKER_HUNG_THRESHOLD_NUM - 1]);

    return DrtRegisterTimerProc(drtInstance, level3HungThreshold);
}

static uint32_t g_gmdbDrtInstanceId = DB_INVALID_UINT32;
uint32_t DrtGetInstanceId(DbInstanceHdT dbInstance)
{
    if (DbIsMultiInstanceEnabled() && dbInstance != NULL) {
        return ((DbInstanceT *)dbInstance)->instanceId;
    }
    return g_gmdbDrtInstanceId;
}

static DrtInstanceT *g_gmdbRtInsCache[MAX_INSTANCE_NUM] = {NULL};
DrtInstanceT *DrtGetInstanceById(uint32_t drtInstanceId)
{
    if (DbIsMultiInstanceEnabled()) {
        DbInstanceT *dbInstance;
        Status ret = DbGetInstanceById(drtInstanceId, &dbInstance);
        if (ret != GMERR_OK) {
            return NULL;
        }

        DrtInstanceT *drtIns = (DrtInstanceT *)(dbInstance->drtIns);
        DbReleaseInstance(dbInstance);
        return drtIns;
    }
    if (SECUREC_UNLIKELY(drtInstanceId != g_gmdbDrtInstanceId || drtInstanceId > MAX_INSTANCE_ID)) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "inv drt instance id %" PRIu8, drtInstanceId);
        return NULL;
    }

    DrtInstanceT **instanceCache = g_gmdbRtInsCache;
    uint32_t idx = DbGetIdxByInstanceId(drtInstanceId);
    // 缓存为空则给缓存赋值，非空直接返回addr
    if (SECUREC_UNLIKELY(instanceCache[idx] == NULL)) {
        DrtInstanceMgrT *drtInstanceMgr =
            (DrtInstanceMgrT *)DbGetShmemStructById(DB_RT_STRUCT_ID_BASE, DbGetProcGlobalId());
        if (SECUREC_UNLIKELY(drtInstanceMgr == NULL)) {
            DB_LOG_ERROR(GMERR_NO_DATA, "get instance mgr, id %" PRIu16, DbGetProcGlobalId());
            return NULL;
        }
        ShmemPtrT rtInstanceShm = drtInstanceMgr->rtInstance[idx];
        instanceCache[idx] = (DrtInstanceT *)DbShmPtrToAddr(rtInstanceShm);
        if (instanceCache[idx] == NULL) {
            DB_LOG_ERROR(GMERR_NO_DATA, "inv rt instance shm, seg %" PRIu32 " offset %" PRIu32, rtInstanceShm.segId,
                rtInstanceShm.offset);
        }
    }

    return instanceCache[idx];
}

DrtInstanceT *DrtGetInstance(DbInstanceHdT dbInstance)
{
    if (DbIsMultiInstanceEnabled()) {
        DB_ASSERT(dbInstance);
        return (DrtInstanceT *)(((DbInstanceT *)dbInstance)->drtIns);
    }
    return DrtGetInstanceById(g_gmdbDrtInstanceId);
}

/*
 * description: 创建rt共享内存上下文根节点并初始化instance manager
 * return {type} 失败返回NULL，否则返回实例首addr
 */
void *DrtCreateRtShmCtx(ShmemPtrT *instMgrShmPtr, uint32_t instanceId)
{
    void *rtTopShmMemCtx = DbGetShmemCtxById(DB_TOP_RT_SHMCTX_ID, instanceId);
    if (rtTopShmMemCtx != NULL) {
        return rtTopShmMemCtx;
    }

    // 创建rt共享内存上下文
    DbMemCtxArgsT rtShmCtxArgs = {0};
    rtShmCtxArgs.instanceId = instanceId;
    rtShmCtxArgs.ctxId = DB_TOP_RT_SHMCTX_ID;

    // Use private memory context setting
    char param[sizeof(DbBlockMemParamT)] = {0};
    DbBlockMemParamT *blockParam = (DbBlockMemParamT *)param;
    blockParam->isHugePage = false;
    blockParam->baseSize = DRT_SHM_CTX_BASE_SIZE;
    blockParam->stepSize = DRT_SHM_CTX_STEP_SIZE;
    uint64_t maxSize = DbGetTopShmemMaxSize(instanceId);
    // limitSize为memCtx通过配置参数计算的内存上限，创建的时候maxSize不能超过该值。
    uint64_t limitSize = blockParam->baseSize + (uint64_t)blockParam->stepSize * (DbMemCtxGetBlockPoolMaxSegNum() - 1);
    limitSize = DB_MIN(UINT32_MAX, limitSize);  // 参数数值不超过uint32
    blockParam->maxSize = (uint64_t)DB_MIN(maxSize, limitSize);
    blockParam->isReused = true;
    blockParam->allowBigChunk = false;
    blockParam->blkPoolType = BLK_NORMAL;
    AlgoParamT algoParam;
    algoParam.blockParam = blockParam;
    rtShmCtxArgs.algoParam = &algoParam;

    /* rtTopShmMemCtx 说明
        用    途: RT实例共享内存
        生命周期: 长进程级别
        释放策略: 就近释放
        兜底清空措施: 依赖上层memCtx销毁
    */
    rtTopShmMemCtx =
        DbCreateBlockPoolShmemCtx(DbSrvGetAppShmCtx((uint16_t)instanceId), "rtTopShmMemCtx", &rtShmCtxArgs);
    if (rtTopShmMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "create rt top shmem.");
        return NULL;
    }
    // 单进程多实例场景，创建完rtTopShmMemCtx直接返回，不需创建DrtInstanceMgrT
    if (DbIsMultiInstanceEnabled()) {
        return rtTopShmMemCtx;
    }

    // 根据指定的id，申请InstanceMgrT空间
    ShmemPtrT shmPtr = DbShmemStructAllocById(rtTopShmMemCtx, sizeof(DrtInstanceMgrT), DB_RT_STRUCT_ID_BASE);
    DrtInstanceMgrT *drtInstanceMgr = (DrtInstanceMgrT *)DbShmPtrToAddr(shmPtr);
    if (drtInstanceMgr == NULL) {
        DbShmemStructFreeById(rtTopShmMemCtx, shmPtr, DB_RT_STRUCT_ID_BASE);
        DbDeleteShmemCtx(rtTopShmMemCtx);
        DB_LOG_ERROR(GMERR_NO_DATA, "inv shmem addr for rt instance mgr.");
        return NULL;
    }
    for (uint32_t i = 0; i < MAX_INSTANCE_NUM; i++) {
        drtInstanceMgr->rtInstance[i].segId = DB_INVALID_UINT32;
        drtInstanceMgr->rtInstance[i].offset = DB_INVALID_UINT32;
        drtInstanceMgr->isInitInstance[i] = false;
    }
    *instMgrShmPtr = shmPtr;
    return rtTopShmMemCtx;
}

void DrtInitComplete(DbInstanceHdT dbInstance)
{
    DrtInstanceT *drtIns = DrtGetInstance(dbInstance);
    if (drtIns == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get rt instance when drt init comp.");
        return;
    }
    DrtRecvAgentT *ra = &drtIns->recvAgent;
    DB_POINTER(ra);
    ra->initComplete = true;
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
ALWAYS_INLINE_C static bool DrtCheckConnStatusNormal(DrtConnectionT *conn)
{
    DB_POINTER(conn);
    if (SECUREC_UNLIKELY(conn->status == CONN_STATUS_PREPARE)) {
        uint64_t timeDuration = DbToMseconds(DbRdtsc() - conn->timeStamp);
        if (timeDuration >= CONNECT_STAGE2_TIMEOUT_MSEC) {
            char processName[MAX_OS_USER_NAME_LENGTH] = {0};
            (void)DbGetProcessNameByPid(conn->pid, processName, sizeof(processName));
            DB_LOG_ERROR(GMERR_REQUEST_TIME_OUT,
                "DRT conn %" PRIu16 " not recv cli(%s) pid %" PRIu16 "conn msg for %" PRIu64 " ms, ctx %" PRIu32
                ", attch count %" PRIu32,
                conn->id, processName, conn->pid, timeDuration,
                (conn->msgMemCtx == NULL ? DB_MAX_UINT32 : conn->msgMemCtx->ctxId),
                (conn->msgMemCtx == NULL ? DB_MAX_UINT32 : conn->msgMemCtx->attachCount));
            DrtSetConnStatus(conn, CONN_STATUS_CLOSING);
        }
    }
    return conn->status != CONN_STATUS_CLOSING && conn->status != CONN_STATUS_CLOSED;
}

inline static bool DrtNormalConnPipeUnavailable(const DrtConnectionT *conn)
{
    DB_POINTER(conn);
    /* 对应pipe已被接收线程从monitor移除，则走异常断连 */
    return conn->status == CONN_STATUS_NORMAL && conn->drtPipe == NULL;
}

inline static void UpdatePipeRecvTimeout(SmScheProcCtxT *scheProcCtx, DrtPipeT *pipe, uint8_t *idleLoop)
{
    if (SmScheListIsEmpty(&scheProcCtx->scheList)) {
        (*idleLoop)++;
        (void)DbPipeSetRecvTimeout(&pipe->dbPipe, DRT_MAX_RECV_TIMEOUTMS);
    } else if (scheProcCtx->isExceedWorkload) {
        (void)DbPipeSetRecvTimeout(&pipe->dbPipe, DB_MIN(scheProcCtx->recvTimeout, DRT_MAX_RECV_TIMEOUTMS));
    } else {
        (void)DbPipeSetRecvTimeout(&pipe->dbPipe, 1);
    }
    if (pipe->dbPipe.nonBlock) {
        (void)DbPipeSetRecvTimeout(&pipe->dbPipe, 1);
    }
}

// 为了降低HPE环境的CPU底噪，提升平滑过渡速率
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
#define SERVICE_WORKER_MAX_IDLE_LOOP 10  // 控制超时时间增长速度
#define DRT_RECV_TIMEOUT_STEP_MS 500     // 控制超时时间增长步长
#else
#define SERVICE_WORKER_MAX_IDLE_LOOP 60  // 控制超时时间增长速度
#define DRT_RECV_TIMEOUT_STEP_MS 20      // 控制超时时间增长步长
#endif

#define DRT_MAX_SCHE_COUNT_BETWEEN_RECV 8
// 什么时候收消息：1、无自调度消息正在处理 且 DrtProcCtxPoolT的procCtx使用率未达到50%
// 2、每调度DRT_MAX_SCHE_COUNT_BETWEEN_RECV次，强制收一次消息
ALWAYS_INLINE_C static bool NeedRecv(SmScheProcCtxT *scheProcCtx)
{
    scheProcCtx->procCount++;
    return (!scheProcCtx->hasSelfScheMsg && scheProcCtx->scheList.procCtxPool.useNum < NOT_RECV_PROC_CTX_MAX) ||
           (scheProcCtx->procCount & (DRT_MAX_SCHE_COUNT_BETWEEN_RECV - 1)) == 0;
}

void DrtServiceWorkerProc(
    DrtInstanceT *drtIns, SmScheProcCtxT *scheProcCtx, DrtConnectionT *conn, DrtPipeT *pipe, uint16_t workerId)
{
    DB_POINTER4(drtIns, scheProcCtx, conn, pipe);
    scheProcCtx->semWaitTimeout = 1;
    uint8_t idleLoop = 0;
    bool enableResilienceStat = drtIns->scheMgr.enableResilienceStat;
    if (SECUREC_UNLIKELY(enableResilienceStat)) {
        DrtConnRefreshOpTimeout(conn);
    }
    while (SECUREC_LIKELY(!DrtWorkerIsStopping(&drtIns->workerMgr, workerId) && DrtCheckConnStatusNormal(conn))) {
        DrtWorkerUpdateStat(&drtIns->workerMgr, workerId, true);
        if (SECUREC_UNLIKELY(DrtConnTimeoutExit(conn))) {
            DrtHandleInvalidPipe(&drtIns->scheMgr, conn);
        }
        if (SECUREC_LIKELY(NeedRecv(scheProcCtx))) {
            RaRecvAndScheMsg(&drtIns->recvAgent, conn, pipe);
        }
        // 处理消息前将 hasSelfScheMsg 置为false，SmScheProc内部如果产生新的分片消息，会重新将 hasSelfScheMsg 置为true
        scheProcCtx->hasSelfScheMsg = false;
        SmScheProc(&drtIns->scheMgr, scheProcCtx);
        UpdatePipeRecvTimeout(scheProcCtx, pipe, &idleLoop);
        // 超时时间从1ms 平滑过渡到 DRT_MAX_RECV_TIMEOUTMS，谨慎修改，影响cpu占用率
        if (SECUREC_UNLIKELY(idleLoop == SERVICE_WORKER_MAX_IDLE_LOOP)) {
            idleLoop = 0;
            scheProcCtx->semWaitTimeout =
                DB_MIN(DRT_MAX_RECV_TIMEOUTMS, (scheProcCtx->semWaitTimeout + DRT_RECV_TIMEOUT_STEP_MS));
        }

        if (SECUREC_UNLIKELY(enableResilienceStat)) {
            DrtPipeUpdateThreatStatIdx(conn->drtPipe);
        }
        DrtWorkerUpdateStat(&drtIns->workerMgr, workerId, false);
    }
}

static void DrtCreateThreadMsg(const DrtConnectionT *conn, char *threadMsg, uint32_t len)
{
    char processName[DB_MAX_PROC_NAME_LEN] = {0};
    Status ret = DbGetProcessNameByPid(conn->pid, processName, DB_MAX_PROC_NAME_LEN);
    int32_t err = snprintf_s(threadMsg, len, len - 1, "%" PRIu16 "-%" PRIu32 "-%s", conn->id, conn->pid,
        SECUREC_LIKELY(ret == GMERR_OK) ? processName : "NULL");
    if (SECUREC_UNLIKELY(err < 0)) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW,
            "concat str when set threadMsg, connId %" PRIu16 ", peer pid %" PRIu32 ", procName %s.", conn->id,
            conn->pid, strlen(processName) == 0 ? "NULL" : processName);
    }
    threadMsg[len - 1] = 0;
}

void DrtSetThreadMsg(const DrtConnectionT *conn)
{
    char threadMsg[DB_LOG_THREAD_MSG_LEN] = {0};
    DrtCreateThreadMsg(conn, threadMsg, DB_LOG_THREAD_MSG_LEN);
    DbLogSetThreadMsg(threadMsg);
}

void DrtSaveThreadMsg2ConnMgr(const DrtConnectionT *conn)
{
    char threadMsg[DB_LOG_THREAD_MSG_LEN] = {0};
    DrtCreateThreadMsg(conn, threadMsg, DB_LOG_THREAD_MSG_LEN);
    char *dest = conn->connMgr->logThreadMsgs + conn->id * DB_LOG_THREAD_MSG_LEN;
    if (strncpy_s(dest, DB_LOG_THREAD_MSG_LEN, threadMsg, DB_LOG_THREAD_MSG_LEN - 1) != EOK) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "copy logThreadMsgs, conn id:  %" PRId16 ", os: %" PRId32 ".", conn->id,
            (int32_t)errno);
    }
}

void DrtSetThreadMsgFromConnMgr(WorkerTypeE workerType, WorkerTaskCtxT *taskCtx)
{
    DB_POINTER(taskCtx);
    DrtConnectionT *conn = NULL;
    if (workerType == DRT_WORKER_STORAGE) {
        // 存储场景没有conn
        return;
    }
    SmScheProcCtxT *scheCtx = (SmScheProcCtxT *)taskCtx->task;
    conn = scheCtx->conn;
    char *threadMsgs = conn->connMgr->logThreadMsgs;
    DbLogSetThreadMsgFromConnMgr(threadMsgs + conn->id * DB_LOG_THREAD_MSG_LEN);
}

void UpdateRequestProcessorDetach(ScheduleModeE scheMode, DrtConnectionT *conn)
{
    if (scheMode != SCHEDULE_DIRECTELY) {
        DrtDetachConnection(conn);
    }
    if (scheMode == SCHEDULE_THREAD_POOL) {
        DrtDetachConnection(conn);
    }
}

static Status UpdateRequestProcessor(ScheduleModeE scheMode, DrtRecvAgentT *ra, DrtConnectionT *conn)
{
    DB_POINTER3(ra, ra->workerMgr, conn);
    DrtPipeT *drtPipe = conn->drtPipe;
    DbPipeMonitorT *nextMonitor = ra->workerMgr->reqMonitor;
    Status ret = GMERR_OK;
    if (scheMode != SCHEDULE_DIRECTELY) {
        DrtAttachConnection(conn);  // 先给模式1的WORKER、线程池的lsnrWorker、单线程的singleWorker加上引用计数
    }
    // step2：所有调度模式，先从ra->monitor上摘除这个pipe，RaRef不用减
    DrtUnMonitorPipe(drtPipe);
    if (scheMode == SCHEDULE_THREAD_POOL) {
        DrtAttachConnection(conn);  // 给procWorker加上引用计数
        ret = WakeupWorkerPool(ra->workerMgr);
        ret = (ret == GMERR_OK) ? MonitorPipe(conn, drtPipe, nextMonitor) : ret;
    }

    if (ret != GMERR_OK) {
        DrtUnMonitorPipe(drtPipe);  // 重复调用，没有副作用，确保上部分代码后续维护变更此处必然会delete event
        UpdateRequestProcessorDetach(scheMode, conn);
    }
    return ret;
}

void DrtRefreshPeakConnNum(DrtConnMgrT *connMgr)
{
    DB_POINTER(connMgr);
    bool setOK = false;
    do {
        uint16_t currNum = connMgr->curNum;
        if (currNum > connMgr->peakConnNum) {
            setOK = DbAtomicValCAS(&connMgr->peakConnNum, connMgr->peakConnNum, currNum);
        } else {
            return;
        }
    } while (!setOK);
}

Status DrtCompleteConnect(
    DrtInstanceT *drtIns, DrtProcCtxT *procCtx, FixBufferT *response, int32_t opStatus, bool sendNotifyFd)
{
    DB_POINTER5(drtIns, procCtx, procCtx->conn, response, response->buf);
    DrtConnectionT *conn = procCtx->conn;
    FixBufferT *request = &procCtx->msg;
    Status ret = opStatus;
    if (ret == GMERR_OK) {
        DrtSetConnStatus(conn, CONN_STATUS_NORMAL);
        // step1: 从ra摘下，按调度模式分发
        ret = UpdateRequestProcessor(drtIns->scheMgr.scheMode, &drtIns->recvAgent, conn);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION,
                "stage2, update cli req processor, connId-Type: %" PRIu16 "-%" PRIu8 ", cli pid-uid-name: %" PRIu32
                "-%s",
                conn->id, conn->nodeId.flag, conn->pid, conn->auditUserInfo);
            DrtSetConnStatus(conn, CONN_STATUS_CLOSING);
        } else {
            DrtRefreshPeakConnNum(&drtIns->connMgr);
        }
    }
    // step2: ack
    DrtCompleteResponseMsg(procCtx, response, ret);
#ifdef EXPERIMENTAL_NERGC
    conn->tcpTimeStamp = DbRdtsc();
#endif
    conn->stat.connReqProcTime.sendSecConnRspBegin = DbGlobalRdtsc();
    DB_ASSERT(response->seekPos == 0);
    if (RpcPeekMsgHeader(response)->size != FixBufGetPos(response)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "stage2, conn %" PRIu16 " rsp buff size: %" PRIu32 ", pos: %" PRIu32 ".",
            conn->id, RpcPeekMsgHeader(response)->size, FixBufGetPos(response));
        DB_ASSERT(false);
        return GMERR_INTERNAL_ERROR;
    }
    Status sendRet = DrtConnWritePack(conn, response);
    if (sendRet != GMERR_OK) {
        DB_LOG_ERROR(sendRet, "stage2, conn %" PRIu16 " send rsp.", conn->id);
    }

    conn->stat.connReqProcTime.sendSecConnRspEnd = DbGlobalRdtsc();
// 光启场景下暂不支持nfd发送(用于状态合并订阅)
#ifndef EXPERIMENTAL_GUANGQI
    if (ret == GMERR_OK && sendRet == GMERR_OK && sendNotifyFd) {
        (void)DbNotifyFdSendFd(&conn->drtPipe->dbPipe, conn->notifyChannel.nfd);
    }
#endif

    CheckConnStage2TimeUsed(conn, opStatus, sendRet);
    DrtFreeMsg(response);
    DrtFreeRequest(conn, request);
    DrtFreeProcCtx(procCtx);

    return ret;
}

#endif /* FEATURE_SIMPLEREL */
RaInitParaT DrtCreateRaInitParas(DrtInstanceT *drtIns)
{
    DB_POINTER(drtIns);
    RaInitParaT para = {
        .memCtx = drtIns->memCtx,
        .connMgr = &drtIns->connMgr,
        .scheMgr = &drtIns->scheMgr,
        .nodeMgr = &drtIns->nodeMgr,
        .workerMgr = &drtIns->workerMgr,
        .scheStartHandle = (ScheHandleT)drtIns,
        .scheCb = SmScheduleOneMsg,
        .selfLctr = &drtIns->selfLctr,
        .uid = drtIns->uid,
    };
    return para;
}

static SaInitParaT DrtCreateSaInitParas(DrtInstanceT *drtIns)
{
    DB_POINTER(drtIns);
    SaInitParaT para = {
        .memCtx = drtIns->memCtx,
        .workerMgr = &drtIns->workerMgr,
    };
    return para;
}

static ConnMgrInitParaT DrtCreateConnMgrInitParas(DrtInstanceT *drtIns)
{
    DB_POINTER(drtIns);
    ConnMgrInitParaT connMgrInitParas = {
        .memCtx = drtIns->memCtx,
        .flowCtrlInfo = &drtIns->flowCtrlInfo,
        .nodeMgr = &drtIns->nodeMgr,
        .shmEntryMgr = &drtIns->shmEntryMgr,
        .type = drtIns->selfLctr.type,
    };
    return connMgrInitParas;
}

static ShmEntryMgrInitParaT DrtCreateShmEntryMgrInitParas(DrtInstanceT *drtIns)
{
    DB_POINTER(drtIns);
    void *rtTopShmCtx = DbGetShmemCtxById(DB_TOP_RT_SHMCTX_ID, DbGetInstanceId(DbGetInstanceByMemCtx(drtIns->memCtx)));
    ShmEntryMgrInitParaT connMgrInitParas = {
        .maxNum = DbDynArrayGetMaxNum(&drtIns->connMgr.connArr), .dynMemCtx = drtIns->memCtx, .shmMemCtx = rtTopShmCtx};
    return connMgrInitParas;
}

static DrtInitScheMgrParaT DrtCreateScheMgrParas(DrtInstanceT *drtIns, uint16_t connMaxNum)
{
    DB_POINTER(drtIns);
    DrtInitScheMgrParaT scheMgrPara = {
        .workerMgr = &drtIns->workerMgr,
        .memctx = drtIns->memCtx,
        .maxNum = connMaxNum,
    };
    return scheMgrPara;
}

// hpe环境上，注册订阅通道的共享内存memctx的根节点
DbMemCtxT *DrtCreateMsgShareShmemCtx(int32_t maxMemSize, DbInstanceHdT dbInstance)
{
    // 除最大值和ctxId外，其余配置参数与rt之前的msgMemCtx保持一致
    char param[sizeof(DbBlockMemParamT)] = {0};
    DbBlockMemParamT *blockParam = (DbBlockMemParamT *)param;
    blockParam->isHugePage = false;
    blockParam->baseSize = DRT_MSG_SHM_CTX_BASE_SIZE;
    blockParam->stepSize = DRT_MSG_SHM_CTX_STEP_SIZE;
    blockParam->isReused = true;
    blockParam->allowBigChunk = false;
    blockParam->maxSize = (uint64_t)DRT_MSG_SHM_CTX_BASE_SIZE + BLOCK_MEM_MAX_EXTEND_TIMES * DRT_MSG_SHM_CTX_STEP_SIZE;
    blockParam->blkPoolType = BLK_NORMAL;

    AlgoParamT algoParam;
    algoParam.blockParam = blockParam;
    DbMemCtxArgsT argsBlock = {0};
    argsBlock.instanceId = DbGetInstanceId(dbInstance);
    argsBlock.ctxId = DB_QE_SUBS_SHM_TOP_SHMCTX_ID;
    argsBlock.algoParam = &algoParam;
    argsBlock.collectAllocSizeOnThisTree = true;
    uint64_t maxAppShmSize = DbSrvGetMaxAppShmSize(dbInstance);
    argsBlock.maxTotalAllocSize = DB_MIN((uint64_t)((uint32_t)maxMemSize * DB_MEBI), maxAppShmSize - 1);
    /* RtMsgTopShareMemCtx 说明
        用    途: RT共享订阅消息TopMemctx，用于限制全局共享订阅消息内存上限
        生命周期: 长进程级别
        释放策略: 无
        兜底清空措施: 依赖上层memCtx
    */
    return (DbMemCtxT *)DbCreateBlockPoolShmemCtx(
        DbSrvGetAppShmCtx(DbGetInstanceId(dbInstance)), "RtMsgTopShareMemCtx", &argsBlock);
}

DbMemCtxT *DrtCreateMsgDynMemCtx(int32_t maxMemSize, DbMemCtxT *appDynCtx)
{
    DbMemCtxArgsT args = {0};
    args.collectAllocSizeOnThisTree = true;
    uint64_t maxAppDynSize = DbSrvGetMaxAppDynSize((uint16_t)appDynCtx->instanceId);
    args.maxTotalAllocSize = DB_MIN((uint64_t)((uint32_t)maxMemSize * DB_MEBI), maxAppDynSize - 1);
    args.dynCtxNeedRecycle = true;
    /* RtMsgTopDynmemCtx 说明
        用    途: RT动态订阅消息TopMemctx，用于限制全局动态订阅消息内存上限
        生命周期: 长进程级别
        释放策略: 就近释放
        兜底清空措施: 依赖上层memCtx销毁
    */
    return DbCreateDynMemCtx(appDynCtx, true, "RtMsgTopDynmemCtx", &args);  // 256
}

Status DrtInstCreateAllMemCtx(DrtInstanceT *drtInst)
{
    DB_POINTER(drtInst);
    /* dynaMemCtx 说明
        用    途: RT实例top动态内存上下文，用于各组件在该memctx下创建子memctx
        生命周期: 长进程级别
        释放策略: 就近释放
        兜底清空措施: 依赖父memctx销毁, server退出时销毁
    */
    DbMemCtxT *appDynCtx = DbSrvGetAppDynCtx((uint16_t)(drtInst->instanceId));
    DbMemCtxArgsT args = {0};
    args.dynCtxNeedRecycle = true;
    args.liteModOn = true;
    drtInst->memCtx = DbCreateDynMemCtx(appDynCtx, true, DRT_INSTANCE_DYNA_MEMCTX_NAME, &args);
    if (drtInst->memCtx == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "create dyn ctx for drt instance.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    drtInst->rtMsgTopShareMemCtx = NULL;
    drtInst->rtMsgTopDynMemCtx = NULL;

    if (!DrtLctrIsTcp(drtInst)) {
        int32_t cfgSubsChanShmMax =
            DbCfgGetInt32Lite(DB_CFG_SUBS_CHANNEL_SHARE_MEM_MAX, DbGetInstanceByMemCtx(appDynCtx));
        drtInst->rtMsgTopShareMemCtx = DrtCreateMsgShareShmemCtx(cfgSubsChanShmMax, DbGetInstanceByMemCtx(appDynCtx));
        if (drtInst->rtMsgTopShareMemCtx == NULL) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Create subs shmem");
            goto ERR_EXIT;
        }
    }

    if ((DbCommonGetEnv() == ADPT_RTOS_SERVER)) {
        int32_t cfgDynMemMax = DbCfgGetInt32Lite(DB_CFG_SUBS_CHANNEL_DYNAMIC_MEM_MAX, DbGetInstanceByMemCtx(appDynCtx));
        drtInst->rtMsgTopDynMemCtx = DrtCreateMsgDynMemCtx(cfgDynMemMax, appDynCtx);
        if (drtInst->rtMsgTopDynMemCtx == NULL) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Create subs dynmem");
            goto ERR_EXIT;
        }
    }

    return GMERR_OK;
ERR_EXIT:
    if (drtInst->rtMsgTopShareMemCtx != NULL) {
        DbDeleteShmemCtx(drtInst->rtMsgTopShareMemCtx);
        drtInst->rtMsgTopShareMemCtx = NULL;
    }
    if (drtInst->rtMsgTopDynMemCtx != NULL) {
        DbDeleteDynMemCtx(drtInst->rtMsgTopDynMemCtx);
        drtInst->rtMsgTopDynMemCtx = NULL;
    }
    if (drtInst->memCtx != NULL) {
        DbDeleteDynMemCtx(drtInst->memCtx);
        drtInst->memCtx = NULL;
    }
    return GMERR_MEMORY_OPERATE_FAILED;
}

Status DrtStartListenWorker(DrtInstanceT *drtIns)
{
    WorkerParaT workerPara = {
        .name = "DRT_LSNR",
        .type = DRT_WORKER_BGTASK,
        .entry = DrtLsnrWorkerMain,
        .ctx = drtIns,
        .isBindCpu = 1,
        .cpuNO = 1,
        .priority = WORKER_PRIORITY_MIDDLE,
        .nameWithId = false,
        .scheduleTime = CS_RECV_TIMEOUT_MS * SCHEDULE_TIMES,
    };
    Status ret = DrtStartNewWorker(&drtIns->workerMgr, &workerPara, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DRT create lsnr worker, instance %" PRIu8, drtIns->instanceId);
        return ret;
    }

    return GMERR_OK;
}

void DrtMsgPoolAllocForConn(SharedMsgPoolMgrT *msgPoolMgrT, DrtConnMgrT *connMgr)
{
    uint32_t connNum = DbDynArrayGetMaxNum(&connMgr->connArr);
    for (uint32_t i = 0; i < connNum; i++) {
        SharedMsgPoolT *msgPool = DbShmPtrToAddr(msgPoolMgrT->msgPools[i]);
        if (msgPool == NULL) {
            continue;
        }
        DbRWSpinRLock(&connMgr->lock);
        msgPool = DbShmPtrToAddr(msgPoolMgrT->msgPools[i]);
        if (msgPool == NULL) {
            DbRWSpinRUnlock(&connMgr->lock);
            continue;
        }
        if (msgPool->expectedSize != 0) {
            DrtBucketT *bucket = (DrtBucketT *)DbDynArrayGetItemById(&connMgr->connArr, i);
            if (bucket == NULL || bucket->item == NULL) {
                DbRWSpinRUnlock(&connMgr->lock);
                continue;
            }
            DrtConnectionT *conn = bucket->item;
            // 看护逻辑，msgPool内存来自连接msgMemCtx
            if (msgPool->memCtxId != conn->msgMemCtx->ctxId) {
                DB_LOG_EMRG(GMERR_DATA_EXCEPTION,
                    "msg pool and connection do not match, msg pool memCtxId: %" PRIu32 ", conn memCtxId:%" PRIu32
                    ", conn id: %" PRIu16 ", client info: %" PRIu32 ", %s",
                    msgPool->memCtxId, conn->msgMemCtx->ctxId, conn->id, conn->pid, conn->auditUserInfo);
                DbRWSpinRUnlock(&connMgr->lock);
                continue;
            }
            Status ret = SharedMsgPoolAlloc(msgPool, msgPool->expectedSize, conn->msgMemCtx);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret,
                    "Msg pool alloc unsucc, size:%" PRIu32 ", conn:%" PRIu32 ", msgCtxId:%" PRIu32 ", pid:%" PRIu32
                    "-%s",
                    msgPool->expectedSize, conn->id, conn->msgMemCtx->ctxId, conn->pid, conn->auditUserInfo);
            }
        }
        DbRWSpinRUnlock(&connMgr->lock);
    }
}

Status DrtStartMsgPoolAllocService(DrtInstanceT *drtIns)
{
    if (DbCfgGetInt32Lite(DB_CFG_ENABLE_SHARE_MSG_POOL, DbGetInstanceByMemCtx(drtIns->memCtx)) == 0) {
        // 不使用共享消息池的情况下不用该线程，返回GMERR_OK
        return GMERR_OK;
    }

    WorkerParaT workerPara = {.name = "MSGPOOL_ALLOC",
        .type = DRT_WORKER_BGTASK,
        .entry = (DrtWorkerEntry)DrtMsgPoolAllocMain,
        .ctx = drtIns,
        .isBindCpu = 1,
        .cpuNO = 1,
        .priority = WORKER_PRIORITY_MIDDLE,
        .nameWithId = false,
        .scheduleTime = 3 * MSECONDS_IN_SECOND * SCHEDULE_TIMES};
    return DrtStartNewWorker(&drtIns->workerMgr, &workerPara, NULL);
}

static Status DrtInitServerMode(DrtInstanceT *drtIns)
{
    /* 持久化实例，服务端退出时释放 */
    ShmemPtrT shmPtr = DB_INVALID_SHMPTR;
    PrivServerModeT *serverModeIns = NULL;
    if (DrtLctrIsTcp(drtIns)) {
        serverModeIns = (PrivServerModeT *)DbDynMemCtxAlloc(drtIns->memCtx, sizeof(PrivServerModeT));
    } else {
        shmPtr = DbShmemCtxAlloc(drtIns->rtMsgTopShareMemCtx, sizeof(PrivServerModeT));
        serverModeIns = (PrivServerModeT *)DbShmPtrToAddr(shmPtr);
    }
    if (serverModeIns == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "inv mem addr for srv mode, ins %" PRIu8, drtIns->instanceId);
        return GMERR_NO_DATA;
    }
    *serverModeIns = (PrivServerModeT){
        .version = 0,
        .serverMode =
            (bool)DbCfgGetInt32Lite(DB_CFG_ENABLE_RESILIENCE_STAT, DbGetInstanceByMemCtx(drtIns->rtMsgTopShareMemCtx)) ?
                PRIV_COMMON_SERVER_MODE :
                PRIV_DISABLED_SERVER_MODE,
        .serverModeShmPtr = shmPtr,
    };
    drtIns->serverMode = serverModeIns;
    return GMERR_OK;
}
#ifdef IDS_HAOTIAN
void DrtDestroySeWorkerMgr(WorkerMgrT *seWorkerMgr)
{
    if (!DbCfgGetInt32Lite(DB_CFG_ENABLE_CONCURRENT_FETCH_PAGE, DbGetInstanceByMemCtx(seWorkerMgr->memCtx))) {
        return;
    }

    DrtDestroyWorkerMgr(seWorkerMgr);
}

Status DrtInitSeWorkerMgr(WorkerMgrT *seWorkerMgr, DbMemCtxT *memCtx)
{
    DbInstanceHdT dbIns = DbGetInstanceByMemCtx(memCtx);
    if (!DbCfgGetInt32Lite(DB_CFG_ENABLE_CONCURRENT_FETCH_PAGE, dbIns)) {
        return GMERR_OK;
    }

    uint32_t workerMaxNum = SE_WORKERPOOL_MAX_WORKER_NUM;
    DrtInitWorkerMgrParaT workerMgrParas = DrtCreateWorkerMgrParas(memCtx, workerMaxNum, DRT_WORKER_STORAGE);
    Status ret = DrtInitWorkerMgr(seWorkerMgr, &workerMgrParas);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to init worker mgr for bufpool, workerMaxNum %" PRIu32 ".", workerMaxNum);
        return ret;
    }

    return STATUS_OK_INTER;
}
#endif

/*
 * description: runtime实例成员初始化
 * param {type} drtInstance：待初始化的实例
 * return {type} 成功返回STATUS_OK，否则返回错误码
 */
Status DrtInitInstanceInner(DrtInstanceT *drtIns, bool recovery)
{
    DB_POINTER(drtIns);

    Status ret = DrtInstCreateAllMemCtx(drtIns);
    if (ret != GMERR_OK) {
        return ret;
    }
#ifndef FEATURE_SIMPLEREL
    if (!DrtLctrIsTcp(drtIns)) {
// 时序不涉及订阅
#if defined(FEATURE_TS) && !defined(FEATURE_STREAM)
        if (!DbCfgIsTsInstance()) {
#endif
            ret = DrtShareMsgMgrInit(drtIns->rtMsgTopShareMemCtx, &drtIns->drtShareMsgMgr);
            if (ret != GMERR_OK) {
                return ret;
            }
#if defined(FEATURE_TS) && !defined(FEATURE_STREAM)
        }
#endif
    } else {
        drtIns->drtShareMsgMgr.shareMsgMgr = NULL;
    }
    DrtModuleInitSucceed(drtIns, DRT_MODULE_MEMCTX);
    DrtAbnMgrInit(&drtIns->abnormityMgr);
    DrtModuleInitSucceed(drtIns, DRT_MODULE_ABN_MGR);
#endif /* FEATURE_SIMPLEREL */
    ConnMgrInitParaT connMgrInitParas = DrtCreateConnMgrInitParas(drtIns);
    ret = DrtInitConnMgr(&drtIns->connMgr, &connMgrInitParas);
    if (ret != GMERR_OK) {
        return ret;
    }
    DrtModuleInitSucceed(drtIns, DRT_MODULE_CONN_MGR);

    ShmEntryMgrInitParaT shmEntryParas = DrtCreateShmEntryMgrInitParas(drtIns);
    ret = DrtShmEntryMgrInit(&drtIns->shmEntryMgr, &shmEntryParas);
    if (ret != GMERR_OK) {
        return ret;
    }
    DrtModuleInitSucceed(drtIns, DRT_MODULE_SHM_ENTRY_MGR);

    uint16_t connMaxNum = (uint16_t)DbDynArrayGetMaxNum(&drtIns->connMgr.connArr);
#ifdef HPE
    SharedMsgPoolMgrT *msgPoolMgr = SharedMsgPoolMgrCreate(drtIns->memCtx, connMaxNum);
    if (msgPoolMgr == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Create shared msg pool mgr, connMax %" PRIu16, connMaxNum);
        return GMERR_INTERNAL_ERROR;
    }
    drtIns->connMgr.msgPoolMgr = msgPoolMgr;
    DrtModuleInitSucceed(drtIns, DRT_MODULE_MSG_POOL_MGR);
#endif
    ret = DrtInitNodeMgr(&drtIns->nodeMgr, drtIns->memCtx, connMaxNum, (uint16_t)(drtIns->instanceId));
    if (ret != GMERR_OK) {
        return ret;
    }
    DrtModuleInitSucceed(drtIns, DRT_MODULE_NODE_MGR);

    DrtInitWorkerMgrParaT workerMgrParas = DrtCreateWorkerMgrParas(drtIns->memCtx, connMaxNum, DRT_WORKER_SERVICE);
    ret = DrtInitWorkerMgr(&drtIns->workerMgr, &workerMgrParas);
    if (ret != GMERR_OK) {
        return ret;
    }
    DrtModuleInitSucceed(drtIns, DRT_MODULE_WORKER_MGR);

#ifdef IDS_HAOTIAN
    ret = DrtInitSeWorkerMgr(&drtIns->seWorkerMgr, drtIns->memCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    DrtModuleInitSucceed(drtIns, DRT_MODULE_SE_WORKER_MGR);
#endif

    DrtInitScheMgrParaT scheMgrParas = DrtCreateScheMgrParas(drtIns, connMaxNum);
    ret = DrtInitScheduleMgr(&drtIns->scheMgr, &scheMgrParas);
    if (ret != GMERR_OK) {
        return ret;
    }
    DrtModuleInitSucceed(drtIns, DRT_MODULE_SCHE_MGR);

#ifndef FEATURE_SIMPLEREL
    ret = DrtInitFlowCtrlInfo(&drtIns->flowCtrlInfo, &drtIns->abnormityMgr);
    if (ret != GMERR_OK) {
        return ret;
    }
    DrtModuleInitSucceed(drtIns, DRT_MODULE_FLOW_CTRL);
    ret = DrtRegisterView(DbGetInstanceByMemCtx(drtIns->memCtx));
    if (ret != GMERR_OK) {
        return ret;
    }
    DrtModuleInitSucceed(drtIns, DRT_MODULE_VIEW);
#endif
    ret = DrtRegisterTimer(drtIns);
    if (ret != GMERR_OK) {
        return ret;
    }
    DrtModuleInitSucceed(drtIns, DRT_MODULE_TIMER);
    // 单进程多实例场景不初始化Sa和Ra
    if (!DbIsMultiInstanceEnabled()) {
        SaInitParaT saInitParas = DrtCreateSaInitParas(drtIns);
        SaInit(&drtIns->sendAgent, &saInitParas);
        DrtModuleInitSucceed(drtIns, DRT_MODULE_SA);

        RaInitParaT raInitParas = DrtCreateRaInitParas(drtIns);
        ret = RaInit(&drtIns->recvAgent, &raInitParas);
        if (ret != GMERR_OK) {
            return ret;
        }
        DrtModuleInitSucceed(drtIns, DRT_MODULE_RA);
    }
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    ret = DrtStartMsgPoolAllocService(drtIns);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif
#ifdef EMBEDDED_MODE
    // 嵌入式模式不启用建链线程
    return GMERR_OK;
#else
    // 单进程多实例场景，不启动listen worker
    if (!DbIsMultiInstanceEnabled()) {
        return DrtStartListenWorker(drtIns);
    }
    return GMERR_OK;
#endif
}

Status DrtInitInstance(DrtInstanceT *drtIns, uint32_t instanceId, bool recovery)
{
    DB_POINTER(drtIns);

    (void)memset_s(drtIns, sizeof(DrtInstanceT), 0, sizeof(DrtInstanceT));

    drtIns->uid = DbAdptGetuid();
    drtIns->instanceId = (uint16_t)instanceId;
    drtIns->flowCtrlInfo.instanceId = (uint16_t)instanceId;

    Status ret = DrtInstanceInitConfig(drtIns);
    if (ret != GMERR_OK) {
        return ret;
    }
#if defined(FEATURE_TS) && !defined(FEATURE_STREAM)
    if (DbCfgIsTsInstance()) {
        DbAdptMallocTrim(0);
    }
#endif
    ret = DrtInitInstanceInner(drtIns, recovery);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DRT init drtIns, phase: %" PRIu16 ".", drtIns->initStack);
        DrtDestroyInstance(drtIns);
        return GMERR_DATA_EXCEPTION;
    }

    ret = DrtInitServerMode(drtIns);
    if (ret != GMERR_OK) {
        DrtDestroyInstance(drtIns);
        return ret;
    }
    return GMERR_OK;
}

Status DrtInsAllocAndInit(
    DbMemCtxT *rtTopShmMemCtx, uint32_t instanceId, bool recovery, ShmemPtrT instMgrShmPtr, ShmemPtrT *shmPtr)
{
    DB_POINTER2(rtTopShmMemCtx, shmPtr);
    /* 持久化实例，服务端退出时释放 */
    ShmemPtrT drtShmPtr = DbShmemCtxAlloc(rtTopShmMemCtx, sizeof(DrtInstanceT));
    DrtInstanceT *instance = (DrtInstanceT *)DbShmPtrToAddr(drtShmPtr);
    if (instance == NULL) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "DRT alloc mem for drtIns %" PRIu8, instanceId);
        if (DbIsShmPtrValid(instMgrShmPtr)) {
            DbShmemCtxFree(rtTopShmMemCtx, instMgrShmPtr);
        }
        return GMERR_DATA_EXCEPTION;
    }

    Status ret = DrtInitInstance(instance, instanceId, recovery);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "DRT init drtIns %" PRIu8, instanceId);
        DbShmemCtxFree(rtTopShmMemCtx, drtShmPtr);
        if (DbIsShmPtrValid(instMgrShmPtr)) {
            DbShmemCtxFree(rtTopShmMemCtx, instMgrShmPtr);
        }
        return GMERR_DATA_EXCEPTION;
    }

    *(shmPtr) = drtShmPtr;
    DB_LOG_INFO("drtIns %" PRIu8 " init ok!", instanceId);
    return GMERR_OK;
}

Status DrtMultiInsInit(DbMemCtxT *rtTopShmMemCtx, uint32_t instanceId, bool recovery)
{
    DB_POINTER(rtTopShmMemCtx);
    /* 持久化实例，服务端退出时释放 */
    ShmemPtrT shmPtr = DB_INVALID_SHMPTR;
    Status ret = GMERR_OK;
    ret = DrtInsAllocAndInit(rtTopShmMemCtx, instanceId, recovery, DB_INVALID_SHMPTR, &shmPtr);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbInstanceT *dbInstance = NULL;

    ret = DbGetInstanceById(instanceId, &dbInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get Ins(%" PRIu16 ") on DrtInit", instanceId);
        return ret;
    }
    dbInstance->drtIns = (void *)(DrtInstanceT *)DbShmPtrToAddr(shmPtr);
    DbReleaseInstance(dbInstance);
    return GMERR_OK;
}

Status DrtInitInstanceById(uint32_t instanceId, bool recovery)
{
    if (recovery) {
        return GMERR_DATA_EXCEPTION;
    }
    if (!DbIsMultiInstanceEnabled() && (instanceId > MAX_INSTANCE_ID || instanceId < FIRST_INSTANCE_ID)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Ins Id %" PRIu8 " inv", instanceId);
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    // 创建上下文
    ShmemPtrT instMgrShmPtr = DB_INVALID_SHMPTR;
    DbMemCtxT *rtTopShmMemCtx = (DbMemCtxT *)DrtCreateRtShmCtx(&instMgrShmPtr, instanceId);
    if (rtTopShmMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "DRT, Rt top shmem context null, instance %" PRIu8, instanceId);
        return GMERR_DATA_EXCEPTION;
    }
    ShmemPtrT shmPtr = DB_INVALID_SHMPTR;
    Status ret = GMERR_OK;
    // 单进程多实例场景，这里直接初始化drtInstance
    if (DbIsMultiInstanceEnabled()) {
        ret = DrtMultiInsInit(rtTopShmMemCtx, instanceId, recovery);
        if (ret != GMERR_OK) {
            return ret;
        }
        return GMERR_OK;
    }
    // 获取manager结构
    DrtInstanceMgrT *drtInstMgr = (DrtInstanceMgrT *)DbGetShmemStructById(DB_RT_STRUCT_ID_BASE, DbGetProcGlobalId());
    uint32_t idx = DbGetIdxByInstanceId(instanceId);
    if (drtInstMgr == NULL) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "drtIns mgr, when init rt ins %" PRIu8, instanceId);
        DbShmemCtxFree(rtTopShmMemCtx, instMgrShmPtr);
        return GMERR_DATA_EXCEPTION;
    }
    if (drtInstMgr->isInitInstance[idx]) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "ins Id %" PRIu8 " exists", instanceId);
        DbShmemCtxFree(rtTopShmMemCtx, instMgrShmPtr);
        return GMERR_INTERNAL_ERROR;
    }
    ret = DrtInsAllocAndInit(rtTopShmMemCtx, instanceId, recovery, instMgrShmPtr, &shmPtr);
    if (ret != GMERR_OK) {
        return ret;
    }
    drtInstMgr->rtInstance[idx] = shmPtr;
    drtInstMgr->isInitInstance[idx] = true;
    return GMERR_OK;
}

Status DrtInstanceInit(bool recovery, DbInstanceHdT dbInstance)
{
    Status ret = GMERR_OK;
#ifdef SHUTDOWN
    ret = DrtSetDbServerStatus(SERVER_STATUS_RUNNING);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif
    uint32_t instanceId = 0;
    if (DbIsMultiInstanceEnabled()) {
        instanceId = DbGetInstanceId(dbInstance);
    } else {
        instanceId = (uint32_t)DbCfgGetInt32Lite(DB_CFG_SE_GET_INSTANCE_ID, NULL);
    }
    ret = DrtInitInstanceById(instanceId, recovery);
    if (ret == GMERR_OK) {
        g_gmdbDrtInstanceId = (uint8_t)instanceId;
    }
    return ret;
}

void DrtDestroyInstanceById(uint32_t drtInstanceId)
{
    if (DbIsMultiInstanceEnabled()) {
        DrtInstanceT *drtInstance = DrtGetInstanceById(drtInstanceId);
        if (drtInstance == NULL) {
            return;
        }
        DrtDestroyInstance(drtInstance);
        return;
    }
    DbMemCtxT *rtTopShmMemCtx = (DbMemCtxT *)DbGetShmemCtxById(DB_TOP_RT_SHMCTX_ID, DbGetProcGlobalId());
    if (rtTopShmMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "get rt shmem when destroy ins %" PRIu8, drtInstanceId);
        return;
    }
    DrtInstanceMgrT *drtInstanceMgr = DbGetShmemStructById(DB_RT_STRUCT_ID_BASE, DbGetProcGlobalId());
    if (drtInstanceMgr == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "get drtIns mgr when destroy it, ins %" PRIu8, drtInstanceId);
        return;
    }
    DrtInstanceT *drtInstance = DrtGetInstanceById(drtInstanceId);
    if (drtInstance == NULL) {
        return;
    }
    DrtDestroyInstance(drtInstance);
    uint32_t idx = DbGetIdxByInstanceId(drtInstanceId);
    g_gmdbRtInsCache[idx] = NULL;
    DbShmemCtxFree(rtTopShmMemCtx, drtInstanceMgr->rtInstance[idx]);
    drtInstanceMgr->isInitInstance[idx] = false;
    drtInstanceMgr->rtInstance[idx].segId = DB_INVALID_UINT32;
    drtInstanceMgr->rtInstance[idx].offset = DB_INVALID_UINT32;
}

void DrtInstanceDestroy(DbInstanceHdT dbInstance)
{
    if (DbIsMultiInstanceEnabled()) {
        DrtDestroyInstanceById(DbGetInstanceId(dbInstance));
        return;
    }
    DrtDestroyInstanceById(g_gmdbDrtInstanceId);
    g_gmdbDrtInstanceId = DB_INVALID_UINT32;
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
Status DrtAllocProcCtx(const DrtInstanceT *drtIns, DrtConnectionT *conn, FixBufferT *msg, DrtProcCtxTypeE procCtxType,
    DrtProcCtxT **procCtxPtr)
{
    DB_POINTER4(drtIns, conn, msg, procCtxPtr);
    return SmAllocProcCtx(&drtIns->scheMgr, conn, msg, procCtxType, procCtxPtr);
}

void DrtFreeProcCtx(DrtProcCtxT *procCtx)
{
    DB_POINTER(procCtx);
    DrtScheduleListFreeProcCtx(procCtx);
}

DrtConnectionT *DrtAttachConnByConnId(uint16_t connId)
{
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get drtIns by connId.");
        return NULL;
    }
    return DrtAttachConnById(&drtIns->connMgr, connId);
}

Status DrtDetachConnectionByConnId(uint16_t connId)
{
    DrtConnectionT *conn = DrtAttachConnByConnId(connId);
    if (conn == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get conn by Id %" PRIu16 ".", connId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    if (conn->status == CONN_STATUS_INVALID) {
        DrtDetachConnection(conn);
        return GMERR_OK;
    }

    DrtSetConnStatus(conn, CONN_STATUS_INVALID);
    DB_LOG_INFO("close conn ok, connId %" PRIu16 " .", connId);

    DrtDetachConnection(conn);
    return GMERR_OK;
}

Status DrtConnSetBuffSize(uint16_t connId, uint32_t value)
{
    uint64_t msgValue = (uint64_t)(value * DB_KIBI);
    if (msgValue > (uint64_t)MSG_BUFFER_MAX_LENGTH) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Input msg buff size: %" PRIu32 " inv.", value);
        return GMERR_DATA_EXCEPTION;
    }

    DrtConnectionT *conn = DrtAttachConnByConnId(connId);
    if (conn == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get conn by id %" PRIu16 ".", connId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    conn->msgBuffMaxSize = (uint32_t)msgValue;
    DrtDetachConnection(conn);
    return GMERR_OK;
}

static void DrtSubConnMsgMemctxGetLimitSize(DbMemTypeE memType, uint32_t *limitSize)
{
    DB_POINTER(limitSize);
    if (memType != DB_SHARED_MEMORY) {
        uint32_t topDynLimit = (uint32_t)DbCfgGetInt32Lite(DB_CFG_SERVER_TOTAL_DYN_SIZE, NULL);
        uint32_t subMsgLimit = (uint32_t)DbCfgGetInt32Lite(DB_CFG_SUBS_CHANNEL_DYNAMIC_MEM_MAX, NULL);
        *limitSize = DB_MIN(topDynLimit, subMsgLimit);
    } else {
        uint32_t connMsgLimit = (uint32_t)DbCfgGetInt32Lite(DB_CFG_MAX_CONN_MSG_SHM_MEM, NULL);
        uint32_t subMsgLimit = (uint32_t)DbCfgGetInt32Lite(DB_CFG_SUBS_CHANNEL_SHARE_MEM_MAX, NULL);
        uint32_t topShmLimit = (uint32_t)DbCfgGetInt32Lite(DB_CFG_SERVER_TOTAL_SHM_SIZE, NULL);
        *limitSize = DB_MIN(DB_MIN(connMsgLimit, subMsgLimit), topShmLimit);
    }
}

static void DrtConnMsgMemctxGetLimitSize(DbMemTypeE memType, uint32_t *limitSize)
{
    DB_POINTER(limitSize);
    if (memType != DB_SHARED_MEMORY) {
        *limitSize = (uint32_t)DbCfgGetInt32Lite(DB_CFG_SERVER_TOTAL_DYN_SIZE, NULL);
    } else {
        uint32_t connMsgLimit = (uint32_t)DbCfgGetInt32Lite(DB_CFG_MAX_CONN_MSG_SHM_MEM, NULL);
        uint32_t topShmLimit = (uint32_t)DbCfgGetInt32Lite(DB_CFG_SERVER_TOTAL_SHM_SIZE, NULL);
        *limitSize = DB_MIN(connMsgLimit, topShmLimit);
    }
}

static void DrtMsgMemctxGetLimitSize(DrtConnectionT *conn, uint32_t *result)
{
    DB_POINTER2(conn, result);
    uint32_t limitSize = 0;
    if (DrtIsSubConn(conn)) {
        DrtSubConnMsgMemctxGetLimitSize(conn->msgMemCtx->memType, &limitSize);
    } else {
        DrtConnMsgMemctxGetLimitSize(conn->msgMemCtx->memType, &limitSize);
    }
    *result = limitSize;
}

Status DrtConnSetMsgMemctxSize(uint16_t connId, uint32_t value)
{
    uint32_t msgMemCtxSize = SIZE_K(value);
    DrtConnectionT *conn = DrtAttachConnByConnId(connId);
    if (conn == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get conn by id %" PRIu16 ".", connId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t limitSize = 0;
    DrtMsgMemctxGetLimitSize(conn, &limitSize);

    if (msgMemCtxSize > SIZE_M(limitSize)) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE,
            "Set conn ctx size for conn %" PRIu16 ", Set %" PRIu32 " exceed limit %" PRIu32 ".", conn->id,
            msgMemCtxSize, limitSize);
        DrtDetachConnection(conn);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    Status ret = GMERR_OK;
    if (conn->msgMemCtx->memType == DB_DYNAMIC_MEMORY) {
        ret = DbDynCtxResetMaxSize(conn->msgMemCtx, msgMemCtxSize);
    } else {
        ret = DbShmemCtxResetMaxSize(conn->msgMemCtx, msgMemCtxSize);
    }
    DrtDetachConnection(conn);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Set conn ctx size for conn %" PRIu16, conn->id);
        return ret;
    }
    return ret;
}

Status DrtSetServerMode(uint32_t mode)
{
    if (mode >= PRIV_SERVER_MODE_BUTT) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "set srv mode %" PRIu32 " unsucc, range is (0,1)", mode);
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    PrivServerModeT *serverModeIns = DrtGetServerMode(NULL);
    if (serverModeIns == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get srv mode  %" PRIu32, mode);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (serverModeIns->serverMode == PRIV_DISABLED_SERVER_MODE) {
        DB_LOG_WARN(GMERR_INVALID_PARAMETER_VALUE, "cfg enableResilienceStat disable, set srv mode %" PRIu32, mode);
        return GMERR_OK;
    }
    if (mode == (uint32_t)serverModeIns->serverMode) {
        return GMERR_OK;
    }
    serverModeIns->serverMode = mode;
    (void)DbAtomicInc(&serverModeIns->version);
    DB_LOG_INFO("set srv mode %" PRIu32 " ok.", mode);
    return GMERR_OK;
}

Status DrtRejectOrAcceptProcess(uint32_t opType, uint32_t pid)
{
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get drtIns when rej or acc proc.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    char msgSource[MAX_MSG_SOURCE_LEN] = {0};
    Status ret = DrtGetMsgSource(msgSource, MAX_MSG_SOURCE_LEN, pid);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get msg source, pid:%" PRIu32, pid);
        return ret;
    }

    uint32_t *value = DbOamapLookup(&drtIns->connMgr.msgSourceMap.banMap, DbStrToHash32(msgSource), msgSource, NULL);
    if (value == NULL) {
        if (opType == DRT_IPS_REJECT_PROCESS) {
            ret = DrtRejectProcess(pid, &drtIns->connMgr, msgSource);
        }
    } else {
        if (opType == DRT_IPS_ACCEPT_PROCESS) {
            DrtAcceptProcess(pid, &drtIns->connMgr, msgSource);
        }
    }
    return ret;
}

Status DrtGetSubMsgShmCtxSize(uint64_t *size, DbInstanceHdT dbInstance)
{
    DB_POINTER(size);
    DrtInstanceT *drtIns = DrtGetInstance(dbInstance);
    if (drtIns == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get drtIns.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *size = DrtLctrIsTcp(drtIns) ? 0 : DbShmCtxGetTotalPhySize(drtIns->rtMsgTopShareMemCtx, true);
    return GMERR_OK;
}

uint32_t DrtGetUsedSubNodeNum(DbInstanceHdT dbInstance)
{
    DrtInstanceT *drtInstance = DrtGetInstance(dbInstance);
    if (drtInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get ins when static sub node.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    uint32_t subNodeNum = 0;
    for (uint16_t dataPlaneIdx = 0; dataPlaneIdx < MAX_DATA_PLANE_NUM; dataPlaneIdx++) {
        DrtDataPlaneT *dataPlane = drtInstance->sendAgent.dataPlane[dataPlaneIdx];
        if (dataPlane == NULL || dataPlane->capacityNow == 0) {
            continue;
        }
        DbRWSpinRLock(&dataPlane->lock);
        for (uint16_t channelIdx = 0; channelIdx < dataPlane->capacityNow; channelIdx++) {
            if (dataPlane->channels[channelIdx] != NULL) {
                subNodeNum += dataPlane->channels[channelIdx]->nodeSubPool.allocNodeNum;
            } else {
                continue;
            }
        }
        DbRWSpinRUnlock(&dataPlane->lock);
    }
    return subNodeNum;
}

static bool DrtIsUidNameSameAsInputUserName(uint32_t uid, const char *inputName)
{
    char userName[MAX_OS_USER_NAME_LENGTH] = {0};
    Status ret = DbGetUserNameByUid(uid, userName, MAX_OS_USER_NAME_LENGTH);
    if (ret != GMERR_OK) {
        DB_LOG_WARN(ret, "get uname by uid %" PRIu32 ".", uid);
        return false;
    }
    if (DbStrCmp(userName, inputName, false) != 0) {
        return false;
    }
    return true;
}

static bool DrtIsPidNameSameAsInputProcName(uint32_t pid, const char *inputName)
{
    char processName[MAX_OS_USER_NAME_LENGTH] = {0};
    Status ret = DbGetProcessNameByPid(pid, processName, MAX_OS_USER_NAME_LENGTH);
    if (ret != GMERR_OK) {
        DB_LOG_WARN(ret, "get proc name by pid %" PRIu32 ", os: %" PRId32 ".", pid, DbAptGetErrno());
        return false;
    }
    if (DbStrCmp(processName, inputName, false) != 0) {
        return false;
    }
    return true;
}

static bool DrtIsMatchConnUserNameAndProcName(
    DrtConnectionT *conn, PrivUserProtocolT *userProtocol, uint32_t *matchUid, uint32_t *matchPid)
{
    DB_POINTER2(matchUid, matchPid);
    if (*matchUid == DB_INVALID_UINT32) {
        if (!DrtIsUidNameSameAsInputUserName(conn->uid, userProtocol->userName)) {
            return false;
        }
        *matchUid = conn->uid;
    } else if (*matchUid != conn->uid) {
        return false;
    }
    if (*matchPid == DB_INVALID_UINT32) {
        if (!DrtIsPidNameSameAsInputProcName(conn->pid, userProtocol->processName)) {
            return false;
        }
        *matchPid = conn->pid;
    } else if (*matchPid != conn->pid) {
        return false;
    }
    return true;
}

Status DrtSetConnDirectReadPriv(PrivUserProtocolT *userProtocol)
{
    DB_POINTER3(userProtocol, userProtocol->userName, userProtocol->processName);
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get ins when set user proto.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t matchUid = DB_INVALID_UINT32;
    uint32_t matchPid = DB_INVALID_UINT32;
    for (uint16_t i = 0; DbDynArrayValidId(&drtIns->connMgr.connArr, i); i++) {
        DrtConnectionT *conn = DrtAttachConnById(&drtIns->connMgr, i);
        if (conn == NULL) {
            continue;
        }
        if (!DrtIsMatchConnUserNameAndProcName(conn, userProtocol, &matchUid, &matchPid)) {
            DrtDetachConnection(conn);
            continue;
        }
        // 过滤掉不符合条件的连接，设置该连接的直连读权限
        DbShmEntrySetDirectRead(conn->shmEntryCtx.hdl, userProtocol->isEnable);
        DrtDetachConnection(conn);
    }
    return GMERR_OK;
}

DrtProbeDataT *DrtGetProbeData(DrtProbeDataT *connProbeData, DrtInstanceT *drtIns)
{
    DB_POINTER2(connProbeData, drtIns);
    connProbeData->abnDisconnTimes += drtIns->connMgr.connProbeData.abnDisconnTimes;
    connProbeData->invalidMsgNum += drtIns->connMgr.connProbeData.invalidMsgNum;
    return connProbeData;
}

void SubChannelShmCtxUsageLog(void)
{
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get ins.");
        return;
    }
    if (drtIns->drtShareMsgMgr.shareMsgMgr == NULL) {
        return;
    }
    for (uint16_t dataPlaneIdx = 0; dataPlaneIdx < MAX_DATA_PLANE_NUM; dataPlaneIdx++) {
        DrtDataPlaneT *dataPlane = drtIns->sendAgent.dataPlane[dataPlaneIdx];
        if (dataPlane == NULL) {
            continue;
        }
        DrtPlaneEachSubChannelShmCtxUsageLog(dataPlane, drtIns->drtShareMsgMgr.shareMsgMgr->shMemCtx);
    }
}

void DrtRegLastAbnConnExit(DrtInstanceT *drtIns, void(lastAbnConnExitProc)(DrtConnectionT *conn))
{
    DB_POINTER2(drtIns, lastAbnConnExitProc);
    drtIns->connMgr.lastConnAbnExitProc = lastAbnConnExitProc;
}
#endif /* FEATURE_SIMPLEREL */

#ifdef SHUTDOWN
bool DrtDbServerIsStopped(void)
{
    return (g_serverStatus == SERVER_STATUS_STOPPED);
}

Status DrtSetDbServerStatus(uint32_t status)
{
    ServerRunStatusE serverStatus = (ServerRunStatusE)status;
    if (serverStatus >= SERVER_STATUS_BUTT) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "set srv stat %" PRIu32 ", range (0,1)", status);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    g_serverStatus = serverStatus;
    DB_LOG_INFO("set srv stat %" PRIu32 ".", status);
    return GMERR_OK;
}

#define PRIADDR PRIu64
#define UINTADDR(addr) ((uint64_t)(uintptr_t)(addr))
#define ADDR_ALIGNMENT(addr) (UINTADDR(addr) % 4 == 0)

// db结束阶段调用 将所有链接的共享内存version自增 让客户端直连读写指令感知到db结束
void DrtSetAllConnUnAccess(void)
{
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        return;
    }

    DrtConnMgrLockAllStatusList(&drtIns->connMgr);
    DbRWSpinWLock(&drtIns->connMgr.lock);
    for (uint32_t id = 0; id < drtIns->connMgr.shmEntryMgr->maxNum; id++) {
        DbShmEntryHdlT hdl = (DbShmEntryHdlT)DbShmPtrToAddr(drtIns->connMgr.shmEntryMgr->shmPtrArr[id]);
        if (hdl == NULL) {
            continue;
        }
        if (!ADDR_ALIGNMENT(hdl)) {  // 4字节不对齐，DB问题，内存被篡改
            DB_LOG_ERROR_UNFOLD(GMERR_UNEXPECTED_NULL_VALUE,
                "shmPtrArr %" PRIu32 " get addr %" PRIADDR " seg %" PRIu32 " offset %" PRIu32, id, UINTADDR(hdl),
                drtIns->connMgr.shmEntryMgr->shmPtrArr[id].segId, drtIns->connMgr.shmEntryMgr->shmPtrArr[id].offset);
        }
        (void)DbShmEntryIncVersion(hdl);
    }
    DbRWSpinWUnlock(&drtIns->connMgr.lock);
    DrtConnMgrUnlockAllStatusList(&drtIns->connMgr);
}
#endif
