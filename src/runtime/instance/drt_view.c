/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: drt_view.c
 * Description: sysview file for runtime view
 * Author:
 * Create: 2020-09-01
 */

#include <string.h>
#include "securec.h"
#include "adpt_time.h"
#include "db_mem_context.h"
#include "dm_data_prop.h"
#include "dm_data_basic.h"
#include "sysview_instance.h"
#include "adpt_process_name.h"
#include "drt_log.h"
#include "drt_common.h"
#include "drt_connection_inner.h"
#include "drt_data_plane.h"
#include "drt_instance.h"
#include "drt_instance_inner.h"
#include "drt_node.h"
#include "drt_pipe.h"
#include "drt_worker_inner.h"
#include "drt_view.h"

#ifdef __cplusplus
extern "C" {
#endif

#define DRT_PIPE_STAT_VIEW_CYCLE_NUMS 6
#define DRT_CON_STAT_VIEW_CYCLE_NUMS DRT_CON_STAT_CYCLE_NUMS
#define DRT_STAT_NAME_LEN 32
#define DRT_INSTANCE_NUM 1
#define DRT_NUM_16_BIT 16
#define DB_LOW_16BIT(u32) ((uint16_t)((u32) & (0xFFFF)))
#define DB_HIGH_16BIT(u32) ((uint16_t)((u32) >> 16))
#define MERGE_16BIT_2_32BIT(high, low) ((uint32_t)(((high) << DRT_NUM_16_BIT) | ((low) & (0xFFFF))))

#define DRT_WORKER_POOL_STAT \
    "[{\
        \"type\":\"record\",\
        \"name\":\"V$DRT_WORKER_POOL_STAT\",\
        \"fields\":[\
            {\"name\":\"INITED\",\"type\":\"string\",\"size\":32},\
            {\"name\":\"STOPPING\",\"type\":\"string\",\"size\":32},\
            {\"name\":\"PERMANENT_NUM\",\"type\":\"uint16\"},\
            {\"name\":\"MAX_NUM\",\"type\":\"uint16\"},\
            {\"name\":\"PERMANENT_TIMEOUT\",\"type\":\"uint16\"},\
            {\"name\":\"NORMAL_TIMEOUT\",\"type\":\"uint16\"},\
            {\"name\":\"LIVE_NUM\",\"type\":\"uint16\"},\
            {\"name\":\"BUSY_NUM\",\"type\":\"uint16\"},\
            {\"name\":\"FREE_NUM\",\"type\":\"uint16\"},\
            {\"name\":\"PRIORITY_TASKS\",\"type\":\"uint16\"},\
            {\"name\":\"NORMAL_TASKS\",\"type\":\"uint16\"}\
        ]\
    }]"

#define DRT_WORKER_STAT \
    "[{\
        \"type\":\"record\",\
        \"name\":\"V$DRT_WORKER_STAT\",\
        \"fields\":[\
            {\"name\":\"CREATE_WORKER_THREAD_FAILURE_CNT\",\"type\":\"uint32\"},\
            {\"name\":\"CREATE_WORKER_THREAD_SUCCESS_CNT\",\"type\":\"uint32\"},\
            {\"name\":\"WORKER_LIST\", \"type\":\"record\", \"vector\":true,\"size\":1024,\
                \"fields\": [\
                    {\"name\":\"WORKER_NAME\",\"type\":\"string\",\"size\":32},\
                    {\"name\":\"WORKER_ID\",\"type\":\"uint16\"},\
                    {\"name\":\"WORKER_TYPE\",\"type\":\"string\",\"size\":32},\
                    {\"name\":\"THREAD_ID\",\"type\":\"uint64\"},\
                    {\"name\":\"PROC_STATUS\",\"type\":\"string\",\"size\":32},\
                    {\"name\":\"PROC_COUNT\",\"type\":\"uint64\"},\
                    {\"name\":\"CPU_TIME\",\"type\":\"uint64\"},\
                    {\"name\":\"LAST_PROC_TIME\",\"type\":\"uint64\"},\
                    {\"name\":\"TOTAL_PROC_TIME\",\"type\":\"uint64\"}\
                ]}\
        ]\
    }]"

#define DRT_CONN_STAT \
    "[{\
        \"type\":\"record\",\
        \"name\":\"V$DRT_CONN_STAT\",\
        \"fields\":[\
            {\"name\":\"CONN_ID\",\"type\":\"uint16\"},\
            {\"name\":\"CONN_NODE_ID\",\"type\":\"uint16\"},\
            {\"name\":\"CLT_PROC_NAME\",\"type\":\"string\",\"size\":16},\
            {\"name\":\"CLT_PROC_ID\",\"type\":\"uint32\"},\
            {\"name\":\"IP\",\"type\":\"string\",\"size\":16},\
            {\"name\":\"PORT\",\"type\":\"uint16\"},\
            {\"name\":\"CLT_THREAD_NAME\",\"type\":\"string\",\"size\":32},\
            {\"name\":\"CLT_THREAD_ID\",\"type\":\"uint64\"},\
            {\"name\":\"CLT_URL\",\"type\":\"string\",\"size\":32},\
            {\"name\":\"CONN_STATUS\",\"type\":\"string\",\"size\":32},\
            {\"name\":\"CONN_TYPE\",\"type\":\"string\",\"size\":32},\
            {\"name\":\"TIME_STAMP\",\"type\":\"string\",\"size\":32},\
            {\"name\":\"CONN_VERSION\",\"type\":\"uint16\"},\
            {\"name\":\"CONN_PIPE_ID\",\"type\":\"uint16\"},\
            {\"name\":\"CONN_PIPE_VERSION\",\"type\":\"uint16\"},\
            {\"name\":\"PIPE_RECV_CNT\",\"type\":\"uint64\"},\
            {\"name\":\"PIPE_SEND_CNT\",\"type\":\"uint64\"},\
            {\"name\":\"PIPE_RECV_SIZE\",\"type\":\"uint64\"},\
            {\"name\":\"PIPE_SEND_SIZE\",\"type\":\"uint64\"},\
            {\"name\":\"TOTAL_MSG_COUNT\",\"type\":\"uint64\"},\
            {\"name\":\"MAX_MSG_PROC_TIME\",\"type\":\"uint64\"},\
            {\"name\":\"TOTAL_MSG_PROC_TIME\",\"type\":\"uint64\"},\
            {\"name\":\"AVG_MSG_PROC_TIME\",\"type\":\"uint64\"},\
            {\"name\":\"MAX_MSG_CPU_PROC_TIME\",\"type\":\"uint64\"},\
            {\"name\":\"TOTAL_MSG_CPU_PROC_TIME\",\"type\":\"uint64\"},\
            {\"name\":\"AVG_MSG_CPU_PROC_TIME\",\"type\":\"uint64\"},\
            {\"name\":\"MAX_MESSAGE_BUFF_SIZE\",\"type\":\"uint32\"},\
            {\"name\":\"SEND_BUFF_SIZE\",\"type\":\"uint32\"},\
            {\"name\":\"RECV_BUFF_SIZE\",\"type\":\"uint32\"},\
            {\"name\":\"SEND_BUFF_USED_SIZE\",\"type\":\"uint32\"},\
            {\"name\":\"RECV_BUFF_USED_SIZE\",\"type\":\"uint32\"},\
            {\"name\":\"CONN_SHAREMEM_ALLOC_NUM\",\"type\":\"uint32\"},\
            {\"name\":\"CONN_SHAREMEM_FREE_NUM\",\"type\":\"uint32\"},\
            {\"name\":\"NODE_NAME\",\"type\":\"string\",\"size\":256},\
            {\"name\":\"NODE_CLIENT_TYPE\",\"type\":\"string\",\"size\":8},\
            {\"name\":\"RESERVED_TOKEN_ID\",\"type\":\"uint16\"}\
        ]\
    }]"

#define DRT_SCHEDULE_STAT \
    "[{\
        \"type\":\"record\",\
        \"name\":\"V$DRT_SCHEDULE_STAT\",\
        \"fields\":[\
            {\"name\":\"CONN_ID\",\"type\":\"uint16\"},\
            {\"name\":\"EXE_TOTAL_NUM\",\"type\":\"uint32\"},\
            {\"name\":\"CUR_TASK_NUM\",\"type\":\"uint32\"},\
            {\"name\":\"TOTAL_WAIT_TIME\",\"type\":\"uint64\"},\
            {\"name\":\"AVG_WAIT_TIME\",\"type\":\"uint32\"},\
            {\"name\":\"MAX_WAIT_TIME\",\"type\":\"uint32\"},\
            {\"name\":\"TOTAL_EXE_TASK_TIME\",\"type\":\"uint64\"},\
            {\"name\":\"AVG_EXE_TASK_TIME\",\"type\":\"uint32\"},\
            {\"name\":\"MAX_EXE_TASK_TIME\",\"type\":\"uint32\"},\
            {\"name\":\"WEIGHT\",\"type\":\"uint32\"},\
            {\"name\":\"UNIT_TIME\",\"type\":\"uint32\"},\
            {\"name\":\"WORKLOAD\",\"type\":\"uint32\"},\
            {\"name\":\"CUR_UTIME_TASK_COUNT\",\"type\":\"uint32\"}\
        ]\
    }]"

#define DRT_COM_STAT \
    "[{\
        \"type\":\"record\",\
        \"name\":\"V$DRT_COM_STAT\",\
        \"fields\":[\
            {\"name\":\"DRT_INSTANCE_ID\",\"type\":\"uint16\"},\
            {\"name\":\"PEAK_CONN_NUM\",\"type\":\"uint32\"},\
            {\"name\":\"TOTAL_RECV_CNT\",\"type\":\"uint64\"},\
            {\"name\":\"TOTAL_SEND_CNT\",\"type\":\"uint64\"},\
            {\"name\":\"TOTAL_RECV_SIZE\",\"type\":\"uint64\"},\
            {\"name\":\"TOTAL_SEND_SIZE\",\"type\":\"uint64\"},\
            {\"name\":\"COM_CYCLE_STAT\",\"type\":\"record\",\"fixed_array\":true,\"size\":6,\
                \"fields\":[\
                    {\"name\":\"TIME_PERIOD\",\"type\":\"string\",\"size\":32},\
                    {\"name\":\"SEND_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"SEND_SUCCESS_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"SEND_FAIL_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"SEND_SIZE\",\"type\":\"uint64\"},\
                    {\"name\":\"RECV_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"RECV_SUCCESS_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"RECV_FAIL_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"RECV_SIZE\",\"type\":\"uint64\"}\
                ]\
            }\
        ]\
    }]"

#define DRT_PIPE_STAT \
    "[{\
        \"type\":\"record\",\
        \"name\":\"V$DRT_PIPE_STAT\",\
        \"fields\":[\
            {\"name\":\"PIPE_ID\",\"type\":\"uint16\"},\
            {\"name\":\"CONN_ID\",\"type\":\"uint16\"},\
            {\"name\":\"PIPE_PROTOCOL\",\"type\":\"string\",\"size\":128},\
            {\"name\":\"PIPE_CYCLE_STAT\",\"type\":\"record\",\"fixed_array\":true,\"size\":6,\
                \"fields\":[\
                    {\"name\":\"TIME_PERIOD\",\"type\":\"string\",\"size\":24},\
                    {\"name\":\"RECV_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"RECV_SUCCESS_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"RECV_FAIL_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"RECV_SIZE\",\"type\":\"uint64\"},\
                    {\"name\":\"SEND_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"SEND_SUCCESS_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"SEND_FAIL_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"SEND_SIZE\",\"type\":\"uint64\"}\
                ]\
            }\
        ]\
    }]"

#define DRT_LONG_OPERATION_STAT \
    "[{\
        \"type\":\"record\",\
        \"name\":\"V$DRT_LONG_OPERATION_STAT\",\
        \"fields\":[\
            {\"name\":\"OPCODE\",\"type\":\"uint32\"},\
            {\"name\":\"COUNT\",\"type\":\"uint32\"},\
            {\"name\":\"TIME_AVERAGE\",\"type\":\"uint32\"},\
            {\"name\":\"TIME_MAX\",\"type\":\"uint32\"},\
            {\"name\":\"CPU_TIME_AVERAGE\",\"type\":\"uint32\"},\
            {\"name\":\"CPU_TIME_MAX\",\"type\":\"uint32\"},\
            {\"name\":\"EXECUTE_COUNT\",\"type\":\"uint32\"},\
            {\"name\":\"EXECUTE_TIME_AVERAGE\",\"type\":\"uint32\"},\
            {\"name\":\"EXECUTE_TIME_MAX\",\"type\":\"uint32\"},\
            {\"name\":\"LARGE_OBJ_COUNT\",\"type\":\"uint32\"},\
            {\"name\":\"LARGE_OBJ_TIME_AVERAGE\",\"type\":\"uint32\"},\
            {\"name\":\"LARGE_OBJ_TIME_MAX\",\"type\":\"uint32\"},\
            {\"name\":\"LARGE_OBJ_CPU_TIME_AVERAGE\",\"type\":\"uint32\"},\
            {\"name\":\"LARGE_OBJ_CPU_TIME_MAX\",\"type\":\"uint32\"}\
        ]\
    }]"

#define DRT_DATA_PLANE_CHANNEL_STAT \
    "[{\
        \"type\":\"record\",\
        \"name\":\"V$DRT_DATA_PLANE_CHANNEL_STAT\",\
        \"fields\":[\
            {\"name\":\"CHANNEL_ID\",\"type\":\"uint16\"},\
            {\"name\":\"CONN_ID\",\"type\":\"uint16\"},\
            {\"name\":\"CONN_NAME\",\"type\":\"string\",\"size\":128},\
            {\"name\":\"PLANE_ID\",\"type\":\"uint16\"},\
            {\"name\":\"STATUS\",\"type\":\"string\",\"size\":32},\
            {\"name\":\"SYNC_MESSAGE_TOTAL_SEND_COUNT\",\"type\":\"uint64\"},\
            {\"name\":\"SYNC_MESSAGE_SEND_SUCCESS_COUNT\",\"type\":\"uint64\"},\
            {\"name\":\"SYNC_MESSAGE_SEND_FAILURE_COUNT\",\"type\":\"uint64\"},\
            {\"name\":\"SYNC_MESSAGE_SEND_BUFFER_FULL_COUNT\",\"type\":\"uint64\"},\
            {\"name\":\"SYNC_MESSAGE_TOTAL_SEND_TIME\",\"type\":\"uint64\"},\
            {\"name\":\"SYNC_MESSAGE_MAX_SEND_TIME\",\"type\":\"uint64\"},\
            {\"name\":\"TOTAL_SEND_COUNT\",\"type\":\"uint64\"},\
            {\"name\":\"CURRENT_MSG_TRY_SEND_COUNT\",\"type\":\"uint32\"},\
            {\"name\":\"SEND_SUCCEED_COUNT\",\"type\":\"uint64\"},\
            {\"name\":\"SEND_FAILED_COUNT\",\"type\":\"uint64\"},\
            {\"name\":\"SEND_BUFFER_FULL_COUNT\",\"type\":\"uint64\"},\
            {\"name\":\"CTRL_MSG_TRY_SEND_COUNT\",\"type\":\"uint32\"},\
            {\"name\":\"CTRL_MSG_SEND_SUCCESS\",\"type\":\"uint32\"},\
            {\"name\":\"TOTAL_READY_SEND_MSG_COUNT\",\"type\":\"uint64\"},\
            {\"name\":\"TOTAL_ALREADY_SEND_MSG_COUNT\",\"type\":\"uint64\"},\
            {\"name\":\"TOTAL_READY_SEND_SIZE\",\"type\":\"uint64\"},\
            {\"name\":\"TOTAL_ALREADY_SEND_SIZE\",\"type\":\"uint64\"},\
            {\"name\":\"CURRENT_NODE_READY_MSG_SIZE\",\"type\":\"uint64\"},\
            {\"name\":\"CURRENT_NODE_ALREADY_SEND_SIZE\",\"type\":\"uint64\"},\
            {\"name\":\"GENERATOR_NUM\",\"type\":\"uint32\"},\
            {\"name\":\"ALLOC_MSG_NODE_FAILED_COUNT\",\"type\":\"uint64\"},\
            {\"name\":\"INIT_WRITER_FAILED_COUNT\",\"type\":\"uint64\"}\
        ]\
    }]"

#define DRT_CONN_SUBS_STAT \
    "[{\
        \"type\":\"record\",\
        \"name\":\"V$DRT_CONN_SUBS_STAT\",\
        \"fields\":[\
            {\"name\":\"NODE_NAME\",\"type\":\"string\",\"size\":128},\
            {\"name\":\"NODE_CLIENT_TYPE\",\"type\":\"string\",\"size\":8},\
            {\"name\":\"CLT_PROC_ID\",\"type\":\"uint32\"},\
            {\"name\":\"IP\",\"type\":\"string\",\"size\":16},\
            {\"name\":\"PORT\",\"type\":\"uint16\"},\
            {\"name\":\"MAX_RING_SIZE\",\"type\":\"uint32\"},\
            {\"name\":\"RING_USED_SIZE\",\"type\":\"uint32\"},\
            {\"name\":\"BUF_USED_SIZE\",\"type\":\"uint64\"},\
            {\"name\":\"STMG_MQ_SIZE\",\"type\":\"uint32\"},\
            {\"name\":\"STMG_MQ_CUR_COUNT\",\"type\":\"uint32\"},\
            {\"name\":\"SUBS_STATIS\", \"type\":\"record\",\
                \"fields\": [\
                    {\"name\":\"SUB_TRY_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"SUB_ALLOC_CON_MEM_FAIL_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"SUB_SEND_SUC_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"SUB_SEND_FAIL_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"SUB_CLIENT_RECV_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"SUB_CLIENT_EPOLL_WAKE_CNT\",\"type\":\"uint32\"},\
                    {\"name\":\"STMG_NTFY_CNT\",\"type\":\"uint64\"},\
                    {\"name\":\"SUBS_CYCLE_STATIS\", \"type\":\"record\",\"fixed_array\":true,\"size\":6,\
                        \"fields\": [\
                            {\"name\":\"TIME_PERIOD\",\"type\":\"string\",\"size\":32},\
                            {\"name\":\"SUB_TRY_CNT\",\"type\":\"uint64\"},\
                            {\"name\":\"SUB_ALLOC_CON_MEM_FAIL_CNT\",\"type\":\"uint64\"},\
                            {\"name\":\"SUB_SEND_SUC_CNT\",\"type\":\"uint64\"},\
                            {\"name\":\"SUB_SEND_FAIL_CNT\",\"type\":\"uint64\"},\
                            {\"name\":\"SUB_CLIENT_RECV_CNT\",\"type\":\"uint64\"},\
                            {\"name\":\"SUB_CLIENT_EPOLL_WAKE_CNT\",\"type\":\"uint32\"},\
                            {\"name\":\"STMG_NTFY_CNT\",\"type\":\"uint64\"}\
                        ]}\
                ]}\
        ]\
    }]"

static char *g_gmdbConnStatus[] = {"CONN_STATUS_INIT", "CONN_STATUS_PREPARE", "CONN_STATUS_NORMAL",
    "CONN_STATUS_REJECT", "CONN_STATUS_INVALID", "CONN_STATUS_CLOSING", "CONN_STATUS_CLOSED"};

static char *g_gmdbConnType[] = {"CONN_TYPE_NULL", "CONN_TYPE_NORMAL", "CONN_TYPE_EMRG", "CONN_TYPE_RESERVED"};

static Status SvQueryProcRuntimeWorkerPoolStat(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    uint32_t *repeatCnt = (uint32_t *)cursor->rowId;
    if (*repeatCnt > 0) {
        return GMERR_NO_DATA;
    }
    DrtInstanceT *drtInstance = DrtGetInstance(DbGetInstanceByMemCtx(((DmVertexT *)vertex)->memCtx));
    if (drtInstance == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "DrtIns when get thread pool view.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    WorkerPoolT *workerPool = &drtInstance->workerMgr.workerPool;
    DbSpinLock(&workerPool->lock);
    char *initedStr = workerPool->init ? "TRUE" : "FALSE";
    char *stoppingdStr = workerPool->isStopping ? "TRUE" : "FALSE";

    Status ret = DmVertexSetStrPropeByName(vertex, "INITED", initedStr);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "STOPPING", stoppingdStr);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "PERMANENT_NUM", workerPool->permanentNum);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "MAX_NUM", workerPool->maxNum);
    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint16PropeByName(vertex, "PERMANENT_TIMEOUT", workerPool->permanentTimeout);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "NORMAL_TIMEOUT", workerPool->normalTimeout);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "LIVE_NUM", workerPool->liveNum);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "BUSY_NUM", workerPool->busyNum);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "FREE_NUM", workerPool->freeNum);
    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint16PropeByName(
                                  vertex, "PRIORITY_TASKS", drtInstance->workerMgr.taskList[TASK_PRIORITY_HIGH].count);
    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint16PropeByName(
                                  vertex, "NORMAL_TASKS", drtInstance->workerMgr.taskList[TASK_PRIORITY_NORMAL].count);
    DbSpinUnlock(&workerPool->lock);
    (*repeatCnt)++;
    return ret;
}

static Status SvQueryProcRuntimeWorkerStatInner(WorkerListT *workerList, uint16_t workerId, DmNodeT *node)
{
    Status ret = DmNodeVectorAppend(node);
    WorkerT *worker = workerList->workers[workerId];
    DmValueT propertyValue = {0};
    propertyValue.type = DB_DATATYPE_UINT16;
    propertyValue.value.ushortValue = worker->workerId;
    char *workerType = (worker->type == DRT_WORKER_SERVICE) ?
                           "DRT_WORKER_SERVICE" :
                           ((worker->type == DRT_WORKER_BGTASK) ? "DRT_WORKER_BGTASK" : "DRT_WORKER_BUTT");

    ret = (ret != GMERR_OK) ?
              ret :
              DmNodeSetStrPropeByName(node, "WORKER_NAME", worker->name, ((uint32_t)strlen(worker->name) + 1));
    ret = (ret != GMERR_OK) ? ret : DmNodeSetPropeByName("WORKER_ID", propertyValue, node);
    ret = (ret != GMERR_OK) ?
              ret :
              DmNodeSetStrPropeByName(node, "WORKER_TYPE", workerType, (uint32_t)(strlen(workerType) + 1));
    ret = (ret != GMERR_OK) ? ret : DmNodeSetUint64PropeByName(node, "THREAD_ID", (uint64_t)(uintptr_t)worker->handle);

    char *workerStatus = worker->workerStat.isProcessing ? "PROCESSING" : "WAITING";
    ret = (ret != GMERR_OK) ?
              ret :
              DmNodeSetStrPropeByName(node, "PROC_STATUS", workerStatus, (uint32_t)strlen(workerStatus) + 1);
    ret = (ret != GMERR_OK) ? ret : DmNodeSetUint64PropeByName(node, "PROC_COUNT", worker->workerStat.procCount);
    ret = (ret != GMERR_OK) ? ret : DmNodeSetUint64PropeByName(node, "CPU_TIME", worker->workerStat.cpuTimeAll);
    ret = (ret != GMERR_OK) ? ret : DmNodeSetUint64PropeByName(node, "LAST_PROC_TIME", worker->workerStat.procTimeLast);
    ret = (ret != GMERR_OK) ? ret : DmNodeSetUint64PropeByName(node, "TOTAL_PROC_TIME", worker->workerStat.procTimeAll);

    return ret;
}

static Status SvQueryProcRuntimeWorkerStat(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    uint16_t *retFlag = (uint16_t *)cursor->rowId;
    if (*retFlag > 0) {
        DB_LOG_DEBUG("DRT Get worker view recorded fin");
        return GMERR_NO_DATA;
    }

    DmNodeT *node = NULL;
    Status ret = DmVertexGetNodeByName(vertex, "WORKER_LIST", &node);
    if (ret != GMERR_OK) {
        return ret;
    }

    DrtInstanceT *drtInstance = DrtGetInstance(DbGetInstanceByMemCtx(((DmVertexT *)vertex)->memCtx));
    if (drtInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, " get ins when query worker view.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    WorkerListT *workerList = &drtInstance->workerMgr.workerList;
    DbSpinLock(&workerList->lock);

    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint32PropeByName(
                                  vertex, "CREATE_WORKER_THREAD_SUCCESS_CNT", workerList->createThreadSuccessCnt);

    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint32PropeByName(
                                  vertex, "CREATE_WORKER_THREAD_FAILURE_CNT", workerList->createThreadFailureCnt);
    uint16_t workerId = 0;
    // 当前vector最大支持1024个，超过的部分舍弃
    while (workerId < workerList->maxNum && workerId < DM_VECTOR_MAX_SIZE) {
        if (workerList->workers[workerId] != NULL) {
            ret = (ret != GMERR_OK) ? ret : SvQueryProcRuntimeWorkerStatInner(workerList, workerId, node);
        }
        workerId++;
    }
    DbSpinUnlock(&workerList->lock);
    (*retFlag)++;
    return ret;
}

static Status SvRuntimeConnStatTotalMsg(const DrtScheduleListStatT *stat, const DmVertexT *vertex)
{
    DB_POINTER2(stat, vertex);
    uint64_t avgExecTime = (stat->totalExecNum != 0) ? (stat->totalExecUtime / stat->totalExecNum) : 0;
    uint64_t avgCpuExecTime = (stat->totalExecNum != 0) ? (stat->cpuExecUTime / stat->totalExecNum) : 0;

    Status ret = DmVertexSetUint64PropeByName(vertex, "TOTAL_MSG_COUNT", stat->totalExecNum);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "MAX_MSG_PROC_TIME", stat->maxExecUtime);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "TOTAL_MSG_PROC_TIME", stat->totalExecUtime);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "AVG_MSG_PROC_TIME", avgExecTime);
    ret =
        (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "MAX_MSG_CPU_PROC_TIME", stat->maxCpuExecUtime);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "TOTAL_MSG_CPU_PROC_TIME", stat->cpuExecUTime);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "AVG_MSG_CPU_PROC_TIME", avgCpuExecTime);
    return ret;
}

// 下述统计次数的方式如果发生改变，请同步修改函数DrtLogAbnormalStat
static Status SvQueryProcRuntimeComTotal(const DrtPipeStatT *comStat, const DmVertexT *vertex)
{
    DB_POINTER2(comStat, vertex);
    DrtMsgStatT totalStat = {0};

    for (uint8_t i = 0; i < DRT_PIPE_STAT_CYCLE_NUMS; i++) {
        totalStat.recvCount += comStat->cycleStat[i].recvCount;
        totalStat.recvSize += comStat->cycleStat[i].recvSize;
        totalStat.sendCount += comStat->cycleStat[i].sendCount;
        totalStat.sendSize += comStat->cycleStat[i].sendSize;
    }
    Status ret = DmVertexSetUint16PropeByName(
        vertex, "DRT_INSTANCE_ID", (uint16_t)DrtGetInstanceId(DbGetInstanceByMemCtx(vertex->memCtx)));
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "TOTAL_RECV_CNT", totalStat.recvCount);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "TOTAL_RECV_SIZE", totalStat.recvSize);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "TOTAL_SEND_CNT", totalStat.sendCount);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "TOTAL_SEND_SIZE", totalStat.sendSize);

    return ret;
}

static Status SvRuntimePipeInnerCnt(const DmVertexT *vertex, const DrtPipeStatT *stat, uint16_t start, uint16_t end)
{
    DB_POINTER2(vertex, stat);

    uint64_t recvCnt = 0;
    uint64_t sendCnt = 0;
    uint64_t recvSize = 0;
    uint64_t sendSize = 0;

    for (uint16_t i = start; i < end; i++) {
        recvCnt += stat->cycleStat[i % DRT_PIPE_STAT_CYCLE_NUMS].recvCount;
        sendCnt += stat->cycleStat[i % DRT_PIPE_STAT_CYCLE_NUMS].sendCount;
        recvSize += stat->cycleStat[i % DRT_PIPE_STAT_CYCLE_NUMS].recvSize;
        sendSize += stat->cycleStat[i % DRT_PIPE_STAT_CYCLE_NUMS].sendSize;
    }
    Status ret = DmVertexSetUint64PropeByName(vertex, "PIPE_RECV_CNT", recvCnt);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "PIPE_RECV_SIZE", recvSize);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "PIPE_SEND_CNT", sendCnt);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "PIPE_SEND_SIZE", sendSize);

    return ret;
}

static Status SvQueryProcRuntimeFillCycle(
    const DrtPipeStatT *pipeStat, const DmNodeT *cycleNode, const uint16_t cycleIndex)
{
    DB_POINTER(pipeStat);
    Status ret = DmNodeSetUint64PropeByName(cycleNode, "RECV_CNT", pipeStat->cycleStat[cycleIndex].recvCount);
    ret = (ret != GMERR_OK) ?
              ret :
              DmNodeSetUint64PropeByName(cycleNode, "RECV_SUCCESS_CNT", pipeStat->cycleStat[cycleIndex].recvSucCount);

    uint64_t recvFailCount = pipeStat->cycleStat[cycleIndex].recvCount - pipeStat->cycleStat[cycleIndex].recvSucCount;
    ret = (ret != GMERR_OK) ? ret : DmNodeSetUint64PropeByName(cycleNode, "RECV_FAIL_CNT", recvFailCount);
    ret = (ret != GMERR_OK) ?
              ret :
              DmNodeSetUint64PropeByName(cycleNode, "RECV_SIZE", pipeStat->cycleStat[cycleIndex].recvSize);
    ret = (ret != GMERR_OK) ?
              ret :
              DmNodeSetUint64PropeByName(cycleNode, "SEND_CNT", pipeStat->cycleStat[cycleIndex].sendCount);
    ret = (ret != GMERR_OK) ?
              ret :
              DmNodeSetUint64PropeByName(cycleNode, "SEND_SUCCESS_CNT", pipeStat->cycleStat[cycleIndex].sendSucCount);
    uint64_t sendFailCount = pipeStat->cycleStat[cycleIndex].sendCount - pipeStat->cycleStat[cycleIndex].sendSucCount;
    ret = (ret != GMERR_OK) ? ret : DmNodeSetUint64PropeByName(cycleNode, "SEND_FAIL_CNT", sendFailCount);
    ret = (ret != GMERR_OK) ?
              ret :
              DmNodeSetUint64PropeByName(cycleNode, "SEND_SIZE", pipeStat->cycleStat[cycleIndex].sendSize);
    return ret;
}

static Status SvRuntimeConnStatSetTimePeriod(
    const DmNodeT *statsNode, uint64_t updateIdx, uint64_t updateTime, uint16_t numCycle, uint64_t cycleDuration)
{
    DB_POINTER(statsNode);
    DB_UNUSED(updateIdx);
    uint64_t timeMs = updateTime;
    if (updateTime == 0) {
        timeMs = DbToMseconds(DbRdtsc());
    }
    // cycleTimeMs只是差值，不够减也没事,翻转了还会在DbCycleMsGetTimestampStr翻转回来
    uint64_t cycleTimeMs = (uint64_t)(timeMs - (cycleDuration * numCycle));

    char timeString[DRT_STAT_NAME_LEN];
    Status ret = DbCycleMsGetTimestampStr(cycleTimeMs, timeString, DRT_STAT_NAME_LEN, DEFAULT_TIME_FORMAT);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DRT View convert time:%" PRIu64 " to str.", cycleTimeMs);
        return ret;
    }
    return DmNodeSetStrPropeByName(statsNode, "TIME_PERIOD", timeString, (uint32_t)strlen(timeString) + 1);
}

static Status SvQueryProcRuntimeFillCycleIntoVertex(const DrtPipeStatT *pipeStat, const DmVertexT *vertex,
    uint64_t updateIdx, uint64_t updateTime, const char *nodeName)
{
    DB_POINTER2(pipeStat, vertex);
    DmNodeT *cycleNode = NULL;
    Status ret = DmVertexGetNodeByName(vertex, nodeName, &cycleNode);
    if (ret != GMERR_OK) {
        return ret;
    }
    DB_POINTER(cycleNode);
    uint16_t startId = (uint16_t)(updateIdx + DRT_PIPE_STAT_CYCLE_NUMS);
    for (uint8_t numCycle = 0; numCycle < DRT_PIPE_STAT_VIEW_CYCLE_NUMS; numCycle++) {
        ret = DmNodeSetElementIndex(cycleNode, numCycle);
        if (ret != GMERR_OK) {
            return ret;
        }
        uint16_t cycleIndex = (uint16_t)((startId - numCycle) % DRT_PIPE_STAT_CYCLE_NUMS);
        ret = SvRuntimeConnStatSetTimePeriod(cycleNode, updateIdx, updateTime, numCycle, DRT_PIPE_STAT_CYCLE_MS);
        if (ret != GMERR_OK) {
            return ret;
        }

        ret = SvQueryProcRuntimeFillCycle(pipeStat, cycleNode, cycleIndex);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status SvRuntimeConnStatBaseSetPipeInfo(const DrtConnectionT *conn, const DmVertexT *vertex)
{
    DB_POINTER2(conn, vertex);
    Status ret = GMERR_OK;
    uint32_t usedRecvSize = 0;
    uint32_t usedSendSize = 0;
    DrtPipeT *pipe = conn->drtPipe;

    if (pipe != NULL) {
        ret = DbPipeUnRecvMsgSize(&pipe->dbPipe, &usedRecvSize);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, " get unrecv msg size, connId = %" PRIu16 ", pipeId = %" PRIu16 ".",
                pipe->connId, pipe->pipeId);
            return GMERR_DATA_EXCEPTION;
        }
        ret = DbPipeUnSendMsgSize(&pipe->dbPipe, &usedSendSize);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, " get unsend msg size, connId = %" PRIu16 ", pipeId = %" PRIu16 ".",
                pipe->connId, pipe->pipeId);
            return GMERR_DATA_EXCEPTION;
        }
    }
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint16PropeByName(vertex, "CONN_PIPE_ID", (pipe == NULL) ? DB_INVALID_ID16 : pipe->pipeId);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint16PropeByName(vertex, "CONN_PIPE_VERSION", (pipe == NULL) ? 0 : pipe->version);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint32PropeByName(vertex, "SEND_BUFF_SIZE", (pipe == NULL) ? 0 : pipe->sendBuffSize);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint32PropeByName(vertex, "RECV_BUFF_SIZE", (pipe == NULL) ? 0 : pipe->recvBuffSize);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "SEND_BUFF_USED_SIZE", usedSendSize);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "RECV_BUFF_USED_SIZE", usedRecvSize);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "MAX_MESSAGE_BUFF_SIZE", conn->msgBuffMaxSize);
    return ret;
}

static Status SvRuntimeConnStatBase(const DrtConnectionT *conn, const DmVertexT *vertex)
{
    DB_POINTER2(conn, vertex);
    Status ret;
    uint32_t msgMemAllocCount = 0;
    uint32_t msgMemFreeCount = 0;
    if (DbMemCtxGetType(conn->msgMemCtx) == DB_SHARED_MEMORY) {
        DbMemCtxT *header = conn->msgMemCtx;
        ret = DbMemCtxLock(header);
        if (ret != GMERR_OK) {
            return ret;
        }
        msgMemAllocCount = DbGetShmCtxAllocTimes(header);
        msgMemFreeCount = DbGetShmCtxFreeTimes(header);
        DbMemCtxUnLock(header);
    }
    ret = SvRuntimeConnStatBaseSetPipeInfo(conn, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set conn stat pipe info , connId=%" PRIu16 ".", conn->id);
        return ret;
    }
    ret = DmVertexSetUint16PropeByName(vertex, "CONN_ID", conn->id);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "CONN_NODE_ID", conn->nodeId.nodeId);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "CONN_VERSION", conn->version);
    uint32_t connStatus = (uint32_t)((uint32_t)conn->status & 0xF);
    uint32_t privType = (uint32_t)((uint32_t)conn->privType & 0xF);
    // connStatus 和 connType 的值是conn内部信息，可信参数，赋值等过程已校验过，此处无数组越界风险
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "CONN_STATUS", g_gmdbConnStatus[connStatus]);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "CONN_TYPE", g_gmdbConnType[privType]);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "CONN_SHAREMEM_ALLOC_NUM", msgMemAllocCount);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "CONN_SHAREMEM_FREE_NUM", msgMemFreeCount);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "RESERVED_TOKEN_ID", conn->tokenId);
    return ret;
}

static Status SvRuntimeConnTimestampInner(const DrtConnectionT *conn, const DmVertexT *vertex)
{
    DB_POINTER2(conn, vertex);

    DrtInstanceT *drtInstance = DrtGetInstance(DbGetInstanceByMemCtx(vertex->memCtx));
    if (drtInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, " get ins when get timestamp in view.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint64_t timeStamp = drtInstance->connMgr.pipeDeltaStat.updateTime;
    char timeString[DRT_STAT_NAME_LEN];
    Status ret = DbCycleMsGetTimestampStr(timeStamp, timeString, DRT_STAT_NAME_LEN, DEFAULT_TIME_FORMAT);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DRT Conn view  write  timestr.");
        return ret;
    }
    return DmVertexSetStrPropeByName(vertex, "TIME_STAMP", timeString);
}

static Status RuntimeComSetPeakConnNum(DrtInstanceT *drtInstance, void *vertex)
{
    uint32_t peakConnNum = DbAtomicGet(&drtInstance->connMgr.peakConnNum);
    return DmVertexSetUint32PropeByName(vertex, "PEAK_CONN_NUM", peakConnNum);
}

static Status SvQueryProcRuntimeComStat(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);

    uint16_t *drtInstanceId = (uint16_t *)cursor->rowId;

    if ((*drtInstanceId) >= DRT_INSTANCE_NUM) {
        DB_LOG_DEBUG("DRT Get DRT_COM view record finish.");
        return GMERR_NO_DATA;
    }
    DrtInstanceT *drtInstance = DrtGetInstance(DbGetInstanceByMemCtx(((DmVertexT *)vertex)->memCtx));
    if (drtInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "DRT get ins when query view.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    Status ret = DrtConnMgrGenPipesStats(&(drtInstance->connMgr));
    if (ret != GMERR_OK) {
        return ret;
    }
    DrtPipeStatT *outputCycleStat = &drtInstance->connMgr.outputStat;

    ret = SvQueryProcRuntimeComTotal(outputCycleStat, vertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = RuntimeComSetPeakConnNum(drtInstance, vertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SvQueryProcRuntimeFillCycleIntoVertex(outputCycleStat, vertex, drtInstance->connMgr.pipeDeltaStat.updateIdx,
        drtInstance->connMgr.pipeDeltaStat.updateTime, "COM_CYCLE_STAT");
    if (ret != GMERR_OK) {
        return ret;
    }

    (*drtInstanceId)++;
    return GMERR_OK;
}

static Status SvQueryProcRuntimePipe(const DrtPipeT *pipe, const DmVertexT *vertex)
{
    DB_POINTER2(pipe, vertex);

    uint16_t pipeId = DrtGetPipeId(pipe);

    Status ret = DmVertexSetUint16PropeByName(vertex, "PIPE_ID", pipeId);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "CONN_ID", pipe->connId);
    char protocolSting[PIPE_PEER_INFO_LEN] = {0};
    DbPipeGetPipeInfo(&pipe->dbPipe, protocolSting, PIPE_PEER_INFO_LEN);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "PIPE_PROTOCOL", protocolSting);
    if (ret != GMERR_OK) {
        return ret;
    }
    DB_POINTER(pipe->deltaStat);
    ret = SvQueryProcRuntimeFillCycleIntoVertex(
        &pipe->stat, vertex, pipe->deltaStat->updateIdx, pipe->deltaStat->updateTime, "PIPE_CYCLE_STAT");

    return ret;
}

static Status SvRuntimeConnStatNodeInfo(const DrtConnectionT *conn, const DmVertexT *vertex)
{
    DB_POINTER2(conn, vertex);
    DrtInstanceT *drtInstance = DrtGetInstance(DbGetInstanceByMemCtx(vertex->memCtx));
    if (drtInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "DRT get ins when query view.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    char nodeName[MAX_CONN_NAME_LEN] = {0};
    Status ret = DrtGetNodeNameByNodeId(&drtInstance->nodeMgr, conn->nodeId.nodeId, nodeName, MAX_CONN_NAME_LEN);
    if (ret != GMERR_OK) {
        DB_LOG_DEBUG("DRT Get DRT_CONN_STAT view record fin.");
        // Can't return GMERR_UNEXPECTED_NULL_VALUE;
        return ret == GMERR_UNEXPECTED_NULL_VALUE ? GMERR_OK : ret;
    }

    // NODE_NAME
    ret = DmVertexSetStrPropeByName(vertex, "NODE_NAME", nodeName);
    if (ret != GMERR_OK) {
        return ret;
    }

    // NODE_CLIENT_TYPE
    char *cliNodeType[] = {"SYNC", "ASYNC", "SUB"};
    uint8_t clientType = DrtGetFlagByNodeId(&drtInstance->nodeMgr, conn->nodeId.nodeId) & DRT_CLIENT_NODE_TYPE_MASKS;
    if (clientType < ELEMENT_COUNT(cliNodeType)) {
        ret = DmVertexSetStrPropeByName(vertex, "NODE_CLIENT_TYPE", cliNodeType[clientType]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    ret = DmVertexSetStrPropeByName(vertex, "IP", (void *)(uintptr_t)conn->ip);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmVertexSetUint16PropeByName(vertex, "PORT", conn->port);
    if (ret != GMERR_OK) {
        return ret;
    }

    return DmVertexSetUint32PropeByName(vertex, "CLT_PROC_ID", conn->pid);
}

static Status SvRuntimeConnStatBufSize(const DrtConnectionT *conn, const DmVertexT *vertex)
{
    DB_POINTER2(conn, vertex);

    // BUF_USED_SIZE
    uint64_t memCtxTotalAllocSize = 0;
    memCtxTotalAllocSize = DbMemCtxGetTotalAllocSize(conn->msgMemCtx, true);
    memCtxTotalAllocSize /= DB_KIBI;
    return DmVertexSetUint64PropeByName(vertex, "BUF_USED_SIZE", memCtxTotalAllocSize);
}

static Status SvRuntimeConnStatStmgMqInfo(const DrtConnectionT *conn, const DmVertexT *vertex)
{
    DB_POINTER2(conn, vertex);
    DbNotifyMsgQueueT *msgQue = DbShmPtrToAddr(conn->notifyChannel.msgQue);
    if (msgQue == NULL) {
        return GMERR_OK;
    }
    uint32_t size = msgQue->capacity;
    Status ret = DmVertexSetUint32PropeByName(vertex, "STMG_MQ_SIZE", size);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t count = 0;
    for (uint32_t i = 0; i < (uint32_t)NOTIFY_PRIORITY_BUTT; i++) {
        count += msgQue->queue[i].count;
    }
    return DmVertexSetUint32PropeByName(vertex, "STMG_MQ_CUR_COUNT", count);
}

static Status SvRuntimeConnStatRingInfo(const DrtConnectionT *conn, const DmVertexT *vertex)
{
    DB_POINTER2(conn, vertex);
    Status ret;
    DrtPipeT *pipe = conn->drtPipe;

    // MAX_RING_SIZE
    uint32_t maxRingSize = 0;
    if (pipe != NULL && !DbIsEulerEnv()) {
        ret = DbPipeGetSendBuffSize(&pipe->dbPipe, &maxRingSize);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "DRT View get send buff size, PipeId %" PRIu16, pipe->pipeId);
            return ret;
        }
    }

    ret = DmVertexSetUint32PropeByName(vertex, "MAX_RING_SIZE", maxRingSize);
    if (ret != GMERR_OK) {
        return ret;
    }

    // RING_USED_SIZE
    uint32_t maxUsedSize = 0;
    if (pipe != NULL && !DbIsEulerEnv()) {
        ret = DbPipeUnSendMsgSize(&pipe->dbPipe, &maxUsedSize);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "DRT View get unsend msg size, PipeId %" PRIu16, pipe->pipeId);
            return GMERR_DATA_EXCEPTION;
        }
    }
    return DmVertexSetUint32PropeByName(vertex, "RING_USED_SIZE", maxUsedSize);
}

static Status SvRuntimeConnStatSubStatBaseItem(const DmNodeT *subStatsNode, const DrtSubStatBaseItemT *statBaseItem)
{
    DB_POINTER2(statBaseItem, statBaseItem);
    Status ret = DmNodeSetUint64PropeByName(subStatsNode, "SUB_TRY_CNT", statBaseItem->tryCnt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmNodeSetUint64PropeByName(subStatsNode, "SUB_ALLOC_CON_MEM_FAIL_CNT", statBaseItem->allocConnMemFailCnt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmNodeSetUint64PropeByName(subStatsNode, "SUB_SEND_SUC_CNT", statBaseItem->sendSuccCnt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmNodeSetUint64PropeByName(subStatsNode, "SUB_SEND_FAIL_CNT", statBaseItem->sendFailCnt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmNodeSetUint64PropeByName(subStatsNode, "SUB_CLIENT_RECV_CNT", statBaseItem->cliRecvCnt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmNodeSetUint32PropeByName(subStatsNode, "SUB_CLIENT_EPOLL_WAKE_CNT", statBaseItem->epollWakeCnt);
    if (ret != GMERR_OK) {
        return ret;
    }
    return DmNodeSetUint64PropeByName(subStatsNode, "STMG_NTFY_CNT", statBaseItem->stmgNotifyCnt);
}

static Status SvRuntimeConnStatCycleStat(const DrtConnectionT *conn, const DmVertexT *vertex)
{
    DB_POINTER2(conn, vertex);
    DmNodeT *subsCycleStatsNode = NULL;
    uint32_t depth = 3;
    char *namePath[DB_SCHEMA_MAX_DEPTH];
    namePath[0] = (char *)"SUBS_STATIS";
    namePath[1] = (char *)"SUBS_CYCLE_STATIS";

    Status ret = DmVertexGetNodeByNamePath(vertex, (char **)(namePath), depth - 1, &subsCycleStatsNode);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DRT View get SUBS_CYCLE_STATIS node");
        return ret;
    }

    uint16_t startId = (uint16_t)(conn->stat.subStat.updateIdx + DRT_CON_STAT_CYCLE_NUMS);
    for (uint8_t numCycle = 0; numCycle < DRT_CON_STAT_VIEW_CYCLE_NUMS; numCycle++) {
        ret = DmNodeSetElementIndex(subsCycleStatsNode, numCycle);
        if (ret != GMERR_OK) {
            return ret;
        }

        uint16_t cycleIndex = (uint16_t)((startId - numCycle) % DRT_CON_STAT_CYCLE_NUMS);

        ret = SvRuntimeConnStatSetTimePeriod(subsCycleStatsNode, conn->stat.subStat.updateIdx,
            conn->stat.subStat.updateTime, numCycle, DRT_CON_STAT_CYCLE_MS);
        if (ret != GMERR_OK) {
            return ret;
        }

        ret = SvRuntimeConnStatSubStatBaseItem(subsCycleStatsNode, &conn->stat.subStat.cycleStat[cycleIndex]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status SvRuntimeConnSubsAllCycleStat(const DrtConnectionT *conn, const DmVertexT *vertex)
{
    DB_POINTER2(conn, vertex);
    DmNodeT *subStatsNode = NULL;
    uint32_t depth = 2;
    char *namePath[DB_SCHEMA_MAX_DEPTH];
    namePath[0] = (char *)"SUBS_STATIS";

    Status ret = DmVertexGetNodeByNamePath(vertex, (char **)(namePath), depth - 1, &subStatsNode);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DRT View get SUBS_STATIS node");
        return ret;
    }

    ret = SvRuntimeConnStatSubStatBaseItem(subStatsNode, &conn->stat.subStat.allStat);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DRT View write allStat, connId=%" PRIu16 ".", conn->id);
        return ret;
    }

    ret = SvRuntimeConnStatCycleStat(conn, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DRT View write cycleStat, connId=%" PRIu16 ".", conn->id);
    }
    return ret;
}

static Status SvRuntimeConnStatSubs(const DrtConnectionT *conn, const DmVertexT *vertex)
{
    DB_POINTER2(conn, vertex);
    Status ret = SvRuntimeConnStatNodeInfo(conn, vertex);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SvRuntimeConnStatRingInfo(conn, vertex);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SvRuntimeConnStatBufSize(conn, vertex);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SvRuntimeConnStatStmgMqInfo(conn, vertex);
    if (ret != GMERR_OK) {
        return ret;
    }

    return SvRuntimeConnSubsAllCycleStat(conn, vertex);
}

static Status SvRuntimeCltThreadMsg(const DmVertexT *vertex, const DrtConnectionT *conn)
{
    char threadName[DB_THREAD_NAME_MAX_LEN] = {0};
    Status ret = sprintf_s(threadName, DB_THREAD_NAME_MAX_LEN, "%s", conn->peerThreadName);
    if (ret < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    ret = DmVertexSetStrPropeByName(vertex, "CLT_THREAD_NAME", threadName);
    if (ret != GMERR_OK) {
        return ret;
    }

#if defined(FEATURE_TS)
    // 时序视图不支持null类型，故填充字符串null
    ret = DmVertexSetStrPropeByName(vertex, "CLT_URL", "NULL");
    if (ret != GMERR_OK) {
        return ret;
    }
#endif

    ret = DmVertexSetUint64PropeByName(vertex, "CLT_THREAD_ID", conn->peerTid);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status SvRuntimePeerInfoInner(const DrtConnectionT *conn, const DmVertexT *vertex)
{
    Status ret;
    char *processNamePtr = (char *)"NULL";
    char processName[DB_MAX_PROC_NAME_LEN] = {0};
    if (!DbIsTcp()) {
        ret = DbGetProcessNameByPid(conn->pid, processName, DB_MAX_PROC_NAME_LEN);
        if (ret == GMERR_OK) {
            processNamePtr = processName;
        }
    }
    ret = DmVertexSetStrPropeByName(vertex, "CLT_PROC_NAME", processNamePtr);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmVertexSetStrPropeByName(vertex, "IP", (void *)(uintptr_t)conn->ip);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmVertexSetUint16PropeByName(vertex, "PORT", conn->port);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status SvRuntimeConnInner(
    DrtInstanceT *drtInstance, const DrtConnectionT *conn, const DrtConnMgrT *connMgr, const DmVertexT *vertex)
{
    DB_POINTER2(conn, vertex);
    DB_UNUSED(connMgr);
    DrtPipeT *pipe = conn->drtPipe;
    if (pipe == NULL) {
        DB_LOG_INFO("DRT view get pipe, connId=%" PRIu16 ".", conn->id);
        return GMERR_OK;
    }
    Status ret = SvRuntimeConnStatBase(conn, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DRT view op conn stat base, connId=%" PRIu16 ".", conn->id);
        return ret;
    }

    ret = SvRuntimeConnTimestampInner(conn, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DRT view op conn Timestamp stat, connId=%" PRIu16 ".", conn->id);
        return ret;
    }

    ret = SvRuntimePipeInnerCnt(vertex, &pipe->stat, 0, DRT_PIPE_STAT_CYCLE_NUMS);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DRT view op conn pipe, connId = %" PRIu16 ".", conn->id);
        return ret;
    }

    DbRWSpinRLock(&drtInstance->scheMgr.lock);
    SmScheProcCtxT *scheProcCtx = drtInstance->scheMgr.scheProcCtx[conn->id];
    DrtScheduleListStatT stat = scheProcCtx != NULL ? scheProcCtx->scheList.stat : (DrtScheduleListStatT){0};
    DbRWSpinRUnlock(&drtInstance->scheMgr.lock);
    ret = SvRuntimeConnStatTotalMsg(&stat, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DRT view op conn stat, connId=%" PRIu16 ".", conn->id);
        return ret;
    }
    ret = SvRuntimePeerInfoInner(conn, vertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SvRuntimeCltThreadMsg(vertex, conn);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "DRT get cli thread msg");
        return ret;
    }

    return SvRuntimeConnStatNodeInfo(conn, vertex);
}

typedef Status (*SvQueryProcRuntimeConnStatFilterTuncT)(
    DrtInstanceT *drtInstance, const DrtConnectionT *conn, bool *isPass);
typedef Status (*SvRuntimeConnInnerFuncT)(
    DrtInstanceT *drtInstance, const DrtConnectionT *conn, const DrtConnMgrT *connMgr, const DmVertexT *vertex);

static Status SvQueryReservedConnStatTotalMsg(const DmVertexT *vertex)
{
    // SvRuntimeConnStatTotalMsg
    Status ret = DmVertexSetUint64PropeByName(vertex, "TOTAL_MSG_COUNT", 0);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "MAX_MSG_PROC_TIME", 0);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "TOTAL_MSG_PROC_TIME", 0);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "AVG_MSG_PROC_TIME", 0);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "MAX_MSG_CPU_PROC_TIME", 0);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "TOTAL_MSG_CPU_PROC_TIME", 0);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "AVG_MSG_CPU_PROC_TIME", 0);
    // SvRuntimePipeInnerCnt
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "PIPE_RECV_CNT", 0);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "PIPE_RECV_SIZE", 0);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "PIPE_SEND_CNT", 0);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "PIPE_SEND_SIZE", 0);
    return ret;
}

static Status SvQueryProcReservedConnStat(const DmVertexT *vertex)
{
    Status ret = DmVertexSetUint16PropeByName(vertex, "CONN_ID", DB_INVALID_ID16);
    ret =
        (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "CONN_STATUS", g_gmdbConnStatus[CONN_STATUS_INIT]);
    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetStrPropeByName(vertex, "CONN_TYPE", g_gmdbConnType[CONN_PRIV_TYPE_RESERVED]);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "CONN_PIPE_VERSION", DB_INVALID_ID16);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "SEND_BUFF_SIZE", 0);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "RECV_BUFF_SIZE", 0);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "SEND_BUFF_USED_SIZE", 0);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "RECV_BUFF_USED_SIZE", 0);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "MAX_MESSAGE_BUFF_SIZE", 0);
    // SvRuntimeConnStatBase
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "CONN_NODE_ID", DB_INVALID_ID16);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "CONN_VERSION", DB_INVALID_ID16);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "CONN_SHAREMEM_ALLOC_NUM", 0);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "CONN_SHAREMEM_FREE_NUM", 0);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "RESERVED_TOKEN_ID", DB_INVALID_UINT16);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "TIME_STAMP", "");
    // SvRuntimeConnStatNodeInfo
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "NODE_NAME", "");
    ret = (ret != GMERR_OK) ? ret : DmVertexSetStrPropeByName(vertex, "NODE_CLIENT_TYPE", "");
    return (ret != GMERR_OK) ? ret : SvQueryReservedConnStatTotalMsg(vertex);
}

static Status SvQueryProcReservedConn(const DmVertexT *vertex, const DrtInstanceT *drtInstance, uint16_t *connId)
{
    DrtReservedConnStatT stat = drtInstance->connMgr.reservedConnStat;
    uint16_t tokenId = DB_INVALID_UINT16;
    while (*connId < MAX_USE_RESERVED_CONN_USER_NUM * MAX_RESERVED_CONN_NUM_PER_USER) {
        uint16_t index = *connId / MAX_USE_RESERVED_CONN_USER_NUM;
        uint16_t tokensId = *connId % MAX_USE_RESERVED_CONN_USER_NUM;
        uint16_t freeConnNum = (uint16_t)(stat.tokens[tokensId].reservedConnNum - stat.tokens[tokensId].useConnNum);
        if (freeConnNum > index) {  // 第index轮,需要排除上一轮已经扫描过的
            tokenId = tokensId;
            break;
        }
        (*connId)++;
    }
    if (*connId >= MAX_USE_RESERVED_CONN_USER_NUM * MAX_RESERVED_CONN_NUM_PER_USER || tokenId == DB_INVALID_UINT16) {
        return GMERR_NO_DATA;
    }

    Status ret = SvQueryProcReservedConnStat(vertex);
    (*connId)++;
    return ret;
}

#define NORMAL_CONN 0    // 普通连接
#define RESERVED_CONN 1  // 预留连接

static Status SvQueryProcNormalConn(const DmVertexT *vertex, const SvQueryProcRuntimeConnStatFilterTuncT filterFunc,
    const SvRuntimeConnInnerFuncT innerFunc, uint16_t *connId, uint16_t *connTypeId)
{
    DB_POINTER3(vertex, connId, connTypeId);
    DrtBucketT *bucket = NULL;
    Status ret;
    DrtInstanceT *drtInstance = DrtGetInstance(DbGetInstanceByMemCtx(vertex->memCtx));
    if (drtInstance == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Get drtIns when proc normal conn.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    while (DbDynArrayValidId(&drtInstance->connMgr.connArr, *connId)) {
        bucket = DbDynArrayGetItemById(&drtInstance->connMgr.connArr, *connId);
        if (bucket != NULL && bucket->item != NULL) {
            if (filterFunc == NULL) {
                break;
            }
            bool isPass = false;
            ret = filterFunc(drtInstance, bucket->item, &isPass);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "DRT view op filterFunc, connId = %" PRIu16 ".", *connId);
                return ret;
            }
            if (!isPass) {
                break;
            }
        }
        (*connId)++;
    }
    if (!DbDynArrayValidId(&drtInstance->connMgr.connArr, *connId) || bucket == NULL) {
        *connTypeId = RESERVED_CONN;
        *connId = 0;
        return (filterFunc == NULL) ? GMERR_OK : GMERR_NO_DATA;
    }
    ret = innerFunc(drtInstance, bucket->item, &drtInstance->connMgr, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DRT view op innerFunc, connId = %" PRIu16 ".", *connId);
    }
    (*connId)++;
    return ret;
}

static Status SvQueryProcRuntimeConnStatBase(void *vertex, SvCursorT *cursor,
    const SvQueryProcRuntimeConnStatFilterTuncT filterFunc, const SvRuntimeConnInnerFuncT innerFunc)
{
    DB_POINTER2(cursor, innerFunc);
    uint16_t connId = DB_LOW_16BIT(*(uint32_t *)cursor->rowId);
    uint16_t connTypeId = DB_HIGH_16BIT(*(uint32_t *)cursor->rowId);  // 先扫描普通连接id，结束后继续扫描预留连接id
    DrtInstanceT *drtInstance = DrtGetInstance(DbGetInstanceByMemCtx(((DmVertexT *)vertex)->memCtx));
    if (drtInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get DrtIns when query conn view.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    Status ret = GMERR_MAX_VALUE;
    DbRWSpinRLock(&drtInstance->connMgr.lock);
    if (connTypeId == NORMAL_CONN) {
        ret = SvQueryProcNormalConn((const DmVertexT *)vertex, filterFunc, innerFunc, &connId, &connTypeId);
        if (ret != GMERR_OK) {
            DbRWSpinRUnlock(&drtInstance->connMgr.lock);
            return ret;
        }
    }
    if (connTypeId == RESERVED_CONN && filterFunc == NULL) {  // DRT_CONN_STAT视图 打印预留连接
        ret = SvQueryProcReservedConn((const DmVertexT *)vertex, drtInstance, &connId);
    }

    *(uint32_t *)cursor->rowId = MERGE_16BIT_2_32BIT(connTypeId, connId);
    DbRWSpinRUnlock(&drtInstance->connMgr.lock);
    return ret;
}

static Status SvQueryProcRuntimeConnStat(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    return SvQueryProcRuntimeConnStatBase(vertex, cursor, NULL, (SvRuntimeConnInnerFuncT)SvRuntimeConnInner);
}

static Status DrtPlaneChannelGeneralInfo(
    const DmVertexT *vertex, const DrtDataSendChannelT *channel, DrtChannelStatisticT *stat)
{
    DB_POINTER(stat);
    Status ret = DmVertexSetUint64PropeByName(vertex, "TOTAL_SEND_COUNT", stat->totalSendCnt);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint32PropeByName(vertex, "CURRENT_MSG_TRY_SEND_COUNT", stat->curMsgTrySendCnt);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "SEND_SUCCEED_COUNT", stat->sendSucceedCnt);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "SEND_FAILED_COUNT", stat->sendFailedCnt);
    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint64PropeByName(vertex, "SEND_BUFFER_FULL_COUNT", stat->sendBufferFullCnt);
    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint32PropeByName(vertex, "CTRL_MSG_TRY_SEND_COUNT", stat->popCtrlMsgCount);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "CTRL_MSG_SEND_SUCCESS", stat->ctrlMsgSuccess);
    uint64_t totalReadySendMsg =
        channel->ctrlQue.msgInQue + channel->sendQue.msgInQue + channel->pendingQue.msgInQue + stat->msgInCache;
    ret =
        (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "TOTAL_READY_SEND_MSG_COUNT", totalReadySendMsg);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "TOTAL_ALREADY_SEND_MSG_COUNT", stat->totalAlreadySendMsgCnt);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "TOTAL_ALREADY_SEND_SIZE", stat->totalAlreadySendSize);
    uint64_t totalSize = channel->ctrlQue.msgSizeInQue + channel->sendQue.msgSizeInQue +
                         channel->pendingQue.msgSizeInQue + FixBufGetPos(&channel->sndCache);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "TOTAL_READY_SEND_SIZE", totalSize);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "CURRENT_NODE_READY_MSG_SIZE", FixBufGetPos(&channel->sndCache));
    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint64PropeByName(
                                  vertex, "CURRENT_NODE_ALREADY_SEND_SIZE", FixBufGetSeekPos(&channel->sndCache));
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "ALLOC_MSG_NODE_FAILED_COUNT", stat->allocMsgNodeFailedCnt);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "GENERATOR_NUM", stat->generatorNum);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "INIT_WRITER_FAILED_COUNT", stat->initWriterFailedCnt);
    return ret;
}

static Status DrtPlaneChannelSyncMsgInfo(const DmVertexT *vertex, DrtChannelStatisticT *stat)
{
    DB_POINTER(stat);
    Status ret = DmVertexSetUint64PropeByName(vertex, "SYNC_MESSAGE_TOTAL_SEND_COUNT", stat->syncMsgTotalSendCnt);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "SYNC_MESSAGE_SEND_SUCCESS_COUNT", stat->syncMsgSendSuccessCnt);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "SYNC_MESSAGE_SEND_FAILURE_COUNT", stat->syncMsgSendFailureCnt);
    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint64PropeByName(
                                  vertex, "SYNC_MESSAGE_SEND_BUFFER_FULL_COUNT", stat->syncMsgSendBufferFullCnt);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "SYNC_MESSAGE_TOTAL_SEND_TIME", stat->syncMsgTotalSendTime);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint64PropeByName(vertex, "SYNC_MESSAGE_MAX_SEND_TIME", stat->syncMsgMaxSendTime);
    return ret;
}

static Status SvDrtPlaneChannelInner(const DmVertexT *vertex, const DrtDataSendChannelT *channel)
{
    DB_POINTER(channel);
    DrtChannelStatisticT stat = channel->statistic;
    Status ret = DmVertexSetUint16PropeByName(vertex, "CHANNEL_ID", channel->channelId);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint16PropeByName(vertex, "CONN_ID", channel->conn->id);
    DrtInstanceT *drtInstance = DrtGetInstance(DbGetInstanceByMemCtx(vertex->memCtx));
    if (drtInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "DRT get ins when query data plane view");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    char subConnName[MAX_CONN_NAME_LEN] = {0};
    ret = DrtGetNodeNameByNodeId(&drtInstance->nodeMgr, channel->conn->nodeId.nodeId, subConnName, MAX_CONN_NAME_LEN);
    // 连接断连时序: removeNodeId->free sendChannel，channel存在，conn可能找不到
    if (ret == GMERR_OK) {
        ret = DmVertexSetStrPropeByName(vertex, "CONN_NAME", subConnName);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    ret = DmVertexSetUint16PropeByName(vertex, "PLANE_ID", channel->planeId);
    SendChannelStatusE channelStatus = channel->status;
    char *drtPlaneChannelStatus[] = {"SEND_CHANNEL_EMPTY", "SEND_CHANNEL_READY", "SEND_CHANNEL_SENDING",
        "SEND_CHANNEL_CLOSING", "SEND_CHANNEL_CLOSED"};
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetStrPropeByName(vertex, "STATUS", drtPlaneChannelStatus[(uint32_t)channelStatus]);
    ret = (ret != GMERR_OK) ? ret : DrtPlaneChannelGeneralInfo(vertex, channel, &stat);
    ret = (ret != GMERR_OK) ? ret : DrtPlaneChannelSyncMsgInfo(vertex, &stat);
    return ret;
}

static DrtDataSendChannelT *SvFindNextValidChannel(DrtDataPlaneT *dataPlane, uint16_t *pos)
{
    DB_POINTER2(dataPlane, pos);
    uint16_t seekPos = *pos;
    for (uint16_t i = seekPos; i < dataPlane->capacityNow; i++) {
        if (dataPlane->channels[i] != NULL) {
            *pos = i;
            return dataPlane->channels[i];
        }
    }
    return NULL;
}

static Status SvQueryProcDrtPlaneChannelStat(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    uint32_t *sqId = (uint32_t *)cursor->rowId;
    DrtInstanceT *drtInstance = DrtGetInstance(DbGetInstanceByMemCtx(((DmVertexT *)vertex)->memCtx));
    if (drtInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get DrtIns when qry snd chan view.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    Status ret = GMERR_OK;
    uint16_t channelIdx = DB_LOW_16BIT(*sqId);
    DrtDataPlaneT *dataPlane = NULL;
    DrtDataSendChannelT *channel = NULL;
    uint16_t dataPlaneIdx;
    for (dataPlaneIdx = DB_HIGH_16BIT(*sqId); dataPlaneIdx < MAX_DATA_PLANE_NUM; dataPlaneIdx++) {
        dataPlane = drtInstance->sendAgent.dataPlane[dataPlaneIdx];
        if (dataPlane == NULL || dataPlane->capacityNow == 0) {
            channelIdx = 0;
            continue;
        }
        DbRWSpinRLock(&dataPlane->lock);
        channel = SvFindNextValidChannel(dataPlane, &channelIdx);
        if (channel == NULL) {
            DbRWSpinRUnlock(&dataPlane->lock);
            channelIdx = 0;
            continue;
        }
        ret = SvDrtPlaneChannelInner((const DmVertexT *)vertex, channel);
        DbRWSpinRUnlock(&dataPlane->lock);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "DRT op plane chan stat, chanId = %" PRIu16 ".", channel->channelId);
        }
        break;
    }
    if (dataPlane == NULL || channel == NULL) {
        return GMERR_NO_DATA;
    }
    channelIdx++;
    if (channelIdx == dataPlane->capacityNow) {
        dataPlaneIdx++;
        channelIdx = 0;
    }
    *sqId = MERGE_16BIT_2_32BIT(dataPlaneIdx, channelIdx);
    return ret;
}

static Status SvQueryProcRuntimePipeStat(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);

    DrtInstanceT *drtInstance = DrtGetInstance(DbGetInstanceByMemCtx(((DmVertexT *)vertex)->memCtx));
    if (drtInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get DrtIns when query pipe view.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DrtPipeListT *pipeList = &drtInstance->connMgr.pipeList;
    DB_POINTER(pipeList);
    // use cursor to get one pipe
    uint16_t *pipeId = (uint16_t *)cursor->rowId;
    DrtBucketT *bucket = NULL;
    DrtPipeT *pipe = NULL;
    DbRWSpinRLock(&pipeList->lock);
    while (DbDynArrayValidId(&pipeList->pipeArr, *pipeId)) {
        bucket = (DrtBucketT *)DbDynArrayGetItemById(&pipeList->pipeArr, *pipeId);
        pipe = bucket == NULL ? NULL : (DrtPipeT *)bucket->item;
        if (pipe != NULL && pipe->dbPipe.protoPipe != NULL) {
            break;
        }
        (*pipeId)++;
    }
    if (!DbDynArrayValidId(&pipeList->pipeArr, *pipeId) || pipe == NULL) {
        DbRWSpinRUnlock(&pipeList->lock);
        return GMERR_NO_DATA;
    }
    Status ret = SvQueryProcRuntimePipe(pipe, vertex);
    (*pipeId)++;
    DbRWSpinRUnlock(&pipeList->lock);
    return ret;
}

static Status SvQueryProcRuntimeLargeObjOperationStat(const DmVertexT *vertex, SmOneOpStatT *opStat)
{
    uint32_t largeObjTimeAvg =
        (opStat->largeObjCount == 0) ? 0 : (uint32_t)(opStat->largeObjTimeAll / opStat->largeObjCount);
    uint32_t largeObjCpuTimeAvg =
        (opStat->largeObjCount == 0) ? 0 : ((uint32_t)(opStat->largeObjCpuTimeAll / opStat->largeObjCount));
    Status ret = DmVertexSetUint32PropeByName(vertex, "LARGE_OBJ_COUNT", opStat->largeObjCount);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "LARGE_OBJ_TIME_AVERAGE", largeObjTimeAvg);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "LARGE_OBJ_TIME_MAX", opStat->largeObjTimeMax);
    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint32PropeByName(vertex, "LARGE_OBJ_CPU_TIME_AVERAGE", largeObjCpuTimeAvg);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint32PropeByName(vertex, "LARGE_OBJ_CPU_TIME_MAX", opStat->largeObjCpuTimeMax);
    return ret;
}

// 将数组下标映射回原始opcode
static uint32_t GetOpcodeFromArrayIdx(uint32_t arrIdx)
{
    uint32_t left = 0, right;
    for (right = 1; right < LAST_OPCODE_SEG; right++) {
        if (DbGetFillMsgOffset(right) > arrIdx) {
            break;
        }
        left++;
    }
    uint32_t opCode = left * SEG_REGION + (arrIdx - DbGetFillMsgOffset(left));
    return opCode;
}

static Status SvQueryProcRuntimeLongOperationStat(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    DrtInstanceT *drtInstance = DrtGetInstance(DbGetInstanceByMemCtx(((DmVertexT *)vertex)->memCtx));
    if (drtInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get DrtIns instance when qry long op view.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t *opCodeIndex = (uint32_t *)cursor->rowId;  // 连续的数组下标

    while (*opCodeIndex < (uint32_t)REAL_OPCODE_SIZE) {
        if (drtInstance->scheMgr.allLongOpStat[*opCodeIndex].count > 0) {
            break;
        }
        (*opCodeIndex)++;
    }
    if (*opCodeIndex >= (uint32_t)REAL_OPCODE_SIZE) {
        DB_LOG_DEBUG("DRT Get worker view record fin.");
        return GMERR_NO_DATA;
    }
    SmOneOpStatT *opStat = &(drtInstance->scheMgr.allLongOpStat[*opCodeIndex]);
    uint32_t timeAvg = (opStat->count == 0) ? 0 : (uint32_t)(opStat->timeAll / opStat->count);
    uint32_t cpuTimeAvg = (opStat->count == 0) ? 0 : ((uint32_t)(opStat->cpuTimeAll / opStat->count));
    uint32_t executeTimeAvg =
        (opStat->executeCount == 0) ? 0 : ((uint32_t)(opStat->executeTimeAll / opStat->executeCount));
    uint32_t realOpcode = GetOpcodeFromArrayIdx(*opCodeIndex);
    Status ret = DmVertexSetUint32PropeByName((const DmVertexT *)vertex, "OPCODE", realOpcode);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName((const DmVertexT *)vertex, "COUNT", opStat->count);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName((const DmVertexT *)vertex, "TIME_AVERAGE", timeAvg);
    ret =
        (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName((const DmVertexT *)vertex, "TIME_MAX", opStat->timeMax);
    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint32PropeByName((const DmVertexT *)vertex, "CPU_TIME_AVERAGE", cpuTimeAvg);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint32PropeByName((const DmVertexT *)vertex, "CPU_TIME_MAX", opStat->cpuTimeMax);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint32PropeByName((const DmVertexT *)vertex, "EXECUTE_COUNT", opStat->executeCount);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint32PropeByName((const DmVertexT *)vertex, "EXECUTE_TIME_AVERAGE", executeTimeAvg);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint32PropeByName((const DmVertexT *)vertex, "EXECUTE_TIME_MAX", opStat->executeTimeMax);
    ret = (ret != GMERR_OK) ? ret : SvQueryProcRuntimeLargeObjOperationStat((const DmVertexT *)vertex, opStat);
    (*opCodeIndex)++;
    return ret;
}

static Status SvRuntimeConnSubsStatInner(
    const DrtInstanceT *drtInstance, const DrtConnectionT *conn, const DrtConnMgrT *connMgr, const DmVertexT *vertex)
{
    DB_POINTER2(conn, vertex);
    DB_UNUSED(connMgr);
    DB_UNUSED(drtInstance);
    Status ret = SvRuntimeConnStatSubs(conn, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DRT view op conn subs stat, connId = %" PRIu16 ".", conn->id);
    }
    return ret;
}

static Status SvQueryProcRuntimeConnSubsStatFilter(
    const DrtInstanceT *drtInstance, const DrtConnectionT *conn, bool *isPass)
{
    DB_POINTER2(conn, isPass);
    DB_UNUSED(drtInstance);
    DB_ASSERT(conn->nodeId.nodeType == (int32_t)NODE_TYPE_CLIENT);  // 当前只存在CLIENT类型节点
    uint8_t cliType = conn->nodeId.flag & DRT_CLIENT_NODE_TYPE_MASKS;
    *isPass = (cliType != (uint8_t)GMC_CONN_TYPE_SUB);
    return GMERR_OK;
}

static Status SvQueryProcRuntimeConnSubsStat(void *vertex, SvCursorT *cursor)
{
    return SvQueryProcRuntimeConnStatBase(vertex, cursor,
        (SvQueryProcRuntimeConnStatFilterTuncT)SvQueryProcRuntimeConnSubsStatFilter,
        (SvRuntimeConnInnerFuncT)SvRuntimeConnSubsStatInner);
}

inline static uint32_t SvDivide(uint64_t divisor, uint32_t dividends)
{
    return (dividends == 0) ? 0 : (uint32_t)(divisor / dividends);
}

static Status SvQueryProcRuntimeScheStatInner(const DrtScheduleListT *scheList, const DmVertexT *vertex)
{
    DB_POINTER(scheList);
    const DrtScheduleListStatT *stat = &scheList->stat;
    Status ret = DmVertexSetUint16PropeByName(vertex, "CONN_ID", scheList->connId);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "EXE_TOTAL_NUM", stat->totalExecNum);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "CUR_TASK_NUM", scheList->procCtxPool.useNum);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "TOTAL_WAIT_TIME", stat->totalWaitUTime);
    ret = (ret != GMERR_OK) ?
              ret :
              DmVertexSetUint32PropeByName(vertex, "AVG_WAIT_TIME", SvDivide(stat->totalWaitUTime, stat->enqueueNum));
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "MAX_WAIT_TIME", stat->maxWaitUtime);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint64PropeByName(vertex, "TOTAL_EXE_TASK_TIME", stat->totalExecUtime);
    ret = (ret != GMERR_OK) ? ret :
                              DmVertexSetUint32PropeByName(
                                  vertex, "AVG_EXE_TASK_TIME", SvDivide(stat->totalExecUtime, stat->totalExecNum));
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "MAX_EXE_TASK_TIME", stat->maxExecUtime);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "WEIGHT", scheList->connWeight);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "CUR_UTIME_TASK_COUNT", stat->curLoopWorkCnt);
    return ret;
}

static Status SvQueryProcRuntimeScheStat(void *vertex, SvCursorT *cursor)
{
    DB_POINTER(cursor);
    DrtInstanceT *drtInstance = DrtGetInstance(DbGetInstanceByMemCtx(((DmVertexT *)vertex)->memCtx));
    if (drtInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, " get ins when query worker view.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DrtScheMgrT *scheMgr = &drtInstance->scheMgr;
    DrtConnMgrT *connMgr = &drtInstance->connMgr;
    uint16_t *connId = (uint16_t *)cursor->rowId;
    DbRWSpinRLock(&scheMgr->lock);
    while (DbDynArrayValidId(&connMgr->connArr, *connId)) {
        if (scheMgr->scheProcCtx[*connId] != NULL) {
            break;
        }
        (*connId)++;
    }
    if (!DbDynArrayValidId(&connMgr->connArr, *connId)) {
        DbRWSpinRUnlock(&scheMgr->lock);
        return GMERR_NO_DATA;
    }
    Status ret = DmVertexSetUint32PropeByName(vertex, "UNIT_TIME", scheMgr->unitTime);
    ret = (ret != GMERR_OK) ? ret : DmVertexSetUint32PropeByName(vertex, "WORKLOAD", scheMgr->workLoad);
    ret = (ret != GMERR_OK) ?
              ret :
              SvQueryProcRuntimeScheStatInner(
                  (const DrtScheduleListT *)&scheMgr->scheProcCtx[*connId]->scheList, (const DmVertexT *)vertex);
    DbRWSpinRUnlock(&scheMgr->lock);
    (*connId)++;
    return ret;
}

static SvDefT drtSysviewDefInfo[] = {{"V$DRT_WORKER_STAT", DRT_WORKER_STAT, SvQueryProcRuntimeWorkerStat, false, false},
    {"V$DRT_WORKER_POOL_STAT", DRT_WORKER_POOL_STAT, SvQueryProcRuntimeWorkerPoolStat, false, false},
    {"V$DRT_CONN_STAT", DRT_CONN_STAT, SvQueryProcRuntimeConnStat, false, false},
    {"V$DRT_COM_STAT", DRT_COM_STAT, SvQueryProcRuntimeComStat, false, false},
    {"V$DRT_PIPE_STAT", DRT_PIPE_STAT, SvQueryProcRuntimePipeStat, false, false},
    {"V$DRT_LONG_OPERATION_STAT", DRT_LONG_OPERATION_STAT, SvQueryProcRuntimeLongOperationStat, false, false},
    {"V$DRT_DATA_PLANE_CHANNEL_STAT", DRT_DATA_PLANE_CHANNEL_STAT, SvQueryProcDrtPlaneChannelStat, false, false},
    {"V$DRT_CONN_SUBS_STAT", DRT_CONN_SUBS_STAT, SvQueryProcRuntimeConnSubsStat, false, false},
    {"V$DRT_SCHEDULE_STAT", DRT_SCHEDULE_STAT, SvQueryProcRuntimeScheStat, false, false}};

Status SvRuntimeRegist(SvInstanceT *viewInst)
{
    return SvRegistViews(viewInst, ELEMENT_COUNT(drtSysviewDefInfo), drtSysviewDefInfo);
}

#ifdef __cplusplus
}
#endif
