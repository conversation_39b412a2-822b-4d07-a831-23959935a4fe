/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: adpt_pipe_hpe.c
 * Description:
 * Author: lumaosheng
 * Create: 2020-8-11
 */
#include <time.h>
#include "adpt_register_hpe.h"
#include "adpt_pipe.h"
#include "adpt_function_loader.h"
#include "adpt_chan_define.h"
#include "adpt_time.h"
#include "adpt_rdtsc.h"
#include "adpt_log.h"
#include "adpt_rdtsc.h"
#include "adpt_pipe.h"

#ifdef __cplusplus
extern "C" {
#endif

#define HPE_INVALID_VPOLL (-1)
#define HPE_INVALID_ND (-1)
/* Valid opcodes ("op" parameter ) to issue to HPE_VpollCtl(). */
#define HPE_VPOLL_CTL_ADD 1 /* Add a nd to the interface.  */
#define HPE_VPOLL_CTL_DEL 2 /* Remove a nd from the interface. */
// Vnotify event masks
#define HPE_VPOLLIN 0x00000001
#define HPE_VPOLLHUP 0x00000010
#define VPOLLHUP 0x0002
#define VPOLLOUT 0x0003
#define ND_WAKE_EVENT_WRITEABLE 1
#define CHAN_NOTIFY_THRESHOLD 0x0008  // 分片报文通信时当channel通道中消息个数大于该阈值时会发送一个notify

struct PipeCycle {
    uint64_t startCycle;
    uint64_t step0Cycle;
    uint64_t step1Cycle;
    uint64_t step2Cycle;
    uint64_t endCycle;
};

typedef enum {
    LOG_CATEGORY_GMDB_APP = 0x700,    // APP操作GMDB交互日志
    LOG_CATEGORY_AUDIT_LOGS = 0x900,  // 审计日志日志
} LogCategory;

#define DHF_XPOLL_ET 0x80000000 /**< Xpoll ctl 边缘触发模式；默认为水平触发模式，无特定宏 */
typedef union {
    void *ptr;
    int xd; /* linux fd, hpe nd */
    uint32_t u32;
    uint64_t u64;
} DHF_XpollData;

#ifdef __x86_64__
#define DHF_XPOLL_PACKED __attribute__((packed))
#else
#define DHF_XPOLL_PACKED
#endif

typedef struct {
    uint32_t events;    /* Vpoll events */
    DHF_XpollData data; /* User data variable */
} DHF_XPOLL_PACKED DHF_XpollEvent;

typedef union {
    int32_t fd;
    uint64_t object;
} DHF_Xd;

static struct {
    int32_t (*ctlChanCreate)(const char *name, uint32_t len);  // server使用
    int32_t (*ctlChanCreateByPara)(
        const char *name, DHF_ChannelPara *para, uint32_t size, uint32_t *localChnId);  // server使用
    int32_t (*chanCreate)(uint32_t len);
    int32_t (*ctlChanRecv)(int32_t chanId, struct timespec *timeout, uint32_t flags, uint64_t *shmAddr, uint32_t *len);
    int32_t (*ctlChanDestroy)(int32_t chanId);
    int32_t (*ctlChanGetItemsNum)(int32_t chanId);
    int32_t (*chanOpen)(int32_t chanId, uint32_t *srvChanId);
    int32_t (*chanOpenByName)(const char *name, uint32_t *channId);
    int32_t (*chanSend)(int32_t chanId, uint64_t shmAddr, uint32_t len);
    int32_t (*chanRecv)(int32_t chanId, struct timespec *timeout, uint32_t flags, uint64_t *shmAddr, uint32_t *len);
    int32_t (*chanDestroy)(int32_t chanId);
    int32_t (*chanGetState)(int32_t chanId);
    int32_t (*chanGetItemsNum)(int32_t chanId);
    int32_t (*chanGetPeerItemsNum)(int32_t chanId);
    uint32_t (*chanGetRingLen)(int32_t chanId);
    int32_t (*chanGetOwnerInfo)(int32_t chanId, void *ownerInfo);
    uint64_t (*globalRdtsc)(void);
    int32_t (*dhfXpollCreate)(int32_t size);
    int32_t (*dhfXpollDestroy)(int32_t xpId);
    int32_t (*dhfXpollCtl)(int32_t xpId, int32_t op, uint32_t xd, DHF_XpollEvent *event);
    int32_t (*dhfXpollWait)(int32_t xpId, DHF_XpollEvent *ev, int32_t maxEvents, int32_t timeout);
    int32_t (*chanidToNd)(int32_t chanId);
    // um支持用户自定义，vm不支持，没有hpk下需求；由支持当前通知是不是支持跨os的，跨os是vm，不跨是um
    int32_t (*ndDestroy)(uint64_t object);
    uint32_t (*ndWait)(uint64_t object, struct timespec *timeout, DHF_NOTIFY_WAIT_FLAGS flags);
    // 不支持ndwake，使用dhfNotifyNotify和dhfNotifySelf
    int32_t (*ndWake)(uint64_t object, DHF_NOTIFY_EVENT_TYPE type);
    uint32_t (*dhfNotifyHeeCreate)(const char *name, uint32_t flags, uint64_t *object);
    uint32_t (*notifyObject2Xd)(uint64_t object);
    uint64_t (*notifyXd2Object)(uint32_t xd);
    uint32_t (*dhfNotifyServerCreate)(DHF_NotifyServerAddr *addr, uint32_t flags, uint64_t *xdObj);
    uint32_t (*dhfNotifyNotify)(uint64_t object);
    // 数据通道自唤醒，仅支持非 HPE_VNOTIFY_SHARED，其它用 DbVnotifyNotify
    uint32_t (*dhfNotifySelf)(uint64_t object);
    int32_t (*chanSendEvent)(int32_t chanId, int32_t event);
    uint32_t (*chanBusyLoopSet)(uint32_t localChnId, uint32_t busyLoopTimeUs);
    // retval true VM模式，flase UM模式
    bool (*notifyOsInterIsSupport)(void);
} g_gmdbAdptPipeHpe;

ControlChannelInfoT g_gmdbControlChannelInfo[MAX_INSTANCE_NUM] = {0};
static DbSpinLockT g_gmdbControlChannelInfoLock = {0};

int32_t DHF_XpollCreateStub(int32_t size)
{
    DB_UNUSED(size);
    return 0;
}

int32_t DHF_XpollDestroyStub(int32_t xpId)
{
    DB_UNUSED(xpId);
    return 0;
}
int32_t DHF_XpollCtlStub(int32_t xpId, int32_t op, DHF_Xd xd, const DHF_XpollEvent *event)
{
    DB_UNUSED(xpId);
    DB_UNUSED(op);
    DB_UNUSED(xd);
    DB_UNUSED(event);
    return 0;
}

int32_t DHF_XpollWaitStub(int32_t xpId, const DHF_XpollEvent *ev, int32_t maxEvents, int32_t timeout)
{
    DB_UNUSED(xpId);
    DB_UNUSED(ev);
    DB_UNUSED(maxEvents);
    DB_UNUSED(timeout);
    return 0;
}

int32_t HpeChanidToNdStub(int32_t vpId)
{
    DB_UNUSED(vpId);
    return 0;
}

int32_t HpeChannelSendEventStub(int32_t chanId, int32_t event)
{
    DB_UNUSED(chanId);
    DB_UNUSED(event);
    return 0;
}

uint32_t HpeChanBusyLoopSetStub(uint32_t localChnId, uint32_t busyLoopTimeUs)
{
    DB_UNUSED(localChnId);
    DB_UNUSED(busyLoopTimeUs);
    return 0;
}

bool DhfNotifyOsInterIsSupport(void)
{
    return true;
}

Status DbAdptRegHpePipeFuncs(void *adapter)
{
    const FuncTableItemT table[] = {
        {&g_gmdbAdptPipeHpe.chanOpen, "DHF_ChannelOpen"},
        {&g_gmdbAdptPipeHpe.chanOpenByName, "DHF_ChannelOpenByName"},
        {&g_gmdbAdptPipeHpe.chanRecv, "DHF_ChannelShmRecv"},
        {&g_gmdbAdptPipeHpe.chanSend, "DHF_ChannelShmSend"},
        {&g_gmdbAdptPipeHpe.chanDestroy, "DHF_ChannelClose"},
        {&g_gmdbAdptPipeHpe.chanGetState, "DHF_ChannelStateGet"},
        {&g_gmdbAdptPipeHpe.ctlChanCreateByPara, "DHF_ChannelCreateByPara"},
        {&g_gmdbAdptPipeHpe.ctlChanRecv, "DHF_ChannelShmRecv"},
        {&g_gmdbAdptPipeHpe.ctlChanDestroy, "DHF_ChannelClose"},
        {&g_gmdbAdptPipeHpe.chanGetItemsNum, "DHF_ChannelRxNumGet"},
        {&g_gmdbAdptPipeHpe.chanGetPeerItemsNum, "DHF_ChannelTxNumGet"},
        {&g_gmdbAdptPipeHpe.chanGetRingLen, "DHF_ChannelLenGet"},
        {&g_gmdbAdptPipeHpe.ctlChanGetItemsNum, "DHF_ChannelRxNumGet"},
        {&g_gmdbAdptPipeHpe.chanGetOwnerInfo, "DHF_ChannelCredGet"},
        {&g_gmdbAdptPipeHpe.globalRdtsc, "DhfRdtsc"},
        {&g_gmdbAdptPipeHpe.ndDestroy, "DHF_NotifyDestroy"},
        {&g_gmdbAdptPipeHpe.ndWait, "DHF_NotifyBlock"},
        {&g_gmdbAdptPipeHpe.ndWake, "DHF_NotifyEvent"},
        {&g_gmdbAdptPipeHpe.dhfNotifyServerCreate, "DHF_NotifyServerCreate"},
        {&g_gmdbAdptPipeHpe.dhfNotifyHeeCreate, "DHF_NotifyHeeCreate"},
        {&g_gmdbAdptPipeHpe.notifyObject2Xd, "DHF_NotifyObject2Xd"},
        {&g_gmdbAdptPipeHpe.notifyXd2Object, "DHF_NotifyXd2Object"},
        // 一发一收双向通知场景：唤醒对端;X发一收单向通知场景：客户端：唤醒server，服务端：唤醒自己
        {&g_gmdbAdptPipeHpe.dhfNotifyNotify, "DHF_NotifyNotify"},
        // 一发一收双向通知场景：唤醒自己
        {&g_gmdbAdptPipeHpe.dhfNotifySelf, "DHF_NotifySelf"},
    };
    Status ret = DbAdptLoadFunc(adapter, table, ELEMENT_COUNT(table));
    if (ret != GMERR_OK) {
        return ret;
    }

    DbAdptLoadFuncWithStub(adapter, (void **)&g_gmdbAdptPipeHpe.dhfXpollCreate, "DHF_XpollCreate", DHF_XpollCreateStub);
    DbAdptLoadFuncWithStub(adapter, (void **)&g_gmdbAdptPipeHpe.dhfXpollCtl, "DHF_XpollCtl", DHF_XpollCtlStub);
    DbAdptLoadFuncWithStub(adapter, (void **)&g_gmdbAdptPipeHpe.dhfXpollWait, "DHF_XpollWait", DHF_XpollWaitStub);
    DbAdptLoadFuncWithStub(
        adapter, (void **)&g_gmdbAdptPipeHpe.dhfXpollDestroy, "DHF_XpollDestroy", DHF_XpollDestroyStub);

    DbAdptLoadFuncWithStub(
        adapter, (void **)&g_gmdbAdptPipeHpe.chanidToNd, "DHF_ChannelVnotifyFdGet", HpeChanidToNdStub);
    DbAdptLoadFuncWithStub(
        adapter, (void **)&g_gmdbAdptPipeHpe.chanSendEvent, "DhfChannelSendEvent", HpeChannelSendEventStub);
    DbAdptLoadFuncWithStub(
        adapter, (void **)&g_gmdbAdptPipeHpe.chanBusyLoopSet, "DHF_ChannelBusyLoopSet", HpeChanBusyLoopSetStub);
    DbAdptLoadFuncWithStub(adapter, (void **)&g_gmdbAdptPipeHpe.notifyOsInterIsSupport, "DHF_NotifyOsInterIsSupport",
        DhfNotifyOsInterIsSupport);
    return GMERR_OK;
}

struct ProtocolPipe {
    int32_t chanId;
};

static void RecordConnCycle(const char *channelName, int32_t chanId, PipeCycleT *pipeCycle)
{
    pipeCycle->endCycle = DbRdtsc();
    uint64_t spendTimes = DbToUseconds(pipeCycle->endCycle - pipeCycle->startCycle);
    if (spendTimes < USECONDS_IN_SECOND) {
        return;
    }
    DB_LOG_WARN(GMERR_CONNECTION_TIMED_OUT,
        "takes a long time to establish connection: channelName:%s, channelId:%" PRId32 ", spendTotalTime:%" PRIu64
        "(us), openTime:%" PRIu64 "(us), createTime:%" PRIu64 "(us), sendTime:%" PRIu64 "(us), recvTime:%" PRIu64
        "(us).",
        channelName, chanId, spendTimes, DbToUseconds(pipeCycle->step0Cycle - pipeCycle->startCycle),
        DbToUseconds(pipeCycle->step1Cycle - pipeCycle->step0Cycle),
        DbToUseconds(pipeCycle->step2Cycle - pipeCycle->step1Cycle),
        DbToUseconds(pipeCycle->endCycle - pipeCycle->step2Cycle));
}

static void RecordConnAcceptCycle(int32_t cliChanId, int32_t serverChanId, PipeCycleT *pipeCycle)
{
    pipeCycle->endCycle = DbRdtsc();
    uint64_t spendTimes = DbToUseconds(pipeCycle->endCycle - pipeCycle->startCycle);
    if (spendTimes < 2 * USECONDS_IN_SECOND) {  // 2s
        return;
    }
    DB_LOG_WARN(GMERR_CONNECTION_TIMED_OUT,
        "takes a long time to accept connection: cliChanId:%" PRId32 ", serverChanId:%" PRId32
        ", spendTotalTime:%" PRIu64 "(us), initTime:%" PRIu64 "(us), recvTime:%" PRIu64 "(us), openTime:%" PRIu64
        "(us), sendTime:%" PRIu64 "(us).",
        cliChanId, serverChanId, spendTimes, DbToUseconds(pipeCycle->step0Cycle - pipeCycle->startCycle),
        DbToUseconds(pipeCycle->step1Cycle - pipeCycle->step0Cycle),
        DbToUseconds(pipeCycle->step2Cycle - pipeCycle->step1Cycle),
        DbToUseconds(pipeCycle->endCycle - pipeCycle->step2Cycle));
}

Status DbNotifyServerCreate(DHF_NotifyServerAddr *addr, uint32_t flags, int32_t *vnoFd)
{
    uint64_t object = 0;
    uint32_t ret = g_gmdbAdptPipeHpe.dhfNotifyServerCreate(addr, flags, &object);
    if (ret != HPE_CHANNEL_SUCCESS) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
            "Create server notify, ret %" PRId32 ", object %" PRId64 ", os ret: %" PRId32 ".", ret, object, errno);
        return GMERR_INTERNAL_ERROR;
    }
    *vnoFd = DbNotifyObject2Xd(object);
    return GMERR_OK;
}

Status DbNotifyClientCreate(DHF_NotifyServerAddr *addr, DHF_NOTIFY_DOMAIN domain, uint32_t flags, int32_t *vnoFd)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status DbVnotifyInit(void)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

int32_t DbNotifyObject2Xd(uint64_t object)
{
    // 返回的xd不会超过int最大值
    return (int32_t)g_gmdbAdptPipeHpe.notifyObject2Xd(object);
}

uint64_t DbNotifyXd2Object(int32_t xd)
{
    return g_gmdbAdptPipeHpe.notifyXd2Object((uint32_t)xd);
}

int32_t DbChanGetItemNum(const DbPipeT *pipe)
{
    return (int32_t)g_gmdbAdptPipeHpe.chanGetItemsNum((uint32_t)pipe->protoPipe->chanId);
}

Status DbNotifyHeeCreate(const char *name, uint32_t flags, int32_t *vnoFd)
{
    DB_POINTER2(name, vnoFd);
    uint64_t object = DB_INVALID_ID64;
    uint32_t ret = g_gmdbAdptPipeHpe.dhfNotifyHeeCreate(name, flags, &object);
    if (SECUREC_UNLIKELY(ret != DHF_OK)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
            "Create vnotify, name %s, object %" PRIu64 ",  os ret no: %" PRId32 ", flags %" PRIu32 ", hpe ret %" PRId32
            ", os ret: %" PRId32 ".",
            name, object, (int32_t)errno, flags, ret, errno);
        return GMERR_INTERNAL_ERROR;
    }
    *vnoFd = DbNotifyObject2Xd(object);
    return GMERR_OK;
}

Status DbVnotifyDestroy(int32_t vnoFd)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status DbVnotifySelf(int32_t vnoFd)
{
    uint64_t object = DbNotifyXd2Object(vnoFd);
    uint32_t ret = g_gmdbAdptPipeHpe.dhfNotifySelf(object);
    if (SECUREC_UNLIKELY(ret != DHF_OK)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
            "Dhf notify self, vnoFd %" PRId32 ", object %" PRIu64 ", ret %" PRIu32 ", os ret: %" PRId32 ".", vnoFd,
            object, ret, errno);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status DbVnotifyNotify(int32_t vnoFd)
{
    uint64_t object = DbNotifyXd2Object(vnoFd);
    uint32_t ret = g_gmdbAdptPipeHpe.dhfNotifyNotify(object);
    if (SECUREC_UNLIKELY(ret != DHF_OK)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
            "Dhf notify, vnoFd %" PRId32 ", object %" PRIu64 ", ret %" PRIu32 ", os ret: %" PRId32 ".", vnoFd, object,
            ret, errno);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status DbVnotifyBlock(int32_t vnoFd, struct timespec *timeout, uint32_t flags)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

int32_t DbNdDestroy(int32_t nd)
{
    uint64_t object = DbNotifyXd2Object(nd);
    int32_t ret = g_gmdbAdptPipeHpe.ndDestroy(object);
    if (SECUREC_UNLIKELY(ret != DHF_OK)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
            "Server notify destroy, ret %" PRId32 ", object %" PRId64 ", nd %" PRId32 ", os ret: %" PRId32 ".", ret,
            object, nd, errno);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status DbNdWait(int32_t nd, struct timespec *timeout, DHF_NOTIFY_WAIT_FLAGS flags)
{
    uint64_t object = DbNotifyXd2Object(nd);
    uint32_t ret = g_gmdbAdptPipeHpe.ndWait(object, timeout, flags);
    if (ret != DHF_OK && ret != DHF_ETIME) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
            "Nd wait, object %" PRIu64 ", flags %" PRIu32 ", hpe ret %" PRIu32 ", os ret: %" PRId32 ".", object, flags,
            ret, errno);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status DbNdBind(int32_t nd)
{
    return GMERR_OK;
}

Status DbNdWake(int32_t nd, DHF_NOTIFY_EVENT_TYPE wakeType)
{
    uint64_t object = DbNotifyXd2Object(nd);
    int32_t ret = g_gmdbAdptPipeHpe.ndWake(object, wakeType);
    if (ret < 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
            "NdWake, ret %" PRId32 ", nd %" PRId32 ", wakeType %" PRIu32 ", os ret: %" PRId32 ".", ret, nd,
            (uint32_t)wakeType, errno);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status DbNotifyFdCreate(uint32_t id, int32_t *nfd)
{
    DB_POINTER(nfd);
    char name[DB_NOTIFY_CHANNEL_NAME_MAX_LEN] = {0};
    int32_t ret = snprintf_s(name, DB_NOTIFY_CHANNEL_NAME_MAX_LEN, DB_NOTIFY_CHANNEL_NAME_MAX_LEN - 1,
        DB_NOTIFY_CHANNEL_NAME "%" PRIX32, id);
    if (ret < 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "hpe make notify fd name, id %" PRIX32 ", ret %" PRId32, id, ret);
        return GMERR_INTERNAL_ERROR;
    }
    uint32_t ndFlag = (uint32_t)(HPE_VNOTIFY_CREATE | HPE_VNOTIFY_PERSISTENT);
    int32_t nd = DB_INVALID_ND;
    Status dbRet = DbNotifyHeeCreate(name, ndFlag, &nd);
    if (dbRet != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Create nd for notify fd! os ret: %" PRId32, (int32_t)errno);
        return GMERR_INTERNAL_ERROR;
    }
    *nfd = nd;
    return GMERR_OK;
}

Status DbNotifyFdClose(int32_t nfd)
{
    return DbNdDestroy(nfd);
}

Status DbNotifyFdWrite(int32_t nfd)
{
    Status ret = DbVnotifyNotify(nfd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "write nd for notify fd! os ret: %" PRId32, (int32_t)errno);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status DbNotifyFdRead(int32_t nfd)
{
    struct timespec ts = {.tv_sec = 0, .tv_nsec = 1000};
    Status ret = DbNdWait(nfd, &ts, DHF_NOTIFY_BLOCK);
    if (ret == -1) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "read nd for notify fd! os ret: %" PRId32, (int32_t)errno);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status DbNotifyFdSendFd(DbPipeT *pipe, int32_t nfd)
{
    return GMERR_OK;
}

Status DbNotifyFdRecvFd(DbPipeT *pipe, int32_t *nfd)
{
    return GMERR_OK;
}

inline Status DbAdptEventWakeSelf(int32_t fd, eventfd_t value)
{
    return DbAdptEventFdWrite(fd, value);
}

Status DbPipeOpenListen(DbPipeT *pipe, const DbLctrT *lctr, DbPipeMemParasT *memParas)
{
    DB_POINTER2(pipe, lctr);
    DbPipeInit(pipe, DbPipeGetMemFuncs(memParas), true);
    ProtocolPipeT *newPipe = (ProtocolPipeT *)DbPipeMemAlloc(&pipe->memFuncs, sizeof(ProtocolPipeT));
    if (newPipe == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Pip open listen. os ret: %" PRId32, (int32_t)errno);
        return GMERR_OUT_OF_MEMORY;
    }

    DHF_ChannelPara para = (DHF_ChannelPara){.type = DHF_CTRL_CHN_TYPE,
        .reuseMode = memParas->isReuse ? DHF_CHN_NAMED_CHN_FORCE_REUSE : DHF_CHN_NAMED_CHN_UNIQUE,
        .ringMode = {DHF_CHN_RING_MODE_MPSC, DHF_CHN_RING_MODE_MPSC},
        .dfxFlags = 0};
    int32_t ret = g_gmdbAdptPipeHpe.ctlChanCreateByPara(
        lctr->hpeChannel.channelName, &para, CTL_CHANNEL_MSG_NUM, (uint32_t *)&newPipe->chanId);
    if (ret != DHF_OK) {
        DbPipeMemFree(&pipe->memFuncs, newPipe);
        DB_LOG_ERROR(GMERR_CONNECTION_RESET_BY_PEER,
            "Create channel by para, reuseMode: %" PRIu32 ", name %s, ret:%" PRId32 " ,os ret: %" PRId32 ".",
            (uint32_t)para.reuseMode, lctr->hpeChannel.channelName, ret, (int32_t)errno);
        return GMERR_CONNECTION_RESET_BY_PEER;
    }

    pipe->protoPipe = newPipe;
    return GMERR_OK;
}

static Status OpenHpeChannelByName(const char *chanName, int32_t *controlChanId)
{
    uint32_t channId = DB_INVALID_ID32;
    uint32_t ret = g_gmdbAdptPipeHpe.chanOpenByName(chanName, &channId);
    if (ret != 0) {
        DB_LOG_ERROR(ChanTransformStatus((int32_t)channId),
            "Open channel by name, ret %" PRIu32 ", name %s, os ret: %" PRId32 ", channId: %" PRId32, ret, chanName,
            (int32_t)errno, (int32_t)channId);
        return ChanTransformStatus((int32_t)channId);
    }
    *controlChanId = (int32_t)channId;
    return GMERR_OK;
}

void ChanSendLog(int32_t ret, int32_t controlChanId, int32_t chanId, const char *chanName, uint64_t time)
{
    DB_LOG_ERROR(ChanTransformStatus(ret),
        "Channel send, ret:%" PRId32 ", ctrlChanId-ChanId-Name:%" PRId32 "-%" PRId32 "-%s, takeMs:%" PRIu64
        ", os ret: %" PRId32,
        ret, controlChanId, chanId, chanName, time, (int32_t)errno);
}

void ChanRecvLog(int32_t ret, int32_t controlChanId, uint64_t handshake, const char *chanName, uint64_t time)
{
    Status status = (ret != HPE_CHANNEL_SUCCESS) ? ChanTransformStatus(ret) : GMERR_DATA_EXCEPTION;
    DB_LOG_ERROR(status,
        "Channel recv, ret-handshake: %" PRIu32 "-%" PRId64 ", ctrlChanId-Name: %" PRId32 "-%s, takeMs:%" PRIu64
        ", os ret: %" PRId32,
        ret, handshake, controlChanId, chanName, time, (int32_t)errno);
}

static uint32_t DbGetOrCacheCtrlChannelId(const DbLctrT *srvLctr)
{
    int32_t ctrlChannelId = (int32_t)DbGetCtrlChannelIdFromCache(srvLctr);
    if (ctrlChannelId != (int32_t)DB_INVALID_ID32) {
        return ctrlChannelId;
    }
    DbSpinLock(&g_gmdbControlChannelInfoLock);
    ctrlChannelId = DbGetCtrlChannelIdFromCache(srvLctr);
    if (ctrlChannelId != (int32_t)DB_INVALID_ID32) {
        DbSpinUnlock(&g_gmdbControlChannelInfoLock);
        return ctrlChannelId;
    }

    Status ret = OpenHpeChannelByName(srvLctr->hpeChannel.channelName, &ctrlChannelId);
    if (ret != GMERR_OK) {
        DbSpinUnlock(&g_gmdbControlChannelInfoLock);
        return DB_INVALID_ID32;
    }
    DbFillCtrlChannelInfoToCache(srvLctr, ctrlChannelId);
    DbSpinUnlock(&g_gmdbControlChannelInfoLock);
    return ctrlChannelId;
}

Status DbPipeConnect(DbPipeT *pipe, const DbLctrT *srvLctr, const DbLctrT *cliLctr, uint32_t bufSize,
    DbPipeMemParasT *memParas, bool nonBlock)
{
    DB_POINTER2(pipe, srvLctr);
    const char *chanName = srvLctr->hpeChannel.channelName;
    PipeCycleT pipeCycle = {0};
    pipeCycle.startCycle = DbRdtsc();
    int32_t controlChanId = (int32_t)DbGetOrCacheCtrlChannelId(srvLctr);
    pipeCycle.step0Cycle = DbRdtsc();
    if (controlChanId == (int32_t)DB_INVALID_ID32) {
        return GMERR_CONNECTION_FAILURE;
    }
    DbPipeInit(pipe, DbPipeGetMemFuncs(memParas), nonBlock);
    ProtocolPipeT *newPipe = (ProtocolPipeT *)DbPipeMemAlloc(&pipe->memFuncs, sizeof(ProtocolPipeT));
    if (newPipe == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Pipe connect, ctrlChannId-Name:%" PRId32 "-%s,useMemCtx %" PRIu8,
            controlChanId, chanName, (uint8_t)pipe->memFuncs.useMemctx);
        return GMERR_OUT_OF_MEMORY;
    }

    DHF_ChannelPara para = (DHF_ChannelPara){
        .type = DHF_DATA_CHN_GUEST_INNER_TYPE, .ringMode = {DHF_CHN_RING_MODE_MPSC, DHF_CHN_RING_MODE_MPSC}};
    int32_t ret = g_gmdbAdptPipeHpe.ctlChanCreateByPara(NULL, &para, bufSize, (uint32_t *)&newPipe->chanId);
    if (ret != DHF_OK) {
        DB_LOG_ERROR(GMERR_CONNECTION_FAILURE,
            "chanCreate, size %" PRIu32 ", ctrlChanId-mgChanId-Name: %" PRId32 "-%" PRId32 "-%s, no:%" PRId32, bufSize,
            controlChanId, newPipe->chanId, chanName, (int32_t)errno);
        DbPipeMemFree(&pipe->memFuncs, newPipe);
        return GMERR_CONNECTION_FAILURE;
    }

    pipeCycle.step1Cycle = DbRdtsc();
    ret = (int32_t)g_gmdbAdptPipeHpe.chanSend((uint32_t)controlChanId, newPipe->chanId, 0);
    if (ret != HPE_CHANNEL_SUCCESS) {
        ChanSendLog(ret, controlChanId, newPipe->chanId, chanName, DbToMseconds(DbRdtsc() - pipeCycle.step1Cycle));
        (void)g_gmdbAdptPipeHpe.chanDestroy((uint32_t)newPipe->chanId);
        DbPipeMemFree(&pipe->memFuncs, newPipe);
        return ChanTransformStatus(ret);
    }

    uint64_t handshake = 0;
    uint32_t len = 0;
    pipeCycle.step2Cycle = DbRdtsc();
    struct timespec ts = {.tv_sec = HPE_CHAN_CONNECT_TIMEOUT_SEC, .tv_nsec = 0};  // 正常环境5s，asan环境600s
    ret = (int32_t)g_gmdbAdptPipeHpe.chanRecv((uint32_t)newPipe->chanId, &ts, HPE_CHANNEL_BLOCK, &handshake, &len);
    if (ret != HPE_CHANNEL_SUCCESS || handshake != HPE_CHAN_CONNECT_SUCCEED) {
        ChanRecvLog(ret, controlChanId, handshake, chanName, DbToMseconds(DbRdtsc() - pipeCycle.step2Cycle));
        (void)g_gmdbAdptPipeHpe.chanDestroy((uint32_t)newPipe->chanId);
        DbPipeMemFree(&pipe->memFuncs, newPipe);
        return ChanTransformStatus(ret);
    }
    pipe->protoPipe = newPipe;
    RecordConnCycle(srvLctr->hpeChannel.channelName, newPipe->chanId, &pipeCycle);
    return GMERR_OK;
}

void DbPipeClose(DbPipeT *pipe)
{
    DB_POINTER2(pipe, pipe->protoPipe);
    int32_t ret = (int32_t)g_gmdbAdptPipeHpe.chanDestroy((uint32_t)pipe->protoPipe->chanId);
    if (ret != HPE_CHANNEL_SUCCESS) {
        DB_LOG_ERROR(ChanTransformStatus(ret), "Destroy Channel, ret %d chanId %d, os ret: %" PRId32 ".", ret,
            pipe->protoPipe->chanId, errno);
    }
    DbPipeMemFree(&pipe->memFuncs, pipe->protoPipe);
    pipe->protoPipe = NULL;
}

inline void DbInitCltControlChannelInfo(void)
{
    DbSpinInit(&g_gmdbControlChannelInfoLock);
    for (uint32_t i = 0; i < MAX_INSTANCE_NUM; i++) {
        g_gmdbControlChannelInfo[i].controlChannelId = DB_INVALID_ID32;
    }
}

void DbUninitCltControlChannelInfo(void)
{
    DbSpinLock(&g_gmdbControlChannelInfoLock);
    for (uint32_t i = 0; i < MAX_INSTANCE_NUM; i++) {
        if (g_gmdbControlChannelInfo[i].controlChannelId != DB_INVALID_ID32) {
            int32_t ret =
                (int32_t)g_gmdbAdptPipeHpe.chanDestroy((uint32_t)g_gmdbControlChannelInfo[i].controlChannelId);
            if (ret != HPE_CHANNEL_SUCCESS) {
                DB_LOG_ERROR(
                    GMERR_DATA_EXCEPTION, "Channel Destroy with erno: %" PRIi32 ", osno: %" PRIu32, ret, errno);
            }
            g_gmdbControlChannelInfo[i].controlChannelId = DB_INVALID_ID32;
        }
    }
    DbSpinUnlock(&g_gmdbControlChannelInfoLock);
    DbSpinInit(&g_gmdbControlChannelInfoLock);
}

static Status HpeCtlPipeRecv(const DbPipeT *srvPipe, DbPipeT *cliPipe, uint64_t *shmAddr, uint32_t *len)
{
    struct timespec ts = {.tv_sec = HPE_CHANNEL_MAX_RECV_DELAY, .tv_nsec = 0};  // 正常环境5s，asan环境600s
    int32_t hpeRet = (int32_t)g_gmdbAdptPipeHpe.ctlChanRecv(
        (uint32_t)srvPipe->protoPipe->chanId, &ts, HPE_CHANNEL_NOBLOCK, shmAddr, len);
    if (hpeRet != HPE_CHANNEL_SUCCESS) {
        if (hpeRet != HPE_CHANNEL_RECV_TIMEOUT) {
            DB_LOG_ERROR(ChanTransformStatus(hpeRet),
                "|connect stage1| server accept, ret: %" PRId32 ", srvChanId: %" PRId32 ", os ret: %" PRId32 ".",
                hpeRet, srvPipe->protoPipe->chanId, errno);
        }
        return hpeRet;
    }
    return GMERR_OK;
}

#define BUSY_LOOP_TIME_US_DEFAULT 0  // 为0，则使用hpe接口内部默认值100us; 范围在0-200us
static Status HpeChanOpen(const DbPipeT *srvPipe, DbPipeT *cliPipe, uint64_t shmAddr, bool needSetLoop)
{
    cliPipe->handshakeInfo.remainHandshakeReq = g_gmdbAdptPipeHpe.ctlChanGetItemsNum(srvPipe->protoPipe->chanId);
    cliPipe->handshakeInfo.recvHandshakeReqTimestamp = DbRdtsc();
    cliPipe->protoPipe->chanId = (int32_t)shmAddr;
    uint32_t srvChanId = DB_INVALID_ID32;
    uint32_t ret = g_gmdbAdptPipeHpe.chanOpen((uint32_t)cliPipe->protoPipe->chanId, &srvChanId);
    if (ret != 0) {
        DB_LOG_ERROR(ChanTransformStatus((int32_t)srvChanId),
            "|connect stage1| open srv channel with cltChanId %" PRId32 ", srvChanId %" PRId32 "os ret:%" PRId32
            ", hpe ret:%" PRIu32,
            cliPipe->protoPipe->chanId, srvPipe->protoPipe->chanId, errno, ret);
        return ChanTransformStatus((int32_t)srvChanId);
    }
    cliPipe->protoPipe->chanId = (int32_t)srvChanId;

    uint32_t hpeRet = needSetLoop ? g_gmdbAdptPipeHpe.chanBusyLoopSet(srvChanId, (uint32_t)BUSY_LOOP_TIME_US_DEFAULT) :
                                    (uint32_t)HPE_CHANNEL_SUCCESS;
    if (hpeRet != HPE_CHANNEL_SUCCESS) {
        DB_LOG_ERROR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
            "Channel set busy loop, chanId %" PRIu32 ", hpe ret %" PRIu32 ", os ret: %" PRId32 ".", srvChanId, hpeRet,
            errno);
        (void)g_gmdbAdptPipeHpe.chanDestroy((uint32_t)cliPipe->protoPipe->chanId);
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    return GMERR_OK;
}

Status DbPipeAccept(const DbPipeT *srvPipe, DbPipeT *cliPipe, PipeAcceptParamT param)
{
    DB_POINTER(srvPipe);
    uint64_t shmAddr = 0;
    uint32_t len;

    PipeCycleT pipeCycle = {0};
    pipeCycle.startCycle = DbRdtsc();
    DbPipeInit(cliPipe, srvPipe->memFuncs, param.nonBlock);
    cliPipe->protoPipe = (ProtocolPipeT *)DbPipeMemAlloc(&srvPipe->memFuncs, sizeof(ProtocolPipeT));
    if (cliPipe->protoPipe == NULL) {
        DB_LOG_ERROR(
            GMERR_OUT_OF_MEMORY, "|connect stage1| pip accept, size: %" PRIu32, (uint32_t)sizeof(ProtocolPipeT));
        return GMERR_OUT_OF_MEMORY;
    }
    pipeCycle.step0Cycle = DbRdtsc();
    Status ret = HpeCtlPipeRecv(srvPipe, cliPipe, &shmAddr, &len);
    if (ret != GMERR_OK) {  // hpe ret
        DbPipeMemFree(&srvPipe->memFuncs, cliPipe->protoPipe);
        cliPipe->protoPipe = NULL;
        return ChanTransformStatus(ret);
    }
    pipeCycle.step1Cycle = DbRdtsc();

    ret = HpeChanOpen(srvPipe, cliPipe, shmAddr, param.needSetLoop);
    if (ret != GMERR_OK) {
        DbPipeMemFree(&srvPipe->memFuncs, cliPipe->protoPipe);
        cliPipe->protoPipe = NULL;
        return ret;
    }
    pipeCycle.step2Cycle = DbRdtsc();

    int32_t hpeRet = (int32_t)g_gmdbAdptPipeHpe.chanSend(
        (uint32_t)cliPipe->protoPipe->chanId, HPE_CHAN_CONNECT_SUCCEED, sizeof(uint64_t));
    cliPipe->handshakeInfo.sendHandshakeRsqTimestamp = DbRdtsc();
    if (hpeRet != HPE_CHANNEL_SUCCESS) {
        DB_LOG_ERROR(ChanTransformStatus(hpeRet),
            "|connect stage1| |handshake| srv send first handshake response, hpeRet %" PRId32 " cltChanId %" PRId32
            ",srvChanId %" PRId32 "os ret: %" PRId32,
            hpeRet, cliPipe->protoPipe->chanId, srvPipe->protoPipe->chanId, (int32_t)errno);
        (void)g_gmdbAdptPipeHpe.chanDestroy((uint32_t)cliPipe->protoPipe->chanId);
        DbPipeMemFree(&srvPipe->memFuncs, cliPipe->protoPipe);
        cliPipe->protoPipe = NULL;
        return ChanTransformStatus(hpeRet);
    }
    RecordConnAcceptCycle(cliPipe->protoPipe->chanId, srvPipe->protoPipe->chanId, &pipeCycle);
    return GMERR_OK;
}

Status DbGetCredit(const DbPipeT *pipe, char *user, uint32_t len, char *auth, uint32_t authLen)
{
    DB_UNUSED5(pipe, user, len, auth, authLen);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status DbGetPeerName(const DbPipeT *cliPipe, char *ip, size_t size, uint16_t *port)
{
    return GMERR_GET_THIRD_PARTY_FUNCTION_FAILED;
}

Status DbAdptPipeSend(const DbPipeT *pipe, PipeBufferT *buffer)
{
    DB_POINTER2(pipe, buffer);
    int32_t ret = (int32_t)g_gmdbAdptPipeHpe.chanSend(
        (uint32_t)pipe->protoPipe->chanId, *(uint64_t *)&buffer->shmPtr, buffer->validSize);
    buffer->sndPos = (ret == HPE_CHANNEL_SUCCESS) ? buffer->validSize : 0;
    return ChanTransformStatus(ret);
}

Status DbAdptPipeTrySend(const DbPipeT *pipe, PipeBufferT *buffer)
{
    DB_POINTER2(pipe, buffer);
    int32_t ret = (int32_t)g_gmdbAdptPipeHpe.chanSend(
        (uint32_t)pipe->protoPipe->chanId, *(uint64_t *)&buffer->shmPtr, buffer->validSize);
    buffer->sndPos = (ret == HPE_CHANNEL_SUCCESS) ? buffer->validSize : 0;
    return ChanTransformStatus(ret);
}

Status DbUserLoginVerify(const char *userName, const char *pwd)
{
    DB_UNUSED2(userName, pwd);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

ALWAYS_INLINE_C static Status HpePipeRecvInner(
    const DbPipeT *pipe, PipeBufferT *buffer, struct timespec ts, uint32_t recvFlag)
{
    int32_t ret = (int32_t)g_gmdbAdptPipeHpe.chanRecv(
        (uint32_t)pipe->protoPipe->chanId, &ts, recvFlag, (uint64_t *)&buffer->shmPtr, &buffer->validSize);
    Status gmerrRet = ChanTransformStatus(ret);
    if (SECUREC_UNLIKELY(ret != HPE_CHANNEL_SUCCESS)) {
        if (ret != HPE_CHANNEL_RECV_TIMEOUT && ret != HPE_CHANNEL_QUE_EMPTY) {
            DB_LOG_WARN(GMERR_INTERNAL_ERROR,
                "Channel recv. flag %" PRIu32 ", chanId %" PRId32 " osno %" PRId32 " hpe ret %" PRId32 ".", recvFlag,
                pipe->protoPipe->chanId, (int32_t)errno, ret);
        }
        buffer->validSize = 0;
        return gmerrRet;
    }

    return gmerrRet;
}

Status DbAdptPipeRecv(const DbPipeT *pipe, PipeBufferT *buffer)
{
    DB_POINTER2(pipe, buffer);

    struct timespec ts = {.tv_sec = pipe->recvTimeout / MSECONDS_IN_SECOND,
        .tv_nsec = (pipe->recvTimeout % MSECONDS_IN_SECOND) * NSECONDS_IN_MSECOND};

    return HpePipeRecvInner(pipe, buffer, ts, HPE_CHANNEL_BLOCK);
}

ALWAYS_INLINE_C Status DbAdptPipeTryRecv(const DbPipeT *pipe, PipeBufferT *buffer)
{
    DB_POINTER2(pipe, buffer);

    static const struct timespec ts = {.tv_sec = 0, .tv_nsec = 0};
    return HpePipeRecvInner(pipe, buffer, ts, HPE_CHANNEL_NOBLOCK);
}

static Status NotifyEpollOut(const DbPipeT *pipe)
{
    DB_POINTER(pipe);
    // 方案:当channel通道有超过CHAN_NOTIFY_THRESHOLD个消息未接收则通知对端
    int32_t unRecvSize = (int32_t)g_gmdbAdptPipeHpe.chanGetItemsNum((uint32_t)pipe->protoPipe->chanId);
    if (unRecvSize > CHAN_NOTIFY_THRESHOLD) {
        int32_t ret =
            (int32_t)g_gmdbAdptPipeHpe.chanSendEvent((uint32_t)pipe->protoPipe->chanId, ND_WAKE_EVENT_WRITEABLE);
        if (ret != HPE_CHANNEL_SUCCESS) {
            DB_LOG_ERROR(GMERR_CONNECTION_EXCEPTION, "Channel EventWakeup, ret %" PRId32 " chanId %" PRId32, ret,
                pipe->protoPipe->chanId);
            return GMERR_CONNECTION_EXCEPTION;
        }
    }
    return GMERR_OK;
}

Status DbAdptPipeRecvWithNotify(const DbPipeT *pipe, PipeBufferT *buffer)
{
    Status ret = DbAdptPipeRecv(pipe, buffer);
    if (ret == GMERR_OK) {
        ret = NotifyEpollOut(pipe);
    }
    return ret;
}

Status DbAdptPipeTryRecvWithNotify(const DbPipeT *pipe, PipeBufferT *buffer)
{
    Status ret = DbAdptPipeTryRecv(pipe, buffer);
    if (ret == GMERR_OK) {
        ret = NotifyEpollOut(pipe);
    }
    return ret;
}

Status DbPipeUnRecvMsgSize(const DbPipeT *pipe, uint32_t *unRecvSize)
{
    DB_POINTER2(pipe, unRecvSize);
    int32_t recvSize = (int32_t)g_gmdbAdptPipeHpe.chanGetItemsNum((uint32_t)pipe->protoPipe->chanId);
    if (recvSize < 0) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW,
            "Channel EventWakeup, ret %" PRId32 " chanId %" PRId32 ", os ret: %" PRId32 ".", recvSize,
            pipe->protoPipe->chanId, errno);
        return GMERR_FIELD_OVERFLOW;
    }
    *unRecvSize = recvSize;
    return GMERR_OK;
}

Status DbPipeUnRecvMsgSizeForCb(const DbPipeT *pipe, uint32_t *unRecvSize)
{
    DB_UNUSED(pipe);
    DB_UNUSED(unRecvSize);
    return GMERR_OK;
}

Status DbPipeSetRecvTimeout(DbPipeT *pipe, uint32_t timeout)
{
    DB_POINTER(pipe);
    pipe->recvTimeout = timeout;
    return GMERR_OK;
}

Status DbPipeSetSendTimeout(DbPipeT *pipe, uint32_t timeout)
{
    DB_POINTER(pipe);
    pipe->sendTimeout = timeout;
    return GMERR_OK;
}

Status DbPipeUnSendMsgSize(const DbPipeT *pipe, uint32_t *unSendSize)
{
    DB_POINTER2(pipe, unSendSize);
    *unSendSize = g_gmdbAdptPipeHpe.chanGetPeerItemsNum((uint32_t)pipe->protoPipe->chanId);
    return GMERR_OK;
}

Status DbPipeGetRecvBuffSize(const DbPipeT *pipe, uint32_t *recvBufLen)
{
    DB_POINTER2(pipe, recvBufLen);
    *recvBufLen = g_gmdbAdptPipeHpe.chanGetRingLen((uint32_t)pipe->protoPipe->chanId);
    return GMERR_OK;
}

Status DbPipeGetSendBuffSize(const DbPipeT *pipe, uint32_t *sendBufLen)
{
    DB_POINTER2(pipe, sendBufLen);
    *sendBufLen = g_gmdbAdptPipeHpe.chanGetRingLen((uint32_t)pipe->protoPipe->chanId);
    return GMERR_OK;
}

int32_t DbPipeGetEventFd(const DbPipeT *pipe)
{
    DB_POINTER(pipe);
    int32_t nd = g_gmdbAdptPipeHpe.chanidToNd((uint32_t)pipe->protoPipe->chanId);
    return nd;
}

Status DbPipeGetCred(const DbPipeT *pipe, DbCredT *cred)
{
    DB_POINTER2(pipe, cred);
    int32_t ret = (int32_t)g_gmdbAdptPipeHpe.chanGetOwnerInfo((uint32_t)pipe->protoPipe->chanId, cred);
    return ChanTransformStatus(ret);
}

void DbPipeGetPipeInfo(const DbPipeT *pipe, char *buf, uint32_t len)
{
    DB_POINTER2(pipe, buf);
    (void)snprintf_s(buf, len, len - 1, "type: hpe, srvFlag: 1, chanId: %" PRId32, pipe->protoPipe->chanId);
}

Status DbPipeSetNoBlock(DbPipeT *pipe, bool nonBlock)
{
    pipe->nonBlock = nonBlock;
    return GMERR_OK;
}

struct DbPipeMonitorHandle {
    int32_t vpollFd;
    DHF_XpollEvent *events;
};

typedef struct VpollEventEle {
    int32_t nd;
    DbPipeMonitorEventT *event;
} VpollEventEleT;

static struct {
    VpollEventEleT *ele;
    uint32_t mapCap;
    uint32_t mapCount;
    DbRWSpinLockT mapLock;  // 保护g_gmdbVpollEventMap的插入、删除与查找操作
    bool isInit;
} g_gmdbVpollEventMap;  // 初始化阶段不会产生并发，但是对于map的插入、删除以及查找需要加rwspinlock锁保护

inline static bool HpeChanIdValid(int32_t nd)
{
    return nd > HPE_INVALID_ND;
}

bool g_gmdbAdptIsVm = true;

static Status InitEventMap(const DbPipeMonitorParasT *paras)
{
    DB_POINTER(paras);
    if (g_gmdbVpollEventMap.isInit) {
        return GMERR_OK;
    }
    g_gmdbAdptIsVm = g_gmdbAdptPipeHpe.notifyOsInterIsSupport();
    uint32_t allocSize = paras->capacity * sizeof(VpollEventEleT);
    uint8_t *buf = (uint8_t *)paras->allocFunc(paras->memctx, allocSize);
    if (buf == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(buf, allocSize, 0, allocSize);
    g_gmdbVpollEventMap.ele = (VpollEventEleT *)buf;
    g_gmdbVpollEventMap.mapCap = paras->capacity;
    g_gmdbVpollEventMap.mapCount = 0;
    DbRWSpinInit(&g_gmdbVpollEventMap.mapLock);
    for (uint16_t i = 0; i < paras->capacity; i++) {
        g_gmdbVpollEventMap.ele[i].nd = HPE_INVALID_ND;
    }
    g_gmdbVpollEventMap.isInit = true;
    return GMERR_OK;
}

static bool EventMapInsert(int32_t key, DbPipeMonitorEventT *value)
{
    if (g_gmdbVpollEventMap.mapCount >= g_gmdbVpollEventMap.mapCap) {
        return false;
    }
    uint32_t idx = (uint32_t)key % g_gmdbVpollEventMap.mapCap;
    DbRWSpinWLock(&g_gmdbVpollEventMap.mapLock);
    while (HpeChanIdValid(g_gmdbVpollEventMap.ele[idx].nd)) {
        if (g_gmdbVpollEventMap.ele[idx].nd == key) {
            DbRWSpinWUnlock(&g_gmdbVpollEventMap.mapLock);
            return false;
        }
        idx++;
        idx %= g_gmdbVpollEventMap.mapCap;
    }
    g_gmdbVpollEventMap.ele[idx].nd = key;
    g_gmdbVpollEventMap.ele[idx].event = value;
    g_gmdbVpollEventMap.mapCount++;
    DbRWSpinWUnlock(&g_gmdbVpollEventMap.mapLock);
    return true;
}

ALWAYS_INLINE_C static int32_t EventMapFind(int32_t key)
{
    uint32_t idx = (uint32_t)key % g_gmdbVpollEventMap.mapCap;
    uint32_t src = idx;
    DbRWSpinRLock(&g_gmdbVpollEventMap.mapLock);
    while (true) {
        if (g_gmdbVpollEventMap.ele[idx].nd == key && HpeChanIdValid(g_gmdbVpollEventMap.ele[idx].nd)) {
            DbRWSpinRUnlock(&g_gmdbVpollEventMap.mapLock);
            return idx;
        } else {
            idx++;
            idx %= g_gmdbVpollEventMap.mapCap;
            if (idx == src) {
                break;
            }
        }
    }
    DbRWSpinRUnlock(&g_gmdbVpollEventMap.mapLock);
    return -1;
}

static void EventMapRemove(int32_t key)
{
    int32_t idx = EventMapFind(key);
    DbRWSpinWLock(&g_gmdbVpollEventMap.mapLock);
    if (idx == -1) {
        DbRWSpinWUnlock(&g_gmdbVpollEventMap.mapLock);
        DB_ASSERT(false);
        return;
    }
    g_gmdbVpollEventMap.ele[idx].nd = HPE_INVALID_ND;
    g_gmdbVpollEventMap.ele[idx].event = NULL;
    g_gmdbVpollEventMap.mapCount--;
    DbRWSpinWUnlock(&g_gmdbVpollEventMap.mapLock);
}

static void DbPipeMonitorInit(DbPipeMonitorT *monitor, const DbPipeMonitorParasT *paras, int32_t vpollFd)
{
    DB_POINTER2(monitor, paras);
    monitor->handle->vpollFd = vpollFd;
    monitor->maxEventNum = paras->capacity;
    DbSpinInit(&monitor->lock);
    (void)DbSemInit(&monitor->sem, THREAD_SEMAPHORE, 0);
    for (uint16_t i = 0; i < paras->capacity; i++) {
        monitor->events[i].id = i;
        monitor->events[i].next = i + 1;
    }
    monitor->allocFunc = paras->allocFunc;
    monitor->freeFunc = paras->freeFunc;
    monitor->memctx = paras->memctx;
    monitor->eventNum = 0;
    monitor->freeIdx = 0;
    monitor->userCtx = paras->userCtx;
}

static void DbPipeMonitorAllocMem(const DbPipeMonitorParasT *paras, DbPipeMonitorT **monitor)
{
    DB_POINTER3(paras, paras->allocFunc, paras->freeFunc);
    uint32_t flexSize = sizeof(DHF_XpollEvent) * paras->capacity + sizeof(DbPipeMonitorEventT) * paras->capacity;
    uint32_t allocSize = sizeof(DbPipeMonitorT) + sizeof(DbPipeMonitorHandleT) + flexSize;
    uint8_t *buf = (uint8_t *)paras->allocFunc(paras->memctx, allocSize);
    if (buf == NULL) {
        *monitor = NULL;
        return;
    }
    (void)memset_s(buf, allocSize, 0, allocSize);
    *monitor = (DbPipeMonitorT *)buf;
    uint32_t offset = sizeof(DbPipeMonitorT);
    (*monitor)->handle = (DbPipeMonitorHandleT *)(buf + offset);
    offset += sizeof(DbPipeMonitorHandleT);
    (*monitor)->handle->events = (DHF_XpollEvent *)(buf + offset);
    offset += sizeof(DHF_XpollEvent) * paras->capacity;
    (*monitor)->events = (DbPipeMonitorEventT *)(buf + offset);
}

DbPipeMonitorT *DbPipeMonitorCreate(const DbPipeMonitorParasT *paras)
{
    DB_POINTER3(paras, paras->allocFunc, paras->freeFunc);
    if (paras->capacity == 0) {
        return NULL;
    }
    int32_t xpollFd = g_gmdbAdptPipeHpe.dhfXpollCreate(paras->capacity);
    if (xpollFd == HPE_INVALID_VPOLL) {
        return NULL;
    }
    DbPipeMonitorT *monitor;
    DbPipeMonitorAllocMem(paras, &monitor);
    if (monitor == NULL) {
        (void)g_gmdbAdptPipeHpe.dhfXpollDestroy(xpollFd);
        return NULL;
    }

    DbPipeMonitorInit(monitor, paras, xpollFd);
    Status ret = InitEventMap(paras);
    if (ret != GMERR_OK) {
        monitor->freeFunc(monitor->memctx, monitor);
        (void)g_gmdbAdptPipeHpe.dhfXpollDestroy(xpollFd);
        return NULL;
    }
    return monitor;
}

static uint16_t DbPipeMonitorAllocEvent(DbPipeMonitorT *monitor)
{
    DB_POINTER(monitor);
    DbSpinLock(&monitor->lock);
    uint16_t id = monitor->freeIdx;
    DbPipeMonitorEventT *event = (id == monitor->maxEventNum) ? NULL : &monitor->events[id];
    if (event != NULL) {
        monitor->freeIdx = event->next;
        monitor->eventNum++;
        event->next = DB_INVALID_UINT16;
        event->pipe = NULL;
        event->procNum = 0;
        event->eventProc = NULL;
        event->userPara = NULL;
    }
    DbSpinUnlock(&monitor->lock);
    return id;
}

static void DbPipeMonitorFreeEvent(DbPipeMonitorT *monitor, uint16_t id)
{
    DB_POINTER(monitor);
    DbSpinLock(&monitor->lock);
    DbPipeMonitorEventT *event = &monitor->events[id];
    event->next = monitor->freeIdx;
    event->userPara = NULL;
    monitor->freeIdx = event->id;
    monitor->eventNum--;
    DbSpinUnlock(&monitor->lock);
}

static void DbPipeWakeupPipeMonitor(DbPipeMonitorT *monitor)
{
    // enventNum增加到1时唤醒
    if (monitor->eventNum == 1) {
        (void)DbSemPost(&monitor->sem);
    }
}

Status DbMonitorAddEvent(
    DbPipeMonitorT *monitor, const DbPipeT *pipe, DbSocket fd, DbPipeMonitorEventParaT *para, uint16_t *eventIdOut)
{
    DB_POINTER2(monitor, para);
    DB_ASSERT((para->mask & DB_PIPE_MONITOR_EVENT_WRITE) != DB_PIPE_MONITOR_EVENT_WRITE);

    // 绑定事件
    uint16_t eventId = DbPipeMonitorAllocEvent(monitor);
    if (eventId == monitor->maxEventNum) {
        DB_LOG_ERROR(GMERR_PROGRAM_LIMIT_EXCEEDED, "PipeMonitor alloc event, id %" PRIu16, eventId);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    DbPipeMonitorEventT *monitorEvent = &monitor->events[eventId];
    monitorEvent->userPara = para->userPara;
    monitorEvent->eventProc = para->eventProc;
    monitorEvent->pipe = pipe;
    DHF_XpollEvent event = {0};
    event.data.ptr = monitorEvent;
    event.events |= (uint32_t)DHF_XPOLL_HUP;
    if ((para->mask & DB_PIPE_MONITOR_EVENT_READ) == DB_PIPE_MONITOR_EVENT_READ) {
        event.events |= (uint32_t)DHF_XPOLL_IN;
    }
    if ((para->mask & DB_PIPE_MONITOR_EVENT_ERR) == DB_PIPE_MONITOR_EVENT_ERR) {
        event.events |= (uint32_t)DHF_XPOLL_IN;
    }
    if ((para->mask & DB_PIPE_MONITOR_EVENT_ET) == DB_PIPE_MONITOR_EVENT_ET) {
        event.events |= (uint32_t)DHF_XPOLL_ET;
    }
    int32_t nd = (int32_t)fd;
    bool insertSucceed = EventMapInsert(nd, monitorEvent);
    if (!insertSucceed) {
        DB_LOG_ERROR(GMERR_PROGRAM_LIMIT_EXCEEDED, "Event map insert, id %" PRIu16, eventId);
        DbPipeMonitorFreeEvent(monitor, eventId);
        return GMERR_DATA_EXCEPTION;
    }

    int vpRet = g_gmdbAdptPipeHpe.dhfXpollCtl(monitor->handle->vpollFd, DHF_XPOLL_CTL_ADD, (uint32_t)fd, &event);
    Status ret;
    if (vpRet == 0) {
        ret = GMERR_OK;
        DbPipeWakeupPipeMonitor(monitor);
    } else {
        DB_LOG_ERROR(GMERR_EPOLL_OPERATE_FAILED, "XpollCtl, vpollFd %" PRId32 ", fd %" PRId32 "hpe ret:%" PRId32,
            monitor->handle->vpollFd, fd, vpRet);
        (void)EventMapRemove(nd);
        DbPipeMonitorFreeEvent(monitor, eventId);
        ret = GMERR_EPOLL_OPERATE_FAILED;
    }
    if (eventIdOut != NULL) {
        *eventIdOut = ret == GMERR_OK ? eventId : DB_INVALID_ID16;
    }
    return ret;
}

Status DbPipeMonitorAddEvent(DbPipeMonitorT *monitor, const DbPipeT *pipe, DbPipeMonitorEventParaT *para)
{
    DB_POINTER4(monitor, pipe, para, pipe->protoPipe);
    DB_ASSERT((para->mask & DB_PIPE_MONITOR_EVENT_WRITE) != DB_PIPE_MONITOR_EVENT_WRITE);
    int32_t nd = DbPipeGetEventFd(pipe);
    if (nd == HPE_INVALID_ND) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Get event fd, nd %" PRId32 ", os ret%" PRId32, nd, errno);
        return GMERR_DATA_EXCEPTION;
    }
    return DbMonitorAddEvent(monitor, pipe, (DbSocket)nd, para, NULL);
}

void DbMonitorDelEvent(DbPipeMonitorT *monitor, DbSocket fd, uint16_t eventId)
{
    DB_POINTER(monitor);
    if (eventId >= monitor->maxEventNum) {
        return;
    }
    int32_t nd = (int32_t)fd;
    (void)EventMapRemove(nd);
    (void)g_gmdbAdptPipeHpe.dhfXpollCtl(monitor->handle->vpollFd, DHF_XPOLL_CTL_DEL, (uint32_t)fd, NULL);
    DbPipeMonitorFreeEvent(monitor, eventId);
}

void DbPipeMonitorDelEvent(DbPipeMonitorT *monitor, const DbPipeT *pipe)
{
    DB_POINTER2(monitor, pipe);
    // find event
    uint16_t eventId = DB_INVALID_UINT16;
    DbSpinLock(&monitor->lock);
    for (uint16_t i = 0; i < monitor->maxEventNum; i++) {
        if (monitor->events[i].pipe == pipe && monitor->events[i].next == DB_INVALID_UINT16) {
            eventId = i;
            break;
        }
    }
    DbSpinUnlock(&monitor->lock);

    DbSocket fd = (DbSocket)DbPipeGetEventFd(pipe);
    DbMonitorDelEvent(monitor, fd, eventId);
}

inline static bool IsHupEvent(uint32_t type)
{
    return (type & HPE_VPOLLHUP) == HPE_VPOLLHUP || (type & VPOLLHUP) == VPOLLHUP;
}

inline static bool IsReadEvent(uint32_t type)
{
    return (type & HPE_VPOLLIN) == HPE_VPOLLIN;
}

inline static void DbPipeMonitorProcInnerUm(DbPipeMonitorT *monitor, int32_t i)
{
    DHF_XpollEvent *ev = &monitor->handle->events[i];
    DbPipeMonitorEventT *event = (DbPipeMonitorEventT *)ev->data.ptr;

    // 允许userPara为空，但是在没有锁的情况下仍会传入野指针需要后续修改
    if (event->userPara != NULL) {
        if (SECUREC_UNLIKELY(IsHupEvent(ev->events))) {
            event->eventProc(monitor->userCtx, event->userPara, DB_PIPE_MONITOR_EVENT_ERR);
        } else if (SECUREC_LIKELY(IsReadEvent(ev->events))) {
            event->eventProc(monitor->userCtx, event->userPara, DB_PIPE_MONITOR_EVENT_READ);
        }
        event->procNum++;
    } else {
#ifndef NDEBUG
        DB_LOG_WARN(GMERR_DATA_EXCEPTION, "hpe MonitorProc um find no event, active fd id: %" PRId32 "", i);
#endif
    }
}

ALWAYS_INLINE_C static void DbPipeMonitorProcInnerVm(DbPipeMonitorT *monitor, int32_t i)
{
    int32_t ndVal = monitor->handle->events[i].data.xd;
    int32_t idx = EventMapFind(ndVal);
    if (idx < 0) {
        uint32_t u32Val = monitor->handle->events[i].data.u32;
        uint64_t u64Val = monitor->handle->events[i].data.u64;
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION,
            "Find no event in event map with idx: %" PRId32 ", vpollWait result:|nd:%" PRId32 ",u32:%" PRIu32
            ",u64:%" PRIu64 "|",
            idx, ndVal, u32Val, u64Val);
        return;
    }
    DbPipeMonitorEventT *event = g_gmdbVpollEventMap.ele[idx].event;
    // 允许userPara为空，但是在没有锁的情况下仍会传入野指针需要后续修改
    if (event->userPara != NULL) {
        if (SECUREC_UNLIKELY(IsHupEvent(monitor->handle->events[i].events))) {
            event->eventProc(monitor->userCtx, event->userPara, DB_PIPE_MONITOR_EVENT_ERR);
        } else if (SECUREC_LIKELY(IsReadEvent(monitor->handle->events[i].events))) {
            event->eventProc(monitor->userCtx, event->userPara, DB_PIPE_MONITOR_EVENT_READ);
        }
        event->procNum++;
    } else {
#ifndef NDEBUG
        DB_LOG_WARN(GMERR_DATA_EXCEPTION, "hpe MonitorProc vm find no event, active fd id: %" PRId32 "", i);
#endif
    }
}

void DbPipeMonitorProc(DbPipeMonitorT *monitor, int32_t timeout)
{
    DB_POINTER(monitor);
    int32_t activeFds =
        g_gmdbAdptPipeHpe.dhfXpollWait(monitor->handle->vpollFd, monitor->handle->events, monitor->eventNum, timeout);
    if (SECUREC_UNLIKELY(activeFds <= 0)) {
        return;
    }

    if (SECUREC_UNLIKELY(g_gmdbAdptIsVm)) {
        for (int32_t i = 0; i < activeFds; i++) {
            DbPipeMonitorProcInnerVm(monitor, i);
        }
    } else {
        for (int32_t i = 0; i < activeFds; i++) {
            DbPipeMonitorProcInnerUm(monitor, i);
        }
    }
}

void DbPipeMonitorDestroy(DbPipeMonitorT *monitor)
{
    DB_POINTER(monitor);
    (void)DbSemDestroy(&monitor->sem);
    (void)g_gmdbAdptPipeHpe.dhfXpollDestroy(monitor->handle->vpollFd);
    (void)memset_s(g_gmdbVpollEventMap.ele, g_gmdbVpollEventMap.mapCap * sizeof(VpollEventEleT), 0,
        g_gmdbVpollEventMap.mapCap * sizeof(VpollEventEleT));
    monitor->freeFunc(monitor->memctx, monitor);
}

uint64_t DbAdptGlobalRdtsc(void)
{
    return g_gmdbAdptPipeHpe.globalRdtsc();
}

int32_t DbPipeGetItemNum(const DbPipeT *srvPipe)
{
    return (int32_t)g_gmdbAdptPipeHpe.ctlChanGetItemsNum((uint32_t)srvPipe->protoPipe->chanId);
}

bool DbIsHpeUm(void)
{
    return !g_gmdbAdptPipeHpe.notifyOsInterIsSupport();
}

void DbPipePeerClosed(void *conn)
{
    return;
}

#ifdef __cplusplus
}
#endif
