/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: adpt_socket_base.c
 * Description: impl of socket
 * Author:
 * Create: 2023-07-28
 */

#include <poll.h>
#include <errno.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include "db_file.h"
#include "adpt_log.h"
#include "adpt_rdtsc.h"
#include "adpt_locator.h"
#include "adpt_io.h"
#include "adpt_socket_base.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define DB_TV_USEC_START 800

int32_t DbUsockOpenListen(const char *unixDomain, DbSocket *lsrnFd, struct sockaddr_un *sockAddr)
{
    DB_POINTER3(unixDomain, sockAddr, lsrnFd);
    // 按需创建父文件夹。因为会临时修改unixDomain，所以请保证它不是一个只读addr且没有多线程共用
    char *slash = strrchr(unixDomain, '/');
    if (slash != NULL) {
        *slash = '\0';
        Status res = DbMakeDirWithGRPRXPermission(unixDomain);
        *slash = '/';
        if (res != GMERR_OK) {
            DB_LOG_ERROR(res, "Make directory %s for usock.", unixDomain);
            return SOCKET_INTERNAL_ERROR;
        }
    }
    // 创建用于通信的套接字,通信域为UNIX通信域
    int32_t ret = DbSockSetLsnr(lsrnFd, AF_UNIX, SOCK_STREAM);
    if (ret != SOCKET_SUCCESS) {
        return ret;
    }
    // 设置服务器addr参数
    sockAddr->sun_family = AF_UNIX;
    uint32_t nameLen = (uint32_t)strlen(unixDomain);
    uint32_t sunPathLen = sizeof(sockAddr->sun_path);
    if (strncpy_s(sockAddr->sun_path, sunPathLen, unixDomain, nameLen) != EOK) {
        DbSockClosePipe(*lsrnFd);
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "str copy %s. sockLen: %" PRIu32 ", nameLen: %" PRIu32 ".", unixDomain,
            sunPathLen, nameLen);
        return SOCKET_INTERNAL_ERROR;
    }

    (void)unlink(unixDomain);

    // 绑定套接字与服务器addr信息
    uint32_t sunAddrLen = (uint32_t)sizeof(sockAddr->sun_family) + nameLen;
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    if (ioCtx->bind(*lsrnFd, (struct sockaddr *)(void *)(sockAddr), sunAddrLen) == -1) {
        DbSockClosePipe(*lsrnFd);
        DB_LOG_ERROR(GMERR_CONNECTION_EXCEPTION, "Bind(open listen), os no:%" PRId32, errno);
        return SOCKET_INTERNAL_ERROR;
    }

    // 对套接字进行接受建联,判断是否有连接请求
    if (ioCtx->listen(*lsrnFd, SOMAXCONN) == -1) {
        DbSockClosePipe(*lsrnFd);
        DB_LOG_ERROR(GMERR_CONNECTION_EXCEPTION, "Listen(open listen), os no:%" PRId32, errno);
        return SOCKET_INTERNAL_ERROR;
    }
    return SOCKET_SUCCESS;
}

int32_t DbUsockAccept(DbSocket lsrnFd, DbSocket *acceptFd, struct sockaddr_un *unixAddr, const SockAcceptParaT *para)
{
    DB_POINTER(acceptFd);
    SockAddrT addr = {.addr = (struct sockaddr *)(void *)unixAddr, .addrLen = (uint32_t)sizeof(struct sockaddr_un)};
    int32_t ret = DbSockAccept(lsrnFd, acceptFd, &addr, para);
    if (ret != SOCKET_SUCCESS) {
        return ret;
    }
    int32_t option = 1;
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t res = ioCtx->setSockOpt(*acceptFd, SOL_SOCKET, SO_KEEPALIVE, (void *)&option, sizeof(int32_t));
    if (res == -1) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Set sockopt %" PRIu32 " SO_KEEPALIVE %" PRIu32 ", osno: %" PRId32 ".",
            *acceptFd, (uint32_t)SO_KEEPALIVE, errno);
        DbSockClosePipe(*acceptFd);
        return SOCKET_INTERNAL_ERROR;
    }
    return SOCKET_SUCCESS;
}

int32_t DbUsockGetCred(DbSocket fd, uint32_t *pid, uint32_t *uid, uint32_t *gid)
{
    DB_POINTER3(pid, uid, gid);
    struct ucred cred;
    uint32_t len = sizeof(cred);
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t ret = ioCtx->getSockOpt(fd, SOL_SOCKET, SO_PEERCRED, &cred, &len);
    if (ret != 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Get sockopt SO_PEERCRED %" PRId32 ", osno %" PRId32 " ret %" PRId32 "", fd,
            errno, ret);
        return SOCKET_INTERNAL_ERROR;
    }
    *pid = (uint32_t)cred.pid;
    *uid = (uint32_t)cred.uid;
    *gid = (uint32_t)cred.gid;
    return SOCKET_SUCCESS;
}

static int32_t DbUsockBindClientPath(DbSocket fd, const char *clientPath)
{
    struct sockaddr_un usAddr;
    usAddr.sun_family = AF_UNIX;

    int ret = strcpy_s(usAddr.sun_path, DOMAIN_NAME_MAX_LEN, clientPath);
    if (ret != EOK) {
        return SOCKET_INTERNAL_ERROR;
    }
    (void)unlink(clientPath);
    socklen_t len = (socklen_t)(sizeof(usAddr.sun_family) + strlen(clientPath));
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    if (ioCtx->bind(fd, (struct sockaddr *)&usAddr, len) < 0) {
        DB_LOG_ERROR(GMERR_CONNECTION_EXCEPTION, "Usocket bind client path, os no:%" PRId32, errno);
        return SOCKET_INTERNAL_ERROR;
    }
    return SOCKET_SUCCESS;
}

static int32_t DbUsockConnect2Sever(int32_t connectFd, const struct sockaddr *srvAddr, socklen_t addrLen)
{
    DB_POINTER(srvAddr);
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t ret = ioCtx->connect(connectFd, srvAddr, addrLen);
    int mistake = errno;
    if (ret == 0) {
        return SOCKET_SUCCESS;
    }

    if (mistake != EINPROGRESS && mistake != EAGAIN) {
        DB_LOG_ERROR(GMERR_CONNECTION_FAILURE, "Usocket connect to server, ret: %d.", mistake);
        return SOCKET_INTERNAL_ERROR;
    }

    struct pollfd pollFd;
    pollFd.fd = connectFd;
    pollFd.events = POLLOUT;
    pollFd.revents = 0;
    if (ioCtx->poll(&pollFd, 1, DB_TV_USEC_START) > 0) {
        errno = 0;
        ret = ioCtx->connect(connectFd, srvAddr, addrLen);
        mistake = errno;
        if (errno == EISCONN || ret == EOK) {
            return SOCKET_SUCCESS;
        }
    }

    DB_LOG_ERROR(GMERR_CONNECTION_FAILURE, "Usocket connect to server: %d", mistake);
    return SOCKET_INTERNAL_ERROR;
}

int32_t DbUsockConnect(const char *unixDomain, DbSocket *fd, const char *clientPath, uint32_t bufSize, bool nonBlock)
{
    DB_POINTER2(unixDomain, fd);
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    *fd = ioCtx->socket(AF_UNIX, SOCK_STREAM, 0);
    if (*fd < 0) {
        DB_LOG_ERROR(GMERR_CONNECTION_EXCEPTION, "Usock connect, os no: %" PRId32 ".", errno);
        return SOCKET_INTERNAL_ERROR;
    }

    int32_t ret = (clientPath != NULL) ? DbUsockBindClientPath(*fd, clientPath) : SOCKET_SUCCESS;
    if (ret != SOCKET_SUCCESS) {
        DbSockClosePipe(*fd);
        return ret;
    }

    struct sockaddr_un srvAddr;
    srvAddr.sun_family = AF_UNIX;

    size_t nameLen = strlen(unixDomain);
    ret = strncpy_s(srvAddr.sun_path, sizeof(srvAddr.sun_path), unixDomain, nameLen);
    if (ret != EOK) {
        DbSockClosePipe(*fd);
        return SOCKET_INTERNAL_ERROR;
    }

    socklen_t addrLen = (socklen_t)(sizeof(srvAddr.sun_family) + nameLen);
    ret = DbUsockConnect2Sever(*fd, (struct sockaddr *)(void *)&srvAddr, addrLen);
    if (ret != SOCKET_SUCCESS) {
        DbSockClosePipe(*fd);
        return ret;
    }

    ret = DbSockSetNoBlock(*fd, nonBlock);
    if (ret != SOCKET_SUCCESS) {
        DbSockClosePipe(*fd);
        DB_LOG_ERROR(ret, "set socket nonblock.");
        return ret;
    }
    ret = DbSockSetBufferSize(*fd, bufSize, bufSize);
    if (ret != SOCKET_SUCCESS) {
        DbSockClosePipe(*fd);
        return ret;
    }
    DB_LOG_DEBUG("Usocket %d connect.", *fd);
    return SOCKET_SUCCESS;
}

#define CS_OSERR_CAN_IGNORE (errno == EINTR || errno == EWOULDBLOCK || errno == EAGAIN)

inline int32_t DbSockSetNoBlock(DbSocket fd, bool nonBlock)
{
    int32_t option = nonBlock ? 1 : 0;
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t ret = ioCtx->ioctl(ioCtx->getRelFd(fd), FIONBIO, &option);
    if (ret == -1) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Socket set nonblock, osno:%" PRId32 ".", errno);
        return SOCKET_INTERNAL_ERROR;
    }
    return SOCKET_SUCCESS;
}

inline int32_t DbSockPoll(DbSocket fd, bool pollRead, int32_t timeout)
{
    struct pollfd fds;
    fds.fd = fd;
    fds.events = pollRead ? POLLIN : POLLOUT;
    fds.revents = 0;
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    return ioCtx->poll(&fds, 1, timeout);
}

int32_t DbSockAccept(DbSocket lsrnFd, DbSocket *acceptedFd, SockAddrT *addr, const SockAcceptParaT *para)
{
    DB_POINTER3(acceptedFd, addr, para);
    if (DbSockPoll(lsrnFd, true, para->timeoutMs) <= 0) {
        DB_LOG_DBG_DEBUG("Sock poll:%" PRId32 ".", errno);
        return SOCKET_INTERNAL_ERROR;
    }
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t fd = ioCtx->accept(lsrnFd, addr->addr, &addr->addrLen);
    if (fd < 0) {
        DB_LOG_DBG_DEBUG("Accept fd, osno:%" PRId32 ".", errno);
        return SOCKET_INTERNAL_ERROR;
    }
    int32_t ret = DbSockSetBufferSize(fd, para->bufSize, para->bufSize);
    if (ret != SOCKET_SUCCESS) {
        return ret;
    }
    ret = DbSockSetNoBlock(fd, para->nonBlock);
    if (ret != SOCKET_SUCCESS) {
        DB_LOG_ERROR(ret, "Sock set noblock %" PRIu8 ". osno:%" PRId32 "", para->nonBlock, errno);
        return ret;
    }
    *acceptedFd = fd;
    return SOCKET_SUCCESS;
}

int32_t DbSockSetLsnr(DbSocket *lsrnFd, int32_t domain, int32_t type)
{
    DB_POINTER(lsrnFd);
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    *lsrnFd = ioCtx->socket(domain, type, 0);
    if (*lsrnFd < 0) {
        DB_LOG_ERROR(GMERR_CONNECTION_EXCEPTION, "Create lsrn socket, osno:%" PRId32 ".", errno);
        return SOCKET_INTERNAL_ERROR;
    }

    int32_t ret = DbSockSetNoBlock(*lsrnFd, true);
    if (ret != SOCKET_SUCCESS) {
        DbSockClosePipe(*lsrnFd);
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
            "Set lsrn socket noblock, osno: %" PRId32 ", domain: %" PRId32 ", type: %" PRId32 ".", errno, domain, type);
        return ret;
    }
    if (domain == AF_UNIX) {
        return SOCKET_SUCCESS;
    }
    int32_t option = 1;
    if (ioCtx->setSockOpt(*lsrnFd, SOL_SOCKET, SO_REUSEPORT, (char *)&option, sizeof(uint32_t)) == -1) {
        DbSockClosePipe(*lsrnFd);
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Set reuse addr, osno: %" PRId32 ", domain: %" PRId32 ", type: %" PRId32 ".",
            errno, domain, type);
        return SOCKET_INTERNAL_ERROR;
    }
    return SOCKET_SUCCESS;
}

int32_t DbSockSetBufferSize(DbSocket fd, uint32_t sendSize, uint32_t recvSize)
{
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t ret = ioCtx->setSockOpt(fd, SOL_SOCKET, SO_SNDBUF, (char *)&sendSize, sizeof(int32_t));
    if (ret != 0) {
        DB_LOG_ERROR(
            GMERR_INTERNAL_ERROR, "Snd buffer setsockopt, size %" PRIu32 ", osno:%" PRId32 "", sendSize, errno);
        return SOCKET_INTERNAL_ERROR;
    }
    ret = ioCtx->setSockOpt(fd, SOL_SOCKET, SO_RCVBUF, (char *)&recvSize, sizeof(int32_t));
    if (ret != 0) {
        DB_LOG_ERROR(
            GMERR_INTERNAL_ERROR, "Rcv buffer setsockopt, size %" PRIu32 ", osno:%" PRId32 "", recvSize, errno);
        return SOCKET_INTERNAL_ERROR;
    }
    return SOCKET_SUCCESS;
}

inline void DbSockClosePipe(DbSocket fd)
{
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    (void)ioCtx->close(fd);
}

int32_t DbSockGetRecvBuffSize(DbSocket fd, uint32_t *recvBuffSize)
{
    DB_POINTER(recvBuffSize);
    socklen_t buffLen = 0;
    socklen_t len = sizeof(buffLen);
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t ret = ioCtx->getSockOpt(fd, SOL_SOCKET, SO_RCVBUF, (void *)&buffLen, &len);
    *recvBuffSize = buffLen;
    if (ret == EOK) {
        return SOCKET_SUCCESS;
    }
    DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "get rcv buffer size, osno:%" PRId32 ".", errno);
    return SOCKET_INTERNAL_ERROR;
}

int32_t DbSockGetSendBuffSize(DbSocket fd, uint32_t *sendBuffSize)
{
    DB_POINTER(sendBuffSize);
    socklen_t buffLen = 0;
    socklen_t len = sizeof(buffLen);
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t ret = ioCtx->getSockOpt(fd, SOL_SOCKET, SO_SNDBUF, (void *)&buffLen, &len);
    *sendBuffSize = buffLen;
    if (ret == EOK) {
        return SOCKET_SUCCESS;
    }
    DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "get snd buffer size, osno:%" PRId32 ".", errno);
    return SOCKET_INTERNAL_ERROR;
}

int32_t DbSockGetUnRecvBytes(DbSocket fd, uint32_t *unRecvSize)
{
    DB_POINTER(unRecvSize);
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t ret = ioCtx->ioctl(ioCtx->getRelFd(fd), TIOCINQ, unRecvSize);
    if (ret == EOK) {
        return SOCKET_SUCCESS;
    }
    DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "get unRcv buffer size, osno:%" PRId32 ".", errno);
    return SOCKET_INTERNAL_ERROR;
}

int32_t DbSockGetUnSendBytes(DbSocket fd, uint32_t *unSendSize)
{
    DB_POINTER(unSendSize);
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t ret = ioCtx->ioctl(ioCtx->getRelFd(fd), TIOCOUTQ, unSendSize);
    if (ret == EOK) {
        return SOCKET_SUCCESS;
    }
    DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "get unSnd buffer size, osno:%" PRId32 ".", errno);
    return SOCKET_INTERNAL_ERROR;
}

int32_t DbBlockSockSetSendTimeout(DbSocket fd, uint32_t timeoutMs)
{
    socklen_t optlen = (socklen_t)sizeof(struct timeval);
    struct timeval tv;
    tv.tv_sec = (time_t)(timeoutMs / MSECONDS_IN_SECOND);
    tv.tv_usec = (time_t)(timeoutMs % MSECONDS_IN_SECOND * USECONDS_IN_MSECOND);
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t ret = ioCtx->setSockOpt(fd, SOL_SOCKET, SO_SNDTIMEO, &tv, optlen);
    if (ret == -1) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "set snd timeout %" PRIu32 ", osno:%" PRId32 ".", timeoutMs, errno);
        return SOCKET_INTERNAL_ERROR;
    }
    return SOCKET_SUCCESS;
}

int32_t DbBlockSockSetRecvTimeout(DbSocket fd, uint32_t timeoutMs)
{
    socklen_t optlen = (socklen_t)sizeof(struct timeval);
    struct timeval tv;
    tv.tv_sec = (time_t)(timeoutMs / MSECONDS_IN_SECOND);
    tv.tv_usec = (time_t)(timeoutMs % MSECONDS_IN_SECOND * USECONDS_IN_MSECOND);
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int32_t ret = ioCtx->setSockOpt(fd, SOL_SOCKET, SO_RCVTIMEO, &tv, optlen);
    if (ret == -1) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "set rcv timeout %" PRIu32 ", osno:%" PRId32 ".", timeoutMs, errno);
        return SOCKET_INTERNAL_ERROR;
    }
    return SOCKET_SUCCESS;
}

int32_t DbBlockSockSend(DbSocket fd, const char *buff, uint32_t size, uint32_t *sendSize)
{
    DB_POINTER(buff);
    uint32_t totalSize = 0;
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    while (totalSize < size) {
        int32_t writeSize = (int32_t)ioCtx->send(fd, buff + totalSize, size - totalSize, MSG_NOSIGNAL);
        if (SECUREC_LIKELY(writeSize > 0)) {
            totalSize += (uint32_t)writeSize;
            DB_ASSERT(totalSize <= size);
        } else if (SECUREC_UNLIKELY(writeSize == 0 || (writeSize < 0 && !CS_OSERR_CAN_IGNORE))) {
            DB_LOG_ERROR(GMERR_CONNECTION_RESET_BY_PEER,
                "Block fd %" PRId32 " reset. Write size %" PRId32 ", osno:%" PRId32 ".", fd, writeSize, errno);
            return SOCKET_FAILED;
        } else if (errno == EWOULDBLOCK || errno == EAGAIN) {
            break;
        }
    }
    if (sendSize != NULL) {
        *sendSize = totalSize;
    }
    return (totalSize == size) ? SOCKET_SUCCESS : SOCKET_BUFFER_FULL;
}

int32_t DbBlockSockRecv(DbSocket fd, char *buff, uint32_t size, uint32_t *recvSize)
{
    DB_POINTER(buff);
    uint32_t totalSize = 0;
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    while (totalSize < size) {
        int32_t readSize = (int32_t)ioCtx->recv(fd, buff + totalSize, size - totalSize, MSG_NOSIGNAL);
        if (SECUREC_LIKELY(readSize > 0)) {
            totalSize += (uint32_t)readSize;
            DB_ASSERT(totalSize <= size);
        } else if (SECUREC_UNLIKELY(readSize == 0 || (readSize < 0 && !CS_OSERR_CAN_IGNORE))) {
            DB_LOG_ERROR(GMERR_CONNECTION_RESET_BY_PEER,
                "Block fd %" PRId32 " reset. Read size %" PRId32 ", osno:%" PRId32 "", fd, readSize, errno);
            return SOCKET_FAILED;
        } else if (errno == EWOULDBLOCK || errno == EAGAIN) {
            break;
        }
    }
    if (recvSize != NULL) {
        *recvSize = totalSize;
    }
    return (totalSize == size) ? SOCKET_SUCCESS : SOCKET_RECV_TIMEOUT;
}

int32_t DbNonBlockSockTrySend(DbSocket fd, const char *buff, uint32_t size, uint32_t *sendSize)
{
    DB_POINTER(buff);
    uint32_t totalSize = 0;
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    do {
        int32_t writeSize = (int32_t)ioCtx->send(fd, buff + totalSize, size - totalSize, MSG_NOSIGNAL);
        bool ignore = CS_OSERR_CAN_IGNORE;
        if (SECUREC_LIKELY(writeSize > 0)) {
            totalSize += (uint32_t)writeSize;
            DB_ASSERT(totalSize <= size);
        } else if (SECUREC_UNLIKELY(writeSize == 0 || (writeSize < 0 && !ignore))) {
            DB_LOG_ERROR(GMERR_CONNECTION_RESET_BY_PEER,
                "NonBlock fd %" PRId32 " reset. Write size %" PRId32 ", osno:%" PRId32 ".", fd, writeSize, errno);
            return SOCKET_FAILED;
        }
        if (ignore) {
            break;
        }
    } while (totalSize < size);
    if (sendSize != NULL) {
        *sendSize = totalSize;
    }
    return (totalSize == size) ? SOCKET_SUCCESS : SOCKET_BUFFER_FULL;
}

int32_t DbNonBlockSockTryRecv(DbSocket fd, char *buff, uint32_t size, uint32_t *recvSize)
{
    DB_POINTER(buff);
    uint32_t totalSize = 0;
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    do {
        int32_t readSize = (int32_t)ioCtx->recv(fd, buff + totalSize, size - totalSize, MSG_NOSIGNAL);
        bool ignore = CS_OSERR_CAN_IGNORE;
        if (SECUREC_LIKELY(readSize > 0)) {
            totalSize += (uint32_t)readSize;
            DB_ASSERT(totalSize <= size);
        } else if (SECUREC_UNLIKELY(readSize == 0 || (readSize < 0 && !ignore))) {
            DB_LOG_ERROR(GMERR_CONNECTION_RESET_BY_PEER,
                "NonBlock fd %" PRId32 " reset. Read size %" PRId32 ", osno:%" PRId32 ".", fd, readSize, errno);
            return SOCKET_FAILED;
        }
        if (ignore) {
            break;
        }
    } while (totalSize < size);
    if (recvSize != NULL) {
        *recvSize = totalSize;
    }
    return SOCKET_SUCCESS;
}

ALWAYS_INLINE_C static int32_t DbNonBlockSockTimedSend(
    DbSocket fd, const char *buff, uint32_t size, uint32_t timeoutMs, uint32_t *sendSize)
{
    DB_POINTER(buff);
    uint32_t totalSize = 0;
    while (totalSize < size) {
        int32_t num = DbSockPoll(fd, false, (int32_t)timeoutMs);
        if (SECUREC_UNLIKELY((num == 0 && totalSize > 0 && size > totalSize) || (num < 0 && !CS_OSERR_CAN_IGNORE))) {
            DB_LOG_ERROR(GMERR_CONNECTION_RESET_BY_PEER,
                "NonBlock fd %" PRId32 " poll reset. Send size %" PRId32 ", total size %" PRId32 ", ret: %" PRId32
                ", osno:%" PRId32 ".",
                size, fd, totalSize, num, errno);
            return SOCKET_FAILED;
        } else if (num == 0) {
            break;
        }
        uint32_t writeSize = 0;
        int32_t ret = DbNonBlockSockTrySend(fd, buff + totalSize, size - totalSize, &writeSize);
        if (SECUREC_UNLIKELY(ret == SOCKET_FAILED)) {
            return SOCKET_FAILED;
        } else if (writeSize > 0) {
            totalSize += writeSize;
        }
    }
    if (sendSize != NULL) {
        *sendSize = totalSize;
    }
    return (totalSize == size) ? SOCKET_SUCCESS : SOCKET_BUFFER_FULL;
}

inline int32_t DbNonBlockSockSend(DbSocket fd, const char *buff, uint32_t size, uint32_t timeoutMs, uint32_t *sendSize)
{
    return timeoutMs == 0 ? DbNonBlockSockTrySend(fd, buff, size, sendSize) :
                            DbNonBlockSockTimedSend(fd, buff, size, timeoutMs, sendSize);
}

ALWAYS_INLINE_C static int32_t DbNonBlockSockTimedRecv(
    DbSocket fd, char *buff, uint32_t size, uint32_t timeoutMs, uint32_t *recvSize)
{
    DB_POINTER(buff);
    uint32_t totalSize = 0;
    errno = 0;
    while (totalSize < size) {
        int32_t num = DbSockPoll(fd, true, (int32_t)timeoutMs);
        if (SECUREC_UNLIKELY((num == 0 && errno == ENOENT) || (num < 0 && !CS_OSERR_CAN_IGNORE))) {
            DB_LOG_ERROR(GMERR_CONNECTION_RESET_BY_PEER,
                "NonBlock fd %" PRId32 " poll reset. Recv size %" PRId32 ", total size %" PRId32 ", ret: %" PRId32
                ", osno:%" PRId32 ".",
                fd, size, totalSize, num, errno);
            return SOCKET_FAILED;
        } else if (num == 0) {
            break;
        }
        uint32_t readSize = 0;
        int32_t ret = DbNonBlockSockTryRecv(fd, buff + totalSize, size - totalSize, &readSize);
        if (SECUREC_UNLIKELY(ret == SOCKET_FAILED)) {
            return SOCKET_FAILED;
        } else if (readSize > 0) {
            totalSize += readSize;
        }
    }
    if (recvSize != NULL) {
        *recvSize = totalSize;
    }
    return (totalSize == size) ? SOCKET_SUCCESS : SOCKET_RECV_TIMEOUT;
}

inline int32_t DbNonBlockSockRecv(DbSocket fd, char *buff, uint32_t size, uint32_t timeoutMs, uint32_t *recvSize)
{
    return timeoutMs == 0 ? DbNonBlockSockTryRecv(fd, buff, size, recvSize) :
                            DbNonBlockSockTimedRecv(fd, buff, size, timeoutMs, recvSize);
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
