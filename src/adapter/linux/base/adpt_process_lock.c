/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: adpt_process_lock.c
 * Description: Implement of process lock of linux system
 * Author:
 * Create: 2021-08-04
 */

#include <unistd.h>
#include "db_file.h"
#include "adpt_log.h"
#include "adpt_io.h"
#include "adpt_process_lock.h"

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
static char *g_gmdbLockFileNameList[] = {
    "GMDBV5_INSTANCE_%d.lock",
    "TOOLS_GMRULE.lock",
};

// 用途：进程级别实例锁
// 是否并发初始化：否
// 是否并发读写：否
// 并发方案：无
static int g_gmdbProcessFileLockFd = DB_INVALID_FD;
#endif /* FEATURE_SIMPLEREL */

static char g_gmdbProcessFileLockPath[DB_MAX_PATH] = "/run/verona";
#define W_PERMISSION_FOR_OWNER 0200

// 用途：存放实例ID
// 是否并发初始化：否
// 是否并发读写：否
// 并发方案：无
int32_t g_gmdbSrvInstanceId = 0;
void DbSetInstanceId(int32_t instanceId)
{
    g_gmdbSrvInstanceId = instanceId;
}

Status DbSetFilePathLock(char *lockPath)
{
    errno_t err = strcpy_s(g_gmdbProcessFileLockPath, DB_MAX_PATH, lockPath);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "DbSetFilePathLock: %" PRId32, err);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
static char *DbGetFilePathLock(void)
{
    return g_gmdbProcessFileLockPath;
}

static int32_t DbGetLockFilePath(DbLockFileNameE lockFileName, char *lockFilePath)
{
    int32_t ret = 0;
    char *fileName = g_gmdbLockFileNameList[lockFileName];
    if (lockFileName == DB_INSTANCE) {
        char destFileName[DB_MAX_PATH];
        ret = snprintf_s(destFileName, DB_MAX_PATH, DB_MAX_PATH - 1, fileName, g_gmdbSrvInstanceId);
        if (ret < 0) {
            DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "snprintf_s, ret:%" PRId32, ret);
            return ret;
        }
        ret = snprintf_s(lockFilePath, DB_MAX_PATH, DB_MAX_PATH - 1, "%s/%s", DbGetFilePathLock(), destFileName);
    } else {
        ret = snprintf_s(lockFilePath, DB_MAX_PATH, DB_MAX_PATH - 1, "%s/%s", DbGetFilePathLock(), fileName);
    }
    if (ret < 0) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "snprintf_s.");
    }
    return ret;
}

static Status DbAdptEulerCreateAndLockFile(const char *filePath, int32_t *fd)
{
    DB_POINTER2(filePath, fd);
    int32_t flag = WRITE_ONLY | CREATE_FILE | TRUNCATE_FILE | O_BINARY;
    Status status = DbOpenFile(filePath, flag, W_PERMISSION_FOR_OWNER, fd);
    if (status != GMERR_OK) {
        DB_LOG_ERROR(status, "Open file, os no: %" PRId32 ".", (int32_t)errno);
        return status;
    }
    struct flock fileLock;
    fileLock.l_type = F_WRLCK;
    fileLock.l_start = 0;
    fileLock.l_whence = SEEK_SET;
    fileLock.l_len = 0;

    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    int ret = ioCtx->fcntl(ioCtx->getRelFd(*fd), F_SETLK, &fileLock);
    if (ret == -1) {
        DbCloseFile(*fd);
        *fd = DB_INVALID_FD;
        DB_LOG_ERROR(GMERR_FILE_OPERATE_FAILED, "fcntl, os no: %" PRId32 ".", (int32_t)errno);
        return GMERR_FILE_OPERATE_FAILED;
    }
    pid_t pid = getpid();
    status = DbWriteFile(*fd, (const void *)&pid, sizeof(pid));
    if (status != GMERR_OK) {
        DB_LOG_ERROR(status, "Write file, fd %" PRId32 ", os no:%" PRId32, *fd, (int32_t)errno);
        DbCloseFile(*fd);
        *fd = DB_INVALID_FD;
    }
    return status;
}

Status DbSingleProcessLock(DbLockFileNameE lockFileName, int32_t *lockFileFd)
{
    DB_POINTER(lockFileFd);
    Status status = DbMakeDirWithGRPRXPermission(DbGetFilePathLock());
    if (status != GMERR_OK) {
        DB_LOG_ERROR(status, "Make dir.");
        return status;
    }
    char filePath[DB_MAX_PATH] = {0};
    int32_t ret = DbGetLockFilePath(lockFileName, filePath);
    if (ret < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    return DbAdptEulerCreateAndLockFile(filePath, lockFileFd);
}

Status DbProcessInstanceLock(void)
{
    return DbSingleProcessLock(DB_INSTANCE, &g_gmdbProcessFileLockFd);
}

void DbSingleProcessUnLock(DbLockFileNameE lockFileName, int32_t lockFileFd)
{
    if (lockFileFd != DB_INVALID_FD) {
        DbCloseFile(lockFileFd);
        char filePath[DB_MAX_PATH] = {0};
        int32_t ret = DbGetLockFilePath(lockFileName, filePath);
        if (ret < 0) {
            return;
        }
        (void)DbRemoveFile(filePath);
    }
}

void DbProcessInstanceUnLock(void)
{
    DbSingleProcessUnLock(DB_INSTANCE, g_gmdbProcessFileLockFd);
    g_gmdbProcessFileLockFd = DB_INVALID_FD;
}

bool DbSingleProcessIsExist(DbLockFileNameE lockFileName)
{
    char filePath[DB_MAX_PATH] = {0};
    int32_t ret = DbGetLockFilePath(lockFileName, filePath);
    if (ret < 0) {
        return false;
    }
    bool fileExist = DbFileExist(filePath);
    if (!fileExist) {
        return false;
    }
    DB_LOG_INFO("file lock exist:%s", filePath);
    int fd = DB_INVALID_FD;
    Status status = DbOpenFile(filePath, WRITE_ONLY | O_BINARY, W_PERMISSION_FOR_OWNER, &fd);
    if (status != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Open file, os no: %" PRId32 ".", (int32_t)errno);
        return true;
    }
    struct flock fileLock;
    fileLock.l_type = F_WRLCK;
    fileLock.l_start = 0;
    fileLock.l_whence = SEEK_SET;
    fileLock.l_len = 0;
    const DbIoFuncT *ioCtx = DbGetIoCtxPtr();
    ret = ioCtx->fcntl(ioCtx->getRelFd(fd), F_GETLK, &fileLock);

    DbCloseFile(fd);
    if (ret == -1) {
        DB_LOG_ERROR(GMERR_FILE_OPERATE_FAILED, "fcntl, os no %" PRId32 ".", (int32_t)errno);
        return true;
    }
    return fileLock.l_type != F_UNLCK;
}

bool DbProcessInstanceIsExist(void)
{
    return DbSingleProcessIsExist(DB_INSTANCE);
}
#endif /* FEATURE_SIMPLEREL */
