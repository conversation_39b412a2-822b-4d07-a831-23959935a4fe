/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: file content
 * Author: GMDBV5 EE Team
 * Create: 2024-12-29
 */

#include <unistd.h>
#include <dlfcn.h>
#include "adpt_ha.h"
#include "adpt_function_loader.h"
#include "adpt_define.h"

#define ORM_HA_LIB_PATH "liborm_ha.so"

static unsigned int RegisterHaEventStub(void)
{
    DB_LOG_ERROR(GMERR_GET_THIRD_PARTY_FUNCTION_FAILED, "HA stub RegisterHaEventStub");
    return 0;
}

static unsigned int ReportSwitchStatusStub(DbSwitchStatusE status, const char *reason)
{
    DB_LOG_ERROR(GMERR_GET_THIRD_PARTY_FUNCTION_FAILED, "HA stub ReportSwitchStatusStub");
    return 0;
}

static uint32_t SetBatchBackupTimeoutStub(uint32_t timeout)
{
    DB_LOG_ERROR(GMERR_GET_THIRD_PARTY_FUNCTION_FAILED, "HA stub SetBatchBackupTimeoutStub");
    return 0;
}

static struct {
    bool haSdkInitFlag;
    unsigned int (*registerHaEvent)(void);
    unsigned int (*reportSwitchStatus)(DbSwitchStatusE status, const char *reason);
    uint32_t (*setBatchBackupTimeout)(uint32_t timeout);
} g_gmdbAdptHaSdk = {
    .haSdkInitFlag = false,
    .registerHaEvent = RegisterHaEventStub,
    .reportSwitchStatus = ReportSwitchStatusStub,
    .setBatchBackupTimeout = SetBatchBackupTimeoutStub,
};

static Status DbAdptGetOrmFuncs(void)
{
    void *handle = NULL;
    Status ret = DbAdptLoadLibrary(ORM_HA_LIB_PATH, &handle, RTLD_LAZY);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "Dlopen, library: %s, os ret: %" PRId32 ", os msg:%s.", (char *)ORM_HA_LIB_PATH, errno, dlerror());
        return ret;
    }

    const FuncTableItemT table[] = {{&g_gmdbAdptHaSdk.registerHaEvent, "OrmAdptEulerHaInit"},
        {&g_gmdbAdptHaSdk.reportSwitchStatus, "OrmReportHaSwitchStatusWithReason"},
        {&g_gmdbAdptHaSdk.setBatchBackupTimeout, "OrmSetBatchBackupTimeout"}};

    ret = DbAdptLoadFunc(handle, table, ELEMENT_COUNT(table));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "Load Func ret:%" PRId32 ", os ret:%" PRId32 ", os msg:%s", ret, errno, dlerror());
        return ret;
    }

    g_gmdbAdptHaSdk.haSdkInitFlag = true;
    DB_LOG_INFO("dlopen succ! Library: %s", ORM_HA_LIB_PATH);
    return ret;
}

// 初始化HA
Status DbAdptHaInit(void)
{
    if (!g_gmdbAdptHaSdk.haSdkInitFlag) {
        Status ret = DbAdptGetOrmFuncs();
        if (ret != GMERR_OK) {
            return ret;  // 前面异常分支里已经有打印
        }
    }
    // HA接口调用
    Status ret = (Status)g_gmdbAdptHaSdk.registerHaEvent();
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
            "Register event, func ret = %" PRId32 ", os ret: %" PRId32 ", os msg:%s.", ret, errno, dlerror());
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    return GMERR_OK;
}

// 组件向HA中心上报批备状态和批备状态原因
Status DbReportHaSwitchStatusWithReason(DbSwitchStatusE status, const char *reason)
{
    if (!g_gmdbAdptHaSdk.haSdkInitFlag) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "Not init ha");
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    uint32_t ret = g_gmdbAdptHaSdk.reportSwitchStatus(status, reason);
    if (ret != 0u) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
            "DbReportHaSwitchStatusWithReason, func ret = %" PRIu32 ", os ret: %" PRId32 ", os msg:%s.", ret, errno,
            dlerror());
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    return GMERR_OK;
}

// 设置批备超时时间
Status DbSetBatchBackupTimeout(uint32_t timeout)
{
    if (!g_gmdbAdptHaSdk.haSdkInitFlag) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "Not init ha");
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    uint32_t ret = g_gmdbAdptHaSdk.setBatchBackupTimeout(timeout);
    if (ret != 0u) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
            "DbSetBatchBackupTimeout, func ret = %" PRIu32 ", os ret: %" PRId32 ", os msg:%s.", ret, errno, dlerror());
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    return GMERR_OK;
}

// 去初始化，当前用不到
Status DbAdptHaUnit(void)
{
    // 目前没有Unit场景
    return GMERR_OK;
}
