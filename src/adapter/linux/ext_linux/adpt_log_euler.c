/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: write func in ext_linux
 * Author: gaohaiyang
 * Create: 2021-09-11
 */
#include <unistd.h>
#include <dlfcn.h>
#include <syslog.h>
#include "db_file.h"
#include "adpt_function_loader.h"
#include "adpt_process_id.h"
#include "adpt_spinlock.h"
#include "adpt_atomic.h"
#include "adpt_log.h"
#include "adpt_memory.h"
#include "adpt_process_name.h"
#include "adpt_init.h"
#include "adpt_log_euler.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define LOG_PATH_MAX DB_MAX_PATH  // 日志路径长度最值
#define LOG_SUB_FOLD_NAME_LEN 8   // 日志子文件夹名长度：{operate, run, secure, alarm, debug} max

#define LOG_SUB_FOLDER "log"              // 日志子文件夹名
#define LOG_DIR_PATH_FMT "%s/%s/%s/%s%s"  // 文件夹路径，如：pwd/log/operate/rgmserver
#define LOG_FILE_PREFIX_FMT "%s/%s%s"     // 文件路径前缀，如：pwd/log/operate/rgmserver/rgmserver

#define LATEST_LOG_PATH_FMT "%s.log"  // 最新日志文件的完整路径，如：pwd/log/operate/rgmserver/rgmserver.log
#define BAK_LOG_PATH_FMT "%s.%u.log"  // 备份日志文件的完整路径，如：pwd/log/operate/rgmserver/rgmserver.1.log

typedef struct {
    char dirPath[LOG_PATH_MAX];   // 文件夹绝对路径
    char filePref[LOG_PATH_MAX];  // 日志文件路径前缀
} LogFileInfoT;

typedef struct {
    char procPath[LOG_PATH_MAX];                                     // 进程绝对路径
    char procName[DB_MAX_PROC_NAME_LEN];                             // 进程名
    char subFold[(int32_t)DB_LOG_TYPE_BUTT][LOG_SUB_FOLD_NAME_LEN];  // 日志子文件夹名

    char filePath[(int32_t)DB_LOG_TYPE_BUTT][LOG_PATH_MAX];  // 当前日志文件路径完整，动态变化
    int32_t logFd[(int32_t)DB_LOG_TYPE_BUTT];                // 缓存的文件句柄
    DbSpinLockT fileLock[(int32_t)DB_LOG_TYPE_BUTT];         // 日志文件锁，写日志时需要加锁保护
} DbLogPathCtxT;

// 文件切换场景
typedef enum {
    NOT_NEED_SWITCH = 0,  // 不用切文件
    CLOSED_FD,            // fd被关闭
    OLD_FD,               // fd旧
    FILE_FULL             // 文件满
} DbLogSwitchCondE;

static DbLogSwitchCondE DbLogGetSwitchCond(const DbLogPathCtxT *pathCtx, DbLogTypeE type)
{
    DB_POINTER(pathCtx);
    if (pathCtx->logFd[type] == DB_INVALID_FD) {
        return CLOSED_FD;
    }
    bool fileExist = DbFileExist(pathCtx->filePath[type]);
    if (!fileExist) {
        return OLD_FD;
    }
    size_t fileSize;
    Status ret = DbFileSize(pathCtx->filePath[type], &fileSize);
    if (ret != GMERR_OK) {
        return NOT_NEED_SWITCH;
    }
    DbLogFileCfgT fileCfg = DbAdptLogGetFileCfg();
    if (fileSize > fileCfg.maxLogFileSize) {
        return FILE_FULL;
    }
    return NOT_NEED_SWITCH;
}

inline static bool DbLogNeedSwitch(DbLogSwitchCondE switchCond)
{
    return switchCond > NOT_NEED_SWITCH;
}

#define LATEST_LOG_IDX 0
// 构造日志文件路径，fileIdx：文件序号，最新的日志文件没有序号，单独处理
static Status DbLogMakeFilePath(char *filePath, uint32_t length, const LogFileInfoT *logFileInfo, uint32_t fileIdx)
{
    DB_POINTER2(filePath, logFileInfo);
    int32_t ret;
    if (fileIdx == LATEST_LOG_IDX) {
        ret = snprintf_truncated_s(filePath, length, LATEST_LOG_PATH_FMT, logFileInfo->filePref);
    } else {
        ret = snprintf_truncated_s(filePath, length, BAK_LOG_PATH_FMT, logFileInfo->filePref, fileIdx);
    }
    if (ret < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

static Status DbLogOpenFile(
    char *pathBuf, const LogFileInfoT *fileInfo, uint32_t fileIdx, int32_t *currentFd, bool isTruncate)
{
    (void)memset_s(pathBuf, LOG_PATH_MAX, 0, LOG_PATH_MAX);
    Status ret = DbLogMakeFilePath(pathBuf, LOG_PATH_MAX, fileInfo, fileIdx);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (isTruncate) {
        ret = DbOpenFile(pathBuf, CREATE_FILE | READ_WRITE | APPEND_FILE | TRUNCATE_FILE, PERM_USRRW, currentFd);
    } else {
        ret = DbOpenFile(pathBuf, CREATE_FILE | READ_WRITE | APPEND_FILE, PERM_USRRW, currentFd);
    }
    return ret;
}

static void DbLogCloseFile(int32_t *currentFd)
{
    if (*currentFd != DB_INVALID_FD) {
        DbCloseFile(*currentFd);
        *currentFd = DB_INVALID_FD;
    }
}

static Status DbLogRenameFile(DbLogPathCtxT *pathCtx, uint32_t type, const LogFileInfoT *fileInfo)
{
    char oldName[LOG_PATH_MAX];
    char newName[LOG_PATH_MAX];
    Status ret;
    DbLogFileCfgT fileCfg = DbAdptLogGetFileCfg();
    for (uint32_t i = fileCfg.maxLogFileNum; i >= 1; i--) {
        int32_t secRet;
        // 序号1文件对应的旧文件是未编号的日志文件
        if (i == 1) {
            secRet = snprintf_s(oldName, LOG_PATH_MAX, LOG_PATH_MAX - 1, LATEST_LOG_PATH_FMT, fileInfo->filePref);
        } else {
            secRet = snprintf_s(oldName, LOG_PATH_MAX, LOG_PATH_MAX - 1, BAK_LOG_PATH_FMT, fileInfo->filePref, i - 1);
        }
        if (secRet < 0) {
            return GMERR_FIELD_OVERFLOW;
        }
        secRet = snprintf_s(newName, LOG_PATH_MAX, LOG_PATH_MAX - 1, BAK_LOG_PATH_FMT, fileInfo->filePref, i);
        if (secRet < 0) {
            return GMERR_FIELD_OVERFLOW;
        }
        bool fileExist = DbFileExist(oldName);
        if (!fileExist) {
            continue;
        }
        ret = DbAdptRename(oldName, newName);
        if (ret != GMERR_OK) {
            return GMERR_FILE_OPERATE_FAILED;
        }
    }
    int32_t *currentFd = &pathCtx->logFd[type];
    DbLogCloseFile(currentFd);
    ret = DbLogOpenFile(pathCtx->filePath[type], fileInfo, LATEST_LOG_IDX, currentFd, true);
    if (ret != GMERR_OK) {
        *currentFd = DB_INVALID_FD;
    }
    return ret;
}

static Status DbLogSwitchFile(
    DbLogPathCtxT *pathCtx, const LogFileInfoT *fileInfo, DbLogTypeE type, DbLogSwitchCondE switchCond)
{
    DB_POINTER2(pathCtx, fileInfo);
    Status ret = GMERR_OK;
    int32_t *currentFd = &pathCtx->logFd[type];

    switch (switchCond) {
        case OLD_FD:
        case CLOSED_FD:
            // 打开最新的日志文件
            DbLogCloseFile(currentFd);
            ret = DbLogOpenFile(pathCtx->filePath[type], fileInfo, LATEST_LOG_IDX, currentFd, false);
            if (ret != GMERR_OK) {
                *currentFd = DB_INVALID_FD;
            }
            break;
        case FILE_FULL:
            ret = DbLogRenameFile(pathCtx, (uint32_t)type, fileInfo);
            break;
        case NOT_NEED_SWITCH:
        default:
            break;
    }
    return ret;
}

static Status DbLogSwitchFileCond(DbLogPathCtxT *pathCtx, DbLogTypeE type, const LogFileInfoT *fileInfo)
{
    DbLogSwitchCondE switchCond = DbLogGetSwitchCond(pathCtx, type);
    if (DbLogNeedSwitch(switchCond)) {
        DbSpinLockWithoutLog(&pathCtx->fileLock[type]);  // 日志模块使用不打印超时日志锁
        switchCond = DbLogGetSwitchCond(pathCtx, type);
        if (DbLogNeedSwitch(switchCond)) {
            Status ret = DbLogSwitchFile(pathCtx, fileInfo, type, switchCond);
            if (ret != GMERR_OK) {
                DbSpinUnlock(&pathCtx->fileLock[type]);
                return ret;
            }
        }
        DbSpinUnlock(&pathCtx->fileLock[type]);
    }
    return GMERR_OK;
}

static Status DbLogWriteInner(
    DbLogPathCtxT *pathCtx, DbLogTypeE type, const LogFileInfoT *fileInfo, const char *msg, uint32_t msgLen)
{
    DB_POINTER3(pathCtx, fileInfo, msg);
    Status ret = DbMakeDirWithGRPRXPermission(fileInfo->dirPath);
    if (ret != GMERR_OK) {
        return ret;
    }
    size_t writeLen;
    // DB_LOG_TYPE_SIGNAL不切换文件
    if (type != DB_LOG_TYPE_SIGNAL) {
        ret = DbLogSwitchFileCond(pathCtx, type, fileInfo);
        if (ret != GMERR_OK) {
            return ret;
        }
        DbSpinLockWithoutLog(&pathCtx->fileLock[type]);  // 日志模块使用不打印超时日志锁
        ret = DbAdptWrite(pathCtx->logFd[type], (const void *)msg, msgLen, &writeLen);
        DbSpinUnlock(&pathCtx->fileLock[type]);
    } else {
        // signal日志写入到run日志中
        ret = DbAdptWrite(pathCtx->logFd[DB_LOG_TYPE_RUN], (const void *)msg, msgLen, &writeLen);
    }

    if (ret != GMERR_OK || writeLen != msgLen) {
        if (type == DB_LOG_TYPE_SIGNAL) {
            DbLogCloseFile(&pathCtx->logFd[DB_LOG_TYPE_RUN]);
        } else {
            DbLogCloseFile(&pathCtx->logFd[type]);
        }
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    return GMERR_OK;
}

static Status DbLogSetDirInfo(
    LogFileInfoT *logFileInfo, const DbLogPathCtxT *pathCtx, DbLogTypeE type, const char *logFolderPrefix)
{
    // 文件夹绝对路径，如/.../log/run/rgmserver
    int32_t ret = snprintf_truncated_s(logFileInfo->dirPath, LOG_PATH_MAX, LOG_DIR_PATH_FMT, pathCtx->procPath,
        LOG_SUB_FOLDER, pathCtx->subFold[type], logFolderPrefix, pathCtx->procName);
    if (ret < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    // 日志文件路径前缀，如/.../log/run/rgmserver/rgmserver
    ret = snprintf_truncated_s(logFileInfo->filePref, LOG_PATH_MAX, LOG_FILE_PREFIX_FMT, logFileInfo->dirPath,
        logFolderPrefix, pathCtx->procName);
    if (ret < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

static Status DbLogSetFileInfo(LogFileInfoT *logFileInfo, const DbLogPathCtxT *pathCtx, DbLogTypeE type)
{
    DB_POINTER2(logFileInfo, pathCtx);

    // 日志子文件夹前缀
    const char *logFolderPrefix[] = {"o", "r", "s", "a", "d"};
    // 校验是否是绝对路径，第一个字母是否为/
    if (pathCtx->procPath[0] != '/') {
        return GMERR_GET_PATH_FAILED;
    }
    // 审计日志和普通日志命名方式保持一致
    Status ret = GMERR_OK;
    if (type == DB_LOG_TYPE_SIGNAL) {
        ret = DbLogSetDirInfo(logFileInfo, pathCtx, DB_LOG_TYPE_RUN, logFolderPrefix[DB_LOG_TYPE_RUN]);
    } else {
        ret = DbLogSetDirInfo(logFileInfo, pathCtx, type, logFolderPrefix[type]);
    }
    return ret;
}

static Status DbLogSetPathInfo(DbLogPathCtxT *log)
{
    DB_POINTER(log);
    if (getcwd(log->procPath, LOG_PATH_MAX) == NULL) {
        return GMERR_GET_PATH_FAILED;
    }

    char *subFoldName[] = {"operate", "run", "secure", "alarm", "debug"};
    for (uint32_t i = 0; i < DB_LOG_TYPE_BUTT; i++) {
        errno_t errRet = strcpy_s(log->subFold[i], LOG_SUB_FOLD_NAME_LEN, subFoldName[i]);
        if (errRet != EOK) {
            return GMERR_FIELD_OVERFLOW;
        }
    }
    return DbGetProcessNameByPid(DbAdptGetpid(), log->procName, DB_MAX_PROC_NAME_LEN);
}

// 用途：存放LOG的上下文信息
// 是否并发初始化：否
// 是否并发读写：否
// 并发方案：无
Handle g_gmdbLogHandle = NULL;  // DbLogPathCtxT

static Handle DbLogHandleCreate(void)
{
    DbLogPathCtxT *logCtx = (DbLogPathCtxT *)DbAdptDynamicMemAlloc(sizeof(DbLogPathCtxT));
    if (logCtx == NULL) {
        return NULL;
    }
    (void)memset_s(logCtx, sizeof(DbLogPathCtxT), 0, sizeof(DbLogPathCtxT));
    Status ret = DbLogSetPathInfo(logCtx);
    if (ret != GMERR_OK) {
        DbAdptDynamicMemFree(logCtx);
        return NULL;
    }
    for (uint32_t i = 0; i < DB_LOG_TYPE_BUTT; i++) {
        logCtx->logFd[i] = DB_INVALID_FD;
        DbSpinInit(&logCtx->fileLock[i]);
        (void)memset_s(logCtx->filePath[i], LOG_PATH_MAX, 0, LOG_PATH_MAX);
    }
    return logCtx;
}

static void DbLogHandleDestroy(Handle handle)
{
    DbLogPathCtxT *pathCtx = handle;
    if (pathCtx != NULL) {
        for (uint32_t i = 0; i < (uint32_t)DB_LOG_TYPE_BUTT; i++) {
            DbLogCloseFile(&pathCtx->logFd[i]);
        }
        DbAdptDynamicMemFree(pathCtx);
    }
}

#ifdef FEATURE_SERVER_FUNC_REG
DbLogFuncsT g_gmdbEulerLogFuns = {0};
bool g_gmdbEulerLogInit = false;
Status DbAdptRegLogFuncs(const DbLogFuncsT *userDefLogFuncs)
{
    DB_POINTER(userDefLogFuncs);
    if (g_gmdbEulerLogInit) {
        return GMERR_DUPLICATE_OBJECT;
    }
    g_gmdbEulerLogFuns = *userDefLogFuncs;
    g_gmdbEulerLogInit = true;
    return GMERR_OK;
}
#endif

Status DbAdptEulerLogInit(void)
{
#ifdef FEATURE_SERVER_FUNC_REG
    if (!g_gmdbEulerLogInit) {  // 未初始化，用固有能力
        g_gmdbLogHandle = DbLogHandleCreate();
        if (g_gmdbLogHandle == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "Create log handle with problem.");
            return GMERR_INTERNAL_ERROR;
        }
        return GMERR_OK;
    }
    if (g_gmdbEulerLogFuns.logInit != NULL) {
        g_gmdbLogHandle = g_gmdbEulerLogFuns.logInit();
        if (g_gmdbLogHandle == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "Create log handle with problem.");
            return GMERR_INTERNAL_ERROR;
        }
        return GMERR_OK;
    } else {
        return GMERR_OK;
    }
#else
    if (g_gmdbLogHandle != NULL) {
        return GMERR_OK;
    }
    g_gmdbLogHandle = DbLogHandleCreate();
    if (g_gmdbLogHandle == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "Create log handle with problem.");
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
#endif
}

void DbAdptEulerLogUnInit(void)
{
#ifdef FEATURE_SERVER_FUNC_REG
    if (!g_gmdbEulerLogInit) {  // 未初始化，用固有能力
        DbLogHandleDestroy(g_gmdbLogHandle);
    } else if (g_gmdbEulerLogFuns.logDestroy != NULL) {
        g_gmdbEulerLogFuns.logDestroy(g_gmdbLogHandle);
    }
#else
    DbLogHandleDestroy(g_gmdbLogHandle);
#endif
    g_gmdbLogHandle = NULL;
}

void DbAdptLogLevelSet(uint32_t level, uint32_t durationSec)
{
#ifdef FEATURE_SERVER_FUNC_REG
    if (g_gmdbEulerLogFuns.logLevelSet != NULL) {
        g_gmdbEulerLogFuns.logLevelSet(g_gmdbLogHandle, level, durationSec);
    }
#else
    DB_UNUSED2(level, durationSec);
#endif
}

// 在嵌入式设备，日志打印使用云杉组件进行统一打印
#define ORM_LOG_LIB_PATH "/usr/local/lib/libdblog_adapter.so"
// 在智算产品使用环境变量配置路径
#define ORM_LOG_LIB_NAME "libdblog_adapter.so"
enum {
    LOG_CATEGORY_GMDB_APP = 0x700,    // APP操作GMDB交互日志
    LOG_CATEGORY_AUDIT_LOGS = 0x900,  // 审计日志日志
};

static struct {
    bool isInited;             // ORM提供的接口是否已经初始化
    uint8_t reserve[3];        // 3个字符，预留字段
    void *handle;              // loger handle
    void *(*gmLogInit)(void);  // 日志模块初始化函数
    void (*gmLogDestroy)(void *handle);
    void (*gmLogWrite)(void *handle, uint32_t level, uint32_t errorCode, const char *str);
    void *logAdapter;
} g_gmdbAdptOrmLog = {0};

// 尝试使用云杉平台提供的日志打印，执行失败将使用欧拉默认的日志打印方式
Status DbAdptOrmLogInit(void)
{
    if (g_gmdbAdptOrmLog.isInited) {
        return GMERR_OK;
    }
    void *adapterHandle = dlopen(ORM_LOG_LIB_PATH, RTLD_LAZY);
    if (adapterHandle == NULL) {
        adapterHandle = dlopen(ORM_LOG_LIB_NAME, RTLD_LAZY);
    }
    if (adapterHandle == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED, "Register orm log Func, %s", dlerror());
        return GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED;
    }

    DbPatchRestore();

    const FuncTableItemT table[] = {{&g_gmdbAdptOrmLog.gmLogInit, "GmLogInit"},
        {&g_gmdbAdptOrmLog.gmLogDestroy, "GmLogDestroy"}, {&g_gmdbAdptOrmLog.gmLogWrite, "GmLogWrite"}};

    Status ret = DbAdptLoadFunc(adapterHandle, table, ELEMENT_COUNT(table));
    if (ret != GMERR_OK) {
        (void)dlclose(adapterHandle);
        return ret;
    }

    g_gmdbAdptOrmLog.handle = g_gmdbAdptOrmLog.gmLogInit();
    if (g_gmdbAdptOrmLog.handle == NULL) {
        (void)dlclose(adapterHandle);
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }

    g_gmdbAdptOrmLog.isInited = true;
    g_gmdbAdptOrmLog.logAdapter = adapterHandle;
    return GMERR_OK;
}

void DbAdptOrgLogUnInit(void)
{
    if (g_gmdbAdptOrmLog.handle != NULL) {
        g_gmdbAdptOrmLog.gmLogDestroy(g_gmdbAdptOrmLog.handle);
    }
    if (g_gmdbAdptOrmLog.logAdapter != NULL) {
        (void)dlclose(g_gmdbAdptOrmLog.logAdapter);
        g_gmdbAdptOrmLog.logAdapter = NULL;
    }
    g_gmdbAdptOrmLog.isInited = false;
}

// 日志写失败不用往上层抛错，因此函数设计为无返回值
void DbLogAdptWriteOrmLog(const DbLogWriteArgT *arg, const LogTextStrT *logText)
{
    DB_POINTER2(arg, logText);
    if (!g_gmdbAdptOrmLog.isInited) {
        return;
    }
    // 除审计日志单独一个场景外，其他类型日志场景均相同
    uint32_t categoryId = (arg->type == (int32_t)DB_LOG_TYPE_SECURE ? LOG_CATEGORY_AUDIT_LOGS : LOG_CATEGORY_GMDB_APP);
    // hlog出错会抛错误码，db采取记录但是忽略错误码的处理方式
    g_gmdbAdptOrmLog.gmLogWrite(g_gmdbAdptOrmLog.handle, categoryId | arg->level, (uint32_t)arg->errCode, logText->buf);
}

static void DbLogAdptEulerWriteImpl(const DbLogWriteArgT *arg, const LogTextStrT *logText)
{
    if (g_gmdbLogHandle == NULL) {
        return;
    }
    // 获取文件路径信息
    LogFileInfoT fileInfo = {0};
    Status ret = DbLogSetFileInfo(&fileInfo, g_gmdbLogHandle, (DbLogTypeE)arg->type);
    if (ret != GMERR_OK) {
        return;
    }
    (void)DbLogWriteInner(g_gmdbLogHandle, (DbLogTypeE)arg->type, &fileInfo, logText->buf, logText->pos);
}

static void DbLogAdptAddCommonMsg(LogTextStrT *logCommonText, const DbLogWriteArgT *arg, const LogTextStrT *logText)
{
    Status ret = DbLogAddTimestamp(logCommonText, 0);
    if (ret != GMERR_OK) {
        return;
    }
    uint32_t pid = DbAdptGetpid();
    uint32_t threadId = DbThreadGetSysTid();
    // 数组描述顺序和 DbLogLevelE 必须一致
    // BOOT日志会打屏，日志级别为WARN或者ERROR时候影响光启构建，因此BOOT日志不打级别，不影响DFX
    if (arg->type != DB_LOG_TYPE_BUTT) {
        char *levelDesc[] = {"EMERGENCY", "ERROR", "WARN", "INFO", "DEBUG", "INVALID"};
        DbLogTextPrintf(logCommonText, "%" PRIu32 ", %" PRIu32 ", %s, ", pid, threadId, levelDesc[arg->level]);
    } else {
        DbLogTextPrintf(logCommonText, "%" PRIu32 ", %" PRIu32 ", ", pid, threadId);
    }
    DbLogTextPrintf(logCommonText, "%s", logText->buf);  // 拼接基础信息和日志内容，构成euler下完整日志
    DbLogTextFinishPrint(logCommonText);                 // 换行符
}

void DbLogAdptEulerWrite(const DbLogWriteArgT *arg, const LogTextStrT *logText)
{
    DB_POINTER2(arg, logText);
    /* adpt层调用不到common层的DB_MALLOC，使用原生malloc，此处使用后即释放无风险 */
    char *logBuf = (char *)malloc(DB_DEFAULT_LOG_SIZE + 1);
    if (logBuf == NULL) {
        return;
    }
    (void)memset_s(logBuf, (DB_DEFAULT_LOG_SIZE + 1), 0, (DB_DEFAULT_LOG_SIZE + 1));
    LogTextStrT logCommonText = {logBuf, (uint32_t)(logText->bufLen + DB_COMMON_LOG_HEADER_SIZE), 0};
#ifdef FEATURE_SERVER_FUNC_REG
    if (g_gmdbEulerLogFuns.logWrite != NULL) {
        g_gmdbEulerLogFuns.logWrite(g_gmdbLogHandle, arg->level, arg->errCode, logText->buf);
    } else {
        DbLogAdptAddCommonMsg(&logCommonText, arg, logText);  // 如果函数注册下走DB自己接口，则需要拼装完整日志
        DbLogAdptEulerWriteImpl(arg, &logCommonText);
    }
#else
    DbLogAdptAddCommonMsg(&logCommonText, arg, logText);  // 拼接基础信息，timestamp，tid，level
    DbLogAdptEulerWriteImpl(arg, &logCommonText);
#endif
    free(logBuf);
}

#define DB_ERROR_CODE_LEN 16  // 错误码长度最大值，当前内部错误码最长为9位
static void DbLogAdptWriteGmcLog(const DbLogWriteArgT *arg, const LogTextStrT *logText)
{
    DB_POINTER3(arg, logText, logText->buf);
    DbSpinLockWithoutLog(&g_gmdbLogAdapterFuncsLock);  // 日志模块使用不打印超时日志锁
    const GmcLogAdptFuncsT *funcs = DbLogGetAdptFuncs();
    if (funcs->userWriteFunc != NULL) {
        char errCodeStr[DB_ERROR_CODE_LEN] = {0};
        int32_t ret = sprintf_s(errCodeStr, DB_ERROR_CODE_LEN, "%d", arg->errCode);
        if (ret < 0) {
            DbSpinUnlock(&g_gmdbLogAdapterFuncsLock);
            return;
        }
        // 除审计日志单独一个场景外，其他类型日志场景均相同
        uint32_t categoryId = (arg->type == DB_LOG_TYPE_SECURE ? LOG_CATEGORY_AUDIT_LOGS : LOG_CATEGORY_GMDB_APP);
        funcs->userWriteFunc(funcs->handle, categoryId | arg->level, errCodeStr, logText->buf);
    } else {
#ifndef NDEBUG
        DbWriteSimpleBootLog(CLI, logText->buf);  // 客户端日志打印到文件中

#endif /* NDEBUG */
    }
    DbSpinUnlock(&g_gmdbLogAdapterFuncsLock);
}

void DbLogAdptWrite(const DbLogWriteArgT *arg, const LogTextStrT *logText)
{
    // 优先使用gmc log
    if (DbLogGetAdptFuncs()->userWriteFunc != NULL) {
        DbLogAdptWriteGmcLog(arg, logText);
    } else if (g_gmdbAdptOrmLog.isInited) {
        DbLogAdptWriteOrmLog(arg, logText);
    } else {
        DbLogAdptEulerWrite(arg, logText);
    }
}

/*
 * description: euler启动日志写接口，写 syslog 和打屏； EMBEDDED 宏内（rtos环境下）与 调测日志和审计日志写保持相同；
 */
void DbAdptWriteBootLog(const DbLogWriteArgT *arg, const LogTextStrT *logText)
{
    DB_POINTER3(arg, logText, logText->buf);
    DbLogAdptWriteOrmLog(arg, logText);
    char logBuf[(uint32_t)DB_DEFAULT_LOG_SIZE + 1] = {0};
    LogTextStrT logCommonText = {logBuf, (uint32_t)(DB_DEFAULT_LOG_SIZE + 1), 0};
    DbLogAdptAddCommonMsg(&logCommonText, arg, logText);
    syslog((int32_t)arg->level, "%s", logText->buf);
    DbPrintToStdout("%s", logCommonText.buf);
}

/*
 * description: euler启动日志初始化，当前无任何依赖，因此是空函数
 */
Status DbAdptInitBootLog(void)
{
    return GMERR_OK;
}

void DbReportUserException(int signo)
{
    return;
}

void DbSilentFaultReport(uint32_t instanceType, uint32_t faultId, char *format, ...)
{
    return;
}

void *DbAdptLogGetHandle(void)
{
    return NULL;
}

void *DbAdptLogGetWriteFunc(void)
{
    return NULL;
}

void DbAdptLogWriteToJournal(uint32_t errCodeNum, const char *logBuf)
{
    return;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
