/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: adpt_rdtsc.c
 * Description: source file for rdtsc
 * Author:
 * Create: 2020-7-27
 */

#include "adpt_rdtsc.h"
#include "adpt_log.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

// 用途：记录时间与cycle数的对应关系
// 是否并发初始化：否
// 是否并发读写：否
// 并发方案：无
static uint64_t g_dmdbCyclesPerSec = DB_INVALID_UINT64;
static uint64_t g_dmdbCyclesPerMsec = DB_INVALID_UINT64;
static uint64_t g_dmdbCyclesPerUsec = DB_INVALID_UINT64;
static uint64_t g_gmdbCyclesPerNsec = DB_INVALID_UINT64;

#define DB_RDTSC_MAX_MICROS 10000

void DbRdtscInit(void)
{
#if (defined(HPE) || defined(RTOSV2X)) && !defined(ARM32) && !defined(X86_64)
    // arm32/x86_64架构上HPE还不支持这个寄存器
    g_dmdbCyclesPerSec = DbRdFrq();
    g_dmdbCyclesPerMsec = g_dmdbCyclesPerSec / MSECONDS_IN_SECOND;
    g_dmdbCyclesPerUsec = g_dmdbCyclesPerMsec / USECONDS_IN_MSECOND;
    g_gmdbCyclesPerNsec = g_dmdbCyclesPerUsec / NSECONDS_IN_USECOND;
    return;
#else
    if (g_dmdbCyclesPerSec != DB_INVALID_UINT64) {
        return;
    }

    /**
     * Compute the fine-grained CPU timer's frequency:
     * take parallel time readings using both rdtsc and clock_gettime.
     * After 10ms have elapsed, take the ratio between these readings.
     * There is one tricky aspect, which is that we could get interrupted
     * between calling clock_gettime and reading the cycle counter,
     * in which case we won't have corresponding readings.
     * To handle this (unlikely) case, compute the overall result repeatedly,
     * and wait until we get two successive calculations that are within 0.1% of each other.
     */
    double oldCycles = 0;
    while (g_dmdbCyclesPerSec == DB_INVALID_UINT64) {
        uint64_t startTimeUs = DbGettimeMonotonicUsec();
        uint64_t startCycles = DbRdtsc();
        double cyclesPerSec = 0;
        while (DbAbs(cyclesPerSec) < DB_DOUBLE_COMPARE_PRECISION) {
            uint64_t endTimeUs = DbGettimeMonotonicUsec();
            uint64_t stopCycles = DbRdtsc();
            uint64_t timeElapsed = endTimeUs - startTimeUs;
            // 经过10ms
            if (timeElapsed > DB_RDTSC_MAX_MICROS) {
                uint64_t cycleElapsed = stopCycles - startCycles;
                cyclesPerSec = USECONDS_IN_SECOND * (double)cycleElapsed / ((double)(timeElapsed));
                break;
            }
        }
        double delta = cyclesPerSec / DB_KILO;
        // 连续两次计算得到的CyclesPerSec值波动率在0.1%以内，则值有效
        if ((oldCycles > (cyclesPerSec - delta)) && (oldCycles < (cyclesPerSec + delta))) {
            g_dmdbCyclesPerSec = (uint64_t)cyclesPerSec;
            g_dmdbCyclesPerMsec = g_dmdbCyclesPerSec / MSECONDS_IN_SECOND;
            g_dmdbCyclesPerUsec = g_dmdbCyclesPerMsec / USECONDS_IN_MSECOND;
            g_gmdbCyclesPerNsec = g_dmdbCyclesPerUsec / NSECONDS_IN_USECOND;
            return;
        }
        oldCycles = cyclesPerSec;
    }
#endif
}

uint64_t DbGetCyclePerMsec(void)
{
    return g_dmdbCyclesPerMsec;
}

// 日志使用的double,这个函数暂时不修改为uint64_t
double DbToSeconds(uint64_t cycles)
{
    return ((double)cycles) / (double)g_dmdbCyclesPerSec;
}

uint64_t DbToMseconds(uint64_t cycles)
{
    return (uint64_t)(cycles / g_dmdbCyclesPerMsec);
}

uint64_t DbToUseconds(uint64_t cycles)
{
    return (uint64_t)(cycles / g_dmdbCyclesPerUsec);
}

uint64_t DbToNseconds(uint64_t cycles)
{
    return (uint64_t)(cycles / g_gmdbCyclesPerNsec);
}

uint64_t DbSecondToCycle(uint64_t sec)
{
    return (uint64_t)(sec * g_dmdbCyclesPerSec);
}

uint64_t DbGetCyclesPerSec(void)
{
    return g_dmdbCyclesPerSec;
}

uint64_t DbGetCyclesPerMsec(void)
{
    return g_dmdbCyclesPerMsec;
}

uint64_t DbGetCyclesPerUsec(void)
{
    return g_dmdbCyclesPerUsec;
}

uint64_t DbGetTimestampUsecond(uint64_t cycles)
{
    uint64_t nowTimestamp = DbGetCurrentTimestamp();
    uint64_t nowCycles = DbRdtsc();
    return nowTimestamp - (uint64_t)DbToUseconds(nowCycles - cycles);
}

Status DbCycleMsGetTimestampStr(uint64_t ms, char *timeStr, uint16_t strSize, TimeStrFormatIdE formatId)
{
    CmTimeDescT cDesc = {0};
    uint64_t nowTimestamp = DbGetCurrentTimestamp() / USECONDS_IN_MSECOND;
    uint64_t nowMs = DbToMseconds(DbRdtsc());

    uint64_t timeMS = nowTimestamp - (nowMs - ms);
    double days = DbEncodeTimestamp((int64_t)(timeMS / MSECONDS_IN_SECOND));

    DbDecodeTimeDesc(days, &cDesc);

    if (formatId >= INVALID_TIME_FORMAT) {
        return DbTimeToStr(&cDesc, timeStr, strSize, DEFAULT_TIME_FORMAT);
    }
    return DbTimeToStr(&cDesc, timeStr, strSize, formatId);
}

Status DbGetTimestampStr(uint64_t cycles, char *timeStr, uint16_t strSize, TimeStrFormatIdE formatId)
{
    CmTimeDescT cDesc = {0};
    uint64_t timestamp = DbGetTimestampUsecond(cycles);
    double days = DbEncodeTimestamp((int64_t)(timestamp / USECONDS_IN_SECOND));
    DbDecodeTimeDesc(days, &cDesc);
    if (formatId >= INVALID_TIME_FORMAT) {
        return DbTimeToStr(&cDesc, timeStr, strSize, DEFAULT_TIME_FORMAT);
    }
    return DbTimeToStr(&cDesc, timeStr, strSize, formatId);
}

Status DbGetTimestampStrByTimeVal(uint64_t timeVal, char *timeStr, uint16_t strSize, TimeStrFormatIdE formatId)
{
    CmTimeDescT cDesc = {0};
    double days = DbEncodeTimestamp((int64_t)(timeVal / USECONDS_IN_SECOND));
    DbDecodeTimeDesc(days, &cDesc);
    if (formatId >= INVALID_TIME_FORMAT) {
        return DbTimeToStr(&cDesc, timeStr, strSize, DEFAULT_TIME_FORMAT);
    }
    return DbTimeToStr(&cDesc, timeStr, strSize, formatId);
}

Status DbGetDurationStr(uint64_t startTime, uint64_t endTime, char *timeStr, uint32_t timeStrLen)
{
    DB_POINTER(timeStr);

    uint64_t runTotalSec = (uint64_t)DbToSeconds(endTime - startTime);
    uint64_t runDay = runTotalSec / SECOND_OF_ONE_DAY;
    uint64_t runHour = ((uint64_t)(runTotalSec % SECOND_OF_ONE_DAY)) / SECOND_OF_ONE_HOUR;
    uint64_t runMin = ((uint64_t)((runTotalSec % SECOND_OF_ONE_DAY) % SECOND_OF_ONE_HOUR)) / SECOND_OF_ONE_MINUTE;
    uint64_t runSec = ((uint64_t)((runTotalSec % SECOND_OF_ONE_DAY) % SECOND_OF_ONE_HOUR)) % SECOND_OF_ONE_MINUTE;

    int32_t len = snprintf_s(timeStr, timeStrLen, timeStrLen - 1,
        "%" PRIu64 "days %02" PRIu64 ":%02" PRIu64 ":%02" PRIu64, runDay, runHour, runMin, runSec);
    if (len < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

Status DbGetDurationStrByTimeVal(uint64_t startTimeVal, uint64_t endTimeVal, char *timeStr, uint32_t timeStrLen)
{
    DB_POINTER(timeStr);

    uint64_t runTotalSec = (endTimeVal - startTimeVal) / USECONDS_IN_SECOND;
    uint64_t runDay = runTotalSec / SECOND_OF_ONE_DAY;
    uint64_t runHour = ((uint64_t)(runTotalSec % SECOND_OF_ONE_DAY)) / SECOND_OF_ONE_HOUR;
    uint64_t runMin = ((uint64_t)((runTotalSec % SECOND_OF_ONE_DAY) % SECOND_OF_ONE_HOUR)) / SECOND_OF_ONE_MINUTE;
    uint64_t runSec = ((uint64_t)((runTotalSec % SECOND_OF_ONE_DAY) % SECOND_OF_ONE_HOUR)) % SECOND_OF_ONE_MINUTE;

    int32_t len = snprintf_s(timeStr, timeStrLen, timeStrLen - 1,
        "%" PRIu64 "days %02" PRIu64 ":%02" PRIu64 ":%02" PRIu64, runDay, runHour, runMin, runSec);
    if (len < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
