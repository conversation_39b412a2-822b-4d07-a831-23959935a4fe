/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: db_file.h
 * Description: Implement of GMDB client resource
 * Author:
 * Create: 2020-08-10
 */

#ifndef DB_FILE_H
#define DB_FILE_H

#include "adpt_types.h"
#include "adpt_file.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/* define the directory splitter */
#define DB_DIR_SPLIT_CHAR '/'

#define WRITE_ONLY O_WRONLY
#define READ_ONLY O_RDONLY
#define READ_WRITE O_RDWR
#define CREATE_FILE O_CREAT
#define APPEND_FILE O_APPEND
#define TRUNCATE_FILE O_TRUNC

#define MODE_O_EXCL O_EXCL

#define O_BINARY 0 /* no text/binary mode flag in Unix */
/* permission */
#define PERM_USER (S_IRUSR)
#define PERM_USRRW (S_IRUSR | S_IWUSR)
#define PERM_USRRWX (S_IRUSR | S_IWUSR | S_IXUSR)
#define PERM_GRPR (S_IRUSR | S_IWUSR | S_IRGRP)
#define PERM_GRPX (S_IRUSR | S_IWUSR | S_IXUSR | S_IXGRP)
#define PERM_GRPRW (S_IRUSR | S_IWUSR | S_IRGRP | S_IWGRP)
#define PERM_GRPRX (S_IRUSR | S_IWUSR | S_IXUSR | S_IRGRP | S_IXGRP)
#define PERM_GRPRWX (S_IRUSR | S_IWUSR | S_IXUSR | S_IRGRP | S_IWGRP | S_IXGRP)
#define PERM_OTHGRPRW (S_IXUSR | S_IXGRP | S_IROTH | S_IWOTH | S_IXOTH)
#define PERM_OTHGRPR (S_IXUSR | S_IXGRP | S_IWGRP | S_IROTH | S_IWOTH | S_IXOTH)
#define PERM_OTHGRPRX (S_IXUSR | S_IRGRP | S_IXGRP | S_IWGRP | S_IROTH | S_IWOTH | S_IXOTH)

#define OS_PATH_SEP_CHAR '/'

ADPTR_EXPORT Status DbGetRealPath(const char *path, char *realPath, size_t maxLen);
ADPTR_EXPORT Status DbGetRealPathNoLog(const char *path, char *realPath, size_t maxLen);
ADPTR_EXPORT Status DbOpenFile(const char *fullPath, int32_t flag, uint32_t permission, int32_t *fd);
ADPTR_EXPORT Status DbOpenPipe(int pipefd[2]);
ADPTR_EXPORT Status DbReadFile(int32_t fd, void *buf, size_t count, size_t *readCount);
ADPTR_EXPORT Status DbWriteFile(int32_t fd, const void *buf, size_t count);
ADPTR_EXPORT void DbCloseFile(int32_t fd);
ADPTR_EXPORT Status DbRemoveFile(const char *fileName);
ADPTR_EXPORT Status DbRemoveFileNoLog(const char *fileName);
ADPTR_EXPORT bool DbFileExist(const char *filePath);
ADPTR_EXPORT bool DbDirExist(const char *dirPath);
ADPTR_EXPORT Status DbFileSize(const char *filePath, size_t *size);
ADPTR_EXPORT Status DbFileSizeByFd(int fd, size_t *size);
ADPTR_EXPORT Status DbReadFileExpSize(int32_t fd, void *buf, size_t count);
// 创建权限指定为750的文件夹
ADPTR_EXPORT Status DbMakeDirWithGRPRXPermission(const char *fullPath);
ADPTR_EXPORT Status DbMakeDirectory(const char *fullPath, uint32_t mode);
ADPTR_EXPORT Status DbReadLine(int32_t fd, char *buffer, size_t bufferSize, size_t *totalRead);
ADPTR_EXPORT Status DbRemoveDir(const char *filePath);
ADPTR_EXPORT void DbRemoveUmask(void);

ADPTR_EXPORT Status DbFsyncFile(int32_t fd);
ADPTR_EXPORT Status DbSeekFileHead(int32_t fd, int64_t offset);
#ifndef NDEBUG
ADPTR_EXPORT Status DbSeekFileEnd(int32_t fd, int64_t offset);
#endif
ADPTR_EXPORT Status DbPreadFile(int32_t fd, char *buf, uint32_t count, int64_t offset, uint32_t *readCount);
ADPTR_EXPORT Status DbPwriteFile(int32_t fd, const char *buf, uint32_t count, int64_t offset);

typedef struct DbDIR DbDIRT;
ADPTR_EXPORT Status DbOpenDir(const char *dirPath, DbDIRT **dir);
ADPTR_EXPORT Status DbReadDir(DbDIRT *dir, char subDir[], uint32_t subDirSize);
ADPTR_EXPORT void DbCloseDir(DbDIRT *dir);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DB_FILE_H */
