/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: adpt_define.h
 * Description: header file for GMDB define
 * Author:
 * Create: 2020-7-27
 */

#ifndef ADPT_DEFINE_H
#define ADPT_DEFINE_H

#include <stdint.h>
#include <stdlib.h>
#include <securec.h>
#include <assert.h>
#include <unistd.h>
#include "gmc_errno.h"
#include "gmc_types.h"

#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
#include <sched.h>
#endif

#ifdef __cplusplus
extern "C" {
#endif

#ifdef _ASAN
#define DB_ASSERT(exp)              \
    do {                            \
        if (!(exp)) {               \
            sync();                 \
            *((int32_t *)NULL) = 1; \
        }                           \
    } while (0)  // NOLINT
#else
#ifndef NDEBUG
#define DB_ASSERT(exp) assert(exp)
#else
#define DB_ASSERT(exp) (void)(sizeof(!!(exp)))
#endif
#endif

#ifdef NDEBUG
#define GMDB_EXPORT GMC_EXPORT
#else
#define LATCH_CONFLICT_DEBUG
#ifdef INJECT
#define GMDB_EXPORT __attribute__((aligned(8)))
#else
#define GMDB_EXPORT
#endif
#endif

// TSDB和多so模式下，涉及跨库调用所以需要export，单so下这部分接口如果export则会导致符号增加，二进制大小增大
#if !defined(TS_MULTI_INST) && !defined(SINGLE_SO)
#define SO_EXPORT GMDB_EXPORT
#else
#define SO_EXPORT
#endif

#if !defined(FEATURE_SIMPLEREL)
#define ADPTR_EXPORT GMDB_EXPORT
#else
#define ADPTR_EXPORT
#endif

#ifdef TS_MULTI_INST
#define SO_EXPORT_FOR_TS GMDB_EXPORT
#else
#define SO_EXPORT_FOR_TS SO_EXPORT
#endif

#ifdef FEATURE_HAC
#define SO_EXPORT_FOR_HAC GMDB_EXPORT
#else
#define SO_EXPORT_FOR_HAC SO_EXPORT
#endif

#if defined(FEATURE_HAC) || defined(TS_MULTI_INST)
#define SO_EXPORT_FOR_HAC_AND_TS GMDB_EXPORT
#else
#define SO_EXPORT_FOR_HAC_AND_TS SO_EXPORT
#endif

// 注意，DB_DIE只能挂死线程使用，其他地方不可使用
#define DB_DIE abort()
#define DB_EXIT exit(1)
// 后续方便 "打开|关闭", 保留这个宏, 当前默认开放
#define SHM_MEM_BLOCK_CHECK
#ifdef LATCH_CONFLICT_DEBUG
#define RLATCH true
#define WLATCH false
#define LATCH_GET_START_WAITTIMES(mode, latchInfo) \
    uint32_t originWaitTimes = ((mode) ? ((latchInfo).readerWaitTimes) : ((latchInfo).writerWaitTimes))

#define LATCH_GET_END_WAITTIMES(mode, latchInfo, session)                                       \
    do {                                                                                        \
        uint32_t endWaitTimes = ((mode) ? (((latchInfo).readerWaitTimes) - (originWaitTimes)) : \
                                          (((latchInfo).writerWaitTimes) - (originWaitTimes))); \
        DbUpdateLatchConflictStatWithSession(endWaitTimes, session, mode);                      \
    } while (0)

#else
#define LATCH_GET_START_WAITTIMES(mode, latchInfo)
#define LATCH_GET_END_WAITTIMES(mode, latchInfo, session)
#endif

#if !defined(RTOSV2) && !defined(RTOSV2X) && !defined(HPE)
#define DB_ENV_EULER 1
#else
#define DB_ENV_EULER 0
#endif

typedef enum DbPrefetchType { DB_PREFECH_READ = 0, DB_PREFECH_WRITE } DbPrefetchTypeE;

typedef enum DbPrefetchLocalityType {
    DB_PREFECH_LOCALITY_NONE = 0,
    DB_PREFECH_LOCALITY_LOW,
    DB_PREFECH_LOCALITY_MEDIUM,
    DB_PREFECH_LOCALITY_HIGH
} DbPrefetchLocalityTypeE;

typedef enum { ADPT_HPE = 10, ADPT_RTOS_SERVER, ADPT_INVALID_ENV } AdptEnvironmentE;

#define DB_PREFETCH(addr, readOrWrite, locality) __builtin_prefetch(addr, readOrWrite, locality)

#define DB_THREAD_LOCAL __thread

#define ALWAYS_INLINE inline __attribute__((always_inline))

#ifdef MINI_BAG
#define ALWAYS_INLINE_C
#else
#define ALWAYS_INLINE_C ALWAYS_INLINE
#endif

#define DIRECT_WRITE_SECTION_COMMON __attribute__((section(".directWrite")))
#define DIRECT_WRITE_SECTION_REPLACE __attribute__((section(".directWrite.replace")))
#define DIRECT_WRITE_SECTION_DATA __attribute__((section(".directWrite.data")))
#define YANG_MODEL_VALIDATE_SECTION __attribute__((section(".yang_model_validate")))

/* calculate element count of an array */
#ifndef ELEMENT_COUNT
#define ELEMENT_COUNT(x) (sizeof(x) / sizeof((x)[0]))
#endif

#define DB_UNUSED(member) (void)(member)
#define DB_UNUSED2(p1, p2) \
    do {                   \
        (void)(p1);        \
        (void)(p2);        \
    } while (0)  // NOLINT
#define DB_UNUSED3(p1, p2, p3) \
    do {                       \
        (void)(p1);            \
        (void)(p2);            \
        (void)(p3);            \
    } while (0)  // NOLINT
#define DB_UNUSED4(p1, p2, p3, p4) \
    do {                           \
        (void)(p1);                \
        (void)(p2);                \
        (void)(p3);                \
        (void)(p4);                \
    } while (0)  // NOLINT
#define DB_UNUSED5(p1, p2, p3, p4, p5) \
    do {                               \
        (void)(p1);                    \
        (void)(p2);                    \
        (void)(p3);                    \
        (void)(p4);                    \
        (void)(p5);                    \
    } while (0)  // NOLINT

#define SIZE_ALIGN8(size) (((uint32_t)(size) + 7) & ~0x7u)
#define SIZE_ALIGN4(size) (((uint32_t)(size) + 3) & ~0x3u)
#define SIZE_FLOOR_ALIGN8(size) (((uint32_t)(size) / 8) * 8)
// align to power of 2
#define DB_CALC_ALIGN(size, align) (((uint64_t)(size) + ((uint64_t)(align)-1)) & ~((uint64_t)((align)-1)))

#define DB_MAX(x, y) (((x) > (y)) ? (x) : (y))
#define DB_MIN(x, y) (((x) < (y)) ? (x) : (y))

#define DB_MAX_PATH 256
#define DB_MAX_SWAP_PATH 199
#define DB_MAX_FILE_SIZE_LIMIT 999999
#define DB_MAX_NAME_LEN 32
#define DB_MAX_SINGLE_PROCESS_INSTANCE_NUM 1  // multi instance under single process
#ifdef WARM_REBOOT
#define MAX_INSTANCE_NUM 64  // multi instance, multi process
#else
#ifdef FEATURE_LARGE_SCALE_MULTI_INSTANCE
#define MAX_INSTANCE_NUM 192  // multi instance, multi process
#else
#define MAX_INSTANCE_NUM 2  // multi instance, multi process
#endif
#endif

#define FIRST_INSTANCE_ID 1
#define FIRST_INVALID_INSTANCE_ID (MAX_INSTANCE_NUM + FIRST_INSTANCE_ID)
#define MAX_INSTANCE_ID (FIRST_INVALID_INSTANCE_ID - 1)

static inline uint32_t DbGetInstanceIdByIdx(uint32_t idx)
{
    return idx + FIRST_INSTANCE_ID;
}

static inline uint32_t DbGetIdxByInstanceId(uint32_t instanceId)
{
    return instanceId - FIRST_INSTANCE_ID;
}

#define DB_RESERVE_HANDLE_NUM 1
#define DB_MAX_MULTIZONE 2
#define DB_ZONE_FIRST_HANDLE 0  // the index of first persist zone handle
#define DB_RESERVE_HANDLE_INDEX DB_MAX_MULTIZONE
#define DB_INVALID_ZONE_ID DB_INVALID_UINT8
#define DB_MAX_WHOLE_PATH (DB_MAX_PATH + DB_MAX_NAME_LEN)
#define DB_MULTIZONE_MAX_PATH (DB_MAX_MULTIZONE * DB_MAX_PATH)

#define MAX_BG_WORKER_NUM 15

// 当前仅NERGC场景下会使用，不包含结束符
#define NERGC_MIN_PWD_LEN 12
#define NERGC_MAX_PWD_LEN 128
#define NERGC_MIN_USER_NAME_LEN 6
#define NERGC_MAX_USER_NAME_LEN 15

#define DB_RPC_CONN_AUTH_LEN 129  // 包含结束符

#define DB_CONFIG_MAX_FILE_SIZE 64000
#define DB_MAX_PROC_NAME_LEN 16
#define DB_MAX_THREAD_NUM 4096  // 4096 is the max thread num in proc
#define DB_GET_THREAD_ID DbThreadGetSelfId
#define MAX_TABLE_NAME_LEN 512
#ifdef EXPERIMENTAL_NERGC
#define MAX_USER_NAME_LEN 16
#define MIN_USER_NAME_LEN 7
#else
#define MAX_USER_NAME_LEN 32
#define MIN_USER_NAME_LEN 1
#endif

#define DB_LOG_THREAD_MSG_LEN 32

#define SESSION_ID_LEN 26           // 通信会话id字节个数
#define SESSION_BINARY_PRINT_LEN 2  // 二进制打印会话id

#define MAX_SUB_TYPE_NAME_LEN 32
#define DB_MAX_FILE_LINE 1024
#define MAX_CONN_NAME_LEN 128
#define MAX_IP_ADDR_LEN 16
#define MAX_RES_POOL_NAME_LEN 64
#define MAX_PSSWD_LENGTH 32
#define MAX_ENV_TYPE_LENGTH 16
#define MAX_COMMAND_LENGTH 32
#define MAX_NAMESPACE_LENGTH 128
#define MAX_IGNORE_OPTION_LENGTH 128
#define MAX_HMAC_KEY_LENGTH 112
#define MAX_SAVEPOINT_LENGTH 128
#define MAX_TABLE_SPACE_LENGTH 128
#define MAX_KV_KEY_LEN 512
#define MAX_VALUE_LEN DB_MEBI  // kv表中value的最大长度
#define MAX_CONN_NUM 1536
#define MIN_CONN_NUM 16
#define EMRG_CONN_NUM 2  // 2个逃生连接
#define MAX_CONN_SRV_MEMCTX_ALLOC_SIZE 1024
#define MIN_CONN_SRV_MEMCTX_ALLOC_SIZE 2
#define MAX_CLIENT_CONN_MEMCTX_ALLOC_SIZE (1024 * DB_MEBI)
#define MIN_CLIENT_CONN_MEMCTX_ALLOC_SIZE (2 * DB_MEBI)
#define MAX_USE_RESERVED_CONN_USER_NUM 5 /* 能够使用特殊连接的用户个数 */
#define MAX_RESERVED_CONN_NUM_PER_USER 2 /* 每个特殊用户预留连接的最大值 */
#define GLOBAL_KV_TABLE_ID 1
#define GLOBAL_KV_TABLE_NAME "T_GMDB"
#define RESOURCE_POOL_RES_ID_LEN_MAX 64u
#define STAT_COUNT_MAX_LABEL_COUNT 100
#define DTL_UDF_NAME_MAX_LEN 640  // 带前缀的UDF名长度上限（含结束符）
#define MAX_TRX_MONITOR_THRESHOLD 1000
#define MMAP_PAGE_SIZE 4096
#define MAX_POLICY_MAP_KEY_LEN (MAX_TABLE_NAME_LEN + MAX_NAMESPACE_LENGTH)
#define DB_MAX_PARTITION_ID 16
#define SE_WORKERPOOL_MAX_WORKER_NUM 4u
#define SE_TASK_LIST_NUM 4u

#ifdef TS_MULTI_INST
#define MAX_TS_SQL_LENGTH 65535
#define MAX_TS_EXPLAIN_LENGTH 65535
#endif

#define DB_DBA_NAME "litedb_admin"
#define DB_DBA_PROCESS_NAME "gmrule;gmips;gmids;gmadmin"

#if defined(HPE) || defined(RTOSV2X) || defined(RTOSV2)
#define DB_DEFAULT_DOMAIN_NAME "channel:"
#define MAX_VDATA_FILE_SIZE (DB_MEBI * 50)
#ifdef HPE_SIMULATION
#undef DB_DEFAULT_DOMAIN_NAME
#define DB_DEFAULT_DOMAIN_NAME "usocket:/run/verona/unix_emserver"
#endif
#else
#define DB_DEFAULT_DOMAIN_NAME "usocket:/run/verona/unix_emserver"
#define MAX_VDATA_FILE_SIZE (DB_MEBI * 100)
#endif
// 兼容V3的domin name
#define SHM_UNIX_EMSERVER "shm:unix_emserver"
#define DB_SCHEMA_MAX_DEPTH 32
// 对齐v3 计算方式 (DB_SCHEMA_MAX_DEPTH + 1) * (128 + 1)
#define DB_MAX_INDEX_NAME_LENGTH 4257
#define DB_MAX_PATH_NAME_LENGTH (DB_SCHEMA_MAX_DEPTH * DM_MAX_NAME_LENGTH)

#define DB_PERCENTAGE_BASE 100

#define MAX_FILTER_STRUCT_NUM 8

#define MAX_OS_USER_NAME_LENGTH 128
#define DB_AUDIT_USER_INFO (16 + MAX_OS_USER_NAME_LENGTH)  // "uid-procName"
#define MAX_OS_READ_LINE 1000

#define BITS_PER_BYTE 8
#define BITS_PER_WORD 16
#define BITS_PER_DWORD 32

#define DB_KIBI 1024u
#define DB_MEBI (DB_KIBI * 1024)
#define DB_GIBI (DB_MEBI * 1024)
#define DB_TEBI (DB_GIBI * 1024)
#define DB_PEBI (DB_TEBI * 1024)

#define DB_KILO 1000
#define DB_MEGA (DB_KILO * 1000)
#define DB_GIGA (DB_MEGA * 1000)
#define DB_TERA (DB_GIGA * 1000)
#define DB_PETA (DB_TERA * 1000)

#define MAX_DEFAULT_FILE_SIZE DB_MEBI
#define MAX_CONFIG_FILE_SIZE DB_MEBI
#define MAX_VSCHEMA_FILE_SIZE DB_MEBI
#define MAX_ESCHEMA_FILE_SIZE DB_MEBI
#define MAX_RESPOOL_FILE_SIZE DB_MEBI
#define MAX_NSP_FILE_SIZE DB_MEBI

#define SCHEMA_VERSION_NUM 8u

// clang-format off
#define DB_HIGH_20BIT_MASK 0x00000FFF
#define DB_LOW_12BIT(u32) ((uint16_t)((u32) & 0x00000FFF))
#define DB_HIGH_17BIT(u32) ((uint32_t)(((u32) >> 15) & 0x0001FFFF))
#define DB_HIGH_32BIT(u64) ((uint32_t)(((u64) & 0xFFFFFFFF00000000) >> 32))
#define DB_LOW_32BIT(u64) ((uint32_t)((u64) & 0xFFFFFFFF))
#define DB_LEFT_SHIFT_15BIT(u32) ((uint32_t)((u32) << 15))
// clang-format on
/* judge whether the value is power of 2 or not */
// clang-format off
#define DB_IS_VAL_POWER_OF_2(val) (0 == (((uint64_t)(val) - 1) & (uint64_t)(val)))
// clang-format on
#ifndef NDEBUG
#define DB_POINTER(p) DB_ASSERT(!(NULL == (p)))
#define DB_POINTER2(p1, p2) DB_ASSERT(!((NULL == (p1)) || (NULL == (p2))))
#define DB_POINTER3(p1, p2, p3) DB_ASSERT(!((NULL == (p1)) || (NULL == (p2)) || (NULL == (p3))))
#define DB_POINTER4(p1, p2, p3, p4) DB_ASSERT(!((NULL == (p1)) || (NULL == (p2)) || (NULL == (p3)) || (NULL == (p4))))
#define DB_POINTER5(p1, p2, p3, p4, p5) \
    DB_ASSERT(!((NULL == (p1)) || (NULL == (p2)) || (NULL == (p3)) || (NULL == (p4)) || (NULL == (p5))))

#else
#define DB_POINTER(p) ((void)(p))
#define DB_POINTER2(p1, p2) \
    do {                    \
        (void)(p1);         \
        (void)(p2);         \
    } while (0)  // NOLINT
#define DB_POINTER3(p1, p2, p3) \
    do {                        \
        (void)(p1);             \
        (void)(p2);             \
        (void)(p3);             \
    } while (0)  // NOLINT
#define DB_POINTER4(p1, p2, p3, p4) \
    do {                            \
        (void)(p1);                 \
        (void)(p2);                 \
        (void)(p3);                 \
        (void)(p4);                 \
    } while (0)  // NOLINT
#define DB_POINTER5(p1, p2, p3, p4, p5) \
    do {                                \
        (void)(p1);                     \
        (void)(p2);                     \
        (void)(p3);                     \
        (void)(p4);                     \
        (void)(p5);                     \
    } while (0)  // NOLINT
#endif

/* invalid int type define */
#define DB_INVALID_UINT8 0xFF
#define DB_INVALID_UINT16 0xFFFF
#define DB_INVALID_UINT32 0xFFFFFFFF
#define DB_INVALID_UINT64 0xFFFFFFFFFFFFFFFF

#define DB_ENABLE_SHARE_MODE 1

/*
 * 修改service type时，同时需要修改客户端往服务端发的消息头中的serviceId属性
 * 指定当前消息发往哪个service
 */
typedef enum ServiceType {
    DRT_SERVICE_CONN = 0,  // conn service
    DRT_SERVICE_STMT,      // ddl , dcl , dml service
    DRT_SERVICE_EMBEDDED,
    DRT_SERVICE_BUTT,
} ServiceTypeE;

/*
 * 指定当前消息发往DRT_SERVICE_STMT中的哪个entry
 */
typedef enum {
    MODEL_DUMMY,     // for serviceTypes which not support modeType
    MODEL_FASTPATH,  // for fastpath model (include yang)
    MODEL_DATALOG,   // for datalog model(import、data)
    MODEL_PUBLIC,    // for public
    MODEL_YANG,      // for YANG model (validate when/must/leafref/mandatory)
    MODEL_SQL,       // for SQL
    MODEL_TS,        // for ts
#ifdef FEATURE_GQL
    MODEL_GQL,
#endif
    MODEL_STREAM,
    MODEL_BUTT,
} ModelTypeE;

/*
 * 接受数据类型
 */
typedef enum ReceiverType {
    TABLE_RESULT,             // 获取未序列化的结果集receiver类型
    SERIALIZED_TABLE_RESULT,  // 获取序列化的结果集receiver类型
    GQL_BATCH_DML_RESULT,     // get result from batch dml, use in gql service
    STREAM_FORWARD,           // get result from stream calc, use in stream service
} ReceiverTypeE;

typedef enum ScheduleMode {
    SCHEDULE_DIRECTELY = 0,
    SCHEDULE_INVALID,
    SCHEDULE_THREAD_POOL,
    SCHEDULE_BOTTOM
} ScheduleModeE;

typedef enum TaskPriority { TASK_PRIORITY_NORMAL = 0, TASK_PRIORITY_HIGH, TASK_PRIORITY_BUTT } TaskPriorityE;

#define SERVICE_TYPE_NUM ((uint32_t)DRT_SERVICE_BUTT)
#define MODEL_TYPE_NUM ((uint32_t)MODEL_BUTT)

typedef enum DbDeadLoopType { DEAD_LOOP_TRX = 0, DEAD_LOOP_BUTT } DbDeadLoopTypeE;

#define DB_DEAD_LOOP_TYPE_NUM ((uint32_t)DEAD_LOOP_BUTT)

#define DB_EMPTY_STR ((DbBufT){NULL, 0})

typedef struct DbBuf {
    uint8_t *buf;
    uint32_t len;
} DbBufT;

typedef union {
    void *ptr;
    int32_t i32;
    uint32_t u32;
    uint64_t u64;
} DbDataT;

#define CM_IS_BIGENDIUM (*(uint32_t *)("\x01\x02\x03\x04") == (0x01020304))
#define COM_LITTLE_ENDIAN ((uint8_t)0x01)
#define COM_BIG_ENDIAN ((uint8_t)0x02)

#define COM_RESULT_SUCCEED ((uint8_t)0x00)
#define COM_RESULT_CONNECTION_USE_UP ((uint8_t)0x01)
#define COM_RESULT_START_WORKER_FAILED ((uint8_t)0x02)
#define COM_RESULT_ADD_EVENT_FAILED ((uint8_t)0x04)
#define COM_RESULT_CONFIG_FAILED ((uint8_t)0x05)
// result(1Byte) | endian(1Byte) | shmemCtxId(2Bytes)
typedef struct {
    uint8_t result;
    uint8_t endian;
    uint8_t shmemCtxIdLow;
    uint8_t shmemCtxIdHigh;
    uint16_t instanceId;
    uint16_t nd;
} ComInfoT;

#ifdef LATCH_CONFLICT_DEBUG
typedef struct TagLatchStatInfo {
    uint32_t id;
    uint32_t readerAverageWaitCount;
    uint32_t readerConflictCount;
    uint32_t readerTotalWaitCount;
    uint32_t readerMaxWaitCount;

    uint32_t writerAverageWaitCount;
    uint32_t writerConflictCount;
    uint32_t writerTotalWaitCount;
    uint32_t writerMaxWaitCount;
} LatchConflictInfoT;
#endif

#define PLACEHOLDER

#define GET_INSTANCE_ID FIRST_INSTANCE_ID
typedef union {
    uint32_t u32[4];
    uint64_t u64[2];
#if defined(__x86_64__) || defined(__aarch64__)
    unsigned __int128 u128;
#endif
} DbUint128;

typedef struct DbCred {
    uint32_t pid;
    uint32_t uid;
    uint32_t gid;
    uint32_t reserved;
} DbCredT;

typedef struct CallBackStruct {
    void (*callBack)(void *);  // callBack函数
    void *parameter;           // callBack函数入参
} CallBackStructT;

typedef struct {
    const char *file;
    int32_t line;
    const char *func;
} DbCallSiteT;

typedef struct BatchRange {
    // 正向时startPos<=endPos，反向时startPos>=endPos
    int32_t startPos;
    int32_t endPos;
    bool direction;  // true:正向 false：反向
} BatchRangeT;

inline static void DbCpuRelax(uint32_t n)
{
#if !defined(NDEBUG) && defined(ARM32)
    for (uint32_t i = 0; i < n; ++i) {
        __asm__ __volatile__("yield" ::: "memory");
    }
#elif defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    sched_yield();
#elif defined(__arm__) || defined(__aarch64__)
    for (uint32_t i = 0; i < n; ++i) {
        __asm__ __volatile__("yield" ::: "memory");
    }
#else  // assume to be x86-64
    for (uint32_t i = 0; i < n; ++i) {
        __asm__ __volatile__("pause" : : : "memory");
    }
#endif
}

#define DB_DOUBLE(x) ((x) << 1)
#define DB_HALF_OF(x) ((x) / 2)
#define DB_QUARTER_OF(x) ((x) / 4)
#define COMPONENT_SO_MAX_NUM 32  // 组件化so数量，当前场景下，32足够

ADPTR_EXPORT void DbIdleLoop(uint32_t n);
ADPTR_EXPORT bool DbIsHpeEnv(void);
ADPTR_EXPORT bool DbIsEulerEnv(void);
ADPTR_EXPORT bool DbIsHpeSimulationEnv(void);
ADPTR_EXPORT AdptEnvironmentE DbCommonGetEnv(void);
ADPTR_EXPORT bool DbCommonIsSingleCltProcess(void);
ADPTR_EXPORT bool DbCommonIsCltThreadInServProcess(void);
ADPTR_EXPORT bool DbSetServerSameProcessStartFlag(void);
ADPTR_EXPORT void DbClearServerSameProcessStartFlag(void);
ADPTR_EXPORT uint32_t DbGetServerSameProcessStartFlag(void);
ADPTR_EXPORT void DbSetServerThreadFlag(void);
ADPTR_EXPORT void DbClearServerThreadFlag(void);
ADPTR_EXPORT void DbCommonSetEnv(AdptEnvironmentE env);
ADPTR_EXPORT bool DbCommonIsServer(void);
ADPTR_EXPORT bool DbAptStrError(char *str, int32_t len);
ADPTR_EXPORT int32_t DbAptGetErrno(void);
ADPTR_EXPORT void DbSetIsServerFlag(void);
#ifdef FEATURE_SERVER_FUNC_REG
ADPTR_EXPORT void DbDisableRegAdaptFuncs(void);
ADPTR_EXPORT void DbEnableRegAdaptFuncs(void);
ADPTR_EXPORT bool DbIsAdaptFuncsReg(void);
#if defined(FEATURE_SERVER_FUNC_REG) && !defined(FEATURE_CLT_SERVER_SAME_PROCESS)
ADPTR_EXPORT int32_t DbRegAdaptFuncs(void);
#endif
#endif
// 用户回调函数名长度最值
#define DB_USER_CALLBACK_FUNC_NAME_LEN_MAX 256  // 需要大于等于订阅关系名长度最值 DM_MAX_NAME_LENGTH

ADPTR_EXPORT void DbSetTcpConnectConfig(bool isTcpConnet);
ADPTR_EXPORT bool DbGetTcpConnectConfig(void);

#define MEMBER_PTR5(p1, member) ((typeof((p1)->member))(void *)(((uint8_t *)(p1)) + (p1)->member##Offset))

#define MEMBER_PTR4(p1, member) (((p1)->member##Offset == 0) ? (p1)->member : MEMBER_PTR5(p1, member))

#define MEMBER_2(p1, offset, m) MEMBER_PTR5(p1, offset)->m

#define MEMBER_3(p1, offset1, offset2, m) MEMBER_PTR5((MEMBER_PTR5(p1, offset1)), offset2)->m

#define MEMBER_PTR_IMPL(p1, member) (((p1)->member##Offset == 0) ? (p1)->member : MEMBER_PTR5(p1, member))

#if defined(EXPERIMENTAL_NERGC)
#define MEMBER_PTR(p1, member) ({ DbGetTcpConnectConfig() ? (p1)->member : MEMBER_PTR_IMPL(p1, member); })
#else
#define MEMBER_PTR(p1, member) MEMBER_PTR_IMPL(p1, member)
#endif

#define CONST_MEMBER_PTR_IN(p1, member) (((const uint8_t *)(p1)) + (p1)->member##Offset)
#define CONST_MEMBER_PTR(p1, member) (((p1)->member##Offset == 0) ? NULL : CONST_MEMBER_PTR_IN(p1, member))

#define MEMBER_PTR_NO_CHECK(p1, member) MEMBER_PTR5(p1, member)

#ifdef __cplusplus
}
#endif

#endif  // ADPT_DEFINE_H
