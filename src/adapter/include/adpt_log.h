/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: file content
 * Author: gaohaiyang
 * Create: 2021-09-13
 */
#ifndef ADPT_LOG_H
#define ADPT_LOG_H
// 日志模块内部加锁代码统一使用不打印超时日志的锁，后缀为NoTimeoutLog，否则在特殊场景下可能导致栈溢出问题
#include "adpt_log_base.h"
#include "adpt_log_haotian.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define LOG_ERROR_FORMAT "GMERR-%" PRId32 ", "
#define LOG_TRUN_SUFFIX_LENGTH 3
#define LOG_TRUN_SUFFIX '.'

// Minimize size by removing funciton names from logs
#define __DB_FUNC_NAME__ " "

// 时间戳（有cpu cycle数）
#define LOG_TIMESTAMP_FORMAT                                                           \
    "%04" PRId32 "-%02" PRId32 "-%02" PRId32 " %02" PRId32 ":%02" PRId32 ":%02" PRId32 \
    " %s, "  // year-month-date hour:min:sec timeZone

#define LOG_TIMESTAMP_WITH_MILLS_FORMAT                                                              \
    "%04" PRId32 "-%02" PRId32 "-%02" PRId32 " %02" PRId32 ":%02" PRId32 ":%02" PRId32 ".%03" PRId32 \
    " %s, "  // year-month-date hour:min:sec.msec timeZone

// 日志位置信息格式
#define LOG_FILE_LOC_FORMAT " %s:%" PRIu32 ", %s\n"  // fileName:lineNum, funcName
#define DB_DEFAULT_LOG_SIZE 1024                     // cycle, threadMsg, module, location, errorCode
#define DB_COMMON_LOG_HEADER_SIZE 64                 // timestamp, tid, level

#ifndef NDEBUG
#define DB_BOOT_LOG_FILE_PATH "/opt/vrpv8/home/<USER>"  // 启动日志文件路径
#endif                                                      /* NDEBUG */

inline static void DbLogTextFinishPrint(LogTextStrT *logText)
{
    DB_POINTER(logText);
    DB_ASSERT(logText->pos < logText->bufLen);
    if (SECUREC_LIKELY(logText->pos < logText->bufLen - 1)) {
        logText->buf[logText->pos] = '\n';
        logText->buf[++logText->pos] = 0;
    } else {
        logText->buf[logText->pos - 1] = '\n';
        logText->buf[logText->pos] = 0;
    }
}

inline static void DbLogTextCorrection(LogTextStrT *logText, uint32_t writeCnt)
{
    DB_POINTER(logText);
    char specialChar[] = {'\r', '\n', '\a', '\b', '\f', '\v'};
    for (uint32_t i = 0; i < writeCnt; i++) {
        for (uint32_t j = 0; j < ELEMENT_COUNT(specialChar); j++) {
            if (logText->buf[i + logText->pos] == specialChar[j]) {
                logText->buf[i + logText->pos] = '/';
                break;
            }
        }
    }
}

inline static void DbLogTextTrun(LogTextStrT *logText)
{
    DB_POINTER(logText);
    if (logText->pos >= (logText->bufLen - LOG_TRUN_SUFFIX_LENGTH - 1)) {
        for (uint32_t i = 0; i < LOG_TRUN_SUFFIX_LENGTH; i++) {
            logText->buf[i + logText->bufLen - LOG_TRUN_SUFFIX_LENGTH - 1] = LOG_TRUN_SUFFIX;
        }
    }
}

inline static Status DbLogTextVprintf(LogTextStrT *logText, const char *fmt, va_list *va)
{
    DB_POINTER3(logText, fmt, va);
    DB_ASSERT(logText->bufLen >= logText->pos);
    int32_t writeCnt = vsnprintf_truncated_s(logText->buf + logText->pos, logText->bufLen - logText->pos, fmt, *va);
    if (SECUREC_UNLIKELY(writeCnt < 0)) {
        return GMERR_FIELD_OVERFLOW;
    }
    DbLogTextCorrection(logText, (uint32_t)writeCnt);
    logText->pos += (uint32_t)writeCnt;
    DbLogTextTrun(logText);
    return GMERR_OK;
}

inline static Status DbLogTextPrintf(LogTextStrT *logText, const char *fmt, ...)
{
    DB_POINTER2(logText, fmt);
    va_list args;
    va_start(args, fmt);
    Status ret = DbLogTextVprintf(logText, fmt, &args);
    va_end(args);
    return ret;
}

/*
 * description: 构造日志头
 * param {LogTextStrT} *logText 入参，日志内容dst字符串
 * param {const DbLogHeaderT} *header 入参，日志头信息结构体
 * param {const DbLogLocationT} *loc 入参，日志位置信息结构体
 * return {*} 构造成功返回 GMERR_OK，否则返回失败
 */
ADPTR_EXPORT Status DbLogAddHeader(LogTextStrT *logText, const DbLogHeaderT *header, const DbLogLocationT *loc);

/*
 * description: 构造时间戳，使用场景：审计日志和启动日志
 */
ADPTR_EXPORT Status DbLogAddOsTimestamp(LogTextStrT *logText);
ADPTR_EXPORT Status DbLogAddTimestamp(LogTextStrT *logText, uint64_t nowCycle);

// 审计日志和调测日志写接口
ADPTR_EXPORT void DbLogAdptWrite(const DbLogWriteArgT *arg, const LogTextStrT *logText);

ADPTR_EXPORT void DbLogRegAdptFuncs(const GmcLogAdptFuncsT *userDefLogFuncs);
ADPTR_EXPORT const GmcLogAdptFuncsT *DbLogGetAdptFuncs(void);
ADPTR_EXPORT extern DbSpinLockT g_gmdbLogAdapterFuncsLock;

ADPTR_EXPORT Status DbAdptOrmLogInit(void);
ADPTR_EXPORT void *DbAdptLogGetHandle(void);
ADPTR_EXPORT void *DbAdptLogGetWriteFunc(void);
ADPTR_EXPORT void DbAdptLogWriteToJournal(uint32_t errCodeNum, const char *logBuf);

// 获取erroCode对应的brief错误码简述信息
typedef const char *(*GetErrCodeDescT)(Status errCode);

ADPTR_EXPORT void DbAdptLogRegLogApi(const DbAdptLogApiT *logApis);
ADPTR_EXPORT void DbAdptRegitstGetErrCodeDescApi(GetErrCodeDescT func);
ADPTR_EXPORT void DbAdptLogSetFileCfg(const DbLogFileCfgT *fileCfg);
ADPTR_EXPORT DbLogFileCfgT DbAdptLogGetFileCfg(void);
ADPTR_EXPORT void DbLogBuildLocation(const DbLogLocationT *loc, char *dst, uint32_t len);

#define INSTANCE_TYPE_LITE_DB 0  // instance type：0 LiteDb 1：yangDb
#define FAULT_ID_0 0             // faultId：only support 0
/**
 * @ingroup DbLog
 * @brief 提供主动复位功能。
 *
 * @par 描述：
 * 根据业务场景，通过此接口主动复位单板。
 * @attention
 * @param instanceType [IN] 支持的GMDB模式，0是litedb，1是yangdb。
 * @param faultId [IN] 故障场景ID，目前只支持0。
 * @param format [IN] 故障场景下记录的详细日志信息。
 */
ADPTR_EXPORT void DbSilentFaultReport(uint32_t instanceType, uint32_t faultId, char *format, ...)
    __attribute__((format(printf, 3, 4), nonnull(3)));

typedef struct LstErrTxt {  // 此处新定义的结构体实际上和TextT无差别，目的是为了不依赖common目录
    uint32_t len;
    char *str;
} LstErrTxtT;

#define LOG_MAX_SIZE_OF_LOG_MSG 1024  // 每条日志消息最多包含1024个字符
extern ADPTR_EXPORT DB_THREAD_LOCAL LstErrTxtT g_gmdbLastError;
extern ADPTR_EXPORT DB_THREAD_LOCAL char g_gmdbLastErrorStr[LOG_MAX_SIZE_OF_LOG_MSG];

ADPTR_EXPORT void DbAdptWriteLastErrorCustom(int32_t errCode, const char *formatStr, ...)
    __attribute__((format(printf, 2, 3), nonnull(2)));
ADPTR_EXPORT void DbAdptLogWriteAndSetLastError(bool needFold, DbLogTypeE type, const char *module, uint32_t level,
    int32_t errCode, const char *fileName, uint32_t lineNum, const char *funcName, const char *formatStr, ...);

ADPTR_EXPORT void DbAdptLogWrite(bool needFold, DbLogTypeE type, const char *module, uint32_t level, int32_t errCode,
    const char *fileName, uint32_t lineNum, const char *funcName, const char *formatStr, ...);
ADPTR_EXPORT void DbAdptRunFoldErrLogWrite(const char *module, int32_t errCode, const char *fileName, uint32_t lineNum,
    const char *funcName, const char *formatStr, ...);
#define LOG_NO_ERRORCODE 0  // INFO级别的日志不用错误码

inline static void DbNoFmtLogUnused(int32_t eCode, ...)
{}

inline static void DbCustomLogUnused(const char *fmt, ...)
{}

ADPTR_EXPORT void DbAdptLogLevelSet(uint32_t level, uint32_t durationSec);

/*
 * description: 不同环境下的启动日志写接口
 * param {const DbLogWriteArgT} *arg 日志透传参数
 * param {const LogTextStrT} *logText 启动日志字符串
 * return {*} 无，日志写失败也不抛错
 */
void DbAdptWriteBootLog(const DbLogWriteArgT *arg, const LogTextStrT *logText);

/*
 * description: 启动日志写接口，不感知环境差异，内部调用 DbAdptWriteBootLog
 */
ADPTR_EXPORT void DbWriteBootLogWithArgs(int32_t errCode, uint32_t level, const char *filePath, uint32_t lineNum,
    const char *formatStr, va_list *args, bool isFormat);

/*
 * description: 启动日志写接口，不感知环境差异，内部调用 DbAdptWriteBootLog
 */
ADPTR_EXPORT void DbWriteBootLog(int32_t errCode, uint32_t level, const char *filePath, uint32_t lineNum,
    const char *formatStr, ...) __attribute__((format(printf, 5, 6)));

/*
 * description: 启动日志初始化，不同环境执行不同函数，当前除hpe，其他环境均是空函数
 */
Status DbAdptInitBootLog(void);

/*
 * description: 信号处理函数中，打印panic信息并持久化到高端内存的接口（仅HPE下支持）
 * param {int signo}: 信号
 * return {*} 无，日志写失败也不抛错
 */
ADPTR_EXPORT void DbReportUserException(int signo);

/*
 * description: 启动日志初始化，使用场景：仅在DB启动流程开始时调用；无内存相关资源申请，因此调用后，异常分支不用
 * DbBootLogUninit，内部调用 DbAdptInitBootLog
 */
ADPTR_EXPORT Status DbBootLogInit(void);

ADPTR_EXPORT bool DbLogIsBootState(void);

/*
 * description: DB打屏接口
 */
ADPTR_EXPORT void DbPrintToStdout(const char *format, ...) __attribute__((format(printf, 1, 2)));

/*
 * description: 启动日志去初始化，使用场景：仅在DB启动流程结束时调用
 */
ADPTR_EXPORT void DbBootLogUninit(void);

ADPTR_EXPORT void DbLogCurrentStackTrace(void);

// 将堆栈记录为debug/info信息，使用场景：仅用于debug
ADPTR_EXPORT void DbLogCurrentStackTraceDebug(void);
ADPTR_EXPORT const char *DbAdptGetErrcodeDesc(Status errCode);

#ifndef NDEBUG
// debug 模式下打印启动阶段日志到私有文件
ADPTR_EXPORT void DbWriteSimpleBootLog(LogPreFixTypeE prefix, const char *logStr);
#endif /* NDEBUG */

// 日志折叠相关日志宏
#ifndef NDEBUG
// 仅在debug模式下生效的日志宏
#define DB_LOG_DBG_INFO(format, ...)                                                                                   \
    DbAdptLogWrite(true, DB_LOG_TYPE_DEBUG, "ADAPTER", DB_LOG_LVL_INFO, (int32_t)LOG_NO_ERRORCODE, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_DBG_DEBUG(format, ...)                                                                                 \
    DbAdptLogWrite(true, DB_LOG_TYPE_DEBUG, "ADAPTER", DB_LOG_LVL_DBG, (int32_t)LOG_NO_ERRORCODE, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_DBG_WARN(format, ...)                                                                                   \
    DbAdptLogWrite(true, DB_LOG_TYPE_DEBUG, "ADAPTER", DB_LOG_LVL_WARN, (int32_t)LOG_NO_ERRORCODE, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_DBG_ERROR(errCode, format, ...)                                                               \
    DbAdptLogWrite(true, DB_LOG_TYPE_DEBUG, "ADAPTER", DB_LOG_LVL_ERR, (int32_t)errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)

#define DB_BOOT_LOG_DBG_DEBUG(format, ...) \
    DbWriteBootLog(LOG_NO_ERRORCODE, DB_LOG_LVL_DBG, __FILE__, __LINE__, format, ##__VA_ARGS__)
#else
#define DB_LOG_DBG_INFO(format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define DB_LOG_DBG_DEBUG(format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define DB_LOG_DBG_WARN(format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define DB_LOG_DBG_ERROR(errCode, format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#define DB_BOOT_LOG_DBG_DEBUG(format, ...) DbCustomLogUnused(format, ##__VA_ARGS__)
#endif

#define DB_LOG_DEBUG(format, ...)                                                                                   \
    DbAdptLogWrite(true, DB_LOG_TYPE_RUN, "ADAPTER", DB_LOG_LVL_DBG, (int32_t)LOG_NO_ERRORCODE, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_INFO(format, ...)                                                                                     \
    DbAdptLogWrite(true, DB_LOG_TYPE_RUN, "ADAPTER", DB_LOG_LVL_INFO, (int32_t)LOG_NO_ERRORCODE, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_WARN(errCode, format, ...)                                                                   \
    DbAdptLogWrite(true, DB_LOG_TYPE_RUN, "ADAPTER", DB_LOG_LVL_WARN, (int32_t)errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_ERROR(errCode, format, ...) \
    DbAdptRunFoldErrLogWrite("ADAPTER", (int32_t)errCode, __FILE__, __LINE__, __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_EMRG(errCode, format, ...)                                                                   \
    DbAdptLogWrite(true, DB_LOG_TYPE_RUN, "ADAPTER", DB_LOG_LVL_EMRG, (int32_t)errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)

// 关闭日志折叠相关日志宏
#define DB_LOG_INFO_UNFOLD(format, ...)                                                                               \
    DbAdptLogWrite(false, DB_LOG_TYPE_RUN, "ADAPTER", DB_LOG_LVL_INFO, (int32_t)LOG_NO_ERRORCODE, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_WARN_UNFOLD(errCode, format, ...)                                                             \
    DbAdptLogWrite(false, DB_LOG_TYPE_RUN, "ADAPTER", DB_LOG_LVL_WARN, (int32_t)errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_ERROR_UNFOLD(errCode, format, ...)                                                           \
    DbAdptLogWrite(false, DB_LOG_TYPE_RUN, "ADAPTER", DB_LOG_LVL_ERR, (int32_t)errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)
#define DB_LOG_ERROR_SIGNAL_UNFOLD(errCode, format, ...)                                                       \
    DbAdptLogWrite(false, DB_LOG_TYPE_SIGNAL, "ADAPTER", DB_LOG_LVL_ERR, (int32_t)errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, format, ##__VA_ARGS__)

// 只记录last error相关日志宏
#define DB_SET_LASTERR(errCode, errDesc, ...) DbAdptWriteLastErrorCustom(errCode, errDesc, ##__VA_ARGS__)

// 记录并打印日志last error相关日志宏
#define DB_LOG_INFO_AND_SET_LASTERR(errCode, errDesc, ...)                                                        \
    DbAdptLogWriteAndSetLastError(true, DB_LOG_TYPE_RUN, "ADAPTER", DB_LOG_LVL_INFO, errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, errDesc, ##__VA_ARGS__)
#define DB_LOG_WARN_AND_SET_LASTERR(errCode, errDesc, ...)                                                        \
    DbAdptLogWriteAndSetLastError(true, DB_LOG_TYPE_RUN, "ADAPTER", DB_LOG_LVL_WARN, errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, errDesc, ##__VA_ARGS__)
#define DB_LOG_ERROR_AND_SET_LASTERR(errCode, errDesc, ...)                                                      \
    DbAdptLogWriteAndSetLastError(true, DB_LOG_TYPE_RUN, "ADAPTER", DB_LOG_LVL_ERR, errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, errDesc, ##__VA_ARGS__)
#define DB_LOG_EMRG_AND_SET_LASTERR(errCode, errDesc, ...)                                                        \
    DbAdptLogWriteAndSetLastError(true, DB_LOG_TYPE_RUN, "ADAPTER", DB_LOG_LVL_EMRG, errCode, __FILE__, __LINE__, \
        __DB_FUNC_NAME__, errDesc, ##__VA_ARGS__)

// 启动阶段日志
#define DB_BOOT_LOG_INFO(format, ...) \
    DbWriteBootLog(LOG_NO_ERRORCODE, DB_LOG_LVL_INFO, __FILE__, __LINE__, format, ##__VA_ARGS__)
#define DB_BOOT_LOG_DEBUG(format, ...) \
    DbWriteBootLog(LOG_NO_ERRORCODE, DB_LOG_LVL_DBG, __FILE__, __LINE__, true, format, ##__VA_ARGS__)
#define DB_BOOT_LOG_WARN(errCode, format, ...) \
    DbWriteBootLog(errCode, DB_LOG_LVL_WARN, __FILE__, __LINE__, format, ##__VA_ARGS__)
#define DB_BOOT_LOG_ERROR(errCode, format, ...) \
    DbWriteBootLog(errCode, DB_LOG_LVL_ERR, __FILE__, __LINE__, format, ##__VA_ARGS__)

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // ADPT_LOG_H
