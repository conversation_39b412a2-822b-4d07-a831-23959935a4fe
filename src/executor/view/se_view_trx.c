/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: se_view_trx.c
 * Description: sysview file for storage trx view
 * Author:
 * Create: 2021-05-24
 */

#include "se_view_trx.h"
#include "se_trx.h"
#include "sysview_instance.h"
#include "se_view_base.h"
#include "db_timer.h"
#include "ee_stmt.h"
#include "ee_session.h"
#include "se_view_label.h"
#include "ee_log.h"  // 放在最后一行

#ifdef __cplusplus
extern "C" {
#endif

#define LOCK_STAT_VIEW_NODE_DEPTH 2

typedef struct LockModeStat {
    uint32_t sum[(int32_t)SE_LOCK_MODE_MAX];
} LockModeStatT;

// STORAGE_TRX_DETAIL视图信息
Status FillDmVertexByAcqLockArrayInner(const DmNodeT *vertexNode, const TrxDetailT *trxDetail, uint32_t id)
{
    DmValueT propertyValue = {0};
    propertyValue.type = DB_DATATYPE_STRING;
    char lockModeString[][4] = {"IS", "IX", "S", "X", "MAX"};
    propertyValue.value.strAddr = lockModeString[trxDetail->lockAcqStat[id].lockMode];
    propertyValue.value.length = (uint32_t)(strlen(lockModeString[trxDetail->lockAcqStat[id].lockMode]) + 1);
    Status ret = DmNodeSetPropeByName("LOCK_MODE", propertyValue, vertexNode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    char lockTypeString[][8] = {"LABEL", "PAGE", "TUPLE", "NO_USED"};
    propertyValue.value.strAddr = lockTypeString[trxDetail->lockAcqStat[id].lockType];
    propertyValue.value.length = (uint32_t)(strlen(lockTypeString[trxDetail->lockAcqStat[id].lockType]) + 1);
    ret = DmNodeSetPropeByName("LOCK_TYPE", propertyValue, vertexNode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    char lockStateString[][8] = {"NO_LOCK", "IS", "IX", "S", "X", "AI", "ISS", "MAX"};
    propertyValue.value.strAddr = lockStateString[trxDetail->lockAcqStat[id].lockState];
    propertyValue.value.length = (uint32_t)(strlen(lockStateString[trxDetail->lockAcqStat[id].lockState]) + 1);
    return DmNodeSetPropeByName("LOCK_STATE", propertyValue, vertexNode);
}

Status FillEachLockAcqVector(const DmNodeT *vertexNode, const TrxDetailT *trxDetail, uint32_t id)
{
    Status ret = SvQueryGetParaUint32ForNode(vertexNode, trxDetail->lockAcqStat[id].lockAcqId, "LOCK_ACQ_ID");
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32ForNode(vertexNode, trxDetail->lockAcqStat[id].holdNum, "HOLD_NUM") :
                              ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32ForNode(vertexNode, trxDetail->lockAcqStat[id].waitNum, "WAIT_NUM") :
                              ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32ForNode(vertexNode, trxDetail->lockAcqStat[id].labelId, "LABEL_ID") :
                              ret;
    ret =
        (ret == GMERR_OK) ? SvQueryGetParaUint32ForNode(vertexNode, trxDetail->lockAcqStat[id].pageId, "PAGE_ID") : ret;
    ret =
        (ret == GMERR_OK) ? SvQueryGetParaUint32ForNode(vertexNode, trxDetail->lockAcqStat[id].slotId, "SLOT_ID") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint16ForNode(vertexNode, trxDetail->lockAcqStat[id].dbId, "DB_ID") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint16ForNode(
                                  vertexNode, trxDetail->lockAcqStat[id].waitForUpdX, "WAIT_FOR_UPD_XLOCK_CNT") :
                              ret;
    ret = (ret == GMERR_OK) ?
              SvQueryGetParaUint16ForNode(vertexNode, trxDetail->lockAcqStat[id].shareUpdX, "SHARE_UPD_XLOCK_CNT") :
              ret;
    ret = (ret == GMERR_OK) ? FillDmVertexByAcqLockArrayInner(vertexNode, trxDetail, id) : ret;
    return ret;
}

Status FillDmVertexByHoldAcqLock(DmVertexT *vertex, const TrxDetailT *trxDetail, LockModeStatT *lockNumStat)
{
    DmNodeT *vertexNodeAll = NULL;
    DmNodeT *vertexNodeInfo = NULL;
    Status ret = DmVertexGetNodeByName(vertex, "HOLD_LOCK_ALL_INFO", &vertexNodeAll);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get node: HOLD_LOCK_ALL_INFO");
        return ret;
    }

    // 超过1024个的数据，使用vector嵌套vector的方法
    uint32_t lockCnt = 0;
    uint32_t childNum = (trxDetail->holdLockNum + MAX_VECTOR_SIZE - 1) / MAX_VECTOR_SIZE;
    for (uint32_t j = 0; j < childNum; j++) {
        ret = DmNodeVectorAppend(vertexNodeAll);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "append node: HOLD_LOCK_ALL_INFO");
            return ret;
        }
        ret = DmNodeGetChildNodeByName(vertexNodeAll, "HOLD_LOCK_INFO", &vertexNodeInfo);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get node: HOLD_LOCK_INFO");
            return ret;
        }
        uint32_t idxCnt = DB_MIN(MAX_VECTOR_SIZE, trxDetail->holdLockNum - lockCnt);
        for (uint32_t id = lockCnt; id < lockCnt + idxCnt; id++) {
            ret = DmNodeVectorAppend(vertexNodeInfo);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_ERROR(ret, "append node: HOLD_LOCK_INFO");
                return ret;
            }

            ret = FillEachLockAcqVector(vertexNodeInfo, trxDetail, id);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
            if (lockNumStat != NULL && trxDetail->lockAcqStat[id].lockMode < (uint16_t)SE_LOCK_MODE_MAX) {
                lockNumStat->sum[trxDetail->lockAcqStat[id].lockMode]++;
            }
        }
        lockCnt += idxCnt;
    }
    return GMERR_OK;
}

Status FillDmVertexByNotifyLock(DmVertexT *vertex, const TrxDetailT *trxDetail)
{
    DmNodeT *vertexNode = NULL;
    DmValueT propertyValue = {0};
    if (trxDetail->waitLockNotifyId == INVALID_VALUE32) {
        return GMERR_OK;
    }
    Status ret = DmVertexGetNodeByName(vertex, "WAIT_LOCK_INFO", &vertexNode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "get node: WAIT_LOCK_INFO");
        return ret;
    }
    ret = SvQueryGetParaUint32ForNode(vertexNode, trxDetail->lockNotifyStat.lockAcqId, "LOCK_ACQ_ID");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = SvQueryGetParaUint32ForNode(vertexNode, trxDetail->lockNotifyStat.labelId, "LABEL_ID");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = SvQueryGetParaUint32ForNode(vertexNode, trxDetail->lockNotifyStat.pageId, "PAGE_ID");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = SvQueryGetParaUint32ForNode(vertexNode, trxDetail->lockNotifyStat.slotId, "SLOT_ID");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = SvQueryGetParaUint32ForNode(vertexNode, trxDetail->lockNotifyStat.holdNum, "HOLD_NUM");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = SvQueryGetParaUint16ForNode(vertexNode, trxDetail->lockNotifyStat.dbId, "DB_ID");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    propertyValue.type = DB_DATATYPE_STRING;
    char lockModeString[][4] = {"IS", "IX", "S", "X", "MAX"};
    propertyValue.value.strAddr = lockModeString[trxDetail->lockNotifyStat.lockMode];
    propertyValue.value.length = (uint32_t)(strlen(lockModeString[trxDetail->lockNotifyStat.lockMode]) + 1);
    return DmNodeSetPropeByName("LOCK_MODE", propertyValue, vertexNode);
}

// STORAGE_LOCK_OVERVIEW视图信息
Status FillDmVertexByLockOverviewPartial(DmVertexT *vertex, const SeLockOverviewStatT *lockStat)
{
    Status ret = SvQueryGetParaUint32(vertex, lockStat->poolLockCnt, "POOL_LOCK_CNT");
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, lockStat->usedPoolLockCnt, "USED_POOL_LOCK_CNT") : ret;
    ret =
        (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, lockStat->usedPoolLockCntMax, "USED_POOL_LOCK_CNT_MAX") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, lockStat->freePoolLockCnt, "FREE_POOL_LOCK_CNT") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, lockStat->reserveLockCnt, "RESERVE_LOCK_CNT") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, lockStat->usedReserveLockCnt, "USED_RESERVE_LOCK_CNT") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, lockStat->freeReserveLockCnt, "FREE_RESERVE_LOCK_CNT") : ret;
    return ret;
}

Status FillDmVertexByLockOverview(DmVertexT *vertex, const SeLockOverviewStatT *lockStat)
{
    Status ret = SvQueryGetParaUint32(vertex, lockStat->lockCnt, "LOCK_CNT");
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, lockStat->usedLockCnt, "USED_LOCK_CNT") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, lockStat->freeLockCnt, "FREE_LOCK_CNT") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, lockStat->bucketLockCnt, "BUCKET_LOCK_CNT") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, lockStat->usedBucketLockCnt, "USED_BUCKET_LOCK_CNT") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, lockStat->freeBucketLockCnt, "FREE_BUCKET_LOCK_CNT") : ret;
    ret = (ret == GMERR_OK) ? FillDmVertexByLockOverviewPartial(vertex, lockStat) : ret;
    return ret;
}

// 查询事务锁的整体使用情况
Status SvQueryProcStorageEngineLockOverview(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    if (*(cursor->rowId) > 0) {
        return GMERR_NO_DATA;
    }
    if (cursor->stmt == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "stmt of cursor is null.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    SeLockOverviewStatT lockStat = {0};
    SeGetLockOverView(QryGetSeInstance(cursor->stmt), &lockStat);

    Status ret = FillDmVertexByLockOverview(vertex, &lockStat);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "fill vertex by lock overview");
        return ret;
    }

    (*(cursor->rowId))++;
    return GMERR_OK;
}

Status FillDmVertexByTxStatOptiRetryTrx(DmVertexT *vertex, const TrxStatAllT *trxStat)
{
    Status ret = SvQueryGetParaBoolean(vertex, trxStat->optiRetryTrxOpen, "OPTI_RETRY_TRX_OPEN");
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint8(vertex, trxStat->optiRetryState, "OPTI_RETRY_STATE") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint64(vertex, trxStat->curRetryTrxId, "CUR_RETRY_TRX_ID") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, trxStat->retryTrxQueueLen, "RETRY_TRX_QUEUE_LEN") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, trxStat->commitTrxQueueLen, "COMMIT_TRX_QUEUE_LEN") : ret;
    return ret;
}

Status FillDmVertexByTxStatOverall(DmVertexT *vertex, const TrxStatAllT *trxStat)
{
    Status ret = SvQueryGetParaUint32(vertex, trxStat->trxCnt, "ACT_TRX_NUM");
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint16(vertex, trxStat->trxUsdNum, "TRX_USED_SLOT_NUM") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint64(vertex, trxStat->maxTrxId, "MAX_TRX_ID") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint64(vertex, trxStat->minActTrxId, "MIN_ACT_TRX_ID") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, trxStat->rwTrxICnt, "RW_TRX_CNT") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, trxStat->roTrxICnt, "RO_TRX_CNT") : ret;
    ret = (ret == GMERR_OK) ? FillDmVertexByTxStatOptiRetryTrx(vertex, trxStat) : ret;
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    char timeStr[DB_MAX_NAME_LEN];
    ret = DbGetDurationStr(0, trxStat->maxTrxTimeUse, timeStr, DB_MAX_NAME_LEN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get running time string.");
        return ret;
    }

    return DmVertexSetStrPropeByName(vertex, "MAX_TRX_TIME_USED", timeStr);
}

// 事务池DFX视图的总览查询接口
Status SvQueryProcStorageEngineTxStatOverall(void *vertex, SvCursorT *cursor)
{
    DB_POINTER3(vertex, cursor, cursor->stmt);
    uint16_t *instance = (uint16_t *)cursor->rowId;
    while (*instance <= MAX_INSTANCE_ID) {
        // 事务池只有一个实例，此处DbGetProcGlobalId()用于判断实例数
        if (*instance == DbGetProcGlobalId()) {
            break;
        }
        (*instance)++;
    }

    if (*instance > MAX_INSTANCE_ID) {
        return GMERR_NO_DATA;
    }
    TrxStatAllT trxStat = {0};
    QryStmtT *stmt = (QryStmtT *)cursor->stmt;
    TrxGetTrxStatAll(&trxStat, stmt->session->seInstance);

    Status ret = FillDmVertexByTxStatOverall(vertex, &trxStat);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    (*instance)++;
    return GMERR_OK;
}

Status FillDmVertexByTrxStatSingle(DmVertexT *vertex, const TrxDetailT *trxDetail)
{
    DB_POINTER2(vertex, trxDetail);
    Status ret = SvQueryGetParaUint64(vertex, trxDetail->trxId, "TRX_ID");
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint16(vertex, trxDetail->connId, "CONNECTION_ID") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint16(vertex, trxDetail->trxSlot, "TRX_SLOT") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint16(vertex, trxDetail->trxState, "TRX_STATE") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, trxDetail->holdLockNum, "HOLD_LOCK_NUM") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, trxDetail->isolationLevel, "ISOLATION_LEVEL") : ret;
    ret = (ret == GMERR_OK) ?
              SvQueryGetParaUint32(vertex, trxDetail->retainedUndoRecCnt, "RETAINED_UNDO_RECORD_COUNT") :
              ret;
    ret =
        (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, trxDetail->normalUndoRecCnt, "NORMAL_UNDO_RECORD_COUNT") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, trxDetail->holdLockAcqId, "HOLD_LOCK_ACQ_ID") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, trxDetail->waitLockNotifyId, "LOCK_NOTIFY_ID") : ret;
    return ret;
}

Status SvQueryProcHeapStatLockInfoPartOneHolder(const DmNodeT *vertexNode, const LockConflictStatT *lockConflictStat)
{
    Status ret = SvQueryGetParaUint64ForNode(vertexNode, lockConflictStat->trxLockAcquireCount, "ACQUIRE_COUNT");
    ret = (ret == GMERR_OK) ?
              SvQueryGetParaUint64ForNode(vertexNode, lockConflictStat->conflictCount, "CONFLICT_COUNT") :
              ret;
    ret = (ret == GMERR_OK) ?
              SvQueryGetParaUint64ForNode(vertexNode, lockConflictStat->lockEscalationCount, "ESCALATION_COUNT") :
              ret;
    ret = (ret == GMERR_OK) ?
              SvQueryGetParaUint64ForNode(vertexNode, lockConflictStat->lockEscalationFail, "ESCALATION_FAIL") :
              ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint64ForNode(vertexNode, lockConflictStat->timeout, "TIMEOUT") : ret;
    ret = (ret == GMERR_OK) ?
              SvQueryGetParaUint64ForNode(vertexNode, lockConflictStat->deadlockRollback, "DEADLOCK_ROLLBACK") :
              ret;
    return ret;
}

Status SvQueryProcHeapStatLockInfoPartTwoHolder(const DmNodeT *vertexNode, const LockConflictStatT *lockConflictStat)
{
    Status ret;
    double doubleValue;
    if (lockConflictStat->trxLockAcquireCount != 0) {
        doubleValue = (double)lockConflictStat->conflictCount / (double)lockConflictStat->trxLockAcquireCount;
    } else {
        doubleValue = 0;
    }
    ret = SvQueryGetParadoubleForNode(vertexNode, doubleValue, "CONFLICT_PERCENTAGE");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (lockConflictStat->lockEscalationCount != 0) {
        doubleValue = (double)lockConflictStat->lockEscalationFail / (double)lockConflictStat->lockEscalationCount;
    } else {
        doubleValue = 0;
    }
    return SvQueryGetParadoubleForNode(vertexNode, doubleValue, "ESCALATION_FAIL_PERCENTAGE");
}

Status SvQueryProcStorageEngineHeapStatLockInfoHolder(
    DmVertexT *viewVertex, const LockStatT *lockStat, int32_t i, int32_t j)
{
    Status ret;
    char *namePath[DB_SCHEMA_MAX_DEPTH];
    char namePath0[DB_MAX_PATH], namePath1[DB_MAX_PATH];
    char *lockStatPrefix[SE_LOCK_TYPE_NUM] = {"LABEL", NULL, "TUPLE"};
    int32_t snLen = snprintf_s(namePath0, DB_MAX_PATH, DB_MAX_PATH - 1, "%s_CONFLICT_INFO", lockStatPrefix[i]);
    if (snLen < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    char *lockStatPostfix[SE_LOCK_TYPE_NUM][SE_LOCK_ACQ_TYPE_NUM] = {
        {"IS", "IX", "S", "X"},
        {NULL, NULL, NULL, NULL},
        {NULL, NULL, "S", "X"},
    };
    snLen = snprintf_s(
        namePath1, DB_MAX_PATH, DB_MAX_PATH - 1, "%s_CONFLICT_INFO_%s_MODE", lockStatPrefix[i], lockStatPostfix[i][j]);
    if (snLen < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    namePath[0] = namePath0;
    namePath[1] = namePath1;

    DmNodeT *vertexNode = NULL;

    ret = DmVertexGetNodeByNamePath(viewVertex, (char **)(namePath), LOCK_STAT_VIEW_NODE_DEPTH, &vertexNode);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get %s node", namePath1);
        return ret;
    }
    ret = SvQueryProcHeapStatLockInfoPartOneHolder(vertexNode, &lockStat->lockConflictStat[i][j]);
    if (ret != GMERR_OK) {
        return ret;
    }

    return SvQueryProcHeapStatLockInfoPartTwoHolder(vertexNode, &lockStat->lockConflictStat[i][j]);
}

Status SvQueryProcStorageEngineHeapStatLockInfo(DmVertexT *viewVertex, const LockStatT *lockStat)
{
    Status ret;
    DmNodeT *vertexNode = NULL;
    char *lockStatPrefix[SE_LOCK_TYPE_NUM] = {"LABEL", NULL, "TUPLE"};
    char *lockStatPostfix[SE_LOCK_TYPE_NUM][SE_LOCK_ACQ_TYPE_NUM] = {
        {"IS", "IX", "S", "X"},
        {NULL, NULL, NULL, NULL},
        {NULL, NULL, "S", "X"},
    };
    for (int32_t i = 0; i < SE_LOCK_TYPE_NUM; i++) {
        if (lockStatPrefix[i] == NULL) {
            continue;
        }
        for (int32_t j = 0; j < SE_LOCK_ACQ_TYPE_NUM; j++) {
            if (lockStatPostfix[i][j] == NULL) {
                continue;
            }
            ret = SvQueryProcStorageEngineHeapStatLockInfoHolder(viewVertex, lockStat, i, j);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
        }
    }
    ret = DmVertexGetNodeByName(viewVertex, "TUPLE_CONFLICT_INFO", &vertexNode);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SvQueryGetParaUint64ForNode(vertexNode, lockStat->tupleLockEscalateToLabelLock, "TUPLE_ESCALATE_TO_LABEL");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return GMERR_OK;
}

Status SvQuerySetLabelNameAndNameSpace(
    DmVertexT *viewVertex, DbSeViewT *seInfo, SvInstanceT *svInstance, HeapRunCtxAllocCfgT *heapRunCtxAllocCfg)
{
    Status ret;
    DmValueT propertyValue = {0};
    propertyValue.type = DB_DATATYPE_STRING;
    uint32_t namespaceId = 0;
    if (seInfo->isKv) {
        DmKvLabelT *kvTable = seInfo->kvTable;
        propertyValue.value.strAddr = kvTable->metaCommon.metaName;
        propertyValue.value.length = DM_STR_LEN(kvTable->metaCommon.metaName);
        heapRunCtxAllocCfg->heapShmAddr = kvTable->heapShmAddr;
        heapRunCtxAllocCfg->dmInfo = kvTable;
        heapRunCtxAllocCfg->isUseRsm = kvTable->metaCommon.isUseRsm;
        namespaceId = kvTable->metaCommon.namespaceId;
    } else {
        DmVertexLabelT *vertexLabel = seInfo->vertexLabel;
        propertyValue.value.strAddr = vertexLabel->metaCommon.metaName;
        propertyValue.value.length = DM_STR_LEN(vertexLabel->metaCommon.metaName);
        heapRunCtxAllocCfg->heapShmAddr = vertexLabel->commonInfo->heapInfo.heapShmAddr;
        heapRunCtxAllocCfg->dmInfo = vertexLabel;
        heapRunCtxAllocCfg->isUseRsm = vertexLabel->metaCommon.isUseRsm;
        namespaceId = vertexLabel->metaCommon.namespaceId;
    }
    ret = DmVertexSetPropeByName("LABEL_NAME", propertyValue, viewVertex);
    if (ret != GMERR_OK) {
        return ret;
    }

    propertyValue.type = DB_DATATYPE_STRING;
    char *namespaceName = NULL;
    ret = CataGetNamespaceNameById((DbMemCtxT *)(void *)svInstance->memCtx, &namespaceName, namespaceId);
    if (ret != GMERR_OK) {
        return ret;
    }

    propertyValue.value.strAddr = namespaceName;
    propertyValue.value.length = (uint32_t)strlen(namespaceName) + 1;
    ret = DmVertexSetPropeByName("NAMESPACE", propertyValue, viewVertex);
    DbDynMemCtxFree(svInstance->memCtx, namespaceName);
    namespaceName = NULL;
    return ret;
}

Status SvQueryProcStorageEngineLockConflictStat(void *viewVertex, SvCursorT *cursor)
{
    DB_POINTER2(viewVertex, cursor);
    DbSeViewT *seInfo = (DbSeViewT *)cursor->rowId;
    Status ret, tmpRet;
    LabelRWLatchT *labelRWLatch = NULL;
    ret = SvQueryGetHeapLabel(seInfo, &labelRWLatch, DbGetInstanceBySvCursor(cursor));
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    SvInstanceT *svInstance = DbSvGetSvInstance(DbGetInstanceBySvCursor(cursor));
    LockStatT *lockStat = DbDynMemCtxAlloc(svInstance->memCtx, sizeof(LockStatT));
    if (lockStat == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        goto EXIT;
    }

    *lockStat = (LockStatT){0};
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {0};
    heapRunCtxAllocCfg.seRunCtx = QryGetSeInstance(cursor->stmt);
    ret = SvQuerySetLabelNameAndNameSpace(viewVertex, seInfo, svInstance, &heapRunCtxAllocCfg);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = HeapLabelGetLockStat(&heapRunCtxAllocCfg, svInstance->memCtx, lockStat);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = SvQueryProcStorageEngineHeapStatLockInfo(viewVertex, lockStat);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

EXIT:
    QryProcReleaseRLabelLatch(labelRWLatch);
    if (lockStat != NULL) {
        DbDynMemCtxFree(svInstance->memCtx, lockStat);
        lockStat = NULL;
    }
    tmpRet = SvQueryDestroyLabel(
        seInfo, DbGetInstanceBySvCursor(cursor));  // 无论ret成功与否，都要显式调用vertexlabel的destroy接口回收所有内存
    return ((ret == GMERR_OK) && (tmpRet != GMERR_OK)) ? tmpRet : ret;
}

// STORAGE_UNDO_MEMORY_USAGE视图信息
static Status DmVertexFillUndoMemoryUsageByName(const UndoStatT *undoStat, DmVertexT *vertex)
{
    DB_POINTER2(undoStat, vertex);
    Status ret = SvQueryGetParaUint64(vertex, undoStat->memUnusedSize, "MEMORY_UNUSED_SIZE");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    return SvQueryGetParaUint32(vertex, undoStat->memUtilizationRatio, "MEMORY_UTILIZATION_RATIO");
}

static Status DmVertexFillUndoMemoryUsageByNameBase(const UndoStatT *undoStat, DmVertexT *vertex, uint16_t labelId)
{
    DB_POINTER2(undoStat, vertex);
    Status ret = SvQueryGetParaUint16(vertex, labelId, "UNDOLOG_ID");
    ret =
        (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, undoStat->rollbackPageNum, "ROLLBACK_SEGMENT_PAGE_NUM") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, undoStat->undologPageNum, "UNDOLOG_PAGE_NUM") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, undoStat->pageSize, "PAGE_SIZE") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint64(vertex, undoStat->insertUndoSize, "TOTAL_INSERT_UNDOLOG_SIZE") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint64(vertex, undoStat->updateUndoSize, "TOTAL_UPDATE_UNDOLOG_SIZE") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint64(vertex, undoStat->deleteUndoSize, "TOTAL_DELETE_UNDOLOG_SIZE") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint64(vertex, undoStat->totalUndoSize, "TOTAL_UNDOLOG_SIZE") : ret;
    ret = (ret == GMERR_OK) ? DmVertexFillUndoMemoryUsageByName(undoStat, vertex) : ret;
    return ret;
}

static Status DmVertexFillUndoLogicUsage(const UndoStatT *undoStat, DmVertexT *vertex)
{
    DB_POINTER2(undoStat, vertex);
    Status ret = SvQueryGetParaUint16(vertex, undoStat->numRsegs, "ROLLBACK_SEGMENT_NUM");
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, undoStat->numUndoSegs, "UNDO_SEGMENT_NUM") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, undoStat->numUndoRecs, "UNDORECORD_NUM") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, undoStat->numRollUndoRecs, "ROLLBACKING_UNDOLOG_NUM") : ret;
    return ret;
}

Status SvQueryProcStorageEngineUndoStat(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    uint16_t *instanId = (uint16_t *)cursor->rowId;
    DbInstanceHdT dbInstance = DbGetInstanceBySvCursor(cursor);
    if (*instanId > DbGetInstanceId(dbInstance)) {
        return GMERR_NO_DATA;
    }
    *instanId = DbGetInstanceId(dbInstance);
    UndoStatT undoStat = {0};
    Status ret = UndoLogGetMemStat(QryGetSeInstance(cursor->stmt), &undoStat);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = TrxGetUndoStat(QryGetSeInstance(cursor->stmt), &undoStat);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = DmVertexFillUndoMemoryUsageByNameBase(&undoStat, vertex, *instanId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = DmVertexFillUndoLogicUsage(&undoStat, vertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    (*instanId)++;
    return GMERR_OK;
}

Status FillDmVertexByTrxDetail(DmVertexT *vertex, const TrxDetailT *trxDetail)
{
    DB_POINTER2(vertex, trxDetail);
    DmValueT propertyValue = {0};

    propertyValue.type = DB_DATATYPE_BOOL;
    propertyValue.value.boolValue = trxDetail->isAbort;
    Status ret = DmVertexSetPropeByName("IS_ABORT", propertyValue, vertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    propertyValue.type = DB_DATATYPE_BOOL;
    propertyValue.value.boolValue = trxDetail->isReadOnly;
    ret = DmVertexSetPropeByName("IS_READ_ONLY", propertyValue, vertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    propertyValue.type = DB_DATATYPE_BOOL;
    propertyValue.value.boolValue = trxDetail->isRetryTrx;
    ret = DmVertexSetPropeByName("IS_RETRY_TRX", propertyValue, vertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    char timeStr[DB_MAX_NAME_LEN];
    ret = DbGetTimestampStr(trxDetail->startTime, timeStr, DB_MAX_NAME_LEN, DEFAULT_TIME_FORMAT);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get timestamp : %" PRIu64 " in trx detail view.", trxDetail->startTime);
        return ret;
    }

    ret = DmVertexSetStrPropeByName(vertex, "START_TIME", timeStr);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = DbGetDurationStr(trxDetail->startTime, DbRdtsc(), timeStr, DB_MAX_NAME_LEN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get running time string.");
        return ret;
    }

    ret = DmVertexSetStrPropeByName(vertex, "DURATION", timeStr);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SvQueryGetParaUint64(vertex, trxDetail->tid, "THREAD_ID");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    return FillDmVertexByTrxStatSingle(vertex, trxDetail);
}

Status FillDmVertexSavePoint(DmVertexT *vertex, const TrxDetailT *trxDetail)
{
    DB_POINTER2(vertex, trxDetail);

    uint32_t listCnt = DbListGetItemCnt(&trxDetail->savePointList);
    Status ret = SvQueryGetParaUint32(vertex, listCnt, "SAVE_POINT_NUM");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = SvQueryGetParaUint32(vertex, trxDetail->savePointIdForCheckActive, "SAVE_POINT_ID_FOR_CHECK_ACTIVE");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    DmNodeT *vertexNodeAll = NULL;
    ret = DmVertexGetNodeByName(vertex, "SAVE_POINT_LIST", &vertexNodeAll);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmValueT propertyValue = {0};
    // 最多显示1024个savepoint
    int32_t endIdx = listCnt <= SAVEPOINT_SYSVIEW_MAX_NUM ? 0 : (int32_t)(listCnt - SAVEPOINT_SYSVIEW_MAX_NUM);
    for (int32_t idx = (int32_t)listCnt - 1; idx >= endIdx; idx--) {
        ret = DmNodeVectorAppend(vertexNodeAll);
        if (ret != GMERR_OK) {
            return ret;
        }
        SavePointT *spInfo = (SavePointT *)DbListItem(&trxDetail->savePointList, (uint32_t)idx);
        propertyValue.value.strAddr = spInfo->savePointName.name;
        propertyValue.value.length = spInfo->savePointName.length;
        propertyValue.type = DB_DATATYPE_STRING;
        ret = DmNodeSetPropeByName("SAVE_POINT_NAME", propertyValue, vertexNodeAll);
        if (ret != GMERR_OK) {
            return ret;
        }
        propertyValue.type = DB_DATATYPE_UINT32;
        propertyValue.value.uintValue = spInfo->savePointId;
        ret = DmNodeSetPropeByName("SAVE_POINT_ID", propertyValue, vertexNodeAll);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

#ifdef EXPERIMENTAL_GUANGQI
Status FillDmVertexChildrenCloneInfo(DmVertexT *vertex, const TrxDetailT *trxDetail)
{
    DmNodeT *vertexNodeAll = NULL;
    Status ret = DmVertexGetNodeByName(vertex, "TRX_CLONE_INFO", &vertexNodeAll);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmValueT propertyValue = {0};
    // 暂只支持两个孩子事务
    if (trxDetail->trxCloneInfo.childTrxActive) {
        ret = DmNodeVectorAppend(vertexNodeAll);
        if (ret != GMERR_OK) {
            return ret;
        }
        propertyValue.type = DB_DATATYPE_UINT64;
        propertyValue.value.uintValue = trxDetail->trxCloneInfo.childTrxId;
        ret = DmNodeSetPropeByName("TRX_ID", propertyValue, vertexNodeAll);
        if (ret != GMERR_OK) {
            return ret;
        }
        propertyValue.type = DB_DATATYPE_UINT16;
        propertyValue.value.uintValue = (uint16_t)TRX_ClONE_TYPE_CLONE_TRX;
        ret = DmNodeSetPropeByName("TRX_CLONE_TYPE", propertyValue, vertexNodeAll);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (trxDetail->trxCloneInfo.successorTrxActive) {
        ret = DmNodeVectorAppend(vertexNodeAll);
        if (ret != GMERR_OK) {
            return ret;
        }
        propertyValue.type = DB_DATATYPE_UINT64;
        propertyValue.value.uintValue = trxDetail->trxCloneInfo.successorTrxId;
        ret = DmNodeSetPropeByName("TRX_ID", propertyValue, vertexNodeAll);
        if (ret != GMERR_OK) {
            return ret;
        }
        propertyValue.type = DB_DATATYPE_UINT16;
        propertyValue.value.uintValue = (uint16_t)TRX_ClONE_TYPE_SUCCESSOR_TRX;
        ret = DmNodeSetPropeByName("TRX_CLONE_TYPE", propertyValue, vertexNodeAll);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status FillDmVertexCloneInfo(DmVertexT *vertex, const TrxDetailT *trxDetail)
{
    DB_POINTER2(vertex, trxDetail);

    Status ret = SvQueryGetParaUint32(vertex, trxDetail->trxCloneInfo.cloneId, "CLONE_ID");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = SvQueryGetParaUint16(vertex, trxDetail->trxCloneInfo.trxCloneType, "TRX_CLONE_TYPE");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (trxDetail->trxCloneInfo.trxCloneType != (uint16_t)TRX_ClONE_TYPE_CLONABLE_TRX &&
        trxDetail->trxCloneInfo.trxCloneType != (uint16_t)TRX_ClONE_TYPE_CLONED) {
        return GMERR_OK;
    }
    return FillDmVertexChildrenCloneInfo(vertex, trxDetail);
}
#endif

Status FillDmVertexByLockModeStat(DmVertexT *vertex, const LockModeStatT *lockModeStat)
{
    Status ret = SvQueryGetParaUint32(vertex, lockModeStat->sum[SE_LOCK_MODE_IS], "HOLD_IS_LOCK_NUM");
    ret =
        (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, lockModeStat->sum[SE_LOCK_MODE_IX], "HOLD_IX_LOCK_NUM") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, lockModeStat->sum[SE_LOCK_MODE_S], "HOLD_S_LOCK_NUM") : ret;
    ret = (ret == GMERR_OK) ? SvQueryGetParaUint32(vertex, lockModeStat->sum[SE_LOCK_MODE_X], "HOLD_X_LOCK_NUM") : ret;
    return ret;
}

Status SvQueryProcStorageEngineTrxDetail(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    uint16_t *trxSlot = (uint16_t *)cursor->rowId;
    uint16_t curTrxSlot = *trxSlot;

    TrxDetailT trxDetail = {0};
    Status ret = SeGetTrxtLockDetail(&curTrxSlot, &trxDetail, QryGetSeInstance(cursor->stmt));
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = FillDmVertexByTrxDetail(vertex, &trxDetail);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = FillDmVertexSavePoint(vertex, &trxDetail);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
#ifdef EXPERIMENTAL_GUANGQI
    ret = FillDmVertexCloneInfo(vertex, &trxDetail);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
#endif
    LockModeStatT lockModeStat = {{0}};
    ret = FillDmVertexByHoldAcqLock(vertex, &trxDetail, &lockModeStat);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = FillDmVertexByLockModeStat(vertex, &lockModeStat);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = FillDmVertexByNotifyLock(vertex, &trxDetail);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    (*trxSlot) = (uint16_t)(curTrxSlot + 1);
EXIT:
    if (trxDetail.lockAcqStat != NULL) {
        DbDynMemCtxFree(((const QryStmtT *)cursor->stmt)->session->memCtx, trxDetail.lockAcqStat);
        trxDetail.lockAcqStat = NULL;
    }
    return ret;
}

#ifdef __cplusplus
}
#endif
