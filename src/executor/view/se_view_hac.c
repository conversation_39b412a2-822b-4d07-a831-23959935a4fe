/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: se_view_hac.c
 * Description: sysview file for hardware accelerator
 * Author:
 * Create: 2024-10-21
 */

#ifdef FEATURE_HAC

#include "se_view_hac.h"

#include "se_index.h"
#include "dm_data_basic.h"
#include "dm_data_prop.h"
#include "se_view_base.h"
#include "se_view_index.h"

#ifdef __cplusplus
extern "C" {
#endif

static Status SvFillHacSoftwareInfo(DmVertexT *vertex, const HacStatisticsT *hacStat)
{
    Status ret = SvQueryGetParaUint32(vertex, hacStat->hacHashIdxCnt, "HAC_HASH_INDEX_COUNT");
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SvQueryGetParaUint32(vertex, hacStat->multiHashIdxCnt, "MULTI_HASH_INDEX_COUNT");
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SvQueryGetParaUint64(vertex, hacStat->batchSoftwareCnt, "BATCH_SOFTWARE_COUNT");
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SvQueryGetParaUint64(vertex, hacStat->batchExcapedCnt, "BATCH_EXCAPED_COUNT");
    if (ret != GMERR_OK) {
        return ret;
    }

    return SvQueryGetParaUint8(vertex, hacStat->allocBlockCnt, "ALLOC_BLOCK_COUNT");
}

static Status SvFillHacHardwareInfo(DmVertexT *vertex, const HacStatisticsT *hacStat)
{
    Status ret = SvQueryGetParaUint64(vertex, hacStat->batchHacCnt, "BATCH_HAC_COUNT");
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SvQueryGetParaUint64(vertex, hacStat->batchHacSyncCnt, "BATCH_HAC_SYNC_COUNT");
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SvQueryGetParaUint64(vertex, hacStat->batchHacAsyncCnt, "BATCH_HAC_ASYNC_COUNT");
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SvQueryGetParaUint64(vertex, hacStat->hacRqstCnt, "HAC_REQUEST_COUNT");
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SvQueryGetParaUint64(vertex, hacStat->hacRspWarnCnt, "HAC_RESPONSE_WARNING_COUNT");
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SvQueryGetParaUint64(vertex, hacStat->hacRspNormalErrCnt, "HAC_RESPONSE_NORMAL_ERR_COUNT");
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SvQueryGetParaUint64(vertex, hacStat->hacRspSpecialErrCnt, "HAC_RESPONSE_SPECIAL_ERR_COUNT");
    if (ret != GMERR_OK) {
        return ret;
    }

    return SvQueryGetParaUint8(vertex, hacStat->allocHacBlockCnt, "ALLOC_HAC_BLOCK_COUNT");
}

static Status SvRecordHacGlobalStat(DmVertexT *vertex)
{
    const HacStatisticsT *hacStat = HacGetStats();
    if (hacStat == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "hacStat.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    Status ret = SvFillHacSoftwareInfo(vertex, hacStat);
    if (ret != GMERR_OK) {
        return ret;
    }

    return SvFillHacHardwareInfo(vertex, hacStat);
}

Status SvQueryProcStorageHacGlobalStat(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    if (!IsHacInitialized()) {
        DB_LOG_WARN(GMERR_NO_DATA, "HacMgr is not initialized.");
        return GMERR_NO_DATA;
    }

    char *labelId = cursor->rowId;
    if (*labelId > 0) {
        return GMERR_NO_DATA;
    }
    *labelId = 1;
    return SvRecordHacGlobalStat(vertex);
}

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* FEATURE_HAC */
