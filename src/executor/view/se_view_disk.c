/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: se_view_disk.c
 * Description: sysview file for disk info
 * Author: hantt
 * Create: 2024-04-25
 */

#ifdef FEATURE_TS

#include "se_view_disk.h"
#include "dm_data_prop.h"
#include "dm_data_ts.h"
#include "se_define.h"
#include "se_cstore.h"
#include "se_space.h"
#include "se_cu_storage.h"
#include "dm_data_record.h"
#include "ee_stmt_interface.h"
#include "ee_stmt.h"
#include "ee_systbl.h"
#include "db_sysapp_context.h"
#include "srv_data_ts.h"
#include "se_view_base.h"
#include "se_view_disk.h"

#define INTERVAL_STR_MAX_LEN 64
#define SEC_TRANS 1000

#ifdef __cplusplus
extern "C" {
#endif

Status SvGetDiskUsageByLogicalLabel(const DmVertexLabelT *vertexLabel, int64_t *rowDiskUsage, int64_t *colDiskUsage)
{
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(DbGetInstanceId(DbGetInstanceByMemCtx(vertexLabel->memCtx)));
    if (seInstance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get seInstance, instanceId:%" PRIu16,
            DbGetInstanceId(DbGetInstanceByMemCtx(vertexLabel->memCtx)));
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint64_t rowSize = 0;
    DmTsInfoT *dmTsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;
    Status ret = SeSpaceGetFileSize(seInstance, dmTsInfo->persistentSpaceId, &rowSize);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get space size in get disk usage.");
        return ret;
    }
    *rowDiskUsage = (int64_t)rowSize;
    if (colDiskUsage != NULL) {
        *colDiskUsage = (int64_t)SeGetCuFileSize(vertexLabel->metaCommon.metaId);
    }
    return GMERR_OK;
}

Status SvSetFixedPropeByName(const DmVertexT *vertex, const char *propeName, char *value, uint32_t valueLen)
{
    DB_POINTER3(vertex, propeName, value);
    uint32_t propeId = 0;
    Status ret = GetPropertyIdByName(vertex->record, propeName, &propeId);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmPropertyInfoT propeInfo = vertex->record->recordDesc->propeInfos[propeId];
    if (SECUREC_UNLIKELY(!propeInfo.isValid)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PROPERTY, "Inv property when set property %s.", propeName);
        return GMERR_INVALID_PROPERTY;
    }

    uint32_t propeLen = propeInfo.propeMaxLen;
    char *tmpStr = DbDynMemCtxAlloc(vertex->memCtx, propeLen);
    if (tmpStr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "set fixed property, val len:%" PRIu32 ".", valueLen);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(tmpStr, propeLen, 0, propeLen);
    do {
        errno_t err = memcpy_s(tmpStr, propeLen, value, valueLen);
        if (err != EOK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "memcpy property, val len:%" PRIu32 ".", valueLen);
            ret = GMERR_MEMORY_OPERATE_FAILED;
            break;
        }

        ret = DmVertexSetFixedPropeByName(vertex, propeName, tmpStr, propeLen);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "set %s to prope %s .", tmpStr, propeName);
        }
    } while (false);

    DbDynMemCtxFree(vertex->memCtx, tmpStr);
    return ret;
}

Status SvOpenCuStorageHdl(uint32_t logicalTblId, uint32_t phyTblId, CuStorageHdlT **cuStorageHdl)
{
    CuStorageKeyT cuStorageKey = {.tblSpcId = logicalTblId, .tblId = phyTblId};
    Status ret = SeOpenCuStorageHdl(NULL, &cuStorageKey, false, cuStorageHdl, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "open cu storage hdl for logicalTblId:%" PRIu32 ", phyTblId:%" PRIu32, logicalTblId, phyTblId);
        return ret;
    }
    return GMERR_OK;
}

static void TsDmGetSchemaLength(const DmSchemaT *schema, uint32_t *schemaMaxLen, uint32_t *schemaMinLen)
{
    if (schema == NULL) {
        return;
    }
    DB_POINTER2(schemaMaxLen, schemaMinLen);

    for (uint32_t i = 0; i < schema->propeNum; ++i) {
        DmPropertySchemaT *prop = &(schema->properties[i]);
        if (!prop->isValid) {
            continue;
        }
        if (prop->isFixed) {
            *schemaMaxLen += prop->size;
            *schemaMinLen += prop->size;
        } else {
            // 变长字段序列化时需要记录长度，最大需要4字节
            *schemaMaxLen += prop->size + (uint32_t)sizeof(uint32_t);
            // 变长字段实际长度为0时，序列化时需占用1字节
            *schemaMinLen += 1;
        }
    }
}

static uint64_t SvGetMemLabelRowCount(const DmVertexLabelT *vertexLabel)
{
    DB_POINTER(vertexLabel);
    DmAccCheckT *accCheckAddr = vertexLabel->commonInfo->accCheckAddr;
    return accCheckAddr->checkInfo->recordCnt;
}

static Status SvSetMemLabelRowCountAndDiskUsage(
    const DmVertexT *vertex, const DmVertexLabelT *vertexLabel, int64_t schemaMaxLen)
{
    Status ret = GMERR_OK;
    bool setOK = true;
    DB_ASSERT(vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_NORMAL);
    int64_t rowCount = 0;

    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    if (SECUREC_LIKELY(
            DmIsLabelRuMode(commonInfo->heapInfo.ccType) || DmIsLabelLatchMode(commonInfo->heapInfo.ccType))) {
        rowCount = (int64_t)SvGetMemLabelRowCount(vertexLabel);
        goto FINISH;
    }
    DbRWLatchW(&commonInfo->accCheckAddr->lock);
    rowCount = (int64_t)SvGetMemLabelRowCount(vertexLabel);
    DbRWUnlatchW(&commonInfo->accCheckAddr->lock);

FINISH:
    ret = DmVertexSetInt64PropeByName(vertex, "ROW_CNT", rowCount);
    setOK = setOK && (ret == GMERR_OK);
    int64_t diskUsage = rowCount * schemaMaxLen;
    ret = DmVertexSetInt64PropeByName(vertex, "DISK_USAGE", diskUsage);
    setOK = setOK && (ret == GMERR_OK);
    if (!setOK) {
        DB_LOG_ERROR(GMERR_INVALID_PROPERTY, "set disk usage for disk usage sysview.");
        return GMERR_INVALID_PROPERTY;
    }
    return ret;
}

Status SvSetMemLabelDiskUsageInfo(const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(vertex, vertexLabel);
    uint32_t metaNameLen = (uint32_t)DM_STR_LEN(vertexLabel->metaCommon.metaName);
    Status ret = GMERR_OK;
    DmTsInfoT *dmTsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;
    int64_t diskLimit = 0;
    uint32_t schemaMaxLen = 0;
    uint32_t schemaMinLen = 0;
    TsDmGetSchemaLength(vertexLabel->metaVertexLabel->schema, &schemaMaxLen, &schemaMinLen);
    if (dmTsInfo->useDiskLimit) {
        diskLimit = dmTsInfo->diskLimit * (int64_t)schemaMaxLen;
    }

    bool setOK = true;
    ret = SvSetFixedPropeByName(vertex, "TABLE_NAME", vertexLabel->metaCommon.metaName, metaNameLen);
    setOK = setOK && (ret == GMERR_OK);
    ret = DmVertexSetInt64PropeByName(vertex, "DISK_LIMIT", diskLimit);
    setOK = setOK && (ret == GMERR_OK);
    ret = DmVertexSetInt64PropeByName(vertex, "ROW_DISK_USAGE", 0);
    setOK = setOK && (ret == GMERR_OK);
    ret = DmVertexSetInt64PropeByName(vertex, "COL_DISK_USAGE", 0);
    setOK = setOK && (ret == GMERR_OK);
    ret = DmVertexSetInt64PropeByName(vertex, "PHY_TBL_NUM", 0);
    setOK = setOK && (ret == GMERR_OK);
    if (!setOK) {
        DB_LOG_ERROR(GMERR_INVALID_PROPERTY, "set disk usage sysview.");
        return GMERR_INVALID_PROPERTY;
    }
    ret = SvSetMemLabelRowCountAndDiskUsage(vertex, vertexLabel, (int64_t)schemaMaxLen);
    return ret;
}

Status SvSetDiskUsageInfo(const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(vertex, vertexLabel);
    uint32_t metaNameLen = (uint32_t)DM_STR_LEN(vertexLabel->metaCommon.metaName);
    Status ret = GMERR_OK;
    int64_t rowDiskUsage = 0;
    int64_t colDiskUsage = 0;
    ret = SvGetDiskUsageByLogicalLabel(vertexLabel, &rowDiskUsage, &colDiskUsage);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get disk usage for disk usage sysview.");
        return ret;
    }
    int64_t diskUsage = rowDiskUsage + colDiskUsage;

    bool setOK = true;
    ret = SvSetFixedPropeByName(vertex, "TABLE_NAME", vertexLabel->metaCommon.metaName, metaNameLen);
    setOK = setOK && (ret == GMERR_OK);

    DmTsInfoT *dmTsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;
    ret = DmVertexSetInt64PropeByName(vertex, "DISK_LIMIT", dmTsInfo->diskLimit);
    setOK = setOK && (ret == GMERR_OK);

    ret = DmVertexSetInt64PropeByName(vertex, "DISK_USAGE", diskUsage);
    setOK = setOK && (ret == GMERR_OK);

    ret = DmVertexSetInt64PropeByName(vertex, "ROW_DISK_USAGE", rowDiskUsage);
    setOK = setOK && (ret == GMERR_OK);

    ret = DmVertexSetInt64PropeByName(vertex, "COL_DISK_USAGE", colDiskUsage);
    setOK = setOK && (ret == GMERR_OK);
    if (!setOK) {
        DB_LOG_ERROR(GMERR_INVALID_PROPERTY, "set col disk usage for disk usage sysview.");
        return GMERR_INVALID_PROPERTY;
    }
    return ret;
}

static Status SvGetLogicTblRowCnt(const DmVertexLabelT *vertexLabel, DbListT *phyTblList, int64_t *rowCnt)
{
    Status ret = SeBeginCuFileAccess();
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "begin CuFile access.");
        return ret;
    }
    uint32_t totalRowCnt = 0;
    uint32_t phyTblCnt = DbListGetItemCnt(phyTblList);
    for (uint32_t i = 0; i < phyTblCnt; i++) {
        uint32_t physicalId = *(uint32_t *)DbListItem(phyTblList, i);
        CuStorageHdlT *cuStorageHdl = NULL;
        ret = SvOpenCuStorageHdl(vertexLabel->metaCommon.metaId, physicalId, &cuStorageHdl);
        if (ret != GMERR_OK) {
            SeEndCuFileAccess();
            return ret;
        }
        totalRowCnt += cuStorageHdl->rowCnt;
        SeCloseCuStorageHdl(cuStorageHdl, false);
    }
    SeEndCuFileAccess();
    *rowCnt = (int64_t)totalRowCnt;
    return GMERR_OK;
}

Status SvSetLogicTblRowCnt(const DmVertexT *vertex, SvCursorT *cursor, const DmVertexLabelT *vertexLabel)
{
    QryStmtT *stmt = (QryStmtT *)cursor->stmt;
    SessionT *session = stmt->session;
    DbListT phyTblList = {0};
    DbCreateList(&phyTblList, sizeof(uint32_t), vertex->memCtx);
    Status ret = SysTableTsMapGetIdListByLogicalId(session, vertexLabel->metaCommon.metaId, &phyTblList);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get phy tbl for:%s.", vertexLabel->metaCommon.metaName);
        return ret;
    }

    uint32_t phyTblCnt = DbListGetItemCnt(&phyTblList);
    ret = DmVertexSetInt64PropeByName(vertex, "PHY_TBL_NUM", phyTblCnt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set phy tbl num");
        return ret;
    }

    int64_t rowCnt = 0;
    ret = SvGetLogicTblRowCnt(vertexLabel, &phyTblList, &rowCnt);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DmVertexSetInt64PropeByName(vertex, "ROW_CNT", rowCnt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set tbl row num");
        return ret;
    }

    return ret;
}

bool SvTsVertexLabelFilter(const void *metaLabel)
{
    DB_POINTER(metaLabel);
    const DmVertexLabelT *vertexLabel = *(const DmVertexLabelT **)(uintptr_t)metaLabel;

    if (vertexLabel->metaCommon.isDeleted) {
        return false;
    }

    if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_TS_LOGICAL) {
        return true;
    } else if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_NORMAL) {
        void *dmTsInfo = vertexLabel->metaVertexLabel->extraInfo.data;
        if (dmTsInfo != NULL) {
            return true;
        }
    }
    return false;
}

bool SvLogicalVertexLabelFilter(const void *metaLabel)
{
    DB_POINTER(metaLabel);
    const DmVertexLabelT *vertexLabel = *(const DmVertexLabelT **)(uintptr_t)metaLabel;

    return !vertexLabel->metaCommon.isDeleted &&
           vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_TS_LOGICAL;
}

static void ReleaseLabelAndLatch(DmVertexLabelT *vertexLabel, LabelRWLatchT *labelRWLatch)
{
    if (vertexLabel != NULL) {
        (void)CataReleaseVertexLabel(vertexLabel);
    }
    if (labelRWLatch != NULL) {
        QryProcReleaseRLabelLatch(labelRWLatch);
    }
}

Status SvQueryStorageDiskUsage(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    Status ret = GMERR_NO_DATA;
    DmVertexT *dmVertex = (DmVertexT *)vertex;
    DbOamapIteratorT *vertexLabelIter = (uint32_t *)cursor->rowId;
    DmVertexLabelT *vertexLabel = NULL;
    uint32_t labelLatchVersionId;
    LabelRWLatchT *labelRWLatch = NULL;

    while (CataFetchMetaLabel(vertexLabelIter, CATA_VL, (void **)&vertexLabel, SvTsVertexLabelFilter,
               DbGetInstanceBySvCursor(cursor)) == GMERR_OK) {
        if (cursor->viewCond != NULL && DbStrCmp(vertexLabel->metaCommon.metaName, cursor->viewCond->str, false) != 0) {
            continue;
        }
        labelLatchVersionId = vertexLabel->commonInfo->vertexLabelLatchVersionId;
        labelRWLatch = (LabelRWLatchT *)DbShmPtrToAddr(vertexLabel->commonInfo->labelLatchShmAddr);
        if (labelRWLatch == NULL) {
            (void)CataReleaseVertexLabel(vertexLabel);
            continue;
        }
        ret = QryProceAcqRLabelLatch(labelRWLatch, labelLatchVersionId);
        if (ret == GMERR_UNDEFINED_TABLE) {
            (void)CataReleaseVertexLabel(vertexLabel);  // 1. if acquire label is dropped , scan next vertexlabel
            continue;
        }
        if (ret != GMERR_OK) {
            (void)CataReleaseVertexLabel(vertexLabel);  // 2. if acquire label latch failed(e.g. timeout), return error
            return ret;
        }
        if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_TS_LOGICAL) {
            ret = SvSetDiskUsageInfo(dmVertex, vertexLabel);  // 3. set the disk usage info into vertex
            if (ret != GMERR_OK) {
                ReleaseLabelAndLatch(vertexLabel, labelRWLatch);
                return ret;
            }
            ret = SvSetLogicTblRowCnt(dmVertex, cursor, vertexLabel);  // 4.phy tbl cnt and row cnt
            if (ret != GMERR_OK) {
                ReleaseLabelAndLatch(vertexLabel, labelRWLatch);
                return ret;
            }
        } else {
            if ((ret = SvSetMemLabelDiskUsageInfo(dmVertex, vertexLabel)) != GMERR_OK) {
                ReleaseLabelAndLatch(vertexLabel, labelRWLatch);
                return ret;
            }
        }

        ReleaseLabelAndLatch(vertexLabel, labelRWLatch);
        return ret;
    }
    return GMERR_NO_DATA;
}

static void SvConvertIntervalToStr(TsIntervalT *interval, char *intervalStr, uint32_t intervalStrLen)
{
    if (interval->day > 0) {
        (void)sprintf_s(intervalStr, intervalStrLen, "%" PRId32 " days", interval->day);
    } else if (interval->month > 0) {
        (void)sprintf_s(intervalStr, intervalStrLen, "%" PRId32 " months", interval->month);
    } else {
        int64_t milliSec = interval->microsecond / SEC_TRANS;
        int64_t sec = milliSec / SEC_TRANS;
        (void)sprintf_s(intervalStr, intervalStrLen, "%" PRId64 " seconds", sec);
    }
    return;
}

static Status SvSetPhyTblInterval(DbMemCtxT *memCtx, const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    char *tmpStr = DbDynMemCtxAlloc(memCtx, INTERVAL_STR_MAX_LEN);
    if (tmpStr == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "set vertex interval property.");
        return GMERR_OUT_OF_MEMORY;
    }

    DmTsInfoT *dmTsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;
    SvConvertIntervalToStr(&dmTsInfo->interval, tmpStr, INTERVAL_STR_MAX_LEN);

    uint32_t intervalStrLen = (uint32_t)DM_STR_LEN(tmpStr);
    Status ret = SvSetFixedPropeByName(vertex, "INTERVAL", tmpStr, intervalStrLen);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set interval for phy tbl sysview.");
    }
    DbDynMemCtxFree(memCtx, tmpStr);
    return ret;
}

static Status SvSetLogicTblInfoInPhyTblView(
    QryStmtT *stmt, PhysicalLabelMetaT *meta, const DmVertexT *vertex, int64_t *totalRowCnt, int64_t *totalRowSize)
{
    SessionT *session = stmt->session;
    DbMemCtxT *memCtx = stmt->memCtx;
    DmVertexLabelT *logicLabel = NULL;
    Status ret = CataGetVertexLabelById(DbGetInstanceByMemCtx(stmt->session->memCtx), meta->logicalId, &logicLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get label id:%" PRIu32 " for phy sysview.", meta->logicalId);
        return ret;
    }
    uint32_t logicNameLen = (uint32_t)DM_STR_LEN(logicLabel->metaCommon.metaName);
    ret = SvSetFixedPropeByName(vertex, "TABLE_NAME", logicLabel->metaCommon.metaName, logicNameLen);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set table name:%s for phy sysview.", logicLabel->metaCommon.metaName);
        goto END;
    }

    ret = SvGetDiskUsageByLogicalLabel(logicLabel, totalRowSize, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get disk usage for tbl: %s.", logicLabel->metaCommon.metaName);
        goto END;
    }

    // phy cnt and row cnt
    DbListT phyTblList = {0};
    DbCreateList(&phyTblList, sizeof(uint32_t), memCtx);
    ret = SysTableTsMapGetIdListByLogicalId(session, logicLabel->metaCommon.metaId, &phyTblList);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get sysTbl map for table: %s.", logicLabel->metaCommon.metaName);
        goto END;
    }
    ret = SvGetLogicTblRowCnt(logicLabel, &phyTblList, totalRowCnt);
    if (ret != GMERR_OK) {
        goto END;
    }
    DbDestroyList(&phyTblList);

    // interval
    ret = SvSetPhyTblInterval(memCtx, vertex, logicLabel);
END:
    (void)CataReleaseVertexLabel(logicLabel);
    return ret;
}

static Status SvSetPhyTblCntAndSizeInPhyTblView(
    PhysicalLabelMetaT *meta, const DmVertexT *vertex, uint32_t totalRowCnt, uint32_t totalRowSize)
{
    CuStorageHdlT *cuStorageHdl = NULL;
    Status ret = SvOpenCuStorageHdl(meta->logicalId, meta->physicalId, &cuStorageHdl);
    if (ret != GMERR_OK) {
        return ret;
    }

    char timeStr[TIME_STR_MAX_SIZE] = {0};
    ret = DmConvertIntTimeToStrTime(meta->upperBound, timeStr, TIME_STR_MAX_SIZE);
    if (ret != GMERR_OK) {
        goto END;
    }

    bool setOK = true;
    ret = DmVertexSetInt64PropeByName(vertex, "UPPER_BOUND", meta->upperBound);
    setOK = setOK && (ret == GMERR_OK);

    ret = SvSetFixedPropeByName(vertex, "UPPER_BOUND_TIME_STR", timeStr, TIME_STR_MAX_SIZE);
    setOK = setOK && (ret == GMERR_OK);

    ret = DmVertexSetInt64PropeByName(vertex, "CU_CNT", cuStorageHdl->maxCuId - CU_ID_START);
    setOK = setOK && (ret == GMERR_OK);

    ret = DmVertexSetInt64PropeByName(vertex, "ROW_CNT", cuStorageHdl->rowCnt);
    setOK = setOK && (ret == GMERR_OK);

    // 按照行数比例计算物理表行存大小
    if (totalRowCnt > 0) {
        uint32_t phyRowDiskUsage = (uint32_t)(((double)cuStorageHdl->rowCnt / totalRowCnt) * totalRowSize);
        ret = DmVertexSetInt64PropeByName(vertex, "ROW_DISK_USAGE", phyRowDiskUsage);
        setOK = setOK && (ret == GMERR_OK);
    }

    ret = DmVertexSetInt64PropeByName(vertex, "COL_DISK_USAGE", cuStorageHdl->cuFileSize + sizeof(CuMetaFileT));
    setOK = setOK && (ret == GMERR_OK);
    if (!setOK) {
        ret = GMERR_INVALID_PROPERTY;
        DB_LOG_ERROR(GMERR_INVALID_PROPERTY, "set sysview property");
        goto END;
    }
END:
    SeCloseCuStorageHdl(cuStorageHdl, false);
    return ret;
}

static Status SvSetPhyTblViewInfo(
    QryStmtT *stmt, PhysicalLabelMetaT *meta, const DmVertexLabelT *phyLabel, const DmVertexT *vertex)
{
    DB_POINTER2(vertex, phyLabel);
    Status ret = DmVertexSetInt64PropeByName(vertex, "PHY_TABLE_ID", phyLabel->metaCommon.metaId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set table id for phy tbl sysview.");
        return ret;
    }

    ret = DmVertexSetInt64PropeByName(vertex, "TABLE_ID", meta->logicalId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set logical table id for phy tbl sysview.");
        return ret;
    }

    int64_t totalRowCnt = 0;
    int64_t totalRowSize = 0;
    ret = SvSetLogicTblInfoInPhyTblView(stmt, meta, vertex, &totalRowCnt, &totalRowSize);
    if (ret != GMERR_OK) {
        return ret;
    }

    return SvSetPhyTblCntAndSizeInPhyTblView(meta, vertex, totalRowCnt, totalRowSize);
}

static Status SvPhyTblViewGetAllPhyTbls(SessionT *session, SvCursorT *cursor)
{
    Status ret = GMERR_OK;
    // 获取所有物理表信息
    cursor->data = DbDynMemCtxAlloc(session->memCtx, sizeof(DbListT));
    if (cursor->data == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc phyMetaList.");
        return GMERR_OUT_OF_MEMORY;
    }
    DbListT *phyMetaList = (DbListT *)cursor->data;
    DbCreateList(phyMetaList, sizeof(PhysicalLabelMetaT), session->memCtx);
    // 查询、修改映射表前开启事务
    ret = SeTransBegin(session->seInstance, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (cursor->viewCond != NULL) {
        CataKeyT cataKey = {0};
        DmVertexLabelT *logicLabel = NULL;
        CataSetKeyForLabel(&cataKey, session->dbId, session->namespaceId, cursor->viewCond->str);
        ret = CataGetVertexLabelByName(DbGetInstanceByMemCtx(session->memCtx), &cataKey, &logicLabel);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get logic label.");
            (void)SeTransRollback(session->seInstance, false);
            if (ret == GMERR_UNDEFINED_TABLE) {  // 找不到表不报错，直接返回，phyMetaList为空即可
                return GMERR_OK;
            }
            return ret;
        }
        ret = SysTableTsMapGetPhysicalLabelMetaByLogicLabelId(session, logicLabel->metaCommon.metaId, phyMetaList);
        if (ret != GMERR_OK) {
            (void)CataReleaseVertexLabel(logicLabel);
            goto ERROR;
        }
        (void)CataReleaseVertexLabel(logicLabel);
    } else {
        ret = SysTableTsMapGetAllPhysicalLabelMeta(session, phyMetaList);
        if (ret != GMERR_OK) {
            goto ERROR;
        }
    }
    ret = SeTransCommit(session->seInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "commit transaction for physical view");
        return ret;
    }
    return GMERR_OK;
ERROR:
    (void)SeTransRollback(session->seInstance, false);
    return ret;
}

static Status GetValidPhysicalLabelFromTsMap(
    SessionT *session, PhysicalLabelMetaT **phyMeta, DmVertexLabelT **phyLabel, uint32_t *curId, DbListT *phyMetaList)
{
    uint32_t phyCnt = DbListGetItemCnt(phyMetaList);
    while (*phyMeta == NULL && *curId < phyCnt) {
        *phyMeta = (PhysicalLabelMetaT *)DbListItem(phyMetaList, *curId);
        DmVertexLabelT *logicLabel = NULL;
        Status ret = CataGetVertexLabelById(DbGetInstanceByMemCtx(session->memCtx), (*phyMeta)->logicalId, &logicLabel);
        if (ret != GMERR_OK) {
            if (ret == GMERR_UNDEFINED_TABLE) {
                (*curId)++;
                *phyMeta = NULL;
                continue;
            }
            DB_LOG_ERROR(ret, "get logic label by id: %" PRIu32, (*phyMeta)->logicalId);
            return ret;
        }
        (void)CataReleaseVertexLabel(logicLabel);
        ret = SysTableTsMapGetPhysicalLabelById(session, session->memCtx, (*phyMeta)->physicalId, phyLabel);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get physical label by id: %" PRIu32, (*phyMeta)->physicalId);
            return ret;
        }
    }
    return GMERR_OK;
}

// 查看物理表统计信息
Status SvQueryPhyTblDiskUsage(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    Status ret = GMERR_NO_DATA;
    DmVertexT *dmVertex = (DmVertexT *)vertex;
    QryStmtT *stmt = (QryStmtT *)cursor->stmt;
    SessionT *session = stmt->session;
    uint32_t *curId = (uint32_t *)cursor->rowId;
    if (*curId == 0) {
        ret = SvPhyTblViewGetAllPhyTbls(session, cursor);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    ret = SeTransBegin(session->seInstance, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }

    DbListT *phyMetaList = (DbListT *)cursor->data;
    uint32_t phyCnt = DbListGetItemCnt(phyMetaList);
    if (*curId >= phyCnt) {
        ret = GMERR_NO_DATA;
        goto END;
    }

    PhysicalLabelMetaT *phyMeta = NULL;
    DmVertexLabelT *phyLabel = NULL;
    ret = GetValidPhysicalLabelFromTsMap(session, &phyMeta, &phyLabel, curId, phyMetaList);
    if (ret != GMERR_OK) {
        goto END;
    }
    if (*curId >= phyCnt || phyLabel == NULL) {
        ret = GMERR_NO_DATA;
        goto END;
    }

    // 填充视图属性入口
    ret = SvSetPhyTblViewInfo(stmt, phyMeta, phyLabel, dmVertex);
END:
    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        (void)SeTransRollback(session->seInstance, false);
        DB_LOG_ERROR(ret, "query phy tbl view, curId: %" PRIu32 ", total: %" PRIu32, *curId, phyCnt);
        return ret;
    }
    Status status = SeTransCommit(session->seInstance);
    if (status != GMERR_OK) {
        DB_LOG_ERROR(status, "commit transaction for physical view");
        return status;
    }
    (*curId)++;
    return ret;
}

static Status SvSetTsOperateStatInfo(DmVertexT *vertex, DmVertexLabelT *vertexLabel)
{
#if !defined(RTOSV2X) && !defined(RTOSV2)
    TsOperateStatT *stat = TsQueryOperateStat(vertex->memCtx, vertexLabel);
    if (stat == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    bool setOK = true;
    uint32_t metaNameLen = (uint32_t)DM_STR_LEN(vertexLabel->metaCommon.metaName);
    Status ret = SvSetFixedPropeByName(vertex, "TABLE_NAME", vertexLabel->metaCommon.metaName, metaNameLen);
    setOK = setOK && (ret == GMERR_OK);

    ret = DmVertexSetInt64PropeByName(vertex, "DML_SUCCESS_CNT", stat->dmlSuccessCnt);
    setOK = setOK && (ret == GMERR_OK);

    ret = DmVertexSetInt64PropeByName(vertex, "DML_TOTAL_FAILED_CNT", stat->dmlTotalFailedCnt);
    setOK = setOK && (ret == GMERR_OK);

    ret = DmVertexSetInt64PropeByName(vertex, "DML_DISK_FAILED_CNT", stat->dmlDiskFailedCnt);
    setOK = setOK && (ret == GMERR_OK);

    ret = DmVertexSetInt64PropeByName(vertex, "DQL_SUCCESS_CNT", stat->dqlSuccessCnt);
    setOK = setOK && (ret == GMERR_OK);

    ret = DmVertexSetInt64PropeByName(vertex, "DQL_TOTAL_FAILED_CNT", stat->dqlTotalFailedCnt);
    setOK = setOK && (ret == GMERR_OK);

    ret = DmVertexSetInt64PropeByName(vertex, "DQL_DISK_FAILED_CNT", stat->dqlDiskFailedCnt);
    setOK = setOK && (ret == GMERR_OK);
    if (!setOK) {
        DB_LOG_ERROR(GMERR_INVALID_PROPERTY, "set sysview vertex property.");
        return GMERR_INVALID_PROPERTY;
    }
    return GMERR_OK;
#else
    return GMERR_FEATURE_NOT_SUPPORTED;
#endif
}

Status SvQueryTsTblOperateStat(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    Status ret = GMERR_NO_DATA;
    DmVertexT *dmVertex = (DmVertexT *)vertex;
    DbOamapIteratorT *vertexLabelIter = (uint32_t *)cursor->rowId;
    DmVertexLabelT *vertexLabel = NULL;
    uint32_t labelLatchVersionId;
    LabelRWLatchT *labelRWLatch = NULL;

    while (CataFetchMetaLabel(vertexLabelIter, CATA_VL, (void **)&vertexLabel, SvLogicalVertexLabelFilter,
               DbGetInstanceBySvCursor(cursor)) == GMERR_OK) {
        if (cursor->viewCond != NULL && DbStrCmp(vertexLabel->metaCommon.metaName, cursor->viewCond->str, false) != 0) {
            continue;
        }
        labelLatchVersionId = vertexLabel->commonInfo->vertexLabelLatchVersionId;
        labelRWLatch = (LabelRWLatchT *)DbShmPtrToAddr(vertexLabel->commonInfo->labelLatchShmAddr);
        if (labelRWLatch == NULL) {
            (void)CataReleaseVertexLabel(vertexLabel);
            continue;
        }
        ret = QryProceAcqRLabelLatch(labelRWLatch, labelLatchVersionId);
        // 1. if acquire label is dropped , scan next vertexlabel
        if (ret == GMERR_UNDEFINED_TABLE) {
            (void)CataReleaseVertexLabel(vertexLabel);
            continue;
        }
        // 2. if acquire label latch failed( e.g. timeout ), return error to client
        if (ret != GMERR_OK) {
            (void)CataReleaseVertexLabel(vertexLabel);
            return ret;
        }
        // 3. if acquire label latch successfully, set the disk usage info into vertex
        ret = SvSetTsOperateStatInfo(dmVertex, vertexLabel);
        (void)CataReleaseVertexLabel(vertexLabel);
        QryProcReleaseRLabelLatch(labelRWLatch);
        return ret;
    }
    return GMERR_NO_DATA;
}

static Status SvSetDefaultTablePathForLogicLabel(const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(vertex, vertexLabel);
    DmTsInfoT *dmTsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;
    DB_ASSERT(dmTsInfo->labelType == LOGIC_LABEL);  // 本函数只适用于逻辑表

    Status ret = GMERR_OK;
    char dataFileDirPath[DB_MAX_PATH] = {0};
    uint32_t shmemKey = DbConstructShmemKeyById(DbGetProcGlobalId(), DB_INNER_PERSIST_PATH_SHMEM_ID);
    if (DB_SHMEM_EXIST(shmemKey)) {
        InnerDataFileDirPathShmemDataT *innerShmem = DB_SHMEM_ATTACH(shmemKey, READONLY_PERMISSION);
        if (innerShmem == NULL) {
            goto DEFAULT;
        }
        if (DM_STR_LEN(innerShmem->buffer[0]) == 1) {
            DB_SHMEM_DETACH(innerShmem);
            goto DEFAULT;
        }
        ret = SvSetFixedPropeByName(
            vertex, "DEFAULT_TABLE_PATH", innerShmem->buffer[0], (uint32_t)DM_STR_LEN(innerShmem->buffer[0]));
        DB_SHMEM_DETACH(innerShmem);
        return ret;
    }
DEFAULT:
    ret = DbCfgGetDirPath(DbGetCfgHandle(NULL), DB_MAX_PATH, dataFileDirPath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get dataFileDirPath");
        return ret;
    }
    return SvSetFixedPropeByName(vertex, "DEFAULT_TABLE_PATH", dataFileDirPath, (uint32_t)DM_STR_LEN(dataFileDirPath));
}

static Status SvSetDefaultTablePathForMemLabel(const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(vertex, vertexLabel);
    DmTsInfoT *dmTsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;
    DB_ASSERT(dmTsInfo->labelType == MEM_LABEL);

    Status ret = SvSetFixedPropeByName(vertex, "DEFAULT_TABLE_PATH", "", 1);  // 空字符串表示内存表不存在默认表路径
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set default table path for mem label %s, id: %" PRIu32 ".", vertexLabel->metaCommon.metaName,
            vertexLabel->metaCommon.metaId);
        return ret;
    }
    return ret;
}

static Status SvSetIntervalAndIntervalUnit(const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(vertex, vertexLabel);
    DmTsInfoT *dmTsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;
    TsIntervalT interval = dmTsInfo->interval;
    // 获取表interval unit
    char *intervalUnit = NULL;
    int64_t intervalToShow = 1;  // interval默认设置，当前只有interval unit为分钟时，interval非1
    if (IsTsIntervalUnitMinute(interval) && IsIntervalMinuteValid(interval)) {
        intervalUnit = "min";
        intervalToShow = interval.microsecond / TS_MICROSECONDS_PER_MIN;
    } else if (IsIntervalOneHour(interval)) {
        intervalUnit = "hour";
    } else if (IsIntervalOneDay(interval)) {
        intervalUnit = "day";
    } else if (IsIntervalOneMonth(interval)) {
        intervalUnit = "month";
    } else if (IsIntervalOneYear(interval)) {
        intervalUnit = "year";
    } else {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PROPERTY,
            "TS interval unit. Interval: %" PRIu64 " us, %" PRIu32 " day, %" PRIu32 " month.", interval.microsecond,
            interval.day, interval.month);
        DB_ASSERT(false);
        return GMERR_INVALID_PROPERTY;
    }
    bool setOK = true;
    Status ret = SvSetFixedPropeByName(vertex, "INTERVAL_UNIT", intervalUnit, (uint32_t)DM_STR_LEN(intervalUnit));
    setOK = setOK && (ret == GMERR_OK);

    ret = DmVertexSetInt64PropeByName(vertex, "INTERVAL", intervalToShow);
    setOK = setOK && (ret == GMERR_OK);
    if (!setOK) {
        DB_LOG_ERROR(GMERR_INVALID_PROPERTY, "set TS_TBL_PROPS sysview.");
        return GMERR_INVALID_PROPERTY;
    }
    return ret;
}

static Status SvSetTtlAndTtlUnit(const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(vertex, vertexLabel);
    DmTsInfoT *dmTsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;
    TsIntervalT ttl = dmTsInfo->ttl;
    // 获取表ttl
    char *ttlUnitToShow = NULL;
    int64_t ttlToShow = 0;
    if (IsTsIntervalEmpty(ttl)) {
        ttlUnitToShow = "";
    } else if (IsTsIntervalUnitMinute(ttl)) {
        ttlUnitToShow = "min";
        ttlToShow = ttl.microsecond / TS_MICROSECONDS_PER_MIN;
    } else if (IsTsIntervalUnitHour(ttl)) {
        ttlUnitToShow = "hour";
        ttlToShow = ttl.microsecond / TS_MICROSECONDS_PER_HOUR;
    } else if (IsTsIntervalUnitDay(ttl)) {
        ttlUnitToShow = "day";
        ttlToShow = ttl.day;
    } else if (IsTsIntervalUnitMonth(ttl)) {
        ttlUnitToShow = "month";
        ttlToShow = ttl.month;
    } else if (IsTsIntervalUnitYear(ttl)) {
        ttlUnitToShow = "year";
        ttlToShow = ttl.month / TS_MONTHS_PER_YEAR;
    } else {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PROPERTY,
            "TS ttl unit. ttl: %" PRIu64 " us, %" PRIu32 " day, %" PRIu32 " month.", ttl.microsecond, ttl.day,
            ttl.month);
        DB_ASSERT(false);
        return GMERR_INVALID_PROPERTY;
    }
    bool setOK = true;
    Status ret = SvSetFixedPropeByName(vertex, "TTL_UNIT", ttlUnitToShow, (uint32_t)DM_STR_LEN(ttlUnitToShow));
    setOK = setOK && (ret == GMERR_OK);

    ret = DmVertexSetInt64PropeByName(vertex, "TTL", ttlToShow);
    setOK = setOK && (ret == GMERR_OK);
    if (!setOK) {
        DB_LOG_ERROR(GMERR_INVALID_PROPERTY, "set TTL for TS_TBL_PROPS sysview.");
        return GMERR_INVALID_PROPERTY;
    }
    return ret;
}

static Status SvSetDiskLimitForLogicLabel(const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(vertex, vertexLabel);
    DmTsInfoT *dmTsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;
    DB_ASSERT(dmTsInfo->labelType == LOGIC_LABEL);
    int64_t diskLimitToShow = dmTsInfo->diskLimit;
    return DmVertexSetInt64PropeByName(vertex, "DISK_LIMIT", diskLimitToShow);
}

static Status SvSetDiskLimitForMemLabel(const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(vertex, vertexLabel);
    DmTsInfoT *dmTsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;
    DB_ASSERT(dmTsInfo->labelType == MEM_LABEL);
    int64_t diskLimitToShow = 0;
    if (dmTsInfo->useDiskLimit) {
        uint32_t schemaMaxLen = 0;
        uint32_t schemaMinLen = 0;
        TsDmGetSchemaLength(vertexLabel->metaVertexLabel->schema, &schemaMaxLen, &schemaMinLen);
        diskLimitToShow = dmTsInfo->diskLimit * (int64_t)schemaMaxLen;  // 内存表diskLimit计算方式确保乘法不会溢出
    }
    return DmVertexSetInt64PropeByName(vertex, "DISK_LIMIT", diskLimitToShow);
}

static Status SvSetMaxSize(const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(vertex, vertexLabel);
    DmTsInfoT *dmTsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;
    return DmVertexSetUint64PropeByName(vertex, "MAX_SIZE", dmTsInfo->maxSize);
}

static Status SvSetTsTblPropsForLogicLabel(const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(vertex, vertexLabel);
    Status ret = GMERR_OK;
    ret = SvSetDefaultTablePathForLogicLabel(vertex, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set default table path for TS_TBL_PROPS sysview.");
        return ret;
    }
    ret = SvSetIntervalAndIntervalUnit(vertex, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set interval unit for TS_TBL_PROPS sysview.");
        return ret;
    }
    ret = SvSetTtlAndTtlUnit(vertex, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set ttl properties for TS_TBL_PROPS sysview.");
        return ret;
    }
    ret = SvSetDiskLimitForLogicLabel(vertex, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set disk limit for TS_TBL_PROPS sysview.");
        return ret;
    }
    ret = SvSetMaxSize(vertex, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set max size for TS_TBL_PROPS sysview.");
        return ret;
    }
    return ret;
}

static Status SvSetTsTblPropsForMemLabel(const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(vertex, vertexLabel);
    Status ret = GMERR_OK;
    ret = SvSetDefaultTablePathForMemLabel(vertex, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set default table path for TS_TBL_PROPS sysview.");
        return ret;
    }
    ret = SvSetIntervalAndIntervalUnit(vertex, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set interval unit for TS_TBL_PROPS sysview.");
        return ret;
    }
    ret = SvSetTtlAndTtlUnit(vertex, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set ttl properties for TS_TBL_PROPS sysview.");
        return ret;
    }
    ret = SvSetDiskLimitForMemLabel(vertex, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set disk limit for TS_TBL_PROPS sysview.");
        return ret;
    }
    ret = SvSetMaxSize(vertex, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set max size for TS_TBL_PROPS sysview.");
        return ret;
    }
    return ret;
}

static Status SvSetTsTblProps(const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(vertex, vertexLabel);
    // 获取表名
    uint32_t metaNameLen = (uint32_t)DM_STR_LEN(vertexLabel->metaCommon.metaName);
    Status ret = SvSetFixedPropeByName(vertex, "TABLE_NAME", vertexLabel->metaCommon.metaName, metaNameLen);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set table name for TS_TBL_PROPS sysview.");
        return ret;
    }
    DmTsInfoT *dmTsInfo = (DmTsInfoT *)vertexLabel->metaVertexLabel->extraInfo.data;
    // 获取表类型
    char *labelTypeName = NULL;
    if (dmTsInfo->labelType == LOGIC_LABEL) {
        labelTypeName = "logical";
    } else if (dmTsInfo->labelType == MEM_LABEL) {
        labelTypeName = "memory";
    } else {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PROPERTY, "TS label type.");
        DB_ASSERT(false);
        return GMERR_INVALID_PROPERTY;
    }
    bool setOK = true;
    ret = SvSetFixedPropeByName(vertex, "TABLE_TYPE", labelTypeName, (uint32_t)DM_STR_LEN(labelTypeName));
    setOK = setOK && (ret == GMERR_OK);

    // 获取表路径
    if (dmTsInfo->tablePath != NULL) {
        ret = SvSetFixedPropeByName(vertex, "TABLE_PATH", dmTsInfo->tablePath, dmTsInfo->tablePathLen);
    } else {
        ret = SvSetFixedPropeByName(vertex, "TABLE_PATH", "", 1);
    }
    setOK = setOK && (ret == GMERR_OK);
    if (!setOK) {
        DB_LOG_ERROR(GMERR_INVALID_PROPERTY, "set table path for TS_TBL_PROPS sysview.");
        return GMERR_INVALID_PROPERTY;
    }

    // 根据表类型设置其他属性
    if (dmTsInfo->labelType == LOGIC_LABEL) {
        return SvSetTsTblPropsForLogicLabel(vertex, vertexLabel);
    } else {
        return SvSetTsTblPropsForMemLabel(vertex, vertexLabel);
    }
}

Status SvQueryTsTblProps(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    Status ret = GMERR_NO_DATA;
    DmVertexT *dmVertex = (DmVertexT *)vertex;
    DbOamapIteratorT *vertexLabelIter = (uint32_t *)cursor->rowId;
    DmVertexLabelT *vertexLabel = NULL;
    uint32_t labelLatchVersionId;
    LabelRWLatchT *labelRWLatch = NULL;

    while (CataFetchMetaLabel(vertexLabelIter, CATA_VL, (void **)&vertexLabel, SvTsVertexLabelFilter,
               DbGetInstanceBySvCursor(cursor)) == GMERR_OK) {
        if (cursor->viewCond != NULL && DbStrCmp(vertexLabel->metaCommon.metaName, cursor->viewCond->str, false) != 0) {
            continue;
        }
        labelLatchVersionId = vertexLabel->commonInfo->vertexLabelLatchVersionId;
        labelRWLatch = (LabelRWLatchT *)DbShmPtrToAddr(vertexLabel->commonInfo->labelLatchShmAddr);
        if (labelRWLatch == NULL) {
            (void)CataReleaseVertexLabel(vertexLabel);
            continue;
        }
        ret = QryProceAcqRLabelLatch(labelRWLatch, labelLatchVersionId);
        if (ret == GMERR_UNDEFINED_TABLE) {
            (void)CataReleaseVertexLabel(vertexLabel);  // 1. if acquire label is dropped , scan next vertexlabel
            continue;
        }
        if (ret != GMERR_OK) {
            (void)CataReleaseVertexLabel(
                vertexLabel);  // 2. if acquire label latch failed( e.g. timeout ), return error to client
            return ret;
        }
        ret = SvSetTsTblProps(dmVertex, vertexLabel);
        ReleaseLabelAndLatch(vertexLabel, labelRWLatch);
        return ret;
    }
    return GMERR_NO_DATA;
}

#endif

#ifdef __cplusplus
}
#endif
