/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: define the core system table gm_sys_nsp in here.
 * Author: linchunbo
 * Create: 2023-8-14
 */

#include "ee_systbl_nsp_label_in.h"
#include "ee_systbl_basic_in.h"
#include "ee_systbl_heap_access_in.h"
#include "se_persist.h"
#include "ee_log.h"  // 放在最后一行

#ifdef __cplusplus
extern "C" {
#endif
#if !defined(FEATURE_STREAM) && !defined(FEATURE_TS)
Status StbInsertOneNamespace(SessionT *session, DmNamespaceT *nsp)
{
    DB_POINTER3(session, nsp, nsp->objPriv);
    DmVertexT *vertex = NULL;
    SysTableGlobalT *sysTableGlobal = DmGetSysTable(DbGetInstanceByMemCtx(session->memCtx));
    Status ret = DmCreateEmptyVertexWithMemCtx(DbGetCurMemCtx(), sysTableGlobal->stbs[STB_NSP], &vertex);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "create vertex for GM_SYS_NSP.");  // LCOV_EXCL_LINE
        DmDestroyVertex(vertex);
        return ret;
    }
    ret = MetaNspToStbVertex(nsp, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "convert namespace to vertex.");  // LCOV_EXCL_LINE
        DmDestroyVertex(vertex);
        return ret;
    }
    ret = HeapLabelInsert4Stb(session, STB_NSP, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "insert heap vertex.");  // LCOV_EXCL_LINE
        DmDestroyVertex(vertex);
        return ret;
    }
    DmDestroyVertex(vertex);
    return GMERR_OK;
}

Status StbUpdateOneNamespace(SessionT *session, DmNamespaceT *nsp)
{
    DB_POINTER2(session, nsp);
    DmVertexT *vertex = NULL;
    SysTableGlobalT *sysTableGlobal = DmGetSysTable(DbGetInstanceByMemCtx(session->memCtx));
    Status ret = DmCreateEmptyVertexWithMemCtx(DbGetCurMemCtx(), sysTableGlobal->stbs[STB_NSP], &vertex);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "create vertex for GM_SYS_NSP.");  // LCOV_EXCL_LINE
        DmDestroyVertex(vertex);
        return ret;
    }
    ret = MetaNspToStbVertex(nsp, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "convert namespace to vertex.");  // LCOV_EXCL_LINE
        DmDestroyVertex(vertex);
        return ret;
    }
    ret = HeapLabelUpdate4Stb(session->stbTrxMemCtx, session->seInstance, STB_NSP, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "update heap vertex.");  // LCOV_EXCL_LINE
        DmDestroyVertex(vertex);
        return ret;
    }
    DmDestroyVertex(vertex);
    return GMERR_OK;
}

// 第二个参数未使用，为了适配钩子定义
Status GetToDeleteVertex(DmVertexT *vertex, uint32_t vertexNo, void *userData)
{
    DB_POINTER2(vertex, userData);
    DmVertexT **toDeleteVertex = (DmVertexT **)userData;
    *toDeleteVertex = vertex;
    return GMERR_OK;
}

Status StbDeleteOneNamespaceByName(SessionT *session, const char *nspName)
{
    DB_POINTER2(session, nspName);

    DmVertexT *toDeleteVertex = NULL;

    StbScanInfoT scanInfo = {0};
    scanInfo.stbToScan = STB_NSP;
    scanInfo.indexSeqNum = 1;
    ScanKeyT scanKey = {
        .value = CreateDmValue(DB_DATATYPE_STRING, nspName, DM_STR_LEN(nspName)), .propeId = 2, .skop = SKOP_EQ};
    scanInfo.scanKeys = &scanKey;
    scanInfo.keysNum = 1;
    scanInfo.scanUserFunc = GetToDeleteVertex;
    scanInfo.userData = &toDeleteVertex;
    Status ret = IndexScan4Stb(session, &scanInfo);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "scan GM_SYS_NSP, namespace name:%s.", nspName);  // LCOV_EXCL_LINE
        DmDestroyVertex(toDeleteVertex);
        return ret;
    }
    if (scanInfo.scanCnt != 1) {
        // LCOV_EXCL_START
        DB_LOG_AND_SET_LASERR(
            GMERR_DATA_EXCEPTION, "scan count:%" PRIu32 " , related namespace name:%s.", scanInfo.scanCnt, nspName);
        // LCOV_EXCL_STOP
        DmDestroyVertex(toDeleteVertex);
        return GMERR_DATA_EXCEPTION;
    }

    ret = HeapLabelDelete4Stb(session, STB_NSP, toDeleteVertex);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "delete heap vertex.");  // LCOV_EXCL_LINE
        DmDestroyVertex(toDeleteVertex);
        return ret;
    }
    DmDestroyVertex(toDeleteVertex);
    return GMERR_OK;
}

Status BuildNamespace(DmVertexT *vertex, uint32_t vertexNo, void *userData)
{
    DB_POINTER2(vertex, userData);
    DmNamespaceT *nsp = (DmNamespaceT *)userData;
    Status ret = StbVertexToMetaNsp(vertex, nsp);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "build convert vertex to nsp");  // LCOV_EXCL_LINE
        return ret;
    }
    return ret;
}

static inline void InitNspScanInfo(StbScanInfoT *scanInfo, ScanKeyT *scanKey, DmNamespaceT *nsp)
{
    scanInfo->stbToScan = STB_NSP;
    scanInfo->indexSeqNum = 0;  // 主键索引扫描
    scanInfo->scanKeys = scanKey;
    scanInfo->keysNum = 1;
    scanInfo->scanUserFunc = BuildNamespace;
    scanInfo->userData = nsp;
}

Status CopyNamespaceForStb(DbMemCtxT *memCtx, DmNamespaceT *src, DmNamespaceT **dest)
{
    Status ret = DmCreateEmptyNamespace(memCtx, dest);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "create namespace.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = DmCopyNamespace(src, *dest);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "copy namespace.");  // LCOV_EXCL_LINE
        DmDestroyNamespace(*dest);
        *dest = NULL;
        return ret;
    }
    ret = DmCopyNamespaceObjPrivs(src, *dest);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "copy namespace objPriv.");  // LCOV_EXCL_LINE
        DmDestroyNamespace(*dest);
        *dest = NULL;
        return ret;
    }
    return ret;
}

Status StbBuildOneNamespace(DbMemCtxT *memCtx, SessionT *session, uint32_t nspId, DmNamespaceT **nsp)
{
    DB_POINTER3(memCtx, session, nsp);
    DmNamespaceT *tmpNsp = NULL;
    Status ret = AllocNamespaceForReboot(&tmpNsp);
    if (ret != GMERR_OK) {
        return ret;
    }
    ScanKeyT scanKey = {
        .value = CreateDmValue(DB_DATATYPE_UINT32, &nspId, sizeof(uint32_t)), .propeId = 0, .skop = SKOP_EQ};
    StbScanInfoT scanInfo = {0};
    InitNspScanInfo(&scanInfo, &scanKey, tmpNsp);
    ret = IndexScan4Stb(session, &scanInfo);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "scan GM_SYS_NSP, id:%" PRIu32 ".", nspId);  // LCOV_EXCL_LINE
        return ret;
    }
    if (scanInfo.scanCnt != 1) {
        // LCOV_EXCL_START
        DB_LOG_AND_SET_LASERR(
            GMERR_DATA_EXCEPTION, "scan count:%" PRIu32 "  nsp id:%" PRIu32 ".", scanInfo.scanCnt, nspId);
        // LCOV_EXCL_STOP
        return GMERR_DATA_EXCEPTION;
    }
    ret = CopyNamespaceForStb(memCtx, tmpNsp, nsp);
    return ret;
}

// 遍历依据STB_NSP_NSP_ID将每个namespace项 放到 idList链表上
Status ScanAllBuildNspList(DmVertexT *vertex, uint32_t vertexNo, void *userData)
{
    DB_POINTER2(vertex, userData);
    DbListT *nspList = userData;
    DmValueT val = {0};
    Status ret = DmVertexGetPropeByName(vertex, STB_NSP_NSP_ID, &val);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get property by name, property name:%s.", STB_NSP_NSP_ID);  // LCOV_EXCL_LINE
        return ret;
    }
    ret = DbAppendListItem(nspList, &val.value.uintValue);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "append id to id list");  // LCOV_EXCL_LINE
        return ret;
    }
    return GMERR_OK;
}

Status StbNspGetAllNsp(DbMemCtxT *memCtx, SessionT *session, DbListT *nspList)
{
    DB_POINTER3(memCtx, session, nspList);
    DbMemCtxT *curMemCtx = DbGetCurMemCtx();
    DbListT idList = {0};
    DbCreateList(&idList, sizeof(uint32_t), curMemCtx);

    DbListT *tmpNspList = NULL;
    Status ret = SysTableCreateNspList(curMemCtx, &tmpNspList);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "create tmp nsp list");  // LCOV_EXCL_LINE
        return ret;
    }

    StbScanInfoT scanInfo = {0};
    scanInfo.stbToScan = STB_NSP;
    scanInfo.scanUserFunc = ScanAllBuildNspList;
    scanInfo.userData = &idList;
    ret = SeqScan4Stb(session, &scanInfo);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "scan GM_SYS_NSP");  // LCOV_EXCL_LINE
        goto EXIT;
    }

    uint32_t cnt = DbListGetItemCnt(&idList);
    uint32_t id = 0;
    DmNamespaceT *namespace = NULL;
    // 构建临时nsp list, 挂载在 curMemCtx上，动态内存申请，
    for (uint32_t i = 0; i < cnt; ++i) {
        id = *(uint32_t *)DbListItem(&idList, i);
        ret = StbBuildOneNamespace(curMemCtx, session, id, &namespace);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "build one nsp, id:%" PRIu32 ".", id);  // LCOV_EXCL_LINE
            return ret;
        }
        ret = DbAppendListItem(tmpNspList, &namespace);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_AND_SET_LASERR(ret, "append nsp to its list");  // LCOV_EXCL_LINE
            return ret;
        }
    }
    // 将 curMemCtx上的tmpNspList赋值到 memCtx 上的nspList
    ret = SysTableCopyNspList(tmpNspList, memCtx, nspList);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "copy namespace list");  // LCOV_EXCL_LINE
        goto EXIT;
    }

EXIT:
    SysTableDestroyNspList(tmpNspList);
    return ret;
}

Status SysTableInsertOneNamespaceImpl(SessionT *session, DmNamespaceT *nsp)
{
    DB_POINTER2(session, nsp);
    DbMemCtxT *oldMemCtx = DbMemCtxSwitchTo(session->stbTrxMemCtx);
    Status ret = StbInsertOneNamespace(session, nsp);
    if (ret == GMERR_UNIQUE_VIOLATION) {
        ret = GMERR_DUPLICATE_OBJECT;
    }
    (void)DbMemCtxSwitchBack(oldMemCtx, session->stbTrxMemCtx);
    return ret;
}

Status SysTableDeleteOneNamespaceByNameImpl(SessionT *session, const char *nspName)
{
    DB_POINTER2(session, nspName);
    DbMemCtxT *oldMemCtx = DbMemCtxSwitchTo(session->stbTrxMemCtx);
    Status ret = StbDeleteOneNamespaceByName(session, nspName);
    (void)DbMemCtxSwitchBack(oldMemCtx, session->stbTrxMemCtx);
    return ret;
}

// 传入的memctx仅用于构建nspList，运行过程中的内存分配均有g_systable.stbMemCtx来分配
Status SysTableNspGetAllNspImpl(DbMemCtxT *memCtx, SessionT *session, DbListT *nspList)
{
    DB_POINTER3(memCtx, session, nspList);
    DbMemCtxT *oldMemCtx = DbMemCtxSwitchTo(session->stbTrxMemCtx);
    Status ret = StbNspGetAllNsp(memCtx, session, nspList);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get all nsp to list");  // LCOV_EXCL_LINE
        (void)DbMemCtxSwitchBack(oldMemCtx, session->stbTrxMemCtx);
        return ret;
    }
    (void)DbMemCtxSwitchBack(oldMemCtx, session->stbTrxMemCtx);
    return ret;
}

Status SysTableUpdateOneNamespaceImpl(SessionT *session, DmNamespaceT *nsp)
{
    DB_POINTER2(session, nsp);
    DbMemCtxT *oldMemCtx = DbMemCtxSwitchTo(session->stbTrxMemCtx);
    Status ret = StbUpdateOneNamespace(session, nsp);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "update one nsp in nsp systb");  // LCOV_EXCL_LINE
        (void)DbMemCtxSwitchBack(oldMemCtx, session->stbTrxMemCtx);
        return ret;
    }
    (void)DbMemCtxSwitchBack(oldMemCtx, session->stbTrxMemCtx);
    return ret;
}
#endif
#ifdef __cplusplus
}
#endif
