/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: header file for time-series mapping of physical and logical label system table
 * Operations on time-series map system table, including insert, scan etc.
 * Author: tangjunnong
 * Create: 2023-07-21
 */

#ifndef DM_SYSTBL_TS_MAP_IN_H
#define DM_SYSTBL_TS_MAP_IN_H

#include "dm_data_prop_seri.h"
#include "dm_meta_prop_strudefs.h"
#include "ee_systbl_basic_in.h"
#include "dm_cu_physical_label.h"
#include "dm_systbl_ts_map_def.h"
#include "dm_data_ts.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct LogicIdWithMetaList {
    uint32_t logicLabelId;
    DbListT *metaList;
} LogicIdWithMetaListT;

/**
 * @brief 将一条数据插入time-series map系统表
 * @param physicalLabel [in]: 要插入记录的相对应的时序物理表
 * @param upperBound [in]: 对应物理表的分区上界
 * @param logicalLabelId [in]: 对应逻辑表的id
 * @param intervalType [in]: 对应逻辑表的时间间隔类型
 * @return Status 成功或错误码
 */
Status StbTsMapInsert(
    SessionT *session, DmVertexLabelT *physicalLabel, int64_t upperBound, uint32_t logicalLabelId, uint8_t labelMode);

/**
 * @brief: 从time-series map系统表中根据二级索引扫描记录，根据指定的上下界
 * key，获取所有可能包含这些值的物理表id，用list的形式返回
 * @param session [in]: 系统表事务session
 * @param lowerKey [in]: 扫描上界key
 * @param upperKey [in]: 扫描下界key
 * @param phyIdList [in/out]: 存储返回结果物理表id的list
 * @return: success或错误码
 */
Status StbTsMapGetIdListByKeyRange(SessionT *session, TsMapKeyT *lowerKey, TsMapKeyT *upperKey, DbListT *phyIdList);

/**
 * @brief: 从time-series map系统表中根据物理表id，构造并返回一张物理表vertexLabel
 * @param physicalId [in]: 物理表id
 * @param physicalLabel [in/out]: 存储返回结果物理表，需要外部先分配此vertexLabel结构体本身的空间
 * @return: success或错误码
 */
Status StbTsMapGetLabelByPhysicalId(uint32_t physicalId, DmVertexLabelT *physicalLabel);

/**
 * @brief: 从time-series map系统表中删除
 * @param physicalId [in]: 物理表id
 * @return: success或错误码
 */
Status StbTsMapDeleteById(uint32_t physicalId);

SO_EXPORT Status SystableTsMapInsertImpl(
    SessionT *session, DmVertexLabelT *physicalLabel, int64_t upperBound, uint32_t logicalLabelId, uint8_t labelMode);
SO_EXPORT Status SystableTsMapGetIdListByKeyRangeImpl(
    SessionT *session, TsMapKeyT *lowerKey, TsMapKeyT *upperKey, DbListT *phyIdList);
SO_EXPORT Status SystableTsMapGetIdListByLogicalIdImpl(SessionT *session, uint32_t logicalId, DbListT *phyIdList);
SO_EXPORT Status SystableTsMapAcquireWLatchImpl(void);
SO_EXPORT Status SystableTsMapReleaseWLatchImpl(void);
SO_EXPORT Status SystableTsMapInsertLogicalLabelImpl(SessionT *session, uint32_t labelId, DmTsInfoT *tsInfo);
SO_EXPORT Status SystableTsMapUpdateLogicalLabelImpl(SessionT *session, uint32_t labelId, DmTsInfoT *tsInfo);
SO_EXPORT Status SystableTsMapMarkLabelDeletedByIdImpl(SessionT *session, uint32_t labelId);
SO_EXPORT Status SystableTsMapGetLogicalIdListImpl(SessionT *session, DbListT *logicalIdList, bool isDeleted);
SO_EXPORT Status SystableTsMapDeleteByPhysicalIdImpl(SessionT *session, uint32_t labelId);
SO_EXPORT Status SystableTsMapGetAllPhysicalLabelMetaImpl(SessionT *session, DbListT *metaList);
SO_EXPORT Status SystableTsMapGetPhysicalLabelMetaByLogicLabelIdImpl(
    SessionT *session, uint32_t logicLabelId, DbListT *metaList);
SO_EXPORT Status SystableTsMapGetPhysicalLabelMetaByIdImpl(
    SessionT *session, uint32_t physicalId, PhysicalLabelMetaT *meta);
SO_EXPORT Status SystableTsMapGetLabelListByKeyRangeImpl(
    SessionT *session, TsMapKeyT *lowerKey, TsMapKeyT *upperKey, DbListT *labelList);
SO_EXPORT Status SystableTsMapGetPhysicalLabelByIdImpl(
    SessionT *session, DbMemCtxT *memCtx, uint32_t labelId, DmVertexLabelT **physicalLabel);
SO_EXPORT Status SystableTsMapInitImpl(DbMemCtxT *tsMapMemCtx, DmVertexLabelT *templateLabel);
SO_EXPORT Status SystableReleaseAllHeapMemImpl(void);
SO_EXPORT DmVertexLabelT *SystableGetPhysicalLabelTemplateImpl(void);

#ifdef __cplusplus
}
#endif
#endif  // DM_SYSTBL_TS_MAP_IN_H
