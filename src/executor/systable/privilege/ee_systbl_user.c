/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: source file for user system table
 * Author:
 * Create: 2024-7
 */

#include "dm_systbl_user_convert.h"
#include "ee_systbl_user_in.h"
#include "ee_systbl_heap_access_in.h"
#include "ee_systbl_basic_in.h"
#include "se_persist.h"
#include "ee_log.h"  // 放在最后一行

#ifdef __cplusplus
extern "C" {
#endif

Status StbInsertOneUser(SessionT *session, CataUserT *user)
{
    DB_POINTER2(session, user);
    DmVertexT *vertex = NULL;
    SysTableGlobalT *sysTableGlobal = DmGetSysTable(DbGetInstanceByMemCtx(session->memCtx));
    Status ret = DmCreateEmptyVertexWithMemCtx(DbGetCurMemCtx(), sysTableGlobal->stbs[STB_USER], &vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "create vertex for GM_SYS_USER.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = MetaUserToStbVertex(user, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "convert user to vertex.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = HeapLabelInsert4Stb(session, STB_USER, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "insert heap vertex.");  // LCOV_EXCL_LINE
        return ret;
    }
    DmDestroyVertex(vertex);
    return GMERR_OK;
}

Status SysTableInsertOneUserImpl(SessionT *session, CataUserT *user)
{
    DB_POINTER2(session, user);
    DbMemCtxT *oldMemCtx = DbMemCtxSwitchTo(session->stbTrxMemCtx);
    Status ret = StbInsertOneUser(session, user);
    if (ret == GMERR_UNIQUE_VIOLATION) {
        ret = GMERR_DUPLICATE_OBJECT;
    }
    (void)DbMemCtxSwitchBack(oldMemCtx, session->stbTrxMemCtx);
    return ret;
}

static Status GetToDeleteVertex(DmVertexT *vertex, uint32_t vertexNo, void *userData)
{
    DB_POINTER2(vertex, userData);
    DB_UNUSED(vertexNo);
    DmVertexT **toDeleteVertex = (DmVertexT **)userData;
    *toDeleteVertex = vertex;
    return GMERR_OK;
}

Status StbDeleteOneUserById(SessionT *session, uint32_t userId)
{
    DmVertexT *toDeleteVertex = NULL;

    StbScanInfoT scanInfo = {0};
    scanInfo.stbToScan = STB_USER;
    scanInfo.indexSeqNum = 0;
    ScanKeyT scanKey = {
        .value = CreateDmValue(DB_DATATYPE_UINT32, &userId, sizeof(userId)), .propeId = 0, .skop = SKOP_EQ};
    scanInfo.scanKeys = &scanKey;
    scanInfo.keysNum = 1;
    scanInfo.scanUserFunc = GetToDeleteVertex;
    scanInfo.userData = &toDeleteVertex;
    Status ret = IndexScan4Stb(session, &scanInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "scan GM_STB_USER, user id: %" PRIu32 ".", userId);  // LCOV_EXCL_LINE
        return ret;
    }
    if (scanInfo.scanCnt != 1) {
        // LCOV_EXCL_START
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "unexpected scan count:%" PRIu32 " , user id: %" PRIu32 ".",
            scanInfo.scanCnt, userId);
        // LCOV_EXCL_STOP
        return GMERR_DATA_EXCEPTION;
    }

    ret = HeapLabelDelete4Stb(session, STB_USER, toDeleteVertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "delete heap vertex.");  // LCOV_EXCL_LINE
        return ret;
    }
    return GMERR_OK;
}

Status SysTableDeleteOneUserByIdImpl(SessionT *session, uint32_t userId)
{
    DB_POINTER(session);
    DbMemCtxT *oldMemCtx = DbMemCtxSwitchTo(session->stbTrxMemCtx);
    Status ret = StbDeleteOneUserById(session, userId);
    (void)DbMemCtxSwitchBack(oldMemCtx, session->stbTrxMemCtx);
    return ret;
}

Status ScanAllBuildUserList(DmVertexT *vertex, uint32_t vertexNo, void *userData)
{
    DB_POINTER2(vertex, userData);
    DbListT *userList = userData;
    CataUserT *user = NULL;
    Status ret = CataCreateEmptyUserWithMemCtx(DbGetCurMemCtx(), &user);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "create user");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = StbVertexToMetaUser(vertex, user);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "convert vertex");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = DbAppendListItem(userList, &user);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "append user to user list");  // LCOV_EXCL_LINE
        return ret;
    }
    return GMERR_OK;
}

Status StbUserGetAllUser(DbMemCtxT *memCtx, SessionT *session, DbListT *userList)
{
    DB_POINTER3(memCtx, session, userList);
    DbListT *tmpUserList = NULL;
    Status ret = SysTableCreateUserList(DbGetCurMemCtx(), &tmpUserList);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "create tmp user list");  // LCOV_EXCL_LINE
        return ret;
    }

    StbScanInfoT scanInfo = {0};
    scanInfo.stbToScan = STB_USER;
    scanInfo.scanUserFunc = ScanAllBuildUserList;
    scanInfo.userData = tmpUserList;
    ret = SeqScan4Stb(session, &scanInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "scan GM_SYS_USER");  // LCOV_EXCL_LINE
        goto EXIT;
    }

    ret = SysTableCopyUserList(tmpUserList, memCtx, userList);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "copy user list");  // LCOV_EXCL_LINE
        goto EXIT;
    }

EXIT:
    SysTableDestroyUserList(tmpUserList);
    return ret;
}

// 传入的memctx仅用于构建userList，运行过程中的内存分配均由g_systable.stbMemCtx来分配
Status SysTableGetAllUserImpl(DbMemCtxT *memCtx, SessionT *session, DbListT *userList)
{
    DB_POINTER3(memCtx, session, userList);
    DbMemCtxT *oldMemCtx = DbMemCtxSwitchTo(session->stbTrxMemCtx);
    Status ret = StbUserGetAllUser(memCtx, session, userList);
    (void)DbMemCtxSwitchBack(oldMemCtx, session->stbTrxMemCtx);
    return ret;
}

#ifdef __cplusplus
}
#endif
