/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: GMDBv5 EE Team
 * Create: 2023-4-17
 */

#ifndef EE_UTIL_H
#define EE_UTIL_H

#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif

#define DTL_EXTERNAL_PREFIX "fake_"

/*
 * description: 由伪外部表的表名获取真正的外部表
 * param {const char} *fakeExternTableName: 伪外部表的表名
 * param {size_t} externTableNameMaxLen: 外部表的表名最大长度
 * param {char} *externTableName: 外部表的表名
 * return {*}
 */
Status GetExternTableNameWithFakeName(
    const char *fakeExternTableName, size_t externTableNameMaxLen, char *externTableName);

#ifdef __cplusplus
}
#endif

#endif  // EE_UTIL_H
