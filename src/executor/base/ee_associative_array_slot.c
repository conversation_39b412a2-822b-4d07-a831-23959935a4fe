/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: associative array slot implementation.
 * Note1: memCtx is used in short process period , we will free all memory when finishing one request
 * Author: GMDBv5 EE Team
 * Create: 2022-10-10
 */

#include "ee_associative_array_slot.h"
#ifndef NDEBUG
#include "db_utils.h"
#include "dm_data_print.h"
#endif
#include <float.h>
#include "dm_meta_udf.h"
#include "dm_data_pattern.h"
#include "dm_data_math.h"
#include "dm_data_record.h"
#include "se_common.h"
#include "ee_reserved_names.h"
#ifdef FEATURE_GQL
#include "dm_data_record.h"
#include "db_memcpy.h"
#endif
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

static inline void IntValueSign(DmValueT *count)
{
    if (count->value.intValue == 0) {
        return;
    }
    count->value.intValue = count->value.intValue > 0 ? 1 : -1;
}

static inline void IntValueNegative(DmValueT *count)
{
    count->value.intValue = -count->value.intValue;
}

Status NewAASlot(DbMemCtxT *memCtx, DmVertexDescT *desc, PlanPropDescT *propDesc, AASlotT **slot)
{
    DB_POINTER2(memCtx, slot);
    // memory will be freed unitied, see Note1
    AASlotT *newSlot = DbDynMemCtxAlloc(memCtx, sizeof(AASlotT));
    if (newSlot == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "AASlotT is null");
        return GMERR_OUT_OF_MEMORY;
    }
    *newSlot = (AASlotT){0};
    newSlot->memCtx = memCtx;
    Status ret = GMERR_OK;
    if (desc != NULL) {
#ifdef FEATURE_SQL
        newSlot->distanceToQuery = DBL_MAX;
        // 接口成对使用，因此显示初始化一下
        TupleBufInit(&newSlot->oldTupleBuf, memCtx);
#endif
        ret = NewAASlotVertex(memCtx, desc, &newSlot->dmVertex);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "new DmVertex");
            return ret;
        }
        newSlot->slotType = AA_SLOT_DM_VERTEX;
    } else {
        ret = NewAASlotArray(memCtx, propDesc, &newSlot->slotArray);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "create AASlotArray");
            return ret;
        }
        newSlot->slotType = AA_SLOT_ARRAY;
    }
    // memory will be freed unitied, see Note1
    *slot = newSlot;
    return GMERR_OK;
}

static Status AASlotCopyArrayFromDmVertex(AASlotT *to, AASlotT *from)
{
    DB_ASSERT(to->slotType == AA_SLOT_ARRAY);
    DB_ASSERT(from->slotType == AA_SLOT_DM_VERTEX);
    DmValueT value = {0};
    DmVertexT *fromVertex = from->dmVertex;
    DmValueT *toArray = to->slotArray.array;
    uint32_t slotLen = to->slotArray.len;
    for (uint32_t i = 0; i < slotLen; ++i) {
        Status ret = DmVertexGetPropeByIdNoCopy(fromVertex, i, &value);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        ret = DmValueCopyNoAlloc(&value, &toArray[i]);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    return GMERR_OK;
}

SO_EXPORT Status AASlotCopy(AASlotT *to, AASlotT *from)
{
    DB_POINTER2(to, from);
    to->addr = from->addr;
#ifdef FEATURE_SQL
    to->oldTupleBuf = from->oldTupleBuf;
#endif
    if (to->slotType == AA_SLOT_ARRAY && from->slotType == AA_SLOT_ARRAY) {
        return AASlotArrayCopy(to, from);
    } else if ((to->slotType == AA_SLOT_DM_VERTEX || to->slotType == AA_SLOT_RETURNING) &&
               (from->slotType == AA_SLOT_DM_VERTEX || from->slotType == AA_SLOT_RETURNING)) {
        return DmVertexCopy(from->dmVertex, to->dmVertex);
    } else if (to->slotType == AA_SLOT_ARRAY && from->slotType == AA_SLOT_DM_VERTEX) {
        return AASlotCopyArrayFromDmVertex(to, from);
    } else {
        return AASlotVertexCopyFromArray(to, from);
    }
}

static Status AASlotArrayCmpAllFields(AASlotT *slot, AASlotT *other, bool includingDtlReservedCount, bool *equal)
{
    DmValueT *fromArray = slot->slotArray.array;
    DmValueT *toArray = other->slotArray.array;
    uint32_t len = other->slotArray.len;
    uint32_t i = includingDtlReservedCount ? 0 : 1;
    for (; i < len; ++i) {
        if (!DmValueIsEqual(&fromArray[i], &toArray[i])) {
            *equal = false;
            return GMERR_OK;
        }
    }
    *equal = true;
    return GMERR_OK;
}

static Status AASlotArrayCmpAllFieldsWithVertex(
    AASlotT *slot, AASlotT *other, bool includingDtlReservedCount, bool *equal)
{
    DmValueT *fromArray = slot->slotArray.array;
    DmValueT value = {0};
    uint32_t len = slot->slotArray.len;
    uint32_t i = includingDtlReservedCount ? 0 : 1;
    for (; i < len; ++i) {
        Status ret = AASlotGetPrope(other, i, &value);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_WARN(ret, "get property from vertex when compare AASlotArray, index=%" PRIu32, i);
            return ret;
        }
        if (!DmValueIsEqual(&fromArray[i], &value)) {
            *equal = false;
            return GMERR_OK;
        }
    }
    *equal = true;
    return GMERR_OK;
}

Status DeepCopyAASlot(DbMemCtxT *memCtx, AASlotT *src, AASlotT **dst)
{
    DB_POINTER3(memCtx, src, dst);
    Status ret = NewAASlot(memCtx, src->dmVertex->vertexDesc, NULL, dst);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return AASlotCopy(*dst, src);
}

#ifdef FEATURE_GQL
Status DmInitVertex4Batch(DbMemCtxT *memCtx, DmVertexT *srcVertex, uint8_t **dstVertex)
{
    DB_POINTER3(memCtx, srcVertex, dstVertex);
    uint8_t **cursor = dstVertex;
    uint32_t vertexMemSize = srcVertex->vertexDesc->memSizePreMalloc;
    DmVertexT *tmpVertex = (DmVertexT *)(void *)*cursor;
    *cursor += vertexMemSize;
    uint32_t recordMemSize = srcVertex->record->recordDesc->memSizePreMalloc;
    DmRecordT *tmpRecord = (DmRecordT *)(void *)*cursor;
    *cursor += recordMemSize;
    // 正确赋值指针成员
    DbFastMemcpy((uint8_t *)tmpVertex, vertexMemSize, (uint8_t *)srcVertex, vertexMemSize);
    tmpVertex->memCtx = memCtx;
    uint32_t offset = (uint32_t)((uint8_t *)srcVertex->compareKeyBuf - (uint8_t *)srcVertex);
    tmpVertex->compareKeyBuf = (uint8_t *)tmpVertex + offset;
    offset = (uint32_t)((uint8_t *)srcVertex->vertexKeyBuf - (uint8_t *)srcVertex);
    tmpVertex->vertexKeyBuf = (uint8_t *)tmpVertex + offset;
    // copy record
    DbFastMemcpy((uint8_t *)tmpRecord, recordMemSize, (uint8_t *)srcVertex->record, recordMemSize);
    tmpVertex->record = tmpRecord;
    offset = (uint32_t)((uint8_t *)srcVertex->record->runningBuf - (uint8_t *)srcVertex->record);
    tmpVertex->record->runningBuf = (uint8_t *)tmpRecord + offset;

    return GMERR_OK;
}

Status NewAASlotFromBuf4Batch(DbMemCtxT *memCtx, AASlotT *from, uint8_t **to)
{
    DB_POINTER3(memCtx, from, to);
    uint8_t **cursor = to;
    // AASlot
    AASlotT *aaSlot = (AASlotT *)(void *)*to;
    *cursor += sizeof(AASlotT);
    // AASlot.dmVertex
    DmVertexT *vertex = (DmVertexT *)(void *)*cursor;
    Status ret = DmInitVertex4Batch(memCtx, from->dmVertex, cursor);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    aaSlot->dmVertex = vertex;
    aaSlot->addr = from->addr;
    aaSlot->oldTupleBuf = from->oldTupleBuf;
    return DmDeSerialize2ExistsVertex(from->oldTupleBuf.buf, from->oldTupleBuf.bufSize, vertex, false);
}

void DestroyAASlot(DbMemCtxT *memCtx, AASlotT **slot)
{
    DB_POINTER(slot);
    if (*slot != NULL) {
        DbDynMemCtxFree(memCtx, (*slot)->dmVertex);
        DbDynMemCtxFree(memCtx, *slot);
        *slot = NULL;
    }
}
#endif

Status AASlotEqual(AASlotT *slot, AASlotT *other, bool includingDtlReservedCount, bool *equal)
{
    DB_POINTER3(slot, other, equal);
    Status ret = GMERR_OK;
    uint32_t slotPropNum = AASlotGetPropNum(slot);
    uint32_t otherPropNum = AASlotGetPropNum(other);
    if (slotPropNum != otherPropNum) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR,
            "two slots feild num not equal; slotPropNum=%" PRIu32 ",otherPropNum=%" PRIu32, slotPropNum, otherPropNum);
        *equal = false;
        return ret;
    }

    if (slot->slotType == AA_SLOT_DM_VERTEX && other->slotType == AA_SLOT_DM_VERTEX) {
        return DmVertexEqual(slot->dmVertex, other->dmVertex, includingDtlReservedCount, equal);
    } else if (slot->slotType == AA_SLOT_ARRAY && other->slotType == AA_SLOT_ARRAY) {
        return AASlotArrayCmpAllFields(slot, other, includingDtlReservedCount, equal);
    } else if (slot->slotType == AA_SLOT_ARRAY && other->slotType == AA_SLOT_DM_VERTEX) {
        // slot is array, other is vertex
        return AASlotArrayCmpAllFieldsWithVertex(slot, other, includingDtlReservedCount, equal);
    }
    // slot is vertex, other is array
    return AASlotArrayCmpAllFieldsWithVertex(other, slot, includingDtlReservedCount, equal);
}

#if defined(FEATURE_DATALOG)
Status AASlot2UserDefStructWithAlloc(AASlotT *slot, DbMemCtxT *memCtx, void **pBuf)
{
    DB_POINTER3(memCtx, slot, pBuf);

    // get user define struct length
    uint32_t structSize = 0;
    uint32_t bufSize = 0;
    Status ret = AASlotGetUserDefStruLen(slot, &structSize, &bufSize);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get struct len");
        return ret;
    }

    // malloc space for holding user define struct, which will be freed unitied, see Note1
    void *buf = DbDynMemCtxAlloc(memCtx, bufSize);
    if (buf == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "buf is null, bufSize: %" PRIu32 ".", bufSize);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(buf, bufSize, 0x00, bufSize);

    // fill fields to user define struct
    ret = AASlotToUserDefStru(slot, bufSize, buf);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "convert vertex to struct, bufSize: %" PRIu32 ".", bufSize);
        return ret;
    }

    *pBuf = buf;
    return GMERR_OK;
}

Status AASlotGetUserDefStruLen(AASlotT *slot, uint32_t *userDefStruLen, uint32_t *userDefStruBufferLen)
{
    DB_POINTER3(slot, userDefStruLen, userDefStruBufferLen);
    if (slot->slotType == AA_SLOT_DM_VERTEX) {
        return AASlotVertexGetUserDefStruLen(slot, userDefStruLen, userDefStruBufferLen);
    }
    return AASlotArrayGetUserDefStruLen(slot, userDefStruLen, userDefStruBufferLen);
}

Status AASlotFromUserDefStru(AASlotT *slot, const uint32_t userDefStruLen, uint8_t *userDefStru)
{
    DB_POINTER2(slot, userDefStru);
    if (slot->slotType == AA_SLOT_DM_VERTEX) {
        return AASlotVertexFromUserDefStru(userDefStru, userDefStruLen, slot);
    }
    return AASlotArrayFromUserDefStru(userDefStru, userDefStruLen, slot);
}

Status AASlotToUserDefStru(AASlotT *slot, uint32_t userDefStruLen, uint8_t *userDefStru)
{
    DB_POINTER(slot);
    if (slot->slotType == AA_SLOT_DM_VERTEX) {
        return AASlotVertexToUserDefStru(slot, userDefStruLen, userDefStru);
    }
    return AASlotArrayToUserDefStru(slot, userDefStruLen, userDefStru);
}
#endif

bool AASlotHasNullProperty(AASlotT *slot)
{
    DB_POINTER(slot);
    if (slot->slotType == AA_SLOT_DM_VERTEX) {
        return AASlotVertexHasNullProperty(slot);
    } else {
        return AASlotArrayHasNullProperty(slot);
    }
}

Status AASlotConvertToExternAASlot(AASlotT *srcSlot, AASlotT *externSlot)
{
    DB_POINTER2(srcSlot, externSlot);
    DB_ASSERT(srcSlot->slotType == AA_SLOT_DM_VERTEX);
    DB_ASSERT(externSlot->slotType == AA_SLOT_DM_VERTEX);
    uint32_t slotPropNum = DmVertexGetPropNum(srcSlot->dmVertex, false);
    uint32_t usrDefStartIndex = DTL_BUSINESS_FIELD_INDEX;
    for (uint32_t i = usrDefStartIndex; i < slotPropNum; i++) {
        /*
         * datalog tuple's fields as follows : dtlReservedCount | upgradeVersion | field0 | field1 |...|fieldn
         * user defined fields begin at index = 2
         */
        uint32_t srcIndex = i;
        uint32_t externIndex = i - DTL_BUSINESS_FIELD_INDEX;

        DmValueT dmValue = {0};
        Status ret = AASlotGetPrope(srcSlot, srcIndex, &dmValue);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }

        // if datatype of one field in externSlot is uint,
        // datatype of corresponding field in srcSlot should be converted to uint
        DbDataTypeE dataType = DB_DATATYPE_NULL;
        ret = DmVertexGetPropTypeById(externSlot->dmVertex, externIndex, &dataType);
        if (ret != GMERR_OK || dataType == DB_DATATYPE_NULL) {
            return ret;
        }
        switch (dataType) {
            case DB_DATATYPE_UINT8: {
                dmValue.type = DB_DATATYPE_UINT8;
                break;
            }
            case DB_DATATYPE_UINT16: {
                dmValue.type = DB_DATATYPE_UINT16;
                break;
            }
            case DB_DATATYPE_UINT32: {
                dmValue.type = DB_DATATYPE_UINT32;
                break;
            }
            case DB_DATATYPE_UINT64: {
                dmValue.type = DB_DATATYPE_UINT64;
                break;
            }
            default:
                break;
        }

        ret = AASlotSetPrope(externSlot, externIndex, dmValue);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status AASlotGetAndAddCountField(
    AASlotT *slot1, AASlotT *slot2, DmValueT *count1, DmValueT *count2, DmValueT *totalCount)
{
    DB_POINTER5(slot1, slot2, count1, count2, totalCount);
    Status ret = GMERR_INTERNAL_ERROR;
    if ((ret = AASlotGetDtlReservedCount(slot1, count1)) != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get slot1 dtlReservedCount");
        return ret;
    }
    if ((ret = AASlotGetDtlReservedCount(slot2, count2)) != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get slot2 dtlReservedCount");
        return ret;
    }

    if ((ret = DmValueAdd(count1, count2, totalCount)) != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "add count field; count1=%" PRIi32 ", count2=%" PRIi32,
            count1->value.intValue, count2->value.intValue);
        return ret;
    }
    return GMERR_OK;
}

Status AASlotAddCountField(AASlotT *slot1, AASlotT *slot2, DmValueT *totalCount)
{
    DB_POINTER3(slot1, slot2, totalCount);
    DmValueT dummyCount1 = {0}, dummyCount2 = {0};
    return AASlotGetAndAddCountField(slot1, slot2, &dummyCount1, &dummyCount2, totalCount);
}

Status AASlotNegativeDtlReservedCount(AASlotT *tuple)
{
    DB_POINTER(tuple);
    Status ret = GMERR_OK;
    DmValueT dtlReservedCount = {};
    if ((ret = AASlotGetDtlReservedCount(tuple, &dtlReservedCount)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "get dtlReservedCount");
        return ret;
    }

    DmValueT negative = {0};
    negative.type = DB_DATATYPE_INT32;
    negative.value.intValue = -1;
    if ((ret = DmValueMulti(&negative, &dtlReservedCount, &dtlReservedCount)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "multiply dtlReservedCount with -1");
        return ret;
    }

    if ((ret = AASlotSetDtlReservedCount(tuple, dtlReservedCount)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "set dtlReservedCount multiply -1 to tuple");
        return ret;
    }
    return GMERR_OK;
}

Status AASlotMultiDtlCount(AASlotT *left, AASlotT *right, DmValueT *resultCount)
{
    DmValueT rightCount = {0};
    DmValueT leftCount = {0};
    Status ret = AASlotGetDtlReservedCount(left, &leftCount);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = AASlotGetDtlReservedCount(right, &rightCount);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    IntValueSign(&leftCount);
    IntValueSign(&rightCount);
    ret = DmValueMulti(&leftCount, &rightCount, resultCount);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    IntValueSign(resultCount);
    return GMERR_OK;
}

Status AASlotSignAndSetDtlCount(AASlotT *to, AASlotT *from)
{
    DmValueT count = {0};
    Status ret = AASlotGetDtlReservedCount(from, &count);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    IntValueSign(&count);
    ret = AASlotSetDtlReservedCount(to, count);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return GMERR_OK;
}

Status AASlotMultiAndSetDtlCount(AASlotT *left, AASlotT *right, AASlotT *result)
{
    DmValueT resultCount = {0};
    Status ret = AASlotMultiDtlCount(left, right, &resultCount);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = AASlotSetDtlReservedCount(result, resultCount);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return GMERR_OK;
}

Status AASlotNegativeMultiAndSetDtlCount(AASlotT *left, AASlotT *right, AASlotT *result)
{
    DmValueT resultCount = {0};
    Status ret = AASlotMultiDtlCount(left, right, &resultCount);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    IntValueNegative(&resultCount);
    ret = AASlotSetDtlReservedCount(result, resultCount);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return GMERR_OK;
}

static Status AASlotArrayProjectProperty(AASlotT *from, AASlotT *to, DmPairT *pair, uint32_t pairlen)
{
    DmValueT *fromArray = from->slotArray.array;
    DmValueT *toArray = to->slotArray.array;
    Status ret;
    for (uint32_t i = 0; i < pairlen; ++i) {
        uint32_t fromId = pair[i].from;
        uint32_t toId = pair[i].to;
        ret = DmValueCopyNoAlloc(&fromArray[fromId], &toArray[toId]);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status AASlotArrayProjectToVertex(AASlotT *from, AASlotT *to, DmPairT *pair, uint32_t pairlen)
{
    DmValueT *fromArray = from->slotArray.array;
    Status ret;
    for (uint32_t i = 0; i < pairlen; ++i) {
        uint32_t fromId = pair[i].from;
        uint32_t toId = pair[i].to;
        ret = AASlotSetPrope(to, toId, fromArray[fromId]);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status AASlotProjectProperty(AASlotT *from, AASlotT *to, DmPairT *pair, uint32_t len)
{
    DB_POINTER2(from, to);
    if (from->slotType == AA_SLOT_DM_VERTEX && to->slotType == AA_SLOT_DM_VERTEX) {
        return DmProjectProperty(from->dmVertex, to->dmVertex, pair, len);
    } else if (from->slotType == AA_SLOT_DM_VERTEX && to->slotType == AA_SLOT_ARRAY) {
        return DmProjectPropertyToArray(from->dmVertex, to->slotArray.array, to->slotArray.len, pair, len);
    } else if (from->slotType == AA_SLOT_ARRAY && to->slotType == AA_SLOT_ARRAY) {
        return AASlotArrayProjectProperty(from, to, pair, len);
    }
    return AASlotArrayProjectToVertex(from, to, pair, len);
}

static bool AASlotArrayProjectIsSame(AASlotT *from, AASlotT *to, DmPairT *pair, uint32_t pairlen)
{
    DmValueT *fromArray = from->slotArray.array;
    DmValueT *toArray = to->slotArray.array;
    for (uint32_t i = 0; i < pairlen; ++i) {
        if (!DmValueIsEqual(&fromArray[pair[i].from], &toArray[pair[i].to])) {
            return false;
        }
    }
    return true;
}

static bool AASlotArrayIsSameWithVertex(AASlotT *from, AASlotT *to, DmPairT *pair, uint32_t pairlen)
{
    DmValueT *fromArray = from->slotArray.array;
    DmValueT value = {0};
    for (uint32_t i = 0; i < pairlen; ++i) {
        Status ret = AASlotGetPrope(to, pair[i].to, &value);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_ASSERT(false);
        }
        if (!DmValueIsEqual(&fromArray[pair[i].from], &value)) {
            return false;
        }
    }
    return true;
}

static bool AASlotVertexIsSameWithArray(AASlotT *from, AASlotT *to, DmPairT *pair, uint32_t pairlen)
{
    DmValueT *toArray = to->slotArray.array;
    DmValueT value = {0};
    for (uint32_t i = 0; i < pairlen; ++i) {
        Status ret = AASlotGetPrope(from, pair[i].from, &value);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_ASSERT(false);
        }
        if (!DmValueIsEqual(&toArray[pair[i].to], &value)) {
            return false;
        }
    }
    return true;
}

bool AASlotProjectPropertyIsSame(AASlotT *from, AASlotT *to, DmPairT *pair, uint32_t pairlen)
{
    DB_POINTER2(from, to);
    if (from->slotType == AA_SLOT_DM_VERTEX && to->slotType == AA_SLOT_DM_VERTEX) {
        return DmProjectPropertyIsSame(from->dmVertex, to->dmVertex, pair, pairlen);
    } else if (from->slotType == AA_SLOT_ARRAY && to->slotType == AA_SLOT_ARRAY) {
        return AASlotArrayProjectIsSame(from, to, pair, pairlen);
    } else if (from->slotType == AA_SLOT_ARRAY && to->slotType == AA_SLOT_DM_VERTEX) {
        return AASlotArrayIsSameWithVertex(from, to, pair, pairlen);
    }
    return AASlotVertexIsSameWithArray(from, to, pair, pairlen);
}

Status AASlotUpgradeVersionEqual(AASlotT *slot, DmValueT comparedUpgradeVersion, bool *equal)
{
    DB_POINTER2(slot, equal);
    DmValueT upgradeVersion = {0};
    Status ret = AASlotGetUpgradeVersion(slot, &upgradeVersion);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "get upgradeVersion property, Version=%" PRId32, comparedUpgradeVersion.value.intValue);
        return ret;
    }
    *equal = DmValueIsEqual(&upgradeVersion, &comparedUpgradeVersion);
    return ret;
}

Status AASlotGetNode(AASlotT *slot, DmVertexDescT *vertexDesc, uint32_t propeId, DmNodeT **node)
{
    if (vertexDesc == NULL || vertexDesc->nodeNum == 0 || vertexDesc->nodeDescs == NULL) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "no nodes, propId=%" PRIu32 ".", propeId);
        return GMERR_DATA_EXCEPTION;
    }
    for (uint32_t j = 0; j < vertexDesc->nodeNum; j++) {
        if (slot->dmVertex->vertexDesc->nodeDescs[j]->nodeId == propeId) {
            *node = slot->dmVertex->nodes[j];
            return GMERR_OK;
        }
    }
    DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "get nodes not match, propId=%" PRIu32 ".", propeId);
    return GMERR_DATA_EXCEPTION;
}

Status AASlotSetValue(AASlotT *newSlot, AASlotT *oldSlot)
{
    DB_POINTER2(newSlot, oldSlot);
    uint32_t propNum = 0;
    if (oldSlot->slotType == AA_SLOT_ARRAY) {
        propNum = oldSlot->slotArray.len;
    } else {
        RecordDescT *recordDesc = oldSlot->dmVertex->record->recordDesc;
        propNum = recordDesc->propeNum - recordDesc->reservedNum;
    }

    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < propNum; i++) {
        DmValueT value = {};
        ret = AASlotGetPrope(oldSlot, i, &value);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "get property, propId=%" PRIu32 ".", i);
            return ret;
        }
        if (value.type == DB_DATATYPE_NULL) {
            continue;
        }
        ret = AASlotSetPrope(newSlot, i, value);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "set property, propId=%" PRIu32 ".", i);
            return ret;
        }
    }
#ifdef FEATURE_SQL
    newSlot->addr = oldSlot->addr;
    newSlot->distanceToQuery = oldSlot->distanceToQuery;
#endif
    return ret;
}

#ifndef NDEBUG
void AASlotPrintVertex(AASlotT *slot)
{
    uint32_t length = 0;
    Status ret = DmVertexGetPrintLength(slot->dmVertex, NULL, &length);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        (void)DbPrintfDefault("unable to get vertex print length.\n");
        return;
    }
    char *buf = (char *)DB_MALLOC(length);
    if (buf == NULL) {
        (void)DbPrintfDefault("unable to alloc memory for vertex buf.\n");
        return;
    }
    (void)memset_s(buf, length, 0x00, length);
    ret = DmVertexPrint(slot->dmVertex, length, buf, NULL);
    if (ret == GMERR_OK) {
        (void)DbPrintfDefault("\n%s\n", buf);
    }
    DB_FREE(buf);
}

void AASlotPrintArray(AASlotT *slot)
{
    if (slot->slotArray.propDesc == NULL || slot->slotArray.len != slot->slotArray.propDesc->propNum) {
        (void)DbPrintfDefault("incorrect prop desc.\n");
        return;
    }
    (void)DbPrintfDefault("\nslot array:\n");
    for (uint32_t i = 0; i < slot->slotArray.len; i++) {
        DmValueT *value = &slot->slotArray.array[i];
        if (value == NULL) {
            continue;
        }
        uint32_t length = DmGetPropertyValuePrintLen(value) + 1;  // +1 for \0
        char *buf = (char *)DB_MALLOC(length);
        if (buf == NULL) {
            (void)DbPrintfDefault("unable to alloc memory for value buf.\n");
            return;
        }
        (void)memset_s(buf, length, 0x00, length);
        char *bufCursor = buf;
        DmPrintPropertyValue(value, &bufCursor, length);
        (void)DbPrintfDefault("\t%s: %s\n", slot->slotArray.propDesc->propSchema[i]->name, buf);
        DB_FREE(buf);
    }
    (void)DbPrintfDefault("\n");
}

void AASlotPrint(AASlotT *slot)
{
    if (slot == NULL) {
        (void)DbPrintfDefault("slot is null.\n");
        return;
    }
    if (slot->slotType == AA_SLOT_ARRAY) {
        AASlotPrintArray(slot);
        return;
    }
    // for AA_SLOT_DM_VERTEX and AA_SLOT_RETURNING
    AASlotPrintVertex(slot);
}
#endif

#ifdef __cplusplus
}
#endif
