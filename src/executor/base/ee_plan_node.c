/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: node impl.
 * Note1: memCtx is used in short process period , we will free all memory when finishing one request
 * Author: GMDBv5 EE Team
 * Create: 2022-8-02
 */

#include "ee_plan_node.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

Status CreatePlanPropDescWithSchema(DbMemCtxT *memCtx, DmSchemaT *schema, PlanPropDescT *slotDesc)
{
    DB_POINTER3(slotDesc, schema, memCtx);
    slotDesc->propNum = schema->propeNum;
    // memory will be freed unitied , see Note1
    slotDesc->propSchema = DbDynMemCtxAlloc(memCtx, slotDesc->propNum * sizeof(DmPropertySchemaT *));
    if (slotDesc->propSchema == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "create propSchema");
        return GMERR_OUT_OF_MEMORY;
    }
    for (uint32_t i = 0; i < slotDesc->propNum; i++) {
        slotDesc->propSchema[i] = &schema->properties[i];
    }
    return GMERR_OK;
}

Status NewCreateTableStmt(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, CreateTableStmtT **stmt)
{
    DB_POINTER3(memCtx, vertexLabel, stmt);
    // memory will be freed unitied, see Note1
    CreateTableStmtT *tableStmt = DbDynMemCtxAlloc(memCtx, sizeof(CreateTableStmtT));
    if (tableStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Create tableStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *tableStmt = (CreateTableStmtT){0};
    tableStmt->node.tag = T_CREATE_TABLE_STMT;
    tableStmt->vertexLabel = vertexLabel;

    *stmt = tableStmt;
    return GMERR_OK;
}
Status NewCreateTableResultStmt(DbMemCtxT *memCtx, NodeTagT tag, CreateTableResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    CreateTableResultStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(CreateTableResultStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Create tableResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (CreateTableResultStmtT){0};
    stmt->node.tag = tag;

    *resultStmt = stmt;
    return GMERR_OK;
}

Status NewDropTableStmt(DbMemCtxT *memCtx, uint32_t vertexLabelId, DropTableStmtT **stmt)
{
    DB_POINTER2(memCtx, stmt);
    // memory will be freed unitied, see Note1
    DropTableStmtT *dropStmt = (DropTableStmtT *)DbDynMemCtxAlloc(memCtx, sizeof(DropTableStmtT));
    if (dropStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "create DropTableStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *dropStmt = (DropTableStmtT){0};
    dropStmt->vertexLabelId = vertexLabelId;
    dropStmt->node.tag = T_DROP_TABLE_STMT;
    dropStmt->dropSysTable = true;

    *stmt = dropStmt;
    return GMERR_OK;
}

Status NewDropTableResultStmt(DbMemCtxT *memCtx, DropTableResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    DropTableResultStmtT *dropStmt = DbDynMemCtxAlloc(memCtx, sizeof(DropTableResultStmtT));
    if (dropStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropTableResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *dropStmt = (DropTableResultStmtT){0};
    dropStmt->node.tag = T_DROP_TABLE_STMT;

    *resultStmt = dropStmt;
    return GMERR_OK;
}

Status NewAlterTableResultStmt(DbMemCtxT *memCtx, AlterTableResultStmtT **resultStmt, NodeTagT tag)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    AlterTableResultStmtT *alterStmt = DbDynMemCtxAlloc(memCtx, sizeof(AlterTableResultStmtT));
    if (alterStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new AlterTableResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *alterStmt = (AlterTableResultStmtT){0};
    alterStmt->node.tag = tag;

    *resultStmt = alterStmt;
    return GMERR_OK;
}

#ifdef FEATURE_DATALOG
Status NewCreateTempTableResultStmt(DbMemCtxT *memCtx, CreateTempTableResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    CreateTempTableResultStmtT *createStmt = DbDynMemCtxAlloc(memCtx, sizeof(CreateTempTableResultStmtT));
    if (createStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new CreateTempTableResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *createStmt = (CreateTempTableResultStmtT){0};
    createStmt->node.tag = T_CREATE_TEMP_TABLE_STMT;

    *resultStmt = createStmt;
    return GMERR_OK;
}

Status NewDropTempTableStmt(DbMemCtxT *memCtx, DeltaTableT *deltaTable, DropTempTableStmtT **stmt)
{
    DB_POINTER3(memCtx, deltaTable, stmt);
    // memory will be freed unitied, see Note1
    DropTempTableStmtT *dropStmt = DbDynMemCtxAlloc(memCtx, sizeof(DropTempTableStmtT));
    if (dropStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropTempTableStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *dropStmt = (DropTempTableStmtT){0};
    dropStmt->deltaTable = deltaTable;
    dropStmt->node.tag = T_DROP_TEMP_TABLE_STMT;

    *stmt = dropStmt;
    return GMERR_OK;
}

Status NewDropTempTableResultStmt(DbMemCtxT *memCtx, DropTempTableResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    DropTempTableResultStmtT *dropStmt = DbDynMemCtxAlloc(memCtx, sizeof(DropTempTableResultStmtT));
    if (dropStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropTempTableResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *dropStmt = (DropTempTableResultStmtT){0};
    dropStmt->node.tag = T_DROP_TEMP_TABLE_STMT;

    *resultStmt = dropStmt;
    return GMERR_OK;
}

Status NewLoadPluginStmt(DbMemCtxT *memCtx, TextT filePath, LoadPluginStmtT **stmt)
{
    DB_POINTER2(memCtx, stmt);
    // memory will be freed unitied, see Note1
    LoadPluginStmtT *loadStmt = (LoadPluginStmtT *)DbDynMemCtxAlloc(memCtx, sizeof(LoadPluginStmtT));
    if (loadStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new LoadPluginStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *loadStmt = (LoadPluginStmtT){0};
    loadStmt->filePath = filePath;
    loadStmt->node.tag = T_LOAD_PLUGIN_STMT;

    *stmt = loadStmt;
    return GMERR_OK;
}

Status NewLoadPluginResultStmt(DbMemCtxT *memCtx, LoadPluginResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    LoadPluginResultStmtT *loadStmt = DbDynMemCtxAlloc(memCtx, sizeof(LoadPluginResultStmtT));
    if (loadStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new LoadPluginResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *loadStmt = (LoadPluginResultStmtT){0};
    loadStmt->node.tag = T_LOAD_PLUGIN_STMT;

    *resultStmt = loadStmt;
    return GMERR_OK;
}
#endif

#ifdef FEATURE_DATALOG
Status NewUnLoadPluginStmt(DbMemCtxT *memCtx, UnLoadPluginStmtT **unLoadPluginStmt)
{
    DB_POINTER2(memCtx, unLoadPluginStmt);
    // memory will be freed unitied, see Note1
    UnLoadPluginStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(UnLoadPluginStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new UnLoadPluginStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (UnLoadPluginStmtT){0};
    stmt->node.tag = T_UNLOAD_PLUGIN_STMT;

    *unLoadPluginStmt = stmt;
    return GMERR_OK;
}

Status NewUnLoadPluginResultStmt(DbMemCtxT *memCtx, UnLoadPluginResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    UnLoadPluginResultStmtT *unloadStmt = DbDynMemCtxAlloc(memCtx, sizeof(UnLoadPluginResultStmtT));
    if (unloadStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new UnLoadPluginResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *unloadStmt = (UnLoadPluginResultStmtT){0};
    unloadStmt->node.tag = T_UNLOAD_PLUGIN_STMT;

    *resultStmt = unloadStmt;
    return GMERR_OK;
}

Status NewDropAllTableInSoStmt(DbMemCtxT *memCtx, DropAllTableInSoStmtT **dropAllTableInSoStmt)
{
    DB_POINTER2(memCtx, dropAllTableInSoStmt);
    // memory will be freed unitied, see Note1
    DropAllTableInSoStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(DropAllTableInSoStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropAllTableInSoStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (DropAllTableInSoStmtT){0};
    stmt->node.tag = T_DROP_ALL_TABLE_IN_SO_STMT;

    *dropAllTableInSoStmt = stmt;
    return GMERR_OK;
}

Status NewDropAllTableInSoResultStmt(DbMemCtxT *memCtx, DropAllTableInSoResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    DropAllTableInSoResultStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(DropAllTableInSoResultStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropAllTableInSoResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (DropAllTableInSoResultStmtT){0};
    stmt->node.tag = T_DROP_ALL_TABLE_IN_SO_STMT;

    *resultStmt = stmt;
    return GMERR_OK;
}

Status NewDropAllUdfInSoStmt(DbMemCtxT *memCtx, DropAllUdfInSoStmtT **dropAllUdfInSoStmt)
{
    DB_POINTER2(memCtx, dropAllUdfInSoStmt);
    // memory will be freed unitied, see Note1
    DropAllUdfInSoStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(DropAllUdfInSoStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropAllUdfInSoStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (DropAllUdfInSoStmtT){0};
    stmt->node.tag = T_DROP_ALL_UDF_IN_SO_STMT;

    *dropAllUdfInSoStmt = stmt;
    return GMERR_OK;
}

Status NewDropAllUdfInSoResultStmt(DbMemCtxT *memCtx, DropAllUdfInSoResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    DropAllUdfInSoResultStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(DropAllUdfInSoResultStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropAllUdfInSoResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (DropAllUdfInSoResultStmtT){0};
    stmt->node.tag = T_DROP_ALL_UDF_IN_SO_STMT;

    *resultStmt = stmt;
    return GMERR_OK;
}

Status NewUnbindAllResPoolInSoStmt(DbMemCtxT *memCtx, UnbindAllResPoolInSoStmtT **unbindAllResPoolInSoStmt)
{
    DB_POINTER2(memCtx, unbindAllResPoolInSoStmt);
    // memory will be freed unitied, see Note1
    UnbindAllResPoolInSoStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(UnbindAllResPoolInSoStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new UnbindAllResPoolInSoStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (UnbindAllResPoolInSoStmtT){0};
    stmt->node.tag = T_UNBIND_ALL_RESOURCEPOOL_IN_SO_STMT;

    *unbindAllResPoolInSoStmt = stmt;
    return GMERR_OK;
}

Status NewUnbindAllResPoolInSoResultStmt(DbMemCtxT *memCtx, UnbindAllResPoolInSoResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    UnbindAllResPoolInSoResultStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(UnbindAllResPoolInSoResultStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new UnbindAllResPoolInSoResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (UnbindAllResPoolInSoResultStmtT){0};
    stmt->node.tag = T_UNBIND_ALL_RESOURCEPOOL_IN_SO_STMT;

    *resultStmt = stmt;
    return GMERR_OK;
}

Status NewDropAllResPoolInSoStmt(DbMemCtxT *memCtx, DropAllResPoolInSoStmtT **dropAllResPoolInSoStmt)
{
    DB_POINTER2(memCtx, dropAllResPoolInSoStmt);
    // memory will be freed unitied, see Note1
    DropAllResPoolInSoStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(DropAllResPoolInSoStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropAllResPoolInSoStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (DropAllResPoolInSoStmtT){0};
    stmt->node.tag = T_DROP_ALL_RESOURCEPOOL_IN_SO_STMT;

    *dropAllResPoolInSoStmt = stmt;
    return GMERR_OK;
}

Status NewDropAllResPoolInSoResultStmt(DbMemCtxT *memCtx, DropAllResPoolInSoResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    DropAllResPoolInSoResultStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(DropAllResPoolInSoResultStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropAllResPoolInSoResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (DropAllResPoolInSoResultStmtT){0};
    stmt->node.tag = T_DROP_ALL_RESOURCEPOOL_IN_SO_STMT;

    *resultStmt = stmt;
    return GMERR_OK;
}
#endif
#ifdef FEATURE_DATALOG
Status NewCreateResourcePoolStmt(DbMemCtxT *memCtx, DmResColPoolT *resPool, CreateResourcePoolStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);

    // memory will be freed unitied, see Note1
    CreateResourcePoolStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(CreateResourcePoolStmtT));
    if (stmt == NULL) {
        Status ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_AND_SET_LASERR(ret, "new CreateResourcePoolStmtT.");
        return ret;
    }

    *stmt = (CreateResourcePoolStmtT){0};
    stmt->node.tag = T_CREATE_RESOURCE_POOL_STMT;
    stmt->resPool = resPool;

    *resultStmt = stmt;
    return GMERR_OK;
}

Status NewCreateResourcePoolResultStmt(DbMemCtxT *memCtx, CreateResourcePoolResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);

    // memory will be freed unitied, see Note1
    CreateResourcePoolResultStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(CreateResourcePoolResultStmtT));
    if (stmt == NULL) {
        Status ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_AND_SET_LASERR(ret, "new CreateResourcePoolResultStmt.");
        return ret;
    }

    *stmt = (CreateResourcePoolResultStmtT){0};
    stmt->node.tag = T_CREATE_RESOURCE_POOL_STMT;

    *resultStmt = stmt;
    return GMERR_OK;
}

Status NewDropResourcePoolStmt(
    DbMemCtxT *memCtx, TextT resPoolName, uint32_t namespaceId, DropResourcePoolStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);

    // memory will be freed unitied, see Note1
    DropResourcePoolStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(DropResourcePoolStmtT));
    if (stmt == NULL) {
        Status ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_AND_SET_LASERR(ret, "new DropResourcePoolStmtT.");
        return ret;
    }

    *stmt = (DropResourcePoolStmtT){0};
    stmt->node.tag = T_DROP_RESOURCE_POOL_STMT;
    stmt->resPoolName = resPoolName;
    stmt->namespaceId = namespaceId;

    *resultStmt = stmt;
    return GMERR_OK;
}

Status NewBindResourcePoolStmt(DbMemCtxT *memCtx, TextT resPoolName, uint32_t namespaceId, uint32_t vertexLabelId,
    BindResourcePoolStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);

    // memory will be freed unitied, see Note1
    BindResourcePoolStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(BindResourcePoolStmtT));
    if (stmt == NULL) {
        Status ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_AND_SET_LASERR(ret, "new BindResourcePoolStmtT.");
        return ret;
    }

    *stmt = (BindResourcePoolStmtT){0};
    stmt->node.tag = T_BIND_RESOURCE_POOL_STMT;
    stmt->resPoolName = resPoolName;
    stmt->namespaceId = namespaceId;
    stmt->vertexLabelId = vertexLabelId;

    *resultStmt = stmt;
    return GMERR_OK;
}

Status NewBindResourcePoolResultStmt(DbMemCtxT *memCtx, BindResourcePoolResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);

    // memory will be freed unitied, see Note1
    BindResourcePoolResultStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(BindResourcePoolResultStmtT));
    if (stmt == NULL) {
        Status ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_AND_SET_LASERR(ret, "new BindResourcePoolResultStmt.");
        return ret;
    }

    *stmt = (BindResourcePoolResultStmtT){0};
    stmt->node.tag = T_BIND_RESOURCE_POOL_STMT;

    *resultStmt = stmt;
    return GMERR_OK;
}

Status NewUnBindResourcePoolStmt(DbMemCtxT *memCtx, uint32_t vertexLabelId, UnBindResourcePoolStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);

    // memory will be freed unitied, see Note1
    UnBindResourcePoolStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(UnBindResourcePoolStmtT));
    if (stmt == NULL) {
        Status ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_AND_SET_LASERR(ret, "new UnBindResourcePoolStmtT.");
        return ret;
    }

    *stmt = (UnBindResourcePoolStmtT){0};
    stmt->node.tag = T_UNBIND_RESOURCE_POOL_STMT;
    stmt->vertexLabelId = vertexLabelId;

    *resultStmt = stmt;
    return GMERR_OK;
}
#endif

Status NewCreateUDFStmt(DbMemCtxT *memCtx, CreateUDFStmtT **stmt)
{
    DB_POINTER2(memCtx, stmt);
    // memory will be freed unitied, see Note1
    CreateUDFStmtT *udfStmt = DbDynMemCtxAlloc(memCtx, sizeof(CreateUDFStmtT));
    if (udfStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new CreateUDFStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *udfStmt = (CreateUDFStmtT){0};
    udfStmt->node.tag = T_CREATE_UDF_STMT;
    *stmt = udfStmt;
    return GMERR_OK;
}

Status NewCreateUDFResultStmt(DbMemCtxT *memCtx, CreateUDFResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    CreateUDFResultStmtT *udfStmt = DbDynMemCtxAlloc(memCtx, sizeof(CreateUDFResultStmtT));
    if (udfStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new CreateUDFResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *udfStmt = (CreateUDFResultStmtT){0};
    udfStmt->node.tag = T_CREATE_UDF_STMT;
    *resultStmt = udfStmt;
    return GMERR_OK;
}

#ifdef FEATURE_DATALOG
Status NewAlterUDFStmt(DbMemCtxT *memCtx, AlterUDFStmtT **stmt)
{
    DB_POINTER2(memCtx, stmt);
    // memory will be freed unitied, see Note1
    AlterUDFStmtT *udfStmt = DbDynMemCtxAlloc(memCtx, sizeof(AlterUDFStmtT));
    if (udfStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new AlterUDFStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *udfStmt = (AlterUDFStmtT){0};
    udfStmt->node.tag = T_ALTER_UDF_STMT;
    *stmt = udfStmt;
    return GMERR_OK;
}

Status NewAlterUDFResultStmt(DbMemCtxT *memCtx, AlterUDFResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    AlterUDFResultStmtT *udfStmt = DbDynMemCtxAlloc(memCtx, sizeof(AlterUDFResultStmtT));
    if (udfStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new AlterUDFResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *udfStmt = (AlterUDFResultStmtT){0};
    udfStmt->node.tag = T_ALTER_UDF_STMT;
    *resultStmt = udfStmt;
    return GMERR_OK;
}

Status NewDegradeUdfStmt(DbMemCtxT *memCtx, DmUdfBaseT *udf, DegradeUdfStmtT **stmt)
{
    DB_POINTER2(memCtx, udf);
    DegradeUdfStmtT *degradeUdfStmt = DbDynMemCtxAlloc(memCtx, sizeof(DegradeUdfStmtT));
    if (degradeUdfStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DegradeUdfStmt.");
        return GMERR_OUT_OF_MEMORY;
    }
    *degradeUdfStmt = (DegradeUdfStmtT){0};
    degradeUdfStmt->node.tag = T_DEGRADE_UDF_STMT;
    degradeUdfStmt->udf = udf;
    *stmt = degradeUdfStmt;
    return GMERR_OK;
}

Status NewDegradeUdfResultStmt(DbMemCtxT *memCtx, DegradeUdfResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    DegradeUdfResultStmtT *udfStmt = DbDynMemCtxAlloc(memCtx, sizeof(DegradeUdfResultStmtT));
    if (udfStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DegradeUdfResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }
    *udfStmt = (DegradeUdfResultStmtT){0};
    udfStmt->node.tag = T_DEGRADE_UDF_STMT;
    *resultStmt = udfStmt;
    return GMERR_OK;
}
#endif

Status NewDropUDFStmt(DbMemCtxT *memCtx, uint32_t udfId, DropUDFStmtT **stmt)
{
    DB_POINTER2(memCtx, stmt);
    // memory will be freed unitied, see Note1
    DropUDFStmtT *udfStmt = DbDynMemCtxAlloc(memCtx, sizeof(DropUDFStmtT));
    if (udfStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropUDFStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *udfStmt = (DropUDFStmtT){0};
    udfStmt->node.tag = T_DROP_UDF_STMT;
    udfStmt->udfId = udfId;
    *stmt = udfStmt;
    return GMERR_OK;
}

#ifdef FEATURE_SQL
#ifndef IDS_HAOTIAN
Status NewBeginTransResultStmt(DbMemCtxT *memCtx, BeginTransResultStmtT **stmt)
{
    DB_POINTER2(memCtx, stmt);
    BeginTransResultStmtT *resultStmt = DbDynMemCtxAlloc(memCtx, sizeof(BeginTransResultStmtT));
    if (resultStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new BeginTransResultStmtT.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(resultStmt, sizeof(BeginTransResultStmtT), 0, sizeof(BeginTransResultStmtT));
    resultStmt->node.tag = T_BEGIN_TRANSACTION_STMT;
    *stmt = resultStmt;
    return GMERR_OK;
}

Status NewCommitTransResultStmt(DbMemCtxT *memCtx, CommitTransResultStmtT **stmt)
{
    DB_POINTER2(memCtx, stmt);
    CommitTransResultStmtT *resultStmt = DbDynMemCtxAlloc(memCtx, sizeof(CommitTransResultStmtT));
    if (resultStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new CommitTransResultStmtT");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(resultStmt, sizeof(CommitTransResultStmtT), 0, sizeof(CommitTransResultStmtT));
    resultStmt->node.tag = T_COMMIT_TRANSACTION_STMT;
    *stmt = resultStmt;
    return GMERR_OK;
}

Status NewRollbackTransResultStmt(DbMemCtxT *memCtx, RollbackTransResultStmtT **stmt)
{
    DB_POINTER2(memCtx, stmt);
    RollbackTransResultStmtT *resultStmt = DbDynMemCtxAlloc(memCtx, sizeof(RollbackTransResultStmtT));
    if (resultStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new RollbackTransResultStmtT");
        return GMERR_OUT_OF_MEMORY;
    }
    *resultStmt = (RollbackTransResultStmtT){0};
    resultStmt->node.tag = T_ROLLBACK_TRANSACTION_STMT;
    *stmt = resultStmt;
    return GMERR_OK;
}

Status NewSavepointResultStmt(DbMemCtxT *memCtx, CreateSavepointResultStmtT **stmt)
{
    DB_POINTER2(memCtx, stmt);
    CreateSavepointResultStmtT *resultStmt = DbDynMemCtxAlloc(memCtx, sizeof(CreateSavepointResultStmtT));
    if (resultStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new CreateSavepointResultStmtT");
        return GMERR_OUT_OF_MEMORY;
    }
    *resultStmt = (CreateSavepointResultStmtT){0};
    resultStmt->node.tag = T_CREATE_SAVEPOINT_STMT;
    *stmt = resultStmt;
    return GMERR_OK;
}

Status NewReleaseResultStmt(DbMemCtxT *memCtx, ReleaseSavepointResultStmtT **stmt)
{
    DB_POINTER2(memCtx, stmt);
    ReleaseSavepointResultStmtT *resultStmt = DbDynMemCtxAlloc(memCtx, sizeof(ReleaseSavepointResultStmtT));
    if (resultStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new ReleaseSavepointResultStmtT");
        return GMERR_OUT_OF_MEMORY;
    }
    *resultStmt = (ReleaseSavepointResultStmtT){0};
    resultStmt->node.tag = T_RELEASE_SAVEPOINT_STMT;
    *stmt = resultStmt;
    return GMERR_OK;
}

Status NewCreateViewResultStmt(DbMemCtxT *memCtx, CreateViewResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    CreateViewResultStmtT *createViewResultStmt = DbDynMemCtxAlloc(memCtx, sizeof(CreateViewResultStmtT));
    if (createViewResultStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new CreateViewResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *createViewResultStmt = (CreateViewResultStmtT){0};
    createViewResultStmt->node.tag = T_CREATE_VIEW_STMT;

    *resultStmt = createViewResultStmt;
    return GMERR_OK;
}
#endif
#endif

#ifdef FEATURE_GQL
Status NewCreateEdgePatternStmt(DbMemCtxT *memCtx, DmEdgePatternT *edgePattern, CreateEdgePatternStmtT **stmt)
{
    DB_POINTER3(memCtx, edgePattern, stmt);
    CreateEdgePatternStmtT *edgeStmt = DbDynMemCtxAlloc(memCtx, sizeof(CreateEdgePatternStmtT));
    if (edgeStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new CreateEdgePatternStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *edgeStmt = (CreateEdgePatternStmtT){0};
    edgeStmt->node.tag = T_GQL_CREATE_EDGE_PATTERN_STMT;
    edgeStmt->edgeLabel = edgePattern;

    *stmt = edgeStmt;
    return GMERR_OK;
}

Status NewCreateEdgePatternResultStmt(DbMemCtxT *memCtx, CreateEdgePatternResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    CreateEdgePatternResultStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(CreateEdgePatternResultStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new CreateEdgePatternResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (CreateEdgePatternResultStmtT){0};
    stmt->node.tag = T_GQL_CREATE_EDGE_PATTERN_STMT;

    *resultStmt = stmt;
    return GMERR_OK;
}

Status NewDropEdgePatternStmt(DbMemCtxT *memCtx, CataKeyT key, DropEdgePatternStmtT **stmt)
{
    DB_POINTER2(memCtx, stmt);
    DropEdgePatternStmtT *dropStmt = DbDynMemCtxAlloc(memCtx, sizeof(DropEdgePatternStmtT));
    if (dropStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropEdgePatternStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *dropStmt = (DropEdgePatternStmtT){0};
    dropStmt->node.tag = T_GQL_DROP_EDGE_PATTERN_STMT;
    dropStmt->key = key;

    *stmt = dropStmt;
    return GMERR_OK;
}

Status NewDropEdgePatternResultStmt(DbMemCtxT *memCtx, DropEdgePatternResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    DropEdgePatternResultStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(DropEdgePatternResultStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropEdgePatternResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (DropEdgePatternResultStmtT){0};
    stmt->node.tag = T_GQL_DROP_EDGE_PATTERN_STMT;

    *resultStmt = stmt;
    return GMERR_OK;
}

Status NewCreatePathPatternStmt(DbMemCtxT *memCtx, DmComplexPathInfoT *pathInfo, CreatePathPatternStmtT **stmt)
{
    DB_POINTER3(memCtx, pathInfo, stmt);
    CreatePathPatternStmtT *createPathStmt = DbDynMemCtxAlloc(memCtx, sizeof(CreatePathPatternStmtT));
    if (createPathStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new CreatePathPatternStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *createPathStmt = (CreatePathPatternStmtT){0};
    createPathStmt->pathInfo = pathInfo;
    createPathStmt->node.tag = T_GQL_CREATE_PATH_PATTERN_STMT;

    *stmt = createPathStmt;
    return GMERR_OK;
}

Status NewCreatePathPatternResultStmt(DbMemCtxT *memCtx, CreatePathPatternResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    CreatePathPatternResultStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(CreatePathPatternResultStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new CreatePathPatternResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (CreatePathPatternResultStmtT){0};
    stmt->node.tag = T_GQL_CREATE_PATH_PATTERN_STMT;

    *resultStmt = stmt;
    return GMERR_OK;
}

Status NewDropPathPatternStmt(DbMemCtxT *memCtx, uint32_t pathPatternId, DropPathPatternStmtT **stmt)
{
    DB_POINTER2(memCtx, stmt);
    DropPathPatternStmtT *dropPathStmt = DbDynMemCtxAlloc(memCtx, sizeof(DropPathPatternStmtT));
    if (dropPathStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropPathPatternStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *dropPathStmt = (DropPathPatternStmtT){0};
    dropPathStmt->pathPatternId = pathPatternId;
    dropPathStmt->node.tag = T_GQL_DROP_PATH_PATTERN_STMT;

    *stmt = dropPathStmt;
    return GMERR_OK;
}

Status NewDropPathPatternResultStmt(DbMemCtxT *memCtx, DropPathPatternResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    DropPathPatternResultStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(DropPathPatternResultStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropPathPatternResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (DropPathPatternResultStmtT){0};
    stmt->node.tag = T_GQL_DROP_PATH_PATTERN_STMT;

    *resultStmt = stmt;
    return GMERR_OK;
}

Status NewCreateSubscriptionStmt(DbMemCtxT *memCtx, DmSubscriptionT *subscription, CreateSubscriptionStmtT **stmt)
{
    DB_POINTER3(memCtx, subscription, stmt);
    CreateSubscriptionStmtT *createStmt = DbDynMemCtxAlloc(memCtx, sizeof(CreateSubscriptionStmtT));
    if (createStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new CreateSubscriptionStmt.");
        return GMERR_OUT_OF_MEMORY;
    }
    *createStmt = (CreateSubscriptionStmtT){0};
    createStmt->node.tag = T_GQL_CREATE_SUBSCRIPTION_STMT;
    createStmt->subscription = subscription;
    *stmt = createStmt;
    return GMERR_OK;
}

Status NewCreateSubscriptionResultStmt(DbMemCtxT *memCtx, CreateSubscriptionResultStmtT **stmt)
{
    DB_POINTER2(memCtx, stmt);
    CreateSubscriptionResultStmtT *resultStmt = DbDynMemCtxAlloc(memCtx, sizeof(CreateSubscriptionResultStmtT));
    if (resultStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new CreateSubscriptionResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }
    *resultStmt = (CreateSubscriptionResultStmtT){0};
    resultStmt->node.tag = T_GQL_CREATE_SUBSCRIPTION_STMT;
    *stmt = resultStmt;
    return GMERR_OK;
}

Status NewDropSubscriptionStmt(DbMemCtxT *memCtx, CataKeyT *key, DropSubscriptionStmtT **stmt)
{
    DB_POINTER2(memCtx, stmt);
    DropSubscriptionStmtT *dropStmt = DbDynMemCtxAlloc(memCtx, sizeof(DropSubscriptionStmtT));
    if (dropStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropSubscriptionStmt.");
        return GMERR_OUT_OF_MEMORY;
    }
    *dropStmt = (DropSubscriptionStmtT){0};
    dropStmt->node.tag = T_GQL_DROP_SUBSCRIPTION_STMT;
    dropStmt->key = key;
    *stmt = dropStmt;
    return GMERR_OK;
}

Status NewDropSubscriptionResultStmt(DbMemCtxT *memCtx, DropSubscriptionResultStmtT **stmt)
{
    DB_POINTER2(memCtx, stmt);
    DropSubscriptionResultStmtT *resultStmt = DbDynMemCtxAlloc(memCtx, sizeof(DropSubscriptionResultStmtT));
    if (resultStmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropSubscriptionResultStmt.");
        return GMERR_OUT_OF_MEMORY;
    }
    *resultStmt = (DropSubscriptionResultStmtT){0};
    resultStmt->node.tag = T_GQL_DROP_SUBSCRIPTION_STMT;
    *stmt = resultStmt;
    return GMERR_OK;
}

Status NewCreatePathTriggerResultStmt(DbMemCtxT *memCtx, CreatePathTriggerResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    CreatePathTriggerResultStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(CreatePathTriggerResultStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new CreatePathTriggerResultStmtT.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (CreatePathTriggerResultStmtT){0};
    stmt->node.tag = T_GQL_CREATE_TRIGGER_STMT;

    *resultStmt = stmt;
    return GMERR_OK;
}

Status NewDropPathTriggerResultStmt(DbMemCtxT *memCtx, DropPathTriggerResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    DropPathTriggerResultStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(DropPathTriggerResultStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new DropPathTriggerResultStmtT.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (DropPathTriggerResultStmtT){0};
    stmt->node.tag = T_GQL_DROP_TRIGGER_STMT;

    *resultStmt = stmt;
    return GMERR_OK;
}

Status NewSetSubscriptionSlowspeedResultStmt(DbMemCtxT *memCtx, SetSubscriptionSlowspeedResultStmtT **resultStmt)
{
    DB_POINTER2(memCtx, resultStmt);
    // memory will be freed unitied, see Note1
    SetSubscriptionSlowspeedResultStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(SetSubscriptionSlowspeedResultStmtT));
    if (stmt == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "new SetSubscriptionSlowspeedResultStmtT.");
        return GMERR_OUT_OF_MEMORY;
    }

    *stmt = (SetSubscriptionSlowspeedResultStmtT){0};
    stmt->node.tag = T_GQL_SET_SUBSCRIPTION_SLOWSPEED_STMT;

    *resultStmt = stmt;
    return GMERR_OK;
}
#endif

#ifdef __cplusplus
}
#endif
