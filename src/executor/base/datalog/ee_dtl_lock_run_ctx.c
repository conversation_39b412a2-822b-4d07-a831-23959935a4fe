/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Implementation of datalog lock run context.
 * Author: GMDBv5 EE Team
 * Create: 2024-1-4
 */

#include "ee_dtl_lock_run_ctx.h"
#include "dm_meta_prop_label.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

Status DtlLockRunCtxInit(DtlLockRunCtxT *lockRunCtx, DbMemCtxT *memCtx)
{
    DB_POINTER2(lockRunCtx, memCtx);
    *lockRunCtx = (DtlLockRunCtxT){0};
    lockRunCtx->isGetLock = false;
    lockRunCtx->memCtx = memCtx;
    lockRunCtx->conflictBit = NULL;
    DbLinkedListInit(&lockRunCtx->listNode);
    DbCreateList(&lockRunCtx->lockIdList, sizeof(uint32_t), memCtx);
    return DbSemInit(&lockRunCtx->sem, THREAD_SEMAPHORE, 0);
}

Status DtlLockRunCtxAppendLockTable(DtlLockRunCtxT *lockRunCtx, DmVertexLabelT *label)
{
    DB_POINTER2(lockRunCtx, label);
    if (label->commonInfo->datalogLabelInfo->inoutType != DM_DTL_INPUT_LABEL) {
        return GMERR_OK;
    }
    uint32_t lockId = (uint32_t)label->commonInfo->datalogLabelInfo->topoSortId;
    return DbAppendListItem(&lockRunCtx->lockIdList, &lockId);
}

void DtlLockRunCtxTimedWait(DtlLockRunCtxT *lockRunCtx, uint32_t ttl)
{
    DB_POINTER(lockRunCtx);
    (void)DbSemTimedWait(&lockRunCtx->sem, ttl * (uint32_t)USECONDS_IN_MSECOND);
}

void DtlLockRunCtxWakeup(DtlLockRunCtxT *lockRunCtx)
{
    DB_POINTER(lockRunCtx);
    (void)DbSemPost(&lockRunCtx->sem);
}

void DtlLockRunCtxDestroy(DtlLockRunCtxT *lockRunCtx)
{
    DB_POINTER(lockRunCtx);
    DbDestroyList(&lockRunCtx->lockIdList);
    (void)DbSemDestroy(&lockRunCtx->sem);
    *lockRunCtx = (DtlLockRunCtxT){0};
}

#ifdef __cplusplus
}
#endif
