/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: datalog lock run context header file.
 * Author: GMDBv5 EE Team
 * Create: 2024-1-4
 */

#ifndef DTL_LOCK_RUN_CTX_H
#define DTL_LOCK_RUN_CTX_H

#include "db_list.h"
#include "db_linkedlist.h"
#include "dm_meta_prop_strudefs.h"
#include "adpt_semaphore.h"
#include "db_internal_error.h"

#ifdef __cplusplus
extern "C" {
#endif

/*
 * Datalog lock Run Context
 */
typedef struct DtlLockRunCtx {
    TagLinkedListT listNode;
    DbListT lockIdList;    // List<uint32_t>; save all topoId of input tables
    uint8_t *conflictBit;  // conflictBit of input tables
    DbSemT sem;            // The semaphore used to wait for the lock
    bool isGetLock;        // Hold the lock or not, 原子变量
    DbMemCtxT *memCtx;     // memCtx for LockRunCtx
} DtlLockRunCtxT;

/*
 * @brief init DtlLockRunCtxT
 * @param[in] lockRunCtx : lockRunCtx to be init
 */
Status DtlLockRunCtxInit(DtlLockRunCtxT *lockRunCtx, DbMemCtxT *memCtx);

/*
 * @brief set input table list for DtlLockRunCtxT
 * @param[in] lockRunCtx : lockRunCtx to be init
 * @param[in] label : label to be locked in datalog
 * @return : Success or ErrorCode
 */
Status DtlLockRunCtxAppendLockTable(DtlLockRunCtxT *lockRunCtx, DmVertexLabelT *label);

/*
 * @brief wait for the lock
 * @param[in] lockRunCtx : this
 * @param[in] ttl : time to live in milliseconds
 * @return : Success or ErrorCode
 */
void DtlLockRunCtxTimedWait(DtlLockRunCtxT *lockRunCtx, uint32_t ttl);

/*
 * @brief  await the lockRunCtx
 * @param[in] lockMgr : lockMgr to be unlock
 * @param[in] lockRunCtx : lockRunCtx to release lock
 */
void DtlLockRunCtxWakeup(DtlLockRunCtxT *lockRunCtx);

/*
 * @brief destroy DtlLockRunCtxT
 * @param[in] lockRunCtx : lockRunCtx to be destroyed
 */
void DtlLockRunCtxDestroy(DtlLockRunCtxT *lockRunCtx);

#ifdef __cplusplus
}
#endif
#endif  // DTL_LOCK_RUN_CTX_H
