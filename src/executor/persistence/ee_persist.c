/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: executor persist am implementation
 * Author:
 * Create: 2023-12-07
 */
#include "ee_feature_import.h"
#include "ee_persist_load_table.h"
#include "ee_log.h"

// from ExecutorInit PersistenceExecutorFuncRegisterStep COMPONENT_PERSISTENCE, "executor-func-register"
SO_EXPORT_FOR_TS void PersistenceExecutorFuncRegister(DbInstanceHdT dbInstance)
{
    ImportPersistenceFunctionsT persistenceFunctions = {NULL};
    RegQryExecuteFlushFunctions(&persistenceFunctions);
    RegSysTableFunctions(&persistenceFunctions);
    RegPersistentViewFunctions(&persistenceFunctions);
    RegQryExecuteVerifyPersistDataFunctions(&persistenceFunctions);
    RegQryExecuteSwapDataDirFunctions(&persistenceFunctions);
    RegQrySetEmergencyFunctions(&persistenceFunctions);
    // 表级别加载功能可以宏隔离或者特性方式隔离，如需开启额外需要将部分接口SO_EXPORT放开
    QryComponentRegLoadTableFunctions(&persistenceFunctions);

    SetPersistenceFunctions(&persistenceFunctions);
    // load table mgr初始化（异步队列，加载的table list）
    (void)SeLoadTableInitMgr(dbInstance);
}
