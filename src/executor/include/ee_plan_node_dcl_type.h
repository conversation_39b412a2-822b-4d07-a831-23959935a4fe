/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: Define all node for DCL.
 * Author: GMDBv5 EE Team
 * Create: 2025-2-8
 */

#ifndef EE_PLAN_NODE_DCL_TYPE_H
#define EE_PLAN_NODE_DCL_TYPE_H

#include "db_galist.h"
#include "dm_meta_namespace.h"
#include "dm_meta_prop_label.h"
#include "dm_meta_res_col_pool.h"
#include "dm_meta_udf.h"
#include "ee_plan_node_tag.h"
#include "ee_plan_node_ddl_enum.h"
#include "ee_temp_table.h"
#include "gmc_errno.h"
#include "dm_meta_index_label.h"
#include "adpt_persistcap.h"
#include "dm_data_basic.h"

#ifdef FEATURE_SQL
#include "dm_data_trigger_info_sql.h"
#endif

#ifdef FEATURE_GQL
#include "dm_meta_complex_path.h"
#include "dm_meta_pathtrigger_info.h"
#include "dm_meta_subscription.h"
#endif

#ifdef FEATURE_STREAM
#include "dm_meta_prop_stream_strudefs.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif

#if defined(FEATURE_SQL) || defined(FEATURE_GQL)

typedef struct AnnIndexParameter {
    AnnIndexParameterTypeE parameterType;
    DmValueT parameterValue;
} AnnIndexParameterT;

/*
 * AST for create index
 */
typedef struct CreateIndexStmt {
    NodeT node;                   // base class
    DmVlIndexLabelT *indexLabel;  // IndexLabel which will be created
    DbListT annIndexParameters;   // <item : AnnIndexParameterT>. Configuration parameters for creating a vector index
} CreateIndexStmtT;

/*
 * AST for drop index
 */
typedef struct DropIndexStmt {
    NodeT node;              // base class
    uint32_t vertexLabelId;  // vertexLabelId for index
    uint32_t indexId;        // index id of the index that will be dropped
} DropIndexStmtT;

/*
 * AST for set parameter
 */
typedef struct SetParameterStmt {
    NodeT node;                    // base class
    AnnIndexParameterT parameter;  // Configuration parameters for querying a vector index
} SetParameterStmtT;

#endif
/*
 * ============================ Index Command end ==============================================
 */

#ifdef FEATURE_SQL
/*
 * ============================ Trigger Command begin ==========================================
 */

/*
 * AST for create trigger
 */
typedef struct CreateTriggerStmt {
    NodeT node;                // base class
    DmTriggerInfoT *trigInfo;  // TriggerInfo which will be created
} CreateTriggerStmtT;

/*
 * AST for drop trigger
 */
typedef struct DropTriggerStmt {
    NodeT node;                // base class
    DmTriggerInfoT *trigInfo;  // TriggerInfo which will be dropped
} DropTriggerStmtT;

/*
 * ============================ Trigger Command end ===========================================
 */

/*
 * ============================ View Command begin ==========================================
 */
/*
 * AST for create view
 */
typedef CreateTableStmtT CreateViewStmtT;

/*
 * AST for create view result
 */
typedef CreateTableResultStmtT CreateViewResultStmtT;

/*
 * AST for drop view
 */
typedef DropTableStmtT DropViewStmtT;

/*
 * AST for drop view result
 */
typedef DropTableResultStmtT DropViewResultStmtT;

/*
 * ============================ View Command end ===========================================
 */

/*
 * AST for begin transaction
 */
typedef struct BeginTransStmt {
    NodeT node;          // base class
    uint32_t trxAction;  // for sqlite, but not used
    DmTrxInfoT trxInfo;
    DmVertexLabelTypeE type;
    bool labelLockSerial;
} BeginTransStmtT;

typedef struct BeginTransResultStmt {
    NodeT node;  // base class
} BeginTransResultStmtT;

/*
 * AST for commit transaction
 */
typedef struct CommitTransStmt {
    NodeT node;  // base class
} CommitTransStmtT;

typedef struct CommitTransResultStmt {
    NodeT node;  // base class
} CommitTransResultStmtT;

/*
 * AST for rollback transaction
 */
typedef struct RollbackTransStmt {
    NodeT node;  // base class
    char *savepointName;
    bool hasSavepoint;
} RollbackTransStmtT;

typedef struct RollbackTransResultStmt {
    NodeT node;  // base class
} RollbackTransResultStmtT;

typedef struct CreateSavepointStmt {
    NodeT node;  // base class
    char *savepointName;
} CreateSavepointStmtT;

typedef struct CreateSavepointResultStmt {
    NodeT node;  // base class
} CreateSavepointResultStmtT;

typedef struct ReleaseSavePointStmt {
    NodeT node;  // base class
    char *savepointName;
} ReleaseSavepointStmtT;

typedef struct ReleaseSavepointResultStmt {
    NodeT node;  // base class
} ReleaseSavepointResultStmtT;

typedef struct FlushDataStat {
    NodeT node;
    uint32_t timeoutMs;  // 0 表示一直等待刷盘结束
} FlushDataStatT;

typedef struct BindCpuStat {
    NodeT node;
    void *cpuSet;
} BindCpuStatT;

typedef struct AlterTableSpaceStat {
    NodeT node;
    void *tableSpaceName;
    void *filePath;
} AlterTableSpaceStatT;

typedef struct PlanLoadTableCommonStmt {
    NodeT node;
    char *labelName;
    uint32_t labelId;
} PlanLoadTableCommonStmtT;

typedef struct PlanLoadTableStmt {
    PlanLoadTableCommonStmtT common;
} PlanLoadTableStmtT;

typedef struct PlanGetLoadTableStatusStmt {
    PlanLoadTableCommonStmtT common;
} PlanGetLoadTableStatusStmtT;

typedef struct PlanUnloadTableStmt {
    PlanLoadTableCommonStmtT common;
} PlanUnloadTableStmtT;

typedef struct PlanResizeBufferPoolStmt {
    NodeT node;
    uint32_t newBufferpoolSize;
} PlanResizeBufferPoolStmtT;

#endif

#ifdef __cplusplus
}
#endif

#endif  // EE_PLAN_NODE_DCL_TYPE_H
