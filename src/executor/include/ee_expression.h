/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementation of expression.
 * Author: GMDBv5 EE Team
 * Create: 2022-7-11
 */

#ifndef EE_EXPRESSION_H
#define EE_EXPRESSION_H

#include "gmc_errno.h"
#include "db_mem_context.h"
#include "dm_data_basic.h"
#include "dm_meta_yang_info.h"
#include "ee_expression_type.h"

#ifdef __cplusplus
extern "C" {
#endif

// 判断二元运算符是否为比较运算符
static inline bool IsCompOp(ExprOpTypeE opType)
{
    return (opType >= EXPR_OP_OPERATOR_COMPARE_TYPE_BEGIN && opType < EXPR_OP_OPERATOR_COMPARE_TYPE_END);
}

// 判断是否为tsdb二元算术表达式类型，只支持加减法
static inline bool IsTsBinaryArithOp(ExprOpTypeE opType)
{
    return (opType == EXPR_OP_ADD || opType == EXPR_OP_SUB);
}

// 判断二元运算符是否为逻辑运算符
static inline bool IsLogicOp(ExprOpTypeE opType)
{
    return (opType >= EXPR_OP_OPERATOR_LOGIC_TYPE_BEGIN && opType < EXPR_OP_OPERATOR_LOGIC_TYPE_END);
}

#if defined(FEATURE_TS) || defined(FEATURE_SQL) || defined(FEATURE_STREAM)
typedef enum SortDirection {
    SORT_ASC = 0,
    SORT_DESC,
} SortDirectionE;

// The enumerated value cannot be modified.
typedef enum SortNullPos {
    SORT_NULL_FIRST,
    SORT_NULL_LAST,
} SortNullPosE;
#endif

/*
 * base class of all expression
 */
typedef struct Expr {
    ExprOpTypeE opType;
} ExprT;

/*
 * array which consists of Exprs
 */
typedef struct ExprArray {
    uint32_t num;
    ExprT *expr[];
} ExprArrayT;

/*
 * *************************************** parameters begin *****************************************************
 */
/*
 * variable
 */
typedef struct ExprVar {
    ExprT expr;       // Base Class, must be the first.
    uint32_t propId;  // The parameter's index in parameters array in EState.
                      // reserve: DbDataTypeE dataType, Target Data Type.
} ExprVarT;

/*
 * constant
 */
typedef struct ExprConst {
    ExprT expr;    // Base Class, must be the first.
    DmValueT arg;  // The value of the const.
} ExprConstT;

/*
 * null
 */
typedef struct ExprNull {
    ExprT expr;  // Base Class, must be the first.
} ExprNullT;

/*
 * This class is an overall object
 * It can represent Tuple , which consists of many var , const etc
 * It can represent parameters of function
 */
typedef struct ExprFunc {
    ExprT expr;              // Base Class, must be the first.
    DbDataTypeE returnType;  // Return type of the function
    ExprArrayT array;        // An array of expression as the argument of the operator.
                             // !! array must be the last field of ExprFunc !!
} ExprFuncT;

/*
 * set-field
 */
typedef struct ExprSetField {
    ExprT expr;       // Base Class, must be the first.
    uint32_t propId;  // The property id in AASlot to be set
    ExprT *func;      // The function used to calculate the property
} ExprSetFieldT;

/*
 * Get parameter from parameters array in EState
 */
typedef struct ExprPara {
    ExprT expr;       // Base Class, must be the first.
    uint32_t paraId;  // The parameter's index in parameters array in EState.
    // reserve: DbDataTypeE dataType, Target Data Type.
    DbDataTypeE valueType;
} ExprParaT;

/*
 * Set parameters to parameters array in EState
 */
typedef struct ExprSetPara {
    ExprT expr;            // Base Class, must be the first.
    uint32_t startParaId;  // The started position of parameters array in EState to be stored.
    ExprArrayT *array;     // An array of expression for building SetPara, and one expression corresponding one
                           // parameter.
} ExprSetParaT;

typedef struct ExprConstPair {
    DmValueT constValue;
    uint32_t idx;
} ExprConstPairT;

typedef enum ExprCountType {
    EMPTY_COUNT,
    LEFT_SIGN_COUNT,  // 普通投影
    RIGHT_SIGN_COUNT,
    MULTI_COUNT,           // join算子投影
    NEGATIVE_MULTI_COUNT,  // not join投影
} ExprCountTypeE;

/*
 * for property value
 */
typedef struct ExprProperty {
    ExprT expr;  // Base Class, must be the first.
    uint32_t uniqNodeId;
    uint32_t propertyId;
    uint32_t pathId;
    DbDataTypeE type;  // Cast to
} ExprPropertyT;

typedef struct ExprNode {
    ExprT expr;  // Base Class, must be the first.
    uint32_t uniqNodeId;
    uint32_t pathId;
} ExprNodeT;

typedef struct ExprNode ExprChildishNodeT;

typedef struct ExprPair {
    ExprT *expr1;
    ExprT *expr2;
} ExprPairT;

typedef struct ExprPairArray {
    uint32_t num;  // Number of ExprPair.
    ExprPairT exprPair[];
} ExprPairArrayT;

typedef struct ExprCase {
    ExprT expr;  // Base Class, must be the first.
    ExprPairArrayT array;
} ExprCaseT;

/*
 * for subPlan
 */
typedef struct ExprSubPlan {
    ExprT expr;  // Base Class, must be the first.
    uint32_t subPlanID;
} ExprSubPlanT;

#ifdef FEATURE_SQL
typedef struct ExprSubQuery {
    ExprT expr;  // Base Class, must be the first.
    uint32_t subQueryId;
    ExprT *testExpr;
    ExprT *parParam;
    ExprT *subParam;
} ExprSubQueryT;
#endif
/*
 * *************************************** parameters end *******************************************************
 */

/*
 * *************************************** operators begin *****************************************************
 */

// Binary operator expression.
// Like Tuple equals, Tuple(Var(a), Var(b), Var(c)) = Tuple(Const(1), Const(2), Const(3))
typedef struct ExprBinary {
    ExprT expr;    // Base Class, must be the first.
    ExprT *left;   // Left tree of the operator.
    ExprT *right;  // Right tree of the operator.
} ExprBinaryT;

typedef struct ExprUnary {
    ExprT expr;    // Base Class, must be the first.
    ExprT *child;  // children of the operator.
} ExprUnaryT;
/*
 * *************************************** operators end *******************************************************
 */

/*
 * @brief Create an variable, which represents one property of left tuple.
 * @param[in] memCtx : Memory context for alloc variable.
 * @param[in] propId : The ID of the property.
 * @return : An expression.
 */
SO_EXPORT_FOR_TS ExprT *ExprMakeLVar(DbMemCtxT *memCtx, uint32_t propId);

/*
 * @brief Create an variable, which represents one property of right tuple.
 * @param[in] memCtx : Memory context for alloc variable.
 * @param[in] propId : The ID of the property.
 * @return : An expression.
 */
SO_EXPORT_FOR_TS ExprT *ExprMakeRVar(DbMemCtxT *memCtx, uint32_t propId);

static inline ExprT *ExprMakeVar(DbMemCtxT *memCtx, uint32_t propId)
{
    return ExprMakeLVar(memCtx, propId);
}

/*
 * @brief Create an const.
 * @param[in] memCtx : Memory context for alloc const.
 * @param[in] value : The value of const.
 * @return : An expression.
 */
SO_EXPORT_FOR_TS ExprT *ExprMakeConst(DbMemCtxT *memCtx, DmValueT value);

/*
 * @brief Create an null.
 * @param[in] memCtx : Memory context for alloc null.
 * @return : An expression.
 */
SO_EXPORT_FOR_TS ExprT *ExprMakeNull(DbMemCtxT *memCtx);

/*
 * @brief Create Tuple for composing some properties into one tuple.
 * @param[in] memCtx : Memory context for alloc parameter.
 * @param[in] arrayNum：The Number of array.
 * @return : An expression.
 */
SO_EXPORT_FOR_TS ExprT *ExprMakeTuple(DbMemCtxT *memCtx, uint32_t arrayNum);

/*
 * @brief Create a function.
 * @param[in] memCtx : Memory context for alloc parameter.
 * @param[in] op：The kind of function.
 * @param[in] arrayNum：The Number of array.
 * @return : An expression.
 */
SO_EXPORT_FOR_TS ExprT *ExprMakeFunc(DbMemCtxT *memCtx, ExprOpTypeE op, uint32_t arrayNum);

/*
 * @brief Create a case expression.
 * @param[in] memCtx : Memory context for alloc parameter.
 * @param[in] caseNum：The Number of case.
 * @return : An expression.
 */
ExprT *ExprMakeCase(DbMemCtxT *memCtx, uint32_t caseNum);

/*
 * @brief Create a setFeild expression.
 * @param[in] memCtx : Memory context for alloc parameter.
 * @param[in] func : the function used to calculate the property.
 * @param[in] propId：the propertyId of the resultTuple to be set.
 * @return : An expression.
 */
SO_EXPORT_FOR_TS ExprT *ExprMakeSetField(DbMemCtxT *memCtx, ExprT *func, uint32_t propId);

/*
 * @brief Create an parameter, which represents one item of parameters in EState.
 * @param[in] memCtx : Memory context for alloc parameter.
 * @param[in] paraId : The parameter's index in parameters array in EState.
 * @return : An expression.
 */
SO_EXPORT_FOR_TS ExprT *ExprMakePara(DbMemCtxT *memCtx, uint32_t paraId);

/*
 * @brief Create a special operator SetPara for saving values to parameters array in EState. SetPara is usually used
 * to pass nested loop parameters to inner tables.
 * @param[in] memCtx :Memory context for alloc parameter.
 * @param[in] array : An array of expression for building SetPara, and one expression corresponding one parameter.
 * @param[in] startParaId : The stared position of parameters array in EState to be stored.
 * @return : An expression.
 */
ExprT *ExprMakeSetPara(DbMemCtxT *memCtx, ExprArrayT *array, uint32_t startParaId);

/**
 * @brief: Create a special operator node for property information
 * @param[in] memCtx : Memory context for alloc parameter.
 * @param[in] nodeId : node unique node id
 * @param[in] propId : node property id
 * @param[in] pathId : node path array index
 * @param[in] type : cast to
 * @return An expression.
 */
ExprT *ExprMakeProperty(DbMemCtxT *memCtx, uint32_t nodeId, uint32_t propId, uint32_t pathId, DbDataTypeE type);

/**
 * @brief: Create a special operator node for node information
 * @param[in] memCtx : Memory context for alloc parameter.
 * @param[in] nodeId : node unique node id
 * @param[in] pathId : node path array index
 * @return An expression.
 */
ExprT *ExprMakeNode(DbMemCtxT *memCtx, uint32_t nodeId, uint32_t pathId);

/**
 * @brief: Create a special operator node for node information
 * @param[in] memCtx : Memory context for alloc parameter.
 * @param[in] nodeId : node unique node id
 * @param[in] pathId : node path array index
 * @return An expression.
 */
ExprT *ExprMakeChildishNode(DbMemCtxT *memCtx, uint32_t nodeId, uint32_t pathId);

/**
 * @brief: Create a special operator SubPlan for save subPlan
 * @param[in] memCtx : Memory context for alloc parameter.
 * @param[in] subPlanID : the plan to be used
 * @return An expression.
 */
ExprT *ExprMakeSubPlan(DbMemCtxT *memCtx, uint32_t subPlanID);

#ifdef FEATURE_SQL
ExprT *ExprMakeSubQuery(DbMemCtxT *memCtx, ExprOpTypeE type, uint32_t subQueryId, ExprT *parParam, ExprT *subParam);
#endif

/*
 * @brief Create an array for store expression.
 * @param[in] memCtx : Memory context for alloc array.
 * @param[in] num : Length of array.
 * @return : An array for expression.
 */
ExprArrayT *ExprMakeArray(DbMemCtxT *memCtx, uint32_t num);

/*
 * @brief Get the array of expression of Tuple or SetPara.
 * @param[in] expr : Tuple or SetPara operator.
 * @return : An array of expression.
 */
SO_EXPORT_FOR_TS ExprArrayT *ExprGetExprArray(ExprT *expr);

/*
 * @brief Get the array of expression of case.
 * @param[in] expr : Tuple or SetPara operator.
 * @return : An array of pair of expression.
 */
ExprPairArrayT *ExprGetExprPairArray(ExprT *expr);

/*
 * @brief Create unary expression.
 * @param[in] memCtx : Memory context for alloc parameter.
 * @param[in] child : The child operator.
 * @param[in] op : The type of operator. Currently, only NOT is supported.
 * @return : An expression.
 */
ExprT *ExprMakeUnary(DbMemCtxT *memCtx, ExprT *child, ExprOpTypeE op);

/*
 * @brief Create binary expression.
 * @param[in] memCtx : Memory context for alloc parameter.
 * @param[in] left : The left tree of operator. Currently, only Tuple is supported.
 * @param[in] right : The right tree of operator. Currently, only Tuple is supported.
 * @param[in] op : The type of operator. Currently, only EQUAL is supported.
 * @return : An expression.
 */
SO_EXPORT_FOR_TS ExprT *ExprMakeBinary(DbMemCtxT *memCtx, ExprT *left, ExprT *right, ExprOpTypeE op);

/*
 * @brief Destroy expression.
 * @param[in] memCtx : Memory context.
 * @param[in] expr : The expression to be destroyed.
 */
SO_EXPORT_FOR_TS void ExprDestroy(DbMemCtxT *memCtx, ExprT *expr);

/*
 * @brief Destroy expression list.
 * @param[in] memCtx : Memory context.
 * @param[in] exprs : The list of expressions to be destroyed.
 */
SO_EXPORT_FOR_TS void DestroyExprList(DbMemCtxT *memCtx, DbListT *exprs);

/*
 * @brief Deep copy expression.
 * @param[in] memCtx : Memory context.
 * @param[in] expr : The expression to be copied.
 * @return : New expression.
 */
SO_EXPORT_FOR_TS ExprT *ExprCopy(DbMemCtxT *memCtx, ExprT *expr);

/*
 * @brief Deep copy expression into existing memory.
 * @param[in] ctx : extend memory context to save new expression.
 * @param[in] expr : The expression to be copied.
 * @return : New expression.
 */
SO_EXPORT_FOR_TS ExprT *ExprCopyWithBuf(DbExtMemCtxT *ctx, ExprT *expr);

/*
 * @brief Judge expression is equal.
 * @param[in] expr1 : One of the expression to be judged.
 * @param[in] expr2 : Another of the expression to be judged.
 * @return : Is expression equaled.
 */
bool ExprEqual(ExprT *expr1, ExprT *expr2);

/*
 * @brief get total memory size of expression.
 * @param[in] expr : expression to get length.
 * @return : the total memory size of expression.
 */
uint32_t ExprGetLen(ExprT *expr);

/*
 * @brief Release the buffer holding the expr
 * @param[in] memCtx : the memCtx that allocated the buf
 * @param[in] expr : the expression to be released
 * @return : void
 */
void ExprDestroyWithBuf(DbMemCtxT *memCtx, ExprT *expr);

/*
 * @brief Create ExprArray using extended memCtx
 * @param[in] memCtx : the extended memCtx
 * @param[in] num : the array num
 * @return : ExprArray
 */
SO_EXPORT_FOR_TS ExprArrayT *ExprMakeArrayWithBuf(DbExtMemCtxT *memCtx, uint32_t num);

/*
 * @brief Create ExprFunc using extended memCtx
 * @param[in] memCtx : the extended memCtx
 * @param[in] op : opType
 * @param[in] array : the array number
 * @return : the expr
 */
SO_EXPORT_FOR_TS ExprT *ExprMakeFuncWithBuf(DbExtMemCtxT *memCtx, ExprOpTypeE op, uint32_t arrayNum);

/*
 * @brief Create ExprFunc using extended memCtx, opType is tuple
 * @param[in] memCtx : the extended memCtx
 * @param[in] array : the array number
 * @return : the expr
 */
SO_EXPORT_FOR_TS ExprT *ExprMakeTupleWithBuf(DbExtMemCtxT *memCtx, uint32_t arrayNum);

/*
 * @brief copy const value using extended memCtx
 * @param[in] memCtx : the extended memCtx
 * @param[in] srcValue : the source const value
 * @param[in] dstValue : the dest const value
 * @return : the expr
 */
Status ExprConstCopyWithExtMemCtx(DbExtMemCtxT *memCtx, DbValueT *srcValue, DbValueT *dstValue);

/*
 * @brief Create ExprConst using extended memCtx
 * @param[in] memCtx : the extended memCtx
 * @param[in] value : the const value
 * @return : the expr
 */
ExprT *ExprMakeConstWithBuf(DbExtMemCtxT *memCtx, DmValueT value);

/*
 * @brief Create ExprVar using extended memCtx
 * @param[in] memCtx : the extended memCtx
 * @param[in] propId : the propId
 * @param[in] opType : opType
 * @return : the expr
 */
ExprT *ExprMakeVarWithBuf(DbExtMemCtxT *memCtx, uint32_t propId, ExprOpTypeE opType);

/*
 * @brief Create ExprLVar using extended memCtx, opType is EXPR_OP_LVAR
 * @param[in] memCtx : the extended memCtx
 * @param[in] propId : the propId
 * @return : the expr
 */
SO_EXPORT_FOR_TS ExprT *ExprMakeLVarWithBuf(DbExtMemCtxT *memCtx, uint32_t propId);

/*
 * @brief Create MakeSetField using extended memCtx
 * @param[in] memCtx : the extended memCtx
 * @param[in] left : the left child of the binary expr
 * @param[in] right : the right child of the binary expr
 * @param[in] opType : opType
 * @return : the expr
 */
SO_EXPORT_FOR_TS ExprT *ExprMakeSetFieldWithBuf(DbExtMemCtxT *memCtx, ExprT *func, uint32_t propId);

/*
 * @brief Create ExprPara using extended memCtx
 * @param[in] memCtx : the extended memCtx
 * @param[in] paraId : The parameter's index in parameters array in EState.
 * @return : the expr
 */
SO_EXPORT_FOR_TS ExprT *ExprMakeParaWithBuf(DbExtMemCtxT *memCtx, uint32_t paraId);

/*
 * @brief Create ExprSetPara using extended memCtx
 * @param[in] memCtx : the extended memCtx
 * @param[in] array : An array of expression for building SetPara, and one expression corresponding one parameter.
 * @param[in] startParaId : The stared position of parameters array in EState to be stored.
 * @return : the expr
 */
SO_EXPORT_FOR_TS ExprT *ExprMakeSetParaWithBuf(DbExtMemCtxT *memCtx, ExprArrayT *array, uint32_t startParaId);

/*
 * @brief Create property expression using arena
 * @param[in] arena : the arena allocator
 * @param[in] nodeId : unique id of node
 * @param[in] nodeId : id of property
 * @param[in] nodeId : the type which is cast to
 * @return : the expr
 */
ExprT *ExprMakePropertyWithArena(DbArenaT *arena, uint32_t nodeId, uint32_t propId, uint32_t pathId, DbDataTypeE type);

/*
 * @brief Create const expression using arena
 * @param[in] arena : the arena allocator
 * @param[in] value : the value of const
 * @return : the expr
 */
ExprT *ExprMakeConstWithArena(DbArenaT *arena, DmValueT value);

/*
 * @brief Create unary operator using arena
 * @param[in] arena : the arena allocator
 * @param[in] child : the child of operator
 * @param[in] op : the type of operation
 * @return : the expr
 */
ExprT *ExprMakeUnaryWithArena(DbArenaT *arena, ExprT *child, ExprOpTypeE op);

/*
 * @brief Create binary operator using arena
 * @param[in] arena : the arena allocator
 * @param[in] left : the left child of operator
 * @param[in] right : the right child of operator
 * @param[in] op : the type of operation
 * @return : the expr
 */
ExprT *ExprMakeBinaryWithArena(DbArenaT *arena, ExprT *left, ExprT *right, ExprOpTypeE op);

/*
 * @brief Create function using arena
 * @param[in] arena : the arena allocator
 * @param[in] op : the type of function
 * @param[in] array : the array number
 * @return : the expr
 */
ExprT *ExprMakeFuncWithArena(DbArenaT *arena, ExprOpTypeE op, uint32_t arrayNum);

/*
 * @brief Create tuple using arena
 * @param[in] arena : the arena allocator
 * @param[in] array : the array number
 * @return : the expr
 */
ExprT *ExprMakeTupleWithArena(DbArenaT *arena, uint32_t arrayNum);

/*
 * @brief Create subplan using arena
 * @param[in] arena : the arena allocator
 * @param[in] subPlanID : the id of subplan
 * @return : the expr
 */
ExprT *ExprMakeSubPlanWithArena(DbArenaT *arena, uint32_t subPlanID);

/*
 * @brief Create case expression using arena
 * @param[in] arena : the arena allocator
 * @param[in] caseNum : the id of condition
 * @return : the expr
 */
ExprT *ExprMakeCaseWithArena(DbArenaT *arena, uint32_t caseNum);

/*
 * @brief Create Node using arena
 * @param[in] arena : the arena allocator
 * @param[in] subPlanID : the unique if of node
 * @param[in] pathId: the label id in search path
 * @return : the expr
 */
ExprT *ExprMakeNodeWithArena(DbArenaT *arena, uint32_t nodeId, uint32_t pathId);

/*
 * @brief Create ExprFunc using extended memCtx, opType is tuple
 * @param[in] arena : the arena allocator
 * @param[in] array : the array number
 * @return : the expr
 */
ExprT *ExprMakeChildishNodeWithArena(DbArenaT *arena, uint32_t nodeId, uint32_t pathId);

/*
 * @brief Create parameter using arena
 * @param[in] arena : the arena allocator
 * @param[in] caseNum : the id of parameter
 * @return : the expr
 */
ExprT *ExprMakeParaWithArena(DbArenaT *arena, uint32_t paraId);

bool IsExprAllConst(ExprArrayT *exprArray, uint32_t projectNum);

inline static bool ExprIsExtdAggFunc(ExprOpTypeE type)
{
    return type == EXTD_EXPR_OP_MIN || type == EXTD_EXPR_OP_MAX || type == EXTD_EXPR_OP_COUNT ||
           type == EXTD_EXPR_OP_SUM || type == EXTD_EXPR_OP_FIRST || type == EXTD_EXPR_OP_LAST ||
#if defined(FEATURE_STREAM)
           type == EXTD_EXPR_OP_SEQDISTINCTCOUNT || type == EXTD_EXPR_OP_ROWNUMBER ||
#endif
           type == EXTD_EXPR_OP_AVG || type == EXTD_EXPR_OP_COLSET;
}

/*
 * Judge expression is binary or not
 */
inline static bool ExprIsBinary(ExprOpTypeE type)
{
    return type == EXPR_OP_EQ || type == EXPR_OP_NE || type == EXPR_OP_GT || type == EXPR_OP_LT || type == EXPR_OP_GE ||
           type == EXPR_OP_LE || type == EXPR_OP_AND || type == EXPR_OP_OR || type == EXPR_OP_ADD ||
           type == EXPR_OP_SUB || type == EXPR_OP_MULTI || type == EXPR_OP_DIV || type == EXPR_OP_MOD ||
#if defined(FEATURE_TS) || defined(FEATURE_SQL)
           type == EXPR_OP_LIKE ||
#endif
#if defined(FEATURE_STREAM) || defined(FEATURE_SQL)
           type == EXPR_OP_IN || type == EXPR_OP_NOTIN ||
#endif
#ifdef FEATURE_SQL
           type == EXPR_OP_NOT_LIKE || type == EXPR_OP_GLOB || type == EXPR_OP_NOT_GLOB || type == EXPR_OP_IS ||
           type == EXPR_OP_ISNOT || type == EXPR_OP_VECTOR_DIST_L2 || type == EXPR_OP_VECTOR_DIST_COSINE ||
           type == EXPR_OP_BITAND || type == EXPR_OP_BITOR || type == EXPR_OP_LSHIFT || type == EXPR_OP_RSHIFT ||
           type == EXPR_OP_CONCAT ||
#endif
           ExprIsExtdAggFunc(type);
}

/*
 * Judge expression is function or not
 */
inline static bool ExprIsFunc(ExprOpTypeE type)
{
    return ((type >= EXPR_OP_FUNC_BEGIN) && (type < EXPR_OP_FUNC_END));
}

inline static bool ExprIsUnary(ExprOpTypeE type)
{
    return type == EXPR_OP_NOT || type == EXPR_OP_ISNULL || type == EXPR_OP_ISNOTNULL || type == EXPR_OP_ISTRUE;
}

inline static bool ExprIsAggFunc(ExprOpTypeE type)
{
    return type == EXPR_OP_MIN || type == EXPR_OP_MAX || type == EXPR_OP_COUNT || type == EXPR_OP_SUM ||
           type == EXPR_OP_AVG;
}

inline static bool ExprIsSubQuery(ExprOpTypeE type)
{
    return type >= EXPR_OP_SUBQUERY_BEGIN && type < EXPR_OP_SUBQUERY_END;
}

/*
 * Notice: the following ExprXXXCalcSize functions calculate the memory to be used by the coresponding
 * ExprMakeXXXWithBuf unlike ExprXXXGetLen, it doesn't include memory used by its child expressions
 */

static inline uint32_t ExprVarCalcSize(void)
{
    return (uint32_t)sizeof(ExprVarT);
}

static inline uint32_t ExprLVarCalcSize(void)
{
    return ExprVarCalcSize();
}

static inline uint32_t ExprRVarCalcSize(void)
{
    return ExprVarCalcSize();
}

static inline uint32_t ExprConstCalcSize(DmValueT value)
{
    if (DM_TYPE_NEED_MALLOC(value.type)) {
        return sizeof(ExprConstT) + value.value.length;
    }
    return (uint32_t)sizeof(ExprConstT);
}

static inline uint32_t ExprFuncCalcSize(uint32_t num)
{
    return (uint32_t)sizeof(ExprFuncT) + num * (uint32_t)sizeof(ExprT *);
}

static inline uint32_t ExprTupleCalcSize(uint32_t num)
{
    return ExprFuncCalcSize(num);
}

static inline uint32_t ExprArrayCalcSize(uint32_t num)
{
    ExprArrayT dummy = {0};
    return (uint32_t)sizeof(dummy) + num * (uint32_t)sizeof(dummy.expr[0]);
}

static inline uint32_t ExprParaCalcSize(void)
{
    return (uint32_t)sizeof(ExprParaT);
}

static inline uint32_t ExprSetParaCalcSize(void)
{
    return (uint32_t)sizeof(ExprSetParaT);
}

static inline uint32_t ExprBinaryCalcSize(void)
{
    return (uint32_t)sizeof(ExprBinaryT);
}

static inline uint32_t ExprSetFieldCalcSize(void)
{
    return (uint32_t)sizeof(ExprSetFieldT);
}

/*
 * ******************************************* cast begin **********************************
 */
static inline ExprSubPlanT *CastToSubPlan(ExprT *expr)
{
    DB_POINTER(expr);
    DB_ASSERT(expr->opType == EXPR_OP_SUBPLAN);
    return (ExprSubPlanT *)(void *)expr;
}

static inline ExprFuncT *CastToFunc(ExprT *expr)
{
    DB_POINTER(expr);
    DB_ASSERT(ExprIsFunc(expr->opType) || ExprIsAggFunc(expr->opType));
    return (ExprFuncT *)(void *)expr;
}

static inline uint32_t GetExprFuncArrayNum(ExprFuncT *expr)
{
    DB_POINTER(expr);
    return expr->array.num;
}

static inline ExprSetFieldT *CastToSetField(ExprT *expr)
{
    DB_POINTER(expr);
    DB_ASSERT(expr->opType == EXPR_OP_SET_FIELD);
    return (ExprSetFieldT *)(void *)expr;
}

static inline ExprVarT *CastToVar(ExprT *expr)
{
    DB_POINTER(expr);
    DB_ASSERT(expr->opType >= EXPR_OP_VAR_BEGIN && expr->opType < EXPR_OP_VAR_END);
    return (ExprVarT *)(void *)expr;
}

static inline ExprConstT *CastToConst(ExprT *expr)
{
    DB_POINTER(expr);
    DB_ASSERT(expr->opType == EXPR_OP_CONST);
    return (ExprConstT *)(void *)expr;
}

static inline ExprParaT *CastToPara(ExprT *expr)
{
    DB_POINTER(expr);
    DB_ASSERT(expr->opType == EXPR_OP_PARA);
    return (ExprParaT *)(void *)expr;
}

static inline ExprSetParaT *CastToSetPara(ExprT *expr)
{
    DB_POINTER(expr);
    DB_ASSERT(expr->opType == EXPR_OP_SET_PARA);
    return (ExprSetParaT *)(void *)expr;
}

static inline ExprPropertyT *CastToProperty(ExprT *expr)
{
    DB_POINTER(expr);
    DB_ASSERT(expr->opType == EXPR_OP_PROPERTY);
    return (ExprPropertyT *)(void *)expr;
}

static inline ExprNodeT *CastToNode(ExprT *expr)
{
    DB_POINTER(expr);
    DB_ASSERT(expr->opType == EXPR_OP_NODE);
    return (ExprNodeT *)(void *)expr;
}

static inline ExprChildishNodeT *CastToChildishNode(ExprT *expr)
{
    DB_POINTER(expr);
    DB_ASSERT(expr->opType == EXPR_OP_CHILDISH_NODE);
    return (ExprChildishNodeT *)(void *)expr;
}

static inline ExprBinaryT *CastToBinary(ExprT *expr)
{
    DB_POINTER(expr);
    DB_ASSERT(ExprIsBinary(expr->opType));
    return (ExprBinaryT *)(void *)expr;
}

static inline ExprUnaryT *CastToUnary(ExprT *expr)
{
    DB_POINTER(expr);
    DB_ASSERT(ExprIsUnary(expr->opType));
    return (ExprUnaryT *)(void *)expr;
}

static inline ExprCaseT *CastToCase(ExprT *expr)
{
    DB_POINTER(expr);
    DB_ASSERT(expr->opType == EXPR_OP_CASE);
    return (ExprCaseT *)(void *)expr;
}

/*
 * ******************************************* cast end **********************************
 */

static inline void ExprSetParaValueType(ExprT *expr, DbDataTypeE dateType)
{
    ExprParaT *para = CastToPara(expr);
    para->valueType = dateType;
}

#ifdef __cplusplus
}
#endif
#endif  // EE_EXPRESSION_H
