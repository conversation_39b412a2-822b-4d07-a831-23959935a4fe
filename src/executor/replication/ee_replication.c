/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: ee_replication.c
 * Description: The facade of replication.
 * Author:GMDBv5 EE TEAM
 * Create: 2024-12-24
 */

#include "adpt_spinlock.h"
#include "adpt_io.h"
#include "db_secure_msg_buffer.h"
#include "drt_base_def.h"
#include "drt_instance.h"
#include "drt_worker_manager.h"
#include "db_sysapp_context.h"
#include "db_msg_buffer.h"
#include "db_file.h"
#include "dm_meta_namespace.h"
#include "se_common.h"
#include "ee_key_cmp.h"
#include "ee_replication_queue.h"
#include "ee_replication_phase.h"
#include "ee_replication_bg.h"
#include "ee_replication_full.h"
#include "ee_replication.h"
#include "srv_data_ha.h"
#include "ee_log.h"  // 放在最后一行

// ee_replication.c和ee_replication_full.c循环依赖解不了
#ifdef __cplusplus
extern "C" {
#endif

#define REPLICATION_LOG_BUF_SIZE (64 * DB_KILO)

struct {
    DbSpinLockT lock;
    bool created;         // 单例模式
    bool slaveOnline;     // 备机上线
    DbLctrT slaveLctr;    // 备机loctor
    uint32_t version;     // 每次备上线加1
    LogQueueT *logQueue;  // 逻辑日志队列
    uint16_t rWorkerID;   // 复制处理线程
    uint16_t frWorkerID;  // 全量复制线程
} g_gmdbReplication = {.rWorkerID = DB_INVALID_UINT16, .frWorkerID = DB_INVALID_UINT16};

bool HaCheckIsInitComplete(void);  // 暂时这样使用，应该使用runtime的接口

static Status QryInitReplicationInLock(void)
{
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get instance.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    Status ret = GMERR_OK;
    if (g_gmdbReplication.created) {
        return GMERR_OK;
    }

    // 等待启动完成&&备机上线
    if (!HaCheckIsInitComplete() || !g_gmdbReplication.slaveOnline) {
        DB_LOG_WARN(GMERR_OK, "ha not init or slave not online.");
        return GMERR_OK;
    }

    DB_LOG_WARN(GMERR_OK, "Start Replication.");
    g_gmdbReplication.version++;
    ret = QryCreateReplicationWorker(&g_gmdbReplication.slaveLctr, g_gmdbReplication.version,
        &g_gmdbReplication.rWorkerID, &g_gmdbReplication.logQueue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to create Replication worker.");
        return ret;
    }

    ret = QryCreateFullReplicationWorker(g_gmdbReplication.version, &g_gmdbReplication.frWorkerID);
    if (ret != GMERR_OK) {
        (void)DrtStopWorkerNoWait(&drtIns->workerMgr, g_gmdbReplication.rWorkerID);
        g_gmdbReplication.rWorkerID = DB_INVALID_UINT16;
        g_gmdbReplication.logQueue = NULL;
        DB_LOG_ERROR(ret, "Unable to create FullReplication worker.");
        return ret;
    }
    g_gmdbReplication.created = true;
    return GMERR_OK;
}

static void QryStopReplicationInLock(void)
{
    g_gmdbReplication.slaveOnline = false;
    if (!g_gmdbReplication.created) {
        return;
    }

    DrtInstanceT *instance = DrtGetInstance(NULL);
    if (instance == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get instance when create replay thread mgr.");
        return;
    }

    (void)DrtStopWorkerNoWait(&instance->workerMgr, g_gmdbReplication.rWorkerID);
    (void)DrtStopWorkerNoWait(&instance->workerMgr, g_gmdbReplication.frWorkerID);
    QrySetFullReplicationPhase(FRP_NO, g_gmdbReplication.version);
    g_gmdbReplication.rWorkerID = DB_INVALID_UINT16;
    g_gmdbReplication.frWorkerID = DB_INVALID_UINT16;
    g_gmdbReplication.logQueue = NULL;
    g_gmdbReplication.slaveLctr = (DbLctrT){};
    g_gmdbReplication.created = false;
}

void QryStopReplication(void)
{
    DbSpinLock(&g_gmdbReplication.lock);
    QryStopReplicationInLock();
    DbSpinUnlock(&g_gmdbReplication.lock);
    DB_LOG_WARN(GMERR_OK, "Stop Replication.");
}

Status QryStartReplication(void)
{
    DbSpinLock(&g_gmdbReplication.lock);
    Status ret = QryInitReplicationInLock();
    DbSpinUnlock(&g_gmdbReplication.lock);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to init replication.");
        return ret;
    }

    return GMERR_OK;
}

Status QrySetReplicationUrl(const char *url)
{
    Status ret = GMERR_OK;
    DbSpinLock(&g_gmdbReplication.lock);

    // HA重复通知备上线
    if (g_gmdbReplication.slaveOnline) {
        ret = GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
        DbSpinUnlock(&g_gmdbReplication.lock);
        DB_LOG_AND_SET_LASERR(ret, "Unable to parse slave url:%s.", url);
        return ret;
    }

    ret = DbLctrParse(&g_gmdbReplication.slaveLctr, url);
    if (ret != GMERR_OK) {
        DbSpinUnlock(&g_gmdbReplication.lock);
        DB_LOG_AND_SET_LASERR(ret, "Unable to parse slave url:%s.", url);
        return ret;
    }
    g_gmdbReplication.slaveOnline = true;
    DbSpinUnlock(&g_gmdbReplication.lock);
    return GMERR_OK;
}

uint32_t QryGetReplicationCount(void)
{
    DbSpinLock(&g_gmdbReplication.lock);
    uint32_t count = g_gmdbReplication.version;
    DbSpinUnlock(&g_gmdbReplication.lock);
    return count;
}

Status QryGetReplicationUrl(char url[DB_LCTR_MAX_LEN])
{
    int32_t len = DB_LCTR_MAX_LEN;
    DbSpinLock(&g_gmdbReplication.lock);
    Status ret = DbLctrFmt(&g_gmdbReplication.slaveLctr, url, &len);
    DbSpinUnlock(&g_gmdbReplication.lock);
    return ret;
}

static Status QryReserveReplicationOpBody(MsgWriterT *writer)
{
    Status ret = GMERR_OK;
    ret = MsgWriterReset(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_REPLICATION);
    if (ret != GMERR_OK) {
        return ret;
    }

    FixBufferT *fixBuf = MsgWriterGetOpPayload(writer);
    uint32_t offset = 0;
    ret = FixBufReserveDataOffset(fixBuf, sizeof(QryReplicationOpBodyT), &offset);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to reserve QryReplicationOpBody to logBuf.");
        return ret;
    }

    MsgWriterFinishPutOp(writer);
    return GMERR_OK;
}

Status QryReplicate(LogBufferT *logBuf)
{
    Status ret = GMERR_OK;
    FixBufferT *fixBuf = QryGetReplicationFixBuf(logBuf);
    QryReplicationOpBodyT *rop = (QryReplicationOpBodyT *)RpcPeekFirstOpMsg(fixBuf);
    *rop = logBuf->rop;
    MsgWriterFinishNotMove(&logBuf->msgWriter);
    DbSpinLock(&g_gmdbReplication.lock);
    if (g_gmdbReplication.logQueue == NULL) {
        DbSpinUnlock(&g_gmdbReplication.lock);
        return GMERR_OK;  // 备下线时,容错处理
    }
    ret = QryWriteReplicationQueue(g_gmdbReplication.logQueue, logBuf);
    DbSpinUnlock(&g_gmdbReplication.lock);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 阻塞等待
    logBuf->requestTime = DbRdtsc();
    ret = DbSemWait(&logBuf->sem);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to replicate when waiting for semaphore.");
        return ret;
    }

    Status opStatus = logBuf->opStatus;
    // 清除上一次内容, 但是保留:事务ID,是否同步
    logBuf->reqData = NULL;
    logBuf->opStatus = 0;
    logBuf->countOffset = 0;
    logBuf->rop.logID += 1;
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (opStatus != GMERR_OK) {
        DB_LOG_WARN(opStatus, "opstatus from slave.");
        return opStatus;
    }
    return GMERR_OK;
}

static Status QryReplicateUseNameSpace(MsgWriterT *writer, uint32_t nspId)
{
    Status ret = GMERR_OK;
    DmNamespaceT *nsp = NULL;
    ret = CataGetNamespaceById(nspId, &nsp, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_USE_NAMESPACE);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    FixBufferT *rawBuf = MsgWriterGetOpPayload(writer);
    ret = FixBufPutUint32(rawBuf, 0);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = FixBufPutString(rawBuf, nsp->metaCommon.metaName);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    MsgWriterFinishPutOp(writer);
EXIT:
    (void)CataReleaseNamespace(NULL, nsp);
    return ret;
}

// 不特殊处理的DDL复制
static Status QryReplicationDDL(LogBufferT *logBuf, FixBufferT *req, uint32_t nspId)
{
    Status ret = GMERR_OK;
    // DDL统一使用同步方式
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (nspId != 0) {
        ret = QryReplicateUseNameSpace(writer, nspId);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    uint32_t opCode = ProtocolPeekFirstOpHeader(req)->opCode;
    ret = MsgWriterStartPutOp(writer, opCode);
    if (ret != GMERR_OK) {
        return ret;
    }

    const uint32_t totalLength = FixBufGetTotalLength(req);
    DB_ASSERT(totalLength > MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE);
    const uint32_t length = totalLength - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE;
    FixBufferT *fixBuf = MsgWriterGetOpPayload(writer);
    uint32_t offset = 0;
    ret = FixBufReserveDataOffset(fixBuf, length, &offset);
    if (ret != GMERR_OK) {
        return ret;
    }

    const void *body = RpcPeekFirstOpMsg(req);
    ret = FixBufPutReservedData(fixBuf, offset, body, length);
    if (ret != GMERR_OK) {
        return ret;
    }
    MsgWriterFinishPutOp(writer);
    return QryReplicate(logBuf);
}

Status QryReplicateCreateVertexLabel(LogBufferT *logBuf, DmVertexLabelT *vertexLabel)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_NAMESPACE) {
        return GMERR_OK;
    }

    if (vertexLabel->commonInfo->replicationType == DM_NO_REPLICATION) {
        return GMERR_OK;
    }

    // DDL统一使用同步方式
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryReplicateUseNameSpace(writer, vertexLabel->metaCommon.namespaceId);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_CREATE_VERTEX_LABEL);
    if (ret != GMERR_OK) {
        return ret;
    }

    FixBufferT *rawBuf = MsgWriterGetOpPayload(writer);

    char *labelJson = vertexLabel->metaVertexLabel->labelJson;
    ret = FixBufPutRawText(rawBuf, DM_STR_LEN(labelJson), labelJson);
    if (ret != GMERR_OK) {
        return ret;
    }

    char *configJson = vertexLabel->metaVertexLabel->configJson;
    ret = FixBufPutStringNullable(rawBuf, configJson);
    if (ret != GMERR_OK) {
        return ret;
    }

    const char *labelName = vertexLabel->metaCommon.metaName;
    ret = FixBufPutRawText(rawBuf, DM_STR_LEN(labelName), labelName);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FixBufPutString(rawBuf, vertexLabel->commonInfo->creator);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FixBufPutUint32(rawBuf, (uint32_t)vertexLabel->commonInfo->isGroupCreate);
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgWriterFinishPutOp(writer);
    logBuf->reqData = vertexLabel;
    return QryReplicate(logBuf);
}

Status QryReplicateDropVertexLabel(LogBufferT *logBuf, DmVertexLabelT *vertexLabel)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_NAMESPACE) {
        return GMERR_OK;
    }

    if (vertexLabel->commonInfo->replicationType == DM_NO_REPLICATION) {
        return GMERR_OK;
    }
    // DDL统一使用同步方式
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryReplicateUseNameSpace(writer, vertexLabel->metaCommon.namespaceId);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_DROP_VERTEX_LABEL);
    if (ret != GMERR_OK) {
        return ret;
    }

    FixBufferT *rawBuf = MsgWriterGetOpPayload(writer);
    const char *labelName = vertexLabel->metaCommon.metaName;
    ret = FixBufPutRawText(rawBuf, DM_STR_LEN(labelName), labelName);
    if (ret != GMERR_OK) {
        return ret;
    }

    // isDropAssoc
    ret = FixBufPutUint32(rawBuf, 0);
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgWriterFinishPutOp(writer);
    logBuf->reqData = vertexLabel;
    return QryReplicate(logBuf);
}

// 兼容TruncateNamespace, 需要手动填入opCode
Status QryReplicateTruncateVertexLabel(LogBufferT *logBuf, DmVertexLabelT *vertexLabel)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_VERTEX_META) {  // truncate针对数据
        return GMERR_OK;
    }

    if (vertexLabel->commonInfo->replicationType == DM_NO_REPLICATION) {
        return GMERR_OK;
    }
    // DDL统一使用同步方式
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryReplicateUseNameSpace(writer, vertexLabel->metaCommon.namespaceId);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_TRUNCATE_VERTEX_LABEL);
    if (ret != GMERR_OK) {
        return ret;
    }

    FixBufferT *rawBuf = MsgWriterGetOpPayload(writer);
    const char *labelName = vertexLabel->metaCommon.metaName;
    ret = FixBufPutRawText(rawBuf, DM_STR_LEN(labelName), labelName);
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgWriterFinishPutOp(writer);
    return QryReplicate(logBuf);
}

Status QryReplicateTruncateVertexLabelBackground(LogBufferT *logBuf, DmVertexLabelT *vertexLabel, FixBufferT *req)
{
    if (QryGetFullReplicationPhase() <= FRP_VERTEX_META) {  // truncate针对数据
        return GMERR_OK;
    }
    if (vertexLabel->commonInfo->replicationType == DM_NO_REPLICATION) {
        return GMERR_OK;
    }
    return QryReplicationDDL(logBuf, req, vertexLabel->metaCommon.namespaceId);
}

Status QryReplicateTxStart(LogBufferT *logBuf, uint64_t trxID, bool replication)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_CONNECT || !replication) {
        logBuf->rop = (QryReplicationOpBodyT){.trxID = DB_INVALID_UINT64};
        return GMERR_OK;
    }
    // 开始事务时候还不确定是否使用同步复制
    logBuf->rop = (QryReplicationOpBodyT){.trxID = trxID};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_TX_START);
    if (ret != GMERR_OK) {
        return ret;
    }

    FixBufferT *fixBuf = MsgWriterGetOpPayload(writer);
    // 目前只支持悲观事务
    ret = FixBufPutMultipleUint32(fixBuf, 4, GMC_TX_ISOLATION_COMMITTED, GMC_PESSIMISITIC_TRX, false, 0);
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgWriterFinishPutOp(writer);
    return GMERR_OK;
}

Status QryReplicateTxCommit(LogBufferT *logBuf)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_CONNECT) {
        return GMERR_OK;
    }

    if (logBuf->rop.trxID == DB_INVALID_UINT64) {
        return GMERR_OK;  // DDL不支持事务
    }
    // 事务包含同一个步表就使用同步提交，否则异步提交
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    if (MsgWriterGetOpCode(writer) != MSG_OP_RPC_NONE) {
        MsgWriterFinishPutOp(writer);
    }
    FixBufferT *req = QryGetReplicationFixBuf(logBuf);
    FixBufSeek(req, 0);
    (void)FixBufGetData(req, MSG_HEADER_ALIGN_SIZE);
    OpHeaderT *op = (OpHeaderT *)FixBufGetData(req, MSG_OP_HEADER_ALIGN_SIZE);
    if (op != NULL) {
        if (op->opCode == MSG_OP_RPC_REPLICATION) {
            FixBufSeek(req, FixBufGetSeekPos(req) + op->len - MSG_OP_HEADER_ALIGN_SIZE);
            op = (OpHeaderT *)FixBufGetData(req, MSG_OP_HEADER_ALIGN_SIZE);
        } else {
            return GMERR_OK;  // 非复制消息
        }
    }

    if (op != NULL) {
        if (op->opCode == MSG_OP_RPC_TX_START) {
            const uint32_t readOnlyOpCnt = 2;
            if (MsgWriterOpCnt(writer) == readOnlyOpCnt) {  // 只读务不和备机交互
                return GMERR_OK;
            }
        }
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_TX_COMMIT);
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgWriterFinishPutOp(writer);
    return QryReplicate(logBuf);
}

Status QryReplicateTxRollback(LogBufferT *logBuf)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_CONNECT) {
        return GMERR_OK;
    }

    if (logBuf->rop.trxID == DB_INVALID_UINT64) {
        return GMERR_OK;  // DDL不支持事务、事务没有开启就回滚
    }
    // 事务包含同一个步表就使用同步回滚，否则异步回滚
    FixBufferT *req = QryGetReplicationFixBuf(logBuf);
    FixBufSeek(req, 0);
    (void)FixBufGetData(req, MSG_HEADER_ALIGN_SIZE);
    OpHeaderT *op = (OpHeaderT *)FixBufGetData(req, MSG_OP_HEADER_ALIGN_SIZE);
    if (op != NULL) {
        if (op->opCode == MSG_OP_RPC_REPLICATION) {
            FixBufSeek(req, FixBufGetSeekPos(req) + op->len - MSG_OP_HEADER_ALIGN_SIZE);
            op = (OpHeaderT *)FixBufGetData(req, MSG_OP_HEADER_ALIGN_SIZE);
        } else {
            return GMERR_OK;  // 非复制消息
        }
    }

    if (op != NULL) {
        if (op->opCode == MSG_OP_RPC_TX_START) {  // 小事务不和备机交互
            return GMERR_OK;
        }
    }

    FixBufSeek(req, 0);
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    if (MsgWriterGetOpCode(writer) != MSG_OP_RPC_NONE) {
        MsgWriterFinishPutOp(writer);
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_TX_ROLLBACK);
    if (ret != GMERR_OK) {
        return ret;
    }
    MsgWriterFinishPutOp(writer);
    return QryReplicate(logBuf);
}

// 空的同步消息用于全量复制完成后的同步，确保异步复制都完成
Status QryReplicateNull(LogBufferT *logBuf)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_NAMESPACE) {
        return GMERR_OK;
    }

    // DDL统一使用同步方式
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_NONE);
    if (ret != GMERR_OK) {
        return ret;
    }
    MsgWriterFinishPutOp(writer);
    return QryReplicate(logBuf);
}

static Status QryReplicateIfBufFull(LogBufferT *logBuf)
{
    FixBufferT *req = QryGetReplicationFixBuf(logBuf);
    if (FixBufGetPos(req) > REPLICATION_LOG_BUF_SIZE) {
        MsgWriterFinishPutOp(&logBuf->msgWriter);
        return QryReplicate(logBuf);
    }
    return GMERR_OK;
}

Status QryReplicateReplaceVertex(LogBufferT *logBuf, TupleT tuple, DmVertexLabelT *vertexLabel)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_VERTEX_META) {
        return GMERR_OK;
    }
    if (logBuf->rop.trxID == DB_INVALID_UINT64) {
        return GMERR_OK;  // 开启事务的时候判断是否需要复制，保证原子性
    }

    if (vertexLabel->commonInfo->replicationType == DM_NO_REPLICATION) {
        return GMERR_OK;
    }

    if (vertexLabel->commonInfo->replicationType == DM_STRONG_CONSISTENCY) {
        logBuf->rop.sync = true;
    }

    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    FixBufferT *req = QryGetReplicationFixBuf(logBuf);
    if (MsgWriterGetOpCode(writer) != MSG_OP_RPC_REPLACE_VERTEX || logBuf->reqData != vertexLabel) {
        if (MsgWriterGetOpCode(writer) != MSG_OP_RPC_NONE) {
            MsgWriterFinishPutOp(writer);
        }

        ret = QryReplicateUseNameSpace(writer, vertexLabel->metaCommon.namespaceId);
        if (ret != GMERR_OK) {
            return ret;
        }

        ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_REPLACE_VERTEX);
        if (ret != GMERR_OK) {
            return ret;
        }

        ret = FixBufPutMultipleUint32(req, 3, vertexLabel->metaCommon.metaId, vertexLabel->metaCommon.version,
            vertexLabel->metaVertexLabel->uuid);
        if (ret != GMERR_OK) {
            return ret;
        }

        ret = FixBufReserveDataOffset(req, sizeof(uint32_t), &logBuf->countOffset);
        if (ret != GMERR_OK) {
            return ret;
        }
        uint32_t *count = FixBufOffsetToAddr(req, logBuf->countOffset);
        *count = 0;
        logBuf->reqData = vertexLabel;
    }

    uint32_t *count = FixBufOffsetToAddr(req, logBuf->countOffset);
    *count += 1;
    ret = FixBufPutRawText(req, tuple.bufSize, tuple.buf);
    if (ret != GMERR_OK) {
        return ret;
    }

    // fill operateEdgeFlag
    FixBufPutUint32(req, 0);
    return QryReplicateIfBufFull(logBuf);
}

static Status QryReplicateDeleteVertexFill(LogBufferT *logBuf, TupleT tuple, DmVertexT *vertex, uint32_t indexId)
{
    Status ret = GMERR_OK;
    FixBufferT *req = QryGetReplicationFixBuf(logBuf);
    IndexKeyT indexKey = {};
    ret = QryGetKeyBufFromVertexBuf(vertex, indexId, tuple.buf, &indexKey);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    uint32_t *count = FixBufOffsetToAddr(req, logBuf->countOffset);
    *count += 1;

    // fill rangeScanFlag
    FixBufPutUint32(req, CS_RANGE_SCAN_LEFT_INCLUDE | CS_RANGE_SCAN_RAW_KEY);

    ret = FixBufPutRawText(req, indexKey.keyLen, indexKey.keyData);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill rightIndexKey with len
    ret = FixBufPutUint32(req, 0);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill operateEdgeFlag
    ret = FixBufPutUint32(req, 0);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status QryReplicateDeleteVertex(LogBufferT *logBuf, TupleT tuple, DmVertexT *vertex, DmVertexLabelT *vertexLabel)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_VERTEX_META) {
        return GMERR_OK;
    }

    if (logBuf->rop.trxID == DB_INVALID_UINT64) {
        return GMERR_OK;  // 开启事务的时候判断释放需要复制，保证原子性
    }

    DmReplicationTypeE replicationType = vertexLabel->commonInfo->replicationType;
    if (replicationType == DM_NO_REPLICATION) {
        return GMERR_OK;
    }

    if (replicationType == DM_STRONG_CONSISTENCY) {
        logBuf->rop.sync = true;
    }
    const uint32_t indexId = vertexLabel->metaVertexLabel->pkIndex->idxLabelBase.indexId;
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    FixBufferT *req = QryGetReplicationFixBuf(logBuf);
    if (MsgWriterGetOpCode(writer) != MSG_OP_RPC_DELETE_VERTEX || logBuf->reqData != vertexLabel) {
        if (MsgWriterGetOpCode(writer) != MSG_OP_RPC_NONE) {
            MsgWriterFinishPutOp(writer);
        }

        ret = QryReplicateUseNameSpace(writer, vertexLabel->metaCommon.namespaceId);
        if (ret != GMERR_OK) {
            return ret;
        }

        ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_DELETE_VERTEX);
        if (ret != GMERR_OK) {
            return ret;
        }

        // fill vertexLabelId|schemaVersion|uuid|indexkey index|condStr with len
        ret = FixBufPutMultipleUint32(req, 5, vertexLabel->metaCommon.metaId, vertexLabel->metaCommon.version,
            vertexLabel->metaVertexLabel->uuid, indexId, 0);
        if (ret != GMERR_OK) {
            return ret;
        }

        // fill delete Num
        ret = FixBufReserveDataOffset(req, sizeof(uint32_t), &logBuf->countOffset);
        if (ret != GMERR_OK) {
            return ret;
        }
        uint32_t *count = FixBufOffsetToAddr(req, logBuf->countOffset);
        *count = 0;
        logBuf->reqData = vertexLabel;
    }

    ret = QryReplicateDeleteVertexFill(logBuf, tuple, vertex, indexId);
    if (ret != GMERR_OK) {
        return ret;
    }

    return QryReplicateIfBufFull(logBuf);
}

Status QryReplicateKvSet(LogBufferT *logBuf, TextT key, TextT value, DmKvLabelT *kvLabel)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_KV_META) {
        return GMERR_OK;
    }

    if (logBuf->rop.trxID == DB_INVALID_UINT64) {
        return GMERR_OK;  // 开启事务的时候判断释放需要复制，保证原子性
    }

    DmReplicationTypeE replicationType = kvLabel->replicationType;
    if (replicationType == DM_NO_REPLICATION) {
        return GMERR_OK;
    }

    if (replicationType == DM_STRONG_CONSISTENCY) {
        logBuf->rop.sync = true;
    }

    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    FixBufferT *req = QryGetReplicationFixBuf(logBuf);
    if (MsgWriterGetOpCode(writer) != MSG_OP_RPC_NONE) {
        MsgWriterFinishPutOp(writer);
    }

    ret = QryReplicateUseNameSpace(writer, kvLabel->metaCommon.namespaceId);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_SET_KV);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FixBufPutUint32(req, kvLabel->metaCommon.metaId);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FixBufPutRawText(req, key.len, key.str);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FixBufPutRawText(req, value.len, value.str);
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgWriterFinishPutOp(writer);
    return GMERR_OK;
}

Status QryReplicateKvRemove(LogBufferT *logBuf, TextT key, DmKvLabelT *kvLabel)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_KV_META) {
        return GMERR_OK;
    }

    if (logBuf->rop.trxID == DB_INVALID_UINT64) {
        return GMERR_OK;  // 开启事务的时候判断释放需要复制，保证原子性
    }

    DmReplicationTypeE replicationType = kvLabel->replicationType;
    if (replicationType == DM_NO_REPLICATION) {
        return GMERR_OK;
    }

    if (replicationType == DM_STRONG_CONSISTENCY) {
        logBuf->rop.sync = true;
    }

    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    FixBufferT *req = QryGetReplicationFixBuf(logBuf);
    if (MsgWriterGetOpCode(writer) != MSG_OP_RPC_NONE) {
        MsgWriterFinishPutOp(writer);
    }

    ret = QryReplicateUseNameSpace(writer, kvLabel->metaCommon.namespaceId);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_DELETE_KV);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FixBufPutUint32(req, kvLabel->metaCommon.metaId);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FixBufPutRawText(req, key.len, key.str);
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgWriterFinishPutOp(writer);
    return GMERR_OK;
}

Status QryReplicateCreateKvTable(LogBufferT *logBuf, DmKvLabelT *kvLabel)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_NAMESPACE) {
        return GMERR_OK;
    }

    if (kvLabel->replicationType == DM_NO_REPLICATION) {
        return GMERR_OK;
    }

    // DDL统一使用同步方式
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryReplicateUseNameSpace(writer, kvLabel->metaCommon.namespaceId);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_CREATE_KV_TABLE);
    if (ret != GMERR_OK) {
        return ret;
    }

    FixBufferT *rawBuf = MsgWriterGetOpPayload(writer);
    const char *labelName = kvLabel->metaCommon.metaName;
    ret = FixBufPutRawText(rawBuf, DM_STR_LEN(labelName), labelName);
    if (ret != GMERR_OK) {
        return ret;
    }

    char *configJson = kvLabel->configJson;
    ret = FixBufPutStringNullable(rawBuf, configJson);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FixBufPutString(rawBuf, kvLabel->creator);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FixBufPutUint32(rawBuf, (uint32_t)kvLabel->isGroupCreate);
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgWriterFinishPutOp(writer);
    logBuf->reqData = kvLabel;
    return QryReplicate(logBuf);
}

// 兼容ClearNamespace, 需要手动填入opCode
Status QryReplicateDropKvTable(LogBufferT *logBuf, DmKvLabelT *kvLabel)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_NAMESPACE) {
        return GMERR_OK;
    }

    if (kvLabel->replicationType == DM_NO_REPLICATION) {
        return GMERR_OK;
    }
    // DDL统一使用同步方式
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryReplicateUseNameSpace(writer, kvLabel->metaCommon.namespaceId);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_DROP_KV_TABLE);
    if (ret != GMERR_OK) {
        return ret;
    }

    FixBufferT *rawBuf = MsgWriterGetOpPayload(writer);
    const char *labelName = kvLabel->metaCommon.metaName;
    ret = FixBufPutRawText(rawBuf, DM_STR_LEN(labelName), labelName);
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgWriterFinishPutOp(writer);
    logBuf->reqData = kvLabel;
    return QryReplicate(logBuf);
}

// 兼容TruncateNamespace, 需要手动填入opCode
Status QryReplicateTruncateKvTable(LogBufferT *logBuf, DmKvLabelT *kvLabel)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_KV_META) {  // truncate针对数据
        return GMERR_OK;
    }

    if (kvLabel->replicationType == DM_NO_REPLICATION) {
        return GMERR_OK;
    }
    // DDL统一使用同步方式
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryReplicateUseNameSpace(writer, kvLabel->metaCommon.namespaceId);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_TRUNCATE_KV_TABLE);
    if (ret != GMERR_OK) {
        return ret;
    }

    FixBufferT *rawBuf = MsgWriterGetOpPayload(writer);
    const char *labelName = kvLabel->metaCommon.metaName;
    ret = FixBufPutRawText(rawBuf, DM_STR_LEN(labelName), labelName);
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgWriterFinishPutOp(writer);
    return QryReplicate(logBuf);
}

static Status QryReplicateFillNameSpace(MsgWriterT *writer, DmNamespaceT *nameSpace)
{
    Status ret = GMERR_OK;
    FixBufferT *rawBuf = MsgWriterGetOpPayload(writer);
    ret = FixBufPutString(rawBuf, nameSpace->metaCommon.metaName);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FixBufPutString(rawBuf, nameSpace->creator);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(nameSpace->memCtx);
    DmTablespaceT *tsp = NULL;
    CataGetTspById(dbInstance, nameSpace->defaultTspId, &tsp);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to get default table, id=%" PRIu32 ".", nameSpace->defaultTspId);
        return ret;
    }
    ret = FixBufPutString(rawBuf, tsp->metaCommon.metaName);
    (void)CataReleaseTsp(tsp);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FixBufPutUint32(rawBuf, nameSpace->trxInfo.isolationLevel);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FixBufPutUint32(rawBuf, nameSpace->trxInfo.trxType);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FixBufPutString(rawBuf, nameSpace->creator);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FixBufPutUint32(rawBuf, (uint32_t)nameSpace->isGroupCreate);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

Status QryReplicateCreateNameSpace(LogBufferT *logBuf, DmNamespaceT *nameSpace)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_GROUP) {
        return GMERR_OK;
    }

    // DDL统一使用同步方式
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_CREATE_NAMESPACE);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = QryReplicateFillNameSpace(writer, nameSpace);
    if (ret != GMERR_OK) {
        return ret;
    }
    MsgWriterFinishPutOp(writer);
    logBuf->reqData = nameSpace;
    return QryReplicate(logBuf);
}

Status QryReplicateDropNameSpace(LogBufferT *logBuf, DmNamespaceT *nameSpace)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_GROUP) {
        return GMERR_OK;
    }

    // DDL统一使用同步方式
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_DROP_NAMESPACE);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryReplicateFillNameSpace(writer, nameSpace);
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgWriterFinishPutOp(writer);
    logBuf->reqData = nameSpace;
    return QryReplicate(logBuf);
}

Status QryReplicateCreateUser(LogBufferT *logBuf, CataUserT *user)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_CONNECT) {
        return GMERR_OK;
    }

    // DDL统一使用同步方式
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_CREATE_USER);
    if (ret != GMERR_OK) {
        return ret;
    }
    FixBufferT *rawBuf = MsgWriterGetOpPayload(writer);
    ret = FixBufPutUint16(rawBuf, 1);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutString(rawBuf, user->userOrGrpName.str);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutString(rawBuf, user->processName.str);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint16(rawBuf, user->reservedConnNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutData(rawBuf, user->metaLimit, USER_META_TYPE_NUM_N * sizeof(uint32_t));
    if (ret != GMERR_OK) {
        return ret;
    }
    // isAtomic
    ret = FixBufPutUint32(rawBuf, 0);
    if (ret != GMERR_OK) {
        return ret;
    }
    MsgWriterFinishPutOp(writer);
    logBuf->reqData = user;
    return QryReplicate(logBuf);
}

Status QryReplicateDropUser(LogBufferT *logBuf, CataUserT *user)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_CONNECT) {
        return GMERR_OK;
    }

    // DDL统一使用同步方式
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_DROP_USER);
    if (ret != GMERR_OK) {
        return ret;
    }
    FixBufferT *rawBuf = MsgWriterGetOpPayload(writer);
    ret = FixBufPutString(rawBuf, user->userOrGrpName.str);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutString(rawBuf, user->processName.str);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint16(rawBuf, user->reservedConnNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    // isAtomic
    ret = FixBufPutUint32(rawBuf, 0);
    if (ret != GMERR_OK) {
        return ret;
    }
    MsgWriterFinishPutOp(writer);
    logBuf->reqData = user;
    return QryReplicate(logBuf);
}

Status QryReplicateCreateGroup(LogBufferT *logBuf, CataGroupT *group)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_CONNECT) {
        return GMERR_OK;
    }

    // DDL统一使用同步方式
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_CREATE_GROUP);
    if (ret != GMERR_OK) {
        return ret;
    }
    FixBufferT *rawBuf = MsgWriterGetOpPayload(writer);
    ret = FixBufPutString(rawBuf, group->userOrGrpName.str);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutString(rawBuf, group->processName.str);
    if (ret != GMERR_OK) {
        return ret;
    }
    // isAtomic
    ret = FixBufPutUint32(rawBuf, 0);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutData(rawBuf, group->metaLimit, USER_META_TYPE_NUM_N * sizeof(uint32_t));
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgWriterFinishPutOp(writer);
    logBuf->reqData = group;
    return QryReplicate(logBuf);
}

Status QryReplicateDropGroup(LogBufferT *logBuf, CataGroupT *group)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_CONNECT) {
        return GMERR_OK;
    }

    // DDL统一使用同步方式
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_DROP_GROUP);
    if (ret != GMERR_OK) {
        return ret;
    }
    FixBufferT *rawBuf = MsgWriterGetOpPayload(writer);
    ret = FixBufPutString(rawBuf, group->userOrGrpName.str);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutString(rawBuf, group->processName.str);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint16(rawBuf, group->reservedConnNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    // isAtomic
    ret = FixBufPutUint32(rawBuf, 0);
    if (ret != GMERR_OK) {
        return ret;
    }
    MsgWriterFinishPutOp(writer);
    logBuf->reqData = group;
    return QryReplicate(logBuf);
}

Status QryReplicateGrantOrRevokeSysPrivs(
    LogBufferT *logBuf, CataUserNameInfoT *userNameInfo, PrivElemT *privileges, bool isGrant, bool toUser)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_GROUP) {
        return GMERR_OK;
    }

    // DCL 统一使用同步方式
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgOpcodeRpcE opCode = isGrant ? MSG_OP_RPC_GRANT_USER_SYSTEM_PRIVS : MSG_OP_RPC_REVOKE_USER_SYSTEM_PRIVS;
    ret = MsgWriterStartPutOp(writer, opCode);
    if (ret != GMERR_OK) {
        return ret;
    }
    FixBufferT *rawBuf = MsgWriterGetOpPayload(writer);
    char *uOrGName = toUser ? userNameInfo->userName : userNameInfo->groupName;
    ret = FixBufPutString(rawBuf, uOrGName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutString(rawBuf, userNameInfo->processName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(rawBuf, privileges->sysPrivs);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(rawBuf, privileges->objType);
    if (ret != GMERR_OK) {
        return ret;
    }
    // isAtomic
    ret = FixBufPutUint32(rawBuf, 0);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(rawBuf, (uint32_t)toUser);
    if (ret != GMERR_OK) {
        return ret;
    }
    MsgWriterFinishPutOp(writer);
    return QryReplicate(logBuf);
}

Status QryNeedReplicateObjPrivs(CataObjPrivT *objPrivs, bool *need)
{
    CataObjTypeE objType = objPrivs->objType;
    if (objType != CATA_VERTEX_LABEL && objType != CATA_KV_TABLE) {
        *need = objType == CATA_NAMESPACE;
        return GMERR_OK;
    }

    uint32_t nspId = 0;
    Status ret = CataGetNamespaceIdByName(NULL, objPrivs->namespaceName, &nspId);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmMetaCommonT *meta = NULL;
    CataKeyT cataKey = {0};
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, nspId, objPrivs->objName);
    if (objType == CATA_VERTEX_LABEL) {
        ret = CataGetVertexLabelByName(NULL, &cataKey, (DmVertexLabelT **)&meta);
        if (ret != GMERR_OK) {
            return ret;
        }
        *need = ((DmVertexLabelT *)meta)->commonInfo->replicationType != DM_NO_REPLICATION;
        (void)CataReleaseVertexLabel((DmVertexLabelT *)meta);
    } else {
        ret = CataGetLabelByName(&cataKey, (DmKvLabelT **)&meta, NULL);
        if (ret != GMERR_OK) {
            return ret;
        }
        *need = ((DmKvLabelT *)meta)->replicationType != DM_NO_REPLICATION;
        (void)CataReleaseLabel((DmKvLabelT *)meta);
    }
    return GMERR_OK;
}

Status QryReplicateFillGrantOrRevokeObjPrivs(MsgWriterT *writer, CataUserNameInfoT *userNameInfo,
    CataObjPrivT *objPrivs, QryReplicateObjPrivsParaT *replicateObjPrivsPara)
{
    MsgOpcodeRpcE opCode =
        replicateObjPrivsPara->isGrant ? MSG_OP_RPC_GRANT_OBJECT_PRIVS : MSG_OP_RPC_REVOKE_OBJECT_PRIVS;
    Status ret = MsgWriterStartPutOp(writer, opCode);
    if (ret != GMERR_OK) {
        return ret;
    }
    FixBufferT *rawBuf = MsgWriterGetOpPayload(writer);
    ret = FixBufPutUint32(rawBuf, objPrivs->objType);
    if (ret != GMERR_OK) {
        return ret;
    }
    char *uOrGName = replicateObjPrivsPara->toUser ? userNameInfo->userName : userNameInfo->groupName;
    ret = FixBufPutString(rawBuf, uOrGName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutString(rawBuf, userNameInfo->processName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutString(rawBuf, objPrivs->objName);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (objPrivs->objType != CATA_NAMESPACE) {
        ret = FixBufPutString(rawBuf, objPrivs->namespaceName);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    ret = FixBufPutUint32(rawBuf, objPrivs->privileges);
    if (ret != GMERR_OK) {
        return ret;
    }
    // isAtomic
    ret = FixBufPutUint32(rawBuf, 0);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(rawBuf, (uint32_t)replicateObjPrivsPara->toUser);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status QryReplicateGrantOrRevokeObjPrivs(LogBufferT *logBuf, CataUserNameInfoT *userNameInfo, CataObjPrivT *objPrivs,
    QryReplicateObjPrivsParaT *replicateObjPrivsPara)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_VERTEX_META) {
        return GMERR_OK;
    }
    if (!replicateObjPrivsPara->isFullReplicate) {
        bool need = false;
        ret = QryNeedReplicateObjPrivs(objPrivs, &need);
        if (ret != GMERR_OK || !need) {
            return ret;
        }
    }

    // DCL 统一使用同步方式
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    ret = QryReserveReplicationOpBody(writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryReplicateFillGrantOrRevokeObjPrivs(writer, userNameInfo, objPrivs, replicateObjPrivsPara);
    if (ret != GMERR_OK) {
        return ret;
    }

    MsgWriterFinishPutOp(writer);
    return QryReplicate(logBuf);
}

static Status QryReplicateGet(LogBufferT *logBuf, QryReplicationStatTypeE type, void *rsp)
{
    Status ret = GMERR_OK;
    if (QryGetFullReplicationPhase() <= FRP_CONNECT) {
        return GMERR_NO_DATA;
    }

    // 统一使用同步方式,没有实际意义
    logBuf->rop = (QryReplicationOpBodyT){.sync = true, .trxID = DB_INVALID_UINT64};
    MsgWriterT *writer = QryGetReplicationMsgWriter(logBuf);
    MsgWriterReset(writer);
    ret = MsgWriterStartPutOp(writer, MSG_OP_RPC_GET_REPLICATION_STAT);
    if (ret != GMERR_OK) {
        return ret;
    }

    FixBufferT *rawBuf = MsgWriterGetOpPayload(writer);
    ret = FixBufPutUint32(rawBuf, (uint32_t)type);
    if (ret != GMERR_OK) {
        return ret;
    }
    MsgWriterFinishPutOp(writer);
    logBuf->reqData = rsp;
    MsgWriterFinishNotMove(&logBuf->msgWriter);
    DbSpinLock(&g_gmdbReplication.lock);
    if (g_gmdbReplication.logQueue == NULL) {
        DbSpinUnlock(&g_gmdbReplication.lock);
        return GMERR_OK;  // 备下线时,容错处理
    }
    ret = QryWriteReplicationQueue(g_gmdbReplication.logQueue, logBuf);
    DbSpinUnlock(&g_gmdbReplication.lock);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 阻塞等待
    ret = DbSemWait(&logBuf->sem);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to replicate when waiting for semaphore.");
        return ret;
    }

    return logBuf->opStatus;
}

Status QryReplicateGetStat(LogBufferT *logBuf, QryReplicateStatT *stat)
{
    return QryReplicateGet(logBuf, RSTAT_STAT, stat);
}

Status QryReplicateGetLocalPort(LogBufferT *logBuf, char ip[], size_t ipLen, uint16_t *port)
{
    QryReplicationStatLocalPortT rsp = {.ip = ip, .ipLen = ipLen, .port = port};
    return QryReplicateGet(logBuf, RSTAT_LOCAL_PORT, &rsp);
}

Status QryReplicateFetchNameSpace(LogBufferT *logBuf, uint32_t *iter, uint32_t *masterID)
{
    QryReplicationStatMapT rsp = {.iter = iter, .masterID = masterID};
    return QryReplicateGet(logBuf, RSTAT_NAMESPACE, &rsp);
}

Status QryReplicateFetchVertexLabel(LogBufferT *logBuf, uint32_t *iter, uint32_t *masterID, uint32_t *slaveID)
{
    QryReplicationStatMapT rsp = {.iter = iter, .masterID = masterID, .slaveID = slaveID};
    return QryReplicateGet(logBuf, RSTAT_VERTEX_LABEL, &rsp);
}

Status QryReplicateFetchKvTable(LogBufferT *logBuf, uint32_t *iter, uint32_t *masterID, uint32_t *slaveID)
{
    QryReplicationStatMapT rsp = {.iter = iter, .masterID = masterID, .slaveID = slaveID};
    return QryReplicateGet(logBuf, RSTAT_KV_TABLE, &rsp);
}

#ifdef __cplusplus
}
#endif
