/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: ee_replication.h
 * Description: The API of replication.
 * Author:GMDBv5 EE TEAM
 * Create: 2024-12-24
 */

#ifndef EE_REPLICATION_H
#define EE_REPLICATION_H

#include "db_tuple_buffer.h"
#include "db_rpc_msg.h"
#include "dm_meta_prop_strudefs.h"
#include "dm_meta_kv_label.h"
#include "dm_meta_namespace.h"
#include "dm_meta_user.h"
#include "dm_meta_obj_priv.h"
#include "dm_data_index.h"
#include "ee_replication_logbuf.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct QryReplicateObjPrivsPara {
    bool isGrant;
    bool toUser;
    bool isFullReplicate;
} QryReplicateObjPrivsParaT;

// 复制模块的对外接口全部放在这个头文件
Status QrySetReplicationUrl(const char *url);
Status QryGetReplicationUrl(char url[DB_LCTR_MAX_LEN]);
uint32_t QryGetReplicationCount(void);
Status QryStartReplication(void);
void QryStopReplication(void);
bool QryFullReplicationIsEnd(void);

// 如果依赖session会导致循环依赖
Status QryReplicateTxStart(LogBufferT *logBuf, uint64_t trxID, bool replication);
Status QryReplicateTxCommit(LogBufferT *logBuf);
Status QryReplicateTxRollback(LogBufferT *logBuf);
Status QryReplicateNull(LogBufferT *logBuf);
Status QryReplicateCreateVertexLabel(LogBufferT *logBuf, DmVertexLabelT *vertexLabel);
Status QryReplicateDropVertexLabel(LogBufferT *logBuf, DmVertexLabelT *vertexLabel);
Status QryReplicateCreateKvTable(LogBufferT *logBuf, DmKvLabelT *kvLabel);
Status QryReplicateDropKvTable(LogBufferT *logBuf, DmKvLabelT *kvLabel);
Status QryReplicateCreateNameSpace(LogBufferT *logBuf, DmNamespaceT *nameSpace);
Status QryReplicateDropNameSpace(LogBufferT *logBuf, DmNamespaceT *nameSpace);
Status QryReplicateTruncateVertexLabel(LogBufferT *logBuf, DmVertexLabelT *vertexLabel);
Status QryReplicateTruncateVertexLabelBackground(LogBufferT *logBuf, DmVertexLabelT *vertexLabel, FixBufferT *req);
Status QryReplicateTruncateKvTable(LogBufferT *logBuf, DmKvLabelT *kvLabel);
Status QryReplicateReplaceVertex(LogBufferT *logBuf, TupleT tuple, DmVertexLabelT *vertexLabel);
Status QryReplicateDeleteVertex(LogBufferT *logBuf, TupleT tuple, DmVertexT *vertex, DmVertexLabelT *vertexLabel);
Status QryReplicateKvSet(LogBufferT *logBuf, TextT key, TextT value, DmKvLabelT *kvLabel);
Status QryReplicateKvRemove(LogBufferT *logBuf, TextT key, DmKvLabelT *kvLabel);
Status QryReplicateCreateUser(LogBufferT *logBuf, CataUserT *user);
Status QryReplicateDropUser(LogBufferT *logBuf, CataUserT *user);
Status QryReplicateCreateGroup(LogBufferT *logBuf, CataGroupT *group);
Status QryReplicateDropGroup(LogBufferT *logBuf, CataGroupT *group);
Status QryReplicateGrantOrRevokeSysPrivs(
    LogBufferT *logBuf, CataUserNameInfoT *userNameInfo, PrivElemT *privileges, bool isGrant, bool toUser);
Status QryReplicateGrantOrRevokeObjPrivs(LogBufferT *logBuf, CataUserNameInfoT *userNameInfo, CataObjPrivT *objPrivs,
    QryReplicateObjPrivsParaT *replicateObjPrivsPara);

// DFX
typedef struct {
    uint64_t logCount;     // 复制的逻辑日志消息数
    uint32_t minDuration;  // 最小时延us
    uint32_t maxDuration;  // 最大时延us
    uint32_t avgDuration;  // 平均时延us
} QryReplicateStatT;
Status QryReplicateGetStat(LogBufferT *logBuf, QryReplicateStatT *stat);
Status QryReplicateGetLocalPort(LogBufferT *logBuf, char ip[], size_t ipLen, uint16_t *port);
Status QryReplicateFetchNameSpace(LogBufferT *logBuf, uint32_t *iter, uint32_t *masterID);
Status QryReplicateFetchVertexLabel(LogBufferT *logBuf, uint32_t *iter, uint32_t *masterID, uint32_t *slaveID);
Status QryReplicateFetchKvTable(LogBufferT *logBuf, uint32_t *iter, uint32_t *masterID, uint32_t *slaveID);

#ifdef __cplusplus
}
#endif

#endif  // EE_REPLICATION_H
