/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: mini kv execute ddl
 * Author:
 * Create: 2023-12-18
 */

#include "ee_mini_ddl.h"
#include "dm_meta_minikv_label.h"

Status MiniExecuteCreateKvTable(MiniRunCtxT *runCtx)
{
    MiniKvLabelCreateCfgT *cfg = runCtx->execParams;
    if (cfg == NULL) {
        DB_LOG_ERROR(GMERR_FATAL, "kv label create cfg NULL");
        return GMERR_FATAL;
    }
    return CatalogCreateMiniKvLabel(cfg, runCtx);
}

Status MiniExecuteDropKvTable(MiniRunCtxT *runCtx)
{
    MiniKvLabelDropCfgT *cfg = runCtx->execParams;
    if (cfg == NULL) {
        DB_LOG_ERROR(GMERR_FATAL, "kv label drop cfg NULL");
        return GMERR_FATAL;
    }
    return CatalogDropMiniKvLabel(cfg, runCtx);
}
