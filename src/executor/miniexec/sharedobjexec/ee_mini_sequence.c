/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: mini sequence functions for distribution
 * Author: zhaoliang
 * Create: 2024-02-18
 */

#include "ee_mini_sequence.h"

#include "adpt_string.h"
#include "db_json.h"
#include "de_instance.h"
#include "dm_data_kv.h"
#include "ee_operation_history.h"
#include "ee_shared_obj.h"
#include "ee_mini_cursor.h"
#include "cpl_sequence_parser.h"
#include "ee_mini_shared_obj.h"
#include "ee_mini_shared_obj_attribute.h"
#include "ee_mini_shared_obj_context.h"
#include "ee_mini_sequence_inner.h"
#include "ee_mini_sequence_utils.h"
#include "gme_shared_obj.h"
#include "gme_types.h"
#include "se_trx.h"
#include "se_redo.h"
#include "srv_mini_shared_obj_service.h"

Status CreateLabelCursor(MiniRunCtxT *runCtx, const char *labelName)
{
    runCtx->labelCursor = runCtx->session->cacheCursor;
    MiniCursorCreateParamT param = {.labelName = labelName,
        .memCtx = runCtx->memCtx,
        .dbId = runCtx->session->dbId,
        .dbInstance = runCtx->session->sessionPool->dbInstance,
        .seRunCtx = runCtx->session->seRunCtx};

    Status ret = MiniCreateKvTableCursor(&param, &runCtx->labelCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Sequence create kv table cursor wrong");
    }
    return ret;
}

static Status WriteDeleteOperationToLog(MiniRunCtxT *runCtx, DmOperationLogBodyT *operationLogBody)
{
    if (operationLogBody->deleteSize == 0) {
        return GMERR_OK;
    }

    SequenceOperationCfgT *sequenceOpCfg = runCtx->execParams;
    operationLogBody->sharedObjInfo.memCtx = runCtx->memCtx;
    operationLogBody->sharedObjInfo.sharedObjType = GME_SEQUENCE_TYPE;
    operationLogBody->sharedObjInfo.sharedObjNameLength = sequenceOpCfg->sequenceName.len;
    operationLogBody->sharedObjInfo.sharedObjName = sequenceOpCfg->sequenceName.str;
    operationLogBody->equipIdLength = DM_STR_LEN(DeEquipIdGet());
    operationLogBody->equipId = (char *)(uintptr_t)DeEquipIdGet();

    uint8_t *logBuf = NULL;
    uint32_t logBufLen = 0u;
    Status ret = DmSerializeOperationLogBody(operationLogBody, &logBuf, &logBufLen);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong when serialize delete operation log body");
        return ret;
    }

    ret = MiniCreateResponseForLogWrite(runCtx, logBuf, logBufLen);
    DbDynMemCtxFree(runCtx->memCtx, logBuf);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong when create response for log write");
        return ret;
    }

    return OpHistoryRecord(runCtx, operationLogBody);
}

static inline Status AttributeDeletedAndWriteToDb(
    MiniRunCtxT *runCtx, DmBaseElementT *element, TagLinkedListT *operationDeletes, uint32_t *deleteSize)
{
    Status ret = AddDeletedOperationToList(element, runCtx->memCtx, operationDeletes);
    if (ret != GMERR_OK) {
        return ret;
    }
    (*deleteSize)++;
    return WriteDeletedElementToDb(runCtx, element);
}

static Status FindElementAccordingToLength(DelElementInfoT *findElementInfo)
{
    DmBaseSequenceT *sequence = (DmBaseSequenceT *)findElementInfo->runCtx->memSharedObj->sharedObj;
    DmBaseElementT *nextElement = findElementInfo->startElement;
    Status ret = GMERR_OK;
    while (findElementInfo->count > 0) {
        if (nextElement == NULL) {
            break;
        }
        if (nextElement->isDelete) {
            nextElement = nextElement->next;
            continue;
        }
        if (nextElement->type == DM_ELEMENT_ATTRIBUTE_TYPE) {
            nextElement = nextElement->next;
            continue;
        }

        // Large Element, split it and get left element
        if (findElementInfo->count < nextElement->length) {
            ret = MiniSplitElement(sequence, nextElement, findElementInfo->count);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        // Small Element, add it.
        findElementInfo->count -= nextElement->length;
        if (findElementInfo->isMarkedDeleted) {
            nextElement->isDelete = true;
            MiniReleaseMemElementContent(sequence->memCtx, nextElement);
            ret = WriteDeletedElementToDb(findElementInfo->runCtx, nextElement);
            if (ret != GMERR_OK) {
                return ret;
            }

            findElementInfo->operationLogBody->deleteSize++;
            ret = AddDeletedOperationToList(
                nextElement, findElementInfo->runCtx->memCtx, &findElementInfo->operationLogBody->operationDeletes);
            if (ret != GMERR_OK) {
                EmbeddedReleaseOperationDeletedList(
                    findElementInfo->runCtx->memCtx, &findElementInfo->operationLogBody->operationDeletes);
                return ret;
            }
        }
        nextElement = nextElement->next;
    }

    findElementInfo->findElement = nextElement;
    return ret;
}

static Status PersistElementToDb(AttributeOperateParamT *attrOpParam, DmBaseElementT *element)
{
    uint32_t flag = 0u;
    Status ret = GetElementFlagAndOriginPrevNext(attrOpParam->runCtx->memCtx, element, &flag);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (element->originPrev != NULL) {
        DbLinkedListInsert(&attrOpParam->toDeleteIdElements, &element->originPrev->linkedNode);
    }
    if (element->originNext != NULL) {
        DbLinkedListInsert(&attrOpParam->toDeleteIdElements, &element->originNext->linkedNode);
    }
    ret = InsertElementToDb(attrOpParam->runCtx, element, flag);
    if (ret != GMERR_OK) {
        return ret;
    }

    return CreateOpElementAddToList(attrOpParam, element, flag);
}

Status LoadSharedObjFromDb(MiniRunCtxT *runCtx, SequenceOperationCfgT *sequenceOpCfg)
{
    // Should never be NULL.
    DB_POINTER(runCtx->labelCursor);
    runCtx->memSharedObj = NULL;
    Status ret = MiniCreateMemSharedObj(&sequenceOpCfg->labelName, runCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Sequence operation read from table go wrong");
    }

    return ret;
}

Status MarkElementAsDeleted(MiniRunCtxT *runCtx, uint32_t index, uint32_t length)
{
    DmBaseSequenceT *sequence = (DmBaseSequenceT *)runCtx->memSharedObj->sharedObj;
    DmOperationLogBodyT operationLogBody = {0};
    DbLinkedListInit(&operationLogBody.operationElements);
    DbLinkedListInit(&operationLogBody.operationDeletes);
    // Find the element according to index.
    DelElementInfoT findElementInfo = {
        .runCtx = runCtx,
        .startElement = sequence->start,
        .count = index,
        .isMarkedDeleted = false,
        .findElement = NULL,
        .operationLogBody = &operationLogBody,
    };

    Status ret = FindElementAccordingToLength(&findElementInfo);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    // Find the element according to length.
    findElementInfo.startElement = findElementInfo.findElement;
    findElementInfo.count = length;
    findElementInfo.isMarkedDeleted = true;
    findElementInfo.findElement = NULL;
    ret = FindElementAccordingToLength(&findElementInfo);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = WriteDeleteOperationToLog(runCtx, &operationLogBody);

EXIT:
    EmbeddedReleaseOperationDeletedList(runCtx->memCtx, &operationLogBody.operationDeletes);
    return ret;
}

Status CheckParamAndCreateLabelCursor(MiniRunCtxT *runCtx)
{
    DB_POINTER(runCtx);
    SequenceOperationCfgT *sequenceOpCfg = runCtx->execParams;
    if (SECUREC_UNLIKELY(sequenceOpCfg == NULL)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Sequence operation cfg NULL");
        return GMERR_INTERNAL_ERROR;
    }

    Status ret = CreateLabelCursor(runCtx, sequenceOpCfg->labelName.str);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Sequence operation scan label cursor go wrong");
    }

    return ret;
}

static Status CreatePositionElement(
    MiniSharedObjQryTypeE type, SequenceOperationCfgT *sequenceOpCfg, AttributeOperateParamT *attrOpParam)
{
    char *equipId = NULL;
    uint32_t nextClock = 0u;
    Status ret = GetEquipIdAndNextClock(attrOpParam->curSequence, &equipId, &nextClock);
    if (ret != GMERR_OK) {
        return ret;
    }

    PositionElementT *positionElement =
        (PositionElementT *)DbDynMemCtxAlloc(attrOpParam->curSequence->memCtx, sizeof(PositionElementT));
    if (SECUREC_UNLIKELY(positionElement == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for position element");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(positionElement, sizeof(PositionElementT), 0, sizeof(PositionElementT));
    // This equip id alloc in vectorClocks, here is a pointer.
    positionElement->equipId = equipId;
    positionElement->nextClock = nextClock;
    positionElement->prev = NULL;
    positionElement->next = NULL;
    if (type == MINI_QRY_TYPE_SEQUENCE_INSERT) {
        positionElement->content.content = sequenceOpCfg->content.str;
        positionElement->content.byteLength = sequenceOpCfg->content.len;
    }
    attrOpParam->element = positionElement;
    return GMERR_OK;
}

static Status CreateAttributeElement(
    AttributeOperateParamT *attrOpParam, DmBaseAttributeT *targetAttribute, DmBaseElementT **element)
{
    DmBaseElementT *newElement =
        (DmBaseElementT *)DbDynMemCtxAlloc(attrOpParam->runCtx->memCtx, sizeof(DmBaseElementT));
    if (SECUREC_UNLIKELY(newElement == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for new element");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(newElement, sizeof(DmBaseElementT), 0, sizeof(DmBaseElementT));
    newElement->elementId.equipId = attrOpParam->element->equipId;
    newElement->elementId.incrClock = attrOpParam->element->nextClock++;
    newElement->originPrev = NULL;
    newElement->originNext = NULL;
    newElement->isDelete = false;
    newElement->parent = attrOpParam->curSequence;
    newElement->type = DM_ELEMENT_ATTRIBUTE_TYPE;
    // attribute element's length is 0
    newElement->length = 0u;
    newElement->baseContent = targetAttribute;
    newElement->prev = attrOpParam->element->prev;
    newElement->next = attrOpParam->element->next;

    *element = newElement;
    attrOpParam->element->prev = newElement;
    DbLinkedListInsert(&attrOpParam->toDeleteAttrElements, &newElement->linkedNode);
    return GMERR_OK;
}

static Status AttributeCopy(DbMemCtxT *memCtx, DmBaseAttributeT *srcAttribute, DmBaseAttributeT **dstAttribute)
{
    char *key = (char *)DbDynMemCtxAlloc(memCtx, srcAttribute->keyLen);
    if (SECUREC_UNLIKELY(key == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc key for attribute copy.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memcpy_s(key, srcAttribute->keyLen, srcAttribute->key, srcAttribute->keyLen);
    char *value = NULL;
    if (srcAttribute->valueType == DM_ATTRIBUTE_VALUE_STRING_START ||
        srcAttribute->valueType == DM_ATTRIBUTE_VALUE_STRING_END) {
        value = (char *)DbDynMemCtxAlloc(memCtx, srcAttribute->valueLen);
        if (SECUREC_UNLIKELY(value == NULL)) {
            DbDynMemCtxFree(memCtx, key);
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc value for attribute copy.");
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memcpy_s(value, srcAttribute->valueLen, srcAttribute->value, srcAttribute->valueLen);
    }
    DmBaseAttributeT *attribute = (DmBaseAttributeT *)DbDynMemCtxAlloc(memCtx, sizeof(DmBaseAttributeT));
    if (SECUREC_UNLIKELY(attribute == NULL)) {
        DbDynMemCtxFree(memCtx, key);
        DbDynMemCtxFree(memCtx, value);
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for attribute copy.");
        return GMERR_OUT_OF_MEMORY;
    }
    attribute->key = key;
    attribute->keyLen = srcAttribute->keyLen;
    attribute->value = value;
    attribute->valueLen = srcAttribute->valueLen;
    attribute->valueType = srcAttribute->valueType;
    *dstAttribute = attribute;
    return GMERR_OK;
}

Status AddAttrWithCountToMap(DbMemCtxT *memCtx, DmBaseAttributeT *attribute, uint32_t index, DbOamapT *attributeMap)
{
    DmBaseAttributeT *newAttribute = NULL;
    Status ret = AttributeCopy(memCtx, attribute, &newAttribute);
    if (ret != GMERR_OK) {
        return ret;
    }
    switch (newAttribute->valueType) {
        case DM_ATTRIBUTE_VALUE_NULL_START:
        case DM_ATTRIBUTE_VALUE_STRING_START:
        case DM_ATTRIBUTE_VALUE_BOOL_START:
            ret = AttributeMapInsertWithCount(memCtx, attributeMap, newAttribute, index);  // count++
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "add attribute with count to map wrong.");
                goto EXIT;
            }
            break;
        case DM_ATTRIBUTE_VALUE_NULL_END:
        case DM_ATTRIBUTE_VALUE_STRING_END:
        case DM_ATTRIBUTE_VALUE_BOOL_END:
            ret = AttributeWithCountMapRemove(memCtx, attributeMap, newAttribute);  // count--
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "remove attribute with count from map wrong.");
                goto EXIT;
            }
            break;
        default:
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "wrong attribute value.");
            ret = GMERR_INTERNAL_ERROR;
            goto EXIT;
    }
    return GMERR_OK;
EXIT:
    FreeBaseAttribute(memCtx, newAttribute);
    return ret;
}

static Status FindElementAndAttributes(
    DmBaseSequenceT *sequence, uint32_t index, DbOamapT *attributesMap, PositionElementT *element, bool attrNull)
{
    DmBaseElementT moveElement = {0};
    moveElement.prev = NULL;
    moveElement.next = sequence->start;
    uint32_t count = index;
    DbMemCtxT *memCtx = sequence->memCtx;

    while (moveElement.next != NULL && count > 0) {
        if (moveElement.next->isDelete) {
            moveElement.prev = moveElement.next;
            moveElement.next = moveElement.next->next;
            continue;
        }
        if (count < moveElement.next->length) {
            Status ret = MiniSplitElement(sequence, moveElement.next, count);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        // if element is attribute, it will be added into map.
        if (moveElement.next->type == DM_ELEMENT_ATTRIBUTE_TYPE) {
            Status ret =
                AddAttrWithCountToMap(memCtx, (DmBaseAttributeT *)moveElement.next->baseContent, 0u, attributesMap);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        count -= moveElement.next->length;
        moveElement.prev = moveElement.next;
        moveElement.next = moveElement.next->next;
    }
    while (!attrNull && moveElement.next != NULL && moveElement.next->type == DM_ELEMENT_ATTRIBUTE_TYPE) {
        if (!moveElement.next->isDelete) {
            Status ret =
                AddAttrWithCountToMap(memCtx, (DmBaseAttributeT *)moveElement.next->baseContent, 0u, attributesMap);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        moveElement.prev = moveElement.next;
        moveElement.next = moveElement.next->next;
    }
    if (element->prev == NULL && element->next == NULL) {
        element->prev = moveElement.prev;
        element->next = moveElement.next;
    }
    return GMERR_OK;
}

static Status TransformAttrPairIntoMap(
    void *memCtx, uint16_t attrsItemSize, AttributePairT *attrValuesArray, DbOamapT *paramsAttrs)
{
    if (attrValuesArray == NULL) {
        return GMERR_OK;
    }
    for (uint16_t i = 0u; i < attrsItemSize; i++) {
        DmBaseAttributeT attribute = {
            .key = attrValuesArray[i].name.str,
            .keyLen = attrValuesArray[i].name.len,
            .value = attrValuesArray[i].value.str,
            .valueLen = attrValuesArray[i].valueLen,
            .valueType = attrValuesArray[i].valueType,
        };

        Status ret = AddAttrWithCountToMap(memCtx, &attribute, 0u, paramsAttrs);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status CopyAttrMapToParamsMap(void *memCtx, DbOamapT *attributesMap, DbOamapT *paramsAttrs)
{
    for (uint32_t index = 0u; index < attributesMap->capacity; ++index) {
        const DbOamapBucketT *bucket = &attributesMap->buckets[index];
        if (bucket == NULL || bucket->state != USED) {
            continue;
        }
        DmBaseAttributeWithCountT *attrWithCount = (DmBaseAttributeWithCountT *)bucket->value;
        if (attrWithCount->count <= 0) {
            continue;
        }
        // only copy attribute when count > 0
        for (int i = 0; i < attrWithCount->count; i++) {
            Status ret = AddAttrWithCountToMap(memCtx, attrWithCount->attribute, 0u, paramsAttrs);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static Status AttributePersistElementToDb(AttributeOperateParamT *attrOpParam, DmBaseAttributeT *attribute)
{
    DmBaseElementT *attributeElement = NULL;
    Status ret = CreateAttributeElement(attrOpParam, attribute, &attributeElement);
    if (ret != GMERR_OK) {
        return ret;
    }
    return PersistElementToDb(attrOpParam, attributeElement);
}

inline static bool AttrStrCmp(const char *targetKey, char **deleteKeys, uint32_t deleteKeyNum)
{
    for (uint32_t i = 0; i < deleteKeyNum; ++i) {
        if (DbStrCmp(targetKey, *(deleteKeys + i), false) == 0) {
            return true;
        }
    }
    return false;
}

static Status AttrEndAdd(
    AttributeOperateParamT *attrOpParam, char **key, uint32_t deleteKeyNum, MiniSharedObjQryTypeE type)
{
    for (uint32_t index = 0u; index < attrOpParam->attributesMap.capacity; ++index) {
        const DbOamapBucketT *bucket = &attrOpParam->attributesMap.buckets[index];
        if (bucket == NULL || bucket->state != USED) {
            continue;
        }
        DmBaseAttributeWithCountT *attrWithCount = (DmBaseAttributeWithCountT *)bucket->value;
        if (!attrOpParam->attrNull && type != MINI_QRY_TYPE_SEQUENCE_INSERT &&
            type != MINI_QRY_TYPE_SEQUENCE_EMBED_INSERT &&
            !AttrStrCmp(attrWithCount->attribute->key, key, deleteKeyNum)) {
            continue;
        }
        DmBaseAttributeWithCountT *findAttrWithCount = NULL;
        Status ret = AttributeMapWithCountFind(&attrOpParam->paramsAttrs, attrWithCount->attribute, &findAttrWithCount);
        if (ret != GMERR_OK) {
            return ret;
        }
        if ((findAttrWithCount != NULL && findAttrWithCount->count >= attrWithCount->count) ||
            attrWithCount->count == 0) {
            continue;
        }

        DmBaseAttributeT endAttribute = {0};
        AttributeOppositeInit(attrWithCount->attribute, &endAttribute);
        int attrCount =
            (findAttrWithCount == NULL) ? attrWithCount->count : attrWithCount->count - findAttrWithCount->count;
        for (int i = 0; i < attrCount; i++) {
            ret = AttributePersistElementToDb(attrOpParam, &endAttribute);
            if (ret != GMERR_OK) {
                return ret;
            }
            ret = AddAttrWithCountToMap(
                attrOpParam->runCtx->memCtx, attrWithCount->attribute, 0u, &attrOpParam->oppositeAttrs);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static Status AttrStartAdd(AttributeOperateParamT *attrOpParam)
{
    for (uint32_t index = 0; index < attrOpParam->paramsAttrs.capacity; ++index) {
        const DbOamapBucketT *bucket = &attrOpParam->paramsAttrs.buckets[index];
        if (bucket == NULL || bucket->state != USED) {
            continue;
        }
        DmBaseAttributeWithCountT *attrWithCount = (DmBaseAttributeWithCountT *)bucket->value;
        if (attrWithCount->attribute->valueType == DM_ATTRIBUTE_VALUE_NULL_START) {
            continue;
        }
        DmBaseAttributeWithCountT *findAttrWithCount = NULL;
        Status ret =
            AttributeMapWithCountFind(&attrOpParam->attributesMap, attrWithCount->attribute, &findAttrWithCount);
        if (ret != GMERR_OK) {
            return ret;
        }
        if ((findAttrWithCount != NULL && findAttrWithCount->count >= attrWithCount->count) ||
            attrWithCount->count == 0) {
            continue;
        }
        int attrCount =
            (findAttrWithCount == NULL) ? attrWithCount->count : attrWithCount->count - findAttrWithCount->count;
        DmBaseAttributeT endAttribute = {0};
        AttributeOppositeInit(attrWithCount->attribute, &endAttribute);

        for (int i = 0; i < attrCount; i++) {
            ret = AttributePersistElementToDb(attrOpParam, attrWithCount->attribute);
            if (ret != GMERR_OK) {
                return ret;
            }
            ret = AddAttrWithCountToMap(attrOpParam->runCtx->memCtx, &endAttribute, 0u, &attrOpParam->oppositeAttrs);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static Status InsertLeftAttrs(
    AttributeOperateParamT *attrOpParam, char **deleteKeys, uint32_t deleteKeyNums, MiniSharedObjQryTypeE type)
{
    Status ret = AttrEndAdd(attrOpParam, deleteKeys, deleteKeyNums, type);
    if (ret != GMERR_OK) {
        return ret;
    }
    return AttrStartAdd(attrOpParam);
}

static Status MoveProcessOppositeAttr(AttributeOperateParamT *attrOpParam)
{
    // only process when assign attribute. For example, assign with <B> for content <A>abc</A>.
    // Final content is <B><A(d)>abc</B></A(d)>. Attribute </A> will be deleted here.
    DmBaseElementT *element = attrOpParam->element->next;
    while (element != NULL && element->type == DM_ELEMENT_ATTRIBUTE_TYPE) {
        DmBaseAttributeT *attribute = (DmBaseAttributeT *)element->baseContent;
        if (element->isDelete || IsAttributeValueTypeStart(attribute->valueType)) {
            element = element->next;
            continue;
        }
        // only process end attribute.
        Status ret = GMERR_OK;
        DmBaseAttributeWithCountT *findStartAttrWithCount = NULL;
        ret = AttributeMapWithCountFind(&attrOpParam->assignMoveAttrs, attribute, &findStartAttrWithCount);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (findStartAttrWithCount == NULL || findStartAttrWithCount->count <= 0) {
            element = element->next;
            continue;
        }
        ret = AttributeDeletedAndWriteToDb(
            attrOpParam->runCtx, element, attrOpParam->operationDeletes, &attrOpParam->deleteSize);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = AddAttrWithCountToMap(attrOpParam->runCtx->memCtx, attribute, 0u, &attrOpParam->assignMoveAttrs);
        if (ret != GMERR_OK) {
            return ret;
        }
        element = element->next;
    }
    return GMERR_OK;
}

static Status EndAttributeWriteToDb(AttributeOperateParamT *attrOpParam, DmBaseAttributeWithCountT *attrWithCount)
{
    if (attrWithCount->attribute->valueType == DM_ATTRIBUTE_VALUE_NULL_START) {
        return GMERR_OK;
    }
    DmBaseAttributeT endAttribute = {0};
    AttributeOppositeInit(attrWithCount->attribute, &endAttribute);
    int countAttr = -attrWithCount->count;
    DB_ASSERT(countAttr > 0);
    for (int i = 0; i < countAttr; i++) {
        Status ret = AttributePersistElementToDb(attrOpParam, &endAttribute);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = AddAttrWithCountToMap(
            attrOpParam->runCtx->memCtx, attrWithCount->attribute, 0u, &attrOpParam->oppositeAttrs);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static bool isEndAttributeNotNeedWrite(const DbOamapBucketT *bucket)
{
    if (bucket == NULL || bucket->state != USED) {
        return true;
    }

    DmBaseAttributeWithCountT *attrWithCount = (DmBaseAttributeWithCountT *)bucket->value;
    return attrWithCount->count >= 0;
}

static Status InsertRightEndAttrForAssign(AttributeOperateParamT *attrOpParam)
{
    // For example, <A>abc</A>123<B>456</B>, assign index=2 len=3 with attribute <A>.
    // Final content is <A>abc</A(d)>12</A>3<B>456</B>.
    for (uint32_t index = 0; index < attrOpParam->assignMoveAttrs.capacity; ++index) {
        const DbOamapBucketT *assignMoveAttrsBucket = &attrOpParam->assignMoveAttrs.buckets[index];
        if (isEndAttributeNotNeedWrite(assignMoveAttrsBucket)) {
            continue;
        }

        Status ret = EndAttributeWriteToDb(attrOpParam, (DmBaseAttributeWithCountT *)assignMoveAttrsBucket->value);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status InsertRightEndAttr(AttributeOperateParamT *attrOpParam)
{
    // Means that attributes may be reused.
    if (attrOpParam->oppositeAttrs.size == 0) {
        return InsertRightEndAttrForAssign(attrOpParam);
    }
    for (uint32_t index = 0; index < attrOpParam->oppositeAttrs.capacity; ++index) {
        const DbOamapBucketT *oppositeAttrsBucket = &attrOpParam->oppositeAttrs.buckets[index];
        if (isEndAttributeNotNeedWrite(oppositeAttrsBucket)) {
            continue;
        }

        Status ret = EndAttributeWriteToDb(attrOpParam, (DmBaseAttributeWithCountT *)oppositeAttrsBucket->value);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status InsertRightStartAttrForAssign(
    AttributeOperateParamT *attrOpParam, char **key, uint32_t deleteKeyNum, MiniSharedObjQryTypeE type)
{
    for (uint32_t index = 0; index < attrOpParam->assignMoveAttrs.capacity; ++index) {
        const DbOamapBucketT *bucket = &attrOpParam->assignMoveAttrs.buckets[index];
        if (bucket == NULL || bucket->state != USED) {
            continue;
        }
        DmBaseAttributeWithCountT *attrWithCount = (DmBaseAttributeWithCountT *)bucket->value;
        if (attrWithCount->count <= 0 ||
            (type != MINI_QRY_TYPE_SEQUENCE_INSERT && !AttrStrCmp(attrWithCount->attribute->key, key, deleteKeyNum))) {
            continue;
        }
        DB_ASSERT(attrWithCount->count > 0);
        for (int i = 0; i < attrWithCount->count; i++) {
            Status ret = AttributePersistElementToDb(attrOpParam, attrWithCount->attribute);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static Status InsertRightStartAttr(
    AttributeOperateParamT *attrOpParam, bool isAssign, char **key, uint32_t deleteKeyNum, MiniSharedObjQryTypeE type)
{
    for (uint32_t index = 0; index < attrOpParam->oppositeAttrs.capacity; ++index) {
        const DbOamapBucketT *bucket = &attrOpParam->oppositeAttrs.buckets[index];
        if (bucket == NULL || bucket->state != USED) {
            continue;
        }
        DmBaseAttributeWithCountT *attrWithCount = (DmBaseAttributeWithCountT *)bucket->value;
        if (attrWithCount->count <= 0 || (!attrOpParam->attrNull && type != MINI_QRY_TYPE_SEQUENCE_INSERT &&
                                             type != MINI_QRY_TYPE_SEQUENCE_EMBED_INSERT &&
                                             !AttrStrCmp(attrWithCount->attribute->key, key, deleteKeyNum))) {
            continue;
        }
        if (isAssign) {
            DmBaseAttributeWithCountT *findAttrWithCount = NULL;
            Status ret =
                AttributeMapWithCountFind(&attrOpParam->assignMoveAttrs, attrWithCount->attribute, &findAttrWithCount);
            if (ret != GMERR_OK) {
                return ret;
            }
            if (findAttrWithCount != NULL && findAttrWithCount->count < 0) {
                attrWithCount->count += findAttrWithCount->count;
            }
        }
        DmBaseAttributeT endAttribute = {0};
        AttributeOppositeInit(attrWithCount->attribute, &endAttribute);
        for (int i = 0; i < attrWithCount->count; i++) {
            Status ret = AttributePersistElementToDb(attrOpParam, attrWithCount->attribute);
            if (ret != GMERR_OK) {
                return ret;
            }
            ret = AddAttrWithCountToMap(attrOpParam->runCtx->memCtx, &endAttribute, 0u, &attrOpParam->oppositeAttrs);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    if (!isAssign) {
        return GMERR_OK;
    }
    return InsertRightStartAttrForAssign(attrOpParam, key, deleteKeyNum, type);
}

static Status IsContainsInParamMap(DmBaseAttributeT *attribute, DmBaseAttributeWithCountT *attrWithCount,
    DbOamapT *paramsAttrs, bool *isContainsInParam)
{
    DmBaseAttributeWithCountT *findParamAttrWithCount = NULL;
    Status ret = AttributeMapWithCountFind(paramsAttrs, attribute, &findParamAttrWithCount);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (findParamAttrWithCount == NULL) {
        *isContainsInParam = false;
        return GMERR_OK;
    }
    *isContainsInParam = (attrWithCount->count >= findParamAttrWithCount->count);
    return GMERR_OK;
}

typedef struct MovePositionInfo {
    DbListT list;
    bool isStay;   // True: need to move the insert position.
    bool isBreak;  // Break the loop of moving the insert position when end attribute is encountered.
    DmBaseElementT *prev;
    DmBaseElementT *next;
} MovePositionInfoT;

static Status MoveWhenEndAttribute(
    DbOamapT *attributesMap, DbOamapT *paramsAttrs, DmBaseAttributeT *attribute, MovePositionInfoT *movePositionInfoT)
{
    // <A><A>abc</A></A>, insert content "123" with attribute <C> after "c".
    // The final content is <A><A>abc</A></A><C>123</C>.
    DmBaseAttributeWithCountT *findAttrWithCount = NULL;
    Status ret = AttributeMapWithCountFind(attributesMap, attribute, &findAttrWithCount);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (findAttrWithCount == NULL || findAttrWithCount->count <= 0) {
        movePositionInfoT->isBreak = true;
        return GMERR_OK;
    }
    bool isContainsInParam = false;
    if (attributesMap->size == paramsAttrs->size) {
        ret = IsContainsInParamMap(attribute, findAttrWithCount, paramsAttrs, &isContainsInParam);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (isContainsInParam) {
            return DbAppendListItem(&movePositionInfoT->list, &attribute);
        }
    }
    findAttrWithCount->count--;
    if (findAttrWithCount->count == 0) {
        ret = AttributeWithCountMapRemoveAll(attributesMap->memCtx, attributesMap, findAttrWithCount->attribute);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    movePositionInfoT->isStay = false;
    return GMERR_OK;
}

static Status MovePosition(DbMemCtxT *memCtx, DbOamapT *attributesMap, DbOamapT *paramsAttrs,
    MovePositionInfoT *movePositionInfoT, PositionElementT *element)
{
    if (!movePositionInfoT->isStay) {
        element->prev = movePositionInfoT->prev;
        element->next = movePositionInfoT->next;
        uint32_t count = DbListGetItemCnt(&movePositionInfoT->list);
        DmBaseAttributeT *item = NULL;
        for (uint32_t i = 0; i < count; ++i) {
            item = *(DmBaseAttributeT **)DbListItem(&movePositionInfoT->list, i);
            Status ret = AttributeWithCountMapRemoveAll(memCtx, attributesMap, item);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        return GMERR_OK;
    }
    uint32_t count = DbListGetItemCnt(&movePositionInfoT->list);
    DmBaseAttributeT *item = NULL;
    for (uint32_t i = 0; i < count; ++i) {
        item = *(DmBaseAttributeT **)DbListItem(&movePositionInfoT->list, i);
        DmBaseAttributeWithCountT *findAttrWithCount = NULL;
        DmBaseAttributeWithCountT *findParamsAttrWithCount = NULL;
        Status ret = AttributeMapWithCountFind(attributesMap, item, &findAttrWithCount);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = AttributeMapWithCountFind(paramsAttrs, item, &findParamsAttrWithCount);
        if (ret != GMERR_OK) {
            return ret;
        }
        DB_ASSERT(findAttrWithCount != NULL);
        DB_ASSERT(findParamsAttrWithCount != NULL);
        findParamsAttrWithCount->count = findAttrWithCount->count;
    }
    return GMERR_OK;
}

static Status MovePositionToAfterAttribute(
    DbMemCtxT *memCtx, PositionElementT *element, DbOamapT *attributesMap, DbOamapT *paramsAttrs)
{
    MovePositionInfoT movePositionInfoT = {0};
    movePositionInfoT.prev = element->prev;
    movePositionInfoT.next = element->next;
    movePositionInfoT.isStay = true;
    DbCreateListWithExtendSize(
        &movePositionInfoT.list, sizeof(DmBaseAttributeT *), DEFAULT_DB_LIST_EXTEND_SIZE, memCtx);
    Status ret = GMERR_OK;
    while (movePositionInfoT.next != NULL) {
        if (movePositionInfoT.next->isDelete) {
            movePositionInfoT.prev = movePositionInfoT.next;
            movePositionInfoT.next = movePositionInfoT.next->next;
            continue;
        }
        if (movePositionInfoT.next->type != DM_ELEMENT_ATTRIBUTE_TYPE) {
            break;
        }
        DmBaseAttributeT *attribute = (DmBaseAttributeT *)movePositionInfoT.next->baseContent;
        if (IsAttributeValueTypeStart(attribute->valueType)) {
            break;
        }
        ret = MoveWhenEndAttribute(attributesMap, paramsAttrs, attribute, &movePositionInfoT);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
        if (movePositionInfoT.isBreak) {
            break;
        }
        movePositionInfoT.prev = movePositionInfoT.next;
        movePositionInfoT.next = movePositionInfoT.next->next;
    }
    ret = MovePosition(memCtx, attributesMap, paramsAttrs, &movePositionInfoT, element);

EXIT:
    DbDestroyList(&movePositionInfoT.list);
    return ret;
}

static Status InsertRightAttrs(AttributeOperateParamT *attrOpParam, bool isAssign, char **deleteKeys,
    uint32_t deleteKeyNums, MiniSharedObjQryTypeE type)
{
    Status ret = GMERR_OK;
    if (isAssign) {
        ret = MoveProcessOppositeAttr(attrOpParam);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    ret = InsertRightEndAttr(attrOpParam);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong insert the right end attribute.");
        return ret;
    }
    ret = InsertRightStartAttr(attrOpParam, isAssign, deleteKeys, deleteKeyNums, type);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong insert the right start attribute.");
    }
    return ret;
}

static Status InitAttributeOperateParam(MiniSharedObjQryTypeE type, SequenceOperationCfgT *sequenceOpCfg,
    AttributeOperateParamT *attrOpParam, DmOperationLogBodyT *operationLogBody)
{
    Status ret = CreatePositionElement(type, sequenceOpCfg, attrOpParam);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create inserted element go wrong");
        return ret;
    }

    ret = FindElementAndAttributes(attrOpParam->curSequence, sequenceOpCfg->index, &attrOpParam->attributesMap,
        attrOpParam->element, attrOpParam->attrNull);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "find element and attributes map go wrong");
        return ret;
    }

    if (sequenceOpCfg->attrsItemSize == SEQUENCE_ATTR_NULL && sequenceOpCfg->attrValuesArray == NULL) {
        ret =
            CopyAttrMapToParamsMap(attrOpParam->runCtx->memCtx, &attrOpParam->attributesMap, &attrOpParam->paramsAttrs);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "copy attributes map to params map go wrong");
            return ret;
        }
    } else {
        ret = TransformAttrPairIntoMap(attrOpParam->runCtx->memCtx, sequenceOpCfg->attrsItemSize,
            sequenceOpCfg->attrValuesArray, &attrOpParam->paramsAttrs);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "transform attribute pair to map go wrong");
            return ret;
        }
    }
    // if the element is attribute after the index element
    ret = MovePositionToAfterAttribute(
        attrOpParam->runCtx->memCtx, attrOpParam->element, &attrOpParam->attributesMap, &attrOpParam->paramsAttrs);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "move position go wrong when attribute after position");
        return ret;
    }
    operationLogBody->startClock = attrOpParam->element->nextClock;
    return ret;
}

static Status SeqInsertContentElement(AttributeOperateParamT *attrOpParam, SequenceOperationCfgT *sequenceOpCfg)
{
    DmBaseElementT *contentElement = NULL;
    DmBaseElementInfoT elementInfo = {
        .type = DM_ELEMENT_STRING_TYPE,
        .elementId.equipId = attrOpParam->element->equipId,
        .elementId.incrClock = attrOpParam->element->nextClock,
        .string =
            {
                .byteLength = sequenceOpCfg->content.len,
                .content = sequenceOpCfg->content.str,
            },
    };

    int32_t characterLength = Utf8CharacterLengthGet(sequenceOpCfg->content.str);
    if (SECUREC_UNLIKELY(characterLength < 0)) {
        DB_LOG_ERROR(GMERR_SEQUENCE_INVALID_INDEX, "Wrong : content is NULL or too long");
        return GMERR_SEQUENCE_INVALID_INDEX;
    }

    attrOpParam->element->nextClock = attrOpParam->element->nextClock + (uint32_t)characterLength;
    Status ret = MiniCreateMemElement(&elementInfo, attrOpParam->curSequence, &contentElement);
    if (ret != GMERR_OK) {
        return ret;
    }

    DbLinkedListInsert(&attrOpParam->toDeleteAttrElements, &contentElement->linkedNode);
    contentElement->prev = attrOpParam->element->prev;
    contentElement->next = attrOpParam->element->next;
    attrOpParam->element->prev = contentElement;

    return PersistElementToDb(attrOpParam, contentElement);
}

static Status SeqInsertEmbedElement(AttributeOperateParamT *attrOpParam, SequenceOperationCfgT *sequenceOpCfg)
{
    DmBaseElementT *embedElement = NULL;
    DmBaseElementInfoT elementInfo = {
        .type = DM_ELEMENT_EMBED_TYPE,
        .elementId.equipId = attrOpParam->element->equipId,
        .elementId.incrClock = attrOpParam->element->nextClock,
        .embed =
            {
                .typeLen = sequenceOpCfg->embed.typeLen,
                .type = sequenceOpCfg->embed.type,
                .pathLen = sequenceOpCfg->embed.pathLen,
                .path = sequenceOpCfg->embed.path,
            },
    };

    attrOpParam->element->nextClock += 1;
    Status ret = MiniCreateMemElement(&elementInfo, attrOpParam->curSequence, &embedElement);
    if (ret != GMERR_OK) {
        return ret;
    }

    DbLinkedListInsert(&attrOpParam->toDeleteAttrElements, &embedElement->linkedNode);
    embedElement->prev = attrOpParam->element->prev;
    embedElement->next = attrOpParam->element->next;
    attrOpParam->element->prev = embedElement;

    return PersistElementToDb(attrOpParam, embedElement);
}

static Status AttrOpParamInit(MiniRunCtxT *runCtx, SequenceOperationCfgT *sequenceOpCfg, DmBaseSequenceT *curSequence,
    AttributeOperateParamT *attrOpParam, DmOperationLogBodyT *operationLogBody)
{
    attrOpParam->runCtx = runCtx;
    attrOpParam->curSequence = curSequence;
    attrOpParam->attrNull = (sequenceOpCfg->attrsItemSize == INHERITED_ATTRIBUTE_INDICATOR);

    DmBaseLogBodyT sharedObjInfo = {
        .memCtx = runCtx->memCtx,
        .sharedObjType = GME_SEQUENCE_TYPE,
        .sharedObjNameLength = sequenceOpCfg->sequenceName.len,
        .sharedObjName = sequenceOpCfg->sequenceName.str,
    };

    operationLogBody->sharedObjInfo = sharedObjInfo;
    operationLogBody->equipIdLength = DM_STR_LEN(DeEquipIdGet());
    operationLogBody->equipId = (char *)(uintptr_t)DeEquipIdGet();
    operationLogBody->elementSize = 0u;
    operationLogBody->deleteSize = 0u;
    attrOpParam->operationElements = &operationLogBody->operationElements;
    attrOpParam->operationDeletes = &operationLogBody->operationDeletes;
    DbLinkedListInit(&attrOpParam->toDeleteAttrElements);
    DbLinkedListInit(&attrOpParam->toDeleteIdElements);

    DbLinkedListInit(attrOpParam->operationElements);
    DbLinkedListInit(attrOpParam->operationDeletes);

    return InitAllAttrsMap(runCtx->memCtx, attrOpParam);
}

static Status SkipAttributeOrDeletedElement(AttributeOperateParamT *attrOpParam)
{
    while (attrOpParam->element->next != NULL &&
           (attrOpParam->element->next->type == DM_ELEMENT_ATTRIBUTE_TYPE || attrOpParam->element->next->isDelete)) {
        if (!attrOpParam->element->next->isDelete) {
            Status ret = AddAttrWithCountToMap(attrOpParam->runCtx->memCtx,
                (DmBaseAttributeT *)attrOpParam->element->next->baseContent, 0u, &attrOpParam->attributesMap);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        attrOpParam->element->prev = attrOpParam->element->next;
        attrOpParam->element->next = attrOpParam->element->next->next;
    }
    return GMERR_OK;
}

static Status AssignAttributeMoveElement(AttributeOperateParamT *attrOpParam, uint32_t length, char **deleteKeys,
    uint32_t deleteKeyNums, bool *needInsertRightAttrs)
{
    Status ret = GMERR_OK;
    for (uint32_t count = 0u; count < length && attrOpParam->element->next != NULL;) {
        if (attrOpParam->element->next->isDelete) {
            attrOpParam->element->prev = attrOpParam->element->next;
            attrOpParam->element->next = attrOpParam->element->next->next;
            continue;
        }

        if (attrOpParam->element->next->type == DM_ELEMENT_ATTRIBUTE_TYPE) {
            ret = InsertRightAttrs(
                attrOpParam, true, deleteKeys, deleteKeyNums, MINI_QRY_TYPE_SEQUENCE_ASSIGN_ATTRIBUTES);
            if (ret != GMERR_OK) {
                return ret;
            }

            ret = SkipAttributeOrDeletedElement(attrOpParam);
            if (attrOpParam->element->next == NULL) {
                *needInsertRightAttrs = false;
                break;
            }

            ret = InsertLeftAttrs(attrOpParam, deleteKeys, deleteKeyNums, MINI_QRY_TYPE_SEQUENCE_ASSIGN_ATTRIBUTES);
            if (ret != GMERR_OK) {
                return ret;
            }
        }

        uint32_t leftNum = length - count;
        if (leftNum < attrOpParam->element->next->length) {
            ret = MiniSplitElement(attrOpParam->curSequence, attrOpParam->element->next, leftNum);
            if (ret != GMERR_OK) {
                return ret;
            }
        }

        count += attrOpParam->element->next->length;
        attrOpParam->element->prev = attrOpParam->element->next;
        attrOpParam->element->next = attrOpParam->element->next->next;
    }
    return ret;
}

static void GetDeleteAttrKeys(AttributeOperateParamT *attrOpParam, char **deleteKeys, uint32_t *deleteKeyNums)
{
    for (uint32_t i = 0; i < attrOpParam->paramsAttrs.capacity; ++i) {
        const DbOamapBucketT *bucket = &attrOpParam->paramsAttrs.buckets[i];
        if (bucket == NULL || bucket->state != USED) {
            continue;
        }
        DmBaseAttributeWithCountT *attrWithCount = (DmBaseAttributeWithCountT *)bucket->value;
        *(deleteKeys + (*deleteKeyNums)) = attrWithCount->attribute->key;
        ++(*deleteKeyNums);
    }
}

static Status InsertElementsToAttrOpParam(AttributeOperateParamT *attrOpParam, SequenceOperationCfgT *sequenceOpCfg,
    DmOperationLogBodyT *operationLogBody, MiniSharedObjQryTypeE type)
{
    Status ret = InitAttributeOperateParam(type, sequenceOpCfg, attrOpParam, operationLogBody);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Wrong init attribute operate param for assign sequence attribute.");
        return ret;
    }
    char **deleteKeys =
        (char **)DbDynMemCtxAlloc(attrOpParam->runCtx->memCtx, sizeof(char *) * attrOpParam->paramsAttrs.capacity);
    if (deleteKeys == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Wrong malloc for new deleteKeys");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(deleteKeys, sizeof(char *) * attrOpParam->paramsAttrs.capacity, 0x00,
        sizeof(char *) * attrOpParam->paramsAttrs.capacity);
    uint32_t deleteKeyNums = 0;
    GetDeleteAttrKeys(attrOpParam, deleteKeys, &deleteKeyNums);
    ret = InsertLeftAttrs(attrOpParam, deleteKeys, deleteKeyNums, type);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    bool isAssign = false;
    bool needInsertRightAttrs = true;
    switch (type) {
        case MINI_QRY_TYPE_SEQUENCE_INSERT:
            ret = SeqInsertContentElement(attrOpParam, sequenceOpCfg);
            break;
        case MINI_QRY_TYPE_SEQUENCE_EMBED_INSERT:
            ret = SeqInsertEmbedElement(attrOpParam, sequenceOpCfg);
            break;
        case MINI_QRY_TYPE_SEQUENCE_ASSIGN_ATTRIBUTES:
            ret = AssignAttributeMoveElement(
                attrOpParam, sequenceOpCfg->length, deleteKeys, deleteKeyNums, &needInsertRightAttrs);
            isAssign = true;
            break;
        default:
            DB_ASSERT(false);
            ret = GMERR_INTERNAL_ERROR;
    }
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    if (needInsertRightAttrs) {
        ret = InsertRightAttrs(attrOpParam, isAssign, deleteKeys, deleteKeyNums, type);
    }
EXIT:
    DbDynMemCtxFree(attrOpParam->runCtx->memCtx, deleteKeys);
    return ret;
}

Status SeqInsertElements(
    MiniRunCtxT *runCtx, SequenceOperationCfgT *sequenceOpCfg, DmBaseSequenceT *curSequence, MiniSharedObjQryTypeE type)
{
    AttributeOperateParamT attrOpParam = {0};
    DmOperationLogBodyT operationLogBody = {0};

    Status ret = AttrOpParamInit(runCtx, sequenceOpCfg, curSequence, &attrOpParam, &operationLogBody);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = InsertElementsToAttrOpParam(&attrOpParam, sequenceOpCfg, &operationLogBody, type);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    operationLogBody.elementSize = attrOpParam.elementSize;
    operationLogBody.deleteSize = attrOpParam.deleteSize;

    // write log
    uint8_t *logBuf = NULL;
    uint32_t logBufLen = 0u;
    ret = DmSerializeOperationLogBody(&operationLogBody, &logBuf, &logBufLen);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = MiniCreateResponseForLogWrite(runCtx, logBuf, logBufLen);
    DbDynMemCtxFree(runCtx->memCtx, logBuf);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = OpHistoryRecord(runCtx, &operationLogBody);

EXIT:
    EmbeddedReleaseOperationList(runCtx->memCtx, attrOpParam.operationElements);
    EmbeddedReleaseOperationDeletedList(runCtx->memCtx, attrOpParam.operationDeletes);
    EmbeddedReleaseDeleteElementsList(runCtx->memCtx, &attrOpParam.toDeleteAttrElements);
    EmbeddedReleaseDeleteIdElementsList(runCtx->memCtx, &attrOpParam.toDeleteIdElements);
    DbDynMemCtxFree(curSequence->memCtx, attrOpParam.element);
    ReleaseAttributeMap(runCtx->memCtx, &attrOpParam.paramsAttrs);
    ReleaseAttributeMap(runCtx->memCtx, &attrOpParam.attributesMap);
    ReleaseAttributeMap(runCtx->memCtx, &attrOpParam.oppositeAttrs);
    ReleaseAttributeMap(runCtx->memCtx, &attrOpParam.assignMoveAttrs);
    return ret;
}

void FreeAttrValuesArray(MiniRunCtxT *runCtx, SequenceOperationCfgT *sequenceOperateCfg)
{
    if (sequenceOperateCfg->attrValuesArray != NULL) {
        DbDynMemCtxFree(runCtx->session->memCtx, sequenceOperateCfg->attrValuesArray);
        sequenceOperateCfg->attrValuesArray = NULL;
    }
}
