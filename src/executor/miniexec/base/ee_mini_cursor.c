/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Cursor for mini kv
 * Author:
 * Create: 2023-12-18
 */

#include "ee_mini_cursor.h"

#include "dm_meta_minikv_label.h"
#include "ee_key_cmp.h"
#include "ee_mini_key_cmp.h"
#include "db_instance.h"
#include "dm_meta_mini_basic_inner.h"
#include "dm_minicache.h"
#include "se_trx_inner.h"
#include "db_tuple_buffer.h"

static Status MiniAllocHeapRunctx(MiniLabelCursorT *labelCursor)
{
    DmMiniBaseLabelT *label = (DmMiniBaseLabelT *)((DmMiniBaseLabelT *)labelCursor->label);
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.heapShmAddr = *(ShmemPtrT *)&label->pageAddr,
        .seRunCtx = labelCursor->seRunCtx,
        .dmInfo = (void *)((DmMiniBaseLabelT *)labelCursor->label),
        .isBackGround = false};
    return HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &labelCursor->hpHandle);
}

static Status MiniOpenCtxs4LabelCursor(MiniLabelCursorT *labelCursor)
{
    DB_POINTER(labelCursor);
    Status ret = MiniAllocHeapRunctx(labelCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong RunCtx-Alloc for heap");
        return ret;
    }
    DmMiniBaseIndexT *index = ((DmMiniKvTableT *)((DmMiniBaseLabelT *)labelCursor->label))->index;
    ret = IdxAlloc(labelCursor->seRunCtx, (uint8_t)index->indexType, &labelCursor->idxCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong RunCtx-Alloc for idx");
        (void)HeapLabelReleaseRunctx(labelCursor->hpHandle);
        labelCursor->hpHandle = NULL;
    }
    return ret;
}

Status MiniCreateKvTableCursor(MiniCursorCreateParamT *createParam, MiniLabelCursorT **labelCursor)
{
    if (*labelCursor != NULL) {
        return GMERR_OK;
    }
    CataKeyT cataKey = {.dbId = createParam->dbId, .labelName = createParam->labelName};
    Status ret = GMERR_OK;
    MiniLabelCursorT *cursor = (MiniLabelCursorT *)DbDynMemCtxAlloc(createParam->memCtx, sizeof(MiniLabelCursorT));
    if (cursor == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for label ctx");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(cursor, sizeof(MiniLabelCursorT), 0, sizeof(MiniLabelCursorT));
    cursor->dbInstance = createParam->dbInstance;
    cursor->memCtx = createParam->memCtx;
    cursor->seRunCtx = createParam->seRunCtx;
    cursor->scanStatus = SCAN_UNINIT;
    MiniCatalogT *catalog = DmGetMiniCatalog(createParam->dbInstance);
    ret = CatalogGetMiniKvLabelByName(catalog, &cataKey, (DmMiniBaseLabelT **)&cursor->label);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = MiniOpenCtxs4LabelCursor(cursor);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    *labelCursor = (void *)cursor;
    return GMERR_OK;
EXIT:
    MiniReleaseKvTableCursor(cursor);
    return ret;
}

Status MiniCheckAddrAndFetch(IndexCtxT *idxCtx, const HpRunHdlT heapRunHdl, HpTupleAddr addr, bool *isExist)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

void InitAllTupleBuf(MiniLabelCursorT *labelCursor)
{
    TupleBufInit(&labelCursor->idxCtx->tupleBuf, labelCursor->memCtx);
    // cursorBuffer中的tupleBuf初始化memCtx
    TupleBufInit(&labelCursor->cursorBuffer.tupleBuf, labelCursor->memCtx);
}

Status MiniOpenKvTableCursor(MiniLabelCursorT *labelCursor)
{
    DB_POINTER(labelCursor);
    MiniCatalogT *catalog = DmGetMiniCatalog(labelCursor->dbInstance);
    if (CatalogCheckMiniKvLabel(catalog, ((DmMiniBaseLabelT *)labelCursor->label)) == false) {
        DB_LOG_WARN(GMERR_UNDEFINED_TABLE, "Table: %s not available", labelCursor->label->metaName);
        return GMERR_UNDEFINED_TABLE;
    }
    if (labelCursor->isOpen) {
        return GMERR_OK;
    }
    IndexOpenCfgT idxCfg = {0};
    Status ret = HeapLabelOpen(labelCursor->hpHandle, labelCursor->action, labelCursor->memCtx);
    if (ret != GMERR_OK) {
        DB_LOG_WARN(ret, "Table: %s heap-open wrong", labelCursor->label->metaName);
        return ret;
    }
    DmMiniBaseIndexT *index = ((DmMiniKvTableT *)((DmMiniBaseLabelT *)labelCursor->label))->index;
    idxCfg = (IndexOpenCfgT){
        .seRunCtx = labelCursor->seRunCtx,
        .vertex = NULL,
        .heapHandle = labelCursor->hpHandle,
        .indexLabel = (DmIndexLabelBaseT *)index,
        .chHandle = NULL,
        .userData = NULL,
        .callbackFunc =
            (IdxCallBackFuncT){
                .keyCmp = QryMiniKvHashCompare,
                .multiVersionKeyCmp = NULL,
                .hpBufCmp = NULL,
                .addrCheck = HeapFetchAndCheckExist,
                .addrCheckAndFetch = MiniCheckAddrAndFetch,
            },
        .indexType = index->indexType,
    };
    if ((ret = IdxOpen(*(ShmemPtrT *)&index->idxTupleAddr, &idxCfg, labelCursor->idxCtx)) != GMERR_OK) {
        DB_LOG_WARN(ret, "Table: %s idx-open wrong", labelCursor->label->metaName);
        return ret;
    }
    if (labelCursor->idxCtx == NULL || labelCursor->idxCtx->idxRunCtx == NULL ||
        (labelCursor->idxCtx->idxMetaCfg.idxType != BTREE_INDEX &&
            labelCursor->idxCtx->idxMetaCfg.idxType != HASH_INDEX)) {
        DB_LOG_WARN(GMERR_INVALID_PARAMETER_VALUE, "IdxOpen: para wrong");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    InitAllTupleBuf(labelCursor);
    labelCursor->isOpen = true;
    return SeSetHeapTrxCtxForIndex(&idxCfg);
}

void MiniCloseKvTableCursor(MiniLabelCursorT *labelCursor)
{
    if (!labelCursor->isOpen) {
        return;
    }
    TupleBufRelease(&labelCursor->cursorBuffer.tupleBuf);
    TupleBufRelease(&labelCursor->idxCtx->tupleBuf);
    HeapLabelCloseAndResetCtx(labelCursor->hpHandle);
    IdxClose(labelCursor->idxCtx);
    labelCursor->isOpen = false;
}

// The labelCursor argument should be set to NULL by caller after calling this function
void MiniReleaseKvTableCursor(MiniLabelCursorT *labelCursor)
{
    if (labelCursor == NULL) {
        return;
    }
    if (labelCursor->cursorBuffer.cache != NULL) {
        DbDynMemCtxFree(labelCursor->memCtx, labelCursor->cursorBuffer.cache);
        labelCursor->cursorBuffer.cache = NULL;
    }
    if (labelCursor->isOpen) {
        MiniCloseKvTableCursor(labelCursor);
    }
    if (labelCursor->idxCtx != NULL) {
        IdxRelease(labelCursor->idxCtx);
        labelCursor->idxCtx = NULL;
    }
    if (labelCursor->hpHandle != NULL) {
        HeapLabelReleaseRunctx(labelCursor->hpHandle);
        labelCursor->hpHandle = NULL;
    }
    if (labelCursor->label != NULL) {
        MiniCatalogT *catalog = DmGetMiniCatalog(labelCursor->dbInstance);
        CatalogReleaseMiniKvLabel(catalog, (DmMiniBaseLabelT *)labelCursor->label);
        labelCursor->label = NULL;
    }
    if (labelCursor->scanStatus != SCAN_UNINIT) {
        DbLinkedListRemove(&labelCursor->linkedNode);
    }
    DbDynMemCtxFree(labelCursor->memCtx, labelCursor);
}

Status MiniLockLabelLock(MiniLabelCursorT *labelCursor)
{
    DB_POINTER(labelCursor);
    Status ret = GMERR_OK;
    if (labelCursor->action != HEAP_OPTYPE_MAX_TYPE) {
        DbInstanceT *dbInstance = (DbInstanceT *)labelCursor->dbInstance;
        MiniCatalogT *catalog = (MiniCatalogT *)dbInstance->cataLog;
        if (labelCursor->action > HEAP_OPTYPE_MODIFY) {
            ret = MetaReadLock((DmMiniCataCacheT *)&catalog->cataCache, labelCursor->label);
        } else {
            ret = MetaWriteLock((DmMiniCataCacheT *)&catalog->cataCache, labelCursor->label);
        }
    }
    return ret;
}

void MiniUnlockLabelLock(MiniLabelCursorT *labelCursor)
{
    DB_POINTER(labelCursor);
    DbInstanceT *dbInstance = (DbInstanceT *)labelCursor->dbInstance;
    MiniCatalogT *catalog = (MiniCatalogT *)dbInstance->cataLog;
    DB_POINTER(labelCursor);
    if (labelCursor->action != HEAP_OPTYPE_MAX_TYPE) {
        if (labelCursor->action > HEAP_OPTYPE_MODIFY) {
            MetaReadUnlock((DmMiniCataCacheT *)&catalog->cataCache, labelCursor->label);
        } else {
            MetaWriteUnlock((DmMiniCataCacheT *)&catalog->cataCache, labelCursor->label);
        }
    }
}
