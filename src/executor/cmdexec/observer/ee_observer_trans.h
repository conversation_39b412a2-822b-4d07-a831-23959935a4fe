/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Define observer trans interface.
 * Author: GMDBv5 EE Team
 * Create: 2023-7-25
 */

#ifndef EE_OBSERVER_TRANS_H
#define EE_OBSERVER_TRANS_H

#include "ee_observer.h"
#include "ee_session_interface.h"
#include "gmc_errno.h"

#ifdef __cplusplus
extern "C" {
#endif

/*
 * @brief finish observer trans
 *  mainly used for finish query tuples trans
 *  trx lock will be released in SeTransCommit/SeTransRollback
 *  latch lock will be released in ReleaseAllDtlLabelLatch()
 * @param[in] session : session instance
 * @param[in] observer : observer itself
 * @param[in] ret : retCode from outer
 * @return : Success or ErrorCode
 */
Status FinishObserverTrans(SessionT *session, ObserverT *observer, Status ret);

#ifdef __cplusplus
}
#endif
#endif  // EE_OBSERVER_TRANS_H
