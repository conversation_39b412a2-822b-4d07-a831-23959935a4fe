/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: resource column function implement
 * Author: chendechen
 * Create: 2021-03-17
 */

#include "ee_ddl_resource_column.h"
#include "se_resource_column.h"
#include "db_label_latch_mgr.h"
#include "ee_concurrency_control.h"
#include "dm_meta_basic.h"
#include "dm_meta_res_col_pool.h"
#include "ee_cmd_router_fusion.h"
#include "ee_plan_node_ddl.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

static Status InitResPoolConfig(const DmResColPoolT *resPool, ResColPoolConfigT *config)
{
    DB_POINTER2(resPool, config);
    errno_t err = strcpy_s(config->name, DM_RES_POOL_MAX_NAME_LEN, resPool->metaCommon.metaName);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR,
            "copy res pool name when init res pool config. Init res pool name:%s.", resPool->metaCommon.metaName);
        return GMERR_INTERNAL_ERROR;
    }
    config->poolId = resPool->metaCommon.metaId;
    config->poolLabelId = resPool->metaCommon.metaId + CATA_AUTO_GENE_INIT_ID + 1;
    config->startId = resPool->startId;
    config->capacity = resPool->capacity;
    config->allocOrder = resPool->allocOrder;
    config->allocType = resPool->allocType;
    return GMERR_OK;
}

static void RemoveResPoolLabelLatch(DmResColPoolT *resPool)
{
    RemoveLabelLatchWhenCreateFailed(resPool->labelLatchId, resPool->labelLatchVersionId, NULL);
    resPool->resPoolLatchShmAddr = DB_INVALID_SHMPTR;
}

Status QryCreateResColPool(DmResColPoolT *resPool)
{
    DB_POINTER(resPool);
    Status ret;

    ret = CataCheckResPoolForCreate(resPool);
    if (ret != GMERR_OK) {
        return ret;  // Catalog会记录Last error，此处用户输入导致的错误，不要在EE记录日志了。
    }
    // 创建label latch
    ret = InitLabelLatch(&resPool->labelLatchId, &resPool->labelLatchVersionId, &resPool->resPoolLatchShmAddr, NULL);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "init label latch for respool, name: %s.", resPool->metaCommon.metaName);
        // LCOV_EXCL_STOP
        return ret;
    }

    // 创建一个res pool
    ShmemPtrT resPoolShm;
    ResColPoolConfigT config;
    ret = InitResPoolConfig(resPool, &config);
    if (ret != GMERR_OK) {
        RemoveResPoolLabelLatch(resPool);
        return ret;
    }

    ret = ResColCreateResPool(&config, &resPoolShm);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create res column, name:%s.", resPool->metaCommon.metaName);  // LCOV_EXCL_LINE
        RemoveResPoolLabelLatch(resPool);
        return ret;
    }
    resPool->resPool = resPoolShm;
    ret = CataSaveResColPool(resPool);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "cata save res pool.");
        RemoveResPoolLabelLatch(resPool);
        Status retTmp = ResColDestroyResPool(resPoolShm);  // 刚刚创建出来，肯定没有相关绑定，因此不调用Check函数
        // 回滚流程不应该失败
        if (retTmp != GMERR_OK) {
            DB_LOG_ERROR(retTmp, "rollback res pool. Res pool is %s.", resPool->metaCommon.metaName);  // LCOV_EXCL_LINE
        }
    }

    return ret;
}

Status QryDropResColPool(DmResColPoolT *resPool)
{
    DB_POINTER(resPool);
    Status ret;
    ret = ResColDestroyResPoolCheck(resPool->resPool);
    if (ret != GMERR_OK) {
        return ret;
    }
    CataKeyT cataKey;
    CataSetKeyForLabel(
        &cataKey, resPool->metaCommon.dbId, resPool->metaCommon.namespaceId, resPool->metaCommon.metaName);
    ret = CataRemoveResColPool(DbGetInstanceByMemCtx(resPool->memCtx), &cataKey);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop res col pool, name: %s.", resPool->metaCommon.metaName);  // LCOV_EXCL_LINE
    }
    // 元数据被成功删除后，后续步骤不允许再有错误
    (void)ResColDestroyResPool(resPool->resPool);
    return ret;
}

Status QryGetResColPoolByName(DmResColPoolT *resPool, DmBuffer *buffer)
{
    DB_POINTER2(resPool, buffer);
    // 序列化res pool元数据得到buffer
    Status ret = DmSerializeResColPoolMeta(resPool, buffer);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "get pool by name, Label is %s.", resPool->metaCommon.metaName);
        // LCOV_EXCL_STOP
        return ret;
    }

    return GMERR_OK;
}

Status QryExecuteCreateResPool(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryCreateResPoolDescT *desc = (QryCreateResPoolDescT *)stmt->context->entry;
    return QryCreateResColPool(desc->resConfig);
}

Status QryExecuteDropResPool(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryDropResPoolDescT *desc = (QryDropResPoolDescT *)stmt->context->entry;
    CataKeyT poolCataKey;
    CataSetKeyForLabel(&poolCataKey, DEFAULT_DATABASE_ID, desc->namespaceId, desc->resPoolName.str);
    // 获取相应的res pool
    DmResColPoolT *resPool = NULL;
    Status ret = CataGetResColPoolByName(&poolCataKey, &resPool, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (ret != GMERR_OK) {
        return ret;
    }
    if (DmResColPoolIsDatalogRes(resPool)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED,
            "operate datalog resource by fastpath api. resPoolName: %s.", resPool->metaCommon.metaName);
        (void)CataReleaseResColPool(resPool);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    ret = QryAcqLatchForResColPool(stmt, resPool->labelLatchId, resPool->labelLatchVersionId, true);
    if (ret != GMERR_OK) {
        (void)CataReleaseResColPool(resPool);
        return ret;
    }
    ret = QryDropResColPool(resPool);
    (void)CataReleaseResColPool(resPool);
    return ret;
}

Status QryExecuteBindExtendResPool(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryBindExtendResPoolDescT *desc = (QryBindExtendResPoolDescT *)stmt->context->entry;
    CataKeyT poolCataKey, extendCataKey;
    CataSetKeyForLabel(&poolCataKey, DEFAULT_DATABASE_ID, desc->namespaceId, desc->resPoolName.str);
    CataSetKeyForLabel(&extendCataKey, DEFAULT_DATABASE_ID, desc->namespaceId, desc->resExtendPoolName.str);
    DmResColPoolT *resPool = NULL;
    Status ret = CataGetResColPoolByName(&poolCataKey, &resPool, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (ret != GMERR_OK) {
        return ret;
    }
    DmResColPoolT *extendedPool = NULL;
    ret = CataGetResColPoolByName(&extendCataKey, &extendedPool, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (ret != GMERR_OK) {
        (void)CataReleaseResColPool(resPool);
        return ret;
    }
    if (DmResColPoolIsDatalogRes(resPool) || DmResColPoolIsDatalogRes(extendedPool)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED,
            "operate datalog resource by fastpath api. resPoolName name:%s,extendedPoolName:%s.",
            resPool->metaCommon.metaName, extendedPool->metaCommon.metaName);
        (void)CataReleaseResColPool(resPool);
        (void)CataReleaseResColPool(extendedPool);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    ret = QryAcqLatchFor2ResColPool(stmt, resPool, extendedPool);
    if (ret != GMERR_OK) {
        (void)CataReleaseResColPool(resPool);
        (void)CataReleaseResColPool(extendedPool);
        return ret;
    }

    ret = ResColBindExtendedPool(resPool->resPool, extendedPool->resPool);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "bind extend pool, pool=%s, extPool=%s.", resPool->metaCommon.metaName,
            extendedPool->metaCommon.metaName);
    }
    (void)CataReleaseResColPool(resPool);
    (void)CataReleaseResColPool(extendedPool);
    return ret;
}

Status QryExecuteUnbindExtendResPool(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryUnbindExtendResPoolDescT *desc = (QryUnbindExtendResPoolDescT *)stmt->context->entry;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, desc->namespaceId, desc->resPoolName.str);
    DmResColPoolT *resPool = NULL;
    Status ret = CataGetResColPoolByName(&cataKey, &resPool, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (ret != GMERR_OK) {
        return ret;
    }

    if (DmResColPoolIsDatalogRes(resPool)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED,
            "operate datalog resource by fastpath api. resPoolName name: %s.", resPool->metaCommon.metaName);
        (void)CataReleaseResColPool(resPool);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    ret = QryAcqLatchForResColPool(stmt, resPool->labelLatchId, resPool->labelLatchVersionId, false);
    if (ret != GMERR_OK) {
        (void)CataReleaseResColPool(resPool);
        return ret;
    }
    // 目前获取不到扩展资源池信息，无法给扩展资源池加锁，只能依赖资源内部的锁
    ret = ResColUnbindExtendedPool(resPool->resPool);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "unbind res pool to table, label is %s.", resPool->metaCommon.metaName);
    }
    (void)CataReleaseResColPool(resPool);
    return ret;
}

static Status QryExecuteIsDatalogVertexOrDatalogRes(DmVertexLabelT *vertexLabel, DmResColPoolT *resPool)
{
    if (DmVertexLabelIsDatalogLabel(vertexLabel)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED,
            "operate datalog vertexLabel by fastpath api. vertexLabel name: %s.", vertexLabel->metaCommon.metaName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    if (DmResColPoolIsDatalogRes(resPool)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED,
            "operate datalog resource by fastpath api. resPoolName name: %s.", resPool->metaCommon.metaName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

Status QryExecuteBindResPoolToVertexLabel(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryBindResPoolToTableDescT *desc = (QryBindResPoolToTableDescT *)stmt->context->entry;
    CataKeyT labelKey, poolKey;
    DmResColPoolT *resPool = NULL;
    DmVertexLabelT *vertexLabel = NULL;
    CataSetKeyForLabel(&labelKey, stmt->session->dbId, desc->namespaceId, desc->labelName.str);
    CataSetKeyForLabel(&poolKey, stmt->session->dbId, desc->namespaceId, desc->resPoolName.str);
    Status ret = CataGetVertexLabelByName(DbGetInstanceByMemCtx(stmt->session->memCtx), &labelKey, &vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_SYSVIEW) {
        ret = GMERR_INVALID_PARAMETER_VALUE;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Bind resPool to vertexLabel: '%s' but sysview label.", desc->labelName.str);
        goto EXIT;
    }

    ret = CataGetResColPoolByName(&poolKey, &resPool, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get resource, resPool=%s.", desc->resPoolName.str);
        goto EXIT;
    }

    ret = QryExecuteIsDatalogVertexOrDatalogRes(vertexLabel, resPool);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = QryAcqLatchForVertexLabel(stmt, vertexLabel, false);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = QryAcqLatchForResColPool(stmt, resPool->labelLatchId, resPool->labelLatchVersionId, false);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = CataBindResPoolToLabel(vertexLabel, resPool);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "bind respool to label, vertexLabel=%s, resPool=%s.", vertexLabel->metaCommon.metaName,
            resPool->metaCommon.metaName);
        goto EXIT;
    }

EXIT:
    if (vertexLabel != NULL) {
        (void)CataReleaseVertexLabel(vertexLabel);
    }

    if (resPool != NULL) {
        (void)CataReleaseResColPool(resPool);
    }
    return ret;
}

static Status QryUnbindResPoolVertexLabelCheck(DmVertexLabelT *vertexLabel)
{
    if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_SYSVIEW) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE,
            "Unbind resPool to vertexLabel: '%s' field but sysview label.", vertexLabel->metaCommon.metaName);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (DmVertexLabelIsDatalogLabel(vertexLabel)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED,
            "operate datalog vertexLabel by fastpath api. vertexLabel name: %s.", vertexLabel->metaCommon.metaName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

Status QryExecuteUnbindResPoolToVertexLabel(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryUnbindResPoolToTableDescT *desc = (QryUnbindResPoolToTableDescT *)stmt->context->entry;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, stmt->session->dbId, desc->namespaceId, desc->labelName.str);
    DmVertexLabelT *vertexLabel = NULL;
    Status ret = CataGetVertexLabelByName(DbGetInstanceByMemCtx(stmt->session->memCtx), &cataKey, &vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryUnbindResPoolVertexLabelCheck(vertexLabel);
    if (ret != GMERR_OK) {
        (void)CataReleaseVertexLabel(vertexLabel);
        return ret;
    }

    ret = QryAcqLatchForVertexLabel(stmt, vertexLabel, false);
    if (ret != GMERR_OK) {
        (void)CataReleaseVertexLabel(vertexLabel);
        return ret;
    }

    if (vertexLabel->commonInfo->resColInfo == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_RESOURCE_POOL_ERROR,
            "unbind res pool to table: Vertex has no res col. Label is %s.", vertexLabel->metaCommon.metaName);
        (void)CataReleaseVertexLabel(vertexLabel);
        return GMERR_RESOURCE_POOL_ERROR;
    }

    if (vertexLabel->commonInfo->resColInfo->labelLatchId == DB_INVALID_UINT32) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_RESOURCE_POOL_ERROR, "unbind res pool to table:%s.", vertexLabel->metaCommon.metaName);
        (void)CataReleaseVertexLabel(vertexLabel);
        return GMERR_RESOURCE_POOL_ERROR;
    }

    ret = QryAcqLatchForResColPool(stmt, vertexLabel->commonInfo->resColInfo->labelLatchId,
        vertexLabel->commonInfo->resColInfo->labelLatchVersionId, false);
    if (ret != GMERR_OK) {
        (void)CataReleaseVertexLabel(vertexLabel);
        return ret;
    }

    ret = CataUnbindResPoolToLabel(vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unbind respool to label:%s.", vertexLabel->metaCommon.metaName);
    }

    (void)CataReleaseVertexLabel(vertexLabel);
    return ret;
}

Status QryExecuteGetResPool(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryDumpResPoolDescT *desc = (QryDumpResPoolDescT *)stmt->context->entry;
    TextT resPoolBuf;
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)stmt->memCtx;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, desc->namespaceId, desc->resPoolName.str);
    // 从Catalog中查询相应res pool的元数据
    DmResColPoolT *resPool = NULL;
    Status ret = CataGetResColPoolByName(&cataKey, &resPool, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "get resource pool when execute get resource pool: %s.", desc->resPoolName.str);
        return ret;
    }
    ret = QryAcqLatchForResColPool(stmt, resPool->labelLatchId, resPool->labelLatchVersionId, false);
    if (ret != GMERR_OK) {
        (void)CataReleaseResColPool(resPool);
        return ret;
    }
    ret = QryGetResColPoolByName(resPool, &buffer);
    (void)CataReleaseResColPool(resPool);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (buffer.buf == NULL) {
        return GMERR_INTERNAL_ERROR;
    }
    resPoolBuf.str = (char *)buffer.buf;
    resPoolBuf.len = buffer.len;
    ret = FixBufPutText(stmt->session->rsp, &resPoolBuf);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "put resource pool.");
    }
    return ret;
}

#ifdef __cplusplus
}
#endif
