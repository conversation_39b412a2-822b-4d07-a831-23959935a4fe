/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementation of subscription message
 * Author: <PERSON>
 * Create: 2022-12-06
 */

#include "ee_dml_subs_common.h"
#include "ee_dml_subs.h"
#include "drt_instance.h"
#include "dm_data_kv.h"
#include "ee_dml_desc.h"
#include "ee_key_cmp.h"
#include "ee_schedule.h"
#include "ee_feature_import.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

static Status QryGetPrimaryKeyMessage(QryLabelCursorT *cursor, QrySubsEventT *subsEvent)
{
    HeapTupleBufT *tuple = &cursor->heapTuple.heapTupleBuf;
    QrySubsEventRowDataT *curEventRowData =
        &subsEvent->eventDataSet.eventRowDataSet.eventRowData[subsEvent->eventDataSet.rowDataSetCurIdx];
    if (subsEvent->curSubs->labelType == VERTEX_LABEL) {
        Status ret = QryGetKeyBufFromVertexBuf(cursor->vertexNode->vertex,
            cursor->labelDef.vertexLabel->metaVertexLabel->pkIndex->idxLabelBase.indexId, tuple->buf,
            &curEventRowData->newKeyBuf);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        DmGetKeyFromKvBuf(tuple->buf, &curEventRowData->newKeyBuf.keyData, &curEventRowData->newKeyBuf.keyLen);
    }

    return GMERR_OK;
}

static Status QryInitFullLabelMessageContent(
    QryStmtT *stmt, QrySubsEventT *subsEvent, QrySubsChannelItemT *subsChannelItem, uint16_t subMsgTypes)
{
    const QrySubsRowDataT *curRowData =
        &subsEvent->eventDataSet.rowDataSet->rowsData[subsEvent->eventDataSet.rowDataSetCurIdx];
    QrySubsEventRowDataT *curEventRowData =
        &subsEvent->eventDataSet.eventRowDataSet.eventRowData[subsEvent->eventDataSet.rowDataSetCurIdx];
    if ((subMsgTypes & DM_SUBS_MSG_KEY_DATA) != 0) {
        QryCursorT *cursor = (QryCursorT *)stmt->qryCursor;
        QryLabelCursorT *labelCursor = &cursor->labelCursors[0];
        Status ret = QryGetPrimaryKeyMessage(labelCursor, subsEvent);
        if (ret != GMERR_OK) {
            return ret;
        }
        // old key data
        subsEvent->contentLen += FixBufGetRawDataLen(curEventRowData->oldKeyBuf.keyLen);
        // new key data
        subsEvent->contentLen += FixBufGetRawDataLen(curEventRowData->newKeyBuf.keyLen);
    }
    if ((subMsgTypes & DM_SUBS_MSG_NEW_DATA) != 0) {
        subsEvent->contentLen += FixBufGetRawDataLen(curRowData->newTupleBuf.bufSize);
    }

    return QryAllocContentBuf(subsEvent, subsChannelItem);
}

static Status QryBuildFullLabelMessageContent(
    QryStmtT *stmt, QrySubsEventT *subsEvent, QrySubsChannelItemT *subsChannelItem)
{
    if (subsEvent->eventType != DM_SUBS_EVENT_INIT && subsEvent->eventType != DM_SUBS_EVENT_TRIGGER_SCAN) {
        return GMERR_OK;
    }
    Status ret = QryInitFullLabelMessageContent(stmt, subsEvent, subsChannelItem, subsEvent->maxMsgType);
    if (ret != GMERR_OK) {
        return ret;
    }
    const QrySubsRowDataT *curRowData =
        &subsEvent->eventDataSet.rowDataSet->rowsData[subsEvent->eventDataSet.rowDataSetCurIdx];
    QrySubsEventRowDataT *curEventRowData =
        &subsEvent->eventDataSet.eventRowDataSet.eventRowData[subsEvent->eventDataSet.rowDataSetCurIdx];
    if ((subsEvent->maxMsgType & DM_SUBS_MSG_KEY_DATA) != 0) {
        ret = FixBufPutRawText(
            subsChannelItem->subsContentBuf, curEventRowData->oldKeyBuf.keyLen, curEventRowData->oldKeyBuf.keyData);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "put old key buf.");
            return ret;
        }
        ret = FixBufPutRawText(
            subsChannelItem->subsContentBuf, curEventRowData->newKeyBuf.keyLen, curEventRowData->newKeyBuf.keyData);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "put new key buf.");
            return ret;
        }
    }
    if ((subsEvent->maxMsgType & DM_SUBS_MSG_NEW_DATA) != 0) {
        ret = FixBufPutRawText(
            subsChannelItem->subsContentBuf, curRowData->newTupleBuf.bufSize, curRowData->newTupleBuf.buf);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "put new data buf.");
            return ret;
        }
    }
    subsChannelItem->contentRowsNum = 1;
    return GMERR_OK;
}

static void QryInitFullLabelSubEvent(QryStmtT *stmt, QrySubsEventT *subsEvent, uint8_t filterByMsgType,
    uint8_t dataTxWay, QrySubsRowDataSetT *rowsDataSet)
{
    QryCursorT *cursor = (QryCursorT *)stmt->qryCursor;
    QryLabelCursorT *labelCursor = &cursor->labelCursors[0];
    QryFullSyncScanVertexDescT *desc = (QryFullSyncScanVertexDescT *)stmt->context->entry;
    subsEvent->labelType = labelCursor->labelDef.labelType;
    subsEvent->vertexLabel = labelCursor->labelDef.vertexLabel;
    subsEvent->curSubs = desc->subscription;
    subsEvent->eventType = QryGetSubsEventType(stmt);
    subsEvent->msgType = QryGetSubsMessageType(stmt, subsEvent);
    // 批处理的最大消息集在前面已经设置了，此处不用单独再设置了
    if (!subsEvent->subsCanBeBatch) {
        subsEvent->maxMsgType = subsEvent->msgType;
    }
    subsEvent->buildMessageDescFunc = QryBuildSubsMessageDesc;
    subsEvent->buildMessageContentFunc = QryBuildFullLabelMessageContent;
    subsEvent->filterByMsgType = filterByMsgType;
    subsEvent->version = labelCursor->version;
    if (subsEvent->eventType == DM_SUBS_EVENT_INIT || subsEvent->eventType == DM_SUBS_EVENT_TRIGGER_SCAN) {
        subsEvent->dataTxWay = dataTxWay;
    }

    subsEvent->eventDataSet.rowDataSet = rowsDataSet;
}

// 根据是否需要重试与否直接吃掉错误了
Status QryBuildRspForFullSyncScan(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    Status ret = QryCacheSubsInfo(stmt->session);
    if (ret != GMERR_OK) {
        return ret;
    }

    QryCursorT *cursor = (QryCursorT *)stmt->qryCursor;
    QryLabelCursorT *labelCursor = &cursor->labelCursors[0];
    QrySubsEventT *subsEvent = NULL;
    ret = QryAllocMem(stmt->session->memCtx, sizeof(QrySubsEventT), (char **)&subsEvent);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 这里这么写是为后续如果全表多条数据合并推送优化做一个入口引子
    QrySubsRowDataSetT rowsDataSet = {0};
    rowsDataSet.rowsDataNum = 1;
    rowsDataSet.rowsData[0].newTupleBuf = labelCursor->heapTuple.heapTupleBuf;

    // 全表推送就只有一个订阅关系，若考虑到多条数据可能做合并推送（当前还是单条数据），可走外带模式，当前不走外带
    QryInitFullLabelSubEvent(stmt, subsEvent, 0, 0, &rowsDataSet);
    ret = QryGenerateSubsMessages(stmt, subsEvent);
    // 走外带的统一在这里完成数据的确认
    ret = QryFinishGenerateSubMessages(stmt->session, ret);
    // 如果构造数据失败，需要考虑构造数据重试
    if (ret == GMERR_OK) {
        // 成功了，检查一下是否是trigger的特殊的开始标记，是的话，设置其已经正确构造
        if (stmt->subsCtx.isTrigger && !stmt->subsCtx.isTriggerBeginHasDone) {
            stmt->subsCtx.isTriggerBeginHasDone = true;
        }
    }
    DbDynMemCtxFree(stmt->session->memCtx, subsEvent);
    return ret;
}

// 该接口会尝试获取一个有效的订阅连接，并设置其连接session上的subsInfo
static Status QryGetSubsConnWithInitSubInfo(const DmSubscriptionT *subs, DrtConnectionT **subsConn)
{
    DmChannelNodeT *chanNode = NULL;
    DmChannelNodeT *chanNodeTmp = NULL;
    DrtConnectionT *subsConnTmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(chanNode, chanNodeTmp, &subs->channelList, linkedNode)
    {
        DbRWSpinRLock(&chanNode->rwlock);
        if (chanNode->isUnBind) {
            DbRWSpinRUnlock(&chanNode->rwlock);
            continue;
        }
        subsConnTmp = DrtAttachConnectionByNodeName((const char *)chanNode->channelName.str);
        if (subsConnTmp == NULL) {
            DbRWSpinRUnlock(&chanNode->rwlock);
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_INVALID_OBJECT_DEFINITION, "attach connection, inv channel name %s.", chanNode->channelName.str);
            return GMERR_INVALID_OBJECT_DEFINITION;
        }
        DbRWSpinRUnlock(&chanNode->rwlock);
        break;
    }
    if (subsConnTmp == NULL) {
        return GMERR_OK;
    }
    // 尝试QryGetSubsSession并发延迟初始化订阅通道的subsinfo
    SessionT *subsSession = QryGetSessionOfSubChannel(subsConnTmp);
    if (subsSession == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "get subinfo.");
        DrtDetachConnection(subsConnTmp);
        *subsConn = NULL;
        return GMERR_DATA_EXCEPTION;
    }
    DbRWSpinWUnlock(&subsSession->rwLock);

    *subsConn = subsConnTmp;
    return GMERR_OK;
}

Status QryStartFullSyncScan(const DmSubscriptionT *subs, bool isTrigger)
{
    DB_POINTER(subs);
    if (subs->channelCount == 0) {
        return GMERR_OK;
    }
    DrtConnectionT *subsConn = NULL;
    Status ret = QryGetSubsConnWithInitSubInfo(subs, &subsConn);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (subsConn == NULL) {
        return GMERR_OK;
    }
    ret = QryAddFullScanToBgTask(subs->metaCommon.metaId, isTrigger);
    DrtDetachConnection(subsConn);
    return ret;
}

#ifdef __cplusplus
}
#endif
