/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * @Description: limit user connection implementation
 * @Author: tianyabo
 * @Create: 2022-09-09
 */

#include <xxh3.h>
#include "dm_meta_user.h"
#include "dm_meta_priv.h"
#include "ee_conn_limit.h"
#include "ee_log.h"

static inline void FreeConnCacheKeyValue(DbMemCtxT *memCtx, QryUserNameInfoT *key, ConnInfoT *value)
{
    DB_POINTER2(memCtx, key);
    DbDynMemCtxFree(memCtx, (void *)key->userName);
    key->userName = NULL;
    DbDynMemCtxFree(memCtx, (void *)key->processName);
    key->processName = NULL;
    DbDynMemCtxFree(memCtx, key);
    if (value != NULL) {
        DbDynMemCtxFree(memCtx, value);
    }
}

static void FreeMapKeyValueMem(DbMemCtxT *memCtx, DbOamapT *map)
{
    DB_POINTER2(memCtx, map);
    DbOamapIteratorT iter = 0;
    QryUserNameInfoT *key = NULL;
    ConnInfoT *value = NULL;

    while (DbOamapFetch(map, &iter, (void **)&key, (void **)&value) == GMERR_OK) {
        FreeConnCacheKeyValue(memCtx, key, value);
    }
}

/**
 * @brief: 用户连接缓存定时任务的回调函数，用于定时清空连接缓存
 * @param params [in]: userConnCache 指针
 */
void ClearConnCacheMap(void *params)
{
    DB_POINTER(params);
    UserConnCacheT *connCache = (UserConnCacheT *)params;
    DbRWSpinWLock(&connCache->rwLock);
    FreeMapKeyValueMem(connCache->memCtx, &connCache->cache);
    DbOamapClear(&connCache->cache);
    DbRWSpinWUnlock(&connCache->rwLock);
}

/**
 * @brief: Check whether the two userNameInfo are the same. This function is used as the comparison function of the
 * hashmap structure (see DbOamapT and DbOamapCompareT).
 */
uint32_t QryUserNameInfoCompare(const void *key1, const void *key2)
{
    DB_POINTER2(key1, key2);
    const QryUserNameInfoT *userNameInfo1 = key1;
    const QryUserNameInfoT *userNameInfo2 = key2;
    return ((strcmp(userNameInfo1->userName, userNameInfo2->userName) == 0) &&
            (strcmp(userNameInfo1->processName, userNameInfo2->processName) == 0));
}

/**
 * @brief: Calculate the hash value of userNameInfo.
 */
uint32_t QryUserNameInfoToHash32(const QryUserNameInfoT *userNameInfo)
{
    DB_POINTER(userNameInfo);

    XXH32_state_t state;
    (void)XXH32_reset(&state, DB_XXHASH_SEED);

    const void *data = userNameInfo->userName;
    size_t size = strlen(data);
    (void)XXH32_update(&state, data, size);

    data = userNameInfo->processName;
    size = strlen(data);
    (void)XXH32_update(&state, data, size);

    return XXH32_digest(&state);
}

static Status InitUserConnInfo(DbMemCtxT *memCtx, char *userName, char *processName, ConnInfoT **userConnInfo)
{
    DB_POINTER4(memCtx, userName, processName, userConnInfo);
    // 内存释放点:FreeConnCacheKeyValue
    ConnInfoT *connInfo = DbDynMemCtxAlloc(memCtx, sizeof(ConnInfoT));
    if (connInfo == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "create memctx when init user connection info.");
        return GMERR_OUT_OF_MEMORY;
    }

    *connInfo = (ConnInfoT){0};
    connInfo->userName = DbStr2Text(userName);
    connInfo->processName = DbStr2Text(processName);
    connInfo->rangeConns = 1;

    *userConnInfo = connInfo;
    return GMERR_OK;
}

Status SetUserNameInfo(
    DbMemCtxT *memCtx, const char *userNamePara, const char *processNamePara, QryUserNameInfoT **userInfo)
{
    DB_POINTER4(memCtx, userNamePara, processNamePara, userInfo);
    // 内存释放点:FreeConnCacheKeyValue
    QryUserNameInfoT *userNameInfo = (QryUserNameInfoT *)DbDynMemCtxAlloc(memCtx, sizeof(QryUserNameInfoT));
    if (userNameInfo == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "create userInfo.");
        return GMERR_OUT_OF_MEMORY;
    }

    *userNameInfo = (QryUserNameInfoT){0};

    uint32_t size = DM_STR_LEN(userNamePara);
    // 内存释放点:FreeConnCacheKeyValue
    userNameInfo->userName = (char *)DbDynMemCtxAlloc(memCtx, size);
    if (userNameInfo->userName == NULL) {
        DbDynMemCtxFree(memCtx, userNameInfo);
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "create userInfo when when alloc uname.");
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s((char *)userNameInfo->userName, size, userNamePara);
    if (err != EOK) {
        DbDynMemCtxFree(memCtx, (void *)userNameInfo->userName);
        DbDynMemCtxFree(memCtx, userNameInfo);
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "copy uname: '%s' when limit connection.", userNamePara);
        return GMERR_FIELD_OVERFLOW;
    }
    size = DM_STR_LEN(processNamePara);
    // 内存释放点:FreeConnCacheKeyValue
    userNameInfo->processName = (char *)DbDynMemCtxAlloc(memCtx, size);
    if (userNameInfo->processName == NULL) {
        DbDynMemCtxFree(memCtx, (void *)userNameInfo->userName);
        DbDynMemCtxFree(memCtx, userNameInfo);
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "create userInfo when alloc processName.");
        return GMERR_OUT_OF_MEMORY;
    }
    err = strcpy_s((char *)userNameInfo->processName, size, processNamePara);
    if (err != EOK) {
        DbDynMemCtxFree(memCtx, userNameInfo->userName);
        DbDynMemCtxFree(memCtx, userNameInfo->processName);
        DbDynMemCtxFree(memCtx, userNameInfo);
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "copy processName: '%s' when limit connection.", processNamePara);
        return GMERR_FIELD_OVERFLOW;
    }
    *userInfo = userNameInfo;
    return GMERR_OK;
}

#ifdef EXPERIMENTAL_NERGC
static Status InsertNergConnInfoIntoHashMap(
    UserConnCacheT *userConnCache, uint32_t hash, QryUserNameInfoT *userNameInfo)
{
    DB_POINTER2(userConnCache, userNameInfo);
    ConnInfoT *connInfo = NULL;
    QryUserNameInfoT *userInfo = NULL;

    Status ret = SetUserNameInfo(userConnCache->memCtx, userNameInfo->userName, userNameInfo->processName, &userInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = InitUserConnInfo(userConnCache->memCtx, userNameInfo->userName, userNameInfo->processName, &connInfo);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = DbOamapInsert(&userConnCache->cache, hash, (void *)userInfo, connInfo, NULL);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    return ret;
EXIT:
    FreeConnCacheKeyValue(userConnCache->memCtx, userInfo, connInfo);
    DB_LOG_ERROR(ret, "insert into hash map when limit user(%s:%s) connection.", userNameInfo->userName,
        userNameInfo->processName);
    return ret;
}
#else
static Status InsertNewConnInfoIntoHashMap(UserConnCacheT *userConnCache, QryUserNameInfoT *userNameInfo)
{
    DB_POINTER2(userConnCache, userNameInfo);
    QryUserNameInfoT *userInfo = NULL;

    Status ret = SetUserNameInfo(userConnCache->memCtx, userNameInfo->userName, userNameInfo->processName, &userInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t hash = QryUserNameInfoToHash32(userInfo);
    ConnInfoT *connInfo = NULL;
    ret = InitUserConnInfo(userConnCache->memCtx, userInfo->userName, userInfo->processName, &connInfo);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = DbOamapInsert(&userConnCache->cache, hash, (void *)userInfo, connInfo, NULL);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    return ret;
EXIT:
    DB_LOG_ERROR(
        ret, "insert into hash map when limit user(%s:%s) connection.", userInfo->userName, userInfo->processName);
    FreeConnCacheKeyValue(userConnCache->memCtx, userInfo, connInfo);
    return ret;
}
#endif

static Status GetUserResLimit(
    CataUserNameInfoT *userNameInfo, uint32_t *maxConnNum, uint32_t *maxConnSpeed, DbInstanceHdT dbInstance)
{
    DB_POINTER3(userNameInfo, maxConnNum, maxConnSpeed);
    Status ret = CataGetUserConnNum(userNameInfo, maxConnNum, dbInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get user conn num from catalog.");
        return ret;
    }
    ret = CataGetUserConnSpeed(userNameInfo, maxConnSpeed, dbInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get user conn speed from catalog.");
    }
    return ret;
}

static Status CheckUserConnSpeed(UserConnCacheT *userConnCache, QryUserNameInfoT *userNameInfo, uint32_t maxConnSpeed)
{
    DB_POINTER2(userConnCache, userNameInfo);
    Status ret;
    uint32_t hash = QryUserNameInfoToHash32(userNameInfo);

    ConnInfoT *connInfo = (ConnInfoT *)DbOamapLookup(&userConnCache->cache, hash, (void *)userNameInfo, NULL);
    if (connInfo == NULL) {
// hashmap中没有用户连接信息，新建
#ifdef EXPERIMENTAL_NERGC
        return InsertNergConnInfoIntoHashMap(userConnCache, hash, userNameInfo);
#else
        return InsertNewConnInfoIntoHashMap(userConnCache, userNameInfo);
#endif
    }
    // 判断当前用户连接速率是否超限，maxConnSpeed为0代表没有设置限制
    if (maxConnSpeed != 0 && connInfo->rangeConns >= maxConnSpeed) {
        ret = GMERR_PROGRAM_LIMIT_EXCEEDED;
        DB_LOG_AND_SET_LASERR(ret, "User connections exceeds, curRangeConns=%" PRIu32 ", maxConnSpeed=%" PRIu32 ".",
            connInfo->rangeConns, maxConnSpeed);
        return ret;
    }
    // 若未超限，将当前区间连接数 +1
    connInfo->rangeConns++;

    return GMERR_OK;
}

Status QryLimitUserConn(SessionT *session, UserConnCacheT *userConnCache)
{
    DB_POINTER2(session, userConnCache);

    CataUserNameInfoT userNameInfo;
    SetCataUserNameInfo(&userNameInfo, session->externalUser.dbUserName, NULL, session->externalUser.dbProcessName);

    // 获取用户连接限制
    uint32_t maxConnNum, maxConnSpeed;
    Status ret = GetUserResLimit(&userNameInfo, &maxConnNum, &maxConnSpeed, DbGetInstanceByMemCtx(session->memCtx));
    if (ret != GMERR_OK) {
        DB_LOG_WARN(ret, "get user resource limit when limit user(%s:%s) connection.", userNameInfo.userName,
            userNameInfo.processName);
        return GMERR_OK;
    }

    // 校验用户连接数
    if (maxConnNum != 0) {
        uint32_t roleId;
        ret = CataGetUserRoleId(&userNameInfo, &roleId, DbGetInstanceByMemCtx(session->memCtx));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(
                ret, "get role id when limit user(%s:%s) connection.", userNameInfo.userName, userNameInfo.processName);
            return ret;
        }

        // 获取当前role
        CataRoleT *role = NULL;
        ret = CataGetRoleById(roleId, &role, DbGetInstanceByMemCtx(session->memCtx));
        if (ret != GMERR_OK) {
            DB_LOG_WARN(
                ret, "get role when limit user(%s:%s) connection.", userNameInfo.userName, userNameInfo.processName);
            return GMERR_OK;
        }

        // 判断当前用户连接是否超过其最大连接限制，由于当前用户和role为一一对应关系，因此role连接数即为用户连接数
        if ((uint32_t)role->connNum >= maxConnNum) {
            ret = GMERR_PROGRAM_LIMIT_EXCEEDED;
            DB_LOG_AND_SET_LASERR(
                ret, "User(%s:%s) connections exceeds the limit.", userNameInfo.userName, userNameInfo.processName);
            (void)CataReleaseRole(role);
            return ret;
        }

        // 释放当前role
        (void)CataReleaseRole(role);
    }

    QryUserNameInfoT qryUserNameInfo;
    SetQryUserNameInfo(&qryUserNameInfo, (char *)userNameInfo.userName, (char *)userNameInfo.processName);
    // 校验用户连接速度
    DbRWSpinWLock(&userConnCache->rwLock);
    ret = CheckUserConnSpeed(userConnCache, &qryUserNameInfo, maxConnSpeed);
    DbRWSpinWUnlock(&userConnCache->rwLock);

    return ret;
}
