/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: header file of user connection limit
 * Author: tianyabo
 * Create: 2022-09-09
 */

#ifndef EE_CONN_LIMIT_H
#define EE_CONN_LIMIT_H

#include "ee_session.h"
#include "ee_context.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct ConnInfo {
    TextT userName;       // 用户名
    TextT processName;    // 进程名
    uint32_t rangeConns;  // 一个定时区间内的连接数
} ConnInfoT;

typedef struct UserConnCache {
    DbRWSpinLockT rwLock;
    DbMemCtxT *memCtx;
    DbOamapT cache;  // key: userNameInfo<QryUserNameInfoT *>, value: connInfo<ConnInfoT *>
    TimerHandleT connCacheTimer;
    bool hasTimer;
    bool isInit;
} UserConnCacheT;

/**
 * @brief: 用户连接缓存定时任务的回调函数，用于定时清空连接缓存
 * @param params [in]: hash map指针
 */
void ClearConnCacheMap(void *params);

/**
 * @brief: set userNameInfo for hashmap, include memory allocation
 */
Status SetUserNameInfo(
    DbMemCtxT *memCtx, const char *userNamePara, const char *processNamePara, QryUserNameInfoT **userInfo);

Status QryLimitUserConn(SessionT *session, UserConnCacheT *userConnCache);

#ifdef __cplusplus
}
#endif

#endif /* EE_CONN_LIMIT_H */
