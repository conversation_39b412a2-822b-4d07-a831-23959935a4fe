/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: implementation for dcl transaction
 * Author: linhuabin
 * Create: 2023-01-03
 */

#include "db_dead_loop.h"
#include "ee_concurrency_control.h"
#include "ee_dcl_ctrl.h"
#include "ee_dml_subs.h"
#include "ee_background_schedule.h"
#include "ee_feature_import.h"
#include "ee_statistic.h"
#include "se_persist.h"
#include "dm_cache_basic.h"
#include "ee_schedule.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

Status QryCheckTrxTypeAndIsolationLevel(QryStmtT *stmt, TrxCfgT *cfg)
{
    DB_POINTER2(stmt, cfg);
    if (stmt->autoCommit) {
        // 自动提交事务保持与之前逻辑不变，此函数仅校验新增显示开启事务
        return GMERR_OK;
    }
    uint32_t namespaceId = stmt->session->namespaceId;
    DmTrxInfoT trxInfo;
    Status ret = CataGetNspTrxInfoById(namespaceId, &trxInfo, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (ret != GMERR_OK) {
        return ret;
    }
    if (cfg->trxType == DEFAULT_TRX) {
        cfg->trxType = trxInfo.trxType;
    }
    if (cfg->isolationLevel == DEFAULT_ISOLATION) {
        cfg->isolationLevel = trxInfo.isolationLevel;
    }
#ifdef EXPERIMENTAL_GUANGQI
    if (cfg->isTrxAllowCloned) {
        if (cfg->trxType != OPTIMISTIC_TRX || cfg->isolationLevel != REPEATABLE_READ ||
            !DbCfgGetInt32Lite(DB_CFG_ENABLE_TRX_CLONE, NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE,
                "begin trx with cloneId, config.trxType=%" PRIu32 ", isolationLevel=%" PRIu32
                ", enableTrxClone:%" PRIu32,
                (uint32_t)cfg->trxType, (uint32_t)cfg->isolationLevel,
                (uint32_t)DbCfgGetInt32Lite(DB_CFG_ENABLE_TRX_CLONE, NULL));
            return GMERR_INVALID_PARAMETER_VALUE;
        }
    }
#endif
    if (!(trxInfo.trxType == cfg->trxType && trxInfo.isolationLevel == cfg->isolationLevel)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE,
            "change trx type when different trx between nsp and config, namespace.trxType=%" PRIu32
            ", isolationLevel=%" PRIu32 "; config.trxType=%" PRIu32 ", isolationLevel=%" PRIu32 ". ",
            (uint32_t)trxInfo.trxType, (uint32_t)trxInfo.isolationLevel, (uint32_t)cfg->trxType,
            (uint32_t)cfg->isolationLevel);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

static inline __attribute__((always_inline)) void QryStartTrxMonitor(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    if (SECUREC_UNLIKELY(!stmt->autoCommit &&
                         DbCfgGetBoolLite(DB_CFG_TRX_MONITOR_ENABLE, DbGetInstanceByMemCtx(stmt->session->memCtx)) &&
                         stmt->session->isAllocTrxMonToken)) {
        // 仅当开启事务monitor功能且显式事务开启事务monitor且已经分配事务monitortoken才能开启monitor
        DbDataT runData;
        runData.u64 = SeTransGetTrxId(stmt->session->seInstance);
        DbDeadLoopDetectBegin(DEAD_LOOP_TRX, stmt->session->trxMonToken, runData);
    }
}

static inline __attribute__((always_inline)) void QryStopTrxMonitor(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    if (SECUREC_UNLIKELY(!stmt->autoCommit &&
                         DbCfgGetBoolLite(DB_CFG_TRX_MONITOR_ENABLE, DbGetInstanceByMemCtx(stmt->session->memCtx)) &&
                         stmt->session->isAllocTrxMonToken)) {
        // 仅当开启事务monitor功能且显式事务开启事务monitor且已经分配事务monitortoken才能关闭monitor
        DbDeadLoopDetectEnd(DEAD_LOOP_TRX, stmt->session->trxMonToken);
    }
}

static Status QryHiddenTransSetIsolationLevel(QryStmtT *stmt, TrxCfgT *cfg)
{
    // 轻量化模式
    if (SECUREC_LIKELY(stmt->context->isLiteTrx)) {
        if (SECUREC_UNLIKELY(DbGaListGetCount(&stmt->context->labels) > 1)) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_FEATURE_NOT_SUPPORTED, "Fast read uncommitted not support multiple tables.");
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
        cfg->isolationLevel = READ_UNCOMMITTED;  // 大表锁模式
        return GMERR_OK;
    } else if (!QryTypeIsDDL(stmt->context->type) && stmt->context->isYangTrx) {
        cfg->isolationLevel = REPEATABLE_READ;
        cfg->trxType = OPTIMISTIC_TRX;
        return GMERR_OK;
    }
    // DDL操作统一使用悲观读已提交
    if (QryTypeIsDDL(stmt->context->type) || DbGaListGetCount(&stmt->context->labels) == 0) {
        cfg->trxType = PESSIMISTIC_TRX;
        cfg->isolationLevel = READ_COMMITTED;
        return GMERR_OK;
    }
    // 根据首个表的隔离级别设置
    QryLabelT *label = NULL;
    Status ret = QryGetVertexLabelByIdx(stmt->context, 0, &label);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (label->def.labelType == VERTEX_LABEL) {
        cfg->trxType = label->def.vertexLabel->commonInfo->heapInfo.trxType;
        cfg->isolationLevel = label->def.vertexLabel->commonInfo->heapInfo.isolationLevel;
    } else {
        cfg->trxType = label->def.kvLabel->trxType;
        cfg->isolationLevel = label->def.kvLabel->isolationLevel;
    }
    return GMERR_OK;
}

static inline __attribute__((always_inline)) void QrySetFroceCommitMode(SessionT *session, uint32_t forceCommitMode)
{
    session->forceCommitMode = DbCfgGetBoolLite(DB_CFG_FORCE_COMMIT_ENABLE, NULL) ? forceCommitMode : 0;
}

static inline __attribute__((always_inline)) void QryResetForceCommitMode(SessionT *session)
{
    session->forceCommitMode = 0;
}

Status QryExecuteAfterBeginTrans(QryStmtT *stmt, TrxStateE trxStateBeforeBegin, bool isTrxAllowCloned)
{
    Status ret = GMERR_OK;
    if (trxStateBeforeBegin == TRX_STATE_NOT_STARTED) {
        QrySetSessionBatchExecutedFlag(stmt->session, false);  // 每次开启新事务就重置此标志位
        QryStartTrxMonitor(stmt);
        if (SECUREC_UNLIKELY(SeTransGetIsolationLevel(stmt->session->seInstance) == REPEATABLE_READ)) {
            ret = SeTransAssignReadView(stmt->session->seInstance);
#ifdef EXPERIMENTAL_GUANGQI
            if (ret == GMERR_OK && !stmt->autoCommit && isTrxAllowCloned) {
                FixBufferT *rsp = stmt->session->rsp;
                ret = FixBufPutUint32(rsp, SeTransGetCloneId(stmt->session->seInstance));
                if (ret != GMERR_OK) {
                    // 这里失败不回滚事务
                    // 按照fastPath框架，会在外面将事务置为abort状态，由用户主动调用回滚接口
                    DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to put cloneId into response when begin trx.");
                }
                return ret;
            }
#endif
        }
    } else {
        DB_LOG_WARN(GMERR_ACTIVE_TRANSACTION,
            "begin trx again, trx state is %" PRIu32 ", autoCommit:%" PRIu32 ", isTrxAllowCloned:%" PRIu32,
            (uint32_t)trxStateBeforeBegin, (uint32_t)stmt->autoCommit, (uint32_t)isTrxAllowCloned);
#ifdef EXPERIMENTAL_GUANGQI
        if (SeTransIsCloneTypeTrx(stmt->session->seInstance)) {
            return GMERR_INVALID_PARAMETER_VALUE;
        }
#endif
    }
    return ret;
}

Status QryExecuteBeginTrans(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    TrxCfgT cfg = {0};
    Status ret;
    if (SECUREC_UNLIKELY(!stmt->autoCommit)) {
        cfg = ((QryBeginTransDescT *)stmt->context->entry)->cfg;
        // 校验客户端设置与namespace的事务类型是否一致
        ret = QryCheckTrxTypeAndIsolationLevel(stmt, &cfg);
        if (ret != GMERR_OK) {
            return ret;
        }
        stmt->session->isInteractiveTrx = true;
        QrySetFroceCommitMode(stmt->session, ((QryBeginTransDescT *)stmt->context->entry)->forceCommitMode);
        cfg.isInteractive = true;
        cfg.isTrxForceCommit = (stmt->session->forceCommitMode != 0);  // 非0即为强制提交
    } else {
        ret = QryHiddenTransSetIsolationLevel(stmt, &cfg);
        if (ret != GMERR_OK) {
            return ret;
        }

        // 隐式事务根据type类型来设置是否是readOnly，DQL设置为只读事务
        cfg.readOnly = (stmt->context->type < QRY_TYPE_DQL_END && stmt->context->type >= QRY_TYPE_DQL_BEG);
        cfg.isLiteTrx = stmt->context->isLiteTrx;
        // 轻量化事务下，老化、merge线程当前台线程处理（只有缩容特殊处理,缩容不走这个函数）
        cfg.isBackGround = false;
    }
    cfg.connId = (stmt->session->conn == NULL) ? DB_INVALID_UINT16 : stmt->session->conn->id;
    TrxStateE trxStateBeforeBegin = SeTransGetState(stmt->session->seInstance);
    ret = SeTransBegin(stmt->session->seInstance, &cfg);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
#ifdef FEATURE_REPLICATION
    ret = QryReplicateTxStart(stmt->session->logBuf, SeTransGetTrxId(stmt->session->seInstance), true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "tx start in replication.");
        return ret;
    }
#endif
    return QryExecuteAfterBeginTrans(stmt, trxStateBeforeBegin, cfg.isTrxAllowCloned);
}

#ifdef EXPERIMENTAL_GUANGQI
Status QryExecuteCloneTransHandleParentTrx(QryStmtT *stmt)
{
    // 自调度消息属于父线程的，由子线程触发；
    TrxStateE trxState = SeTransGetState(stmt->session->seInstance);
    if (trxState != TRX_STATE_ACTIVE) {  // 当前事务必须是active状态才允许被clone
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "trxState is %" PRIu32, (uint32_t)trxState);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    QryCloneTransDescT *desc = (QryCloneTransDescT *)stmt->context->entry;
    TrxCfgT cfg = {0};
    cfg.isolationLevel = REPEATABLE_READ;
    cfg.trxType = OPTIMISTIC_TRX;
    cfg.connId = (stmt->session->conn == NULL) ? DB_INVALID_UINT16 : stmt->session->conn->id;
    cfg.isCloneTrx = true;
    cfg.isInteractive = true;
    Status ret = SeTransCreateSuccessor(stmt->session->seInstance, &cfg, desc->cloneId, desc->connId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "create successorTrx unsucc, initiateConnId:%" PRIu16, desc->connId);
    }
    return GMERR_OK;  // 自调度消息不应该返回错误码，否则会造成父事务被置为abort状态，失败流程都会复原父事务原本状态
}

Status QryExecuteCloneTrans(QryStmtT *stmt)
{
    bool isSelfSchedule = false;
    if (stmt->session->req != NULL) {
        FixBufferT *req = stmt->session->req;
        MsgHeaderT *reqHeader = RpcPeekMsgHeader(req);
        isSelfSchedule = (reqHeader->flags & CS_FLAG_SELF_SCHEDULE) == CS_FLAG_SELF_SCHEDULE;
    }
    if (!isSelfSchedule) {
        // 非自调度消息属于子线程的，由客户端触发；
        TrxStateE trxState = SeTransGetState(stmt->session->seInstance);
        if (trxState != TRX_STATE_NOT_STARTED) {  // 当前事务必须是not start状态才允许clone
            DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "trxState is %" PRIu32, (uint32_t)trxState);
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        QryCloneTransDescT *desc = (QryCloneTransDescT *)stmt->context->entry;
        TrxBaseInfoT trxBaseInfo = {0};  // 获取父事务的信息
        Status ret = SeTransGetTrxBaseInfoById(stmt->session->seInstance, desc->cloneId, true, &trxBaseInfo);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get trx base info unsucc, cloneId:%" PRIu32, desc->cloneId);
            return ret;
        }
        // 发送自调度消息
        CloneOrMergeTaskInfoT info = {
            .cloneId = desc->cloneId, .connId = stmt->session->conn->id, .opCode = MSG_OP_RPC_TX_CLONE};
        ret = QryAddCloneOrMergeTask(trxBaseInfo.connId, &info);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "send self task, cloneId:%" PRIu32, desc->cloneId);
            return ret;
        }
        TrxCfgT cfg = {0};
        cfg.isolationLevel = REPEATABLE_READ;
        cfg.trxType = OPTIMISTIC_TRX;
        cfg.connId = (stmt->session->conn == NULL) ? DB_INVALID_UINT16 : stmt->session->conn->id;
        cfg.isCloneTrx = true;
        cfg.isInteractive = true;
        ret = SeTransClone(stmt->session->seInstance, &cfg, desc->cloneId);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "clone unsucc, cloneId:%" PRIu32, desc->cloneId);
        }
        QrySetSessionBatchExecutedFlag(stmt->session, false);  // 每次开启新事务就重置此标志位
        return ret;
    } else {
        return QryExecuteCloneTransHandleParentTrx(stmt);
    }
}

Status QryExecuteMergeTrans(QryStmtT *stmt)
{
    bool isSelfSchedule = false;
    if (stmt->session->req != NULL) {
        FixBufferT *req = stmt->session->req;
        MsgHeaderT *reqHeader = RpcPeekMsgHeader(req);
        isSelfSchedule = (reqHeader->flags & CS_FLAG_SELF_SCHEDULE) == CS_FLAG_SELF_SCHEDULE;
    }
    DB_ASSERT(isSelfSchedule);  // 预期一定是自调度消息
    // 自调度消息属于父线程的，由子线程触发；
    QryMergeTransDescT *desc = (QryMergeTransDescT *)stmt->context->entry;
    TrxCfgT cfg = {0};
    cfg.isolationLevel = REPEATABLE_READ;
    cfg.trxType = OPTIMISTIC_TRX;
    cfg.connId = (stmt->session->conn == NULL) ? DB_INVALID_UINT16 : stmt->session->conn->id;
    cfg.isTrxAllowCloned = true;
    cfg.isInteractive = true;
    Status ret = SeTransCreateCombineTrx(stmt->session->seInstance, &cfg, desc->cloneId, desc->trxId, desc->connId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create combineTrx unsucc, initiateConnId:%" PRIu16, desc->connId);
    }
    return GMERR_OK;  // 自调度消息不应该返回错误码，否则会造成父事务被置为abort状态，失败流程都会复原父事务原本状态
}
#endif

inline static void ClearYangDiffData(QryStmtT *stmt)
{
    QryResetForceCommitMode(stmt->session);
    if (stmt->session->yangTreeCtx != NULL) {
        DbDeleteDynMemCtx((DbMemCtxT *)stmt->session->yangTreeCtx->memCtx);
        DbDeleteDynMemCtx((DbMemCtxT *)stmt->session->yangTreeCtx->fetchMemCtx);
        DbDynMemCtxFree(stmt->session->memCtx, stmt->session->yangTreeCtx);
        stmt->session->yangTreeCtx = NULL;
        if (stmt->session->qryLabelCtx != NULL) {
            QryReleaseQryLabelCtx(stmt->session->qryLabelCtx);
        }
    }
}

// 后续改成session的方法
inline static void ResetInteractiveTrxResource(QryStmtT *stmt)
{
    stmt->session->isInteractiveTrx = false;
}

Status QryTrxCommitCallBackCb(void *para)
{
    DB_UNUSED(para);
    SessionT *session = para;

    // 校验记录数是否超限
    GaListT *labels = &session->trxStatistics.labels;
    for (uint32_t i = 0; i < DbGaListGetCount(labels); i++) {
        QryLabelCacheT *label = DbGaListGet(labels, i);
        if (SECUREC_UNLIKELY(label == NULL)) {
            continue;
        }

        if (SECUREC_UNLIKELY(label->labelDef.labelType != VERTEX_LABEL)) {
            continue;
        }

        // 只有Normal表支持对账老化和新订阅，在EE做超限判断
        if (SECUREC_UNLIKELY(!DmVertexLabelIsNormalLabel(label->labelDef.vertexLabel))) {
            continue;
        }

        Status ret = QryCheckMaxRecord(label);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // 刷新记录数到元数据
    for (uint32_t i = 0; i < DbGaListGetCount(labels); i++) {
        QryLabelCacheT *label = DbGaListGet(labels, i);
        if (SECUREC_UNLIKELY(label == NULL)) {
            continue;
        }

        if (SECUREC_UNLIKELY(label->labelDef.labelType != VERTEX_LABEL)) {
            continue;
        }

        // 只有Normal表支持对账老化和新订阅，在EE做超限判断
        if (SECUREC_UNLIKELY(!DmVertexLabelIsNormalLabel(label->labelDef.vertexLabel))) {
            continue;
        }
        QryCommitCallBack(label);
    }

    return GMERR_OK;
}

void QrySetTrxCommitCallBack(SessionT *session)
{
    DB_POINTER(session);
    TrxCommitCallBackCfgT commitCallBackCfg = {
        .func = QryTrxCommitCallBackCb,
        .parameter = session,
    };

    SeSetTrxCommitCallBackCfg(session->seInstance, &commitCallBackCfg);

    // 持久化不支持新订阅
    if (SECUREC_UNLIKELY(SeGetPersistMode() != PERSIST_OFF)) {
        return;
    }

    if (session->stmgSubDataSet.subData.vertexLabel == NULL) {
        return;
    }

    UpdateStatusMergeListCfgT cfg = {
        .func = QryUpdateStatusMergeListCb,
        .statusMergeSubDataSet = &session->stmgSubDataSet,
    };
    SeSetTrxCommitStatusMergeCfg(session->seInstance, &cfg);
}

Status QryCommitTrans(QryStmtT *stmt)
{
    Status ret = SeTransCommit(stmt->session->seInstance);
    if (SECUREC_UNLIKELY(!stmt->autoCommit && ret == GMERR_RESTRICT_VIOLATION && stmt->session->yangTreeCtx != NULL)) {
        // 进行事务重演重试提交
        ret = QryReplayYangOpreation(stmt, ret);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "retry replay when commit trans.");
            return ret;
        }
    }
    return ret;
}

Status QryExecuteSetCreated(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryCreateVertexLabelDescT *desc = (QryCreateVertexLabelDescT *)stmt->context->entry;
    uint32_t labelNum = DbListGetItemCnt(&desc->vertexLabels);
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < labelNum; i++) {
        QryCreateSingleVertexLabelDescT *singleDesc =
            (QryCreateSingleVertexLabelDescT *)DbListItem(&desc->vertexLabels, i);

        DmVertexLabelT *vertexLabel = singleDesc->vertexLabel;
        ret = CataSetVertexLabelCreateStatusById(
            DbGetInstanceByMemCtx(vertexLabel->memCtx), vertexLabel->metaCommon.metaId, true);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status QryExecuteSetNspCreated(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryCreateNamespaceDescT *desc = (QryCreateNamespaceDescT *)stmt->context->entry;
    if (desc == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "desc is NULL when set nsp create status.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DmNamespaceT *nsp = desc->nsp;
    Status ret = CataSetNamespaceCreateStatusById(nsp, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status QryExecuteSetCreatedWithIdx(QryStmtT *stmt, uint32_t idx)
{
    DB_POINTER(stmt);
    QryCreateVertexLabelDescT *desc = (QryCreateVertexLabelDescT *)stmt->context->entry;
    Status ret = GMERR_OK;
    QryCreateSingleVertexLabelDescT *singleDesc =
        (QryCreateSingleVertexLabelDescT *)DbListItem(&desc->vertexLabels, idx);
    DmVertexLabelT *vertexLabel = singleDesc->vertexLabel;
    ret = CataSetVertexLabelCreateStatusById(
        DbGetInstanceByMemCtx(vertexLabel->memCtx), vertexLabel->metaCommon.metaId, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

#ifdef EXPERIMENTAL_GUANGQI
static void QryHandleMergeTrxInfo(SeRunCtxHdT seRunCtx)
{
    if (seRunCtx->mergeTrxInfoList == NULL) {
        return;
    }
    DbMemCtxT *memCtx = SeTransGetTrxMemCtxForTrxMerge(seRunCtx);
    DbListT *mergeTrxInfoList = seRunCtx->mergeTrxInfoList;
    uint32_t GaListNum = DbListGetItemCnt(mergeTrxInfoList);
    if (GaListNum == 0) {
        seRunCtx->mergeTrxInfoList = NULL;
        DbDynMemCtxFree(memCtx, mergeTrxInfoList);
        return;
    }
    for (int32_t i = (int32_t)GaListNum - 1; i >= 0; --i) {
        TrxMergeInfoT *mergeInfo = (TrxMergeInfoT *)DbListItem(mergeTrxInfoList, i);
        QryYangValidateCommitForTrxMerge(mergeInfo->validateState, mergeInfo->trxId);
    }
    seRunCtx->mergeTrxInfoList = NULL;
    DbDestroyList(mergeTrxInfoList);
    DbDynMemCtxFree(memCtx, mergeTrxInfoList);
}
#endif

void QryHandleResAfterCommitTrans(QryStmtT *stmt, uint64_t trxId)
{
    QryStopTrxMonitor(stmt);
    ResetInteractiveTrxResource(stmt);

    if (!stmt->subsCtx.isSubs || stmt->eof) {  // 全量订阅场景仅在scan完毕后对cursor回收
        QryCloseCursors(stmt);
    }
    QryReleaseAllLabelLatch(stmt->session);
    ClearYangDiffData(stmt);
#ifdef EXPERIMENTAL_GUANGQI
    QryHandleMergeTrxInfo(stmt->session->seInstance);
#endif
    QryYangValidateCommit(stmt->session, trxId);
}

#ifdef EXPERIMENTAL_GUANGQI
Status QryExecuteMergeTransToParentTrx(QryStmtT *stmt)
{
    // 由客户端提交子事务触发
    TrxStateE trxState = SeTransGetState(stmt->session->seInstance);
    if (trxState != TRX_STATE_ACTIVE) {  // 当前事务必须是active状态才允许merge
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "trxState is %" PRIu32, (uint32_t)trxState);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    uint64_t curTrxId = SeTransGetTrxId(stmt->session->seInstance);
    uint32_t cloneId = SeTransGetParentCloneId(stmt->session->seInstance);
    DbMemCtxT *memCtx = SeTransGetTrxMemCtxForTrxMerge(stmt->session->seInstance);  // 跨连接使用的memctx
    GaListT *newLabelLatch = NULL;
    Status ret = QryCopyLabelLatchInfoForTrxMerge(stmt->session, memCtx, &newLabelLatch);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "alloc latch info unsucc, curTrxId:%" PRIu64 ", cloneId:%" PRIu32, curTrxId, cloneId);
        return ret;
    }
    // 获取当前事务的基本信息，找到父事务id
    TrxBaseInfoT parentTrxBaseInfo = {0};
    ret = SeTransGetTrxBaseInfoById(stmt->session->seInstance, cloneId, true, &parentTrxBaseInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get trx base info unsucc, curTrxId:%" PRIu64 ", cloneId:%" PRIu32, curTrxId, cloneId);
        QryDestoryLabelLatchInfoForTrxMerge(memCtx, newLabelLatch);
        return ret;
    }
    // 发送自调度消息，把子事务Id告诉父事务，由父事务处理
    CloneOrMergeTaskInfoT info = {
        .cloneId = cloneId, .trxId = curTrxId, .connId = stmt->session->conn->id, .opCode = MSG_OP_RPC_TX_MERGE};
    ret = QryAddCloneOrMergeTask(parentTrxBaseInfo.connId, &info);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "send self task, curTrxId:%" PRIu64 ", cloneId:%" PRIu32, curTrxId, cloneId);
        QryDestoryLabelLatchInfoForTrxMerge(memCtx, newLabelLatch);
        return ret;
    }
    // 合并前，把表锁信息挂到子事务下
    SeTransSetLabelLatchInfoForTrxMerge(stmt->session->seInstance, newLabelLatch);
    if (stmt->session->yangValidateCtx != NULL) {
        TrxMergeInfoT mergeInfo = {
            .validateState = *(uint32_t *)stmt->session->yangValidateCtx->state, .trxId = curTrxId};
        SeTransSetYangInfoForTrxMerge(stmt->session->seInstance, &mergeInfo);
    }
    ret = SeTransMerge(stmt->session->seInstance, stmt->session->conn->id);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "SeTransMerge, curTrxId:%" PRIu64 ", cloneId:%" PRIu32, curTrxId, cloneId);
        QryDestoryLabelLatchInfoForTrxMerge(memCtx, newLabelLatch);
        SeTransSetLabelLatchInfoForTrxMerge(stmt->session->seInstance, NULL);
        TrxMergeInfoT mergeInfo = {0};
        SeTransSetYangInfoForTrxMerge(stmt->session->seInstance, &mergeInfo);
        return ret;
    }
    QryYangValidateStateSetInvalid(stmt->session);
    QryResetAllLabelLatch(stmt->session);
    QryHandleResAfterCommitTrans(stmt, curTrxId);
    return GMERR_OK;
}
#endif

Status QryExecuteCommitTrans(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    Status ret = GMERR_OK;
    struct Session *session = stmt->session;
#ifdef EXPERIMENTAL_GUANGQI
    if (SeTransIsCloneTrx(session->seInstance)) {
        return QryExecuteMergeTransToParentTrx(stmt);
    }
#endif

#ifdef FEATURE_REPLICATION
    if (SeTransGetState(session->seInstance) == TRX_STATE_ACTIVE) {
        ret = QryReplicateTxCommit(stmt->session->logBuf);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "commit transaction in replication.");
            return ret;
        }
    }
#endif
    uint64_t trxId = SeTransGetTrxId(session->seInstance);
    QrySetTrxCommitCallBack(session);
    if (session->yangTreeCtx != NULL) {
        // 可靠订阅生成diff订阅消息失败会导致提交失败
        ret = QryGenerateYangSubsMessage(stmt);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "gen yang subs msg when commit trans.");  // LCOV_EXCL_LINE
            return ret;
        }
    }
    ret = QryCommitTrans(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {  // 当前只有乐观事务有返回冲突检查错误码的情况
        if (!stmt->autoCommit) {              // 如果是用户显示开启的事务，需要给用户处理的机会
            return ret;
        }
        QryReleaseStMgPubsubDataSet(&session->stmgSubDataSet);
    }
    QryHandleResAfterCommitTrans(stmt, trxId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        QryFinishSub(session, false);
        return ret;
    }
    if (session->stmgSubDataSet.subData.vertexLabel != NULL) {
        QryPushStMgDataAfterCommit(&session->stmgSubDataSet);
    }
    if (QryHasUsedSubs(session)) {
        QryFinishSub(session, true);
    }
    return GMERR_OK;
}

static bool QryIsNoNeedSelfRollback(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    if (stmt->autoCommit) {
        // 其他请求里调用的回滚，不是自处理消息的回滚或者客户端的回滚
        return false;
    }

    FixBufferT *req = stmt->session->req;
    if (req == NULL) {
        return false;
    }

    MsgHeaderT *reqHeader = RpcPeekMsgHeader(req);
    if (reqHeader == NULL || (reqHeader->flags & CS_FLAG_SELF_SCHEDULE) != CS_FLAG_SELF_SCHEDULE) {
        return false;
    }

    uint64_t trxId = 0;
    Status ret = FixBufGetUint64(req, &trxId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get trxId form message.");  // LCOV_EXCL_LINE
        return true;
    }

    if (SeTransGetState(stmt->session->seInstance) != TRX_STATE_ACTIVE) {
        // LCOV_EXCL_START
        DB_LOG_WARN(GMERR_DATA_EXCEPTION, "Self schedule rollback task, state=%" PRIi32 ".",
            (int32_t)SeTransGetState(stmt->session->seInstance));
        // LCOV_EXCL_STOP
        // 自调度消息返回错误无意义，还可能将正常事务的状态置为abort，参考DTS2024121104083
        return true;
    }

#ifdef EXPERIMENTAL_GUANGQI
    bool isTargetTrx = (trxId == SeTransGetTrxId(stmt->session->seInstance) ||
                        SeTransIsTargetTrxOldestParent(stmt->session->seInstance, trxId));
#else
    bool isTargetTrx = (trxId == SeTransGetTrxId(stmt->session->seInstance));
#endif
    if (!isTargetTrx) {
        // LCOV_EXCL_START
        DB_LOG_WARN(GMERR_DATA_EXCEPTION,
            "Self schedule rollback task, current trxId = %" PRIu64 ", while trxId = %" PRIu64 " need rollback",
            SeTransGetTrxId(stmt->session->seInstance), trxId);
        // LCOV_EXCL_STOP
        // 自调度消息返回错误无意义，还可能将正常事务的状态置为abort，参考DTS2024121104083
        return true;
    }

    return false;
}
// 后续改成session的方法
Status QryExecuteRollbackTrans(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    Status ret = GMERR_OK;

    bool isSelfSchedule = false;
    if (stmt->session->req != NULL) {
        FixBufferT *req = stmt->session->req;
        MsgHeaderT *reqHeader = RpcPeekMsgHeader(req);
        isSelfSchedule = (reqHeader->flags & CS_FLAG_SELF_SCHEDULE) == CS_FLAG_SELF_SCHEDULE;
    }

    if (stmt->session->isLongTrxRollBack) {
        // 当isLongTrxRollBack为true时不用真的释放资源
        if (!isSelfSchedule) {
            // 防止自调度的二次回滚导致逻辑错误
            stmt->session->isLongTrxRollBack = false;
        }
        return GMERR_OK;
    }

    if (QryIsNoNeedSelfRollback(stmt)) {
        return GMERR_OK;
    }

    QryStopTrxMonitor(stmt);
    bool isTimeout = !stmt->autoCommit && isSelfSchedule;  // 是否为长事务超时回滚
    ret = QryClearTrans(stmt->session, isTimeout);
    if (ret == GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE) {
        return ret;
    }

    ResetInteractiveTrxResource(stmt);
    ClearYangDiffData(stmt);
    QryYangValidateRollBackTrx(stmt->session);

    if (!stmt->autoCommit && isSelfSchedule) {
        // 当长事务需要回滚时，set标志位isLongTrxRollBack来通知后续回滚请求不用真的释放资源
        stmt->session->isLongTrxRollBack = true;
        (void)DbAtomicInc(&g_gmdbLongTrxRollbackCount);
    }
    return ret;
}

Status QryExecuteAbortTrans(QryStmtT *stmt)
{
    DB_POINTER(stmt);

    if (!stmt->session->isDBA) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INSUFFICIENT_PRIVILEGE, "drop transaction, not DBA.");  // LCOV_EXCL_LINE
        return GMERR_INSUFFICIENT_PRIVILEGE;
    }

    const QryAbortTransDescT *abortDesc = stmt->context->entry;
    TrxBaseInfoT trxBaseInfo = {0};
    Status ret = SeTransGetTrxBaseInfoById(stmt->session->seInstance, abortDesc->transId, false, &trxBaseInfo);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "get connection id by trx id=%" PRIu64 " when drop trx.", abortDesc->transId);
        // LCOV_EXCL_STOP
        return ret;
    }

    return QryAddLongTrxRollBackTask((uint32_t)trxBaseInfo.connId, abortDesc->transId);
}

static Status QryCopyToSavepointName(QryStmtT *stmt, SeTrxSavepointNameT *spName)
{
    DB_POINTER2(spName, spName);
    QrySavepointDescT *desc = (QrySavepointDescT *)stmt->context->entry;
    spName->length = desc->savepointName.len;
    if (spName->length == 0) {
        return GMERR_OK;
    }
    errno_t err = strcpy_s(spName->name, sizeof(spName->name), desc->savepointName.str);
    if (SECUREC_UNLIKELY(err != EOK)) {
        // LCOV_EXCL_START
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "copy savepoint name:%s.", desc->savepointName.str);
        // LCOV_EXCL_STOP
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status AddSavepointToStaticValueList(QryStmtT *stmt, SeTrxSavepointNameT *spName)
{
    QryLabelCacheT *labelCache = NULL;
    GaListT *labels = &stmt->session->trxStatistics.labels;
    for (uint32_t i = 0; i < DbGaListGetCount(labels); ++i) {
        labelCache = DbGaListGet(labels, i);
        if (labelCache == NULL) {
            // LCOV_EXCL_START
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_INTERNAL_ERROR, "get label statistics index=%" PRIu32 "when create savepoint.", i);
            // LCOV_EXCL_STOP
            return GMERR_INTERNAL_ERROR;
        }
        if (labelCache->labelDef.labelType == VERTEX_LABEL && DmIsYangVertexLabel(labelCache->labelDef.vertexLabel)) {
            // YANG 表不维护对应计数
            continue;
        }
        QryStatisticValueForSavepointT statVal = {0};
        statVal.spName.length = spName->length;
        errno_t err = memcpy_s(statVal.spName.name, SAVEPOINT_NAME_MAX_SIZE, spName->name, spName->length);
        if (SECUREC_UNLIKELY(err != EOK)) {
            // LCOV_EXCL_START
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_MEMORY_OPERATE_FAILED, "copy label statistic savepointName=%s.", spName->name);
            // LCOV_EXCL_STOP
            return GMERR_MEMORY_OPERATE_FAILED;
        }

        QryCopyStaticsValue(
            statVal.labelStatistic, labelCache->statistic.staticValue, !labelCache->labelDef.isNotPartitioned);

        Status ret = DbAppendListItem(&labelCache->statistic.staticValueListForSavepoint, &statVal);
        if (ret != GMERR_OK) {
            // LCOV_EXCL_START
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Append statistic value(savepointName=%s) to list.", spName->name);
            // LCOV_EXCL_STOP
            return ret;
        }
    }
    return GMERR_OK;
}

Status QryExecuteCreateSavepoint(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    if (stmt->autoCommit) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_FEATURE_NOT_SUPPORTED, "Implicit transaction create savepoint.");  // LCOV_EXCL_LINE
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    SeTrxSavepointNameT spName = {0};
    Status ret = QryCopyToSavepointName(stmt, &spName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = QryDiffAddSavePoint(stmt, spName.name, spName.length);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryYangInSertValidateSavePoint(stmt->session, spName.name, spName.length);
    if (ret != GMERR_OK) {
        QryDiffRemoveSavePointByName(stmt, spName.name, spName.length);
        return ret;
    }
    ret = SeTrxCreateSavepoint(stmt->session->seInstance, &spName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create savepoint=%s.", spName.name);  // LCOV_EXCL_LINE
        QryDiffRemoveSavePointByName(stmt, spName.name, spName.length);
        QryYangDeleteValidateSavePoint(stmt->session, spName.name, spName.length);
        return ret;
    }

    ret = AddSavepointToStaticValueList(stmt, &spName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "add savepoint=%s to static value list.", spName.name);  // LCOV_EXCL_LINE
        QryDiffRemoveSavePointByName(stmt, spName.name, spName.length);
        QryYangDeleteValidateSavePoint(stmt->session, spName.name, spName.length);
        (void)SeTrxReleaseSavepoint(stmt->session->seInstance, &spName);
        return ret;
    }
    return GMERR_OK;
}

static void FindStatValIndex(QryLabelStatisticT *labelStatic, SeTrxSavepointNameT *spName, int32_t *index)
{
    *index = -1;
    DbListT *statValList = &labelStatic->staticValueListForSavepoint;
    int32_t listCnt = (int32_t)DbListGetItemCnt(statValList);
    QryStatisticValueForSavepointT *statVal = NULL;
    for (int32_t i = listCnt - 1; i >= 0; --i) {
        statVal = (QryStatisticValueForSavepointT *)DbListItem(statValList, (uint32_t)i);
        if (statVal->spName.length != spName->length) {
            continue;
        }
        if (spName->length == 0 || strcmp(statVal->spName.name, spName->name) == 0) {
            *index = i;
            return;
        }
    }
}

static void QryExecStatisticSavepoint(QryStmtT *stmt, SeTrxSavepointNameT *spName, bool rollback)
{
    DB_POINTER2(stmt, spName);
    GaListT *labels = &stmt->session->trxStatistics.labels;

    for (uint32_t i = 0; i < DbGaListGetCount(labels); ++i) {
        QryLabelCacheT *labelCache = DbGaListGet(labels, i);
        if (labelCache == NULL) {
            // LCOV_EXCL_START
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "get label statistics index=%" PRIu32 ".", i);
            // LCOV_EXCL_STOP
            // 当前已通过一定的机制保障存储的回滚成功，即使出错也不会返回错误码，使得流程继续往下执行，保障存储与ee层savepoint的一致性
            DB_ASSERT(false);
            return;
        }
        if (labelCache->labelDef.labelType == VERTEX_LABEL && DmIsYangVertexLabel(labelCache->labelDef.vertexLabel)) {
            // YANG 表不维护对应计数
            continue;
        }

        int32_t statIndex = 0;
        FindStatValIndex(&labelCache->statistic, spName, &statIndex);
        if (statIndex < 0) {
            if (rollback) {
                QryResetStatisticsValue(labelCache->statistic.staticValue, !labelCache->labelDef.isNotPartitioned);
            }
            continue;
        }

        DbListT *statValList = &labelCache->statistic.staticValueListForSavepoint;
        int32_t cnt = (int32_t)DbListGetItemCnt(statValList);
        for (int32_t j = cnt - 1; j > statIndex; --j) {
            DbDelListItem(statValList, (uint32_t)j);
        }
        if (rollback) {
            QryStatisticValueForSavepointT *statVal =
                (QryStatisticValueForSavepointT *)DbListItem(statValList, (uint32_t)statIndex);
            QryCopyStaticsValue(
                labelCache->statistic.staticValue, statVal->labelStatistic, !labelCache->labelDef.isNotPartitioned);
        } else {
            DbDelListItem(statValList, (uint32_t)statIndex);
        }
    }
}

Status QryExecuteReleaseSavepoint(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    if (stmt->autoCommit) {
        // LCOV_EXCL_START
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Implicit transaction release savepoint.");
        // LCOV_EXCL_STOP
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    SeTrxSavepointNameT spName = {0};
    Status ret = QryCopyToSavepointName(stmt, &spName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SeTrxReleaseSavepoint(stmt->session->seInstance, &spName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "release savepoint=%s.", spName.name);  // LCOV_EXCL_LINE
        return ret;
    }
    QryDiffRemoveSavePointByName(stmt, spName.name, spName.length);
    QryYangDeleteValidateSavePoint(stmt->session, spName.name, spName.length);
    QryExecStatisticSavepoint(stmt, &spName, false);
    return GMERR_OK;
}

Status QryExecuteRollbackToSavepoint(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    if (stmt->autoCommit) {
        // LCOV_EXCL_START
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Implicit transaction rollback savepoint.");
        // LCOV_EXCL_STOP
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    SeTrxSavepointNameT spName = {0};
    Status ret = QryCopyToSavepointName(stmt, &spName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SeTrxRollbackToSavepoint(stmt->session->seInstance, &spName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "rollback to savepoint=%s.", spName.name);  // LCOV_EXCL_LINE
        return ret;
    }
    QryDiffRollbackFilterTreeInfo(stmt, spName.name, spName.length);
    QryYangRollBackValidateSavePoint(stmt->session, spName.name, spName.length);
    QryExecStatisticSavepoint(stmt, &spName, true);
    return GMERR_OK;
}

Status QryOptimisticTrxConflictCheck(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    return SeOptimisticTrxConflictCheck(stmt->session->seInstance);
}

#ifdef __cplusplus
}
#endif
