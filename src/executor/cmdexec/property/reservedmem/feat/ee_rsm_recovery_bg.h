/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: header of rsm recovery background
 * Author:
 * Create: 2024-09-28
 */

#ifndef EE_RSM_RECOVERY_BG_H
#define EE_RSM_RECOVERY_BG_H

#include "ee_stmt_interface.h"

#ifdef __cplusplus
extern "C" {
#endif

// 后台线程恢复状态，严格保序
// 同步修改QryRsmStateProcEntry和QryRsmSetRecoveryTaskInfo
typedef enum QryRsmRecoveryStatus {
    RSM_STATUS_RECOVER_INIT = 0,
    RSM_STATUS_DATA_RECOVER_FINISH,
    RSM_STATUS_TSP_RECOVER_FINISH,
    RSM_STATUS_MEMCTX_ADD_TASK_FINISH,
    RSM_STATUS_PAGE_ADD_TASK_FINISH,
    RSM_STATUS_MEM_CHECKING_FINISH,
    RSM_STATUS_WRITE_CACHE_ADD_TASK_FINISH,
    RSM_STATUS_WRITE_CACHE_MERGE_FINISH,
    RSM_STATUS_RECOVERY_FLAG_RESET_FINISH,
    RSM_STATUS_CREATE_PATH_FINISH,
    RSM_STATUS_RECOVER_FINISH,
} QryRsmRecoveryStatusE;

typedef enum QryRsmTaskType {
    TASK_VERTEX_LABEL_DATA_RECOVER = 0,  // vertexLabel元数据恢复完成后添加
    TASK_KV_LABEL_DATA_RECOVER,          // kvLabel元数据恢复完成后添加
    TASK_MEMCTX_RECOVER,                 // 所有表数据恢复完成后添加
    TASK_PAGE_RECOVER,                   // 所有表数据恢复完成后添加
    TASK_WRITE_CACHE_RECOVER,            // 所有数据恢复完成后添加
    TASK_WRITE_CACHE_MERGE,              // 所有数据恢复完成后添加
    TASK_TYPE_BUTT,
} QryRsmTaskTypeE;

typedef struct QryRsmRecoveryState {
    QryRsmRecoveryStatusE status;  // 表示后台线程恢复状态
    bool isFinish[TASK_TYPE_BUTT];
    uint32_t wcMergeTaskCnt;
    uint32_t wcMergeTaskFinishCnt;
} QryRsmRecoveryStateT;

QryRsmRecoveryStateT *QryGetRsmRecoveryState(void);

void QrySetRsmTaskFinish(QryRsmRecoveryStateT *state, QryRsmTaskTypeE taskType);

Status QryRsmAllocSessionAndStmt(SessionT **session, QryStmtT **stmt);

Status QryRsmAddRecoveryTask(QryRsmTaskTypeE taskType, uint32_t labelId);

#ifdef __cplusplus
}
#endif

#endif /* EE_RSM_RECOVERY_BG_H */
