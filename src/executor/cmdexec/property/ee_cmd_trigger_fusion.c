/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Commands for create and drop trigger
 * Author: GMDBv5 EE Team
 * Create: 2024-03-26
 */

#include "ee_systbl.h"
#include "ee_plan_node_ddl.h"
#include "ee_cmd_vertex_label_template.h"
#include "ee_cmd_router_fusion.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif
#ifndef IDS_HAOTIAN
Status CmdCreateTrigger(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    DB_POINTER2(cstate, cmd);
    DB_UNUSED(cmdResult);
    CreateTriggerStmtT *cmdReq = (CreateTriggerStmtT *)(void *)cmd;
    DmTriggerInfoT *trigInfo = cmdReq->trigInfo;

    // 创建前需确保触发器所属 vertexlabel 存在
    DmVertexLabelT *vertexLabel = NULL;
    uint32_t vlId = trigInfo->metaCommon.metaId;  // trigInfo 的 metaId 复用其所属表的id
    Status ret = CataGetVertexLabelById(DbGetInstanceByMemCtx(cstate->memCtx), vlId, &vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get vertexlabel, id: %" PRIu32 ".", vlId);
        return ret;
    }
    LabelRWLatchT *latch = NULL;
    ret = ExecGetLabelLatch(vertexLabel, &latch);
    if (ret != GMERR_OK) {
        (void)CataReleaseVertexLabel(vertexLabel);
        return ret;
    }

    ret = DmCreateTriggerInfo(trigInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "create trigger: %s.", trigInfo->metaCommon.metaName);
    }

    LabelWLatchRelease(latch);
    (void)CataReleaseVertexLabel(vertexLabel);
    return ret;
}

Status CmdDropTrigger(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    DB_POINTER2(cstate, cmd);
    DB_UNUSED(cmdResult);
    DropTriggerStmtT *cmdReq = (DropTriggerStmtT *)(void *)cmd;
    DmTriggerInfoT *trigInfo = cmdReq->trigInfo;

    Status ret = DmDropTriggerInfo(trigInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "create trigger: %s.", trigInfo->metaCommon.metaName);
    }
    return ret;
}
#endif

#ifdef __cplusplus
}
#endif
