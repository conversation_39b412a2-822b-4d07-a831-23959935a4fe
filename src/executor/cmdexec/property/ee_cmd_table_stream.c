/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author:
 * Create: 2024-11-05
 */
#include "ee_cmd_table_stream.h"

#include "cpl_stream_analyzer_common.h"
#include "cpl_opt_plangenerator_common.h"
#include "ee_cmd_router_fusion.h"
#include "ee_expression_eval.h"
#include "ee_stmt_fusion.h"

#include "ee_plan.h"
#include "ee_receiver.h"
#include "ee_cmd_epoll.h"

#include "ee_stream_timer_collection.h"
#include "ee_log.h"

uint32_t g_stream_ref_num = 0;
#define MAX_STREAM_REF_NUM 128
#define EE_STREAM_DEFAULT_EXPR_ITME_SIZE 8
#define THE_INIT_REF_ARRAY_SIZE 1024

void RemoveFromDispatchMap(DbOamapT *dispatchMap, DmStreamLinkNodeT *preTempNode, DmStreamVertexLabelT *currentNode)
{
    DmStreamDispatchValueT *dispatchValue =
        DbOamapLookup(dispatchMap, preTempNode->dispatchKey->keyHash, preTempNode->dispatchKey, NULL);
    if (dispatchValue == NULL) {
        DB_ASSERT(dispatchValue != NULL);
        return;
    }
    DmStreamLinkNodeT *currNode = NULL;
    DmStreamLinkNodeT *currNodeTmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(currNode, currNodeTmp, &dispatchValue->nextOfDispatch, node)
    {
        if (currNode->vertexLabel->base.metaCommon.metaId == currentNode->base.metaCommon.metaId) {
            DbLinkedListRemove(&currNode->node);
            DbDynMemCtxFree(g_parentStreamTopoCtx, currNode);
            break;
        }
    }
    if (DbLinkedListEmpty(&dispatchValue->nextOfDispatch)) {
        (void)DbOamapRemove(dispatchMap, preTempNode->dispatchKey->keyHash, preTempNode->dispatchKey);
    }
}

void RemoveFromDispatchAll(TagLinkedListT *tagLinkeds, DmStreamVertexLabelT *currentNode)
{
    DmStreamLinkNodeT *currNode = NULL;
    DmStreamLinkNodeT *currNodeTmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(currNode, currNodeTmp, tagLinkeds, node)
    {
        if (currNode->vertexLabel->base.metaCommon.metaId == currentNode->base.metaCommon.metaId) {
            DbLinkedListRemove(&currNode->node);
            DbDynMemCtxFree(g_parentStreamTopoCtx, currNode);
            break;
        }
    }
}

Status StreamCreateRef(CreateStreamRefStmtT *createStreamRef, StreamRefSlotT *streamRef)
{
    Status ret = GMERR_OK;
    if (createStreamRef->refType == STREAM_REF_MAP) {
        // streamRef用途: 存放ref kv键值对(读写锁)
        // 生命周期：ref表的生命周期
        // 释放方式: 跟随drop Ref释放
        streamRef->ref.refMap = (DbOamapT *)DbDynMemCtxAlloc(createStreamRef->refArray->memCtx, sizeof(DbOamapT));
        if (streamRef->ref.refMap == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, " ");
            return GMERR_OUT_OF_MEMORY;
        }
        ret = DbOamapInit(
            streamRef->ref.refMap, STREAM_INIT_REF_SIZE, DbOamapUint64Compare, createStreamRef->refArray->memCtx, true);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(createStreamRef->refArray->memCtx, streamRef->ref.refMap);
            return ret;
        }
    } else if (createStreamRef->refType == STREAM_REF_ARRAY) {
        // streamRef用途: 存放ref 数组
        // 生命周期：全局
        // 释放方式: 跟随drop ref释放
        streamRef->ref.refArray = (DbListT *)DbDynMemCtxAlloc(createStreamRef->refArray->memCtx, sizeof(DbListT));
        if (streamRef->ref.refArray == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, " ");
            return GMERR_OUT_OF_MEMORY;
        }
        DbCreateList(streamRef->ref.refArray, sizeof(MiniRefKVT), createStreamRef->refArray->memCtx);
        ret = DbListReserve(streamRef->ref.refArray, THE_INIT_REF_ARRAY_SIZE);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(createStreamRef->refArray->memCtx, streamRef->ref.refArray);
            return ret;
        }
    } else {
        DB_LOG_ERROR(GMERR_INVALID_NAME, "unsupported ref type");
        return GMERR_INVALID_NAME;
    }
    return ret;
}

Status CmdCreateStreamRef(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    DB_POINTER3(cstate, cmd, cmdResult);
    Status ret = GMERR_OK;
    if (g_stream_ref_num == MAX_STREAM_REF_NUM) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "too many stream ref");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    CreateStreamRefStmtT *createStreamRef = (CreateStreamRefStmtT *)cmd;
    // streamRef用途: 存放ref的名字, 类型和内容
    // 生命周期: 全局
    // 释放方式: drop ref释放
    StreamRefSlotT *streamRef =
        (StreamRefSlotT *)DbDynMemCtxAlloc(createStreamRef->refArray->memCtx, sizeof(StreamRefSlotT));
    (void)memset_s(streamRef, sizeof(StreamRefSlotT), 0x00, sizeof(StreamRefSlotT));
    streamRef->memCtx = createStreamRef->refArray->memCtx;
    uint32_t size = DM_STR_LEN(createStreamRef->refName);
    streamRef->name = (char *)DbDynMemCtxAlloc(createStreamRef->refArray->memCtx, size);
    if (streamRef->name == NULL) {
        DbDynMemCtxFree(createStreamRef->refArray->memCtx, streamRef);
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, " ");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memcpy_s(streamRef->name, size, createStreamRef->refName, size);
    streamRef->refType = createStreamRef->refType;
    // create refMap、refArray
    ret = StreamCreateRef(createStreamRef, streamRef);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(createStreamRef->refArray->memCtx, streamRef->name);
        DbDynMemCtxFree(createStreamRef->refArray->memCtx, streamRef);
        return ret;
    }
    // 初始化vertexLabelIds，用于存储使用该ref表的vertexLabel的id
    DbCreateList(&streamRef->vertexLabelIds, sizeof(uint32_t), createStreamRef->refArray->memCtx);
    uint32_t left = 0;
    int32_t refArrayIdx = -1;
    if (BinarySearchCharList(createStreamRef->refArray, &left, &refArrayIdx, createStreamRef->refName) != NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DUPLICATE_TABLE, "Duplicate name for stream ref.");
        return GMERR_DUPLICATE_TABLE;
    }
    ret = DbInsertListItem(createStreamRef->refArray, &streamRef, left);
    if (ret != GMERR_OK) {
        return ret;
    }
    g_stream_ref_num++;
    return GMERR_OK;
}

Status CmdDropStreamRef(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    DB_POINTER3(cstate, cmd, cmdResult);
    DropStreamRefStmtT *dropStreamRef = (DropStreamRefStmtT *)cmd;
    // 在array ref中找表
    uint32_t left = 0;
    int32_t refArrayIdx = -1;
    StreamRefSlotT *streamRef =
        BinarySearchCharList(dropStreamRef->refArray, &left, &refArrayIdx, dropStreamRef->refName);
    if (streamRef == NULL) {
        return GMERR_UNDEFINED_TABLE;
    }
    if (DbListGetItemCnt(&streamRef->vertexLabelIds) > 0) {
        DB_LOG_AND_SET_LASERR(GMERR_RESTRICT_VIOLATION, "cannot remove label with ref");
        return GMERR_RESTRICT_VIOLATION;
    }
    DbDelListItem(dropStreamRef->refArray, refArrayIdx);
    DbDynMemCtxFree(dropStreamRef->refArray->memCtx, streamRef->name);
    DbDynMemCtxFree(dropStreamRef->refArray->memCtx, streamRef);
    g_stream_ref_num--;
    return GMERR_OK;
}

// Remove the reference dependency from the global reference map.
// Given the reference name, we explore the globalRef list, until we find a match.
// Then in the interal loop we explore the vertexLabelIds contained within, until we find
// the first instance of the current vertexId: this is the one that must be removed to free the
// reference from its vertexLabel dependency.
Status StreamRemoveRef(DmVertexLabelT *vertexLabel, const char *refName)
{
    uint32_t vertexLabelId = vertexLabel->metaCommon.metaId;
    DbListT *globalRef = StreamGetGlobalRef();
    for (uint32_t i = 0; i < DbListGetItemCnt(globalRef); i++) {
        StreamRefSlotT *streamRef = *(StreamRefSlotT **)DbListItem(globalRef, i);
        if (DbStrCmp(streamRef->name, refName, false) == 0) {
            for (uint32_t j = 1; j <= DbListGetItemCnt(&streamRef->vertexLabelIds); j++) {
                uint32_t *refId = (uint32_t *)DbListItem(&streamRef->vertexLabelIds, j - 1);
                if (vertexLabelId == *refId) {
                    DbDelListItem(&streamRef->vertexLabelIds, j - 1);
                    return GMERR_OK;
                }
            }
        }
    }
    // We are not supposed to reach this point... something weird must have happened!
    return GMERR_INTERNAL_ERROR;
}

// This function is used to traverse the old where expression tree, looking for streamref
// nodes. For each of these nodes we find we extract the reference name, and then we pass
// it to StreamRemoveRef(..) which removes the current vertexId dependency.
Status TraverseExprAndRemoveRefs(DmVertexLabelT *vertexLabel, ExprT *inExpr)
{
    DB_POINTER2(vertexLabel, inExpr);
    Status ret = GMERR_OK;

    ExprOpTypeE exprOpType = inExpr->opType;
    if (ExprIsBinary(exprOpType)) {
        ExprBinaryT *binaryExpr = CastToBinary(inExpr);
        ret = TraverseExprAndRemoveRefs(vertexLabel, binaryExpr->left);
        if (ret != GMERR_OK) {
            return ret;
        }
        return TraverseExprAndRemoveRefs(vertexLabel, binaryExpr->right);
    } else if (ExprIsUnary(exprOpType)) {
        ExprUnaryT *unaryExpr = CastToUnary(inExpr);
        return TraverseExprAndRemoveRefs(vertexLabel, unaryExpr->child);
    } else if (exprOpType == EXPR_OP_STREAMREF) {
        // While traversing the expression tree, we found a streamref.
        ExprFuncT *func = CastToFunc(inExpr);
        ExprConstT *value = CastToConst(func->array.expr[0]);
        const char *refName = (const char *)(value->arg.value.constStrAddr);
        ret = StreamRemoveRef(vertexLabel, refName);
    }

    return ret;
}

Status StreamSqlAlterReplaceWhereConditionInternal(
    StreamQryStmtT *fmqs, ExprStateT *newWhereState, ExprStateT **oldQualExpr)
{
    switch (fmqs->base.planState->tagType) {
        case T_STREAMSCAN: {
            StreamScanStateT *scanState = (StreamScanStateT *)fmqs->base.planState;
            *oldQualExpr = scanState->scan.qualExpr;
            scanState->scan.qualExpr = newWhereState;
            break;
        }
        case T_STREAM_WINDOW_TABLE: {
            StreamWindowTableStateT *tableState = (StreamWindowTableStateT *)fmqs->base.planState;
            *oldQualExpr = tableState->qualExpr;
            tableState->qualExpr = newWhereState;
            break;
        }
        case T_STREAM_AGG_OVER_WINDOW: {
            StreamAggOverWindowStateT *tableState = (StreamAggOverWindowStateT *)fmqs->base.planState;
            *oldQualExpr = tableState->aggCommon.qualExpr;
            tableState->aggCommon.qualExpr = newWhereState;
            break;
        }
        case T_STREAM_WINDOW_AGG: {
            StreamWindowAggStateT *tableState = (StreamWindowAggStateT *)fmqs->base.planState;
            *oldQualExpr = tableState->aggCommon.qualExpr;
            tableState->aggCommon.qualExpr = newWhereState;
            break;
        }
        case T_SEQ_DISTINCT_COUNT: {
            SeqDistctCntStateT *tableState = (SeqDistctCntStateT *)fmqs->base.planState;
            DB_ASSERT(tableState->base.planState.leftTree->tagType == T_STREAMSCAN);
            StreamScanStateT *scanState = (StreamScanStateT *)tableState->base.planState.leftTree;
            *oldQualExpr = scanState->scan.qualExpr;
            scanState->scan.qualExpr = newWhereState;
            break;
        }
        default:
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "unsupported plan state type");
            return GMERR_FEATURE_NOT_SUPPORTED;
    }

    return GMERR_OK;
}

Status StreamRegisterRefId(const char *refName, uint32_t vertexLabelId)
{
    uint32_t left = 0;
    int32_t refArrayIdx = -1;
    DbListT *globalRef = StreamGetGlobalRef();
    StreamRefSlotT *streamRef = BinarySearchCharList(globalRef, &left, &refArrayIdx, refName);
    if (streamRef == NULL) {
        return GMERR_UNDEFINED_TABLE;
    }
    left = 0;
    (void)BinarySearchIntList(
        &streamRef->vertexLabelIds, &left, DbListGetItemCnt(&streamRef->vertexLabelIds), vertexLabelId);
    return DbInsertListItem(&streamRef->vertexLabelIds, &vertexLabelId, left);
}

Status StreamRegistVertexLabelId(DmStreamVertexLabelT *streamVertexLabel)
{
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < DbListGetItemCnt(&streamVertexLabel->refName); i++) {
        char *name = *(char **)DbListItem(&streamVertexLabel->refName, i);
        ret = StreamRegisterRefId(name, streamVertexLabel->base.metaCommon.metaId);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    // We need to empty the refName list: despite being used only for temporarely storing
    // the newly added reference names, the list itself is part of the vertexLabel, and it
    // will be saved to the catalog; however, the strings (list elements) are allocated on
    // the temporary memory context and will be thrown away.
    // This would create a problem the next time we access the list through the function
    // StreamSqlRegisterRefName(..), i.e. when altering the content of a WHERE clause
    // associated with a node, as we would have a non-empty list with each element pointing
    // to a random / empty memory location.
    for (uint32_t i = DbListGetItemCnt(&streamVertexLabel->refName); i != 0; --i) {
        DbDelListItem(&streamVertexLabel->refName, i - 1);
    }

    return ret;
}

// Executing the ALTER WHERE statement
// Here we convert the logical plan into an expression state (we can afford skipping the middle conversion
// to physical plan, as we are dealing with just the where condition).
// Then, we replace the old where state - it is always a 'qualExpr' inside a PlanState - with the new one.
// Before leaving the function, if we are dealing with a streamref we must update the global reference list
// which keeps track of vertexLabel dependencies; then we update the same list with the references contained
// in the new where condition.
Status StreamSqlAlterReplaceWhereCondition(DmVertexLabelT *vertexLabel, ExprT *newWhereExpr)
{
    DB_POINTER2(vertexLabel, newWhereExpr);
    Status ret = GMERR_OK;
    DmStreamVertexLabelT *streamVertexLabel = (DmStreamVertexLabelT *)vertexLabel;
    StreamQryStmtT *fmqs = NULL;

    // Recovering the fusion exec statement, distinguishing between view and sink nodes.
    if (streamVertexLabel->streamType == STREAM_LABEL_TYPE_SINK) {
        fmqs = (StreamQryStmtT *)streamVertexLabel->streamMeta.sinkMeta.streamCachedPlan.fusionModelQryStmt;
    } else if (streamVertexLabel->streamType == STREAM_LABEL_TYPE_VIEW) {
        fmqs = (StreamQryStmtT *)streamVertexLabel->streamMeta.viewMeta.streamCachedPlan.fusionModelQryStmt;
    }

    // Convert the new where ExprT into an ExprStateT.
    ExprStateT *newWhereState = NULL;
    ret = ExprStateBuildDeepCopy(newWhereExpr, streamVertexLabel->streamMemCtx, &newWhereState);
    if (ret != GMERR_OK) {
        return ret;
    }

    // Get the cached plan state. Its type varies according to the entity we are dealing with, but they all
    // have in common the presence of a 'qualExpr' containing the actual WHERE expression.
    // Contextually we change the old expression with the new one (newWhereState).
    ExprStateT *oldQualExpr = NULL;
    ret = StreamSqlAlterReplaceWhereConditionInternal(fmqs, newWhereState, &oldQualExpr);
    if (ret != GMERR_OK) {
        ExprDestroy(streamVertexLabel->streamMemCtx, newWhereExpr);
        return ret;
    }

    // Delete the old entries from the global ref list.
    if (oldQualExpr != NULL) {
        ret = TraverseExprAndRemoveRefs(vertexLabel, oldQualExpr->expr);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "remove old ref");
            return GMERR_INTERNAL_ERROR;
        }
    }

    // And update the reference with the new one, with the same technique used while
    // building the stream vertex label for a view or sink node.
    ret = StreamRegistVertexLabelId(streamVertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    // We should delete the old expression, btw...
    if (oldQualExpr != NULL) {
        ExprDestroy(streamVertexLabel->streamMemCtx, oldQualExpr->expr);
        DbDynMemCtxFree(streamVertexLabel->streamMemCtx, oldQualExpr);
    }

    return ret;
}

Status CmdAlterStream(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    DB_POINTER3(cstate, cmd, cmdResult);
    Status ret = GMERR_OK;
    StreamAlterStmtT *alterStream = (StreamAlterStmtT *)cmd;
    DmVertexLabelT *vertexLabel = alterStream->vertexLabel;

    // We are now ready to convert the logical plan to a physical one
    // We still use the temporay memCtx to build the intermediate newWhereExpr; then we save the newWhereState in
    // the actual vertexLabel memCtx - this is enough to save it
    ExprT *newWhereExpr = NULL;
    ret =
        CreateFilterExpressionAsTree(alterStream->tempMemCtx, (IRExprT *)(alterStream->newWhereIRExpr), &newWhereExpr);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = StreamSqlAlterReplaceWhereCondition(vertexLabel, newWhereExpr);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

uint32_t getPathToSink(DmStreamVertexLabelT *vertex)
{
    DB_POINTER(vertex);
    if (vertex->streamType == STREAM_LABEL_TYPE_SINK) {
        return 1;
    }
    TagLinkedListT *nxtList = StreamGetNextList(vertex);
    if (nxtList == NULL) {
        return 0;
    }
    DmStreamLinkNodeT *nxtTempNode = NULL;
    DmStreamLinkNodeT *nxtTmp = NULL;
    uint32_t path = 0;
    LIST_FOR_EACH_ENTRY_SAFE(nxtTempNode, nxtTmp, nxtList, node)
    {
        path += nxtTempNode->pathToSink;
    }
    return path;
}

void UpdatePathToSink(DmStreamVertexLabelT *streamVertex, int32_t pathToSink)
{
    TagLinkedListT *preList = StreamGetPreList(streamVertex);
    if (preList == NULL) {
        if (streamVertex->streamType == STREAM_LABEL_TYPE_SOURCE) {
            int32_t newPath = (int32_t)streamVertex->streamMeta.sourceMeta.totalPathToSink + pathToSink;
            newPath = newPath < 0 ? 0 : newPath;
            streamVertex->streamMeta.sourceMeta.totalPathToSink = (uint32_t)newPath;
        }
        return;
    }
    DmStreamLinkNodeT *preTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(preTempNode, tmp, preList, node)
    {
        int32_t prePath =
            (int32_t)preTempNode->pathToSink + pathToSink < 0 ? 0 : (int32_t)preTempNode->pathToSink + pathToSink;
        preTempNode->pathToSink = (uint32_t)prePath;
        UpdatePathToSink(preTempNode->vertexLabel, pathToSink);
    }
}

void DropDispatchRelation(DmStreamLinkNodeT *preTempNode, DmStreamLinkNodeT *nxtTempNode)
{
    DmStreamVertexLabelT *preVertex = preTempNode->vertexLabel;
    DmStreamVertexLabelT *nxtVertex = nxtTempNode->vertexLabel;
    DbOamapT *prevDispatchMap = NULL;
    TagLinkedListT *prevTagList = NULL;
    DmStreamDispatchT *prevDispatch = NULL;
    if (preVertex->streamType == STREAM_LABEL_TYPE_SOURCE) {
        prevDispatch = &preVertex->streamMeta.sourceMeta.streamDispatch;
        prevDispatchMap = &preVertex->streamMeta.sourceMeta.streamDispatch.dispatchMap;
        prevTagList = &preVertex->streamMeta.sourceMeta.streamDispatch.nextOfAll;
    } else if (preVertex->streamType == STREAM_LABEL_TYPE_VIEW) {
        prevDispatch = &preVertex->streamMeta.viewMeta.streamDispatch;
        prevDispatchMap = &preVertex->streamMeta.viewMeta.streamDispatch.dispatchMap;
        prevTagList = &preVertex->streamMeta.viewMeta.streamDispatch.nextOfAll;
    } else {
        return;
    }
    if (prevDispatch->hasDispatch) {
        if (preTempNode->dispatchKey != NULL) {
            RemoveFromDispatchMap(prevDispatchMap, preTempNode, nxtVertex);
        } else {
            RemoveFromDispatchAll(prevTagList, nxtVertex);
        }
    }
}

void UpdateDropPathToSink(DmStreamLinkNodeT *nxtTempNode, DmStreamVertexLabelT *preVertex)
{
    uint32_t pathToSink = nxtTempNode->pathToSink;
    TagLinkedListT *preListNode = StreamGetPreList(nxtTempNode->vertexLabel);
    DmStreamLinkNodeT *preTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(preTempNode, tmp, preListNode, node)
    {
        DmStreamVertexLabelT *pre = preTempNode->vertexLabel;
        if (DbStrCmp(pre->base.metaCommon.metaName, preVertex->base.metaCommon.metaName, true) == 0) {
            UpdatePathToSink(pre, 0 - (int32_t)pathToSink);
        }
    }
}

void DropNextNodeInUnion(DmStreamLinkNodeT *preTempNode, DmStreamVertexLabelT *streamVertex)
{
    DmStreamVertexLabelT *preVertex = preTempNode->vertexLabel;
    TagLinkedListT *nxtListNode = StreamGetNextList(preVertex);
    DmStreamLinkNodeT *nxtTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(nxtTempNode, tmp, nxtListNode, node)
    {
        DmStreamVertexLabelT *nxtVertexLabel = nxtTempNode->vertexLabel;
        if (DbStrCmp(streamVertex->base.metaCommon.metaName, nxtVertexLabel->base.metaCommon.metaName, true) == 0) {
            UpdateDropPathToSink(nxtTempNode, preVertex);
            DbLinkedListRemove(&nxtTempNode->node);
            DropDispatchRelation(preTempNode, nxtTempNode);
            DbDynMemCtxFree(g_parentStreamTopoCtx, nxtTempNode);
            return;
        }
    }
}

Status StreamUnionDropVertex(StreamAlterUnionStmtT *alterUnionStmt, DmVertexLabelT *vertex, DmVertexLabelT *unionVertex)
{
    Status ret = GMERR_INTERNAL_ERROR;
    DmStreamVertexLabelT *streamVertex = (DmStreamVertexLabelT *)(vertex);
    DmStreamVertexLabelT *streamUnionVertex = (DmStreamVertexLabelT *)(unionVertex);
    TagLinkedListT *list = StreamGetPreList(streamVertex);
    DmStreamLinkNodeT *preTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(preTempNode, tmp, list, node)
    {
        DmStreamVertexLabelT *preVertexLabel = preTempNode->vertexLabel;
        if (DbStrCmp(unionVertex->metaCommon.metaName, preVertexLabel->base.metaCommon.metaName, true) == 0) {
            DropNextNodeInUnion(preTempNode, streamVertex);
            DbLinkedListRemove(&preTempNode->node);
            DbDynMemCtxFree(g_parentStreamTopoCtx, preTempNode);
            ret = GMERR_OK;
            break;
        }
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unable to drop in executor");
        return ret;
    }
    if (streamUnionVertex->streamType == STREAM_LABEL_TYPE_SOURCE) {
        streamUnionVertex->streamMeta.sourceMeta.nextNum--;
    }
    if (streamUnionVertex->streamType == STREAM_LABEL_TYPE_VIEW) {
        streamUnionVertex->streamMeta.viewMeta.nextNum--;
    }
    return ret;
}

// 遍历前驱节点链表，将当前节点从前驱节点的next链表中移除
void StreamRemoveCurrentNodeFromPrevNextList(DmStreamVertexLabelT *currentNode)
{
    // 查找当前节点的前驱链表
    TagLinkedListT *prevList = StreamGetPreList(currentNode);
    // 遍历前驱节点链表
    if (prevList == NULL) {
        return;
    }
    DmStreamLinkNodeT *preTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(preTempNode, tmp, prevList, node)
    {
        DmStreamVertexLabelT *preVertexLabel = preTempNode->vertexLabel;
        // 接下来需要更新当前前驱节点的后继节点链表。同时遍历这些前驱节点的前驱节点链表，让里面该节点的pathtosink减1。
        TagLinkedListT *preNextList = StreamGetNextList(preVertexLabel);
        DmStreamLinkNodeT *preNextListTmpNode = NULL;
        DmStreamLinkNodeT *tmpNext = NULL;
        LIST_FOR_EACH_ENTRY_SAFE(preNextListTmpNode, tmpNext, preNextList, node)
        {
            if (preNextListTmpNode->vertexLabel == currentNode) {
                break;
            }
        }
        if (preNextList == &preNextListTmpNode->node) {
            continue;
        }
        // 该节点需要被移除
        if (preVertexLabel->streamType == STREAM_LABEL_TYPE_SOURCE) {
            preVertexLabel->streamMeta.sourceMeta.nextNum--;
            if (preTempNode->dispatchKey != NULL) {
                RemoveFromDispatchMap(
                    &preVertexLabel->streamMeta.sourceMeta.streamDispatch.dispatchMap, preTempNode, currentNode);
            } else if (preVertexLabel->streamMeta.sourceMeta.streamDispatch.hasDispatch) {
                RemoveFromDispatchAll(&preVertexLabel->streamMeta.sourceMeta.streamDispatch.nextOfAll, currentNode);
            }
        } else if (preVertexLabel->streamType == STREAM_LABEL_TYPE_VIEW) {
            preVertexLabel->streamMeta.viewMeta.nextNum--;
            if (preTempNode->dispatchKey != NULL) {
                RemoveFromDispatchMap(
                    &preVertexLabel->streamMeta.viewMeta.streamDispatch.dispatchMap, preTempNode, currentNode);
            } else if (preVertexLabel->streamMeta.viewMeta.streamDispatch.hasDispatch) {
                RemoveFromDispatchAll(&preVertexLabel->streamMeta.viewMeta.streamDispatch.nextOfAll, currentNode);
            }
        }
        DbLinkedListRemove(&preNextListTmpNode->node);
        (void)CataReleaseVertexLabel((DmVertexLabelT *)preNextListTmpNode->vertexLabel);
        DbDynMemCtxFree(g_parentStreamTopoCtx, preNextListTmpNode);
    }
}

// 遍历自己的前驱节点链表，回收自己的前驱链表资源
void StreamRemovePrevList(DmStreamVertexLabelT *streamVertexLabel)
{
    TagLinkedListT *prev = StreamGetPreList(streamVertexLabel);
    if (prev == NULL) {
        return;
    }
    while (!DbLinkedListEmpty(prev)) {
        DmStreamLinkNodeT *node = LIST_HEAD(prev, DmStreamLinkNodeT, node);
        DbLinkedListRemoveHead(prev);
        if (node->dispatchKey != NULL) {
            DbDynMemCtxFree(g_parentStreamTopoCtx, node->dispatchKey);
        }
        (void)CataReleaseVertexLabel((DmVertexLabelT *)node->vertexLabel);
        DbDynMemCtxFree(g_parentStreamTopoCtx, node);
    }
}

// 遍历前驱节点链表，将当前节点从前驱节点的next链表中移除
void StreamUpdatePathToSinkWhenRemoveSinkNode(DmStreamVertexLabelT *currentNode)
{
    // 查找当前节点的前驱链表
    TagLinkedListT *prevList = StreamGetPreList(currentNode);
    // 遍历前驱节点链表
    if (prevList != NULL) {
        DmStreamLinkNodeT *currTempNode = NULL;
        DmStreamLinkNodeT *tmp = NULL;
        LIST_FOR_EACH_ENTRY_SAFE(currTempNode, tmp, prevList, node)
        {
            DmStreamVertexLabelT *currVertexLabel = currTempNode->vertexLabel;
            // 递归遍历前驱节点链表，更新pathtosink
            StreamUpdatePathToSinkWhenRemoveSinkNode(currVertexLabel);

            // 接下来需要更新当前前驱节点的后继节点链表。找到当前节点，把该节点的pathtosink减1。
            TagLinkedListT *currNextList = StreamGetNextList(currVertexLabel);
            DmStreamLinkNodeT *nextTempNode = NULL;
            DmStreamLinkNodeT *tmpNext = NULL;
            LIST_FOR_EACH_ENTRY_SAFE(nextTempNode, tmpNext, currNextList, node)
            {
                if (nextTempNode->vertexLabel == currentNode) {
                    nextTempNode->pathToSink--;
                    break;
                }
            }
            // source 节点要维护totalPathToSink
            if (currVertexLabel->streamType == STREAM_LABEL_TYPE_SOURCE) {
                currVertexLabel->streamMeta.sourceMeta.totalPathToSink--;
            }
        }
    }
}

void StreamDropRefId(DmStreamVertexLabelT *streamVertexLabel)
{
    uint32_t vertexLabelId = streamVertexLabel->base.metaCommon.metaId;
    DbListT *globalRef = StreamGetGlobalRef();
    for (uint32_t i = 0; i < DbListGetItemCnt(globalRef); i++) {
        StreamRefSlotT *streamRef = *(StreamRefSlotT **)DbListItem(globalRef, i);
        for (uint32_t j = 1; j <= DbListGetItemCnt(&streamRef->vertexLabelIds); j++) {
            uint32_t *refId = (uint32_t *)DbListItem(&streamRef->vertexLabelIds, j - 1);
            if (vertexLabelId == *refId) {
                DbDelListItem(&streamRef->vertexLabelIds, j - 1);
                j--;
            }
        }
    }
    return;
}

void CmdProcessDropStreamTable(DmVertexLabelT *vertexLabel)
{
    if (vertexLabel->metaVertexLabel->vertexLabelType != VERTEX_TYPE_STREAM) {
        return;
    }
    DmStreamVertexLabelT *streamVertexLabel = (DmStreamVertexLabelT *)vertexLabel;
    if (streamVertexLabel->streamType == STREAM_LABEL_TYPE_SOURCE) {
        // file injection only exists in source node
        RemoveFileEventOutOfEpoll(streamVertexLabel);
    } else if (((DmStreamVertexLabelT *)vertexLabel)->streamType == STREAM_LABEL_TYPE_SINK) {
        // 对创建的domain socket进行unlink操作
        if (streamVertexLabel->streamMeta.sinkMeta.dispenser.sinkType == STREAM_SINK_SOCKET) {
            DbListT *socketList = &streamVertexLabel->streamMeta.sinkMeta.dispenser.sinkSocketMeta.socketAddrList;
            uint32_t cnt = DbListGetItemCnt(socketList);
            for (uint32_t i = 0; i < cnt; i++) {
                int32_t clientFd = *(int32_t *)DbListItem(socketList, i);
                close(clientFd);
            }
            DbOamapDeletation(streamVertexLabel->streamMeta.sinkMeta.dispenser.sinkSocketMeta.serverFd);
            char *currentAddrs = streamVertexLabel->streamMeta.sinkMeta.dispenser.sinkSocketMeta.socketAddrs;
            unlink(currentAddrs);
        }
        if (streamVertexLabel->streamMeta.sinkMeta.dispenser.postBufCache.timer != NULL) {
            // 回收timer切置为废弃
            StreamRemoveTimer(streamVertexLabel->streamMeta.sinkMeta.dispenser.postBufCache.timer);
        }
        StreamUpdatePathToSinkWhenRemoveSinkNode(streamVertexLabel);
    }
    StreamRemoveCurrentNodeFromPrevNextList(streamVertexLabel);
    StreamRemovePrevList(streamVertexLabel);

    if (streamVertexLabel->streamType == STREAM_LABEL_TYPE_VIEW ||
        streamVertexLabel->streamType == STREAM_LABEL_TYPE_SINK) {
        DmStreamCachedPlanT *cachedPlan = (streamVertexLabel->streamType == STREAM_LABEL_TYPE_VIEW) ?
                                              &streamVertexLabel->streamMeta.viewMeta.streamCachedPlan :
                                              &streamVertexLabel->streamMeta.sinkMeta.streamCachedPlan;
        if (cachedPlan->fusionModelQryStmt != NULL) {
            // 释放执行计划中缓存的本节点
            CataReleaseVertexLabel(vertexLabel);
        }
    }

    // 删除g_ref表中的vertexLabelId
    StreamDropRefId(streamVertexLabel);
}

static void SetStreamScanLabel(void *plan, DmStreamVertexLabelT *vertexLabel)
{
    PlanT *tempPlan = (PlanT *)plan;
    if (plan == NULL) {
        return;
    }
    SetStreamScanLabel((void *)tempPlan->leftTree, vertexLabel);
    SetStreamScanLabel((void *)tempPlan->rightTree, vertexLabel);
    if (tempPlan->tagType == T_STREAMSCAN) {
        StreamScanT *scanPlan = (StreamScanT *)tempPlan;
        scanPlan->currentStreamLabel = vertexLabel;
    }
}

Status InitStreamPlanCache(DmStreamVertexLabelT *streamLabel, DmStreamCachedPlanT *cachedPlan, void *plan)
{
    SetStreamScanLabel(plan, (DmStreamVertexLabelT *)streamLabel);
    // stmt用途: 用于存放计划树与转发类型
    // 生命周期: 活动范围伴随vertexLabel的生命周期
    // 释放方式: vertexLabel释放时一并释放
    StreamQryStmtT *stmt = DbDynMemCtxAlloc(streamLabel->streamMemCtx, sizeof(StreamQryStmtT));
    if (stmt == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, " ");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(stmt, sizeof(StreamQryStmtT), 0, sizeof(StreamQryStmtT));
    stmt->base.memCtx = streamLabel->streamMemCtx;
    stmt->base.receiverType = STREAM_FORWARD;
    stmt->base.planTree = plan;
    stmt->base.needResult = true;
    // 缓存在本表的执行计划中的VertexLabel指针。CmdDropTable时候释放，或者由running的人自行释放
    DmStreamVertexLabelT *tmpVertexLabel = NULL;
    CataGetStreamVertexLabelById(streamLabel->base.metaCommon.metaId, &tmpVertexLabel);
    stmt->streamVertexLabel = tmpVertexLabel;
    stmt->streamLabelType = streamLabel->streamType;
    Status ret = QryInitReceiver(NULL, &stmt->base);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = ExecutorStart(&stmt->base);
    if (ret != GMERR_OK) {
        ExecutorFinish(&stmt->base);
        return ret;
    }
    cachedPlan->fusionModelQryStmt = stmt;
    cachedPlan->planTree = plan;
    return GMERR_OK;
}

Status CreateStreamLabelMemCtx(uint32_t labelId, const char *labelName, DbMemCtxT **streamMemCtx)
{
    DB_POINTER2(labelName, streamMemCtx);
    DbMemCtxT *appDynCtx = DbSrvGetAppDynCtx(DbGetProcGlobalId());
    if (appDynCtx == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, " ");
        return GMERR_INTERNAL_ERROR;
    }
    char ctxName[DB_MAX_MEMCTX_NAME - 1];
    (void)memset_s(ctxName, DB_MAX_MEMCTX_NAME - 1, 0x00, DB_MAX_MEMCTX_NAME - 1);

    int32_t iret = snprintf_truncated_s(ctxName, DB_MAX_MEMCTX_NAME - 1, "streamLabel %s", labelName);
    if (iret < 0) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, " ");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    DbMemCtxArgsT args = {0};
    args.dynCtxNeedRecycle = true;
    args.liteModOn = true;
    args.collectAllocSizeOnThisTree = true;
    // streamMemCtx用途: 用于vertexLabel的内存分配
    // 生命周期: vertexLabel的生命周期
    // 释放方式: 释放整个vertexLabel时释放
    DbMemCtxT *tmpMemCtx = DbCreateDynMemCtx(appDynCtx, true, ctxName, &args);
    if (tmpMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, " ");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    *streamMemCtx = tmpMemCtx;
    return GMERR_OK;
}

static inline void DestroyStreamLabelMemCtx(DmStreamVertexLabelT *streamLabel)
{
    DbDeleteDynMemCtx(streamLabel->streamMemCtx);
}

bool StreamIsTaskPool(void);
Status InitStreamPlanAndDataSpace(
    DmStreamVertexLabelT *cataStreamlabel, void *plan, DmStreamCachedPlanT *cachedPlan, DmStreamDataSpaceT *dataSpace)
{
    DbSpinInit(StreamGetForwardStatusLock(cataStreamlabel));
    DmStreamForwardStatusE *status = StreamGetForwardStatus(cataStreamlabel);
    *status = STREAM_FORWARD_STATUS_IDLE;
    Status ret = InitStreamPlanCache(cataStreamlabel, cachedPlan, plan);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbSpinInit(&dataSpace->dataLock);
    ret = NewNoLimitedAA(cataStreamlabel->streamMemCtx, (AAT **)&dataSpace->aa);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create data space");
        return ret;
    }
    DrtInstanceT *drtInstance = DrtGetInstance(DbGetInstanceByMemCtx(cataStreamlabel->streamMemCtx));
    if (!(drtInstance->scheMgr.scheMode == SCHEDULE_THREAD_POOL)) {
        AASetNoOwnerShip(dataSpace->aa);
    }

    return GMERR_OK;
}

static Status InitStreamSinkDestnation(DmStreamVertexLabelT *cataStreamlabel, DmStreamVertexLabelT *streamLabel)
{
    DbMemCtxT *memCtx = cataStreamlabel->streamMemCtx;
    DbSpinInit(&cataStreamlabel->streamMeta.sinkMeta.dispenser.lock);
    if (cataStreamlabel->streamMeta.sinkMeta.dispenser.sinkType == STREAM_SINK_TABLE) {
#ifdef FEATURE_TS
        SinkTSDBTableMetaT *state = &(cataStreamlabel->streamMeta.sinkMeta.dispenser.sinkTSDBTableMeta);
        state->base = DbDynMemCtxAlloc(memCtx, sizeof(TimePartitionInsertStateT));
        if (state->base == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, " ");
            return GMERR_OUT_OF_MEMORY;
        }
#else
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "sink into TSDB not supported");
        return GMERR_OUT_OF_MEMORY;
#endif
    }
    if (cataStreamlabel->streamMeta.sinkMeta.dispenser.sinkType == STREAM_SINK_SOCKET) {
        uint32_t size = DM_STR_LEN(streamLabel->streamMeta.sinkMeta.dispenser.sinkSocketMeta.socketAddrs);
        // socketAddrs用途: 用于存放vertexLabel的socketAddr
        // 生命周期: 活动范围伴随vertexLabel的生命周期
        // 释放方式: vertexLabel释放时一并释放
        cataStreamlabel->streamMeta.sinkMeta.dispenser.sinkSocketMeta.socketAddrs =
            (char *)DbDynMemCtxAlloc(memCtx, size);
        if (cataStreamlabel->streamMeta.sinkMeta.dispenser.sinkSocketMeta.socketAddrs == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, " ");
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memcpy_s(cataStreamlabel->streamMeta.sinkMeta.dispenser.sinkSocketMeta.socketAddrs, size,
            streamLabel->streamMeta.sinkMeta.dispenser.sinkSocketMeta.socketAddrs, size);
        Status ret = SinkServerSocketCreate(cataStreamlabel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (cataStreamlabel->streamMeta.sinkMeta.dispenser.postBufCache.timeThreshold > 0) {
        EEStreamTimerT *timer = DbDynMemCtxAlloc(memCtx, sizeof(EEStreamTimerT));
        if (timer == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, " ");
            return GMERR_OUT_OF_MEMORY;
        }

        (void)memset_s(timer, sizeof(EEStreamTimerT), 0x0, sizeof(EEStreamTimerT));
        cataStreamlabel->streamMeta.sinkMeta.dispenser.postBufCache.timer = timer;
        timer->vertexLabelId = cataStreamlabel->base.metaCommon.metaId;
    } else {
        cataStreamlabel->streamMeta.sinkMeta.dispenser.postBufCache.timer = NULL;
    }
    return GMERR_OK;
}

Status TraverseAndLink(DmStreamVertexLabelT *currentNode)
{
    if (currentNode == NULL) {
        return GMERR_OK;
    }
    // 查找当前节点的前驱链表
    TagLinkedListT *prevList = StreamGetPreList(currentNode);
    // 遍历前驱节点链表
    if (prevList == NULL) {
        return GMERR_OK;
    }
    DmStreamLinkNodeT *currTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(currTempNode, tmp, prevList, node)
    {
        DmStreamVertexLabelT *currVertexLabel = currTempNode->vertexLabel;
        // 递归遍历前驱节点链表
        Status ret = TraverseAndLink(currVertexLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (currVertexLabel->streamType == STREAM_LABEL_TYPE_SOURCE) {
            currVertexLabel->streamMeta.sourceMeta.totalPathToSink++;
        }
        TagLinkedListT *currNextList = StreamGetNextList(currVertexLabel);
        DmStreamLinkNodeT *nextTempNode = NULL;
        DmStreamLinkNodeT *tmpNext = NULL;
        LIST_FOR_EACH_ENTRY_SAFE(nextTempNode, tmpNext, currNextList, node)
        {
            if (nextTempNode->vertexLabel == currentNode) {
                // 已经被加入了，此时更新对应的计数即可
                nextTempNode->pathToSink++;
                break;
            }
        }
    }
    return GMERR_OK;
}

static Status InsertNodeIntoNextList(TagLinkedListT *preNextList, DmStreamVertexLabelT *currentNode)
{
    DmStreamVertexLabelT *preNextListVertexLabel;
    DmStreamLinkNodeT *data = DbDynMemCtxAlloc(g_parentStreamTopoCtx, sizeof(DmStreamLinkNodeT));
    if (data == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(data, sizeof(DmStreamLinkNodeT), 0x00, sizeof(DmStreamLinkNodeT));
    // 在drop表时释放，StreamRemoveCurrentNodeFromPrevNextList
    Status ret =
        CataGetVertexLabelById(NULL, currentNode->base.metaCommon.metaId, (DmVertexLabelT **)&preNextListVertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "cannot cata get vertexlabel:%s", currentNode->base.metaCommon.metaName);
        return ret;
    }
    data->vertexLabel = preNextListVertexLabel;
    if (data->vertexLabel->streamType == STREAM_LABEL_TYPE_SINK) {
        data->pathToSink = 1;
    }
    DbLinkedListAppend(preNextList, &data->node);
    return GMERR_OK;
}

uint32_t StreamDispatchKeyCompare(const void *key1, const void *key2)
{
    DB_POINTER2(key1, key2);
    const DmStreamDispatchKeyT *dispatchKey1 = (const DmStreamDispatchKeyT *)key1;
    const DmStreamDispatchKeyT *dispatchKey2 = (const DmStreamDispatchKeyT *)key2;
    for (uint32_t i = 0; i < dispatchKey1->keyNum; i++) {
        bool result = DmValueIsEqual(&dispatchKey1->value[i], &dispatchKey2->value[i]);
        if (!result) {
            return 0;
        }
    }
    return 1;
}

Status StreamDispatchRegisterRef(ExprFuncT *funcExpr, ExprContextT *exprContext, uint32_t vertexLabelId)
{
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < funcExpr->array.num; i++) {
        ExprT *expr = funcExpr->array.expr[i];
        if (expr->opType == EXPR_OP_STREAMREF) {
            ExprFuncT *subFunc = CastToFunc(expr);
            DmValueT paramRef = {0};
            ret = ExprEvalValue(subFunc->array.expr[0], exprContext, &paramRef);
            if (ret != GMERR_OK) {
                return ret;
            }
            const char *refName = (const char *)paramRef.value.constStrAddr;
            ret = StreamRegisterRefId(refName, vertexLabelId);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return ret;
}

Status InitStreamVertexLabelDispatch(
    DbMemCtxT *memCtx, DmStreamDispatchT *dstDispatch, DmStreamDispatchT *srcDispatch, uint32_t vertexLabelId)
{
    if (!srcDispatch->hasDispatch) {
        return GMERR_OK;
    }
    dstDispatch->hasDispatch = true;
    DbSpinInit(&dstDispatch->dispatchLock);
    DbLinkedListInit(&dstDispatch->nextOfAll);
    dstDispatch->keyNum = srcDispatch->keyNum;
    Status ret = DbOamapInit(&dstDispatch->dispatchMap, 0, StreamDispatchKeyCompare, memCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    // dispatchSchema用途: 用于存放vertexLabel的dispatch规则信息
    // 生命周期: 活动范围伴随vertexLabel的生命周期
    // 释放方式: vertexLabel释放时一并释放
    dstDispatch->dispatchSchema = DbDynMemCtxAlloc(memCtx, sizeof(AASchemaT));
    if (dstDispatch->dispatchSchema == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    ret = IRDeepCopyAASchema(memCtx, srcDispatch->dispatchSchema, dstDispatch->dispatchSchema);
    if (ret != GMERR_OK) {
        return ret;
    }
    PlanPropDescT planPropDesc = {.propNum = ((AASchemaT *)dstDispatch->dispatchSchema)->propAA.propNum,
        .propSchema = ((AASchemaT *)dstDispatch->dispatchSchema)->propAA.properties};
    ret = NewAASlot(memCtx, NULL, &planPropDesc, (AASlotT **)&dstDispatch->tmpDispatchSlot);
    if (ret != GMERR_OK) {
        return ret;
    }
    dstDispatch->dispatchExpr = ExprCopy(memCtx, srcDispatch->dispatchExpr);
    if (dstDispatch->dispatchExpr == NULL) {
        return GMERR_INTERNAL_ERROR;
    }
    ret = ExprStateBuildDeepCopy(srcDispatch->dispatchExpr, memCtx, (ExprStateT **)&dstDispatch->dispatchExprState);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = ExprContextBuild(memCtx, NULL, 0, (ExprContextT **)&dstDispatch->dispatchExprContext);
    if (ret != GMERR_OK) {
        return ret;
    }
    // register vertexLabelId to ref
    ExprFuncT *funcExpr = CastToFunc(dstDispatch->dispatchExpr);
    ret = StreamDispatchRegisterRef(funcExpr, (ExprContextT *)dstDispatch->dispatchExprContext, vertexLabelId);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

Status StreamDeepCopyDefaultExprList(DmStreamVertexLabelT *streamLabel, DmStreamVertexLabelT *cataStreamlabel)
{
    DbListT *sourceList = streamLabel->streamMeta.sourceMeta.defaultExpr;
    // tmpList用途: 用于存放vertexLabel的默认表达式/列名
    // 生命周期: 活动范围伴随vertexLabel的生命周期
    // 释放方式: vertexLabel释放时一并释放
    DbListT *tmpList = (DbListT *)DbDynMemCtxAlloc(cataStreamlabel->streamMemCtx, sizeof(DbListT));
    if (tmpList == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, " ");
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateListWithExtendSize(
        tmpList, sizeof(DmDefaultExprItemT), EE_STREAM_DEFAULT_EXPR_ITME_SIZE, cataStreamlabel->streamMemCtx);
    // Iterate over the list of expressions in streamLabel and deep copy each one into cataStreamlabel
    // (this is badly inefficient)
    uint32_t exprCount = sourceList->count;
    for (uint32_t i = 0; i < exprCount; i++) {
        DmDefaultExprItemT tmpDefaultExprItem = {.hasDefault = false, .expr = NULL};
        DmDefaultExprItemT *srcDefaultExprItem = (DmDefaultExprItemT *)DbListItem(sourceList, i);
        if (srcDefaultExprItem->hasDefault) {
            SqlExprT *srcExpr = srcDefaultExprItem->expr;
            if (srcExpr->op->type == SQL_EXPR_ITEM_CONST) {
                SqlExprConstT *tempExpr = (SqlExprConstT *)(srcExpr->op);
                char *data = tempExpr->arg.value.strAddr;  // Maybe NULL is better...
                if (((tempExpr->arg.type == DB_DATATYPE_FIXED) || (tempExpr->arg.type == DB_DATATYPE_STRING)) &&
                    (tempExpr->constStr != NULL)) {
                    // Wait! There's more! Alloc the space for a string and copy to the new context
                    data = DbDynMemCtxAlloc(cataStreamlabel->streamMemCtx, tempExpr->arg.value.length);
                    (void)memcpy_s(data, tempExpr->arg.value.length, tempExpr->constStr, tempExpr->arg.value.length);
                }
                tmpDefaultExprItem.hasDefault = true;
                tmpDefaultExprItem.expr = SqlExprMakeConst(cataStreamlabel->streamMemCtx, &tempExpr->arg, data);
                ((SqlExprConstT *)(((SqlExprT *)tmpDefaultExprItem.expr)->op))->arg.value.strAddr = data;
            } else if (srcExpr->op->type == SQL_EXPR_OP_FUN) {
                SqlExprFuncT *tempExpr = (SqlExprFuncT *)(srcExpr->op);
                tmpDefaultExprItem.hasDefault = true;
                tmpDefaultExprItem.expr = SqlExprMakeFunc(
                    cataStreamlabel->streamMemCtx, tempExpr->funcType, tempExpr->args, tempExpr->aggArgs);
            } else {
                DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "unsupported default expr type");
                return GMERR_FEATURE_NOT_SUPPORTED;
            }
        }
        DbAppendListItem(tmpList, &tmpDefaultExprItem);
    }

    // Assign the newly generated list
    cataStreamlabel->streamMeta.sourceMeta.defaultExpr = tmpList;
    return GMERR_OK;
}

static Status InitPropDesc(DmStreamVertexLabelT *cataStreamlabel)
{
    uint32_t propNum = cataStreamlabel->base.metaVertexLabel->schema->propeNum;
    //  内存兜底释放
    // propertySchema用途: 用于存放vertexLabel的schema
    // 生命周期: 活动范围伴随vertexLabel的生命周期
    // 释放方式: vertexLabel释放时一并释放
    DmPropertySchemaT **propertySchema =
        (DmPropertySchemaT **)DbDynMemCtxAlloc(cataStreamlabel->streamMemCtx, sizeof(DmPropertySchemaT *) * propNum);
    if (propertySchema == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    for (uint32_t i = 0; i < propNum; i++) {
        propertySchema[i] = &cataStreamlabel->base.metaVertexLabel->schema->properties[i];
    }

    cataStreamlabel->propDesc = (PlanPropDescT){
        .propNum = propNum,
        .propSchema = propertySchema,
    };
    return GMERR_OK;
}

Status InitStreamVertexLabelSinkInner(DmStreamVertexLabelT *cataStreamlabel, DmStreamVertexLabelT *streamLabel)
{
    // 必须要在这里做的原因：只有这时sink节点才被赋予了vertexlabel id 后续看能不能有别的办法
    // 执行层耦合这段逻辑有点奇怪
    Status ret = TraverseAndLink(cataStreamlabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "TraverseAndLink");
        return ret;
    }
    DmStreamSinkMetaT *sinkMeta = &cataStreamlabel->streamMeta.sinkMeta;
    sinkMeta->watermark.watermark = INT64_MIN;
    AAT *aa = NULL;
    ret = NewAAWithSize(cataStreamlabel->streamMemCtx, sinkMeta->dispenser.postBufCache.batchWindowSize, &aa);
    if (ret != GMERR_OK) {
        return ret;
    }
    sinkMeta->dispenser.postBufCache.aa = aa;
    ret = InitStreamSinkDestnation(cataStreamlabel, streamLabel);
    if (ret != GMERR_OK) {
        StreamUpdatePathToSinkWhenRemoveSinkNode(cataStreamlabel);
        return ret;
    }
    ret = InitStreamPlanAndDataSpace(cataStreamlabel, streamLabel->streamMeta.sinkMeta.streamCachedPlan.planTree,
        &sinkMeta->streamCachedPlan, &sinkMeta->streamDataSpace);
    if (ret != GMERR_OK) {
        StreamUpdatePathToSinkWhenRemoveSinkNode(cataStreamlabel);
    }
    return ret;
}

Status InitStreamVertexLabelInner(DmStreamVertexLabelT *cataStreamlabel, DmStreamVertexLabelT *streamLabel)
{
    Status ret = GMERR_OK;
    if (cataStreamlabel->streamType == STREAM_LABEL_TYPE_SOURCE) {
        cataStreamlabel->streamMeta.sourceMeta.watermarkDef = streamLabel->streamMeta.sourceMeta.watermarkDef;
        if (cataStreamlabel->streamMeta.sourceMeta.fileNum > 0) {
            ret = InsertFileEventIntoEpoll(cataStreamlabel);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        // Deep copy the default expression list
        ret = StreamDeepCopyDefaultExprList(streamLabel, cataStreamlabel);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = InitStreamVertexLabelDispatch(cataStreamlabel->streamMemCtx,
            &cataStreamlabel->streamMeta.sourceMeta.streamDispatch, &streamLabel->streamMeta.sourceMeta.streamDispatch,
            cataStreamlabel->base.metaCommon.metaId);
    } else if (cataStreamlabel->streamType == STREAM_LABEL_TYPE_SINK) {
        ret = InitStreamVertexLabelSinkInner(cataStreamlabel, streamLabel);
    } else if (cataStreamlabel->streamType == STREAM_LABEL_TYPE_VIEW) {
        cataStreamlabel->streamMeta.viewMeta.watermarkDef = streamLabel->streamMeta.viewMeta.watermarkDef;
        DmStreamViewMetaT *viewMeta = &cataStreamlabel->streamMeta.viewMeta;
        viewMeta->watermark.watermark = INT64_MIN;
        ret = InitStreamPlanAndDataSpace(cataStreamlabel, streamLabel->streamMeta.viewMeta.streamCachedPlan.planTree,
            &viewMeta->streamCachedPlan, &viewMeta->streamDataSpace);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = InitStreamVertexLabelDispatch(cataStreamlabel->streamMemCtx,
            &cataStreamlabel->streamMeta.viewMeta.streamDispatch, &streamLabel->streamMeta.viewMeta.streamDispatch,
            cataStreamlabel->base.metaCommon.metaId);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

Status CheckPreVertexLabelNextNum(DmStreamVertexLabelT *preVertexLabel)
{
    uint32_t nextNum;
    if (preVertexLabel->streamType == STREAM_LABEL_TYPE_SOURCE) {
        nextNum = preVertexLabel->streamMeta.sourceMeta.nextNum;
    } else {
        nextNum = preVertexLabel->streamMeta.viewMeta.nextNum;
    }
    if (nextNum == STREAM_NEXT_MAX) {
        DB_LOG_AND_SET_LASERR(
            GMERR_RESTRICT_VIOLATION, "Many next nodes for vertexlabel:%s", preVertexLabel->base.metaCommon.metaName);
        return GMERR_RESTRICT_VIOLATION;
    }
    return GMERR_OK;
}

bool CheckVertexLabelNotInPreList(TagLinkedListT *preNextList, DmStreamVertexLabelT *currentVertexLabel)
{
    DmStreamLinkNodeT *preNextListTempNode = NULL;
    DmStreamLinkNodeT *tmpNext = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(preNextListTempNode, tmpNext, preNextList, node)
    {
        if (preNextListTempNode->vertexLabel == currentVertexLabel) {
            return false;
        }
    }
    return true;
}

Status GetOrCreateDispatchValue(
    DmStreamLinkNodeT *preTempNode, DmStreamVertexLabelT *preVertexLabel, DmStreamDispatchValueT **value)
{
    Status ret = GMERR_OK;
    DmStreamDispatchValueT *streamDispatchValue = NULL;
    DmStreamDispatchT *streamDispatch = StreamGetDispatch(preVertexLabel);
    if (streamDispatch == NULL) {
        return GMERR_INTERNAL_ERROR;
    }
    streamDispatchValue =
        DbOamapLookup(&streamDispatch->dispatchMap, preTempNode->dispatchKey->keyHash, preTempNode->dispatchKey, NULL);
    if (streamDispatchValue == NULL) {
        if (DbOamapUsedSize(&streamDispatch->dispatchMap) == STREAM_MAX_DISPATCH_KEY_SIZE) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "too many dispatch");
            return GMERR_PROGRAM_LIMIT_EXCEEDED;
        }
        streamDispatchValue = DbDynMemCtxAlloc(g_parentStreamTopoCtx, sizeof(DmStreamDispatchValueT));
        if (streamDispatchValue == NULL) {
            return GMERR_OUT_OF_MEMORY;
        }
        DbLinkedListInit(&streamDispatchValue->nextOfDispatch);
        streamDispatchValue->dispatchKey.keyNum = preTempNode->dispatchKey->keyNum;
        streamDispatchValue->dispatchKey.keyHash = preTempNode->dispatchKey->keyHash;
        streamDispatchValue->dispatchKey.value =
            DbDynMemCtxAlloc(g_parentStreamTopoCtx, sizeof(DmValueT) * preTempNode->dispatchKey->keyNum);
        if (streamDispatchValue->dispatchKey.value == NULL) {
            DbDynMemCtxFree(g_parentStreamTopoCtx, streamDispatchValue);
            return GMERR_OUT_OF_MEMORY;
        }
        for (uint32_t i = 0; i < streamDispatchValue->dispatchKey.keyNum; i++) {
            streamDispatchValue->dispatchKey.value[i] = preTempNode->dispatchKey->value[i];
        }
        ret = DbOamapInsert(&streamDispatch->dispatchMap, streamDispatchValue->dispatchKey.keyHash,
            &streamDispatchValue->dispatchKey, streamDispatchValue, NULL);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(g_parentStreamTopoCtx, streamDispatchValue->dispatchKey.value);
            DbDynMemCtxFree(g_parentStreamTopoCtx, streamDispatchValue);
            return ret;
        }
    }
    *value = streamDispatchValue;
    return ret;
}
Status FillDispatch(DmStreamDispatchValueT *streamDispatchValue, DmStreamVertexLabelT *currentVertexLabel,
    DmStreamVertexLabelT *preVertexLabel)
{
    if (streamDispatchValue != NULL) {
        DmStreamLinkNodeT *dispatchNode = DbDynMemCtxAlloc(g_parentStreamTopoCtx, sizeof(DmStreamLinkNodeT));
        if (dispatchNode == NULL) {
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s(dispatchNode, sizeof(DmStreamLinkNodeT), 0x00, sizeof(DmStreamLinkNodeT));
        dispatchNode->dispatchKey = &streamDispatchValue->dispatchKey;
        dispatchNode->vertexLabel = currentVertexLabel;
        DbLinkedListAppend(&streamDispatchValue->nextOfDispatch, &dispatchNode->node);
    }
    if (preVertexLabel->streamType == STREAM_LABEL_TYPE_SOURCE) {
        if (preVertexLabel->streamMeta.sourceMeta.streamDispatch.hasDispatch && streamDispatchValue == NULL) {
            DmStreamLinkNodeT *dispatchNode = DbDynMemCtxAlloc(g_parentStreamTopoCtx, sizeof(DmStreamLinkNodeT));
            if (dispatchNode == NULL) {
                return GMERR_OUT_OF_MEMORY;
            }
            (void)memset_s(dispatchNode, sizeof(DmStreamLinkNodeT), 0x00, sizeof(DmStreamLinkNodeT));
            dispatchNode->vertexLabel = currentVertexLabel;
            DbLinkedListAppend(&preVertexLabel->streamMeta.sourceMeta.streamDispatch.nextOfAll, &dispatchNode->node);
        }
        preVertexLabel->streamMeta.sourceMeta.nextNum++;
    } else if (preVertexLabel->streamType == STREAM_LABEL_TYPE_VIEW) {
        if (preVertexLabel->streamMeta.viewMeta.streamDispatch.hasDispatch && streamDispatchValue == NULL) {
            DmStreamLinkNodeT *dispatchNode = DbDynMemCtxAlloc(g_parentStreamTopoCtx, sizeof(DmStreamLinkNodeT));
            if (dispatchNode == NULL) {
                return GMERR_OUT_OF_MEMORY;
            }
            (void)memset_s(dispatchNode, sizeof(DmStreamLinkNodeT), 0x00, sizeof(DmStreamLinkNodeT));
            dispatchNode->vertexLabel = currentVertexLabel;
            DbLinkedListAppend(&preVertexLabel->streamMeta.viewMeta.streamDispatch.nextOfAll, &dispatchNode->node);
        }
        preVertexLabel->streamMeta.viewMeta.nextNum++;
    }
    return GMERR_OK;
}
Status UpdateStreamPreNodeNextList(DmStreamVertexLabelT *currentVertexLabel)
{
    DB_POINTER(currentVertexLabel);
    Status ret = GMERR_OK;
    // 查找当前节点的前驱链表
    TagLinkedListT *prevList = StreamGetPreList(currentVertexLabel);
    // 遍历前驱节点链表
    if (prevList == NULL) {
        return GMERR_OK;
    }
    DmStreamLinkNodeT *preTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(preTempNode, tmp, prevList, node)
    {
        DmStreamDispatchValueT *streamDispatchValue = NULL;
        DmStreamVertexLabelT *preVertexLabel = preTempNode->vertexLabel;
        if (preTempNode->dispatchKey != NULL) {
            ret = GetOrCreateDispatchValue(preTempNode, preVertexLabel, &streamDispatchValue);
            if (ret != GMERR_OK) {
                return ret;
            }
        }

        // 任何节点的前驱都不会是SINK节点
        DB_ASSERT(preVertexLabel->streamType != STREAM_LABEL_TYPE_SINK);
        TagLinkedListT *preNextList = StreamGetNextList(preVertexLabel);

        bool notFound = CheckVertexLabelNotInPreList(preNextList, currentVertexLabel);
        if (notFound) {
            ret = CheckPreVertexLabelNextNum(preVertexLabel);
            if (ret != GMERR_OK) {
                return ret;
            }
            ret = InsertNodeIntoNextList(preNextList, currentVertexLabel);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        ret = FillDispatch(streamDispatchValue, currentVertexLabel, preVertexLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

Status InitStreamVertexLabel(DmVertexLabelT *vertexLabel)
{
    DmStreamVertexLabelT *streamLabel = (DmStreamVertexLabelT *)vertexLabel;
    DmStreamVertexLabelT *cataStreamlabel = NULL;

    Status ret = CataGetStreamVertexLabelById(vertexLabel->metaCommon.metaId, &cataStreamlabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CreateStreamLabelMemCtx(
        vertexLabel->metaCommon.metaId, vertexLabel->metaCommon.metaName, &cataStreamlabel->streamMemCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, " ");
        (void)CataReleaseVertexLabel(&cataStreamlabel->base);
        return ret;
    }

    ret = InitPropDesc(cataStreamlabel);
    if (ret != GMERR_OK) {
        DestroyStreamLabelMemCtx(cataStreamlabel);
        DB_LOG_ERROR(ret, "init prop desc");
        goto EXIT;
    }

    // sink or view add myself to prev's next list.
    ret = InitStreamVertexLabelInner(cataStreamlabel, streamLabel);
    if (ret != GMERR_OK) {
        DestroyStreamLabelMemCtx(cataStreamlabel);
        DB_LOG_ERROR(ret, "init plan and dataspace");
        goto EXIT;
    }
    // 将vertexLabelId添加到索引的ref表中
    ret = StreamRegistVertexLabelId(cataStreamlabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "register ref count");
        DestroyStreamLabelMemCtx(cataStreamlabel);
        goto EXIT;
    }
    ret = UpdateStreamPreNodeNextList(cataStreamlabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "UpdateStreamPreNodeNextList");
        StreamRemoveCurrentNodeFromPrevNextList(cataStreamlabel);
        DestroyStreamLabelMemCtx(cataStreamlabel);
    }
EXIT:
    (void)CataReleaseVertexLabel(&cataStreamlabel->base);
    return ret;
}
Status CmdDropStreamVertexLabelCheck(const DmVertexLabelT *vertexLabel)
{
    if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_STREAM) {
        const DmStreamVertexLabelT *streamLabel = (const DmStreamVertexLabelT *)vertexLabel;
        if ((streamLabel->streamType == STREAM_LABEL_TYPE_SOURCE &&
                !DbLinkedListEmpty(&streamLabel->streamMeta.sourceMeta.next)) ||
            (streamLabel->streamType == STREAM_LABEL_TYPE_VIEW &&
                !DbLinkedListEmpty(&streamLabel->streamMeta.viewMeta.next))) {
            DB_LOG_AND_SET_LASERR(GMERR_RESTRICT_VIOLATION, "stream node has next list");
            return GMERR_RESTRICT_VIOLATION;
        }
    }
    return GMERR_OK;
}

DmStreamLinkNodeT *CheckVertexInList(DmStreamVertexLabelT *streamVertex, TagLinkedListT *list)
{
    if (list == NULL) {
        return NULL;
    }
    DmStreamLinkNodeT *tmp = NULL;
    DmStreamLinkNodeT *nxtTempNode = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(nxtTempNode, tmp, list, node)
    {
        if (DbStrCmp(streamVertex->base.metaCommon.metaName, nxtTempNode->vertexLabel->base.metaCommon.metaName,
                true) == 0) {
            return nxtTempNode;
        }
    }
    return NULL;
}

Status StreamUnionAddVertex(DmVertexLabelT *vertex, DmVertexLabelT *unionVertex, DmStreamDispatchKeyT *dispatchKey,
    DmStreamLinkNodeT **preNode, DmStreamLinkNodeT **nxtNode)
{
    DmStreamVertexLabelT *streamVertex = (DmStreamVertexLabelT *)(vertex);
    TagLinkedListT *list = StreamGetPreList(streamVertex);
    DmStreamVertexLabelT *streamUnionVertex = (DmStreamVertexLabelT *)(unionVertex);
    DmStreamLinkNodeT *preTmpNode = DbDynMemCtxAlloc(g_parentStreamTopoCtx, sizeof(DmStreamLinkNodeT));
    if (preTmpNode == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "unable to alloc preNode");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(preTmpNode, sizeof(DmStreamLinkNodeT), 0x00, sizeof(DmStreamLinkNodeT));
    preTmpNode->vertexLabel = streamUnionVertex;
    DbLinkedListAppend(list, &preTmpNode->node);

    list = StreamGetNextList(streamUnionVertex);
    DmStreamLinkNodeT *nxtTmpNode = CheckVertexInList(streamVertex, list);
    if (nxtTmpNode == NULL) {
        nxtTmpNode = DbDynMemCtxAlloc(g_parentStreamTopoCtx, sizeof(DmStreamLinkNodeT));
        if (nxtTmpNode == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "unable to alloc nxtNode");
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s(nxtTmpNode, sizeof(DmStreamLinkNodeT), 0x00, sizeof(DmStreamLinkNodeT));
        nxtTmpNode->vertexLabel = streamVertex;
        DbLinkedListAppend(list, &nxtTmpNode->node);
    }

    if (dispatchKey != NULL) {
        preTmpNode->dispatchKey = dispatchKey;
    }
    uint32_t pathsNum = getPathToSink(streamVertex);
    nxtTmpNode->pathToSink = pathsNum;

    uint32_t pathsUnionNum = getPathToSink(streamUnionVertex);
    preTmpNode->pathToSink = pathsUnionNum;
    UpdatePathToSink(streamUnionVertex, (int32_t)pathsNum);
    if (streamUnionVertex->streamType == STREAM_LABEL_TYPE_SOURCE) {
        streamUnionVertex->streamMeta.sourceMeta.nextNum++;
    }
    if (streamUnionVertex->streamType == STREAM_LABEL_TYPE_VIEW) {
        streamUnionVertex->streamMeta.viewMeta.nextNum++;
    }
    *preNode = preTmpNode;
    *nxtNode = nxtTmpNode;
    return GMERR_OK;
}

Status StreamUnionAddDispatchKeyVal(
    DmVertexLabelT *vertex, DmVertexLabelT *unionVertex, DmStreamDispatchKeyT *dispatchKey, DmStreamLinkNodeT *preNode)
{
    DmStreamVertexLabelT *streamVertex = (DmStreamVertexLabelT *)(vertex);

    DmStreamDispatchValueT *dispatchValue = NULL;
    Status ret = GetOrCreateDispatchValue(preNode, preNode->vertexLabel, &dispatchValue);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "unable to get dispatchValue");
        return ret;
    }
    ret = FillDispatch(dispatchValue, streamVertex, preNode->vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "unable to get dispatchValue");
    }
    return ret;
}

Status StreamUnionAddDispatchVertex(
    StreamAlterUnionDispatchStmtT *alterUnionStmt, DmVertexLabelT *vertex, DmVertexLabelT *unionVertex)
{
    DmStreamLinkNodeT *preNode = NULL;
    DmStreamLinkNodeT *nxtNode = NULL;
    Status ret = StreamUnionAddVertex(vertex, unionVertex, alterUnionStmt->dispatchKey, &preNode, &nxtNode);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Cannot do StreamUnionAddVertex");
        return ret;
    }
    ret = StreamUnionAddDispatchKeyVal(vertex, unionVertex, alterUnionStmt->dispatchKey, preNode);
    if (ret != GMERR_OK) {
        DbLinkedListRemove(&preNode->node);
        DbDynMemCtxFree(g_parentStreamTopoCtx, preNode);
        return ret;
    }
    return ret;
}

Status StreamFindPreAndNxtNode(StreamAlterUnionDispatchStmtT *alterUnionStmt, DmVertexLabelT *vertex,
    DmVertexLabelT *unionVertex, DmStreamLinkNodeT **preNode, DmStreamLinkNodeT **nxtNode)
{
    DmStreamVertexLabelT *streamVertex = (DmStreamVertexLabelT *)(vertex);
    DmStreamVertexLabelT *unionStream = (DmStreamVertexLabelT *)(unionVertex);
    TagLinkedListT *preList = StreamGetPreList(streamVertex);
    DmStreamLinkNodeT *preTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    bool notPre = true;
    LIST_FOR_EACH_ENTRY_SAFE(preTempNode, tmp, preList, node)
    {
        DmStreamVertexLabelT *preVertexLabel = preTempNode->vertexLabel;
        if (DbStrCmp(unionVertex->metaCommon.metaName, preVertexLabel->base.metaCommon.metaName, true) == 0) {
            if (preTempNode->dispatchKey != NULL) {
                if (preTempNode->dispatchKey->keyHash == alterUnionStmt->dispatchKey->keyHash) {
                    *preNode = preTempNode;
                    notPre = false;
                    break;
                }
            }
        }
    }
    if (notPre) {
        DB_LOG_ERROR(GMERR_DATATYPE_MISMATCH, "Cannot find preNode");
        return GMERR_DATATYPE_MISMATCH;
    }

    bool notNxt = true;
    TagLinkedListT *nxtList = StreamGetNextList(unionStream);
    DmStreamLinkNodeT *nxtTmpNode = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(nxtTmpNode, tmp, nxtList, node)
    {
        DmStreamVertexLabelT *nxtVertex = nxtTmpNode->vertexLabel;
        if (DbStrCmp(vertex->metaCommon.metaName, nxtVertex->base.metaCommon.metaName, true) == 0) {
            notNxt = false;
            *nxtNode = nxtTmpNode;
            break;
        }
    }
    if (notNxt) {
        DB_LOG_ERROR(GMERR_DATATYPE_MISMATCH, "Cannot find nxtNode");
        return GMERR_DATATYPE_MISMATCH;
    }
    return GMERR_OK;
}

Status StreamUnionDropDispatchVertex(
    StreamAlterUnionDispatchStmtT *alterUnionStmt, DmVertexLabelT *vertex, DmVertexLabelT *unionVertex)
{
    DmStreamLinkNodeT *preNode = NULL;
    DmStreamLinkNodeT *nxtNode = NULL;
    DmStreamVertexLabelT *unionStream = (DmStreamVertexLabelT *)(unionVertex);
    DmStreamVertexLabelT *streamVertex = (DmStreamVertexLabelT *)(vertex);
    Status ret = StreamFindPreAndNxtNode(alterUnionStmt, vertex, unionVertex, &preNode, &nxtNode);
    if (ret != GMERR_OK) {
        return ret;
    }
    DropDispatchRelation(preNode, nxtNode);
    DbLinkedListRemove(&preNode->node);
    DbDynMemCtxFree(g_parentStreamTopoCtx, preNode);
    if (CheckVertexInList(unionStream, StreamGetPreList(streamVertex)) == NULL) {
        DbLinkedListRemove(&nxtNode->node);
        DbDynMemCtxFree(g_parentStreamTopoCtx, nxtNode);
    }
    return ret;
}

Status CmdAlterUnionDispatchStream(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    DB_POINTER3(cstate, cmd, cmdResult);
    Status ret = GMERR_OK;
    StreamAlterUnionDispatchStmtT *alterUnionDispatchStmt = (StreamAlterUnionDispatchStmtT *)cmd;
    DmVertexLabelT *vertex = alterUnionDispatchStmt->vertexLabel;
    DmVertexLabelT *unionVertex = alterUnionDispatchStmt->unionVertex;
    switch (alterUnionDispatchStmt->alterType) {
        case STREAM_ALTER_ADD:
            ret = StreamUnionAddDispatchVertex(alterUnionDispatchStmt, vertex, unionVertex);
            break;
        case STREAM_ALTER_DROP:
            ret = StreamUnionDropDispatchVertex(alterUnionDispatchStmt, vertex, unionVertex);
            break;
    }
    return ret;
}

Status CmdAlterUnionStream(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    DB_POINTER3(cstate, cmd, cmdResult);
    Status ret = GMERR_OK;
    StreamAlterUnionStmtT *alterUnionStmt = (StreamAlterUnionStmtT *)cmd;
    DmVertexLabelT *vertex = alterUnionStmt->vertexLabel;
    DmVertexLabelT *unionVertex = alterUnionStmt->unionVertex;
    DmStreamLinkNodeT *preNode = NULL;
    DmStreamLinkNodeT *nxtNode = NULL;
    switch (alterUnionStmt->alterType) {
        case STREAM_ALTER_ADD:
            ret = StreamUnionAddVertex(vertex, unionVertex, NULL, &preNode, &nxtNode);
            break;
        case STREAM_ALTER_DROP:
            ret = StreamUnionDropVertex(alterUnionStmt, vertex, unionVertex);
            break;
    }
    return ret;
}
