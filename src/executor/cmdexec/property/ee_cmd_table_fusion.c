/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Commands for creating and altering table structures and settings.
 * Author: GMDBv5 EE Team
 * Create: 2022-7-17
 */

#include "drt_instance.h"
#include "db_label_latch_mgr.h"
#include "db_timer.h"
#include "ee_systbl.h"
#include "dm_meta_key_oper.h"
#include "dm_meta_sql_prop_label.h"
#include "dm_meta_obj_priv.h"
#include "ee_cmd_router_fusion.h"
#include "ee_cmd.h"
#include "ee_session.h"
#include "db_label_latch_mgr.h"
#include "db_timer.h"
#include "dm_meta_basic_in.h"
#include "drt_instance.h"
#include "ee_plan_state.h"
#include "ee_ddl_vertex_label.h"
#include "ee_merge_write_cache.h"
#include "ee_concurrency_control.h"
#include "ee_extern_table.h"

#ifdef FEATURE_SQL
#include "dm_data_trigger_info_sql.h"
#endif

#ifdef FEATURE_TS
#include "cpl_ts_analyzer_common.h"
#include "srv_data_ts.h"
#include "ee_time_partition_util.h"
#include "dm_data_ts.h"
#include "se_persist.h"
#include "ee_background_schedule.h"
#define SQL_COL_NUM_MAX 256  // max number of column
#endif

#ifdef FEATURE_STREAM
#include "ee_cmd_table_stream.h"
#endif
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef FEATURE_TS
// 时序获取锁超时时间
static uint32_t QrySessionGetTsLockTimeout(SessionT *session)
{
    DB_POINTER(session);
    TsQryStmtT *stmt = (TsQryStmtT *)session->sqlCtx->currentFusionStmt;
    return stmt->lockTimeoutUs;
}
extern DbMemCtxT *g_parentViewLabelListCtx;

Status CmdGrantRevokePrivInner(CStateT *cstate, ObjPrivOpParaT objPrivPara, CataUserNameInfoT *userNameInfo,
    const CataObjPrivT *objPrivilege, bool isGrant)
{
    DmVertexLabelT *vertexLabel = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, cstate->session->namespaceId, objPrivilege->objName);
    Status ret = CataGetVertexLabelByName(DbGetInstanceByMemCtx(cstate->memCtx), &cataKey, &vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get vertexLabel(name: %s, nsp: %s) when cmd grant revoke objPriv.", objPrivilege->objName,
            objPrivilege->namespaceName);
        return ret;
    }

    CataPrivRolesT cataPrivRoles = {0};
    cataPrivRoles.memCtx = cstate->memCtx;
    if (isGrant) {
        ret = CataGrantObjPrivByType(
            objPrivPara, userNameInfo->userName, userNameInfo->processName, objPrivilege, &cataPrivRoles);
    } else {
        ret = CataRevokeObjPrivByType(
            objPrivPara, userNameInfo->userName, userNameInfo->processName, objPrivilege, &cataPrivRoles);
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "change privileges, op=%d, user_name=%s, process_name=%s, table_name=%s, priv=%d", isGrant,
            userNameInfo->userName, userNameInfo->processName, objPrivilege->objName, objPrivilege->privileges);
        goto EXIT;
    }

    DmVertexLabelTypeE vlType = vertexLabel->metaVertexLabel->vertexLabelType;
    if (vlType != VERTEX_TYPE_SYSTABLE && vertexLabel->metaCommon.isPersistent) {
        ret = SysTableUpdateOneVertexLabel(cstate->session, vertexLabel);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
        for (uint32_t i = 0; i < cataPrivRoles.roleNum; i++) {
            if (cataPrivRoles.roles[i]->isUser && !cataPrivRoles.roles[i]->isDBA) {
                ret = SysTableUpdateOneRole(cstate->session, cataPrivRoles.roles[i]);
                if (ret != GMERR_OK) {
                    goto EXIT;
                }
            }
        }
    }

EXIT:
    CataReleasePrivRoles(&cataPrivRoles);
    (void)CataReleaseVertexLabel(vertexLabel);
    return ret;
}

// 时序场景下表的创建者默认具有对象权限
Status CmdGrantPrivsForCreator(CStateT *cstate, DmVertexLabelT *vertexLabel)
{
    DB_POINTER(cstate);
    if (vertexLabel == NULL) {
        return GMERR_OK;
    }
    // DBA user no need obj priv
    SessionT *session = cstate->session;
    if (session->isDBA) {
        return GMERR_OK;
    }
    // no need create priv when policy mode is 0
    int32_t policyMode = DbCfgGetInt32Lite(DB_CFG_USER_POLICY_MODE, NULL);
    if (policyMode == 0) {
        return GMERR_OK;
    }
    CataObjPrivT objPriv = {0};
    objPriv.objType = CATA_VERTEX_LABEL;
    objPriv.privileges = DM_OBJ_SELECT_PRIV | DM_OBJ_INSERT_PRIV | DM_OBJ_UPDATE_PRIV | DM_OBJ_DELETE_PRIV;
    objPriv.objName = vertexLabel->metaCommon.metaName;
    Status ret = CataGetNamespaceNameById(cstate->memCtx, &objPriv.namespaceName, cstate->session->namespaceId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get curr nsp");
        return ret;
    }
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(session->memCtx);
    ObjPrivOpParaT objPrivOpPara = {0};
    objPrivOpPara.grantToUser = true;
    objPrivOpPara.grantorRoleId = cstate->session->roleId;
    objPrivOpPara.isAtomic = true;
    objPrivOpPara.dbInstance = dbInstance;
    CataUserNameInfoT userNameInfo = {0};
    userNameInfo.userName = session->externalUser.dbUserName;
    userNameInfo.processName = session->externalUser.dbProcessName;
    ret = CmdGrantRevokePrivInner(cstate, objPrivOpPara, &userNameInfo, &objPriv, true);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(cstate->memCtx, objPriv.namespaceName);
        DB_LOG_ERROR(ret, "grant privs for table creator");
        return ret;
    }
    DbDynMemCtxFree(cstate->memCtx, objPriv.namespaceName);
    return ret;
}
#endif

#ifdef FEATURE_TS
static Status ProcessTimeSeriesTable(CStateT *cstate, CreateTableStmtT *cmdReq, DmVertexLabelT *copyVertexLabel)
{
    Status ret = GMERR_OK;
    if (SeGetPersistMode() != PERSIST_OFF && DmVertexLabelIsTsLabel(cmdReq->vertexLabel)) {
        ret = SysTableTsMapInsertLogicalLabel(cstate->session, cmdReq->vertexLabel->metaCommon.metaId,
            cmdReq->vertexLabel->metaVertexLabel->extraInfo.data);
        if (ret != GMERR_OK) {
            (void)SeSpaceDrop(cstate->session->seInstance,
                ((DmTsInfoT *)(cmdReq->vertexLabel->metaVertexLabel->extraInfo.data))->persistentSpaceId);
            QryRollbackCreateVertexLabel(cstate->seInstance, cmdReq->vertexLabel);
            (void)CmdDropVertexLabelFromCatalog(cmdReq->vertexLabel);
            return ret;
        }
        ret = LogicalLabelPrepareDataCache(copyVertexLabel);
        if (ret != GMERR_OK) {
            QryRollbackCreateVertexLabel(cstate->seInstance, cmdReq->vertexLabel);
            (void)CmdDropVertexLabelFromCatalog(cmdReq->vertexLabel);
            return ret;
        }
    }
    (void)CmdGrantPrivsForCreator(cstate, cmdReq->vertexLabel);
    return GMERR_OK;
}
#endif

Status CmdCreateTable(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    DB_POINTER3(cstate, cmd, cmdResult);
    Status ret;
    CreateTableStmtT *cmdReq = (void *)cmd;
    CreateTableResultStmtT *result = NULL;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(cstate->memCtx);
    DmVertexLabelT *copyVertexLabel = NULL;

    if ((ret = NewCreateTableResultStmt(cstate->memCtx, cmdReq->node.tag, &result)) != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "create new CreateTableResultStmt.");
        return ret;
    }

    if (cmdReq->vertexLabel->metaCommon.tablespaceId == 0) {
        ret = CataGetTspIdByName(PUBLIC_TABLESPACE_NAME, &cmdReq->vertexLabel->metaCommon.tablespaceId, dbInstance);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "get table space id by name %s.", PUBLIC_TABLESPACE_NAME);
            return ret;
        }
    }

    ret = CreateVertexLabelInSystableBefore(cstate->session, cmdReq->vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    if ((ret = CmdCreateVertexLabel(cstate->seInstance, cmdReq->vertexLabel, &copyVertexLabel)) != GMERR_OK) {
        return ret;
    }
    ret = CreateVertexLabelInSystable(cstate->session, cmdReq->vertexLabel, copyVertexLabel);
    if (ret != GMERR_OK) {
        QryRollbackCreateVertexLabel(cstate->seInstance, cmdReq->vertexLabel);
        (void)CmdDropVertexLabelFromCatalog(cmdReq->vertexLabel);
        return ret;
    }
#ifdef FEATURE_TS
    ret = ProcessTimeSeriesTable(cstate, cmdReq, copyVertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif
    result->savedVertexLabel = copyVertexLabel;
    *cmdResult = (NodeT *)(void *)result;
    return GMERR_OK;
}

#ifdef FEATURE_SQL
static bool TableIsLocked(SessionT *session, DmVertexLabelT *vertexLabel)
{
    if (session == NULL) {
        return false;
    }
    QryTrxStatisticT *trxStatistics = &session->trxStatistics;
    if (trxStatistics->usedNum == 0) {
        return false;
    }
    QryLatchDefT *latchDef;
    for (uint32_t j = 0; j < trxStatistics->usedNum; j++) {
        latchDef = &trxStatistics->labelLatchArr[j];
        if (vertexLabel->commonInfo->vertexLabelLatchId == latchDef->latchId) {
            return true;
        }
    }

    GaListT *labelLatch = &trxStatistics->labelLatch;
    for (uint32_t i = 0; i < DbGaListGetCount(labelLatch); i++) {
        latchDef = (QryLatchDefT *)DbGaListGet(labelLatch, i);
        if (latchDef != NULL && vertexLabel->commonInfo->vertexLabelLatchId == latchDef->latchId) {
            return true;
        }
    }
    return false;
}

static Status CmdAlterVertexLabelAddColumn(DmVertexLabelT *vertexLabel, DmPropertySchemaT *properties)
{
    DB_POINTER2(vertexLabel, properties);
    Status ret = DmMetaDataAddProperty(vertexLabel, properties);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "add column.");
        return ret;
    }
    uint32_t vertexLabelLen =
        (vertexLabel->metaVertexLabel->labelLevel == VERTEX_LEVEL_SIMPLE) ? DmGetFixVertexLabelLen(vertexLabel) : 0u;
    // 当前heap size只能增加不能减少，故需最后处理heap，聚簇容器等同于变长表，不对row size进行变更
    HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = vertexLabel->commonInfo->heapInfo.heapShmAddr,
        .isPersistent = true,
        .isUseRsm = true,
        .instanceId = DbGetInstanceId(DbGetInstanceByMemCtx(vertexLabel->memCtx))};
    if (vertexLabel->metaVertexLabel->containerType == CONTAINER_HEAP &&
        vertexLabel->metaVertexLabel->labelLevel == VERTEX_LEVEL_SIMPLE) {
        ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo, (uint16_t)vertexLabelLen);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                ret, "upgrade heap fix row size with fixRowSize %" PRIu32 " for add column.", vertexLabelLen);
        }
    }
    return ret;
}

static Status CmdAlterVertexLabelDropColumn(DmVertexLabelT *vertexLabel, const char *propeName)
{
    DB_POINTER2(vertexLabel, propeName);
    Status ret = DmMetaDataDropProperty(vertexLabel, propeName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "drop colume %s.", propeName);
        return ret;
    }
    uint32_t vertexLabelLen =
        (vertexLabel->metaVertexLabel->labelLevel == VERTEX_LEVEL_SIMPLE) ? DmGetFixVertexLabelLen(vertexLabel) : 0u;
    HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = vertexLabel->commonInfo->heapInfo.heapShmAddr,
        .isPersistent = true,
        .isUseRsm = true,
        .instanceId = DbGetInstanceId(DbGetInstanceByMemCtx(vertexLabel->memCtx))};
    if (vertexLabel->metaVertexLabel->containerType == CONTAINER_HEAP &&
        vertexLabel->metaVertexLabel->labelLevel == VERTEX_LEVEL_SIMPLE) {
        ret = HeapLabelUpgradeFixRowSize(&heapCntrAcsInfo, (uint16_t)vertexLabelLen);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                ret, "upgrade heap fix row size with fixRowSize %" PRIu32 " for drop column.", vertexLabelLen);
        }
    }
    return ret;
}

static Status CmdAlterTableByType(AlterTableStmtT *cmdReq, DmVertexLabelT *vertexLabel)
{
    Status ret = GMERR_OK;
    switch (cmdReq->type) {
        case T_RENAME_TABLE:
            ret = DmMetaDataRenameVertexLabel(vertexLabel, cmdReq->tableName, cmdReq->target);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR_AND_SET_LASTERR(ret, "rename table name.");
            }
            break;
        case T_RENAME_COLUMN:
            ret = DmMetaDataRenameProperty(vertexLabel, cmdReq->source, cmdReq->target);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR_AND_SET_LASTERR(ret, "rename column name.");
            }
            break;
        case T_ADD_COLUMN:
            ret = CmdAlterVertexLabelAddColumn(vertexLabel, cmdReq->column);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR_AND_SET_LASTERR(ret, "add column when altering vertexLabel.");
            }
            break;
        case T_DDL_DROP_COLUMN:
            ret = CmdAlterVertexLabelDropColumn(vertexLabel, cmdReq->target);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR_AND_SET_LASTERR(ret, "drop column when altering vertexLabel.");
            }
            break;
        default:
            ret = GMERR_FEATURE_NOT_SUPPORTED;
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "curr optype:%" PRIu32, (uint32_t)cmdReq->type);
            break;
    }
    return ret;
}

#endif

#ifdef FEATURE_TS
static Status AlterVertexLabelPreCheck(DmVertexLabelT *originVertexLabel)
{
    DB_POINTER(originVertexLabel);
    // 只操作非datalog表
    if (DmVertexLabelIsDatalogLabel(originVertexLabel)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Unsupported to alter datalog VertexLabel: %s.",
            originVertexLabel->metaCommon.metaName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    // 不操作系统表
    if (originVertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_SYSTABLE) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Not supported alter sysTable, label name: %s.",
            originVertexLabel->metaCommon.metaName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    // 视图不能alter
    if (originVertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_SYSVIEW) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Label %s can't be altered because it is a view",
            originVertexLabel->metaCommon.metaName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    // 拥有缓存空间的表不支持alter
    DmTsInfoT *info = (DmTsInfoT *)originVertexLabel->metaVertexLabel->extraInfo.data;
    if (info->cacheCapacity != 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Label %s alter table when cache is set",
            originVertexLabel->metaCommon.metaName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

static Status InitNewProperties(DmVertexLabelT *vertexLabel, DmPropertySchemaT *properties, uint32_t addNum)
{
    uint32_t newPropNum = vertexLabel->metaVertexLabel->schema->propeNum + addNum;
    size_t propArraySize = newPropNum * sizeof(DmPropertySchemaT);
    // 此处为动态内存
    DmPropertySchemaT *newProperties = (DmPropertySchemaT *)DbDynMemCtxAlloc(vertexLabel->memCtx, propArraySize);
    if (newProperties == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY,
            "alloc memory for adding column. Vertex label name:%s, id:%" PRIu32, vertexLabel->metaCommon.metaName,
            vertexLabel->metaCommon.metaId);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(newProperties, propArraySize, 0, propArraySize);
    for (uint32_t i = 0; i < vertexLabel->metaVertexLabel->schema->propeNum; i++) {
        newProperties[i] = vertexLabel->metaVertexLabel->schema->properties[i];
    }
    for (uint32_t i = vertexLabel->metaVertexLabel->schema->propeNum; i < newPropNum; i++) {
        newProperties[i] = properties[i - vertexLabel->metaVertexLabel->schema->propeNum];
        newProperties[i].propeId = i;
    }
    DbDynMemCtxFree(vertexLabel->memCtx, vertexLabel->metaVertexLabel->schema->properties);
    vertexLabel->metaVertexLabel->schema->propeNum = newPropNum;
    vertexLabel->metaVertexLabel->schema->properties = newProperties;
    return GMERR_OK;
}

static Status TsAddPropertyInNewVertexLabel(DmVertexLabelT *newVtl, DmPropertySchemaT *properties, uint32_t addNum)
{
    DB_POINTER2(newVtl, properties);
    Status ret = GMERR_OK;
    uint32_t newPropNum = newVtl->metaVertexLabel->schema->propeNum + addNum;
    if (newPropNum > DM_MAX_FIELD_PROPE_NUM || newPropNum > SQL_COL_NUM_MAX) {
        ret = GMERR_INVALID_COLUMN_DEFINITION;
        DB_LOG_ERROR_AND_SET_LASTERR(ret,
            "too much fields properties. Table name:%s, id:%" PRIu32 ", new prop num:%" PRIu32,
            newVtl->metaCommon.metaName, newVtl->metaCommon.metaId, addNum);
        return ret;
    }
    // 检查新列名是否存在
    for (uint32_t i = 0; i < addNum; i++) {
        ret = CheckColumnExists(properties[i].name, newVtl->metaVertexLabel->schema, NULL);
        if (ret != GMERR_UNDEFINE_COLUMN) {
            ret = GMERR_DUPLICATE_COLUMN;
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "column already exists. Table name:%s, id:%" PRIu32,
                newVtl->metaCommon.metaName, newVtl->metaCommon.metaId);
            return ret;
        }
    }
    ret = InitNewProperties(newVtl, properties, addNum);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Init new properties with problem. Table name:%s, id:%" PRIu32, newVtl->metaCommon.metaName,
            newVtl->metaCommon.metaId);
        return ret;
    }
    return GMERR_OK;
}

static Status GetLabelAndGenerateNewLabel(
    DbMemCtxT *memCtx, AlterTableStmtT *cmdReq, DmVertexLabelT **oriVertexLabel, DmVertexLabelT **newVertexLabel)
{
    if (cmdReq->type == T_SET_FEATURE) {  // 新vertexLabel在compiler构造
        *newVertexLabel = cmdReq->vertexLabel;
        *oriVertexLabel = cmdReq->oldVertexLabel;
        return GMERR_OK;
    }
    Status ret = DmCreateEmptyVertexLabelWithMemCtx(memCtx, newVertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create new vertexLabel for alter table.");
        return ret;
    }
    // analyzer中vertexLabel被释放，需要再次get
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, cmdReq->oldVertexLabel->metaCommon.dbId,
        cmdReq->oldVertexLabel->metaCommon.namespaceId, cmdReq->oldVertexLabel->metaCommon.metaName);
    if ((ret = CataGetVertexLabelByName(DbGetInstanceByMemCtx(memCtx), &cataKey, oriVertexLabel)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "get vertexLabel by name, name = %s", cmdReq->oldVertexLabel->metaCommon.metaName);
        return ret;
    }
    ret = DmCopyVertexLabel(*oriVertexLabel, *newVertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "copy vertexLabel when altering table.");
        goto EXIT;
    }
    ret = TsAddPropertyInNewVertexLabel(*newVertexLabel, cmdReq->column, 1);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    return ret;
EXIT:
    if (*oriVertexLabel != NULL) {
        CataReleaseVertexLabel(*oriVertexLabel);
        *oriVertexLabel = NULL;
    }
    if (*newVertexLabel != NULL) {
        CataReleaseVertexLabel(*newVertexLabel);
        *newVertexLabel = NULL;
    }
    return ret;
}

Status CmdUpdateSysTblForAlter(SessionT *session, DmVertexLabelT *oriVertexLabel)
{
    uint32_t vlId = oriVertexLabel->metaCommon.metaId;
    Status ret = CataRemoveVertexLabelByIdAndVersion(DbGetInstanceByMemCtx(session->memCtx), vlId, 0);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SysTableDeleteOneVertexLabel(session, oriVertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmVertexLabelT *vertexLabel = NULL;
    ret = CataGetVertexLabelById(DbGetInstanceByMemCtx(session->memCtx), vlId, &vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SysTableInsertOneVertexLabel(session, vertexLabel);
    (void)CataReleaseVertexLabel(vertexLabel);
    return ret;
}

// 仅时序场景使用
Status CmdAlterTable(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    DB_POINTER3(cstate, cmd, cmdResult);
    Status ret = GMERR_OK;
    AlterTableStmtT *cmdReq = (void *)cmd;
    AlterTableResultStmtT *result = NULL;

    if ((ret = NewAlterTableResultStmt(cstate->memCtx, &result, T_ALTER_TABLE_STMT_SQL)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "create new CreateTableResultStmt.");
        return ret;
    }
    DmVertexLabelT *oriVertexLabel = NULL;
    DmVertexLabelT *newVertexLabel = NULL;
    if ((ret = GetLabelAndGenerateNewLabel(cstate->memCtx, cmdReq, &oriVertexLabel, &newVertexLabel)) != GMERR_OK) {
        return ret;
    }
    if ((ret = AlterVertexLabelPreCheck(oriVertexLabel)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "precheck when alter table; labelName = %s", oriVertexLabel->metaCommon.metaName);
        goto EXIT;
    }
    // 存入 Catalog 之前先分配内部的系统表 id
    ret = SysTableAssignVlInnerStbIds(cstate->session, newVertexLabel);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    // 目前实现逻辑不涉及表升降级，仅实现新旧vertexLabel替换
    ret = CataAlterVertexLabel(newVertexLabel);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = CmdUpdateSysTblForAlter(cstate->session, oriVertexLabel);
    if (ret != GMERR_OK) {
        goto EXIT_ROLLBACK;
    }

    *cmdResult = (NodeT *)(void *)result;
    goto EXIT;

EXIT_ROLLBACK:
    CataAlterVertexLabel(cmdReq->oldVertexLabel);
    CataRemoveVertexLabelByIdAndVersion(DbGetInstanceByMemCtx(cstate->memCtx), newVertexLabel->metaCommon.metaId, 0);
EXIT:
    if (cmdReq->type != T_SET_FEATURE && oriVertexLabel != NULL) {  // set 的oriVertexLabel在analyzer获取
        (void)CataReleaseVertexLabel(oriVertexLabel);
    }
    return ret;
}

Status CmdGrantRevokePriv(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    DB_POINTER3(cstate, cmd, cmdResult);
    Status ret = GMERR_OK;
    GrantRevokeStmtT *grantStmt = (GrantRevokeStmtT *)cmd;
    CataObjPrivT objPriv = {0};
    objPriv.objType = CATA_VERTEX_LABEL;
    objPriv.privileges = (uint16_t)grantStmt->privileges;
    objPriv.objName = grantStmt->vertexLabel->metaCommon.metaName;
    ret = CataGetNamespaceNameById(cstate->memCtx, &objPriv.namespaceName, cstate->session->namespaceId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get nsp name, id=%u", cstate->session->namespaceId);
        return ret;
    }
    ObjPrivOpParaT objPrivOpPara = {0};
    objPrivOpPara.grantToUser = true;
    objPrivOpPara.grantorRoleId = cstate->session->roleId;
    if (grantStmt->isGrant && grantStmt->userList != NULL) {
        objPrivOpPara.isAtomic = true;
    }
    objPrivOpPara.dbInstance = DbGetInstanceByMemCtx(cstate->session->memCtx);
    CataUserNameInfoT userNameInfo = {0};
    if (grantStmt->userList == NULL) {
        // userList null means public
        userNameInfo.userName = "*";
        userNameInfo.processName = "*";
        ret = CmdGrantRevokePrivInner(cstate, objPrivOpPara, &userNameInfo, &objPriv, grantStmt->isGrant);
        DbDynMemCtxFree(cstate->memCtx, objPriv.namespaceName);
        return ret;
    }
    uint32_t userCnt = DbListGetItemCnt(grantStmt->userList);
    for (uint32_t i = 0; i < userCnt; i++) {
        GrantUserProcessPairT *userProcess = DbListItem(grantStmt->userList, i);
        userNameInfo.userName = userProcess->userName;
        userNameInfo.processName = userProcess->processName;
        ret = CmdGrantRevokePrivInner(cstate, objPrivOpPara, &userNameInfo, &objPriv, grantStmt->isGrant);
        if (ret != GMERR_OK) {
            if (ret == GMERR_UNDEFINED_OBJECT) {
                DB_LOG_WARN(ret, "specified user process not exist, user=%s, process=%s", userProcess->userName,
                    userProcess->processName);
            } else {
                // 内部有详细日志
                DbDynMemCtxFree(cstate->memCtx, objPriv.namespaceName);
                return ret;
            }
        }
    }
    // 指定多个用户时有某个用户不存在，不中断操作，与V5批量导入保持一致
    if (ret == GMERR_UNDEFINED_OBJECT && userCnt > 1) {
        ret = GMERR_OK;
    }
    DbDynMemCtxFree(cstate->memCtx, objPriv.namespaceName);
    return ret;
}

#endif

#ifdef FEATURE_SQL

static void ExecResetMetaVLOffset(MetaVertexLabelT *metaVertexLabel)
{
    metaVertexLabel->topRecordNameOffset = 0;
    metaVertexLabel->commentOffset = 0;
    metaVertexLabel->schemaOffset = 0;
    metaVertexLabel->pkIndexOffset = 0;
    metaVertexLabel->secIndexesOffset = 0;
    metaVertexLabel->labelJsonOffset = 0;
    metaVertexLabel->configJsonOffset = 0;
    metaVertexLabel->schema->propertiesOffset = 0;
    metaVertexLabel->schema->superFieldsOffset = 0;
    metaVertexLabel->schema->yangInfoOffset = 0;
    metaVertexLabel->schema->nodesOffset = 0;
}

static Status ExecCreateNewDropVertexLabel(DropColumnStateT *dropColumnState, DbInstanceHdT dbInstance)
{
    DB_POINTER(dropColumnState);
    Status ret =
        DmCreateEmptyVertexLabelWithMemCtx(dropColumnState->vertexLabel->memCtx, &dropColumnState->newVertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "create alter vertexLabel.");
        return ret;
    }
    ret = DmCopyVertexLabel(dropColumnState->vertexLabel, dropColumnState->newVertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "copy vertexLabel.");
        return ret;
    }
    ExecResetMetaVLOffset(dropColumnState->newVertexLabel->metaVertexLabel);

    DmVertexLabelT *newVtxLabel = dropColumnState->newVertexLabel;
    const uint32_t propNum = newVtxLabel->metaVertexLabel->schema->propeNum;
    size_t propArraySize = (propNum - 1) * sizeof(DmPropertySchemaT);
    DmPropertySchemaT *newProperties = (DmPropertySchemaT *)DbDynMemCtxAlloc(newVtxLabel->memCtx, propArraySize);
    if (newProperties == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "alloc memctx.");
        return GMERR_OUT_OF_MEMORY;
    }
    uint32_t newPropNum = 0;
    for (uint32_t i = 0; i < propNum; i++) {
        if (i != dropColumnState->propDropId) {
            newProperties[newPropNum] = newVtxLabel->metaVertexLabel->schema->properties[i];
            newPropNum++;
        }
    }
    DmPropertySchemaT *tmp = newVtxLabel->metaVertexLabel->schema->properties;
    newVtxLabel->metaVertexLabel->schema->propeNum = newPropNum;
    newVtxLabel->metaVertexLabel->schema->properties = newProperties;

    ret = DmCreateVertexDesc(newVtxLabel->memCtx, newVtxLabel, &newVtxLabel->vertexDesc);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "create vertexDesc.");
        return ret;
    }
    ret = CataResetRecordDesc(newVtxLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "rebuild recordDesc.");
        return ret;
    }
    ret = CataGenerateUuid(DbGetInstanceByMemCtx(newVtxLabel->memCtx), &newVtxLabel->metaVertexLabel->uuid);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "generate uuid when create new drop vertexLabel.");
        return ret;
    }
    DbDynMemCtxFree(newVtxLabel->memCtx, tmp);
    return GMERR_OK;
}

static Status CreateDropColumnSlot(
    DropColumnStateT *dropColumnState, DbMemCtxT *memCtx, AASlotT *oldSlot, AASlotT **newSlot)
{
    DB_POINTER3(dropColumnState, oldSlot, newSlot);
    Status ret = GMERR_OK;
    DmVertexLabelT *newVtxLabel = dropColumnState->newVertexLabel;
    ret = NewAASlot(newVtxLabel->memCtx, newVtxLabel->vertexDesc, NULL, newSlot);
    if (ret != GMERR_OK) {
        return ret;
    }
    const uint32_t propNum = DmVertexGetPropNum(oldSlot->dmVertex, false);
    uint32_t index = 0;
    for (uint32_t i = 0; i < propNum; i++) {
        DmValueT dmValue = {0};
        if (i == dropColumnState->propDropId) {
            continue;
        } else {
            ret = DmVertexGetPropeByIdNoCopy(oldSlot->dmVertex, i, &dmValue);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        ret = DmVertexSetPropeById(index, dmValue, (*newSlot)->dmVertex);
        if (ret != GMERR_OK) {
            return ret;
        }
        index++;
    }
    (*newSlot)->dmVertex->vertexDesc = oldSlot->dmVertex->vertexDesc;
    (*newSlot)->addr = oldSlot->addr;
    (*newSlot)->oldTupleBuf = oldSlot->oldTupleBuf;
    return GMERR_OK;
}

static Status CmdAlterDropScan(SessionT *session, AlterTableStmtT *cmdReq, DmVertexLabelT *vertexLabel)
{
    DropColumnStateT dropColumnState = {.vertexLabel = vertexLabel};
    LabelBeginCfgT beginCfg = {.vertexLabel = vertexLabel,
        .seRunCtx = session->seInstance,
        .eeMemCtx = session->memCtx,
        .trxMemCtx = session->memCtx,
        .session = session};
    Status ret = HeapLabelBeginModify(beginCfg, &dropColumnState.vertexModifyDesc);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = ExecCreateNewDropVertexLabel(&dropColumnState, DbGetInstanceByMemCtx(session->memCtx));
    if (ret != GMERR_OK) {
        return ret;
    }

    LabelScanDescT *scanDesc = NULL;
    ret = HeapLabelBeginScan(beginCfg, &scanDesc);
    if (ret != GMERR_OK) {
        return ret;
    }
    ((HeapLabelScanDescT *)scanDesc)->needOldTuple = true;
    DmVertexT *vertex = NULL;
    ret = DmCreateEmptyVertexWithMemCtx(vertexLabel->memCtx, vertexLabel, &vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create vertex for drop table.");
        goto EXIT;
    }
    ret = HeapLabelReScan(scanDesc);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set sequential scan info for drop table.");
        goto EXIT;
    }
    AASlotT slot = {.dmVertex = vertex};
    AASlotT *newSlot = NULL;
    while (HeapLabelGetNext(scanDesc, &slot) == GMERR_OK) {
        ret = CreateDropColumnSlot(&dropColumnState, vertexLabel->memCtx, &slot, &newSlot);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "create new table.");
            goto EXIT;
        }
        ret = LabelUpdate(dropColumnState.vertexModifyDesc, newSlot);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "drop column.");
            goto EXIT;
        }
    }
EXIT:
    HeapLabelFinishScan(scanDesc);
    return ret;
}

static Status CmdAlterTableSqlWithLock(SessionT *session, AlterTableStmtT *cmdReq, DmVertexLabelT *oriVertexLabel)
{
    DB_POINTER3(session, cmdReq, oriVertexLabel);
    Status ret = GMERR_OK;
    bool isLock = TableIsLocked(session, oriVertexLabel);
    if (isLock) {
        // 在同一个事务中，如果表已经被锁住，禁止被修改
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_OPTION, "table is locked.");
        return GMERR_INVALID_OPTION;
    }

    // use latch protect ddl concurrency with dml
    LabelRWLatchT *latch = (LabelRWLatchT *)GetLabelRWLatchPtrById(
        oriVertexLabel->commonInfo->vertexLabelLatchId, DbGetInstanceByMemCtx(session->memCtx));
    if (latch == NULL) {
        ret = GMERR_DATA_EXCEPTION;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get latchLatch when alter table.");
        return ret;
    }
    LabelWLatchAcquire(latch);
    ret = CmdAlterVertexLabelPreCheck(oriVertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "precheck when alter table.");
        goto EXIT;
    }
    ret = CmdAlterTableByType(cmdReq, oriVertexLabel);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
EXIT:
    LabelWLatchRelease(latch);
    return ret;
}

Status CmdAlterTableSql(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    DB_POINTER3(cstate, cmd, cmdResult);
    Status ret = GMERR_OK;
    AlterTableStmtT *cmdReq = (void *)cmd;
    AlterTableResultStmtT *result = NULL;
    SessionT *session = cstate->session;

    if ((ret = NewAlterTableResultStmt(cstate->memCtx, &result, T_SQL_ALTER_TABLE_STMT)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "create new CreateTableResultStmt.");
        return ret;
    }

    CataKeyT cataKey = {cmdReq->dbId, cmdReq->nspId, cmdReq->tableName};
    DmVertexLabelT *oriVertexLabel = NULL;
    if ((ret = CataGetVertexLabelByName(DbGetInstanceByMemCtx(cstate->memCtx), &cataKey, &oriVertexLabel)) !=
        GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get vertexLabel by name: %s", cmdReq->tableName);
        return ret;
    }

    if (cmdReq->type == T_DDL_DROP_COLUMN) {
        ret = CmdAlterDropScan(session, cmdReq, oriVertexLabel);
        if (ret != GMERR_OK) {
            (void)CataReleaseVertexLabel(oriVertexLabel);
            return ret;
        }
        QryReleaseLastLabelLatch(session);
    }

    ret = CmdAlterTableSqlWithLock(session, cmdReq, oriVertexLabel);
    (void)CataReleaseVertexLabel(oriVertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    *cmdResult = (NodeT *)(void *)result;
    return GMERR_OK;
}
#endif
#ifdef FEATURE_DATALOG
Status CmdDegradeTable(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}
#endif

#ifdef FEATURE_TS
static Status CmdDropInTsMapAndTsInfo(DmVertexLabelT *vertexLabel, SessionT *session)
{
    Status ret = GMERR_OK;
    if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_TS_LOGICAL) {
        ret = SysTableTsMapMarkLabelDeletedById(session, vertexLabel->metaCommon.metaId);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(
                ret, "mark deleted logical label into systable, logical label: %s.", vertexLabel->metaCommon.metaName);
            return ret;
        }
    }
    return GMERR_OK;
}
#endif

static Status CmdDropTableInSystable(DmVertexLabelT *vertexLabel, SessionT *session)
{
    if (vertexLabel->metaCommon.isPersistent) {
        Status ret = SysTableDeleteOneVertexLabelEx(session, vertexLabel);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "delete vertexlabel from systable, vertexlabel: %s.", vertexLabel->metaCommon.metaName);
            return ret;
        }
#ifdef FEATURE_TS
        ret = CmdDropInTsMapAndTsInfo(vertexLabel, session);
        if (ret != GMERR_OK) {
            return ret;
        }
#endif
        return ret;
    }
#if defined(FEATURE_TS)
    if (DmVertexLabelIsTsLogicOrMemLabel(vertexLabel)) {
        return SysTableDeleteOneVertexLabelEx(session, vertexLabel);
    }
#endif
    return GMERR_OK;
}

static Status CmdDropLabelAcquireLatch(
    SessionT *session, DmVertexLabelT *vertexLabel, LabelRWLatchT *latch, bool isUseWriteCache)
{
#ifdef FEATURE_TS
    if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_TS_LOGICAL) {
        uint32_t lockTimeoutUs = QrySessionGetTsLockTimeout(session);
        if (!LabelWLatchTimedAcquire(latch, lockTimeoutUs)) {
            DB_LOG_ERROR(GMERR_LOCK_NOT_AVAILABLE, "Get latch lock timeout when drop table:%s",
                vertexLabel->metaCommon.metaName);
            return GMERR_LOCK_NOT_AVAILABLE;
        }
        return GMERR_OK;
    }
#endif
    LabelWLatchAcquire(latch);
    return GMERR_OK;
}

static Status CmdDropTableForMetaAndDataInner(
    SessionT *session, DmVertexLabelT *vertexLabel, LabelRWLatchT *latch, bool isUseWriteCache, bool dropSysTable)
{
    // 校验表锁的有效性，防止并发重复删除表
    Status ret = LabelLatchCheckVersion(latch, vertexLabel->commonInfo->vertexLabelLatchVersionId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "table has already been dropped, table name: %s", vertexLabel->metaCommon.metaName);
        goto ERROR;
    }
    if (dropSysTable) {
        ret = CmdDropTableInSystable(vertexLabel, session);
        if (ret != GMERR_OK) {
            goto ERROR;
        }
    }
    ret = CmdDropVertexLabel(session->seInstance, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop a table, table name: %s", vertexLabel->metaCommon.metaName);
        goto ERROR;
    }
    ret = CmdDropVertexLabelFromCatalog(vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop meta from catalog, table name: %s", vertexLabel->metaCommon.metaName);
        goto ERROR;
    }
    return ret;
ERROR:
    LabelWLatchRelease(latch);
    WriteCacheMgrUnLockAcquire(isUseWriteCache);
    return ret;
}

static Status CmdDropTableForMetaAndData(DmVertexLabelT *vertexLabel, SessionT *session, bool dropSysTable)
{
    Status ret = GMERR_OK;
    LabelRWLatchT *latch = (LabelRWLatchT *)GetLabelRWLatchPtrById(
        vertexLabel->commonInfo->vertexLabelLatchId, DbGetInstanceByMemCtx(session->memCtx));
    if (latch == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "get latchLatch when drop table, table name: %s",
            vertexLabel->metaCommon.metaName);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    bool isUseWriteCache = vertexLabel->metaVertexLabel->isUseWriteCache;
    // 保证加锁顺序，避免与后台Merge死锁
    WriteCacheMgrLockAcquire(isUseWriteCache);
    // use latch protect ddl concurrency with dml
    ret = CmdDropLabelAcquireLatch(session, vertexLabel, latch, isUseWriteCache);
    if (ret != GMERR_OK) {
        WriteCacheMgrUnLockAcquire(isUseWriteCache);
        return ret;
    }

    ret = CmdDropTableForMetaAndDataInner(session, vertexLabel, latch, isUseWriteCache, dropSysTable);
    if (ret != GMERR_OK) {
        return ret;
    }

    RemoveLabelLatchAndRuMode(latch, vertexLabel->commonInfo->vertexLabelLatchId,
        vertexLabel->commonInfo->vertexLabelLatchVersionId, DbGetInstanceByMemCtx(session->memCtx));
    WriteCacheMgrUnLockAcquire(isUseWriteCache);
    return ret;
}

#ifdef FEATURE_SQL
static Status ProcDropTableExtend(const DmVertexLabelT *vertexLabel)
{
#ifndef IDS_HAOTIAN
    char *vlName = vertexLabel->metaCommon.metaName;
    Status ret = DmDropTriggerInfoByVertexLabel(vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop trigger info:%s.", vlName);
        return ret;
    }
    uint32_t vlId = vertexLabel->metaCommon.metaId;
    DmVertexLabelConstraintT *vlCons = vertexLabel->vertexLabelConstraint;
    if (vlCons == NULL || DbListGetItemCnt(&vlCons->fkConstraints) == 0) {
        return GMERR_OK;
    }
    // 更新相关表的外键信息时 暂不支持用于回滚的备份列表
    ret = DmUpdateRelatedVtxLabelWhenDropWithFkCons(
        vlId, &vlCons->fkConstraints, DbGetInstanceByMemCtx(vertexLabel->memCtx));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "update foreign key info:%s.", vlName);
        return ret;
    }
#endif
    return GMERR_OK;
}
#endif

Status CmdDropTable(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    DB_POINTER3(cstate, cmd, cmdResult);
    Status ret;
    DropTableStmtT *cmdReq = (void *)cmd;
    DropTableResultStmtT *result = NULL;  // result is not used when drop table but is retained to ensure
                                          // function prototype remains unchanged.

    DmVertexLabelT *vertexLabel = NULL;
    if ((ret = CataGetVertexLabelById(DbGetInstanceByMemCtx(cstate->memCtx), cmdReq->vertexLabelId, &vertexLabel)) !=
        GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get vertexLabel by id:%" PRIu32 ".", cmdReq->vertexLabelId);
        return ret;
    }

    if ((ret = CmdDropOrTruncateVertexLabelCheck(vertexLabel)) != GMERR_OK) {
        goto EXIT;
    }
    ret = CmdDropTableForMetaAndData(vertexLabel, cstate->session, cmdReq->dropSysTable);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    QryIncCheckChangedObjectCnt();

#ifdef FEATURE_SQL
    ret = ProcDropTableExtend(vertexLabel);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
#endif
#ifdef FEATURE_STREAM
    CmdProcessDropStreamTable(vertexLabel);
#endif
    *cmdResult = (NodeT *)(void *)result;
EXIT:
    (void)CataReleaseVertexLabel(vertexLabel);
    return ret;
}

#ifdef FEATURE_DATALOG
Status NewTempTableByLabelIdAndName(
    DbMemCtxT *tempTableCtx, uint32_t labelId, char *labelName, TempTableT **tempTablePtr)
{
    DB_POINTER3(tempTableCtx, labelName, tempTablePtr);
    // guard for a non empty pointer
    *tempTablePtr = NULL;

    // memory will be freed unitied, see Note1
    TempTableT *tempTableInner = (TempTableT *)DbDynMemCtxAlloc(tempTableCtx, sizeof(*tempTableInner));
    if (tempTableInner == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Temp Table Alloc Memory");
        return GMERR_OUT_OF_MEMORY;
    }
    *tempTableInner = (TempTableT){0};
    tempTableInner->memctx = tempTableCtx;
    tempTableInner->vertexLabelId = labelId;
    tempTableInner->mergeDone = false;

    DbCreateList(&tempTableInner->reusableObj.msgNotifyInsertTuples, sizeof(void *), tempTableCtx);
    DbCreateList(&tempTableInner->reusableObj.msgNotifyDeleteTuples, sizeof(void *), tempTableCtx);

    uint32_t len = (uint32_t)strlen(labelName);
    // memory will be freed unitied, see Note1
    tempTableInner->vertexLabelName = (char *)DbDynMemCtxAlloc(tempTableCtx, len + 1);
    if (tempTableInner->vertexLabelName == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Alloc temp table's name, vertexLabel name =%s", labelName);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t rc = strncpy_s(tempTableInner->vertexLabelName, len + 1, labelName, len);
    if (rc != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "strcpy property %s when create temp table.", labelName);
        return GMERR_FIELD_OVERFLOW;
    }

    tempTableInner->isModify = false;
    TempTableSetInPriQueue(tempTableInner, false);

    // memory will be freed unitied , see Note1
    tempTableInner->tempTableTuples = DbDynMemCtxAlloc(tempTableCtx, sizeof(DbListT));
    if (tempTableInner->tempTableTuples == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "allocate memory when create temp table.");
        return GMERR_OUT_OF_MEMORY;
    }

    DbCreateList(tempTableInner->tempTableTuples, sizeof(DmVertexT *), tempTableInner->memctx);
    *tempTablePtr = tempTableInner;

    return GMERR_OK;
}

#define TEMP_CLEAR_EXCEED 500
void ClearAllDataInTempTable(TempTableT *tempTable)
{
    DB_POINTER(tempTable);
    uint64_t startTime = DbClockGetTsc();

    uint32_t cnt = DbListGetItemCnt(tempTable->tempTableTuples);
    for (uint32_t i = 0; i < cnt; i++) {
        DmVertexT *dmVertex = *(DmVertexT **)DbListItem(tempTable->tempTableTuples, i);
        if (dmVertex != NULL) {
            DmDestroyVertex(dmVertex);
        }
        if (DbExceedTime(startTime, TEMP_CLEAR_EXCEED)) {
            (void)DrtKeepThisWorkerAlive(DbGetInstanceByMemCtx(tempTable->memctx));
            startTime = DbClockGetTsc();
        }
    }
    DbDestroyList(tempTable->tempTableTuples);
    DbCreateList(tempTable->tempTableTuples, sizeof(DmVertexT *), tempTable->memctx);
}

Status NewTempTable(CreateTableCmdT *cmd, DbMemCtxT *tempTableCtx, TempTableT **tempTablePtr)
{
    DB_POINTER3(cmd, tempTableCtx, tempTablePtr);
    return NewTempTableByLabelIdAndName(
        tempTableCtx, cmd->vertexLabel->metaCommon.metaId, cmd->vertexLabel->metaCommon.metaName, tempTablePtr);
}

Status CmdCreateTempTable(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    DB_POINTER3(cstate, cmd, cmdResult);
    CreateTempTableStmtT *cmdReq = (void *)cmd;
    CreateTempTableResultStmtT *result;

    Status ret;
    if ((ret = NewCreateTempTableResultStmt(cstate->memCtx, &result)) != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "create new CreateTempTableResultStmt.");
        return ret;
    }

    CreateTableCmdT createTableCmd = {.vertexLabel = cmdReq->vertexLabel};
    if ((ret = NewTempTable(&createTableCmd, cstate->memCtx, &(result->deltaTable))) != GMERR_OK) {
        return ret;
    }

    *cmdResult = (NodeT *)(void *)result;
    return GMERR_OK;
}

Status CmdDropTempTable(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    DB_POINTER3(cstate, cmd, cmdResult);
    DropTempTableStmtT *cmdReq = (void *)cmd;
    DropTempTableResultStmtT *result;

    Status ret;
    if ((ret = NewDropTempTableResultStmt(cstate->memCtx, &result)) != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "create new DropTempTableResultStmtT.");
        return ret;
    }
    ClearAllDataInTempTable(cmdReq->deltaTable);

    *cmdResult = (NodeT *)(void *)result;
    return GMERR_OK;
}

static Status DropOneTableInSo(SeRunCtxHdT seRunCtx, DmVertexLabelT *vertexLabel, bool ignorePubsub)
{
    Status ret = CmdDropOrTruncateVertexLabelCheck(vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "pass the pre check of dropping vertexLabel: %s", vertexLabel->metaCommon.metaName);
        (void)CataReleaseVertexLabel(vertexLabel);
        return ret;
    }

    if (SECUREC_LIKELY(!ignorePubsub)) {
        // 校验订阅是否存在
        if (vertexLabel->commonInfo->metaInfoAddr->subscriptionNum > 0) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_RESTRICT_VIOLATION,
                "drop vertexlabel which has any sub, nspId = %" PRIu32 ", vertexLabel name = %s",
                vertexLabel->metaCommon.namespaceId, vertexLabel->metaCommon.metaName);
            return GMERR_RESTRICT_VIOLATION;
        }
    }

    ret = CmdDropVertexLabel(seRunCtx, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop vertexLabel: %s!", vertexLabel->metaCommon.metaName);
        (void)CataReleaseVertexLabel(vertexLabel);
        return ret;
    }

    ret = CmdDropVertexLabelFromCatalog(vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop vertexLabel: %s!", vertexLabel->metaCommon.metaName);
        (void)CataReleaseVertexLabel(vertexLabel);
        return ret;
    }

    // notify recycle status merge sub list
    if (DmIsLabelSupportStatusMerge(vertexLabel)) {
        (void)QryAddPubSubGcTask(
            vertexLabel->metaCommon.metaId, GC_TYPE_CLEAR_LIST, vertexLabel->commonInfo->statusMergeList);
    }
    (void)CataReleaseVertexLabel(vertexLabel);
    QryIncCheckChangedObjectCnt();
    return GMERR_OK;
}

Status CmdDropAllTableInSo(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    DB_POINTER3(cstate, cmd, cmdResult);
    Status ret = GMERR_OK;
    Status lastErr = GMERR_OK;
    DbMemCtxT *memCtx = cstate->memCtx;
    DropAllTableInSoStmtT *cmdReq = (void *)cmd;

    DropAllTableInSoResultStmtT *result = NULL;
    if ((ret = NewDropAllTableInSoResultStmt(memCtx, &result)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "create new DropAllTableInSoResultStmtT.");
        return ret;
    }

    // drop vertexlabel in the namespace one by one
    DmVertexLabelT *vertexLabel = NULL;
    DbOamapIteratorT iter = 0;
    uint64_t startTime = DbClockGetTsc();
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(cstate->memCtx);
    while ((vertexLabel = CataFetchVertexLabelBySoId(cmdReq->soId, &iter, dbInstance)) != NULL) {
        if (DbExceedTime(startTime, EXEC_SPLIT_TIME)) {
            if (!DrtKeepThisWorkerAlive(DbGetInstanceByMemCtx(cstate->memCtx))) {
                return GMERR_INTERNAL_ERROR;
            }
            startTime = DbClockGetTsc();
        }

        if (vertexLabel->commonInfo->datalogLabelInfo->isTrans) {
            CataKeyT cataKey;
            CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, vertexLabel->metaCommon.namespaceId,
                vertexLabel->metaCommon.metaName + strlen(DTL_EXTERNAL_PREFIX));
            DmVertexLabelT *newLabel = NULL;
            ret = CataGetVertexLabelByName(DbGetInstanceByMemCtx(cstate->memCtx), &cataKey, &newLabel);
            if (ret != GMERR_OK) {
                DB_LOG_WARN(ret, "get external table by fake label:%s when drop all table in so",
                    vertexLabel->metaCommon.metaName);
            } else {
                ret = DropOneTableInSo(cstate->seInstance, newLabel, cmdReq->ignorePubsub);
                if (ret != GMERR_OK) {
                    DB_LOG_ERROR(ret, "drop external table by fake label:%s when unload datalog so",
                        vertexLabel->metaCommon.metaName);
                    lastErr = ret;
                }
            }
        }

        // 卸载尽力而为
        ret = DropOneTableInSo(cstate->seInstance, vertexLabel, cmdReq->ignorePubsub);
        if (ret != GMERR_OK) {
            lastErr = ret;
        }
    }

    *cmdResult = (NodeT *)(void *)result;
    return lastErr == GMERR_OK ? GMERR_OK : lastErr;
}
#endif

#ifdef FEATURE_TS
static Status CmdTruncateLabelAcquireLatch(SessionT *session, DmVertexLabelT *vertexLabel, LabelRWLatchT *latch)
{
    if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_TS_LOGICAL ||
        vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_NORMAL) {
        uint32_t lockTimeoutUs = QrySessionGetTsLockTimeout(session);
        if (!LabelWLatchTimedAcquire(latch, lockTimeoutUs)) {
            DB_LOG_ERROR(GMERR_LOCK_NOT_AVAILABLE, "Get latch lock timeout:%s", vertexLabel->metaCommon.metaName);
            return GMERR_LOCK_NOT_AVAILABLE;
        }
        return GMERR_OK;
    }
    LabelWLatchAcquire(latch);
    return GMERR_OK;
}

static Status CmdDropOnePhysicalLabelHeap(SessionT *session, uint32_t physicalId, PhysicalLabelMetaT *meta)
{
    HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = *(ShmemPtrT *)(void *)(&meta->heapShmAddr),
        .isPersistent = true,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    Status ret = HeapLabelDrop(session->seInstance, &heapCntrAcsInfo, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "truncate heap for physical labels in cmd, physical id:%" PRIu32 ".", physicalId);
        return ret;
    }
    ret = IdxDrop(session->seInstance, (uint8_t)BTREE_INDEX, *(ShmemPtrT *)(void *)(&meta->indexShmAddr));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "truncate heap for physical labels in cmd, physical id:%" PRIu32 ".", physicalId);
        return ret;
    }
    return ret;
}

static Status CmdTruncateOnePhysicalLabelForMeta(SessionT *session, uint32_t physicalId, PhysicalLabelMetaT *meta)
{
    Status ret = SysTableTsMapGetPhysicalLabelMetaById(session, physicalId, meta);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get physical label meta in ts map, label id: %" PRIu32 ".", physicalId);
        return ret;
    }
    ret = SysTableTsMapDeleteByPhysicalId(session, physicalId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "process physical label.");
        return ret;
    }
    ret = InitMemFieldsForPhysicalLabel(session, physicalId, *(ShmemPtrT *)(void *)&meta->heapShmAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init mem field of heap trm during truncating physical labels.");
        return ret;
    }
    return ret;
}

static Status CmdTruncateOnePhysicalLabel(SessionT *session, uint32_t physicalId, uint32_t logicalId)
{
    PhysicalDestroyMiniBatchCacheById(physicalId);
    PhysicalLabelMetaT meta = {0};
    Status ret = SeTransBegin(session->seInstance, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "begin trans before truncating physical label, id: %" PRIu32 ".", physicalId);
        return ret;
    }
    ret = CmdTruncateOnePhysicalLabelForMeta(session, physicalId, &meta);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get meta when truncating physical label, id: %" PRIu32 ".", physicalId);
        (void)SeTransRollback(session->seInstance, false);
        return ret;
    }
    DB_ASSERT(meta.logicalId == logicalId);
    ret = CmdDropOnePhysicalLabelHeap(session, physicalId, &meta);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop one physical label heap, id: %" PRIu32 ".", physicalId);
        (void)SeTransRollback(session->seInstance, false);
        return ret;
    }
    CuStorageKeyT cuKey = {.tblSpcId = logicalId, .tblId = physicalId};
    ret = SeRemoveCuDir(session->seInstance, &cuKey);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "remove cu directory, id: %" PRIu32 ".", physicalId);
        (void)SeTransRollback(session->seInstance, false);
        return ret;
    }
    ret = SeTransCommit(session->seInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "commit transaction for physical label truncate, id: %" PRIu32 ".", physicalId);
        return ret;
    }
    RemoveInitedPhysicalLabelIdFromMap(physicalId);
    TsRemovePhysicLabelDataCache(logicalId, physicalId);
    ret = TsLcmLabelIdRecycle(&physicalId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "recycle physical id: %" PRIu32 ".", physicalId);
        return ret;
    }
    ret = TsLcmLabelIdRecycle(&meta.indexId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "recycle secIndex id: %" PRIu32 ".", meta.indexId);
        return ret;
    }
    return ret;
}

static Status CmdTruncateOnelogicalLabel(SessionT *session, DmVertexLabelT *logicalLabel, uint32_t logicalId)
{
    Status ret = SeTransBegin(session->seInstance, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "start transaction for truncating logical label, label id: %" PRIu32 ".", logicalId);
        return ret;
    }
    CuStorageKeyT cuStorageKey = {.tblSpcId = logicalId, .tblId = INVALID_TBL_ID};
    ret = SeRemoveCuDir(session->seInstance, &cuStorageKey);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "remove logical label dir, labelId: %" PRIu32 ".", logicalId);
        (void)SeTransRollback(session->seInstance, false);
        return ret;
    }
    uint32_t persistSpaceId = ((DmTsInfoT *)logicalLabel->metaVertexLabel->extraInfo.data)->persistentSpaceId;
    ret = SeSpaceTruncate(session->seInstance, persistSpaceId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "truncate space, labelId: %" PRIu32 ".", logicalId);
        (void)SeTransRollback(session->seInstance, false);
        return ret;
    }
    ret = SeTransCommit(session->seInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "commit transaction when truncating single logical label, label id: %" PRIu32 ".", logicalId);
        return ret;
    }
    return ret;
}

static Status CmdTruncateAllPhysicalLabel(uint32_t logicalId, DmVertexLabelT *vertexLabel, SessionT *session)
{
    if (vertexLabel->metaVertexLabel->vertexLabelType != VERTEX_TYPE_TS_LOGICAL) {
        return GMERR_OK;
    }
    DbListT physicalIdList = {0};
    DbCreateList(&physicalIdList, sizeof(uint32_t), session->memCtx);
    Status ret = SysTableTsMapGetIdListByLogicalId(session, vertexLabel->metaCommon.metaId, &physicalIdList);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get physical id list when truncating a logical label, logical id: %" PRIu32 ".",
            vertexLabel->metaCommon.metaId);
        return ret;
    }
    uint32_t labelNum = DbListGetItemCnt(&physicalIdList);
    uint32_t physicalId = 0;
    for (uint32_t i = 0; i < labelNum; i++) {
        (void)DrtKeepThisWorkerAlive(DbGetInstanceByMemCtx(session->memCtx));
        physicalId = *(uint32_t *)DbListItem(&physicalIdList, i);
        ret = CmdTruncateOnePhysicalLabel(session, physicalId, logicalId);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "truncate one physical label, id = %" PRIu32 ".", physicalId);
            return ret;
        }
    }
    (void)DrtKeepThisWorkerAlive(DbGetInstanceByMemCtx(session->memCtx));
    ret = CmdTruncateOnelogicalLabel(session, vertexLabel, logicalId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "truncate one logical label, id = %" PRIu32 ".", logicalId);
        return ret;
    }
    return ret;
}

static Status CmdTruncateTableForMetaAndData(uint32_t logicalId, DmVertexLabelT *vertexLabel, SessionT *session)
{
    Status ret = GMERR_OK;
    LabelRWLatchT *latch = (LabelRWLatchT *)GetLabelRWLatchPtrById(vertexLabel->commonInfo->vertexLabelLatchId, NULL);
    if (latch == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "get latchLatch when truncate table, table name: %s",
            vertexLabel->metaCommon.metaName);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    bool isUseWriteCache = vertexLabel->metaVertexLabel->isUseWriteCache;
    // 保证加锁顺序，避免与后台Merge死锁
    WriteCacheMgrLockAcquire(isUseWriteCache);
    // use latch protect ddl concurrency with dml
    ret = CmdTruncateLabelAcquireLatch(session, vertexLabel, latch);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "acquire latch when truncate table, table name: %s", vertexLabel->metaCommon.metaName);
        WriteCacheMgrUnLockAcquire(isUseWriteCache);
        return ret;
    }
    // 校验表锁的有效性，防止并发重复清空表
    ret = LabelLatchCheckVersion(latch, vertexLabel->commonInfo->vertexLabelLatchVersionId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "table has already been truncated, table name: %s", vertexLabel->metaCommon.metaName);
        goto FINISH;
    }
    ret = CmdTruncateAllPhysicalLabel(logicalId, vertexLabel, session);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "truncate all physical label, table name: %s", vertexLabel->metaCommon.metaName);
        goto FINISH;
    }
    CmdTruncateVertexLabel(session->seInstance, vertexLabel);
FINISH:
    LabelWLatchRelease(latch);
    WriteCacheMgrUnLockAcquire(isUseWriteCache);
    return ret;
}

Status CmdTruncateTable(CStateT *cstate, NodeT *cmd, NodeT **cmdResult)
{
    DB_POINTER3(cstate, cmd, cmdResult);
    TruncateTableStmtT *cmdReq = (void *)cmd;
    TruncateTableResultStmtT *result = NULL;
    DmVertexLabelT *vertexLabel = NULL;
    Status ret = CataGetVertexLabelById(DbGetInstanceByMemCtx(cstate->memCtx), cmdReq->vertexLabelId, &vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get vertexLabel by id, id = %" PRIu32 ".", cmdReq->vertexLabelId);
        return ret;
    }
    ret = CmdDropOrTruncateVertexLabelCheck(vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "check vertex label.");
        goto EXIT;
    }
    ret = CmdTruncateTableForMetaAndData(cmdReq->vertexLabelId, vertexLabel, cstate->session);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "truncate table for meta and data.");
        goto EXIT;
    }
    *cmdResult = (NodeT *)(void *)result;
EXIT:
    (void)CataReleaseVertexLabel(vertexLabel);
    return ret;
}
#endif

#ifdef __cplusplus
}
#endif
