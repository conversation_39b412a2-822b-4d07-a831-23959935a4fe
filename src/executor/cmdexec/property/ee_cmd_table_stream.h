/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author:
 * Create: 2024-11-05
 */
#ifndef EE_CMD_TABLE_STREAM_H
#define EE_CMD_TABLE_STREAM_H
#ifdef FEATURE_STREAM
#include "dm_meta_prop_strudefs.h"

#ifdef __cplusplus
extern "C" {
#endif

void CmdProcessDropStreamTable(DmVertexLabelT *vertexLabel);
Status InitStreamVertexLabel(DmVertexLabelT *vertexLabel);
Status CmdDropStreamVertexLabelCheck(const DmVertexLabelT *vertexLabel);

#ifdef __cplusplus
}
#endif
#endif  // FEATURE_STREAM
#endif  // EE_CMD_TABLE_STREAM_H
