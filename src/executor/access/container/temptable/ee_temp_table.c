/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: temp table impl.
 * Note1: memCtx is used in short process period , we will free all memory when finishing one request
 * Author: GMDBv5 EE Team
 * Create: 2022-7-13
 */

#include "drt_instance.h"
#include "ee_access_method.h"
#include "ee_associative_array_slot.h"
#include "ee_primary_key_cmp.h"
#include "ee_delta_table_access_method.h"
#include "ee_delta_table_info_access_method.h"
#include "ee_reserved_names.h"
#include "db_timer.h"
#include "se_index.h"
#include "ee_plan_stmt.h"
#include "dm_meta_prop_label.h"
#include "dm_data_math.h"
#include "ee_cmd_router_fusion.h"
#include "ee_temp_table.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

Status NewTempTableWithVtxLabel(DbMemCtxT *tempTableCtx, DmVertexLabelT *vtxLabel, TempTableT **tempTablePtr)
{
    DB_POINTER3(tempTableCtx, vtxLabel, tempTablePtr);
    return NewTempTableByLabelIdAndName(
        tempTableCtx, vtxLabel->metaCommon.metaId, vtxLabel->metaCommon.metaName, tempTablePtr);
}

void DropTempTable(TempTableT *tempTable)
{
    DB_POINTER(tempTable);
    DbClearList(tempTable->tempTableTuples);
}

void ClearTempTable(TempTableT *tempTable)
{
    DB_POINTER(tempTable);
    ClearAllDataInTempTable(tempTable);
}

Status TempTableInsert(TempTableT *tempTable, DmVertexT *dmVertex)
{
    DB_POINTER2(tempTable, dmVertex);
    return DbAppendListItem(tempTable->tempTableTuples, &dmVertex);
}

DmVertexT *TempTableGetElement(TempTableT *tempTable, uint32_t index)
{
    DB_POINTER(tempTable);
    if (index >= DbListGetItemCnt(tempTable->tempTableTuples)) {
        return NULL;
    }

    DmVertexT *ret = *(void **)DbListItem(tempTable->tempTableTuples, index);
    return ret;
}

uint32_t TempTableCount(TempTableT *temptable)
{
    DB_POINTER(temptable);
    return DbListGetItemCnt(temptable->tempTableTuples);
}

bool TempTableIsEmpty(TempTableT *temptable)
{
    DB_POINTER(temptable);
    return TempTableCount(temptable) == 0;
}

static uint32_t VertexCmpAllField(const void *key1, const void *key2)
{
    DB_POINTER2(key1, key2);
    bool equal = false;
    Status ret = DmVertexEqual((const DmVertexT *)key1, (const DmVertexT *)key2, false, &equal);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "compare vertex, labelName=%s.", ((const DmVertexT *)key1)->vertexDesc->labelName);
        return equal;
    }

    return equal;
}

static Status TempTableMergeVertex(DmVertexT *dst, DmVertexT *src)
{
    DB_POINTER2(dst, src);
    DmValueT countDst = {};
    DmValueT countSrc = {};
    Status ret = GMERR_OK;
    if ((ret = DmVertexGetPropeByIdNoCopy(dst, DTL_RESERVED_COUNT_INDEX, &countDst)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "get cnt, labelId=%" PRIu32, dst->vertexDesc->labelId);
        return ret;
    }

    ret = DmVertexGetPropeByIdNoCopy(src, DTL_RESERVED_COUNT_INDEX, &countSrc);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get cnt, labelId=%" PRIu32, src->vertexDesc->labelId);
        return ret;
    }

    ret = DmValueAdd(&countDst, &countSrc, &countDst);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "add cnt. srcVlId=%" PRIu32 ",dstVlId=%" PRIu32, src->vertexDesc->labelId, dst->vertexDesc->labelId);
        return ret;
    }

    if ((ret = DmVertexSetPropeById(DTL_RESERVED_COUNT_INDEX, countDst, dst)) != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "set cnt. srcVlId=%" PRIu32 ",dstVlId=%" PRIu32, src->vertexDesc->labelId, dst->vertexDesc->labelId);
        return ret;
    }

    return GMERR_OK;
}

Status CheckIndexKeyLen(DmVertexT *dmVertex, uint32_t indexId, uint32_t indexLen)
{
    DmIndexKeyBufInfoT *indexKeyInfo = NULL;
    Status ret = DmVertexGetIndexKeyInfo(dmVertex, indexId, &indexKeyInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get IndexKeyInfo");
        return ret;
    }

    if (indexLen > DM_MAX_INDEX_KEY_SIZE + indexKeyInfo->nullInfoBytes) {
        ret = GMERR_PROGRAM_LIMIT_EXCEEDED;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "indexLen:%" PRIu32 " exceeds; labelName=%s, id=%" PRIu32, indexLen,
            dmVertex->vertexDesc->labelName, dmVertex->vertexDesc->labelId);
        return ret;
    }
    return GMERR_OK;
}

static Status TempTableMergeOne(TempTableT *tempTable, DmVertexT *dmVertex)
{
    DB_POINTER2(tempTable, dmVertex);
    Status ret;
    uint32_t indexId = dmVertex->vertexDesc->indexKeyBufInfos[0].indexId;
    IndexKeyT indexkey = {};
    ret = DmGetKeyBufFromVertex(dmVertex, indexId, &indexkey.keyData, &indexkey.keyLen);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get keyBuf");
        return ret;
    }

    ret = CheckIndexKeyLen(dmVertex, indexId, indexkey.keyLen);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t hash = DbHash32(indexkey.keyData, indexkey.keyLen);
    DmVertexT *oldDmVertex = DbOamapLookupKey(&tempTable->mergeMap, hash, dmVertex);
    if (oldDmVertex != NULL) {
        return TempTableMergeVertex(oldDmVertex, dmVertex);  // 这里被合并的DmVertex，内存是没有释放的。
    }

    ret = DbOamapInsert(&tempTable->mergeMap, hash, dmVertex, NULL, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "hashmap insert AASlot");
        return ret;
    }

    return GMERR_OK;
}

static Status TempTableMergeInit(TempTableT *tempTable, bool classify)
{
    DB_POINTER(tempTable);
    uint32_t tupleCnt = DbListGetItemCnt(tempTable->tempTableTuples);
    DbMemCtxT *memctx = tempTable->memctx;

    // init mergeMap for accelerate count of tuples in tempTableTuples
    Status ret = DbOamapInit(&tempTable->mergeMap, tupleCnt, VertexCmpAllField, memctx, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "hashmap init, labelName=%s", tempTable->vertexLabelName);
        return ret;
    }
    DbOamapResetIterator(&tempTable->mapIter);

    if (classify) {
        // init deleteTupleList for deleted tuples
        tempTable->deleteTupleList = DbDynMemCtxAlloc(memctx, sizeof(DbListT));
        if (tempTable->deleteTupleList == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_OUT_OF_MEMORY, "deleteTupleList alloc mem. labelName=%s", tempTable->vertexLabelName);
            return GMERR_OUT_OF_MEMORY;
        }
        DbCreateListWithExtendSize(tempTable->deleteTupleList, sizeof(DmVertexT *), 1 + (tupleCnt >> 1), memctx);

        // init insertTupleList for inserted and updated tuples
        tempTable->insertTupleList = DbDynMemCtxAlloc(memctx, sizeof(DbListT));
        if (tempTable->insertTupleList == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_OUT_OF_MEMORY, "insertTupleList alloc mem. labelName=%s", tempTable->vertexLabelName);
            return GMERR_OUT_OF_MEMORY;
        }
        DbCreateListWithExtendSize(tempTable->insertTupleList, sizeof(DmVertexT *), 1 + (tupleCnt >> 1), memctx);
    }

    tempTable->mergeCursor = 0;
    tempTable->state = TEMP_MERGE_STATE_MERGE;
    return GMERR_OK;
}

#define TEMP_MERGE_EXCEED 300
static Status TempTableMerging(TempTableT *tempTable, MergeCallBack mergeCallBack, TempMergeStateE nextState)
{
    Status ret = GMERR_OK;
    uint64_t startTime = DbClockGetTsc();
    uint32_t size = TempTableCount(tempTable);
    DmValueT totalCount = {0};
    totalCount.type = DB_DATATYPE_INT32;
    totalCount.value.intValue = 0;

    for (uint32_t i = tempTable->mergeCursor; i < size; i++) {
        DmVertexT *dmVertex = *(void **)DbListItem(tempTable->tempTableTuples, i);

        if ((ret = DmVertexGetPropeById(dmVertex, DTL_RESERVED_COUNT_INDEX, &totalCount)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "vertex count, labelName=%s, labelId=%" PRIu32, tempTable->vertexLabelName,
                tempTable->vertexLabelId);
            return ret;
        }
        if (totalCount.value.intValue == 0) {
            continue;
        }

        ret = mergeCallBack(tempTable, dmVertex);
        if (ret != GMERR_OK) {
            return ret;
        }

        if (DbExceedTime(startTime, TEMP_MERGE_EXCEED)) {
            tempTable->state = TEMP_MERGE_STATE_MERGE;
            tempTable->mergeCursor = i + 1;
            return GMERR_OK;
        }
    }

    DbClearList(tempTable->tempTableTuples);
    tempTable->mergeCursor = 0;
    tempTable->state = nextState;
    return GMERR_OK;
}

static Status TempTableMergeWrite(TempTableT *tempTable, bool classify)
{
    DB_POINTER(tempTable);
    Status ret;
    uint64_t startTime = DbClockGetTsc();
    DmVertexT *key = NULL;
    void *value = NULL;

    while (DbOamapFetch(&tempTable->mergeMap, &tempTable->mapIter, (void **)&key, &value) == GMERR_OK) {
        DbListT *list = NULL;
        if (classify) {
            DmValueT count = {0};
            if ((ret = DmVertexGetPropeByIdNoCopy(key, DTL_RESERVED_COUNT_INDEX, &count)) != GMERR_OK) {
                DB_LOG_ERROR(ret, "dmVertex dtlReservedCount, labelName=%s", key->vertexDesc->labelName);
                return ret;
            }
            // discard tuples with count = 0 when classifying delta tuples in tempTable
            if (count.value.intValue == 0) {
                continue;
            }
            list = count.value.intValue < 0 ? tempTable->deleteTupleList : tempTable->insertTupleList;
        } else {
            list = tempTable->tempTableTuples;
        }

        ret = DbAppendListItem(list, (void *)&key);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "vertex append to list. labelName=%s", tempTable->vertexLabelName);
            return ret;
        }

        if (DbExceedTime(startTime, TEMP_MERGE_EXCEED)) {
            tempTable->state = TEMP_MERGE_STATE_WRITE;
            return GMERR_OK;
        }
    }

    if (classify) {
        tempTable->updateStartIdx = DbListGetItemCnt(tempTable->insertTupleList);
    }
    tempTable->state = TEMP_MERGE_STATE_DONE;
    return GMERR_OK;
}

static Status TempTableAllMerge(TempTableT *tempTable, bool classify, bool *done)
{
    DB_POINTER2(tempTable, done);
    Status ret = GMERR_OK;
    TempMergeStateE oldState = 0;
    do {
        oldState = tempTable->state;
        switch (tempTable->state) {
            case TEMP_MERGE_STATE_UNINIT:
                ret = TempTableMergeInit(tempTable, classify);
                break;
            case TEMP_MERGE_STATE_MERGE:
                ret = TempTableMerging(tempTable, TempTableMergeOne, TEMP_MERGE_STATE_WRITE);
                break;
            case TEMP_MERGE_STATE_WRITE:
                ret = TempTableMergeWrite(tempTable, classify);
                break;
            case TEMP_MERGE_STATE_DONE:
                *done = true;
                DbOamapDestroy(&tempTable->mergeMap);
                return GMERR_OK;
            case TEMP_MERGE_STATE_WRITE_INSERT:
            case TEMP_MERGE_STATE_WRITE_UPDATE:
            default:
                ret = GMERR_DATA_EXCEPTION;
                DB_LOG_ERROR_AND_SET_LASTERR(
                    ret, "state=%" PRIu32 ". labelName=%s", (uint32_t)tempTable->state, tempTable->vertexLabelName);
                return ret;
        }
    } while (oldState != tempTable->state);
    *done = false;
    return ret;
}

Status TempTableMerge(TempTableT *tempTable, bool *done)
{
    DB_POINTER2(tempTable, done);
    if (tempTable->state == TEMP_MERGE_STATE_UNINIT && TempTableCount(tempTable) == 1) {
        *done = true;
        return GMERR_OK;
    }
    return TempTableAllMerge(tempTable, false, done);
}

static Status TempTableQuickClassifyMerge(TempTableT *tempTable, uint32_t *updIdx, bool *done)
{
    DbMemCtxT *memctx = tempTable->memctx;
    // init deleteTupleList for deleted tuples
    tempTable->deleteTupleList = DbDynMemCtxAlloc(memctx, sizeof(DbListT));
    if (tempTable->deleteTupleList == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_OUT_OF_MEMORY, "deleteTupleList alloc mem. labelName=%s", tempTable->vertexLabelName);
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateListWithExtendSize(tempTable->deleteTupleList, sizeof(DmVertexT *), 1, memctx);

    // init insertTupleList for inserted and updated tuples
    tempTable->insertTupleList = DbDynMemCtxAlloc(memctx, sizeof(DbListT));
    if (tempTable->insertTupleList == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_OUT_OF_MEMORY, "insertTupleList alloc mem. labelName=%s", tempTable->vertexLabelName);
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateListWithExtendSize(tempTable->insertTupleList, sizeof(DmVertexT *), 1, memctx);
    DmVertexT *vertex = *(DmVertexT **)DbListItem(tempTable->tempTableTuples, 0);
    DmValueT count = {0};
    Status ret = DmVertexGetPropeByIdNoCopy(vertex, DTL_RESERVED_COUNT_INDEX, &count);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "dmVertex dtlReservedCount, labelName=%s", tempTable->vertexLabelName);
        return ret;
    }
    // discard tuples with count = 0 when classifying delta tuples in tempTable
    if (count.value.intValue > 0) {
        ret = DbAppendListItem(tempTable->insertTupleList, &vertex);
    } else if (count.value.intValue < 0) {
        ret = DbAppendListItem(tempTable->deleteTupleList, &vertex);
    } else {
        ret = GMERR_OK;
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "vertex append to list. labelName=%s", tempTable->vertexLabelName);
        return ret;
    }
    DbClearList(tempTable->tempTableTuples);
    tempTable->updateStartIdx = DbListGetItemCnt(tempTable->insertTupleList);
    if (updIdx != NULL) {
        *updIdx = tempTable->updateStartIdx;
    }
    *done = true;
    return GMERR_OK;
}

Status TempTableClassifyMerge(TempTableT *tempTable, bool *done)
{
    DB_POINTER2(tempTable, done);
    if (tempTable->state == TEMP_MERGE_STATE_UNINIT && TempTableCount(tempTable) == 1) {
        return TempTableQuickClassifyMerge(tempTable, NULL, done);
    }
    return TempTableAllMerge(tempTable, true, done);
}

static Status IsVertexExist(DbOamapT *tempTableMergeMap, DmVertexT *dmVertex, bool *isExist, char *name, uint32_t id)
{
    const uint32_t indexId = dmVertex->vertexDesc->indexKeyBufInfos[0].indexId;
    IndexKeyT indexkey = {};
    DmGetPrimaryKeyBufFromVertex(dmVertex, dmVertex->vertexKeyBuf, &indexkey.keyLen);
    indexkey.keyData = dmVertex->vertexKeyBuf;

    Status ret = CheckIndexKeyLen(dmVertex, indexId, indexkey.keyLen);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "check idxKeyLen, table=%s", name);
        return ret;
    }
    const uint32_t hash = DbHash32(indexkey.keyData, indexkey.keyLen);
    ret = DbOamapInsert(tempTableMergeMap, hash, dmVertex, NULL, NULL);
    if (ret != GMERR_OK) {
        if (ret == GMERR_DUPLICATE_OBJECT) {
            *isExist = true;  // de-duplicate:dmVertex already exist
            return GMERR_OK;
        }
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "hashmap insert vertex, table=%s", name);
        return ret;
    }
    *isExist = false;
    return GMERR_OK;
}

static Status BuildNewReqData(DbListT *reqDataList, DmValueT upgradeVersion, DmVertexLabelT *vertexLabel,
    DmVertexT *dmVertex, DmVertexT **newVertex)
{
    // create empty DmVertex to hold tuple
    Status ret = GMERR_OK;
    if ((ret = DmCreateEmptyVertexWithMemCtx(reqDataList->memCtx, vertexLabel, newVertex)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "create vertex, labelName:%s", vertexLabel->metaCommon.metaName);
        return ret;
    }
    if ((ret = DmVertexCopy(dmVertex, *newVertex)) != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "vertex copy, labelName:%s", vertexLabel->metaCommon.metaName);
        return ret;
    }

    // upgradeVersion equal with VertexLabel's
    if ((ret = DmVertexSetPropeById(UPGRADE_VERSION_INDEX, upgradeVersion, *newVertex)) != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "set upgradeVersion, labelName=%s", vertexLabel->metaCommon.metaName);
        return ret;
    }
    return GMERR_OK;
}

Status TempTableConvertToReqDataList(TempTableT *tempTable, bool removeDup, DbListT *reqDataList, uint32_t opCode)
{
    DB_POINTER2(tempTable, reqDataList);
    uint32_t tupleCount = DbListGetItemCnt(tempTable->tempTableTuples);
    uint32_t labelId = tempTable->vertexLabelId;
    Status ret = DbOamapInit(&tempTable->mergeMap, tupleCount, VertexPkCmp, tempTable->memctx, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "tmpTable map init, tableId=%" PRIu32, labelId);
        return ret;
    }
    DmVertexLabelT *vertexLabel = NULL;
    if ((ret = CataGetVertexLabelById(
             DbGetInstanceByMemCtx(tempTable->memctx), tempTable->vertexLabelId, &vertexLabel)) != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get vertexLabel, id:(%" PRIu32 ").", labelId);
        return ret;
    }

    DmValueT upgradeVersion = {0};
    upgradeVersion.type = DB_DATATYPE_INT32;
    upgradeVersion.value.intValue = vertexLabel->commonInfo->datalogLabelInfo->upgradeInfo.upgradeVersion;

    DmVertexT *newVertex = NULL;
    // 这个循环发生错误没有直接return，而是break，是为了vertex的资源释放
    for (uint32_t i = 0; i < tupleCount; i++) {
        DmVertexT *dmVertex = *(DmVertexT **)DbListItem(tempTable->tempTableTuples, i);
        if (removeDup) {
            bool isExist = false;
            if ((ret = IsVertexExist(&tempTable->mergeMap, dmVertex, &isExist, tempTable->vertexLabelName,
                     tempTable->vertexLabelId)) != GMERR_OK) {
                break;
            }
            if (isExist) {
                continue;
            }
        }
        // create empty DmVertex to hold tuple
        if ((ret = BuildNewReqData(reqDataList, upgradeVersion, vertexLabel, dmVertex, &newVertex)) != GMERR_OK) {
            break;
        }

        ReqDataT newReqData = {.labelId = labelId, .vertex = newVertex, .opCode = opCode};
        if ((ret = DbAppendListItem(reqDataList, &newReqData)) != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "write %" PRIu32 " delta tuple, tableId=%" PRIu32, i, labelId);
            break;
        }
    }

    DbOamapDestroy(&tempTable->mergeMap);
    (void)CataReleaseVertexLabel(vertexLabel);
    return ret;
}

// cmpResult = +1 when dmVertex1.count > 0 and dmVertex2.count < 0
// cmpResult = -1 when dmVertex1.count < 0 and dmVertex2.count > 0
// cmpResult =  0 in other cases
static Status VertexCompareCountSign(DmVertexT *dmVertex1, DmVertexT *dmVertex2, int8_t *cmpResult)
{
    DB_POINTER3(dmVertex1, dmVertex2, cmpResult);

    Status ret = GMERR_OK;
    DmValueT count1, count2;
    if ((ret = DmVertexGetPropeById(dmVertex1, DTL_RESERVED_COUNT_INDEX, &count1)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "get cnt, labelName=%s", dmVertex1->vertexDesc->labelName);
        return ret;
    }
    if ((ret = DmVertexGetPropeById(dmVertex2, DTL_RESERVED_COUNT_INDEX, &count2)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "get cnt, labelName=%s", dmVertex2->vertexDesc->labelName);
        return ret;
    }
    *cmpResult = 0;
    if (count1.value.intValue > 0 && count2.value.intValue < 0) {
        *cmpResult = 1;
    }
    if (count1.value.intValue < 0 && count2.value.intValue > 0) {
        *cmpResult = -1;
    }
    return ret;
}

static Status TempTableUpdatableMergeOneProc(
    TempTableT *tempTable, DmVertexT *oldVertex, DmVertexT *currentVertex, uint32_t hash)
{
    Status ret = GMERR_OK;
    int8_t cmpResult = 0;
    ret = VertexCompareCountSign(currentVertex, oldVertex, &cmpResult);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (cmpResult == 0) {
        // 尽可能获取完整记录内容，记录存在截断的可能，优先输出主键信息
        char vertexDataBuf[LOG_LENGTH_MAX] = {0};
        ret = PrefillDmVertexDataToBuf(oldVertex, currentVertex, vertexDataBuf, LOG_LENGTH_MAX);
        if (ret != GMERR_OK) {
            // 此处不return错误码，以免覆盖主键冲突的根因
            DB_LOG_ERROR(ret, "log space full, labelName=%s", tempTable->vertexLabelName);
        }
        DB_LOG_ERROR(GMERR_PRIMARY_KEY_VIOLATION, "PK unique violation, labelName=%s, id=%" PRIu32 ". vertexDataBuf:%s",
            tempTable->vertexLabelName, tempTable->vertexLabelId, vertexDataBuf);
        return GMERR_PRIMARY_KEY_VIOLATION;
    }

    (void)DbOamapRemove(&tempTable->mergeMap, hash, oldVertex);
    // Let the vertex with negative count set in key and the vertex with positive count set in value
    if (cmpResult > 0) {
        ret = DbOamapInsert(&tempTable->mergeMap, hash, oldVertex, currentVertex, NULL);
    }
    if (cmpResult < 0) {
        ret = DbOamapInsert(&tempTable->mergeMap, hash, currentVertex, oldVertex, NULL);
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "mergeMap insert, tableName=%s.", tempTable->vertexLabelName);
    }
    return ret;
}

static Status TempTableUpdatableMergeOne(
    TempTableT *tempTable, DmVertexT *oldVertex, DmVertexT *currentVertex, uint32_t hash)
{
    Status ret = GMERR_OK;
    if (oldVertex == NULL) {
        // The primary key of currentVertex appears for the first time.
        ret = DbOamapInsert(&tempTable->mergeMap, hash, currentVertex, NULL, NULL);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "hashmap insert key");
        }
        return ret;
    }

    if (DbOamapLookup(&tempTable->mergeMap, hash, currentVertex, NULL) == NULL) {
        // The primary key of currentVertex appears for the second time.
        return TempTableUpdatableMergeOneProc(tempTable, oldVertex, currentVertex, hash);
    }

    // The primary key of currentVertex appears for the third time.
    DB_LOG_ERROR(GMERR_PRIMARY_KEY_VIOLATION, "PK unique violation, labelName=%s, id=%" PRIi32,
        tempTable->vertexLabelName, tempTable->vertexLabelId);
    return GMERR_PRIMARY_KEY_VIOLATION;
}

Status TempTableUpdatableMergeCallBack(TempTableT *tempTable, DmVertexT *currentVertex)
{
    DB_POINTER2(tempTable, currentVertex);
    IndexKeyT indexkey = {0};
    uint32_t indexId = currentVertex->vertexDesc->indexKeyBufInfos[0].indexId;
    Status ret = DmGetKeyBufFromVertex(currentVertex, indexId, &indexkey.keyData, &indexkey.keyLen);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get keyBuf, labelName=%s", tempTable->vertexLabelName);
        return ret;
    }

    ret = CheckIndexKeyLen(currentVertex, indexId, indexkey.keyLen);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t hash = DbHash32(indexkey.keyData, indexkey.keyLen);

    DmVertexT *oldVertex = (DmVertexT *)DbOamapLookupKey(&tempTable->mergeMap, hash, currentVertex);
    return TempTableUpdatableMergeOne(tempTable, oldVertex, currentVertex, hash);
}

/*
 * ============================ function for updatable temp table end================================================
 */

Status TempTableAddToPriQueue(
    DbInstanceHdT dbInstance, TempTableT *tempTable, bool usePreTopoSortId, PriorityQueueT *dtlTableQueue)
{
    DB_POINTER2(dtlTableQueue, tempTable);

    // if tempTable is in PriorityQueue , no need to put again
    if (TempTableInPriQueue(tempTable)) {
        return GMERR_OK;
    }

    DmVertexLabelT *vertexLabel = NULL;
    Status ret = CataGetVertexLabelById(dbInstance, tempTable->vertexLabelId, &vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get label by id(%" PRIu32 ")", tempTable->vertexLabelId);
        return ret;
    }

    RelatedTblT relatedTbl = {
        .tblType = RW_TYPE_WRITE,
        .sortId = (uint32_t)(usePreTopoSortId ? vertexLabel->commonInfo->datalogLabelInfo->preTopoSortId :
                                                vertexLabel->commonInfo->datalogLabelInfo->topoSortId),
        .tableId = tempTable->vertexLabelId,
        .inOutType = vertexLabel->commonInfo->datalogLabelInfo->inoutType,
    };
    (void)CataReleaseVertexLabel(vertexLabel);

    ret = DbPriorityQueuePush(dtlTableQueue, (void *)&relatedTbl, sizeof(relatedTbl));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "priority queue push table, tableName=%s.", tempTable->vertexLabelName);
        return ret;
    }
    TempTableSetInPriQueue(tempTable, true);
    return GMERR_OK;
}

static Status CollectUpdateTuples(TempTableT *tempTable, bool ignoreDelete)
{
    DB_POINTER(tempTable);
    Status ret = GMERR_OK;

    uint64_t startTime = DbClockGetTsc();
    void *key = NULL;
    void *value = NULL;
    while (DbOamapFetch(&tempTable->mergeMap, &tempTable->mapIter, &key, &value) == GMERR_OK) {
        if (value == NULL) {
            continue;
        }
        bool equal = false;
        ret = DmVertexEqual((const DmVertexT *)key, (const DmVertexT *)value, false, &equal);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "compare dmvertex, tableName=%s.", tempTable->vertexLabelName);
            return ret;
        }
        // ignore update tuples while all field are equal
        if (equal) {
            continue;
        }
        if (!ignoreDelete) {
            ret = DbAppendListItem(tempTable->insertTupleList, (void *)&key);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR_AND_SET_LASTERR(ret, "append vertex, tableName=%s.", tempTable->vertexLabelName);
                return ret;
            }
        }
        ret = DbAppendListItem(tempTable->insertTupleList, (void *)&value);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "append vertex, tableName=%s.", tempTable->vertexLabelName);
            return ret;
        }
        if (DbExceedTime(startTime, TEMP_CLASSIFY_EXCEED)) {
            return GMERR_OK;
        }
    }
    tempTable->classifyState = TEMP_CLASSIFY_STATE_WRITE_DONE;
    return ret;
}

static Status CollectDeleteAndInsertTuplesFormMergeMap(TempTableT *tempTable, uint32_t *updIdx)
{
    DB_POINTER(tempTable);
    Status ret = GMERR_OK;

    uint64_t startTime = DbClockGetTsc();
    void *key = NULL;
    void *value = NULL;
    while (DbOamapFetch(&tempTable->mergeMap, &tempTable->mapIter, &key, &value) == GMERR_OK) {
        if (value != NULL) {
            continue;
        }
        DmValueT count = {0};
        count.type = DB_DATATYPE_INT32;
        if ((ret = DmVertexGetPropeById((DmVertexT *)key, DTL_RESERVED_COUNT_INDEX, &count)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "get vertex cnt, labelName=%s, labelId=%" PRIu32, tempTable->vertexLabelName,
                tempTable->vertexLabelId);
            return ret;
        }
        // 归纳删除数据
        if (count.value.intValue < 0) {
            ret = DbAppendListItem(tempTable->deleteTupleList, (void *)&key);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR_AND_SET_LASTERR(ret, "append vertex, tableName=%s.", tempTable->vertexLabelName);
                return ret;
            }
        }
        // 归纳插入数据
        if (count.value.intValue > 0) {
            ret = DbAppendListItem(tempTable->insertTupleList, (void *)&key);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR_AND_SET_LASTERR(ret, "append vertex, tableName=%s.", tempTable->vertexLabelName);
                return ret;
            }
        }

        if (DbExceedTime(startTime, TEMP_CLASSIFY_EXCEED)) {
            return GMERR_OK;
        }
    }
    DbOamapResetIterator(&tempTable->mapIter);
    tempTable->updateStartIdx = DbListGetItemCnt(tempTable->insertTupleList);
    *updIdx = tempTable->updateStartIdx;
    tempTable->classifyState = TEMP_CLASSIFY_STATE_WRITE_UPDATE;
    return ret;
}

static Status TempTableMergeMapInit(TempTableT *tempTable)
{
    DbMemCtxT *memctx = tempTable->memctx;
    // memory will be freed unitied , see Note1
    Status ret =
        DbOamapInit(&tempTable->mergeMap, DbListGetItemCnt(tempTable->tempTableTuples), VertexPkCmp, memctx, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "mergeMap init, tbleName:%s", tempTable->vertexLabelName);
        return ret;
    }
    DbOamapResetIterator(&tempTable->mapIter);
    tempTable->mergeCursor = 0;
    tempTable->classifyState = TEMP_CLASSIFY_STATE_CLASSIFY;
    return ret;
}
static Status TempTableClassifyInit(TempTableT *tempTable)
{
    DB_POINTER(tempTable);
    DbMemCtxT *memctx = tempTable->memctx;
    uint32_t tupleCnt = TempTableCount(tempTable);

    // memory will be freed unitied , see Note1
    // create deleteTupleList holding deleted DmVertexT
    tempTable->deleteTupleList = DbDynMemCtxAlloc(memctx, sizeof(DbListT));
    if (tempTable->deleteTupleList == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "deleteTupleList alloc mem");
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateListWithExtendSize(tempTable->deleteTupleList, sizeof(DmVertexT *), 1 + (tupleCnt >> 1), memctx);

    // memory will be freed unitied , see Note1
    // create insertTupleList holding inserted and updated DmVertexT
    tempTable->insertTupleList = DbDynMemCtxAlloc(memctx, sizeof(DbListT));
    if (tempTable->insertTupleList == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "insertTupleList alloc mem");
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateListWithExtendSize(tempTable->insertTupleList, sizeof(DmVertexT *), 1 + (tupleCnt >> 1), memctx);

    // create mergeMap for updated DmVertexT
    return TempTableMergeMapInit(tempTable);
}

static Status TempTableClassify(TempTableT *tempTable)
{
    uint64_t startTime = DbClockGetTsc();
    uint32_t size = TempTableCount(tempTable);
    DmValueT count = {0};
    count.type = DB_DATATYPE_INT32;

    Status ret = GMERR_OK;
    // 遍历区分更新数据
    for (uint32_t i = tempTable->mergeCursor; i < size; i++) {
        DmVertexT *dmVertex = *(void **)DbListItem(tempTable->tempTableTuples, i);

        if ((ret = DmVertexGetPropeById(dmVertex, DTL_RESERVED_COUNT_INDEX, &count)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "get vertex cnt,labelName=%s, labelId=%" PRIu32, tempTable->vertexLabelName,
                tempTable->vertexLabelId);
            return ret;
        }
        if (count.value.intValue == 0) {
            continue;
        }

        ret = TempTableUpdatableMergeCallBack(tempTable, dmVertex);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (DbExceedTime(startTime, TEMP_CLASSIFY_EXCEED)) {
            tempTable->classifyState = TEMP_CLASSIFY_STATE_CLASSIFY;
            tempTable->mergeCursor = i + 1;
            return GMERR_OK;
        }
    }
    DbClearList(tempTable->tempTableTuples);
    tempTable->mergeCursor = 0;
    tempTable->classifyState = TEMP_CLASSIFY_STATE_WRITE_DELELE_AND_INSERT;
    return ret;
}

static Status TempTableUpdatableClassify(TempTableT *tempTable, bool ignoreDeleteInUpdate, uint32_t *updIdx, bool *done)
{
    DB_POINTER2(tempTable, done);
    Status ret = GMERR_OK;
    TempClassifyStateE oldClassifyState = TEMP_CLASSIFY_STATE_UNINIT;
    do {
        oldClassifyState = (TempClassifyStateE)tempTable->classifyState;
        switch ((TempClassifyStateE)tempTable->classifyState) {
            case TEMP_CLASSIFY_STATE_UNINIT:
                ret = TempTableClassifyInit(tempTable);
                break;
            case TEMP_CLASSIFY_STATE_CLASSIFY:
                ret = TempTableClassify(tempTable);
                break;
            case TEMP_CLASSIFY_STATE_WRITE_DELELE_AND_INSERT:
                ret = CollectDeleteAndInsertTuplesFormMergeMap(tempTable, updIdx);
                break;
            case TEMP_CLASSIFY_STATE_WRITE_UPDATE:
                ret = CollectUpdateTuples(tempTable, ignoreDeleteInUpdate);
                break;
            case TEMP_CLASSIFY_STATE_WRITE_DONE:
                *done = true;
                tempTable->writeDataType = WRITE_DATA_TYPE_DELETE;
                DbOamapDestroy(&tempTable->mergeMap);
                return GMERR_OK;
            default:
                ret = GMERR_DATA_EXCEPTION;
                DB_LOG_ERROR_AND_SET_LASTERR(ret, "state=%" PRIu32 ", tableName=%s", (uint32_t)tempTable->classifyState,
                    tempTable->vertexLabelName);
                return ret;
        }
    } while ((uint32_t)oldClassifyState != (uint32_t)tempTable->classifyState);
    *done = false;
    return ret;
}

Status TempTableUpdatableClassifyMerge(TempTableT *tempTable, bool ignoreDeleteInUpdate, uint32_t *updIdx, bool *done)
{
    Status ret = GMERR_OK;
    if (!tempTable->mergeDone) {
        // Combine all tuples with the same fields
        // example: [{1,1,-1}, {1,1, +1}, {1,2,+1}] -> [{1,1,0},{1,2,+1}]
        // the tuple with zero count will be ignored in TempTableUpdatableClassify
        ret = TempTableMerge(tempTable, &tempTable->mergeDone);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (!tempTable->mergeDone) {
        return ret;
    }

    if (tempTable->classifyState == TEMP_CLASSIFY_STATE_UNINIT && TempTableCount(tempTable) == 1) {
        tempTable->writeDataType = WRITE_DATA_TYPE_DELETE;
        return TempTableQuickClassifyMerge(tempTable, updIdx, done);
    }
    // separate DeltaTable to two parts : [deleted tuples] , [inserted and updated tuples]
    return TempTableUpdatableClassify(tempTable, ignoreDeleteInUpdate, updIdx, done);
}

#ifdef __cplusplus
}
#endif
