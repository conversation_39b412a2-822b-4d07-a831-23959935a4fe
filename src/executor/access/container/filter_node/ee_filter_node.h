/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: header file for filter node
 * Author:
 * Create: 2024-07-21
 */

#ifndef EE_FILTER_NODE_H
#define EE_FILTER_NODE_H
#include "db_mem_context.h"
#include "db_tuple_buffer.h"
#include "db_hashmap.h"
#include "dm_data_prop_seri.h"
#include "ee_associative_array_slot.h"
#include "dm_yang_common.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct AddrArray AddrArrayT;

#pragma pack(1)
typedef struct YangFilterNodeKey {
    uint64_t vertexAddr;
    uint32_t vertexLabelId;
    uint32_t pid;
    uint32_t id;
    uint32_t propId;
    uint16_t uniqueId;
} YangFilterNodeKeyT;
#pragma pack()

typedef struct YangFilterNodeCtx {
    DbMemCtxT *memCtx;
    DbOamapT map;  // key：YangDeltaTupleKeyT,Value：YangDeltaTupleT
} YangFilterNodeCtxT;

Status YangCreateFilterNodeCtx(DbMemCtxT *memCtx, YangFilterNodeCtxT **dhc);
void YangDestroyFilterNodeCtx(YangFilterNodeCtxT *dhc);
Status YangFilterNodeInsert(YangFilterNodeCtxT *dhc, AASlotT *slot, uint16_t uniqueNodeId, uint32_t propId);
Status YangFilterNodeFetch(
    YangFilterNodeCtxT *dhc, AASlotT *slot, uint16_t uniqueNodeId, uint32_t propId, bool *visible);

static inline Status YangVertexCheckFilter(YangFilterNodeCtxT *ctx, AASlotT *slot, bool *visible)
{
    if (ctx == NULL) {
        *visible = true;
        return GMERR_OK;
    }

    return YangFilterNodeFetch(ctx, slot, DM_YANG_TREE_NODE_IS_VERTEX, DB_MAX_UINT32, visible);
}

static inline Status YangNodeCheckFilter(YangFilterNodeCtxT *ctx, AASlotT *slot, uint16_t uniqueNodeId, bool *visible)
{
    if (ctx == NULL) {
        *visible = true;
        return GMERR_OK;
    }

    return YangFilterNodeFetch(ctx, slot, uniqueNodeId, DB_MAX_UINT32, visible);
}

static inline Status YangPropCheckFilter(
    YangFilterNodeCtxT *ctx, AASlotT *slot, uint16_t uniqueNodeId, uint32_t propId, bool *visible)
{
    if (ctx == NULL) {
        *visible = true;
        return GMERR_OK;
    }

    return YangFilterNodeFetch(ctx, slot, uniqueNodeId, propId, visible);
}

#ifdef __cplusplus
}
#endif

#endif  // EE_FILTER_NODE_H
