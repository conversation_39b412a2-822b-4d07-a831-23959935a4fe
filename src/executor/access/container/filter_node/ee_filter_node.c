/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: source file for delta heap
 * Author:
 * Create: 2024-07-21
 */

#include "ee_filter_node.h"
#include "ee_plan_state.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

uint32_t YangDeltaTupleKeyCmp(const void *key1, const void *key2)
{
    DB_POINTER2(key1, key2);
    return memcmp(key1, key2, sizeof(YangFilterNodeKeyT)) == 0;
}

Status YangCreateFilterNodeCtx(DbMemCtxT *memCtx, YangFilterNodeCtxT **dhc)
{
    YangFilterNodeCtxT *ctx = (YangFilterNodeCtxT *)DbDynMemCtxAlloc(memCtx, sizeof(YangFilterNodeCtxT));
    if (ctx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "delta heap alloc mem");
        return GMERR_OUT_OF_MEMORY;
    }

    *ctx = (YangFilterNodeCtxT){.memCtx = memCtx};
    Status ret = DbOamapInit(&ctx->map, 1, YangDeltaTupleKeyCmp, memCtx, true);
    if (ret != GMERR_OK) {
        YangDestroyFilterNodeCtx(ctx);
        DB_LOG_AND_SET_LASERR(ret, "init hashmap");
        return ret;
    }

    *dhc = ctx;
    return GMERR_OK;
}

void YangDestroyFilterNodeCtx(YangFilterNodeCtxT *dhc)
{
    DbOamapIteratorT it = 0;
    void *key;
    void *value;
    while (DbOamapFetch(&dhc->map, &it, &key, &value) == GMERR_OK) {
        DbDynMemCtxFree(dhc->memCtx, key);  // key 和 value 是同一块内存
    }
    DbOamapDestroy(&dhc->map);
    DbDynMemCtxFree(dhc->memCtx, dhc);
}

static Status YangGetFilterNodeKey(AASlotT *slot, YangFilterNodeKeyT *key)
{
    Status ret = GMERR_OK;
    DmValueT value = {};
    DmVertexT *vertex = slot->dmVertex;
    ret = DmVertexGetPropeByIdNoCopy(vertex, DM_YANG_ID_PROPE_SUBSCRIPT, &value);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get PID prope value. labelName:%s", vertex->vertexDesc->labelName);
        return ret;
    }

    key->vertexAddr = slot->addr;

    key->vertexLabelId = vertex->vertexDesc->labelId;
    key->id = value.value.uintValue;
    if (DmIsLeafListYangInfo(vertex->vertexDesc->yangInfoDesc)) {
        bool isSetByServer;
        bool isEqualDefault;
        ret = DmGetLeafListDefaultFlagsFromVertex(vertex, &isSetByServer, &isEqualDefault);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "leaflist dft flag. labelName=%s", vertex->vertexDesc->labelName);
            return ret;
        }
        if (isSetByServer) {
            key->id = DmYangGetLeafListDefaultValueIdx(vertex->vertexDesc, vertex);
            if (key->id == DB_INVALID_UINT32) {
                DB_LOG_ERROR(
                    GMERR_DATA_EXCEPTION, "leaflist dft value idx. labelName=%s", vertex->vertexDesc->labelName);
                return GMERR_DATA_EXCEPTION;
            }
        }
    }

    if (DmIsRootYangVertexDesc(vertex->vertexDesc)) {
        key->pid = YANG_NP_ACCESS_FIELD_U32_NULL;
        return GMERR_OK;
    }

    ret = DmVertexGetPropeByIdNoCopy(vertex, DM_YANG_PID_PROPE_SUBSCRIPT, &value);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "PID prope value. labelName=%s", vertex->vertexDesc->labelName);
        return ret;
    }
    key->pid = value.value.uintValue;
    return GMERR_OK;
}

Status YangFilterNodeFetch(
    YangFilterNodeCtxT *dhc, AASlotT *slot, uint16_t uniqueNodeId, uint32_t propId, bool *visible)
{
    Status ret = GMERR_OK;
    YangFilterNodeKeyT key = {};
    ret = YangGetFilterNodeKey(slot, &key);
    if (ret != GMERR_OK) {
        return ret;
    }

    key.uniqueId = uniqueNodeId;
    key.propId = propId;
    uint32_t hashCode = DbHash32((const uint8_t *)&key, sizeof(key));
    *visible = DbOamapLookup(&dhc->map, hashCode, &key, NULL) == NULL;

    return GMERR_OK;
}

Status YangFilterNodeInsert(YangFilterNodeCtxT *dhc, AASlotT *slot, uint16_t uniqueNodeId, uint32_t propId)
{
    Status ret = GMERR_OK;
    YangFilterNodeKeyT *key = (YangFilterNodeKeyT *)DbDynMemCtxAlloc(dhc->memCtx, sizeof(YangFilterNodeKeyT));
    if (key == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "YangDeltaTupleT alloc mem");
        return GMERR_OUT_OF_MEMORY;
    }

    *key = (YangFilterNodeKeyT){};
    ret = YangGetFilterNodeKey(slot, key);
    if (ret != GMERR_OK) {
        return ret;
    }

    key->uniqueId = uniqueNodeId;
    key->propId = propId;

    // 暂不支持覆盖
    uint32_t hashCode = DbHash32((const uint8_t *)key, sizeof(*key));
    ret = DbOamapInsert(&dhc->map, hashCode, key, key, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "insert hashmap");
        return ret;
    }

    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
