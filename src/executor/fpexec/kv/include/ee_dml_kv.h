/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: header file of kv dml operations.
 * Author: liangjialing
 * Create: 2023-06-06
 */

#ifndef EE_DML_KV_H
#define EE_DML_KV_H

#include "ee_stmt_interface.h"

#ifdef __cplusplus
extern "C" {
#endif

// kv dml entry
Status QryExecuteSetKv(QryStmtT *stmt);
Status QryExecuteDeleteKv(QryStmtT *stmt);
Status QryExecuteGetKv(QryStmtT *stmt);
Status QryExecuteScanKv(QryStmtT *stmt);
Status QryExecuteCountKv(QryStmtT *stmt);
Status QryExecuteExistKv(QryStmtT *stmt);

Status QryBuildRspForScanKv(QryStmtT *stmt, FixBufferT *rsp);

#ifdef __cplusplus
}
#endif

#endif /* EE_DML_KV_H */
