/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2021. All rights reserved.
 * Description: The entry of fastpath execution engine
 * Author: zhulixia
 * Create: 2020-08-27
 */

#include "ee_fastpath.h"
#include "ee_schedule.h"
#include "ee_dml_query.h"
#include "ee_dml_subs.h"
#include "ee_subtree.h"
#include "ee_yang_edit_common.h"
#include "ee_diff_fetch.h"
#include "ee_command.h"
#include "ee_yang_edit_node.h"
#include "ee_dcl_ctrl.h"
#include "ee_dml_kv.h"
#include "ee_dml_edge.h"
#include "ee_dml_vertex.h"
#include "ee_dql_meta.h"
#include "ee_concurrency_control.h"
#include "ee_feature_import.h"
#include "ee_merge_write_cache.h"
#include "ee_yang_export.h"
#include "ee_log.h"

static inline Status GetHandler(QryExecHandlerT *handler, QryExecFunc execFunc, CataObjTypeE type,
    DmPrivilegeE objectPriv, CataSysPrivTypeE sysPriv, bool dbaPriv)
{
    handler->execFunc = execFunc;
    handler->privChecker.dbaPriv = dbaPriv;
    handler->privChecker.objectPriv = objectPriv;
    handler->privChecker.sysPriv = sysPriv;
    handler->privChecker.objType = type;
    return GMERR_OK;
}

static Status QryGetExecutorNonCommonUsedScene(QryStmtT *stmt, QryExecHandlerT *handler)
{
    uint32_t type = (uint32_t)(stmt->context->type);
    switch (type) {
        // DQL
        case QRY_TYPE_SCAN_VERTEX:
            return GetHandler(
                handler, QryExecuteScanVertex, CATA_VERTEX_LABEL, DM_OBJ_SELECT_PRIV, SELECTANY_PRIV, false);
        case QRY_TYPE_TRIGGER_SCAN_VERTEX:
            return GetHandler(
                handler, QryExecuteTriggerFullScanVertex, CATA_OBJ_TYPE_NUM, DM_OBJ_SELECT_PRIV, SELECTANY_PRIV, false);
        case QRY_TYPE_SCAN_PATH:
            return GetHandler(handler, QryExecuteScanPath, CATA_PATH_SUBS, DB_INVALID_ID32, SELECTANY_PRIV, false);
        case QRY_TYPE_TABLESPACE_ALARM:
            return GetHandler(
                handler, QryExecuteTableSpaceAlarmDfx, CATA_VERTEX_LABEL, DM_OBJ_SELECT_PRIV, SELECTANY_PRIV, false);
        case QRY_TYPE_GET_COUNT:
            return GetHandler(
                handler, QryExecuteGetCount, CATA_OBJ_TYPE_NUM, DM_OBJ_SELECT_PRIV, SELECTANY_PRIV, false);
        case QRY_TYPE_GET_KV:
            return GetHandler(handler, QryExecuteGetKv, CATA_KV_TABLE, DM_OBJ_SELECT_PRIV, SELECTANY_PRIV, false);
        case QRY_TYPE_EXIST_KV:
            return GetHandler(handler, QryExecuteExistKv, CATA_KV_TABLE, DM_OBJ_SELECT_PRIV, SELECTANY_PRIV, false);
        case QRY_TYPE_COUNT_KV:
            return GetHandler(handler, QryExecuteCountKv, CATA_KV_TABLE, DM_OBJ_SELECT_PRIV, SELECTANY_PRIV, false);
        case QRY_TYPE_SCAN_KV:
            return GetHandler(handler, QryExecuteScanKv, CATA_KV_TABLE, DM_OBJ_SELECT_PRIV, SELECTANY_PRIV, false);
        // DML
        case QRY_TYPE_DELETE_GRAPH:
            return GetHandler(
                handler, QryExecuteDeleteVertex, CATA_VERTEX_LABEL, DM_OBJ_DELETE_PRIV, DELETEANY_PRIV, false);
        case QRY_TYPE_REMOVE_GRAPH:
            return GetHandler(
                handler, QryExecuteDeleteVertex, CATA_VERTEX_LABEL, DM_OBJ_DELETE_PRIV, DELETEANY_PRIV, false);
        case QRY_TYPE_REPLACE_GRAPH:
            return GetHandler(
                handler, QryExecuteMergRepVertex, CATA_VERTEX_LABEL, DM_OBJ_REPLACE_PRIV, REPLACEANY_PRIV, false);
        case QRY_TYPE_SET_KV:
            return GetHandler(handler, QryExecuteSetKv, CATA_KV_TABLE, DM_OBJ_REPLACE_PRIV, REPLACEANY_PRIV, false);
        case QRY_TYPE_DELETE_KV:
            return GetHandler(handler, QryExecuteDeleteKv, CATA_KV_TABLE, DM_OBJ_DELETE_PRIV, DELETEANY_PRIV, false);
        case QRY_TYPE_INSERT_EDGE:
            return GetHandler(handler, QryExecuteInsertEdge, CATA_EDGE_LABEL, DB_INVALID_ID32, INSERTANY_PRIV, false);
        case QRY_TYPE_DELETE_EDGE:
            return GetHandler(handler, QryExecuteDeleteEdge, CATA_EDGE_LABEL, DB_INVALID_ID32, DELETEANY_PRIV, false);
        case QRY_TYPE_UPDATE_CHECK_VERSION:
            return GetHandler(
                handler, QryExecuteUpdateCheckVersion, CATA_VERTEX_LABEL, DM_OBJ_UPDATE_PRIV, UPDATEANY_PRIV, false);
        case QRY_TYPE_CHECK_REPLACE:
            return GetHandler(
                handler, QryExecuteCheckReplace, CATA_VERTEX_LABEL, DM_OBJ_REPLACE_PRIV, REPLACEANY_PRIV, false);
#ifdef FEATURE_VLIVF
        case QRY_TYPE_LOAD_INDEX:
            return GetHandler(
                handler, QryExecuteLoadIndex, CATA_VERTEX_LABEL, DM_OBJ_INSERT_PRIV, INSERTANY_PRIV, false);
#endif
        default:
            DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "inv opcode.");
            return GMERR_DATATYPE_MISMATCH;
    }
}

// 顺序需和qry_context.h里的QryType保持一致
// 并发方案：不涉及并发，资源只读
static Status QryGetExecutor(QryStmtT *stmt, QryExecHandlerT *handler)
{
    // 性能关键路径，将性能相关操作放在第一层，非性能相关操作放在default中
    uint32_t type = (uint32_t)(stmt->context->type);
    switch (type) {
        // DML
        case QRY_TYPE_INSERT_VERTEX:
            return GetHandler(
                handler, QryExecuteInsertVertex, CATA_VERTEX_LABEL, DM_OBJ_INSERT_PRIV, INSERTANY_PRIV, false);
        case QRY_TYPE_UPDATE_VERTEX:
            return GetHandler(
                handler, QryExecuteUpdateVertex, CATA_VERTEX_LABEL, DM_OBJ_UPDATE_PRIV, UPDATEANY_PRIV, false);
        case QRY_TYPE_DELETE_VERTEX:
            return GetHandler(
                handler, QryExecuteDeleteVertex, CATA_VERTEX_LABEL, DM_OBJ_DELETE_PRIV, DELETEANY_PRIV, false);
        case QRY_TYPE_REPLACE_VERTEX:
            return GetHandler(
                handler, QryExecuteMergRepVertex, CATA_VERTEX_LABEL, DM_OBJ_REPLACE_PRIV, REPLACEANY_PRIV, false);
        case QRY_TYPE_MERGE_VERTEX:
            return GetHandler(
                handler, QryExecuteMergRepVertex, CATA_VERTEX_LABEL, DM_OBJ_MERGE_PRIV, MERGEANY_PRIV, false);
        default:
            return QryGetExecutorNonCommonUsedScene(stmt, handler);
    }
}

static Status QryExecuteBeginTx(QryStmtT *stmt)
{
    SeRunCtxHdT seRunCtx = stmt->session->seInstance;
    QryTypeE type = stmt->context->type;
    TrxCfgT trxCfg;
    Status ret = GMERR_OK;
    TrxStateE trxState = SeTransGetState(seRunCtx);
    if (SECUREC_UNLIKELY(trxState == TRX_STATE_ABORT)) {
        DB_LOG_AND_SET_LASERR(GMERR_TRANSACTION_ROLLBACK, "It needs rollback when begin trx.");
        return GMERR_TRANSACTION_ROLLBACK;
    }

    if (QryExecuteSupportTrans(stmt) && trxState == TRX_STATE_NOT_STARTED) {
        stmt->autoCommit = true;
        ret = QryExecuteBeginTrans(stmt);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }

    SeTransGetCfg(seRunCtx, &trxCfg);
    if (SECUREC_UNLIKELY(trxCfg.readOnly && QryTypeIsDML(type))) {
        DB_LOG_AND_SET_LASERR(GMERR_READ_ONLY_SQL_TRANSACTION, "The dml operation is not allowed for read-only trx.");
        return GMERR_READ_ONLY_SQL_TRANSACTION;
    }

    return GMERR_OK;
}

static inline Status QryActiveTrxCommitCheck(QryStmtT *stmt)
{
    if (SECUREC_UNLIKELY(!QryTypeIsDML(stmt->context->type))) {
        return GMERR_OK;
    }
    // 只有dml操作才能激活事务提交阶段的校验（避免显示开启可写事务，实际没做修改，读了很多表，最后提交产生冲突的问题）
    SeTransSetLabelModifiedActive(stmt->session->seInstance);
    return GMERR_OK;
}

static Status QryExecuteFirstSplit(QryStmtT *stmt, QryExecHandlerT *handler)
{
    DB_POINTER(handler);
    Status ret = GMERR_OK;
    if (SECUREC_UNLIKELY(SeTransGetState(stmt->session->seInstance) != TRX_STATE_NOT_STARTED)) {
        if (SECUREC_UNLIKELY((ret = QryLiteTrxCheck(stmt)) != GMERR_OK)) {
            return ret;
        }
    }
    if (SECUREC_UNLIKELY((ret = QryCheckPrivs(stmt, &handler->privChecker)) != GMERR_OK)) {
        DB_LOG_ERROR_ON_DEMAND(ret, "Permission Denied, type=%" PRIi32 ".", (int32_t)stmt->context->type);
        return ret;
    }
    if (SECUREC_UNLIKELY((ret = QryAllocCursor(stmt)) != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY((ret = QryExecuteBeginTx(stmt)) != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY((ret = QryActiveTrxCommitCheck(stmt)) != GMERR_OK)) {
    }
    return ret;
}

static Status QryExecuteEndTx(QryStmtT *stmt, Status opStatus)
{
    SeRunCtxHdT seRunCtx = stmt->session->seInstance;
    Status ret = opStatus;
    if (SECUREC_LIKELY(stmt->autoCommit)) {
        if (SECUREC_LIKELY(ret == GMERR_OK)) {
            if (stmt->eof) {
                ret = QryExecuteCommitTrans(stmt);
                stmt->autoCommit = false;
            }
        }
        /* In the automatic submission scenario, the submission may be unsuccessful due to message subscription. In this
           case, automatic rollback is required. Otherwise, subsequent STMT transactions cannot be started
           automatically. */
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            (void)QryExecuteRollbackTrans(stmt);
            stmt->autoCommit = false;
        }
    } else {
        if (ret != GMERR_OK) {
            SeTransAbort(seRunCtx);
        }
        if (stmt->context->isLiteTrx && QryTypeIsDQL(stmt->context->type)) {
            if (stmt->eof) {  // 全表扫描没有整个期间持有锁，但是Cursor持有
                QryCloseCursors(stmt);
            }
            QryReleaseAllLabelLatch(stmt->session);
        }
    }
    return ret;
}

Status QryExecute(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryExecHandlerT handler;
    Status ret = QryGetExecutor(stmt, &handler);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(handler.execFunc == NULL)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATATYPE_MISMATCH, "inv type when execute, type=%" PRIi32 ".", (int32_t)stmt->context->type);
        return GMERR_DATATYPE_MISMATCH;
    }
    if (stmt->splitInfo.isFirst) {
        if (SECUREC_UNLIKELY((ret = QryExecuteFirstSplit(stmt, &handler)) != GMERR_OK)) {
            QryCloseCursors(stmt);  // 确保 Cursor不残留到下次请求
            return ret;
        }
    }
    ret = handler.execFunc(stmt);
    if (SECUREC_UNLIKELY((ret = QryExecuteEndTx(stmt, ret)) != GMERR_OK)) {
        return ret;
    }
    stmt->status = STMT_STATUS_EXECUTED;

    if (!stmt->eof && (QryTypeIsDML(stmt->context->type) || QryIsYangLongOperation(stmt))) {
        // Indicates the stmt is not the last fragment and the response packet will not send.
        stmt->splitInfo.isLast = false;
        return QryAddDmlExecuteTask(stmt);
    }

    return GMERR_OK;
}

// 加锁失败后调用
static void QryDestroyCursor(QryLabelCursorT *cursor)
{
    if (cursor->cursor != NULL) {
        ContainerDestroyCursor(cursor->containerHdl, cursor->cursor);
        cursor->cursor = NULL;
    }
}

Status QryExecuteFetch(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    Status ret = GMERR_OK;

    // 聚簇容器扫描不加锁会core
    QryCursorT *cursor = (QryCursorT *)stmt->qryCursor;
    if (cursor == NULL) {
        return GMERR_OK;
    }

    QryLabelDefT *labelDef = &cursor->labelCursors[0].labelDef;
    const char *metaName = "";
    if (labelDef->labelType == VERTEX_LABEL) {
        if (labelDef->vertexLabel->metaVertexLabel->vertexLabelType != VERTEX_TYPE_SYSVIEW) {
            metaName = labelDef->vertexLabel->metaCommon.metaName;
            ret = QryAcqLatchForVertexLabel(stmt, labelDef->vertexLabel, false);
        }
    }

    if (labelDef->labelType == KV_TABLE) {
        metaName = labelDef->kvLabel->metaCommon.metaName;
        ret = QryAcqLatchForKvLabel(stmt, labelDef->kvLabel, false);
    }

    if (ret != GMERR_OK) {
        QryDestroyCursor(&cursor->labelCursors[0]);  // 只释放存储的Cursor
        QryCloseCursors(stmt);
        DB_LOG_ERROR(ret, "acquire lock, name=%s.", metaName);
        return ret;
    }

    ret = QryFetchAndMakeRsp(stmt);
    ret = QryExecuteEndTx(stmt, ret);
    if (ret == GMERR_OK) {
        stmt->status = STMT_STATUS_FETCH;
    }

    return ret;
}

Status QryProcessFetch(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    stmt->phase = QRY_PHASE_FETCH;
    if (stmt->status < STMT_STATUS_EXECUTED) {
        DB_LOG_AND_SET_LASERR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "inv stmt status when fetch cypher.");
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }

    return QryExecuteFetch(stmt);
}

Status QryProcessSysNotify(SessionT *session)
{
    DB_POINTER(session);
    return QryExecuteSubsNotify(session);
}

void FastPathExecutorFuncRegister(void)
{
    ImportFastPathFunctionsT fastPathFunctions = {NULL};
    RegQryExecuteIntrinsicFunc4Datalog(&fastPathFunctions);
#ifdef FEATURE_YANG
    RegQryBuildErrorPath4Namespace(&fastPathFunctions);
    RegQryDiffSavePointFunctions(&fastPathFunctions);
    RegQryExecuteImportDataFunctions(&fastPathFunctions);
    RegQryExecuteExportDataFunctions(&fastPathFunctions);
#endif
    RegAgedTaskFunctions(&fastPathFunctions);
    RegQryAllocTaskFromPubSubGcMgr(&fastPathFunctions);
    RegQryEmplacePubSubGcTask(&fastPathFunctions);
    RegQryFreeTaskFromPubSubGcMgr(&fastPathFunctions);
    RegQryGetPubSubGcMgr(&fastPathFunctions);
    RegQryFullScanBgTaskInterface(&fastPathFunctions);
    RegDegradeTaskFunctions(&fastPathFunctions);
    RegPersistentTaskFunctions(&fastPathFunctions);
    RegQryDropResInterface(&fastPathFunctions);
    RegWriteCacheMgrFunctions(&fastPathFunctions);
    QrySetFastPathFunctions(&fastPathFunctions);
}
