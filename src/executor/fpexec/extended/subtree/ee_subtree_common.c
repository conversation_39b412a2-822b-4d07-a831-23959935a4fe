/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementation of subtree filter
 * Author: linhuabin
 * Create: 2022-07-21
 */

#include "dm_yang_interface.h"
#include "gmc_yang_types.h"
#include "ee_subtree_path.h"
#include "ee_yang_edit_common.h"
#include "ee_non_presence_op.h"
#include "ee_yang_access_method.h"
#include "ee_subtree_common.h"

#ifdef __cplusplus
extern "C" {
#endif

bool QryIsReachMaxDepth(QryStmtT *stmt, uint32_t currLevel)
{
    QrySubtreeFilterItemT *currFilter = ((QrySubtreeFilterDescT *)stmt->context->entry)->currFilter;
    if (currFilter == NULL) {
        return true;
    }
    return currFilter->maxDepth != 0 && currFilter->maxDepth <= currLevel;
}

#ifdef YANG_SUPPORT_GRAPH
bool IsCouldReuseVertex(QryStmtT *stmt)
{
    QrySubtreeFilterItemT *currFilter = ((QrySubtreeFilterDescT *)stmt->context->entry)->currFilter;
    // 配置属性过滤会在subtree主过滤查询过滤完时进行减枝，所以不能复用vertex
    if (currFilter->configFilter != GMC_SUBTREE_FILTER_DEFAULT) {
        return false;
    }

    // 默认值查询report-all会对存储层捞取结果增加字段信息，当前普通字段的默认值是不落存储的，leaflist默认值是落存储的，但是查询过程一定不会复用vertex
    // trim会对存储层捞取结果减枝，所以不复用vertex
    if (currFilter->defaultMode == GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED ||
        currFilter->defaultMode == GMC_DEFAULT_FILTER_REPORT_ALL ||
        currFilter->defaultMode == GMC_DEFAULT_FILTER_TRIM) {
        return false;
    }

    // 因为带深度查询最后要清理掉查到的最后一级数据，需要重新序列化，不能复用vertex，后续考虑优化
    if (currFilter->maxDepth != 0) {
        return false;
    }

    return true;
}
#endif

static Status LabelBeginScanCursor(QryLabelCursorT *labelCursor, DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(labelCursor, vertexLabel);
    HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false,
        .isToGetOldestVisibleBuf = false,
        .beginAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR,
        .beginAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID,
        .beginAddr.pageBlockId = DB_INVALID_UINT32,
        .beginAddr.pageDeviceId = DB_INVALID_UINT32,
        .maxFetchNum = 1,
        .heapDowngradeInfo = HeapLabelDowngradeInfoInit(),
        .heapLabelLatchInfoForClt = HeapLabelLatchInfoInit(
            vertexLabel->commonInfo->vertexLabelLatchVersionId, vertexLabel->commonInfo->labelLatchShmAddr)};
    return HeapLabelBeginScanHpTupleBuffer(
        labelCursor->hpRunHdl, &beginScanCfg, (HeapScanCursorHdlT *)&labelCursor->cursor);
}

static Status QryFetchVertexByScanInner(
    QryStmtT *stmt, QryLabelT *qryLabel, QryLabelCursorT *labelCursor, bool isReuse, DmVertexT **vertex)
{
    DB_POINTER4(stmt, qryLabel, labelCursor, vertex);
    DmVertexLabelT *vertexLabel = qryLabel->def.vertexLabel;
    Status ret = LabelBeginScanCursor(labelCursor, vertexLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    do {
        HpFetchedAuxInfoT auxInfo = {};
        HeapFetchedRowInfoT fetchedRowInfo = {};
        ret = HeapFetchNextHpTupleBuffers(
            labelCursor->hpRunHdl, labelCursor->cursor, labelCursor->tupleBuf, &fetchedRowInfo, &auxInfo);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            break;
        }
        labelCursor->heapTuple.heapTupleBuf = TupleBufGet(labelCursor->tupleBuf);
        // 查询起点有且只有一个
        if (auxInfo.actualFetchRowsCnt != 1) {
            break;
        }

        labelCursor->addr = auxInfo.fetchRowTupleAddr;
        if (isReuse) {
            DmVertexT *reuseVertex = qryLabel->vertexContainer->emptyObjNode->vertex;
            ret = DmResetVertex(reuseVertex);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                break;
            }
            ret = DmDeSerialize2ExistsVertex(labelCursor->heapTuple.heapTupleBuf.buf,
                labelCursor->heapTuple.heapTupleBuf.bufSize, reuseVertex, DmGetCheckMode());
            *vertex = (ret == GMERR_OK) ? reuseVertex : NULL;
        } else {
            ret = DmDeSerializeVertexWithMemCtx((DbMemCtxT *)stmt->memCtx, labelCursor->heapTuple.heapTupleBuf.buf,
                labelCursor->heapTuple.heapTupleBuf.bufSize, vertexLabel, vertex);
        }
    } while (0);

    HeapLabelEndScan(labelCursor->hpRunHdl, labelCursor->cursor);
    labelCursor->cursor = NULL;
    return ret;
}

Status QryFetchVertexByScan(
    QryStmtT *stmt, QryLabelT *qryLabel, DmVertexT *filter, bool isReuse, DmVertexT **vertex, uint64_t *addr)
{
    DB_POINTER3(stmt, filter, vertex);

    // 查询起始点是List类型, 必须提供主键, 不能扫描获取
    if (DmIsListVertexDesc(filter->vertexDesc)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_INVALID_PROPERTY, "List vertex no key, vertexName=%s", filter->vertexDesc->labelName);
        return GMERR_INVALID_PROPERTY;
    }

    QryLabelCursorT *labelCursor = &((QryCursorT *)stmt->qryCursor)->labelCursors[0];
    labelCursor->action = HEAP_OPTYPE_NORMALREAD;

    Status ret = QryOpenVertexLabelCursor(stmt, qryLabel, labelCursor, true);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "open vLabelCursor, vertexName=%s", filter->vertexDesc->labelName);
        return ret;
    }

    // 从label中扫描获取点
    ret = QryFetchVertexByScanInner(stmt, qryLabel, labelCursor, isReuse, vertex);
    *addr = labelCursor->addr;
    QryCloseVertexLabelCursor(labelCursor);
    return ret;
}

#ifdef YANG_SUPPORT_GRAPH
Status QryFetchVertexByFilter(QryStmtT *stmt, QryLabelT *qryLabel, DmVertexT *filter, DmVertexT **vertex)
{
    DB_POINTER4(stmt, qryLabel, filter, vertex);
    Status ret;
    uint64_t addr = 0;
    if (filter->vertexDesc->hasPkIdx == true && DmVertexPkPropeIsAllSet(filter)) {
        // 设置了所有主键字段的情况下，直接通过主键查点
        ret = QryFetchVertexByPk(stmt, qryLabel, filter, false, vertex);
    } else if (DmIsRootYangVertexDesc(filter->vertexDesc)) {
        ret = QryFetchVertexByScan(stmt, qryLabel, filter, false, vertex, &addr);
    } else {
        ret = GMERR_INVALID_PARAMETER_VALUE;
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "fetch vertex, vertexName=%s", filter->vertexDesc->labelName);
        return ret;
    }
    return GMERR_OK;
}

Status QryFetchChildVertexByFilter(
    QryStmtT *stmt, DmVertexT *currVertex, QryEdgeLabelT *qryEdgeLabel, DmVertexT *filter, DmVertexT **vertex)
{
    DB_POINTER4(stmt, qryEdgeLabel, filter, vertex);
    if (filter->vertexDesc->hasPkIdx == true && DmVertexPkPropeIsAllSet(filter)) {
        // 设置了所有主键字段的情况下，直接通过主键查孩子点
        return QryFetchVertexByPk(stmt, qryEdgeLabel->destQryLabel, filter, false, vertex);
    }
    // 没有主键信息 通过边获取父节点的第一个孩子
    qryEdgeLabel->type = DEST_VERTEX_ADDR;
    return QryFetchRelatedVertex(stmt, currVertex, qryEdgeLabel, true, vertex);
}

static bool DefaultSchemaIsValid(DmSchemaT *schema)
{
    DB_POINTER(schema);
    // 如果当前vertex是leaflist，则都不构造默认vertex，会在后续流程中构造
    if (DmIsListSchema(schema) || DmIsLeafListSchema(schema)) {
        return false;
    }
    if ((DmIsCaseSchema(schema) && schema->yangInfo->isDefault == 0) ||
        (DmIsChoiceSchema(schema) && schema->yangInfo->isNullable == 0)) {
        return false;
    }
    return !DmIsPresenceSchema(schema);
}

Status QryProcessRootDefaultVertex(QryStmtT *stmt, QryLabelT *qryLabel, DmVertexT **vertex, bool *isDefault)
{
    DB_POINTER4(stmt, qryLabel, vertex, isDefault);

    if (*vertex != NULL) {
        return GMERR_OK;
    }
    // 如果从Label中没有扫描到点，根据默认值查询模式判断是否需要创建空的vertex，提取默认值数据
    QrySubtreeFilterItemT *currFilter = ((QrySubtreeFilterDescT *)stmt->context->entry)->currFilter;
    if (!(currFilter->defaultMode == DM_DEFAULT_FILTER_REPORT_ALL_TAGGED ||
            currFilter->defaultMode == DM_DEFAULT_FILTER_REPORT_ALL)) {
        return GMERR_OK;
    }

    DmVertexLabelT *vertexLabel = qryLabel->def.vertexLabel;
    // 对 presence 类型的 container，如果实体数据不存在，不创建默认值 vertex，直接返回
    if (DmIsPresenceVertexLabel(qryLabel->def.vertexLabel)) {
        return GMERR_OK;
    }

    *isDefault = true;
    return DmCreateEmptyVertexWithMemCtx((DbMemCtxT *)stmt->memCtx, vertexLabel, vertex);
}

Status QryProcessDefaultNode(
    QryStmtT *stmt, const DmSubtreeT *parent, DmNodeT *node, DmNodeSchemaT *childNodeSchema, bool *isDefault)
{
    DB_POINTER4(stmt, parent, node, isDefault);

    if (node->isCreated) {
        return GMERR_OK;
    }

    QrySubtreeFilterItemT *currFilter = ((QrySubtreeFilterDescT *)stmt->context->entry)->currFilter;
    if (!(currFilter->defaultMode == DM_DEFAULT_FILTER_REPORT_ALL_TAGGED ||
            currFilter->defaultMode == DM_DEFAULT_FILTER_REPORT_ALL)) {
        return GMERR_OK;
    }

    DmSchemaT *schema = childNodeSchema->schema;
    if (!DefaultSchemaIsValid(schema)) {
        return GMERR_OK;
    }

    if (!DmSchemaHasValidWhenClause(schema)) {
        *isDefault = true;
        return DmVectorNodeInit(node);
    }
    // 如果该 node 有 when 条件定义，需要看 when 条件是否满足，满足才生成默认 node
    bool isNewDefaultVisible;
    ret = QryDmVertexNpaVisibleByIndex(parent->origVertex, schema->yangInfo->npaIndex, &isNewDefaultVisible);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (!isNewDefaultVisible) {
        return GMERR_OK;  // when 条件不满足，不用生成默认值
    }
    *isDefault = true;
    return DmVectorNodeInit(node);
}

Status QryProcessDefaultVertex(
    QryStmtT *stmt, const DmSubtreeT *parent, DmVertexLabelT *childLabel, DmVertexT **vertex, bool *isDefault)
{
    DB_POINTER5(stmt, parent, childLabel, vertex, isDefault);
    if (*vertex != NULL) {
        return GMERR_OK;
    }

    QrySubtreeFilterItemT *currFilter = ((QrySubtreeFilterDescT *)stmt->context->entry)->currFilter;
    if (currFilter->defaultMode == DM_DEFAULT_FILTER_REPORT_ALL_TAGGED ||
        currFilter->defaultMode == DM_DEFAULT_FILTER_REPORT_ALL) {
        if (!DefaultSchemaIsValid(childLabel->metaVertexLabel->schema)) {
            return GMERR_OK;
        }

        // 默认值有效且在REPORT_ALL_TAGGED过滤模式下则还要尝试提取默认值
        Status ret = DmCreateEmptyVertexWithMemCtx((DbMemCtxT *)stmt->memCtx, childLabel, vertex);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        ret = DmSetChildVertexPid(parent->origVertex, *vertex);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        *isDefault = true;
    }
    return GMERR_OK;
}

bool IsDefaultCase(DmTreeDescT *desc)
{
    if (desc->type == DM_TREE_DESC_VERTEX_CHILD) {
        return false;
    }
    DmYangInfoT *info = DmTreeDescGetSchema(desc)->yangInfo;
    if (info == NULL) {
        return false;
    }
    return info->isDefault;
}

// 判断leafList是否含有用户设置的数据
static bool LeafListIsNotDefault(QryStmtT *stmt, QryEdgeLabelT *qryEdgeLabel, DmVertexT *parent)
{
    DB_POINTER3(stmt, qryEdgeLabel, parent);
    // scan取第一个点
    DmVertexT *currVertex = NULL;
    qryEdgeLabel->type = DEST_VERTEX_ADDR;
    Status ret = QryFetchRelatedVertex(stmt, parent, qryEdgeLabel, IsCouldReuseVertex(stmt), &currVertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get child leafList,Name=%s",
            qryEdgeLabel->sourceQryLabel->def.vertexLabel->metaVertexLabel->topRecordName);
        return false;
    }
    qryEdgeLabel->type = SOURCE_VERTEX_NEXT_EDGE_ADDR;
    // 点是否存在于存储层
    while (currVertex != NULL) {
        bool isSetByServer = false;
        bool isEqualDefault = false;
        ret = DmGetLeafListDefaultFlagsFromVertex(currVertex, &isSetByServer, &isEqualDefault);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get leafList flag,Name=%s", currVertex->vertexDesc->labelName);
            return false;
        }
        if (!isSetByServer) {
            return true;
        }
        // 使用nextEdge找下一个vertex
        DmVertexT *nextVertex = NULL;
        ret = QryFetchRelatedVertex(stmt, currVertex, qryEdgeLabel, IsCouldReuseVertex(stmt), &nextVertex);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get next leafList,Name=%s", currVertex->vertexDesc->labelName);
            return false;
        }
        currVertex = nextVertex;
    }
    return false;
}

bool ChoiceHasValidChildInner(QryStmtT *stmt, DmTreeDescT *desc, DmVertexT *vertex, DmNodeT *node, uint32_t index);

bool ChoiceChildHasValidChild(QryStmtT *stmt, DmTreeDescT *childDesc, DmVertexT *childVertex, DmNodeT *childNode)
{
    for (uint32_t i = 0; i < childDesc->childNum; i++) {
        if (ChoiceHasValidChildInner(stmt, childDesc, childVertex, childNode, i)) {
            return true;
        }
    }
    return false;
}

bool ChoiceHasValidChildInner(QryStmtT *stmt, DmTreeDescT *desc, DmVertexT *vertex, DmNodeT *node, uint32_t index)
{
    DB_POINTER3(stmt, vertex, node);
    QryEdgeLabelT *qryEdgeLabel = NULL;
    DmTreeDescT *childDesc = desc->childDesc[index];
    DmNodeT *childNode = NULL;
    DmVertexT *childVertex = vertex;
    DmSchemaT *schema = NULL;
    DmRecordT *record = NULL;
    if (DmIsVertexTreeDesc(childDesc)) {
        uint32_t edgeLabelId = vertex->vertexDesc->commonInfo->relatedEdgeLabels[childDesc->index].edgeLabelId;
        Status ret = QryGetQryEdgeLabelById(stmt, edgeLabelId, &qryEdgeLabel);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get child eLabel, Name=%s", vertex->vertexDesc->labelName);
            return false;
        }
        // leafList 处理
        schema = qryEdgeLabel->destQryLabel->def.vertexLabel->metaVertexLabel->schema;
        qryEdgeLabel->type = DEST_VERTEX_ADDR;
        childDesc = qryEdgeLabel->destQryLabel->desc;
        if (DmIsLeafListSchema(schema)) {
            return LeafListIsNotDefault(stmt, qryEdgeLabel, vertex);
        }
        ret = QryFetchRelatedVertex(stmt, vertex, qryEdgeLabel, IsCouldReuseVertex(stmt), &childVertex);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "fetch related vertex, Name=%s", vertex->vertexDesc->labelName);
            return false;
        }
        if (childVertex == NULL) {
            return false;
        }
        if (DmIsListSchema(schema)) {
            return true;
        }
        record = childVertex->record;
    } else {
        childNode = DmIsVertexTreeDesc(desc) ? vertex->nodes[childDesc->index] : node->currNodes[childDesc->index];
        if (!DmNodeIsCreated(childNode)) {
            return false;
        }
        record = childNode->currRecord;
        schema = childDesc->nodeSchema->schema;
    }
    if (DmIsPresenceSchema(schema) || DmRecordHasNormalPrope(schema, record, false)) {
        return true;
    }
    return ChoiceChildHasValidChild(stmt, childDesc, childVertex, childNode);
}

bool ChoiceHasValidChild(QryStmtT *stmt, DmTreeDescT *desc, DmVertexT *vertex, DmNodeT *node)
{
    if (vertex == NULL || node == NULL) {
        return false;
    }
    DmTreeDescT *childDesc = NULL;
    for (uint32_t i = 0; i < desc->childNum; i++) {
        childDesc = desc->childDesc[i];
        if (IsDefaultCase(childDesc)) {
            continue;
        }
        if (ChoiceHasValidChildInner(stmt, desc, vertex, node, i)) {
            return true;
        }
    }
    return false;
}

// 默认值节点：存在when校验且校验失败，节点需删除
// choice case, leaflist节点：作为np节点处理
// list节点：作为p节点处理
bool QrySubtreeIsValid(DmSubtreeT *tree)
{
    if (tree->hasChild) {
        return true;
    }
    DmSchemaT *schema = DmTreeDescGetSchema(tree->desc);

    DmRecordT *record = DmSubtreeGetRecord(tree);
    // 如果是一个非默认case下的case孩子分支
    if (tree->isNormalCaseChild) {
        if (DmRecordHasNormalPrope(schema, record, false)) {
            return true;
        }
        // leafList可能前面是默认值，但是又包含非默认值节点
        if (DmIsLeafListSchema(schema) && !tree->isDefault) {
            return true;
        }
        if (!DmIsPresenceSchema(schema)) {
            return false;
        }
        return true;
    }
    // 节点含有普通属性字段，保留该节点
    if (DmRecordHasNormalPrope(schema, record, true)) {
        return true;
    }

    // 空的 NP 节点不保留（list 被视为 P 节点）
    if ((!DmIsPresenceSchema(schema) && !DmIsListSchema(schema))) {
        return false;
    }
    return true;
}
#endif

bool QrySubtreeHasChild(DmSubtreeT *filter, DmSubtreeT *tree)
{
    if (filter == NULL || tree == NULL) {
        return false;
    }
    if (tree->hasChild) {
        return true;
    }
    return DmSubtreeHasNormalPrope(filter, tree);
}

#ifdef YANG_SUPPORT_GRAPH
static Status QryLeafListReserveResultTree(DmVertexT *currVertex, DmDefaultFilterModeE defaultMode, bool *isReserve)
{
    if (currVertex == NULL || !DmIsLeafListVertexDesc(currVertex->vertexDesc)) {
        return GMERR_OK;
    }
    bool isSetByServer = false;
    bool isEqualDefault = false;
    Status ret = DmGetLeafListDefaultFlagsFromVertex(currVertex, &isSetByServer, &isEqualDefault);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (defaultMode == DM_DEFAULT_FILTER_EXPLICIT) {
        // 若当前value为服务端设置的值，则不应出现在结果树中，所以保留结果树是isSetByServer的取反
        *isReserve = !isSetByServer;
    } else if (defaultMode == DM_DEFAULT_FILTER_TRIM) {
        // 若当前value为默认值，则不应该出现在结果树中，所以保留结果树是isEqualDefault的取反
        *isReserve = !isEqualDefault;
    } else {
        // report-all
        *isReserve = true;
    }
    return GMERR_OK;
}

static Status QrySetLeafListPropIsEmpty(DmSubtreeT *filter, DmDefaultFilterModeE defaultMode, DmSubtreeT *treeNode)
{
    if (!DmIsLeafListVertexDesc(filter->origVertex->vertexDesc) || defaultMode != DM_DEFAULT_FILTER_REPORT_ALL_TAGGED) {
        return GMERR_OK;
    }
    bool isSetByServer = false;
    bool isEqualDefault = false;
    Status ret = DmGetLeafListDefaultFlagsFromVertex(filter->origVertex, &isSetByServer, &isEqualDefault);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // isSetByServer = 1 的值设置 propIsEmpty[pk_index] = true
    if (isSetByServer) {
        DmSubtreeSetPropeEmptyById(DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT, treeNode);
    }
    return GMERR_OK;
}

// 如果属性有 when 条件定义，需要看 when 条件是否满足，不满足需要把之前设置的默认值去掉
Status QrySetUnReadableDefaultValues(QryStmtT *stmt, DmVertexT *vertex, DmSchemaT *schema, DmRecordT *result)
{
    DB_POINTER4(stmt, vertex, schema, result);
    bool isNewDefaultVisible;
    for (uint32_t i = 0; i < result->recordDesc->propeNum; ++i) {
        DmPropertyInfoT *currProp = &(result->recordDesc->propeInfos[i]);
        if (!currProp->isValid || currProp->isSysPrope) {
            continue;
        }

        if (result->propeIsSetValue[i] == DM_PROPERTY_IS_NOT_NULL && result->propeIsEmpty[i] == DM_PROPERTY_IS_EMPTY) {
            DmPropertySchemaT *propSchema = NULL;
            Status ret = DmSchemaGetPropeById(schema, i, &propSchema);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "get prop schema when set unreadable prop.");
                return ret;
            }
            if (!DmPropeSchemaHasValidWhenClause(propSchema)) {
                continue;
            }

            ret = QryDmVertexNpaVisibleByIndex(vertex, propSchema->npaIndex, &isNewDefaultVisible);
            if (ret != GMERR_OK) {
                return ret;
            }
            if (!isNewDefaultVisible) {
                // 字段值是默认值且被标记不可见的默认值
                result->propeIsEmpty[i] = DM_PROPERTY_IS_NOT_EMPTY;
                result->propeIsSetValue[i] = DM_PROPERTY_IS_NULL;
            }
        }
    }
    return GMERR_OK;
}

Status QryCreateTreeNodeByFilter(QryStmtT *stmt, DmSubtreeT *filter, DmSubtreeT *parent, DmSubtreeT **tree)
{
    DB_POINTER3(stmt, filter, tree);
    bool isVertex = DmIsVertexSubtree(filter);
    if (SECUREC_UNLIKELY(parent == NULL && !isVertex)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "create subtree, name:%s", DmGetSubtreeName(filter));
        return GMERR_DATA_EXCEPTION;
    }
    // 默认值过滤条件
    QrySubtreeFilterItemT *currFilter = ((QrySubtreeFilterDescT *)stmt->context->entry)->currFilter;
    DmDefaultFilterModeE defaultMode = currFilter->defaultMode;
    bool isReserve = true;
    Status ret = QryLeafListReserveResultTree(filter->origVertex, defaultMode, &isReserve);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (!isReserve) {
        *tree = NULL;
        return GMERR_OK;
    }
    // 若是list位置查询，不应返回所有属性字段
    DmFilterTypeE filterType = currFilter->isLocationFilter ? DM_FILTER_LIST_LOCATION : filter->filterType;

    DmSubtreeT *treeNode = NULL;
    if (isVertex) {
        ret = DmCreateResultSubtreeByFilter(stmt->memCtx, filter, DM_TREE_RESULT, &treeNode);
    } else {
        ret = DmCreateResultNodeSubtreeByFilter(stmt->memCtx, filter, parent, DM_TREE_RESULT, &treeNode);
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = DmCopyRecordByFilter(
        filter, filterType, defaultMode, DmSubtreeGetOrigRecord(filter), DmSubtreeGetRecord(treeNode));
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DmDestroySubtree(treeNode);
        return ret;
    }

    DmRecordT *record = DmSubtreeGetRecord(treeNode);
    ret = QrySetUnReadableDefaultValues(stmt, DmSubtreeGetVertex(treeNode), DmTreeDescGetSchema(filter->desc), record);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DmDestroySubtree(treeNode);
        return ret;
    }

    ret = QrySetLeafListPropIsEmpty(filter, defaultMode, treeNode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DmDestroySubtree(treeNode);
        return ret;
    }
    *tree = treeNode;
    return GMERR_OK;
}
#endif

// 判断从存储中获取的leaf-list记录是否能被subtree查询可见并用于后续过滤
Status QryIsLeafListFromSeVisible(
    DmVertexT *parent, DmVertexT *dmVertex, uint32_t defaultMode, DmVertexLabelT *leafListLabel, bool *isVisible)
{
    *isVisible = false;
    bool isSetByServer = false;
    bool isEqualDefault = false;
    Status ret = DmGetLeafListDefaultFlagsFromVertex(dmVertex, &isSetByServer, &isEqualDefault);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (!isSetByServer) {
        // 若为用户实例，则在Trim模式下该值等于默认值时不可见；其他情况均可见
        *isVisible = (defaultMode == DM_DEFAULT_FILTER_TRIM) ? (!isEqualDefault) : true;
        return GMERR_OK;
    }

    // 记录为系统实例
    if (defaultMode != DM_DEFAULT_FILTER_REPORT_ALL_TAGGED && defaultMode != DM_DEFAULT_FILTER_REPORT_ALL) {
        // 若为系统实例但非默认值过滤查询，则不可见
        return GMERR_OK;
    }

    // 无when定义则可见 dmVertex->vertexLabel->schema |log|
    if (!DmSchemaHasValidWhenClause(leafListLabel->metaVertexLabel->schema)) {
        *isVisible = true;
        return GMERR_OK;
    }

    // 若在NPA表有相关记录，则不可见；否则，可见
    uint32_t index = DmYangGetLeafListDefaultValueIdx(dmVertex->vertexDesc, dmVertex);
    if (index == DB_INVALID_UINT32) {
        DB_LOG_AND_SET_LASERR(
            GMERR_INVALID_PROPERTY, "vLabel(%s) is not default value.", dmVertex->vertexDesc->labelName);
        return GMERR_INVALID_PROPERTY;
    }
    uint8_t npaIndex = (uint8_t)(dmVertex->vertexDesc->yangInfoDesc->npaIndex + index);
    return QryDmVertexNpaVisibleByIndex(parent, npaIndex, isVisible);
}

static Status QryBuildLeafListSubtreeByDefaultValue(
    QryStmtT *stmt, QryEdgeLabelT *qryEdgeLabel, uint8_t defaultValueIndex, DmSubtreeT *parent, DmSubtreeT **resultTree)
{
    DB_POINTER3(stmt, qryEdgeLabel, parent);
    DmVertexT *currVertex = NULL;
    Status ret = DmCreateEmptyVertexWithMemCtx(
        (DbMemCtxT *)stmt->memCtx, qryEdgeLabel->destQryLabel->def.vertexLabel, &currVertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = DmSetChildVertexPid(parent->origVertex, currVertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    DmPropertySchemaT *propertySchema =
        qryEdgeLabel->destQryLabel->def.vertexLabel->metaVertexLabel->schema->properties;
    ret = DmVertexSetPropeById(DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT,
        propertySchema[DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT].defaultValue[defaultValueIndex], currVertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // 每个vertex构造一个subtree
    DmSubtreeT *currResultTree = NULL;
    // 释放原则：异常场景清理 stmt->memCtx 时兜底释放
    ret = DmCreateEmptySubtree(stmt->memCtx, qryEdgeLabel->destQryLabel->desc, DM_TREE_RESULT, &currResultTree);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    currVertex->isDeltaVertex = true;
    currResultTree->isDefault = true;
    currResultTree->vertex = currVertex;
    currResultTree->origVertex = currVertex;
    currResultTree->leafListIndex = defaultValueIndex;
    DmSubtreeSetPropeEmptyById(DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT, currResultTree);
    *resultTree = currResultTree;
    return GMERR_OK;
}

static Status QrySubtreeConfigFilterInner(QryStmtT *stmt, DmSubtreeT *currentTree, bool *deleteFlag)
{
    DB_POINTER3(stmt, currentTree, deleteFlag);
    bool isConfig = (QryGetSubtreeConfigFilter(stmt) == GMC_SUBTREE_FILTER_CONFIG);

    // 当前是否要删除的标记位
    *deleteFlag = true;

    DmSchemaT *schema = DmTreeDescGetSchema(currentTree->desc);

    // 过滤当前节点字段
    DmFilterPropByConfig(currentTree, schema, isConfig, deleteFlag);
    if (!*deleteFlag) {
        return GMERR_OK;
    }

    // 判断是否有孩子 （有孩子则不应删除）
    DmSubtreeT *childTree = NULL;
    for (uint32_t i = 0; i < currentTree->desc->childNum; ++i) {
        childTree = currentTree->childTree[i];
        if (childTree == NULL) {
            continue;
        }

        // 只要有一个孩子子树要保留当前父节点，则节点被保留
        *deleteFlag = false;
        break;
    }
    if (!*deleteFlag) {
        return GMERR_OK;
    }

    // 如果为相应的过滤类型且为P节点或list节点，也不删除
    if ((DmIsConfigSchema(schema) == isConfig) &&
        (DmIsPresenceSchema(schema) || DmIsListSchema(schema) || DmIsLeafListSchema(schema))) {
        *deleteFlag = false;
    }

    return GMERR_OK;
}

Status QrySubtreeConfigFilter(QryStmtT *stmt, DmSubtreeT **result)
{
    DB_POINTER2(stmt, result);
    if (*result == NULL || QryGetSubtreeConfigFilter(stmt) == GMC_SUBTREE_FILTER_DEFAULT) {
        return GMERR_OK;
    }

    Status ret = GMERR_OK;
    bool deleteFlag = false;
    if (QryGetSubtreeConfigFilter(stmt) != GMC_SUBTREE_FILTER_DEFAULT) {
        ret = QrySubtreeConfigFilterInner(stmt, *result, &deleteFlag);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (deleteFlag) {
        ret = DmDestroySubtreeAndNodeVector(*result);
        if (ret != GMERR_OK) {
            return ret;
        }
        *result = NULL;
    }
    return GMERR_OK;
}

void SetLeafListCurrResultTree(DmSubtreeT *currResultTree, DmSubtreeT **treeCursor, DmSubtreeT **resultTree)
{
    if (*treeCursor == NULL) {
        *resultTree = currResultTree;
    } else {
        (*treeCursor)->nextTree = currResultTree;
    }
    *treeCursor = currResultTree;
}

Status QryBuildAndLinkSingleLeafListDefaultTree(QryStmtT *stmt, uint8_t index, QryEdgeLabelT *qryEdgeLabel,
    DmSubtreeT *parent, DmSubtreeT **treeCursor, DmSubtreeT **resultTree)
{
    DmSubtreeT *currResultTree = NULL;
    Status ret = QryBuildLeafListSubtreeByDefaultValue(stmt, qryEdgeLabel, index, parent, &currResultTree);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t vertexNum = 0;
    uint32_t addLen = 0;
    LinkParentTree(parent, currResultTree);
    AddTreeNode(stmt, currResultTree, &vertexNum, &addLen);
    if (IsReachThreshold(stmt)) {
        QrySetPathList(stmt, NULL, currResultTree);
        // 如果达到分批条件，最后一个不加入结果树，等待下一批
        return GMERR_OK;
    }

    ret = QrySubtreeConfigFilter(stmt, &currResultTree);
    if (ret != GMERR_OK) {
        return ret;
    }

    FixTreeNode(stmt, currResultTree, vertexNum, addLen);
    if (currResultTree == NULL) {
        return GMERR_OK;
    }

    ret = DmSerializeSubtreeVertex(currResultTree);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 把新生成的结果树插到链表尾部
    SetLeafListCurrResultTree(currResultTree, treeCursor, resultTree);
    return GMERR_OK;
}

// 从index下标开始构造所有的leaflist默认值
Status QryBuildLeafListDefaultFromIndex(
    QryStmtT *stmt, uint8_t startIndex, QryEdgeLabelT *qryEdgeLabel, DmSubtreeT *parent, DmSubtreeT **resultTree)
{
    DB_POINTER3(stmt, qryEdgeLabel, parent);
    // 判断是否是report-all
    QrySubtreeFilterItemT *currFilter = ((QrySubtreeFilterDescT *)stmt->context->entry)->currFilter;
    if (currFilter->defaultMode != GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED &&
        currFilter->defaultMode != GMC_DEFAULT_FILTER_REPORT_ALL) {
        return GMERR_OK;
    }

    Status ret;
    DmVertexLabelT *leafListLabel = qryEdgeLabel->destQryLabel->def.vertexLabel;
    DmSubtreeT *treeCursor = NULL;
    // 该 leaf-list 未定义 when 条件，则直接生成所有默认值结果树
    if (!DmSchemaHasValidWhenClause(leafListLabel->metaVertexLabel->schema)) {
        for (uint8_t i = startIndex;
             i < leafListLabel->metaVertexLabel->schema->properties[DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT].defaultValueNum;
             ++i) {
            ret = QryBuildAndLinkSingleLeafListDefaultTree(stmt, i, qryEdgeLabel, parent, &treeCursor, resultTree);
            if (ret != GMERR_OK || IsThresholdStatus(stmt)) {
                return ret;
            }
        }
        return GMERR_OK;
    }

    // 有定义 when 条件，需要逐个默认值判断是否可见，可见的才生成默认值结果树
    bool isNewDefaultVisible = true;
    uint8_t npaIndexStart = leafListLabel->metaVertexLabel->schema->yangInfo->npaIndex;
    for (uint8_t i = startIndex;
         i < leafListLabel->metaVertexLabel->schema->properties[DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT].defaultValueNum;
         ++i) {
        ret = QryDmVertexNpaVisibleByIndex(parent->origVertex, npaIndexStart + i, &isNewDefaultVisible);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (!isNewDefaultVisible) {
            continue;
        }
        ret = QryBuildAndLinkSingleLeafListDefaultTree(stmt, i, qryEdgeLabel, parent, &treeCursor, resultTree);
        if (ret != GMERR_OK || IsThresholdStatus(stmt)) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status QryLeafListDefaultByOneFilter(
    QryStmtT *stmt, DmSubtreeT *filter, DmSubtreeT *parent, DmVertexLabelT *leafList, bool *isMatch)
{
    DB_POINTER5(stmt, filter, parent, leafList, isMatch);

    *isMatch = false;
    DmValueT filterValue = {0};
    DmRecordT *filterRecord = DmSubtreeGetRecord(filter);
    Status ret = RecordGetPropeById(filterRecord, DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT, &filterValue, false);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 有定义 when 条件，需要逐个默认值判断是否可见，可见的才生成默认值结果树
    bool hasWhen = DmSchemaHasValidWhenClause(leafList->metaVertexLabel->schema);
    bool isNewDefaultVisible = true;
    uint8_t npaIndexStart = leafList->vertexDesc->yangInfoDesc->npaIndex;
    DmPropertySchemaT *properties = leafList->metaVertexLabel->schema->properties;
    bool isEmpty = (filterRecord->propeIsEmpty[DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT] == DM_PROPERTY_IS_EMPTY);
    bool isNull = (filterRecord->propeIsSetValue[DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT] == DM_PROPERTY_IS_NULL);
    for (uint8_t i = 0; i < properties[DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT].defaultValueNum; i++) {
        if (hasWhen) {
            ret = QryDmVertexNpaVisibleByIndex(parent->origVertex, npaIndexStart + i, &isNewDefaultVisible);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
        }
        if (!isNewDefaultVisible) {
            continue;
        }
        if (isEmpty || isNull) {
            *isMatch = true;
            break;
        }
        DmValueT *dftValue = &(properties[DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT].defaultValue[i]);
        if (DmValueIsEqual(&filterValue, dftValue)) {
            *isMatch = true;
            break;
        }
    }

    return GMERR_OK;
}

Status QryLeafListDefaultsByFilterList(
    QryStmtT *stmt, DmSubtreeT *filter, DmSubtreeT *parent, DmVertexLabelT *leafList, bool *isMatch)
{
    DB_POINTER5(stmt, filter, parent, leafList, isMatch);

    // currIsMatch 状态初始化为当前origRecord经过所有filter判断后的值，
    // 对于与条件，如果未在while中途退出，说明全部条件均满足，返回true；
    // 对于或条件，说明全部条件均不满足，返回false；
    // 前序流程保证同个leaflist的filter上所有isAndCondition均相同。
    DmSubtreeT *currFilter = filter;
    bool currIsMatch = filter->isAndCondition;
    while (currFilter != NULL) {
        Status ret = QryLeafListDefaultByOneFilter(stmt, currFilter, parent, leafList, &currIsMatch);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        if (!currIsMatch && currFilter->isAndCondition) {
            *isMatch = false;
            return GMERR_OK;
        }
        if (currIsMatch && !currFilter->isAndCondition) {
            *isMatch = true;
            return GMERR_OK;
        }
        currFilter = currFilter->nextTree;
    }
    *isMatch = filter->isAndCondition;
    return GMERR_OK;
}

Status QryLeafListByOrCondFilterList(DmSubtreeT *filter, DmVertexLabelT *leafList, bool *isMatch)
{
    DB_POINTER3(filter, leafList, isMatch);

    bool currIsMatch = false;
    DmSubtreeT *currentFilter = filter;
    DmSchemaT *schema = leafList->metaVertexLabel->schema;
    while (currentFilter != NULL) {
        currentFilter->origVertex = filter->origVertex;
        DmRecordT *filterRecord = DmSubtreeGetRecord(currentFilter);
        DmRecordT *origRecord = DmSubtreeGetOrigRecord(currentFilter);
        Status ret = DmRecordIsMatchedWithFilter(filterRecord, origRecord, schema, &currIsMatch);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }

        if (currIsMatch) {
            *isMatch = true;
            break;
        }

        currentFilter = currentFilter->nextTree;
    }

    return GMERR_OK;
}

Status QryLeafListByAndCondFilterList(
    DmSubtreeT *filter, DmVertexLabelT *leafList, bool *filterMatchArr, uint32_t filterCnt)
{
    DB_POINTER3(filter, leafList, filterMatchArr);
    DB_ASSERT(DmGetSubtreeListSize(filter) == filterCnt);
    bool currIsMatch = false;
    DmSubtreeT *currentFilter = filter;
    DmSchemaT *schema = leafList->metaVertexLabel->schema;
    uint32_t filterIndex = 0;
    while (currentFilter != NULL) {
        currentFilter->origVertex = filter->origVertex;
        if (filterMatchArr[filterIndex]) {
            currentFilter = currentFilter->nextTree;
            filterIndex += 1;
            continue;
        }
        DmRecordT *filterRecord = DmSubtreeGetRecord(currentFilter);
        DmRecordT *origRecord = DmSubtreeGetOrigRecord(currentFilter);
        Status ret = DmRecordIsMatchedWithFilter(filterRecord, origRecord, schema, &currIsMatch);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }

        if (currIsMatch) {
            filterMatchArr[filterIndex] = true;
        }

        currentFilter = currentFilter->nextTree;
        filterIndex += 1;
    }

    return GMERR_OK;
}

void LinkParentTree(DmSubtreeT *parent, DmSubtreeT *childTree)
{
    if (childTree == NULL) {
        return;
    }

    childTree->parent = parent;
}

void InitChildTree(DmSubtreeT *parent, DmSubtreeT *childTree)
{
    // 深度从第一个条件处计算，带有过滤条件时直接继承父亲的深度，从不带条件的递归后开始计算增加深度
    // 深度查询时，略过choice-case的深度，仅container和list类型计算深度
    DmSchemaT *schema = DmTreeDescGetSchema(childTree->desc);
    if (DmIsChoiceSchema(schema) || DmIsCaseSchema(schema)) {
        childTree->level = parent->level;
    } else {
        childTree->level = parent->level + 1;
    }
    // 记录孩子节点是否是默认case下的孩子
    // 节点本身不是case节点，继承父亲的该属性，是case节点，根据自身属性判断是否是普通case
    childTree->isNormalCaseChild = parent->isNormalCaseChild;
    if (DmIsCaseSchema(schema)) {
        childTree->isNormalCaseChild = !DmIsDefaultSchema(schema);
    }

    LinkParentTree(parent, childTree);
}

void SetChildTree(DmSubtreeT *parent, uint32_t index, DmSubtreeT *childTree)
{
    if (childTree == NULL) {
        return;
    }
    parent->hasChild = true;
    parent->childTree[index] = childTree;
}

static inline Status SwitchNodetree(DmSubtreeT *tree)
{
    if (DmIsVertexSubtree(tree)) {
        return GMERR_OK;
    }
    return DmNodeSetElementIndex(tree->node, tree->nodeElemIdx);
}

static inline void SetCurrFilterByChildFilter(DmSubtreeT *childFilter, DmSubtreeT *currFilter)
{
    currFilter->level = childFilter->level;
    currFilter->isDefault = childFilter->isDefault;
    currFilter->origVertex = childFilter->origVertex;
    currFilter->origNode = childFilter->origNode;
}

Status QryMergeSubtree(DmSubtreeT *destSubtree, DmSubtreeT *currSubtree)
{
    Status ret = GMERR_OK;
    if (!DmIsVertexSubtree(currSubtree)) {
        // 如果是node的合并，此时vertex未序列化过，mergeVertex上无node信息，需要深拷贝
        ret = DmNodeCopyForSubtree(currSubtree->node, currSubtree->mergeNode);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "copy node when merge subtree %s", currSubtree->node->nodeDesc->name);
            return ret;
        }
    }

    ret = DmMergeSubTree(currSubtree, destSubtree);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "merge subtree.");
        return ret;
    }

    (void)DmDestroySubtreeAndNodeVector(currSubtree);

    return GMERR_OK;
}

static void QryReset2InitialState(QryStmtT *stmt, DmSubtreeT **childTree, uint32_t startNum, uint32_t startSize)
{
    *childTree = NULL;
    QryClearPathList(stmt);  //  清除查询扫描的最后位置路径，在上层调用的地方重新设置
    ResetThreshold(stmt, startNum, startSize);
}

static DmSubtreeT *QryGetChildFilterByPathTree(FetchVertexItemT *item)
{
    DB_POINTER(item);

    if ((item->path == NULL) || (item->path->filter == NULL) || (item->childFilter == item->path->filter)) {
        return item->childFilter;
    }

    DB_ASSERT(item->childFilter != NULL);
    DmSubtreeT *childFiler = item->path->filter;
    childFiler->level = item->childFilter->level;
    childFiler->isDefault = item->childFilter->isDefault;
    childFiler->origVertex = item->childFilter->origVertex;
    childFiler->origNode = item->childFilter->origNode;

    return childFiler;
}

typedef struct ThresholdContext {
    bool isMerged;
    uint32_t startNum;
    uint32_t startSize;
    uint32_t firstNum;
    uint32_t firstSize;
} ThresholdContextT;

static Status QryLinkListMultiFilterResult(
    QryStmtT *stmt, ThresholdContextT *thrCtx, DmSubtreeT **childTree, DmSubtreeT *currChild)
{
    DB_POINTER3(stmt, thrCtx, childTree);

    if (*childTree == NULL) {
        *childTree = currChild;
        RecordThreshold(stmt, &thrCtx->firstNum, &thrCtx->firstSize);
    } else {
        thrCtx->isMerged = true;
        // 过滤到相同的数据，需要进行合并。此处是把后得到的结果树合并到先前得到的结果树上
        Status ret = QryMergeSubtree(*childTree, currChild);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }

    return GMERR_OK;
}

static void QryResetThresholdContext(QryStmtT *stmt, ThresholdContextT *thrCtx, DmSubtreeT **childTree)
{
    DB_POINTER3(stmt, thrCtx, childTree);

    if (thrCtx->isMerged) {
        // 当前这条list数据被多个或条件过滤，且合并之后达到分批限制部分子树数据未捞完整。
        // 因此，这条数据是不完整的，不能当做过滤的结果返回， *childTree会置NULL。
        QryReset2InitialState(stmt, childTree, thrCtx->startNum, thrCtx->startSize);
    } else {
        ResetThreshold(stmt, thrCtx->firstNum, thrCtx->firstSize);
    }
}

static void QryReCalcAndResetThresholdContext(QryStmtT *stmt, ThresholdContextT *thrCtx, DmSubtreeT **childTree)
{
    DB_POINTER3(stmt, thrCtx, childTree);

    if (thrCtx->isMerged) {
        // 有多个或查询结果做了合并，需要重新计算vertex个数和buffer大小
        ResetThreshold(stmt, thrCtx->startNum, thrCtx->startSize);
        bool isReached = QryTreeNodeThresholdCheck(stmt, *childTree);
        if (isReached) {
            QryReset2InitialState(stmt, childTree, thrCtx->startNum, thrCtx->startSize);
        }
    } else if (thrCtx->firstNum != 0) {
        // 只有一个或查询且有结果数据，则需要重置为第一次计算的vertex个数和buffer大小
        ResetThreshold(stmt, thrCtx->firstNum, thrCtx->firstSize);
    }
}

Status QryBuildSubtreeByFilterList(FetchVertexItemT *item, FilterCb filterCbFunc, DmSubtreeT **childTree)
{
    DB_POINTER3(item, item->stmt, item->parent);
    DB_POINTER3(item->childFilter, filterCbFunc, childTree);

    *childTree = NULL;
    QryStmtT *stmt = item->stmt;
    DmSubtreeT *childFilter = QryGetChildFilterByPathTree(item);
    DmSubtreeT *currFilter = childFilter;
    Status ret;
    ThresholdContextT thrCtx = {0};
    RecordThreshold(stmt, &thrCtx.startNum, &thrCtx.startSize);
    while (currFilter != NULL) {
        ResetThreshold(stmt, thrCtx.startNum, thrCtx.startSize);
        ret = SwitchNodetree(currFilter);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        SetCurrFilterByChildFilter(childFilter, currFilter);
        item->childFilter = currFilter;
        DmSubtreeT *currChild = NULL;
        ret = filterCbFunc(item, &currChild);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        currFilter = currFilter->nextTree;

        if (currChild == NULL) {
            continue;
        }

        ret = QryLinkListMultiFilterResult(stmt, &thrCtx, childTree, currChild);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }

        if (IsThresholdStatus(stmt)) {
            QryResetThresholdContext(stmt, &thrCtx, childTree);
            return GMERR_OK;
        }
    }

    QryReCalcAndResetThresholdContext(stmt, &thrCtx, childTree);

    return GMERR_OK;
}

void LinkChildTree(DmSubtreeT *parent, uint32_t index, DmSubtreeT *child, DmSubtreeT **prevChild)
{
    if (child == NULL) {
        return;
    }
    // 第一棵子树插在父节点的childTree[index]上，下一个孩子插在第一个孩子的nextTree上
    if ((*prevChild) == NULL) {
        parent->childTree[index] = child;
        parent->hasChild = true;
        *prevChild = child;
    } else {
        (*prevChild)->nextTree = child;
        (*prevChild) = child;
    }
}

static Status QryLocationFilterTree(
    QryStmtT *stmt, DmSubtreeT *childFilter, uint32_t index, DmSubtreeT *parent, LocationCb locationFun)
{
    DB_POINTER4(stmt, childFilter, parent, locationFun);

    // 若对该list的过滤条件为empty，则只获取list首个元素返回
    if (childFilter->filterType == DM_FILTER_CONTAINER) {
        return locationFun(stmt, childFilter, true, index, parent);
    }

    Status ret = DmSetChildVertexPid(parent->origVertex, childFilter->vertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 若查询树中设置了该list的所有主键字段（PID是在上面设的），根据主键查找该节点并匹配
    if (DmVertexPkPropeIsAllSet(childFilter->vertex)) {
        return locationFun(stmt, childFilter, false, index, parent);
    }

    // 若未设置所有主键字段但是最后一级list，则取首个元素返回
    if (!childFilter->hasNestedList) {
        return locationFun(stmt, childFilter, true, index, parent);
    }

    DB_LOG_AND_SET_LASERR(
        GMERR_INVALID_PROPERTY, "%s has nested list but not set pri key.", DmGetSubtreeName(childFilter));
    return GMERR_INVALID_PROPERTY;
}

Status QryLocationFilterEntry(
    QryStmtT *stmt, DmSubtreeT *filter, uint32_t index, DmSubtreeT *parent, LocationCb locationFun, bool *isContinue)
{
    DB_POINTER2(stmt, filter);
    Status ret = GMERR_OK;
    DmSubtreeT *childFilter = filter->childTree[index];
    if (DmIsVertexSubtree(filter) && DmIsListVertexLabel(filter->desc->vertexLabel)) {
        // 位置查询时父节点为list的情况
        if (childFilter == NULL) {
            *isContinue = true;  // 对list进行位置查询时，若childTree为NULL，则不用获取child信息
            return GMERR_OK;
        }
        if (DmIsVertexSubtree(childFilter) && DmIsListVertexLabel(childFilter->desc->vertexLabel)) {
            ret = QryLocationFilterTree(stmt, childFilter, index, parent, locationFun);
            if (ret != GMERR_OK) {
                return ret;
            }
            *isContinue = true;
        }
        // 位置查询时父节点为container的情况
    } else if (childFilter != NULL && DmIsVertexSubtree(childFilter) &&
               DmIsListVertexLabel(childFilter->desc->vertexLabel)) {
        ret = QryLocationFilterTree(stmt, childFilter, index, parent, locationFun);
        if (ret != GMERR_OK) {
            return ret;
        }
        *isContinue = true;
    }
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
