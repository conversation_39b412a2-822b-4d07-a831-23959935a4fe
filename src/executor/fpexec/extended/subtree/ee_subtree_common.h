/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: header file of subtree filter common
 * Author: wuyongzheng
 * Create: 2023-05-29
 */
#ifndef EE_SUBTREE_COMMON_H
#define EE_SUBTREE_COMMON_H

#include "dm_yang_subtree.h"
#include "ee_cursor.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct FetchVertexItem {
    QryStmtT *stmt;
    DmPathTreeT *path;
    DmSubtreeT *parent;
    DmSubtreeT *childFilter;
    uint32_t index;
    void *param;
    QryLabelT *qryLabel;
} FetchVertexItemT;

Status QryCreateTreeNodeByFilter(QryStmtT *stmt, DmSubtreeT *filter, DmSubtreeT *parent, DmSubtreeT **tree);
bool QrySubtreeIsValid(DmSubtreeT *tree);
bool QrySubtreeHasChild(DmSubtreeT *filter, DmSubtreeT *tree);
bool ChoiceHasValidChild(QryStmtT *stmt, DmTreeDescT *desc, DmVertexT *vertex, DmNodeT *node);
bool IsCouldReuseVertex(QryStmtT *stmt);
bool QryIsReachMaxDepth(QryStmtT *stmt, uint32_t currLevel);
bool IsDefaultCase(DmTreeDescT *desc);
Status QryProcessDefaultVertex(
    QryStmtT *stmt, const DmSubtreeT *parent, DmVertexLabelT *childLabel, DmVertexT **vertex, bool *isDefault);
Status QryProcessDefaultNode(
    QryStmtT *stmt, const DmSubtreeT *parent, DmNodeT *node, DmNodeSchemaT *childNodeSchema, bool *isDefault);
Status QryProcessRootDefaultVertex(QryStmtT *stmt, QryLabelT *qryLabel, DmVertexT **vertex, bool *isDefault);
Status QryFetchChildVertexByFilter(
    QryStmtT *stmt, DmVertexT *currVertex, QryEdgeLabelT *qryEdgeLabel, DmVertexT *filter, DmVertexT **vertex);
Status QryFetchVertexByFilter(QryStmtT *stmt, QryLabelT *qryLabel, DmVertexT *filter, DmVertexT **vertex);
Status QryFetchVertexByScan(
    QryStmtT *stmt, QryLabelT *qryLabel, DmVertexT *filter, bool isReuse, DmVertexT **vertex, uint64_t *addr);
Status QrySetUnReadableDefaultValues(QryStmtT *stmt, DmVertexT *vertex, DmSchemaT *schema, DmRecordT *result);

void LinkParentTree(DmSubtreeT *parent, DmSubtreeT *childTree);

void InitChildTree(DmSubtreeT *parent, DmSubtreeT *childTree);

void SetChildTree(DmSubtreeT *parent, uint32_t index, DmSubtreeT *childTree);

void LinkChildTree(DmSubtreeT *parent, uint32_t index, DmSubtreeT *child, DmSubtreeT **prevChild);

typedef Status (*FilterCb)(FetchVertexItemT *item, DmSubtreeT **tree);

Status QryBuildSubtreeByFilterList(FetchVertexItemT *item, FilterCb filterCbFunc, DmSubtreeT **childTree);

Status QryIsLeafListFromSeVisible(
    DmVertexT *parent, DmVertexT *dmVertex, uint32_t defaultMode, DmVertexLabelT *leafListLabel, bool *isVisible);

Status QryBuildAndLinkSingleLeafListDefaultTree(QryStmtT *stmt, uint8_t index, QryEdgeLabelT *qryEdgeLabel,
    DmSubtreeT *parent, DmSubtreeT **treeCursor, DmSubtreeT **resultTree);

// 从index下标开始构造所有的leaflist默认值
Status QryBuildLeafListDefaultFromIndex(
    QryStmtT *stmt, uint8_t startIndex, QryEdgeLabelT *qryEdgeLabel, DmSubtreeT *parent, DmSubtreeT **resultTree);

Status QryLeafListDefaultsByFilterList(
    QryStmtT *stmt, DmSubtreeT *filter, DmSubtreeT *parent, DmVertexLabelT *leafList, bool *isMatch);

typedef Status (*LocationCb)(QryStmtT *stmt, DmSubtreeT *filter, bool isGetFirst, uint32_t index, DmSubtreeT *parent);

Status QryLocationFilterEntry(
    QryStmtT *stmt, DmSubtreeT *filter, uint32_t index, DmSubtreeT *parent, LocationCb locationFun, bool *isContinue);

Status QryLeafListByOrCondFilterList(DmSubtreeT *filter, DmVertexLabelT *leafList, bool *isMatch);

Status QryLeafListByAndCondFilterList(
    DmSubtreeT *filter, DmVertexLabelT *leafList, bool *filterMatchArr, uint32_t filterCnt);

Status QrySubtreeConfigFilter(QryStmtT *stmt, DmSubtreeT **result);

static inline uint32_t QryGetSubtreeConfigFilter(QryStmtT *stmt)
{
    QrySubtreeFilterDescT *filterCtx = (QrySubtreeFilterDescT *)stmt->context->entry;
    return filterCtx->currFilter->configFilter;
}

static inline uint32_t QryGetSubtreeRootFilterType(QryStmtT *stmt)
{
    QrySubtreeFilterDescT *filterCtx = (QrySubtreeFilterDescT *)stmt->context->entry;
    return filterCtx->currFilter->filter->filterType;
}

#ifdef __cplusplus
}
#endif
#endif /* EE_SUBTREE_COMMON_H */
