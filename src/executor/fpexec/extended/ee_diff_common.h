/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: header file for diff common
 * Author: ch<PERSON><PERSON>wen
 * Create: 2022-12-01
 */
#ifndef EE_DIFF_COMMON_H
#define EE_DIFF_COMMON_H

#include "dm_yang_diff.h"
#include "dm_yang_common.h"
#include "ee_cursor.h"
#include "ee_context.h"

#ifdef __cplusplus
extern "C" {
#endif
#define DIFF_LIST_EXTEND_SIZE 8
#define DIFF_RANDOM_SEED 10
#define DIFF_INVALID_EDGE_ADDR 0

typedef enum DiffOpType {
    DIFF_OPERATION_NONE = 0,  // 表示没有操作，只是维护路径
    DIFF_OPERATION_MERGE,     // 表示值被修改获取移动位置
    DIFF_OPERATION_REMOVE,    // 表示节点删除
    DIFF_OPERATION_CREATE,    // 表示节点创建
    DIFF_OPERATION_MOVE,      // 表示只单纯移动位置，只会用于resultTree生成
    DIFF_OPERATION_BUTT
} DiffOpTypeE;

typedef struct DiffFilterCreate {
    DiffOpTypeE opType;  // 当前对节点的操作类型
    uint32_t vertexId;   // 用于remove场景下保证主键到自增id的映射
    DmVertexDescT *vertexDesc;
    const DmVertexT *vertex;
    TupleAddr vertexAddr;  // 当前节点存储位置
    TupleAddr newVertexAddr;  // 当前节点最新存储位置，只有先删除后创建场景下newVertexAddr不等于vertexAddr
    IndexKeyT *pkIdxkey;         // 用于remove场景下保证主键到自增id的映射
    TupleAddr parentVertexAddr;  // 父亲节点存储位置
    bool isWhenDiff;
} DiffFilterCreateT;

typedef struct DiffLeafListVisibilityParam {
    QryStmtT *stmt;
    DmVertexLabelT *vertexLabel;
    DmYangTreeT *parentVertexTree;
    uint8_t visibilityCache[DM_MAX_DEFAULT_VALUE_NUM];
    uint32_t defaultIndex;
    uint32_t pid;
} DiffLeafListVisibilityParamT;

/**
 * @brief 是否开启diff
 * @param stmt [in]: 本次操作对应的stmt
 * @return 是否开启diff
 */
inline static bool DiffModeIsOpen(QryStmtT *stmt)
{
    return stmt->session->yangTreeCtx != NULL && stmt->session->yangTreeCtx->diffCtx != NULL &&
           (stmt->session->yangTreeCtx->diffCtx->diffType == BATCH_YANG_DIFF_ON);
}

/**
 * @brief fetch diff 时的模式是否为 explicit
 * @attention: 这个函数由调用方保证stmt内部的diffCtx有效
 * @param stmt [in]: 本次操作对应的stmt
 * @return fetch diff 时的模式是否为 explicit
 */
inline static bool DiffFetchModeIsExplicit(QryStmtT *stmt)
{
    return stmt->session->yangTreeCtx->diffCtx->isExplicit;
}

/**
 * @brief 设置fetch diff 时的模式
 * @attention: 这个函数由调用方保证stmt内部的diffCtx有效
 * @param stmt [in]: 本次操作对应的stmt
 * @param mode [in]: fetch diff 时的模式
 */
inline static void DiffSetFetchMode(QryStmtT *stmt, DmFetchDiffModeE mode)
{
    stmt->session->yangTreeCtx->diffCtx->isExplicit = mode == DM_FETCH_DIFF_EXPLICIT;
}

/**
 * @brief 是否第一次打包
 * @param stmt [in]: 本次操作对应的stmt
 * @return 是否第一次打包
 */
inline static bool DiffIsFirstPack(QryStmtT *stmt)
{
    return stmt->session->yangTreeCtx->diffCtx->iter == NULL;
}

/**
 * @brief 开始节点操作状态构建
 * @param diffNodeTree [in]: 当前节点
 * @return
 */
inline static void DiffSetGenerationStart(DmYangTreeT *diffNodeTree)
{
    diffNodeTree->op = DM_DIFF_OPERATION_INVALID;
}

/**
 * @brief 更新当前fetch序号
 * @param stmt [in]: 本次操作对应的stmt
 * @param diffNodeTree [in]: 当前节点
 * @return
 */
inline static void DiffUpdateFetchSeq(QryStmtT *stmt, DmYangTreeT *diffNodeTree)
{
    diffNodeTree->diffCtx->fetchSeq = stmt->session->yangTreeCtx->diffCtx->fetchSeq;
}

/**
 * @brief 结束节点操作状态构建
 * @param finalOp [in]: 当前节点操作类型
 * @param diffNodeTree [in]: 当前节点
 * @return
 */
inline static void DiffSetGenerationEnd(QryStmtT *stmt, DmDiffTreeTypeE finalOp, DmYangTreeT *diffNodeTree)
{
    // 最高bit位置1表示生成完成
    diffNodeTree->op = DmSetTreeOp(finalOp);
    DiffUpdateFetchSeq(stmt, diffNodeTree);
}

/**
 * @brief 判断该节点在当前fetch序号是否已经构建完成
 * @param stmt [in]: 本次操作对应的stmt
 * @param diffNodeTree [in]: 当前操作节点
 * @return 是否完成
 */
inline static bool DiffCheckGenerationIsFinshed(QryStmtT *stmt, DmYangTreeT *diffNodeTree)
{
    // 不同fetch序号需要重新生成
    if (diffNodeTree->diffCtx->fetchSeq != stmt->session->yangTreeCtx->diffCtx->fetchSeq) {
        DiffSetGenerationStart(diffNodeTree);
        return false;
    }
    // 判断节点是否已经完成生成
    if (DmTreeOpIsFinish(diffNodeTree->op)) {
        return true;
    }
    DiffSetGenerationStart(diffNodeTree);
    return false;
}

/**
 * @brief 获取辨识filterTree的key（vertexAddr+uniqueNodeId)哈希值
 * @param diffKey [in]: list节点集合键值
 * @return 哈希值
 */
inline static uint32_t DiffGetTreeKeyCode(DmDiffTreeKeyT *diffKey)
{
    return DbHash32WithSeed((const uint8_t *)diffKey, sizeof(DmDiffTreeKeyT), DIFF_RANDOM_SEED);
}

/**
 * @brief 获取辨识相关list节点集合的key（id+edgeLabelId)哈希值，id为父节点唯一id，该集合与父节点绑定
 * @param setKey [in]: list节点集合键值
 * @return 哈希值
 */
inline static uint32_t DiffGetfNodeSetKeyCode(DmDiffListNodeSetKeyT *setKey)
{
    return DbHash32WithSeed((const uint8_t *)setKey, sizeof(DmDiffListNodeSetKeyT), DIFF_RANDOM_SEED);
}

/**
 * @brief 判断该节点状态是否无效
 * @param filterTree [in]: 当前节点
 * @return 是否无效
 */
inline static bool DiffIsInvalidTreeState(const DmYangTreeT *filterTree)
{
    DB_POINTER(filterTree);
    return filterTree->diffCtx->state == DM_DIFF_STATE_INITIAL || filterTree->diffCtx->state == DM_DIFF_STATE_INVALID;
}

/**
 * @brief 根据key查找对应的节点的diffTree
 * @param stmt [in]: 本次操作对应的stmt
 * @param key [in]: 轨迹集合hash的键值
 * @return 对应的节点的diffTree
 */
inline static DmYangTreeT *DiffFindNodeTreeByKey(QryStmtT *stmt, DmDiffTreeKeyT *key)
{
    return (DmYangTreeT *)DbOamapLookup(
        &stmt->session->yangTreeCtx->diffCtx->diffFilterMap, DiffGetTreeKeyCode(key), key, NULL);
}

/**
 * @brief 将diffTree节点插入到prevTree节点的下一节点
 * @param prevTree [in]: 目标节点
 * @param diffTree [in]: 插入节点
 * @return
 */
inline static void DiffListNodeLink(DmYangTreeT *prevTree, DmYangTreeT *diffTree)
{
    diffTree->nextTree = prevTree->nextTree;
    diffTree->prevTree = prevTree;
    if (prevTree->nextTree != NULL) {
        prevTree->nextTree->prevTree = diffTree;
    }
    prevTree->nextTree = diffTree;
}

/**
 * @brief 将diffTree节点从链表上摘除
 * @param diffTree [in]: 本次摘除的节点
 * @return
 */
inline static void DiffListNodeUnlink(DmYangTreeT *diffTree)
{
    if (diffTree->prevTree != NULL) {
        diffTree->prevTree->nextTree = diffTree->nextTree;
    }
    if (diffTree->nextTree != NULL) {
        diffTree->nextTree->prevTree = diffTree->prevTree;
    }
}

/**
 * @brief 根据key查找是否已经存在对应的list节点集合
 * @param stmt [in]: 本次操作对应的stmt
 * @param parenId [in]: 父节点唯一id
 * @param edgeLabelId [in]: 联系边表id
 * @return 对应list节点集合
 */
inline static DmDiffListNodeSetT *FindDiffNodeSetByKey(QryStmtT *stmt, uint32_t parenId, uint32_t edgeLabelId)
{
    DmDiffListNodeSetKeyT listNodeKey = {.id = parenId, .edgeLabelId = edgeLabelId};
    return (DmDiffListNodeSetT *)DbOamapLookup(
        &stmt->session->yangTreeCtx->diffCtx->diffListMap, DiffGetfNodeSetKeyCode(&listNodeKey), &listNodeKey, NULL);
}

DmDiffSubsModeE GetDiffSubsMode(DmVertexDescT *vertexDesc);

inline static bool DiffModeHasSubs(DmFetchDiffModeE mode, DmDiffSubsModeE subMode)
{
    if (subMode == DM_DIFF_SUBS_BOTH) {
        return true;
    }
    if (mode == DM_FETCH_DIFF_REPORT_ALL && subMode == DM_DIFF_SUBS_REPORT_ALL) {
        return true;
    } else if (mode == DM_FETCH_DIFF_EXPLICIT && subMode == DM_DIFF_SUBS_EXPLICIT) {
        return true;
    }
    return false;
}

/**
 * @brief 将op枚举转换成字符串输出
 * @param op [in]: 操作类型
 * @return 操作名字符串
 */
const char *DiffGetOptypeStr(DiffOpTypeE op);

/**
 * @brief 根据转换规则或者最新节点状态
 * @param currentState [in]: 当前节点状态
 * @param currentOperation [in]: 当前节点操作
 * @return 最新状态
 */
DmDiffTreeStateE DiffTreeStateConvert(DmDiffTreeStateE currentState, DiffOpTypeE currentOperation);

/**
 * @brief 查找已删除节点对应的自增id
 * @param stmt [in]: 本次操作对应的stmt
 * @param pkIdx [in]: 节点对应主键
 * @param labelId [in]: 节点对应表定义
 * @return 已删除节点对应的自增id
 */
uint32_t *QryFindVertexIdFromRemoveMap(QryStmtT *stmt, const IndexKeyT *pkIdx, uint32_t labelId);

/**
 * @brief 将根节点插入结果树集合
 * @param stmt [in]: 本次操作对应的stmt
 * @param vertexDesc [in]: 节点对应静态描述信息
 * @param diffTree [in]: 当前操作节点
 * @return success或错误码
 */
Status DiffInsertRootNodeToMap(QryStmtT *stmt, DmVertexDescT *vertexDesc, DmYangTreeT *diffTree);

/**
 * @brief 将节点插入节点轨迹树集合
 * @param stmt [in]: 本次操作对应的stmt
 * @param vertexAddr [in]: 节点存储位置
 * @param diffTree [in]: 当前操作节点
 * @return success或错误码
 */
Status DiffInsertNodeToFilterMap(QryStmtT *stmt, TupleAddr vertexAddr, DmYangTreeT *diffTree);

/**
 * @brief 将list节点加入list集合，集合与父节点绑定
 * @param stmt [in]: 本次操作对应的stmt
 * @param parenId [in]: 父节点diffTree唯一id
 * @param edgeLabelId [in]: 建立联系的边表定义Id
 * @param diffTree [in]: 当前操作节点
 * @return success或错误码
 */
Status DiffInsertNodeToListMap(QryStmtT *stmt, uint32_t parenId, uint32_t edgeLabelId, DmYangTreeT *diffTree);

/**
 * @brief 根据 label id 和默认值下标查找filtertree
 * @param stmt [in]: 本次操作对应的stmt
 * @param leafListLabelId [in]: leaflist 表Id
 * @param defaultIndex [in]: leaflist节点的默认值下标
 * @return filtertree节点
 */
DmYangTreeT *DiffFindleaflistTreeByKey(QryStmtT *stmt, uint32_t leafListLabelId, uint32_t defaultIndex);

/**
 * @brief 删除操作下需要记录主键和对应的自增id，用于下次创建同一键值记录恢复其自增id
 * @param stmt [in]: 本次操作对应的stmt
 * @param filterCreate [in]: 当前操作节点
 * @return success或错误码
 */
Status DiffInsertNodeKeyToRemoveMap(QryStmtT *stmt, const DiffFilterCreateT *filterCreate);

/**
 * @brief 判断该节点在none和merge操作中是否被访问过
 * @param visitedNodes [in]: 访问列表
 * @param uniqueId [in]: 目标node节点唯一id
 * @return success或错误码
 */
bool DiffIsNodeVisited(const DbListT *visitedNodes, uint16_t uniqueId);

/**
 * @brief 缓存用过的vertexLabel,后续需要发送到客户端
 * @param stmt [in]: 本次操作对应的stmt
 * @param vertexLabel [in]: 本次操作的表定义
 * @return success或错误码
 */
Status DiffCacheVertexLabel(QryStmtT *stmt, DmVertexLabelT *vertexLabel);

/**
 * @brief 判断是否存在孩子节点diff树
 * @param parent [in]: 本次操作对应的父diffTree
 * @return 存在孩子diff树返回true，不存在返回false
 */
bool DiffHasChildDiffTree(const DmYangTreeT *parent);

/**
 * @brief 创建一个新的diffNodeTree
 * @param stmt [in]: 本次操作对应的stmt
 * @param isVertex [in]: 如果是Vertex，生命周期是事务级别
 *                       如果是Node，在fetch diff 后释放
 * @param vertexAddr [in]: 节点存储位置
 * @param uniqueNodeId [in]: 节点唯一id
 * @param diffFilter [out]: 新创建的diffTree
 * @return success或错误码
 */
Status DiffCreateNodeOnly(
    QryStmtT *stmt, bool isVertex, uint64_t vertexAddr, uint16_t uniqueNodeId, DmYangTreeT **diffFilter);

/**
 * @brief 将node节点加入访问轨迹，挂在vertex级别filterTree下
 * @param stmt [in]: 本次操作对应的stmt
 * @param uniqueId [in]: node节点唯一id
 * @param diffNode [out]: 当前diffTree
 * @return success或错误码
 */
Status DiffInsertVisitedNode(QryStmtT *stmt, uint16_t uniqueId, DmYangTreeT *diffNode);

/**
 * @brief 分配缓存labelCursor
 * @param stmt [in]: 本次操作对应的stmt
 * @param qryLabel [in]: 次操作对应的qryLabel
 * @param labelCursor [out]: 分配对应的cursor
 * @return success或错误码
 */
Status DiffAllocLabelCursor(QryStmtT *stmt, QryLabelT *qryLabel, QryLabelCursorT **labelCursor);

/**
 * @brief 释放所有缓存labelCursor
 * @param stmt [in]: 本次操作对应的stmt
 * @return
 */
void DiffReleaseCacheLabelCursor(QryStmtT *stmt);

/**
 * @brief 是否有用户设置的孩子节点
 * @param stmt [in]: 本次操作对应的stmt
 * @param isOld [in]: 老数据还是新数据
 * @param node [in]: 本次操作对应的node
 * @param vertex [in]: 本次操作node对应的vertex
 * @param hasChild [out]: 是否有用户设置的孩子节点
 * @return success或错误码
 */
Status DiffNodeIsHadChild(QryStmtT *stmt, bool isOld, const DmNodeT *node, const DmVertexT *vertex, bool *hasChild);

/**
 * @brief 判断是否为构造的虚拟addr
 * @param addr [in]: addr
 * @return bool 判断结果
 */
bool DiffIsVirtualAddr(uint64_t addr);

/**
 * @brief 获取一个虚拟addr
 * @param stmt [in]: 本次操作对应的stmt
 * @return uint64_t 虚拟addr
 */
uint64_t DiffGetDefaultTreeVirtualAddr(QryStmtT *stmt);

/**
 * @brief 设置 diff 树上 isOldNotEmpty 和 isNewNotEmpty 两个标志位，它们用于处理无意义的 NP 节点的情况。无意义指的是
 * 自身及整个子树无用户实例或用户设值属性，也没有可见默认值。这种节点应视为不可见，相应地其 diff 状态也要据此更新
 * @param stmt [in]: 本次操作对应的stmt
 * @param tree [in]: diff 结果树
 * @param desc [in]: 当前节点静态信息
 * @return uint64_t 虚拟addr
 */
Status DiffSetTreeIsNotEmptyFlags(QryStmtT *stmt, DmYangTreeT *tree, const DmTreeDescT *desc);

// 在 fetch diff 过程中可能会修改原始的 diff 状态，为了下次 fetch diff 时能正常获取 vertex，需要恢复原来的状态。
// 另外两个 isNotEmpty 标志位也需要重置，否则会影响下次 fetch diff 时对节点是否为空的判断。
// 此函数一定是在已经调用 DiffSetGenerationEnd 生成完最终 diff 操作状态之后才调用
static inline void ResetFilterTreeState(DmYangTreeT *filterTree, DmDiffTreeStateE originalState)
{
    DB_POINTER(filterTree);
    filterTree->diffCtx->state = originalState;
    filterTree->diffCtx->isOldNotEmpty = false;
    filterTree->diffCtx->isNewNotEmpty = false;
}

// 判断是否是带有默认值定义的 leaf-list
bool DiffIsLeafTreeWithDefaultValue(DmVertexLabelT *vertexLabel);

// 更新 diff 树上的 vertex
Status YangTreeUpdateDmVertex(DmYangTreeT *tree);
Status DiffCacheTreeNpaRecord(QryStmtT *stmt, const DmTreeDescT *desc, const DmYangTreeT *diffNodeTree);

#ifdef __cplusplus
}
#endif

#endif /* EE_DIFF_COMMON_H */
