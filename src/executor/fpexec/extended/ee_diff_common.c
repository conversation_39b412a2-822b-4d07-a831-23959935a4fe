/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementation of diff common operation
 * Author: ch<PERSON><PERSON><PERSON>
 * Create: 2022-12-01
 */
#include "ee_diff_common.h"
#include "ee_yang_edit_common.h"
#include "dm_meta_topo_label.h"

#ifdef __cplusplus
extern "C" {
#endif

#define DIFF_LEAF_LIST_DEAULT_ADDR_MASK 0x8000000000000000

DmDiffTreeStateE DiffTreeStateConvert(DmDiffTreeStateE currentState, DiffOpTypeE currentOperation)
{
    if (SECUREC_UNLIKELY(currentState >= DM_DIFF_STATE_BUTT || currentOperation >= DIFF_OPERATION_BUTT)) {
        return DM_DIFF_STATE_BUTT;
    }

    // filterTree节点状态转换，最终的状态由当前状态和当前操作转换
    // DIFF_OPERATION_BUTT当前操作顺序为NONE、MERGE、REMOVE、CREATE、MOVE
    const DmDiffTreeStateE diffStateConvert[(int32_t)DM_DIFF_STATE_BUTT][(int32_t)DIFF_OPERATION_BUTT] = {
        // 当前状态为DM_DIFF_STATE_INITIAL
        {DM_DIFF_STATE_NONE, DM_DIFF_STATE_MERGE, DM_DIFF_STATE_REMOVE, DM_DIFF_STATE_CREATE, DM_DIFF_STATE_MOVE},
        // 当前状态为DM_DIFF_STATE_MERGE
        {DM_DIFF_STATE_MERGE, DM_DIFF_STATE_MERGE, DM_DIFF_STATE_REMOVE, DM_DIFF_STATE_BUTT, DM_DIFF_STATE_MERGE},
        // 当前状态为DM_DIFF_STATE_REMOVE
        {DM_DIFF_STATE_BUTT, DM_DIFF_STATE_MERGE, DM_DIFF_STATE_BUTT, DM_DIFF_STATE_MERGE, DM_DIFF_STATE_REMOVE},
        // 当前状态为DM_DIFF_STATE_CREATE
        {DM_DIFF_STATE_CREATE, DM_DIFF_STATE_CREATE, DM_DIFF_STATE_INVALID, DM_DIFF_STATE_BUTT, DM_DIFF_STATE_CREATE},
        // 当前状态为DM_DIFF_STATE_NONE
        {DM_DIFF_STATE_NONE, DM_DIFF_STATE_MERGE, DM_DIFF_STATE_REMOVE, DM_DIFF_STATE_BUTT, DM_DIFF_STATE_MOVE},
        // 当前状态为DM_DIFF_STATE_INVALID
        {DM_DIFF_STATE_BUTT, DM_DIFF_STATE_CREATE, DM_DIFF_STATE_BUTT, DM_DIFF_STATE_CREATE, DM_DIFF_STATE_BUTT},
        // 当前状态为DM_DIFF_STATE_MOVE
        {DM_DIFF_STATE_MOVE, DM_DIFF_STATE_MERGE, DM_DIFF_STATE_REMOVE, DM_DIFF_STATE_BUTT, DM_DIFF_STATE_MOVE}};
    return diffStateConvert[currentState][currentOperation];
}

uint32_t DbOamapDmDiffTreeCompare(const DmDiffTreeKeyT *k1, const DmDiffTreeKeyT *k2)
{
    DB_POINTER2(k1, k2);
    return k1->vertexAddr == k2->vertexAddr;
}

uint32_t DbOamapDmListTreeCompare(const DmDiffListNodeSetKeyT *k1, const DmDiffListNodeSetKeyT *k2)
{
    DB_POINTER2(k1, k2);
    return (k1->id == k2->id) && (k1->edgeLabelId == k2->edgeLabelId);
}

// 根据labelId和主键查找被删除的节点曾经的自增id
uint32_t *QryFindVertexIdFromRemoveMap(QryStmtT *stmt, const IndexKeyT *pkIdx, uint32_t labelId)
{
    return (uint32_t *)DbOamapLookup(&stmt->session->yangTreeCtx->diffCtx->removeIdMap,
        DbHash32WithSeed(pkIdx->keyData, pkIdx->keyLen, labelId), pkIdx, NULL);
}

// 将根节点插入结果树集合
Status DiffInsertRootNodeToMap(QryStmtT *stmt, DmVertexDescT *vertexDesc, DmYangTreeT *diffTree)
{
    DB_POINTER2(stmt, diffTree);
    if (!DmIsRootYangVertexDesc(vertexDesc)) {
        return GMERR_OK;
    }
    // 根节点记录下是否有订阅关系
    diffTree->diffCtx->trailCtx->subsMode = GetDiffSubsMode(vertexDesc);
    return DbGaListInsert(&stmt->session->yangTreeCtx->diffCtx->diffResultTrees, diffTree);
}

// 将节点加入已经访问过的节点轨迹集合
Status DiffInsertNodeToFilterMap(QryStmtT *stmt, TupleAddr vertexAddr, DmYangTreeT *diffTree)
{
    DB_POINTER2(stmt, diffTree);
    // 释放原则：事务提交或回滚时，调用ClearYangDiffData通过销毁ctx来释放内存
    // memctx: yangDiffTreeMemCtx, 事务提交或回滚时释放
    DmDiffTreeKeyT *key =
        (DmDiffTreeKeyT *)DbDynMemCtxAlloc(stmt->session->yangTreeCtx->memCtx, sizeof(DmDiffTreeKeyT));
    if (SECUREC_UNLIKELY(key == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "diffTree key");
        return GMERR_OUT_OF_MEMORY;
    }

    key->vertexAddr = vertexAddr;
    Status ret = DbOamapInsert(
        &stmt->session->yangTreeCtx->diffCtx->diffFilterMap, DiffGetTreeKeyCode(key), key, diffTree, NULL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "insert to diffFilterMap");
        DbDynMemCtxFree(stmt->session->yangTreeCtx->memCtx, key);  // key 是栈变量不用置空
    }
    return ret;
}

// 将list节点加入list集合，集合与父节点绑定
Status DiffInsertNodeToListMap(QryStmtT *stmt, uint32_t parenId, uint32_t edgeLabelId, DmYangTreeT *diffTree)
{
    DB_POINTER2(stmt, diffTree);
    DmDiffListNodeSetT *listHead = FindDiffNodeSetByKey(stmt, parenId, edgeLabelId);
    if (listHead != NULL) {
        // 直接插入已有的集合
        // 释放原则：事务提交或回滚时，调用ClearYangDiffData通过销毁ctx来释放内存
        // memctx: yangDiffTreeMemCtx, 事务提交或回滚时释放
        DmDiffListNodeSetT *newNode =
            (DmDiffListNodeSetT *)DbDynMemCtxAlloc(stmt->session->yangTreeCtx->memCtx, sizeof(DmDiffListNodeSetT));
        if (SECUREC_UNLIKELY(newNode == NULL)) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "list node");
            return GMERR_OUT_OF_MEMORY;
        }
        newNode->tree = diffTree;
        newNode->next = listHead->next;
        listHead->next = newNode;
        return GMERR_OK;
    }
    // 释放原则：事务提交或回滚时，调用ClearYangDiffData通过销毁ctx来释放内存
    // memctx: yangDiffTreeMemCtx, 事务提交或回滚时释放
    DmDiffListNodeSetKeyT *newKey =
        (DmDiffListNodeSetKeyT *)DbDynMemCtxAlloc(stmt->session->yangTreeCtx->memCtx, sizeof(DmDiffListNodeSetKeyT));
    if (SECUREC_UNLIKELY(newKey == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "list node set key");
        return GMERR_OUT_OF_MEMORY;
    }
    newKey->id = parenId;
    newKey->edgeLabelId = edgeLabelId;
    DmDiffListNodeSetT *nodeSet =
        (DmDiffListNodeSetT *)DbDynMemCtxAlloc(stmt->session->yangTreeCtx->memCtx, sizeof(DmDiffListNodeSetT));
    if (SECUREC_UNLIKELY(nodeSet == NULL)) {
        DbDynMemCtxFree(stmt->session->yangTreeCtx->memCtx, newKey);  // newKey是栈变量不用置空
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "first list node");
        return GMERR_OUT_OF_MEMORY;
    }
    nodeSet->tree = diffTree;
    nodeSet->next = NULL;
    Status ret = DbOamapInsert(
        &stmt->session->yangTreeCtx->diffCtx->diffListMap, DiffGetfNodeSetKeyCode(newKey), newKey, nodeSet, NULL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbDynMemCtxFree(stmt->session->yangTreeCtx->memCtx, newKey);   // newKey是栈变量不用置空
        DbDynMemCtxFree(stmt->session->yangTreeCtx->memCtx, nodeSet);  // nodeSet是栈变量不用置空
    }
    return ret;
}

bool DiffIsVirtualAddr(uint64_t addr)
{
    return (DIFF_LEAF_LIST_DEAULT_ADDR_MASK & addr) != 0;
}

uint64_t DiffGetDefaultTreeVirtualAddr(QryStmtT *stmt)
{
    // 虚拟生成的逻辑vertex address，最高位置1，同时通过defaultUniqueId保证唯一性
    return DIFF_LEAF_LIST_DEAULT_ADDR_MASK | stmt->session->yangTreeCtx->diffCtx->defaultUniqueId++;
}

// 判断该节点在none和merge操作中是否被访问过
bool DiffIsNodeVisited(const DbListT *visitedNodes, uint16_t uniqueId)
{
    // 所有node节点都没被访问过，指针为空
    if (visitedNodes == NULL) {
        return false;
    }
    for (uint32_t j = 0; j < DbListGetItemCnt(visitedNodes); j++) {
        if (*(uint16_t *)DbListItem(visitedNodes, j) == uniqueId) {
            return true;
        }
    }
    return false;
}

// 缓存用过的vertexLabel,后续需要发送到客户端
Status DiffCacheVertexLabel(QryStmtT *stmt, DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(stmt, vertexLabel);
    bool isExist = false;
    for (uint32_t i = 0; i < DbGaListGetCount(&stmt->session->yangTreeCtx->vertexLabelList); i++) {
        // i < count vertexLabelCache不会为NULL
        DmVertexLabelT *vertexLabelCache =
            (DmVertexLabelT *)DbGaListGet(&stmt->session->yangTreeCtx->vertexLabelList, i);
        if (vertexLabel->metaCommon.metaId == vertexLabelCache->metaCommon.metaId) {
            isExist = true;
            break;
        }
    }
    if (!isExist) {
        return DbGaListInsert(&stmt->session->yangTreeCtx->vertexLabelList, vertexLabel);
    }
    return GMERR_OK;
}

// 判断是否存在孩子节点
bool DiffHasChildDiffTree(const DmYangTreeT *parent)
{
    DB_POINTER(parent);
    for (uint32_t i = 0; i < parent->childNum; i++) {
        if (parent->childTreeCtx[i].tree != NULL) {
            return true;
        }
    }
    return false;
}

// 创建一个新的diffNode
Status DiffCreateNodeOnly(
    QryStmtT *stmt, bool isVertex, uint64_t vertexAddr, uint16_t uniqueNodeId, DmYangTreeT **diffFilter)
{
    DB_POINTER2(stmt, diffFilter);
    Status ret;
    if (!isVertex) {
        // 如果是Node，将内存绑定在yangTreeCtx->fetchMemCtx，生命周期是请求级别
        ret = DmCreateEmptyDiffTree(stmt->session->yangTreeCtx->fetchMemCtx, DM_YANG_SERVER_DIFF_NODE_TREE, diffFilter);
    } else {
        // 如果是Vertex，将内存绑定在yangTreeCtx->memCtx，生命周期是事务级别
        ret = DmCreateEmptyDiffTree(stmt->session->yangTreeCtx->memCtx, DM_YANG_SERVER_DIFF_VERTEX_TREE, diffFilter);
    }
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "create a empty tree");
        return ret;
    }
    if (isVertex) {
        (*diffFilter)->diffCtx->trailCtx->id = stmt->session->yangTreeCtx->diffCtx->treeId++;
        (*diffFilter)->diffCtx->trailCtx->savePointSeq = stmt->session->yangTreeCtx->diffCtx->savePointSeq;
        (*diffFilter)->diffCtx->trailCtx->vertexAddr = vertexAddr;
    }
    (*diffFilter)->diffCtx->state = DM_DIFF_STATE_INITIAL;
    (*diffFilter)->uniqueNodeId = uniqueNodeId;
    return GMERR_OK;
}

// 将node节点加入访问轨迹，挂在vertex级别filterTree下
Status DiffInsertVisitedNode(QryStmtT *stmt, uint16_t uniqueId, DmYangTreeT *diffNode)
{
    DB_POINTER2(stmt, diffNode);
    if (diffNode->diffCtx->trailCtx->visitNodes == NULL) {
        // 释放原则：事务提交或回滚时，调用ClearYangDiffData通过销毁ctx来释放内存
        // memctx: yangDiffTreeMemCtx, 事务提交或回滚时释放
        diffNode->diffCtx->trailCtx->visitNodes =
            (DbListT *)DbDynMemCtxAlloc(stmt->session->yangTreeCtx->memCtx, sizeof(DbListT));
        if (SECUREC_UNLIKELY(diffNode->diffCtx->trailCtx->visitNodes == NULL)) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "visited node list");
            return GMERR_OUT_OF_MEMORY;
        }
        DbCreateListWithExtendSize(diffNode->diffCtx->trailCtx->visitNodes, sizeof(uint16_t), DIFF_LIST_EXTEND_SIZE,
            stmt->session->yangTreeCtx->memCtx);
    }
    return DiffIsNodeVisited(diffNode->diffCtx->trailCtx->visitNodes, uniqueId) ?
               GMERR_OK :
               DbAppendListItem(diffNode->diffCtx->trailCtx->visitNodes, &uniqueId);
}

// 删除操作下需要记录主键和对应的自增id，用于下次创建同一键值记录恢复其自增id
Status DiffInsertNodeKeyToRemoveMap(QryStmtT *stmt, const DiffFilterCreateT *filterCreate)
{
    DB_POINTER2(stmt, filterCreate);
    // leaf-list的ID不自增，不用这个步骤。非删除操作也不用。
    if (DmIsLeafListVertexDesc(filterCreate->vertexDesc) || filterCreate->opType != DIFF_OPERATION_REMOVE) {
        return GMERR_OK;
    }
    uint32_t *id = QryFindVertexIdFromRemoveMap(stmt, filterCreate->pkIdxkey, filterCreate->vertexDesc->labelId);
    if (id != NULL) {
        return GMERR_OK;
    }
    // hasRemoveOp用于优化插入流程，如果该事务不存在删除操作，不用每次插入判断是否恢复自增id值
    stmt->session->yangTreeCtx->diffCtx->hasRemoveOp = true;
    // 释放原则：事务提交或回滚时，调用ClearYangDiffData通过销毁ctx来释放内存
    // memctx: yangDiffTreeMemCtx, 事务提交或回滚时释放
    uint8_t *buf = DbDynMemCtxAlloc(
        stmt->session->yangTreeCtx->memCtx, sizeof(IndexKeyT) + filterCreate->pkIdxkey->keyLen + sizeof(uint32_t));
    if (SECUREC_UNLIKELY(buf == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "remove map buf");
        return GMERR_OUT_OF_MEMORY;
    }
    IndexKeyT *mapKey = (IndexKeyT *)buf;
    mapKey->keyLen = filterCreate->pkIdxkey->keyLen;
    mapKey->keyData = (uint8_t *)(buf + sizeof(IndexKeyT));
    errno_t err = memcpy_s(mapKey->keyData, filterCreate->pkIdxkey->keyLen, filterCreate->pkIdxkey->keyData,
        filterCreate->pkIdxkey->keyLen);
    if (err != EOK) {
        DbDynMemCtxFree(stmt->session->yangTreeCtx->memCtx, buf);  // mapKey 和 buf 是栈变量不用置空，下同
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy primary key");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    uint32_t hashCode = DbHash32WithSeed(
        filterCreate->pkIdxkey->keyData, filterCreate->pkIdxkey->keyLen, filterCreate->vertexDesc->labelId);
    id = (uint32_t *)(buf + sizeof(IndexKeyT) + mapKey->keyLen);
    *id = filterCreate->vertexId;
    Status ret = DbOamapInsert(&stmt->session->yangTreeCtx->diffCtx->removeIdMap, hashCode, mapKey, id, NULL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbDynMemCtxFree(stmt->session->yangTreeCtx->memCtx, buf);
    }
    return ret;
}

Status DiffAllocLabelCursor(QryStmtT *stmt, QryLabelT *qryLabel, QryLabelCursorT **labelCursor)
{
    GaListT *labelCursorList = &stmt->session->yangTreeCtx->labelCursorList;
    QryLabelCursorT *cursor = NULL;
    for (uint32_t i = 0; i < DbGaListGetCount(labelCursorList); i++) {
        // i < count label不会为NULL
        cursor = (QryLabelCursorT *)DbGaListGet(labelCursorList, i);
        if (cursor->labelDef.vertexLabel->metaCommon.metaId == qryLabel->def.vertexLabel->metaCommon.metaId &&
            !qryLabel->def.vertexLabel->metaCommon.isDeleted) {
            *labelCursor = cursor;
            return GMERR_OK;
        }
    }

    Status ret = DbGaListNew(labelCursorList, sizeof(QryLabelCursorT), (void **)&cursor);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "new gaList, id=%" PRIu32 ".", qryLabel->def.vertexLabel->metaCommon.metaId);
        return ret;
    }
    (void)memset_s(cursor, (uint32_t)sizeof(QryLabelCursorT), 0x00, (uint32_t)sizeof(QryLabelCursorT));
    cursor->action = HEAP_OPTYPE_NORMALREAD;
    ret = QryOpenVertexLabelCursor(stmt, qryLabel, cursor, true);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Open vLabelCursor, vertexName=%s", qryLabel->def.vertexLabel->metaCommon.metaName);
        DbGaListDeleteTail(labelCursorList);
        return ret;
    }
    *labelCursor = cursor;
    return GMERR_OK;
}

void DiffReleaseCacheLabelCursor(QryStmtT *stmt)
{
    GaListT *labelCursorList = &stmt->session->yangTreeCtx->labelCursorList;
    QryLabelCursorT *cursor = NULL;
    for (int32_t i = (int32_t)DbGaListGetCount(labelCursorList) - 1; i >= 0; i--) {
        // i < count label不会为NULL
        cursor = (QryLabelCursorT *)DbGaListGet(labelCursorList, (uint32_t)i);
        QryCloseVertexLabelCursor(cursor);
        DbGaListDelete(labelCursorList, (uint32_t)i);
    }
    return;
}

Status DiffNodeHasLeafListChild(QryStmtT *stmt, uint64_t firstEdge, uint32_t edgeLabelId, bool isOld, bool *hasChild)
{
    *hasChild = false;
    QryEdgeLabelT *qryEdgeLabel = NULL;
    Status ret = QryGetQryEdgeLabelById(stmt, edgeLabelId, &qryEdgeLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (!DmIsLeafListVertexLabel(qryEdgeLabel->destQryLabel->def.vertexLabel)) {
        *hasChild = true;
        return GMERR_OK;
    }

    QryLabelCursorT *labelCursor = NULL;
    ret = DiffAllocLabelCursor(stmt, qryEdgeLabel->destQryLabel, &labelCursor);
    if (ret != GMERR_OK) {
        return ret;
    }
    TupleBufT tupleBuf = {0};

    TupleBufInit(&tupleBuf, stmt->memCtx);
    uint64_t edgeAddr = firstEdge;
    while (edgeAddr != 0) {
        ret = EdgeTopoGetEdgeBufWithEdition(qryEdgeLabel->edgeTopoHandle, edgeAddr, isOld, qryEdgeLabel->edgeTopo);
        if (ret != GMERR_OK) {
            break;
        }
        uint64_t destAddr = DmGetEdgeTopoAddr(qryEdgeLabel->edgeTopo, DEST_VERTEX_ADDR);
        ret = isOld ? HeapFetchHpOldestVisibleTupleBuffer(labelCursor->hpRunHdl, destAddr, &tupleBuf) :
                      HeapFetchHpTupleBuffer(labelCursor->hpRunHdl, destAddr, &tupleBuf);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(
                ret, "Vertex is inexistent, vertex_name=%s.", labelCursor->labelDef.vertexLabel->metaCommon.metaName);
            break;
        }
        bool isSetByServer = false;
        bool isEqualsDefault = false;
        ret = DmGetLeafListDefaultFlagsFromVertexBuf(
            tupleBuf.buf, labelCursor->labelDef.vertexLabel->vertexDesc, &isSetByServer, &isEqualsDefault);
        if (ret != GMERR_OK) {
            break;
        }
        if (!isSetByServer) {
            *hasChild = true;
            TupleBufRelease(&tupleBuf);
            return GMERR_OK;
        }
        edgeAddr = DmGetEdgeTopoAddr(qryEdgeLabel->edgeTopo, SOURCE_VERTEX_NEXT_EDGE_ADDR);
    }
    TupleBufRelease(&tupleBuf);
    return ret;
}

Status DiffNodeHasListChild(
    QryStmtT *stmt, bool isOld, const DmNodeT *node, const DmVertexT *vertex, bool *hasListChild)
{
    *hasListChild = false;
    if (!DmNodeIsCreated(node)) {
        return GMERR_OK;
    }
    if (vertex == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "vertex is null");
        return GMERR_DATA_EXCEPTION;
    }
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    for (uint32_t i = 0; i < vertexDesc->edgeLabelNum; i++) {
        uint32_t edgeLabelId = vertexDesc->commonInfo->relatedEdgeLabels[i].edgeLabelId;
        DmEdgeLabelT *edgeLabel = NULL;
        Status ret = CataGetEdgeLabelById(edgeLabelId, (DmEdgeLabelT **)&edgeLabel, NULL);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get eLabel by id, %" PRIu32, edgeLabelId);
            return ret;
        }
        // 如果当前边关联的没有sourceNode，或者关联的sourcenodeId不是当前要删除的node或者该node的孩子则略过
        if (edgeLabel->sourceNodePath == NULL || !DmSourceNodeIdIsInNode(edgeLabel->sourceUniqueNodeId, node)) {
            (void)CataReleaseEdgeLabel(edgeLabel);
            continue;
        }

        EdgeScanDirectionE edgeDirection = (edgeLabel->sourceVertexLabelId == vertexDesc->labelId) ? EDGE_OUT : EDGE_IN;
        (void)CataReleaseEdgeLabel(edgeLabel);
        if (edgeDirection == EDGE_IN) {
            continue;
        }
        // 对于出边，根据边是否有效判断是否有孩子
        uint64_t edgeAddr = 0;
        ret = DmVertexGetFirstEdgeTopoAddrById(vertex, edgeLabelId, &edgeAddr);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (edgeAddr != 0) {
            ret = DiffNodeHasLeafListChild(stmt, edgeAddr, edgeLabelId, isOld, hasListChild);
            if (ret != GMERR_OK) {
                return ret;
            }
            if (*hasListChild) {
                return GMERR_OK;
            }
        }
    }
    return GMERR_OK;
}

Status DiffNodeIsHadChild(QryStmtT *stmt, bool isOld, const DmNodeT *node, const DmVertexT *vertex, bool *hasChild)
{
    if (DmNodeHasLeafChild(node)) {
        *hasChild = true;
        return GMERR_OK;
    }
    // 判断节点有没有通过边连接的孩子节点
    *hasChild = false;
    Status ret = DiffNodeHasListChild(stmt, isOld, node, vertex, hasChild);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (*hasChild) {
        return GMERR_OK;
    }
    for (uint32_t i = 0; i < node->nodeDesc->nodeNumPerElement; i++) {
        if (DmNodeIsCreated(node->currNodes[i])) {
            if (DmIsPresenceYangInfo(node->currNodes[i]->nodeDesc->yangInfoDesc)) {
                *hasChild = true;
                return GMERR_OK;
            }
            ret = DiffNodeIsHadChild(stmt, isOld, node->currNodes[i], vertex, hasChild);
            if (ret != GMERR_OK) {
                return ret;
            }
            if (*hasChild) {
                return GMERR_OK;
            }
        }
    }
    return GMERR_OK;
}

const char *DiffGetOptypeStr(DiffOpTypeE op)
{
    // DIFF_OPERATION_NONE = 0,  // 表示没有操作，只是维护路径
    // DIFF_OPERATION_MERGE,     // 表示值被修改获取移动位置
    // DIFF_OPERATION_REMOVE,    // 表示节点删除
    // DIFF_OPERATION_CREATE,    // 表示节点创建
    // DIFF_OPERATION_MOVE,      // 表示只单纯移动位置，只会用于resultTree生成
    // DIFF_OPERATION_BUTT
    if (op > DIFF_OPERATION_BUTT) {
        return "ERR";
    }
    static const char *convert[] = {
        "NONE",
        "MERGE",
        "REMOVE",
        "CREATE",
        "MOVE",
        "INVALID",
    };
    return convert[op];
}

// 根据状态去缓存对应的vertex
void DiffResultTreeFreeCacheVertexByState(QryStmtT *stmt, DmVertexLabelT *vertexLabel, DmYangTreeT *filterTree)
{
    DB_POINTER3(stmt, vertexLabel, filterTree);
    switch (filterTree->diffCtx->state) {
        case DM_DIFF_STATE_CREATE: {
            DmYangFreeCacheVertex(&stmt->session->yangTreeCtx->diffCtx->list, vertexLabel, filterTree->newVertex);
            break;
        }
        case DM_DIFF_STATE_REMOVE: {
            DmYangFreeCacheVertex(&stmt->session->yangTreeCtx->diffCtx->list, vertexLabel, filterTree->oldVertex);
            break;
        }
        case DM_DIFF_STATE_INITIAL:
        case DM_DIFF_STATE_INVALID:
            break;
        case DM_DIFF_STATE_NONE:
        case DM_DIFF_STATE_MOVE:
        case DM_DIFF_STATE_MERGE: {
            DmYangFreeCacheVertex(&stmt->session->yangTreeCtx->diffCtx->list, vertexLabel, filterTree->newVertex);
            DmYangFreeCacheVertex(&stmt->session->yangTreeCtx->diffCtx->list, vertexLabel, filterTree->oldVertex);
            break;
        }
        case DM_DIFF_STATE_BUTT:
        default:
            DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "free state, vertexName=%s", vertexLabel->metaCommon.metaName);
            return;
    }
    return;
}

Status CheckCurrentNPHasVisibleDefaultValue(DmVertexT *vertex, DmSchemaT *schema, bool *hasDefault)
{
    for (uint32_t i = 0; i < schema->propeNum; i++) {
        DmPropertySchemaT *propertySchema = &(schema->properties[i]);
        if (propertySchema->defaultValueNum == 0 || propertySchema->defaultValue->type == DB_DATATYPE_BITMAP) {
            continue;
        }
        if (!DmPropeSchemaHasValidWhenClause(propertySchema)) {
            *hasDefault = true;
            return GMERR_OK;
        }
        if (vertex == NULL) {
            *hasDefault = true;
            return GMERR_OK;
        }
        bool isFound = false;
        Status ret = DmGetVertexNpaValueByIndex(vertex, propertySchema->npaIndex, &isFound);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (!isFound) {
            *hasDefault = true;
            return GMERR_OK;
        }
    }
    return GMERR_OK;
}

Status NPVertexSetIsNotEmptyFlagByCurrentTree(
    QryStmtT *stmt, DmYangTreeT *diffNodeTree, DmVertexT *vertex, DmSchemaT *schema, bool isOld)
{
    bool *isNotEmpty = isOld ? &(diffNodeTree->diffCtx->isOldNotEmpty) : &(diffNodeTree->diffCtx->isNewNotEmpty);
    DmVertexT *currVertex = isOld ? diffNodeTree->oldVertex : diffNodeTree->newVertex;
    if (currVertex == NULL) {
        // 无实例，直接遍历属性看是否有可见的默认属性
        return CheckCurrentNPHasVisibleDefaultValue(currVertex, schema, isNotEmpty);
    }
    // 有实例，先看是否有用户设值的属性，没有再看是否有可见的默认属性
    if (DmVertexHasLeafChild(currVertex)) {
        *isNotEmpty = true;
        return GMERR_OK;
    }
    return CheckCurrentNPHasVisibleDefaultValue(currVertex, schema, isNotEmpty);
}

Status VertexSetIsNotEmptyFlagByCurrentTree(QryStmtT *stmt, DmYangTreeT *tree, DmVertexT *vertex, DmSchemaT *schema)
{
    Status ret;
    // 只有在可见且 isNotEmpty 标志位尚未被子节点置为 true 的情况下，需要遍历判断自己的属性
    if (tree->diffCtx->isOldVisible && !tree->diffCtx->isOldNotEmpty) {
        ret = NPVertexSetIsNotEmptyFlagByCurrentTree(stmt, tree, vertex, schema, true);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (tree->diffCtx->isNewVisible && !tree->diffCtx->isNewNotEmpty) {
        ret = NPVertexSetIsNotEmptyFlagByCurrentTree(stmt, tree, vertex, schema, false);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status NPNodeSetIsNotEmptyFlagByCurrentTree(
    QryStmtT *stmt, DmYangTreeT *diffNodeTree, DmVertexT *vertex, DmSchemaT *schema, bool isOld)
{
    bool *isNotEmpty = isOld ? &(diffNodeTree->diffCtx->isOldNotEmpty) : &(diffNodeTree->diffCtx->isNewNotEmpty);
    DmNodeT *node = isOld ? diffNodeTree->oldNode : diffNodeTree->newNode;
    DmVertexT *parentVertex =
        isOld ? diffNodeTree->diffCtx->parentTree->oldVertex : diffNodeTree->diffCtx->parentTree->newVertex;
    if (!DmNodeIsCreated(node)) {
        return CheckCurrentNPHasVisibleDefaultValue(parentVertex, schema, isNotEmpty);
    }

    // 有实例，看是否有用户设值属性，没有再看是否有可见默认属性
    if (DmNodeHasLeafChild(node)) {
        *isNotEmpty = true;
        return GMERR_OK;
    }
    return CheckCurrentNPHasVisibleDefaultValue(parentVertex, schema, isNotEmpty);
}

Status NodeSetIsNotEmptyFlagByCurrentTree(QryStmtT *stmt, DmYangTreeT *tree, DmVertexT *vertex, DmSchemaT *schema)
{
    Status ret;
    // 只有在可见且 isNotEmpty 标志位尚未被子节点置为 true 的情况下，需要遍历判断自己的属性
    if (tree->diffCtx->isOldVisible && !tree->diffCtx->isOldNotEmpty) {
        ret = NPNodeSetIsNotEmptyFlagByCurrentTree(stmt, tree, vertex, schema, true);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (tree->diffCtx->isNewVisible && !tree->diffCtx->isNewNotEmpty) {
        ret = NPNodeSetIsNotEmptyFlagByCurrentTree(stmt, tree, vertex, schema, false);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

inline bool DiffIsLeafTreeWithDefaultValue(DmVertexLabelT *vertexLabel)
{
    if (!DmIsLeafListVertexLabel(vertexLabel)) {
        return false;
    }
    return (vertexLabel->metaVertexLabel->schema->properties[DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT].defaultValueNum > 0);
}

// 在 destVertexLabel 无实例的情况下，检查其是否有可见的 leaf-list 默认值
Status CheckHasVisibleDefaultLeafListChild(DmVertexLabelT *destVertexLabel, DmVertexT *vertex, bool *hasDefault)
{
    DB_POINTER2(destVertexLabel, hasDefault);
    if (!DiffIsLeafTreeWithDefaultValue(destVertexLabel)) {
        return GMERR_OK;
    }
    if (!DmSchemaHasValidWhenClause(destVertexLabel->metaVertexLabel->schema)) {
        *hasDefault = true;
        return GMERR_OK;
    }

    uint8_t defaultValueNum =
        destVertexLabel->metaVertexLabel->schema->properties[DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT].defaultValueNum;
    for (uint8_t i = 0; i < defaultValueNum; ++i) {
        uint8_t npaIdx = DmLeafListLabelGetNpaIndex(destVertexLabel, i);
        bool isFound = false;
        if (vertex == NULL) {
            *hasDefault = true;
            return GMERR_OK;
        }
        Status ret = DmGetVertexNpaValueByIndex(vertex, npaIdx, &isFound);
        if (ret != GMERR_OK) {
            return ret;
        }

        if (!isFound) {
            *hasDefault = true;
            return GMERR_OK;
        }
    }
    return GMERR_OK;
}

Status CheckNonExistNodeHasVisibleDefaultValue(
    QryStmtT *stmt, const DmTreeDescT *desc, DmVertexT *vertex, bool *isNotEmpty)
{
    DmSchemaT *schema = desc->nodeSchema->schema;
    if (DmIsPresenceSchema(schema)) {
        return GMERR_OK;
    }
    // 先看自身有无默认值可见的属性
    Status ret = CheckCurrentNPHasVisibleDefaultValue(vertex, schema, isNotEmpty);
    if (ret != GMERR_OK || *isNotEmpty) {
        return ret;
    }
    // 自身没有，再看子节点
    for (uint32_t i = 0; i < desc->childNum; i++) {
        DmTreeDescT *childDesc = desc->childDesc[i];
        if (childDesc->type == DM_TREE_DESC_NODE) {
            // node 子节点，直接递归判断
            ret = CheckNonExistNodeHasVisibleDefaultValue(stmt, childDesc, vertex, isNotEmpty);
        } else {
            // vertex 子节点只需看有无可见的默认 leaf-list 孩子
            QryLabelT *currLabel = NULL;
            ret = QryGetQryLabelById(stmt, childDesc->metaId, &currLabel);
            if (ret != GMERR_OK) {
                return ret;
            }
            DmVertexLabelT *destVertexLabel = currLabel->def.vertexLabel;
            ret = CheckHasVisibleDefaultLeafListChild(destVertexLabel, vertex, isNotEmpty);
        }
        if (ret != GMERR_OK || *isNotEmpty) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status GetSingleLeafListVisibilityByBuf(QryStmtT *stmt, HeapTupleBufT *heapTupleBuf, DmVertexT *parentVertex,
    DmVertexLabelT *vertexLabel, DmVertexT *vertex, bool *isVisible)
{
    bool isSetByServer = false;
    bool isEqualsDefault = false;
    Status ret =
        DmGetLeafListDefaultFlagsFromVertexBuf(heapTupleBuf->buf, vertex->vertexDesc, &isSetByServer, &isEqualsDefault);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (!isSetByServer) {
        *isVisible = true;
        return GMERR_OK;
    }
    if (DiffFetchModeIsExplicit(stmt)) {
        return GMERR_OK;
    }
    ret = DmDeSerialize2ExistsVertex(heapTupleBuf->buf, heapTupleBuf->bufSize, vertex, DmGetCheckMode());
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "deserialize vertex, vertex_name=%s.", vertex->vertexDesc->labelName);
        return ret;
    }

    uint32_t defaultIndex = DmYangGetLeafListDefaultValueIdx(vertex->vertexDesc, vertex);
    if (defaultIndex == DB_INVALID_UINT32) {
        DB_LOG_ERROR(
            GMERR_DATA_EXCEPTION, "no match any default value, vertex_name=%s.", vertex->vertexDesc->labelName);
        return GMERR_DATA_EXCEPTION;
    }

    uint8_t npaIdx = DmLeafListLabelGetNpaIndex(vertexLabel, defaultIndex);
    bool isFound = false;
    ret = DmGetVertexNpaValueByIndex(parentVertex, npaIdx, &isFound);
    if (ret != GMERR_OK) {
        return ret;
    }

    *isVisible = !isFound;
    return GMERR_OK;
}

Status CheckHasVisibleLeafListChild(
    QryStmtT *stmt, uint64_t firstEdge, uint32_t edgeLabelId, DmVertexT *parentVertex, bool isOld, bool *isNotEmpty)
{
    QryEdgeLabelT *qryEdgeLabel = NULL;
    Status ret = QryGetQryEdgeLabelById(stmt, edgeLabelId, &qryEdgeLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    QryLabelCursorT *labelCursor = NULL;
    ret = DiffAllocLabelCursor(stmt, qryEdgeLabel->destQryLabel, &labelCursor);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmVertexLabelT *vertexLabel = labelCursor->labelDef.vertexLabel;
    TupleBufT tupleBuf = {0};

    TupleBufInit(&tupleBuf, stmt->memCtx);
    uint64_t edgeAddr = firstEdge;
    DmVertexT *vertex = NULL;
    ret = DmYangAllocCacheVertex(&stmt->session->yangTreeCtx->diffCtx->list, vertexLabel, NULL, &vertex);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 遍历出边所连接的 leaf-list 孩子，看是否有用户实例 ，或可见的默认实例
    while (edgeAddr != DIFF_INVALID_EDGE_ADDR) {
        ret = EdgeTopoGetEdgeBufWithEdition(qryEdgeLabel->edgeTopoHandle, edgeAddr, isOld, qryEdgeLabel->edgeTopo);
        if (ret != GMERR_OK) {
            break;
        }
        uint64_t destAddr = DmGetEdgeTopoAddr(qryEdgeLabel->edgeTopo, DEST_VERTEX_ADDR);

        ret = !isOld ? HeapFetchHpTupleBuffer(labelCursor->hpRunHdl, destAddr, &tupleBuf) :
                       HeapFetchHpOldestVisibleTupleBuffer(labelCursor->hpRunHdl, destAddr, &tupleBuf);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Vertex is inexistent, vertex_name=%s.", vertexLabel->metaCommon.metaName);
            break;
        }
        TupleT tuple = TupleBufGet(&tupleBuf);
        ret = GetSingleLeafListVisibilityByBuf(stmt, &tuple, parentVertex, vertexLabel, vertex, isNotEmpty);
        if (ret != GMERR_OK || *isNotEmpty) {
            break;
        }
        edgeAddr = DmGetEdgeTopoAddr(qryEdgeLabel->edgeTopo, SOURCE_VERTEX_NEXT_EDGE_ADDR);
    }

    DmYangFreeCacheVertex(&stmt->session->yangTreeCtx->diffCtx->list, vertexLabel, vertex);
    TupleBufRelease(&tupleBuf);
    return ret;
}

static Status CheckListChildIsEmpty(
    QryStmtT *stmt, const DmTreeDescT *desc, DmVertexT *srcVertex, bool isOld, bool *isNotEmpty)
{
    QryLabelT *destQryLabel = NULL;
    Status ret = QryGetQryLabelById(stmt, desc->metaId, &destQryLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmVertexLabelT *destVertexLabel = destQryLabel->def.vertexLabel;

    uint32_t edgeLabelId = srcVertex->vertexDesc->commonInfo->relatedEdgeLabels[desc->index].edgeLabelId;
    uint64_t edgeAddr = DIFF_INVALID_EDGE_ADDR;
    ret = DmVertexGetFirstEdgeTopoAddrById(srcVertex, edgeLabelId, &edgeAddr);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (edgeAddr == DIFF_INVALID_EDGE_ADDR) {
        if (!DiffFetchModeIsExplicit(stmt)) {
            // 该 list 孩子无实例，则直接看是否有可见的 leaf-list 默认值
            return CheckHasVisibleDefaultLeafListChild(destVertexLabel, srcVertex, isNotEmpty);
        }
        return GMERR_OK;
    }

    // 有连接非 leaf-list 实例的出边，或非explicit模式下，连接的 leaf-list 无 when 定义，则必不为空
    if (!DmIsLeafListVertexLabel(destVertexLabel) ||
        (!DiffFetchModeIsExplicit(stmt) && !DmSchemaHasValidWhenClause(destVertexLabel->metaVertexLabel->schema))) {
        *isNotEmpty = true;
        return GMERR_OK;
    }

    // 出边连接的是有 when 定义的 leaf-list，则需要进一步看是否有可见默认值或用户实例
    return CheckHasVisibleLeafListChild(stmt, edgeAddr, edgeLabelId, srcVertex, isOld, isNotEmpty);
}

Status CheckExistNodeIsEmpty(QryStmtT *stmt, DmNodeT *node, const DmTreeDescT *desc, DmYangTreeT *tree, bool isOld);

static Status CheckNodeChildIsEmpty(
    QryStmtT *stmt, DmNodeT *childNode, const DmTreeDescT *childDesc, DmYangTreeT *tree, bool isOld)
{
    if (DmNodeIsCreated(childNode)) {
        return CheckExistNodeIsEmpty(stmt, childNode, childDesc, tree, isOld);
    }
    if (DiffFetchModeIsExplicit(stmt)) {
        return GMERR_OK;
    }
    DmVertexT *vertex = isOld ? tree->diffCtx->parentTree->oldVertex : tree->diffCtx->parentTree->newVertex;
    bool *isNotEmpty = isOld ? &(tree->diffCtx->isOldNotEmpty) : &(tree->diffCtx->isNewNotEmpty);
    return CheckNonExistNodeHasVisibleDefaultValue(stmt, childDesc, vertex, isNotEmpty);
}

Status CheckExistNodeIsEmpty(QryStmtT *stmt, DmNodeT *node, const DmTreeDescT *desc, DmYangTreeT *tree, bool isOld)
{
    DmVertexT *vertex = isOld ? tree->diffCtx->parentTree->oldVertex : tree->diffCtx->parentTree->newVertex;
    bool *isNotEmpty = isOld ? &(tree->diffCtx->isOldNotEmpty) : &(tree->diffCtx->isNewNotEmpty);
    DmSchemaT *schema = desc->nodeSchema->schema;
    if (DmIsNonDefaultCaseSchema(schema)) {
        // 判断非默认 case 是否有意义
        return DiffNodeIsHadChild(stmt, isOld, node, vertex, isNotEmpty);
    }
    // 有实例的情况下，若为 P 节点则一定有意义。若为 NP 节点，先检查自身是否有用户设值的属性，有的话也有意义
    if (DmIsPresenceSchema(schema) || DmNodeHasLeafChild(node)) {
        *isNotEmpty = true;
        return GMERR_OK;
    }

    // 检查自身是否有默认值可见的属性
    Status ret = GMERR_OK;
    if (!DiffFetchModeIsExplicit(stmt)) {
        ret = CheckCurrentNPHasVisibleDefaultValue(vertex, schema, isNotEmpty);
        if (ret != GMERR_OK || *isNotEmpty) {
            return ret;
        }
    }

    // 自身没有，再看子节点
    for (uint32_t i = 0; i < desc->childNum; i++) {
        DmTreeDescT *childDesc = desc->childDesc[i];
        if (childDesc->type == DM_TREE_DESC_NODE) {
            // currNodes[i]为NULL，不用处理该node
            DmNodeT *childNode = node->currNodes[i];
            ret = CheckNodeChildIsEmpty(stmt, childNode, childDesc, tree, isOld);
        } else {
            ret = CheckListChildIsEmpty(stmt, childDesc, vertex, isOld, isNotEmpty);
        }
        if (ret != GMERR_OK || *isNotEmpty) {
            return ret;
        }
    }
    return GMERR_OK;
}

static inline bool ParentOfNPHasNoInstance(const DmTreeDescT *parentDesc, DmVertexT *vertex, DmNodeT *node)
{
    return ((parentDesc->type == DM_TREE_DESC_NODE && (!DmNodeIsCreated(node))) ||
            (parentDesc->type == DM_TREE_DESC_VERTEX && vertex == NULL));
}

static Status CheckNPHasNotEmptyChild(
    QryStmtT *stmt, DmYangTreeT *tree, const DmTreeDescT *desc, bool isOld, bool *isNotEmpty)
{
    DB_POINTER4(stmt, tree, desc, isNotEmpty);
    DmVertexT *vertex = isOld ? tree->diffCtx->parentTree->oldVertex : tree->diffCtx->parentTree->newVertex;
    DmNodeT *node = isOld ? tree->oldNode : tree->newNode;
    Status ret;
    DmNodeT *childNode = NULL;
    for (uint32_t i = 0; i < desc->childNum; i++) {
        DmTreeDescT *childDesc = desc->childDesc[i];
        if (childDesc->type == DM_TREE_DESC_NODE) {
            // node 孩子除了以下两种情况之外都会生成 diff 树且只有一棵。
            // 有 diff 树的之前一定已经判断过是否为空了，故不用处理。 两种不生成 diff 树的情况是：
            // 1. 父节点操作为 none 且该子节点未被访问到；
            // 2. 该子节点前后均不可见
            // 只有第一种情况需要检查该子节点是否非空
            if (tree->childTreeCtx[i].tree != NULL || tree->diffCtx->state != DM_DIFF_STATE_NONE) {
                continue;
            }
            if (!DiffFetchModeIsExplicit(stmt) && ParentOfNPHasNoInstance(desc, vertex, node)) {
                // 父节点无实例的情况，直接检查该 node 孩子是否有可见默认值
                ret = CheckNonExistNodeHasVisibleDefaultValue(stmt, childDesc, vertex, isNotEmpty);
            } else if (desc->type == DM_TREE_DESC_VERTEX && vertex != NULL) {
                // 父节点为 vertex 且有实例的情况
                childNode = vertex->nodes[childDesc->index];
                ret = CheckNodeChildIsEmpty(stmt, childNode, childDesc, tree, isOld);
            } else {
                // 父节点为 node 且有实例的情况
                DB_POINTER(vertex);  // node 有实例，其上层的 vertex 也一定有实例
                childNode = node->currNodes[childDesc->index];  // childNode为NULL不用额外处理
                ret = CheckNodeChildIsEmpty(stmt, childNode, childDesc, tree, isOld);
            }
            if (ret != GMERR_OK || *isNotEmpty) {
                return ret;
            }
            continue;
        }

        // vertex 孩子通过有无可见实例判断
        if (vertex != NULL) {
            ret = CheckListChildIsEmpty(stmt, childDesc, vertex, isOld, isNotEmpty);
        } else {
            // 父节点无实例，非explicit则只需看有无可见的默认 leaf-list 孩子，explicit不可见
            if (DiffFetchModeIsExplicit(stmt)) {
                continue;
            }
            QryLabelT *currLabel = NULL;
            ret = QryGetQryLabelById(stmt, childDesc->metaId, &currLabel);
            if (ret != GMERR_OK) {
                return ret;
            }
            DmVertexLabelT *destVertexLabel = currLabel->def.vertexLabel;
            ret = CheckHasVisibleDefaultLeafListChild(destVertexLabel, vertex, isNotEmpty);
        }
        if (ret != GMERR_OK || *isNotEmpty) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status SetIsNotEmptyFlagByNoDiffChild(QryStmtT *stmt, DmYangTreeT *tree, const DmTreeDescT *desc)
{
    Status ret = GMERR_OK;
    // 只有在该节点可见但前面有diff树的子节点和自身属性依然不能保证isNotEmpty标志位为true的情况下，
    // 才需要遍历无 diff 树的子节点。
    if (tree->diffCtx->isOldVisible && !tree->diffCtx->isOldNotEmpty) {
        ret = CheckNPHasNotEmptyChild(stmt, tree, desc, true, &(tree->diffCtx->isOldNotEmpty));
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (tree->diffCtx->isNewVisible && !tree->diffCtx->isNewNotEmpty) {
        ret = CheckNPHasNotEmptyChild(stmt, tree, desc, false, &(tree->diffCtx->isNewNotEmpty));
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

void VertexSetIsNotEmptyFlagWithExplictMode(DmYangTreeT *tree)
{
    DmSchemaT *schema = tree->vertexInfo->vertexLabel->metaVertexLabel->schema;
    // 除 NP 类型根节点之外，list、leaf-list 是否有意义都只取决于其自身可见性，可见性在前面已经判断完了
    if (!(DmIsRootYangVertexLabel(tree->vertexInfo->vertexLabel) && DmIsNonPresenceSchema(schema))) {
        tree->diffCtx->isOldNotEmpty = tree->diffCtx->isOldVisible;
        tree->diffCtx->isNewNotEmpty = tree->diffCtx->isNewVisible;
        return;
    }
    // 只有在可见且 isNotEmpty 标志位尚未被子节点置为 true 的情况下，需要遍历判断自己的属性
    if (tree->diffCtx->isOldVisible && !tree->diffCtx->isOldNotEmpty) {
        tree->diffCtx->isOldNotEmpty = false;
        // 有实例，先看是否有用户设值的属性，没有再看是否有可见的默认属性
        if (tree->oldVertex != NULL && DmVertexHasLeafChild(tree->oldVertex)) {
            tree->diffCtx->isOldNotEmpty = true;
        }
    }
    if (tree->diffCtx->isNewVisible && !tree->diffCtx->isNewNotEmpty) {
        tree->diffCtx->isNewNotEmpty = false;
        // 有实例，先看是否有用户设值的属性，没有再看是否有可见的默认属性
        if (tree->newVertex != NULL && DmVertexHasLeafChild(tree->newVertex)) {
            tree->diffCtx->isNewNotEmpty = true;
        }
    }
}

void NodeSetIsNotEmptyFlagWithExplictMode(DmYangTreeT *tree, const DmTreeDescT *desc)
{
    DmSchemaT *schema = desc->nodeSchema->schema;
    if (DmIsNonDefaultCaseSchema(schema)) {
        return;  // 非默认 case 是否有意义在前面判断并赋值，不在这里判断
    }
    if (DmIsPresenceSchema(schema)) {
        // P 节点是否有意义只取决于其自身可见性，可见性在前面已经判断完了
        tree->diffCtx->isOldNotEmpty = tree->diffCtx->isOldVisible;
        tree->diffCtx->isNewNotEmpty = tree->diffCtx->isNewVisible;
        return;
    }
    // 只有在可见且 isNotEmpty 标志位尚未被子节点置为 true 的情况下，需要遍历判断自己的属性
    if (tree->diffCtx->isOldVisible && !tree->diffCtx->isOldNotEmpty) {
        tree->diffCtx->isOldNotEmpty = false;
        // 有实例，先看是否有用户设值的属性，没有再看是否有可见的默认属性
        if (tree->oldNode != NULL && tree->oldNode->isCreated && DmNodeHasLeafChild(tree->oldNode)) {
            tree->diffCtx->isOldNotEmpty = true;
        }
    }
    if (tree->diffCtx->isNewVisible && !tree->diffCtx->isNewNotEmpty) {
        tree->diffCtx->isNewNotEmpty = false;
        // 有实例，先看是否有用户设值的属性，没有再看是否有可见的默认属性
        if (tree->newNode != NULL && tree->newNode->isCreated && DmNodeHasLeafChild(tree->newNode)) {
            tree->diffCtx->isNewNotEmpty = true;
        }
    }
}

Status DiffSetTreeIsNotEmptyFlagsWithExplictMode(QryStmtT *stmt, DmYangTreeT *tree, const DmTreeDescT *desc)
{
    if (DmIsVertexTree(tree)) {
        VertexSetIsNotEmptyFlagWithExplictMode(tree);
    } else {
        NodeSetIsNotEmptyFlagWithExplictMode(tree, desc);
        if (DmIsNonDefaultCaseSchema(desc->nodeSchema->schema)) {
            return GMERR_OK;  // 非默认 case 是否有意义在前面判断并赋值，不在这里判断
        }
    }
    return SetIsNotEmptyFlagByNoDiffChild(stmt, tree, desc);
}

// 对 NP 类型节点而言，isVisible 标志位为 true 并不代表该节点就可见，还需判断该节点是否有意义，无意义的也视为不可见
// 故此处进行判断，并将结果记录到两个 isNotEmpty 标志位
Status DiffSetTreeIsNotEmptyFlags(QryStmtT *stmt, DmYangTreeT *tree, const DmTreeDescT *desc)
{
    if (DiffFetchModeIsExplicit(stmt)) {
        return DiffSetTreeIsNotEmptyFlagsWithExplictMode(stmt, tree, desc);
    }
    DmVertexT *vertex = DmGetYangTreeVertex(tree->diffCtx->parentTree);
    if (vertex == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "get vertex from diff tree");
        return GMERR_DATA_EXCEPTION;
    }

    Status ret;
    DmSchemaT *schema = NULL;
    if (DmIsVertexTree(tree)) {
        schema = tree->vertexInfo->vertexLabel->metaVertexLabel->schema;
        // 除 NP 类型根节点之外，list、leaf-list 是否有意义都只取决于其自身可见性，可见性在前面已经判断完了
        if (!(DmIsRootYangVertexLabel(tree->vertexInfo->vertexLabel) && DmIsNonPresenceSchema(schema))) {
            tree->diffCtx->isOldNotEmpty = tree->diffCtx->isOldVisible;
            tree->diffCtx->isNewNotEmpty = tree->diffCtx->isNewVisible;
            return GMERR_OK;
        }
        // 遍历自身属性看是否有设值或默认值可见的属性
        ret = VertexSetIsNotEmptyFlagByCurrentTree(stmt, tree, vertex, schema);
    } else {
        schema = desc->nodeSchema->schema;
        if (DmIsNonDefaultCaseSchema(schema)) {
            return GMERR_OK;  // 非默认 case 是否有意义在前面判断并赋值，不在这里判断
        }
        if (DmIsPresenceSchema(schema)) {
            // P 节点是否有意义只取决于其自身可见性，可见性在前面已经判断完了
            tree->diffCtx->isOldNotEmpty = tree->diffCtx->isOldVisible;
            tree->diffCtx->isNewNotEmpty = tree->diffCtx->isNewVisible;
            return GMERR_OK;
        }
        // 遍历自身属性看是否有设值或默认值可见的属性
        ret = NodeSetIsNotEmptyFlagByCurrentTree(stmt, tree, vertex, schema);
    }
    if (ret != GMERR_OK) {
        return ret;
    }

    // 可能还需要遍历检查未产生 diff 树的子节点
    return SetIsNotEmptyFlagByNoDiffChild(stmt, tree, desc);
}

Status YangTreeUpdateDmVertex(DmYangTreeT *tree)
{
    if (!DmIsVertexTree(tree)) {
        return GMERR_OK;
    }

    Status ret = GMERR_OK;
    if (tree->vertexInfo->newVertexBuffer != NULL && tree->newVertex != NULL) {
        ret = DmDeSerialize2ExistsVertex(  // 数据来源于存储，可信，不校验
            tree->vertexInfo->newVertexBuffer->buf, tree->vertexInfo->newVertexBuffer->len, tree->newVertex, false);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "deserialize to new vertex.");
            return ret;
        }
    }

    if (tree->vertexInfo->oldVertexBuffer != NULL && tree->oldVertex != NULL) {
        ret = DmDeSerialize2ExistsVertex(  // 数据来源于存储，可信，不校验
            tree->vertexInfo->oldVertexBuffer->buf, tree->vertexInfo->oldVertexBuffer->len, tree->oldVertex, false);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "deserialize to old vertex.");
            return ret;
        }
    }

    return GMERR_OK;
}

DmDiffSubsModeE GetDiffSubsMode(DmVertexDescT *vertexDesc)
{
    if (vertexDesc->commonInfo->metaInfoAddr->subscriptionNum == 0) {
        return DM_DIFF_SUBS_NONE;
    }
    if (vertexDesc->commonInfo->metaInfoAddr->subEventNums[DM_SUBS_EVENT_DIFF] > 0 &&
        vertexDesc->commonInfo->metaInfoAddr->subEventNums[DM_SUBS_EVENT_DIFF_EXPLICIT] == 0) {
        return DM_DIFF_SUBS_REPORT_ALL;
    } else if (vertexDesc->commonInfo->metaInfoAddr->subEventNums[DM_SUBS_EVENT_DIFF] == 0 &&
               vertexDesc->commonInfo->metaInfoAddr->subEventNums[DM_SUBS_EVENT_DIFF_EXPLICIT] > 0) {
        return DM_DIFF_SUBS_EXPLICIT;
    } else if (vertexDesc->commonInfo->metaInfoAddr->subEventNums[DM_SUBS_EVENT_DIFF] > 0 &&
               vertexDesc->commonInfo->metaInfoAddr->subEventNums[DM_SUBS_EVENT_DIFF_EXPLICIT] > 0) {
        return DM_DIFF_SUBS_BOTH;
    }
    return DM_DIFF_SUBS_NONE;
}

#ifdef __cplusplus
}
#endif
