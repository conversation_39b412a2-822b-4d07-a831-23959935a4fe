/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Implementation of delete vertex.
 * Author: GMDBv5 EE TEAM
 * Create: 2023-09-23
 */

#include "ee_dml_vertex.h"
#include "ee_dml_query.h"
#include "ee_dml_sync.h"
#include "ee_check.h"
#include "ee_statistic.h"
#include "ee_error_path.h"
#include "ee_tuplebuf_array.h"
#include "ee_dml_vertex_heap_batch.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_DIFF_HEAP_PAGE_NUM 2  // 超过这个阈值后，heap batch delete的时候需要对pageId排序以取得更好的性能

static Status QryExecuteMarkDeleteSingleVertex(QryStmtT *stmt, QryCursorT *qryCursor, const QryVertexDataT *vertexData)
{
    DB_POINTER3(stmt, qryCursor, vertexData);
    QryLabelCursorT *labelCursor = &qryCursor->labelCursors[0];

    Status ret = QryExecuteMarkDeleteSingleVertexWithSub(stmt, labelCursor);
    if (ret != GMERR_OK) {
        return ret;
    }

    qryCursor->affectRows++;
    return GMERR_OK;
}

static Status QryExecuteMarkDeleteStMgVertex(QryStmtT *stmt, QryCursorT *qryCursor, PlanNodeT *plan)
{
    DB_POINTER3(stmt, qryCursor, plan);
    Status ret;
    QryVertexParamT *deleteParams = (QryVertexParamT *)stmt->execParams;
    uint32_t deleteNum = deleteParams->count;
    QryLabelCursorT *labelCursor = &qryCursor->labelCursors[0];

    for (uint32_t i = 0; i < deleteNum; i++) {
        QryVertexDataT *vertexItem = &deleteParams->vertexData[i];
        QryResetBatchCursor(qryCursor, labelCursor, &vertexItem->indexKey);
        ret = QryExecuteUpdateOrDeleteVertexInner(stmt, qryCursor, plan, vertexItem, QryExecuteMarkDeleteSingleVertex);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    DmVertexLabelT *vertexLabel = labelCursor->labelDef.vertexLabel;
    return QryAddGcTaskToDataSet(&stmt->session->stmgSubDataSet, vertexLabel->metaCommon.metaId);
}

static Status QryExecuteMarkDeleteVertex(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryDeleteVertexDescT *deleteCtx = (QryDeleteVertexDescT *)stmt->context->entry;
    DeletePlanT *deletePlan = &deleteCtx->plan->deletePlan;
    PlanNodeT *plan = deletePlan->next->qryVertexPlan.next;
    QryCursorT *qryCursor = (QryCursorT *)stmt->qryCursor;
    qryCursor->labelCursors[0].action = HEAP_OPTYPE_DELETE;
    Status ret;

    if (stmt->splitInfo.isFirst) {
        ret = QryExecPlan(stmt, qryCursor, plan, 0);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "execute mark delete plan.");
            return ret;
        }
        ret =
            QryAppendStMgPubsubDataSet(&stmt->session->stmgSubDataSet, qryCursor->labelCursors[0].labelDef.vertexLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 使用链表undo时必须调这个函数，防止超限
        SeTransSetDmlHint4RangeUpdate(stmt->session->seInstance);
    }
    if (!QrySchemaVersionExistCheck(qryCursor->labelCursors[0].labelDef.vertexLabel, deleteCtx->query->uuid)) {
        return GMERR_UNDEFINED_TABLE;
    }
    ret = QryExecuteMarkDeleteStMgVertex(stmt, qryCursor, plan);
    if (ret != GMERR_OK) {
        QrySetErrorInfoWhenReplay(stmt, ret);
#ifdef FEATURE_YANG
        DmVertexLabelT *vertexLabel = qryCursor->labelCursors[0].labelDef.vertexLabel;
        QryBuildErrorPath(stmt, ret, NULL, vertexLabel, QRY_YANG_ERROR_PATH_NOT_EXIST);
#endif
        DB_LOG_ERROR(ret, "execute mark delete vertex, vertexLabel=%s.",
            qryCursor->labelCursors[0].labelDef.vertexLabel->metaCommon.metaName);
        return ret;
    }

    return QryMakeRespForDmlOperation(stmt, qryCursor->affectRows);
}

static bool QryIsPrimaryIdxDelForChLabel(PlanNodeT *plan)
{
    DB_POINTER(plan);
    // 如果非主键索引扫描则直接返回false
    if (plan->type != PLAN_NODE_SCAN_VERTEX) {
        return false;
    }

    if (plan->scanPlan.scanMode != SCAN_INDEX_PRIMARY) {
        return false;
    }

    // 需要走QryExecPlan，不能走批量主键删除
    if (plan->scanPlan.indexKey != NULL) {
        return false;
    }

    // 如果为条件扫描直接返回false
    if (plan->scanPlan.cond != NULL) {
        return false;
    }

    // 如果qryLabel为NULL返回false
    if (plan->scanPlan.qryLabel == NULL) {
        return false;
    }

    if (plan->scanPlan.qryLabel->def.labelType != VERTEX_LABEL) {
        return false;
    }

    DmVertexLabelT *vertexLabel = plan->scanPlan.qryLabel->def.vertexLabel;
    // vertexLabel为非空，聚簇容器类型，去除视图查询的情况
    if (!(vertexLabel != NULL && vertexLabel->metaVertexLabel->containerType == CONTAINER_CLUSTERED_HASH &&
            (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_NORMAL ||
                vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_DATALOG))) {
        return false;
    }
    return true;
}

inline static bool IsSupportBatchDelete(QryLabelCursorT *labelCursor)
{
    DB_POINTER(labelCursor);
    DmVertexLabelT *vertexLabel = labelCursor->labelDef.vertexLabel;
    /***
     * 1. 批量删除主要支持主键删除
     * 2. 不支持有资源列，自增列，Bitmap，数据同步表，建边的批量删除；
     * 3. RC模式下，批量删除先操作索引后操作heap，导致未记录undo日志，要屏蔽掉CONCURRENCY_CONTROL_NORMAL
     ***/
    return labelCursor->scanMode == SCAN_INDEX_PRIMARY && vertexLabel->commonInfo->resColInfo == NULL &&
           vertexLabel->commonInfo->autoIncrPropNum == 0 &&
           !labelCursor->vertexNode->vertex->vertexDesc->hasBitMapProp &&
           !vertexLabel->commonInfo->dlrInfo.isDataSyncLabel && vertexLabel->commonInfo->edgeLabelNum == 0 &&
           vertexLabel->commonInfo->heapInfo.ccType != CONCURRENCY_CONTROL_NORMAL;
}

static int32_t KeyBytesCmp(const uint8_t *input1, const uint8_t *input2, uint32_t len1, uint32_t len2)
{
    if (len1 == len2) {
        return memcmp(input1, input2, len1);
    }
    if (len1 > len2) {
        return 1;
    }
    return -1;
}

static int QryCmpTupleBufArrayKeyForSort(const void *p1, const void *p2)
{
    const IndexKeyT *key1 = (const IndexKeyT *)p1;
    const IndexKeyT *key2 = (const IndexKeyT *)p2;
    return KeyBytesCmp((uint8_t *)(key1->keyData), (uint8_t *)(key2->keyData), key1->keyLen, key2->keyLen);
}

static uint32_t QryRemoveDuplicateTupleBufArrayKey(IndexKeyT *idxKeys, uint32_t size)
{
    if (SECUREC_UNLIKELY(size <= 1)) {
        return size;
    }
    // 排序
    qsort(idxKeys, size, sizeof(IndexKeyT), QryCmpTupleBufArrayKeyForSort);
    // 去重
    uint32_t realIndex = 0;
    for (uint32_t i = 1; i < size; i++) {
        // 先判断是否重复
        if (KeyBytesCmp(idxKeys[realIndex].keyData, idxKeys[i].keyData, idxKeys[realIndex].keyLen, idxKeys[i].keyLen) ==
            0) {
            continue;
        }
        realIndex++;
        // 非重复下标等于记录下标则不拷贝
        if (realIndex == i) {
            continue;
        }
        // 覆盖重复部分
        idxKeys[realIndex] = idxKeys[i];
    }
    // 返回去重后的记录数
    return realIndex + 1;
}

static Status QryCategorizeDeleteAndNonDelete(
    IndexCtxT *idxCtx, IdxBatchLookupIterT *iter, IdxTupleOrIterT addrOrIter, uint32_t pos, IdxValueTypeE valueType)
{
    DB_POINTER2(idxCtx, iter);
    Status ret = GMERR_OK;
    if (valueType == IDX_IS_TUPLE_ADDR) {
        HpTupleAddr addr = addrOrIter.addr;
        QryBatchLookupIterT *iterator = (QryBatchLookupIterT *)(void *)iter;
        bool isAged = false;
        DmVertexDescT *vertexDesc = iterator->delLabelDef.vertexLabel->vertexDesc;
        TupleT tuple = TupleBufGet(&idxCtx->tupleBuf);
        ret = QryIsVertexBufAged(&tuple, iterator->delLabelDef.vertexLabel, &isAged, vertexDesc);
        if (ret != GMERR_OK || isAged) {
            return ret;
        }
        QryTupleBufArrayT *tupleBufArray = iterator->tupleBufArray;
        uint32_t deleteIter = iterator->deleteIter;
        // 缓存待删除tuple的数据addr
        TupleBufCopy(&tupleBufArray->oldTuples[deleteIter].tupleBuf, &idxCtx->tupleBuf);
        tupleBufArray->oldTuples[deleteIter].dataStatus = iterator->dataStatus;
        tupleBufArray->tuplesAddr[deleteIter] = addr;        // 缓存待删除的tuple的逻辑addr
        tupleBufArray->batchOut[deleteIter].addrOut = addr;  // 缓存待删除的tuple的逻辑addr
        tupleBufArray->outIndexKeys[deleteIter] = tupleBufArray->indexKeys[pos];
        iterator->deleteIter++;

        if (iterator->latestPageId != ((HpItemPointerT *)(void *)&(addr))->pageId) {
            iterator->diffHeapPageNum++;
            iterator->latestPageId = ((HpItemPointerT *)(void *)&(addr))->pageId;
        }
    }
    return ret;
}

static Status CategorizeDeleteAndNonDeleteOps(QryLabelCursorT *labelCursor, const QryVertexParamT *deleteParams,
    QryTupleBufArrayT *tupleBufArray, bool needRemoveDuplicate)
{
    Status ret = GMERR_OK;
    uint32_t vertexCount = deleteParams->count;
    QryVertexDataT *vertexData = deleteParams->vertexData;
    IndexKeyT *idxKeys = tupleBufArray->indexKeys;
    for (register uint32_t i = 0; i < vertexCount; i++) {
        if (vertexData[i].indexKey.leftValue.value == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "leftValue is NULL, vertexData id = %" PRIu32 "", i);
            return GMERR_DATA_EXCEPTION;
        }
        idxKeys[i].keyData = vertexData[i].indexKey.leftValue.value->keyBuf;
        idxKeys[i].keyLen = vertexData[i].indexKey.leftValue.value->keyBufLen;
    }
    uint32_t realCount = vertexCount;
    // 有订阅时才需要去重
    if (needRemoveDuplicate) {
        // 按key去重（保留尾部重复数据,后续操作按deleteNum和修改后的tupleNum遍历不会访问到该部分内容.这部分内存仍由上层释放）
        realCount = QryRemoveDuplicateTupleBufArrayKey(idxKeys, vertexCount);
        // 去重后修改tupleNum,避免删除索引时遍历到重复内容
        tupleBufArray->tupleNum = realCount;
    }

    QryBatchLookupIterT iter;
    iter.tupleBufArray = tupleBufArray;
    iter.delLabelDef = labelCursor->labelDef;
    iter.dataStatus = labelCursor->heapTuple.dataStatus;
    iter.deleteIter = 0u;
    iter.diffHeapPageNum = 0u;
    iter.latestPageId = 0u;

    IdxBatchLookupParaT para = {
        // 注入对比函数
        .categorizeFunc = QryCategorizeDeleteAndNonDelete,
        .iter = (IdxBatchLookupIterT *)(void *)&iter,
    };
#ifdef FEATURE_HAC
    QryJudgeHacIndexSupportKeyCmp(labelCursor, labelCursor->idxCtx);
#endif
    ret = IdxBatchLookup(labelCursor->idxCtx, tupleBufArray->indexKeys, realCount, &para);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "batch lookup index, vertexCnt=%" PRIu32 ".", realCount);
        return ret;
    }

    tupleBufArray->deleteNum = iter.deleteIter;
    // batch的第一个item里面的isHeapPageSort生效
    tupleBufArray->batchOut->isHeapPageSort = (iter.diffHeapPageNum > MAX_DIFF_HEAP_PAGE_NUM);
    return ret;
}

static Status QryBatchExecDeleteSubsMessage(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, const QryTupleBufArrayT *tupleBufArray)
{
    Status ret;
    QrySubsRowDataSetT rowDataSet;
    rowDataSet.rowsDataNum = 0;
    DmSubsEventE eventType = DM_SUBS_EVENT_DELETE;
    DmVertexLabelT *vertexLabel = labelCursor->labelDef.vertexLabel;
    for (register uint32_t i = 0; i < tupleBufArray->deleteNum; i++) {
        DmSubsEventE eventTypeCur;
        if ((tupleBufArray->oldTuples[i].dataStatus & QRY_OLD_DATA_STATUS_AGE) == 0) {
            eventTypeCur = DM_SUBS_EVENT_DELETE;
        } else {
            eventTypeCur = DM_SUBS_EVENT_AGED;
        }
        ret = QryBatchExecCheckLastSubsMessage(stmt, vertexLabel, &rowDataSet, &eventType, eventTypeCur);
        if (ret != GMERR_OK) {
            return ret;
        }
        rowDataSet.rowsData[rowDataSet.rowsDataNum].oldTupleBuf = TupleBufGet(&tupleBufArray->oldTuples[i].tupleBuf);
        ret = QryBatchExecCheckCurSubsMessage(stmt, vertexLabel, &rowDataSet, eventType);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return QryBatchExecCheckEndSubsMessage(stmt, vertexLabel, &rowDataSet, eventType);
}

static Status QryBatchExecDeleteSingleIndex(QryStmtT *stmt, IndexRemoveParaT removePara,
    QryTupleBufArrayT *tupleBufArray, IndexCtxT *secIndexCtx, const DmIndexKeyBufInfoT *keyInfo)
{
    DB_ASSERT(!secIndexCtx->idxOpenCfg.indexLabel->isKvLabel);
    uint32_t maxKeyLen = secIndexCtx->idxOpenCfg.vlIndexLabel->maxKeyLen;
    uint8_t *buf = NULL;
    uint32_t keyLen;
    uint32_t size = (uint32_t)(maxKeyLen * tupleBufArray->deleteNum + (sizeof(IndexKeyT) * tupleBufArray->deleteNum));

    Status ret = QryAllocStmtMem(stmt, size, (char **)&buf);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "alloc batch delete memory.");
        return ret;
    }
    uint8_t *keyDataPtr = buf;
    IndexKeyT *hashKey = (IndexKeyT *)(void *)((char *)buf + (maxKeyLen * tupleBufArray->deleteNum));

    for (register uint32_t i = 0; i < tupleBufArray->deleteNum; i++) {
        DmGetKeyBufFromVertexBuf(keyInfo, tupleBufArray->oldTuples[i].tupleBuf.buf, keyDataPtr, &keyLen);
        hashKey[i].keyData = keyDataPtr;
        hashKey[i].keyLen = keyLen;
        keyDataPtr = keyDataPtr + keyLen;
    }
    ret = IdxBatchDelete(secIndexCtx, hashKey, tupleBufArray->batchOut, tupleBufArray->deleteNum, removePara);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "batch delete index, deleteNum=%" PRIu32 ".", tupleBufArray->deleteNum);
    }

    QryFreeStmtMem(stmt, (char *)buf);
    return ret;
}

static Status QryBatchExecDeleteAllIndexes(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, QryTupleBufArrayT *tupleBufArray)
{
    bool isLiteTrx = QryVertexLabelIsLiteTrx(labelCursor->labelDef.vertexLabel);
    IndexRemoveParaT removePara = {0};
    removePara.isErase = isLiteTrx;

    // 批量删除主键索引
    Status ret;
    if (!labelCursor->isChLabel) {
        ret = IdxBatchDelete(labelCursor->idxCtx, tupleBufArray->outIndexKeys, tupleBufArray->batchOut,
            tupleBufArray->deleteNum, removePara);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // 批量删除二级索引
    uint32_t secIdxNum = labelCursor->secIdxNum;
    DmIndexKeyBufInfoT *keyInfo[DM_MAX_KEY_COUNT] = {NULL};
    for (uint32_t i = 0; i < secIdxNum; i++) {
        ret = DmVertexGetIndexKeyInfo(
            labelCursor->vertexNode->vertex, labelCursor->secIdxCtx[i]->idxMetaCfg.indexId, &keyInfo[i]);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get index keyInfo, vertexLabel=%s, secIdx = %" PRIu32 ".",
                labelCursor->labelDef.vertexLabel->metaCommon.metaName, i);
            return ret;
        }
    }
    for (uint32_t idx = 0; idx < secIdxNum; idx++) {
        IndexCtxT *secIndexCtx = labelCursor->secIdxCtx[idx];
        // 轻量化事务下HC已经提前删除了
        // 聚簇容器二级索引若为hashcluster由SE删除
        if (isLiteTrx && secIndexCtx->idxMetaCfg.idxType == HASHCLUSTER_INDEX) {
            continue;
        }
        ret = QryBatchExecDeleteSingleIndex(stmt, removePara, tupleBufArray, secIndexCtx, keyInfo[idx]);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "execute index delete, idxType:%" PRId32 ".", (int32_t)secIndexCtx->idxMetaCfg.idxType);
            return ret;
        }
    }
    return GMERR_OK;
}

static Status QryBatchExecDeleteHcIndexes(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, QryTupleBufArrayT *tupleBufArray)
{
    /* 轻量化事务下才需要先删除HC，RU模式下无HC索引，所以走后面的逻辑也没有影响 */
    bool isLiteTrx = QryVertexLabelIsLiteTrx(labelCursor->labelDef.vertexLabel);
    if (!isLiteTrx) {
        return GMERR_OK;
    }

    IndexRemoveParaT removePara = {0};
    removePara.isErase = isLiteTrx;

    Status ret = GMERR_OK;
    // 批量删除HashCluster
    uint32_t secIdxNum = labelCursor->secIdxNum;
    for (uint32_t idx = 0; idx < secIdxNum; idx++) {
        IndexCtxT *secIndexCtx = labelCursor->secIdxCtx[idx];
        if (secIndexCtx->idxMetaCfg.idxType != HASHCLUSTER_INDEX) {
            continue;
        }
        DmIndexKeyBufInfoT *keyInfo = NULL;
        ret = DmVertexGetIndexKeyInfo(
            labelCursor->vertexNode->vertex, labelCursor->secIdxCtx[idx]->idxMetaCfg.indexId, &keyInfo);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get index keyInfo, secIdx = %" PRIu32 ".", idx);
            return ret;
        }
        ret = QryBatchExecDeleteSingleIndex(stmt, removePara, tupleBufArray, secIndexCtx, keyInfo);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "execute hashcluster index delete");
            return ret;
        }
    }
    return GMERR_OK;
}

static Status QryBatchDeleteStaticChangeNotPartition(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, const QryTupleBufArrayT *tupleBufArray)
{
    Status ret = GMERR_OK;
    QryTuple2T *oldTuples = tupleBufArray->oldTuples;

    const uint8_t partitionId = DB_INVALID_UINT8;
    DmCheckInfoT *checkInfo = QryGetCheckInfoByIdForServer(labelCursor->labelDef.vertexLabel, partitionId);
    bool hasBeenChecked = checkInfo->version.hasBeenChecked;
    bool isNormalLabel = DmVertexLabelIsNormalLabel(labelCursor->labelDef.vertexLabel);
    for (register uint32_t i = 0; i < tupleBufArray->deleteNum; i++) {
        if (SECUREC_LIKELY(isNormalLabel)) {
            QryTupleStateE state = QRY_TUPLE_NULL;
            ret = QryGetTupleState(TupleBufGet(&oldTuples[i].tupleBuf), labelCursor->labelDef.vertexLabel, &state);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
            QryUpdateStatics(state, QRY_TUPLE_NULL, QryGetStatisticValue(labelCursor, partitionId));
        }

        if (SECUREC_LIKELY(!hasBeenChecked)) {
            continue;
        }
        QryTupleT oldTuple = {
            .dataStatus = oldTuples[i].dataStatus,
            .heapTupleBuf = TupleBufGet(&oldTuples[i].tupleBuf),
        };
        ret = QryStaticChange4CheckAccount(stmt, labelCursor->labelCache, partitionId, &oldTuple);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return GMERR_OK;
}

static Status QryBatchDeleteStaticChangePartition(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, const QryTupleBufArrayT *tupleBufArray)
{
    Status ret = GMERR_OK;
    QryTuple2T *oldTuples = tupleBufArray->oldTuples;
    for (register uint32_t i = 0; i < tupleBufArray->deleteNum; i++) {
        QryTupleStateE state = QRY_TUPLE_NULL;
        ret = QryGetTupleState(TupleBufGet(&oldTuples[i].tupleBuf), labelCursor->labelDef.vertexLabel, &state);
        if (ret != GMERR_OK) {
            return ret;
        }

        QryTupleT oldTuple = {
            .dataStatus = oldTuples[i].dataStatus,
            .heapTupleBuf = TupleBufGet(&oldTuples[i].tupleBuf),
        };

        uint8_t partitionId = 0;
        ret = QryGetPartitionId(labelCursor->labelDef.vertexLabel, &oldTuple.heapTupleBuf, &partitionId);
        if (ret != GMERR_OK) {
            return ret;
        }

        if (DmVertexLabelIsNormalLabel(labelCursor->labelDef.vertexLabel)) {
            QryUpdateStatics(state, QRY_TUPLE_NULL, QryGetStatisticValue(labelCursor, partitionId));
        }
        DmCheckInfoT *checkInfo = QryGetCheckInfoByIdForServer(labelCursor->labelDef.vertexLabel, partitionId);
        if (SECUREC_UNLIKELY(checkInfo->version.hasBeenChecked)) {
            ret = QryStaticChange4CheckAccount(stmt, labelCursor->labelCache, partitionId, &oldTuple);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static Status QryBatchDeleteStaticChange(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, const QryTupleBufArrayT *tupleBufArray)
{
    if (SECUREC_LIKELY(labelCursor->labelDef.isNotPartitioned)) {
        return QryBatchDeleteStaticChangeNotPartition(stmt, labelCursor, tupleBufArray);
    } else {
        return QryBatchDeleteStaticChangePartition(stmt, labelCursor, tupleBufArray);
    }
}

static Status QryBatchExecDeleteVertexes(QryStmtT *stmt, QryLabelCursorT *labelCursor, QryTupleBufArrayT *tupleBufArray)
{
    // 批量删除HashCluster索引，仅在轻量化事务开启时生效
    Status ret = QryBatchExecDeleteHcIndexes(stmt, labelCursor, tupleBufArray);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 需要记录实际删除数量,避免去重导致计数不一致问题
    tupleBufArray->batchOut->realDeleteCnt = 0;
    ret = HeapLabelBatchDeleteHpTuples(labelCursor->hpRunHdl, tupleBufArray->deleteNum, tupleBufArray->batchOut);
    if (ret != GMERR_OK) {
        return ret;
    }
    tupleBufArray->tupleNum = tupleBufArray->batchOut->realDeleteCnt;
    tupleBufArray->deleteNum = tupleBufArray->batchOut->realDeleteCnt;
    SeTransSetSendSubFlag(stmt->session->seInstance);
    return QryBatchDeleteStaticChange(stmt, labelCursor, tupleBufArray);
}

Status QryBatchDeleteLabelLatchModelWithPkIdxCore(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, const QryVertexParamT *deleteParams, QryTupleBufArrayT *tupleBufArray)
{
    // 需要生成订阅消息时才在EE做去重
    bool isGenerateSubs = IsGenerateVertexSubsMessages(labelCursor->labelDef.vertexLabel, DM_SUBS_EVENT_DELETE) ||
                          IsGenerateVertexSubsMessages(labelCursor->labelDef.vertexLabel, DM_SUBS_EVENT_AGED);
    Status ret = CategorizeDeleteAndNonDeleteOps(labelCursor, deleteParams, tupleBufArray, isGenerateSubs);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (tupleBufArray->deleteNum == 0) {
        return ret;
    }

    // 生成订阅信息
    if (isGenerateSubs) {
        ret = QryBatchExecDeleteSubsMessage(stmt, labelCursor, tupleBufArray);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    /* 1、轻量化事务下先删除索引，因为轻量化事务删除不允许回滚，出错了也要继续完成
     * 2、表锁模式必须开启轻量化事务，否则以上不成立
     * 3、批量删除数据时heap会对ee传下去的数组进行排序，导致数组乱序，与ee层数组对应关系失效，导致索引插入失败（KEY和ADDR对应不上），
     *    所以此处改为先删除索引，再删除数据
     */
    // 批量删除索引
    ret = QryBatchExecDeleteAllIndexes(stmt, labelCursor, tupleBufArray);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 批量删除数据
    return QryBatchExecDeleteVertexes(stmt, labelCursor, tupleBufArray);
}

static Status QryBatchDeleteLabelLatchModelWithPkIdx(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, const QryVertexParamT *deleteParams, uint32_t *affectRows)
{
    DB_POINTER4(stmt, labelCursor, deleteParams, affectRows);
    // 准备删除处理动作
    QryTupleBufArrayT *tupleBufArray = NULL;
    Status ret = QryAllocTupleBufArray(stmt, labelCursor, deleteParams->count, &tupleBufArray);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = QryBatchDeleteLabelLatchModelWithPkIdxCore(stmt, labelCursor, deleteParams, tupleBufArray);
    // 删除结束后处理
    *affectRows = tupleBufArray->deleteNum;
#ifdef FEATURE_HAC
    if (!labelCursor->idxCtx->isKeyCmpByHac) {  // 硬化回调后由存储层回收内存
#endif
        QryReleaseCachedHeapTupleBuffer(labelCursor, tupleBufArray->tupleNum, tupleBufArray);
#ifdef FEATURE_HAC
    }
#endif
    QryFreeTupleBufArray(stmt, tupleBufArray);
    stmt->eof = true;
    return ret;
}

static Status QryExecuteDeleteVertexInnerForChLabel(QryStmtT *stmt, QryCursorT *qryCursor)
{
    Status ret = GMERR_OK;
    QryVertexParamT *deleteParams = (QryVertexParamT *)stmt->execParams;
    uint32_t deleteNum = deleteParams->count;
    QryLabelCursorT *labelCursor = &qryCursor->labelCursors[0];
    if (deleteNum > 1 && IsSupportBatchDelete(labelCursor)) {
        return QryBatchDeleteForChLabel(stmt, labelCursor, deleteParams, &qryCursor->affectRows);
    }
    for (uint32_t i = 0; i < deleteNum; i++) {
        QryVertexDataT *vertexItem = &deleteParams->vertexData[i];
        labelCursor->indexKey = &vertexItem->indexKey;
        ret = QryDeleteSingleVertexForChLabel(stmt, qryCursor, vertexItem);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    stmt->eof = true;
    return GMERR_OK;
}

static Status QryExecuteDeleteVertexForChLabel(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    Status ret = GMERR_OK;
    // 按顺序一次获取所需的值，减少切换结构体导致的cache miss
    QryDeleteVertexDescT *deleteCtx = (QryDeleteVertexDescT *)stmt->context->entry;
    QryCursorT *qryCursor = (QryCursorT *)stmt->qryCursor;
    DeletePlanT *deletePlan = &deleteCtx->plan->deletePlan;
    uint32_t uuid = deleteCtx->query->uuid;
    PlanNodeT *plan = deletePlan->next->qryVertexPlan.next;
    QryLabelT *qryLabel = plan->scanPlan.qryLabel;
    ScanModeT scanMode = plan->scanPlan.scanMode;
    QryLabelCursorT *labelCursor = &qryCursor->labelCursors[0];
    labelCursor->action = HEAP_OPTYPE_DELETE;
    labelCursor->scanMode = scanMode;

    ret = QryOpenVertexLabelCursor(stmt, qryLabel, labelCursor, true);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (!QrySchemaVersionExistCheck(labelCursor->labelDef.vertexLabel, uuid)) {
        ret = GMERR_UNDEFINED_TABLE;
        return ret;
    }

    ret = QryExecuteDeleteVertexInnerForChLabel(stmt, qryCursor);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(
            ret, "execute delete vertex for ChLabel: %s.", labelCursor->labelDef.vertexLabel->metaCommon.metaName);
        return ret;
    }

    return QryMakeRespForDmlOperation(stmt, qryCursor->affectRows);
}

static Status QryExecuteDeleteVertexInner(QryStmtT *stmt, QryCursorT *qryCursor, PlanNodeT *plan)
{
    Status ret;
    QryVertexParamT *deleteParams = (QryVertexParamT *)stmt->execParams;
    uint32_t deleteNum = deleteParams->count;
    QryLabelCursorT *labelCursor = &qryCursor->labelCursors[0];
    DmVertexLabelT *vertexLabel = labelCursor->labelDef.vertexLabel;
    DbSetCliOpTableName(vertexLabel->metaCommon.metaName, 0);
    if (deleteNum > 1 && vertexLabel->metaVertexLabel->containerType == CONTAINER_HEAP &&
        IsSupportBatchDelete(labelCursor)) {
        return QryBatchDeleteLabelLatchModelWithPkIdx(stmt, labelCursor, deleteParams, &qryCursor->affectRows);
    } else {
        QryExecuteSingleVertexFunc func = QryExecuteDeleteSingleVertex;
        // 判断是否为同步表
        if (SECUREC_UNLIKELY(QryCheckIsSyncVertexLabel(vertexLabel))) {
            func = QryExecDeleteSingleVertex4Sync;
        }
        for (uint32_t i = 0; i < deleteNum; i++) {
            QryVertexDataT *vertexItem = &deleteParams->vertexData[i];
            // 索引表有两个来源：1.执行时读取执行参数 2.下层scan算子的索引表，这里默认用第一种
            QryResetBatchCursor(qryCursor, labelCursor, &vertexItem->indexKey);
            ret = QryExecuteUpdateOrDeleteVertexInner(stmt, qryCursor, plan, vertexItem, func);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static Status QryExecuteDeleteNormalVertex(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryDeleteVertexDescT *deleteCtx = (QryDeleteVertexDescT *)stmt->context->entry;
    DeletePlanT *deletePlan = &deleteCtx->plan->deletePlan;
    PlanNodeT *plan = deletePlan->next->qryVertexPlan.next;
    QryCursorT *qryCursor = (QryCursorT *)stmt->qryCursor;
    qryCursor->labelCursors[0].action = HEAP_OPTYPE_DELETE;
    Status ret;

    if (stmt->splitInfo.isFirst) {
        ret = QryExecPlan(stmt, qryCursor, plan, 0);
        if (ret != GMERR_OK) {
#ifdef FEATURE_YANG
            DmVertexLabelT *vertexLabel = plan->scanPlan.qryLabel->def.vertexLabel;
            Status result = QryProcessErrorPath(stmt, vertexLabel, ret);
            if (result == GMERR_OK) {
                return result;
            }
#endif
            DB_LOG_ERROR(ret, "execute delete vertex plan.");
            return ret;
        }
    }
    if (!QrySchemaVersionExistCheck(qryCursor->labelCursors[0].labelDef.vertexLabel, deleteCtx->query->uuid)) {
        return GMERR_UNDEFINED_TABLE;
    }
    ret = QryExecuteDeleteVertexInner(stmt, qryCursor, plan);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        QrySetErrorInfoWhenReplay(stmt, ret);

        DmVertexLabelT *vertexLabel = qryCursor->labelCursors[0].labelDef.vertexLabel;
#ifdef FEATURE_YANG
        QryBuildErrorPath(stmt, ret, NULL, vertexLabel, QRY_YANG_ERROR_PATH_NOT_EXIST);
#endif
        DB_LOG_ERROR(ret, "execute delete vertex for label: %s.", vertexLabel->metaCommon.metaName);
        return ret;
    }

    return QryMakeRespForDmlOperation(stmt, qryCursor->affectRows);
}

Status QryExecuteDeleteVertex(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryLabelT *qryLabel = stmt->context->labelCache;
    if (DmIsLabelSupportStatusMerge(qryLabel->def.vertexLabel)) {
        return QryExecuteMarkDeleteVertex(stmt);
    }

    QryDeleteVertexDescT *deleteCtx = (QryDeleteVertexDescT *)stmt->context->entry;
    DeletePlanT *deletePlan = &deleteCtx->plan->deletePlan;
    PlanNodeT *plan = deletePlan->next->qryVertexPlan.next;
    if (SECUREC_LIKELY(QryIsPrimaryIdxDelForChLabel(plan))) {
        return QryExecuteDeleteVertexForChLabel(stmt);
    }
    return QryExecuteDeleteNormalVertex(stmt);
}

#ifdef __cplusplus
}
#endif
