/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: header file of DML executor
 * Author: zhulixia
 * Create: 2020-08-27
 */

#ifndef EE_DML_COMMON_H
#define EE_DML_COMMON_H

#include "ee_cursor.h"
#include "se_chained_hash_index.h"
#include "container_access.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct QryTupleInfo {
    DmVertexT *vertex;
    HeapTupleBufT heapTupleBuf;
} QryTupleInfoT;

typedef struct QryUpdateVertexData {
    DmVertexLabelT *label;
    DmVertexT *deltaVertex;
    TextT deltaVertexBuf;
    bool isNonDeSer;
    uint32_t autoOperateFlag;  // auto operate edge type
    uint32_t isReplay;
    uint64_t replayOldVersion;
} QryUpdateVertexDataT;

#define QRY_OLD_DATA_STATUS_AGE 0x0001       // old date status is age
#define QRY_OLD_DATA_STATUS_TRUNCATE 0x0002  // old date status is truncate
#define QRY_AUTO_DELETE_WEAK_EDGE 0x0001     // mark whether auto delete weak edges
#define QRY_AUTO_INSERT_OUT_EDGE 0x0002      // mark whether auto insert out edges
#define QRY_AUTO_INSERT_IN_EDGE 0x0004       // mark whether auto insert in edges
#define QRY_AUTO_DEL_RELATION_VERTEX 0x0008  // mark whether auto delete all relationship vertexes
#define QRY_AFFECT_ROW_INSERT 1
#define QRY_AFFECT_ROW_UPDATE 2

inline static bool QryExceedSplitTime(uint64_t startTime, double timeThreshold)
{
    return DbExceedTime(startTime, timeThreshold);
}

inline static void QryCheckTupleAddr(QryLabelCursorT *labelCursor)
{
    ContainerTypeE type = *(ContainerTypePtrT)(labelCursor->containerHdl);
    if (type == CONTAINER_HEAP && !IsHeapAddrOk((HpRunHdlT)labelCursor->containerHdl, labelCursor->addr)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Addr wrong, label:%s, trxId: %" PRIu64 "addr %" PRIu64,
            labelCursor->labelDef.vertexLabel->metaCommon.metaName,
            (uint64_t)ContainerGetTrxId(labelCursor->containerHdl, labelCursor->idxCtx), (uint64_t)labelCursor->addr);
    }
}

inline static Status QryLookupIdxWithPrefetch(QryLabelCursorT *labelCursor, bool *isFound)
{
    DB_POINTER2(labelCursor, isFound);
    // 当前AmIndexPrefetch内部保证上下文是表锁才生效，QE不处理并发导致的失效问题
    Status ret = ContainerIdxLookupWithPrefetch(
        labelCursor->containerHdl, labelCursor->idxCtx, labelCursor->pkValue, &labelCursor->addr, isFound);
    QryCheckTupleAddr(labelCursor);
    return ret;
}

void QrySetHeapTupleCheckVersion(DmVertexLabelT *vertexLabel, const HeapTupleBufT *heapTupleBuf, uint8_t partitionId);
Status QryReserveRespHeader(QryStmtT *stmt, uint32_t *headOffSet, bool hasRes);
Status QryMakeRespForDmlOperation(QryStmtT *stmt, uint32_t affectRows);
Status QryMakeResp4ResAckHeader(QryStmtT *stmt, uint32_t headOffSet, DbResAckHeaderT *resAckHeader, bool hasRes);
Status QryMakeRespForDmlOperationByRes(QryStmtT *stmt);

// resource dml entry
Status QryAllocRes(QryStmtT *stmt, QryLabelCursorT *labelCursor, DmVertexT *vertex);
Status QryCheckRes4Replace(QryStmtT *stmt, QryLabelCursorT *labelCursor);
Status QryFreeRes(QryStmtT *stmt, QryLabelCursorT *labelCursor);
Status QryInitResData(QryStmtT *stmt, DmVertexLabelT *vertexLabel);
void QryUnInitResData(QryStmtT *stmt, const DmVertexLabelT *vertexLabel);

Status QryInsertHeapTuple(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, HeapTupleBufT *heapTupleBuf, uint8_t *partitionId);
Status QryUpdateHeapTuple(QryStmtT *stmt, QryLabelCursorT *labelCursor, QryTupleInfoT *tupleInfo);
Status QryUpdateStMgHeapTuple(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, QryTupleInfoT *tupleInfo, bool isUpdateCheckVersion);
Status QryDeleteHeapTuple(QryStmtT *stmt, QryLabelCursorT *labelCursor);

Status QryUpdateHeapTupleCheck(
    QryStmtT *stmt, QryLabelCursorT *labelCursor, HeapTupleBufT *heapTupleBuf, QryTupleT *oldHeapTuple, bool *isAged);

void QryFreeHeapTupleBufWithCond(QryStmtT *stmt, QryLabelCursorT *cursor);
#ifdef FEATURE_HAC
// 执行层判断索引是否能支持走硬化回调逻辑
void QryJudgeHacIndexSupportKeyCmp(QryLabelCursorT *labelCursor, IndexCtxT *index);
#endif

#ifdef __cplusplus
}
#endif

#endif /* EE_DML_COMMON_H */
