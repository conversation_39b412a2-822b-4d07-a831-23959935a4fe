/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: Implementation of check account
 * Author: zhulixia
 * Create: 2021-06-05
 */

#include "ee_check.h"
#include "ee_systbl.h"
#include "adpt_sleep.h"
#include "dm_data_print.h"
#include "dm_cache_basic.h"
#include "ee_cond_data.h"
#include "ee_dml_vertex.h"
#include "ee_dml_query.h"
#include "ee_dml_edge.h"
#include "db_hash.h"
#include "db_bg_task_hub.h"
#include "ee_concurrency_control.h"
#include "db_sysapp_context.h"
#include "ee_dml_sync.h"
#include "ee_feature_import.h"
#include "ee_dcl_ctrl.h"
#include "ee_dml_index.h"
#include "ee_statistic.h"
#include "db_shm_hashmap.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

// 并发方案:加锁保护
QryAgedMgrT g_gmdbAgedMgr = {0};

QryAgedMgrT *GetQryAgedMgrImpl(void)
{
    return &g_gmdbAgedMgr;
}

void QryAgeTaskKeyCopy(const void *srcKey, void *destKey)
{
    const QryAgeTaskKeyT *ageTaskSrcKey = (const QryAgeTaskKeyT *)srcKey;
    QryAgeTaskKeyT *ageTaskDestKey = (QryAgeTaskKeyT *)destKey;
    ageTaskDestKey->labelId = ageTaskSrcKey->labelId;
    ageTaskDestKey->partitionId = ageTaskSrcKey->partitionId;
    ageTaskDestKey->oldCheckVersion = ageTaskSrcKey->oldCheckVersion;
}

void QryAgeTaskValueCopy(const void *srcValue, void *destValue)
{
    *(bool *)destValue = *(const bool *)srcValue;
}

uint32_t QryAgeTaskKeyCompare(const void *key1, const void *key2)
{
    const QryAgeTaskKeyT *ageTaskKey1 = (const QryAgeTaskKeyT *)key1;
    const QryAgeTaskKeyT *ageTaskKey2 = (const QryAgeTaskKeyT *)key2;
    return (ageTaskKey1->labelId == ageTaskKey2->labelId && ageTaskKey1->partitionId == ageTaskKey2->partitionId &&
            ageTaskKey1->oldCheckVersion == ageTaskKey2->oldCheckVersion);
}

/*
 * Calculate the hash value of a ageTaskKey.
 */
uint32_t QryAgeTaskKeyToHash32(const QryAgeTaskKeyT *ageTaskKey)
{
    DB_POINTER(ageTaskKey);
    uint32_t keyBufLen = (uint32_t)(sizeof(uint32_t) + sizeof(uint8_t) + sizeof(uint8_t));
    // keyBuf : labelId + partitionId + oldCheckVersion
    uint8_t keyBuf[sizeof(uint32_t) + sizeof(uint8_t) + sizeof(uint8_t)];

    uint8_t *keyBufPtr = keyBuf;
    *((uint32_t *)keyBufPtr) = ageTaskKey->labelId;
    keyBufPtr += sizeof(uint32_t);
    *((uint8_t *)keyBufPtr) = ageTaskKey->partitionId;
    keyBufPtr += sizeof(uint8_t);
    *((uint8_t *)keyBufPtr) = ageTaskKey->oldCheckVersion;
    return DbHash32((uint8_t *)keyBuf, keyBufLen);
}

static void QrySetCheckVersion(
    const HeapTupleBufT *heapTupleBuf, const uint8_t version, const DmVertexLabelT *vertexLabel)
{
    DmValueT propeValue;
    propeValue.type = DB_DATATYPE_UINT8;
    propeValue.value.ubyteValue = version;
    DmVertexBufSetSysPrope(CHECK_VERSION, &propeValue, vertexLabel->vertexDesc, (uint8_t *)heapTupleBuf->buf);
}

static void QryUpdateTaskInfo(QryLabelCursorT *cursor, QryAgedTaskT *curTask, DmCheckInfoT *checkInfo, uint64_t cnt)
{
    if (curTask->status == QRY_AGE_STATUS_FINISH) {
        curTask->taskFinishTime = DbClockGetTsc();
    }

    if (!curTask->isRecovery) {
        // agedRcdCnt is not counted for aging events because statistics are collected when QryExecuteDeleteVertexInfo
        // is invoked.
        if (curTask->status == QRY_AGE_STATUS_FINISH) {
            (void)DbAtomicDec8(&checkInfo->checkCnt);
        }
        return;
    }

    // 表示恢复版本可见，否则已经被truncate或老化，不处理对账
    if (CataIsRecoveryVersion(&checkInfo->version, curTask->ageTaskKey.oldCheckVersion)) {
        DmAccCheckT *accCheckAddr = cursor->labelDef.vertexLabel->commonInfo->accCheckAddr;
        if (SECUREC_UNLIKELY(accCheckAddr == NULL)) {
            DB_LOG_ERROR(GMERR_NULL_VALUE_NOT_ALLOWED, "get check addr.");
            return;
        }
        bool isPartition = accCheckAddr->isPartition;
        uint32_t id = isPartition ? curTask->ageTaskKey.partitionId : 0;
        cursor->labelCache->statistic.staticValue[id].recoverRcdCnt = cnt;
    }
}

// 当前场景更新系统表失败没有影响，可以等待下次修改checkInfo时刷新
void QryUpdateSysTableVertexLabel(SessionT *session, DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(session, vertexLabel);
    bool isLiteTrx = QryVertexLabelIsLiteTrx(vertexLabel);
    TrxCfgT cfg = {
        .isLiteTrx = isLiteTrx,
        .isBackGround = false,  // 老化线程当前台线程特殊处理
        .connId = DB_INVALID_UINT16,
        .trxType = PESSIMISTIC_TRX,
        .isolationLevel = isLiteTrx ? READ_UNCOMMITTED : READ_COMMITTED,
    };
    Status ret = SeTransBegin(session->seInstance, &cfg);
    if (ret != GMERR_OK) {
        DB_LOG_WARN(ret, "Trans begin: %s.", vertexLabel->metaCommon.metaName);
        return;
    }
    ret = SysTableUpdateOneVertexLabel(session, vertexLabel);
    if (ret == GMERR_OK) {
        ret = SeTransCommit(session->seInstance);
        if (ret != GMERR_OK) {
            DB_LOG_WARN(ret, "Trans commit: %s.", vertexLabel->metaCommon.metaName);
        }
    }
    if (ret != GMERR_OK) {
        DB_LOG_WARN(ret, "Update vertexLabel sysTable: %s.", vertexLabel->metaCommon.metaName);
        (void)SeTransRollback(session->seInstance, false);
    }
}

void QryUpdateCheckInfo(SessionT *session, const QryAgedTaskT *curTask)
{
    if (!curTask->isRecovery || curTask->status != QRY_AGE_STATUS_FINISH) {
        return;
    }

    DmCheckInfoT *checkInfo = curTask->checkInfo;
    // 表示恢复版本可见，否则已经被truncate或老化，不处理对账
    if (CataIsRecoveryVersion(&checkInfo->version, curTask->ageTaskKey.oldCheckVersion)) {
        SHM_CRASHPOINT_ONE(SHM_CRASH_UPDATE_MIN_RECOVERY_BEFORE);
        checkInfo->version.minRecovery = curTask->targetCheckVersion;
        SHM_CRASHPOINT_ONE(SHM_CRASH_UPDATE_MIN_RECOVERY_AFTER);
        // 更新版本不是恢复版本表示全部恢复完成，刷新checkInfo
        if (!CataIsRecoveryVersion(&checkInfo->version, curTask->targetCheckVersion) &&
            checkInfo->version.checkStatus == DM_CHECK_STATUS_ABNORMAL) {
            SHM_CRASHPOINT_ONE(SHM_CRASH_UPDATE_CHECK_STATUS_BEFORE);
            checkInfo->version.checkStatus = DM_CHECK_STATUS_NORMAL;
            SHM_CRASHPOINT_ONE(SHM_CRASH_UPDATE_CHECK_STATUS_AFTER);
            CataResetOldCheckVersion(checkInfo);
            SHM_CRASHPOINT_ONE(SHM_CRASH_RESET_OLD_VERSION_AFTER);
        }
        QryUpdateSysTableVertexLabel(session, curTask->qryLabel->def.vertexLabel);
    }
}

static Status QryRecoveryData(QryStmtT *stmt, QryLabelCursorT *cursor, QryAgedTaskT *task, DmCheckInfoT *checkInfo)
{
    Status ret;
    uint64_t effectiveCnt = 0;

    while (!QryExceedSplitTime(task->startTime, stmt->session->splitTime)) {
        ret = QryFetchSequenceScan(stmt, cursor);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (cursor->isEof) {
            task->status = QRY_AGE_STATUS_FINISH;
            break;
        }
        if (cursor->mismatched) {
            continue;
        }
        QrySetCheckVersion(&cursor->heapTuple.heapTupleBuf, task->targetCheckVersion, task->qryLabel->def.vertexLabel);
        ret = ContainerUpdateTuple(cursor->containerHdl, &cursor->heapTuple.heapTupleBuf, cursor->addr, NULL, NULL);
        if (ret != GMERR_OK) {
            return ret;
        }
        effectiveCnt++;
    }

    QryUpdateTaskInfo(cursor, task, checkInfo, effectiveCnt);

    return GMERR_OK;
}

static Status QryPrintPropertyValue(const DmValueT *propValue)
{
    TextT *pBuf = &g_gmdbAgedMgr.printBuf;
    uint32_t maxLen = g_gmdbAgedMgr.maxBufLen;

    if ((int32_t)(maxLen - pBuf->len) <= 1) {
        return GMERR_OK;
    }
    char *tmpBuf = pBuf->str + pBuf->len;
    Status ret = DmTruncPrintPropertyValue(propValue, &tmpBuf, maxLen - pBuf->len);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "truncate print age prop value.");
        return ret;
    }
    pBuf->len += (uint32_t)(tmpBuf - (pBuf->str + pBuf->len));
    return GMERR_OK;
}

static Status QryPrintComma(uint32_t keyNum, uint32_t idx)
{
    TextT *pBuf = &g_gmdbAgedMgr.printBuf;
    uint32_t maxLen = g_gmdbAgedMgr.maxBufLen;

    if ((int32_t)(maxLen - pBuf->len) <= 1 || (idx == (uint32_t)(keyNum - 1))) {
        return GMERR_OK;
    }
    int32_t length = snprintf_truncated_s(pBuf->str + pBuf->len, maxLen - pBuf->len, ", ");
    if (length < 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "print age info to buf.");
        return GMERR_FIELD_OVERFLOW;
    }
    pBuf->len += (uint32_t)length;
    return GMERR_OK;
}

static Status QryPrintPropertyName(const char *name)
{
    TextT *pBuf = &g_gmdbAgedMgr.printBuf;
    uint32_t maxLen = g_gmdbAgedMgr.maxBufLen;
    if ((int32_t)(maxLen - pBuf->len) <= 1) {
        return GMERR_OK;
    }
    int32_t length = snprintf_truncated_s(pBuf->str + pBuf->len, maxLen - pBuf->len, "%s: ", name);
    if (length < 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "print age propName to buf.");
        return GMERR_FIELD_OVERFLOW;
    }
    pBuf->len += (uint32_t)length;
    return GMERR_OK;
}

static Status QryFillKeyPropertyInfo(QryStmtT *stmt, QryLabelCursorT *cursor, const DmVertexLabelT *label)
{
    Status ret;
    const HeapTupleBufT *tupleBuf = &cursor->heapTuple.heapTupleBuf;
    QryObjectMapNodeT *vertexNode = NULL;
    ret = QryAllocAndDeSerializeVertex(
        stmt->session->objectMap, tupleBuf->buf, tupleBuf->bufSize, label->metaCommon.metaId, &vertexNode);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "alloc and deserialize vertex, name =%s.", label->metaCommon.metaName);
        return ret;
    }

    const DmVlIndexLabelT *pkIndex = label->metaVertexLabel->pkIndex;
    for (uint32_t i = 0; i < pkIndex->propeNum; i++) {
        ret = QryPrintPropertyName(pkIndex->properties[pkIndex->propIds[i]].name);
        if (ret != GMERR_OK) {
            QryReleaseEmptyVertex(vertexNode);
            return ret;
        }

        DmValueT propValue = {0};
        ret = DmVertexGetPropeByIdNoCopy(vertexNode->vertex, pkIndex->propIds[i], &propValue);
        if (ret != GMERR_OK) {
            QryReleaseEmptyVertex(vertexNode);
            return ret;
        }

        ret = QryPrintPropertyValue(&propValue);
        if (ret != GMERR_OK) {
            QryReleaseEmptyVertex(vertexNode);
            return ret;
        }
        ret = QryPrintComma(pkIndex->propeNum, i);
        if (ret != GMERR_OK) {
            QryReleaseEmptyVertex(vertexNode);
            return ret;
        }
    }
    QryReleaseEmptyVertex(vertexNode);
    return GMERR_OK;
}

static Status QryFillPrintBuf(
    QryStmtT *stmt, QryLabelCursorT *cursor, const QryAgedTaskT *curTask, DmVertexLabelT *label)
{
    uint8_t partitionId = DB_INVALID_UINT8;
    const HeapTupleBufT *tupleBuf = &cursor->heapTuple.heapTupleBuf;
    Status ret = GMERR_OK;
    if (SECUREC_UNLIKELY(!cursor->labelDef.isNotPartitioned)) {
        ret = QryGetPartitionId(label, tupleBuf, &partitionId);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    int32_t length = snprintf_truncated_s(g_gmdbAgedMgr.printBuf.str, g_gmdbAgedMgr.maxBufLen,
        "Aged record, nameSpace:%s, tableName:%s, partition:%" PRIu8 ", ", curTask->nspName, label->metaCommon.metaName,
        partitionId);
    if (length < 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "print ageInfo string to buf.");
        return GMERR_FIELD_OVERFLOW;
    }
    g_gmdbAgedMgr.printBuf.len += (uint32_t)length;
    return QryFillKeyPropertyInfo(stmt, cursor, label);
}

static void QryPrintAgedInfo(QryStmtT *stmt, QryLabelCursorT *cursor, QryAgedTaskT *curTask)
{
    DmVertexLabelT *label = cursor->labelDef.vertexLabel;
    if (!g_gmdbAgedMgr.printAgedInfo || (label->metaVertexLabel->pkIndex == NULL) || curTask->isTruncate) {
        return;
    }
    g_gmdbAgedMgr.printBuf.len = 0;
    (void)QryFillPrintBuf(stmt, cursor, curTask, label);
    g_gmdbAgedMgr.printBuf.str[g_gmdbAgedMgr.printBuf.len] = '\0';
    DB_LOG_WARN_UNFOLD(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "%s.", g_gmdbAgedMgr.printBuf.str);
    curTask->logCount++;
}

static Status QryDeleteAgedDataBySubType(QryStmtT *stmt, QryLabelCursorT *cursor)
{
    DB_POINTER2(stmt, cursor);
    if (DmIsLabelSupportStatusMerge(cursor->labelDef.vertexLabel)) {
        return QryExecuteMarkDeleteSingleVertexWithSub(stmt, cursor);
    } else {
        if (cursor->labelDef.vertexLabel->commonInfo->dlrInfo.isDataSyncLabel) {
            return QryExecDeleteVertexInfo4Sync(stmt, cursor);
        } else {
            return QryExecuteDeleteVertexInfo(stmt, cursor);
        }
    }
}

static Status QryLoopDeleteAgedData(QryStmtT *stmt, QryLabelCursorT *cursor, QryAgedTaskT *curTask, uint64_t *delCnt)
{
    DB_POINTER4(stmt, cursor, curTask, delCnt);
    Status ret = GMERR_OK;
    while (!QryExceedSplitTime(curTask->startTime, stmt->session->splitTime)) {
        const HpTupleAddr addr = cursor->addr;
        const uint32_t scanCurBlockId = cursor->scanCurBlockId;
        ret = QryFetchSequenceScan(stmt, cursor);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (cursor->isEof) {
            curTask->status = QRY_AGE_STATUS_FINISH;
            break;
        }
        if (cursor->mismatched) {
            continue;
        }

        if (curTask->logCount < TASK_LOG_LIMIT) {
            QryPrintAgedInfo(stmt, cursor, curTask);
        }

        if (cursor->labelDef.vertexLabel->commonInfo->edgeLabelNum != 0) {
            ret = QryExecuteAutoDeleteEdge(stmt, cursor, QRY_AUTO_DELETE_WEAK_EDGE);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        if (curTask->isTruncate) {
            cursor->heapTuple.dataStatus |= QRY_OLD_DATA_STATUS_TRUNCATE;
        } else {
            cursor->heapTuple.dataStatus |= QRY_OLD_DATA_STATUS_AGE;
        }
        ret = QryDeleteAgedDataBySubType(stmt, cursor);
        if (ret != GMERR_OK) {
            // 生成订阅消息如果无法申请内存，就直接提交
            if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
                DB_LOG_ERROR_AND_SET_LASTERR(ret, "generate subs message, labelName=%s, addr=%" PRIu64 ".",
                    cursor->labelDef.vertexLabel->metaCommon.metaName, cursor->addr);
                ret = GMERR_OK;
                cursor->addr = addr;  // 当前记录没有老化成功，需要重新扫描
                cursor->scanCurBlockId = scanCurBlockId;
                break;
            }
            return ret;
        }
        (*delCnt)++;
        if (*delCnt >= curTask->pushSubsBatch) {
            break;
        }
    }
    return GMERR_OK;
}

static Status QryDeleteAgedData(QryStmtT *stmt, QryLabelCursorT *cursor, QryAgedTaskT *curTask, DmCheckInfoT *checkInfo)
{
    DB_POINTER4(stmt, cursor, curTask, checkInfo);
    uint64_t delCnt = 0;
    Status ret = QryLoopDeleteAgedData(stmt, cursor, curTask, &delCnt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "loop delete aged data, labelName: %s.", cursor->labelDef.vertexLabel->metaCommon.metaName);
        return ret;
    }

    QryUpdateTaskInfo(cursor, curTask, checkInfo, delCnt);

    if (DmIsLabelSupportStatusMerge(cursor->labelDef.vertexLabel)) {
        ret = QryAddGcTaskToDataSet(&stmt->session->stmgSubDataSet, cursor->labelDef.vertexLabel->metaCommon.metaId);
    }
    return ret;
}

Status QryMatchAgeCond(QryStmtT *stmt, const QryCondDataT *condData, bool *isMatched)
{
    DB_POINTER3(stmt, condData, isMatched);
    TupleT tuple = TupleBufGet(condData->tupleBuf);
    uint8_t partitionId;
    uint32_t labelIdx = 0;
    QryLabelT *qryLabel = NULL;
    uint8_t tupleVersion;

    Status ret = QryGetVertexLabelByIdx(stmt->context, labelIdx, &qryLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get vertexlabel when match age.");
        return ret;
    }

    ret = QryGetPartitionId(qryLabel->def.vertexLabel, &tuple, &partitionId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get partition id.");
        return ret;
    }
    if (partitionId != ((QryAgeCondDataT *)condData->cond)->partitionId) {
        *isMatched = false;
        return GMERR_OK;
    }
    DmVertexDescT *vertexDesc = qryLabel->def.vertexLabel->vertexDesc;
    QryGetCheckVersion(&tuple, &tupleVersion, vertexDesc);
    if (tupleVersion != ((QryAgeCondDataT *)condData->cond)->checkVersion) {
        *isMatched = false;
        return GMERR_OK;
    }
    *isMatched = true;
    return GMERR_OK;
}

Status QryMatchStMgMarkDeleteCond(QryStmtT *stmt, const QryCondDataT *condData, bool *isMatched)
{
    DB_POINTER3(stmt, condData, isMatched);
    TupleT tuple = TupleBufGet(condData->tupleBuf);
    uint8_t deleteMark;
    uint32_t labelIdx = 0;
    QryLabelT *qryLabel = NULL;

    Status ret = QryGetVertexLabelByIdx(stmt->context, labelIdx, &qryLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get vertexlabel when match delete.");
        return ret;
    }

    QryGetStMgDataDeleteMark(&tuple, qryLabel->def.vertexLabel, &deleteMark, qryLabel->def.vertexLabel->vertexDesc);
    if (deleteMark != 0) {
        *isMatched = false;
        return GMERR_OK;
    }
    *isMatched = true;
    return GMERR_OK;
}

Status QryMatchStMgAgeMarkDeleteCond(QryStmtT *stmt, const QryCondDataT *condData, bool *isMatched)
{
    DB_POINTER3(stmt, condData, isMatched);
    Status ret = QryMatchStMgMarkDeleteCond(stmt, condData, isMatched);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "match mark delete condition.");
        return ret;
    }
    // 数据被标记删除，直接返回false
    if (!(*isMatched)) {
        return GMERR_OK;
    }
    // 否则重置isMatched，并进行老化判断
    *isMatched = false;
    return QryMatchAgeCond(stmt, condData, isMatched);
}

static Status QrySetAgeCondData(QryStmtT *stmt, const QryAgedTaskT *curTask, QryLabelCursorT *cursor)
{
    QryCondDataT *userData = NULL;
    Status ret = !cursor->isChLabel ? QryInitHpScanSubsCondData(stmt, cursor, &userData, SCAN_COND_DATA_AGE) :
                                      QryInitChLabelScanSubsCondData(stmt, cursor, &userData, SCAN_COND_DATA_AGE);
    if (ret != GMERR_OK) {
        return ret;
    }
    userData->stmt = stmt;
    userData->tupleBuf = cursor->tupleBuf;
    userData->heapTupleBuf = &cursor->heapTuple.heapTupleBuf;
    ((QryAgeCondDataT *)userData->cond)->checkVersion = curTask->ageTaskKey.oldCheckVersion;
    ((QryAgeCondDataT *)userData->cond)->partitionId = curTask->ageTaskKey.partitionId;
    // 状态合并表的老化流程也判断是否被标记删除
    if (DmIsLabelSupportStatusMerge(cursor->labelDef.vertexLabel)) {
        userData->qryMatchCondProc = (QryMatchCondProc)QryMatchStMgAgeMarkDeleteCond;
    } else {
        userData->qryMatchCondProc = (QryMatchCondProc)QryMatchAgeCond;
    }
    return GMERR_OK;
}

static Status QryResetHeapBeginAddr(QryLabelCursorT *cursor, QryAgedTaskT *curTask)
{
    cursor->labelDef = curTask->qryLabel->def;
    ContainerBeginScanCfgT scanCfg = {.isDefragmentation = false,
        .isRecovery = false,
        .chBeginAddr = *(ChLabelTupleCombineAddr *)&curTask->curHeapTupleAddr,
        .hpBeginAddr = curTask->curHeapTupleAddr,
        .maxFetchNum = 1,
        .scanMode = CH_SCAN_ROWS,
        .labelLatchInfoForClt = ContainerLabelLatchInfoInit(DB_INVALID_UINT32, DB_INVALID_SHMPTR),
        .heapDowngradeInfo = HeapLabelDowngradeInfoInit()};
    ContainerEndScan(cursor->containerHdl, cursor->cursor);
    cursor->cursor = NULL;
    return ContainerBeginScanCursor(cursor->containerHdl, &scanCfg, &cursor->cursor);
}

Status QryGenerateSubs4ChLabelDeleteCbs(QryStmtT *stmt, QryLabelCursorT *labelCursor, HeapTupleBufT *heapTupleBuf)
{
    DB_POINTER3(stmt, labelCursor, heapTupleBuf);
    DmSubsEventE eventType = ((labelCursor->heapTuple.dataStatus & QRY_OLD_DATA_STATUS_AGE) != 0) ?
                                 DM_SUBS_EVENT_AGED :
                                 DM_SUBS_EVENT_DELETE;
    if (IsGenerateVertexSubsMessages(labelCursor->labelDef.vertexLabel, eventType)) {
        QrySubsRowDataSetT rowDataSet;
        rowDataSet.rowsDataNum = 1;
        rowDataSet.rowsData[0] = (QrySubsRowDataT){0};
        rowDataSet.rowsData[0].oldTupleBuf = *heapTupleBuf;
        SetVersionNo2RowDataSet(labelCursor->labelDef.vertexLabel, heapTupleBuf, &rowDataSet.rowsData[0]);
        return QryGenerateVertexSubsMessageByRow(stmt, labelCursor->labelDef.vertexLabel, &rowDataSet, eventType);
    }
    return GMERR_OK;
}

Status QryMatchAgeCondAndGenerateSubsFunc(ChLabelScanDeleteParaT *params, HeapTupleBufT *heapTupleBuf, bool *isMatched)
{
    DB_POINTER2(params, isMatched);
    QryStmtT *stmt = (QryStmtT *)params->stmt;
    QryLabelCursorT *cursor = (QryLabelCursorT *)params->cursor;
    uint8_t partitionId;
    DmVertexLabelT *vertexLabel = cursor->labelDef.vertexLabel;
    Status ret = GMERR_OK;

    *isMatched = false;
    if (!cursor->labelDef.isNotPartitioned) {
        ret = QryGetPartitionId(vertexLabel, heapTupleBuf, &partitionId);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        if (partitionId != params->partitionId) {
            return GMERR_OK;
        }
    }
    QryGetCheckVersion(heapTupleBuf, &params->tupleVersion, vertexLabel->vertexDesc);
    if (params->tupleVersion != params->checkVersion) {
        return GMERR_OK;
    }
    if (params->isGenSubs) {
        ret = QryGenerateSubs4ChLabelDeleteCbs(stmt, (QryLabelCursorT *)params->cursor, heapTupleBuf);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    *isMatched = true;
    return GMERR_OK;
}

static Status QryScanDeleteAgedData(
    QryStmtT *stmt, QryLabelCursorT *cursor, QryAgedTaskT *curTask, ChLabelScanDeleteParaT *userData)
{
    Status ret = GMERR_OK;
    // 每次循环重新赋值
    userData->rowId = DB_INVALID_UINT64;
    userData->isScanEnd = false;
    ret = ChLabelScanDelete(cursor->chRunHdl, cursor->cursor, QryMatchAgeCondAndGenerateSubsFunc, userData);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "age ch label scan delete");
        return ret;
    }

    // 如果扫描结束直接退出
    if (userData->isScanEnd) {
        cursor->isEof = userData->isScanEnd;
        curTask->status = QRY_AGE_STATUS_FINISH;
        return GMERR_OK;
    }

    // 删除二级索引
    // 聚簇容器查找的时候已经给cursor->tupleBuf赋值了
    cursor->heapTuple.heapTupleBuf = TupleBufGet(cursor->tupleBuf);
    cursor->addr = userData->rowId;
    cursor->scanCurBlockId = userData->scanCurBlockId;
    ret = QryRemoveSecondIndexes(stmt, cursor);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 打印age信息，不过这边函数有点奇怪，pk为NULL，是不是聚簇容器不打印信息
    if (curTask->logCount < TASK_LOG_LIMIT) {
        QryPrintAgedInfo(stmt, cursor, curTask);
    }

    // 如果有资源把资源列的资源归还
    if (cursor->labelDef.vertexLabel->commonInfo->resColInfo != NULL) {
        ret = QryFreeRes(stmt, cursor);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }

    if (DmVertexLabelIsNormalLabel(cursor->labelDef.vertexLabel)) {
        QryUpdateStatics(QRY_TUPLE_AGING, QRY_TUPLE_NULL, QryGetStatisticValue(cursor, userData->partitionId));
    }

    return QryStaticChange4CheckAccountForScanDelete(
        stmt, cursor->labelCache, userData->partitionId, &cursor->heapTuple, userData->tupleVersion);
}

static Status QryLoopDeleteAgedDataForChLabel(
    QryStmtT *stmt, QryLabelCursorT *cursor, QryAgedTaskT *curTask, uint64_t *delCnt)
{
    DB_POINTER4(stmt, cursor, curTask, delCnt);
    Status ret;

    // 执行订阅判断语句，防止反复调用函数的情况
    bool isGenerateSubs = false;
    if (QryIsGenSub(cursor->labelDef)) {
        DmSubsEventE eventType = curTask->isTruncate ? DM_SUBS_EVENT_DELETE : DM_SUBS_EVENT_AGED;
        isGenerateSubs = IsGenerateVertexSubsMessages(cursor->labelDef.vertexLabel, eventType);  // 判断是否生成订阅消息
    }

    ChLabelScanDeleteParaT userData = {0};
    userData.stmt = (void *)stmt;
    userData.partitionId = curTask->ageTaskKey.partitionId;
    userData.checkVersion = curTask->ageTaskKey.oldCheckVersion;
    userData.isGenSubs = isGenerateSubs;
    userData.tupleVersion = DB_DATATYPE_UINT8;
    userData.cursor = (void *)cursor;
    userData.rowId = DB_INVALID_UINT64;
    userData.isScanEnd = false;

    // 循环处理老化数据
    while (!QryExceedSplitTime(curTask->startTime, stmt->session->splitTime)) {
        const HpTupleAddr addr = cursor->addr;
        const uint32_t scanCurBlockId = cursor->scanCurBlockId;
        ret = QryScanDeleteAgedData(stmt, cursor, curTask, &userData);
        if (SECUREC_UNLIKELY(ret != GMERR_OK) || userData.isScanEnd) {
            // 生成订阅消息如果无法申请内存，就直接提交
            if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
                DB_LOG_ERROR_AND_SET_LASTERR(
                    ret, "generate subs message, labelName:%s.", cursor->labelDef.vertexLabel->metaCommon.metaName);
                ret = GMERR_OK;
                cursor->addr = addr;  // 当前记录没有老化成功，需要重新扫描
                cursor->scanCurBlockId = scanCurBlockId;
            }
            break;
        }

        (*delCnt)++;  // 统计本次删除数据量
        // 如果有订阅的情况下，超过pushSubsBatch数量就要进行事务的提交，将订阅消息放送出去，无订阅情况则分片或处理完事务提交
        if (isGenerateSubs && *delCnt >= curTask->pushSubsBatch) {
            break;
        }
    }

    return GMERR_OK;
}

static Status QryDeleteAgedData2ChLabel(QryStmtT *stmt, QryAgedTaskT *curTask)
{
    DmCheckInfoT *checkInfo = curTask->checkInfo;
    QryLabelCursorT *cursor = &((QryCursorT *)stmt->qryCursor)->labelCursors[0];
    Status ret = GMERR_OK;
    uint64_t delCnt = 0;

    // 老化统计通过此状态进行判断
    if (curTask->isTruncate) {
        cursor->heapTuple.dataStatus |= QRY_OLD_DATA_STATUS_TRUNCATE;
    } else {
        cursor->heapTuple.dataStatus |= QRY_OLD_DATA_STATUS_AGE;
    }

    bool hasResCol = (cursor->labelDef.vertexLabel->commonInfo->resColInfo != NULL) ? true : false;
    // 提前进行资源列的内存申请防止delete流程的失败
    if (SECUREC_UNLIKELY(hasResCol)) {
        ret = QryInitResData(stmt, cursor->labelDef.vertexLabel);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }

    // 事务设置标记
    SeTransSetSendSubFlag(stmt->session->seInstance);
    ret = QryLoopDeleteAgedDataForChLabel(stmt, cursor, curTask, &delCnt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 释放资源
    if (SECUREC_UNLIKELY(hasResCol)) {
        QryUnInitResData(stmt, cursor->labelDef.vertexLabel);
    }

    QryUpdateTaskInfo(cursor, curTask, checkInfo, delCnt);
    if (ret != GMERR_OK || curTask->status != QRY_AGE_STATUS_AGING) {
        return ret;
    }

    curTask->curHeapTupleAddr.tupleAddr = cursor->addr;
    curTask->curHeapTupleAddr.blockId = cursor->scanCurBlockId;
    return GMERR_OK;
}

static Status QryProcessAgedDataInner(QryStmtT *stmt, QryAgedTaskT *curTask)
{
    DmCheckInfoT *checkInfo = curTask->checkInfo;
    QryLabelCursorT *cursor = &((QryCursorT *)stmt->qryCursor)->labelCursors[0];
    Status ret = QryResetHeapBeginAddr(cursor, curTask);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (!curTask->isRecovery && cursor->isChLabel && !DmIsLabelSupportStatusMerge(cursor->labelDef.vertexLabel) &&
        !cursor->labelDef.vertexLabel->commonInfo->hasUpd) {
        return QryDeleteAgedData2ChLabel(stmt, curTask);
    }
    ret = QrySetAgeCondData(stmt, curTask, cursor);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (curTask->isRecovery) {
        ret = QryRecoveryData(stmt, cursor, curTask, checkInfo);
    } else {
        ret = QryDeleteAgedData(stmt, cursor, curTask, checkInfo);
    }
    if (ret != GMERR_OK || curTask->status != QRY_AGE_STATUS_AGING) {
        return ret;
    }

    curTask->curHeapTupleAddr.tupleAddr = cursor->addr;
    curTask->curHeapTupleAddr.blockId = cursor->scanCurBlockId;
    return GMERR_OK;
}

static Status QryProcessAgedData(QryStmtT *stmt, QryAgedTaskT *curTask)
{
    Status ret;
    QryLabelCursorT *cursor = &((QryCursorT *)stmt->qryCursor)->labelCursors[0];

    ret = QryOpenVertexLabelCursor(stmt, curTask->qryLabel, cursor, true);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (cursor->isChLabel && !curTask->isInitScanPin) {
        ret = ChLabelPin(cursor->chRunHdl);
        if (ret != GMERR_OK) {
            return ret;
        }
        curTask->isInitScanPin = true;
    }

    ret = QryAppendStMgPubsubDataSet(&stmt->session->stmgSubDataSet, cursor->labelDef.vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = QryProcessAgedDataInner(stmt, curTask);
    if (cursor->isChLabel && curTask->status == QRY_AGE_STATUS_FINISH && curTask->isInitScanPin) {
        ChLabelUnPin(cursor->chRunHdl);
    }

    return ret;
}

static Status QryAgeSetPrepareInfo(QryStmtT *stmt, QryAgedTaskT *curTask)
{
    Status ret;
    int32_t printFlag = 0;

    if (curTask->isRecovery) {
        stmt->context->type = QRY_TYPE_UPDATE_VERTEX;
    } else {
        stmt->context->type = QRY_TYPE_DELETE_VERTEX;
    }
    stmt->isAgedTask = true;
    ret = QryAddLabel2ListById(stmt, QRY_LABEL_TYPE_VERTEX, curTask->labelId, true, &curTask->qryLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CataGetNamespaceNameById(
        (DbMemCtxT *)stmt->memCtx, &curTask->nspName, curTask->qryLabel->def.vertexLabel->metaCommon.namespaceId);
    if (ret != GMERR_OK) {
        return ret;
    }
    curTask->pushSubsBatch = curTask->qryLabel->def.vertexLabel->commonInfo->pushAgeVertexBatch;
    if (curTask->pushSubsBatch == 0) {
        curTask->pushSubsBatch = (uint32_t)DbCfgGetInt32Lite(DB_CFG_AGE_PUSH_SUBS_BATCH, NULL);
    }
    printFlag = DbCfgGetInt32Lite(DB_CFG_PRINT_AGED_INFO_ENABLED, NULL);
    g_gmdbAgedMgr.printAgedInfo = (printFlag == 1);
    curTask->status = QRY_AGE_STATUS_AGING;
    return GMERR_OK;
}

static void QryAgeSetExecInfo(QryStmtT *stmt, QryAgedTaskT *curTask)
{
    QryLabelCursorT *cursor = &((QryCursorT *)stmt->qryCursor)->labelCursors[0];

    cursor->scanMode = SCAN_SEQUENCE;
    if (curTask->isRecovery) {
        cursor->action = HEAP_OPTYPE_UPDATE;
    } else {
        cursor->action = HEAP_OPTYPE_DELETE;
    }
    cursor->addr = curTask->curHeapTupleAddr.tupleAddr;
    cursor->scanCurBlockId = curTask->curHeapTupleAddr.blockId;
}

static Status QryPrepareForNewAgedTask(SessionT *session, QryAgedTaskT *curTask)
{
    Status ret;
    QryStmtT *stmt = NULL;

    curTask->taskStartTime = DbClockGetTsc();
    ret = QryAllocStmt(session, &stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = QryAllocCtxFromCtxMemPool(NULL, &stmt->context);
    if (ret != GMERR_OK) {
        QryReleaseStmt(stmt);
        return ret;
    }
    ret = QryAgeSetPrepareInfo(stmt, curTask);
    if (ret != GMERR_OK) {
        QryReleaseStmt(stmt);
        return ret;
    }
    curTask->stmt = stmt;
    return GMERR_OK;
}

static Status QryAgedTaskProcessInner(QryStmtT *stmt, QryAgedTaskT *curTask)
{
    Status ret = GMERR_OK;

    QryLabelT *qryLabel = curTask->qryLabel;
    bool isLiteTrx = QryLabelIsLiteTrx(qryLabel);
    TrxCfgT cfg = {0};
    cfg.isLiteTrx = isLiteTrx;
    cfg.isBackGround = false;  // 老化线程当前台线程特殊处理
    cfg.connId = DB_INVALID_UINT16;
    cfg.trxType = PESSIMISTIC_TRX;
    cfg.isolationLevel = isLiteTrx ? READ_UNCOMMITTED : READ_COMMITTED;
    while (curTask->status == QRY_AGE_STATUS_AGING) {
        if (QryExceedSplitTime(curTask->startTime, stmt->session->splitTime)) {
            return GMERR_OK;
        }

        ret = QryAllocCursor(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }

        QryAgeSetExecInfo(stmt, curTask);
        ret = SeTransBegin(stmt->session->seInstance, &cfg);
        if (ret != GMERR_OK) {
            break;
        }
        SeTransSetDmlHint4RangeUpdate(stmt->session->seInstance);
        ret = QryProcessAgedData(stmt, curTask);
        if (ret == GMERR_OK) {
            ret = QryExecuteCommitTrans(stmt);
            DmCheckInfoT *checkInfo = curTask->checkInfo;
            if (ret == GMERR_OK && checkInfo->checkCnt == 0 && isLiteTrx &&
                checkInfo->shouldAgedCnt != checkInfo->realAgedCnt &&
                qryLabel->def.vertexLabel->metaVertexLabel->labelSubsType ==
                    LABEL_MESSAGE_QUEUE) {  // 老化任务全部结束后，两个计数应该是一致的
                DB_LOG_WARN_AND_SET_LASTERR(GMERR_DATA_CORRUPTED,
                    "process aged data, vertexLabel=%s, shouldAgedCnt=%" PRIu64 ", realAgedCnt=%" PRIu64 ".",
                    qryLabel->def.vertexLabel->metaCommon.metaName, checkInfo->shouldAgedCnt, checkInfo->realAgedCnt);
            }
        }
        if (ret != GMERR_OK) {
            DB_LOG_WARN_AND_SET_LASTERR(ret, "process aged data.");
            (void)QryExecuteRollbackTrans(stmt);
            QryReleaseAllLabelLatch(stmt->session);
            break;
        }
        // 事务成功提交后以及释放labelLatch前才能更新checkInfo
        // 不然RC隔离级别下，数据版本和对账版本可能会不一致
        QryUpdateCheckInfo(stmt->session, curTask);
        QryCloseCursors(stmt);
        QryReleaseAllLabelLatch(stmt->session);
    }
    return ret;
}

static void QryReleaseTaskRes(QryAgedTaskT *curTask)
{
    DbRWLatchW(g_gmdbAgedMgr.latch);
    g_gmdbAgedMgr.curTask = NULL;
    DbRWUnlatchW(g_gmdbAgedMgr.latch);
    if (curTask->stmt != NULL) {
        QryReleaseStmt(curTask->stmt);
        curTask->stmt = NULL;
        curTask->qryLabel = NULL;
    }
}

static void FinishTaskAppend(QryAgedMgrT *agedManager, QryAgedTaskT *agedTask)
{
    // 同V3兼容，显示异常结束任务，结束时间为出队时间
    if (agedTask->taskFinishTime == 0) {
        agedTask->taskFinishTime = DbClockGetTsc();
    }
    DbLinkedListAppend(&agedManager->finishTasks, &agedTask->linkedNode);
    if (agedManager->finishCount < MAX_FINISH_COUNT) {
        agedManager->ageFinish[agedManager->finishCount] = agedTask;
        agedManager->finishCount++;
        return;
    }
    QryAgedTaskT *removeTask = LIST_HEAD_ENTRY(&agedManager->finishTasks, QryAgedTaskT, linkedNode);
    for (uint32_t i = 0; i < MAX_FINISH_COUNT; i++) {
        if (agedManager->ageFinish[i] == removeTask) {
            agedManager->ageFinish[i] = NULL;
            break;
        }
    }
    // 进行迁移，最新数据在最后，每次把最上面的给移除，后面往前迁移。
    for (uint32_t j = 1; j < MAX_FINISH_COUNT; j++) {
        agedManager->ageFinish[j - 1] = agedManager->ageFinish[j];
    }
    agedManager->ageFinish[MAX_FINISH_COUNT - 1] = agedTask;
    DbLinkedListRemoveHead(&agedManager->finishTasks);
    DbDynMemCtxFree(agedManager->memCtx, removeTask);
}

static void QryRemoveTaskFromList(QryAgedTaskT *curTask)
{
    Status ret = GMERR_OK;
    DbRWLatchW(g_gmdbAgedMgr.latch);
    uint32_t hash = QryAgeTaskKeyToHash32(&curTask->ageTaskKey);
    void *value = DbOamapRemove(&g_gmdbAgedMgr.taskMap, hash, &curTask->ageTaskKey);
    if (value == NULL) {
        DbRWUnlatchW(g_gmdbAgedMgr.latch);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "remove the task from Oamap.");
        return;
    }

    ret = DbShmOamapRemove(g_gmdbAgedMgr.shmTaskMap, hash, &curTask->ageTaskKey, QryAgeTaskKeyCompare);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "remove the task from shmOamap.");
        DB_ASSERT(false);
    }

    DbLinkedListRemove(&curTask->linkedNode);
    FinishTaskAppend(&g_gmdbAgedMgr, curTask);
    g_gmdbAgedMgr.taskCount--;
    DbRWUnlatchW(g_gmdbAgedMgr.latch);
}

inline static void QryFetchCurTask(QryAgedTaskT **curTask)
{
    if (g_gmdbAgedMgr.curTask == NULL) {
        DbRWLatchW(g_gmdbAgedMgr.latch);
        g_gmdbAgedMgr.curTask = LIST_HEAD_ENTRY(&g_gmdbAgedMgr.taskList, QryAgedTaskT, linkedNode);
        DbRWUnlatchW(g_gmdbAgedMgr.latch);
    }
    *curTask = g_gmdbAgedMgr.curTask;
}

static bool QryAgedTaskTryLockBgTask(QryAgedTaskT *curTask)
{
    DB_POINTER(curTask);
    if (!curTask->hasBgTaskLock) {
        DmVertexLabelT *vertexLabel = curTask->qryLabel->def.vertexLabel;
        LabelRWLatchT *labelRWLatch =
            (LabelRWLatchT *)GetLabelRWLatchPtrById(vertexLabel->commonInfo->vertexLabelLatchId, NULL);
        if (labelRWLatch == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "get labelLatch when lock, vertexLabel=%s.",
                vertexLabel->metaCommon.metaName);
            return false;  // 如果走到这里就是有BUG
        }

        bool ret = LabelLatchTryLockBgTask(labelRWLatch, BGTASK_AGING);
        if (!ret) {
            DB_LOG_WARN(
                GMERR_UNEXPECTED_NULL_VALUE, "try lock bgTask, vertexLabel=%s.", vertexLabel->metaCommon.metaName);
            return false;
        }
        curTask->hasBgTaskLock = true;
    }
    return true;
}

static void QryAgedTaskUnlockBgTask(QryAgedTaskT *curTask)
{
    DB_POINTER(curTask);
    if (curTask->hasBgTaskLock) {
        DmVertexLabelT *vertexLabel = curTask->qryLabel->def.vertexLabel;
        LabelRWLatchT *labelRWLatch =
            (LabelRWLatchT *)GetLabelRWLatchPtrById(vertexLabel->commonInfo->vertexLabelLatchId, NULL);
        if (labelRWLatch == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "get labelLatch when unlock, vertexLabel=%s.",
                vertexLabel->metaCommon.metaName);
            return;  // 如果走到这里就是有BUG
        }

        LabelLatchUnlockBgTask(labelRWLatch, BGTASK_AGING);
        curTask->hasBgTaskLock = false;
    }
}

static void QryRunTaskStatusTransfer(SessionT *session, uint64_t currTime)
{
    QryAgedTaskT *curTask = NULL;
    Status ret = GMERR_OK;
    QryFetchCurTask(&curTask);
    if (curTask->status != QRY_AGE_STATUS_AGING) {
        ret = QryPrepareForNewAgedTask(session, curTask);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
    }

    if (!QryAgedTaskTryLockBgTask(curTask)) {
        ret = GMERR_OK;
        goto EXIT;
    }

    curTask->startTime = currTime;
    ret = QryAgedTaskProcessInner(curTask->stmt, curTask);
EXIT:
    if (ret == GMERR_UNDEFINED_TABLE) {
        curTask->status = QRY_AGE_STATUS_FINISH;
    } else if (ret != GMERR_OK) {
        curTask->status = QRY_AGE_STATUS_FAILED;
    }
    switch (curTask->status) {
        case QRY_AGE_STATUS_FINISH:
            DB_LOG_WARN(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
                "Aged task finished, label(id: %" PRIu32 ", partitionId: %" PRIu32 ") should aged count: %" PRIu64
                ", real aged count: %" PRIu64 ", should truncate count: %" PRIu64 ", real truncate count: %" PRIu64 ".",
                curTask->labelId, curTask->ageTaskKey.partitionId, curTask->checkInfo->shouldAgedCnt,
                curTask->checkInfo->realAgedCnt, curTask->checkInfo->shouldTruncateCnt,
                curTask->checkInfo->realTruncatedCnt);
            QryAgedTaskUnlockBgTask(curTask);  // 必须放在前面，对curTask中成员有依赖
            QryReleaseTaskRes(curTask);
            QryRemoveTaskFromList(curTask);
            break;
        case QRY_AGE_STATUS_FAILED:
            QryAgedTaskUnlockBgTask(curTask);  // 必须放在前面，对curTask中成员有依赖
            QryReleaseTaskRes(curTask);
            curTask->curHeapTupleAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
            curTask->curHeapTupleAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
            break;
        case QRY_AGE_STATUS_WAITING:
            QryReleaseTaskRes(curTask);
            curTask->curHeapTupleAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
            curTask->curHeapTupleAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
            break;
        case QRY_AGE_STATUS_AGING:
        default:
            break;
    }
}

bool QryAgedTaskProc(void)
{
    if (g_gmdbAgedMgr.session != NULL) {
        QryClearInvalidCache(g_gmdbAgedMgr.session);
        QryClearInvalidSubsCache(g_gmdbAgedMgr.session);
    }
    if (g_gmdbAgedMgr.taskCount == 0) {
        return true;
    }

    if (SECUREC_UNLIKELY(g_gmdbAgedMgr.session == NULL)) {
        Status ret = QryCreateBgTaskSession("AgedTask", &g_gmdbAgedMgr.session);
        if (ret != GMERR_OK) {
            return false;
        }
    }
    QryRunTaskStatusTransfer(g_gmdbAgedMgr.session, DbClockGetTsc());
    return false;
}

void QryFreeAgedMgr(void)
{
    QryAgedMgrT *ctx = &g_gmdbAgedMgr;
    if (ctx->session != NULL) {
        QrySessionRelease(ctx->session);
        ctx->session = NULL;
    }
    if (ctx->memCtx != NULL) {
        DbDeleteDynMemCtx(ctx->memCtx);
        ctx->memCtx = NULL;
    }

    if (ctx->shmemCtx != NULL) {
        DbDeleteShmemCtx(ctx->shmemCtx);
        ctx->shmemCtx = NULL;
    }

    // 防止指向已经释放的内存
    ctx->printBuf = (TextT){.str = NULL, .len = 0};
    ctx->curTask = NULL;
    ctx->session = NULL;
    ctx->taskList = (TagLinkedListT){0};
    ctx->finishTasks = (TagLinkedListT){0};
    ctx->hasInit = false;
    ctx->latch = NULL;
    ctx->latchShmAddr = DB_INVALID_SHMPTR;
    ctx->shmTaskMap = NULL;
    ctx->shmTaskMapAddr = DB_INVALID_SHMPTR;
}

static void QryInitAgedMgrProp(QryAgedMgrT *agedMgr)
{
    DB_POINTER(agedMgr);
    agedMgr->printAgedInfo = false;
    agedMgr->taskCount = 0;
    agedMgr->printAgedInfo = false;
    agedMgr->printBuf = (TextT){.str = NULL, .len = 0};
    agedMgr->curTask = NULL;
    DbLinkedListInit(&agedMgr->taskList);
    DbLinkedListInit(&agedMgr->finishTasks);
}

inline static void InitAgeTashMapArgs(DbShmOamapInitArgT *args, QryAgedMgrT *agedMgr)
{
    args->capacity = LABEL_CACHE_MAP_INIT_CAP;
    args->memCtx = agedMgr->shmemCtx;
    args->extendable = true;
    // 深度预留内存，保证对账结束后因内存资源不足失败。
    args->fixedSizeDeepExtend = true;
    args->keySize = (uint32_t)sizeof(QryAgeTaskKeyT);
    args->valueSize = (uint32_t)sizeof(bool);
    args->selfPtr = agedMgr->shmTaskMapAddr;
}

static Status QryInitAgedTaskMap(QryAgedMgrT *agedMgr)
{
    DB_POINTER(agedMgr);
    Status ret = DbOamapInit(&agedMgr->taskMap, LABEL_CACHE_MAP_INIT_CAP, QryAgeTaskKeyCompare, agedMgr->memCtx, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init age truncateVersion map.");
        return ret;
    }

    uint32_t oamapSize = (uint32_t)sizeof(DbShmOamapT);
    agedMgr->shmTaskMapAddr = DbShmemCtxAlloc(agedMgr->shmemCtx, oamapSize);
    agedMgr->shmTaskMap = DbShmPtrToAddr(agedMgr->shmTaskMapAddr);
    if (agedMgr->shmTaskMap == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "alloc memory for oamap, size:%" PRIu32 ".", oamapSize);
        return GMERR_OUT_OF_MEMORY;
    }
    DbShmOamapInitArgT args = {0};
    InitAgeTashMapArgs(&args, agedMgr);
    ret = DbShmOamapInit(agedMgr->shmTaskMap, &args);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init age truncateVersion shmTaskMap.");
        return ret;
    }
    return GMERR_OK;
}

static DbMemCtxT *CreateAgedTaskShmemCtx(void)
{
    const char *ctxName = "aged mgr share memctx";

    // 创建缩容管理共享内存上下文
    DbMemCtxArgsT shmCtxArgs = {0};
    shmCtxArgs.instanceId = DbGetProcGlobalId();
    shmCtxArgs.ctxId = DB_EE_AGED_MGR_CTX_ID;

    DbBlockMemParamT param = {0};
    DbBlockMemParamT *blockParam = &param;
    blockParam->isHugePage = false;
    blockParam->baseSize = AGED_TASK_SHM_CTX_BASE_SIZE;
    blockParam->stepSize = AGED_TASK_SHM_CTX_STEP_SIZE;
    uint64_t maxSize = DbGetSysShmemMaxSize();
    // limitSize为memCtx通过配置参数计算的内存上限，创建的时候maxSize不能超过该值。
    uint64_t limitSize = blockParam->baseSize + (uint64_t)blockParam->stepSize * (DbMemCtxGetBlockPoolMaxSegNum() - 1);
    limitSize = DB_MIN(UINT32_MAX, limitSize);  // 参数数值不超过uint32
    blockParam->maxSize = (uint64_t)DB_MIN(maxSize, limitSize);
    blockParam->isReused = true;
    blockParam->allowBigChunk = true;
    blockParam->blkPoolType = BLK_NORMAL;
    AlgoParamT algoParam = {0};
    algoParam.blockParam = blockParam;
    shmCtxArgs.algoParam = &algoParam;

    return (DbMemCtxT *)DbCreateBlockPoolShmemCtx(DbSrvGetSysShmCtx(DbGetProcGlobalId()), ctxName, &shmCtxArgs);
}

static Status QryInitDwProp(QryAgedMgrT *agedMgr)
{
    agedMgr->shmemCtx = CreateAgedTaskShmemCtx();
    if (agedMgr->shmemCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "create age task memctx.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    uint32_t latchSize = (uint32_t)sizeof(DbLatchT);
    agedMgr->latchShmAddr = DbShmemCtxAlloc(agedMgr->shmemCtx, latchSize);
    agedMgr->latch = DbShmPtrToAddr(agedMgr->latchShmAddr);
    if (agedMgr->latch == NULL) {
        DbDeleteShmemCtx(agedMgr->shmemCtx);
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_MEMORY_OPERATE_FAILED, "alloc memory for latch, size:%" PRIu32 ".", latchSize);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    DbRWLatchInit(agedMgr->latch);
    return GMERR_OK;
}

static Status QryInitAgedMgr(QryAgedMgrT *agedMgr)
{
    DB_POINTER(agedMgr);
    Status ret;

    QryInitAgedMgrProp(agedMgr);

    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        ret = GMERR_INTERNAL_ERROR;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get instance.");
        return ret;
    }

    agedMgr->workerMgr = &drtIns->workerMgr;

    DbMemCtxArgsT args = {0};
    DbMemCtxT *sysDynCtx = DbSrvGetSysDynCtx(DbGetProcGlobalId());
    if (sysDynCtx == NULL) {
        ret = GMERR_INTERNAL_ERROR;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get sys dyn memctx.");
        return ret;
    }

    // memCtx用途：老化任务使用
    // 生命周期：专项任务生命周期
    // 释放方式：就近释放
    // 兜底清空措施：无
    agedMgr->memCtx = DbCreateDynMemCtx(sysDynCtx, true, "top memory context of age mgr.", &args);
    if (agedMgr->memCtx == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "create dynamic memctx.");
        return ret;
    }

    ret = QryInitAgedTaskMap(agedMgr);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 内存释放点:无;生命周期:进程级
    agedMgr->maxBufLen = (uint32_t)DbCfgGetInt32Lite(DB_CFG_LOG_LENGTH_MAX, NULL) + 1;
    agedMgr->printBuf.str = (char *)DbDynMemCtxAlloc(agedMgr->memCtx, agedMgr->maxBufLen);
    if (agedMgr->printBuf.str == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "alloc memory for age printBuf, size:%" PRIu32 ".", agedMgr->maxBufLen);
        return ret;
    }
    return GMERR_OK;
}

Status QryCreateAgedServiceImpl(void)
{
    if (g_gmdbAgedMgr.hasInit) {
        return GMERR_OK;
    }
    DbSpinLock(&g_gmdbAgedMgr.lock);
    if (g_gmdbAgedMgr.hasInit) {
        DbSpinUnlock(&g_gmdbAgedMgr.lock);
        return GMERR_OK;
    }

    Status ret = QryInitDwProp(&g_gmdbAgedMgr);
    if (ret != GMERR_OK) {
        DbSpinUnlock(&g_gmdbAgedMgr.lock);
        return ret;
    }

    ret = QryInitAgedMgr(&g_gmdbAgedMgr);
    if (ret != GMERR_OK) {
        QryFreeAgedMgr();
        DbSpinUnlock(&g_gmdbAgedMgr.lock);
        return ret;
    }

    g_gmdbAgedMgr.hasInit = true;
    DbSpinUnlock(&g_gmdbAgedMgr.lock);
    return GMERR_OK;
}

static void QrySetAgedTaskInfo(const QryAgedTaskArgsT *agedTaskArgs, QryAgedTaskT *task)
{
    DmCheckInfoT *checkInfo = QryGetCheckInfoByIdForServer(agedTaskArgs->label, agedTaskArgs->partitionId);

    task->status = QRY_AGE_STATUS_WAITING;
    task->curHeapTupleAddr.tupleAddr = BEFORE_FIRST_TUPLE_ADDR;
    task->curHeapTupleAddr.blockId = BEFORE_FIRST_TUPLE_BLOCK_ID;
    task->checkInfo = checkInfo;
    task->labelId = agedTaskArgs->label->metaCommon.metaId;
    task->ageTaskKey.labelId = task->labelId;
    task->ageTaskKey.partitionId = agedTaskArgs->partitionId;
    task->ageTaskKey.oldCheckVersion = agedTaskArgs->oldVersion;
    task->targetCheckVersion = checkInfo->version.version;
    task->createTime = DbClockGetTsc();
    task->isTruncate = agedTaskArgs->isTruncate;
    task->isRecovery = checkInfo->version.checkStatus == DM_CHECK_STATUS_ABNORMAL;
    task->taskStartTime = 0;
    task->taskFinishTime = 0;
    task->logCount = 0;
    task->hasBgTaskLock = false;
    task->isInitScanPin = false;
}

Status QryAddAgeTaskKey(QryAgedTaskT *task)
{
    uint32_t hash = QryAgeTaskKeyToHash32(&task->ageTaskKey);
    Status ret = DbOamapInsert(&g_gmdbAgedMgr.taskMap, hash, &task->ageTaskKey, task, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret,
            "insert key to oamap, labelId: %" PRIu32 ", partitionId=%" PRIu8 ", oldCheckVersion=%" PRIu8 ".",
            task->ageTaskKey.labelId, task->ageTaskKey.partitionId, task->ageTaskKey.oldCheckVersion);
        return ret;
    }
    ret = DbShmOamapInsert(g_gmdbAgedMgr.shmTaskMap, hash,
        (DbShmOamapKVT){&task->ageTaskKey, &task->isTruncate, sizeof(QryAgeTaskKeyT), sizeof(bool)}, NULL,
        (DbShmOamapFuncsT){QryAgeTaskKeyCopy, QryAgeTaskValueCopy, QryAgeTaskKeyCompare});
    if (ret != GMERR_OK) {
        // 发生残留尝试删除
        (void)DbShmOamapRemove(g_gmdbAgedMgr.shmTaskMap, hash, &task->ageTaskKey, QryAgeTaskKeyCompare);
        (void)DbOamapRemove(&g_gmdbAgedMgr.taskMap, hash, &task->ageTaskKey);
        DB_LOG_ERROR(ret,
            "insert key to shmHashMap, labelId: %" PRIu32 ", partitionId=%" PRIu8 ", oldCheckVersion=%" PRIu8 ".",
            task->ageTaskKey.labelId, task->ageTaskKey.partitionId, task->ageTaskKey.oldCheckVersion);
        return ret;
    }
    return GMERR_OK;
}

Status QryAllocTaskFromAgedMgrImpl(void **buf)
{
    Status ret = GMERR_OK;
    // 内存释放点:QryRemoveTaskFromList，异常情况显示调用QryReleaseTaskFromAgedMgr释放
    // 这个MemCtx是设置成共享的，所以可以放在锁外面。
    const size_t agedTaskSize = sizeof(QryAgedTaskT);
    void *task = DbDynMemCtxAlloc(g_gmdbAgedMgr.memCtx, agedTaskSize);
    if (task == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "alloc memctx, len=%" PRIu32 ".", (uint32_t)agedTaskSize);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(task, agedTaskSize, 0x00, agedTaskSize);
    *buf = (void *)task;

    DbRWLatchW(g_gmdbAgedMgr.latch);
    ret = DbOamapExtend(&g_gmdbAgedMgr.taskMap);  // 给map扩容，确保AddAgeTask时有足够空间
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbShmOamapExtend(g_gmdbAgedMgr.shmTaskMap);  // 给map扩容，确保AddAgeTask时有足够空间
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

void QryEmplaceAgedTaskImpl(const QryAgedTaskArgsT *agedTaskArgs, void *buf)
{
    DB_POINTER2(agedTaskArgs, buf);
    if (agedTaskArgs->shouldProcessCnt == 0) {
        DbDynMemCtxFree(g_gmdbAgedMgr.memCtx, buf);
        goto EXIT;
    }
    QryAgedTaskT *task = (QryAgedTaskT *)buf;
    QrySetAgedTaskInfo(agedTaskArgs, task);
    // QryAllocTaskFromAgedMgr中已提前给map扩容，确保此处不会产生DUPLICATE以外的错误，为确保老化流程的原子性也不应出错
    Status ret = QryAddAgeTaskKey(task);
    if (ret == GMERR_DUPLICATE_OBJECT) {
        DbDynMemCtxFree(g_gmdbAgedMgr.memCtx, buf);
        goto EXIT;
    }
    DbLinkedListAppend(&g_gmdbAgedMgr.taskList, &task->linkedNode);
    g_gmdbAgedMgr.taskCount++;
    (void)DbBgTaskNotify(BG_TASK_CHECK);
EXIT:
    DbRWUnlatchW(g_gmdbAgedMgr.latch);
}

void QryReleaseTaskFromAgedMgrImpl(void *buf)
{
    DbDynMemCtxFree(g_gmdbAgedMgr.memCtx, buf);
    DbRWUnlatchW(g_gmdbAgedMgr.latch);
}

Status QryCheckAgeTasksDuplicateInnerImpl(DmVertexLabelT *label, uint8_t partitionId)
{
    DB_POINTER(label);
    Status ret = QryCreateAgedServiceImpl();
    if (ret != GMERR_OK) {
        return ret;
    }
    QryAgeTaskKeyT ageTaskKey;
    uint8_t version = QryGetCheckVersionByIdForServer(label, partitionId);
    uint8_t checkVersion = (version == DB_MAX_UINT8) ? 0 : version + 1;
    SetQryAgeTaskKey(&ageTaskKey, label->metaCommon.metaId, partitionId, checkVersion);
    uint32_t hash = QryAgeTaskKeyToHash32(&ageTaskKey);
    void *isTruncate = NULL;
    DbRWLatchW(g_gmdbAgedMgr.latch);
    void *value = DbOamapLookup(&g_gmdbAgedMgr.taskMap, hash, &ageTaskKey, NULL);
    ret = DbShmOamapLookup(
        g_gmdbAgedMgr.shmTaskMap, (DbShmOamapFindParaT){hash, &ageTaskKey}, NULL, &isTruncate, QryAgeTaskKeyCompare);
    DbRWUnlatchW(g_gmdbAgedMgr.latch);
    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret,
            "lookup vertexLabel(%s) truncate status from shmHashMap, partitionId:%" PRIu8 ", checkVersion:%" PRIu8 ".",
            label->metaCommon.metaName, partitionId, checkVersion);
        return ret;
    }
    if (value != NULL) {
        DB_ASSERT(ret == GMERR_OK);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "Exceeds the maximum number of consecutive check in vertexLabel %s, please try again later.",
            label->metaCommon.metaName);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    // taskMap 中查找的 value == NULL，则 shmTaskMap 的返回值必定为 GMERR_NO_DATA
    DB_ASSERT(ret == GMERR_NO_DATA);
    return GMERR_OK;
}

Status QryCheckAgeTasksDuplicateImpl(DmVertexLabelT *label)
{
    DB_POINTER(label);
    Status ret;
    if (!label->commonInfo->accCheckAddr->isPartition) {
        ret = QryCheckAgeTasksDuplicateInnerImpl(label, DB_MAX_UINT8);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        for (uint8_t i = 0; i < DM_MAX_PARTITION_ID; i++) {
            ret = QryCheckAgeTasksDuplicateInnerImpl(label, i);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return ret;
}

Status QryHeapTupleBufIsTruncated(QryLabelCursorT *cursor, bool *isTruncate)
{
    DB_POINTER2(cursor, isTruncate);
    uint8_t partitionId = DB_INVALID_UINT8;
    uint8_t checkVersion;
    Status ret = GMERR_OK;
    DmVertexLabelT *vertexLabel = cursor->labelDef.vertexLabel;
    if (SECUREC_UNLIKELY(!cursor->labelDef.isNotPartitioned)) {
        if ((ret = QryGetPartitionId(vertexLabel, &cursor->heapTuple.heapTupleBuf, &partitionId)) != GMERR_OK) {
            return ret;
        }
    }
    DmVertexDescT *vertexDesc = vertexLabel->vertexDesc;
    QryGetCheckVersion(&cursor->heapTuple.heapTupleBuf, &checkVersion, vertexDesc);
    QryAgeTaskKeyT ageTaskKey;
    SetQryAgeTaskKey(&ageTaskKey, vertexLabel->metaCommon.metaId, partitionId, checkVersion);
    uint32_t hash = QryAgeTaskKeyToHash32(&ageTaskKey);
    void *isTruncateTmp = NULL;
    DbRWLatchW(g_gmdbAgedMgr.latch);
    void *value = DbOamapLookup(&g_gmdbAgedMgr.taskMap, hash, &ageTaskKey, NULL);
    ret = DbShmOamapLookup(
        g_gmdbAgedMgr.shmTaskMap, (DbShmOamapFindParaT){hash, &ageTaskKey}, NULL, &isTruncateTmp, QryAgeTaskKeyCompare);
    DbRWUnlatchW(g_gmdbAgedMgr.latch);
    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret,
            "lookup vertexLabel(%s) truncate status, partitionId:%" PRIu8 ", checkVersion:%" PRIu8 ".",
            vertexLabel->metaCommon.metaName, partitionId, checkVersion);
        return ret;
    }
    if (value == NULL) {
        *isTruncate = false;
        DB_ASSERT(ret == GMERR_NO_DATA);
        return GMERR_OK;
    }
    DB_ASSERT(ret == GMERR_OK);
    DB_ASSERT(*(bool *)isTruncateTmp == ((QryAgedTaskT *)value)->isTruncate);
    *isTruncate = ((QryAgedTaskT *)value)->isTruncate;
    return GMERR_OK;
}

void RegAgedTaskFunctions(ImportFastPathFunctionsT *fastPathFunctions)
{
    fastPathFunctions->qryEmplaceAgedTask = QryEmplaceAgedTaskImpl;
    fastPathFunctions->qryAllocTaskFromAgedMgr = QryAllocTaskFromAgedMgrImpl;
    fastPathFunctions->qryReleaseTaskFromAgedMgr = QryReleaseTaskFromAgedMgrImpl;
    fastPathFunctions->qryCheckAgeTasksDuplicate = QryCheckAgeTasksDuplicateImpl;
    fastPathFunctions->qryCheckAgeTasksDuplicateInner = QryCheckAgeTasksDuplicateInnerImpl;
    fastPathFunctions->qryCreateAgedService = QryCreateAgedServiceImpl;
    fastPathFunctions->getQryAgedMgr = GetQryAgedMgrImpl;
}

#ifdef __cplusplus
}
#endif
