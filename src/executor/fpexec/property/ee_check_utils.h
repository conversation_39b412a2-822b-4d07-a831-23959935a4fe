/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: header file of utils for check aged.
 * Author: EE Team
 * Create: 2023-06-14
 */

#ifndef EE_CHECK_UTILS_H
#define EE_CHECK_UTILS_H

#include "drt_base_def.h"
#include "ee_dml_desc.h"
#include "db_shm_hashmap.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_FINISH_COUNT 3

typedef struct QryAgedMgr {
    DbLatchT *latch;
    ShmemPtrT latchShmAddr;
    DbShmOamapT *shmTaskMap;
    ShmemPtrT shmTaskMapAddr;
    DbMemCtxT *shmemCtx;
    DbSpinLockT lock;
    bool hasInit;
    bool printAgedInfo;
    DbMemCtxT *memCtx;  // dynamic memctx
    SessionT *session;
    WorkerMgrT *workerMgr;
    uint32_t taskCount;
    TextT printBuf;
    uint32_t maxBufLen;
    DbOamapT taskMap;
    QryAgedTaskT *curTask;
    TagLinkedListT taskList;
    uint32_t finishCount;  // count of finished task
    TagLinkedListT finishTasks;
    QryAgedTaskT *ageFinish[MAX_FINISH_COUNT];  // 缓存3个已完成的任务；
} QryAgedMgrT;

typedef struct QryAgedTaskArgs {
    DmVertexLabelT *label;
    uint8_t partitionId;
    uint8_t oldVersion;
    bool isTruncate;
    uint8_t reserved;
    uint64_t shouldProcessCnt;
} QryAgedTaskArgsT;

extern DB_THREAD_LOCAL bool g_gmdbIsServerThreadTmp;

// 性能优化，服务端的获取可以直接调用该函数
inline static DmCheckInfoT *QryGetCheckInfoByIdForServer(DmVertexLabelT *vertexLabel, uint8_t partitionId)
{
    DmAccCheckT *accCheckAddr = vertexLabel->commonInfo->accCheckAddr;
    return partitionId == DB_INVALID_UINT8 ? &accCheckAddr->checkInfo[0] : &accCheckAddr->checkInfo[partitionId];
}

// 直连写会调用这个接口
inline static Status QryGetCheckInfoById(DmVertexLabelT *vertexLabel, uint8_t partitionId, DmCheckInfoT **checkInfo)
{
    DmAccCheckT *accCheckAddr;
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    if (SECUREC_LIKELY(g_gmdbIsServerThreadTmp)) {
        accCheckAddr = commonInfo->accCheckAddr;
    } else if (DbCommonIsServer()) {
        g_gmdbIsServerThreadTmp = true;
        accCheckAddr = commonInfo->accCheckAddr;
    } else {
        accCheckAddr = (DmAccCheckT *)DbShmPtrToAddr(commonInfo->accCheckShm);
        if (SECUREC_UNLIKELY(accCheckAddr == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED,
                "trans shm to addr, segId: %" PRIu32 ", offset: %" PRIu32, commonInfo->accCheckShm.segId,
                commonInfo->accCheckShm.offset);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }

    if (SECUREC_UNLIKELY(partitionId != DB_INVALID_UINT8)) {
        *checkInfo = &accCheckAddr->checkInfo[partitionId];
    } else {
        *checkInfo = &accCheckAddr->checkInfo[0];
    }

    return GMERR_OK;
}

// 仅服务端调用，直连写不能使用该接口
inline static uint8_t QryGetCheckVersionByIdForServer(DmVertexLabelT *vertexLabel, uint8_t partitionId)
{
    DB_POINTER(vertexLabel);
    return (QryGetCheckInfoByIdForServer(vertexLabel, partitionId))->version.version;
}

inline static void QrySetAgedTaskArgs(
    QryAgedTaskArgsT *agedTaskArgs, uint8_t partitionId, bool isTruncate, uint64_t shouldProcessCnt, uint8_t oldVersion)
{
    agedTaskArgs->partitionId = partitionId;
    agedTaskArgs->isTruncate = isTruncate;
    agedTaskArgs->shouldProcessCnt = shouldProcessCnt;
    agedTaskArgs->oldVersion = oldVersion;
}

bool QryIsAged(const DmCheckVersionT *version, uint8_t checkVersion);
Status QryGetPartitionId(DmVertexLabelT *vertexLabel, const HeapTupleBufT *heapTupleBuf, uint8_t *partitionId);
Status QryGetCheckInfo(DmVertexLabelT *vertexLabel, const HeapTupleBufT *heapTupleBuf, DmCheckInfoT **checkInfo);
void QryGetCheckVersion(const HeapTupleBufT *heapTupleBuf, uint8_t *checkVersion, DmVertexDescT *vertexDesc);
Status QryIsVertexBufAged(
    const HeapTupleBufT *heapTupleBuf, DmVertexLabelT *vertexLabel, bool *isAged, DmVertexDescT *vertexDesc);

#ifdef __cplusplus
}
#endif

#endif /* EE_CHECK_UTILS_H */
