/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: receiver for serialized table
 * Author: GQL
 * Create: 2023-09-15
 */

#include "ee_gql_batch_dml_receiver.h"
#include "ee_log.h"

#define PROPERTY_NUM_MAX 254
#define PATH_RECEIVER_SIZE sizeof(PathBatchDmlReceiverT)

Status ReceivePathBatchDml(DestReceiverT *destReceiver, EEResultT *eeResult, DbInstanceHdT dbInstance);

Status InitPathBatchDmlReceiver(DbMemCtxT *memCtx, FusionModelQryStmtT *stmt)
{
    DB_POINTER2(stmt, stmt->session);
    // memory release point: after rule engine used
    PathBatchDmlReceiverT *receiver = (PathBatchDmlReceiverT *)DbDynMemCtxAlloc(memCtx, PATH_RECEIVER_SIZE);
    if (SECUREC_UNLIKELY(receiver == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "path batch dml receiver alloc");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(receiver, PATH_RECEIVER_SIZE, 0, PATH_RECEIVER_SIZE);
    receiver->destReceiver.receivePrepare = NULL;
    receiver->destReceiver.receive = ReceivePathBatchDml;
    receiver->destReceiver.receiveEnd = NULL;
    stmt->receiverType = GQL_BATCH_DML_RESULT;
    receiver->memCtx = memCtx;

    stmt->destReceiver = (DestReceiverT *)receiver;
    return GMERR_OK;
}

static Status SetBitMap4BatchReplace(PathBatchDmlReceiverT *dmlReceiver, EEResultT *eeResult)
{
    uint32_t bitmapNum = eeResult->aa->topo.info.bitmapNum;
    DbListT bitmapList = eeResult->aa->topo.info.bitmap;
    // memory release point: abnormal branch; after rule engine used
    dmlReceiver->bitmap = (DbBitmapT **)DbDynMemCtxAlloc(dmlReceiver->memCtx, sizeof(DbBitmapT *) * bitmapNum);
    if (dmlReceiver->bitmap == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "path batch dml receiver bitmap array alloc");
        DbDynMemCtxFree(dmlReceiver->memCtx, dmlReceiver->hpAddrs);
        return GMERR_OUT_OF_MEMORY;
    }
    uint32_t i = 0;
    Status ret = GMERR_OK;
    for (i = 0; i < bitmapNum; i++) {
        DbBitmapT *desBitmap = DbBitmapCreate(dmlReceiver->memCtx, PROPERTY_NUM_MAX);
        if (desBitmap == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "path batch dml receiver bitmap array alloc");
            ret = GMERR_OUT_OF_MEMORY;
            goto RELEASE;
        }
        dmlReceiver->bitmap[i] = desBitmap;
        DbBitmapT *srcBitmap = (DbBitmapT *)DbListItem(&bitmapList, i);
        ret = DbBitmapFill(dmlReceiver->bitmap[i], srcBitmap);
        if (ret != GMERR_OK) {
            goto RELEASE;
        }
    }
    dmlReceiver->bitmapNum = bitmapNum;
    return GMERR_OK;
RELEASE:
    for (uint32_t j = 0; j < i; j++) {
        DbBitmapDestroy(dmlReceiver->bitmap[i]);
    }
    DbDynMemCtxFree(dmlReceiver->memCtx, dmlReceiver->bitmap);
    return ret;
}

static Status ReceivePathBatchDmlReplace(PathBatchDmlReceiverT *dmlReceiver, EEResultT *eeResult)
{
    uint32_t bitmapNum = eeResult->aa->topo.info.bitmapNum;
    uint32_t batchSize = eeResult->aa->prop.size;
    if (eeResult->aa->prop.originTupleAddr != NULL) {
        // memory release point: abnormal branch; after rule engine used
        dmlReceiver->hpAddrs = (HpTupleAddr *)DbDynMemCtxAlloc(dmlReceiver->memCtx, sizeof(HpTupleAddr) * batchSize);
        if (SECUREC_UNLIKELY(dmlReceiver->hpAddrs == NULL)) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "path batch dml receiver tuple addr alloc");
            return GMERR_OUT_OF_MEMORY;
        }
        for (uint32_t k = 0; k < batchSize; k++) {
            dmlReceiver->hpAddrs[k] = eeResult->aa->prop.originTupleAddr[k];
        }
    } else {
        dmlReceiver->hpAddrs = NULL;
    }
    if (SECUREC_LIKELY(bitmapNum == 0)) {
        dmlReceiver->bitmap = NULL;
        dmlReceiver->bitmapNum = 0;
        return GMERR_OK;
    }
    return SetBitMap4BatchReplace(dmlReceiver, eeResult);
}

static void CleanVertexBufs(PathBatchDmlReceiverT *dmlReceiver, uint32_t tupleSize)
{
    for (uint32_t tupleIdx = 0; tupleIdx < tupleSize; tupleIdx++) {
        DbDynMemCtxFree(dmlReceiver->memCtx, dmlReceiver->vertexBufs[tupleIdx]);
        dmlReceiver->vertexBufSizes[tupleIdx] = 0;
    }
    dmlReceiver->size = 0;
    DbDynMemCtxFree(dmlReceiver->memCtx, dmlReceiver->vertexBufSizes);
    dmlReceiver->vertexBufSizes = NULL;
    DbDynMemCtxFree(dmlReceiver->memCtx, dmlReceiver->vertexBufs);
    dmlReceiver->vertexBufs = NULL;
    DbDynMemCtxFree(dmlReceiver->memCtx, dmlReceiver->hpAddrs);
    dmlReceiver->hpAddrs = NULL;
}

static Status ReceivePathBatchDmlDelete(PathBatchDmlReceiverT *dmlReceiver, EEResultT *eeResult)
{
    AASlotT *tuple;
    dmlReceiver->size = eeResult->aa->prop.writePos;
    dmlReceiver->hpAddrs =
        (HpTupleAddr *)DbDynMemCtxAlloc(dmlReceiver->memCtx, sizeof(HpTupleAddr) * eeResult->aa->prop.writePos);
    if (dmlReceiver->hpAddrs == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "hpAddrs alloc");
        return GMERR_OUT_OF_MEMORY;
    }
    dmlReceiver->vertexBufs =
        (uint8_t **)DbDynMemCtxAlloc(dmlReceiver->memCtx, sizeof(uint8_t *) * eeResult->aa->prop.writePos);
    if (dmlReceiver->vertexBufs == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "vertexBufs alloc");
        return GMERR_OUT_OF_MEMORY;
    }
    dmlReceiver->vertexBufSizes =
        (uint32_t *)DbDynMemCtxAlloc(dmlReceiver->memCtx, sizeof(uint32_t) * eeResult->aa->prop.writePos);
    if (dmlReceiver->vertexBufSizes == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "vertexBufSizes alloc");
        return GMERR_OUT_OF_MEMORY;
    }
    uint32_t tupleIdx = 0;
    while ((tuple = AAGetNextPropSlot(eeResult->aa)) != NULL) {
        dmlReceiver->hpAddrs[tupleIdx] = tuple->addr;
        Status ret = DmVertexGetSeriBufLength(tuple->dmVertex, &(dmlReceiver->vertexBufSizes[tupleIdx]));
        if (ret != GMERR_OK) {
            CleanVertexBufs(dmlReceiver, tupleIdx);
            DB_LOG_ERROR(ret, "get vertex len");
            return ret;
        }
        uint8_t *vertex = (uint8_t *)DbDynMemCtxAlloc(dmlReceiver->memCtx, dmlReceiver->vertexBufSizes[tupleIdx]);
        ret = DmSerializeVertex2InvokerBuf(tuple->dmVertex, dmlReceiver->vertexBufSizes[tupleIdx], vertex);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(dmlReceiver->memCtx, vertex);
            CleanVertexBufs(dmlReceiver, tupleIdx);
            DB_LOG_ERROR(ret, "serialize vertex");
            return ret;
        }
        dmlReceiver->vertexBufs[tupleIdx] = vertex;
        tupleIdx++;
    }
    return GMERR_OK;
}

Status ReceivePathBatchDml(DestReceiverT *destReceiver, EEResultT *eeResult, DbInstanceHdT dbInstance)
{
    DB_UNUSED(dbInstance);
    DB_POINTER2(destReceiver, eeResult);
    PathBatchDmlReceiverT *dmlReceiver = (PathBatchDmlReceiverT *)(void *)destReceiver;
    if (eeResult->tagType == T_GQL_REPLACE || eeResult->tagType == T_GQL_INSERT) {
        // replace
        return ReceivePathBatchDmlReplace(dmlReceiver, eeResult);
    } else if (eeResult->tagType == T_DELETE) {
        // delete
        return ReceivePathBatchDmlDelete(dmlReceiver, eeResult);
    } else {
        // unsupported type
        return GMERR_INTERNAL_ERROR;
    }
}
