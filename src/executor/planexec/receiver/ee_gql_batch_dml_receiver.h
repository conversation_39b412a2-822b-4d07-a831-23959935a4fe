/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: receive result for path batch dml.
 * Author: GQL
 * Create: 2023-09-15
 */

#ifndef EE_PATH_BATCH_DML_RECEIVER_H
#define EE_PATH_BATCH_DML_RECEIVER_H

#include "db_bitmap.h"
#include "ee_stmt_fusion.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    DestReceiverT destReceiver;
    DbMemCtxT *memCtx;     // allocate memory for qryResult
    HpTupleAddr *hpAddrs;  // get the tuple add in delete and replace exec
    union {
        // replace
        struct {
            uint32_t bitmapNum;
            DbBitmapT **bitmap;  // item<DbBitmapT *>
        };
        // delete
        struct {
            uint32_t size;  // vertexSize
            uint8_t **vertexBufs;
            uint32_t *vertexBufSizes;
        };
    };
} PathBatchDmlReceiverT;

Status InitPathBatchDmlReceiver(DbMemCtxT *memCtx, FusionModelQryStmtT *stmt);

#ifdef __cplusplus
}
#endif
#endif  // EE_PATH_BATCH_DML_RECEIVER_H
