/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: modify table implementation.
 * Author: GQL
 * Create: 2022-10-25
 */

#include "db_simd_hashset.h"
#include "ee_access_method.h"
#include "ee_plan_state.h"
#include "ee_plan_state_router.h"
#include "ee_gql_utils.h"

#ifdef __cplusplus
extern "C" {
#endif

static Status CreateGqlInsertState(DbMemCtxT *memCtx, GqlInsertStateT **gqlInsertState)
{
    DB_POINTER2(memCtx, gqlInsertState);
    GqlInsertStateT *tmpState = DbDynMemCtxAlloc(memCtx, sizeof(GqlInsertStateT));
    if (tmpState == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "GqlInsert node alloc");
        return GMERR_OUT_OF_MEMORY;
    }
    *tmpState = (GqlInsertStateT){0};
    *gqlInsertState = tmpState;
    return GMERR_OK;
}

static Status CheckLabelSanityForInsert(GqlInsertStateT *gqlInsertState)
{
    // Check pk exist
    DmVertexLabelT *vertexLabel = gqlInsertState->label;
    if (vertexLabel->metaVertexLabel->pkIndex == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "No PK of vertexLabel:%s", vertexLabel->metaCommon.metaName);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status ExecExplainGqlInsert(ExecExplainCtxT *explainCtx, PlanT *plan, uint32_t level)
{
    DB_POINTER3(plan, explainCtx, explainCtx->sb);
    StringBuilderT *sb = explainCtx->sb;
    GqlInsertT *insertInto = (void *)plan;
    DmVertexLabelT *label;
    Status ret = CataGetVertexLabelById(NULL, insertInto->labelId, &label);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmSbAppendIndent(sb, level);
    DmSbAppendWithFormat(sb, "->  Replace Into on Label(%s), tuple count = %d\n", label->metaCommon.metaName,
        insertInto->aaBuffer.slotNums);

    (void)CataReleaseVertexLabel(label);
    if (plan->leftTree != NULL) {
        ret = ExecExplainNode(explainCtx, plan->leftTree, level + 1);
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static void ExecInitGqlInsertHelper(GqlInsertStateT *gqlInsertState, GqlInsertT *replaceInto, EStateT *estate)
{
    gqlInsertState->planState = (PlanStateT){.tagType = replaceInto->plan.tagType, .estate = estate};
    gqlInsertState->batchBufHead = replaceInto->batchBufHead;
    gqlInsertState->dmlType = replaceInto->dmlType;
    gqlInsertState->isTrigsAllTrue = replaceInto->isTrigsAllTrue;
}

Status ExecInitGqlInsert(PlanT *plan, EStateT *estate, PlanStateT **state)
{
    GqlInsertT *replaceInto = (void *)plan;
    GqlInsertStateT *gqlInsertState;
    Status ret = CreateGqlInsertState(estate->memCtx, &gqlInsertState);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "create replaceInto");
        return ret;
    }

    // child node of replace node should be nil, check sanity
    if (plan->leftTree != NULL || plan->rightTree != NULL) {
        DB_LOG_ERROR(ret, "not child node");
        return GMERR_DATA_EXCEPTION;
    }

    // Initialize replaceInto state
    ExecInitGqlInsertHelper(gqlInsertState, replaceInto, estate);

    if ((ret = CataGetVertexLabelById(NULL, replaceInto->labelId, &gqlInsertState->label)) != GMERR_OK) {
        goto FREEMEM;
    }
    // Check if label is replaceable
    if ((ret = CheckLabelSanityForInsert(gqlInsertState)) != GMERR_OK) {
        goto RELEASE_LABEL;
    }
    // Open Heap and various index
    LabelBeginCfgT beginCfg = {.trxMemCtx = estate->memCtx,
        .eeMemCtx = estate->memCtx,
        .vertexLabel = gqlInsertState->label,
        .seRunCtx = estate->seInstance,
        .session = NULL};
    ret = HeapLabelBeginModify(beginCfg, &gqlInsertState->modifyDesc);
    if (ret != GMERR_OK) {
        goto RELEASE_LABEL;
    }

    if ((ret = IndexBeginScan(beginCfg, PK_INDEX_SEQ, &gqlInsertState->indexDesc)) != GMERR_OK) {
        goto END_MODIFY;
    }
    // Create an empty slot as a working medium for further processing
    if ((ret = NewAASlot(estate->memCtx, gqlInsertState->label->vertexDesc, NULL, &gqlInsertState->workSlot)) !=
        GMERR_OK) {
        goto ERROR_HANDLING;
    }

    SeTransSetDmlHint4BatchNum(gqlInsertState->planState.estate->seInstance, replaceInto->aaBuffer.slotNums);
    gqlInsertState->buffers = &replaceInto->aaBuffer;
    *state = (PlanStateT *)gqlInsertState;
    return GMERR_OK;

ERROR_HANDLING:
    IndexEndScan(gqlInsertState->indexDesc);
END_MODIFY:
    LabelEndModify(gqlInsertState->modifyDesc);
RELEASE_LABEL:
    (void)CataReleaseVertexLabel(gqlInsertState->label);
FREEMEM:
    DbDynMemCtxFree(estate->memCtx, gqlInsertState);
    return ret;
}

Status GqlInsertDoInsert(PlanStateT *state, AABufferT *vertexBuffers, AASlotT *slots)
{
    GqlInsertStateT *gqlInsertState = (void *)state;
    HeapLabelModifyDescT *heapDesc = (void *)gqlInsertState->modifyDesc;
    IndexScanDescT *indexDesc = gqlInsertState->indexDesc;
    DB_POINTER(indexDesc);

    uint32_t slotNums = vertexBuffers->slotNums;
    gqlInsertState->insertNum = slotNums;
    for (uint32_t i = 0; i < slotNums; i++) {
        slots[i].newTupleBuf.buf = (uint8_t *)vertexBuffers->buffers[i].str;
        slots[i].newTupleBuf.bufSize = vertexBuffers->buffers[i].len;
    }
    AAT tempAA = (AAT){};
    tempAA.prop.slots = &slots;
    Status ret = HeapLabelBatchInsert((LabelModifyDescT *)heapDesc, slotNums, slots);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = ExecProcModifyVertexIndice(state, &tempAA);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return ret;
}

static Status VectorExecuteInsert(PlanStateT *state, uint32_t batchSize, AABufferT *buff, AASlotT *slots, AAT *result)
{
    result->prop.originTupleAddr = (HpTupleAddr *)DbDynMemCtxAlloc(result->memCtx, sizeof(HpTupleAddr) * batchSize);
    if (SECUREC_UNLIKELY(result->prop.originTupleAddr == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "origin tuple addr alloc");
        return GMERR_OUT_OF_MEMORY;
    }
    result->prop.size = batchSize;
    return GqlInsertDoInsert(state, buff, slots);
}

Status ExecProcGqlInsert(PlanStateT *state, AAT *result)
{
    GqlInsertStateT *gqlInsertState = (void *)state;
    AABufferT *vertexBuffers = gqlInsertState->buffers;
    uint32_t batchSize = vertexBuffers->slotNums;
    DbMemCtxT *repMemCtx = gqlInsertState->planState.estate->memCtx;
    result->memCtx = gqlInsertState->planState.estate->memCtx;
    DbCreateList(&result->topo.info.bitmap, sizeof(DbBitmapT), result->memCtx);

    AASlotT *slots = DbDynMemCtxAlloc(repMemCtx, sizeof(AASlotT) * batchSize);
    if (SECUREC_UNLIKELY(slots == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "slots alloc");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(slots, sizeof(AASlotT) * batchSize, 0x00, sizeof(AASlotT) * batchSize);
    Status ret;
    ret = VectorExecuteInsert(state, batchSize, vertexBuffers, slots, result);
    DbDynMemCtxFree(repMemCtx, slots);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return GMERR_NO_DATA;
}

void ExecEndGqlInsert(PlanStateT *planState)
{
    // replace node should be leaf, no left or right child
    GqlInsertStateT *gqlInsertState = (void *)planState;

    IndexEndScan(gqlInsertState->indexDesc);
    LabelEndModify(gqlInsertState->modifyDesc);
    (void)CataReleaseVertexLabel(gqlInsertState->label);
    DbDynMemCtxFree(gqlInsertState->planState.estate->memCtx, gqlInsertState);
}

#ifdef __cplusplus
}
#endif
