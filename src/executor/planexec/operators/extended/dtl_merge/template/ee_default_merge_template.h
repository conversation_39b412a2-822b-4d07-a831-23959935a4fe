/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: merge template.
 * Author: GMDBv5 EE Team
 * Create: 2022-7-13
 */

#ifndef EE_DEFAULT_MERGE_TEMPLATE_H
#define EE_DEFAULT_MERGE_TEMPLATE_H

#include "ee_plan_state.h"

#ifdef __cplusplus
extern "C" {
#endif
#define DTL_RESERVED_COUNT_VALUE 1
typedef struct TemplateMergeMethod {
    Status (*mergeDeltaTable)(MergeStateT *mergeState, DeltaTableT *deltaTable, bool *done);
    Status (*mergeDeltaTupleOption)(MergeStateT *mergeState, AASlotT *deltaTuple);
    Status (*orgTupleExistCallBack)(MergeStateT *mergeState, AASlotT *orgTuple, AASlotT *deltaTuple);
    Status (*orgTupleUnExistCallBack)(
        MergeStateT *mergeState, AASlotT *orgTuple, AASlotT *deltaTuple, bool isOrgTupleMarkDeleted);
    Status (*finish)(MergeStateT *mergeState);
} TemplateMergeMethodT;

Status ExecDeltaMerge(MergeStateT *mergeState, AAT *aa);
Status ExecInitDeltaMerge(MergeT *merge, EStateT *estate, MergeStateT *mergeState);
void ExecEndDeltaMerge(PlanStateT *planState);

Status DeltaDefaultMergeDeltaTable(MergeStateT *mergeState, DeltaTableT *deltaTable, bool *done);
Status DeltaDefaultMergeDeltaTupleOption(MergeStateT *mergeState, AASlotT *deltaTuple);
Status DeltaDefaultMergeOrgTupleExistCallBack(MergeStateT *mergeState, AASlotT *orgTuple, AASlotT *deltaTuple);
Status DeltaDefaultMergeOrgTupleUnExistCallBack(
    MergeStateT *mergeState, AASlotT *orgTuple, AASlotT *deltaTuple, bool isOrgTupleMarkDeleted);
Status DeltaDefaultMergeFinish(MergeStateT *mergeState);

Status ResTemplateMergeDeltaTable(MergeStateT *mergeState, DeltaTableT *deltaTable, bool *done);
Status ResTemplateMergeDeltaTupleOption(MergeStateT *mergeState, AASlotT *deltaTuple);
Status ResTemplateOrgTupleExistCallBack(MergeStateT *mergeState, AASlotT *orgTuple, AASlotT *deltaTuple);
Status ResTemplateOrgTupleUnExistCallBack(
    MergeStateT *mergeState, AASlotT *orgTuple, AASlotT *deltaTuple, bool isOrgTupleMarkDeleted);
Status ResTemplateMergeFinish(MergeStateT *mergeState);

Status DeltaUpdatableMergeDeltaTable(MergeStateT *mergeState, DeltaTableT *deltaTable, bool *done);
Status DeltaUpdatableMergeDeltaTupleOption(MergeStateT *mergeState, AASlotT *deltaTuple);
Status DeltaUpdatableMergeOrgTupleExistCallBack(MergeStateT *mergeState, AASlotT *orgTuple, AASlotT *deltaTuple);
Status DeltaUpdatableMergeOrgTupleUnExistCallBack(
    MergeStateT *mergeState, AASlotT *orgTuple, AASlotT *deltaTuple, bool isOrgTupleMarkDeleted);
Status DeltaUpdatableMergeFinish(MergeStateT *mergeState);

Status DeltaTransientFinishMergeDeltaTable(MergeStateT *mergeState, DeltaTableT *deltaTable, bool *done);
Status DeltaTransientFinishMergeDeltaTupleOption(MergeStateT *mergeState, AASlotT *deltaTuple);
Status DeltaTransientFinishMergeOrgTupleExistCallBack(MergeStateT *mergeState, AASlotT *orgTuple, AASlotT *deltaTuple);
Status DeltaTransientFinishMergeOrgTupleUnExistCallBack(
    MergeStateT *mergeState, AASlotT *orgTuple, AASlotT *deltaTuple, bool isOrgTupleMarkDeleted);
Status DeltaTransientFinishMergeFinish(MergeStateT *mergeState);

Status DeltaResPubsubMergeDeltaTable(MergeStateT *mergeState, DeltaTableT *deltaTable, bool *done);

#ifdef __cplusplus
}
#endif

#endif  // EE_DEFAULT_MERGE_TEMPLATE_H
