/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: delta merge template implementation.
 * Author: GMDBv5 EE Team
 * Create: 2022-7-13
 */
#include "ee_delta_table_info_access_method.h"
#include "ee_temp_table.h"
#include "ee_iterate_merge_template.h"
#include "ee_access_method.h"
#include "ee_delta_merge_union_delete.h"
#include "ee_default_merge_template.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

static inline TemplateMergeMethodT *SetMergeMethods(TemplateMergeMethodT *func,
    Status (*mergeDeltaTable)(MergeStateT *mergeState, DeltaTableT *deltaTable, bool *done),
    Status (*mergeDeltaTupleOption)(MergeStateT *mergeState, AASlotT *deltaTuple),
    Status (*orgTupleExistCallBack)(MergeStateT *mergeState, AASlotT *orgTuple, AASlotT *deltaTuple),
    Status (*orgTupleUnExistCallBack)(
        MergeStateT *mergeState, AASlotT *orgTuple, AASlotT *deltaTuple, bool isOrgTupleMarkedDelete),
    Status (*finish)(MergeStateT *mergeState))
{
    func->mergeDeltaTable = mergeDeltaTable;
    func->mergeDeltaTupleOption = mergeDeltaTupleOption;
    func->orgTupleExistCallBack = orgTupleExistCallBack;
    func->orgTupleUnExistCallBack = orgTupleUnExistCallBack;
    func->finish = finish;
    return func;
}

static TemplateMergeMethodT *GetMergeMethods(NodeTagT tag, TemplateMergeMethodT *func)
{
    switch (tag) {
        case T_DELTA_DEFAULT_MERGE:
            return SetMergeMethods(func, DeltaDefaultMergeDeltaTable, DeltaDefaultMergeDeltaTupleOption,
                DeltaDefaultMergeOrgTupleExistCallBack, DeltaDefaultMergeOrgTupleUnExistCallBack,
                DeltaDefaultMergeFinish);
        case T_DELTA_RESOURCE_POOL_MERGE:
            return SetMergeMethods(func, ResTemplateMergeDeltaTable, ResTemplateMergeDeltaTupleOption,
                ResTemplateOrgTupleExistCallBack, ResTemplateOrgTupleUnExistCallBack, ResTemplateMergeFinish);
        case T_DELTA_RESOURCE_PUBSUB_MERGE:
            return SetMergeMethods(func, DeltaResPubsubMergeDeltaTable, ResTemplateMergeDeltaTupleOption,
                ResTemplateOrgTupleExistCallBack, ResTemplateOrgTupleUnExistCallBack, ResTemplateMergeFinish);
        case T_DELTA_UPDATABLE_MERGE:
            return SetMergeMethods(func, DeltaUpdatableMergeDeltaTable, DeltaUpdatableMergeDeltaTupleOption,
                DeltaUpdatableMergeOrgTupleExistCallBack, DeltaUpdatableMergeOrgTupleUnExistCallBack,
                DeltaUpdatableMergeFinish);
        case T_DELTA_TRANSIENT_FINISH_MERGE:
            return SetMergeMethods(func, DeltaTransientFinishMergeDeltaTable, DeltaTransientFinishMergeDeltaTupleOption,
                DeltaTransientFinishMergeOrgTupleExistCallBack, DeltaTransientFinishMergeOrgTupleUnExistCallBack,
                DeltaTransientFinishMergeFinish);
        default:
            return SetMergeMethods(func, NULL, NULL, NULL, NULL, NULL);
    }
}

inline static Status MergeDeltaTable(MergeStateT *mergeState, DeltaTableT *deltaTable, bool *done)
{
    TemplateMergeMethodT func;
    (void)GetMergeMethods(mergeState->planState.tagType, &func);
    return func.mergeDeltaTable(mergeState, deltaTable, done);
}

inline static Status MergeTemplateDeltaTupleOption(MergeStateT *mergeState, AASlotT *deltaTuple)
{
    TemplateMergeMethodT func;
    (void)GetMergeMethods(mergeState->planState.tagType, &func);
    return func.mergeDeltaTupleOption(mergeState, deltaTuple);
}

inline static Status OrgTupleExistCallBack(MergeStateT *mergeState, AASlotT *orgTuple, AASlotT *deltaTuple)
{
    TemplateMergeMethodT func;
    (void)GetMergeMethods(mergeState->planState.tagType, &func);
    return func.orgTupleExistCallBack(mergeState, orgTuple, deltaTuple);
}

inline static Status OrgTupleUnExistCallBack(
    MergeStateT *mergeState, AASlotT *orgTuple, AASlotT *deltaTuple, bool isOrgTupleMarkDeleted)
{
    TemplateMergeMethodT func;
    (void)GetMergeMethods(mergeState->planState.tagType, &func);
    return func.orgTupleUnExistCallBack(mergeState, orgTuple, deltaTuple, isOrgTupleMarkDeleted);
}

inline static Status DefaultFinish(MergeStateT *mergeState)
{
    TemplateMergeMethodT func;
    (void)GetMergeMethods(mergeState->planState.tagType, &func);
    return func.finish(mergeState);
}

Status MergeTemplateMergeDeltaTable(MergeStateT *mergeState, DeltaTableT *deltaTable, bool *done)
{
    DB_POINTER3(mergeState, deltaTable, done);
    return MergeDeltaTable(mergeState, deltaTable, done);
}

static Status MergeTemplateDeltaTupleExistCallBackImpl(
    PrimaryIndexLookUpDescT *orgTablePrimaryIndexScanDesc, AASlotT *deltaTuple, MergeStateT *mergeState)
{
    Status ret = PrimaryIndexLookUp(orgTablePrimaryIndexScanDesc, deltaTuple, mergeState->orgAASlot);
    bool isMarkDeleted = false;
    if (ret == GMERR_OK) {
        isMarkDeleted = DmVertexGetIsMarkDeletedSysPrope(mergeState->vertexLabel, mergeState->orgAASlot->dmVertex);
        if (isMarkDeleted) {
            ret = GMERR_NO_DATA;
        }
    }
    switch (ret) {
        // org tuple exist
        case GMERR_OK:
            if ((ret = OrgTupleExistCallBack(mergeState, mergeState->orgAASlot, &mergeState->deltaAASlot)) !=
                GMERR_OK) {
                return ret;
            }
            break;

        // org tuple non-existed
        case GMERR_NO_DATA:
            if ((ret = OrgTupleUnExistCallBack(
                     mergeState, mergeState->orgAASlot, &mergeState->deltaAASlot, isMarkDeleted)) != GMERR_OK) {
                return ret;
            }
            break;

        // get heap Blunder
        default:
            return ret;
    }
    return GMERR_OK;
}

Status MergeTemplateDeltaTupleExistCallBack(MergeStateT *mergeState, AASlotT *deltaTuple)
{
    DB_POINTER2(mergeState, deltaTuple);
    Status ret = GMERR_OK;
    ret = MergeTemplateDeltaTupleOption(mergeState, deltaTuple);
    if (ret != GMERR_OK) {
        return ret;
    }
    PrimaryIndexLookUpDescT *orgTablePrimaryIndexScanDesc = mergeState->orgTablePrimaryIndexLookUpDesc;
    // if encounter dtlReservedCount == 0 , skip this tuple
    DmValueT deltaCount = {0};
    if ((ret = AASlotGetDtlReservedCount(&mergeState->deltaAASlot, &deltaCount)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "get delta dtlReservedCount;labelName=%s", mergeState->vertexLabel->metaCommon.metaName);
        return ret;
    }

    if (mergeState->vertexLabel->commonInfo->datalogLabelInfo->inoutType == DM_DTL_INPUT_LABEL &&
        (deltaCount.value.intValue != DTL_RESERVED_COUNT_VALUE &&
            deltaCount.value.intValue != -DTL_RESERVED_COUNT_VALUE) &&
        !mergeState->deltaCountHasLog) {
        DB_LOG_WARN(GMERR_DATA_EXCEPTION, "Delta count of Tuple=%" PRId32 ", labelName=%s", deltaCount.value.intValue,
            mergeState->vertexLabel->metaCommon.metaName);
        mergeState->deltaCountHasLog = true;
    }
    if (deltaCount.value.intValue == 0) {
        return ret;
    }
    return MergeTemplateDeltaTupleExistCallBackImpl(orgTablePrimaryIndexScanDesc, deltaTuple, mergeState);
}

Status MergeTemplateFinish(MergeStateT *mergeState)
{
    DB_POINTER(mergeState);
    return DefaultFinish(mergeState);
}

bool MergeTemplateNeedCheckOrgNum(MergeStateT *mergeState)
{
    DB_POINTER(mergeState);
    return mergeState->vertexLabel->commonInfo->heapInfo.maxVertexNumCheck;
}

bool MergeTemplateNeedBuildOutputTable(MergeStateT *mergeState)
{
    DB_POINTER(mergeState);
    return true;
}

Status ExecDeltaMerge(MergeStateT *mergeState, AAT *aa)
{
    DB_POINTER(mergeState);
    return ExecIterateMerge(mergeState, aa);
}

Status ExecInitDeltaMerge(MergeT *merge, EStateT *estate, MergeStateT *mergeState)
{
    DB_POINTER3(merge, estate, mergeState);
    mergeState->planState.estate = estate;
    DbMemCtxT *memCtx = estate->memCtx;
    DbMemCtxT *trxMemCtx = estate->trxMemCtx;

    Status ret = ExecInitIterateMerge((MergeT *)merge, mergeState, estate);
    if (ret != GMERR_OK) {
        return ret;
    }

    // init PrimaryIndexScanDesc for Heap
    LabelBeginCfgT beginCfg = {.vertexLabel = mergeState->vertexLabel,
        .seRunCtx = estate->seInstance,
        .eeMemCtx = memCtx,
        .trxMemCtx = trxMemCtx};
    ret = PrimaryIndexBeginLookUp(beginCfg, &mergeState->orgTablePrimaryIndexLookUpDesc);
    if (ret != GMERR_OK) {
        return ret;
    }

    // Creating orgAASlot for saving tuple obtaining from access methods
    return NewAASlotWithVertexLabel(memCtx, mergeState->vertexLabel, &(mergeState->orgAASlot));
}

void ExecEndDeltaMerge(PlanStateT *planState)
{
    DB_POINTER(planState);
    MergeStateT *mergeState = (void *)planState;

    // unionDeletions is lazy init in MergeStateUnionDelete, end unionDeletions here
    if (mergeState->unionDeletions != NULL) {
        MergeStateEndUnionDeletions(mergeState->unionDeletions);
        mergeState->unionDeletions = NULL;
    }

    // end orgTablePrimaryIndexLookUpDesc
    if (mergeState->orgTablePrimaryIndexLookUpDesc != NULL) {
        PrimaryIndexEndLookUp(mergeState->orgTablePrimaryIndexLookUpDesc);
        mergeState->orgTablePrimaryIndexLookUpDesc = NULL;
    }

    // end baseIterateMerge
    ExecEndIterateMerge(mergeState);
}

#ifdef __cplusplus
}
#endif
