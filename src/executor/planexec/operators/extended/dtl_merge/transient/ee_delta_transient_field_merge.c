/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: delta transient field merge implementation.
 * Note1: memCtx is used in short process period , we will free all memory after finishing one request
 * Author: GMDBv5 EE Team
 * Create: 2022-9-5
 */

#include "ee_plan_state_router.h"
#include "ee_iterate_merge_template.h"
#include "ee_reserved_names.h"
#include "dm_data_math.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum TransOprType {
    TRST_SKIP,   // skip current delta tuple
    TRST_ADD,    // add delta tuple into orgTable
    TRST_DEL,    // delete org tuple from orgTable
    TRST_FLIP,   // flip the transient field in orgTable(0->1 or 1->0)
    TRST_UPD_C,  // update the count of orgTuple in orgTable
} TransOprTypeT;

typedef enum TransFieldType {
    TRST_ZERO_FIELD,  // transient field of delta tuple is zero
    TRST_ONE_FIELD,   // transient field of delta tuple is one
    TRST_EXCEPTION_FIELD,
} TransFieldTypeT;

/*
 * =============== implement iterator in volcano begin===================
 */
Status ExecExplainDeltaTransientFieldMerge(ExecExplainCtxT *explain, PlanT *plan, uint32_t level)
{
    DB_POINTER2(explain, plan);
    return ExecExplainMerge((void *)plan, explain->sb, "DeltaTransientFieldMerge", level);
}

Status NewDeltaTransientFieldMergeState(DbMemCtxT *memCtx, DeltaTransientFieldMergeStateT **mergeState)
{
    DB_POINTER2(memCtx, mergeState);
    // memory will be freed unitied , see Note1
    DeltaTransientFieldMergeStateT *state =
        (DeltaTransientFieldMergeStateT *)DbDynMemCtxAlloc(memCtx, sizeof(DeltaTransientFieldMergeStateT));
    if (state == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc DeltaTransientFieldMergeStateT");
        return GMERR_OUT_OF_MEMORY;
    }

    *state = (DeltaTransientFieldMergeStateT){0};
    state->mergeState.planState.tagType = T_DELTA_TRANSIENT_FIELD_MERGE;

    *mergeState = state;
    return GMERR_OK;
}

static Status ExecInitDeltaTransientFieldMergeTups(DeltaTransientFieldMergeStateT *transState, EStateT *estate)
{
    DbMemCtxT *memCtx = estate->memCtx;
    DmVertexLabelT *vertexLabel = transState->mergeState.vertexLabel;
    Status ret = NewAASlotWithVertexLabel(memCtx, vertexLabel, &transState->deltaTup);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = NewAASlotWithVertexLabel(memCtx, vertexLabel, &transState->opsDeltaTup);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = NewAASlotWithVertexLabel(memCtx, vertexLabel, &transState->orgTup);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = NewAASlotWithVertexLabel(memCtx, vertexLabel, &transState->opsOrgTup);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

static void ExecInitDeltaTransientFieldMergeCount(DeltaTransientFieldMergeStateT *transState)
{
    DmValueT zeroCount;
    zeroCount.type = DB_DATATYPE_INT32;
    zeroCount.value.intValue = 0;

    transState->zeroCount = zeroCount;

    transState->oneCount = zeroCount;
    transState->oneCount.value.intValue = 1;

    transState->negativeCount = zeroCount;
    transState->negativeCount.value.intValue = -1;
}

Status ExecInitDeltaTransientFieldMerge(PlanT *plan, EStateT *estate, PlanStateT **planState)
{
    DB_POINTER3(plan, estate, planState);
    DeltaTransientFieldMergeStateT *transState = NULL;
    DbMemCtxT *memCtx = estate->memCtx;
    DbMemCtxT *trxMemCtx = estate->trxMemCtx;

    // Create DeltaTransientFieldMergeStateT operator
    Status ret = NewDeltaTransientFieldMergeState(memCtx, &transState);
    if (ret != GMERR_OK) {
        return ret;
    }

    // init base iterate merge
    ret = ExecInitIterateMerge((void *)plan, &transState->mergeState, estate);
    if (ret != GMERR_OK) {
        goto ERROR;
    }

    LabelBeginCfgT beginCfg = {.vertexLabel = transState->mergeState.vertexLabel,
        .seRunCtx = estate->seInstance,
        .eeMemCtx = memCtx,
        .trxMemCtx = trxMemCtx,
        .session = estate->session};
    // init PrimaryIndexScanDesc for Heap
    ret = PrimaryIndexBeginLookUp(beginCfg, &transState->mergeState.orgTablePrimaryIndexLookUpDesc);
    if (ret != GMERR_OK) {
        goto ERROR;
    }

    // init TableModifyDesc for Heap
    ret = HeapLabelBeginModify(beginCfg, &transState->orgTableModifyDesc);
    if (ret != GMERR_OK) {
        goto ERROR;
    }

    // init LabelScanDescT for OrgTable
    ret = HeapLabelBeginScan(beginCfg, &transState->mergeState.orgTableScanDesc);
    if (ret != GMERR_OK) {
        goto ERROR;
    }

    // init deltaTup , opsDeltaTup , orgTup , opsOrgTup
    ret = ExecInitDeltaTransientFieldMergeTups(transState, estate);
    if (ret != GMERR_OK) {
        goto ERROR;
    }

    // init transFieldPropId and  count
    transState->transFieldPropId = transState->mergeState.vertexLabel->commonInfo->datalogLabelInfo->transientPropId;
    ExecInitDeltaTransientFieldMergeCount(transState);

    *planState = (PlanStateT *)(void *)transState;
    return GMERR_OK;

ERROR:
    ExecEndDeltaTransientFieldMerge((PlanStateT *)(void *)transState);
    return ret;
}

Status ExecProcDeltaTransientFieldMerge(PlanStateT *planState, AAT *aa)
{
    DB_POINTER(planState);
    return ExecIterateMerge((void *)planState, aa);
}

void ExecEndDeltaTransientFieldMerge(PlanStateT *planState)
{
    DB_POINTER(planState);
    DeltaTransientFieldMergeStateT *transState = (void *)planState;

    // end orgTableScanDesc
    if (transState->mergeState.orgTableScanDesc != NULL) {
        LabelEndScan(transState->mergeState.orgTableScanDesc);
        transState->mergeState.orgTableScanDesc = NULL;
    }

    // end orgTableModifyDesc
    if (transState->orgTableModifyDesc != NULL) {
        LabelEndModify(transState->orgTableModifyDesc);
        transState->orgTableModifyDesc = NULL;
    }

    // end orgTablePrimaryIndexLookUpDesc
    if (transState->mergeState.orgTablePrimaryIndexLookUpDesc != NULL) {
        PrimaryIndexEndLookUp(transState->mergeState.orgTablePrimaryIndexLookUpDesc);
        transState->mergeState.orgTablePrimaryIndexLookUpDesc = NULL;
    }

    ExecEndIterateMerge((MergeStateT *)(void *)transState);
}

/*
 * =============== implement iterator in volcano end===================
 */

/*
 * =============== implement template iterate merge begin=======================
 */
Status DeltaTransientFieldMergeDeltaTable(MergeStateT *mergeState, DeltaTableT *deltaTable, bool *done)
{
    DB_POINTER3(mergeState, deltaTable, done);
    return TempTableMerge(deltaTable, done);
}

static Status TrstFieldMergeSetAndVerifyTransField(
    DeltaTransientFieldMergeStateT *transState, TransFieldTypeT *fieldType)
{
    char *labelName = transState->mergeState.vertexLabel->metaCommon.metaName;
    uint32_t transPropId = transState->transFieldPropId;

    DmValueT *transField = &transState->transField;
    Status ret = AASlotGetPrope(transState->deltaTup, transPropId, transField);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get transField;label=%s, transPropId=%" PRIu32, labelName, transPropId);
        return ret;
    }

    if (DmValueIsEqual(transField, &transState->zeroCount)) {
        *fieldType = TRST_ZERO_FIELD;
    } else if (DmValueIsEqual(transField, &transState->oneCount)) {
        *fieldType = TRST_ONE_FIELD;
    } else {
        *fieldType = TRST_EXCEPTION_FIELD;
        DB_LOG_WARN(GMERR_DATA_EXCEPTION, "transField not 1 or 0;label=%s, propId=%" PRIu32 ",transField=%" PRIi32,
            labelName, transPropId, transField->value.intValue);
    }
    return GMERR_OK;
}

static Status TrstFieldMergeSetOpsDeltaTup(DeltaTransientFieldMergeStateT *transState)
{
    char *labelName = transState->mergeState.vertexLabel->metaCommon.metaName;
    uint32_t transPropId = transState->transFieldPropId;

    DmValueT opsTransField = {0};
    Status ret = DmValueSub(&transState->oneCount, &transState->transField, &opsTransField);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = AASlotCopy(transState->opsDeltaTup, transState->deltaTup);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "AASlot copy from deltaTup to opsDeltaTup;label=%s, transPropId=%" PRIu32, labelName, transPropId);
        return ret;
    }

    ret = AASlotSetPrope(transState->opsDeltaTup, transPropId, opsTransField);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set opsTransField;label=%s, transPropId=%" PRIu32 ", opsTransField=%" PRIi32, labelName,
            transPropId, opsTransField.value.intValue);
        return ret;
    }
    return ret;
}

static Status TrstFieldMergeSetAndSearchOrgTup(
    DeltaTransientFieldMergeStateT *transState, bool *orgTupExist, bool *opsOrgTupExist)
{
    char *labelName = transState->mergeState.vertexLabel->metaCommon.metaName;
    bool tup0PkSame = false;
    bool tup1PkSame = false;

    PrimaryIndexLookUpDescT *orgPkLookUpDesc = transState->mergeState.orgTablePrimaryIndexLookUpDesc;
    Status ret = PrimaryIndexLookUp(orgPkLookUpDesc, transState->deltaTup, transState->orgTup);
    if (ret == GMERR_OK) {
        ret = AASlotEqual(transState->deltaTup, transState->orgTup, false, orgTupExist);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = AASlotEqual(transState->opsDeltaTup, transState->orgTup, false, opsOrgTupExist);
        if (ret != GMERR_OK) {
            return ret;
        }
        tup0PkSame = true;
    } else if (ret != GMERR_NO_DATA) {
        return ret;
    }

    ret = PrimaryIndexLookUp(orgPkLookUpDesc, transState->opsDeltaTup, transState->opsOrgTup);
    if (ret == GMERR_OK) {
        ret = AASlotEqual(transState->deltaTup, transState->opsOrgTup, false, orgTupExist);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = AASlotEqual(transState->opsDeltaTup, transState->opsOrgTup, false, opsOrgTupExist);
        if (ret != GMERR_OK) {
            return ret;
        }
        tup1PkSame = true;
    } else if (ret != GMERR_NO_DATA) {
        return ret;
    }
    if ((tup1PkSame || tup0PkSame) && !(*orgTupExist || *opsOrgTupExist)) {
        DB_LOG_ERROR(GMERR_PRIMARY_KEY_VIOLATION, "Tuple dropped as PK violation when transient field merge;label=%s",
            labelName);
        return GMERR_PRIMARY_KEY_VIOLATION;
    }

    if (*orgTupExist && *opsOrgTupExist) {
        return GMERR_DATA_EXCEPTION;
    }

    if (*opsOrgTupExist) {
        return AASlotCopy(transState->orgTup, transState->opsOrgTup);
    }
    return GMERR_OK;
}

/*
 * 1. search OrgTable with primary key
 * 2. if orgTuple unExist, return ADD
 * 3. if orgTuple Exist:
 *      3.1 calculate totalCount
 *      3.2 totalCount == 0, return DEL
 *      3.3 totalCount != 0, return UPD_C
 */
static Status TrstFieldMergeProcessFieldZero(DeltaTransientFieldMergeStateT *transState, TransOprTypeT *oprType)
{
    char *labelName = transState->mergeState.vertexLabel->metaCommon.metaName;
    bool orgTupExist = false;
    bool opsOrgTupExist = false;
    Status ret = TrstFieldMergeSetAndSearchOrgTup(transState, &orgTupExist, &opsOrgTupExist);
    if (ret != GMERR_OK) {
        return ret;
    }

    // check if orgTuple exist
    if (!orgTupExist && !opsOrgTupExist) {
        *oprType = TRST_ADD;
        return GMERR_OK;
    }

    DmValueT totalCount = {0};
    ret = AASlotAddCountField(transState->orgTup, transState->deltaTup, &totalCount);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "add cnt when field==0;label=%s", labelName);
        return ret;
    }
    if (DmValueIsEqual(&totalCount, &transState->zeroCount)) {
        *oprType = TRST_DEL;
        return GMERR_OK;
    }
    *oprType = TRST_UPD_C;
    return GMERR_OK;
}

/*
 * 1. search deltaTable with primary key
 *      1.1 if other delta tuple exist, return skip
 * 2. search OrgTable with primary key
 *      2.1 if orgTuple unExist, skip
 *      2.2 if orgTuple exist, return FLIP
 */
static Status TrstFieldMergeProcessFieldOne(DeltaTransientFieldMergeStateT *transState, TransOprTypeT *oprType)
{
    char *labelName = transState->mergeState.vertexLabel->metaCommon.metaName;
    bool hasAnother = false;
    Status ret =
        DeltaLabelHasTuple(transState->mergeState.inputDeltaTableScanDesc, transState->opsDeltaTup, &hasAnother);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "search tuple when field==1;label=%s", labelName);
        return ret;
    }
    // skip if tup.transField == 1 when both 1 and 0 in the delta table
    if (hasAnother) {
        *oprType = TRST_SKIP;
        return GMERR_OK;
    }

    // check if orgTuple exist
    bool orgTupExist = false;
    bool opsOrgTupExist = false;
    ret = TrstFieldMergeSetAndSearchOrgTup(transState, &orgTupExist, &opsOrgTupExist);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (!orgTupExist && !opsOrgTupExist) {
        *oprType = TRST_SKIP;
        DB_LOG_WARN(GMERR_DATA_EXCEPTION, "transField == 1;labelName=%s", labelName);
    } else {
        *oprType = TRST_FLIP;
    }
    return GMERR_OK;
}

/*
 * 1. check the transField of deltaTuple, if transField != 0 or != 1, skip
 * 2. construct opposite delta tuple(0->1 or 1->0)
 * 3. process with curresponding callback
 */
static Status TrstFieldMergeCheckField(DeltaTransientFieldMergeStateT *transState, TransOprTypeT *oprType)
{
    char *labelName = transState->mergeState.vertexLabel->metaCommon.metaName;
    TransFieldTypeT fieldType = TRST_EXCEPTION_FIELD;

    // 1. check if field is 0 or 1
    Status ret = TrstFieldMergeSetAndVerifyTransField(transState, &fieldType);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "verify transField;label=%s", labelName);
        return ret;
    }

    if (fieldType == TRST_EXCEPTION_FIELD) {
        *oprType = TRST_SKIP;
        return GMERR_OK;
    }

    // 2. Set opposite delta tuple
    ret = TrstFieldMergeSetOpsDeltaTup(transState);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set opposite tuple;label=%s", labelName);
        return ret;
    }

    // 3. if field == 0, insert, delete or update count of orgTup
    if (fieldType == TRST_ZERO_FIELD) {
        ret = TrstFieldMergeProcessFieldZero(transState, oprType);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "process zero field;label=%s", labelName);
            return ret;
        }
    }

    // 4. if field == 1, flip transField of orgTup
    if (fieldType == TRST_ONE_FIELD) {
        ret = TrstFieldMergeProcessFieldOne(transState, oprType);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "process one field;label=%s", labelName);
            return ret;
        }
    }

    return GMERR_OK;
}

static Status TrstFieldMergeInsertTuple(DeltaTransientFieldMergeStateT *transState, AASlotT *tuple)
{
    LabelModifyDescT *outputDeltaTableModifyDesc = transState->mergeState.outputDeltaTableModifyDesc;
    LabelModifyDescT *orgTableModifyDesc = transState->orgTableModifyDesc;
    char *labelName = transState->mergeState.vertexLabel->metaCommon.metaName;

    Status ret = LabelInsert(outputDeltaTableModifyDesc, tuple);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "insert deltaTable;label=%s", labelName);
        return ret;
    }

    ret = LabelInsert(orgTableModifyDesc, tuple);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "insert orgTable;label=%s", labelName);
        return ret;
    }
    return ret;
}

static Status TrstFieldMergeDeleteTuple(DeltaTransientFieldMergeStateT *transState, AASlotT *tuple)
{
    LabelModifyDescT *outputDeltaTableModifyDesc = transState->mergeState.outputDeltaTableModifyDesc;
    LabelModifyDescT *orgTableModifyDesc = transState->orgTableModifyDesc;
    char *labelName = transState->mergeState.vertexLabel->metaCommon.metaName;

    DmValueT orgCount = {0};
    // Get count from orgTup
    Status ret = AASlotGetDtlReservedCount(tuple, &orgCount);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get count;label=%s", labelName);
        return ret;
    }

    ret = DmValueMulti(&orgCount, &transState->negativeCount, &orgCount);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "negative count;label=%s", labelName);
        return ret;
    }
    ret = AASlotSetDtlReservedCount(tuple, orgCount);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set count;label=%s", labelName);
        return ret;
    }

    ret = LabelInsert(outputDeltaTableModifyDesc, tuple);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "insert deltaTable;label=%s", labelName);
        return ret;
    }

    ret = LabelDelete(orgTableModifyDesc, tuple);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "delete orgTable;label=%s", labelName);
        return ret;
    }
    return ret;
}

static Status TrstFieldMergeFlipTuple(DeltaTransientFieldMergeStateT *transState)
{
    char *labelName = transState->mergeState.vertexLabel->metaCommon.metaName;
    DmValueT orgTransField = {0};
    DmValueT opsTransField = {0};
    Status ret = AASlotCopy(transState->opsOrgTup, transState->orgTup);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "copy orgTup;label=%s", labelName);
        return ret;
    }

    ret = AASlotGetPrope(transState->opsOrgTup, transState->transFieldPropId, &orgTransField);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set trstField;label=%s", labelName);
        return ret;
    }

    ret = DmValueSub(&transState->oneCount, &orgTransField, &opsTransField);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "sub trstField;label=%s", labelName);
        return ret;
    }

    ret = AASlotSetPrope(transState->opsOrgTup, transState->transFieldPropId, opsTransField);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set trstField;label=%s", labelName);
        return ret;
    }

    ret = TrstFieldMergeDeleteTuple(transState, transState->orgTup);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "delete oldTuple;label=%s", labelName);
        return ret;
    }

    ret = TrstFieldMergeInsertTuple(transState, transState->opsOrgTup);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "insert newTuple;label=%s", labelName);
        return ret;
    }
    return ret;
}

static Status TrstFieldMergeUpdateTupleCount(DeltaTransientFieldMergeStateT *transState)
{
    LabelModifyDescT *orgTableModifyDesc = transState->orgTableModifyDesc;
    char *labelName = transState->mergeState.vertexLabel->metaCommon.metaName;

    DmValueT totalCount = {0};
    // Get count from orgTup
    Status ret = AASlotAddCountField(transState->orgTup, transState->deltaTup, &totalCount);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "add count;label=%s", labelName);
        return ret;
    }

    ret = AASlotSetDtlReservedCount(transState->orgTup, totalCount);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set count;label=%s", labelName);
        return ret;
    }

    ret = LabelUpdate(orgTableModifyDesc, transState->orgTup);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "update orgTable;label=%s", labelName);
        return ret;
    }
    return ret;
}

/*
 * if transient field != 0 && != 1, skip
 * if transient field == 0:
 *    1. insert tuple if orgTuple unExist
 *    2. delete tuple if orgTuple Exist and totalCount == 0
 *    3. update count if orgTuple Exist and totalCount != 0
 * if transient field == 1:
 *    1. flip the transient field in orgTable (0->1 or 1->0)
 *    2. skip if orgTuple UnExist
 */
Status DeltaTransientFieldMergeDeltaTupleExistCallBack(MergeStateT *mergeState, AASlotT *deltaTuple)
{
    DB_POINTER2(mergeState, deltaTuple);
    DeltaTransientFieldMergeStateT *transState = (void *)mergeState;
    TransOprTypeT oprType = TRST_SKIP;
    // 1. check transient field and judge operType
    transState->deltaTup = deltaTuple;
    Status ret = TrstFieldMergeCheckField(transState, &oprType);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 2. process transient by event type
    switch (oprType) {
        case TRST_SKIP:
            break;
        case TRST_ADD:
            // transient field == 0, orgTuple unExist, and deltaCount > 0
            ret = TrstFieldMergeInsertTuple(transState, transState->deltaTup);
            break;
        case TRST_DEL:
            // transient field == 0, orgTuple exist, and totalCount == 0
            ret = TrstFieldMergeDeleteTuple(transState, transState->orgTup);
            break;
        case TRST_UPD_C:
            // transient field == 0, orgTuple exist, and totalCount != 0
            ret = TrstFieldMergeUpdateTupleCount(transState);
            break;
        case TRST_FLIP:
            // transient field == 1, orgTuple exist
            ret = TrstFieldMergeFlipTuple(transState);
            break;
        default:
            break;
    }
    return ret;
}

Status DeltaTransientFieldMergeFinish(MergeStateT *mergeState)
{
    return GMERR_OK;
}

bool DeltaTransientFieldMergeNeedCheckOrgNum(MergeStateT *mergeState)
{
    DB_POINTER(mergeState);
    return mergeState->vertexLabel->commonInfo->heapInfo.maxVertexNumCheck;
}

bool DeltaTransientFieldMergeNeedBuildOutputTable(MergeStateT *mergeState)
{
    DB_POINTER(mergeState);
    return true;
}

#define DMVERTEX_POINTER_SIZE 8
Status ExecEstimateTransientFieldMerge(EstimateCtxT *estimateCtx, PlanT *planTree, uint32_t *estimatedValue)
{
    DB_POINTER3(estimateCtx, planTree, estimatedValue);
    Status ret = GMERR_OK;
    DeltaTransientFieldMergeT *mergeOp = (DeltaTransientFieldMergeT *)planTree;
    uint32_t labelId = mergeOp->deltaMerge.label->metaCommon.metaId;
    UpgradeTblT *tblInfo = DbOamapLookup(estimateCtx->tblMap, labelId, &labelId, NULL);
    if (tblInfo == NULL) {
        ret = InsertNewTbl2Map(estimateCtx, mergeOp->deltaMerge.label, &tblInfo);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    uint32_t dmVertexSize = 0;
    ret = EstimateDmVertexSize(mergeOp->deltaMerge.label, &dmVertexSize);
    if (ret != GMERR_OK) {
        return ret;
    }

    // (dmVertexSize + 8) * deltaCnt
    dmVertexSize += DMVERTEX_POINTER_SIZE;
    uint32_t deltaMemSize = 0;
    ret = DmValueMultiUint32(dmVertexSize, tblInfo->deltaCnt, &deltaMemSize);
    if (ret != GMERR_OK) {
        return ret;
    }

    *estimatedValue += deltaMemSize;
    return GMERR_OK;
}

/*
 * =============== implement template iterate merge begin=======================
 */

#ifdef __cplusplus
}
#endif
