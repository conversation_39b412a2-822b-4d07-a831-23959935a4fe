/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Define all interfaces of pubsub helper
 * Author: GMDBv5 EE Team
 * Create: 2023-6-27
 */

#ifndef EE_DELTA_PUBSUB_HELPER_H
#define EE_DELTA_PUBSUB_HELPER_H

#include "db_mem_context.h"
#include "ee_plan_state.h"
#include "dm_meta_subscription.h"
#include "ee_sub_channels.h"
#include "ee_batch_merge_template.h"

#ifdef __cplusplus
extern "C" {
#endif

// The max number of messages to be sent at one time for datalog pubsub. The value should be chosen carefully
// in case there are very big bytes that could exceed the limit (normally 2MB) of the FixBuffer
#define DTL_SUBS_BATCH_MAX 28

// pubsub max msg size 2MB, 2*1024*1024
#define PUBSUB_MAX_MSG_SIZE 2097152

#define UPDATE_TWO_TUPLE 2

/*
 * @brief convert vertex to buf in tbmMerge
 * @param[in] tbmMerge : tbm itself
 * @param[in] vertex : to be converted
 * @param[in] memCtx : memory context to be used
 * @ return Success or ErrorCode
 */
Status SerializeBatchVertex2RowData(DeltaPubsubMergeStateT *pubsubMergeState, uint32_t batchSize,
    DmSubsEventE eventType, QrySubsRowDataSetT *rowDataSet, bool isRollback);

uint32_t PubsubGetBatchSize(
    BatchTemplateMergeStateT *batchMergeState, uint32_t restMsgNum, uint32_t msgCount, BatchMergeTypeE eventType);

void DtlReverseRowsDataBuf(QrySubsRowDataSetT *rowDataSet);

#ifdef __cplusplus
}
#endif
#endif  // EE_DELTA_TBM_HELPER_H
