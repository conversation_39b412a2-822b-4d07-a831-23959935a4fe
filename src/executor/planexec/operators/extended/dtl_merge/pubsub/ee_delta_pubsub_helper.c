/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Define pubsub helper impl.
 * Note1: memCtx is used in short process period , we will free all memory after finishing one request
 * Author: GMDBv5 EE Team
 * Create: 2023-06-27
 */

#include "ee_delta_pubsub_helper.h"
#include "dm_meta_prop_strudefs.h"
#include "ee_iterate_merge_template.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

static Status SerializeVertex2RowData(
    DbMemCtxT *planMemCtx, TempTableT *tempTable, uint32_t idx, uint32_t *bufSize, uint8_t **buf, bool isRollback)
{
    Status ret = GMERR_OK;
    char *info = isRollback ? "rollback" : "forward";
    DmVertexT *dmVertex = TempTableGetElement(tempTable, idx);
    if (dmVertex == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Vertex null when %s, tblName:%s, idx:%" PRIu32, info,
            tempTable->vertexLabelName, idx);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    if (isRollback) {
        ret = ExecNegateVertexRsvdCount(dmVertex);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(
                ret, "negate rsvdCount when rollback, table:%s idx:%" PRIu32, tempTable->vertexLabelName, idx);
            return ret;
        }
    }

    ret = DmSerializeVertex(dmVertex, buf, bufSize);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "seri vertex %s, tblName:%s, idx:%" PRIu32, info, tempTable->vertexLabelName, idx);
        return ret;
    }

    if (!isRollback) {
        /*  MemCtx purpose: when serializing rollback dmVertex, we reuse the forward dmVertex. Notice that calling
                DmSerializeVertex for the first time it will malloc a buf(i.e. dmVertex->vertexSeriBuf), and calling it
           again it will free the previous buf on that dmVertex. That means one dmVertex relates to only one buf. So we
           should create a buf by ourself when serializing forward tuples. Lifecycle: the forwardBuf will be freed when
           this plan finishes execution. Release method: ExecEntryFinish->...->ResetExePlanChildMemCtx Concurrency: not
           involved
        */
        uint8_t *forwardBuf = (uint8_t *)DbDynMemCtxAlloc(planMemCtx, *bufSize);
        if (forwardBuf == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc vertex seriBuf, size:%" PRIu32, *bufSize);
            return GMERR_OUT_OF_MEMORY;
        }
        errno_t err = memcpy_s(forwardBuf, *bufSize, *buf, *bufSize);
        if (err != EOK) {
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "memcpy buf to forwardBuf, bufSize:%" PRIu32, *bufSize);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        *buf = forwardBuf;
    }

    if (isRollback) {
        ret = ExecNegateVertexRsvdCount(dmVertex);  // restore count after serializing rollback tuples
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(
                ret, "restore rsvdCount when rollback table:%s, idx:%" PRIu32, tempTable->vertexLabelName, idx);
            return ret;
        }
    }
    return GMERR_OK;
}

/*
    Example: rollback, update event, batchSize=10
    0 0 -1 <-- curIdx=0
    0 1 +1
    1 1 -1
    1 2 +1
    ...
    8 8 -1
    8 9 +1
    9 9 -1
    9 10 +1 <-- tupleIdx=19
*/
static inline void UpdateIdxForRollback(uint32_t batchSize, DmSubsEventE eventType, bool isRollback, int32_t *tupleIdx)
{
    if (!isRollback) {
        return;
    }
    *tupleIdx += (int32_t)batchSize - 1;
    if (eventType == DM_SUBS_EVENT_UPDATE) {
        *tupleIdx += (int32_t)batchSize;  // for update event, there are 2*batchSize tuples
    }
}

Status SerializeBatchVertex2RowData(DeltaPubsubMergeStateT *pubsubMergeState, uint32_t batchSize,
    DmSubsEventE eventType, QrySubsRowDataSetT *rowDataSet, bool isRollback)
{
    Status ret = GMERR_OK;
    char *info = isRollback ? "rollback" : "forward";
    rowDataSet->rowsDataNum = (uint16_t)batchSize;
    TableInfoT *tableInfo = pubsubMergeState->batchMergeState.mergeState.tableInfo;
    TempTableT *tempTable = tableInfo->table;
    int32_t tupleIdx = pubsubMergeState->batchMergeState.curIdx;
    UpdateIdxForRollback(batchSize, eventType, isRollback, &tupleIdx);
    DbMemCtxT *planMemCtx = pubsubMergeState->batchMergeState.mergeState.planState.estate->memCtx;

    if (TempTableIsEmpty(tempTable)) {
        DB_LOG_INFO("TempTable empty %s;tableName=%s,dataType=%" PRId32 ",eventType=%" PRId32, info,
            tempTable->vertexLabelName, (int32_t)tableInfo->writeDataType, (int32_t)eventType);
        return GMERR_OK;
    }

    for (uint32_t batchIdx = 0; batchIdx < batchSize; batchIdx++) {
        // serialize update tuples
        if (eventType == DM_SUBS_EVENT_UPDATE) {
            ret = SerializeVertex2RowData(planMemCtx, tempTable, (uint32_t)tupleIdx,
                &rowDataSet->rowsData[batchIdx].oldTupleBuf.bufSize, &rowDataSet->rowsData[batchIdx].oldTupleBuf.buf,
                isRollback);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "seri old vertex %s;batchIdx=%" PRIu32, info, batchIdx);
                return ret;
            }
            isRollback ? tupleIdx-- : tupleIdx++;
        }

        // serialize insert/delete tuples
        ret = SerializeVertex2RowData(planMemCtx, tempTable, (uint32_t)tupleIdx,
            &rowDataSet->rowsData[batchIdx].newTupleBuf.bufSize, &rowDataSet->rowsData[batchIdx].newTupleBuf.buf,
            isRollback);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "seri new vertex %s;batchIdx=%" PRIu32, info, batchIdx);
            return ret;
        }
        isRollback ? tupleIdx-- : tupleIdx++;
    }
    return ret;
}

static bool IsFinishRead(int32_t tupleIdx, uint32_t msgCount, bool isUpdate, bool isRollback)
{
    if (isRollback) {
        return tupleIdx < 0;
    }
    return isUpdate ? (tupleIdx >= (int32_t)(msgCount * UPDATE_TWO_TUPLE)) : (tupleIdx >= (int32_t)msgCount);
}

static int32_t GetTupleIdx(uint32_t msgCount, uint32_t restMsgNum, bool isUpdate, bool isRollback)
{
    if (isRollback) {
        return isUpdate ? (int32_t)(restMsgNum * UPDATE_TWO_TUPLE - 1) : (int32_t)(restMsgNum - 1);
    }
    return isUpdate ? (int32_t)((msgCount - restMsgNum) * UPDATE_TWO_TUPLE) : (int32_t)(msgCount - restMsgNum);
}

static uint32_t DmVertexGetPubsubBufferLen(TempTableT *tempTable, uint32_t index)
{
    DmVertexT *dmVertex = TempTableGetElement(tempTable, index);
    uint32_t dmVertexSize = 0;
    Status ret = DmVertexGetSeriBufLength(dmVertex, &dmVertexSize);
    if (ret != GMERR_OK) {
        return 0;
    }
    return FixBufGetRawDataLen(dmVertexSize);
}

uint32_t PubsubGetBatchSize(
    BatchTemplateMergeStateT *batchMergeState, uint32_t restMsgNum, uint32_t msgCount, BatchMergeTypeE eventType)
{
    uint32_t batchSize = 0;
    uint32_t totalBufferLen = 0;
    TempTableT *tempTable = batchMergeState->mergeState.tableInfo->table;
    bool isRollback = (batchMergeState->mergeState.planState.tagType == T_DELTA_PUBSUB_ROLLBACK_MERGE);
    bool isUpdate = (eventType == BATCH_UPDATE);
    int32_t tupleIdx = GetTupleIdx(msgCount, restMsgNum, isUpdate, isRollback);

    while (!IsFinishRead(tupleIdx, msgCount, isUpdate, isRollback)) {
        if (batchSize >= SUBS_BATCH_BATCH_MAX) {
            return SUBS_BATCH_BATCH_MAX;
        }

        if (isUpdate) {
            totalBufferLen += DmVertexGetPubsubBufferLen(tempTable, (uint32_t)tupleIdx);
            // can not use >= here, it will lost cur msg
            if (totalBufferLen > PUBSUB_MAX_MSG_SIZE) {
                return DB_MIN(batchSize, SUBS_BATCH_BATCH_MAX);
            }
            isRollback ? tupleIdx-- : tupleIdx++;
        }

        totalBufferLen += DmVertexGetPubsubBufferLen(tempTable, (uint32_t)tupleIdx);
        if (totalBufferLen > PUBSUB_MAX_MSG_SIZE) {
            return DB_MIN(batchSize, SUBS_BATCH_BATCH_MAX);
        }
        isRollback ? tupleIdx-- : tupleIdx++;
        // update only add once
        batchSize++;
    }
    return DB_MIN(batchSize, SUBS_BATCH_BATCH_MAX);
}

void DtlReverseRowsDataBuf(QrySubsRowDataSetT *rowDataSet)
{
    uint16_t size = rowDataSet->rowsDataNum;
    uint8_t *tmpBuf = NULL;
    uint32_t tmpBufSize = 0;
    for (uint32_t i = 0; i < DB_HALF_OF(rowDataSet->rowsDataNum); i++) {
        // swap
        tmpBuf = rowDataSet->rowsData[i].newTupleBuf.buf;
        tmpBufSize = rowDataSet->rowsData[i].newTupleBuf.bufSize;

        rowDataSet->rowsData[i].newTupleBuf.buf = rowDataSet->rowsData[size - 1u - i].newTupleBuf.buf;
        rowDataSet->rowsData[i].newTupleBuf.bufSize = rowDataSet->rowsData[size - 1u - i].newTupleBuf.bufSize;

        rowDataSet->rowsData[size - 1u - i].newTupleBuf.buf = tmpBuf;
        rowDataSet->rowsData[size - 1u - i].newTupleBuf.bufSize = tmpBufSize;
    }
}

#ifdef __cplusplus
}
#endif
