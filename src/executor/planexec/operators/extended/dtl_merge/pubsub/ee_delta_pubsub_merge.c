/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: delta pubsub merge implementation.
 * Note1: memCtx is used in short process period , we will free all memory after finishing one request
 * Author: GMDBv5 EE Team
 * Create: 2023-03-30
 */

#include "ee_access_method.h"
#include "ee_plan_state.h"
#include "ee_plan_state_router.h"
#include "template/ee_batch_merge_template.h"
#include "template/ee_iterate_merge_template.h"
#include "ee_delta_table_info_access_method.h"
#include "ee_temp_table.h"
#include "ee_sync_invoke.h"
#include "ee_delta_pubsub_helper.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum PubsubRsp { TIMEOUT, FAILED, SUCCESS } PubsubRspE;

typedef struct DeltaPubsubMethod {
    Status (*MergeDeltaTable)(DeltaPubsubMergeStateT *mergeState, DeltaTableT *deltaTable, bool *done);
} DeltaPubsubMethodT;

/*
 * ====================================== inserted and updated methods begin ============================
 */

Status DeltaPubsubMergeDeltaTableInsertable(DeltaPubsubMergeStateT *mergeState, DeltaTableT *deltaTable, bool *done)
{
    *done = true;
    Status ret = GMERR_OK;

    // this is insert and update's turn
    TempTableSetInsertTupleList(deltaTable);
    uint32_t tupleCnt = TempTableCount((TempTableT *)(void *)deltaTable);
    if (tupleCnt == 0) {
        return ret;
    }

    // set updIdx to insert's length for insert and update
    mergeState->batchMergeState.updIdx = deltaTable->updateStartIdx;

    // record for rollback
    ExecuteInfoT *executeInfo = TableInfoGetExecuteInfo(mergeState->batchMergeState.mergeState.tableInfo);
    ExecuteInfoSetUpdIdx(executeInfo, deltaTable->updateStartIdx);
    return ret;
}

inline static bool TempTableIsUpdatable(const TableInfoT *tableInfo)
{
    return tableInfo->labelType == DM_DTL_UPDATE;
}

/*
 * ====================================== inserted and updated methods end ============================
 */

/*
 * ====================================== deleted methods begin ====================================
 */
Status DeltaPubsubMergeDeltaTableDeletable(DeltaPubsubMergeStateT *mergeState, DeltaTableT *deltaTable, bool *done)
{
    DB_POINTER3(mergeState, deltaTable, done);

    Status ret = GMERR_OK;
    if (TempTableIsUpdatable(mergeState->batchMergeState.mergeState.tableInfo)) {
        // separate DeltaTable to two parts : [deleted tuples] , [inserted tuples and updated tuples]
        ret = TempTableUpdatableClassifyMerge(deltaTable, false, &mergeState->batchMergeState.updIdx, done);
    } else {
        // separate DeltaTable to two parts : [deleted tuples] , [inserted tuples]
        ret = TempTableClassifyMerge(deltaTable, done);
    }
    if (ret != GMERR_OK) {
        return ret;
    }

    // TempTableUpdatableClassifyMerge does not finish
    if (!(*done)) {
        return ret;
    }

    // this is delete's turn
    TempTableSetDeleteTupleList(deltaTable);
    uint32_t tupleCnt = TempTableCount((TempTableT *)(void *)deltaTable);
    // set updIdx to tuple count for delete
    mergeState->batchMergeState.updIdx = tupleCnt;

    // record for rollback
    ExecuteInfoT *executeInfo = TableInfoGetExecuteInfo(mergeState->batchMergeState.mergeState.tableInfo);
    ExecuteInfoSetUpdIdx(executeInfo, tupleCnt);
    return ret;
}

/*
 * ====================================== deleted methods end ====================================
 */
static Status NewDeltaPubsubMergeState(DbMemCtxT *memCtx, DeltaPubsubMergeStateT **mergeState)
{
    DB_POINTER2(memCtx, mergeState);
    // memory will be freed unitied , see Note1
    DeltaPubsubMergeStateT *state = (DeltaPubsubMergeStateT *)DbDynMemCtxAlloc(memCtx, sizeof(DeltaPubsubMergeStateT));
    if (state == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Alloc DeltaPubsubMergeStateT.");
        return GMERR_OUT_OF_MEMORY;
    }
    *state = (DeltaPubsubMergeStateT){0};
    state->batchMergeState.mergeState.planState.tagType = T_DELTA_PUBSUB_MERGE;

    *mergeState = state;
    return GMERR_OK;
}

static void DeltaPubsubMergeUpdateExecutedMsgNum(
    DeltaPubsubMergeStateT *mergeState, DmSubsEventE eventType, uint32_t batchSize, uint32_t successNum, PubsubRspE rsp)
{
    DB_POINTER(mergeState);
    TableInfoT *tableInfo = mergeState->batchMergeState.mergeState.tableInfo;
    ExecuteInfoT *executeInfo = TableInfoGetExecuteInfo(tableInfo);
    uint32_t batchTupleNum = (eventType == DM_SUBS_EVENT_UPDATE ? batchSize * 2 : batchSize);
    mergeState->batchMergeState.curIdx += (int32_t)batchTupleNum;
    uint32_t rollbackTupleNum = batchTupleNum - ((eventType == DM_SUBS_EVENT_UPDATE ? successNum * 2 : successNum));

    if (rsp == FAILED) {
        // invoke failed needs rollback previous batch
        ExecuteInfoSetExecutedPos(executeInfo, (mergeState->batchMergeState.curIdx - (int32_t)rollbackTupleNum) - 1);
    } else {
        // timeout or success needs rollback current batch
        ExecuteInfoSetExecutedPos(executeInfo, mergeState->batchMergeState.curIdx - 1);
    }
}

/*
 * =============== implement iterator in volcano begin===================
 */

Status ExecExplainDeltaPubsubMerge(ExecExplainCtxT *explain, PlanT *plan, uint32_t level)
{
    return ExecExplainMerge((void *)plan, explain->sb, "DeltaPubsubMerge", level);
}

Status ExecInitDeltaPubsubMerge(PlanT *plan, EStateT *estate, PlanStateT **planState)
{
    DB_POINTER3(plan, estate, planState);
    Status ret = GMERR_OK;
    DeltaPubsubMergeT *pubsubMerge = (void *)plan;
    DeltaPubsubMergeStateT *pubsubMergeState = NULL;
    DbMemCtxT *memCtx = estate->memCtx;

    if ((ret = NewDeltaPubsubMergeState(memCtx, &pubsubMergeState)) != GMERR_OK) {
        return ret;
    }

    // Init base batch merge
    ret = ExecInitBatchMerge(&pubsubMerge->deltaMerge, (void *)pubsubMergeState, estate);
    if (ret != GMERR_OK) {
        goto ERROR;
    }

    pubsubMergeState->session = estate->session;
    pubsubMergeState->labelDef.labelType = VERTEX_LABEL;
    pubsubMergeState->labelDef.vertexLabel = pubsubMergeState->batchMergeState.mergeState.vertexLabel;
    pubsubMergeState->batchMergeState.batchMergeOrder = MERGE_FORWARD;

    *planState = (PlanStateT *)(void *)pubsubMergeState;
    return ret;

ERROR:
    ExecEndDeltaPubsubMerge((PlanStateT *)(void *)pubsubMergeState);
    return ret;
}

Status ExecProcDeltaPubsubMerge(PlanStateT *planState, AAT *aa)
{
    DB_POINTER(planState);
    return ExecProcBatchMerge((void *)planState, aa);
}

void ExecEndDeltaPubsubMerge(PlanStateT *planState)
{
    DB_POINTER(planState);
    // end IterateMerge
    ExecEndBatchMerge((void *)planState);
}

/*
 * =============== implement iterator in volcano end===================
 */

/*
 * =============== implement template iterate merge begin =======================
 */

static Status NewPubsubReqT(MergeStateT *mergeState, QrySubsRowDataSetT *rowDataSet,
    QrySubsRowDataSetT *rollbackRowDataSet, DmSubsEventE eventType, PubsubSyncReqT **req)
{
    Status ret = GMERR_OK;
    // memory will be freed unitied , see Note1
    PubsubSyncReqT *syncReq =
        (PubsubSyncReqT *)DbDynMemCtxAlloc(mergeState->planState.estate->memCtx, sizeof(PubsubSyncReqT));
    if (syncReq == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_OUT_OF_MEMORY, "alloc PubsubSyncReqT, labelName=%s", mergeState->vertexLabel->metaCommon.metaName);
        return GMERR_OUT_OF_MEMORY;
    }
    *syncReq = (PubsubSyncReqT){0};

    syncReq->dtlReq.req.tag = PUBSUB_COMMAND;
    syncReq->dtlReq.labelDef.labelType = VERTEX_LABEL;
    syncReq->dtlReq.labelDef.vertexLabel = mergeState->vertexLabel;
    syncReq->dtlReq.rowDataSet = rowDataSet;
    syncReq->dtlReq.rollbackRowDataSet = rollbackRowDataSet;
    syncReq->dtlReq.eventType = eventType;
    *req = syncReq;
    return ret;
}

Status DeltaPubsubMergeDeltaTable(BatchTemplateMergeStateT *batchMergeState, DeltaTableT *deltaTable, bool *done)
{
    DB_POINTER3(batchMergeState, deltaTable, done);
    DeltaPubsubMergeStateT *pubsubMergeState = ((DeltaPubsubMergeStateT *)(void *)batchMergeState);
    TableInfoT *table = batchMergeState->mergeState.tableInfo;
    DeltaPubsubMethodT deltaPubsubMethods[] = {
        [WRITE_DATA_TYPE_ALL] = {NULL},
        [WRITE_DATA_TYPE_INSERT_AND_UPDATE] = {DeltaPubsubMergeDeltaTableInsertable},
        [WRITE_DATA_TYPE_DELETE] = {DeltaPubsubMergeDeltaTableDeletable},
    };
    return deltaPubsubMethods[table->writeDataType].MergeDeltaTable(pubsubMergeState, deltaTable, done);
}

Status DeltaPubsubMergePrepare(BatchTemplateMergeStateT *batchMergeState)
{
    DB_POINTER(batchMergeState);
    uint32_t totalCnt = TempTableCount(batchMergeState->mergeState.tableInfo->table);
    int32_t curIdx = batchMergeState->curIdx;
    uint32_t updIdx = batchMergeState->updIdx;
    batchMergeState->insertNum = curIdx <= (int32_t)updIdx ? (uint32_t)((int32_t)updIdx - curIdx) : 0;
    batchMergeState->updateNum =
        curIdx <= (int32_t)updIdx ? DB_HALF_OF(totalCnt - updIdx) : DB_HALF_OF(totalCnt - (uint32_t)curIdx);
    batchMergeState->maxBatchNum = SUBS_BATCH_BATCH_MAX;
    return GMERR_OK;
}

Status DeltaPubsubMergeInverseDeltaTuple(BatchTemplateMergeStateT *batchMergeState, AASlotT *deltaTuple)
{
    return GMERR_OK;
}

static Status DeltaPubsubMergeSeriBatchVertex2RowData(DeltaPubsubMergeStateT *pubsubMergeState, uint32_t batchSize,
    DmSubsEventE subEventType, QrySubsRowDataSetT *rowDataSet, QrySubsRowDataSetT *rollbackRowDataSet)
{
    char *labelName = pubsubMergeState->batchMergeState.mergeState.vertexLabel->metaCommon.metaName;
    // Serialize forward batch tuples to rowDataSet
    Status ret = SerializeBatchVertex2RowData(pubsubMergeState, batchSize, subEventType, rowDataSet, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "seri batch vertex, labelName=%s", labelName);
        return ret;
    }

    // Negate rsvdCount of delta tuples for current batch, and then serialize the tuples to rollbackRowDataSet
    ret = SerializeBatchVertex2RowData(pubsubMergeState, batchSize, subEventType, rollbackRowDataSet, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "seri rollback vertex, labelName=%s", labelName);
        return ret;
    }

    return GMERR_OK;
}

static Status DeltaPubsubMergeHandleCallbackError(BatchTemplateMergeStateT *batchMergeState, uint32_t batchSize,
    DmSubsEventE subEventType, SyncInvokeStubT *syncStub, PubsubSyncResT *pubsubRsp)
{
    char *labelName = batchMergeState->mergeState.vertexLabel->metaCommon.metaName;
    DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
        "Please check pubsub callback, tableName: %s, num:%" PRIu16 ", index:%" PRIu16 ", ret:%" PRId64, labelName,
        pubsubRsp->failedDataNum, pubsubRsp->failedIndexes[0], syncStub->errCode);
    DeltaPubsubMergeStateT *pubsubMergeState = (DeltaPubsubMergeStateT *)batchMergeState;
    CallBackErrResT *callBackErrRes = &pubsubMergeState->session->datalogCtx->callBackErrRes;
    CallBackErrResSetLabelNameAndErrCode(callBackErrRes, labelName, syncStub->errCode);
    DeltaPubsubMergeUpdateExecutedMsgNum(
        pubsubMergeState, subEventType, batchSize, pubsubRsp->failedIndexes[0], FAILED);
    if (pubsubRsp->failedIndexes[0] == 0) {
        (void)QryClearLastReverseMsg(syncStub->session, batchMergeState->mergeState.vertexLabel, subEventType);
    }
    return GMERR_DATA_EXCEPTION;
}

static void DeltaPubsubMergeRebuildRollbackRowDataSet(
    PubsubSyncResT *pubsubRsp, uint32_t batchSize, QrySubsRowDataSetT *rollbackRowDataSet)
{
    if (pubsubRsp->failedIndexes[0] > batchSize) {
        pubsubRsp->failedIndexes[0] = (uint16_t)batchSize;
    }
    DtlReverseRowsDataBuf(rollbackRowDataSet);
    rollbackRowDataSet->rowsDataNum = pubsubRsp->failedIndexes[0];
    DtlReverseRowsDataBuf(rollbackRowDataSet);
}

Status DeltaPubsubMergeSendOneBatchMsg(
    BatchTemplateMergeStateT *batchMergeState, uint32_t batchSize, BatchMergeTypeE eventType)
{
    DB_POINTER(batchMergeState);
    DeltaPubsubMergeStateT *pubsubMergeState = ((DeltaPubsubMergeStateT *)(void *)batchMergeState);
    DmSubsEventE subEventType = eventType == BATCH_UPDATE ? DM_SUBS_EVENT_UPDATE : DM_SUBS_EVENT_INSERT;
    char *labelName = batchMergeState->mergeState.vertexLabel->metaCommon.metaName;
    DbMemCtxT *memCtx = batchMergeState->mergeState.planState.estate->memCtx;
    CallBackErrResT *callBackErrRes = &pubsubMergeState->session->datalogCtx->callBackErrRes;

    // Seriallize both forward and rollback data
    QrySubsRowDataSetT rowDataSet = {0};
    QrySubsRowDataSetT rollbackRowDataSet = {0};
    Status ret = DeltaPubsubMergeSeriBatchVertex2RowData(
        pubsubMergeState, batchSize, subEventType, &rowDataSet, &rollbackRowDataSet);
    if (ret != GMERR_OK) {
        return ret;
    }

    // execute syncInvoke
    PubsubSyncReqT *req = NULL;
    if ((ret = NewPubsubReqT(&batchMergeState->mergeState, &rowDataSet, &rollbackRowDataSet, subEventType, &req)) !=
        GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to create dtlReq in DeltaPubsubMerge, labelName=%s", labelName);
        return ret;
    }

    PubsubSyncResT *pubsubRsp = NULL;
    SyncInvokeStubT *syncStub = NULL;
    if ((ret = NewSyncInvokeStub(memCtx, pubsubMergeState->session, PUBSUB_COMMAND, true, &syncStub)) != GMERR_OK) {
        return ret;
    }

    if ((ret = SyncInvoke(syncStub, (void *)req, (void *)&pubsubRsp)) == (int32_t)NO_DATA_DATALOG_NO_SUBSCRIPTION) {
        DeltaPubsubMergeUpdateExecutedMsgNum(pubsubMergeState, subEventType, batchSize, 0, SUCCESS);
        return GMERR_OK;
    } else if (ret != GMERR_OK) {
        // pubsub failed or timeout
        DB_LOG_ERROR(ret, "Unable to syncInvoke in DeltaPubsubMerge, labelName=%s", labelName);
        PubsubRspE rspState = (ret == GMERR_REQUEST_TIME_OUT) ? TIMEOUT : FAILED;
        CallBackErrResSetLabelNameAndErrCode(callBackErrRes, labelName, syncStub->errCode);
        DeltaPubsubMergeUpdateExecutedMsgNum(pubsubMergeState, subEventType, batchSize, 0, rspState);
        QryFinishGenerateReverseSyncSubMessages(syncStub->session);
        if (rspState == FAILED && req->dtlReq.rollbackMsgAppended) {
            // Failed to send the message, no need to rollback current batch
            (void)QryClearLastReverseMsg(syncStub->session, batchMergeState->mergeState.vertexLabel, subEventType);
        }
        return ret;
    }

    if (pubsubRsp->failedIndexes[0] > 0) {
        DeltaPubsubMergeRebuildRollbackRowDataSet(pubsubRsp, batchSize, &rollbackRowDataSet);
        QryReFillReverseMsg(syncStub->session, &req->dtlReq, batchMergeState->mergeState.vertexLabel, subEventType);
    }
    QryFinishGenerateReverseSyncSubMessages(syncStub->session);

    // pubsub callback failed
    if (syncStub->errCode != 0 || pubsubRsp->failedDataNum > 0 || pubsubRsp->failedIndexes[0] > 0) {
        return DeltaPubsubMergeHandleCallbackError(batchMergeState, batchSize, subEventType, syncStub, pubsubRsp);
    }

    // update position by curIdx
    DeltaPubsubMergeUpdateExecutedMsgNum(pubsubMergeState, subEventType, batchSize, batchSize, SUCCESS);
    return GMERR_OK;
}

#define MERGE_FIELD_SIZE_CONST 21
Status ExecEstimatePubsubMerge(EstimateCtxT *estimateCtx, PlanT *planTree, uint32_t *estimatedValue)
{
    DB_POINTER3(estimateCtx, planTree, estimatedValue);
    Status ret = GMERR_OK;
    uint32_t pubsubMemSize = 0;
    DeltaPubsubMergeT *mergeOp = (DeltaPubsubMergeT *)planTree;
    ret = EstimateMergeMem(estimateCtx, mergeOp->deltaMerge.label, MERGE_FIELD_SIZE_CONST, &pubsubMemSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    *estimatedValue += pubsubMemSize;
    return GMERR_OK;
}

uint32_t DeltaPubsubGetBatchSize(
    BatchTemplateMergeStateT *batchMergeState, uint32_t restMsgNum, uint32_t msgCount, BatchMergeTypeE eventType)
{
    DB_POINTER(batchMergeState);
    return PubsubGetBatchSize(batchMergeState, restMsgNum, msgCount, eventType);
}

Status DeltaPubsubSortBatchMsg(BatchTemplateMergeStateT *batchMergeState, uint32_t batchSize, BatchMergeTypeE eventType)
{
    return GMERR_OK;
}

/*
 * =============== implement template iterate merge end =======================
 */

#ifdef __cplusplus
}
#endif
