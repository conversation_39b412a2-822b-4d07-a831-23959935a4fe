/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author:
 * Create: 2024-12-01
 */
#include "ee_stream_agg_over_common.h"

#include "dm_data_math_sql.h"
#include "dm_meta_prop_stream_label.h"
#include "ee_stream_planexec_common.h"
#include "ee_log.h"  // 放在最后一行

#define STREAM_AGG_OVER_DEFAULT_SLOT_LIST_NUM 8

void StreamDeleteAggKey(DbMemCtxT *hashTableMemCtx, StreamAggKeyValueT *key)
{
    for (uint32_t i = 0; i < key->number; i++) {
        if (DM_TYPE_NEED_MALLOC(key->value[i].type) && key->value[i].value.strAddr != NULL) {
            DbDynMemCtxFree(hashTableMemCtx, key->value[i].value.strAddr);
        }
    }
    if (key->number != 0) {
        DbDynMemCtxFree(hashTableMemCtx, key->value);
    }
    DbDynMemCtxFree(hashTableMemCtx, key);
}
void StreamAggDestroyArraySlot(DbMemCtxT *memCtx, AASlotT *slot)
{
    DB_ASSERT(slot->slotType == AA_SLOT_ARRAY);
    DbDynMemCtxFree(memCtx, slot->slotArray.array);
    DbDynMemCtxFree(memCtx, slot);
}
uint32_t StreamHashMapKeyCompare(const void *key1, const void *key2)
{
    DB_POINTER2(key1, key2);
    bool result = true;
    const DmValueT *aggKey1 = (const DmValueT *)key1;
    const DmValueT *aggKey2 = (const DmValueT *)key2;
    result = DmValueIsEqual(aggKey1, aggKey2);
    if (!result) {
        return 0;
    }
    return 1;
}

uint32_t StreamAggKeyCompare(const void *key1, const void *key2)
{
    DB_POINTER2(key1, key2);
    const StreamAggKeyValueT *aggKey1 = (const StreamAggKeyValueT *)key1;
    const StreamAggKeyValueT *aggKey2 = (const StreamAggKeyValueT *)key2;
    for (uint32_t i = 0; i < aggKey1->number; i++) {
        bool result = DmValueIsEqual(&aggKey1->value[i], &aggKey2->value[i]);
        if (!result) {
            return 0;
        }
    }
    return 1;
}
static Status HashAggSlotEvalAvg(StreamAggStateCommonT *aggCommon, AASlotT *oldSlot)
{
    uint32_t aggPropNum = AASlotGetPropNum(oldSlot);
    DmValueT cntValue;
    Status ret = AASlotGetPrope(oldSlot, aggPropNum - 1, &cntValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = 0; i < aggPropNum; ++i) {
        if (!aggCommon->isAvgColumns[i]) {
            continue;
        }
        DmValueT propValue;
        ret = AASlotGetPrope(oldSlot, i, &propValue);
        if (ret != GMERR_OK) {
            break;
        }
        DmValueT avgValue;
        ret = DmValueDivDiffType(&propValue, &cntValue, &avgValue);
        if (ret != GMERR_OK) {
            break;
        }
        ret = AASlotSetPrope(oldSlot, i, avgValue);
        if (ret != GMERR_OK) {
            break;
        }
    }

    return ret;
}
Status StreamAggSlotFilterAndProjection(StreamAggStateCommonT *aggCommon, AAT *aa, AASlotT *oldSlot)
{
    DB_POINTER3(aggCommon, aa, oldSlot);
    PlanPropDescT *resultPropDesc = &aggCommon->resultPropDesc;
    ExprContextT *exprContext = aggCommon->exprContext;
    ExprStateT *projectionExpr = aggCommon->projectionExpr;
    Status ret;
    AASlotT *resultTuple = NULL;
    ret = AAPopPropSlot(aa, NULL, resultPropDesc, &resultTuple);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "create AASlot");
        return ret;
    }
    if (projectionExpr != NULL) {
        HashAggSlotEvalAvg(aggCommon, oldSlot);
        ExprSetAASlot(exprContext, resultTuple, oldSlot, oldSlot);
        ret = ExprEval(projectionExpr, exprContext);
    } else {
        ret = AASlotCopy(resultTuple, oldSlot);
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DeleteAASlot(resultTuple);
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "evaluate the projection expression");
        return ret;
    }
    return AAPushPropSlot(aa, resultTuple);
}

static Status StreamGenerateAggKey(StreamAggStateCommonT *aggCommon, AASlotT *slot, StreamAggKeyValueT **key)
{
    Status ret = GMERR_OK;
    StreamAggKeyValueT *streamAggKeyValue = NULL;
    uint32_t number = aggCommon->partitionBy.num;
    if (aggCommon->cachedAggKey != NULL) {
        streamAggKeyValue = aggCommon->cachedAggKey;
        aggCommon->cachedAggKey = NULL;
    } else {
        streamAggKeyValue =
            (StreamAggKeyValueT *)DbDynMemCtxAlloc(aggCommon->hashTableMemCtx, sizeof(StreamAggKeyValueT));
        if (SECUREC_UNLIKELY(streamAggKeyValue == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, " ");
            return GMERR_OUT_OF_MEMORY;
        }
        streamAggKeyValue->number = number;
        streamAggKeyValue->value = NULL;
        if (number != 0) {
            size_t valLen = number * sizeof(DmValueT);
            streamAggKeyValue->value = (DmValueT *)DbDynMemCtxAlloc(aggCommon->hashTableMemCtx, valLen);
            if (SECUREC_UNLIKELY(streamAggKeyValue->value == NULL)) {
                DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, " ");
                DbDynMemCtxFree(aggCommon->hashTableMemCtx, streamAggKeyValue);
                return GMERR_OUT_OF_MEMORY;
            }
            (void)memset_s(streamAggKeyValue->value, valLen, 0, valLen);
        }
    }
    uint32_t i;
    for (i = 0; i < number; i++) {
        uint32_t id = aggCommon->partitionBy.ids[i];
        ret = AASlotGetPropeDeepCopy(slot, id, aggCommon->hashTableMemCtx, &streamAggKeyValue->value[i]);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            if (aggCommon->cachedAggKey == NULL) {
                aggCommon->cachedAggKey = streamAggKeyValue;
            } else {
                StreamDeleteAggKey(aggCommon->hashTableMemCtx, streamAggKeyValue);
            }
            return ret;
        }
        // 默认字段最大长度，这里转为实际长度，后续生成hash时使用
        if (streamAggKeyValue->value[i].type == DB_DATATYPE_FIXED) {
            streamAggKeyValue->value[i].value.length = strlen(streamAggKeyValue->value[i].value.strAddr) + 1;
        }
    }
    *key = streamAggKeyValue;
    return ret;
}
static Status ExprEvalGetValueByType(
    ExprOpTypeE opType, ExprContextT *exprContext, ExprBinaryT *binary, DmValueT *originVal)
{
    if (SECUREC_UNLIKELY(!ExprIsExtdAggFunc(opType))) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_FEATURE_NOT_SUPPORTED, "Unsupported func expression type=%" PRIu32 ".", opType);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    switch (opType) {
        case EXTD_EXPR_OP_ROWNUMBER:
        case EXTD_EXPR_OP_COUNT:
            return ExprEvalCountValue(binary->right, exprContext, originVal);
        case EXTD_EXPR_OP_COLSET:
            return ExprEvalColSetValue(binary->right, exprContext, originVal);
        default:
            return ExprEvalValue(binary->right, exprContext, originVal);
    }
}

Status InitWindowHashItemForOverAgg(StreamAggStateCommonT *aggCommon, AASlotT *aggSlot,
    StreamWindowHashMapItemT *windowHashMapIt, uint32_t innerHash, StreamAggKeyValueT *aggKey)
{
    DB_ASSERT(aggCommon->tagType == T_STREAM_AGG_OVER_WINDOW || aggCommon->tagType == T_STREAM_OVER_AGG);
    DbListT *slotList = DbDynMemCtxAlloc(aggCommon->hashTableMemCtx, sizeof(DbListT));
    if (SECUREC_UNLIKELY(slotList == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, " ");
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateListWithExtendSize(
        slotList, sizeof(AASlotT *), STREAM_AGG_OVER_DEFAULT_SLOT_LIST_NUM, aggCommon->hashTableMemCtx);
    Status ret = DbAppendListItem(slotList, &aggSlot);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbDynMemCtxFree(aggCommon->hashTableMemCtx, slotList);
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "append list.");
        return ret;
    }
    // 新增的aggslot直接插入hashtable
    ret = DbOamapInsert(windowHashMapIt->aggHashMap, innerHash, aggKey, slotList, NULL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbDynMemCtxFree(aggCommon->hashTableMemCtx, slotList);
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "insert agg hashTable.");
        return ret;
    }
    return GMERR_OK;
}

Status InitWindowHashItem(StreamWindowHashMapItemT *windowHashMapIt, StreamAggStateCommonT *aggCommon,
    DmValueT windowStart, DmValueT windowEnd, AASlotT *aggSlot)
{
    windowHashMapIt->windowStart = windowStart;
    windowHashMapIt->windowEnd = windowEnd;
    windowHashMapIt->aggHashMap = DbDynMemCtxAlloc(aggCommon->hashTableMemCtx, sizeof(DbOamapT));
    if (SECUREC_UNLIKELY(windowHashMapIt->aggHashMap == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, " ");
        return GMERR_OUT_OF_MEMORY;
    }
    Status ret = DbOamapInit(windowHashMapIt->aggHashMap, 0, StreamAggKeyCompare, aggCommon->hashTableMemCtx, true);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbDynMemCtxFree(aggCommon->hashTableMemCtx, windowHashMapIt->aggHashMap);
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "init AGG hash table");
        return ret;
    }
    StreamAggKeyValueT *aggKey = NULL;
    ret = StreamGenerateAggKey(aggCommon, aggSlot, &aggKey);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    uint32_t innerHash = 0;
    if (aggKey->number != 0) {
        innerHash = DmValuesHash(aggKey->value, aggKey->number);
    }

    if (aggCommon->tagType == T_STREAM_WINDOW_AGG) {
        // 新增的aggslot直接插入hashtable
        ret = DbOamapInsert(windowHashMapIt->aggHashMap, innerHash, aggKey, aggSlot, NULL);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            if (aggCommon->cachedAggKey == NULL) {
                aggCommon->cachedAggKey = aggKey;
            } else {
                StreamDeleteAggKey(aggCommon->hashTableMemCtx, aggKey);
            }
            DbDynMemCtxFree(aggCommon->hashTableMemCtx, windowHashMapIt->aggHashMap);
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "insert agg hashTable.");
            return ret;
        }
    } else {
        ret = InitWindowHashItemForOverAgg(aggCommon, aggSlot, windowHashMapIt, innerHash, aggKey);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            if (aggCommon->cachedAggKey == NULL) {
                aggCommon->cachedAggKey = aggKey;
            } else {
                StreamDeleteAggKey(aggCommon->hashTableMemCtx, aggKey);
            }
            DbDynMemCtxFree(aggCommon->hashTableMemCtx, windowHashMapIt->aggHashMap);
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "insert agg hashTable.");
            return ret;
        }
    }
    return ret;
}
static Status AggHashSetFirstSlotProp(StreamAggStateCommonT *aggCommon, ExprSetFieldT *setField, AASlotT *aggSlot)
{
    DmValueT originVal = {};
    ExprBinaryT *binary = CastToBinary(setField->func);
    ExprOpTypeE opType = binary->expr.opType;
    Status ret = ExprEvalGetValueByType(opType, aggCommon->exprContext, binary, &originVal);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // Unable to Obtain the initialization data
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "type:%" PRIu32 ".", opType);
        return ret;
    }
    if (opType == EXTD_EXPR_OP_AVG) {
        aggCommon->isAvgColumns[setField->propId] = true;
        ret = DmValueConvert(&originVal, DB_DATATYPE_DOUBLE);
        if (ret != GMERR_OK) {
            // unable to convert avg value to double
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "type:%" PRIu32 ".", originVal.type);
            return ret;
        }
    }
    ret = AASlotSetPrope(aggSlot, setField->propId, originVal);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // Unable to set property
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "propId=%" PRIu32 ".", setField->propId);
    }
    return ret;
}

/* 第二层插入 */
Status AggHashMapInert(
    StreamAggStateCommonT *aggCommon, uint32_t hash, StreamAggKeyValueT *key, AASlotT *aggSlot, DbOamapT *map)
{
    DB_POINTER2(aggCommon, aggSlot);
    ExprArrayT *arrayExpr = &CastToFunc(aggCommon->aggExpr->expr)->array;
    ExprSetAASlot(aggCommon->exprContext, aggSlot, aggSlot, aggSlot);
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < arrayExpr->num; i++) {
        ExprSetFieldT *setField = CastToSetField(arrayExpr->expr[i]);
        ret = AggHashSetFirstSlotProp(aggCommon, setField, aggSlot);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Index is %" PRId32 ".", i);
            return ret;
        }
    }
    DbListT *slotList = NULL;
    if (aggCommon->tagType == T_STREAM_WINDOW_AGG) {
        ret = DbOamapInsert(map, hash, key, aggSlot, NULL);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "insert state hashTable.");
            return ret;
        }
    } else {
        DB_ASSERT(aggCommon->tagType == T_STREAM_AGG_OVER_WINDOW || aggCommon->tagType == T_STREAM_OVER_AGG);
        slotList = DbDynMemCtxAlloc(aggCommon->hashTableMemCtx, sizeof(DbListT));
        if (SECUREC_UNLIKELY(slotList == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, " ");
            return GMERR_OUT_OF_MEMORY;
        }
        DbCreateListWithExtendSize(
            slotList, sizeof(AASlotT *), STREAM_AGG_OVER_DEFAULT_SLOT_LIST_NUM, aggCommon->hashTableMemCtx);
        DbAppendListItem(slotList, &aggSlot);
        // 新增的aggslot直接插入hashtable
        ret = DbOamapInsert(map, hash, key, slotList, NULL);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "insert state hashTable.");
            return ret;
        }
    }
    return ret;
}

/* 第一层插入 */
Status WindowHashItemInsert(StreamAggStateCommonT *aggCommon, uint32_t hash, DmValueT *key, DmValueT windowStart,
    DmValueT windowEnd, AASlotT *aggSlot)
{
    DB_POINTER2(aggCommon, aggSlot);
    ExprArrayT *arrayExpr = &CastToFunc(aggCommon->aggExpr->expr)->array;
    ExprSetAASlot(aggCommon->exprContext, aggSlot, aggSlot, aggSlot);
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < arrayExpr->num; i++) {
        ExprSetFieldT *setField = CastToSetField(arrayExpr->expr[i]);
        ret = AggHashSetFirstSlotProp(aggCommon, setField, aggSlot);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Index is %" PRId32 ".", i);
            return ret;
        }
    }
    StreamWindowHashMapItemT *windowHashMapIt =
        (StreamWindowHashMapItemT *)DbDynMemCtxAlloc(aggCommon->hashTableMemCtx, sizeof(StreamWindowHashMapItemT));
    if (windowHashMapIt == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, " ");
        return GMERR_OUT_OF_MEMORY;
    }

    ret = DbOamapInsert(aggCommon->hashTable, hash, key, windowHashMapIt, NULL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbDynMemCtxFree(aggCommon->hashTableMemCtx, windowHashMapIt);
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "insert state hashTable.");
        return ret;
    }
    ret = InitWindowHashItem(windowHashMapIt, aggCommon, windowStart, windowEnd, aggSlot);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        (void)DbOamapRemove(aggCommon->hashTable, hash, key);
        DbDynMemCtxFree(aggCommon->hashTableMemCtx, windowHashMapIt);
    }
    return ret;
}

Status ExecStreamAggUpdateSlotInList(StreamAggStateCommonT *aggCommon, void *lookUpEntry, AASlotT *newSlot)
{
    AASlotT *calcSlot = NULL;
    if (aggCommon->tagType == T_STREAM_WINDOW_AGG) {
        calcSlot = lookUpEntry;
        ExprSetAASlot(aggCommon->exprContext, calcSlot, calcSlot, newSlot);
        Status ret = StreamExprEvalArray(aggCommon->aggExpr, aggCommon->exprContext);
        DB_ASSERT(aggCommon->tmpSlot == NULL);
        aggCommon->tmpSlot = newSlot;
        return ret;
    } else {
        DbListT *lookupSlotList = lookUpEntry;
        DB_ASSERT(lookupSlotList->count > 0);
        uint32_t lastIdx = DbListGetItemCnt(lookupSlotList) - 1;
        DB_ASSERT(aggCommon->tagType == T_STREAM_AGG_OVER_WINDOW || aggCommon->tagType == T_STREAM_OVER_AGG);
        calcSlot = *(AASlotT **)DbListItem(lookupSlotList, lastIdx);
        ExprSetAASlot(aggCommon->exprContext, newSlot, calcSlot, newSlot);
        Status ret = StreamExprEvalArray(aggCommon->aggExpr, aggCommon->exprContext);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        if (aggCommon->isOnlyInstant) {
            DB_ASSERT(lastIdx == 0);
            DbClearList(lookupSlotList);
            if (aggCommon->tmpSlot == NULL) {
                aggCommon->tmpSlot = calcSlot;
            } else {
                StreamAggDestroyArraySlot(aggCommon->hashTableMemCtx, calcSlot);
            }
        }
        return DbAppendListItem(lookupSlotList, &newSlot);
    }
}

static Status ExecProcWindowTableFilter(ExprStateT *qualExpr, ExprContextT *exprContext, AASlotT *dataSlot)
{
    Status ret = GMERR_OK;
    if (qualExpr != NULL) {
        bool match = true;
        ExprSetAASlot(exprContext, NULL, dataSlot, NULL);
        if (SECUREC_UNLIKELY((ret = ExprEvalQual(qualExpr, exprContext, &match)) != GMERR_OK)) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "eval expr");
            return ret;
        }
        if (!match) {
            return GMERR_NO_DATA;
        }
    }
    return ret;
}
/**
 * 生成aggSlot
 * 1.如果存在投影下推，在生成state时会创建windowSlot，这里需要线拷贝到windowSlot，借助preProjectionExpr初始化aggSlot；
 * 2.如果不存在投影下推，则preProjectionExpr是空的，这时fromSlot的属性合newSlot的前n个属性是一样的，不经过windowSlot。
 * @param aggCommon agg信息
 * @param fromSlot 上层传入的slot
 * @param info 窗口信息
 * @param newSlot 最终生成的aggSlot
 * @return
 */
Status ExecAggNewAASlotBySlot(StreamAggStateCommonT *aggCommon, ExprStateT *qualExpr, AASlotT *fromSlot,
    const StreamWindowInfoT *info, AASlotT **newSlot)
{
    Status ret;
    if (aggCommon->tmpSlot != NULL) {
        *newSlot = aggCommon->tmpSlot;
        aggCommon->tmpSlot = NULL;
    } else {
        ret = NewAASlot(aggCommon->hashTableMemCtx, NULL, &aggCommon->appendPropDesc, newSlot);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    if (aggCommon->preProjectionExpr != NULL) {
        ret = AASlotArrayCopyInStreamPlan(aggCommon->windowSlot, fromSlot);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        uint32_t propeNum = AASlotGetPropNum(fromSlot);
        AASlotSetPrope(aggCommon->windowSlot, propeNum, info->windowStart);
        AASlotSetPrope(aggCommon->windowSlot, propeNum + 1, info->windowEnd);
        ret = ExecProcWindowTableFilter(qualExpr, aggCommon->exprContext, aggCommon->windowSlot);
        if (ret != GMERR_OK) {
            return ret;
        }
        ExprSetAASlot(aggCommon->exprContext, *newSlot, aggCommon->windowSlot, aggCommon->windowSlot);
        ret = ExprEval(aggCommon->preProjectionExpr, aggCommon->exprContext);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    } else {
        // 初始化待聚合的slot
        ret = AASlotArrayCopyInStreamPlan(*newSlot, fromSlot);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        uint32_t propeNum = AASlotGetPropNum(fromSlot);
        AASlotSetPrope(*newSlot, propeNum, info->windowStart);
        AASlotSetPrope(*newSlot, propeNum + 1, info->windowEnd);
        ret = ExecProcWindowTableFilter(qualExpr, aggCommon->exprContext, *newSlot);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}
Status StreamAggInsertIntoSecondLayerHash(
    StreamAggStateCommonT *aggCommon, AASlotT *slot, StreamWindowHashMapItemT *lookupHashMap, AASlotT *newSlot)
{
    StreamAggKeyValueT *aggKey;
    Status ret = StreamGenerateAggKey(aggCommon, newSlot, &aggKey);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    uint32_t aggHash = 0;
    if (aggKey->number != 0) {
        aggHash = DmValuesHash(aggKey->value, aggKey->number);
    }
    void *lookupSlotList = DbOamapLookup(lookupHashMap->aggHashMap, aggHash, aggKey, NULL);
    /* 第二层hash没有找到，插入第二场hash */
    if (lookupSlotList == NULL) {
        ret = AggHashMapInert(aggCommon, aggHash, aggKey, newSlot, lookupHashMap->aggHashMap);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            if (aggCommon->cachedAggKey == NULL) {
                aggCommon->cachedAggKey = aggKey;
            } else {
                StreamDeleteAggKey(aggCommon->hashTableMemCtx, aggKey);
            }
        }
    } else {
        /* 第二层hash找到了 */
        // 遍历list，更新每个数据，并更新当前数据
        ret = ExecStreamAggUpdateSlotInList(aggCommon, lookupSlotList, newSlot);
        DB_ASSERT(aggCommon->cachedAggKey == NULL);
        aggCommon->cachedAggKey = aggKey;
    }
    return ret;
}

Status StreamAggPushSlot(DbMemCtxT *memCtx, StreamAggStateCommonT *aggCommon, AASlotT *slot, AAT *aa, bool needDestroy)
{
    Status ret;
    if (AAPropSlotsIsFull(aa)) {
        ret = PushSlotIntoTmpList(memCtx, &aggCommon->tmpItemList, &aggCommon->freeItemList, slot);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "push lookupSlot to tmpList");
        }
    } else {
        ret = StreamAggSlotFilterAndProjection(aggCommon, aa, slot);
        if (needDestroy) {
            if (aggCommon->tmpSlot == NULL) {
                aggCommon->tmpSlot = slot;
            } else {
                StreamAggDestroyArraySlot(aggCommon->hashTableMemCtx, slot);
            }
        }
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "push lookupSlot to aa");
        }
    }
    return ret;
}

static void StreamAggStatePushDataForOverAgg(
    DbMemCtxT *memCtx, StreamAggStateCommonT *aggCommon, StreamWindowHashMapItemT *value, AAT *aa)
{
    Status ret;
    DbOamapIteratorT iter = 0;
    StreamAggKeyValueT *key = NULL;
    DbListT *slotList = NULL;
    ExprArrayT *arrayExpr = &CastToFunc(aggCommon->aggExpr->expr)->array;
    while (DbOamapFetch(value->aggHashMap, &iter, (void **)&key, (void **)&slotList) == GMERR_OK) {
        /* 在关闭窗口时更新所有slot的full agg值，最后的slot的值应该是最新的 */
        AASlotT *latestSlot = *(AASlotT **)DbListItem(slotList, slotList->count - 1);
        DmValueT originVal = {};
        for (uint32_t i = 0; i < slotList->count - 1; i++) {
            AASlotT *lookupSlot = *(AASlotT **)DbListItem(slotList, i);
            for (uint32_t j = 0; j < arrayExpr->num; j++) {
                ExprSetFieldT *setField = CastToSetField(arrayExpr->expr[j]);
                if (setField->func->opType == EXTD_EXPR_OP_ROWNUMBER) {
                    continue;
                }
                // todo 可以优化成从latestSlot取一次
                ret = AASlotGetPrope(latestSlot, setField->propId, &originVal);
                if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                    DB_LOG_ERROR(ret, "Obtain the initialization data");
                    continue;
                }
                ret = AASlotSetPrope(lookupSlot, setField->propId, originVal);
                if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                    DB_LOG_ERROR(ret, "set property, propId=%" PRIu32 ".", setField->propId);
                    continue;
                }
            }
            ret = StreamAggPushSlot(memCtx, aggCommon, lookupSlot, aa, true);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                continue;
            }
        }
        ret = StreamAggPushSlot(memCtx, aggCommon, latestSlot, aa, true);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            continue;
        }
        DbDestroyList(slotList);
        DbDynMemCtxFree(aggCommon->hashTableMemCtx, slotList);
        if (aggCommon->cachedAggKey == NULL) {
            aggCommon->cachedAggKey = key;
        } else {
            StreamDeleteAggKey(aggCommon->hashTableMemCtx, key);
        }
    }
}

static void StreamAggPushData(
    DbMemCtxT *memCtx, StreamAggStateCommonT *aggCommon, StreamWindowHashMapItemT *value, AAT *aa)
{
    Status ret = GMERR_OK;
    if (aggCommon->tagType == T_STREAM_WINDOW_AGG) {
        DbOamapIteratorT iter = 0;
        AASlotT *lookupSlot = NULL;
        StreamAggKeyValueT *key = NULL;
        while (DbOamapFetch(value->aggHashMap, &iter, (void **)&key, (void **)&lookupSlot) == GMERR_OK) {
            ret = StreamAggPushSlot(memCtx, aggCommon, lookupSlot, aa, true);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                // 不处理报错
                continue;
            }
            if (aggCommon->cachedAggKey == NULL) {
                aggCommon->cachedAggKey = key;
            } else {
                StreamDeleteAggKey(aggCommon->hashTableMemCtx, key);
            }
        }
    } else {
        StreamAggStatePushDataForOverAgg(memCtx, aggCommon, value, aa);
    }
}

static void ExecAggFreeSlotInMap(StreamAggStateCommonT *aggCommon, StreamWindowHashMapItemT *value)
{
    DbOamapIteratorT iter = 0;
    StreamAggKeyValueT *key = NULL;
    DbListT *slotList = NULL;
    while (DbOamapFetch(value->aggHashMap, &iter, (void **)&key, (void **)&slotList) == GMERR_OK) {
        for (uint32_t i = 0; i < slotList->count; i++) {
            AASlotT *lookupSlot = *(AASlotT **)DbListItem(slotList, i);
            if (aggCommon->tmpSlot == NULL) {
                aggCommon->tmpSlot = lookupSlot;
            } else {
                StreamAggDestroyArraySlot(aggCommon->hashTableMemCtx, lookupSlot);
            }
        }
        DbDestroyList(slotList);
        DbDynMemCtxFree(aggCommon->hashTableMemCtx, slotList);
        if (aggCommon->cachedAggKey == NULL) {
            aggCommon->cachedAggKey = key;
        } else {
            StreamDeleteAggKey(aggCommon->hashTableMemCtx, key);
        }
    }
}
#define MALLOC_TRIM_THREASHOLD 10

Status ExecAggTryCloseWindow(
    DbMemCtxT *memCtx, StreamAggStateCommonT *aggCommon, const DmStreamWatermarkStatusT **watermarkStatus, AAT *aa)
{
    const DmStreamWatermarkStatusT *tmpWatermarkStatus = DmGetWatermark(aggCommon->currentStreamLabel);
    if (SECUREC_UNLIKELY(tmpWatermarkStatus == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "unsupported label type");
        return GMERR_INTERNAL_ERROR;
    }
    DbOamapIteratorT iter = 0;
    DmValueT *key = NULL;
    StreamWindowHashMapItemT *value = NULL;
    bool windowClosed = false;
    int64_t windowSize = 0;
    while (DbOamapFetch(aggCommon->hashTable, &iter, (void **)&key, (void **)&value) == GMERR_OK) {
        if (value->windowEnd.value.longValue < tmpWatermarkStatus->watermark ||
            (!tmpWatermarkStatus->isTolerant && value->windowEnd.value.longValue == tmpWatermarkStatus->watermark)) {
            windowClosed = true;
            windowSize = value->windowEnd.value.longValue - value->windowStart.value.longValue;
            // 塞到tmpValue
            if (!aggCommon->isOnlyInstant) {
                StreamAggPushData(memCtx, aggCommon, value, aa);
            } else {
                ExecAggFreeSlotInMap(aggCommon, value);
            }
            // 释放当前
            value = DbOamapRemoveByIdx(aggCommon->hashTable, iter - 1);
            if (value != NULL) {
                DbOamapDestroy(value->aggHashMap);
                DbDynMemCtxFree(aggCommon->hashTableMemCtx, value->aggHashMap);
                DbDynMemCtxFree(aggCommon->hashTableMemCtx, value);
                value = NULL;
                if (aggCommon->cachedWindowKey == NULL) {
                    aggCommon->cachedWindowKey = key;
                } else {
                    DbDynMemCtxFree(aggCommon->hashTableMemCtx, key);
                }
                key = NULL;
            }
        }
    }
    if (windowClosed && windowSize > MALLOC_TRIM_THREASHOLD) {
        (void)DbAdptMallocTrim(0);  // 有窗口关闭时，进行内存回收
    }
    *watermarkStatus = tmpWatermarkStatus;
    return GMERR_OK;
}

bool ExecStreamAggCheckDelay(const DmStreamWatermarkStatusT *watermarkStatus, int64_t windowEndTime)
{
    return (watermarkStatus->watermark > windowEndTime ||
            (!watermarkStatus->isTolerant && watermarkStatus->watermark == windowEndTime));
}

Status ExecInitAggCommonAvgColumns(EStateT *estate, StreamAggStateCommonT *aggCommon)
{
    PlanPropDescT *aggSlotDesc = &aggCommon->appendPropDesc;
    if (aggSlotDesc == NULL) {
        return GMERR_OK;
    }
    uint32_t memSize = aggSlotDesc->propNum;
    aggCommon->isAvgColumns = DbDynMemCtxAlloc(estate->memCtx, memSize);
    if (aggCommon->isAvgColumns == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, " ");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(aggCommon->isAvgColumns, memSize, 0, memSize);
    return GMERR_OK;
}

Status ExecStreamAggLookUpHashEntry(StreamAggStateCommonT *aggCommon, ExprStateT *qualExpr, AASlotT *slot,
    const StreamWindowInfoT *info, AASlotT **resultTuple)
{
    /* 当前slot的windowEnd和水位做对比 如果小于水位，则不处理；否则，塞到map里 */
    DB_POINTER2(aggCommon, slot);
    // state上缓存了第一层的key缓存，如果没有，再申请
    DmValueT *windowKey = aggCommon->cachedWindowKey;
    aggCommon->cachedWindowKey = NULL;
    if (windowKey == NULL) {
        windowKey = (DmValueT *)DbDynMemCtxAlloc(aggCommon->hashTableMemCtx, sizeof(DmValueT));
        if (SECUREC_UNLIKELY(windowKey == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, " ");
            return GMERR_OUT_OF_MEMORY;
        }
    }
    *windowKey = info->windowEnd;
    uint32_t hash = DmValuesHash(windowKey, 1);
    StreamWindowHashMapItemT *lookupHashMap =
        (StreamWindowHashMapItemT *)DbOamapLookup(aggCommon->hashTable, hash, windowKey, NULL);
    AASlotT *newSlot = NULL;
    Status ret = ExecAggNewAASlotBySlot(aggCommon, qualExpr, slot, info, &newSlot);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        if (newSlot != NULL) {
            aggCommon->tmpSlot = newSlot;
        }
        aggCommon->cachedWindowKey = windowKey;
        return ret;
    }
    if (lookupHashMap == NULL) {
        // 新申请资源插入
        ret = WindowHashItemInsert(aggCommon, hash, windowKey, info->windowStart, info->windowEnd, newSlot);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            aggCommon->tmpSlot = newSlot;
            aggCommon->cachedWindowKey = windowKey;
            return ret;
        }
    } else {
        /* 第一层找到了 */
        aggCommon->cachedWindowKey = windowKey;
        ret = StreamAggInsertIntoSecondLayerHash(aggCommon, slot, lookupHashMap, newSlot);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            aggCommon->tmpSlot = newSlot;
            return ret;
        }
    }
    *resultTuple = newSlot;
    return ret;
}
Status GenWindowAndProcHash(DbMemCtxT *memCtx, StreamAggStateCommonT *aggCommon,
    const DmStreamWatermarkStatusT *watermarkStatus, AASlotT *dataSlot, AAT *aa)
{
    // 计算数据哪些窗口
    DmValueT tmpValue = {0};
    Status ret = AASlotGetPrope(dataSlot, aggCommon->timeColId, &tmpValue);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get event time");
        return ret;
    }
    int64_t lastStart;
    bool needGen = GetWindowStartWithOffset(tmpValue.value.longValue, 0, aggCommon->slide, &lastStart);
    if (!needGen) {
        return GMERR_OK;
    }
    for (int64_t start = lastStart; start > tmpValue.value.longValue - aggCommon->size; start -= aggCommon->slide) {
        bool isDelay = ExecStreamAggCheckDelay(watermarkStatus, start + aggCommon->size);
        if (isDelay) {
            continue;
        }
        StreamWindowInfoT info = {.windowStart = {.type = DB_DATATYPE_INT64, .value.longValue = start},
            .windowEnd = {.type = DB_DATATYPE_INT64, .value.longValue = start + aggCommon->size}};
        AASlotT *resultTuple = NULL;
        ret = ExecStreamAggLookUpHashEntry(aggCommon, aggCommon->qualExpr, dataSlot, &info, &resultTuple);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            continue;
        }
        if (resultTuple != NULL && aggCommon->isOnlyInstant) {
            ret = StreamAggPushSlot(memCtx, aggCommon, resultTuple, aa, false);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                continue;
            }
        }
    }
    return GMERR_OK;
}
