/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: API for datalog user define function.
 * Author: GMDBv5 EE Team
 * Create: 2022-8-31
 */

#ifndef EE_UDF_H
#define EE_UDF_H
#ifdef FEATURE_DATALOG

#include "db_hashmap.h"
#include "db_mem_context.h"
#include "dm_data_prop.h"
#include "se_instance.h"
#include "ee_temp_table.h"
#include "ee_plan_state.h"
#include "dm_meta_udf.h"
#include "ee_feature_import.h"
#include "ee_associative_array.h"
#include "gm_udf.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum GmUdfWriterType {
    GMUDF_WRITER_DELTA,
    GMUDF_WRITER_AA,
    GMUDF_WRITER_BUTT,
} GmUdfWriterTypeE;

struct GmUdfWriter {
    GmUdfWriterTypeE type;
    DbMemCtxT *memCtx;
    DmVertexDescT *aaSlotDesc;
    PlanPropDescT *planPropDesc;
    bool execUpgradeDml;                      // copy from dtlCtx->execUpgradeDml
    bool useCurrentUpgradeVersionWhenAccess;  // copy from dtlCtx->useCurrentUpgradeVersionWhenAccess
    bool usePreTopoSortID;                    // copy from dtlCtx->usePreTopoSortID
};

typedef enum GmUdfReaderType {
    GMUDF_READER_LABEL,
    GMUDF_READER_DELTA,
    GMUDF_READER_INDEX,
    GMUDF_READER_AA,
    GMUDF_READER_KV,
} GmUdfReaderTypeE;

typedef enum TbmOpCode {
    OP_UDF_INSERT = 0u,
    OP_UDF_DELETE,
    OP_UDF_UPDATE,
} TbmOpCodeT;

typedef TbmOpCodeT MsgOpCodeT;

struct GmUdfCtx {
    SeRunCtxHdT seRunCtx;
    DbMemCtxT *memctx;
    DbMemCtxT *udfmemctx;  // manage memory used inside in the udf
    DbMemCtxT *trxMemCtx;  // manage memory for delta table

    DmVertexLabelT **orgList;  // org tables which udf will read
    uint32_t orgNum;

    DeltaTableT **deltaList;   // delta tables which udf will read and write
    int32_t *deltaUpgVersion;  // upgradeVersion of delta tables
    GmUdfWriterT **writerList;
    uint32_t deltaNum;

    GmUdfReaderT *kvReader;  // GmUdfKvReaderT, kv table which udf will read

    PriorityQueueT *dtlTableQueue;  // the delta tables which was modifiled should be added to queue
    uint64_t startTime;
    char *udfName;                            // function name
    DmNamespaceT *nsp;                        // namespace info
    bool execUpgradeDml;                      // copy from dtlCtx->execUpgradeDml
    bool useCurrentUpgradeVersionWhenAccess;  // copy from dtlCtx->useCurrentUpgradeVersionWhenAccess
    bool usePreTopoSortID;                    // copy from dtlCtx->usePreTopoSortID
};

struct GmUdfShmCtx {
    DbMemCtxT *memCtx;
};

typedef struct GmUdfKvReader GmUdfKvReaderT;

Status NewGmUdfCtx(EStateT *estate, DmUdfBaseT *udf, GmUdfCtxT **ctx);
void DestroyGmUdfCtx(GmUdfCtxT *ctx);
void SetGmUdfStartTime(GmUdfCtxT *ctx);
void CheckGmUdfTimeout(GmUdfCtxT *ctx);
Status GmUdfCreateAAReader(GmUdfCtxT *ctx, AAT *aa, GmUdfReaderT **reader);
Status GmUdfCreateAAWriter(GmUdfCtxT *ctx, AAT *aa, PlanPropDescT *desc, GmUdfWriterT **writer);
Status ReAllocMem4UdfBuf(uint8_t **udfBuf, uint32_t *curBufLen, uint32_t newBufLen, DbMemCtxT *memCtx);

// convert Udf Blunder code, prevent the executor from ignoring the exception of UDF.
inline static Status ConvertUdfError(int32_t err)
{
    return err == GMERR_OK ? GMERR_OK : GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
}

Status UdfTimeoutCallback(ObserverT *observer, void *ctx, DbListT *tupleList);

Status GmUdfCtxInitDeltaListImpl(EStateT *estate, DmUdfBaseT *udf, GmUdfCtxT *newCtx);
Status GmUdfDeltaTableAppendImpl(GmUdfWriterT *writer, uint32_t tupleStruLen, void *tuple);

void RegPlanGmUdfMemory(ImportFusionFunctionsT *fusionFunctions);
void RegPlanGmUdfInfoGetter(ImportFusionFunctionsT *fusionFunctions);

void RegPlanGmUdfWriteDeltaTable(ImportFusionFunctionsT *fusionFunctions);

#ifdef __cplusplus
}
#endif
#endif  // FEATURE_DATALOG
#endif  // EE_UDF_H
