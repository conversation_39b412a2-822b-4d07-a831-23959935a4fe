/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: API for datalog user define function.
 * Note1: memCtx is used in short process period , we will free all memory when finishing one request
 * Author: GMDBv5 EE Team
 * Create: 2022-8-26
 */

#ifdef FEATURE_DATALOG
#include "dm_meta_basic.h"
#include "dm_meta_prop_label.h"
#include "ee_plan_stmt.h"
#include "gm_udf.h"
#include "ee_udf_reader.h"
#include "ee_feature_import.h"
#include "ee_udf.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

DB_THREAD_LOCAL GmUdfCtxT *g_gmdbDtlUdfCtx = NULL;

GmUdfCtxT *GmUdfGetCurUdfCtx(void)
{
    return g_gmdbDtlUdfCtx;
}

void GmUdfSetCurUdfCtx(void *tlsValue)
{
    g_gmdbDtlUdfCtx = (GmUdfCtxT *)tlsValue;
}

#define MEM_SIZE_UNIT 1024

void *GmUdfMemAllocImpl(GmUdfCtxT *ctx, size_t size)
{
    if (ctx == NULL) {
        DB_LOG_ERROR(GMERR_NULL_VALUE_NOT_ALLOWED, "Input para null, size=%zu.", size);
        return NULL;
    }

    if (ctx->udfmemctx == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Udf memctx null");
        return NULL;
    }

    uint32_t alarmMemSize = (uint32_t)DbCfgGetInt32Lite(DB_CFG_UDF_MEM_ALLOC_ALARM, NULL) * MEM_SIZE_UNIT;
    if (size > alarmMemSize) {
        DB_LOG_WARN(GMERR_INVALID_VALUE, "alloc mem exceeds:%s, size=%" PRIu64 "kb.", ctx->udfName,
            (uint64_t)size / MEM_SIZE_UNIT);
    }

    // memory could be freed by user calling GmUdfMemFree in udf. Otherwise, memory will be freed when udfmemctx delete
    return DbDynMemCtxAlloc(ctx->udfmemctx, size);
}

void GmUdfMemFreeImpl(GmUdfCtxT *ctx, void *ptr)
{
    if (ctx == NULL || ptr == NULL) {
        DB_LOG_ERROR(GMERR_NULL_VALUE_NOT_ALLOWED, "Input para null.");
        return;
    }

    if (ctx->udfmemctx == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Udf memctx null");
        return;
    }

    DbDynMemCtxFree(ctx->udfmemctx, ptr);
}

Status GmUdfCtxInitOrgList(EStateT *estate, DmUdfBaseT *udf, GmUdfCtxT *newCtx)
{
    DB_POINTER3(estate, udf, newCtx);
    Status ret = GMERR_OK;

    if (udf->accessOrgNum == 0) {
        return GMERR_OK;
    }

    size_t size = sizeof(DmVertexLabelT *) * udf->accessOrgNum;
    // memory will be freed unitied, see Note1
    newCtx->orgList = DbDynMemCtxAlloc(estate->memCtx, size);
    if (newCtx->orgList == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_AND_SET_LASERR(ret, "alloc list, num=%" PRIu32, udf->accessOrgNum);
        return ret;
    }

    (void)memset_s(newCtx->orgList, size, 0, size);
    newCtx->orgNum = udf->accessOrgNum;

    for (uint32_t i = 0; i < udf->accessOrgNum; i++) {
        CataKeyT catakey = {.dbId = estate->dbId, .nspId = estate->nspId, .labelName = udf->accessOrgNames[i]};
        ret = CataGetVertexLabelByName(DbGetInstanceByMemCtx(estate->memCtx), &catakey, &newCtx->orgList[i]);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get vertexLabel, name=%s.", catakey.labelName);
            return ret;
        }
    }
    return GMERR_OK;
}

Status GmUdfCtxInitWriterList(EStateT *estate, DmUdfBaseT *udf, GmUdfCtxT *newCtx)
{
    DB_POINTER3(estate, udf, newCtx);

    if (udf->accessDeltaNum == 0) {
        return GMERR_OK;
    }

    size_t size = udf->accessDeltaNum * sizeof(GmUdfWriterT *);
    // memory will be freed unitied, see Note1
    newCtx->writerList = DbDynMemCtxAlloc(estate->memCtx, size);
    if (newCtx->writerList == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc list, num=%" PRIu32, udf->accessDeltaNum);
        return GMERR_OUT_OF_MEMORY;
    }

    (void)memset_s(newCtx->writerList, size, 0, size);
    return GMERR_OK;
}

// If the udf's input parameters don't include GmUdfCtxT, it can't access udfMemCtx, so we don't need to create it.
static inline bool DtlNeedCreateUdfMemCtx(DmUdfBaseT *udf)
{
    char *udfName = udf->metaCommon.metaName;
    return !(udf->udfType == DM_UDF_TBM || udf->udfType == DM_UDF_MSG_NOTIFY || strcmp(udfName, DTL_UNINIT_FUNC) == 0 ||
             strcmp(udfName, DTL_INIT_FUNC) == 0);
}

static Status CreateUdfMemCtx(EStateT *estate, DmUdfBaseT *udf, GmUdfCtxT *ctx)
{
    if (!DtlNeedCreateUdfMemCtx(udf)) {
        return GMERR_OK;
    }

    if (estate->session->datalogCtx->udfMemCtx != NULL) {
        ctx->udfmemctx = estate->session->datalogCtx->udfMemCtx;
        return GMERR_OK;
    }
    char udfMemCtxName[DB_MAX_MEMCTX_NAME - 1];
    errno_t error = strncpy_s(udfMemCtxName, sizeof(udfMemCtxName), udf->metaCommon.metaName, DB_MAX_MEMCTX_NAME - 2);
    if (error != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "copy MemCtx name from funcName:%s", udf->metaCommon.metaName);
        return GMERR_INTERNAL_ERROR;
    }
    uint32_t maxUdfMem = (uint32_t)DbCfgGetInt32Lite(DB_CFG_UDF_MAX_MEM, NULL) * MEM_SIZE_UNIT;
    DbMemCtxArgsT args = {0};
    // maxTotalAllocSize 约束用户可申请总内存大小上限，符合配置项udfMemSizeMax实际表达含义
    args.maxTotalAllocSize = maxUdfMem;
    args.collectAllocSizeOnThisTree = true;
    DbMemCtxT *udfMemCtx =
        (DbMemCtxT *)DbCreateDynMemCtx(estate->session->datalogCtx->dtlMemCtx, false, udfMemCtxName, &args);
    if (udfMemCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "create MemCtx, funcName=%s", udf->metaCommon.metaName);
        return GMERR_OUT_OF_MEMORY;
    }
    udfMemCtx->isUdfCtx = true;
    ctx->udfmemctx = udfMemCtx;
    estate->session->datalogCtx->udfMemCtx = udfMemCtx;
    return GMERR_OK;
}

static void GmUdfCtxInitCommon(EStateT *estate, DmUdfBaseT *udf, GmUdfCtxT *newCtx)
{
    DtlSessCtxT *dtlCtx = estate->session->datalogCtx;
    *newCtx = (GmUdfCtxT){0};
    newCtx->memctx = estate->memCtx;
    newCtx->seRunCtx = estate->seInstance;
    newCtx->trxMemCtx = estate->trxMemCtx;
    newCtx->startTime = 0;
    newCtx->udfName = udf->metaCommon.metaName;
    newCtx->useCurrentUpgradeVersionWhenAccess = DtlSessGetUseCurrUpgradeVersionWhenAccess(dtlCtx);
    newCtx->usePreTopoSortID = DtlSessGetUsePreTopoSortID(dtlCtx);
    newCtx->execUpgradeDml = DtlSessGetExecUpgDml(dtlCtx);
}

Status NewGmUdfCtx(EStateT *estate, DmUdfBaseT *udf, GmUdfCtxT **ctx)
{
    DB_POINTER3(estate, udf, ctx);
    DmNamespaceT *nsp = NULL;
    Status ret = GMERR_OK;

    // memory will be freed unitied, see Note1
    GmUdfCtxT *newCtx = DbDynMemCtxAlloc(estate->memCtx, sizeof(GmUdfCtxT));
    if (newCtx == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc GmUdfCtx.");
        return GMERR_OUT_OF_MEMORY;
    }
    GmUdfCtxInitCommon(estate, udf, newCtx);
    if ((ret = CreateUdfMemCtx(estate, udf, newCtx)) != GMERR_OK) {
        return ret;
    }

    if ((ret = CataGetNamespaceById(estate->session->namespaceId, &nsp, DbGetInstanceByMemCtx(estate->memCtx))) !=
        GMERR_OK) {
        DB_LOG_ERROR(ret, "get namespace, id:%" PRIu32, estate->session->namespaceId);
        goto ERROR;
    }
    newCtx->nsp = nsp;

    if ((ret = GmUdfCtxInitOrgList(estate, udf, newCtx)) != GMERR_OK) {
        goto ERROR;
    }
#ifdef FEATURE_DATALOG
    if ((ret = GmUdfCtxInitDeltaList(estate, udf, newCtx)) != GMERR_OK) {
        goto ERROR;
    }
#endif

    if ((ret = GmUdfCtxInitWriterList(estate, udf, newCtx)) != GMERR_OK) {
        goto ERROR;
    }

    if ((ret = GmUdfCtxInitKvReader(estate->memCtx, udf, estate->session->namespaceId, newCtx)) != GMERR_OK) {
        goto ERROR;
    }
    newCtx->dtlTableQueue = estate->dtlTableQueue;
    *ctx = newCtx;
    GmUdfSetCurUdfCtx(newCtx);
    return GMERR_OK;
ERROR:
    DestroyGmUdfCtx(newCtx);
    return ret;
}

void DestroyGmUdfCtx(GmUdfCtxT *ctx)
{
    DB_POINTER(ctx);
    GmUdfSetCurUdfCtx(NULL);
    if (ctx->udfmemctx != NULL) {
        DbMemCtxReset(ctx->udfmemctx);
        ctx->udfmemctx = NULL;
    }
    if (ctx->nsp != NULL) {
        CataReleaseNamespace(DbGetInstanceByMemCtx(ctx->memctx), ctx->nsp);
        ctx->nsp = NULL;
    }
    for (uint32_t i = 0; i < ctx->orgNum; i++) {
        if (ctx->orgList[i] != NULL) {
            (void)CataReleaseVertexLabel(ctx->orgList[i]);
            ctx->orgList[i] = NULL;
        }
    }

    if (ctx->kvReader != NULL) {
        DestroyKvReader(ctx->kvReader);
        ctx->kvReader = NULL;
    }
}

void SetGmUdfStartTime(GmUdfCtxT *ctx)
{
    DB_POINTER(ctx);
    ctx->startTime = DbClockGetTsc();
}

void CheckGmUdfTimeout(GmUdfCtxT *ctx)
{
    DB_POINTER(ctx);
    uint32_t timeoutThreshold = (uint32_t)DbCfgGetInt32Lite(DB_CFG_DTL_UDF_TIME_OUT, NULL);
    uint64_t startTime = ctx->startTime;
    uint64_t endTime = DbClockGetTsc();
    if (endTime <= startTime) {
        return;
    }
    double costTime = (double)DbToMseconds(endTime - startTime);
    if (costTime >= timeoutThreshold) {
        DB_LOG_WARN(GMERR_REQUEST_TIME_OUT, "UDF func:%s exceed. cost:%lf ms, threshold:%" PRId32 " ms.", ctx->udfName,
            costTime, timeoutThreshold);
    }
}

// realloc memory for udf buffer if newBufLen > *curBufLen
Status ReAllocMem4UdfBuf(uint8_t **udfBuf, uint32_t *curBufLen, uint32_t newBufLen, DbMemCtxT *memCtx)
{
    DB_POINTER3(udfBuf, curBufLen, memCtx);
    if (*udfBuf != NULL && *curBufLen >= newBufLen) {
        (void)memset_s(*udfBuf, *curBufLen, 0x00, *curBufLen);
        return GMERR_OK;
    }

    if (*udfBuf != NULL) {
        DbDynMemCtxFree(memCtx, *udfBuf);
        *udfBuf = NULL;
        *curBufLen = 0;
    }
    // memory will be freed unitied, see Note1
    *udfBuf = (uint8_t *)DbDynMemCtxAlloc(memCtx, newBufLen);
    if (*udfBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc buffer, len=%" PRIu32, newBufLen);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(*udfBuf, newBufLen, 0x00, newBufLen);
    *curBufLen = newBufLen;
    return GMERR_OK;
}

const char *GmUdfGetNamespaceNameImpl(GmUdfCtxT *ctx)
{
    if (ctx == NULL || ctx->nsp == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get namespace, ctx:%" PRIu32, (uint32_t)(ctx == NULL));
        return NULL;
    }
    return ctx->nsp->metaCommon.metaName;
}

Status GmUdfGetNamespaceIdImpl(GmUdfCtxT *ctx, uint32_t *namespaceId)
{
    DB_POINTER(namespaceId);
    GmUdfCtxT *udfCtx = GmUdfGetCurUdfCtx();
    if (udfCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "udfCtx null");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *namespaceId = udfCtx->nsp->metaCommon.metaId;
    return GMERR_OK;
}

void RegPlanGmUdfMemory(ImportFusionFunctionsT *fusionFunctions)
{
    DB_POINTER(fusionFunctions);
    fusionFunctions->udfMemAllocFunc = GmUdfMemAllocImpl;
    fusionFunctions->udfMemFreeFunc = GmUdfMemFreeImpl;
}

void RegPlanGmUdfInfoGetter(ImportFusionFunctionsT *fusionFunctions)
{
    DB_POINTER(fusionFunctions);
    fusionFunctions->udfGetNamespaceNameFunc = GmUdfGetNamespaceNameImpl;
    fusionFunctions->udfGetNamespaceIdFunc = GmUdfGetNamespaceIdImpl;
}

#ifdef __cplusplus
}
#endif

#endif  // FEATURE_DATALOG
