/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: IndexScan operator implementation.
 * Note1: memCtx is used in short process period , we will free all memory when finishing one request
 * Author: GMDBv5 EE Team
 * Create: 2022-7-21
 */
#include "ee_plan_state.h"
#include "ee_scan_template.h"
#include "dm_meta_prop_label.h"
#include "ee_expression_dump.h"
#include "ee_expression_hash.h"
#include "ee_plan_method_router.h"
#include "ee_plan_state_router.h"
#include "dm_data_prop.h"
#include "dm_data_record.h"
#include "db_extmem.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

static inline IndexScanT *CastToIndexScan(PlanT *plan)
{
    DB_ASSERT(plan->tagType == T_INDEX_SCAN);
    return (void *)plan;
}

static inline IndexScanStateT *CastToIndexScanState(PlanStateT *planState)
{
    DB_ASSERT(planState->tagType == T_INDEX_SCAN);
    return (void *)planState;
}

#ifdef FEATURE_SQL

static inline bool IsIndexScanCompareOpValid(ExprOpTypeE cmpType)
{
    return (cmpType != EXPR_OP_NE && IsCompOp(cmpType));
}

static Status ExecFillScanKeysAndExpr(IndexScanStateT *state, ExprT *expr, ExprT **scanKeyExpr)
{
    DB_POINTER2(state, expr);

    const ScanKeyOperatorE scanKeyOpMap[] = {
        [EXPR_OP_EQ] = SKOP_EQ,
        [EXPR_OP_LT] = SKOP_LT,
        [EXPR_OP_LE] = SKOP_LE,
        [EXPR_OP_GT] = SKOP_GT,
        [EXPR_OP_GE] = SKOP_GE,
        [EXPR_OP_NE] = SKOP_END,  // 索引扫描不支持 !=, 映射到非法值
    };

    DbMemCtxT *memCtx = state->scanState.planState.estate->memCtx;
    ExprT *keyExpr = ExprMakeTuple(memCtx, state->scanKeyNum);
    if (keyExpr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "create scan keyExpr");
        return GMERR_OUT_OF_MEMORY;
    }
    ExprArrayT *scanKeyArray = &((ExprFuncT *)(void *)keyExpr)->array;

    ExprArrayT *array = ExprGetExprArray(expr);
    for (uint32_t i = 0; i < state->scanKeyNum; i++) {
        ExprBinaryT *binary = (ExprBinaryT *)(void *)array->expr[i];
        scanKeyArray->expr[i] = binary->right;
        ExprT *left = binary->left;
        if (!IsIndexScanCompareOpValid(binary->expr.opType)) {
            DbDynMemCtxFree(memCtx, keyExpr);
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED,
                "expr opType, left(%" PRIu32 "), binary(%" PRIu32 ")", (uint32_t)left->opType,
                (uint32_t)binary->expr.opType);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
        if (left->opType == EXPR_OP_LVAR || left->opType == EXPR_OP_RVAR) {
            state->scanKey[i].propeId = CastToVar(left)->propId;
        }
        state->scanKey[i].skop = scanKeyOpMap[binary->expr.opType];
        state->dmScanKey[i] = &state->scanKey[i].value;
    }

    *scanKeyExpr = keyExpr;
    return GMERR_OK;
}

static Status ExecBuildScanKeys(IndexScanT *plan, EStateT *estate, IndexScanStateT *state)
{
    DB_POINTER3(plan, estate, state);
    Status ret = GMERR_OK;
    if (plan->indexQualExpr == NULL) {
        // it's an index scan without any filtering
        return ret;
    }

    if (plan->indexQualExpr->opType != EXPR_OP_TUPLE) {
        ret = GMERR_FEATURE_NOT_SUPPORTED;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "expr opType(%" PRIu32 ").", (uint32_t)plan->indexQualExpr->opType);
        return ret;
    }

    state->scanKeyNum = ExprGetExprArray(plan->indexQualExpr)->num;
    DbMemCtxT *memCtx = state->scanState.planState.estate->memCtx;
    size_t scanKeySize = sizeof(ScanKeyT) * state->scanKeyNum;
    // memory will be freed unitied, see Note1
    state->scanKey = DbDynMemCtxAlloc(memCtx, scanKeySize);
    if (state->scanKey == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "alloc ScanKey, scanKeyNum=%" PRIu32, state->scanKeyNum);
        return ret;
    }
    (void)memset_s(state->scanKey, scanKeySize, 0x00, scanKeySize);

    size_t dmScanKeySize = sizeof(DmValueT *) * state->scanKeyNum;
    // memory will be freed unitied, see Note1
    state->dmScanKey = DbDynMemCtxAlloc(memCtx, dmScanKeySize);
    if (state->dmScanKey == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "alloc DmValueT* array, scanKeyNum=%" PRIu32, state->scanKeyNum);
        return ret;
    }
    (void)memset_s(state->dmScanKey, dmScanKeySize, 0x00, dmScanKeySize);

    ExprT *scanKeyExpr = NULL;
    ret = ExecFillScanKeysAndExpr(state, plan->indexQualExpr, &scanKeyExpr);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ExprStateBuild(scanKeyExpr, memCtx, &state->scanKeyExpr);
}

#else

static Status ExecFillScanKeys(IndexScanStateT *state, ExprT *left)
{
    DB_POINTER2(state, left);

    ExprArrayT *array = ExprGetExprArray(left);
    for (uint32_t i = 0; i < state->scanKeyNum; i++) {
        ExprVarT *var = (void *)array->expr[i];
        if (var->expr.opType == EXPR_OP_LVAR || var->expr.opType == EXPR_OP_RVAR) {
            state->scanKey[i].uniqNodeId = 0;
            state->scanKey[i].propeId = var->propId;
            state->scanKey[i].skop = SKOP_EQ;
            state->dmScanKey[i] = &state->scanKey[i].value;
        } else if (var->expr.opType == EXPR_OP_PROPERTY) {
            ExprPropertyT *node = (void *)array->expr[i];
            state->scanKey[i].uniqNodeId = node->uniqNodeId;
            state->scanKey[i].propeId = node->propertyId;
            state->scanKey[i].skop = SKOP_EQ;
            state->dmScanKey[i] = &state->scanKey[i].value;
        } else {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_FEATURE_NOT_SUPPORTED, "opType(%" PRIu32 ").", (uint32_t)var->expr.opType);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
    }

    return GMERR_OK;
}

static Status ExecBuildScanKeys(IndexScanT *plan, EStateT *estate, IndexScanStateT *state)
{
    DB_POINTER3(plan, estate, state);
    Status ret = GMERR_OK;

    if (plan->indexQualExpr->opType != EXPR_OP_EQ) {
        ret = GMERR_FEATURE_NOT_SUPPORTED;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "expr opType(%" PRIu32 ").", (uint32_t)plan->indexQualExpr->opType);
        return ret;
    }

    ExprBinaryT *eq = (void *)plan->indexQualExpr;
    if (eq->left->opType != EXPR_OP_TUPLE || eq->right->opType != EXPR_OP_TUPLE) {
        ret = GMERR_FEATURE_NOT_SUPPORTED;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "expr opType(%" PRIu32 ")=opType(%" PRIu32 ").", (uint32_t)eq->left->opType,
            (uint32_t)eq->right->opType);
        return ret;
    }

    state->scanKeyNum = ExprGetExprArray(eq->left)->num;
    DbMemCtxT *memCtx = state->scanState.planState.estate->memCtx;
    size_t scanKeySize = sizeof(ScanKeyT) * state->scanKeyNum;
    // memory will be freed unitied, see Note1
    state->scanKey = DbDynMemCtxAlloc(memCtx, scanKeySize);
    if (state->scanKey == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "alloc ScanKey, scanKeyNum=%" PRIu32, state->scanKeyNum);
        return ret;
    }
    (void)memset_s(state->scanKey, scanKeySize, 0x00, scanKeySize);

    size_t dmScanKeySize = sizeof(DmValueT *) * state->scanKeyNum;
    // memory will be freed unitied, see Note1
    state->dmScanKey = DbDynMemCtxAlloc(memCtx, dmScanKeySize);
    if (state->dmScanKey == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "alloc DmValueT* array, scanKeyNum=%" PRIu32, state->scanKeyNum);
        return ret;
    }
    (void)memset_s(state->dmScanKey, dmScanKeySize, 0x00, dmScanKeySize);

    ret = ExecFillScanKeys(state, eq->left);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = ExprStateBuild(eq->right, estate->memCtx, &state->scanKeyExpr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "build indexScanKey ExprState.");
        return ret;
    }

    return GMERR_OK;
}
#endif

// =============== implement iterator in volcano begin===================
Status ExecExplainIndexScan(ExecExplainCtxT *explain, PlanT *plan, uint32_t level)
{
    DB_POINTER2(explain, plan);

    IndexScanT *indexScan = CastToIndexScan(plan);
    DmVertexLabelT *label = indexScan->scan.label;

    DmVlIndexLabelT *indexLabel = NULL;
    Status ret = DmGetIndexLabelById(label, indexScan->indexSeqNum, &indexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmSbAppendIndent(explain->sb, level);
    DmSbAppendWithFormat(explain->sb, "->  IndexScan on Label(%s) Using Index[%" PRIu32 "]=%s\n", DmGetYangAlias(label),
        indexScan->indexSeqNum, indexLabel->indexName);

    PlanPropDescT aaSlotDesc = {0};
    ret = CreatePlanPropDescWithSchema(explain->memCtx, label->metaVertexLabel->schema, &aaSlotDesc);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "deep Copy PlanPropDesc, labelName=%s.", label->metaCommon.metaName);
        return ret;
    }

    ExprDumpContextT ctx = {
        .leftTuple = &aaSlotDesc,
        .rightTuple = NULL,
        .pathNum = explain->pathNum,
        .pathArray = explain->pathArray,
    };
    ExecExplainScan(plan, &ctx, explain->sb, level);

    if (indexScan->indexQualExpr) {
        DmSbAppendIndent(explain->sb, level);
        DmSbAppendWithFormat(explain->sb, "      Index Key: ");
        ExprDump(indexScan->indexQualExpr, &ctx, explain->sb);
        DmSbAppend(explain->sb, "\n");
    }
    return GMERR_OK;
}

Status ExecAnalyzeIndexScan(ExecAnalyzeCtxT *analyzeCtx, PlanStateT *planState, uint32_t level)
{
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
    IndexScanStateT *indexScanState = CastToIndexScanState(planState);
    DmSbAppendIndent(analyzeCtx->sb, level);
    DmSbAppendWithFormat(analyzeCtx->sb, "->  IndexScan statistics:\n");
    DmSbAppendIndent(analyzeCtx->sb, level);
    DmSbAppendWithFormat(
        analyzeCtx->sb, "      IndexScanTime: %" PRIu64 " ms\n", indexScanState->scanState.executeTimeMs);
    return GMERR_OK;
#else
    DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Only ts using ExecAnalyzeIndexScan now.");
    return GMERR_FEATURE_NOT_SUPPORTED;
#endif
}

void ExecEndIndexScan(PlanStateT *planState)
{
    DB_POINTER(planState);

    IndexScanStateT *indexScanState = CastToIndexScanState(planState);
    if (indexScanState->scanDesc != NULL) {
        IndexEndScan(indexScanState->scanDesc);
        indexScanState->scanDesc = NULL;
    }
    DbDestroyList(&indexScanState->preFilterResult);
    ExecEndScan(&indexScanState->scanState);
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
    indexScanState->scanState.executeTimeMs = DbToMseconds(DbRdtsc() - indexScanState->scanState.beginTime);
#endif
}

static Status IndexScanGetHash(IndexScanStateT *indexScan, uint32_t *hash)
{
    DmValueT **scanKey = indexScan->dmScanKey;
    uint32_t scanKeyNum = indexScan->scanKeyNum;
    uint8_t *buf = indexScan->hashKeyBuf;
    uint32_t bufLen = 0;

    for (uint32_t i = 0; i < scanKeyNum; ++i) {
        bufLen += DmValueGetSeriLen(scanKey[i]);
        if (bufLen > indexScan->hashKeyBufLen) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_DATA_EXCEPTION, "buffer len=%" PRIu32 ", preallocLen=%" PRIu32, bufLen, indexScan->hashKeyBufLen);
            return GMERR_DATA_EXCEPTION;
        }

        Status ret = DmValueSeri2InvokerBuf(scanKey[i], buf);
        if (ret != GMERR_OK) {
            return ret;
        }
        buf = indexScan->hashKeyBuf + bufLen;
    }
    *hash = DbHash32(indexScan->hashKeyBuf, bufLen);
    (void)memset_s(indexScan->hashKeyBuf, indexScan->hashKeyBufLen, 0, indexScan->hashKeyBufLen);
    return GMERR_OK;
}

static Status IndexScanCheckHash(IndexScanStateT *indexScan)
{
    char *labelName = indexScan->scanState.label->metaCommon.metaName;
    uint32_t indexSeqNum = indexScan->scanDesc->leftIndexKey->indexId;
    DbMemCtxT *memCtx = indexScan->scanState.planState.estate->memCtx;
    Status ret = IndexScanGetHash(indexScan, &indexScan->currentHash);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get hash key, label=%s, indexSeqNum=%" PRIu32, labelName, indexSeqNum);
        return ret;
    }
    indexScan->listIdx = 0;
    DbListT *list =
        (DbListT *)DbOamapLookup(&indexScan->hashTable, indexScan->currentHash, &indexScan->currentHash, NULL);
    if (list != NULL) {
        indexScan->currentList = list;
        indexScan->needIndexScan = false;
        return GMERR_OK;
    }
    // 统一申请内存currentList和hashKey的内存
    uint8_t *buf = (uint8_t *)DbDynMemCtxAlloc(memCtx, sizeof(DbListT) + sizeof(uint32_t));
    if (buf == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_OUT_OF_MEMORY, "hashTable alloc, label=%s, indexSeqNum=%" PRIu32, labelName, indexSeqNum);
        return GMERR_OUT_OF_MEMORY;
    }
    indexScan->currentList = (DbListT *)(buf);
    DbCreateListWithExtendSize(indexScan->currentList, sizeof(DmVertexT *), 1, memCtx);
    indexScan->needIndexScan = true;
    uint32_t *hashKey = (uint32_t *)(buf + sizeof(DbListT));
    *hashKey = indexScan->currentHash;
    return DbOamapInsert(&indexScan->hashTable, *hashKey, hashKey, indexScan->currentList, NULL);
}

Status CreateIndexScanState(DbMemCtxT *memCtx, IndexScanStateT **indexScantate)
{
    DB_POINTER2(memCtx, indexScantate);

    // memory will be freed unitied, see Note1
    IndexScanStateT *newState = DbDynMemCtxAlloc(memCtx, sizeof(IndexScanStateT));
    if (newState == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "alloc SeqScanState.");
        return GMERR_OUT_OF_MEMORY;
    }

    *newState = (IndexScanStateT){0};
    newState->needIndexScan = true;
    *indexScantate = newState;
    return GMERR_OK;
}

static Status IndexScanInitHashKeyBuf(IndexScanStateT *indexScanState)
{
    DbMemCtxT *memCtx = indexScanState->scanState.planState.estate->memCtx;
    ScanKeyT *scanKey = indexScanState->scanKey;
    uint32_t keyNum = indexScanState->scanKeyNum;
    uint32_t keyLen = 0;
    DmPropertySchemaT *properties = indexScanState->scanState.label->metaVertexLabel->schema->properties;
    for (uint32_t i = 0; i < keyNum; ++i) {
        uint32_t propId = scanKey[i].propeId;
        DmPropertySchemaT *property = &properties[propId];
        DbDataTypeE propType = property->dataType;
        // bitmap is not supported in datalog
        DB_ASSERT(propType != DB_DATATYPE_BITMAP);
        keyLen += (uint32_t)sizeof(DmValueT);
        if (DM_TYPE_NEED_MALLOC(propType)) {
            keyLen += property->size;
        }
    }
    DB_ASSERT(keyLen != 0);
    indexScanState->hashKeyBufLen = keyLen;
    indexScanState->hashKeyBuf = DbDynMemCtxAlloc(memCtx, keyLen);
    if (indexScanState->hashKeyBuf == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "alloc hashKeyBuf.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(indexScanState->hashKeyBuf, keyLen, 0, keyLen);
    return GMERR_OK;
}

#define CACHE_THRESHOLD 20
static inline bool IndexScanUseCached(IndexScanStateT *indexScan, EStateT *estate)
{
    return estate->deltaCnt >= CACHE_THRESHOLD;
}

static inline void SetIndexScanDescT(IndexScanStateT *newState, IndexScanT *indexScan, EStateT *eState)
{
    ((IndexScanDescT *)newState->scanDesc)->yangDesc.filterNodeCtx = eState->filterNodeCtx;
    ((IndexScanDescT *)newState->scanDesc)->needOldTuple = eState->needOldTuple;
}

static Status ExecInitIndexScanState(EStateT *eState, PlanStateT **planState, IndexScanStateT **newState)
{
    DB_POINTER3(eState, planState, newState);
    Status ret = GMERR_OK;
    if (*planState != NULL) {
        *newState = CastToIndexScanState(*planState);
        (*newState)->scanState.planState.estate = eState;
    } else {
        ret = CreateIndexScanState(eState->memCtx, newState);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static inline void InitAutoIncIndexExtendSize(IndexScanStateT *newState)
{
#ifdef FEATURE_SQL
    if (newState->scanState.label->vertexLabelConstraint &&
        newState->scanState.label->vertexLabelConstraint->pqIndexNum > 0 &&
        newState->scanDesc->super.indexLabel->idxLabelBase.indexType == BTREE_INDEX) {
        // 能走到这一定是普通标量索引，key后存自增ID
        newState->scanDesc->super.idxCtx->extendedKeySize = AUTO_ID_SIZE;
    }
#endif
}

static void ExecInitIndexScanBeginCfg(IndexScanStateT *newState, EStateT *eState, LabelBeginCfgT *beginCfg)
{
    beginCfg->vertexLabel = newState->scanState.label;
    beginCfg->seRunCtx = eState->seInstance;
    beginCfg->eeMemCtx = eState->memCtx;
    beginCfg->trxMemCtx = eState->trxMemCtx;
    beginCfg->session = eState->session;
}

Status ExecInitIndexScan(PlanT *plan, EStateT *eState, PlanStateT **planState)
{
    DB_POINTER3(plan, eState, planState);
    IndexScanStateT *newState = NULL;
    Status ret = ExecInitIndexScanState(eState, planState, &newState);
    if (ret != GMERR_OK) {
        return ret;
    }

    // Initialize base class.
    IndexScanT *indexScan = CastToIndexScan(plan);
    newState->pathId = indexScan->scan.pathId;
    if ((ret = ExecInitScan(&indexScan->scan, eState, &newState->scanState)) != GMERR_OK) {
        goto ERROR;
    }
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
    newState->scanState.beginTime = DbRdtsc();
#endif
    if ((ret = ExecBuildScanKeys(indexScan, eState, newState)) != GMERR_OK) {
        goto ERROR;
    }
    newState->scanDirection = indexScan->scanDirection;
    LabelBeginCfgT beginCfg;
    ExecInitIndexScanBeginCfg(newState, eState, &beginCfg);
    if (newState->scanDesc != NULL) {
        IndexEndScan(newState->scanDesc);
        newState->scanDesc = NULL;
    }
    if ((ret = IndexBeginScan(beginCfg, indexScan->indexSeqNum, &newState->scanDesc)) != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "begin index scan, labelName=%s, seqNum=%" PRIu32 ".",
            indexScan->scan.label->metaCommon.metaName, indexScan->indexSeqNum);
        goto ERROR;
    }

    InitAutoIncIndexExtendSize(newState);
    newState->useCache = IndexScanUseCached(newState, eState);
    if (newState->useCache) {
        if ((ret = DbOamapInit(&newState->hashTable, 1, DbOamapUint32Compare, eState->memCtx, true)) != GMERR_OK) {
            goto ERROR;
        };
        if ((ret = IndexScanInitHashKeyBuf(newState)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "init hash key buf, labelName=%s, indexSeqNum=%" PRIu32,
                indexScan->scan.label->metaCommon.metaName, indexScan->indexSeqNum);
            goto ERROR;
        }
    }

    SetIndexScanDescT(newState, indexScan, eState);
    *planState = &newState->scanState.planState;
    return GMERR_OK;

ERROR:
    ExecEndIndexScan(&newState->scanState.planState);
    return ret;
}

Status ExecReScanIndexScan(PlanStateT *planState)
{
    DB_POINTER(planState);
    IndexScanStateT *indexScan = CastToIndexScanState(planState);
    indexScan->rescaned = false;  // 具体动作到 proc index scan 中再执行，这里只轻量化设置标记位
    return GMERR_OK;
}

static Status ExecReScanIndexScanImpl(PlanStateT *planState)
{
    IndexScanStateT *indexScan = CastToIndexScanState(planState);
    char *labelName = indexScan->scanState.label->metaCommon.metaName;
    if (indexScan->scanDesc->isEmpty) {
        return GMERR_OK;
    }
    uint32_t indexSeqNum = indexScan->scanDesc->leftIndexKey->indexId;
    Status ret;
    if (indexScan->scanKeyNum != 0) {  // 指定索引扫描但无索引上的过滤条件
        ret = ExprEvalValues(
            indexScan->scanKeyExpr, indexScan->scanState.exprContext, indexScan->dmScanKey, indexScan->scanKeyNum);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "eval expr, labelName=%s, indexSeqNum=%" PRIu32, labelName, indexSeqNum);
            return ret;
        }
    }

    if (indexScan->useCache) {
        ret = IndexScanCheckHash(indexScan);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (!indexScan->needIndexScan) {
            indexScan->rescaned = true;
            return GMERR_OK;
        }
    }
    ret = IndexReScan(indexScan->scanKey, indexScan->scanKeyNum, indexScan->scanDirection, indexScan->scanDesc);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "rescan IndexLabel.labelName=%s, indexSeqNum=%" PRIu32, labelName, indexSeqNum);
        return ret;
    }
    indexScan->rescaned = true;

    // 重构:包装成函数
    if (planState->estate->pathArray.pathArray != NULL) {
        indexScan->scanDesc->yangDesc.srcSlot = planState->estate->pathArray.pathArray[indexScan->pathId - 1];
    }

    return GMERR_OK;
}

#ifdef FEATURE_SQL

static Status ExecProcPrefilterOptBeginScan(PlanStateT *planState, AAT *aa)
{
    Status ret = GMERR_OK;
    IndexScanStateT *indexState = CastToIndexScanState(planState);
    VectorIndexScanStateT *vecIndexScanState = indexState->scanDesc->super.idxCtx->pqIndexPlanState;

    IdxKeyCmpFunc oldKeyCmp = indexState->scanDesc->super.idxCtx->idxOpenCfg.callbackFunc.keyCmp;
    if (indexState->scanState.qualExpr == NULL) {
        // 说明除索引外没有其他过滤条件, 不读heap加快获取PQ信息
        indexState->scanDesc->super.idxCtx->idxOpenCfg.callbackFunc.keyCmp = QryVertexNoCompare;
        // 提前取出来itemptr，减少拷贝,list预分配了内存，首次必不为空
        DataIdAndPqAddrT *pq = (DataIdAndPqAddrT *)DbNewListItem(&indexState->preFilterResult);
        while (ret = IndexGetNextPqInfo(indexState->scanDesc, pq), ret == GMERR_OK) {
            pq = (DataIdAndPqAddrT *)DbNewListItem(&indexState->preFilterResult);
            if (SECUREC_UNLIKELY(pq == NULL)) {
                DbClearList(&indexState->preFilterResult);
                indexState->scanDesc->super.idxCtx->idxOpenCfg.callbackFunc.keyCmp = oldKeyCmp;
                return ret;
            }
        }
        indexState->scanDesc->super.idxCtx->idxOpenCfg.callbackFunc.keyCmp = oldKeyCmp;
        DbDelListItem(&indexState->preFilterResult, indexState->preFilterResult.count - 1);
    } else {
        // 前过滤出所有数据
        while (ret = ExecProcScan(&indexState->scanState, aa), ret == GMERR_OK) {
            AASlotT *outerSlot = NULL;
            while (outerSlot = AAGetNextPropSlot(aa), outerSlot != NULL) {
                DataIdAndPqAddrT pq;
                pq.dataId = outerSlot->addr;
                pq.pqInfo = outerSlot->pqInfo;
                ret = DbAppendListItem(&indexState->preFilterResult, &pq);
                if (ret != GMERR_OK) {
                    DbClearList(&indexState->preFilterResult);
                    return ret;
                }
            }
        }
    }

    bool isNoOwnerShip = aa->prop.noOwnerShip;
    aa->prop.noOwnerShip = true;
    AAClearPropSlots(aa);
    aa->prop.noOwnerShip = isNoOwnerShip;

    // 过滤完所有数据后，将dataId和PQ信息传递给算法
    if (ret == GMERR_NO_DATA) {
        // 算法根据量化数据排序
        vecIndexScanState->vecScanPara.data = (void *)indexState->preFilterResult.items;
        vecIndexScanState->vecScanPara.dataCount = indexState->preFilterResult.count;
        ret = ExecReScanVectorIndexScanImpl((PlanStateT *)vecIndexScanState);
    }
    DbClearList(&indexState->preFilterResult);
    return ret;
}

static inline Status ExecProcPrefilterOptScan(PlanStateT *planState, AAT *aa)
{
    IndexScanStateT *indexState = CastToIndexScanState(planState);
    VectorIndexScanStateT *vecIndexScanState = indexState->scanDesc->super.idxCtx->pqIndexPlanState;
    // 将算法返回的TOPL依次取出，上层ExecProcSortBatch中会再次做精排
    return ExecProcScan(&vecIndexScanState->scanState, aa);
}

Status ExecProcIndexScan(PlanStateT *planState, AAT *aa)
{
    DB_POINTER2(planState, aa);

    IndexScanStateT *indexState = CastToIndexScanState(planState);
    if (!indexState->rescaned) {
        Status ret = ExecReScanIndexScanImpl(planState);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (indexState->scanDesc->super.idxCtx->autoIdIndexCtx) {
            ret = ExecProcPrefilterOptBeginScan(planState, aa);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }

    if (indexState->scanDesc->super.idxCtx->autoIdIndexCtx) {
        return ExecProcPrefilterOptScan(planState, aa);
    }

    return ExecProcScan(&indexState->scanState, aa);
}

Status ExecProcIndexScanRowId(PlanStateT *planState, uint64_t *rowid)
{
    DB_POINTER2(planState, rowid);

    IndexScanStateT *indexState = CastToIndexScanState(planState);
    if (!indexState->rescaned) {
        Status ret = ExecReScanIndexScanImpl(planState);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return IndexGetNextRowId(indexState->scanDesc, rowid);
}

#else

Status ExecProcIndexScan(PlanStateT *planState, AAT *aa)
{
    DB_POINTER2(planState, aa);

    IndexScanStateT *indexState = CastToIndexScanState(planState);
    if (!indexState->rescaned) {
        Status ret = ExecReScanIndexScanImpl(planState);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return ExecProcScan(&indexState->scanState, aa);
}

#endif  // FEATURE_SQL

// 事务发生提交后，一些信息被重置了，需要重新打开
Status ExecReStartIndexScan(PlanStateT *planState)
{
#ifdef FEATURE_SQL
    EStateT *estate = planState->estate;
    IndexScanStateT *indexState = CastToIndexScanState(planState);
    DmVertexLabelT *vertexLabel = indexState->scanState.label;
    DmVertexT **dmVertex = &indexState->scanDesc->super.dmVertex;
    Status ret = DmCreateEmptyVertexWithMemCtx(estate->trxMemCtx, vertexLabel, dmVertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    indexState->scanDesc->super.idxCtx->idxOpenCfg.vertex = *dmVertex;
    HeapResetTrxCtxForIndex(indexState->scanDesc->super.heapRunHdl, estate->seInstance, *dmVertex);
    return GMERR_OK;
#else
    return GMERR_OK;
#endif
}

// =============== implement iterator in volcano end===================

static Status IndexGetNextInHashTable(IndexScanStateT *indexScan, AASlotT *slot)
{
    uint32_t cnt = DbListGetItemCnt(indexScan->currentList);
    if (indexScan->listIdx >= cnt) {
        return GMERR_NO_DATA;
    }
    DmVertexT *dmVertexTmp = *(DmVertexT **)DbListItem(indexScan->currentList, indexScan->listIdx);
    Status ret = DmVertexCopy(dmVertexTmp, slot->dmVertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 由于DmVertexCopy不拷贝系统字段，此处需要额外拷贝
    ret = DmVertexCopySysProp(dmVertexTmp, slot->dmVertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    indexScan->listIdx++;
    return GMERR_OK;
}

static Status IndexScanAppendHashTable(IndexScanStateT *indexScan, AASlotT *slot)
{
    AASlotT *slotTmp = NULL;
    DbMemCtxT *memCtx = indexScan->scanState.planState.estate->memCtx;
    Status ret = NewAASlotWithVertexLabel(memCtx, indexScan->scanState.label, &slotTmp);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = AASlotCopy(slotTmp, slot);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 由于DmVertexCopy不拷贝系统字段，此处需要额外拷贝
    ret = DmVertexCopySysProp(slot->dmVertex, slotTmp->dmVertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    return DbAppendListItem(indexScan->currentList, &slotTmp->dmVertex);
}

// =============== implement template scan begin=======================
Status ExecIndexScanNext(ScanStateT *scanState, AASlotT *slot)
{
    DB_POINTER2(scanState, slot);

    IndexScanStateT *indexScan = CastToIndexScanState(&scanState->planState);
    if (!indexScan->useCache) {
        return IndexGetNext(indexScan->scanDesc, slot);
    }

    if (!indexScan->needIndexScan) {
        return IndexGetNextInHashTable(indexScan, slot);
    }
    Status ret = IndexGetNext(indexScan->scanDesc, slot);
    if (ret == GMERR_OK) {
        ret = IndexScanAppendHashTable(indexScan, slot);
    }
    return ret;
}
// =============== implement template scan end=======================
#ifdef __cplusplus
}
#endif
