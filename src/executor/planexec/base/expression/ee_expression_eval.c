/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Implementation of expression evaluation.
 * Author: GMDBv5 EE Team
 * Create: 2023-6-6
 */

#include <regex.h>
#include "adpt_string.h"
#include "dm_data_math.h"
#include "dm_data_print_inner.h"
#if defined(FEATURE_SQL) || defined(FEATURE_TS) || defined(FEATURE_STREAM)
#include "dm_data_math_sql.h"
#endif
#ifdef FEATURE_SQL
#include "dm_data_annvector.h"
#endif
#include "ee_experssion_dtl.h"
#include "ee_plan_state.h"
#include "ee_expression_eval.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

#if defined(FEATURE_SQL) || defined(FEATURE_TS) || defined(FEATURE_STREAM)

/*
 * 正则比较value1和value2
 * UNKNOWN_CMP_RESULT 表示遇到 NULL
 * cmpResult = 0 表示匹配失败
 * cmpResult = 1 表示匹配成功
 */
static Status ExprEvalValueMatch(ExprT *left, ExprT *right, ExprContextT *exprCtx, int8_t *cmpResult, bool isGlob)
{
    DB_POINTER3(left, right, cmpResult);
    DmValueT lValue = {0};
    DmValueT rValue = {0};
    Status ret = ExprEvalValue(left, exprCtx, &lValue);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (lValue.type == DB_DATATYPE_NULL) {
        goto END;
    }

    ret = ExprEvalValue(right, exprCtx, &rValue);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (rValue.type == DB_DATATYPE_NULL) {
        goto END;
    }

    bool isMatch = false;
    if (isGlob) {
        ret = DmValueMatchGlob(&lValue, &rValue, &isMatch);
    } else {
        ret = DmValueMatchLike(&lValue, &rValue, &isMatch);
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    *cmpResult = isMatch ? 1 : 0;
    return GMERR_OK;
END:
    *cmpResult = UNKNOWN_CMP_RESULT;
    return GMERR_OK;
}

Status ExprEvalLike(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprBinaryT *like = CastToBinary(expr);
    ExprT *left = like->left;
    ExprT *right = like->right;

    int8_t result = 0;
    Status ret = ExprEvalValueMatch(left, right, exprCtx, &result, false);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (result == UNKNOWN_CMP_RESULT) {
        *value = TRILEAN_NULL;
        return GMERR_OK;
    }
    *value = result == 0 ? TRILEAN_FALSE : TRILEAN_TRUE;
    return GMERR_OK;
}

static Status ExprEvalInOperator(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    // 右孩子必须为tuple.
    ExprBinaryT *exprBin = CastToBinary(expr);
    if (exprBin->right->opType != EXPR_OP_TUPLE) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Inexpr's right child");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    // 获取右孩子的列表
    ExprArrayT *rightTuple = &(CastToFunc(exprBin->right)->array);
    uint32_t num = rightTuple->num;

    DmValueT lValue = {0};
    Status ret = ExprEvalValue(exprBin->left, exprCtx, &lValue);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (lValue.type == DB_DATATYPE_NULL && num != 0) {
        *value = TRILEAN_NULL;
        return GMERR_OK;
    }

    bool isMatch = false;
    bool hasNull = false;
    DmValueT rValue = {0};
    for (uint32_t i = 0; i < num; i++) {
        // 遍历右孩子
        ExprT *rightExpr = rightTuple->expr[i];
        ret = ExprEvalValue(rightExpr, exprCtx, &rValue);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        if (rValue.type == DB_DATATYPE_NULL) {
            hasNull = true;  // in/not in 遇到 NULL 标记一下，仅在无法在右操作数列表中匹配到左值时才返回 NULL
            continue;
        }
        // 调用比较方法
        ret = DmValueIsEqualDiffType(&lValue, &rValue, &isMatch);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        // 一旦匹配成功，返回
        if (isMatch) {
            break;
        }
    }
    if (hasNull && !isMatch) {
        *value = TRILEAN_NULL;
        return GMERR_OK;
    }
    isMatch = (expr->opType == (uint8_t)EXPR_OP_IN) ? (isMatch) : !(isMatch);
    *value = isMatch ? TRILEAN_TRUE : TRILEAN_FALSE;
    return GMERR_OK;
}
#endif

#ifdef FEATURE_SQL
Status ExprEvalNotLike(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprBinaryT *like = CastToBinary(expr);
    ExprT *left = like->left;
    ExprT *right = like->right;

    int8_t result = 0;
    Status ret = ExprEvalValueMatch(left, right, exprCtx, &result, false);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (result == UNKNOWN_CMP_RESULT) {
        *value = TRILEAN_NULL;
        return GMERR_OK;
    }
    *value = result == 0 ? TRILEAN_TRUE : TRILEAN_FALSE;
    return GMERR_OK;
}

Status ExprEvalGlob(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprBinaryT *like = CastToBinary(expr);
    ExprT *left = like->left;
    ExprT *right = like->right;

    int8_t result = 0;
    Status ret = ExprEvalValueMatch(left, right, exprCtx, &result, true);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (result == UNKNOWN_CMP_RESULT) {
        *value = TRILEAN_NULL;
        return GMERR_OK;
    }
    *value = result == 0 ? TRILEAN_FALSE : TRILEAN_TRUE;
    return GMERR_OK;
}

Status ExprEvalNotGlob(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprBinaryT *like = CastToBinary(expr);
    ExprT *left = like->left;
    ExprT *right = like->right;

    int8_t result = 0;
    Status ret = ExprEvalValueMatch(left, right, exprCtx, &result, true);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (result == UNKNOWN_CMP_RESULT) {
        *value = TRILEAN_NULL;
        return GMERR_OK;
    }
    *value = result == 0 ? TRILEAN_TRUE : TRILEAN_FALSE;
    return GMERR_OK;
}

static Status ExprEvalStrConcat(DmValueT *left, DmValueT *right, DbMemCtxT *memCtx, DmValueT *outStr)
{
    DB_POINTER4(left, right, outStr, memCtx);

    DmValueT tmpValue = {0};
    Status ret = DmValueConcat(left, right, &tmpValue, memCtx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    outStr->value = tmpValue.value;
    outStr->type = tmpValue.type;
    return GMERR_OK;
}

static Status ExprEvalIsResult(ExprT *left, ExprT *right, ExprContextT *exprCtx, bool hasNot, TrileanE *cmpResult)
{
    DB_POINTER3(left, right, cmpResult);
    DmValueT lValue = {0};
    DmValueT rValue = {0};
    Status ret = ExprEvalValue(left, exprCtx, &lValue);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = ExprEvalValue(right, exprCtx, &rValue);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    bool isMatch = false;
    ret = DmValueIsLogicallyEqual(&lValue, &rValue, &isMatch);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // is匹配且没取反，is未匹配取反了
    *cmpResult = isMatch ^ hasNot ? TRILEAN_TRUE : TRILEAN_FALSE;
    return GMERR_OK;
}

Status ExprEvalIs(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprBinaryT *is = CastToBinary(expr);
    ExprT *left = is->left;
    ExprT *right = is->right;

    return ExprEvalIsResult(left, right, exprCtx, false, value);
}

Status ExprEvalIsNot(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprBinaryT *is = CastToBinary(expr);
    ExprT *left = is->left;
    ExprT *right = is->right;

    return ExprEvalIsResult(left, right, exprCtx, true, value);
}
#endif

static Status ExprEvalBool(ExprT *expr, ExprContextT *exprCtx, TrileanE *value);

Status ExprStateBuildDeepCopy(ExprT *tree, DbMemCtxT *memCtx, ExprStateT **state)
{
    DB_POINTER3(tree, memCtx, state);
    // memory will be freed unitied , see Note1
    ExprStateT *newState = DbDynMemCtxAlloc(memCtx, sizeof(ExprStateT));
    if (newState == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "ExprState alloc");
        return GMERR_OUT_OF_MEMORY;
    }
    ExprT *tempExpr = ExprCopy(memCtx, tree);
    *newState = (ExprStateT){.expr = tempExpr};
    *state = newState;
    return GMERR_OK;
}

Status ExprStateBuild(ExprT *tree, DbMemCtxT *memCtx, ExprStateT **state)
{
    DB_POINTER3(tree, memCtx, state);
    // memory will be freed unitied , see Note1
    ExprStateT *newState = DbDynMemCtxAlloc(memCtx, sizeof(ExprStateT));
    if (newState == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "ExprState alloc");
        return GMERR_OUT_OF_MEMORY;
    }
    *newState = (ExprStateT){.expr = tree};
    *state = newState;
    return GMERR_OK;
}

Status ExprContextBuild(DbMemCtxT *memCtx, DmValueT para[], uint32_t paraNum, ExprContextT **ctx)
{
    DB_POINTER2(memCtx, ctx);
    // memory will be freed unitied , see Note1
    ExprContextT *newCtx = DbDynMemCtxAlloc(memCtx, sizeof(ExprContextT));
    if (newCtx == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "ExprContext alloc");
        return GMERR_OUT_OF_MEMORY;
    }

    *newCtx = (ExprContextT){.memCtx = memCtx, .paraNum = paraNum, .para = para};

    *ctx = newCtx;
    return GMERR_OK;
}

static uint32_t GetIntValLen(int64_t val)
{
    int64_t tmpVal = val;
    if (tmpVal == 0) {
        return 1;
    }
    uint32_t count = 0;
    while (tmpVal != 0) {
        count++;
        tmpVal /= TEN;
    }

    // 负数多个负号
    return val < 0 ? count + 1 : count;
}

static char *ConvertInt642Str(DbMemCtxT *memCtx, int64_t longValue)
{
    uint32_t len = GetIntValLen(longValue) + 1;
    char *str = DbDynMemCtxAlloc(memCtx, len);
    if (str == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "int64Tostr buf alloc");
        return NULL;
    }
    memset_s(str, len, 0, len);
    int32_t err = sprintf_s(str, len, "%" PRId64 "", longValue);
    if (err < 0) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "int64 snprintf, len:%" PRIu32 ", int64=%" PRId64, len, longValue);
        return NULL;
    }
    return str;
}

Status ExprEvalColSetValue(ExprT *expr, ExprContextT *exprCtx, DmValueT *dmValue)
{
    DmValueT intValue = {0};
    Status ret = ExprEvalValue(expr, exprCtx, &intValue);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 将colset列值的类型转化为str
    char *str = ConvertInt642Str(exprCtx->memCtx, intValue.value.longValue);
    if (str == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    dmValue->type = DB_DATATYPE_STRING;
    dmValue->value.strAddr = str;
    dmValue->value.length = (uint32_t)strlen(str) + 1;
    return GMERR_OK;
}

Status ExprEvalValue(ExprT *expr, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER3(expr, exprCtx, value);

    switch (expr->opType) {
        case EXPR_OP_LVAR:
        case EXPR_OP_RVAR:
#ifdef FEATURE_SQL
        case EXPR_OP_NVAR:
        case EXPR_OP_OVAR:
#endif
            return ExprEvalVar(expr, exprCtx, value);
        case EXPR_OP_CONST:
            return ExprEvalConst(expr, value);
        case EXPR_OP_PARA:
            return ExprEvalPara(expr, exprCtx, value);
        case EXPR_OP_AND:
        case EXPR_OP_OR:
        case EXPR_OP_ADD:
        case EXPR_OP_SUB:
        case EXPR_OP_MULTI:
        case EXPR_OP_DIV:
        case EXPR_OP_MOD:
#ifdef FEATURE_SQL
        case EXPR_OP_VECTOR_DIST_L2:
        case EXPR_OP_VECTOR_DIST_COSINE:
        case EXPR_OP_BITAND:
        case EXPR_OP_BITOR:
        case EXPR_OP_LSHIFT:
        case EXPR_OP_RSHIFT:
        case EXPR_OP_CONCAT:
#endif
            return ExprEvalBinary(expr, exprCtx, value);
        case EXPR_OP_CASE:
            return ExprEvalCase(expr, exprCtx, value);
        case EXPR_OP_SIGN:
            return ExprEvalSign(expr, exprCtx, value);
        case EXPR_OP_NEGATIVE:
            return ExprEvalNegative(expr, exprCtx, value);
        case EXPR_OP_MIN:
        case EXPR_OP_MAX:
        case EXPR_OP_COUNT:
        case EXPR_OP_SUM:
            return ExprEvalAgg(expr, value);
        case EXPR_OP_AVG:
            return ExprEvalAvg(expr, value);
#ifdef FEATURE_YANG
        case EXPR_OP_PROPERTY:
            return ExprEvalProperty(expr, exprCtx, value);
        case EXPR_OP_NODE:
            return ExprEvalNode(expr, exprCtx, value);
        case EXPR_OP_CHILDISH_NODE:
            return ExprEvalChildishNode(expr, exprCtx, value);
        case EXPR_OP_SUBPLAN:
            return ExprEvalSubPlan(expr, exprCtx, value);
        case EXPR_OP_STRING:
            return ExprEvalString(expr, exprCtx, value);
        case EXPR_OP_NUMBER:
            return ExprEvalNumber(expr, exprCtx, value);
        case EXPR_OP_TRANSLATE:
            return ExprEvalTranslate(expr, exprCtx, value);
        case EXPR_OP_STARTSWITH:
            return ExprEvalStartsWith(expr, exprCtx, value);
        case EXPR_OP_DERIVEDFROM:
        case EXPR_OP_DERIVEDFROMORSELF:
            return ExprEvalDerivedFrom(expr, exprCtx, value);
        case EXPR_OP_CONTAINS:
            return ExprEvalContains(expr, exprCtx, value);
        case EXPR_OP_SUBSTRING:
            return ExprEvalSubString(expr, exprCtx, value);
        case EXPR_OP_BOOLEAN:
            return ExprEvalBoolean(expr, exprCtx, value);
        case EXPR_OP_SUBSTRINGBEFORE:
            return ExprEvalSubStringBefore(expr, exprCtx, value);
        case EXPR_OP_SUBSTRINGAFTER:
            return ExprEvalSubStringAfter(expr, exprCtx, value);
        case EXPR_OP_STRINGLENGTH:
            return ExprEvalStringLength(expr, exprCtx, value);
        case EXPR_FUNC_CONCAT:
            return ExprEvalYangStringConcat(expr, exprCtx, value);
        case EXPR_OP_REMATCH:
            return ExprEvalReMatch(expr, exprCtx, value);
        case EXPR_OP_ENUMVALUE:
            return ExprEvalEnumValue(expr, exprCtx, value);
#endif
        case EXPR_OP_LAST:
        case EXPR_OP_POSITION:
        case EXPR_OP_NAME:
        case EXPR_OP_NORMALIZESPACE:
        case EXPR_OP_LANG:
        case EXPR_OP_FLOOR:
        case EXPR_OP_DEREF:
        case EXPR_OP_BITISSET:
        case EXPR_OP_LOCALNAME:
        case EXPR_OP_CEILING:
        case EXPR_OP_ROUND:
            return ExprEvalNotSupported(expr, exprCtx, value);
#ifdef FEATURE_SQL
#ifndef IDS_HAOTIAN
        case EXPR_OP_DATE:
        case EXPR_OP_TIME:
        case EXPR_OP_DATETIME:
            return ExprEvalDateTime(expr, exprCtx, value);
        case EXPR_OP_STRFTIME:
            return ExprEvalStrfTime(expr, exprCtx, value);
        case EXPR_OP_JULIANDAY:
            return ExprEvalJulianDay(expr, exprCtx, value);
        case EXPR_OP_RANDOM:
            return ExprEvalGetRandom(expr, value);
#else
        case EXPR_OP_DATE:
        case EXPR_OP_TIME:
        case EXPR_OP_DATETIME:
        case EXPR_OP_STRFTIME:
        case EXPR_OP_JULIANDAY:
        case EXPR_OP_RANDOM:
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "not support,opType=%" PRIu32, (uint32_t)expr->opType);
            return GMERR_FEATURE_NOT_SUPPORTED;
#endif
        case EXPR_OP_ABS:
            return ExprEvalAbs(expr, exprCtx, value);
        case EXPR_OP_UPPER:
            return ExprEvalUpperOrLower(expr, exprCtx, value, true);
        case EXPR_OP_LOWER:
            return ExprEvalUpperOrLower(expr, exprCtx, value, false);
        case EXPR_OP_SUBSTR:
            return ExprEvalSubstr(expr, exprCtx, value);
#ifndef IDS_HAOTIAN
        case EXPR_OP_SUBQUERY_EXISTS:
        case EXPR_OP_SUBQUERY_ANY:
        case EXPR_OP_SUBQUERY_COMPARE:
            return ExprEvalSubQuery(expr, exprCtx, value);
#endif
#endif
#ifdef FEATURE_STREAM
        case EXPR_OP_STREAMREF:
            return ExprEvalStreamRef(expr, exprCtx, value);
        case EXPR_OP_STREAMFORMAT:
            return ExprEvalStreamFormat(expr, exprCtx, value);
        case EXPR_OP_CURRENTTIMESECOND:
            return ExprEvalStreamCurrentTimeSecond(expr, exprCtx, value);
        case EXPR_OP_HOSTNAME:
            return ExprEvalStreamGetHostname(expr, exprCtx, value);
        case EXTD_EXPR_OP_SEQDISTINCTCOUNT:
            return ExprEvalSeqDistctCnt(expr, exprCtx, value);
        case EXTD_EXPR_OP_ROWNUMBER:
            return ExprEvalRowNumber(expr, exprCtx, value);
#endif
#if defined(FEATURE_TS) || defined(FEATURE_SQL) || defined(FEATURE_STREAM)
        case EXPR_OP_LENGTH:
            return ExprEvalLength(expr, exprCtx, value);
        case EXTD_EXPR_OP_MAX:
            return ExprEvalExtdMaxMin(expr, exprCtx, value, -1);
        case EXTD_EXPR_OP_MIN:
            return ExprEvalExtdMaxMin(expr, exprCtx, value, 1);
        case EXTD_EXPR_OP_COUNT:
            return ExprEvalExtdCount(expr, exprCtx, value);
        case EXTD_EXPR_OP_AVG:
        case EXTD_EXPR_OP_SUM:
            return ExprEvalExtdSum(expr, exprCtx, value);
        case EXTD_EXPR_OP_FIRST:
            return ExprEvalExtdFirst(expr, exprCtx, value);
        case EXTD_EXPR_OP_LAST:
            return ExprEvalExtdLast(expr, exprCtx, value);
        case EXTD_EXPR_OP_COLSET:
            return ExprEvalExtdColSet(expr, exprCtx, value);
#endif
        default: {
            TrileanE val = TRILEAN_NULL;
            Status ret = ExprEvalBool(expr, exprCtx, &val);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_AND_SET_LASERR(ret, "eval exprVal, opType=%" PRIu32, (uint32_t)expr->opType);
                return ret;
            }
            Trilean2DmValue(val, value);
            return GMERR_OK;
        }
    }
}

static Status ExprEvalSetPara(ExprT *expr, ExprContextT *exprCtx)
{
    DB_POINTER2(expr, exprCtx);
    const ExprSetParaT *setPara = CastToSetPara(expr);
    uint32_t startParaId = setPara->startParaId;
    ExprArrayT *array = setPara->array;
    for (uint32_t i = 0; i < setPara->array->num; i++) {
        DmValueT *value = &exprCtx->para[startParaId + i];
        Status ret = ExprEvalValue(array->expr[i], exprCtx, value);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }

    return GMERR_OK;
}

static Status ExprEvalResult(ExprT *expr, ExprContextT *exprCtx)
{
    DB_POINTER2(expr, exprCtx);

    ExprArrayT *tuple = &CastToFunc(expr)->array;
    for (uint32_t i = 0; i < tuple->num; i++) {
        DmValueT value = {0};
        Status ret = ExprEvalValue(tuple->expr[i], exprCtx, &value);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        ret = AASlotSetPrope(exprCtx->resultTuple, i, value);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "set property, propId=%" PRIu32, i);
            return ret;
        }
    }

    return GMERR_OK;
}

static Status ExprEvalDtlReservedCount(ExprCountTypeE countType, ExprContextT *exprCtx)
{
    switch (countType) {
        case EMPTY_COUNT:
            return GMERR_OK;
        case LEFT_SIGN_COUNT:
            return AASlotSignAndSetDtlCount(exprCtx->resultTuple, exprCtx->leftTuple);
        case RIGHT_SIGN_COUNT:
            return AASlotSignAndSetDtlCount(exprCtx->resultTuple, exprCtx->rightTuple);
        case MULTI_COUNT:
            return AASlotMultiAndSetDtlCount(exprCtx->leftTuple, exprCtx->rightTuple, exprCtx->resultTuple);
        case NEGATIVE_MULTI_COUNT:
            return AASlotNegativeMultiAndSetDtlCount(exprCtx->leftTuple, exprCtx->rightTuple, exprCtx->resultTuple);
        default:
            DB_LOG_AND_SET_LASERR(
                GMERR_FEATURE_NOT_SUPPORTED, "calc expr DtlReservedCount, opType:%" PRIu32, (uint32_t)countType);
            return GMERR_FEATURE_NOT_SUPPORTED;
    }
}

static Status ExprEvalDtlProj(ExprT *expr, ExprContextT *exprCtx)
{
    DB_POINTER2(expr, exprCtx);
    Status ret = GMERR_OK;
    ExprDtlProjT *dtlProj = CastToDtlProj(expr);
    AASlotT *dst = exprCtx->resultTuple;
    if (dtlProj->leftProjLen > 0) {
        ret = AASlotProjectProperty(exprCtx->leftTuple, dst, dtlProj->leftProjArray, dtlProj->leftProjLen);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    if (dtlProj->rightProjLen > 0) {
        ret = AASlotProjectProperty(exprCtx->rightTuple, dst, dtlProj->rightProjArray, dtlProj->rightProjLen);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    for (uint32_t i = 0; i < dtlProj->constProjLen; i++) {
        ret = AASlotSetPrope(dst, dtlProj->constProjArray[i].idx, dtlProj->constProjArray[i].constValue);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }

    return ExprEvalDtlReservedCount(dtlProj->countType, exprCtx);
}

static Status ExprEvalSetField(ExprT *expr, ExprContextT *exprCtx)
{
    DB_POINTER2(expr, exprCtx);

    ExprSetFieldT *setField = CastToSetField(expr);
    DmValueT value = {0};
    Status ret = ExprEvalValue(setField->func, exprCtx, &value);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = AASlotSetPrope(exprCtx->leftTuple, setField->propId, value);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "set property, propId=%" PRIu32, setField->propId);
        return ret;
    }

    return GMERR_OK;
}

Status ExprEval(ExprStateT *state, ExprContextT *exprCtx)
{
    DB_POINTER2(state, exprCtx);

    switch (state->expr->opType) {
        case EXPR_OP_SET_PARA:
            return ExprEvalSetPara(state->expr, exprCtx);
        case EXPR_OP_TUPLE:
            return ExprEvalResult(state->expr, exprCtx);
        case EXPR_OP_SET_FIELD:
            return ExprEvalSetField(state->expr, exprCtx);
        case EXPR_OP_DTL_PROJ:
            return ExprEvalDtlProj(state->expr, exprCtx);
        default:
            return ExprEvalValue(state->expr, exprCtx, &exprCtx->resultTuple->dmValue);
    }
}

Status ExprEvalValues(ExprStateT *state, ExprContextT *exprCtx, DmValueT *values[], uint32_t num)
{
    DB_POINTER3(state, exprCtx, values);
    Status ret = GMERR_OK;

    if (state->expr->opType != EXPR_OP_TUPLE) {
        DB_LOG_AND_SET_LASERR(
            GMERR_FEATURE_NOT_SUPPORTED, "calc exprVals, opType:%" PRIu32, (uint32_t)state->expr->opType);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    ExprArrayT *tuple = &CastToFunc(state->expr)->array;

    uint32_t min = tuple->num < num ? tuple->num : num;
    for (uint32_t i = 0; i < min; i++) {
        if (tuple->expr[i] == NULL) {
            ret = GMERR_DATA_EXCEPTION;
            DB_LOG_AND_SET_LASERR(ret, "expr NULL, idx=%" PRIu32, i);
            return ret;
        }

        ret = ExprEvalValue(tuple->expr[i], exprCtx, values[i]);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }

    return GMERR_OK;
}

NO_INLINE
Status ExprEvalSign(ExprT *expr, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER2(expr, value);
    ExprFuncT *sign = CastToFunc(expr);
    Status ret = ExprEvalValue(sign->array.expr[0], exprCtx, value);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    switch (value->type) {
        case DB_DATATYPE_INT8:
            ExprEvalSign8(value);
            break;
        case DB_DATATYPE_INT16:
            ExprEvalSign16(value);
            break;
        case DB_DATATYPE_INT32:
            ExprEvalSign32(value);
            break;
        case DB_DATATYPE_INT64:
            ExprEvalSign64(value);
            break;
        case DB_DATATYPE_FLOAT:
            ExprEvalSignFloat(value);
            break;
        case DB_DATATYPE_DOUBLE:
            ExprEvalSignDouble(value);
            break;
        default:
            DB_LOG_AND_SET_LASERR(
                GMERR_FEATURE_NOT_SUPPORTED, "calc signExpr, dataType:%" PRIu32, (uint32_t)value->type);
            return GMERR_FEATURE_NOT_SUPPORTED;
    }

    return GMERR_OK;
}

NO_INLINE
Status ExprEvalNegative(ExprT *expr, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER2(expr, value);

    DmValueT orgValue = {};
    ExprFuncT *neg = CastToFunc(expr);
    Status ret = ExprEvalValue(neg->array.expr[0], exprCtx, &orgValue);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = DmValueNegative(&orgValue, value);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "multiply -1");
        return ret;
    }
    return GMERR_OK;
}

#ifdef FEATURE_YANG
static DbDataTypeE GetUpdatedNumValueTypeForBinary(DmValueT *left, DmValueT *right)
{
    if (left->type == right->type) {
        return left->type;
    }
    if (!DmIsNumberType(left->type) || !DmIsNumberType(right->type)) {
        // 非数值类型不处理类型提升
        return DB_DATATYPE_NULL;
    }
    if (DmIsFloatOrDoubleType(left->type) || DmIsFloatOrDoubleType(right->type)) {
        return DB_DATATYPE_DOUBLE;
    }
    return DB_DATATYPE_INT64;
}

static Status ConvertDmValueForEval(DmValueT *left, DmValueT *right)
{
    // 处理数值类型节点的转换，如果非数值类型则不处理
    // 如果数据类型相同，且能直接获得实际值则不操作
    DB_ASSERT(!DmIsYangUnionType(left->type) && !DmIsYangEmptyType(left->type));
    DB_ASSERT(!DmIsYangUnionType(right->type) && !DmIsYangEmptyType(right->type));
    if (left->type == right->type) {
        return GMERR_OK;
    }
    DbDataTypeE type = GetUpdatedNumValueTypeForBinary(left, right);
    if (type != DB_DATATYPE_NULL) {
        Status ret = DmValueConvert(left, type);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DmValueConvert(right, type);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        // 类型未提升且类型不相等，则说说明无法计算，返回NULL
        DB_LOG_WARN(GMERR_DATATYPE_MISMATCH, "convert type, left:%s, right:%s", DbGetDataTypeName(left->type),
            DbGetDataTypeName(right->type));
        DmValueT nullValue = {.type = DB_DATATYPE_NULL};
        *left = nullValue;
        *right = nullValue;
    }
    return GMERR_OK;
}
#endif

/*
 * 比较value1和value2的大小
 * 若大于，cmpResult返回1
 * 若等于，cmpResult返回0
 * 若小于，cmpResult返回-1
 * 若有操作数为null，cmpResult返回-2
 */
static Status ExprEvalValueCompare(ExprT *left, ExprT *right, ExprContextT *exprCtx, int8_t *cmpResult)
{
    DB_POINTER3(left, right, cmpResult);
    DmValueT lValue = {0};
    DmValueT rValue = {0};
    Status ret = ExprEvalValue(left, exprCtx, &lValue);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (lValue.type == DB_DATATYPE_NULL || lValue.type == DB_DATATYPE_PLACE_HOLDER) {
        goto END;
    }

    ret = ExprEvalValue(right, exprCtx, &rValue);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (rValue.type == DB_DATATYPE_NULL || rValue.type == DB_DATATYPE_PLACE_HOLDER) {
        goto END;
    }
#ifdef FEATURE_YANG
    ret = ConvertDmValueForEval(&lValue, &rValue);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (lValue.type == DB_DATATYPE_NULL || rValue.type == DB_DATATYPE_NULL) {
        goto END;
    }
#endif
    return DmValueCompareWithTypeConversion(&lValue, &rValue, cmpResult);

END:
    *cmpResult = UNKNOWN_CMP_RESULT;
    return GMERR_OK;
}

// 目前只有datalog用到tuple比较，保持原有逻辑，该函数只返回true/false，不会返回null
static Status ExprEvalTupleEQ(ExprT *left, ExprT *right, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(left, right, value);
    ExprArrayT *leftTuple = &CastToFunc(left)->array;
    ExprArrayT *rightTuple = &CastToFunc(right)->array;
    DB_POINTER2(leftTuple, rightTuple);
    if (leftTuple->num != rightTuple->num) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "ExprEvalTupleEQ: leftNum=%" PRIu32 ", rightNum=%" PRIu32,
            leftTuple->num, rightTuple->num);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    uint32_t num = leftTuple->num;
    int8_t result = 0;
    for (uint32_t i = 0; i < num; i++) {
        ExprT *lExpr = leftTuple->expr[i];
        ExprT *rExpr = rightTuple->expr[i];

        Status ret = ExprEvalValueCompare(lExpr, rExpr, exprCtx, &result);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }

        if (result != 0) {
            *value = TRILEAN_FALSE;
            return GMERR_OK;
        };
    }

    *value = TRILEAN_TRUE;
    return GMERR_OK;
}

static Status ExprEvalDtlEQ(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprDtlEqualT *dtlEqual = CastToDtlEQ(expr);
    bool result =
        AASlotProjectPropertyIsSame(exprCtx->leftTuple, exprCtx->rightTuple, dtlEqual->equalArray, dtlEqual->equalLen);
    *value = Bool2Trilean(result);
    return GMERR_OK;
}

static Status ExprEvalEQ(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprBinaryT *equal = CastToBinary(expr);
    ExprT *left = equal->left;
    ExprT *right = equal->right;
    DB_POINTER2(left, right);

    if (left->opType == EXPR_OP_TUPLE && right->opType == EXPR_OP_TUPLE) {
        return ExprEvalTupleEQ(left, right, exprCtx, value);
    }

    int8_t result = 0;
    Status ret = ExprEvalValueCompare(left, right, exprCtx, &result);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (result == UNKNOWN_CMP_RESULT) {
        *value = TRILEAN_NULL;
        return GMERR_OK;
    }
    *value = result == 0;
    return GMERR_OK;
}

static Status ExprEvalNE(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    Status ret = GMERR_OK;
    ExprBinaryT *equal = CastToBinary(expr);
    ExprT *left = equal->left;
    ExprT *right = equal->right;
    DB_POINTER2(left, right);

    if (left->opType == EXPR_OP_TUPLE && right->opType == EXPR_OP_TUPLE) {
        TrileanE result = TRILEAN_NULL;
        ret = ExprEvalTupleEQ(left, right, exprCtx, &result);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        // ExprEvalTupleEQ只会返回true或false
        *value = (result == TRILEAN_TRUE) ? TRILEAN_FALSE : TRILEAN_TRUE;
        return GMERR_OK;
    }

    int8_t result = 0;
    ret = ExprEvalValueCompare(left, right, exprCtx, &result);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (result == UNKNOWN_CMP_RESULT) {
        *value = TRILEAN_NULL;
        return GMERR_OK;
    }
    *value = result != 0;
    return GMERR_OK;
}

static Status ExprEvalGT(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprBinaryT *greater = CastToBinary(expr);
    ExprT *left = greater->left;
    ExprT *right = greater->right;
    DB_POINTER2(left, right);

    int8_t result = 0;
    Status ret = ExprEvalValueCompare(left, right, exprCtx, &result);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (result == UNKNOWN_CMP_RESULT) {
        *value = TRILEAN_NULL;
        return GMERR_OK;
    }
    *value = result > 0;
    return GMERR_OK;
}

static Status ExprEvalLT(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprBinaryT *less = CastToBinary(expr);
    ExprT *left = less->left;
    ExprT *right = less->right;
    DB_POINTER2(left, right);

    int8_t result = 0;
    Status ret = ExprEvalValueCompare(left, right, exprCtx, &result);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (result == UNKNOWN_CMP_RESULT) {
        *value = TRILEAN_NULL;
        return GMERR_OK;
    }
    *value = result < 0;
    return GMERR_OK;
}

static Status ExprEvalGE(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprBinaryT *greaterEqual = CastToBinary(expr);
    ExprT *left = greaterEqual->left;
    ExprT *right = greaterEqual->right;
    DB_POINTER2(left, right);

    int8_t result = 0;
    Status ret = ExprEvalValueCompare(left, right, exprCtx, &result);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (result == UNKNOWN_CMP_RESULT) {
        *value = TRILEAN_NULL;
        return GMERR_OK;
    }
    *value = result >= 0;
    return GMERR_OK;
}

static Status ExprEvalLE(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprBinaryT *lessEqual = CastToBinary(expr);
    ExprT *left = lessEqual->left;
    ExprT *right = lessEqual->right;
    DB_POINTER2(left, right);

    int8_t result = 0;
    Status ret = ExprEvalValueCompare(left, right, exprCtx, &result);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (result == UNKNOWN_CMP_RESULT) {
        *value = TRILEAN_NULL;
        return GMERR_OK;
    }
    *value = result <= 0;
    return GMERR_OK;
}

/*
 * A and B| true  |  false | null
 * -------|-------|--------|------
 *  true  | true  | false  | null
 *  false | false | false  | false
 *  null  | null  | false  | null
 */
static Status ExprEvalAnd(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    Status ret;

    ExprBinaryT *binary = CastToBinary(expr);
    ret = ExprEvalBool(binary->left, exprCtx, value);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 左操作数为false，则结果一定为false
    if (*value == TRILEAN_FALSE) {
        return GMERR_OK;
    }

    // 左操作数为true，则结果取决于右操作数
    if (*value == TRILEAN_TRUE) {
        return ExprEvalBool(binary->right, exprCtx, value);
    }

    // 左操作数为NULL，则右操作数为false的时候结果为false，否则结果为NULL
    ret = ExprEvalBool(binary->right, exprCtx, value);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    *value = (*value == TRILEAN_FALSE) ? TRILEAN_FALSE : TRILEAN_NULL;
    return ret;
}

/*
 * A or B | true  |  false | null
 * -------|-------|--------|------
 *  true  | true  | true   | true
 *  false | true  | false  | null
 *  null  | true  | null   | null
 */
Status ExprEvalOr(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    Status ret;

    ExprBinaryT *binary = CastToBinary(expr);
    ret = ExprEvalBool(binary->left, exprCtx, value);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 左操作数为true，则结果一定为true
    if (*value == TRILEAN_TRUE) {
        return GMERR_OK;
    }

    // 左操作数为false，则结果取决于右操作数
    if (*value == TRILEAN_FALSE) {
        return ExprEvalBool(binary->right, exprCtx, value);
    }

    // 左操作数为NULL，则右操作数为true的时候结果为true，否则结果为NULL
    ret = ExprEvalBool(binary->right, exprCtx, value);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    *value = (*value == TRILEAN_TRUE) ? TRILEAN_TRUE : TRILEAN_NULL;
    return ret;
}

/*
 * NOT   | result
 * ------|--------
 * true  | false
 * false | true
 * null  | null
 */
Status ExprEvalNot(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    Status ret;

    ExprUnaryT *unary = CastToUnary(expr);
    TrileanE child = TRILEAN_NULL;
    ret = ExprEvalBool(unary->child, exprCtx, &child);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (child == TRILEAN_NULL) {
        *value = TRILEAN_NULL;
        return GMERR_OK;
    }
    *value = ((child == TRILEAN_TRUE) ? TRILEAN_FALSE : TRILEAN_TRUE);
    return GMERR_OK;
}

Status ExprEvalIsNotNull(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    Status ret;

    ExprUnaryT *unary = CastToUnary(expr);
    DmValueT child = {0};
    ret = ExprEvalValue(unary->child, exprCtx, &child);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    *value = (child.type != DB_DATATYPE_NULL);  // 只会返回true或false
    return GMERR_OK;
}

Status ExprEvalIsNull(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    Status ret;

    ExprUnaryT *unary = CastToUnary(expr);
    DmValueT child = {0};
    ret = ExprEvalValue(unary->child, exprCtx, &child);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    *value = (child.type == DB_DATATYPE_NULL);  // 只会返回true或false
    return GMERR_OK;
}

Status ExprEvalIsTrue(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    Status ret;

    ExprUnaryT *unary = CastToUnary(expr);
    TrileanE val = TRILEAN_NULL;
    ret = ExprEvalBool(unary->child, exprCtx, &val);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    *value = (val == TRILEAN_TRUE);  // 只会返回true或false
    return GMERR_OK;
}

#if defined(FEATURE_TS) || defined(FEATURE_SQL)

// 计算 EXPR_OP_TUPLE 类型的表达式中，第 idx 个表达式的值
Status ExprEvalValueInTuple(ExprT *expr, ExprContextT *exprCtx, uint32_t idx, DmValueT *value)
{
    DB_POINTER2(expr, value);
    ExprArrayT *array = ExprGetExprArray(expr);
    if (idx >= array->num) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "idx out of range");
        return GMERR_DATA_EXCEPTION;
    }
    return ExprEvalValue(array->expr[idx], exprCtx, value);
}

#endif

static Status ExprEvalBoolDefault(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DmValueT dmValue = {};
    Status ret = ExprEvalValue(expr, exprCtx, &dmValue);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (dmValue.type == DB_DATATYPE_NULL) {
        *value = TRILEAN_NULL;
        return GMERR_OK;
    }

    // 如果是一元数字, 则转换成bool的值, 然后进行判断
    ret = DmValueConvert(&dmValue, DB_DATATYPE_BOOL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // 日志中记录实际错误码，对外返回不支持.
        DB_LOG_AND_SET_LASERR(ret, "unsupported Type(%" PRIi32 ")", (int32_t)expr->opType);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    *value = dmValue.value.boolValue;
    return GMERR_OK;
}

static Status ExprEvalBoolPhase2(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    switch (expr->opType) {
#if defined(FEATURE_TS) || defined(FEATURE_SQL) || defined(FEATURE_STREAM)
        case EXPR_OP_LIKE:
            return ExprEvalLike(expr, exprCtx, value);
        case EXPR_OP_IN:
        case EXPR_OP_NOTIN:
            return ExprEvalInOperator(expr, exprCtx, value);
#endif
#ifdef FEATURE_SQL
        case EXPR_OP_NOT_LIKE:
            return ExprEvalNotLike(expr, exprCtx, value);
        case EXPR_OP_GLOB:
            return ExprEvalGlob(expr, exprCtx, value);
        case EXPR_OP_NOT_GLOB:
            return ExprEvalNotGlob(expr, exprCtx, value);
        case EXPR_OP_IS:
            return ExprEvalIs(expr, exprCtx, value);
        case EXPR_OP_ISNOT:
            return ExprEvalIsNot(expr, exprCtx, value);
#endif
        default:
            return ExprEvalBoolDefault(expr, exprCtx, value);
    }
}

static Status ExprEvalBool(ExprT *expr, ExprContextT *exprCtx, TrileanE *value)
{
    DB_POINTER3(expr, exprCtx, value);
    switch (expr->opType) {
        case EXPR_OP_EQ:
            return ExprEvalEQ(expr, exprCtx, value);
        case EXPR_OP_DTL_EQ:
            return ExprEvalDtlEQ(expr, exprCtx, value);
        case EXPR_OP_NE:
            return ExprEvalNE(expr, exprCtx, value);
        case EXPR_OP_GT:
            return ExprEvalGT(expr, exprCtx, value);
        case EXPR_OP_LT:
            return ExprEvalLT(expr, exprCtx, value);
        case EXPR_OP_GE:
            return ExprEvalGE(expr, exprCtx, value);
        case EXPR_OP_LE:
            return ExprEvalLE(expr, exprCtx, value);
        case EXPR_OP_AND:
            return ExprEvalAnd(expr, exprCtx, value);
        case EXPR_OP_OR:
            return ExprEvalOr(expr, exprCtx, value);
        case EXPR_OP_NOT:
            return ExprEvalNot(expr, exprCtx, value);
        case EXPR_OP_ISNOTNULL:
            return ExprEvalIsNotNull(expr, exprCtx, value);
        case EXPR_OP_ISNULL:
            return ExprEvalIsNull(expr, exprCtx, value);
        case EXPR_OP_ISTRUE:
            return ExprEvalIsTrue(expr, exprCtx, value);
        default:
            return ExprEvalBoolPhase2(expr, exprCtx, value);
    }
}

Status ExprEvalQual(ExprStateT *expr, ExprContextT *exprCtx, bool *value)
{
    DB_POINTER3(expr, exprCtx, value);
    TrileanE val = TRILEAN_NULL;
    Status ret = ExprEvalBool(expr->expr, exprCtx, &val);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    *value = (val == TRILEAN_TRUE);
    return GMERR_OK;
}

// 处理二元运算符的操作数含null的场景
static void ExprEvalBinaryWithNull(ExprOpTypeE type, DmValueT *left, DmValueT *right, DmValueT *value)
{
    if (type == EXPR_OP_AND) {
        // AND运算，操作数含false的时候计算结果才是false，否则计算结果为null
        if ((left->type == DB_DATATYPE_BOOL && left->value.boolValue == false) ||
            (right->type == DB_DATATYPE_BOOL && right->value.boolValue == false)) {
            value->type = DB_DATATYPE_BOOL;
            value->value.boolValue = false;
            return;
        }
    }
    if (type == EXPR_OP_OR) {
        // OR运算，操作数含true的时候计算结果才是true，否则计算结果为null
        if ((left->type == DB_DATATYPE_BOOL && left->value.boolValue == true) ||
            (right->type == DB_DATATYPE_BOOL && right->value.boolValue == true)) {
            value->type = DB_DATATYPE_BOOL;
            value->value.boolValue = true;
            return;
        }
    }
    // 其它场景下，如果操作数里面带null，则计算结果也是null
    value->type = DB_DATATYPE_NULL;
    return;
}
#ifdef FEATURE_SQL

static Status ExprEvalBinaryValue(
    ExprOpTypeE type, DmValueT *left, DmValueT *right, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER4(left, right, exprCtx, value);

    // 支持3值逻辑，允许操作数为null
    if (left->type == DB_DATATYPE_NULL || right->type == DB_DATATYPE_NULL) {
        ExprEvalBinaryWithNull(type, left, right, value);
        return GMERR_OK;
    }
    bool isSameType = (left->type == right->type);
    switch (type) {
        case EXPR_OP_AND:
            return isSameType ? DmValueAnd(left, right, value) : DmValueAndDiffType(left, right, value);
        case EXPR_OP_OR:
            return isSameType ? DmValueOr(left, right, value) : DmValueOrDiffType(left, right, value);
        case EXPR_OP_ADD:
            return isSameType ? DmValueAdd(left, right, value) : DmValueAddDiffType(left, right, value);
        case EXPR_OP_SUB:
            return isSameType ? DmValueSub(left, right, value) : DmValueSubDiffType(left, right, value);
        case EXPR_OP_MULTI:
            return isSameType ? DmValueMulti(left, right, value) : DmValueMultiDiffType(left, right, value);
        case EXPR_OP_DIV:
            return isSameType ? DmValueDiv(left, right, value) : DmValueDivDiffType(left, right, value);
        case EXPR_OP_MOD:
            return DmValueModDiffType(left, right, value);
        case EXPR_OP_VECTOR_DIST_L2:
            return DmValueVecDist(left, right, DIST_TYPE_L2, value);
        case EXPR_OP_VECTOR_DIST_COSINE:
            return DmValueVecDist(left, right, DIST_TYPE_COSINE, value);
#ifndef IDS_HAOTIAN
        case EXPR_OP_BITAND:
            return isSameType ? DmValueBitAnd(left, right, value) : DmValueBitAndDiffType(left, right, value);
        case EXPR_OP_BITOR:
            return isSameType ? DmValueBitOr(left, right, value) : DmValueBitOrDiffType(left, right, value);
        case EXPR_OP_LSHIFT:
            return isSameType ? DmValueLshift(left, right, value) : DmValueLshiftDiffType(left, right, value);
        case EXPR_OP_RSHIFT:
            return isSameType ? DmValueRshift(left, right, value) : DmValueRshiftDiffType(left, right, value);
#endif
        case EXPR_OP_CONCAT:
            return ExprEvalStrConcat(left, right, exprCtx->memCtx, value);
        default: {
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "ExprEvalBinaryValueSQL: expr opCode");
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
    }
}
#else
static Status ExprEvalBinaryValue(
    ExprOpTypeE type, DmValueT *left, DmValueT *right, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER3(left, right, value);

    // 支持3值逻辑，允许操作数为null
    if (left->type == DB_DATATYPE_NULL || right->type == DB_DATATYPE_NULL) {
        ExprEvalBinaryWithNull(type, left, right, value);
        return GMERR_OK;
    }

    switch (type) {
        case EXPR_OP_AND:
            return DmValueAnd(left, right, value);
        case EXPR_OP_OR:
            return DmValueOr(left, right, value);
        case EXPR_OP_ADD:
            return DmValueAdd(left, right, value);
        case EXPR_OP_SUB:
            return DmValueSub(left, right, value);
        case EXPR_OP_MULTI:
            return DmValueMulti(left, right, value);
        case EXPR_OP_DIV:
            return DmValueDiv(left, right, value);
        case EXPR_OP_MOD:
            return DmValueMod(left, right, value);
        default: {
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "ExprEvalBinaryValue: expr opCode");
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
    }
}
#endif

Status ExprEvalBinary(ExprT *expr, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER2(expr, value);

    ExprBinaryT *binary = CastToBinary(expr);
    DmValueT left = {0};
    DmValueT right = {0};

    Status ret = ExprEvalValue(binary->left, exprCtx, &left);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = ExprEvalValue(binary->right, exprCtx, &right);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
#ifdef FEATURE_YANG
    ret = ConvertDmValueForEval(&left, &right);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif
    if ((ret = ExprEvalBinaryValue(expr->opType, &left, &right, exprCtx, value)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "calc left and right val");
        return ret;
    }

    return GMERR_OK;
}

Status ExprEvalCase(ExprT *expr, ExprContextT *exprCtx, DmValueT *value)
{
    Status ret;
    ExprCaseT *exprCase = CastToCase(expr);
    for (uint32_t i = 0; i < exprCase->array.num; i++) {
        ExprPairT *pair = &exprCase->array.exprPair[i];
        if (pair->expr1 == NULL) {  // Default
            return ExprEvalValue(pair->expr2, exprCtx, value);
        }

        TrileanE condition = 0;
        ret = ExprEvalBool(pair->expr1, exprCtx, &condition);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }

        if (condition == TRILEAN_TRUE) {
            return ExprEvalValue(pair->expr2, exprCtx, value);
        }
    }

    value->type = DB_DATATYPE_NULL;
    return GMERR_OK;
}

static AASlotT *ExprVarGetTuple(ExprOpTypeE opType, ExprContextT *exprCtx)
{
    if (opType == EXPR_OP_LVAR) {
        return exprCtx->leftTuple;
    }
    if (opType == EXPR_OP_RVAR) {
        return exprCtx->rightTuple;
    }
#ifdef FEATURE_SQL
    if (opType == EXPR_OP_NVAR) {
        return exprCtx->newTuple;
    }
    if (opType == EXPR_OP_OVAR) {
        return exprCtx->oldTuple;
    }
#endif
    return NULL;
}

static inline bool IsScanState(PlanStateT *planState)
{
    if (planState == NULL) {
        return false;
    }
    switch (planState->tagType) {
        case T_SEQ_SCAN:
#ifdef FEATURE_DATALOG
        case T_DELTA_SEQ_SCAN:
#endif
            return true;
        default:
            return false;
    }
}

static Status TransNode2JsonStr(DbMemCtxT *memCtx, DmNodeT *node, char **jsonStr)
{
    DbJsonT *root = DbJsonCreateObject();
    if (root == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Null pointer");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DmJsonPrintFormatTypeT printType = {0};
    uint32_t janssonFlag = GetPrintFormatFlagJanssonFlag(DM_JSON_VIEW_PRINT_FLAG, &printType);
    DbMemCtxT *oldMemCtx = DbMemCtxSwitchTo(memCtx);
    Status ret = DmVertexAddNodeToJson(node, root, printType, false);
    if (ret != GMERR_OK) {
        (void)DbMemCtxSwitchBack(oldMemCtx, memCtx);
        DbJsonDelete(root);
        return ret;
    }
    ret = DbMemCtxSwitchBack(oldMemCtx, memCtx);
    if (ret != GMERR_OK) {
        DbJsonDelete(root);
        return ret;
    }
    DbJsonT *valueJson = DbJsonObjectGet(root, node->nodeDesc->name);
    if (valueJson == NULL) {
        (void)DbMemCtxSwitchBack(oldMemCtx, memCtx);
        DbJsonDelete(root);
        return GMERR_OK;
    }
    ret = JsonToStr(memCtx, valueJson, janssonFlag, jsonStr);
    if (ret != GMERR_OK) {
        (void)DbMemCtxSwitchBack(oldMemCtx, memCtx);
    }
    DbJsonDelete(root);
    return ret;
}

static Status ExprEvalNodeVar(ExprContextT *exprCtx, AASlotT *slot, uint32_t propId, DmValueT *propertyValue)
{
    // join场景等，非node
    if (!IsScanState(exprCtx->plan)) {
        return GMERR_DATA_EXCEPTION;
    }
    ScanStateT *scan = (ScanStateT *)exprCtx->plan;
    // 存在无label场景
    if (scan == NULL || scan->label == NULL) {
        return GMERR_DATA_EXCEPTION;
    }
    // 获取node属性对应的node信息
    DmNodeT *node = NULL;
    Status ret = AASlotGetNode(slot, scan->label->vertexDesc, propId, &node);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get node property, propId=%" PRIu32, propId);
        return ret;
    }
    // 把node及子node序列化成json, jsonStr在堆上分配
    char *jsonStr = NULL;
    ret = TransNode2JsonStr(slot->memCtx, node, &jsonStr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "trans node to json, propId=%" PRIu32, propId);
        return ret;
    }
    // 字段不可读，要对齐类型
    if (jsonStr == NULL) {
        propertyValue->type = DB_DATATYPE_STRING;
        propertyValue->value.length = 0;
        return GMERR_OK;
    }
    // 把json填充到value中
    propertyValue->type = DB_DATATYPE_STRING;
    propertyValue->value.length = strlen(jsonStr) + 1;
    propertyValue->value.strAddr = jsonStr;
    return GMERR_OK;
}

Status ExprEvalVar(ExprT *expr, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprVarT *var = CastToVar(expr);

    if (var->expr.opType >= EXPR_OP_VAR_END || var->expr.opType < EXPR_OP_VAR_BEGIN) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "calc exprVar, opType:%" PRIu32, (uint32_t)var->expr.opType);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    AASlotT *slot = ExprVarGetTuple(var->expr.opType, exprCtx);
    if (slot == NULL) {
        *value = (DmValueT){0};
        value->type = DB_DATATYPE_NULL;
        return GMERR_OK;
    }
#ifdef FEATURE_SQL
    if (slot->buf != NULL) {
        value->type = slot->dataType;
        value->value.length = slot->propLength;
        value->value.strAddr = slot->buf + slot->propOffset;
        return GMERR_OK;
    }
#endif
    Status ret = (slot->slotType == AA_SLOT_DM_VERTEX || slot->slotType == AA_SLOT_RETURNING) &&
                         !DmVertexPropIsValidById(slot->dmVertex, var->propId) ?
                     ExprEvalNodeVar(exprCtx, slot, var->propId, value) :
                     AASlotGetPrope(slot, var->propId, value);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "get property, propId=%" PRIu32, var->propId);
        return ret;
    }
    return GMERR_OK;
}

SO_EXPORT Status ExprEvalConst(ExprT *expr, DmValueT *value)
{
    DB_POINTER2(expr, value);
    *value = CastToConst(expr)->arg;
    return GMERR_OK;
}

Status ExprEvalPara(ExprT *expr, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprParaT *para = CastToPara(expr);

    if (SECUREC_UNLIKELY(para->paraId >= exprCtx->paraNum)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "paraId out of range, paraId=%" PRIu32 ", paraNum=%" PRIu32,
            para->paraId, exprCtx->paraNum);
        return GMERR_DATA_EXCEPTION;
    }

    *value = exprCtx->para[para->paraId];

    if (para->valueType == DB_DATATYPE_NULL || value->type == DB_DATATYPE_NULL) {
        return GMERR_OK;
    }
    Status ret = DmValueConvert(value, para->valueType);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "convert value type, paraId:%" PRIu32, para->paraId);
    }
    return ret;
}

// 空实现，表示还未实现的内建函数
NO_INLINE
Status ExprEvalNotSupported(ExprT *expr, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER2(expr, value);
    DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "BuiltIn funcType");
    return GMERR_FEATURE_NOT_SUPPORTED;
}

#if defined(FEATURE_TS) || defined(FEATURE_SQL)

Status ExprEvalExtdSum(ExprT *expr, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER3(expr, exprCtx, value);
    DmValueT leftVal = {};
    DmValueT rightVal = {};
    ExprBinaryT *binary = CastToBinary(expr);
    Status ret = ExprEvalValue(binary->left, exprCtx, &leftVal);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (leftVal.type == DB_DATATYPE_TIME || leftVal.type == DB_DATATYPE_STRING || leftVal.type == DB_DATATYPE_BYTES ||
        leftVal.type == DB_DATATYPE_FIXED || leftVal.type == DB_DATATYPE_BITMAP) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Object to sum");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    ret = ExprEvalValue(binary->right, exprCtx, &rightVal);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return DmValueAddDiffType(&leftVal, &rightVal, value);
}

Status ExprEvalExtdCount(ExprT *expr, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER3(expr, exprCtx, value);
    DmValueT leftVal = {};
    DmValueT countVal = {};
    ExprBinaryT *binary = CastToBinary(expr);
    Status ret = ExprEvalValue(binary->left, exprCtx, &leftVal);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (leftVal.type == DB_DATATYPE_TIME) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "Object to count");
        return GMERR_INVALID_VALUE;
    }

    ret = ExprEvalCountValue(binary->right, exprCtx, &countVal);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (exprCtx->leftTuple == NULL) {
        value->type = DB_DATATYPE_INT64;
        value->value.longValue = countVal.value.longValue;
        return GMERR_OK;
    }
    return DmValueAdd(&leftVal, &countVal, value);
}

Status ExprEvalExtdMaxMin(ExprT *expr, ExprContextT *exprCtx, DmValueT *value, int8_t compare)
{
    DB_POINTER3(expr, exprCtx, value);
    int8_t result;
    ExprBinaryT *binary = CastToBinary(expr);
    Status ret = ExprEvalValue(binary->left, exprCtx, value);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    DmValueT tempVal = {};
    ret = ExprEvalValue(binary->right, exprCtx, &tempVal);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = DmValueCompare(value, &tempVal, &result);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (result == compare) {
        *value = tempVal;
    }
    return ret;
}

Status ExprEvalExtdFirst(ExprT *expr, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprBinaryT *binary = CastToBinary(expr);
    return ExprEvalValue(binary->left, exprCtx, value);
}

Status ExprEvalExtdLast(ExprT *expr, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprBinaryT *binary = CastToBinary(expr);
    return ExprEvalValue(binary->right, exprCtx, value);
}

static Status ColSetAppendStr(DbMemCtxT *memCtx, DmValueT *originValue, DmValueT *curValue)
{
    // ", "占用2个字节
    uint32_t newStrLen = originValue->value.length + GetIntValLen(curValue->value.longValue) + 2;
    char *newStr = DbDynMemCtxAlloc(memCtx, newStrLen);
    if (newStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "colset newstr alloc");
        return GMERR_OUT_OF_MEMORY;
    }
    memset_s(newStr, newStrLen, 0, newStrLen);

    // 不能超过ts字符串最大长度
    errno_t err =
        sprintf_s(newStr, newStrLen, "%s, %" PRId64 "", (char *)originValue->value.strAddr, curValue->value.longValue);
    if (err < 0 || newStrLen > TS_CHAR_MAX_LEN) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW,
            "tsAggFunc colset sprintf_s:%" PRId64 ", maxlen:%" PRIu32 ", curlen:%" PRIu32 ", oriStr: %s",
            curValue->value.longValue, TS_CHAR_MAX_LEN, newStrLen, (char *)originValue->value.strAddr);
        return GMERR_FIELD_OVERFLOW;
    }

    originValue->value.strAddr = newStr;
    originValue->value.length = (uint32_t)(strlen(newStr) + 1);
    return GMERR_OK;
}

Status ExprEvalExtdColSet(ExprT *expr, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER3(expr, exprCtx, value);
    Status ret = GMERR_OK;
    ExprBinaryT *binary = CastToBinary(expr);

    // 获取当前slot中value
    DmValueT curValue = {0};
    ret = ExprEvalValue(binary->right, exprCtx, &curValue);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get curSlot val");
        return ret;
    }

    // 已去重后的值
    ret = ExprEvalValue(binary->left, exprCtx, value);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get origin colset strVal");
        return ret;
    }

    char *curStr = ConvertInt642Str(exprCtx->memCtx, curValue.value.longValue);
    if (curStr == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    // 在hashtable中查看是否存在，不存在则追加
    char *originStr = (char *)value->value.strAddr;
    if (strstr(originStr, curStr) == NULL) {
        ret = ColSetAppendStr(exprCtx->memCtx, value, &curValue);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_AND_SET_LASERR(ret, "append new str: %s to %s.", curStr, originStr);
            return ret;
        }
    }
    return GMERR_OK;
}

Status ExprEvalArray(ExprStateT *state, ExprContextT *exprCtx)
{
    DB_POINTER2(state, exprCtx);
    ExprArrayT *arrayExpr = &CastToFunc(state->expr)->array;
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < arrayExpr->num; i++) {
        ret = ExprEvalSetField(arrayExpr->expr[i], exprCtx);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    return ret;
}

#endif

#ifdef FEATURE_STREAM
Status StreamExprEvalArray(ExprStateT *state, ExprContextT *exprCtx)
{
    DB_POINTER2(state, exprCtx);
    ExprArrayT *arrayExpr = &CastToFunc(state->expr)->array;
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < arrayExpr->num; i++) {
        ExprSetFieldT *setField = CastToSetField(arrayExpr->expr[i]);
        DmValueT value = {0};
        ret = ExprEvalValue(setField->func, exprCtx, &value);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        ret = AASlotSetPrope(exprCtx->resultTuple, setField->propId, value);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "set property, propId=%" PRIu32, setField->propId);
            return ret;
        }
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    return ret;
}
static Status StreamHashCmp(ExprContextT *exprCtx, ExprFuncT *funcExpr, DmValueT *value)
{
    ExprArrayT *arrayExpr = &funcExpr->array;
    DmValueT rightValue = {0};
    DmValueT leftValue = {0};
    uint64_t lHash = 0, rHash = 0;
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < arrayExpr->num; i++) {
        ExprT *itemExpr = arrayExpr->expr[i];
        uint32_t propId = CastToVar(itemExpr)->propId;
        ret = AASlotGetPrope(exprCtx->rightTuple, propId, &rightValue);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get right tuple val");
            return ret;
        }
        ret = AASlotGetPrope(exprCtx->leftTuple, propId, &leftValue);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "get left tuple val");
            return ret;
        }
        DB_ASSERT(rightValue.type == leftValue.type);
        if (!DM_TYPE_NEED_MALLOC(rightValue.type)) {
            int8_t cmp = 0;
            DmValueCompare(&leftValue, &rightValue, &cmp);
            if (cmp != 0) {
                value->type = DB_DATATYPE_INT8;
                value->value.byteValue = -1;
                return GMERR_OK;
            }
        } else {
            rHash += DbHash32(rightValue.value.strAddr, rightValue.value.length);
            lHash += DbHash32(leftValue.value.strAddr, leftValue.value.length);
            if (rHash != lHash) {
                value->type = DB_DATATYPE_INT8;
                value->value.byteValue = -1;
                return GMERR_OK;
            }
        }
    }
    return ret;
}
/* 允许hash冲突的情况存在，即不精确比较 */
Status ExprEvalSeqDistctCnt(ExprT *expr, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER3(expr, exprCtx, value);
    ExprBinaryT *binary = CastToBinary(expr);
    if (binary->left->opType == EXPR_OP_LVAR) {
        /* 第一次访问，初始化比较结果 */
        value->type = DB_DATATYPE_INT8;
        value->value.byteValue = 0;
    } else {
        DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR, "cnt expr left op");
        return GMERR_SEMANTIC_ERROR;
    }

    if (binary->right->opType != EXPR_OP_FUNC_BEGIN) {
        DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR, "cnt expr right op");
        return GMERR_SEMANTIC_ERROR;
    }
    ExprFuncT *funcExpr = CastToFunc(binary->right);

    return StreamHashCmp(exprCtx, funcExpr, value);
}

Status ExprEvalRowNumber(ExprT *expr, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER3(expr, exprCtx, value);
    return ExprEvalExtdCount(expr, exprCtx, value);
}

#endif

#ifdef FEATURE_SQL
#ifndef IDS_HAOTIAN
static inline Status ExprEvalSetParaTuple(ExprT *expr, ExprContextT *exprCtx)
{
    DB_POINTER2(expr, exprCtx);
    const ExprArrayT *setParaArray = &CastToFunc(expr)->array;
    for (uint32_t i = 0; i < setParaArray->num; i++) {
        Status ret = ExprEvalSetPara(setParaArray->expr[i], exprCtx);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status ExprEvalAndMakeConst(ExprContextT *exprCtx, ExprT *expr, ExprT **resExpr, bool *isLeftNull)
{
    DB_POINTER4(exprCtx, expr, resExpr, isLeftNull);
    Status ret = GMERR_OK;
    if (expr->opType == EXPR_OP_TUPLE) {
        ExprArrayT *array = &CastToFunc(expr)->array;
        ExprT *newExpr = ExprMakeTuple(exprCtx->memCtx, array->num);
        if (newExpr == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "make tuple expr");
            return GMERR_OUT_OF_MEMORY;
        }
        ExprArrayT *newArray = &CastToFunc(newExpr)->array;
        for (uint32_t i = 0; i < array->num; i++) {
            ret = ExprEvalAndMakeConst(exprCtx, array->expr[i], &newArray->expr[i], isLeftNull);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
        }
        *resExpr = newExpr;
        return GMERR_OK;
    }
    // 1.1 计算左孩子，标记一下NULL值
    DmValueT value = {0};
    ret = ExprEvalValue(expr, exprCtx, &value);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (value.type == DB_DATATYPE_NULL) {
        *isLeftNull = true;
    }
    // 1.2 构造ExprConstT替换原有左孩子，避免多次计算左孩子, 在SwitchConstBackToExpr中释放内存
    ExprT *newExpr = ExprMakeConst(exprCtx->memCtx, value);
    if (newExpr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "make const expr");
        return GMERR_OUT_OF_MEMORY;
    }
    *resExpr = newExpr;
    return GMERR_OK;
}

static inline Status SwitchExprToConst(ExprT *testExpr, ExprContextT *exprCtx, bool *isLeftNull, ExprT **originLeftExpr)
{
    DB_POINTER4(testExpr, exprCtx, isLeftNull, originLeftExpr);
    *originLeftExpr = CastToBinary(testExpr)->left;
    return ExprEvalAndMakeConst(exprCtx, CastToBinary(testExpr)->left, &CastToBinary(testExpr)->left, isLeftNull);
}

static inline void SwitchConstBackToExpr(ExprT *testExpr, ExprContextT *exprCtx, ExprT *originLeftExpr)
{
    DB_POINTER3(testExpr, exprCtx, originLeftExpr);
    ExprDestroy(exprCtx->memCtx, CastToBinary(testExpr)->left);
    CastToBinary(testExpr)->left = originLeftExpr;
}

static inline void SetBoolValue(DmValueT *value, bool boolValue)
{
    DB_POINTER(value);
    *value = (DmValueT){0};
    value->type = DB_DATATYPE_BOOL;
    value->value.boolValue = boolValue;
}

Status ExecSubQueryEachSlot(AAT *aa, ExprSubQueryT *subQuery, ExprContextT *tmpCtx, bool *subHasNull, DmValueT *value)
{
    DB_POINTER5(aa, subQuery, tmpCtx, subHasNull, value);
    AASlotT *slot = NULL;
    Status ret = GMERR_OK;
    while ((slot = AAGetNextPropSlot(aa)) != NULL) {
        ExprSetAASlot(tmpCtx, NULL, slot, NULL);
        // 4.3.1 计算subParam, 设置子查询结果到参数列表
        ret = ExprEvalSetPara(subQuery->subParam, tmpCtx);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        // 4.3.2 计算testExpr, 校验子查询结果是否满足ANY结果
        ret = ExprEvalValue(subQuery->testExpr, tmpCtx, value);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        // case 1 : COMPARE 只返回第一个结果
        if (subQuery->expr.opType == EXPR_OP_SUBQUERY_COMPARE) {
            return GMERR_OK;
        }
        // case 2 : ANY 遇到满足的即可返回true, 遇到NULL标记
        if (value->type == DB_DATATYPE_NULL) {
            *subHasNull = true;
        } else if (value->type == DB_DATATYPE_BOOL && value->value.boolValue) {
            return GMERR_OK;
        }
    }
    value->type = DB_DATATYPE_UNDEFINED;  // 没有满足的子查询结果，需要继续遍历
    return GMERR_OK;
}

Status ExecSubQuery(
    PlanStateT *planState, ExprSubQueryT *subQuery, ExprContextT *exprCtx, bool isLeftNull, DmValueT *value)
{
    DB_POINTER4(planState, subQuery, exprCtx->memCtx, value);
    AAT *aa = NULL;
    Status ret = NewAA(exprCtx->memCtx, &aa);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ExprContextT tmpCtx = *exprCtx;  // 临时的表达式上下文，避免子查询更改父查询的表达式上下文
    bool subHasNull = false;
    ExprOpTypeE type = subQuery->expr.opType;
    while ((ret = ExecProcNode(planState, aa)) == GMERR_OK) {
        // 4.1 EXIST类型只要子查询有结果就可以返回
        if (type == EXPR_OP_SUBQUERY_EXISTS) {
            SetBoolValue(value, !AAIsEmpty(aa));
            goto EXIT;
        }
        // 4.2 左操作数为NULL时，只要子查询有结果，就直接返回NULL
        if (isLeftNull) {
            value->type = DB_DATATYPE_NULL;
            goto EXIT;
        }
        // 4.3 遍历这一批次的每个slot，校验是否满足子查询条件
        value->type = DB_DATATYPE_UNDEFINED;
        ret = ExecSubQueryEachSlot(aa, subQuery, &tmpCtx, &subHasNull, value);
        if (ret != GMERR_OK || value->type != DB_DATATYPE_UNDEFINED) {
            goto EXIT;
        }
    }
    // 4.4 ANY类型下，子查询结果中没有与左操作数匹配的结果，如果子查询中出现至少一个NULL则返回NULL，否则返回false
    if (type == EXPR_OP_SUBQUERY_ANY) {
        value->type = subHasNull ? DB_DATATYPE_NULL : DB_DATATYPE_BOOL;
        value->value.boolValue = false;
    }

    // 4.5 COMPARE类型下，子查询结果为空时返回NULL
    if (type == EXPR_OP_SUBQUERY_COMPARE && ret == GMERR_NO_DATA) {
        value->type = DB_DATATYPE_NULL;
    }
EXIT:
    // 这里只释放上方申请的aa，是否释放其slot由noOwnerShip决定
    DeleteAA(aa);
    // 遇到算子表示终止迭代的错误码，转为GMERR_OK
    return ret == GMERR_NO_DATA ? GMERR_OK : ret;
}

Status ExprEvalSubQuery(ExprT *expr, ExprContextT *exprCtx, DmValueT *value)
{
    DB_POINTER3(expr, exprCtx, value);
    if (exprCtx->memCtx == NULL || exprCtx->subQueryStateList == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Incorrect exprCtx");
        return GMERR_DATA_EXCEPTION;
    }
    ExprSubQueryT *subQueryExpr = (ExprSubQueryT *)(void *)expr;

    // 1. 判断testExpr的左孩子是否只需要计算一次: 仅在testExpr为比较运算符时成立
    Status ret = GMERR_OK;
    ExprT *originLeftExpr = NULL;
    bool isLeftNull = false;
    ExprT *testExpr = subQueryExpr->testExpr;
    bool isTestExprCompOp = (testExpr != NULL && IsCompOp(testExpr->opType));
    if (isTestExprCompOp) {
        ret = SwitchExprToConst(testExpr, exprCtx, &isLeftNull, &originLeftExpr);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            ExprDestroy(exprCtx->memCtx, CastToBinary(testExpr)->left);
            return ret;
        }
    }

    // 2. 计算parParam, 设置父查询相关列到参数列表
    if (subQueryExpr->parParam != NULL) {
        ret = ExprEvalSetParaTuple(subQueryExpr->parParam, exprCtx);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }

    // 3. 调用ExecReScan, 重置子查询状态
    PlanStateT *subQueryState = *(PlanStateT **)DbListItem(exprCtx->subQueryStateList, subQueryExpr->subQueryId);
    ret = ExecReScan(subQueryState);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 4. 循环调用ExecProcNode, 获取子查询的结果, 进行结果校验
    ret = ExecSubQuery(subQueryState, subQueryExpr, exprCtx, isLeftNull, value);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "eval sub-query");
        return ret;
    }

    if (isTestExprCompOp) {
        SwitchConstBackToExpr(subQueryExpr->testExpr, exprCtx, originLeftExpr);
    }
    return GMERR_OK;
}
#endif

Status EvalConstValueInDisExpr(ExprT *expr, DmValueT *value)
{
    DB_POINTER2(expr, value);
    ExprBinaryT *binary = CastToBinary(expr);
    if (binary->left->opType == EXPR_OP_CONST) {
        *value = CastToConst(binary->left)->arg;
    } else {
        *value = CastToConst(binary->right)->arg;
    }
    return GMERR_OK;
}
#endif

#ifdef __cplusplus
}
#endif
