/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: implements for path metadata interfaces
 * Author: gongsai
 * Create: 2022-9-5
 */

#include <securec.h>
#include "dm_meta_complex_path.h"
#include "dm_meta_complex_path_in.h"
#include "db_mem_context.h"
#include "dm_log.h"

#ifdef __cplusplus
extern "C" {
#endif

Status DmCreateEmptyComplexPathInfoWithMemCtx(DbMemCtxT *memCtx, DmComplexPathInfoT **pathInfo)
{
    DB_POINTER2(memCtx, pathInfo);
    uint32_t memSize = (uint32_t)sizeof(DmComplexPathInfoT);
    *pathInfo = (DmComplexPathInfoT *)DbDynMemCtxAlloc(memCtx, memSize);
    if (*pathInfo == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "allocate for complex path info.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(*pathInfo, memSize, 0x00, memSize);
    (*pathInfo)->memCtx = memCtx;
    return GMERR_OK;
}

static void DestroyAllEdgeAndVertex(DbOamapT *vertexLabelId2PathInfoVertexMap)
{
    if (vertexLabelId2PathInfoVertexMap == NULL) {
        return;
    }

    DbOamapIteratorT iter = 0;
    uint32_t *vertexLabelId;
    DbListT *pathInfoVertexList = NULL;
    while (DbOamapFetch(vertexLabelId2PathInfoVertexMap, &iter, (void *)&vertexLabelId, (void **)&pathInfoVertexList) ==
           GMERR_OK) {
        uint32_t cnt = DbListGetItemCnt(pathInfoVertexList);
        for (uint32_t i = 0; i < cnt; ++i) {
            DmPathInfoVertexT *pathInfoVertex = *(DmPathInfoVertexT **)DbListItem(pathInfoVertexList, i);
            DestroyPathInfoVertex(pathInfoVertex);
        }
        DbDynMemFree(vertexLabelId);
        DbDestroyList(pathInfoVertexList);
        DbDynMemFree(pathInfoVertexList);
    }

    DbOamapDestroy(vertexLabelId2PathInfoVertexMap);
    DbDynMemFree(vertexLabelId2PathInfoVertexMap);
}

static void DestroySubFields(DmComplexPathInfoT *pathInfo)
{
    for (uint32_t i = 0; i < DM_SUBS_COMPLEXPATH_CEIL; i++) {
        if (pathInfo->vertexLabelId2SubFieldsMap[i] == NULL) {
            continue;
        }
        DbOamapT *tmpMap = pathInfo->vertexLabelId2SubFieldsMap[i];
        DbOamapIteratorT iter = 0;
        uint32_t *vertexLabelId = NULL;
        DmVertexSubFieldsT *src = NULL;
        uint32_t cnt = 0;
        DmSubFieldT *tmp = NULL;
        while (DbOamapFetch(tmpMap, &iter, (void **)&vertexLabelId, (void **)&src) == GMERR_OK) {
            if (src->vertexLabelName != NULL) {
                DbDynMemFree(src->vertexLabelName->str);
            }
            DbDynMemFree(src->vertexLabelName);
            cnt = DbListGetItemCnt(&src->subFields);
            for (uint32_t j = 0; j < cnt; j++) {
                tmp = (DmSubFieldT *)DbListItem(&src->subFields, j);
                DbDynMemFree(tmp->fieldName->str);  // 釋放str*
                DbDynMemFree(tmp->fieldName);       // 释放TEXT*
                DbDynMemFree(tmp->specifyPathName);
            }
            DbDestroyList(&src->subFields);
            DbDynMemFree(src);
        }
        DbOamapDestroy(tmpMap);
        DbDynMemFree(tmpMap);
        pathInfo->vertexLabelId2SubFieldsMap[i] = NULL;
    }
}

static void DestroyDmVertexLabelAndFieldTupleT(DmVertexLabelAndFieldTupleT *tuple)
{
    if (tuple == NULL) {
        return;
    }
    if (tuple->vertexLabelName.str != NULL) {
        DbDynMemFree(tuple->vertexLabelName.str);
        tuple->vertexLabelName.str = NULL;
        tuple->vertexLabelName.len = 0;
    }
    if (tuple->fieldName.str != NULL) {
        DbDynMemFree(tuple->fieldName.str);
        tuple->fieldName.str = NULL;
        tuple->fieldName.len = 0;
    }
    DbDynMemFree(tuple);
}

static void DestroyPIDField(DmComplexPathInfoT *pathInfo)
{
    DmPIDFieldT *pidField = pathInfo->pidField;
    if (pidField == NULL) {
        return;
    }
    DestroyDmVertexLabelAndFieldTupleT(pidField);
    pathInfo->pidField = NULL;
}

static void DestroyMergeAndReplaceFields(DmComplexPathInfoT *pathInfo)
{
    pathInfo->hasReplaceRootOrMergeView = false;
    DmGeneratePathInfoT *generatePathInfo = pathInfo->generatePathInfo;
    if (generatePathInfo == NULL) {
        return;
    }
    generatePathInfo->viewTableId = 0;
    uint32_t clausesCnt = DbListGetItemCnt(&generatePathInfo->setClauses);
    for (uint32_t i = 0; i < clausesCnt; i++) {
        DmSetClauseT *currClause = (DmSetClauseT *)DbListItem(&generatePathInfo->setClauses, i);
        if (currClause->tag == DM_SET_CLAUSE_PROPERTY) {
            DestroyDmVertexLabelAndFieldTupleT(currClause->rightProp);
        } else if (currClause->tag == DM_SET_CLAUSE_COALESCE_FUNC) {
            DestroyDmVertexLabelAndFieldTupleT(currClause->coalesceClause->coalesceLeftProp);
            DestroyDmVertexLabelAndFieldTupleT(currClause->coalesceClause->coalesceRightProp);
            DbDynMemFree(currClause->coalesceClause);
        }
        DestroyDmVertexLabelAndFieldTupleT(currClause->leftProp);
        currClause->tag = 0;
    }
    DmSetClauseT *listClauses = (DmSetClauseT *)DbListGetItems(&generatePathInfo->setClauses);
    DbDynMemFree(listClauses);
    DbClearList(&generatePathInfo->setClauses);
    DbDynMemFree(generatePathInfo);
    pathInfo->generatePathInfo = NULL;
}

static void DestroyPathConstraint(DmComplexPathInfoT *pathInfo)
{
    if (pathInfo->constraint == NULL) {
        return;
    }
    DmPathConstraintT *constraint = pathInfo->constraint;
    uint32_t condNum = DbListGetItemCnt(&constraint->conditions);
    for (uint32_t i = 0; i < condNum; ++i) {
        DmPathConditionT *condition = (DmPathConditionT *)DbListItem(&constraint->conditions, i);
        DmValueFreeInner(&condition->value);
    }
    DbDestroyList(&constraint->conditions);
    DbDynMemFree(pathInfo->constraint);
    pathInfo->constraint = NULL;
}

// 依赖Current memory context释放内存
static void DestroyPathInfoInner(DmComplexPathInfoT *pathInfo)
{
    if (pathInfo == NULL) {
        return;
    }

    if (pathInfo->metaCommon.metaName != NULL) {
        DbDynMemFree(pathInfo->metaCommon.metaName);
        pathInfo->metaCommon.metaName = NULL;
    }

    DbDynMemFree(pathInfo->creator);
    pathInfo->creator = NULL;

    pathInfo->deletionMode = 0;

    if (pathInfo->pattern != NULL) {
        DbDynMemFree(pathInfo->pattern);
        pathInfo->pattern = NULL;
    }

    if (pathInfo->vertexLabelId2PathInfoVertexMap != NULL) {
        // 通过vertexLabelId2PathInfoVertex成员free所有的边和点，最后free自身
        DestroyAllEdgeAndVertex(pathInfo->vertexLabelId2PathInfoVertexMap);
        pathInfo->vertexLabelId2PathInfoVertexMap = NULL;
    }
    DestroySubFields(pathInfo);
    DestroyPIDField(pathInfo);
    DestroyMergeAndReplaceFields(pathInfo);
    DestroyPathConstraint(pathInfo);

    DbDynMemFree(pathInfo);
}

void DmDestroyComplexPathInfo(DmComplexPathInfoT *pathInfo)
{
    if (pathInfo == NULL) {
        return;
    }
    if (pathInfo->memCtx != NULL) {
        DbMemCtxT *oldMemCtx = DbMemCtxSwitchTo(pathInfo->memCtx);
        DestroyPathInfoInner(pathInfo);
        if (oldMemCtx != NULL) {
            (void)DbMemCtxSwitchTo(oldMemCtx);
        } else {
            DbSetCurrMemCtxToNull();
        }
    } else {
        DestroyPathInfoInner(pathInfo);
    }
}

static void InitPathInfo2NonInnerMem(DmComplexPathInfoT *pathInfo)
{
    pathInfo->metaCommon.metaName = NULL;
    pathInfo->memCtx = NULL;
    pathInfo->vertexLabelId2PathInfoVertexMap = NULL;
    pathInfo->pattern = NULL;
    pathInfo->creator = NULL;
    size_t size = DM_SUBS_COMPLEXPATH_CEIL * sizeof(DbOamapT *);
    (void)memset_s(pathInfo->vertexLabelId2SubFieldsMap, size, 0x00, size);
    pathInfo->pidField = NULL;
    pathInfo->constraint = NULL;
}

static Status CopyVertexLabelId2PathInfoVertexMap(DbMemCtxT *destMemCtx, DbOamapT *src, DbOamapT *dst)
{
    DB_POINTER3(destMemCtx, src, dst);
    DbListT *pathInfoVertexList = NULL;
    uint32_t *vertexLabelId;
    DbOamapIteratorT iter = 0;
    Status ret = GMERR_OK;
    while (DbOamapFetch(src, &iter, (void **)&vertexLabelId, (void **)&pathInfoVertexList) == GMERR_OK) {
        uint32_t cnt = DbListGetItemCnt(pathInfoVertexList);
        for (uint32_t i = 0; i < cnt; ++i) {
            DmPathInfoVertexT *pathInfoVertex = *(DmPathInfoVertexT **)DbListItem(pathInfoVertexList, i);
            // 创建新PiVertex，将源piVertex内容深拷贝过去，然后加入键值对到新哈希中
            DmPathInfoVertexT *newPiVertex = NULL;
            ret = DmCreateEmptyPathInfoVertexWithMemCtx(destMemCtx, &newPiVertex);
            if (ret != GMERR_OK) {
                // 前面创建成功的pathInfoVertex由外层统一释放
                return ret;
            }
            // 此时插入新hash的新PiVertex无法更新其边集合和或组集合，新PiVertex只拷贝源PiVertex的vertexLabelId字段和variable
            // name
            newPiVertex->vertexLabelId = *vertexLabelId;
            ret = DbCopyStrToText(destMemCtx, &newPiVertex->pathInfoVertexName, pathInfoVertex->pathInfoVertexName.str,
                pathInfoVertex->pathInfoVertexName.len - 1);
            if (ret != GMERR_OK) {
                DbDynMemCtxFree(destMemCtx, newPiVertex);
                return ret;
            }
            ret = DmAddPathInfoVertexToVertexLabelId2PathInfoVertexMap(destMemCtx, dst, &newPiVertex);
            if (ret != GMERR_OK) {
                DbDynMemCtxFree(destMemCtx, newPiVertex);
                return ret;
            }
        }
    }
    return ret;
}

static Status CopyEdgeSet(DbMemCtxT *destMemCtx, DbListT *srcEdgeList, DmPathInfoVertexT *destPathInfoVertex,
    DbOamapT *dstVertexLabelId2PathInfoVertex, DbOamapT *dstEdgeLabelId2PathInfoEdge, bool inOrOutFlag)
{
    uint32_t count = DbListGetItemCnt(srcEdgeList);
    DmPathInfoEdgeT **edges = DbListGetItems(srcEdgeList);
    DmPathInfoEdgeT *dstPathInfoEdge = NULL;
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < count; ++i) {
        // 判断destPv中的edge实例是否存在，不存在则新实例化piedge，拷贝源edge并加入edgeh中
        dstPathInfoEdge =
            DbOamapLookup(dstEdgeLabelId2PathInfoEdge, edges[i]->edgeLabelId, &(edges[i]->edgeLabelId), NULL);
        if (dstPathInfoEdge == NULL) {
            ret = DmCreateEmptyPathInfoEdgeWithMemCtx(destMemCtx, &dstPathInfoEdge);
            if (ret != GMERR_OK) {
                break;
            }
            ret = CopyPathInfoEdge(edges[i], dstPathInfoEdge, dstVertexLabelId2PathInfoVertex);
            if (ret != GMERR_OK) {
                break;
            }
            ret = DbOamapInsert(dstEdgeLabelId2PathInfoEdge, dstPathInfoEdge->edgeLabelId,
                &(dstPathInfoEdge->edgeLabelId), dstPathInfoEdge, NULL);
            if (ret != GMERR_OK) {
                break;
            }
        }
        ret = DmAddEdgeToVertexByDirection(destPathInfoVertex, &dstPathInfoEdge, inOrOutFlag);
        if (ret != GMERR_OK) {
            break;
        }
    }
    // 异常处理
    if (ret != GMERR_OK) {
        DestroyPathInfoEdge(dstPathInfoEdge);
        dstPathInfoEdge = NULL;
    }
    return ret;
}

static Status CopyInEdgeSet(DbMemCtxT *destMemCtx, DmPathInfoVertexT *srcPathInfoVertex,
    DmPathInfoVertexT *destPathInfoVertex, DbOamapT *dstVertexLabelId2PathInfoVertex,
    DbOamapT *dstEdgeLabelId2PathInfoEdge)
{
    return CopyEdgeSet(destMemCtx, srcPathInfoVertex->inEdgeList, destPathInfoVertex, dstVertexLabelId2PathInfoVertex,
        dstEdgeLabelId2PathInfoEdge, true);
}

static Status CopyOutEdgeSet(DbMemCtxT *destMemCtx, DmPathInfoVertexT *srcPathInfoVertex,
    DmPathInfoVertexT *destPathInfoVertex, DbOamapT *dstVertexLabelId2PathInfoVertex,
    DbOamapT *dstEdgeLabelId2PathInfoEdge)
{
    return CopyEdgeSet(destMemCtx, srcPathInfoVertex->outEdgeList, destPathInfoVertex, dstVertexLabelId2PathInfoVertex,
        dstEdgeLabelId2PathInfoEdge, false);
}

static Status CopyEdgeSets(DbMemCtxT *destMemCtx, DmPathInfoVertexT *srcPathInfoVertex,
    DmPathInfoVertexT *destPathInfoVertex, DbOamapT *dstVertexLabelId2PathInfoVertex,
    DbOamapT *dstEdgeLabelId2PathInfoEdge)
{
    DB_POINTER5(destMemCtx, srcPathInfoVertex, destPathInfoVertex, dstVertexLabelId2PathInfoVertex,
        dstEdgeLabelId2PathInfoEdge);
    Status ret = GMERR_OK;
    // 深拷贝入边集合
    if (srcPathInfoVertex->inEdgeList != NULL) {
        destPathInfoVertex->inEdgeList = (DbListT *)DbDynMemCtxAlloc(DbGetCurMemCtx(), sizeof(DbListT));
        if (destPathInfoVertex->inEdgeList == NULL) {
            return GMERR_OUT_OF_MEMORY;
        }
        DbCreateList(destPathInfoVertex->inEdgeList, sizeof(DmPathInfoEdgeT *), DbGetCurMemCtx());
        ret = CopyInEdgeSet(destMemCtx, srcPathInfoVertex, destPathInfoVertex, dstVertexLabelId2PathInfoVertex,
            dstEdgeLabelId2PathInfoEdge);
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    // 深拷贝出边集合
    if (srcPathInfoVertex->outEdgeList != NULL) {
        destPathInfoVertex->outEdgeList = (DbListT *)DbDynMemCtxAlloc(DbGetCurMemCtx(), sizeof(DbListT));
        if (destPathInfoVertex->outEdgeList == NULL) {
            return GMERR_OUT_OF_MEMORY;
        }
        DbCreateList(destPathInfoVertex->outEdgeList, sizeof(DmPathInfoEdgeT *), DbGetCurMemCtx());
        ret = CopyOutEdgeSet(destMemCtx, srcPathInfoVertex, destPathInfoVertex, dstVertexLabelId2PathInfoVertex,
            dstEdgeLabelId2PathInfoEdge);
    }
    return ret;
}

static Status CopyInOrGroupSet(DbMemCtxT *destMemCtx, DmPathInfoVertexT *srcPathInfoVertex,
    DmPathInfoVertexT *destPathInfoVertex, DbOamapT *dstVertexLabelId2PathInfoVertex,
    DbOamapT *dstEdgeLabelId2PathInfoEdge)
{
    uint32_t count = DbListGetItemCnt(srcPathInfoVertex->inOrGroupList);
    DmPathInfoOrGroupT **orGroups = DbListGetItems(srcPathInfoVertex->inOrGroupList);
    DmPathInfoOrGroupT *dstOrGroup = NULL;
    Status ret = GMERR_OK;
    // 遍历每个或组
    for (uint32_t i = 0; i < count; ++i) {
        ret = DmCreateEmptyPathInfoOrGroupWithMemCtx(destMemCtx, &dstOrGroup);
        if (ret != GMERR_OK) {
            break;
        }
        // 深拷贝或组
        ret =
            CopyPathInfoOrGroup(orGroups[i], dstOrGroup, dstVertexLabelId2PathInfoVertex, dstEdgeLabelId2PathInfoEdge);
        if (ret != GMERR_OK) {
            break;
        }
        ret = DmAddOrGroupToVertexByDirection(destPathInfoVertex, &dstOrGroup, true);
        if (ret != GMERR_OK) {
            break;
        }
    }
    // 异常处理
    if (ret != GMERR_OK) {
        DestroyPathInfoOrGroup(dstOrGroup);
        dstOrGroup = NULL;
    }
    return ret;
}

static Status CopyOutOrGroupSet(DbMemCtxT *destMemCtx, DmPathInfoVertexT *srcPathInfoVertex,
    DmPathInfoVertexT *destPathInfoVertex, DbOamapT *dstVertexLabelId2PathInfoVertex,
    DbOamapT *dstEdgeLabelId2PathInfoEdge)
{
    uint32_t count = DbListGetItemCnt(srcPathInfoVertex->outOrGroupList);
    DmPathInfoOrGroupT **orGroups = DbListGetItems(srcPathInfoVertex->outOrGroupList);
    DmPathInfoOrGroupT *dstPathInfoOrGroup = NULL;
    Status ret = GMERR_OK;
    // 遍历每个或组
    for (uint32_t i = 0; i < count; ++i) {
        ret = DmCreateEmptyPathInfoOrGroupWithMemCtx(destMemCtx, &dstPathInfoOrGroup);
        if (ret != GMERR_OK) {
            break;
        }
        // 深拷贝或组
        ret = CopyPathInfoOrGroup(
            orGroups[i], dstPathInfoOrGroup, dstVertexLabelId2PathInfoVertex, dstEdgeLabelId2PathInfoEdge);
        if (ret != GMERR_OK) {
            break;
        }
        ret = DmAddOrGroupToVertexByDirection(destPathInfoVertex, &dstPathInfoOrGroup, false);
        if (ret != GMERR_OK) {
            break;
        }
    }
    // 异常处理
    if (ret != GMERR_OK) {
        DestroyPathInfoOrGroup(dstPathInfoOrGroup);
        dstPathInfoOrGroup = NULL;
    }
    return ret;
}

static Status CopyOrGroupSets(DbMemCtxT *destMemCtx, DmPathInfoVertexT *srcPathInfoVertex,
    DmPathInfoVertexT *destPathInfoVertex, DbOamapT *dstVertexLabelId2PathInfoVertex,
    DbOamapT *dstEdgeLabelId2PathInfoEdge)
{
    DB_POINTER5(destMemCtx, srcPathInfoVertex, destPathInfoVertex, dstVertexLabelId2PathInfoVertex,
        dstEdgeLabelId2PathInfoEdge);
    Status ret = GMERR_OK;
    // 深拷贝入边或组集合
    if (srcPathInfoVertex->inOrGroupList != NULL) {
        destPathInfoVertex->inOrGroupList = (DbListT *)DbDynMemCtxAlloc(DbGetCurMemCtx(), sizeof(DbListT));
        if (destPathInfoVertex->inOrGroupList == NULL) {
            return GMERR_OUT_OF_MEMORY;
        }
        DbCreateList(destPathInfoVertex->inOrGroupList, sizeof(DmPathInfoOrGroupT *), DbGetCurMemCtx());
        ret = CopyInOrGroupSet(destMemCtx, srcPathInfoVertex, destPathInfoVertex, dstVertexLabelId2PathInfoVertex,
            dstEdgeLabelId2PathInfoEdge);
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    // 深拷贝出边或组集合
    if (srcPathInfoVertex->outOrGroupList != NULL) {
        destPathInfoVertex->outOrGroupList = (DbListT *)DbDynMemCtxAlloc(DbGetCurMemCtx(), sizeof(DbListT));
        if (destPathInfoVertex->outOrGroupList == NULL) {
            return GMERR_OUT_OF_MEMORY;
        }
        DbCreateList(destPathInfoVertex->outOrGroupList, sizeof(DmPathInfoOrGroupT *), DbGetCurMemCtx());
        ret = CopyOutOrGroupSet(destMemCtx, srcPathInfoVertex, destPathInfoVertex, dstVertexLabelId2PathInfoVertex,
            dstEdgeLabelId2PathInfoEdge);
    }
    return ret;
}

static Status CopyEdgeAndOrGroupSets(
    DbMemCtxT *destMemCtx, DbOamapT *srcVertexLabelId2PathInfoVertex, DbOamapT *dstVertexLabelId2PathInfoVertex)
{
    DB_POINTER3(destMemCtx, srcVertexLabelId2PathInfoVertex, dstVertexLabelId2PathInfoVertex);
    Status ret = GMERR_OK;
    DbOamapT *dstEdgeLabelId2PathInfoEdge = (DbOamapT *)DbDynMemCtxAlloc(destMemCtx, sizeof(DbOamapT));
    if (dstEdgeLabelId2PathInfoEdge == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "allocate for dstEdgeLabelId2PathInfoEdge.");
        return GMERR_OUT_OF_MEMORY;
    }
    ret = DbOamapInit(
        dstEdgeLabelId2PathInfoEdge, PATHINFO_EDGE_CACHE_MAP_INIT_CAP, DbOamapUint32Compare, destMemCtx, true);
    if (ret != GMERR_OK) {
        DbOamapDestroy(dstEdgeLabelId2PathInfoEdge);
        DbDynMemFree(dstEdgeLabelId2PathInfoEdge);
        return ret;
    }

    DbOamapIteratorT ite = 0;
    uint32_t *destVid;
    DbListT *srcPathInfoVertexList = NULL;
    DbListT *destPathInfoVertexList = NULL;
    while (DbOamapFetch(dstVertexLabelId2PathInfoVertex, &ite, (void **)&destVid, (void **)&destPathInfoVertexList) ==
           GMERR_OK) {
        srcPathInfoVertexList = DbOamapLookup(srcVertexLabelId2PathInfoVertex, *destVid, destVid, NULL);
        uint32_t cnt = DbListGetItemCnt(destPathInfoVertexList);
        for (uint32_t i = 0; i < cnt; ++i) {
            DmPathInfoVertexT *srcPathInfoVertex = *(DmPathInfoVertexT **)DbListItem(srcPathInfoVertexList, i);
            DmPathInfoVertexT *destPathInfoVertex = *(DmPathInfoVertexT **)DbListItem(destPathInfoVertexList, i);
            // 深拷贝入边和出边集合，会新实例化PiEdge
            ret = CopyEdgeSets(destMemCtx, srcPathInfoVertex, destPathInfoVertex, dstVertexLabelId2PathInfoVertex,
                dstEdgeLabelId2PathInfoEdge);
            if (ret != GMERR_OK) {
                DbOamapDestroy(dstEdgeLabelId2PathInfoEdge);
                DbDynMemFree(dstEdgeLabelId2PathInfoEdge);
                return ret;
            }
            // 深拷贝入边和出边或组集合，会新实例化PiEdge
            ret = CopyOrGroupSets(destMemCtx, srcPathInfoVertex, destPathInfoVertex, dstVertexLabelId2PathInfoVertex,
                dstEdgeLabelId2PathInfoEdge);
            if (ret != GMERR_OK) {
                DbOamapDestroy(dstEdgeLabelId2PathInfoEdge);
                DbDynMemFree(dstEdgeLabelId2PathInfoEdge);
                return ret;
            }
        }
    }

    DbOamapDestroy(dstEdgeLabelId2PathInfoEdge);
    DbDynMemFree(dstEdgeLabelId2PathInfoEdge);
    return ret;
}

static Status CopyPathPattern(DbMemCtxT *dstMemCtx, DmPathPatternT *srcPathPattern, DmPathPatternT *dstPathPattern,
    DbOamapT *vertexLabelId2PathInfoVertexMap)
{
    DB_POINTER4(dstMemCtx, srcPathPattern, dstPathPattern, vertexLabelId2PathInfoVertexMap);
    *dstPathPattern = *srcPathPattern;
    dstPathPattern->root = NULL;

    if (srcPathPattern->root == NULL) {
        return GMERR_OK;
    }
    DbListT *dstPathInfoVertexList = DbOamapLookup(vertexLabelId2PathInfoVertexMap, srcPathPattern->root->vertexLabelId,
        &(srcPathPattern->root->vertexLabelId), NULL);
    // 在头点无环约束场景下，root节点只能有一个，且root节点的variable name唯一
    uint32_t cnt = DbListGetItemCnt(dstPathInfoVertexList);
    for (uint32_t i = 0; i < cnt; ++i) {
        DmPathInfoVertexT *dstPathInfoVertex = *(DmPathInfoVertexT **)DbListItem(dstPathInfoVertexList, i);
        if (strcmp(srcPathPattern->root->pathInfoVertexName.str, dstPathInfoVertex->pathInfoVertexName.str) == 0) {
            dstPathPattern->root = dstPathInfoVertex;
            break;
        }
    }
    return GMERR_OK;
}

Status DmCreateEmptyVertexLabelId2PathInfoVertexWithMemCtx(
    DbMemCtxT *memCtx, DbOamapT **vertexLabelId2PathInfoVertexMap)
{
    DB_POINTER2(memCtx, vertexLabelId2PathInfoVertexMap);
    DbOamapT *hashT = (DbOamapT *)DbDynMemCtxAlloc(memCtx, sizeof(DbOamapT));
    if (hashT == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "allocate for vertexLabelId2PathInfoVertexMap with memCtx.");
        return GMERR_OUT_OF_MEMORY;
    }
    *vertexLabelId2PathInfoVertexMap = hashT;

    return DbOamapInit(hashT, PATHINFO_VERTEX_CACHE_MAP_INIT_CAP, DbOamapUint32Compare, memCtx, true);
}

Status DmAddPathInfoVertexToVertexLabelId2PathInfoVertexMap(
    DbMemCtxT *memCtx, DbOamapT *vertexLabelId2PathInfoVertexMap, DmPathInfoVertexT **pathInfoVertex)
{
    DB_POINTER3(memCtx, vertexLabelId2PathInfoVertexMap, pathInfoVertex);
    Status ret = GMERR_OK;
    // 查找key是否存在
    uint32_t vertexLabelId = (*pathInfoVertex)->vertexLabelId;
    DbListT *value = DbOamapLookup(vertexLabelId2PathInfoVertexMap, vertexLabelId, &vertexLabelId, NULL);
    // 若存在，则插入；否则，新建<Key,Value>再插入
    if (value == NULL) {
        // 不存在
        DbListT *tempList = (DbListT *)DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
        if (tempList == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
                "alloc for list, size is "
                "%" PRIu32 ".",
                (uint32_t)sizeof(DbListT));
            return GMERR_OUT_OF_MEMORY;
        }
        DbCreateList(tempList, sizeof(DmPathInfoVertexT *), memCtx);
        ret = DbAppendListItem(tempList, pathInfoVertex);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "add PathInfoVertex to VertexLabelId2PathInfoVertexMap.");
            DbDestroyList(tempList);
            DbDynMemCtxFree(memCtx, tempList);
            return ret;
        }
        uint32_t *key = (uint32_t *)DbDynMemCtxAlloc(memCtx, sizeof(uint32_t));
        if (key == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY,
                "alloc for key, size is "
                "%" PRIu32 ".",
                (uint32_t)sizeof(uint32_t));
            DbDestroyList(tempList);
            DbDynMemCtxFree(memCtx, tempList);
            return GMERR_OUT_OF_MEMORY;
        }
        *key = vertexLabelId;
        ret = DbOamapInsert(vertexLabelId2PathInfoVertexMap, *key, key, tempList, NULL);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "insert pathinfovertex into vertexLabelId2PathInfoVertexMap.");
            DbDynMemCtxFree(memCtx, key);
            DbDestroyList(tempList);
            DbDynMemCtxFree(memCtx, tempList);
            return ret;
        }
        return ret;
    }
    // 存在
    ret = DbAppendListItem(value, pathInfoVertex);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "add PathInfoVertex to VertexLabelId2PathInfoVertexMap.");
        return ret;
    }
    return ret;
}

static Status CopyPathName(const DmComplexPathInfoT *srcPathInfo, DmComplexPathInfoT *dstPathInfo)
{
    if (srcPathInfo->metaCommon.metaName != NULL) {
        uint32_t strLen = DM_STR_LEN(srcPathInfo->metaCommon.metaName);
        dstPathInfo->metaCommon.metaName = (char *)DbDynMemCtxAlloc(dstPathInfo->memCtx, strLen);
        if (dstPathInfo->metaCommon.metaName == NULL) {
            return GMERR_OUT_OF_MEMORY;
        }
        errno_t err = strcpy_s(dstPathInfo->metaCommon.metaName, strLen, srcPathInfo->metaCommon.metaName);
        if (err != EOK) {
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }
    return GMERR_OK;
}

static Status CopySubFieldsInner(DbMemCtxT *dstMemCtx, DmVertexSubFieldsT *dst, DmVertexSubFieldsT *src)
{
    Status ret = GMERR_OK;
    DmSubFieldT dstFields;
    DmSubFieldT *srcFields = NULL;
    uint32_t cnt = DbListGetItemCnt(&src->subFields);
    DbCreateList(&dst->subFields, sizeof(DmSubFieldT), dstMemCtx);
    for (uint32_t i = 0; i < cnt; i++) {
        srcFields = (DmSubFieldT *)DbListItem(&src->subFields, i);
        dstFields.fieldId = srcFields->fieldId;
        dstFields.fieldName = (TextT *)DbDynMemCtxAlloc(dstMemCtx, sizeof(TextT));
        if (dstFields.fieldName == NULL) {
            return GMERR_OUT_OF_MEMORY;
        }
        ret = DbCopyStrToText(dstMemCtx, dstFields.fieldName, srcFields->fieldName->str, srcFields->fieldName->len - 1);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (srcFields->specifyPathName != NULL) {
            uint32_t strLen = (uint32_t)strlen(srcFields->specifyPathName);
            dstFields.specifyPathName = (char *)DbDynMemCtxAlloc(dstMemCtx, strLen + 1);
            if (dstFields.specifyPathName == NULL) {
                DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc for specifypathname.");
                return GMERR_OUT_OF_MEMORY;
            }
            errno_t err = memcpy_s(dstFields.specifyPathName, strLen + 1, srcFields->specifyPathName, strLen + 1);
            if (err != EOK) {
                DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy specifypathname, specifypathname is %s.",
                    srcFields->specifyPathName);
                return GMERR_MEMORY_OPERATE_FAILED;
            }
            dstFields.specifyPathId = srcFields->specifyPathId;
        } else {
            dstFields.specifyPathName = NULL;
            dstFields.specifyPathId = DB_INVALID_ID32;
        }
        ret = DmAddSubFieldsToVertexSubFields(dst, &dstFields);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status CopySubVertexName(DbMemCtxT *dstMemCtx, DmVertexSubFieldsT *dst, DmVertexSubFieldsT *src)
{
    dst->vertexLabelName = (TextT *)DbDynMemCtxAlloc(dstMemCtx, sizeof(TextT));
    if (dst->vertexLabelName == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "allocate for copy vertex name.");
        return GMERR_OUT_OF_MEMORY;
    }
    return DbCopyStrToText(dstMemCtx, dst->vertexLabelName, src->vertexLabelName->str, src->vertexLabelName->len - 1);
}

static Status CopyVertexSubFields(DbMemCtxT *dstMemCtx, DbOamapT **dstMap, DbOamapT *srcMap)
{
    if (srcMap == NULL) {
        return GMERR_OK;
    }
    uint32_t iter = 0;
    uint32_t *destVid = NULL;
    DmVertexSubFieldsT *src = NULL;
    DmVertexSubFieldsT *dst = NULL;
    DbOamapT *tmpMap = (DbOamapT *)DbDynMemCtxAlloc(dstMemCtx, sizeof(DbOamapT));
    if (tmpMap == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "allocate for copy vertex subFields.");
        return GMERR_OUT_OF_MEMORY;
    }
    Status ret = DbOamapInit(tmpMap, 0, DbOamapUint32Compare, dstMemCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (DbOamapFetch(srcMap, &iter, (void **)&destVid, (void **)&src) == GMERR_OK) {
        ret = DmCreateEmptyVertexSubFieldsWithMemCtx(dstMemCtx, &dst);
        if (ret != GMERR_OK) {
            return ret;
        }
        dst->vertexLabelId = src->vertexLabelId;
        dst->encapType = src->encapType;
        ret = CopySubVertexName(dstMemCtx, dst, src);
        if (ret != GMERR_OK) {
            return ret;
        }
        /* 拷贝fields */
        ret = CopySubFieldsInner(dstMemCtx, dst, src);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DbOamapInsert(tmpMap, dst->vertexLabelId, &dst->vertexLabelId, dst, NULL);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    *dstMap = tmpMap;
    return GMERR_OK;
}

static Status CopyComplexInfoSubFields(
    DbMemCtxT *dstMemCtx, const DmComplexPathInfoT *srcPathInfo, DmComplexPathInfoT *dstPathInfo)
{
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < DM_SUBS_COMPLEXPATH_CEIL; i++) {
        ret = CopyVertexSubFields(
            dstMemCtx, &dstPathInfo->vertexLabelId2SubFieldsMap[i], srcPathInfo->vertexLabelId2SubFieldsMap[i]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

static Status CopyDmVertexLabelAndFieldTupleT(
    DbMemCtxT *dstMemCtx, const DmVertexLabelAndFieldTupleT *srcTuple, DmVertexLabelAndFieldTupleT **dstTuple)
{
    *dstTuple = (DmVertexLabelAndFieldTupleT *)DbDynMemCtxAlloc(dstMemCtx, sizeof(DmVertexLabelAndFieldTupleT));
    if (*dstTuple == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc for pidField.");
        return GMERR_OUT_OF_MEMORY;
    }
    *(*dstTuple) = *(srcTuple);
    // copy vertexLabelName
    Status ret = DbCopyStrToText(
        dstMemCtx, &(*dstTuple)->vertexLabelName, srcTuple->vertexLabelName.str, srcTuple->vertexLabelName.len - 1);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "copy vtxLabelName: %s.", srcTuple->vertexLabelName.str);
        return ret;
    }
    // copy fieldName
    ret = DbCopyStrToText(dstMemCtx, &(*dstTuple)->fieldName, srcTuple->fieldName.str, srcTuple->fieldName.len - 1);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "copy fieldName: %s.", srcTuple->fieldName.str);
        return ret;
    }
    return GMERR_OK;
}

static Status CopyPIDField(DbMemCtxT *dstMemCtx, const DmComplexPathInfoT *srcPathInfo, DmComplexPathInfoT *dstPathInfo)
{
    if (srcPathInfo->pidField == NULL) {
        dstPathInfo->pidField = NULL;
        return GMERR_OK;
    }
    return CopyDmVertexLabelAndFieldTupleT(dstMemCtx, srcPathInfo->pidField, &dstPathInfo->pidField);
}

static Status CopyCoalesceClause(DbMemCtxT *dstMemCtx, DmSetClauseT *srcClause, DmCoalesceClauseT **coalesceClause)
{
    *coalesceClause = (DmCoalesceClauseT *)DbDynMemCtxAlloc(dstMemCtx, sizeof(DmCoalesceClauseT));
    if (*coalesceClause == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc for coalesceClause.");
        return GMERR_OUT_OF_MEMORY;
    }
    Status ret = CopyDmVertexLabelAndFieldTupleT(
        dstMemCtx, srcClause->coalesceClause->coalesceLeftProp, &(*coalesceClause)->coalesceLeftProp);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CopyDmVertexLabelAndFieldTupleT(
        dstMemCtx, srcClause->coalesceClause->coalesceRightProp, &(*coalesceClause)->coalesceRightProp);
    return ret;
}

static Status CopyMergeAndReplaceFields(
    DbMemCtxT *dstMemCtx, const DmComplexPathInfoT *srcPathInfo, DmComplexPathInfoT *dstPathInfo)
{
    dstPathInfo->hasReplaceRootOrMergeView = srcPathInfo->hasReplaceRootOrMergeView;

    if (srcPathInfo->generatePathInfo == NULL) {
        dstPathInfo->generatePathInfo = NULL;
        return GMERR_OK;
    }
    dstPathInfo->generatePathInfo = (DmGeneratePathInfoT *)DbDynMemCtxAlloc(dstMemCtx, sizeof(DmGeneratePathInfoT));
    if (dstPathInfo->generatePathInfo == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc for generatePathInfo.");
        return GMERR_OUT_OF_MEMORY;
    }

    DmGeneratePathInfoT *srcGeneratePathInfo = srcPathInfo->generatePathInfo,
                        *dstGeneratePathInfo = dstPathInfo->generatePathInfo;
    dstGeneratePathInfo->viewTableId = srcGeneratePathInfo->viewTableId;
    uint32_t clausesCnt = DbListGetItemCnt(&srcGeneratePathInfo->setClauses);
    dstGeneratePathInfo->setClauses = (DbListT){};
    DbCreateList(&dstGeneratePathInfo->setClauses, sizeof(DmSetClauseT), dstMemCtx);
    for (uint32_t i = 0; i < clausesCnt; i++) {
        DmSetClauseT *dstClause = (DmSetClauseT *)DbDynMemCtxAlloc(dstMemCtx, sizeof(DmSetClauseT));
        if (dstClause == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc for dstClause.");
            return GMERR_OUT_OF_MEMORY;
        }
        DmSetClauseT *srcClause = (DmSetClauseT *)DbListItem(&srcGeneratePathInfo->setClauses, i);
        dstClause->tag = srcClause->tag;
        Status ret = CopyDmVertexLabelAndFieldTupleT(dstMemCtx, srcClause->leftProp, &dstClause->leftProp);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (srcClause->tag == DM_SET_CLAUSE_PROPERTY) {
            ret = CopyDmVertexLabelAndFieldTupleT(dstMemCtx, srcClause->rightProp, &dstClause->rightProp);
            if (ret != GMERR_OK) {
                return ret;
            }
        } else if (srcClause->tag == DM_SET_CLAUSE_COALESCE_FUNC) {
            ret = CopyCoalesceClause(dstMemCtx, srcClause, &dstClause->coalesceClause);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        ret = DbAppendListItem(&dstGeneratePathInfo->setClauses, dstClause);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "append set clause.");
            return GMERR_OUT_OF_MEMORY;
        }
    }
    return GMERR_OK;
}

static Status CopyPathConstraint(
    DbMemCtxT *dstMemCtx, const DmComplexPathInfoT *srcPathInfo, DmComplexPathInfoT *dstPathInfo)
{
    DB_POINTER2(srcPathInfo, dstPathInfo);
    if (srcPathInfo->constraint == NULL) {
        return GMERR_OK;
    }
    DmPathConstraintT *dstConstraint = NULL;
    Status ret = DmCreateEmptyPathConstraintWithMemCtx(dstMemCtx, &dstConstraint);
    if (ret != GMERR_OK) {
        return ret;
    }
    dstConstraint->vertexLabelId = srcPathInfo->constraint->vertexLabelId;
    uint32_t condNum = DbListGetItemCnt(&srcPathInfo->constraint->conditions);
    for (uint32_t i = 0; i < condNum; ++i) {
        DmPathConditionT *srcCond = (DmPathConditionT *)DbListItem(&srcPathInfo->constraint->conditions, i);
        DmPathConditionT *dstCond = (DmPathConditionT *)DbDynMemCtxAlloc(dstMemCtx, sizeof(DmPathConditionT));
        if (dstCond == NULL) {
            // 内存释放点：前面[0,i-1]的dstCond内存释放统一在DmCopyComplexPathInfo异常分支中处理
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc dstCond, size is %zu.", sizeof(DmPathConditionT));
            return GMERR_OUT_OF_MEMORY;
        }
        dstCond->propId = srcCond->propId;
        ret = DmValueCopy(dstMemCtx, &srcCond->value, &dstCond->value);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DbAppendListItem(&dstConstraint->conditions, dstCond);
        if (ret != GMERR_OK) {
            DmValueFreeInner(&dstCond->value);
            DbDynMemFree(dstCond);
            return ret;
        }
    }
    dstPathInfo->constraint = dstConstraint;
    return GMERR_OK;
}

static Status CopyComplexPathInfoHelper(
    DbMemCtxT *memCtx, const DmComplexPathInfoT *srcPathInfo, DmComplexPathInfoT *dstPathInfo)
{
    // 内存释放点:此函数内申请的内存,由外面调用者统一释放
    Status ret = DmCreateEmptyPathPatternWithMemCtx(memCtx, &dstPathInfo->pattern);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CopyPathPattern(
        memCtx, srcPathInfo->pattern, dstPathInfo->pattern, dstPathInfo->vertexLabelId2PathInfoVertexMap);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CopyComplexInfoSubFields(memCtx, srcPathInfo, dstPathInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CopyPIDField(memCtx, srcPathInfo, dstPathInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CopyMergeAndReplaceFields(memCtx, srcPathInfo, dstPathInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CopyPathConstraint(memCtx, srcPathInfo, dstPathInfo);
}

Status DmCopyComplexPathInfo(const DmComplexPathInfoT *srcPathInfo, DmComplexPathInfoT *dstPathInfo)
{
    DB_POINTER2(srcPathInfo, dstPathInfo);
    DbMemCtxT *oldMemCtx = NULL;
    if (dstPathInfo->memCtx != NULL) {
        oldMemCtx = DbMemCtxSwitchTo(dstPathInfo->memCtx);
    }
    DbMemCtxT *tmpMemCtx = dstPathInfo->memCtx;
    *dstPathInfo = *srcPathInfo;
    InitPathInfo2NonInnerMem(dstPathInfo);
    dstPathInfo->memCtx = tmpMemCtx;

    // deep copy pathInfoName
    Status ret = CopyPathName(srcPathInfo, dstPathInfo);
    // deep copy creator
    if (ret == GMERR_OK) {
        ret = DmStrAllocAndCopy(srcPathInfo->creator, GetStrLen(srcPathInfo->creator), &dstPathInfo->creator);
    }
    // deep copy vertexLabelId2PathInfoVertexMap
    if (ret == GMERR_OK) {
        ret = DmCreateEmptyVertexLabelId2PathInfoVertexWithMemCtx(
            tmpMemCtx, &(dstPathInfo->vertexLabelId2PathInfoVertexMap));
    }
    if (ret == GMERR_OK) {
        ret = CopyVertexLabelId2PathInfoVertexMap(
            tmpMemCtx, srcPathInfo->vertexLabelId2PathInfoVertexMap, dstPathInfo->vertexLabelId2PathInfoVertexMap);
    }
    // deep copy edge sets and orgroups sets in pivertex, pivertex must has existed
    if (ret == GMERR_OK) {
        ret = CopyEdgeAndOrGroupSets(
            tmpMemCtx, srcPathInfo->vertexLabelId2PathInfoVertexMap, dstPathInfo->vertexLabelId2PathInfoVertexMap);
    }
    // deep copy path pattern
    if (ret == GMERR_OK) {
        ret = CopyComplexPathInfoHelper(tmpMemCtx, srcPathInfo, dstPathInfo);
    }
    // 异常处理
    if (ret != GMERR_OK) {
        DmDestroyComplexPathInfo(dstPathInfo);
    }

    (void)DbMemCtxSwitchBack(oldMemCtx, dstPathInfo->memCtx);
    return ret;
}

Status DmCreateEmptyPathPatternWithMemCtx(DbMemCtxT *memCtx, DmPathPatternT **pathPattern)
{
    DB_POINTER2(memCtx, pathPattern);
    uint32_t memSize = (uint32_t)sizeof(DmPathPatternT);
    *pathPattern = (DmPathPatternT *)DbDynMemCtxAlloc(memCtx, memSize);
    if (*pathPattern == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc path pattern.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(*pathPattern, memSize, 0x00, memSize);
    return GMERR_OK;
}

Status DmCreateEmptyVertexSubFieldsWithMemCtx(DbMemCtxT *memCtx, DmVertexSubFieldsT **pathInfoVertexFields)
{
    DB_POINTER2(memCtx, pathInfoVertexFields);
    uint32_t memSize = (uint32_t)sizeof(DmVertexSubFieldsT);
    DmVertexSubFieldsT *tmp = (DmVertexSubFieldsT *)DbDynMemCtxAlloc(memCtx, memSize);
    if (tmp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc vertex subfields.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(tmp, memSize, 0x00, memSize);
    *pathInfoVertexFields = tmp;
    return GMERR_OK;
}

Status DmAddSubFieldsToVertexSubFields(DmVertexSubFieldsT *pathInfoVertexFields, DmSubFieldT *pathInfoSubFields)
{
    DB_POINTER2(pathInfoSubFields, pathInfoVertexFields);
    return DbAppendListItem(&pathInfoVertexFields->subFields, pathInfoSubFields);
}

Status DmCopyPathPatternInfo(
    DbMemCtxT *memCtx, const DmPathPatternInfoT *srcPathPatternInfo, DmPathPatternInfoT *destPathPatternInfo)
{
    *destPathPatternInfo = *srcPathPatternInfo;
    destPathPatternInfo->pathInfoName = NULL;  // 避免外层free出错
    uint32_t size = destPathPatternInfo->pathInfoNameLen;
    char *newName = (char *)DbDynMemCtxAlloc(memCtx, size);
    if (newName == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_OUT_OF_MEMORY, "deepcopy pathpatterninfo, memSize:%" PRIu32 "", destPathPatternInfo->pathInfoNameLen);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = memcpy_s(newName, size, srcPathPatternInfo->pathInfoName, size);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "copy edgename, edgename:%s", srcPathPatternInfo->pathInfoName);
        DbDynMemCtxFree(memCtx, newName);
        return GMERR_OUT_OF_MEMORY;
    }
    destPathPatternInfo->pathInfoName = newName;
    return GMERR_OK;
}

bool DmIsExistVertexLabelInPathPattern(const DmComplexPathInfoT *pathInfo, uint32_t vtxLabelId)
{
    DB_POINTER(pathInfo);
    DbListT *pathInfoVertexList =
        DbOamapLookup(pathInfo->vertexLabelId2PathInfoVertexMap, vtxLabelId, &vtxLabelId, NULL);
    if (pathInfoVertexList == NULL) {
        return false;
    }
    return true;
}

Status DmCreateEmptyPathConstraintWithMemCtx(DbMemCtxT *memCtx, DmPathConstraintT **constraint)
{
    DB_POINTER2(memCtx, constraint);
    uint32_t memSize = (uint32_t)sizeof(DmPathConstraintT);
    DmPathConstraintT *tmp = (DmPathConstraintT *)DbDynMemCtxAlloc(memCtx, memSize);
    if (tmp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc pathconstraint, size: %u.", memSize);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(tmp, memSize, 0x00, memSize);
    DbCreateList(&tmp->conditions, sizeof(DmPathConditionT), memCtx);
    *constraint = tmp;
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
