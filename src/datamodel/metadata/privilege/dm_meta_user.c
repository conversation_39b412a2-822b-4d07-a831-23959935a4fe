/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: user cache
 * Author: optimizer team
 * Create: 2021-05-28
 */

#include "dm_meta_user.h"
#include "dm_data_basic.h"
#include "dm_meta_basic.h"
#include "dm_meta_role.h"
#include "dm_meta_basic_in.h"
#include "dm_cache_multi_ver_mgr.h"
#include "dm_meta_namespace.h"
#include "dm_meta_priv_oper.h"
#include "dm_meta_res_col_pool.h"
#include "dm_cache_single_ver_mgr.h"
#include "dm_meta_udf.h"
#include "dm_meta_priv.h"
#include "dm_meta_obj_priv.h"

#include "dm_meta_log.h"
#include "dm_meta_common.h"
#include "adpt_pipe.h"
#include "dm_log.h"

#ifdef __cplusplus
extern "C" {
#endif

inline static Status CopyStrToText(const char *str, TextT *text)
{
    errno_t err = strcpy_s(text->str, text->len, str);
    if (err != EOK) {
        // 内部错误,后续一并修改
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

// isGroup 表示 privEntity 是 user 或者 group，isGroupCreate 表示元数据是由 user 或者 group 创建
void IncrCreatedMetaNum(
    char *creator, CataPrivEntityT *privEntity, bool isGroup, CataUserMetaTypeE metaType, bool isGroupCreate)
{
    if (creator == NULL || isGroup != isGroupCreate) {
        return;
    }
    if (strcmp(privEntity->metaCommon.metaName, creator) == 0) {
        privEntity->metaCreated[metaType]++;
    }
}

static void InitCreatedMultiVersionMetaNum(CataPrivEntityT *privEntity, bool isGroup)
{
    DmVertexLabelT *vertexLabel = NULL;
    DbOamapIteratorT iter = 0;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(privEntity->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    while (MultiVersionFetchMinLabel(GetMetaCache(cataCacheMgr, CATA_VL), &iter, (DmMetaCommonT **)&vertexLabel) ==
           GMERR_OK) {
        bool isGroupCreate = vertexLabel->commonInfo->isGroupCreate;
        if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_YANG) {
            IncrCreatedMetaNum(vertexLabel->commonInfo->creator, privEntity, isGroup, USER_META_YANG_VL, isGroupCreate);
        }
        if (vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_NORMAL ||
            vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_DATALOG) {
            IncrCreatedMetaNum(
                vertexLabel->commonInfo->creator, privEntity, isGroup, USER_META_NORMAL_VL, isGroupCreate);
        }
    }

    DmUdfBaseT *udf = NULL;
    iter = 0;
    while (MultiVersionFetchMinLabel(cataCacheMgr->metaCache[CATA_UDF], &iter, (DmMetaCommonT **)&udf) == GMERR_OK) {
        IncrCreatedMetaNum(
            udf->multiVersionShare->creator, privEntity, isGroup, USER_META_UDF, udf->multiVersionShare->isGroupCreate);
    }
}

static void InitCreatedSingleVersionMetaNum(CataPrivEntityT *privEntity, bool isGroup)
{
    uint32_t *id = NULL;
    DmMetaCommonT *metaCommon = NULL;
    DbOamapIteratorT iter = 0;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(privEntity->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    while (SingleVersionFetch(cataCacheMgr->metaCache[CATA_KV], &iter, IsMetaUndeleted, &id, &metaCommon) == GMERR_OK) {
        DmKvLabelT *kvLabel = (DmKvLabelT *)metaCommon;
        IncrCreatedMetaNum(kvLabel->creator, privEntity, isGroup, USER_META_KV, kvLabel->isGroupCreate);
    }

    iter = 0;
    while (SingleVersionFetch(cataCacheMgr->metaCache[CATA_EL], &iter, IsMetaUndeleted, &id, &metaCommon) == GMERR_OK) {
        DmEdgeLabelT *edgeLabel = (DmEdgeLabelT *)metaCommon;
        IncrCreatedMetaNum(edgeLabel->creator, privEntity, isGroup, USER_META_EDGE, edgeLabel->isGroupCreate);
    }

    iter = 0;
    while (
        SingleVersionFetch(cataCacheMgr->metaCache[CATA_SUB], &iter, IsMetaUndeleted, &id, &metaCommon) == GMERR_OK) {
        DmSubscriptionT *subscription = (DmSubscriptionT *)metaCommon;
        IncrCreatedMetaNum(subscription->creator, privEntity, isGroup, USER_META_SUBS, subscription->isGroupCreate);
    }

    iter = 0;
    while (SingleVersionFetch(cataCacheMgr->metaCache[CATA_RES_COL], &iter, IsMetaUndeleted, &id, &metaCommon) ==
           GMERR_OK) {
        DmResColPoolT *resColPool = (DmResColPoolT *)metaCommon;
        IncrCreatedMetaNum(resColPool->creator, privEntity, isGroup, USER_META_RESPOOL, resColPool->isGroupCreate);
    }

    iter = 0;
    while (
        SingleVersionFetch(cataCacheMgr->metaCache[CATA_NSP], &iter, IsMetaUndeleted, &id, &metaCommon) == GMERR_OK) {
        DmNamespaceT *namespace = (DmNamespaceT *)metaCommon;
        IncrCreatedMetaNum(namespace->creator, privEntity, isGroup, USER_META_NSP, namespace->isGroupCreate);
    }

    iter = 0;
    while (
        SingleVersionFetch(cataCacheMgr->metaCache[CATA_TSP], &iter, IsMetaUndeleted, &id, &metaCommon) == GMERR_OK) {
        DmTablespaceT *tablespace = (DmTablespaceT *)metaCommon;
        IncrCreatedMetaNum(tablespace->creator, privEntity, isGroup, USER_META_TSP, tablespace->isGroupCreate);
    }
}

static void InitPrivEntityCreatedMetaNum(CataPrivEntityT *privEntity, bool isGroup)
{
    InitCreatedMultiVersionMetaNum(privEntity, isGroup);
    InitCreatedSingleVersionMetaNum(privEntity, isGroup);
}

static void SetOtherPrivInfo(CataPrivEntityT **privEntity, const CataUserInfoT *cataUserInfo, size_t metaNameLen)
{
    char *userOrGroupName = (*privEntity)->userOrGrpName.str;
#ifdef EXPERIMENTAL_NERGC
    errno_t err = strcpy_s((*privEntity)->metaCommon.metaName, strlen(userOrGroupName) + 1, userOrGroupName);
    DB_ASSERT(err == 0);
#else
    const char *format = USER_ROLE_META_NAME_FORMAT;
    int32_t printLen = sprintf_s((*privEntity)->metaCommon.metaName, metaNameLen, format, userOrGroupName,
        cataUserInfo->userNameInfo.processName);
    DB_ASSERT(printLen == (int32_t)(metaNameLen - 1));
#endif
}

Status GeneratePrivEntity(
    DbMemCtxT *memCtx, const CataUserInfoT *cataUserInfo, CreatePrivEntityPara *para, CataPrivEntityT **privEntity)
{
    bool isUser = para->isUser;
    char *userOrGroupName = isUser ? cataUserInfo->userNameInfo.userName : cataUserInfo->userNameInfo.groupName;
    size_t privEntityNameLen = strlen(userOrGroupName) + 1;
    size_t processNameLen = strlen(cataUserInfo->userNameInfo.processName) + 1;
    // metaNameLen -> userName + : + processName + \0
    size_t metaNameLen = privEntityNameLen + processNameLen;
    size_t memSize = sizeof(CataPrivEntityT) + sizeof(uint32_t) + privEntityNameLen + processNameLen + metaNameLen;
    // 内存释放：若后续函数出错则在本函数内释放，否则由调用者负责管理，有如下两种情况：
    // （1）外层调用者后续操作失败则释放此内存（2）存储在Catalog中直至该用户被删除或DB进程结束
    // 并发：不涉及并发
    *privEntity = (CataUserT *)DbDynMemCtxAlloc(memCtx, memSize);
    if (*privEntity == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "%s:%s, process:%s, alloc size:%zu.",
            GetCataMetaTypeName(isUser ? CATA_USR : CATA_GROUP), userOrGroupName,
            cataUserInfo->userNameInfo.processName, memSize);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(*privEntity, memSize, 0x00, memSize);

    (*privEntity)->roleIds = (uint32_t *)(*privEntity + 1);
    (*privEntity)->userOrGrpName.str = (char *)(*privEntity)->roleIds + sizeof(uint32_t);
    (*privEntity)->userOrGrpName.len = (uint32_t)privEntityNameLen;
    (*privEntity)->processName.str = (*privEntity)->userOrGrpName.str + privEntityNameLen;
    (*privEntity)->processName.len = (uint32_t)processNameLen;
    (*privEntity)->metaCommon.metaName = (*privEntity)->processName.str + processNameLen;
    (*privEntity)->memCtx = memCtx;
    (*privEntity)->roleNum = 1;
    (*privEntity)->roleIds[0] = cataUserInfo->roleId;
    //  代码逻辑保证了CopyStrToText不会因为长度校验而失败
    (void)CopyStrToText(userOrGroupName, &((*privEntity)->userOrGrpName));
    (void)CopyStrToText(cataUserInfo->userNameInfo.processName, &((*privEntity)->processName));
    SetOtherPrivInfo(privEntity, cataUserInfo, metaNameLen);
    (*privEntity)->metaCommon.metaId = cataUserInfo->userId;
    (*privEntity)->isDBA = cataUserInfo->isDBA;
    (*privEntity)->reservedConnNum = cataUserInfo->reservedConnNum;
    if (isUser) {
        (*privEntity)->weight = 1;
        for (uint8_t i = 0; i < PRIV_PROTOCOL_TYPE_NUM; i++) {
            (*privEntity)->userProtocol |= (uint8_t)(((uint8_t)1) << i);
        }
    }
    for (uint32_t i = 0; i < USER_META_TYPE_NUM_N; i++) {
        (*privEntity)->metaLimit[i] =
            (*privEntity)->isDBA ? (uint16_t)DB_MAX_UINT16 : cataUserInfo->userNameInfo.metaLimit[i];
        (*privEntity)->metaCreated[i] = 0;
    }
    InitPrivEntityCreatedMetaNum(*privEntity, !isUser);
    return GMERR_OK;
}

Status CheckPrivEntityForCreate(
    DbInstanceHdT dbInstance, CataMetaTypeE metaType, char *metaName, CataUserInfoT *cataUserInfo, bool isUser)
{
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, USER_ROLE_DEFAULT_NAMESPACE_ID, metaName);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    // 如果已经存在则直接报错返回
    if (SingleVersionLabelNameExist(GetMetaCache(cataCacheMgr, metaType), &cataKey)) {
        DB_LOG_AND_SET_LASERR(GMERR_DUPLICATE_OBJECT, "user or group object, name: %s.", cataKey.labelName);
        return GMERR_DUPLICATE_OBJECT;
    }
    if (SingleVersionLabelIdExist(GetMetaCache(cataCacheMgr, metaType), cataUserInfo->userId)) {
        DB_LOG_AND_SET_LASERR(GMERR_DUPLICATE_OBJECT, "user id:%" PRIu32 ".", cataUserInfo->userId);
        return GMERR_DUPLICATE_OBJECT;
    }
    return GMERR_OK;
}

void DestroyPrivEntity(CataPrivEntityT *privEntity)
{
    if (privEntity == NULL) {
        return;
    }
    // 因为 CataPrivEntityT 是一整块申请的内存，所以一同进行 free
    DbDynMemCtxFree(privEntity->memCtx, privEntity);
}

static Status CreatePrivEntityInner(const CataUserInfoT *cataUserInfo, CreatePrivEntityPara *para)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(para->dbInstance);
    CataPrivEntityT *privEntity = NULL;
    Status ret = GeneratePrivEntity((DbMemCtxT *)cataCacheMgr->dynMemCtx, cataUserInfo, para, &privEntity);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "The %s name is %s, processName is %s.", para->userOrGroupStr, para->roleName, para->processName);
        return ret;
    }
    ret = SingleVersionPut(para->dbInstance, GetMetaCache(cataCacheMgr, para->metaType), (DmMetaCommonT *)privEntity);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(
            ret, "The %s name is %s, processName is %s.", para->userOrGroupStr, para->roleName, para->processName);
        DestroyPrivEntity(privEntity);
        return ret;
    }
    // 重启流程从系统表中恢复 role 元数据，不随 user 的创建而创建
    if (cataUserInfo->isReboot) {
        return GMERR_OK;
    }

    para->roleId = privEntity->roleIds[0];
    // 构建同名的角色
    ret = CreateRoleNoLock(para);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "The %s name is %s,, processName is %s.", para->userOrGroupStr, para->roleName, para->processName);
        privEntity->metaCommon.isDeleted = true;
        Status tmpRet = SingleVersionRemove(GetMetaCache(cataCacheMgr, para->metaType), (DmMetaCommonT *)privEntity);
        DB_ASSERT(tmpRet == GMERR_OK);  // 前面 SingleVersionPut 成功，此处remove不会失败
        DestroyPrivEntity(privEntity);
    }
    return ret;
}

static Status CreatePrivEntity(DbInstanceHdT dbInstance, CataUserInfoT *cataUserInfo, bool isUser)
{
#ifdef EXPERIMENTAL_NERGC
    if (!isUser) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "The name is %s, not usr!", cataUserInfo->userNameInfo.userName);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
#endif
    CreatePrivEntityPara para = {
        .metaType = isUser ? CATA_USR : CATA_GROUP,
        .userOrGroupStr = GetCataMetaTypeName(isUser ? CATA_USR : CATA_GROUP),
        .roleName = isUser ? cataUserInfo->userNameInfo.userName : cataUserInfo->userNameInfo.groupName,
        .processName = cataUserInfo->userNameInfo.processName,
        .isUser = isUser,
        .isDBA = cataUserInfo->isDBA,
        .dbInstance = dbInstance,
    };
    char metaName[USER_ROLE_META_NAME_MAX_LEN] = {0};
    Status ret = CataBuildUserName(metaName, sizeof(metaName), para.roleName, para.processName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "The %s name is %s, processName is %s.", para.userOrGroupStr, para.roleName, para.processName);
        return ret;
    }
    ret = CheckPrivEntityForCreate(dbInstance, para.metaType, metaName, cataUserInfo, isUser);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CreatePrivEntityInner(cataUserInfo, &para);
}

Status CataCreateUser(DbInstanceHdT dbInstance, CataUserInfoT *cataUserInfo)
{
    DB_POINTER3(cataUserInfo, cataUserInfo->userNameInfo.userName, cataUserInfo->userNameInfo.processName);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    Status ret = CreatePrivEntity(dbInstance, cataUserInfo, true);
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CataCreateGroup(DbInstanceHdT dbInstance, CataUserInfoT *cataUserInfo)
{
    DB_POINTER3(cataUserInfo, cataUserInfo->userNameInfo.groupName, cataUserInfo->userNameInfo.processName);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    // group 一定不是 DBA
    DB_ASSERT(!cataUserInfo->isDBA);
    Status ret = CreatePrivEntity(dbInstance, cataUserInfo, false);
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

/**
 * @brief: 根据用户（组）名和进程名获取到用户（组）结构体
 * @note: 该函数不对外暴露，只在catalog内部用于获取user结构体信息
 * @dbInstance: [in], 实例
 * @userNameInfo: [in], 用户（组）名称信息结构体
 * @isUser: [in], 用户或用户组
 * @user: [out], privEntity 结构体
 * @return: success或错误码
 * */
Status GetPrivEntityByName(
    DbInstanceHdT dbInstance, const CataUserNameInfoT *userNameInfo, bool isUser, CataPrivEntityT **privEntity)
{
    *privEntity = NULL;
    char metaName[USER_ROLE_META_NAME_MAX_LEN] = {0};
#ifdef EXPERIMENTAL_NERGC
    errno_t err = strcpy_s(metaName, strlen(userNameInfo->userName) + 1, userNameInfo->userName);
    DB_ASSERT(err == EOK);
#else
    Status ret = CataBuildUserName(metaName, sizeof(metaName),
        isUser ? userNameInfo->userName : userNameInfo->groupName, userNameInfo->processName);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, USER_ROLE_DEFAULT_NAMESPACE_ID, metaName);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    return SingleVersionGetByName(
        GetMetaCache(cataCacheMgr, isUser ? CATA_USR : CATA_GROUP), &cataKey, (DmMetaCommonT **)privEntity);
}

Status RemovePrivEntityPhysically(CataPrivEntityT *privEntity, bool isUser)
{
    CataMetaTypeE metaType = isUser ? CATA_USR : CATA_GROUP;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(privEntity->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    Status ret = SingleVersionRemove(GetMetaCache(cataCacheMgr, metaType), (const DmMetaCommonT *)privEntity);
    if (ret != GMERR_OK) {
        return ret;
    }
    DestroyPrivEntity(privEntity);
    return GMERR_OK;
}

static Status DropPrivEntity(DbInstanceHdT dbInstance, const CataUserNameInfoT *userNameInfo, bool isUser)
{
    CataPrivEntityT *privEntity = NULL;
    Status ret = GetPrivEntityByName(dbInstance, userNameInfo, isUser, &privEntity);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_OBJECT, "The %s name is %s.",
            GetCataMetaTypeName(isUser ? CATA_USR : CATA_GROUP),
            isUser ? userNameInfo->userName : userNameInfo->groupName);
        // 此处应该返回ret，但是目前cataCache接口里统一都是undefine_table，待后续添加类型
        return GMERR_UNDEFINED_OBJECT;
    }

    // DBA 一定是 user
    if (isUser && privEntity->isDBA) {
        DB_LOG_AND_SET_LASERR(GMERR_RESTRICT_VIOLATION, "drop DBA, Uname: %s.", userNameInfo->userName);
        return GMERR_RESTRICT_VIOLATION;
    }
    // 先删除user对应的role
    for (uint32_t i = 0; i < privEntity->roleNum; ++i) {
        // 需要考虑异常场景的恢复
        ret = DropRoleNoLock(privEntity->roleIds[i], dbInstance);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    privEntity->metaCommon.isDeleted = true;
    if (privEntity->metaCommon.refCount == 0) {
        ret = RemovePrivEntityPhysically(privEntity, isUser);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status CataGetUserByName(const CataUserNameInfoT *userNameInfo, CataUserT **user, DbInstanceHdT dbInstance)
{
    DB_POINTER4(userNameInfo, userNameInfo->userName, userNameInfo->processName, user);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = GetPrivEntityByName(dbInstance, userNameInfo, true, user);
    if (ret != GMERR_OK) {
        DbRWSpinRUnlock(&cataCacheMgr->cataLock);
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_OBJECT, "user name: %s.", userNameInfo->userName);
        // 此处应该返回ret，但是目前cataCache接口里统一都是undefine_table，待后续添加类型
        return GMERR_UNDEFINED_OBJECT;
    }
    (void)DbAtomicInc(&(*user)->metaCommon.refCount);
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return GMERR_OK;
}

Status CataReleaseUser(CataUserT *user)
{
    DB_POINTER(user);
    // 在未标记删除的情况下加读锁，原子操作减 refCount，提升 release 操作性能
    bool hasReleased = false;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(user->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    Status ret = ReleaseMetaQuickly(dbInstance, &user->metaCommon, CATA_USR, &hasReleased);
    if (ret != GMERR_OK || hasReleased) {
        return ret;
    }

    DbRWSpinWLock(&cataCacheMgr->cataLock);
    if (user->metaCommon.refCount == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Urefcount is not correct, Uname:%s", user->metaCommon.metaName);
        DbRWSpinWUnlock(&cataCacheMgr->cataLock);
        return GMERR_INTERNAL_ERROR;
    }
    user->metaCommon.refCount--;
    if (user->metaCommon.refCount == 0 && user->metaCommon.isDeleted) {
        ret = RemovePrivEntityPhysically(user, true);
    }
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CataDropUser(const CataUserNameInfoT *userNameInfo, DbInstanceHdT dbInstance)
{
    DB_POINTER3(userNameInfo, userNameInfo->userName, userNameInfo->processName);
    DbRWSpinWLock(&DmGetCataCache(dbInstance)->cataLock);
    Status ret = DropPrivEntity(NULL, userNameInfo, true);
    DbRWSpinWUnlock(&DmGetCataCache(dbInstance)->cataLock);
    return ret;
}

Status CataGetGroupByName(const CataUserNameInfoT *userNameInfo, CataGroupT **group, DbInstanceHdT dbInstance)
{
    DB_POINTER4(userNameInfo, userNameInfo->groupName, userNameInfo->processName, group);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = GetPrivEntityByName(dbInstance, userNameInfo, false, group);
    if (ret != GMERR_OK) {
        DbRWSpinRUnlock(&cataCacheMgr->cataLock);
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_OBJECT, "group name: %s.", userNameInfo->userName);
        return GMERR_UNDEFINED_OBJECT;
    }
    (void)DbAtomicInc(&(*group)->metaCommon.refCount);
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return GMERR_OK;
}

Status CataReleaseGroup(CataGroupT *group)
{
    DB_POINTER(group);
    // 在未标记删除的情况下加读锁，原子操作减 refCount，提升 release 操作性能
    bool hasReleased = false;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(group->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    Status ret = ReleaseMetaQuickly(dbInstance, &group->metaCommon, CATA_GROUP, &hasReleased);
    if (ret != GMERR_OK || hasReleased) {
        return ret;
    }

    DbRWSpinWLock(&cataCacheMgr->cataLock);
    if (group->metaCommon.refCount == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Refcount is not correct, Uname:%s", group->metaCommon.metaName);
        DbRWSpinWUnlock(&cataCacheMgr->cataLock);
        return GMERR_INTERNAL_ERROR;
    }
    group->metaCommon.refCount--;
    if (group->metaCommon.refCount == 0 && group->metaCommon.isDeleted) {
        ret = RemovePrivEntityPhysically(group, false);
    }
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CataDropGroup(const CataUserNameInfoT *userNameInfo, DbInstanceHdT dbInstance)
{
    DB_POINTER3(userNameInfo, userNameInfo->groupName, userNameInfo->processName);
    DbRWSpinWLock(&DmGetCataCache(dbInstance)->cataLock);
    Status ret = DropPrivEntity(NULL, userNameInfo, false);
    DbRWSpinWUnlock(&DmGetCataCache(dbInstance)->cataLock);
    return ret;
}

Status CataGetAllUser(DbInstanceHdT dbInstance, DbListT *list, bool (*filter)(CataUserT *))
{
    DB_POINTER(list);
    DB_ASSERT(DbListGetItemCnt(list) == 0);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = GetAllMetaDataByType(cataCacheMgr, list, (void *)filter, CATA_USR);
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CataGetAllGroup(DbInstanceHdT dbInstance, DbListT *list, bool (*filter)(CataGroupT *))
{
    DB_POINTER(list);
    DB_ASSERT(DbListGetItemCnt(list) == 0);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = GetAllMetaDataByType(cataCacheMgr, list, (void *)filter, CATA_GROUP);
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CataDropAllUser(DbInstanceHdT dbInstance)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    uint32_t *key = NULL;
    DbOamapIteratorT mapIter = 0;
    CataPrivEntityT *user = NULL;
    CataCacheT *cataCache = GetMetaCache(cataCacheMgr, CATA_USR);
    Status ret = GMERR_OK;
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    while (SingleVersionFetch(cataCache, &mapIter, IsMetaUndeleted, &key, (DmMetaCommonT **)&user) == GMERR_OK) {
        if (user->isDBA) {
            continue;
        }
        // 先删除user对应的role
        for (uint32_t i = 0; i < user->roleNum; ++i) {
            ret = DropRoleNoCheck(user->roleIds[i], dbInstance);
            if (ret != GMERR_OK) {
                goto EXIT;
            }
        }
        user->metaCommon.isDeleted = true;
        if (user->metaCommon.refCount == 0) {
            ret = RemovePrivEntityPhysically(user, true);
            if (ret != GMERR_OK) {
                goto EXIT;
            }
        }
    }

EXIT:
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CheckGrantSysPriv(bool isAtomic, CataRoleT *role, const PrivElemT *privileges, uint32_t privilegeNum)
{
    for (uint32_t i = 0; i < privilegeNum; i++) {
        CataObjTypeE objType = privileges[i].objType;
        if (SECUREC_UNLIKELY(objType >= CATA_OBJ_TYPE_NUM)) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "Object type: %" PRIu32 ".", (uint32_t)objType);
            return GMERR_INVALID_PROPERTY;
        }
        uint32_t sysPriv = privileges[i].sysPrivs;
        if (isAtomic) {
            if ((uint32_t)((uint32_t)(role->sysPrivileges[objType].privilege ^ sysPriv) | (uint32_t)(~sysPriv)) !=
                DB_INVALID_ID32) {
                DB_LOG_AND_SET_LASERR(GMERR_PRIVILEGE_NOT_GRANTED, "priv already exist.");
                return GMERR_PRIVILEGE_NOT_GRANTED;
            }
        }
    }
    return GMERR_OK;
}

Status FillPrivRolesInfo(CataPrivRolesT *cataPrivRoles, uint32_t roleNum, CataRoleT **roles)
{
    if (cataPrivRoles == NULL || roleNum == 0) {
        return GMERR_OK;
    }
    cataPrivRoles->roles = DbDynMemCtxAlloc(cataPrivRoles->memCtx, roleNum * sizeof(CataRoleT *));
    if (cataPrivRoles->roles == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, " malloc for cataPrivRoles's roles, Size: %" PRIu32 ".",
            (uint32_t)(roleNum * sizeof(CataRoleT *)));
        return GMERR_OUT_OF_MEMORY;
    }
    for (uint32_t i = 0; i < roleNum; i++) {
        cataPrivRoles->roles[i] = roles[i];
        // 外部加 Catalog 写锁
        cataPrivRoles->roles[i]->metaCommon.refCount++;
    }
    cataPrivRoles->roleNum = roleNum;
    return GMERR_OK;
}

void CataReleasePrivRoles(CataPrivRolesT *cataPrivRoles)
{
    if (cataPrivRoles == NULL) {
        return;
    }
    for (uint32_t i = 0; i < cataPrivRoles->roleNum; i++) {
        if (cataPrivRoles->roles[i] != NULL) {
            (void)CataReleaseRole(cataPrivRoles->roles[i]);
        }
    }
    DbDynMemCtxFree(cataPrivRoles->memCtx, cataPrivRoles->roles);
    cataPrivRoles->roles = NULL;
    cataPrivRoles->roleNum = 0;
}

Status AlterPrivEntitySysPriv(DbInstanceHdT dbInstance, const CataUserNameInfoT *userNameInfo, bool isUser,
    PrivElemArrayT privElemArray, PrivOperationE privOperation, CataPrivRolesT *cataPrivRoles)
{
    // 可以支持 * 用来配置 RESOURCE 操作权限,权限约束在QE里实现，需要确认是否已实现
    char *userOrGroupName = isUser ? userNameInfo->userName : userNameInfo->groupName;
    if (strcmp(userOrGroupName, "*") == 0 && strcmp(userNameInfo->processName, "*") == 0) {
        return AlterUserSysPrivWithWildcard(isUser, privElemArray, privOperation, cataPrivRoles, dbInstance);
    }
    // 根据userName + processName找到user对应的role（NERG场景下仅根据userName）
    CataRoleT *role = NULL;
    Status ret = GetRoleNolock(dbInstance, userOrGroupName, userNameInfo->processName, &role, isUser);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "%s name is %s, process name is %s",
            GetCataMetaTypeName(isUser ? CATA_USR : CATA_GROUP), userOrGroupName, userNameInfo->processName);
        return ret;
    }

    ret = FillPrivRolesInfo(cataPrivRoles, 1, &role);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (privOperation == CATA_PRIV_GRANT) {
        ret = CheckGrantSysPriv(privElemArray.isAtomic, role, privElemArray.privileges, privElemArray.privilegeNum);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = AlterSysPriv(role, privElemArray.privileges, privElemArray.privilegeNum, CATA_PRIV_GRANT, true);
    } else {
        ret = AlterSysPriv(role, privElemArray.privileges, privElemArray.privilegeNum, CATA_PRIV_REVOKE,
            !privElemArray.privileges->notCheckRevokePriv);
    }
    return ret;
}

Status CataGrantSysPrivToUser(bool isAtomic, const CataUserNameInfoT *userNameInfo, const PrivElemT *privileges,
    uint32_t privilegeNum, CataPrivRolesT *cataPrivRoles, DbInstanceHdT dbInstance)
{
    DB_POINTER4(userNameInfo, userNameInfo->userName, userNameInfo->processName, privileges);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    PrivElemArrayT privElemArray = (PrivElemArrayT){privileges, privilegeNum, isAtomic};
    Status ret = AlterPrivEntitySysPriv(dbInstance, userNameInfo, true, privElemArray, CATA_PRIV_GRANT, cataPrivRoles);
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CataGrantSysPrivToGroup(bool isAtomic, const CataUserNameInfoT *userNameInfo, const PrivElemT *privileges,
    uint32_t privilegeNum, DbInstanceHdT dbInstance)
{
    DB_POINTER4(userNameInfo, userNameInfo->groupName, userNameInfo->processName, privileges);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    PrivElemArrayT privElemArray = (PrivElemArrayT){privileges, privilegeNum, isAtomic};
    Status ret = AlterPrivEntitySysPriv(dbInstance, userNameInfo, false, privElemArray, CATA_PRIV_GRANT, NULL);
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CataRevokeSysPrivFromUser(const CataUserNameInfoT *userNameInfo, const PrivElemT *privileges,
    uint32_t privilegeNum, CataPrivRolesT *cataPrivRoles, DbInstanceHdT dbInstance)
{
    DB_POINTER4(userNameInfo, userNameInfo->userName, userNameInfo->processName, privileges);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    PrivElemArrayT privElemArray = (PrivElemArrayT){privileges, privilegeNum, false};
    Status ret = AlterPrivEntitySysPriv(dbInstance, userNameInfo, true, privElemArray, CATA_PRIV_REVOKE, cataPrivRoles);
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CataRevokeSysPrivFromGroup(
    const CataUserNameInfoT *userNameInfo, const PrivElemT *privileges, uint32_t privilegeNum, DbInstanceHdT dbInstance)
{
    DB_POINTER4(userNameInfo, userNameInfo->groupName, userNameInfo->processName, privileges);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    PrivElemArrayT privElemArray = (PrivElemArrayT){privileges, privilegeNum, false};
    Status ret = AlterPrivEntitySysPriv(dbInstance, userNameInfo, false, privElemArray, CATA_PRIV_REVOKE, NULL);
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

#ifdef EXPERIMENTAL_NERGC
static Status VerifyLoginPWD(CataUserT *user, char *pwd)
{
    return DbUserLoginVerify(user->userOrGrpName.str, pwd);
}
#endif

Status CataLoginVerifyByUser(
    const CataUserNameInfoT *userNameInfo, bool *login, bool *isDBA, bool *isGroupLogin, CataRoleT *role)
{
    DB_POINTER3(userNameInfo, userNameInfo->userName, userNameInfo->processName);
    DB_POINTER3(login, isDBA, role);
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(role->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    CataRoleT *srcRole = NULL;
    CataUserT *user = NULL;
    *login = false;
    *isDBA = false;
    *isGroupLogin = false;
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = GetPrivEntityByName(dbInstance, userNameInfo, true, &user);
    if (ret == GMERR_OK) {
        DB_ASSERT(user != NULL);
#ifdef EXPERIMENTAL_NERGC
        // 非NERG-C场景下，仅需要获取用户是否在白名单中，而NERG-C场景，还需要使用业务注册的钩子函数进行二次校验
        ret = VerifyLoginPWD(user, userNameInfo->pwd);
        if (ret != GMERR_OK) {
            DbRWSpinRUnlock(&cataCacheMgr->cataLock);
            DB_LOG_ERROR(ret, "login in");
            return ret;
        }
#endif
        ret = SingleVersionGetById(GetMetaCache(cataCacheMgr, CATA_RL), user->roleIds[0], (DmMetaCommonT **)&srcRole);
        if (ret != GMERR_OK) {
            DbRWSpinRUnlock(&cataCacheMgr->cataLock);
            return ret;
        }
        GetRoleSysPrivInfo(srcRole, role);
        srcRole->connNum++;
        *login = true;
        *isDBA = user->isDBA;
        *isGroupLogin = false;
    }
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CataLoginVerifyByGroup(
    const CataUserNameInfoT *userNameInfo, bool *login, bool *isDBA, bool *isGroupLogin, CataRoleT *role)
{
    DB_POINTER3(userNameInfo, userNameInfo->userName, userNameInfo->processName);
    DB_POINTER3(login, isDBA, role);
    DB_POINTER(userNameInfo->groupName);
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(role->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    CataRoleT *srcRole = NULL;
    CataGroupT *group = NULL;
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = GetPrivEntityByName(dbInstance, userNameInfo, false, &group);
    if (ret == GMERR_OK) {
        DB_ASSERT(group != NULL);
        ret = SingleVersionGetById(GetMetaCache(cataCacheMgr, CATA_RL), group->roleIds[0], (DmMetaCommonT **)&srcRole);
        if (ret != GMERR_OK) {
            DbRWSpinRUnlock(&cataCacheMgr->cataLock);
            return ret;
        }
        GetRoleSysPrivInfo(srcRole, role);
        srcRole->connNum++;
        *login = true;
        *isDBA = false;
        *isGroupLogin = true;
    }
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return GMERR_OK;
}

// 当user和process同时为*，只支持RESOURCE权限且不创建新用户。权限约束在QE里实现，需要确认是否已实现
Status AlterUserSysPrivWithWildcard(bool grantToUser, PrivElemArrayT privElemArray, PrivOperationE privOperation,
    CataPrivRolesT *cataPrivRoles, DbInstanceHdT dbInstance)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    const PrivElemT *privileges = privElemArray.privileges;
    uint32_t privNum = privElemArray.privilegeNum;
    bool isAtomic = privElemArray.isAtomic;
    Status ret = GMERR_OK;
    uint32_t roleNum = 0;
    uint32_t mapSize = DbOamapUsedSize(GetMetaCache(cataCacheMgr, CATA_RL)->idMap);
    // role map中没有任何role也属于成功情况。
    if (mapSize == 0) {
        return GMERR_OK;
    }
    // 内存释放：本函数内；并发：不涉及并发
    CataRoleT **granteeRoleArray = DbDynMemCtxAlloc(cataCacheMgr->dynMemCtx, mapSize * sizeof(CataRoleT *));
    if (granteeRoleArray == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "cataCacheMgr memCtx, size: %zu", mapSize * sizeof(CataRoleT *));
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(granteeRoleArray, mapSize * sizeof(CataRoleT *), 0x00, mapSize * sizeof(CataRoleT *));

    // 外部函数已加锁：CATA_RL, CATA_USR/CATA_GROUP
    GetNonDBARoleFilterByProcess(dbInstance, grantToUser, granteeRoleArray, &roleNum, NULL);

    if (privOperation == CATA_PRIV_GRANT) {
        for (uint32_t i = 0; i < roleNum; i++) {
            CataRoleT *role = granteeRoleArray[i];
            ret = CheckGrantSysPriv(isAtomic, role, privileges, privNum);
            if (ret != GMERR_OK) {
                goto EXIT;
            }
        }
    }

    ret = FillPrivRolesInfo(cataPrivRoles, roleNum, granteeRoleArray);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    for (uint32_t i = 0; i < roleNum; i++) {
        CataRoleT *role = granteeRoleArray[i];
        ret = AlterSysPriv(role, privileges, privNum, privOperation, false);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
    }

EXIT:
    DbDynMemCtxFree(cataCacheMgr->dynMemCtx, granteeRoleArray);
    return ret;
}

Status CataGetUserWeight(const CataUserNameInfoT *userNameInfo, uint32_t *weight, DbInstanceHdT dbInstance)
{
    DB_POINTER4(userNameInfo, userNameInfo->userName, userNameInfo->processName, weight);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    CataUserT *user = NULL;
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = GetPrivEntityByName(dbInstance, userNameInfo, true, &user);
    if (ret != GMERR_OK) {
        DbRWSpinRUnlock(&cataCacheMgr->cataLock);
        DB_LOG_AND_SET_LASERR(ret, "get user weight, uname: %s", userNameInfo->userName);
        return ret;
    }
    DB_ASSERT(user != NULL);
    *weight = user->weight;
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return GMERR_OK;
}

Status CataSetUserWeight(const CataUserNameInfoT *userNameInfo, uint32_t weight, DbInstanceHdT dbInstance)
{
    DB_POINTER3(userNameInfo, userNameInfo->userName, userNameInfo->processName);

    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    CataUserT *user = NULL;
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    Status ret = GetPrivEntityByName(dbInstance, userNameInfo, true, &user);
    if (ret != GMERR_OK) {
        DbRWSpinWUnlock(&cataCacheMgr->cataLock);
        DB_LOG_AND_SET_LASERR(ret, "set user weight, uname: %s", userNameInfo->userName);
        return ret;
    }
    DB_ASSERT(user != NULL);
    user->weight = weight;
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return GMERR_OK;
}

Status CataGetUserConnNum(const CataUserNameInfoT *userNameInfo, uint32_t *connNum, DbInstanceHdT dbInstance)
{
    DB_POINTER4(userNameInfo, userNameInfo->userName, userNameInfo->processName, connNum);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    CataUserT *user = NULL;
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = GetPrivEntityByName(dbInstance, userNameInfo, true, &user);
    if (ret != GMERR_OK) {
        DbRWSpinRUnlock(&cataCacheMgr->cataLock);
        DB_LOG_AND_SET_LASERR(ret, "get user connection number, uname: %s", userNameInfo->userName);
        return ret;
    }
    DB_ASSERT(user != NULL);
    *connNum = user->rscLMT.connNum;
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return GMERR_OK;
}

Status CataGetUserConnSpeed(const CataUserNameInfoT *userNameInfo, uint32_t *conSpeed, DbInstanceHdT dbInstance)
{
    DB_POINTER4(userNameInfo, userNameInfo->userName, userNameInfo->processName, conSpeed);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    CataUserT *user = NULL;
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = GetPrivEntityByName(dbInstance, userNameInfo, true, &user);
    if (ret != GMERR_OK) {
        DbRWSpinRUnlock(&cataCacheMgr->cataLock);
        DB_LOG_AND_SET_LASERR(ret, "get user connection speed, uname: %s", userNameInfo->userName);
        return ret;
    }
    DB_ASSERT(user != NULL);
    *conSpeed = user->rscLMT.connSpeed;
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return GMERR_OK;
}

Status CataSetUserConnNum(const CataUserNameInfoT *userNameInfo, uint32_t connNum, DbInstanceHdT dbInstance)
{
    DB_POINTER3(userNameInfo, userNameInfo->userName, userNameInfo->processName);

    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    CataUserT *user = NULL;
    Status ret = GetPrivEntityByName(dbInstance, userNameInfo, true, &user);
    if (ret != GMERR_OK) {
        DbRWSpinWUnlock(&cataCacheMgr->cataLock);
        DB_LOG_AND_SET_LASERR(ret, "set user connection number, uname: %s", userNameInfo->userName);
        return ret;
    }
    DB_ASSERT(user != NULL);
    user->rscLMT.connNum = connNum;
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return GMERR_OK;
}

Status CataSetUserConnSpeed(const CataUserNameInfoT *userNameInfo, uint32_t conSpeed, DbInstanceHdT dbInstance)
{
    DB_POINTER3(userNameInfo, userNameInfo->userName, userNameInfo->processName);

    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    CataUserT *user = NULL;
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    Status ret = GetPrivEntityByName(dbInstance, userNameInfo, true, &user);
    if (ret != GMERR_OK) {
        DbRWSpinWUnlock(&cataCacheMgr->cataLock);
        DB_LOG_AND_SET_LASERR(ret, "set user connection speed, uname: %s", userNameInfo->userName);
        return ret;
    }
    DB_ASSERT(user != NULL);
    user->rscLMT.connSpeed = conSpeed;
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return GMERR_OK;
}

Status CataGetUserRoleId(const CataUserNameInfoT *userNameInfo, uint32_t *roleId, DbInstanceHdT dbInstance)
{
    DB_POINTER4(userNameInfo, userNameInfo->userName, userNameInfo->processName, roleId);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    CataUserT *user = NULL;
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = GetPrivEntityByName(dbInstance, userNameInfo, true, &user);
    if (ret != GMERR_OK) {
        DbRWSpinRUnlock(&cataCacheMgr->cataLock);
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_OBJECT, "get user role names, uname: %s", userNameInfo->userName);
        return GMERR_UNDEFINED_OBJECT;
    }
    DB_ASSERT(user != NULL);
    *roleId = user->roleIds[0];
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return GMERR_OK;
}

Status CataGetGroupRoleId(const CataUserNameInfoT *userNameInfo, uint32_t *roleId, DbInstanceHdT dbInstance)
{
    DB_POINTER4(userNameInfo, userNameInfo->groupName, userNameInfo->processName, roleId);
    CataGroupT *group = NULL;
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = GetPrivEntityByName(dbInstance, userNameInfo, false, &group);
    if (ret != GMERR_OK) {
        DbRWSpinRUnlock(&cataCacheMgr->cataLock);
        DB_LOG_AND_SET_LASERR(ret, "get group role ids, gname: %s", userNameInfo->groupName);
        return ret;
    }
    DB_ASSERT(group != NULL);
    *roleId = group->roleIds[0];
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return GMERR_OK;
}

Status CataSetUserProtocol(
    const CataUserNameInfoT *userNameInfo, PrivProtocolTypeE protocolType, bool isEnable, DbInstanceHdT dbInstance)
{
    DB_POINTER3(userNameInfo, userNameInfo->userName, userNameInfo->processName);

    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    CataUserT *user = NULL;
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    Status ret = GetPrivEntityByName(dbInstance, userNameInfo, true, &user);
    if (ret != GMERR_OK) {
        DbRWSpinWUnlock(&cataCacheMgr->cataLock);
        DB_LOG_AND_SET_LASERR(ret, "set user communication protocol, uname: %s", userNameInfo->userName);
        return ret;
    }
    DB_ASSERT(user != NULL);
    uint8_t type = (uint8_t)protocolType;
    user->userProtocol = isEnable ? (uint8_t)(user->userProtocol | type) : (uint8_t)(user->userProtocol & (~type));
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return GMERR_OK;
}

Status CataGetUserProtocol(
    const CataUserNameInfoT *userNameInfo, PrivProtocolTypeE protocolType, bool *isEnable, DbInstanceHdT dbInstance)
{
    DB_POINTER4(userNameInfo, userNameInfo->userName, userNameInfo->processName, isEnable);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    CataUserT *user = NULL;
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = GetPrivEntityByName(dbInstance, userNameInfo, true, &user);
    if (ret != GMERR_OK) {
        DbRWSpinRUnlock(&cataCacheMgr->cataLock);
        DB_LOG_AND_SET_LASERR(ret, "get user communication protocol, uname: %s", userNameInfo->userName);
        return ret;
    }
    DB_ASSERT(user != NULL);
    *isEnable = (user->userProtocol & (uint8_t)protocolType) != 0;
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return GMERR_OK;
}

// 只有找不到user时返回错误
// isAdd时前面已经校验过user存在，不会出错
// !isAdd时，user可能已经被删除，可以不校验返回值
Status ModifyPrivEntityMetaCreatedNum(
    DbInstanceHdT dbInstance, const char *creatorName, bool isGroup, CataUserMetaTypeE metaType, bool isAdd)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    // userPolicymode = 0时不检验usermeta上限，也不维护usermeta信息
    if (DbCfgGetInt32Lite(DB_CFG_USER_POLICY_MODE, dbInstance) == 0) {
        return GMERR_OK;
    }
    if (creatorName == NULL) {
        return GMERR_OK;
    }
    CataPrivEntityT *privEntity = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, USER_ROLE_DEFAULT_NAMESPACE_ID, creatorName);
    CataCacheT *metaCache = isGroup ? cataCacheMgr->metaCache[CATA_GROUP] : GetMetaCache(cataCacheMgr, CATA_USR);
    Status ret = SingleVersionGetByName(metaCache, &cataKey, (DmMetaCommonT **)&privEntity);
    if (ret != GMERR_OK) {
        return ret;
    }
    DB_ASSERT(privEntity != NULL);
    if (isAdd) {
        privEntity->metaCreated[metaType]++;
    } else {
        privEntity->metaCreated[metaType]--;
    }
    return GMERR_OK;
}

Status CheckPrivEntityMetaLimit(
    DbInstanceHdT dbInstance, const char *creatorName, bool isGroup, CataUserMetaTypeE metaType)
{
    int32_t userPolicyMode = DbCfgGetInt32Lite(DB_CFG_USER_POLICY_MODE, NULL);
    if (userPolicyMode == 0) {
        return GMERR_OK;
    }
    // default 的 kv/nsp/tsp 的 creator 为 NULL
    if (creatorName == NULL) {
        return GMERR_OK;
    }
    CataPrivEntityT *privEntity = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, USER_ROLE_DEFAULT_NAMESPACE_ID, creatorName);
    CataMetaTypeE userOrGroupType = isGroup ? CATA_GROUP : CATA_USR;
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    CataCacheT *metaCache = GetMetaCache(cataCacheMgr, userOrGroupType);
    Status ret = SingleVersionGetByName(metaCache, &cataKey, (DmMetaCommonT **)&privEntity);
    if (ret != GMERR_OK) {
        if (userPolicyMode == 1) {
            DB_LOG_WARN(GMERR_INSUFFICIENT_PRIVILEGE, "The %s (name: %s) does not exist.",
                GetCataMetaTypeName(userOrGroupType), creatorName);
            return GMERR_OK;
        } else {
            DB_LOG_AND_SET_LASERR(
                ret, "The %s (name: %s) does not exist.", GetCataMetaTypeName(userOrGroupType), creatorName);
            return ret;
        }
    }
    DB_ASSERT(privEntity != NULL);
    if (privEntity->metaCreated[metaType] >= privEntity->metaLimit[metaType]) {
        if (userPolicyMode == 1) {
            DB_LOG_WARN(GMERR_PROGRAM_LIMIT_EXCEEDED, "%s num created by %s (name: %s) exceeds %" PRIu16 ".",
                GetCataUserMetaTypeName(metaType), GetCataMetaTypeName(userOrGroupType), creatorName,
                privEntity->metaLimit[metaType]);
            return GMERR_OK;
        } else {
            DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "%s num created by %s (name: %s) exceeds %" PRIu16 ".",
                GetCataUserMetaTypeName(metaType), GetCataMetaTypeName(userOrGroupType), creatorName,
                privEntity->metaLimit[metaType]);
            return GMERR_PROGRAM_LIMIT_EXCEEDED;
        }
    }
    return GMERR_OK;
}

static Status GenerateIds(DbInstanceHdT dbInstance, CataUserInfoT *cataUserInfo)
{
    Status ret = CataGenerateUuid(dbInstance, &cataUserInfo->userId);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CataGenerateUuid(dbInstance, &cataUserInfo->roleId);
}

static Status CreateDBAImpl(DbInstanceHdT dbInstance, char *processName, char *user)
{
    // gmrule客户端在赋权的时候，需要使用prepareStmt，此时客户端需要权限校验。客户端的权限校验，无法使用isDBA字段，无法通过是否
    // 是DBA来authentication。只能通过角色的系统权限、以及该角色在对象上的对象权限进行authentication。
    // 所以在此处对DBA赋予vertex label上的所有权限。
    PrivElemT privElem = {.objType = CATA_VERTEX_LABEL,
        .sysPrivs = (uint32_t)CREATE_PRIV | (uint32_t)DROP_PRIV | (uint32_t)ALTER_PRIV | (uint32_t)TRUNCATE_PRIV |
                    (uint32_t)INSERTANY_PRIV | (uint32_t)DELETEANY_PRIV | (uint32_t)UPDATEANY_PRIV |
                    (uint32_t)SELECTANY_PRIV | (uint32_t)REPLACEANY_PRIV | (uint32_t)MERGEANY_PRIV |
                    (uint32_t)GET_PRIV};
    do {
#ifndef EXPERIMENTAL_NERGC
        // DBA配置项，支持解析多个进程，多个进程以';'隔开
        char *tailStr = NULL;
        char *procName = strtok_s(processName, ";", &tailStr);
        if (procName == NULL || strlen(procName) == 0) {
            DB_LOG_AND_SET_LASERR(GMERR_CONFIG_ERROR, "Illegal user or process.");
            return GMERR_CONFIG_ERROR;
        }
        if (strlen(procName) >= DB_MAX_PROC_NAME_LEN) {
            DB_LOG_AND_SET_LASERR(GMERR_CONFIG_ERROR, "process len %" PRIu32 ", max len %" PRIu32 "",
                (uint32_t)strlen(procName), (uint32_t)DB_MAX_PROC_NAME_LEN);
            return GMERR_CONFIG_ERROR;
        }
#else
        // 不解析，直接赋值
        char *procName = processName;
#endif
        // 无论首次启动还是重启均创建 DBA 对应的 user 和 role，不依赖系统表恢复
        CataUserInfoT cataUserInfo = {0};
        Status ret = GenerateIds(dbInstance, &cataUserInfo);
        if (ret != GMERR_OK) {
            return ret;
        }
        cataUserInfo.isDBA = true;
        SetCataUserNameInfo(&cataUserInfo.userNameInfo, user, NULL, procName);
        ret = CataCreateUser(dbInstance, &cataUserInfo);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = CataGrantSysPrivToUser(false, &cataUserInfo.userNameInfo, &privElem, 1, NULL, dbInstance);
        if (ret != GMERR_OK) {
            return ret;
        }
#ifndef EXPERIMENTAL_NERGC
        processName = tailStr;
#else
        break;
#endif
    } while (*processName != '\0');
    return GMERR_OK;
}

#ifndef EXPERIMENTAL_NERGC
static Status CreateDBAUsers(char *userStr, DbInstanceHdT dbInstance)
{
    char *process = NULL;
    char *user = strtok_s(userStr, ":", &process);
    if (user == NULL || process == NULL || strlen(user) == 0 || strlen(process) == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_CONFIG_ERROR, "Illegal user or process.");
        return GMERR_CONFIG_ERROR;
    }
    if (strlen(user) >= MAX_OS_USER_NAME_LENGTH) {
        DB_LOG_AND_SET_LASERR(GMERR_CONFIG_ERROR, "user len %" PRIu32 ", max len %" PRIu32 "", (uint32_t)strlen(user),
            (uint32_t)MAX_OS_USER_NAME_LENGTH);
        return GMERR_CONFIG_ERROR;
    }
    return CreateDBAImpl(dbInstance, process, user);
}
#else
// NERGC场景下，DBA格式形如：userNameA|userNameB|userNameC
static Status CreateDBAUsers(char *userName, DbInstanceHdT dbInstance)
{
    if (strlen(userName) > NERGC_MAX_USER_NAME_LEN || strlen(userName) < NERGC_MIN_USER_NAME_LEN) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "DBA");
        return GMERR_CONFIG_ERROR;
    }
    return CreateDBAImpl(dbInstance, "", userName);
}
#endif

Status CreateDBA(DbInstanceHdT dbInstance)
{
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(dbInstance);
    DbCfgValueT dbaConf;
    Status ret = DbCfgGet(cfgHandle, DB_CFG_DBA_INFO, &dbaConf);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get config DBA info.");
        return ret;
    }
    char *dbaCfgStr = dbaConf.str;
    // DBA配置项，支持解析多个用户，多个用户以'|'隔开
    do {
        char *tailStr = NULL;
        char *userStr = strtok_s(dbaCfgStr, "|", &tailStr);
        if (userStr == NULL || strlen(userStr) == 0) {
            DB_LOG_AND_SET_LASERR(GMERR_CONFIG_ERROR, "Illegal user string.");
            return GMERR_CONFIG_ERROR;
        }
        ret = CreateDBAUsers(userStr, dbInstance);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "create DBA users.");
            return ret;
        }
        dbaCfgStr = tailStr;
    } while (*dbaCfgStr != '\0');

    return GMERR_OK;
}

Status CataCreateEmptyUserWithMemCtx(DbMemCtxT *memCtx, CataUserT **user)
{
    DB_POINTER2(memCtx, user);
    // 创建空 user 流程，由调用者释放
    CataUserT *tmpUser = (CataUserT *)DbDynMemCtxAlloc(memCtx, sizeof(CataUserT));
    if (tmpUser == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "create user, size: %zu", sizeof(CataUserT));
        return GMERR_OUT_OF_MEMORY;
    }

    (void)memset_s(tmpUser, sizeof(CataUserT), 0x00, sizeof(CataUserT));
    tmpUser->memCtx = memCtx;
    *user = tmpUser;
    return GMERR_OK;
}

Status CataCopyUser(const CataUserT *srcUser, CataUserT *destUser)
{
    DB_POINTER2(srcUser, destUser);
    DbMemCtxT *memCtx = destUser->memCtx;
    *destUser = *srcUser;
    destUser->memCtx = memCtx;

    destUser->metaCommon.metaName = NULL;
    destUser->userOrGrpName.str = NULL;
    destUser->processName.str = NULL;
    destUser->roleIds = NULL;

    uint32_t metaNameLen = GetStrLen(srcUser->metaCommon.metaName);
    Status ret = DmCopyBufWithMemCtx(destUser->memCtx, (uint8_t *)srcUser->metaCommon.metaName, metaNameLen,
        (uint8_t **)&destUser->metaCommon.metaName);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "alloc for metaName size %" PRIu32 "", metaNameLen);
        return ret;
    }
    destUser->userOrGrpName.len = GetStrLen(srcUser->userOrGrpName.str);
    ret = DmCopyBufWithMemCtx(destUser->memCtx, (uint8_t *)srcUser->userOrGrpName.str, destUser->userOrGrpName.len,
        (uint8_t **)&destUser->userOrGrpName.str);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "alloc for userOrGrpName size %" PRIu32 "", destUser->userOrGrpName.len);
        return ret;
    }
    destUser->processName.len = GetStrLen(srcUser->processName.str);
    ret = DmCopyBufWithMemCtx(destUser->memCtx, (uint8_t *)srcUser->processName.str, destUser->processName.len,
        (uint8_t **)&destUser->processName.str);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "allocate for processName size %" PRIu32 "", destUser->processName.len);
        return ret;
    }
    uint32_t roleIdLen = srcUser->roleNum * (uint32_t)sizeof(uint32_t);
    ret = DmCopyBufWithMemCtx(destUser->memCtx, (uint8_t *)srcUser->roleIds, roleIdLen, (uint8_t **)&destUser->roleIds);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "allocate for roleIds size %" PRIu32 "", roleIdLen);
        return ret;
    }
    return GMERR_OK;
}

void CataDestroyUser(CataPrivEntityT *user)
{
    if (user == NULL) {
        return;
    }
    DbDynMemCtxFree(user->memCtx, user->metaCommon.metaName);
    DbDynMemCtxFree(user->memCtx, user->userOrGrpName.str);
    DbDynMemCtxFree(user->memCtx, user->processName.str);
    DbDynMemCtxFree(user->memCtx, user->roleIds);
    DbDynMemCtxFree(user->memCtx, user);
}

inline uint32_t CataGetRoleIdOfUser(const CataUserT *user)
{
    DB_POINTER(user);
    return user->roleIds[0];
}

#ifdef __cplusplus
}
#endif
