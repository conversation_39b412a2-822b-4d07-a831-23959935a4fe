/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: header file for namespace cache
 * Author: wangjingyi
 * Create: 2023-3-24
 */

#ifndef NAMESPACE_CACHE_H
#define NAMESPACE_CACHE_H

#include "db_mem_context.h"

#ifdef __cplusplus
extern "C" {
#endif

// 通过namespace name拿到namespace id
Status GetNamespaceIdByNameWithoutLock(DbInstanceHdT dbInstance, const char *nspName, uint32_t *nspId);
// 通过namespace id拿到namespace name
Status GetNamespaceNameByIdWithoutLock(DbMemCtxT *memCtx, char **nspName, uint32_t nspId);

Status CataInitDefaultNsp(DbInstanceHdT dbInstance, char *nspName, TrxTypeE trxType, IsolationLevelE isolationLevel);

/**
 * @brief: 检查namespace中是否有yang默认值可见性表（NpAccess）
 * @param[in] dbInstance 当前实例
 * @param[in] nspId 待判断的namespace的id
 * @param[out] hasNpAccess 默认值可见性表是否存在
 * @return: 状态码
 */
Status CheckNamespaceHasNpAccess(DbInstanceHdT dbInstance, uint32_t nspId, bool *hasNpAccess);

/**
 * @brief: 设置namespace中yang默认值可见性表（NpAccess）信息
 * @param[in] nspId namespace的id
 * @param[in] vertexLabelId 默认值可见性表的vertexLabelId，不存在可见性表时，id为 0
 * @param[in] hasNpAccess 设置namespace中默认值可见性表存在标志
 * @return: 状态码
 */
Status SetNspNpAccessInfo(uint32_t nspId, uint32_t vertexLabelId, bool hasNpAccess, DbInstanceHdT dbInstance);

#endif  // NAMESPACE_CACHE_H
