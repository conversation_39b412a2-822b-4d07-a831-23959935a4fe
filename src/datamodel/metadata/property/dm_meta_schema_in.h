/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: header file for data schema, used internally
 * Author: jinfanglin
 * Create: 2022-7-19
 */

#ifndef DM_META_SCHEMA_IN_H
#define DM_META_SCHEMA_IN_H

#include "dm_meta_schema.h"

#ifdef __cplusplus
extern "C" {
#endif

// isSpecial用于表示是否为特殊复杂表，特殊复杂表只支持在record末尾增加变长字段、节点
bool SchemaCompatible(
    const DmSchemaT *oriSchema, DmSchemaT *updSchema, bool isSpecial, bool *isChanged, bool modifiable);

bool PropertySchemaCompatible(const DmPropertySchemaT *oriProperty, DmPropertySchemaT *updProperty, bool modifiable);

#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif /* DM_META_SCHEMA_IN_H */
