/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: dm_meta_stream_prop_label_oper.c
 * Description: implementation of stream vertex label related functions
 * Author: stream Team
 * Create: 2024-8-14
 */
#include <securec.h>
#include "dm_meta_prop_stream_label.h"
#include "dm_meta_schema_in.h"

#include "dm_meta_prop_label.h"
#include "dm_log.h"

SO_EXPORT_FOR_TS DbMemCtxT *g_parentStreamTopoCtx = NULL;
SO_EXPORT DbListT *g_ref = NULL;

void StreamInitParentStreamTopoMemCtx(DbMemCtxT *memCtx)
{
    g_parentStreamTopoCtx = memCtx;
}

void StreamInitGlobalRef(DbListT *ref)
{
    g_ref = ref;
}

DbListT *StreamGetGlobalRef(void)
{
    return g_ref;
}

SO_EXPORT Status DmCreateStreamEmptyVertexLabelWithMemCtx(DbMemCtxT *memCtx, DmStreamVertexLabelT **vertexLabel)
{
    DB_POINTER2(memCtx, vertexLabel);
    // 创建空vertexlabel流程，由调用者释放
    *vertexLabel = (DmStreamVertexLabelT *)DbDynMemCtxAlloc(memCtx, sizeof(DmStreamVertexLabelT));
    if (SECUREC_UNLIKELY(*vertexLabel == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "create vertexLabel size %zu.", sizeof(DmStreamVertexLabelT));
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(*vertexLabel, sizeof(DmStreamVertexLabelT), 0x00, sizeof(DmStreamVertexLabelT));
    DbCreateList(&(*vertexLabel)->refName, sizeof(char *), memCtx);
    (*vertexLabel)->base.memCtx = memCtx;
    return GMERR_OK;
}

SO_EXPORT Status DmModifyStreamVertexLabelStatus(
    DmStreamVertexLabelT *streamVertexLabel, DmStreamForwardStatusE targetStatus)
{
    DbSpinLock(StreamGetForwardStatusLock(streamVertexLabel));
    DmStreamForwardStatusE *currStatus = StreamGetForwardStatus(streamVertexLabel);
    Status ret = GMERR_OK;
    switch (*currStatus) {
        case STREAM_FORWARD_STATUS_IDLE: {
            if (SECUREC_UNLIKELY(targetStatus == STREAM_FORWARD_STATUS_IDLE)) {
                // 当前应该没有这种情况
                ret = GMERR_INVALID_PARAMETER_VALUE;
                break;
            } else if (targetStatus == STREAM_FORWARD_STATUS_READY) {
                // 线程池模式，从idle切ready
                *currStatus = STREAM_FORWARD_STATUS_READY;
                break;
            } else {
                // 连接独占线程模式，从idle切running
                DB_ASSERT(targetStatus == STREAM_FORWARD_STATUS_RUNNING);
                *currStatus = STREAM_FORWARD_STATUS_RUNNING;
                break;
            }
        }
        case STREAM_FORWARD_STATUS_READY: {
            if (targetStatus == STREAM_FORWARD_STATUS_IDLE) {
                // 切ready后失败，需要切回来
                *currStatus = STREAM_FORWARD_STATUS_IDLE;
                break;
            } else if (targetStatus == STREAM_FORWARD_STATUS_READY) {
                // 并发切ready,已经是ready了不允许ready
                ret = GMERR_INVALID_PARAMETER_VALUE;
                break;
            } else {
                // ready后需要被调度执行
                DB_ASSERT(targetStatus == STREAM_FORWARD_STATUS_RUNNING);
                *currStatus = STREAM_FORWARD_STATUS_RUNNING;
                break;
            }
        }
        case STREAM_FORWARD_STATUS_RUNNING: {
            if (targetStatus == STREAM_FORWARD_STATUS_IDLE) {
                // 执行完了，从running切idle
                *currStatus = STREAM_FORWARD_STATUS_IDLE;
                break;
            } else if (SECUREC_UNLIKELY(targetStatus == STREAM_FORWARD_STATUS_READY)) {
                // 这个情况不该走这个函数，线程池模式在执行完之后发现数据区还有数据，切回ready
                ret = GMERR_INVALID_PARAMETER_VALUE;
                break;
            } else {
                // 连接独占线程模式，已经running的允许再切running
                DB_ASSERT(targetStatus == STREAM_FORWARD_STATUS_RUNNING);
                ret = GMERR_INVALID_PARAMETER_VALUE;
                break;
            }
        }
        default:
            break;
    }
    DbSpinUnlock(StreamGetForwardStatusLock(streamVertexLabel));
    return ret;
}

SO_EXPORT Status CataGetStreamVertexLabelById(uint32_t vlId, DmStreamVertexLabelT **vertexLabel)
{
    DmVertexLabelT *tmpLabel;
    Status ret = CataGetVertexLabelById(NULL, vlId, &tmpLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get vertex label by id(%" PRIu32 ").", vlId);
        return ret;
    }
    if (!DmIsStreamVertexLabel(tmpLabel)) {
        // 非流计算的表被流计算调度执行
        CataReleaseVertexLabel(tmpLabel);
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "label %" PRIu32 " not stream table", vlId);
        DB_ASSERT(false);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    *vertexLabel = (DmStreamVertexLabelT *)tmpLabel;
    return GMERR_OK;
}

Status CataGetStreamVertexLabelByName(const CataKeyT *cataKey, DmStreamVertexLabelT **vertexLabel)
{
    DmVertexLabelT *tmpLabel;
    Status ret = CataGetVertexLabelByName(NULL, cataKey, &tmpLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get vertex label by name(%s).", cataKey->labelName);
        return ret;
    }
    if (!DmIsStreamVertexLabel(tmpLabel)) {
        // 非流计算的表被流计算调度执行
        CataReleaseVertexLabel(tmpLabel);
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "label %s not stream table", cataKey->labelName);
        DB_ASSERT(false);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    *vertexLabel = (DmStreamVertexLabelT *)tmpLabel;
    return GMERR_OK;
}

Status DmGenWindowProperty(DbMemCtxT *memCtx, DmPropertySchemaT **properties, uint32_t propId)
{
    DB_POINTER(properties);
    properties[0] = DbDynMemCtxAlloc(memCtx, sizeof(DmPropertySchemaT));
    if (SECUREC_UNLIKELY(properties[0] == NULL)) {
        return GMERR_OUT_OF_MEMORY;
    }
    properties[1] = DbDynMemCtxAlloc(memCtx, sizeof(DmPropertySchemaT));
    if (SECUREC_UNLIKELY(properties[1] == NULL)) {
        DbDynMemCtxFree(memCtx, properties[0]);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(properties[0], sizeof(DmPropertySchemaT), 0, sizeof(DmPropertySchemaT));
    (void)memset_s(properties[1], sizeof(DmPropertySchemaT), 0, sizeof(DmPropertySchemaT));

    properties[0]->nameLen = (uint16_t)strlen(STREAM_WINDOW_START_PROP_NAME) + 1;
    properties[0]->name = STREAM_WINDOW_START_PROP_NAME;
    properties[0]->propeId = propId;  // begin with 0
    properties[0]->isValid = true;
    properties[0]->isFixed = true;
    properties[0]->dataType = DB_DATATYPE_INT64;
    properties[0]->size = DmGetBasicDataTypeLength(properties[0]->dataType);

    properties[1]->nameLen = (uint16_t)strlen(STREAM_WINDOW_END_PROP_NAME) + 1;
    properties[1]->name = STREAM_WINDOW_END_PROP_NAME;
    properties[1]->propeId = propId + 1;  // begin with 0
    properties[1]->isValid = true;
    properties[1]->isFixed = true;
    properties[1]->dataType = DB_DATATYPE_INT64;
    properties[1]->size = DmGetBasicDataTypeLength(properties[1]->dataType);
    return GMERR_OK;
}

void DmFreeStreamVertexLabel(DmStreamVertexLabelT *streamVertexLabel)
{
    DB_POINTER(streamVertexLabel);
    if (streamVertexLabel->streamMemCtx != NULL) {
        DbDeleteDynMemCtx(streamVertexLabel->streamMemCtx);
        streamVertexLabel->streamMemCtx = NULL;
    }
}

bool DmUpdateWatermark(DmStreamVertexLabelT *streamLabel, int64_t newWatermark, bool isTolerant)
{
    DmStreamWatermarkStatusT *watermark;
    if (streamLabel->streamType == STREAM_LABEL_TYPE_VIEW) {
        watermark = &streamLabel->streamMeta.viewMeta.watermark;
    } else if (streamLabel->streamType == STREAM_LABEL_TYPE_SINK) {
        watermark = &streamLabel->streamMeta.sinkMeta.watermark;
    } else {
        return false;
    }
    if (watermark->watermark < newWatermark) {
        watermark->watermark = newWatermark;
        watermark->isTolerant = isTolerant;
        return true;
    }
    return false;
}

StreamRefSlotT *BinarySearchCharList(DbListT *refArray, uint32_t *left, int32_t *idx, const char *tableName)
{
    uint32_t right = DbListGetItemCnt(refArray);
    while (*left < right) {
        int32_t mid = *left + (right - *left) / 2;
        StreamRefSlotT *streamRef = *(StreamRefSlotT **)DbListItem(refArray, mid);
        int res = DbStrCmp((const char *)streamRef->name, tableName, false);
        if (res == 0) {
            *idx = mid;
            return streamRef;
        } else if (res < 0) {
            *left = mid + 1;
        } else {
            right = mid;
        }
    }
    return NULL;
}

MiniRefKVT *BinarySearchKVList(DbListT *refArray, uint32_t *left, uint32_t right, int64_t key)
{
    while (*left < right) {
        int mid = *left + (right - *left) / 2;
        MiniRefKVT *kv = (MiniRefKVT *)DbListItem(refArray, mid);
        if (kv->key == key) {
            return kv;
        } else if (kv->key < key) {
            *left = mid + 1;
        } else {
            right = mid;
        }
    }

    return NULL;  // 没找到
}

uint32_t *BinarySearchIntList(DbListT *refArray, uint32_t *left, uint32_t right, int64_t value)
{
    while (*left < right) {
        int mid = *left + (right - *left) / 2;
        uint32_t *v = (uint32_t *)DbListItem(refArray, mid);
        if (*v == value) {
            return v;
        } else if (*v < value) {
            *left = mid + 1;
        } else {
            right = mid;
        }
    }

    return NULL;  // 没找到
}

static DbRWSpinLockT g_streamTopoLock;  // 保护stream整体结构，应对DDL和DML并发

void StreamInitGlobalStreamTopoLock(void)
{
    DbRWSpinInit(&g_streamTopoLock);
}

void StreamDDLLock(void)
{
    DbRWSpinWLock(&g_streamTopoLock);
}

void StreamDDLUnlock(void)
{
    DbRWSpinWUnlock(&g_streamTopoLock);
}

void StreamDMLLock(void)
{
    DbRWSpinRLock(&g_streamTopoLock);
}

void StreamDMLUnlock(void)
{
    DbRWSpinRUnlock(&g_streamTopoLock);
}
