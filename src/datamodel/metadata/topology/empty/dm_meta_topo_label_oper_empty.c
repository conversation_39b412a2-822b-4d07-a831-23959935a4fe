/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: empty implementation catalog meta topo label
 * Author:
 * Create: 2024-9-6
 */

#include "dm_meta_topo_label.h"
#include "dm_meta_vertex_label.h"
#include "dm_data_basic_in.h"
#include "dm_data_prop_seri.h"

#ifdef __cplusplus
extern "C" {
#endif

Status DmCreateEmptyEdgeLabelWithMemCtx(DbMemCtxT *memCtx, DmEdgeLabelT **edgeLabel)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status DmCopyEdgeLabel(const DmEdgeLabelT *srcEdgeLabel, DmEdgeLabelT *destEdgeLabel)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

void DmDestroyEdgeLabel(DmEdgeLabelT *edgeLabel)
{
    return;
}

uint32_t DmGetEdgeLabelSeriBufLength(const DmEdgeLabelT *edgeLabel)
{
    return 0;
}

Status DmSerializeEdgeLabel(const DmEdgeLabelT *edgeLabel, DmBuffer *buffer)
{
    return FEATURE_NOT_SUPPORTED_INNER;
}

Status DmDeSerializeEdgeLabelWithMemCtx(DbMemCtxT *memCtx, uint8_t *buf, uint32_t length, DmEdgeLabelT **edgeLabel)
{
    return FEATURE_NOT_SUPPORTED_INNER;
}

#ifdef __cplusplus
}
#endif
