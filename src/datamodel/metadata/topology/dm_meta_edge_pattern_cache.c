/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: main entry for catalog label cache
 * Author: gongsai
 * Create: 2024-3-25
 */

#include "dm_meta_topo_label.h"
#include "dm_meta_log.h"
#include "dm_meta_basic.h"
#include "dm_meta_basic_in.h"
#include "dm_meta_key_oper.h"
#include "dm_cache_multi_ver_mgr.h"
#include "dm_meta_namespace.h"
#include "dm_meta_prop_label_in.h"
#include "dm_cache_single_ver_mgr.h"
#include "dm_meta_tablespace.h"
#include "dm_meta_user.h"
#include "dm_meta_topo_label_inner.h"
#include "dm_log.h"

#ifdef __cplusplus
extern "C" {
#endif

uint32_t g_gmdbEdgePatternNum = 0;

Status CataGetEdgePatternByName(const CataKeyT *cataKey, DmEdgePatternT **edgePattern, DbInstanceHdT dbInstance)
{
    DB_POINTER2(cataKey, edgePattern);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret =
        SingleVersionGetByName(cataCacheMgr->metaCache[CATA_EDGE_PATTERN], cataKey, (DmMetaCommonT **)edgePattern);
    if (ret == GMERR_OK) {  // ret为GMERR_OK时edgePattern必定不会为NULL
        (void)DbAtomicInc(&((*edgePattern)->metaCommon.refCount));
    }
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CataGetEdgePatternById(uint32_t edgePatternId, DmEdgePatternT **edgePattern, DbInstanceHdT dbInstance)
{
    DB_POINTER(edgePattern);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret =
        SingleVersionGetById(cataCacheMgr->metaCache[CATA_EDGE_PATTERN], edgePatternId, (DmMetaCommonT **)edgePattern);
    if (ret == GMERR_OK) {  // ret为GMERR_OK时edgePattern必定不会为NULL
        (void)DbAtomicInc(&((*edgePattern)->metaCommon.refCount));
    }
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status VertexLabelDeleteRelatedEdgePatternById(uint32_t vid, uint32_t edgePatternId, DbInstanceHdT dbInstance)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    // 1.lookup vid2RelatedEdgePattern找到vid对应的列表对象
    DbListT *infoList = (DbListT *)DbOamapLookup(cataCacheMgr->vid2RelatedEdgePattern, vid, &vid, NULL);
    if (infoList == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATA_EXCEPTION, "label id:%" PRIu32 ", EdgePattern id:%" PRIu32 "", vid, edgePatternId);
        return GMERR_DATA_EXCEPTION;
    }
    // 2.遍历列表对象，找到并删除edgepatterninfo
    uint32_t cnt = DbListGetItemCnt(infoList);
    for (uint32_t i = 0; i < cnt; ++i) {
        DmEdgePatternInfoT *tmpInfo = (DmEdgePatternInfoT *)DbListItem(infoList, i);
        if (tmpInfo->edgeLabelId == edgePatternId) {
            DbDynMemCtxFree(cataCacheMgr->dynMemCtx, tmpInfo->edgeName);
            DbDelListItem(infoList, i);
            return GMERR_OK;
        }
    }
    // 要么列表对象无info元素，要么没有找到edgepatterninfo，都属于数据异常情况
    DB_LOG_ERROR_AND_SET_LASTERR(
        GMERR_DATA_EXCEPTION, "label id:%" PRIu32 ", EdgePattern id:%" PRIu32 "", vid, edgePatternId);
    return GMERR_DATA_EXCEPTION;
}

Status RemoveEdgePatternInner(DmEdgePatternT *patternToDelete)
{
    Status ret = GMERR_OK;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(patternToDelete->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    patternToDelete->metaCommon.isDeleted = true;
    UpdateBelongSpaceLabelCount(&patternToDelete->metaCommon, false, dbInstance);
    g_gmdbEdgePatternNum--;
    (void)ModifyPrivEntityMetaCreatedNum(dbInstance, patternToDelete->creator, false, USER_META_EDGE_PATTERN, false);
    if (patternToDelete->metaCommon.refCount == 0) {
        ret = SingleVersionRemove(
            cataCacheMgr->metaCache[CATA_EDGE_PATTERN], (const DmMetaCommonT *)(const void *)patternToDelete);
        DB_ASSERT(ret == GMERR_OK);  // 前面mark as delete成功，remove一定成功
        ret = VertexLabelDeleteRelatedEdgePatternById(
            patternToDelete->sourceVertexLabelId, patternToDelete->metaCommon.metaId, dbInstance);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (patternToDelete->edgeType != EMPTY_EDGE) {
            ret = VertexLabelDeleteRelatedEdgePatternById(
                patternToDelete->destVertexLabelId, patternToDelete->metaCommon.metaId, dbInstance);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        DmDestroyEdgeLabel(patternToDelete);
    }

    return GMERR_OK;
}

static bool IsRelatedPathPatternAllDeleted(const DmEdgePatternT *edgePattern)
{
    uint32_t eid = edgePattern->metaCommon.metaId;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(edgePattern->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbListT *infoList = (DbListT *)DbOamapLookup(cataCacheMgr->eid2RelatedPathPattern, eid, &eid, NULL);
    // 无依赖的可能场景：
    // 1.没有创建过依赖该边模式的pathpattern
    // 2.创建过，但所有依赖该边模式的pathpattern已经被删除
    if (infoList == NULL) {
        return true;
    }
    uint32_t cnt = DbListGetItemCnt(infoList);
    return cnt == 0 ? true : false;
}

Status CataRemoveEdgePatternById(DbInstanceHdT dbInstance, uint32_t edgePatternId)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    DmEdgePatternT *patternToDelete = NULL;
    Status ret = SingleVersionGetById(
        cataCacheMgr->metaCache[CATA_EDGE_PATTERN], edgePatternId, (DmMetaCommonT **)&patternToDelete);
    if (ret != GMERR_OK) {
        DbRWSpinWUnlock(&cataCacheMgr->cataLock);
        return ret;
    }
    // 检测是否有pathpattern依赖该边模式
    if (!IsRelatedPathPatternAllDeleted(patternToDelete)) {
        DbRWSpinWUnlock(&cataCacheMgr->cataLock);
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_RESTRICT_VIOLATION, "Edgepattern name :%s", patternToDelete->metaCommon.metaName);
        return GMERR_RESTRICT_VIOLATION;
    }
    ret = RemoveEdgePatternInner(patternToDelete);
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CheckRelatedVertexLabelForEdgePattern(const DmEdgePatternT *edgePattern)
{
    uint32_t elId = edgePattern->metaCommon.metaId;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(edgePattern->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    // 边模式的源点标签和目的点标签是否已存在
    bool isSrcIdVld = SingleVersionLabelIdValid(cataCacheMgr->metaCache[CATA_VL], edgePattern->sourceVertexLabelId);
    if (!isSrcIdVld) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_UNDEFINED_TABLE, "Edge label id:%" PRIu32 ", name:%s", elId, edgePattern->metaCommon.metaName);
        return GMERR_UNDEFINED_TABLE;
    }
    // 空边类型的边模式无目的点标签，无后续校验流程
    if (edgePattern->edgeType == EMPTY_EDGE) {
        return GMERR_OK;
    }

    bool isDestIdVld = SingleVersionLabelIdValid(cataCacheMgr->metaCache[CATA_VL], edgePattern->destVertexLabelId);
    if (!isDestIdVld) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_UNDEFINED_TABLE, "Edge label id:%" PRIu32 ", name:%s", elId, edgePattern->metaCommon.metaName);
        return GMERR_UNDEFINED_TABLE;
    }
    return GMERR_OK;
}

Status CheckEdgePatternForCreate(const DmEdgePatternT *edgePattern)
{
    DB_POINTER(edgePattern);
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(edgePattern->memCtx);
    if (g_gmdbEdgePatternNum >= CATA_EDGE_NUM_MAX) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "The num is %" PRIu32, g_gmdbEdgePatternNum);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }

    // namespace是否存在
    if (!IsNamespaceIdExist(dbInstance, edgePattern->metaCommon.namespaceId)) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_UNDEFINED_TABLE, "Namespace id:%" PRIu32, edgePattern->metaCommon.namespaceId);
        return GMERR_UNDEFINED_TABLE;
    }

    // tablespace是否存在
    if (!IsTablespaceIdExistNoLock(dbInstance, edgePattern->metaCommon.tablespaceId)) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_UNDEFINED_TABLE, "Tablespace id:%" PRIu32 "", edgePattern->metaCommon.tablespaceId);
        return GMERR_UNDEFINED_TABLE;
    }

    if (!IsRsmTspIdExistNoLock(dbInstance, &edgePattern->metaCommon)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNDEFINED_OBJECT, "Label is %s.", edgePattern->metaCommon.metaName);
        return GMERR_UNDEFINED_OBJECT;
    }

    // 是否已存在相同ID的边模式
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    uint32_t elId = edgePattern->metaCommon.metaId;
    bool isIdExist = SingleVersionLabelIdExist(cataCacheMgr->metaCache[CATA_EDGE_PATTERN], elId);
    if (isIdExist) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DUPLICATE_TABLE, "Edge label id:%" PRIu32 ", name:%s", elId, edgePattern->metaCommon.metaName);
        return GMERR_DUPLICATE_TABLE;
    }

    // 是否已存在相同name且isDeleted为false的边模式
    CataKeyT cataKey;
    CataSetKeyForLabel(
        &cataKey, edgePattern->metaCommon.dbId, edgePattern->metaCommon.namespaceId, edgePattern->metaCommon.metaName);
    bool isNameExist = SingleVersionLabelNameExist(cataCacheMgr->metaCache[CATA_EDGE_PATTERN], &cataKey);
    if (isNameExist) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DUPLICATE_TABLE, "Edge label name:%s.", cataKey.labelName);
        return GMERR_DUPLICATE_TABLE;
    }

    Status ret = CheckPrivEntityMetaLimit(dbInstance, edgePattern->creator, false, USER_META_EDGE_PATTERN);
    if (ret != GMERR_OK) {
        return ret;
    }

    return CheckRelatedVertexLabelForEdgePattern(edgePattern);
}

// 统一负责VertexLabelAddRelatedEdgePatternById中新开辟对象在异常分支下的内存释放
static void FreeUpdatingEdgePatternInfo(
    DbInstanceHdT dbInstance, DbListT *infoList, bool isNewList, DmEdgePatternInfoT *newInfo, uint32_t *key)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    if (isNewList && (infoList != NULL)) {
        DbDestroyList(infoList);
        DbDynMemCtxFree(cataCacheMgr->dynMemCtx, infoList);
    }
    if (newInfo != NULL) {
        if (newInfo->edgeName != NULL) {
            DbDynMemCtxFree(cataCacheMgr->dynMemCtx, newInfo->edgeName);
        }
        DbDynMemCtxFree(cataCacheMgr->dynMemCtx, newInfo);
    }
    if (key != NULL) {
        DbDynMemCtxFree(cataCacheMgr->dynMemCtx, key);
    }
}

Status VertexLabelAddRelatedEdgePatternById(
    uint32_t vlId, const DmEdgePatternInfoT *edgePatternInfo, DbInstanceHdT dbInstance)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    // 1.lookup vid2RelatedEdgePattern找到点标签对应存储依赖信息的列表
    DbListT *infoList = (DbListT *)DbOamapLookup(cataCacheMgr->vid2RelatedEdgePattern, vlId, &vlId, NULL);
    bool isNewList = false;
    uint32_t *key = NULL;
    DmEdgePatternInfoT *newInfo = NULL;
    if (infoList == NULL) {
        isNewList = true;
        // 没有找到，则是初次加入依赖信息，创建列表
        infoList = (DbListT *)DbDynMemCtxAlloc(cataCacheMgr->dynMemCtx, sizeof(DbListT));
        if (infoList == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED,
                "create dblist for edgepatterninfo, size:%" PRIu32 ".", (uint32_t)sizeof(DbListT));
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        DbCreateList(infoList, sizeof(DmEdgePatternInfoT), cataCacheMgr->dynMemCtx);
    }
    // 2.深拷贝一份输入的edgepatterninfo信息，加入到列表中
    newInfo = (DmEdgePatternInfoT *)DbDynMemCtxAlloc(cataCacheMgr->dynMemCtx, sizeof(DmEdgePatternInfoT));
    if (newInfo == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "create edgepatterninfo, size:%" PRIu32 ".",
            (uint32_t)sizeof(DmEdgePatternInfoT));
        FreeUpdatingEdgePatternInfo(dbInstance, infoList, isNewList, newInfo, key);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    Status ret = DmCopyEdgePatternInfo(cataCacheMgr->dynMemCtx, edgePatternInfo, newInfo);
    if (ret != GMERR_OK) {
        FreeUpdatingEdgePatternInfo(dbInstance, infoList, isNewList, newInfo, key);
        return ret;
    }
    ret = DbAppendListItem(infoList, newInfo);
    if (ret != GMERR_OK) {
        FreeUpdatingEdgePatternInfo(dbInstance, infoList, isNewList, newInfo, key);
        return ret;
    }
    // 3.若是新创建的列表对象，需要insert到vid2RelatedEdgePattern
    if (isNewList) {
        key = (uint32_t *)DbDynMemCtxAlloc(cataCacheMgr->dynMemCtx, sizeof(uint32_t));
        if (key == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED,
                "create key for edgepatterninfo, size:%" PRIu32 ".", (uint32_t)sizeof(uint32_t));
            FreeUpdatingEdgePatternInfo(dbInstance, infoList, isNewList, newInfo, key);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        *key = vlId;
        ret = DbOamapInsert(cataCacheMgr->vid2RelatedEdgePattern, *key, key, infoList, NULL);
        if (ret != GMERR_OK) {
            FreeUpdatingEdgePatternInfo(dbInstance, infoList, isNewList, newInfo, key);
            return ret;
        }
    }
    return GMERR_OK;
}

Status CreateEdgePatternInfoAndUpdateById(const DmEdgePatternT *edgePattern)
{
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(edgePattern->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DmEdgePatternInfoT edgePatternInfo;
    edgePatternInfo.edgeLabelId = edgePattern->metaCommon.metaId;
    edgePatternInfo.edgeNameLen = DM_STR_LEN(edgePattern->metaCommon.metaName);
    edgePatternInfo.edgeName = (char *)DbDynMemCtxAlloc(cataCacheMgr->dynMemCtx, edgePatternInfo.edgeNameLen);
    if (edgePatternInfo.edgeName == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_MEMORY_OPERATE_FAILED, "Malloc edgeName, len is %" PRIu32 ".", edgePatternInfo.edgeNameLen);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    errno_t err = strcpy_s(edgePatternInfo.edgeName, edgePatternInfo.edgeNameLen, edgePattern->metaCommon.metaName);
    DB_ASSERT(err == EOK);

    Status ret = VertexLabelAddRelatedEdgePatternById(edgePattern->sourceVertexLabelId, &edgePatternInfo, dbInstance);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(cataCacheMgr->dynMemCtx, edgePatternInfo.edgeName);
        return ret;
    }
    if (edgePattern->edgeType != EMPTY_EDGE) {
        ret = VertexLabelAddRelatedEdgePatternById(edgePattern->destVertexLabelId, &edgePatternInfo, dbInstance);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(cataCacheMgr->dynMemCtx, edgePatternInfo.edgeName);
            return ret;
        }
    }
    DbDynMemCtxFree(cataCacheMgr->dynMemCtx, edgePatternInfo.edgeName);
    return ret;
}

Status SaveEdgePatternInner(DmEdgePatternT *edgePattern)
{
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(edgePattern->memCtx);
    CataCacheMgrT *cata = DmGetCataCache(dbInstance);
    Status ret = SingleVersionPut(dbInstance, cata->metaCache[CATA_EDGE_PATTERN], (DmMetaCommonT *)(void *)edgePattern);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CreateEdgePatternInfoAndUpdateById(edgePattern);
    if (ret != GMERR_OK) {
        edgePattern->metaCommon.isDeleted = true;
        // 刚刚put成功，此处删除应该必定成功
        Status state =
            SingleVersionRemove(cata->metaCache[CATA_EDGE_PATTERN], (const DmMetaCommonT *)(const void *)edgePattern);
        DB_ASSERT(state == GMERR_OK);
        return ret;
    }
    UpdateBelongSpaceLabelCount(&edgePattern->metaCommon, true, dbInstance);
    g_gmdbEdgePatternNum++;
    (void)ModifyPrivEntityMetaCreatedNum(dbInstance, edgePattern->creator, false, USER_META_EDGE_PATTERN, true);
    return GMERR_OK;
}

Status CataSaveEdgePattern(const DmEdgePatternT *edgePattern)
{
    DB_POINTER(edgePattern);
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(edgePattern->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->cataLock);

    Status ret = CheckEdgePatternForCreate(edgePattern);
    if (ret != GMERR_OK) {
        DbRWSpinWUnlock(&cataCacheMgr->cataLock);
        return ret;
    }
    // 深拷贝一份元数据
    DmEdgePatternT *copyPattern = NULL;
    ret = DmCreateEmptyEdgeLabelWithMemCtx((DbMemCtxT *)cataCacheMgr->dynMemCtx, &copyPattern);
    if (ret != GMERR_OK) {
        DbRWSpinWUnlock(&cataCacheMgr->cataLock);
        return ret;
    }
    ret = DmCopyEdgeLabel(edgePattern, copyPattern);
    if (ret != GMERR_OK) {
        DbRWSpinWUnlock(&cataCacheMgr->cataLock);
        DmDestroyEdgeLabel(copyPattern);
        return ret;
    }
    copyPattern->metaCommon.refCount = 0;
    copyPattern->metaCommon.isDeleted = false;
    ret = SaveEdgePatternInner(copyPattern);
    if (ret != GMERR_OK) {
        DmDestroyEdgeLabel(copyPattern);
    }
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status RemoveEdgePatternWhenRelease(DmEdgePatternT *edgePattern)
{
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(edgePattern->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    Status ret = SingleVersionRemove(
        cataCacheMgr->metaCache[CATA_EDGE_PATTERN], (const DmMetaCommonT *)(const void *)edgePattern);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = VertexLabelDeleteRelatedEdgePatternById(
        edgePattern->sourceVertexLabelId, edgePattern->metaCommon.metaId, dbInstance);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (edgePattern->edgeType != EMPTY_EDGE) {
        ret = VertexLabelDeleteRelatedEdgePatternById(
            edgePattern->destVertexLabelId, edgePattern->metaCommon.metaId, dbInstance);
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    DmDestroyEdgeLabel(edgePattern);
    return GMERR_OK;
}

Status CataReleaseEdgePattern(DmEdgePatternT *edgePattern)
{
    DB_POINTER(edgePattern);
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(edgePattern->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    Status ret = GMERR_OK;
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    if (edgePattern->metaCommon.refCount >= 1) {
        (void)DbAtomicDec(&edgePattern->metaCommon.refCount);
        if (edgePattern->metaCommon.refCount == 0 && edgePattern->metaCommon.isDeleted) {
            ret = RemoveEdgePatternWhenRelease(edgePattern);
        }
    } else {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "Edge Pattern name:%s.", edgePattern->metaCommon.metaName);
        ret = GMERR_INTERNAL_ERROR;
    }
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

void CataGetReleatedEdgeLabelInfoListByVertexLabelId(
    uint32_t vertexLabelId, DbListT **edgeInfoList, DbInstanceHdT dbInstance)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    *edgeInfoList = (DbListT *)DbOamapLookup(cataCacheMgr->vid2RelatedEdgePattern, vertexLabelId, &vertexLabelId, NULL);
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return;
}

#ifdef __cplusplus
}
#endif
