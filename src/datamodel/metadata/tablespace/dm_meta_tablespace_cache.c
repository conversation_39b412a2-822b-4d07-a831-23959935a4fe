/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: tablespace struct cache
 * 本文件中主要实现tablespace结构体的缓存
 * Author: linchunbo
 * Create: 2022-08-31
 */

#include "dm_meta_tablespace.h"
#include "dm_meta_log.h"
#include "db_table_space.h"
#include "dm_meta_basic.h"
#include "dm_meta_basic_in.h"
#include "dm_meta_key_oper.h"
#include "dm_meta_namespace.h"
#include "dm_cache_single_ver_mgr.h"
#include "dm_meta_user.h"
#include "dm_meta_rsm.h"
#include "dm_log.h"

#ifdef __cplusplus
extern "C" {
#endif

// 请统一使用此函数设置tsp nameMap的哈希值
void SetCataKeyForTsp(CataKeyT *cataKey, const char *tspName)
{
    CataSetKeyForLabel(cataKey, DEFAULT_DATABASE_ID, DB_INVALID_UINT32, tspName);
}

Status CheckTspForCreate(const DmTablespaceT *tsp)
{
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(tsp->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    // 不感知undo tablespace, 此处仍以总数65个tsp为上限
    uint32_t tablespaceMaxNum = DB_USER_TABLE_SPACE_NUM + DB_DEFAULT_TABLE_SPACE_NUM + RsmGetRsmTspMaxNum();
    if (SECUREC_UNLIKELY(DbOamapUsedSize(cataCacheMgr->metaCache[CATA_TSP]->nameMap) >= tablespaceMaxNum)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "Create tsp.");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }

    CataKeyT cataKey;
    SetCataKeyForTsp(&cataKey, tsp->metaCommon.metaName);
    if (SECUREC_UNLIKELY(SingleVersionLabelNameExist(cataCacheMgr->metaCache[CATA_TSP], &cataKey))) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DUPLICATE_OBJECT, "Create tsp:%s.", tsp->metaCommon.metaName);
        return GMERR_DUPLICATE_OBJECT;
    }

    return CheckPrivEntityMetaLimit(dbInstance, tsp->creator, tsp->isGroupCreate, USER_META_TSP);
}

Status CataCreateTsp(const DmTablespaceT *tsp)
{
    DB_POINTER(tsp);
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(tsp->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    Status ret = CheckTspForCreate(tsp);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT;
    }

    DmTablespaceT *newTsp = NULL;
    // 此函数为创建并存储元数据函数，如果创建过程中失败，会在函数中释放; 创建成功后，需调用DmDestroyTablespace释放
    // 并发方案：支持并发，catalog模块内有锁看护
    ret = DmCreateEmptyTablespace(cataCacheMgr->dynMemCtx, &newTsp);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT;
    }
    ret = DmCopyTablespace(tsp, newTsp);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DmDestroyTablespace(newTsp);
        goto EXIT;
    }
    // 存储到catalog前将nspId和dbId设置为默认值，防止生成的hashKey错误
    newTsp->metaCommon.namespaceId = DB_INVALID_UINT32;
    newTsp->metaCommon.dbId = DEFAULT_DATABASE_ID;
    ret = SingleVersionPut(dbInstance, cataCacheMgr->metaCache[CATA_TSP], (DmMetaCommonT *)newTsp);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DmDestroyTablespace(newTsp);
        goto EXIT;
    }
    (void)ModifyPrivEntityMetaCreatedNum(dbInstance, newTsp->creator, newTsp->isGroupCreate, USER_META_TSP, true);
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
EXIT:
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    DB_LOG_ERROR(ret, "Tsp name:%s, id:%" PRIu32, tsp->metaCommon.metaName, tsp->metaCommon.metaId);
    return ret;
}

Status GetTspIdByNameNoLock(DbInstanceHdT dbInstance, const char *tspName, uint32_t *tspId)
{
    DmTablespaceT *tsp = NULL;
    CataKeyT cataKey;
    SetCataKeyForTsp(&cataKey, tspName);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    Status ret = SingleVersionGetByName(cataCacheMgr->metaCache[CATA_TSP], &cataKey, (DmMetaCommonT **)&tsp);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNDEFINED_OBJECT, "Get tsp id by name:%s.", tspName);
        return GMERR_UNDEFINED_OBJECT;
    }
    *tspId = tsp->metaCommon.metaId;
    return GMERR_OK;
}

SO_EXPORT_FOR_TS Status CataGetTspIdByName(const char *tspName, uint32_t *tspId, DbInstanceHdT dbInstance)
{
    DB_POINTER2(tspName, tspId);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = GetTspIdByNameNoLock(dbInstance, tspName, tspId);
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CataGetTspMgrIdxById(uint32_t tspId, uint32_t *tspMgrIdx, DbInstanceHdT dbInstance)
{
    DB_POINTER(tspMgrIdx);
    DmTablespaceT *tsp = NULL;
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);

    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = SingleVersionGetById(cataCacheMgr->metaCache[CATA_TSP], tspId, (DmMetaCommonT **)&tsp);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbRWSpinRUnlock(&cataCacheMgr->cataLock);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNDEFINED_OBJECT, "Get tspMgrIdx by id:%" PRIu32 ".", tspId);
        return GMERR_UNDEFINED_OBJECT;
    }
    *tspMgrIdx = tsp->tspMgrIdx;
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status UpdateTspLabelCountNoLock(uint32_t tspId, bool add, DbInstanceHdT dbInstance)
{
    DmTablespaceT *tsp = NULL;
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    Status ret = SingleVersionGetById(cataCacheMgr->metaCache[CATA_TSP], tspId, (DmMetaCommonT **)&tsp);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNDEFINED_OBJECT, "update tsp label count, id:%" PRIu32 ".", tspId);
        return GMERR_UNDEFINED_OBJECT;
    }

    if (add) {
        (void)DbAtomicInc(&tsp->labelCount);
    } else {
        (void)DbAtomicDec(&tsp->labelCount);
    }

    return GMERR_OK;
}

bool IsTablespaceIdExistNoLock(DbInstanceHdT dbInstance, uint32_t tspId)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    return SingleVersionLabelIdValid(cataCacheMgr->metaCache[CATA_TSP], tspId);
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
Status GetTspIdByNameIfExistNoLock(DbInstanceHdT dbInstance, const char *tspName, uint32_t *tspId)
{
    if (!IsTablespaceNameExistNoLock(dbInstance, tspName)) {
        *tspId = DB_INVALID_TABLE_SPACE_ID;
        return GMERR_OK;
    }
    return GetTspIdByNameNoLock(dbInstance, tspName, tspId);
}

Status CataGetTspIdByNameIfExist(const char *tspName, uint32_t *tspId, DbInstanceHdT dbInstance)
{
    DB_POINTER2(tspName, tspId);
    DbRWSpinRLock(&DmGetCataCache(dbInstance)->cataLock);
    Status ret = GetTspIdByNameIfExistNoLock(dbInstance, tspName, tspId);
    DbRWSpinRUnlock(&DmGetCataCache(dbInstance)->cataLock);
    return ret;
}

Status CheckIsUseRsmByTspNameNoLock(DbInstanceHdT dbInstance, const char *tspName, bool *isUseRsm)
{
    DB_POINTER2(tspName, isUseRsm);
    *isUseRsm = false;
    DmTablespaceT *tsp = NULL;
    CataKeyT cataKey;
    SetCataKeyForTsp(&cataKey, tspName);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    Status ret = SingleVersionGetByName(cataCacheMgr->metaCache[CATA_TSP], &cataKey, (DmMetaCommonT **)&tsp);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNDEFINED_OBJECT, "Get tsp id by name:%s.", tspName);
        return GMERR_UNDEFINED_OBJECT;
    }
    *isUseRsm = tsp->metaCommon.isUseRsm;
    return GMERR_OK;
}

Status CataCheckIsUseRsmByTspName(DbInstanceHdT dbInstance, const char *tspName, bool *isUseRsm)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = CheckIsUseRsmByTspNameNoLock(dbInstance, tspName, isUseRsm);
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return ret;
}

bool IsBoundByNsp(uint32_t tspId, DbInstanceHdT dbInstance)
{
    uint32_t *id = NULL;
    DbOamapIteratorT iter = 0;
    DmNamespaceT *nsp = NULL;
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    while (SingleVersionFetch(cataCacheMgr->metaCache[CATA_NSP], &iter, IsMetaUndeleted, &id, (DmMetaCommonT **)&nsp) ==
           GMERR_OK) {
        if (nsp->defaultTspId == tspId) {
            return true;
        }
    }
    return false;
}

Status RemoveTablespacePhysically(DbInstanceHdT dbInstance, DmTablespaceT *tsp)
{
    Status ret = SingleVersionRemove(DmGetCataCache(dbInstance)->metaCache[CATA_TSP], (const DmMetaCommonT *)tsp);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Remove tsp:%s.", tsp->metaCommon.metaName);
        return ret;
    }
    DmDestroyTablespace(tsp);
    return GMERR_OK;
}

Status DropTspInner(DmTablespaceT *tsp)
{
    if (tsp->labelCount != 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_RESTRICT_VIOLATION, "Remove tsp:%s.", tsp->metaCommon.metaName);
        return GMERR_RESTRICT_VIOLATION;
    }
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(tsp->memCtx);
    if (IsBoundByNsp(tsp->metaCommon.metaId, dbInstance)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_RESTRICT_VIOLATION, "Remove tsp:%s.", tsp->metaCommon.metaName);
        return GMERR_RESTRICT_VIOLATION;
    }

    tsp->metaCommon.isDeleted = true;
    (void)ModifyPrivEntityMetaCreatedNum(dbInstance, tsp->creator, tsp->isGroupCreate, USER_META_TSP, false);
    if (tsp->metaCommon.refCount == 0) {
        Status ret = RemoveTablespacePhysically(dbInstance, tsp);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status CataDropTspByName(const char *tspName, DbInstanceHdT dbInstance)
{
    DB_POINTER(tspName);
    DmTablespaceT *tsp = NULL;
    CataKeyT cataKey;
    SetCataKeyForTsp(&cataKey, tspName);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);

    DbRWSpinWLock(&cataCacheMgr->cataLock);
    Status ret = SingleVersionGetByName(cataCacheMgr->metaCache[CATA_TSP], &cataKey, (DmMetaCommonT **)&tsp);
    if (ret != GMERR_OK) {
        DbRWSpinWUnlock(&cataCacheMgr->cataLock);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNDEFINED_OBJECT, "Remove tsp by name:%s.", tspName);
        return GMERR_UNDEFINED_OBJECT;
    }
    ret = DropTspInner(tsp);
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CataGetTspById(DbInstanceHdT dbInstance, uint32_t tspId, DmTablespaceT **tsp)
{
    DB_POINTER(tsp);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = SingleVersionGetById(cataCacheMgr->metaCache[CATA_TSP], tspId, (DmMetaCommonT **)tsp);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbRWSpinRUnlock(&cataCacheMgr->cataLock);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNDEFINED_OBJECT, "Get tsp by id:%" PRIu32 ".", tspId);
        return GMERR_UNDEFINED_OBJECT;
    }
    (void)DbAtomicInc(&((*tsp)->metaCommon.refCount));
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CataGetTspByName(DbInstanceHdT dbInstance, const char *tspName, DmTablespaceT **tsp)
{
    DB_POINTER(tsp);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    CataKeyT cataKey;
    SetCataKeyForTsp(&cataKey, tspName);
    Status ret = SingleVersionGetByName(cataCacheMgr->metaCache[CATA_TSP], &cataKey, (DmMetaCommonT **)tsp);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DbRWSpinRUnlock(&cataCacheMgr->cataLock);
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNDEFINED_OBJECT, "Get tsp by name:%s.", tspName);
        return GMERR_UNDEFINED_OBJECT;
    }
    (void)DbAtomicInc(&((*tsp)->metaCommon.refCount));
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CataReleaseTsp(DmTablespaceT *tsp)
{
    DB_POINTER(tsp);
    // 在未标记删除的情况下加读锁，原子操作减 refCount，提升 release 操作性能
    bool hasReleased = false;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(tsp->memCtx);
    Status ret = ReleaseMetaQuickly(dbInstance, &tsp->metaCommon, CATA_TSP, &hasReleased);
    if (ret != GMERR_OK || hasReleased) {
        return ret;
    }

    DbRWSpinWLock(&DmGetCataCache(dbInstance)->cataLock);
    if (tsp->metaCommon.refCount == 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "Tablespace name:%s.", tsp->metaCommon.metaName);
        DbRWSpinWUnlock(&DmGetCataCache(dbInstance)->cataLock);
        return GMERR_INTERNAL_ERROR;
    }

    tsp->metaCommon.refCount--;
    if (tsp->metaCommon.refCount == 0 && tsp->metaCommon.isDeleted) {
        ret = RemoveTablespacePhysically(dbInstance, tsp);
    }
    DbRWSpinWUnlock(&DmGetCataCache(dbInstance)->cataLock);
    return ret;
}

bool IsTablespaceNameExistNoLock(DbInstanceHdT dbInstance, const char *tspName)
{
    DB_POINTER(tspName);
    CataKeyT cataKey;
    SetCataKeyForTsp(&cataKey, tspName);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    return SingleVersionLabelNameExist(cataCacheMgr->metaCache[CATA_TSP], &cataKey);
}

bool CataIsTablespaceNameExist(DbInstanceHdT dbInstance, const char *tspName)
{
    DB_POINTER(tspName);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    bool isExist = IsTablespaceNameExistNoLock(dbInstance, tspName);
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return isExist;
}
#endif /* FEATURE_SIMPLEREL */

#ifdef __cplusplus
}
#endif
