/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: DmMetaCommon
 * Create: 2024-1-8
 */

#ifndef DM_MINILABEL_COMMON_H
#define DM_MINILABEL_COMMON_H

#include "adpt_spinlock.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * DmMiniMetaCommonT结构体中isPersistent前面的变量需要与DmMetaCommonT结构体保持一致，
 * DmMetaCommonT结构体基本不会改动，但DmMetaCommonT如果发生变动，DmMiniMetaCommonT需要做相同的改动，
 * 如果需要在DmMiniMetaCommonT结构体中增加新的变量，请往后追加。
 */
typedef struct DmMiniMetaCommon {
    uint32_t dbId;
    uint32_t namespaceId;
    uint32_t tablespaceId;
    uint32_t version;
    uint32_t metaId;
    char *metaName;
    uint32_t refCount;
    bool isDeleted;
    bool isPersistent;
    uint64_t metaNameOffset;
    ShmemPtrT metaShmPtr;
    void *mgr;
    union {
        DbRWSpinLockT spinLock;
        void *tableLock;
    } lock;
} DmMiniMetaCommonT;

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // DM_MINILABEL_COMMON_H
