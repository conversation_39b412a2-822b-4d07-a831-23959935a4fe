/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2024. All rights reserved.
 * Description: header file for complex path
 * Author: gongsai
 * Create: 2022-09-05
 */

#ifndef DM_COMPLEX_PATH_H
#define DM_COMPLEX_PATH_H

#include "db_mem_context.h"
#include "db_hashmap.h"
#include "db_list.h"
#include "dm_meta_basic.h"
#include "dm_meta_subscription.h"
#include "dm_meta_topo_label.h"

#define PATHINFO_VERTEX_CACHE_MAP_INIT_CAP 200
#define PATHINFO_EDGE_CACHE_MAP_INIT_CAP 500
#define CATA_COMPLEX_PATHINFO_NUM_MAX 500
#define PATH_DEL_MODE_UNDEFINED 0
#define PATH_DEL_MODE_1 1
#define PATH_DEL_MODE_2 2
#define PATH_DEL_MODE_3 3
#define PATH_DEL_MODE_4 4

#ifdef __cplusplus
extern "C" {
#endif

/*
 * 树状path所需的vertex
 */
typedef struct PathInfoOrGroup {
    DbListT *edges;  // item<DmPathInfoEdgeT*>
} DmPathInfoOrGroupT;

typedef struct PathInfoVertex {
    uint32_t vertexLabelId;
    TextT pathInfoVertexName;  // 构成Path pattern的点的name，用于支持variable
    DbListT *inEdgeList;       // 入边集合（包括或组边/一般边）, item<DmPathInfoEdgeT*>
    DbListT *inOrGroupList;    // 入边或组集合, item<DmPathInfoOrGroupT*>
    DbListT *outEdgeList;      // 出边集合（包括或组边/一般边/空边）, item<DmPathInfoEdgeT*>
    DbListT *outOrGroupList;   // 出边或组集合, item<DmPathInfoOrGroupT*>
} DmPathInfoVertexT;

/*
 * 树状path所需的edge
 */
typedef struct PathInfoEdge {
    DmEdgeTypeE type;
    uint32_t edgeLabelId;                  // 一般边、空边有效
    DmPathInfoVertexT *srcPathInfoVertex;  // 源点，一般边/空边有效
    DmPathInfoVertexT *dstPathInfoVertex;  // 目的节点，一般边有效
} DmPathInfoEdgeT;

/*
 * 树状path
 */
typedef struct PathPattern {
    DmPathInfoVertexT *root;       // 树状path的头节点
    uint32_t pathInfoVertexCount;  // 总节点数
    uint32_t pathInfoEdgeCount;    // 总边数
} DmPathPatternT;

/*
 * 订阅字段信息
 */
typedef struct SubField {
    TextT *fieldName;  // 字段name
    uint32_t fieldId;  // 字段id
    char *specifyPathName;
    uint32_t specifyPathId;
} DmSubFieldT;

/*
 * subFields related with vertex
 */
typedef struct VertexSubFields {
    uint32_t vertexLabelId;
    TextT *vertexLabelName;
    uint32_t encapType;  // 封装类型，由app对每张表定义
    DbListT subFields;   // 订阅字段item<DmSubFieldT>
} DmVertexSubFieldsT;

typedef struct DmVertexLabelAndFieldTuple {
    uint32_t vertexLabelId;
    TextT vertexLabelName;
    TextT fieldName;   // 字段name
    uint32_t fieldId;  // 字段id
} DmVertexLabelAndFieldTupleT;

// PID field
typedef DmVertexLabelAndFieldTupleT DmPIDFieldT;
typedef DmVertexLabelAndFieldTupleT DmVertexLabelPropertyT;

typedef enum {
    DM_SET_CLAUSE_PROPERTY,
    DM_SET_CLAUSE_COALESCE_FUNC,
} DmSetClauseTagE;

typedef struct {
    DmVertexLabelPropertyT *coalesceLeftProp;
    DmVertexLabelPropertyT *coalesceRightProp;
} DmCoalesceClauseT;

typedef struct {
    DmSetClauseTagE tag;
    DmVertexLabelPropertyT *leftProp;
    union {
        DmVertexLabelPropertyT *rightProp;
        DmCoalesceClauseT *coalesceClause;
    };
} DmSetClauseT;

typedef enum { DM_COMPLEX_PATH_NORMAL, DM_COMPLEX_PATH_MERGE, DM_COMPLEX_PATH_REPLACE } DmComplexPathTypeE;

typedef struct {
    // These fields are used for merge and replace paths
    uint32_t viewTableId;
    DbListT setClauses;  // item<DmSetClauseT*>
} DmGeneratePathInfoT;

typedef enum PathTlvE {
    DM_PATH_TLV_DEFAULT = 0,  // the default format of path instance
    DM_PATH_TLV_1,            // the format of path instance when use DefinePathDwnldByTlv
} DmPathTlvE;

typedef struct DmPathCondition {
    uint32_t propId;
    DmValueT value;
} DmPathConditionT;

typedef struct DmPathConstraint {
    uint32_t vertexLabelId;
    DbListT conditions;  // item<DmPathConditionT>
} DmPathConstraintT;

/*
 * 树状path元数据
 */
typedef struct ComplexPathInfo {
    DmMetaCommonT metaCommon;  // 该成员必须放在最前面以供cata_cache类型强转
    DbMemCtxT *memCtx;
    uint32_t pathIdInApp;                       // 保留，app定义的pathId
    DmPathPatternT *pattern;                    // 树状path，由PathInfoVertex构成
    DbOamapT *vertexLabelId2PathInfoVertexMap;  // 哈希：通过vertexLabelId直接索引到list *，元素是PathInfoVertex *
    DbOamapT *vertexLabelId2SubFieldsMap[(
        int32_t)DM_SUBS_COMPLEXPATH_CEIL];  // 哈希：通过vertexId直接索引到该表上的订阅字段<DmVertexSubFieldsT>
    uint32_t subscriptionNum;               // 该path被订阅的次数
    uint16_t subEventNums[(int32_t)DM_SUBS_COMPLEXPATH_CEIL];  // 该path各event被订阅的次数
    char *creator;                                             // 创建该pathinfo的user
    uint32_t deletionMode;
    DmPIDFieldT *pidField;  // only for CSUB, it's NULL in other scenario
    DmPathTlvE pathTlv;
    // these are used for the mergePaths and replacePaths
    bool hasReplaceRootOrMergeView;  // Flags if the NORMAL path contains a merge view or a replace root
    DmComplexPathTypeE pathType;
    DmGeneratePathInfoT *generatePathInfo;
    DmPathConstraintT *constraint;  // path with constraint used for condition subscription
} DmComplexPathInfoT;

/* 记录vertexID所属的所有path列表 */
typedef struct VertexPathIds {
    uint32_t vertexLabelId;
    DbListT pathInfoIdList;  // <uint32_t>
} DmVertexPathIdsT;

typedef struct DmPathPatternInfo {
    uint32_t pathInfoId;       // path ID
    uint32_t pathInfoNameLen;  // path名称长度
    char *pathInfoName;        // path名称
} DmPathPatternInfoT;

/*
 * 创建一个空的ComplexPathInfo结构体，该ComplexPathInfo所有的内存申请和释放都在指定memCtx下完成
 */
Status DmCreateEmptyComplexPathInfoWithMemCtx(DbMemCtxT *memCtx, DmComplexPathInfoT **pathInfo);

/*
 * 释放ComplexPathInfo结构体中的所有内存，含该结构体自身的内存
 */
void DmDestroyComplexPathInfo(DmComplexPathInfoT *pathInfo);

/*
 * 服务于Catalog模块的元数据深拷贝
 */
Status DmCopyComplexPathInfo(const DmComplexPathInfoT *srcPathInfo, DmComplexPathInfoT *dstPathInfo);

/*
 * 创建一个空的PathPattern结构体，该PathPattern所有的内存申请和释放都在指定memCtx下完成
 */
Status DmCreateEmptyPathPatternWithMemCtx(DbMemCtxT *memCtx, DmPathPatternT **pathPattern);

/*
 * 创建一个空的PiEdge结构体，该PiEdge所有的内存申请和释放都在指定memCtx下完成
 */
Status DmCreateEmptyPathInfoEdgeWithMemCtx(DbMemCtxT *memCtx, DmPathInfoEdgeT **pathInfoEdge);

/*
 * 创建一个空的PiVertex结构体，该PiVertex所有的内存申请和释放都在指定memCtx下完成
 */
Status DmCreateEmptyPathInfoVertexWithMemCtx(DbMemCtxT *memCtx, DmPathInfoVertexT **pathInfoVertex);

/*
 * 根据inOrOut指示的边的方向，将piEdge加入到piVertex的出边/入边链表中
 * inOrOut为true代表入边，false代表出边
 */
Status DmAddEdgeToVertexByDirection(DmPathInfoVertexT *pathInfoVertex, DmPathInfoEdgeT **pathInfoEdge, bool inOrOut);

/*
 * 创建一个空的PiOrGroup结构体，该PiOrGroup所有的内存申请和释放都在指定memCtx下完成
 */
Status DmCreateEmptyPathInfoOrGroupWithMemCtx(DbMemCtxT *memCtx, DmPathInfoOrGroupT **pathInfoOrGroup);

/*
 * 将piEdge加入到或组piOrGroup中
 */
Status DmAddEdgeToOrGroup(DmPathInfoOrGroupT *pathInfoOrGroup, DmPathInfoEdgeT **pathInfoEdge);

/*
 * 根据inOrOut指示的或组的方向，将piEdge加入到piVertex的出边/入边或组链表中
 * inOrOut为true代表入边或组，false代表出边或组
 */
Status DmAddOrGroupToVertexByDirection(
    DmPathInfoVertexT *pathInfoVertex, DmPathInfoOrGroupT **pathInfoOrGroup, bool inOrOut);

/*
 * 创建一个空的vertexLabelId2PathInfoVertex哈希，该vertexLabelId2PathInfoVertex所有的内存申请和释放都在指定memCtx下完成
 */
Status DmCreateEmptyVertexLabelId2PathInfoVertexWithMemCtx(
    DbMemCtxT *memCtx, DbOamapT **vertexLabelId2PathInfoVertexMap);

/*
 * 向vertexLabelId2PathInfoVertexMap中插入pathInfoVertex
 * 若映射该pathInfoVertex的key还未创建，则创建<key,value>，再插入该节点；否则，插入到对应的value中
 */
Status DmAddPathInfoVertexToVertexLabelId2PathInfoVertexMap(
    DbMemCtxT *memCtx, DbOamapT *vertexLabelId2PathInfoVertexMap, DmPathInfoVertexT **pathInfoVertex);

/*
 * 创建一个空的DmVertexSubFieldsT，该DmVertexSubFieldsT下所有订阅字段都属于同一个订阅的事件类型
 */
Status DmCreateEmptyVertexSubFieldsWithMemCtx(DbMemCtxT *memCtx, DmVertexSubFieldsT **pathInfoVertexFields);

/*
 * 创建一个空的DmPathConstraintT
 */
Status DmCreateEmptyPathConstraintWithMemCtx(DbMemCtxT *memCtx, DmPathConstraintT **constraint);

/*
 * 将订阅字段插入到某个vertex的某个特定订阅事件下
 */
Status DmAddSubFieldsToVertexSubFields(DmVertexSubFieldsT *pathInfoVertexFields, DmSubFieldT *pathInfoSubFields);

/*
 * 深拷贝pathPatternInfo
 */
Status DmCopyPathPatternInfo(
    DbMemCtxT *memCtx, const DmPathPatternInfoT *srcPathPatternInfo, DmPathPatternInfoT *destPathPatternInfo);

/*
 * @brief check if vertexLabel exist in path pattern
 * @param pathInfo[in]: the pathInfo
 * @param vtxLabelId[in]: the VertexLabelId
 */
bool DmIsExistVertexLabelInPathPattern(const DmComplexPathInfoT *pathInfo, uint32_t vtxLabelId);

/**
 * @description: 保存complexpathinfo元数据至id2map和name2map中
 * @param pathInfo : 待保存的complexpathinfo元数据
 * @return 状态码
 */
Status CataSaveComplexPathInfo(const DmComplexPathInfoT *pathInfo);

/**
 * @description: 根据name删除ComplexPathInfo元数据
 * @param cataKey : 用于nameMap查找元数据的键
 * @return 状态码
 */
Status CataDropComplexPathInfoByName(const CataKeyT *cataKey, DbInstanceHdT dbInstance);

/**
 * @description: 根据id删除ComplexPathInfo元数据
 * @param pathInfoId : 用于idMap查找元数据的键
 * @return 状态码
 */
Status CataDropComplexPathInfoById(uint32_t pathInfoId, DbInstanceHdT dbInstance);

/**
 * @description: 根据name查找获取对ComplexPathInfo元数据的引用，并不deepcopy，
 *     引用计数+1，配套使用CataReleaseComplexPathInfo
 * @param cataKey : 用于nameMap查找元数据的键
 * @param pathInfo : 保存获取到的元数据引用
 * @return 状态码
 */
Status CataGetComplexPathInfoByName(const CataKeyT *cataKey, DmComplexPathInfoT **pathInfo, DbInstanceHdT dbInstance);

/**
 * @description: 根据id查找获取对ComplexPathInfo元数据的引用，并不deepcopy，
 *     引用计数+1，配套使用CataReleaseComplexPathInfo
 * @param cataKey : 用于idMap查找元数据的键
 * @param pathInfo : 保存获取到的元数据引用
 * @return 状态码
 */
Status CataGetComplexPathInfoById(uint32_t pathInfoId, DmComplexPathInfoT **pathInfo, DbInstanceHdT dbInstance);

/**
 * @description: 引用计数减1，若之前Drop过该元数据，当引用计数为0时，删除该元数据
 * @param pathInfo : non copy出的元数据
 * @return 状态码
 */
Status CataReleaseComplexPathInfo(DmComplexPathInfoT *pathInfo);

/**
 * @description: 根据vertexID，获取所有与其相关的pathID列表，该接口为深拷贝，需要使用者传入内存上下文
 * @param memCtx : 内存上下文句柄
 * @param vertexLabelId : 顶点标签ID
 * @param dstList : pathID列表集合
 * @return 状态码
 */
Status CataGetPathInfoIdListByVertexLabelId(DbMemCtxT *memCtx, uint32_t vertexLabelId, DbListT **dstList);

/**
 * @description: 获取Path相关的的vertexlabel的名称，该接口为深拷贝，需要使用者传入内存上下文
 * @attention 异常情况下和正常情况下调用方均需要释放list及其内部元素分配内存
 * @param memCtx : 内存上下文句柄
 * @param list : vertexLabel名称列表，元素类型为TextT
 * @return 状态码
 */
Status CataGetPathVertexLabelNames(DbMemCtxT *memCtx, DbListT *list);

/**
 * @description: 获取所有的edgeLabel的名称，该接口为深拷贝，需要使用者传入内存上下文
 * @attention 异常情况下和正常情况下调用方均需要释放list及其内部元素分配内存
 * @param memCtx : 内存上下文句柄
 * @param list : edgeLabel名称列表，元素类型为TextT
 * @return 状态码
 */
Status CataGetAllEdgeLabelNames(DbMemCtxT *memCtx, DbListT *list);

/**
 * @description: 获取所有的pathpattern的名称，该接口为深拷贝，需要使用者传入内存上下文
 * @attention 异常情况下和正常情况下调用方均需要释放list及其内部元素分配内存
 * @param memCtx : 内存上下文句柄
 * @param list : pathpattern名称列表，元素类型为TextT
 * @return 状态码
 */
Status CataGetAllPathPatternNames(DbMemCtxT *memCtx, DbListT *list);

#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif /* DM_COMPLEX_PATH_H */
