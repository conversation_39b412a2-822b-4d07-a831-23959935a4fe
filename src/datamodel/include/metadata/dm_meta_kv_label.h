/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: header file for dm_kv_table
 * Author: wangsheng
 * Create: 2021-3-24
 */

#ifndef DM_META_KV_LABEL_H
#define DM_META_KV_LABEL_H

#include "db_hashmap.h"
#include "dm_data_basic.h"
#include "dm_meta_basic.h"
#include "dm_meta_kv_index.h"
#include "dm_meta_prop_label.h"
#include "dm_meta_rsm_public.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CATA_KV_TABLE_MAX 1024  // kv label数量上限

struct DmKvLabel;

struct DmObject;

typedef enum DmKvTimeStatType {
    KV_SET_TIME_STATIS = 0,
    KV_REMOVE_TIME_STATIS = 1,
    KV_TIME_STATIS_END,
} DmKvTimeStatTypeE;

/**
 *  数据模型的基本类型，具体的数据类型都继承此类型
 */
typedef struct DmKvLabel {
    DmMetaCommonT metaCommon;  // 该成员必须放在最前面以供cata_cache类型强转
    DmLabelTypeE labelType;
    uint32_t labelLatchId;         // 标签latchId
    uint32_t labelLatchVersionId;  // 标签latchId的版本号
    DbMemCtxT *memCtx;
    uint64_t maxRecordNum;           // 该标签中最多可能存在的记录数目
    uint32_t subscriptionNum : 16;   // 该标签上的订阅数
    uint32_t stmgSubNum : 16;        // 该标签上的合并订阅数
    uint32_t klSize;                 // 元数据占用内存的大小，在CataSaveLabel中计算赋值
    bool maxRecordNumCheck;          // 该标签是否要检查最大记录数
    bool isGroupCreate;              // true 表示由 group 创建，false 表示由 user 创建
    ConcurrencyControlE ccType;      // 标识该标签对应的表的并发控制类型
    uint64_t trxId;                  // 记录最后一个操作过该KvLabel的事务Id（成功提交的）
    uint64_t trxIdLastModify;        // 记录最后一个修改过该KvLabel的事务Id（成功提交的）
    uint64_t trxCommitTime;          // 记录最后一个操作过该KvLabel的事务提交时间（成功提交的）
    IsolationLevelE isolationLevel;  // 标识该标签对应的表的隔离级别
    TrxTypeE trxType;                // 标识该标签对应的表的事务类型
    uint32_t configJsonLen;          // configJson的长度
    char *configJson;                // configJson，配置信息
#ifdef FEATURE_GQL
    bool skipRowLockPessimisticRR;  // cfg designed for FES, default: false
#endif
    bool needDefragmentation;     // 该点标签对应的表是否需要内存碎片整理
    bool disableSubBackPressure;  // 默认值为false, 如果为true, 则不允许创建可靠订阅
#ifdef WARM_REBOOT
    bool warmRebootStoreDisk;  // warm reboot场景是否需要导出表数据
#endif
#ifdef FEATURE_REPLICATION
    uint8_t replicationType;  // 主备的复制方式 DmReplicationTypeE
#endif
    bool isSupportReservedMemory;  // WarmReboot特性中表示是否支持使用保留内存
    ShmemPtrT heapShmAddr;         // 存储引擎Heap空间的共享内存入口addr
    ShmemPtrT labelLatchShmAddr;   // 全局控制表并发的latch共享内存addr
    DmKvIndexLabelT *index;
    char *creator;                                            // 创建该kv label的user
    DmDmlOperStatT dmlOperStat[(int32_t)KV_TIME_STATIS_END];  // DML操作执行次数统计信息
    DmDmlPerfStatT dmlPerfStat[(int32_t)KV_TIME_STATIS_END];  // DML操作执行时间统计信息
    uint32_t objPrivVersion;                                  // 对象权限版本号
    uint16_t subEventNums[(int32_t)DM_SUBS_KV_CEIL];          // 事件类型的订阅数量，事件有kv set/delete
    TagLinkedListT pidRefList;                                // 用于存储客户端的pid对元数据的refcount
    ShmemPtrT nspObjPrivShmPtr;  // label所属的nsp的对象权限，此处仅记录，create和drop label时不处理
    ShmemPtrT metaInfoShm;       // 服务端metaInfo存储的是共享内存addr
    DmReusableMetaT *metaInfoAddr;  // 客户端会把metaInfo共享内存addr转换为逻辑addr
    uint32_t recycleTryTime;  // 后台回收cltRef已尝试的次数，为0或大于40（120s）则会尝试回收其中的cltRef;
                              // 取表后置为1，每次定时器遍历到该表则+1，大于40后回收并置0
    uint32_t configJsonOffset;
    uint32_t indexOffset;
    uint32_t creatorOffset;
    uint64_t subFlowControlSleepTime;  // 使用64bit标识表流控sleepTime（us）,
                                       // 低0~20位标识一级流控时间,20~40位标识二级流控时间,40~60位标识三级流控时间
    DmRsmInfoT *rsmInfo;  // 当前暂不提供给客户端访问
} DmKvLabelT;

typedef struct DmObject {
    DmKvLabelT *label;
    DbMemCtxT *memCtx;
} DmObjectT;

// 统一的对外接口，根据label的类型调用相应的处理函数

Status DmCreateEmptyKvLabel(DbMemCtxT *memCtx, DmKvLabelT **label);

/*
 * Copy函数服务于Catalog模块的元数据深拷贝,memCtx不拷贝
 */
Status DmCopyKvLabel(DmKvLabelT *srcLabel, DmKvLabelT *destLabel);

/*
 * 释放DmKvLabelT结构体中的所有内存，含该结构体自身的内存
 */
void DmDestroyKvLabel(DmKvLabelT *label);

/*
 * 创建KV index
 */
Status DmCreateKvIndex(DbMemCtxT *memCtx, const DmKvLabelT *label, DmKvIndexLabelT **index);

/**
 * @brief fetch kvTable by nsp id
 * @param[in] nspId
 * @param[in] iter
 * @return get a new kvTable for each fetch, null if end of fetch
 */
DmKvLabelT *CataFetchKvTableByNspId(uint32_t nspId, DbOamapIteratorT *iter, DbInstanceHdT dbInstance);

/**
 * @brief: 根据labelId设置该kvLabel的trxId值和提交时间
 * @param[in] labelId kvLabel的ID
 * @param[in] trxId 操作过kvLabel的最后一个事务的ID
 * @param[in] trxCommitTime 事务提交时间
 * @param[in] trxIsModify 事务是否修改过该表
 * @param[in] dbInstance 当前实例
 * @return: 若该kvLabel不存在，返回错误码，否则返回success
 */
Status CataSetKvLabelTrxIdAndTrxCommitTimeById(
    uint32_t labelId, uint64_t trxId, uint64_t trxCommitTime, bool trxIsModify, DbInstanceHdT dbInstance);

/**
 * @brief: 根据labelId获取该kvLabel的trxId和提交时间
 * @param[in] labelId kvLabel的ID
 * @param[out] trxId 操作过kvLabel的最后一个事务的ID
 * @param[out] trxIdLastModify 最后修改过kvLabel的事务的ID
 * @param[out] trxCommitTime 事务提交时间
 * @param[in] dbInstance 当前实例
 * @return: 若该kvLabel不存在，返回错误码，否则返回success
 */
Status CataGetKvLabelTrxIdAndTrxCommitTimeById(
    uint32_t labelId, uint64_t *trxId, uint64_t *trxIdLastModify, uint64_t *trxCommitTime, DbInstanceHdT dbInstance);

/**
 * @brief: 根据labelId获取该kvLabel的labelName
 * @param[in] labelId kvLabel的ID
 * @param[out] labelName kvLabel的名字
 * @return: 若该kvLabel不存在，返回错误码，否则返回success
 */
Status CataGetLabelNameByKvLabelId(uint32_t labelId, char *labelName, DbInstanceHdT dbInstance);

// 下面的接口用label来命名，暂时只用来操作kv table
/**
 * Catalog manager save label
 * @brief save label
 * @param[in] label
 * 入参label会被显式深拷贝一份，入参的生命周期由调用者管理，调用结束后，入参是否销毁不会影响已经保存的元数据
 */
Status CataSaveLabel(DmKvLabelT *label);

/**
 * Catalog remove label by id
 * @brief 根据labelId在元数据中删除该点标签所有的元数据
 * @param[in] dbInstance DB实例句柄
 * @param[in] labelId
 */
Status CataRemoveLabelById(DbInstanceHdT dbInstance, uint32_t labelId);

/**
 * Catalog manager get label by name non copy interface
 * @brief get label non copy by name
 * @param[in] cataKey cataKey
 * @param[out] label pointer where the label is stored in catalog
 * label结构体没有进行copy，返回指向catalog中的指针，调用者不能进行修改。
 * 本接口把label中的引用计数refCount加1
 * 调用者使用label指针结束后，需要显式调用CataReleaseLabel接口将引用计数refCount减1，方便回收label
 */
Status CataGetLabelByName(const CataKeyT *cataKey, DmKvLabelT **label, DbInstanceHdT dbInstance);

/**
 * Catalog manager get kv table by id non copy interface
 * @brief get kv table non copy by name
 * @param[in] labelId
 * @param[out] label pointer where the label is stored in catalog
 * label结构体没有进行copy，返回指向catalog中的指针，调用者不能进行修改
 * 本接口把label中的引用计数refCount加1
 * 调用者使用label指针结束后，需要显式调用CataReleaseLabel接口将引用计数refCount减1，方便回收label
 */
Status CataGetLabelById(uint32_t labelId, DmKvLabelT **label, DbInstanceHdT dbInstance);

/**
 * Catalog decrement kv table ref count interface for non copy
 * @brief decrement kv table ref count
 * @param[in] label
 * 调用者不再使用label时，需要显式调用此接口将Label的引用计数减1
 */
Status CataReleaseLabel(DmKvLabelT *label);

/**
 * @brief: 获取所有符合条件的未被删除的 KvLabel
 * @param[in] list 保存了KvLabel指针，维护了引用计数，上层需要手工释放
 * @param[in] filter 过滤函数，可为 NULL
 * @return: 返回状态码
 */

Status CataGetAllKvLabel(DbInstanceHdT dbInstance, DbListT *list, bool (*filter)(DmKvLabelT *));

#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif /* DM_META_KV_LABEL_H */
