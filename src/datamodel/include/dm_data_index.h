/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * Description: header file for data model IndexKey
 * Author: zhangxiaolei
 * Create: 2020-8-7
 */

#ifndef DM_DATA_INDEX_H
#define DM_DATA_INDEX_H

#include "db_mem_context.h"
#include "dm_data_basic.h"
#include "dm_data_strudefs.h"
#include "dm_meta_prop_label.h"
#include "gmc_errno.h"

#ifdef __cplusplus
extern "C" {
#endif

#define LEFT_SHIFT_UINT8 7
#define LEFT_SHIFT_UINT16 15
#define LEFT_SHIFT_UINT32 31
#define LEFT_SHIFT_UINT64 63

/* sizeof(uint32_t) totalLength
 * sizeof(uint32_t) dbId
 * sizeof(uint32_t) labelId
 * sizeof(uint32_t) indexId
 * sizeof(bool) isNullable
 * sizeof(uint8_t) DmIndexConstraintE
 * sizeof(uint8_t) DmIndexTypeE
 * sizeof(uint32_t) keyBufLen
 */
#define INDEX_KEY_BUF_LENGTH_BEFORE_MAX_KEY_LEN 23

typedef struct DmVertex DmVertexT;

typedef struct DmIndexKey DmIndexKeyT;

typedef struct DmIndexTupleTank {
    DbMemCtxT *memCtx;
    uint8_t *ptr;
    DmIndexKeyT ***indexKey;
} DmIndexTupleTankT;

typedef struct PartialKeyInfo {
    uint32_t propeNum;
    uint32_t idx;
    uint8_t curOffset;
} PartialKeyInfoT;
typedef struct DmIndexKeyPrefix {
    uint8_t *keyBuf;          // 待反序列化的key值buffer
    uint32_t prefixPropeNum;  // 前缀属性数量
} DmIndexKeyPrefixT;

typedef enum DmIndexKeyCmpResult {
    DM_INDEX_KEY_EQ,   // 左右key相等
    DM_INDEX_KEY_LT,   // 左key 小于 右key
    DM_INDEX_KEY_GT,   // 左key 大于 右key
    DM_INDEX_KEY_BUTT  // 右key 出现不允许出现的null值
} DmIndexKeyCmpResultE;

static inline uint8_t DmGetNullInfoBytes(uint32_t propNum)
{
    DB_ASSERT(propNum <= DM_MAX_KEY_PROPE_NUM);
    if (propNum == 0) {
        return 1u;
    }
    return (uint8_t)((propNum + UINT8_SIZE - 1) / UINT8_SIZE);
}
/*
 * 从key buffer中获取属性值
 * 输入参数：keyBuf待反序列化的key值, indexLabel提供index元数据信息
 * 输出参数：propertyValues为属性值数组，长度为indexLabel->propertyNum
 */
SO_EXPORT Status DmGetPropeValuesFromKeyBuf(uint8_t *keyBuf, DmVlIndexLabelT *indexLabel, DmValueT *propertyValues);
/*
 * 从key buffer中获取前propertyNum个属性值
 * 输入参数：keyBuf待反序列化的key值, indexLabel提供index元数据信息, propertyNum获取key buffer属性值的个数
 * 输出参数：propertyValues为属性值数组，长度为propertyNum
 */
SO_EXPORT Status DmGetForwardPropeValuesFromKeyBuf(
    uint8_t *keyBuf, DmVlIndexLabelT *indexLabel, const uint32_t forwardPropertyNum, DmValueT *propertyValues);
/*
 * create IndexKey by indexName
 * memory alloc and free all in given memCtx
 * input parameters: memCtx, vertexLabel, indexName, propertyValues, propertyNum
 * output parameters: indexKey(malloc and free by the Dm)
 * propertyValues order is the same as the property order in indexLabel
 * indexKey memory and the inner memory of indexKey is malloc and free by Dm
 * invoker ensure memory alloc and free in the same MemContext
 */
SO_EXPORT Status DmCreateIndexKeyByNameWithMemCtx(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, const char *indexName,
    DmValueT propertyValues[], uint32_t propertyNum, DmIndexKeyT **indexKey);

/*
 * create indexKey by indexLabel
 * memory alloc and free all in given memCtx
 * input parameters: indexLabel, propertyValues, propertyNum
 * output parameters: indexKey(malloc and free by the Dm)
 * invoker ensure memory alloc and free in the same MemContext
 */
SO_EXPORT Status DmCreateIndexKeyByIdxLabelWithMemCtx(DbMemCtxT *memCtx, DmVlIndexLabelT *indexLabel,
    DmValueT propertyValues[], uint32_t propertyNum, DmIndexKeyT **indexKey);

/*
 * create empty indexKey by indexLabel
 * memory alloc and free all in current memCtx or given memCtx (withMemCtx interface)
 */
SO_EXPORT Status DmCreateEmptyIndexKeyByIdxLabelWithMemCtx(
    DbMemCtxT *memCtx, DmVlIndexLabelT *indexLabel, DmIndexKeyT **indexKey);

/*
 * set keyBuf for empty indexKey
 * invoker ensure value type is correct
 */
SO_EXPORT Status DmIndexKeySetKeyBufByPropeValues(DmIndexKeyT *indexKey, DmVlIndexLabelT *indexLabel,
    DmValueT propertyValues[], uint32_t propeNum, bool isKeyNullable);

/*
 * get the total length of the indexKey
 * input parameters: indexKey
 * output parameters: NULL
 */
uint32_t DmIndexKeyGetSeriBufLength(const DmIndexKeyT *indexKey);

/*
 * serialize indexKey
 * length是通过DmIndexKeyGetSeriBufLength计算的，所以这里不进行校验buf长度
 * 调用方需要保证length合法
 * input parameters: indexKey, length
 * output parameters: seriBuf(malloc and free by the invoker)
 */
SO_EXPORT Status DmSerializeIndexKey2InvokerBuf(const DmIndexKeyT *indexKey, uint32_t length, uint8_t *seriBuf);

/*
 * deserialize indexKey
 * input parameters: memCtx, seriBuf, totalLength (seriBuf中包含IndexKey序列化时的totalLength信息)
 * output parameters: indexKey
 * memory alloc and free all in given memCtx
 */
SO_EXPORT Status DmDeSerializeIndexKeyWithMemCtx(
    DbMemCtxT *memCtx, uint8_t *seriBuf, uint32_t totalLength, DmIndexKeyT **indexKey);

SO_EXPORT Status DmDeSeri2ExistsIndexKey(uint8_t *seriBuf, uint32_t totalLength, DmIndexKeyT *indexKey);

/*
 * indexKeyBuf 合法性校验，校验buf本身是否合法，与传入的indexLabel是否匹配
 */
Status DmIndexKeyBufCheckIsLegal(uint8_t *seriBuf, uint32_t totalLength, DmVlIndexLabelT *indexLabel);

inline static Status DmGetMaxKeyLenFormIndexBuf(uint8_t *seriBuf, uint32_t totalLength, uint32_t *maxKeyLen)
{
    DB_POINTER2(seriBuf, maxKeyLen);
    if (totalLength < (uint32_t)(INDEX_KEY_BUF_LENGTH_BEFORE_MAX_KEY_LEN + (uint32_t)sizeof(uint32_t))) {
        return GMERR_DATA_EXCEPTION;
    }
    uint8_t *tmp = seriBuf + INDEX_KEY_BUF_LENGTH_BEFORE_MAX_KEY_LEN;
    *maxKeyLen = *((uint32_t *)tmp);
    return GMERR_OK;
}

/*
 * serialize indexKey key
 * buf (malloc and free by the Dm) can not be free by invoker
 */
static inline void DmIndexKeyGetKeyBuf(const DmIndexKeyT *indexKey, uint8_t **keyBuf, uint32_t *length)
{
    DB_POINTER3(indexKey, keyBuf, length);
    *keyBuf = indexKey->keyBuf;
    *length = indexKey->keyBufLen;
}

uint32_t DmIndexKeyGetKeyBufLen(const DmIndexKeyT *indexKey);

/*
 * free the memory of the indexKey
 * input parameters: indexKey
 * output parameters: null
 */
void DmDestroyIndexKey(DmIndexKeyT *indexKey);

/*
 * copy indexKey (临时接口，供EE订阅时的联调串通，后续待EE整理具体需求后，再提供一套对应的接口)
 * input parameters: memctx, srcIndexKey
 * output parameters: destIndexKey
 */
SO_EXPORT Status DmCopyIndexKeyWithMemCtx(
    DbMemCtxT *memCtx, const DmIndexKeyT *srcIndexKey, DmIndexKeyT **destIndexKey);

/*
 * get isPk member in IndexKey
 */
bool DmIndexKeyIsPk(const DmIndexKeyT *indexKey);

/*
 * get indexId member in IndexKey
 */
uint32_t DmIndexKeyGetIndexId(const DmIndexKeyT *indexKey);

typedef struct DmTransKeyCmpPara {
    uint8_t *leftKeyBuf;      // 入参
    uint8_t *rightKeyBuf;     // 入参
    bool isLeftIncluded;      // 入参
    bool isRightIncluded;     // 入参
    MatchTypeE keyMatchType;  // 入参
    uint32_t matchBytes;      // 入参
    uint32_t condIdx;         // 入参
    uint32_t nearAIdx;
    uint32_t nearBIdx;
} DmTransKeyCmpParaT;

typedef struct DmPrefixMatchKeyCmpPara {
    uint32_t condIdx;        // 第几个属性
    uint32_t matchBytesNum;  // 前缀匹配多少个字节
    uint32_t nearAIdx;
    uint32_t nearBIdx;
    int32_t nearDiff;
} DmPrefixMatchKeyCmpParaT;

uint32_t GetPropNumByIdxLabel(const DmVlIndexLabelT *indexLabel, uint8_t *leftKeyBuf, uint8_t *rightKeyBuf);
Status TransLeftKeyForVertexCmpInner(
    DmVlIndexLabelT *indexLabel, bool isLeftIncluded, const PartialKeyInfoT *keyInfo, uint8_t *leftKeyBuf);
Status TransRightKeyForVertexCmpInner(
    DmVlIndexLabelT *indexLabel, bool isRightIncluded, const PartialKeyInfoT *keyInfo, uint8_t *rightKeyBuf);
SO_EXPORT Status DmTransKeyForVertexCmp(DmVlIndexLabelT *indexLabel, DmTransKeyCmpParaT *par);

/**
 * @brief 基于vertex获取key buffer
 * @param vertex 顶点句柄
 * @param keyBuf 出参，获取的key buffer
 * @return void
 */
inline static void DmVertexGetVertexKeyBuf(const DmVertexT *vertex, uint8_t **keyBuf)
{
    DB_POINTER2(vertex, keyBuf);
    *keyBuf = vertex->vertexKeyBuf;
}

/**
 * @brief 获取vertex的compareKeyBuf
 * @param vertex 顶点句柄
 * @param keyBuf 出参，获取的key buffer
 * @return void
 */
inline static void DmVertexGetCompareKeyBuf(const DmVertexT *vertex, uint8_t **keyBuf)
{
    DB_POINTER2(vertex, keyBuf);
    *keyBuf = vertex->compareKeyBuf;
}

/**
 * @brief 基于vertex获取indexKeyInfo
 * @param vertex 顶点句柄
 * @param indexId DmIndexKeyBufInfo数组下标
 * @param indexKeyInfo 出参，获取的indexKeyInfo
 * @return Status 成功或错误码
 */
SO_EXPORT_FOR_HAC_AND_TS Status DmVertexGetIndexKeyInfo(
    const DmVertexT *vertex, uint32_t indexId, DmIndexKeyBufInfoT **indexKeyInfo);

/**
 * @brief 基于vertex获取key buffer，key buffer内存由DM管理，调用者不可释放
 * @param vertex 顶点句柄
 * @param indexId DmIndexKeyBufInfo数组下标
 * @param keyBuf 出参，获取的key buffer
 * @param length 出参，获取的key buffer长度
 * @return Status 成功或错误码
 */
SO_EXPORT_FOR_TS Status DmGetKeyBufFromVertex(
    const DmVertexT *vertex, uint32_t indexId, uint8_t **keyBuf, uint32_t *length);

#ifdef FEATURE_GQL
/**
 * @brief 基于vertex获取key buffer，并将key buffer缓存至用户预分配的内存
 * @param vertex 顶点句柄
 * @param indexId DmIndexKeyBufInfo数组下标
 * @param keyBuf 出参，获取的key buffer
 * @param length 出参，获取的key buffer长度
 * @return Status 成功或错误码
 */
Status DmGetKeyBufFromVertex2ExistsBuf(const DmVertexT *vertex, uint32_t indexId, uint8_t *keyBuf, uint32_t *length);

/**
 * @brief 基于vertex获取key buffer，key buffer内存由DM管理，调用者不可释放
 * @param vertex 顶点句柄
 * @param indexKeyInfo DmIndexKeyBufInfo
 * @param keyBuf 出参，获取的key buffer
 * @param length 出参，获取的key buffer长度
 * @return Status 成功或错误码
 */
void DmGetKeyBufFromVertexWithIndexInfo(
    const DmVertexT *vertex, DmIndexKeyBufInfoT *indexKeyInfo, uint8_t **keyBuf, uint32_t *keyBufLen);
#endif

/**
 * @brief 基于vertex和indexKeyInfo获取key buffer
 * @param vertex 顶点句柄
 * @param indexKeyInfo vertex对应的indexkeyinfo
 * @param keyBuf 出参，由函数负责填充，keybuf的内存由调用者申请
 * @param length 出参，填充keybuf对应的长度
 * @return void
 */
void DmGetKeyBufFromVertexWithKeyInfo(
    const DmVertexT *vertex, const DmIndexKeyBufInfoT *indexKeyInfo, uint8_t *keyBuf, uint32_t *length);

/**
 * @brief 基于vertex获取主键key buffer
 * @param vertex 顶点句柄
 * @param keyBuf 出参，获取的key buffer
 * @param length 出参，获取的key buffer长度
 * @return void
 */
void DmGetPrimaryKeyBufFromVertex(const DmVertexT *vertex, uint8_t *keyBuf, uint32_t *length);

/**
 * @brief 判断两个属性前par->matchBytesNum字节是否完全匹配
 *
 * @param firstKeyBuf
 * @param secondKeyBuf
 * @param indexLabel
 * @param isPrefixMatch 出参
 * @param par
 * @return SO_EXPORT
 */
SO_EXPORT Status DmIndexKeyPrefixMatchCompare(uint8_t *firstKeyBuf, uint8_t *secondKeyBuf, DmVlIndexLabelT *indexLabel,
    bool *isPrefixMatch, DmPrefixMatchKeyCmpParaT *par);

/**
 * @brief 判断两个属性前par->matchBytesNum字节是否完全匹配
 *
 * @param firstKeyBuf
 * @param secondKeyBuf
 * @param indexLabel
 * @param isPrefixMatch 出参
 * @param par
 * @return SO_EXPORT
 */
SO_EXPORT Status DmIndexKeyNearMatchCompare(uint8_t *firstKeyBuf, uint8_t *secondKeyBuf, DmVlIndexLabelT *indexLabel,
    bool *isNearMatch, DmPrefixMatchKeyCmpParaT *par);

/*
 * key值比较函数（注：此函数为通用Key比较函数，具有普遍比较的语意）
 * 输入参数：firstKeyBuf与secondKeyBuf为需要比较的key值buffer
            indexLabel提供index元信息
 * 输出参数：cmpResult返回值有三种（-1、0、1），含义分别为：
 *          -1: 左key值小于右key值；
 *           0：左右key值相等；
 *           1：左key值大于右key值
 * 调用时需要注意传进的两个key值对应的schema相同，此函数不对key的schema进行校验.
 */
SO_EXPORT Status DmIndexKeyCompare(
    uint8_t *firstKeyBuf, uint8_t *secondKeyBuf, DmVlIndexLabelT *indexLabel, int8_t *cmpResult);

/*
 * 为基于ART的二级索引准备的，记录索引中带符号整数字段的信息
 * 输入参数：indexLabel提供index元信息
 * 输出参数：signedFlag已解析的索引key的带符号整数字段的信息
 * 注1：处理后的signedFlag（32位）是用bits信息从低位到高位存储带符号整数信息，举例如下
        若第1、4、7个字段为带符号整型，则输出signedFlag的二进制低8位表示是01001001
 */
SO_EXPORT uint32_t DmIndexKeyGetSignedFlag(DmVlIndexLabelT *indexLabel);

/*
 * key的int型子key解析函数，将字节序翻转（注：此函数为通用Key解析函数）
 * 输入参数：keyBuf待解析的索引key的buffer
            keyLen待解析的索引key的length
            indexLabel提供index元信息
 * 输出参数：revKeyBuf为字节调整完成的buffer，长度与入参keyLen相同
 * 注1：若indexLabel里面解析出原key没有长度>=uint16_t的无符号整型子key，则revKeyBuf全等于keyBuf
 * 注2：若keyBuf为空，则吐出的revKeyBuf也为空
 */
SO_EXPORT void DmReverseKeyForBigEndianInts(
    const uint8_t *keyBuf, uint32_t keyLen, DmVlIndexLabelT *indexLabel, uint8_t *revKeyBuf);

/*
 * key的signed int型子key解析函数，将ART中读出的在写入时从signed int转化来的unsigned int转回原来的signed int
 * （注：此函数为通用Key解析函数）
 * 输入参数：keyBuf待解析的索引key的buffer
            keyLen待解析的索引key的length
            indexLabel提供index元信息
 * 输出参数：keyBuf，原地更新
 */
SO_EXPORT Status DmTransferKeyFromUInt2intLocal(
    uint8_t *keyBuf, uint32_t keyLen, DmVlIndexLabelT *indexLabel, uint32_t signedFlag);

/*
 * key的int型子key解析函数
 * 输入参数：keyBuf待解析的索引key的buffer
            keyLen待解析的索引key的length
            indexLabel提供index元信息
 * 输出参数：revKeyBuf为字节调整完成的buffer，长度与入参keyLen相同
 * 注1：若indexLabel里面解析出原key没有有符号整型子key，则transKeyBuf全等于keyBuf
 * 注2：若keyBuf为空，则吐出的transKeyBuf也为空
 */
SO_EXPORT Status DmTransferKeyFromInt2Uint(
    uint8_t *keyBuf, uint32_t keyLen, DmVlIndexLabelT *indexLabel, uint8_t **transKeyBuf);

/*
 * get the possible maximum length of the keyBuf
 * input parameters: indexLabel
 * output parameters: NULL
 * return: Max length of the key buffer
 */
SO_EXPORT uint32_t DmGetMaxKeyBufLen(DmVlIndexLabelT *indexLabel);

SO_EXPORT_FOR_TS uint32_t DmGetMaxKeyBufLen4QE(DmVlIndexLabelT *indexLabel);

SO_EXPORT uint32_t DmGetMaxKeyBufLen4ListUnique(DmVlIndexLabelT *indexLabel, DmVertexLabelT *vertexLabel);

/**
 * @brief 判断当前key是否符合filter约束
 * @param keyBuf 入参，key buffer的指针
 * @param keyLen 入参，key buffer的长度
 * @param indexLabel 入参，点的label参数，用于解析key每个字段，且包含filter表达式
 * @param cmpRes 出参，判断结果，1表示符合filter约束，0表示不符合
 * @return 错误码
 */
SO_EXPORT Status DmPartialIndexFilterSatisfied(
    uint8_t *keyBuf, uint32_t keyLen, DmVlIndexLabelT *indexLabel, uint8_t *cmpRes);
/*
 * 调用时需要注意传进的两个key值对应的indexLable相同，此函数不对key的indexLable进行校验.
 * key值比较函数，只比较前面k个属性
 * 输入参数: key1和key2为需要比较的key
 *          key->buf为需要比较的key buf
 *          key->perfixPropeNum为key buf携带的前缀属性数
 *          k = min(key1->prefixPropeNum，key2->prefixPropeNum)
 *          indexLabel提供index元信息
 *          nullDisableBitMap提供key2不允许为null的bitmap, 如 0x5 代表第 0 和 2 个索引属性不允许为null
 * 输出参数: cmpResult返回值有四种, 含义分别为:
 *          DM_INDEX_KEY_LT: key1 < key2 (DM_SORT_ASC), key1 > key2 (DM_SORT_DESC);
 *          DM_INDEX_KEY_EQ: key1 = key2;
 *          DM_INDEX_KEY_GT: key1 > key2 (DM_SORT_ASC), key1 < key2 (DM_SORT_DESC);
 *          DM_INDEX_KEY_BUTT: key2 含有null值, 但实际不允许.
 */
SO_EXPORT_FOR_TS Status DmIndexKeyPrefixCompare(const DmIndexKeyPrefixT *key1, const DmIndexKeyPrefixT *key2,
    DmVlIndexLabelT *indexLabel, uint64_t nullDisableBitMap, DmIndexKeyCmpResultE *cmpResult);
#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif /* DM_DATA_INDEX_H */
