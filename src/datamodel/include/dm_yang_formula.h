/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: header file for dm_yang_formula_compiler.c
 * Author: zengzebin
 * Create: 2023-07-08
 */
#ifndef DM_YANG_FORMULA_H
#define DM_YANG_FORMULA_H

#include "db_mem_context.h"
#include "db_arena.h"
#include "dm_meta_basic.h"
#include "db_list.h"

#ifdef __cplusplus
extern "C" {
#endif

#define DM_MAX_OWNER_COUNT 2  // 用两个owner记录当前计算栈的前一个位置以及current位置
#define DM_MAX_STACK_DEPTH 8  // 函数或谓词栈嵌套最大深度

typedef enum {
    OPERAND = 0,  // 操作数
    OPERATOR = 1  // 操作符
} DmFormulaNodeTypeE;

typedef enum {
    COMPILE_STATE = 0,  // 在编译原始字符串时生成的node，中途计算时不能释放
    RUN_STATE = 1       // 在计算运行过程中生成的node，在本次计算结束后，需要释放
} DmFormulaNodeStateE;

typedef enum {
    ARITHMETIC_BEGIN,
    ARITHMETIC_ADDITION,
    ARITHMETIC_SUBTRACTION,
    ARITHMETIC_MULTIPLY,
    ARITHMETIC_DIV,
    ARITHMETIC_MOD,
    ARITHMETIC_END,

    LOGIC_BEGIN = ARITHMETIC_END,
    LOGIC_EQUAL = LOGIC_BEGIN,
    LOGIC_NOT_EQUAL,
    LOGIC_LESS_THAN,
    LOGIC_GREATER_THAN,
    LOGIC_LESS_EQUAL,
    LOGIC_GREATER_EQUAL,
    LOGIC_AND,
    LOGIC_OR,
    LOGIC_BELONG,
    LOGIC_LEFT_BRACKET,
    LOGIC_RIGHT_BRACKET,
    LOGIC_END,

    // Change the sequence or add a new type
    // Note that the function SetFuncOpTypeAndValueType
    FUNCTION_BEGIN = LOGIC_END,
    FUNCTION_COUNT = FUNCTION_BEGIN,
    FUNCTION_SUM,
    FUNCTION_NOT,
    FUNCTION_CURRENT,
    FUNCTION_CONTAINS,
    FUNCTION_STRING,
    FUNCTION_CONCAT,
    FUNCTION_SUBSTRING,
    FUNCTION_BOOLEAN,
    FUNCTION_NUMBER,
    FUNCTION_REMATCH,
    FUNCTION_STRINGLENGTH,
    FUNCTION_TRANSLATE,
    FUNCTION_STRATSWITH,
    FUNCTION_LAST,
    FUNCTION_POSITION,
    FUNCTION_NAME,
    FUNCTION_SUBSTRINGBEFORE,
    FUNCTION_SUBSTRINGAFTER,
    FUNCTION_NORMALIZESPACE,
    FUNCTION_TRUE,
    FUNCTION_FALSE,
    FUNCTION_LANG,
    FUNCTION_FLOOR,
    FUNCTION_DEREF,
    FUNCTION_DERIVEDFROM,
    FUNCTION_DERIVEDFROMORSELF,
    FUNCTION_ENUMVALUE,
    FUNCTION_BITISSET,
    FUNCTION_LOCALNAME,
    FUNCTION_CEILING,
    FUNCTION_ROUND,
    FUNCTION_END,

    XPATH_BEGIN = FUNCTION_END,
    XPATH_TOTAL = XPATH_BEGIN,
    XPATH_ROOT,
    XPATH_CURRENT,
    XPATH_CHILD,
    XPATH_CHILD_VERTEX,
    XPATH_CHILD_NODE,
    XPATH_CHILD_PROPERTY,
    XPATH_CHILD_PROPERTY_KEY,
    XPATH_PARENT,
    XPATH_PARENT_VERTEX,
    XPATH_PARENT_NODE,
    XPATH_PARENT_PROPERTY,
    XPATH_PREDICATE,
    XPATH_FETCH,
    XPATH_FETCH_PREDICATE,
    XPATH_END,
    OPERATION_BUTTON = XPATH_END
} DmOperatorTypeE;

typedef struct DmFetchOwner {
    uint32_t vertexLabelId;
    uint32_t propertyId;
    uint32_t relatedEdgeId;  // 记录vertexLabel对应边在元数据边数组中的下标，避免数据校验重复获取
    uint16_t uniqNodeId;
    bool isListVertexLabel;
    bool isInPk;  // 属性是不是在主键上
} DmFetchOwnerT;

typedef struct DmFormulaStack DmFormulaStackT;

typedef struct DmOperator {
    DmOperatorTypeE type;  // 操作数类型
    union {
        char *pathStr;             // 路径类型节点使用(XPATH_TOTAL ~ XPATH_FETCH_PREDICATE)，需要释放内存
        const char *constExprStr;  // 其他操作符使用，指向一个字符串常量
    };
    uint32_t argc;             // 记录参数数量
    DmFetchOwnerT fetchOwner;  // 路径类型节点的当前节点信息
    union {
        DmFormulaStackT *pathStack;  // 路径节点表示完整路径对应的栈结构
        DmFormulaStackT **argStackArr;  // 函数类型节点表示函数参数数组，谓词类型节点表示谓词数组
    };
} DmOperatorT;

typedef struct DmFormulaOwner {
    void *vertex;
    uint32_t namespaceId;
    uint32_t vertexLabelId;
    uint32_t propertyId;
    uint16_t uniqNodeId;
    bool isInPk;  // 属性是不是在主键上
} DmFormulaOwnerT;

typedef struct DmOpVariable {
    char *variableName;  // 变量标识，以$符号开头
} DmOpVariableT;

typedef enum DmFormulaLastStep {
    FORMULA_LAST_FETCH_MODE,  // 表示处理状态为获取模型
    FORMULA_LAST_FETCH_DATA,  // 表示处理状态为获取具体数据
} DmFormulaLastStepE;

typedef struct DmFormulaCalcState {
    bool isCalcPredicate;         // 表示当前状态是否为谓词校验
    bool isXPath;                 // 表示当前为 XPATH_TOTAL 的模型校验计算栈
    DmFormulaLastStepE lastStep;  // 表示当前处理的类型
} DmFormulaCalcStateT;

typedef enum DmOperandType {
    OPERAND_FORMULA_OWNER = 0,  // 表示当前数据为模型信息
    OPERAND_VALUE,              // 表示当前数据为DmValue
    OPERAND_VARIABLE,           // 表示当前数据为用户自定义变量
    OPERAND_NULL                // 表示当前数据为空
} DmOperandTypeE;

typedef enum DmFormulaStackType {
    COMPILE_STACK = 0,  // 表示当前栈为编译后的栈
    CALCULATE_STACK     // 表示当前栈为计算用的临时栈
} DmFormulaStackTypeE;

typedef struct DmOperand {
    DmOperandTypeE type;
    DmValueT dmValue;
    DmFormulaOwnerT owner;  // 表示获取模型信息时的值
    DmOpVariableT variable;
} DmOperandT;

// 新增 DmFormulaNode 字段请同步修改 DmFormulaCopyNode、DmFormulaFreeNode 逻辑
typedef struct DmFormulaNode {
    DmFormulaNodeTypeE type;
    DmFormulaNodeStateE state;
    union {
        DmOperatorT dmOperator;
        DmOperandT dmOperand;
    };
} DmFormulaNodeT;

typedef struct DmFormulaStackNode {
    DmFormulaNodeT *node;
    struct DmFormulaStackNode *prev;
    struct DmFormulaStackNode *next;
} DmFormulaStackNodeT;

struct DmFormulaStack {
    DmFormulaStackNodeT *head;
    DmFormulaStackNodeT *tail;
    DmFormulaStackNodeT *iter;
    DbArenaT *arena;
    DmFormulaStackTypeE type;
    DmFormulaCalcStateT calcState;
    uint32_t len;
    uint32_t funcNestDepth;                      // 记录当前函数嵌套的深度
    uint32_t predNestDepth;                      // 记录当前谓词嵌套的深度
    DmFormulaOwnerT owners[DM_MAX_OWNER_COUNT];  // 记录当前计算栈的模型上下文节点信息
                                                 // （下标：0）以及current上下文节点信息（下标：1）
};

/**
 * @description: 获取栈顶节点，不会将节点从栈中弹出
 * @param stack : formulaStack指针
 * @return 栈顶元素的 DmFormulaNodeT 节点
 */
inline static DmFormulaNodeT *DmFormulaStackGetTopNode(DmFormulaStackT *stack)
{
    DB_POINTER(stack);
    if (stack->len == 0) {
        return NULL;
    }
    return stack->tail->node;
}

/**
 * @description: 获取栈底节点，不会将节点从栈中弹出
 * @param stack : formulaStack指针
 * @return 栈底元素的 DmFormulaNodeT 节点
 */
inline static DmFormulaNodeT *DmFormulaStackGetBottomNode(DmFormulaStackT *stack)
{
    DB_POINTER(stack);
    if (stack->len == 0) {
        return NULL;
    }
    return stack->head->node;
}

/**
 * @description: 判断当前 formulaStack 是否表示路径
 * @param stack : formulaStack指针
 * @return formulaStack 是否表示路径的布尔值
 */
inline static bool DmFormulaStackIsPath(DmFormulaStackT *stack)
{
    if (stack->len != 1) {
        return false;
    }
    DmFormulaNodeT *topNode = DmFormulaStackGetTopNode(stack);
    if (topNode != NULL && topNode->type == OPERATOR && topNode->dmOperator.type == XPATH_TOTAL) {
        return true;
    }
    return false;
}

/**
 * @description: 创建空的 formulaStack
 * @param memCtx : 用于创建 formulaStack 的 memCtx
 * @param type : formulaStack 的类型
 * @param funcNestDepth : 当前表达式函数嵌套的深度
 * @param predNestDepth : 当前表达式谓词嵌套的深度
 * @param formulaStack : 指向需要被创建的 formulaStack
 * @return 状态码
 */
SO_EXPORT Status DmFormulaStackCreate(DbArenaT *arena, DmFormulaStackTypeE type, uint32_t funcNestDepth,
    uint32_t predNestDepth, DmFormulaStackT **formulaStack);

/**
 * @description: 获取 formulaStack 的栈顶元素
 * @param stack : formulaStack指针
 * @return 栈顶元素的 DmFormulaNodeT 节点
 */
SO_EXPORT DmFormulaNodeT *DmFormulaStackPop(DmFormulaStackT *stack);

/**
 * @description: 获取 formulaStack 的栈底元素
 * @param stack : formulaStack指针
 * @return 栈底元素的 DmFormulaNodeT 节点
 */
SO_EXPORT DmFormulaNodeT *DmFormulaStackPopHead(DmFormulaStackT *stack);

/**
 * @description: 往 formulaStack 的栈顶插入一个节点，node需要先用stack的memCtx创建好
 * @param stack : formulaStack 指针
 * @param node : 待插入的节点的指针
 * @return 状态码
 */
SO_EXPORT Status DmFormulaStackAppendNode(DmFormulaStackT *stack, DmFormulaNodeT *node);

/**
 * @description: 往 formulaStack 的栈底插入一个节点，node需要先用stack的memCtx创建好
 * @param stack : formulaStack 指针
 * @param node : 待插入的节点的指针
 * @return 状态码
 */
SO_EXPORT Status DmFormulaStackAppendHeadNode(DmFormulaStackT *stack, DmFormulaNodeT *node);

/**
 * @description: 往 formulaStack 的栈顶插入一个节点，插入formulaStack的 node 在内部创建，浅拷贝入参 node
 * @param stack : formulaStack 指针
 * @param node : 待插入的节点
 * @return 状态码
 */
SO_EXPORT Status DmFormulaStackAppendNodeWithMemCtx(DmFormulaStackT *stack, DmFormulaNodeT node);

/**
 * @description: 将中缀表达式转换成后缀表达式
 * @param inOrder : 已处理好的中缀表达式
 * @param postOrder : 已创建好并写好，但未写入内容的中缀表达式
 * @return 状态码
 */
SO_EXPORT Status DmFormulaInOrderToPostOrder(DmFormulaStackT *inOrder, DmFormulaStackT *postOrder);

/**
 * @description: 重置 formulaStack 的迭代器
 * @param stack : formulaStack 指针
 * @return void
 */
SO_EXPORT void DmFormulaStackResetIterator(DmFormulaStackT *stack);

/**
 * @description: 设置当前计算栈的起始位置信息
 * @param owners : 起始位置数组
 * @param ownerSize : owners 数组大小
 * @param formulaStack : formulaStack 指针
 * @return void
 */
SO_EXPORT void DmFormulaStackSetOwner(DmFormulaOwnerT *owners, uint32_t ownerSize, DmFormulaStackT *formulaStack);

/**
 * @description: 获取 formulaStack 迭代器的下一个节点
 * @param stack : formulaStack 指针
 * @return 迭代器的下一个 DmFormulaNodeT 节点
 */
SO_EXPORT DmFormulaNodeT *DmFormulaStackIterNext(DmFormulaStackT *stack);

/**
 * @description: 将字符串编译成后缀公式栈
 * @param str : 字符串指针
 * @param len : 字符串长度
 * @param postOrder : 函数内创建并编译好的 formulaStack 指针
 * @return 状态码
 */
SO_EXPORT Status DmFormulaToPostOrderStack(char *str, uint32_t len, DmFormulaStackT *postOrder);

/**
 * @description: 插入owner类型的 formulaStack 节点
 * @param stack : formulaStack 指针
 * @param owner : 待插入的 owner 指针，不会进行深拷贝
 * @return 状态码
 */
SO_EXPORT Status DmFormulaStackAppendOwnerNode(DmFormulaStackT *stack, const DmFormulaOwnerT *owner);

static inline bool FormulaNodeIsFunction(DmOperatorTypeE type)
{
    return (type >= FUNCTION_BEGIN && type < FUNCTION_END);
}

#ifdef __cplusplus
}
#endif

#endif  // DM_YANG_FORMULA_H
