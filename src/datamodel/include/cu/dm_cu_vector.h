/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: header file for vector data model.
 * Author: hujing lihainuo
 * Create: 2023-09-11
 */
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
#ifndef DM_CU_VECTOR_H
#define DM_CU_VECTOR_H

#include "adpt_types.h"
#include "db_bitmap.h"
#include "db_mem_context.h"
#include "dm_data_prop.h"
#include "dm_cu.h"
#include "se_temp_file.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct DmCuVector {
    DbDataTypeE dataType;  // 数据类型, 当前设计不保证支持变长类型
    uint32_t initRowCnt;   // 内存中可以存放的数据行数
    uint32_t totalRowCnt;  // vector总行数，包含外存数据
    uint32_t dataSize;     // 数据长度（字节数）
    void *data;            // 内存数据指针数组
    BufferFileT *file;     // 外存数据管理
    DbMemCtxT *memCtx;     // 内存上下文
} DmCuVectorT;

typedef struct DmCuInfo {
    bool hasIp;         // cu块是否是ip类型
    DbBitmapT *bitmap;  // 用于确认cu块哪些数据是ipv6
    DmCuT *cu;          // cu块信息
} DmCuInfoT;

/**
 * @brief create empty vector [注:cu vector涉及临时文件，是敏感操作，用完一定要释放]
 * @param[in] memCtx memory context to create vector
 * @param[in] dataType vector data type
 * @param[in] dataSize vector data size
 * @param[in] initRowCnt initial row count in memory, others will be stored in buffer file
 * @param[in,out] pVector pointer to store vector
 * @return Status Return code indicating success or failure.
 */
SO_EXPORT Status DmCuVectorCreate(
    DbMemCtxT *memCtx, DbDataTypeE dataType, uint32_t dataSize, uint32_t initRowCnt, DmCuVectorT **pVector);

/**
 * @brief destroy vector
 * @param[in] vector pointer to store vector
 */
SO_EXPORT void DmCuVectorDestroy(DmCuVectorT *vector);

/**
 * @brief append a row from buffer
 * @param[in] vector pointer to store vector
 * @param[in] buffer pointer to copy row from
 * @param[in] dataType buffer data type
 */
SO_EXPORT Status DmCuVectorAppendRow(DmCuVectorT *vector, void *buffer, DbDataTypeE dataType);

/**
 * @brief replace row from buffer
 * @param[in] vector pointer to store vector
 * @param[in] rowId row id in vector
 * @param[in] buffer pointer to copy data from
 * @param[in] dataType buffer data type
 */
SO_EXPORT Status DmCuVectorReplaceRow(DmCuVectorT *vector, uint32_t rowId, void *buffer, DbDataTypeE dataType);

/**
 * @brief add to row from addend in memory (particularly for acceleration)
 * @param[in] vector pointer to store vector
 * @param[in] rowId row id in vector
 * @param[in] addend pointer to copy data from
 */
inline static Status DmCuVectorAddToRowInMem(DmCuVectorT *vector, uint32_t rowId, int64_t addend)
{
    int64_t *dstPos = (int64_t *)(vector->data) + rowId;
    if (SECUREC_UNLIKELY((addend >= 0) ? ((LLONG_MAX - addend) < *dstPos) : ((LLONG_MIN - addend) > *dstPos))) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW,
            "Int64 add result overflow, addend: %" PRId64 ", dstPos: %" PRId64 ".", addend, *dstPos);
        return GMERR_FIELD_OVERFLOW;
    }
    *dstPos += addend;
    return GMERR_OK;
}

/**
 * @brief add to row from addend
 * @param[in] vector pointer to store vector
 * @param[in] rowId row id in vector
 * @param[in] addend pointer to copy data from
 */
SO_EXPORT Status DmCuVectorAddToRow(DmCuVectorT *vector, uint32_t rowId, int64_t addend);

/**
 * @brief compare row value and comp value, if comp value is smaller, do replacement.
 * @param[in] vector pointer to store vector
 * @param[in] rowId row id in vector
 * @param[in] comp value wait to be compared
 */
SO_EXPORT Status DmCuVectorSetMinToRow(DmCuVectorT *vector, uint32_t rowId, int64_t comp);

/**
 * @brief compare row value and comp value, if comp value is larger, do replacement.
 * @param[in] vector pointer to store vector
 * @param[in] rowId row id in vector
 * @param[in] comp value wait to be compared
 */
SO_EXPORT Status DmCuVectorSetMaxToRow(DmCuVectorT *vector, uint32_t rowId, int64_t comp);

/**
 * @brief set the first value to row.
 * @param[in] vector pointer to store vector
 * @param[in] rowId row id in vector
 * @param[in] comp value wait to be set
 */
SO_EXPORT Status DmCuVectorSetFirstToRow(DmCuVectorT *vector, uint32_t rowId, int64_t comp);

/**
 * @brief set the last value to row.
 * @param[in] vector pointer to store vector
 * @param[in] rowId row id in vector
 * @param[in] newVal value wait to be set
 */
SO_EXPORT Status DmCuVectorSetLastToRow(DmCuVectorT *vector, uint32_t rowId, int64_t newVal);

// set agg func to row for fixed type
SO_EXPORT Status DmCuVectorSetMinToRow4Str(DmCuVectorT *vector, uint32_t rowId, char *comp);
SO_EXPORT Status DmCuVectorSetMaxToRow4Str(DmCuVectorT *vector, uint32_t rowId, char *comp);
SO_EXPORT Status DmCuVectorSetFirstToRow4Str(DmCuVectorT *vector, uint32_t rowId, char *comp);
SO_EXPORT Status DmCuVectorSetLastToRow4Str(DmCuVectorT *vector, uint32_t rowId, char *comp);

// ut only
Status ReadRowFromFile(BufferFileT *file, size_t offset, void *dstData, uint32_t dstSize);
Status WriteRowToFile(DbMemCtxT *memCtx, BufferFileT **file, size_t offset, void *srcData, uint32_t srcSize);

/**
 * @brief clear vector
 * @param[in] vector pointer to store vector
 */
SO_EXPORT inline void DmCuVectorClear(DmCuVectorT *vector)
{
    DB_POINTER(vector);
    vector->totalRowCnt = 0;
}

/**
 * @brief get a row from vector
 * @param[in] vector pointer to store vector
 * @param[in] rowId row id in vector
 * @param[in] buffer pointer to copy data from
 * @param[in] dataType buffer data type
 * @return Status Return code indicating success or failure.
 */
SO_EXPORT Status DmCuVectorGetRow(DmCuVectorT *vector, uint32_t rowId, void *buffer, DbDataTypeE dataType);

/**
 * @brief get a row directly from vector when rowId is smaller than initRowCnt
 * @param[in] vector pointer to store vector
 * @param[in] rowId row id in vector
 * @param[in] buffer pointer to get data from. When rowId is smaller than initRowCnt, buffer can be NULL.
 *  Otherwise enough space should be allocated.
 * @return Status Return code indicating success or failure.
 */
SO_EXPORT Status DmCuVectorGetRowAddr(DmCuVectorT *vector, uint32_t rowId, void **buffer);

/**
 * @brief expand rowCnt rows to vector
 * @param[in] vector pointer to store vector
 * @param[in] rowCnt number of expand rows
 * @param[in] value value for each row
 * @return Status Return code indicating success or failure.
 */
SO_EXPORT Status DmCuVectorAppendRowsWithValue(DmCuVectorT *vector, uint32_t rowCnt, int64_t value);

/**
 * @brief expand first function rowCnt rows to vector
 * @param[in] vector pointer to store vector
 * @param[in] rowCnt number of expand rows
 * @param[in] cu column storage block
 * @param[in] valuePos subscript array of each data group
 * @return Status Return code indicating success or failure.
 */
SO_EXPORT Status DmCuVectorAppendFirstValue(DmCuVectorT *vector, uint32_t rowCnt, DmCuT *cu, int32_t *valuePos);

#ifdef __cplusplus
}
#endif
#endif
#endif
