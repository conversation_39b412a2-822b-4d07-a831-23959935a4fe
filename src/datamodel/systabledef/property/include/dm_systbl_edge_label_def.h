/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: header file for edge label system table definition
 * Author:
 * Create: 2024-4
 */
#ifndef DM_SYSTBL_EDGE_LABEL_DEF_H
#define DM_SYSTBL_EDGE_LABEL_DEF_H

#ifdef __cplusplus
extern "C" {
#endif

/*
 * edge label system table definition
 */
#define STB_EL_PROP_NUM 19
#define STB_EL_CONSTRAINT_PROP_NUM 4

#define STB_EL_ID "EL_ID"
#define STB_EL_DB_ID "DB_ID"
#define STB_EL_NSP_ID "NSP_ID"
#define STB_EL_NAME "EL_NAME"  // 字段长度约束是MAX_TABLE_NAME_LEN
#define STB_EL_VERSION "EL_VERSION"
#define STB_EL_PERSISTENT "EL_PERSISTENT"
#define STB_EL_LIGHT_EDGE "EL_LIGHT_EDGE"
#define STB_EL_MAX_EDGE_NUM "EL_MAX_EDGE_NUM"
#define STB_EL_CC_TYPE "EL_CC_TYPE"
#define STB_EL_PAGE_ID "EL_PAGE_ID"
#define STB_EL_SOURCE_VERTEX_NAME "EL_SOURCE_VERTEX_NAME"
#define STB_EL_DEST_VERTEX_NAME "EL_DEST_VERTEX_NAME"
#define STB_EL_SOURCE_UNIQUE_NODE_ID "EL_SOURCE_UNIQUE_NODE_ID"
#define STB_EL_SOURCE_NODE_PATH "EL_SOURCE_NODE_PATH"
#define STB_EL_DEST_NODE_PATH "EL_DEST_NODE_PATH"
#define STB_EL_SOURCE_VL_ID "EL_SOURCE_VL_ID"
#define STB_EL_DEST_VL_ID "EL_DEST_VL_ID"  // 字段长度约束是MAX_TABLE_NAME_LEN
#define STB_EL_CREATOR "EL_CREATOR"

// Constraint
#define STB_EL_CONSTRAINT "EL_CONSTRAINT"
#define STB_EL_OPERATOR "EL_OPERATOR"
#define STB_EL_SOURCE_PROPE_NAME "EL_SOURCE_PROPE_NAME"
#define STB_EL_DEST_PROPE_NAME "EL_DEST_PROPE_NAME"
#define STB_EL_SOURCE_PROPEID "EL_SOURCE_PROPEID"
#define STB_EL_DEST_PROPEID "EL_DEST_PROPEID"

#define STB_EL_JSON                                                                                                   \
    "{ \
    \"type\":\"record\", \
    \"name\":\"GM_SYS_EL\", \
    \"fields\":[ \
        {\"name\":\"" STB_EL_ID "\", \"type\":\"uint32\", \"nullable\":false},\
        {\"name\":\"" STB_EL_DB_ID "\", \"type\":\"uint32\", \"nullable\":false},\
        {\"name\":\"" STB_EL_NSP_ID "\", \"type\":\"uint32\", \"nullable\":false},\
        {\"name\":\"" STB_EL_NAME "\", \"type\":\"string\", \"size\":" MAX_TABLE_NAME_LEN_STR ", \"nullable\":false},\
        {\"name\":\"" STB_EL_VERSION "\", \"type\":\"uint32\", \"nullable\":false},\
        {\"name\":\"" STB_EL_MAX_EDGE_NUM "\", \"type\":\"uint64\", \"nullable\":false},\
        {\"name\":\"" STB_EL_CC_TYPE "\", \"type\":\"uint32\", \"nullable\":false},\
        {\"name\":\"" STB_EL_PAGE_ID "\", \"type\":\"uint64\", \"nullable\":false},\
        {\"name\":\"" STB_EL_PERSISTENT "\", \"type\":\"boolean\", \"nullable\":false},\
        {\"name\":\"" STB_EL_LIGHT_EDGE "\", \"type\":\"boolean\", \"nullable\":false},\
        {\"name\":\"" STB_EL_SOURCE_VL_ID "\", \"type\":\"uint32\", \"nullable\":false},\
        {\"name\":\"" STB_EL_DEST_VL_ID "\", \"type\":\"uint32\", \"nullable\":false},\
        {\"name\":\"" STB_EL_SOURCE_UNIQUE_NODE_ID "\", \"type\":\"uint16\", \"nullable\":false},\
        {\"name\":\"" STB_EL_SOURCE_VERTEX_NAME "\", \"type\":\"string\", \"size\":" MAX_TABLE_NAME_LEN_STR "},\
        {\"name\":\"" STB_EL_DEST_VERTEX_NAME "\", \"type\":\"string\", \"size\":" MAX_TABLE_NAME_LEN_STR "},\
        {\"name\":\"" STB_EL_SOURCE_NODE_PATH "\", \"type\":\"string\", \"size\":" MAX_TABLE_NAME_LEN_STR             \
    ", \"nullable\":true},\
        {\"name\":\"" STB_EL_DEST_NODE_PATH "\", \"type\":\"string\", \"size\":" MAX_TABLE_NAME_LEN_STR               \
    ", \"nullable\":true},\
        {\"name\":\"" STB_EL_CREATOR "\", \"type\":\"string\", \"size\":" MAX_TABLE_NAME_LEN_STR                      \
    ", \"nullable\":true},\
        {\"name\":\"" STB_EL_OPERATOR "\", \"type\":\"uint32\", \"nullable\":false},\
        {\"name\": \"" STB_EL_CONSTRAINT "\",\
            \"type\": \"record\",\
            \"vector\": true,\
            \"fields\": [\
                { \"name\": \"" STB_EL_SOURCE_PROPEID "\", \"type\": \"uint32\", \"nullable\": false},\
                { \"name\": \"" STB_EL_SOURCE_PROPE_NAME "\", \"type\": \"string\", \"size\":" DM_MAX_NAME_LENGTH_STR \
    ",  \"nullable\": true},\
                { \"name\": \"" STB_EL_DEST_PROPEID "\", \"type\": \"uint32\", \"nullable\": false},\
                { \"name\": \"" STB_EL_DEST_PROPE_NAME "\", \"type\": \"string\", \"size\":" DM_MAX_NAME_LENGTH_STR   \
    ",  \"nullable\": true}\
            ]\
        }\
    ],\
    \"keys\": [\
        {\
            \"node\":\"GM_SYS_EL\",\
            \"name\":\"GM_SYS_EL_PK\",\
            \"fields\":[\"" STB_EL_ID "\"]," STB_PK_INDEX_KEY_INFO "}\
    ]\
}"

#ifdef __cplusplus
}
#endif
#endif  // DM_SYSTBL_EDGE_LABEL_DEF_H
