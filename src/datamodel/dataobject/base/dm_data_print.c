/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: implementation of print value of type
 * Author: wangjie
 * Create: 2022-06-22
 */

#include <ctype.h>
#include <arpa/inet.h>
#include "adpt_string.h"
#ifndef NDEBUG
#include "db_utils.h"
#endif  // NDEBUG
#include "dm_data_print_inner.h"
#include "dm_log.h"
#define DECIMAL_FIELD_TRANS 10  // 在函数strtol中使用，表示10进制
#define MAX_TS_BLOB_LENGTH 65535

uint32_t GetCharValueLen(char value)
{
    if (value <= '~' && value >= ' ') {
        return (uint32_t)sizeof(char);
    } else {
        return GetSignedValueLen((int64_t)value);
    }
}

uint32_t GetFloatValueLen(float value)
{
    char str[DM_PRINT_MAX_FLOAT_LEN];
    // 十进制float类型最大占38个数位，此处宏定义为50，数组能容纳输出的float值
    int32_t printLen = sprintf_s(str, DM_PRINT_MAX_FLOAT_LEN, DM_PRINT_FLOAT_FORMAT, value);
    DB_ASSERT(printLen >= 0);
    return (uint32_t)printLen;
}

uint32_t GetDoubleValueLen(double value)
{
    char str[DM_PRINT_MAX_DOUBLE_LEN];
    // 十进制double类型最大占308个数位，此处宏定义为320，数组能容纳输出的double值
    int32_t printLen = sprintf_s(str, DM_PRINT_MAX_DOUBLE_LEN, DM_PRINT_DOUBLE_FORMAT, value);
    DB_ASSERT(printLen >= 0);
    return (uint32_t)printLen;
}

uint32_t GetSignedValueLen(int64_t value)
{
    char str[DM_PRINT_MAX_INTEGER_LEN];
    int32_t printLen = sprintf_s(str, DM_PRINT_MAX_INTEGER_LEN, "%" PRId64 "", value);
    DB_ASSERT(printLen >= 0);
    return (uint32_t)printLen;
}

// flag 为0，计算普通无符号数打印长度，flag为1，计算十六进制addr型打印长度
uint32_t GetUnsignedValueLen(uint64_t value, uint32_t flag)
{
    char str[DM_PRINT_MAX_INTEGER_LEN];
    int32_t printLen = 0;
    if (flag == 0) {
        printLen = sprintf_s(str, DM_PRINT_MAX_INTEGER_LEN, "%" PRIu64 "", value);
    } else if (flag == 1) {
        printLen = sprintf_s(str, DM_PRINT_MAX_INTEGER_LEN, "%#x", (uint32_t)value);
    }
    DB_ASSERT(printLen >= 0);
    return (uint32_t)printLen;
}

void PrintCharValueToBuf(char value, char **strCursor)
{
    if (value <= '~' && value >= ' ') {
        **strCursor = value;
        *strCursor += (uint32_t)sizeof(char);
    } else {
        int32_t printLen = sprintf_s(*strCursor, DM_PRINT_MAX_INTEGER_LEN, "%" PRId64 "", (int64_t)value);
        if (printLen < 0) {
            **strCursor = ' ';
            printLen = (int32_t)sizeof(char);
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "sprintf_s.");
        }
        *strCursor += printLen;
    }
}

void PrintFloatToBuf(float value, char **strCursor)
{
    int32_t printLen = sprintf_s(*strCursor, DM_PRINT_MAX_FLOAT_LEN, DM_PRINT_FLOAT_FORMAT, value);
    if (printLen < 0) {
        **strCursor = ' ';
        printLen = (int32_t)sizeof(char);
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "sprintf_s.");
    }
    *strCursor += printLen;
}

void PrintDoubleToBuf(double value, char **strCursor)
{
    int32_t printLen = sprintf_s(*strCursor, DM_PRINT_MAX_DOUBLE_LEN, DM_PRINT_DOUBLE_FORMAT, value);
    if (printLen < 0) {
        **strCursor = ' ';
        printLen = (int32_t)sizeof(char);
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "sprintf_s.");
    }
    *strCursor += printLen;
}

void PrintSignedValueToBuf(int64_t value, char **strCursor)
{
    int32_t printLen = sprintf_s(*strCursor, DM_PRINT_MAX_INTEGER_LEN, "%" PRId64 "", value);
    if (printLen < 0) {
        **strCursor = ' ';
        printLen = (int32_t)sizeof(char);
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "sprintf_s.");
    }
    *strCursor += printLen;
}

void PrintUnsignedValueToBuf(uint64_t value, uint32_t flag, char **strCursor)
{
    int32_t printLen = 0;
    if (flag == 0) {
        printLen = sprintf_s(*strCursor, DM_PRINT_MAX_INTEGER_LEN, "%" PRIu64 "", value);
    } else if (flag == 1) {
        printLen = sprintf_s(*strCursor, DM_PRINT_MAX_INTEGER_LEN, "%#x", (uint32_t)value);
    }
    if (printLen < 0) {
        **strCursor = ' ';
        printLen = (int32_t)sizeof(char);
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "sprintf_s.");
    }
    *strCursor += printLen;
}

void PrintStringToBuf(const char *str, char **strCursor)
{
    int32_t printLen;
    if (str != NULL) {
        printLen = sprintf_s(*strCursor, strlen(str) + 1, "%s", str);
        if (printLen < 0) {
            *strCursor[0] = ' ';
            printLen = 1;
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "sprintf_s.");
        }
        *strCursor += printLen;
    }
}

void PrintStringToBufWithLen(const char *str, uint32_t length, char **strCursor)
{
    int32_t printLen;
    if (str != NULL) {
        printLen = sprintf_s(*strCursor, length, "%s", str);
        if (printLen < 0) {
            *strCursor[0] = ' ';
            printLen = 1;
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "sprintf_s.");
        }
        *strCursor += printLen;
    }
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
void PrintBlobToBufWithLen(const char *str, uint32_t length, char **strCursor)
{
    uint32_t printLen;
    if (str != NULL) {
        errno_t err = memcpy_s(*strCursor, MAX_TS_BLOB_LENGTH, str, length);
        printLen = length;
        if (err != EOK) {
            *strCursor[0] = ' ';
            printLen = 1;
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "sprintf_s.");
        }
        for (uint32_t i = 0; i < printLen; i++) {
            char *ptr = *strCursor + i;
            if (*ptr == '\0') {
                *ptr = ' ';
            }
        }
        *strCursor += printLen;
    }
}

void FlatPrintFloatToBuf(float floatValue, char **strCursor)
{
    int32_t printLen = snprintf_truncated_s(*strCursor, FLAT_FLOAT_LEN + 1, FLAT_FLOAT_FMT, floatValue);
    if (printLen < 0) {
        *strCursor[0] = ' ';
        printLen = 1;
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "sprintf_s.");
    }
    *strCursor += printLen;
}

void FlatPrintDoubleToBuf(double doubleValue, char **strCursor)
{
    int32_t printLen = snprintf_truncated_s(*strCursor, FLAT_FLOAT_LEN + 1, FLAT_FLOAT_FMT, doubleValue);
    if (printLen < 0) {
        *strCursor[0] = ' ';
        printLen = 1;
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "sprintf_s.");
    }
    *strCursor += printLen;
}

void FlatPrintCharValueToBuf(char value, char **strCursor)
{
    int32_t printLen =
        snprintf_truncated_s(*strCursor, FLAT_INT32_LEN + 1, "%" FLAT_INT32_FMT "" PRId64 "", (int64_t)value);
    if (printLen < 0) {
        *strCursor[0] = ' ';
        printLen = 1;
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "sprintf_s.");
    }
    *strCursor += printLen;
}

void FlatPrintSignedValueToBuf(int64_t value, char **strCursor)
{
    int32_t printLen = snprintf_truncated_s(*strCursor, FLAT_INT32_LEN + 1, "%" FLAT_INT32_FMT "" PRId64 "", value);
    if (printLen < 0) {
        *strCursor[0] = ' ';
        printLen = 1;
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "sprintf_s.");
    }
    *strCursor += printLen;
}

void FlatPrintUnsignedValueToBuf(uint64_t value, char **strCursor)
{
    int32_t printLen = snprintf_truncated_s(*strCursor, FLAT_INT32_LEN + 1, "%" FLAT_INT32_FMT "" PRIu64 "", value);
    if (printLen < 0) {
        *strCursor[0] = ' ';
        printLen = 1;
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "sprintf_s.");
    }
    *strCursor += printLen;
}

void FlatPrintStringToBuf(const char *str, char **strCursor, uint32_t strFmtLen)
{
    int32_t printLen;
    if (str == NULL) {
        // 此处长度可控，可以不校验返回值
        (void)memset_s(*strCursor, strFmtLen, ' ', strFmtLen);
        *strCursor += strFmtLen;
        return;
    }

    char *stringFmt = (char *)DB_MALLOC(FLAT_STRING_MAX_LEN);
    if (SECUREC_UNLIKELY(stringFmt == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Flat print string.");
        return;
    }
    (void)memset_s(stringFmt, FLAT_STRING_MAX_LEN, 0x00, FLAT_STRING_MAX_LEN);
    printLen = sprintf_s(stringFmt, FLAT_STRING_MAX_LEN, "%s%" PRIu32 "%s", "%-", strFmtLen, "s");
    DB_ASSERT(printLen >= 0);
    printLen = snprintf_truncated_s(*strCursor, strFmtLen + 1, stringFmt, str);
    DB_FREE((void *)stringFmt);
    if (printLen < 0) {
        **strCursor = ' ';
        printLen = (int32_t)sizeof(char);
    }

    *strCursor += printLen;
}

void PrintStringToBufV3Adpt(const char *str, char **strCursor, uint32_t strFmtLen, const char *fmt)
{
    int32_t printLen = snprintf_truncated_s(*strCursor, strFmtLen, fmt, str);
    if (SECUREC_UNLIKELY(printLen < 0)) {
        *strCursor[0] = ' ';
        printLen = 1;
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "sprintf_s.");
    }
    *strCursor += printLen;
}

void FlatPrintByteFixedTypeToBuf(const uint8_t *str, uint32_t propeLen, uint8_t **strCursor)
{
    if (str == NULL) {
        return;
    }
    uint32_t length = propeLen;
    // 如果结尾为'\0'则减1，以避免将'\0'拷贝到strCursor中
    if (str[length - 1] == '\0') {
        length--;
    }
    char printStr[FLAT_STRING_LEN] = {0};
    if (length > FLAT_STRING_LEN) {
        errno_t err = memcpy_s(printStr, FLAT_STRING_LEN, str, FLAT_STRING_LEN);
        DB_ASSERT(err == EOK);
    } else {
        errno_t err = memcpy_s(printStr, FLAT_STRING_LEN, str, length);
        DB_ASSERT(err == EOK);
    }
    errno_t errCpy = memcpy_s(*strCursor, FLAT_STRING_LEN, printStr, FLAT_STRING_LEN);
    DB_ASSERT(errCpy == EOK);
    *strCursor += FLAT_STRING_LEN;
}

void PrintBitmapValueToBuf(const DmValueT *propertyValue, char **strCursor)
{
    uint32_t printLen = GetBitmapValuePrintLen(propertyValue);
    uint32_t endPos = propertyValue->value.endPos;
    uint32_t j = 0;
    char *str = *strCursor;
    for (uint32_t i = 0; i <= endPos; i++) {
        if (GetBitmapBit((uint8_t *)propertyValue->value.strAddr, i)) {
            str[j++] = '0';
        } else {
            str[j++] = '1';
        }
        if ((i + 1) % DM_BYTE_BINARY_PRINT_UNIT == 0 && i != endPos) {
            str[j++] = ' ';
        }
    }
    *strCursor += printLen;
}

#ifdef FEATURE_SQL
// 打印格式：[d1, d2, ..., dm]
void PrintFloatVectorValueToBuf(const DmValueT *propertyValue, char **strCursor)
{
    uint32_t dim = propertyValue->value.length / sizeof(float);
    float *vector = (float *)propertyValue->value.strAddr;
    char *str = *strCursor;
    PrintCharValueToBuf('[', &str);
    for (uint32_t i = 0; i < dim; i++) {
        PrintFloatToBuf(vector[i], &str);
        if (i < dim - 1) {
            PrintCharValueToBuf(',', &str);
            PrintCharValueToBuf(' ', &str);
        }
    }
    PrintCharValueToBuf(']', &str);
    *strCursor += (uint32_t)(str - *strCursor);
}

void PrintFloat16VectorValueToBuf(const DmValueT *propertyValue, char **strCursor)
{
    uint32_t dim = propertyValue->value.length / sizeof(float16);
    float16 *vector = (float16 *)propertyValue->value.strAddr;
    char *str = *strCursor;
    PrintCharValueToBuf('[', &str);
    for (uint32_t i = 0; i < dim; i++) {
        PrintFloatToBuf(DbFloat16ToFloat(vector[i]), &str);
        if (i < dim - 1) {
            PrintCharValueToBuf(',', &str);
            PrintCharValueToBuf(' ', &str);
        }
    }
    PrintCharValueToBuf(']', &str);
    *strCursor += (uint32_t)(str - *strCursor);
}
#endif  // FEATURE_SQL

static Status DmIpv4ToStr(uint32_t ipv4Addr, char *dst, uint32_t dstLen)
{
    if (dst == NULL || dstLen < DB_IPV4_STR_LEN) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
            "Dest string is null or len less than DB_IPV4_STR_LEN, desLen is %" PRIu32 ".", dstLen);
        return GMERR_DATA_EXCEPTION;
    }
    const uint32_t offset8 = 8;
    const uint32_t offset16 = 16;
    const uint32_t offset24 = 24;
    uint32_t byte0 = ipv4Addr & DB_INVALID_ID8;
    uint32_t byte1 = (ipv4Addr >> offset8) & DB_INVALID_ID8;
    uint32_t byte2 = (ipv4Addr >> offset16) & DB_INVALID_ID8;
    uint32_t byte3 = (ipv4Addr >> offset24) & DB_INVALID_ID8;
    int32_t ret = snprintf_s(
        dst, dstLen, dstLen - 1, "%" PRIu32 ".%" PRIu32 ".%" PRIu32 ".%" PRIu32 "", byte3, byte2, byte1, byte0);
    if (ret < 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Cast ipv4 to string.");
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

Status DmIpv4Print(const char *src, uint32_t srcLen, char *dst, uint32_t dstLen, bool isKV)
{
    if (src == NULL || dst == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (srcLen != (uint32_t)sizeof(uint32_t)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Src string convert to ipv4.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    Status ret = GMERR_OK;
    if (isKV) {
        ret = DmIpv4ToStr(*(const uint32_t *)(const void *)src, dst, dstLen);
    } else {
        char *endptr = NULL;
        uint32_t val = (uint32_t)strtol(src, &endptr, DECIMAL_FIELD_TRANS);
        ret = DmIpv4ToStr(val, dst, dstLen);
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Convert %s to ipv4.", src);
        return ret;
    }
    return GMERR_OK;
}

Status DmIpv6Print(const char *src, uint32_t srcLen, char *dst, uint32_t dstLen)
{
    if (src == NULL || dst == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    char tmp[16] = {0};                              // inet_ntop接口src参数需为16字节
    uint32_t copyLen = (srcLen > 16) ? 16 : srcLen;  // src长度最多为16字节，如溢出则截断（传入srcLen不合ipv6标准）
    errno_t err = memcpy_s(tmp, copyLen, src, copyLen);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Copy srcbuf to tmpbuf, len:%" PRIu32 ".", copyLen);
        return GMERR_OUT_OF_MEMORY;
    }
    if (inet_ntop(AF_INET6, tmp, dst, dstLen) == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Convert %s to ipv6.", src);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status DmMacPrint(const char *src, uint32_t srcLen, char *dst, uint32_t dstLen)
{
    if (src == NULL || dst == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (srcLen != DM_MAC_BYTES) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Byte string len must be %" PRId32 ".", DM_MAC_BYTES);
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t offset = 0;
    for (uint32_t i = 0; i < DM_MAC_BYTES; i++) {
        int32_t ret = snprintf_s(dst + offset, dstLen - offset, (dstLen - offset) - 1, "%02x%s", (uint8_t)src[i],
            (i == DM_MAC_BYTES - 1) ? "" : "-");
        if (ret < 0) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Convert %s to MAC address.", src);
            return GMERR_FIELD_OVERFLOW;
        }
        offset += DM_MAC_ITEM_LEN;
    }
    return GMERR_OK;
}

Status DmTimePrint(const char *src, uint32_t srcLen, char *dst, uint16_t dstLen)
{
    if (src == NULL || dst == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (srcLen != (uint32_t)sizeof(int64_t)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Src string Convert to time.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    char *endptr = NULL;
    int64_t val = strtoll(src, &endptr, DECIMAL_FIELD_TRANS);
    CmTimeDescT desc;
    DbConvertTimestampToDesc(val, &desc);
    Status ret = DbTimeToStr(&desc, dst, dstLen, DEFAULT_TIME_FORMAT);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Convert object to format time string.");
        return ret;
    }
    return GMERR_OK;
}

Status DmStringPrint(const char *src, uint32_t srcLen, char *dst, uint32_t dstLen)
{
    if (src == NULL || dst == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t j = 0;
    for (uint32_t i = 0; i < srcLen; i++) {
        if (isprint(src[i])) {
            dst[j] = src[i];
            j++;
        } else {
            int32_t ret = snprintf_s(dst + j, dstLen - j, (dstLen - j) - 1, "\\0%02d", src[i]);
            if (ret < 0) {
                DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Copy unvisable char: %" PRId32 ".", ret);
                return GMERR_FIELD_OVERFLOW;
            }
            j += UN_VISABLE_LEN;
        }
    }
    dst[j] = '\0';
    return GMERR_OK;
}

Status DmStringTruncatePrint(const char *src, uint32_t srcLen, char *dst, uint32_t dstLen)
{
    if (src == NULL || dst == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE, "dest or src null.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t j = 0;
    for (uint32_t i = 0; i < srcLen; i++) {
        if (isprint(src[i])) {
            dst[j] = src[i];
            j++;
        } else {
            break;
        }
    }
    return GMERR_OK;
}

Status DmHexPrint(const char *src, uint32_t srcLen, char *dst, uint32_t dstLen, DbDataTypeE type)
{
    if (src == NULL || dst == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    char *endptr = NULL;
    uint32_t offset = DM_COMMON_HEX_PREFIX_LEN;
    dst[0] = '0';
    dst[1] = 'x';
    switch (type) {
        case DB_DATATYPE_INT32:
            if (sprintf_s(&dst[DM_COMMON_HEX_PREFIX_LEN], dstLen - DM_COMMON_HEX_PREFIX_LEN, "%08x",
                    (int32_t)strtol(src, &endptr, DECIMAL_FIELD_TRANS)) < 0) {
                goto EXIT;
            }
            break;
        case DB_DATATYPE_UINT32:
            if (sprintf_s(&dst[DM_COMMON_HEX_PREFIX_LEN], dstLen - DM_COMMON_HEX_PREFIX_LEN, "%08x",
                    (uint32_t)strtol(src, &endptr, DECIMAL_FIELD_TRANS)) < 0) {
                goto EXIT;
            }
            break;
        case DB_DATATYPE_INT64:
            if (sprintf_s(&dst[DM_COMMON_HEX_PREFIX_LEN], dstLen - DM_COMMON_HEX_PREFIX_LEN, "%016llx",
                    (int64_t)strtoll(src, &endptr, DECIMAL_FIELD_TRANS)) < 0) {
                goto EXIT;
            }
            break;
        case DB_DATATYPE_UINT64:
        case DB_DATATYPE_TIME:
            if (sprintf_s(&dst[DM_COMMON_HEX_PREFIX_LEN], dstLen - DM_COMMON_HEX_PREFIX_LEN, "%016llx",
                    (uint64_t)strtoll(src, &endptr, DECIMAL_FIELD_TRANS)) < 0) {
                goto EXIT;
            }
            break;
        default:
            for (uint32_t i = 0; i < srcLen; i++) {
                if (snprintf_s(dst + offset, dstLen - offset, (dstLen - offset) - 1, "%02x", (uint8_t)src[i]) < 0) {
                    goto EXIT;
                }
                offset += DM_COMMON_HEX_PREFIX_LEN;
            }
    }
    return GMERR_OK;
EXIT:
    DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Convert %s to hex, type:%" PRIu32 "", src, type);
    return GMERR_FIELD_OVERFLOW;
}

// 为了int型使用typeLen
Status DmPrintFormat(const char *src, DmPrintLenT printLen, char *dst, uint32_t dstLen, DmPrintFmtE printFmt, bool isKV,
    DbDataTypeE type)
{
    DB_POINTER2(src, dst);
    Status ret = GMERR_OK;
    switch (printFmt) {
        case IPV4_FORMAT:
            ret = DmIpv4Print(src, printLen.typeLen, dst, dstLen, isKV);
            break;
        case IPV6_FORMAT:
            ret = DmIpv6Print(src, printLen.realLen, dst, dstLen);
            break;
        case MAC_FORMAT:
            ret = DmMacPrint(src, printLen.realLen, dst, dstLen);
            break;
        case STRING_FORMAT:
            ret = DmStringPrint(src, printLen.realLen, dst, dstLen);
            break;
        case TIME_FORMAT:
            ret = DmTimePrint(src, printLen.typeLen, dst, (uint16_t)dstLen);
            break;
        case HEX_FORMAT:
            ret = DmHexPrint(src, printLen.realLen, dst, dstLen, type);
            break;
        case STRING_TRUNCATE_FORMAT:
            ret = DmStringTruncatePrint(src, printLen.realLen, dst, dstLen);
            break;
        default:
            ret = GMERR_INVALID_PARAMETER_VALUE;
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Print format input.");
            break;
    }
    return ret;
}
#endif /* FEATURE_SIMPLEREL */

/*
 * 打印定长属性，按十六进制打印。一字节需要两位char表示。打印以"0x"开头。
 * 函数DmBytesToHex末尾会加'\0'，申请长度时需要注意
 * 函数DmBytesToHex结束后需要把'\0'覆盖，防止截断
 */
void PrintByteFixedTypeToBuf(const uint8_t *str, uint32_t length, uint8_t **strCursor, uint32_t maxLen)
{
    uint32_t strLen = DmBytesToHex(str, length, true, ((char *)*strCursor), maxLen);  // 末尾会加'\0'
    *strCursor += strLen;
}

uint32_t DmBytesToHex(const uint8_t *bytes, uint32_t byteSize, bool hasPrefix, char *str, uint32_t bufLen)
{
    DB_POINTER2(bytes, str);
    char numToHexMap[DM_NUM_TO_HEX_MAP_LEN] = {
        '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    uint32_t pos = 0;
    uint32_t length = 0;  // not include '\0'
    char *buf = str;
    if (hasPrefix) {
        if (bufLen <= DM_HEX_MIN_LEN) {
            buf[0] = '\0';
            return length;
        }
        buf[0] = '0';
        buf[1] = 'x';
        pos = DM_COMMON_HEX_PREFIX_LEN;
        length = DM_COMMON_HEX_PREFIX_LEN;
    } else {
        pos = 0;
    }

    for (uint32_t i = 0; i < byteSize; i++) {
        buf[pos++] = numToHexMap[(bytes[i] & 0xF0) >> DM_HEX_MIN_LEN];
        length++;
        if (pos >= bufLen - 1) {
            break;
        }
        buf[pos++] = numToHexMap[bytes[i] & 0x0F];
        length++;
        if (pos >= bufLen - 1) {
            break;
        }
    }
    buf[pos] = '\0';
    return length;
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
void DmHexToBytes(const char *str, bool hasPrefix, uint8_t *bytes, uint32_t maxSize, uint32_t *size)
{
    DB_POINTER3(str, bytes, size);
    TextT hexText;
    hexText.str = (char *)(uintptr_t)str;
    hexText.len = (uint32_t)strlen(str);

    DbHexTextToBytes(&hexText, hasPrefix, bytes, maxSize, size);
}

bool DmCheckHexValid(const char *str, uint32_t begin)
{
    DB_POINTER(str);
    uint32_t len = (uint32_t)strlen(str);
    for (uint32_t i = begin; i < len; i++) {
        if (str[i] >= '0' && str[i] <= '9') {
            continue;
        } else if (str[i] >= 'A' && str[i] <= 'F') {
            continue;
        } else if (str[i] >= 'a' && str[i] <= 'f') {
            continue;
        } else {
            return false;
        }
    }
    return true;
}

bool DmCheckHexValidWithPrefix(const char *str)
{
    DB_POINTER(str);
    uint32_t begin = 0;
    if (strlen(str) <= DM_COMMON_HEX_PREFIX_LEN) {
        return false;
    }
    if (str[begin] != '0') {
        return false;
    }
    begin++;
    if (str[begin] != 'x' && str[begin] != 'X') {
        return false;
    }
    begin++;
    return DmCheckHexValid(str, begin);
}

Status DmConvertStrTimeToIntTime(const char *strTime, int64_t *intTime)
{
    DB_POINTER2(strTime, intTime);
    struct tm timeptr;
    Status ret = DbStrToTime(strTime, DB_DEFAULT_TIME_FORMAT, &timeptr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "Convert int64_t time value to strTime:%s.", strTime);
        return GMERR_INTERNAL_ERROR;
    }
    *intTime = (int64_t)mktime(&timeptr);
    return GMERR_OK;
}

Status DmTruncPrintSignedValueToBuf(int64_t value, char **strCursor, uint32_t maxLen)
{
    DB_POINTER(strCursor);
    int32_t length = snprintf_truncated_s(*strCursor, maxLen, "%" PRId64, value);
    if (length < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    *strCursor += length;
    return GMERR_OK;
}

Status DmTruncPrintUnsignedValueToBuf(uint64_t value, char **strCursor, uint32_t maxLen)
{
    DB_POINTER(strCursor);
    int32_t length = snprintf_truncated_s(*strCursor, maxLen, "%" PRIu64 "", value);
    if (length < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    *strCursor += length;
    return GMERR_OK;
}

Status DmTruncPrintCharValueToBuf(char value, char **strCursor, uint32_t maxLen)
{
    if (maxLen < (uint32_t)sizeof(char)) {
        return GMERR_FIELD_OVERFLOW;
    }
    **strCursor = value;
    *strCursor += (uint32_t)sizeof(char);
    return GMERR_OK;
}

Status DmTruncPrintFloatToBuf(float value, char **strCursor, uint32_t maxLen)
{
    DB_POINTER(strCursor);
    int32_t length = snprintf_truncated_s(*strCursor, maxLen, DM_PRINT_FLOAT_FORMAT, value);
    if (length < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    *strCursor += length;
    return GMERR_OK;
}

Status DmTruncPrintDoubleToBuf(double value, char **strCursor, uint32_t maxLen)
{
    DB_POINTER(strCursor);
    int32_t length = snprintf_truncated_s(*strCursor, maxLen, DM_PRINT_DOUBLE_FORMAT, value);
    if (length < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    *strCursor += length;
    return GMERR_OK;
}

Status DmTruncPrintStringToBuf(const char *str, char **strCursor, uint32_t maxLen)
{
    DB_POINTER(strCursor);
    if (str == NULL) {
        return GMERR_OK;
    }
    int32_t length = snprintf_truncated_s(*strCursor, maxLen, "%s", str);
    if (length < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    *strCursor += length;
    return GMERR_OK;
}

Status DmTruncPrintBitmapValueToBuf(const DmValueT *propertyValue, char **strCursor, uint32_t maxLen)
{
    Status ret = DmTruncPrintStringToBuf("beginPos:", strCursor, maxLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmTruncPrintUnsignedValueToBuf(propertyValue->value.beginPos, strCursor, maxLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmTruncPrintStringToBuf(", endPos:", strCursor, maxLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmTruncPrintUnsignedValueToBuf(propertyValue->value.endPos, strCursor, maxLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmTruncPrintStringToBuf(", length:", strCursor, maxLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    return DmTruncPrintUnsignedValueToBuf(propertyValue->value.length, strCursor, maxLen);
}

Status DmTruncPrintPropertyValue(const DmValueT *propertyValue, char **strCursor, uint32_t maxLen)
{
    DB_POINTER2(propertyValue, strCursor);

    switch (propertyValue->type) {
        case DB_DATATYPE_CHAR:
            return DmTruncPrintCharValueToBuf(propertyValue->value.charValue, strCursor, maxLen);
        case DB_DATATYPE_INT8:
            return DmTruncPrintSignedValueToBuf((int64_t)propertyValue->value.byteValue, strCursor, maxLen);
        case DB_DATATYPE_UCHAR:
            return DmTruncPrintUnsignedValueToBuf((uint64_t)propertyValue->value.ucharValue, strCursor, maxLen);
        case DB_DATATYPE_UINT8:
            return DmTruncPrintUnsignedValueToBuf((uint64_t)propertyValue->value.ubyteValue, strCursor, maxLen);
        case DB_DATATYPE_PARTITION:
            return DmTruncPrintUnsignedValueToBuf((uint64_t)propertyValue->value.partitionValue, strCursor, maxLen);
        case DB_DATATYPE_INT16:
            return DmTruncPrintSignedValueToBuf((int64_t)propertyValue->value.shortValue, strCursor, maxLen);
        case DB_DATATYPE_UINT16:
            return DmTruncPrintUnsignedValueToBuf((uint64_t)propertyValue->value.ushortValue, strCursor, maxLen);
        case DB_DATATYPE_INT32:
            return DmTruncPrintSignedValueToBuf((int64_t)propertyValue->value.intValue, strCursor, maxLen);
        case DB_DATATYPE_UINT32:
            return DmTruncPrintUnsignedValueToBuf((uint64_t)propertyValue->value.uintValue, strCursor, maxLen);
        case DB_DATATYPE_BOOL:
            return DmTruncPrintUnsignedValueToBuf((uint64_t)propertyValue->value.boolValue, strCursor, maxLen);
        case DB_DATATYPE_INT64:
            return DmTruncPrintSignedValueToBuf(propertyValue->value.longValue, strCursor, maxLen);
        case DB_DATATYPE_UINT64:
            return DmTruncPrintUnsignedValueToBuf(propertyValue->value.ulongValue, strCursor, maxLen);
        case DB_DATATYPE_RESOURCE:
            return DmTruncPrintUnsignedValueToBuf(propertyValue->value.resValue, strCursor, maxLen);
        case DB_DATATYPE_TIME:
            return DmTruncPrintSignedValueToBuf(propertyValue->value.timeValue, strCursor, maxLen);
        case DB_DATATYPE_BITFIELD8:
            return DmTruncPrintUnsignedValueToBuf((uint64_t)propertyValue->value.bitfield8, strCursor, maxLen);
        case DB_DATATYPE_BITFIELD16:
            return DmTruncPrintUnsignedValueToBuf((uint64_t)propertyValue->value.bitfield16, strCursor, maxLen);
        case DB_DATATYPE_BITFIELD32:
            return DmTruncPrintUnsignedValueToBuf((uint64_t)propertyValue->value.bitfield32, strCursor, maxLen);
        case DB_DATATYPE_BITFIELD64:
            return DmTruncPrintUnsignedValueToBuf((uint64_t)propertyValue->value.bitfield64, strCursor, maxLen);
        case DB_DATATYPE_DOUBLE:
            return DmTruncPrintDoubleToBuf(propertyValue->value.doubleValue, strCursor, maxLen);
        case DB_DATATYPE_FLOAT:
            return DmTruncPrintFloatToBuf(propertyValue->value.floatValue, strCursor, maxLen);
        case DB_DATATYPE_STRING:
            return DmTruncPrintStringToBuf((char *)propertyValue->value.strAddr, strCursor, maxLen);
        case DB_DATATYPE_BYTES:
        case DB_DATATYPE_FIXED:
        case DB_DATATYPE_UNION:
            PrintByteFixedTypeToBuf(
                (uint8_t *)propertyValue->value.strAddr, propertyValue->value.length, (uint8_t **)strCursor, maxLen);
            return GMERR_OK;
        case DB_DATATYPE_BITMAP:
            return DmTruncPrintBitmapValueToBuf(propertyValue, strCursor, maxLen);
        case DB_DATATYPE_NULL:
        case DB_DATATYPE_PLACE_HOLDER:
            return DmTruncPrintStringToBuf("null", strCursor, maxLen);
        default:
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PROPERTY, "Print property value.");
            return GMERR_INVALID_PROPERTY;
    }
}

static void DmSetPrintLen(DmPrintLenT *printLen, uint32_t typeLen, uint32_t realLen)
{
    printLen->typeLen = typeLen;
    printLen->realLen = realLen;
}

Status DmValueFormat(const DmValueT *srcValue, char *dst, uint32_t dstLen, DmPrintFmtE fmt)
{
    char src[DM_PRINT_MAX_INTEGER_LEN] = {0};  // 用来将整型转为字符串
    uint32_t srcLen = DM_PRINT_MAX_INTEGER_LEN;
    DmPrintLenT printLen = {0};
    // 兼容V3格式
    DmPrintFmtE format = (srcValue->type != DB_DATATYPE_TIME && fmt == TIME_FORMAT) ? STRING_FORMAT : fmt;

    switch (srcValue->type) {
        case DB_DATATYPE_INT32:
            if (snprintf_s(src, srcLen, srcLen - 1, "%" PRId32 "", srcValue->value.intValue) < 0) {
                return GMERR_FIELD_OVERFLOW;
            }
            DmSetPrintLen(&printLen, (uint32_t)sizeof(int32_t), (uint32_t)strlen(src));
            return DmPrintFormat(src, printLen, dst, dstLen, format, false, DB_DATATYPE_INT32);
        case DB_DATATYPE_UINT32:
            if (snprintf_s(src, srcLen, srcLen - 1, "%" PRIu32 "", srcValue->value.uintValue) < 0) {
                return GMERR_FIELD_OVERFLOW;
            }
            DmSetPrintLen(&printLen, (uint32_t)sizeof(uint32_t), (uint32_t)strlen(src));
            return DmPrintFormat(src, printLen, dst, dstLen, format, false, DB_DATATYPE_UINT32);
        case DB_DATATYPE_INT64:
            if (snprintf_s(src, srcLen, srcLen - 1, "%lld", srcValue->value.longValue) < 0) {
                return GMERR_FIELD_OVERFLOW;
            }
            DmSetPrintLen(&printLen, (uint32_t)sizeof(int64_t), (uint32_t)strlen(src));
            return DmPrintFormat(src, printLen, dst, dstLen, format, false, DB_DATATYPE_INT64);
        case DB_DATATYPE_UINT64:
            if (snprintf_s(src, srcLen, srcLen - 1, "%" PRIu64, srcValue->value.ulongValue) < 0) {
                return GMERR_FIELD_OVERFLOW;
            }
            DmSetPrintLen(&printLen, (uint32_t)sizeof(uint64_t), (uint32_t)strlen(src));
            return DmPrintFormat(src, printLen, dst, dstLen, format, false, DB_DATATYPE_UINT64);
        case DB_DATATYPE_TIME:
            if (snprintf_s(src, srcLen, srcLen - 1, "%lld", srcValue->value.timeValue) < 0) {
                return GMERR_FIELD_OVERFLOW;
            }
            DmSetPrintLen(&printLen, (uint32_t)sizeof(int32_t), (uint32_t)strlen(src));
            return DmPrintFormat(src, printLen, dst, dstLen, format, false, DB_DATATYPE_TIME);
        case DB_DATATYPE_STRING:
            DmSetPrintLen(&printLen, srcValue->value.length - 1, srcValue->value.length - 1);
            return DmPrintFormat(
                (char *)srcValue->value.strAddr, printLen, dst, dstLen, format, false, DB_DATATYPE_STRING);
        case DB_DATATYPE_BYTES:
        case DB_DATATYPE_FIXED:
        case DB_DATATYPE_UNION:
            DmSetPrintLen(&printLen, srcValue->value.length, srcValue->value.length);
            return DmPrintFormat(
                (char *)srcValue->value.strAddr, printLen, dst, dstLen, format, false, DB_DATATYPE_FIXED);
        default:
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PROPERTY, "Print property value with format.");
            return GMERR_INVALID_PROPERTY;
    }
}
#endif /* FEATURE_SIMPLEREL */
