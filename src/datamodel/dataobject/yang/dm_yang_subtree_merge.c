/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: source file for subtree merge
 * Create: 2023-05-06
 */

#include "dm_yang_subtree.h"
#include "dm_data_node_inner.h"
#include "dm_data_record.h"
#include "dm_log.h"

inline static bool IsInvalidPrope(const DmPropertyInfoT *propeInfo)
{
    return !propeInfo->isValid || propeInfo->isSysPrope;
}

// 将srcTree放在destTree末尾
void AppendSubtree(DmSubtreeT *destTree, DmSubtreeT *srcTree)
{
    DB_POINTER(destTree);
    if (srcTree == NULL) {
        return;
    }
    DmSubtreeT *tree = destTree;
    while (tree->nextTree != NULL) {
        tree = tree->nextTree;
    }
    tree->nextTree = srcTree;
}

bool IsSameKey(const uint8_t *keyBuf1, uint32_t keyLen1, const uint8_t *keyBuf2, uint32_t keyLen2)
{
    DB_POINTER2(keyBuf1, keyBuf2);
    if (keyLen1 != keyLen2) {
        return false;
    }
    return memcmp(keyBuf1, keyBuf2, keyLen1) == 0;
}

// 遍历listHead，查找到与 keyData 1 的key数据一致的数据
static Status FindListItemByVertexKey(
    DmSubtreeT *listHead, const uint8_t *keyData1, uint32_t keyLen1, DmSubtreeT **result)
{
    DB_POINTER3(listHead, keyData1, result);
    uint32_t pkIndexId;
    Status ret = GMERR_OK;

    DmSubtreeT *currTree = listHead;
    while (currTree != NULL) {
        uint8_t *keyData2 = NULL;
        uint32_t keyLen2;
        pkIndexId = currTree->vertex->vertexDesc->indexKeyBufInfos[0].indexId;
        DB_ASSERT(currTree->vertexSeriBuf != NULL);
        DmIndexKeyBufInfoT *keyInfo;
        ret = DmVertexGetIndexKeyInfo(currTree->vertex, pkIndexId, (DmIndexKeyBufInfoT **)&keyInfo);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        DmVertexGetCompareKeyBuf(currTree->vertex, (uint8_t **)&keyData2);
        DmGetKeyBufFromVertexBuf(keyInfo, currTree->vertexSeriBuf, keyData2, &keyLen2);

        if (IsSameKey(keyData1, keyLen1, keyData2, keyLen2)) {
            *result = currTree;
            return GMERR_OK;
        }
        currTree = currTree->nextTree;
    }
    *result = NULL;
    return GMERR_OK;
}

// 将src的内容合并到dst中
static Status MergeList(DmSubtreeT *srcTree, DmSubtreeT *dstTree)
{
    DmSubtreeT *currDiffKeyTree = NULL;
    DmSubtreeT *firstDiffKeyTree = NULL;
    DmSubtreeT *tree = srcTree;
    Status ret = GMERR_OK;
    while (tree) {
        DmSubtreeT *tmp = tree;
        tree = tree->nextTree;
        tmp->nextTree = NULL;

        DmSubtreeT *sameKeyTree = NULL;
        uint8_t *keyData1 = NULL;
        uint32_t keyLen1;
        DB_ASSERT(tmp->vertexSeriBuf != NULL);
        uint32_t pkIndexId = tmp->mergeVertex->vertexDesc->indexKeyBufInfos[0].indexId;
        DmIndexKeyBufInfoT *keyInfo;
        ret = DmVertexGetIndexKeyInfo(tmp->mergeVertex, pkIndexId, (DmIndexKeyBufInfoT **)&keyInfo);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        DmVertexGetCompareKeyBuf(tmp->mergeVertex, (uint8_t **)&keyData1);
        DmGetKeyBufFromVertexBuf(keyInfo, tmp->vertexSeriBuf, keyData1, &keyLen1);
        ret = FindListItemByVertexKey(dstTree, keyData1, keyLen1, &sameKeyTree);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        if (sameKeyTree != NULL) {
            // 将tmp内容合并到sameKeyTree
            ret = DmMergeSubTree(tmp, sameKeyTree);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
            continue;
        }

        if (currDiffKeyTree == NULL) {
            currDiffKeyTree = tmp;
            firstDiffKeyTree = currDiffKeyTree;
        } else {
            currDiffKeyTree->nextTree = tmp;
            currDiffKeyTree = currDiffKeyTree->nextTree;
        }
    }

    // 处理diffKeyTree，挂在dstTree末尾
    AppendSubtree(dstTree, firstDiffKeyTree);
    return GMERR_OK;
}

static Status DmMergeSubtreeRecord(DmRecordT *src, DmRecordT *dst)
{
    DB_POINTER2(src, dst);
    // 普通字段
    Status ret = GMERR_OK;
    uint32_t propeNum = src->recordDesc->propeNum;
    DmPropertyInfoT *propeInfos = src->recordDesc->propeInfos;
    for (uint32_t i = 0; i < propeNum; ++i) {
        if (IsInvalidPrope(&propeInfos[i])) {
            continue;
        }
        // 仅在src的字段值非NULL而dst的字段值为NULL的情况下需要拷贝
        if (src->propeIsSetValue[i] == DM_PROPERTY_IS_NOT_NULL && dst->propeIsSetValue[i] == DM_PROPERTY_IS_NULL) {
            DmValueT value = {0};
            ret = RecordGetPropeById(src, i, &value, false);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
            dst->propeIsEmpty[i] = src->propeIsEmpty[i];  // 目前暂时复用 propeIsEmpty 来表示属性值是否来源于默认值
            ret = RecordSetPropeById(i, value, dst);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static Status GetTreeRecord(DmSubtreeT *tree, DmRecordT *record, bool isMerge)
{
    if (DmIsVertexSubtree(tree)) {
        *record = isMerge ? *(tree->mergeVertex->record) : *(tree->vertex->record);
        return GMERR_OK;
    }
    DmNodeT *node = isMerge ? tree->mergeNode : tree->node;
    if (!DmNodeIsCreated(node)) {
        DB_LOG_ERROR(GMERR_NO_DATA, "Subtree not create %s.", DmGetSubtreeName(tree));
        return GMERR_NO_DATA;
    }
    Status ret = DmNodeSetElementIndex(node, tree->nodeElemIdx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Set tree node %s element index: %" PRIu32 ".", DmGetSubtreeName(tree), tree->nodeElemIdx);
        return ret;
    }
    *record = *(node->currRecord);
    return GMERR_OK;
}

Status DmMergeSingleSubtreeNode(DmSubtreeT *srcTree, DmSubtreeT *destTree)
{
    DB_POINTER2(srcTree, destTree);

    DmRecordT src;
    DmRecordT dst;
    Status ret = GetTreeRecord(srcTree, &src, true);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = GetTreeRecord(destTree, &dst, false);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return DmMergeSubtreeRecord(&src, &dst);
}

Status DmNodeCopyForSubtree(DmNodeT *srcNode, DmNodeT *destNode);

Status DmNodeCopyElementForSubtree(DmNodeT *srcNode, uint32_t idx, DmNodeT *destNode)
{
    Status ret = DmNodeSetElementIndex(srcNode, idx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = DmNodeSetElementIndex(destNode, idx);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    ret = DmCopyRecord(destNode->currRecord, srcNode->currRecord);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Copy subtree %s record", srcNode->nodeDesc->name);
        return ret;
    }

    for (uint32_t i = 0; i < srcNode->nodeDesc->nodeNumPerElement; i++) {
        DmNodeT *destChildNode = NULL;
        ret = DmNodeGetChildNodeByIndex(destNode, i, &destChildNode);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DmNodeCopyForSubtree(srcNode->currNodes[i], destChildNode);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to copy node for subtree %s", srcNode->nodeDesc->name);
            return ret;
        }
    }

    return GMERR_OK;
}

// node 节点深拷贝，如果 destNode 不为空，会重置 destNode 的内容再拷贝
Status DmNodeCopyForSubtree(DmNodeT *srcNode, DmNodeT *destNode)
{
    // srcNode可能为NULL
    DB_POINTER(destNode);
    if (srcNode == destNode) {
        return GMERR_OK;
    }
    Status ret = GMERR_OK;
    if (SECUREC_UNLIKELY(destNode != NULL && destNode->isCreated && destNode->realElementNum != 0)) {
        ret = DmResetNode(destNode);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (!DmNodeIsCreated(srcNode) || srcNode->realElementNum == 0) {
        return GMERR_OK;
    }
    // DEBUG 模式下校验node name
#ifndef NDEBUG
    if (strcmp(srcNode->nodeDesc->name, destNode->nodeDesc->name) != 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR, "Unmatch node name, src: %s, dest: %s",
            srcNode->nodeDesc->name, destNode->nodeDesc->name);
        return GMERR_INTERNAL_ERROR;
    }
#endif
    destNode->isCreated = srcNode->isCreated;
    uint32_t curElementIndex = srcNode->currRecord->recordIndex;
    while (srcNode->realElementNum > destNode->realElementNum) {
        ret = DmNodeVectorAppend(destNode);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Append vector for destNode, src num:%" PRIu32 ", dest num:%" PRIu32 ".",
                srcNode->realElementNum, destNode->realElementNum);
            return ret;
        }
    }

    for (uint32_t i = 0; i < srcNode->realElementNum; i++) {
        ret = DmNodeCopyElementForSubtree(srcNode, i, destNode);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Copy subtree element subtree, idx=%" PRIu32 ".", i);
            return ret;
        }
    }

    ret = DmNodeSetElementIndex(srcNode, curElementIndex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = DmNodeSetElementIndex(destNode, curElementIndex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    return GMERR_OK;
}

static void DmMergeSubTreeCheck(DmSubtreeT *srcTree, DmSubtreeT *destTree)
{
    if (!DmIsVertexSubtree(srcTree)) {
        DB_ASSERT(srcTree->vertexSeriBuf == NULL);
    } else {
        DB_ASSERT(srcTree->vertexSeriBuf != NULL);
    }
    if (!DmIsVertexSubtree(destTree)) {
        DB_ASSERT(destTree->vertexSeriBuf == NULL);
    } else {
        DB_ASSERT(destTree->vertexSeriBuf != NULL);
    }
}

static Status DmMergeDeSerialize(DmSubtreeT *srcTree, DmSubtreeT *destTree)
{
    Status ret = GMERR_OK;
    if (srcTree->vertexSeriBuf != NULL) {
        ret = DmDeSerialize2ExistsVertex(
            srcTree->vertexSeriBuf, srcTree->vertexSeriBufLen, srcTree->mergeVertex, DmGetCheckMode());
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Deseri srcTree. buf len= %" PRIu32, srcTree->vertexSeriBufLen);
            return ret;
        }
    }
    if (destTree->vertexSeriBuf != NULL) {
        ret = DmDeSerialize2ExistsVertex(
            destTree->vertexSeriBuf, destTree->vertexSeriBufLen, destTree->vertex, DmGetCheckMode());
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Deseri destTree. buf len= %" PRIu32, destTree->vertexSeriBufLen);
            return ret;
        }
    }
    return GMERR_OK;
}

Status DmMergeSingleSubtree(DmSubtreeT *srcTree, DmSubtreeT *destTree)
{
    DmMergeSubTreeCheck(srcTree, destTree);

    // 先反序列化
    Status ret = DmMergeDeSerialize(srcTree, destTree);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Deseri subtree node %s.", DmGetSubtreeName(srcTree));
        return ret;
    }

    // 合并当前tree的vertex
    return DmMergeSingleSubtreeNode(srcTree, destTree);
}

// 根据index从DmSubtreeT上获取子node，如果node未创建，则进行create
Status GetChildNodeFromSubtreeByIndex(DmSubtreeT *tree, uint32_t index, DmNodeT **childNode)
{
    if (DmIsVertexSubtree(tree)) {
        return DmVertexGetChildNodeByIndex(tree->vertex, index, childNode);
    }
    return DmNodeGetChildNodeByIndex(tree->node, index, childNode);
}

// 根据index从DmSubtreeT上获取子node，如果node未创建，则进行create
Status GetChildMergeNodeFromSubtreeByIndex(DmSubtreeT *tree, uint32_t index, DmNodeT **childMergeNode)
{
    if (DmIsVertexSubtree(tree)) {
        return DmVertexGetChildNodeByIndex(tree->mergeVertex, index, childMergeNode);
    }
    return DmNodeGetChildNodeByIndex(tree->mergeNode, index, childMergeNode);
}

void DmSubtreeSetNodeRecursive(DmSubtreeT *nodeSubtree, DmNodeT *node, bool isMergeNode)
{
    DB_POINTER2(nodeSubtree, node);
    if (DmIsVertexSubtree(nodeSubtree)) {
        return;
    }
    // 将node设置到subtreeNode上，递归设置！
    if (isMergeNode) {
        nodeSubtree->mergeNode = node;
    } else {
        nodeSubtree->node = node;
    }
    for (uint32_t i = 0; i < nodeSubtree->desc->childNum; ++i) {
        DmSubtreeT *childTree = nodeSubtree->childTree[i];
        if (childTree == NULL) {
            continue;
        }
        Status ret = DmNodeSetElementIndex(node, nodeSubtree->nodeElemIdx);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Set element index %" PRIu32 ".", nodeSubtree->nodeElemIdx);
            DB_ASSERT(0);
        }

        DmSubtreeSetNodeRecursive(childTree, node->currNodes[childTree->desc->index], isMergeNode);
    }
}

Status DmCopyNodeForEmptyDestChildTree(DmSubtreeT *destTree, DmSubtreeT *srcChildTree, uint32_t i)
{
    // node 节点上 dest 为空时需要拷贝 mergeNode节点到对应 element（dest 与对应 mergeNode）上，并设置 subtree node信息
    DmNodeT *destChildNode = NULL;
    DmNodeT *destMergeChildNode = NULL;
    Status ret = GetChildNodeFromSubtreeByIndex(destTree, srcChildTree->desc->index, &destChildNode);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GetChildMergeNodeFromSubtreeByIndex(destTree, srcChildTree->desc->index, &destMergeChildNode);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmNodeCopyForSubtree(srcChildTree->mergeNode, destChildNode);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Copy merge node %s.", srcChildTree->node->nodeDesc->name);
        return ret;
    }
    ret = DmNodeCopyForSubtree(srcChildTree->mergeNode, destMergeChildNode);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Copy merge node %s.", srcChildTree->node->nodeDesc->name);
        return ret;
    }
    DmSubtreeSetNodeRecursive(destTree->childTree[i], destChildNode, false);
    DmSubtreeSetNodeRecursive(destTree->childTree[i], destMergeChildNode, true);
    return GMERR_OK;
}

Status DmMergeSubTree(DmSubtreeT *srcTree, DmSubtreeT *destTree)
{
    DB_POINTER2(srcTree, destTree);
    // 合并当前tree节点
    Status ret = DmMergeSingleSubtree(srcTree, destTree);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 合并孩子
    for (uint32_t i = 0; i < srcTree->desc->childNum; ++i) {
        DmSubtreeT *srcChildTree = srcTree->childTree[i];
        DmSubtreeT *destChildTree = destTree->childTree[i];
        if (srcChildTree == NULL) {
            continue;
        }
        if (destChildTree == NULL) {
            destTree->childTree[i] = srcChildTree;
            srcTree->childTree[i] = NULL;
            // node 类型将node拷贝到 destTree node上，并修改destTree的node指针指向
            if (!DmIsVertexSubtree(srcChildTree)) {
                ret = DmCopyNodeForEmptyDestChildTree(destTree, srcChildTree, i);
                if (ret != GMERR_OK) {
                    return ret;
                }
            }
            continue;
        }

        if (DmIsVertexSubtree(srcChildTree) && DmIsListVertexLabel(srcChildTree->desc->vertexLabel)) {
            // list vertex
            ret = MergeList(srcChildTree, destChildTree);
            srcTree->childTree[i] = NULL;
        } else {
            ret = DmMergeSubTree(srcChildTree, destChildTree);
        }
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    // 合并完所有孩子后，重新序列化
    if (DmIsVertexSubtree(destTree)) {
        ret = DmSerializeSubtreeVertex(destTree);
    }
    return ret;
}
