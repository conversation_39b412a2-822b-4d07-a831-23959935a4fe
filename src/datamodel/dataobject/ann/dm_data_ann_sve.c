/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: implementation of print value of type
 * Author: qh
 * Create: 2025-05-06
 */

#include "dm_data_annvector.h"
#if defined(__aarch64__)
// sve的内置intrinsic接口方式，如果后续gcc版本升级到10以上，可以直接使用
#ifdef __ARM_FEATURE_SVE
#include <arm_sve.h>
double CalculateL2WithSveIntrinsic(const AnnVecT *x, const AnnVecT *y)
{
    uint16_t d = x->dim;
    svfloat32_t sum = svdup_f32(0.0f);
    uint16_t i = 0;

    svbool_t pg = svptrue_b32();

    while (i < d) {
        if (d - i < svcntw())
            pg = svwhilelt_b32(i, d);

        svfloat32_t a = svld1_f32(pg, (float *)x->items + i);
        svfloat32_t b = svld1_f32(pg, (float *)y->items + i);
        svfloat32_t diff = svsub_f32_m(pg, a, b);
        sum = svmla_f32_m(pg, sum, diff, diff);
        i += svcntw();
    }

    return svaddv_f32(svptrue_b32(), sum);
}
#endif

double CalculateL2WithSve(const AnnVecT *x, const AnnVecT *y)
{
    double result = 0;

    __asm__ __volatile__(".arch armv8.2-a+sve\n"
                         "mov     x0, x0;"
                         "mov     x1, x1;"
                         "ldrh    w3, [x0, #4];"
                         "cntw    x2;"
                         "neg     x4, x2;"
                         "ldr     x5, [x1, #8];"
                         "ptrue   p0.s;"
                         "mov     x1, x3;"
                         "mov     z0.s, #0;"
                         "ldr     x6, [x0, #8];"
                         "mov     x0, #0x0;"

                         "loop8_19668_%=:"
                         "cmp     x1, x2;"
                         "b.cs    start8_19668_%=;"
                         "whilelo p0.s, x0, x3;"

                         "start8_19668_%=:"
                         "add	x7, x6, x0, lsl #2;"
                         "ld1w    {z1.s}, p0/z, [x7];"
                         "prfm PLDL1KEEP, [x7, #512];"
                         "ld1w    {z2.s}, p0/z, [x5, x0, lsl #2];"
                         "add     x0, x0, x2                    ;"
                         "fsub    z1.s, p0/m, z1.s, z2.s        ;"
                         "add     x1, x1, x4                    ;"
                         "fmla    z0.s, p0/m, z1.s, z1.s        ;"
                         "cmp     x3, x0                        ;"
                         "b.hi    loop8_19668_%=;"
                         "ptrue   p0.b                          ;"
                         "faddv   s0, p0, z0.s                  ;"
                         "fcvt    d0, s0                        ;"
                         "ret                                   ;"
                         : [result] "=r"(result)
                         : [x] "r"(x), [y] "r"(y));
    return result;
}

#endif  // __aarch64__
