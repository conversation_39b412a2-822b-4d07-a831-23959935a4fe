/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * Description: header file for data model EdgeT
 * Author: wangweicheng
 * Create: 2020-9-10
 */

#include <securec.h>
#include "dm_data_topo_seri.h"

#include "se_heap_access.h"
#include "dm_log.h"

struct DmEdgeTopo {
    DbMemCtxT *memCtx;
    DmEdgeLabelT *edgeLabel;
    uint64_t addr[EDGE_TOPO_ADDR_TYPE_BOTTOM];  // 入点addr，出点addr，入点下一条边addr，出点下一条边addr
    uint8_t *seriBuf;                           // 序列化addr
};

Status DmCreateEmptyEdgeTopoWithMemCtx(DbMemCtxT *memCtx, DmEdgeLabelT *edgeLabel, DmEdgeTopoT **edgeTopo)
{
    DB_POINTER3(memCtx, edgeLabel, edgeTopo);
    // 内存释放逻辑：DmDestroyEdgeTopo
    *edgeTopo = (DmEdgeTopoT *)DbDynMemCtxAlloc(memCtx, sizeof(DmEdgeTopoT));
    if (*edgeTopo == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc mem for edge topo, size: %zu", sizeof(DmEdgeTopoT));
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(*edgeTopo, sizeof(DmEdgeTopoT), 0x00, sizeof(DmEdgeTopoT));
    (*edgeTopo)->memCtx = memCtx;
    (*edgeTopo)->edgeLabel = edgeLabel;
    return GMERR_OK;
}

void DmDestroyEdgeTopo(DmEdgeTopoT *edgeTopo)
{
    if (edgeTopo == NULL) {
        return;
    }

    if (edgeTopo->seriBuf != NULL) {
        DbDynMemCtxFree(edgeTopo->memCtx, edgeTopo->seriBuf);
        edgeTopo->seriBuf = NULL;
    }
    DbDynMemCtxFree(edgeTopo->memCtx, edgeTopo);
}

void DmSetEdgeTopoAddr(DmEdgeTopoT *edgeTopo, EdgeTopoAddrTypeE addrType, uint64_t addr)
{
    DB_POINTER(edgeTopo);
    DB_ASSERT(addrType < EDGE_TOPO_ADDR_TYPE_BOTTOM);
    edgeTopo->addr[addrType] = addr;
}

uint64_t DmGetEdgeTopoAddr(const DmEdgeTopoT *edgeTopo, EdgeTopoAddrTypeE addrType)
{
    DB_POINTER(edgeTopo);
    DB_ASSERT(addrType < EDGE_TOPO_ADDR_TYPE_BOTTOM);
    return edgeTopo->addr[addrType];
}

void DmEdgeTopoSetAddr(uint8_t *edgeTopoBuf, EdgeTopoAddrTypeE addrType, uint64_t addr)
{
    DB_POINTER(edgeTopoBuf);
    DB_ASSERT(addrType < EDGE_TOPO_ADDR_TYPE_BOTTOM);
    uint8_t *bufCursor = edgeTopoBuf;
    bufCursor += (uint32_t)addrType * sizeof(uint64_t);
    *(uint64_t *)bufCursor = addr;
}

uint64_t DmEdgeTopoGetAddr(uint8_t *edgeTopoBuf, EdgeTopoAddrTypeE addrType)
{
    DB_POINTER(edgeTopoBuf);
    DB_ASSERT(addrType < EDGE_TOPO_ADDR_TYPE_BOTTOM);
    uint8_t *bufCursor = edgeTopoBuf;
    bufCursor += (uint32_t)addrType * sizeof(uint64_t);
    return *(uint64_t *)bufCursor;
}

// ---------------------------------------边序列化相关接口-----------------------------------------

uint32_t DmGetSeriEdgeAddrNum(const DmEdgeLabelT *edgeLabel)
{
    return edgeLabel->seriType == DM_NORMAL_SERI_EDGE ? EDGE_TOPO_ADDR_TYPE_BOTTOM : DEST_VERTEX_PREV_EDGE_ADDR;
}

Status DmSerializeEdgeTopo2InvokerBuf(
    const DmEdgeTopoT *edgeTopo, uint32_t length, SeHpTupleAddrMode addrMode, uint8_t *buf)
{
    DB_POINTER2(edgeTopo, buf);
    uint32_t totalLength = DmEdgeTopoGetSerializeBufLength(edgeTopo, addrMode);
    if (length != totalLength) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "EdgeTopo seri mistake, check bufLen,"
            " len: %" PRIu32 ", totalLen: %" PRIu32 "",
            length, totalLength);
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t addrNum = DmGetSeriEdgeAddrNum(edgeTopo->edgeLabel);
    uint8_t *bufCursor = buf;
    if (addrMode == SE_HEAP_TUPLE_ADDR_32) {
        // 每个uint64类型的addr压缩至32位
        TuplePointer32T addr[EDGE_TOPO_ADDR_TYPE_BOTTOM];
        for (uint32_t i = 0; i < addrNum; i++) {
            addr[i] = HeapCompressTupleAddr32(edgeTopo->addr[i]);
        }
        errno_t err = memcpy_s(bufCursor, totalLength, addr, totalLength);
        DB_ASSERT(err == EOK);
    } else if (addrMode == SE_HEAP_TUPLE_ADDR_48) {
        // 每个uint64类型的addr压缩至48位，使用3个uint16类型存储，因此申请大小为18的uint16数组
        TuplePointer48T addr[EDGE_TOPO_ADDR_TYPE_BOTTOM];
        for (uint32_t i = 0; i < addrNum; i++) {
            addr[i] = HeapCompressTupleAddr48(edgeTopo->addr[i]);
        }
        errno_t err = memcpy_s(bufCursor, totalLength, addr, totalLength);
        DB_ASSERT(err == EOK);
    } else {
        // addr不压缩，仍使用uint64类型存储
        errno_t err = memcpy_s(bufCursor, totalLength, edgeTopo->addr, totalLength);
        DB_ASSERT(err == EOK);
    }
    return GMERR_OK;
}

// QryCreateEdgeLabelInitCfg中会初始化heap rowSize，需要与此处一致
uint32_t DmEdgeTopoGetSerializeBufLength(const DmEdgeTopoT *edgeTopo, SeHpTupleAddrMode addrMode)
{
    DB_POINTER(edgeTopo);
    size_t len = 0;
    uint32_t addrNum = DmGetSeriEdgeAddrNum(edgeTopo->edgeLabel);
    switch (addrMode) {
        case SE_HEAP_TUPLE_ADDR_32:
            len = addrNum * sizeof(uint32_t);
            break;
        case SE_HEAP_TUPLE_ADDR_48:
            len = addrNum * SE_ADDR_ITEM_NUM * sizeof(uint16_t);  // 使用3个uint16来完成64位addr向48位addr的压缩
            break;
        case SE_HEAP_TUPLE_ADDR_64:
            len = addrNum * sizeof(uint64_t);
            break;
        default:
            DB_ASSERT(false);
    }
    return (uint32_t)len;  // addr
}

void DmDeSerialize2ExistsEdgeTopo(const uint8_t *buf, SeHpTupleAddrMode addrMode, DmEdgeTopoT *edgeTopo)
{
    DB_POINTER2(buf, edgeTopo);
    uint32_t addrNum = DmGetSeriEdgeAddrNum(edgeTopo->edgeLabel);
    if (addrMode == SE_HEAP_TUPLE_ADDR_32) {
        // buf中为压缩至32位的addr
        const TuplePointer32T *bufCursor = (const TuplePointer32T *)buf;
        for (uint32_t i = 0; i < addrNum; i++) {
            edgeTopo->addr[i] = HeapUncompressTupleAddr32(bufCursor[i]);
        }
    } else if (addrMode == SE_HEAP_TUPLE_ADDR_48) {
        // buf中为压缩至48位的addr
        const TuplePointer48T *bufCursor = (const TuplePointer48T *)buf;
        for (uint32_t i = 0; i < addrNum; i++) {
            edgeTopo->addr[i] = HeapUncompressTupleAddr48(bufCursor[i]);
        }
    } else {
        // buf中为未压缩的addr
        uint32_t totalLength = DmEdgeTopoGetSerializeBufLength(edgeTopo, addrMode);
        errno_t err = memcpy_s(edgeTopo->addr, totalLength, buf, totalLength);
        DB_ASSERT(err == EOK);
    }
}
