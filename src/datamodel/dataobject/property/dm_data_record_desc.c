/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: source file for data model Record desc
 * Author: zhang<PERSON>olei
 * Create: 2022-06-25
 */

#include "dm_data_record.h"
#include "db_memcpy.h"
#include "dm_yang_interface.h"
#include "dm_log.h"

/*
 * 预先获取单个属性值在runningBuf上所占的内存大小，创建接口优化使用
 */
static uint32_t GetPropertyRunningBufLen4PreMalloc(const DmPropertySchemaT *property)
{
    // 变长类型的长度，变长类型包括string，byte，union
    if (!DmIsFixedType(property->dataType)) {
        if (property->size > DM_ALLOC_VAR_PROPE_LEN_IN_BUF) {
            return DM_ALLOC_VAR_PROPE_LEN_IN_BUF + (uint32_t)sizeof(uint32_t) + (uint32_t)sizeof(uint32_t);
        } else {
            return (uint32_t)sizeof(uint32_t) + (uint32_t)sizeof(uint32_t) +
                   property->size;  // 4: varBufLen 4: realLength
        }
    } else if (property->dataType == DB_DATATYPE_BITMAP) {
        return property->size / BYTE_LENGTH;
    } else if (DmIsBitFieldType(property->dataType)) {
        // 位域类型的长度
        if (property->bitfieldOffset != 0) {  // 位域，只在首个字段时统计长度
            return 0;
        }
        return DmGetBasicDataTypeLength(property->dataType);
    } else {
        // 定长类型的长度
        return property->size;
    }
}

/*
 * 预先获取创建record所需runningBuf的内存大小，创建接口优化使用, 不包括系统字段
 */
static uint32_t GetRecordRunningBufLen4PreMalloc(const DmSchemaT *schema, const DmPropertySchemaT *properties)
{
    uint32_t len = 0;
    uint32_t propertyNum = schema->propeNum;
    for (uint32_t i = 0; i < propertyNum; i++) {
        if (properties[i].isValid == false || properties[i].isSysPrope) {
            continue;
        }
        len += GetPropertyRunningBufLen4PreMalloc(&properties[i]);
    }
    // 预留checkversion的位置
    if (schema->sysPropeNum != 0) {
        len += (uint32_t)sizeof(uint8_t);
    }
    return len;
}

static uint32_t GetPropeNumInDesc(DmSchemaT *schema)
{
    return schema->sysPropeNum == 0 ? schema->propeNum : ((schema->propeNum - schema->sysPropeNum) + RESERVED_SIZE);
}

uint32_t GetSchemaSize4PreMalloc(uint32_t propeNum, uint32_t runningBufLen)
{
    uint32_t bufSize = 0;
    // 以下为默认值相关处理，与ProcessDefaultValue4Create函数保持一致
    // 若record里没有普通属性，全是子树节点，此时runningBufLen为0，不用默认值相关buf空间的申请
    if (runningBufLen != 0) {
        bufSize += runningBufLen * (uint32_t)sizeof(uint8_t);  // defaultValueBuf 空间大小
        bufSize += propeNum * (uint32_t)sizeof(uint8_t);       // defaultValueNullInfo 空间大小
    }

    bufSize += propeNum * (uint32_t)sizeof(int8_t);    // propeIsSetValueAllSet 申请所需bufSize
    bufSize += propeNum * (uint32_t)sizeof(uint16_t);  // notNullPropeIds 申请所需bufSize
    return bufSize;
}

/*
 * 预先获取创建静态record所需申请的内存大小
 * 由于目前orm结构化写写死了系统字段只有check version，此处额外预留1位，没有实际用途，仅为了对齐结构化写
 */
static uint32_t GetRecordDescSize4PreMalloc(
    DmSchemaT *schema, DmRecordTypeE recordType, uint32_t *runningBufMemSize, DmPropertySchemaT *properties)
{
    uint32_t bufSize = (uint32_t)sizeof(RecordDescT);  // RecordDescT结构体大小
    uint32_t propeNum = GetPropeNumInDesc(schema);
    bufSize += propeNum * (uint32_t)sizeof(DmPropertyInfoT);  // propeInfos创建所需buf的size
    for (uint32_t i = 0; i < propeNum; i++) {  // propeInfos中name和defaultValue集中申请所需buf的size
        bufSize += properties[i].nameLen;
        if (properties[i].defaultValueNum != 0) {
            bufSize += (properties[i].defaultValueNum * (uint32_t)sizeof(DmValueT));
        }
        if (properties[i].defaultValueNum != 0 && DM_TYPE_NEED_MALLOC(properties[i].dataType)) {
            for (uint32_t j = 0; j < properties[i].defaultValueNum; ++j) {
                DmValueT defaultValue = MEMBER_PTR(&properties[i], defaultValue)[j];
                bufSize += defaultValue.value.length;
            }
        }
    }
    if (schema->superFieldNum != 0) {
        uint32_t superFieldNameLen = 0;
        DmSuperFieldT *superFields = NULL;
        superFields = MEMBER_PTR(schema, superFields);
        bufSize += schema->superFieldNum * (uint32_t)sizeof(DmSuperFieldInfoT);  // superFieldInfos创建所需buf的size
        for (uint32_t i = 0; i < schema->superFieldNum; i++) {
            superFieldNameLen += GetStrLen(MEMBER_PTR(&superFields[i], name));
        }
        bufSize += superFieldNameLen;
    }
    uint32_t runningBufLen = GetRecordRunningBufLen4PreMalloc(schema, properties);
    *runningBufMemSize = runningBufLen;
    // 以下为默认值相关处理，与ProcessDefaultValue4Create函数保持一致
    // 若record里没有普通属性，全是子树节点，此时runningBufLen为0，不用默认值相关buf空间的申请
    bufSize += GetSchemaSize4PreMalloc(propeNum, runningBufLen);
    return bufSize;
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
/*
 * 预先获取创建静态record所需申请的内存大小
 * 仅限于datalog和sql接口调用，表类型为VERTEX_TYPE_NORMAL，propes中的属性要在同一层。
 */
static uint32_t GetRecordRunningBufLen4PreMallocByPropes(DmPropertySchemaT **properties, uint32_t propeNum)
{
    uint32_t len = 0;
    uint8_t newBitfieldOffset = 0;
    for (uint32_t i = 0; i < propeNum - RESERVED_SIZE; i++) {
        // datalog和sql专用函数，传入的properties必须都是第一层，且不包含系统字段
        DB_ASSERT(properties[i]->isValid);
        DB_ASSERT(!properties[i]->isSysPrope);
        if (DmIsBitFieldType(properties[i]->dataType)) {
            if (i == 0 || properties[i]->dataType != properties[i - 1]->dataType ||
                newBitfieldOffset + properties[i]->size >
                    DmGetBasicDataTypeLength(properties[i]->dataType) * BYTE_LENGTH) {
                newBitfieldOffset = 0;
            }
            uint8_t originalBitFieldOffset = properties[i]->bitfieldOffset;
            properties[i]->bitfieldOffset = newBitfieldOffset;
            len += GetPropertyRunningBufLen4PreMalloc(properties[i]);
            properties[i]->bitfieldOffset = originalBitFieldOffset;
            newBitfieldOffset += properties[i]->size;
            continue;
        }
        newBitfieldOffset = 0;
        len += GetPropertyRunningBufLen4PreMalloc(properties[i]);
    }
    return len + RESERVED_SIZE;
}

/*
 * 预先获取创建静态record所需申请的内存大小
 * 仅限于datalog和sql接口调用，表类型为VERTEX_TYPE_NORMAL，propes中的属性要在同一层。
 */
static uint32_t GetRecordDescSize4PreMallocByPropes(
    uint32_t *runningBufMemSize, DmPropertySchemaT **properties, uint32_t propeNum)
{
    uint32_t bufSize = (uint32_t)sizeof(RecordDescT);          // RecordDescT结构体大小
    bufSize += propeNum * (uint32_t)sizeof(DmPropertyInfoT);   // propeInfos创建所需buf的size
    for (uint32_t i = 0; i < propeNum - RESERVED_SIZE; i++) {  // propeInfos中name集中申请所需buf的size
        bufSize += properties[i]->nameLen;
    }
    bufSize += RESERVED_SIZE == 0 ? 0 : (uint32_t)strlen(SYS_PROPE_CHECK_VERSION_NAME);

    uint32_t runningBufLen = GetRecordRunningBufLen4PreMallocByPropes(properties, propeNum);
    *runningBufMemSize = runningBufLen;
    // 以下为默认值相关处理，与ProcessDefaultValue4Create函数保持一致
    // 若record里没有普通属性，全是子树节点，此时runningBufLen为0，不用默认值相关buf空间的申请
    bufSize += GetSchemaSize4PreMalloc(propeNum, runningBufLen);
    return bufSize;
}
#endif /* FEATURE_SIMPLEREL */

/*
 * 预先获取创建record所需申请的内存大小，创建接口优化使用
 */
static uint32_t GetEmptyRecordSize4PreMalloc(const RecordDescT *recordDesc, DmRecordTypeE recordType)
{
    size_t bufSize = sizeof(DmRecordT);  // record结构体大小

    uint32_t runningBufLen = recordDesc->runningBufLen;
    // vertex record中runningBuf、propeIsSetValue和propeIsEmpty申请在record中
    // node record中runningBuf、propeIsSetValue和propeIsEmpty申请在node中，创建empty record时不用申请
    if ((recordType == DM_RECORD_IN_VERTEX) || (recordType == DM_RECORD_IN_YANG_VERTEX)) {
        bufSize += runningBufLen * sizeof(uint8_t);        // record申请runningBuf的Size
        bufSize += recordDesc->propeNum * sizeof(int8_t);  // record申请propeIsSetValue的Size
        uint32_t updateBitMapBufLen = 0;
        for (uint32_t i = 0; i < recordDesc->propeNum; i++) {
            if (recordDesc->propeInfos[i].isValid && recordDesc->propeInfos[i].dataType == DB_DATATYPE_BITMAP) {
                updateBitMapBufLen += DM_UPDATA_BITMAP_LEN;
            }
        }
        bufSize += updateBitMapBufLen;                     // updateBitMapInfo 申请所需bufSize
        bufSize += recordDesc->propeNum * sizeof(int8_t);  // propeIsEmpty 申请所需bufSize
        if (RecordIsYangType(recordDesc)) {
            bufSize += recordDesc->propOpTypesLen;  // 字段五原语操作信息申请所需bufSize
        }
    }

    return (uint32_t)bufSize;
}

void RecordDescInitPropeInfoForFixSizePart(RecordDescT *recordDesc, const DmPropertySchemaT *properties)
{
    const DmPropertySchemaT *property = NULL;
    uint32_t allFixPropertiesLen = 0;
    for (uint32_t i = 0; i < recordDesc->propeNum; i++) {
        property = &properties[i];
        if (recordDesc->hasBitMapPrope == 0) {
            recordDesc->hasBitMapPrope = (property->isValid) && (property->dataType == DB_DATATYPE_BITMAP) ? 1 : 0;
        }
        uint32_t propertySize = DmGetFixSizePropeSize(property);
        recordDesc->propeInfos[i].bitfieldOffset = property->bitfieldOffset;
        if (DmIsBitFieldType(property->dataType) && propertySize == 0 && i > 0) {
            recordDesc->propeInfos[i].offset = recordDesc->propeInfos[i - 1].offset;
            continue;
        }
        recordDesc->propeInfos[i].offset = allFixPropertiesLen;
        allFixPropertiesLen += propertySize;
    }
    recordDesc->fixedPropertiesLen = allFixPropertiesLen;
}

// properties为指针数组
void RecordDescInitPropeInfoForFixSizePartByPropes(RecordDescT *recordDesc, DmPropertySchemaT **properties)
{
    DmPropertySchemaT *property = NULL;
    uint32_t fixedPropertiesLen = 0;
    for (uint32_t i = 0; i < recordDesc->propeNum - RESERVED_SIZE; i++) {
        property = properties[i];
        if (property->isValid && property->dataType == DB_DATATYPE_BITMAP) {
            recordDesc->hasBitMapPrope = 1;
        }
        uint8_t originalBitfieldOffset = property->bitfieldOffset;
        property->bitfieldOffset = recordDesc->propeInfos[i].bitfieldOffset;
        uint32_t propertySize = DmGetFixSizePropeSize(property);
        property->bitfieldOffset = originalBitfieldOffset;
        if (DmIsBitFieldType(property->dataType) && propertySize == 0 && i > 0) {
            recordDesc->propeInfos[i].offset = recordDesc->propeInfos[i - 1].offset;
            continue;
        }
        recordDesc->propeInfos[i].offset = fixedPropertiesLen;
        fixedPropertiesLen += propertySize;
    }
    if (RESERVED_SIZE != 0) {
        recordDesc->propeInfos[recordDesc->propeNum - RESERVED_SIZE].offset = fixedPropertiesLen;
    }
    recordDesc->fixedPropertiesLen = fixedPropertiesLen + RESERVED_SIZE;
}

void RecordDescInitPropeInfoForVarSizePartByPropes(RecordDescT *recordDesc, DmPropertySchemaT **properties)
{
    const DmPropertySchemaT *property = NULL;
    uint32_t offset = recordDesc->fixedPropertiesLen;
    for (uint32_t i = 0; i < recordDesc->propeNum - RESERVED_SIZE; i++) {
        property = properties[i];
        if (property->isValid && DmIsFixedType(property->dataType) != true) {
            // 变长属性的offset是从realLen开始的。因此这里加上4B
            recordDesc->propeInfos[i].offset = offset + (uint32_t)sizeof(uint32_t);
            recordDesc->isFixed = false;
            uint32_t varBufLen =
                (property->size > DM_ALLOC_VAR_PROPE_LEN_IN_BUF) ?
                    (uint32_t)(DM_ALLOC_VAR_PROPE_LEN_IN_BUF + sizeof(uint32_t) + sizeof(uint32_t)) :
                    (uint32_t)(property->size + sizeof(uint32_t) + sizeof(uint32_t));  // maxLen, realLen
            offset = offset + varBufLen;
        }
    }
}

void RecordDescInitPropeInfoForVarSizePart(RecordDescT *recordDesc, const DmPropertySchemaT *properties)
{
    const DmPropertySchemaT *property = NULL;
    uint32_t offset = recordDesc->fixedPropertiesLen;
    for (uint32_t i = 0; i < recordDesc->propeNum; i++) {
        property = &properties[i];
        if (property->isValid && DmIsFixedType(property->dataType) != true) {
            recordDesc->isFixed = false;
            // 变长属性的offset是从realLen开始的。因此这里加上4B
            recordDesc->propeInfos[i].offset = offset + (uint32_t)sizeof(uint32_t);
            uint32_t varBufLen =
                (property->size > DM_ALLOC_VAR_PROPE_LEN_IN_BUF) ?
                    (uint32_t)(DM_ALLOC_VAR_PROPE_LEN_IN_BUF + sizeof(uint32_t) + sizeof(uint32_t)) :
                    (uint32_t)(property->size + sizeof(uint32_t) + sizeof(uint32_t));  // maxLen, realLen
            offset = offset + varBufLen;
        }
    }
}

void InitRecordDesc(RecordDescT *recordDesc, uint8_t **preAllocBuf)
{
    recordDesc->isFixed = true;
    recordDesc->totalBitMapNum = 0;
    // 内存已申请，这里以指针偏移方式完成分配
    recordDesc->propeInfos = (DmPropertyInfoT *)*preAllocBuf;
    *preAllocBuf += recordDesc->propeNum * sizeof(DmPropertyInfoT);
}

void RecordDescInitPropInfoDefaultValues(
    uint8_t **preAllocBuf, DmPropertySchemaT *properties, DmPropertyInfoT *propeInfo)
{
    errno_t err = EOK;
    if (propeInfo->defaultValueNum == 0) {
        propeInfo->defaultValue = NULL;
        return;
    }
    DmValueT *defaultValue = MEMBER_PTR(properties, defaultValue);
    propeInfo->defaultValue = (DmValueT *)*preAllocBuf;
    uint32_t defaultValueTotalLen = propeInfo->defaultValueNum * (uint32_t)sizeof(DmValueT);
    err = memcpy_s(propeInfo->defaultValue, defaultValueTotalLen, defaultValue, defaultValueTotalLen);
    DB_ASSERT(err == EOK);
    *preAllocBuf += defaultValueTotalLen;
    if (DM_TYPE_NEED_MALLOC(propeInfo->dataType)) {
        for (uint32_t j = 0; j < propeInfo->defaultValueNum; j++) {
            propeInfo->defaultValue[j].value.strAddr = *preAllocBuf;
            err = memcpy_s(propeInfo->defaultValue[j].value.strAddr, defaultValue[j].value.length,
                MEMBER_PTR(&defaultValue[j], value.strAddr), defaultValue[j].value.length);
            DB_ASSERT(err == EOK);
            *preAllocBuf += defaultValue[j].value.length;
        }
    }
}

void RecordDescInitDescPropeInfos(uint8_t **preAllocBuf, DmPropertySchemaT *properties, RecordDescT *recordDesc)
{
    InitRecordDesc(recordDesc, preAllocBuf);
    uint32_t propeNameLenSum = 0;
    for (uint32_t i = 0; i < recordDesc->propeNum; i++) {
        propeNameLenSum += properties[i].nameLen;
    }
    char *propeNamesBuf = (char *)*preAllocBuf;
    *preAllocBuf += propeNameLenSum;

    // 处理定长属性和变长属性，初始化propeInfo中的部分信息
    // 因为offset的计算需要先处理定长字段后处理变长字段（依赖当前的序列化算法），因此放在后面两个循环中计算
    // 当前list属性在record被当成普通属性处理了,在runningbuf中分配了空间,后续如果需要修改，要同步考虑offset、fixedPropertiesLen等信息
    for (uint32_t i = 0; i < recordDesc->propeNum; i++) {
        DmPropertyInfoT *propeInfo = &(recordDesc->propeInfos[i]);
        propeInfo->isValid = properties[i].isValid;
        if (properties[i].isValid) {
            char *name = MEMBER_PTR(&properties[i], name);
            errno_t err = memcpy_s(propeNamesBuf, properties[i].nameLen, name, properties[i].nameLen);
            DB_ASSERT(err == EOK);
            propeInfo->propeName = propeNamesBuf;
            propeNamesBuf += properties[i].nameLen;
            propeInfo->nameLen = properties[i].nameLen;
            propeInfo->propeId = i;
            propeInfo->dataType = properties[i].dataType;
            if (properties[i].dataType == DB_DATATYPE_BITMAP) {
                recordDesc->totalBitMapNum++;
                propeInfo->propeMaxLen = properties[i].size / BYTE_LENGTH;
            } else {
                propeInfo->propeMaxLen = properties[i].size;
            }
            propeInfo->isNullable = properties[i].isNullable;
            propeInfo->isAutoIncPrope = properties[i].isAutoIncProp;
            propeInfo->isSysPrope = properties[i].isSysPrope;
            propeInfo->isSensitive = properties[i].isSensitive;
            propeInfo->isFixed = properties[i].isFixed;
            propeInfo->defaultValueNum = properties[i].defaultValueNum;
            propeInfo->npaIndex = properties[i].npaIndex;
            RecordDescInitPropInfoDefaultValues(preAllocBuf, &properties[i], propeInfo);
        }
    }
    recordDesc->bitmapUpdateInfoLen = recordDesc->totalBitMapNum * DM_UPDATA_BITMAP_LEN;
    // 处理定长属性：计算offset & 初始化位域属性信息
    RecordDescInitPropeInfoForFixSizePart(recordDesc, properties);
    // 处理变长属性：计算offset
    RecordDescInitPropeInfoForVarSizePart(recordDesc, properties);
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
void InitReservedPropeInfo(RecordDescT *recordDesc, char **propeNamesBuf)
{
    DmPropertyInfoT *propertyInfo = &(recordDesc->propeInfos[recordDesc->propeNum - RESERVED_SIZE]);

    propertyInfo->propeMaxLen = RESERVED_SIZE;
    propertyInfo->isFixed = true;
    propertyInfo->isSysPrope = true;
    DmSysPropeTypeE sysPropeType = CHECK_VERSION;
    DmSysPropeInfoT sysPropeInfo;
    DmGetSysPropeInfoByType(&sysPropeInfo, sysPropeType);
    propertyInfo->dataType = sysPropeInfo.type;
    propertyInfo->nameLen = (uint16_t)strlen(SYS_PROPE_CHECK_VERSION_NAME);
    errno_t err = memcpy_s(*propeNamesBuf, propertyInfo->nameLen, SYS_PROPE_CHECK_VERSION_NAME, propertyInfo->nameLen);
    DB_ASSERT(err == EOK);
    propertyInfo->propeName = *propeNamesBuf;
    *propeNamesBuf += propertyInfo->nameLen;
    propertyInfo->propeId = recordDesc->propeNum - RESERVED_SIZE;
    propertyInfo->isValid = true;
    propertyInfo->isNullable = true;
    propertyInfo->isAutoIncPrope = false;
    propertyInfo->isSensitive = false;
}

static void InitSinglePropeInfosByPropes(DmPropertySchemaT *properties, RecordDescT *recordDesc, uint32_t propeId,
    DmPropertyInfoT *propertyInfo, char **propeNamesBuf)
{
    if (properties->nameLen != 0) {
        errno_t err = memcpy_s(*propeNamesBuf, properties->nameLen, properties->name, properties->nameLen);
        DB_ASSERT(err == EOK);
        propertyInfo->propeName = *propeNamesBuf;
        *propeNamesBuf += properties->nameLen;
    } else {
        properties->name = NULL;
    }
    propertyInfo->nameLen = properties->nameLen;
    propertyInfo->propeId = propeId;
    propertyInfo->dataType = properties->dataType;
    if (properties->dataType == DB_DATATYPE_BITMAP) {
        recordDesc->totalBitMapNum++;
        propertyInfo->propeMaxLen = properties->size / BYTE_LENGTH;
    } else {
        propertyInfo->propeMaxLen = properties->size;
    }
    propertyInfo->isNullable = properties->isNullable;
    propertyInfo->isAutoIncPrope = properties->isAutoIncProp;
    propertyInfo->isSysPrope = properties->isSysPrope;
    propertyInfo->isSensitive = properties->isSensitive;
}

// 仅限于datalog和sql接口调用，表类型为VERTEX_TYPE_NORMAL，propes中的属性要在同一层。
void RecordDescInitDescPropeInfosByPropes(
    uint8_t **preAllocBuf, DmPropertySchemaT **properties, RecordDescT *recordDesc)
{
    InitRecordDesc(recordDesc, preAllocBuf);
    uint32_t propeNameTotalLen = 0;
    for (uint32_t i = 0; i < recordDesc->propeNum - RESERVED_SIZE; i++) {
        propeNameTotalLen += properties[i]->nameLen;
    }
    propeNameTotalLen += recordDesc->reservedNum == 0 ? 0 : (uint32_t)strlen(SYS_PROPE_CHECK_VERSION_NAME);
    char *propeNamesBuf = (char *)*preAllocBuf;
    *preAllocBuf += propeNameTotalLen;
    uint8_t newBitfieldOffset = 0;
    // 处理定长属性和变长属性，初始化propeInfo中的部分信息
    // 因为offset的计算需要先处理定长字段后处理变长字段（依赖当前的序列化算法），因此放在后面两个循环中计算
    // 当前list属性在record被当成普通属性处理了,在runningbuf中分配了空间,后续如果需要修改，要同步考虑offset、fixedPropertiesLen等信息
    for (uint32_t i = 0; i < recordDesc->propeNum - RESERVED_SIZE; i++) {
        DmPropertyInfoT *propertyInfo = &(recordDesc->propeInfos[i]);
        if (!(propertyInfo->isValid = properties[i]->isValid)) {
            continue;
        }
        InitSinglePropeInfosByPropes(properties[i], recordDesc, i, propertyInfo, &propeNamesBuf);
        propertyInfo->bitfieldOffset = 0;
        if (DmIsBitFieldType(propertyInfo->dataType)) {
            if (i == 0 || propertyInfo->dataType != properties[i - 1]->dataType ||
                newBitfieldOffset + properties[i]->size >
                    DmGetBasicDataTypeLength(properties[i]->dataType) * BYTE_LENGTH) {
                newBitfieldOffset = 0;
            }
            propertyInfo->bitfieldOffset = newBitfieldOffset;
            newBitfieldOffset += properties[i]->size;
        } else {
            newBitfieldOffset = 0;
        }
    }
    if (recordDesc->reservedNum != 0) {
        InitReservedPropeInfo(recordDesc, &propeNamesBuf);
    }
    DB_ASSERT(propeNamesBuf == (char *)*preAllocBuf);
    recordDesc->bitmapUpdateInfoLen = recordDesc->totalBitMapNum * DM_UPDATA_BITMAP_LEN;
    // 处理定长属性：计算offset & 初始化位域属性信息
    RecordDescInitPropeInfoForFixSizePartByPropes(recordDesc, properties);
    // 处理变长属性：计算offset
    RecordDescInitPropeInfoForVarSizePartByPropes(recordDesc, properties);
}

void InitDescSuperFieldBeginAndEndPropeNames(RecordDescT *recordDesc, DmSuperFieldInfoT *info, DmSchemaT *schema,
    DmPropertySchemaT *properties, DmSuperFieldT *superField)
{
    uint32_t propeBegin = 0;
    bool visitBegin = false;
    char *beginPropeName = MEMBER_PTR(superField, beginPropeName);
    char *endPropeName = MEMBER_PTR(superField, endPropeName);
    for (uint32_t j = 0; j < schema->propeNum; j++) {
        char *name = MEMBER_PTR(&properties[j], name);
        // 先比较length，不相等可直接略过，不用调用memcmp，节省函数开销
        if (properties[j].nameLen == DM_STR_LEN(beginPropeName) &&
            memcmp(name, beginPropeName, properties[j].nameLen) == 0) {
            info->beginPropeId = j;
            info->offset = recordDesc->propeInfos[j].offset;
            propeBegin = j;
            visitBegin = true;
        }
        if (visitBegin && properties[j].isValid && properties[j].dataType == DB_DATATYPE_BITMAP) {
            info->hasBitMap = true;
        }
        if (properties[j].nameLen == DM_STR_LEN(endPropeName) &&
            memcmp(name, endPropeName, properties[j].nameLen) == 0) {
            info->length = (recordDesc->propeInfos[j].offset - info->offset) + recordDesc->propeInfos[j].propeMaxLen;
            info->propeNum = (j - propeBegin) + 1;
            break;
        }
    }
}

void InitDescSuperFieldNames(RecordDescT *recordDesc, DmSuperFieldT *superFields, char *superFieldNameBuf,
    DmSchemaT *schema, DmPropertySchemaT *properties)
{
    for (uint32_t i = 0; i < recordDesc->superFieldNum; i++) {
        char *superFieldsName = MEMBER_PTR(&superFields[i], name);
        uint32_t nameLen = DM_STR_LEN(superFieldsName);
        errno_t err = memcpy_s(superFieldNameBuf, nameLen, superFieldsName, nameLen);
        DB_ASSERT(err == EOK);
        DmSuperFieldInfoT *info = &recordDesc->superFieldInfos[i];
        info->superFieldName = superFieldNameBuf;
        superFieldNameBuf += nameLen;
        info->nameLen = nameLen;
        info->superFieldId = superFields[i].id;
        InitDescSuperFieldBeginAndEndPropeNames(recordDesc, info, schema, properties, &superFields[i]);
    }
}

void InitDescSuperFieldInfos(
    uint8_t **preAllocBuf, DmSchemaT *schema, RecordDescT *recordDesc, DmPropertySchemaT *properties)
{
    recordDesc->superFieldNum = schema->superFieldNum;
    if (recordDesc->superFieldNum == 0) {
        recordDesc->superFieldInfos = (DmSuperFieldInfoT *)*preAllocBuf;
        return;
    }
    DmSuperFieldT *superFields = MEMBER_PTR(schema, superFields);
    recordDesc->superFieldInfos = (DmSuperFieldInfoT *)*preAllocBuf;
    *preAllocBuf += recordDesc->superFieldNum * sizeof(DmSuperFieldInfoT);
    uint32_t superFieldNameLen = 0;
    for (uint32_t i = 0; i < recordDesc->superFieldNum; i++) {
        char *superFieldsName = MEMBER_PTR(&superFields[i], name);
        superFieldNameLen += DM_STR_LEN(superFieldsName);
    }
    char *superFieldNameBuf = (char *)*preAllocBuf;
    *preAllocBuf += superFieldNameLen;

    InitDescSuperFieldNames(recordDesc, superFields, superFieldNameBuf, schema, properties);
}

Status DescProcessPropertyDefaultValue(RecordDescT *recordDesc, DmPropertySchemaT *properties)
{
    DB_POINTER(recordDesc);
    Status ret;
    DmValueT value;
    DmValueT *defaultValue = NULL;
    for (uint32_t i = 0; i < recordDesc->propeNum; i++) {
        if (!properties[i].isValid || properties[i].defaultValueNum == 0) {
            continue;
        }
        defaultValue = MEMBER_PTR(&properties[i], defaultValue);
        value = *defaultValue;
        if (DM_TYPE_NEED_MALLOC(defaultValue->type)) {
            value.value.strAddr = MEMBER_PTR(defaultValue, value.strAddr);
        }
        recordDesc->hasDefaultValue = true;
        uint8_t *bufCursor = recordDesc->defaultValueBuf + recordDesc->propeInfos[i].offset;
        if (DmIsBitFieldType(properties[i].dataType)) {
            DmValueT bitFieldValue = value;
            // 因为该函数对bitFieldValue有副作用，故不能直接传入defaultValue，否则会修改元数据中的默认值
            ret = GetMergedBitFieldPropeValue(bufCursor, &recordDesc->propeInfos[i], &bitFieldValue);
            if (ret != GMERR_OK) {
                return ret;
            }
            ret = SetPropeValueIntoBuf(
                recordDesc->memCtx, recordDesc->propeInfos[i].propeMaxLen, &bitFieldValue, bufCursor);
        } else {
            ret = SetPropeValueIntoBuf(recordDesc->memCtx, recordDesc->propeInfos[i].propeMaxLen, &value, bufCursor);
        }
        if (ret != GMERR_OK) {
            return ret;
        }
        recordDesc->defaultValueNullInfo[i] = DM_PROPERTY_IS_NOT_NULL;
        if ((defaultValue->type == DB_DATATYPE_STRING || defaultValue->type == DB_DATATYPE_BYTES) &&
            defaultValue->value.length > DM_ALLOC_VAR_PROPE_LEN_IN_BUF) {
            recordDesc->hasLongDefault = 1;
        }
    }
    return GMERR_OK;
}

// 仅限于datalog和sql接口调用，表类型为VERTEX_TYPE_NORMAL，propes中的属性要在同一层。
Status DescProcessPropertyDefaultValueByPropes(RecordDescT *recordDesc, DmPropertySchemaT **properties)
{
    DB_POINTER(recordDesc);
    Status ret;
    DmValueT *defaultValue = NULL;
    for (uint32_t i = 0; i < recordDesc->propeNum - RESERVED_SIZE; i++) {
        if (!properties[i]->isValid || properties[i]->defaultValue == NULL) {
            continue;
        }
        defaultValue = properties[i]->defaultValue;
        recordDesc->hasDefaultValue = true;
        uint8_t *bufCursor = recordDesc->defaultValueBuf + recordDesc->propeInfos[i].offset;
        if (DmIsBitFieldType(properties[i]->dataType)) {
            DmValueT bitFieldValue = *defaultValue;
            // 因为该函数对bitFieldValue有副作用，故不能直接传入defaultValue，否则会修改元数据中的默认值
            ret = GetMergedBitFieldPropeValue(bufCursor, &recordDesc->propeInfos[i], &bitFieldValue);
            if (ret != GMERR_OK) {
                return ret;
            }
            ret = SetPropeValueIntoBuf(
                recordDesc->memCtx, recordDesc->propeInfos[i].propeMaxLen, &bitFieldValue, bufCursor);
        } else {
            ret = SetPropeValueIntoBuf(
                recordDesc->memCtx, recordDesc->propeInfos[i].propeMaxLen, defaultValue, bufCursor);
        }
        if (ret != GMERR_OK) {
            return ret;
        }
        recordDesc->defaultValueNullInfo[i] = DM_PROPERTY_IS_NOT_NULL;
        if ((defaultValue->type == DB_DATATYPE_STRING || defaultValue->type == DB_DATATYPE_BYTES) &&
            defaultValue->value.length > DM_ALLOC_VAR_PROPE_LEN_IN_BUF) {
            recordDesc->hasLongDefault = 1;
        }
    }
    return GMERR_OK;
}

// 仅限于datalog和sql接口调用，表类型为VERTEX_TYPE_NORMAL，propes中的属性要在同一层。
static Status InitRecordDescDefaultInfoByPropes(
    DmRecordTypeE recordType, uint8_t **preAllocBuf, RecordDescT *recordDesc, DmPropertySchemaT **properties)
{
    Status ret = GMERR_OK;
    if (recordDesc->runningBufLen != 0) {
        recordDesc->defaultValueBuf = (uint8_t *)*preAllocBuf;
        *preAllocBuf += recordDesc->runningBufLen;
        recordDesc->defaultValueNullInfo = (uint8_t *)*preAllocBuf;
        *preAllocBuf += recordDesc->propeNum * sizeof(uint8_t);
    }

    (void)memset_s(recordDesc->defaultValueBuf, recordDesc->runningBufLen, 0x00, recordDesc->runningBufLen);
    (void)memset_s(recordDesc->defaultValueNullInfo, recordDesc->propeNum, 0x00, recordDesc->propeNum);
    for (uint32_t i = 0; i < recordDesc->propeNum - RESERVED_SIZE; i++) {
        DB_ASSERT(properties[i]->isValid);
        if (properties[i]->defaultValue != NULL) {
            recordDesc->hasDefaultValue = true;
            break;
        }
    }

    // 如果vertex中的record里没有普通属性，全是子树节点，此时runningBufLen为0，不用默认值相关的处理
    if (recordDesc->runningBufLen != 0) {
        ret = DescProcessPropertyDefaultValueByPropes(recordDesc, properties);
    }
    return ret;
}

void DescProcessVarPropeMaxSize(RecordDescT *desc)
{
    uint8_t *bufCursor = NULL;
    for (uint32_t i = 0; i < desc->propeNum; i++) {
        if (DmIsFixedType(desc->propeInfos[i].dataType) == false) {
            bufCursor = desc->defaultValueBuf + desc->propeInfos[i].offset - sizeof(uint32_t);
            uint32_t maxLen = desc->propeInfos[i].propeMaxLen;
            *((uint32_t *)bufCursor) = maxLen > DM_ALLOC_VAR_PROPE_LEN_IN_BUF ? DM_ALLOC_VAR_PROPE_LEN_IN_BUF : maxLen;
        }
    }
}
#else
void DescProcessVarPropeMaxSize(RecordDescT *desc)
{
    DB_ASSERT(false);
}

void InitDescSuperFieldInfos(
    uint8_t **preAllocBuf, DmSchemaT *schema, RecordDescT *recordDesc, DmPropertySchemaT *properties)
{
    return;
}

Status DescProcessPropertyDefaultValue(RecordDescT *recordDesc, DmPropertySchemaT *properties)
{
    return GMERR_OK;
}
#endif /* FEATURE_SIMPLEREL */

static Status InitRecordDescDefaultInfo(DmSchemaT *schema, DmRecordTypeE recordType, uint8_t **preAllocBuf,
    RecordDescT *recordDesc, DmPropertySchemaT *properties)
{
    Status ret = GMERR_OK;
    if (recordDesc->runningBufLen != 0) {
        recordDesc->defaultValueBuf = (uint8_t *)*preAllocBuf;
        *preAllocBuf += recordDesc->runningBufLen;
        recordDesc->defaultValueNullInfo = (uint8_t *)*preAllocBuf;
        *preAllocBuf += recordDesc->propeNum * sizeof(uint8_t);
    }

    (void)memset_s(recordDesc->defaultValueBuf, recordDesc->runningBufLen, 0x00, recordDesc->runningBufLen);
    (void)memset_s(recordDesc->defaultValueNullInfo, recordDesc->propeNum, 0x00, recordDesc->propeNum);
    for (uint32_t i = 0; i < recordDesc->propeNum; i++) {
        if (properties[i].isValid && properties[i].defaultValueOffset != 0) {
            recordDesc->hasDefaultValue = true;
            break;
        }
    }

    // Yang场景下，默认值不落存储，这里不用把默认值存储到defaultValueBuf中
    if (DmIsYangSchema(schema)) {
        return ret;
    }

    // 如果vertex中的record里没有普通属性，全是子树节点，此时runningBufLen为0，不用默认值相关的处理
    if (recordDesc->runningBufLen != 0) {
        ret = DescProcessPropertyDefaultValue(recordDesc, properties);
    }
    return ret;
}

void InitRecordDescNotNullPropeInfo(uint8_t **preAllocBuf, RecordDescT *recordDesc)
{
    DmPropertyInfoT *propeInfos = recordDesc->propeInfos;
    // 内存已申请，这里以指针偏移方式完成分配
    recordDesc->notNullPropeIds = (uint16_t *)*preAllocBuf;
    *preAllocBuf += recordDesc->propeNum * sizeof(uint16_t);
    uint16_t notNullPropeNum = 0;
    for (uint32_t i = 0; i < recordDesc->propeNum; i++) {
        // 自增列默认nullable为true，但当自增列为主键属性时，nullabel为false。
        // 自增列不允许用户设值，即自增列属性在自增前为空也是合法的，所以这里的notNullPropeIds不包含自增列属性id，
        if (propeInfos[i].isValid && !propeInfos[i].isNullable && !propeInfos[i].isAutoIncPrope) {
            recordDesc->notNullPropeIds[notNullPropeNum++] = (uint16_t)i;
        }
    }
    recordDesc->notNullPropeNum = notNullPropeNum;
}

Status InitRecordDescInner(DmRecordTypeE recordType, uint8_t **preAllocBuf, DmPropertySchemaT *properties,
    RecordDescT **recordDesc, DmSchemaT *schema)
{
    RecordDescInitDescPropeInfos(preAllocBuf, properties, *recordDesc);     // propeInfo, fixedPropertiesLen
    InitDescSuperFieldInfos(preAllocBuf, schema, *recordDesc, properties);  // superFieldNum, superFieldInfo
    Status ret = InitRecordDescDefaultInfo(schema, recordType, preAllocBuf, *recordDesc, properties);
    if (ret != GMERR_OK) {
        DestroyRecordDesc(*recordDesc);
        *recordDesc = NULL;
        return ret;
    }
    return GMERR_OK;
}

Status CreateRecordDesc(DbMemCtxT *memCtx, DmRecordTypeE recordType, DmSchemaT *schema, RecordDescT **recordDesc)
{
    DmPropertySchemaT *properties = MEMBER_PTR(schema, properties);
    // 计算创建当前静态record所需申请内存的大小
    uint32_t runningBufLen = 0;
    uint32_t memSize = GetRecordDescSize4PreMalloc(schema, recordType, &runningBufLen, properties);
    // 预先申请内存，并以指针偏移方式在后面流程中分配内存
    // 内存释放逻辑：DmDestroyVertexDesc
    uint8_t *totalBuf = (uint8_t *)DbDynMemCtxAlloc(memCtx, memSize);
    if (totalBuf == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Buf for recordDesc, size %" PRIu32 ".", memSize);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(totalBuf, memSize, 0x00, memSize);
    uint8_t *preAllocBuf = totalBuf;
    *recordDesc = (RecordDescT *)preAllocBuf;
    preAllocBuf += sizeof(RecordDescT);
    (*recordDesc)->runningBufLen = runningBufLen;
    (*recordDesc)->memCtx = memCtx;
    (*recordDesc)->recordType = recordType;
    (*recordDesc)->propeNum = GetPropeNumInDesc(schema);
    (*recordDesc)->propOpTypesLen =
        RecordIsYangType(*recordDesc) ? (*recordDesc)->propeNum * (uint32_t)sizeof(uint8_t) : 0;
    (*recordDesc)->reservedNum = schema->sysPropeNum == 0 ? 0 : RESERVED_SIZE;
    Status ret = InitRecordDescInner(recordType, &preAllocBuf, properties, recordDesc, schema);
    if (ret != GMERR_OK) {
        return ret;
    }

    if ((*recordDesc)->isFixed != 1) {
        DescProcessVarPropeMaxSize(*recordDesc);
    }
    // 序列化性能优化，创建时全置为1，序列化时与propeIsSetValue比较判断是否有空属性
    (*recordDesc)->propeIsSetValueAllSet = (int8_t *)preAllocBuf;
    preAllocBuf += (*recordDesc)->propeNum * sizeof(int8_t);
    (void)memset_s((*recordDesc)->propeIsSetValueAllSet, (*recordDesc)->propeNum, DM_PROPERTY_IS_NOT_NULL,
        (*recordDesc)->propeNum);

    // Not Null约束检查性能优化，Not Null属性id在Record创建时就保存到notNullPropeIds数组中
    InitRecordDescNotNullPropeInfo(&preAllocBuf, *recordDesc);
    (*recordDesc)->memSizePreMalloc = GetEmptyRecordSize4PreMalloc(*recordDesc, recordType);
    if (memSize != (uintptr_t)preAllocBuf - (uintptr_t)totalBuf) {
        DestroyRecordDesc(*recordDesc);
        *recordDesc = NULL;
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR,
            "Size of alloc not match, Mem size: %" PRIu32 ", computed size: %" PRIu32 ".", memSize,
            (uint32_t)((uintptr_t)preAllocBuf - (uintptr_t)totalBuf));
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
Status InitDescInfosByPropes(
    DmRecordTypeE recordType, uint8_t **preAllocBuf, RecordDescT **recordDesc, DmPropertySchemaT **properties)
{
    // propeNum, propeInfo, fixedPropertiesLen
    RecordDescInitDescPropeInfosByPropes(preAllocBuf, properties, *recordDesc);
    Status ret = InitRecordDescDefaultInfoByPropes(recordType, preAllocBuf, *recordDesc, properties);
    if (ret != GMERR_OK) {
        DestroyRecordDesc(*recordDesc);
        *recordDesc = NULL;
        return ret;
    }
    if ((*recordDesc)->isFixed != 1) {
        DescProcessVarPropeMaxSize(*recordDesc);
    }
    return GMERR_OK;
}

// 仅限于datalog和sql接口调用，表类型为VERTEX_TYPE_NORMAL，propes中的属性要在同一层。
// sql 场景的 RESERVED_SIZE 为 0
Status CreateRecordDescByPropes(
    DbMemCtxT *memCtx, DmPropertySchemaT **properties, RecordDescT **recordDesc, uint32_t propeNum)
{
    // 计算创建当前静态record所需申请内存的大小
    uint32_t runningBufLen = 0;
    uint32_t memSize = GetRecordDescSize4PreMallocByPropes(&runningBufLen, properties, propeNum + RESERVED_SIZE);
    // 预先申请内存，并以指针偏移方式在后面流程中分配内存
    // 内存释放逻辑：DmDestroyVertexDesc
    uint8_t *totalBuf = (uint8_t *)DbDynMemCtxAlloc(memCtx, memSize);
    if (totalBuf == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Buf for record desc, size %" PRIu32 ".", memSize);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(totalBuf, memSize, 0x00, memSize);
    uint8_t *preAllocBuf = totalBuf;
    *recordDesc = (RecordDescT *)preAllocBuf;
    preAllocBuf += sizeof(RecordDescT);

    DmRecordTypeE recordType = DM_RECORD_IN_VERTEX;
    (*recordDesc)->runningBufLen = runningBufLen;
    (*recordDesc)->memCtx = memCtx;
    (*recordDesc)->recordType = recordType;
    (*recordDesc)->propeNum = propeNum + RESERVED_SIZE;
    (*recordDesc)->reservedNum = RESERVED_SIZE;
    (*recordDesc)->propOpTypesLen = 0;

    Status ret = InitDescInfosByPropes(recordType, &preAllocBuf, recordDesc, properties);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 序列化性能优化，创建时全置为1，序列化时与propeIsSetValue比较判断是否有空属性
    (*recordDesc)->propeIsSetValueAllSet = (int8_t *)preAllocBuf;
    preAllocBuf += (*recordDesc)->propeNum * sizeof(int8_t);
    (void)memset_s((*recordDesc)->propeIsSetValueAllSet, (*recordDesc)->propeNum, DM_PROPERTY_IS_NOT_NULL,
        (*recordDesc)->propeNum);

    // Not Null约束检查性能优化，Not Null属性id在Record创建时就保存到notNullPropeIds数组中
    InitRecordDescNotNullPropeInfo(&preAllocBuf, *recordDesc);
    (*recordDesc)->memSizePreMalloc = GetEmptyRecordSize4PreMalloc(*recordDesc, recordType);
    if (memSize != (uintptr_t)preAllocBuf - (uintptr_t)totalBuf) {
        DestroyRecordDesc(*recordDesc);
        *recordDesc = NULL;
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INTERNAL_ERROR,
            "Size of malloc not match, Memory size %" PRIu32 ", computed size %" PRIu32 ".", memSize,
            (uint32_t)((uintptr_t)preAllocBuf - (uintptr_t)totalBuf));
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

void FreeRecordDescLongVarProp(RecordDescT *recordDesc)
{
    uint8_t *bufCursor = recordDesc->defaultValueBuf;
    for (uint32_t i = 0; i < recordDesc->propeNum; i++) {
        if (DmIsFixedType(recordDesc->propeInfos[i].dataType)) {
            continue;
        }
        bufCursor = recordDesc->defaultValueBuf + recordDesc->propeInfos[i].offset;
        if (*((uint32_t *)bufCursor) > DM_ALLOC_VAR_PROPE_LEN_IN_BUF) {  // 此种情况下需要释放之前ptr指向的内存
            uintptr_t ptrValue = (uintptr_t) * ((uint64_t *)(bufCursor + sizeof(uint32_t)));
            if ((uint8_t *)ptrValue != NULL) {
                DbDynMemCtxFree(recordDesc->memCtx, (uint8_t *)ptrValue);
                *((uint32_t *)bufCursor) = 0;  // 将realLen = 0写进buf中去;
            }
        }
    }
}
#else
void FreeRecordDescLongVarProp(RecordDescT *recordDesc)
{
    DB_ASSERT(false);
}
#endif /* FEATURE_SIMPLEREL */

void DestroyRecordDesc(RecordDescT *recordDesc)
{
    if (recordDesc == NULL) {
        return;
    }
    if (recordDesc->hasLongDefault == 1) {
        FreeRecordDescLongVarProp(recordDesc);
    }
    DbDynMemCtxFree(recordDesc->memCtx, recordDesc);
}

void RecordDescInitResColInfo(RecordDescT *recordDesc, VertexLabelCommonInfoT *commonInfo)
{
    if (commonInfo->resColInfoOffset == 0) {
        return;
    }
    DmResColInfoT *resColInfo = MEMBER_PTR(commonInfo, resColInfo);
    for (uint32_t i = 0; i < resColInfo->resColCount; i++) {
        recordDesc->propeInfos[resColInfo->resPropeId[i]].isResCol = true;
    }
}
