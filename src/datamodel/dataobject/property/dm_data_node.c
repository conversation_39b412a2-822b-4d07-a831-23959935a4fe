/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * Description: source file for data model Node
 * Author: z<PERSON><PERSON><PERSON>i
 * Create: 2020-10-18
 */

#include <string.h>
#include "dm_data_keybuf_opti.h"
#include "db_hashmap.h"
#include "dm_data_node_inner.h"
#include "dm_data_index.h"
#include "dm_data_strudefs.h"
#include "dm_data_tree.h"

#include "dm_yang_union.h"
#include "dm_yang_interface.h"
#include "dm_log.h"

#define DM_VECTOR_NODE_EXTEND_SPEED 2

typedef enum VectorScaling {
    VECTOR_EXPAND = 0,  // 扩容
    VECTOR_SHRINK,      // 缩容
} VectorScalingE;

/**
 * @brief Create a record or array node inner
 * @param node record或者array节点
 * @param maxElementNum 元素个数
 * @return Status 成功或错误码
 */
Status CreateRecordOrArrayNodeInner(DmNodeT *node, uint32_t maxElementNum);

/**
 * @brief Create a empty vector node
 * @param node vector节点
 * @return Status 成功或错误码
 */
Status CreateEmptyVectorNode(DmNodeT *node);

/**
 * @brief 对Node节点里StartIndex到EndIndex的元素中的RunningBuf进行初始化，主要包括默认值和变长属性最大值的处理
 * @param node Node节点
 * @param startIndex 待初始化的元素起始index（包含该元素）
 * @param endIndex 待初始化的元素结束index（不包含该元素）
 * @return Status 成功或错误码
 */
Status NodeRunningBufInit(DmNodeT *node, uint32_t startIndex, uint32_t endIndex, bool withDefault);

// 创建node的子节点，有副作用，如果失败，需要调用者在CreateChildNodes外调用DestroyNode，释放node及其子节点空间
Status CreateChildNodes(DmNodeT *node);

void FreeNodeInnerMemory(DmNodeT *node);

/**
 * @brief Vector型子树节点针对非普通属性的扩容
 * @param node 待扩容的Vector型Node
 * @param newElementNum 扩容后的元素个数
 * @return Status 成功或错误码
 */
Status VectorElementNodeExpand(DmNodeT *node, uint32_t newElementNum);

/**
 * @brief Vector型子树节点针对非普通属性的缩容
 * @param node 待缩容的Vector型Node
 * @param newElementNum 缩容后的元素个数
 * @return Status 成功或错误码
 */
Status VectorElementNodeShrink(DmNodeT *node, uint32_t newElementNum);

/**
 * @brief Vector型子树节点的缩容
 * @param node 待缩容的Vector型Node
 * @return Status 成功或错误码
 */
Status VectorElementShrink(DmNodeT *node);

/**
 * @brief 对数组节点进行排序（选择排序，非稳定排序）
 * 首先将属性值为null的element排在最前面
 * 之后将剩下的element记录按照选择排序算法进行排序
 * @param node node节点
 * @param values 基于values进行排序
 * @param valuesNum values数组的个数
 * @param isAscending true表示升序，false表示降序
 * @return Status 成功或错误码
 */
Status NodeSortInner(const DmNodeT *node, DmValueT *values, uint32_t valuesNum, bool isAscending);

/**
 * @brief 交换数组节点中prevIdx和extremeIdx处的element记录
 * @param node node节点
 * @param bufTmp 提前申请好的buf内存，用于record中的runningBuf交换时的拷贝
 * @param nullInfoTmp 提前申请好的nullInfo内存，用于record中的properIsSetValue交换时的拷贝
 * @param prevIdx 需要交换的第一个element在数组中的位置
 * @param extremeIdx 需要交换的第二个element在数组中的位置
 * @return void
 */
void ElementSwapForNodeSort(
    const DmNodeT *node, uint8_t *bufTmp, int8_t *nullInfoTmp, uint32_t prevIdx, uint32_t extremeIdx);

/**
 * @brief 将数组节点中指定排序属性值为null的元素都移到数组的最前面（按照null值在原数组中的顺序）
 * @param node node节点
 * @param bufTmp 提前申请好的buf内存，用于record中的runningBuf交换时的拷贝
 * @param nullInfoTmp 提前申请好的nullInfo内存，用于record中的properIsSetValue交换时的拷贝
 * @param values 排序属性值数组，从数组节点的元素记录中根据属性名获取出来，用以排序时的比较
 * @param nullValueCount 出参，返回指定属性值为null的元素个数
 * @return void
 */
void ProcessNullForNodeSort(
    const DmNodeT *node, uint8_t *bufTmp, int8_t *nullInfoTmp, DmValueT *values, uint32_t *nullValueCount);

/**
 * @brief 从数组节点的element记录中抽取排序属性的属性值
 * @param node node节点
 * @param propeName 属性名
 * @param values 排序的属性值
 * @param valuesNum 属性值数组的个数
 * @return Status 成功或错误码
 */
Status ExtractSortPropertyValues(DmNodeT *node, const char *propeName, DmValueT *values, uint32_t valuesNum);

/**
 * @brief 初始化map时的比较函数，用来比较两个key
 * @param key1 待比较的第一个key
 * @param key2 待比较的第二个key
 * @return uint32_t 与原先Oamap中compare函数保持一致
 */
uint32_t OamapKeyBufCompare(const void *key1, const void *key2);

inline static void InitNode(DbMemCtxT *memCtx, DmNodeT *node, DmNodeDescT *nodeDesc)
{
    node->memCtx = memCtx;
    node->nodeDesc = nodeDesc;
    node->opNum = 0;
    node->deltaOpType = DM_VECTOR_UPDATE;  // 如果是新创建的delta节点，是可以添加任何操作码的，因此需要预先置位UPDATE
    node->isClear = false;
    node->isCreated = false;
    node->headMagicCode = DM_RUNNING_MAGIC_CODE;
    node->tailMagicCode = DM_RUNNING_MAGIC_CODE;
}

// 为节点申请opArray数组
Status InitNodeOpArray(DmNodeT *node)
{
    node->maxOpNum = DM_DELTA_UPDATE_INIT_OPERATIONS;
    uint32_t memSize = (uint32_t)(sizeof(uint16_t) * node->maxOpNum);
    // 内存释放逻辑：DmDestroyVertex
    node->opArray = (uint16_t *)DbDynMemCtxAlloc(node->memCtx, memSize);
    if (SECUREC_UNLIKELY(node->opArray == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Init node opArray size:%" PRIu32 "", memSize);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(node->opArray, memSize, 0x00, memSize);
    return GMERR_OK;
}

Status DmCreateEmptyNode(DbMemCtxT *memCtx, DmNodeDescT *nodeDesc, DmNodeT **node, bool needCreated)
{
    if (*node != NULL || !needCreated) {
        // node已存在，或者当前为按需创建场景的初始化，不进行node的创建，直接返回。
        return GMERR_OK;
    }
    uint32_t totalLen = (uint32_t)sizeof(DmNodeT) + nodeDesc->maxKeyLen;  // node结构体内存+keybuf内存
    // 内存释放逻辑：DmDestroyVertex
    // 异常分支释放：EXIT分支中的DestroyNode
    uint8_t *totalBuf = (uint8_t *)DbDynMemCtxAlloc(memCtx, totalLen);
    if (SECUREC_UNLIKELY(totalBuf == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Create node, totalBuf size: %" PRIu32 ".", totalLen);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(totalBuf, totalLen, 0x00, totalLen);
    *node = (DmNodeT *)totalBuf;
    totalBuf += (uint32_t)sizeof(DmNodeT);
    InitNode(memCtx, *node, nodeDesc);
    DmRecordTypeE type = DmIsYangNodeDesc(nodeDesc) ? DM_RECORD_IN_YANG_NODE : DM_RECORD_IN_NODE;
    Status ret = CreateEmptyRecord(memCtx, type, nodeDesc->recordDesc, &((*node)->currRecord));
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT;
    }
    if (nodeDesc->nodeType == DM_NODE_VECTOR) {
        ret = CreateEmptyVectorNode(*node);
    } else {
        ret = CreateRecordOrArrayNodeInner(*node, nodeDesc->maxElementNum);
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT;
    }

    if (nodeDesc->indexNum != 0) {
        // keyLen 如果为0默认不申请空间
        if (nodeDesc->maxKeyLen > 0) {
            (*node)->nodeKeyBuf = (uint8_t *)totalBuf;
        }
    }

    ret = InitNodeOpArray(*node);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT;
    }

    ret = CreateChildNodes(*node);
EXIT:
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DestroyNode(node);
    }
    return ret;
}

Status CreateRecordOrArrayNodeInner(DmNodeT *node, uint32_t maxElementNum)
{
    node->totalElementNum = maxElementNum;
    node->realElementNum = node->totalElementNum;
    node->totalBufSize = node->totalElementNum * node->currRecord->recordDesc->runningBufLen;
    size_t arrMemSize = node->totalElementNum * sizeof(uint32_t);
    uint32_t totalPropeNum = node->currRecord->recordDesc->propeNum * node->totalElementNum;
    uint32_t bitMapUpdateSize = node->currRecord->recordDesc->bitmapUpdateInfoLen * node->realElementNum;
    // 提前申请propeIsSetValue，propeIsEmpty，runningBuf，recordSeriLenArr，updateBitMapInfoLen所需buf，以指针偏移方式进行内存分配
    size_t preAllocBufLen = totalPropeNum * sizeof(int8_t) + totalPropeNum * sizeof(int8_t) + node->totalBufSize +
                            arrMemSize + bitMapUpdateSize + node->totalElementNum * sizeof(uint8_t *) + arrMemSize;
    // 加上propeOp长度 * sizeof(int8_t)
    uint32_t totalPropOpNum = node->currRecord->recordDesc->propOpTypesLen * node->totalElementNum;
    preAllocBufLen += totalPropOpNum * sizeof(uint8_t);
    // 内存释放逻辑：DmDestroyVertex
    // 异常分支释放：FreeNodeInnerMemory
    uint8_t *preAllocBuf = (uint8_t *)DbDynMemCtxAlloc(node->memCtx, preAllocBufLen);
    if (preAllocBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Create record or arrayNode, preAlloc bufSize: %zu", preAllocBufLen);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(preAllocBuf, preAllocBufLen, 0x00, preAllocBufLen);

    node->propeIsSetValue = (int8_t *)preAllocBuf;  // int8_t数组
    preAllocBuf += totalPropeNum * sizeof(int8_t);
    node->propeIsEmpty = (int8_t *)preAllocBuf;  // int8_t数组
    preAllocBuf += totalPropeNum * sizeof(int8_t);
    node->propeOpTypes = (uint8_t *)preAllocBuf;  // int8_t数组
    preAllocBuf += totalPropOpNum * sizeof(int8_t);
    node->recordSeriLenArr = (uint32_t *)preAllocBuf;
    preAllocBuf += arrMemSize;
    node->nodeSeriBuf = (uint8_t **)preAllocBuf;
    preAllocBuf += node->totalElementNum * sizeof(uint8_t *);
    node->nodeSeriBufLen = (uint32_t *)preAllocBuf;
    preAllocBuf += arrMemSize;
    node->runningBuf = (uint8_t *)preAllocBuf;
    preAllocBuf += node->totalBufSize;
    if (bitMapUpdateSize != 0) {
        node->updateBitmapBuf = (uint8_t *)preAllocBuf;
        // 此处未做addr偏移: preAllocBuf += bitMapUpdateSize;
        (void)memset_s(node->updateBitmapBuf, bitMapUpdateSize, 0xFF, bitMapUpdateSize);
    }
    Status ret = NodeRunningBufInit(node, 0, node->totalElementNum, true);
    if (ret != GMERR_OK) {
        FreeNodeInnerMemory(node);
        return ret;
    }
    return GMERR_OK;
}

inline static void InitNodeElementNum(DmNodeT *node)
{
    node->totalElementNum = node->nodeDesc->initElementNum;
    node->realElementNum = 0;
}

uint32_t GetPreAllocSizeInVectorNode(DmNodeT *node, uint32_t elementNum)
{
    RecordDescT *recordDesc = node->currRecord->recordDesc;
    return (uint32_t)(elementNum * (recordDesc->propeNum + recordDesc->propeNum + recordDesc->propOpTypesLen +
                                       recordDesc->runningBufLen + sizeof(uint32_t) + sizeof(uint8_t *) +
                                       sizeof(uint32_t) + recordDesc->bitmapUpdateInfoLen));
}

// 内存释放逻辑：DmDestroyVertex
// 异常分支内存统一在上层去释放
Status CreateEmptyVectorNode(DmNodeT *node)
{
    InitNodeElementNum(node);
    uint32_t initElementNum = node->nodeDesc->initElementNum;
    if (initElementNum == 0) {
        return GMERR_OK;
    }
    RecordDescT *recordDesc = node->currRecord->recordDesc;

    node->maxOpNumByMemberKey = DM_DELTA_UPDATE_INIT_OPERATIONS;
    // 为节省内存，按照初始长度申请，后续随element一起扩容缩容。
    // 内存统一申请，包括propeIsSetValue，propeIsEmpty，propeOpTypes，runningBuf，recordSeriLenArr，nodeSeriBuf，
    uint32_t preAllocSize = GetPreAllocSizeInVectorNode(node, initElementNum);
    uint8_t *preAllocBuf = (uint8_t *)DmMallocAndInit(node->memCtx, preAllocSize);
    if (preAllocBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Create vector nodeBuf.");
        return GMERR_OUT_OF_MEMORY;
    }

    node->propeIsSetValue = (int8_t *)preAllocBuf;
    preAllocBuf += recordDesc->propeNum * initElementNum;

    node->propeIsEmpty = (int8_t *)preAllocBuf;
    preAllocBuf += recordDesc->propeNum * initElementNum;

    uint32_t totalPropeOpNum = recordDesc->propOpTypesLen * initElementNum;
    node->propeOpTypes = (uint8_t *)preAllocBuf;
    preAllocBuf += totalPropeOpNum;

    node->recordSeriLenArr = (uint32_t *)preAllocBuf;
    preAllocBuf += (sizeof(uint32_t) * initElementNum);
    node->nodeSeriBuf = (uint8_t **)preAllocBuf;
    preAllocBuf += (sizeof(uint8_t *) * initElementNum);
    node->nodeSeriBufLen = (uint32_t *)preAllocBuf;
    preAllocBuf += (sizeof(uint32_t) * initElementNum);

    node->totalBufSize = initElementNum * recordDesc->runningBufLen;
    node->runningBuf = (uint8_t *)preAllocBuf;
    preAllocBuf += node->totalBufSize;

    if (recordDesc->bitmapUpdateInfoLen != 0) {
        uint32_t arrMemSize = recordDesc->bitmapUpdateInfoLen * initElementNum;
        node->updateBitmapBuf = (uint8_t *)preAllocBuf;
        preAllocBuf += arrMemSize;
        (void)memset_s(node->updateBitmapBuf, arrMemSize, 0xFF, arrMemSize);
    }
    DB_ASSERT((uintptr_t)preAllocBuf - (uintptr_t)node->propeIsSetValue == preAllocSize);

    // deltaMemberKeys的扩容和缩容逻辑与上面不同，因此不能合并申请内存
    node->deltaMemberKeys = DmMallocAndInit(node->memCtx, (sizeof(DmIndexKeyT *) * node->maxOpNumByMemberKey));
    if (node->deltaMemberKeys == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Create vectorNode memKeys.");
        return GMERR_OUT_OF_MEMORY;
    }
    return NodeRunningBufInit(node, 0, initElementNum, true);
}

Status NodeNotNullPropeIsAllSet(DmNodeT *node)
{
    Status ret;
    if (!DmNodeIsCreated(node)) {
        return GMERR_OK;
    }
    // 先处理所有元素中的普通属性
    for (uint32_t i = 0; i < node->realElementNum; i++) {
        NodeSetElementIndexWithoutCheck(node, i);
        ret = RecordNotNullPropeIsAllSet(node->currRecord);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    // 再处理所有元素中的子树
    uint32_t realNodeNum = node->nodeDesc->nodeNumPerElement * node->realElementNum;
    for (uint32_t i = 0; i < realNodeNum; i++) {
        ret = NodeNotNullPropeIsAllSet(node->nodes[i]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status NodeUpdatePropeIsAllFixed(DmNodeT *node, bool *isAllFixed)
{
    if (node == NULL) {
        return GMERR_OK;
    }
    if (node->opNum > 0) {
        *isAllFixed = false;
        return GMERR_OK;
    }

    // 先处理所有元素中的普通属性
    for (uint32_t i = 0; i < node->realElementNum; i++) {
        NodeSetElementIndexWithoutCheck((DmNodeT *)node, i);
        *isAllFixed = RecordUpdatePropeIsAllFixed(node->currRecord);
        if (!(*isAllFixed)) {
            return GMERR_OK;
        }
    }
    // 再处理所有元素中的子树
    uint32_t realNodeNum = node->nodeDesc->nodeNumPerElement * node->realElementNum;
    for (uint32_t i = 0; i < realNodeNum; i++) {
        Status ret = NodeUpdatePropeIsAllFixed(node->nodes[i], isAllFixed);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (!(*isAllFixed)) {
            return GMERR_OK;
        }
    }
    return GMERR_OK;
}

uint32_t OamapKeyBufCompare(const void *key1, const void *key2)
{
    DB_POINTER2(key1, key2);
    const DmBuffer *buffer1 = (const DmBuffer *)key1;
    const DmBuffer *buffer2 = (const DmBuffer *)key2;
    return (buffer1->len == buffer2->len && memcmp(buffer1->buf, buffer2->buf, buffer1->len) == 0);
}

// maxKeyBufSize是所有子树节点中keybufLen的最大值，包含nullInfoBytes
// maxRealElementNum是所有子树节点中realElementNum最大的
// keyBufTotal是按最大长度计算的（即: 最大的实际元素个数maxRealElementNum * 最大的keybuf长度maxKeyBufSize）
Status NodeMemberKeyCheckInner(
    DmNodeT *node, DbOamapT *keyBufMap, uint8_t *keyBufTotal, uint32_t maxKeyBufSize, uint32_t maxRealElementNum)
{
    Status ret = GMERR_OK;
    // 这里的maxKeyBufSize 和 maxRealElementNum需要和调用处保持一致。
    uint64_t keyBufSize = (uint64_t)maxKeyBufSize * maxRealElementNum;
    uint8_t *keyBuf = keyBufTotal;

    DmBuffer *dmBuf = (DmBuffer *)(keyBufTotal + keyBufSize);
    const DmIndexKeyBufInfoT *indexKeyInfo = NULL;
    // 依次检查node上定义的memberKey
    for (uint32_t i = 0; i < node->nodeDesc->indexNum; i++) {
        // 获取对应memberkey的indexKeyInfo信息
        indexKeyInfo = GetKeyBufInfoForNode(node->nodeDesc, i);
        // 遍历所有的record记录，获取每个record的keyBuf进行验证
        for (uint32_t j = 0; j < node->realElementNum; j++) {
            NodeSetElementIndexWithoutCheck(node, j);
            // memberKey 属性非空检查
            ret = RecordNotNullPropeIsAllSet(node->currRecord);
            if (ret != GMERR_OK) {
                return ret;
            }
            uint32_t keyBufLen = 0;
            uint8_t *keyBufCursor = (uint8_t *)(keyBuf + maxKeyBufSize * j);
            RecordGetKeyBuf(node->currRecord, indexKeyInfo, keyBufCursor, &keyBufLen);
            // memberKey最大值约束检查：memberKey不能超过532B
            if (keyBufLen > DM_MAX_INDEX_KEY_SIZE_WITH_NULL(indexKeyInfo->nullInfoBytes)) {
                DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED,
                    "Keybuf exceeds limit, total"
                    " size %" PRIu32 " limit %" PRIu32 ".",
                    keyBufLen, (uint32_t)DM_MAX_INDEX_KEY_SIZE_WITH_NULL(indexKeyInfo->nullInfoBytes));
                return GMERR_PROGRAM_LIMIT_EXCEEDED;
            }
            // memberKey 唯一性约束检查
            if (indexKeyInfo->indexConstraint == NON_UNIQUE) {
                continue;
            }
            dmBuf[j].buf = keyBufCursor;
            dmBuf[j].len = keyBufLen;
            uint32_t hash = DbHash32(dmBuf[j].buf, dmBuf[j].len);
            ret = DbOamapInsert(keyBufMap, hash, &dmBuf[j], 0, NULL);
            if (ret == GMERR_DUPLICATE_OBJECT) {
                DB_LOG_AND_SET_LASERR(GMERR_MEMBER_KEY_VIOLATION, "Duplicate memKey.");
                return GMERR_MEMBER_KEY_VIOLATION;
            }
            if (ret != GMERR_OK) {
                DB_LOG_AND_SET_LASERR(ret, "Check node memKey, insert map");
                return ret;
            }
        }
        DbOamapClear(keyBufMap);
    }  // 针对所有memkey的约束检查
    return GMERR_OK;
}

static void GetNodeMemberKeyTotalNodeNum(
    const DmNodeT *node, uint64_t *totalNodeNum, uint32_t *maxKeyBufSize, uint32_t *maxRealElementNum)
{
    if (!DmNodeIsCreated(node)) {
        return;
    }
    // 先判断当前节点是否要加进来（array\vector节点 并且有memberkey定义的节点才加进来）
    uint32_t realNodeNum = node->realElementNum * node->nodeDesc->nodeNumPerElement;
    if (node->nodeDesc->nodeType != DM_NODE_RECORD && node->nodeDesc->indexNum != 0) {
        *totalNodeNum += 1;
        if (node->nodeDesc->maxKeyLen > *maxKeyBufSize) {
            *maxKeyBufSize = node->nodeDesc->maxKeyLen;  // 这里maxKeyLen包含了nullInfoBytes长度
        }
        if (node->realElementNum > *maxRealElementNum) {
            *maxRealElementNum = node->realElementNum;
        }
    }
    for (uint32_t i = 0; i < realNodeNum; i++) {
        GetNodeMemberKeyTotalNodeNum(node->nodes[i], totalNodeNum, maxKeyBufSize, maxRealElementNum);
    }
}

static void GetNodeMemberKeyTotalNodePtr(
    const DmNodeT **nodePtr, uint64_t totalNodeNum, const DmNodeT *node, uint64_t *index)
{
    if (!DmNodeIsCreated(node)) {
        return;
    }
    // 先判断当前节点是否要加进来（array\vector节点 并且有memberkey定义的节点才加进来）
    uint32_t realNodeNum = node->realElementNum * node->nodeDesc->nodeNumPerElement;
    if (node->nodeDesc->nodeType != DM_NODE_RECORD && node->nodeDesc->indexNum != 0) {
        nodePtr[(*index)++] = node;
    }
    for (uint32_t i = 0; i < realNodeNum; i++) {
        GetNodeMemberKeyTotalNodePtr(nodePtr, totalNodeNum, node->nodes[i], index);
    }
}

Status NodeMemberKeyCheck(DmNodeT *node)
{
    if (!DmNodeIsCreated(node)) {
        return GMERR_OK;
    }
    // 这里最好是改成深度优先的栈实现，但由于栈的大小不能确定（由于当前对数据节点没有约束），所以这里先用两个简单的递归实现
    // 后续有好的方案可继续优化
    uint64_t totalNodeNum = 0;
    uint32_t maxKeyBufSize = 0;
    uint32_t maxRealElementNum = 0;
    GetNodeMemberKeyTotalNodeNum((const DmNodeT *)node, &totalNodeNum, &maxKeyBufSize, &maxRealElementNum);
    // 空间换时间，这里有内存浪费,后面需要的内存在这里统一申请
    // 主要包含keybuf的内存和 map中的key（DmBuffer）内存申请
    uint64_t nodeBufSize = totalNodeNum * (uint64_t)sizeof(DmNodeT *);
    // keyBufSize 按最大的realElementNum 和 最大的keyBufSize进行申请， DmBuffer 是后面插入map的value
    uint64_t keyBufSize = (uint64_t)((sizeof(DmBuffer)) + maxKeyBufSize) * maxRealElementNum;
    uint64_t totalMemSize = keyBufSize + nodeBufSize;
    // 内存释放逻辑：本函数内销毁
    // 这里申请的内存不memset是为了性能更优，后续从内存中读之前都会先对对应的内存区域进行先写，逻辑保证不会读到未初始化的内存区域
    // 这里依赖RecordGetKeyBuf函数对keybuf的填充和实际len是一致的。并且不依赖keybuf的memset操作
    uint8_t *totalBuf = (uint8_t *)DbDynMemCtxAlloc(node->memCtx, totalMemSize);
    if (totalBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Check node memKey, totalBuf size:%" PRIu64 ".", totalMemSize);
        return GMERR_OUT_OF_MEMORY;
    }

    uintptr_t *nodePtr = (uintptr_t *)(totalBuf);
    uint64_t index = 0;
    GetNodeMemberKeyTotalNodePtr((const DmNodeT **)nodePtr, totalNodeNum, (const DmNodeT *)node, &index);

    DbOamapT keyBufMap = {0};
    Status ret = DbOamapInit(&keyBufMap, maxRealElementNum, OamapKeyBufCompare, node->memCtx, true);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(node->memCtx, totalBuf);
        return ret;
    }

    uint8_t *keyBufTotal = (uint8_t *)(totalBuf + nodeBufSize);

    for (uint64_t i = 0; i < index; ++i) {
        DmNodeT *nodeTmp = (DmNodeT *)nodePtr[i];
        ret = NodeMemberKeyCheckInner(nodeTmp, &keyBufMap, keyBufTotal, maxKeyBufSize, maxRealElementNum);
        if (ret != GMERR_OK) {
            break;
        }
    }
    DbOamapDestroy(&keyBufMap);
    DbDynMemCtxFree(node->memCtx, totalBuf);
    return ret;
}

Status NodeMemberKeyCheckSizeInner(DmNodeT *node, uint8_t *keyBuf, uint64_t keyBufSize)
{
    const DmIndexKeyBufInfoT *indexKeyInfo = NULL;
    // 依次检查node上定义的memberKey
    for (uint32_t i = 0; i < node->nodeDesc->indexNum; i++) {
        // 获取对应memberkey的indexKeyInfo信息
        indexKeyInfo = GetKeyBufInfoForNode(node->nodeDesc, i);
        // 遍历所有的record记录，获取每个record的keyBufLen
        for (uint32_t j = 0; j < node->realElementNum; j++) {
            NodeSetElementIndexWithoutCheck(node, j);
            uint32_t keyBufLen = 0;
            RecordGetKeyBuf(node->currRecord, indexKeyInfo, keyBuf, &keyBufLen);
            // memberKey最大值约束检查：memberKey不能超过532B
            if (keyBufLen > DM_MAX_INDEX_KEY_SIZE_WITH_NULL(indexKeyInfo->nullInfoBytes)) {
                DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED,
                    "keybuf size exceeds limit, total size %" PRIu32 ""
                    " limit %" PRIu32 ".",
                    keyBufLen, (uint32_t)DM_MAX_INDEX_KEY_SIZE_WITH_NULL(indexKeyInfo->nullInfoBytes));
                return GMERR_PROGRAM_LIMIT_EXCEEDED;
            }
        }
        // 复用前需要memset一下，不然后面做唯一性校验会出问题
        (void)memset_s(keyBuf, keyBufSize, 0x00, keyBufSize);
    }
    return GMERR_OK;
}

Status NodeMemberKeySizeCheck(DmNodeT *node)
{
    if (node == NULL) {
        return GMERR_OK;
    }
    uint64_t nodeNum = 1;  // 需要先把当前节点算上
    // 为了复用函数，这里定义的两个栈变量没有用到
    uint32_t maxKeyBufSize = 0;
    uint32_t maxElementNum = 0;
    GetNodeMemberKeyTotalNodeNum((const DmNodeT *)node, &nodeNum, &maxKeyBufSize, &maxElementNum);

    uint64_t nodeBufSize = nodeNum * (uint64_t)sizeof(DmNodeT *);
    // keyBufSize 按最大的realElementNum 和 最大的keyBufSize进行申请
    uint64_t keyBufSize = (uint64_t)maxKeyBufSize * maxElementNum;
    // totalMemSize 包含了keybuf的最大长度
    uint64_t totalMemSize = keyBufSize + nodeBufSize;
    // 内存释放逻辑：本函数内销毁
    uint8_t *totalBuf = (uint8_t *)DbDynMemCtxAlloc(node->memCtx, totalMemSize);
    if (totalBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Check node memKeySize:%" PRIu64 ".", totalMemSize);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(totalBuf, totalMemSize, 0x00, totalMemSize);

    uintptr_t *nodePtr = (uintptr_t *)(totalBuf);
    uint64_t index = 0;
    ((DmNodeT **)nodePtr)[index++] = node;  // 需要先把当前节点放进去
    GetNodeMemberKeyTotalNodePtr((const DmNodeT **)nodePtr, nodeNum, (const DmNodeT *)node, &index);
    DB_ASSERT(nodeNum == index);  // 用于debug环境，totalNodeNum 必定和index相等

    uint8_t *keyBuf = (uint8_t *)(totalBuf + nodeBufSize);
    Status ret = GMERR_OK;
    for (uint64_t i = 0; i < nodeNum; ++i) {
        DmNodeT *nodeTmp = (DmNodeT *)nodePtr[i];
        if (nodeTmp == NULL || nodeTmp->nodeDesc->nodeType == DM_NODE_RECORD || nodeTmp->nodeDesc->indexNum == 0 ||
            nodeTmp->nodeDesc->maxKeyLenWithoutBits <= DM_MAX_INDEX_KEY_SIZE) {
            continue;
        }
        ret = NodeMemberKeyCheckSizeInner(nodeTmp, keyBuf, keyBufSize);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(node->memCtx, totalBuf);
            return ret;
        }
    }
    DbDynMemCtxFree(node->memCtx, totalBuf);
    return GMERR_OK;
}

Status NodeRunningBufInit(DmNodeT *node, uint32_t startIndex, uint32_t endIndex, bool withDefault)
{
    // 如果节点下面全是子树节点，没有普通属性，此时不用进行默认值的相关处理
    if (node->totalBufSize == 0) {
        NodeSetElementIndexWithoutCheck(node, 0);
        return GMERR_OK;
    }
    // 不带默认值的场景，在有变长字段的情况下，需要处理下runningBuf中maxLen
    if (!withDefault && node->currRecord->recordDesc->isFixed != 1) {
        for (uint32_t i = startIndex; i < endIndex; i++) {
            NodeSetElementIndexWithoutCheck(node, i);
            ProcessVarPropeMaxSize(node->currRecord);
        }
        return GMERR_OK;
    }
    for (uint32_t i = startIndex; i < endIndex; i++) {
        NodeSetElementIndexWithoutCheck(node, i);
        Status ret = ProcessDefaultValue4Node(node->currRecord);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

uint32_t VectorNodeGetExpandedSize(const DmNodeT *node)
{
    // 扩容第一阶段按系数(2)倍增，超过vectorExtendSize第二阶段按定值增加
    uint32_t extendSize = (DM_VECTOR_NODE_EXTEND_SPEED - 1) * node->totalElementNum;
    extendSize = DM_MIN2(extendSize, node->nodeDesc->vectorExtendSize);
    uint32_t newElementNum = extendSize + node->totalElementNum;
    // 扩容将超过最大数目，直接扩容到maxElementNum
    newElementNum = DM_MIN2(newElementNum, node->nodeDesc->maxElementNum);
    newElementNum = DM_MAX2(newElementNum, 1);  // 防止node初始totalElement为0
    return newElementNum;
}

void VectorNodeBufScalingRecordSerBuf(
    DmNodeT *node, uint32_t newElementNum, VectorScalingE scaleDirection, uint8_t **preAllocBuf)
{
    uint32_t arrMemSize = (uint32_t)(sizeof(uint8_t *) * newElementNum);
    uint32_t copySize =
        (scaleDirection == VECTOR_EXPAND) ? (uint32_t)(sizeof(uint8_t *) * node->totalElementNum) : arrMemSize;
    uint8_t **newRecordSeriBuf = (uint8_t **)(*preAllocBuf);
    *preAllocBuf += arrMemSize;
    (void)memset_s(newRecordSeriBuf, arrMemSize, 0x00, arrMemSize);
    if (copySize != 0) {
        errno_t err = memcpy_s(newRecordSeriBuf, arrMemSize, node->nodeSeriBuf, copySize);
        DB_ASSERT(err == EOK);  // arrMemSize一定大于等于copySize
    }
    node->nodeSeriBuf = newRecordSeriBuf;
}

void VectorNodeBufScalingRecordSerBufLen(
    DmNodeT *node, uint32_t newElementNum, VectorScalingE scaleDirection, uint8_t **preAllocBuf)
{
    uint32_t arrMemSize = (uint32_t)(sizeof(uint32_t) * newElementNum);
    uint32_t copySize =
        (scaleDirection == VECTOR_EXPAND) ? (uint32_t)(sizeof(uint32_t) * node->totalElementNum) : arrMemSize;
    // 内存释放逻辑：DmDestroyVertex或VectorNodeBufScaling
    uint32_t *newRecordSeriBufLen = (uint32_t *)(*preAllocBuf);
    *preAllocBuf += arrMemSize;
    (void)memset_s(newRecordSeriBufLen, arrMemSize, 0x00, arrMemSize);
    if (copySize != 0) {
        errno_t err = memcpy_s(newRecordSeriBufLen, arrMemSize, node->nodeSeriBufLen, copySize);
        DB_ASSERT(err == EOK);  // arrMemSize一定大于等于copySize
    }
    node->nodeSeriBufLen = newRecordSeriBufLen;
}

static void VectorElementRecordScalingCopy(
    const void *oldBuf, uint32_t newTotalPropeNum, VectorScalingE scaleDirection, const DmNodeT *node, void *newBuf)
{
    (void)memset_s(newBuf, newTotalPropeNum, 0x00, newTotalPropeNum);
    uint32_t copySize = (scaleDirection == VECTOR_EXPAND) ?
                            (node->currRecord->recordDesc->propeNum * node->totalElementNum) :
                            newTotalPropeNum;
    if (copySize != 0) {
        errno_t err = memcpy_s(newBuf, newTotalPropeNum, oldBuf, copySize);
        DB_ASSERT(err == EOK);  // Note：copySize可能为0，yang场景空节点的propeNum为0
    }
}

void VectorNodeBufScalingUpdateBitmapBuf(
    DmNodeT *node, uint32_t newElementNum, VectorScalingE scaleDirection, uint8_t **preAllocBuf)
{
    // copy原node中updateBitmapBuf
    RecordDescT *recordDesc = node->currRecord->recordDesc;
    uint32_t arrMemSize = (uint32_t)(recordDesc->bitmapUpdateInfoLen * newElementNum);
    if (arrMemSize != 0) {
        uint8_t *newUpdateBitMapBuf = (uint8_t *)(*preAllocBuf);
        *preAllocBuf += arrMemSize;
        (void)memset_s(newUpdateBitMapBuf, arrMemSize, 0xFF, arrMemSize);
        uint32_t copySize = (scaleDirection == VECTOR_EXPAND) ?
                                (uint32_t)(node->currRecord->recordDesc->bitmapUpdateInfoLen * node->totalElementNum) :
                                arrMemSize;
        if (copySize != 0) {
            errno_t err = memcpy_s(newUpdateBitMapBuf, arrMemSize, node->updateBitmapBuf, copySize);
            DB_ASSERT(err == EOK);  // arrMemSize一定大于等于copySize
        }
        node->updateBitmapBuf = newUpdateBitMapBuf;
    }
}

void VectorNodeBufScalingRecordSeriLenArr(
    DmNodeT *node, uint32_t newElementNum, VectorScalingE scaleDirection, uint8_t **preAllocBuf)
{
    // copy原node中recordSeriLenArr
    uint32_t arrMemSize = (uint32_t)(sizeof(uint32_t) * newElementNum);
    // newElementNum不可能为0，arrMemSize一定大于0
    uint32_t copySize =
        (scaleDirection == VECTOR_EXPAND) ? (uint32_t)(sizeof(uint32_t) * node->totalElementNum) : arrMemSize;
    uint32_t *newRecordSeriLenArr = (uint32_t *)(*preAllocBuf);
    *preAllocBuf += arrMemSize;
    (void)memset_s(newRecordSeriLenArr, arrMemSize, 0x00, arrMemSize);
    if (copySize != 0) {
        errno_t err = memcpy_s(newRecordSeriLenArr, arrMemSize, node->recordSeriLenArr, copySize);
        DB_ASSERT(err == EOK);  // arrMemSize一定大于等于copySize
    }
    node->recordSeriLenArr = newRecordSeriLenArr;
}

void VectorNodeBufScalingRuningbuf(
    DmNodeT *node, uint32_t newElementNum, VectorScalingE scaleDirection, uint8_t **preAllocBuf, bool withDefaultValue)
{
    // 如果节点下面全是子树节点，没有普通属性，此时不用进行属性相关buf的扩缩容
    // copy原node中runningBuf
    if (node->currRecord->recordDesc->runningBufLen == 0) {
        return;
    }
    uint32_t newTotalBufSize = newElementNum * node->currRecord->recordDesc->runningBufLen;
    uint8_t *runningBuf = (uint8_t *)(*preAllocBuf);
    *preAllocBuf += newTotalBufSize;
    (void)memset_s(runningBuf, newTotalBufSize, 0x00, newTotalBufSize);
    uint32_t copySize = (scaleDirection == VECTOR_EXPAND) ? node->totalBufSize : newTotalBufSize;
    if (copySize != 0) {
        errno_t err = memcpy_s(runningBuf, newTotalBufSize, node->runningBuf, copySize);
        DB_ASSERT(err == EOK);  // newTotalBufSize一定大于等于copySize
    }
    node->runningBuf = runningBuf;
    node->totalBufSize = newTotalBufSize;
    return;
}

Status VectorNodeBufScaling(DmNodeT *node, uint32_t newElementNum, VectorScalingE scaleDirection, bool withDefaultValue)
{
    uint32_t preAllocBufLen = GetPreAllocSizeInVectorNode(node, newElementNum);
    // 内存释放逻辑：DmDestroyVertex或VectorNodeBufScaling
    uint8_t *preAllocBuf = (uint8_t *)DbDynMemCtxAlloc(node->memCtx, preAllocBufLen);
    if (preAllocBuf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Vector update buf expand, size: %" PRIu32 ".", preAllocBufLen);
        return GMERR_OUT_OF_MEMORY;
    }

    // copy原node中propeIsSetValue
    uint32_t newTotalPropeNum = node->currRecord->recordDesc->propeNum * newElementNum;
    int8_t *propeIsSetValue = (int8_t *)preAllocBuf;
    preAllocBuf += newTotalPropeNum;
    VectorElementRecordScalingCopy(node->propeIsSetValue, newTotalPropeNum, scaleDirection, node, propeIsSetValue);
    // copy原node中propeIsEmpty
    int8_t *propeIsEmtpy = (int8_t *)preAllocBuf;
    preAllocBuf += newTotalPropeNum;
    VectorElementRecordScalingCopy(node->propeIsEmpty, newTotalPropeNum, scaleDirection, node, propeIsEmtpy);
    node->propeIsEmpty = propeIsEmtpy;
    // copy原node中propeOpTypes
    if (node->currRecord->recordDesc->propOpTypesLen > 0) {
        uint32_t newTotalPropeOpNum = node->currRecord->recordDesc->propOpTypesLen * newElementNum;
        uint8_t *propeOpTypes = (uint8_t *)preAllocBuf;
        preAllocBuf += newTotalPropeOpNum;
        VectorElementRecordScalingCopy(node->propeOpTypes, newTotalPropeOpNum, scaleDirection, node, propeOpTypes);
        node->propeOpTypes = propeOpTypes;
    }
    VectorNodeBufScalingUpdateBitmapBuf(node, newElementNum, scaleDirection, &preAllocBuf);
    VectorNodeBufScalingRecordSeriLenArr(node, newElementNum, scaleDirection, &preAllocBuf);
    VectorNodeBufScalingRecordSerBuf(node, newElementNum, scaleDirection, &preAllocBuf);
    VectorNodeBufScalingRecordSerBufLen(node, newElementNum, scaleDirection, &preAllocBuf);
    VectorNodeBufScalingRuningbuf(node, newElementNum, scaleDirection, &preAllocBuf, withDefaultValue);

    DbDynMemCtxFree(node->memCtx, node->propeIsSetValue);
    node->propeIsSetValue = propeIsSetValue;

    // 这里缩容不用初始化，删除元素的时候已经进行初始化了
    if (node->totalBufSize != 0 && scaleDirection == VECTOR_EXPAND) {
        Status ret = NodeRunningBufInit(node, node->totalElementNum, newElementNum, withDefaultValue);
        if (ret != GMERR_OK) {
            FreeNodeInnerMemory(node);
            return ret;
        }
    }
    DB_ASSERT((uintptr_t)preAllocBuf - (uintptr_t)propeIsSetValue == preAllocBufLen);
    return GMERR_OK;
}

Status VectorElementNodeExpand(DmNodeT *node, uint32_t newElementNum)
{
    uint32_t nodeNumPerElement = node->nodeDesc->nodeNumPerElement;
    if (nodeNumPerElement == 0) {
        return GMERR_OK;
    }
    uint32_t newTotalNodeNum = newElementNum * nodeNumPerElement;
    size_t nodesPtrMemSize = sizeof(DmNodeT *) * newTotalNodeNum;
    // 内存释放逻辑：DmDestroyVertex或VectorElementNodeExpand
    DmNodeT **nodes = DbDynMemCtxAlloc(node->memCtx, nodesPtrMemSize);
    if (nodes == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Expand vector ele node, size: %zu", nodesPtrMemSize);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(nodes, nodesPtrMemSize, 0x00, nodesPtrMemSize);
    // copy原vector中的nodes数组
    uint32_t oldTotalNodeNum = node->totalElementNum * nodeNumPerElement;
    size_t copySize = sizeof(DmNodeT *) * oldTotalNodeNum;
    errno_t err = memcpy_s(nodes, nodesPtrMemSize, node->nodes, copySize);
    DB_ASSERT(err == EOK);
    for (uint32_t i = oldTotalNodeNum; i < newTotalNodeNum; i++) {
        Status ret =
            DmCreateEmptyNode(node->memCtx, node->nodeDesc->childNodeDescs[i % nodeNumPerElement], &nodes[i], true);
        if (ret != GMERR_OK) {
            // 释放前面已经创建成功的node
            for (uint32_t j = oldTotalNodeNum; j < i; j++) {
                DestroyNode(&nodes[j]);
            }
            // 释放node指针数组
            DbDynMemCtxFree(node->memCtx, nodes);
            return ret;
        }
    }

    DbDynMemCtxFree(node->memCtx, node->nodes);
    node->nodes = nodes;
    return GMERR_OK;
}

Status VectorElementExpand(DmNodeT *node, uint32_t newElementNum, bool withDefaultValue)
{
    Status ret = VectorNodeBufScaling(node, newElementNum, VECTOR_EXPAND, withDefaultValue);
    if (ret != GMERR_OK) {
        return ret;
    }

    // vector元素中node的扩容
    ret = VectorElementNodeExpand(node, newElementNum);
    if (ret != GMERR_OK) {
        return ret;
    }

    node->totalElementNum = newElementNum;
    return GMERR_OK;
}

Status VectorElementNodeShrink(DmNodeT *node, uint32_t newElementNum)
{
    uint32_t nodeNumPerElement = node->nodeDesc->nodeNumPerElement;
    if (nodeNumPerElement == 0) {
        return GMERR_OK;
    }
    uint32_t newTotalNodeNum = nodeNumPerElement * newElementNum;
    size_t nodesPtrMemSize = sizeof(DmNodeT *) * newTotalNodeNum;
    // 内存释放逻辑：DmDestroyVertex或VectorElementNodeShrink
    DmNodeT **nodes = DbDynMemCtxAlloc(node->memCtx, nodesPtrMemSize);
    if (nodes == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Shrink vector ele node, size: %zu", nodesPtrMemSize);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(nodes, nodesPtrMemSize, 0x00, nodesPtrMemSize);
    // copy原vector中的nodes数组
    errno_t err = memcpy_s(nodes, nodesPtrMemSize, node->nodes, nodesPtrMemSize);
    DB_ASSERT(err == EOK);
    uint32_t oldTotalNodeNum = nodeNumPerElement * node->totalElementNum;
    for (uint32_t i = newTotalNodeNum; i < oldTotalNodeNum; i++) {
        DestroyNode(&node->nodes[i]);
    }

    DbDynMemCtxFree(node->memCtx, node->nodes);
    node->nodes = nodes;
    return GMERR_OK;
}

Status VectorElementShrink(DmNodeT *node)
{
    // 缩容后剩余的元素个数
    uint32_t newTotalElementNum =
        DM_MIN2(node->realElementNum + node->nodeDesc->vectorExtendSize, node->totalElementNum);
    Status ret = VectorNodeBufScaling(node, newTotalElementNum, VECTOR_SHRINK, false);
    if (ret != GMERR_OK) {
        return ret;
    }

    // vector元素中node的缩容
    ret = VectorElementNodeShrink(node, newTotalElementNum);
    if (ret != GMERR_OK) {
        return ret;
    }

    node->totalElementNum = newTotalElementNum;
    return GMERR_OK;
}

inline static Status CheckMagicCodeAndLogInfo(
    DmNodeT *node, const char *nodeName, uint32_t childIndex, DbMemCtxT *memCtxPtr)
{
    if (node != NULL && node->headMagicCode == DM_RUNNING_MAGIC_CODE && node->tailMagicCode == DM_RUNNING_MAGIC_CODE) {
        return GMERR_OK;
    }
    if (node == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "Node null, nodeName:%s, child index:%" PRIu32 ", ctx magicCode:%" PRIu32 "", nodeName, childIndex,
            memCtxPtr->magicCode);
        DB_ASSERT(false);
        return GMERR_DATA_EXCEPTION;
    }
    DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
        "Node inv, nodeName:%s, child index:%" PRIu32 ", ctx magicCode:%" PRIu32 ", node headMagicNode:%" PRIu32
        ",  node tailMagicCode:%" PRIu32 "",
        nodeName, childIndex, memCtxPtr->magicCode, node->headMagicCode, node->tailMagicCode);
    DB_ASSERT(false);
    return GMERR_DATA_EXCEPTION;
}

// 异常分支内存由上层释放
Status CreateChildNodes(DmNodeT *node)
{
    if (node->nodeDesc->nodeNumPerElement == 0) {
        return GMERR_OK;
    }

    uint32_t totalNodeNum = node->totalElementNum * node->nodeDesc->nodeNumPerElement;
    size_t nodesPtrMemSize = sizeof(DmNodeT *) * totalNodeNum;
    // 内存释放逻辑：DmDestroyVertex
    node->nodes = DbDynMemCtxAlloc(node->memCtx, nodesPtrMemSize);
    if (SECUREC_UNLIKELY(node->nodes == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Create child nodes, size: %zu", nodesPtrMemSize);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(node->nodes, nodesPtrMemSize, 0x00, nodesPtrMemSize);
    node->currNodes = node->nodes;
    const char *parentNodeName = node->nodeDesc->name;
    DbMemCtxT *memCtxPtr = node->memCtx;
    bool needCreated = node->nodeDesc->yangInfoDesc == NULL;  // yang句柄按需创建
    for (uint32_t i = 0; i < totalNodeNum; i++) {
        DmNodeDescT *nodeDesc = node->nodeDesc->childNodeDescs[i % node->nodeDesc->nodeNumPerElement];
        Status ret = DmCreateEmptyNode(node->memCtx, nodeDesc, &node->nodes[i], needCreated);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_AND_SET_LASERR(ret, "Create child node, name: %s.", nodeDesc->name);
            return ret;
        }
        ret = CheckMagicCodeAndLogInfo(node, parentNodeName, i, memCtxPtr);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

void DestroyNode(DmNodeT **node)
{
    if (SECUREC_UNLIKELY(node == NULL || *node == NULL)) {
        return;
    }
    FreeNodeInnerMemory(*node);
    (*node)->headMagicCode = DM_DESTROYED_MAGIC_CODE;
    (*node)->tailMagicCode = DM_DESTROYED_MAGIC_CODE;
    DbDynMemCtxFree((*node)->memCtx, *node);
    *node = NULL;
}

void FreeChildNodes(DmNodeT *node)
{
    if (node->nodes == NULL) {
        return;
    }
    uint32_t totalNodeNum = node->totalElementNum * node->nodeDesc->nodeNumPerElement;
    for (uint32_t i = 0; i < totalNodeNum; i++) {
        if (node->nodes[i] == NULL) {
            continue;
        }
        DestroyNode(&node->nodes[i]);
    }
    DbDynMemCtxFree(node->memCtx, node->nodes);
    node->nodes = NULL;
}

void FreeNodeInnerMemory(DmNodeT *node)
{
    if (node == NULL || node->currRecord == NULL) {
        return;
    }
    if (node->currRecord->hasLongVarPrope == 1) {
        for (uint32_t i = 0; i < node->totalElementNum; i++) {
            uint8_t *runningBufCursor = node->runningBuf;
            runningBufCursor += i * node->currRecord->recordDesc->runningBufLen;
            FreeRecordLongVarProp(node->currRecord, runningBufCursor);
        }
    }

    node->currRecord->hasLongVarPrope = 0;
    DestroyRecord(&(node->currRecord));
    node->currRecord = NULL;

    // record node和 array node中propeIsSetValue，propeIsEmpty，runningBuf，recordSeriLenArr申请在一块buf上，
    // propeIsSetValue指向这块buf的头部，因此只需释放propeIsSetValue
    DmNodeDescT *nodeDesc = node->nodeDesc;
    DbDynMemCtxFree(node->memCtx, node->propeIsSetValue);
    node->propeIsSetValue = NULL;
    node->propeIsEmpty = NULL;
    node->propeOpTypes = NULL;
    node->runningBuf = NULL;
    node->recordSeriLenArr = NULL;
    node->nodeSeriBuf = NULL;
    node->nodeSeriBufLen = NULL;
    node->updateBitmapBuf = NULL;

    node->nodeKeyBuf = NULL;  // keyBuf和node结构体一起申请，需要一起释放
    // 释放opArray数组
    DbDynMemCtxFree(node->memCtx, node->opArray);
    node->opArray = NULL;

    // deltaMemberKeys需要单独释放
    if (nodeDesc->nodeType == DM_NODE_VECTOR && node->deltaMemberKeys != NULL) {
        for (uint32_t i = 0; i < node->opNumByMemberKey; i++) {
            DmDestroyIndexKey(node->deltaMemberKeys[i]);
            node->deltaMemberKeys[i] = NULL;
        }
        DbDynMemCtxFree(node->memCtx, node->deltaMemberKeys);
        node->deltaMemberKeys = NULL;
    }
    FreeChildNodes(node);
}

Status DmResetNode(DmNodeT *node)
{
    if (node == NULL) {
        return GMERR_OK;
    }
    Status ret;
    node->isCreated = false;
    // 先处理所有元素中的普通属性
    // clear用totalElementNum而不是realElementNum：vector预申请了一部分元素内存，clear之后再进行reset操作时，
    // realElementNum为0，所以恢复这些element的默认值需要使用totalElementNum。
    uint32_t resetElementNum = (node->isClear) ? node->totalElementNum : node->realElementNum;
    if (node->nodeDesc->nodeType == DM_NODE_RECORD) {
        ret = ResetRecord(node->currRecord);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    } else {
        for (uint32_t i = 0; i < resetElementNum; i++) {
            NodeSetElementIndexWithoutCheck(node, i);
            ret = ResetRecord(node->currRecord);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
        }
    }
    size_t realLen = resetElementNum * sizeof(uint8_t *);
    (void)memset_s(node->nodeSeriBuf, realLen, DM_PROPERTY_IS_NULL, realLen);
    realLen = resetElementNum * sizeof(uint32_t);
    (void)memset_s(node->nodeSeriBufLen, realLen, DM_PROPERTY_IS_NULL, realLen);

    // 在reset完后，统一对是否含有大字段的标记位进行处理，使其状态和默认值中是否有大字段保持一致
    node->currRecord->hasLongVarPrope = node->currRecord->recordDesc->hasLongDefault;

    // 再处理所有元素中的子树
    node->opNum = 0;
    uint32_t totalNodeNum = node->nodeDesc->nodeNumPerElement * resetElementNum;
    for (uint32_t i = 0; i < totalNodeNum; i++) {
        ret = DmResetNode(node->nodes[i]);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    if (node->nodeDesc->nodeType == DM_NODE_VECTOR) {
        node->isClear = false;
        ResetVectorNode(node);
    }
    return GMERR_OK;
}

void DmClearNode(DmNodeT *node)
{
    if (node == NULL) {
        return;
    }
    node->isCreated = false;
    uint32_t totalPropeNum = node->currRecord->recordDesc->propeNum * node->totalElementNum;  // int8_t数组
    (void)memset_s(node->propeIsSetValue, totalPropeNum, DM_PROPERTY_IS_NULL, totalPropeNum);
    (void)memset_s(node->propeIsEmpty, totalPropeNum, false, totalPropeNum);

    uint32_t propOpTypesLen = node->currRecord->recordDesc->propOpTypesLen;
    if (propOpTypesLen > 0) {
        uint32_t totalPropeOpNum = propOpTypesLen * node->totalElementNum;
        (void)memset_s(node->propeOpTypes, totalPropeOpNum, 0, totalPropeOpNum);
    }
    size_t realLen = node->totalElementNum * sizeof(uint8_t *);
    (void)memset_s(node->nodeSeriBuf, realLen, DM_PROPERTY_IS_NULL, realLen);
    realLen = node->totalElementNum * sizeof(uint32_t);
    (void)memset_s(node->nodeSeriBufLen, realLen, DM_PROPERTY_IS_NULL, realLen);
    node->opNum = 0;
    for (uint32_t i = 0; i < node->totalElementNum; i++) {
        NodeSetElementIndexWithoutCheck(node, i);
        if (node->currRecord->hasLongVarPrope == 1) {
            FreeRecordLongVarProp(node->currRecord, node->currRecord->runningBuf);
        }
    }
    node->currRecord->hasLongVarPrope = 0;
    uint32_t totalNodeNum = node->nodeDesc->nodeNumPerElement * node->totalElementNum;
    for (uint32_t i = 0; i < totalNodeNum; i++) {
        DmClearNode(node->nodes[i]);
    }
    if (node->nodeDesc->nodeType == DM_NODE_VECTOR) {
        node->isClear = true;
        ResetVectorNode(node);
    }
}

Status DmNodeSetUint8PropeByName(const DmNodeT *node, const char *propeName, uint8_t value)
{
    DB_POINTER2(node, propeName);
    DmValueT propertyValue = {0};
    propertyValue.type = DB_DATATYPE_UINT8;
    propertyValue.value.ushortValue = value;
    return DmNodeSetPropeByName(propeName, propertyValue, node);
}

Status DmNodeSetUint16PropeByName(const DmNodeT *node, const char *propeName, uint16_t value)
{
    DB_POINTER2(node, propeName);
    DmValueT propertyValue = {0};
    propertyValue.type = DB_DATATYPE_UINT16;
    propertyValue.value.uintValue = value;
    return DmNodeSetPropeByName(propeName, propertyValue, node);
}

Status DmNodeSetUint32PropeByName(const DmNodeT *node, const char *propeName, uint32_t value)
{
    DB_POINTER2(node, propeName);
    DmValueT propertyValue = {0};
    propertyValue.type = DB_DATATYPE_UINT32;
    propertyValue.value.uintValue = value;
    return DmNodeSetPropeByName(propeName, propertyValue, node);
}

Status DmNodeSetUint64PropeByName(const DmNodeT *node, const char *propeName, uint64_t value)
{
    DB_POINTER2(node, propeName);
    DmValueT propertyValue = {0};
    propertyValue.type = DB_DATATYPE_UINT64;
    propertyValue.value.ulongValue = value;
    return DmNodeSetPropeByName(propeName, propertyValue, node);
}

Status DmNodeSetStrPropeByName(const DmNodeT *node, const char *propeName, char *value, uint32_t valueLen)
{
    DB_POINTER3(node, propeName, value);
    DmValueT propertyValue = {0};
    propertyValue.type = DB_DATATYPE_STRING;
    propertyValue.value.strAddr = value;
    propertyValue.value.length = valueLen;
    return DmNodeSetPropeByName(propeName, propertyValue, node);
}

Status DmNodeSetPropeByName(const char *propeName, DmValueT propertyValue, const DmNodeT *node)
{
    DB_POINTER3(propeName, node, node->currRecord);
    return RecordSetPropeByName(propeName, propertyValue, node->currRecord);
}

Status DmNodeSetPropeById(uint32_t propeId, DmValueT propertyValue, const DmNodeT *node)
{
    DB_POINTER2(node, node->currRecord);
    return RecordSetPropeById(propeId, propertyValue, node->currRecord);
}

Status DmNodeGetPropeTypeById(uint32_t propeId, const DmNodeT *node, DbDataTypeE *type)
{
    DB_POINTER2(node, type);
    return RecordGetPropeTypeById(propeId, node->currRecord, type);
}

Status DmNodeGetPropeByName(const DmNodeT *node, const char *propeName, DmValueT *propertyValue)
{
    DB_POINTER3(node, propeName, propertyValue);
    return RecordGetPropeByName(node->currRecord, propeName, propertyValue, true);
}

Status DmNodeGetPropeByNameNoCopy(const DmNodeT *node, const char *propeName, DmValueT *propertyValue)
{
    DB_POINTER3(node, propeName, propertyValue);
    return RecordGetPropeByName(node->currRecord, propeName, propertyValue, false);
}

Status DmNodeGetPropeById(const DmNodeT *node, uint32_t propeId, DmValueT *propertyValue)
{
    DB_POINTER2(node, propertyValue);
    return RecordGetPropeById(node->currRecord, propeId, propertyValue, true);
}

Status DmNodeGetRealPropeById(const DmNodeT *node, uint32_t propeId, DmValueT *propeValue)
{
    DB_POINTER2(node, propeValue);
#ifdef FEATURE_YANG
    return DmNodeGetYangRealPropeById(node, propeId, propeValue);
#else
    return DmNodeGetPropeById(node, propeId, propeValue);
#endif
}

Status DmNodeGetRealPropeByName(const DmNodeT *node, const char *propeName, DmValueT *propertyValue)
{
    DB_POINTER3(node, propeName, propertyValue);
#ifdef FEATURE_YANG
    return DmNodeGetYangRealPropeByName(node, propeName, propertyValue);
#else
    return DmNodeGetPropeByName(node, propeName, propertyValue);
#endif
}

Status DmNodeGetRealPropeByNameNoCopy(const DmNodeT *node, const char *propeName, DmValueT *propeValue)
{
    DB_POINTER3(node, propeName, propeValue);
#ifdef FEATURE_YANG
    return DmNodeGetYangRealPropeByNameNoCopy(node, propeName, propeValue);
#else
    return DmNodeGetPropeByNameNoCopy(node, propeName, propeValue);
#endif
}

Status DmNodeGetPropeByIdNoCopy(const DmNodeT *node, uint32_t propeId, DmValueT *propertyValue)
{
    DB_POINTER2(node, propertyValue);
    return RecordGetPropeById(node->currRecord, propeId, propertyValue, false);
}

Status DmNodeGetPropNameById(const DmNodeT *node, uint32_t propId, char **propName)
{
    DB_POINTER2(node, propName);
    RecordDescT *recordDesc = node->currRecord->recordDesc;
    if (propId >= recordDesc->propeNum) {
        DB_LOG_AND_SET_LASERR(
            GMERR_INVALID_PROPERTY, "Get prop name by id, node %s, id = %" PRIu32 "", node->nodeDesc->name, propId);
        return GMERR_INVALID_PROPERTY;
    }
    *propName = recordDesc->propeInfos[propId].propeName;
    return GMERR_OK;
}

bool DmNodePropIsValidById(const DmNodeT *node, uint32_t propId)
{
    DB_POINTER(node);
    RecordDescT *recordDesc = node->currRecord->recordDesc;
    DB_ASSERT(propId < recordDesc->propeNum);
    return recordDesc->propeInfos[propId].isValid;
}

Status DmNodeGetPropeSizeByName(const DmNodeT *node, const char *propeName, uint32_t *propeSize)
{
    DB_POINTER3(node, propeName, propeSize);
    return RecordGetPropeSizeByName(node->currRecord, propeName, propeSize);
}
Status DmNodeGetPropeSizeById(const DmNodeT *node, uint32_t propeId, uint32_t *propeSize)
{
    DB_POINTER2(node, propeSize);
    return RecordGetPropeSizeById(node->currRecord, propeId, propeSize);
}

Status DmNodeGetRealPropeSizeById(const DmNodeT *node, uint32_t propeId, uint32_t *propeSize)
{
    DB_POINTER2(node, propeSize);
#ifdef FEATURE_YANG
    return DmNodeGetYangRealPropeSizeById(node, propeId, propeSize);
#else
    return DmNodeGetPropeSizeById(node, propeId, propeSize);
#endif
}

Status DmNodeSetSuperFieldByName(DmNodeT *node, const char *superFieldName, const DmValueT *superFieldValue)
{
    DB_POINTER3(node, superFieldName, superFieldValue);
    return RecordSetSuperFieldByName(node->currRecord, superFieldName, superFieldValue);
}

// 直接通过脚标设置SuperField。入参seqNum为脚标。
Status DmNodeSetSuperFieldById(const DmNodeT *node, uint32_t seqNum, const DmValueT *superFieldValue)
{
    DB_POINTER2(node, superFieldValue);
    return RecordSetSuperFieldById(node->currRecord, seqNum, superFieldValue);
}

Status DmNodeGetSuperFieldByName(DmNodeT *node, const char *superFieldName, DmValueT *superFieldValue)
{
    DB_POINTER3(node, superFieldName, superFieldValue);
    return RecordGetSuperFieldByName(node->currRecord, superFieldName, superFieldValue);
}

// 直接通过脚标获取SuperField。入参seqNum为脚标
Status DmNodeGetSuperFieldById(const DmNodeT *node, uint32_t seqNum, DmValueT *superFieldValue)
{
    DB_POINTER2(node, superFieldValue);
    return RecordGetSuperFieldById(node->currRecord, seqNum, superFieldValue);
}

Status DmNodeGetSuperFieldLengthByName(const DmNodeT *node, const char *superFieldName, uint32_t *length)
{
    DB_POINTER3(node, superFieldName, length);
    return RecordGetSuperFieldSizeByName(node->currRecord, superFieldName, length);
}

Status DmNodeGetSuperFieldLengthById(const DmNodeT *node, uint32_t superFieldId, uint32_t *length)
{
    DB_POINTER2(node, length);
    return RecordGetSuperFieldSizeById(node->currRecord, superFieldId, length);
}

void DmNodeGetRunningbuf(
    DmNodeT *node, uint8_t **runningBuf, uint32_t *fixLen, int8_t **propeIsSetValue, uint32_t *propeNum)
{
    DB_POINTER5(node, runningBuf, fixLen, propeIsSetValue, propeNum);
    DmRecordT *record = node->currRecord;
    *runningBuf = record->runningBuf;
    *fixLen = record->recordDesc->fixedPropertiesLen;
    *propeIsSetValue = record->propeIsSetValue;
    *propeNum = record->recordDesc->propeNum;
}

void DmNodeGetRecordbuf(DmNodeT *node, uint8_t **recordSeriBuf, uint32_t *recordSeriBufLen)
{
    DB_POINTER3(node, recordSeriBuf, recordSeriBufLen);
    DmRecordT *record = node->currRecord;
    uint32_t elementIndex = record->recordIndex;
    *recordSeriBuf = record->recordSeriBuf;
    *recordSeriBufLen = node->nodeSeriBufLen[elementIndex];
}

inline void NodeSetElementIndexWithoutCheck(DmNodeT *node, uint32_t elementIndex)
{
    DmRecordT *nodeRecord = node->currRecord;
    RecordSetRecordIdx(nodeRecord, elementIndex);
    // 在node节点不存在普通属性即totalBufSize为0的场景下，其相关的runningBufCursor，propeIsSetValue实际不会被操作到
    // 所以vector类型node在扩展元素时会不去扩展这些buf的内存，只保留最初创建时的内存；因为propeNum还包含了孩子node节点个数
    // 在没有普通属性的场景下如果还有孩子节点propeNum的值就不会为0，导致如果此处切换了buf，可能会导致问题，所以此处判断
    // 在node没有普通属性totalBufSize为0的场景下，不切换buf。实际也不会操作到相关的buf。无属性node的所有元素共享这些buf
    uint8_t *runningBufCursor = node->runningBuf;
    int8_t *propeIsSetValueCursor = node->propeIsSetValue;
    int8_t *propeIsEmptyCursor = node->propeIsEmpty;
    uint8_t *propeOpTypesCursor = node->propeOpTypes;
    uint8_t *updateBitMapBuf = node->updateBitmapBuf;
    RecordDescT *recordDesc = nodeRecord->recordDesc;
    if (node->totalBufSize != 0) {
        runningBufCursor += elementIndex * recordDesc->runningBufLen;
        propeIsSetValueCursor += elementIndex * recordDesc->propeNum * sizeof(int8_t);
        propeIsEmptyCursor += elementIndex * recordDesc->propeNum * sizeof(int8_t);
        propeOpTypesCursor += elementIndex * recordDesc->propOpTypesLen * sizeof(uint8_t);
        updateBitMapBuf += elementIndex * recordDesc->bitmapUpdateInfoLen;
    }
    RecordSetRunningBuf(nodeRecord, runningBufCursor);
    RecordSetPropeIsSetValue(nodeRecord, propeIsSetValueCursor);
    RecordSetPropeIsEmpty(nodeRecord, propeIsEmptyCursor);
    RecordSetPropeOpTypes(nodeRecord, propeOpTypesCursor);
    RecordSetUpdateBitMapBuf(nodeRecord, updateBitMapBuf);
    nodeRecord->recordSeriBuf = node->nodeSeriBuf[elementIndex];
    node->currNodes = &node->nodes[elementIndex * node->nodeDesc->nodeNumPerElement];
}

Status DmNodeSetElementIndex(DmNodeT *node, uint32_t elementIndex)
{
    DB_POINTER(node);

    if (SECUREC_UNLIKELY(elementIndex >= node->realElementNum)) {
        DB_LOG_AND_SET_LASERR(GMERR_ARRAY_SUBSCRIPT_ERROR,
            "Set eleIndex in node %s, Index (%" PRIu32 ") exceeds real"
            " eleNum (%" PRIu32 ").",
            node->nodeDesc->name, elementIndex, node->realElementNum);
        return GMERR_ARRAY_SUBSCRIPT_ERROR;
    }
    NodeSetElementIndexWithoutCheck(node, elementIndex);
    return GMERR_OK;
}

uint32_t DeltaVectorNodeGetExpandedSize(const DmNodeT *node)
{
    // 扩容第一阶段按系数(2)倍增，超过vectorExtendSize第二阶段按定值增加
    uint32_t extendSize = (DM_VECTOR_NODE_EXTEND_SPEED - 1) * node->totalElementNum;
    extendSize = DM_MIN2(extendSize, node->nodeDesc->vectorExtendSize);
    uint32_t newElementNum = extendSize + node->totalElementNum;
    // 扩容将超过最大数目，直接扩容到maxElementNum
    newElementNum = DM_MIN2(newElementNum, DM_DELTA_UPDATE_MAX_OPERATIONS);
    return newElementNum;
}

Status DeltaNodeVectorAppend(DmNodeT *node)
{
    DB_POINTER(node);
    // 需要扩容
    if (node->realElementNum == node->totalElementNum) {
        uint32_t newElementNum = DeltaVectorNodeGetExpandedSize(node);
        Status ret = VectorElementExpand(node, newElementNum, false);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    // totalElementNum是否增加，realElementNum都需要加1
    uint32_t recordIndex = node->realElementNum;
    node->realElementNum++;
    if (node->realElementNum > DM_DELTA_UPDATE_MAX_OPERATIONS) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "Append ele in delta vectorNode %s.", node->nodeDesc->name);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    // 切换到最后的Record，以备插入值
    NodeSetElementIndexWithoutCheck(node, recordIndex);
    return GMERR_OK;
}

Status NodeVectorAppendWithoutCheck(DmNodeT *node)
{
    DB_POINTER(node);
    // 需要扩容
    if (node->realElementNum == node->totalElementNum) {
        uint32_t newElementNum = VectorNodeGetExpandedSize(node);
        Status ret = VectorElementExpand(node, newElementNum, true);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    // totalElementNum是否增加，realElementNum都需要加1
    uint32_t recordIndex = node->realElementNum;
    node->realElementNum++;
    // 切换到最后的Record，以备插入值
    NodeSetElementIndexWithoutCheck(node, recordIndex);
    return GMERR_OK;
}

inline Status DmNodeVectorAppend(DmNodeT *node)
{
    DB_POINTER(node);
    if (node->nodeDesc->nodeType == DM_NODE_RECORD || node->nodeDesc->nodeType == DM_NODE_ARRAY) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Append op only allowed for vectorNode.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    // 容器已满，报错
    if (node->realElementNum >= node->nodeDesc->maxElementNum) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION,
            "Append ele to vector, realElementNum (%" PRIu32 ") exceeds max"
            " recordNum (%" PRIu32 ") in vectorNode %s.",
            node->realElementNum, node->nodeDesc->maxElementNum, node->nodeDesc->name);
        return GMERR_DATA_EXCEPTION;
    }
    return NodeVectorAppendWithoutCheck(node);
}

Status DmVectorClear(DmNodeT *node)
{
    DB_POINTER(node);
    Status ret = GMERR_OK;
    // 语义校验默认值场景需要全部清除
    for (uint32_t i = 0; i < node->totalElementNum; i++) {
        NodeSetElementIndexWithoutCheck(node, i);
        ret = ResetRecord(node->currRecord);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        for (uint32_t j = 0; j < node->nodeDesc->nodeNumPerElement; j++) {
            if (node->currNodes[j] == NULL) {
                continue;
            }
            node->currNodes[j]->isCreated = false;
            node->currNodes[j]->realElementNum = 0;
        }
    }
    return GMERR_OK;
}

Status DmNodeVectorRemove(DmNodeT *node, uint32_t index)
{
    DB_POINTER(node);
    DmNodeDescT *nodeDesc = node->nodeDesc;
    if (nodeDesc->nodeType != DM_NODE_VECTOR || index >= node->realElementNum) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Remove ele from vector, "
                                                           "only allowed vector or exceeds realEleNum.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    RecordDescT *recordDesc = node->currRecord->recordDesc;
    // 删除当前index位置Record，后面的内存往前移动
    // 当前待删除record后的record总数
    uint32_t latterElementNum = (node->totalElementNum - index) - 1;
    // 当前待删除record后的buf大小
    uint32_t latterBufSize = recordDesc->runningBufLen * latterElementNum;
    // 当前待删除record后一个record元素的起始buf addr
    uint8_t *latterBufAddr = node->runningBuf + recordDesc->runningBufLen * (index + 1);

    // 当前待删除record后的propeIsSetValue buf大小
    uint32_t latterNullInfoSize = recordDesc->propeNum * latterElementNum;
    // 当前待删除record后一个record元素的propeIsSetValue addr
    int8_t *latterNullInfoAddr = node->propeIsSetValue + recordDesc->propeNum * (index + 1);

    // 当前待删除record后的propeIsEmpty buf大小
    uint32_t latterEmptyInfoSize = recordDesc->propeNum * latterElementNum;
    // 当前待删除record后一个record元素的propeIsEmpty addr
    int8_t *latterEmtpyInfoAddr = node->propeIsEmpty + recordDesc->propeNum * (index + 1);

    // latterBufSize为零，说明当前record为最后一个元素,不用处理
    if (latterBufSize != 0) {
        errno_t err = memmove_s(latterBufAddr - recordDesc->runningBufLen, latterBufSize, latterBufAddr, latterBufSize);
        DB_ASSERT(err == EOK);
        err = memmove_s(
            latterNullInfoAddr - recordDesc->propeNum, latterNullInfoSize, latterNullInfoAddr, latterNullInfoSize);
        DB_ASSERT(err == EOK);
        err = memmove_s(
            latterEmtpyInfoAddr - recordDesc->propeNum, latterEmptyInfoSize, latterEmtpyInfoAddr, latterEmptyInfoSize);
        DB_ASSERT(err == EOK);
    }
    node->realElementNum--;
    // 删除后的初始化操作，这里初始化后，缩容就不用初始化了
    Status ret = NodeRunningBufInit(node, node->realElementNum, node->realElementNum + 1, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = 0; i < nodeDesc->nodeNumPerElement; i++) {
        ret = DmResetNode(node->nodes[node->realElementNum * nodeDesc->nodeNumPerElement + i]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    // 需要缩容
    if (node->totalElementNum > node->realElementNum + nodeDesc->vectorExtendSize) {
        ret = VectorElementShrink(node);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return GMERR_OK;
}

Status DmNodeGetChildNodeByNameSilent(const DmNodeT *node, const char *propertyName, DmNodeT **childNode)
{
    DB_POINTER3(node, propertyName, childNode);
    for (uint32_t i = 0; i < node->nodeDesc->nodeNumPerElement; i++) {
        if (strcmp(propertyName, node->nodeDesc->childNodeDescs[i]->name) == 0) {
            return DmNodeGetChildNodeByIndex(node, i, childNode);
        }
    }
    return GMERR_INVALID_NAME;
}

Status DmNodeGetChildNodeByName(const DmNodeT *node, const char *propertyName, DmNodeT **childNode)
{
    DB_POINTER3(node, propertyName, childNode);
    Status ret = DmNodeGetChildNodeByNameSilent(node, propertyName, childNode);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Get %s's childNode by name, %s.", node->nodeDesc->name, propertyName);
        return ret;
    }

    return GMERR_OK;
}

Status DmNodeGetChildNodeById(const DmNodeT *node, uint32_t id, DmNodeT **childNode)
{
    DB_POINTER2(node, childNode);
    for (uint32_t i = 0; i < node->nodeDesc->nodeNumPerElement; i++) {
        if (id == node->nodeDesc->childNodeDescs[i]->nodeId) {
            return DmNodeGetChildNodeByIndex(node, i, childNode);
        }
    }
    DB_LOG_AND_SET_LASERR(
        GMERR_INVALID_PARAMETER_VALUE, "Get childNode by ID, %s, ID %" PRIu32 ".", node->nodeDesc->name, id);
    return GMERR_INVALID_PARAMETER_VALUE;
}

Status DmNodeGetChildNodeByIndex(const DmNodeT *node, uint32_t index, DmNodeT **childNode)
{
    DB_POINTER2(node, childNode);
    // 内部参数，不满足一定是编码问题，直接进行assert
    DB_ASSERT(index < node->nodeDesc->nodeNumPerElement * node->totalElementNum);
    if (node->currNodes[index] == NULL) {
        DmNodeDescT *nodeDesc = node->nodeDesc->childNodeDescs[index % node->nodeDesc->nodeNumPerElement];
        Status ret = DmCreateEmptyNode(node->memCtx, nodeDesc, &node->currNodes[index], true);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "Get childNode by Index, %s, Index %" PRIu32 ".", node->nodeDesc->name, index);
            return ret;
        }
    }
    *childNode = node->currNodes[index];
    return GMERR_OK;
}

void DmNodeGetSize(const DmNodeT *node, uint32_t *size)
{
    DB_POINTER2(node, size);
    *size = node->realElementNum;
}

/*
 * 对数组节点进行排序（选择排序，非稳定排序）
 * 首先基于传入的属性名，到Node中所有record提取对应的属性值，构成DmValue数组，用于支持排序（DmValue可比较）
 * 然后基于DmValue数组进行排序，采用选择排序算法，排序过程中同时交换数组节点中record记录
 * 排序属性类型不支持bool类型，bitmap，bitfield
 */
Status DmNodeSort(DmNodeT *node, const char *propeName, bool isAscending)
{
    DB_POINTER2(node, propeName);
    uint32_t elementNum = node->realElementNum;
    if (elementNum <= 1) {
        return GMERR_OK;
    }
    // step 1: 基于传入的属性名，到Node中所有record提取对应的属性值，构成DmValue数组，用于支持排序（DmValue可比较）
    // 内存释放逻辑：本函数释放
    DmValueT *values = (DmValueT *)DbDynMemCtxAlloc(node->memCtx, elementNum * sizeof(DmValueT));
    if (values == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Sort node, size: %zu", elementNum * sizeof(DmValueT));
        return GMERR_OUT_OF_MEMORY;
    }

    Status ret = ExtractSortPropertyValues(node, propeName, &values[0], elementNum);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(node->memCtx, values);
        return ret;
    }

    // step 2：基于DmValue数组进行排序，采用选择排序算法，排序过程中同时交换数组节点中record记录
    ret = NodeSortInner(node, &values[0], elementNum, isAscending);

    // step 3：将DmValue数组中申请的内存释放掉
    if (DM_TYPE_NEED_MALLOC(values[elementNum - 1].type)) {
        for (uint32_t i = 0; i < elementNum; i++) {
            DbDynMemCtxFree(node->memCtx, (void *)values[i].value.strAddr);
        }
    }
    DbDynMemCtxFree(node->memCtx, values);
    return ret;
}

Status ExtractSortPropertyValues(DmNodeT *node, const char *propeName, DmValueT *values, uint32_t valuesNum)
{
    NodeSetElementIndexWithoutCheck(node, 0);
    DmPropertyInfoT *propeInfo = NULL;
    Status ret = RecordGetPropeInfoByName(node->currRecord, propeName, &propeInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (propeInfo->dataType == DB_DATATYPE_BOOL || propeInfo->dataType == DB_DATATYPE_NULL ||
        propeInfo->dataType == DB_DATATYPE_BITMAP || DmIsBitFieldType(propeInfo->dataType)) {
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "Extract sorted prop values, dataType"
                                                       " not supported");
        return GMERR_DATATYPE_MISMATCH;
    }
    bool needMalloc = DM_TYPE_NEED_MALLOC(propeInfo->dataType);
    uint32_t propeSize;
    for (uint32_t i = 0; i < valuesNum; i++) {
        NodeSetElementIndexWithoutCheck(node, i);
        values[i].type = propeInfo->dataType;
        if (needMalloc) {
            ret = DmNodeGetPropeSizeByName(node, propeName, &propeSize);
            if (ret != GMERR_OK) {
                break;
            }
            values[i].value.length = propeSize;
            // 内存释放逻辑：DmNodeSort
            values[i].value.strAddr = DbDynMemCtxAlloc(node->memCtx, propeSize);
            if (values[i].value.strAddr == NULL) {
                DB_LOG_AND_SET_LASERR(
                    GMERR_OUT_OF_MEMORY, "StrAddr when extract sorted prop values, size: %" PRIu32 ".", propeSize);
                ret = GMERR_OUT_OF_MEMORY;
                break;
            }
        }
        ret = DmNodeGetPropeByName(node, propeName, &values[i]);
        if (ret != GMERR_OK) {
            break;
        }
    }
    if (ret != GMERR_OK) {
        if (needMalloc) {  // 出现错误后释放申请的内存
            for (uint32_t i = 0; i < valuesNum; i++) {
                DbDynMemCtxFree(node->memCtx, (void *)values[i].value.strAddr);
            }
        }
    }
    return ret;
}

Status NodeSortInner(const DmNodeT *node, DmValueT *values, uint32_t valuesNum, bool isAscending)
{
    uint32_t bufLen = node->currRecord->recordDesc->runningBufLen;
    uint32_t nullInfoLen = node->currRecord->recordDesc->propeNum * (uint32_t)sizeof(int8_t);
    // 内存释放逻辑：本函数内释放
    uint8_t *bufTmp = DbDynMemCtxAlloc(node->memCtx, bufLen + nullInfoLen);  // 优化性能，一起申请空间
    if (bufTmp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Node sort inner, bufTmp size: %" PRId32 ".", bufLen + nullInfoLen);
        return GMERR_OUT_OF_MEMORY;
    }
    int8_t *nullInfoTmp = (int8_t *)(bufTmp + bufLen);

    // step1: 处理数组节点中指定属性值为NULL的record记录，将null属性值对应的record放在最前面
    uint32_t nullValueCount = 0;
    ProcessNullForNodeSort(node, bufTmp, nullInfoTmp, values, &nullValueCount);
    // step2: 对剩余的record记录进行排序
    bool cmpFlag = false;
    Status ret;
    for (uint32_t i = nullValueCount; i < valuesNum - 1; i++) {
        uint32_t extremeIdx = i;
        for (uint32_t j = i + 1; j < valuesNum; j++) {
            if (isAscending) {
                ret = DmValueLessThan(&values[j], &values[extremeIdx], &cmpFlag);
                extremeIdx = (cmpFlag ? j : extremeIdx);
            } else {
                ret = DmValueGreaterThan(&values[j], &values[extremeIdx], &cmpFlag);
                extremeIdx = (cmpFlag ? j : extremeIdx);
            }
            if (ret != GMERR_OK) {
                DbDynMemCtxFree(node->memCtx, bufTmp);
                return ret;
            }
        }
        if (i != extremeIdx) {
            DmValueSwap(&values[i], &values[extremeIdx]);
            // 根据DmValue排序结果对record中runningBuf和propeIsSetValue进行交换
            ElementSwapForNodeSort(node, bufTmp, nullInfoTmp, i, extremeIdx);
        }
    }
    DbDynMemCtxFree(node->memCtx, bufTmp);
    return GMERR_OK;
}

void ElementSwapForNodeSort(
    const DmNodeT *node, uint8_t *bufTmp, int8_t *nullInfoTmp, uint32_t prevIdx, uint32_t extremeIdx)
{
    // step 1: 交换元素中的普通属性（Record）
    uint32_t bufLen = node->currRecord->recordDesc->runningBufLen;
    uint32_t nullInfoLen = node->currRecord->recordDesc->propeNum * (uint32_t)sizeof(int8_t);
    uint32_t prevBufOffset = prevIdx * bufLen;
    uint32_t nextBufOffset = extremeIdx * bufLen;
    uint32_t prevNullOffset = prevIdx * nullInfoLen;
    uint32_t nextNullOffset = extremeIdx * nullInfoLen;
    errno_t err;
    if (bufLen > 0) {
        err = memcpy_s(bufTmp, bufLen, node->runningBuf + prevBufOffset, bufLen);
        DB_ASSERT(err == EOK);
        err = memcpy_s(node->runningBuf + prevBufOffset, bufLen, node->runningBuf + nextBufOffset, bufLen);
        DB_ASSERT(err == EOK);
        err = memcpy_s(node->runningBuf + nextBufOffset, bufLen, bufTmp, bufLen);
        DB_ASSERT(err == EOK);
    }
    if (nullInfoLen > 0) {
        err = memcpy_s(nullInfoTmp, nullInfoLen, node->propeIsSetValue + prevNullOffset, nullInfoLen);
        DB_ASSERT(err == EOK);
        err = memcpy_s(
            node->propeIsSetValue + prevNullOffset, nullInfoLen, node->propeIsSetValue + nextNullOffset, nullInfoLen);
        DB_ASSERT(err == EOK);
        err = memcpy_s(node->propeIsSetValue + nextNullOffset, nullInfoLen, nullInfoTmp, nullInfoLen);
        DB_ASSERT(err == EOK);
    }
    // step 2: 交换元素中的非普通属性（子树节点）
    DmNodeT *nodePtrTmp = NULL;
    uint32_t nodeNumPerElement = node->nodeDesc->nodeNumPerElement;
    uint32_t prevNodePos = prevIdx * nodeNumPerElement;
    uint32_t nextNodePos = extremeIdx * nodeNumPerElement;
    for (uint32_t i = 0; i < nodeNumPerElement; i++) {
        nodePtrTmp = node->nodes[prevNodePos + i];
        node->nodes[prevNodePos + i] = node->nodes[nextNodePos + i];
        node->nodes[nextNodePos + i] = nodePtrTmp;
    }
}

void ProcessNullForNodeSort(
    const DmNodeT *node, uint8_t *bufTmp, int8_t *nullInfoTmp, DmValueT *values, uint32_t *nullValueCount)
{
    // i从前往后找到不是null值的记录
    for (uint32_t i = 0; i < node->realElementNum - 1; i++) {
        if (values[i].type == DB_DATATYPE_NULL) {
            (*nullValueCount)++;
            continue;
        }
        // j从i+1往后找到null值的记录，和i进行交换
        uint32_t j = i + 1;
        for (; j < node->realElementNum; j++) {
            if (values[j].type != DB_DATATYPE_NULL) {
                continue;
            }
            (*nullValueCount)++;
            DmValueSwap(&values[i], &values[j]);
            ElementSwapForNodeSort(node, bufTmp, nullInfoTmp, i, j);
            break;
        }
        // 如果j走到最后也没有null值，此时可以直接退出
        if (j == node->realElementNum - 1) {
            break;
        }
    }
}

Status DmNodeLookup(DmNodeT *node, const DmIndexKeyT *key, uint32_t *recordIndex)
{
    DB_POINTER3(node, key, recordIndex);

    const DmIndexKeyBufInfoT *indexKeyInfo = NULL;
    Status ret = NodeGetIndexKeyInfo(node, key->indexId, &indexKeyInfo);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint8_t *keyBuf = node->nodeKeyBuf;
    for (uint32_t i = 0; i < node->realElementNum; i++) {
        NodeSetElementIndexWithoutCheck(node, i);
        uint32_t keyBufLen = 0;
        RecordGetKeyBuf(node->currRecord, indexKeyInfo, keyBuf, &keyBufLen);
        if (key->keyBufLen == keyBufLen && memcmp(keyBuf, key->keyBuf, keyBufLen) == 0) {
            *recordIndex = i;
            return GMERR_OK;
        }
    }
    DB_LOG_AND_SET_LASERR(GMERR_NO_DATA, "Record not found when look up.");
    return GMERR_NO_DATA;
}

Status DmNodeLookupFromRecordBuf(DmNodeT *node, const DmIndexKeyT *key, uint32_t *recordIndex)
{
    DB_POINTER3(node, key, recordIndex);

    const DmIndexKeyBufInfoT *indexKeyInfo = NULL;
    Status ret = NodeGetIndexKeyInfo(node, key->indexId, &indexKeyInfo);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint8_t *keyBuf = node->nodeKeyBuf;
    for (uint32_t i = 0; i < node->realElementNum; i++) {
        NodeSetElementIndexWithoutCheck(node, i);
        uint32_t keyBufLen = 0;
        // 处理recordLen
        uint8_t *recordBufCursor = node->nodeSeriBuf[i];
        uint32_t recordLength, varintByteNum;
        DmConvertVarintToUint32(*((uint32_t *)recordBufCursor), &recordLength, &varintByteNum);
        recordBufCursor += varintByteNum;
        GetKeyBufFromRecordBuf(indexKeyInfo, recordBufCursor, keyBuf, &keyBufLen);
        if (key->keyBufLen == keyBufLen && memcmp(keyBuf, key->keyBuf, keyBufLen) == 0) {
            *recordIndex = i;
            return GMERR_OK;
        }
    }
    DB_LOG_AND_SET_LASERR(GMERR_NO_DATA, "Record not found when look up form recordBuf.");
    return GMERR_NO_DATA;
}

Status NodeGetIndexKeyInfo(const DmNodeT *node, uint32_t indexId, const DmIndexKeyBufInfoT **indexKeyInfo)
{
    DmNodeDescT *nodeDesc = node->nodeDesc;
    for (uint32_t i = 0; i < nodeDesc->indexNum; i++) {
        if (GetKeyBufInfoForNode(nodeDesc, i)->indexId == indexId) {
            *indexKeyInfo = GetKeyBufInfoForNode(nodeDesc, i);
            return GMERR_OK;
        }
    }
    DB_LOG_AND_SET_LASERR(
        GMERR_DATA_EXCEPTION, "Get indexKeyInfo in node %s, not match index ID: %" PRIu32 "", nodeDesc->name, indexId);
    return GMERR_DATA_EXCEPTION;
}

Status DmNodeGetMemberKey(DmNodeSchemaT *nodeSchema, const char *keyName, DmVlIndexLabelT **memberKey)
{
    DB_POINTER3(nodeSchema, keyName, memberKey);
    DmVlIndexLabelT *memberKeys = MEMBER_PTR(nodeSchema, memberKeys);
    for (uint32_t i = 0; i < nodeSchema->memberKeyNum; i++) {
        if (strcmp(MEMBER_PTR5(&memberKeys[i], indexName), keyName) == 0) {
            *memberKey = &memberKeys[i];
            return GMERR_OK;
        }
    }
    DB_LOG_AND_SET_LASERR(GMERR_INVALID_NAME, "Get memKey by name in node %s, inv memKey name %s.",
        MEMBER_PTR(nodeSchema, name), keyName);
    return GMERR_INVALID_NAME;
}

Status DmNodeGetMemberKeyById(DmNodeSchemaT *nodeSchema, const uint32_t id, DmVlIndexLabelT **memberKey)
{
    DB_POINTER2(nodeSchema, memberKey);
    DmVlIndexLabelT *memberKeys = MEMBER_PTR(nodeSchema, memberKeys);
    for (uint32_t i = 0; i < nodeSchema->memberKeyNum; i++) {
        if (memberKeys[i].globalIndexId == id) {
            *memberKey = &memberKeys[i];
            return GMERR_OK;
        }
    }
    DB_LOG_AND_SET_LASERR(
        GMERR_INVALID_PROPERTY, "Get memKey by id %" PRIu32 " in node %s", id, MEMBER_PTR(nodeSchema, name));
    return GMERR_INVALID_PROPERTY;
}

char *DmNodeGetName(const DmNodeT *node)
{
    DB_POINTER(node);
    return node->nodeDesc->name;
}

uint32_t DmNodeGetMemberKeyNum(const DmNodeT *node)
{
    DB_POINTER(node);
    return node->nodeDesc->indexNum;
}

uint32_t DmNodeGetPerElementNodeNum(const DmNodeT *node)
{
    DB_POINTER(node);
    return node->nodeDesc->nodeNumPerElement;
}

uint32_t DmNodeGetMaxElementNum(const DmNodeT *node)
{
    DB_POINTER(node);
    return node->nodeDesc->maxElementNum;
}

uint32_t DmNodeGetPropeNum(const DmNodeT *node)
{
    DB_POINTER(node);
    return node->nodeDesc->recordDesc->propeNum;
}

Status ElementIsSame(DmNodeT *fromNode, DmNodeT *toNode, uint32_t elementIdx, bool *isSame)
{
    NodeSetElementIndexWithoutCheck(fromNode, elementIdx);
    NodeSetElementIndexWithoutCheck(toNode, elementIdx);
    Status ret = RecordIsSame(fromNode->currRecord, toNode->currRecord, isSame);
    if (!(*isSame) || ret != GMERR_OK) {
        return ret;
    }

    // 挨个比较元素中的子树节点
    for (uint32_t i = 0; i < fromNode->nodeDesc->nodeNumPerElement; i++) {
        ret = NodeIsSame(fromNode->currNodes[i], toNode->currNodes[i], isSame);
        if (!(*isSame) || ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status NodeIsSame(DmNodeT *fromNode, DmNodeT *toNode, bool *isSame)
{
    if (fromNode == NULL && toNode == NULL) {
        *isSame = true;
        return GMERR_OK;
    }
    if (fromNode == NULL || toNode == NULL) {
        *isSame = false;
        return GMERR_OK;
    }
    if (fromNode->realElementNum != toNode->realElementNum) {
        *isSame = false;
        DB_LOG_INFO("realElementNum in node %s not same!, realElementNum in fromNode: %" PRIu32
                    " , realElementNum in fromNode: %" PRIu32 " .",
            fromNode->nodeDesc->name, fromNode->realElementNum, toNode->realElementNum);
        return GMERR_OK;
    }
    Status ret;
    for (uint32_t i = 0; i < fromNode->realElementNum; i++) {
        ret = ElementIsSame(fromNode, toNode, i, isSame);
        if (!(*isSame) || ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}
