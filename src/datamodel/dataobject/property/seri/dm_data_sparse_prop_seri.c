/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: source file for sparse data model vertex serialize
 * Author:
 * Create: 2024-08-20
 */

#include "db_memcpy.h"
#include "db_se_heap_serialization.h"
#include "dm_data_seri_base_in.h"
#include "dm_data_seri_sparse_in.h"
#include "dm_yang_interface.h"
#include "dm_log.h"

SeHpTupleAddrMode g_heapTupleAddrMode = SE_HEAP_TUPLE_ADDR_64;
// DmEdgeAddrsT中的元素个数
#define ADDR_NUM_IN_DM_EDGE_ADDRS 2
// DmEdgeAddrsT序列化长度
#define SERI_DM_EDGE_ADDRS_LEN ADDR_NUM_IN_DM_EDGE_ADDRS *GetHeapTupleAddrLen(g_heapTupleAddrMode)

void DmSetAddrMode(SeHpTupleAddrMode addrMode)
{
    g_heapTupleAddrMode = addrMode;
}

SeHpTupleAddrMode DmGetAddrMode(void)
{
    return g_heapTupleAddrMode;
}

// 获取edgeAddr长度
uint32_t DmGetAddrSeriLen(void)
{
    return (uint32_t)SERI_DM_EDGE_ADDRS_LEN;
}

// -----------------序列化反序列化相关函数实现-------------------
DM_SPARSE_FUN Status SparseVertexGetComplexModelSeriLen(const DmVertexT *vertex, uint32_t *length)
{
    // 该函数中的入参length是累加的
    uint32_t nodeNum = vertex->vertexDesc->nodeNum;
    Status ret;

    // 获取子树长度
    uint32_t chileNodesLen = 0;
    for (uint32_t i = 0; i < nodeNum; i++) {
        ret = SparseNodeGetSeriBufLength(vertex->nodes[i], &chileNodesLen, vertex->isDeltaVertex);
        if (ret != GMERR_OK) {
            return ret;
        }
        *length += chileNodesLen;
    }
    return GMERR_OK;
}

// 客户端传入的vertex无边信息，末尾edgeAddr的长度
DM_SPARSE_FUN uint32_t SparseVertexGetEdgeAddrInfoLen(uint32_t edgeLabelNum)
{
    uint32_t varintByteNum = 0;
    DmGetVarint2Len(edgeLabelNum, &varintByteNum);
    return varintByteNum + DM_NULL_INFO_SERI_BYTES(edgeLabelNum);
}

DM_SPARSE_FUN uint32_t SparseVertexGetEdgeAddrLen(const DmVertexT *vertex)
{
    DB_POINTER(vertex);
    uint32_t edgeAddrNum = 0;
    uint32_t edgeLabelNum = vertex->edgeLabelNum;
    for (uint32_t i = 0; i < edgeLabelNum; i++) {
        if (vertex->edgeAddrsEntry[i].first != 0) {
            edgeAddrNum++;
        }
    }
    return SparseVertexGetEdgeAddrInfoLen(edgeLabelNum) + (uint32_t)(edgeAddrNum * SERI_DM_EDGE_ADDRS_LEN);
}

DM_SPARSE_FUN uint32_t SparseGetFirstEdgeAddrsLenFromBuf(uint32_t edgeLabelNum, const uint8_t *buf)
{
    // 边压缩场景，需要根据edgeAddrInfo计算实际边长度
    // 计算edgeAddrInfo长度
    uint32_t edgeAddrInfoByteNum = DM_NULL_INFO_SERI_BYTES(edgeLabelNum);
    // 从edgeNullInfo中读取边的数目
    uint32_t realEdgeNum = 0;
    for (uint32_t i = 0; i < edgeLabelNum; i++) {
        if (DmGetBitValueFromUint8Arr(buf, i)) {
            realEdgeNum++;
        }
    }
    return edgeAddrInfoByteNum + realEdgeNum * SERI_DM_EDGE_ADDRS_LEN;
}

DM_SPARSE_FUN Status SparseVertexGetSeriBufLength(DmVertexT *vertex, uint32_t *length)
{
    // vertex->vertexType，用一个字节存储vertexType信息
    uint32_t lengthTmp = (uint32_t)sizeof(uint8_t);

    // record序列化长度
    uint32_t recordSeriLen = 0;
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    Status ret = SparseRecordGetSeriBufLength(vertex->record, &recordSeriLen, vertex->isDeltaVertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    lengthTmp += recordSeriLen;

    // 处理node
    ret = SparseVertexGetComplexModelSeriLen(vertex, &lengthTmp);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // vertex总长度
    uint32_t varintByteNum = 0;
    DmGetTotalBufVarint2Len(lengthTmp, &varintByteNum);
    lengthTmp += varintByteNum;

    // 系统字段区域
    lengthTmp += DmGetSysPropeSeriBufLen(vertexDesc);

    // yang Npa表区
    if (vertexDesc->npaCount != 0) {
        lengthTmp += DM_NULL_INFO_SERI_BYTES(vertexDesc->npaCount);
    }

    // 关联边标签首尾边addr存储区长度
    lengthTmp += SparseVertexGetEdgeAddrLen(vertex);
    *length = lengthTmp;
    return GMERR_OK;
}

DM_SPARSE_FUN void SparseSerializeVertexTotalLen(const DmVertexT *vertex, uint32_t length, uint8_t **bufCursor)
{
    uint32_t varintByteNum = 0;
    // 记录序列化buf的总长度，写进buf的总长度为totalLength减去边addr存储区长度,系统字段的长度,npa表bitmap长度
    uint32_t edgeAddrLen = DmVertexGetEdgeAddrLen(vertex);
    size_t bufLen = ((length - edgeAddrLen) - DmGetSysPropeSeriBufLen(vertex->vertexDesc) -
                     DM_NULL_INFO_SERI_BYTES(vertex->vertexDesc->npaCount));
    DmConvertVarint2ToBuf((uint32_t)bufLen, *bufCursor, &varintByteNum);
    (*bufCursor) += varintByteNum;
}

DM_SPARSE_FUN void SeriNpaBitmap(const DmVertexT *vertex, uint8_t **bufCursor)
{
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    if (vertexDesc->npaCount == 0) {
        return;
    }
    uint32_t npaBitMapLen = DM_NULL_INFO_SERI_BYTES(vertexDesc->npaCount);
    DbFastMemcpy(*bufCursor, npaBitMapLen, vertex->npaBitmap, npaBitMapLen);
    *bufCursor += npaBitMapLen;
}

DM_SPARSE_FUN void CompressEdgeAddrToBuf(uint64_t edgeAddr, uint8_t **buf)
{
    errno_t err;
    if (g_heapTupleAddrMode == SE_HEAP_TUPLE_ADDR_32) {
        TuplePointer32T addr = HeapCompressTupleAddr32(edgeAddr);
        err = memcpy_s(*buf, SE_HEAP_TUPLE_ADDR_LEN_32, &addr, SE_HEAP_TUPLE_ADDR_LEN_32);
        DB_ASSERT(err == EOK);
        *buf += SE_HEAP_TUPLE_ADDR_LEN_32;
    } else if (g_heapTupleAddrMode == SE_HEAP_TUPLE_ADDR_48) {
        TuplePointer48T addr = HeapCompressTupleAddr48(edgeAddr);
        err = memcpy_s(*buf, SE_HEAP_TUPLE_ADDR_LEN_48, &addr, SE_HEAP_TUPLE_ADDR_LEN_48);
        DB_ASSERT(err == EOK);
        *buf += SE_HEAP_TUPLE_ADDR_LEN_48;
    } else {
        // addr不压缩，仍使用uint64类型存储
        *(uint64_t *)*buf = edgeAddr;
        *buf += SE_HEAP_TUPLE_ADDR_LEN_64;
    }
}

DM_SPARSE_FUN void SerializeSingleEdgeAddrs(DmEdgeAddrsT edgeAddr, uint8_t **edgeAddrCursor)
{
    CompressEdgeAddrToBuf(edgeAddr.first, edgeAddrCursor);
    CompressEdgeAddrToBuf(edgeAddr.last, edgeAddrCursor);
}

// 根据db的压缩模式，从buf中解压edgeAddr
DM_SPARSE_FUN void UncompressEdgeAddrFromBuf(uint64_t *edgeAddr, uint8_t **buf)
{
    if (g_heapTupleAddrMode == SE_HEAP_TUPLE_ADDR_32) {
        TuplePointer32T addr = *(TuplePointer32T *)*buf;
        *edgeAddr = HeapUncompressTupleAddr32(addr);
        *buf += SE_HEAP_TUPLE_ADDR_LEN_32;
    } else if (g_heapTupleAddrMode == SE_HEAP_TUPLE_ADDR_48) {
        TuplePointer48T addr = *(TuplePointer48T *)*buf;
        *edgeAddr = HeapUncompressTupleAddr48(addr);
        *buf += SE_HEAP_TUPLE_ADDR_LEN_32;
    } else {
        // addr不压缩，仍使用uint64类型存储
        *edgeAddr = *(uint64_t *)*buf;
        *buf += SE_HEAP_TUPLE_ADDR_LEN_64;
    }
}

// 序列化edgeAddr
DM_SPARSE_FUN Status SparseSerializeEdgeAddrs(const DmVertexT *vertex, uint8_t **buf)
{
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    uint32_t edgeLabelNum = vertex->edgeLabelNum;
    if (SECUREC_UNLIKELY(edgeLabelNum != vertexDesc->edgeLabelNum)) {
        // The DML or DQL operation cannot be performed before the DDL operation is complete
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
            "Sparse seri edgeNum in vertex (%" PRIu32 ") not equal in the Desc (%" PRIu32 ").", edgeLabelNum,
            vertexDesc->edgeLabelNum);
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t varintByteNum = 0;
    DmConvertVarint2ToBuf(edgeLabelNum, *buf, &varintByteNum);
    *buf += varintByteNum;
    if (SECUREC_LIKELY(edgeLabelNum == 0)) {
        return GMERR_OK;
    }

    // 动态预留方案，edgeInfo标识当前是否有边数据
    uint32_t edgeInfoByteNum = DM_NULL_INFO_SERI_BYTES(edgeLabelNum);
    uint8_t *edgeAddrBuf = *buf;
    edgeAddrBuf += edgeInfoByteNum;

    for (uint32_t i = 0; i < edgeLabelNum; i++) {
        if (vertex->edgeAddrsEntry[i].first != 0) {
            DmSetBitTrueIntoUint8Arr(*buf, i);
            // 压缩边
            SerializeSingleEdgeAddrs(vertex->edgeAddrsEntry[i], &edgeAddrBuf);
        }
    }
    *buf = edgeAddrBuf;
    return GMERR_OK;
}

DM_SPARSE_FUN Status SparseSerializeVertex2InvokerBuf(const DmVertexT *vertex, uint32_t length, uint8_t *buf)
{
    (void)memset_s(buf, length, 0x00, length);  // 不初始化会有功能问题，如序列化nullInfo信息区

    // 出于安全考虑totalLength应调用DmVertexGetSeriBufLength获取，然后再与入参length比对一下
    // 在性能场景下先默认length与totalLength一致（假设length是正确的），最后利用序列化后的buf偏移量与length进行对比
    // 如果length是错误的，可能会导致内存脏写
    uint8_t *bufHeader = buf;
    uint8_t *bufCursor = buf;

    // 序列化系统字段区
    DmSeriSysPrope(vertex, &bufCursor);

    // vertexHead
    uint8_t firstByteValue = 0;
    EncodeVertexHead(vertex, &firstByteValue);
    *(uint8_t *)bufCursor = firstByteValue;
    bufCursor += (uint32_t)sizeof(uint8_t);

    // 记录vertex总长度
    SparseSerializeVertexTotalLen(vertex, length, &bufCursor);

    // 序列化record
    uint32_t nodeNum = vertex->vertexDesc->nodeNum;
    Status ret = SparseRecordSerialize(vertex->record, &bufCursor, vertex->isDeltaVertex, vertex->nodes, nodeNum);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 序列化node
    for (uint32_t i = 0; i < nodeNum; i++) {
        ret = SparseNodeSerialize(vertex->nodes[i], &bufCursor, vertex->isDeltaVertex, false);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // 序列化yang里的npa表
    SeriNpaBitmap(vertex, &bufCursor);

    // 序列化边
    ret = SparseSerializeEdgeAddrs(vertex, &bufCursor);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (SECUREC_UNLIKELY((uintptr_t)bufCursor - (uintptr_t)bufHeader != length)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
            "vertex seri len(%td) "
            " not equal expected len(%" PRIu32 ").",
            (uintptr_t)bufCursor - (uintptr_t)bufHeader, length);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

DM_SPARSE_FUN Status SparseFirstEdgeAddrBufCheckIsLegal(
    const DmVertexT *vertex, uint8_t **buf, const uint8_t *bufTail, bool isFromMsg)
{
    uint32_t varintByteNum;
    uint32_t edgeLabelNum = 0;
    uint32_t realLen = (uintptr_t)*buf - (uintptr_t)bufTail;
    Status ret = DmDecodeVarint2WithCheck(*buf, &edgeLabelNum, &varintByteNum, realLen);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Sparse vertexBuf edgeLabelNum");
        return ret;
    }
    *buf += varintByteNum;

    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    if (edgeLabelNum != vertexDesc->edgeLabelNum) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
            "edgeLabelNum in sparse label %s is %" PRIu32 ", edgeLabelNum in Desc is %" PRIu32 ".",
            vertexDesc->labelName, edgeLabelNum, vertexDesc->edgeLabelNum);
        return GMERR_DATA_EXCEPTION;
    }

    if (vertexDesc->edgeLabelNum != vertex->edgeLabelNum) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
            "edgeNum in sparse vertex (%" PRIu32 ") not equal in Desc (%" PRIu32 ").", vertex->edgeLabelNum,
            vertex->vertexDesc->edgeLabelNum);
        return GMERR_DATA_EXCEPTION;
    }

    // 校验edgeAddrInfo
    uint32_t edgeAddrInfoByteNum = DM_NULL_INFO_SERI_BYTES(edgeLabelNum);
    if ((*buf + edgeAddrInfoByteNum) > bufTail) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATA_EXCEPTION, "Sparse vertexBuf edge, edgeAddrInfoByteNum is %" PRIu32 ".", edgeAddrInfoByteNum);
        return GMERR_DATA_EXCEPTION;
    }

    size_t firstEdgeAddrsLen = SparseGetFirstEdgeAddrsLenFromBuf(edgeLabelNum, (const uint8_t *)*buf);
    if ((*buf + firstEdgeAddrsLen) > bufTail) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATA_EXCEPTION, "Sparse vertexBuf edge, firstEdgeAddrsLen is %zu .", firstEdgeAddrsLen);
        return GMERR_DATA_EXCEPTION;
    }
    if (isFromMsg && firstEdgeAddrsLen != 0 && vertexDesc->vertexLabelType == VERTEX_TYPE_YANG) {
        if (!DbBufferIsAllZero(*buf, firstEdgeAddrsLen)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Sparse vertexBuf edge, Topo is all zero");
            return GMERR_DATA_EXCEPTION;
        }
    }
    *buf += firstEdgeAddrsLen;
    if (*buf != bufTail) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "deseri sparse vertex edgeLen mistake.");
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

DM_SPARSE_FUN Status SparseVertexLenCheckIsLegal(
    DmVertexT *vertex, uint8_t *vertexBuf, uint8_t **bufCursor, uint8_t **nodeTail, uint8_t *bufTail)
{
    uint8_t *bufPtr = *bufCursor;
    uint32_t varintByteNum;
    uint32_t bufLen = 0;
    // 校验totalLen
    uint32_t realLen = (uintptr_t)*bufCursor - (uintptr_t)*nodeTail;
    Status ret = DmDecodeVarint2WithCheck(bufPtr, &bufLen, &varintByteNum, realLen);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Deseri sparse vertex mistake");
        return ret;
    }
    bufPtr += varintByteNum;
    *nodeTail = vertexBuf + bufLen + DmGetSysPropeSeriBufLen(vertex->vertexDesc);
    *bufCursor = bufPtr;
    return GMERR_OK;
}

Status NpaCountCheckIsLegal(const DmVertexDescT *vertexDesc, uint8_t **bufCursor, uint8_t *bufTail)
{
    if (vertexDesc->npaCount == 0) {
        return GMERR_OK;
    }
    if ((*bufCursor + DM_NULL_INFO_SERI_BYTES(vertexDesc->npaCount)) > bufTail) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Deserialize vertex mistake, npaCount is incorrect.");
        return GMERR_DATA_EXCEPTION;
    }
    *bufCursor += DM_NULL_INFO_SERI_BYTES(vertexDesc->npaCount);
    return GMERR_OK;
}

DM_SPARSE_FUN Status SparseVertexBufCheckIsLegal(uint8_t *vertexBuf, uint32_t length, DmVertexT *vertex, bool isFromMsg)
{
    if (length == 0 || vertexBuf == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Sparse vertexBuf len is 0 or NULL.");
        return GMERR_DATA_EXCEPTION;
    }
    Status ret;
    uint8_t *bufCursor = vertexBuf;
    uint8_t *bufTail = vertexBuf + length;
    // 校验系统字段
    ret = SysPropeCheckIsLegal(vertex, &bufCursor, bufTail);
    if (ret != GMERR_OK) {
        return ret;
    }

    // vertexHead
    ret = VertexHeadCheckIsLegal(&bufCursor, vertex);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint8_t *nodeTail = NULL;

    // 校验vertex len
    ret = SparseVertexLenCheckIsLegal(vertex, vertexBuf, &bufCursor, &nodeTail, bufTail);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 校验record
    DmSerParaT para = {.record = vertex->record,
        .nodes = vertex->nodes,
        .nodeDescs = vertex->vertexDesc->nodeDescs,
        .nodeNum = vertex->vertexDesc->nodeNum,
        .isDeltaVertex = vertex->isDeltaVertex};
    ret = SparseRecordBufCheckIsLegal(&bufCursor, bufTail, para);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 校验node
    for (uint32_t i = 0; i < vertex->vertexDesc->nodeNum; i++) {
        ret = SparseNodeBufCheckIsLegal(&bufCursor, bufTail, vertex->nodes[i], vertex->isDeltaVertex);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    // 校验nodeTail
    if (bufCursor != nodeTail) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Deseri sparse vertex check len mistake.");
        return GMERR_DATA_EXCEPTION;
    }
    // 校验npa bitmap
    ret = NpaCountCheckIsLegal(vertex->vertexDesc, &bufCursor, bufTail);
    if (ret != GMERR_OK) {
        return ret;
    }

    return SparseFirstEdgeAddrBufCheckIsLegal(vertex, &bufCursor, bufTail, isFromMsg);
}

DM_SPARSE_FUN Status SparseVertexDeSeriComplexModel(uint8_t **buf, const DmVertexT *vertex)
{
    // 反序列化nodes
    // 不支持升降级，按照desc中的数目进行解析
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < vertex->vertexDesc->nodeNum; i++) {
        ret = SparseNodeDeSerialize(buf, vertex->nodes[i], vertex->isDeltaVertex);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    return GMERR_OK;
}

DM_SPARSE_FUN Status SparseDeSerializeEdgeAddrs(DmVertexT *vertex, uint8_t **buf)
{
    uint32_t varintByteNum;
    uint32_t edgeLabelNum = 0;
    Status ret = DmDecodeVarint2(*buf, &edgeLabelNum, &varintByteNum);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret,
            "Check edgeLabelNum deseri sparse buf, edgeLabelNum = %" PRIu32 ", varintByteNum = %" PRIu32 ".",
            edgeLabelNum, varintByteNum);
        return ret;
    }
    *buf += varintByteNum;
    if (SECUREC_LIKELY(edgeLabelNum == 0)) {
        return GMERR_OK;
    }

    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    if (vertexDesc->edgeLabelNum != edgeLabelNum || vertex->edgeLabelNum != edgeLabelNum) {
        // 可能存在删边后还有读数据的操作，导致这个日志出现.
        // 可能会导致后续基于边的操作失败，但此时已经没有边数据了，这里返回是为了防止后续会core掉。并发场景，可能后面还会core掉
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
            "Edgenum not equal, Label name=%s, vertexDesc edge=%" PRIu32 ", buf edge=%" PRIu32 ", vertex edge=%" PRIu32
            ".",
            vertexDesc->labelName, vertexDesc->edgeLabelNum, edgeLabelNum, vertex->edgeLabelNum);
        return GMERR_DATA_EXCEPTION;
    }
    size_t edgeAddrsLen = vertex->edgeLabelNum * sizeof(DmEdgeAddrsT);

    // 必须先清零，因为如果 vertex 来源于缓存的话可能含有之前的边数据
    (void)memset_s(vertex->edgeAddrsEntry, edgeAddrsLen, 0x00, edgeAddrsLen);
    uint32_t edgeInfoByteNum = DM_NULL_INFO_SERI_BYTES(vertex->edgeLabelNum);
    uint8_t *edgeAddrBuf = *buf;
    edgeAddrBuf += edgeInfoByteNum;
    for (uint32_t i = 0; i < edgeLabelNum; i++) {
        if (DmGetBitValueFromUint8Arr(*buf, i) == 1) {
            // 解析边
            UncompressEdgeAddrFromBuf(&(vertex->edgeAddrsEntry[i].first), &edgeAddrBuf);
            UncompressEdgeAddrFromBuf(&(vertex->edgeAddrsEntry[i].last), &edgeAddrBuf);
        }
    }
    *buf = edgeAddrBuf;
    return GMERR_OK;
}

void DeSeriNpaBitmap(const DmVertexT *vertex, uint8_t **bufCursor)
{
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    if (vertexDesc->npaCount == 0) {
        return;
    }

    uint32_t npaBitMapLen = DM_NULL_INFO_SERI_BYTES(vertexDesc->npaCount);
    DbFastMemcpy(vertex->npaBitmap, npaBitMapLen, *bufCursor, npaBitMapLen);
    *bufCursor += npaBitMapLen;
}

DM_SPARSE_FUN Status SparseDeSerialize2ExistsVertex(uint8_t *buf, uint32_t length, DmVertexT *vertex, bool checkFlag)
{
    Status ret;
    DmClearVertex(vertex);
    if (SECUREC_UNLIKELY(checkFlag)) {
        ret = SparseVertexBufCheckIsLegal(buf, length, vertex, false);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }

    uint8_t *bufCursor = buf;
    // 系统字段区
    DeseriSysPrope(vertex, &bufCursor);

    // vertexHead
    uint8_t firstByteValue = *(uint8_t *)bufCursor;
    DecodeVertexHead(vertex, firstByteValue);
    bufCursor += (uint32_t)sizeof(uint8_t);

    // vertexLen
    uint32_t varintByteNum;
    uint32_t bufLen = 0;
    ret = DmDecodeVarint2(bufCursor, &bufLen, &varintByteNum);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    bufCursor += varintByteNum;

    // 反序列化vertex中的record
    DmSerParaT para = {.record = vertex->record,
        .nodes = vertex->nodes,
        .nodeDescs = vertex->vertexDesc->nodeDescs,
        .nodeNum = vertex->vertexDesc->nodeNum,
        .isDeltaVertex = vertex->isDeltaVertex};
    ret = SparseRecordDeSerialize(&bufCursor, para);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 反序列化node
    ret = SparseVertexDeSeriComplexModel(&bufCursor, vertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 反序列化yang里的npa表
    DeSeriNpaBitmap(vertex, &bufCursor);

    // 反序列化边
    return SparseDeSerializeEdgeAddrs(vertex, &bufCursor);
}

// -----------------vertexBuf相关操作--------------------------
DM_SPARSE_FUN uint32_t GetCurPropeBufForKey(
    const DmIndexKeyBufInfoT *indexKeyInfo, uint8_t **vertexBufCursor, uint8_t *nullInfoBytes, uint32_t keyPropeId)
{
    uint32_t propeId = 0;
    uint32_t propeLen = 0;
    uint32_t realPropeLen, varintByteNum;
    while (propeId < indexKeyInfo->keyPropeIds[keyPropeId]) {
        // 当前字段未设置或者当前字段为node，略过
        if (DmGetBitValueFromUint8Arr(nullInfoBytes, propeId) == DM_PROPERTY_IS_NULL ||
            indexKeyInfo->isValide[propeId] == 0) {
            propeId++;
            continue;
        }
        propeLen = indexKeyInfo->propeSize[propeId];
        // 略过前序属性
        if (propeLen == 0) {
            DmDecodeVarint2(*vertexBufCursor, &realPropeLen, &varintByteNum);
            *vertexBufCursor += (varintByteNum + realPropeLen);
        } else {
            *vertexBufCursor += propeLen;
        }
        propeId++;
    }
    DB_ASSERT(indexKeyInfo->keyPropeIds[keyPropeId] == propeId);
    return propeId;
}

DM_SPARSE_FUN void SpraseGetCurPropeBufFromRecordBuf(
    const RecordDescT *recordDesc, uint8_t **vertexBufCursor, uint8_t *nullInfoBytes, uint32_t propeId)
{
    uint32_t propeLen = 0;
    uint32_t realPropeLen, varintByteNum;
    for (uint32_t i = 0; i < propeId; i++) {
        // 当前字段未设置或者当前字段为node，略过
        if (DmGetBitValueFromUint8Arr(nullInfoBytes, i) == DM_PROPERTY_IS_NULL ||
            recordDesc->propeInfos[i].isValid == false) {
            continue;
        }
        // 略过前序属性
        DbDataTypeE type = recordDesc->propeInfos[i].dataType;
        if (!DmIsFixedType(type)) {
            DmDecodeVarint2(*vertexBufCursor, &realPropeLen, &varintByteNum);
            *vertexBufCursor += (varintByteNum + realPropeLen);
            continue;
        }
        // 定长字段
        if (DmIsBitFieldType(type)) {
            SparseGetBitFieldSeriLenWithBuf(recordDesc, propeId, &propeLen, nullInfoBytes);
            if (propeLen == 0) {
                // 当前bitFiled已经处理过。
                continue;
            }
        } else {
            propeLen = recordDesc->propeInfos[propeId].propeMaxLen;
        }
        *vertexBufCursor += propeLen;
    }
}

DM_SPARSE_FUN void SparseGetKeyBufProcessSingleVarPrope(
    uint8_t *recordBufCursor, uint8_t **varkeyBufCursor, uint32_t *keyBufLen, bool isNull)
{
    uint32_t varintByteNum, realPropeLen;
    // 解析realPropeLen
    if (isNull) {
        realPropeLen = 0;
    } else {
        Status ret = DmDecodeVarint2(recordBufCursor, &realPropeLen, &varintByteNum);
        DB_ASSERT(ret == GMERR_OK);
        recordBufCursor += varintByteNum;
    }
    *((uint32_t *)*varkeyBufCursor) = realPropeLen;
    *varkeyBufCursor += (uint32_t)sizeof(uint32_t);
    if (realPropeLen != 0) {
        DbFastMemcpy(*varkeyBufCursor, realPropeLen, recordBufCursor, realPropeLen);
        *varkeyBufCursor += realPropeLen;
    }
    *keyBufLen += ((uint32_t)sizeof(uint32_t) + realPropeLen);
}

DM_SPARSE_FUN void SparseGetKeyBufFromRecordBuf(
    const DmIndexKeyBufInfoT *indexKeyInfo, uint8_t *recordBuf, uint8_t *keyBuf, uint32_t *length)
{
    uint8_t nullInfoBytes = indexKeyInfo->nullInfoBytes;
    uint8_t *vertexBufCursor = recordBuf;

    // 若索引支持空，将keyBuf前部标识字节置0
    uint8_t *keyBufCursor = keyBuf;
    uint8_t nullInfo = indexKeyInfo->isNullable ? (uint8_t)0x00 : (uint8_t)0xFF;
    for (uint32_t i = 0; i < nullInfoBytes; i++) {
        *(uint8_t *)keyBufCursor = nullInfo;
        keyBufCursor += (uint32_t)sizeof(uint8_t);
    }

    // vertexBufCursor略过nullInfo区
    uint32_t nullInfoByteNum = DM_NULL_INFO_SERI_BYTES(indexKeyInfo->totalPropeNum);
    vertexBufCursor += nullInfoByteNum;
    uint8_t *propeBuf = vertexBufCursor;

    uint32_t keyBufLen = 0;

    // 逐个处理属性
    for (uint32_t i = 0; i < indexKeyInfo->keyPropeNum; i++) {
        // 处理每个属性时都从头开始遍历，后续可以优化。注意，indexKeyInfo->keyPropeIds是定长在前，变长在后
        vertexBufCursor = propeBuf;
        uint32_t propeId = GetCurPropeBufForKey(indexKeyInfo, &vertexBufCursor, recordBuf, i);
        bool isNull = indexKeyInfo->isNullable && DmGetBitValueFromUint8Arr(recordBuf, propeId) == DM_PROPERTY_IS_NULL;
        uint32_t propeLen = indexKeyInfo->propeSize[propeId];
        if (propeLen == 0) {
            // segmentLengths为0，表示当前为变长属性
            SparseGetKeyBufProcessSingleVarPrope(vertexBufCursor, &keyBufCursor, &keyBufLen, isNull);
        } else {
            // 定长属性，按照segmentLengths拷贝
            if (!isNull) {
                errno_t err = memcpy_s(keyBufCursor, propeLen, vertexBufCursor, propeLen);
                DB_ASSERT(err == EOK);
            }
            keyBufLen += propeLen;
            keyBufCursor += propeLen;
        }
    }

    *length = (keyBufLen + nullInfoBytes);
    if (!indexKeyInfo->isNullable) {
        return;
    }
    // 索引允许为空，须进行后续处理
    RecordKeyBufProcessNull(recordBuf, indexKeyInfo, keyBuf);
}

// 从头跳转到第一个recordBuf的位置
// 系统字段 | vertexHead | vertexLen | record | nodes | npaBitmap | edgeAddrs
DM_SPARSE_FUN void SparseVertexBufJumpToRecordBuf(uint8_t **vertexBuf)
{
    uint32_t varintByteNum;
    // 系统字段区
    *vertexBuf += GetSysBufLen(*vertexBuf);
    // vertexHead
    *vertexBuf += (uint32_t)sizeof(uint8_t);
    // vertex总长度
    uint32_t totalLen = 0;
    DmDecodeVarint2(*vertexBuf, &totalLen, &varintByteNum);
    *vertexBuf += varintByteNum;
}

DM_SPARSE_FUN void SparseGetKeyBufFromVertexBuf(
    const DmIndexKeyBufInfoT *indexKeyInfo, uint8_t *vertexBuf, uint8_t *keyBuf, uint32_t *length)
{
    uint8_t *vertexBufCursor = vertexBuf;
    SparseVertexBufJumpToRecordBuf(&vertexBufCursor);
    SparseGetKeyBufFromRecordBuf(indexKeyInfo, vertexBufCursor, keyBuf, length);
}

// 只允许修改已经写入的值，如果本身未设置，报错
DM_SPARSE_FUN Status SparseVertexBufSetFirstLevelFixedPropById(
    uint8_t *vertexBuf, const DmVertexDescT *vertexDesc, uint32_t propeId, DmValueT *value)
{
    RecordDescT *recordDesc = vertexDesc->recordDesc;
    if (propeId >= recordDesc->propeNum) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_INVALID_PROPERTY, "Incorrect propId when set value to sparse buf, propeId(%" PRIu32 ").", propeId);
        return GMERR_INVALID_PROPERTY;
    }
    DmPropertyInfoT *propeInfo = &recordDesc->propeInfos[propeId];
    // type不匹配、需要申请内存的属性或设置为NULL 都不支持。
    if (value->type != propeInfo->dataType || !DmIsFixedType(value->type) || value->type == DB_DATATYPE_NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATATYPE_MISMATCH,
            "Incorrect propId when set value to sparse buf, propeId:(%" PRIu32 "), value type:(%" PRIu32
            "), prop type(%" PRIu32 ").",
            propeId, (uint32_t)value->type, (uint32_t)propeInfo->dataType);
        return GMERR_DATATYPE_MISMATCH;
    }
    uint8_t *bufCursor = (uint8_t *)vertexBuf;
    SparseVertexBufJumpToRecordBuf(&bufCursor);
    uint8_t *nullInfoBuf = bufCursor;
    if (DmGetBitValueFromUint8Arr(nullInfoBuf, propeId) == DM_PROPERTY_IS_NULL) {
        // 字段原先未设置，报错
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATATYPE_MISMATCH,
            "Prope is null when set value sparsebuf, propeId:(%" PRIu32 "), prop type(%" PRIu32 ").", propeId,
            (uint32_t)propeInfo->dataType);
        return GMERR_DATATYPE_MISMATCH;
    }
    // 略过nullifno区域的长度
    bufCursor += DM_NULL_INFO_SERI_BYTES(recordDesc->propeNum);
    // 跳转到当前属性对应的buf
    SpraseGetCurPropeBufFromRecordBuf(recordDesc, &bufCursor, nullInfoBuf, propeId);

    Status ret;
    if (DmIsBitFieldType(value->type)) {
        ret = GetMergedBitFieldPropeValue(bufCursor, propeInfo, value);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return SetPropeValueIntoBuf(NULL, propeInfo->propeMaxLen, value, bufCursor);
}

DM_SPARSE_FUN Status SparseVertexBufGetFirstLevelFixedPropById(
    uint8_t *vertexBuf, const DmVertexDescT *vertexDesc, uint32_t propeId, DmValueT *value)
{
    RecordDescT *recordDesc = vertexDesc->recordDesc;
    if (propeId >= recordDesc->propeNum) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PROPERTY,
            "Incorrect propId when set value to sparse buf, propId(%" PRIu32 "), propNum in Desc is %" PRIu32 ".",
            propeId, recordDesc->propeNum);
        return GMERR_INVALID_PROPERTY;
    }
    DmPropertyInfoT *propeInfo = &recordDesc->propeInfos[propeId];
    if (!DmIsFixedType(propeInfo->dataType)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATATYPE_MISMATCH,
            "Get value from sparse vertexBuf, dataType(%" PRIu32 "), propeId:(%" PRIu32 ").",
            (uint32_t)propeInfo->dataType, propeId);
        return GMERR_DATATYPE_MISMATCH;
    }
    uint8_t *bufCursor = (uint8_t *)vertexBuf;
    SparseVertexBufJumpToRecordBuf(&bufCursor);
    uint8_t *nullInfoBuf = bufCursor;

    // 解析属性的nullInfo信息
    uint8_t nullInfo = (uint8_t)DmGetBitValueFromUint8Arr(nullInfoBuf, propeId);
    if (nullInfo == 0) {
        value->type = DB_DATATYPE_NULL;
        if (propeInfo->dataType == DB_DATATYPE_BITMAP) {
            // 如果不设置bitmap类型字段的值，则默认值为0，无论nullable属性值为true还是false
            (void)memset_s((void *)value->value.strAddr, value->value.length, 0x00, value->value.length);
        }
        return GMERR_OK;
    }
    // 略过nullInfo区域的长度
    bufCursor += DM_NULL_INFO_SERI_BYTES(recordDesc->propeNum);
    // 跳转到当前属性对应的buf
    SpraseGetCurPropeBufFromRecordBuf(recordDesc, &bufCursor, nullInfoBuf, propeId);

    value->type = propeInfo->dataType;
    return GetPropeValueFromRecordBuf(bufCursor, propeInfo, value, false);
}

DM_SPARSE_FUN Status SparseVertexBufJumpToEdgeTopoAddr(uint8_t **bufCursor, const DmVertexLabelT *vertexLabel)
{
    // 略过vertex数据区长度
    uint32_t dataLen = 0;
    Status ret = SparseGetVertexBufDataLen(*bufCursor, vertexLabel, &dataLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    *bufCursor += dataLen;
    // 略过边个数
    uint32_t varintByteNum;
    uint32_t edgeLabelNum = 0;
    ret = DmDecodeVarint2(*bufCursor, &edgeLabelNum, &varintByteNum);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret,
            "when jump to EdgeTopoAddr unable check checkedgeLabelNum = %" PRIu32 ", varintByteNum = %" PRIu32 ".",
            edgeLabelNum, varintByteNum);
        return ret;
    }
    *bufCursor += varintByteNum;
    DB_ASSERT(edgeLabelNum == vertexLabel->commonInfo->edgeLabelNum);
    return GMERR_OK;
}

DM_SPARSE_FUN Status SparseGetEdgeTopoAddrBufsById(
    DmVertexLabelT *vertexLabel, uint8_t *edgeTopoAddrBuf, const uint32_t edgeLabelId, DmEdgeAddrsT **addr)
{
    uint32_t edgeLabelNum = vertexLabel->commonInfo->edgeLabelNum;
    DmEdgeLabelInfoT *edgeLabelInfos = vertexLabel->commonInfo->relatedEdgeLabels;
    DB_ASSERT(vertexLabel->metaVertexLabel->seriType == DM_SPARSE_SERI_VERTEX);

    uint8_t *edgeAddrsCursor = (uint8_t *)(edgeTopoAddrBuf + (uint32_t)DM_NULL_INFO_SERI_BYTES(edgeLabelNum));
    for (uint32_t i = 0; i < edgeLabelNum; i++) {
        if (edgeLabelInfos[i].edgeLabelId != edgeLabelId) {
            edgeAddrsCursor += DmGetBitValueFromUint8Arr(edgeTopoAddrBuf, i) ? SERI_DM_EDGE_ADDRS_LEN : 0;
            continue;
        }
        if (!DmGetBitValueFromUint8Arr(edgeTopoAddrBuf, i)) {
            // 说明还没有插入过这种类型的边，不用取edgeAddr
            *addr = NULL;
            return GMERR_OK;
        }
        UncompressEdgeAddrFromBuf(&(*addr)->first, &edgeAddrsCursor);
        UncompressEdgeAddrFromBuf(&(*addr)->last, &edgeAddrsCursor);
        return GMERR_OK;
    }
    return GMERR_UNDEFINED_TABLE;
}

// newBuf需要初始化，否则edge nullinfo区域可能会有问题
DM_SPARSE_FUN Status SparseRefreshEdgeWithVertexBuf(
    const DmVertexT *vertex, uint8_t *oldBuf, uint32_t oldBufLen, uint8_t *newBuf, uint32_t newLength)
{
    // 拼接buf oldBuf的数据部分 + vertex的边部分
    // datalen为oldBuf中的数据部分长度，不包含edgeaddr信息
    uint32_t edgeAddrInfoLen = SparseVertexGetEdgeAddrInfoLen(vertex->edgeLabelNum);
    uint32_t edgeAddrLen = DmVertexGetEdgeAddrLen(vertex);
    uint32_t dataLen = oldBufLen - edgeAddrInfoLen;
    uint32_t replaceBufSize = dataLen + edgeAddrLen;
    if (SECUREC_UNLIKELY(replaceBufSize != newLength)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
            "sparse vertex seri len(%" PRIu32 ") not equal expected len(%" PRIu32 ").", replaceBufSize, newLength);
        return GMERR_DATA_EXCEPTION;
    }

    errno_t err = memcpy_s(newBuf, dataLen, oldBuf, dataLen);
    DB_ASSERT(err == EOK);
    // 单独序列化边
    uint8_t *tmpBuf = newBuf + dataLen;
    return SparseSerializeEdgeAddrs(vertex, &tmpBuf);
}

DM_SPARSE_FUN uint32_t SparseVertexRefreshSeriBufLengthWithEdge(const DmVertexT *vertex, uint32_t oldBufLen)
{
    // 拼接buf 客户端传入buf的数据部分 + rootVertex序列化的边部分
    // rootVertex是replace之前的数据，data->deltaVertexBuf为新数据，所以此处不能根据rootVertex计算buf长度
    uint32_t edgeAddrLen = DmVertexGetEdgeAddrLen(vertex);
    uint32_t edgeAddrInfoLen = SparseVertexGetEdgeAddrInfoLen(vertex->edgeLabelNum);
    // 客户端不感知边信息，因此客户端的数据部分为data->deltaVertexBuf.len - edgeAddrInfoLen
    // 如果传入buf包含实际建边信息，计算会有问题
    uint32_t dataLen = oldBufLen - edgeAddrInfoLen;
    return dataLen + edgeAddrLen;
}

DM_SPARSE_FUN Status SparseVertexBufHasEdgeByIndex(
    uint8_t *vertexBuf, DmVertexLabelT *vertexLabel, const uint32_t edgeIndex, bool *hasEdge)
{
    DB_POINTER3(vertexBuf, vertexLabel, hasEdge);
    DB_ASSERT(vertexLabel->metaVertexLabel->seriType == DM_SPARSE_SERI_VERTEX);
    uint8_t *bufCursor = vertexBuf;
    Status ret = SparseVertexBufJumpToEdgeTopoAddr(&bufCursor, vertexLabel);  // 这里会略过边的个数
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "edge label(index:%" PRIu32 ") in sparse vertex label %s", edgeIndex,
            vertexLabel->metaCommon.metaName);
        return ret;
    }
    *hasEdge = DmGetBitValueFromUint8Arr(bufCursor, edgeIndex);
    return GMERR_OK;
}

DM_SPARSE_FUN Status SparseVertexBufGetEdgeTopoAddrsById(
    uint8_t *vertexBuf, DmVertexLabelT *vertexLabel, const uint32_t edgeLabelId, DmEdgeAddrsT *addr)
{
    DB_POINTER3(vertexBuf, vertexLabel, addr);
    uint8_t *bufCursor = vertexBuf;
    Status ret = SparseVertexBufJumpToEdgeTopoAddr(&bufCursor, vertexLabel);  // 这里会略过边的个数
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "edge label(ID:%" PRIu32 ") in sparse vertex label %s", edgeLabelId, vertexLabel->metaCommon.metaName);
        return ret;
    }
    DmEdgeLabelInfoT *edgeLabelInfos = vertexLabel->commonInfo->relatedEdgeLabels;
    uint32_t edgeLabelNum = vertexLabel->commonInfo->edgeLabelNum;

    DB_ASSERT(vertexLabel->metaVertexLabel->seriType == DM_SPARSE_SERI_VERTEX);
    uint8_t *edgeAddrsCursor = (uint8_t *)(bufCursor + (uint32_t)DM_NULL_INFO_SERI_BYTES(edgeLabelNum));
    for (uint32_t i = 0; i < edgeLabelNum; i++) {
        if (edgeLabelInfos[i].edgeLabelId != edgeLabelId) {
            edgeAddrsCursor += DmGetBitValueFromUint8Arr(bufCursor, i) ? SERI_DM_EDGE_ADDRS_LEN : 0;
            continue;
        }
        if (!DmGetBitValueFromUint8Arr(bufCursor, i)) {
            // 说明还没有插入过这种类型的边，不用取edgeAddr
            return GMERR_OK;
        }
        UncompressEdgeAddrFromBuf(&(addr->first), &edgeAddrsCursor);
        UncompressEdgeAddrFromBuf(&(addr->last), &edgeAddrsCursor);
        return GMERR_OK;
    }

    DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNDEFINED_TABLE, "Vertex label %s not have edge label with ID %" PRIu32 "",
        vertexLabel->metaCommon.metaName, edgeLabelId);
    return GMERR_UNDEFINED_TABLE;
}

DM_SPARSE_FUN Status SparseVertexUpdateOldEdgeAddrInVarBuf(
    uint8_t *vertexBuf, const DmVertexLabelT *vertexLabel, const uint32_t edgeLabelId, const DmEdgeAddrsT *newEdgeAddr)
{
    DB_POINTER3(vertexBuf, vertexLabel, newEdgeAddr);

    uint8_t *nullInfoCursor = vertexBuf;
    Status ret = SparseVertexBufJumpToEdgeTopoAddr(&nullInfoCursor, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret,
            "Sparse vertex update EdgeAddr, label(ID:%" PRIu32 ") in sparse vertex label %s", edgeLabelId,
            vertexLabel->metaCommon.metaName);
        return ret;
    }

    uint32_t edgeLabelNum = vertexLabel->commonInfo->edgeLabelNum;
    uint8_t *edgeAddrCursor = nullInfoCursor + (uint32_t)DM_NULL_INFO_SERI_BYTES(edgeLabelNum);

    DmEdgeLabelInfoT *edgeLabelInfos = vertexLabel->commonInfo->relatedEdgeLabels;
    for (uint32_t i = 0; i < edgeLabelNum; ++i) {
        if (edgeLabelInfos[i].edgeLabelId == edgeLabelId) {
            // 该接口只能处理edge存在，原地更新场景
            DB_ASSERT(DmGetBitValueFromUint8Arr(nullInfoCursor, i) == true);
            SerializeSingleEdgeAddrs(*newEdgeAddr, &edgeAddrCursor);
            return GMERR_OK;
        }
        if (DmGetBitValueFromUint8Arr(nullInfoCursor, i)) {
            edgeAddrCursor += SERI_DM_EDGE_ADDRS_LEN;
        }
    }
    DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNDEFINED_TABLE,
        "Sparse vertex update EdgeAddr, label %s not have edgeLabel with ID %" PRIu32 "",
        vertexLabel->metaCommon.metaName, edgeLabelId);
    return GMERR_UNDEFINED_TABLE;
}

DM_SPARSE_FUN Status SparseVertexUpdateNewEdgeAddrInVarBuf(uint8_t *oldVertexBuf, const DmVertexLabelT *vertexLabel,
    const uint32_t edgeLabelId, const DmEdgeAddrsT *newEdgeAddr, uint8_t *newVertexBuf)
{
    DB_POINTER4(oldVertexBuf, vertexLabel, newEdgeAddr, newVertexBuf);

    uint32_t vertexDataLen = 0;
    Status ret = SparseGetVertexBufDataLen(oldVertexBuf, vertexLabel, &vertexDataLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t edgeLabelNum = vertexLabel->commonInfo->edgeLabelNum;

    uint32_t edgeNullInfoLen = DM_NULL_INFO_SERI_BYTES(edgeLabelNum);
    uint32_t copyBufLen = vertexDataLen + SparseVertexGetEdgeAddrInfoLen(edgeLabelNum);
    errno_t err = memcpy_s(newVertexBuf, copyBufLen, oldVertexBuf, copyBufLen);
    DB_ASSERT(err == EOK);

    uint8_t *oldEdgeAddrCursor = (uint8_t *)(oldVertexBuf + copyBufLen);
    uint8_t *newEdgeAddrCursor = (uint8_t *)(newVertexBuf + copyBufLen);
    uint8_t *oldNullInfoCursor = (uint8_t *)((uintptr_t)oldEdgeAddrCursor - edgeNullInfoLen);
    uint8_t *newNullInfoCursor = (uint8_t *)((uintptr_t)newEdgeAddrCursor - edgeNullInfoLen);

    DmEdgeLabelInfoT *edgeLabelInfos = vertexLabel->commonInfo->relatedEdgeLabels;
    bool isFound = false;
    for (uint32_t i = 0; i < edgeLabelNum; ++i) {
        if (edgeLabelInfos[i].edgeLabelId == edgeLabelId) {
            // 填入新边edgeAddr
            DmSetBitTrueIntoUint8Arr(newNullInfoCursor, i);
            SerializeSingleEdgeAddrs(*newEdgeAddr, &newEdgeAddrCursor);
            isFound = true;
        } else {
            if (DmGetBitValueFromUint8Arr(oldNullInfoCursor, i)) {
                // 拷贝旧边edgeAddr
                err = memcpy_s(newEdgeAddrCursor, SERI_DM_EDGE_ADDRS_LEN, oldEdgeAddrCursor, SERI_DM_EDGE_ADDRS_LEN);
                DB_ASSERT(err == EOK);
                newEdgeAddrCursor += SERI_DM_EDGE_ADDRS_LEN;
                oldEdgeAddrCursor += SERI_DM_EDGE_ADDRS_LEN;
            }
        }
    }
    if (!isFound) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNDEFINED_TABLE, "Vertex label %s not have edge label with ID %" PRIu32 "",
            vertexLabel->metaCommon.metaName, edgeLabelId);
        return GMERR_UNDEFINED_TABLE;
    }
    return GMERR_OK;
}

DM_SPARSE_FUN Status SparseGetVertexBufDataLen(uint8_t *vertexBuf, const DmVertexLabelT *vertexLabel, uint32_t *dataLen)
{
    uint8_t *offset = vertexBuf;
    // 系统字段区
    uint32_t sysProLen = GetSysBufLen(vertexBuf);
    offset += sysProLen;
    // vertexHead
    offset += (uint32_t)sizeof(uint8_t);

    // 解析vertexLen
    uint32_t totalLen = 0;
    uint32_t varintByteNum;
    Status ret = DmDecodeVarint2(offset, &totalLen, &varintByteNum);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Sparse get VertexBufLen");
        return ret;
    }

    uint32_t npaBitmapLen = 0;
    DmSchemaT *schema = MEMBER_PTR(vertexLabel->metaVertexLabel, schema);
    DmYangInfoT *yangInfo = MEMBER_PTR(schema, yangInfo);
    npaBitmapLen = DM_NULL_INFO_SERI_BYTES(yangInfo->npaCount);

    *dataLen = totalLen + sysProLen + npaBitmapLen;
    return GMERR_OK;
}

DM_SPARSE_FUN bool SparseIsAllSetKeyBufProp(
    const DmIndexKeyBufInfoT *indexKeyBufInfos, const DmVertexDescT *vertexDesc, uint8_t *vertexBuf)
{
    DB_POINTER3(indexKeyBufInfos, vertexDesc, vertexBuf);
    uint8_t *nullInfoBuf = vertexBuf;
    SparseVertexBufJumpToRecordBuf(&nullInfoBuf);

    uint16_t keyPropeNum = indexKeyBufInfos->keyPropeNum;
    const uint16_t *keyPropeIds = indexKeyBufInfos->keyPropeIds;
    for (uint32_t i = 0; i < keyPropeNum; i++) {
        if (KeyPropHasDefaultValue(vertexDesc, keyPropeIds[i])) {
            continue;
        }
        if (DmGetBitValueFromUint8Arr(nullInfoBuf, keyPropeIds[i]) == DM_PROPERTY_IS_NULL) {
            return false;
        }
    }
    return true;
}
