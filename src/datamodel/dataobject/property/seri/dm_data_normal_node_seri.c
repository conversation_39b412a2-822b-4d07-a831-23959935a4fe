/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: source file for data model node serialize
 * Author:
 * Create: 2021-10-12
 */

#include "dm_data_tree.h"
#include "dm_data_keybuf_opti.h"
#include "dm_data_node_inner.h"
#include "dm_data_basic.h"
#include "db_memcpy.h"
#include "dm_data_prop_in.h"
#include "dm_log.h"

Status NodeElementBufCheckIsLegal(
    DmNodeT *node, uint32_t elementIdx, uint8_t **bufCursor, uint8_t *bufTail, bool isDelta);

Status NodeDeSeriElement(DmNodeT *node, uint32_t elementIdx, uint8_t **bufCursor, bool isDelta);

Status NodeParseElement(DmNodeT *node, uint32_t elementIdx, uint8_t **bufCursor);

Status FixNodeDeSeriElement(DmNodeT *node, uint32_t elementIdx, uint8_t **bufCursor);

Status NodeBufCheckIsLegal(uint8_t **bufCursor, uint8_t *bufTail, struct DmNode *node, bool isDelta)
{
    Status ret = GMERR_OK;
    uint32_t byteNum;
    // 校验isCreated字节，主要用于兼容v3
    if ((*bufCursor + (uint32_t)sizeof(uint8_t)) > bufTail) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "NodeBuf check, isCreated");
        return GMERR_DATA_EXCEPTION;
    }
    *bufCursor += (uint32_t)sizeof(uint8_t);

    uint32_t realElementNum;
    // 校验realElementNum length
    if ((*bufCursor + (uint32_t)sizeof(uint32_t)) > bufTail) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "NodeBuf check, realEleNumLen");
        return GMERR_DATA_EXCEPTION;
    }
    DmConvertVarintToUint32(*(uint32_t *)*bufCursor, &realElementNum, &byteNum);
    *bufCursor += byteNum;

    if (!isDelta && realElementNum > node->nodeDesc->maxElementNum) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATA_EXCEPTION, "NodeBuf check realElementNum: %" PRIu32 ".", realElementNum);
        return GMERR_DATA_EXCEPTION;
    }

    if (isDelta) {
        ret = NodeBufDeltaInfoCheckIsLegal(bufCursor, bufTail, node);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    DmNodeDescT *nodeDesc = node->nodeDesc;
    if ((nodeDesc->nodeType == DM_NODE_ARRAY && realElementNum != node->realElementNum) ||
        (nodeDesc->nodeType == DM_NODE_RECORD && realElementNum != 1)) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATA_EXCEPTION, "NodeBuf Check type and realEleNum, realElementNum: %" PRIu32 ".", realElementNum);
        return GMERR_DATA_EXCEPTION;
    }

    for (uint32_t i = 0; i < realElementNum; i++) {
        ret = NodeElementBufCheckIsLegal(node, i, bufCursor, bufTail, isDelta);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status NodeElementBufCheckIsLegal(
    DmNodeT *node, uint32_t elementIdx, uint8_t **bufCursor, uint8_t *bufTail, bool isDelta)
{
    // 校验record length
    Status ret = RecordBufCheckIsLegal(bufCursor, bufTail, node->currRecord, true, isDelta);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 校验nodeNumPerElement length
    if ((*bufCursor + (uint32_t)sizeof(uint32_t)) > bufTail) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "NodeEleBuf check");
        return GMERR_DATA_EXCEPTION;
    }
    uint32_t nodeNumPerElement;
    uint32_t byteNum;
    uint32_t varint = *(uint32_t *)*bufCursor;
    DmConvertVarintToUint32(varint, &nodeNumPerElement, &byteNum);
    *bufCursor += byteNum;

    uint32_t minNodeNum = DM_MIN2(nodeNumPerElement, node->nodeDesc->nodeNumPerElement);
    for (uint32_t i = 0; i < minNodeNum; i++) {
        ret = NodeBufCheckIsLegal(bufCursor, bufTail, node->currNodes[i], isDelta);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

// 序列化逻辑是深度优先遍历
Status NodeGetSeriBufLength(DmNodeT *node, uint32_t *nodeBufLen, bool isDelta)
{
    uint32_t byteNum;
    // 序列化时需要记录isCreated，主要用于兼容v3
    *nodeBufLen = (uint32_t)sizeof(uint8_t);
    // 序列化时需要记录realElementNum，主要用于支持vector
    Status ret = DmGetVarintLength(node->realElementNum, &byteNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    *nodeBufLen += byteNum;
    // 如果是deltaVertex中的node 应该计算序列化opNum,opArray,opNumByMemberKey,deltaMemberKeys所需要空间的大小
    if (isDelta) {
        *nodeBufLen += (uint32_t)sizeof(uint32_t);                // opNum 所需要的空间大小
        *nodeBufLen += (uint32_t)sizeof(uint16_t) * node->opNum;  // opArray 所需要的空间大小
        if (node->nodeDesc->nodeType == DM_NODE_VECTOR) {
            *nodeBufLen += (uint32_t)sizeof(uint32_t);  // opNumByMemberKey所需要空间的大小
            uint32_t memberKeyLen = 0;
            for (uint32_t i = 0; i < node->opNumByMemberKey; i++) {
                memberKeyLen = DmIndexKeyGetSeriBufLength(node->deltaMemberKeys[i]);
                *nodeBufLen += memberKeyLen;
                node->deltaMemberKeys[i]->seriBufLen = memberKeyLen;  // 记录该key序列化的长度，在序列化时使用
            }
        }
    }

    for (uint32_t i = 0; i < node->realElementNum; i++) {
        uint32_t length = 0;
        ret = ElementGetSeriBufLen(node, i, isDelta, &length);
        if (ret != GMERR_OK) {
            return ret;
        }
        (*nodeBufLen) += length;
    }

    return GMERR_OK;
}

Status ElementGetSeriBufLen(DmNodeT *node, uint32_t elementIdx, bool isDelta, uint32_t *length)
{
    Status ret = GMERR_OK;
    uint32_t recordBufLen = 0;
    uint32_t byteNum = 0;
    DB_ASSERT(elementIdx < node->realElementNum);
    NodeSetElementIndexWithoutCheck(node, elementIdx);
    ret = RecordGetSeriBufLength(node->currRecord, &recordBufLen, true, isDelta);
    if (ret != GMERR_OK) {
        return ret;
    }
    node->recordSeriLenArr[elementIdx] = recordBufLen;
    *length = recordBufLen;

    // 序列化时需要记录nodeNumPerElement，主要用于支持schema升级，这里后续可以优化
    DmNodeDescT *nodeDesc = node->nodeDesc;
    (void)DmGetVarintLength(nodeDesc->nodeNumPerElement, &byteNum);  // nodeNumPerElement最大1024，不会错
    *length += byteNum;

    // 获取当前元素中所有子树的序列化长度
    uint32_t childNodeBufLen = 0;
    for (uint32_t i = 0; i < nodeDesc->nodeNumPerElement; i++) {
        ret = NodeGetSeriBufLength(node->currNodes[i], &childNodeBufLen, isDelta);
        if (ret != GMERR_OK) {
            return ret;
        }
        *length += childNodeBufLen;
    }
    return ret;
}

// ignoreDeltaInfo为true的使用场景：当新版本增加了node节点，当用新版本的deltaVertex更新老版本的数据时，
// 新版本的deltaVertex带有的新增node的信息在老版本数据中并不存在，此时新增加的node里面只支持append操作，
// node里面的record的定长、变长设置进新的merge buf，delta info不再需要。
Status NodeSerialize(DmNodeT *node, uint8_t **bufCursor, bool isDelta, bool ignoreDeltaInfo)
{
    uint32_t varint;
    uint32_t byteNum;

    uint8_t *bufPtr = *bufCursor;
    // 序列化isCreated，主要用于兼容v3
    *(uint8_t *)bufPtr = (uint8_t)node->isCreated;
    bufPtr += (uint32_t)sizeof(uint8_t);

    // 序列化realElementNum
    Status ret = DmConvertUint32ToVarint(node->realElementNum, &varint, &byteNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    *(uint32_t *)bufPtr = varint;
    bufPtr += byteNum;

    *bufCursor = bufPtr;
    // 如果node的isDelta为true（在deltaVertex中）需要序列化opNum和opArray
    if (isDelta && !ignoreDeltaInfo) {
        ret = NodeSerializeDeltaInfo(node, bufCursor);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // 挨个序列化element
    for (uint32_t i = 0; i < node->realElementNum; i++) {
        ret = ElementSeriIntoBuf(node, i, bufCursor, isDelta, ignoreDeltaInfo);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return GMERR_OK;
}

Status ElementSeriIntoBuf(DmNodeT *node, uint32_t elementIdx, uint8_t **bufCursor, bool isDelta, bool ignoreDeltaInfo)
{
    uint32_t varint = 0;
    uint32_t byteNum = 0;
    NodeSetElementIndexWithoutCheck(node, elementIdx);
    uint32_t recordBufLen = node->recordSeriLenArr[elementIdx];
    Status ret;
    if (!isDelta && ignoreDeltaInfo) {
        // 在进行merge的时候，存在使用新版本的vertex升级老版本的数据，新版本的数据可能带有新的节点，此时节点应该序列化进新的序列化
        // buf。ignoreDeltaInfo只有在merge场景下才会为true。
        ret = RecordGetSeriBufLength(node->currRecord, &recordBufLen, true, false);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    ret = RecordSerialize(node->currRecord, recordBufLen, bufCursor, true, isDelta);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 序列化nodeNumPerElement
    DmNodeDescT *nodeDesc = node->nodeDesc;
    (void)DmConvertUint32ToVarint(nodeDesc->nodeNumPerElement, &varint, &byteNum);  // nodeNumPerElement最大1024，不会错
    *(uint32_t *)*bufCursor = varint;
    *bufCursor += byteNum;

    // 挨个序列化元素中的子树节点
    for (uint32_t i = 0; i < nodeDesc->nodeNumPerElement; i++) {
        ret = NodeSerialize(node->currNodes[i], bufCursor, isDelta, ignoreDeltaInfo);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status NodeDeSerialize(uint8_t **bufCursor, DmNodeT *node, bool isDelta)
{
    Status ret = GMERR_OK;
    uint32_t byteNum;
    // 反序列化isCreated，主要用于兼容v3
    node->isCreated = *(uint8_t *)*bufCursor;
    *bufCursor += (uint32_t)sizeof(uint8_t);

    // 反序列化realElementNum
    uint32_t realElementNum;
    uint32_t varint = *(uint32_t *)*bufCursor;
    DmConvertVarintToUint32(varint, &realElementNum, &byteNum);
    *bufCursor += byteNum;
    DmNodeDescT *nodeDesc = node->nodeDesc;
    if (nodeDesc->nodeType == DM_NODE_VECTOR) {
        node->isClear = true;
        ResetVectorNode(node);
    }
    if (isDelta) {
        ret = NodeDeSerializeDeltaInfo(bufCursor, node);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // 若node为vector类型，应先扩容至real大小
    if (nodeDesc->nodeType == DM_NODE_VECTOR) {
        while (realElementNum > node->totalElementNum) {
            if (isDelta) {
                ret = DeltaNodeVectorAppend(node);
            } else {
                ret = DmNodeVectorAppend(node);
            }
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        node->realElementNum = realElementNum;
    }
    // node为单层定长
    if (!isDelta && node->nodeDesc->nodeNumPerElement == 0 && node->currRecord->recordDesc->isFixed == true) {
        for (uint32_t i = 0; i < node->realElementNum; i++) {
            ret = FixNodeDeSeriElement(node, i, bufCursor);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        return GMERR_OK;
    }

    // 反序列化node中的所有record, schema升级时需要考虑:
    // 若realElementNum < node->realElementNum:
    // 句柄处于Reset状态，需将后面的record默认值去掉；句柄处于Clear状态，不用处理
    // 若realElementNum > node->realElementNum：
    // 句柄中的record数据不用额外处理，但需将bufCursor后移
    for (uint32_t i = 0; i < DM_MIN2(realElementNum, node->realElementNum); i++) {
        ret = NodeDeSeriElement(node, i, bufCursor, isDelta);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status NodeDeSeriElement(DmNodeT *node, uint32_t elementIdx, uint8_t **bufCursor, bool isDelta)
{
    // 内部函数保证elementIdx正确
    NodeSetElementIndexWithoutCheck(node, elementIdx);
    Status ret = RecordDeSerialize(bufCursor, node->currRecord, true, isDelta);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 反序列化nodeNumPerElement
    uint32_t nodeNumPerElement;
    uint32_t byteNum;
    uint32_t varint = *(uint32_t *)*bufCursor;
    DmConvertVarintToUint32(varint, &nodeNumPerElement, &byteNum);
    *bufCursor += byteNum;

    // 反序列化nodes
    // schema升级时需要考虑：
    // 若nodeNum < node->nodeNum：句柄处于Reset状态，需将后面的node默认值去掉（Clear）；句柄处于Clear状态，不用处理
    // 若nodeNum > node->nodeNum：句柄中的record数据不用额外处理，但需将bufCursor后移
    uint32_t minNodeNum = DM_MIN2(nodeNumPerElement, node->nodeDesc->nodeNumPerElement);
    for (uint32_t i = 0; i < minNodeNum; i++) {
        ret = NodeDeSerialize(bufCursor, node->currNodes[i], isDelta);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status FixNodeDeSeriElement(DmNodeT *node, uint32_t elementIdx, uint8_t **bufCursor)
{
    // 内部函数保证elementIdx正确
    NodeSetElementIndexWithoutCheck(node, elementIdx);

    uint32_t varintNum;
    uint32_t byteNum;
    // 读recordLen
    uint32_t recordLen;
    DmConvertVarintToUint32(*((uint32_t *)*bufCursor), &recordLen, &byteNum);
    uint8_t *bufTail = *bufCursor + recordLen;
    *bufCursor += byteNum;

    // 读fixedPropLen
    uint32_t fixedPropLen;
    DmConvertVarintToUint32(*((uint32_t *)*bufCursor), &fixedPropLen, &byteNum);
    *bufCursor += byteNum;

    // 处理定长属性
    if (fixedPropLen != 0) {
        if (fixedPropLen == node->currRecord->recordDesc->fixedPropertiesLen) {
            DbFastMemcpy(node->currRecord->runningBuf, fixedPropLen, *bufCursor, fixedPropLen);
        } else {
            // 升降级场景
            FixedPropeDeseriInSchemaAlter(*bufCursor, node->currRecord, fixedPropLen, false);
        }
        *bufCursor += fixedPropLen;
    }

    // 反序列化null info
    bool hasSchemaAlter = false;
    RecordPropeInfoDeSerialize(bufCursor, node->currRecord, true, false, &hasSchemaAlter);
    // 升降级场景下有可能新增变长字段，需要根据recordLen跳转
    *bufCursor = bufTail;

    // 处理nodeNumPerElement数目，该场景下为0
    uint32_t varint = *(uint32_t *)*bufCursor;
    DmConvertVarintToUint32(varint, &varintNum, &byteNum);
    *bufCursor += byteNum;
    DB_ASSERT(varintNum == 0);

    return GMERR_OK;
}

Status NodeParse(uint8_t **bufCursor, DmNodeT *node)
{
    // isCreated
    node->isCreated = *(uint8_t *)*bufCursor;
    *bufCursor += (uint32_t)sizeof(uint8_t);
    // 解析realElementNum
    uint32_t byteNum;
    uint32_t realElementNum;
    uint32_t varint = *(uint32_t *)*bufCursor;
    DmConvertVarintToUint32(varint, &realElementNum, &byteNum);
    *bufCursor += byteNum;
    DmNodeDescT *nodeDesc = node->nodeDesc;
    if (nodeDesc->nodeType == DM_NODE_VECTOR) {
        node->isClear = true;
        ResetVectorNode(node);
    }

    // 若node为vector类型，应先扩容至real大小
    Status ret;
    if (nodeDesc->nodeType == DM_NODE_VECTOR) {
        while (realElementNum > node->totalElementNum) {
            ret = DmNodeVectorAppend(node);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        node->realElementNum = realElementNum;
    }

    // 升降级不涉及新增element
    uint32_t minRealElementNum = DM_MIN2(node->realElementNum, realElementNum);
    for (uint32_t i = 0; i < minRealElementNum; i++) {
        ret = NodeParseElement(node, i, bufCursor);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status NodeParseElement(DmNodeT *node, uint32_t elementIdx, uint8_t **bufCursor)
{
    // 内部函数保证elementIdx正确
    NodeSetElementIndexWithoutCheck(node, elementIdx);
    RecordParse(bufCursor, node->currRecord, true);
    node->nodeSeriBuf[elementIdx] = node->currRecord->recordSeriBuf;
    node->nodeSeriBufLen[elementIdx] = node->currRecord->recordSeriBufLen;

    // 解析nodeNumPerElement
    uint32_t nodeNumPerElement;
    uint32_t byteNum;
    uint32_t varint = *(uint32_t *)*bufCursor;
    DmConvertVarintToUint32(varint, &nodeNumPerElement, &byteNum);
    *bufCursor += byteNum;

    // 反序列化nodes
    // schema升级时需要考虑：
    // 若nodeNum < node->nodeNum：句柄处于Reset状态，需将后面的node默认值去掉（Clear）；句柄处于Clear状态，不用处理
    // 若nodeNum > node->nodeNum：句柄中的record数据不用额外处理，但需将bufCursor后移
    uint32_t minNodeNum = DM_MIN2(nodeNumPerElement, node->nodeDesc->nodeNumPerElement);
    Status ret;
    for (uint32_t i = 0; i < minNodeNum; i++) {
        ret = NodeParse(bufCursor, node->currNodes[i]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status LeafArrayNodeCheckValueIsChangedInReplace(
    DmNodeT *deltaNode, DmNodeT *oldNode, uint32_t propeId, bool *propeIsChanged)
{
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < deltaNode->realElementNum; i++) {
        NodeSetElementIndexWithoutCheck(deltaNode, i);
        NodeSetElementIndexWithoutCheck(oldNode, i);
        ret = RecordGetPropeIsChangedInReplace(deltaNode->currRecord, oldNode->currRecord, propeId, propeIsChanged);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 如果发现某个element上的该属性被set值了，直接返回，增加性能，同时避免propeIsSet被重新赋值
        if (*propeIsChanged) {
            return GMERR_OK;
        }
    }
    return GMERR_OK;
}

Status LeafVectorNodeCheckValueIsChangedInReplace(
    DmNodeT *deltaNode, DmNodeT *oldNode, uint32_t propeId, bool *propeIsChanged)
{
    // 此时为vector replace流程，如果前后element数量不一致直接触发推送
    if (deltaNode->realElementNum != oldNode->realElementNum) {
        *propeIsChanged = true;
        return GMERR_OK;
    } else {
        return LeafArrayNodeCheckValueIsChangedInReplace(deltaNode, oldNode, propeId, propeIsChanged);
    }
}

Status LeafNodeCheckValueIsChangedInReplace(
    DmNodeT *deltaNode, DmNodeT *oldNode, uint32_t propeId, bool *propeIsChanged)
{
    *propeIsChanged = false;
    switch (deltaNode->nodeDesc->nodeType) {
        case DM_NODE_RECORD:
            return RecordGetPropeIsChangedInReplace(
                deltaNode->currRecord, oldNode->currRecord, propeId, propeIsChanged);
        case DM_NODE_ARRAY:
            return LeafArrayNodeCheckValueIsChangedInReplace(deltaNode, oldNode, propeId, propeIsChanged);
        case DM_NODE_VECTOR:
            return LeafVectorNodeCheckValueIsChangedInReplace(deltaNode, oldNode, propeId, propeIsChanged);
        default:
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_NAME, "Check value in replace, Wrong nodeType: %" PRId32 ".",
                (int32_t)deltaNode->nodeDesc->nodeType);
            return GMERR_INVALID_NAME;
    }
}

Status CheckNodePropeIsChangedInReplace(
    const DmIdPathT *idPath, DmNodeT *deltaNode, DmNodeT *oldNode, uint32_t startLevel, bool *propeIsChanged)
{
    DB_POINTER4(idPath, deltaNode, oldNode, propeIsChanged);
    uint32_t depth = idPath->depth;
    if (startLevel == depth - 1) {
        // 此时遍历到叶子节点
        return LeafNodeCheckValueIsChangedInReplace(deltaNode, oldNode, idPath->path[depth - 1], propeIsChanged);
    } else {
        // 循环体中加入对propeIsSet的判断，避免对propeIsSet的二次赋值，同时增加性能
        if (deltaNode->realElementNum != oldNode->realElementNum) {
            *propeIsChanged = true;
            return GMERR_OK;
        }
        for (uint32_t i = 0; i < deltaNode->realElementNum; i++) {
            if (*propeIsChanged) {
                return GMERR_OK;
            }
            NodeSetElementIndexWithoutCheck(deltaNode, i);
            DmNodeT *childNode;
            Status ret = DmNodeGetChildNodeById(deltaNode, idPath->path[startLevel], &childNode);
            if (ret != GMERR_OK) {
                return ret;
            }
            NodeSetElementIndexWithoutCheck(oldNode, i);
            DmNodeT *oldChildNode;
            ret = DmNodeGetChildNodeById(oldNode, idPath->path[startLevel], &oldChildNode);
            if (ret != GMERR_OK) {
                return ret;
            }
            return CheckNodePropeIsChangedInReplace(idPath, childNode, oldChildNode, startLevel + 1, propeIsChanged);
        }
    }
    return GMERR_OK;
}
