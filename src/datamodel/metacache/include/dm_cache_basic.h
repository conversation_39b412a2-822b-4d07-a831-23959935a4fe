/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: yangyi
 * Create: 2023-06-07
 */

#ifndef DM_CACHE_BASIC_H
#define DM_CACHE_BASIC_H

#include "dm_meta_prop_strudefs.h"
#include "dm_meta_prop_label.h"
#include "db_timer.h"

#ifdef __cplusplus
extern "C" {
#endif

#define LABEL_CACHE_MAP_INIT_CAP 0

/**
 *  每种label类型在catalog中均存在一个cataCache
 *  后续逐步将 TagCatalogMgr 中的所有成员用此替代
 */
typedef struct CataCache {
    DbMemCtxT *memctx;
    DbOamapT *nameMap;
    DbOamapT *idMap;
    char *cacheName;
} CataCacheT;

typedef struct TagCatalogMgr {
    uint32_t uuid;
    uint32_t resPoolId;  // res pool id [0, 65535)，映射到uuid的中预留的一段
#ifdef DB_REF_CNT_TRACE
    DbOamapT *parentFunc2IncRefCnt;  // Catalog调用函数 -> 其增加的refCount数
    DbOamapT *parentFunc2DecRefCnt;  // Catalog调用函数 -> 其减少的refCount数
#endif
    DbOamapT *labelId2MqSubs;                       // 根据label id保存对应的message queue型订阅指针链表
    DbOamapT *labelId2SmSubs;                       // 根据label id保存对应的status merge型订阅指针链表
    CataCacheT *metaCache[(uint32_t)CATA_CACHE_N];  // 和CataMetaTypeE元数据类型一一对应的catacache数组.
                                                    // 根据id/name保存对应的元数据指针
    bool isUsingTmpMetaCache;
    CataCacheT *tmpMetaCache[(uint32_t)CATA_CACHE_N];
    DbRWSpinLockT cataLock;  // catalog模块锁，所有元数据的并发依赖该锁控制（模块对外接口内完成加解锁）
    /* Catalog Dynamic Memory Context 说明
     * 用    途: 负责Catalog模块内除了 vertexLabel/edgeLabel/kvLabel 之外的所有元数据的内存的申请和释放
     * 生命周期: 长进程
     * 释放策略：就近释放
     * 释放思路：在构造元数据函数申请内存，如果构造过程中失败，会在构造函数中释放，构造成功后，需要调用释放函数释放
     * 兜底清空措施: DB进程结束
     */
    DbMemCtxT *dynMemCtx;
    /* Catalog Label Dynamic Memory Context 说明
     * 用    途: 负责Catalog模块内 vertexLabel/edgeLabel/kvLabel
     * 延迟申请的动态内存的申请和释放，vertexLabel/edgeLabel/kvLabel 在 创建时使用 Catalog
     * 共享内存，后续内部的指针字段延迟申请内存时使用此动态内存上下文
     * 生命周期: 长进程 释放策略：就近释放
     * 释放思路：用于元数据内部的指针字段延迟申请内存，如果申请过程中失败，会在申请函数中释放，申请成功后，需要在删除元数据时释放
     * 兜底清空措施: DB进程结束
     */
    DbMemCtxT *labelDynMemCtx;
    /* Catalog Share Memory Context 说明
     * 用    途: 负责Catalog模块内共享内存的申请和释放
     * 生命周期: 长进程
     * 释放策略：就近释放
     * 释放思路：在构造元数据函数申请内存，如果构造过程中失败，会在构造函数中释放，构造成功后，需要调用释放函数释放
     * 兜底清空措施: DB进程结束
     */
    DbMemCtxT *shmMemCtx;
    /* Catalog Reusable Share Memory Context 说明
     * 用    途: 负责Catalog模块内所有可重用的共享内存的申请，主要是对账信息、权限信息等，
     * 生命周期: 长进程
     * 释放策略：不释放
     * 释放思路：这些内存一旦申请了就不会释放，只会复用
     * 兜底清空措施: DB进程结束
     */
    DbMemCtxT *reusableShmCtx;
    ReusableMetaMgrT *reusableMetaMgr;        // Catalog 共享内存管理结构
    uint32_t userKvTableNum;                  // 用户创建的Kv表的个数
    uint32_t vertexNormalNum;                 // 用户创建的vertexLabel表的个数
    TagLinkedListT reusableClientRefList;     // CataClientRefT 可复用链表
    DbRWSpinLockT reusableClientRefListLock;  // CataClientRefT 可复用链表锁
    TimerHandleT vlCleanTimer;
    TimerHandleT elCleanTimer;
    TimerHandleT kvCleanTimer;
    uint32_t vlCleanNum;
    uint32_t elCleanNum;
    uint32_t kvCleanNum;
    DbOamapIteratorT vlCleanTter;
    DbOamapIteratorT elCleanTter;
    DbOamapIteratorT kvCleanTter;
    uint32_t vlUndeletedNum;
    uint32_t elUndeletedNum;
    uint32_t kvUndeletedNum;
    DbOamapIteratorT vlRecycleIter;
    DbOamapIteratorT elRecycleIter;
    DbOamapIteratorT kvRecycleIter;
    TimerHandleT vlRecycleTimer;
    TimerHandleT elRecycleTimer;
    TimerHandleT kvRecycleTimer;
#ifdef FEATURE_GQL
    DbOamapT *vid2RelatedEdgePattern;  // 根据vertexlabel id保存依赖该点标签的EdgePattern; item<vId,
                                       // edgePatternInfoList>
    DbOamapT *vid2RelatedPathTrigger;  // 根据vertexlabel id保存依赖该点标签的pathtrigger; item<vId, pathTriggerIdList>
    DbOamapT *vid2PathList;  // 根据vertexId找到所属的所有pathID <DmVertexPathIdsT:key:vertexID - val:pathList>
    DbOamapT *pathId2RelatedPathTrigger;  // 根据path id保存依赖该点标签的pathtrigger; item<pathId, pathTriggerIdList>
    DbOamapT *eid2RelatedPathPattern;  // 根据edgepattern id保存依赖该边模式的ComplexPathInfo结构体指针
    SmoothReconCataParamsT srParams;   // Catalog parameters for Smoothing and Reconciliation
#endif                                 // FEATURE_GQL
} CataCacheMgrT;

CataCacheMgrT *DmGetCataCache(DbInstanceHdT dbInstance);

CataCacheMgrT *DmInitCataCache(DbInstanceHdT dbInstance, DbMemCtxT *sysDynCtx);

CataCacheT *GetMetaCache(CataCacheMgrT *cataCacheMgr, CataMetaTypeE metaType);

/**
 * @brief  将保存元数据的 cache 切换到临时 cache
 * @param dbInstance DB 实例
 * @return Status
 */
SO_EXPORT_FOR_TS Status CataSwitchToTmpCache(DbInstanceHdT dbInstance);

/**
 * @brief  判断VL的元数据是否为空（包括视图和标记删除的表）
 * @param dbInstance DB 实例
 * @return Status
 */
SO_EXPORT_FOR_TS bool CataCheckVLIsEmpty(DbInstanceHdT dbInstance);

/**
 * @brief  将保存元数据的 cache 由临时 cache 切换回正常的 cache
 * @param dbInstance DB 实例
 * @return Status
 */

typedef Status (*CataSwitchFromTmpCacheProc)(void *stmt, DmVertexLabelT *vertexLabel);
SO_EXPORT_FOR_TS Status CataSwitchFromTmpCache(DbInstanceHdT dbInstance, void *stmt, CataSwitchFromTmpCacheProc proc);

/**
 * Catalog manager generate uuid interface
 * @brief generate uuid
 * @param uuid
 * @return status
 */
SO_EXPORT_FOR_TS Status CataGenerateUuid(DbInstanceHdT dbInstance, uint32_t *uuid);

// 初始化 g_gmdbCataCache ，Catalog动态内存管理相关资源
Status InitCataCacheMgr(DbInstanceHdT dbInstance);

// 初始化 g_gmdbCataCache ，Catalog共享内存管理相关资源
Status InitCataShmMgr(DbInstanceHdT dbInstance);

// 删除 g_gmdbCataCache 中的内存memctx，包括动态内存和共享内存
void DeleteCataCacheMgrMemCtx(CataCacheMgrT *cataCacheMgr);

static inline void AppendList2ReusableClientRefList(TagLinkedListT *pidRefList, DbInstanceHdT dbInstance)
{
    CataClientRefT *node = NULL;
    CataClientRefT *iterTmp = NULL;
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->reusableClientRefListLock);
    LIST_FOR_EACH_ENTRY_SAFE(node, iterTmp, pidRefList, linkedNode)
    {
        DbLinkedListRemove(&node->linkedNode);                                        // 从上一个的list上拿下来
        DbLinkedListAppend(&cataCacheMgr->reusableClientRefList, &node->linkedNode);  // 加入reusablelist
    }
    DbRWSpinWUnlock(&cataCacheMgr->reusableClientRefListLock);
}

static inline CataClientRefT *ReusableClientRefListRemoveHead(DbInstanceHdT dbInstance)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->reusableClientRefListLock);
    if (DbLinkedListEmpty(&cataCacheMgr->reusableClientRefList)) {
        DbRWSpinWUnlock(&cataCacheMgr->reusableClientRefListLock);
        return NULL;
    }
    CataClientRefT *reusableNode = LIST_HEAD(&cataCacheMgr->reusableClientRefList, CataClientRefT, linkedNode);
    DbLinkedListRemoveHead(&cataCacheMgr->reusableClientRefList);
    DbRWSpinWUnlock(&cataCacheMgr->reusableClientRefListLock);
    return reusableNode;
}

#define CATA_CLEAN_META_INTERVAL_MS (3 * MSECONDS_IN_SECOND)     // 3s，清理元数据内存的时间间隔
#define CATA_RECYCLE_META_INTERVAL_MS (10 * MSECONDS_IN_SECOND)  // 10s，回收 CataClientRefT 结构体的时间间隔

Status RegisterCatalogTimer(
    void *callBack, char *timerName, uint32_t interval, TimerHandleT *timerHandle, DbInstanceHdT dbInstance);
#ifdef __cplusplus
}
#endif  /* __cplusplus */
#endif  // DM_CACHE_BASIC_H
