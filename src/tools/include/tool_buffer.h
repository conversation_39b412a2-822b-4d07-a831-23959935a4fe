/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: tool_buffer.h
 * Description: Header file for tool buffer, some public interface.
 * Author: yuanxin
 * Create: 2022-11-10
 */

#ifndef TOOL_BUFFER_H
#define TOOL_BUFFER_H

#include "db_mem_context.h"
#include "tool_utils.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

typedef struct OutputBuffer {
    DbMemCtxT *memCtx;     // buffer 的申请节点
    void *buf;             // buf的指针
    uint32_t writeCursor;  // 写指针
    uint32_t capacity;     // 总容量
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    uint32_t binDataBufLen;  // 存储将要存入buffer的总长度，用来判断是否已经超过一定大小需要写入磁盘
    uint32_t countFile;  // 存储已经写了多少个文件
    bool isFinish;       // 是否已经fetch完数据
#endif
} OutputBufferT;

/**
 * 申请的Buf随memCtx一起释放
 */
inline static Status OutPutBufferInit(OutputBufferT *buffer, DbMemCtxT *memCtx, uint32_t initSize)
{
    DB_POINTER2(buffer, memCtx);
    buffer->memCtx = memCtx;
    buffer->buf = DbDynMemCtxAlloc(memCtx, initSize);
    if (buffer->buf == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_OUT_OF_MEMORY, "Alloc memory unsuccessful! ret = %" PRIu32 "",
            (uint32_t)GMERR_OUT_OF_MEMORY);
        return GMERR_OUT_OF_MEMORY;
    }
    buffer->writeCursor = 0;
    buffer->capacity = initSize;
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    buffer->countFile = 0;
    buffer->binDataBufLen = 0;
#endif
    return GMERR_OK;
}

GMDB_EXPORT Status OutPutBufferWrite(OutputBufferT *buffer, const void *data, uint32_t count);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
