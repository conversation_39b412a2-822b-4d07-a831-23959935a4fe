/*
 * Copyright (c) 2025 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef ARKDATA_PRINT_DATA_H
#define ARKDATA_PRINT_DATA_H

#include "arkdata_tool_function.h"
#include "grd_db_api.h"
#include "grd_resultset_api.h"

void PrintData(uint8_t *data, uint32_t dataLen);
void PrintTabledData(GRD_ResultSet *resultSet, bool bPreference);
void PrintPreferencesData(uint8_t *data, uint32_t dataLen);
#endif /* ARKDATA_PRINT_DATA_H */
