/*
 * Copyright (c) 2025 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <cstdio>
#include <cstdint>
#include <cstring>
#include <iostream>
#include <string>
#include <cstdlib>
#include <cinttypes>
#include <sys/stat.h>
#include "arkdata_command.h"
#include "arkdata_tool_function.h"
using namespace std;

typedef struct ArkDataCommandMgr {
    ArkDataCommandT *commands;
    uint32_t numItem;
} ArkDataCommandMgrT;
// limit to use in this file
static ArkDataCommandMgrT g_commandMgr = {0};

// 出于可读性考虑，结构体数组初始化部分成员对齐，且同一个命令不换行
ArkDataCommandT g_commonCmds[] = {
    // 配置函数，命令，帮助信息，类型，值
    {ArkDataCmdUsage, ".help", nullptr, "print help information", ARKDATA_CMD_TYPE_ACTION, 0},
};

// 创建命令管理句柄
int32_t ArkDataCmdCreateMgr()
{
    if (g_commandMgr.commands != nullptr || g_commandMgr.numItem != 0) {
        ARKDATA_LOG_ERROR("Cmd manager has been created.\n");
        return ARKDATA_ERR;
    }

    uint32_t size = sizeof(g_commonCmds);
    ArkDataCommandT *cmds = (ArkDataCommandT *)malloc(size);
    if (cmds == nullptr) {
        ARKDATA_LOG_ERROR("Unable to malloc cmds.\n");
        return ARKDATA_ERR;
    }
    errno_t err = memcpy_s(cmds, size, g_commonCmds, size);
    if (err != EOK) {
        ARKDATA_LOG_ERROR("Unable to copy commands.\n");
        free(cmds);
        return ARKDATA_ERR;
    }

    uint32_t numItem = sizeof(g_commonCmds) / sizeof(g_commonCmds[0]);
    g_commandMgr = (ArkDataCommandMgrT){
        .commands = cmds,
        .numItem = numItem,
    };
    return ARKDATA_OK;
}

// 销毁命令管理句柄
void ArkDataCmdDestroyMgr()
{
    if (g_commandMgr.commands == nullptr) {
        return;
    }

    free(g_commandMgr.commands);
    g_commandMgr.commands = nullptr;
    g_commandMgr.numItem = 0;
}

// 注册命令
int32_t ArkDataCmdRegister(ArkDataCommandT *commands, uint32_t size)
{
    if (g_commandMgr.commands == nullptr || g_commandMgr.numItem == 0) {
        ARKDATA_LOG_ERROR("Cmd manager is null.\n");
        return ARKDATA_ERR;
    }

    uint32_t oldSize = g_commandMgr.numItem * sizeof(ArkDataCommandT);
    uint32_t newSize = oldSize + size;
    ArkDataCommandT *newCmds = (ArkDataCommandT *)malloc(newSize);
    if (newCmds == nullptr) {
        ARKDATA_LOG_ERROR("Unable to malloc commands.\n");
        return ARKDATA_ERR;
    }
    errno_t err = memcpy_s(newCmds, newSize, g_commandMgr.commands, oldSize);
    if (err != EOK) {
        ARKDATA_LOG_ERROR("Unable to copy old commands.\n");
        free(newCmds);
        return ARKDATA_ERR;
    }

    uint32_t oldNumItem = g_commandMgr.numItem;
    uint32_t newNumItem = oldNumItem + size / sizeof(ArkDataCommandT);
    for (uint32_t i = oldNumItem, j = 0; i < newNumItem; i++, j++) {
        newCmds[i] = commands[j];
    }

    free(g_commandMgr.commands);
    g_commandMgr.commands = nullptr;
    g_commandMgr = (ArkDataCommandMgrT){
        .commands = newCmds,
        .numItem = newNumItem,
    };
    return ARKDATA_OK;
}

// 获取命令
ArkDataCommandT *ArkDataCmdGet(std::string inputCmd)
{
    ArkDataCommandT *cmds = g_commandMgr.commands;
    int32_t numItems = g_commandMgr.numItem;
    for (int32_t i = 0; i < numItems; i++) {
        std::string strCmd(cmds[i].longCmd);
        if (strCmd == inputCmd) {
            return &(cmds[i]);
        }
    }
    return nullptr;
}
// for command '.help'
void ArkDataCmdUsage(UserInputArgs *userInputArgs)
{
    ArkDataCommandT *cmds = g_commandMgr.commands;
    int32_t numItems = g_commandMgr.numItem;
    char *cmdStr = (char *)malloc(ARKDATA_MAX_STR_LEN);
    if (cmdStr == nullptr) {
        return;
    }
    for (int32_t i = 0; i < numItems; i++) {
        memset_s(cmdStr, ARKDATA_MAX_STR_LEN, '\0', ARKDATA_MAX_STR_LEN);
        errno_t err = strcat_s(cmdStr, ARKDATA_MAX_STR_LEN, cmds[i].longCmd);
        if (err != 0) {
            ARKDATA_LOG_ERROR("Function strcat_s executed fail.\n");
            return;
        }
        if (cmds[i].args != nullptr) {
            err = strcat_s(cmdStr, ARKDATA_MAX_STR_LEN, " ");
            if (err != 0) {
                ARKDATA_LOG_ERROR("Function strcat_s executed fail.\n");
                return;
            }
            err = strcat_s(cmdStr, ARKDATA_MAX_STR_LEN, cmds[i].args);
            if (err != 0) {
                ARKDATA_LOG_ERROR("Function strcat_s executed fail.\n");
                return;
            }
        }
        printf("%-32s %s\n", cmdStr, cmds[i].description);
    }
    free(cmdStr);
    printf("%-32s %s\n", ".quit", "exit the current client");
}
