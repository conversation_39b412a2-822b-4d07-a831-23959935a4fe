/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: tool_interactive.c
 * Description: Implementing an Interactive Framework
 * Author:chenjunyu
 * Create: 2020-10-26
 */
#include "tool_interactive.h"

#include "adpt_file.h"
#include "adpt_string.h"
#include "db_utils.h"
#include "tool_utils.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define TLS_PREVIOUS_INDEX (-1)
#define TLS_CURRENT_INDEX 0
#define TLS_NEXT_INDEX 1
#define TLS_INPUT_DEFAULT_LEN 3
#define TLS_MAX_PRINT_KEYWORD_COLUMN 10             // 打印keyword的最大列宽
#define TLS_LINE_DOUBLE_LEN (TLS_LINE_MAX_LEN * 2)  // 单行命令最大长度x2
#define TLS_MAX_FUNC_NUM 30                         // 最大映射函数个数

// 交互式框架上下文
typedef struct TlsInteractiveContext {
    TlsFuncMappingT funcMapping[TLS_MAX_FUNC_NUM];           // 映射函数指针
    char keyWord[TLS_KEYWORD_MAX_NUM][TLS_KEYWORD_MAX_LEN];  // 用于tab补全的关键字
    char history[TLS_HISTORY_MAX_NUM][TLS_LINE_MAX_LEN];     // 保存历史信息
    int32_t funcMappingNum;
    int32_t keyWordNum;  // 关键字个数
    int32_t historyNum;  // 历史信息数目
    int32_t scanIdx;     // 当前查看的历史信息索引
    int32_t oldest;      // 最旧的历史输入
    int32_t latest;      // 最新的历史输入
    struct termios orignTermios;
    bool isRawMode;
} TlsInteractiveContextT;

static TlsInteractiveContextT g_gmdbTlsIaCtx = {
    .funcMappingNum = 0,
    .keyWord = {{0}},
    .keyWordNum = 0,
    .history = {{0}},
    .historyNum = 1,
    .scanIdx = 0,
    .oldest = 0,
    .latest = 0,
    .isRawMode = false,
};

typedef enum TlsKeyAction {
    CTRL_C = 3, /* Ctrl-c */
    CTRL_D = 4, /* Ctrl-d */
    BACKSPACE = 8,
    TAB = 9,
    ENTER = 13, /* Enter */
    ESC = 27,   /* Escape */
} TlsKeyActionE;

typedef struct TlsLineState {
    int32_t ifd;    /* Terminal stdin file descriptor. */
    int32_t ofd;    /* Terminal stdout file descriptor. */
    char *buf;      /* Edited line buffer. */
    int32_t bufLen; /* Edited line length. */
    int32_t bufMaxLen;
    const char *prompt; /* Prompt to display. */
    int32_t promptLen;  /* Prompt length. */
    int32_t curPos;     /* Current cursor position. */
    bool isPlainText;   /* Display plaintext. */
} TlsLineStateT;

static void TlsRefreshSingleLine(const TlsLineStateT *lineState)
{
    // 用于保存一行所需要显示的所有信息，因此双倍单行最大输入数，保证空间足够
    char seq[TLS_LINE_DOUBLE_LEN];
    const char *prmpt = (lineState == NULL) ? "" : lineState->prompt;
    const char *buffer = (lineState == NULL) ? "" : lineState->buf;
    int32_t curPos = (lineState == NULL) ? 0 : lineState->curPos;
    int32_t promptLen = (lineState == NULL) ? 0 : lineState->promptLen;
    bool isPlainText = (lineState == NULL) ? true : lineState->isPlainText;
    // "\r\x1b[0K"：光标移动到行首，清空光标后的内容; "\r\x1b[%dC"：光标移动到curPos + promptLen，并显示内容
    int32_t ret = snprintf_s(seq, TLS_LINE_DOUBLE_LEN, TLS_LINE_DOUBLE_LEN - 1, "\r\x1b[0K%s%s\r\x1b[%dC", prmpt,
        isPlainText ? buffer : "", curPos + promptLen);
    if (ret < 0) {
        return;
    }
    int32_t ofd = (lineState == NULL) ? STDOUT_FILENO : lineState->ofd;
    if (DbAdptWrite(ofd, seq, strlen(seq), NULL) != GMERR_OK) {
        (void)DbPrintfDefault("input error!");
    }
}

static void TlsLineInsert(TlsLineStateT *lineState, char c)
{
    DB_POINTER2(lineState, lineState->buf);
    // 保证buf有格子设置末尾标识符，相当于一行最多输入TLS_LINE_MAX_LEN - 1个字符
    if (lineState->bufLen >= lineState->bufMaxLen - 1) {
        return;
    }
    // 若不在末尾，先移动字符
    if (lineState->curPos != lineState->bufLen) {
        char *bufAddr = lineState->buf + lineState->curPos;
        uint32_t moveSize = (uint32_t)(lineState->bufLen - lineState->curPos);
        if (memmove_s(bufAddr + 1, moveSize, bufAddr, moveSize) != EOK) {
            return;
        }
    }
    lineState->buf[lineState->curPos++] = c;
    lineState->buf[++lineState->bufLen] = '\0';
    TlsRefreshSingleLine(lineState);
}

static int32_t TlsEnableRawMode(int32_t fd)
{
    if (!isatty(STDIN_FILENO)) {
        /* ENOTTY: 25  Not a typewriter */
        return TLS_STATUS_RW_ERROR;
    }

    if (tcgetattr(fd, &g_gmdbTlsIaCtx.orignTermios) == TLS_STATUS_RW_ERROR) {
        /* ENOTTY: 25  Not a typewriter */
        return TLS_STATUS_RW_ERROR;
    }

    struct termios raw;
    raw = g_gmdbTlsIaCtx.orignTermios; /* modify the original mode */
    // 控制终端输入方式
    raw.c_cflag |= (CS8);
    raw.c_iflag &= (unsigned short)(~(IXON | ICRNL | ISTRIP | INPCK | BRKINT));
    raw.c_oflag &= (unsigned short)(~OPOST);
    // 控制终端编辑功能
    raw.c_lflag &= (unsigned short)(~(IEXTEN | ICANON | ECHO | ISIG));
    raw.c_cc[VMIN] = 1;
    raw.c_cc[VTIME] = 0; /* 1 byte, no timer */

    if (tcsetattr(fd, TCSAFLUSH, &raw) < 0) {
        /* ENOTTY 25: Not a typewriter */
        return TLS_STATUS_RW_ERROR;
    }
    g_gmdbTlsIaCtx.isRawMode = true;
    return DB_SUCCESS;
}

static void TlsDisableRawMode(int32_t fd)
{
    if (g_gmdbTlsIaCtx.isRawMode && tcsetattr(fd, TCSAFLUSH, &g_gmdbTlsIaCtx.orignTermios) != TLS_STATUS_RW_ERROR) {
        g_gmdbTlsIaCtx.isRawMode = false;
    }
}

// 得到准确的查询历史索引
static void TlsGetScanHistoryIdx(int32_t tag)
{
    switch (tag) {
        case TLS_PREVIOUS_INDEX: {
            if (g_gmdbTlsIaCtx.scanIdx != g_gmdbTlsIaCtx.oldest) {
                g_gmdbTlsIaCtx.scanIdx += tag;
                g_gmdbTlsIaCtx.scanIdx = (g_gmdbTlsIaCtx.scanIdx < 0) ? (g_gmdbTlsIaCtx.scanIdx + TLS_HISTORY_MAX_NUM) :
                                                                        g_gmdbTlsIaCtx.scanIdx;
            }
            break;
        }
        case TLS_NEXT_INDEX: {
            if (g_gmdbTlsIaCtx.scanIdx != g_gmdbTlsIaCtx.latest) {
                g_gmdbTlsIaCtx.scanIdx += tag;
                g_gmdbTlsIaCtx.scanIdx = (g_gmdbTlsIaCtx.scanIdx >= TLS_HISTORY_MAX_NUM) ?
                                             (g_gmdbTlsIaCtx.scanIdx - TLS_HISTORY_MAX_NUM) :
                                             g_gmdbTlsIaCtx.scanIdx;
            }
            break;
        }
        default:
            break;
    }
}

static void TlsQueryHistory(TlsLineStateT *lineState, int32_t tag)
{
    DB_POINTER2(lineState, lineState->buf);
    if (g_gmdbTlsIaCtx.historyNum <= 1) {
        return;
    }
    TlsGetScanHistoryIdx(tag);
    if (strncpy_s(lineState->buf, (uint32_t)(lineState->bufMaxLen - 1), g_gmdbTlsIaCtx.history[g_gmdbTlsIaCtx.scanIdx],
            strlen(g_gmdbTlsIaCtx.history[g_gmdbTlsIaCtx.scanIdx])) != EOK) {
        return;
    }
    lineState->bufLen = (int32_t)strlen(lineState->buf);
    lineState->curPos = lineState->bufLen;
    TlsRefreshSingleLine(lineState);
}

/*
    描述：将输入命令加入历史记录中
    g_history structure
    --------------------
    + 0 +   1st input  +   oldest
    --------------------    |
    + 1 +   2nd input  +    |
    --------------------    V
    + 2 +   3rd input  +   latest
    --------------------   <- insert point
    + 3 +     '\0'     +   <- use for print
    --------------------
    + 4 +     null     +   <- g_historyLen
    --------------------
*/
static void TlsAddHistory(const char *history)
{
    DB_POINTER(history);
    int32_t idx = 0;
    // 空字符串不记录
    if (history[idx] == '\0') {
        return;
    }
    // 与最新一条记录相同的字符串不记录
    int32_t historyTop;
    if (g_gmdbTlsIaCtx.latest < 1) {
        historyTop = (g_gmdbTlsIaCtx.latest - 1) + TLS_HISTORY_MAX_NUM;
    } else {
        historyTop = g_gmdbTlsIaCtx.latest - 1;
    }
    if (g_gmdbTlsIaCtx.historyNum > 1 && DbStrCmp(g_gmdbTlsIaCtx.history[historyTop], history, false) == 0) {
        g_gmdbTlsIaCtx.scanIdx = g_gmdbTlsIaCtx.latest;
        return;
    }
    // 超过历史记录上界
    if (g_gmdbTlsIaCtx.historyNum == TLS_HISTORY_MAX_NUM) {
        g_gmdbTlsIaCtx.history[g_gmdbTlsIaCtx.oldest][idx] = '\0';
        g_gmdbTlsIaCtx.oldest = (g_gmdbTlsIaCtx.oldest + 1) % TLS_HISTORY_MAX_NUM;
        g_gmdbTlsIaCtx.historyNum--;
    }
    // 在latest点插入
    idx = 0;
    while (history[idx] != '\0') {
        g_gmdbTlsIaCtx.history[g_gmdbTlsIaCtx.latest][idx] = history[idx];
        idx++;
    }
    // line结束符
    g_gmdbTlsIaCtx.history[g_gmdbTlsIaCtx.latest][idx] = '\0';
    // 空记录向后推一格
    g_gmdbTlsIaCtx.latest = (g_gmdbTlsIaCtx.latest + 1) % TLS_HISTORY_MAX_NUM;
    g_gmdbTlsIaCtx.history[g_gmdbTlsIaCtx.latest][0] = '\0';
    g_gmdbTlsIaCtx.historyNum++;
    g_gmdbTlsIaCtx.scanIdx = g_gmdbTlsIaCtx.latest;
}

static void TlsEditFinish(const TlsLineStateT *lineState)
{
    DB_POINTER(lineState);
    // 添加历史
    TlsAddHistory(lineState->buf);
    // 清空末尾空格
    DbRightTrim(lineState->buf);
}

static void TlsPrintKeyWord(int32_t left, int32_t right, int32_t column)
{
    (void)DbPrintfDefault("\n");
    TlsRefreshSingleLine(NULL);
    for (int32_t i = left; i <= right; i++) {
        (void)DbPrintfDefault("%s\t\t", g_gmdbTlsIaCtx.keyWord[i]);
        if (i != 0 && column != 0 && i % column == 0) {
            (void)DbPrintfDefault("\n");
            TlsRefreshSingleLine(NULL);
        }
    }
    (void)DbPrintfDefault("\n");
}

static void TlsTabPad(TlsLineStateT *lineState, int32_t kwIdx, int32_t head)
{
    DB_POINTER2(lineState, lineState->buf);
    int32_t kwLen = (int32_t)strlen(g_gmdbTlsIaCtx.keyWord[kwIdx]);
    int32_t insertHead = (int32_t)(head + kwLen + 1);  // 插入位置的头索引
    int32_t moveHead = lineState->curPos;              // 移动的位置
    int32_t offset = insertHead - moveHead;
    if (lineState->bufLen + offset >= lineState->bufMaxLen) {
        return;
    }
    for (int32_t i = lineState->bufLen - 1; i >= moveHead; i--) {
        lineState->buf[i + offset] = lineState->buf[i];
    }
    for (int32_t i = 0; i < kwLen; i++) {
        lineState->buf[head + i] = g_gmdbTlsIaCtx.keyWord[kwIdx][i];
    }
    // 前一个再插入一个空格，分开补全的关键字与后续输入
    lineState->buf[insertHead - 1] = ' ';
    lineState->bufLen += offset;
    lineState->curPos += offset;
    lineState->buf[lineState->bufLen] = '\0';
}

/*
  @param head: 前缀头索引
  @param tail: 前缀尾索引
*/
static void TlsMatchSuffix(TlsLineStateT *lineState, int32_t head, int32_t tail)
{
    DB_POINTER2(lineState, lineState->buf);
    // 无输入，一次性打印
    if (head < 0) {
        TlsPrintKeyWord(0, g_gmdbTlsIaCtx.keyWordNum - 1, TLS_MAX_PRINT_KEYWORD_COLUMN);
        return;
    }
    int32_t left = 0;                               // keyword头指针
    int32_t right = g_gmdbTlsIaCtx.keyWordNum - 1;  // keyword尾指针
    int32_t len = (tail - head) + 1;                // 前缀字符串长
    if (DbStrNCmp(&lineState->buf[head], g_gmdbTlsIaCtx.keyWord[left], (uint32_t)len, false) < 0 ||
        DbStrNCmp(&lineState->buf[head], g_gmdbTlsIaCtx.keyWord[right], (uint32_t)len, false) > 0) {
        return;
    }
    // 二分查找
    int32_t mid;
    while (left <= right) {
        // 2代表取平均值
        mid = (left + right) / 2;
        if (DbStrNCmp(&lineState->buf[head], g_gmdbTlsIaCtx.keyWord[mid], (uint32_t)len, false) > 0) {
            left = mid + 1;
        } else if (DbStrNCmp(&lineState->buf[head], g_gmdbTlsIaCtx.keyWord[mid], (uint32_t)len, false) < 0) {
            right = mid - 1;
        } else {
            break;
        }
    }
    while (DbStrNCmp(&lineState->buf[head], g_gmdbTlsIaCtx.keyWord[left], (uint32_t)len, false) > 0) {
        left++;
    }
    while (DbStrNCmp(&lineState->buf[head], g_gmdbTlsIaCtx.keyWord[right], (uint32_t)len, false) < 0) {
        right--;
    }
    // 无前缀匹配
    if (left > right) {
        return;
    }
    // 匹配多个，全部打印
    if (left < right) {
        TlsPrintKeyWord(left, right, TLS_MAX_PRINT_KEYWORD_COLUMN);
        return;
    }
    // 匹配单个，自动填充
    TlsTabPad(lineState, left, head);
}

/* 自动补全功能 */
static void TlsEditTab(TlsLineStateT *lineState)
{
    DB_POINTER2(lineState, lineState->buf);
    // 刷新行，否则tab键会影响光标
    TlsRefreshSingleLine(lineState);
    // 无关键字，退出
    if (g_gmdbTlsIaCtx.keyWordNum == 0) {
        return;
    }
    // 取光标前的位置
    int32_t tail = lineState->curPos - 1;
    int32_t head = tail;
    while (head > 0 && lineState->buf[head - 1] != ' ') {
        head--;
    }
    TlsMatchSuffix(lineState, head, tail);
    TlsRefreshSingleLine(lineState);
}

// op = -1:退格键,删除光标位置前的字符；op = 0: delete键，删除光标所在字符
static void TlsEditDelete(TlsLineStateT *lineState, int32_t op)
{
    DB_POINTER2(lineState, lineState->buf);
    if (lineState->curPos <= 0 || lineState->bufLen <= 0) {
        return;
    }
    char *bufAddr = lineState->buf + lineState->curPos + op + 1;
    uint32_t dstSize = (uint32_t)(((lineState->bufMaxLen - lineState->curPos) - op) - 1);
    uint32_t moveSize = (uint32_t)(((lineState->bufLen - lineState->curPos) - op) - 1);
    if (memmove_s(bufAddr - 1, dstSize, bufAddr, moveSize) != EOK) {
        return;
    }
    lineState->curPos = lineState->curPos + op;
    lineState->buf[--lineState->bufLen] = '\0';
    TlsRefreshSingleLine(lineState);
}

static void TlsEditMoveLeft(TlsLineStateT *lineState)
{
    DB_POINTER(lineState);
    if (lineState->curPos > 0) {
        lineState->curPos--;
        TlsRefreshSingleLine(lineState);
    }
}

static void TlsEditMoveRight(TlsLineStateT *lineState)
{
    DB_POINTER(lineState);
    if (lineState->curPos != lineState->bufLen) {
        lineState->curPos++;
        TlsRefreshSingleLine(lineState);
    }
}

static void TlsEditMove(TlsLineStateT *lineState)
{
    DB_POINTER(lineState);
    char seq[TLS_INPUT_DEFAULT_LEN];
    if (DbAdptRead(lineState->ifd, seq, 1, NULL) != GMERR_OK ||
        DbAdptRead(lineState->ifd, seq + 1, 1, NULL) != GMERR_OK) {
        return;
    }
    do { /* ESC [ sequences. */
        if (seq[0] != '[') {
            break;
        }
        if (seq[1] >= '0' && seq[1] <= '9') {
            if (DbAdptRead(lineState->ifd, seq + TLS_INPUT_DEFAULT_LEN - 1, 1, NULL) != GMERR_OK) {
                return;
            }
            if (seq[TLS_INPUT_DEFAULT_LEN - 1] == '~' && seq[1] == '3') {
                TlsEditDelete(lineState, TLS_CURRENT_INDEX);
                return;
            }
        } else {
            switch (seq[1]) {
                case 'A': /* Up */
                    TlsQueryHistory(lineState, TLS_PREVIOUS_INDEX);
                    return;
                case 'B': /* Down */
                    TlsQueryHistory(lineState, TLS_NEXT_INDEX);
                    return;
                case 'C': /* Right */
                    TlsEditMoveRight(lineState);
                    return;
                case 'D': /* Left */
                    TlsEditMoveLeft(lineState);
                    return;
                default:
                    return;
            }
        }
    } while (false);
}

static void TlsCharEdit(TlsLineStateT *lineState)
{
    DB_POINTER2(lineState, lineState->buf);
    size_t readCount;
    char c;
    while (true) {
        if (DbAdptRead(lineState->ifd, &c, 1u, &readCount) != GMERR_OK || readCount == 0) {
            return;
        }
        switch (c) {
            case ENTER:
                TlsEditFinish(lineState);
                return;
            case BACKSPACE:
                TlsEditDelete(lineState, TLS_PREVIOUS_INDEX);
                break;
            case TAB:
                TlsEditTab(lineState);
                break;
            case CTRL_C:
                lineState->bufLen = TLS_STATUS_RW_ERROR;
                return;
            case CTRL_D:  // 清空所有编辑
                lineState->buf[0] = '\0';
                lineState->curPos = lineState->bufLen = 0;
                TlsRefreshSingleLine(lineState);
                break;
            case ESC:  // 操作上下左右
                TlsEditMove(lineState);
                break;
            default:  // 字符插入
                TlsLineInsert(lineState, c);
                break;
        }
    }
}

int32_t TlsLineEdit(int32_t ifd, int32_t ofd, const TlsDisplaySetT dspSet, char *buf, int32_t maxLen)
{
    TlsLineStateT lineState = {.ifd = ifd,
        .ofd = ofd,
        .buf = buf,
        .bufLen = 0,
        .bufMaxLen = maxLen,
        .prompt = dspSet.prompt,
        .promptLen = (int32_t)strlen(dspSet.prompt),
        .curPos = 0,
        .isPlainText = dspSet.isPlainText};
    if (DbAdptWrite(lineState.ofd, (void *)dspSet.prompt, strlen(dspSet.prompt), NULL) != GMERR_OK) {
        return TLS_STATUS_RW_ERROR;
    }
    TlsCharEdit(&lineState);
    return lineState.bufLen;
}

static int32_t TlsReadLineRaw(const TlsDisplaySetT disSet, char *buf, int32_t maxLen)
{
    // 启动终端模式， 配置参数
    if (TlsEnableRawMode(STDIN_FILENO) == TLS_STATUS_RW_ERROR) {
        return TLS_STATUS_RW_ERROR;
    }

    int32_t count = TlsLineEdit(STDIN_FILENO, STDOUT_FILENO, disSet, buf, maxLen);
    // 回显正常输出
    TlsDisableRawMode(STDIN_FILENO);
    (void)DbPrintfDefault("\n");
    return count;
}

static int32_t TlsLineNoTTY(char *line, int32_t maxLen)
{
    DB_POINTER(line);
    int32_t len = 0;
    int c;
    while (true) {
        if (len >= maxLen) {
            line[maxLen - 1] = '\0';
            return DB_SUCCESS;
        }
        c = fgetc(stdin);
        if (c == EOF || c == '\n') {
            line[len] = '\0';
            return DB_SUCCESS;
        }
        line[len++] = (char)c;
    }
}

int32_t TlsNextLine(char *prompt, char *line, int32_t maxLen, bool isPlainText)
{
    // 为匹配历史记录长度以及TlsExcuteFunByCmd接口中的命令解析，目前最大长度为TLS_LINE_MAX_LEN
    int32_t len = DB_MAX(maxLen, TLS_LINE_MAX_LEN);
    if (!isatty(STDIN_FILENO)) {
        if (DbAdptWrite(STDOUT_FILENO, (const void *)prompt, strlen(prompt), NULL) != GMERR_OK) {
            return TLS_STATUS_RW_ERROR;
        }
        return TlsLineNoTTY(line, len);
    }
    TlsDisplaySetT disSet = {prompt, isPlainText};
    return (TlsReadLineRaw(disSet, line, len) == TLS_STATUS_RW_ERROR) ? TLS_STATUS_RW_ERROR : DB_SUCCESS;
}

Status TlsExcuteFunByCmd(void *ctx, const char *str, bool *isFinish)
{
    DB_POINTER(str);
    char copyStr[TLS_LINE_MAX_LEN] = {0};
    if (strcpy_s(copyStr, TLS_LINE_MAX_LEN, str) != EOK) {
        return GMERR_FIELD_OVERFLOW;
    }
    char *leftStr = NULL;
    char *opName = strtok_r(copyStr, " ", &leftStr);
    if (opName == NULL) {
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    while (leftStr != NULL && *leftStr == ' ') {
        leftStr++;
    }
    for (int32_t i = 0; i < g_gmdbTlsIaCtx.funcMappingNum; i++) {
        if (DbStrCmp(opName, g_gmdbTlsIaCtx.funcMapping[i].opName, false) == 0) {
            if (g_gmdbTlsIaCtx.funcMapping[i].func == NULL) {
                return GMERR_INVALID_OPTION;
            } else {
                return g_gmdbTlsIaCtx.funcMapping[i].func(ctx, leftStr, isFinish);
            }
        }
    }
    return GMERR_NULL_VALUE_NOT_ALLOWED;
}

Status TlsAddKeyWord(const char *str)
{
    if (str == NULL || strlen(str) == 0) {
        return GMERR_OK;
    }
    if (g_gmdbTlsIaCtx.keyWordNum >= TLS_KEYWORD_MAX_NUM) {
        return GMERR_TOO_MANY_ARGUMENTS;
    }
    int32_t left = 0;
    int32_t right = g_gmdbTlsIaCtx.keyWordNum - 1;
    int32_t mid;
    int32_t insertIdx = 0;
    while (left <= right) {
        // 2代表取平均值
        mid = (left + right) / 2;
        if (DbStrCmp(str, g_gmdbTlsIaCtx.keyWord[mid], false) > 0) {
            left = mid + 1;
            insertIdx = mid + 1;
        } else if (DbStrCmp(str, g_gmdbTlsIaCtx.keyWord[mid], false) < 0) {
            right = mid - 1;
            insertIdx = mid;
        } else {
            return GMERR_OK;
        }
    }
    int32_t idx;
    for (int32_t i = g_gmdbTlsIaCtx.keyWordNum - 1; i >= insertIdx; i--) {
        idx = 0;
        while (g_gmdbTlsIaCtx.keyWord[i][idx] != '\0') {
            g_gmdbTlsIaCtx.keyWord[i + 1][idx] = g_gmdbTlsIaCtx.keyWord[i][idx];
            idx++;
        }
        g_gmdbTlsIaCtx.keyWord[i + 1][idx] = '\0';
    }
    if (strcpy_s(g_gmdbTlsIaCtx.keyWord[insertIdx], TLS_KEYWORD_MAX_LEN, str) != EOK) {
        return GMERR_FIELD_OVERFLOW;
    }
    g_gmdbTlsIaCtx.keyWordNum++;
    return GMERR_OK;
}

Status TlsInitFuncMapping(const TlsFuncMappingT *funcMappingArrs, uint32_t len)
{
    DB_POINTER(funcMappingArrs);
    if (len == 0) {
        return GMERR_OK;
    }
    if (len > TLS_MAX_FUNC_NUM) {
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    for (uint32_t i = 0; i < len; i++) {
        g_gmdbTlsIaCtx.funcMapping[g_gmdbTlsIaCtx.funcMappingNum++] = funcMappingArrs[i];
    }
    // 将opName加入关键字
    for (int32_t i = 0; i < g_gmdbTlsIaCtx.funcMappingNum; i++) {
        Status ret = TlsAddKeyWord(g_gmdbTlsIaCtx.funcMapping[i].opName);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status TlsEnterKeyInteractive(char *userKey, uint32_t *keyLen)
{
    DB_POINTER2(userKey, keyLen);
    int32_t ret = 0;
    char inputKey[TLS_LINE_MAX_LEN] = {0};  // 传入的userKey空间小于TLS_LINE_MAX_LEN时容易踩存
    while ((ret = TlsNextLine((char *)"Enter User Key : ", inputKey, TLS_LINE_MAX_LEN, false)) == DB_SUCCESS) {
        *keyLen = (uint32_t)strlen(inputKey);
        Status status = TlsCheckKeyComplexity(inputKey, *keyLen);
        if (status == GMERR_OK) {
            errno_t err = memcpy_s(userKey, *keyLen, inputKey, *keyLen);
            (void)memset_s(inputKey, TLS_LINE_MAX_LEN, 0, TLS_LINE_MAX_LEN);
            if (SECUREC_UNLIKELY(err != EOK)) {
                return GMERR_MEMORY_OPERATE_FAILED;
            }
            break;
        }
        (void)memset_s(inputKey, TLS_LINE_MAX_LEN, 0, TLS_LINE_MAX_LEN);
        TOOL_RUN_INFO(DbPrintfDefault,
            "The key complexity is insufficient, make sure it's at least 8 characters and including two or "
            "more of following characters: digits, uppercase and lowercase letters, and special characters.");
    }
    return ret == DB_SUCCESS ? GMERR_OK : GMERR_INVALID_PARAMETER_VALUE;
}

Status TlsInteractiveProcess(void *ctx, char *prompt)
{
    bool isFinish = false;
    char line[TLS_LINE_MAX_LEN];
    while (TlsNextLine(prompt, line, TLS_LINE_MAX_LEN, true) == DB_SUCCESS) {
        Status ret = TlsExcuteFunByCmd(ctx, line, &isFinish);
        if (isFinish) {
            return ret;
        }
    }
    return GMERR_OK;
}

void TlsPrintKeywordInfo(void)
{
    (void)DbPrintfDefault("[USAGE]:\n");
    for (int32_t i = 0; i < g_gmdbTlsIaCtx.funcMappingNum; i++) {
        (void)DbPrintfDefault("%s\n", g_gmdbTlsIaCtx.funcMapping[i].opHelp);
    }
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
