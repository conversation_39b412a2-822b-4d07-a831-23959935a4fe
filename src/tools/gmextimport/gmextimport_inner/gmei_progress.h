/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: nlog importer
 * Create: 2025-01-23
 */
#ifndef GMEI_PROGRESS_H
#define GMEI_PROGRESS_H

#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <stdbool.h>
#include <inttypes.h>
#include "adpt_printf.h"
#include "gmc_errno.h"
#include "tools_log.h"
#include "gmei_alloc.h"
#include "gmei_string.h"
#include "gmei_data.h"
#include "gmei_utils.h"
#include "gmei_directory.h"

typedef enum GmeiProgressStatus {
    GMEI_PROGRESS_STATUS_NORMAL = 0,
    GMEI_PROGRESS_STATUS_INIT_ABNORMAL,
    GMEI_PROGRESS_STATUS_PROC_ABNORMAL,
} GmeiProgressStatusE;

void SetStatus(GmeiProgressStatusE value);
GmeiProgressStatusE GetStatus(void);
void IncProcessedDbFileNum(void);
int32_t CreateProgressJsonFile(GmeiStringT fullDir);
int32_t GmeiCountDbFile(GmeiStringT path, const GmeiOptionsT *opts);
int32_t UpdateProgressToFile(const GmeiOptionsT *opts, bool isFinished);
int32_t GmeiGetDbFileNum(const GmeiOptionsT *opts);

#endif
