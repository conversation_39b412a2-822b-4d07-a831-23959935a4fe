/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: nlog importer
 * Create: 2025-04-08
 */
#include "db_json_common.h"
#include "gmei_progress.h"
#include "gmei_schemas.h"

static uint32_t g_dbFileNum = 0;
static uint32_t g_processedDbFileNum = 0;
static uint32_t g_gmeiPid = 0;
static GmeiProgressStatusE g_progressStatus = GMEI_PROGRESS_STATUS_NORMAL;

void SetStatus(GmeiProgressStatusE value)
{
    g_progressStatus = value;
}

GmeiProgressStatusE GetStatus(void)
{
    return g_progressStatus;
}

void IncProcessedDbFileNum(void)
{
    g_processedDbFileNum++;
}

int32_t GmeiCountDbFile(GmeiStringT path, const GmeiOptionsT *opts)
{
    (void)opts;
    if (GmeiIsDbFile(path)) {
        GmeiStringT filename = GmeiFilenameFromPath(path);
        const GmeiTableT *table = GmeiMatchFilenameToTable(filename);
        if (table != NULL) {
            g_dbFileNum++;
        }
    }
    return GMERR_OK;
}

int32_t CreateProgressJsonFile(GmeiStringT fullDir)
{
    const char *dir = fullDir.data;
    if (dir == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Progress json file is empty.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (IsFileExist(dir)) {
        DbJsonT *json = DbJsonLoadsFile(dir, 0);
        if (json == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Load progress json content with problem.");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        DbJsonT *totalFileNum = DbJsonObjectGet(json, "total_file_num");
        DbJsonT *importedFileNum = DbJsonObjectGet(json, "imported_file_num");
        g_dbFileNum = (uint32_t)DbJsonIntegerValue(totalFileNum);
        g_processedDbFileNum = (uint32_t)DbJsonIntegerValue(importedFileNum);
    }
    g_gmeiPid = DbAdptGetpid();
    FILE *fp = NULL;
    fp = fopen(dir, "w");
    if (fp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_FILE_OPERATE_FAILED, "Create progress json file with problem.");
        return GMERR_FILE_OPERATE_FAILED;
    }
    (void)fprintf(fp,
        "{\"total_file_num\": %" PRIu32 ", \"imported_file_num\": %" PRIu32 ", \"pid\": %" PRIu32
        ", \"status\": \"processing\"}",
        g_dbFileNum, g_processedDbFileNum, g_gmeiPid);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t UpdateProgressToFile(const GmeiOptionsT *opts, bool isFinished)
{
    GmeiStringT progressFileDir = GmeiOptionsGet(opts, GmeiStringFromChars("table-process-progress")).value.stringVal;
    const char *dir = progressFileDir.data;
    FILE *fp = NULL;
    fp = fopen(dir, "w");
    if (fp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_FILE_OPERATE_FAILED, "Open or create progress json file with problem.");
        return GMERR_FILE_OPERATE_FAILED;
    }
    char *sta = NULL;
    if (g_progressStatus == GMEI_PROGRESS_STATUS_INIT_ABNORMAL) {
        sta = "done";
    } else if (g_progressStatus == GMEI_PROGRESS_STATUS_PROC_ABNORMAL) {
        sta = "processing";
    } else if (isFinished) {
        sta = "done";
    } else {
        sta = "processing";
    }
    (void)fprintf(fp,
        "{\"total_file_num\": %" PRIu32 ", \"imported_file_num\": %" PRIu32 ", \"pid\": %" PRIu32
        ", \"status\": \"%s\"}",
        g_dbFileNum, g_processedDbFileNum, g_gmeiPid, sta);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t GmeiGetDbFileNum(const GmeiOptionsT *opts)
{
    GmeiStringT directory = GmeiOptionsGet(opts, GmeiStringFromChars("directory")).value.stringVal;
    GmeiStringT file = GmeiOptionsGet(opts, GmeiStringFromChars("file")).value.stringVal;
    int32_t ret = GMERR_OK;
    if (directory.data) {
        ret = GmeiWalkMonitoringDbFolders(directory, opts, GmeiCountDbFile);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (file.data) {
        ret = GmeiCountDbFile(file, opts);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}
