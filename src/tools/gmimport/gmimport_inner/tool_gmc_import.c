/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: tool_gmc_import.c
 * Description: Importing Binary Files on the Client.
 * Author: GMDBV5 team
 * Create: 2025-06-25
 */

#include "clt_check.h"
#include "clt_stmt.h"
#include "gmc.h"
#include "gmimport_file.h"
#include "gmimport_utils.h"
#include "tool_thread_pool.h"

#ifdef FEATURE_RSMEM

#define MAX_SYS_IMPORT_PATH_LAYER 10

typedef struct CltImportArgs {
    char filePath[DB_MAX_PATH];
    const char **tables;
    bool *tableBinFileExists;  // 判断表名对应的bin file是否存在
    uint32_t tableNum;
    uint32_t threadNum;
    uint32_t timeoutMs;
} CltImportArgsT;

typedef struct ImportBinFileInfo {
    char fullFilePath[DB_MAX_PATH];
    char *oriLabelName;  // 原始表表名
    char *nameSpace;     // 命名空间名
    char *labelName;     // 表名
    char *tableIdx;      // 文件序号
} ImportBinFileInfoT;

Status ImportTablesArgsCheck(GmcStmtT *stmt, GmcImportParaT *importPara)
{
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = CltPtrCheckWithErr(importPara);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "importPara");
        return ret;
    }
    ret = CltPtr2CheckWithErr(importPara->importDirPath, importPara->tablesName);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "importDirPath or tablesName");
        return ret;
    }
    ret = GmcGetCfg(stmt, "maxNormalTableNum");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Get maxNormalTableNum config.");  // LCOV_EXCL_LINE
        return ret;
    }
    if (importPara->tableNum == 0 || importPara->tableNum > stmt->cfgValue.curValue.value.uintValue) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Param tableNum out of range. ");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    for (uint32_t i = 0; i < importPara->tableNum; ++i) {
        ret = CltCheckStringValid(importPara->tablesName[i], MAX_TABLE_NAME_LEN, "tableName");
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}

Status CltGetAndCheckImportFilePath(char *path, const char *inputPath, uint32_t maxSize)
{
    DB_POINTER2(path, inputPath);
    Status ret = CltCheckStringValid(inputPath, maxSize, "Import path");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    errno_t err = strcpy_s(path, maxSize, inputPath);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "Cpy path.");  // LCOV_EXCL_LINE
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (path[0] != '/' || strcmp(path, "/") == 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Inv import path.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    // 要考虑最后一个字符是'/'的情况, 去掉
    uint32_t len = strlen(path);
    if (path[len - 1] == '/') {
        path[len - 1] = '\0';
    }
    uint32_t layer = 0;
    for (uint32_t i = 0; i < len; ++i) {
        if (path[i] != '/') {
            continue;
        }
        layer++;
    }
    if (layer > MAX_SYS_IMPORT_PATH_LAYER) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "Inv import path, layer %u.", layer);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (!DbDirExist(path)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DIRECTORY_OPERATE_FAILED, "Import path not exist, %s.", path);
        return GMERR_DIRECTORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static uint32_t GetActualTableNumAndRemoveDupTable(const char **tablesName, uint32_t tableNum, const char **tables)
{
    DB_POINTER2(tablesName, tables);
    uint32_t actualTableNum = 0;
    for (uint32_t i = 0; i < tableNum; i++) {
        bool isDup = false;
        for (uint32_t j = 0; j < actualTableNum; j++) {
            if (strcmp(tablesName[i], tables[j]) == 0) {
                isDup = true;
                DB_LOG_INFO("Import Duplicate Table %s.", tablesName[i]);  // LCOV_EXCL_LINE
                break;
            }
        }
        if (!isDup) {
            tables[actualTableNum] = (const char *)tablesName[i];
            actualTableNum++;
        }
    }
    return actualTableNum;
}

static Status ParseBinFileName(char *fileName, ImportBinFileInfoT *binFileInfo)
{
    char tmpBuffer[DB_MAX_PATH] = {0};
    errno_t err = strcpy_s(tmpBuffer, sizeof(tmpBuffer), fileName);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Cpy file name %s.", fileName);  // LCOV_EXCL_LINE
        return GMERR_FIELD_OVERFLOW;
    }
    char *prefix = DbGetFileNameInfo(tmpBuffer, DB_MAX_PATH, DB_FILENAME_PREFIX);
    if (prefix == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FILE_OPERATE_FAILED, "Get prefix, file: %s.", fileName);  // LCOV_EXCL_LINE
        return GMERR_FILE_OPERATE_FAILED;
    }
    char *nameSpace = NULL;
    char *labelName = NULL;
    char *tableIdx = NULL;
    binFileInfo->oriLabelName = strtok_r(prefix, " ", &nameSpace);
    binFileInfo->nameSpace = strtok_r(nameSpace, " ", &labelName);
    binFileInfo->labelName = strtok_r(labelName, " ", &tableIdx);
    binFileInfo->tableIdx = tableIdx;
    return GMERR_OK;
}

Status ImportTablesSetFullPathStr(char *fullPathStr, uint32_t maxLen, const char *dirPath, const struct dirent *dirName)
{
    int32_t ret;
    if (strlen(dirPath) == 0) {
        ret = snprintf_s(fullPathStr, maxLen, maxLen - 1, "%s", dirName->d_name);
    } else {
        ret = snprintf_s(fullPathStr, maxLen, maxLen - 1, "%s/%s", dirPath, dirName->d_name);
    }
    return ret < 0 ? GMERR_MEMORY_OPERATE_FAILED : GMERR_OK;
}

bool IsImportTableNameExists(char *labelName, CltImportArgsT *importArgs)
{
    for (uint32_t i = 0; i < importArgs->tableNum; i++) {
        if (strcmp(labelName, importArgs->tables[i]) == 0) {
            importArgs->tableBinFileExists[i] = true;
            return true;
        }
    }
    return false;
}

Status GetTableBinFileFromDir(CltImportArgsT *importArgs, DbListT *labelInfoList)
{
    struct dirent *direntName;
    DIR *dir = NULL;
    char *dirPath = importArgs->filePath;
    Status ret = DbAdptOpenDir(dirPath, (void **)&dir);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Open dir %s unsucc.", dirPath);  // LCOV_EXCL_LINE
        return ret;
    }
    while ((direntName = readdir(dir)) != NULL) {
        if (DbStrCmp(direntName->d_name, ".", true) == 0 || DbStrCmp(direntName->d_name, "..", true) == 0) {
            continue;
        }
        if (!TlsCheckFileSuffix(direntName->d_name, "bin_data")) {
            continue;
        }
        ImportBinFileInfoT binFileInfo = {0};
        ret = ImportTablesSetFullPathStr(binFileInfo.fullFilePath, DB_MAX_PATH, dirPath, direntName);
        if (ret != GMERR_OK) {
            DB_LOG_WARN(ret, "Set full path %s unsucc.", direntName->d_name);
            continue;
        }
        if (!DbFileExist(binFileInfo.fullFilePath)) {
            continue;
        }
        ret = ParseBinFileName(direntName->d_name, &binFileInfo);
        if (ret != GMERR_OK) {
            DbAdptCloseDir(dir);  // LCOV_EXCL_LINE
            return ret;
        }
        if (binFileInfo.labelName == NULL || !IsImportTableNameExists(binFileInfo.labelName, importArgs)) {
            continue;
        }
        ret = DbAppendListItem(labelInfoList, &binFileInfo);
        if (ret != GMERR_OK) {
            // LCOV_EXCL_START
            DbAdptCloseDir(dir);
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Append list unsucc, bin file %s.", direntName->d_name);
            // LCOV_EXCL_STOP
            return ret;
        }
    }
    DbAdptCloseDir(dir);
    return GMERR_OK;
}

static Status ImportArgsInit(ImportArgumentT *args, uint32_t threadNum)
{
    DB_POINTER(args);
    errno_t err = strcpy_s(args->domainName, DOMAIN_NAME_MAX_LEN, DB_DEFAULT_DOMAIN_NAME);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_FIELD_OVERFLOW, "Cpy domain name %s.", DB_DEFAULT_DOMAIN_NAME);  // LCOV_EXCL_LINE
        return GMERR_FIELD_OVERFLOW;
    }
    args->threadNum = threadNum;
    args->file.type = FILE_BINDATA;
    args->cmdType = GMIMP_CMD_BINDATA;
    return GMERR_OK;
}

static Status ImportBinFileList(GmcStmtT *stmt, CltImportArgsT *importArgs, DbListT *labelInfoList)
{
    uint64_t startTime = DbGetMsec();
    ImportArgumentT args = {0};
    Status ret = ImportArgsInit(&args, importArgs->threadNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    ImportBinFileResetStat();
    uint32_t binFileCnt = DbListGetItemCnt(labelInfoList);
    for (uint32_t i = 0; i < binFileCnt; i++) {
        ImportBinFileInfoT *binFileInfo = DbListItem(labelInfoList, i);
        errno_t err = strcpy_s(args.file.path, DB_MAX_PATH, binFileInfo->fullFilePath);
        if (err != EOK) {
            // LCOV_EXCL_START
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FIELD_OVERFLOW, "Cpy file name %s.", binFileInfo->fullFilePath);
            // LCOV_EXCL_STOP
            return GMERR_FIELD_OVERFLOW;
        }
        ret = ImportScanBinFileByName(stmt, &args, FILE_BINDATA, startTime, importArgs->timeoutMs);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Import bin file unsucc, file: %s.", binFileInfo->fullFilePath);  // LCOV_EXCL_LINE
            return ret;
        }
    }
    return GMERR_OK;
}

Status CltImportBinDataStart(GmcStmtT *stmt, CltImportArgsT *importArgs)
{
    DbListT labelInfoList;
    DbCreateList(&labelInfoList, sizeof(ImportBinFileInfoT), stmt->memCtx);
    // 遍历目录，获取bin_data文件，把文件信息放在list
    Status ret = GetTableBinFileFromDir(importArgs, &labelInfoList);
    if (ret != GMERR_OK) {
        DbDestroyList(&labelInfoList);
        return ret;
    }
    for (uint32_t i = 0; i < importArgs->tableNum; i++) {
        // 确保表名对应的bin_file存在
        if (!importArgs->tableBinFileExists[i]) {
            DbDestroyList(&labelInfoList);
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_INVALID_PARAMETER_VALUE, "Bin file not exists, table: %s.", importArgs->tables[i]);
            return GMERR_INVALID_PARAMETER_VALUE;
        }
    }
    ret = ImportBinFileList(stmt, importArgs, &labelInfoList);
    DbDestroyList(&labelInfoList);
    return ret;
}
#endif  // FEATURE_RSMEM

Status GmcImportTables(GmcStmtT *stmt, GmcImportParaT *importPara)
{
#ifndef FEATURE_RSMEM
    DB_UNUSED2(stmt, importPara);
    DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Import tables");
    return GMERR_FEATURE_NOT_SUPPORTED;
#else
    // 1.校验入参
    Status ret = ImportTablesArgsCheck(stmt, importPara);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    CltImportArgsT importArgs = {0};
    // 2.校验目录
    ret = CltGetAndCheckImportFilePath(importArgs.filePath, importPara->importDirPath, DB_MAX_PATH);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    uint32_t tablesSize = sizeof(char *) * importPara->tableNum;
    const char **tables = (const char **)DbDynMemCtxAlloc(stmt->memCtx, tablesSize);
    if (tables == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc tables.");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    uint32_t fileExistArrSize = sizeof(bool) * importPara->tableNum;
    bool *tableBinFileExist = (bool *)DbDynMemCtxAlloc(stmt->memCtx, fileExistArrSize);
    if (tableBinFileExist == NULL) {
        DbDynMemCtxFree(stmt->memCtx, tables);
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc bin file array.");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(tables, tablesSize, 0, tablesSize);
    (void)memset_s(tableBinFileExist, fileExistArrSize, 0, fileExistArrSize);
    // 3.输入表名去重，获取表数量
    uint32_t actualTableNum = GetActualTableNumAndRemoveDupTable(importPara->tablesName, importPara->tableNum, tables);
    importArgs.tableNum = actualTableNum;
    importArgs.tables = tables;
    importArgs.tableBinFileExists = tableBinFileExist;
    importArgs.threadNum = importPara->threadNum;
    importArgs.timeoutMs = importPara->timeoutMs;
    // 4.导表
    ret = CltImportBinDataStart(stmt, &importArgs);
    DbDynMemCtxFree(stmt->memCtx, tableBinFileExist);
    DbDynMemCtxFree(stmt->memCtx, tables);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Import bin data.");
        return ret;
    }
    return GMERR_OK;
#endif  // FEATURE_RSMEM
}
