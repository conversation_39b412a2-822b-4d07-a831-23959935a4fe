/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Implementation of drop database
 * Author: hebaisheng
 * Create: 2023-10-30
 */

#include <stdio.h>
#include "adpt_string.h"
#include "gmcmd_utils.h"
#include "gmcmd_log.h"
#include "gmc_graph.h"
#include "gmc_internal.h"
#include "gmc_kv.h"
#include "gmcmd_drop_truncate.h"

void DropAllKvTable(GmCmdContextT *cmdContext);
void DropAllEdgeLabel(GmCmdContextT *cmdContext);
void DropAllVertexLabel(GmCmdContextT *cmdContext);
void DropAllDatalogLabel(GmCmdContextT *cmdContext);
#define BUFFER_MAX_SIZE (MAX_TABLE_NAME_LEN * 2)
#define VIEW_DELIM_PARAM ":"
#define KV_TABLE_PREFIX " LABEL_NAME:"
#define EDGE_LABEL_PREFIX " EDGE_LABEL_NAME:"
#define VERTEX_LABEL_PREFIX " VERTEX_LABEL_NAME:"
#define VERTEX_LABEL_DEGE_NUM " EDGE_LABEL_NUM:"
#define VERTEX_LABEL_TYPE_STR " VERTEX_LABEL_TYPE:"
#define VERTEX_LABEL_TYPE_STR_NORMAL "VERTEX_TYPE_NORMAL"
#define VERTEX_LABEL_TYPE_STR_YANG "VERTEX_TYPE_YANG"
#define DATALOG_LABEL_SO_NAME " SO_NAME:"

// 1、删除 vertexlabel：normal表、yang表、datalog表（系统表、视图表不删）
// 2、删除edgelabel;
// 3、删除kv表（DB创建的全局表不删）；
void GmCmdDropDatabase(GmCmdContextT *cmdContext)
{
// 设备上不能直接删表,保险起见,这里加宏区分
#if (defined HPE || defined RTOSV2 || defined RTOSV2X)
    CMD_SUCCESS("Can not drop database.");
#else
    CMD_INFO("Enter drop database");
    DropAllKvTable(cmdContext);
    DropAllEdgeLabel(cmdContext);
    DropAllVertexLabel(cmdContext);
    DropAllDatalogLabel(cmdContext);
#endif
}

void GetStrByKeyword(char *buffer, char *keyWord, char *labelName, uint32_t buffSize)
{
    char *bufCursor = strstr(buffer, keyWord);
    if (bufCursor != NULL) {
        char *lastWords = NULL;
        char *token = strtok_r(bufCursor, VIEW_DELIM_PARAM, &lastWords);
        if (strlen(lastWords) <= 1 || token == NULL) {
            CMD_INFO("Can not get name!");
            return;
        }
        char *tmpName = TrimInvisibleChar(lastWords);
        size_t len = strlen(tmpName);
        errno_t err = strncpy_s(labelName, buffSize, TrimInvisibleChar(lastWords), len);
        if (err != EOK) {
            CMD_INFO("Can not get name when strncpy!");
            return;
        }
    }
    return;
}

void DropAllKvTable(GmCmdContextT *cmdContext)
{
    char strCmd[BUFFER_MAX_SIZE] = {0};
    (void)memset_s(strCmd, BUFFER_MAX_SIZE, 0x00, BUFFER_MAX_SIZE);
    int32_t snLen = snprintf_s(strCmd, BUFFER_MAX_SIZE, BUFFER_MAX_SIZE - 1, "gmsysview -q V\\$CATA_KV_TABLE_INFO");
    if (snLen < 0) {
        CMD_ERROR("Drop all kv table fail when snprintf.");  // LCOV_EXCL_LINE
        return;
    }
    char buffer[BUFFER_MAX_SIZE] = {0};
    FILE *fp = popen(strCmd, "r");
    if (fp == NULL) {
        CMD_ERROR("Drop all kv label fail when popen cmd!");  // LCOV_EXCL_LINE
        return;
    }
    while (
        memset_s(buffer, BUFFER_MAX_SIZE, 0x00, BUFFER_MAX_SIZE) == EOK && fgets(buffer, BUFFER_MAX_SIZE, fp) != NULL) {
        char labelName[MAX_TABLE_NAME_LEN] = {0};
        GetStrByKeyword(buffer, KV_TABLE_PREFIX, labelName, MAX_TABLE_NAME_LEN);
        if (strlen(labelName) == 0) {
            continue;
        }
        // 过滤掉不能删的表
        if (DbStrCmp(labelName, GLOBAL_KV_TABLE_NAME, false) == 0) {
            continue;
        }
        Status ret = GmcKvDropTable(cmdContext->stmt, labelName);
        if (ret != GMERR_OK) {
            CMD_ERROR("Drop kv label fail, name=%s", labelName);
        }
    }
    (void)pclose(fp);
}

void DropAllEdgeLabel(GmCmdContextT *cmdContext)
{
    char strCmd[BUFFER_MAX_SIZE] = {0};
    (void)memset_s(strCmd, BUFFER_MAX_SIZE, 0x00, BUFFER_MAX_SIZE);
    int32_t snLen = snprintf_s(strCmd, BUFFER_MAX_SIZE, BUFFER_MAX_SIZE - 1, "gmsysview -q V\\$CATA_EDGE_LABEL_INFO");
    if (snLen < 0) {
        CMD_ERROR("Drop all edge label fail when snprintf.");  // LCOV_EXCL_LINE
        return;
    }
    char buffer[BUFFER_MAX_SIZE] = {0};
    FILE *fp = popen(strCmd, "r");
    if (fp == NULL) {
        CMD_ERROR("Drop all edge label fail when popen cmd!");  // LCOV_EXCL_LINE
        return;
    }
    while (
        memset_s(buffer, BUFFER_MAX_SIZE, 0x00, BUFFER_MAX_SIZE) == EOK && fgets(buffer, BUFFER_MAX_SIZE, fp) != NULL) {
        char labelName[MAX_TABLE_NAME_LEN] = {0};
        GetStrByKeyword(buffer, EDGE_LABEL_PREFIX, labelName, MAX_TABLE_NAME_LEN);
        if (strlen(labelName) == 0) {
            continue;
        }
        Status ret = GmcDropEdgeLabel(cmdContext->stmt, labelName);
        if (ret != GMERR_OK) {
            CMD_ERROR("Drop edge label fail, name=%s", labelName);
        }
    }
    (void)pclose(fp);
}

void DropAllVertexLabel(GmCmdContextT *cmdContext)
{
    char strCmd[BUFFER_MAX_SIZE] = {0};
    (void)memset_s(strCmd, BUFFER_MAX_SIZE, 0x00, BUFFER_MAX_SIZE);
    int32_t snLen = snprintf_s(strCmd, BUFFER_MAX_SIZE, BUFFER_MAX_SIZE - 1, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    if (snLen < 0) {
        CMD_ERROR("Drop all vertex label fail when snprintf.");  // LCOV_EXCL_LINE
        return;
    }
    char buffer[BUFFER_MAX_SIZE] = {0};
    char labelName[MAX_TABLE_NAME_LEN] = {0};
    FILE *fp = popen(strCmd, "r");
    if (fp == NULL) {
        CMD_ERROR("Drop all vertex label fail when popen cmd!");  // LCOV_EXCL_LINE
        return;
    }
    while (
        memset_s(buffer, BUFFER_MAX_SIZE, 0x00, BUFFER_MAX_SIZE) == EOK && fgets(buffer, BUFFER_MAX_SIZE, fp) != NULL) {
        if (strlen(labelName) == 0) {
            GetStrByKeyword(buffer, VERTEX_LABEL_PREFIX, labelName, MAX_TABLE_NAME_LEN);
            continue;
        }
        // 如果还有边没删，那么这张表也删不掉，略过这张表
        char edgeNum[MAX_TABLE_NAME_LEN] = {0};
        GetStrByKeyword(buffer, VERTEX_LABEL_DEGE_NUM, edgeNum, MAX_TABLE_NAME_LEN);
        if (strlen(edgeNum) != 0 && DbStrCmp(edgeNum, "0", false) != 0) {
            CMD_INFO("The label:%s has edge in this moment, edgeNum:%s.", labelName, edgeNum);
            (void)memset_s(labelName, MAX_TABLE_NAME_LEN, 0x00, MAX_TABLE_NAME_LEN);
            continue;
        }
        // 获取vertex label的type,只删除type为normal和yang的表
        char labelType[MAX_TABLE_NAME_LEN] = {0};
        GetStrByKeyword(buffer, VERTEX_LABEL_TYPE_STR, labelType, MAX_TABLE_NAME_LEN);
        if (strlen(labelType) == 0) {
            continue;
        }
        if ((DbStrCmp(labelType, VERTEX_LABEL_TYPE_STR_NORMAL, false) != 0) &&
            (DbStrCmp(labelType, VERTEX_LABEL_TYPE_STR_YANG, false) != 0)) {
            (void)memset_s(labelName, MAX_TABLE_NAME_LEN, 0x00, MAX_TABLE_NAME_LEN);
            continue;
        }
        Status ret = GmcDropVertexLabel(cmdContext->stmt, labelName);
        if (ret != GMERR_OK) {
            CMD_ERROR("Drop vertex label fail, name=%s", labelName);
        }
        (void)memset_s(labelName, MAX_TABLE_NAME_LEN, 0x00, MAX_TABLE_NAME_LEN);
    }
    (void)pclose(fp);
}

void DropAllDatalogLabel(GmCmdContextT *cmdContext)
{
    DB_UNUSED(cmdContext);
    char strCmd[BUFFER_MAX_SIZE] = {0};
    (void)memset_s(strCmd, BUFFER_MAX_SIZE, 0x00, BUFFER_MAX_SIZE);
    int32_t snLen = snprintf_s(strCmd, BUFFER_MAX_SIZE, BUFFER_MAX_SIZE - 1, "gmsysview -q V\\$PTL_DATALOG_SO_INFO");
    if (snLen < 0) {
        CMD_ERROR("Drop all datalog label fail when snprintf.");  // LCOV_EXCL_LINE
        return;
    }
    char buffer[BUFFER_MAX_SIZE] = {0};
    FILE *fp = popen(strCmd, "r");
    if (fp == NULL) {
        CMD_ERROR("Drop all datalog label fail when popen cmd!");  // LCOV_EXCL_LINE
        return;
    }
    while (
        memset_s(buffer, BUFFER_MAX_SIZE, 0x00, BUFFER_MAX_SIZE) == EOK && fgets(buffer, BUFFER_MAX_SIZE, fp) != NULL) {
        char soName[MAX_TABLE_NAME_LEN] = {0};
        GetStrByKeyword(buffer, DATALOG_LABEL_SO_NAME, soName, MAX_TABLE_NAME_LEN);
        if (strlen(soName) == 0) {
            continue;
        }
        char cmdStr[BUFFER_MAX_SIZE] = {0};
        int cmdLen = snprintf_truncated_s(cmdStr, BUFFER_MAX_SIZE, "gmimport -c datalog -d %s", soName);
        if (cmdLen < 0) {
            CMD_ERROR("Drop datalog label fail, soName=%s", soName);  // LCOV_EXCL_LINE
            continue;
        }
        int ret = system(cmdStr);
        if (ret != GMERR_OK) {
            CMD_ERROR("Drop datalog label fail when call system, soName=%s", soName);
        }
    }
    (void)pclose(fp);
}
